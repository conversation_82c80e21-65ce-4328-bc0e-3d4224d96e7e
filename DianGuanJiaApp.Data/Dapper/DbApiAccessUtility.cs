using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;

namespace DianGuanJiaApp.Data.Dapper
{
    public class DbApiAccessUtility
    {
        private ApiDbConfigModel _dbConfig = new ApiDbConfigModel();
        /// <summary>
        /// 请求来自的云平台
        /// </summary>
        private string FromCloundPt { get; set; } = CustomerConfig.CloudPlatformType;

        /// <summary>
        /// 是否需要同步到其他配置库
        /// </summary>
        private bool NeedSync { get; set; }

        /// <summary>
        /// 带Id的sql，用户在阿里云api站点同步到其他配置
        /// </summary>
        public string SqlWithId { get; set; }

        /// <summary>
        /// 是否是异步执行
        /// </summary>
        public bool IsAsync { get; set; }


        /// <summary>
        /// 获取配置库，仅提供给非阿里云上的服务使用
        /// </summary>
        /// <returns></returns>
        public static DbApiAccessUtility GetConfigureDb(bool needSync = false)
        {
            var pt = Data.Enum.PlatformType.Alibaba.ToString();
            var model = new ApiDbConfigModel();
            model.PlatformType = pt;
            model.DbNameConfigId = 10000;
            model.Location = pt;
            return new DbApiAccessUtility(model, needSync);
        }

        /// <summary>
        /// 获取配置库，仅提供给非阿里云上的服务使用
        /// </summary>
        /// <returns></returns>
        public static DbApiAccessUtility GetPddConfigureDb()
        {
            var pt = Data.Enum.PlatformType.Pinduoduo.ToString();
            var model = new ApiDbConfigModel();
            model.PlatformType = pt;
            model.DbNameConfigId = 11000;
            model.Location = pt;
            return new DbApiAccessUtility(model, false);
        }

        /// <summary>
        /// 获取配置库，仅提供给非阿里云上的服务使用
        /// </summary>
        /// <returns></returns>
        public static DbApiAccessUtility GetTouTiaoConfigureDb(bool isFromApiSite = false)
        {
            var pt = Data.Enum.PlatformType.TouTiao.ToString();
            var model = new ApiDbConfigModel();
            model.PlatformType = pt;
            model.DbNameConfigId = 12000; //TODO:待抖店云配置库 DbNameConfig 表配置好之后，需要修改这个id为真实id
            model.Location = pt;
            return new DbApiAccessUtility(model, false);
        }

        /// <summary>
        /// 拼多多订单分发数据库
        /// </summary>
        /// <returns></returns>
        public static DbApiAccessUtility GetPddFenDanDb()
        {
            var pt = Data.Enum.PlatformType.Pinduoduo.ToString();
            var model = new ApiDbConfigModel();
            model.PlatformType = pt;
            model.DbNameConfigId = 11050;
            model.Location = pt;
            return new DbApiAccessUtility(model);
        }

        /// <summary>
        /// 拼多多订单分发数据库
        /// </summary>
        /// <returns></returns>
        public static DbApiAccessUtility GetAlibabaFenDanDb()
        {
            var pt = Data.Enum.PlatformType.Alibaba.ToString();
            var model = new ApiDbConfigModel();
            model.PlatformType = pt;
            model.DbNameConfigId = 11057;
            model.Location = pt;
            return new DbApiAccessUtility(model);
        }

        /// <summary>
        /// 获取接口数据
        /// </summary>
        /// <returns></returns>
        public static DbApiAccessUtility GetApiDb(int dbNameConfigId,string location)
        {
            var model = new ApiDbConfigModel();
            model.PlatformType = location;
            model.DbNameConfigId = dbNameConfigId;
            model.Location = location;
            return new DbApiAccessUtility(model, false);
        }

        /// <summary>
        /// 判断是否能异步执行
        /// </summary>
        /// <returns></returns>
        public static void ExcuteActionToOtherCloundPt(string currentCloundPt, string tableName, string cationType, Action<bool> action)
        {
            //TODO: 是否异步，这块逻辑还需细化，后续可以考虑走redis配置哪些动作可以走异步
            //[{"TB":"","OP":"insert,update","CP":"Alibaba,Pinduoduo,Toutiao"}]，默认为空[],表示没有走异步的表和操作
            //暂时先全部返回false
            var isAsyncExcute = false; //不异步
            if (isAsyncExcute)
            {
                //异步执行
                ThreadPool.QueueUserWorkItem(s =>
                {
                    try
                    {
                        action(isAsyncExcute);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"同步到其他配置库报错：{ex}");
                    }
                });
            }
            else
            {
                //同步执行
                action(isAsyncExcute);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="platformType">平台类型</param>
        [Obsolete("该方法已弃用，请使用DianGuanJiaApp.Data.Dapper.DbAccessUtility代替")]
        public DbApiAccessUtility(PlatformType platformType)
        {
            _dbConfig.PlatformType = platformType.ToString();
        }
        public DbApiAccessUtility(string platformType)
        {
            _dbConfig.PlatformType = platformType.ToString();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        [Obsolete("该方法已弃用，请使用DianGuanJiaApp.Data.Dapper.DbAccessUtility代替")]
        public DbApiAccessUtility(ApiDbConfigModel model, bool needSync = false)
        {
            _dbConfig.PlatformType = model.PlatformType;
            _dbConfig.DbNameConfigId = model.DbNameConfigId;
            _dbConfig.Location = model.Location;
            _dbConfig.ApplicationName = model.ApplicationName;
            NeedSync = needSync;
        }

        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="platformType">平台类型</param>
        ///// <param name="dbNameConfigId">分库分表需提供数据库配置ID</param>
        //public DbApiAccessUtility(PlatformType platformType, int dbNameConfigId)
        //{
        //    _dbConfig.PlatformType = platformType.ToString();
        //    _dbConfig.DbNameConfigId = dbNameConfigId;
        //}

        public string ExecuteScalar(string sql, dynamic paras = null, ApiSqlParamModel apiParams = null, bool isBaseProduct = false)
        {
            var request = new DbApiRequestModel()
            {
                DbConfig = _dbConfig,
                Sql = sql,
                ApiSqlParams = apiParams == null ? new ApiSqlParamModel(paras) : apiParams,
                CommondType = DbApiRequestCommondType.ExecuteScalar,
                IsBaseProduct = isBaseProduct,
            };
            var rsp = ExecuteDbApiRequest(request);
            if (rsp.IsError)
                throw new Exception(rsp.ErrorMessage);
            else
                return rsp.Result;
        }

        public string ExecuteNonQuery(string sql, dynamic paras = null, ApiSqlParamModel apiParams = null, bool isBaseProduct = false)
        {
            var request = new DbApiRequestModel()
            {
                DbConfig = _dbConfig,
                Sql = sql,
                ApiSqlParams = apiParams == null ? new ApiSqlParamModel(paras) : apiParams,
                CommondType = DbApiRequestCommondType.ExecuteNonQuery,
                IsBaseProduct = isBaseProduct,
            };
            var rsp = ExecuteDbApiRequest(request);
            if (rsp.IsError)
                throw new Exception(rsp.ErrorMessage);
            else
                return rsp.Result;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T">查询的数据类型</typeparam>
        /// <param name="sql">查询语句</param>
        /// <param name="para">参数</param>
        /// <param name="isBaseProduct">是否查基础商品库，默认false</param>
        /// <returns>返回列表</returns>
        public List<T> Query<T>(string sql, dynamic paras = null, ApiSqlParamModel apiParams = null, bool isBaseProduct = false)
        {
            var typeName = typeof(T).FullName;
            //if (typeName.StartsWith("DianGuanJiaApp.Data") == false)
            //    throw new ArgumentException("类型T必须存在于当前命名空间:DianGuanJiaApp.Data");
            var request = new DbApiRequestModel()
            {
                DbConfig = _dbConfig,
                Sql = sql,
                ApiSqlParams = apiParams == null ? new ApiSqlParamModel(paras) : apiParams,
                CommondType = DbApiRequestCommondType.Query,
                ExpectReturnTypeName = typeName,
                IsBaseProduct = isBaseProduct,
            };
            var rsp = ExecuteDbApiRequest(request);
            if (rsp.IsError)
                throw new Exception(rsp.ErrorMessage);
            else
                return rsp.Result?.ToObject<List<T>>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sql">查询语句</param>
        /// <param name="para">参数</param>
        /// <param name="isBaseProduct">是否查基础商品库，默认false</param>
        /// <returns>返回JSON数组</returns>
        public string Query(string sql, dynamic paras = null, ApiSqlParamModel apiParams = null, bool isBaseProduct = false)
        {
            var request = new DbApiRequestModel()
            {
                DbConfig = _dbConfig,
                Sql = sql,
                ApiSqlParams = apiParams == null ? new ApiSqlParamModel(paras) : apiParams,
                CommondType = DbApiRequestCommondType.Query,
                IsBaseProduct = isBaseProduct,
            };
            var rsp = ExecuteDbApiRequest(request);
            if (rsp.IsError)
                throw new Exception(rsp.ErrorMessage);
            else
                return rsp.Result;
        }

        private static ConcurrentDictionary<string, string> _dbapiRequestTypes = new ConcurrentDictionary<string, string>();

        private DbApiResponseModel ExecuteDbApiRequest(DbApiRequestModel request)
        {
            request.FromCloundPt = FromCloundPt; //请求发起的云平台
            request.NeedSync = NeedSync;  //是否需要同步到其他配置库（例如：多多云发起的配置库数据新增或者修改，到阿里云api站点后，需要同步到抖店云）
            request.SqlWithId = SqlWithId; //带id的sql

            var str = Repository.OuterDbAcessRepository.Encrypt(request);
            //请求API
            //根据平台类型获取对应的请求链接
            var apiUrl = CustomerConfig.GetDbApiUrl(request.DbConfig.PlatformType.ToString2(), request.DbConfig.Location.ToString2());
            DbApiResponseModel rsp = null;
            var apiResultJson = string.Empty;
            Exception exception = null;
            var reqElapsedTime = 0; //请求耗时
            var sw = new Stopwatch();
            sw.Start(); //统计 接口请求响应耗时
            try
            {
                //如果是访问配置库，则切换为配置库api的站点
                if (request?.DbConfig?.DbNameConfigId == 10000)
                    apiUrl = CustomerConfig.ConfigApiUrl;
                //var json = DianGuanJiaApp.Utility.Net.HttpMethods.HttpPost(apiUrl, $"request={str}");
                bool request5sTimeout = false; //请求是否5秒超时
                var configDbNameIds = new List<int>() { 10000, 11000, 12000 };
                var dbNameId = request?.DbConfig?.DbNameConfigId ?? 0;
                if (configDbNameIds.Contains(dbNameId))
                    request5sTimeout = true; //配置库操作，超时时间改为5秒
                //apiResultJson = DianGuanJiaApp.Utility.Net.HttpMethods.HttpClientPost(apiUrl, str, isRequest5sTimeOut: request5sTimeout);
                Log.Debug(() => $"跨域请求数据库，{apiUrl}", $"HttpPostByDbApi_{DateTime.Now:yyyy-MM-dd}.log");
                if (request5sTimeout)
                    apiResultJson = DianGuanJiaApp.Utility.Net.HttpMethods.HttpPostByDbApi(apiUrl, str, 5 * 1000);
                else
                    apiResultJson = DianGuanJiaApp.Utility.Net.HttpMethods.HttpPostByDbApi(apiUrl, str);
                sw.Stop();
                rsp = Repository.OuterDbAcessRepository.Decrypt<DbApiResponseModel>(apiResultJson);
                //Log.Debug($"请求数据库API，请求链接：{apiUrl}，Request:{str} Response:{rsp?.ToJson()}");
                if (rsp.IsError)
                    rsp.ErrorMessage += $" = = RequstJson={request.ToJson()}, ApiUrl={apiUrl}";
                Log.Debug(() => $"跨域请求数据库，{apiUrl}，响应信息：{rsp.ToJson(true)}",
                    $"HttpPostByDbApi_{DateTime.Now:yyyy-MM-dd}.log");
                return rsp;
            }
            catch (Exception ex)
            {
                if (sw.IsRunning)
                    sw.Stop();
                Log.WriteError($"请求ApiUrl：{apiUrl}，\n 请求参数：{request.ToJson()}，返回结果：{apiResultJson}，\n异常信息：{ex}", $"DbApiServiceLog-{DateTime.Now.ToString("yyyy-MM-dd")}.txt");
                exception = ex;
                throw ex;
            }
            finally
            {
                reqElapsedTime = sw.ElapsedMilliseconds > int.MaxValue ? -1 : (int)sw.ElapsedMilliseconds;
                if (rsp == null || rsp.IsError == true || exception != null)
                {
                    //记录操作失败的日志
                    LogToLocalConfigDb(apiUrl, request, rsp, apiResultJson, exception);
                }

                //记录每个请求的日志
                TryWriteDbApiLog(apiUrl, request, rsp, apiResultJson, exception, reqElapsedTime);
            }
            if (rsp?.IsError == true)
                rsp.ErrorMessage += $" = = RequstJson={request?.ToJson()}, ApiUrl={apiUrl}";
            return rsp;
        }

        private void TryWriteDbApiLog(string apiUrl, DbApiRequestModel request, DbApiResponseModel response, string apiRstJson, Exception ex, int reqElapsedTime)
        {
            object addModel = null;
            try
            {
                //先判断是否记录请求日志
                var commonSettingRepo = new CommonSettingRepository();
                var isAllRequestLog = commonSettingRepo.Get("EnableConfigDbSyncAllRequestLog", 0)?.Value == "1";
                if (!isAllRequestLog)
                    return;

                //var sb = new StringBuilder();
                //sb.AppendLine($"{apiUrl}-{request.Sql}-{request.CommondType}-{request.ExpectReturnTypeName}");
                //sb.AppendLine($"-{request.ApiSqlParams?.Name}");
                //if (request.ApiSqlParams != null && request.ApiSqlParams.List != null && request.ApiSqlParams.List.Any())
                //    sb.AppendLine(string.Join("-", request.ApiSqlParams.List.Select(x => x.Name).OrderBy(f => f)));
                var md5 = "";// sb.ToString().ToShortMd5();
                             //if (_dbapiRequestTypes.ContainsKey(md5) == false)
                             //{
                             //    _dbapiRequestTypes.TryAdd(md5, null);

                //取出错误日志
                var errorMsg = string.Empty;
                if (ex != null)
                    errorMsg = ex.Message;
                else if (response == null)
                    errorMsg = apiRstJson;
                else if (response.IsError)
                    errorMsg = response.ErrorMessage;

                //存入到数据库日志
                var configDb = DbUtility.GetConfigureConnection();
                addModel = new
                {
                    HashCode = md5,
                    Url = apiUrl?.ToCutString(512),
                    SQL = request.Sql,
                    SQLParams = request.ApiSqlParams?.ToJson() ?? "",
                    CommondType = request.CommondType.ToString()?.ToCutString(32),
                    ExpectReturnTypeName = request.ExpectReturnTypeName?.ToCutString(64) ?? "",
                    ExceptionMessage = errorMsg?.ToCutString(512),
                    IsAsync = IsAsync,
                    ElapsedTime = reqElapsedTime,
                    ServerIP = DianGuanJiaApp.Utility.Net.HttpUtility.GetServerIP()?.ToCutString(32),
                    RequestForm = AppDomain.CurrentDomain.BaseDirectory?.ToCutString(512)
                };
                configDb.Execute("INSERT INTO dbo.DbApiRequestLog(HashCode,Url,SQL,SQLParams,CommondType,ExpectReturnTypeName,ExceptionMessage,IsAsync,ElapsedTime,ServerIP,RequestForm)VALUES(@HashCode,@Url, @SQL, @SQLParams, @CommondType,@ExpectReturnTypeName,@ExceptionMessage,@IsAsync,@ElapsedTime,@ServerIP,@RequestForm)"
                , addModel);
                //}
            }
            catch (Exception e)
            {
                Log.WriteError($"记录数据库接口访问日志数据时发生错误：{e}，数据：{addModel?.ToJson()}", "记录DbApiRequestLog发生错误.txt");
            }
        }

        /// <summary>
        /// 记录数据库访问日志到本地库中
        /// </summary>
        /// <param name="apiUrl"></param>
        /// <param name="request"></param>
        /// <param name="response"></param>
        private void LogToLocalConfigDb(string apiUrl, DbApiRequestModel request, DbApiResponseModel response, string apiRstJson, Exception ex)
        {
            ThreadPool.QueueUserWorkItem(state =>
            {
                DbApiRequestLog log = null;
                try
                {
                    var remark = "执行失败";
                    if (ex != null)
                        remark = ex.ToString();

                    log = new DbApiRequestLog
                    {
                        ApiUrl = apiUrl,
                        RequestJson = request.ToJson(),
                        ResponseJson = response?.ToJson() ?? apiRstJson,
                        CreateTime = DateTime.Now,
                        Remark = remark,
                        ServerIP = DianGuanJiaApp.Utility.Net.HttpUtility.GetServerIP(),
                        RequestForm = AppDomain.CurrentDomain.BaseDirectory
                    };
                    var db = DbUtility.GetConfigureConnection();
                    db.Insert(log);
                }
                catch (Exception e)
                {
                    Log.WriteError($"记录数据库请求日志时发生错误：{e}，数据：{log.ToJson()}", "记录P_DbApiRequestLog发生错误.txt");
                }
            });
        }

        public static DbApiAccessUtility GetConfigureDbApiAccessUtility(string targetCloudPt)
        {
            if (targetCloudPt == PlatformType.Pinduoduo.ToString())
                return GetPddConfigureDb();
            else if (targetCloudPt == PlatformType.TouTiao.ToString())
                return GetTouTiaoConfigureDb();
            else
                return GetConfigureDb();
        }

        /// <summary>
        /// 获取所有配置库
        /// </summary>
        /// <param name="excludePlatfomType">排除指定平台的配置库</param>
        /// <returns>所有配置库</returns>
        public static IEnumerable<DbApiAccessUtility> GetAllConfigureDb(string excludePlatfomType = null)
        {
            if (excludePlatfomType != PlatformType.Alibaba.ToString() && excludePlatfomType != PlatformType.Jingdong.ToString())
            {
                yield return GetConfigureDb();
            }
            if (excludePlatfomType != PlatformType.Pinduoduo.ToString())
            {
                yield return GetPddConfigureDb();
            }
            if (excludePlatfomType != PlatformType.TouTiao.ToString())
            {
                yield return GetTouTiaoConfigureDb();
            }
        }
    }
}
