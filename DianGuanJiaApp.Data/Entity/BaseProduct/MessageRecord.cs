using System;
using Dapper;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Data.Entity.BaseProduct
{
    /// <summary>
    /// 消息记录表
    /// </summary>
    [Table("MessageRecord")]
    public class MessageRecord
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// 消息类型
        /// </summary>
        public string MsgType { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 当前商品源商家在哪个分区，用于消息消费初始化SiteContext
        /// 目前只有源商家在旧库时有意义
        /// </summary>
        public string DbName { get; set; }
        /// <summary>
        /// 商品所属平台类型
        /// </summary>
        public string ProductPlatformType { get; set; }
        /// <summary>
        /// 业务Id
        /// </summary>
        public string BusinessId { get; set; }
        /// <summary>
        /// 详细内容
        /// </summary>
        public string DataJson { get; set; }

        /// <summary>
        /// 源云平台
        /// </summary>
        public string SourceCloud { get; set; }

        /// <summary>
        /// 目标云平台（目前有四种：Alibaba、TouTiao、Pinduoduo、Jingdong）
        /// </summary>
        public string TargetCloud { get; set; }

        /// <summary>
        /// 目标URL，可为空
        /// </summary>
        public string TargetUrl { get; set; }

        /// <summary>
        /// 状态，默认0待处理，1已发送
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 重试次数，默认0
        /// </summary>
        public int RetryTime { get; set; }

        /// <summary>
        /// 下次执行时间，默认当前时间
        /// </summary>
        public DateTime NextExeTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public int CreateFxUserId { get; set; }

        /// <summary>
        /// 创建时间，默认当前时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间，默认当前时间，自动更新
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 处理失败时消息需要写入的队列名称
        /// 默认为空即可，在消费时，会将处理失败的消息写入延迟队列，
        /// 以达到延迟重试的效果
        /// </summary>
        [NotMapped]
        public int FailToQueue { get; set; }

        /// <summary>
        /// 根据业务平台，返回目标云平台
        /// </summary>
        [NotMapped]
        public string GetTargetCloud
        {
            get
            {
                if (ProductPlatformType.IsNullOrEmpty())
                    return CloudPlatformType.Alibaba.ToString();
                if (CustomerConfig.FxDouDianCloudPlatformTypes.Contains(ProductPlatformType))
                    return CloudPlatformType.TouTiao.ToString();
                if (CustomerConfig.FxJingDongCloudPlatformTypes.Contains(ProductPlatformType))
                    return CloudPlatformType.Jingdong.ToString();
                if (CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(ProductPlatformType))
                    return CloudPlatformType.Pinduoduo.ToString();
                return CloudPlatformType.Alibaba.ToString();
            }
        }
    }
}
