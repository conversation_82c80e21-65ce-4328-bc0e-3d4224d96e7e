using System;
using Dapper;
using DianGuanJiaApp.Utility.Extension;
using System.Linq;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;

namespace DianGuanJiaApp.Data.Entity
{
    /// <summary>
    /// 逻辑表
    /// </summary>
    [Table("LogicOrder")]
    public partial class LogicOrder
    {
        /// <summary>
        ///  
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public string ErpRefundState { get; set; }

        /// <summary>
        ///  异常单异常状态：1：可申请退款，999：申请退款待确认中，-2：拒绝退款（可再次申请退款），-1：退单成功
        /// </summary>
        public int ExceptionStatus { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public string ExceptionReason { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public int PrintState { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public int SendState { get; set; }

        /// <summary>
        /// 线上单审核状态（1：待审核，2：审核中，3：已审核，4：已取消，5：已拒绝）
        /// 线下单审核状态（11：待审核，12：审核中，13：已审核，14：已取消，15：已拒绝）
        /// </summary>
        public int ApprovalStatus { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public DateTime? ExpressPrintTime { get; set; }
        /// <summary>
        /// 打印次数
        /// </summary>
        public int ExpressPrintTimes { get; set; }
        /// <summary>
        /// 打印序列号
        /// </summary>
        public string PrintedSerialNumber { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public int LastExpressPrintTemplateId { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public string LastWaybillCode { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public DateTime? SendPrintTime { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public DateTime? NahuoPrintTime { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public string PlatformOrderId { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public DateTime? OnlineSendTime { get; set; }

        /// <summary>
        /// 跨境发货的交运时间
        /// </summary>
        public DateTime? ShipmentDate { get; set; }

        /// <summary>
        /// 最迟发货时间
        /// </summary>
        public DateTime? LastShipTime { get; set; }

        /// <summary>
        ///  逻辑单备注
        /// </summary>
        public string SystemRemark { get; set; }

        /// <summary>
        ///  打单备注
        /// </summary>
        public string PrintRemark { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public int WarehouseId { get; set; }

        /// <summary>
        ///合并类型
        ///0：正常单（自动拆出的单或原始订单）
        ///1：被自动合并订单（即：子单）
        ///2：被手工合并的子订单
        ///3：自动合并的主订单（即：主单）
        ///4：手工合并的主订单（即：主单）
        ///5：手工拆出的子订单（这种单不能再参与合并，只能通过手工合并）
        /// </summary>
        public int MergeredType { get; set; }
        /// <summary>
        /// 是否是合并订单:主订单
        /// </summary>
        [NotMapped]
        public bool IsMainOrder { get { return MergeredType == 3 || MergeredType == 4; } }

        /// <summary>
        /// 是否是被合并订单：子订单
        /// </summary>
        [NotMapped]
        public bool IsChildOrder { get { return MergeredType == 1 || MergeredType == 2; } }


        /// <summary>
        ///  
        /// </summary>
        public string MergeredOrderId { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public string ChildOrderId { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 平台补贴金额
        /// </summary>
        public decimal? PlatformSubsidy { get; set; }
        /// <summary>
        /// 运费 LogicOrder.ShippingFee
        /// </summary>
        public decimal? ShippingFee { get; set; }


        /// <summary>
        ///  
        /// </summary>
        public decimal TotalWeight { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public int ProductCount { get; set; }


        /// <summary>
        ///  
        /// </summary>
        public int ProductPlatformIdCount { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public int ProductKindCount { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public int ProductItemCount { get; set; }

        /// <summary>
        ///  MD5取值：FxUserId+ErpState+ShopId+PathFlowCode+ToName+ToPhone+ToFullAddress
        /// </summary>
        public string BuyerHashCode
        {
            get
            {
                var rec = (ToName + ToPhone + ToFullAddress) ?? "";
                if (PlatformType == "Pinduoduo")
                {
                    //open_address_id
                    if (string.IsNullOrEmpty(DecryptField))
                    {
                        var toName = StringExtension.ExtractPddSearchIndex(ToName);
                        if (string.IsNullOrEmpty(toName))
                            toName = ToName;
                        var toMobile = StringExtension.ExtractPddSearchIndex(ToPhone);
                        var toAddress = StringExtension.ExtractPddSearchIndex(ToAddress);
                        if (string.IsNullOrEmpty(toAddress))
                            toAddress = ToAddress;
                        rec = (toName + toMobile + ToProvince + ToCity + ToCounty + toAddress) ?? "";
                        if (ExtField1 == "2" || ExtField2 == "1")
                        {
                            //跨境单
                            rec = rec + PlatformOrderId;
                        }
                        else if (ExtField1 == "1" && string.IsNullOrWhiteSpace(toAddress))//拼多多厂家代打订单
                        {
                            rec = rec + PlatformOrderId; //这里为空就赋值订单id，作用是防止历史订单合并
                        }
                        if (TradeType == "集运")
                        {
                            rec = rec + LogicOrderId;
                        }
                    }
                    else
                    {
                        rec = DecryptField;
                    }
                }
                else if (PlatformType == "Virtual")
                {
                    rec = (ToName + ToPhone + ToFullAddress + ApprovalStatus) ?? "";
                    if (DecryptField.IsNotNullOrEmpty())
                    {
                        // 追加来源平台
                        rec += ExtField3;
                        // 追加oaid
                        rec += DecryptField;
                    }
                    else if (ExtField2.IsNotNullOrEmpty()) rec += ExtField2;
                }
                else if (PlatformType == "TouTiao" || PlatformType == "TouTiaoSaleShop")
                {
                    //open_address_id
                    if (string.IsNullOrEmpty(DecryptField))
                    {
                        var toName = ToName.IsDyEncryptData() ? ToName.ExtractDySearchIndex() : ToName;
                        if (string.IsNullOrEmpty(toName))
                            toName = ToName;
                        var toMobile = ToPhone.IsDyEncryptData() ? ToPhone.ExtractDySearchIndex() : ToPhone;
                        if (string.IsNullOrEmpty(toMobile))
                            toMobile = ToPhone;
                        var toAddress = ToAddress.IsDyEncryptData() ? ToAddress.ExtractDySearchIndex() : ToAddress;
                        if (string.IsNullOrEmpty(toAddress))
                            toAddress = ToAddress;
                        //未加密按照原有方式，与历史数据保持一致
                        if (toName != ToName || toMobile != ToPhone || toAddress != ToAddress)
                        {
                            var pcc = ToProvince.ToString2() + ToCity.ToString2() + ToCounty.ToString2();
                            var street = ToFullAddress.TrimStart(pcc).TrimEnd(ToAddress);
                            rec = (toName + toMobile + pcc + street + toAddress) ?? "";
                        }
                    }
                    else
                    {
                        rec = DecryptField;
                    }
                    
                    // 即时零售无需校验抖店的部分时间字段合并
                    if (PlatformType == "TouTiaoSaleShop")
                    {
                        // 即时零售 次日达外,其他订单不合并
                        if (ExtField2 != "19")
                            rec += PlatformOrderId;
                        else
                            rec += ExtField2;// 不允许跨标签合并

                        // 不允许跨仓库合并
                        rec += WarehouseId; 
                    }
                    else
                    {
                        if (ExtField2 == "logistics_transit")
                            rec += PlatformOrderId;   //新疆中转订单不允许合并

                        //抖店 预约发货时间不为空时，只有预约发货订单之间只有预约发货时间相同才能合并

                        if (ExtField2 == OrderTag.appointment_ship_time.ToString())
                        {
                            rec += ExtField3;
                        }
                        //送礼单不允许合并
                        if (ExtField1 == "1")
                        {
                            rec += PlatformOrderId;
                        }
                        //风控订单不允许合并(平台解除风控允许合并)
                        if (ExtField1 == OrderTag.risk_processing.ToString())
                        {
                            rec += PlatformOrderId;
                        }
                    }
                }
                else if (PlatformType == "Taobao" && DecryptField.IsNotNullOrEmpty())
                {
                    rec = DecryptField; // 淘宝根据OaId确定是否合单(7天内相同)
                }
                else if ((PlatformType == "AlibabaC2M" || PlatformType == "TaobaoMaiCaiV2") && DecryptField.IsNotNullOrEmpty())
                {
                    var baseCode = DecryptField.SplitToList("-").FirstOrDefault();
                    rec = baseCode; // 淘工厂根据CaId确定是否合单(7天内相同)
                }
                else if (PlatformType == "KuaiShou" && DecryptField.IsNotNullOrEmpty())
                {
                    rec = DecryptField;

                    if (TradeType == "logistics_transit")
                        rec += "中转订单";
                }
                else if (PlatformType == "Alibaba" && DecryptField.IsNotNullOrEmpty())
                {
                    //阿里巴巴和阿里c2m 的处理是一样的，目前先单独写在一个方法
                    var baseCode = DecryptField.SplitToList("-")?.FirstOrDefault() ?? "";
                    rec = baseCode; // 阿里根据CaId确定是否合单

                    //1688分销订单，ExtField1=outChannel，如：kuaishou\douyin
                    if (string.IsNullOrWhiteSpace(ExtField1) == false)
                        rec = baseCode + ExtField1;

                    //跨境转运订单不允许合并
                    if (ExtField2.IsNotNullOrEmpty() && ExtField2.Contains("logistics_transit"))
                        rec += PlatformOrderId;

                    // 淘大店订单，根据ExtField3判断是否合并
                    if (ExtField3 == DataConstants.TaoBigShopFlag)
                    {
                        rec += ExtField3;
                    }

                }
                else if (PlatformType == "TuanHaoHuo" && DecryptField.IsNotNullOrEmpty())
                {
                    rec = DecryptField;
                }
                else if (PlatformType == "XiaoHongShu" && DecryptField.IsNotNullOrEmpty())
                {
                    rec = DecryptField;
                }
                else if (PlatformType == "WeiDian" && DecryptField.IsNotNullOrEmpty())
                {
                    var tempMobile = ToPhone;
                    if (ToPhone.IsNotNullOrEmpty())
                    {
                        var tempArr = ToPhone.Split('#');
                        if (tempArr.Length > 1) tempMobile = tempArr[1];

                    }
                    rec = (ToName + tempMobile + ToFullAddress) ?? "";
                }
                else if (PlatformType == "WxVideo")
                {
                    //if (!string.IsNullOrWhiteSpace(ExtField1))
                    rec = (ToName + ExtField1 + ToFullAddress + DecryptField) ?? "";

                    //rec = DecryptField; // 20231103根据接口提供的地址唯一码合并
                }
                else if (PlatformType == "Jingdong")
                {
                    if (ExtField1.IsNotNullOrEmpty())
                    {
                        rec = ExtField1;
                    }
                    //京东DecryptField 填充的是原手机号密文，新增OAID模式后，为空，新的Oaid填充到了ExtField1中，合单根据此判断
                    var time = GetPublishTime();
                    if (time.HasValue)
                    {
                        rec += time?.ToString("yyyy-MM-dd");
                    }
                }

                rec = FxUserId + ErpState + ShopId + PathFlowCode + rec.RemoveSpecialChar();
                return rec.ToShortMd5();
            }
        }

        /// <summary>
        /// 源自P_Order.BuyerHashCode
        /// </summary>
        public string OrderBuyerHashCode { get; set; }

        /// <summary>
        /// 新版BuyerHashCode：MD5取值：FxUserId+ErpState+ShopId+PathFlowCode+OrderBuyerHashCode
        /// </summary>
        public string BuyerHashCodeV2
        {
            get
            {
                var rec = FxUserId + ErpState + ShopId + PathFlowCode + OrderBuyerHashCode;

                if (PlatformType == "Virtual")
                {
                    rec = rec + ApprovalStatus;
                    if (DecryptField.IsNotNullOrEmpty())
                    {
                        // 追加来源平台
                        rec += ExtField3;
                        // 追加oaid
                        rec += DecryptField;
                    }
                    else if (ExtField2.IsNotNullOrEmpty()) rec += ExtField2;
                }
                //1688采购单，同一外部渠道的订单允许合并 2023.12.06 邱
                //if (TradeType == "AlibabaSupplierOrder")
                //{
                //    //针对1688采购单，需求(在BuyerHashCodeV1\V2的属性逻辑中，增加判断逻辑：当TradeType为1688采购单时，生成规则中的字符增加订单ID)
                //    rec = rec + ApprovalStatus + LogicOrderId;
                //}
                if (PlatformType == "Alibaba" && ExtField1.IsNotNullOrEmpty())
                {
                    //1688采购订单，ExtField1=outChannel，如：kuaishou\douyin
                    if (CustomerConfig.Need1688OutChannels.Contains(ExtField1))
                        rec += ExtField1;

                    //跨境转运订单不允许合并
                    if (ExtField2.IsNotNullOrEmpty() && ExtField2.Contains("logistics_transit"))
                        rec += PlatformOrderId;
                }
                if (PlatformType == "Pinduoduo")
                {
                    if (TradeType == "集运")
                    {
                        rec += LogicOrderId.RemoveSpecialChar();
                    }
                }
                //抖店 预约发货时间不为空时，只有预约发货订单之间只有预约发货时间相同才能合并 或 预约发货订单可以和非预约发货的订单合并
                //预约发货时间已使用Order.ReceiptDate存储，且参与OrderBuyerHashCode的生成
                /*if(PlatformType == "TouTiao")
                {
                    if (AppointmentShipDate != null) 
                    {
                        rec += AppointmentShipDate?.ToString("yyyy-MM-dd");
                    }
                }*/

                // 京东订单合并规则:
                // 0. 京东预约订单有特定的合并规则。
                // 1. 订单预约发货时间由 PublishTime 字段表示。
                // 2. 预约发货订单不能与其他订单合并。
                // 3. 预约发货订单只能与同一天的预约发货订单合并。
                if (PlatformType == "Jingdong")
                {
                    var time = GetPublishTime();
                    if (time.HasValue)
                    {
                        rec += time.Value.ToString("yyyy-MM-dd");
                    }
                }

                return rec.ToShortMd5();
            }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ToName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ToPhone { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public string ToProvince { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public string ToCity { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public string ToCounty { get; set; }
        /// <summary>
        /// 街道、城镇
        /// </summary>
        public string ToTown { get; set; }

        /// <summary>
        /// 只保存打码数据
        /// </summary>
        public string ToAddress { get; set; }

        /// <summary>
        /// 只保存打码数据
        /// </summary>
        public string ToFullAddress { get; set; }
        /// <summary>
        /// 收件人短地址，仅包含：省市区
        /// </summary>
        public string ToShortAddress
        {
            get
            {
                var shortAddr = $"{ToProvince} {ToCity} {ToCounty}";
                if (!string.IsNullOrEmpty(ToTown))
                {
                    shortAddr += $" {ToTown}";
                }
                if (PlatformType.IsNullOrEmpty())
                    return shortAddr;
                if ((PlatformType == "Jingdong" || PlatformType == "KuaiShou"))
                {
                    if (ToProvince.IsNotNullOrEmpty() && CommUtls.ZhiXiaShiDic().ContainsKey(ToProvince))
                        shortAddr = $"{ToProvince} {ToProvince} {ToCounty}";
                }
                else if (PlatformType == "TouTiao")
                {
                    if (ToFullAddress.IsNotNullOrEmpty() && ToCity.IsNotNullOrEmpty() && CommUtls.DiJiShiDic().ContainsKey(ToCity))
                    {
                        var pcc = ToProvince + ToCity + ToCounty;
                        var toCounty = ToFullAddress?.Replace(ToAddress, "").Replace(pcc, "");
                        shortAddr = $"{ToProvince} {ToCity} {ToCounty}";
                    }
                }
                else if (CustomerConfig.IsCrossBorderSite && PlatformType == "TikTok") { }
                {
                    shortAddr = $"{ToCountry} {ToProvince} {ToCity} {ToCounty}";
                }
                return shortAddr;
            }
        }

        /// <summary>
        ///  
        /// </summary>
        public string PlatformType { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public string OrderCode { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public string LogicOrderId { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public string PathFlowCode { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public string ErpState { get; set; }

        public int FxUserId { get; set; }
        public bool IsPreviewed { get; set; }
        public DateTime? PayTime { get; set; }

        private string _BuyerRemark;
        public string BuyerRemark
        {
            get { return _BuyerRemark; }
            set { _BuyerRemark = value ?? string.Empty; }
        }

        private string _SellerRemark;
        public string SellerRemark
        {
            get { return _SellerRemark; }
            set { _SellerRemark = value ?? string.Empty; }
        }

        private string _SellerRemarkFlag;
        public string SellerRemarkFlag
        {
            get { return _SellerRemarkFlag; }
            set { _SellerRemarkFlag = value ?? string.Empty; }
        }

        public int ResendState { get; set; }

        /// <summary>
        ///  平台加密字段
        /// </summary>
        public string DecryptField { get; set; }

        /// <summary>
        /// 订单类型(1688轻应用采购单：AlibabaSupplierOrder )
        /// </summary>
        public string TradeType { get; set; }

        /// <summary>
        ///  扩展字段1
        /// </summary>
        public string ExtField1 { get; set; }

        /// <summary>
        /// 扩展字段2
        /// </summary>
        public string ExtField2 { get; set; }

        /// <summary>
        ///  扩展字段3
        /// </summary>
        public string ExtField3 { get; set; }

        /// <summary>
        /// 快手收件人加密信息
        /// </summary>
        [NotMapped]
        public KuaiShouEncryptedReceiverInfo EncryptedReceiverInfo { get; set; }

        /// <summary>
        /// 是否为多包裹（由我们系统控制），仅抖店
        /// </summary>
        public bool? IsMultiPack { get; set; }
        /// <summary>
        /// 收件人信息HashCode
        /// </summary>
        public string ReceiverHashCode { get; set; }

        /// <summary>
        /// 收件人电话搜索串（密文为索引串）
        /// </summary>
        public string ToPhoneIndex { get; set; }

        public string StockState { get; set; }
        /// <summary>
        /// 数据标识（区分冷，热数据）0:热数据，1:冷数据，有些情况用来识别冷热数据，需要前置识别
        /// </summary>
        [NotMapped]
        public int DataFlag { get; set; }
        /// <summary>
        /// 版本号
        /// </summary>
        public long? VersionNo { get; set; }

        /// <summary>
        /// 平台状态
        /// </summary>
        public string PlatformStatus { get; set; }

        /// <summary>
        /// 履约方式：BY_SELLER、BY_TIKTOK
        /// </summary>
        public string FulfillmentType { get; set; }

        /// <summary>
        /// 发货方式,目前跨境需要 TIKTOK:平台物流
        /// </summary>
        public string ShippingType { get; set; }
        /// <summary>
        /// 交运方式需要,目前跨境需要
        /// </summary>
        public string DeliveryOptionId { get; set; }

        /// <summary>
        ///收件人的国家当平台类型为Tiktok时：来源于平台订单的
        /// </summary>
        public string ToCountry { get; set; }

        /// <summary>
        /// 揽收方式
        /// </summary>
        public string CollectionType { get; set; }

        /// <summary>
        /// 原始订单项数量
        /// </summary>

        public int POrderItemCount { get; set; }

        /// <summary>
        /// 交运状态
        /// 0：未打印未交运
        /// 1：已打印未交运
        /// 2：已打印已交运
        /// </summary>
        public int LogisticStatus { get; set; }

		/// <summary>
		/// 发运类型,跨境。值：Global standard shipping（全球标准运输服务）、Standard shipping（本地标准运输服务）
		/// </summary>
		public string DeliveryOptionName { get; set; }


		//* SUCCESS--开票成功
		//* PROCESSING--开票中
		//* FAILED--开票失败
		//* INVALID--无效发票
		/// <summary>
		/// 发票状态=》TK巴西订单所需
		/// </summary>
		public string InvoiceStatus { get; set; }

        /// <summary>
        /// 当自寄时不为空，来自表OrderSelfDelivery.BatchNo
        /// </summary>
        public string OrderSelfDeliveryBatchNo { set; get; }

        private string _SystemRemarkFlag;

        /// 分发备注旗帜
        /// </summary>
        public string SystemRemarkFlag
        {
            get { return _SystemRemarkFlag; }
            set { _SystemRemarkFlag = value ?? string.Empty; }
        }

        /// <summary>
        /// 平台名称
        /// </summary>
        [NotMapped]
        public string PlatformTypeName { get; set; }

        /// <summary>
        /// 是否是手工发货
        /// </summary>
        [NotMapped]
        public bool IsManual { get; set; } = false;

        /// <summary>
        /// 同步外部wms 状态 1 已同步
        /// </summary>
        public int? SyncOutStatus { get; set; }
    }
}
