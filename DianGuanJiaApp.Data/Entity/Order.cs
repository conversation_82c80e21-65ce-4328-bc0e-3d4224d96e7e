using System;
using System.Linq;
using Dapper;
using Newtonsoft.Json;
using DianGuanJiaApp.Data.MongoRepository;
using DianGuanJiaApp.Utility.Extension;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;

namespace DianGuanJiaApp.Data.Entity
{
    /// <summary>
    /// 订单数据
    /// </summary>
    [Table("P_Order")]
    [CollectionName("Order")]
    public partial class Order : BaseEntity
    {
        //[BsonIgnore]
        //public int _dbId;
        [Key]
        [BsonIgnore]
        public int Id { get; set; }
        //public int Id { get { if (_dbId != 0) return _dbId; else if (!string.IsNullOrEmpty(_Id)) return Math.Abs(_Id.GetHashCode()); return _dbId; } set { _dbId = value; if (value == -1) { _Id = ObjectId.GenerateNewId().ToString(); } } }
        public string PlatformOrderId { get; set; }
        public int ShopId { get; set; }
        public string PlatformType { get; set; }
        public bool? IsMergered { get; set; }
        public bool? IsSplited { get; set; }

        private string _ChildOrderId;

        [BsonElement("ChildOrderId")]
        public string ChildOrderId
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_ChildOrderId))
                    if (OrderItems != null && !OrderItems.Any(o => string.IsNullOrEmpty(o.OrignalOrderId)))
                        return string.Join(",", OrderItems.Select(o => o.OrignalOrderId).Distinct());
                return "";
            }
        }
        //[JsonIgnore]
        public string ParentOrderId { get; set; }
        //[JsonIgnore]
        public string MergeredOrderId { get; set; }
        //[JsonIgnore]
        [BsonElement("BuyerHashCode")]
        public string BuyerHashCode
        {
            get
            {
                var rec = (ToName + ToMobile + ToFullAddress + BuyerMemberId) ?? "";
                if (PlatformType == "TuanHaoHuo")
                {
                    if (string.IsNullOrWhiteSpace(ExtField2))
                        rec = (ToName + ToMobile + ToFullAddress) ?? "";
                    else
                        rec = ExtField2;
                }
                else if (PlatformType == "XiaoHongShu")
                {
                    if (string.IsNullOrWhiteSpace(ExtField3))
                    {
                        if (string.IsNullOrWhiteSpace(ExtField2))
                            rec = (ToName + ToMobile + ToFullAddress) ?? "";
                        else
                            rec = ExtField2;
                    }
                    else
                        rec = ExtField3;
                }
                else if (PlatformType == "KuaiShou")
                {
                    if (ExtField3.IsNotNullOrEmpty())
                        rec = ExtField3;
                    else if (string.IsNullOrWhiteSpace(ExtField2))
                        rec = (ToName + ToMobile + ToFullAddress) ?? "";
                    else
                        rec = ExtField2;

                    if (TradeType == "logistics_transit")
                        rec += "中转订单";
                }
                else if (PlatformType == "Pinduoduo")
                {
                    if (ExtField3.IsNullOrEmpty())
                    {
                        var toName = StringExtension.ExtractPddSearchIndex(ToName);
                        if (string.IsNullOrEmpty(toName))
                            toName = ToName;
                        var toMobile = StringExtension.ExtractPddSearchIndex(ToMobile);
                        if (string.IsNullOrEmpty(toMobile))
                            toMobile = ToMobile;
                        var toAddress = StringExtension.ExtractPddSearchIndex(ToAddress);
                        if (string.IsNullOrEmpty(toAddress))
                            toAddress = ToAddress;

                        var buyerMemberId = toName;
                        if (PlatformStatus == "1" || PlatformStatus == "0")//拼多多厂家代打订单
                        {
                            toAddress = string.IsNullOrWhiteSpace(toAddress) ? PlatformOrderId : toAddress; //这里为空就赋值订单id，作用是防止历史订单合并
                        }

                        rec = (toName + toMobile + ToProvince + ToCity + ToCounty + ToTown + toAddress + buyerMemberId) ?? "";
                        if (BusinessType == "2" || IsWeiGong)
                        {
                            //跨境单
                            rec = rec + PlatformOrderId;
                        }
                    }
                    else
                    {
                        rec = ExtField3;
                    }
                }
                else if (PlatformType == "Virtual")
                {
                    rec = (ToName + ToPhone + ToFullAddress + ApprovalStatus) ?? "";
                    if (ExtField7.IsNotNullOrEmpty())
                    {
                        // 追加来源平台
                        rec += ExtField5;
                        // 密文线下单，需要追加Oaid
                        rec += ExtField3;
                    }
                    else if (ExtField2.IsNotNullOrEmpty()) rec += ExtField2;
                }
                else if (PlatformType == "TouTiao" || PlatformType == "TouTiaoSaleShop")
                {
                    var toName = ToName.IsDyEncryptData() ? ToName.ExtractDySearchIndex() : ToName;
                    if (ExtField3.IsNotNullOrEmpty()) //ShopStatus == "BuyerHashCodeNewLogic" && 
                    {
                        rec = ExtField3;
                    }
                    else if (string.IsNullOrEmpty(toName) && string.IsNullOrEmpty(ExtField3) == false)
                    {
                        rec = ExtField3;
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(toName))
                            toName = ToName;
                        var toMobile = ToMobile.IsDyEncryptData() ? ToMobile.ExtractDySearchIndex() : ToMobile;
                        if (string.IsNullOrEmpty(toMobile))
                            toMobile = ToMobile;
                        var toAddress = ToAddress.IsDyEncryptData() ? ToAddress.ExtractDySearchIndex() : ToAddress;
                        if (string.IsNullOrEmpty(toAddress))
                            toAddress = ToAddress;
                        //未加密按照原有方式，与历史数据保持一致
                        if (toName != ToName || toMobile != ToMobile || toAddress != ToAddress)
                        {
                            var pcc = ToProvince.ToString2() + ToCity.ToString2() + ToCounty.ToString2();
                            var street = ToFullAddress.TrimStart(pcc).TrimEnd(ToAddress);
                            rec = (toName + toMobile + pcc + street + toAddress) ?? "";
                        }
                    }

                    // 即时零售无需校验抖店的部分时间字段合并
                    if (PlatformType == "TouTiaoSaleShop")
                    {
                        // 即时零售 次日达外,其他订单不合并
                        if (ExtField2 != "19")
                            rec += PlatformOrderId;
                        else
                            rec += ExtField2;// 不允许跨标签合并

                        // 不允许跨门店合并
                        rec += BusinessType;
                        // 不允许跨仓库合并
                        rec += Warehouse; 
                    }
                    else
                    {
                        if (BusinessType == "2")
                            rec += BusinessType;//预售单不与普通订单合单
                        if (ExtField2 == "appointment_arrival_flower" || ExtField2 == "logistics_transit")
                            rec += PlatformOrderId;   //选日达、新疆中转订单不允许合并
                        if (ExtField2 == OrderTag.appointment_ship_time.ToString() && ReceiptDate!=null) // 预约发货订单 发货时间相同才能合并
                        {
                            rec += ReceiptDate?.ToString("yyyy-MM-dd");
                        }
                        if (ExtField5 == "1")
                        {
                            rec += PlatformOrderId; // 送礼单不允许合并
                        }
                        if (ExtField6 == OrderTag.risk_processing.ToString())
                        {
                            rec += PlatformOrderId; //风控订单不允许合并(不为这个值时是可以合单的，代表风控订单通过了)
                        }
                    }
                }
                else if (PlatformType == "Taobao" && ExtField3.IsNotNullOrEmpty())
                {
                    rec = BuyerMemberId + ExtField3; // 淘宝根据OaId确定是否合单
                }
                else if ((PlatformType == "AlibabaC2M" || PlatformType == "TaobaoMaiCaiV2") && ExtField3.IsNotNullOrEmpty())
                {
                    var baseCode = ExtField3.SplitToList("-")?.FirstOrDefault() ?? "";
                    rec = BuyerMemberId + baseCode; // 淘工厂根据CaId确定是否合单
                }
                else if (PlatformType == "Alibaba" && ExtField3.IsNotNullOrEmpty())
                {
                    //阿里巴巴和阿里c2m 的处理是一样的，目前先单独写在一个方法
                    var baseCode = ExtField3.SplitToList("-")?.FirstOrDefault() ?? "";
                    rec = BuyerMemberId + baseCode; // 阿里根据CaId确定是否合单
                    rec += ExtField5 + ExtField6 + OrderFrom; // 官方直送保障服务订单(OrderFrom = "officialLogisticOrder"),不与正常订单合并 
                    //阿里来之淘特的采购单，不与正常订单合并。(ttpft:淘特批发团订单，会到1688这边下单，由阿里商家履约)
                    if (BusinessType == "ttpft")
                        rec += BusinessType;

                    //不包含淘系订单
                    if (!string.IsNullOrWhiteSpace(ExtField5) && CommUtls.IsHasTaoXiAliFxPt(ExtField5) == false) //ExtField5 == "douyin" || ExtField5 == "kuaishou" || ExtField5 == "pinduoduo"
                    {
                        rec = ExtField3 + BuyerMemberId + ExtField5 + OrderFrom;
                    }
                    // 淘大店订单不与普通订单合并
                    if (BusinessType == DataConstants.TaoBigShopFlag)
                    {
                        rec += BusinessType;
                    }
                    //跨境转运订单不允许合并
                    if (OrderFrom.IsNotNullOrEmpty() && OrderFrom.Contains("logistics_transit"))
                        rec += PlatformOrderId;

                    //官方仓发订单不允许合并
                    if (OrderFrom.IsNotNullOrEmpty() && OrderFrom.Contains("hyperLinkShip"))
                        rec += PlatformOrderId;
                }
                else if (PlatformType == "Suning" && ExtField1.IsNotNullOrEmpty())
                {
                    rec = BuyerMemberId + ExtField1;
                }
                else if (PlatformType == "WxXiaoShangDian")
                {
                    rec = rec.Replace("\u200B", "").Replace("\n", "");
                }
                else if (PlatformType == "WxVideo")
                {
                    //视频号数据加密后，接口会返回收件人信息的唯一标识，按此标识来合单，标识值存储在ExtField4字段内
                    //文档：https://doc.weixin.qq.com/doc/w3_AeQAsQaWAFgmUwV2735R72fQr4dmo?scode=AJEAIQdfAAoKr80J88AH4ARAZHAEo
                    if (!string.IsNullOrEmpty(ExtField4))
                    {
                        rec = ExtField4;
                    }
                    else
                    {
                        var tempMobile = "";
                        if (ExtField1 != "")
                        {
                            //ExtField1 真实手机号,不为空表示使用了虚拟手机号，合单中，使用此字段作为手机号
                            tempMobile = ExtField1;
                        }
                        else
                        {
                            //兼容之前使用店管家加密方式,不变
                            tempMobile = ToMobile;
                            if (ToMobile.IsNotNullOrEmpty())
                            {
                                var tempArr = ToMobile.Split('#');
                                if (tempArr.Length > 1) tempMobile = tempArr[1];

                            }
                        }
                        rec = (ToName + tempMobile + ToFullAddress + BuyerMemberId) ?? "";
                        rec = rec.Replace("\u200B", "").Replace("\n", "");
                    }
                }
                else if (PlatformType == "WeiDian")
                {
                    var tempMobile = ToMobile;
                    if (ToMobile.IsNotNullOrEmpty())
                    {
                        var tempArr = ToMobile.Split('#');
                        if (tempArr.Length > 1)
                            tempMobile = tempArr[1];
                    }
                    rec = (ToName + tempMobile + ToFullAddress + BuyerMemberId) ?? "";
                }
                //else if (PlatformType == "YouZan") 
                //    rec = (ToName + ToMobile + ToFullAddress) ?? "";
                else if (PlatformType == "Jingdong" || PlatformType == "JingdongPurchase")
                {
                    if (ExtField6.IsNotNullOrEmpty())
                    {
                        rec = ExtField6;
                    }
                    // 京东平台订单 PublishTime 有值，是预约发货订单，不与普通订单合并 2024-10-11
                    if (PublishTime != null)
                    {
                        rec += PublishTime.Value.ToString("yyyy-MM-dd");
                    }
                }
                rec = rec.RemoveSpecialChar();
                if (!string.IsNullOrEmpty(Warehouse))
                    rec += Warehouse;
                if (PlatformType == "Pinduoduo" && IsWeiGong)
                    rec += "风控";
                if (PlatformType == "TouTiao" && TradeType == "0")
                    rec += "代收货款";
                if (RefundStatus?.ToLower()?.Replace("_", "") == "refundsuccess")
                    rec += "退款成功";
                //淘宝 清仓订单+清仓订单允许合并，清仓订单+普通订单不允许合并
                if (PlatformType == "Taobao" && ExtField5 == OrderTag.clearance_order.ToString())
                {
                    rec += OrderTag.clearance_order.ToString();
                }
                return rec.ToShortMd5();
            }
        }
        //[JsonIgnore]
        [BsonElement("PrintedBuyerHashCode")]
        public string PrintedBuyerHashCode
        {
            get
            {
                var rec = (ToName + ToMobile + ToFullAddress + BuyerMemberId) ?? "";
                if (PlatformType == "TuanHaoHuo")
                {
                    if (string.IsNullOrWhiteSpace(ExtField2))
                        rec = (ToName + ToMobile + ToFullAddress) ?? "";
                    else
                        rec = ExtField2;
                }
                else if (PlatformType == "XiaoHongShu")
                {
                    if (string.IsNullOrWhiteSpace(ExtField3))
                    {
                        if (string.IsNullOrWhiteSpace(ExtField2))
                            rec = (ToName + ToMobile + ToFullAddress) ?? "";
                        else
                            rec = ExtField2;
                    }
                    else
                        rec = ExtField3;
                }
                else if (PlatformType == "KuaiShou")
                {
                    if (ExtField3.IsNotNullOrEmpty())
                        rec = ExtField3;
                    if (string.IsNullOrWhiteSpace(ExtField2))
                        rec = (ToName + ToMobile + ToFullAddress) ?? "";
                    else
                        rec = ExtField2;

                    if (TradeType == "logistics_transit")
                        rec += "中转订单";
                }
                if (PlatformType == "Pinduoduo")
                {
                    if (ExtField3.IsNullOrEmpty())
                    {
                        var toName = StringExtension.ExtractPddSearchIndex(ToName);
                        if (string.IsNullOrEmpty(toName))
                            toName = ToName;
                        var toMobile = StringExtension.ExtractPddSearchIndex(ToMobile);
                        if (string.IsNullOrEmpty(toMobile))
                            toMobile = ToMobile;
                        var toAddress = StringExtension.ExtractPddSearchIndex(ToAddress);
                        if (string.IsNullOrEmpty(toAddress))
                            toAddress = ToAddress;

                        var buyerMemberId = toName;
                        if (PlatformStatus == "1" || PlatformStatus == "0")//拼多多厂家代打订单
                        {
                            toAddress = string.IsNullOrWhiteSpace(toAddress) ? PlatformOrderId : toAddress; //这里为空就赋值订单id，作用是防止历史订单合并
                        }
                        rec = (toName + toMobile + ToProvince + ToCity + ToCounty + toAddress + buyerMemberId) ?? "";
                        if (BusinessType == "2")
                        {
                            //跨境单
                            rec = rec + PlatformOrderId;
                        }
                    }
                    else
                    {
                        rec = ExtField3;
                    }
                }
                else if (PlatformType == "Virtual")
                {
                    rec = (ToName + ToPhone + ToFullAddress + ApprovalStatus) ?? "";
                }
                else if (PlatformType == "TouTiao" || PlatformType == "TouTiaoSaleShop")
                {
                    var toName = ToName.IsDyEncryptData() ? ToName.ExtractDySearchIndex() : ToName;
                    if (ExtField3.IsNotNullOrEmpty()) //ShopStatus == "BuyerHashCodeNewLogic" &&
                    {
                        rec = ExtField3;
                    }
                    else if (string.IsNullOrEmpty(toName) && string.IsNullOrEmpty(ExtField3) == false)
                    {
                        rec = ExtField3;
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(toName))
                            toName = ToName;
                        var toMobile = ToMobile.IsDyEncryptData() ? ToMobile.ExtractDySearchIndex() : ToMobile;
                        if (string.IsNullOrEmpty(toMobile))
                            toMobile = ToMobile;
                        var toAddress = ToAddress.IsDyEncryptData() ? ToAddress.ExtractDySearchIndex() : ToAddress;
                        if (string.IsNullOrEmpty(toAddress))
                            toAddress = ToAddress;
                        //未加密按照原有方式，与历史数据保持一致
                        if (toName != ToName || toMobile != ToMobile || toAddress != ToAddress)
                        {
                            var pcc = ToProvince.ToString2() + ToCity.ToString2() + ToCounty.ToString2();
                            var street = ToFullAddress.TrimStart(pcc).TrimEnd(ToAddress);
                            rec = (toName + toMobile + pcc + street + toAddress) ?? "";
                        }
                    }
                    
                    // 即时零售无需校验抖店的部分时间字段合并
                    if (PlatformType == "TouTiaoSaleShop")
                    {
                        // 即时零售 次日达外,其他订单不合并
                        if (ExtField2 != "19")
                            rec += PlatformOrderId;
                        else
                            rec += ExtField2;// 不允许跨标签合并

                        // 不允许跨门店合并
                        rec += BusinessType;
                        // 不允许跨仓库合并
                        rec += Warehouse; 
                    }
                    else
                    {
                        if (BusinessType == "2")
                            rec += BusinessType;//预售单不与普通订单合单
                        if (ExtField2 == "appointment_arrival_flower" || ExtField2 == "logistics_transit")
                            rec += PlatformOrderId;   //选日达、新疆中转订单不允许合并
                        if (ExtField2 == OrderTag.appointment_ship_time.ToString() && ReceiptDate != null) // 预约发货订单 发货时间相同才能合并
                        {
                            rec += ReceiptDate?.ToString("yyyy-MM-dd");
                        }
                        if(ExtField5 == "1")
                        {
                            rec += PlatformOrderId; //抖店送礼单不允许合并
                        }
                        if (ExtField6 == OrderTag.risk_processing.ToString())
                        {
                            rec += PlatformOrderId; //风控订单不允许合并(不为这个值时是可以合单的，代表风控订单通过了)
                        }   
                    }
                }
                else if (PlatformType == "Taobao" && ExtField3.IsNotNullOrEmpty())
                {
                    rec = BuyerMemberId + ExtField3; // 淘宝根据OaId确定是否合单
                }
                else if ((PlatformType == "AlibabaC2M" || PlatformType== "TaobaoMaiCaiV2") && ExtField3.IsNotNullOrEmpty())
                {
                    var baseCode = ExtField3.SplitToList("-")?.FirstOrDefault() ?? "";
                    rec = BuyerMemberId + baseCode; // 淘工厂根据CaId确定是否合单
                }
                else if (PlatformType == "Alibaba" && ExtField3.IsNotNullOrEmpty())
                {
                    //阿里巴巴和阿里c2m 的处理是一样的，目前先单独写在一个方法
                    var baseCode = ExtField3.SplitToList("-")?.FirstOrDefault() ?? "";
                    rec = BuyerMemberId + baseCode; // 阿里根据CaId确定是否合单
                    rec += ExtField5 + ExtField6 + OrderFrom; // 官方直送保障服务订单(OrderFrom = "officialLogisticOrder"),不与正常订单合并
                    //阿里来之淘特的采购单，不与正常订单合并。(ttpft:淘特批发团订单，会到1688这边下单，由阿里商家履约)
                    if (BusinessType == "ttpft")
                        rec += BusinessType;

                    // 淘大店订单不与普通订单合并
                    if (BusinessType == DataConstants.TaoBigShopFlag)
                    {
                        rec += BusinessType;
                    }
                    //不包含淘系订单
                    if (!string.IsNullOrWhiteSpace(ExtField5) && CommUtls.IsHasTaoXiAliFxPt(ExtField5) == false) //ExtField5 == "douyin" || ExtField5 == "kuaishou" || ExtField5 == "pinduoduo"
                    {
                        rec = ExtField3 + BuyerMemberId + ExtField5 + OrderFrom;
                    }

                    //跨境转运订单不允许合并
                    if (OrderFrom.IsNotNullOrEmpty() && OrderFrom.Contains("logistics_transit"))
                        rec += PlatformOrderId;

                    //官方仓发订单不允许合并
                    if (OrderFrom.IsNotNullOrEmpty() && OrderFrom.Contains("hyperLinkShip"))
                        rec += PlatformOrderId;
                }
                else if (PlatformType == "Suning" && ExtField1.IsNotNullOrEmpty())
                {
                    rec = BuyerMemberId + ExtField1;
                }
                else if (PlatformType == "WxXiaoShangDian")
                {
                    rec = rec.Replace("\u200B", "").Replace("\n", "");
                }
                else if (PlatformType == "WxVideo")
                {
                    //视频号数据加密后，接口会返回收件人信息的唯一标识，按此标识来合单，标识值存储在ExtField4字段内
                    //文档：https://doc.weixin.qq.com/doc/w3_AeQAsQaWAFgmUwV2735R72fQr4dmo?scode=AJEAIQdfAAoKr80J88AH4ARAZHAEo
                    if (!string.IsNullOrEmpty(ExtField4))
                    {
                        rec = ExtField4;
                    }
                    else
                    {
                        var tempMobile = "";
                        if (ExtField1 != "")
                        {
                            //ExtField1 真实手机号,不为空表示使用了虚拟手机号，合单中，使用此字段作为手机号
                            tempMobile = ExtField1;
                        }
                        else
                        {
                            //兼容之前使用店管家加密方式,不变
                            tempMobile = ToMobile;
                            if (ToMobile.IsNotNullOrEmpty())
                            {
                                var tempArr = ToMobile.Split('#');
                                if (tempArr.Length > 1) tempMobile = tempArr[1];

                            }
                        }
                        rec = (ToName + tempMobile + ToFullAddress + BuyerMemberId) ?? "";
                        rec = rec.Replace("\u200B", "").Replace("\n", "");
                    }
                }
                else if (PlatformType == "WeiDian")
                {
                    var tempMobile = ToMobile;
                    if (ToMobile.IsNotNullOrEmpty())
                    {
                        var tempArr = ToMobile.Split('#');
                        if (tempArr.Length > 1)
                            tempMobile = tempArr[1];
                    }
                    rec = (ToName + tempMobile + ToFullAddress + BuyerMemberId) ?? "";
                }
                //else if (PlatformType == "YouZan")
                //    rec = (ToName + ToMobile + ToFullAddress) ?? "";
                else if (PlatformType == "Jingdong" || PlatformType == "JingdongPurchase")
                {
                    if (ExtField6.IsNotNullOrEmpty())
                        rec = ExtField6;
                }
                //去除地址中的特殊符号
                rec = rec.RemoveSpecialChar();
                if (!string.IsNullOrEmpty(Warehouse))
                    rec += Warehouse;
                if (LastExpressPrintTime != null || LastNahuoPrintTime != null || LastSendPrintTime != null)
                    rec += "1";
                if (PlatformType == "TouTiao" && TradeType == "0")
                    rec += "代收货款";
                if (PlatformType == "Pinduoduo" && IsWeiGong)
                    rec += "风控";
                if (RefundStatus?.ToLower()?.Replace("_", "") == "refundsuccess")
                    rec += "退款成功";
                //淘宝 清仓订单+清仓订单允许合并，清仓订单+普通订单不允许合并
                if (PlatformType == "Taobao" && ExtField5 == OrderTag.clearance_order.ToString())
                {
                    rec += OrderTag.clearance_order.ToString();
                }
                return rec.ToShortMd5();
            }
        }
        /// <summary>
        /// 新版BuyerHashCode
        /// </summary>
        [BsonElement("BuyerHashCodeV2")]
        public string BuyerHashCodeV2
        {
            get
            {
                var rec = (ReceiverHashCode + BuyerMemberId) ?? "";
                if (PlatformType == "Alibaba")
                {
                    if (string.IsNullOrWhiteSpace(ExtField5) == false)
                    {
                        rec += ExtField5;
                    }

                    //跨境转运订单不允许合并
                    if (OrderFrom.IsNotNullOrEmpty() && OrderFrom.Contains("logistics_transit"))
                        rec += PlatformOrderId;

                    //官方仓发订单不允许合并
                    if (OrderFrom.IsNotNullOrEmpty() && OrderFrom.Contains("hyperLinkShip"))
                        rec += PlatformOrderId;
                        
                    // 淘大店订单不与普通订单合并
                    if (BusinessType == DataConstants.TaoBigShopFlag)
                    {
                        rec += BusinessType;
                    }
                }
                else if (PlatformType == "Pinduoduo")
                {
                    rec = ReceiverHashCode;
                    if (BusinessType == "2" || IsWeiGong)
                    {
                        //跨境单
                        rec = rec + PlatformOrderId;
                    }
                }
                else if (PlatformType == "TouTiao" || PlatformType == "TouTiaoSaleShop" || PlatformType == "Taobao" || PlatformType == "AlibabaC2M" || PlatformType == "Suning" || PlatformType == "WxXiaoShangDian" || PlatformType == "TaobaoMaiCaiV2")
                {
                    rec = ReceiverHashCode;
                }
                // 即时零售无需校验抖店的部分时间字段合并
                if (PlatformType == "TouTiaoSaleShop")
                {
                    // 即时零售 次日达外,其他订单不合并
                    if (ExtField2 != "19")
                        rec += PlatformOrderId;
                    else
                        rec += ExtField2;// 不允许跨标签合并

                    // 不允许跨门店合并
                    rec += BusinessType;
                    // 不允许跨仓库合并
                    rec += Warehouse; 
                }
                // 抖店预约发货对接 预约发货订单需要预约发货日期相同才能合并
                if(PlatformType == "TouTiao" && ExtField2 == OrderTag.appointment_ship_time.ToString() && ReceiptDate!=null)
                {
                    rec += ReceiptDate?.ToString("yyyy-MM-dd");
                }
                // 抖店送礼单不允许合并
                if(PlatformType == "TouTiao" && ExtField5 == "1")
                {
                    rec += PlatformOrderId;
                }
                if (PlatformType == "TouTiao" && ExtField6 == OrderTag.risk_processing.ToString())
                {
                    rec += PlatformOrderId; //风控订单不允许合并(不为这个值时是可以合单的，代表风控订单通过了)
                }
                // 京东平台订单 PublishTime 有值，是预约发货订单，不与普通订单合并 2024-10-11
                if (PlatformType == "Jingdong" && PublishTime != null)
                {
                    rec += PublishTime.Value.ToString("yyyy-MM-dd");
                }
                rec = rec.RemoveSpecialChar();
                if (!string.IsNullOrEmpty(Warehouse))
                    rec += Warehouse;
                if (PlatformType == "Pinduoduo" && IsWeiGong)
                    rec += "风控";
                if (PlatformType == "TouTiao" && TradeType == "0")
                    rec += "代收货款";
                if(PlatformType == "KuaiShou")
                {
                    if (ExtField3.IsNotNullOrEmpty())
                    {
                        rec= ExtField3;
                    } 
                    if(TradeType == "logistics_transit")
                    {
                        rec += "中转订单";
                    }
                }
                    
                //淘宝 清仓订单+清仓订单允许合并，清仓订单+普通订单不允许合并
                if (PlatformType == "Taobao" && ExtField5 == OrderTag.clearance_order.ToString())
                {
                    rec += OrderTag.clearance_order.ToString();
                }
                
                // 阿里系订单需要区分买家Id
                if (PlatformType == "Alibaba" || PlatformType == "AlibabaC2M" || PlatformType == "Taobao" || PlatformType == "TaobaoMaiCai" || PlatformType == "TaobaoMaiCaiV2")
                    rec += BuyerMemberId;

                // 线下单
                if (PlatformType == "Virtual") 
                {
                    if (ExtField7.IsNotNullOrEmpty())
                    {
                        // 追加来源平台
                        rec += ExtField5;
                        // 追加Oaid
                        rec += ExtField3;
                    }
                    else if (ExtField2.IsNotNullOrEmpty()) rec += ExtField2;
                }
                
                return rec.ToShortMd5();
            }
        }

        public string CouldMergerOrderId { get; set; }
        public string CouldMergerOrderIdExt { get; set; }

        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? LastSendPrintTime { get; set; }

        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? LastNahuoPrintTime { get; set; }

        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? LastExpressPrintTime { get; set; }
        /// <summary>
        /// 订单项数量
        /// </summary>
        [BsonElement("ProductCount")]
        public int? ProductCount { get { return OrderItems?.Where(oi => oi.PlatformOrderId == this.PlatformOrderId)?.Count(); } }
        /// <summary>
        /// 产品种类数量
        /// </summary>
        [BsonElement("ProductKindCount")]
        public int? ProductKindCount { get { return OrderItems?.GroupBy(oi => oi.SkuID ?? (oi.ProductSubject + oi.Color + oi.Size + oi.ExtAttr1 ?? "" + oi.ExtAttr2 ?? "" + oi.ExtAttr3 ?? "" + oi.ExtAttr4 ?? "" + oi.ExtAttr5 ?? ""))?.Count(); } }
        /// <summary>
        /// 产品总数（订单项数量总计）
        /// </summary>
        [BsonElement("ProductItemCount")]
        public int? ProductItemCount { get { return this.OrderItems?.Where(oi => oi.PlatformOrderId == this.PlatformOrderId)?.Sum(oi => oi.Count); } }
        /// <summary>
        /// 产品数量（订单项产品ID一致视为同一产品）
        /// </summary>
        [BsonElement("ProductPlatformIdCount")]
        public int? ProductPlatformIdCount { get { return this.OrderItems?.Where(oi => oi.PlatformOrderId == this.PlatformOrderId)?.GroupBy(oi => oi.ProductID)?.Count(); } }

        /// <summary>
        /// 快递单已打印的订单项数量，部分打印订单：ExpressPrintedCount<ProductCount
        /// </summary>
        [BsonElement("ExpressPrintedCount")]
        public int? ExpressPrintedCount { get { return this.OrderItems?.Count(oi => oi.PlatformOrderId == this.PlatformOrderId && oi.LastExpressPrintTime != null); } }
        /// <summary>
        /// 拿货单已打印的订单项数量，部分打印订单：NaHuoPrintedCount<ProductCount
        /// </summary>
        [BsonElement("NaHuoPrintedCount")]
        public int? NaHuoPrintedCount { get { return this.OrderItems?.Count(oi => oi.PlatformOrderId == this.PlatformOrderId && oi.LastNahuoPrintTime != null); } }
        /// <summary>
        /// 发货单已打印的订单项数量，部分打印订单：FahuoPrintedCount<ProductCount
        /// </summary>
        [BsonElement("FahuoPrintedCount")]
        public int? FahuoPrintedCount { get { return this.OrderItems?.Count(oi => oi.PlatformOrderId == this.PlatformOrderId && oi.LastFahuoPrintTime != null); } }
        /// <summary>
        /// 已发货的订单项数
        /// </summary>
        [BsonElement("SendedCount")]
        public int? SendedCount { get { return this.OrderItems?.Where(oi => oi.PlatformOrderId == this.PlatformOrderId && oi.IsSended != null && oi.IsSended.Value)?.Count(); } }

        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? CreateTime { get; set; }

        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        [IgnoreInsert]
        public System.DateTime? UpdateTime { get; set; }

        public int? CategoryId { get; set; }
        //[BsonSerializer(typeof(EntityExtension.Int32Serializer))]
        public int SendPrintTimes { get; set; }
        //[BsonSerializer(typeof(EntityExtension.Int32Serializer))]
        public int NahuoPrintTimes { get; set; }
        //[BsonSerializer(typeof(EntityExtension.Int32Serializer))]
        public int ExpressPrintTimes { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public string BusinessType { get; set; }
        /// <summary>
        /// 运费
        /// </summary>
        [BsonRepresentation(BsonType.Decimal128)]
        public decimal? ShippingFee { get; set; }
        /// <summary>
        /// 平台补贴金额
        /// </summary>
        [BsonRepresentation(BsonType.Decimal128)]
        public decimal? PlatformSubsidy { get; set; }

        public string IsLocked { get; set; }
        public string OrderFrom { get; set; }
        public string BuyerWangWang { get; set; }
        public string BuyerMemberId { get; set; }

        [BsonRepresentation(BsonType.Decimal128)]
        public decimal? TotalAmount { get; set; }
        [BsonRepresentation(BsonType.Decimal128)]
        public decimal? TotalWeight { get; set; }//{ get { return this.OrderItems?.Sum(oi => oi.Weight); } }
        /// <summary>
        /// 只保存打码数据
        /// </summary>
        public string ToName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ToPhone { get; set; }
        /// <summary>
        /// 只保存打码数据
        /// </summary>
        public string ToMobile { get; set; }
        /// <summary>
        /// 省
        /// </summary>
        public string ToProvince { get; set; }
        /// <summary>
        /// 市
        /// </summary>
        public string ToCity { get; set; }
        public string ToPost { get; set; }
        /// <summary>
        /// 区
        /// </summary>
        public string ToCounty { get; set; }
        /// <summary>
        /// 街道、城镇
        /// </summary>
        public string ToTown { get; set; }
        /// <summary>
        /// 只保存打码数据
        /// </summary>
        public string ToAddress { get; set; }
        /// <summary>
        /// 只保存打码数据
        /// </summary>
        public string ToFullAddress { get; set; }
        [BsonElement("IsToCountyOrTown")]
        public bool IsToCountyOrTown
        {
            get
            {
                if (ToFullAddress != null && (ToFullAddress.Contains("乡") || ToFullAddress.Contains("镇")))
                    return true;
                return false;
            }
        }
        public string SenderName { get; set; }
        public string SenderPhone { get; set; }

        /// <summary>
        /// 发件人固话
        /// </summary>
        public string SenderMobile { get; set; }
        public string SenderAddress { get; set; }
        public string SenderCompany { get; set; }
        public string BuyerMemberName { get; set; }
        public string BuyerRemark { get; set; }
        public string SellerRemarkFlag { get; set; }
        public string SellerRemark { get; set; }
        //[JsonIgnore]
        public int? LastExpressTemplateId { get; set; }
        //[JsonIgnore]
        public string LastWaybillCode { get; set; }
        public string PlatformStatus { get; set; }
        /// <summary>
        /// 订单类型
        /// 1688轻应用采购单：AlibabaSupplierOrder
        /// 快手中转订单：logistics_transit
        /// </summary>
        //[JsonIgnore]
        public string TradeType { get; set; }
        public string RefundStatus { get; set; }
        [BsonRepresentation(BsonType.Decimal128)]
        public decimal? RefundPayment { get; set; }
        //[JsonIgnore]
        /// <summary>
        /// 买家评价状态(4:已评论,5:未评论,6;不需要评论,111:已过有效评论期（自加字段）)
        /// </summary>
        public int? SellerRateStatus { get; set; }
        //[JsonIgnore]
        public int? BuyerRateStatus { get; set; }

        //[JsonIgnore]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? ModifyTime { get; set; }

        public decimal? Discount { get; set; }

        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? PayTime { get; set; }

        //[JsonIgnore]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? AllDeliveredTime { get; set; }

        //[JsonIgnore]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? ConfirmedTime { get; set; }

        //[JsonIgnore]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? ReceivingTime { get; set; }

        [JsonIgnore]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? CompleteTime { get; set; }

        //[JsonIgnore]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? PublishTime { get; set; }

        //[JsonIgnore]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? LastSyncTime { get; set; }

        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public System.DateTime? LastSendTime { get; set; }
        /// <summary>
        /// 是否被用户手动拆出
        /// </summary>
        [BsonRepresentation(BsonType.Boolean)]
        public bool IsSplitedByUser { get; set; }
        /// <summary>
        /// 是否被用户手动合并
        /// </summary>
        [BsonRepresentation(BsonType.Boolean)]
        public bool IsMergeredByHand { get; set; }


        /// <summary>
        /// 是否是预发货订单
        /// 对于分单系统该字段：作为【是否拆逻辑单】标记
        /// </summary>
        [BsonRepresentation(BsonType.Boolean)]
        public bool IsPreordain { get; set; }
        /// <summary>
        /// 打印流水号
        /// </summary>
        public string PrintedSerialNumber { get; set; }

        /// <summary>
        /// 是否代收货款
        /// </summary>
        [BsonRepresentation(BsonType.Boolean)]
        public bool IsSvcCOD { get; set; }
        [BsonRepresentation(BsonType.Decimal128)]
        public decimal? SvcCODAmount { get; set; }

        /// <summary>
        /// 是否保价声明
        /// </summary>
        [BsonRepresentation(BsonType.Boolean)]
        public bool IsSvcInsure { get; set; }
        [BsonRepresentation(BsonType.Decimal128)]
        public decimal? SvcInsureAmount { get; set; }

        /// <summary>
        /// 有效的打印次数（有效面单数）
        /// </summary>
        //[JsonIgnore]
        //[BsonSerializer(typeof(EntityExtension.Int32Serializer))]
        public int PrintEffectiveCount { get; set; }
        //[JsonIgnore]
        //[BsonSerializer(typeof(EntityExtension.Int32Serializer))]
        public int DefaultSellerId { get; set; }
        //[JsonIgnore]
        public string BackUpPlatformStatus { get; set; }
        /// <summary>
        /// 是否微供订单
        /// </summary>
        [BsonRepresentation(BsonType.Boolean)]
        public bool IsWeiGong { get; set; }

        /// <summary>
        /// 打印内容 目前只有自由打印订单需要保存打印内容
        /// </summary>
        public string PrintContent { get; set; }

        /// <summary>
        /// 订单仓库：目前仅零售通有
        /// </summary>
        public string Warehouse { get; set; }



        /// <summary>
        /// 是否预览过运单
        /// </summary>
        [BsonRepresentation(BsonType.Boolean)]
        public bool IsPreviewed { get; set; }

        /// <summary>
        /// 最迟发货时间
        /// </summary>
        public DateTime? LastShipTime { get; set; }

        /// <summary>
        /// 扩展字段
        /// </summary>
        public string ExtField1 { get; set; }
        public string ExtField2 { get; set; }
        /// <summary>
        /// 抖店：open_address_id；小红书：openAddressId；快手：openAddressId；
        /// </summary>
        public string ExtField3 { get; set; }
        /// <summary>
        /// 对应合单标识2（暂不存储到数据库，仅做临时变量存储做临时判断,目前仅拼多多对应有第二个辅助合单标识，需结合对应辅助判断是否可以合单）
        /// </summary>
        [NotMapped]
        public string Oaid2 { get; set; }
        public string ExtField4 { get; set; }
        /// <summary>
        /// 1688平台：存储的代发来源outChannel，需参与合并计算
        /// 淘宝-清仓订单：存储清仓订单标识，需参与合并计算
        /// </summary>
        public string ExtField5 { get; set; }
        /// <summary>
        /// 目前仅1688有使用到，存储的淘宝OAID，需参与合并计算
        /// </summary>
        public string ExtField6 { get; set; }
        /// <summary>
        /// 目前仅1688有使用到，存储完整的1688代发订单加密信息encryptOutOrderInfo(四个字段：outPlatformOrderNo、outPlatformCode、outPlatformAppkey、oaid)，打印时需以该字段为准
        /// </summary>
        public string ExtField7 { get; set; }
        /// <summary>
        /// 收件人短地址，仅包含：省市区城镇
        /// </summary>
        public string ToShortAddress
        {
            get
            {
                var shortAddr = $"{ToProvince} {ToCity} {ToCounty}";
                if (!string.IsNullOrEmpty(ToTown))
                {
                    shortAddr += $" {ToTown}";
                }
                if (PlatformType.IsNullOrEmpty())
                    return shortAddr;
                if ((PlatformType == "Jingdong" || PlatformType == "KuaiShou"))
                {
                    if (ToProvince.IsNotNullOrEmpty() && CommUtls.ZhiXiaShiDic().ContainsKey(ToProvince))
                        shortAddr = $"{ToProvince} {ToProvince} {ToCounty}";
                }
                else if (PlatformType == "TouTiao")
                {
                    if (ToCity.IsNotNullOrEmpty() && CommUtls.DiJiShiDic().ContainsKey(ToCity))
                    {
                        var pcc = ToProvince + ToCity + ToCounty;
                        var toCounty = ToFullAddress.Replace(ToAddress, "").Replace(pcc, "");
                        shortAddr = $"{ToProvince} {ToCity} {ToCounty}";
                    }
                }
                return shortAddr;
            }
        }

        [NotMapped]
        /// <summary>
        /// 打印状态
        /// </summary>
        public int LogicOrderPrintState { get; set; }

        /// <summary>
        /// 是否为部分数量发货（和平台上状态一致），仅抖店
        /// </summary>
        public bool? IsPlatPartSend { get; set; }
        /// <summary>
        /// 收件人信息HashCode
        /// </summary>
        public string ReceiverHashCode { get; set; }

        /// <summary>
        /// 收件人电话搜索串（密文为索引串）
        /// </summary>
        public string ToPhoneIndex { get; set; }

        /// <summary>
        /// 抖店 承诺日达 -- 用户承诺送达时间/预约送达时间、 履约发货--预约发货时间
        /// </summary>
        public DateTime? ReceiptDate { get; set; }
               
        /// <summary>
        /// 发货方式
        /// </summary>
        public string ShippingType { get; set; }

        /// <summary>
        /// 交运方式需要
        /// </summary>
        public string DeliveryOptionId { get; set; }

        [NotMapped]
        /// <summary>
        /// 特殊场景需要
        /// </summary>
        public string ShippingProvider { get; set; }

        /// <summary>
        /// 履约方式
        /// </summary>
        public string FulfillmentType { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        public string ToCountry { get; set; }

        /// <summary>
        /// 交运状态
        /// </summary>
        public int LogisticStatus { get; set; }

		/// <summary>
		/// 发运类型,跨境。值：Global standard shipping（全球标准运输服务）、Standard shipping（本地标准运输服务）
		/// </summary>
		public string DeliveryOptionName { get; set; }

	}

    public enum RateStatus
    {
        /// <summary>
        /// 已评论
        /// </summary>
        Commented = 4,
        /// <summary>
        /// 未评论
        /// </summary>
        NoComment = 5,
        /// <summary>
        /// 不需要评论
        /// </summary>
        NoNeed = 6,
        /// <summary>
        /// 已过有效评论期（自加字段）
        /// </summary>
        Expired = 111
    }
}
