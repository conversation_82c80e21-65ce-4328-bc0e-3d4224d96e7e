using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Enum
{
    public enum CloudPlatformType
    {
        /// <summary>
        /// 阿里云
        /// </summary>
        [Description("阿里云")]
        Alibaba = 1,
        /// <summary>
        /// 多多云
        /// </summary>
        [Description("多多云")]
        Pinduoduo = 2,
        /// <summary>
        /// 京东云
        /// </summary>
        [Description("京东云")]
        Jingdong = 3,
        /// <summary>
        /// 抖店云
        /// </summary>
        [Description("抖店云")]
        TouTiao = 4,
        /// <summary>
        /// 跨境站点
        /// </summary>
        [Description("TiKToK")]
        ChinaAliyun = 5,
    }
}
