using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.SubAccount;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Extension
{
    /// <summary>
    /// 数据库策略
    /// </summary>
    public class DbPolicyExtension
    {
        #region 获取店铺数据库配置信息

        public static DbConfigModel GetConfig(int shopId)
        {
            var rp = new DbConfigRepository();
            return rp.GetDbConfigModel(shopId);
        }

        /// <summary>
        /// (已含新版)
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public static List<DbConfigModel> GetConfig(List<int> shopIds)
        {
            var rp = new DbConfigRepository();
            return rp.GetDbConfigModel(shopIds);
        }

        /// <summary>
        /// (已含新版)
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public static List<DbConfigModel> GetConfigFx(List<int> shopIds)
        {
            var rp = new DbConfigRepository();
            return rp.GetDbConfigModelFx(shopIds);
        }

        /// <summary>
        /// 站点，获取系统用户数据库配置信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="systemShopId"></param>
        /// <returns></returns>
        public static List<DbConfigModel> GetConfigFx(int fxUserId, int systemShopId)
        {
            var rp = new DbConfigRepository();
            return rp.GetDbConfigModelFx(fxUserId, systemShopId);
        }

        /// <summary>
        /// (已含新版)
        /// </summary>
        /// <param name="shops"></param>
        public static void InitDbConfig(List<Shop> shops)
        {
            //获取店铺数据库配置信息
            var sids = shops?.Where(s => s.DbConfig == null)?.Select(s => s.Id).ToList();
            var dbConfigs = DbPolicyExtension.GetConfig(sids);
            if (dbConfigs != null && dbConfigs.Any())
            {
                foreach (var shop in shops)
                {
                    var dbConfig = dbConfigs.FirstOrDefault(d => d?.DbConfig?.ShopId == shop.Id);
                    shop.DbConfig = dbConfig;
                }
            }
        }

        public static DbConfigModel GetDbConfigModelByDbNameId(int dbNameId)
        {
            var rp = new DbConfigRepository();
            return rp.GetDbConfigModelByDbNameId(dbNameId);
        }

        ///// <summary>
        ///// 获取店铺最佳的数据库配置，优先取对应平台的库，再取数据量少的库
        ///// </summary>
        ///// <param name="platformType">店铺平台类型</param>
        ///// <param name="targetCloudPlatformType">目标数据库平台</param>
        ///// <returns></returns>
        //public static DbNameConfig GetBestDbNameConfig(string shopPlatformType, string targetCloudPlatformType = "")
        //{
        //    var rp = new DbConfigRepository();
        //    return rp.GetBestDbNameConfig(shopPlatformType, targetCloudPlatformType);
        //}

        #endregion

        #region 针对新用户初始化店铺数据库配置

        //public static DbConfig CreateDbConfig(Shop shop, DbNameConfig dbNameConfig)
        //{
        //    var rp = new DbConfigRepository();
        //    return rp.CreateDbConfig(shop, dbNameConfig);
        //}
        ///// <summary>
        ///// 获取店铺最佳的数据库配置，优先取对应平台的库，再取数据量少的库
        ///// </summary>
        ///// <param name="shop">店铺</param>
        ///// <param name="targetCloudPlatformType">目标数据库平台</param>
        ///// <returns></returns>
        //public static DbConfig CreateDbConfig(Shop shop, string targetCloudPlatformType = "")
        //{
        //    var rp = new DbConfigRepository();
        //    var dbNameConfig = rp.GetBestDbNameConfig(shop.PlatformType, targetCloudPlatformType);
        //    return rp.CreateDbConfig(shop, dbNameConfig);
        //}
        #endregion


        //public static DataMigrateTask CreateDataMigrateTask(DataMigrateTask task)
        //{
        //    var rp = new DbConfigRepository();
        //    var t = rp.CreateDataMigrateTask(task);
        //    return t;
        //}

        //public static DataMigrateTask GetDataMigrateTaskByShopId(int shopid)
        //{
        //    var rp = new DbConfigRepository();
        //    var t = rp.GetDataMigrateTaskByShopId(shopid);
        //    return t;
        //}

        //public static void UpdateDataMigrateTask(int taskId, DateTime hopeTime)
        //{
        //    var rp = new DbConfigRepository();
        //    rp.UpdateDataMigrateTask(taskId, hopeTime);
        //}
        //public static void UpdateDataMigrateTask(List<int> taskIds, DateTime hopeTime)
        //{
        //    var rp = new DbConfigRepository();
        //    rp.UpdateDataMigrateTask(taskIds, hopeTime);
        //}

        //public static DataMigrateTask GetDataMigrateTask(int taskId)
        //{
        //    var rp = new DbConfigRepository();
        //    var t = rp.GetDataMigrateTask(taskId);
        //    return t;
        //}

        //public static int GetShopOrderCount(Shop shop)
        //{
        //    return 0;
        //    //var rp = new DbConfigRepository();
        //    //var db = new DbAccessUtility(new ApiDbConfigModel { PlatformType = shop.PlatformType, DbNameConfigId = shop.DbConfig?.DbNameConfig?.Id ?? 0, Location = shop.DbConfig?.DbServer?.Location });
        //    //return db.ExecuteScalar($"select count(1) from P_Order WITH(NOLOCK) where ShopId={shop.Id} AND CreateTime>'{DateTime.Now.AddDays(-90).ToString("yyyy-MM-dd")}'").ToInt();
        //}

        #region 判断数据库是否可以互相访问

        /// <summary>
        /// 判断数据库是否可以互相访问
        /// </summary>
        /// <param name="currentLoginShopDbConfig">当前登录店铺数据库配置信息</param>
        /// <param name="targetShopDbConfigModel">目标店铺数据库配置ID</param>
        /// <returns></returns>
        public static bool IsDbAvaliableInCurrentSite(ApiDbConfigModel currentLoginShopDbConfig, ApiDbConfigModel targetShopDbConfigModel)
        {
            //优先按Location来判断，Location没有值，则按DbNameConfigId将值补上再判断，DbNameConfigId如果为0，则将PlatformType赋值给Location
            //数据库云平台类型一样，返回true
            if (string.IsNullOrEmpty(currentLoginShopDbConfig.Location) == false && string.IsNullOrEmpty(targetShopDbConfigModel.Location) && currentLoginShopDbConfig.Location == targetShopDbConfigModel.Location)
                return true;
            //location没有值，且DbNameConfigId也没有值，则完全使用PlatformType
            var currentLocation = currentLoginShopDbConfig.Location;
            if (string.IsNullOrEmpty(currentLocation) && currentLoginShopDbConfig.DbNameConfigId <= 0)
                currentLocation = currentLoginShopDbConfig.PlatformType;
            var targetLocation = targetShopDbConfigModel.Location;
            if (string.IsNullOrEmpty(targetLocation) && targetShopDbConfigModel.DbNameConfigId <= 0)
                targetLocation = targetShopDbConfigModel.PlatformType;

            //location都有提供，只需要更加location判断
            if (string.IsNullOrEmpty(currentLocation) == false && string.IsNullOrEmpty(targetLocation) == false)
                return CustomerConfig.IsDbAvaliableInCurrentSite(currentLocation, targetLocation);
            //数据库ID一样，返回true
            if (currentLoginShopDbConfig.DbNameConfigId > 0 && targetShopDbConfigModel.DbNameConfigId > 0 && currentLoginShopDbConfig.DbNameConfigId == targetShopDbConfigModel.DbNameConfigId)
                return true;


            var needQueryDbIds = new List<int>();
            //如果location还未空，需抛出异常
            if (string.IsNullOrEmpty(currentLocation))
            {
                if (currentLoginShopDbConfig.DbNameConfigId <= 0)
                    throw new LogicException($"数据库配置ID不能为0");
                needQueryDbIds.Add(currentLoginShopDbConfig.DbNameConfigId);
            }
            if (string.IsNullOrEmpty(targetLocation))
            {
                if (targetShopDbConfigModel.DbNameConfigId <= 0)
                    throw new LogicException($"数据库配置ID不能为0");
                if (needQueryDbIds.Contains(targetShopDbConfigModel.DbNameConfigId) == false)
                    needQueryDbIds.Add(targetShopDbConfigModel.DbNameConfigId);
            }
            //参数未指定云平台，需从数据库查询数据库所处云平台
            if (needQueryDbIds.Any())
            {
                var rp = new DbConfigRepository();
                var locations = rp.GetDbNameConfigLocations(needQueryDbIds);
                if (locations == null || locations.Any() == false || locations.Count() < needQueryDbIds.Count())
                    throw new LogicException($"数据库配置不存在");
                foreach (var temp in locations)
                {
                    if (currentLoginShopDbConfig.DbNameConfigId == temp.DbNameConfigId)
                        currentLocation = temp.Location;
                    if (targetShopDbConfigModel.DbNameConfigId == temp.DbNameConfigId)
                        targetLocation = temp.Location;
                }
            }

            return currentLocation == targetLocation;
        }

        /// <summary>
        /// 判断数据库是否可以互相访问
        /// </summary>
        /// <param name="currentLoginShopDbConfig">当前登录店铺数据库配置信息</param>
        /// <param name="targetShopDbConfigModel">目标店铺数据库配置ID</param>
        /// <returns></returns>
        [Obsolete]
        public static bool IsDbAvaliableInCurrentSiteOld(ApiDbConfigModel currentLoginShopDbConfig, ApiDbConfigModel targetShopDbConfigModel)
        {
            var rp = new DbConfigRepository();
            //仅提供了平台类型
            if (currentLoginShopDbConfig.DbNameConfigId <= 0 && targetShopDbConfigModel.DbNameConfigId <= 0)
            {
                return CustomerConfig.IsDbAvaliableInCurrentSite(currentLoginShopDbConfig.PlatformType, targetShopDbConfigModel.PlatformType);
            }
            //都提供了数据库配置ID
            else if (currentLoginShopDbConfig.DbNameConfigId > 0 && targetShopDbConfigModel.DbNameConfigId > 0)
            {
                var ids = new List<int> { currentLoginShopDbConfig.DbNameConfigId, targetShopDbConfigModel.DbNameConfigId }.Distinct().ToList();
                var dbNameConfigs = rp.GetDbNameConfigLocationByIds(ids);
                if (dbNameConfigs == null || !dbNameConfigs.Any())
                    throw new LogicException($"数据库配置有误，配置ID：{ids.ToJson()}");
                if (dbNameConfigs.Distinct().Count() == 1)
                    return true;
                else
                    return false;
            }
            //仅提供了当前登录店铺的数据库配置ID
            else if (currentLoginShopDbConfig.DbNameConfigId > 0)
            {
                var currentLoginShopDbLocation = rp.GetDbNameConfigLocationByIds(new List<int> { currentLoginShopDbConfig.DbNameConfigId }).FirstOrDefault();
                if (string.IsNullOrEmpty(currentLoginShopDbLocation))
                    throw new LogicException($"数据库配置有误，配置ID：{currentLoginShopDbConfig.DbNameConfigId}");
                var isPdd = false;
                if (currentLoginShopDbLocation == PlatformType.Pinduoduo.ToString())
                    isPdd = true;
                return CustomerConfig.IsDbAvaliableInCurrentSite(currentLoginShopDbLocation, targetShopDbConfigModel.PlatformType);
            }
            //仅提供了目标店铺的数据库配置ID
            else
            {
                //当前是在多多云上
                if (CustomerConfig.CloudPlatformType == PlatformType.Pinduoduo.ToString())
                    currentLoginShopDbConfig.PlatformType = PlatformType.Pinduoduo.ToString();
                else if (CustomerConfig.CloudPlatformType == PlatformType.TouTiao.ToString())
                    currentLoginShopDbConfig.PlatformType = PlatformType.TouTiao.ToString();
                //当前平台是拼多多,但云服务器不是拼多多，说明服务器是在阿里云
                else if (currentLoginShopDbConfig.PlatformType == PlatformType.Pinduoduo.ToString())
                    currentLoginShopDbConfig.PlatformType = PlatformType.Alibaba.ToString();
                var targetShopDbLocation = rp.GetDbNameConfigLocationByIds(new List<int> { targetShopDbConfigModel.DbNameConfigId }).FirstOrDefault();
                if (string.IsNullOrEmpty(targetShopDbLocation))
                    throw new LogicException($"数据库配置有误，配置ID：{targetShopDbConfigModel.DbNameConfigId}");
                return CustomerConfig.IsDbAvaliableInCurrentSite(currentLoginShopDbConfig.PlatformType, targetShopDbLocation);
            }
        }

        public static DbApiAccessUtility GetDbUtility()
        {
            throw new NotImplementedException();
        }

        ///// <summary>
        ///// 获取所有不同的数据库信息
        ///// </summary>
        ///// <returns></returns>
        //public static List<DbNameConfig> GetAllDbNameConfigs(List<int> shopIds = null)
        //{
        //    var rp = new DbConfigRepository();
        //    return rp.GetAllDbNameConfigs(shopIds);
        //}

        /// <summary>
        /// 获取所有不同的数据库简要信息(已包含新版)
        /// </summary>
        /// <param name="shopIds"></param>
        /// <param name="extWhere"></param>
        /// <param name="fxUserId">大于0追加新版数据配置</param>
        /// <param name="cpt">新版指定云平台，默认为当前云平台，all表示所有云平台</param>
        /// <param name="paramters">参数化</param>
        /// <returns></returns>
        public static List<DbNameConfig> GetAllDbNameSimpleConfigs(List<int> shopIds = null, string extWhere = "", int fxUserId = 0, string cpt = "", DynamicParameters paramters = null)
        {
            var rp = new DbConfigRepository();
            var result = rp.GetAllDbNameSimpleConfigs(shopIds, extWhere, paramters : paramters);

            //分库新逻辑，追加数据库
            if (fxUserId > 0)
            {
                if (string.IsNullOrEmpty(cpt))
                    cpt = CustomerConfig.CloudPlatformType;
                //查询新版分库信息
                var fxDbNameConfigs = GetFxDbNameSimpleConfigs(fxUserId, cpt);
                if (fxDbNameConfigs != null)
                {
                    fxDbNameConfigs.ForEach(fxDbNameConfig =>
                    {
                        if (!result.Any(a => a.Id == fxDbNameConfig.Id))
                        {
                            result.Add(fxDbNameConfig);
                        }

                        // 覆盖为分库的配置，更新冷库的配置信息
                        if (fxDbNameConfig.FromFxDbConfig==1)
                        {
                            var config = result.Find(a => a.Id==fxDbNameConfig.Id);
                            config.ColdDbNameConfigId = fxDbNameConfig.ColdDbNameConfigId;
                            config.ColdDbStatus = fxDbNameConfig.ColdDbStatus;
                            config.FromFxDbConfig=fxDbNameConfig.FromFxDbConfig;
                        }
                    });
                }
                //else
                //{
                //    if(systemShopId > 0)
                //    {
                //        //查询旧表新库信息
                //        var newDb = new DbConfigRepository().GetNewDbConfigModel(systemShopId);
                //        if (newDb != null && !result.Any(a => a.Id == newDb.DbNameConfig.Id))
                //            result.Add(newDb.DbNameConfig);
                //    }
                //}
            }
            return result;
        }

        /// <summary>
        /// 获取所有不同的数据库简要信息
        /// </summary>
        /// <returns></returns>
        public static List<DbNameConfig> GetAllDbNameSimpleConfigs(string platformType, List<int> shopIds = null)
        {
            var rp = new DbConfigRepository();
            return rp.GetPlatformAllDbNameSimpleConfigs(platformType, shopIds);
        }

        /// <summary>
        /// 获取店铺数据库配置信息
        /// </summary>
        /// <returns></returns>
        public static List<DbConfig> GetDbConfigs(List<int> shopIds = null)
        {
            var rp = new DbConfigRepository();
            return rp.GetDbConfigs(shopIds);
        }

        public static void InitShopDbConfigs(List<Shop> shops)
        {

        }
        /// <summary>
        /// 根据FxUserId获取业务库简单信息，关联FxDbConfig
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="cpt">指定云平台,all表示所有的云平台</param>
        /// <returns></returns>
        private static List<DbNameConfig> GetFxDbNameSimpleConfigs(int fxUserId, string cpt)
        {
            var rp = new FxDbConfigRepository();
            return rp.GetFxDbNameSimpleConfigs(fxUserId, cpt);
        }
        #endregion

        ///// <summary>
        ///// 遍历指定店铺的数据库
        ///// </summary>
        ///// <param name="currentDbConfig">当前数据库配置，参照系</param>
        ///// <param name="shops">店铺</param>
        ///// <param name="action">执行动作，第二个参数为目标数据库的店铺id列表</param>
        //public static void ForeachShopDbs(ApiDbConfigModel currentDbConfig, List<Shop> shops, Action<DbAccessUtility, List<int>> action)
        //{
        //    var dbConfigs = new List<DbConfig>();
        //    dbConfigs.AddRange(shops.Where(s => s.DbConfig?.DbConfig != null).Select(s => s.DbConfig?.DbConfig));
        //    var shopIds = shops.Where(s => s.DbConfig == null).Select(s => s.Id).ToList();
        //    var temp = Data.Extension.DbPolicyExtension.GetDbConfigs(shopIds);
        //    if (temp != null)
        //        dbConfigs.AddRange(temp);
        //    var dbgroups = Data.Extension.DbPolicyExtension.GetAllDbNameSimpleConfigs(shopIds).GroupBy(d => d.DbServerConfigId + d.DbName).ToList();
        //    if (dbgroups != null && dbgroups.Any())
        //    {
        //        foreach (var dbgroup in dbgroups)
        //        {
        //            var dbNameConfig = dbgroup.FirstOrDefault();
        //            var curShopIds = dbConfigs.Where(d => d.DbNameConfigId == dbNameConfig.Id).Select(s => s.ShopId).ToList();
        //            var dbAccessUtility = new DbAccessUtility(currentDbConfig, new ApiDbConfigModel(dbNameConfig.DbPlatformType, dbNameConfig.DbPlatformType, dbNameConfig.Id));
        //            action(dbAccessUtility, curShopIds);
        //        }
        //    }
        //}

        /// <summary>
        /// 遍历所有的分库的数据库
        /// </summary>
        /// <param name="currentDbConfig">当前数据库配置，参照系</param>
        /// <param name="action">执行动作，第二个参数为目标数据库的店铺id列表</param>
        /// <param name="fxUserId">大于0追加新版数据配置</param>
        /// <param name="cpt">新版指定云平台，默认为当前云平台</param>
        public static void ForeachAllDbs(Action<DbAccessUtility> action, int fxUserId = 0, string cpt = "",string filter = "", DynamicParameters paramters = null)
        {
            var currentDbConfig = new ApiDbConfigModel { PlatformType = CustomerConfig.CloudPlatformType };
            var dbgroups = Data.Extension.DbPolicyExtension.GetAllDbNameSimpleConfigs(fxUserId: fxUserId, cpt: cpt,extWhere: filter, paramters: paramters).GroupBy(d => d.DbServerConfigId + d.DbName).ToList();
            if (dbgroups != null && dbgroups.Any())
            {
                foreach (var dbgroup in dbgroups)
                {
                    var dbNameConfig = dbgroup.FirstOrDefault();
                    var dbAccessUtility = new DbAccessUtility(currentDbConfig, new ApiDbConfigModel(dbNameConfig.DbPlatformType, dbNameConfig.DbPlatformType, dbNameConfig.Id));
                    action(dbAccessUtility);
                }
            }
        }


        /// <summary>
        /// 遍历指定平台所有的分库的数据库
        /// </summary>
        /// <param name="targetPlatformType">目标平台</param>
        /// <param name="action">执行动作，第二个参数为目标数据库的店铺id列表</param>
        public static void ForeachPlatformAllDbs(string targetPlatformType, Action<DbAccessUtility> action)
        {
            var currentDbConfig = new ApiDbConfigModel { PlatformType = CustomerConfig.CloudPlatformType };
            var dbgroups = Data.Extension.DbPolicyExtension.GetAllDbNameSimpleConfigs(targetPlatformType).GroupBy(d => d.DbServerConfigId + d.DbName).ToList();
            if (dbgroups != null && dbgroups.Any())
            {
                foreach (var dbgroup in dbgroups)
                {
                    var dbNameConfig = dbgroup.FirstOrDefault();
                    var dbAccessUtility = new DbAccessUtility(currentDbConfig, new ApiDbConfigModel(dbNameConfig.DbPlatformType, dbNameConfig.DbPlatformType, dbNameConfig.Id));
                    action(dbAccessUtility);
                }
            }
        }

        /// <summary>
        /// 并发遍历所有的分库的数据库
        /// </summary>
        /// <param name="currentDbConfig">当前数据库配置，参照系</param>
        /// <param name="action">执行动作，第二个参数为目标数据库的店铺id列表，该方法必须是线程安全的</param>
        /// <param name="fxUserId">大于0追加新版数据配置</param>
        /// <param name="cpt">新版指定云平台，默认为当前云平台</param>
        public static void ParellelForeachAllDbs(Action<DbAccessUtility> action, string filter = null, int fxUserId = 0, string cpt = "", DynamicParameters filterParamters = null)
        {
            var currentDbConfig = new ApiDbConfigModel { PlatformType = CustomerConfig.CloudPlatformType };
            var allDbNameConfigs = Data.Extension.DbPolicyExtension.GetAllDbNameSimpleConfigs(null, filter, fxUserId, cpt, paramters : filterParamters);
            var dbgroups = allDbNameConfigs?.GroupBy(d => d.DbServerConfigId + d.DbName).ToList();
            if (dbgroups != null && dbgroups.Any())
            {
                if (dbgroups != null && dbgroups.Any())
                {
                    Parallel.ForEach(dbgroups, new ParallelOptions { MaxDegreeOfParallelism = 5 }, dbgroup =>
                    {
                        var dbNameConfig = dbgroup.FirstOrDefault();
                        var dbAccessUtility = new DbAccessUtility(currentDbConfig, new ApiDbConfigModel(dbNameConfig.DbPlatformType, dbNameConfig.DbPlatformType, dbNameConfig.Id, applicationName: dbNameConfig.ApplicationName));
                        action(dbAccessUtility);
                    });

                }
            }

        }

        /// <summary>
        /// 并发遍历所有的分库的数据库(返回数据库名称)
        /// </summary>
        /// <param name="action">执行动作，第二个参数是冷库的数据库信息，第三个参数为目标数据库的店铺id列表，该方法必须是线程安全的</param>
        /// <param name="filter">过滤条件</param>
        /// <param name="fxUserId">大于0追加新版数据配置</param>
        /// <param name="cpt">新版指定云平台，默认为当前云平台</param>
        public static void ParellelForeachAllDbs(Action<DbAccessUtility, string> action, string filter = null, int fxUserId = 0, string cpt = "")
        {
            var currentDbConfig = new ApiDbConfigModel { PlatformType = CustomerConfig.CloudPlatformType };
            var allDbNameConfigs = Data.Extension.DbPolicyExtension.GetAllDbNameSimpleConfigs(null, filter, fxUserId, cpt);

            var dbgroups = allDbNameConfigs?.GroupBy(d => d.DbServerConfigId + d.DbName).ToList();
            if (dbgroups != null && dbgroups.Any())
            {
                if (dbgroups != null && dbgroups.Any())
                {
                    Parallel.ForEach(dbgroups, new ParallelOptions { MaxDegreeOfParallelism = 5 }, dbgroup =>
                    {
                        var dbNameConfig = dbgroup.FirstOrDefault();
                        var dbAccessUtility = new DbAccessUtility(currentDbConfig, new ApiDbConfigModel(dbNameConfig.DbPlatformType, dbNameConfig.DbPlatformType, dbNameConfig.Id));
                        action(dbAccessUtility, dbNameConfig.DbName);
                    });

                }
            }

        }

        /// <summary>
        /// 并发遍历所有的分库的数据库(返回数据库名称)
        /// </summary>
        /// <param name="action">执行动作，第二个参数是冷库的数据库信息，第三个参数为目标数据库的店铺id列表，该方法必须是线程安全的</param>
        /// <param name="filter">过滤条件</param>
        /// <param name="fxUserId">大于0追加新版数据配置</param>
        /// <param name="cpt">新版指定云平台，默认为当前云平台</param>
        public static void ParellelForeachAllDbsWithCold(Action<DbAccessUtility, DbAccessUtility, string> action, string filter = null, int fxUserId = 0, string cpt = "")
        {
            var currentDbConfig = new ApiDbConfigModel { PlatformType = CustomerConfig.CloudPlatformType };
            var allDbNameConfigs = Data.Extension.DbPolicyExtension.GetAllDbNameSimpleConfigs(null, filter, fxUserId, cpt);

            var dbgroups = allDbNameConfigs?.GroupBy(d => d.DbServerConfigId + d.DbName).ToList();
            if (dbgroups != null && dbgroups.Any())
            {
                if (dbgroups != null && dbgroups.Any())
                {
                    Parallel.ForEach(dbgroups, new ParallelOptions { MaxDegreeOfParallelism = 5 }, dbgroup =>
                    {
                        var dbNameConfig = dbgroup.FirstOrDefault();
                        var dbAccessUtility = new DbAccessUtility(currentDbConfig, new ApiDbConfigModel(dbNameConfig.DbPlatformType, dbNameConfig.DbPlatformType, dbNameConfig.Id));
                        // 当开启了冷库，并且有冷库配置的情况下，才创建冷库连接
                        DbAccessUtility coldDbAccessUtility = null;
                        var coldConfig = dbgroup.FirstOrDefault(c => c.ColdDbStatus>=2&&c.FromFxDbConfig==1&&c.ColdDbNameConfigId>0);
                        if (coldConfig!=null)
                        {
                            coldDbAccessUtility = new DbAccessUtility(currentDbConfig, new ApiDbConfigModel(dbNameConfig.DbPlatformType, dbNameConfig.DbPlatformType, coldConfig.ColdDbNameConfigId.Value));
                        }
                        action(dbAccessUtility, coldDbAccessUtility, dbNameConfig.DbName);
                    });

                }
            }

        }

        #region 并发访问 指定店铺所在的库
        /// <summary>
        /// 并发遍历所有的分库的数据库
        /// </summary>
        /// <param name="action">执行动作，第二个参数为目标数据库的店铺id列表，该方法必须是线程安全的</param>
        /// <param name="IsStop">并发终止条件</param>
        /// <param name="shopIds">并发哪些店铺所在的库</param>
        /// <param name="fxUserId">大于0追加新版数据配置</param>
        /// <param name="cpt">新版指定云平台，默认为当前云平台</param>
        public static void ParellelForeachAllDbs(Action<DbAccessUtility> action, Func<bool> IsStop, List<int> shopIds, int fxUserId = 0, string cpt = "", List<int> appendFxDbNameIds = null)
        {
            var currentDbConfig = new ApiDbConfigModel { PlatformType = CustomerConfig.CloudPlatformType };
            var filter = GetShopDbById(shopIds, appendFxDbNameIds);

            var allDbNameConfigs = Data.Extension.DbPolicyExtension.GetAllDbNameSimpleConfigs(null, filter, fxUserId, cpt);
            var dbgroups = allDbNameConfigs?.GroupBy(d => d.DbServerConfigId + d.DbName).ToList();
            if (dbgroups != null && dbgroups.Any())
            {
                Parallel.ForEach(dbgroups, new ParallelOptions { MaxDegreeOfParallelism = 10 }, (dbgroup, state) =>
                {
                    if (IsStop())
                    {
                        state.Stop();
                    }

                    var dbNameConfig = dbgroup.FirstOrDefault();
                    var dbAccessUtility = new DbAccessUtility(currentDbConfig, new ApiDbConfigModel(dbNameConfig.DbPlatformType, dbNameConfig.DbPlatformType, dbNameConfig.Id, applicationName: dbNameConfig.ApplicationName));
                    action(dbAccessUtility);

                    if (IsStop())
                    {
                        state.Stop();
                    }
                });

            }
        }

        /// <summary>
        /// 并发遍历所有的分库的数据库
        /// </summary>
        /// <param name="action">执行动作，第二个参数为目标数据库的店铺id列表，该方法必须是线程安全的</param>
        /// <param name="IsStop">并发终止条件</param>
        /// <param name="shopIds">并发哪些店铺所在的库</param>
        /// <param name="fxUserId">大于0追加新版数据配置</param>
        public static void ParellelForeachAllDbs(Action<DbAccessUtility> action, List<int> shopIds, int fxUserId = 0)
        {
            var currentDbConfig = new ApiDbConfigModel { PlatformType = CustomerConfig.CloudPlatformType };

            var filter = GetShopDbById(shopIds);
            var allDbNameConfigs = Data.Extension.DbPolicyExtension.GetAllDbNameSimpleConfigs(null, filter, fxUserId);
            var dbgroups = allDbNameConfigs?.GroupBy(d => d.DbServerConfigId + d.DbName).ToList();
            if (dbgroups != null && dbgroups.Any())
            {
                Parallel.ForEach(dbgroups, new ParallelOptions { MaxDegreeOfParallelism = 10 }, (dbgroup, state) =>
                {
                    var dbNameConfig = dbgroup.FirstOrDefault();
                    var dbAccessUtility = new DbAccessUtility(currentDbConfig, new ApiDbConfigModel(dbNameConfig.DbPlatformType, dbNameConfig.DbPlatformType, dbNameConfig.Id));
                    action(dbAccessUtility);
                });

            }
        }

        private static string GetShopDbById(List<int> shopIds, List<int> appendFxDbNameIds = null)
        {
            var dbNameConfigIds = new List<int>();
            string filter = string.Empty;
            if (shopIds != null && shopIds.Any())
            {
                var isContainsJdShop = true;
                var db = DbUtility.GetConfigureConnection();
                var shopInfoSql = $@"select Id,PlatformType from P_Shop t1 WITH(NOLOCK) where t1.Id IN @shopIds";
                var shopInfoSqlParameters = new DynamicParameters();
                shopInfoSqlParameters.Add("@shopIds", shopIds);
                var shopInfos = db.Query<Shop>(shopInfoSql, shopInfoSqlParameters).ToList();

                //var shopInfos = db.Query<Shop>($"select Id,PlatformType from P_Shop t1 WITH(NOLOCK) where t1.Id IN({string.Join(",", shopIds)})").ToList();
                var pddShopIds = shopInfos.Where(x => x.PlatformType == PlatformType.Pinduoduo.ToString()).Select(x => x.Id).ToList();
                isContainsJdShop = shopInfos.Any(x => x.PlatformType == PlatformType.Jingdong.ToString() || x.PlatformType == PlatformType.JingdongPurchase.ToString());
                var notPddShopIds = shopInfos.Where(x => x.PlatformType != PlatformType.Pinduoduo.ToString()).Select(x => x.Id).ToList();

                //由于数据库配置，两边配置库不一样，需要以各自云平台的为准
                //拼多多店铺查询拼多多的配置库
                if (pddShopIds != null && pddShopIds.Any())
                {
                    var pddSql = $@"SELECT DISTINCT DbNameConfigId FROM dbo.P_DbConfig WITH(NOLOCK) WHERE ShopId IN @pddShopIds";
                    var pddSqlParameters = new DynamicParameters();
                    pddSqlParameters.Add("@pddShopIds", pddShopIds);

                    //var pddSql = $"SELECT DISTINCT DbNameConfigId FROM dbo.P_DbConfig WITH(NOLOCK) WHERE ShopId IN ({string.Join(",", pddShopIds)})";
                    //var pddDbConfigIds = db.Query<int>(pddSql);
                    //dbNameConfigIds.AddRange(pddDbConfigIds);

                    //var dbApi = DbApiAccessUtility.GetPddConfigureDb();
                    //pddDbConfigIds = dbApi.Query<int>(pddSql);
                    //dbNameConfigIds.AddRange(pddDbConfigIds);

                    if (CustomerConfig.CloudPlatformType == PlatformType.Pinduoduo.ToString())
                    {
                        var pddDbConfigIds = db.Query<int>(pddSql, pddSqlParameters);
                        dbNameConfigIds.AddRange(pddDbConfigIds);
                    }
                    else
                    {
                        var dbApi = DbApiAccessUtility.GetPddConfigureDb();
                        var pddDbConfigIds = dbApi.Query<int>(pddSql, pddSqlParameters);
                        dbNameConfigIds.AddRange(pddDbConfigIds);
                    }
                }
                //非拼多多店铺查询阿里的配置库
                if (notPddShopIds != null && notPddShopIds.Any())
                {

                    var pddSql = $@"SELECT DISTINCT DbNameConfigId FROM dbo.P_DbConfig WITH(NOLOCK) WHERE ShopId IN @notPddShopIds";
                    var pddSqlParameters = new DynamicParameters();
                    pddSqlParameters.Add("@notPddShopIds", notPddShopIds);

                    //var pddSql = $"SELECT DISTINCT DbNameConfigId FROM dbo.P_DbConfig WITH(NOLOCK) WHERE ShopId IN ({string.Join(",", notPddShopIds)})";
                    //var pddDbConfigIds = db.Query<int>(pddSql);
                    //dbNameConfigIds.AddRange(pddDbConfigIds);

                    //var dbApi = DbApiAccessUtility.GetConfigureDb();
                    //pddDbConfigIds = dbApi.Query<int>(pddSql);
                    //dbNameConfigIds.AddRange(pddDbConfigIds);
                    if (CustomerConfig.CloudPlatformType != PlatformType.Pinduoduo.ToString())
                    {
                        var pddDbConfigIds = db.Query<int>(pddSql, pddSqlParameters);
                        dbNameConfigIds.AddRange(pddDbConfigIds);
                    }
                    else
                    {
                        var dbApi = DbApiAccessUtility.GetConfigureDb();
                        var pddDbConfigIds = dbApi.Query<int>(pddSql, pddSqlParameters);
                        dbNameConfigIds.AddRange(pddDbConfigIds);
                    }
                }

                if (appendFxDbNameIds != null && appendFxDbNameIds.Any())
                    dbNameConfigIds.AddRange(appendFxDbNameIds);
                dbNameConfigIds = dbNameConfigIds.Distinct().ToList();

                if (isContainsJdShop)
                {
                    if (dbNameConfigIds.Any())
                        filter = $" AND (dn.Id IN({string.Join(",", dbNameConfigIds)}) OR ds.Location='{PlatformType.Jingdong}' )";
                    else
                        filter = $" AND ds.Location='{PlatformType.Jingdong}' ";
                }
                else
                {
                    if (dbNameConfigIds.Any())
                        filter = $" AND dn.Id IN({string.Join(",", dbNameConfigIds)}) ";
                    else
                        filter = "";
                }
            }

            return filter;
        }
        #endregion

        #region 分库相关


        /// <summary>
        /// 分库扩展.查询某一用户业务数据分布的库
        /// </summary>
        /// <param name="userid"></param>
        /// <param name="dbs"></param>
        /// <returns></returns>
        public static List<DbConfigModel> GetConfigFx(int userid, List<DbConfigModel> dbs)
        {
            var rp = new DbConfigRepository();
            //var result = rp.GetConfigFx(userid, dbs);
            var result = FxCaching.GetCache(FxCachingType.DbConfigModelPathFlowNode, userid.ToString(), () => rp.GetConfigFx(userid, dbs));
            //Log.Debug($"当前用户ID：{userid},当前云平台：{CustomerConfig.CloudPlatformType},所有业务库配置信息(计算订单数)：{result.ToJson(true)}",
            //    $"InitDbConfigsFx_{DateTime.Now:yyyy-MM-dd}.log");
            //查询新版分库信息
            var fxdb = rp.GetFxDbConfigModel(userid, CustomerConfig.CloudPlatformType);
            if (fxdb != null && !result.Any(a => a.DbNameConfig.Id == fxdb.DbNameConfig.Id))
                result.Add(fxdb);
            //Log.Debug($"当前用户ID：{userid},当前云平台：{CustomerConfig.CloudPlatformType},所有业务库配置信息(附加分库)：{result.ToJson(true)}",
            //    $"InitDbConfigsFx_{DateTime.Now:yyyy-MM-dd}.log");
            //if(systemShopId > 0 && fxdb == null)
            //{
            //    //查询旧表新库信息
            //    var newDb = new DbConfigRepository().GetNewDbConfigModel(systemShopId);
            //    if (newDb != null && !result.Any(a => a.DbNameConfig.Id == newDb.DbNameConfig.Id))
            //        result.Add(newDb);
            //}

            return result;
        }

        /// <summary>
        /// 获取厂家数据库配置信息
        /// </summary>
        /// <param name="supplierFxUserIds"></param>
        /// <param name="dbs"></param>
        /// <param name="isAppendFxDbConfig"></param>
        /// <returns></returns>
        public static List<DbConfigModel> GetConfigFxBySupplier(List<int> supplierFxUserIds, List<DbConfigModel> dbs,
            bool isAppendFxDbConfig = false)
        {
            var rp = new DbConfigRepository();
            //添加精选分库
            if (isAppendFxDbConfig)
            {
                // var fxDbConfigs = rp.GetFxDbConfigFxUserIds(supplierFxUserIds,
                //     new List<string> { CloudPlatformType.Alibaba.ToString() });
                var fxDbConfigs =
                    rp.GetFxDbConfigModelsByFxUserIdsAndCloudPlatformWithCache(supplierFxUserIds,
                        CloudPlatformType.Alibaba.ToString());
                fxDbConfigs.ForEach(dbConfig =>
                {
                    if (dbConfig != null && dbs.All(a => a.DbNameConfig.Id != dbConfig.DbNameConfig.Id))
                    {
                        dbs.Add(dbConfig);
                    }
                });
            }

            Log.Debug(() => $"1688货源区域信息，{dbs.ToJson(true)}", $"GetConfigFxBySupplier_{DateTime.Now:yyyy-MM-dd}.log");
            var result = rp.GetConfigFxBySupplier(supplierFxUserIds, dbs);
            return result;
        }

        /// <summary>
        /// 获取用户的配置分库
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        public static DbConfigModel GetConfigFxUserId(int userid)
        {
            var rp = new DbConfigRepository();
            return rp.GetConfigFxUserId(userid);
        }

        ///// <summary>
        ///// 修改用户的分库配置和商家一致
        ///// </summary>
        ///// <param name="userid">用户</param>
        ///// <param name="agentid">商家</param>
        ///// <returns></returns>
        //public static bool ChangeUserDb(int userid, int agentid)
        //{
        //    var rp = new DbConfigRepository();
        //    return rp.ChangeUserDb(userid, agentid);
        //}

        /// <summary>
        /// 查找用户所有商家(分销商)的分库情况
        /// 返回库名和商家id
        /// </summary>
        /// <param name="supplierids">商家id</param>
        /// <param name="dbs"></param>
        /// <returns></returns>
        public static Dictionary<string, List<int>> GetConfigFxSupplier(int fxuserid, List<int> supplierids)
        {
            var rp = new DbConfigRepository();
            return rp.GetConfigFxSupplierIds(fxuserid, supplierids);
        }


        /// <summary>
        /// 查找用户所有商家(分销商)的分库情况
        /// 返回库名和商家名称
        /// </summary>
        /// <param name="supplierids">商家id</param>
        /// <returns>Item1:按库名分组；Item2:按云平台分组</returns>
        public static Tuple<Dictionary<string, List<SupplierUserNameModel>>, Dictionary<string, List<SupplierUserNameModel>>> GetConfigFxSupplierNames(int supplierid, bool useCache = true)
        {
            Tuple<Dictionary<string, List<SupplierUserNameModel>>, Dictionary<string, List<SupplierUserNameModel>>> result = null;

            //Func<Tuple<Dictionary<string, List<SupplierUserNameModel>>, Dictionary<string, List<SupplierUserNameModel>>>> funGetFxSupplierNames =
            //    () => { return new DbConfigRepository().GetConfigFxSupplierNames(supplierid); };

            Func<Tuple<Dictionary<string, List<SupplierUserNameModel>>, Dictionary<string, List<SupplierUserNameModel>>>> funGetFxSupplierNamesNew =
               () => { return new DbConfigRepository().GetConfigFxSupplierNamesNew(supplierid); };

            if (useCache)
            {
                // result = FxCaching.GetCache(FxCachingType.FxSupplierNames, supplierid.ToString(), funGetFxSupplierNames);
                result = FxCaching.GetCache(FxCachingType.FxSupplierNames, supplierid.ToString(), funGetFxSupplierNamesNew);
            }
            else
            {
                //result = funGetFxSupplierNames();

                //if (CustomerConfig.IsDebug)
                //{
                //    Log.WriteLine($"GetConfigFxSupplierNames优化前结果：{result.ToJson()}", "GetConfigFxSupplierNames.txt");
                //}

                result = funGetFxSupplierNamesNew();

                if (CustomerConfig.IsDebug)
                {
                    Log.WriteLine($"GetConfigFxSupplierNames优化后结果：{result.ToJson()}", "GetConfigFxSupplierNames.txt");
                }
            }
            // 子账号判断权限
            if (BaseSiteContext.CurrentNoThrow.SubFxUserId > 0 && !BaseSiteContext.CurrentNoThrow.PermissionTags.Contains(FxPermission.AgentAccount.ToString()))
            {
                result.Item1.SelectMany(d => d.Value).ToList().ForEach(v => { v.Mobile = EncryptUtil.EncryptAccount(v.Mobile); });
                result.Item2.SelectMany(d => d.Value).ToList().ForEach(v => { v.Mobile = EncryptUtil.EncryptAccount(v.Mobile); });
            }

            return result;
            //bool isUseRedis = !string.IsNullOrEmpty(CustomerConfig.ConfigRedisConnectionString);

            //var cacheMinutes = 30;
            //var cacheKey = $"FX-FxSupplierNames-{supplierid}";
            //if (useCache)
            //{
            //    if(isUseRedis && RedisHelper.Exists(cacheKey)) 
            //        result = RedisHelper.Get(cacheKey).ToObject<Tuple<Dictionary<string, List<SupplierUserNameModel>>, Dictionary<string, List<SupplierUserNameModel>>>>();
            //    else if(!isUseRedis && HttpRuntime.Cache[cacheKey] != null)
            //        result =  HttpRuntime.Cache[cacheKey] as Tuple<Dictionary<string, List<SupplierUserNameModel>>, Dictionary<string, List<SupplierUserNameModel>>>;
            //}
            //if(result == null)
            //{
            //    var rp = new DbConfigRepository();
            //    result = rp.GetConfigFxSupplierNames(supplierid);

            //    if (result != null)
            //    {
            //        if (isUseRedis)
            //            RedisHelper.Set(cacheKey, result.ToJson(), cacheMinutes * 60);
            //        else
            //            HttpRuntime.Cache.Insert(cacheKey, result, null, DateTime.Now.AddMinutes(cacheMinutes), System.Web.Caching.Cache.NoSlidingExpiration, System.Web.Caching.CacheItemPriority.Default, null);
            //    }

            //}

        }

        public static Dictionary<string, List<int>> GetConfigFxSuppliers(int fxuserid, List<int> userids, List<DbConfigModel> dbs)
        {
            var rp = new DbConfigRepository();
            return rp.GetConfigFxSupplier(fxuserid, userids, dbs);
        }

        #endregion

    }

}

