using Dapper;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Data.Entity
{

    public partial class LogicOrder
    {
        public LogicOrder()
        {
            LogicOrderItems = new List<LogicOrderItem>();
            SubOrders = new List<LogicSubOrderExtension>();
        }
        [NotMapped]
        public DateTime? PublishTime { get; set; }
        [NotMapped]
        public string ShopName { get; set; }
        [NotMapped]
        public string AgentMobile { get; set; }
        [NotMapped]
        public string AgentName { get; set; }
        [NotMapped]
        public string SupplierName { get; set; }
        [NotMapped]
        public int UpFxUserId { get; set; }
        [NotMapped]
        public int DownFxUserId { get; set; }
        [NotMapped]
        public string ExpressWayBillCode { get; set; }
        [NotMapped]
        public string SelfShopName { get; set; }

        /// <summary>
        /// 实收款：当前组订单SUM(LogicOrder.TotalAmout)
        /// </summary>
        [NotMapped]
        public decimal? SumTotalAmount { get; set; }
        /// <summary>
        /// 商品件数：当前组订单SUM(LogicOrder.ProductItemCount)
        /// </summary>
        [NotMapped]
        public int SumProductItemCount { get; set; }
        /// <summary>
        /// 拆分包裹数：当前组LogicOrder的个数
        /// </summary>
        [NotMapped]
        public int SumPackageCount { get; set; }

        [NotMapped]
        public string Date { get; set; }
        [NotMapped]
        public string Time { get; set; }
        [NotMapped]
        public DateTime? OrderTime { get; set; }
        //[NotMapped]
        //public DateTime? LastShipTime { get; set; }
        [NotMapped]
        public string BuyerWangWang { get; set; }
        [NotMapped]
        public int MergerOrderCount { get; set; }

        /// <summary>
        /// 固话
        /// </summary>
        [NotMapped]
        public string ToGPhone { get; set; }
        /// <summary>
        /// 异常状态操作
        /// </summary>
        [NotMapped]
        public ExceptionRefundOpt ExceptionOpt { get; set; }
        /// <summary>
        /// 打印过的快递单号
        /// </summary>
        [NotMapped]
        public List<WaybillCodeViewModel> WaybillCodes { get; set; }
        [JsonIgnore]
        public List<LogicOrderItem> LogicOrderItems { get; set; }
        [NotMapped]
        public List<LogicSubOrderExtension> SubOrders { get; set; }
        [NotMapped]
        public string ToAddressNotHasTown { get; set; }
        [NotMapped]
        public string CustomerOrderId { get; set; }
        [NotMapped]
        public bool IsShowSalePrice { get; set; }

        /// <summary>
        /// 是否显示商品标题
        /// </summary>
        [NotMapped]
        public bool IsShowProductTitle { get; set; } = true;

        /// <summary>
        /// 是否显示商品图片
        /// </summary>
        [NotMapped]
        public bool IsShowProductImg { get; set; } = true;

        /// <summary>
        /// 商家店铺名
        /// </summary>
        [NotMapped]
        public string AgentShopName { get; set; }

        /// <summary>
        /// 是否已解密（判断是收件人本身有特殊字符还是未解密）
        /// </summary>
        [NotMapped]
        public bool IsDecrypted { get; set; }
        /// <summary>
        /// 1:店铺过期，2：额度上限，3：API接口错误，4：系统错误
        /// </summary>
        [NotMapped]
        public int DecryptErrorType { get; set; }
        [NotMapped]

        public string DecryptErrorMsg { get; set; }

        [NotMapped]
        public bool IsPartSend { get; set; }

        private List<OrderTags> _Tags;
        /// <summary>
        /// 订单标签
        /// </summary>
        public List<OrderTags> Tags
        {
            get
            {
                if ((_Tags == null || _Tags.Any() == false) && LogicOrderItems != null && LogicOrderItems.Any())
                {
                    _Tags = LogicOrderItems.Where(oi => (oi.Tags != null && oi.Tags.Any())).SelectMany(f => f.Tags).Distinct(new OrderTagComparer()).ToList();
                }
                return _Tags;
            }
        }

        /// <summary>
        /// 服务承诺：是否指定快递
        /// </summary>
        [NotMapped]
        public bool IsAppointExpress
        {
            get
            {
                var v = OrderPromises?.Any(a => a.PromiseType == 1) ?? false;
                if (!v)
                {
                    v = SubOrders?.Where(a => a.OrderPromises != null)?.SelectMany(b => b.OrderPromises)?.Any(a => a.PromiseType == 1) ?? false;
                }
                return v;
            }
        }
        /// <summary>
        /// 服务承诺：是否优先发货
        /// </summary>
        [NotMapped]
        public bool IsFirstDelivery
        {
            get
            {
                var v = OrderPromises?.Any(a => a.PromiseType == 2) ?? false;
                if (!v)
                {
                    v = SubOrders?.Where(a => a.OrderPromises != null)?.SelectMany(b => b.OrderPromises)?.Any(a => a.PromiseType == 2) ?? false;
                    //if (!v)
                    //{
                    //    //催发货
                    //    v = OrderTags?.Any(a => a.Tag == OrderTag.remind_shipment.ToString()) ?? false;
                    //}
                }
                return v;
            }
        }
        /// <summary>
        /// 服务承诺列表
        /// </summary>
        [NotMapped]
        public List<OrderPromise> OrderPromises { get; set; }

        /// <summary>
        /// 拼多多集运标签,0-中国香港集运、1-中国新疆中转、2-哈萨克斯坦集运、3-中国西藏中转、5-日本集运、6-中国台湾集运、7-韩国集运、8-新加坡集运、9-马来西亚集运、10-泰国集运
        /// 11-越南集运、12-吉尔吉斯斯坦集运、13-乌兹别克斯坦集运、14-中国甘肃中转、15-中国内蒙古中转、16-中国宁夏中转、17-中国青海中转：18-中国澳门集运、19-柬埔寨集运、20-老挝集运
        /// 21-塔吉克斯坦集运、22-亚美尼亚集运、23-格鲁吉亚集运、24-蒙古集运
        /// </summary>
        [NotMapped]
        public string PddConsolidate { get; set; }
        /// <summary>
        /// 拼多多暂停发货,1:暂停,0:不暂停
        /// </summary>
        [NotMapped]
        public string PddShipHold { get; set; }

        /// <summary>
        /// 是否为拼多多跨境单
        /// </summary>
        [NotMapped]
        public bool IsPddCrossBorderOrder
        {
            get
            {
                var v = (PlatformType == "Pinduoduo" && ExtField1 == "2");
                return v;
            }
        }
        /// <summary>
        /// 拼多多跨境单：快递上门揽收
        /// </summary>
        [NotMapped]
        public bool IsPddCourierDoorToDoorCollect { get; set; }
        /// <summary>
        /// 变更前的路径流
        /// </summary>
        [NotMapped]
        public string OldPathFlowCode { get; set; }
        /// <summary>
        /// 变更前的路径流
        /// </summary>
        [NotMapped]
        public List<string> OldPathFlowCodeList { get; set; }


        /// <summary>
        /// 2013-01-06修改，查看到LogicOrder表的ordercode，【合并订单】对应的PlatformOrderId，对不上。这里临时赋值后打印时赋值
        /// </summary>
        [NotMapped]
        public string PlatformOrderIdByMerge { get; set; }

        /// <summary>
        /// 数据库订单原始收件人姓名（防止脱敏数据覆盖ToName无法更新）
        /// </summary>
        [NotMapped]
        public string ToOrderName { get; set; }
        /// <summary>
        /// 数据库订单原始收件人电话（防止脱敏数据覆盖ToName无法更新）
        /// </summary>
        [NotMapped]
        public string ToOrderPhone { get; set; }
        /// <summary>
        /// 数据库订单原始收件人地址（防止脱敏数据覆盖ToName无法更新）
        /// </summary>
        [NotMapped]
        public string ToOrderAddress { get; set; }
        /// <summary>
        /// 收件人是否变化了
        /// </summary>
        [NotMapped]
        public bool ReceiverIsChange { get; set; }

        private List<OrderTags> _OrderTags;
        /// <summary>
        /// 标签列表
        /// </summary>
        public List<OrderTags> OrderTags
        {
            get
            {
                if (Tags != null)
                {
                    _OrderTags = _OrderTags ?? new List<OrderTags>();
                    Tags.ForEach(t =>
                    {
                        if (_OrderTags.Any(ot => (ot.HashCode == t.HashCode) || (ot.OiCode == t.OiCode && ot.TagType == t.TagType && ot.UniqueKey == t.UniqueKey)) == false)
                            _OrderTags.Add(t);
                    });
                }
                return _OrderTags;
            }
            set
            {
                _OrderTags = value;
            }
        }

        /// <summary>
        /// 是否为加密平台
        /// </summary>
        [NotMapped]
        public bool IsEncryptPlatform
        {
            get
            {
                var encryptPlatformTypes = CustomerConfig.EncryptPlatformTypes();
                return encryptPlatformTypes.Contains(PlatformType.ToString2());
            }
        }

        /// <summary>
        /// 列表查询-实际系统订单数量
        /// </summary>
        [NotMapped]
        public int RealOrderCount { get; set; }
        /// <summary>
        /// 列表查询-平台订单数量
        /// </summary>
        [NotMapped]
        public int PlatfromOrderCount { get; set; }
        /// <summary>
        /// 列表查询-列表显示合单订单数量
        /// </summary>
        [NotMapped]
        public int MergeOrderCount { get; set; }

        [NotMapped]
        public bool IsPurchasePayed
        {
            get; set;
        }

        /// <summary>
        /// 采购单情况：0不需要，1已下单，-1待下单
        /// </summary>
        [NotMapped]
        public int NeedPurchase
        {
            get; set;
        }

        /// <summary>
        /// 采购单状态;waitcreate待创建，waitpay待支付，waitsellersend待发货，waitbuyerreceive待收货，close交易关闭，success交易完成，error，close_waitcreate关闭待下单
        /// </summary>
        [NotMapped]
        public string PurchaseStatus
        {
            get; set;
        }


        [NotMapped]
        public string ToNameMask { get; set; }
        [NotMapped]
        public string ToPhoneMask { get; set; }
        [NotMapped]
        public string ToAddressMask { get; set; }

        /// <summary>
        /// 1688采购单状态，waitcreate待创建(这个状态暂时不会有，后面自动下采购单可能会用上)，waitpay待支付，waitsellersend待发货，waitbuyerreceive待收货，    close交易关闭，   success交易完成，   error下采购单错误,changeprice_error改价失败
        /// </summary>
        [NotMapped]
        public string PurchaseOrderStatus { get; set; }

        ///// <summary>
        ///// 表OrderCheck的状态, AlibabaSupplierOrder为1688采购
        ///// </summary>
        //[NotMapped]
        //public string CheckType { get; set; }

        /// <summary>
        /// AliFxPurchase=预付单
        /// </summary>
        [NotMapped]
        public string OrderType { get; set; }

        /// <summary>
        /// 是否时冷数据
        /// </summary>
        private int? _IsCold;
        /// <summary>
        /// 2024-01-04 zouyi 冷热分离
        /// 1：标识冷数据，0：热数据
        /// </summary>
        [NotMapped]
        public int IsCold
        {
            get
            {
                if (_IsCold == null)
                {
                    try
                    {
                        _IsCold = (CustomerConfig.HotOrderStatus.Contains(ErpState) == false &&
                                   BaseSiteContext.CurrentNoThrow?.CurrentDbConfig?.IsUseColdDb == true)
                            ? 1
                            : 0;
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"计算订单数据冷热状态失败：{ex.Message}");
                    }
                }

                return _IsCold ?? 0;
            }
        }
        /// <summary>
        /// 采购单运费
        /// 为空有特殊处理
        /// 用处1：赋值下采购单使用自定义运费模板计算所得的运费
        /// 用处2：赋值发货时对应的采购单运费
        /// </summary>
        [NotMapped]
        public decimal? PurchaseShippingFee { get; set; }
        /// 是否需要打印：0=无需打印，源自PurchaseOrderDeliveryMode
        /// </summary>
        [NotMapped]
        public int? IsNeedPrint { get; set; }

        /// <summary>
        /// 采购关联的源商家Id
        /// </summary>
        [NotMapped]
        public int PurchaseSourceFxUserId { get; set; }
        /// <summary>
        /// 采购关联的源商家账号
        /// </summary>
        [NotMapped]
        public string PurchaseSourceAgentMobile { get; set; }
        /// <summary>
        /// 发货方式
        /// </summary>
        [NotMapped]
        public PurchaseOrderDeliveryMode PurchaseOrderDeliveryMode { get; set; }

        /// <summary>
        /// 关联的采购单
        /// </summary>
        [NotMapped]
        public PurchaseOrderShowModel PurchaseOrderRelation { get; set; }
        /// <summary>
        /// 旗帜标签
        /// taobao：从备注中截取
        /// </summary>
        [NotMapped]
        public string SellerRemarkFlagTag { get; set; }

        /// <summary>
        /// 是否线下单
        /// </summary>
        [NotMapped]
        public bool IsOfflineOrder { get; set; }

        /// <summary>
        /// 是否线下单(无商品)
        /// </summary>
        [NotMapped]
        public bool IsOfflineNoSku { get; set; }

        /// <summary>
        /// 币种
        /// 依据订单上的国家返回响应的币种
        /// </summary>
        [NotMapped]
        public string Currency
        {
            get
            {
                if (string.IsNullOrWhiteSpace(ToCountry))
                    return "";
                return CommUtls.GetCountryCurrencyDic(ToCountry);
            }
        }
        [NotMapped]
        public List<string> ChildCustomerOrderIds
        {
            get
            {
                return LogicOrderId?.StartsWith("C") == false
                    ? new List<string>()
                    : SubOrders?.Select(m => m.PlatformOrderId).Distinct().ToList();
            }
        }

        [NotMapped]
        public List<string> ChildLogicOrderIds
        {
            get
            {
                return LogicOrderId?.StartsWith("C") == false
                    ? new List<string>()
                    : SubOrders?.Select(m => m.LogicOrderId).Distinct().ToList();
            }
        }

        /// <summary>
        /// 店铺状态 P_FxUserShop.Status
        /// </summary>
        [NotMapped]
        public int ShopStatus { get; set; }
        /// <summary>
        /// 是否允许非自营厂家使用卖家备注
        /// </summary>
        [NotMapped]
        public bool IsEditSellerRemark { get; set; }

        /// <summary>
        /// 添加备注模式
        /// </summary>
        [NotMapped]
        public int AddSellerNoteMode { get; set; }

        /// <summary>
        /// 留言凭证
        /// </summary>
        public List<PrintscreenPic> PrintscreenPics { get; set; }
        /// <summary>
        /// 快递公司Id
        /// </summary>
        [NotMapped]
        public int ExpressCompanyId { get; set; }

        /// <summary>
        /// 获取标签
        /// </summary>
        /// <param name="orderTag"></param>
        /// <returns></returns>
        public OrderTags GetOrderTag(OrderTag orderTag)
        {
            return OrderTags?.Find(item => item.Tag == orderTag.ToString());
        }

        /// <summary>
        /// 获取预约发货时间（京东）
        /// </summary>
        /// <returns></returns>
        public DateTime? GetPublishTime()
        {
            if (PlatformType.IsNotNullOrEmpty() && !string.Equals(PlatformType, Enum.PlatformType.Jingdong.ToString(), StringComparison.OrdinalIgnoreCase))
            {
                return null;
            }
            if (PublishTime.HasValue)
            {
                return PublishTime;
            }

            var val = GetOrderTag(OrderTag.appointment_ship_time)?.TagValue;
            if (val.IsNullOrEmpty())
            {
                return null;
            }
            DateTime result;
            DateTime.TryParse(val, result: out result);
            return result;
        }

        /// <summary>
        /// 多单号取号时索引
        /// </summary>
        [NotMapped]
        public int ManyCodeSendIndex { get; set; }

        /// <summary>
        /// 是否推到虚拟厂家
        /// </summary>
        [NotMapped]
        public bool IsVirtualSupplier { get; set; }
    }

    public partial class LogicOrderItem
    {
        [NotMapped]
        public string ProductSubject { get; set; }
        [NotMapped]
        public string productCargoNumber { get; set; }
        [NotMapped]
        public string CargoNumber { get; set; }
        [NotMapped]
        public string ProductImgUrl { get; set; }
        [NotMapped]
        public string ProductID { get; set; }
        [NotMapped]
        public string Color { get; set; }
        [NotMapped]
        public string Size { get; set; }
        [NotMapped]
        public decimal? Price { get; set; }
        [NotMapped]
        public int Count { get; set; }
        [NotMapped]
        public string Status { get; set; }
        [NotMapped]
        public string RefundStatus { get; set; }
        [NotMapped]
        public string ExtAttr1 { get; set; }
        [NotMapped]
        public string ExtAttr2 { get; set; }
        [NotMapped]
        public string ExtAttr3 { get; set; }
        [NotMapped]
        public string ExtAttr4 { get; set; }
        [NotMapped]
        public string ExtAttr5 { get; set; }
        [NotMapped]
        public string SkuID { get; set; }
        [NotMapped]
        public decimal Weight { get; set; }
        [NotMapped]
        public string ShortTitle { get; set; }
        [NotMapped]
        public decimal SkuWeight { get; set; }
        [NotMapped]
        public string SkuShortTitle { get; set; }
        [NotMapped]
        public decimal ItemAmount { get; set; }

        public DateTime? OnlineSendTime { get; set; }
        public string StockState { get; set; }
        [NotMapped]
        public string AuthorId { get; set; }
        [NotMapped]
        public string AuthorName { get; set; }

        /// <summary>
        /// 订单项标签
        /// </summary>
        public List<OrderTags> Tags { get; set; } = new List<OrderTags>();

        /// <summary>
        /// 已发货数量（仅抖店）
        /// </summary>
        [NotMapped]
        public int? SendedCount { get; set; }

        /// <summary>
        /// 所属源用户
        /// </summary>
        [NotMapped]
        public int FxUserId { get; set; }
        [NotMapped]
        public string PathFlowCode { get; set; }

        /// <summary>
        /// 我对上游设置的结算价
        /// </summary>
        [NotMapped]
        public decimal? AuthorUpSettlementPrice { get; set; }
        /// <summary>
        /// 上游结算价
        /// </summary>
        [NotMapped]
        public decimal? UpFxUserSettlementPrice { get; set; }
        /// <summary>
        /// 下游结算价
        /// </summary>
        [NotMapped]
        public decimal? DownFxUserSettlementPrice { get; set; }
        /// <summary>
        /// 我对下游设置的结算价
        /// </summary>
        [NotMapped]
        public decimal? AuthorDownSettlementPrice { get; set; }

        /// <summary>
        /// 是否有关联基础商品
        /// </summary>
        [NotMapped]
        public bool IsRelationBaseProduct { get; set; }

        /// <summary>
        /// 基础资料规格表的SkuUid 
        /// </summary>
        [NotMapped]
        public long BaseProductSkuUid { get; set; }

        /// <summary>
        /// 旧的OrderItemCode
        /// </summary>
        [NotMapped]
        public string OldOrderItemCode { get; set; }
    }

    public class LogicSubOrderExtension
    {
        public LogicSubOrderExtension()
        {
            //OrderItems = new List<LogicOrderItem>();
        }
        public int Id { get; set; }
        public string OrderCode { get; set; }
        public string OrderItemCode { get; set; }
        public string ProductCode { get; set; }
        public string SkuCode { get; set; }
        public int UpFxUserId { get; set; }
        public int FxUserId { get; set; }
        public int DownFxUserId { get; set; }
        public bool IsFirst { get; set; }
        public string PlatformOrderId { get; set; }
        public string LogicOrderId { get; set; }
        public string OrignalOrderId { get; set; }
        public string PathFlowCode { get; set; }
        public string MergeredOrderId { get; set; }
        public string AgentName { get; set; }
        public bool IsSelf { get; set; }
        public int ShopId { get; set; }
        //public string PrintRemark { get; set; }
        public string BuyerRemark { get; set; }
        public string SellerRemarkFlag { get; set; }
        /// <summary>
        /// 旗帜标签
        /// taobao：从备注中截取
        /// </summary>
        [NotMapped]
        public string SellerRemarkFlagTag { get; set; }
        /// <summary>
        /// 卖家备注    
        /// </summary>
        public string SellerRemark { get; set; }

        /// <summary>
        /// 分发备注
        /// </summary>
        public string SystemRemark { get; set; }

        /// <summary>
        /// 分单备注旗帜
        /// </summary>
        public string SystemRemarkFlag { get; set; }

        public DateTime? CreateTime { get; set; }
        public DateTime? PayTime { get; set; }
        public DateTime? LastShipTime { get; set; }
        public DateTime? SendPrintTime { get; set; }
        public DateTime? NahuoPrintTime { get; set; }
        public DateTime? OnlineSendTime { get; set; }
        public DateTime? ExpressPrintTime { get; set; }
        public bool IsPreviewed { get; set; }
        public int LastExpressPrintTemplateId { get; set; }
        public string LastWaybillCode { get; set; }
        public string BuyerWangWang { get; set; }
        //public decimal? ShippingFee { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalWeight { get; set; }
        public int? ProductItemCount { get; set; }
        public string PlatformType { get; set; }

        public int OrderItemId { get; set; }
        public string ProductSubject { get; set; }
        public string CargoNumber { get; set; }
        public string productCargoNumber { get; set; }
        public string ProductImgUrl { get; set; }
        public string ProductID { get; set; }
        public string SkuId { get; set; }
        public string Color { get; set; }
        public string Size { get; set; }
        public decimal Price { get; set; }
        public int Count { get; set; }
        public string ErpState { get; set; }

        public string Status { get; set; }
        public int PrintState { get; set; }
        public string RefundStatus { get; set; }

        public object TkRefundStatus
        {
            get
            {
                try
                {
                    if (PlatformType == "TikTok" && ExtAttr1.IsNotNullOrEmpty())
                    {
                        var ois = OrderItem.MergeOiJsonToOis(ExtAttr1);
                        return ois.Where(f => f.RefundStatus.IsNotNullOrEmpty())
                            .GroupBy(f => f.RefundStatus)
                            .Select(f => new
                            {
                                RefundStatus = f.Key,
                                Count = f.Count(),
                                RefundAmount = f.Sum(x => x.RefundAmount)
                            });
                    }
                    return null;
                }
                catch (Exception)
                {
                    return null;
                }

            }
        }

        public string ExtAttr1 { get; set; }
        public string ExtAttr2 { get; set; }
        public string ExtAttr3 { get; set; }
        public string ExtAttr4 { get; set; }
        public string ExtAttr5 { get; set; }
        public decimal Weight { get; set; }
        public string ShortTitle { get; set; }
        public decimal SkuWeight { get; set; }
        public string SkuShortTitle { get; set; }
        public decimal ItemAmount { get; set; }

        /// <summary>
        /// 服务承诺：是否指定快递
        /// </summary>
        public bool IsAppointExpress
        {
            get
            {
                return OrderPromises?.Any(a => a.PromiseType == 1) ?? false;
            }
        }
        /// <summary>
        /// 服务承诺：是否优先发货
        /// </summary>
        [NotMapped]
        public bool IsFirstDelivery
        {
            get
            {
                var v = OrderPromises?.Any(a => a.PromiseType == 2) ?? false;
                //if (!v)
                //{
                //    //催发货
                //    v = OrderItemTags?.Any(a => a.Tag == OrderTag.remind_shipment.ToString()) ?? false;
                //}
                return v;
            }
        }
        /// <summary>
        /// 服务承诺列表
        /// </summary>
        public List<OrderPromise> OrderPromises { get; set; }

        /// <summary>
        /// 订单项数据
        /// </summary>
        //public List<LogicOrderItem> OrderItems { get; set; }

        /// <summary>
        /// 已发货数量（仅抖店）
        /// </summary>
        public int? SendedCount { get; set; }
        /// <summary>
        /// 订单项标签
        /// </summary>
        public List<OrderTags> OrderItemTags { get; set; }


        /// <summary>
        /// 采购单是否已付款
        /// </summary>
        [NotMapped]
        public bool IsPurchasePayed
        {
            get; set;
        }

        /// <summary>
        /// 采购单情况：0不需要，1已下单，-1待下单
        /// </summary>
        [NotMapped]
        public int NeedPurchase
        {
            get; set;
        }

        /// <summary>
        /// 采购单状态;waitcreate待创建，waitpay待支付，waitsellersend待发货，waitbuyerreceive待收货，close交易关闭，success交易完成，error
        /// </summary>
        [NotMapped]
        public string PurchaseStatus
        {
            get; set;
        }

        /// <summary>
        /// 是否需要打印：0=无需打印，源自PurchaseOrderDeliveryMode
        /// </summary>
        public int? IsNeedPrint { get; set; }

        /// <summary>
        /// 采购关联的源商家Id
        /// </summary>
        public int PurchaseSourceFxUserId { get; set; }
        /// <summary>
        /// 采购关联的源商家账号
        /// </summary>
        public string PurchaseSourceAgentMobile { get; set; }
        /// <summary>
        /// 留言凭证
        /// </summary>
        public List<PrintscreenPic> PrintscreenPics { get; set; }

        /// <summary>
        /// 是否有关联基础商品
        /// </summary>
        public bool IsRelationBaseProduct { get; set; }

        /// <summary>
        /// 基础资料规格表的SkuUid 
        /// </summary>
        public long BaseProductSkuUid { get; set; }

        /// <summary>
        /// 卡片->基础商品数据【json】
        /// </summary>
        public PrductSkuSimple BaseProductInfo { get; set; } = new PrductSkuSimple();

        /// <summary>
        /// 卡片->平台商品数据【json】
        /// </summary>
        public PrductSkuSimple PtProductInfo { get; set; } = new PrductSkuSimple();


        [NotMapped]
        public string TradeType { get; set; }
        
        /// <summary>
        /// 加密线下单对应的平台单号
        /// </summary>
        public string VirtualRelationId { get; set; }
    }

    public class AllOrderFirstModel
    {
        public AllOrderFirstModel()
        {
            SecondSubOrders = new List<AllOrderSecondModel>();
            WaybillCodes = new List<WaybillCodeViewModel>();
        }
        public DateTime? PublishTime { get; set; }
        /// <summary>
        /// 是否时冷数据
        /// </summary>
        private int? _IsCold;

        /// <summary>
        /// 2024-01-04 zouyi 冷热分离
        /// 1：标识冷数据，0：热数据
        /// </summary>
        [NotMapped]
        public int IsCold
        {
            get
            {
                if (_IsCold == null)
                {
                    try
                    {
                        _IsCold = (CustomerConfig.HotOrderStatus.Contains(ErpState) == false &&
                                   BaseSiteContext.CurrentNoThrow?.CurrentDbConfig?.IsUseColdDb == true)
                            ? 1
                            : 0;
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"计算订单数据冷热状态失败：{ex.Message}");
                    }
                }

                return _IsCold ?? 0;
            }
        }



		/// <summary>
		/// 发运类型,跨境。值：Global standard shipping（全球标准运输服务）、Standard shipping（本地标准运输服务）
		/// </summary>
		[NotMapped]
		public string DeliveryOptionName { get; set; }


		public string ErpState { get; set; }
        public string PlatformOrderId { get; set; }
        public string LogicOrderId { get; set; }
        public string PlatformType { get; set; }
        public string ShopName { get; set; }
        public int ApprovalStatus { get; set; }
        public int ShopId { get; set; }
        public string OrderTime { get; set; }
        public string SellerRemarkFlag { get; set; }
        [NotMapped]
        public string SellerRemarkFlagTag { get; set; }
        public string RefundStatus { get; set; }
        public string ExtField2 { get; set; }
        /// <summary>
        /// 平台补贴 LogicOrder.PlatformSubsidy
        /// </summary>
        public decimal? PlatformSubsidy { get; set; }
        /// <summary>
        /// 运费 LogicOrder.ShippingFee
        /// </summary>
        public decimal? ShippingFee { get; set; }
        /// <summary>
        /// 实付款 LogicOrder.TotalAmout
        /// </summary>
        public decimal? PayTotalAmount { get; set; }
        /// <summary>
        /// 实收款：当前组订单SUM(LogicOrder.SumTotalAmout) 
        /// </summary>
        public decimal? SumTotalAmount { get; set; }
        /// <summary>
        /// 商品件数：当前组订单SUM(LogicOrder.ProductItemCount)
        /// </summary>
        public int SumProductItemCount { get; set; }
        /// <summary>
        /// 拆分包裹数：当前组LogicOrder的个数
        /// </summary>
        public int SumPackageCount { get; set; }

        /// <summary>
        /// 商家店铺名
        /// </summary>
        public string AgentShopName { get; set; }

        public List<AllOrderSecondModel> SecondSubOrders { get; set; }
        public List<WaybillCodeViewModel> WaybillCodes { get; set; }

        public string LastShipTime { get; set; }

        public string TradeType { get; set; }
        public bool IsShowSalePrice { get; set; }
        //拼多多集运单
        public string PddConsolidate { get; set; }
        /// <summary>
        /// 拼多多暂停发货
        /// </summary>
        public string PddShipHold { get; set; }
        /// <summary>
        /// 是否为拼多多跨境单
        /// </summary>
        public bool IsPddCrossBorderOrder { get; set; }
        /// <summary>
        /// 拼多多跨境单：快递上门揽收
        /// </summary>
        public bool IsPddCourierDoorToDoorCollect { get; set; }

        /// <summary>
        /// 服务承诺列表
        /// </summary>
        [NotMapped]
        public List<OrderPromise> OrderPromises { get; set; }
        /// <summary>
        /// 标签列表
        /// </summary>
        public List<OrderTags> OrderTags { get; set; }
        /// <summary>
        /// 服务承诺：是否优先发货
        /// </summary>
        [NotMapped]
        public bool IsFirstDelivery
        {
            get
            {
                var v = OrderPromises?.Any(a => a.PromiseType == 2) ?? false;
                //if (!v)
                //{
                //    //催发货
                //    v = OrderTags?.Any(a => a.Tag == OrderTag.remind_shipment.ToString()) ?? false;
                //}
                return v;
            }
        }
        /// <summary>
        /// 是否为加密平台
        /// </summary>
        [NotMapped]
        public bool IsEncryptPlatform
        {
            get
            {
                var encryptPlatformTypes = CustomerConfig.EncryptPlatformTypes();
                return encryptPlatformTypes.Contains(PlatformType.ToString2());
            }
        }


        /// <summary>
        /// 采购单是否已付款
        /// </summary>
        public bool IsPurchasePayed { get; set; }

        /// <summary>
        /// 采购单情况：0不需要，1已下单，-1待下单
        /// </summary>
        public int NeedPurchase { get; set; }

        /// <summary>
        /// 采购单状态;waitcreate待创建，waitpay待支付，waitsellersend待发货，waitbuyerreceive待收货，close交易关闭，success交易完成，error
        /// </summary>
        public string PurchaseStatus { get; set; }

        /// <summary>
        /// 是否需要打印：0=无需打印，源自PurchaseOrderDeliveryMode
        /// </summary>
        public int? IsNeedPrint { get; set; }

        /// <summary>
        /// 采购关联的源商家Id
        /// </summary>
        public int PurchaseSourceFxUserId { get; set; }
        /// <summary>
        /// 采购关联的源商家账号
        /// </summary>
        public string PurchaseSourceAgentMobile { get; set; }

        /// <summary>
        /// 关联的采购单
        /// </summary>
        public PurchaseOrderShowModel PurchaseOrderRelation { get; set; }
        /// <summary>
        /// 是否线下单
        /// </summary>
        public bool IsOfflineOrder { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        [NotMapped]
        public string ToCountry { get; set; }

        /// <summary>
        /// 币种
        /// </summary>
        [NotMapped]
        public string Currency { get; set; }

        /// <summary>
        /// 物流状态： 0：未打印未交运1：已打印未交运2：已打印已交运
        /// </summary>
        public int LogisticStatus { get; set; }

        /// <summary>
        /// 是否允许非自营厂家使用卖家备注
        /// </summary>
        [NotMapped]
        public bool IsEditSellerRemark { get; set; }

        /// <summary>
        /// 卖家备注
        /// </summary>
        public int AddSellerNoteMode { get; set; }

        /// <summary>
        /// 是否手工发货
        /// </summary>
        public bool IsManual { get; set; }

        /// <summary>
        /// 发票状态
        /// </summary>
        public string InvoiceStatus { get; set; }

        /// <summary>
        /// 发货方式
        /// </summary>
        public string ShippingType { get; set; }

        /// <summary>
        /// 是否推到虚拟厂家
        /// </summary>
        public bool CanSendManual { get; set; }

        /// <summary>
        /// 对应的平台名称
        /// </summary>
        public string PlatformTypeName { get; set; }

        /// <summary>
        /// 仓库Id
        /// </summary>
        public int WarehouseId { get; set; }


        /// <summary>
        /// 代发留言旗帜
        /// </summary>
        //public string SystemRemarkFlag { get; set; }

        /// <summary>
        /// 留言凭证
        /// </summary>
        //public List<PrintscreenPic> PrintscreenPics {  get; set; }

        [NotMapped]
        public bool IsOfflineNoSku
        {
            get
            {
                if (this.OrderTags != null && this.OrderTags.Any())
                {
                    return this.OrderTags.Any(t => t.Tag == OrderTag.OfflineNoSku.ToString());
                }
                else
                {
                    return false;
                }
            }
        }
    }

    public class AllOrderSecondModel
    {
        public AllOrderSecondModel()
        {
            ThressSubOrders = new List<AllOrderThreeModel>();
        }
        public int FxUserId { get; set; }
        public int UpFxUserId { get; set; }
        public string SupplierName { get; set; }
        public int DownFxUserId { get; set; }
        public string LogicOrderId { get; set; }
        public string SystemRemark { get; set; }
        public string RefundStatus { get; set; }
        public string SelfShopName { get; set; }
        public decimal? TotalAmount { get; set; }


        /// <summary>
        /// 跨境发货的交运时间
        /// </summary>
        public DateTime? ShipmentDate { get; set; }

        public List<WaybillCodeViewModel> WaybillCodes { get; set; }
        public List<AllOrderThreeModel> ThressSubOrders { get; set; }
        /// <summary>
        /// 发货时间
        /// </summary>
        public DateTime? OnlineSendTime { get; set; }
        public bool ReceiverIsChange { get; set; }

        /// <summary>
        /// 采购单是否已付款
        /// </summary>
        public bool IsPurchasePayed { get; set; }

        /// <summary>
        /// 采购单情况：0不需要，1已下单，-1待下单
        /// </summary>
        public int NeedPurchase { get; set; }

        /// <summary>
        /// 采购单状态;waitcreate待创建，waitpay待支付，waitsellersend待发货，waitbuyerreceive待收货，close交易关闭，success交易完成，error
        /// </summary>
        public string PurchaseStatus { get; set; }
        /// <summary>
        /// 是否需要打印：0=无需打印，源自PurchaseOrderDeliveryMode
        /// </summary>
        public int? IsNeedPrint { get; set; }
        /// <summary>
        /// 采购关联的源商家Id
        /// </summary>
        public int PurchaseSourceFxUserId { get; set; }
        /// <summary>
        /// 采购关联的源商家账号
        /// </summary>
        public string PurchaseSourceAgentMobile { get; set; }


        /// <summary>
        /// 留言凭证
        /// </summary>
        public List<PrintscreenPic> PrintscreenPics { get; set; }

        /// <summary>
        /// 代发留言旗帜
        /// </summary>
        public string SystemRemarkFlag { get; set; }

    }

    public class AllOrderThreeModel
    {
        public int Id { get; set; }
        public int FxPageType { get; set; }
        public string OrderCode { get; set; }
        public int OrderItemId { get; set; }
        public string OrderItemCode { get; set; }
        public string LogicOrderId { get; set; }
        public bool IsFirst { get; set; }
        public bool IsSelf { get; set; }
        public int ShopId { get; set; }
        public int ApprovalStatus { get; set; }
        public string BuyerRemark { get; set; }
        public string SellerRemark { get; set; }
        public string ToName { get; set; }
        public string ToPhone { get; set; }
        public string ToFullAddress { get; set; }
        public DateTime? SendPrintTime { get; set; }
        public DateTime? NahuoPrintTime { get; set; }
        public string ExpressName { get; set; }
        public string ExpressWaybillCode { get; set; }

        public string ProductSubject { get; set; }
        public string ProductCargoNumber { get; set; }
        public string CargoNumber { get; set; }
        public string ProductImgUrl { get; set; }
        public string ProductCode { get; set; }
        public string ProductID { get; set; }
        public string SkuId { get; set; }
        public string Color { get; set; }
        public string Size { get; set; }
        public decimal? Price { get; set; }

        public decimal? ItemAmount { get; set; }
        public int Count { get; set; }
        public int PrintState { get; set; }
        public string Status { get; set; }
        public string ErpState { get; set; }
        public string RefundStatus { get; set; }

        public object TkRefundStatus
        {
            get
            {
                try
                {
                    if (PlatformType == "TikTok" && ExtAttr1.IsNotNullOrEmpty())
                    {
                        var ois = OrderItem.MergeOiJsonToOis(ExtAttr1);
                        return ois.Where(f => f.RefundStatus.IsNotNullOrEmpty())
                            .GroupBy(f => f.RefundStatus)
                            .Select(f => new
                            {
                                RefundStatus = f.Key,
                                Count = f.Count(),
                                RefundAmount = f.Sum(x => x.RefundAmount)
                            });
                    }
                    return null;
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }
        public string PlatformType { get; set; }

        public string ExtAttr1 { get; set; }

        public string PurchaseStatus { get; set; }

        /// <summary>
        /// 异常状态操作
        /// </summary>
        public ExceptionRefundOpt ExceptionOpt { get; set; }
        public decimal Weight { get; set; }
        public string ShortTitle { get; set; }
        public decimal SkuWeight { get; set; }
        public string SkuShortTitle { get; set; }

        public string ToProvince { get; set; }
        public string ToCity { get; set; }
        public string ToCounty { get; set; }
        public string ToAddress { get; set; }
        /// <summary>
        /// 拼多多集运
        /// </summary>
        public string PddConsolidate { get; set; }

        /// <summary>
        /// 拼多多暂停发货
        /// </summary>
        public string PddShipHold { get; set; }
        /// <summary>
        /// 是否为拼多多跨境单
        /// </summary>
        public bool IsPddCrossBorderOrder { get; set; }

        /// <summary>
        /// 拼多多跨境单：快递上门揽收
        /// </summary>
        public bool IsPddCourierDoorToDoorCollect { get; set; }

        /// <summary>
        /// 订单项标签
        /// </summary>
        public List<OrderTags> OrderItemTags { get; set; }

        public string ExtAttr4 { get; set; }

        /// <summary>
        /// 是否有关联商品库
        /// </summary>
        public bool IsRelationBaseProduct { get; set; }

        /// <summary>
        /// 卡片->基础商品数据【json】
        /// </summary>
        public PrductSkuSimple BaseProductInfo { get; set; }

        /// <summary>
        /// 卡片->平台商品数据【json】
        /// </summary>
        public PrductSkuSimple PtProductInfo { get; set; }
    }
    public class PurchaseOrderShowModel
    {
        /// <summary>
        /// 平台类型
        /// </summary>
        public string PlatformType { get; set; }

        /// <summary>
        /// 0 商家 1 厂家
        /// </summary>
        public int RoleType { get; set; }

        /// <summary>
        /// 展示名称
        /// </summary>
        public string ShowName { get; set; }

        /// <summary>
        /// 展示单号
        /// </summary>
        public string OrderId { get; set; }

        /// <summary>
        /// 采购单P_Order.PlatformOrderId
        /// </summary>
        public string PurchasePlatformOrderId { get; set; }

        /// <summary>
        /// 来源逻辑单ID
        /// </summary>
        public string SourceLogicOrderId { get; set; }

        /// <summary>
        /// 订单创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 加密后分区数据库名
        /// </summary>

        public string DbName { get; set; }
    }

}
