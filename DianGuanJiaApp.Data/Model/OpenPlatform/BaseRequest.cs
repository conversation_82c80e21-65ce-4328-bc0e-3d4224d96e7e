using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Linq;
using System.Reflection;

namespace DianGuanJiaApp.Data.Model.OpenPlatform
{
    /// <summary>
    ///
    /// </summary>
    public class BaseRequest
    {
        /// <summary>
        /// 校验必填参数
        /// </summary>
        /// <exception cref="LogicException"></exception>
        public virtual void CheckParam(string prefix = "")
        {
            prefix += prefix != "" ? "." : "";
            // 获取类型信息
            Type type = this.GetType();
            // 获取所有属性
            PropertyInfo[] properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            //是否有必填属性
            var requiredProperties = properties.Where(t => t.GetCustomAttributes<RequiredAttribute>().Any()).ToList();
            if (requiredProperties.Any())
            {
                //查询出所有必填属性
                requiredProperties.ForEach(property =>
                {
                    var value = property.GetValue(this);
                    if (property.PropertyType == typeof(string))
                    {
                        if (string.IsNullOrWhiteSpace(value?.ToString2()))
                        {
                            throw new LogicException($"值能不能为空，参数名：{prefix}{property.Name}", OpenApiErrCode.ParamNull.ToString());
                        }
                    }
                    else
                    {
                        if (value == null)
                        {
                            throw new LogicException($"值能不能为空，参数名：{prefix}{property.Name}", OpenApiErrCode.ParamNull.ToString());
                        }
                    }
                });
            }
        }

        /// <summary>
        /// 校验云平台类型
        /// </summary>
        /// <param name="cloudPlatformType"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        internal string CheckCloudPlatformType(string cloudPlatformType)
        {
            if (!System.Enum.IsDefined(typeof(CloudPlatformType), cloudPlatformType) && cloudPlatformType != PlatformType.TikTok.ToString())
            {
                throw new LogicException("值错误，参数名：CloudPlatformType", OpenApiErrCode.ParamErr.ToString());
            }
            if (cloudPlatformType == PlatformType.TikTok.ToString())
            {
                //设置上下文为跨境
                BaseSiteContext.SetIsCrossBorder(true);
                //设置云平台类型为精选
                cloudPlatformType = CloudPlatformType.Alibaba.ToString();
            }
            return cloudPlatformType;
        }

        /// <summary>
        /// 校验分页参数
        /// </summary>
        /// <param name="pageSize"></param>
        /// <param name="page"></param>
        /// <exception cref="LogicException"></exception>
        internal void CheckPageParam(int pageSize, int page)
        {
            if (pageSize <= 0)
                throw new LogicException("值错误，参数名：PageSize", OpenApiErrCode.ParamErr.ToString());
            if (pageSize > 100)
                throw new LogicException("超过最大值，参数名：PageSize", OpenApiErrCode.ParamErr.ToString());
            if (page <= 0)
                throw new LogicException("值错误，参数名：Page", OpenApiErrCode.ParamErr.ToString());
        }

        /// <summary>
        /// 校验时间范围参数
        /// </summary>
        /// <param name="startDateStr"></param>
        /// <param name="endDateStr"></param>
        /// <param name="maxTimeSpanDay"></param>
        /// <exception cref="LogicException"></exception>
        internal void CheckDateTimeRangeParam(string startDateStr, string endDateStr, int maxTimeSpanDay = 15)
        {
            if (!startDateStr.IsDateTime())
                throw new LogicException("值错误，参数名：StartDate", OpenApiErrCode.ParamErr.ToString());
            if (!startDateStr.IsDateTime())
                throw new LogicException("值错误，参数名：EndDate", OpenApiErrCode.ParamErr.ToString());
            var startDate = startDateStr.ToDateTime().Value;
            var endDate = endDateStr.ToDateTime().Value;
            if ((DateTime.Now - startDate).TotalDays > 45)
                throw new LogicException("查询时间不能超过45天", OpenApiErrCode.ParamErr.ToString());
            // 检查开始时间和结束时间间隔是否超过maxTimeSpanDay天
            var timeSpan = endDate - startDate;
            if (timeSpan.TotalDays > maxTimeSpanDay)
                throw new LogicException($"开始时间和结束时间间隔不能超过{maxTimeSpanDay}天", OpenApiErrCode.ParamErr.ToString());
        }
    }
}