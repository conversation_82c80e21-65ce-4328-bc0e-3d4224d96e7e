using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Data.Model.OpenPlatform
{
    public class UploadOrderRequest : BaseRequest
    {
        /// <summary>
        /// 订单id 唯一标识
        /// </summary>
        [Required]
        public string Id { get; set; }

        /// <summary>
        /// 店铺id
        /// </summary>
        [Required]
        public int ShopId { get; set; }

        /// <summary>
        /// 总金额，或叫付款金额，单位分
        /// </summary>
        public long TotalAmount { get; set; }

        /// <summary>
        /// 创建时间，下单时间 2024-11-06 00:00:00
        /// </summary>
        [Required]
        public string CreateTime { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        public string PayTime { get; set; }

        /// <summary>
        /// 订单状态 详见 订单状态说明表
        /// </summary>
        [Required]
        public string Status { get; set; }

        /// <summary>
        /// 退款状态，详见 退款状态说明表
        /// </summary>
        public string RefundStatus { get; set; }

        /// <summary>
        /// 买家留言
        /// </summary>
        public string BuyerRemark { get; set; }

        /// <summary>
        /// 卖家备注
        /// </summary>
        public string SellerRemark { get; set; }

        /// <summary>
        /// 分销备注
        /// </summary>
        public string FxRemark { get; set; }

        /// <summary>
        /// 收件人姓名
        /// </summary>
        [Required]
        public string ToName { get; set; }

        /// <summary>
        /// 收件人联系电话
        /// </summary>
        [Required]
        public string ToMobile { get; set; }

        /// <summary>
        /// 收件人省份
        /// </summary>
        [Required]
        public string ToProvince { get; set; }

        /// <summary>
        /// 收件人市
        /// </summary>
        [Required]
        public string ToCity { get; set; }

        /// <summary>
        /// 收件人区或镇或县
        /// </summary>
        [Required]
        public string ToCounty { get; set; }

        /// <summary>
        /// 收件人街道详细地址
        /// </summary>
        [Required]
        public string ToStreet { get; set; }

        /// <summary>
        /// 订单项（子订单、商品）
        /// </summary>
        [Required]
        public List<UploadOrderItemRequest> Items { get; set; }

        /// <summary>
        /// 外部订单来源平台类型（有值表示这是平台单，必须使用平台对应的电子面单进行取号）
        /// 抖音：TouTiao
        /// 淘宝：Taobao
        /// 快手：KuaiShou
        /// 拼多多：Pinduoduo
        /// </summary>
        public string OutChannel { get; set; }

        /// <summary>
        /// 平台单的密文信息
        /// </summary>
        public OpenPlatformEncryptOutOrderInfo EncryptOutOrderInfo { get; set; }

        /// <summary>
        /// 校验必填参数
        /// </summary>
        public override void CheckParam(string prefix = "")
        {
            base.CheckParam();
            if (ShopId <= 0)
                throw new LogicException("值不能小于等于0，参数名：ShopId", OpenApiErrCode.ParamErr.ToString());
            if (Items.IsNullOrEmptyList())
                throw new LogicException("值能不能为空，参数名：Items", OpenApiErrCode.ParamNull.ToString());
            Items.ForEach(t =>
            {
                t.CheckParam("Item");
            });
            if (Items.GroupBy(s => s.Id).Any(g => g.Count() > 1))
                throw new LogicException("值不能重复，参数名：Items.id", OpenApiErrCode.ParamErr.ToString());
            if (!string.IsNullOrWhiteSpace(OutChannel))
            {
                //密文单的校验
                if (!System.Enum.IsDefined(typeof(PlatformType), OutChannel))
                    throw new LogicException("值错误，参数名：OutChannel", OpenApiErrCode.ParamErr.ToString());
                if (EncryptOutOrderInfo == null)
                    throw new LogicException("值不能为空，参数名：EncryptOutOrderInfo", OpenApiErrCode.ParamNull.ToString());
            }
        }
    }

    /// <summary>
    /// 订单项
    /// </summary>
    public class UploadOrderItemRequest : BaseRequest
    {
        /// <summary>
        /// 订单项ID，当前订单中的唯一标识
        /// </summary>
        [Required]
        public string Id { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// 商品的skuId，平台标识
        /// </summary>
        [Required]
        public string SkuId { get; set; }

        /// <summary>
        /// 规格名称
        /// </summary>
        [Required]
        public string SkuName { get; set; }

        /// <summary>
        /// sku商家编码（或货号）
        /// </summary>
        public string SkuOuterId { get; set; }

        /// <summary>
        /// 商品ID，平台标识
        /// </summary>
        [Required]
        public string ProductId { get; set; }

        /// <summary>
        /// 商品图片
        /// </summary>
        public string ProductImgUrl { get; set; }

        /// <summary>
        /// 商品商家编码（或货号）
        /// </summary>
        public string ProductOuterId { get; set; }

        /// <summary>
        /// 订单项金额（折扣后的），单位分
        /// </summary>
        public long ItemAmount { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 订单状态 详见 订单状态说明表
        /// </summary>
        [Required]
        public string Status { get; set; }

        /// <summary>
        /// 退款状态，详见 退款状态说明表
        /// </summary>
        public string RefundStatus { get; set; }
    }

    /// <summary>
    /// 开放平台密文订单信息
    /// </summary>
    public class OpenPlatformEncryptOutOrderInfo
    {
        /// <summary>
        /// 是否为密文单
        /// </summary>
        public bool FromEncryptOrder { get; set; }

        /// <summary>
        /// 外部平台单号
        /// </summary>
        public string OutPlatformOrderNo { get; set; }

        /// <summary>
        /// 淘宝oaid/抖店open_address_id/拼多多的open_address_id
        /// </summary>
        public string OaId { get; set; }

        /// <summary>
        /// 加密收货人姓名
        /// </summary>
        public string EncryptReceiverName { get; set; }

        /// <summary>
        /// 加密收货人电话
        /// </summary>
        public string EncryptReceiverMobile { get; set; }

        /// <summary>
        /// 加密收货人地址
        /// </summary>
        public string EncryptReceiverAddress { get; set; }

        /// <summary>
        /// 店铺ID 快手、拼多多必填打单必须使用
        /// </summary>
        public string OutShopId { get; set; }
    }

    public class UploadOrderRespone
    {
        public UploadOrderRespone()
        { }

        public UploadOrderRespone(string id, string platformOrderId)
        {
            Id = id;
            PlatformOrderId = platformOrderId;
        }

        /// <summary>
        /// 订单ID(外部单号)
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 平台订单ID(店管家单号)
        /// </summary>
        public string PlatformOrderId { get; set; }
    }
}