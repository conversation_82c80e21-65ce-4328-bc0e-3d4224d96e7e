using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Dapper;

namespace DianGuanJiaApp.Data
{
    public static class PropertyInfoHelper
    {
        /// <summary>
        /// 反射性能问题，静态变量缓存
        /// </summary>
        private static readonly ConcurrentDictionary<string, List<PropertyInfo>> PropertyInfos =
            new ConcurrentDictionary<string, List<PropertyInfo>>();

        /// <summary>
        /// 返回实体属性信息（只有相同使用情况，才可以使用此方法）
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static List<PropertyInfo> GetPropertyInfos<T>()
        {
            //字段
            var type = typeof(T);
            var typeName = type.Name;
            var fullName = type.FullName ?? typeName;
            List<PropertyInfo> properties;
            if (PropertyInfos.TryGetValue(fullName, out properties) == false)
            {
                properties = type.GetProperties(
                    BindingFlags.GetProperty |
                    BindingFlags.Instance |
                    BindingFlags.Public
                ).Where(m => !m.GetCustomAttributes(true).Any(ca =>
                    ca.GetType().Name == nameof(IgnoreSelectAttribute) ||
                    ca.GetType().Name == nameof(NotMappedAttribute)) && m.PropertyType.IsSimpleType()).ToList();
                PropertyInfos.TryAdd(fullName, properties);
            }

            //返回实体属性信息
            return properties;
        }

        /// <summary>
        /// 反射性能问题，静态变量缓存，不要继承属性信息
        /// </summary>
        private static readonly ConcurrentDictionary<string, List<PropertyInfo>> PropertyInfosByDeclaredOnly =
            new ConcurrentDictionary<string, List<PropertyInfo>>();

        /// <summary>
        /// 返回实体属性信息，不要继承属性信息（只有相同使用情况，才可以使用此方法）
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static List<PropertyInfo> GetPropertyInfosByDeclaredOnly<T>()
        {
            //字段
            var type = typeof(T);
            var typeName = type.Name;
            var fullName = type.FullName ?? typeName;
            List<PropertyInfo> properties;
            if (PropertyInfosByDeclaredOnly.TryGetValue(fullName, out properties) == false)
            {
                properties = type.GetProperties(
                    BindingFlags.GetProperty |
                    BindingFlags.Instance |
                    BindingFlags.Public |
                    BindingFlags.DeclaredOnly
                ).Where(m => !m.GetCustomAttributes(true).Any(ca =>
                    ca.GetType().Name == nameof(IgnoreSelectAttribute) ||
                    ca.GetType().Name == nameof(NotMappedAttribute))).ToList();
                PropertyInfosByDeclaredOnly.TryAdd(fullName, properties);
            }

            //返回实体属性信息
            return properties;
        }

        /// <summary>
        /// 反射性能问题，静态变量缓存
        /// </summary>
        private static readonly ConcurrentDictionary<string, List<PropertyInfo>> PropertyInfosByCustom =
            new ConcurrentDictionary<string, List<PropertyInfo>>();

        /// <summary>
        /// 返回实体属性信息（只有相同使用情况，才可以使用此方法）
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static List<PropertyInfo> GetPropertyInfosByCustom<T>()
        {
            //字段
            var type = typeof(T);
            var typeName = type.Name;
            var fullName = type.FullName ?? typeName;
            List<PropertyInfo> properties;
            if (PropertyInfosByCustom.TryGetValue(fullName, out properties) == false)
            {
                var defaultIgnoreTypes = new List<string> { "NotMappedAttribute", "IgnoreColumnAttribute" };
                properties = type.GetProperties().Where(prop =>
                {
                    if (prop.CustomAttributes.Any(c => defaultIgnoreTypes.Contains(c.AttributeType.Name)))
                        return false;
                    if (prop.PropertyType.IsValueType || prop.PropertyType.Name.StartsWith("String"))
                        return true;
                    return false;
                }).ToList();
                PropertyInfosByCustom.TryAdd(fullName, properties);
            }

            //返回实体属性信息
            return properties;
        }
        
        /// <summary>
        /// 反射性能问题，静态变量缓存
        /// </summary>
        private static readonly ConcurrentDictionary<string, List<PropertyInfo>> Properties =
            new ConcurrentDictionary<string, List<PropertyInfo>>();
        /// <summary>
        /// 返回实体属性信息（只有相同使用情况，才可以使用此方法）
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static List<PropertyInfo> GetProperties<T>()
        {
            //字段
            var type = typeof(T);
            var typeName = type.Name;
            var fullName = type.FullName ?? typeName;
            List<PropertyInfo> properties;
            if (Properties.TryGetValue(fullName, out properties) == false)
            {
                properties = type.GetProperties().ToList();
                Properties.TryAdd(fullName, properties);
            }
            //返回实体属性信息
            return properties;
        }
        /// <summary>
        /// 返回实体属性信息，指定类型（只有相同使用情况，才可以使用此方法）
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static List<PropertyInfo> GetProperties(Type type)
        {
            //字段
            var typeName = type.Name;
            var fullName = type.FullName ?? typeName;
            List<PropertyInfo> properties;
            if (Properties.TryGetValue(fullName, out properties) == false)
            {
                properties = type.GetProperties().ToList();
                Properties.TryAdd(fullName, properties);
            }
            //返回实体属性信息
            return properties;
        }
    }
}