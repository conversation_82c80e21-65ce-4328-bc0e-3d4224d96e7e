using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Diagnostics;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using System.Data;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Order = DianGuanJiaApp.Data.Entity.Order;
using System.Text;
using System.Diagnostics.Contracts;
using Jd.Api.Domain;
using Elasticsearch.Net;
using DianGuanJiaApp.Data.FxModel;

namespace DianGuanJiaApp.Data.Repository
{

    public partial class AfterSaleOrderRepository : BaseRepository<AfterSaleOrder>
    {
        private string _connectionString = string.Empty;

        private readonly PurchaseOrderRelationRepository _purchaseOrderRelationRepository;
        public AfterSaleOrderRepository()
        {
            _purchaseOrderRelationRepository = new PurchaseOrderRelationRepository();
        }
        public AfterSaleOrderRepository(string connectionString) : base(connectionString)
        {
            _connectionString = connectionString;
            _purchaseOrderRelationRepository = new PurchaseOrderRelationRepository(connectionString);
        }


        /// <summary>
        /// 批量插入
        /// </summary>
        /// <param name="list"></param>
        public new void BulkInsert(List<AfterSaleOrder> list)
        {
            try
            {
                BulkWrite(list, "AfterSaleOrder");
            }
            catch (Exception e)
            {
                var db = DbConnection;
                if (db.State == ConnectionState.Closed)
                {
                    db.Open();   
                }
                using (db)
                {
                    //单条
                    list.ForEach(item =>
                    {
                        try
                        {
                            db.Insert(item);
                        }
                        catch (Exception ex)
                        {
                            var errMsg = ex.Message.ToLower();
                            if (errMsg.Contains("duplicate key") || errMsg.Contains("pk_") ||
                                errMsg.Contains("primary key") || errMsg.Contains("重复键"))
                            {
                                //忽略
                            }
                            else
                            {
                                throw ex;
                            }
                        }
                    });
                }
            }
        }

        /// <summary>
        /// 根据AfterSaleCode查询
        /// </summary>
        /// <param name="afterSaleCode"></param>
        /// <returns></returns>
        public AfterSaleOrder GetByAfterSaleCode(string afterSaleCode)
        {
            var sql = "SELECT TOP 1 Id,AfterSaleId,AfterSaleCode,ShopId,FxUserId,SourceFlag FROM AfterSaleOrder WITH(NOLOCK) WHERE AfterSaleCode=@AfterSaleCode ";
            return this.DbConnection.Query<AfterSaleOrder>(sql, new { AfterSaleCode = afterSaleCode }).FirstOrDefault();
        }

        /// <summary>
        /// 根据AfterSaleCode查询
        /// </summary>
        /// <param name="afterSaleCode"></param>
        /// <returns></returns>
        public List<AfterSaleOrder> GetListByAfterSaleCode(string afterSaleCode)
        {
            var sql = "SELECT * FROM AfterSaleOrder WITH(NOLOCK) WHERE AfterSaleCode=@AfterSaleCode ";
            //var sql = "SELECT Id,AfterSaleId,AfterSaleCode,ShopId,FxUserId,SourceFlag FROM AfterSaleOrder WITH(NOLOCK) WHERE AfterSaleCode=@AfterSaleCode ";
            return this.DbConnection.Query<AfterSaleOrder>(sql, new { AfterSaleCode = afterSaleCode }).ToList();
        }

        /// <summary>
        /// 根据AfterSaleCode列表查询
        /// </summary>
        /// <param name="afterSaleCodes"></param>
        /// <param name="fields">字段</param>
        /// <returns></returns>
        public List<AfterSaleOrder> GetListByAfterSaleCode(List<string> afterSaleCodes, List<string> fields = null)
        {
            var strFields = "Id,AfterSaleId,AfterSaleCode,PathFlowCode,ShopId,FxUserId,SourceFlag,CreateBy";
            if (fields != null && fields.Any())
                strFields = string.Join(",", fields);
            var sql = $"SELECT {strFields} FROM AfterSaleOrder WITH(NOLOCK) WHERE AfterSaleCode IN @AfterSaleCodes ";

            var result = new List<AfterSaleOrder>();
            var index = 0;
            while (true)
            {
                var curCodes = afterSaleCodes.Skip(index * 500).Take(500).ToList();
                index++;
                if (curCodes == null || curCodes.Any() == false)
                    break;
                var parameters = new DynamicParameters();
                parameters.Add("AfterSaleCodes", curCodes);
                var temps = DbConnection.Query<AfterSaleOrder>(sql, parameters)?.ToList();
                if (temps != null && temps.Any())
                    result.AddRange(temps);
            }
            return result;
        }

        /// <summary>
        /// 根据AfterSaleId查询
        /// </summary>
        /// <param name="afterSaleId"></param>
        /// <returns></returns>
        public List<AfterSaleOrder> GetListByAfterSaleId(string afterSaleId)
        {
            var sql = "SELECT Id,AfterSaleId,AfterSaleCode,ShopId,FxUserId,SourceFlag FROM AfterSaleOrder WITH(NOLOCK) WHERE AfterSaleId=@AfterSaleId ";
            return this.DbConnection.Query<AfterSaleOrder>(sql, new { AfterSaleId = afterSaleId }).ToList();
        }

        ///// <summary>
        ///// 根据AfterSaleId列表查询
        ///// </summary>
        ///// <param name="afterSaleIds"></param>
        ///// <returns></returns>
        //public List<AfterSaleOrder> GetListByAfterSaleId(List<string> afterSaleIds)
        //{
        //    var sql = "SELECT Id,AfterSaleId,AfterSaleCode,ShopId,FxUserId,SourceFlag,PlatAfterSaleStatus FROM AfterSaleOrder WITH(NOLOCK) WHERE AfterSaleId IN @AfterSaleIds ";
        //    return this.DbConnection.Query<AfterSaleOrder>(sql, new { AfterSaleCodes = afterSaleIds }).ToList();
        //}
        /// <summary>
        /// 获取平台单路径流的用户id
        /// </summary>
        /// <param name="orderItemCodes"></param>
        /// <returns></returns>
        public List<PathFlowFxUserModel> GetFxUserByQuery(List<string> orderItemCodes)
        {
            const string sql = @"SELECT DISTINCT pfn.FxUserId,pfn.UpFxUserId, pfn.DownFxUserId  FROM LogicOrder lo
                    INNER JOIN LogicOrderItem loi WITH (NOLOCK) ON lo.LogicOrderId = loi.LogicOrderId
                    INNER JOIN PathFlowNode pfn WITH (NOLOCK) ON pfn.PathFlowCode = lo.PathFlowCode
                    INNER JOIN FunStringToTableV2 (@OrderItemCodes, ',') t1 ON t1.item = loi.OrderItemCode";
                    // WHERE lo.ShopId in @ShopIds";
            return DbConnection.Query<PathFlowFxUserModel>(sql,
                    new { OrderItemCodes = string.Join(",", orderItemCodes.Distinct().ToList()) }).ToList();
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<AfterSalePageResult>> GetAfterSaleOrderList(AfterSaleOrderQuery query)
        {
            if (query.SourceFlag == 1)//售后手工单
                return GetAfterSaleOrderListBySourceFlag(query);

            //判断是否查询平台售后订单&已发货的售后单
            if (BaseSiteContext.Current.IsUseColdDb //用户开启了冷库
                && query.SourceFlag == 0  //平台售后订单
                && query.SendState.HasValue && query.SendState.Value == 1 //已发货订单
                && query.ColdDbByAfterSaleOrder)
            {
                return GetAfterSaleOrderListBySended(query);
            }

            var parameters = new DynamicParameters();
            #region 混合查询基础商品
            var baseofptskurelationJoinSql = string.Empty;
            var baseofptskurelationFields = string.Empty;
            var tempTableName = GetTempTableName(BusinessTypes.AfterSaleOrder, query.FxUserId);
            // sql优化需求：https://www.tapd.cn/tapd_fe/32787034/story/detail/1132787034001046398
            var pTempTableName = tempTableName + "_4";
            var fxUserId = query.FxUserId;
            var baseofptskurelationSwitch =query.IsBaseProduceCombine;
            if (baseofptskurelationSwitch)
            {
                baseofptskurelationJoinSql = $@" LEFT JOIN BaseOfPtSkuRelation rel WITH ( NOLOCK ) ON asoi.ProductCode= rel.ProductCode 
AND asoi.SkuCode= rel.ProductSkuCode 
AND rel.FxUserId=@fxUserId AND rel.Status=1";
                parameters.Add("fxUserId", fxUserId);
                baseofptskurelationFields = $",CASE WHEN rel.BaseProductSkuUid IS NULL THEN 0 ELSE 1 END AS IsRelationBaseProduct,rel.BaseProductSkuUid AS BaseProductSkuUid";
            }
            #endregion

            // 系统售后单，以LogicOrder路径流为准
            var sql =
                $@"
-- sql优化需求，将路径流子查询放入存入临时表
SELECT DISTINCT t1.SourceFxUserId  INTO #temp{pTempTableName} FROM PathFlow t1 WITH(NOLOCK) INNER JOIN PathFlowNode t2 WITH(NOLOCK) ON t1.PathFlowCode = t2.PathFlowCode WHERE t2.FxUserId=@FxUserId;
--
SELECT TOP 1000000 asoi.Id AS ItemId,aso.AfterSaleId,asoi.PlatformOrderId,pfn.UpFxUserId,pfn.DownFxUserId,pfn.PathFlowCode,lo.OnlineSendTime,lo.ErpState,lo.PrintState as OrderPrintState,lo.IsPreviewed,lo.ExpressPrintTime,lo.LastWaybillCode,lo.LogicOrderId AS SourceLogicOrderId,lo.MergeredOrderId
                        INTO #temp{tempTableName}
                        FROM AfterSaleOrderItem asoi WITH(NOLOCK)
                        INNER JOIN AfterSaleOrder aso WITH(NOLOCK) ON aso.AfterSaleCode = asoi.AfterSaleCode
                        INNER JOIN #temp{pTempTableName}  AS tfx ON tfx.SourceFxUserId=aso.FxUserId
                        INNER JOIN LogicOrder lo WITH(NOLOCK) ON lo.PlatformOrderId = asoi.PlatformOrderId AND lo.ShopId=asoi.ShopId  
                        INNER JOIN LogicOrderItem loi WITH(NOLOCK) ON loi.OrderItemCode = asoi.OrderItemCode AND lo.LogicOrderId=loi.LogicOrderId
                        INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode = lo.PathFlowCode
                        {(query.ApplyAfterSaleStatus.HasValue ? "LEFT JOIN PurchaseOrderRelation por WITH(NOLOCK) ON por.SourcePlatformOrderId = asoi.PlatformOrderId AND por.SourceLogicOrderId = lo.LogicOrderId AND por.Status = 1" : string.Empty)}";
            var whereFxUser = " WHERE pfn.FxUserId=@FxUserId ";
            string whereStr = "";      //1.查出OrderCode条件
            string secondWhereStr = "";//二次查询条件
            string joinQuerySql = string.Empty; //join查询条件
            string secondJoinQuerySql = string.Empty;//二次join查询条件
            var tableName = "lo";//LogicOrderId查询的表名，手工单为aso，平台单为lo
            parameters.Add("FxUserId", query.FxUserId);

            //手动创建的售后单，以创建时的路径流为准
            if (query.SourceFlag == 1 || query.SourceFlag == 2)
            {
                sql = $@"SELECT TOP 1000000 asoi.Id AS ItemId,aso.AfterSaleId,aso.PlatformOrderId,pfn.UpFxUserId,pfn.DownFxUserId,pfn.PathFlowCode,NULL AS OnlineSendTime,NULL AS ErpState,NULL AS OrderPrintState,NULL AS IsPreviewed,NULL AS ExpressPrintTime,NULL AS LastWaybillCode,NULL AS SourceLogicOrderId,NULL AS MergeredOrderId 
                        INTO #temp{tempTableName}
                        FROM AfterSaleOrderItem asoi WITH(NOLOCK)
                        INNER JOIN AfterSaleOrder aso WITH(NOLOCK) ON aso.AfterSaleCode = asoi.AfterSaleCode
                        INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode = aso.PathFlowCode";

                tableName = "aso";
            }

            whereStr += " AND SourceFlag=@SourceFlag";
            parameters.Add("SourceFlag", query.SourceFlag);


            var isSearchId = (query.Ids != null && query.Ids.Any());

            //只搜索指定Id
            if (isSearchId)
            {
                whereStr += " AND aso.Id IN @Ids";
                parameters.Add("Ids", query.Ids);

                secondWhereStr = whereStr;
            }
            else
            {
                #region 查询其他条件
                if (!string.IsNullOrEmpty(query.PlatformType))
                {
                    whereStr += " AND aso.PlatformType=@PlatformType";
                    parameters.Add("PlatformType", query.PlatformType);
                }
                if (query.AfterSaleType != null)
                {
                    whereStr += " AND aso.AfterSaleType=@AfterSaleType";
                    parameters.Add("AfterSaleType", query.AfterSaleType.Value);
                }
                if (query.RefundStatus != null)
                {
                    whereStr += " AND aso.RefundStatus=@RefundStatus";
                    parameters.Add("RefundStatus", query.RefundStatus.Value);
                }
                if (query.AfterSaleStatus != null)
                {
                    whereStr += " AND aso.AfterSaleStatus=@AfterSaleStatus";
                    parameters.Add("AfterSaleStatus", query.AfterSaleStatus.Value);
                }
                if (query.InterceptStatus != null)
                {
                    if (query.InterceptStatus == 1)
                    {
                        whereStr += " AND (aso.InterceptFxUserId!=0 OR aso.InterceptFxUserId IS NOT NULL)";
                        //parameters.Add("InterceptFxUserId", query.InterceptFxUserId);
                    }
                    else if (query.InterceptStatus < 1)
                    {
                        whereStr += " AND (aso.InterceptFxUserId =0 OR aso.InterceptFxUserId IS NULL)";
                        //parameters.Add("InterceptStatus", query.InterceptStatus);
                    }
                }

                if (!string.IsNullOrEmpty(query.PlatformOrderId))
                {
                    whereStr += " AND aso.PlatformOrderId LIKE @PlatformOrderId";
                    parameters.Add("PlatformOrderId", $"%{query.PlatformOrderId}%");
                }
                if (!string.IsNullOrEmpty(query.ReturnTrackingNo))
                {
                    whereStr += " AND aso.ReturnTrackingNo LIKE @ReturnTrackingNo";
                    parameters.Add("ReturnTrackingNo", $"%{query.ReturnTrackingNo}%");
                }
                if (!string.IsNullOrEmpty(query.ResendTrackingNo))
                {
                    whereStr += " AND aso.ResendTrackingNo LIKE @ResendTrackingNo";
                    parameters.Add("ResendTrackingNo", $"%{query.ResendTrackingNo}%");
                }
                if (!string.IsNullOrEmpty(query.AfterSaleRemark))
                {
                    whereStr += " AND aso.AfterSaleRemark LIKE @AfterSaleRemark";
                    parameters.Add("AfterSaleRemark", $"%{query.AfterSaleRemark}%");
                }
                if (query.SourceShopIds != null && query.SourceShopIds.Any())
                {
                    whereStr += $" AND aso.ShopId IN ({string.Join(",", query.SourceShopIds.Distinct().ToList())})";
                }
                if (!string.IsNullOrWhiteSpace(query.AfterSaleIds))
                {
                    joinQuerySql += " INNER JOIN dbo.FunStringToTable(@AfterSaleIds,',') t_AfterSaleId ON t_AfterSaleId.item =aso.AfterSaleId";
                    parameters.Add("AfterSaleIds", query.AfterSaleIds);
                    //whereStr += " AND aso.AfterSaleId IN @AfterSaleIds";
                    //parameters.Add("AfterSaleIds", query.AfterSaleIds.SplitToList(",").Distinct().ToList());
                }
                if (!string.IsNullOrWhiteSpace(query.AfterSaleCodes))
                {
                    joinQuerySql += " INNER JOIN dbo.FunStringToTable(@AfterSaleCodes,',') t_AfterSaleCode ON t_AfterSaleCode.item =aso.AfterSaleCode";
                    parameters.Add("AfterSaleCodes", query.AfterSaleCodes);
                    //whereStr += " AND aso.AfterSaleCode IN @AfterSaleCodes";
                    //parameters.Add("AfterSaleCodes", query.AfterSaleCodes.SplitToList(",").Distinct().ToList());
                }
                if (query.StartTime != null && query.EndTime != null)
                {
                    var queryDateField = "aso.ApplyTime";
                    var queryDateType = query.QueryDateType.ToLower();
                    if (queryDateType == "ocreatetime")
                        queryDateField = "aso.OrderCreateTime";
                    else if (queryDateType == "paytime")
                        queryDateField = "aso.OrderPayTime";
                    else if (queryDateType == "sendtime")
                        queryDateField = "aso.OrderSendTime";
                    else if (queryDateType == "aftersalestatustofinaltime")
                        queryDateField = "aso.AfterSaleStatusToFinalTime";

                    whereStr += $" AND {queryDateField} BETWEEN @StartTime AND @EndTime ";
                    parameters.Add("StartTime", query.StartTime.Value);
                    parameters.Add("EndTime", query.EndTime.Value);
                }
                ///留言旗帜
                if (!string.IsNullOrWhiteSpace(query.AfterSaleRemarkFlag))
                {
                    if (query.AfterSaleRemarkFlag == "-1")
                        whereStr += " AND(aso.AfterSaleRemarkFlag=-1 or aso.AfterSaleRemarkFlag is null)";
                    else
                        whereStr += " AND aso.AfterSaleRemarkFlag=@AfterSaleRemarkFlag";
                    parameters.Add("AfterSaleRemarkFlag", query.AfterSaleRemarkFlag);
                }
                ///平台售后备注
                if (!string.IsNullOrWhiteSpace(query.PlatAfterSaleRemark))
                {
                    whereStr += " AND aso.PlatAfterSaleRemark LIKE @PlatAfterSaleRemark";
                    parameters.Add("PlatAfterSaleRemark", $"%{query.PlatAfterSaleRemark}%");
                }


                //需要作为二次条件
                secondWhereStr = whereStr;
                secondJoinQuerySql = joinQuerySql;

                if (!string.IsNullOrWhiteSpace(query.PlatformOrderIds))
                {
                    joinQuerySql += " INNER JOIN dbo.FunStringToTable(@PlatformOrderIds,',') t_PlatformOrderId ON t_PlatformOrderId.item =aso.PlatformOrderId";
                    parameters.Add("PlatformOrderIds", query.PlatformOrderIds);
                    //whereStr += " AND aso.PlatformOrderId IN @PlatformOrderIds";
                    //parameters.Add("PlatformOrderIds", query.PlatformOrderIds.SplitToList(",").Distinct().ToList());
                }

                if (query.UpFxUserIds != null && query.UpFxUserIds.Any())
                {
                    whereStr += $" AND pfn.UpFxUserId IN ({string.Join(",", query.UpFxUserIds.Distinct().ToList())})";
                    secondWhereStr += $" AND tmp.UpFxUserId IN ({string.Join(",", query.UpFxUserIds.Distinct().ToList())})";
                }

                if (query.DownFxUserIds != null && query.DownFxUserIds.Any())
                {
                    whereStr += $" AND pfn.DownFxUserId IN ({string.Join(",", query.DownFxUserIds.Distinct().ToList())})";
                    secondWhereStr += $" AND tmp.DownFxUserId IN ({string.Join(",", query.DownFxUserIds.Distinct().ToList())})";
                }

                if (!string.IsNullOrEmpty(query.LogicOrderId))
                {
                    whereStr += $" AND {tableName}.LogicOrderId LIKE @LogicOrderId";
                    parameters.Add("LogicOrderId", $"%{query.LogicOrderId}%");
                    if (query.SourceFlag == 1 || query.SourceFlag == 2)//只针对手工售后单
                    {
                        secondWhereStr += $" AND {tableName}.LogicOrderId LIKE @LogicOrderId";
                    }
                }
                if (!string.IsNullOrWhiteSpace(query.LogicOrderIds))
                {
                    joinQuerySql += $" INNER JOIN dbo.FunStringToTable(@LogicOrderIds,',') t_LogicOrderId ON t_LogicOrderId.item ={tableName}.LogicOrderId";
                    parameters.Add("LogicOrderIds", query.LogicOrderIds);
                    //whereStr += $" AND {tableName}.LogicOrderId IN @LogicOrderIds";
                    //parameters.Add("LogicOrderIds", query.LogicOrderIds.SplitToList(",").Distinct().ToList());
                    if (query.SourceFlag == 1 || query.SourceFlag == 2)//只针对手工售后单
                    {
                        secondJoinQuerySql += $" INNER JOIN dbo.FunStringToTable(@LogicOrderIds,',') t_LogicOrderId ON t_LogicOrderId.item ={tableName}.LogicOrderId";
                        //secondWhereStr += $" AND {tableName}.LogicOrderId IN @LogicOrderIds";
                    }
                }
                //只有平台单才搜索逻辑单状态
                if (query.SourceFlag == 0)
                {

                    if (!string.IsNullOrWhiteSpace(query.SellerRemarkFlag))
                    {
                        if (query.SellerRemarkFlag== "0")
                            whereStr += " AND (ISNULL(aso.SellerRemarkFlag, '') = '' OR aso.SellerRemarkFlag = '0')";
                        else
                        {
                            whereStr += " AND aso.SellerRemarkFlag=@SellerRemarkFlag";
                            parameters.Add("SellerRemarkFlag", query.SellerRemarkFlag);
                        }

                    }
                    if (!string.IsNullOrWhiteSpace(query.SellerRemark))
                    {
                        whereStr += " AND aso.SellerRemark LIKE @SellerRemark";
                        parameters.Add("SellerRemark", $"%{query.SellerRemark}%");
                    }
                    //发货状态
                    if (query.SendState.HasValue)
                    {
                        // 时间判断修改为 2000年，因为有些平台未发货，会传值为1970-01-01 08:00:00,目前发现的有快手平台
                        if (query.SendState.Value == 1)
                            whereStr += " AND  aso.OrderSendTime>'2000-01-01'";
                        else
                            whereStr += " AND (aso.OrderSendTime IS NULL OR  aso.OrderSendTime<'2000-01-01') ";
                    }
                    //分销商售后申请
                    if (query.ApplyAfterSaleStatus.HasValue)
                    {
                        if (query.ApplyAfterSaleStatus.Value == 1)
                            whereStr += " AND por.PurchaseOrderRefundStatus<>''";
                        else
                            whereStr += " AND por.SourcePlatformOrderId IS NOT NULL AND (por.PurchaseOrderRefundStatus IS NULL OR por.PurchaseOrderRefundStatus='') ";
                    }
                    //分销商打印状态
                    if (query.OrderPrintStatus.HasValue)
                    {
                        whereStr += "AND lo.PrintState = @PrintState";
                        parameters.Add("PrintState", query.OrderPrintStatus.Value);
                    }
                }
                #endregion
            }
            sql += joinQuerySql;
            sql += whereFxUser;
            sql += whereStr;
            //排序
            sql += " ORDER BY asoi.Id DESC";

            #region 拼SQL

            var rTempTableName = tempTableName + "_2";
            var sTempTableName = tempTableName + "_3";


            var lastSQL = $@"-- 查询售后单
-- 1.满足条件订单存入临时表
{sql}
{"option(loop join)"}

-- 2.列表显示查询结果
SELECT IDENTITY(INT,1,1) AS RowIndex,* INTO #temp{rTempTableName} FROM (
	SELECT TOP 1000000 ItemId,
           AfterSaleId,
           PlatformOrderId,
           UpFxUserId,
           DownFxUserId,
           PathFlowCode,
           OnlineSendTime,
           ErpState,
           OrderPrintState,
           IsPreviewed,
           ExpressPrintTime,
           LastWaybillCode,
           SourceLogicOrderId,
           MergeredOrderId,
    ROW_NUMBER() OVER(PARTITION BY PlatformOrderId,AfterSaleId ORDER BY ItemId desc) rn
	FROM #temp{tempTableName} o 
) AS tmp WHERE tmp.rn =1;

-- 3.查询记录数
SELECT COUNT(1) FROM #temp{rTempTableName}

-- 取当前页数据
SELECT * INTO #temp{sTempTableName}
        FROM #temp{rTempTableName}
        ORDER BY RowIndex OFFSET {(query.PageIndex - 1) * query.PageSize} ROWS FETCH NEXT {query.PageSize} ROWS ONLY;

-- 4.查售后相关数据
SELECT aso.Id,aso.AfterSaleId,aso.TradeType,asoi.AfterSaleId,aso.AfterSaleCode,aso.PlatformOrderId,aso.OrderCode,aso.ShopId,aso.FxUserId,aso.InterceptFxUserId,
aso.PlatformType,aso.PathFlowCode,aso.PlatAfterSaleStatus,aso.AfterSaleStatus,aso.PlatAfterSaleType,aso.AfterSaleType,aso.PlatRefundStatus,aso.RefundStatus,aso.PlatArbitrateStatus,aso.ArbitrateStatus,aso.RefundTotalAmount,aso.RefundPostAmount,aso.RefundPromotionAmount,aso.RealRefundAmount,aso.ApplyTime,aso.OverdueTime,aso.PlatUpdateTime,aso.Reason,aso.ReasonCode,aso.ReasonRemark,aso.ReturnTrackingNo,aso.ReturnCompanyName,aso.ReturnCompanyCode,aso.ReturnLogisticsTime,aso.ResendTrackingNo,aso.ResendCompanyName,aso.ResendCompanyCode,aso.ResendLogisticsTime,aso.SourceFlag,aso.StockState,aso.AfterSaleRemark,aso.CreateBy,aso.CreateTime,
aso.LogicOrderId,aso.OrderCreateTime,aso.OrderPayTime,aso.OrderSendTime,aso.AftersaleStatusToFinalTime,tmp.ErpState,tmp.UpFxUserId,tmp.DownFxUserId,tmp.PathFlowCode LPathFlowCode,tmp.OnlineSendTime,tmp.IsPreviewed,tmp.ExpressPrintTime,tmp.LastWaybillCode,tmp.SourceLogicOrderId,tmp.MergeredOrderId,aso.ToName,aso.ToPhone,aso.ToProvince,aso.ToCity,aso.ToCounty,aso.ToAddress,aso.ToFullAddress,aso.PlatAfterSaleRemark,aso.AfterSaleRemarkFlag,aso.SellerRemark,aso.SellerRemarkFlag,
asoi.Id,asoi.AfterSaleCode,asoi.OrderItemCode,asoi.AfterSaleCount,asoi.RefundAmount,asoi.StockState,asoi.OrderCode,asoi.AfterSaleItemCode,tmp.OrderPrintState,asoi.SkuId,asoi.SkuCode,asoi.ProductId,asoi.ProductCode,asoi.ProductSubject,asoi.ProductCargoNumber,asoi.CargoNumber,asoi.ProductImgUrl,asoi.Price,asoi.OrignalCount,asoi.Color,asoi.Size,asoi.ItemStatus,asoi.ItemRefundStatus,asoi.PrintState,asoi.AfterSaleId {baseofptskurelationFields}
,ext.Id,ext.AfterSaleCode, ext.ExchangeGoods, ext.evidence
FROM #temp{sTempTableName} tmp WITH(NOLOCK)
INNER JOIN AfterSaleOrder aso WITH (NOLOCK) ON aso.PlatformOrderId = tmp.PlatformOrderId
LEFT JOIN AfterSaleOrderExt ext WITH(NOLOCK) ON aso.AfterSaleCode = ext.AfterSaleCode
-- INNER JOIN (
--    SELECT PlatformOrderId FROM  #temp{rTempTableName}
-- 	ORDER BY RowIndex
--    OFFSET {(query.PageIndex - 1) * query.PageSize} ROWS FETCH NEXT {query.PageSize} ROWS ONLY
--) tmp ON aso.PlatformOrderId=tmp.PlatformOrderId
-- INNER JOIN #temp{tempTableName} tmp2 ON tmp.PlatformOrderId=tmp2.PlatformOrderId 
INNER JOIN AfterSaleOrderItem asoi WITH(NOLOCK) ON tmp.ItemId = asoi.Id AND aso.AfterSaleCode=asoi.AfterSaleCode
{baseofptskurelationJoinSql}
{secondJoinQuerySql}
WHERE 1=1 {secondWhereStr} ORDER BY aso.Id DESC
option(force order,loop join)
";
            #endregion

            #region 记录执行的SQL语句
            if (CustomerConfig.IsDebug)
            {
                var realSql = GetRealSql(lastSQL, parameters);
                var sqllog = WriteSqlToLog(realSql, parameters, "AfterSaleSql.txt", "FxOrderSql", "GetAfterSaleOrderList");
            }
            #endregion
             
            Stopwatch sw = new Stopwatch();
            sw.Start();

            var db = this.DbConnection;

            LocalAcCode.FuncTest(() => db = Dapper.DbUtility.GetConnection("server=*************;uid=sa;pwd=**********;database=AlibabaFenFaDB;max pool size=512;"));
            
            var grid = db.QueryMultiple(lastSQL, param: parameters);
            var totalCount = grid.Read<int>().FirstOrDefault();

            var itemList = new List<AfterSaleOrderItemResult>();
            var lookUp = new Dictionary<string, AfterSalePageResult>();
            grid.Read<AfterSaleOrderResult, AfterSaleOrderItemResult, AfterSaleOrderExt, AfterSalePageResult>((o, oi, ext) =>
            {
                //平台订单级
                AfterSalePageResult porder = null;
                var key = o.PlatformOrderId;
                if (!lookUp.TryGetValue(key, out porder))
                {
                    porder = new AfterSalePageResult
                    {
                        OrderCode = o.OrderCode,
                        PlatformOrderId = o.PlatformOrderId,
                        PlatformType = o.PlatformType,
                        OrderCreateTime = o.OrderCreateTime,
                        OrderPayTime = o.OrderPayTime,
                        OrderSendTime = o.OrderSendTime,
                        ShopId = o.ShopId,
                        FxUserId = o.FxUserId,
                        UpFxUserId = o.UpFxUserId,
                        DownFxUserId = o.DownFxUserId,
                        AfterSaleId = o.AfterSaleId,
                        IsOfflineNoSku = o.TradeType == "OfflineNoSku",
                        Orders = new List<AfterSaleOrderResult>()
                    };
                    lookUp.Add(key, porder);
                }

                //售后单级
                if (!porder.Orders.Any(a => a.AfterSaleCode == o.AfterSaleCode))
                    porder.Orders.Add(o);

                //售后单项
                if (!itemList.Any(a => a.AfterSaleCode == oi.AfterSaleCode && a.OrderItemCode == oi.OrderItemCode))
                    itemList.Add(oi);

                // 售后单-扩展表项
                if (ext != null)
                {
                    var order = porder.Orders.Find(a => a.AfterSaleCode == ext.AfterSaleCode);
                    if (order != null)
                        order.Ext = ext;
                }

                return porder;
            }).ToList();

            var result = lookUp.Values.ToList();
            //更新结果集的售后单项
            if (result != null && result.Any())
            {
                //订单
                result.SelectMany(a => a.Orders).ToList().ForEach(order =>
                {
                    order.Items = itemList.Where(b => b.AfterSaleCode == order.AfterSaleCode).ToList();
                });

                //设置运单号
                try
                {
                    SetWaybillCodes(result, query.FxUserId);
                }
                catch (Exception e)
                {
                    Log.Debug($"售后单设置运单号异常，异常信息：{e.Message}，堆栈信息：{e.StackTrace}.",
                        $"AfterSaleSetWaybillCodesError_{DateTime.Now:yyyy-MM-dd}.log");
                }
                //获取1688厂家售后单所对应分销商订单信息
                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                {
                    //测试
                    //result.ForEach(o =>
                    //{
                    //    o.DistributionOrder = new AfterSalePageResult
                    //    {
                    //        FxUserId = 5,
                    //        ShopId = 3156882,
                    //        PlatformOrderId = "2300300019899578",
                    //        OrderCreateTime = DateTime.Now,
                    //        UpFxUserId = 5,
                    //        ShopName = "我乐时代",
                    //        PlatformType = "TouTiao"
                    //    };
                    //});
                    //根据1688订单ID，获取采购单关联信息
                    var platformOrderIds = result.Where(m => m.PlatformType == PlatformType.Alibaba.ToString())
                        .Select(m => m.PlatformOrderId).Distinct().ToList();
                    if (platformOrderIds.Any())
                    {
                        var purchaseOrderRelations = _purchaseOrderRelationRepository.GetListAndItems(platformOrderIds,
                            whereFieldName: "o.PurchasePlatformOrderId");
                        if (purchaseOrderRelations != null && purchaseOrderRelations.Any())
                        {
                            //分销商用户ID
                            var sourceFxUserIds = purchaseOrderRelations.Select(m => m.CreateFxUserId).Distinct()
                                .ToList();
                            //数据库配置信息
                            var dbConfigs = new DbConfigRepository().GetListByFxUserIdsForCurrentCloudPlatform(sourceFxUserIds);
                            var dbNameWithEncrypts = dbConfigs?.Select(m => m.DbNameConfig.DbName).Distinct().Select(m =>
                                new
                                {
                                    DbName = m,
                                    DbNameEncrypt = DES.EncryptDES(m, CustomerConfig.LoginCookieEncryptKey)
                                }).ToList();

                            result.ForEach(o =>
                            {
                                var purchaseOrderRelation =
                                    purchaseOrderRelations.FirstOrDefault(m =>
                                        m.PurchasePlatformOrderId == o.PlatformOrderId);
                                if (purchaseOrderRelation != null)
                                {
                                    var dbConfig = dbConfigs.FirstOrDefault(m =>
                                        m.DbConfig.UserId == purchaseOrderRelation.CreateFxUserId);
                                    o.DistributionOrder = new AfterSalePageResult
                                    {
                                        FxUserId = purchaseOrderRelation.CreateFxUserId,
                                        ShopId = purchaseOrderRelation.SourceShopId,
                                        PlatformOrderId = purchaseOrderRelation.SourcePlatformOrderId,
                                        OrderCreateTime = purchaseOrderRelation.CreateTime,
                                        UpFxUserId = purchaseOrderRelation.CreateFxUserId,
                                        DbNameEncrypt = dbNameWithEncrypts
                                            .FirstOrDefault(m => m.DbName == dbConfig?.DbNameConfig.DbName)
                                            ?.DbNameEncrypt
                                    };
                                }
                            });
                        }
                    }
                }
            }
			sw.Stop();
            Log.Debug($"AfterSaleOrderRepository=>GetAfterSaleOrderList(拼字符串)，耗时：{sw.Elapsed.TotalMilliseconds} ");

            return Tuple.Create(totalCount, result);
        }

        /// <summary>
        /// 分页查询
        /// 已发售后订单
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<AfterSalePageResult>> GetAfterSaleOrderListBySended(AfterSaleOrderQuery query)
        {
            var parameters = new DynamicParameters();

            #region 混合查询基础商品
            var baseofptskurelationJoinSql = string.Empty;
            var baseofptskurelationFields = string.Empty;

            var fxUserId = query.FxUserId;
            var baseofptskurelationSwitch = query.IsBaseProduceCombine;
            if (baseofptskurelationSwitch)
            {
                baseofptskurelationJoinSql = $@" LEFT JOIN BaseOfPtSkuRelation rel WITH ( NOLOCK ) ON asoi.ProductCode= rel.ProductCode 
AND asoi.SkuCode= rel.ProductSkuCode 
AND rel.FxUserId=@fxUserId AND rel.Status=1";
                parameters.Add("fxUserId", fxUserId);
                baseofptskurelationFields = $",CASE WHEN rel.BaseProductSkuUid IS NULL THEN 0 ELSE 1 END AS IsRelationBaseProduct,rel.BaseProductSkuUid AS BaseProductSkuUid";
            }
            #endregion
            var tempTableName = GetTempTableName(BusinessTypes.AfterSaleOrder, query.FxUserId);
            var rTempTableName = tempTableName + "_2";
            // sql优化需求：https://www.tapd.cn/tapd_fe/32787034/story/detail/1132787034001046398
            var pTempTableName = tempTableName + "_4";
            // 系统售后单，以LogicOrder路径流为准

            var tempSql = $@"
-- sql优化需求，将路径流子查询放入存入临时表
SELECT DISTINCT t1.SourceFxUserId  INTO #temp{pTempTableName} FROM PathFlow t1 WITH(NOLOCK) INNER JOIN PathFlowNode t2 WITH(NOLOCK) ON t1.PathFlowCode = t2.PathFlowCode WHERE t2.FxUserId=@FxUserId;";
            var sql =
                $@"

SELECT DISTINCT TOP 1000000  asoi.Id ASItemId,asoi.PlatformOrderId,pfn.UpFxUserId,pfn.DownFxUserId,pfn.PathFlowCode,
--lo.OnlineSendTime,lo.PrintState as OrderPrintState,lo.IsPreviewed,lo.ExpressPrintTime,lo.LastWaybillCode,lo.LogicOrderId AS SourceLogicOrderId,lo.MergeredOrderId
aso.OrderSendTime AS OnlineSendTime,aso.PrintState AS OrderPrintState,NULL AS IsPreviewed,NULL AS ExpressPrintTime,NULL AS LastWaybillCode,aso.LogicOrderId AS SourceLogicOrderId,NULL AS MergeredOrderId 
                        FROM AfterSaleOrderItem asoi WITH(NOLOCK)
                        INNER JOIN AfterSaleOrder aso WITH(NOLOCK) ON aso.AfterSaleCode = asoi.AfterSaleCode
                        INNER JOIN #temp{pTempTableName} AS tfx ON tfx.SourceFxUserId=aso.FxUserId
                        --INNER JOIN LogicOrder lo WITH(NOLOCK) ON lo.PlatformOrderId = asoi.PlatformOrderId AND lo.ShopId=asoi.ShopId  
                        --INNER JOIN LogicOrderItem loi WITH(NOLOCK) ON loi.OrderItemCode = asoi.OrderItemCode AND lo.LogicOrderId=loi.LogicOrderId
                        INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode = asoi.PathFlowCode --lo.PathFlowCode
                        {(query.ApplyAfterSaleStatus.HasValue ? "LEFT JOIN PurchaseOrderRelation por WITH(NOLOCK) ON por.SourcePlatformOrderId = asoi.PlatformOrderId AND por.Status = 1 AND por.SourceLogicOrderId = asoi.LogicOrderId --lo.LogicOrderId" : string.Empty)}
                        WHERE pfn.FxUserId=@FxUserId ";

            string whereStr = "";      //1.查出OrderCode条件
            string secondWhereStr = "";//二次查询条件
            //var tableName = "lo";//LogicOrderId查询的表名，手工单为aso，平台单为lo
            parameters.Add("FxUserId", query.FxUserId);

            whereStr += " AND SourceFlag=@SourceFlag";
            parameters.Add("SourceFlag", query.SourceFlag);


            var isSearchId = (query.Ids != null && query.Ids.Any());

            //只搜索指定Id
            if (isSearchId)
            {
                whereStr += " AND aso.Id IN @Ids";
                parameters.Add("Ids", query.Ids);

                secondWhereStr = whereStr;
            }
            else
            {
                #region 查询其他条件
                if (!string.IsNullOrEmpty(query.PlatformType))
                {
                    whereStr += " AND aso.PlatformType=@PlatformType";
                    parameters.Add("PlatformType", query.PlatformType);
                }
                if (query.AfterSaleType != null)
                {
                    whereStr += " AND aso.AfterSaleType=@AfterSaleType";
                    parameters.Add("AfterSaleType", query.AfterSaleType.Value);
                }
                if (query.RefundStatus != null)
                {
                    whereStr += " AND aso.RefundStatus=@RefundStatus";
                    parameters.Add("RefundStatus", query.RefundStatus.Value);
                }
                if (query.AfterSaleStatus != null)
                {
                    whereStr += " AND aso.AfterSaleStatus=@AfterSaleStatus";
                    parameters.Add("AfterSaleStatus", query.AfterSaleStatus.Value);
                }
                if (!string.IsNullOrEmpty(query.PlatformOrderId))
                {
                    whereStr += " AND aso.PlatformOrderId LIKE @PlatformOrderId";
                    parameters.Add("PlatformOrderId", $"%{query.PlatformOrderId}%");
                }
                if (!string.IsNullOrEmpty(query.ReturnTrackingNo))
                {
                    whereStr += " AND aso.ReturnTrackingNo LIKE @ReturnTrackingNo";
                    parameters.Add("ReturnTrackingNo", $"%{query.ReturnTrackingNo}%");
                }
                if (!string.IsNullOrEmpty(query.ResendTrackingNo))
                {
                    whereStr += " AND aso.ResendTrackingNo LIKE @ResendTrackingNo";
                    parameters.Add("ResendTrackingNo", $"%{query.ResendTrackingNo}%");
                }
                if (!string.IsNullOrEmpty(query.AfterSaleRemark))
                {
                    whereStr += " AND aso.AfterSaleRemark LIKE @AfterSaleRemark";
                    parameters.Add("AfterSaleRemark", $"%{query.AfterSaleRemark}%");
                }
                if (query.SourceShopIds != null && query.SourceShopIds.Any())
                {
                    whereStr += $" AND aso.ShopId IN ({string.Join(",", query.SourceShopIds.Distinct().ToList())})";
                }
                if (!string.IsNullOrWhiteSpace(query.AfterSaleIds))
                {
                    whereStr += " AND aso.AfterSaleId IN @AfterSaleIds";
                    parameters.Add("AfterSaleIds", query.AfterSaleIds.SplitToList(",").Distinct().ToList());
                }
                if (!string.IsNullOrWhiteSpace(query.AfterSaleCodes))
                {
                    whereStr += " AND aso.AfterSaleCode IN @AfterSaleCodes";
                    parameters.Add("AfterSaleCodes", query.AfterSaleCodes.SplitToList(",").Distinct().ToList());
                }
                if (query.StartTime != null && query.EndTime != null)
                {
                    var queryDateField = "aso.ApplyTime";
                    var queryDateType = query.QueryDateType.ToLower();
                    if (queryDateType == "ocreatetime")
                        queryDateField = "aso.OrderCreateTime";
                    else if (queryDateType == "paytime")
                        queryDateField = "aso.OrderPayTime";
                    else if (queryDateType == "sendtime")
                        queryDateField = "aso.OrderSendTime";
                    else if (queryDateType == "aftersalestatustofinaltime")
                        queryDateField = "aso.AftersaleStatusToFinalTime";


                    whereStr += $" AND {queryDateField} BETWEEN @StartTime AND @EndTime ";
                    parameters.Add("StartTime", query.StartTime.Value);
                    parameters.Add("EndTime", query.EndTime.Value);
                }

                //需要作为二次条件
                secondWhereStr = whereStr;

                if (!string.IsNullOrWhiteSpace(query.PlatformOrderIds))
                {
                    whereStr += " AND aso.PlatformOrderId IN @PlatformOrderIds";
                    parameters.Add("PlatformOrderIds", query.PlatformOrderIds.SplitToList(",").Distinct().ToList());
                }

                if (query.UpFxUserIds != null && query.UpFxUserIds.Any())
                {
                    whereStr += $" AND pfn.UpFxUserId IN ({string.Join(",", query.UpFxUserIds.Distinct().ToList())})";
                    secondWhereStr += $" AND tmp2.UpFxUserId IN ({string.Join(",", query.UpFxUserIds.Distinct().ToList())})";
                }

                if (query.DownFxUserIds != null && query.DownFxUserIds.Any())
                {
                    whereStr += $" AND pfn.DownFxUserId IN ({string.Join(",", query.DownFxUserIds.Distinct().ToList())})";
                    secondWhereStr += $" AND tmp2.DownFxUserId IN ({string.Join(",", query.DownFxUserIds.Distinct().ToList())})";
                }
                ///留言旗帜
                if (!string.IsNullOrWhiteSpace(query.AfterSaleRemarkFlag))
                {
                    if (query.AfterSaleRemarkFlag == "-1")
                        whereStr += " AND (aso.AfterSaleRemarkFlag is null or aso.AfterSaleRemarkFlag=-1)";
                    else
                        whereStr += " AND aso.AfterSaleRemarkFlag=@AfterSaleRemarkFlag";
                    parameters.Add("AfterSaleRemarkFlag", query.AfterSaleRemarkFlag);
                }
                ///平台售后备注
                if (!string.IsNullOrWhiteSpace(query.PlatAfterSaleRemark))
                {
                    whereStr += " AND aso.PlatAfterSaleRemark LIKE @PlatAfterSaleRemark";
                    parameters.Add("PlatAfterSaleRemark", $"%{query.PlatAfterSaleRemark}%");
                }

                //只有平台单才搜索逻辑单状态
                if (query.SourceFlag == 0)
                {
                    if (!string.IsNullOrWhiteSpace(query.SellerRemarkFlag))
                    {
                        whereStr += " AND aso.SellerRemarkFlag=@SellerRemarkFlag";
                        parameters.Add("SellerRemarkFlag", query.SellerRemarkFlag);
                    }
                    if (!string.IsNullOrWhiteSpace(query.SellerRemark))
                    {
                        whereStr += " AND aso.SellerRemark LIKE @SellerRemark";
                        parameters.Add("SellerRemark", $"%{query.SellerRemark}%");
                    }

                    //发货状态
                    if (query.SendState.HasValue)
                    {
                        // 时间判断修改为 2000年，因为有些平台未发货，会传值为1970-01-01 08:00:00,目前发现的有快手平台
                        if (query.SendState.Value == 1)
                            whereStr += " AND  aso.OrderSendTime>'2000-01-01'";
                        else
                            whereStr += " AND (aso.OrderSendTime IS NULL OR  aso.OrderSendTime<'2000-01-01') ";
                    }
                    //分销商售后申请
                    if (query.ApplyAfterSaleStatus.HasValue)
                    {
                        if (query.ApplyAfterSaleStatus.Value == 1)
                            whereStr += " AND por.PurchaseOrderRefundStatus<>''";
                        else
                            whereStr += " AND por.SourcePlatformOrderId IS NOT NULL AND (por.PurchaseOrderRefundStatus IS NULL OR por.PurchaseOrderRefundStatus='') ";
                    }
                    //分销商打印状态
                    if (query.OrderPrintStatus.HasValue)
                    {
                        //whereStr += " AND lo.PrintState = @PrintState";
                        whereStr += " AND aso.PrintState = @PrintState";
                        parameters.Add("PrintState", query.OrderPrintStatus.Value);
                    }

                }

                if (query.InterceptStatus != null)
                {
                    if (query.InterceptStatus == 1)
                    {
                        whereStr += " AND (aso.InterceptFxUserId!=0 OR aso.InterceptFxUserId IS NOT NULL)";
                        //parameters.Add("InterceptFxUserId", query.InterceptFxUserId);
                    }
                    else if (query.InterceptStatus < 1)
                    {
                        whereStr += " AND (aso.InterceptFxUserId =0 OR aso.InterceptFxUserId IS NULL)";
                        //parameters.Add("InterceptStatus", query.InterceptStatus);
                    }
                }
                #endregion
            }

            sql += whereStr;
            //排序
            sql += " ORDER BY asoi.Id DESC";

            #region 拼SQL

           

            var lastSQL = $@"-- 查询售后单
{tempSql}
-- 1.满足条件订单存入临时表

SELECT IDENTITY(INT,1,1) AS RowIndex,* INTO #temp{tempTableName} FROM (     
{sql}
) AS tmp

-- 2.列表显示查询结果
SELECT IDENTITY(INT,1,1) AS RowIndex,* INTO #temp{rTempTableName} FROM (
	SELECT TOP 1000000 PlatformOrderId,MAX(o.RowIndex) MaxRowIndex
	FROM #temp{tempTableName} o 
    GROUP BY PlatformOrderId ORDER BY MAX(o.RowIndex)
) AS tmp


-- 3.查询记录数
SELECT COUNT(1) FROM #temp{rTempTableName}

-- 4.查售后相关数据
SELECT aso.Id,aso.AfterSaleId,asoi.AfterSaleId,aso.AfterSaleCode,aso.PlatformOrderId,aso.OrderCode,aso.ShopId,aso.FxUserId,aso.InterceptFxUserId,
aso.PlatformType,aso.PathFlowCode,aso.PlatAfterSaleStatus,aso.AfterSaleStatus,aso.PlatAfterSaleType,aso.AfterSaleType,aso.PlatRefundStatus,aso.RefundStatus,aso.PlatArbitrateStatus,aso.ArbitrateStatus,aso.RefundTotalAmount,aso.RefundPostAmount,aso.RefundPromotionAmount,aso.RealRefundAmount,aso.ApplyTime,aso.OverdueTime,aso.PlatUpdateTime,aso.Reason,aso.ReasonCode,aso.ReasonRemark,aso.ReturnTrackingNo,aso.ReturnCompanyName,aso.ReturnCompanyCode,aso.ReturnLogisticsTime,aso.ResendTrackingNo,aso.ResendCompanyName,aso.ResendCompanyCode,aso.ResendLogisticsTime,aso.SourceFlag,aso.StockState,aso.AfterSaleRemark,aso.CreateBy,aso.CreateTime,
aso.LogicOrderId,aso.OrderCreateTime,aso.OrderPayTime,aso.OrderSendTime,tmp2.UpFxUserId,tmp2.DownFxUserId,tmp2.PathFlowCode LPathFlowCode,tmp2.OnlineSendTime,tmp2.IsPreviewed,tmp2.ExpressPrintTime,tmp2.LastWaybillCode,tmp2.SourceLogicOrderId,tmp2.MergeredOrderId,aso.ToName,aso.ToPhone,aso.ToProvince,aso.ToCity,aso.ToCounty,aso.ToAddress,aso.ToFullAddress,aso.PlatAfterSaleRemark,aso.AfterSaleRemarkFlag,aso.SellerRemark,aso.SellerRemarkFlag,aso.AftersaleStatusToFinalTime,
asoi.Id,asoi.AfterSaleCode,asoi.OrderItemCode,asoi.AfterSaleCount,asoi.RefundAmount,asoi.StockState,asoi.OrderCode,asoi.AfterSaleItemCode,tmp2.OrderPrintState,asoi.SkuId,asoi.SkuCode,asoi.ProductId,asoi.ProductCode,asoi.ProductSubject,asoi.ProductCargoNumber,asoi.CargoNumber,asoi.ProductImgUrl,asoi.Price,asoi.OrignalCount,asoi.Color,asoi.Size,asoi.ItemStatus,asoi.ItemRefundStatus,asoi.PrintState,tmp2.DownFxUserId,asoi.AfterSaleId {baseofptskurelationFields}
,ext.Id,ext.AfterSaleCode, ext.ExchangeGoods, ext.evidence
FROM AfterSaleOrder aso WITH(NOLOCK)
LEFT JOIN AfterSaleOrderExt ext WITH(NOLOCK) ON aso.AfterSaleCode = ext.AfterSaleCode
INNER JOIN (
    SELECT PlatformOrderId FROM  #temp{rTempTableName}
 	ORDER BY RowIndex
    OFFSET {(query.PageIndex - 1) * query.PageSize} ROWS FETCH NEXT {query.PageSize} ROWS ONLY
) tmp ON aso.PlatformOrderId=tmp.PlatformOrderId
INNER JOIN #temp{tempTableName} tmp2 ON tmp.PlatformOrderId=tmp2.PlatformOrderId 
INNER JOIN AfterSaleOrderItem asoi WITH(NOLOCK) ON tmp2.ASItemId=asoi.Id AND aso.AfterSaleCode=asoi.AfterSaleCode
{baseofptskurelationJoinSql}
WHERE 1=1 {secondWhereStr} ORDER BY aso.Id DESC
";
            #endregion

            #region 记录执行的SQL语句
            if (CustomerConfig.IsDebug)
            {
                var realSql = GetRealSql(lastSQL, parameters);
                var sqllog = WriteSqlToLog(realSql, parameters, "AfterSaleSql.txt", "FxOrderSql", "GetAfterSaleOrderList");
            }
            #endregion

            Stopwatch sw = new Stopwatch();
            sw.Start();

            var db = this.DbConnection;
            var grid = db.QueryMultiple(lastSQL, param: parameters);
            var totalCount = grid.Read<int>().FirstOrDefault();

            var itemList = new List<AfterSaleOrderItemResult>();
            var lookUp = new Dictionary<string, AfterSalePageResult>();
            grid.Read<AfterSaleOrderResult, AfterSaleOrderItemResult, AfterSaleOrderExt, AfterSalePageResult>((o, oi, ext) =>
            {
                //平台订单级
                AfterSalePageResult porder = null;
                var key = o.PlatformOrderId;
                if (!lookUp.TryGetValue(key, out porder))
                {
                    porder = new AfterSalePageResult
                    {
                        OrderCode = o.OrderCode,
                        PlatformOrderId = o.PlatformOrderId,
                        PlatformType = o.PlatformType,
                        OrderCreateTime = o.OrderCreateTime,
                        OrderPayTime = o.OrderPayTime,
                        OrderSendTime = o.OrderSendTime,
                        ShopId = o.ShopId,
                        FxUserId = o.FxUserId,
                        UpFxUserId = o.UpFxUserId,
                        DownFxUserId = o.DownFxUserId,
                        Orders = new List<AfterSaleOrderResult>(),
                        AfterSaleCreateTime = o.CreateTime,
                        AfterSaleId = o.AfterSaleId,
                        LogicOrderId = o.LogicOrderId
                    };
                    lookUp.Add(key, porder);
                }
                o.ErpState = "sended";
                //售后单级
                if (!porder.Orders.Any(a => a.AfterSaleCode == o.AfterSaleCode))
                    porder.Orders.Add(o);

                //售后单项
                if (!itemList.Any(a => a.AfterSaleCode == oi.AfterSaleCode && a.OrderItemCode == oi.OrderItemCode))
                    itemList.Add(oi);

                // 售后单-扩展表项
                if (ext != null)
                {
                    var order = porder.Orders.Find(a => a.AfterSaleCode == ext.AfterSaleCode);
                    if (order != null)
                        order.Ext = ext;
                }

                return porder;
            }).ToList();

            var result = lookUp.Values.ToList();

			//更新结果集的售后单项
			if (result != null && result.Any())
            {
                //订单
                result.SelectMany(a => a.Orders).ToList().ForEach(order =>
                {
                    order.Items = itemList.Where(b => b.AfterSaleCode == order.AfterSaleCode).ToList();
                });

                //设置运单号
                try
                {
                    SetWaybillCodes(result, query.FxUserId, true);
                }
                catch (Exception e)
                {
                    Log.Debug($"售后单设置运单号异常，异常信息：{e.Message}，堆栈信息：{e.StackTrace}.",
                        $"AfterSaleSetWaybillCodesError_{DateTime.Now:yyyy-MM-dd}.log");
                }
                //获取1688厂家售后单所对应分销商订单信息
                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                {
                    //测试
                    //result.ForEach(o =>
                    //{
                    //    o.DistributionOrder = new AfterSalePageResult
                    //    {
                    //        FxUserId = 5,
                    //        ShopId = 3156882,
                    //        PlatformOrderId = "2300300019899578",
                    //        OrderCreateTime = DateTime.Now,
                    //        UpFxUserId = 5,
                    //        ShopName = "我乐时代",
                    //        PlatformType = "TouTiao"
                    //    };
                    //});
                    //根据1688订单ID，获取采购单关联信息
                    var platformOrderIds = result.Where(m => m.PlatformType == PlatformType.Alibaba.ToString())
                        .Select(m => m.PlatformOrderId).Distinct().ToList();
                    if (platformOrderIds.Any())
                    {
                        var purchaseOrderRelations = _purchaseOrderRelationRepository.GetListAndItems(platformOrderIds,
                            whereFieldName: "o.PurchasePlatformOrderId");
                        if (purchaseOrderRelations != null && purchaseOrderRelations.Any())
                        {
                            //分销商用户ID
                            var sourceFxUserIds = purchaseOrderRelations.Select(m => m.CreateFxUserId).Distinct()
                                .ToList();
                            //数据库配置信息
                            var dbConfigs = new DbConfigRepository().GetListByFxUserIdsForCurrentCloudPlatform(sourceFxUserIds);
                            var dbNameWithEncrypts = dbConfigs?.Select(m => m.DbNameConfig.DbName).Distinct().Select(m =>
                                new
                                {
                                    DbName = m,
                                    DbNameEncrypt = DES.EncryptDES(m, CustomerConfig.LoginCookieEncryptKey)
                                }).ToList();

                            result.ForEach(o =>
                            {
                                var purchaseOrderRelation =
                                    purchaseOrderRelations.FirstOrDefault(m =>
                                        m.PurchasePlatformOrderId == o.PlatformOrderId);
                                if (purchaseOrderRelation != null)
                                {
                                    var dbConfig = dbConfigs.FirstOrDefault(m =>
                                        m.DbConfig.UserId == purchaseOrderRelation.CreateFxUserId);
                                    o.DistributionOrder = new AfterSalePageResult
                                    {
                                        FxUserId = purchaseOrderRelation.CreateFxUserId,
                                        ShopId = purchaseOrderRelation.SourceShopId,
                                        PlatformOrderId = purchaseOrderRelation.SourcePlatformOrderId,
                                        OrderCreateTime = purchaseOrderRelation.CreateTime,
                                        UpFxUserId = purchaseOrderRelation.CreateFxUserId,
                                        DbNameEncrypt = dbNameWithEncrypts
                                            .FirstOrDefault(m => m.DbName == dbConfig?.DbNameConfig.DbName)
                                            ?.DbNameEncrypt
                                    };
                                }
                            });
                        }
                    }
                }
            }

            sw.Stop();
            Log.Debug($"AfterSaleOrderRepository=>GetAfterSaleOrderList(拼字符串)，耗时：{sw.Elapsed.TotalMilliseconds} ");

			return Tuple.Create(totalCount, result);
        }

        private void SetWaybillCodes(List<AfterSalePageResult> result, int fxUserId, bool isSended = false)
        {
            #region 获取运单号
            //获取运单号
            var waybillCodeRepository = new WaybillCodeRepository();
            var orders = result.SelectMany(m => m.Orders).ToList();
            var keys = orders.Where(o =>
                    (o.ExpressPrintTime.HasValue || (o.IsPreviewed.HasValue && o.IsPreviewed.Value) ||
                     !string.IsNullOrEmpty(o.LastWaybillCode)) && o.Items.All(m => m.OrderPrintState == 1))
                .Select(m => new { PlatformOrderId = m.SourceLogicOrderId, m.ShopId }).Distinct().Select(x => new OrderSelectKeyModel
                { PlatformOrderId = x.PlatformOrderId, ShopId = x.ShopId })
                .ToList();
            //已发货，冷热数据处理，需要通过平台订单号，取逻辑单号，然后进行对换处理
            if (isSended)
            {
                var ordersByPrint = orders.Where(o => o.Items.All(m => m.OrderPrintState == 1)).ToList();
                if (!ordersByPrint.Any())
                {
                    return;
                }

                var platformOrderIds = ordersByPrint.Select(m => m.PlatformOrderId).Distinct().ToList();
                if (!platformOrderIds.Any())
                {
                    return;
                }

                var fields = new List<string> { "LogicOrderId", "PlatformOrderId", "ShopId" };
                var logicOrdersByDictionary = new Dictionary<string, List<LogicOrder>>();
                var logicOrderRepository = new LogicOrderRepository();
                var logicOrders = logicOrderRepository.GetOnlyLogicOrderByPoIds(platformOrderIds, fields);
                logicOrders.ForEach(lo =>
                {
                    List<LogicOrder> outLogicOrders;
                    if (logicOrdersByDictionary.TryGetValue(lo.PlatformOrderId, out outLogicOrders))
                    {
                        if (outLogicOrders.All(m => m.LogicOrderId != lo.LogicOrderId))
                        {
                            outLogicOrders.Add(lo);
                        }
                    }
                    else
                    {
                        logicOrdersByDictionary.Add(lo.PlatformOrderId, new List<LogicOrder> { lo });
                    }
                });
                if (BaseSiteContext.CurrentNoThrow.IsUseColdDb)
                {
                    var repository =
                        new LogicOrderRepository(
                            BaseSiteContext.CurrentNoThrow.CurrentDbConfig.ColdDbConnectionString, true);
                    logicOrders = repository.GetOnlyLogicOrderByPoIds(platformOrderIds, fields);
                    logicOrders.ForEach(lo =>
                    {
                        List<LogicOrder> outLogicOrders;
                        if (logicOrdersByDictionary.TryGetValue(lo.PlatformOrderId, out outLogicOrders))
                        {
                            if (outLogicOrders.All(m => m.LogicOrderId != lo.LogicOrderId))
                            {
                                outLogicOrders.Add(lo);
                            }
                        }
                        else
                        {
                            logicOrdersByDictionary.Add(lo.PlatformOrderId, new List<LogicOrder> { lo });
                        }
                    });
                }

                keys = logicOrdersByDictionary.Values.SelectMany(m => m).ToList()
                    .Select(m => new { PlatformOrderId = m.LogicOrderId, m.ShopId, Pid = m.PlatformOrderId }).Distinct()
                    .Select(x =>
                        new OrderSelectKeyModel
                        { PlatformOrderId = x.PlatformOrderId, ShopId = x.ShopId, UniqueKey = x.Pid })
                    .ToList();
            }
            Log.Debug($"售后单设置运单号,相关信息：{keys.ToJson()}.",
                $"AfterSaleSetWaybillCodes_{DateTime.Now:yyyy-MM-dd}.log");
            //改造：查询底单记录，不判断PrintDataType
            var waybillCodes = waybillCodeRepository.GetWaybillCodeForOrders(keys, false, 1);
            waybillCodes = waybillCodes.Where(x => x.ExpressWayBillCode.IsNotNullOrEmpty()).ToList();
            Log.Debug($"售后单设置运单号,运单信息：{waybillCodes.ToJson(true)}.",
                $"AfterSaleSetWaybillCodes_{DateTime.Now:yyyy-MM-dd}.log");
            // 获取订单上下游关系
            var pathFlowRepository = new PathFlowRepository();
            var pathFlowNodeDic = new Dictionary<string, List<PathFlowNode>>();
            var pathFlowCodes = orders.Where(x => x.PathFlowCode.IsNotNullOrEmpty()).Select(x => x.PathFlowCode).Distinct().ToList();
            var wPathFlowCodes = waybillCodes.Select(x => x.PathFlowCode).Distinct().ToList();
            if (wPathFlowCodes.Any())
                pathFlowCodes = pathFlowCodes.Concat(wPathFlowCodes).Distinct().ToList();
            if (pathFlowCodes.Any())
            {
                var pathFlowNodes = pathFlowRepository.GetPathFlowNodeList(pathFlowCodes);
                pathFlowNodeDic = GetSortPathFlowNodes(pathFlowNodes);
            }

            //var sendHistorys = new List<SendHistory>();
            //var sendOrders = orders
            //    .Where(x => x.OnlineSendTime != null && !string.IsNullOrWhiteSpace(x.SourceLogicOrderId))
            //    .Select(x => x.SourceLogicOrderId).ToList();
            //if (sendOrders.Count > 0)
            //{
            //    var orderResendIds = orders.Where(x => x.OnlineSendTime != null && !string.IsNullOrWhiteSpace(x.MergeredOrderId)).Select(x => x.MergeredOrderId).ToList();
            //    sendOrders.AddRange(orderResendIds);
            //    sendHistorys = new SendHistoryRepository().GetSendHistoryListByLogicOrderIds(sendOrders).OrderByDescending(x => x.SendDate).ToList();
            //    // 添加多运单号发货记录
            //    if (sendHistorys.Any())
            //    {
            //        var sendHistoryIds = sendHistorys.Select(x => x.ID).Distinct().ToList();
            //        var shcSql = $"SELECT SendHistoryId,LogistiscBillNo,ExpressCode,ExpressName FROM P_SendHistoryChild WITH(NOLOCK) WHERE SendHistoryId IN({string.Join(",", sendHistoryIds)})";
            //        var shcs = DbConnection.Query<SendHistoryChild>(shcSql).ToList();
            //        foreach (var shc in shcs)
            //        {
            //            var sh = sendHistorys.FirstOrDefault(x => x.ID == shc.SendHistoryId);
            //            if (sh != null && sh.LogistiscBillNo != shc.LogistiscBillNo)
            //            {
            //                var shCopy = sh.ToJson().ToObject<SendHistory>();
            //                shCopy.LogistiscBillNo = shc.LogistiscBillNo;
            //                shCopy.ExpressCode = shc.ExpressCode;
            //                shCopy.ExpressName = shc.ExpressName;
            //                sendHistorys.Add(shCopy);
            //            }
            //        }
            //    }
            //    foreach (var item in sendHistorys)
            //    {
            //        var w = waybillCodes.FirstOrDefault(x => x.ExpressWayBillCode == item.LogistiscBillNo);
            //        if (w == null)
            //        {
            //            waybillCodes.Add(new WaybillCode
            //            {
            //                OrderId = item.OrderId,
            //                OrderIdJoin = item.OrderJoin,
            //                ExpressWayBillCode = item.LogistiscBillNo,
            //                FxUserId = item.FxUserId,
            //                ExpressNo = item.ExpressCode,
            //                ExpressName = item.ExpressName,
            //                CreateDate = item.SendDate,
            //                PathFlowCode = item.PathFlowCode,
            //                SendType = item.SendType,
            //                Status = 3,
            //            });
            //        }
            //    }
            //}
            waybillCodes = waybillCodes.Where(x => x.Status != 2).ToList();
            #endregion

            //处理运单号

            result.ForEach(o =>
            {
                var childPlatformOrderIds =
                    o.Orders.Select(m => m.SourceLogicOrderId).Distinct().ToList();
                if (isSended)
                {
                    childPlatformOrderIds = keys.Where(m => m.UniqueKey == o.PlatformOrderId)
                        .Select(m => m.PlatformOrderId).Distinct().ToList();
                }

				o.WaybillCodes = waybillCodes?.Where(w =>
                {
                    //商家才能看到厂家打印信息，厂家不应该看到商家打印的信息
                    var isAgentRole = true;
                    var wIsSelf = isAgentRole = w.FxUserId == fxUserId;
                    if (wIsSelf == false && w.PathFlowCode.IsNotNullOrEmpty())
                    {
                        List<PathFlowNode> nodes;
                        if (pathFlowNodeDic.TryGetValue(w.PathFlowCode, out nodes))
                        {
                            isAgentRole = nodes.Any(x => x.UpFxUserId == fxUserId);
                        }
                    }

                    if (!isAgentRole)
                    {
                        return false;
                    }

                    return MatchOrderWithWaybillCode(o.Orders.FirstOrDefault(), w, childPlatformOrderIds);
                })?.Select(x => new WaybillCodeViewModel
                {
                    CompanyCode = x.ExpressNo,
                    WaybillCode = x.ExpressWayBillCode,
                    TemplateId = x.TemplateId,
                    TemplateType = x.TemplateType ?? 0,
                    ExpressCpCode = x.ExpressNo,
                    ExpressName = x.ExpressName,
                    WaybillCodeId = x.ID,
                    UniqueKey = x.UniqueKey,
                    CreateDate = x.CreateDate,
                    IsPreviewed = x.IsPreviewed,
                    TotalWeight = (x.TotalWeight ?? 0) * 1000,
                    SendType = x.SendType,
                    Status = x.Status
                })?.OrderByDescending(w => w.CreateDate)?.ToList();
            });
        }
        private static bool MatchOrderWithWaybillCode(AfterSaleOrderResult o, WaybillCode w, List<string> childPlatformOrderIds)
        {
            w.IsPreviewed = o.IsPreviewed ?? false;
            if (w.OrderId == o.SourceLogicOrderId && (childPlatformOrderIds == null || childPlatformOrderIds.Any() == false))
                return true;
            if (childPlatformOrderIds != null && childPlatformOrderIds.Any() && childPlatformOrderIds.Contains(w.OrderId)
                || (!string.IsNullOrEmpty(w.OrderIdJoin) && w.OrderIdJoin.Split(',').Contains(o.LogicOrderId))
               )
                return true;
            //订单是否是运单号的子集
            var wPids = w.OrderIdJoin.Split(',').OrderBy(x => x).ToList();
            var cpids = childPlatformOrderIds.OrderBy(x => x).ToList();
            //运单号中只要有一个订单号在当前的[合并]订单中，就需要显示出来
            if (wPids != null && cpids != null && wPids.Any(x => cpids.Any(y => y == x)))
                return true;
            //if (wPids != null && cpids != null && wPids.Count()== cpids.Count())
            //{
            //    var isSame = true;
            //    for (int i = 0; i < wPids.Count(); i++)
            //    {
            //        if (wPids[i] != cpids[i])
            //        {
            //            isSame = false;
            //            break;
            //        }
            //    }
            //    return isSame;
            //}
            return false;
        }

        public Dictionary<string, List<PathFlowNode>> GetSortPathFlowNodes(List<PathFlowNode> nodes)
        {
            var pathFlowNodeDic = new Dictionary<string, List<PathFlowNode>>();
            if (nodes == null || nodes.Count() <= 1)
                return pathFlowNodeDic;
            /*
             * FxUserId,UpFxUserId,DownFxUserId
             * 1,0,2
             * 2,1,3
             * 3,2,4
             * 4,3,0
             * 商家角色的有：1,2,3
             */
            var agentIds = nodes.Where(x => x.DownFxUserId > 0).Select(x => x.FxUserId).Distinct().ToList();
            var fxUserShopRepository = new FxUserShopRepository();
            var commonSettingRepository = new CommonSettingRepository();
            var agentShops = fxUserShopRepository.GetFxUserShopIds(agentIds);
            var salePriceSets = commonSettingRepository.Get("/FenFa/System/Config/IsSalePricePublic", agentShops.Select(x => x.ShopId).ToList());
            foreach (var node in nodes)
            {
                if (node.DownFxUserId == 0)
                    continue;
                var agentShop = agentShops.FirstOrDefault(x => x.FxUserId == node.FxUserId);
                if (agentShop == null)
                    continue;
                node.IsShowSalePrice = salePriceSets.FirstOrDefault(x => x.ShopId == agentShop.ShopId)?.Value.ToBool() ?? false;
            }

            //多个排序
            nodes.GroupBy(x => x.PathFlowCode).ToList().ForEach(g =>
            {
                var rootNode = g.FirstOrDefault(f => f.UpFxUserId == 0);
                if (rootNode == null)
                    return;

                var nodeIndex = 0;
                var list = new List<PathFlowNode>() { rootNode };
                var curNode = rootNode;
                curNode.NodeIndex = nodeIndex++;
                foreach (var pf in g)
                {
                    var nextNode = g.FirstOrDefault(x => x.FxUserId == curNode.DownFxUserId);
                    if (nextNode != null)
                    {
                        curNode = nextNode;
                        nextNode = null;
                        curNode.NodeIndex = nodeIndex++;
                        list.Add(curNode);
                    }
                }
                pathFlowNodeDic.Add(g.Key, list);
            });
            return pathFlowNodeDic;
        }

        /// <summary>
        /// 获取售后单，根据指定字段
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<AfterSaleOrder> GetListByCodes(List<string> codes, List<string> selectFields = null,
            string whereFieldName = "aso.PlatformOrderId")
        {
            if (codes == null || !codes.Any())
            {
                return new List<AfterSaleOrder>();
            }

            var filedString = "*";
            if (selectFields != null && selectFields.Any())
            {
                filedString = string.Join(",", selectFields);
            }

            var db = DbConnection;
            var sql = $@"SELECT {filedString} FROM AfterSaleOrder aso(NOLOCK) 
                        INNER JOIN AfterSaleOrderItem asoi(NOLOCK) 
                        ON aso.AfterSaleCode = asoi.AfterSaleCode WHERE {whereFieldName} IN@Codes";

            var dictionary = new Dictionary<string, AfterSaleOrder>();

            var result = db.Query<AfterSaleOrder, AfterSaleOrderItem, AfterSaleOrder>(sql, (aso, asoi) =>
            {
                AfterSaleOrder order = null;
                if (!dictionary.TryGetValue(aso.AfterSaleCode, out order))
                {
                    order = aso;
                    dictionary.Add(aso.AfterSaleCode, order);
                }

                //处理Item
                if (asoi != null && order.AfterSaleOrderItems.All(x => x.AfterSaleCode != asoi.AfterSaleCode))
                {
                    order.AfterSaleOrderItems.Add(asoi);
                }

                return order;
            }, new { Codes = codes });

            return dictionary.Values.ToList();
        }

        /// <summary>
        /// 更新售后状态：只更新以下字段：AfterSaleStatus,PlatAfterSaleStatus
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int UpdateStatus(UpdateStatusModel model)
        {
            var sql = "UPDATE AfterSaleOrder SET AfterSaleStatus=@AfterSaleStatus,PlatAfterSaleStatus=@PlatAfterSaleStatus,UpdateBy=@UpdateBy,UpdateTime=GETDATE() WHERE AfterSaleCode IN @AfterSaleCodes";

            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("AfterSaleStatus", model.Status);
            parameters.Add("PlatAfterSaleStatus", model.PlatStatus);
            parameters.Add("UpdateBy", model.UpdateBy);
            parameters.Add("AfterSaleCodes", model.AfterSaleCodes);

            var result = DbConnection.Execute(sql, parameters);

            return result;
        }

        /// <summary>
        /// 更新拦截用户：只更新以下字段：InterceptFxUser
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int UpdateInterceptStatus(UpdateStatusModel model)
        {
            var sqlWhere = string.Empty;
            if (model.InterceptFxUserId == null) // 取标
            {
                sqlWhere = " AND InterceptFxUserId = @UpdateBy";
            }
            else
            {
                sqlWhere = " AND (InterceptFxUserId IS NULL OR InterceptFxUserId = 0)";
            }
            var sql = $"UPDATE AfterSaleOrder SET InterceptFxUserId = @InterceptFxUserId, UpdateBy = @UpdateBy, UpdateTime = GETDATE() WHERE AfterSaleCode IN @AfterSaleCodes {sqlWhere}";

            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("InterceptFxUserId", model.InterceptFxUserId);
            parameters.Add("UpdateBy", model.UpdateBy);
            parameters.Add("AfterSaleCodes", model.AfterSaleCodes);

            var result = DbConnection.Execute(sql, parameters);

            return result;
        }

        /// <summary>
        /// 更新售后备注
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int UpdateRemark(UpdateRemarkModel model)
        {
            var sql = "UPDATE AfterSaleOrder SET AfterSaleRemark=@AfterSaleRemark,UpdateBy=@UpdateBy,AfterSaleRemarkFlag=@RemarkFlag,UpdateTime=GETDATE() WHERE AfterSaleCode IN @AfterSaleCodes ";

            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("AfterSaleRemark", model.Remark);
            parameters.Add("AfterSaleCodes", model.AfterSaleCodes);
            parameters.Add("UpdateBy", model.UpdateBy);
            parameters.Add("RemarkFlag", model.RemarkFlag);
            var result = DbConnection.Execute(sql, parameters);

            return result;
        }

        public void ExecuteUpdateProc(List<AfterSaleOrder> orders)
        {
            if (orders == null || !orders.Any())
                return;

            var db = this.DbConnection;
            DataTable dt = new DataTable();
            DataTable dtItem = new DataTable();
            var first = orders.First();
            var shopId = first.ShopId;
            var fxUserId = first.FxUserId;

            #region 转成DataTable

            dt.TableName = "FxBulkUpdateAfterSaleOrder";
            dt.Columns.Add(new DataColumn() { ColumnName = "Id", DataType = typeof(int) });
            dt.Columns.Add(new DataColumn() { ColumnName = "AfterSaleId", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "AfterSaleCode", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "PlatAfterSaleStatus", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "AfterSaleStatus", DataType = typeof(int) });

            dt.Columns.Add(new DataColumn() { ColumnName = "PlatAfterSaleType", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "AfterSaleType", DataType = typeof(int) });
            dt.Columns.Add(new DataColumn() { ColumnName = "PlatRefundStatus", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "RefundStatus", DataType = typeof(int) });
            dt.Columns.Add(new DataColumn() { ColumnName = "PlatArbitrateStatus", DataType = typeof(string) });

            dt.Columns.Add(new DataColumn() { ColumnName = "ArbitrateStatus", DataType = typeof(int) });
            dt.Columns.Add(new DataColumn() { ColumnName = "RefundTotalAmount", DataType = typeof(decimal) });
            dt.Columns.Add(new DataColumn() { ColumnName = "RefundPostAmount", DataType = typeof(decimal) });
            dt.Columns.Add(new DataColumn() { ColumnName = "RefundPromotionAmount", DataType = typeof(decimal) });
            dt.Columns.Add(new DataColumn() { ColumnName = "RealRefundAmount", DataType = typeof(decimal) });

            dt.Columns.Add(new DataColumn() { ColumnName = "OverdueTime", DataType = typeof(DateTime) });
            dt.Columns.Add(new DataColumn() { ColumnName = "PlatUpdateTime", DataType = typeof(DateTime) });
            dt.Columns.Add(new DataColumn() { ColumnName = "Reason", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "ReasonCode", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "ReasonRemark", DataType = typeof(string) });

            dt.Columns.Add(new DataColumn() { ColumnName = "ReturnTrackingNo", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "ReturnCompanyName", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "ReturnCompanyCode", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "ReturnLogisticsTime", DataType = typeof(DateTime) });
            dt.Columns.Add(new DataColumn() { ColumnName = "BuyerRemark", DataType = typeof(string) });

            dt.Columns.Add(new DataColumn() { ColumnName = "SellerRemarkFlag", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "SellerRemark", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "OrderSendTime", DataType = typeof(DateTime) });
            dt.Columns.Add(new DataColumn() { ColumnName = "AftersaleStatusToFinalTime", DataType = typeof(DateTime) });
            dt.Columns.Add(new DataColumn() { ColumnName = "LogicOrderId", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "PathFlowCode", DataType = typeof(string) });
            dt.Columns.Add(new DataColumn() { ColumnName = "PrintState", DataType = typeof(string) });


            dtItem.TableName = "FxBulkUpdateAfterSaleOrderItem";
            dtItem.Columns.Add(new DataColumn() { ColumnName = "Id", DataType = typeof(int) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "AfterSaleId", DataType = typeof(string) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "AfterSaleCode", DataType = typeof(string) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "OrderItemCode", DataType = typeof(string) });
            //dtItem.Columns.Add(new DataColumn() { ColumnName = "SubItemId", DataType = typeof(string) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "AfterSaleCount", DataType = typeof(int) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "RefundAmount", DataType = typeof(decimal) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "RefundPostAmount", DataType = typeof(decimal) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "ItemSendTime", DataType = typeof(DateTime) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "ItemStatus", DataType = typeof(string) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "ItemRefundStatus", DataType = typeof(string) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "Size", DataType = typeof(string) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "Color", DataType = typeof(string) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "PathFlowCode", DataType = typeof(string) });
            dtItem.Columns.Add(new DataColumn() { ColumnName = "LogicOrderId", DataType = typeof(string) });

            int x = 0, y = 0;
            var afterSaleCodes = new List<string>();
            foreach (var order in orders)
            {
                //存在AfterSaleCode不添加
                if (!afterSaleCodes.Contains(order.AfterSaleCode))
                {
                    afterSaleCodes.Add(order.AfterSaleCode);

                    x += 1;
                    DataRow dr = dt.NewRow();
                    dr["Id"] = x;
                    dr["AfterSaleId"] = order.AfterSaleId;
                    dr["AfterSaleCode"] = order.AfterSaleCode;
                    dr["PlatAfterSaleStatus"] = order.PlatAfterSaleStatus;
                    dr["AfterSaleStatus"] = order.AfterSaleStatus;

                    dr["PlatAfterSaleType"] = order.PlatAfterSaleType;
                    dr["AfterSaleType"] = order.AfterSaleType;
                    dr["PlatRefundStatus"] = order.PlatRefundStatus;
                    dr["RefundStatus"] = order.RefundStatus;
                    dr["PlatArbitrateStatus"] = order.PlatArbitrateStatus;

                    dr["ArbitrateStatus"] = order.ArbitrateStatus;
                    dr["RefundTotalAmount"] = order.RefundTotalAmount;
                    dr["RefundPostAmount"] = order.RefundPostAmount;
                    dr["RefundPromotionAmount"] = order.RefundPromotionAmount;
                    dr["RealRefundAmount"] = order.RealRefundAmount;

                    if (order.OverdueTime == null)
                        dr["OverdueTime"] = DBNull.Value;
                    else
                        dr["OverdueTime"] = order.OverdueTime.Value;

                    if (order.PlatUpdateTime == null)
                        dr["PlatUpdateTime"] = DBNull.Value;
                    else
                        dr["PlatUpdateTime"] = order.PlatUpdateTime.Value;

                    if (order.AftersaleStatusToFinalTime == null)
                        dr["AftersaleStatusToFinalTime"] = DBNull.Value;
                    else
                        dr["AftersaleStatusToFinalTime"] = order.AftersaleStatusToFinalTime.Value;

                    dr["LogicOrderId"] = order.LogicOrderId;
                    dr["PathFlowCode"] = order.PathFlowCode;
                    dr["PrintState"] = order.PrintState;
                    dr["ReasonCode"] = order.ReasonCode;
                    dr["Reason"] = order.Reason;
                    dr["ReasonRemark"] = order.ReasonRemark;

                    dr["ReturnTrackingNo"] = order.ReturnTrackingNo;
                    dr["ReturnCompanyName"] = order.ReturnCompanyName;
                    dr["ReturnCompanyCode"] = order.ReturnCompanyCode;
                    if (order.ReturnLogisticsTime == null)
                        dr["ReturnLogisticsTime"] = DBNull.Value;
                    else
                        dr["ReturnLogisticsTime"] = order.ReturnLogisticsTime.Value;
                    dr["BuyerRemark"] = order.BuyerRemark;
                    dr["SellerRemarkFlag"] = order.SellerRemarkFlag;
                    dr["SellerRemark"] = order.SellerRemark;
                    if (order.OrderSendTime == null)
                        dr["OrderSendTime"] = DBNull.Value;
                    else
                        dr["OrderSendTime"] = order.OrderSendTime.Value;

                    dt.Rows.Add(dr);
                }

                foreach (var item in order.AfterSaleOrderItems)
                {
                    y += 1;
                    DataRow drItem = dtItem.NewRow();
                    drItem["Id"] = y;
                    drItem["AfterSaleId"] = order.AfterSaleId;
                    drItem["AfterSaleCode"] = order.AfterSaleCode;
                    drItem["OrderItemCode"] = item.OrderItemCode;
                    //drItem["SubItemId"] = item.SubItemId;
                    drItem["AfterSaleCount"] = item.AfterSaleCount;
                    drItem["RefundAmount"] = item.RefundAmount;
                    drItem["RefundPostAmount"] = item.RefundPostAmount;
                    if (item.ItemSendTime == null)
                        drItem["ItemSendTime"] = DBNull.Value;
                    else
                        drItem["ItemSendTime"] = item.ItemSendTime.Value;
                    drItem["ItemStatus"] = item.ItemStatus;
                    drItem["ItemRefundStatus"] = item.ItemRefundStatus;
                    drItem["Size"] = item.Size;
                    drItem["Color"] = item.Color;
                    drItem["PathFlowCode"] = item.PathFlowCode;
                    drItem["LogicOrderId"] = item.LogicOrderId;
                    dtItem.Rows.Add(drItem);
                }
            }
            #endregion
            if (db.State != ConnectionState.Open)
                db.Open();
            try
            {
                db.Execute("FxBulkUpdateAfterSaleOrder_V20250410", new { shopId = shopId, fxUserId = fxUserId, orderDatas = dt, orderItemDatas = dtItem }, null, null, System.Data.CommandType.StoredProcedure);
            }
            catch (Exception ex)
            {
                Log.WriteError($"批量更新售后订单时发生错误：{ex},存储过程：FxBulkUpdateAfterSaleOrder_V3,订单数量:\r\n{afterSaleCodes.Count()}，关联数据：{string.Join("','", afterSaleCodes)}，5秒后重试1次");
                //重试一次
                try
                {
                    Thread.Sleep(5000);
                    if (db.State != ConnectionState.Open)
                        db.Open();
                    db.Execute("FxBulkUpdateAfterSaleOrder_V20250410", new { shopId = shopId, fxUserId = fxUserId, orderDatas = dt, orderItemDatas = dtItem }, null, null, System.Data.CommandType.StoredProcedure);
                }
                catch (Exception ex1)
                {
                    Log.WriteError($"重试批量更新售后订单时发生错误：{ex1},存储过程：FxBulkUpdateAfterSaleOrder_V3,订单数据:\r\n{dt.ToJson()}");
                    throw ex1;
                }
                finally
                {
                    db.Close();
                }
            }
            finally
            {
                db.Close();
            }
        }


        //        /// <summary>
        //        /// 设置为失效
        //        /// </summary>
        //        /// <param name="afterSaleCodes"></param>
        //        /// <param name="setOrder">是否设置关联的订单</param>
        //        public int SetInvalid(List<string> afterSaleCodes, bool setOrder = false)
        //        {
        //            DynamicParameters parameters = new DynamicParameters();
        //            parameters.Add("AfterSaleCodes", afterSaleCodes);

        //            if (setOrder)
        //            {
        //                var strSql = "SELECT Id,PlatformOrderId FROM AfterSaleOrder WITH (NOLOCK) WHERE AfterSaleCode IN @AfterSaleCodes AND SourceFlag=0";
        //                var list = DbConnection.Query<AfterSaleOrder>(strSql, parameters).ToList();
        //                if (list != null && list.Any())
        //                {
        //                    var platformOrderIds = list.Select(a => a.PlatformOrderId).Distinct().ToList();
        //                    var updateSql = @"UPDATE P_Order SET PlatformOrderId='DEL-'+PlatformOrderId,OrderCode='DEL-'+OrderCode,ShopId=ShopId*-1,UserId=UserId*-1 WHERE PlatformOrderId IN @PlatformOrderIds ;
        //UPDATE P_OrderItem SET PlatformOrderId='DEL-'+PlatformOrderId,OrderCode='DEL-'+OrderCode,ShopId=ShopId*-1,UserId=UserId*-1,OrderItemCode='DEL-'+OrderItemCode WHERE PlatformOrderId IN @PlatformOrderIds ;
        //UPDATE LogicOrder SET PlatformOrderId='DEL-'+PlatformOrderId,ShopId=ShopId*-1,FxUserId=FxUserId*-1,PathFlowCode='DEL-'+PathFlowCode WHERE PlatformOrderId IN @PlatformOrderIds ;
        //UPDATE LogicOrderItem SET PlatformOrderId='DEL-'+PlatformOrderId,ShopId=ShopId*-1,OrderItemCode='DEL-'+OrderItemCode WHERE PlatformOrderId IN @PlatformOrderIds ;
        //";
        //                    DbConnection.Execute(updateSql, new { PlatformOrderIds = platformOrderIds });
        //                }
        //            }


        //            var sql = $@"UPDATE AfterSaleOrder SET AfterSaleCode='DEL-'+AfterSaleCode,ShopId=ShopId*-1,FxUserId=FxUserId*-1,PathFlowCode='DEL-'+PathFlowCode WHERE AfterSaleCode IN @AfterSaleCodes ;
        //UPDATE AfterSaleOrderItem SET AfterSaleCode='DEL-'+AfterSaleCode,ShopId=ShopId*-1,UserId=UserId*-1,OrderItemCode='DELA-'+OrderItemCode,AfterSaleItemCode='DEL-'+AfterSaleItemCode WHERE AfterSaleCode IN @AfterSaleCodes
        //";
        //            return DbConnection.Execute(sql, parameters);
        //        }

        /// <summary>
        /// 获取售后订单信息，为复制副本
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<AfterSaleOrder> GetListForDuplication(DuplicationConditionModel condition,
            int pageSize)
        {
            //SQL脚本
            var sql =
                $@"SELECT TOP {pageSize} * FROM AfterSaleOrder(NOLOCK) WHERE  FxUserId = {condition.FxUserId} AND ShopId = {condition.ShopId}";
            //游标分页
            if (condition.MaxId.HasValue && condition.MaxId.Value > 0)
            {
                sql += $" AND Id > {condition.MaxId.Value}";
            }
            //时间区间
            if (condition.BeginTime.HasValue)
            {
                sql += $" AND ApplyTime > '{condition.BeginTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}'";
            }

            if (condition.EndTime.HasValue)
            {
                sql += $" AND ApplyTime <= '{condition.EndTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}'";
            }
            //排序
            sql += " ORDER BY Id";
            //查询
            return DbConnection.Query<AfterSaleOrder>(sql).ToList();
        }

        /// <summary>
        /// 获取售后单信息，按售后单代码列表
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public List<AfterSaleOrder> GetListForDuplication(List<string> codes, string selectFields = "*")
        {
            //SQL脚本
            var sqlByIn = $"SELECT {selectFields} FROM AfterSaleOrder(NOLOCK) WHERE AfterSaleCode IN @AfterSaleCodes";
            var sqlByTableFun = $"SELECT {selectFields} FROM AfterSaleOrder(NOLOCK) INNER JOIN dbo.FunStringToTable(@AfterSaleCodes,',') fstt ON AfterSaleCode = fstt.item ";
            //查询
            //return DbConnection.Query<AfterSaleOrder>(sql, new { AfterSaleCodes = string.Join(",", codes.Distinct()) }).ToList();
            return SqlOptimizationHandler.QueryEntities(codes, sqlByIn, sqlByTableFun,
                (sql, param) => DbConnection.Query<AfterSaleOrder>(sql, param).ToList(), "AfterSaleCodes");
        }


        /// <summary>
        /// 获取已存在列表
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        public List<IdAndCodeModel> GetExistIdAndCodes(List<string> codes)
        {
            //SQL脚本
            const string sqlByIn =
                "SELECT Id,AfterSaleCode AS Code FROM AfterSaleOrder(NOLOCK) WHERE AfterSaleCode IN @AfterSaleCodes";
            const string sqlByTableFun =
                "SELECT Id,AfterSaleCode AS Code FROM AfterSaleOrder(NOLOCK) INNER JOIN dbo.FunStringToTable(@AfterSaleCodes,',') fstt ON AfterSaleCode = fstt.item";
            //查询
            //return DbConnection.Query<IdAndCodeModel>(sql, new { AfterSaleCodes = codes }).ToList();
            return SqlOptimizationHandler.QueryEntities(codes, sqlByIn, sqlByTableFun,
                (sql, param) => DbConnection.Query<IdAndCodeModel>(sql, param).ToList(), "AfterSaleCodes");
        }

        /// <summary>
        /// 获取平台售后单的路径流代码信息
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        public List<AfterSaleWithPathFlowCodeModel> GetAfterSaleWithPathFlowCodes(List<string> codes)
        {
            //判空处理
            if (codes == null || codes.Any() == false)
            {
                return new List<AfterSaleWithPathFlowCodeModel>();
            }
            //SQL脚本
            const string sql =
                @"SELECT DISTINCT AfterSaleCode,pfn.PathFlowCode FROM AfterSaleOrderItem asoi WITH(NOLOCK) 
                                INNER JOIN LogicOrder lo WITH(NOLOCK) ON lo.PlatformOrderId = asoi.PlatformOrderId AND lo.ShopId=asoi.ShopId 
                                INNER JOIN LogicOrderItem loi WITH(NOLOCK) ON loi.OrderItemCode = asoi.OrderItemCode AND lo.LogicOrderId=loi.LogicOrderId
                                INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode = lo.PathFlowCode
                                WHERE asoi.AfterSaleCode IN @AfterSaleCodes";
            //查询
            return DbConnection.Query<AfterSaleWithPathFlowCodeModel>(sql, new { AfterSaleCodes = codes }).ToList();
        }
        
        /// <summary>
        /// 批量更新售后订单的信息
        /// </summary>
        /// <param name="models"></param>
        public void UpdateAfterSaleOrderInfo(List<UpdateAfterSaleOrderModel> models)
        {
            if (models == null || !models.Any())
                return;

            /** 为了额外增加索引，调整更新逻辑，先查询，再更新
            var updateAfterSaleOrderSql = "UPDATE dbo.AfterSaleOrder SET OrderSendTime=@orderSendTime WHERE PlatformOrderId in @pids AND ShopId in @sids";

            var first = models.FirstOrDefault();
            DbConnection.Execute(updateAfterSaleOrderSql, new
            {
                orderSendTime = first.SendTime,
                pids = models.Select(f => f.PlatformOrderId).Distinct().ToList(),
                sids = models.Select(f => f.ShopId).Distinct().ToList()
            });

            //更新订单项
            var afterSaleOrderItems = models.SelectMany(f => f.UpdateAfterSaleOrderItemModels).ToList();
            if (afterSaleOrderItems.Any())
            {
                using (var db = DbConnection)
                {
                    if (db.State != ConnectionState.Open)
                        db.Open();

                    var updateAfterSaleOrderItemSql = "UPDATE dbo.AfterSaleOrderItem SET PathFlowCode=@pathFlowCode,LogicOrderId=@logicOrderId WHERE PlatformOrderId IN @pids AND SubItemId IN @subItemIds AND ShopId in @sids;";
                    foreach (var item in afterSaleOrderItems.GroupBy(f => new { f.PathFlowCode, f.LogicOrderId }))
                    {
                        var pathFlowCode = item.Key.PathFlowCode;
                        var logicOrderId = item.Key.LogicOrderId;
                        var pids = item.Select(f => f.PlatformOrderId).Distinct().ToList();
                        var subItemIds = item.Select(f => f.SubItemId).Distinct().ToList();
                        var sids = item.Select(f => f.ShopId).Distinct().ToList();
                        db.Execute(updateAfterSaleOrderItemSql, new { pathFlowCode, logicOrderId, pids, subItemIds, sids });
                    }
                }
            }*/

            //先查询售后订单项
            var sql = @"
SELECT t2.AfterSaleCode,t2.PlatformOrderId,t2.SubItemId,t2.OrderItemCode,t2.ShopId,t2.PathFlowCode 
FROM AfterSaleOrder (NOLOCK) AS t1
    INNER JOIN AfterSaleOrderItem (NOLOCK) AS t2 ON t1.AfterSaleCode = t2.AfterSaleCode
    INNER JOIN FunStringToTable(@pids, ',') AS t3 ON t1.PlatformOrderId = t3.item
WHERE t1.ShopId IN @sids;";

            var afterSaleOrderItems = DbConnection.Query<AfterSaleOrderItem>(sql, new
            {
                pids = string.Join(",", models.Select(f => f.PlatformOrderId).Distinct().ToList()),
                sids = models.Select(f => f.ShopId).Distinct().ToList()
            });

            if (!afterSaleOrderItems.Any())
                return;

            //转dict 方便查找数据
            var afterSaleOrderCodeDict = afterSaleOrderItems.GroupBy(f => $"{f.PlatformOrderId}-{f.ShopId}").ToDictionary(f => f.Key, f => f.FirstOrDefault()?.AfterSaleCode);
            var afterSaleOrderItemDict = afterSaleOrderItems.GroupBy(f => $"{f.PlatformOrderId}-{f.SubItemId}-{f.ShopId}").ToDictionary(f => f.Key, f => f.FirstOrDefault());

            //需要更新的售后订单
            foreach (var item in models)
            {
                var soKey = $"{item.PlatformOrderId}-{item.ShopId}";
                if (afterSaleOrderCodeDict.ContainsKey(soKey))
                {
                    var afterSaleOrderCode = afterSaleOrderCodeDict[soKey];
                    item.AfterSaleCode = afterSaleOrderCode;
                }

                foreach (var oi in item.UpdateAfterSaleOrderItemModels)
                {
                    var soiKey = $"{oi.PlatformOrderId}-{oi.SubItemId}-{oi.ShopId}";
                    if (afterSaleOrderItemDict.ContainsKey(soiKey))
                    {
                        var afterSaleOrderItem = afterSaleOrderItemDict[soiKey];
                        if (!string.IsNullOrWhiteSpace(afterSaleOrderItem.PathFlowCode))
                            continue;

                        oi.AfterSaleCode = afterSaleOrderItem.AfterSaleCode;
                        oi.OrderItemCode = afterSaleOrderItem.OrderItemCode;
                    }
                }
            }

            //执行更新
            var updateAfterSaleOrderSql = "UPDATE dbo.AfterSaleOrder SET OrderSendTime=@orderSendTime,PathFlowCode=@pathFlowCode WHERE AfterSaleCode = @afterSaleCode;";

            var first = models.FirstOrDefault();
            var waitUpdateSoModels = models.Where(f => !string.IsNullOrEmpty(f.AfterSaleCode) && f.SendTime != null);
            if (waitUpdateSoModels.Any())
            {
                foreach (var model in waitUpdateSoModels)
                {
                    DbConnection.Execute(updateAfterSaleOrderSql, new
                    {
                        pathFlowCode = model.PathFlowCode,
                        orderSendTime = model.SendTime,
                        afterSaleCode = model.AfterSaleCode,
                    });
                }
            }

            //更新订单项
            var waitUpdateAfterSaleOrderItems = models.SelectMany(f => f.UpdateAfterSaleOrderItemModels).ToList();
            if (waitUpdateAfterSaleOrderItems.Any())
            {
                using (var db = DbConnection)
                {
                    if (db.State != ConnectionState.Open)
                        db.Open();

                    var updateAfterSaleOrderItemSql = "UPDATE dbo.AfterSaleOrderItem SET PathFlowCode=@pathFlowCode,LogicOrderId=@logicOrderId WHERE AfterSaleCode IN @afterSaleCodes AND OrderItemCode IN @orderItemCodes;";
                    foreach (var item in waitUpdateAfterSaleOrderItems.GroupBy(f => new { f.PathFlowCode, f.LogicOrderId }))
                    {
                        var pathFlowCode = item.Key.PathFlowCode;
                        var logicOrderId = item.Key.LogicOrderId;
                        var list = item.Where(f => !string.IsNullOrEmpty(f.AfterSaleCode));
                        if (list.Any())
                        {
                            var afterSaleCodes = list.Select(f => f.AfterSaleCode).Distinct().ToList();
                            var orderItemCodes = list.Select(f => f.OrderItemCode).Distinct().ToList();
                            db.Execute(updateAfterSaleOrderItemSql, new { pathFlowCode, logicOrderId, afterSaleCodes, orderItemCodes });
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 批量更新售后订单的信息
        /// </summary>
        /// <param name="models"></param>
        /// <param name="updateSendTime"></param>
        public void UpdateAfterSaleOrderPrintState(List<UpdateAfterSaleOrderPrintStateModel> models, bool updateSendTime = false)
        {
            if (models == null || !models.Any())
                return;
            if (!updateSendTime)
            {
                const string updateAfterSaleOrderSql = "UPDATE dbo.AfterSaleOrder SET PrintState=@printState WHERE PlatformOrderId in @pids AND ShopId in @sids";

                var first = models.First();

                DbConnection.Execute(updateAfterSaleOrderSql, new
                {
                    printState = first.PrintState,
                    pids = models.Select(f => f.PlatformOrderId).Distinct().ToList(),
                    sids = models.Select(f => f.ShopId).Distinct().ToList()
                });
            }
            else
            {
                using (var db = DbConnection)
                {
                    if (db.State != ConnectionState.Open)
                        db.Open();
                    foreach (var item in models)
                    {
                        var updateFields = new List<string>();
                        if (item.PrintState > 0)
                            updateFields.Add("PrintState=@PrintState");
                        if (item.SendTime != null)
                            updateFields.Add("OrderSendTime=@SendTime");
                        if (updateFields.Any())
                            db.Execute($"UPDATE dbo.AfterSaleOrder SET {string.Join(",", updateFields)} WHERE PlatformOrderId =@PlatformOrderId AND ShopId=@ShopId", item);
                    }
                }
            }
        }

        /// <summary>
        /// 获取平台售后单的相关图片信息
        /// </summary>
        /// <param name="codes">AfterSaleCode</param>
        /// <returns></returns>

        public List<ImagesRelation> GetAfterSaleImagesRelationByEntityCode(List<string> codes)
        {
            //SQL脚本
            const string sql =
                @"SELECT t1.Id,t1.CreatedBy,t1.EntityId,t1.EntityCode,t1.EntityType,t1.ImageUrl,t1.Description,t1.Sequence,t1.CreateTime,t1.UpdateTime 
                  FROM dbo.ImagesRelation (NOLOCK) AS t1
                  INNER JOIN FunStringToTable(@codes,',') AS t3 ON t1.EntityCode = t3.item
                  WHERE t1.ImageUrl IS NOT NULL;";
            //查询
            return DbConnection.Query<ImagesRelation>(sql, new { codes = string.Join(",", codes) }).ToList();
        }

        /// <summary>
        /// 更新售后备注
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int UpdateAfterSaleOrderRelationRemark(UpdateRemarkModel model)
        {
            var sql = "UPDATE AfterSaleOrderRelation SET Remark=@Remark,Flag=@Flag,UpdateTime=GETDATE() WHERE AfterSaleOrderRelationCode IN @AfterSaleOrderRelationCodes ";
            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("Flag", model.Flag);
            parameters.Add("Remark", model.Remark);
            parameters.Add("AfterSaleOrderRelationCodes", model.AfterSaleOrderRelationCodes);
            var result = DbConnection.Execute(sql, parameters);
            return result;
        }

        /// <summary>
        /// 查询售后凭证详细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<AfterSaleCertificateResult>> GetAfterSaleCertificateList(AfterSaleOrderQuery query)
        {
            var parameters = new DynamicParameters();
            #region 混合查询基础商品归一
            var fxUserId = query.FxUserId;
            var baseofptskurelationSwitch = query.IsBaseProduceCombine;
            var baseofptskurelationJoinSql = string.Empty;
            var baseofptskurelationFields = string.Empty;
            if (baseofptskurelationSwitch)
            {
                baseofptskurelationJoinSql = $@" LEFT JOIN BaseOfPtSkuRelation rel WITH ( NOLOCK ) ON asoi.ProductCode= rel.ProductCode 
AND asoi.SkuCode= rel.ProductSkuCode 
AND rel.FxUserId=@fxUserId AND rel.Status=1";
                parameters.Add("fxUserId", fxUserId);
                baseofptskurelationFields = $",CASE WHEN rel.BaseProductSkuUid IS NULL THEN 0 ELSE 1 END AS IsRelationBaseProduct,rel.BaseProductSkuUid AS BaseProductSkuUid";
            }
            #endregion

            var sql = $@"SELECT TOP 1000000 asoi.Id ASItemId,aso.PlatformOrderId,pfn.FxUserId,pfn.UpFxUserId,pfn.DownFxUserId,
                        pfn.PathFlowCode,asor.FxUserId as RelationFxUserId,asor.UpFxUserId As RelationUpFxUserId,asor.DownFxUserId As RelationDownFxUserId,asor.Remark,asor.Flag,asor.AfterSaleOrderRelationCode,ISNULL(asor.IsPushToSupplier,0) AS IsPushToSupplier,asor.FlowStatus,asor.IsSyncAgent,asor.Comment,asor.Operate
                        FROM AfterSaleOrderItem asoi WITH(NOLOCK)
                        INNER JOIN AfterSaleOrder aso WITH(NOLOCK) ON aso.AfterSaleCode = asoi.AfterSaleCode
                        INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode = aso.PathFlowCode
                        INNER JOIN AfterSaleOrderRelation asor WITH(NOLOCK) ON aso.AfterSaleCode=asor.AfterSaleCode 
                        WHERE pfn.FxUserId=@FxUserId and asor.FxUserId=@FxUserId";
            string whereStr = "";//第一次查询条件     
            string secondWhereStr = "";//第二次查询条件
            parameters.Add("FxUserId", query.FxUserId);

            #region 查询条件
            if (!string.IsNullOrWhiteSpace(query.AfterSaleIds))
            {
                whereStr += " AND aso.AfterSaleId IN @AfterSaleIds";
                parameters.Add("AfterSaleIds", query.AfterSaleIds.SplitToList(",").Distinct().ToList());
            }
            secondWhereStr += whereStr;
            sql += whereStr;
            //排序
            sql += " ORDER BY asoi.Id DESC";
            #endregion

            #region 拼SQL

            var tempTableName = GetTempTableName(BusinessTypes.AfterSaleOrder, query.FxUserId) + "_certificate";
            var rTempTableName = tempTableName + "_2";

            var lastSQL = $@"-- 查询售后单
-- 1.满足条件订单存入临时表
SELECT IDENTITY(INT,1,1) AS RowIndex,* INTO #temp{tempTableName} FROM (     
{sql}
) AS tmp

-- 2.列表显示查询结果
SELECT IDENTITY(INT,1,1) AS RowIndex,* INTO #temp{rTempTableName} FROM (
	SELECT TOP 1000000 PlatformOrderId,MAX(o.RowIndex) MaxRowIndex
	FROM #temp{tempTableName} o 
    GROUP BY PlatformOrderId ORDER BY MAX(o.RowIndex)
) AS tmp


-- 3.查询记录数
SELECT COUNT(1) FROM #temp{rTempTableName}

-- 4.查售后相关数据
SELECT aso.Id,
aso.AfterSaleId,aso.AfterSaleCode,aso.PlatformOrderId,aso.OrderCode,aso.ShopId,
aso.PlatformType,aso.PathFlowCode,aso.PlatAfterSaleStatus,aso.AfterSaleStatus,aso.PlatAfterSaleType,
aso.AfterSaleType,aso.ArbitrateStatus,aso.RefundTotalAmount as PriceSum,aso.ApplyTime,aso.SourceFlag,aso.AfterSaleRemark,aso.CreateBy,aso.CreateTime,aso.LogicOrderId,
tmp2.UpFxUserId,tmp2.DownFxUserId,tmp2.PathFlowCode LPathFlowCode,
tmp2.RelationDownFxUserId,tmp2.RelationUpFxUserId,tmp2.RelationFxUserId,tmp2.Remark,tmp2.Flag,tmp2.AfterSaleOrderRelationCode,tmp2.FlowStatus,tmp2.Comment,tmp2.FxUserId,tmp2.IsPushToSupplier,tmp2.IsSyncAgent,
asoi.Id,
asoi.Id as OrderItemId,aso.LogicOrderId,aso.PlatformOrderId,aso.AfterSaleId,asoi.AfterSaleCode,asoi.OrderItemCode,asoi.AfterSaleType,
asoi.AfterSaleCount as Count,asoi.RefundAmount,asoi.StockState,asoi.OrderCode,asoi.AfterSaleItemCode,asoi.SkuId,asoi.SkuCode,asoi.ProductId,asoi.ProductCode,asoi.ProductSubject,
asoi.ProductCargoNumber,asoi.CargoNumber,
asoi.ProductImgUrl,asoi.Price as AfterSalePrice,asoi.OrignalCount,asoi.Color,asoi.Size,asoi.ItemStatus,asoi.ItemRefundStatus,asoi.PrintState,asoi.UserId{baseofptskurelationFields}
FROM AfterSaleOrder aso WITH(NOLOCK)
INNER JOIN (SELECT PlatformOrderId FROM  #temp{rTempTableName}) tmp 
ON aso.PlatformOrderId=tmp.PlatformOrderId
INNER JOIN #temp{tempTableName} tmp2 ON tmp.PlatformOrderId=tmp2.PlatformOrderId 
INNER JOIN AfterSaleOrderItem asoi WITH(NOLOCK) ON tmp2.ASItemId=asoi.Id AND aso.AfterSaleCode=asoi.AfterSaleCode
{baseofptskurelationJoinSql}
WHERE 1=1 AND tmp2.FxUserId=@FxUserId  and tmp2.RelationFxUserId=@FxUserId {secondWhereStr} ORDER BY aso.Id DESC
";
            #endregion

            #region 记录执行的SQL语句
            if (CustomerConfig.IsDebug)
            {
                var realSql = GetRealSql(lastSQL, parameters);
                var sqllog = WriteSqlToLog(realSql, parameters, "AfterSaleCertificateSql.txt", "FxOrderSql", "GetAfterSaleCertificateOrders");
            }
            #endregion

            Stopwatch sw = new Stopwatch();
            sw.Start();
            var db = this.DbConnection;
            var grid = db.QueryMultiple(lastSQL, param: parameters);
            var totalCount = grid.Read<int>().FirstOrDefault();

            var itemList = new List<AfterSaleCertificateItemResult>();
            var lookUp = new Dictionary<string, AfterSaleCertificateResult>();
            var imagesRelatEntityCodes = new List<string>();///查询的实体的Code
            grid.Read<AfterSaleOrderCertificateResult, AfterSaleCertificateItemResult, AfterSaleCertificateResult>((o, oi) =>
            {
                //平台订单级
                AfterSaleCertificateResult porder = null;
                var key = o.PlatformOrderId;
                if (!lookUp.TryGetValue(key, out porder))
                {
                    porder = new AfterSaleCertificateResult()
                    {
                        CreateTime = o.CreateTime,
                        OrderCode = o.OrderCode,
                        PlatformOrderId = o.PlatformOrderId,
                        PlatformType = o.PlatformType,
                        ShopId = o.ShopId,
                        FxUserId = o.FxUserId,
                        UpFxUserId = o.UpFxUserId,
                        DownFxUserId = o.DownFxUserId,
                        FlowStatus = o.FlowStatus,
                        IsPushToSupplier = o.IsPushToSupplier,
                        Orders = new List<AfterSaleOrderCertificateResult>()
                    };
                    lookUp.Add(key, porder);
                };

                ///详细凭证
                if (!imagesRelatEntityCodes.Contains(o.AfterSaleCode))
                    imagesRelatEntityCodes.Add(o.AfterSaleCode);
                if (!imagesRelatEntityCodes.Contains(oi.AfterSaleItemCode))
                    imagesRelatEntityCodes.Add(oi.AfterSaleItemCode);


                //售后单级
                if (!porder.Orders.Any(a => a.AfterSaleCode == o.AfterSaleCode))
                    porder.Orders.Add(o);

                //售后单项
                if (!itemList.Any(a => a.AfterSaleCode == oi.AfterSaleCode && a.OrderItemCode == oi.OrderItemCode))
                    itemList.Add(oi);

                return porder;
            }).ToList();

            var result = lookUp.Values.ToList();
            ///获取创建人相关的图片
            var imagesRelationList = GetImagesRelationsByEntityCodes(imagesRelatEntityCodes);
            List<PrintscreenPic> printscreenPicList = new List<PrintscreenPic>();
            imagesRelationList.ForEach(im =>
            {
                PrintscreenPic prc = new PrintscreenPic
                {
                    Id = im.Id.ToString(),
                    ImageUrl = Utility.DES.EncryptUrl(im.ImageUrl, CustomerConfig.LoginCookieEncryptKey),//加密中转解密
                    sort = im.Sequence,
                    FxUserId = im.CreatedBy,
                    FileName = im.ImageName,
                    EntityId = im.EntityId,
                    EntityType = im.EntityType,
                    EntityCode = im.EntityCode

                };
                printscreenPicList.Add(prc);
            });

            //更新结果集的售后单项
            if (result != null && result.Any())
            {
                //订单
                result.SelectMany(a => a.Orders).ToList().ForEach(order =>
                {
                    order.SecondSubOrders = itemList.Where(b => b.AfterSaleCode == order.AfterSaleCode).ToList();
                    #region 凭证明细图片详细
                    order.SecondSubOrders.ForEach(sso =>
                    {
                        sso.PrintscreenPic = printscreenPicList.Where(p => p.EntityType == "AfterSaleOrderItem" && p.EntityCode == sso.AfterSaleItemCode).ToList();
                    });
                    order.PrintscreenPic = printscreenPicList.Where(p => p.EntityType == "AfterSaleOrder" && p.EntityCode == order.AfterSaleCode).ToList();
                    #endregion
                });
            }
            sw.Stop();
            Log.Debug($"AfterSaleOrderRepository=>GetAfterSaleCertificateList(拼字符串)，耗时：{sw.Elapsed.TotalMilliseconds} ");

            return Tuple.Create(totalCount, result);
        }

        /// <summary>
        /// 根据平台单查询售后单
        /// </summary>
        /// <param name="platformOrderIds"></param>
        /// <returns></returns>
        public List<AfterSaleOrder> GetAfterSaleOrderByPlatformIds(List<string> platformOrderIds)
        {
            const string sql = "SELECT * FROM AfterSaleOrder(NOLOCK) WHERE PlatformOrderId IN @platformOrderIds";
            return DbConnection.Query<AfterSaleOrder>(sql, new { platformOrderIds }).ToList();
        }

        /// <summary>
        /// 根据创建人用户获取图片关联表
        /// </summary>
        /// <param name="entityCodes"></param>
        /// <returns></returns>
        public List<ImagesRelation> GetImagesRelationsByEntityCodes(List<string> entityCodes)
        {
            const string sql = "SELECT Id, CreatedBy, EntityId, EntityCode, EntityType, ImageUrl, Description, Sequence, CreateTime, UpdateTime, ImageName FROM dbo.ImagesRelation AS ir (NOLOCK) INNER JOIN FunStringToTable(@entityCodes, ',' ) AS t1 ON ir.EntityCode = t1.item";
            return DbConnection.Query<ImagesRelation>(sql, new { entityCodes = string.Join(",", entityCodes) }).ToList();
        }

        /// <summary>
        /// 更新售后删除状态：只更新以下字段：IsDeleted
        /// </summary>
        /// <param name="afterSaleId">售后单Id</param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        public int DelAfterSaleOrderByAfterSaleCodes(List<string> afterSaleCodes)
        {
            var delSQL = $@"UPDATE AfterSaleOrder SET IsDeleted=1,UpdateTime=GETDATE() WHERE AfterSaleCode in @afterSaleCodes";
            int result = DbConnection.Execute(delSQL, new { afterSaleCodes });
            return result;
        }

        /// <summary>
        /// 售后手工单数据 SourceFlag为1
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<AfterSalePageResult>> GetAfterSaleOrderListBySourceFlag(AfterSaleOrderQuery query)
        {
            var parameters = new DynamicParameters();

            #region 混合查询基础商品
            string baseofptskurelationJoinSql;
            string baseofptskurelationFields;
            string baseofptskurelationExtentFields;
            string baseofptskurelationWhereSql;
            MixSearchAfterSale(query, parameters,
                out baseofptskurelationJoinSql,
                out baseofptskurelationFields,
                out baseofptskurelationExtentFields,
                out baseofptskurelationWhereSql);
            #endregion

            #region 构建需要查询的数据临时表
            var sql =
                $@"SELECT TOP 1000000 asoi.Id ASItemId,aso.AfterSaleId,aso.PlatformOrderId,pfn.FxUserId,pfn.UpFxUserId,pfn.DownFxUserId,
                        pfn.PathFlowCode,NULL AS OnlineSendTime,NULL AS ErpState,NULL AS OrderPrintState,NULL AS IsPreviewed,NULL AS ExpressPrintTime,NULL AS LastWaybillCode,NULL AS SourceLogicOrderId,NULL AS MergeredOrderId,
                         asor.FxUserId as RelationFxUserId,asor.UpFxUserId As RelationUpFxUserId,asor.DownFxUserId As RelationDownFxUserId,asor.Remark,asor.Flag,asor.AfterSaleOrderRelationCode,ISNULL(asor.IsPushToSupplier,0) AS IsPushToSupplier,asor.FlowStatus,asor.IsSyncAgent,asor.Comment,asor.Operate {baseofptskurelationFields}
                        FROM AfterSaleOrderItem asoi WITH(NOLOCK)
                        INNER JOIN AfterSaleOrder aso WITH(NOLOCK) ON aso.AfterSaleCode = asoi.AfterSaleCode
                        INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode = aso.PathFlowCode
                        INNER JOIN AfterSaleOrderRelation asor WITH(NOLOCK) ON aso.AfterSaleCode=asor.AfterSaleCode 
                        {baseofptskurelationJoinSql}
                        WHERE pfn.FxUserId=@FxUserId and asor.FxUserId=@FxUserId  ";

            string whereStr = "";      //1.查出OrderCode条件
            string secondWhereStr = "";//二次查询条件
            string noContractwhereStr = "";//用于订单商品不包含
            var tableName = "aso";//LogicOrderId查询的表名，手工单为aso，平台单为lo
            parameters.Add("FxUserId", query.FxUserId);
            whereStr += " AND SourceFlag=@SourceFlag  AND (IsDeleted IS NULL OR IsDeleted = 0)";
            parameters.Add("SourceFlag", query.SourceFlag);
            #endregion

            var isSearchId = (query.Ids != null && query.Ids.Any());
            //只搜索指定Id
            if (isSearchId)
            {
                whereStr += " AND aso.Id IN @Ids";
                parameters.Add("Ids", query.Ids);
                secondWhereStr = whereStr;
            }
            else
            {
                #region 查询其他条件
                if (!string.IsNullOrEmpty(query.PlatformType))
                {
                    whereStr += " AND aso.PlatformType=@PlatformType";
                    parameters.Add("PlatformType", query.PlatformType);
                }
                if (query.RefundStatus != null)
                {
                    whereStr += " AND aso.RefundStatus=@RefundStatus";
                    parameters.Add("RefundStatus", query.RefundStatus.Value);
                }
                if (query.AfterSaleStatus != null)
                {
                    whereStr += " AND aso.AfterSaleStatus=@AfterSaleStatus";
                    parameters.Add("AfterSaleStatus", query.AfterSaleStatus.Value);
                }
                if (!string.IsNullOrEmpty(query.PlatformOrderId))
                {
                    whereStr += " AND aso.PlatformOrderId LIKE @PlatformOrderId";
                    parameters.Add("PlatformOrderId", $"%{query.PlatformOrderId}%");
                }
                if (!string.IsNullOrEmpty(query.ReturnTrackingNo))
                {
                    whereStr += " AND aso.ReturnTrackingNo LIKE @ReturnTrackingNo";
                    parameters.Add("ReturnTrackingNo", $"%{query.ReturnTrackingNo}%");
                }
                if (!string.IsNullOrEmpty(query.ResendTrackingNo))
                {
                    whereStr += " AND aso.ResendTrackingNo LIKE @ResendTrackingNo";
                    parameters.Add("ResendTrackingNo", $"%{query.ResendTrackingNo}%");
                }
                ///工单留言查询
                if (!string.IsNullOrEmpty(query.AfterSaleRemark))
                {
                    whereStr += " AND aso.AfterSaleRemark LIKE @AfterSaleRemark";
                    parameters.Add("AfterSaleRemark", $"%{query.AfterSaleRemark}%");
                }
                if (query.SourceShopIds != null && query.SourceShopIds.Any())
                {
                    whereStr += $" AND aso.ShopId IN ({string.Join(",", query.SourceShopIds.Distinct().ToList())})";
                }
                ///
                if (!string.IsNullOrWhiteSpace(query.AfterSaleIds))
                {
                    whereStr += " AND aso.AfterSaleId IN @AfterSaleIds";
                    parameters.Add("AfterSaleIds", query.AfterSaleIds.SplitToList(",").Distinct().ToList());
                }
                if (query.StartTime != null && query.EndTime != null)
                {
                    var queryDateField = "aso.ApplyTime";
                    var queryDateType = query.QueryDateType.ToLower();
                    if (queryDateType == "ocreatetime")
                        queryDateField = "aso.OrderCreateTime";
                    else if (queryDateType == "paytime")
                        queryDateField = "aso.OrderPayTime";
                    else if (queryDateType == "sendtime")
                        queryDateField = "aso.OrderSendTime";
                    else if (queryDateType == "aftersaleStatusToFinalTime")
                        queryDateField = "aso.AftersaleStatusToFinalTime";

                    whereStr += $" AND {queryDateField} BETWEEN @StartTime AND @EndTime ";
                    parameters.Add("StartTime", query.StartTime.Value);
                    parameters.Add("EndTime", query.EndTime.Value);
                }


                if (!string.IsNullOrWhiteSpace(query.PlatformOrderIds))
                {
                    whereStr += " AND aso.PlatformOrderId IN @PlatformOrderIds";
                    parameters.Add("PlatformOrderIds", query.PlatformOrderIds.SplitToList(",").Distinct().ToList());
                }

                //需要作为二次条件
                secondWhereStr = whereStr;
                ///售后类型
                if (query.AfterSaleType != null)
                {
                    whereStr += $" AND asoi.AfterSaleType=@AfterSaleType";
                    parameters.Add("AfterSaleType", query.AfterSaleType.Value);
                }
                //备注旗帜
                if (!string.IsNullOrWhiteSpace(query.WorkFlag) && query.WorkFlag != "0")
                {
                    whereStr += " AND asor.Flag=@WorkFlag";
                    parameters.Add("WorkFlag", query.WorkFlag);
                }
                //工单备注
                if (!string.IsNullOrWhiteSpace(query.WorkOrderRemark))
                {
                    whereStr += " AND asor.Remark LIKE @WorkOrderRemark";
                    parameters.Add("WorkOrderRemark", $"%{query.WorkOrderRemark}%");
                }
                //工单留言
                if (!string.IsNullOrWhiteSpace(query.WorkComment))
                {
                    whereStr += query.AfterSaleFlowFlag == "Pending" ? " AND aso.AfterSaleRemark LIKE @WorkComment " : " AND asor.Comment LIKE @WorkComment";
                    parameters.Add("WorkComment", $"%{query.WorkComment}%");
                }
                ///工单类型
                if (query.IsSelf != null)
                {
                    if (query.IsSelf.Value == 0)
                        whereStr += " AND ISNULL(asor.DownFxUserId,0)=0";
                    else
                        whereStr += " AND ISNULL(asor.DownFxUserId,0)<>0";
                    //parameters.Add("IsSelf", query.IsSelf.Value);
                }
                ///售后单-人工售后单模块流转状态
                if (!string.IsNullOrWhiteSpace(query.AfterSaleFlowFlag) && query.SourceFlag == 1)
                {
                    //query.AfterSaleFlowFlag = "Pending";
                    whereStr += " AND asor.FlowStatus=@AfterSaleFlowFlag";
                    parameters.Add("AfterSaleFlowFlag", query.AfterSaleFlowFlag);
                }
                ////手工单查询条件
                if (query.SourceFlag == 1)
                {
                    secondWhereStr += " AND tmp2.RelationFxUserId=@FxUserId";//业务关联表
                }
                if (query.UpFxUserIds != null && query.UpFxUserIds.Any())
                {
                    //var table = (query.SourceFlag == 1 || query.SourceFlag == 2) ? "asor" : "pfn";
                    whereStr += $" AND pfn.UpFxUserId IN ({string.Join(",", query.UpFxUserIds.Distinct().ToList())})";
                    secondWhereStr += $" AND tmp2.UpFxUserId IN ({string.Join(",", query.UpFxUserIds.Distinct().ToList())})";
                }

                if (query.DownFxUserIds != null && query.DownFxUserIds.Any())
                {
                    //var table = (query.SourceFlag == 1 || query.SourceFlag == 2) ? "asor" : "pfn";
                    whereStr += $" AND pfn.DownFxUserId IN ({string.Join(",", query.DownFxUserIds.Distinct().ToList())})";
                    secondWhereStr += $" AND tmp2.DownFxUserId IN ({string.Join(",", query.DownFxUserIds.Distinct().ToList())})";
                }

                if (!string.IsNullOrEmpty(query.LogicOrderId))
                {
                    whereStr += $" AND {tableName}.LogicOrderId LIKE @LogicOrderId";
                    parameters.Add("LogicOrderId", $"%{query.LogicOrderId}%");
                    if (query.SourceFlag == 1 || query.SourceFlag == 2)//只针对手工售后单
                    {
                        secondWhereStr += $" AND {tableName}.LogicOrderId LIKE @LogicOrderId";
                    }
                }
                if (!string.IsNullOrWhiteSpace(query.LogicOrderIds))
                {
                    whereStr += $" AND {tableName}.LogicOrderId IN @LogicOrderIds";
                    parameters.Add("LogicOrderIds", query.LogicOrderIds.SplitToList(",").Distinct().ToList());
                    if (query.SourceFlag == 1 || query.SourceFlag == 2)//只针对手工售后单
                    {
                        secondWhereStr += $" AND {tableName}.LogicOrderId IN @LogicOrderIds";
                    }
                }
                #endregion

                #region MultiFilters 查询条件 目前只有含或者不含
                if (query.MultiFilters != null)
                {
                    StringBuilder multiWhere = new StringBuilder();
                    foreach (var multiFilter in query.MultiFilters)
                    {
                        if (multiFilter == null || multiFilter.Count == 0) continue;
                        multiWhere.Append(" AND (");
                        StringBuilder sb = new StringBuilder();
                        foreach (var filter in multiFilter)
                        {
                            var tempParam = Guid.NewGuid().ToString().Replace("-", "").ToLower();
                            var name = filter.Name.ToString2();
                            var contract = filter.Contract.ToString2().ToLower().Trim();
                            var tableAlias = filter.TableAlias.ToString2();
                            var fieldType = filter.FieldType.ToString2().ToLower().Trim();
                            var val = filter.Value.ToString2();
                            var extVal = filter.ExtValue.ToString2();
                            var compareName = filter.CompareName.ToString2();
                            var opt = filter.Operator.ToString2();
                            var customerQuery = filter.CustomQuery.ToString2();
                            var muTableName = filter.TableName.ToString2();
                            var param = name + tempParam;

                            if (contract.Contains("like"))
                            {
                                if (contract.Contains("like *%"))
                                {
                                    val = CustomerConfig.ReplaceSqlChar(val);
                                    val = val + "%";
                                    contract = contract.Replace("%", "").Replace("*", "");
                                    sb.AppendFormat($" {tableAlias}.{name} {contract} @{param} ");
                                    parameters.Add(param, val);
                                }
                                else
                                {
                                    if (contract.Contains("not like"))
                                    {//不包含
                                        noContractwhereStr += $" AND CHARINDEX(@{param},{tableAlias}.{name}) {">"} 0 ";//注需要排除 所以这里条件是>
                                        sb.AppendFormat($" 1=1");
                                    }
                                    else
                                    {//包含
                                        sb.AppendFormat($" CHARINDEX(@{param},{tableAlias}.{name}) {">"} 0 ");
                                    }

                                    parameters.Add(param, val);
                                }
                            }
                            sb.Append($" {filter.Operator}");
                        }
                        var sb_where = sb.ToString2().Trim().TrimStart("AND").TrimStart("OR").TrimEnd("AND").TrimEnd("OR");
                        multiWhere.Append(sb_where);
                        multiWhere.Append(") ");
                    }
                    //判断是否存在商品级别不包含
                    noContractwhereStr += string.IsNullOrWhiteSpace(noContractwhereStr) ? "" : " AND pfn.FxUserId=@FxUserId AND asor.FxUserId=@FxUserId";
                    whereStr += multiWhere.ToString();
                }
                #endregion

            }
            sql += whereStr + baseofptskurelationWhereSql;
            //排序
            sql += " ORDER BY asoi.Id DESC";

            #region 拼SQL

            var tempTableName = GetTempTableName(BusinessTypes.AfterSaleOrder, query.FxUserId);
            var rTempTableName = tempTableName + "_2";
            var lastSQL = "";
            var exeSQL_1 = $@"-- 查询售后单
-- 1.满足条件订单存入临时表
SELECT IDENTITY(INT,1,1) AS RowIndex,* INTO #temp{tempTableName} FROM (     
{sql}
) AS tmp

-- 2.列表显示查询结果
SELECT IDENTITY(INT,1,1) AS RowIndex,* INTO #temp{rTempTableName} FROM (
	SELECT TOP 1000000 AfterSaleId,MAX(o.RowIndex) MaxRowIndex
	FROM #temp{tempTableName} o 
    GROUP BY AfterSaleId ORDER BY MAX(o.RowIndex)
) AS tmp";
            ////注：排除不满足条件商品订单
            var exeSQL_2 = string.IsNullOrWhiteSpace(noContractwhereStr) ? "" : $@"
--3.排除不满足条件产品的订单
DELETE tmp FROM #temp{rTempTableName} tmp
WHERE  EXISTS ( SELECT  2 FROM AfterSaleOrderItem asoi WITH(NOLOCK)
                        INNER JOIN AfterSaleOrder aso WITH(NOLOCK) ON aso.AfterSaleCode = asoi.AfterSaleCode
                        INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode = aso.PathFlowCode
                        INNER JOIN AfterSaleOrderRelation asor WITH(NOLOCK) ON aso.AfterSaleCode=asor.AfterSaleCode 
                        WHERE 1=1 and tmp.AfterSaleId=asoi.AfterSaleId  {noContractwhereStr});";
            var exeSQL_3 = $@"

-- 4.查询记录数
SELECT COUNT(1) FROM #temp{rTempTableName}

-- 5.查售后相关数据
SELECT aso.Id,aso.AfterSaleId,aso.TradeType,asoi.AfterSaleId,aso.AfterSaleCode,aso.PlatformOrderId,aso.OrderCode,aso.ShopId,aso.FxUserId,
aso.PlatformType,aso.PathFlowCode,aso.PlatAfterSaleStatus,aso.AfterSaleStatus,aso.PlatAfterSaleType,aso.AfterSaleType,aso.PlatRefundStatus,aso.RefundStatus,aso.PlatArbitrateStatus,aso.ArbitrateStatus,aso.RefundTotalAmount,aso.RefundPostAmount,aso.RefundPromotionAmount,aso.RealRefundAmount,aso.ApplyTime,aso.OverdueTime,aso.PlatUpdateTime,aso.Reason,aso.ReasonCode,aso.ReasonRemark,aso.ReturnTrackingNo,aso.ReturnCompanyName,aso.ReturnCompanyCode,aso.ReturnLogisticsTime,aso.ResendTrackingNo,aso.ResendCompanyName,aso.ResendCompanyCode,aso.ResendLogisticsTime,aso.SourceFlag,aso.StockState,aso.AfterSaleRemark,aso.CreateBy,aso.CreateTime,
aso.LogicOrderId,aso.OrderCreateTime,aso.OrderPayTime,aso.OrderSendTime,tmp2.ErpState,tmp2.FxUserId,tmp2.UpFxUserId,tmp2.DownFxUserId,tmp2.PathFlowCode LPathFlowCode,tmp2.OnlineSendTime,tmp2.IsPreviewed,tmp2.ExpressPrintTime,tmp2.LastWaybillCode,tmp2.SourceLogicOrderId,tmp2.MergeredOrderId,aso.ToName,aso.ToPhone,aso.ToProvince,aso.ToCity,aso.ToCounty,aso.ToAddress,aso.ToFullAddress,
tmp2.RelationFxUserId,tmp2.RelationUpFxUserId,tmp2.RelationDownFxUserId,tmp2.Remark,tmp2.Flag,tmp2.AfterSaleOrderRelationCode,tmp2.IsPushToSupplier,tmp2.FlowStatus,tmp2.IsSyncAgent,tmp2.Comment,
asoi.Id,asoi.AfterSaleCode,asoi.OrderItemCode,asoi.AfterSaleCount,asoi.RefundAmount,asoi.StockState,asoi.OrderCode,asoi.AfterSaleItemCode,tmp2.OrderPrintState,asoi.SkuId,asoi.SkuCode,asoi.ProductId,asoi.ProductCode,asoi.ProductSubject,asoi.ProductCargoNumber,asoi.CargoNumber,asoi.ProductImgUrl,asoi.Price,asoi.OrignalCount,asoi.Color,asoi.Size,asoi.ItemStatus,asoi.ItemRefundStatus,asoi.PrintState,tmp2.UpFxUserId,tmp2.DownFxUserId,asoi.SubItemId,asoi.AfterSaleType,tmp2.Operate {baseofptskurelationFields}
FROM AfterSaleOrder aso WITH(NOLOCK)
INNER JOIN (
    SELECT AfterSaleId FROM  #temp{rTempTableName}
 	ORDER BY RowIndex
    OFFSET {(query.PageIndex - 1) * query.PageSize} ROWS FETCH NEXT {query.PageSize} ROWS ONLY
) tmp ON aso.AfterSaleId=tmp.AfterSaleId
INNER JOIN #temp{tempTableName} tmp2 ON tmp.AfterSaleId=tmp2.AfterSaleId 
INNER JOIN AfterSaleOrderItem asoi WITH(NOLOCK) ON tmp.AfterSaleId=asoi.AfterSaleId  AND aso.AfterSaleCode=asoi.AfterSaleCode
{baseofptskurelationJoinSql}
WHERE 1=1 {secondWhereStr} ORDER BY aso.Id DESC
";
            lastSQL += exeSQL_1 += exeSQL_2 += exeSQL_3;
            #endregion

            #region 记录执行的SQL语句
            if (CustomerConfig.IsDebug)
            {
                var realSql = GetRealSql(lastSQL, parameters);
                var sqllog = WriteSqlToLog(realSql, parameters, "AfterSaleSql.txt", "FxOrderSql", "GetAfterSaleOrderList");
            }
            #endregion

            Stopwatch sw = new Stopwatch();
            sw.Start();

            var db = this.DbConnection;
            var grid = db.QueryMultiple(lastSQL, param: parameters);
            var totalCount = grid.Read<int>().FirstOrDefault();
            var itemList = new List<AfterSaleOrderItemResult>();
            var lookUp = new Dictionary<string, AfterSalePageResult>();
            var imagesRelatEntityCodes = new List<string>();///查询的创建用户ID用于查询关联图表

            grid.Read<AfterSaleOrderResult, AfterSaleOrderItemResult, AfterSalePageResult>((o, oi) =>
            {
                //平台订单级
                AfterSalePageResult porder = null;
                var key = o.AfterSaleId;///注：2024.03.27数据展示不在以订单级 以售后单级
                if (!lookUp.TryGetValue(key, out porder))
                {
                    porder = new AfterSalePageResult
                    {
                        OrderCode = o.OrderCode,
                        PlatformOrderId = o.PlatformOrderId,
                        PlatformType = o.PlatformType,
                        OrderCreateTime = o.OrderCreateTime,
                        OrderPayTime = o.OrderPayTime,
                        OrderSendTime = o.OrderSendTime,
                        ShopId = o.ShopId,
                        FxUserId = o.FxUserId,
                        UpFxUserId = o.UpFxUserId,
                        DownFxUserId = o.DownFxUserId,
                        AfterSaleCreateTime = o.CreateTime,
                        AfterSaleId = o.AfterSaleId,
                        LogicOrderId = o.LogicOrderId,
                        IsOfflineNoSku = o.TradeType == "OfflineNoSku",
                        Orders = new List<AfterSaleOrderResult>()
                    };
                    lookUp.Add(key, porder);
                }

                //售后单级
                if (!porder.Orders.Any(a => a.AfterSaleCode == o.AfterSaleCode))
                    porder.Orders.Add(o);

                //售后单项
                if (!itemList.Any(a => a.AfterSaleCode == oi.AfterSaleCode && a.OrderItemCode == oi.OrderItemCode))
                    itemList.Add(oi);

                ///售后手工单凭证图片
                if (!imagesRelatEntityCodes.Contains(oi.AfterSaleItemCode))
                    imagesRelatEntityCodes.Add(oi.AfterSaleItemCode);
                if (!imagesRelatEntityCodes.Contains(o.AfterSaleCode))
                    imagesRelatEntityCodes.Add(o.AfterSaleCode);
                if (!imagesRelatEntityCodes.Contains(o.AfterSaleOrderRelationCode))
                    imagesRelatEntityCodes.Add(o.AfterSaleOrderRelationCode);

                return porder;
            }).ToList();
            var result = lookUp.Values.ToList();

            ///获取创建人相关的图片
            var imagesRelationList = GetImagesRelationsByEntityCodes(imagesRelatEntityCodes);
            List<PrintscreenPic> printscreenPicList = new List<PrintscreenPic>();
            imagesRelationList.ForEach(im =>
            {
                PrintscreenPic prc = new PrintscreenPic
                {
                    Id = im.Id.ToString(),
                    ImageUrl = Utility.DES.EncryptUrl(im.ImageUrl, CustomerConfig.LoginCookieEncryptKey),//加密中转解密
                    sort = im.Sequence,
                    FxUserId = im.CreatedBy,
                    FileName = im.ImageName,
                    EntityId = im.EntityId,
                    EntityType = im.EntityType,
                    EntityCode = im.EntityCode

                };
                printscreenPicList.Add(prc);
            });

            //更新结果集的售后单项
            if (result != null && result.Any())
            {
                //订单
                result.SelectMany(a => a.Orders).ToList().ForEach(order =>
                {
                    order.Items = itemList.Where(b => b.AfterSaleCode == order.AfterSaleCode).ToList();
                    ////当前用户
                    order.CurrentFxUserId = query.FxUserId;
                    ///售后订单其他凭证
                    ///1.待处理：明细
                    ///2.已接受：关联表
                    ///3.已拒绝：关联表
                    ///4.已完成：关联表
                    switch (query.AfterSaleFlowFlag)
                    {
                        case "Pending":
                            order.PrintscreenPic = printscreenPicList.Where(p => p.EntityType == "AfterSaleOrder" && p.EntityCode == order.AfterSaleCode).ToList();
                            break;
                        case "Processed":
                        case "Rejected":
                        case "Completed":
                            order.PrintscreenPic = printscreenPicList.Where(p => p.EntityType == "AfterSaleOrderRelation" && p.EntityCode == order.AfterSaleOrderRelationCode).ToList();
                            break;
                    }
                    if (query.AfterSaleFlowFlag == "Pending")
                        order.Items.ForEach(i => { i.PrintscreenPic = printscreenPicList.Where(p => p.EntityType == "AfterSaleOrderItem" && p.EntityCode == i.AfterSaleItemCode).ToList(); });
                    else
                        order.Items.ForEach(i => { i.PrintscreenPic = printscreenPicList.Where(p => p.EntityType == "AfterSaleOrderRelation" && p.EntityCode == order.AfterSaleOrderRelationCode).ToList(); });


                });

                //设置运单号
                try
                {
                    SetWaybillCodes(result, query.FxUserId);
                }
                catch (Exception e)
                {
                    Log.Debug($"售后单设置运单号异常，异常信息：{e.Message}，堆栈信息：{e.StackTrace}.",
                        $"AfterSaleSetWaybillCodesError_{DateTime.Now:yyyy-MM-dd}.log");
                }

                //获取1688厂家售后单所对应分销商订单信息
                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                {
                    //根据1688订单ID，获取采购单关联信息
                    var platformOrderIds = result.Where(m => m.PlatformType == PlatformType.Alibaba.ToString())
                        .Select(m => m.PlatformOrderId).Distinct().ToList();
                    if (platformOrderIds.Any())
                    {
                        var purchaseOrderRelations = _purchaseOrderRelationRepository.GetListAndItems(platformOrderIds,
                            whereFieldName: "o.PurchasePlatformOrderId");
                        if (purchaseOrderRelations != null && purchaseOrderRelations.Any())
                        {
                            //分销商用户ID
                            var sourceFxUserIds = purchaseOrderRelations.Select(m => m.CreateFxUserId).Distinct()
                                .ToList();
                            //数据库配置信息
                            var dbConfigs = new DbConfigRepository().GetListByFxUserIdsForCurrentCloudPlatform(sourceFxUserIds);
                            var dbNameWithEncrypts = dbConfigs?.Select(m => m.DbNameConfig.DbName).Distinct().Select(m =>
                                new
                                {
                                    DbName = m,
                                    DbNameEncrypt = DES.EncryptDES(m, CustomerConfig.LoginCookieEncryptKey)
                                }).ToList();

                            result.ForEach(o =>
                            {
                                var purchaseOrderRelation =
                                    purchaseOrderRelations.FirstOrDefault(m =>
                                        m.PurchasePlatformOrderId == o.PlatformOrderId);
                                if (purchaseOrderRelation != null)
                                {
                                    var dbConfig = dbConfigs.FirstOrDefault(m =>
                                        m.DbConfig.UserId == purchaseOrderRelation.CreateFxUserId);
                                    o.DistributionOrder = new AfterSalePageResult
                                    {
                                        FxUserId = purchaseOrderRelation.CreateFxUserId,
                                        ShopId = purchaseOrderRelation.SourceShopId,
                                        PlatformOrderId = purchaseOrderRelation.SourcePlatformOrderId,
                                        OrderCreateTime = purchaseOrderRelation.CreateTime,
                                        UpFxUserId = purchaseOrderRelation.CreateFxUserId,
                                        DbNameEncrypt = dbNameWithEncrypts
                                            .FirstOrDefault(m => m.DbName == dbConfig?.DbNameConfig.DbName)
                                            ?.DbNameEncrypt
                                    };
                                }
                            });
                        }
                    }
                }
            }
            sw.Stop();
            Log.Debug($"AfterSaleOrderRepository=>GetAfterSaleOrderList(拼字符串)，耗时：{sw.Elapsed.TotalMilliseconds} ");

            return Tuple.Create(totalCount, result);
        }


        private void MixSearchAfterSale(
            AfterSaleOrderQuery query,
            DynamicParameters parameters,
            out string baseofptskurelationJoinSql, 
            out string baseofptskurelationFields, 
            out string baseofptskurelationExtentFields,
            out string baseofptskurelationWhereSql
            )
        {
            baseofptskurelationJoinSql = string.Empty;
            baseofptskurelationFields = string.Empty;
            baseofptskurelationExtentFields = string.Empty;
            baseofptskurelationWhereSql = string.Empty;

            var fxUserId = query.FxUserId;
            var baseofptskurelationSwitch = query.IsBaseProduceCombine;
            if (baseofptskurelationSwitch)
            {
                baseofptskurelationJoinSql = $@" LEFT JOIN BaseOfPtSkuRelation rel WITH ( NOLOCK ) ON asoi.ProductCode= rel.ProductCode 
AND asoi.SkuCode= rel.ProductSkuCode 
AND rel.FxUserId=@fxUserId AND rel.Status=1";
                parameters.Add("fxUserId", fxUserId);
                baseofptskurelationFields = $",CASE WHEN rel.BaseProductSkuUid IS NULL THEN 0 ELSE 1 END AS IsRelationBaseProduct,rel.BaseProductSkuUid AS BaseProductSkuUid";
                baseofptskurelationExtentFields = $",tmp2.IsRelationBaseProduct,tmp2.BaseProductSkuUid";

                var filters = 
                    query.MultiFilters == null ? new List<OrderSearchFieldModel>():
                    query.MultiFilters?.SelectMany(p => p).Where(x => x.Name == "ProductSubject").ToList();
                var allMultiFilters = 
                    query.MultiFilters == null ? new List<OrderSearchFieldModel>():
                    query.MultiFilters?.SelectMany(p => p).ToList();
                var symbol = string.Empty;
                var productkeyName = "ProductSubject";
                var productkeyNameBase = "BaseProductSubject";
                var key = "ProductSubjectKey";
                var contract = filters.FirstOrDefault()?.Contract;
                var baseSqlLikeY = string.Empty;
                var baseSqlLikeN = string.Empty;
                var containSymbol = string.Empty;

                if (string.IsNullOrWhiteSpace(contract))
                {
                    symbol = ">";
                    containSymbol = "OR";
                    baseSqlLikeY = "AND rel.BaseProductSkuUid IS NULL";
                    baseSqlLikeN = string.Empty;
                }
                else
                {
                    symbol =
                        contract == "like" ? ">" : "=";
                    containSymbol =
                        contract == "like" ? "OR" : "AND";
                    baseSqlLikeY =
                        contract == "like" ? "AND rel.BaseProductSkuUid IS NULL" : "OR rel.BaseProductSkuUid IS NOT NULL";
                    baseSqlLikeN =
                        contract == "like" ? "" : "OR rel.BaseProductSkuUid IS NULL";
                }
               
                for (int i = 0; i < filters.Count; i++)
                {
                    var productFilterValue = filters[i]?.Value;
                    if (string.IsNullOrEmpty(productFilterValue)) continue;
                    baseofptskurelationWhereSql += 
                        $"(" +
                        $" (CHARINDEX(@{key}{i}, asoi.{productkeyName}) {symbol} 0  {baseSqlLikeY} )" +
                        $" {containSymbol}" +
                        $" (CHARINDEX(@{key}{i}, rel.{productkeyNameBase}) {symbol} 0 {baseSqlLikeN})" +
                        $")";
                    baseofptskurelationWhereSql += 
                        i == filters.Count - 1 ? string.Empty : $" {containSymbol}";
                    parameters.Add($"@{key}{i}", productFilterValue);
                    allMultiFilters.Remove(filters[i]);
                }
                query.MultiFilters = new List<List<OrderSearchFieldModel>> { allMultiFilters };
                if (!string.IsNullOrEmpty(baseofptskurelationWhereSql))
                    baseofptskurelationWhereSql = $" AND ({baseofptskurelationWhereSql})";
            }
        }



        /// <summary>
        /// 更新售后备注
        /// </summary>
        /// <param name="selectKeyModel"></param>
        /// <param name="flag"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        public bool UpdatePlatAfterSaleRemark(string platformOrderId, string shopId, string remark, int updateBy)
        {
            var sql = @"UPDATE AfterSaleOrder SET [UpdateTime]=GETDATE(),PlatAfterSaleRemark=@PlatAfterSaleRemark WHERE PlatformOrderId = @PlatformOrderId and shopId=@shopId";

            return DbConnection.Execute(sql, new
            {
                PlatAfterSaleRemark = remark,
                PlatformOrderId = platformOrderId,
                shopId = shopId,
                UpdateBy = updateBy
            }) > 0;
        }

        /// <summary>
        /// 更新卖家备注
        /// </summary>
        /// <param name="platformOrderId"></param>
        /// <param name="afterSaleId"></param>
        /// <param name="remark"></param>
        /// <param name="updateBy"></param>
        /// <returns></returns>
        public bool UpdatePlatAfterSellerRemark(string platformOrderId, string remark, string SellerRemarkFlag, int updateBy)
        {
            var sql = @"UPDATE AfterSaleOrder SET [UpdateTime]=GETDATE(),SellerRemarkFlag=@SellerRemarkFlag,SellerRemark=@SellerRemark WHERE PlatformOrderId = @PlatformOrderId;";

            return DbConnection.Execute(sql, new
            {
                SellerRemarkFlag = SellerRemarkFlag,
                SellerRemark = remark,
                platformOrderId = platformOrderId,
                UpdateBy = updateBy
            }) > 0;
        }

        /// <summary>
        /// 查找厂家Id
        /// </summary>
        /// <returns>Dictionary《AfterSaleCode, 厂家Id》</returns>
        public Dictionary<string, int> SearchForManufacturers(List<int> fxuserId, List<string> afterSaleCodeList, int shopId)
        {
            if (fxuserId == null || fxuserId.Count == 0 || afterSaleCodeList == null || afterSaleCodeList.Count == 0)
                return new Dictionary<string, int>();

            // afterSaleCode ---> OrderItemCode ---- > DownFxUserId
            var tempName = Guid.NewGuid().ToString("N").ToShortMd5();
            var sql = $@"select AfterSaleCode,OrderItemCode INTO #temp_{tempName}  FROM AfterSaleOrderItem asoi WITH(NOLOCK) 
                    INNER JOIN FunStringToTable(@afterSaleCodeList,',') t on t.item = asoi.afterSaleCode;
                    select loi.OrderItemCode as OrderCode, pfn.DownFxUserId as FxUserId,temp.AfterSaleCode FROM LogicOrder lo WITH(NOLOCK)
                    INNER JOIN LogicOrderItem loi WITH(NOLOCK) ON lo.LogicOrderId = loi.LogicOrderId
                    INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode = lo.PathFlowCode
                    INNER JOIN #temp_{tempName} temp on temp.OrderItemCode = loi.OrderItemCode
                    WHERE lo.ShopId = @shopId and pfn.FxUserId in @fxuserId option(loop join);";

            var parameters = new DynamicParameters();
            parameters.Add("fxuserId", fxuserId);
            parameters.Add("shopId", shopId);
            parameters.Add("afterSaleCodeList", string.Join(",", afterSaleCodeList));
            var downFxUserIdList = DbConnection.Query<AfterSaleOrder>(sql, parameters).ToList();

            Log.Debug(() => $"查找厂家Id 用户：{fxuserId.ToJson()}\n他的厂家：{downFxUserIdList.Select(p=> $"AfterSaleCode={p.AfterSaleCode},厂家={p.FxUserId}").ToJson()}",
                "BatchAfterOrderExt.txt");

            var tsql = GetRealSql(sql, parameters);
            var dic = new Dictionary<string, int>();
            foreach (var item in downFxUserIdList)
            {
                if (!dic.ContainsKey(item.AfterSaleCode))
                    dic[item.AfterSaleCode] = item.FxUserId.ToInt(); // OrderCode 临时等于 OrderItemCode, FxUserId 临时等于 DownFxUserId
            }
            return dic;
        }
    }
}
