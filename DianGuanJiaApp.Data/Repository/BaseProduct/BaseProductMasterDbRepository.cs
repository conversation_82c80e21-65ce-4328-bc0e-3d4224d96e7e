using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Entity;
using MongoDB.Driver.Core.Configuration;
using System.Data;
using DianGuanJiaApp.Data.Dapper;
using Z.Dapper.Plus;
using DianGuanJiaApp.Data.Enum;
using Jd.Api.Domain;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Repository.BaseProduct
{
    /// <summary>
    /// 基础商品主库-仓储基类
    /// </summary>
    public class BaseProductMasterDbRepository<T> : BaseRepository<T>
    {
        private IDbConnection _connection;
        private string _connectionString;
        private bool _isUseMySQL = true;

        /// <summary>
        /// 
        /// </summary>
        public BaseProductMasterDbRepository()
        {
            _connectionString = CustomerConfig.BaseProductDbConnectionString;
            if (!string.IsNullOrEmpty(_connectionString))
            {
                _isUseMySQL = _connectionString.StartsWith("server") == false;
            }
        }

        /// <summary>
        /// 指定连接串
        /// </summary>
        public BaseProductMasterDbRepository(string connectionString) : base(connectionString)
        {
            _connectionString = connectionString;
            if (string.IsNullOrEmpty(_connectionString))
            {
                _connectionString = CustomerConfig.BaseProductDbConnectionString;
            }
            if (!string.IsNullOrEmpty(_connectionString))
            {
                _isUseMySQL = _connectionString.StartsWith("server") == false;
            }
        }

        /// <summary>
        /// 返回DbConnection，根据IsUseMySQL区分MySQL和SQLServer
        /// </summary>
        public new IDbConnection DbConnection
        {
            get
            {

                IDbConnection db = null;

                if (!string.IsNullOrEmpty(_connectionString))
                {
                    if (_isUseMySQL)
                    {
                        db = Dapper.DbUtility.GetMySqlConnection(_connectionString);
                    }
                    else
                    {
                        db = Dapper.DbUtility.GetConnection(_connectionString);
                    }
                }
                else
                {
                    throw new Exception("基础商品数据连接为空");
                }
                return db;
            }
        }

        /// <summary>
        /// 是否为MySQL连接
        /// </summary>
        public bool IsUseMySQL
        {
            get { return _isUseMySQL; }
        }

        /// <summary>
        /// 转换SQL，针对MySQL去除部分不支持的关键字
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public string TranSql(string sql)
        {
            if (!IsUseMySQL)
                return sql;
            sql = sql.Replace("WITH(NOLOCK)", string.Empty);
            sql = sql.Replace("with(nolock)", string.Empty);
            sql = sql.Replace("(NOLOCK)", string.Empty);
            sql = sql.Replace("(nolock)", string.Empty);
            return sql;
        }

        /// <summary>
        /// 针对【库存系统】SPU-生成系统唯一id
        /// </summary>
        /// <param name="fxUserId">数据所属用户</param>
        /// <param name="count">生成的id个数</param>
        /// <returns></returns>
        public List<string> WareHouseSpuUniqueId(int fxUserId, int count)
        {
            return BaseProductSystemUniqueId("", fxUserId, count, 1, 6);
        }

        /// <summary>
        /// 生成系统唯一id
        /// </summary>
        /// <param name="prefix">前缀：1基础商品；2基础商品规格；可为空</param>
        /// <param name="fxUserId">数据所属用户</param>
        /// <param name="count">生成的id个数</param>
        /// <param name="userIdLen">FxUserId长度，不足补0，默认7位</param>
        /// <param name="idLen">Id长度，不足补0，默认11位</param>
        /// <returns></returns>
        public List<string> BaseProductSystemUniqueId(string prefix, int fxUserId, int count, int userIdLen = 7, int idLen = 11)
        {
            if (count <= 0)
                return new List<string>();
            var list = new List<string>();
            var code = Guid.NewGuid().ToString().ToShortMd5();
            var codes = new List<BaseUniqueIdCode>();
            for (int i = 0; i < count; i++)
            {
                codes.Add(new BaseUniqueIdCode { Code = code });
            }

            //var userIdLen = 7;  //FxUserId长度，不足补0
            //var idLen = 11;     //Id长度，不足补0

            prefix += fxUserId.ToString().PadLeft(userIdLen, '0');

            var db = DbConnection;

            db.BulkInsert(codes);
            var sql = "select Id from BaseUniqueIdCode WITH(NOLOCK) where Code=@code";
            sql = TranSql(sql);
            var ids = db.Query<string>(sql, new { code }).ToList();

            if (ids == null)
                throw new LogicException("基础商品库唯一编号生成失败");

            foreach (var id in ids)
            {
                //判断ID长度，如果ID长度不够，左侧补0
                var temp = id;
                temp = temp.ToString().PadLeft(idLen, '0');
                list.Add(prefix + temp);
            }
            return list;
        }

        /// <summary>
        /// 获取当前时间
        /// </summary>
        /// <returns></returns>
        public DateTime GetNowTime()
        {
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
            {
                return DateTime.Now;
            }
            var db = DbConnection;
            if (IsUseMySQL)
            {
                return db.ExecuteScalar<DateTime>("SELECT NOW()");
            }
            return db.ExecuteScalar<DateTime>("SELECT GETDATE()");
        }

        #region 基础操作，重写，兼容MySQL
        /// <summary>
        /// 重写，兼容MySQL和SQLServer
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public new long Add(T model)
        {
            if (IsUseMySQL)
            {
                long? result;
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    result = _connection.InsertMysqlWithLongId<T>(model);
                }
                return result ?? 0;
            }
            else
            {
                return base.Add(model).ToLong();
            }
        }

        /// <summary>
        /// 重写，兼容MySQL和SQLServer
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public new bool Update(T model)
        {
            if (IsUseMySQL)
            {
                int result;
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    result = _connection.UpdateMysql<T>(model);
                }
                if (result > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return base.Update(model);
            }
        }

        /// <summary>
        /// 重写，兼容MySQL和SQLServer
        /// </summary>
        /// <param name="models"></param>
        public new void BulkInsert(List<T> models)
        {
            if (IsUseMySQL)
            {
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    if (_connection.State == ConnectionState.Closed)
                        _connection.Open();

                    models.ForEach(model => { _connection.InsertMysqlWithLongId<T>(model); });
                }
            }
            else
            {
                base.BulkInsert(models);
            }
        }
        #endregion
    }
}
