using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using MySql.Data.MySqlClient;
using Z.Dapper.Plus;

namespace DianGuanJiaApp.Data.Repository.DataSyncStatus
{
    public class DataSyncStatusRepository : BaseRepository<Entity.DataSyncStatus.DataSyncStatus>
    {
        private string _connectionString;
        public DataSyncStatusRepository() : base(CustomerConfig.DataSyncStatusDbConnectionString)
        {
            _connectionString = IsBackupDataDuplicationEnvironment
                ? CustomerConfig.DataSyncStatusBackupDbConnectionString
                : CustomerConfig.DataSyncStatusDbConnectionString;
        }

        public DataSyncStatusRepository(string cpt)
        {
            if (IsBackupDataDuplicationEnvironment)
            {
                _connectionString = CustomerConfig.DataSyncStatusBackupDbConnectionString;
            }
            else
            {
                if (cpt == "TouTiao")
                    _connectionString = CustomerConfig.TouTiaoDataSyncStatusDbConnectionString;
                else
                    _connectionString = CustomerConfig.DataSyncStatusDbConnectionString;
            }
        }

        public new IDbConnection DbConnection
        {
            get
            {
                var conn = _connectionString;// CustomerConfig.DataSyncStatusDbConnectionString;
                if (conn?.StartsWith("server") == true)
                {
                    return DbUtility.GetConnection(conn);
                }
                return DbUtility.GetMySqlConnection(conn);
            }
        }

        public new int Add(Entity.DataSyncStatus.DataSyncStatus model)
        {
            if (DbConnection.IsMySqlDb())
            {
                return DbConnection.InsertMysql(model) ?? 0;
            }

            return DbConnection.Insert(model) ?? 0;
        }

        public new bool Update(Entity.DataSyncStatus.DataSyncStatus model)
        {
            const string sql = @"UPDATE FenDan_Data_SyncStatus 
                                    SET Mode = @Mode,LastSyncBeginTime = @LastSyncBeginTime,
		                                LastSyncTime = @LastSyncTime,LastDurations = @LastDurations,IsSameDataSource = @IsSameDataSource,
		                                NextMaxId = @NextMaxId,LastStatus = @LastStatus,LastFailLog = @LastFailLog,LastRetries = @LastRetries,
		                                LastLockRetries = @LastLockRetries,LastServiceNo = @LastServiceNo,LastProcessId = @LastProcessId,
		                                LastTotals = @LastTotals,LastTotalPages = @LastTotalPages,LastIp = @LastIp, UpdateTime = @UpdateTime
                                    WHERE Id = @Id;";
            var row = DbConnection.Execute(sql, model);
            return row > 0;
        }

        public new void BulkInsert(List<Entity.DataSyncStatus.DataSyncStatus> models)
        {
            DbConnection.BulkInsert(models);
        }

        public new void BulkUpdate(List<Entity.DataSyncStatus.DataSyncStatus> models)
        {
            DbConnection.BulkUpdate(models);
        }

        public new void BatchUpdate(List<Entity.DataSyncStatus.DataSyncStatus> models, List<string> updateFieldNames,
            List<string> whereFieldNames)
        {
            DbConnection.BatchUpdate(models, updateFieldNames, whereFieldNames);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="serviceName"></param>
        /// <param name="fxUserId"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public Entity.DataSyncStatus.DataSyncStatus GetByServiceNameFxUserIdShopId(string serviceName, int fxUserId,
            int shopId)
        {
            var db = DbConnection;
            //SQL脚本
            var sql = $@"SELECT * FROM FenDan_Data_SyncStatus{db.WithNoLock()}
                         WHERE ServiceName = @ServiceName AND FxUserId = @FxUserId AND ShopId = @ShopId";
            //查询
            return db.QueryFirstOrDefault<Entity.DataSyncStatus.DataSyncStatus>(sql,
                new { ServiceName = serviceName, FxUserId = fxUserId, ShopId = shopId });
        }

        /// <summary>
        /// 获取用户所有店铺ID信息，按服务名称及用户ID
        /// </summary>
        /// <param name="serviceName"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<FxUserWithShopIdModel> GetFxUserWithShopIds(string serviceName, int fxUserId)
        {
            var db = DbConnection;
            //SQL脚本
            var sql = $@"SELECT FxUserId,ShopId FROM FenDan_Data_SyncStatus{db.WithNoLock()}
                         WHERE ServiceName=@ServiceName AND FxUserId = @FxUserId";
            //查询
            return db.Query<FxUserWithShopIdModel>(sql,
                new { ServiceName = serviceName, FxUserId = fxUserId }).ToList();
        }

        /// <summary>
        /// 获取失败的同步状态用户ID和店铺信息
        /// </summary>
        /// <param name="serviceName"></param>
        /// <returns></returns>
        public List<FxUserWithShopIdModel> GetFailFxUserWithShopIds(string serviceName)
        {
            var db = DbConnection;
            //SQL脚本
            var sql = $@"SELECT FxUserId,ShopId FROM FenDan_Data_SyncStatus{db.WithNoLock()}
                         WHERE ServiceName = @ServiceName AND LastStatus = 3";
            //查询
            return db.Query<FxUserWithShopIdModel>(sql,
                new { ServiceName = serviceName }).ToList();
        }

        /// <summary>
        /// 获取TOP超时未同步状态（LastSyncTime）
        /// </summary>
        /// <param name="serviceName"></param>
        /// <param name="lastTime"></param>
        /// <param name="fetchCount"></param>
        /// <returns></returns>
        public List<FxUserWithShopIdModel> GetTopFxUserWithShopIds(string serviceName, DateTime lastTime, int fetchCount = 100)
        {
            var db = DbConnection;
            var sql = $@"SELECT TOP ({fetchCount}) FxUserId,ShopId FROM FenDan_Data_SyncStatus WHERE ServiceName=@ServiceName AND (LastSyncTime IS NULL OR LastSyncTime<'{lastTime.ToString("yyyy-MM-dd HH:mm:ss")}') ORDER BY LastSyncTime ASC  ";
            if (db is MySqlConnection)
            {
                sql = $@"SELECT FxUserId,ShopId FROM FenDan_Data_SyncStatus WHERE ServiceName=@ServiceName AND (LastSyncTime IS NULL OR LastSyncTime<'{lastTime.ToString("yyyy-MM-dd HH:mm:ss")}') ORDER BY LastSyncTime ASC limit {fetchCount} ";
            }
            return db.Query<FxUserWithShopIdModel>(sql,
                new { ServiceName = serviceName }).ToList();
        }

        /// <summary>
        /// 更新UpdateTime
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public bool SetUpdateTime(long id)
        {
            var db = DbConnection;
            var sql = $@"UPDATE FenDan_Data_SyncStatus SET UpdateTime=GETDATE() WHERE Id={id}  ";
            if (db is MySqlConnection)
            {
                sql = $@"UPDATE FenDan_Data_SyncStatus SET UpdateTime=NOW() WHERE Id={id}  ";
            }
            return db.Execute(sql) > 0;
        }

        /// <summary>
        /// 更新最后状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="lastStatus"></param>
        /// <param name="lastServiceNo"></param>
        /// <param name="lastProcessId"></param>
        /// <param name="lastIp"></param>
        /// <returns></returns>
        public bool UpdateLastStatus(long id, int lastStatus, string lastServiceNo, int lastProcessId, string lastIp)
        {
            var db = DbConnection;
            var sql =
                "UPDATE FenDan_Data_SyncStatus SET LastStatus = @lastStatus,LastServiceNo = @lastServiceNo,LastProcessId = @lastProcessId,LastIp = @lastIp,UpdateTime = GETDATE() WHERE Id = @id";
            if (db is MySqlConnection)
            {
                sql =
                    "UPDATE FenDan_Data_SyncStatus SET LastStatus = @lastStatus,LastServiceNo = @lastServiceNo,LastProcessId = @lastProcessId,LastIp = @lastIp,UpdateTime = NOW() WHERE Id = @id";
            }

            return db.Execute(sql, new
            {
                id, lastStatus, lastServiceNo, lastProcessId, lastIp
            }) > 0;
        }

		/// <summary>
		/// 删除数据
		/// </summary>
		/// <param name="shopIds"></param>
		/// <returns></returns>
		public bool DelDataSyncStatusByShopIds(List<int> shopIds)
        {
            var db = DbConnection;

			var sql = @"delete from fendan_data_syncstatus
	                        where shopId in @shopIds";

            //拼多多表名区分大小写
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
            {
                sql = @"delete from FenDan_Data_SyncStatus
	                        where shopId in @shopIds";
            }

            return db.Execute(sql, new { shopIds })>0;
		}
	}
}