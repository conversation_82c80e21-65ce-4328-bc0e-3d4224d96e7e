using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Dapper;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using System.Data;
using NodaTime;

namespace DianGuanJiaApp.Data.Repository
{
    public class DbConfigRepository : BaseRepository<DbConfig>
    {
        private static int _redisState = -1;
        public DbConfigRepository() : base(CustomerConfig.ConfigureDbConnectionString)
        {
        }
        public DbConfigRepository(string connectionString) : base(connectionString)
        {
        }

        //使用TryGetDbConfigFromCache代替
        //private List<DbConfigModel> ConvertToDbConfigModel(IDbConnection db, List<DbConfig> dbConfigs)
        //{
        //    List<DbConfigModel> models;
        //    var dbNameIds = dbConfigs.Select(x => x.DbNameConfigId).Distinct().ToList() ?? new List<int>();
        //    var coldDbNameIds = dbConfigs.Select(x => x.ColdDbNameConfigId).Where(x => x > 0).Distinct();
        //    if (coldDbNameIds != null && coldDbNameIds.Any())
        //    {
        //        dbNameIds.AddRange(coldDbNameIds);
        //        dbNameIds = dbNameIds.Distinct().ToList();
        //    }
        //    //查询DbServer信息
        //    var dbServerSql = $@"SELECT s.*,n.* FROM dbo.P_DbServerConfig s WITH(NOLOCK) INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId WHERE n.Id IN({string.Join(",", dbNameIds)});";
        //    var dbNameModels = db.Query<DbServerConfig, DbNameConfig, DbNameConfigModel>(dbServerSql, (dbServer, dbName) =>
        //    {
        //        return new DbNameConfigModel { DbNameConfig = dbName, DbServer = dbServer };
        //    }, splitOn: "Id").ToList();
        //    //赋值
        //    models = BuildDbConfigModels(dbConfigs, dbNameModels);
        //    return models;
        //}

        #region DbConfig数据库配置

        /// <summary>
        /// 仅查询DbConfig数据
        /// </summary>
        /// <param name="shopIds"></param>
        /// <param name="cloudPlatformType"></param>
        /// <returns></returns>
        private List<DbConfigModel> GetDbConfigModelsFromDb(List<int> shopIds, string cloudPlatformType)
        {
            if (shopIds == null || !shopIds.Any())
                return null;
            var cptWhereSql = "";
            if (string.IsNullOrEmpty(cloudPlatformType) == false)
                cptWhereSql = $" AND d.DbCloudPlatform = @cpt";
            using (var db = DbConnection)
            {

                var sql = $@"SELECT d.UserId,d.ShopId,d.DbNameConfigId,d.DbCloudPlatform,d.ColdDbNameConfigId,d.ColdDbStatus,0 AS FromFxDbConfig FROM dbo.P_DbConfig d WITH(NOLOCK) WHERE d.ShopId IN@ShopIds {cptWhereSql};";
                var dbConfigs = new List<DbConfig>(shopIds.Count);
                if (shopIds.Count >= 2000)
                {
                    var param = new DynamicParameters();
                    param.Add("cpt", cloudPlatformType);
                    dbConfigs.AddRange(BatchQuery(shopIds, "ShopIds", sql, param));
                }
                else
                {
                    db.Open();
                    dbConfigs.AddRange(db.Query<DbConfig>(sql, new { ShopIds = shopIds, cpt = cloudPlatformType })
                        .ToList());
                }
                if (dbConfigs == null || dbConfigs.Any() == false)
                    return new List<DbConfigModel>();

                if (db.State != ConnectionState.Open)
                    db.Open();
                var models = TryGetDbConfigFromCache(db, dbConfigs);
                return models;
            }
        }

        private List<DbConfigModel> BuildDbConfigModels(List<DbConfig> dbConfigs, List<DbNameConfigModel> dbNameModels)
        {
            var models = new List<DbConfigModel>();
            foreach (var dbConfig in dbConfigs)
            {
                var dbNameModel = dbNameModels.FirstOrDefault(x => x.DbNameConfig.Id == dbConfig.DbNameConfigId);
                if (dbNameModel == null)
                    throw new ArgumentNullException($"DB配置({dbConfig.DbNameConfigId})不存在");
                var tempModel = new DbConfigModel
                {
                    DbConfig = dbConfig,
                    DbNameConfig = dbNameModel.DbNameConfig,
                    DbServer = dbNameModel.DbServer
                };
                //获取冷库数据库配置
                if (dbConfig.ColdDbNameConfigId > 0)
                {
                    var coldDbNameModel = dbNameModels.FirstOrDefault(x => x.DbNameConfig.Id == dbConfig.ColdDbNameConfigId);
                    if (coldDbNameModel == null)
                        throw new ArgumentNullException($"COLDDB配置({dbConfig.DbNameConfigId})不存在");
                    tempModel.SetColdDbServer(coldDbNameModel.DbServer, coldDbNameModel.DbNameConfig);
                }
                models.Add(tempModel);
            }
            return models;
        }

        private List<DbConfigModel> GetDbConfigModelsUseCache(List<int> shopIds, string cloudPlatformType = "")
        {
            if (shopIds == null || !shopIds.Any())
                return null;
            if (_redisState == 0)
                return GetDbConfigModelsFromDb(shopIds, cloudPlatformType);
            //-1表示还未判断过Redis的状态
            if (_redisState == -1)
            {
                try
                {
                    var redisHelper = RedisHelper.Instance;
                    redisHelper.Get("ping");
                    _redisState = 1;
                }
                catch (Exception ex)
                {
                    _redisState = 0;
                    Log.WriteError($"GetDbConfigModelsUseCache，Redis未能连接上，{ex}");
                    return GetDbConfigModelsFromDb(shopIds, cloudPlatformType);
                }
            }

            var dbConfigs = new List<DbConfig>();
            if (IsEnabledGlobalDataCacheKey)
            {
                foreach (var shopId in shopIds)
                {
                    var dbConfigList = GetDbConfigsWithCacheOrDb(shopId, cloudPlatformType);
                    if (dbConfigList == null || dbConfigList.Any() == false) continue;

                    dbConfigs.AddRange(dbConfigList);
                }

                if (dbConfigs.Any() == false)
                    return new List<DbConfigModel>();
            }
            else
            {
                var cptWhereSql = "";
                if (string.IsNullOrEmpty(cloudPlatformType) == false)
                    cptWhereSql = $" AND d.DbCloudPlatform = '{cloudPlatformType}'";
                var db = DbConnection;
                var sql = $@"SELECT d.UserId,d.ShopId,d.DbNameConfigId,d.ColdDbNameConfigId,d.ColdDbStatus FROM dbo.P_DbConfig d WITH(NOLOCK) WHERE d.ShopId IN@shopIds {cptWhereSql}";
                //大于10个使用关联查询
                object sqlParam = null;
                if (shopIds.Count > 10)
                {
                    var idString = string.Join(",", shopIds);
                    sql = $"SELECT d.UserId,d.ShopId,d.DbNameConfigId,d.ColdDbNameConfigId,d.ColdDbStatus FROM dbo.P_DbConfig d WITH(NOLOCK) INNER JOIN dbo.FunStringToIntTable(@Codes,',') t ON d.ShopId=t.item ";
                    if (string.IsNullOrEmpty(cptWhereSql) == false)
                        sql += " where 1=1 " + cptWhereSql;
                    sqlParam = new { Codes = idString };
                }
                else
                    sqlParam = new { shopIds };
                dbConfigs = db.Query<DbConfig>(sql, sqlParam).ToList();
            }

            //根据数据库ID到Redis缓存中查询，缓存中没有再从数据库中查询
            var dbConfigsModel = TryGetDbConfigFromCache(DbConnection, dbConfigs);
            return dbConfigsModel;
        }

        /// <summary>
        /// 仅查询DbConfig数据
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetDbConfigModel(List<int> shopIds)
        {
            return GetDbConfigModelsUseCache(shopIds);
        }

        /// <summary>
        /// 仅查询DbConfig数据
        /// </summary>
        /// <returns></returns>
        public DbConfigModel GetDbConfigModel(int shopId, string cloudPlatformType = "")
        {
            return GetDbConfigModelsUseCache(new List<int> { shopId }, cloudPlatformType)?.FirstOrDefault();
        }

        #endregion

        #region DbConfig+CloudPlatformType\FxDbConfig-分单数据库配置相关

        public DbConfigModel GetFxDbConfigModel(int fxUserId, string dbCloudPlatform = "Alibaba")
        {
            if (fxUserId <= 0) return null;

            if (IsEnabledGlobalDataCacheKey)
            {
                var fxDbConfig = GetFxUserIdConfigWithCache(fxUserId, dbCloudPlatform);
                if (fxDbConfig == null) return null;

                var models = TryGetDbConfigFromCache(DbConnection, new List<DbConfig> { fxDbConfig });
                return models?.FirstOrDefault();
            }

            const string sql = @"
SELECT d.Id,d.FxUserId AS UserId,d.SystemShopId AS ShopId,d.DbNameConfigId,d.DbCloudPlatform,d.FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus
FROM  dbo.FxDbConfig d WITH(NOLOCK) WHERE d.FxUserId = @fxUserId AND d.DbCloudPlatform= @dbCloudPlatform;";
            using (var db = DbConnection)
            {
                db.Open();
                var dbConfigs = db.Query<DbConfig>(sql, new { fxUserId, dbCloudPlatform })?.ToList();
                if (dbConfigs == null || dbConfigs.Any() == false)
                    return null;
                var models = TryGetDbConfigFromCache(db, dbConfigs);
                return models?.FirstOrDefault();
            }
        }

        public List<DbConfigModel> GetFxDbConfigModels(List<int> fxUserIds, string cpt = "")
        {
            if (string.IsNullOrEmpty(cpt))
                cpt = CustomerConfig.CloudPlatformType;
            if (fxUserIds == null || !fxUserIds.Any())
                return null;
            var sql = $@"
SELECT d.Id,d.FxUserId AS UserId,d.SystemShopId AS ShopId,d.DbNameConfigId,d.DbCloudPlatform,d.FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus
FROM  dbo.FxDbConfig d WITH(NOLOCK)
WHERE d.FxUserId IN@FxUserIds AND d.DbCloudPlatform=@cpt;";
            using (var db = DbConnection)
            {
                var dbConfigs = new List<DbConfig>(fxUserIds.Count);
                if (fxUserIds.Count >= 2000)
                {
                    var param = new DynamicParameters();
                    param.Add("cpt", cpt);
                    dbConfigs.AddRange(BatchQuery(fxUserIds, "FxUserIds", sql, param));
                }
                else
                {
                    db.Open();
                    dbConfigs.AddRange(db.Query<DbConfig>(sql, new { FxUserIds = fxUserIds, cpt }).ToList());
                }
                if (dbConfigs == null || dbConfigs.Any() == false)
                    return null;

                if (db.State != ConnectionState.Open)
                    db.Open();
                var models = TryGetDbConfigFromCache(db, dbConfigs);
                return models;
            }
        }

        /// <summary>
        /// 查询FxDbConfig
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        private List<DbConfigModel> GetFxDbConfigModelFxFromDb(List<int> shopIds, string cpt)
        {

            if (shopIds == null || !shopIds.Any())
            {
                return null;
            }

            List<DbConfigModel> models = new List<DbConfigModel>();
            //缓存性能优化:未迁移迁前所有业务库(部分缓存)
            //var orgModel = FxCaching.GetCache(FxCachingType.FxOrgDbConfigModel, string.Join("-", shopIds.OrderBy(p => p)), () => this.GetDbConfigModelsUseCache(shopIds, CustomerConfig.CloudPlatformType));
            //models.AddRange(orgModel);

            //var cpt = CustomerConfig.CloudPlatformType;
            using (var db = DbConnection)
            {
                db.Open();

                var sql = $@"SELECT d.Id,d.FxUserId AS UserId,d.SystemShopId AS ShopId,d.DbNameConfigId,d.DbCloudPlatform,d.DbCloudPlatform,d.ColdDbNameConfigId,d.ColdDbStatus,d.FromFxDbConfig FROM dbo.FxDbConfig d WITH(NOLOCK) WHERE d.SystemShopId IN @systemShopId AND d.DbCloudPlatform =@dbCloudPlatform;";

                var dbConfigs = new List<DbConfig>();
                var dataPageSize = 2000;
                var dataPageCount = Math.Ceiling(shopIds.Count * 1.0 / dataPageSize);

                for (var i = 0; i < dataPageCount; i++)
                {
                    var tempShopIds = shopIds
                        .Skip(i * dataPageSize)
                        .Take(dataPageSize)
                        .ToList();

                    var sqlParamters = new DynamicParameters();
                    sqlParamters.Add("@systemShopId", tempShopIds);
                    sqlParamters.Add("@dbCloudPlatform", cpt);

                    var dataTemps = db.Query<DbConfig>(sql, sqlParamters).ToList();
                    if (dataTemps != null)
                    {
                        dbConfigs.AddRange(dataTemps);
                    }
                }

                if (dbConfigs == null || dbConfigs.Any() == false)
                    return new List<DbConfigModel>();
                models = TryGetDbConfigFromCache(db, dbConfigs);
            }
            return models;
        }


        /// <summary>
        /// 查询FxDbConfig
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        private List<DbConfigModel> GetFxDbConfigModelFxFromDbs(List<int> fxUserIds)
        {
            if (fxUserIds == null || !fxUserIds.Any()) return null;

            using (var db = DbConnection)
            {
                db.Open();
                const string sql = @"SELECT d.Id,d.FxUserId AS UserId,d.SystemShopId AS ShopId,d.DbNameConfigId,d.DbCloudPlatform,
d.DbCloudPlatform,d.ColdDbNameConfigId,d.ColdDbStatus,d.FromFxDbConfig 
FROM dbo.FxDbConfig d WITH(NOLOCK) WHERE d.FxUserId IN @fxUserIds ;";
                var sqlParam = new DynamicParameters();
                sqlParam.Add("@fxUserIds", fxUserIds);
                var dbConfigs = db.Query<DbConfig>(sql, sqlParam)?.ToList();
                if (dbConfigs == null || dbConfigs.Any() == false)
                    return new List<DbConfigModel>();
                var models = TryGetDbConfigFromCache(db, dbConfigs);
                return models;
            }
        }

        /// <summary>
        /// 获取店铺的配置信息并缓存，Key为ShopId和DbCloudPlatform
        /// 如果DbCloudPlatform不为当前平台，则不缓存
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="cloudPlatform"></param>
        /// <returns></returns>
        public List<DbConfig> GetDbConfigsWithCacheOrDb(int shopId, string cloudPlatform = "")
        {
            var result = new List<DbConfig>();
            if (string.IsNullOrWhiteSpace(cloudPlatform))
            {
                // 遍历所有平台
                var cloudTypes = System.Enum.GetNames(typeof(CloudPlatformType)).Select(x => x.ToString()).ToList();
                foreach (var cpt in cloudTypes)
                {
                    var dbConfig = GetDbConfigWithCache(shopId, cpt);
                    if (dbConfig != null)
                    {
                        result.Add(dbConfig);
                    }
                }

                return result;
            }

            // 仅查询当前平台
            var dbConfigSingle = GetDbConfigWithCache(shopId, cloudPlatform);
            if (dbConfigSingle != null) result.Add(dbConfigSingle);

            return result;
        }

        /// <summary>
        /// 获取店铺的配置信息并缓存，Key为ShopId和DbCloudPlatform
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="cpt"></param>
        /// <returns></returns>
        public DbConfig GetDbConfigWithCache(int shopId, string cpt)
        {
            cpt = string.IsNullOrEmpty(cpt) ? CustomerConfig.CloudPlatformType : cpt;

            var sql = $"SELECT * FROM dbo.P_DbConfig d WITH(NOLOCK) WHERE d.ShopId = @shopId AND d.DbCloudPlatform = '{cpt}'";

            return IsEnabledGlobalDataCacheKey ?
                DbConnection.QueryWithSingleCache<DbConfig, int>(sql, new List<int> { shopId }, appendCacheKey: cpt, parameterName: "shopId", keyFieldName: "ShopId", cacheExpireSeconds: 900).FirstOrDefault() :
                DbConnection.Query<DbConfig>(sql, new { shopId, cpt })?.FirstOrDefault();
        }

        /// <summary>
        /// 获取用户的配置信息并转换成DbConfig
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="cpt"></param>
        /// <returns></returns>
        private DbConfig GetFxUserIdConfigWithCache(int fxUserId, string cpt = null)
        {
            var result = new FxDbConfigRepository().GetFxDbConfigsWithCache(fxUserId, cpt);
            if (result == null) return null;
            // 转换成DbConfig
            var dbConfig = new DbConfig
            {
                ColdDbNameConfigId = result.ColdDbNameConfigId,
                ColdDbStatus = result.ColdDbStatus,
                DbCloudPlatform = result.DbCloudPlatform,
                DbNameConfigId = result.DbNameConfigId,
                FromFxDbConfig = result.FromFxDbConfig,
                Id = result.Id,
                ShopId = result.SystemShopId,
                UserId = result.FxUserId
            };
            return dbConfig;
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="db"></param>
        /// <param name="dbConfigs"></param>
        /// <param name="isFromCache"></param>
        /// <returns></returns>
        private List<DbConfigModel> TryGetDbConfigFromCache(IDbConnection db, List<DbConfig> dbConfigs,
            bool isFromCache = false)
        {
            var dbNameRedisCacheKeyPrefix = "System:DbNameConfig";
            var dbNameIds = dbConfigs.Select(x => x.DbNameConfigId).Distinct().ToList();
            var coldDbNameIds = dbConfigs.Where(x => x.ColdDbNameConfigId > 0).Select(x => x.ColdDbNameConfigId)
                .Distinct().ToList();
            if (coldDbNameIds.Any())
            {
                dbNameIds.AddRange(coldDbNameIds);
                dbNameIds = dbNameIds.Distinct().ToList();
            }
            var dbNameKeys = dbNameIds.Select(x => $"{dbNameRedisCacheKeyPrefix}:{x}").ToArray();
            var dbNameModels = new List<DbNameConfigModel>();
            bool isGetUseRedis = false;
            if (_redisState == 1 || isFromCache)
            {
                try
                {
                    dbNameModels =
                        RedisHelper.MGet<DbNameConfigModel>(dbNameKeys)
                            ?.Where(x => x != null && x.DbNameConfig != null && x.DbServer != null)?.ToList() ??
                        new List<DbNameConfigModel>();
                    isGetUseRedis = true;
                }
                catch (Exception ex)
                {
                    isGetUseRedis = false;
                    Log.WriteError($"TryGetDbConfigFromCache，RedisHelper.MGet异常：{ex}");
                }
            }
            var notCachedDbNameIds = dbNameIds.Where(x =>
                dbNameModels == null ||
                dbNameModels.Any(y => y.DbNameConfig != null && y.DbNameConfig.Id == x) == false).ToList();
            if (notCachedDbNameIds != null && notCachedDbNameIds.Any())
            {
                //从数据库中获取
                var sql2 =
                    "SELECT * FROM dbo.P_DbServerConfig s WITH(NOLOCK) INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId WHERE n.Id IN @Ids";
                var tempModels = db.Query<DbServerConfig, DbNameConfig, DbNameConfigModel>(sql2, (server, dbname) =>
                {
                    var temp = new DbNameConfigModel { DbServer = server, DbNameConfig = dbname };
                    return temp;
                }, new { Ids = notCachedDbNameIds }, splitOn: "Id,Id").ToList();

                if (tempModels != null && tempModels.Any())
                {
                    if (isGetUseRedis)
                    {
                        //更新缓存
                        tempModels.ForEach(x =>
                        {
                            RedisHelper.Set($"{dbNameRedisCacheKeyPrefix}:{x.DbNameConfig.Id}", x,
                                expireSeconds: 300);
                        });
                    }

                    dbNameModels.AddRange(tempModels);
                }
            }

            //拼装成DbConfigModel
            var dbConfigsModel = BuildDbConfigModels(dbConfigs, dbNameModels);

            return dbConfigsModel;
        }

        private List<DbConfigModel> GetFxDbConfigModelsUseCache(List<int> shopIds, string cpt)
        {
            if (shopIds == null || !shopIds.Any())
                return null;
            if (_redisState == 0)
            {
                Log.Debug(() => $"获取数据库配置（{string.Join("|", shopIds)}），命中缓存",
                    $"GetFxDbConfigModelsUseCache_{DateTime.Now:yyyy-MM-dd}.log");
                return GetFxDbConfigModelFxFromDb(shopIds, cpt);
            }

            //-1表示还未判断过Redis的状态
            if (_redisState == -1)
            {
                try
                {
                    var redisHelper = RedisHelper.Instance;
                    _redisState = 1;
                }
                catch (Exception ex)
                {
                    _redisState = 0;
                    Log.WriteError($"GetDbConfigModelsUseCache，Redis未能连接上，{ex}");
                    Log.Debug("获取数据库配置，命中缓存", $"GetFxDbConfigModelsUseCache_{DateTime.Now:yyyy-MM-dd}.log");
                    return GetFxDbConfigModelFxFromDb(shopIds, cpt);
                }
            }

            Log.Debug(() => "获取数据库配置，未命中缓存", $"GetFxDbConfigModelsUseCache_{DateTime.Now:yyyy-MM-dd}.log");
            //var cpt = CustomerConfig.CloudPlatformType;
            var db = DbConnection;
            var sql = $@"SELECT d.Id,d.FxUserId AS UserId,d.SystemShopId AS ShopId,d.DbNameConfigId,d.DbCloudPlatform,d.FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus FROM dbo.FxDbConfig d WITH(NOLOCK) WHERE d.SystemShopId IN@shopIds  AND d.DbCloudPlatform ='{cpt}'";
            //大于10个使用关联查询
            object sqlParam = null;
            if (shopIds.Count() > 10)
            {
                var idString = string.Join(",", shopIds);
                sql = $"SELECT d.Id,d.FxUserId AS UserId,d.SystemShopId AS ShopId,d.DbNameConfigId,d.DbCloudPlatform,d.FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus FROM dbo.FxDbConfig d WITH(NOLOCK) INNER JOIN dbo.FunStringToIntTable(@Codes,',') t ON d.SystemShopId=t.item WHERE d.DbCloudPlatform ='{cpt}'";
                sqlParam = new { Codes = idString };
            }
            else
                sqlParam = new { shopIds };
            var dbConfigs = db.Query<DbConfig>(sql, sqlParam).ToList();

            if (dbConfigs == null || dbConfigs.Any() == false)
                return new List<DbConfigModel>();

            //根据数据库ID到Redis缓存中查询，缓存中没有再从数据库中查询
            var dbConfigsModel = TryGetDbConfigFromCache(db, dbConfigs);
            return dbConfigsModel;
        }

        /// <summary>
        /// (已含新版)-根据云平台决定查询的是FxDbConfig还是DbConfig
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetDbConfigModelFx(List<int> shopIds)
        {
            //判空处理
            if (shopIds == null || !shopIds.Any())
            {
                return null;
            }

            //支持分库的平台，仅需要查询对应的表
            var cpt = CustomerConfig.CloudPlatformType;
            //如果是跨境站点，则使用跨境配置获取业务库配置
            if (CustomerConfig.IsCrossBorderSite || BaseSiteContext.GetIsCrossBorder())
                cpt = CustomerConfig.CrossBorderCloudLocation;
            //获取新分库用户数据库配置
            if (CustomerConfig.UseFxDbConfigCloudPlatformTypes.Contains(cpt))
            {
                return GetFxDbConfigModelsUseCache(shopIds, cpt);
            }

            return FxCaching.GetCache(FxCachingType.FxOrgDbConfigModel, string.Join("-", shopIds.OrderBy(p => p)), () =>
            {
                //所有库
                var allDbConfigs = new List<DbConfigModel>();
                //旧库
                var dbConfigs = this.GetDbConfigModelsUseCache(shopIds, cpt);
                if (dbConfigs != null && dbConfigs.Any())
                {
                    allDbConfigs.AddRange(dbConfigs);
                }

                //新库
                var fxDbConfigs = GetFxDbConfigModelFxFromDb(shopIds, cpt);
                if (fxDbConfigs != null && fxDbConfigs.Any())
                {
                    allDbConfigs.AddRange(fxDbConfigs);
                }

                //返回
                return allDbConfigs;
            });
        }

        #endregion


        public DbConfigModel GetDbConfigModelByDbNameId(int dbNameId)
        {
            //缓存性能优化:通过库名获得配置库
            return FxCaching.GetCache(FxCachingType.DbConfigModelByDbName, dbNameId.ToString(), () => GetDbConfigModelByDbNameId(new List<int> { dbNameId })?.FirstOrDefault());
            //return GetDbConfigModelByDbNameId(new List<int> { dbNameId })?.FirstOrDefault();
        }

        public List<DbConfigModel> GetDbConfigModelByDbNameId(List<int> dbNameIds)
        {
            if (dbNameIds == null || !dbNameIds.Any())
                return null;

            // 通过缓存获取数据
            var dbServerConfigs = GetDbServerConfigsWithCache();
            var dbNameConfigs = GetDbNameConfigsByIdsWithCache(dbNameIds);

            // 合并数据
            var model = dbServerConfigs.Join(dbNameConfigs, s => s.Id, n => n.DbServerConfigId, (s, n) =>
            {
                var temp = new DbConfigModel { DbServer = s, DbNameConfig = n };
                return temp;
            }).ToList();

            return model;
            //             
            //             
            //             var db = DbConnection;
            //             var sql = $@"
            // SELECT *
            // FROM dbo.P_DbServerConfig s WITH(NOLOCK)
            //     INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK)
            //         ON s.Id = n.DbServerConfigId
            // WHERE n.Id IN ({string.Join(",", dbNameIds)});";
            //             var model = db.Query<DbServerConfig, DbNameConfig, DbConfigModel>(sql, (server, dbname) =>
            //             {
            //                 var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname };
            //                 return temp;
            //             }, splitOn: "Id,Id,Id").ToList();
            //             return model;
        }

        public List<DbConfigModel> GetAllDbConfigModel()
        {
            var db = DbConnection;
            var sql = $@"
SELECT *
FROM dbo.P_DbServerConfig s WITH(NOLOCK)
    INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK)
        ON s.Id = n.DbServerConfigId;";
            var model = db.Query<DbServerConfig, DbNameConfig, DbConfigModel>(sql, (server, dbname) =>
            {
                var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname };
                return temp;
            }, splitOn: "Id,Id,Id").ToList();
            return model;
        }

        public List<DbConfigModel> GetAllNewDbConfigModel()
        {
            var whereSql = string.Empty;
            if (CustomerConfig.IsDebug)
            {
                whereSql = $@" AND n.DbName <> 'AlibabaFenFaDB_TikTok' AND n.DbName <> 'PinduoduoFenDanDB_0'";
            }
            var db = DbConnection;
            var sql = $@"SELECT
	* 
FROM
	dbo.P_DbServerConfig s WITH ( NOLOCK )
	INNER JOIN dbo.P_DbNameConfig n WITH ( NOLOCK ) ON s.Id = n.DbServerConfigId 
WHERE
	n.ApplicationName IN ( 'fx_new', 'fx_cloud' ) 
	AND n.RunningStatus = 'Running' 
	AND n.DbName <> 'pdd_fendan_db' 
	AND n.DbName <> 'tiktok_fendan_1' {whereSql};";
            var model = db.Query<DbServerConfig, DbNameConfig, DbConfigModel>(sql, (server, dbname) =>
            {
                var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname };
                return temp;
            }, splitOn: "Id,Id,Id").ToList();
            return model;
        }

        /// <summary>
        /// 获取所有数据库配置信息，按云平台
        /// </summary>
        /// <returns></returns>
        public List<DbConfigModel> GetAllDbConfigsByCloudPlatform()
        {
            var db = DbConnection;
            var sql = $@"SELECT *
                         FROM dbo.P_DbServerConfig s WITH(NOLOCK)
                            INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK)
                                ON s.Id = n.DbServerConfigId WHERE s.Location = '{CustomerConfig.CloudPlatformType}';";
            var model = db.Query<DbServerConfig, DbNameConfig, DbConfigModel>(sql, (server, dbname) =>
            {
                var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname };
                return temp;
            }, splitOn: "Id,Id,Id").ToList();
            return model;
        }

        ///// <summary>
        ///// 获取店铺最佳的数据库配置，优先取对应平台的库，再取数据量少的库
        ///// </summary>
        ///// <param name="platformType">店铺平台类型</param>
        ///// <param name="targetCloudPlatformType">目标数据库平台</param>
        ///// <returns></returns>
        //public DbNameConfig GetBestDbNameConfig(string shopPlatformType, string targetCloudPlatformType = "")
        //{
        //    var pts = new List<string> { PlatformType.Alibaba.ToString(), PlatformType.Jingdong.ToString(), PlatformType.Pinduoduo.ToString(), PlatformType.Taobao.ToString() };
        //    var pt = shopPlatformType;
        //    if (pts.Contains(shopPlatformType))
        //        pt = shopPlatformType;
        //    else
        //        pt = "other";
        //    var sql = $"SELECT dc.* FROM dbo.P_DbNameConfig dc WITH(NOLOCK) INNER JOIN P_DbServerConfig ds WITH(NOLOCK) ON dc.DbServerConfigId = ds.Id  WHERE dc.RunningStatus='Running' AND dc.IsInSelection=1 AND (dc.DbPlatformType like'%{pt}%' OR dc.DbPlatformType IN('*','other')) ";
        //    if (string.IsNullOrEmpty(targetCloudPlatformType))
        //        sql += $" AND ds.Location='{targetCloudPlatformType}' ";
        //    var db = DbConnection;
        //    var names = db.Query<DbNameConfig>(sql).ToList();
        //    if (names == null || !names.Any())
        //    {
        //        var msg = $"平台【{pt}】未查询到可用的数据库配置，请联系我们";
        //        Log.WriteError(msg);
        //        throw new Exception(msg);
        //    }
        //    var bestDbConfig = names.Where(s => s.DbPlatformType == pt).OrderBy(n => n.CurColdOrderCount + n.CurHotOrderCount).FirstOrDefault();
        //    var secondDbConfig = names.OrderBy(n => n.CurColdOrderCount + n.CurHotOrderCount).FirstOrDefault();
        //    return bestDbConfig ?? secondDbConfig;
        //}

        ///// <summary>
        ///// 获取店铺最佳的数据库配置，优先取对应平台的库，再取数据量少的库
        ///// </summary>
        ///// <returns></returns>
        //public DbNameConfig GetBestDbNameConfigV2(Shop shop)
        //{
        //    var pts = new List<string> { PlatformType.Alibaba.ToString(), PlatformType.Jingdong.ToString(), PlatformType.Pinduoduo.ToString(), PlatformType.Taobao.ToString() };
        //    var pt = shop.PlatformType;
        //    var sql = $"SELECT * FROM dbo.P_DbNameConfig WITH(NOLOCK) WHERE RunningStatus='Running' AND IsInSelection=1 AND (DbPlatformType like'%{pt}%' OR DbPlatformType IN('*','other'))";
        //    var db = DbConnection;
        //    var names = db.Query<DbNameConfig>(sql).ToList();
        //    if (names == null || !names.Any())
        //    {
        //        var msg = $"平台【{pt}】未查询到可用的数据库配置，请联系我们";
        //        Log.WriteError(msg);
        //        throw new Exception(msg);
        //    }
        //    var bestDbConfig = names.Where(s => s.DbPlatformType == pt).OrderBy(n => n.CurColdOrderCount + n.CurHotOrderCount).FirstOrDefault();
        //    var secondDbConfig = names.OrderBy(n => n.CurColdOrderCount + n.CurHotOrderCount).FirstOrDefault();
        //    return bestDbConfig ?? secondDbConfig;
        //}

        /// <summary>
        /// 获取数据库配置信息的数据库位置
        /// </summary>
        /// <param name="dbNameConfigIds"></param>
        /// <returns>数据库服务器位置，目前仅有：Alibaba\Pinduoduo\Jingdong</returns>
        public List<string> GetDbNameConfigLocationByIds(List<int> dbNameConfigIds)
        {
            var rs = DbConnection.Query<string>("SELECT DISTINCT Location FROM dbo.P_DbServerConfig ds WITH(NOLOCK) INNER JOIN dbo.P_DbNameConfig dn WITH(NOLOCK) ON ds.Id = dn.DbServerConfigId WHERE dn.Id IN@ids", new { ids = dbNameConfigIds }).ToList();
            return rs;
        }

        public List<DbNameConfigLocation> GetDbNameConfigLocations(List<int> dbNameConfigIds)
        {
            var rs = DbConnection.Query<DbNameConfigLocation>("SELECT dn.Id AS DbNameConfigId,Location FROM dbo.P_DbServerConfig ds WITH(NOLOCK) INNER JOIN dbo.P_DbNameConfig dn WITH(NOLOCK) ON ds.Id = dn.DbServerConfigId WHERE dn.Id IN@ids", new { ids = dbNameConfigIds }).ToList();
            return rs;
        }


        #region 已注释

        //public DbConfig CreateDbConfig(Shop shop, DbNameConfig dbNameConfig)
        //{
        //    var db = DbConnection;
        //    var config = db.Query<DbConfig>($"select * from P_DbConfig WITH(NOLOCK) where ShopId={shop.Id} AND (IsDisabled IS NULL OR IsDisabled=0) Order By Id desc").FirstOrDefault();
        //    if (config == null)
        //    {
        //        config = new DbConfig
        //        {
        //            DbNameConfigId = dbNameConfig.Id,
        //            ShopId = shop.Id,
        //            IsHotTableEnabled = true,
        //            CreateTime = DateTime.Now,
        //            IsDisabled = false
        //        };
        //        var id = db.Insert(config);
        //        if (id == null)
        //            throw new Exception($"初始化店铺【{shop.ShopName} {shop.Id}】的数据库配置时发生错误");
        //        config.Id = id.Value;
        //    }
        //    return config;
        //}

        //public DataMigrateTask CreateDataMigrateTask(DataMigrateTask task)
        //{
        //    var id = DbConnection.Insert(task);
        //    task.Id = id.Value;
        //    return task;
        //}

        //public DataMigrateTask GetDataMigrateTaskByShopId(int shopid)
        //{
        //    var tasks = DbConnection.Query<DataMigrateTask>("select * from P_DataMigrateTask WITH(NOLOCK) where ShopId=@shopid", new { shopid });
        //    return tasks?.FirstOrDefault();
        //}

        //public void UpdateDataMigrateTask(int taskId, DateTime hopeTime)
        //{
        //    DbConnection.Execute("update P_DataMigrateTask set HopeMigrateTime=@hopeTime where Id=@taskId", new { taskId, hopeTime });
        //}

        //public void UpdateDataMigrateTask(List<int> taskIds, DateTime hopeTime)
        //{
        //    DbConnection.Execute("update P_DataMigrateTask set HopeMigrateTime=@hopeTime where Id IN@taskIds", new { taskIds, hopeTime });
        //}

        //public DataMigrateTask GetDataMigrateTask(int taskId)
        //{
        //    var task = DbConnection.Query<DataMigrateTask>($"select * from P_DataMigrateTask WITH(NOLOCK) where id={taskId}").FirstOrDefault();
        //    return task;
        //}

        //public int GetShopOrderCount(Shop shop)
        //{
        //    var dbConfig = GetDbConfigModel(shop.Id);
        //    var connectionString = dbConfig?.ConnectionString;
        //    if (string.IsNullOrEmpty(connectionString))
        //        connectionString = CustomerConfigExt.GetConnectString(shop.PlatformType);
        //    var db = Dapper.DbUtility.GetConnection(connectionString);
        //    return db.ExecuteScalar<int>($"select count(1) from P_Order WITH(NOLOCK) where ShopId={shop.Id} AND CreateTime>'{DateTime.Now.AddDays(-90).ToString("yyyy-MM-dd")}'");
        //}

        //public List<DbNameConfig> GetAllDbNameConfigs(List<int> shopIds = null)
        //{
        //    //TODO:排除默认数据库
        //    var sql = "select dn.* from P_DbNameConfig dn WITH(NOLOCK) where dn.RunningStatus ='Running'";
        //    if (shopIds != null && shopIds.Any())
        //        sql = $"select dn.* from P_DbNameConfig dn WITH(NOLOCK) LEFT JOIN P_DbConfig dc WITH(NOLOCK) ON dn.Id = dc.DbNameConfigId where dn.RunningStatus ='Running' AND dc.ShopId IN({string.Join(",", shopIds)})";
        //    var db = Dapper.DbUtility.GetConfigureConnection();
        //    return db.Query<DbNameConfig>(sql).ToList();
        //}

        #endregion

        /// <summary>
        /// 非业务方法：仅供工具程序使用
        /// </summary>
        /// <param name="shopIds"></param>
        /// <param name="extWhere"></param>
        /// <param name="paramters"></param>
        /// <returns></returns>
        public List<DbNameConfig> GetAllDbNameSimpleConfigs(List<int> shopIds = null, string extWhere = "", DynamicParameters paramters = null)
        {
            //TODO:排除默认数据库
            var sql = "select distinct dn.Id,ds.Location AS DbPlatformType,dn.DbServerConfigId,dn.DbName,dn.ApplicationName  from P_DbNameConfig dn WITH(NOLOCK) INNER JOIN dbo.P_DbServerConfig ds WITH(NOLOCK) ON dn.DbServerConfigId = ds.Id where dn.RunningStatus ='Running'";
            if (shopIds != null && shopIds.Any())
            {
                sql = "select distinct dn.Id,ds.Location AS DbPlatformType,dn.DbServerConfigId,dn.DbName,dn.ApplicationName  FROM dbo.P_DbConfig dc WITH(NOLOCK) INNER JOIN P_DbNameConfig dn WITH(NOLOCK) ON dc.DbNameConfigId=dn.Id INNER JOIN dbo.P_DbServerConfig ds WITH(NOLOCK) ON dn.DbServerConfigId = ds.Id where dn.RunningStatus ='Running'";
                sql += $"AND dc.ShopId IN({string.Join(",", shopIds)})";
            }
            if (string.IsNullOrEmpty(extWhere) == false)
                sql += extWhere;
            var db = Dapper.DbUtility.GetConfigureConnection();
            if (paramters != null)
                return db.Query<DbNameConfig>(sql, paramters).ToList();
            else
                return db.Query<DbNameConfig>(sql).ToList();
        }

        public List<DbNameConfig> GetPlatformAllDbNameSimpleConfigs(string platformType, List<int> shopIds = null)
        {
            //TODO:排除默认数据库
            var sql = $"select distinct dn.Id,ds.Location AS DbPlatformType,dn.DbServerConfigId,dn.DbName,dn.NickName  from P_DbNameConfig dn WITH(NOLOCK) INNER JOIN dbo.P_DbServerConfig ds WITH(NOLOCK) ON dn.DbServerConfigId = ds.Id where dn.RunningStatus ='Running' AND dn.DbPlatformType='{platformType}'";
            if (shopIds != null && shopIds.Any())
            {
                sql = $"select distinct dn.Id,ds.Location AS DbPlatformType,dn.DbServerConfigId,dn.DbName,dn.NickName  FROM dbo.P_DbConfig dc WITH(NOLOCK) INNER JOIN P_DbNameConfig dn WITH(NOLOCK) ON dc.DbNameConfigId=dn.Id INNER JOIN dbo.P_DbServerConfig ds WITH(NOLOCK) ON dn.DbServerConfigId = ds.Id where dn.RunningStatus ='Running' AND dn.DbPlatformType='{platformType}'";
                sql += $"AND dc.ShopId IN({string.Join(",", shopIds)})";
            }
            var db = Dapper.DbUtility.GetConfigureConnection();
            return db.Query<DbNameConfig>(sql).ToList();
        }

        public List<DbConfig> GetDbConfigs(List<int> shopIds)
        {
            var db = Dapper.DbUtility.GetConfigureConnection();
            if (shopIds == null || !shopIds.Any())
                return new List<DbConfig>();

            const string sql = "select * from P_DbConfig WITH(NOLOCK) where ShopId IN@sids";

            if (shopIds.Count >= 1000)
            {
                return BatchQuery(shopIds, "sids", sql, null);
            }

            return db.Query<DbConfig>(sql, new { sids = shopIds }).ToList();

        }

        /// <summary>
        /// (已含新版)
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public List<ApiDbConfigModel> GetDbConfigNameIdAndPlatFormType(List<int> shopIds)
        {
            string sql = $@"SELECT DISTINCT dn.Id as DbNameConfigId,
                               ds.Location as PlatformType
                        FROM P_DbNameConfig dn WITH(NOLOCK)
                            INNER JOIN P_DbConfig dc WITH(NOLOCK)
                                ON dn.Id = dc.DbNameConfigId
                            INNER JOIN dbo.P_DbServerConfig ds WITH(NOLOCK)
                                ON dn.DbServerConfigId = ds.Id
                        WHERE dn.RunningStatus ='Running' AND dc.ShopId IN ({string.Join(",", shopIds)})
;
SELECT DISTINCT dn.Id as DbNameConfigId,
                               ds.Location as PlatformType
                        FROM P_DbNameConfig dn WITH(NOLOCK)
                            INNER JOIN FxDbConfig dc WITH(NOLOCK)
                                ON dn.Id = dc.DbNameConfigId
                            INNER JOIN dbo.P_DbServerConfig ds WITH(NOLOCK)
                                ON dn.DbServerConfigId = ds.Id
                        WHERE dn.RunningStatus ='Running' AND dc.SystemShopId IN ({string.Join(",", shopIds)}) ";
            //return DbConnection.Query<ApiDbConfigModel>(sql).ToList();

            var grid = DbConnection.QueryMultiple(sql);
            var models = grid.Read<ApiDbConfigModel>().ToList();
            models.AddRange(grid.Read<ApiDbConfigModel>().ToList());
            return models;
        }

        #region 分单系统分库相关

        /// <summary>
        /// 根据ApplicationName获取所有的库
        /// </summary>
        /// <returns></returns>
        public List<DbConfigModel> GetFxBusinessDbConfigs(List<string> applicationNames)
        {
            //缓存性能优化:ApplicationName获取所有的库
            //return FxCaching.GetCache(FxCachingType.FxBusinessDbConfigs, string.Join("-", applicationNames.OrderBy(p => p).ToList()), () => this.GetFxBusinessConfigs(applicationNames));

            //使用新版缓存（拆分查询）
            return GetDbConfigModelsByApplicationNamesWithCache(applicationNames);
        }

        /// <summary>
        /// 获取当前云业务库配置，按应用名称列表
        /// </summary>
        /// <param name="applicationNames"></param>
        /// <returns></returns>
        [Obsolete("作废，建议使用 GetDbConfigModelsByApplicationNamesWithCache 方法")]
        public List<DbConfigModel> GetFxBusinessConfigs(List<string> applicationNames, string platform = null)
        {
            var db = DbConnection;
            var sql = $@"
                        SELECT *
                        FROM dbo.P_DbServerConfig s WITH(NOLOCK) 
                        INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
                        WHERE n.applicationName IN @applicationNames and s.Location = @Platform AND n.RunningStatus ='Running'";
            var model = new List<DbConfigModel>();
            var cloudPlatformType = platform;
            if (cloudPlatformType == null)
            {
                cloudPlatformType = CustomerConfig.CloudPlatformType;
            }
            var sqlParams = new DynamicParameters();
            sqlParams.Add($"@applicationNames", applicationNames);
            sqlParams.Add($"@Platform", cloudPlatformType);

            if (!IsEnabledGlobalDataCacheKey)
            {
                model = db.Query<DbServerConfig, DbNameConfig, DbConfigModel>(sql, (server, dbname) =>
                {
                    var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname };
                    return temp;
                }, sqlParams, splitOn: "Id,Id").ToList();
                return model;
            }
            else
            {
                if (applicationNames == null || !applicationNames.Any())
                    return new List<DbConfigModel>();

                var dbNameConfigs = GetDbNameConfigsByName(applicationNames);
                if (dbNameConfigs == null || !dbNameConfigs.Any())
                    return new List<DbConfigModel>();

                var dbServerConfigs = GetDbServerConfigsWithCache();
                dbServerConfigs = dbServerConfigs.Where(p => p.Location == cloudPlatformType.ToString()).ToList();
                if (dbServerConfigs == null || !dbServerConfigs.Any())
                    return new List<DbConfigModel>();

                //关联
                return dbNameConfigs.Join(dbServerConfigs, nc => nc.DbServerConfigId, sc => sc.Id,
                    (nc, sc) => new DbConfigModel { DbNameConfig = nc, DbServer = sc }).ToList();
            }
        }

        /// <summary>
        /// 获取当前云业务库配置，按应用名称列表，并缓存
        /// </summary>
        /// <param name="applicationNames"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetDbConfigModelsByApplicationNamesWithCache(List<string> applicationNames)
        {
            //判空处理
            if (applicationNames == null || !applicationNames.Any())
            {
                return new List<DbConfigModel>();
            }
            //获取所有配置
            var dbConfigModels = GetAllDbConfigModelWithCache();
            //判空处理
            if (dbConfigModels == null || !dbConfigModels.Any())
            {
                return new List<DbConfigModel>();
            }
            //查询
            return dbConfigModels.Where(m => applicationNames.Contains(m.DbNameConfig.ApplicationName) &&
                                             m.DbNameConfig.RunningStatus == "Running" &&
                                             m.DbServer.Location == CurrentCloudPlatformType).ToList();

        }

        /// <summary>
        /// 获取当前云业务库配置，按应用名称列表和云平台类型，并缓存
        /// </summary>
        /// <param name="applicationNames"></param>
        /// <param name="locations"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetDbConfigModelsByApplicationNamesAndLocationsWithCache(
            List<string> applicationNames, List<string> locations)
        {
            //判空处理
            if (applicationNames == null || !applicationNames.Any())
            {
                return new List<DbConfigModel>();
            }
            if (locations == null || !locations.Any())
            {
                return new List<DbConfigModel>();
            }
            //获取所有配置
            var dbConfigModels = GetAllDbConfigModelWithCache();
            //判空处理
            if (dbConfigModels == null || !dbConfigModels.Any())
            {
                return new List<DbConfigModel>();
            }
            //查询
            return dbConfigModels.Where(m => applicationNames.Contains(m.DbNameConfig.ApplicationName) &&
                                             m.DbNameConfig.RunningStatus == "Running" &&
                                             locations.Contains(m.DbServer.Location)).ToList();
        }

        /// <summary>
        /// 获取分单系统所有平台的业务库（只取旧版：fx、fx_cloud）
        /// </summary>
        /// <returns></returns>
        public List<DbConfigModel> GetFxAllCloudPlatformBusinessDbConfigs()
        {
            return FxCaching.GetCache(FxCachingType.FxBusinessAllDbConfigs, "fx-fx_cloud", () =>
            {
                var db = DbConnection;
                var sql = $@"SELECT *
                        FROM dbo.P_DbServerConfig s WITH(NOLOCK) 
                        INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
                        WHERE n.ApplicationName IN @applicationNames AND n.RunningStatus ='Running'";

                var sqlParam = new DynamicParameters();
                var applicationNames = new List<string> { "fx", "fx_cloud" };
                sqlParam.Add("@applicationNames", applicationNames);

                if (!IsEnabledGlobalDataCacheKey)
                {
                    var model = db.Query<DbServerConfig, DbNameConfig, DbConfigModel>(sql, (server, dbname) =>
                    {
                        var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname };
                        return temp;
                    }, splitOn: "Id,Id", param: sqlParam).ToList();
                    return model;
                }
                else
                {
                    var dbNameConfigs = GetDbNameConfigsByName(applicationNames);
                    if (dbNameConfigs == null || !dbNameConfigs.Any())
                    {
                        return new List<DbConfigModel>();
                    }

                    var dbServerConfigs = GetDbServerConfigsWithCache();
                    if (dbServerConfigs == null || !dbServerConfigs.Any())
                        return new List<DbConfigModel>();

                    return dbNameConfigs.Join(dbServerConfigs, nc => nc.DbServerConfigId, sc => sc.Id,
                        (nc, sc) => new DbConfigModel { DbNameConfig = nc, DbServer = sc }).ToList();
                }
            });
        }

        /// <summary>
        /// 获取分单系统所有平台的业务库 Alibaba
        /// </summary>
        /// <returns></returns>
        public List<DbConfigModel> GetFxAllCloudPlatformBusinessDbConfigsByAlibaba()
        {
            //return GetFxBusinessDbConfigs(new List<string> { "fx", "fx_cloud" });
            //var db = DbConnection;
            //var sql = $@"SELECT *
            //            FROM dbo.P_DbServerConfig s WITH(NOLOCK) 
            //            INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
            //            WHERE n.ApplicationName IN('fx','fx_cloud','fx_new') AND n.RunningStatus ='Running' AND s.Location = 'Alibaba'";
            //    var model = db.Query<DbServerConfig, DbNameConfig, DbConfigModel>(sql, (server, dbname) =>
            //    {
            //        var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname };
            //        return temp;
            //    }, splitOn: "Id,Id").ToList();
            //    return model;
            var applicationNames = new List<string> { "fx", "fx_cloud", "fx_new" };
            var platform = "Alibaba";
            var datas = GetFxBusinessConfigs(applicationNames, platform);
            return datas;
        }

        /// <summary>
        /// 获取业务库
        /// </summary>
        /// <param name="applicationNames"></param>
        /// <param name="locations"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetBusinessDbConfigsByAppNamesWithLocations(List<string> applicationNames,
            List<string> locations)
        {
            //判空处理
            if (applicationNames == null || !applicationNames.Any())
            {
                return new List<DbConfigModel>();
            }
            if (locations == null || !locations.Any())
            {
                return new List<DbConfigModel>();
            }
            //是否开启缓存
            if (IsEnableDbConfigCache)
            {
                return GetDbConfigModelsByApplicationNamesAndLocationsWithCache(applicationNames, locations);
            }
            //未开启缓存
            var db = DbConnection;
            const string sql = @"SELECT *
                        FROM dbo.P_DbServerConfig s WITH(NOLOCK) 
                        INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
                        WHERE n.applicationName IN @applicationNames and s.Location IN @Locations AND n.RunningStatus ='Running'";
            var model = db.Query<DbServerConfig, DbNameConfig, DbConfigModel>(sql, (server, dbname) =>
            {
                var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname };
                return temp;
            }, new { applicationNames = applicationNames, Locations = locations },
                splitOn: "Id,Id").ToList();
            return model;
        }

        /// <summary>
        /// 分库扩展.查询某一用户业务数据分布的库
        /// </summary>
        /// <param name="userid"></param>
        /// <param name="dbs"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetConfigFx(int userid, List<DbConfigModel> dbs)
        {
            List<DbConfigModel> result = new List<DbConfigModel>();
            //查询所有业务库中的订单路径流
            foreach (var db in dbs)
            {
                if (CustomerConfig.IsLocalDbDebug && db.DbServer.LocalConnectionString.IsNullOrEmpty() == true && db.ConnectionString.Contains("192.168.1.168") == false)
                    continue;

                var connstr = db.DbServer.ConnectionString.Replace("$database$", db.DbNameConfig.DbName);

                BaseRepository<PathFlowNode> _db = new BaseRepository<PathFlowNode>(connstr);
                string sql = @" SELECT count(1) as nums FROM dbo.PathFlowNode WITH(NOLOCK) 
                                WHERE FxUserId = @fxuserid  ";
                var value = _db.DbConnection.Query<int>(sql, new { fxuserid = userid }).FirstOrDefault();
                if (value > 0)
                {
                    db.OrderCount = value;
                    result.Add(db);
                }
            }
            return result.OrderByDescending(x => x.OrderCount).ToList();
        }


        /// <summary>
        /// 分库扩展.查询些用户业务数据分布的库
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="dbs"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetConfigFxBySupplier(List<int> fxUserIds, List<DbConfigModel> dbs)
        {
            List<DbConfigModel> result = new List<DbConfigModel>();
            //查询所有业务库中的订单路径流
            foreach (var db in dbs)
            {
                if (CustomerConfig.IsLocalDbDebug && db.DbServer.LocalConnectionString.IsNullOrEmpty() == true && db.ConnectionString.Contains("192.168.1.168") == false)
                    continue;

                //同云
                if (CustomerConfig.CloudPlatformType == db.DbServer.Location)
                {
                    var connstr = db.DbServer.ConnectionString.Replace("$database$", db.DbNameConfig.DbName);

                    BaseRepository<PathFlowNode> _db = new BaseRepository<PathFlowNode>(connstr);
                    string sql = @" SELECT TOP 1 1 as nums FROM dbo.PathFlowNode WITH(NOLOCK) 
                                WHERE FxUserId IN @fxUserIds ";
                    var value = _db.DbConnection.Query<bool>(sql, new { fxUserIds = fxUserIds }).FirstOrDefault();
                    if (value)
                    {
                        result.Add(db);
                    }
                }
                else
                {
                    //不同云
                    var dbApi = new DbAccessUtility(new ApiDbConfigModel { DbNameConfigId = db.DbNameConfig.Id, Location = "Alibaba", PlatformType = "Alibaba" });
                    string sql = @" SELECT TOP 1 1 as nums FROM dbo.PathFlowNode WITH(NOLOCK) 
                                WHERE FxUserId IN @fxUserIds ";
                    var value = dbApi.Query<bool>(sql, new { fxUserIds = fxUserIds }).FirstOrDefault();
                    if (value)
                    {
                        result.Add(db);
                    }
                }

            }
            return result.ToList();
        }

        ///// <summary>
        ///// 查询某一用户业务数据分布的库
        ///// </summary>
        //private DbConfigModel GeFxConfigModel(int userid, DbConfigModel model)
        //{
        // //List<DbConfigModel> result = new List<DbConfigModel>();
        // ConcurrentDictionary<string, DbConfigModel> result = new ConcurrentDictionary<string, DbConfigModel>();
        // if (dbs == null || !dbs.Any())
        // {
        //     return null;
        // }


        // Parallel.ForEach(dbs, (db,d) => 
        // {
        //     DbConfigModel model = GeFxConfigModel(userid, db);
        //     if (model != null)
        //     {
        //         try
        //         {
        //             result.TryAdd(model.Identity, model);
        //         }
        //         catch (Exception ex) 
        //         {
        //             Log.WriteError($"");
        //         }
        //     }
        // });

        ////开启多线程，取检索多个库
        //Task[] tasks = new Task[dbs.Count];
        // //查询所有业务库中的订单路径流
        // for(int i = 0; i< dbs.Count; i++)
        // {
        //     DbConfigModel db = dbs[i];
        //     tasks[i] = Task.Run(() => {
        //         DbConfigModel model = GeFxConfigModel(userid, db);
        //         if (model != null)
        //         {
        //             lock (result)
        //             {
        //                 result.Add(db);
        //             }
        //         }
        //     });
        // }

        // Task.WhenAll(tasks).Wait();//等待所有线程结束返回
        // return result.OrderByDescending(x => x.OrderCount).ToList();


        //    if (CustomerConfig.IsLocalDbDebug && model.DbServer.LocalConnectionString.IsNullOrEmpty() == true && model.ConnectionString.Contains("192.168.1.168") == false)
        //    {
        //        return null;
        //    }

        //    DbConfigModel result = null;
        //    var connstr = model.DbServer.ConnectionString.Replace("$database$", model.DbNameConfig.DbName);
        //    if (CustomerConfig.IsLocalDbDebug && model.DbServer.LocalConnectionString.IsNullOrEmpty() == true && model.ConnectionString.Contains("192.168.1.168") == false)
        //    {
        //        connstr = $"server=39.100.57.149,22;uid=sa;pwd=*************************;database={model.DbNameConfig.DbName};";
        //    }

        //    BaseRepository<PathFlowNode> _db = new BaseRepository<PathFlowNode>(connstr);
        //    string sql = @" SELECT count(1) as nums FROM dbo.PathFlowNode WITH(NOLOCK)  WHERE FxUserId = @fxuserid  ";
        //    //string sql = @" SELECT top 1 1 as nums FROM dbo.PathFlowNode WITH(NOLOCK)   WHERE FxUserId = @fxuserid  ";
        //    var value = _db.DbConnection.Query<int>(sql, new { fxuserid = userid }).FirstOrDefault();
        //    if (value > 0)
        //    {
        //        model.OrderCount = value;
        //        result = model;
        //    }

        //    return result;
        //}


        /// <summary>
        /// 是否开启数据库配置缓存
        /// </summary>
        private bool IsEnableDbConfigCache
        {
            get
            {
                return new CommonSettingRepository().IsEnableDbConfigCache;
            }
        }

        /// <summary>
        /// 查找某个用户的分库配置
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="isFromCache"></param>
        /// <returns></returns>
        public DbConfigModel GetConfigByFxUserId(int fxUserId, bool isFromCache = false)
        {
            //是否开启缓存
            if (IsEnableDbConfigCache)
            {
                return GetDbConfigByFxUserIdWithCache(fxUserId);
            }
            //分销用户ID
            var fxUserIds = new List<int> { fxUserId };
            //TODO 分库完成后需要修改
            var models = CustomerConfig.UseFxDbConfigCloudPlatformTypes.Contains(CustomerConfig.CloudPlatformType)
                ? GetListByFxUserIdsForCurrentCloudPlatform(fxUserIds)
                : GetListByFxUserIds(fxUserIds, isFromCache: isFromCache);
            //取当前库
            return models?.OrderByDescending(m => m.DbConfig.FromFxDbConfig).FirstOrDefault();
        }

        /// <summary>
        /// 查找用户的分库配置，并缓存（最后取FromFxDbConfig最大的）
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public DbConfigModel GetDbConfigByFxUserIdWithCache(int fxUserId)
        {
            //分销用户ID
            var fxUserIds = new List<int> { fxUserId };
            //如已分库迁移云平台，则使用新分库，否则使用新，旧库配置多需要查询
            var models = CustomerConfig.UseFxDbConfigCloudPlatformTypes.Contains(CustomerConfig.CloudPlatformType)
                ? GetListByFxUserIdsForCurrentCloudPlatformWithCache(fxUserIds)
                : GetListByFxUserIdsWithCloudPlatformTypeWithCache(fxUserIds);
            //取当前库
            return models?.OrderByDescending(m => m.DbConfig.FromFxDbConfig).FirstOrDefault();
        }

        /// <summary>
        /// 查找某个用户的分库配置(已含新版)
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="cloudPlatformType"></param>
        /// <param name="isIncludeAlibabaDbConfig"></param>
        /// <param name="isFromCache"></param>
        /// <returns></returns>
        public DbConfigModel GetConfigFxUserId(int fxUserId, string cloudPlatformType = null,
            bool isIncludeAlibabaDbConfig = true, bool isFromCache = false)
        {
            //判断是否有传云平台，没有则是当前云平台
            if (string.IsNullOrWhiteSpace(cloudPlatformType))
            {
                cloudPlatformType = CurrentCloudPlatformType;
            }
            //是否开启缓存
            if (IsEnableDbConfigCache)
            {
                return GetDbConfigByFxUserIdAndCloudPlatformTypeWithCache(fxUserId, cloudPlatformType,
                    isIncludeAlibabaDbConfig);
            }
            //SQL脚本
            const string sql = @"SELECT d.Id,d.FxUserId AS UserId,d.SystemShopId AS ShopId,d.DbNameConfigId,d.DbCloudPlatform,d.FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus FROM dbo.FxDbConfig d WITH(NOLOCK) WHERE d.FxUserId = @FxUserId AND d.DbCloudPlatform = @DbCloudPlatform;";
            const string sqlAlibaba = @"SELECT d.Id,us.FxUserId AS UserId,d.ShopId,d.DbNameConfigId,d.DbCloudPlatform,0 AS FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus
                        FROM dbo.P_DbConfig d WITH(NOLOCK)
                            INNER JOIN P_FxUserShop us WITH(NOLOCK) ON d.ShopId = us.ShopId
                        WHERE us.FxUserId = @FxUserId AND us.PlatformType = 'System' AND d.DbCloudPlatform = @DbCloudPlatform;";
            using (var db = DbConnection)
            {
                db.Open();
                var dbConfigs = db.Query<DbConfig>(sql, param: new { FxUserId = fxUserId, DbCloudPlatform = cloudPlatformType }).ToList() ?? new List<DbConfig>();
                //包含阿里云配置
                if (isIncludeAlibabaDbConfig)
                {
                    var temps = db.Query<DbConfig>(sqlAlibaba, param: new { FxUserId = fxUserId, DbCloudPlatform = cloudPlatformType }).ToList() ?? new List<DbConfig>();
                    dbConfigs.AddRange(temps);
                }

                var models = TryGetDbConfigFromCache(db, dbConfigs, isFromCache: isFromCache);
                return models.OrderByDescending(m => m.DbConfig.FromFxDbConfig).FirstOrDefault();
            }
        }

        /// <summary>
        /// 获取单个用户和单个平台业务库配置，并缓存
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="cloudPlatformType"></param>
        /// <param name="isIncludeAlibabaDbConfig"></param>
        /// <returns></returns>
        public DbConfigModel GetDbConfigByFxUserIdAndCloudPlatformTypeWithCache(int fxUserId, string cloudPlatformType,
            bool isIncludeAlibabaDbConfig = true)
        {
            //如果未传云平台类型，则为当前云平台类型
            if (string.IsNullOrWhiteSpace(cloudPlatformType))
            {
                cloudPlatformType = CurrentCloudPlatformType;
            }
            //所有DbConfig
            var allDbConfigs = new List<DbConfig>();
            //构建用户列表
            var fxUserIds = new List<int> { fxUserId };
            //取新分库配置
            var fxDbConfigs = GetFxDbConfigsByFxUserIdsAndCloudPlatformTypeWithCache(fxUserIds, cloudPlatformType);
            if (fxDbConfigs != null && fxDbConfigs.Any())
            {
                allDbConfigs.AddRange(fxDbConfigs);
            }
            //是否包含旧库配置
            if (isIncludeAlibabaDbConfig)
            {
                var dbConfigs = GetDbConfigsByFxUserIdsAndCloudPlatformTypeWithCache(fxUserIds, cloudPlatformType);
                if (dbConfigs != null && dbConfigs.Any())
                {
                    allDbConfigs.AddRange(dbConfigs);
                }
            }
            //转换成DbConfigModel
            var models = TryGetDbConfigFromCache(DbConnection, allDbConfigs, isFromCache: true);
            //返回
            return models.OrderByDescending(m => m.DbConfig.FromFxDbConfig).FirstOrDefault();
        }

        ///// <summary>
        ///// 修改用户的分库配置和商家一致
        ///// </summary>
        ///// <param name="userid">用户</param>
        ///// <param name="agentid">商家</param>
        ///// <returns></returns>
        //public bool ChangeUserDb(int userid, int agentid)
        //{
        //    string sql = @" SELECT conf.* FROM P_DbConfig   AS conf WITH(NOLOCK) 
        //                    INNER JOIN dbo.P_FxUserShop AS fus WITH(NOLOCK) ON conf.ShopId = fus.ShopId
        //                    WHERE fus.FxUserId =@userid AND fus.PlatformType = 'System' AND conf.DbCloudPlatform = 'alibaba';

        //                    SELECT conf.* FROM P_DbConfig   AS conf WITH(NOLOCK) 
        //                    INNER JOIN dbo.P_FxUserShop AS fus WITH(NOLOCK) ON conf.ShopId = fus.ShopId
        //                    WHERE fus.FxUserId =@agentid AND fus.PlatformType = 'System' AND conf.DbCloudPlatform = 'alibaba';";
        //    var multi = DbConnection.QueryMultiple(sql, new { userid = userid, agentid = agentid });
        //    var userdbconfig = multi.Read<DbConfig>().FirstOrDefault();
        //    var agentdbconfig = multi.Read<DbConfig>().FirstOrDefault();
        //    if (userdbconfig.DbNameConfigId != agentdbconfig.DbNameConfigId)
        //    {
        //        userdbconfig.DbNameConfigId = agentdbconfig.DbNameConfigId;
        //        string sqlupdate = " Update P_DbConfig Set DbNameConfigId=@DbNameConfigId Where Id = @Id ";
        //        int i = DbConnection.Execute(sqlupdate, new { DbNameConfigId = agentdbconfig.DbNameConfigId, Id = userdbconfig.Id });
        //        if (i < 1)
        //        {
        //            return false;
        //        }
        //    }
        //    return true;
        //}

        /// <summary>
        /// 查找用户所有商家(分销商)的业务分库情况
        /// 依次查找每个业务库是否有商家的订单业务数据
        /// 一个商家有可能属于多个业务库
        /// </summary>
        /// <param name="userids"></param>
        /// <param name="dbs"></param>
        /// <returns></returns>
        public Dictionary<string, List<int>> GetConfigFxSupplier(int fxuserid, List<int> userids, List<DbConfigModel> dbs)
        {
            Dictionary<string, List<int>> result = new Dictionary<string, List<int>>();
            //查询当前云平台业务库中的订单路径流
            foreach (var db in dbs.Where(a => a.DbServer.Location == CustomerConfig.CloudPlatformType))
            {
                if (CustomerConfig.IsLocalDbDebug && db.DbServer.LocalConnectionString.IsNullOrEmpty() == true && db.ConnectionString.Contains("192.168.1.168") == false)
                    continue;
                var connstr = db.DbServer.ConnectionString.Replace("$database$", db.DbNameConfig.DbName);
                if (CustomerConfig.IsLocalDbDebug && db.DbServer.LocalConnectionString.IsNullOrEmpty() == true && db.ConnectionString.Contains("192.168.1.168") == false)
                    connstr = $"server=39.100.57.149,22;uid=sa;pwd=*************************;database={db.DbNameConfig.DbName};";

                BaseRepository<PathFlowNode> _db = new BaseRepository<PathFlowNode>(connstr);
                string sql = @" SELECT distinct FxUserId FROM dbo.PathFlowNode WITH(NOLOCK)
                                WHERE FxUserId in@fxuserid And DownFxUserId = @downuserid  ";
                var suppliers = new List<int>();
                var groupSize = 2000;
                var count = Math.Ceiling(userids.Count * 1.0 / groupSize);
                for (int i = 0; i < count; i++)
                {
                    var tmpUserIds = userids.Skip(i * groupSize).Take(groupSize).ToList();
                    var tempSuppliers = _db.DbConnection.Query<int>(sql, new { downuserid = fxuserid, fxuserid = tmpUserIds });
                    if (tempSuppliers != null && tempSuppliers.Any())
                        suppliers.AddRange(tempSuppliers);
                }
                if (suppliers != null && suppliers.Any())
                {
                    if (!result.ContainsKey(db.DbNameConfig.DbName))
                        result.Add(db.DbNameConfig.DbName, suppliers.ToList());
                }
                else
                {
                    if (!result.ContainsKey(db.DbNameConfig.DbName))
                        result.Add(db.DbNameConfig.DbName, null);
                }
            }

            //查询其他云平台的业务库中的订单路径流
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                var otherCloudDbs = dbs.Where(a => a.DbServer.Location != CustomerConfig.CloudPlatformType).ToList();
                if (otherCloudDbs.Any())
                {
                    try
                    {
                        var currentDbConfig = new ApiDbConfigModel { PlatformType = CustomerConfig.CloudPlatformType };
                        Parallel.ForEach(otherCloudDbs, new ParallelOptions { MaxDegreeOfParallelism = 5 }, db =>
                        {
                            var dbNameConfig = db.DbNameConfig;
                            var dbAccessUtility = new DbAccessUtility(currentDbConfig, new ApiDbConfigModel(dbNameConfig.DbPlatformType, dbNameConfig.DbPlatformType, dbNameConfig.Id));
                            string sql = @" SELECT distinct FxUserId FROM dbo.PathFlowNode WITH(NOLOCK)
                                WHERE FxUserId in@fxuserid And DownFxUserId = @downuserid  ";
                            var suppliers = new List<int>();
                            var groupSize = 2000;
                            var count = Math.Ceiling(userids.Count * 1.0 / groupSize);
                            for (int i = 0; i < count; i++)
                            {
                                var tmpUserIds = userids.Skip(i * groupSize).Take(groupSize).ToList();
                                var tempSuppliers = dbAccessUtility.Query<int>(sql, new { downuserid = fxuserid, fxuserid = tmpUserIds });
                                if (tempSuppliers != null && tempSuppliers.Any())
                                    suppliers.AddRange(tempSuppliers);
                            }
                            if (suppliers != null && suppliers.Any())
                            {
                                if (!result.ContainsKey(db.DbNameConfig.DbName))
                                    result.Add(dbNameConfig.DbName, suppliers.ToList());
                            }
                            else
                            {
                                if (!result.ContainsKey(db.DbNameConfig.DbName))
                                    result.Add(dbNameConfig.DbName, null);
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"查询其他云平台的业务库订单路径流信息异常：{ex}");
                    }
                }
            }
            return result;
        }


        /// <summary>
        /// 查找分销商的分库情况-读取商家配置库
        /// 返回分销商的名称
        /// </summary>
        /// <param name="userId"></param>
        /// <returns>Item1:按库名分组；Item2:按云平台分组</returns>
        public Tuple<Dictionary<string, List<SupplierUserNameModel>>, Dictionary<string, List<SupplierUserNameModel>>> GetConfigFxSupplierNames(int userId)
        {
            var hideCancelUser = new CommonSettingRepository().IsShowCancelUser(BaseSiteContext.Current.CurrentShopId);
            List<int> status = new List<int>();
            if (hideCancelUser)
            {
                status.AddRange(new List<int> { 1, 2, 5, 6 });
            }
            else
            {
                status.AddRange(new List<int> { 1, 2, 3, 4, 5, 6 });
            }
            var resultA = new Dictionary<string, List<SupplierUserNameModel>>();
            var resultC = new Dictionary<string, List<SupplierUserNameModel>>();

            string sqlSuppliers = $@"SELECT DISTINCT n.DbName,su.Remark,uf.Name,uf.NickName,uf.Mobile,su.FxUserId,s.Location AS DbCloudPlatform,n.Id AS DbAreaId,n.NickName AS DbAreaNickName,n.ApplicationName
                                        FROM dbo.P_DbServerConfig s WITH(NOLOCK)
                                        INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
                                        INNER JOIN dbo.P_DbConfig d WITH(NOLOCK) ON n.Id = d.DbNameConfigId
                                        INNER JOIN dbo.P_FxUserShop fs WITH(NOLOCK) On d.ShopId = fs.ShopId
                                        INNER JOIN P_SupplierUser AS su WITH(NOLOCK) ON fs.FxUserId = su.FxUserId
                                        INNER JOIN dbo.P_UserFx AS uf WITH(NOLOCK) ON uf.id = su.FxUserId
                                        WHERE su.SupplierFxUserId = @fxuserid AND su.Status in@status AND fs.PlatformType = 'System' AND n.applicationName IN('fx','fx_cloud') AND n.RunningStatus ='Running' OPTION(LOOP JOIN);
                                    SELECT DISTINCT n.DbName,su.Remark,uf.Name,uf.NickName,uf.Mobile,su.FxUserId,s.Location AS DbCloudPlatform,n.Id AS DbAreaId,n.NickName AS DbAreaNickName,n.ApplicationName
                                        FROM dbo.P_DbServerConfig s WITH(NOLOCK)
                                        INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
                                        INNER JOIN dbo.FxDbConfig d WITH(NOLOCK) ON n.Id = d.DbNameConfigId
                                        INNER JOIN dbo.P_FxUserShop fs WITH(NOLOCK) On d.SystemShopId = fs.ShopId
                                        INNER JOIN P_SupplierUser AS su WITH(NOLOCK) ON fs.FxUserId = su.FxUserId
                                        INNER JOIN dbo.P_UserFx AS uf WITH(NOLOCK) ON uf.id = su.FxUserId
                                        WHERE su.SupplierFxUserId = @fxuserid AND su.Status in@status AND fs.PlatformType = 'System' AND n.applicationName IN('fx_new') AND n.RunningStatus ='Running' OPTION(LOOP JOIN)";
            //var models = DbConnection.Query<SupplierUserNameModel>(sqlSuppliers, new { fxuserid = userId, status = status }).ToList();

            var grid = DbConnection.QueryMultiple(sqlSuppliers, param: new { fxuserid = userId, status = status });
            var models = grid.Read<SupplierUserNameModel>().ToList();
            models.AddRange(grid.Read<SupplierUserNameModel>().ToList());

            if (CustomerConfig.IsDebug)
            {
                Log.WriteLine($"GetConfigFxSupplierNames优化前数据查询结果：{models.ToJson()}", "GetConfigFxSupplierNames.txt");
            }

            if (!models.Any()) return Tuple.Create(new Dictionary<string, List<SupplierUserNameModel>>(), new Dictionary<string, List<SupplierUserNameModel>>());
            //只取有路径流的区
            //旧表取所有业务库
            var dbs = GetFxAllCloudPlatformBusinessDbConfigs();
            //新表只取自己的业务库
            var selfNewDbs = new FxDbConfigRepository().GetFxAllCloudPlatformBusinessNewDbConfigs(userId);
            dbs.AddRange(selfNewDbs);
            var dbAgentIds = GetConfigFxSupplier(userId, models.Select(x => x.FxUserId).Distinct().ToList(), dbs);

            if (dbAgentIds != null && dbAgentIds.Any())
            {
                foreach (var db in dbAgentIds)
                {
                    if (db.Value == null) continue;

                    foreach (var agent in db.Value)
                    {
                        var agentInfo = models.FirstOrDefault(x => x.DbName == db.Key && x.FxUserId == agent);
                        if (agentInfo == null)
                        {
                            //数据库信息
                            var dbInfo = dbs.FirstOrDefault(x => x.DbNameConfig.DbName == db.Key);
                            if (models.FirstOrDefault(x => x.FxUserId == agent) != null)
                                agentInfo = models.FirstOrDefault(x => x.FxUserId == agent).ToJson().ToObject<SupplierUserNameModel>();
                            if (agentInfo != null)
                            {
                                if (dbInfo != null)
                                {
                                    agentInfo.DbCloudPlatform = dbInfo.DbServer.Location;
                                    agentInfo.DbAreaId = dbInfo.DbNameConfig.Id;
                                    agentInfo.DbAreaNickName = dbInfo.DbNameConfig.NickName;
                                    agentInfo.ApplicationName = dbInfo.DbNameConfig.ApplicationName;
                                }
                                else
                                {
                                    agentInfo.DbCloudPlatform = db.Key;
                                }
                                agentInfo.DbName = db.Key;
                            }
                        }
                        if (agentInfo != null)
                        {
                            //分库名
                            if (resultA.ContainsKey(db.Key))
                            {
                                if (!resultA[db.Key].Any(a => a.FxUserId == agentInfo.FxUserId))
                                    resultA[db.Key].Add(agentInfo);
                            }
                            else
                            {
                                resultA.Add(db.Key, new List<SupplierUserNameModel> { agentInfo });
                            }

                            //云平台
                            if (resultC.ContainsKey(agentInfo.DbCloudPlatform.ToLower()))
                            {
                                if (!resultC[agentInfo.DbCloudPlatform.ToLower()].Any(a => a.FxUserId == agentInfo.FxUserId))
                                    resultC[agentInfo.DbCloudPlatform.ToLower()].Add(agentInfo);
                            }
                            else
                            {
                                resultC.Add(agentInfo.DbCloudPlatform.ToLower(), new List<SupplierUserNameModel> { agentInfo });
                            }
                        }
                    }

                }
            }

            #region 注释掉，不用考虑商家所在库，以上方的各分区是否有关联路径流为准
            //models.ForEach(x =>
            //{
            //    if (x.DbCloudPlatform.ToLower() == "alibaba")
            //    {
            //        if (resultA.ContainsKey(x.DbName))
            //        {
            //            if (!resultA[x.DbName].Any(a => a.FxUserId == x.FxUserId))
            //                resultA[x.DbName].Add(x);
            //        }
            //        else
            //        {
            //            resultA.Add(x.DbName, new List<SupplierUserNameModel> { x });
            //        }
            //        if (resultC.ContainsKey(x.DbCloudPlatform.ToLower()))
            //        {
            //            if (!resultC[x.DbCloudPlatform.ToLower()].Any(a => a.FxUserId == x.FxUserId))
            //                resultC[x.DbCloudPlatform.ToLower()].Add(x);
            //        }
            //        else
            //        {
            //            resultC.Add(x.DbCloudPlatform.ToLower(), new List<SupplierUserNameModel> { x });
            //        }
            //    }
            //});
            #endregion

            return Tuple.Create(resultA, resultC);
        }


        /// <summary>
        /// 查找分销商的分库情况-读取商家配置库
        /// 返回分销商的名称
        /// </summary>
        /// <param name="userId"></param>
        /// <returns>Item1:按库名分组；Item2:按云平台分组</returns>
        public Tuple<Dictionary<string, List<SupplierUserNameModel>>, Dictionary<string, List<SupplierUserNameModel>>> GetConfigFxSupplierNamesNew(int userId)
        {
            var status = new List<int>();
            var hideCancelUser = new CommonSettingRepository().IsShowCancelUser(BaseSiteContext.Current.CurrentShopId);
            if (hideCancelUser)
                status.AddRange(new List<int> { 1, 2, 5, 6 });
            else
                status.AddRange(new List<int> { 1, 2, 3, 4, 5, 6 });

            var resultA = new Dictionary<string, List<SupplierUserNameModel>>();
            var resultC = new Dictionary<string, List<SupplierUserNameModel>>();

            // 1. 查询P_SupplierUser
            const string sqlSupplier = @"SELECT 
                            su.Remark,
                            su.FxUserId,
                            su.SupplierFxUserId
                        FROM P_SupplierUser AS su WITH (NOLOCK)
                        WHERE 
                            su.SupplierFxUserId = @fxuserid 
                            AND su.Status IN @status";

            // 2. 查询P_UserFx
            const string sqlUser = @"SELECT 
                        uf.Id,
                        uf.Name,
                        uf.NickName,
                        uf.Mobile
                    FROM dbo.P_UserFx AS uf WITH (NOLOCK)
                    INNER JOIN FunStringToIntTable(@fxUserIds, ',') AS fx ON uf.Id = fx.item";

            // 3. 查询P_FxUserShop
            const string sqlShop = @"SELECT 
                        fs.FxUserId,
                        fs.ShopId
                    FROM dbo.P_FxUserShop fs WITH (NOLOCK)
                    INNER JOIN FunStringToIntTable(@fxUserIds, ',') AS fx ON fs.FxUserId = fx.item
                    WHERE fs.PlatformType = 'System'";

            var supplierUsers = DbConnection.Query<SupplierUserNameModel>(sqlSupplier,
                new { fxuserid = userId, status }).ToList();

            if (supplierUsers.Any() == false) return Tuple.Create(resultA, resultC);
            // 获取数据
            var fxUserIds = supplierUsers.Select(p => p.FxUserId).Distinct().ToList();
            var userInfos = DbConnection.Query<UserFx>(sqlUser, new { fxUserIds = string.Join(",", fxUserIds) })
                .ToDictionary(u => u.Id, u => u);
            var userShops = DbConnection.Query<FxUserShop>(sqlShop, new { fxUserIds = string.Join(",", fxUserIds) })
                .GroupBy(s => s.FxUserId)
                .ToDictionary(g => g.Key, g => g.Select(s => s.ShopId).ToList());

            var supplierUserModels = new List<SupplierUserNameModel>();

            // 合并数据
            foreach (var su in supplierUsers)
            {
                UserFx userInfo;
                if (userInfos.TryGetValue(su.FxUserId, out userInfo))
                {
                    var model = new SupplierUserNameModel
                    {
                        FxUserId = su.FxUserId,
                        Remark = su.Remark,
                        Name = userInfo.Name,
                        NickName = userInfo.NickName,
                        Mobile = userInfo.Mobile
                    };

                    List<int> shopIds;
                    if (userShops.TryGetValue(su.FxUserId, out shopIds))
                    {
                        foreach (var shopId in shopIds)
                        {
                            var shopModel = CommUtls.DeepClone(model);
                            shopModel.ShopId = shopId;
                            supplierUserModels.Add(shopModel);
                        }
                    }
                    else
                    {
                        // 如果没有关联的Shop也添加用户
                        supplierUserModels.Add(model);
                    }
                }
            }

            var supplierUserShopIds = supplierUserModels.Select(p => p.ShopId).Where(id => id > 0).Distinct().ToList();
            var supplierUserFxIds = supplierUserModels.Select(p => p.FxUserId).Where(id => id > 0).Distinct().ToList();
            var models = new List<SupplierUserNameModel>();

            if (supplierUserShopIds.Any() && supplierUserFxIds.Any())
            {
                //全库
                var allDbConfigs = new List<DbConfigModel>();

                //分页处理
                var pageSize = 2000;
                var pageCount = Math.Ceiling(supplierUserShopIds.Count * 1.0 / pageSize);
                var dbConfigs = new List<DbConfigModel>();
                var fxDbConfigs = new List<DbConfigModel>();
                for (int i = 0; i < pageCount; i++)
                {
                    var supplierUserShopIdsTemp = supplierUserShopIds.Skip(i * pageSize).Take(pageSize).ToList();
                    var supplierUserFxIdsTemp = supplierUserFxIds.Skip(i * pageSize).Take(pageSize).ToList();
                    //旧库
                    var dbConfigsTemp = this.GetDbConfigModelsUseCache(supplierUserShopIdsTemp).Where(p => (
                    p.DbNameConfig.ApplicationName == "fx" ||
                    p.DbNameConfig.ApplicationName == "fx_cloud") &&
                    p.RunningStatus == "Running");
                    if (dbConfigsTemp != null && dbConfigsTemp.Any())
                    {
                        dbConfigs.AddRange(dbConfigsTemp);
                    }

                    //新库
                    // 20250227，查询FxDbConfig用FxUserId查询
                    var fxDbConfigsTemp = GetFxDbConfigModelFxFromDbs(supplierUserFxIdsTemp).Where(p => (
                        p.DbNameConfig.ApplicationName == "fx_new") &&
                        p.RunningStatus == "Running");
                    if (fxDbConfigsTemp != null && fxDbConfigsTemp.Any())
                    {
                        fxDbConfigs.AddRange(fxDbConfigsTemp);
                    }
                }

                if (dbConfigs != null && dbConfigs.Any())
                {
                    allDbConfigs.AddRange(dbConfigs);
                }
                if (fxDbConfigs != null && fxDbConfigs.Any())
                {
                    allDbConfigs.AddRange(fxDbConfigs);
                }

                foreach (var db in allDbConfigs)
                {
                    var user = supplierUserModels.FirstOrDefault(p => p.ShopId == db.DbConfig.ShopId);
                    var data = new SupplierUserNameModel();
                    data.DbName = db.DbNameConfig.DbName;
                    data.Remark = user?.Remark;
                    data.Name = user?.Name;
                    data.NickName = user?.NickName;
                    data.Mobile = user?.Mobile;
                    data.FxUserId = user == null ? 0 : user.FxUserId;
                    data.DbCloudPlatform = db?.DbServer.Location;
                    data.DbAreaId = db == null ? 0 : db.DbNameConfig.Id;
                    data.DbAreaNickName = db?.DbNameConfig.NickName;
                    data.ApplicationName = db?.DbNameConfig.ApplicationName;
                    models.Add(data);
                }
            }
            models = models.GroupBy(p => new { p.DbAreaId, p.FxUserId }).Select(p => p.First()).ToList();

            if (CustomerConfig.IsDebug)
            {
                Log.WriteLine($"GetConfigFxSupplierNames优化后数据查询结果：{models.ToJson()}", "GetConfigFxSupplierNames.txt");
            }

            #region 后续逻辑
            if (!models.Any())
            {
                return Tuple.Create(new Dictionary<string, List<SupplierUserNameModel>>(), new Dictionary<string, List<SupplierUserNameModel>>());
            }
            //只取有路径流的区
            //旧表取所有业务库
            var dbs = GetFxAllCloudPlatformBusinessDbConfigs();
            //新表只取自己的业务库
            var selfNewDbs = new FxDbConfigRepository().GetFxAllCloudPlatformBusinessNewDbConfigs(userId);
            dbs.AddRange(selfNewDbs);
            var dbAgentIds = GetConfigFxSupplier(userId, models.Select(x => x.FxUserId).Distinct().ToList(), dbs);

            if (dbAgentIds != null && dbAgentIds.Any())
            {
                foreach (var db in dbAgentIds)
                {
                    if (db.Value == null) continue;

                    foreach (var agent in db.Value)
                    {
                        var agentInfo = models.FirstOrDefault(x => x.DbName == db.Key && x.FxUserId == agent);
                        if (agentInfo == null)
                        {
                            //数据库信息
                            var dbInfo = dbs.FirstOrDefault(x => x.DbNameConfig.DbName == db.Key);
                            if (models.FirstOrDefault(x => x.FxUserId == agent) != null)
                                agentInfo = models.FirstOrDefault(x => x.FxUserId == agent).ToJson().ToObject<SupplierUserNameModel>();
                            if (agentInfo != null)
                            {
                                if (dbInfo != null)
                                {
                                    agentInfo.DbCloudPlatform = dbInfo.DbServer.Location;
                                    agentInfo.DbAreaId = dbInfo.DbNameConfig.Id;
                                    agentInfo.DbAreaNickName = dbInfo.DbNameConfig.NickName;
                                    agentInfo.ApplicationName = dbInfo.DbNameConfig.ApplicationName;
                                }
                                else
                                {
                                    agentInfo.DbCloudPlatform = db.Key;
                                }
                                agentInfo.DbName = db.Key;
                            }
                        }
                        if (agentInfo != null)
                        {
                            //分库名
                            if (resultA.ContainsKey(db.Key))
                            {
                                if (!resultA[db.Key].Any(a => a.FxUserId == agentInfo.FxUserId))
                                    resultA[db.Key].Add(agentInfo);
                            }
                            else
                            {
                                resultA.Add(db.Key, new List<SupplierUserNameModel> { agentInfo });
                            }

                            //云平台
                            if (resultC.ContainsKey(agentInfo.DbCloudPlatform.ToLower()))
                            {
                                if (!resultC[agentInfo.DbCloudPlatform.ToLower()].Any(a => a.FxUserId == agentInfo.FxUserId))
                                    resultC[agentInfo.DbCloudPlatform.ToLower()].Add(agentInfo);
                            }
                            else
                            {
                                resultC.Add(agentInfo.DbCloudPlatform.ToLower(), new List<SupplierUserNameModel> { agentInfo });
                            }
                        }
                    }

                }
            }

            return Tuple.Create(resultA, resultC);
            #endregion
        }


        /// <summary>
        /// 是否开启全局缓存
        /// </summary>
        public bool IsEnabledGlobalDataCacheKey
        {
            get
            {
                return new CommonSettingRepository().IsEnabledGlobalDataCache();
            }
        }


        /// <summary>
        /// 查找分销商的分库情况-读取商家配置库
        /// 返回分销商的UserId
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="userIds"></param>
        /// <returns></returns>
        public Dictionary<string, List<int>> GetConfigFxSupplierIds(int fxUserId, List<int> userIds)
        {
            Dictionary<string, List<int>> result = new Dictionary<string, List<int>>();
            var hideCancelUser = new CommonSettingRepository().IsShowCancelUser(BaseSiteContext.Current.CurrentShopId);
            List<int> status = new List<int>();
            if (hideCancelUser)
            {
                status.AddRange(new List<int> { 1, 2, 5, 6 });
            }
            else
            {
                status.AddRange(new List<int> { 1, 2, 3, 4, 5, 6 });
            }

            var groupSize = 2000;
            var count = Math.Ceiling(userIds.Count * 1.0 / groupSize);
            for (int i = 0; i < count; i++)
            {
                var tmpUserIds = userIds.Skip(i * groupSize).Take(groupSize).ToList();
                List<SupplierUserNameModel> models = null;
                if (!IsEnabledGlobalDataCacheKey)
                {
                    string sqlsuppliers = $@" SELECT n.DbName,su.Remark,uf.Name,uf.NickName,uf.Mobile,su.FxUserId
                                        FROM dbo.P_DbServerConfig s With(NoLock)
                                        INNER JOIN dbo.P_DbNameConfig n With(NoLock) ON s.Id = n.DbServerConfigId
                                        INNER JOIN dbo.P_DbConfig d With(NoLock) ON n.Id = d.DbNameConfigId
                                        INNER JOIN dbo.P_FxUserShop fs With(NoLock) On d.ShopId = fs.ShopId
                                        INNER JOIN P_SupplierUser AS su WITH(NOLOCK) ON fs.FxUserId = su.FxUserId AND su.SupplierFxUserId = @fxuserid
                                        INNER JOIN dbo.P_UserFx AS uf WITH(NOLOCK) ON uf.id = su.FxUserId
                                        WHERE uf.Id in @userids AND su.Status in@status  And fs.PlatformType = 'System' AND d.DbCloudPlatform ='{CustomerConfig.CloudPlatformType}' ";
                    models = DbConnection.Query<SupplierUserNameModel>(sqlsuppliers, new { fxuserid = fxUserId, userids = tmpUserIds, status = status }).ToList();
                }
                else
                {
                    // 根据厂家的Id，查询商家的Id
                    string sqlsuppliers = $@" SELECT su.FxUserId
                                        FROM P_SupplierUser AS su WITH(NOLOCK) 
                                        INNER JOIN dbo.P_UserFx AS uf WITH(NOLOCK) ON uf.id = su.FxUserId
                                        WHERE uf.Id in @userids AND su.Status in@status  AND  su.SupplierFxUserId = @fxuserid ";

                    var superliers = DbConnection.Query<SupplierUserNameModel>(sqlsuppliers, new { fxuserid = fxUserId, userids = tmpUserIds, status = status }).ToList();

                    var configs = GetDbConfigWithCache(tmpUserIds);
                    models = superliers.Join(configs, s => s.FxUserId, c => c.FxUserId, (s, c) =>
                    {
                        s.DbName = c.DbName;
                        return s;
                    }).ToList();
                }
                models.ForEach(x =>
                {
                    if (result.ContainsKey(x.DbName))
                    {
                        if (result[x.DbName].Contains(x.FxUserId) == false)
                            result[x.DbName].Add(x.FxUserId);
                    }
                    else
                    {
                        result.Add(x.DbName, new List<int> { x.FxUserId });
                    }
                });
            }

            return result;
        }


        private List<SupplierUserNameModel> GetDbConfigWithCache(List<int> userIds)
        {
            var key = $"/DianGuanJia/FenDan/FxDbConfig/Supplier/{CustomerConfig.CloudPlatformType}/";
            List<SupplierUserNameModel> result = new List<SupplierUserNameModel>();
            // 缓存找不到的商家的Id
            var dbGetUserIds = new List<int>();
            foreach (var userId in userIds)
            {
                var cacheItem = RedisHelper.Get<SupplierUserNameModel>(key + userId);
                if (cacheItem != null)
                {
                    result.Add(cacheItem);
                }
                else
                {
                    dbGetUserIds.Add(userId);
                }
            }

            if (dbGetUserIds.Any())
            {
                // 旧的表的配置信息
                var pdbconfigsql = $@"select n.DbName,0 as FromFxDbConfig,fs.FxUserId
                       FROM P_DbConfig d with(NoLock)
                       INNER JOIN dbo.P_FxUserShop fs With(NoLock)  On d.ShopId = fs.ShopId 
                       INNER JOIN dbo.P_DbNameConfig n With(NoLock) On n.id= d.DbNameConfigId
                       where fs.FxUserid in@userIds AND  fs.PlatformType = 'System' AND d.DbCloudPlatform ='{CustomerConfig.CloudPlatformType}'";
                var models = DbConnection.Query<SupplierUserNameModel>(pdbconfigsql, new { userIds = dbGetUserIds });
                // 新的表的配置信息
                var pfxdbconfigsql = $@"select n.DbName,d.FromFxDbConfig,d.FxUserId from 
                       FxDbConfig d with(NoLock)
                       INNER JOIN dbo.P_DbNameConfig n With(NoLock) On n.id= d.DbNameConfigId
                       where d.FxUserid in@userIds AND d.DbCloudPlatform ='{CustomerConfig.CloudPlatformType}' ";
                var fxModels = DbConnection.Query<SupplierUserNameModel>(pfxdbconfigsql, new { userIds = dbGetUserIds });

                var allModels = models.Concat(fxModels).GroupBy(m => m.FxUserId).Select(g => g.OrderByDescending(c => c.FromFxDbConfig).FirstOrDefault());

                foreach (var config in allModels)
                {
                    RedisHelper.Set(key + config.FxUserId.ToString(), config, 7200);
                    result.Add(config);
                }
            }
            return result;
        }

        ///// <summary>
        ///// 查找分销商的云平台情况-读取商家配置库
        ///// 返回云平台-分销商的名称
        ///// </summary>
        ///// <param name="userId"></param>
        ///// <returns></returns>
        //public Dictionary<string, List<string>> GetDbCloudAndSupplierNames(int userId)
        //{
        //    var hideCancelUser = new CommonSettingRepository().IsShowCancelUser(BaseSiteContext.Current.CurrentShopId);
        //    List<int> status = new List<int>();
        //    if (hideCancelUser)
        //    {
        //        status.AddRange(new List<int> { 1, 2, 5, 6 });
        //    }
        //    else
        //    {
        //        status.AddRange(new List<int> { 1, 2, 3, 4, 5, 6 });
        //    }
        //    Dictionary<string, List<string>> result = new Dictionary<string, List<string>>();
        //    var sqlSuppliers = $@" SELECT DISTINCT n.Id,n.DbName,su.Remark,uf.Name,uf.NickName,uf.Mobile,su.FxUserId,d.DbCloudPlatform
        //                                FROM dbo.P_DbServerConfig s With(NoLock)
        //                                INNER JOIN dbo.P_DbNameConfig n With(NoLock) ON s.Id = n.DbServerConfigId
        //                                INNER JOIN P_DbConfig d WITH(NOLOCK) ON n.Id = d.DbNameConfigId
        //                                INNER JOIN dbo.P_FxUserShop fs With(NoLock) On d.ShopId = fs.ShopId
        //                                INNER JOIN P_SupplierUser AS su WITH(NOLOCK) ON fs.FxUserId = su.FxUserId
        //                                INNER JOIN dbo.P_UserFx AS uf WITH(NOLOCK) ON uf.id = su.FxUserId
        //                                WHERE su.SupplierFxUserId = @fxuserid AND su.Status in@status And fs.PlatformType = 'System' AND n.ApplicationName IN('fx','fx_cloud') AND n.RunningStatus ='Running' ;
        //		SELECT DISTINCT n.Id,n.DbName,su.Remark,uf.Name,uf.NickName,uf.Mobile,su.FxUserId,d.DbCloudPlatform
        //                                FROM dbo.P_DbServerConfig s With(NoLock)
        //                                INNER JOIN dbo.P_DbNameConfig n With(NoLock) ON s.Id = n.DbServerConfigId
        //                                INNER JOIN FxDbConfig d WITH(NOLOCK) ON n.Id = d.DbNameConfigId
        //                                INNER JOIN dbo.P_FxUserShop fs With(NoLock) On d.SystemShopId = fs.ShopId
        //                                INNER JOIN P_SupplierUser AS su WITH(NOLOCK) ON fs.FxUserId = su.FxUserId
        //                                INNER JOIN dbo.P_UserFx AS uf WITH(NOLOCK) ON uf.id = su.FxUserId
        //                                WHERE su.SupplierFxUserId = @fxuserid AND su.Status in@status And fs.PlatformType = 'System' AND n.ApplicationName IN('fx_new') AND n.RunningStatus ='Running' ";
        //    //var models = DbConnection.Query<SupplierUserNameModel>(sqlSuppliers, new { fxuserid = userId, status }).ToList();

        //    var grid = DbConnection.QueryMultiple(sqlSuppliers, param: new { fxuserid = userId, status });
        //    var models = grid.Read<SupplierUserNameModel>().ToList();
        //    models.AddRange(grid.Read<SupplierUserNameModel>().ToList());

        //    if (!models.Any())
        //        return result;

        //    models.ForEach(x =>
        //    {
        //        if (result.ContainsKey(x.DbCloudPlatform))
        //        {
        //            result[x.DbCloudPlatform].Add((string.IsNullOrEmpty(x.NickName) ? x.Mobile : x.NickName) +
        //                                 (string.IsNullOrEmpty(x.Remark) ? "" : "(" + x.Remark + ")"));
        //        }
        //        else
        //        {
        //            result.Add(x.DbCloudPlatform,
        //                new List<string>
        //                {
        //                    (string.IsNullOrEmpty(x.NickName) ? x.Mobile : x.NickName) +
        //                    (string.IsNullOrEmpty(x.Remark) ? "" : "(" + x.Remark + ")")
        //                });
        //        }
        //    });

        //    return result;
        //}



        /// <summary>
        /// 店铺与所在业务库的关系(已含新版):仅工具使用
        /// </summary>
        /// <param name="shopIds">店铺Id</param>
        /// <param name="dbCloudPlatform"></param>
        /// <returns>返回的ShopId为店铺Id即P_FxUserShop.ShopId</returns>
        [Obsolete("非业务方法，仅工具方法可以使用")]
        public List<DbNameConfigIdShopId> GetDbConfigListByShopIds(List<int> shopIds, string dbCloudPlatform = "Alibaba")
        {
            //            var sql = $@"SELECT DISTINCT dc.DbNameConfigId,fus.ShopId FROM P_FxUserShop fus WITH(NOLOCK)
            //INNER JOIN P_UserFx uf WITH(NOLOCK) ON uf.Id=fus.FxUserId
            //INNER JOIN P_Shop s WITH(NOLOCK) ON s.ShopId=uf.Mobile AND s.PlatformType='System'
            //INNER JOIN P_DbConfig dc WITH(NOLOCK) ON dc.ShopId=s.Id AND dc.DbCloudPlatform=@dbCloudPlatform
            // WHERE fus.ShopId IN @shopIds";
            var sql = $@"SELECT DISTINCT dc.DbNameConfigId,t2.ShopId
FROM   dbo.FxDbConfig dc WITH(NOLOCK)
    INNER JOIN (SELECT t2.ShopId AS SystemShopId,t1.ShopId FROM P_FxUserShop t1 WITH(NOLOCK) 
  INNER JOIN P_FxUserShop t2 WITH(NOLOCK) ON t1.FxUserId=t2.FxUserId AND t2.PlatformType='System'
   WHERE t1.ShopId IN @shopIds) t2 ON t2.SystemShopId=dc.SystemShopId   
   WHERE dc.DbCloudPlatform=@dbCloudPlatform
   ;
   SELECT dc.DbNameConfigId,t2.ShopId FROM dbo.P_DbConfig dc WITH(NOLOCK)
    INNER JOIN
   (SELECT t2.ShopId AS SystemShopId,t1.ShopId FROM P_FxUserShop t1 WITH(NOLOCK) 
  INNER JOIN P_FxUserShop t2 WITH(NOLOCK) ON t1.FxUserId=t2.FxUserId AND t2.PlatformType='System'
   WHERE t1.ShopId IN @shopIds
   ) AS t2 ON t2.SystemShopId=dc.ShopId
   WHERE dc.DbCloudPlatform=@dbCloudPlatform";

            //return DbConnection.Query<DbNameConfigIdShopId>(sql, new { dbCloudPlatform = dbCloudPlatform, shopIds = shopIds }).Distinct().ToList();

            var grid = DbConnection.QueryMultiple(sql, param: new { dbCloudPlatform = dbCloudPlatform, shopIds = shopIds });
            var models = grid.Read<DbNameConfigIdShopId>().ToList();
            models.AddRange(grid.Read<DbNameConfigIdShopId>().ToList());
            return models.Distinct().ToList();
        }


        #endregion

        /// <summary>
        /// 获取FxDbConfig，按用户ID列表
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetListByFxUserIdsForCurrentCloudPlatform(List<int> fxUserIds)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return null;
            }
            var sql = string.Empty;
            var sqlParameters = new DynamicParameters();
            var cloudPlatformType = CustomerConfig.CloudPlatformType;
            sqlParameters.Add("@FxUserIds", fxUserIds);
            sqlParameters.Add("@DbCloudPlatform", cloudPlatformType);
            if (cloudPlatformType == CloudPlatformType.TouTiao.ToString())
            {
                sql = $@"SELECT d.Id,d.FxUserId AS UserId,d.SystemShopId AS ShopId,d.DbNameConfigId,d.DbCloudPlatform,d.FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus
                    FROM dbo.FxDbConfig d WITH(NOLOCK) WHERE d.FxUserId IN @FxUserIds AND d.DbCloudPlatform = @DbCloudPlatform;";
            }
            else
            {
                sql = $@"SELECT d.Id,us.FxUserId AS UserId,d.ShopId,d.DbNameConfigId,d.DbCloudPlatform,0 AS FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus FROM dbo.P_DbConfig d WITH (NOLOCK) INNER JOIN P_FxUserShop us WITH (NOLOCK) ON d.ShopId =us.ShopId WHERE us.FxUserId IN @FxUserIds AND us.PlatformType ='System' AND d.DbCloudPlatform = @DbCloudPlatform;";
            }
            using (var db = DbConnection)
            {
                var dbConfigs = new List<DbConfig>();
                if (!IsEnabledGlobalDataCacheKey || cloudPlatformType == CloudPlatformType.TouTiao.ToString())
                {
                    db.Open();
                    dbConfigs = db.Query<DbConfig>(sql, param: sqlParameters).ToList() ?? new List<DbConfig>();
                }
                else
                {
                    dbConfigs = GetDbConfigsByFxUserIdWithCache(fxUserIds);
                    dbConfigs = dbConfigs.Where(p => p.DbCloudPlatform == cloudPlatformType).ToList();
                }
                var models = TryGetDbConfigFromCache(db, dbConfigs);
                return models;
            }
        }

        /// <summary>
        /// 当前云平台类型
        /// </summary>
        private string CurrentCloudPlatformType
        {
            get
            {
                //跨境站点，取配置的CrossBorderCloudLocation=ChinaAliyun
                return CustomerConfig.IsCrossBorderSite || BaseSiteContext.GetIsCrossBorder()
                    ? CustomerConfig.CrossBorderCloudLocation
                    : CustomerConfig.CloudPlatformType;
            }
        }

        /// <summary>
        /// 查询用户业务库配置（当前云），并缓存
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetListByFxUserIdsForCurrentCloudPlatformWithCache(List<int> fxUserIds)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return null;
            }
            //如已分库迁移云平台，则使用新分库，否则使用旧库配置
            var dbConfigs = CustomerConfig.UseFxDbConfigCloudPlatformTypes.Contains(CustomerConfig.CloudPlatformType)
                ? GetFxDbConfigsByFxUserIdsAndCloudPlatformTypeWithCache(fxUserIds)
                : GetDbConfigsByFxUserIdsAndCloudPlatformTypeWithCache(fxUserIds);
            //获取DbConfigModel
            return TryGetDbConfigFromCache(DbConnection, dbConfigs, isFromCache: true);
        }



        /// <summary>
        /// 查询旧业务库配置 按用户ID列表 云平台类型 并缓存
        /// </summary>
        /// <param name="fxUserIds">用户ID</param>
        /// <param name="cloudPlatformType">不传值，则获取当前云平台</param>
        /// <returns></returns>
        public List<DbConfig> GetDbConfigsByFxUserIdsAndCloudPlatformTypeWithCache(List<int> fxUserIds,
            string cloudPlatformType = null)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return new List<DbConfig>();
            }
            //云平台参数为空则取当前云平台
            if (string.IsNullOrWhiteSpace(cloudPlatformType))
            {
                cloudPlatformType = CurrentCloudPlatformType;
            }
            //系统用户店铺关系列表
            var systemFxUserShops = new FxUserShopRepository().GetSystemFxUserShopsByFxUserIdsWithCache(fxUserIds);
            //系统用户店铺关系是否有值
            if (systemFxUserShops == null || !systemFxUserShops.Any())
            {
                return new List<DbConfig>();
            }
            //系统店铺ID列表
            var systemShopIds = systemFxUserShops.Select(m => m.ShopId).Distinct().ToList();
            var dbConfigs = GetDbConfigsBySystemShopIdsAndCloudPlatformTypeWithCache(systemShopIds, cloudPlatformType);
            if (dbConfigs == null || !dbConfigs.Any())
            {
                return new List<DbConfig>();
            }
            //关联附加用户ID
            return dbConfigs.Join(systemFxUserShops, dc => dc.ShopId, sfu => sfu.ShopId, (dc, sfu) =>
            {
                dc.UserId = sfu.FxUserId;
                return dc;
            }).ToList();
        }

        /// <summary>
        /// 查询旧业务库配置 按系统店铺ID列表 云平台类型 并缓存（模型上没有用户ID信息）
        /// </summary>
        /// <param name="systemShopIds">系统店铺</param>
        /// <param name="cloudPlatformType">不传值，则获取当前云平台</param>
        /// <returns></returns>
        public List<DbConfig> GetDbConfigsBySystemShopIdsAndCloudPlatformTypeWithCache(List<int> systemShopIds,
            string cloudPlatformType = null)
        {
            //判空处理
            if (systemShopIds == null || !systemShopIds.Any())
            {
                return new List<DbConfig>();
            }

            //云平台参数为空则取当前云平台
            if (string.IsNullOrWhiteSpace(cloudPlatformType))
            {
                //跨境站点，取配置的CrossBorderCloudLocation=ChinaAliyun
                cloudPlatformType = CurrentCloudPlatformType;
            }

            //SQL脚本
            var sql =
                $@"SELECT d.Id,d.ShopId,d.DbNameConfigId,d.DbCloudPlatform,0 AS FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus
                       FROM dbo.P_DbConfig d WITH(NOLOCK) WHERE d.ShopId IN @Ids AND d.DbCloudPlatform = '{cloudPlatformType}'";
            //是否表值函数参数
            var isTableValueFunctionParam = systemShopIds.Count > 3;
            if (isTableValueFunctionParam)
            {
                sql =
                    $@"SELECT d.Id,d.ShopId,d.DbNameConfigId,d.DbCloudPlatform,0 AS FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus
                       FROM dbo.P_DbConfig d WITH(NOLOCK) INNER JOIN dbo.FunStringToIntTable(@Ids,',') t ON t.item = d.ShopId 
                       WHERE d.DbCloudPlatform = '{cloudPlatformType}'";
            }

            //先取缓存，缓存中没有则再查询数据库
            return DbConnection.QueryWithSingleCache<DbConfig, int>(sql, systemShopIds,
                appendCacheKey: cloudPlatformType, keyFieldName: "ShopId",
                isTableValueFunctionParam: isTableValueFunctionParam, cacheExpireSeconds: 900);
        }

        /// <summary>
        /// 按用户ID列表获取配置信息(已含新版)，（内部有缓存处理）
        /// </summary>
        /// <param name="fxUserIds">分销用户ID列表</param>
        /// <param name="cloudPlatformTypes">如果不传，则获取当前云平台数据库配置</param>
        /// <param name="isOnlyAlibabaPlatform"></param>
        /// <param name="isFromCache">从缓存中取DbName，DbServer 数据，默认</param>
        /// <returns></returns>
        public List<DbConfigModel> GetListByFxUserIds(List<int> fxUserIds, List<string> cloudPlatformTypes,
            bool isOnlyAlibabaPlatform = false, bool isFromCache = false)
        {
            // 拆分SQL处理
            return GetListByFxUserIdsNew(fxUserIds, cloudPlatformTypes, isOnlyAlibabaPlatform);
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return null;
            }
            //如果不传，则获取当前云平台数据库配置
            if (cloudPlatformTypes == null || !cloudPlatformTypes.Any())
            {
                cloudPlatformTypes = new List<string>
                {
                    CurrentCloudPlatformType
                };
            }
            //是否开启缓存
            if (IsEnableDbConfigCache)
            {
                return GetListByFxUserIdsAndCloudPlatformTypesWithCache(fxUserIds, cloudPlatformTypes,
                    isOnlyAlibabaPlatform);
            }

            //SQL脚本
            var sql =
                @"SELECT d.Id,us.FxUserId AS UserId,d.ShopId,d.DbNameConfigId,d.DbCloudPlatform,0 AS FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus
FROM dbo.P_DbConfig d WITH(NOLOCK)
INNER JOIN P_FxUserShop us WITH(NOLOCK) ON d.ShopId = us.ShopId
INNER JOIN dbo.FunStringToIntTable(@FxUserIds,',') t ON t.item=us.FxUserId
WHERE us.PlatformType = 'System' AND d.DbCloudPlatform IN @DbCloudPlatforms;";

            const string otherSql =
                                @"SELECT d.Id,d.FxUserId AS UserId,d.SystemShopId AS ShopId,d.DbNameConfigId,d.DbCloudPlatform,d.FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus FROM dbo.FxDbConfig d WITH(NOLOCK) 
INNER JOIN dbo.FunStringToIntTable(@FxUserIds,',') t ON t.item=d.FxUserId
WHERE d.DbCloudPlatform IN @DbCloudPlatforms;";

            var fxUserIdStr = string.Join(",", fxUserIds);

            using (var db = DbConnection)
            {
                db.Open();
                var dbConfigs = new List<DbConfig>();
                if (!IsEnabledGlobalDataCacheKey)
                {
                    var sqlParams = new { FxUserIds = fxUserIdStr, DbCloudPlatforms = cloudPlatformTypes };
                    dbConfigs = db.Query<DbConfig>(sql, param: sqlParams).ToList() ?? new List<DbConfig>();
                }
                else
                {
                    dbConfigs = GetDbConfigsByFxUserIdWithCache(fxUserIds);
                    dbConfigs = dbConfigs.Where(p => cloudPlatformTypes.Contains(p.DbCloudPlatform)).ToList();
                }

                if (!isOnlyAlibabaPlatform)
                {
                    var temps = db.Query<DbConfig>(otherSql, param: new { FxUserIds = fxUserIdStr, DbCloudPlatforms = cloudPlatformTypes }).ToList() ?? new List<DbConfig>();
                    dbConfigs.AddRange(temps);
                }

                //Log.Debug($"获取用户数据库配置信息：{dbConfigs.ToJson()}是否只有旧库：{isOnlyAlibabaPlatform}",
                //    $"GetListByFxUserIds_{DateTime.Now:yyyy-MM-dd}.log");

                var models = TryGetDbConfigFromCache(db, dbConfigs, isFromCache: isFromCache);
                return models;
            }

        }

        /// <summary>
        /// 获取业务库配置，按用户ID列表，平台类型列表，并缓存
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="cloudPlatformTypes"></param>
        /// <param name="isOnlyAlibabaPlatform"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetListByFxUserIdsAndCloudPlatformTypesWithCache(List<int> fxUserIds,
            List<string> cloudPlatformTypes,
            bool isOnlyAlibabaPlatform = false)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return new List<DbConfigModel>();
            }
            //如果不传，则获取当前云平台数据库配置
            if (cloudPlatformTypes == null || !cloudPlatformTypes.Any())
            {
                cloudPlatformTypes = new List<string>
                {
                    CurrentCloudPlatformType
                };
            }
            if (!cloudPlatformTypes.Any())
            {
                return new List<DbConfigModel>();
            }
            //最终返回集合
            var dbConfigModels = new List<DbConfigModel>();
            //遍历云平台类型
            cloudPlatformTypes.ForEach(cloudPlatformType =>
            {
                var models =
                    GetListByFxUserIdsWithCloudPlatformTypeWithCache(fxUserIds, cloudPlatformType,
                        isOnlyAlibabaPlatform);
                if (models == null || !models.Any())
                {
                    return;
                }
                dbConfigModels.AddRange(models);
            });
            //返回
            return dbConfigModels;
        }

        /// <summary>
        /// 查询业务库配置，可以包含新，旧配置，并缓存
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="cloudPlatformType"></param>
        /// <param name="isOnlyAlibabaPlatform"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetListByFxUserIdsWithCloudPlatformTypeWithCache(List<int> fxUserIds,
            string cloudPlatformType = null,
            bool isOnlyAlibabaPlatform = false)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return null;
            }

            //如果不传，则获取当前云平台数据库配置    
            if (string.IsNullOrWhiteSpace(cloudPlatformType))
            {
                //跨境站点，取配置的CrossBorderCloudLocation=ChinaAliyun
                cloudPlatformType = CurrentCloudPlatformType;
            }

            //所有配置
            var allDbConfigs = new List<DbConfig>();
            //旧库配置
            var dbConfigs = GetDbConfigsByFxUserIdsAndCloudPlatformTypeWithCache(fxUserIds, cloudPlatformType);
            if (dbConfigs != null && dbConfigs.Any())
            {
                allDbConfigs.AddRange(dbConfigs);
            }
            //需要包含新分库配置
            if (!isOnlyAlibabaPlatform)
            {
                dbConfigs = GetFxDbConfigsByFxUserIdsAndCloudPlatformTypeWithCache(fxUserIds, cloudPlatformType);
                if (dbConfigs != null && dbConfigs.Any())
                {
                    allDbConfigs.AddRange(dbConfigs);
                }
            }
            //获取DbConfigModel
            var models = TryGetDbConfigFromCache(DbConnection, allDbConfigs, isFromCache: true);
            return models;
        }

        /// <summary>
        /// 按用户ID列表获取配置信息(已含新版)，内部支持缓存
        /// </summary>
        /// <param name="fxUserIds">分销用户ID列表</param>
        /// <param name="cloudPlatformTypes">如果不传，则获取当前云平台数据库配置</param>
        /// <param name="isOnlyAlibabaPlatform"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetListByFxUserIdsNew(List<int> fxUserIds, List<string> cloudPlatformTypes,
            bool isOnlyAlibabaPlatform = false)
        {
            if (fxUserIds == null || !fxUserIds.Any()) return null;

            cloudPlatformTypes = cloudPlatformTypes?.Any() == true
                ? cloudPlatformTypes
                : new List<string> { CustomerConfig.CloudPlatformType };

            using (var db = DbConnection)
            {
                db.Open();

                // 先获取店铺信息
                var userShops = db.Query<FxUserShop>(
                    @"SELECT ShopId, FxUserId FROM P_FxUserShop s WITH(NOLOCK)
                        INNER JOIN dbo.FunStringToIntTable(@FxUserIds,',') AS t ON s.FxUserId = t.item
                   WHERE s.PlatformType = 'System'",
                    new { FxUserIds = string.Join(",", fxUserIds) }).ToList();

                // 再获取数据库配置信息
                var dbConfigs = new List<DbConfig>();
                if (userShops.Any())
                {
                    var shopIds = userShops.Select(x => x.ShopId).Distinct().ToList();
                    var shopConfigs = db.Query<DbConfig>(
                        @"SELECT Id, ShopId, DbNameConfigId, DbCloudPlatform, ColdDbNameConfigId, ColdDbStatus
                      FROM P_DbConfig p WITH(NOLOCK) INNER JOIN dbo.FunStringToIntTable(@ShopIds,',') AS t ON p.ShopId = t.item
                        WHERE DbCloudPlatform IN @CloudPlatforms",
                        new { ShopIds = string.Join(",", shopIds), CloudPlatforms = cloudPlatformTypes });

                    // 关联数据
                    dbConfigs.AddRange(
                        from relation in userShops
                        join config in shopConfigs on relation.ShopId equals config.ShopId
                        select new DbConfig
                        {
                            Id = config.Id,
                            UserId = relation.FxUserId,
                            ShopId = config.ShopId,
                            DbNameConfigId = config.DbNameConfigId,
                            DbCloudPlatform = config.DbCloudPlatform,
                            FromFxDbConfig = 0,
                            ColdDbNameConfigId = config.ColdDbNameConfigId,
                            ColdDbStatus = config.ColdDbStatus
                        });
                }

                if (!isOnlyAlibabaPlatform)
                {
                    var standaloneConfigs = db.Query<DbConfig>(
                        @"SELECT Id, FxUserId AS UserId, SystemShopId AS ShopId, DbNameConfigId, DbCloudPlatform, FromFxDbConfig,ColdDbNameConfigId, ColdDbStatus
                  FROM FxDbConfig f WITH(NOLOCK) INNER JOIN dbo.FunStringToIntTable(@FxUserIds,',') AS t ON f.FxUserId = t.item
                    WHERE DbCloudPlatform IN @CloudPlatforms",
                        new { FxUserIds = string.Join(",", fxUserIds), CloudPlatforms = cloudPlatformTypes });

                    dbConfigs.AddRange(standaloneConfigs);
                }

                return TryGetDbConfigFromCache(db, dbConfigs);
            }
        }

        /// <summary>
        /// 按用户ID列表获取配置信息(已含新版)
        /// </summary>
        /// <param name="fxUserIds">分销用户ID列表</param>
        /// <param name="cloudPlatformType">如果不传，则获取当前云平台数据库配置</param>
        /// <param name="isOnlyAlibabaPlatform">是否仅返回阿里云，默认：否</param>
        /// <param name="isFromCache">默认不缓存</param>
        /// <returns></returns>
        public List<DbConfigModel> GetListByFxUserIds(List<int> fxUserIds, string cloudPlatformType = "",
            bool isOnlyAlibabaPlatform = false, bool isFromCache = false)
        {
            //处理多个云平台类型
            if (string.IsNullOrEmpty(cloudPlatformType))
            {
                //跨境站点，取配置的CrossBorderCloudLocation=ChinaAliyun
                cloudPlatformType = CurrentCloudPlatformType;
            }

            //是否开启缓存
            if (IsEnableDbConfigCache)
            {
                return GetListByFxUserIdsWithCloudPlatformTypeWithCache(fxUserIds, cloudPlatformType,
                    isOnlyAlibabaPlatform);
            }

            var cloudPlatformTypes = new List<string> { cloudPlatformType };
            //返回
            return GetListByFxUserIds(fxUserIds, cloudPlatformTypes, isOnlyAlibabaPlatform, isFromCache: isFromCache);
        }



        public void GetAllDbMaxId(Action<string> action)
        {
            var errDbs = new ConcurrentDictionary<string, string>();
            var maxIdModeBags = new ConcurrentBag<MaxIdModel>();
            var ptDbNameDic = new ConcurrentDictionary<string, List<DbNameConfig>>();
            //分库分表的数据库
            DianGuanJiaApp.Data.Extension.DbPolicyExtension.ParellelForeachAllDbs(db =>
            {
                DbApiAccessUtility dbConfig = DbApiAccessUtility.GetConfigureDb();
                var dbPt = db.TargetDbConfig.PlatformType;
                if (dbPt == PlatformType.Pinduoduo.ToString())
                    dbConfig = DbApiAccessUtility.GetPddConfigureDb();
                else if (dbPt == PlatformType.TouTiao.ToString())
                    dbConfig = DbApiAccessUtility.GetTouTiaoConfigureDb();

                var dbId = db.TargetDbConfig.DbNameConfigId;
                List<DbNameConfig> dbnames = null;
                if (ptDbNameDic.TryGetValue(dbPt, out dbnames) == false)
                {
                    dbnames = dbConfig.Query<DbNameConfig>("SELECT * FROM dbo.P_DbNameConfig WITH(NOLOCK)");
                    ptDbNameDic.TryAdd(dbPt, dbnames);
                }
                var dbName = dbnames.FirstOrDefault(x => x.Id == dbId);
                var dbKey = dbName == null ? dbId.ToString() : dbName.DbName;
                //if (dbName == null || dbName.DbName.Contains("alibaba_fendan_db") == false)
                //    return;

                try
                {
                    var sql = "SELECT Name FROM SysObjects Where XType='U' ORDER BY Id";
                    var tbNames = new List<string>();
                    try
                    {
                        tbNames = db.Query<string>(sql).ToList();
                    }
                    catch (Exception ex)
                    {
                        var errMsg = $"【{db.TargetDbConfig.PlatformType}】数据库：{dbKey}，查询：{sql}，异常：{ex}";
                        if (errDbs.ContainsKey(dbKey) == false)
                            errDbs.TryAdd(dbKey, errMsg);
                        Log.WriteError(errMsg);
                        throw ex;
                    }

                    var maxIdModes = new List<MaxIdModel>();
                    var noIdentTbNames = new List<string>();
                    foreach (var name in tbNames)
                    {
                        var indetSql = $@"--查询表自增列
SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME='{name}' AND COLUMNPROPERTY(OBJECT_ID('{name}'),COLUMN_NAME,'isIdentity')=1";

                        try
                        {
                            var identName = db.Query<string>(indetSql).FirstOrDefault();
                            if (identName.IsNullOrEmpty())
                            {
                                //Log.WriteLine($"【{db.TargetDbConfig.PlatformType}】数据库：{dbKey}，表【{name}】没有自增列");
                                noIdentTbNames.Add(name);
                            }
                            else
                            {
                                var lastIdSql = $"SELECT IDENT_CURRENT('{name}')";
                                try
                                {
                                    var lastIdentVal = db.Query<long>(lastIdSql).FirstOrDefault();

                                    var model = new MaxIdModel
                                    {
                                        DbKey = dbKey,
                                        MaxId = lastIdentVal,
                                        TableName = name
                                    };
                                    maxIdModes.Add(model);
                                    maxIdModeBags.Add(model);
                                    //Log.WriteError($"【{db.TargetDbConfig.PlatformType}】数据库：{dbKey}查询：{lastIdSql}，最大Id信息：{model.ToJson()}");
                                }
                                catch (Exception ex)
                                {
                                    var errMsg = $"【{db.TargetDbConfig.PlatformType}】数据库：{dbKey}查询：{lastIdSql}，异常：{ex}";
                                    if (errDbs.ContainsKey(dbKey) == false)
                                        errDbs.TryAdd(dbKey, errMsg);
                                    Log.WriteError(errMsg);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            var errMsg = $"【{db.TargetDbConfig.PlatformType}】数据库：{dbKey}查询：{indetSql}，异常：{ex}";
                            if (errDbs.ContainsKey(dbKey) == false)
                                errDbs.TryAdd(dbKey, errMsg);
                            Log.WriteError(errMsg);
                        }
                    }

                    action($"【{db.TargetDbConfig.PlatformType}】数据【{dbKey}】表最大ID信息：{maxIdModes.OrderByDescending(x => x.MaxId).FirstOrDefault()?.ToJson() ?? "null"}");
                    Log.WriteLine($"【{db.TargetDbConfig.PlatformType}】数据【{dbKey}】表最大ID信息：{maxIdModes.OrderByDescending(x => x.MaxId).ToJson()}", "MaxIdInfo.txt");
                    //if (noIdentTbNames.Any())
                    //    Log.WriteLine($"【{db.TargetDbConfig.PlatformType}】数据【{dbKey}】没有自增列的表：{noIdentTbNames.OrderByDescending(x => x).ToJson()}", "MaxIdInfo.txt");
                }
                catch (Exception ex)
                {
                    Log.WriteError($"【{dbId}】数据库查询异常：{ex}");
                }
            });

            Log.WriteLine($"表最大ID异常信息：{errDbs.ToJson()}", "MaxIdInfoErr.txt");
            Log.WriteLine($"表最大ID信息：{maxIdModeBags.OrderByDescending(x => x.MaxId).ToJson()}", "MaxIdInfo2.txt");
            Log.WriteLine($"表最大ID信息：{maxIdModeBags.Where(x => x.MaxId > (int.MaxValue / 2)).OrderByDescending(x => x.MaxId).ToJson()}", "MaxIdInfo3.txt");
        }

        /// <summary>
        /// 1688分销功能专用，仅需要操作热数据
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="dbCloudPt"></param>
        /// <returns></returns>
        public List<FxUserIdAndDbNameConfigId> GetFxUserDbNameConfigIds(List<int> fxUserIds,
            string dbCloudPt = "Alibaba")
        {
            //旧库SQL脚本
            const string sql = @"SELECT u.Id AS FxUserId,
                               dc.DbNameConfigId, 0 AS FromFxDbConfig
                        FROM dbo.P_DbConfig dc (NOLOCK)
                            INNER JOIN dbo.P_FxUserShop us (NOLOCK)
                                ON dc.ShopId = us.ShopId
                                   AND dc.DbCloudPlatform = @DbCloudPlatform
                            INNER JOIN dbo.P_UserFx u (NOLOCK)
                                ON u.Id = us.FxUserId
                                   AND us.PlatformType = 'System'
                        WHERE u.Id IN @FxUserIds";
            //分库SQL脚本
            const string otherSql =
                "SELECT d.FxUserId,d.DbNameConfigId,d.FromFxDbConfig FROM dbo.FxDbConfig d WITH(NOLOCK) WHERE d.FxUserId IN @FxUserIds AND d.DbCloudPlatform = @DbCloudPlatform;";

            var fxUserIdAndDbNameConfigIds = new List<FxUserIdAndDbNameConfigId>();
            //查询
            using (var db = DbConnection)
            {
                if (db.State != ConnectionState.Open)
                {
                    db.Open();
                }
                //旧库配置查询
                var models =
                    db.Query<FxUserIdAndDbNameConfigId>(sql,
                        param: new { FxUserIds = fxUserIds, DbCloudPlatform = dbCloudPt }).ToList();
                if (models != null && models.Any())
                {
                    fxUserIdAndDbNameConfigIds.AddRange(models);
                }
                //分库配置查询
                models = db.Query<FxUserIdAndDbNameConfigId>(otherSql,
                    param: new { FxUserIds = fxUserIds, DbCloudPlatform = dbCloudPt }).ToList();

                if (models != null && models.Any())
                {
                    fxUserIdAndDbNameConfigIds.AddRange(models);
                }
            }
            //空处理
            if (!fxUserIdAndDbNameConfigIds.Any())
            {
                return fxUserIdAndDbNameConfigIds;
            }
            //返回FromFxDbConfig最大的用户配置
            return fxUserIdAndDbNameConfigIds.GroupBy(m => m.FxUserId)
                .Select(m => m.OrderByDescending(o => o.FromFxDbConfig).First()).ToList();
        }

        /// <summary>
        /// 分页获取用户数据库配置-非业务方法工具程序使用
        /// </summary>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetPageList(int page, int pageSize = 10000)
        {
            var sql = CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString()
                ? $@"SELECT s.*,n.*,d.Id,d.FxUserId AS UserId,d.SystemShopId AS ShopId,d.DbNameConfigId,d.DbCloudPlatform,0 AS IsHotTableEnabled,0 AS IsDisabled,d.FromFxDbConfig
                        FROM dbo.P_DbServerConfig s WITH(NOLOCK)
                            INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
                            INNER JOIN dbo.FxDbConfig d WITH(NOLOCK) ON n.Id = d.DbNameConfigId
                        WHERE d.DbCloudPlatform = 'TouTiao' AND n.RunningStatus ='Running' AND n.ApplicationName IN('fx','fx_cloud','fx_new')
                        ORDER BY d.[FxUserId] 
                        OFFSET {(page - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY"
                : $@"SELECT s.*,n.*,d.Id,us.FxUserId AS UserId,d.ShopId,d.DbNameConfigId,d.DbCloudPlatform,d.IsHotTableEnabled,IsDisabled,0 AS FromFxDbConfig
                         FROM dbo.P_DbServerConfig s WITH(NOLOCK)
                            INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
                            INNER JOIN dbo.P_DbConfig d WITH(NOLOCK) ON n.Id = d.DbNameConfigId
                            INNER JOIN dbo.P_FxUserShop us WITH(NOLOCK) ON d.ShopId = us.ShopId
                        WHERE us.PlatformType = 'System' AND d.DbCloudPlatform = '{CustomerConfig.CloudPlatformType}' AND n.RunningStatus ='Running' AND n.ApplicationName IN('fx','fx_cloud','fx_new')
                        ORDER BY d.[ShopId] 
                        OFFSET {(page - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";

            return DbConnection.Query<DbServerConfig, DbNameConfig, DbConfig, DbConfigModel>(sql,
                (server, dbname, config) =>
                {
                    var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname, DbConfig = config };
                    return temp;
                }, splitOn: "Id,Id,Id,Id").ToList();
        }

        /// <summary>
        /// 获取FxDbConfig配置
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="dbCloudPlatform"></param>
        /// <returns></returns>
        public List<FxDbConfig> GetFxUserConfigs(List<int> fxUserIds, string dbCloudPlatform)
        {
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return null;
            }
            var sql = @"SELECT * FROM dbo.FxDbConfig d WITH(NOLOCK) WHERE d.FxUserId IN @FxUserIds AND d.DbCloudPlatform = @DbCloudPlatform;";
            using (var db = DbConnection)
            {
                db.Open();
                var dbConfigs = db.Query<FxDbConfig>(sql, param: new { FxUserIds = fxUserIds, DbCloudPlatform = dbCloudPlatform }).ToList() ?? new List<FxDbConfig>();
                return dbConfigs;
            }
        }

        /// <summary>
        /// 获取用户的fxdbconfig配置
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="cloudPlatformTypes"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetFxDbConfigFxUserIds(List<int> fxUserIds, List<string> cloudPlatformTypes)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return null;
            }
            //如果不传，则获取当前云平台数据库配置
            if (cloudPlatformTypes == null || !cloudPlatformTypes.Any())
            {
                cloudPlatformTypes = new List<string>
                {
                    CustomerConfig.CloudPlatformType
                };
            }

            const string sql =
                @"SELECT d.Id,d.FxUserId AS UserId,d.SystemShopId AS ShopId,d.DbNameConfigId,d.DbCloudPlatform,d.FromFxDbConfig,d.ColdDbNameConfigId,d.ColdDbStatus FROM dbo.FxDbConfig d WITH(NOLOCK) WHERE d.FxUserId IN @FxUserIds AND d.DbCloudPlatform IN @DbCloudPlatforms;";

            using (var db = DbConnection)
            {
                var dbConfigs = new List<DbConfig>(fxUserIds.Count);
                if (fxUserIds.Count >= 1000)
                {
                    var param = new DynamicParameters();
                    param.Add("DbCloudPlatforms", cloudPlatformTypes);
                    dbConfigs.AddRange(BatchQuery(fxUserIds, "FxUserIds", sql, param));
                }
                else
                {
                    db.Open();
                    dbConfigs.AddRange(db.Query<DbConfig>(sql, param: new { FxUserIds = fxUserIds, DbCloudPlatforms = cloudPlatformTypes }).ToList() ?? new List<DbConfig>());
                }
                if (db.State != ConnectionState.Open)
                    db.Open();
                var models = TryGetDbConfigFromCache(db, dbConfigs);
                return models;
            }
        }

        /// <summary>
        /// 通过分销商ID返回对应数据库分区名
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public Dictionary<int, string> GetDbNameByFxUserId(List<int> fxUserIds)
        {
            if (fxUserIds.IsNullOrEmptyList())
                return new Dictionary<int, string>();
            var sql = @"SELECT pu.FxUserId, pdName.DbName
FROM P_DbConfig pdc WITH(NOLOCK)
JOIN P_DbNameConfig pdName WITH(NOLOCK) ON pdc.DbNameConfigId = pdName.Id
JOIN P_FxUserShop pu WITH(NOLOCK) ON pdc.ShopId = pu.ShopId
WHERE pu.FxUserId IN @FxUserIds
AND pu.PlatformType = 'System'
AND pdc.DbCloudPlatform = 'Alibaba';";

            var queryResult = new List<dynamic>(fxUserIds.Count);
            using (var db = DbConnection)
            {
                db.Open();
                const int batchSize = 500;

                for (var i = 0; i < fxUserIds.Count; i += batchSize)
                {
                    var batchIds = fxUserIds.Skip(i).Take(batchSize).ToList();
                    queryResult.AddRange(db.Query(sql, new { FxUserIds = batchIds }));
                }
            }
            //var queryResult = DbConnection.Query(sql, new { FxUserIds = fxUserIds });

            var result = queryResult.GroupBy(row => row.FxUserId).ToDictionary(
                row => (int)row.Key,
                row => (string)row.FirstOrDefault().DbName
            );

            return result;
        }
        
        /// <summary>
        ///  获取业务库配置数据，并缓存（仅DbNameConfig，DbServerConfig）
        /// </summary>
        /// <returns></returns>
        public List<DbConfigModel> GetAllDbConfigModelWithCache()
        {
            //数据库名称配置，并缓存
            var dbNameConfigs = GetDbNameConfigsWithCache();
            if (dbNameConfigs == null || !dbNameConfigs.Any())
            {
                return new List<DbConfigModel>();
            }

            //数据库服务器配置
            var dbServerConfigs = GetDbServerConfigsWithCache();
            if (dbServerConfigs == null || !dbServerConfigs.Any())
            {
                return new List<DbConfigModel>();
            }

            //关联
            return dbNameConfigs.Join(dbServerConfigs, nc => nc.DbServerConfigId, sc => sc.Id,
                (nc, sc) => new DbConfigModel { DbNameConfig = nc, DbServer = sc }).ToList();
        }

        /// <summary>
        /// 查询数据库名称配置，并缓存，按名称配置ID
        /// </summary>
        /// <param name="nameConfigIds"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetDbConfigsByNameConfigIdsWithCache(List<int> nameConfigIds)
        {
            //判空处理
            if (nameConfigIds == null || !nameConfigIds.Any())
            {
                return new List<DbConfigModel>();
            }

            //数据库名称配置，并缓存
            var dbNameConfigs = GetDbNameConfigsByIdsWithCache(nameConfigIds);
            if (dbNameConfigs == null || !dbNameConfigs.Any())
            {
                return new List<DbConfigModel>();
            }

            //数据库服务器配置
            var dbServerConfigs = GetDbServerConfigsWithCache();
            if (dbServerConfigs == null || !dbServerConfigs.Any())
            {
                return new List<DbConfigModel>();
            }

            //关联
            return dbNameConfigs.Join(dbServerConfigs, nc => nc.DbServerConfigId, sc => sc.Id,
                (nc, sc) => new DbConfigModel { DbNameConfig = nc, DbServer = sc }).ToList();
        }

        /// <summary>
        /// 查询所有的数据库配名称配置，并缓存（仅供查询全部使用）
        /// </summary>
        /// <returns></returns>
        public List<DbNameConfig> GetDbNameConfigsWithCache()
        {
            const string sql = "SELECT * FROM [P_DbNameConfig] WITH(NOLOCK)";
            return DbConnection.QueryWithCacheAndSecondCache<DbNameConfig>(sql, secondCacheExpireSeconds: 60);
        }

        /// <summary>
        /// 查询所有的数据库服务器配置，并缓存
        /// </summary>
        /// <returns></returns>
        public List<DbServerConfig> GetDbServerConfigsWithCache()
        {
            const string sql = "SELECT * FROM [P_DbServerConfig](NOLOCK)";
            return DbConnection.QueryWithCacheAndSecondCache<DbServerConfig>(sql);
        }

        /// <summary>
        /// 查询数据库名称配置，并缓存
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<DbNameConfig> GetDbNameConfigsByIdsWithCache(List<int> ids)
        {
            //判空处理
            if (ids == null || !ids.Any())
            {
                return new List<DbNameConfig>();
            }
            //SQL脚本
            const string sql = "SELECT * FROM [P_DbNameConfig] (NOLOCK) WHERE Id IN @Ids";
            //查询并缓存
            return DbConnection.QueryWithSingleCache<DbNameConfig, int>(sql, ids);
        }

        /// <summary>
        /// 获取数据库配置模型 按用户ID
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public DbConfigModel GetDbConfigModelByFxUserId(int fxUserId)
        {
            //当前云平台
            var cloudPlatformType = CustomerConfig.CloudPlatformType;
            //非阿里云和京东云
            if (cloudPlatformType != CloudPlatformType.Alibaba.ToString() &&
                cloudPlatformType != CloudPlatformType.Jingdong.ToString())
            {
                return GetFxDbConfigModel(fxUserId, cloudPlatformType);
            }

            //其他云（抖店云，拼多多云）
            var models = GetListByFxUserIds(new List<int> { fxUserId },
                isOnlyAlibabaPlatform: cloudPlatformType == CloudPlatformType.Jingdong.ToString());
            if (models == null || !models.Any())
            {
                return null;
            }

            return models.GroupBy(m => new
            {
                m.DbConfig.DbCloudPlatform,
                m.DbConfig.UserId
            })
                .Select(m => m.OrderByDescending(o => o.FromFxDbConfig).First()).FirstOrDefault();
        }

        /// <summary>
        /// 删除缓存
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="cpt"></param>
        public void DeleteCache(int shopId, string cpt = null)
        {
            cpt = cpt ?? CustomerConfig.CloudPlatformType;
            DbConnection.DeleteQueryCache<DbConfig>(shopId + ":" + cpt);
        }

        public List<DbNameConfig> GetDbNameConfigsByName(List<string> applicationNames)
        {
            //判空处理
            if (applicationNames == null || !applicationNames.Any())
            {
                return new List<DbNameConfig>();
            }
            //SQL脚本
            const string sql = "SELECT * FROM [P_DbNameConfig] (NOLOCK) WHERE ApplicationName IN @applicationNames AND RunningStatus ='Running'";
            ////查询并缓存
            //var configs = DbConnection.QueryWithMultipleCache<DbNameConfig, string>(sql,
            //    keyFieldName: "ApplicationName",
            //    keyFieldValue: applicationNames,
            //    parameterName: "applicationNames",
            //    filterNotMapped: false,
            //    cacheExpireSeconds: 1800);
            var sqlParameter = new DynamicParameters();
            sqlParameter.Add("@applicationNames", applicationNames);
            var configs = DbConnection.Query<DbNameConfig>(sql, sqlParameter).ToList();
            return configs;
        }


        /// <summary>
        /// 查询新分库业务库配置 按用户ID列表 云平台类型 并缓存
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="cloudPlatformType"></param>
        /// <returns></returns>
        public List<DbConfig> GetFxDbConfigsByFxUserIdsAndCloudPlatformTypeWithCache(List<int> fxUserIds,
            string cloudPlatformType = null)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return new List<DbConfig>();
            }

            //云平台参数为空则取当前云平台
            if (string.IsNullOrWhiteSpace(cloudPlatformType))
            {
                cloudPlatformType = CurrentCloudPlatformType;
            }

            //先取缓存，缓存中没有则再查询数据库
            var fxDbConfigs = new FxDbConfigRepository().GetFxDbConfigsByFxUserIdsAndCloudPlatformTypeWithCache(
                fxUserIds,
                cloudPlatformType);

            if (fxDbConfigs == null || !fxDbConfigs.Any())
            {
                return new List<DbConfig>();
            }

            //转换到DbConfig
            return fxDbConfigs.ToJson().ToObject<List<DbConfig>>();
        }

        /// <summary>
        /// 获取用户新分库数据库配置信息信息，按用户ID列表，云平台类型 并缓存
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="cloudPlatformType"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetFxDbConfigModelsByFxUserIdsAndCloudPlatformWithCache(List<int> fxUserIds,
            string cloudPlatformType = null)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return new List<DbConfigModel>();
            }

            //云平台类型为空
            if (string.IsNullOrWhiteSpace(cloudPlatformType))
            {
                cloudPlatformType = CurrentCloudPlatformType;
            }

            //获取新分库数据配置
            var fxDbConfigs = GetFxDbConfigsByFxUserIdsAndCloudPlatformTypeWithCache(fxUserIds, cloudPlatformType);

            //返回DbConfigModels
            return TryGetDbConfigFromCache(DbConnection, fxDbConfigs, true);
        }

        /// <summary>
        /// 获取用户旧数据库配置信息信息，按用户ID列表，云平台类型 并缓存
        /// </summary>
        /// <param name="systemShopIds"></param>
        /// <param name="cloudPlatformType"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetDbConfigModelsBySystemShopIdsAndCloudPlatformWithCache(List<int> systemShopIds,
            string cloudPlatformType = null)
        {
            //判空处理
            if (systemShopIds == null || !systemShopIds.Any())
            {
                return new List<DbConfigModel>();
            }

            //云平台类型为空
            if (string.IsNullOrWhiteSpace(cloudPlatformType))
            {
                cloudPlatformType = CurrentCloudPlatformType;
            }

            var dbConfigs = GetDbConfigsBySystemShopIdsAndCloudPlatformTypeWithCache(systemShopIds, cloudPlatformType);
            if (dbConfigs == null || !dbConfigs.Any())
            {
                return new List<DbConfigModel>();
            }

            //返回DbConfigModels
            return TryGetDbConfigFromCache(DbConnection, dbConfigs, true);
        }

        /// <summary>
        /// (已含新版)-根据云平台决定查询的是FxDbConfig还是DbConfig
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="systemShopId"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetDbConfigModelFx(int fxUserId, int systemShopId)
        {
            //判空处理
            if (fxUserId <= 0 || systemShopId <= 0)
            {
                return null;
            }
            var fxUserIds = new List<int> { fxUserId };
            //获取已经分库迁移完成
            if (CustomerConfig.UseFxDbConfigCloudPlatformTypes.Contains(CurrentCloudPlatformType))
            {
                return GetFxDbConfigModelsByFxUserIdsAndCloudPlatformWithCache(fxUserIds);
            }
            //系统店铺类别
            var systemShopIds = new List<int> { systemShopId };
            //所有库
            var allDbConfigs = new List<DbConfigModel>();
            //旧库
            var dbConfigs = GetDbConfigModelsBySystemShopIdsAndCloudPlatformWithCache(systemShopIds);
            if (dbConfigs != null && dbConfigs.Any())
            {
                allDbConfigs.AddRange(dbConfigs);
            }
            //新库
            var fxDbConfigs = GetFxDbConfigModelsByFxUserIdsAndCloudPlatformWithCache(fxUserIds);
            if (fxDbConfigs != null && fxDbConfigs.Any())
            {
                allDbConfigs.AddRange(fxDbConfigs);
            }
            //返回
            return allDbConfigs;
        }

        public List<DbConfig> GetDbConfigsByFxUserIdWithCache(List<int> fxUserIds)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return new List<DbConfig>();
            }
            //SQL脚本
            var sql = $@"SELECT
	d.Id,
	us.FxUserId AS UserId,
	d.ShopId,
	d.DbNameConfigId,
	d.IsHotTableEnabled,
	d.CreateTime,
	d.ModifyTime,
	d.IsDisabled,
	d.DbCloudPlatform,
	--d.FromFxDbConfig,
	d.ColdDbNameConfigId,
	d.ColdDbStatus,
	0 AS FromFxDbConfig 
FROM
	dbo.P_DbConfig d WITH ( NOLOCK )
	INNER JOIN P_FxUserShop us WITH ( NOLOCK ) ON d.ShopId = us.ShopId 
WHERE
	us.FxUserId IN @FxUserIds";
            //var configs = DbConnection.QueryWithMultipleCache<DbConfig, int>(sql,
            //    keyFieldName: "UserId",
            //    keyFieldValue: fxUserIds,
            //    parameterName: "FxUserIds",
            //    filterNotMapped: false,
            //    cacheExpireSeconds: 1800);
            var sqlParameters = new DynamicParameters();
            sqlParameters.Add("@FxUserIds", fxUserIds);
            var configs = DbConnection.Query<DbConfig>(sql, sqlParameters).ToList();
            return configs;
        }
        
        /// <summary>
        /// 根据服务器Id，获取该实例下的所有的数据库实例
        /// </summary>
        /// <param name="serverId"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<DbNameConfig> GetDbConfigByServerId(string serverId, string platformType)
        {
            var sql = @"SELECT n.Id,n.DbName FROM P_DbNameConfig n(NOLOCK)  WHERE n.DbServerConfigId=@serverId and DbPlatformType=@platformType";
            return DbConnection.Query<DbNameConfig>(sql, new { serverId, platformType }).ToList();
        }

        /// <summary>
        /// 根据数据库实例Id，获取用户所在的数据库信息
        /// 迁移程序使用，禁止其他业务调用该方法
        /// </summary>
        /// <param name="configNameIds"></param>
        /// <param name="platformType"></param>
        /// <param name="fetchCount"></param>
        /// <param name="version">灰度环境，如果是0，那么忽略环境</param>
        /// <returns></returns>
        public List<FxDbConfig> GetUnColdDbConfigsByNameConfig(List<int> configNameIds, string platformType, int fetchCount = 10, int version = 0)
        {
            #region 指定要迁移的环境用户，比如灰度3，那么version=3
            var versionSql = string.Empty;
            var versionWhere = string.Empty;
            if (version>0)
            {
                versionSql=@" INNER JOIN P_FxUserShop us(NOLOCK) ON us.FxUserId=dc.FxUserId 
                              INNER JOIN P_Shop s(NOLOCK) ON us.ShopId=s.Id AND s.PlatformType='System'";
                versionWhere = " AND s.[Version]=@version";
            }
            #endregion

            var sql = $@"SELECT TOP {fetchCount} dc.* FROM FXDbConfig dc(NOLOCK) {versionSql}
                                WHERE DbNameConfigId in @configNameIds 
                                AND DbCloudPlatform=@platformType 
                                AND (ISNULL(ColdDbNameConfigId,0)=0 OR ISNULL(ColdDbStatus,0)<1)
                                AND NOT EXISTS(SELECT 1 FROM FxDataMigrateTask t(NOLOCK) WHERE t.FxUserId=dc.FxUserId  AND MigrateType='LogicOrderStatusInit' AND SourceCloudPlatformType=@platformType)
                                {versionWhere}";

            var dbConfigs = DbConnection.Query<FxDbConfig>(sql, new { fetchCount, configNameIds, platformType, version }).ToList();
            return dbConfigs;
        }


        /// <summary>
        /// 获取分单所有库
        /// </summary>
        /// <param name="dbNameConfigId"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetAllDbConfigModelFx(int dbNameConfigId)
        {
            var allDbConfigs = new List<DbConfigModel>();

            //查出所有业务库
            var whereSQL = " WHERE  n.RunningStatus ='Running' ";
            if (dbNameConfigId > 0)
            {
                whereSQL += " AND n.Id > @DbNameConfigId ";
            }
            var sql = $@"
						SELECT * FROM dbo.P_DbServerConfig s WITH(NOLOCK) 
						INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
						{whereSQL}
						ORDER BY n.Id";

            var allDbs = DbConnection.Query<DbServerConfig, DbNameConfig, DbConfigModel>(sql, (server, dbname) =>
            {
                var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname };
                return temp;
            }, new { DbNameConfigId= dbNameConfigId }, splitOn: "Id,Id").ToList();

            var applicationNames = new List<string>() { "fx", "fx_new", "fx_cloud" };

            //返回
            return allDbs.Where(a => a.DbServer != null && a.DbNameConfig != null && string.IsNullOrEmpty(a.ConnectionString) == false && applicationNames.Contains(a.DbNameConfig.ApplicationName)).ToList();
		}

		/// <summary>
		/// 获取分单所有库
		/// </summary>
		/// <param name="dbNameConfigIdList"></param>
		/// <returns></returns>
		public List<DbConfigModel> GetAllDbConfigModelFx(List<int> dbNameConfigIdList)
		{
			var allDbConfigs = new List<DbConfigModel>();

			//查出所有业务库
			var whereSQL = " WHERE  n.RunningStatus ='Running'  AND n.Id in @DbNameConfigId ";

			var sql = $@"
						SELECT * FROM dbo.P_DbServerConfig s WITH(NOLOCK) 
						INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
						{whereSQL}
						ORDER BY n.Id";

			var allDbs = DbConnection.Query<DbServerConfig, DbNameConfig, DbConfigModel>(sql, (server, dbname) =>
			{
				var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname };
				return temp;
			}, new { DbNameConfigId = dbNameConfigIdList }, splitOn: "Id,Id").ToList();

			var applicationNames = new List<string>() { "fx", "fx_new", "fx_cloud" };

			//返回
			return allDbs.Where(a => a.DbServer != null && a.DbNameConfig != null && string.IsNullOrEmpty(a.ConnectionString) == false && applicationNames.Contains(a.DbNameConfig.ApplicationName)).ToList();
		}

	}

    public class MaxIdModel
    {
        public string DbKey { get; set; }
        public string TableName { get; set; }
        public long MaxId { get; set; }
    }
}
