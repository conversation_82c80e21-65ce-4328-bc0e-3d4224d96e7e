using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace DianGuanJiaApp.Data.Repository
{
    /// <summary>
    /// 分销品数据仓储
    /// </summary>
    public class DistributorProductRepository : BaseRepository<Product>
    {
        private string _connectionString;
        public DistributorProductRepository()
        {

        }

        public DistributorProductRepository(string connectionString) : base(connectionString)
        {
            _connectionString = connectionString;
        }

        private void LogSql(string sql, DynamicParameters parameters, string desc)
        {
            if (CustomerConfig.IsDebug)
            {
                var realSql = GetRealSql(sql, parameters);
                Log.WriteLine(desc + Environment.NewLine + realSql, "DistProductListBySupplier-sql.txt");
            }
        }

        /// <summary>
        /// 查询分销品列表
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public Tuple<int, List<DistributionProductViewModel>> LoadDistributionProductList(QueryDistributionProductModel queryModel, out DbApiAccessUtility dbApiAccessUtility)
        {
            var joinSqls = new List<string>();
            var parameters = new DynamicParameters();
            var conditions = new List<string>();

            //厂家的id条件
            if (queryModel.SupplierUserId.Count == 1)
            {
                conditions.Add("p.SourceUserId=@sourceUserId");
                parameters.Add("@sourceUserId", queryModel.SupplierUserId.FirstOrDefault());
            }
            else
            {
                conditions.Add("p.SourceUserId IN @sourceUserId");
                parameters.Add("@sourceUserId", queryModel.SupplierUserId);
            }

            //产品过滤条件 1（一件代发），新增类型2（一件起批），3（定制品）
            conditions.Add("p.ProductType IN @productType");
            parameters.Add("@productType", new List<int> { 1, 2, 3 });

            //1688 店铺id
            if (queryModel.AlibabaShopId != null && queryModel.AlibabaShopId.Any(f => f > 0))
            {
                // 20240911 部分数据库使用数据库关键字参数sid会报错，参数名称改为ShopId
                var sids = queryModel.AlibabaShopId.Where(f => f > 0).Distinct().ToList();
                if (sids.Count == 1)
                {
                    conditions.Add("p.ShopId=@ShopId");
                    parameters.Add("@ShopId", sids.FirstOrDefault());
                }
                else
                {
                    conditions.Add("p.ShopId IN @ShopId");
                    parameters.Add("@ShopId", sids);
                }
            }

            //商品状态
            if (!string.IsNullOrWhiteSpace(queryModel.ProductStatus) && queryModel.ProductStatus != "0")
            {
                if (queryModel.ProductStatus == "1")
                    conditions.Add("p.Status=@status");
                else
                    conditions.Add("p.Status!=@status");
                parameters.Add("@status", "published");
            }

            //商品ID
            if (!string.IsNullOrWhiteSpace(queryModel.ProductId))
            {
                conditions.Add("p.PlatformId=@productId");
                parameters.Add("@productId", queryModel.ProductId.Trim());
            }
            //商品名称
            if (!string.IsNullOrWhiteSpace(queryModel.ProductSubject))
            {
                conditions.Add("p.Subject LIKE @subject");
                parameters.Add("@subject", $"%{queryModel.ProductSubject.Trim()}%");
            }
            //商品简称
            if (!string.IsNullOrWhiteSpace(queryModel.ProductShortTitle))
            {
                //关联商品简称信息表
                joinSqls.Add(@"INNER JOIN dbo.ProductInfo AS pi WITH(NOLOCK) ON p.ProductCode = pi.ProductCode AND p.SourceUserId= pi.FxUserId");
                conditions.Add("pi.ShortTitle=@shortTitle");
                parameters.Add("@shortTitle", queryModel.ProductShortTitle.Trim());
            }

            //Sku简称
            var isJoinSkuInfoTable = false;
            if (!string.IsNullOrWhiteSpace(queryModel.ProductSkuShortTitle))
            {
                //关联Sku表及Sku简称信息表
                joinSqls.Add($@"INNER JOIN dbo.ProductSku AS ps WITH(NOLOCK) ON p.ProductCode = ps.ProductCode
INNER JOIN dbo.ProductSkuInfo AS psi WITH(NOLOCK) ON psi.SkuCode=ps.SkuCode  ");
                conditions.Add("psi.ShortTitle=@skuShortTitle");
                parameters.Add("@skuShortTitle", queryModel.ProductSkuShortTitle.Trim());
                isJoinSkuInfoTable = true;
            }

            //按Sku查询
            if (!string.IsNullOrWhiteSpace(queryModel.SkuId) || !string.IsNullOrWhiteSpace(queryModel.DistributionStatus) || !string.IsNullOrWhiteSpace(queryModel.ProductSkuName))
            {
                //关联Sku表
                if (isJoinSkuInfoTable == false)
                    joinSqls.Add(@"INNER JOIN dbo.ProductSku AS ps WITH(NOLOCK) ON p.ProductCode = ps.ProductCode");

                //商品SkuID
                if (!string.IsNullOrWhiteSpace(queryModel.SkuId))
                {
                    conditions.Add("ps.SkuId=@skuId");
                    parameters.Add("@skuId", queryModel.SkuId.Trim());
                }
                //规格名称
                if (!string.IsNullOrWhiteSpace(queryModel.ProductSkuName))
                {
                    conditions.Add("ps.Name=@skuName");
                    parameters.Add("@skuName", queryModel.ProductSkuName.Trim());
                }

                //判断是否有选择了分销状态条件
                if (queryModel.DistributionStatus == "1")
                {
                    //1：已关联未设置结算价
                    joinSqls.Add(@"INNER JOIN dbo.DistributorProductMapping AS dpm WITH(NOLOCK) ON p.ProductCode =dpm.UpProductCode");
                    conditions.Add("(ps.SalePrice IS NULL OR ps.SalePrice=0)");
                }
                else if (queryModel.DistributionStatus == "2")
                {
                    //2：未关联未设置结算价
                    joinSqls.Add(@"LEFT JOIN dbo.DistributorProductMapping AS dpm WITH(NOLOCK) ON p.ProductCode =dpm.UpProductCode");
                    conditions.Add("dpm.UpProductCode IS NULL AND (ps.SalePrice IS NULL OR ps.SalePrice=0)");
                }
                else if (queryModel.DistributionStatus == "3")
                {
                    //3：未关联已设置结算价
                    joinSqls.Add(@"LEFT JOIN dbo.DistributorProductMapping AS dpm WITH(NOLOCK) ON p.ProductCode =dpm.UpProductCode");
                    conditions.Add("dpm.UpProductCode IS NULL AND ps.SalePrice IS NOT NULL AND ps.SalePrice>0");
                }
            }

            var db = DbConnection;

            //非精选云，跨云查询
            DbApiAccessUtility _apiAccessUtility = null;
            ApiDbConfigModel apiDbConfig = null;
            var baseCloudPlatformType = CloudPlatformType.Alibaba.ToString();
            if (queryModel.QueryAlibabaCloud && queryModel.SupplierUserId != null && queryModel.SupplierUserId.Any() && queryModel.CurCloudPlatformType != baseCloudPlatformType)
            {
                var supplierDbConfigs = new DbConfigRepository().GetListByFxUserIds(queryModel.SupplierUserId, baseCloudPlatformType);
                if (supplierDbConfigs != null && supplierDbConfigs.Any())
                {
                    //兼容精选分库逻辑
                    supplierDbConfigs = supplierDbConfigs.GroupBy(m => m.DbConfig.UserId)
                        .Select(m => m.OrderByDescending(o => o.FromFxDbConfig).First()).ToList();

                    var targetDbConfig = supplierDbConfigs.First();
                    apiDbConfig = new ApiDbConfigModel(targetDbConfig.DbServer.Location, baseCloudPlatformType,
                    targetDbConfig.DbNameConfig.Id);
                    _apiAccessUtility = new DbApiAccessUtility(apiDbConfig);
                }
            }
            dbApiAccessUtility = _apiAccessUtility;

            var totalCount = 0;
            var products = new List<DistributionProductViewModel>();
            //只有第一页需要加载总数据量
            if (queryModel.PageIndex <= 1)
            {
                var sqlByCount = $@"
SELECT Count(DISTINCT p.ProductCode) AS TotalCount
FROM dbo.Product AS p WITH(NOLOCK)
{string.Join(Environment.NewLine, joinSqls)} 
WHERE {string.Join(" AND ", conditions)}";

                //查询数据条数
                if (_apiAccessUtility != null)
                    totalCount = _apiAccessUtility.Query<int>(sqlByCount, parameters).FirstOrDefault();
                else
                    totalCount = db.QueryFirstOrDefault<int>(sqlByCount, parameters);
                //记录日志
                LogSql(sqlByCount, parameters, $"查询分销品列表数量{(apiDbConfig == null ? db.Database : apiDbConfig.ToJson(true))}");
            }

            if (totalCount > 0 || queryModel.PageIndex > 1)
            {
                //基础查询语句
                //                var baseSql = @"SELECT 
                //DISTINCT p.Id,p.ShopId,p.SourceUserId,p.PlatformId,p.ProductCode,p.Subject,p.ImageUrl,p.CargoNumber,p.Status --,
                //--ps.Id,ps.SkuCode,ps.SalePrice,ps.DefaultSettlementPrice,ps.ImgUrl,ps.Name,ps.CargoNumber
                //FROM dbo.Product AS p WITH(NOLOCK)
                //INNER JOIN dbo.ProductSku AS ps WITH(NOLOCK) ON p.ProductCode = ps.ProductCode";

                var sql = $@"
SELECT DISTINCT p.Id,p.ShopId,p.SourceUserId,p.PlatformId,p.ProductCode,p.Subject,p.ImageUrl,p.CargoNumber,p.Status,p.CreateTime,p.ProductType
FROM dbo.Product AS p WITH(NOLOCK)
{string.Join(Environment.NewLine, joinSqls)}
WHERE {string.Join(" AND ", conditions)}
ORDER BY p.CreateTime DESC ,p.Id
OFFSET {(queryModel.PageIndex - 1) * queryModel.PageSize} ROWS 
FETCH NEXT {queryModel.PageSize} ROWS ONLY";

                //记录日志
                LogSql(sql, parameters, $"查询分销品商品数据{(apiDbConfig == null ? db.Database : apiDbConfig.ToJson(true))}");

                if (_apiAccessUtility != null)
                    products = _apiAccessUtility.Query<DistributionProductViewModel>(sql, parameters).ToList();
                else
                    products = db.Query<DistributionProductViewModel>(sql, parameters).ToList();

                if (products.Any())
                {
                    var productCodes = products.Select(f => f.ProductCode).Distinct().ToList();
                    //var querySkuSql = @"
                    //SELECT ps.Id,ps.ProductCode,ps.SkuCode,ps.SkuId,ps.SalePrice,ps.DefaultSettlementPrice,ps.ImgUrl,ps.Name,ps.CargoNumber,ps.PriceJson 
                    //FROM dbo.ProductSku AS ps WITH(NOLOCK) WHERE ProductCode IN@productCodes";
                    var querySkuSql = $@"SELECT ps.Id,ps.ProductCode,ps.SkuCode,ps.SkuId,ps.SalePrice,ps.DefaultSettlementPrice,ps.ImgUrl,ps.Name,ps.CargoNumber,ps.PriceJson 
FROM dbo.ProductSku AS ps WITH(NOLOCK)
INNER JOIN dbo.FunStringToTable(@pCodeStr,',') fstt ON ps.ProductCode = fstt.item
WHERE 1=1";

                    var tdp = new DynamicParameters();
                    tdp.Add("@pCodeStr", string.Join(",", productCodes));

                    //商品SkuID
                    if (!string.IsNullOrWhiteSpace(queryModel.SkuId))
                    {
                        querySkuSql += " AND ps.SkuId=@skuId";
                        tdp.Add("@skuId", queryModel.SkuId.Trim());
                    }

                    //记录日志
                    LogSql(sql, tdp, "查询分销品商品Sku数据");

                    #region 读取结算价开关设置
                    //读取结算价开关设置
                    //var commonSettingRepo = new CommonSettingRepository();
                    //var settlementPriceSetting = commonSettingRepo.Get(Enum.BusinessSettingKeys.SupplyBy1688.DefaultSettlementPriceSwitch, BaseSiteContext.Current.CurrentShopId);
                    //var settlementPriceSwitch = true;
                    //if (settlementPriceSetting != null)
                    //{
                    //    settlementPriceSwitch = settlementPriceSetting.Value.ToBool();
                    //}

                    var tupleSet = GetsettlementPriceSwitch();
                    var settlementPriceSwitch = tupleSet.Item1;
                    var priceFields = tupleSet.Item2;
                    var productSkus = new List<DistributionProductSkuVm>();
                    if (_apiAccessUtility != null)
                        productSkus = _apiAccessUtility.Query<DistributionProductSkuVm>(querySkuSql, tdp).ToList();
                    else
                        productSkus = db.Query<DistributionProductSkuVm>(querySkuSql, tdp).ToList();

                    if (productSkus.Any())
                    {
                        var skuLookup = productSkus.ToLookup(f => f.ProductCode, f => f);
                        foreach (var item in products)
                        {
                            if (skuLookup.Contains(item.ProductCode))
                            {
                                var skus = skuLookup[item.ProductCode].OrderBy(m => m.Id).ToList();
                                if (skus.Any())
                                {
                                    //赋值默认结算价的开关，按优先级排序的价格字段
                                    skus.ForEach(sku => 
                                    {
                                        sku.DefaultSettlementPriceSwitch = settlementPriceSwitch;
                                        sku.PriceFields = priceFields;
                                    });
                                }
                                item.Skus = skus;
                            }
                        }
                    }
                    #endregion

                    #region 商品简称及规格简称
                    //获取商品简称及规格简称信息
                    var productInfos = new List<ProductInfoFx>();
                    var productSkuInfos = new List<ProductSkuInfoFx>();
                    var fxUserId = BaseSiteContext.Current.CurrentFxUserId;
                    if (_apiAccessUtility != null)
                    {
                        var sqlInfo = $"SELECT * FROM dbo.ProductInfo WITH(NOLOCK) WHERE FxUserId = @FxUserId AND ProductCode IN @Codes;";
                        productInfos = _apiAccessUtility.Query<ProductInfoFx>(sqlInfo, new { FxUserId = fxUserId, Codes = productCodes }).ToList();

                        var sqlSkuInfo = $"SELECT * FROM dbo.ProductSkuInfo WITH(NOLOCK) WHERE FxUserId = @FxUserId AND ProductCode IN @Codes;";
                        productSkuInfos = _apiAccessUtility.Query<ProductSkuInfoFx>(sqlSkuInfo, new { FxUserId = fxUserId, Codes = productCodes }).ToList();
                    }
                    else
                    {
                        var productInfoRepository = new ProductInfoFxRepository(_connectionString);
                        productInfos = productInfoRepository.GetList(fxUserId, productCodes);

                        var productSkuInfoRepository = new ProductSkuInfoFxRepository(_connectionString);
                        productSkuInfos = productSkuInfoRepository.GetList(fxUserId, productCodes);
                    }

                    //设置分销品简称
                    products.ForEach(item =>
                    {
                        var productInfo = productInfos?.FirstOrDefault(m => m.ProductCode == item.ProductCode);
                        item.ShortTitle = productInfo?.ShortTitle;
                    });

                    products.ForEach(item =>
                    {
                        item.Skus?.ForEach(skuItem =>
                        {
                            var skuInfo = productSkuInfos.FirstOrDefault(m =>
                                m.ProductCode == item.ProductCode && m.SkuCode == skuItem.SkuCode);
                            skuItem.ShortTitle = skuInfo?.ShortTitle;
                        });
                    });
                    #endregion
                }

                //var queryRst = DbConnection.Query<DistributionProductViewModel, DistributionProductSkuVm, DistributionProductViewModel>(sql, (pv, psv) =>
                //{
                //    DistributionProductViewModel productVm = null;
                //    if (!dict.TryGetValue(pv.ProductCode, out productVm))
                //    {
                //        productVm = pv;
                //        dict.Add(productVm.ProductCode, productVm);
                //    }
                //    //添加sku的数据
                //    if (psv != null && !productVm.Skus.Any(x => x.Id == psv.Id))
                //    {
                //        psv.ProductCode = productVm.ProductCode;  //赋值 ProductCode
                //        productVm.Skus.Add(psv);
                //    }

                //    return productVm;
                //}, parameters);
            }

            return new Tuple<int, List<DistributionProductViewModel>>(totalCount, products);
        }

        /// <summary>
        /// 赋值 关联的商品数量字段
        /// </summary>
        /// <param name="list"></param>
        public void SetRelationCount(List<DistributionProductViewModel> list, int supplierFxUserId, DbApiAccessUtility dbApiAccessUtility = null)
        {
            //无数据
            if (list == null || !list.Any()) return;

            //查询脚本
            //@currUserId-当前登录的厂家UserId
            var currUserId = supplierFxUserId;
            //@upShopId-当前厂家设置的下单1688店铺
            var upShopId = list.FirstOrDefault().ShopId;
            var sql = @"
SELECT dpm.UpProductCode,dpsm.UpSkuCode,dpm.DownProductCode,dpsm.DownSkuCode,COUNT(1) AS RelationCount FROM dbo.DistributorProductMapping  AS dpm
INNER JOIN dbo.DistributorProductSkuMapping AS dpsm ON dpm.ProductMappingCode = dpsm.ProductMappingCode
WHERE dpm.UpFxUserId=@currUserId AND dpm.UpShopId = @upShopId AND dpsm.Status=1
AND dpm.UpProductCode IN @productCode AND dpsm.UpSkuCode IN @skuCode
GROUP BY dpm.UpProductCode,dpsm.UpSkuCode,dpm.DownProductCode,dpsm.DownSkuCode;
";
            var dp = new DynamicParameters();
            dp.Add("@currUserId", currUserId);
            dp.Add("@upShopId", upShopId);
            dp.Add("@productCode", list.Select(f => f.ProductCode).Distinct().ToList());
            dp.Add("@skuCode", list.SelectMany(f => f.Skus).Select(f => f.SkuCode).Distinct().ToList());
            //记录日志            
            LogSql(sql, dp, "厂家视角查询分销品关联的商家商品数量");

            //分页查询，谨防 参数超出2.1k 个
            var db = DbConnection;
            var skus = list.SelectMany(f => f.Skus);
            var pageSize = 1000;
            var pageCount = (long)decimal.Ceiling(skus.Count() / (pageSize * 1.0M));
            var rst = new List<DistributionProductRelationCountModel>();
            for (int i = 0; i < pageCount; i++)
            {
                var tempList = skus.Skip(i * pageSize).Take(pageSize);
                //@productCode-厂家分销品的产品code
                var productCode = tempList.Select(f => f.ProductCode).Distinct().ToList();
                //@skuCode-厂家分销品的skuCode
                var skuCode = tempList.Select(f => f.SkuCode).Distinct().ToList();
                //查询结果
                var rstByPage = new List<DistributionProductRelationCountModel>();
                if (dbApiAccessUtility != null)
                    rstByPage = dbApiAccessUtility.Query<DistributionProductRelationCountModel>(sql, new
                    {
                        currUserId,
                        upShopId,
                        productCode,
                        skuCode
                    }).ToList();
                else
                {
                    rstByPage = db.Query<DistributionProductRelationCountModel>(sql, new
                    {
                        currUserId,
                        upShopId,
                        productCode,
                        skuCode
                    }).ToList();
                }
                if (rstByPage.Any())
                {
                    rst.AddRange(rstByPage);
                }
            }

            if (rst.Any())
            {
                //赋值
                var proRelationCountDict = rst.GroupBy(f => $"{f.UpProductCode}").ToDictionary(f => f.Key, f => f.GroupBy(p => p.UpProductCode + p.DownProductCode).Count());
                var skuRelationCountDict = rst.GroupBy(f => $"{f.UpProductCode}-{f.UpSkuCode}").ToDictionary(f => f.Key, f => f.Count());
                foreach (var pro in list)
                {
                    if (proRelationCountDict.ContainsKey(pro.ProductCode))
                        pro.RelationCount = proRelationCountDict[pro.ProductCode];

                    foreach (var sku in pro.Skus)
                    {
                        var key = $"{sku.ProductCode}-{sku.SkuCode}";
                        if (!skuRelationCountDict.ContainsKey(key))
                            continue;
                        sku.RelationCount = skuRelationCountDict[key];
                    }
                }
            }
        }

        /// <summary>
        /// 赋值 关联的商品数量字段
        /// 商家视角
        /// </summary>
        /// <param name="list"></param>
        public void SetRelationCountByAgent(List<DistributionProductViewModel> list, int fxUserId)
        {
            //无数据
            if (list == null || !list.Any()) return;

            //查询脚本
            //@currUserId-当前登录的商家FxUserId
            var currUserId = fxUserId;
            //@supplierUserId 厂家的用户id
            var supplierUserId = list.Select(f => f.SourceUserId).Distinct();
            //@UpShopId厂家设置的下单1688店铺
            var upShopId = list.Select(f => f.ShopId).Distinct();

            var sql = @"
SELECT dpm.UpProductCode,dpsm.UpSkuCode,dpm.DownProductCode,dpsm.DownSkuCode,COUNT(1) AS RelationCount FROM dbo.DistributorProductMapping  AS dpm
INNER JOIN dbo.DistributorProductSkuMapping AS dpsm ON dpm.ProductMappingCode = dpsm.ProductMappingCode
WHERE dpm.UpFxUserId IN @supplierUserId AND dpm.UpShopId IN @upShopId AND dpsm.Status=1 
AND dpm.DownFxUserId=@currUserId
AND dpm.UpProductCode IN @productCode AND dpsm.UpSkuCode IN @skuCode
GROUP BY dpm.UpProductCode,dpsm.UpSkuCode,dpm.DownProductCode,dpsm.DownSkuCode;
";

            var dp = new DynamicParameters();
            dp.Add("@supplierUserId", supplierUserId.ToList());
            dp.Add("@currUserId", currUserId);
            dp.Add("@upShopId", upShopId.ToList());
            dp.Add("@productCode", list.Select(f => f.ProductCode).Distinct().ToList());
            dp.Add("@skuCode", list.SelectMany(f => f.Skus).Select(f => f.SkuCode).Distinct().ToList());
            //记录日志            
            LogSql(sql, dp, "商家视角查询分销品关联的商家商品数量");

            //分页查询，谨防 参数超出2.1k 个
            var db = DbConnection;
            var skus = list.SelectMany(f => f.Skus);
            var pageSize = 1000;
            var pageCount = (long)decimal.Ceiling(skus.Count() / (pageSize * 1.0M));
            var rst = new List<DistributionProductRelationCountModel>();
            for (int i = 0; i < pageCount; i++)
            {
                var tempList = skus.Skip(i * pageSize).Take(pageSize);
                //@productCode-厂家分销品的产品code
                var productCode = tempList.Select(f => f.ProductCode).Distinct().ToList();
                //@skuCode-厂家分销品的skuCode
                var skuCode = tempList.Select(f => f.SkuCode).Distinct().ToList();
                //查询结果
                var rstByPage = db.Query<DistributionProductRelationCountModel>(sql, new
                {
                    supplierUserId,
                    upShopId,
                    currUserId,
                    productCode,
                    skuCode
                });
                if (rstByPage.Any())
                {
                    rst.AddRange(rstByPage);
                }
            }

            if (rst.Any())
            {
                //赋值
                var proRelationCountDict = rst.GroupBy(f => $"{f.UpProductCode}").ToDictionary(f => f.Key, f => f.GroupBy(p => p.UpProductCode + p.DownProductCode).Count());
                var skuRelationCountDict = rst.GroupBy(f => $"{f.UpProductCode}-{f.UpSkuCode}").ToDictionary(f => f.Key, f => f.Count());
                foreach (var pro in list)
                {
                    if (proRelationCountDict.ContainsKey(pro.ProductCode))
                        pro.RelationCount = proRelationCountDict[pro.ProductCode];

                    foreach (var sku in pro.Skus)
                    {
                        var key = $"{sku.ProductCode}-{sku.SkuCode}";
                        if (!skuRelationCountDict.ContainsKey(key))
                            continue;
                        sku.RelationCount = skuRelationCountDict[key];
                    }
                }
            }
        }

        /// <summary>
        /// 赋值 供应商信息
        /// </summary>
        /// <param name="list"></param>
        /// <param name="fxUserId"></param>
        public void SetSupplierInfo(List<DistributionProductViewModel> list, int fxUserId)
        {

            //无数据
            if (list == null || !list.Any()) return;

            var supplierUserIds = list.Select(f => f.SourceUserId).Distinct().ToList();

            var supplierInfo = new UserFxRepository().GetSupplierInfoByAgentUserId(supplierUserIds, fxUserId);

            if (supplierInfo.Any())
            {
                var supplerDict = supplierInfo.ToDictionary(f => f.Id, f => f);
                list.ForEach(item =>
                {
                    if (supplerDict.ContainsKey(item.SourceUserId))
                    {
                        var supplier = supplerDict[item.SourceUserId];
                        item.SupplierInfo = $"{supplier.Mobile}({supplier.RemarkName})";
                    }
                });
            }
        }


        /// <summary>
        /// 查询分销品关联的商品列表
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public Tuple<int, List<MapProductViewModel>> LoadRelationProductList(QueryMapProductModel queryModel)
        {
            var joinSqls = new List<string>();
            var parameters = new DynamicParameters();
            var conditions = new List<string>();

            //厂家分销品的code
            conditions.Add("dpm.UpProductCode=@upProductCode");
            parameters.Add("@upProductCode", queryModel.AlibabaProductCode);

            //关联状态为1
            conditions.Add("dpm.Status=1");

            //商家id
            if (queryModel.AgentUserId > 0)
            {
                conditions.Add("dpm.DownFxUserId=@downFxUserId");
                parameters.Add("@downFxUserId", queryModel.AgentUserId);
            }
            //多商家
            if (string.IsNullOrEmpty(queryModel.SelectAgentId) == false)
            {
                var agentIds = queryModel.SelectAgentId.SplitToList(",").ConvertAll(x => x.ToInt()).Where(sid => sid > 0).Distinct().ToList();
                if (agentIds.Any())
                {
                    conditions.Add("dpm.DownFxUserId IN @downFxUserIds");
                    parameters.Add("@downFxUserIds", agentIds);
                }
            }
            //商家-商品ID
            if (!string.IsNullOrWhiteSpace(queryModel.ProductId))
            {
                conditions.Add("p.PlatformId=@productId");
                parameters.Add("@productId", queryModel.ProductId.Trim());
            }

            //商家-商品名称
            if (!string.IsNullOrWhiteSpace(queryModel.ProductSubject))
            {
                conditions.Add("p.Subject LIKE @subject");
                parameters.Add("@subject", $"%{queryModel.ProductSubject.Trim()}%");
            }

            var needJoinSku = false; //是否需要关联sku关系表

            //商家-商品SkuID
            if (!string.IsNullOrWhiteSpace(queryModel.SkuId))
            {
                needJoinSku = true;
                conditions.Add("ps.SkuId=@skuId");
                parameters.Add("@skuId", queryModel.SkuId.Trim());
            }

            //商家-SKU属性值
            if (!string.IsNullOrWhiteSpace(queryModel.SkuAttrVal))
            {
                needJoinSku = true;
                conditions.Add("ps.Name=@attrVal");
                parameters.Add("@attrVal", queryModel.SkuAttrVal.Trim());
            }

            //商家-SkuCargoNumber
            if (!string.IsNullOrWhiteSpace(queryModel.SkuCargoNumber))
            {
                needJoinSku = true;
                conditions.Add("ps.CargoNumber = @cargoNunmber");
                parameters.Add("@cargoNunmber", queryModel.SkuCargoNumber.Trim());
            }

            var needJoinSettlementPrice = false;
            //判断是否有选择了分销状态条件
            if (queryModel.DistributionStatus == "1")
            {
                needJoinSku = true;
                needJoinSettlementPrice = true;
                //1：已设置结算价
                conditions.Add("(psp.Price IS NOT NULL AND psp.Price>0)");
            }
            else if (queryModel.DistributionStatus == "2")
            {
                needJoinSku = true;
                needJoinSettlementPrice = true;
                //2：未设置结算价
                conditions.Add("(psp.Price IS NULL OR psp.Price=0)");
            }

            joinSqls.Add(@"INNER JOIN dbo.DistributorProductSkuMapping AS dpsm WITH(NOLOCK) ON dpm.ProductMappingCode=dpsm.ProductMappingCode");
            conditions.Add("dpsm.Status=1");

            if (needJoinSku)
            {
                joinSqls.Add(@"INNER JOIN dbo.ProductSku AS ps WITH(NOLOCK) ON p.ProductCode = ps.ProductCode AND dpsm.DownSkuCode = ps.SkuCode");
            }
            if (needJoinSettlementPrice)
            {
                joinSqls.Add(@"INNER JOIN P_ProductSettlementPrice AS psp WITH(NOLOCK) ON ps.SkuCode=psp.ProductSkuCode AND p.SourceUserId=psp.FxUserId");
            }

            ////商家-商品简称
            //if (!string.IsNullOrWhiteSpace(queryModel.ProductShortTitle))
            //{
            //    joinSqls.Add(@"INNER JOIN dbo.ProductInfo AS pi WITH(NOLOCK) ON p.ProductCode = pi.ProductCode AND p.SourceUserId= pi.FxUserId");
            //    conditions.Add("pi.ShortTitle=@productShortTitle");
            //    parameters.Add("@productShortTitle", queryModel.ProductShortTitle.Trim());
            //}

            ////商家-SKU简称
            //if (!string.IsNullOrWhiteSpace(queryModel.SkuShortTitle))
            //{
            //    joinSqls.Add(@"INNER JOIN dbo.ProductSkuInfo AS psi WITH(NOLOCK) ON p.ProductCode=psi.ProductCode AND p.SourceUserId=psi.FxUserId");
            //    conditions.Add("pi.ShortTitle=@skuShortTitle");
            //    parameters.Add("@skuShortTitle", queryModel.SkuShortTitle.Trim());
            //}

            var db = DbConnection;

            var totalCount = 0;
            var dict = new Dictionary<string, MapProductViewModel>();
            //只有第一页需要加载总数据量
            if (queryModel.PageIndex <= 1)
            {
                var sqlByCount = $@"
SELECT Count(DISTINCT p.ProductCode) AS TotalCount
FROM dbo.DistributorProductMapping AS dpm WITH(NOLOCK)
INNER JOIN dbo.Product AS p WITH(NOLOCK) ON dpm.DownProductCode =p.ProductCode
{string.Join(Environment.NewLine, joinSqls)} 
WHERE {string.Join(" AND ", conditions)}";

                //查询数据条数
                totalCount = db.QueryFirstOrDefault<int>(sqlByCount, parameters);
                //记录日志
                LogSql(sqlByCount, parameters, "查看分销品关联的商品列表的数量");
            }

            var products = new List<MapProductViewModel>();
            if (totalCount > 0 || queryModel.PageIndex > 1)
            {
                //                var baseSql = @"SELECT 
                //p.Id,p.ShopId,p.SourceUserId,p.PlatformId,p.ProductCode,p.Subject,p.ImageUrl,p.CargoNumber,
                //ps.Id,ps.SkuCode,ps.ImgUrl,ps.Name,ps.CargoNumber,dpsm.UpSkuCode,dpsm.SkuMappingCode
                //FROM dbo.DistributorProductMapping AS dpm WITH(NOLOCK)
                //INNER JOIN dbo.DistributorProductSkuMapping AS dpsm WITH(NOLOCK) ON dpm.ProductMappingCode=dpsm.ProductMappingCode
                //INNER JOIN dbo.Product AS p WITH(NOLOCK) ON dpm.DownProductCode =p.ProductCode
                //INNER JOIN dbo.ProductSku AS ps WITH(NOLOCK) ON p.ProductCode = ps.ProductCode AND dpsm.DownSkuCode = ps.SkuCode";

                var sql = $@"
SELECT DISTINCT p.Id,p.ShopId,p.PlatformType,p.SourceUserId,p.PlatformId,p.ProductCode,p.Subject,p.ImageUrl,p.CargoNumber,p.CreateTime,dpm.ProductMappingCode
FROM dbo.DistributorProductMapping AS dpm WITH(NOLOCK)
INNER JOIN dbo.Product AS p WITH(NOLOCK) ON dpm.DownProductCode =p.ProductCode
{string.Join(Environment.NewLine, joinSqls)}
WHERE {string.Join(" AND ", conditions)}
ORDER BY p.CreateTime DESC ,p.ProductCode
OFFSET {(queryModel.PageIndex - 1) * queryModel.PageSize} ROWS 
FETCH NEXT {queryModel.PageSize} ROWS ONLY";

                //记录日志
                LogSql(sql, parameters, "查询关联商品列表");

                products = db.Query<MapProductViewModel>(sql, parameters).ToList();
                if (products.Any())
                {
                    var productCodes = products.Select(f => f.ProductCode).Distinct().ToList();

                    //var querySkuSql = @"
                    //SELECT ps.Id,ps.ProductCode,ps.SkuCode,ps.SkuId,ps.SalePrice,ps.DefaultSettlementPrice,ps.ImgUrl,ps.Name,ps.CargoNumber,dpsm.UpSkuCode,dpsm.SkuMappingCode,dpsm.UpProductCode  
                    //FROM dbo.ProductSku AS ps WITH(NOLOCK)
                    //INNER JOIN dbo.DistributorProductSkuMapping AS dpsm WITH(NOLOCK) ON ps.SkuCode= dpsm.DownSkuCode
                    //WHERE ps.ProductCode in@productCodes AND dpsm.UpFxUserId=@supplierUserId AND dpsm.Status=1 AND dpsm.UpProductCode=@upProductCode";
                    var querySkuSql = $@"SELECT ps.Id,ps.ProductCode,ps.SkuCode,ps.SkuId,ps.SalePrice,ps.DefaultSettlementPrice,ps.ImgUrl,ps.Name,ps.CargoNumber,dpsm.UpSkuCode,dpsm.SkuMappingCode,dpsm.UpProductCode  
FROM dbo.ProductSku AS ps WITH(NOLOCK)
INNER JOIN dbo.FunStringToTable(@pCodeStr,',') fstt ON ps.ProductCode = fstt.item
INNER JOIN dbo.DistributorProductSkuMapping AS dpsm WITH(NOLOCK) ON ps.SkuCode= dpsm.DownSkuCode
WHERE dpsm.UpFxUserId=@supplierUserId AND dpsm.Status=1 AND dpsm.UpProductCode=@upProductCode";

                    var tdp = new DynamicParameters();
                    tdp.Add("@pCodeStr", string.Join(",", productCodes));
                    tdp.Add("@supplierUserId", queryModel.SuplierUserId);
                    tdp.Add("@upProductCode", queryModel.AlibabaProductCode);

                    //商家-商品SkuID
                    if (!string.IsNullOrWhiteSpace(queryModel.SkuId))
                    {
                        querySkuSql += " AND ps.SkuId=@skuId";
                        tdp.Add("@skuId", queryModel.SkuId.Trim());
                    }

                    //记录日志
                    LogSql(querySkuSql, tdp, "查询分销品关联商品的Sku数据");

                    var productSkus = db.Query<MapProductSkuVm>(querySkuSql, tdp);// new { productCodes, supplierUserId = queryModel.SuplierUserId });
                    if (productSkus.Any())
                    {
                        var skuLookup = productSkus.ToLookup(f => f.ProductCode, f => f);
                        foreach (var item in products)
                        {
                            if (skuLookup.Contains(item.ProductCode))
                            {
                                item.Skus = skuLookup[item.ProductCode].ToList();
                            }
                        }
                    }

                    #region 商品简称及规格简称
                    //获取商品简称及规格简称信息
                    var productInfoRepository = new ProductInfoFxRepository();
                    var productInfos =
                        productInfoRepository.GetList(BaseSiteContext.Current.CurrentFxUserId, productCodes);
                    //设置分销品简称
                    products.ForEach(item =>
                    {
                        var productInfo = productInfos?.FirstOrDefault(m => m.ProductCode == item.ProductCode);
                        if (productInfo != null)
                        {
                            item.ShortTitle = productInfo.ShortTitle;
                        }
                    });
                    var productSkuInfoRepository = new ProductSkuInfoFxRepository();
                    var productSkuInfos =
                        productSkuInfoRepository.GetList(BaseSiteContext.Current.CurrentFxUserId, productCodes);
                    products.ForEach(item =>
                    {
                        item.Skus?.ForEach(skuItem =>
                        {
                            var skuInfo = productSkuInfos.FirstOrDefault(m =>
                                m.ProductCode == item.ProductCode && m.SkuCode == skuItem.SkuCode);
                            skuItem.ShortTitle = skuInfo?.ShortTitle;
                        });
                    });
                    #endregion
                }
            }

            return new Tuple<int, List<MapProductViewModel>>(totalCount, products);
        }

        /// <summary>
        /// 赋值 关联商品的结算价格字段
        /// </summary>
        /// <param name="list"></param>
        /// <param name="createUserId"></param>
        public void SetSettlementPrice(List<MapProductViewModel> list, int createUserId, int settlementType)
        {
            //无数据
            if (list == null || !list.Any())
                return;

            //通过 P_ProductSettlementPrice 表来获取结算价格
            // CreateUser - 创建结算价的用户id（这个场景来看就是供应商的id）
            // FxUserId - 结算商品所属的用户（这个场景来看是指商家的用户id)
            const string sql = @"SELECT Id,UniqueKey,ProductCode,ProductSkuCode,SettlementType,PlatformType,CreateUser,FxUserId,Price 
                        FROM dbo.P_ProductSettlementPrice 
                        WHERE ProductSkuCode IN @skuCode AND FxUserId=@fxUserId AND CreateUser=@createUserId AND SettlementType=@settlementType";
            //记录日志
            var tdp = new DynamicParameters();
            tdp.Add("@skuCode", list.SelectMany(f => f.Skus).Select(f => f.SkuCode).Distinct());
            tdp.Add("@fxUserId", list.Select(f => f.SourceUserId).Distinct());
            tdp.Add("@createUserId", createUserId);
            tdp.Add("@settlementType", settlementType);
            LogSql(sql, tdp, "查询商品的结算价");

            var rst = new List<MapProductSettlementPriceModel>();

            //按商家分组
            var groups = list.GroupBy(f => f.SourceUserId);
            foreach (var g in groups)
            {
                var fxUserId = g.Key;

                //分页查询，谨防 参数超出2.1k 个
                var db = DbConnection;
                var skus = g.ToList().SelectMany(f => f.Skus);
                var pageSize = 1000;
                var pageCount = (long)decimal.Ceiling(skus.Count() / (pageSize * 1.0M));
                for (int i = 0; i < pageCount; i++)
                {
                    var tempList = skus.Skip(i * pageSize).Take(pageSize);
                    //@productCode-商家商品的产品code
                    //var productCode = tempList.Select(f => f.ProductCode).Distinct().ToList();
                    //@skuCode-商家商品的skuCode
                    var skuCode = tempList.Select(f => f.SkuCode).Distinct().ToList();
                    //查询结果
                    var rstByPage = db.Query<MapProductSettlementPriceModel>(sql, new
                    {
                        skuCode,
                        fxUserId,
                        createUserId,
                        settlementType
                    });
                    if (rstByPage.Any())
                    {
                        rst.AddRange(rstByPage);
                    }
                }
            }

            if (rst.Any())
            {
                //赋值
                var dict = rst.ToLookup(f => $"{f.ProductCode}-{f.ProductSkuCode}", f => f).ToDictionary(f => f.Key, f => f.OrderByDescending(p => p.Id).FirstOrDefault());
                foreach (var pro in list)
                {
                    foreach (var sku in pro.Skus)
                    {
                        var key = $"{sku.ProductCode}-{sku.SkuCode}";
                        if (!dict.ContainsKey(key))
                            continue;
                        var settlementPrice = dict[key];
                        sku.SettlementId = settlementPrice.Id;
                        sku.SettlementType = settlementPrice.SettlementType;
                        sku.SettlementCreateUserId = settlementPrice.CreateUser;
                        sku.SettlementUniqueKey = settlementPrice.UniqueKey;
                        sku.PurchasePrice = settlementPrice.Price;
                    }
                }
            }
        }


        /// <summary>
        /// 赋值 关联商品的结算价格字段(已关联列表使用)
        /// </summary>
        /// <param name="list"></param>
        /// <param name="createUserId"></param>
        public void SetSettlementPriceProductFx(List<ProductFx> list, int createUserId, int settlementType, ProductFxRequertModule model)
        {
            //无数据
            if (list == null || !list.Any())
                return;


            //Log.WriteLine($" createUserId:{createUserId}", "耗时测试.txt");
            //通过 P_ProductSettlementPrice 表来获取结算价格
            // CreateUser - 创建结算价的用户id（这个场景来看就是供应商的id）
            // FxUserId - 结算商品所属的用户（这个场景来看是指商家的用户id)
            string sqlSelect = @"SELECT Id,UniqueKey,ProductCode,ProductSkuCode,PlatformType,FxUserId,SettlementType,Price,CreateUser
                        FROM dbo.P_ProductSettlementPrice WITH(NOLOCK) ";
            string sqlWhere = " WHERE FxUserId=@fxUserId AND CreateUser=@createUserId AND SettlementType=@SettlementType ";
            //string sql = @"SELECT Id,UniqueKey,ProductCode,ProductSkuCode,PlatformType,FxUserId,SettlementType,Price,CreateUser
            //            FROM dbo.P_ProductSettlementPrice WITH(NOLOCK)
            //            WHERE ProductSkuCode IN @skuCode AND FxUserId=@fxUserId AND CreateUser=@createUserId AND SettlementType=@SettlementType";
            //记录日志
            var tdp = new DynamicParameters();
            tdp.Add("@skuCode", list.SelectMany(f => f.Skus).Select(f => f.SkuCode).Distinct());
            tdp.Add("@fxUserId", list.Select(f => f.SourceUserId).Distinct());
            tdp.Add("@createUserId", createUserId);
            tdp.Add("@SettlementType", settlementType);
            LogSql(sqlSelect + sqlWhere, tdp, "查询商品的结算价(已关联列表使用)");

            var rst = new List<MapProductSettlementPriceModel>();

            //按商家分组
            var groups = list.GroupBy(f => f.SourceUserId);
            foreach (var g in groups)
            {

                var fxUserId = g.Key;

                //分页查询，谨防 参数超出2.1k 个
                var db = DbConnection;
                var skus = g.ToList().SelectMany(f => f.Skus).ToList();
                var pageSize = 2000;
                var pageCount = (long)decimal.Ceiling(skus.Count() / (pageSize * 1.0M));
                for (int i = 0; i < pageCount; i++)
                {

                    var tempList = skus.Skip(i * pageSize).Take(pageSize);
                    var skuCode = tempList.Select(f => f.SkuCode).Distinct().ToList();
                    var sql = $"{sqlSelect} INNER JOIN FunStringToTable(@SkuCodes,',') ft ON ft.item=ProductSkuCode {sqlWhere}";

                    var queryData = new
                    {
                        SkuCodes = string.Join(",", skuCode),
                        fxUserId,
                        createUserId,
                        settlementType
                    };

                    //Stopwatch stopwatch4 = new Stopwatch();
                    //stopwatch4.Start();
                    //查询结果
                    var rstByPage = db.Query<MapProductSettlementPriceModel>(sql, queryData);
                    //stopwatch4.Stop();
                    //Log.WriteLine($"遍历数据执行SQL g;{queryData.ToJson()}；db：{db.ConnectionString}  耗时{stopwatch4.ElapsedMilliseconds}ms", "耗时测试.txt");

                    if (rstByPage.Any())
                    {
                        rst.AddRange(rstByPage);
                    }
                }

            }

            //if (rst.Any())
            //{
            //赋值
            var dict = rst.ToLookup(f => $"{f.ProductCode}-{f.ProductSkuCode}", f => f).ToDictionary(f => f.Key, f => f.OrderByDescending(p => p.Id).FirstOrDefault());

            foreach (var pro in list)
            {
                foreach (var sku in pro.Skus)
                {
                    var key = $"{sku.ProductCode}-{sku.SkuCode}";
                    if (!dict.ContainsKey(key))
                    {
                        sku.PurchasePrice = null;
                        continue;
                    }

                    var settlementPrice = dict[key];
                    sku.SettlementId = settlementPrice.Id;
                    sku.SettlementUniqueKey = settlementPrice.UniqueKey;
                    sku.PurchasePrice = settlementPrice.Price;
                    sku.SettlementType = settlementPrice.SettlementType.ToString2();
                    sku.SettlementCreateUserId = settlementPrice.CreateUser;
                }

                //设置商品级别的最大最小价格
                pro.minPrice = pro.Skus?.Min(f => f.PurchasePrice) ?? 0;
                pro.maxPrice = pro.Skus?.Max(f => f.PurchasePrice) ?? 0;

            }
            //}
        }

        public Tuple<bool, string> GetsettlementPriceSwitch()
        {
            var commonSettingRepo = new CommonSettingRepository();
            var keys = new List<string> { BusinessSettingKeys.SupplyBy1688.DefaultSettlementPriceSwitch, BusinessSettingKeys.SupplyBy1688.DefaultSettlementPriceFields };
            var settings = commonSettingRepo.GetSets(keys, BaseSiteContext.Current.CurrentShopId);
            var defaultSettlementPriceFields = settings.FirstOrDefault(a => a.Key == BusinessSettingKeys.SupplyBy1688.DefaultSettlementPriceFields)?.Value ?? "";

            var settlementPriceSetting = settings.FirstOrDefault(a => a.Key == BusinessSettingKeys.SupplyBy1688.DefaultSettlementPriceSwitch);
            var settlementPriceSwitch = true;
            if (settlementPriceSetting != null)
            {
                settlementPriceSwitch = settlementPriceSetting.Value.ToBool();
            }
            return Tuple.Create<bool, string>(settlementPriceSwitch, defaultSettlementPriceFields);
        }


        /// <summary>
        /// 赋值 关联商品的 分销商信息
        /// </summary>
        /// <param name="list"></param>
        public void SetAgentInfo(List<MapProductViewModel> list, string from, int supplierFxUserId)
        {
            //无数据
            if (list == null || !list.Any())
                return;
            var db = DbConnection;
            //供应商查看列表显示 分销商账号（备注）
            if (from == "supplier")
            {
                var fxUserIds = list.Select(f => f.SourceUserId).Distinct().ToList();

                var agentInfo = new UserFxRepository().GetMapProductAgentInfo(fxUserIds, supplierFxUserId);

                if (agentInfo.Any())
                {
                    var agentDict = agentInfo.ToDictionary(f => f.Id, f => f);
                    list.ForEach(item =>
                    {
                        if (agentDict.ContainsKey(item.SourceUserId))
                        {
                            var agent = agentDict[item.SourceUserId];
                            item.AgentInfo = $"{agent.Mobile}({agent.Remark})";
                        }
                    });
                }
            }
            else
            {
                //分销商查看，显示商品的店铺
                var shopIds = list.Select(f => f.ShopId).Distinct().ToList();

                var shops = new ShopRepository().GetShopByIds(shopIds, "Id,NickName,PlatformType");

                if (shops.Any())
                {
                    var shopDict = shops.ToDictionary(f => f.Id, f => f);
                    list.ForEach(item =>
                    {
                        if (shopDict.ContainsKey(item.ShopId))
                        {
                            var agentShop = shopDict[item.ShopId];
                            item.AgentInfo = $"{agentShop.NickName}({agentShop.PlatformType})";
                        }
                    });
                }
            }

        }
    }
}