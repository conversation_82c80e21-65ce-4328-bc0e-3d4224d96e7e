using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Data.Repository
{
    /// <summary>
    /// Winform 解绑账号或店铺绑定
    /// </summary>
    public partial class FxUnbindRepository
    {
        private Dictionary<string, List<string>> tbFieldDic = new Dictionary<string, List<string>>();
        private List<string> UseMysqlTbs = new List<string> { "P_PrintOrderProduct", "P_PrintHistoryOrder", "P_PrintHistory" };
        private string _connectionString;//指定业务库连接字符串
        private string _printHistoryConnectionString;//指定打印记录连接字符串
        public FxUnbindRepository()
        {
            InitTableFields();
            var isBackup = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnableBackUp").ToString2().ToLower();
            isEnableBackup = isBackup.IsNullOrEmpty() || isBackup == "1" || isBackup == "true";
        }

        /// <summary>
        /// 指定云平台的解绑（用于迁移）
        /// </summary>
        /// <param name="connectionString">业务库的连接字符串</param>
        /// <param name="printHistoryConnectionString">打印记录的连接字符串</param>
        /// <param name="isBuckUp">是否开启解绑前备份</param>
        public FxUnbindRepository(string connectionString, string printHistoryConnectionString = "", bool isBuckUp = false)
        {
            _connectionString = connectionString;
            _printHistoryConnectionString = printHistoryConnectionString;
            isEnableBackup = isBuckUp;
            InitTableFields();
        }

        private string fileName = "UnBindErrorLog.txt";
        private string dir = "UnBindErrorLog";
        private bool isEnableBackup = true;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopIds"></param>
        /// <param name="isDelFxUser"></param>
        /// <param name="action"></param>
        /// <param name="isFromColdDelete"></param>
        /// <param name="isDeleteOldDbData"></param>
        /// <exception cref="LogicException"></exception>
        public void DelOrderDatas(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action,
            bool isFromColdDelete = false, bool isDeleteOldDbData = false)
        {
            if (shopIds == null || shopIds.Any() == false)
                throw new LogicException("店铺Id不能为空");
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");

            var shops = new ShopRepository().GetShopByIds(shopIds);
            var logicOrderRepository = _connectionString.IsNullOrEmpty() ? new LogicOrderRepository() : new LogicOrderRepository(_connectionString);
            var pathFlowRepository = _connectionString.IsNullOrEmpty() ? new PathFlowRepository() : new PathFlowRepository(_connectionString);
            var db = logicOrderRepository.DbConnection;
            foreach (var sid in shopIds)
            {
                Stopwatch sw0 = new Stopwatch();
                sw0.Start();
                //替换“-”，兼容迁移逻辑
                var pathFlowCodes = pathFlowRepository
                    .GetPathFlowByFxUserIdAndShopId(fxUserId, sid, needAllPathFlowCode: true,
                        isDeleteOldDbData: isDeleteOldDbData)?.Select(x => x.PathFlowCode.Replace("-", string.Empty))
                    .Distinct().ToList();
                sw0.Stop();
                action.Invoke($"订单PathFlowCode数量：{pathFlowCodes.Count}，耗时：{sw0.Elapsed.TotalSeconds}s");
                if (pathFlowCodes.Any() == false)
                    return;

                sw0.Restart();
                var returnSize = 1000; // 根据PathFlowCode单次处理1w单
                var needFullSync = false;
                foreach (var pathcode in pathFlowCodes)
                {
                    Stopwatch sw = new Stopwatch();
                    action.Invoke($"**************** {pathcode} ******************");
                    var index = 0;
                    while (index < 50000) // 同一个PathFlowCode路径上的订单设置上限最多删500w单
                    {
                        var logicOrders = new List<LogicOrder>();
                        var logicOrderIds = new List<string>();

                        for (int i = 1; i <= 3; i++)
                        {
                            sw.Start();
                            try
                            {

                                logicOrders = logicOrderRepository.GetOrderByPathFlowCode(new List<string> { pathcode },
                                    new List<string> { "PlatformOrderId", "LogicOrderId", "ShopId" }, returnSize,
                                    commandTimeout: 300);
                                break;
                            }
                            catch (Exception ex)
                            {
                                sw.Stop();
                                var log =
                                    $"第【{i}】次通过PathFlowCode【{pathcode}】，查询LogicOrder异常：{ex}，\n耗时：{sw.Elapsed.TotalSeconds}s";
                                WriteSqlToLog(log, fileName, dir);
                                action.Invoke(log);
                                sw.Restart();
                                if (i == 3)
                                    throw ex;
                                else
                                    Thread.Sleep(3000);
                            }
                        }
                        logicOrderIds = logicOrders.Select(x => x.LogicOrderId).Distinct().Select(x => $"'{x}'").ToList();
                        sw.Stop();
                        action.Invoke($"根据PathFlowCode【{pathcode}】，查询订单数量：{logicOrderIds.Count}，耗时：{sw.Elapsed.TotalSeconds}s");
                        if (logicOrderIds.Any() == false)
                            break;
                        var pids = logicOrders.Select(x => x.PlatformOrderId).Distinct().Select(x => $"'{x}'").ToList();
                        var sids = logicOrders.Select(x => x.ShopId).Distinct().ToList();

                        #region 删除LogicOrder(先删LogicOrder再删LogicOrderItem，避免删除失败，产生脏数据)

                        var tbName = "LogicOrder";
                        var whereSql = $" logicorderid in ('a',{string.Join(",", logicOrderIds)})";
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);

                        #endregion

                        #region 删除LogicOrderItem

                        tbName = "LogicOrderItem";
                        whereSql = $" logicorderid in ('a',{string.Join(",", logicOrderIds)})";
                        // 先备份再删除数据
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);

                        #endregion

                        #region 删除P_OrderItem

                        tbName = "P_OrderItem";
                        whereSql = $" ShopId in ({string.Join(",", sids)}) AND PlatformOrderId IN('a',{string.Join(",", pids)}) AND UserId={fxUserId}";
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);
                        #endregion

                        #region 删除P_Order

                        tbName = "P_Order";
                        whereSql = $" ShopId in ({string.Join(",", sids)}) AND PlatformOrderId IN('a',{string.Join(",", pids)}) AND UserId={fxUserId}";
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);

                        #endregion

                        if (needFullSync == false)
                            needFullSync = logicOrders.Count > 0;

                        if (logicOrders.Count == 0)
                            break;
                        index++;
                    }
                }

                #region 很久前解綁存在全量同步脏数据情况，这里才补充删除全量同步订单，2025-02-06移除
                //if (!isFromColdDelete)
                //{
                //    #region 补充删除全量同步订单还未拆分还没有创建PathFlowCode的平台单

                //    var sw1 = new Stopwatch();
                //    sw1.Start();

                //    action.Invoke($"===========补充删除全量同步订单===========");
                //    var shop = shops.FirstOrDefault(x => x.Id == sid);
                //    var fullOrders = new List<Order>();
                //    if (shop != null && shop.PlatformType != PlatformType.Virtual.ToString())
                //    {
                //        var sql = $"SELECT PlatformOrderId,ShopId FROM dbo.P_Order (NOLOCK) WHERE ShopId={sid} AND PlatformStatus='waitsellersend' AND UserId={fxUserId}";
                //        fullOrders = db.Query<Order>(sql).ToList();
                //        if (fullOrders != null && fullOrders.Any())
                //        {
                //            var batchSize = 1000;
                //            //var sids = fullOrders.Select(x => x.ShopId).Distinct().ToList();
                //            var count = Math.Ceiling(fullOrders.Count * 1.0 / batchSize);
                //            for (int i = 0; i < count; i++)
                //            {
                //                var loids = new List<string>();
                //                var orders = fullOrders.Skip(i * batchSize).Take(batchSize);
                //                var pids = orders.Select(x => $"'{x.PlatformOrderId}'").Distinct().ToList();
                //                var sids = orders.Select(x => x.ShopId).Distinct().ToList();
                //                for (int j = 1; j <= 3; j++)
                //                {
                //                    try
                //                    {
                //                        var fields = "PlatformOrderId,LogicOrderId,ShopId";
                //                        var logicOrders = logicOrderRepository.GetOrders(pids, sids, fields);
                //                        if (logicOrders != null)
                //                            loids = logicOrders.Select(x => x.LogicOrderId).Distinct().Select(x => $"'{x}'").ToList();
                //                        break;
                //                    }
                //                    catch (Exception ex)
                //                    {
                //                        Log.WriteError($"第[{(j + 1)}]次补充删除全量同步订单：获取LogicOrder异常：{ex}");
                //                        if (i == 3)
                //                            throw ex;
                //                        else
                //                            Thread.Sleep(1000);
                //                    }
                //                }

                //                #region 删除P_OrderItem

                //                var tbName = "P_OrderItem";
                //                var whereSql = $" ShopId={sid} AND PlatformOrderId IN('a',{string.Join(",", pids)}) AND UserId={fxUserId}";
                //                BkThenDelDatas(action, $"全量P_OrderItem", db, tbName, whereSql);
                //                #endregion

                //                #region 删除P_Order

                //                tbName = "P_Order";
                //                whereSql = $" ShopId={sid} AND PlatformOrderId IN('a',{string.Join(",", pids)}) AND UserId={fxUserId}";
                //                BkThenDelDatas(action, $"全量P_Order", db, tbName, whereSql);

                //                #endregion

                //                #region 删除LogicOrderItem

                //                tbName = "LogicOrderItem";
                //                whereSql = $" LogicOrderId IN(select LogicOrderId from LogicOrder (NOLOCK) where ShopId={sid} AND PlatformOrderId IN('a',{string.Join(",", pids)}) AND FxUserId={fxUserId}) ";
                //                BkThenDelDatas(action, $"全量LogicOrderItem", db, tbName, whereSql);
                //                #endregion

                //                #region 删除LogicOrder

                //                tbName = "LogicOrder";
                //                whereSql = $" ShopId={sid} AND PlatformOrderId IN('a',{string.Join(",", pids)}) AND FxUserId={fxUserId}";
                //                BkThenDelDatas(action, $"全量LogicOrder", db, tbName, whereSql);

                //                #endregion


                //                if (loids != null && loids.Any())
                //                {
                //                    #region 删除LogicOrder
                //                    tbName = "LogicOrder";
                //                    whereSql = $" LogicOrderId IN('a',{string.Join(",", loids)})";
                //                    BkThenDelDatas(action, $"全量LogicOrder", db, tbName, whereSql);
                //                    #endregion

                //                    #region 删除LogicOrderItem
                //                    tbName = "LogicOrderItem";
                //                    whereSql = $" LogicOrderId IN('a',{string.Join(",", loids)})";
                //                    BkThenDelDatas(action, $"全量LogicOrderItem", db, tbName, whereSql);
                //                }

                //                #endregion

                //            }

                //        }
                //    }

                //    sw1.Stop();
                //    action.Invoke($"查询（删除）全量同步订单数量：{fullOrders.Count}，耗时：{sw1.Elapsed.TotalSeconds}s");
                //    #endregion

                //    if (needFullSync)
                //    {
                //        new SyncStatusRepository().ResetFullSyncStatus(sid);
                //        action.Invoke($"存在解绑脏数据，已重置店铺【{sid}】全量同步");
                //    }
                //}
                #endregion

                sw0.Stop();
                action.Invoke($"店铺【{sid}】清理订单相关记录总耗时：{sw0.Elapsed.TotalSeconds}s");
            }
        }

        private void DelMySqlPrintOrderProductDatas(Action<string> action, IDbConnection db, string tbName, List<long> pids)
        {
            var whereSql = $" o.PrintHistoryId IN( {string.Join(",", pids)} )";
            Stopwatch sw = new Stopwatch();
            sw.Start();
            var delSql1 = $@"DELETE p FROM P_PrintOrderProduct p
INNER JOIN P_PrintHistoryOrder o ON p.MasterId = o.ID
WHERE {whereSql}";
            try
            {
                if (whereSql.IsNullOrEmpty())
                    throw new Exception("WHERE条件不能为空");
                if (IsUseMysqlDb() && UseMysqlTbs.Any(x => x == tbName))
                {
                    WriteSqlToLog($"DELETE {tbName} ==>{delSql1}", "PrintHistory.txt", "PrintHistory");
                }
                // 删除数据
                db.Execute(delSql1, commandTimeout: 600);
                sw.Stop();
                action.Invoke($"删除{tbName}耗时：{sw.Elapsed.TotalSeconds}s");
            }
            catch (Exception ex)
            {
                sw.Stop();
                var log = $"SQL=>{delSql1}，删除{tbName}异常：{ex}，\n耗时：{sw.Elapsed.TotalSeconds}s";
                WriteSqlToLog(log, fileName, dir);
                action.Invoke(log);
                throw ex;
            }
        }
        private void DelDatas(Action<string> action, IDbConnection db, string pathcode, string tbName, string whereSql)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            var pageSize = 1000;
            var delSql1 = $"DELETE TOP({pageSize}) FROM {tbName} WHERE {whereSql}";
            try
            {
                if (whereSql.IsNullOrEmpty())
                    throw new Exception("WHERE条件不能为空");
                while (true)
                {
                    if (IsUseMysqlDb() && UseMysqlTbs.Any(x => x == tbName))
                    {
                        delSql1 = $"DELETE FROM {tbName} WHERE {whereSql} LIMIT {pageSize}";
                        WriteSqlToLog($"DELETE {tbName} ==>{delSql1}", "PrintHistory.txt", "PrintHistory");
                    }
                    // 删除数据
                    var count = db.Execute(delSql1, commandTimeout: 600);
                    sw.Stop();
                    action.Invoke($"{(pathcode.IsNotNullOrEmpty() ? $"根据PathFlowCode【{pathcode}】，" : "")} 删除{tbName}=>{count}条数据耗时：{sw.Elapsed.TotalSeconds}s");

                    if (count == 0)
                        break;
                }
            }
            catch (Exception ex)
            {
                sw.Stop();
                var log = $"SQL=>{delSql1}，删除{tbName}异常：{ex}，\n耗时：{sw.Elapsed.TotalSeconds}s";
                WriteSqlToLog(log, fileName, dir);
                action.Invoke(log);
                throw ex;
            }
        }

        public bool IsUseMysqlDb()
        {
            return string.IsNullOrEmpty(CustomerConfig.PrintHistoryDefaultMysqlConnectionString) == false;
        }

        private void BkThenDelDatas(Action<string> action, string pathcode, IDbConnection db, string tbName, string whereSql)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            var bkInsertSql = string.Empty;
            try
            {
                if (whereSql.IsNullOrEmpty())
                    throw new Exception("WHERE条件不能为空");
                if (tbFieldDic.ContainsKey(tbName) == false)
                    throw new Exception("指定的表名不能识别");
                var fields = tbFieldDic[tbName].ToJson().ToList<string>();
                // 移除原表 DeleteTime 字段
                if (fields.Contains("[DeleteTime]"))
                    fields.Remove("[DeleteTime]");
                var selectFieldStr = string.Join(",", fields);
                if (fields.Contains("[DeleteTime]") == false)
                    fields.Add("[DeleteTime]");
                var insertFieldStr = string.Join(",", fields);

                //删除前先备份到备份表
                var bkTbName = CreateBkTargetTable(db, tbName);
                if (isEnableBackup)
                {
                    bkInsertSql = $@"INSERT INTO {bkTbName}({insertFieldStr}) SELECT {selectFieldStr},GETDATE() AS DeleteTime FROM {tbName} WHERE {whereSql}";
                    if (IsUseMysqlDb() && UseMysqlTbs.Any(x => x == tbName))
                    {
                        selectFieldStr = selectFieldStr.Replace("[", "").Replace("]", "");
                        insertFieldStr = insertFieldStr.Replace("[", "").Replace("]", "");
                        bkInsertSql = $@"INSERT INTO {bkTbName}({insertFieldStr}) SELECT {selectFieldStr},now() AS 'DeleteTime'  FROM {tbName} WHERE {whereSql}";
                        WriteSqlToLog($"BkInsert ==>{bkInsertSql}", "PrintHistory.txt", "PrintHistory");
                    }

                    //if (IsUseMysqlDb())
                    //{
                    //    if (tbName == "P_PrintOrderProduct")
                    //        bkInsertSql = $@"INSERT INTO {bkTbName}([MasterId], [OrderItemId], [Quantity], [CreateTime], [Id])
                    //        SELECT [MasterId], [OrderItemId], [Quantity], [CreateTime], [Id] FROM {tbName} WHERE {whereSql}";
                    //    else if (tbName == "P_PrintHistoryOrder")
                    //        bkInsertSql = $@"INSERT INTO {bkTbName}([ID], [PrintHistoryId], [OrderId], [CustomerOrderId])
                    //        SELECT [ID], [PrintHistoryId], [OrderId], [CustomerOrderId] FROM {tbName} WHERE {whereSql}";
                    //    else if (tbName == "P_PrintHistory")
                    //        bkInsertSql = $@"INSERT INTO {bkTbName}([ID], [ShopId], [WaybillCodeOrderId], [BuyerMemberId], [BuyerMemberName], [Reciver], [ReciverPhone], [ReciverAddress], [ToProvince], [ToCity], [ToDistrict], [SenderCompany], [Sender], [SenderPhone], [SenderAddress], [FromProvince], [FromCity], [FromDistrict], [ProductCount], [TotalWeight], [TotalPayAomount], [ExpressId], [ExpressName], [TemplateId], [TemplateType], [TemplateName], [SendContent], [PrinterName], [PrintType], [PrintDataType], [PrintDate], [PrinterJsonData], [PlatformOrderId], [PlatformOrderJoin], [ExpressWaybillCode], [ExpressWaybillCodeChild], [IsConfirmed], [CaiNiaoBranchId], [CustomerOrderId], [PrintMethod], [PrintBatchNumber], [UserId], [PrintBatch], [BatchIndex], [FxUserId], [PathFlowCode])
                    //        SELECT [ID], [ShopId], [WaybillCodeOrderId], [BuyerMemberId], [BuyerMemberName], [Reciver], [ReciverPhone], [ReciverAddress], [ToProvince], [ToCity], [ToDistrict], [SenderCompany], [Sender], [SenderPhone], [SenderAddress], [FromProvince], [FromCity], [FromDistrict], [ProductCount], [TotalWeight], [TotalPayAomount], [ExpressId], [ExpressName], [TemplateId], [TemplateType], [TemplateName], [SendContent], [PrinterName], [PrintType], [PrintDataType], [PrintDate], [PrinterJsonData], [PlatformOrderId], [PlatformOrderJoin], [ExpressWaybillCode], [ExpressWaybillCodeChild], [IsConfirmed], [CaiNiaoBranchId], [CustomerOrderId], [PrintMethod], [PrintBatchNumber], [UserId], [PrintBatch], [BatchIndex], [FxUserId], [PathFlowCode] FROM {tbName} WHERE {whereSql}";
                    //    else
                    //        throw new Exception("未知MySql表");
                    //}
                    db.Execute(bkInsertSql, commandTimeout: 600);
                }

                sw.Stop();
                action.Invoke($"{(pathcode.IsNotNullOrEmpty() ? $"根据PathFlowCode【{pathcode}】，" : "")}{(isEnableBackup ? "启用" : "未启用")}备份{tbName}耗时：{sw.Elapsed.TotalSeconds}s");
            }
            catch (Exception ex)
            {
                sw.Stop();
                var log = $"SQL=>{bkInsertSql}，备份{tbName}异常：{ex}，\n耗时：{sw.Elapsed.TotalSeconds}s";
                WriteSqlToLog(log, fileName, dir);
                action.Invoke(log);
                throw ex;
            }
            //删除数据(P_PrintOrderProduct 单独写了删除逻辑)
            if (tbName != "P_PrintOrderProduct")
            {
                for (int i = 0; i < 3; i++)
                {
                    try
                    {
                        DelDatas(action, db, pathcode, tbName, whereSql);
                        break;
                    }
                    catch (Exception ex)
                    {
                        if (i >= 2)
                        {
                            throw ex;
                        }
                        Thread.Sleep((i + 1) * 1000);
                    }
                }
            }
        }

        private string CreateBkTargetTable(IDbConnection db, string tbName)
        {
            // 创建备份表结构
            var bkTbName = "BkUnBind_" + tbName;
            return bkTbName;

            // 其他平台服务器无法通过站点API处理这类SQL，导致大量接口错误，后期创建表结构改为手工创建
            var insertSql = string.Empty;
            try
            {
                insertSql = $@"IF NOT EXISTS ( SELECT TOP 1 1 FROM sysobjects WHERE id = OBJECT_ID(N'{bkTbName}') AND xtype = 'U' )
BEGIN
    SELECT TOP 1 *  INTO {bkTbName} FROM {tbName}
	TRUNCATE TABLE {bkTbName}
    --删除自增列
    ALTER TABLE {bkTbName} DROP COLUMN Id 
    --创建新的ID
    ALTER TABLE {bkTbName} add Id INT
    --创建新的ID
    ALTER TABLE {bkTbName} add DeleteTime DateTime
END";
                if (IsUseMysqlDb() && UseMysqlTbs.Any(x => x == tbName))
                {
                    insertSql = $@"create table if not exists {bkTbName} like {tbName};";
                    WriteSqlToLog($"CreateBkTargetTable==>{insertSql}", "PrintHistory.txt", "PrintHistory");
                }

                var result = db.Execute(insertSql);
            }
            catch (Exception ex)
            {
                Log.WriteError($"CreateBkTargetTable==>{insertSql}，异常信息：{ex}");
                throw ex;
            }
            return bkTbName;
        }

        public void InitTableFields()
        {
            var tbs = new List<string> {
                "P_UserFx", "P_FxUserShop", "P_SyncTask", "P_SyncStatus","P_Shop",
                "PathFlowReference", "PathFlowNode", "PathFlow", "ProductSku", "Product",
                "LogicOrderItem", "LogicOrder", "P_OrderItem", "P_Order",
                "P_WaybillCode", "P_WaybillCodeOrder",
                "P_PrintOrderProduct", "P_PrintHistoryOrder", "P_PrintHistory",
                "P_SendOrderProduct","P_SendHistoryOrder","P_SendHistoryChild", "P_SendHistory",
                "P_FxUserForeignShop"
            };
            foreach (var tb in tbs)
            {
                try
                {
                    var fields = GetTableFileds(tb);
                    if (tbFieldDic.ContainsKey(tb) == false)
                        tbFieldDic.Add(tb, fields);
                }
                catch (Exception ex)
                {
                    Log.Debug($"获取表【{tb}】字段信息错误：{ex}");
                }
            }

            //foreach (var kv in tbFieldDic)
            //{
            //    Log.WriteError($"{kv.Key}=>{string.Join(",", kv.Value)}");
            //}
        }

        public List<string> GetTableFileds(string tbName)
        {
            if (tbName == "ProductSku")
                tbName = "ProductSkuFx";
            if (tbName == "Product")
                tbName = "ProductFx";
            var entityName = $"DianGuanJiaApp.Data.Entity.{tbName.Replace("P_", "")}";

            Assembly assembly = Assembly.Load($"DianGuanJiaApp.Data");
            var obj = assembly.CreateInstance(entityName);
            var type = obj.GetType();

            var fields = new List<string>();
            foreach (var prop in type.GetProperties())
            {
                if (!prop.IsDefined(typeof(NotMappedAttribute), true) &&
                    !prop.IsDefined(typeof(System.ComponentModel.DataAnnotations.Schema.NotMappedAttribute), true) &&
                    //&& prop.Name.ToLower() != "id" 
                    (prop.PropertyType.IsEnum || prop.PropertyType.FullName.ToLower().Contains("dianguanjiaapp") == false))
                {
                    fields.Add($"[{prop.Name}]");
                }
            }
            return fields;
        }

        public void DelProductDatas(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action)
        {
            if (shopIds == null || shopIds.Any() == false)
                throw new LogicException("店铺Id不能为空");
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");

            foreach (var sid in shopIds)
            {
                var logicOrderRepository = _connectionString.IsNullOrEmpty() ? new LogicOrderRepository() : new LogicOrderRepository(_connectionString);
                var db = logicOrderRepository.DbConnection;

                Stopwatch sw0 = new Stopwatch();
                sw0.Start();


                var sql = $"select distinct productcode from Product with(nolock) where SourceUserId IN({fxUserId}) AND ShopId in ({sid})";
                var productCodes = db.Query<string>(sql, commandTimeout: 300).ToList();
                var pagesize = 100;
                var count = Math.Ceiling(productCodes.Count * 1.0 / pagesize);
                for (int i = 0; i < count; i++)
                {
                    Stopwatch sw1 = new Stopwatch();
                    sw1.Start();

                    #region 删除ProductSku
                    var codes = productCodes.Skip(i * pagesize).Take(pagesize).Select(x => $"'{x}'").ToList();
                    var tbName = "ProductSku";
                    var whereSql = $" productcode in ({string.Join(",", codes)})";
                    BkThenDelDatas(action, null, db, tbName, whereSql);
                    //var updateSql = $"UPDATE ProductSku SET ProductCode='-'+ProductCode,SkuCode='-'+SkuCode WHERE {whereSql}";
                    //db.Execute(updateSql, commandTimeout: 600);
                    #endregion

                    #region 删除Product

                    tbName = "Product";
                    //whereSql = $" SourceUserId IN({fxUserId}) AND ShopId in ({sid})";
                    BkThenDelDatas(action, null, db, tbName, whereSql);
                    //updateSql = $"UPDATE Product SET ProductCode='-'+ProductCode,ShopId=-ABS(ShopId),SourceUserId=-ABS(SourceUserId) WHERE {whereSql}";
                    //db.Execute(updateSql, commandTimeout: 600);
                    #endregion

                    #region PathFlow 相关数据清除--注释
                    //#region 删除PathFlowReference

                    //tbName = "PathFlowReference";
                    //whereSql = $" pathflowcode in (select pathflowcode from PathFlow with(nolock) where  SourceFxUserId IN({fxUserId}) AND SourceShopId in ({sid}))";
                    //BkThenDelDatas(action, null, db, tbName, whereSql);

                    //#endregion

                    //#region PathFlowNode

                    //tbName = "PathFlowNode";
                    //whereSql = $" pathflowcode in (select pathflowcode from PathFlow with(nolock) where SourceFxUserId IN({fxUserId}) AND SourceShopId in ({sid}))";
                    //BkThenDelDatas(action, null, db, tbName, whereSql);

                    //#endregion

                    //#region 删除PathFlow

                    //tbName = "PathFlow";
                    //whereSql = $" SourceFxUserId IN({fxUserId}) AND SourceShopId in ({sid})";
                    //BkThenDelDatas(action, null, db, tbName, whereSql);

                    //#endregion 
                    #endregion

                    sw1.Stop();
                    action.Invoke($"店铺【{sid}】清理【{codes.Count}】条商品相关记录耗时：{sw0.Elapsed.TotalSeconds}s");
                }
                sw0.Stop();
                action.Invoke($"店铺【{sid}】清理商品相关记录总耗时：{sw0.Elapsed.TotalSeconds}s");
            }
        }


        /// <summary>
        /// 获取库存货品关系 按用户和店铺ID
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        public List<WareHouseSkuBindRelation> GetWareHouseSkuBindRelations(int fxUserId, int shopId)
        {
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");

            var ownerCode = ("FenDanSystem" + fxUserId).ToShortMd5();

            var repository = _connectionString.IsNullOrEmpty()
                ? new WareHouseSkuBindRelationRepository()
                : new WareHouseSkuBindRelationRepository(_connectionString);

            const string sql =
                "SELECT OwnerCode,SkuBindRelationCode FROM WareHouseSkuBindRelation WHERE OwnerCode = @OwnerCode AND ShopId = @ShopId";
            return repository.DbConnection
                .Query<WareHouseSkuBindRelation>(sql, new { OwnerCode = ownerCode, ShopId = shopId }).ToList();
        }

        /// <summary>
        /// 清理库存货品关系
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopIds"></param>
        /// <param name="action"></param>
        public void UnbindWareHouseSkuBindRelationData(int fxUserId, List<int> shopIds, Action<string> action)
        {
            //判空处理
            if (shopIds == null || shopIds.Any() == false)
                throw new LogicException("店铺Id不能为空");
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");
            var ownerCode = ("FenDanSystem" + fxUserId).ToShortMd5();
            //按店铺ID
            shopIds.ForEach(shopId =>
            {
                var repository = _connectionString.IsNullOrEmpty()
                    ? new WareHouseSkuBindRelationRepository()
                    : new WareHouseSkuBindRelationRepository(_connectionString);
                var db = repository.DbConnection;
                //清理
                var sw0 = new Stopwatch();
                sw0.Start();
                const string sql =
                    "UPDATE WareHouseSkuBindRelation SET Status = -1 WHERE OwnerCode = @OwnerCode AND ShopId = @ShopId";
                db.Execute(sql, new { OwnerCode = ownerCode, ShopId = shopId }, commandTimeout: 600);
                sw0.Stop();
                action.Invoke($"店铺【{shopId}|{fxUserId}】清理库存货品关系相关记录总耗时：{sw0.Elapsed.TotalMilliseconds} ms");
            });
        }

        /// <summary>
        /// 1688代销商品关联数据状态设为0
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopIds"></param>
        /// <param name="isDelFxUser"></param>
        /// <param name="action"></param>
        /// <param name="isSupplier">是否厂家库 true时不再跨库删除其他精选库数据</param>
        /// <exception cref="LogicException"></exception>
        public void DelDistributorProductMapping(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action, bool isSupplier = false)
        {
            if (shopIds == null || shopIds.Any() == false)
                throw new LogicException("店铺Id不能为空");
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");

            foreach (var sid in shopIds)
            {
                var logicOrderRepository = _connectionString.IsNullOrEmpty() ? new LogicOrderRepository() : new LogicOrderRepository(_connectionString);
                var db = logicOrderRepository.DbConnection;

                Stopwatch sw0 = new Stopwatch();
                sw0.Start();

                //1.查出其他精选库
                var dbList = new DbConfigRepository().GetFxAllCloudPlatformBusinessDbConfigs();
                var otherAlibabaDbs = dbList.Where(a => a.DbServer.Location == CloudPlatformType.Alibaba.ToString() && a.DbNameConfig.DbName != db.Database).ToList();

                #region 分批清理，注释掉
                var sql = $"select ProductMappingCode from DistributorProductMapping with(nolock) where DownFxUserId =@fxUserId AND DownShopId = @sid AND Status=1";
                var codes = db.Query<string>(sql,new { fxUserId,sid }, commandTimeout: 300).ToList();
                var pagesize = 100;
                var count = Math.Ceiling(codes.Count * 1.0 / pagesize);
                for (int i = 0; i < count; i++)
                {
                    Stopwatch sw1 = new Stopwatch();
                    sw1.Start();

                    #region 设置状态为0
                    var batchCodes = codes.Skip(i * pagesize).Take(pagesize).Select(x => $"'{x}'").ToList();
                    var whereSql = $" ProductMappingCode in ({string.Join(",", batchCodes)})";

                    //DistributorProductMapping
                    var updateSql = $"UPDATE DistributorProductMapping SET Status=0 WHERE {whereSql} ";
                    db.Execute(updateSql, commandTimeout: 600);

                    //DistributorProductSkuMapping
                    var updateSql2 = $"UPDATE DistributorProductSkuMapping SET Status=0 WHERE {whereSql}";
                    db.Execute(updateSql2, commandTimeout: 600);
                    //1.查出其他精选库+其他拼多多旧库
                    #endregion

                    otherAlibabaDbs = dbList.Where(a => (a.DbServer.Location == CloudPlatformType.Alibaba.ToString() || a.DbServer.Location == CloudPlatformType.Pinduoduo.ToString()) && a.DbNameConfig.DbName != db.Database).ToList();

                    //2.其他精选库也要同步执行
                    //跨库查询、跨云平台查询
                    otherAlibabaDbs.ForEach(oDb =>
                    {
                        var dbLocation = oDb.DbServer.Location;
                        var dbApi = new DbAccessUtility(new ApiDbConfigModel { DbNameConfigId = oDb.DbNameConfig.Id, Location = dbLocation, PlatformType = dbLocation });
                        dbApi.ExecuteScalar(updateSql);
                        dbApi.ExecuteScalar(updateSql2);
                    });

                    sw1.Stop();
                    action.Invoke($"店铺【{sid}】清理【{batchCodes.Count}】条1688代销商品关联相关记录耗时：{sw0.Elapsed.TotalSeconds}s");
                }
                #endregion

                //#region 不分批清
                //var updateSql = $"UPDATE DistributorProductMapping SET Status=0 WHERE DownFxUserId={fxUserId} AND DownShopId={sid} AND Status=1";
                //var updateSql2 = $"UPDATE DistributorProductSkuMapping SET Status=0 WHERE DownFxUserId={fxUserId} AND DownShopId={sid} AND Status=1";
                //db.Execute(updateSql, commandTimeout: 600);
                //db.Execute(updateSql2, commandTimeout: 600);

                //if (isSupplier == false)
                //{
                //    //1.查出其他精选库
                //    var dbList = new DbConfigRepository().GetFxAllCloudPlatformBusinessDbConfigs();
                //    var otherAlibabaDbs = dbList.Where(a => a.DbServer.Location == CloudPlatformType.Alibaba.ToString() && a.DbNameConfig.DbName != db.Database).ToList();

                //    //2.其他精选库也要同步执行
                //    //跨库查询、跨云平台查询
                //    otherAlibabaDbs.ForEach(oDb =>
                //    {
                //        var dbApi = new DbAccessUtility(new ApiDbConfigModel { DbNameConfigId = oDb.DbNameConfig.Id, Location = "Alibaba", PlatformType = "Alibaba" });
                //        dbApi.ExecuteScalar(updateSql + " ; " + updateSql2);

                //        Log.WriteLine($"店铺【{sid}】跨库/跨云清理精选库【{oDb.DbNameConfig.DbName}】1688代销商品关联相关记录：{updateSql};{updateSql2}", $"DelDistributorProductMapping-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                //    });
                //}
                //#endregion

                sw0.Stop();
                action.Invoke($"店铺【{sid}】isSupplier={isSupplier}清理1688代销商品关联相关记录总耗时：{sw0.Elapsed.TotalSeconds}s");
            }
        }

        public void DelWaybillCodeDatas(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action)
        {
            if (shopIds == null || shopIds.Any() == false)
                throw new LogicException("店铺Id不能为空");
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");

            var waybillCodeRepository = new WaybillCodeRepository(_connectionString);
            var pathFlowRepository = new PathFlowRepository(_connectionString);
            var db = waybillCodeRepository.DbConnection;
            foreach (var sid in shopIds)
            {
                Stopwatch sw0 = new Stopwatch();
                sw0.Start();
                var pathFlowCodes = pathFlowRepository.GetPathFlowByFxUserIdAndShopId(fxUserId, sid)?.Select(x => x.PathFlowCode).Distinct().ToList();
                sw0.Stop();
                action.Invoke($"底单记录PathFlowCode数量：{pathFlowCodes.Count}，耗时：{sw0.Elapsed.TotalSeconds}s");
                if (pathFlowCodes.Any() == false)
                    return;

                sw0.Restart();
                var returnSize = 1000; // 根据PathFlowCode单次处理1w单
                foreach (var pathcode in pathFlowCodes)
                {
                    Stopwatch sw = new Stopwatch();
                    action.Invoke($"**************** {pathcode} ******************");
                    var index = 0;
                    while (index < 50000)
                    {
                        sw.Start();
                        var waybillCodes = new List<WaybillCode>();
                        var wids = new List<int>();
                        try
                        {
                            waybillCodes = waybillCodeRepository.GetWaybillCodeByPathFlowCode(new List<string> { pathcode }, new List<string> { "Id" }, returnSize);
                            wids = waybillCodes.Select(x => x.ID).ToList();

                            sw.Stop();
                            action.Invoke($"根据PathFlowCode【{pathcode}】，查询底单记录数量：{wids.Count}，耗时：{sw.Elapsed.TotalSeconds}s");

                            if (waybillCodes.Any() == false)
                                break;
                        }
                        catch (Exception ex)
                        {
                            sw.Stop();
                            var log = $"通过PathFlowCode【{pathcode}】，查询WaybillCode异常：{ex}，\n耗时：{sw.Elapsed.TotalSeconds}s";
                            WriteSqlToLog(log, fileName, dir);
                            action.Invoke(log);
                            throw ex;
                        }

                        #region 删除P_WaybillCodeOrder

                        var tbName = "P_WaybillCodeOrder";
                        var whereSql = $" waybillcodeid in ({string.Join(",", wids)})";
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);

                        #endregion

                        #region 删除P_WaybillCode
                        tbName = "P_WaybillCode";
                        whereSql = $" PathFlowCode='{pathcode}' AND id in ({string.Join(",", wids)})";
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);

                        #endregion

                        if (waybillCodes.Count != returnSize)
                            break;
                        index++;
                    }
                }


                sw0.Stop();
                action.Invoke($"店铺【{sid}】清理底单记录总耗时：{sw0.Elapsed.TotalSeconds}s");
            }
        }

        public void DelPrintHistoryDatas(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action)
        {
            if (shopIds == null || shopIds.Any() == false)
                throw new LogicException("店铺Id不能为空");
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");

            var printHistoryRepository = new PrintHistoryRepository(_printHistoryConnectionString, fxUserId);
            var db = printHistoryRepository.DbConnection;
            var pathFlowRepository = new PathFlowRepository(_connectionString);

            foreach (var sid in shopIds)
            {
                Stopwatch sw0 = new Stopwatch();
                sw0.Start();
                var pathFlowCodes = pathFlowRepository.GetPathFlowByFxUserIdAndShopId(fxUserId, sid)?.Select(x => x.PathFlowCode).Distinct().ToList();
                sw0.Stop();
                action.Invoke($"打印记录PathFlowCode数量：{pathFlowCodes.Count}，耗时：{sw0.Elapsed.TotalSeconds}s");
                if (pathFlowCodes.Any() == false)
                    return;

                sw0.Restart();

                var returnSize = 1000; // 根据PathFlowCode单次处理1w单
                foreach (var pathcode in pathFlowCodes)
                {
                    Stopwatch sw = new Stopwatch();
                    action.Invoke($"**************** {pathcode} ******************");
                    var index = 0;
                    while (index < 50000) // 同一个PathFlowCode路径上的订单设置上限最多删500w单
                    {
                        sw.Start();
                        var printHistorys = new List<PrintHistory>();
                        var pids = new List<long>();
                        try
                        {
                            printHistorys = printHistoryRepository.GetPrintHistoryByPathFlowCode(new List<string> { pathcode }, new List<string> { "Id" }, returnSize);
                            pids = printHistorys.Select(x => x.ID).Distinct().ToList();
                            sw.Stop();
                            action.Invoke($"根据PathFlowCode【{pathcode}】，查询打印记录数量：{pids.Count}，耗时：{sw.Elapsed.TotalSeconds}s");
                            if (pids.Any() == false)
                                break;
                        }
                        catch (Exception ex)
                        {
                            sw.Stop();
                            var log = $"通过PathFlowCode【{pathcode}】，查询PrintHistory异常：{ex}，\n耗时：{sw.Elapsed.TotalSeconds}s";
                            WriteSqlToLog(log, fileName, dir);
                            action.Invoke(log);
                            throw ex;
                        }

                        #region 删除P_PrintOrderProduct

                        var tbName = "P_PrintOrderProduct";
                        var whereSql = $@" masterid in (select ID from P_PrintHistoryOrder where printhistoryid in ({string.Join(",", pids)}))";
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);
                        // MySql Delet FROM IN 删除太慢，单独改造
                        DelMySqlPrintOrderProductDatas(action, db, tbName, pids);
                        #endregion

                        #region 删除P_PrintHistoryOrder

                        tbName = "P_PrintHistoryOrder";
                        whereSql = $@" printhistoryid in ({string.Join(",", pids)})";
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);

                        #endregion

                        //#region 删除P_PrintHistoryData

                        //tbName = "P_PrintHistoryData";
                        //whereSql = $@" shopid in (SELECT ShopId from P_PrintHistory where id in ({string.Join(",", pids)}))";
                        //BkThenDelDatas(action, pathcode, db, tbName, whereSql);

                        //#endregion

                        #region 删除P_PrintHistory

                        tbName = "P_PrintHistory";
                        whereSql = $@" PathFlowCode='{pathcode}' AND id in ({string.Join(",", pids)})";
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);

                        #endregion

                        if (printHistorys.Count != returnSize)
                            break;
                        index++;
                    }
                }

                sw0.Stop();
                action.Invoke($"店铺【{sid}】清理打印记录总耗时：{sw0.Elapsed.TotalSeconds}s");
            }
        }

        public void DelSendHistoryDatas(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action)
        {
            if (shopIds == null || shopIds.Any() == false)
                throw new LogicException("店铺Id不能为空");
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");

            var sendHistoryRepository = new SendHistoryRepository(_connectionString);
            var db = sendHistoryRepository.DbConnection;
            var pathFlowRepository = new PathFlowRepository(_connectionString);
            foreach (var sid in shopIds)
            {
                Stopwatch sw0 = new Stopwatch();
                sw0.Start();
                var pathFlowCodes = pathFlowRepository.GetPathFlowByFxUserIdAndShopId(fxUserId, sid)?.Select(x => x.PathFlowCode).Distinct().ToList();
                sw0.Stop();
                action.Invoke($"发货记录PathFlowCode数量：{pathFlowCodes.Count}，耗时：{sw0.Elapsed.TotalSeconds}s");
                if (pathFlowCodes.Any() == false)
                    return;

                sw0.Restart();

                var returnSize = 1000; // 根据PathFlowCode单次处理1w单
                foreach (var pathcode in pathFlowCodes)
                {
                    Stopwatch sw = new Stopwatch();
                    action.Invoke($"**************** {pathcode} ******************");
                    var index = 0;
                    while (index < 50000) // 同一个PathFlowCode路径上的订单设置上限最多删500w单
                    {
                        sw.Start();
                        var sendHistorys = new List<SendHistory>();
                        var sids = new List<int>();
                        try
                        {
                            sendHistorys = sendHistoryRepository.GetSendHistoryByPathFlowCode(new List<string> { pathcode }, new List<string> { "Id" }, returnSize);
                            sids = sendHistorys.Select(x => x.ID).Distinct().ToList();
                            sw.Stop();
                            action.Invoke($"根据PathFlowCode【{pathcode}】，查询发货记录数量：{sids.Count}，耗时：{sw.Elapsed.TotalSeconds}s");
                            if (sendHistorys.Any() == false)
                                break;
                        }
                        catch (Exception ex)
                        {
                            sw.Stop();
                            var log = $"通过PathFlowCode【{pathcode}】，查询SendHistory异常：{ex}，\n耗时：{sw.Elapsed.TotalSeconds}s";
                            WriteSqlToLog(log, fileName, dir);
                            action.Invoke(log);
                            throw ex;
                        }

                        #region 删除P_SendOrderProduct

                        var tbName = "P_SendOrderProduct";
                        var whereSql = $" MasterId in (SELECT Id FROM P_SendHistoryOrder WHERE SendHistoryId IN( {string.Join(",", sids)}))";
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);

                        #endregion

                        #region 删除P_SendHistoryChild

                        tbName = "P_SendHistoryChild";
                        whereSql = $" SendHistoryId in ({string.Join(",", sids)})";
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);

                        #endregion

                        #region 删除P_SendHistoryOrder

                        tbName = "P_SendHistoryOrder";
                        whereSql = $" SendHistoryId in ({string.Join(",", sids)})";
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);

                        #endregion

                        #region 删除P_SendHistory

                        tbName = "P_SendHistory";
                        whereSql = $" PathFlowCode='{pathcode}' AND Id in ({string.Join(",", sids)})";
                        BkThenDelDatas(action, pathcode, db, tbName, whereSql);

                        #endregion

                        if (sendHistorys.Count != returnSize)
                            break;
                        index++;
                    }
                }

                sw0.Stop();
                action.Invoke($"店铺【{sid}】清理发货记录总耗时：{sw0.Elapsed.TotalSeconds}s");
            }
        }

        public void DelBindShopDatas(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action)
        {
            if (shopIds == null || shopIds.Any() == false)
                throw new LogicException("店铺Id不能为空");
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");

            var fxUserShopRepository = new FxUserShopRepository();
            var db = fxUserShopRepository.DbConnection;

            #region 删除P_SyncTask
            var tbName = "P_SyncTask";
            var whereSql = $" FxUserId={fxUserId} AND ShopId IN ({string.Join(",", shopIds)}) AND Source='FenDanSystem'";
            BkThenDelDatas(action, null, db, tbName, whereSql);
            //var updateSql = $"UPDATE P_SyncTask SET ShopId='-'+ABS(ShopId),FxUserId='-'+ABS(FxUserId) WHERE {whereSql}";
            //db.Execute(updateSql, commandTimeout: 600);
            #endregion

            #region 删除P_SyncStatus
            tbName = "P_SyncStatus";
            whereSql = $" FxUserId={fxUserId} AND ShopId IN ({string.Join(",", shopIds)}) AND Source='FenDanSystem'";
            BkThenDelDatas(action, null, db, tbName, whereSql);
            //updateSql = $"UPDATE P_SyncStatus SET ShopId='-'+ABS(ShopId),FxUserId='-'+ABS(FxUserId) WHERE {whereSql}";
            //db.Execute(updateSql, commandTimeout: 600);
            #endregion

            #region 删除 P_Shop (Virtual)
            tbName = "P_Shop";
            whereSql = $" Id IN(SELECT ShopId FROM P_FxUserShop (NOLOCK) WHERE FxUserId={fxUserId} AND ShopId IN ({string.Join(",", shopIds)}) AND PlatformType='Virtual' )";
            //BkThenDelDatas(action, null, db, tbName, whereSql);
            var updateSql = $"UPDATE P_Shop SET ShopId=ShopId+'_Unbind',ShopName=ShopName+'_Unbind',NickName=NickName+'_Unbind' WHERE {whereSql}";
            db.Execute(updateSql);
            //缓存性能优化:删除原来缓存，实时更新
            FxCaching.RefeshCache(FxCachingType.FxShopSelf, fxUserId);
            #endregion

            #region 删除P_FxUserShop

            tbName = "P_FxUserShop";
            whereSql = $" FxUserId={fxUserId} AND PlatformType!='System' AND ShopId IN({string.Join(",", shopIds)})";
            BkThenDelDatas(action, null, db, tbName, whereSql);
            //updateSql = $"UPDATE P_FxUserShop SET ShopId=-ABS(ShopId),FxUserId=-ABS(FxUserId) WHERE {whereSql}";
            //db.Execute(updateSql, commandTimeout: 600);
            #endregion

            #region 删除P_FxUserForeignShop

            tbName = "P_FxUserForeignShop";
            whereSql = $" FxUserId={fxUserId} AND PlatformType!='System' AND ShopId IN({string.Join(",", shopIds)})";
            BkThenDelDatas(action, null, db, tbName, whereSql);
            //updateSql = $"UPDATE P_FxUserShop SET ShopId=-ABS(ShopId),FxUserId=-ABS(FxUserId) WHERE {whereSql}";
            //db.Execute(updateSql, commandTimeout: 600);
            #endregion
        }

        public void DelUserDatas(int fxUserId, bool isDelFxUser, Action<string> action)
        {
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");

            var fxUserShopRepository = new FxUserShopRepository();
            var db = fxUserShopRepository.DbConnection;

            #region 删除 P_Shop (System)
            var tbName = "P_Shop";
            var whereSql = $" Id IN(SELECT ShopId FROM P_FxUserShop WITH(NOLOCK) WHERE FxUserId={fxUserId} AND PlatformType='System')";
            //BkThenDelDatas(action, null, db, tbName, whereSql);
            var updateSql = $"UPDATE P_Shop SET ShopId = ShopName +'-删除' WHERE {whereSql}"; // Syystem店铺ShopName、ShopId是一样的
            db.Execute(updateSql, commandTimeout: 600);
            #endregion

            #region 软删除 P_BranchShareRelation，P_CainiaoAuthOwner
            DelBranchShareRelationData(fxUserId, action);
            #endregion

            #region 删除P_FxWeChatUserInfo（微信绑定，表数据只有在阿里这边）
            if (CustomerConfig.CloudPlatformType == PlatformType.Alibaba.ToString())
            {
                tbName = "P_FxWeChatUserInfo";
                whereSql = $" FxUserId={fxUserId} AND UnBind = 0";
                //BkThenDelDatas(action, null, db, tbName, whereSql);
                updateSql = $"UPDATE {tbName} SET UnBind = 1 WHERE {whereSql}";
                db.Execute(updateSql, commandTimeout: 600);
            }
            #endregion

            #region 删除P_FxUserShop
            tbName = "P_FxUserShop";
            whereSql = $" FxUserId={fxUserId} AND PlatformType='System'";
            BkThenDelDatas(action, null, db, tbName, whereSql);
            //updateSql = $"UPDATE P_FxUserShop SET ShopId=-ABS(ShopId),FxUserId=-ABS(FxUserId) WHERE {whereSql}";
            //db.Execute(updateSql, commandTimeout: 600);
            #endregion

            #region 删除P_UserFx
            tbName = "P_UserFx";
            whereSql = $" Id={fxUserId}";
            BkThenDelDatas(action, null, db, tbName, whereSql);
            //updateSql = $"UPDATE P_UserFx SET Mobile='-'+Mobile WHERE {whereSql}";
            //db.Execute(updateSql, commandTimeout: 600);
            #endregion

            #region 删除职位 PostFx
            new PostFxRepository().DeleteByCreateUserId(fxUserId);
            #endregion

            #region 删除用户-职位关联 UserFxPostRelation
            new UserFxPostRelationRepository().DeleteByCreateUserId(fxUserId);
            #endregion

            #region 删除子账号P_SubFxUser
            new SubUserFxRepository().DeleteByCreateUserId(fxUserId);
            #endregion
        }
        /// <summary>
        /// 软删除共享面单，菜鸟授权相关
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="action"></param>
        /// <exception cref="LogicException"></exception>
        public void DelBranchShareRelationData(int fxUserId, Action<string> action)
        {
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");
            var repository = new BranchShareRelationRepository();
            var db = repository.DbConnection;
            const string sqlBySystemShopId = "SELECT ShopId FROM P_FxUserShop WHERE FxUserId=@FxUserId AND PlatformType='System'";
            var sql =
                $"UPDATE P_BranchShareRelation SET Status='Deleted' WHERE FromId IN({sqlBySystemShopId});";
            db.Execute(sql, new { FxUserId = fxUserId }, commandTimeout: 600);

            sql = $"UPDATE P_BranchShareRelation SET Status='Deleted' WHERE ToId IN({sqlBySystemShopId});";
            db.Execute(sql, new { FxUserId = fxUserId }, commandTimeout: 600);

            sql = $"UPDATE P_CainiaoAuthOwner set IsDeleted = 1 where ShopId IN({sqlBySystemShopId});";
            db.Execute(sql, new { FxUserId = fxUserId }, commandTimeout: 600);
            // 清理缓存
            var caiNiaoAuthInfoIds=  db.Query<int>($"SELECT CaiNiaoAuthInfoId FROM P_CainiaoAuthOwner WITH(NOLOCK) WHERE ShopId IN({sqlBySystemShopId});", new { FxUserId = fxUserId });
            if (caiNiaoAuthInfoIds != null)
            {
                foreach (var id in caiNiaoAuthInfoIds)
                {
                    db.DeleteQueryCache<CainiaoAuthOwner>(id.ToString());
                }
            }
        }

        public string WriteSqlToLog(string txt, string fileName, string dir = "", string method = "")
        {
            try
            {
                if (txt.IsNullOrEmpty())
                    return txt;
                if (fileName.IsNullOrEmpty())
                    throw new LogicException("文件名不能为空");

                txt += "\r\n-----------------------------------------------------\r\n";
                var path = dir.IsNullOrEmpty() ? $"{AppDomain.CurrentDomain.BaseDirectory}log\\{fileName}" :
                    $"{AppDomain.CurrentDomain.BaseDirectory}log\\{dir}\\{fileName}";

                txt.WriteToFile(ref path);
            }
            catch (Exception ex)
            {
                Log.WriteError($"{method} ==> 写入文件失败：{ex}");
            }
            return txt;
        }

        public void UpdateUnbindHistory(int fxUserId, string mobile, int shopId, UnbindType unbindType, bool isDeleteUser, string exception)
        {
            var logicOrderRepository = _connectionString.IsNullOrEmpty() ? new LogicOrderRepository() : new LogicOrderRepository(_connectionString);
            var sql = $"INSERT INTO dbo.UbindHistory( FxUserId ,Mobile,ShopId ,UnbindType ,IsDeleteUser,CreateTime,Exception) VALUES  ({fxUserId},@mobile,{shopId},{unbindType.ToInt()},{(isDeleteUser ? 1 : 0)},GetDate(),@exception)";
            logicOrderRepository.DbConnection.Execute(sql, new { mobile, exception });
        }

        public void SwitchFxUser(UserFx oldUserFx, UserFx newUserFx, bool isSwitchBack = false, Action<string> action = null)
        {
            //-------------检查基本信息--------------
            if (oldUserFx == null || oldUserFx.Mobile.IsNullOrEmpty())
            {
                if (action == null)
                    throw new LogicException("旧账号不能为空");
                else
                    action("旧账号不能为空");
                return;
            }
            if (newUserFx == null || newUserFx.Mobile.IsNullOrEmpty())
            {
                if (action == null)
                    throw new LogicException("新账号不能为空");
                else
                    action("新账号不能为空");
                return;
            }

            var mobiles = new List<string> { oldUserFx.Mobile, newUserFx.Mobile };
            var shopRepository = new ShopRepository();
            var fxUserShopRepository = new FxUserShopRepository();
            //取P_UserFx P_FxUserShop P_Shop 表数据
            var fxUserShops = fxUserShopRepository.GetFxUserShops("System", mobiles, "Id,ShopId,NickName,PlatformType");
            var systemShopIds = fxUserShops?.Select(f => f.ShopId)?.ToList();
            var shops = shopRepository.GetShopByIds(systemShopIds, "Id,ShopId,PlatformType,ShopName,NickName");

            var oldFxUserShop = fxUserShops?.FirstOrDefault(s => s.NickName == oldUserFx.Mobile);
            var oldShop = shops?.FirstOrDefault(s => s.Id == oldFxUserShop?.ShopId);
            if (oldShop == null || oldFxUserShop == null)
            {
                if (action == null)
                    throw new LogicException("旧账号缺少店铺信息");
                else
                    action("旧账号缺少店铺信息");
                return;
            }

            //-------------开始更换账号--------------
            var oldPassword = oldUserFx.Password;
            var newPassword = oldUserFx.Password; // 默认旧账号，新账号存在使用新账号密码
            var switchOldMobile = $"{oldUserFx.Mobile}_改_{newUserFx.Mobile}";
            var newMobile = newUserFx.Mobile;
            var oldMobile = oldUserFx.Mobile;
            if (isSwitchBack)
                switchOldMobile = oldMobile.SplitToList("_").First();

            #region 新逻辑
            var _backNewSql = string.Empty;
            if (newUserFx.Id > 0)
            {
                newPassword = newUserFx.Password;
                var newFxUserShop = fxUserShops?.FirstOrDefault(s => s.NickName == newUserFx.Mobile);
                var newShop = shops?.FirstOrDefault(s => s.Id == newFxUserShop?.ShopId);
                if (newShop == null || newFxUserShop == null)
                {
                    if (action == null)
                        throw new LogicException("新账号缺少店铺信息");
                    else
                        action("新账号缺少店铺信息");
                    return;
                }

                //--新账号注销
                var _newSql = $@"
UPDATE dbo.P_UserFx SET Mobile='{switchOldMobile}',Password='{oldPassword}' WHERE Id={newUserFx.Id};
UPDATE dbo.P_FxUserShop SET NickName='{switchOldMobile}' WHERE Id={newFxUserShop.Id};
UPDATE dbo.P_Shop SET ShopName='{switchOldMobile}',NickName='{switchOldMobile}',ShopId='{switchOldMobile}' WHERE Id={newShop.Id};
";
                //--恢复脚本
                _backNewSql = $@"
UPDATE dbo.P_UserFx SET Mobile='{newUserFx.Mobile}',Password='{newUserFx.Password}' WHERE Id={newUserFx.Id};
UPDATE dbo.P_FxUserShop SET NickName='{newFxUserShop.NickName}' WHERE Id={newFxUserShop.Id};
UPDATE dbo.P_Shop SET ShopName='{newShop.ShopName}',NickName='{newShop.NickName}',ShopId='{newShop.ShopId}' WHERE Id={newShop.Id};
";
                try
                {
                    var row = fxUserShopRepository.DbConnection.Execute(_newSql, commandTimeout: 600);
                    if (row < 3)
                    {
                        fxUserShopRepository.DbConnection.Execute(_backNewSql, commandTimeout: 600);
                        if (action == null)
                            throw new LogicException("新账号注销操作失败，已回滚操作");
                        else
                            action("新账号注销操作失败，已回滚操作");
                        return;
                    }
                }
                catch (Exception ex)
                {
                    var log = $"切换账号：{oldUserFx.Mobile}=> {newUserFx.Mobile}，_newSql：{_newSql}，_backNewSql：{_backNewSql}，新账号注销异常：{ex}";
                    WriteSqlToLog(log, fileName, dir);
                    throw ex;
                }
            }
            //--旧账号改成新账号
            var _oldSql = $@"
UPDATE dbo.P_UserFx SET Mobile='{newMobile}',Password='{newPassword}' WHERE Id={oldUserFx.Id};
UPDATE dbo.P_FxUserShop SET NickName='{newMobile}' WHERE Id={oldFxUserShop.Id};
UPDATE dbo.P_Shop SET ShopName='{newMobile}',NickName='{newMobile}',ShopId='{newMobile}' WHERE Id={oldShop.Id};
";
            //--恢复脚本
            var _backOldSql = $@"
UPDATE dbo.P_UserFx SET Mobile='{oldUserFx.Mobile}',Password='{oldUserFx.Password}' WHERE Id={oldUserFx.Id};
UPDATE dbo.P_FxUserShop SET NickName='{oldFxUserShop.NickName}' WHERE Id={oldFxUserShop.Id};
UPDATE dbo.P_Shop SET ShopName='{oldShop.ShopName}',NickName='{oldShop.NickName}',ShopId='{oldShop.ShopId}' WHERE Id={oldShop.Id};
";
            try
            {
                var row = fxUserShopRepository.DbConnection.Execute(_oldSql, commandTimeout: 600);
                if (row < 3)
                {
                    try
                    {
                        if (_backNewSql.IsNotNullOrEmpty())
                            fxUserShopRepository.DbConnection.Execute(_backNewSql, commandTimeout: 600);
                        fxUserShopRepository.DbConnection.Execute(_backOldSql, commandTimeout: 600);
                    }
                    catch (Exception ex)
                    {
                        var log = $"执行账号回滚操作失败：{(_backNewSql.IsNotNullOrEmpty() ? $"_backNewSql：{_backNewSql}，" : "")}_backOldSql：{_backOldSql}，异常信息：{ex}";
                        WriteSqlToLog(log, fileName, dir);
                    }
                    if (action == null)
                        throw new LogicException("旧账号改成新账号操作失败，已回滚操作");
                    else
                        action("旧账号改成新账号操作失败，已回滚操作");
                    return;
                }
            }
            catch (Exception ex)
            {
                var log = $"切换账号：{oldUserFx.Mobile}=> {newUserFx.Mobile}，_oldSql：{_oldSql}，{(_backNewSql.IsNotNullOrEmpty() ? $"_backNewSql：{_backNewSql}，" : "")}_backOldSql：{_backOldSql}，旧账号改成新账号异常：{ex}";
                WriteSqlToLog(log, fileName, dir);
                throw ex;
            }
            #endregion

            //缓存性能优化:直接清除缓存--再次使用时刷新
            FxCaching.RefeshCache(FxCachingType.FxUser, oldUserFx.Id);
            FxCaching.RefeshCache(FxCachingType.SystemShops, oldUserFx.Id);
            FxCaching.RefeshCache(FxCachingType.FxShopSelf, oldUserFx.Id);

            #region 旧逻辑
            //            var sql = $@"
            //--旧账号改成新账号
            //UPDATE dbo.P_UserFx SET Mobile=@newMobile,Password=@newPassword WHERE Mobile=@oldMobile;
            //UPDATE dbo.P_FxUserShop SET NickName=@newMobile WHERE PlatformType='System' AND NickName=@oldMobile;
            //UPDATE dbo.P_Shop SET ShopName=@newMobile,NickName=@newMobile,ShopId=@newMobile WHERE PlatformType='System' AND ShopId=@oldMobile;";
            //            if (newUserFx.Id > 0)
            //            {
            //                newPassword = newUserFx.Password;
            //                // 新账号存在， 先注销新账号再更新旧账号
            //                sql = $@"
            //--新账号注销
            //UPDATE dbo.P_UserFx SET Mobile=@switchOldMobile,Password=@oldPassword WHERE Mobile=@newMobile;
            //UPDATE dbo.P_FxUserShop SET NickName=@switchOldMobile WHERE PlatformType='System' AND NickName=@newMobile;
            //UPDATE dbo.P_Shop SET ShopName=@switchOldMobile,NickName=@switchOldMobile,ShopId=@switchOldMobile WHERE PlatformType='System' AND ShopId=@newMobile;
            //---------------------------------------
            //{sql}";
            //            } 

            //try
            //{
            //    fxUserShopRepository.DbConnection.Execute(sql,
            //        new 
            //        { 
            //            switchOldMobile= switchOldMobile,
            //            newPassword= newPassword,
            //            oldPassword = oldPassword,
            //            newMobile = newMobile,
            //            oldMobile = oldMobile
            //        }, 
            //        commandTimeout: 600 );

            //    //缓存性能优化:直接清除缓存--再次使用时刷新
            //    FxCaching.RefeshCache(FxCachingType.FxUser, oldUserFx.Id);
            //    FxCaching.RefeshCache(FxCachingType.SystemShops, oldUserFx.Id);
            //    FxCaching.RefeshCache(FxCachingType.FxShopSelf, oldUserFx.Id);
            //}
            //catch (Exception ex)
            //{
            //    var log = $"切换账号：{oldUserFx.Mobile}=> {newUserFx.Mobile}，修改SQL：{executeSqls.ToJson()}，恢复SQL：{restoreSqls.ToJson()}，异常：{ex}";
            //    WriteSqlToLog(log, fileName, dir);
            //    throw ex;
            //}
            #endregion
        }

        public void ClearNewFxUserAgentAndSupplier(UserFx newUserFx, Action<string> action = null)
        {
            var rp = new SupplierUserRepository();
            var sql = $"UPDATE dbo.P_SupplierUser SET FxUserId=-ABS(FxUserId),SupplierFxUserId=-ABS(SupplierFxUserId),CreateBy=-ABS(CreateBy) WHERE FxUserId={newUserFx.Id} OR SupplierFxUserId={newUserFx.Id}";
            rp.DbConnection.Execute(sql);
            rp.DeleteCacheByFxUserId(newUserFx.Id);
        }

 

        /// <summary>
        /// 直接修改手机号
        /// </summary>
        /// <param name="oldMobile">原手机号</param>
        /// <param name="newMobile">新手机号</param>
        /// <param name="newPassword">密码</param>
        public void BindMobile(string oldMobile, string newMobile, string newPassword)
        {
            var sql = $@"
DECLARE @newMobile VARCHAR(32)='{newMobile}',@oldMobile VARCHAR(32)='{oldMobile}',@newPassword VARCHAR(64)='{newPassword}'
UPDATE dbo.P_UserFx SET Mobile=@newMobile,Password=@newPassword WHERE Mobile=@oldMobile;
UPDATE dbo.P_FxUserShop SET NickName=@newMobile WHERE PlatformType='System' AND NickName=@oldMobile;
UPDATE dbo.P_Shop SET ShopName=@newMobile,NickName=@newMobile,ShopId=@newMobile WHERE PlatformType='System' AND NickName=@oldMobile;";

            var fxUserShopRepository = new FxUserShopRepository();
            try
            {
                fxUserShopRepository.DbConnection.Execute(sql, commandTimeout: 600);

                //缓存性能优化:更新FxUser缓存
                var user = new UserFxRepository().GetByMobile(newMobile);
                FxCaching.RefeshCache(FxCachingType.FxUser, user.Id.ToString(), user);
                FxCaching.RefeshCache(FxCachingType.SystemShops, user.Id);
                FxCaching.RefeshCache(FxCachingType.FxShopSelf, user.Id);
            }
            catch (Exception ex)
            {
                var log = $"SQL => {sql}，绑定手机号：{oldMobile}=> {newMobile}，异常：{ex}";
                WriteSqlToLog(log, fileName, dir);
                throw ex;
            }
        }

        /// <summary>
        /// 登录并绑定手机号
        /// </summary>
        /// <param name="oldFxUserId"></param>
        /// <param name="newFxUserId"></param>
        public void UpdateBindMobile(int oldFxUserId, int newFxUserId)
        {
            var sql = $@"
UPDATE dbo.P_UserFx SET Status=0,Mobile=Mobile+'-作废' WHERE Id={oldFxUserId};
UPDATE dbo.P_FxUserShop SET FxUserId={newFxUserId} WHERE FxUserId={oldFxUserId};";

            Log.Debug(sql);
            var fxUserShopRepository = new FxUserShopRepository();
            try
            {
                fxUserShopRepository.DbConnection.Execute(sql, commandTimeout: 600);
                //缓存性能优化:直接清除缓存--再次使用时刷新
                FxCaching.RefeshCache(FxCachingType.FxUser, oldFxUserId);
                FxCaching.RefeshCache(FxCachingType.FxShopSelf, oldFxUserId);
                FxCaching.RefeshCache(FxCachingType.SystemShops, oldFxUserId);

            }
            catch (Exception ex)
            {
                var log = $"SQL => {sql}，登录并绑定手机号：{oldFxUserId}=> {newFxUserId}，异常：{ex}";
                WriteSqlToLog(log, fileName, dir);
                throw ex;
            }
        }

        #region 恢复解绑店铺数据

        public void Resume(int fxUserId, int sid, Action<string> action)
        {
            var pageSize = 10000;
            var pageIndex = 1;
            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            var tbName = "P_Order";
            var bkTbName = $"dbo.BkUnBind_{tbName}";
            var fieldStr = string.Join(",", tbFieldDic[tbName].Where(x => x.ToLower() != "[id]"));
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();
                var sql = $@"
--SET IDENTITY_INSERT dbo.P_Order ON 
INSERT INTO P_Order({fieldStr}) 
SELECT {fieldStr} FROM {bkTbName} WHERE  UserId={fxUserId} AND ShopId={sid}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
--SET IDENTITY_INSERT dbo.P_Order OFF";
                try
                {
                    logicOrderRepository.DbConnection.Execute(sql, commandTimeout: 600);
                    sql = $@"
        SELECT COUNT(0) FROM (
			SELECT ID
			FROM {bkTbName} WHERE UserId={fxUserId} AND ShopId={sid}
			ORDER BY Id
			OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
		) AS t";

                    var count = logicOrderRepository.DbConnection.Query<int>(sql, commandTimeout: 600).FirstOrDefault();
                    sw.Stop();
                    action($"恢复第{pageIndex}页 {count}/{pageSize}条{tbName}数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    if (count == 0)
                        break;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"SQL=>{sql},\n异常信息：{ex}");
                    throw ex;
                }

                pageIndex++;
            }


            pageIndex = 1;
            tbName = "P_OrderItem";
            bkTbName = $"BkUnBind_{tbName}";
            fieldStr = string.Join(",", tbFieldDic[tbName].Where(x => x.ToLower() != "[id]"));
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();
                var sql = $@"
--SET IDENTITY_INSERT dbo.P_OrderItem ON 
INSERT INTO P_OrderItem({fieldStr}) SELECT {fieldStr} FROM {bkTbName} WHERE UserId={fxUserId} AND ShopId={sid}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
--SET IDENTITY_INSERT dbo.P_OrderItem OFF";
                try
                {
                    logicOrderRepository.DbConnection.Execute(sql, commandTimeout: 600);
                    sql = $@"
        SELECT COUNT(0) FROM (
			SELECT ID
			FROM {bkTbName} WHERE UserId={fxUserId} AND ShopId={sid}
			ORDER BY Id
			OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
		) AS t";

                    var count = logicOrderRepository.DbConnection.Query<int>(sql, commandTimeout: 600).FirstOrDefault();
                    sw.Stop();
                    action($"恢复第{pageIndex}页 {count}/{pageSize}条{tbName}数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    if (count == 0)
                        break;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"SQL=>{sql},\n异常信息：{ex}");
                    throw ex;
                }

                pageIndex++;
            }

            pageIndex = 1;
            tbName = "LogicOrder";
            bkTbName = $"BkUnBind_{tbName}";
            fieldStr = string.Join(",", tbFieldDic[tbName].Where(x => x.ToLower() != "[id]"));
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();
                var sql = $@"
--SET IDENTITY_INSERT dbo.LogicOrder ON 
INSERT INTO LogicOrder({fieldStr}) SELECT {fieldStr} FROM {bkTbName} WHERE  FxUserId={fxUserId} AND ShopId={sid}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
--SET IDENTITY_INSERT dbo.LogicOrder OFF";
                try
                {
                    logicOrderRepository.DbConnection.Execute(sql, commandTimeout: 600);
                    sql = $@"
        SELECT COUNT(0) FROM (
			SELECT ID
			FROM {bkTbName} WHERE  FxUserId={fxUserId} AND ShopId={sid}
			ORDER BY Id
			OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
		) AS t";

                    var count = logicOrderRepository.DbConnection.Query<int>(sql, commandTimeout: 600).FirstOrDefault();
                    sw.Stop();
                    action($"恢复第{pageIndex}页 {count}/{pageSize}条{tbName}数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    if (count == 0)
                        break;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"SQL=>{sql},\n异常信息：{ex}");
                    throw ex;
                }

                pageIndex++;
            }

            pageIndex = 1;
            tbName = "LogicOrderItem";
            bkTbName = $"BkUnBind_{tbName}";
            fieldStr = string.Join(",", tbFieldDic[tbName].Where(x => x.ToLower() != "[id]"));
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();
                var sql = $@"
--SET IDENTITY_INSERT dbo.LogicOrderItem ON 
INSERT INTO LogicOrderItem({fieldStr}) SELECT {fieldStr} FROM {bkTbName} WHERE ShopId={sid}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
--SET IDENTITY_INSERT dbo.LogicOrderItem OFF

SELECT Id FROM {bkTbName} WHERE ShopId={sid}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";
                try
                {
                    logicOrderRepository.DbConnection.Execute(sql, commandTimeout: 600);
                    sql = $@"
        SELECT COUNT(0) FROM (
			SELECT ID
			FROM {bkTbName} WHERE  ShopId={sid}
			ORDER BY Id
			OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
		) AS t";

                    var count = logicOrderRepository.DbConnection.Query<int>(sql, commandTimeout: 600).FirstOrDefault();
                    sw.Stop();
                    action($"恢复第{pageIndex}页 {count}/{pageSize}条{tbName}数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    if (count == 0)
                        break;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"SQL=>{sql},\n异常信息：{ex}");
                    throw ex;
                }

                pageIndex++;
            }

            pageIndex = 1;
            tbName = "Product";
            bkTbName = $"BkUnBind_{tbName}";
            fieldStr = string.Join(",", tbFieldDic[tbName].Where(x => x.ToLower() != "[id]"));
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();
                var sql = $@"
--SET IDENTITY_INSERT dbo.Product ON 
INSERT INTO Product({fieldStr}) SELECT {fieldStr} FROM {bkTbName} WHERE  SourceUserId={fxUserId} AND ShopId={sid}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
--SET IDENTITY_INSERT dbo.Product OFF

SELECT Id FROM {bkTbName} WHERE  SourceUserId={fxUserId} AND ShopId={sid}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";
                try
                {
                    logicOrderRepository.DbConnection.Execute(sql, commandTimeout: 600);
                    sql = $@"
        SELECT COUNT(0) FROM (
			SELECT ID
			FROM {bkTbName} WHERE  SourceUserId={fxUserId} AND ShopId={sid}
			ORDER BY Id
			OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
		) AS t";

                    var count = logicOrderRepository.DbConnection.Query<int>(sql, commandTimeout: 600).FirstOrDefault();
                    sw.Stop();
                    action($"恢复第{pageIndex}页 {count}/{pageSize}条{tbName}数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    if (count == 0)
                        break;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"SQL=>{sql},\n异常信息：{ex}");
                    throw ex;
                }

                pageIndex++;
            }

            pageIndex = 1;
            pageSize = 10000;
            tbName = "ProductSku";
            bkTbName = $"BkUnBind_{tbName}";
            fieldStr = string.Join(",", tbFieldDic[tbName].Where(x => x.ToLower() != "[id]"));
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();
                var sql = $@"
--SET IDENTITY_INSERT dbo.ProductSku ON 
INSERT INTO ProductSku({fieldStr}) SELECT {fieldStr} FROM {bkTbName} WHERE  CreateBy={fxUserId}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
--SET IDENTITY_INSERT dbo.ProductSku OFF

SELECT Id FROM {bkTbName} WHERE  CreateBy={fxUserId}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";
                try
                {
                    logicOrderRepository.DbConnection.Execute(sql, commandTimeout: 600);
                    sql = $@"
        SELECT COUNT(0) FROM (
			SELECT ID
			FROM {bkTbName} WHERE  CreateBy={fxUserId}
			ORDER BY Id
			OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
		) AS t";

                    var count = logicOrderRepository.DbConnection.Query<int>(sql, commandTimeout: 600).FirstOrDefault();
                    sw.Stop();
                    action($"恢复第{pageIndex}页 {count}/{pageSize}条{tbName}数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    if (count == 0)
                        break;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"SQL=>{sql},\n异常信息：{ex}");
                    throw ex;
                }
                pageIndex++;
            }
        }


        public void ResumeOrder(int fxUserId, int sid, Action<string> action)
        {
            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            var db = logicOrderRepository.DbConnection;
            var tbName = "P_Order";
            var whereSql = $" WHERE  UserId={fxUserId} AND ShopId={sid}";
            ResumeData(db, tbName, whereSql, action, needPKeyInsert: false);

            tbName = "P_OrderItem";
            whereSql = $" WHERE  UserId={fxUserId} AND ShopId={sid}";
            ResumeData(db, tbName, whereSql, action, needPKeyInsert: false);

            tbName = "LogicOrder";
            whereSql = $" WHERE  FxUserId={fxUserId} AND ShopId={sid}";
            ResumeData(db, tbName, whereSql, action, needPKeyInsert: false);

            tbName = "LogicOrderItem";
            whereSql = $" WHERE ShopId={sid}";
            ResumeData(db, tbName, whereSql, action, needPKeyInsert: false);

        }

        public void ResumeProduct(int fxUserId, int sid, Action<string> action)
        {
            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            var db = logicOrderRepository.DbConnection;

            var productFieldStr = string.Join(",", tbFieldDic["Product"].Where(x => x.ToLower() != "[id]"));
            var productSkuFieldStr = string.Join(",", tbFieldDic["ProductSku"].Where(x => x.ToLower() != "[id]"));
            var pageIndex = 1;
            var pageSize = 1000;
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();

                var sql = $@"
SELECT ProductCode FROM BkUnBind_Product WHERE  SourceUserId={fxUserId} AND ShopId={sid}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";
                try
                {
                    var productCodes = db.Query<string>(sql, commandTimeout: 600)?.Distinct().ToList();
                    if (productCodes == null || productCodes.Any() == false)
                        break;

                    var count = productCodes.Count;
                    // 恢复Product
                    sql = $@"
INSERT INTO Product({productFieldStr}) SELECT {productFieldStr} FROM BkUnBind_Product WHERE  SourceUserId={fxUserId} AND ShopId={sid}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";
                    db.Execute(sql, commandTimeout: 600);
                    sw.Stop();
                    action($"恢复第{pageIndex}页 {count}/{pageSize}条 Product数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    sw.Restart();

                    // 恢复ProductSku

                    //var codes = string.Join(",", productCodes.Select(x => $"'{x}'").Distinct());
                    //sql = $@" SELECT SkuCode FROM dbo.ProductSku WITH(NOLOCK) WHERE ProductCode IN({codes})";
                    //var existSkuCodes = db.Query<string>(sql, commandTimeout: 600).Distinct().ToList();

                    //Log.Debug($"是否存在SkuCode ==> {sql}，existSkuCodes : {string.Join(",", existSkuCodes)}");
                    //if (existSkuCodes.Any())
                    //{
                    //    var skucodes = string.Join(",", existSkuCodes.Select(x => $"'{x}'").Distinct());
                    //    sql = $@"INSERT INTO ProductSku({productSkuFieldStr}) SELECT {productSkuFieldStr} FROM BkUnBind_ProductSku WHERE ProductCode IN({codes}) AND SkuCode NOT IN({skucodes})";
                    //}
                    //else
                    //    sql = $@"INSERT INTO ProductSku({productSkuFieldStr}) SELECT {productSkuFieldStr} FROM BkUnBind_ProductSku WHERE ProductCode IN({codes})";
                    //Log.Debug($"Insert ProductSku ==> {sql}");
                    //db.Execute(sql, commandTimeout: 600);
                    //sw.Stop();
                    //action($"恢复第{pageIndex}页 {count}/{pageSize}条 ProductSku数据，耗时：{sw.Elapsed.TotalSeconds} s");

                    pageIndex++;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"SQL=>{sql},\n异常信息：{ex}");
                    throw ex;
                }
            }
        }

        public void ResumeShop(int fxUserId, int sid, Action<string> action)
        {
            var configRepository = new FxUserShopRepository();
            var db = configRepository.DbConnection;
            var tbName = "P_SyncStatus";
            var whereSql = $" WHERE  FxUserId={fxUserId} AND ShopId={sid}";
            ResumeData(db, tbName, whereSql, action, needPKeyInsert: false);

            tbName = "P_SyncTask";
            whereSql = $" WHERE  FxUserId={fxUserId} AND ShopId={sid}";
            ResumeData(db, tbName, whereSql, action, needPKeyInsert: false);

            tbName = "P_FxUserShop";
            whereSql = $" WHERE  FxUserId={fxUserId} AND ShopId={sid}";
            ResumeData(db, tbName, whereSql, action, needPKeyInsert: false);
        }

        public void ResumeData(IDbConnection db, string tbName, string whereSql, Action<string> action, int pageSize = 10000, bool needPKeyInsert = false)
        {
            if (whereSql.IsNullOrEmpty() || whereSql.ToLower().Contains("where") == false)
                throw new LogicException($"Where条件不能为空");
            var pageIndex = 1;
            var bkTbName = $"BkUnBind_{tbName}";
            var fieldStr = needPKeyInsert ? string.Join(",", tbFieldDic[tbName]) : string.Join(",", tbFieldDic[tbName].Where(x => x.ToLower() != "[id]"));
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();
                var sql = $@"
{(needPKeyInsert ? $"SET IDENTITY_INSERT {tbName} ON" : "")} 
INSERT INTO {tbName}({fieldStr}) SELECT {fieldStr} FROM {bkTbName} {whereSql}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
{(needPKeyInsert ? $"SET IDENTITY_INSERT {tbName} OFF" : "")} ";
                try
                {
                    Log.Debug(sql, $"SQL-{tbName}.txt");
                    db.Execute(sql, commandTimeout: 600);
                    sql = $@"
        SELECT COUNT(0) FROM (
			SELECT ID
			FROM {bkTbName} {whereSql}
			ORDER BY Id
			OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
		) AS t";

                    var count = db.Query<int>(sql, commandTimeout: 600).FirstOrDefault();
                    sw.Stop();
                    action($"恢复第{pageIndex}页 {count}/{pageSize}条{tbName}数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    if (count == 0)
                        break;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"SQL=>{sql},\n异常信息：{ex}");
                    throw ex;
                }
                pageIndex++;
            }
        }


        public void ResumeAll(int fxUserId, int sid, DateTime? deleteTime, Action<string> action, bool isAliDb = false)
        {
            ResumeOrderNew2(fxUserId, sid, deleteTime, action);
            ResumeProductNew2(fxUserId, sid, deleteTime, action);
            ResumeShopNew(fxUserId, sid, action, isAliDb);
            // 冷库数据还原
        }

        public void ResumeOrderNew2(int fxUserId, int sid, DateTime? deleteTime, Action<string> action)
        {
            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            var db = logicOrderRepository.DbConnection;
            var pageIndex = 1;
            var pageSize = 1000;
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();

                var bkDeleteTimeWhereSql = deleteTime == null ? "" : $" AND bk.DeleteTime>'{deleteTime.Value.Format()}'";
                var sql = $@"
SELECT PlatformOrderId,LogicOrderId,ShopId,FxUserId FROM BkUnBind_LogicOrder bk WITH(NOLOCK) WHERE  FxUserId={fxUserId} AND ShopId={sid} {bkDeleteTimeWhereSql}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";
                var pidStr = "";
                var loidStr = "";
                try
                {
                    // 从备份表翻页恢复
                    var orders = db.Query<LogicOrder>(sql, commandTimeout: 600)?.ToList();
                    if (orders == null || orders.Any() == false)
                    {
                        action($"账号【{fxUserId}】未找到店铺【{sid}】需要恢复 LogicOrder 的数据，耗时：{sw.Elapsed.TotalSeconds} s");
                        break;
                    }

                    var forLogicOrders = orders.ToJson().ToList<Order>();
                    var pids = orders.Select(x => x.PlatformOrderId).Distinct().ToList();
                    pidStr = string.Join(",", pids);


                    var loids = orders.Select(x => x.LogicOrderId).Distinct().ToList();
                    loidStr = string.Join(",", loids);


                    #region 恢复 P_Order

                    var orderFieldStr = string.Join(",", tbFieldDic["P_Order"].Where(x => x.ToLower() != "[id]"));
                    var bkOrderFieldStr = string.Join(",", tbFieldDic["P_Order"].Where(x => x.ToLower() != "[id]").Select(x => $"bk.{x}"));
                    pids = orders.Select(x => x.PlatformOrderId).Distinct().ToList();
                    pidStr = string.Join(",", pids);
                    // 恢复 P_Order
                    sql = $@"
INSERT INTO P_Order({orderFieldStr}) 
SELECT
	{orderFieldStr}
 FROM (
	SELECT ROW_NUMBER() OVER(PARTITION BY bk.PlatformOrderId,bk.ShopId ORDER BY bk.id DESC) AS RowIndex, {bkOrderFieldStr}
	FROM dbo.BkUnBind_P_Order bk WITH(NOLOCK)
    INNER JOIN dbo.FunStringToTable(@pidStrs,',') fstt ON bk.PlatformOrderId = fstt.item
    LEFT JOIN P_Order o(NOLOCK) ON  bk.PlatformOrderId=o.PlatformOrderId AND bk.ShopId=o.ShopId
    WHERE bk.UserId={fxUserId} AND bk.ShopId={sid} {bkDeleteTimeWhereSql}
    AND o.PlatformOrderId IS NULL
) AS t 
where RowIndex=1";

                    //Log.WriteLine($"恢复P_Order SQL=>{sql.Replace("@pidStrs", $"'{pidStr}'")}", "resumeSQL.txt");
                    db.Execute(sql, new { pidStrs = pidStr }, commandTimeout: 600);
                    sw.Stop();
                    action($"恢复第{pageIndex}页 [{orders.Count}]条待恢复 P_Order数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    sw.Restart();

                    #endregion

                    #region 恢复 P_OrderItem

                    var orderItemFieldStr = string.Join(",", tbFieldDic["P_OrderItem"].Where(x => x.ToLower() != "[id]"));
                    var bkOrderItemFieldStr = string.Join(",", tbFieldDic["P_OrderItem"].Where(x => x.ToLower() != "[id]").Select(x => $"bk.{x}"));
                    sql = $@"
INSERT INTO P_OrderItem({orderItemFieldStr}) 
SELECT
	{orderItemFieldStr}
 FROM (
	SELECT ROW_NUMBER() OVER(PARTITION BY bk.PlatformOrderId,bk.ShopId,bk.SubItemID ORDER BY bk.id DESC) AS RowIndex,{bkOrderItemFieldStr}
    FROM dbo.BkUnBind_P_OrderItem bk WITH(NOLOCK)
    INNER JOIN dbo.FunStringToTable(@pidStrs,',') fstt ON bk.PlatformOrderId = fstt.item
    LEFT JOIN P_OrderItem oi(NOLOCK) ON  bk.PlatformOrderId=oi.PlatformOrderId AND bk.ShopId=oi.ShopId AND bk.SubItemID=oi.SubItemID
    WHERE bk.UserId={fxUserId} AND bk.ShopId={sid} {bkDeleteTimeWhereSql}
    AND oi.PlatformOrderId IS NULL
) AS t 
where RowIndex=1";

                    //Log.WriteLine($"恢复P_OrderItem SQL=>{sql.Replace("@pidStrs", $"'{pidStr}'")}", "resumeSQL.txt");
                    db.Execute(sql, new { pidStrs = pidStr }, commandTimeout: 600);
                    sw.Stop();
                    action($"恢复第{pageIndex}页 P_OrderItem数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    sw.Restart();

                    #endregion

                    #region 恢复 LogicOrder

                    // 查询需要恢复的LogicOrder
                    var logicOrderFieldStr = string.Join(",", tbFieldDic["LogicOrder"].Where(x => x.ToLower() != "[id]"));
                    var bkLogicOrderFieldStr = string.Join(",", tbFieldDic["LogicOrder"].Where(x => x.ToLower() != "[id]").Select(x => $"bk.{x}"));
                    sql = $@"
INSERT INTO LogicOrder({logicOrderFieldStr}) 
SELECT
	{logicOrderFieldStr}
 FROM (
	SELECT ROW_NUMBER() OVER(PARTITION BY bk.LogicOrderId ORDER BY bk.id DESC) AS RowIndex, {bkLogicOrderFieldStr}
	FROM dbo.BkUnBind_LogicOrder bk WITH(NOLOCK)
    INNER JOIN dbo.FunStringToTable(@loidStr,',') fstt ON bk.LogicOrderId = fstt.item
	LEFT JOIN dbo.LogicOrder lo(NOLOCK) ON bk.LogicOrderId=lo.LogicOrderId
	WHERE bk.FxUserId={fxUserId} AND bk.ShopId={sid} {bkDeleteTimeWhereSql}
    AND lo.LogicOrderId IS NULL
) AS t 
where RowIndex=1";

                    //Log.WriteLine($"恢复LogicOrder SQL=>{sql.Replace("@loidStr", $"'{loidStr}'")}", "resumeSQL.txt");
                    db.Execute(sql, new { loidStr = loidStr }, commandTimeout: 600);
                    sw.Stop();
                    action($"恢复第{pageIndex}页 LogicOrder数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    sw.Restart();

                    #endregion

                    #region 恢复 LogicOrderItem

                    // 恢复 LogicOrderItem
                    var logicOrderItemFieldStr = string.Join(",", tbFieldDic["LogicOrderItem"].Where(x => x.ToLower() != "[id]"));
                    var bkLogicOrderItemFieldStr = string.Join(",", tbFieldDic["LogicOrderItem"].Where(x => x.ToLower() != "[id]").Select(x => $"bk.{x}"));

                    sql = $@"
INSERT INTO LogicOrderItem({logicOrderItemFieldStr}) 
SELECT
	{logicOrderItemFieldStr}
FROM (
	SELECT ROW_NUMBER() OVER(PARTITION BY bk.LogicOrderId,bk.OrderItemCode ORDER BY bk.id DESC) AS RowIndex, {bkLogicOrderItemFieldStr}
	FROM dbo.BkUnBind_LogicOrderItem bk WITH(NOLOCK)
    INNER JOIN dbo.FunStringToTable(@loidStr,',') fstt ON bk.LogicOrderId = fstt.item
    LEFT JOIN dbo.LogicOrderItem oi(NOLOCK) ON oi.LogicOrderId = bk.LogicOrderId AND oi.OrderItemCode = bk.OrderItemCode
	WHERE bk.ShopId={sid} {bkDeleteTimeWhereSql}
    AND oi.LogicOrderId IS NULL
) AS t 
where RowIndex=1";

                    //Log.WriteLine($"恢复LogicOrderItem SQL=>{sql.Replace("@loidStr", $"'{loidStr}'")}", "resumeSQL.txt");
                    db.Execute(sql, new { loidStr = loidStr }, commandTimeout: 600);
                    sw.Stop();
                    action($"恢复第{pageIndex}页 LogicOrderItem数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    sw.Restart();

                    #endregion

                    pageIndex++;

                    action($"----------------------------");
                    action("");
                }
                catch (Exception ex)
                {
                    Log.WriteError($"ResumeOrderNew SQL=>{sql.Replace("@pidStrs", $"'{pidStr}'").Replace("@loidStr", $"'{loidStr}'")},\n异常信息：{ex}");
                    throw ex;
                }
            }

        }

        public void ResumeProductNew2(int fxUserId, int sid, DateTime? deleteTime, Action<string> action)
        {
            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            var db = logicOrderRepository.DbConnection;
            var pageIndex = 1;
            var pageSize = 100;
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();

                var bkDeleteTimeWhereSql = deleteTime == null ? "" : $" AND bk.DeleteTime>'{deleteTime.Value.Format()}'";
                // ProductCode 是计算列转为PlatformId，不需要再计算
                var sql = $@"
SELECT ProductCode AS PlatformId,ShopId,SourceUserId FROM BkUnBind_Product bk WITH(NOLOCK) WHERE  SourceUserId={fxUserId} AND ShopId={sid} {bkDeleteTimeWhereSql}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
";
                var pidStr = "";
                try
                {
                    #region 恢复 Product

                    var products = db.Query<ProductFx>(sql, commandTimeout: 600)?.ToList();
                    if (products == null || products.Any() == false)
                    {
                        action($"账号【{fxUserId}】未找到店铺【{sid}】需要恢复 Product 的数据，耗时：{sw.Elapsed.TotalSeconds} s");
                        break;
                    }


                    var pids = products.Select(x => x.PlatformId).Distinct().ToList();
                    pidStr = string.Join(",", pids);

                    // 恢复 Product
                    var productFieldStr = string.Join(",", tbFieldDic["Product"].Where(x => x.ToLower() != "[id]"));
                    var bkProductFieldStr = string.Join(",", tbFieldDic["Product"].Where(x => x.ToLower() != "[id]").Select(x => $"bk.{x}"));
                    
                    sql = $@"
INSERT INTO Product({productFieldStr}) 
SELECT
	{productFieldStr}
FROM (
	SELECT ROW_NUMBER() OVER(PARTITION BY bk.ProductCode ORDER BY bk.id DESC) AS RowIndex, {bkProductFieldStr}
	FROM dbo.BkUnBind_Product bk WITH(NOLOCK)
    INNER JOIN dbo.FunStringToTable(@pidStrs,',') fstt ON bk.ProductCode = fstt.item
	LEFT JOIN dbo.Product p(NOLOCK) ON bk.ProductCode=p.ProductCode
    WHERE bk.SourceUserId={fxUserId} AND bk.ShopId={sid} {bkDeleteTimeWhereSql}
	AND p.ProductCode IS NULL
) AS t 
where RowIndex=1";

                    //Log.WriteLine($"恢复Product SQL=>{sql.Replace("@pidStrs", $"'{pidStr}'")}","resumeSQL.txt");
                    db.Execute(sql, new { pidStrs = pidStr }, commandTimeout: 600);
                    sw.Stop();
                    action($"恢复第{pageIndex}页 Product数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    sw.Restart();

                    #endregion

                    #region 恢复 ProductSku

                    var productSkuFieldStr = string.Join(",", tbFieldDic["ProductSku"].Where(x => x.ToLower() != "[id]"));
                    var bkProductSkuFieldStr = string.Join(",", tbFieldDic["ProductSku"].Where(x => x.ToLower() != "[id]").Select(x => $"bk.{x}"));

                    sql = $@"
INSERT INTO ProductSku({productSkuFieldStr}) 
SELECT
	{productSkuFieldStr}
FROM (
    SELECT ROW_NUMBER() OVER(PARTITION BY bk.ProductCode,bk.SkuCode ORDER BY bk.id DESC) AS RowIndex,{bkProductSkuFieldStr}
    FROM dbo.BkUnBind_ProductSku bk WITH(NOLOCK)
    INNER JOIN dbo.FunStringToTable(@pidStrs,',') fstt ON bk.ProductCode = fstt.item
	LEFT JOIN dbo.ProductSku ps(NOLOCK) ON bk.ProductCode=ps.ProductCode AND bk.SkuCode=ps.SkuCode
    WHERE 1=1 AND bk.CreateBy={fxUserId} {bkDeleteTimeWhereSql}
	AND ps.ProductCode IS NULL
) AS t 
where RowIndex=1";

                    //Log.WriteLine($"恢复ProductSku SQL=>{sql.Replace("@pidStrs", $"'{pidStr}'")}", "resumeSQL.txt");
                    db.Execute(sql, new { pidStrs = pidStr }, commandTimeout: 600);
                    sw.Stop();
                    action($"恢复第{pageIndex}页 ProductSku数据，耗时：{sw.Elapsed.TotalSeconds} s");

                    #endregion

                    pageIndex++;
                    action($"----------------------------");
                    action("");
                }
                catch (Exception ex)
                {
                    Log.WriteError($"ResumeProductNew SQL=>{sql.Replace("@pidStrs", $"'{pidStr}'")},\n异常信息：{ex}");
                    throw ex;
                }
            }
        }

        public void ResumeOrderNew(int fxUserId, int sid, Action<string> action)
        {
            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            var db = logicOrderRepository.DbConnection;
            var pageIndex = 1;
            var pageSize = 1000;
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();

                var sql = $@"
SELECT DISTINCT * FROM (
    SELECT PlatformOrderId,ShopId,UserId FROM BkUnBind_P_Order WITH(NOLOCK) WHERE  UserId={fxUserId} AND ShopId={sid}
    ORDER BY Id
    OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY
) AS t";
                try
                {
                    #region 恢复 P_Order P_OrderItem
                    var orders = db.Query<Order>(sql, commandTimeout: 600)?.ToList();
                    if (orders == null || orders.Any() == false)
                        break;

                    var forLogicOrders = orders.ToJson().ToList<Order>();
                    var pids = orders.Select(x => $"'{x.PlatformOrderId}'").Distinct().ToList();
                    pids.Add("'a'");
                    var pidStr = string.Join(",", pids);
                    // 查询数据库已存在的P_Order数据，需过滤
                    sql = $@"SELECT PlatformOrderId,ShopId,UserId FROM P_Order WITH(NOLOCK) WHERE  UserId={fxUserId} AND PlatformOrderId IN({pidStr}) AND ShopId={sid}";
                    //Log.WriteWarning(sql, $"OrderCheckSql-{sid}.txt");
                    // P_Order 有唯一键约束无法恢复，需要过滤
                    var existOrders = db.Query<Order>(sql, commandTimeout: 600).ToList();
                    if (existOrders.Any())
                    {
                        //Log.WriteWarning($"检测到已存在订单：{string.Join(",", existOrders.Select(x => x.PlatformOrderId))}", $"OrderCheckSql-{sid}.txt");
                        var bindToOtherOrders = new List<Order>();
                        var notExistOrders = new List<Order>();
                        foreach (var x in orders)
                        {
                            if (existOrders.Any(y => y.PlatformOrderId == x.PlatformOrderId && y.ShopId == x.ShopId) == false)
                                notExistOrders.Add(x);
                            else
                            {
                                if (existOrders.Any(y => y.PlatformOrderId == x.PlatformOrderId && y.ShopId == x.ShopId && y.UserId != x.UserId))
                                    bindToOtherOrders.Add(x);
                            }
                        }

                        if (bindToOtherOrders.Any())
                        {
                            action($"店铺【{sid}】存在{bindToOtherOrders.Count}绑定到其他账号的订单");
                            var bindToOtherOrdersJson = bindToOtherOrders.Select(x => new { to = $"{fxUserId} => {x.UserId}", pid = x.PlatformOrderId, sid = x.ShopId }).ToJson();
                            Log.WriteWarning($"修复订单绑定到其他账号：{bindToOtherOrdersJson}", $"OrderCheckSql-{sid}.txt");
                        }
                        orders = notExistOrders;
                    }
                    var count = 0;
                    if (orders.Any() == false)
                    {
                        sw.Stop();
                        action($"第{pageIndex}页 P_Order 都已经恢复，耗时：{sw.Elapsed.TotalSeconds} s");
                        sw.Restart();
                        pageIndex++;
                    }
                    else
                    {
                        #region 恢复 P_Order

                        var orderFieldStr = string.Join(",", tbFieldDic["P_Order"].Where(x => x.ToLower() != "[id]"));
                        pids = orders.Select(x => $"'{x.PlatformOrderId}'").Distinct().ToList();
                        pids.Add("'a'");
                        pidStr = string.Join(",", pids);
                        count = orders.Count;
                        // 恢复 P_Order
                        sql = $@"
INSERT INTO P_Order({orderFieldStr}) 
SELECT
	{orderFieldStr}
 FROM (
	SELECT ROW_NUMBER() OVER(PARTITION BY bk.PlatformOrderId,bk.ShopId ORDER BY id DESC) AS RowIndex, {orderFieldStr}
	FROM dbo.BkUnBind_P_Order bk WITH(NOLOCK)
    WHERE bk.UserId={fxUserId} AND bk.ShopId={sid}
    AND bk.PlatformOrderId IN({pidStr})
) AS t 
where RowIndex=1";

                        db.Execute(sql, commandTimeout: 600);
                        sw.Stop();
                        action($"恢复第{pageIndex}页 {count}/{pageSize}条 P_Order数据，耗时：{sw.Elapsed.TotalSeconds} s");
                        sw.Restart();

                        #endregion

                        #region 恢复 P_OrderItem

                        var orderItemFieldStr = string.Join(",", tbFieldDic["P_OrderItem"].Where(x => x.ToLower() != "[id]"));
                        sql = $@"
INSERT INTO P_OrderItem({orderItemFieldStr}) 
SELECT
	{orderItemFieldStr}
 FROM (
	SELECT ROW_NUMBER() OVER(PARTITION BY bk.PlatformOrderId,bk.ShopId ORDER BY id DESC) AS RowIndex,{orderItemFieldStr}
    FROM dbo.BkUnBind_P_OrderItem bk WITH(NOLOCK)
    WHERE bk.UserId={fxUserId} AND bk.ShopId={sid}
    AND bk.PlatformOrderId IN({pidStr})
) AS t 
where RowIndex=1";

                        db.Execute(sql, commandTimeout: 600);
                        sw.Stop();
                        action($"恢复第{pageIndex}页 {count}/{pageSize}条 P_OrderItem数据，耗时：{sw.Elapsed.TotalSeconds} s");
                        sw.Restart();

                        #endregion
                    }
                    #endregion

                    #region 恢复 LogicOrder

                    pids = forLogicOrders.Select(x => $"'{x.PlatformOrderId}'").Distinct().ToList();
                    pids.Add("'a'");
                    pidStr = string.Join(",", pids);
                    var existLpidWhereSql = string.Empty;

                    // 查询需要恢复的LogicOrder
                    sql = $@"
    SELECT
        bk.LogicOrderId
	FROM dbo.BkUnBind_LogicOrder bk WITH(NOLOCK)
	WHERE bk.FxUserId={fxUserId} AND bk.ShopId={sid}
    AND bk.PlatformOrderId IN({pidStr})";
                    var logicOrderIds = db.Query<string>(sql).Distinct().Select(x => $"'{x}'").ToList();
                    if (logicOrderIds.Any() == false)
                    {
                        sw.Stop();
                        action($"查询第{pageIndex}页 备份表中LogicOrder数据未找到，耗时：{sw.Elapsed.TotalSeconds} s");
                        continue;
                    }

                    // 查询数据库中LogicOrder已经存在的需要过滤
                    sql = $@"SELECT DISTINCT PlatformOrderId,LogicOrderId FROM dbo.LogicOrder WITH(NOLOCK) WHERE LogicOrderId IN({string.Join(",", logicOrderIds)})";
                    var existLogicOrders = db.Query<LogicOrder>(sql, commandTimeout: 600).ToList();
                    if (existLogicOrders.Any())
                    {
                        var existLids = existLogicOrders.Select(x => $"'{x.LogicOrderId}'").Distinct().ToList();
                        existLpidWhereSql = $" AND LogicOrderId NOT IN({string.Join(",", existLids)})";
                    }
                    sw.Stop();
                    action($"查询第{pageIndex}页 已存在LogicOrder数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    sw.Restart();


                    var logicOrderFieldStr = string.Join(",", tbFieldDic["LogicOrder"].Where(x => x.ToLower() != "[id]"));
                    count = pids.Count - 1;
                    sql = $@"
INSERT INTO LogicOrder({logicOrderFieldStr}) 
SELECT
	{logicOrderFieldStr}
 FROM (
	SELECT ROW_NUMBER() OVER(PARTITION BY bk.LogicOrderId ORDER BY id DESC) AS RowIndex, {logicOrderFieldStr}
	FROM dbo.BkUnBind_LogicOrder bk WITH(NOLOCK)
	WHERE bk.FxUserId={fxUserId} AND bk.ShopId={sid}
    AND bk.PlatformOrderId IN({pidStr}) {existLpidWhereSql}
) AS t 
where RowIndex=1";

                    db.Execute(sql, commandTimeout: 600);
                    sw.Stop();
                    action($"恢复第{pageIndex}页 {count}/{pageSize}条 LogicOrder数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    sw.Restart();
                    #endregion

                    #region 恢复 LogicOrderItem

                    sql = $@"SELECT DISTINCT LogicOrderId FROM dbo.LogicOrderItem WITH(NOLOCK) WHERE LogicOrderId IN({string.Join(",", logicOrderIds)})";
                    var existLogicOrderItems = db.Query<LogicOrderItem>(sql, commandTimeout: 600).ToList();
                    sw.Stop();
                    action($"查询第{pageIndex}页 已存在LogicOrderItem数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    sw.Restart();

                    existLpidWhereSql = string.Empty;
                    if (existLogicOrderItems.Any())
                    {
                        var existLids = existLogicOrderItems.Select(x => $"'{x.LogicOrderId}'").Distinct().ToList();
                        existLpidWhereSql = $" AND LogicOrderId NOT IN({string.Join(",", existLids)})";
                    }
                    // 恢复 LogicOrderItem
                    var logicOrderItemFieldStr = string.Join(",", tbFieldDic["LogicOrderItem"].Where(x => x.ToLower() != "[id]"));
                    sql = $@"
INSERT INTO LogicOrderItem({logicOrderItemFieldStr}) 
SELECT
	{logicOrderItemFieldStr}
FROM (
	SELECT ROW_NUMBER() OVER(PARTITION BY bk.LogicOrderId ORDER BY id DESC) AS RowIndex, {logicOrderItemFieldStr}
	FROM dbo.BkUnBind_LogicOrderItem bk WITH(NOLOCK)
	WHERE bk.ShopId={sid}
    AND bk.PlatformOrderId IN({pidStr}) {existLpidWhereSql}
) AS t 
where RowIndex=1";

                    db.Execute(sql, commandTimeout: 600);
                    sw.Stop();
                    action($"恢复第{pageIndex}页 {count}/{pageSize}条 LogicOrderItem数据，耗时：{sw.Elapsed.TotalSeconds} s");
                    sw.Restart();

                    #endregion

                    pageIndex++;

                    action($"----------------------------");
                    action("");
                }
                catch (Exception ex)
                {
                    Log.WriteError($"ResumeOrderNew SQL=>{sql},\n异常信息：{ex}");
                    throw ex;
                }
            }

        }

        public void ResumeProductNew(int fxUserId, int sid, Action<string> action)
        {
            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            var db = logicOrderRepository.DbConnection;
            var pageIndex = 1;
            var pageSize = 100;
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();

                // ProductCode 是计算列转为PlatformId，不需要再计算
                var sql = $@"
SELECT ProductCode AS PlatformId,ShopId,SourceUserId FROM BkUnBind_Product WITH(NOLOCK) WHERE  SourceUserId={fxUserId} AND ShopId={sid}
ORDER BY Id
OFFSET {(pageIndex - 1) * pageSize } ROWS FETCH NEXT {pageSize} ROWS ONLY
";
                try
                {
                    #region 恢复 Product

                    var products = db.Query<ProductFx>(sql, commandTimeout: 600)?.ToList();
                    if (products == null || products.Any() == false)
                        break;

                    var pids = products.Select(x => $"'{x.PlatformId}'").Distinct().ToList();
                    var pidStr = string.Join(",", pids);
                    var platformIds = products.Select(x => x.PlatformId).Distinct().ToList();
                    //sql = $@"SELECT  ProductCode AS PlatformId FROM Product WITH(NOLOCK) WHERE ProductCode IN({pidStr})";
                    sql = $"SELECT p.ProductCode AS PlatformId FROM Product p WITH(NOLOCK) INNER JOIN dbo.FunStringToTable(@pidStrs,',') fstt ON p.ProductCode = fstt.item WHERE 1=1";
                    // Product 有唯一键约束无法恢复，需要过滤

                    var existProducts = db.Query<ProductFx>(sql, new { pidStrs = string.Join(",", platformIds) }, commandTimeout: 600).ToList();
                    var existProductDic = existProducts.GroupBy(x => x.PlatformId).ToDictionary(x => x.Key, x => x.ToList());

                    var forSkuproducts = products.ToJson().ToList<ProductFx>();
                    products = products.Where(x => existProducts.Any(y => y.PlatformId == x.PlatformId) == false).ToList();
                    if (products.Any() == false)
                    {
                        sw.Stop();
                        action($"第{pageIndex}页 Product 都已经恢复，耗时：{sw.Elapsed.TotalSeconds} s");
                        sw.Restart();
                        //pageIndex++;
                        //continue;
                    }
                    else
                    {
                        // 恢复 Product
                        var productFieldStr = string.Join(",", tbFieldDic["Product"].Where(x => x.ToLower() != "[id]"));
                        var count = products.Count;
                        pids = products.Select(x => $"'{x.PlatformId}'").Distinct().ToList();
                        pidStr = string.Join(",", pids);
                        sql = $@"
INSERT INTO Product({productFieldStr}) 
SELECT
	{productFieldStr}
FROM (
	SELECT ROW_NUMBER() OVER(PARTITION BY bk.ProductCode ORDER BY id DESC) AS RowIndex, {productFieldStr}
	FROM dbo.BkUnBind_Product bk WITH(NOLOCK)
    WHERE bk.SourceUserId={fxUserId} AND bk.ShopId={sid}
    AND bk.ProductCode IN({pidStr})
) AS t 
where RowIndex=1";

                        db.Execute(sql, commandTimeout: 600);
                        sw.Stop();
                        action($"恢复第{pageIndex}页 {count}/{pageSize}条 Product数据，耗时：{sw.Elapsed.TotalSeconds} s");
                        sw.Restart();
                    }

                    #endregion

                    #region 恢复 ProductSku

                    var skuWhereSql = string.Empty;
                    pids = forSkuproducts.Select(x => $"'{x.PlatformId}'").Distinct().ToList();
                    pidStr = string.Join(",", pids);
                    platformIds = forSkuproducts.Select(x => x.PlatformId).Distinct().ToList();
                    var productSkuFieldStr = string.Join(",", tbFieldDic["ProductSku"].Where(x => x.ToLower() != "[id]"));
                    //sql = $@"SELECT SkuCode FROM dbo.ProductSku WITH(NOLOCK) WHERE ProductCode IN({pidStr})";
                    sql = $"SELECT psku.SkuCode FROM dbo.ProductSku psku WITH(NOLOCK) INNER JOIN dbo.FunStringToTable(@pidStrs,',') fstt ON psku.ProductCode = fstt.item WHERE 1=1";
                    var skus = db.Query<string>(sql, new { pidStrs = string.Join(",", platformIds) }).ToList();
                    if (skus.Any())
                    {
                        var skuCodes = string.Join(",", skus.Select(x => $"'{x}'").Distinct());
                        skuWhereSql = $" AND SkuCode NOT IN({skuCodes})";
                    }

                    sql = $@"
INSERT INTO ProductSku({productSkuFieldStr}) 
SELECT
	{productSkuFieldStr}
FROM (
    SELECT ROW_NUMBER() OVER(PARTITION BY bk.SkuCode ORDER BY id DESC) AS RowIndex,{productSkuFieldStr}
    FROM dbo.BkUnBind_ProductSku bk WITH(NOLOCK)
    WHERE bk.ProductCode IN({pidStr}) {skuWhereSql}
) AS t 
where RowIndex=1";

                    db.Execute(sql, commandTimeout: 600);
                    sw.Stop();
                    action($"恢复第{pageIndex}页 {forSkuproducts.Count}/{pageSize}条 ProductSku数据，耗时：{sw.Elapsed.TotalSeconds} s");

                    #endregion

                    pageIndex++;
                    action($"----------------------------");
                    action("");
                }
                catch (Exception ex)
                {
                    Log.WriteError($"ResumeProductNew SQL=>{sql},\n异常信息：{ex}");
                    throw ex;
                }
            }
        }

        public void ResumeSystemShopNew(int fxUserId, int sysid)
        {
            var configRepository = new FxUserShopRepository();
            var db = configRepository.DbConnection;

            var tbName = "P_Shop";
            var whereSql = $" WHERE Id={sysid}";
            ResumeShopNew(db, sysid, tbName, whereSql, null);
        }

        public void ResumeShopNew(int fxUserId, int sid, Action<string> action, bool isAliDb = false)
        {
            var configRepository = new FxUserShopRepository();
            var db = configRepository.DbConnection;

            var tbName = "P_FxUserShop";
            var whereSql = $" WHERE  FxUserId={fxUserId} AND ShopId={sid}";
            ResumeShopNew(db, sid, tbName, whereSql, action, needSetInsertId: true, isAliDb: isAliDb);

            tbName = "P_Shop";
            whereSql = $" WHERE  Id={sid}";
            ResumeShopNew(db, sid, tbName, whereSql, action, needSetInsertId: true, isAliDb: isAliDb);

            tbName = "P_SyncTask";
            whereSql = $" WHERE  FxUserId={fxUserId} AND ShopId={sid} AND Source='FenDanSystem'";
            ResumeShopNew(db, sid, tbName, whereSql, action, needSetInsertId: true, isAliDb: isAliDb);

            tbName = "P_SyncStatus";
            whereSql = $" WHERE  FxUserId={fxUserId} AND ShopId={sid} AND Source='FenDanSystem'";
            ResumeShopNew(db, sid, tbName, whereSql, action, needSetInsertId: true, isAliDb: isAliDb);
        }

        public void ResumeUserNew(int fxUserId, int sysid, Action<string> action)
        {
            var configRepository = new FxUserShopRepository();
            var db = configRepository.DbConnection;
            var isAliDb = CustomerConfig.CloudPlatformType == "Alibaba";
            var tbName = "P_FxUserShop";
            var whereSql = $" WHERE  FxUserId={fxUserId} AND ShopId={sysid}";
            ResumeShopNew(db, sysid, tbName, whereSql, action, needSetInsertId: true, isAliDb: isAliDb);

            tbName = "P_Shop";
            whereSql = $" WHERE Id={sysid}";
            ResumeShopNew(db, sysid, tbName, whereSql, action, needSetInsertId: true, isAliDb: isAliDb);

            tbName = "P_UserFx";
            whereSql = $" WHERE Id={fxUserId}";
            ResumeShopNew(db, fxUserId, tbName, whereSql, action, needSetInsertId: true, isAliDb: isAliDb);
        }

        public void ResumeShopNew(IDbConnection db, int sid, string tbName, string whereSql, Action<string> action, bool needSetInsertId = false, bool isAliDb = false)
        {
            if (whereSql.ToLower().Contains("where") == false)
            {
                action($"ResumeShopNew 必须包含Where条件");
                return;
            }

            var sql = $"SELECT Id FROM {tbName} WITH(NOLOCK) {whereSql}";
            var id = db.Query<int>(sql).FirstOrDefault();
            if (id == 0)
            {
                try
                {
                    var fields = (needSetInsertId ? tbFieldDic[tbName] : tbFieldDic[tbName].Where(x => x.ToLower() != "[id]")).ToList();
                    Log.WriteWarning($"【{tbName}】字段：{fields.ToJson()}");
                    if (needSetInsertId && fields.Contains("[Id]") == false)
                        fields.Add("[Id]");
                    var fieldStr = string.Join(",", fields);
                    sql = $@"
{(needSetInsertId && isAliDb ? $"SET IDENTITY_INSERT {tbName} ON" : "")}
INSERT INTO {tbName}({fieldStr}) 
SELECT DISTINCT {fieldStr} FROM dbo.BkUnBind_{tbName} WITH(NOLOCK) {whereSql}
{(needSetInsertId && isAliDb ? $"SET IDENTITY_INSERT {tbName} OFF" : "")}
";
                    db.Execute(sql);
                    action($"店铺【{sid}】{tbName} 恢复成功");
                }
                catch (Exception ex)
                {
                    Log.WriteError($"SQL=>{sql}\n异常信息：{ex}");
                    throw ex;
                }
            }
            else
            {
                action($"店铺【{sid}】{tbName} 已经恢复");
            }
        }
        #endregion

        private void DelBackupDatas()
        {
            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            var db = logicOrderRepository.DbConnection;
            var deleteTime = DateTime.Now.AddDays(-180).Format();
            var delTables = new List<string> { "BkUnBind_P_Order", "BkUnBind_P_OrderItem", "BkUnBind_LogicOrder", "BkUnBind_LogicOrderItem", "BkUnBind_Product", "BkUnBind_ProductSku" };
            var baseDelSql = $"DELETE TOP 【NUM】 FROM 【TableName】 WHERE DeleteTime IS NULL OR DeleteTime < '{deleteTime}'";
            Parallel.ForEach(delTables, new ParallelOptions { MaxDegreeOfParallelism = 6 }, tb =>
            {
                var batchCount = 500;
                var sql = baseDelSql.Replace("【TableName】", tb).Replace("【NUM】", batchCount.ToString());
                var index = 1;
                while (true)
                {
                    try
                    {
                        var count = db.Execute(sql, commandTimeout: 120);
                        Log.WriteLine($"{tb},第【{index}】次成功删除【{count}】行,SQL:{sql}");
                        index++;
                        Thread.Sleep(200);
                        if (count < batchCount)
                            break;
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"数据库连接：{db.ConnectionString}执行删除语句时发生错误,SQL:{sql}，错误详情：{ex}");
                        break;
                    }
                }

                db.Close();
                db.Dispose();
            });
        }

        /// <summary>
        /// 按店铺修改FxUserShopDefaultSupplier表状态
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopId"></param>
        public void DelFxUserShopDefaultSupplierByShopId(int fxUserId, List<int> shopIds, Action<string> action)
        {
            var sw = new Stopwatch();
            sw.Start();

            string sql = "update FxUserShopDefaultSupplier set IsDeleted = 1,UpdateTime=GETDATE() where ShopId in @shopIds and IsDeleted = 0";
            new FxUserForeignShopRepository().DbConnection.Execute(sql, new
            {
                shopIds
            });

            sw.Stop();
            action.Invoke($"删除店铺绑定厂家数据fxUserId：{fxUserId}，shopId:{string.Join(",", shopIds)}，耗时：{sw.Elapsed.TotalSeconds}s");
        }

        /// <summary>
        /// 解除店铺商品与基础商品关联数据
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopIds"></param>
        /// <param name="supplierFxUserIds"></param>
        /// <param name="action"></param>
        public List<string> DelBaseProductRelation(int fxUserId, List<int> shopIds, List<int> supplierFxUserIds,
            Action<string> action)
        {
            if (shopIds == null || shopIds.Any() == false)
                throw new LogicException("店铺Id不能为空");
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");
            supplierFxUserIds.Add(fxUserId);
            var cloudPlatformTypeList = new List<string> { CustomerConfig.CloudPlatformType };
            var dbConfigList = new DbConfigRepository().GetListByFxUserIds(supplierFxUserIds, cloudPlatformTypeList);
            var dbConn = dbConfigList
                .GroupBy(x => x.ConnectionString)
                .Select(x => x.Key)
                .ToList();
            var allProductCodes = new List<string>();

            foreach (var sid in shopIds)
            {
                var logicOrderRepository = _connectionString.IsNullOrEmpty()
                    ? new LogicOrderRepository()
                    : new LogicOrderRepository(_connectionString);
                var db = logicOrderRepository.DbConnection;
                // 获取店铺商品ProductCode
                const string sql = "SELECT DISTINCT ProductCode FROM Product WITH(NOLOCK) WHERE SourceUserId = @fxUserId AND ShopId = @sid";
                var productCodes = db.Query<string>(sql, new { fxUserId, sid }, commandTimeout: 300).ToList();
                allProductCodes.AddRange(productCodes);
                const int pageSize = 100;
                var count = Math.Ceiling(productCodes.Count * 1.0 / pageSize);
                for (var i = 0; i < count; i++)
                {
                    // 设置关联关系状态为0
                    var codes = productCodes.Skip(i * pageSize).Take(pageSize).Select(x => $"'{x}'").ToList();
                    var whereSql = $" ProductCode in ({string.Join(",", codes)})";
                    var updateSql = $"UPDATE BaseOfPtSkuRelation SET Status = 0 WHERE {whereSql}";
                    var result = db.Execute(updateSql, commandTimeout: 300);
                    
                    // 记录更新语句
                    Log.WriteLine($"店铺【{sid}】解除商品关联关系SQL：{updateSql}, 成功数量: {result}", "DelBaseProductRelation.txt");
                    
                    // 其他厂家库也需要执行
                    foreach (var conn in dbConn)
                    {
                        // 相同库不处理
                        if (db.ConnectionString == conn || string.IsNullOrEmpty(conn))
                            continue;
                        
                        var otherDb = new LogicOrderRepository(conn).DbConnection;
                        otherDb.Execute(updateSql, commandTimeout: 300);
                        otherDb.Close();
                        otherDb.Dispose();
                    }
                }
            }
            
            #region 处理商品库的关联关系
                
            // 如果是精选，则直接处理
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                var productDbConfigRepository = new ProductDbConfigRepository();
                var basePtDbNameIds = productDbConfigRepository.GetDbConfigByFxUserId(supplierFxUserIds)
                    .Select(x => x.DbNameConfigId).Distinct().ToList();
                // 获取所有的基础分库连接串
                var productDbConfigModels = productDbConfigRepository.GetDbConfigModelByDbNameId(basePtDbNameIds);
                if (productDbConfigModels != null && productDbConfigModels.Any())
                {
                    var basePtDbConfigs = productDbConfigModels.Select(x => x.ConnectionString).Distinct().ToList();
                    // 删除基础商品关联关系
                    var result = basePtDbConfigs.Sum(conn => new BaseOfPtSkuRelationRepository(conn).DeleteByProductCodes(allProductCodes));
                    Log.WriteLine($"店铺【{shopIds.ToJson()}】解除商品关联关系——删除商品库数据， 成功数量: {result}", "DelBaseProductRelation.txt");
                }
            }

            #endregion

            return allProductCodes;
        }
    }

    public enum UnbindType
    {
        DelOrder = 1,
        DelProduct = 2,
        DelWaybillCode = 3,
        DelPrintHistory = 4,
        DelSendHistory = 5,
        DelBindShop = 6,
        DelApplication = 7,
        DelRelation = 8,
        DelUser = 10,
        ResetTryTime = 11,
        SwitchUser = 12,
        SwitchUserBack = 13,
        Resume = 14
    }
}
