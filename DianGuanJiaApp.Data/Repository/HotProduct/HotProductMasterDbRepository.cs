using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Data;

namespace DianGuanJiaApp.Data.Repository.HotProduct
{
    /// <summary>
    /// 爆品分析 mysql 主库仓储基类
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class HotProductMasterDbRepository<T> : BaseRepository<T>
    {
        private IDbConnection _connection;
        private string _connectionString;
        private bool _isUseMySQL = true;

        /// <summary>
        ///
        /// </summary>
        public HotProductMasterDbRepository()
        {
            _connectionString = CustomerConfig.HotProductDbConnectionString;
            if (!string.IsNullOrEmpty(_connectionString))
            {
                _isUseMySQL = _connectionString.StartsWith("server") == false;
            }
        }

        /// <summary>
        /// 指定连接串
        /// </summary>
        public HotProductMasterDbRepository(string connectionString)
        {
            _connectionString = connectionString;
            if (string.IsNullOrEmpty(_connectionString))
            {
                _connectionString = CustomerConfig.HotProductDbConnectionString;
            }
            if (!string.IsNullOrEmpty(_connectionString))
            {
                _isUseMySQL = _connectionString.StartsWith("server") == false;
            }
        }

        /// <summary>
        ///
        /// </summary>
        public new IDbConnection DbConnection
        {
            get
            {
                IDbConnection db = null;

                if (!string.IsNullOrEmpty(_connectionString))
                {
                    if (_isUseMySQL)
                    {
                        db = Dapper.DbUtility.GetMySqlConnection(_connectionString);
                    }
                    else
                    {
                        db = Dapper.DbUtility.GetConnection(_connectionString);
                    }
                }
                else
                {
                    throw new Exception("基础商品数据连接为空");
                }
                return db;
            }
        }

        public bool IsUseMySQL
        {
            get { return _isUseMySQL; }
        }

        /// <summary>
        /// 转换SQL，针对MySQL去除部分不支持的关键字
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public string TranSql(string sql)
        {
            if (!IsUseMySQL)
                return sql;
            sql = sql.Replace("WITH(NOLOCK)", string.Empty);
            sql = sql.Replace("with(nolock)", string.Empty);
            sql = sql.Replace("(NOLOCK)", string.Empty);
            sql = sql.Replace("(nolock)", string.Empty);
            return sql;
        }

        /// <summary>
        /// 获取当前时间
        /// </summary>
        /// <returns></returns>
        public DateTime GetNowTime()
        {
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
            {
                return DateTime.Now;
            }
            var db = DbConnection;
            if (IsUseMySQL)
            {
                return db.ExecuteScalar<DateTime>("SELECT NOW()");
            }
            return db.ExecuteScalar<DateTime>("SELECT GETDATE()");
        }

        #region 基础操作，重写，兼容MySQL

        public new long Add(T model)
        {
            if (IsUseMySQL)
            {
                long? result;
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    result = _connection.InsertMysqlWithLongId<T>(model);
                }
                return result ?? 0;
            }
            else
            {
                return base.Add(model).ToLong();
            }
        }

        public new bool Update(T model)
        {
            if (IsUseMySQL)
            {
                int result;
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    result = _connection.UpdateMysql<T>(model);
                }
                if (result > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return base.Update(model);
            }
        }

        public new void BulkInsert(List<T> models)
        {
            if (IsUseMySQL)
            {
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    if (_connection.State == ConnectionState.Closed)
                        _connection.Open();

                    models.ForEach(model => { _connection.InsertMysqlWithLongId<T>(model); });
                }
            }
            else
            {
                base.BulkInsert(models);
            }
        }

        #endregion 基础操作，重写，兼容MySQL
    }
}