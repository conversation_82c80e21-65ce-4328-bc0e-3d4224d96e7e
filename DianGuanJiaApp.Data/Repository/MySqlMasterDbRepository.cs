using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Repository
{
    /// <summary>
    /// mysql 主库仓储基类
    /// </summary>
    public class MySqlMasterDbRepository<T> : BaseRepository<T>
    {
        private IDbConnection _connection;
        private string _connectionString;
        private const bool _isUseMySQL = true;

        /// <summary>
        /// 指定连接串
        /// </summary>
        public MySqlMasterDbRepository(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new ArgumentNullException("使用此MySql主库基类需要指定连接字符串");
            }

            _connectionString = connectionString;
            _connection = Dapper.DbUtility.GetMySqlConnection(_connectionString);
        }

        /// <summary>
        /// 用于类初始化之后重新设置连接字符串
        /// </summary>
        protected string ConnectionString
        {
            set
            {
                _connectionString = value;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        public new IDbConnection DbConnection
        {
            get
            {
                if (_connection != null)
                    return _connection;
                IDbConnection db = null;

                if (!string.IsNullOrEmpty(_connectionString))
                {
                    db = Dapper.DbUtility.GetMySqlConnection(_connectionString);
                }
                else
                {
                    throw new ArgumentNullException("MySql主库仓储连接字符串为空");
                }
                return db;
            }
        }

        public static bool IsUseMySQL
        {
            get { return _isUseMySQL; }
        }

        /// <summary>
        /// 转换SQL，针对MySQL去除部分不支持的关键字
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public static string TranSql(string sql)
        {
            if (!IsUseMySQL)
                return sql;
            sql = sql.Replace("WITH(NOLOCK)", string.Empty);
            sql = sql.Replace("with(nolock)", string.Empty);
            sql = sql.Replace("(NOLOCK)", string.Empty);
            sql = sql.Replace("(nolock)", string.Empty);
            return sql;
        }

        /// <summary>
        /// 获取当前时间
        /// </summary>
        /// <returns></returns>
        public DateTime GetNowTime()
        {
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
            {
                return DateTime.Now;
            }
            var db = DbConnection;
            if (IsUseMySQL)
            {
                return db.ExecuteScalar<DateTime>("SELECT NOW()");
            }
            return db.ExecuteScalar<DateTime>("SELECT GETDATE()");
        }

        #region 基础操作，重写，兼容MySQL
        public new long Add(T model)
        {
            if (IsUseMySQL)
            {
                long? result;
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    result = _connection.InsertMysqlWithLongId<T>(model);
                }
                return result ?? 0;
            }
            else
            {
                return base.Add(model).ToLong();
            }
        }

        public new bool Update(T model)
        {
            if (IsUseMySQL)
            {
                int result;
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    result = _connection.UpdateMysql<T>(model);
                }
                if (result > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return base.Update(model);
            }
        }


        public new void BulkInsert(List<T> models)
        {
            if (IsUseMySQL)
            {
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    if (_connection.State == ConnectionState.Closed)
                        _connection.Open();

                    models.ForEach(model => { _connection.InsertMysqlWithLongId<T>(model); });
                }
            }
            else
            {
                base.BulkInsert(models);
            }
        }
        #endregion

        #region 云消息相关

        /// <summary>
        /// 获取当前需要补偿的记录
        /// </summary>
        /// <param name="query"></param>
        /// <param name="tableName"></param>
        /// <returns></returns>
        public List<T> GetRetryMessageList(CloudMessageQuery query,string tableName)
        {
            if (tableName.IsNullOrEmpty())
                throw new ArgumentNullException("请指定表名");
            var db = DbConnection;
            DateTime dtNow = DateTime.Now;
            var strFields = "*";
            if (query.Fields != null && query.Fields.Any())
                strFields = string.Join(",", query.Fields);

            var parameters = new DynamicParameters();
            parameters.Add("RetryTime", query.RetryTime);
            parameters.Add("NextExecTime", dtNow);

            var sql = $@"SELECT TOP {query.PageSize} {strFields} FROM {tableName} WITH(NOLOCK) WHERE 1=1 
AND Status=0 AND RetryTime < @RetryTime AND NextExecTime<=@NextExecTime 
ORDER BY NextExecTime ASC";
            if (IsUseMySQL)
            {
                sql = $@"SELECT {strFields} FROM {tableName} WHERE 1=1 
AND Status=0 AND RetryTime < @RetryTime AND NextExecTime<=@NextExecTime 
ORDER BY NextExecTime ASC LIMIT {query.PageSize}";
            }
            return db.Query<T>(sql, parameters).ToList();
        }

        /// <summary>
        /// 更新重试次数以及下次执行时间
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="interval">单位：分钟</param>
        /// <returns></returns>
        public int UpdateMessageNextExecTimeAndRetryTime(List<long> ids,string tableName, int interval = 5)
        {
            if (ids == null || ids.Any() == false)
            {
                return 0;
            }
            if (tableName.IsNullOrEmpty())
                throw new ArgumentNullException("请指定表名");

            var db = DbConnection;
            var sql = $@"UPDATE {tableName} SET RetryTime = RetryTime + 1,NextExeTime = DATEADD(MINUTE,{interval}*RetryTime*RetryTime,GETDATE()) WHERE Id IN @ids";
            if (IsUseMySQL)
            {
                sql = $@"UPDATE {tableName} SET RetryTime = RetryTime + 1,NextExeTime = date_add(now(),interval RetryTime * RetryTime * {interval} minute) WHERE Id IN @ids";
            }
            return db.Execute(sql, new { ids });
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        public int UpdateMessageStatus(List<long> ids,string tableName, int status = 1)
        {
            if (ids == null || ids.Any() == false)
            {
                return 0;
            }
            if (tableName.IsNullOrEmpty())
                throw new ArgumentNullException("请指定表名");
            var db = DbConnection;
            var sql = $@"UPDATE {tableName} SET Status=@status,UpdateTime=GETDATE() WHERE Id IN @ids";
            if (IsUseMySQL)
            {
                sql = $@"UPDATE {tableName} SET Status=@status,UpdateTime=now() WHERE Id IN @ids";
            }
            return db.Execute(sql, new { ids, status });
        }
        #endregion
    }
}
