using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Entity;
using Z.Dapper.Plus;
using DianGuanJiaApp.Utility.Extension;
using System.Collections.Concurrent;
using DianGuanJiaApp.Utility;
using System.Data;
using MySql.Data.MySqlClient;
using System.Threading;
using MongoDB.Driver.Core.Configuration;
using System.Web;
using DianGuanJiaApp.Data.Dapper;
using System.Diagnostics;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Data.Enum;

namespace DianGuanJiaApp.Data.Repository
{

    public partial class PaymentStatementRepository : BaseRepository<Entity.PaymentStatement>
    {
        private bool _isUseMysql = true;
        private int _dbNameId = 0;
        private string _connectionString = "";
        public PaymentStatementRepository(int dbNameId = 0)
        {
            _dbNameId = dbNameId;
            if (_dbNameId <= 0)
            {
                var key = $"/FxSystem/PaymentStatement/DbId";
                _dbNameId = new CommonSettingRepository().Get(key, 0)?.Value.ToInt() ?? 0;

                if (_dbNameId > 0 && CustomerConfig.CloudPlatformType == PlatformType.Alibaba.ToString())
                {
                    var rp = new DbConfigRepository();
                    var dbConfig = rp.GetDbConfigModelByDbNameId(_dbNameId);
                    _connectionString = dbConfig?.ConnectionString ?? string.Empty;
                    _isUseMysql = !_connectionString.StartsWith("server");
                }
            }
        }

        public PaymentStatementRepository(string connectionString, int dbNameId = 0) : base(connectionString)
        {
            _isUseMysql = !connectionString.StartsWith("server");
            _connectionString = connectionString;
            _dbNameId = dbNameId;
        }

        private IEnumerable<TReturn> Query<TReturn>(string sql, object param)
        {
            if (CustomerConfig.CloudPlatformType != PlatformType.Alibaba.ToString())
            {
                var db = DbApiAccessUtility.GetApiDb(_dbNameId, "Alibaba");
                return db.Query<TReturn>(sql, param);
            }
            else
            {
                var db = this.DbConnection;
                return db.Query<TReturn>(sql, param);
            }
        }

        private int Execute(string sql, object param)
        {
            if (CustomerConfig.CloudPlatformType != PlatformType.Alibaba.ToString())
            {
                var db = DbApiAccessUtility.GetApiDb(_dbNameId, "Alibaba");
                return db.ExecuteNonQuery(sql, param).ToInt();
            }
            else
            {
                var db = this.DbConnection;
                return db.Execute(sql, param);
            }
        }

        private IDbConnection _db()
        {
            IDbConnection db = null;
            if (string.IsNullOrEmpty(_connectionString) == false && _isUseMysql)
                db = Data.Dapper.DbUtility.GetMySqlConnection(_connectionString);
            else
                db = base.DbConnection;
            return db;
        }

        public new IDbConnection DbConnection
        {
            get
            {
                return _db();
            }
        }

        /// <summary>
        /// 添加（带子表）（精选云）
        /// </summary>
        /// <param name="models"></param>
        public void Save(List<PaymentStatement> models)
        {
            if (models == null || models.Any() == false)
                return;

            var db = _db();
            foreach (var model in models)
            {
                if (db is MySqlConnection)
                {
                    try
                    {
                        db.InsertMysqlWithLongId(model);
                    }
                    catch (Exception ex)
                    {
                        if (ex.Message.ToLower().Contains(" key") == false)
                            throw ex;
                    }
                    if (model.Orders != null && model.Orders.Any())
                    {
                        model.Orders.ForEach(o =>
                        {
                            try
                            {
                                db.InsertMysqlWithLongId(o);
                            }
                            catch (Exception ex)
                            {
                                if (ex.Message.ToLower().Contains(" key") == false)
                                    throw ex;
                            }
                        });
                    }
                }
                else
                {
                    db.Insert(model);
                    if (model.Orders != null && model.Orders.Any())
                    {
                        model.Orders.ForEach(o =>
                        {
                            db.Insert(o);
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 更新（自动区分是否MySQL）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public new int Update(PaymentStatement model)
        {
            var db = DbConnection;
            if (db is MySqlConnection)
                return db.UpdateMysql(model);
            else
                return db.Update(model);
        }

        /// <summary>
        /// 添加采购金退款（精选云）
        /// </summary>
        /// <param name="models"></param>
        public void SaveRefund(PaymentStatementRefund model)
        {
            if (model == null )
                return;

            var db = _db();
            if (db is MySqlConnection)
            {
                db.InsertMysqlWithLongId(model);
            }
            else
            {
                db.Insert(model);
            }
        }

        /// <summary>
        /// 获取列表（内部已根据所在云处理查询）
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFieldNames"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<PaymentStatement> GetList(List<string> codes, string selectFieldNames = "*", string whereFieldName = "OutPayNo")
        {
            var noLock = " WITH(NOLOCK)";
            if (_isUseMysql)
                noLock = "";
            //SQL脚本
            var sql = $"SELECT {selectFieldNames} FROM PaymentStatement {noLock} WHERE {whereFieldName} IN @codes";
            //查询
            var list = this.Query<PaymentStatement>(sql, new { codes }).ToList();

            return list;
        }

        /// <summary>
        /// 获取明细列表
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFieldNames"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<PaymentStatementOrder> GetOrderList(List<string> codes, string selectFieldNames = "*", string whereFieldName = "PaymentStatementOrderCode")
        {
            var noLock = " WITH(NOLOCK)";
            if (_isUseMysql)
                noLock = "";
            //SQL脚本
            var sql = $"SELECT {selectFieldNames} FROM PaymentStatementOrder {noLock} WHERE {whereFieldName} IN @codes";
            //查询
            var list = this.Query<PaymentStatementOrder>(sql, new { codes }).ToList();

            return list;
        }

        /// <summary>
        /// 获取单个（带订单子表）
        /// </summary>
        /// <param name="applyNo"></param>
        /// <param name="isNeedGetOrders">是否带订单子表</param>
        /// <param name="isNeedGetRefunds">是否带退款子表</param>
        /// <returns></returns>
        public PaymentStatement GetByApplyNo(string applyNo, bool isNeedGetOrders = false, bool isNeedGetRefunds = false)
        {
            var noLock = " WITH(NOLOCK)";
            if (_isUseMysql)
                noLock = "";
            //SQL脚本
            var sql = $"SELECT * FROM PaymentStatement {noLock} WHERE ApplyNo=@applyNo";
            //查询
            var model = this.Query<PaymentStatement>(sql, new { applyNo }).FirstOrDefault();
            if (model != null && isNeedGetOrders)
            {
                sql = $"SELECT * FROM PaymentStatementOrder {noLock} WHERE ApplyNo=@applyNo";
                model.Orders = this.Query<PaymentStatementOrder>(sql, new { applyNo }).ToList();
            }
            if (model != null && isNeedGetRefunds)
            {
                sql = $"SELECT * FROM PaymentStatementRefund {noLock} WHERE ApplyNo=@applyNo";
                model.Refunds = this.Query<PaymentStatementRefund>(sql, new { applyNo }).ToList();
            }

            return model;
        }

        /// <summary>
        /// 获取单个退款
        /// </summary>
        /// <param name="refundNo"></param>
        /// <returns></returns>
        public PaymentStatementRefund GetRefundByRefundNo(string refundNo)
        {
            var noLock = " WITH(NOLOCK)";
            if (_isUseMysql)
                noLock = "";
            //SQL脚本
            var sql = $"SELECT * FROM PaymentStatementRefund {noLock} WHERE RefundNo=@refundNo";
            //查询
            var model = this.Query<PaymentStatementRefund>(sql, new { refundNo }).FirstOrDefault();

            return model;
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="applyNo"></param>
        /// <param name="status"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public int UpdateStatus(string applyNo, int status, string whereFieldName = "ApplyNo")
        {
            var sql = $"UPDATE PaymentStatement SET Status={status},PayResultTime='{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}' WHERE {whereFieldName} = @applyNo";
            return this.Execute(sql, new { applyNo });
        }

        /// <summary>
        /// 更新总退款成功金额
        /// </summary>
        /// <param name="applyNo"></param>
        /// <param name="refundedAmount"></param>
        /// <returns></returns>
        public int UpdateTotalRefundSuccessAmount(string applyNo, decimal refundedAmount)
        {
            var sql = $"UPDATE PaymentStatement SET TotalRefundSuccessAmount={refundedAmount},UpdateTime='{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}' WHERE ApplyNo = @applyNo";
            return this.Execute(sql, new { applyNo });
        }


        /// <summary>
        /// 更新：最后更新时间
        /// </summary>
        /// <param name="applyNo"></param>
        /// <returns></returns>
        public int UpdateLastUpdateTime(string applyNo)
        {
            var sql = $"UPDATE PaymentStatement SET UpdateTime='{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}' WHERE ApplyNo = @applyNo";
            return this.Execute(sql, new { applyNo });
        }

        /// <summary>
        /// 更新退款状态
        /// </summary>
        /// <param name="refundNo"></param>
        /// <param name="status"></param>
        /// <param name="refundedAmount">已退金额</param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public int UpdateRefundStatus(string refundNo, int status, decimal? refundedAmount = null, string whereFieldName = "RefundNo")
        {
            var sql = $"UPDATE PaymentStatementRefund SET RefundStatus={status}{(refundedAmount.HasValue? ",refundedAmount="+ refundedAmount : "")},RefundResultTime='{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}' WHERE {whereFieldName} = @refundNo";
            return this.Execute(sql, new { refundNo });
        }

        /// <summary>
        /// 获取渠道支付统计
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="payType">1支出/ 0收入（当前用户厂家或商家）</param>
        /// <param name="supplierUserId">厂家用户id</param>
        /// <param name="agentUserId">商家用户id</param>
        /// <returns>Item1 == 渠道，item2 渠道付款总计</returns>
        public List<PaymentStatementStatisticsModel> PaymentStatementStatistics(PaymentStatementsQuerySearchModel queryModel, int fxUserId)
        {
            var noLock = " WITH(NOLOCK)";
            if (_isUseMysql)
            {
                noLock = "";
            }

            var where = string.Empty;
            if (queryModel.PayType == "1")//支出
                where += " and SourceFxUserId = @curFxUserId";
            else
                where += " and PurchaseFxUserId = @curFxUserId";
            where += " and CreateTime between @startTime and @endTime";


            dynamic param = new System.Dynamic.ExpandoObject();
            param.curFxUserId = fxUserId;
            param.startTime = queryModel.StartTime;
            param.endTime = queryModel.EndTime;
            if(queryModel.SupplierFxUserIds?.Any() == true)
            {
                var total = (int)Math.Ceiling(queryModel.SupplierFxUserIds.Count / 500M);
                string tempSql = string.Empty;
                for (var i = 0; i < total; i++)
                {
                    var ids = queryModel.SupplierFxUserIds.Skip(i * 500).Take(500).ToList();
                    tempSql += $"or PurchaseFxUserId in({string.Join(",",ids)})";
                }
                where += $" and ({tempSql.TrimStart("or")})";
            }
            if(queryModel.AgentFxUserIds?.Any() == true)
            {
                var total = (int)Math.Ceiling(queryModel.AgentFxUserIds.Count / 500M);
                string tempSql = string.Empty;
                for (var i = 0; i < total; i++)
                {
                    var ids = queryModel.AgentFxUserIds.Skip(i * 500).Take(500).ToList();
                    tempSql += $"or SourceFxUserId in({string.Join(",",ids)}) ";
                }
                where += $" and ({tempSql.TrimStart("or")})";
            }

            var commandText = $"select PayChannel,sum(OrderTotalPaySuccessAmount) ChannelTotalPay from PaymentStatement ps {noLock} where 1=1 {where} group by PayChannel";
            return this.Query<PaymentStatementStatisticsModel>(commandText, (object)param).ToList();
        }


        /// <summary>
        /// 支付批次记录查询
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="fxUserId">当前登录的用户</param>
        /// <returns></returns>
        public Tuple<int,List<PaymentStatement>> PaymentStatementList(PaymentStatementsQuerySearchModel queryModel,int fxUserId)
        {
            var noLock = " WITH(NOLOCK)";
            var pageCommandText = $" offset {(queryModel.PageIndex - 1) * queryModel.PageSize} rows fetch next {queryModel.PageSize} row only ";
            if (_isUseMysql)
            {
                noLock = "";
                pageCommandText = $" limit {(queryModel.PageIndex - 1) * queryModel.PageSize},{queryModel.PageSize}";
            }
            if (queryModel.NotNeedPagging == true)
            {
                pageCommandText = string.Empty;
            }
            
            string where = string.Empty;

            dynamic param = new System.Dynamic.ExpandoObject();
            param.curFxUserId = fxUserId;
            param.startTime = queryModel.StartTime;
            param.endTime = queryModel.EndTime;

            if (queryModel.PayType == "1")//支出
                where += " and ps.SourceFxUserId = @curFxUserId";
            else//收入只查成功的
            {
                where += " and ps.PurchaseFxUserId = @curFxUserId";
                where += " and ps.OrderSuccessNums > 0";
            }
            where += " and ps.CreateTime between @startTime and @endTime";

            if (!string.IsNullOrEmpty(queryModel.PayNo))
            {
                param.payNo = queryModel.PayNo.Split(',', '，');
                where += " and ps.PayNo in @payNo";
            }if (!string.IsNullOrEmpty(queryModel.ApplyNo))
            {
                param.applyNo = queryModel.ApplyNo.Split(',', '，');
                where += " and ps.ApplyNo in @applyNo";
            }
            if (!string.IsNullOrEmpty(queryModel.OutBizNo))
            {
                param.OutBizNo = queryModel.OutBizNo.Split(',', '，');
                where += " and ps.OutBizNo in @OutBizNo";
            }
            
            if (queryModel.BatchPayStatus == "1")
                where += " and ps.Status = 1 and (ps.OrderTotalNums = ps.OrderSuccessNums)";
            if (queryModel.BatchPayStatus == "3")//部分成功
                where += " and ps.Status = 1 and (ps.OrderTotalNums != ps.OrderSuccessNums and ps.OrderSuccessNums > 0)";
            if (queryModel.BatchPayStatus == "2")
                where += " and ps.Status != 0 and (ps.Status = -1 or ps.OrderSuccessNums = 0)";
            
            

            #region 关联明细表，搜索逻辑单号、平台单号
            string joinTable = string.Empty;
            if (!string.IsNullOrEmpty(queryModel.PlatformOrderId))
            {
                param.platformOrderId = queryModel.PlatformOrderId.Split(',','，');
                where += " and pso.SourcePlatformOrderId in @platformOrderId";
            }
            if (!string.IsNullOrEmpty(queryModel.LogicOrderId))
            {
                param.logicOrderId = queryModel.LogicOrderId.Split(',', '，');
                where += " and pso.SourceLogicOrderId in @logicOrderId";
            }
            if(!string.IsNullOrEmpty(queryModel.PlatformOrderId) || !string.IsNullOrEmpty(queryModel.LogicOrderId))
            {
                joinTable = $"inner join P_PaymentStatementOrder pso {noLock} on ps.OutPayNo = pso.OutPayNo";
            }
            #endregion

            #region 商家，厂家账号搜索
            if (queryModel.SupplierFxUserIds?.Any() == true)
            {
                var total = (int)Math.Ceiling(queryModel.SupplierFxUserIds.Count / 500M);
                string tempSql = string.Empty;
                for (var i = 0; i < total; i++)
                {
                    var ids = queryModel.SupplierFxUserIds.Skip(i * 500).Take(500).ToList();
                    tempSql += $"or ps.PurchaseFxUserId in({string.Join(",", ids)})";
                }
                where += $" and ({tempSql.TrimStart("or")})";
            }
            if (queryModel.AgentFxUserIds?.Any() == true)
            {
                var total = (int)Math.Ceiling(queryModel.AgentFxUserIds.Count / 500M);
                string tempSql = string.Empty;
                for (var i = 0; i < total; i++)
                {
                    var ids = queryModel.AgentFxUserIds.Skip(i * 500).Take(500).ToList();
                    tempSql += $"or ps.SourceFxUserId in({string.Join(",", ids)}) ";
                }
                where += $" and ({tempSql.TrimStart("or")})";
            }
            #endregion

            string orderBySql = "order by CreateTime desc";
            if (queryModel.OrderByField == "PayResultTime")
            {
                orderBySql = "order by PayResultTime desc";
            }

            #region 拼接执行查询
            string commandText = $"select ps.OutBizNo from PaymentStatement ps {noLock} {joinTable} where 1=1 {where}";
            var pageSql = $@"select * from PaymentStatement {noLock} where OutBizNo in ({commandText}) {orderBySql} {pageCommandText};";
            var countSql = $@"select count(distinct OutBizNo) from PaymentStatement {noLock} where OutBizNo in ({commandText});";
            var count = this.Query<int>(countSql, (object)param).FirstOrDefault();
            var list = this.Query<PaymentStatement>(pageSql, (object)param).ToList();
            #endregion

            return Tuple.Create(count, list);
        }


        /// <summary>
        /// 支付批次记录查询
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="fxUserId">当前登录的用户</param>
        /// <returns></returns>
        public Tuple<int, List<PaymentStatementOrder>> PaymentStatementOrderList(PaymentStatementsQuerySearchModel queryModel, int fxUserId)
        {
            var noLock = " WITH(NOLOCK)";
            var pageCommandText = $" offset {(queryModel.PageIndex - 1) * queryModel.PageSize} rows fetch next {queryModel.PageSize} row only ";
            if (_isUseMysql)
            {
                noLock = "";
                pageCommandText = $" limit {(queryModel.PageIndex - 1) * queryModel.PageSize},{queryModel.PageSize}";
            }
            if (queryModel.NotNeedPagging == true)
            {
                pageCommandText = string.Empty;
            }

            string where = string.Empty;

            dynamic param = new System.Dynamic.ExpandoObject();
            param.curFxUserId = fxUserId;
            if(queryModel.PayType != null)
            {
                if (queryModel.PayType == "1")
                    where += " and pso.SourceFxUserId = @curFxUserId";
                else
                {
                    where += " and pso.PurchaseFxUserId = @curFxUserId";
                    where += " and pso.PayStatus = 1";
                }
            }


            if (!string.IsNullOrEmpty(queryModel.OutPayNo))
            {
                param.OutPayNo = queryModel.OutPayNo;
                where += " and pso.OutPayNo = @OutPayNo";
            }
            if (queryModel.IsApplyedRepayment != null)
            {
                where += $" and pso.IsApplyedRepayment = {(queryModel.IsApplyedRepayment.Value ? "1" : "0")}";
            }

            if (!string.IsNullOrEmpty(queryModel.PayStatus))
            {
                param.PayStatus = queryModel.PayStatus.Split(',','，');
                where += " and pso.PayStatus in @PayStatus";
            }

            #region 搜索逻辑单号、平台单号
            if (!string.IsNullOrEmpty(queryModel.PlatformOrderId))
            {
                param.platformOrderId = queryModel.PlatformOrderId.Split(',', '，');
                where += " and pso.SourcePlatformOrderId in @platformOrderId";
            }
            if (!string.IsNullOrEmpty(queryModel.LogicOrderId))
            {
                param.logicOrderId = queryModel.LogicOrderId.Split(',', '，');
                where += " and pso.SourceLogicOrderId in @logicOrderId";
            }
            if (!string.IsNullOrEmpty(queryModel.ApplyNo))
            {
                param.ApplyNo = queryModel.ApplyNo.Split(',', '，');
                where += " and pso.ApplyNo in @ApplyNo";
            }
            #endregion

            #region 拼接执行查询
            string commandText = $"select pso.* from PaymentStatementOrder pso {noLock} " +
                                 $"inner join PaymentStatement ps {noLock} on ps.ApplyNo = pso.ApplyNo where 1=1 {where}";
            var pageSql = $@"{commandText} order by CreateTime desc {pageCommandText};";
            var countSql = $@"select count(1) from ({commandText}) as temp;";
            var count = this.Query<int>(countSql, (object)param).FirstOrDefault();
            var list = this.Query<PaymentStatementOrder>(pageSql, (object)param).ToList();
            #endregion

            return Tuple.Create(count, list);
        }
        /// <summary>
        /// 支付失败记录查询
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="fxUserId">当前登录的用户</param>
        /// <returns></returns>
        public Tuple<int, List<PaymentStatementOrder>,int,decimal> PaymentStatementFialdOrderList(PaymentStatementsQuerySearchModel queryModel, int fxUserId)
        {
            var noLock = " WITH(NOLOCK)";
            var pageCommandText = $" offset {(queryModel.PageIndex - 1) * queryModel.PageSize} rows fetch next {queryModel.PageSize} row only ";
            if (_isUseMysql)
            {
                noLock = "";
                pageCommandText = $" limit {(queryModel.PageIndex - 1) * queryModel.PageSize},{queryModel.PageSize}";
            }
            
            string where = string.Empty;

            dynamic param = new System.Dynamic.ExpandoObject();
            param.curFxUserId = fxUserId;
            where += " and pso.SourceFxUserId = @curFxUserId";
            if (!string.IsNullOrEmpty(queryModel.OutPayNo))
            {
                param.OutPayNo = queryModel.OutPayNo;
                where += " and pso.OutPayNo = @OutPayNo";
            }
            if (queryModel.IsApplyedRepayment != null)
            {
                where += $" and pso.IsApplyedRepayment = {(queryModel.IsApplyedRepayment.Value ? "1" : "0")}";
            }

            if (!string.IsNullOrEmpty(queryModel.PayStatus))
            {
                param.PayStatus = queryModel.PayStatus.Split(',','，');
                where += " and pso.PayStatus in @PayStatus";
            }

            #region 搜索逻辑单号、平台单号
            if (!string.IsNullOrEmpty(queryModel.PlatformOrderId))
            {
                param.platformOrderId = queryModel.PlatformOrderId.Split(',', '，');
                where += " and pso.SourcePlatformOrderId in @platformOrderId";
            }
            if (!string.IsNullOrEmpty(queryModel.LogicOrderId))
            {
                param.logicOrderId = queryModel.LogicOrderId.Split(',', '，');
                where += " and pso.SourceLogicOrderId in @logicOrderId";
            }
            if (!string.IsNullOrEmpty(queryModel.ApplyNo))
            {
                param.ApplyNo = queryModel.ApplyNo.Split(',', '，');
                where += " and pso.ApplyNo in @ApplyNo";
            }
            #endregion

            #region 拼接执行查询
            string commandText = $"select pso.* from PaymentStatementOrder pso {noLock} " +
                                 $"inner join PaymentStatement ps {noLock} on ps.ApplyNo = pso.ApplyNo where 1=1 {where}";
            var pageSql = $@"{commandText} order by CreateTime desc {pageCommandText};";
            var countSql = $@"select ifnull(sum(cnt),0) cnt,count(1) totalOrderCnt,ifnull(sum(totalAmount),0) totalAmount from (
                                select PurchasePlatformOrderId,count(1) cnt,sum(PurchaseApplyTotalAmount) totalAmount from ({commandText}) as temp
                                group by PurchasePlatformOrderId) as temp2 ; ";
            var cntModel = this.Query<dynamic>(countSql, (object)param).FirstOrDefault();
            var list = this.Query<PaymentStatementOrder>(pageSql, (object)param).ToList();
            #endregion

            return Tuple.Create((int)cntModel.cnt, list,(int)cntModel.totalOrderCnt,(decimal)cntModel.totalAmount); ;
        }

        /// <summary>
        /// 更新是否发起重新支付
        /// </summary>
        public int UpdateApplyedRepayment(List<string> paymentStatementOrderCode, bool isApplyedRepayment, string whereFieldName = "PaymentStatementOrderCode")
        {
            var sql = $"UPDATE PaymentStatementOrder SET isApplyedRepayment={(isApplyedRepayment ? 1 : 0)} WHERE {whereFieldName} = @paymentStatementOrderCode";
            return this.Execute(sql, new { paymentStatementOrderCode });
        }

        /// <summary>
        /// 更新支付失败的订单发起重新支付状态
        /// </summary>
        public int UpdateFaildApplyedRepaymentByPurcharOrderId(List<string> purcharOrderIds, bool isApplyedRepayment, int fxUserId)
        {
            var sql = $"UPDATE PaymentStatementOrder SET isApplyedRepayment={(isApplyedRepayment ? 1 : 0)} WHERE PurchasePlatformOrderId = @purcharOrderIds and SourceFxUserId = @fxUserId and PayStatus in(-1,-2)";
            return this.Execute(sql, new { purcharOrderIds, fxUserId });
        }

        /// <summary>
        /// 获取当前需要重新更新采购金预充值结果的记录
        /// </summary>
        /// <param name="fields"></param>
        /// <returns></returns>
        public List<PaymentStatement> GetRetryPaymentStatement(int retryTime = 3,string fields = "ps.ApplyNo,ps.SourceFxUserId")
        {
            DateTime now = DateTime.Now;

            var noLock = " WITH(NOLOCK)";
            var pageCommandText = $" offset 0 rows fetch next 100 row only ";
            if (_isUseMysql)
            {
                noLock = "";
                pageCommandText = $" limit 0,100";
            }
            string commandText = $"select {fields} from PaymentStatement ps {noLock} where 1=1 " +
                                 $"and ps.Status=0 and ps.RetryTime < {retryTime} and ps.NextExeTime<='{now.ToString("yyyy-MM-dd HH:mm:ss")}' " +
                                 $"order by ps.NextExeTime asc";
            return this.Query<PaymentStatement>(commandText, null).ToList();
        }
        /// <summary>
        /// 获取当前需要重新更新采购金预充值结果的记录
        /// </summary>
        /// <param name="fields"></param>
        /// <returns></returns>
        public List<PaymentStatementRefund> GetRetryPaymentStatementRefund(int retryTime = 3, string fields = "psr.ApplyNo,psr.RefundNo,psr.OutRefundNo")
        {
            DateTime now = DateTime.Now;
            var noLock = " WITH(NOLOCK)";
            var pageCommandText = $" offset 0 rows fetch next 100 row only ";
            if (_isUseMysql)
            {
                noLock = "";
                pageCommandText = $" limit 0,100";
            }
            string commandText = $"select {fields} from PaymentStatementRefund psr {noLock} where 1=1 " +
                                 $"and psr.RefundStatus=0 and psr.RetryTime < {retryTime} and psr.NextExeTime<='{now.ToString("yyyy-MM-dd HH:mm:ss")}' " +
                                 $"order by psr.NextExeTime asc";
            return this.Query<PaymentStatementRefund>(commandText,new { }).ToList();
        }
        /// <summary>
        /// 获取需要申请退款的数据
        /// </summary>
        /// <param name="fields"></param>
        /// <returns></returns>
        public List<PaymentStatement> GetPaymentStatementRefundApply(DateTime startDateTime, string fields = "*")
        {
            var noLock = " WITH(NOLOCK)";
            var pageCommandText = $" offset 0 rows fetch next 100 row only ";
            if (_isUseMysql)
            {
                noLock = "";
                pageCommandText = $" limit 0,100";
            }
            string commandText = $"select {fields} from PaymentStatement ps {noLock} where 1=1 " +
                                 $"and ps.Status=1 and ps.CreateTime>'{startDateTime.ToString("yyyy-MM-dd HH:mm:ss")}' and (PayedAmount-OrderTotalPaySuccessAmount-TotalRefundSuccessAmount)>0 " +
                                 $"order by Id asc";
            return this.Query<PaymentStatement>(commandText, null).ToList();
        }
        /// <summary>
        /// 更新下次重试次数以及下次执行时间
        /// </summary>
        /// <param name="applyNos"></param>
        /// <returns></returns>
        public int UpdatePaymentStatementNextExeTimeAndRetryTime(List<string> applyNos,int interval = 5)
        {
            if(applyNos?.Any() != true)
            {
                return 0;
            }
            DateTime now = DateTime.Now;
            string commandText = $"update PaymentStatement set RetryTime = RetryTime + 1,NextExeTime = date_add(now(),interval RetryTime * RetryTime * {interval} minute) " +
                                $"where  ApplyNo in @applyNos";
            return this.Execute(commandText,new { applyNos });
        }

        /// <summary>
        /// 更新退款下次重试次数以及下次执行时间
        /// </summary>
        /// <param name="applyNos"></param>
        /// <param name="interval">单位：分钟</param>
        /// <returns></returns>
        public int UpdatePaymentStatementRefundNextExeTimeAndRetryTime(List<string> outRefundNos, int interval = 5)
        {
            if (outRefundNos?.Any() != true)
            {
                return 0;
            }
            DateTime now = DateTime.Now;
            string commandText = $"update PaymentStatementRefund set RetryTime = RetryTime + 1,NextExeTime = date_add(now(),interval RetryTime * RetryTime * {interval} minute) " +
                                $"where  OutRefundNo in @outRefundNos";
            return this.Execute(commandText, new { outRefundNos });
        }

        /// <summary>
        /// 更新明细供应商备注，
        /// </summary>
        /// <param name="curFxUserId">当前登录的用户</param>
        /// <returns></returns>
        public int UpdateOrderSupplierRemark(string paymentStatementOrderCode, string supplierRemark,int curFxUserId)
        {
            var sql = $"UPDATE PaymentStatementOrder SET SupplierRemark=@supplierRemark where PaymentStatementOrderCode = @paymentStatementOrderCode and PurchaseFxUserId = @curFxUserId";
            return this.Execute(sql, new { paymentStatementOrderCode, supplierRemark, curFxUserId });
        }

        public List<PaymentStatementRefund> GetRefundList(List<string> codes, string fields = "*",string whereFieldName = "ApplyNo")
        {
            if(codes?.Any() != true)
            {
                return new List<PaymentStatementRefund>();
            }
            var noLock = " WITH(NOLOCK)";
            if (_isUseMysql)
                noLock = "";
            //SQL脚本
            var sql = $"SELECT {fields} FROM PaymentStatementRefund {noLock} WHERE {whereFieldName} IN @codes";
            //查询
            var list = this.Query<PaymentStatementRefund>(sql, new { codes }).ToList();

            return list;
        }

    }
}
