using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using MySql.Data.MySqlClient;

namespace DianGuanJiaApp.Data.Repository
{

    public partial class PrintHistoryDataRepository : BaseRepository<Entity.PrintHistoryData>
    {
        private readonly bool _isUseMysql;
        private readonly PrintHistoryDbConfigRepository _dbConfigRepo = new PrintHistoryDbConfigRepository();
        private readonly PrintHistoryDbConfigRepository _dbConfigRepoOld = new PrintHistoryDbConfigRepository(CustomerConfig.ConfigureDbConnectionString);
        private readonly bool _isAllUseJdMysqlDb;
        private readonly bool _isJd;

        private string GetDefaultDbIdKey()
        {
            return $"/System/Fendan/PrintHistoryData/DefaultDbId/{CustomerConfig.CloudPlatformType}Cloud";
        }

        public PrintHistoryDataRepository()
        {
            _isUseMysql = IsUseMysqlDb();
            _isAllUseJdMysqlDb = IsAllUseJdMysqlDb();
            _isJd = CustomerConfig.CloudPlatformType == nameof(CloudPlatformType.Jingdong);
        }

        /// <summary>
        /// 打印记录是否使用mysql数据，若已经配置了mysql数据库链接，则认为启用
        /// </summary>
        /// <returns></returns>
        public static bool IsUseMysqlDb()
        {
            // 京东平台单独处理，等全部发版上线后，才使用MySQL
            if (CustomerConfig.CloudPlatformType == nameof(CloudPlatformType.Jingdong))
            {
                // 2代表全部发版上线，0代表兼容开关关闭（可以直接使用MySQL）
                // 1代表兼容开关打开，但是未全部发版上线
                var value = GetTempUseJdMysqlValue();
                return value == "0" || value == "2";
            }
            
            //阿里云平台全部用的MYSQL，可以直接返回true，其他云平台根据配置来
            if ((CustomerConfig.CloudPlatformType == "Alibaba" || CustomerConfig.CloudPlatformType == "TouTiao" ||
                 CustomerConfig.CloudPlatformType == "Pinduoduo") && !CustomerConfig.IsLocalDbDebug)
                return true;
            else
                return string.IsNullOrEmpty(CustomerConfig.PrintHistoryDefaultMysqlConnectionString) == false;
        }

        /// <summary>
        /// 根据店铺id获取店铺保存重打数据的数据库连接
        /// </summary>
        /// <param name="dbConfig"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        private IDbConnection _db(PrintHistoryDbConfig dbConfig, int shopId)
        {
            //1.不使用mysql存储重打数据，则直接返回sqlserver 业务库连接
            if (_isUseMysql == false)
                return DbConnection;

            // 如果是京东平台，单独使用京东的打印数据存储库连接
            if (_isJd)
            {
                var connStr = GetJdMysqlDbStr();
                return DbUtility.GetMySqlConnection(connStr);
            }

            //2.读取用户配置的打印数据存储库连接
            if (dbConfig == null)
            {
                //3.新用户第一次保存重打数据，读不到配置，则读取当前配置的默认数据库，并初始化用户的分库配置
                // 250113，新增新库配置，初始化时优先使用新库配置
                var defaultDbId = _dbConfigRepo.GetHashDefaultDbId(shopId);
                var dbIdKey = GetDefaultDbIdKey();
                var defaultDb = new CommonSettingRepository().Get(dbIdKey, 0);
                if (defaultDbId == 0 && (defaultDb == null || string.IsNullOrWhiteSpace(defaultDb.Value) || defaultDb.Value.ToInt() == 0))
                    throw new LogicException("重打数据存储Mysql默认DbId未配置");
                //创建用户的默认分库配置
                defaultDbId = defaultDbId == 0 ? defaultDb.Value.ToInt() : defaultDbId;
                dbConfig = _dbConfigRepo.CreateDbConfig(shopId, defaultDbId);
            }

            if (dbConfig == null)
                throw new Exception($"初始化店铺【{shopId}】的重打数据保存的数据库配置时发生错误");

            //4.实例化数据库连接
            var db = DbUtility.GetMySqlConnection(dbConfig.ConnectionString);
            return db;
        }

        /// <summary>
        /// 监控程序 使用
        /// </summary>
        public void GetAllPrintHistoryDataMaxIds()
        {
            var dbNameConfigs = _dbConfigRepo.GetAllPrintHistoryDbNameConfigs();
            var dic = new Dictionary<string, int>();
            var maxIdModels = new List<MaxIdModel>();
            foreach (var dbNameConfig in dbNameConfigs)
            {
                try
                {
                    //4.实例化数据库连接
                    var conn = dbNameConfig.DbServer.ConnectionString.Replace("$database$", dbNameConfig.DbName);
                    var db = Data.Dapper.DbUtility.GetMySqlConnection(conn);
                    var sql = $"SELECT Id FROM `p_printhistorydata` order by id desc LIMIT 1";
                    var id = db.Query<int>(sql).FirstOrDefault();
                    //if (dic.ContainsKey(dbNameConfig.DbName) == false)
                    //    dic.Add(dbNameConfig.DbName, id);
                    var model = new MaxIdModel
                    {
                        MaxId = id,
                        DbKey = dbNameConfig.DbName,
                        TableName = "p_printhistorydata"
                    };
                    maxIdModels.Add(model);
                }
                catch (Exception ex)
                {
                    Log.Debug($"数据库：{dbNameConfig.ToJson()}，查询Id最大值异常：{ex}");
                }
            }

            //var json = dic.ToList().OrderByDescending(x => x.Value).ToJson();
            //Log.Debug($"打印记录数据表最大id值：{json}");
            Log.WriteLine($"打印记录数据表最大id值：{maxIdModels.OrderByDescending(x => x.MaxId).ToJson()}", "PrintDataMaxId.txt");
        }

        public void CreatePrintHistoryDataNewTable()
        {
            var dbNameConfigs = _dbConfigRepo.GetAllPrintHistoryDbNameConfigs();
            var dic = new Dictionary<string, int>();
            foreach (var dbNameConfig in dbNameConfigs)
            {
                try
                {
                    //4.实例化数据库连接
                    var conn = dbNameConfig.DbServer.ConnectionString.Replace("$database$", dbNameConfig.DbName);
                    var db = Data.Dapper.DbUtility.GetMySqlConnection(conn);
                    var sql = $@"
CREATE TABLE `P_PrintHistoryDataNew` (
	`Id` BIGINT  NOT NULL AUTO_INCREMENT,
	`SendContent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
	`PrintDataJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
	`ShopId` int NOT NULL,
	`RelationCode` char(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
	`CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (`Id`),
	KEY `IX_RelationCode`(`RelationCode`) USING BTREE,
	KEY `IX_CreateTime`(`CreateTime`) USING BTREE
) ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;";
                    var id = db.Execute(sql);
                    if (dic.ContainsKey(dbNameConfig.DbName) == false)
                        dic.Add(dbNameConfig.DbName, id);
                }
                catch (Exception ex)
                {
                    Log.Debug($"数据库：{dbNameConfig.ToJson()}，创建新表异常：{ex}");
                }
            }

            var json = dic.ToList().OrderByDescending(x => x.Value).ToJson();
            Log.Debug($"打印记录创建新表：{json}");
        }

        /// <summary>
        /// 批量添加打印数据，按店铺分组并处理新旧库和备库逻辑
        /// </summary>
        /// <param name="list"></param>
        public void AddList(IEnumerable<PrintHistoryData> list)
        {
            // 1. 按店铺分组
            var groupList = list.GroupBy(f => f.ShopId).ToList();

            // 2. 获取店铺的分库配置
            var sids = groupList.Select(f => f.Key).ToList();
            var dbConfigList = _dbConfigRepo.GetDbConfigModels(sids);

            // 3. 构建分库配置字典
            var dbConfigDict = dbConfigList.GroupBy(f => f.ShopId)
                .ToDictionary(f => f.Key, f => f.Where(x => !x.IsDisabled).ToList());

            // 从旧配置库获取兜底配置
            var missingSids = sids.Where(sid => !dbConfigDict.ContainsKey(sid)).ToList();
            var oldDbConfigs = missingSids.Any() && _isUseMysql
                ? _dbConfigRepoOld.GetDbConfigModels(missingSids).Where(x => !x.IsDisabled).ToList()
                : new List<PrintHistoryDbConfig>();
            var oldDbConfigDic = oldDbConfigs
                .GroupBy(f => f.ShopId)
                .ToDictionary(f => f.Key, f => f.ToList());

            // 4. 遍历分组，处理打印数据
            foreach (var group in groupList)
            {
                var sid = group.Key;
                var printDataList = group.ToList();

                // 获取分库配置
                PrintHistoryDbConfig oldDbConfig;
                var dbConfig = GetDbConfig(sid, dbConfigDict, oldDbConfigDic, out oldDbConfig);
                if (dbConfig != null && dbConfig.IsUseNewDbConfigId == false) dbConfig = oldDbConfig;
                // 获取数据库连接
                var db = _db(dbConfig, sid);

                // 批量插入打印数据
                foreach (var item in printDataList)
                {
                    try
                    {
                        InsertPrintHistory(db, item);
                    }
                    catch (Exception ex)
                    {
                        HandleInsertionException(db, item, ex);
                    }
                }
            }
        }

        /// <summary>
        /// 插入打印数据到数据库
        /// </summary>
        /// <param name="db">数据库连接</param>
        /// <param name="item">打印数据</param>
        private void InsertPrintHistory(IDbConnection db, PrintHistoryData item)
        {
            const string sql = @"
        INSERT INTO P_PrintHistoryDataNew(SendContent, PrintDataJson, ShopId, RelationCode)
        VALUES(@SendContent, @PrintDataJson, @ShopId, @RelationCode)";
            Log.Debug(() => $"插入时的数据库连接：{db.ConnectionString}");
            db.Execute(sql, item);
        }

        /// <summary>
        /// 处理插入异常
        /// </summary>
        /// <param name="db"></param>
        /// <param name="item"></param>
        /// <param name="ex"></param>
        private void HandleInsertionException(IDbConnection db, PrintHistoryData item, Exception ex)
        {
            var exMsg = ex.Message.ToString2();
            if (_isUseMysql && exMsg.Contains("Incorrect string value") &&
                (exMsg.Contains("SendContent") || exMsg.Contains("PrintDataJson")))
            {
                // 移除特殊字符并重试
                item.PrintDataJson = item.PrintDataJson.ToString2().RemoveFourChar();
                item.SendContent = item.SendContent.ToString2().RemoveFourChar();
                InsertPrintHistory(db, item);
            }
            else
            {
                // 尝试使用备库
                HandleBackupDbInsertion(item, ex);
            }
        }

        /// <summary>
        /// 插入打印数据到备库
        /// </summary>
        /// <param name="item"></param>
        /// <param name="ex"></param>
        /// <exception cref="Exception"></exception>
        private void HandleBackupDbInsertion(PrintHistoryData item, Exception ex)
        {
            // 尝试使用备库
            var backupDb = GetBackupDbConnection();
            if (backupDb != null)
            {
                try
                {
                    InsertPrintHistory(backupDb, item);
                }
                catch (Exception e)
                {
                    Log.WriteError($"插入打印数据到备库失败：{e.Message}");
                    var exMsg = e.Message.ToString2();
                    if (_isUseMysql && exMsg.Contains("Incorrect string value") && (exMsg.Contains("SendContent") || exMsg.Contains("PrintDataJson")))
                    {
                        // mysql存储需要处理特殊字符 移除特殊字符
                        item.PrintDataJson = item.PrintDataJson.ToString2().RemoveFourChar();
                        item.SendContent = item.SendContent.ToString2().RemoveFourChar();
                        InsertPrintHistory(backupDb, item);
                    }
                    else throw;
                }
            }
            else
            {
                Log.WriteError($"插入打印数据到打印备库失败：{ex.Message}");
                throw ex;
            }
        }

        /// <summary>
        /// 查询打印数据（有使用MySql进行查询）
        /// </summary>
        /// <param name="historyList"></param>
        /// <returns></returns>
        public void SetHistoryPrintData(List<PrintHistory> historyList)
        {
            if (historyList == null || historyList.Any() == false)
                return;

            //重打数据已经存在，则不再额外查询
            if (historyList.All(f => string.IsNullOrWhiteSpace(f.PrinterJsonData) == false))
                return;

            if (_isUseMysql == false || (_isAllUseJdMysqlDb == false && _isJd))
            {
                //设置打印历史
                SetPrintHistory(DbConnection, historyList);
                return;
            }

            // 京东平台单独处理
            if (_isJd)
            {
                var db = _db(null, 0);
                SetPrintHistory(db, historyList, null);
                return;
            }

            //按店铺分组去分库中查询重打数据
            var groupList = historyList.Where(f => string.IsNullOrWhiteSpace(f.PrinterJsonData)).GroupBy(f => f.ShopId)
                .ToList();

            // 查询分库配置
            var sids = groupList.Select(f => f.Key).ToList();

            // 从新配置库获取配置
            var dbConfigList = _dbConfigRepo.GetDbConfigModels(sids);
            var dbConfigLookup = dbConfigList
                .GroupBy(f => f.ShopId)
                .ToDictionary(f => f.Key, f => f.ToList());

            // 从旧配置库获取兜底配置
            var missingSids = sids.Where(sid => !dbConfigLookup.ContainsKey(sid)).ToList();
            var oldDbConfigs = missingSids.Any()
                ? _dbConfigRepoOld.GetDbConfigModels(missingSids).Where(x => !x.IsDisabled).ToList()
                : new List<PrintHistoryDbConfig>();
            var oldDbConfigLookup = oldDbConfigs
                .GroupBy(f => f.ShopId)
                .ToDictionary(f => f.Key, f => f.ToList());

            // 遍历分组
            foreach (var group in groupList)
            {
                var sid = group.Key;
                var printHistoryList = group.ToList();

                // 获取分库配置
                PrintHistoryDbConfig oldDbConfig;
                var dbConfig = GetDbConfig(sid, dbConfigLookup, oldDbConfigLookup, out oldDbConfig);

                // 确认打印历史分库
                var db = _db(dbConfig, sid);

                // 设置打印历史
                SetPrintHistory(db, printHistoryList, oldDbConfig);

                // 兼容拼多多逻辑
                var printHistories = printHistoryList.Where(m => m.PrintHistoryData == null).ToList();
                if (CustomerConfig.CloudPlatformType == "Pinduoduo" && printHistories.Any()) SetPrintHistory(DbConnection, printHistories);

                // 如果所有打印数据已设置，退出循环
                if (printHistoryList.All(f => !string.IsNullOrWhiteSpace(f.PrinterJsonData)))
                    break;
            }
        }

        /// <summary>
        /// 获取分库配置，优先使用新库配置，兜底为旧库配置
        /// </summary>
        /// <param name="sid"></param>
        /// <param name="dbConfigLookup"></param>
        /// <param name="oldDbConfigLookup"></param>
        /// <param name="oldDbConfig"></param>
        /// <returns></returns>
        private PrintHistoryDbConfig GetDbConfig(int sid, Dictionary<int, List<PrintHistoryDbConfig>> dbConfigLookup,
            Dictionary<int, List<PrintHistoryDbConfig>> oldDbConfigLookup, out PrintHistoryDbConfig oldDbConfig)
        {
            // 如果不使用mysql，或者京东平台，直接返回null
            if (_isUseMysql == false || _isJd)
            {
                oldDbConfig = null;
                return oldDbConfig;
            }

            // 新配置库(MySql)
            List<PrintHistoryDbConfig> allDbConfig;
            if (dbConfigLookup.TryGetValue(sid, out allDbConfig))
            {
                var newDbConfig = allDbConfig.FirstOrDefault(x => !x.IsDisabled && x.DbNameConfig.IsNew);
                oldDbConfig = allDbConfig.FirstOrDefault(x => !x.IsDisabled && !x.DbNameConfig.IsNew) ?? newDbConfig;
                var config = oldDbConfig;
                if (newDbConfig != null || oldDbConfig != null)
                {
                    Log.Debug(() => $"新库配置：{newDbConfig?.ToJson()}");
                    Log.Debug(() => $"旧库配置：{config?.ToJson()}");
                    return newDbConfig ?? oldDbConfig;
                }
            }

            // 旧配置库(SQLServer)
            List<PrintHistoryDbConfig> oldDbConfigList;
            if (oldDbConfigLookup.TryGetValue(sid, out oldDbConfigList))
            {
                var newDbConfig = oldDbConfigList.FirstOrDefault(x => !x.IsDisabled && x.DbNameConfig.IsNew);
                oldDbConfig = oldDbConfigList.FirstOrDefault(x => !x.IsDisabled && !x.DbNameConfig.IsNew);
                if (newDbConfig != null || oldDbConfig != null)
                {
                    var dbConfig = newDbConfig ?? oldDbConfig;
                    _dbConfigRepo.CreateDbConfig(sid, dbConfig.DbNameConfigId, newDbNameId: dbConfig.NewDbNameConfigId); // 插入到新库
                    return dbConfig;
                }
            }
            else
            {
                // 创建配置
                var defaultDbId = _dbConfigRepo.GetHashDefaultDbId(sid);
                var dbConfig = _dbConfigRepo.CreateDbConfig(sid, defaultDbId);
                oldDbConfig = null;
                Log.Debug(() => "新旧库都没有配置，创建新库配置：" + dbConfig.ToJson());
                return dbConfig;
            }

            oldDbConfig = null;
            return oldDbConfig;
        }

        /// <summary>
        /// 设置打印历史
        /// </summary>
        /// <param name="db"></param>
        /// <param name="printHistories"></param>
        /// <param name="oldDbConfig"></param>
        private void SetPrintHistory(IDbConnection db, List<PrintHistory> printHistories, PrintHistoryDbConfig oldDbConfig = null)
        {
            //新打印的数据SendContent是有值的
            var relationCodes = printHistories
                .Where(f => string.IsNullOrWhiteSpace(f.PrinterJsonData) &&
                            string.IsNullOrWhiteSpace(f.SendContent) == false).Select(f => f.SendContent)
                .ToList();
            //旧数据迁移到新的PrintHistory库后，SendConent 和 PrinterJsonData 都会清空
            var oldPrintHistoryId = printHistories
                .Where(f => string.IsNullOrWhiteSpace(f.PrinterJsonData) &&
                            string.IsNullOrWhiteSpace(f.SendContent)).Select(f => f.ID).ToList();
            var printDataList = GetPrintHistoryDataList(db, relationCodes, oldPrintHistoryId, oldDbConfig);
            //设置打印历史部分字段
            SetData(printHistories, printDataList);
        }
        /// <summary>
        /// 设置打印历史部分字段
        /// </summary>
        /// <param name="printHistoryList"></param>
        /// <param name="printDataList"></param>
        private void SetData(List<PrintHistory> printHistoryList, List<PrintHistoryData> printDataList)
        {
            if (printDataList.Any())
            {
                var printDataDict = printDataList.ToLookup(f => f.RelationCode, f => f)
                    .ToDictionary(f => f.Key, f => f.OrderBy(p => p.Id).FirstOrDefault());
                foreach (var item in printHistoryList)
                {
                    PrintHistoryData printData;
                    if (string.IsNullOrWhiteSpace(item.SendContent))
                        printDataDict.TryGetValue(item.ID.ToString(), out printData);
                    else
                        printDataDict.TryGetValue(item.SendContent, out printData);

                    if (printData != null)
                    {
                        item.PrintHistoryData = printData;
                        item.PrinterJsonData = printData.PrintDataJson ?? "";
                        item.SendContent = printData.SendContent ?? "";
                        item.SendContentExt = printData.SendContent ?? "";
                    }
                }
            }
        }

        /// <summary>
        /// 获取重打时要用的打印数据
        /// 分单系统 PrintHistory 数据单独存在一个库，涉及新库、旧库和备库逻辑
        /// </summary>
        /// <param name="db"></param>
        /// <param name="relationCodes">新数据的关联Code</param>
        /// <param name="printHistoryIds">旧数据根据打印记录id去查询PrintHistory取出PrintDataJson</param>
        /// <param name="oldDbConfig"></param>
        /// <returns></returns>
        private List<PrintHistoryData> GetPrintHistoryDataList(IDbConnection db, List<string> relationCodes, List<long> printHistoryIds, PrintHistoryDbConfig oldDbConfig = null)
        {
            var datas = new List<PrintHistoryData>();

            if (!(relationCodes?.Any() ?? false) && !(printHistoryIds?.Any() ?? false))
                return datas;

            // 查询新库数据
            if (relationCodes?.Any() ?? false)
            {
                datas.AddRange(QueryPrintData(db, relationCodes));

                // 查询旧库数据
                var notFoundCodes = relationCodes.Where(rc => datas.All(d => d.RelationCode != rc)).ToList();
                Log.Debug(() => $"未找到新库中的打印数据，关联码：{notFoundCodes.ToJson()}, DbConfig:{db.ConnectionString}, OldDbConfig:{oldDbConfig?.ConnectionString}");
                var isUseJdMysqlDb = _isJd && _isAllUseJdMysqlDb == false;
                if (notFoundCodes.Any() && isUseJdMysqlDb || (oldDbConfig != null && db.ConnectionString != oldDbConfig.ConnectionString))
                {
                    // 获取实际的mysql连接字符串
                    var dbStr = isUseJdMysqlDb ? GetJdMysqlDbStr() : oldDbConfig.ConnectionString;
                    using (var oldDb = DbUtility.GetMySqlConnection(dbStr))
                    {
                        var oldData = QueryPrintData(oldDb, notFoundCodes);
                        datas.AddRange(oldData);
                        Log.Debug(() => $"从旧库查询到的打印数据：{oldData.Count}条，关联码：{notFoundCodes.ToJson()}, IsUseJdMysqlDb:{isUseJdMysqlDb}, OldDbConfig:{oldDb.ConnectionString}");
                    }

                    // 查询备份库数据
                    notFoundCodes = relationCodes.Where(rc => datas.All(d => d.RelationCode != rc)).ToList();
                }
                if (!notFoundCodes.Any()) return datas;

                // 查询备库数据
                Log.Debug(() => $"未找到新旧库中的打印数据，关联码：{notFoundCodes.ToJson()}");
                var backupDb = GetBackupDbConnection();
                if (backupDb == null) return datas;

                var backupDatas = QueryPrintData(backupDb, notFoundCodes);
                if (!backupDatas.Any()) return datas;

                // 尝试插入到新库
                datas.AddRange(backupDatas);
                InsertPrintDataToNewDb(db, backupDatas);
            }
            return datas;
        }

        /// <summary>
        /// 查询打印数据
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="codes">关联码</param>
        /// <returns>打印数据列表</returns>
        private List<PrintHistoryData> QueryPrintData(IDbConnection connection, List<string> codes)
        {
            if (!(codes?.Any() ?? false)) return new List<PrintHistoryData>();
            const string sql = "SELECT * FROM P_PrintHistoryDataNew WHERE RelationCode IN @codes";
            // 测试使用，无误后可删除
            Log.Debug(() => connection.ConnectionString);
            return connection.Query<PrintHistoryData>(sql, new { codes }).ToList();
        }

        /// <summary>
        /// 插入打印数据到新库
        /// </summary>
        /// <param name="db"></param>
        /// <param name="datas"></param>
        private void InsertPrintDataToNewDb(IDbConnection db, List<PrintHistoryData> datas)
        {
            foreach (var data in datas)
            {
                try
                {
                    InsertPrintHistory(db, data);
                }
                catch (Exception e)
                {
                    Log.WriteError($"插入打印数据到新库失败：{e.Message}");
                }
            }
        }

        /// <summary>
        /// 获取备库连接
        /// </summary>
        /// <returns></returns>
        private IDbConnection GetBackupDbConnection()
        {
            var key = SystemSettingKeys.PrintHistoryDataBackUpKey.Replace("{CloudPlatform}", CustomerConfig.CloudPlatformType);
            var defaultDb = new CommonSettingRepository().Get(key, 0);
            if (string.IsNullOrEmpty(defaultDb?.Value))
            {
                Log.WriteError($"获取打印数据备库的连接串信息失败：{SystemSettingKeys.PrintHistoryDataBackUpKey}");
                return null;
            }

            try
            {
                var db = new MySqlConnection(defaultDb.Value);
                return db;
            }
            catch (Exception e)
            {
                Log.WriteError($"获取打印数据备库的默认连接串列表失败：{SystemSettingKeys.PrintHistoryDataBackUpKey}，{e.Message}");
                throw;
            }
        }
        
        
        /// <summary>
        /// 默认1，0关闭，2代表全部发版上线
        /// </summary>
        /// <returns></returns>
        private static string GetTempUseJdMysqlValue()
        {
            var jdMysqlDbKey = $"/System/Fendan/PrintHistoryData/IsTempUseJdMysqlDb";
            var isTempUseJdMysqlDb = new CommonSettingRepository().Get(jdMysqlDbKey, 0);
            const string defaultValue = "1";
            return isTempUseJdMysqlDb?.Value ?? defaultValue;
        }

        /// <summary>
        /// 是否全部使用京东的Mysql打印数据存储库
        /// </summary>
        /// <returns></returns>
        private static bool IsAllUseJdMysqlDb()
        {
            // 非京东平台，不使用京东的Mysql重打数据库
            if (CustomerConfig.CloudPlatformType != nameof(CloudPlatformType.Jingdong)) return false;

            // 默认开，等待45天后的数据自然清理，清空老库PrintHistoryDataNew，兼容开关关闭，全部走新库
            return GetTempUseJdMysqlValue() == "0";
        }

        /// <summary>
        /// 获取京东打印数据存储的Mysql数据库连接字符串。
        /// 如果未正确配置相关的Mysql数据库连接信息，则会抛出异常。
        /// </summary>
        /// <returns>。</returns>
        private static string GetJdMysqlDbStr()
        {
            var jdMysqlDbKey = $"/System/Fendan/PrintHistoryData/JingDongMysqlDb";
            var jdMysqlDb = new CommonSettingRepository().Get(jdMysqlDbKey, 0);
            if (jdMysqlDb == null || string.IsNullOrWhiteSpace(jdMysqlDb.Value))
                throw new LogicException("京东打印数据存储Mysql数据库连接未配置");
            return jdMysqlDb.Value;
        }
    }
}
