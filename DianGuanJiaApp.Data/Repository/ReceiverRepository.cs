using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using Z.Dapper.Plus;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility;
using System.Data;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using MySql.Data.MySqlClient;

namespace DianGuanJiaApp.Data.Repository
{
    /// <summary>
    /// 收件人信息 相关数据层操作
    /// </summary>
    public partial class ReceiverRepository : BaseRepository<Receiver>
    {
        private string _connectionString = string.Empty;
        private bool _isUseMySQL = false;
        private readonly CommonSettingRepository _commonSettingRepository = new CommonSettingRepository();
        private readonly ReceDbConfigRepository _receDbConfigRepository = new ReceDbConfigRepository();
        private List<ReceDbConfigModel> _oldDbConfigModel;
        private readonly bool _isNewDb;
        private readonly int _fxUserId;
        private readonly List<int> _oldDbNameIds;

        /// <summary>
        /// 通过BaseSiteContext.Current.CurrentFxUserId获取使用的数据库
        /// </summary>
        public ReceiverRepository()
        {
            _fxUserId = BaseSiteContext.Current.CurrentFxUserId;
            var dbConfigModel = _receDbConfigRepository.GetNewDbConfigModel(_fxUserId);
            _connectionString = dbConfigModel?.ConnectionString;
            _isUseMySQL = dbConfigModel?.IsMySQL ?? false;
            _isNewDb = dbConfigModel?.IsNewDb == true;
            // TODO 临时增加日志，待删除
            Log.Debug(() => $"ReceiverRepository fxUserId:{_fxUserId}, IsUseMySQL:{_isUseMySQL}, IsNewDb:{_isNewDb}, RepoConnectionString:{_receDbConfigRepository?.DbConnection?.ConnectionString}");
        }

        /// <summary>
        /// 指定fxUserId或dbNameId（dbNameId大于0时用dbNameId，小于0用fxUserId）
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="dbNameId"></param>
        /// <param name="oldDbNameIds"></param>
        public ReceiverRepository(int fxUserId, int dbNameId = 0, List<int> oldDbNameIds = null)
        {
            _fxUserId = fxUserId;
            _oldDbNameIds = oldDbNameIds;
            if (dbNameId > 0)
            {
                var dbConfigModel = _receDbConfigRepository.GetDbConfigModelByDbNameId(dbNameId);
                _connectionString = dbConfigModel?.ConnectionString;
                _isUseMySQL = dbConfigModel?.IsMySQL ?? false;
                _isNewDb = dbConfigModel?.IsNewDb == true;
            }
            else
            {
                var dbConfigModel = _receDbConfigRepository.GetNewDbConfigModel(fxUserId);
                _connectionString = dbConfigModel?.ConnectionString;
                _isUseMySQL = dbConfigModel?.IsMySQL ?? false;
                _isNewDb = dbConfigModel?.IsNewDb == true;
            }
            // TODO 临时增加日志，待删除
            Log.Debug(() => $"ReceiverRepository fxUserId:{_fxUserId}, IsUseMySQL:{_isUseMySQL}, IsNewDb:{_isNewDb}, RepoConnectionString:{_receDbConfigRepository?.DbConnection?.ConnectionString}");
        }
        
        /// <summary>
        /// 指定连接字符串
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="isMySQL"></param>
        public ReceiverRepository(string connectionString, bool isMySQL = false)
        {
            _connectionString = connectionString;
            _isUseMySQL = isMySQL;
            _isNewDb = true;
            _fxUserId = BaseSiteContext.Current.CurrentFxUserId;
        }

        /// <summary>
        /// 是否使用MySQL
        /// </summary>
        public bool IsUseMySQL => _isUseMySQL;

        private IDbConnection _db()
        {
            if (string.IsNullOrEmpty(_connectionString))
            {
                _connectionString = CustomerConfig.ReceiverDbConnectionString;
                _isUseMySQL = _connectionString.IndexOf("port=3306") > 0;
            }
            IDbConnection db = null;

            if (!string.IsNullOrEmpty(_connectionString))
            {
                if (_isUseMySQL)
                    db = Dapper.DbUtility.GetMySqlConnection(_connectionString);
                else
                    db = Dapper.DbUtility.GetConnection(_connectionString);
            }
            else
            {
                throw new Exception("Receiver数据连接为空");
            }
            return db;
        }
        /// <summary>
        /// Config配置的是否MySQL
        /// </summary>
        public bool OldDbIsMySql
        {
            get
            {
                var connectionString = CustomerConfig.ReceiverDbConnectionString;
                return connectionString.IndexOf("port=3306", StringComparison.OrdinalIgnoreCase) > 0;
            }
        }

        /// <summary>
        /// 兼容Config配置的数据库
        /// </summary>
        public IDbConnection OldDbConnection
        {
            get
            {
                var connectionString = CustomerConfig.ReceiverDbConnectionString;
                return OldDbIsMySql
                    ? Dapper.DbUtility.GetMySqlConnection(connectionString)
                    : Dapper.DbUtility.GetConnection(connectionString);
            }
        }

        /// <summary>
        /// 默认获取新库连接
        /// </summary>
        public new IDbConnection DbConnection => _db();

        /// <summary>
        /// 获取旧库连接
        /// </summary>
        public List<IDbConnection> DbConnectionByOldDb
        {
            get
            {
                var dbList = new List<IDbConnection>();
                if (_oldDbConfigModel == null && _oldDbNameIds != null && _oldDbNameIds.Any())
                {
                    _oldDbConfigModel = _receDbConfigRepository.GetDbConfigModelByDbNameId(_oldDbNameIds);
                    dbList.AddRange(_oldDbConfigModel.Select(model => model.IsMySQL
                        ? Dapper.DbUtility.GetMySqlConnection(model.ConnectionString)
                        : Dapper.DbUtility.GetConnection(model.ConnectionString)));
                }
                else if (_oldDbConfigModel == null && _fxUserId > 0)
                {
                    _oldDbConfigModel = new List<ReceDbConfigModel>
                        { _receDbConfigRepository.GetDbConfigModel(_fxUserId, isNew: false) };
                }

                return dbList;
            }
        }
        
        /// <summary>
        /// 获取备库连接
        /// </summary>
        /// <returns></returns>
        private IDbConnection BackupDbConnection
        {
            get
            {
                var key = SystemSettingKeys.ReceiverBackUpKey.Replace("{CloudPlatform}", CustomerConfig.CloudPlatformType);
                var defaultDb = new CommonSettingRepository().Get(key, 0); 
                if (string.IsNullOrEmpty(defaultDb?.Value))
                {
                    // 京东无备库相关逻辑
                    if (CustomerConfig.CloudPlatformType != CloudPlatformType.Jingdong.ToString2())
                        Log.WriteError($"获取收件人备库的连接串信息失败：{key}");
                    return null;
                }

                try
                {
                    Log.Debug(() => "获取收件人备库的默认连接串列表：" + defaultDb.Value);
                    var db = Dapper.DbUtility.GetMySqlConnection(defaultDb.Value);
                    return db;
                }
                catch (Exception e)
                {
                    Log.WriteError($"获取收件人库的默认连接串列表失败：{SystemSettingKeys.ReceiverBackUpKey}，{e.Message}");
                    throw;
                } 
            }
        }


        /// <summary>
        /// 云平台使用旧数据库开关
        /// </summary>
        public bool UseOldDbByCloudPlatformType
        {
            get
            {
                ////本地测试使用Alibaba
                //return CustomerConfig.CloudPlatformType == "Alibaba";
                return CustomerConfig.CloudPlatformType == "Pinduoduo";
            }
        }
        /// <summary>
        /// 是否开启使用新收件人数据库
        /// </summary>
        public bool IsEnabledUseNewReceiverDb
        {
            get
            {
                var key = SystemSettingKeys.IsEnabledUseNewReceiverDb.Replace("{CloudPlatformType}",
                    CustomerConfig.CloudPlatformType);
                var value = _commonSettingRepository.GetValue(key, 0);
                return !string.IsNullOrWhiteSpace(value) && value == "1";
            }
        }

        /// <summary>
        /// 是否开启使用旧的收件人DB
        /// </summary>
        public bool IsEnabledUseOldReceiverDb
        {
            get
            {
                return UseOldDbByCloudPlatformType && IsEnabledUseNewReceiverDb == false;
            }
        }

        /// <summary>
        /// 批量添加收件人信息
        /// </summary>
        /// <param name="models"></param>
        public void BatchAdd(List<Receiver> models)
        {
            if (models == null || models.Any() == false)
                return;

            try
            {
                if (IsEnabledUseOldReceiverDb)
                {
                    OldDbConnection.BulkInsert(models);
                }
                else
                {
                    _db().BulkInsert(models);
                }
            }
            catch (Exception e)
            {
                // 判断是否为唯一键冲突异常
                if (IsUniqueKeyConflictException(e))
                {
                    BatchAddOneByOne(models);
                }
                else
                {
                    Log.WriteError($"批量添加收件人信息失败：{e.Message}，尝试使用备库插入");
                    var backDb = BackupDbConnection;
                    if (backDb != null)
                        using (backDb)
                        {
                            backDb.BulkInsert(models);
                        }
                }
            }
        }

        /// <summary>
        /// 判断异常是否为唯一键冲突
        /// </summary>
        /// <param name="exception"></param>
        /// <returns></returns>
        private bool IsUniqueKeyConflictException(Exception exception)
        {
            var message = exception.Message?.ToLower() ?? "";

            // MySQL唯一键冲突异常
            if (message.Contains("duplicate entry") && message.Contains("for key"))
                return true;

            // SQL Server唯一键冲突异常
            if (message.Contains("violation of unique key constraint") ||
                message.Contains("cannot insert duplicate key"))
                return true;

            return false;
        }

        /// <summary>
        /// 逐个添加收件人信息
        /// </summary>
        /// <param name="models"></param>
        private void BatchAddOneByOne(List<Receiver> models)
        {
            foreach (var model in models)
            {
                try
                {
                    var connection = IsEnabledUseOldReceiverDb ? OldDbConnection : _db();
                    if (connection.IsMySqlDb()) connection.InsertMysql(model);
                    else connection.Insert(model);
                }
                catch (Exception e)
                {
                    // 非唯一键冲突，尝试使用备库插入
                    if (IsUniqueKeyConflictException(e) == false)
                    {
                        var backDb = BackupDbConnection;
                        if (backDb != null)
                            using (backDb)
                            {
                                backDb.InsertMysql(model);
                            }
                        Log.WriteError($"单个添加收件人信息失败，OrderCode: {model.OrderCode}，错误: {e.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 是否存在收件人信息（引用点没有使用，也进行兼容性考虑）
        /// </summary>
        /// <param name="orderCode"></param>
        /// <returns></returns>
        public bool ExistReceiver(string orderCode)
        {
            string sql;
            var sqlByMsSql = string.Empty;
            var sqlByMySql = string.Empty;
            if (IsUseMySQL)
            {
                sql = "SELECT Id FROM Receiver WHERE OrderCode=@orderCode LIMIT 1";
                sqlByMsSql = sql;
            }
            else
            {
                sql = "SELECT TOP 1 Id FROM Receiver WITH(NOLOCK) WHERE OrderCode=@orderCode";
                sqlByMySql = sql;
            }

            var isExist = DbConnection.ExecuteScalar(sql, new { orderCode }).ToInt() > 0;

            if (isExist == false && _isNewDb)
            {
                // 尝试使用旧库
                foreach (var dbConnection in DbConnectionByOldDb)
                {
                    using (dbConnection)
                    {
                        isExist = dbConnection.ExecuteScalar(sql, new { orderCode }).ToInt() > 0;
                        if (isExist) break;
                    }
                }
                
                // 还是为空，尝试使用备库
                if (isExist == false)
                {
                    var backDb = BackupDbConnection;
                    if (backDb != null)
                        using (backDb)
                        {
                            isExist = backDb.ExecuteScalar(sql, new { orderCode }).ToInt() > 0;
                        }
                }
            }
            
            // 存在或非拼多多直接返回
            if (isExist || UseOldDbByCloudPlatformType == false)
            {
                return isExist;
            }

            return OldDbExistReceiver(sqlByMsSql, sqlByMySql, orderCode);
        }
        
        /// <summary>
        /// 兼容旧版是否存在收件人
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="sqlByMySql"></param>
        /// <param name="orderCode"></param>
        /// <returns></returns>
        private bool OldDbExistReceiver(string sql, string sqlByMySql, string orderCode)
        {
            if (OldDbIsMySql)
            {
                sql = sqlByMySql;
            }

            return OldDbConnection.ExecuteScalar(sql, new { orderCode }).ToInt() > 0;
        }

        /// <summary>
        /// 批量插入，存在则更新（主键和唯一键）未使用
        /// </summary>
        /// <param name="models"></param>
        /// <param name="chunkExceptionCallBack"></param>
        /// <param name="chunkSize"></param>
        /// <param name="isIgnoreUpdate"></param>
        /// <returns></returns>
        public bool InsertDuplicateUpdate(List<Receiver> models, Action<List<Receiver>, string, Exception> chunkExceptionCallBack, int chunkSize = 100, bool isIgnoreUpdate = false)
        {
            //自增与唯一键字段
            const string declaringTypeName = "Receiver";
            const string autoIncrementField = "Id";
            var unionIndexKeys = new[] { "OrderCode" };
            //表字段
            var propertyNames = new DefaultTypeMap(typeof(Receiver)).Properties
                .Where(m => m.DeclaringType != null && m.DeclaringType.Name == declaringTypeName)
                .Select(m => m.Name).ToList();
            //脚本语法
            var tableName = "Receiver";
            var insertIntoTemplate = $"INSERT INTO {tableName}({{0}}) VALUES{{1}}";
            var onDuplicateKeyUpdateTemplate = " ON DUPLICATE KEY UPDATE {0};";
            var onDuplicateUpdateFieldTemplate = "`{0}`=VALUES(`{0}`)";
            //是否忽略更新
            if (isIgnoreUpdate)
            {
                onDuplicateUpdateFieldTemplate = "`{0}`=`{0}`";
            }
            //处理更新字段
            var fields = propertyNames.Where(m => m != autoIncrementField).Select(m => $"`{m}`").ToList();
            var fieldsSql = string.Join(",", fields);
            var updateFields = propertyNames.Where(m => m != autoIncrementField && !unionIndexKeys.Contains(m))
                .ToList();
            var onDuplicateUpdateFieldSql = string.Join(",",
                updateFields.Select(m => string.Format(onDuplicateUpdateFieldTemplate, m)).ToList());
            var onDuplicateKeyUpdateSql = string.Format(onDuplicateKeyUpdateTemplate, onDuplicateUpdateFieldSql);
            //分块
            var chunkList = models.ChunkList(chunkSize);
            if (!chunkList.Any())
            {
                return true;
            }
            chunkList.ForEach(list =>
            {
                string executeSql = string.Empty;
                try
                {
                    //构建值
                    var valuesList = list.Select(x =>
                    {
                        var map = new DefaultTypeMap(x.GetType());
                        var nameValues = map.Properties
                            .Where(m => m.DeclaringType != null && m.DeclaringType.Name == declaringTypeName)
                            .Select(m => new { m.Name, TypeName = m.PropertyType.Name, Value = m.GetValue(x) }).ToList();
                        var values = nameValues.Where(m => m.Name != autoIncrementField).Select(m =>
                        {
                            switch (m.TypeName)
                            {
                                case "Int32":
                                    return m.Value;
                                case "DateTime":
                                    return $"'{Convert.ToDateTime(m.Value):yyyy-MM-dd HH:mm:ss,fff}'";
                                default:
                                    return $"'{m.Value}'";

                            }
                        }).ToList();
                        return $"({string.Join(",", values)})";
                    }).ToList();
                    var valuesSql = string.Join(",", valuesList);
                    executeSql = string.Format(insertIntoTemplate, fieldsSql, valuesSql) + onDuplicateKeyUpdateSql;
                    //执行SQL
                    DbConnection.ExecuteScalar(executeSql);
                }
                catch (Exception)
                {
                    // 备库处理
                    BackupHandle(list,executeSql, chunkExceptionCallBack);
                }
            });
            return true;
        }

        /// <summary>
        /// 备库处理
        /// </summary>
        /// <param name="list"></param>
        /// <param name="executeSql"></param>
        /// <param name="chunkExceptionCallBack"></param>
        private void BackupHandle(List<Receiver> list, string executeSql, Action<List<Receiver>, string, Exception> chunkExceptionCallBack)
        {
            try
            {
                var backDb = BackupDbConnection;
                if (backDb == null)
                {
                    Log.WriteError("收件人信息备库连接为空");
                    return;
                }
                using (backDb)
                {
                    backDb.ExecuteScalar(executeSql);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"收件人信息备库处理失败：{ex.Message}");
                chunkExceptionCallBack(list, executeSql, ex);
            }
        }

        /// <summary>
        /// 批量更新
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public void BatchUpdate(List<Receiver> models)
        {
            if (IsEnabledUseOldReceiverDb)
            {
                OldDbConnection.BulkUpdate(models);
            }
            else
            {
                DbConnection.BulkUpdate(models);
            }
        }

        /// <summary>
        /// 获取指定 OrderCode 的收件人信息列表
        /// </summary>
        /// <param name="orderCodes"></param>
        /// <param name="fields"></param>
        /// <param name="isQueryOldDb"></param>
        /// <returns></returns>
        public List<Receiver> GetList(List<string> orderCodes, List<string> fields = null, bool isQueryOldDb = true)
        {
            if (orderCodes == null || !orderCodes.Any())
                return new List<Receiver>();

            var strField = fields != null && fields.Any() ? string.Join(",", fields) : "*";
            if (strField.ToLower().Contains("id") == false && strField.Contains("*") == false) strField = "Id," + strField;
            var sqlTemplate = $"SELECT {strField} FROM Receiver WITH(NOLOCK) WHERE OrderCode IN @orderCodes";
            var sql = TranSql(sqlTemplate);

            var receivers = new Dictionary<string,Receiver>();
            
            var index = 0;
            var db = DbConnection;
            using (db)
            {
                if (db.State == ConnectionState.Closed)
                    db.Open();
                while (true)
                {
                    var curCodes = orderCodes.Skip(index * 500).Take(500).ToList();

                    index++;

                    if (!curCodes.Any())
                        break;

                    var parameters = new DynamicParameters();
                    parameters.Add("orderCodes", curCodes);
                    var models = db.Query<Receiver>(sql, parameters)?.ToList();

                    if (models != null && models.Any())
                    {
                        models.ForEach(r =>
                        {
                            if (!receivers.ContainsKey(r.OrderCode))
                            {
                                receivers.Add(r.OrderCode, r);
                            }
                        });
                    }
                    //兼容旧库
                    var orderOrdersByOld = models == null
                        ? curCodes
                        : curCodes.Where(o => !models.Select(m => m.OrderCode).Contains(o)).ToList();
                    if (!isQueryOldDb || !orderOrdersByOld.Any() || UseOldDbByCloudPlatformType == false)
                    {
                        continue;
                    }

                    models = GetListByOldDb(orderOrdersByOld, sqlTemplate);
                    if (models != null && models.Any())
                    {
                        models.ForEach(r =>
                        {
                            if (!receivers.ContainsKey(r.OrderCode))
                            {
                                receivers.Add(r.OrderCode, r);
                            }
                        });
                    }
                }
            }
            // 查询未找到的 OrderCode
            QueryMissingOrderCodes(orderCodes, receivers, sql);

            return receivers.Values.ToList();
        }
        /// <summary>
        /// 获取指定OrderCode的收件人信息列表
        /// </summary>
        /// <param name="orderCodes"></param>
        /// <returns></returns>
        public List<Receiver> GetListFromReceiverOld(List<string> orderCodes)
        {
            var strField = "Id,ShopId,PlatformType,PlatformOrderId,OrderCode,ToName,ToPhone,ToMobile,ToProvince,ToCity,ToCounty,ToStreet,ToAddress,ToNameMask,ToPhoneMask,ToAddressMask,ToPhoneIndex,DecryptField,ExtField1,ExtField2,CreateTime";
            var sqlTemplate = $"SELECT {strField} FROM Receiver_old WITH(NOLOCK) WHERE OrderCode IN @orderCodes";
            var sql = TranSql(sqlTemplate);

            var receivers = new List<Receiver>();
            var index = 0;

            var db = DbConnection;
            using (db)
            {
                if (db.State == ConnectionState.Closed)
                    db.Open();
                while (true)
                {
                    var curCodes = orderCodes.Skip(index * 500).Take(500).ToList();

                    index++;

                    if (!curCodes.Any())
                        break;

                    var parameters = new DynamicParameters();
                    parameters.Add("orderCodes", curCodes);
                    var models = db.Query<Receiver>(sql, parameters)?.ToList();

                    if (models != null && models.Any())
                    {
                        receivers.AddRange(models);
                    }
                }
            }
            
            // 查询未找到的 OrderCode
            var receiversDic = receivers.ToDictionary(r => r.OrderCode);
            QueryMissingOrderCodes(orderCodes, receiversDic, sql);
            
            return receiversDic.Values.ToList();
        }
        
        private List<Receiver> GetListByOldDb(List<string> orderCodes, string sql)
        {
            //处理SQL
            sql = OldDbTranSql(sql);
            //连接是否开启
            var db = OldDbConnection;
            if (db.State == ConnectionState.Closed)
                db.Open();

            var receivers = new List<Receiver>();
            var index = 0;
            using (db)
            {
                while (true)
                {
                    var curCodes = orderCodes.Skip(index * 500).Take(500).ToList();

                    index++;

                    if (!curCodes.Any())
                        break;

                    var parameters = new DynamicParameters();
                    parameters.Add("orderCodes", curCodes);
                    var models = db.Query<Receiver>(sql, parameters)?.ToList();
                    if (models != null && models.Any())
                    {
                        //标识是否旧版DB
                        models.ForEach(m =>
                        {
                            m.IsOldDb = true;
                        });
                        receivers.AddRange(models);
                    }
                }
            }
            return receivers;
        }

        /// <summary>
        /// 根据OrderCode获取收件人信息
        /// </summary>
        /// <param name="orderCode"></param>
        /// <returns></returns>
        public Receiver GetByOrderCode(string orderCode)
        {
            var sqlTemplate = "SELECT * FROM Receiver WITH(NOLOCK) WHERE OrderCode=@orderCode";
            var sql = TranSql(sqlTemplate);
            var model = DbConnection.QueryFirstOrDefault<Receiver>(sql, new { orderCode });
            
            // 查询旧库或备库
            if (model == null && _isNewDb)
            {
                // 查询旧库
                foreach (var dbConnection in DbConnectionByOldDb)
                {
                    using (dbConnection)
                    {
                        model = dbConnection.QueryFirstOrDefault<Receiver>(sql, new { orderCode });
                        if (model != null)
                        {
                            break;
                        }
                    }
                }
                if (model == null)
                {
                    var backDb = BackupDbConnection;
                    // 查询备库
                    if (backDb != null)
                        using (backDb)
                        {
                            model = backDb.QueryFirstOrDefault<Receiver>(sql, new { orderCode });
                        }
                    // 插入新库
                    if (model != null) BatchAdd(new List<Receiver> { model });
                }
            }
            
            if (model != null || UseOldDbByCloudPlatformType == false)
            {
                return model;
            }

            //兼容旧版
            return GetByOrderCodeWithOldDb(sqlTemplate, orderCode);
        }

        /// <summary>
        /// 兼容旧版Config配置数据库
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="orderCode"></param>
        /// <returns></returns>
        private Receiver GetByOrderCodeWithOldDb(string sql, string orderCode)
        {
            sql = OldDbTranSql(sql);
            var model = OldDbConnection.QueryFirstOrDefault<Receiver>(sql, new { orderCode });
            //加旧库标识
            if (model != null)
            {
                model.IsOldDb = true;
            }
            return model;
        }

        /// <summary>
        /// 添加收件人信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public bool AddReceiver(Receiver model)
        {
            BatchAdd(new List<Receiver> { model });
            return true;
        }

        /// <summary>
        /// 更新收件人信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public bool UpdateReceiver(Receiver model)
        {
            BatchUpdate(new List<Receiver> { model });
            return true;
        }

        /// <summary>
        /// 转换SQL，若是MySQL去除部分不支持的大写关键字
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="isTrans"></param>
        /// <returns></returns>
        private string TranSql(string sql, bool isTrans = false)
        {
            if (!IsUseMySQL && isTrans == false) 
                return sql;
            sql = sql.Replace("WITH(NOLOCK)", string.Empty);
            sql = sql.Replace("with(nolock)", string.Empty);
            return sql;
        }
        /// <summary>
        /// 兼容旧版转换SQL，若是MySQL去除部分不支持的大写关键字
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        private string OldDbTranSql(string sql)
        {
            if (!OldDbIsMySql) 
                return sql;
            sql = sql.Replace("WITH(NOLOCK)", string.Empty);
            sql = sql.Replace("with(nolock)", string.Empty);
            return sql;
        }

        /// <summary>
        /// 更新打码数据（兼容旧版）
        /// </summary>
        /// <param name="models"></param>
        public void UpdateMaskReceiver(List<Receiver> models)
        {
            if (models == null || !models.Any())
            {
                return;
            }

            var groupModels = models.GroupBy(m => m.IsOldDb).ToList();
            groupModels.ForEach(group =>
            {
                //兼容旧版
                UpdateMaskReceiver(group.Key ? OldDbConnection : DbConnection, group.ToList());
            });
        }

        /// <summary>
        /// 更新打码数据
        /// </summary>
        /// <param name="dbConnection"></param>
        /// <param name="list"></param>
        public void UpdateMaskReceiver(IDbConnection dbConnection, List<Receiver> list)
        {
            var i = 0;
            var batchSize = 100;
            var batchSql = string.Empty;
            var parameters = new DynamicParameters();

            list?.ForEach(w =>
            {
                i++;
                batchSql +=
                    $"UPDATE Receiver SET ToNameMask=@ToNameMask{i},ToPhoneMask=@ToPhoneMask{i},ToAddressMask=@ToAddressMask{i} WHERE Id=@Id{i} ;";

                parameters.Add($"ToNameMask{i}", w.ToNameMask);
                parameters.Add($"ToPhoneMask{i}", w.ToPhoneMask);
                parameters.Add($"ToAddressMask{i}", w.ToAddressMask);
                parameters.Add($"Id{i}", w.Id);

                if (i % batchSize == 0)
                {
                    dbConnection.Execute(batchSql, parameters);

                    batchSql = string.Empty;
                    parameters = new DynamicParameters();
                }
            });

            if (batchSql != string.Empty)
                dbConnection.Execute(batchSql, parameters);
        }

        /// <summary>
        /// 获取最小和最大Id（监控程序使用，一次性）
        /// </summary>
        /// <returns></returns>
        public RepairOrderQueryModel GetMinAndMaxId()
        {
            var sql = "SELECT MIN(Id) AS StartId,MAX(Id) AS EndId FROM Receiver WITH(NOLOCK)";
            sql = TranSql(sql);
            return this.DbConnection.QueryFirstOrDefault<RepairOrderQueryModel>(sql);
        }

        /// <summary>
        /// 根据Id区间查询（监控程序使用，一次性）
        /// </summary>
        /// <param name="query"></param>
        /// <returns>只返回Id,PlatformOrderId,ShopId</returns>
        public List<Receiver> GetReceivers(RepairOrderQueryModel query)
        {
            var sql = $@"SELECT Id,PlatformOrderId,ShopId FROM Receiver WITH(NOLOCK) WHERE Id BETWEEN @startId AND @endId";
            sql = TranSql(sql);
            var result = DbConnection.Query<Receiver>(sql, new { startId = query.StartId, endId = query.EndId }).ToList();

            return result;
        }

        /// <summary>
        /// 查询未找到的 OrderCode，从旧库和备库获取（支持分批处理）
        /// </summary>
        /// <param name="orderCodes"></param>
        /// <param name="receivers"></param>
        /// <param name="sql"></param>
        private void QueryMissingOrderCodes(List<string> orderCodes, Dictionary<string, Receiver> receivers, string sql)
        {
            // 获取未找到的 OrderCode
            var notExistOrderCodes = orderCodes.Where(o => !receivers.ContainsKey(o)).ToList();
            if (!notExistOrderCodes.Any()) return;
            var notExistDataIdByOld = new List<long>();
            var notExistDataIdByBackup = new List<long>();

            var list = notExistOrderCodes.ChunkList();
            
            // 查询旧库
            foreach (var dbConnection in DbConnectionByOldDb)
            {
                using (dbConnection)
                {
                    var isUseMysql = dbConnection.IsMySqlDb();

                    // 分批查询旧库
                    foreach (var batch in list)
                    {
                        var parameters = new DynamicParameters();
                        parameters.Add("orderCodes", batch);
                        List<Receiver> temp;
                        if (isUseMysql)
                        {
                            var sqlTemp = TranSql(sql, true);
                            temp = dbConnection.Query<Receiver>(sqlTemp, parameters)?.ToList();
                        }
                        else temp = dbConnection.Query<Receiver>(sql, parameters)?.ToList();
                        if (temp == null || !temp.Any()) continue;

                        AddToDictionary(temp, receivers);
                        notExistDataIdByOld.AddRange(temp.Select(m => m.Id));
                    }
                }
            }
            
            // 再次检查未找到的 OrderCode
            notExistOrderCodes = orderCodes.Where(o => !receivers.ContainsKey(o)).ToList();
            if (!notExistOrderCodes.Any()) return;
            list = notExistOrderCodes.ChunkList();

            // 分批查询备库
            var backDb = BackupDbConnection;
            if (backDb == null) return;
            using (backDb)
            {
                if (backDb.State == ConnectionState.Closed)
                    backDb.Open();
                var isUseMysql = backDb.IsMySqlDb();
                foreach (var batch in list)
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("orderCodes", batch);
                    var sqlTemp = isUseMysql ? TranSql(sql, true) : TranSql(sql);
                    var temp = backDb.Query<Receiver>(sqlTemp, parameters)?.ToList();
                    if (temp == null || !temp.Any()) continue;
                
                    AddToDictionary(temp, receivers);
                    notExistDataIdByBackup.AddRange(temp.Select(m => m.Id));
                }
            }
            
            if (!notExistDataIdByOld.Any() && !notExistDataIdByBackup.Any()) return;
            Task.Run(() =>
            {
                // 插入新库
                try
                {
                    // 旧库数据不插入，只插入备库的数据
                    if (notExistDataIdByBackup.Any())
                        GetAndInsertByIds(notExistDataIdByBackup, BackupDbConnection);
                }
                catch (Exception e)
                {
                    Log.WriteError($"尝试插入未找到的OrderCode失败：{e.Message}");
                }
            });
        }

        /// <summary>
        /// 添加收件人信息到字典
        /// </summary>
        /// <param name="models"></param>
        /// <param name="receivers"></param>
        private void AddToDictionary(IEnumerable<Receiver> models, Dictionary<string, Receiver> receivers)
        {
            foreach (var model in models)
                if (!receivers.ContainsKey(model.OrderCode))
                    receivers.Add(model.OrderCode, model);
        }
        
        /// <summary>
        /// 根据Id查询并插入新库
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="db"></param>
        public void GetAndInsertByIds(List<long> ids, IDbConnection db)
        {
            if (ids == null || !ids.Any() && db != null)
                return;

            var sql = "SELECT * FROM Receiver WITH(NOLOCK) WHERE Id IN @ids";
            if (db is MySqlConnection)
            {
                sql = "SELECT * FROM Receiver WHERE Id IN @ids";
            }
            var models = db.Query<Receiver>(sql, new { ids }).ToList();
            if (!models.Any())
                return;

            if (_isNewDb == false)
            {
                new ReceiverRepository().BatchAdd(models);
            }
            else
            {
                BatchAdd(models);
            }
        }
    }
}
