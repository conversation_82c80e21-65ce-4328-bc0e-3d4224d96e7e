using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Data.Repository.Settings
{
    public class DuplicationColdStorageSwitchRepository
    {
        private readonly CommonSettingRepository _commonSettingRepository;
        //静态构造
        static DuplicationColdStorageSwitchRepository()
        {
            _instance = new DuplicationColdStorageSwitchRepository();
        }
        //单例
        private static DuplicationColdStorageSwitchRepository _instance;
        public static DuplicationColdStorageSwitchRepository Instance
        {
            get { return _instance; }
        }
        //私有构造函数
        private DuplicationColdStorageSwitchRepository()
        {
            _commonSettingRepository = new CommonSettingRepository();
        }

        /// <summary>
        /// 是否开启复制副本（基于变更表类型）
        /// </summary>
        /// <returns></returns>
        public bool IsEnabledDuplication()
        {
            //京东暂时不支持分库
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Jingdong.ToString())
            {
                return false;
            }
            //配置
            var duplicationSwitchConfig = _commonSettingRepository.GetDuplicationSwitchConfigWithCache();
            //有配置，则取配置的。
            return duplicationSwitchConfig.GlobalDuplicationSwitch;
        }

        /// <summary>
        /// 是否开启冷数据存储
        /// </summary>
        /// <returns></returns>
        public bool IsEnabledColdStorage()
        {
            //京东暂时不支持冷热分离
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Jingdong.ToString())
            {
                return false;
            }
            //配置
            var duplicationSwitchConfig = _commonSettingRepository.GetDuplicationSwitchConfigWithCache();
            //有配置，则取配置的。
            return duplicationSwitchConfig.GlobalColdStorageSwitch;
        }
    }
}