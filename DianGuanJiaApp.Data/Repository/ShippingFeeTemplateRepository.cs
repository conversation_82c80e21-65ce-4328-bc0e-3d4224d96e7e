using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.FxModel;
using static Dapper.SqlMapper;

namespace DianGuanJiaApp.Data.Repository
{
    public class ShippingFeeTemplateRepository : BaseRepository<ShippingFeeTemplate>
    {
        public ShippingFeeTemplateRepository() : base(CustomerConfig.ConfigureDbConnectionString)
        {

        }

        //private IEnumerable<TReturn> Query<TReturn>(string sql, object param = null)
        //{
        //    if (CustomerConfig.CloudPlatformType != PlatformType.Alibaba.ToString())
        //    {
        //        var db = DbApiAccessUtility.GetConfigureDb();
        //        return db.Query<TReturn>(sql, param);
        //    }
        //    else
        //    {
        //        var db = this.DbConnection;
        //        return db.Query<TReturn>(sql, param);
        //    }
        //}

        //private int Execute(string sql)
        //{
        //    if (CustomerConfig.CloudPlatformType != PlatformType.Alibaba.ToString())
        //    {
        //        var db = DbApiAccessUtility.GetConfigureDb();
        //        return db.ExecuteNonQuery(sql).ToInt();
        //    }
        //    else
        //    {
        //        var db = this.DbConnection;
        //        return db.Execute(sql);
        //    }
        //}

        /// <summary>
        /// 获取1688第一个运费模板
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public ShippingFeeTemplate GetFirstBy1688(int fxUserId)
        {
            var db = this.DbConnection;
            var temp = db.Query<ShippingFeeTemplate>("SELECT TOP 1 * FROM dbo.ShippingFeeTemplate (NOLOCK) WHERE BusinessType = @businessType AND CreateBy = @fxUserId AND Status = 1", new { businessType = 0, fxUserId })?.FirstOrDefault();
            if (temp != null)
            {
                if (temp.IsFree || temp.IsRemote)
                {
                    var tempOtherRuleSql = "SELECT * FROM dbo.ShippingFeeTemplateOtherRule (NOLOCK) WHERE TemplateId = @tempId";
                    var otherRules = db.Query<ShippingFeeTemplateOtherRule>(tempOtherRuleSql, new { tempId = temp.Id });
                    if (otherRules != null && otherRules.Any())
                        temp.OtherRules.AddRange(otherRules);
                }
            }
            return temp;
        }
        /// <summary>
        /// 根据id获取运费模板，状态是开启的
        /// </summary>
        /// <param name="tmpeId"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public ShippingFeeTemplate GetFirst(int tmpeId, int fxUserId)
        {
            var db = this.DbConnection;
            var temp = db.Query<ShippingFeeTemplate>("SELECT TOP 1 * FROM dbo.ShippingFeeTemplate (NOLOCK) WHERE Id = @tmpeId AND CreateBy = @fxUserId AND Status = 1", new { tmpeId, fxUserId })?.FirstOrDefault();
            if (temp != null)
            {
                if (temp.IsFree || temp.IsRemote)
                {
                    var tempOtherRuleSql = "SELECT * FROM dbo.ShippingFeeTemplateOtherRule (NOLOCK) WHERE TemplateId = @tempId";
                    var otherRules = db.Query<ShippingFeeTemplateOtherRule>(tempOtherRuleSql, new { tempId = temp.Id });
                    if (otherRules != null && otherRules.Any())
                        temp.OtherRules.AddRange(otherRules);
                }
            }
            return temp;
        }

        /// <summary>
        /// 根据用户id获取所有的运费模板，排除已经删除的
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="loadOtherInfo">是否补充其他规则</param>
        /// <param name="businessType">默认为0,1688类型;1，小站名片类型</param>
        /// <returns></returns>
        public List<ShippingFeeTemplate> GetList(int fxUserId, bool loadOtherInfo = true, int businessType = 0)
        {
            var db = this.DbConnection;
            var allTemps =
                db.Query<ShippingFeeTemplate>(
                    "SELECT * FROM dbo.ShippingFeeTemplate (NOLOCK) WHERE CreateBy = @fxUserId AND Status != 3 AND BusinessType = @businessType",
                    new { fxUserId, businessType })?.ToList();
            if (allTemps != null && allTemps.Any())
            {
                if (loadOtherInfo)
                {
                    var needOtherInfoTempIds = allTemps.Where(a => a.IsFree || a.IsRemote).Select(a => a.Id)?.Distinct()?.ToList();
                    var tempOtherRuleSql = "SELECT * FROM dbo.ShippingFeeTemplateOtherRule (NOLOCK) WHERE TemplateId in@tempIds";
                    var otherRules = db.Query<ShippingFeeTemplateOtherRule>(tempOtherRuleSql, new { tempIds = needOtherInfoTempIds });
                    if (otherRules != null && otherRules.Any())
                    {
                        allTemps.ForEach(temp =>
                        {
                            var cuurOtherInfo = otherRules.Where(o => o.TemplateId == temp.Id);
                            if (cuurOtherInfo != null && cuurOtherInfo.Any())
                                temp.OtherRules.AddRange(cuurOtherInfo);
                        });
                    }
                }
            }
            return allTemps;
        }

        /// <summary>
        /// 根据用户id获取所有的运费模板，只获取生效的模板
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="businessType">默认为0,1688类型;1，小站名片类型</param>
        /// <returns></returns>
        public List<ShippingFeeTemplate> GetList(List<int> fxUserIds, int businessType = 0)
        {
            var db = this.DbConnection;
            var allTemps =
                db.Query<ShippingFeeTemplate>(
                    "SELECT * FROM dbo.ShippingFeeTemplate (NOLOCK) WHERE CreateBy IN @fxUserIds AND Status = 1 AND BusinessType = @businessType",
                    new { fxUserIds, businessType })?.ToList();
            if (allTemps != null && allTemps.Any())
            {
                var needOtherInfoTempIds = allTemps.Where(a => a.IsFree || a.IsRemote).Select(a => a.Id)?.Distinct()?.ToList();
                var tempOtherRuleSql = "SELECT * FROM dbo.ShippingFeeTemplateOtherRule (NOLOCK) WHERE TemplateId in@tempIds";
                var otherRules = db.Query<ShippingFeeTemplateOtherRule>(tempOtherRuleSql, new { tempIds = needOtherInfoTempIds });
                if (otherRules != null && otherRules.Any())
                {
                    allTemps.ForEach(temp =>
                    {
                        var cuurOtherInfo = otherRules.Where(o => o.TemplateId == temp.Id);
                        if (cuurOtherInfo != null && cuurOtherInfo.Any())
                            temp.OtherRules.AddRange(cuurOtherInfo);
                    });
                }
            }
            return allTemps;
        }

        /// <summary>
        /// 修改模板的状态
        /// </summary>
        /// <param name="tempId"></param>
        /// <returns></returns>
        public int UpdateStatusById(int tempId, int status)
        {
            var db = this.DbConnection;
            return db.Execute($"UPDATE dbo.ShippingFeeTemplate SET Status ={status},UpdateTime = GETDATE() WHERE Id = @tempId", new { tempId});
        }

        /// <summary>
        /// 检查是否存在可以用模板
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="businessType">默认为0,1688类型;1，小站名片类型</param>
        /// <returns></returns>
        public bool CheckUseTemp(int fxUserId, int businessType = 0)
        {
            var db = this.DbConnection;
            var row = db
                .Query<int>(
                    "SELECT COUNT(*) FROM dbo.ShippingFeeTemplate (NOLOCK) WHERE CreateBy = @fxUserId AND Status = 1 AND BusinessType = @businessType",
                    new { fxUserId, businessType })?.FirstOrDefault();
            return row > 0;
        }

        /// <summary>
        /// 添加运费模板
        /// </summary>
        /// <param name="shippingFeeTemplate"></param>
        /// <returns></returns>
        public int AddShippingFeeTemplate(ShippingFeeTemplate shippingFeeTemplate)
        {
            var otherRules = new List<ShippingFeeTemplateOtherRule>();
            if (shippingFeeTemplate.OtherRules != null && shippingFeeTemplate.OtherRules.Any())
            {
                otherRules.AddRange(shippingFeeTemplate.OtherRules);
                shippingFeeTemplate.OtherRules = null;
            }

            var db = this.DbConnection;
            var newId = db.Insert(shippingFeeTemplate);
            if (newId <= 0)
                throw new LogicException("添加运费模板基础信息失败");
            if(otherRules.Any())
            {
                otherRules.ForEach(o =>
                {
                    o.TemplateId = newId.Value;
                    db.Insert<ShippingFeeTemplateOtherRule>(o);
                });
            }
            return newId.Value;
        }

        /// <summary>
        /// 更新运费模板数据及更新其他规则
        /// </summary>
        /// <param name="shippingFeeTemplate"></param>
        /// <returns></returns>
        public int UpdateShippingFeeTemplate(ShippingFeeTemplate shippingFeeTemplate)
        {
            var db = this.DbConnection;
            var tempInfoSQL = @"UPDATE dbo.ShippingFeeTemplate SET 
TemplateName = @TemplateName,TemplateType = @TemplateType,TemplateRule=@TemplateRule,IsFree=@IsFree,IsRemote=@IsRemote,UpdateTime=GETDATE()
WHERE Id = @Id";
            var row = db.Execute(tempInfoSQL, new
            {
                shippingFeeTemplate.Id,
                shippingFeeTemplate.TemplateName,
                shippingFeeTemplate.TemplateType,
                shippingFeeTemplate.TemplateRule,
                shippingFeeTemplate.IsFree,
                shippingFeeTemplate.IsRemote
            });

            var tempOtherInfo = @"UPDATE dbo.ShippingFeeTemplateOtherRule SET 
OtherRuleCode=@OtherRuleCode,AddressData=@AddressData,TemplateRule=@TemplateRule
WHERE Id = @Id";
            shippingFeeTemplate.OtherRules.ForEach(o =>
            {
                if (o.Id <= 0)
                    db.Insert<ShippingFeeTemplateOtherRule>(o);
                else
                {
                    db.Execute(tempOtherInfo, new
                    {
                        o.Id,
                        o.OtherRuleCode,
                        o.TemplateRule,
                        o.AddressData
                    });
                }
            });
            return row;
        }

        /// <summary>
        /// 更新运费模板数据及更新其他规则
        /// </summary>
        /// <param name="shippingFeeTemplate"></param>
        /// <returns></returns>
        public void UpdateShippingFeeTemplate(ShippingFeeTemplate newDbTempInfo, ShippingFeeTemplate oldDbTempInfo, string setTempInfoStr, string setOtherInfoStr)
        {
            var db = this.DbConnection;
            if (string.IsNullOrWhiteSpace(setTempInfoStr) == false)
            {
                var tempInfoSQL = $@"UPDATE dbo.ShippingFeeTemplate SET {setTempInfoStr.TrimEnd(',')},UpdateTime=GETDATE() WHERE Id = @Id";
                var newParam = new {
                    newDbTempInfo.Id,
                    newDbTempInfo.TemplateName,
                    newDbTempInfo.TemplateType,
                    newDbTempInfo.TemplateRule,
                    newDbTempInfo.IsFree,
                    newDbTempInfo.IsRemote
                };
                var oldParam = new {
                    oldDbTempInfo.Id,
                    oldDbTempInfo.TemplateName,
                    oldDbTempInfo.TemplateType,
                    oldDbTempInfo.TemplateRule,
                    oldDbTempInfo.IsFree,
                    oldDbTempInfo.IsRemote
                };
                int row = db.ExecuteByCloud(tempInfoSQL,
                    CloudPlatformType.TouTiao.ToString(),
                    newParam,
                    () => {
                        //回滚原来的修改
                        var command = new CommandDefinition(tempInfoSQL, oldParam);
                        return command;
                    }
                );
            }


            newDbTempInfo.OtherRules.ForEach(o =>
            {
                if (o.Id <= 0)
                {
                    db.Insert<ShippingFeeTemplateOtherRule>(o);
                }
                else
                {
                    if (string.IsNullOrWhiteSpace(setOtherInfoStr) == false)
                    {
                        var tempOtherInfoSQL = $@"UPDATE dbo.ShippingFeeTemplateOtherRule SET {setOtherInfoStr.TrimEnd(',')} WHERE Id = @Id";
                        var newParam = new
                        {
                            o.Id,
                            o.TemplateRule,
                            o.AddressData
                        };
                        var oldOther = oldDbTempInfo.OtherRules.FirstOrDefault(or => or.Id == o.Id);
                        var oldParam = new
                        {
                            oldOther.Id,
                            oldOther.TemplateRule,
                            oldOther.AddressData
                        };
                        db.ExecuteByCloud(tempOtherInfoSQL,
                            CloudPlatformType.TouTiao.ToString(),
                            newParam,
                            () => {
                                //回滚原来的修改
                                var command = new CommandDefinition(tempOtherInfoSQL, oldParam);
                                return command;
                            }
                        );
                    }
                }
            });

            //更新UpdateTime字段
            if (string.IsNullOrWhiteSpace(setTempInfoStr) && newDbTempInfo.OtherRules != null && newDbTempInfo.OtherRules.Any())
            {
                var tempInfoSQL = $@"UPDATE dbo.ShippingFeeTemplate SET UpdateTime=GETDATE() WHERE Id = @Id";
                //只更新UpdateTime字段，不需要跨云
                int row = db.Execute(tempInfoSQL,new { newDbTempInfo.Id });
            }
        }

        /// <summary>
        /// 添加变更记录
        /// </summary>
        /// <param name="changeRecord"></param>
        /// <returns></returns>
        public int AddChangeRecord(ShippingFeeTemplateChangeRecord changeRecord)
        {
            var db = this.DbConnection;
            var rowId = db.Insert<ShippingFeeTemplateChangeRecord>(changeRecord);
            return rowId.Value;
        }

        /// <summary>
        /// 获取变更日志
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="queryModel">分页查询</param>
        /// <returns></returns>
        public PagingResultModel<ShippingFeeTemplateChangeRecord> GetChangeRecords(int fxUserId, PagingQueryModel queryModel)
        {
            var db = this.DbConnection;
            var total = db.Query<int>($"SELECT COUNT(*) FROM dbo.ShippingFeeTemplateChangeRecord (NOLOCK) WHERE CreateBy = @fxUserId", new { fxUserId }).FirstOrDefault();
            var sql = @"SELECT * FROM dbo.ShippingFeeTemplateChangeRecord (NOLOCK) 
WHERE CreateBy = @fxUserId ORDER BY CreateTime DESC";
            if (queryModel.PageIndex > 0 && queryModel.PageSize > 0)
                sql += $" OFFSET {(queryModel.PageIndex - 1) * queryModel.PageSize} ROWS FETCH NEXT {queryModel.PageSize} ROWS ONLY";
            var rows = db.Query<ShippingFeeTemplateChangeRecord>(sql, new { fxUserId })?.ToList();

            var reuslt = new PagingResultModel<ShippingFeeTemplateChangeRecord>()
            {
                PageIndex = queryModel.PageIndex,
                PageSize = queryModel.PageSize,
                Total = total,
                Rows = rows
            };
            return reuslt;
        }

        /// <summary>
        /// 去除其他关联的规则
        /// </summary>
        /// <param name="otherRuleIds"></param>
        public void DelOtherRule(List<int> otherRuleIds)
        {
            var db = this.DbConnection;
            otherRuleIds.ForEach(o =>
            {
                //var sql = $"UPDATE dbo.ShippingFeeTemplateOtherRule SET TemplateId = -TemplateId,OtherRuleCode='-'+OtherRuleCode WHERE Id ={o}";
                var sql = $"DELETE dbo.ShippingFeeTemplateOtherRule WHERE Id = @id";
                db.Execute(sql,new { id = o });
            });
        }

        public int DelTemplateById(int tempId, int fxUserId)
        {
            var db = this.DbConnection;
            var createby = db.QueryFirstOrDefault<int>($"SELECT TOP 1 CreateBy FROM dbo.ShippingFeeTemplate (NOLOCK)  WHERE Id ={tempId}");
            if(createby <= 0)
            {
                throw new LogicException("模板未找到");
            }
            if(fxUserId != createby)
            {
                throw new LogicException("权限不足");
            }
            var delSQL = $@"
DELETE FROM dbo.ShippingFeeTemplate WHERE Id ={tempId};
DELETE FROM dbo.ShippingFeeTemplateOtherRule WHERE TemplateId ={tempId};";
            int result = db.Execute(delSQL);
            return result;
        }

        /// <summary>
        /// 是否存在运费模板
        /// </summary>
        /// <param name="currentFxUserId"></param>
        /// <param name="businessType">默认为0,1688类型;1，小站名片类型</param>
        /// <returns></returns>
        public int CheckShippingFeeTemplateStatus(int currentFxUserId, int businessType = 0)
        {
            var sql = $"SELECT COUNT(1) FROM ShippingFeeTemplate WITH(NOLOCK) WHERE CreateBy = @currentFxUserId AND Status IN (1, 2) AND BusinessType = @businessType";
            return DbConnection.QueryFirstOrDefault<int>(sql,new { currentFxUserId, businessType });
        }
    }
}
