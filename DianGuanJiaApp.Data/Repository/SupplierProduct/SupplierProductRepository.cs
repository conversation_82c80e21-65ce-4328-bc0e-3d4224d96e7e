using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Entity.SupplierProduct;
using DianGuanJiaApp.Data.Model.SupplierProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Z.Dapper.Plus;

namespace DianGuanJiaApp.Data.Repository.SupplierProduct
{
    /// <summary>
    /// 货盘商品仓储层
    /// </summary>
    public class SupplierProductRepository : SupplierProductBaseRepository<SupplierProductEntity>
    {
        /// <summary>
        /// 获取默认的数据库连接
        /// </summary>
        public SupplierProductRepository()
        {
        }
        
        /// <summary>
        /// 根据连接字符串和数据库类型获取数据库连接
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="isUseMySql"></param>
        public SupplierProductRepository(string connectionString, bool isUseMySql) : base(connectionString, isUseMySql)
        {
        }

        /// <summary>
        /// 批量添加
        /// </summary>
        /// <param name="models"></param>
        public void BatchAdd(List<SupplierProductEntity> models)
        {
            if (models == null || models.Any() == false)
                return;
            
            const int count = 500;
            var total = models.Count;
            var times = total / count;
            if (total % count > 0)
                times++;

            using (var db = DbConnection)
            {
                if (db.State != ConnectionState.Open)
                    db.Open();
                for (var i = 0; i < times; i++)
                {
                    var list = models.Skip(i * count).Take(count).ToList();
                    db.BulkInsert(list);
                }
            }
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<SupplierProductEntity>> GetPageList(SupplierProductQuery query)
        {
            var strFields = "DISTINCT t1.*";
            if (query.Fields != null && query.Fields.Any())
                strFields = string.Join(",", query.Fields);

            var whereStr = "t1.Status = 1 AND IsPublic = true  ";
            // 商品类型，默认为空，小站商品
            var whereExt = query.ProductType.IsNullOrEmpty() ?
                "AND (t1.ProductType IS NULL OR t1.ProductType = '')" :
                "AND t1.ProductType = @ProductType";
            var innerStr = string.Empty;
            var parameters = new DynamicParameters();
            var orderByStr = string.Empty;
             // 我的小站
            if (query.Tag == 1)
            {
                // 仅我的小站才可以查看下架的商品
                if (query.IsPublic.HasValue)
                {
                    whereStr = "t1.Status = 1 AND IsPublic = @IsPublic ";
                    parameters.Add("IsPublic", query.IsPublic);
                }
                else whereStr = "t1.Status = 1 ";
                whereStr += " AND t1.FxUserId=@FxUserId ";
                parameters.Add("FxUserId", query.FxUserId);
            }
            // 厂家小站
            if (query.Tag == 2)
            {
                if (query.SupplierFxUserId.IsNullOrEmpty()) throw new LogicException("厂家信息不允许为空！");

                whereStr += " AND t1.FxUserId=@FxUserId ";
                parameters.Add("FxUserId", query.SupplierFxUserId.First());
            }
            // 选品铺货页面
            if (query.Tag == 3)
            {
                if (query.SupplierFxUserId.IsNullOrEmpty()) throw new LogicException("厂家信息不允许为空！");

                whereStr += " AND t1.FxUserId IN @FxUserId ";
                parameters.Add("FxUserId", query.SupplierFxUserId);

                if (query.CategoryId != null && query.CategoryId.Any())
                {
                    innerStr += " INNER JOIN SupplierProductCateRelation t2 ON t1.Uid = t2.SupplierProductUid AND t2.Status = 1 ";
                    whereStr += " AND CateId IN @CategoryId";
                    parameters.Add("CategoryId", query.CategoryId);
                }

                if (query.ExpressBillId != null && query.ExpressBillId.Any())
                {
                    innerStr += " INNER JOIN SupProductWaybillRelation t3 ON t1.Uid = t3.SupplierProductUid AND t3.Status = 1 ";
                    whereStr += " AND t3.WaybillId IN @ExpressBillId";
                    parameters.Add("ExpressBillId", query.ExpressBillId);
                }
            }
            // 商品名称
            if (!string.IsNullOrEmpty(query.ProductName))
            {
                whereStr += " AND Subject LIKE @Subject";
                parameters.Add("@Subject", $"%{query.ProductName}%");
            }

            // 排序字段 
            switch (query.OrderByField)
            {
                // 供货价排序
                case "DistributePrice":
                {
                    // 低价
                    if (query.IsOrderDesc) orderByStr += " MinPrice, ";
                    // 高价
                    else orderByStr += " MaxPrice DESC, ";
                    break;
                }
                // 上新时间排序
                case "PublicTime":
                {
                    // 降序
                    if (query.IsOrderDesc) orderByStr += " PublicTime DESC,";
                    // 升序
                    else orderByStr += " PublicTime,";
                    break;
                }
            }

            // 补充类型条件
            whereStr += whereExt;

            #region 拼SQL

            var sql = $@"SELECT COUNT(DISTINCT t1.Id) FROM SupplierProduct t1 WITH(NOLOCK) {innerStr} WHERE {whereStr} ; 
SELECT {strFields} FROM SupplierProduct t1 WITH(NOLOCK) {innerStr}  WHERE {whereStr} ORDER BY {orderByStr} UpdateTime DESC
OFFSET {(query.PageIndex - 1) * query.PageSize} ROWS FETCH NEXT {query.PageSize} ROWS ONLY;
";
            if (IsUseMySql)
            {
                sql = $@"SELECT COUNT(DISTINCT t1.Id) FROM SupplierProduct t1 {innerStr}  WHERE {whereStr} ; 
SELECT {strFields} FROM SupplierProduct t1 {innerStr}  WHERE {whereStr} ORDER BY {orderByStr} UpdateTime DESC
limit {(query.PageIndex - 1) * query.PageSize},{query.PageSize};
";
            }

            #endregion

            #region 记录执行的SQL语句
            if (CustomerConfig.IsDebug)
            {
                WriteSqlToLog(sql, parameters, "GetPageListSql.txt", "SupplierProduct", "SupplierProduct");
            }
            #endregion

            var db = DbConnection;
            var grid = db.QueryMultiple(sql, param: parameters);
            var totalCount = grid.Read<int>().FirstOrDefault();
            var result = grid.Read<SupplierProductEntity>().ToList();

            return Tuple.Create(totalCount, result);
        }

        /// <summary>
        /// 根据uid获取信息
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public SupplierProductEntity GetByUid(long uid)
        {
            const string sqlTemplate = "SELECT * FROM SupplierProduct WITH(NOLOCK) WHERE Uid=@uid";
            var sql = TranSql(sqlTemplate);
            var model = DbConnection.QueryFirstOrDefault<SupplierProductEntity>(sql, new { uid });
            return model;
        }
        
        /// <summary>
        /// 获取信息为复制副本
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<SupplierProductEntity> GetList(List<string> codes, string selectFields = "*", string whereFieldName = "SpuCode")
        {
            var sqlTemplate = $"SELECT {(string.IsNullOrWhiteSpace(selectFields) ? "*" : selectFields)} FROM SupplierProduct WITH(NOLOCK) WHERE {whereFieldName} IN @Codes ";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Query<SupplierProductEntity>(sql, new { Codes = codes }).ToList();
        }

        /// <summary>
        /// 获取该用户已存在SpuCode列表
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<string> GetExistSpuCodeList(int fxUserId)
        {
            const string sql = "SELECT SpuCode FROM SupplierProduct WHERE FxUserId = @FxUserId";
            return DbConnection.Query<string>(sql, new { FxUserId = fxUserId }).ToList();
        }

        /// <summary>
        /// 根据Uid列表获取
        /// </summary>
        /// <param name="uids"></param>
        /// <returns></returns>
        public List<SupplierProductEntity> GetByUids(List<long> uids)
        {
            const string sqlTemplate = "SELECT * FROM SupplierProduct WITH(NOLOCK) WHERE Uid IN @uids AND Status = 1";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Query<SupplierProductEntity>(sql, new { uids }).ToList();
        }

        /// <summary>
        /// 根据Uid列表获取
        /// </summary>
        /// <param name="uids"></param>
        /// <returns></returns>
        public List<SupplierProductEntity> GetAllByUids(List<long> uids)
        {
            const string sqlTemplate = "SELECT * FROM SupplierProduct WITH(NOLOCK) WHERE Uid IN @uids";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Query<SupplierProductEntity>(sql, new { uids }).ToList();
        }

        /// <summary>
        /// 获取用户是否存在货盘商品
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<int> ExistProductByFxUserId(IEnumerable<int> fxUserIds)
        {
            const string sqlTemplate = "SELECT FxUserId FROM SupplierProduct WITH(NOLOCK) WHERE FxUserId IN @fxUserIds AND Status = 1 AND IsPublic = true AND (ProductType IS NULL OR ProductType = '')";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Query<int>(sql, new { fxUserIds }).ToList();
        }

        /// <summary>
        /// 通过基础商品Uid获取货盘商品Uid
        /// </summary>
        /// <param name="baseProductUid">基础商品Uid</param>
        /// <param name="productType">SupplierProduct商品类型</param>
        /// <returns></returns>
        public long GetUidByBaseProductUid(long baseProductUid, string productType = null)
        {
            const string baseSql = "SELECT Uid FROM SupplierProduct WITH(NOLOCK) WHERE FromProductUid = @baseProductUid AND Status = 1";
            // 根据是否传入 productType 动态拼接条件，避免重复或矛盾的 ProductType 条件
            var sqlWithType = string.IsNullOrEmpty(productType)
                ? baseSql + " AND (ProductType IS NULL OR ProductType = '')"
                : baseSql + " AND ProductType = @productType";
            var sql = TranSql(sqlWithType);

            var parameters = new DynamicParameters();
            parameters.Add("baseProductUid", baseProductUid);
            if (!string.IsNullOrEmpty(productType))
            {
                parameters.Add("productType", productType);
            }

            return DbConnection.QueryFirstOrDefault<long>(sql, parameters);
        }

        /// <summary>
        /// 基础商品查询
        /// </summary>
        /// <param name="supplierProductUids"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<SupplierProductEntity> GetSupplierProductByUids(List<long> supplierProductUids, int fxUserId)
        {
            if (supplierProductUids == null || supplierProductUids.Any() == false) return new List<SupplierProductEntity>();

            const int count = 500;
            var total = supplierProductUids.Count;
            var times = total / count;
            if (total % count > 0)
                times++;

            var result = new List<SupplierProductEntity>();

            using (var db = DbConnection)
            {
                if (db.State != ConnectionState.Open) db.Open();

                for (var i = 0; i < times; i++)
                {
                    var tempBaseProductUids = supplierProductUids.Skip(i * count).Take(count).ToList();
                    const string sqlTemplate = "SELECT * FROM SupplierProduct WITH(NOLOCK) p LEFT JOIN SupplierProductSku WITH(NOLOCK) s ON p.Uid = s.SupplierProductUid WHERE p.Uid IN @uids AND p.FxUserId = @fxUserId AND p.Status = 1 AND s.Status=1 ";
                    var sql = TranSql(sqlTemplate);

                    var lookUp = new Dictionary<long, SupplierProductEntity>();
                    var dictSku = new Dictionary<long, Dictionary<long, SupplierProductSku>>();

                    var model = db.Query<SupplierProductEntity, SupplierProductSku, SupplierProductEntity>(sql, (p, ps) =>
                    {
                        SupplierProductEntity product = null;
                        if (!lookUp.TryGetValue(p.Id, out product))
                        {
                            product = p;
                            product.Skus = new List<SupplierProductSku>();
                            lookUp.Add(product.Id, product);
                            dictSku.Add(product.Id, new Dictionary<long, SupplierProductSku>());
                        }
                        //添加sku的数据
                        if (ps != null && dictSku[product.Id].ContainsKey(ps.Id) == false)
                        {
                            product.Skus.Add(ps);
                            dictSku[product.Id].Add(ps.Id, null);
                        }
                        return product;
                    }, new { uids = tempBaseProductUids, fxUserId= fxUserId }, splitOn: "Id,Id,Id").ToList();

                    var data = lookUp.Select(k => k.Value ?? new SupplierProductEntity()).ToList();
                    if (lookUp.Count != 0)
                    {
                        result.AddRange(data);
                    }
                }
            }
            return result;
        }


        public void BulkDelete(List<SupplierProductEntity> deleteSkuList)
        {
            if (deleteSkuList == null || deleteSkuList.Any() == false)
                return;

            const int count = 500;
            var total = deleteSkuList.Count;
            var times = total / count;
            if (total % count > 0)
                times++;

            var sql = "UPDATE SupplierProduct SET Status=0 WHERE Uid IN @UidList";

            for (var i = 0; i < times; i++)
            {
                var list = deleteSkuList.Skip(i * count).Take(count).ToList();
                var uidList = list.Select(x => x.Uid).ToList();
                DbConnection.Execute(sql, new { UidList = uidList });
            }
        }
    }
}