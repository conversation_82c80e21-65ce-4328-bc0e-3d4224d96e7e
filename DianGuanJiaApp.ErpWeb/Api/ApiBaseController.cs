using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Web.Mvc;

namespace DianGuanJiaApp.ErpWeb.Api
{
    public class ApiBaseController : Controller
    {
        protected ApiRequestModel RequestModel { get; set; }

        protected override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            var request = filterContext.HttpContext.Request;
            var dbName = string.Empty;

            //PostDESData不为空时，即为加密请求
            var postDESData = request["postDESData"].ToString2();
            if (string.IsNullOrEmpty(postDESData) == false)
            {
                var dicJson = DES.DecryptDES(postDESData, CustomerConfig.LoginCookieEncryptKey);
                var dic = dicJson.ToObject<Dictionary<string, string>>();

                RequestModel = new ApiRequestModel
                {
                    TimeStamp = dic.ContainsKey("timestamp") ? dic["timestamp"].ToString2() : "",
                    Sign = dic.ContainsKey("sign") ? dic["sign"].ToString2() : "",
                    ShopId = dic.ContainsKey("shopid") ? dic["shopid"].ToString2() : "",
                    FxUserId = dic.ContainsKey("fxuserid") ? dic["fxuserid"].ToString2() : "",
                    Param = dic.ContainsKey("param") ? dic["param"].ToString2() : "",
                    IsEncrypt = true
                };
                dbName = dic.ContainsKey("dbname") ? dic["dbname"].ToString2() : "";
            }
            else
            {
                RequestModel = new ApiRequestModel
                {
                    TimeStamp = request["timestamp"]?.ToString(),
                    Sign = request["sign"]?.ToString(),
                    ShopId = request["shopid"]?.ToString(),
                    FxUserId = request["fxuserid"]?.ToString(),
                    Param = request["param"]?.ToString(),
                };
                dbName = request["dbname"].ToString2();
            }


            try
            {
                if (string.IsNullOrEmpty(RequestModel.TimeStamp))
                    throw new LogicException("时间戳不能为空");
                var date = RequestModel.TimeStamp.ConvertTimeStampToDateTime();
                //时间上下波动5分钟
                var maxExpiredSeconds = 120;
                if (date == null || date < DateTime.Now.AddSeconds(-maxExpiredSeconds) || date > DateTime.Now.AddSeconds(maxExpiredSeconds))
                {
                    if(CustomerConfig.IsDebug == false)
                        throw new LogicException("请求已过期，请更新参数重新请求");
                }
                if (string.IsNullOrEmpty(RequestModel.Sign))
                    throw new LogicException("签名不能为空");
                if (string.IsNullOrEmpty(RequestModel.FxUserId))
                    throw new LogicException("分销用户Id不能为空");
                var dict = new SortedDictionary<string, string>();
                dict.Add("timestamp", RequestModel.TimeStamp);
                dict.Add("param", RequestModel.Param ?? "");
                //dict.Add("shopid", RequestModel.ShopId);
                dict.Add("fxuserid", RequestModel.FxUserId);
                if (CustomerConfig.IsDebug == false)
                {
                    var sign = CommUtls.ApiParamSign(dict);
                    if (sign != RequestModel.Sign)
                        throw new LogicException("签名错误");
                }
                var userFxService = new UserFxService();
                var userFx = userFxService.Get(RequestModel.FxUserId.ToInt());
                if (userFx == null)
                    throw new LogicException($"分销用户ID【{RequestModel.FxUserId}】错误，或对应的分销用户不存在.");

                //var dbName = request["dbname"].ToString2();
                if(dbName.IsNotNullOrEmpty() && (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString() || CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString()))
                    new SiteContext(userFx, dbName, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
                else
                    new SiteContext(userFx, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });

            }
            catch (LogicException lex)
            {
                filterContext.Result = FalidResult(lex.Message);
            }
            catch (Exception ex)
            {
                Log.WriteError($"Api请求：{RequestModel?.ToJson()}，异常：{ex}");
                filterContext.Result = FalidResult("接口繁忙，请稍后再试");
            }

            base.OnActionExecuting(filterContext);
        }


        protected ActionResult SuccessResult(dynamic data = null)
        {
            return new CustomJsonResult(new AjaxResult()
            {
                Success = true,
                Data = data
            });
        }

        protected ActionResult SuccessResult(string message, dynamic data)
        {
            return new CustomJsonResult(new AjaxResult()
            {
                Message = message,
                Success = true,
                Data = data
            });
        }

        protected ActionResult FalidResult(string message, dynamic data = null)
        {
            return new CustomJsonResult(new AjaxResult()
            {
                Success = false,
                Message = message,
                Data = data
            });
        }
    }
}