
using DianGuanJiaApp.ErpWeb.Controllers;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.EntityExtension;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.Mvc;
using System.Web.UI.WebControls;

namespace DianGuanJiaApp.ErpWeb.Api
{
    public class CommonApiController : ApiBaseController
    {
        private CommonSettingService _service = new CommonSettingService();

        [LogForOperatorFilter("跨云平台更新配置API")] 
        public ActionResult SaveCommonSetting()
        {
            var setting = RequestModel.GetParamModel<CommonSetting>();

            bool result = true;

            //try
            //{
            //    _service.Set(setting.Key, setting.Value, setting.ShopId);
            //}
            //catch (Exception ex)
            //{
            //    result = false;
            //    Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】更新配置：{ex}");
            //    throw new LogicException("服务器繁忙，请稍后再试！");
            //}

            //记录请求日志
            var logView = new InvokeApiDataLogModel
            {
                BatchId = Guid.NewGuid().ToString(),
                RequestContent = setting.ToJson(),
                ShopId = setting.ShopId,
                StartTime = DateTime.Now,
                Status = "接收端"
            };
            InvokeApiDataTrackingService.Instance.WriteLog(logView);

            var retryTimes = 3;
            for (var i = 0; i < retryTimes; i++)
            {
                try
                {
                    _service.Set(setting.Key, setting.Value, setting.ShopId);

                    break;
                }
                catch (Exception ex)
                {
                    ExceptionLogDataEventTrackingService.Instance.WriteLog(ex, GetType().Name);
                    if (i >= retryTimes - 1)
                        throw new LogicException("服务器繁忙，请稍后再试！");
                    Thread.Sleep(100 * i);
                }
            }

            return SuccessResult(result);
        }

        [LogForOperatorFilter("头条Spi卖家修改地址审核")]
        public ActionResult ProcessTouTiaoFxSpiMessage()
        {
            var requestApiModel = RequestModel.GetParamModel<TouTiaoSpiMessageModel>();

            try
            {
                //抖店云处理，未有FxDbConfig配置的，直接return
                if (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString())
                {
                    var fxDbConfig = new FxDbConfigService().GetByFxUserId(RequestModel.FxUserId.ToInt(), CustomerConfig.CloudPlatformType);
                    if (fxDbConfig == null)
                    {
                        return SuccessResult((new { code = 100003, msg = "解析param_json失败" }).ToJson());
                    }
                }
                var result = new MessageController().ProcessTouTiaoFxSpiMessage(requestApiModel);
                return SuccessResult(result);
            }
            catch (Exception ex)
            {
                Log.WriteError($"CommonApi.ProcessTouTiaoFxSpiMessage头条Spi卖家修改地址审核失败：{ex}");
                return SuccessResult((new { code = 100003, msg = "系统错误" }).ToJson());
            }
        }

        [LogForOperatorFilter("授权自动添加快递模板")]
        public ActionResult AutoAddTemplate()
        {
            var cainiaoAuthInfo = RequestModel.GetParamModel<CaiNiaoAuthInfo>();
            //记录请求日志
            var logView = new InvokeApiDataLogModel
            {
                BatchId = "授权自动添加快递模板",
                RequestContent = cainiaoAuthInfo.ToJson(),
                ShopId = cainiaoAuthInfo.ShopId,
                StartTime = DateTime.Now,
                Status = "接收端",
                StackTrace = Environment.StackTrace
            };
            InvokeApiDataTrackingService.Instance.WriteLog(logView);
            try
            {
                var temlateSetCtrl = new TemplateSetController();
                temlateSetCtrl.AutoAddTemplate(cainiaoAuthInfo);
                return SuccessResult();
            }
            catch (Exception ex)
            {
                Log.WriteError($"CommonApi.AuthAutoAddTemplate授权自动添加快递模板失败：{ex}");
                return FalidResult(ex.Message);
            }
        }

    }
}