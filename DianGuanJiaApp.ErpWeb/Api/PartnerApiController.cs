using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using System;
using System.Linq;
using System.Web.Mvc;
using System.Threading;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.ErpWeb.Controllers;

namespace DianGuanJiaApp.ErpWeb.Api
{
    public class PartnerApiController : ApiBaseController
    {
        private ProductFxService _productFxService = new ProductFxService();

        [LogForOperatorFilter("跨云平台解绑厂家API")]
        public ActionResult SaveReBindProductOrder()
        {
            var requestApiModel = RequestModel.GetParamModel<BindSupplierRequestForApiModel>();
            bool result = true;

            //抖店云处理，未有FxDbConfig配置的，直接return
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString())
            {
                var fxDbConfig = new FxDbConfigService().GetByFxUserId(requestApiModel.CurFxUserId, CustomerConfig.CloudPlatformType);
                if(fxDbConfig == null)
                {
                    return SuccessResult(result);
                }
            }

            var userFxService = new UserFxService();
            var curFxUser = userFxService.Get(requestApiModel.CurFxUserId);
            var asyncTaskService = new AsyncTaskService();
            var task = requestApiModel.Task;
            if (task != null)
            {
                task.CloudPlatformType = CustomerConfig.CloudPlatformType;
                asyncTaskService.AddAsyncTask(task);
            }
            //启动线程，执行任务
            ThreadPool.QueueUserWorkItem(state =>
            {
                try
                {
                    #region 没锁再执行，有锁等待。暂时不用，POST端已做处理。
                    //var _servicce = new CommonSettingService();

                    //var op = Data.Enum.OptimisticLockOperationType.BindProductSupplier;
                    //var opId = $"FX{fxUserId}";

                    //for (var x = 1; x <= 5; x++)
                    //{
                    //    if (!_servicce.GetOptimisticLock(op, opId))
                    //        break;

                    //    Thread.Sleep(5 * x * x * 1000);
                    //}
                    #endregion
                    var fxUserId = requestApiModel.FxUserId;
                    var userFx = userFxService.Get(fxUserId);
                    var siteContext = new SiteContext(userFx, "", new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
                    //更新任务状态进行中
                    if (task != null)
                    {
                        task.Status = 1;//进行中
                        task.UpdateTime = DateTime.Now;
                        asyncTaskService.UpdatePendingStatus(task);
                    }
                    if (SiteContext.Current.CurrentLoginShop != null && SiteContext.Current.CurrentDbAreaConfig != null && SiteContext.Current.CurrentDbAreaConfig.Any())
                    {
                        if(SiteContext.Current.CurrentDbAreaConfig.Any(x=> x?.DbNameConfig == null) == true)
                            Log.WriteWarning($"PartnerApi.SaveReBindProductOrder.SiteContext.Current.CurrentDbAreaConfig中存在为空的DbNameConfig，CurrentShopId:{SiteContext.Current.CurrentShopId}");
                        var dbArea = SiteContext.Current.CurrentDbAreaConfig.Select(x => x?.DbNameConfig).Where(x=>x != null).Select(y => new { DbName = y.DbName, y.NickName }).ToList();

                        if (dbArea != null && dbArea.Any())
                        {
                            foreach (var _db in dbArea)
                            {
                                //指定分库
                                var sc = new SiteContext(userFx, _db.DbName, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
                                _productFxService.ReBindProductOrderLogic(requestApiModel.RequestModel, requestApiModel.SupplierId, requestApiModel.FxUserId, requestApiModel.IsAgent, requestApiModel.Task);
                            }
                        }
                        new SiteContext(curFxUser, "", new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
                    }
                    else
                    {
                        Log.Debug(() => $"解绑厂家，相关信息：{requestApiModel.ToJson(true)}",
                            $"ReBindProductOrderLogic_{DateTime.Now:yyyy-MM-dd}.log");
                        _productFxService.ReBindProductOrderLogic(requestApiModel.RequestModel, requestApiModel.SupplierId, requestApiModel.FxUserId, requestApiModel.IsAgent, requestApiModel.Task); 
                    }
                    if (task != null)
                    {
                        task.Status = 5;//执行完成
                        task.UpdateTime = DateTime.Now;
                        asyncTaskService.UpdateStatus(task);
                    }
                }
                catch (Exception ex)
                {
                    result = false;
                    Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】解绑厂家：{ex}");
                    if (task != null) 
                    {
                        task.Status = -1;//失败
                        task.ExceptionDesc = ex.Message;
                        task.UpdateTime = DateTime.Now;
                        asyncTaskService.UpdateAsyncTask(task);
                    }
                    //throw new LogicException("服务器繁忙，请稍后再试！");
                }
            });
            return SuccessResult(result);
        }

        public ActionResult GetOtherCloudAsyncTask()
        {
            var queryApiModel = RequestModel.GetParamModel<AsyncTaskQueryModel>();
            queryApiModel.CloudPlatformType = CustomerConfig.CloudPlatformType;

            //抖店云处理，未有FxDbConfig配置的，直接return
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString())
            {
                var fxDbConfig = new FxDbConfigService().GetByFxUserId(queryApiModel.FxUserId ?? 0, CustomerConfig.CloudPlatformType);
                if (fxDbConfig == null)
                {
                    return SuccessResult(null);
                }
            }

            var asyncTaskService = new AsyncTaskService();
            var task = asyncTaskService.GetAsyncTask(queryApiModel);

            return SuccessResult(task);
        }
        /// <summary>
        /// 是否存在商品绑定关系
        /// </summary>
        /// <returns></returns>
        public ActionResult IsHasProductBindRelation()
        {
            //判空处理
            var requestApiModel = RequestModel.GetParamModel<BindSupplierRequestForApiModel>();
            if (requestApiModel == null)
            {
                return FalidResult("解绑用户信息不能为空。", true);
            }
            //用户信息
            var fxUserId = requestApiModel.FxUserId;
            var supplierUserId = requestApiModel.SupplierId;
            //商家存在商品绑定关系
            var isHasProductRelation = SiteContextHandler.Handle(fxUserId,
                () => new PathFlowReferenceService().IsHasProductBindRelation(fxUserId, supplierUserId));
            if (isHasProductRelation)
            {
                return SuccessResult("商家存在商品绑定关系", true);
            }
            //厂家存在商品绑定关系
            isHasProductRelation = SiteContextHandler.Handle(supplierUserId,
                () => new PathFlowReferenceService().IsHasProductBindRelation(fxUserId, supplierUserId));
            if (isHasProductRelation)
            {
                return SuccessResult("厂家存在商品绑定关系", true);
            }
            //商家&厂家d都不存在商品绑定关系
            return SuccessResult("商家&厂家都不存在商品绑定关系", false);
        }

		public ActionResult IsHasAndDelProductBindRelation()
		{
			//判空处理
			var requestApiModel = RequestModel.GetParamModel<BindSupplierRequestForApiModel>();
			if (requestApiModel == null)
			{
				return FalidResult("解绑用户信息不能为空。", true);
			}
			//用户信息
			var fxUserId = requestApiModel.FxUserId;
			var supplierUserId = requestApiModel.SupplierId;
			//商家存在商品绑定关系
			//初始化用户信息
			new SiteContext(new UserFxService().Get(fxUserId), "", new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });

			var isHasProductRelation = new PathFlowReferenceService().IsHasProductBindRelation(fxUserId, supplierUserId);
			if (isHasProductRelation)
			{
				try
				{
					new PartnerController().DelProductRelation(fxUserId, supplierUserId);
				}
				catch (Exception ex)
				{
					return SuccessResult("商家存在商品绑定关系并清理绑定关系数据失败", true);
				}
			}

			//厂家存在商品绑定关系
			new SiteContext(new UserFxService().Get(supplierUserId), "", new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
			isHasProductRelation = new PathFlowReferenceService().IsHasProductBindRelation(fxUserId, supplierUserId);

			if (isHasProductRelation)
			{
				try
				{
					new PartnerController().DelProductRelation(fxUserId, supplierUserId);
				}
				catch (Exception ex)
				{
					return SuccessResult("厂家存在商品绑定关系并清理绑定关系数据失败", true);
				}
			}

			//商家&厂家d都不存在商品绑定关系
			return SuccessResult("商家&厂家都不存在商品绑定关系", false);
		}

	}
}