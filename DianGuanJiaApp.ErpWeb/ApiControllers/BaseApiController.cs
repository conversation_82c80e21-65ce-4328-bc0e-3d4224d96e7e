using DianGuanJiaApp.Controllers;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.Http.Controllers;
using System.Web.Http.Description;
using System.Web.Http.Results;

namespace DianGuanJiaApp.ErpWeb.ApiControllers
{
    /// <summary>
    /// API接口的基类
    /// </summary>
    [DefaultApiAuthorizationFilter()]
    public class BaseApiController : ApiController
    {
        /// <summary>
        /// 返回结果
        /// </summary>
        /// <param name="message"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected AjaxResult<T> SuccessResult<T>(T data , string message = "成功！")
        {
            return new AjaxResult<T>()
            {
                Success = true,
                Data = data,
                Message = message
            };
        }

        /// <summary>
        /// 返回结果
        /// </summary>
        /// <param name="message"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected AjaxResult<T> FailedResult<T>(T data , string message)
        {
            return new AjaxResult<T>()
            {
                Success = false,
                Data = data,
                Message = message
            };
        }

        /// <summary>
        /// 返回结果
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        protected AjaxResult<T> FailedResult<T>(T data)
        {
            return new AjaxResult<T>()
            {
                Success = false,
                Data = data,
                Message = data is string ? data.ToString() : "失败!"
            };
        }

        protected string EntryVerification()
        {
            if (!ModelState.IsValid)
            {
                var firstError = ModelState.Where(ms => ms.Value.Errors.Count > 0).SelectMany(ms => ms.Value.Errors).FirstOrDefault();
                return firstError?.ErrorMessage;
            }

            return null;
        }        

        /// <summary>
        ///
        /// </summary>
        private HttpRequest _Request = null;

        /// <summary>
        ///
        /// </summary>
        protected new HttpRequest Request
        {
            get
            {
                if (_Request == null)
                {
                    _Request = HttpContext.Current.Request;
                }
                return _Request;
            }
            set
            {
                _Request = value;
            }
        }

        private string _pageToken = null;
        /// <summary>
        /// 当前页面请求Token
        /// </summary>
        public string PageToken
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_pageToken))
                {
                    _pageToken = Request["token"].ToString2();
                }
                return _pageToken;

            }
        }

        /// <summary>
        /// 直接返回Json
        /// </summary>
        /// <param name="message"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected JsonResult<AjaxResult> SuccessResult(string message, object data)
        {
            return Json(new AjaxResult()
            {
                Message = message,
                Success = true,
                Data = data
            });
        }

        /// <summary>
        /// 直接返回Json
        /// </summary>
        /// <param name="message"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected JsonResult<AjaxResult> FalidResult(string message, dynamic data = null)
        {
            return Json(new AjaxResult()
            {
                Message = message,
                Success = true,
                Data = data
            });
        }

        /// <summary>
        /// ip白名单校验-不需要了，直接用ToolsBaseController.IpWhiteCheck
        /// </summary>
        /// <returns></returns>
        public static bool IpWhiteCheck(string userHostAddress, string remoteAddr, string ipforHeader)
        {
            if (CustomerConfig.IsLocalDbDebug)
                return true;
            try
            {
                //ip白名单控制，读取ip白名单
                var ipWhiteList = new CommonSettingService().Get<List<IpWhiteModel>>("/AllSystem/GetAllowIpList/", -168);
                if (ipWhiteList == null || ipWhiteList.Count == 0)
                    return false;

                if (string.IsNullOrWhiteSpace(ipforHeader) == false && ipWhiteList.Exists(f => ipforHeader.Contains(f.IP)))
                    return true;
                if (string.IsNullOrWhiteSpace(userHostAddress) == false && ipWhiteList.Exists(f => userHostAddress.Contains(f.IP)))
                    return true;
                if (string.IsNullOrWhiteSpace(remoteAddr) == false && ipWhiteList.Exists(f => remoteAddr.Contains(f.IP)))
                    return true;

                var ipStr = $"{userHostAddress}，{remoteAddr},{ipforHeader}";
                Log.WriteError($"非白名单的IP试图进入系统，IP：{ipStr}", "TestSiteIpWhiteLog.txt");
                return false;
            }
            catch (Exception ex)
            {
                Log.WriteError($"测试站点白名单检测报错：{ex.Message}", "TestSiteIpWhiteLog.txt");
            }
            return false;
        }

        /// <summary>
        /// ip白名单校验
        /// </summary>
        /// <returns></returns>
        public string IpWhiteCheck()
        {
            if (!CustomerConfig.IsDebug)
                return "No Permission";

            //ip白名单控制，读取ip白名单
            if (!ToolsBaseController.IpWhiteCheck(Request?.UserHostAddress, Request?.ServerVariables["REMOTE_ADDR"], Request?.Headers["X-Forwarded-For"]))
                return "IP白名单限制";

            return null;
        }

        /// <summary>
        /// 校验越权 2025-2-17 代码审核优化
        /// </summary>
        /// <param name="shopId">前端传过来的店铺Id</param>
        /// <exception cref="LogicException"></exception>
        protected void CheckDataPermission(int shopId)
        {
            if (shopId > 0)
            {
                var userShops = SiteContext.Current.UserShops;
                if (userShops == null || userShops.FirstOrDefault(t => t.Id == shopId) == null)
                    throw new LogicException("指定店铺不属于当前的账号！");
            }
        }
    }
}