using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.Services.BaseProduct;
using DianGuanJiaApp.Services.Services.SupplierProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Other;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using BaseOfPtSkuRelationService = DianGuanJiaApp.Services.BaseProduct.BaseOfPtSkuRelationService;
using BaseProductSkuCommonService = DianGuanJiaApp.Services.BaseProduct.BaseProductSkuCommonService;
using DianGuanJiaApp.Data.Model.Tools;
using DianGuanJiaApp.Warehouse.Model.Request;
using DianGuanJiaApp.Warehouse.Model.Response;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using CSRedis;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.Services;

namespace DianGuanJiaApp.ErpWeb.ApiControllers
{
    /// <summary>
    /// 货品库接口
    /// </summary>
    [FxWhiteUserFilter]
    public class BaseProductController : BaseApiController
    {
        private ProductFxService _productFxService = new ProductFxService();
        private PathFlowService _pathFlowService = new PathFlowService();
        private ShopService _shopService = new ShopService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private WareHouseService _service = new WareHouseService();

        private BaseProductSkuService _baseProductSkuService;

        private SupplierUserService _supplierUserService = new SupplierUserService();
        private AsyncTaskService _asyncTaskService = new AsyncTaskService();
        private BaseProductAbnormalService _baseProductAbnormalService = null;
        private FinancialSettlementService _financialSettlementService = new FinancialSettlementService();
        private readonly SupplierProductService _supplierProductService = new SupplierProductService();
        private readonly CommonSettingService _settingService = new CommonSettingService();

        /// <summary>
        ///  功能模块：商品库、
        ///  功能点：快捷添加、
        ///  描述信息：店铺商品SPU列表、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("店铺商品SPU列表")]
        [HttpPost]
        public AjaxResult<PageResultApiModel<PlateProductSearchRes>> GetPlateProducts(PlateProductSearch model)
        {
            var isAllData = true; // 包含虚拟、下架失效
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _productFxService.GetProductSpuList(model, fxUserId, isAllData);
            return new AjaxResult<PageResultApiModel<PlateProductSearchRes>>()
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }

        /// <summary>
        ///  功能模块：货盘库、
        ///  功能点：快捷上架、
        ///  描述信息： 基础商品SPU列表、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("基础商品SPU列表")]
        [HttpPost]
        public AjaxResult<PageResultApiModel<BaseProductSearchSimpleRes>> GetBaseProducts(BaseProductReqModel model)
        {
            if (!string.IsNullOrEmpty(model.SpuCode) && model.SpuCode.Contains(','))
            {
                if (model.SpuCode.SplitToList(",").Count > 500)
                {
                    return new AjaxResult<PageResultApiModel<BaseProductSearchSimpleRes>>()
                    {
                        Success = true,
                        Data = null,
                        Message = "搜索编码不能超过500个，请重新输入搜索编码！"
                    };
                }
            }
            var service = new BaseProductEntityService();
            var tuple = service.GetBaseProducts(model);
            var result = new PageResultApiModel<BaseProductSearchSimpleRes>();
            result.PageIndex = model.PageIndex;
            result.PageSize = model.PageSize;
            result.Rows = tuple.Item2;
            result.Total = tuple.Item1;
            return new AjaxResult<PageResultApiModel<BaseProductSearchSimpleRes>>()
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }

        /// <summary>
        ///  功能模块：商品库、
        ///  功能点：货盘关联、
        ///  描述信息： 关联明细货盘、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("货盘关联明细")]
        [HttpPost]
        public AjaxResult<PageResultApiModel<BaseProductSkuBindSearchModelRes>> GetSupplierProductSkuRelations(BaseProductSkuBindSearchModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var service = new BaseOfSupplierSkuRelationService(fxUserId);
            var tuple = service.GetSupplierSkuRelations(model, fxUserId);
            var list = tuple.Item2;
            var count = tuple.Item1;
            var res = new List<BaseProductSkuBindSearchModelRes>();
            if (list != null && list.Count > 0)
            {
                foreach (var item in list)
                {
                    var data = new BaseProductSkuBindSearchModelRes();
                    data.Attributes = item.Attributes;
                    data.Subject = item.Subject;
                    data.ShortTitle = item.ShortTitle;
                    data.DistributePrice = item.DistributePrice;
                    data.ImageUrl = ImgHelper.ChangeImgUrl(item.ImageUrl);
                    data.Weight = item.Weight;
                    data.BaseProductSkuUid = item.BaseProductSkuUid.ToString();
                    data.BaseProductUid = item.BaseProductUid.ToString();
                    data.SupplierProductSkuUid = item.SupplierProductSkuUid.ToString();
                    data.SupplierProductUid = item.SupplierProductUid.ToString();
                    data.SkuCode = item.SkuCode;
                    data.PlatformTypeStr = "我的小站";
                    res.Add(data);
                }
            }
            var result = new PageResultApiModel<BaseProductSkuBindSearchModelRes>();
            result.PageIndex = model.PageIndex;
            result.PageSize = model.PageSize;
            result.Rows = res;
            result.Total = count;

            // 跨云查询其他云平台的关联数量
            model.OnlyQueryCount = true;
            model.FxUserId = SiteContext.GetCurrentFxUserId();
            var relationCountDict = new ConcurrentDictionary<string, int>();
            
            var cloudList = new List<string>()
            {
                CloudPlatformType.Alibaba.ToString(),
                CloudPlatformType.TouTiao.ToString(),
                CloudPlatformType.Jingdong.ToString(),
                CloudPlatformType.Pinduoduo.ToString()
            };

            var maxParall = 4;
            if (CustomerConfig.IsLocalDbDebug)
                maxParall = 1;
            Parallel.ForEach(cloudList, new ParallelOptions() { MaxDegreeOfParallelism = maxParall }, cloud =>
            {
                if (CustomerConfig.CloudPlatformType != cloud && CustomerConfig.IsLocalDbDebug)
                {
                    relationCountDict.TryAdd(cloud, 0);
                    return;
                }

                try
                {
                    var relCount = 0;
                    if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                    {

                        relCount = _productFxService.GetProductSkuBindDetailList(model)?.Item1 ?? 0;
                    }
                    else
                    {
                        var apiUrl = "/BaseProductApi/BaseProductSkuRelationDetailList";
                        var targetSiteUrl = CustomerConfig.GetTargetSiteUrl(cloud).TrimEnd("/") + apiUrl;

                        relCount = Common.PostFxSiteApi<BaseProductSkuBindSearchModel, Tuple<int, List<ProductSkuFx>>>(targetSiteUrl, fxUserId, model, "跨云查询基础商品规格关联", isEncrypt: true).Item1;
                    }

                    var overCount = 5;
                    while (!relationCountDict.TryAdd(cloud, relCount) && overCount > 0)
                    {
                        overCount--;
                    }
                    if (overCount <= 0)
                        Log.WriteError("跨云查询基础商品规格关联数，写入并发字典失败次数过多", ModuleFileName.BaseProduct);

                }
                catch (Exception ex)
                {
                    Log.WriteError($"{cloud} 跨云查询基础商品规格关联数发生异常：{ex},{ex.InnerException}", ModuleFileName.BaseProduct);
                }

            });

            result.Other = new Dictionary<string, dynamic>
            {
                {"RelationCountDict",relationCountDict}
            };
            return new AjaxResult<PageResultApiModel<BaseProductSkuBindSearchModelRes>>
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }

        /// <summary>
        ///  功能模块：商品库、
        ///  功能点：编辑价格、
        ///  描述信息： 关联明细编辑价格（店铺）、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("关联明细编辑采购价（店铺）")]
        [HttpPost]
        public AjaxResult EditBaseProductSkuRelations(EditBaseProductSkuRelation model)
        {
            var res = new AjaxResult();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //if (model.SettlementType <= 0)
            //{
            //    res.Success = false;
            //    res.Data = false;
            //    res.Message = "编辑类型不能为空！";
            //    return res;
            //}
            if (model.PriceModels == null || model.PriceModels.Count <= 0)
            {
                res.Success = false;
                res.Data = false;
                res.Message = "编辑价格数据不能为空，请填写价格数据！";
                return res;
            }

            var priceModels = new List<ProductSettlementPrice>();
            var pricelist = model.PriceModels;

            foreach (var price in pricelist)
            {
                var p = new ProductSettlementPrice();
                p.Id = price.Id;
                p.SettlementType = price.SettlementType;
                p.ProductCode = price.ProductCode;
                p.ProductSkuCode = price.ProductSkuCode;
                p.PlatformType = model.PlatformType;
                p.FxUserId = price.FxUserId;
                p.Price = price.Price;
                priceModels.Add(p);
            }
            if (priceModels.Count > 0)
            {
                var groups = priceModels.GroupBy(p => p.SettlementType).ToList();
                foreach (var group in groups)
                {
                    var list = group.ToList();
                    var type = group.Key;
                    _financialSettlementService.SetCommonSettlementPrice(list, fxUserId, type);
                }
            }
            res.Success = true;
            res.Data = true;
            res.Message = "成功！";
            return res;
        }

        /// <summary>
        ///  功能模块：商品库、
        ///  功能点：编辑价格、
        ///  描述信息： 关联明细编辑分销价（货盘）、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("关联明细编辑分销价（货盘）")]
        [HttpPost]
        public AjaxResult EditSupplierProductSkuRelations(EditSupplierProductSkuRelation model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var res = new AjaxResult();
            if (model.Relations == null && model.Relations.Count == 0)
            {
                res.Success = false;
                res.Data = false;
                res.Message = "编辑数据不能为空！";
                return res;
            }
            // 编辑货盘分销价格
            var data = _supplierProductService.UpdateDistributePrice(model, fxUserId);
            res.Success = data;
            res.Data = data;
            res.Message = data == true ? "编辑成功！" : "编辑失败！";
            return res;
        }

        /// <summary>
        ///  功能模块：商品库、
        ///  功能点：同步更新资料、
        ///  描述信息： 商品库资料同步更新、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>

        [LogForOperatorFilter("商品库资料同步更新")]
        [HttpPost]
        public AjaxResult SyncPtProductInfo(SyncPtProductInfoModel model)
        {
            var res = new AjaxResult();
            res.Success = true;
            res.Data = true;
            res.Message = "资料同步成功！";
            if (model.PlatformType == "HuoPan" && model.SupplierProductUid == null)
            {
                res.Success = false;
                res.Data = false;
                res.Message = "货盘商品Id不能为空！";
                return res;
            }
            var baseProductSkuService = new BaseProductSkuService();
            var baseProductService = new BaseProductEntityService();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var req = new BaseProductDetailReqModel() { BaseProductUid = model.BaseProductUid, Type = false };
            var baseProductInfo = baseProductSkuService.GetBaseProductDetail(req, fxUserId);
            var baseProductSkus = baseProductInfo.Skus;

            // 货盘更新
            if (model.PlatformType == "HuoPan")
            {
                try
                {
                    baseProductService.UpdateSyncSProductInfos(baseProductInfo);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"更新货盘资料失败！失败原因：{ex.ToJson()}");
                    res.Message = $"更新货盘资料失败！失败原因：{ex.ToJson()}";
                    res.Success = false;
                    res.Data = false;
                }
            }
            // 平台资料更新
            else
            {
                try
                {
                    baseProductService.UpdateSyncPtProductInfos(baseProductInfo, model.PlatformType);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"更新平台资料失败！失败原因：{ex.ToJson()}");
                    res.Message = $"更新平台资料失败！失败原因：{ex.ToJson()}";
                    res.Success = false;
                    res.Data = false;
                }
            }
            return res;
        }

        /// <summary>
        ///  功能模块：商品库、
        ///  功能点：绑定供货、
        ///  描述信息： 商品库SUP供货方式换绑检测、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("商品库SUP供货方式换绑检测")]
        [HttpPost]
        public AjaxResult<BindCheckBaseProductRes> CheckBaseProductBindSupplier(BindCheckBaseProductModel model)
        {
            if (model.BaseProductUids == null || model.BaseProductUids.Count == 0)
            {
                return new AjaxResult<BindCheckBaseProductRes>
                {
                    Success = false,
                    Data = null,
                    Message = "基础商品Id不能为空！"
                };
            }
            var baseProductConfigs =
                new BaseProductSkuSupplierConfigService().GetAllListByProductUids(model.BaseProductUids);

            var baseProductGroups = baseProductConfigs.GroupBy(a => a.ProductUid).ToList();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var res = new BindCheckBaseProductRes();
            res.IsDifferent = false;

            foreach (var group in baseProductGroups)
            {
                var item = group.FirstOrDefault(a => a.RefType == "Product");
                var items = group.ToList();
                var productSupplierFxUserId = item?.SupplierFxUserId;
                // SPU 已设置
                if (productSupplierFxUserId.HasValue && productSupplierFxUserId.Value != 0)
                {
                    var tempOne = items
                        .Where(a => a.RefType == "Sku")
                        .Any(a => a.SupplierFxUserId != 0);
                    var tempTwo = items
                        .Where(a => a.RefType == "Sku")
                        .Where(a => a.SupplierFxUserId != 0)
                        .Any(a => a.SupplierFxUserId != productSupplierFxUserId);
                    if (tempOne)
                    {
                        // SKU 有设置
                        // SKU 有设置 是存在和SPU不同 是存在差异
                        // SKU 未设置 否存在和SPU不同 否存在差异
                        if (tempTwo)
                        {
                            res.IsDifferent = true;
                            break;
                        }
                    }
                    else
                    {
                        // SKU 未设置 是存在差异
                        res.IsDifferent = true;
                        break;
                    }
                }
                // SPU 未设置
                else
                {
                    var tempOne = items.Any(a => a.SupplierFxUserId != 0);
                    // SKU 有设置 是存在差异
                    // SKU 未设置 否存在差异
                    if (tempOne)
                    {
                        res.IsDifferent = true;
                        break;
                    }
                }
            }
            return new AjaxResult<BindCheckBaseProductRes>
            {
                Success = true,
                Data = res,
                Message = "成功！"
            };
        }

        /// <summary>
        ///  功能模块：商品库、
        ///  功能点：商品库设置、
        ///  描述信息： 设置商品库设置、
        ///  接口开发人员：肖茂翔
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("设置商品库设置")]
        [HttpPost]
        public AjaxResult<string> SetBaseProductSetting(BaseProductSettingsModel model)
        {
            var shopId = SiteContext.Current.CurrentShopId;
            const string settingKey = "/ErpWeb/FenDan/BaseProductSetting";

            // 获取设置
            var result = _commonSettingService.Set(settingKey, model.ToJson(), shopId);
            if (result > 0)
            {
                try
                {
                    // 同步到各平台，拼多多、头条，京东属于阿里云，不需要同步
                    _commonSettingService.SetToTouTiao(settingKey, model.ToJson(), shopId);
                    _commonSettingService.SetToPdd(settingKey, model.ToJson(), shopId);
                }
                catch (Exception e)
                {
                    Log.WriteError($"跨云平台更新商品库设置失败，失败原因：{e}");
                    
                    return FailedResult("出现意料之外的错误，请联系管理员");
                }
            }
            
            return result > 0 ? SuccessResult("设置成功") : FailedResult("设置失败");
        }

        /// <summary>
        /// 功能模块：商品库、
        /// 功能点：商品库设置、
        /// 描述信息：获取商品库设置、
        /// 接口开发人员：肖茂翔
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("获取商品库设置")]
        [HttpGet]
        public AjaxResult<BaseProductSettingsModel> GetBaseProductSetting()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            const string settingKey = "/ErpWeb/FenDan/BaseProductSetting";

            // 获取设置
            var setting = _commonSettingService.Get<BaseProductSettingsModel>(settingKey, shopId);
            return SuccessResult(setting ?? new BaseProductSettingsModel());
        }

        /// <summary>
        /// 功能模块：商品库、
        /// 功能点：获取基础商品Sku列表、
        /// 描述信息：在商品列表或订单列表页面获取基础商品Sku列表，用于关联关系、
        /// 接口开发人员：肖茂翔
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("获取基础商品Sku列表")]
        [HttpPost]
        public AjaxResult<PagedResultModel<BaseProductSku>> LoadBaseProductSkuList(BaseProductSearchModel model)
        {
            var result = new PagedResultModel<BaseProductSku>();

            if (!string.IsNullOrEmpty(model.SkuCode) && model.SkuCode.Contains(','))
            {
                if (model.SkuCode.SplitToList(",").Count > 500 || model.SpuCode.SplitToList(",").Count > 500)
                {
                    return FailedResult(result,"查询的商品不要超过500");
                }
            }
            if (!string.IsNullOrEmpty(model.SkuUid) && model.SkuUid.Contains(','))
            {
                if (model.SkuUid.SplitToList(",").Count > 500)
                {
                    return FailedResult(result,"查询的商品不要超过500");
                }
            }
            
            // 如果不是精选平台，则需要转发请求
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
            {
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                
                try
                {
                    const string apiUrl = "/BaseProductApi/LoadBaseProductSkuList";
                    var targetSiteUrl = CommUtls.GetTargetSiteUrl(apiUrl);
                    Log.Debug($"跨云查询基础商品Sku列表，targetSiteUrl={targetSiteUrl}，lastModel={model.ToJson()}");

                    result = Common.PostFxSiteApi<BaseProductSearchModel, PagedResultModel<BaseProductSku>>(targetSiteUrl,
                            fxUserId, model, "跨云查询基础商品Sku列表", isEncrypt: true);
                    
                    return SuccessResult(result);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"跨云查询基础商品Sku列表：{ex.Message}");
                    return FailedResult(result, "查询失败");
                } 
            }

            var (listCount, list) = new BaseProductSkuCommonService().LoadBaseProductSkuList(model); 
            result = new PagedResultModel<BaseProductSku>
            {
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                Rows = list,
                Total = listCount
            };
            return SuccessResult(result);
        }

        /// <summary>
        /// 获取基础商品Sku关联情况
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("获取基础商品Sku关联情况")]
        [HttpPost]
        public AjaxResult<BaseProductSku> GetSkuRelation(BaseProductSkuRelationModel model)
        {
            var result = new BaseProductSku();
            if (model == null) return FailedResult(result, "参数不能为空");
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            // 查找对应的关联信息
            var baseOfPtSkuRelations =
                new BaseOfPtSkuRelationService(false).GetListBySkuModel(new List<long> { model.SkuUid },
                    new List<string> { model.ProductSkuCode }, fxUserId);
            if (baseOfPtSkuRelations == null || baseOfPtSkuRelations.Count == 0) return FailedResult(result, "未找到关联信息");
            
            var baseOfPtSkuRelation = baseOfPtSkuRelations.FirstOrDefault(x => x.BaseProductSkuUid == model.SkuUid);
            if (baseOfPtSkuRelation == null) return FailedResult(result, "未找到关联信息");
            BaseProductSku baseProductSku;
            model.FxUserId = fxUserId;
            
            // 判断是否是精选平台
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
            {
                try
                {
                    const string apiUrl = "/BaseProductApi/GetSkuRelation";
                    var targetSiteUrl = CommUtls.GetTargetSiteUrl(apiUrl);
                    Log.Debug($"跨云查询基础商品Sku信息，targetSiteUrl={targetSiteUrl}，lastModel={model.ToJson()}");

                    baseProductSku = Common.PostFxSiteApi<BaseProductSkuRelationModel, BaseProductSku>(targetSiteUrl,
                        fxUserId, model, "跨云查询基础商品Sku信息", isEncrypt: true);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"跨云查询基础商品Sku信息：{ex.Message}");
                    return FailedResult(result, "查询失败");
                } 
            }
            else
            {
                // 查找对应的基础商品Sku信息
                baseProductSku =  new BaseProductSkuService().GetByUid(model.SkuUid); 
            }
          
            if (baseProductSku == null || baseProductSku.Status != 1) return FailedResult(result, "未找到关联信息");
            baseProductSku.ImageUrl = ImgHelper.ChangeImgUrl(baseProductSku.ImageUrl);
            baseProductSku.ProSubject = baseOfPtSkuRelation.BaseProductSubject;
            baseProductSku.ProShortTitle = baseOfPtSkuRelation.BaseProductShortTitle;
            return SuccessResult(baseProductSku);
        }
        #region 基础数据种子用户优化 20240830
        /// <summary>
        /// 仅获取基础商品
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<PagedResultModel<BaseProductEntity>> GetOnlyBaseProducts(BaseProductSearchModel model)
        {
            if (!string.IsNullOrEmpty(model.SpuCode) && model.SpuCode.Contains(','))
            {
                if (model.SpuCode.SplitToList(",").Count > 500)
                {
                    return new AjaxResult<PagedResultModel<BaseProductEntity>>() { Success = false, Message = "查询的Spu编码不要超过500" };
                }
            }
            model.FxUserId = SiteContext.Current.CurrentFxUserId;
            // 查询数据
            _baseProductSkuService = new BaseProductSkuService();
            var tuple = _baseProductSkuService.GetBaseProductListNoSkuQuery(model);
            var list = tuple.Item2;
            var listCount = tuple.Item1;
            // 数据组装
            if (list.Count > 0)
            {
                // 厂家数据
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var suppliers = _supplierUserService.GetSupplierList(fxUserId,needEncryptAccount:true);
                var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");
                var skus = list.SelectMany(a => a.Skus).ToList();
                var skuCodes = skus.Select(a => a.SkuCode).Distinct().ToList();
                var skuUid = skus.Select(a => a.Uid).Distinct().ToList();

                var mainImageIds = list.Select(a => a.Uid).Where(a => a != 0).Distinct().ToList();
                var mainImages = new BaseProductImageRepository().GetListByProductUid(mainImageIds);
                // 商品资料信息
                var baseProductUid = list.Select(a => a.Uid).Distinct().ToList();
                var supplierRelations = new BaseOfSupplierSkuRelationService(fxUserId).GetListByProductUid(baseProductUid, fxUserId);
                var supplierInfos = supplierRelations.GroupBy(a => a.BaseProductUid).Select(g => g.FirstOrDefault()).ToList();
                // 平台资料
                var ptInfos = new PtProductInfoService().GetByProductUids(baseProductUid, fxUserId);
                // 绑定信息
                var supplierConfigs = new BaseProductSkuSupplierConfigService().GetAllListByProductUids(baseProductUid);
                // 规格库存数据
                var stocks = _service.GetStockBySkuCode(skuCodes);
                // 规格关联数据
                var baseProductSkuRelations = new BaseOfPtSkuRelationService().GetListBySkuUids(skuUid, fxUserId);
                var baseProductSkuRelationsValue = baseProductSkuRelations.SelectMany(a => a.Value).ToList();
                // 规格货盘关联数据
                var supplierProductSkuRelationService = new BaseOfSupplierSkuRelationService(fxUserId);
                var supplierProudctSkuRelations =
                    supplierProductSkuRelationService.GetListByProductSkuUid(skuUid, fxUserId);
                foreach (var sku in skus)
                {
                    var skuConfig = supplierConfigs.Where(c => c.RefUid == sku.Uid && c.RefType == "Sku").FirstOrDefault();
                    var proConfig = supplierConfigs.Where(c => c.RefUid == sku.BaseProductUid && c.RefType == "Product").FirstOrDefault();

                    // 绑定信息采用方式
                    sku.SkuSupplierUserId = 0;
                    if (proConfig != null)
                        sku.SkuSupplierUserId = proConfig?.SupplierFxUserId ?? 0;
                    if (skuConfig != null)
                        sku.SkuSupplierUserId = skuConfig?.SupplierFxUserId ?? 0;
                    var supplierRes = supplierList.Where(t => t.Key == sku.SkuSupplierUserId).FirstOrDefault();
                    sku.SupplierName = supplierRes.Value;

                    var stock = stocks.Where(s => s.SkuCargoNumber == sku.SkuCode).FirstOrDefault();
                    var relations = baseProductSkuRelationsValue.Where(a => a.BaseProductSkuUid == sku.Uid).ToList();
                    var relationSuppliers = supplierProudctSkuRelations.Where(a => a.BaseProductSkuUid == sku.Uid).ToList();
                    sku.ImageUrl = ImgHelper.ChangeImgUrl(sku.ImageUrl);
                    sku.StockCount = stock?.StockCount;
                    sku.RelationCount = relations == null ? 0 : relations.Count;
                    if (relationSuppliers.Count > 0)
                        sku.RelationCount = sku.RelationCount + relationSuppliers.Count;
                }
                // 商品库存
                foreach (var p in list)
                {
                    // 资料标志
                    var supplierInfo = supplierInfos.Where(a => a.BaseProductUid == p.Uid).Distinct().ToList();
                    var ptInfo = ptInfos.Where(a => a.BaseProductUid == p.Uid).Distinct().ToList();
                    var infos = _baseProductSkuService.MakeInfoTage(supplierInfo, ptInfo);
                    p.PlatformInfoTags = infos;

                    // 供货方式
                    var pConfig = supplierConfigs.Where(c => c.RefUid == p.Uid && c.RefType == "Product").FirstOrDefault();
                    p.ProductSupplierUserId = pConfig?.SupplierFxUserId ?? 0;
                    var supplierRes = supplierList.Where(t => t.Key == p.ProductSupplierUserId).FirstOrDefault();
                    p.SupplierName = supplierRes.Value;
                    p.IsMultipleSupplierUser = p.Skus.Where(a => a.SkuSupplierUserId.HasValue).Count() > 0 ? true : false;

                    // 规格数量
                    p.SkuCount = p.Skus.Count();

                    // 供货方式
                    // p.SupplyMethod = _baseProductSkuService.MakeSupplierTag(p, fxUserId);
                    // 以商品维度，看supplierConfigs给商品设置了啥
                    // 未设置
                    if (p.ProductSupplierUserId == 0)
                    {
                        p.SupplyMethod = 0;
                    }
                    // 自营
                    else if(p.ProductSupplierUserId == fxUserId)
                    {
                        p.SupplyMethod = 1;
                    }
                    // 厂家
                    else
                    {
                        p.SupplyMethod = 2;
                    }
                    p.IsCombination = p.Skus.Any(x => x.IsCombineSku).ToInt();

                    var m = mainImages.FirstOrDefault(a => a.ImageObjectId == p.MainImageObjectId && p.MainImageObjectId != 0);
                    if (m != null)
                    {
                        var suffix = m.Suffix.IsNullOrEmpty() ? string.Empty : $".{m.Suffix}";
                        var url = $"{m?.Domain}/{m?.Url}/{m?.Name}{suffix}";
                        p.MainImageUrl = ImgHelper.ChangeImgUrl(url);
                    }
                }
            }
            
            var res = new PagedResultModel<BaseProductEntity>()
            {
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                Total = listCount,
                Rows = list
            };
            return new AjaxResult<PagedResultModel<BaseProductEntity>> { Data = res, Success = true };
        }
        /// <summary>
        /// 仅获取基础商品Sku
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<PagedResultModel<BaseProductSku>> GetOnlySkus(BaseProductSearchModel model)
        {
            if (!string.IsNullOrEmpty(model.SkuCode) && model.SkuCode.Contains(','))
            {
                if (model.SkuCode.SplitToList(",").Count > 500)
                {
                    return new AjaxResult<PagedResultModel<BaseProductSku>>() { Success = false, Message = "查询的SKU编码不要超过500" };
                }
            }

            if (!string.IsNullOrEmpty(model.SpuCode) && model.SpuCode.Contains(','))
            {
                if (model.SpuCode.SplitToList(",").Count > 500)
                {
                    return new AjaxResult<PagedResultModel<BaseProductSku>>() { Success = false, Message = "查询的商品编码不要超过500" };
                }
            }

            if (!string.IsNullOrEmpty(model.SkuUid) && model.SkuUid.Contains(','))
            {
                if (model.SkuUid.SplitToList(",").Count > 500)
                {
                    return new AjaxResult<PagedResultModel<BaseProductSku>>() { Success = false, Message = "查询的SkuId不要超过500" };
                }
            }
            model.FxUserId = SiteContext.Current.CurrentFxUserId;
            // 查询数据
            _baseProductSkuService = new BaseProductSkuService();
            var tuple = _baseProductSkuService.GetAllBaseProductSkuList(model);
            var skus = tuple.Item2;
            var listCount = tuple.Item1;
            var resModel = new List<BaseProductSku>();
            // 数据组装
            if (skus?.Count > 0)
            {
                // 厂家数据
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var suppliers = _supplierUserService.GetSupplierList(fxUserId,needEncryptAccount:true);
                var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");
                
                var skuCodes = skus.Select(a => a.SkuCode).Distinct().ToList();
                var skuUid = skus.Select(a => a.Uid).Distinct().ToList();
                // 商品资料信息
                var baseProductUid = skus.Select(a => a.BaseProductUid).Distinct().ToList();
                //// 平台资料
                //var ptInfos = new PtProductInfoService().GetByProductUids(baseProductUid, fxUserId);
                // 绑定信息
                var supplierConfigs = new BaseProductSkuSupplierConfigService().GetAllListByProductUids(baseProductUid);
                // 规格关联数据
                var baseProductSkuRelations = new BaseOfPtSkuRelationService().GetListBySkuUids(skuUid, fxUserId);
                var baseProductSkuRelationsValue = baseProductSkuRelations.SelectMany(a => a.Value).ToList();
                // 规格货盘关联数据
                var supplierProductSkuRelationService = new BaseOfSupplierSkuRelationService(fxUserId);
                var supplierProudctSkuRelations =
                    supplierProductSkuRelationService.GetListByProductSkuUid(skuUid, fxUserId);

                var spuCombines = skus.GroupBy(x => x.BaseProductUid).Select(x => new { x.Key, Datas = x.Select(y => y) }).ToDictionary(x => x.Key, x => x.Datas.Any(y => y.IsCombineSku));
                // 如果有组合货品
                var combineSkuResponse = new WarehouseProductSkuContainChildResponse();
                if (spuCombines.Any(x => x.Value))
                {
                    var combineSkuCodes = skus.Where(x => x.IsCombineSku).Select(x => x.SkuCode).ToList();
                    combineSkuResponse = _service.GetSkuContainChildListByCargoNumber(combineSkuCodes);
                }
                foreach (var sku in skus)
                {
                    var skuConfig = supplierConfigs.Where(c => c.RefUid == sku.Uid && c.RefType == "Sku").FirstOrDefault();
                    var proConfig = supplierConfigs.Where(c => c.RefUid == sku.BaseProductUid && c.RefType == "Product").FirstOrDefault();

                    // 绑定信息采用方式
                    sku.SkuSupplierUserId = 0;
                    if (proConfig != null)
                        sku.SkuSupplierUserId = proConfig.SupplierFxUserId;
                    if (skuConfig != null)
                        sku.SkuSupplierUserId = skuConfig.SupplierFxUserId;

                    // 规格供货方式,按照产品的意思，规格绑定了什么就显示什么
                    if (sku.SupplierFxUserId > 0)
                    {
                        var supplierRes = supplierList.Where(t => t.Key == sku.SkuSupplierUserId).FirstOrDefault();
                        sku.SupplierName = supplierRes.Value;
                    }
                    else
                    {
                        sku.SkuSupplierUserId = sku.SupplierFxUserId == 0 ? 0 : fxUserId;
                    }
                    var relations = baseProductSkuRelationsValue.Where(a => a.BaseProductSkuUid == sku.Uid).ToList();
                    var relationSuppliers = supplierProudctSkuRelations.Where(a => a.BaseProductSkuUid == sku.Uid).ToList();
                    sku.ImageUrl = ImgHelper.ChangeImgUrl(sku.ImageUrl);
                    sku.RelationCount = relations == null ? 0 : relations.Count;
                    if (relationSuppliers.Count > 0)
                        sku.RelationCount = sku.RelationCount + relationSuppliers.Count;
                    var skuValueTemp = BaseProductUtils.FromJson(sku.Attributes);
                    var skuValueStr = $"{skuValueTemp.AttributeValue1};{skuValueTemp.AttributeValue2};{skuValueTemp.AttributeValue3}".Trim(';');
                    sku.AttributeValue = skuValueStr;
                    sku.IsCombineSpu = spuCombines.ContainsKey(sku.BaseProductUid) && spuCombines[sku.BaseProductUid];
                    if (sku.IsCombineSpu)
                    {
                        sku.ChildSku =
                            combineSkuResponse?.Skus?.Where(x => x.SkuCargoNumber == sku.SkuCode).FirstOrDefault()
                                ?.ChildSkuList.ToJson()?.ToObject<List<BaseProductSkuChildModel>>() ??
                            new List<BaseProductSkuChildModel>();
                        // 处理图片
                        sku.ChildSku.ForEach(c => c.ImageUrl = ImgHelper.ChangeImgUrl(c.ImageUrl));
                    }
                }
                
                // _baseProductSkuService.SortBaseProductSku(p.Skus);
            }
            var res = new PagedResultModel<BaseProductSku>()
            {
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                Rows = skus,
                Total = listCount
            };

            return new AjaxResult<PagedResultModel<BaseProductSku>> { Data = res, Success = true };
        }

        #region 关联日志
        /// <summary>
        /// 获取基础资料种子关联日志
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<PagedResultModel<BpRelRecordModel>> GetBaseProductRelLogs(BpRelRecordSearchModel model)
        {
            var resModel = new BaseOfPtSkuRelationService(false).GetBpRelRecords(model);
            var res = new PagedResultModel<BpRelRecordModel>()
            {
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                Rows = resModel.Item2,
                Total = resModel.Item1
            };
            return new AjaxResult<PagedResultModel<BpRelRecordModel>> { Data = res, Success = true };
        }
        #endregion


        /// <summary>
        /// 功能：编辑基础商品页面--删除基础商品规格；
        /// 版本：2024-10-14 邱庆基 创建；
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<ReturnedModel> DelSku(long skuuid)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var resModel = new BaseProductSkuService().Delete(skuuid, curFxUserId);
            return new AjaxResult<ReturnedModel> { Message = resModel.Message, Success = resModel.Success };
        }

        #endregion
        
        /// <summary>
        /// 功能：创建组合商品--转换为创建页模型；
        /// 版本：2024-11-13 肖茂翔 创建；
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<BaseProductEntity> GetCreateModelByCombine(ProductSkuCombinationRequest model)
        {
            var result = new BaseProductEntity();
            if (model?.ChildSkuCodes == null || model.ChildSkuCodes.Count == 0) return FailedResult(result, "参数不能为空");
            if (model.ChildSkuCodes.Count > 50) return FailedResult(result, "组合商品子货品不能超过50个");
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            try
            {
                result = new BaseProductSkuService().GetCreateModelByCombine(model, curFxUserId);
            }
            catch (LogicException lo)
            {
                return FailedResult(result, lo.Message);
            }
            catch (Exception e)
            {
                Log.WriteError($"构建组合商品信息失败，失败原因：{e}");
                return FailedResult(result, "构建组合商品信息失败");
            }
            
            return SuccessResult(result);
        }
        
        /// <summary>
        /// 功能：修改组合品子货品；
        /// 版本：2024-12-15 肖茂翔 创建；
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<bool> UpdateCombineProduct(ProductSkuCombinationRequest model)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var result = false;
            try
            {
                result = new BaseProductSkuService().UpdateCombineProduct(model, curFxUserId);
            }
            catch (LogicException lo)
            {
                return FailedResult(result, lo.Message);
            }
            catch (Exception e)
            {
                Log.WriteError($"修改组合商品失败，失败原因：{e}");
                return FailedResult(result, "修改组合商品失败");
            }
            
            return SuccessResult(true);
        }

        /// <summary>
        ///  功能模块：基础商品、
        ///  功能点：批量解绑、
        ///  描述信息：各云店铺商品关联基础商品批量解绑
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("批量解绑关联")]
        [HttpPost]
        public AjaxResult<BatchBaseProductSkuUnbindRes> BatchUnbindBaseProductSkuRelation(BatchBaseProductSkuUnbindModel model)
        {
            var res = new AjaxResult<BatchBaseProductSkuUnbindRes>();
            var dbName = Request["dbname"];
            if (dbName.IsNotNullOrEmpty())
            {
                try
                {
                    dbName = DES.DecryptDES(dbName, CustomerConfig.LoginCookieEncryptKey);
                }
                catch (Exception ex)
                {
                    res.Success = false;
                    res.Data = null;
                    res.Message = "平台dbName解析失败！";
                    return res;
                }
            }
            else
            {
                dbName = new ProductFxRepository().DbConnection.Database;
            }

            // 规格删除限制50
            if (model.Skus.Count() > 50)
            {
                res.Success = false;
                res.Data = null;
                res.Message = "单次批量规格解绑上限为50件！";
                return res;
            }

            // 解绑基础商品Id不能为空
            if (model.BaseProductUid == null)
            {
                res.Success = false;
                res.Data = null;
                res.Message = "解绑基础商品不能为空，请联系我们！";
                return res;
            }
            // 解绑基础规格Id不能为空
            if (model.BaseProductSkuUid == null)
            {
                res.Success = false;
                res.Data = null;
                res.Message = "解绑基础规格不能为空，请联系我们！";
                return res;
            }
            // 规格解绑未选中
            if (!model.Skus.Any())
            {
                res.Success = false;
                res.Data = null;
                res.Message = "请选择要解绑的规格！";
                return res;
            }
            // 数据问题
            if (model.Skus.Any(p=>p.ProductPtCode.IsNullOrEmpty()))
            {
                res.Success = false;
                res.Data = null;
                res.Message = "所选数据存在商品编码为空，请联系我们！";
                return res;
            }

            // 数据问题
            if (model.Skus.Any(p => p.ProductSkuPtCode.IsNullOrEmpty()))
            {
                res.Success = false;
                res.Data = null;
                res.Message = "所选数据存在商品规格编码为空，请联系我们！";
                return res;
            }

            // 数据问题
            if (model.Skus.Any(p => p.ProductPtId.IsNullOrEmpty()))
            {
                res.Success = false;
                res.Data = null;
                res.Message = "所选数据存在商品ID为空，请联系我们！";
                return res;
            }

            // 数据问题
            if (model.Skus.Any(p => p.ProductSkuPtId.IsNullOrEmpty()))
            {
                res.Success = false;
                res.Data = null;
                res.Message = "所选数据存在商品规格ID为空，请联系我们！";
                return res;
            }

            var service = new BaseProductSkuCommonService();
            var data = service.BatchBaseProductSkuRelationUnbind(new List<BatchBaseProductSkuUnbindModel> { model }, true, dbName);
            res.Success = data.Success;
            res.Data = data.Data;
            res.Message = data.Message;
            return res;
        }

        /// <summary>
        ///  功能模块：基础商品、
        ///  功能点：删除规格检测、
        ///  描述信息：删除规格检测，涉及组合货品删除数据、组合货品删除规格数据
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("删除规格检测")]
        [HttpPost]
        public AjaxResult<DeleteBaseProductCheckRes> BathcDeleteBaseProductCheck(DeleteBaseProductCheckModel model)
        {
            var data = new DeleteBaseProductCheckRes();
            var res = new AjaxResult<DeleteBaseProductCheckRes>();
           
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var baseProductSkus = new List<BaseProductSku>();
            _baseProductSkuService = new BaseProductSkuService();

            if (model.IsBaseProduct)
            {
                if (!model.BaseProductUids.Any())
                {
                    res.Success = false;
                    res.Data = null;
                    res.Message = "请选择删除商品！";
                    return res;
                }
               
                if (model.BaseProductUids.Count() > 10)
                {
                    res.Success = false;
                    res.Data = null;
                    res.Message = "单次批量删除商品上限为10件！";
                    return res;
                }
                baseProductSkus = _baseProductSkuService.GetSkuByProductUids(model.BaseProductUids, fxUserId);
            }
            else
            {
                // 规格删除未选中
                if (!model.BaseProductSkuUids.Any())
                {
                    res.Success = false;
                    res.Data = null;
                    res.Message = "请选择删除规格！";
                    return res;
                }

                // 规格删除限制50
                if (model.BaseProductSkuUids.Count() > 50)
                {
                    res.Success = false;
                    res.Data = null;
                    res.Message = "单次批量删除规格上限为50件！";
                    return res;
                }
                baseProductSkus = _baseProductSkuService.GetListBySkuUids(model.BaseProductSkuUids, fxUserId);
            }
          
            if (!baseProductSkus.Any())
            {
                res.Success = false;
                res.Data = null;
                res.Message = "未找到已选择的商品规格！";
                return res;
            }

            var skuCodes = baseProductSkus
                .Select(p => p.SkuCode)
                .Distinct()
                .ToList();
            
            var resulst = _service.DeleteSkuCheck(skuCodes,true);
         

            if (resulst.IsSucc == false)
            {
                res.Success = false;
                res.Data = null;
                res.Message = "检测组合规格失败！";
                Log.WriteError($"库存检测组合规格失败,失败原因：{resulst.Message}");
                return res;
            }
            else
            {
                var deleteProducts = resulst.Data.DeleteCombineProduts.Where(p => p.IsCombineSku).ToList();
                var deleteProductSkus = resulst.Data.DeleteCombineProdutSkus.Where(p => p.IsCombineSku).ToList();
                //var deleteProducts = resulst.Data.DeleteCombineProduts;
                //var deleteProductSkus = resulst.Data.DeleteCombineProdutSkus;
                foreach (var item in deleteProducts)
                {
                    item.WareHouseProductImageUrl = ImgHelper.ChangeImgUrl(item.WareHouseProductImageUrl);
                }
                foreach (var item in deleteProductSkus)
                {
                    item.ImageUrl = ImgHelper.ChangeImgUrl(item.ImageUrl);
                }

                resulst.Data.DeleteCombineProduts = deleteProducts;
                resulst.Data.DeleteCombineProdutSkus = deleteProductSkus;
                resulst.Data.DeleteCombineProdutCount = deleteProductSkus.GroupBy(p => p.WareHouseProductCargoNumber).Count();
                res.Success = true;
                res.Data = resulst.Data;
                res.Message = "检测成功！";
                return res;

                //var allProductCodes = deleteProducts
                //    .Select(P => P.WareHouseProductCargoNumber)
                //    .Concat(deleteProductSkus
                //    .Select(p => p.WareHouseProductCargoNumber)
                //    .Distinct()
                //    .ToList()).Distinct().ToList();

                //var baseProduct = _baseProductService.GetBaseProductByCodes(allProductCodes, fxUserId);
            }
        }

        /// <summary>
        ///  功能模块：基础商品、
        ///  功能点：删除规格、
        ///  描述信息：规格批量删除
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("删除规格")]
        [HttpPost]
        public AjaxResult<BatchDeleteBaseProductRes> BatchDeleteBaseProductSkus(DeleteBaseProductCheckModel model)
        {
            var res = new AjaxResult<BatchDeleteBaseProductRes>();
            var data = new BatchDeleteBaseProductRes();

            // 规格删除未选中
            if (!model.BaseProductSkuUids.Any())
            {
                res.Success = false;
                res.Data = null;
                res.Message = "请选择删除规格！";
                return res;
            }

            // 规格删除限制50
            if (model.BaseProductSkuUids.Count() > 50)
            {
                res.Success = false;
                res.Data = null;
                res.Message = "单次批量删除规格上限为50件！";
                return res;
            }

            model.IsBaseProduct = false;
            _baseProductSkuService = new BaseProductSkuService();
            var result = _baseProductSkuService.BatchDeleteSku(model);
            res.Success = result.Success;
            res.Message = result.Message;
            res.Data = result.Data;
            return res;
        }


        /// <summary>
        ///  功能模块：基础商品、
        ///  功能点：商品删除、
        ///  描述信息：商品批量删除
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("删除商品")]
        [HttpPost]
        public AjaxResult<BatchDeleteBaseProductRes> BatchDeleteBaseProducts(DeleteBaseProductCheckModel model)
        {
            var res = new AjaxResult<BatchDeleteBaseProductRes>();
            var data = new BatchDeleteBaseProductRes();
            
            if (!model.BaseProductUids.Any())
            {
                res.Success = false;
                res.Data = null;
                res.Message = "请选择删除商品！";
                return res;
            }
         
            if (model.BaseProductUids.Count() > 10)
            {
                res.Success = false;
                res.Data = null;
                res.Message = "单次批量删除商品上限为10件！";
                return res;
            }
            model.IsBaseProduct = true;

            _baseProductSkuService = new BaseProductSkuService();
            var result = _baseProductSkuService.BatchDeleteSku(model);
            res.Success = result.Success;
            res.Message = result.Message;
            res.Data = result.Data;

            return res;
        }

        /// <summary>
        ///  功能模块：基础商品、
        ///  功能点：商品删除配置、
        ///  描述信息：商品批量删除配置设置
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public AjaxResult<bool> SetDeleteSetting(DeleteBaseProductSettingModel model)
        {
            var commonSettingService = new CommonSettingService();
            var res = new AjaxResult<bool>();
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var shopId = SiteContext.Current.CurrentShopId;
            var keys = new List<SettingModel> {
                new SettingModel {Key=$"/FxSystem/DeleteBaseProductSetting/IsRestoreShortTitle/{fxUserId}"
                ,Value= model.IsRestoreShortTitle },
                new SettingModel {Key=$"/FxSystem/DeleteBaseProductSetting/IsRestoreCostPrice/{fxUserId}"
                ,Value= model.IsRestoreCostPrice },
                new SettingModel {Key=$"/FxSystem/DeleteBaseProductSetting/IsRestoreSettlePrice/{fxUserId}"
                ,Value= model.IsRestoreSettlePrice },
                new SettingModel {Key=$"/FxSystem/DeleteBaseProductSetting/IsRestoreDistributePrice/{fxUserId}"
                ,Value= model.IsRestoreDistributePrice },
                new SettingModel {Key=$"/FxSystem/DeleteBaseProductSetting/IsRestoreSupplier/{fxUserId}"
                ,Value= model.IsRestoreSupplier },
            };
            Parallel.ForEach(keys,new ParallelOptions { MaxDegreeOfParallelism = 5 },(key)=>
            {
                try
                {
                    commonSettingService.Set(key.Key,key.Value.ToString(),shopId,true);
                }
                catch (Exception ex)
                {
                    Utility.Log.WriteError($"设置删除基础商品配置：{key} 错误：{ex}");
                }
            });
            res.Success = true;
            res.Data = true;
            return res;
        }


        /// <summary>
        ///  功能模块：基础商品、
        ///  功能点：商品删除配置获取、
        ///  描述信息：商品批量删除配置获取
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public AjaxResult<DeleteBaseProductSettingModel> GetDeleteSetting()
        {
            var commonSettingService = new CommonSettingService();
            var res = new AjaxResult<DeleteBaseProductSettingModel>();
            var resData = new DeleteBaseProductSettingModel();
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var shopId = SiteContext.Current.CurrentShopId;

            var key1 = $"/FxSystem/DeleteBaseProductSetting/IsRestoreShortTitle/{fxUserId}";
            var key2 = $"/FxSystem/DeleteBaseProductSetting/IsRestoreCostPrice/{fxUserId}";
            var key3 = $"/FxSystem/DeleteBaseProductSetting/IsRestoreSettlePrice/{fxUserId}";
            var key4 = $"/FxSystem/DeleteBaseProductSetting/IsRestoreDistributePrice/{fxUserId}";
            var key5 = $"/FxSystem/DeleteBaseProductSetting/IsRestoreSupplier/{fxUserId}";

            var keys = new List<string> {
               key1,
               key2,
               key3,
               key4,
               key5,
            };
            Parallel.ForEach(keys, new ParallelOptions { MaxDegreeOfParallelism = 5 }, (key) =>
            {
                try
                {
                    var data = commonSettingService.Get(key, shopId);

                    if (key == key1)
                    {
                        resData.IsRestoreShortTitle = data.Value.IsNullOrEmpty() ? false : Convert.ToBoolean(data.Value);
                    }
                    if (key == key2)
                    {
                        resData.IsRestoreCostPrice = data.Value.IsNullOrEmpty() ? false : Convert.ToBoolean(data.Value);
                    }
                    if (key == key3)
                    {
                        resData.IsRestoreSettlePrice = data.Value.IsNullOrEmpty() ? false : Convert.ToBoolean(data.Value);
                    }
                    if (key == key4)
                    {
                        resData.IsRestoreDistributePrice = data.Value.IsNullOrEmpty() ? false : Convert.ToBoolean(data.Value);
                    }
                    if (key == key5)
                    {
                        resData.IsRestoreSupplier = data.Value.IsNullOrEmpty() ? false : Convert.ToBoolean(data.Value);
                    }

                }
                catch (Exception ex)
                {
                    Utility.Log.WriteError($"设置删除基础商品配置：{key} 错误：{ex}");
                }
            });
            res.Success = true;
            res.Data = resData;
            return res;
        }
    }
}
