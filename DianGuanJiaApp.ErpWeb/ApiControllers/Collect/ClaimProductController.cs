using System;
using System.Collections.Generic;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json.Linq;
using System.Linq;
using DianGuanJiaApp.Utility.Web;
using System.Web.Http;
using DianGuanJiaApp.Services.CrossBorder;
using DianGuanJiaApp.Data.Model.CrossBorder;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.Entity.Collect;
using DianGuanJiaApp.Services.Services.CrossBorder;
using DianGuanJiaApp.Utility;
using System.Web;

namespace DianGuanJiaApp.ErpWeb.ApiControllers.Collect
{
    /// <summary>
    /// 商品箱
    /// </summary>
    public class ClaimProductController : BaseApiController
    {
        /// <summary>
        /// 商品认领关系服务
        /// </summary>
        private CollectClaimRelationService _relationService = new CollectClaimRelationService();

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("采集商品列表")]
        [HttpPost]
        public AjaxResult<PageResultApiModel<ClaimRelationPageResult>> GetClaimProductLsit(ClaimProductSearchModel model)
        {

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            model.FxUserId = fxUserId;
            var result = _relationService.GetClaimRelationPageRes(model);
            return new AjaxResult<PageResultApiModel<ClaimRelationPageResult>>()
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 加载全球店
        /// </summary>
        /// <returns></returns>
        public AjaxResult LoadCrossBorderStores()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //1.获取用户是否跨境店主账号 不存在 返回
            FxUserShopQueryModel _reqModel = new FxUserShopQueryModel()
            {
                FxUserId = fxUserId,
                QueryPageType = 2,
                IsShowAccount = true,
            };
            var tuple = new FxUserForeignShopService().GetList(_reqModel);
            //过滤本土店
            var list = tuple.Item2.Where(f => f.SellerType != TikTokShopType.LOCAL.ToString()).ToList();

            List<GlobalShopResult> results = new List<GlobalShopResult>();
            list.ForEach(f =>
            {
                GlobalShopResult globalShopResult = new GlobalShopResult()
                {
                    ShopId = f.ShopId,
                    SellerType = f.SellerType,
                    ShopName = f.NickName,
                    PlatformTypeName = f.PlatformTypeName,
                };
                results.Add(globalShopResult);
            });


            return new AjaxResult { Success = true, Message = "", Data = results };
        }

        /// <summary>
        /// 删除认领商品
        /// </summary>
        /// <param name="inModel"></param>
        /// <returns></returns>
        public AjaxResult DelClaimProductRelation(ClaimShopOperateInModel inModel)
        {
            if (inModel == null || !inModel.ClaimRelationUids.Any())
                return new AjaxResult { Success = false, Message = "参数异常，请重试", Data = null };
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var tuple = _relationService.DelClaimProductRelation(inModel.ClaimRelationUids, fxUserId);
            return new AjaxResult { Success = tuple.Item1, Message = tuple.Item2, Data = null };
        }

        /// <summary>
        /// 更新标题
        /// </summary>
        /// <param name="inModel"></param>
        /// <returns></returns>
        public AjaxResult UpdateClaimProductTitle(ClaimShopOperateInModel inModel)
        {
            if (inModel == null || !inModel.ClaimRelationUids.Any() || string.IsNullOrEmpty(inModel.Title))
                return new AjaxResult { Success = false, Message = "参数异常，请重试", Data = null };
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var tuple = _relationService.UpdateCollectProductTitle(inModel.ClaimRelationUids, inModel.Title, fxUserId);
            return new AjaxResult { Success = tuple.Item1, Message = tuple.Item2, Data = null };
        }


        /// <summary>
        /// 获取类目匹配校验
        /// </summary>
        /// <param name="inModel"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult GetCateMatchTaskResult(ClaimShopOperateInModel inModel)
        {
            if (inModel == null || inModel.ClaimRelationUids == null || !inModel.ClaimRelationUids.Any())
                return new AjaxResult { Success = false, Message = "参数异常，请重试", Data = null };
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var tuple = _relationService.GetClaimRelatioCateMatchTaskResult(inModel.ClaimRelationUids, fxUserId);
            return new AjaxResult { Success = tuple.Item1, Message = "", Data = tuple.Item2 };
        }


        /// <summary>
        /// 标记编辑
        /// </summary>
        /// <param name="inModel"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ClaimRelationMarkers(ClaimShopOperateInModel inModel)
        {
            if (inModel == null || !inModel.ClaimRelationUids.Any())
                return new AjaxResult { Success = false, Message = "参数异常，请重试", Data = null };
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _relationService.UpClaimProductRelationMarkers(string.Join(",", inModel.ClaimRelationUids), inModel.IsMarkers, fxUserId) > 0;
            return new AjaxResult { Success = result, Message = "", Data = null };
        }


        /// <summary>
        /// 预选店铺
        /// </summary>
        /// <param name="inModel"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ChoiceClaimProductShop(ClaimShopInModel inModel)
        {
            if (inModel == null || !inModel.ClaimRelationUid.Any() || !inModel.ShopIds.Any())
                return new AjaxResult { Success = false, Message = "参数异常，请重试", Data = null };
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var tuple = _relationService.ChoiceClaimShop(inModel, fxUserId);
            if (!tuple.Item1)
                return new AjaxResult { Success = false, Message = tuple.Item2, Data = null };
            return new AjaxResult { Success = true, Message = "", Data = null };
        }


        /// <summary>
        /// 取消店铺预选
        /// </summary>
        /// <param name="inModel"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult CancelClaimProductRelation(ClaimShopInModel inModel)
        {
            if (inModel == null || !inModel.ClaimRelationUid.Any() || !inModel.ShopIds.Any())
                return new AjaxResult { Success = false, Message = "参数异常，请重试", Data = null };
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var tuple = _relationService.CancelClaimProductRelation(string.Join(",", inModel.ClaimRelationUid), inModel.ShopIds, fxUserId);
            if (!tuple.Item1)
                return new AjaxResult { Success = false, Message = tuple.Item2, Data = null };
            return new AjaxResult { Success = true, Message = "", Data = null };
        }

        /// <summary>
        /// 发布全球商品
        /// </summary>
        /// <param name="inModel">Uid</param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult PublishGlobalProduct(ClaimPublishGlobalShopModel inModel)
        {
            if (inModel == null)
                return new AjaxResult { Data = null, Success = false, Message = "参数不可为空" };
            if (inModel.ClaimRelationUIds == null || !inModel.ClaimRelationUIds.Any())
                return new AjaxResult { Data = null, Success = false, Message = "参数不可为空" };
            if (inModel.PublishShopIds == null || !inModel.PublishShopIds.Any())
                return new AjaxResult { Data = null, Success = false, Message = "PublishShopIds参数不可为空" };
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var tuple = _relationService.PublishGlobalProduct(inModel.ClaimRelationUIds, inModel.PublishShopIds, fxUserId);
            return new AjaxResult { Success = tuple.Item1, Message = tuple.Item2, Data = tuple.Item3 };
        }


        /// <summary>
        /// 获取品牌
        /// </summary>
        /// <param name="searchBrand"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult GetCrossBrands(CrossSearchBrandList searchBrand)
        {
            if (string.IsNullOrEmpty(searchBrand.ShopId))
                return new AjaxResult { Success = false, Message = "参数有误，请稍后重试", Data = null };
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var brandModels = new BrandInfoResponse();
            //1.通过主账号ID 反查跨境TK站点店铺 SiteContext.Current.AllShops
            var subList = new ShopService().GetSiteShopsByManShopId(fxUserId, searchBrand.ShopId.ToInt(), PlatformType.TikTok.ToString());
            if (!subList.Any())
                return new AjaxResult { Success = false, Message = "非当前用户店铺信息", Data = brandModels };
            var siteShop = subList.Find(x => x.RoleType == "CROSS_BORDER");
            if (siteShop != null)
            {
                var tkPlatformService = PlatformFactory.GetPlatformService(siteShop) as TikTokPlatformService;
                searchBrand.ShopId = siteShop.ShopId;
                var tuple = tkPlatformService.GetTikTokBrands(searchBrand);
                if (tuple.Item1)
                {
                    brandModels = tuple.Item3;
                }
            }
            return new AjaxResult { Success = true, Message = "", Data = brandModels };
        }

        /// <summary>
        /// 获取采集认领明细
        /// </summary>
        /// <param name="inModel"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult GetClaimItemInfo(ClaimProductInModel inModel)
        {
            if (string.IsNullOrEmpty(inModel.DetailId) || inModel.ShopId <= 0)
                return new AjaxResult { Success = false, Message = "参数异常，请重试", Data = null };

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var model = _relationService.GetClaimShopProductAndExtInfo(inModel.DetailId, inModel.ShopId, fxUserId);

            return new AjaxResult { Success = true, Message = "", Data = model };
        }



        /// <summary>
        /// 获取平台类目数据
        /// </summary>
        /// <param name="inModel"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult GetCategoryMetadata(ProductCategoryInModel inModel)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //1.获取用户是否跨境店主账号 不存在 返回
            FxUserShopQueryModel _reqModel = new FxUserShopQueryModel()
            {
                FxUserId = fxUserId,
                QueryPageType = 2,
                IsShowAccount = true,
            };
            var tuple = new FxUserForeignShopService().GetList(_reqModel);
            //过滤本土店
            var model = tuple.Item2.Find(f => f.ShopId == inModel.ShopId);
            if (model == null)
                return new AjaxResult { Success = false, Message = "验证失败!", Data = null };
            var reult = _relationService.GetCategoryMetadata(inModel.Cid, inModel.ShopId, inModel.PlatformType, fxUserId, inModel.ProductUid);
            return new AjaxResult { Success = true, Message = "", Data = reult };
        }


        /// <summary>
        /// 保存店铺采集
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult SaveShopCollectItemInfo(ClaimProductInfoModel model)
        {
            if (model == null)
                return new AjaxResult { Success = false, Message = "参数异常" };

            var operation = model.Operationtype;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var claimShopProducts = _relationService.GetClaimShopProductByClaimUid(model.ClaimUId, fxUserId);
            var claimShopProduct = claimShopProducts.Find(x => x.Uid == model.ItemId);
            if (claimShopProduct == null)
                return new AjaxResult { Success = false, Message = "保存失败，未找到相关数据" };
            if (model.Operationtype == 0)
                return new AjaxResult { Success = false, Message = "参数有误，请选择保存类型" };
            if (model.Weight == null || model.Weight <= 0)
                return new AjaxResult { Success = false, Message = "物流信息:重量信息不可为空" };
            if (string.IsNullOrEmpty(model.PackageHeight) || string.IsNullOrEmpty(model.PackageWidth) || string.IsNullOrEmpty(model.PackageLength))
                return new AjaxResult { Success = false, Message = "物流信息:尺寸信息不可为空" };
            if (string.IsNullOrEmpty(model.CategoryId))
            {
                return new AjaxResult { Success = false, Message = "类目信息:类目信息不可为空" };
            }
            PackageWeight Weight = new PackageWeight() { Weight = model.Weight, WeightUnit = model.WeightUnit };
            PackageDimensions dimensions = new PackageDimensions() { PackageHeight = model.PackageHeight, PackageWidth = model.PackageWidth, PackageLength = model.PackageLength };
            List<ClaimShopProduct> needUpClaimProducts = new List<ClaimShopProduct>();
            foreach (var item in claimShopProducts)
            {
                item.CategoryId = model.CategoryId;
                item.Ext.ProductAttributes = model.ProductAttributes.ToJson();
                item.BrandId = model.BrandId.ToInt();
                item.ImageUrl = model.ProductImages != null ? model.ProductImages[0] : null;
                item.Title = model.Title;
                item.ImageUrl = model.ProductInfoSkus != null ? model.ProductInfoSkus.FirstOrDefault()?.ImageUrl : null;
                item.Ext.Description = model.Description;
                item.Ext.PackageDimensions = dimensions.ToJson();
                item.Ext.PackageWeight = Weight.ToJson(); ;
                item.Ext.ProductImages = model.ProductImages != null ? string.Join(",", model.ProductImages) : null;
                item.Ext.SizeImageUrl = model.SizeImageUrl;
                item.Ext.CertificationUrls = model.CertificationUrls != null ? string.Join(",", model.CertificationUrls) : null;
                item.Ext.Video = model.Video.ToJson();
                item.Ext.ProductInfoSkus = model.ProductInfoSkus != null ? model.ProductInfoSkus.ToJson() : null;
                item.Ext.Description = !string.IsNullOrEmpty(model.Description) ? HttpUtility.UrlDecode(model.Description) : null;
                item.Ext.AttributeTypes = model.AttributeTypes != null ? model.AttributeTypes.ToJson() : null;
                item.UpdateTime = DateTime.Now;
                //保存//保存并发布
                if ((operation == 1 || operation == 3) && item.Uid == model.ItemId)
                    needUpClaimProducts.Add(item);
                if (operation == 2)//保存并更新其他认领关系店铺
                    needUpClaimProducts.Add(item);
            }
            var tuple = _relationService.SaveShopCollectItemInfo(needUpClaimProducts, operation, fxUserId);
            return new AjaxResult { Success = tuple.Item1, Message = tuple.Item2 };
        }


        /// <summary>
        /// 匹配产品属性与商品规格
        /// </summary>
        /// <returns></returns>
        public AjaxResult MatchProductAttrAndSkuProperty(ProductAttrAndSkuPropertyInModel inModel)
        {
            // 参数验证
            if (string.IsNullOrEmpty(inModel.Cid) || inModel.ShopId <= 0)
                return new AjaxResult { Success = false, Message = "参数异常，请重试", Data = null };

            var productAttrAndSku = new ProductAttrAndSkuPropertyModel();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            // 获取用户店铺信息
            var subList = new ShopService().GetSiteShopsByManShopId(fxUserId, inModel.ShopId, PlatformType.TikTok.ToString());
            if (!subList.Any())
                return new AjaxResult { Success = false, Message = "非当前用户信息" };

            var siteShop = subList.FirstOrDefault(x => x.RoleType == "CROSS_BORDER");
            if (siteShop == null)
                return new AjaxResult { Success = false, Message = "未找到符合条件" };
            // 获取平台服务并匹配产品属性
            var tkPlatformService = PlatformFactory.GetPlatformService(siteShop) as TikTokPlatformService;
            if (tkPlatformService != null)
            {
                var tuple = tkPlatformService.GetGlobalGetAttributes(inModel.Cid);
                if (tuple != null && tuple.Item1)
                {
                    var proAttributes = new MatchCateService().MapProductAttributes(inModel.SourceAttrs, tuple.Item3);
                    if (proAttributes != null && proAttributes.Any())
                    {
                        productAttrAndSku.ProductAttribute = proAttributes.ToList();
                    }
                }
            }
            // 设置 SKU 属性列表
            productAttrAndSku.SkuPropertyList = inModel.SkuPropertyList;
            // 匹配成功返回结果
            return new AjaxResult { Success = true, Message = "", Data = productAttrAndSku };
        }

        /// <summary>
        /// 更新SKU明细价格或库存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public AjaxResult UpClaimProductSkuPriceOrInventory(ProductSkusInModel model)
        {
            if (model.SkuModels == null || !model.SkuModels.Any())
                return new AjaxResult { Success = false, Message = "参数有误，请选择更新类型" };

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var claimShopProduct = _relationService.GetClaimShopProductByUid(model.ProductUid.ToString(), model.ShopId, fxUserId);
            if (claimShopProduct == null)
                return new AjaxResult { Success = false, Message = "未找到相关数据，请刷新重试" };
            claimShopProduct.Ext.ProductInfoSkus = model.SkuModels.ToJson();
            _relationService.UpClaimProductSkuPriceOrInventory(claimShopProduct);
            return new AjaxResult { Success = true, Message = "", Data = null };
        }


        /// <summary>
        /// 一键翻译认领商品
        /// </summary>
        /// <returns></returns>
        public AjaxResult TranslateCollectItemInfo(ClaimProductInfoModel model)
        {
            if (model == null)
                return new AjaxResult { Success = false, Message = "参数异常" };

            var operation = model.Operationtype;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var claimShopProducts = _relationService.GetClaimShopProductByClaimUid(model.ClaimUId, fxUserId);
            var claimShopProduct = claimShopProducts.Find(x => x.Uid == model.ItemId);
            if (claimShopProduct == null)
                return new AjaxResult { Success = false, Message = "翻译失败，未找到相关数据，请刷新重试" };
            var tuple = _relationService.TranslateCollectItemInfo(model);
            return new AjaxResult { Success = tuple.Item1, Data = tuple.Item2};
        }
    }
}