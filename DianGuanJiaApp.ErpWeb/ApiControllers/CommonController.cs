using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace DianGuanJiaApp.ErpWeb.ApiControllers
{
    /// <summary>
    /// 公用配置接口
    /// </summary>
    public class CommonController : BaseApiController
    {
        /// <summary>
        ///  功能模块：跨云平台组件、
        ///  功能点：分区切换组件、
        ///  描述信息： 获取全平台分区DbAreas、
        ///  接口开发人员：李云
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogForOperatorFilter("获取全平台分区")]
        public AjaxResult<List<CommonDbModel>> GetAllPlatformDbAreas()
        {
            var dataModels = new List<CommonDbModel>();
            var fxUserId =
                SiteContext.Current.CurrentFxUserId;
            var currentDbName =
                SiteContext.Current.CurrentDbConfig.DbNameConfig.DbName;
            var currentSitePlatformType =
                CustomerConfig.CloudPlatformType;
            var allPlatformTypes =
                CustomerConfig.AllCloudPts;
            // 当前云
            var currentPlatformType =
                allPlatformTypes.Where(a => a.ToLower() == currentSitePlatformType.ToLower()).FirstOrDefault();
            // 其他云
            var otherPlatformType =
                allPlatformTypes.Where(a => a.ToLower() != currentSitePlatformType.ToLower()).ToList();

            if (currentPlatformType.IsNotNullOrEmpty())// 当前云
            {
                //分区
                var dbAreas = SiteContext.Current.CurrentDbAreaConfig
                    .Select(x => x.DbNameConfig).OrderBy(x => x.Id)
                    .Select(y => new DbAreaName
                    {
                        DbName = DES.EncryptDES(y.DbName, CustomerConfig.LoginCookieEncryptKey),
                        NickName = y.NickName,
                        ApplicationName = y.ApplicationName
                    }).ToList();

                var commonDbModel = new CommonDbModel();
                commonDbModel.PlatformName = CustomerConfig.GetPtName(currentPlatformType);
                commonDbModel.PlatformDomain = CustomerConfig.GetPtUrl(currentPlatformType);
                commonDbModel.PlatformType = currentPlatformType;
                commonDbModel.DbAreas = dbAreas;
                commonDbModel.DbName = DES.EncryptDES(currentDbName, CustomerConfig.LoginCookieEncryptKey);
                dataModels.Add(commonDbModel);
            }

            if (otherPlatformType != null && otherPlatformType.Count > 0)// 其他云
            {
                var wu = new WebUtils(15000);
                if (CustomerConfig.IsDebug)
                    wu = new WebUtils(120000);
                var headers =
                    new Dictionary<string, string> {
                        { "User-Agent", Request.Headers["User-Agent"] },
                        { "X-Requested-With", "XMLHttpRequest" }
                    };
                // 获取多云数据
                Parallel.ForEach(otherPlatformType, new ParallelOptions { MaxDegreeOfParallelism = 5 }, type =>
                {
                    try
                    {
                        //分销云平台地址
                        var url = CustomerConfig.GetPtUrl(type);
                        var apiUrl = url.TrimEnd("/") +
                                     $"/api/Common/GetPlatformDbAreas?token={Request["token"]}&cloudPlatformType={type}";
                        var result = wu.DoPost(apiUrl,
                            new Dictionary<string, string> { { "timestamp", DateTime.Now.ToTicks().ToString() } },
                            headers: headers);

                        var res = result.ToObject<AjaxResult<CommonDbModel>>();
                        if (res.Success && res.Data != null)
                        {
                            dataModels.Add(res.Data);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"跨云获取数据分区信息异常：异常平台：{type}，参数：{ex.ToJson()}");
                    }
                });
            }
            return new AjaxResult<List<CommonDbModel>>
            {
                Success = true,
                Data = dataModels,
                Message = "成功！"
            };
        }

        /// <summary>
        ///  功能模块：跨云平台组件、
        ///  功能点：分区切换组件、
        ///  描述信息： 获取指定平台分区DbAreas、
        ///  接口开发人员：李云
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogForOperatorFilter("获取指定台分区")]
        public AjaxResult<CommonDbModel> GetPlatformDbAreas(string cloudPlatformType)
        {
            var fxUserId =
                SiteContext.Current.CurrentFxUserId;
            var currentDbName =
                SiteContext.Current.CurrentDbConfig.DbNameConfig.DbName;
            var currentSitePlatformType =
                CustomerConfig.CloudPlatformType;
            CommonDbModel commonDbModel = null;
            if (cloudPlatformType.IsNotNullOrEmpty() && cloudPlatformType.ToLower() == currentSitePlatformType.ToLower())// 当前云
            {
                //分区
                var dbAreas = SiteContext.Current.CurrentDbAreaConfig
                    .Select(x => x.DbNameConfig).OrderBy(x => x.Id)
                    .Select(y => new DbAreaName
                    {
                        DbName = DES.EncryptDES(y.DbName, CustomerConfig.LoginCookieEncryptKey),
                        NickName = y.NickName,
                        ApplicationName = y.ApplicationName
                    }).ToList();
                commonDbModel = new CommonDbModel();
                commonDbModel.PlatformName = CustomerConfig.GetPtName(cloudPlatformType);
                commonDbModel.PlatformDomain = CustomerConfig.GetPtUrl(cloudPlatformType);
                commonDbModel.PlatformType = cloudPlatformType;
                commonDbModel.DbAreas = dbAreas;
                commonDbModel.DbName = DES.EncryptDES(currentDbName, CustomerConfig.LoginCookieEncryptKey);
            }
            return new AjaxResult<CommonDbModel>
            {
                Success = true,
                Data = commonDbModel,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 功能：【双角色用户】获取当前用户标记；
        /// 版本：2024-07-05 邱庆基 创建
        /// </summary>
        /// <returns>1688new=1688预付新用户；only_listing=纯铺货用户；from_listing=纯铺货用户转双应用</returns>
        [HttpPost]
        public AjaxResult<string> GetUserFlag()
        {
            var userFlag = SiteContext.Current.CurrentFxUser.UserFlag.ToString2();

            return new AjaxResult<string>
            {
                Success = true,
                Data = userFlag,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 功能：是否白名单用户；
        /// 版本：2024-07-23 邱庆基 创建
        /// </summary>
        /// <returns>1=白名单用户；0=非白名单用户；</returns>
        [HttpPost]
        public AjaxResult<int> IsWhiteFxUser()
        {
            try
            {
                var isWhiteUser = SiteContext.Current.IsWhiteUser;
                return isWhiteUser ? SuccessResult(1) : SuccessResult(0);
            }
            catch (Exception ex)
            {
                return new AjaxResult<int>
                {
                    Success = false,
                    Data = 0,
                    Message = "系统异常"
                };
            }
        }

        /// <summary>
        /// 是否【大客户优化白名单】用户
        /// </summary>
        /// <returns>1=白名单用户；0=非白名单用户；</returns>
        [HttpPost]
        public AjaxResult<int> IsReconVipFxUser()
        {
            try
            {
                var isIsReconVipUser = SiteContext.Current.IsReconVip;
                return isIsReconVipUser ? SuccessResult(1) : SuccessResult(0);
            }
            catch (Exception ex)
            {
                return new AjaxResult<int>
                {
                    Success = false,
                    Data = 0,
                    Message = "系统异常"
                };
            }
        }

        /// <summary>
        /// 功能：【双角色用户】支持铺货平台的店铺订购普通应用的情况；
        /// 版本：2024-07-05 邱庆基 创建
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<List<ShopSubscribeAppView>> GetSubscribeNormalAppShops(ShopSubscribeAppQuery model)
        {
            if (model == null)
                model = new ShopSubscribeAppQuery();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            model.FxUserId = fxUserId;
            var shopAppOrders = new FxUserShopService().GetSubscribeNormalAppShops(model);

            return new AjaxResult<List<ShopSubscribeAppView>>
            {
                Success = true,
                Data = shopAppOrders,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 功能：清除FxUser缓存
        /// 版本：2024-08-01 邱庆基 创建
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<string> ClearFxUserCache()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //清除缓存
            FxCaching.ForeRefeshCache(FxCachingType.FxUser, fxUserId.ToString2());

            return new AjaxResult<string>
            {
                Success = true,
                Data = "",
                Message = "成功！"
            };
        }

        /// <summary>
        /// 获取消息提醒设置
        /// </summary>
        /// <returns></returns>
        public AjaxResult<ReminderSettingModel> GetReminderSetting()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var result = new CommonSettingService().GetReminderSetting(shopId);
            return new AjaxResult<ReminderSettingModel>()
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 保存消息提醒设置
        /// </summary>
        /// <returns></returns>
        public AjaxResult SaveReminderSetting(ReminderSettingModel model)
        {
            var result = new AjaxResult();
            try
            {
                result.Success = true;
                result.Data = true;
                result.Message = "成功！";
                var shopId = SiteContext.Current.CurrentShopId;
                new CommonSettingService().SaveReminderSetting(model, shopId);
            }
            catch (Exception ex)
            {
                Log.WriteError($"保存消息提醒设置失败！失败原因：{ex.ToJson()}");
                result.Message = $"保存消息提醒设置失败！失败原因：{ex.ToJson()}";
                result.Success = false;
                result.Data = false;
            }
            return result;
        }

        /// <summary>
        /// 获取我的代理商（商家信息）
        /// </summary>
        /// <returns></returns>
        public AjaxResult<List<AgentModel>> LoadMyAgents()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var _supplierUserService = new SupplierUserService();
            //商家数据源
            var agents = _supplierUserService.GetAgentList(fxUserId, onlyGetCurDb: true,needEncryptAccount:true);
            var result = agents.Select(x => new AgentModel { 
                UserName = x.AgentMobileAndRemark,
                FxUserId =  x.FxUserId, 
                Status =  x.Status, 
                IsTop = x.IsTop 
            }).Distinct().ToList();
            return new AjaxResult<List<AgentModel>>()
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }
        
        /// <summary>
        /// 功能：是否白名单用户，用于开启供应链相关的功能
        /// 版本：2024-11-10 肖茂翔 创建
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<bool> GetIsWhiteUser()
        {
            // 如果没开启，默认返回true，不过滤
            if (!CustomerConfig.IsFxWhiteUserFilter) return SuccessResult(true);
            
            var isWhiteUser = SiteContext.Current.IsWhiteUser;
            return isWhiteUser ? SuccessResult(true) : SuccessResult(false);
        }

        [HttpPost]
        /// <summary>
        /// 0324-新旧面单切换强引导需求
        /// 检查当前用户添加小红书店铺的分销商和小红书店铺分销商合作的厂家，并且没有添加新版小红书模板。
        /// </summary>
        /// <returns>true:表示有小红书店铺、厂家需要提示添加新版小红书模版</returns>
        public AjaxResult<bool> CheckNewXiaoHongShuTemplate()
        {
            bool result = false;

            //当前用户
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //系统店铺id
            var curShopId = SiteContext.Current.CurrentShopId;

            var rdsKey = $"CheckNewXiaoHongShuTemplate";
            var cache = "";
            try {
                cache = RedisHelper.Get<string>($"{rdsKey}/{fxUserId}");
            }
            catch (Exception ex)
            {
                Log.WriteError($"配置库CSRedis仓储读取配置信息发生错误，keys:{rdsKey}，{ex} {ex?.InnerException}");
            }
            if (cache.IsNotNullOrEmpty()) { 
                return SuccessResult(cache.ToBool());
            }

            //查找该用户下绑定的分销商和合作的厂家 的用户
            var supplierService = new SupplierUserService();
            var fxusers = supplierService.GetSuppliersByFxUserId(fxUserId, " DISTINCT f.FxUserId ", false);
            var fxList = new List<int>(fxUserId);
    
            if (fxusers != null && fxusers.Any())
                fxList.AddRange(fxusers.Select(x => x.FxUserId).Distinct().ToList());

            fxList = fxList.Distinct().ToList();

            //相关用户下绑定的小红书店铺
            var shopService = new FxUserShopService();
            var xhsShops = shopService.GetShopListByPlatformType(fxList, PlatformType.XiaoHongShu.ToString(), null);

            //存在小红书店铺的，判断该用户下是否存在新版小红书电子面单
            if (xhsShops != null && xhsShops.Any())
            {
                var templateService = new PrintTemplateService();
                var hasNewXhsTemplate = templateService.CheckUserIsExistsTempalte(curShopId, new List<int> { 180, 182 });
                //存在新版小红书模板不提示，不存在提示·
                result = !hasNewXhsTemplate;
            }
            try
            {
                RedisHelper.Set($"{rdsKey}/{fxUserId}",result.ToString(),1800);
            }
            catch (Exception ex)
            {
                Log.WriteError($"配置库CSRedis仓储保存配置信息发生错误，keys:{rdsKey}，value:{result},{ex} {ex?.InnerException}");

            }
            return SuccessResult(result);
        }
    }
}
