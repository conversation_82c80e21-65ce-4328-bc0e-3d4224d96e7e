using System;
using System.Collections.Generic;
using System.Web.Mvc;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Services;
using System.Linq;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Data.Repository;
using System.Web;

namespace DianGuanJiaApp.ErpWeb
{
    public class FxMigrateLockFilter : ActionFilterAttribute
    {
        private static CommonSettingRepository _csRep = new CommonSettingRepository();
        public int Flag { get; set; }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="flag">1:同步订单</param>
        public FxMigrateLockFilter(int flag = 0)
        {
            Flag = flag;
        }
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString() || CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString())
            {
                #region 过滤标签已放到Controller方法层，此处注释掉，不再判断ActionName
                /*
                var key = "/System/Fendan/FxMigrateLock/FilterUrlList";
                var filterUrlString = _csRep.GetBySysCache(key, 0)?.Value ?? "";
                if (!string.IsNullOrEmpty(filterUrlString))
                {
                    var controllerName = filterContext.ActionDescriptor.ControllerDescriptor.ControllerName ?? "";
                    var actionName = filterContext.ActionDescriptor.ActionName ?? "";
                    var curActionName = $"{controllerName}/{actionName}";
                    var dictUrl = filterUrlString.Split(',').Distinct().ToDictionary(a => a, a => 1);
                    if (dictUrl.ContainsKey(curActionName))
                    {
                        //检查迁移锁
                        var isFxMigrateLock = new FxMigrateLockService().IsFxMigrateLock(SiteContext.Current.CurrentFxUserId);

                        var errorCode = "FX_MIGRATELOCK";
                        if (Flag == 1)
                        {
                            errorCode = "FX_MIGRATELOCK_SYNC";
                        }
                        if (isFxMigrateLock)
                        {
                            throw new LogicException("因平台数据存储要求，正在将您系统的抖音订单迁移至抖店云平台，为保证数据迁移完整，迁移期间暂停该功能操作，预计迁移还需2分钟。迁移完成后，请您前往抖店云平台操作，谢谢您的理解。", errorCode);
                        }
                    }
                }
                */
                #endregion


                int fxUserID = SiteContext.CurrentNoThrow?.CurrentFxUserId?? 0;
                ////未加载base时候，不存在SiteContext和fxUserID
                //if (fxUserID == 0)
                //{
                //    var baseController = new BaseController(filterContext.HttpContext.Request);
                //    baseController.ValidLoginToken(filterContext.HttpContext.Request);
                //    fxUserID = baseController.GetCurrFxUserId();
                //}

                if (fxUserID == 0)
                    return;

                //2023-10-12
                //1、精选云：只有指定白名单的用户才要作迁移锁检查
                //2、抖店云：保持原逻辑不变
                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                {
                    var key = "/System/Fendan/NeedWriteDataChangeLog/FxUserIds";
                    var strNeedWriteLogFxUserIds = _csRep.Get(key, 0)?.Value ?? "";
                    if (string.IsNullOrEmpty(strNeedWriteLogFxUserIds))
                        return;

                    strNeedWriteLogFxUserIds = strNeedWriteLogFxUserIds.Replace("，", ",");
                    strNeedWriteLogFxUserIds = strNeedWriteLogFxUserIds.Replace(" ", ",");

                    if (strNeedWriteLogFxUserIds.Split(',').Contains(fxUserID.ToString()) == false)
                        return;
                }

                //检查迁移锁
                new FxMigrateLockService().CheckFxMigrateLock(fxUserID, flag: Flag);
            }
        }

    }
}