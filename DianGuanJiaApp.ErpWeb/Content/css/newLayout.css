
.newVersionsLeftNav .layui-side-menu .layui-nav, .newVersionsLeftNav.layui-layout-admin .layui-header, .newVersionsLeftNav.layui-layout-admin .layui-logo, .newVersionsLeftNav.layui-layout-admin .layui-header .layui-nav .layui-nav-item {
    height: 50px;
    line-height: 50px;
}

.newVersionsLeftNav.layui-layout-admin .layui-logo {
    background-color: #fff;
    box-shadow: unset;
    height: 48px;
}

.newVersionsLeftNav.layui-layout-admin .layui-logo, .layui-layout-admin .layui-logo a {
    color: #666;
}

.newVersionsLeftNav .layui-side-menu, .newVersionsLeftNav .layadmin-setTheme-side, .newVersionsLeftNav .layui-bg-black {
    background-color: unset !important;
}

.newVersionsLeftNav.layui-layout-admin .layui-side {
    top: 50px;
}

.newVersionsLeftNav.layui-layout-admin .layui-body {
    top: 50px;
}

.newVersionsLeftNav .layui-side-menu .layui-side-scroll, .newVersionsLeftNav.layui-layout-admin .layui-side {
    width: 115px;
}
/* .newVersionsLeftNav.layui-layout-admin .layui-layout-left,.newVersionsLeftNav .layadmin-pagetabs, .newVersionsLeftNav.layui-layout-admin .layui-body, .newVersionsLeftNav.layui-layout-admin .layui-footer {
    left: 230px;
} */
.newChangeNav {
    width: 115px;
    background-color: #fff;
    height: 100%;
    display: flex;
    flex-direction: row;
    position: relative;
    border-right: 1px solid #ecf0f5;
}

    .newChangeNav .newChangeNav-left {
        background-color: #282b33;
        color: #b9bbc7;
        padding: 15px 0;
        box-sizing: border-box;
        width: 115px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        position: relative;
    }

    .newChangeNav .newChangeNav-left-down {
        flex: 1;
        margin-top: 15px;
        overflow-y: auto;
        padding-bottom: 20px;
    }

    .newChangeNav .newChangeNav-left-goBack {
        display: flex;
        color: #9fa2ad;
        position: absolute;
        bottom: 0;
        left: 0;
        padding: 15px 0;
        cursor: pointer;
        width: 100%;
        justify-content: center;
        background-color: #282b33;
    }

    .newChangeNav .newChangeNav-left .newChangeNav-left-ul {
        display: flex;
        flex-direction: column;
    }

        .newChangeNav .newChangeNav-left .newChangeNav-left-ul > li {
            display: flex;
            margin: 5px;
        }

            .newChangeNav .newChangeNav-left .newChangeNav-left-ul > li > a {
                padding: 8px 5px 8px 7px;
                display: flex;
                align-items: center;
                cursor: pointer;
                color: #b9bbc7;
            }

            .newChangeNav .newChangeNav-left .newChangeNav-left-ul > li.active {
                background-color: #3aadff;
                color: #fff;
                border-radius: 5px;
            }

                .newChangeNav .newChangeNav-left .newChangeNav-left-ul > li.active > a {
                    background-color: #3aadff;
                    color: #fff;
                    border-radius: 5px;
                }

            .newChangeNav .newChangeNav-left .newChangeNav-left-ul > li:hover {
                color: #d0d1df;
            }

                .newChangeNav .newChangeNav-left .newChangeNav-left-ul > li:hover.active {
                    color: #fff;
                }

            .newChangeNav .newChangeNav-left .newChangeNav-left-ul > li > a > i {
                font-size: 18px;
                display: inline-block;
                width: 28px;
            }

            .newChangeNav .newChangeNav-left .newChangeNav-left-ul > li > a > span {
                font-size: 14px;
            }

    .newChangeNav .newChangeNav-right {
        display: flex;
        padding: 15px 0;
        width: 115px;
        box-sizing: border-box;
        position: absolute;
        top: 0;
        left: 115px;
        background-color: #fff;
        flex: 1;
        /*height: 100%;*/
    }

        .newChangeNav .newChangeNav-right .newChangeNav-right-ul {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

            .newChangeNav .newChangeNav-right .newChangeNav-right-ul > li {
                display: flex;
                flex-direction: row;
                width: 100%;
                box-sizing: border-box;
            }

                .newChangeNav .newChangeNav-right .newChangeNav-right-ul > li > a {
                    display: flex;
                    padding: 12px 15px 12px 15px;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: center;
                    color: #252931;
                    width: 100%;
                }

                .newChangeNav .newChangeNav-right .newChangeNav-right-ul > li:hover {
                    color: #3aadff;
                    cursor: pointer;
                }

                .newChangeNav .newChangeNav-right .newChangeNav-right-ul > li > a > span {
                    font-size: 12px;
                }

                .newChangeNav .newChangeNav-right .newChangeNav-right-ul > li > a > i {
                    color: #3aadff;
                    transform: rotate(-90deg) scale(0.8);
                    font-size: 12px;
                    display: none;
                    position: relative;
                    left: 5px;
                }

                .newChangeNav .newChangeNav-right .newChangeNav-right-ul > li.active > a > span {
                    color: #3aadff;
                }

                .newChangeNav .newChangeNav-right .newChangeNav-right-ul > li.active > a > i {
                    color: #3aadff;
                    display: block;
                }

    .newChangeNav .newChangeNav-right-active {
        position: fixed;
        width: 150px;
        top: 0;
        left: 115px;
        box-sizing: border-box;
        background-color: #fff;
        z-index: 100000;
        border-right: 1px solid #e2e2e2;
        box-shadow: 3px 2px 10px 1px #e2e2e2;
        box-shadow: 0 2px 12px 0 rgb(0,0,0,0.1);
        border-radius: 2px;
        border-left: 2px solid #45c6ff;
        opacity: 0;
        animation: operateLasChild03 0.2s ease-in-out 0s 1 alternate forwards;
    }

@keyframes operateLasChild03 {
    0% {
        opacity: 0;
        left: 130px;
    }

    100% {
        opacity: 1;
        left: 115px;
    }
}

.newVersionsLeftNav.layui-layout-admin .layui-bodys, .newVersionsLeftNav.newVersionsLeftNav.layui-layout-admin .layui-layout-left, .newVersionsLeftNav.newVersionsLeftNav .layadmin-pagetabs, .newVersionsLeftNav.newVersionsLeftNav.layui-layout-admin .layui-body, .newVersionsLeftNav.newVersionsLeftNav.layui-layout-admin .layui-footer {
    margin-left: 115px;
}

.newChangeNav-left-goBackNew {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 170px;
    padding: 15px 0;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.newVersionsLeftNav.layui-layout-admin .layui-header .layui-nav .layui-nav-item .header-nav-title {
    font-size: 12px;
    color: #565960;
}

.newVersionsLeftNav.layui-layout-admin .layui-header a {
    line-height: 46px;
    position: relative;
}

.newVersionsLeftNav.layui-layout-admin .layui-header .layui-nav-item::after {
    width: 1px;
    height: 16px;
    display: block;
    content: "";
    position: absolute;
    right: -1px;
    top: 18px;
    background-color: #e2e2e2;
}

.newVersionsLeftNav .layui-nav.layui-layout-right .layui-nav-item .iconfont {
    font-size: 18px;
}

.newVersionsLeftNav .layui-header .layui-nav-item .layui-icon {
    font-size: 18px;
    color: #666 !important;
    font-weight: 400;
}

.shopPastWrap {
    display: flex;
    flex-direction: row;
    border: 1px solid #fde2e2;
    height: 28px;
    border-radius: 3px;
    background-color: #fef0f0;
    align-items: center;
    padding: 0 10px;
    color: #f56c6c;
    cursor: pointer;
    position: relative;
}
.shopPastWrap .shopPastWrap-main {
    max-height: 250px;
    overflow: auto;
}

.shopPastWrap > span {
    font-size: 12px;
}

    .shopPastWrap > .shopPastWrap-num {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        background-color: #fff;
        border-radius: 50%;
        color: #f56c6c;
        font-size: 12px;
        margin-right: 3px;
        justify-content: center;
    }

    .shopPastWrap > .icon-down {
        color: #f56c6c !important;
        border-top-color: #f56c6c !important;
        font-size: 12px !important;
        margin-left: 5px;
    }

    .shopPastWrap .shopPastWrapShow {
        position: absolute;
        top: 45px;
        width: 400px;
        background-color: #fff;
        box-shadow: 0 0 8px 0 #cbcbcb;
        border-radius: 4px;
        right: 0;
        box-sizing: border-box;
        padding: 5px 15px;
        display: none;
        opacity: 0;
        flex-direction:column;
    }

    .shopPastWrap:hover .shopPastWrapShow {
        display: flex;
        top: 50px;
        opacity: 1;
        animation: anShopPastWrap_main 0.2s ease-in-out 0s 1 alternate forwards;
    }

    .shopPastWrap .icon-down {
        transition: 0.2s all;
        transform: rotate(0deg);
    }

    .shopPastWrap:hover .icon-down {
        transform: rotate(180deg);
    }

@keyframes anShopPastWrap_main {
    0% {
        top: 45px;
        opacity: 0;
    }

    100% {
        top: 32px;
        opacity: 1;
    }
}
.shopPastWrapShow .layui-form {
    display:flex;
    align-items:center;
}
.shopPastWrapShow .layui-form .layui-form-title {
    color:#666;
    font-size:12px;
}
.shopPastWrapShow .layui-form .layui-input-block {
    margin-left:0;
}
.shopPastWrapShow .layui-form .layui-form-item {
    margin-bottom: 8px;
}
.shopPastWrap .shopPastWrapShow .warn-tar {
    position:relative;
}
.shopPastWrap .shopPastWrapShow .warn-tar .warn-tar-title {
    width: 200px;
    display: inline-block;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    box-shadow: 0 0 15px 3px #e2e2e2;
    line-height: 18px;
    background-color: #fff;
    padding: 10px;
    color: #04385d;
    display: none;
    z-index: 10000;
    position: absolute;
    top: -67px;
    left: -10px;
    font-weight: 400;
    font-size: 12px;
    color: #888;
}
.shopPastWrap .shopPastWrapShow .warn-tar:hover .warn-tar-title {
    display:block;
}
    .shopPastWrap .shopPastWrapShow .warn-tar .warn-tar-title::before {
        display: block;
        content: "";
        position: absolute;
        bottom: -8px;
        left: 9px;
        width: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #e2e2e2;
    }

    .shopPastWrap .shopPastWrapShow .warn-tar .warn-tar-title::after {
        display: block;
        content: "";
        position: absolute;
        bottom: -6px;
        left: 9px;
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #fff;
    }
.shopPastWrapShow .warn-tar a {
    color: #3aadff;
    margin-right: 5px;
    display: inline-block;
    line-height: 14px !important;
    padding: 0;
    margin-top: 4px;
    font-size:12px;
}
.shopPastWrap .shopPastWrap-main::after {
    position: absolute;
    display: block;
    content: "";
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #fff;
    top: -7px;
    right: 20px;
}

.shopPastWrap .shopPastWrap-main .shopPastWrap-main-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-size: 12px;
    border-bottom: 1px dotted #e2e2e2;
    cursor: auto;
}

    .shopPastWrap .shopPastWrap-main .shopPastWrap-main-item:last-child {
        border-bottom: unset;
    }

    .shopPastWrap .shopPastWrap-main .shopPastWrap-main-item .shopPastWrap-main-item-left {
        display: flex;
        flex-direction: row;
        width: 220px;
        align-items: center;
    }

    .shopPastWrap .shopPastWrap-main .shopPastWrap-main-item .shopPastWrap-main-item-right {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

        .shopPastWrap .shopPastWrap-main .shopPastWrap-main-item .shopPastWrap-main-item-right .layui-btn-sm {
            font-size: 12px;
        }

    .shopPastWrap .shopPastWrap-main .shopPastWrap-main-item .shopPastWrap-main-item-left .pintaiIcon {
        top: 0;
        margin-right: 5px;
    }

    .shopPastWrap .shopPastWrap-main .shopPastWrap-main-item .shopPastWrap-main-item-left > .shopName {
        color: #565960;
        font-size: 12px;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .shopPastWrap .shopPastWrap-main .shopPastWrap-main-item .shopPastWrap-main-item-right .status {
        font-size: 12px;
    }

    .shopPastWrap .shopPastWrap-main .shopPastWrap-main-item .shopPastWrap-main-item-right .layui-btn {
        margin-left: 15px;
    }

.newVersionsLeftNav.layui-layout-admin .layui-header {
    z-index: 100000001;
}
/* .newVersions-logo{
	padding-left:15px;
} */
.newVersions-logo > img {
    position: relative;
    top: -1px;
}

.newVersions-logo > .icon-layui-icon-shrink-right {
    font-size: 18px;
    transform: rotate(180deg);
    display: inline-block;
}

.newChangeNav-left-down {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

    .newChangeNav-left-down .newChangeNav-left-down-title {
        display: flex;
        flex-direction: column;
        margin-bottom: 8px;
    }

        .newChangeNav-left-down .newChangeNav-left-down-title .newChangeNav-left-down-title-content {
            display: flex;
            justify-content: space-between;
            padding: 0 18px;
            position: relative;
            top: -6px;
        }

            .newChangeNav-left-down .newChangeNav-left-down-title .newChangeNav-left-down-title-content .icon-shezhi2 {
                cursor: pointer;
            }

.newChangeNav-left-down-navs {
    display: flex;
    flex-direction: column;
}

    .newChangeNav-left-down-navs > li.active {
    }
        /* .newChangeNav-left-down-navs>li.active>a .icon-zhuangtai{
	color:#62beff;
} */
        .newChangeNav-left-down-navs > li.active > a > span {
            color: #f29a1a;
        }

    .newChangeNav-left-down-navs > li > a {
        font-size: 12px;
        padding: 3px 10px;
        color: #b9bbc7;
        position: relative;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

        .newChangeNav-left-down-navs > li > a > s {
            font-size: 12px;
            color: #fff;
            position: absolute;
            top: 5px;
            left: 16px;
        }

        .newChangeNav-left-down-navs > li > a > span {
            padding-left: 10px;
            display: inline-block;
            width: 75px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .newChangeNav-left-down-navs > li > a .icon-zhuangtai {
            font-size: 20px;
            color: #f29a1a;
        }

        .newChangeNav-left-down-navs > li > a:hover {
            color: #dadbe3;
            cursor: pointer;
        }

.quickNav-dailog {
    background-color: #fff;
    padding: 15px;
    box-sizing: border-box;
    display: none;
    width: 750px;
    box-sizing: border-box;
}

    .quickNav-dailog .quickNav-main {
        border: 1px solid #e2e2e2;
        padding: 10px;
        display: flex;
        flex-direction: column;
        margin-bottom: 15px;
        min-height: 100px;
        box-sizing: border-box;
    }

        .quickNav-dailog .quickNav-main:last-child {
            margin-bottom: 0;
        }

    .quickNav-dailog .quickNav-main-title {
        padding: 5px 10px 10px 0;
        color: #333333;
        border-bottom: 1px dotted #e2e2e2;
    }

    .quickNav-dailog .quickNav-main-content {
        display: flex;
        flex-direction: column;
        margin-top: 10px;
    }

        .quickNav-dailog .quickNav-main-content .quickNav-main-content-item {
            padding: 10px;
            display: flex;
            flex-direction: row;
            padding-bottom: 0;
        }

            .quickNav-dailog .quickNav-main-content .quickNav-main-content-item .quickNav-main-content-item-tile {
                width: 60px;
                text-align: right;
                margin-right: 15px;
                color: #888;
            }

            .quickNav-dailog .quickNav-main-content .quickNav-main-content-item .quickNav-main-content-item-content {
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                flex: 1;
            }

                .quickNav-dailog .quickNav-main-content .quickNav-main-content-item .quickNav-main-content-item-content > li {
                    display: flex;
                    align-items: center;
                    margin-right: 10px;
                    width: 110px;
                    margin-bottom: 5px;
                }

                    .quickNav-dailog .quickNav-main-content .quickNav-main-content-item .quickNav-main-content-item-content > li > label {
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                    }

                    .quickNav-dailog .quickNav-main-content .quickNav-main-content-item .quickNav-main-content-item-content > li span {
                        margin-left: 3px;
                        color: #2d323e;
                    }

    .quickNav-dailog .quickNav-main .quickNav-main-changede {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding: 0 15px;
        box-sizing: border-box;
        margin-top: 10px;
    }

        .quickNav-dailog .quickNav-main .quickNav-main-changede > li {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            align-items: center;
            padding: 8px 17px;
            color: #2d323e;
            position: relative;
            cursor: move;
            box-shadow: 0 0 2px 1px #f2f2f2;
            margin-right: 10px;
            margin-bottom: 10px;
            border: 1px solid #3aadff;
            color: #3aadff;
            border-radius: 2px;
        }



            .quickNav-dailog .quickNav-main .quickNav-main-changede > li.active {
                animation: operateLasChild 2s ease-in-out 0s 1 alternate forwards;
            }

@keyframes operateLasChild {
    0% {
        box-shadow: 0 0 7px 0px #fbb057;
    }

    50% {
        box-shadow: 0 0 2px 1px #f2f2f2;
    }

    75% {
        box-shadow: 0 0 2px 1px #f2f2f2;
    }

    100% {
        box-shadow: 0 0 2px 1px #f2f2f2;
    }
}

/*.quickNav-dailog .quickNav-main .quickNav-main-changede > li::after {
    position: absolute;
    display: block;
    content: "";
    width: 1px;
    height: 18px;
    top: 10px;
    right: 0;
    background-color: #e2e2e2;
}*/

.quickNav-dailog .quickNav-main .quickNav-main-changede > li .iconfont {
    cursor: pointer;
    color: #3aadff;
    cursor: pointer;
    font-size: 12px;
}

.quickNav-dailog .quickNav-main .quickNav-main-changede > li .leftIcon {
    display: inline-block;
    transform: rotate(90deg);
    margin-right: 10px;
}

.quickNav-dailog .quickNav-main .quickNav-main-changede > li .rightIcon {
    display: inline-block;
    transform: rotate(275deg);
    margin-left: 10px;
}

.my-form-switch {
    position: relative;
    height: 22px;
    line-height: 22px;
    min-width: 35px;
    padding: 0 5px;
    border: 1px solid #d2d2d2;
    border-radius: 20px;
    cursor: pointer;
    background-color: #fff;
    display: inline-block;
    -webkit-transition: .1s linear;
    transition: .1s linear;
}

    .my-form-switch em {
        position: relative;
        top: 0;
        width: 25px;
        margin-left: 21px;
        padding: 0 !important;
        text-align: center !important;
        color: #999 !important;
        font-style: normal !important;
        margin-left: 21px;
        color: #fff;
        font-size: 12px;
        min-width: 38px;
    }

    .my-form-switch i {
        position: absolute;
        left: 5px;
        top: 3px;
        width: 16px;
        height: 16px;
        border-radius: 20px;
        background-color: #d2d2d2;
        -webkit-transition: .1s linear;
        transition: .1s linear;
    }

    .my-form-switch.active {
        border-color: #0888ff;
        background-color: #0888ff;
    }

.switch-active {
    display: none;
}

.my-form-switch.active i {
    left: 100%;
    margin-left: -21px;
    background-color: #fff;
}

.my-form-switch.active em {
    margin-left: 5px;
    margin-right: 21px;
    color: #fff !important;
}

    .my-form-switch.active em.switch-close {
        display: none;
    }

    .my-form-switch.active em.switch-active {
        display: block;
    }

/* 轮播图 */
.w-slider {
    width: 970px;
    background: aliceblue;
    position: relative;
    overflow: hidden;
    background-color: #fff;
}

.slider {
    width: 970px;
    height: 250px;
}

.slider-main {
    width: 2000px;
    height: 250px;
}

/*每个图片盒子用定位*/
.slider-main-img {
    position: absolute;
    top: 0;
    left: 0;
}

.slider-ctrl {
    text-align: center;
}

.slider-ctrl-con {
    width: 24px;
    height: 20px;
    display: inline-block;
    background: url(/Content/images/indexicons.png)no-repeat -24px -782px;
    margin: 0 5px;
    cursor: pointer;
    text-indent: -20em;
    overflow: hidden;
}

.current {
    background-position: -24px -762px;
}

.slider-ctrl-prev,
.slider-ctrl-next {
    position: absolute;
    top: 50%;
    margin-top: -25px;
    width: 30px;
    height: 35px;
    background: url(/Content/images/indexicons.png)no-repeat 6px top;
    cursor: pointer;
}

.slider-ctrl-prev {
    left: 0;
}

.slider-ctrl-next {
    right: 0;
    background-position: -6px -44px;
}

.slider-main-img img {
    width: 970px;
    height: 250px;
    vertical-align: top;
}

.newtopBanner {
    background-color: #fff;
    position: relative;
    width: 100%;
    box-sizing: border-box;
    z-index: 1000;
}

    .newtopBanner .w-slider, .newtopBanner .slider, .newtopBanner .slider-main, .newtopBanner .slider-main-img img {
        width: 1408px;
        height: 60px;
    }

.newVersionsLeftNav .layui-body {
    overflow-y: hidden;
}

.newChangeNav .newChangeNav-right .newChangeNav-right-ul > li > a > span:hover {
    color: #3aadff;
}

.newChangeNav .newChangeNav-left .newChangeNav-left-ul > li > a:hover {
    color: #70c4ff;
}

.newChangeNav-left-down-navs .icon-jia-copy1 {
    transform: rotate(0deg);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    border: 1px dashed #454a56;
    color: #7bc8ff;
    height: 25px;
    margin: 0 28px;
}

.newVersionsLeftNav #header_right .layui-nav-item {
    padding: 0 15px;
}

.newVersionsLeftNav .level-explain .level-iconWrap .level-iconWrap-right {
    font-size: 12px;
}

.newVersionsLeftNav .level-explain .level-iconWrap {
    margin-right: 0;
}

.newVersionsLeftNav .layui-bodys {
    margin-top: 50px;
}

.newChangeNav .newChangeNav-right .newChangeNav-right-ul > li:hover {
    background-color: #ecf5ff;
}

.newChangeNav-left-down .newChangeNav-left-down-title .newChangeNav-left-down-title-content .icon-jia-copy1 {
    transform: rotate(0deg);
    font-weight: 700;
    font-size: 17px;
    color: #3aadff;
}

.newVersionsLeftNav .shopPastWrap-main-item .layui-form {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}

    .newVersionsLeftNav .shopPastWrap-main-item .layui-form .layui-input-block {
        margin-left: 0;
    }

    .newVersionsLeftNav .shopPastWrap-main-item .layui-form .layui-form-item {
        margin-bottom: 0;
    }

    .newVersionsLeftNav .shopPastWrap-main-item .layui-form .layui-form-switch {
        margin-top: 0;
    }

    .newVersionsLeftNav .shopPastWrap-main-item .layui-form .layui-form-title {
        color: #666;
        font-size: 12px;
    }

.newChangeNav .newChangeNav-right .newChangeNav-right-ul > li > a:hover {
    color: #3aadff;
}

.newChangeNav .newChangeNav-right .newChangeNav-right-ul > li.invite-item {
    color: #252931;
    padding: 12px 15px 12px 15px;
}

    .newChangeNav .newChangeNav-right .newChangeNav-right-ul > li.invite-item:hover {
        color: #3aadff;
    }

.newChangeNav .newChangeNav-left .newChangeNav-left-ul > li.active > a:hover {
    color: #fff;
}

body #newtopBanner .top-slider {
    left: 0;
    top: 0;
}

.newVersionsLeftNav .iframewrap {
    left: 115px;
}

.newVersionsLeftNav .layui-layout-iframe-body #LAY_app_body {
    margin-top: -10px;
}

.newVersionsLeftNav #layadmin_flexible {
    display: none !important;
}

.newVersionsLeftNav #header_right .layui-nav-item.level-explain {
    padding: 0;
}

#newChangeNav_left_down_navs .inviteDailog {
    background-color: unset;
}

body .newVersionsLeftNav .top-slider .slider-main-img img {
    width: 1408px;
}

.newtopBanner .icon-jia-copy {
    position: absolute;
    color: #fe6f4f;
    top: 0;
    left: 1385px;
    cursor: pointer;
    transform: rotate(45deg);
    color: #fff;
    font-size: 20px;
}
.newtopBanner .icon-add-fill-hover-copy {
    position: absolute;
    color: #fe6f4f;
    top: 0;
    left: 1385px;
    cursor: pointer;
    transform: rotate(45deg);
    color: #fff;
    font-size: 18px;
    background-color:#fff;
    border-radius:50%;

}
.layui-layout-admin .layui-side {
    z-index:1001;
}
#newChangeNav_right_active .newChangeNav-right-ul .invite-item {
    display: none !important;
}
.newVersionsLeftNav .layui-nav.layui-layout-right .layui-nav-item .iconfont.icon-yaoqing1 {
    font-size: 14px;
    color: #565960;
    font-weight: 700;
    margin-right:0;
}
.inviteWrap-text {
    color: #565960;
    font-size:12px;
}
.aliNewFunDailog {
    left:115px;
}

.layui-nav-item-free-icon {
    width: 28px;
    height: 14px;
    margin-left: 4px;
    margin-bottom: 3px;
}