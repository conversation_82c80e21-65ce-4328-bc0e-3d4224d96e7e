using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web.Mvc;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    public class AccountListController : BaseController
    {
        CommService commService = new CommService();
        CaiNiaoAuthInfoService cNiaoAcountServier = new CaiNiaoAuthInfoService();
        CainiaoAuthOwnerService cainiaoAuthOwnerService = new CainiaoAuthOwnerService();
        UserFxService _userFxService = new UserFxService();
        // GET: AccountList
        public ActionResult Index(string controller)
        {
            //List<WayBillAuthConfig> list = commService.GetAccountAuthInfoList(new List<int> { SiteContext.Current.ExpressTemplateShopId }, false);
            //AccountViewModel avm = GetAccountViewModel(list);
            ////avm.AccountList = list;
            //return View(avm);
            var hasPermission = SiteContext.HasPermission(FxPermission.NewOrderEbillAccount);
            ViewBag.ViewPermission = hasPermission == true ? "true" : "false";
            if (!hasPermission) return View();

            var isShow = new CainiaoAuthOwnerService().IsAnyOldToutiaoCainiaoOwnerInfoId(SiteContext.Current.CurrentShopId);
            ViewBag.isShowOldToutiaoWaybillLink = isShow;
            ViewBag.ShowOldToutiaoWaybillLinkSylte = isShow ? "class" : "";
            new OpenEbillSendTaskService().ExecuteCheckEbillOpenStatusAsync();
            ViewBag.PddIsUsePrintSystemAppForBill = new CommonSettingService().PddIsUsePrintSystemAppForBill();
            ViewBag.IsJingDongTwoApp = _userFxService.CheckFxUserHasOldJingdongAppUnExpired(SiteContext.Current.CurrentFxUserId);
            ViewBag.IsWxVideoOldUser = _userFxService.CheckWxVideoUser(SiteContext.Current.CurrentFxUserId) ? "true" : "false";
            return View();
        }

        public ActionResult LoadList()
        {
            List<WayBillAuthConfig> list = commService.GetAccountAuthInfoList(new List<int> { SiteContext.Current.ExpressTemplateShopId }, false);
            AccountViewModel avm = GetAccountViewModel(list);
            return Json(avm);
        }


        private void GetAuthUrl()
        {
            var token = Convert.ToString(Request["token"]); //当前店铺的token
        }

        public AccountViewModel GetAccountViewModelNew(List<WayBillAuthConfig> list)
        {
            var token = Convert.ToString(Request["token"]); //当前店铺的token
            var toutiaoWaybillAcountAuthUrl = CustomerConfig.AuthCallbackUrl;
            Shop _masterShop = SiteContext.Current.MasterShop;
            var authRequetModel = new AuthRequetModel
            {
                token = token,
                masterToken = token,//AuthController.CreateToken(_masterShop.Id, Request, response: Response),
                newAppUrl = RequestHost,
                UrlEncodeAuthUrl = Server.UrlEncode("http://auth.dgjapp.com/"),
                UrlEncodeAuthSuccessUrl = Server.UrlEncode("http://auth.dgjapp.com/auth/authsuccess"),
                UrlEncodeEntranceUrl = Server.UrlEncode("http://auth.dgjapp.com/auth/entrance?"),
                UrlEncodeTaobaoAuthUrl = Server.UrlEncode("http://auth.dgjapp.com/TaobaoAuth"),
                NeedDbConfig = false,
                NeedAddTemplate = true
            };
            var accountViewModel = new CommService().GetAccountViewModel(authRequetModel);

            var allShops = new List<Shop>();
            allShops.AddRange(SiteContext.Current.AllShops);
            allShops.Add(SiteContext.Current.MasterShop);
            var toutiaoWaybillShops = new List<Shop>();
            //授权信息列表，找出授权店铺，不在当前登录用户店铺群里的店铺
            if (list != null && list.Count > 0)
            { 
                var toutiaoWaybillShopIds = list.Where(x => CustomerConfig.FxDouDianCloudPlatformTypes.Contains(x.Types) == false).Select(x => x.CaiNiaoAuthInfoId).Distinct().ToList();
                if (toutiaoWaybillShopIds.Any())
                {
                    toutiaoWaybillShops = new ShopService().GetShopByIds(toutiaoWaybillShopIds);
                }
                var sids = allShops.Select(f => f.Id);
                var shopIds = new List<int>();

                list.AsParallel().WithDegreeOfParallelism(5).ForAll(s =>
                {
                    if (CustomerConfig.FxDouDianCloudPlatformTypes.Contains(s.Types))
                    {
                        var waybillShop = toutiaoWaybillShops.FirstOrDefault(x => x.Id == s.CaiNiaoAuthInfoId);
                        if (waybillShop != null)
                        {
                            try
                            {
                                waybillShop.IsSyncServierTime = true;
                                waybillShop.ShopExtension = s.ShopEx;
                                var ptService = PlatformFactory.GetPlatformService(waybillShop);
                                s.ServiceEndDate = ptService.GetExpiredTime()?.ToString("yyyy-MM-dd HH:mm:ss") ?? "";
                            }
                            catch (Exception ex)
                            {
                                s.ServiceEndDate = "";
                                Log.WriteError($"店铺【{waybillShop.Id}】获取服务截止时间异常：{ex}");
                            }
                            if (CustomerConfig.FxSystemAppKeyNameDict.ContainsKey(s.AppKey))
                                s.AppName = CustomerConfig.FxSystemAppKeyNameDict[s.AppKey];
                            else
                                s.AppName = "店管家打单";
                        }

                        //s.AuthUrl = AppServiceFactory.GetAppService(s.Types)?.getAppModel(s.AppKey)?.AuthUrl;
                        s.AuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfxnew?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + token;
                        if (waybillShop?.ShopExtension != null)
                        {
                            if (waybillShop.ShopExtension.AppKey == CustomerConfig.TouTiaoFxNewAppKey)
                                s.AuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfxnew?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + token;
                            else
                                s.AuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfx?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + token;
                        }
                    }

                    //店铺群不包含分享的店铺，则另外加载
                    if (sids.Contains(s.ShopId) == false)
                        shopIds.Add(s.ShopId);
                    //此处应将token清除掉，防止泄露到前端
                    s.AppSecret = "";
                    s.SessionKey = "";
                    s.Refresh_Token = "";
                    
                    // 清理ShopEx中的敏感信息
                    s.ShopEx = null;
                });
                if (shopIds.Any())
                {
                    allShops.AddRange((new ShopService()).GetShopByIds(shopIds.Distinct().ToList()));
                }
            }

            allShops.ForEach(s =>
            {
                s.DbConfig = null;
            });
            accountViewModel.AccountList = list;
            return accountViewModel;
        }


        public AccountViewModel GetAccountViewModel(List<WayBillAuthConfig> list)
        {
            return GetAccountViewModelNew(list);


            var token = Convert.ToString(Request["token"]); //当前店铺的token
            var newAppUrl = RequestHost;
            var taobaoAccountRedirectUrl = ConfigurationManager.AppSettings["Taobao:Auth_RedirectUrl"];
            var pinDuoDuoWaybillAcountAuthUrl = "http://testauth.dgjapp.com/fxauth/pdd";// CustomerConfig.PinduoduoOldSystemLink;
            var pinDuoDuoDadanWaybillAcountAuthUrl = CustomerConfig.PinduoduoOldSystemLink;
            var toutiaoWaybillAcountAuthUrl = CustomerConfig.AuthCallbackUrl;
            //if (CustomerConfig.IsDebug)
            //{
            //    toutiaoWaybillAcountAuthUrl = "http://testauth1.dgjapp.com";
            //}
            var authSiteUrl = CustomerConfig.AuthCallbackUrl;
            Shop _shops = SiteContext.Current.CurrentLoginShop;

            //授权给主店铺的链接
            string taobaoAuthPriUrl = "", //top菜鸟
                cainiaoAuthPriUrl = "",  //菜鸟官方
                toutiaoAuthPriUrl = "", //头条
                pinDuoDuoAuthPriUrl = string.Empty, //pdd分单应用
                pinDuoDuoDdAuthPriUrl = string.Empty, //pdd打单应用
                kuaishouAuthPriUrl = "";  //快手电子面单;

            //授权给当前店铺的链接
            string taobaoAuthUrl = "", //top菜鸟
                cainiaoAuthUrl = "", //菜鸟官方
                toutiaoAuthUrl = "", //头条新应用
                toutiaoOldAuthUrl = "", // 头条旧应用（铺货助手）
                pinDuoDuoAuthUrl = string.Empty, //pdd分单应用
                pinDuoDuoDdAuthUrl = string.Empty, //pdd打单应用
                kuaishouAuthUrl = "";//快手电子面单

            Shop _masterShop = SiteContext.Current.MasterShop;
            var masterToken = string.Empty;
            if (_masterShop != null && _masterShop.Id != _shops.Id)
            {
                taobaoAuthPriUrl = taobaoAccountRedirectUrl + "tbAuth.aspx?callurl=" + Server.UrlEncode("http://auth.dgjapp.com/TaobaoAuth") + "&SuccToUrl=" + Server.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&channel=newprint&print_loginid=" + _shops.ShopId + "&shopid=" + _shops.Id + "&is_pri=true&linktoken=" + token + "&newappurl=" + newAppUrl;
                cainiaoAuthPriUrl = CustomerConfig.CaiNiaoRedirectUrl + "/Auth/CaiNiaoCloud?IsShowSuccess=1&channel=newprint&print_loginid=" + _shops.ShopId + "&shopid=" + _shops.Id + "&is_pri=true&linktoken=" + token + "&newappurl=" + Server.UrlEncode("http://auth.dgjapp.com/");//+ newAppUrl;

                var masterLoginAuthToken = (new ShopService()).GetTokenByShopId(_masterShop.Id);
                if (masterLoginAuthToken != null)
                {
                    masterToken = token;// DES.EncryptUrl($"{masterLoginAuthToken.Id}", CustomerConfig.LoginCookieEncryptKey);
                }
                {
                    masterToken = token;// AuthController.CreateToken(_masterShop.Id, Request, response: Response);
                }
                pinDuoDuoAuthPriUrl = pinDuoDuoWaybillAcountAuthUrl + "?SuccToUrl=" + Server.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&rp=" + masterToken;
                pinDuoDuoDdAuthPriUrl = pinDuoDuoDadanWaybillAcountAuthUrl + "?callurl=" + Server.UrlEncode("http://auth.dgjapp.com/auth/entrance?") + "&SuccToUrl=" + Server.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&rp=" + masterToken;
                toutiaoAuthPriUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfxnew?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + masterToken;
                toutiaoOldAuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfx?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + masterToken;
                kuaishouAuthPriUrl = authSiteUrl.TrimEnd('/') + "/auth/kuaishou?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + masterToken;
            }
            taobaoAuthUrl = taobaoAccountRedirectUrl + "tbAuth.aspx?callurl=" + Server.UrlEncode("http://auth.dgjapp.com/TaobaoAuth") + "&SuccToUrl=" + Server.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&channel=newprint&print_loginid=" + _shops.ShopId + "&shopid=" + _shops.Id + "&is_pri=false&linktoken=" + token + "&newappurl=" + newAppUrl;
            cainiaoAuthUrl = CustomerConfig.CaiNiaoRedirectUrl + "/Auth/CaiNiaoCloud?IsShowSuccess=1&channel=newprint&print_loginid=" + _shops.ShopId + "&shopid=" + _shops.Id + "&is_pri=false&linktoken=" + token + "&newappurl=" + Server.UrlEncode("http://auth.dgjapp.com/");// + newAppUrl;
            pinDuoDuoAuthUrl = pinDuoDuoWaybillAcountAuthUrl + "?SuccToUrl=" + Server.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&rp=" + token;
            pinDuoDuoDdAuthUrl = pinDuoDuoDadanWaybillAcountAuthUrl + "?callurl=" + Server.UrlEncode("http://auth.dgjapp.com/auth/entrance?") + "&SuccToUrl=" + Server.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&rp=" + token;
            toutiaoAuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfxnew?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + token;
            toutiaoOldAuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfx?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + masterToken;
            kuaishouAuthUrl = authSiteUrl.TrimEnd('/') + "/fxauth/kuaishou?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + token;

            var allShops = new List<Shop>();
            allShops.AddRange(SiteContext.Current.AllShops);
            allShops.Add(SiteContext.Current.MasterShop);

            var toutiaoWaybillShops = new List<Shop>();
            //授权信息列表，找出授权店铺，不在当前登录用户店铺群里的店铺
            if (list != null && list.Count > 0)
            {
                var toutiaoWaybillShopIds = list.Where(x => x.Types == PlatformType.TouTiao.ToString()).Select(x => x.CaiNiaoAuthInfoId).Distinct().ToList();
                if (toutiaoWaybillShopIds.Any())
                {
                    toutiaoWaybillShops = new ShopService().GetShopByIds(toutiaoWaybillShopIds);
                }
                var sids = allShops.Select(f => f.Id);
                var shopIds = new List<int>();
                list.ForEach(s =>
                {
                    if (s.Types == PlatformType.TouTiao.ToString())
                    {
                        var waybillShop = toutiaoWaybillShops.FirstOrDefault(x => x.Id == s.CaiNiaoAuthInfoId);
                        if (waybillShop != null)
                        {
                            try
                            {
                                waybillShop.ShopExtension = s.ShopEx;
                                var ptService = PlatformFactory.GetPlatformService(waybillShop);
                                s.ServiceEndDate = ptService.GetExpiredTime()?.ToString("yyyy-MM-dd HH:mm:ss") ?? "";
                            }
                            catch (Exception ex)
                            {
                                s.ServiceEndDate = "";
                                Log.WriteError($"店铺【{waybillShop.Id}】获取服务截止时间异常：{ex}");
                            }
                            if (CustomerConfig.FxSystemAppKeyNameDict.ContainsKey(s.AppKey))
                                s.AppName = CustomerConfig.FxSystemAppKeyNameDict[s.AppKey];
                            else
                                s.AppName = "店管家打单";
                        }

                        //s.AuthUrl = AppServiceFactory.GetAppService(s.Types)?.getAppModel(s.AppKey)?.AuthUrl;
                        s.AuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfxnew?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + token;
                        if (waybillShop.ShopExtension != null)
                        {
                            if (waybillShop.ShopExtension.AppKey == CustomerConfig.TouTiaoFxNewAppKey)
                                s.AuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfxnew?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + token;
                            else
                                s.AuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfx?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + token;
                        }
                    }

                    //店铺群不包含分享的店铺，则另外加载
                    if (sids.Contains(s.ShopId) == false)
                        shopIds.Add(s.ShopId);
                    //此处应将token清除掉，防止泄露到前端
                    s.AppSecret = "";
                    s.SessionKey = "";
                    s.Refresh_Token = "";
                });
                if (shopIds.Any())
                {
                    allShops.AddRange((new ShopService()).GetShopByIds(shopIds.Distinct().ToList()));
                }
            }

            allShops.ForEach(s =>
            {
                s.DbConfig = null;
            });


            var jdAuthUrl = $"{CustomerConfig.JdAuthCallbackUrl.TrimEnd('/')}/auth/jingdong";
            AccountViewModel avm = new AccountViewModel()
            {
                AccountList = list,
                TaobaoAuthPriUrl = taobaoAuthPriUrl,
                TaobaoAuthUrl = taobaoAuthUrl,
                CaiNiaoAuthPriUrl = cainiaoAuthPriUrl,
                CaiNiaoAuthUrl = cainiaoAuthUrl,
                PinduoduoAuthPriUrl = pinDuoDuoAuthPriUrl,
                PinduoduoAuthUrl = pinDuoDuoAuthUrl,

                PinduoduoDdAuthPriUrl = pinDuoDuoDdAuthPriUrl,
                PinduoduoDdAuthUrl = pinDuoDuoDdAuthUrl,

                KuaiShouAuthPriUrl = kuaishouAuthPriUrl,
                KuaiShouAuthUrl = kuaishouAuthUrl,

                ToutiaoAuthPriUrl = toutiaoAuthPriUrl,
                ToutiaoAuthUrl = toutiaoAuthUrl,
                ToutiaoOldAuthUrl = toutiaoOldAuthUrl,

                JdAuthUrl = jdAuthUrl,
                MasterToken = masterToken ?? "",
                Token = token,
                AllShops = allShops?.Select(x => new { x.Id, x.NickName, x.ShopId, x.ShopName }).ToList(),
            };

            return avm;
        }

        /// <summary>
        /// 供添加模板弹窗里添加授权使用
        /// </summary>
        /// <returns></returns>
        public ActionResult GetAuthUrls()
        {
            var avm = GetAccountViewModel(null);
            return Json(avm);
        }

        public ActionResult Relieve(int cainiaoAuthId, int authSourceType, int shopId)
        {

            string res = "1";
            try
            {
                cainiaoAuthOwnerService.RelieveAcount(cainiaoAuthId, authSourceType, shopId);
            }
            catch
            {
                res = "2";
            }

            return Content(res);
        }


        public ActionResult BranchPastLink()
        {
            var taobaoAuthUrl = "";
            try
            {
                var token = Convert.ToString(Request["token"]);
                var newAppUrl = CustomerConfig.ApplicationWebUrl;

                var taobaoAccountRedirectUrl = ConfigurationManager.AppSettings["Taobao:Auth_RedirectUrl"];
                Shop _shops = SiteContext.Current.CurrentLoginShop;


                taobaoAuthUrl = taobaoAccountRedirectUrl + "tbAuth.aspx?channel=newprint&print_loginid=" + _shops.ShopId + "&shopid=" + _shops.Id + "&is_pri=false&linktoken=" + token + "&newappurl=" + newAppUrl;


            }
            catch (Exception ex)
            {

            }


            return SuccessResult(new { taobaoAuthUrl = taobaoAuthUrl });
        }


    }
}