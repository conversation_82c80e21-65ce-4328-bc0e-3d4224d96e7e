using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Threading;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.ErpWeb
{
    public class BaseController : DianGuanJiaApp.Controllers.BaseController
    {
        /// <summary>
        /// 发往其他云平台站点更新配置
        /// </summary>
        /// <param name="cloudPtApiUrl"></param>
        /// <param name="setting"></param>
        /// <returns></returns>
        protected bool SendToOtherCloudPlatform(string cloudPtApiUrl, CommonSetting setting)
        {
            var result = false;
            var retryTimes = 3;

            //记录请求日志
            var logView = new InvokeApiDataLogModel
            {
                BatchId = Guid.NewGuid().ToString(),
                RequestContent = setting.ToJson(),
                ShopId = setting.ShopId,
                StartTime = DateTime.Now,
                Url = cloudPtApiUrl,
                Status = "发送端"
            };
            InvokeApiDataTrackingService.Instance.WriteLog(logView);

            for (var i = 0; i < retryTimes; i++)
            {
                try
                {
                    result = Common.PostFxSiteApi<CommonSetting, bool>(
                        cloudPtApiUrl,
                        SiteContext.Current.CurrentFxUserId,
                        setting,
                        "分单系统分发到其他云平台站点更新配置");
                    //发送端-请求完成
                    logView.ResponseContent = result.ToJson();
                    logView.Status = "发送端-请求完成";
                    InvokeApiDataTrackingService.Instance.WriteLog(logView);
                    break;
                }
                catch (Exception ex)
                {
                    ExceptionLogDataEventTrackingService.Instance.WriteLog(ex,
                        $"{GetType().Name}，追踪编号：{logView?.BatchId}");
                    if (i >= retryTimes - 1)
                        throw ex;
                    Thread.Sleep(100 * i);
                }
            }
            return result;
        }
        protected string GetFenDanCloudPlatformUrl(string cloudPlatformType)
        {
            //分销云平台地址
            string url;
            switch (cloudPlatformType.ToLower())
            {
                case "pinduoduo":
                    url = CustomerConfig.PinduoduoFenFaSystemUrl;
                    break;
                case "jingdong":
                    url = CustomerConfig.JingdongFenFaSystemUrl;
                    break;
                case "toutiao":
                    url = CustomerConfig.ToutiaoFenFaSystemUrl;
                    break;
                case "tiktok":
                    url = CustomerConfig.TikTokFenFaSystemUrl;
                    break;
                default:
                    url = CustomerConfig.AlibabaFenFaSystemUrl;
                    break;
            }
            return url;
        }

        /// <summary>
        /// 获取用户的店铺
        /// </summary>
        /// <param name="defaultFxUserId"></param>
        /// <param name="isAllPlats">是否获取所有平台的店铺</param>
        /// <returns></returns>
        protected List<FxUserShop> GetFxUserShops(int defaultFxUserId = -1, bool isAllPlats = false)
        {
            var fxUserId = defaultFxUserId;
            if (fxUserId == -1)
            {
                fxUserId = SiteContext.Current.CurrentFxUserId;
            }
            var fxUserShopService = new FxUserShopService();

            //店铺
            var reqModel = new FxUserShopQueryModel { FxUserId = fxUserId };
            var fxUserShops = fxUserShopService.GetList(reqModel)?.Item2 ?? new List<FxUserShop>();
            fxUserShops = fxUserShops.Where(x => x.Status != FxUserShopStatus.UnBind).ToList();

            if (CustomerConfig.IsCrossBorderSite)
            {
                var foreignShops = new FxUserForeignShopService().GetList(reqModel)?.Item2 ?? new List<FxUserForeignShop>();
                fxUserShops.AddRange(foreignShops);
                return foreignShops.Cast<FxUserShop>().ToList();
            }
            if (isAllPlats)
                return fxUserShops;

            if (CustomerConfig.CloudPlatformType == PlatformType.Pinduoduo.ToString())
                fxUserShops = fxUserShops.Where(x =>
                    CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(x.PlatformType) ||
                    x.PlatformType == PlatformType.Virtual.ToString()).ToList();
            else if (CustomerConfig.CloudPlatformType == PlatformType.Jingdong.ToString())
                fxUserShops = fxUserShops.Where(x =>
                    x.PlatformType == PlatformType.Virtual.ToString() ||
                    CustomerConfig.FxJingDongCloudPlatformTypes.Contains(x.PlatformType)).ToList();
            else if (CustomerConfig.CloudPlatformType == PlatformType.TouTiao.ToString())
                fxUserShops = fxUserShops.Where(x =>
                    CustomerConfig.FxDouDianCloudPlatformTypes.Contains(x.PlatformType) ||
                    x.PlatformType == PlatformType.Virtual.ToString()).ToList();
            else
            {
                var aliFilterPlats = new List<string>();
                aliFilterPlats.AddRange(CustomerConfig.FxDouDianCloudPlatformTypes);
                aliFilterPlats.AddRange(CustomerConfig.FxJingDongCloudPlatformTypes);
                aliFilterPlats.AddRange(CustomerConfig.FxPinduoduoCloudPlatformTypes);

                fxUserShops = fxUserShops.Where(x => !aliFilterPlats.Contains(x.PlatformType)).ToList();

            }
            return fxUserShops;
        }

        /// <summary>
        /// 向其他云平台更新配置，除了当前云平台
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="shopId"></param>
        protected void SendToExceptCurrentCloud(string key, string value, int shopId)
        {
            var apiUrl = "/CommonApi/SaveCommonSetting";
            var aliCloudHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/") + apiUrl;
            var pddCloudHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/") + apiUrl;
            var ttCloudHost = CustomerConfig.ToutiaoFenFaSystemUrl.TrimEnd("/") + apiUrl;
            var jdCloudHost = CustomerConfig.JingdongFenFaSystemUrl.TrimEnd("/") + apiUrl;

            var setting = new CommonSetting { Key = key, Value = value, ShopId = shopId };

            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
            {
                apiUrl = "/BaseProductApi/SaveCommonSettingForPdd";
                aliCloudHost = CustomerConfig.AlibabaMessageDomainForPdd.TrimEnd("/") + apiUrl;
                SendToOtherCloudPlatform(aliCloudHost, setting);
            }
            else
            {
                //分单系统主站点在阿里云，所有分发到多多云和京东云、抖店云
                if (CustomerConfig.CloudPlatformType != PlatformType.Alibaba.ToString() && !string.IsNullOrEmpty(CustomerConfig.AlibabaFenFaSystemUrl))
                {
                    SendToOtherCloudPlatform(aliCloudHost, setting);
                }
                if (CustomerConfig.CloudPlatformType != PlatformType.Pinduoduo.ToString() && !string.IsNullOrEmpty(CustomerConfig.PinduoduoFenFaSystemUrl))
                {
                    SendToOtherCloudPlatform(pddCloudHost, setting);
                }
                if (CustomerConfig.CloudPlatformType != PlatformType.TouTiao.ToString() && !string.IsNullOrEmpty(CustomerConfig.ToutiaoFenFaSystemUrl))
                {
                    SendToOtherCloudPlatform(ttCloudHost, setting);
                }
                if (CustomerConfig.CloudPlatformType != PlatformType.Jingdong.ToString() && !string.IsNullOrEmpty(CustomerConfig.JingdongFenFaSystemUrl))
                {
                    SendToOtherCloudPlatform(jdCloudHost, setting);
                }
            }
        }

        /// <summary>
        /// 验证配置key是否有非法字符（未配置非法字符，则不检测）
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        protected bool ValidCommonSettingKeyContainsIllegalCharacters(string key)
        {
            //判空处理
            if (string.IsNullOrWhiteSpace(key))
            {
                return true;
            }
            //获取非法字符
            var settingKeyIllegalCharacters =
                new CommonSettingService().GetCommonSettingKeyIllegalCharactersWithCache();
            if (settingKeyIllegalCharacters == null || settingKeyIllegalCharacters.Any() == false)
            {
                return false;
            }
            //是否存在非法字符
            return settingKeyIllegalCharacters.Any(m => key.ToLower().Contains(m.ToLower()));
        }

        protected string EntryVerification()
        {
            if (!ModelState.IsValid)
            {
                var firstError = ModelState.Where(ms => ms.Value.Errors.Count > 0).SelectMany(ms => ms.Value.Errors).FirstOrDefault();
                return firstError?.ErrorMessage;
            }

            return null;
        }
    }
}