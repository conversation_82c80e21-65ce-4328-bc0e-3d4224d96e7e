using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using NPOI.SS.UserModel;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    public class ExpressBillController : BaseController
    {
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private ExportTaskService _exportTaskService = new ExportTaskService();

        [FxAuthorize(FxPermission.ExpressBillIndex)]
        public ActionResult Index()
        {
            var currShopId = SiteContext.Current.CurrentShopId;

            var keys = new List<string> { "/ErpExpressBill/WaybillCode/UpdateTime" };
            var commSets = _commonSettingService.GetSets(keys, currShopId);
            //最后导出时间
            var exportUpdateTimeSet = commSets.FirstOrDefault(m => m.Key == "/ErpExpressBill/WaybillCode/UpdateTime");
            var defaultExportUpdateTime = exportUpdateTimeSet?.Value.ToDateTime() ?? null;
            var defaultExportUpdateTimeSetVal = defaultExportUpdateTime == null ? "" : defaultExportUpdateTime.Value.ToString("yyyy-MM-ddTHH:mm:ss");
            // 默认导出间隔时长（300s）
            var defaultExportExpireSecondsSet = _commonSettingService.Get("/ErpExpressBill/WaybillCode/ExpireSeconds", 0);
            var defaultExportExpireSeconds = defaultExportExpireSecondsSet?.Value.ToInt() ?? 0;
            defaultExportExpireSeconds = defaultExportExpireSeconds <= 0 ? 300 : defaultExportExpireSeconds;

            //前台获取导出任务
            var exportTask = _exportTaskService.GetExportTask(currShopId, ExportType.ErpExpressBill.ToInt());
            // 显示未完成任务或已完成一天前的导出任务
            exportTask = exportTask != null && ((exportTask.Status >= 0 && exportTask.Status < 4) || (exportTask.Status >= 4 && exportTask.UploadToServerTime != null && DateTime.Now < exportTask.UploadToServerTime.Value.AddDays(1))) ? exportTask : null;
            var task = GetExportTaskToWeb(exportTask);

            ViewBag.ExportUpdateTimeSet = defaultExportUpdateTimeSetVal;
            ViewBag.ExportExpireSeconds = defaultExportExpireSeconds;
            ViewBag.ExpressBillTask = task?.ToJson() ?? "null";
            return View();
        }

        [LogForOperatorFilter("快递对账")]
        public ActionResult SaveFileNew(string fileDirectory)
        {
            if (string.IsNullOrWhiteSpace(fileDirectory))
            {
                fileDirectory = "Files/Temp";
            }
            var path = $"/{fileDirectory.TrimStart('/').TrimEnd('/')}/";
            var directory = Server.MapPath("~" + path);

            if (System.IO.Directory.Exists(directory) == false)
            {
                System.IO.Directory.CreateDirectory(directory);
            }

            if (Request.Files.Count > 0)
            {
                var file = Request.Files[0];
                if (!file.FileName.EndsWith(".xls") && !file.FileName.EndsWith(".xlsx"))
                {
                    return FalidResult("只能上传Excel文件");
                }

                var fileName = (DateTime.Now.ToString("yyyyMMddHHmmssfff") + file.FileName);

                file.SaveAs(directory + fileName);

                var filePath = directory + fileName;
                if (filePath.IsNullOrEmpty() || !System.IO.File.Exists(filePath))
                {
                    return FalidResult("文件未找到");
                }

                var serverFilePath = CustomerConfig.UploadFileToFileServer(filePath);
                if (serverFilePath.IsNullOrEmpty())
                    return FalidResult("文件上传至服务器失败");

                var dbname = Request["dbname"].ToString2();
                var shopIds = new List<int>() { SiteContext.Current.CurrentShopId }; //SiteContext.Current.AllShops.Select(x => x.Id).ToList(); 无需所店铺id，超长会报错
                var curShopId = SiteContext.Current.CurrentShopId;
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                //使用任务方式导出
                var newTask = new ExportTask
                {
                    IP = Request.UserHostAddress,
                    CreateTime = DateTime.Now,
                    PlatformType = "Alibaba", //固定在精选平台进行快递对账
                    ShopId = curShopId,
                    UserId = fxUserId.ToString(),
                    Status = 0,
                    Type = ExportType.ErpExpressBill.ToInt(),
                    PageIndex = 1,
                    PageSize = 1000,
                    TotalCount = 10000, //预设总数，任务开始时再获取实际数量
                    ParamJson = serverFilePath, //将文件暂存到服务器
                    ExtField1 = shopIds.ToJson(),
                    ExtField3 = file.FileName,
                    FromModule = "分单系统快递对账",
                    ExtField5 = dbname,
                };
                newTask.HopeExecuteTime = new CommService().ExportExecuteCheck(newTask.UserId.ToInt(), newTask.Type);
                newTask.Id = _exportTaskService.Add(newTask);

                // 删除Web站点临时文件
                System.IO.File.Delete(filePath);

                return SuccessResult("导出任务创建成功", GetExportTaskToWeb(newTask));
            }
            else
            {
                return FalidResult("未读取到文件");
            }
        }

        [LogForOperatorFilter("快递对账导出")]
        public ActionResult ExportExcel()
        {
            var log = LogForOperatorContext.Current.logInfo;
            try
            {
                var curShopId = SiteContext.Current.CurrentShopId;
                string options = Request.Form["options"].ToString2();
                options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
                options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果

                var waybillCodeList = options.ToList<WaybillCode>();
                var fileName = ExcelHelper.GetFileName("快递对账.xlsx", Request);

                if (waybillCodeList == null || !waybillCodeList.Any())
                    return FalidResult("无对账数据，不需要导出");

                var workbook = BuildExcel(waybillCodeList, fileName);
                Response.Cookies.Add(new HttpCookie("downloadToken", Request.Form["downloadToken"].ToString2()));
                var rootPath = Server.MapPath("../Files") + $"\\快递对账-{curShopId}-{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx";
                using (var fs = new FileStream(rootPath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(fs);
                }
                var memoryStream = new MemoryStream();
                using (var fileStream = new FileStream(rootPath, FileMode.Open))
                {
                    fileStream.CopyTo(memoryStream);
                }
                memoryStream.Position = 0;
                if (System.IO.File.Exists(rootPath))
                    System.IO.File.Delete(rootPath);
                return File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                log.Exception = ex.ToString();
                throw ex;
            }
        }

        private IWorkbook BuildExcel(List<WaybillCode> waybillCodeLst, string fileName)
        {
            var platformType = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (platformType == PlatformType.Pinduoduo.ToString())
            {
                try
                {
                    //var tempOrders = waybillCodeLst?.Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ToAddress }).ToList();
                    var tempOrders = new List<Order>();
                    foreach (var x in waybillCodeLst)
                    {
                        if (x == null) continue;
                        var order = new Order
                        {
                            PlatformOrderId = x.OrderId.ToString2(),
                            ShopId = x.ShopId.ToInt(),
                            ToName = x.Reciver.ToString2(),
                            ToMobile = x.ReciverPhone.ToString2(),
                            ToProvince = x.ToProvince.ToString2(),
                            ToCity = x.ToCity.ToString2(),
                            ToCounty = x.ToDistrict.ToString2(),
                            ToAddress = x.ToAddress.ToString2()
                        };
                        tempOrders.Add(order);
                    }

                    BranchShareRelationService.TryToDecryptPddOrders(tempOrders, true);
                    //按店铺分组
                    waybillCodeLst.ForEach(item =>
                    {
                        var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId.ToString2() && x.ShopId == item.ShopId.ToInt());
                        if (decryptedOrder != null)
                        {
                            item.Reciver = decryptedOrder.ToName;
                            item.ReciverPhone = decryptedOrder.ToMobile;
                            item.BuyerMemberName = item.Reciver;
                            item.BuyerMemberId = item.Reciver;
                            item.ToAddress = decryptedOrder.ToFullAddress;
                        }
                    });
                    waybillCodeLst.ForEach(o =>
                    {
                        EncryptReceiverInfo(o);
                    });
                }
                catch (Exception ex)
                {
                    Log.WriteError($"拼多多收件人信息解密失败：{ex}");
                    throw;
                }
            }

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("快递对账");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            var heads = new Dictionary<string, string>() {
                { "匹配情况" ,"Result"},
                { "快递" ,"ExpressName"},
                { "快递单号" ,"ExpressWayBillCode"},
                { "省份" ,"ToProvince"},
                { "重量","TotalWeight"},
                { "订单金额","TotalPayAomount"},
                { "商品数量","ProductCount"},
                { "店铺名称","TemplateName"},
                { "买家旺旺","BuyerMemberName"},
                { "收件人","Reciver"},
                { "状态","Status"},
            };
            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });


            int rowIndex = 1;
            waybillCodeLst.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;
                var dic = (model ?? new WaybillCode()).ToDictionary();

                colIndex = 0;
                foreach (var item in heads.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2() ?? "";
                    if (dic?.ContainsKey(key) == true)
                    {
                        var val = dic[key]?.ToString() ?? "";
                        if (key == "Status")
                        {
                            var text = val == "1" ? "已打印" :
                                       val == "2" ? "已回收" :
                                       val == "3" ? "已发货" : "未知";
                            dataRow.CreateCell(colIndex).SetCellValue(text);
                        }
                        else
                            dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                    }
                    else
                    {
                        if (key == "Result")
                        {
                            var val = model.ID == 0 ? "未找到" : "找到";
                            val = val + (model.PrintDataType > 0 ? $" - 重复({model.PrintDataType})" : "");
                            dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                        }
                    }
                    dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                    colIndex++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;
            });

            return workbook;
        }

        private WaybillCode EncryptReceiverInfo(WaybillCode order)
        {
            order.BuyerMemberName = order.BuyerMemberName.ToEncryptName();
            order.BuyerMemberId = order.BuyerMemberId.ToEncryptName();
            order.ReciverPhone = order.ReciverPhone.ToPddEncryptPhone();
            order.Reciver = order.Reciver.ToEncryptName();
            order.ToAddress = order.ToAddress.ToPddEncryptAddress();
            //order.ToDistrict = "****";
            return order;
        }

        private void SetColumnWidth(ISheet sheet, string headName, int index)
        {
            int width = 20 * 256;
            //if (headName == "序号" || headName == "商品数量")
            //    width = 10 * 256;
            //else if (headName == "省份" || headName == "重量" || headName == "订单金额")
            //    width = 16 * 256;
            //else if (headName == "快递单号" || headName == "收件人姓名" || headName == "发件人" || headName == "发货时间" || headName == "打单时间" || headName == "状态" || headName == "收件人电话")
            //    width = 20 * 256;
            //else if (headName == "快递公司" || headName == "订单编号" || headName == "买家旺旺" || headName == "店铺名称")
            //    width = 25 * 256;
            //else if (headName == "详细地址" || headName == "买家留言" || headName == "卖家备注")
            //    width = 35 * 256;
            //else if (headName == "发货内容")
            //    width = 45 * 256;
            sheet.SetColumnWidth(index, width);
        }

        private ICellStyle GetHeadStyle(IWorkbook workbook)
        {
            IFont font = workbook.CreateFont();
            font.FontName = "Times New Roman";
            font.Boldweight = short.MaxValue;
            font.FontHeightInPoints = 11;

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(font);
            headerStyle.Alignment = HorizontalAlignment.Center;//内容居中显示
            headerStyle.WrapText = true;

            headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightOrange.Index;
            headerStyle.FillPattern = FillPattern.SolidForeground;
            return headerStyle;
        }

        private ICellStyle GetContentStyle(IWorkbook workbook, HorizontalAlignment alignment)
        {
            IFont font = workbook.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = workbook.CreateCellStyle();
            contentStyle.SetFont(font);
            contentStyle.Alignment = alignment;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.WrapText = true;

            return contentStyle;
        }

    }
}