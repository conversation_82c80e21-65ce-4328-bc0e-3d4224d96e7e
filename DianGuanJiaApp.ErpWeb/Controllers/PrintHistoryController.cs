
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.IO;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.ErpWeb.Models;
using DianGuanJiaApp.Utility.NPOI;
using NPOI.SS.UserModel;
using DianGuanJiaApp.Utility.Web;
using DianGuanJiaApp.Models;
using System.Web.UI;
using System.Collections.Concurrent;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    public class PrintHistoryController : BaseController
    {
        private PrintHistoryService _service;
        private WaybillCodeService _wayBillCodeService = new WaybillCodeService();
        private PrintTemplateService _printTemplateService = new PrintTemplateService();
        private ExportTaskService _exportTaskService = new ExportTaskService();

        public ActionResult Index()
        {
            var _commonSettingService = new CommonSettingService();
            int loginShopId = SiteContext.Current.CurrentShopId;

            //前台获取导出任务
            //var taskType = ExportType.ErpPrintHistory.ToInt();
            //var exportTask = new ExportTaskService().GetErpWebExportTaskSharding(loginShopId, CustomerConfig.CloudPlatformType, taskType, "");
            // 显示未完成任务或已完成一天前的导出任务
            //exportTask = exportTask != null && ((exportTask.Status >= 0 && exportTask.Status < 4) || (exportTask.Status >= 4 && exportTask.UploadToServerTime != null && DateTime.Now < exportTask.UploadToServerTime.Value.AddDays(1))) ? exportTask : null;
            //var task = GetExportTaskToWeb(exportTask);
            //var taskJson = task?.ToJson();
            //ViewBag.PrintHistoryExportTask = taskJson.IsNullOrEmpty() ? null : taskJson;

            // 默认导出间隔时长（30s）
            var exportExpireSecond = _commonSettingService.Get("/ErpWeb/ExportOrder/ExpireSeconds", 0)?.Value.ToInt() ?? 0;
            ViewBag.ExportExpireSeconds = exportExpireSecond <= 0 ? 30 : exportExpireSecond;

            var exportUpdateTimeSet = _commonSettingService.Get("/Export/PrintHistory/ExportUpdateTime", loginShopId);
            ViewBag.ExportUpdateTimeSet = exportUpdateTimeSet.IsNullOrEmpty() ? null : exportUpdateTimeSet.toDateTime().ToString("yyyy-MM-ddTHH:mm:ss");
            ViewBag.TemplateData = LoadTemplateNames().ToJson();
            return View();
        }
        public ActionResult SecondSendList()
        {
            ViewBag.TemplateData = LoadTemplateNames().ToJson();
            return View();
        }

        /// <summary>
        /// 加载发往的省份
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadProvinces()
        {
            var shopIds = new List<int> { SiteContext.Current.CurrentShopId };
            _service = new PrintHistoryService(SiteContext.Current.CurrentFxUserId);
            var provinces = _service.GetToProvinces(shopIds);
            var selectItemList = new List<SelectListItem>() { new SelectListItem() { Text = "==所有省份==", Value = "0" } };
            if (provinces != null && provinces.Count > 0)
            {
                provinces.ForEach(item =>
                {
                    if (string.IsNullOrWhiteSpace(item) == false)
                        selectItemList.Add(new SelectListItem() { Text = item, Value = item });
                });
            }
            return Json(selectItemList);
        }

        /// <summary>
        /// 加载打印过的模板名称
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("打印记录查询模板条件")]
        public ActionResult LoadTemplateNames()
        {
            int shopId = SiteContext.Current.CurrentShopId;
            var dataList = _printTemplateService.GetTemplateNamesByShopId(shopId);
            var selectItemList = new List<SelectListItemViewModel>() { new SelectListItemViewModel() { Text = "=====所有模板=====", Value = "0" } };
            if (dataList != null && dataList.Count > 0)
            {
                dataList.ForEach(item =>
                {
                    selectItemList.Add(new SelectListItemViewModel() { Text = item.TemplateName, Value = item.Id.ToString(), TemplateType = item.TemplateType });
                });
            }
            return Json(selectItemList);
        }


        /// <summary>
        /// 加载买家数/订单数
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("打印记录查询数据统计")]
        public ActionResult LoadStatisticsCount(PrintHistoryRequestModel requestModel)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _service = new PrintHistoryService(SiteContext.Current.CurrentFxUserId);
            requestModel.FxUserId = fxUserId;
            var isPdd = Request["Pt"]?.ToLower() == PlatformType.Pinduoduo.ToString().ToLower();
            var countResult = _service.StatisticsCount(requestModel, isPdd);
            var result = new { BuyerCount = countResult.Item1, LogicOrderCount = countResult.Item2, OrderCount = countResult.Item3 };
            return Json(result);
        }

        /// <summary>
        /// 加载列表数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        [LogForOperatorFilter("打印记录查询列表")]
        [PageDepthControlFilter]
        public ActionResult LoadList(PrintHistoryRequestModel requestModel)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _service = new PrintHistoryService(SiteContext.Current.CurrentFxUserId);
            requestModel.FxUserId = fxUserId;
            var isPdd = Request["Pt"]?.ToLower() == PlatformType.Pinduoduo.ToString().ToLower();
            requestModel.IsPdd = isPdd;
            requestModel.IsCrossBorderSite = CustomerConfig.IsCrossBorderSite;
            var pageModel = _service.LoadList(requestModel);

            FxPlatformEncryptService.EncryptPrintHistory(pageModel.Rows, encryptSender: true);

            #region 加密--注释
            ////底单中保存的订单编号为逻辑订单号，需要转换成原始订单编号，才能解密收件人信息
            //var logicOrderDict = (new LogicOrderService()).GetLogicOrders(pageModel.Rows?.Select(f => f.PlatformOrderId.Trim('C')),
            //    fields: "o.ShopId,o.LogicOrderId,o.PlatformOrderId,o.PlatformType".Split(",".ToArray()).ToList()).ToDictionary(f => f.LogicOrderId, f => f);

            //var tempOrders = pageModel.Rows?.Select(x => new Order { PlatformOrderId = x.PlatformOrderId, LogicOrderId = x.PlatformOrderId, Id = x.ShopId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ReciverAddress }).ToList();
            //var findTmpOrders = new List<Order>(); // 部分订单丢失时，无法找到正确的店铺导致无法解密
            //tempOrders.ForEach(to =>
            //{
            //    LogicOrder lo;
            //    if (logicOrderDict.TryGetValue(to.PlatformOrderId, out lo))
            //    {
            //        to.PlatformOrderId = lo.PlatformOrderId;
            //        to.ShopId = lo.ShopId;
            //        findTmpOrders.Add(to);
            //    }
            //});


            //var isExistJdOrderOrTbOrder = pageModel.Rows?.Any(f =>
            //{
            //    LogicOrder lo;
            //    if (logicOrderDict.TryGetValue(f.PlatformOrderId, out lo))
            //    {
            //        return lo.PlatformType == PlatformType.Jingdong.ToString() || lo.PlatformType == PlatformType.Taobao.ToString();
            //    }
            //    return false;
            //}) == true;

            //var isExistJdOrder = pageModel.Rows?.Any(f =>
            //{
            //    LogicOrder lo;
            //    if (logicOrderDict.TryGetValue(f.PlatformOrderId, out lo))
            //    {
            //        return lo.PlatformType == PlatformType.Jingdong.ToString();
            //    }
            //    return false;
            //}) == true;

            //var isExistTbOrder = pageModel.Rows?.Any(f =>
            //{
            //    LogicOrder lo;
            //    if (logicOrderDict.TryGetValue(f.PlatformOrderId, out lo))
            //    {
            //        return lo.PlatformType == PlatformType.Taobao.ToString();
            //    }
            //    return false;
            //}) == true;

            //var isExistPddOrder = pageModel.Rows?.Any(f =>
            //{
            //    LogicOrder lo;
            //    if (logicOrderDict.TryGetValue(f.PlatformOrderId, out lo))
            //    {
            //        return lo.PlatformType == PlatformType.Pinduoduo.ToString();
            //    }
            //    return false;
            //}) == true;

            //if (isExistJdOrderOrTbOrder)
            //{
            //    var pids = new List<string>();
            //    pageModel.Rows?.ForEach(t =>
            //    {
            //        if (!string.IsNullOrEmpty(t.PlatformOrderJoin))
            //        {
            //            pids.AddRange(t.PlatformOrderJoin.Split(','));
            //        }
            //        else
            //        {
            //            pids.Add(t.PlatformOrderId.Trim('C'));
            //        }
            //        t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
            //        t.Reciver = t.Reciver.ToEncryptName();
            //        t.ReciverAddress = t.ReciverAddress.ToTaoBaoEncryptAddress();
            //        if (isExistTbOrder)
            //        {
            //            //淘宝还需加密发件人信息
            //            t.Sender = t.Sender.ToEncryptName();
            //            t.SenderPhone = t.SenderPhone.ToEncrytPhone();
            //        }
            //    });

            //    //if (isExistJdOrder)
            //    //{   //京东安全日志
            //    //    jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
            //    //}
            //    //else
            //    //{
            //    //    //记御城河日志
            //    //    ych_sdk.YchRequestLogger.Order(shop.Id.ToString(), "订单查询", pids);
            //    //}
            //}
            //else if (isExistPddOrder)
            //{
            //    var tempOrders = pageModel.Rows?.Select(x => new Order { PlatformOrderId = x.PlatformOrderId, LogicOrderId = x.PlatformOrderId, Id = x.ShopId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ReciverAddress }).ToList();

            //    var findTmpOrders = new List<Order>(); // 部分订单丢失时，无法找到正确的店铺导致无法解密
            //    tempOrders.ForEach(to =>
            //    {
            //        LogicOrder lo;
            //        if (logicOrderDict.TryGetValue(to.PlatformOrderId, out lo))
            //        {
            //            to.PlatformOrderId = lo.PlatformOrderId;
            //            to.ShopId = lo.ShopId;
            //            findTmpOrders.Add(to);
            //        }
            //    });

            //    BranchShareRelationService.TryToDecryptPddOrders(findTmpOrders, true);
            //    //按店铺分组
            //    pageModel.Rows?.GroupBy(x => x.ShopId).ToList().ForEach(g =>
            //    {
            //        foreach (var item in g)
            //        {
            //            var decryptedOrder = findTmpOrders.FirstOrDefault(x => x.LogicOrderId == item.PlatformOrderId && x.Id == item.ShopId);
            //            if (decryptedOrder != null)
            //            {
            //                item.Reciver = decryptedOrder.ToName;
            //                item.ReciverPhone = decryptedOrder.ToMobile;
            //                item.BuyerMemberName = item.Reciver;
            //                item.BuyerMemberId = item.Reciver;
            //                item.ReciverAddress = decryptedOrder.ToFullAddress;
            //            }
            //        }
            //    });
            //    pageModel.Rows?.ForEach(w =>
            //    {
            //        w.ReciverPhone = w.ReciverPhone.ToEncrytPhone();
            //        w.BuyerMemberName = w.BuyerMemberName.ToEncryptName();
            //        w.BuyerMemberId = w.BuyerMemberId.ToEncryptName();
            //        w.Reciver = w.Reciver.ToEncryptName();
            //        w.ReciverAddress = w.ReciverAddress.ToPddEncryptAddress();
            //        w.ReciverPhone = w.ReciverPhone.ToPddEncryptPhone();
            //    });
            //} 
            #endregion

            #region 收件人信息脱敏处理
            //EncryptionService DataMaskservice = new EncryptionService();
            //var noVirtualOrders = DataMaskservice.getPlatformType(pageModel.Rows);
            var notEncryptOrders = pageModel.Rows.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.ReciverPhone?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
            notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
            if (notEncryptOrders.Any())
                EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);

            #endregion
            return Json(pageModel);
        }

        /// <summary>
        /// 加载二次发货打印记录的列表数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        [LogForOperatorFilter("二次发货的打印记录查询列表")]
        public ActionResult LoadSecondSendList(PrintHistoryRequestModel requestModel)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _service = new PrintHistoryService(SiteContext.Current.CurrentFxUserId);
            requestModel.FxUserId = fxUserId;
            requestModel.SendType = 200;
            requestModel.IsHide = false;
            requestModel.IsCrossBorderSite = CustomerConfig.IsCrossBorderSite;//是否跨境
            var isPdd = Request["Pt"]?.ToLower() == PlatformType.Pinduoduo.ToString().ToLower();


            var pageModel = _service.LoadSecondSendList(requestModel, isPdd);

            FxPlatformEncryptService.EncryptPrintHistory(pageModel.Rows, encryptSender: true);

            return Json(pageModel);
        }

        /// <summary>
        /// 保存打印记录
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult SavelPrintHistory(PrintHistory model)
        {
            _service = new PrintHistoryService(SiteContext.Current.CurrentFxUserId);
            var result = _service.SavelPrintHistory(model);

            if (result == true)
                return SuccessResult();
            else
                return FalidResult("打印记录保存失败");
        }

        /// <summary>
        /// 批量保存打印记录
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult SavelPrintHistoryList(List<PrintHistory> models)
        {
            if (models == null && models.Any() == false) return FalidResult("后台未收到需要保存的打印记录数据");
            _service = new PrintHistoryService(SiteContext.Current.CurrentFxUserId);
            try
            {
                models.ForEach(item =>
                {
                    _service.SavelPrintHistory(item);
                });
            }
            catch (Exception ex)
            {
                Log.WriteError(ex.ToString());
                return FalidResult("打印记录保存失败");
            }
            return SuccessResult();
        }

        /// <summary>
        /// 加载打印记录未确认的数据
        /// </summary>
        /// <returns></returns>
        public ActionResult GetWaitConfirmPrintHistoryList()
        {
            var shopIds = SiteContext.Current.ShopIds;
            _service = new PrintHistoryService(SiteContext.Current.CurrentFxUserId);
            var result = _service.GetWaitConfirmPrintHistoryList(shopIds, false);

            return Json(result);
        }

        /// <summary>
        /// 加载回收状态，及需要的数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        public ActionResult LoadCancelData(List<PrintHistory> requestModel)
        {

            var result = _wayBillCodeService.GetWaybillCodeList(requestModel);
            return SuccessResult(result);
        }

        /// <summary>
        /// 加载打印数据
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadPrintData(List<long> printHistoryIds)
        {
            _service = new PrintHistoryService(SiteContext.Current.CurrentFxUserId);
            if (printHistoryIds == null || printHistoryIds.Any() == false)
                return FalidResult("请先选择需要打印的打印记录");

            var list = _service.GetListByIds(printHistoryIds);

            //前置检查模板
            if (!CustomerConfig.IsCrossBorderSite)
            {
                //20241010打印记录校验模板 允许打印删除的模板进行重打
                var checkResult = PerCheckTemplate(list, Request, Response, false);
                if (checkResult != null)
                    return checkResult;
            }
            GetPrintHistoryDataFromApi(list);
            //头条需要把打印数据的签名重新生成一遍
            new CommService().UpSignPrintHistory(list);

            //跨境需要把打印数据的PDF重新成生成并下载
            if (CustomerConfig.IsCrossBorderSite)
                new CommService().GetTikTokPrintPdf(list);
            return SuccessResult(list);
        }

        /// <summary>
        /// 二次发货
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [FxAuthorize(FxPermission.OnlineResend)]
        public ActionResult RetryOnlineResend(List<ResendFromPrintHistoryModel> requests)
        {
            if (requests == null || !requests.Any())
                return FalidResult("请求数据为空");

            var models = new LogicOrderService().GetSendRequestFromPrintHistory(requests);
            if (models == null || !models.Any())
                return FalidResult("失败");


            //合并结果
            var results = new List<DeliverySendOrderResultModel>();

            var controller = new NewOrderController();
            models.ForEach(model =>
            {

                List<DeliverySendOrderResultModel> dsoResults = null;
                var actionResult = controller.ExeOnlineResend(model, out dsoResults);

                results.AddRange(dsoResults);

            });

            //返回模型
            var result = new DeliverySendResponseModel
            {
                Orders = results,
                SuccessCount = results?.Where(r => r.IsSuccess).Count(),
                ErrorCount = results?.Where(r => !r.IsSuccess || !string.IsNullOrEmpty(r.ErrorMessage)).Count(),
                HasSendPreCheckError = results?.Any(f => f.ErrorCode == "SendPreCheckError") ?? false
            };

            return Json(result);
        }

        /// <summary>
        /// 设置为隐藏
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [FxAuthorize()]
        public ActionResult SetHide(List<long> ids)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _service = new PrintHistoryService(SiteContext.Current.CurrentFxUserId);
            var result = _service.SetHide(ids, fxUserId);
            if (result > 0)
                return SuccessResult("成功");
            else
                return FalidResult("更新失败");
        }

        #region 导出

        /// <summary>
        /// 导出打印记录
        /// </summary>
        /// <returns></returns>
        [FxAuthorize]
        public ActionResult PrintHistoryExportExcel()
        {
            var shop = SiteContext.Current.CurrentLoginShop;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _service = new PrintHistoryService(fxUserId);
            try
            {
                var options = Request.Form["options"].ToString2();
                options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
                options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果
                PrintHistoryRequestModel requestModel = JsonExtension.ToObject<PrintHistoryRequestModel>(options) ?? new PrintHistoryRequestModel();

                #region 导出任务验证、查询超时转换后台任务
                var timeOutStatus = false;
                var timeOutSecond = 25;
                var timeOutSet = Request["time_out_set"].ToBoolean(); // 模拟任务校验超时
                var isVerification = requestModel.DisabledTaskVerification == true ? false : true; // 是否任务校验
                var typeStr = "打印记录导出";

                if (isVerification)
                {
                    try
                    {
                        var task = _exportTaskService.GetErpWebExportTaskSharding(shop.Id
                            , CustomerConfig.CloudPlatformType
                            , ExportType.ErpPrintHistory.ToInt()
                            , Request["dbname"] ?? ""
                            , timeOutSecond); ;

                        if (task != null && task.Status >= 0 && task.Status < 4)
                            return FalidResult("已存在订单导出任务，如需重新导出，请先取消再创建新导出任务", GetExportTaskToWeb(task));
                    }
                    catch (Exception ex)
                    {
                        if (ex.Message.IndexOf("执行超时已过期") != -1)
                        {
                            timeOutStatus = true;
                            Log.WriteError($"导出任务查询超时，转换为后台导出任务，超时原因：{ex.ToJsonExt()},超时任务类型：{typeStr}");
                        }
                    }
                }
                timeOutStatus = timeOutSet ? true : timeOutStatus;
                #endregion

                requestModel.FxUserId = fxUserId;
                requestModel.PrintTemplateShopId = shop.Id;

                var pageSize = 500;
                //查询第一页数据
                var model = requestModel.ToJson().ToObject<PrintHistoryRequestModel>();
                requestModel.PageIndex = 1;
                model.PageIndex = requestModel.PageIndex;
                model.PageSize = pageSize;
                var returnCount = model.PageSize;

                //var _service = new PrintHistoryService();
                var _service = new PrintHistoryService(SiteContext.Current.CurrentFxUserId);
                model.IsCrossBorderSite = CustomerConfig.IsCrossBorderSite;
                var rsp = _service.LoadList(model);
                var totalCount = rsp.Total;
                var limitCount = CustomerConfig.ExportByTaskPrintHistoryCountLimit;
                var type = ExportType.ErpPrintHistory.ToInt();
                var dbname = Request["dbname"].ToString2();

                //limitCount = 0;//调试：导出使用任务方式

                ///临时测试导出任务程序，上线后删除
                //if (CustomerConfig.IsDebug && totalCount > 5)
                //    limitCount = 5;

                //任务方式
                if (totalCount > limitCount || timeOutStatus)
                {
                    //使用任务方式导出
                    var newTask = new ExportTask
                    {
                        IP = Request.UserHostAddress,
                        CreateTime = DateTime.Now,
                        PlatformType = CustomerConfig.CloudPlatformType,
                        ShopId = shop.Id,
                        UserId = SiteContext.Current.CurrentFxUserId.ToString(),
                        Status = 0,
                        Type = type,
                        PageIndex = 1,
                        PageSize = pageSize,
                        TotalCount = totalCount,
                        ParamJson = model.ToJson(),
                        FromModule = "打印记录-导出Excel",
                        ExtField2 = shop.PlatformType,
                        ExtField5 = dbname,
                        ExtField4 = CustomerConfig.IsCrossBorderSite ? "3" : null,//跨境环境指定灰度3导出程序
                        ExtField6 = CustomerConfig.IsCrossBorderSite ? PlatformType.TikTok.ToString() : CustomerConfig.CloudPlatformType, //跨境
                        SubFxUserId = SiteContext.GetSubFxUserId().ToString()
                    };
                    newTask.HopeExecuteTime = new CommService().ExportExecuteCheck(newTask.UserId.ToInt(), newTask.Type);
                    newTask.Id = new ExportTaskService().Add(newTask);
                    return SuccessResult("导出任务创建成功", GetExportTaskToWeb(newTask));
                }

                // 直接导出方式进行导出            
                var list = new List<PrintHistory>();

                ///第一页
                returnCount = rsp.Rows.Count;
                if (returnCount > 0)
                {
                    list.AddRange(rsp.Rows);
                }

                //后面的页
                requestModel.PageIndex++;
                bool IsCrossBorderSite = CustomerConfig.IsCrossBorderSite;
                while (model.PageSize == returnCount)
                {
                    model = requestModel.ToJson().ToObject<PrintHistoryRequestModel>();
                    model.PageSize = pageSize;
                    model.IsCrossBorderSite = IsCrossBorderSite;
                    var result = _service.LoadList(model);

                    var pgList = result.Rows;
                    list.AddRange(pgList);
                    returnCount = pgList.Count;
                    requestModel.PageIndex++;
                }

                if ((list?.Count ?? 0) == 0)
                    return FalidResult("无满足条件的数据导出");

                var fileName = "打印记录.xls";
                IWorkbook workbook = FxBuildExccelService.PrintHistoryBuildExcel(list, fileName, shop.Id, CustomerConfig.IsCrossBorderSite);

                Response.Cookies.Add(new HttpCookie("downloadToken", Request.Form["downloadToken"].ToString2()));
                using (MemoryStream ms = new MemoryStream())
                {
                    workbook.Write(ms);
                    var buffer = ms.GetBuffer();
                    return File(buffer, "application/ms-excel", ExcelHelper.GetFileName(fileName, Request));
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"打印记录Excel导出失败：{ex}");
                return FalidResult("程序异常，请联系我们");
            }
        }
        #endregion

        /// <summary>
        /// 针对头条平台通过二次获取打印内容
        /// </summary>
        /// <param name="list"></param>
        public void GetPrintHistoryDataFromApi(List<PrintHistory> list)
        {
            if (list == null || !list.Any())
                return;

            if (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString())
            {
                //当前为头条云平台，PrinterJsonData为空，二次获取PrintHistoryData数据
                var query = list.Where(a => string.IsNullOrEmpty(a.PrinterJsonData) && !string.IsNullOrEmpty(a.SendContent)).Select(a => new PrintHistoryDataApiModel { ShopId = a.ShopId, RelationCode = a.SendContent }).ToList();

                if (query.Any())
                {
                    try
                    {
                        var curFxUserId = SiteContext.Current.CurrentFxUserId;
                        var apiUrl = "/PrintHistoryApi/GetList";
                        var cloundPtSiteHost = CustomerConfig.AlibabaFenFaSystemUrl;
                        var aliCloudHostUrl = cloundPtSiteHost.TrimEnd("/") + apiUrl;

                        var apiResult = Common.PostFxSiteApi<List<PrintHistoryDataApiModel>, List<PrintHistoryDataApiModel>>(aliCloudHostUrl, curFxUserId, query, "从精选云获取打印内容");
                        if (!string.IsNullOrEmpty(cloundPtSiteHost) && apiResult != null && apiResult.Any())
                        {
                            apiResult.ForEach(item =>
                            {
                                var exist = list.FirstOrDefault(a => a.SendContent == item.RelationCode);
                                if (exist != null)
                                {
                                    exist.PrinterJsonData = item.PrinterJsonData;
                                    exist.SendContent = item.SendContent;
                                }
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"从精选云获取打印内容失败{ex}");
                    }
                }
            }
        }
        /// <summary>
        /// 模板前置校验
        /// </summary>
        /// <param name="list"></param>
        /// <param name="request"></param>
        /// <param name="response"></param>
        /// <param name="isIgnoreDeleted">是否过滤删除数据</param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        public ActionResult PerCheckTemplate(List<PrintHistory> list, HttpRequestBase request, HttpResponseBase response, bool isIgnoreDeleted = true)
        {
            if (list == null || !list.Any())
                return null;

            var templateIds = list.Select(a => a.TemplateId).Distinct().ToList();
            var templateSetController = new TemplateSetController();

            foreach (var templateId in templateIds)
            {

                var branchList = new List<BranchAddress>();
                var isNoNeedCheck = false;
                var cpCodeMapping = new ExpressCpCodeMapping();
                var tempPlatformType = "";
                //打印记录、底单记录重打时，不校验快递模板是否删除。
                var template = _printTemplateService.Get(templateId, isIgnoreDeleted);
                if (template == null)
                    throw new LogicException($"模板[{templateId}]已删除");
                var checkResult = templateSetController.CheckTemplate(template, out branchList, out isNoNeedCheck, out cpCodeMapping, out tempPlatformType, request, response);

                if (checkResult != null)
                    return checkResult;
            };

            return null;
        }
    }
}