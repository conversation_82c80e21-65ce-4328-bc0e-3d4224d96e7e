using DianGuanJiaApp.Controllers;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.ErpWeb.Models;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.BaseProduct;
using DianGuanJiaApp.Services.Services.SyncService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Helpers;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Web.Mvc;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Services.Services.SubAccount;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    public class ProductController : BaseController
    {

        private ProductFxService _productFxService = new ProductFxService();
        private FxUserShopService _fxUserShopService = new FxUserShopService();
        private PathFlowService _pathFlowService = new PathFlowService();
        private PathFlowReferenceService _pathFlowRefService = new PathFlowReferenceService();
        private ShopService _shopService = new ShopService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private WareHouseService _service = new WareHouseService();
        private SettlementProductSkuService _settlementProductSkuService = new SettlementProductSkuService();
        private ProductInfoFxService _productInfoFxService = new ProductInfoFxService();


        /// <summary>
        /// 商品
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            //获取我的厂家信息
            ViewBag.DefaultFenFaSystemUrl = CustomerConfig.DefaultFenFaSystemUrl;
            var supplierService = new SupplierUserService();
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            //var MyShop = new Dictionary<int, string>();
            var times = "";
            var isSyncButton = "1";
            try
            {
                times = new SyncStatusService().LastTimeProductByFxUserId(fxUserId);
                ////商家数据源
                //var agents = supplierService.GetAgentList(fxUserId, null, null, 1, 10000);
                //ViewBag.AgentUsers = agents?.Item2?.Where(x => x.Status == AgentBingSupplierStatus.Binded).Select(x => new { x.NickName, x.FxUserId }).Distinct().ToDictionary(x => x.FxUserId, x => x.NickName);
                ////厂家数据源
                //var suppliers = supplierService.GetSupplierList(fxUserId, null, null, 1, 10000);
                //ViewBag.SupplierUsers = suppliers?.Item2?.Where(x => x.Status == AgentBingSupplierStatus.Binded).Select(x => new { x.NickName, FxUserId = x.SupplierFxUserId }).Distinct().ToDictionary(x => x.FxUserId, x => x.NickName);

                //MyShop = new FxUserShopService().GetDictionaryShopsByFxUserId(fxUserId);

                //GetIsShowProductSyncButton，追加判断是同个平台，且是Pending，开始时间在30分钟内的。
                isSyncButton = new SyncStatusService().GetIsShowProductSyncButton(fxUserId);

                //如果停止，则不检测
                if (CustomerConfig.IsSyncDisabledByProductExamine)
                    isSyncButton = "1";
            }
            catch (Exception e)
            {
                Log.WriteError($"商品列表错误：{e.Message}");
            }

            ViewBag.LastTime = times;
            //上次同步时间超过7天给提示文案
            ViewBag.LastTimeRemark = "若您后台更新/上架了新的商品链接，请及时更新到店管家分销系统";
            var _commonSettingService = new CommonSettingService();
            var SyncMessageTime = _commonSettingService.Get("/ErpWeb/Product/SyncMessageTime", SiteContext.Current.CurrentShopId);
            DateTime dtLastTime;
            if (SyncMessageTime != null && DateTime.TryParse(SyncMessageTime.Value, out dtLastTime))
            {
                if (dtLastTime > DateTime.Now.AddDays(-7))
                {
                    ViewBag.LastTimeRemark = "";
                }
            }

            //ViewBag.MyShop = MyShop;
            ViewBag.IsShowSyncButton = isSyncButton;

            //var currShop = SiteContext.Current.CurrentLoginShop;
            //var _commonSettingService = new CommonSettingService();
            //var keys = new List<string> { "/ErpWeb/Product/PageSize" };
            //var commSets = _commonSettingService.GetSets(keys, currShop.Id);

            //var pageSizeSet = commSets.FirstOrDefault(x => x.Key == "/ErpWeb/Product/PageSize");
            //ViewBag.PageSize = pageSizeSet?.Value ?? "20";

            LoadDefaultConfig();
            //检测
            //ProductStatusAndStart();

            // 页面按钮展示权限
            ViewBag.ShowPermDict = new Dictionary<string, bool>
            {
                {$"#{nameof(FxPermission.SyncProduct)}",SiteContext.HasPermission(FxPermission.SyncProduct)},
                {$"#{nameof(FxPermission.DeleteProduct)}",SiteContext.HasPermission(FxPermission.DeleteProduct)},
                {$".{nameof(FxPermission.TriggerBindSupplier)}",SiteContext.HasPermission(FxPermission.TriggerBindSupplier)},
                {$".{nameof(FxPermission.SaveProductShortTitleOrWeight)}",SiteContext.HasPermission(FxPermission.SaveProductShortTitleOrWeight)},
                {$".{nameof(FxPermission.SetProductSettlementPrice)}",SiteContext.HasPermission(FxPermission.SetProductSettlementPrice)},
                {$".{nameof(FxPermission.AbnormalProduct)}",SiteContext.HasPermission(FxPermission.AbnormalProduct)},
            }.ToJson();

            return View();
        }

        /// <summary>
        /// 商品回收站
        /// </summary>
        /// <returns></returns>
        public ActionResult Recycle()
        {
            //厂家数据源
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var suppliers = new SupplierUserService().GetSupplierList(fxUserId,needEncryptAccount:true);
            ViewBag.Suppliers = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter }).Distinct().ToJson();

            return View();
        }

        public ActionResult ProductBasics()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var suppliers = new SupplierUserService().GetSupplierList(fxUserId,needEncryptAccount:true);
            ViewBag.Suppliers = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter }).Distinct().ToJson();

            return View();
        }


        public ActionResult CreateBaseProduct()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var suppliers = new SupplierUserService().GetSupplierList(fxUserId,needEncryptAccount:true);
            ViewBag.Suppliers = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter }).Distinct().ToJson();

            return View();
        }

        public void LoadDefaultConfig()
        {
            var pts = CustomerConfig.GetAllPlatformAuthLinks().ToList();
            if (!pts.Any(x => x.PlatformType == "Toutiao"))
            {
                pts.Add(new SuportPlatformAuthEntryModel
                {
                    PlatformType = "TouTiao",
                    Name = "抖音",
                    Index = 4
                });
            }
            if (!pts.Any(x => x.PlatformType == PlatformType.Virtual.ToString()))
            {
                pts.Add(new SuportPlatformAuthEntryModel
                {
                    PlatformType = "Virtual",
                    Name = "线下单",
                    Index = 6
                });
            }
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var _reqModel = new FxUserShopQueryModel { FxUserId = fxUserId };

            var fxUserShops = new List<FxUserShop>();
            //绑定店铺
            ViewBag.Shops = fxUserShops.Select(x => new { x.NickName, x.ShopId, x.PlatformType }).Distinct().ToJson();
            if (CustomerConfig.IsCrossBorderSite)
            {
                FxUserForeignShopService _fxUserForeignShopService = new FxUserForeignShopService();
                var fxUserForeignShops = _fxUserForeignShopService.GetList(_reqModel)?.Item2 ?? new List<FxUserForeignShop>();
                fxUserForeignShops = fxUserForeignShops.Where(x => x.Status != FxUserShopStatus.UnBind).ToList();
                //绑定店铺
                ViewBag.Shops = fxUserForeignShops.Select(x => new { NickName = $"{x.NickName}-{x.SubPlatformType}", x.ShopId, x.PlatformType }).Distinct().ToJson();
                //未过期店铺
                ViewBag.ShopsEffect = fxUserForeignShops.Where(w => w.Status == FxUserShopStatus.Binded).Select(x => new { NickName = $"{x.NickName}-{x.SubPlatformType}", x.ShopId, x.PlatformType }).Distinct().ToJson();
                fxUserShops = fxUserForeignShops.Select(f => f as FxUserShop).ToList();
            }
            else
            {
                fxUserShops = GetFxUserShops(); 
                //绑定店铺
                ViewBag.Shops = fxUserShops.Select(x => new { x.NickName, x.ShopId, x.PlatformType }).Distinct().ToJson();
                //未过期店铺
                ViewBag.ShopsEffect = fxUserShops.Where(w => w.Status == FxUserShopStatus.Binded).Select(x => new { x.NickName, x.ShopId, x.PlatformType }).Distinct().ToJson();
            }
            var pathFlowList = _pathFlowService.GetPathFlowByFxUserId(fxUserId);
            var sids = pathFlowList.Where(f => f.SourceShopId > 0).Select(f => f.SourceShopId).ToList();
            var shops = _shopService.GetShopByIds(sids, "id,shopId,nickName,platformtype");
            if (CustomerConfig.CloudPlatformType == PlatformType.Pinduoduo.ToString())
                shops = shops.Where(x => CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(x.PlatformType)).ToList();
            else if (CustomerConfig.CloudPlatformType == PlatformType.Jingdong.ToString())
                shops = shops.Where(x => CustomerConfig.FxJingDongCloudPlatformTypes.Contains(x.PlatformType)).ToList();
            else if (CustomerConfig.CloudPlatformType == PlatformType.TouTiao.ToString())
                shops = shops.Where(x => CustomerConfig.FxDouDianCloudPlatformTypes.Contains(x.PlatformType)).ToList();
            else
                shops = shops.Where(x =>
                    CustomerConfig.FxJingDongCloudPlatformTypes.Contains(x.PlatformType) == false &&
                    CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(x.PlatformType) == false &&
                    CustomerConfig.FxDouDianCloudPlatformTypes.Contains(x.PlatformType) == false).ToList();
            fxUserShops.ForEach(fx =>
            {
                if (!shops.Any(s => s.Id == fx.ShopId))
                    shops.Add(new Shop { Id = fx.ShopId, PlatformType = fx.PlatformType });
            });
            //店铺对应的平台
            ViewBag.PlatformTypes = pts.Where(x => shops.Any(n => n.PlatformType.ToLower() == x.PlatformType.ToLower())).OrderBy(x => x.Index).Select(x => new { x.PlatformType, x.Name }).ToJson();


            var currShopId = SiteContext.Current.CurrentShopId;
            var _commonSettingService = new CommonSettingService();
            var _supplierUserService = new SupplierUserService();
            var permissionService = new SysPermissionFxService();
            //商家数据源
            var agents = _supplierUserService.GetAgentList(fxUserId, onlyGetCurDb: true,needEncryptAccount:true);
            //permissionService.EncryptAccount(agents, isAgent : true);
            ViewBag.Agents = agents.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct().ToJson();
            //厂家数据源
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, onlyGetCurDb: true,needEncryptAccount:true);
            //permissionService.EncryptAccount(suppliers, isAgent: false);
            ViewBag.Suppliers = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter }).Distinct().ToJson();
            ViewBag.SupplierUsers = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId }).GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.UserName ?? "");
            var keys = new List<string>
            {
                "/ErpWeb/Product/PageSize",
                "/ErpWeb/Product/HideOfflineProduct",
            };
            var commSets = _commonSettingService.GetSets(keys, currShopId);
            var pageSizeSet = commSets.FirstOrDefault(x => x.Key == "/ErpWeb/Product/PageSize");
            ViewBag.PageSize = pageSizeSet?.Value ?? "20";
            var hideOfflineProduct = commSets.FirstOrDefault(x => x.Key == "/ErpWeb/Product/HideOfflineProduct");
            ViewBag.HideOfflineProduct = hideOfflineProduct?.Value ?? "true";

            #region 调整分库下各参数的范围
            //如果只有一个分库,跳过此段
            //如果有多个分库,按照分库分组商家(商家可在多个分库中出现)
            //然后重新赋值新的商家下拉框信息
            //上面已经只取当前分区相关的用户了，此处不需要再处理了 2023-08-14
            //ViewBag.Agents = _commonSettingService.RecoverSupplierViewBag(fxUserId,agents
            //    , SiteContext.Current.CurrentLoginShop.DbAreaConfig);
            #endregion

        }

        /// <summary>
        /// 绑定厂家
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("绑定厂家")]
        [FxMigrateLockFilter()]
        public ActionResult BindSupllier(BindSupplierRequestModel model)
        {
            var logContext = LogForOperatorContext.Current;
            model.IP = Request.UserHostAddress;

            var productFxService = new ProductFxService();
            productFxService.BindSupplier(model);

            var subLog1 = new LogForOperator { OperatorType = "查询最新商品展示", Detail = model.productCodes };
            logContext.StartStep(subLog1);

            var queryModel = new ProductFxRequertModule
            {
                PageIndex = 1,
                PageSize = model.productCodes.Count,
                ProductCodes = model.productCodes
            };
            var data = _productFxService.GetProductFxList(SiteContext.Current.CurrentFxUserId, queryModel);

            logContext.EndStep();
            return SuccessResult(data);
        }


        /// <summary>
        /// 触发 绑定厂家
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        [IgnoreDoubleAuth]
        [FxAuthorize()]
        public ActionResult TriggerBindSupplier(BindSupplierRequestModel model)
        {
            //站点云平台
            var siteCloudPlatformType = CustomerConfig.CloudPlatformType;

            var dbname = Request["dbname"] ?? "";
            var token = Request["token"] ?? "";                           //读取token，实例化sitecontext
            if (string.IsNullOrEmpty(token))
                return SuccessResult($"发往云平台{siteCloudPlatformType}的TriggerBindSupplier请求token丢失");

            //var initSiteContextResult = InitSiteContextByToken(token, dbname);

            //if (initSiteContextResult == false)
            //    return SuccessResult($"发往云平台{siteCloudPlatformType}的TriggerBindSupplier请求token【{token}】解析失败");


            model.IP = Request.UserHostAddress;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var subUserId = SiteContext.GetSubFxUserId();
            //批次ID
            var batchId = Guid.NewGuid().ToReplaceHyphen();

            //前置检查
            var pathFlowService = new PathFlowService();
            var pathFlowRefService = new PathFlowReferenceService();
            var perCheck =
                _productFxService.PerCheckBindSupplier(model, pathFlowService, pathFlowRefService, fxUserId, batchId);

            //业务检查
            ActionResult rst = null;
            if (perCheck.CheckResult != null && !perCheck.CheckResult.Success)
            {
                if (string.IsNullOrEmpty(perCheck.CheckResult.Data))
                    //throw new LogicException(perCheck.CheckResult.Message);
                    rst = FalidResult(perCheck.CheckResult.Message);
                else
                    //返回检查失败的ProdcutCode
                    rst = FalidResult("12", perCheck.CheckResult.Message, perCheck.CheckResult.Data);
            }

            #region 判断当前是否有进行中的任务，若有直接返回并提示；没有再创建任务
            AsyncTask task = null;
            var asyncTaskService = new AsyncTaskService();
            if (rst == null)
            {
                var query = new AsyncTaskQueryModel
                {
                    ShopId = SiteContext.Current.CurrentShopId,
                    FxUserId = fxUserId,
                    StatusList = new List<int>() { 0, 1 },
                    Flag = "BindSupplier",
                    IsSetExpiress = true,
                    ExpiressTime = 30
                };

                task = asyncTaskService.GetAsyncTask(query);
                if (task != null)
                    rst = FalidResult("当前有商品更换厂家任务进行，请等待任务结束后操作！");
            }

            try
            {
                if (rst == null)
                {
                    //添加任务
                    task = new AsyncTask();
                    task.Flag = "BindSupplier";
                    task.CData = model.ToJson();
                    asyncTaskService.AddAsyncTask(task);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"绑定厂家生成任务失败：{ex}");
                rst = FalidResult("绑定厂家生成任务失败，请稍后重试");
            }

            if (rst != null)
            {
                var lockService = new ShopLockService(); //店铺锁服务，业务如果未处理返回之前要要释放锁
                lockService.UnLock(perCheck.LockSids, (int)ShopLockType.ProductBind, batchId); //释放店铺锁
                return rst; //返回失败消息
            }

            //启动线程，执行任务
            ThreadPool.QueueUserWorkItem(state =>
            {
                try
                {
                    //Thread.Sleep(30*1000);//调试
                    if (!string.IsNullOrEmpty(dbname))
                    {
                        dbname = DES.DecryptDES(dbname, CustomerConfig.LoginCookieEncryptKey);
                    }

                    var userFx = new UserFxService().Get(fxUserId);
                    if (subUserId > 0)
                    {
                        var subUserFx = new SubUserFxService().Get(subUserId);
                        SiteContext siteContext = new SiteContext(userFx, dbname,
                            new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false },
                            subUserFx); //实例化商家的SiteContext（包含子账号）   
                    }
                    else
                    {
                        SiteContext siteContext = new SiteContext(userFx, dbname,
                            new SiteContextConfig
                                { NeedShopExpireTime = false, NeedRelationShops = false }); //实例化商家的SiteContext   
                    }


                    task.Status = 1; //进行中
                    task.UpdateTime = DateTime.Now;
                    asyncTaskService.UpdatePendingStatus(task);
                    _productFxService.BindSupplier(model, perCheck, false, task, batchId: batchId);

                    task.Status = 5; //执行完成
                    task.UpdateTime = DateTime.Now;
                    asyncTaskService.UpdateStatus(task);

                }
                catch (LogicException lex)
                {
                    Log.WriteError($"商品绑定厂家-更新订单发生异常：{lex}");
                    task.Status = -1; //失败
                    task.ExceptionDesc = $"异常消息：{lex.Message}=》异常堆栈：{lex.StackTrace}";
                    task.UpdateTime = DateTime.Now;

                    asyncTaskService.UpdateAsyncTask(task);
                    ExceptionLogDataEventTrackingService.Instance.WriteLog("TriggerBindSupplier", lex,
                        task.ToJson(true));
                }
                catch (Exception ex)
                {
                    Log.WriteError($"商品绑定厂家-更新订单发生异常：{ex}");
                    task.Status = -1; //失败
                    task.ExceptionDesc = $"异常消息：{ex.Message}=》异常堆栈：{ex.StackTrace}";
                    task.UpdateTime = DateTime.Now;

                    asyncTaskService.UpdateAsyncTask(task);
                    ExceptionLogDataEventTrackingService.Instance.WriteLog("TriggerBindSupplier", ex,
                        task.ToJson(true));
                }
                finally
                {
                    //释放店铺锁
                    new ShopLockService().UnLock(perCheck.LockSids, (int)ShopLockType.ProductBind, batchId);
                }
            });

            #endregion

            return SuccessResult("");
        }

        /// <summary>
        /// 获取 绑定厂家 任务执行状态
        /// </summary>
        /// <returns></returns>
        public ActionResult GetBindSupplierStatus()
        {
            var siteCloudPlatformType = CustomerConfig.CloudPlatformType; //站点云平台

            var dbname = Request["dbname"] ?? "";
            var token = Request["token"] ?? "";                           //读取token，实例化sitecontext
            if (string.IsNullOrEmpty(token))
                return SuccessResult($"发往云平台{siteCloudPlatformType}的TriggerBindSupplier请求token丢失");

            //var initSiteContextResult = InitSiteContextByToken(token, dbname);

            //if (initSiteContextResult == false)
            //    return SuccessResult($"发往云平台{siteCloudPlatformType}的TriggerBindSupplier请求token【{token}】解析失败");



            #region 查询最近一条任务
            var asyncTaskService = new AsyncTaskService();
            var query = new AsyncTaskQueryModel
            {
                ShopId = SiteContext.Current.CurrentShopId,
                FxUserId = SiteContext.Current.CurrentFxUserId,
                Flag = "BindSupplier",
                StartCreateTime = DateTime.Now.AddMinutes(-30)
            };

            var task = asyncTaskService.GetAsyncTask(query);
            if (task != null)
            {
                if (task.Status == 0 || task.Status == 1)
                {
                    return SuccessResult($"任务执行中，请稍候", task);
                }
                else if (task.Status == 5)
                {
                    return SuccessResult($"无进行中的任务.", task);
                }
                else
                {
                    return SuccessResult($"任务执行出现异常，请稍候重新提交", task);
                }
            }

            #endregion

            return SuccessResult("无进行中的任务", null);
        }
        /// <summary>
        /// 查询最新商品展示
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult QueryProduct(BindSupplierRequestModel model)
        {
            var logContext = LogForOperatorContext.Current;
            model.IP = Request.UserHostAddress;

            var subLog1 = new LogForOperator { OperatorType = "查询最新商品展示2", Detail = model.productCodes };
            logContext.StartStep(subLog1);

            var queryModel = new ProductFxRequertModule
            {
                PageIndex = 1,
                PageSize = model.productCodes.Count,
                ProductCodes = model.productCodes
            };

            queryModel.IsBaseProduceCombine = true; // 商品列表不受归一开关影响
            var data = _productFxService.GetProductFxList(SiteContext.Current.CurrentFxUserId, queryModel);

            logContext.EndStep();
            return SuccessResult(data);
        }

        /// <summary>
        /// 检测同步异常后，重新启动线程处理
        /// </summary>
        public void ProductStatusAndStart()
        {
            ////如果停止，则不检测
            //if (CustomerConfig.IsSyncDisabledByProductExamine)
            //    return;

            ////需要同步的店铺          
            //var syncShopList = new SyncStatusService().IsSyncSucceed(SiteContext.Current.CurrentFxUserId);


            //if (syncShopList.Count > 0)
            //{
            //    var shopIdList = syncShopList?.Select(s => s.ShopId).ToList();

            //    var shopList = new ShopService().GetShopByIds(shopIdList);


            //    ThreadPool.QueueUserWorkItem(state =>
            //    {
            //        foreach (Shop shop in shopList)
            //        {
            //            new SyncFxProductService().SyncProduct(shop, 1);
            //        }

            //    });
            //}

        }


        [LogForOperatorFilter("商品列表查询")]
        [FxMigrateLockFilter()]
        [PageDepthControlFilter]
        public ActionResult LoadList(ProductFxRequertModule model)
        {
            var displaySkuCount = 3;
            var isFromOffline = Request["FxPageType"] == "3";
            if (isFromOffline)
                displaySkuCount = 0;
            var hideOfflineProduct = _commonSettingService.GetString("/ErpWeb/Product/HideOfflineProduct", SiteContext.Current.CurrentShopId) ?? "true";
            model.HideOfflineProduct = model.HideOfflineProduct.HasValue ? model.HideOfflineProduct : hideOfflineProduct.ToBool();
            // 归一开关
            model.FxUserId =
                SiteContext.Current.CurrentFxUserId;
            //model.IsBaseProduceCombine = 
            //    SiteContext.Current.BaseProductSetting.OrderCombine;
            model.IsBaseProduceCombine = true;
            var data = _productFxService.GetProductFxList(SiteContext.Current.CurrentFxUserId, model, displaySkuCount: displaySkuCount);
            var result = Json(data);
            return result;
        }

        /// <summary>
        /// 只用于商品管理的列表查询
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("商品管理查询")]
        [FxMigrateLockFilter()]
        [PageDepthControlFilter]
        public ActionResult LoadProductList(ProductFxRequertModule model)
        {
            var displaySkuCount = 3;
            var isFromOffline = Request["FxPageType"] == "3";
            if (isFromOffline)
                displaySkuCount = 0;
            var hideOfflineProduct = _commonSettingService.GetString("/ErpWeb/Product/HideOfflineProduct", SiteContext.Current.CurrentShopId) ?? "true";
            model.HideOfflineProduct = hideOfflineProduct.ToBool();
            var data = _productFxService.GetProductFxListV2(SiteContext.Current.CurrentFxUserId, model, displaySkuCount: displaySkuCount);
            var result = Json(data);
            return result;
        }


        [FxMigrateLockFilter()]
        public ActionResult LoadSkuList(Data.Model.ProductFxMoreSkuRequestModule model)
        {
            model.IsBaseProduceCombine = SiteContext.Current.BaseProductSetting.OrderCombine;
            if (string.IsNullOrEmpty(model.ProductCode))
            {
                return FalidResult("未传入商品Id");
            }
            //var data = _productFxService.GetProductFxList(SiteContext.Current.CurrentFxUserId, model);            
            //var data = _productFxService.GetProductFxListMoreSku(SiteContext.Current.CurrentFxUserId, model.ProductCode, model.ProductType);
            var data = _productFxService.GetProductFxListMoreSku(SiteContext.Current.CurrentFxUserId, model);
            var result = Json(data);
            return result;
        }

        /// <summary>
        /// 获取商品同步状态
        /// </summary>
        /// <returns></returns>
        public ActionResult IsProductSucceed()
        {
            //GetIsShowProductSyncButton，追加判断是同个平台，且是Pending，开始时间在30分钟内的。
            // var isSyncButton = new SyncStatusService().GetIsShowProductSyncButton(SiteContext.Current.CurrentFxUserId);
            // var result = "2";
            // var syncShopName = "";
            // if (isSyncButton == "1")
            // {
            //     result = new SyncStatusService().LastTimeProductByFxUserId(SiteContext.Current.CurrentFxUserId);
            //     syncShopName = new FxUserShopService().GetCurrentUserShopNickNameByAlibaba();
            // }
            
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var syncStatus = new SyncStatusService().GetSelfShopProductSyncStatus(fxUserId);
            if (syncStatus == null || !syncStatus.Any())
            {
                return SuccessResult(new
                {
                    Results = string.Empty, SyncShopName = new FxUserShopService().GetCurrentUserShopNickNameByAlibaba()
                });
            }
            var isSyncFinished = true;
            syncStatus.ForEach(t =>
            {
                //如果该店铺同步中，且30分钟内,属于正常同步中
                if (t.LastSyncStatus != "Pending" || t.StartSyncTime == null)
                {
                    return;
                }
                var ts = DateTime.Now.Subtract(Convert.ToDateTime(t.StartSyncTime));
                if (Convert.ToInt32(ts.TotalMinutes) < 30)
                {
                    isSyncFinished = false;
                }
            });
            var result = "2";
            var syncShopName = string.Empty;
            if (!isSyncFinished)
            {
                return SuccessResult(new { Results = result, SyncShopName = syncShopName });
            }
            var syncState = syncStatus.OrderByDescending(m => m.LastSyncTime).FirstOrDefault();
            result = syncState == null ? string.Empty : syncState.LastSyncTime?.ToString("yyyy-MM-dd HH:mm:ss");
            syncShopName = new FxUserShopService().GetCurrentUserShopNickNameByAlibaba();
            return SuccessResult(new { Results = result, SyncShopName = syncShopName });
        }

        #region 是否开启商品同步服务
        /// <summary>
        /// 是否开启商品同步服务
        /// </summary>
        /// <returns></returns>

        public bool IsEnabledProductSyncService()
        {
            //按用户（系统店铺id）键：IsEnabledOrderSyncService_{CloudPlatformType},如：IsEnabledOrderSyncService_Alibaba
            var key = "IsEnabledProductSyncService_" + CustomerConfig.CloudPlatformType;
            var shopId = SiteContext.Current.CurrentShopId;
            //按用户获取配置开关
            var configValue = _commonSettingService.Get(key, shopId);
            var isEnabledProductSyncService = false;
            if (configValue != null)
            {
                isEnabledProductSyncService = Convert.ToBoolean(configValue.Value);
            }
            //是否全局开启
            if (CustomerConfig.IsEnabledGlobalProductSyncService)
            {
                return true;
            }
            //是否白名单
            return CustomerConfig.IsEnabledProductSyncService && isEnabledProductSyncService;
        }
        #endregion

        /// <summary>
        /// 同步商品
        /// </summary>
        /// <param name="isAuto">isAuto为true时，表示系统自动</param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        [FxAuthorize()]
        public ActionResult SyncProduct(bool isAuto = false, int statusType = 0, DateTime? timeStart = null, DateTime? timeEnd = null, string inputsid = null, string platformid = null)
        {
            var lastTime = "";
            var inputShopid = new List<int>();
            //入参中店铺id处理
            if (!string.IsNullOrEmpty(inputsid))
            {
                var strsid = inputsid.Split(',');
                for (int i = 0; i < strsid.Length; i++)
                {
                    int sid;
                    if (int.TryParse(strsid[i], out sid))
                    {
                        inputShopid.Add(sid);
                    }
                }
            }
            //对参数做一些校验
            if (!string.IsNullOrEmpty(platformid) && inputShopid.Count != 1)
            {
                return FalidResult("通过商品Id同步必须选择一个店铺");
            }

            //当前平台下的店铺，改在GetCloudPlatformTypeShopsByFxUserId 方法里。
            //改在一个方法上，其他地方也能用到
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            
            //获取关联的店铺（同一个云端）
            var fxUserShopService = new FxUserShopService();
            var fxUserShops = fxUserShopService.GetCloudPlatformTypeShopsByFxUserId(fxUserId);

            //是否开启商品同步服务
            // var notAlibabaPlatform = fxUserShops.Any(m =>
            //     m.PlatformType == PlatformType.Pinduoduo.ToString() ||
            //     m.PlatformType == PlatformType.Jingdong.ToString() ||
            //     m.PlatformType == PlatformType.JingdongPurchase.ToString() ||
            //     m.PlatformType == PlatformType.KuaiTuanTuan.ToString());
            // if (!notAlibabaPlatform && IsEnabledProductSyncService())
            if (IsEnabledProductSyncService())
            {
                Log.Debug(() => $"商品同步(服务方式)，当前用户信息：{fxUserId}", $"ProductSync_{DateTime.Now:yyyy-MM-dd}.log");
                //系统店铺ID
                var systemShopId = SiteContext.Current.CurrentShopId;
                //相关键
                var syncMessageDeclaration = new SyncMessageDeclarationService(SyncBusinessTypes.ProductIncrementSync);
                var queueKey = syncMessageDeclaration.QueueKey;
                var counterKey = syncMessageDeclaration.CounterKey;
                //增加批次ID，用户ID
                var batchId = Guid.NewGuid().ToReplaceHyphen();
                //推送队列
                var serviceMessage = new ServiceProductMessageModel
                {
                    BatchId = batchId,
                    FxUserId = fxUserId,
                    TraceId = Request["traceId"],
                    Token = Request["token"],
                    DbName = Request["dbname"],
                    ShopId = systemShopId,
                    Time = DateTime.Now,
                    PlatformType = CustomerConfig.CloudPlatformType,
                    Parameter = new ProductSyncParameterModel
                    {
                        IsAuto = isAuto,
                        StatusType = statusType,
                        TimeStart = timeStart,
                        TimeEnd = timeEnd,
                        InputSId = inputsid,
                        PlatformId = platformid
                    }
                };
                RedisHelper.RPush(queueKey, serviceMessage);
                //同步任务日志
                var syncLogView = new SyncServiceTaskLogModel
                {
                    BusinessType = "Product",
                    TraceName = "TaskPush",
                    BatchId = serviceMessage.BatchId,
                    FxUserId = serviceMessage.FxUserId,
                    ShopId = serviceMessage.ShopId,
                    TraceId = serviceMessage.TraceId,
                    Token = serviceMessage.Token,
                    DbName = serviceMessage.DbName,
                    PlatformType = serviceMessage.PlatformType,
                    Time = serviceMessage.Time,
                    BeginTime = DateTime.Now,
                    EndTime = DateTime.Now
                };
                SyncServiceTaskDataEventTrackingService.Instance.WriteLog(syncLogView);
                //追加计数器
                try
                {
                    RedisHelper.HIncrBy(counterKey, "Totals");
                }
                catch
                {
                    // ignored
                }

                return SuccessResult(new { LastTime = lastTime });
            }

            Log.Debug(() => $"商品同步(站点方式)，当前用户信息：{fxUserId}", $"ProductSync_{DateTime.Now:yyyy-MM-dd}.log");
            var shopId = fxUserShops?.Select(s => s.ShopId).ToList();
            var newUserShops = fxUserShops;
            //如果传入了店铺,则只使用传入的店铺(前提是传入的店铺必须是该用户绑定的店铺)
            if (inputShopid != null && inputShopid.Count > 0)
            {
                shopId = shopId.Where(s => inputShopid.Any(a => a == s))?.ToList();
                newUserShops = fxUserShops.Where(m => inputShopid.Contains(m.ShopId)).ToList();
            }
            
            var shopList = _shopService.GetShopByIds(shopId);
            //shopList.ForEach(s =>
            //{
            //    s.FxUserIds = fxUserId;
            //});

            var guid = Guid.NewGuid().ToString();
            //通过路径流，找出需要追加同步的店铺,20220915,根据需求1012240,不在自动做关联店铺的商品同步,关联商品只需要同步订单的商品即可 20240906 跟新需求 1023801 增加自动关联店铺商品同步 (同步不动，屏蔽代码)
            //var pathFlowByShop = _pathFlowService.GetCloudPlatformTypeSourceShopIdByPathFlowUserId(fxUserId);
            //if (inputShopid.IsNullOrEmptyList() && pathFlowByShop.Count > 0)
            //{
            //    var addShop = new List<Shop>();
            //    pathFlowByShop.ForEach(s =>
            //    {
            //        var shops = shopList.FirstOrDefault(p => p.Id == s.Id);
            //        if (shops == null)
            //            addShop.Add(s);
            //    });
            //    if (addShop.Count > 0)
            //        shopList.AddRange(addShop);
            //    Log.Debug($"{guid} 当前用户信息：{fxUserId}，附加的路径流店铺id：{addShop.Select(t => $"{t.Id}：{t.NickName}").ToJson()}", "SyncProduct.txt");
            //}
            

            if (shopList != null && shopList.Any())
            {
                var sids = shopList.Select(a => a.Id).ToList();
                //var syncStatus = new SyncStatusService().GetSyncStatusBySid(sids, ShopSyncType.Product);
                var syncStatus =
                    new SyncStatusService().GetSyncStatusByFxUserIdWithUserShopsWithSyncType(fxUserId, newUserShops, 2);
                if (syncStatus != null && syncStatus.Any())
                {
                    #region 自动同步-按规则过滤
                    //自动同步规则如下：
                    //1.根据用户上次商品同步时间，超过72小时
                    //2.过滤授权过期失效的店铺
                    //3.排除拼多多（原因：接口要收费）
                    if (isAuto)
                    {
                        var hours = CustomerConfig.SyncProductHours;
                        shopList = shopList.Where(a => a.PlatformType != PlatformType.Pinduoduo.ToString() && a.PlatformType != PlatformType.KuaiTuanTuan.ToString())?.ToList();
                        var needShopIds = syncStatus.Where(a => a.StartSyncTime < DateTime.Now.AddHours(-1 * hours) && a.LastSyncStatus != "Pending" && !a.ShopIsExpired)?.Select(b => b.ShopId).ToList();
                        shopList = shopList.Where(a => needShopIds.Contains(a.Id))?.ToList();
                        if (!shopList.Any())
                            return SuccessResult(new { LastTime = "系统自动同步：无满足条件的店铺" });
                    }
                    #endregion

                    shopList.ForEach(s =>
                    {
                        s.FxUserIds = syncStatus.FirstOrDefault(x => x.ShopId > 0 && x.ShopId == s.Id)?.FxUserId ?? fxUserId;
                        s.SyncStatusList = syncStatus.Where(a => a.ShopId == s.Id).ToList();
                    });
                    //针对抖店做二次过滤
                    shopList = _shopService.FilterTouTiaoShop(shopList, CustomerConfig.CloudPlatformType);
                    //排除过期的店铺
                    //shopList = shopList
                    //    .Where(m => !(m.ProductSyncStatus?.ShopIsExpired ?? false) &&
                    //                !(m.OrderSyncStatus?.ShopIsExpired ?? false)).ToList();
                }
            }


            Log.Debug(() => $"{guid} 当前用户信息：{fxUserId}，完整的同步店铺：{shopList.Select(t => $"{t.Id}：{t.NickName}").ToJson()}",
                "SyncProduct.txt");
            //改成线程的方式，实时的话，前端会超时。
            //使用线程后，会有个任务一直检测是否成功。成功了会把同步按钮恢复。new SyncFxProductService()
            ThreadPool.QueueUserWorkItem(state =>
             {
                 try
                 {
                     //1.同步商品：自己绑定店铺+路径上商家店铺
                     var shopGroup = shopList.GroupBy(x => x.FxUserIds).ToList();
                     //var fxUsers = new UserFxService().GetUserFxs(shopGroup.Select(f => f.Key));
                     var fxUserIds = shopGroup.Select(f => f.Key).Distinct().ToList();
                     var fxUsers = new UserFxService().GetListByIdsWithCache(fxUserIds);
                     var fxUserDict = fxUsers.ToDictionary(f => f.Id, f => f);
                     foreach (var sg in shopGroup)
                     {
                         var sameFxShops = sg.ToList();
                         UserFx fxUser;
                         if (fxUserDict.TryGetValue(sg.Key, out fxUser) == false)
                         {
                             Log.Debug(
                                 () =>
                                     $"{guid} 当前用户信息：{fxUserId}，同步的用户id：{fxUser.Id}+{fxUser.NickName},找不到用户信息的店铺：{sameFxShops.Select(t => $"{t.Id}：{t.NickName}：{t.FxUserIds}").ToJson()}",
                                 "SyncProduct.txt");
                             continue;
                         }

                         Log.Debug(()=>$"{guid} 当前用户信息：{fxUserId}，同步的用户id：{fxUser.Id}+{fxUser.NickName},同步的店铺：{sameFxShops.Select(t => $"{t.Id}：{t.NickName}").ToJson()}", "SyncProduct.txt");
                         foreach (var s in sameFxShops)
                         {
                             if (s.FxUserIds > 0)
                             {
                                 var op = OptimisticLockOperationType.FxProductSync;
                                 var opId = $"FX{s.Id}";
                                 var result = _commonSettingService.ExecuteWithOptimisticLock(() =>
                                 {
                                     //实例化商家的SiteContext，分库分表需要保存到不同库      
                                     var siteContext = new SiteContext(fxUser);                          
                                     var syncFxProductService = new SyncFxProductService(s.FxUserIds);
                                     syncFxProductService.SyncProduct(s, statusType, timeStart, timeEnd, platformid,
                                         true);
                                     return true;
                                 }, () => { Log.Debug(() => $"店铺【{s.Id}】同步完成后，同步商品动作未获取到锁"); }, op, opId);
                             }
                             else
                                 Log.WriteError($"同步商品-店铺id:【{s.Id}】，用户id为0，shopList：{shopList.ToJson()}；");

                             //Log.WriteError($"同步商品-店铺id:【{s.Id}】，用户id为0，shopList：{shopList.ToJson()}；pathFlowByShop：{pathFlowByShop.ToJson()}");
                             Log.Debug(
                                 () =>
                                     $"{guid} 当前用户信息：{fxUserId}，同步的用户id：{fxUser.Id}+{fxUser.NickName},同步的店铺：{s.Id}：{s.NickName}",
                                 "SyncProduct.txt");
                         }
                     }
                 }
                 catch (Exception ex)
                 {
                     lastTime = "error";
                     Log.WriteError($"同步商品,错误详情：{ex}");
                 }
             });

            return SuccessResult(new { LastTime = lastTime });
        }

        /// <summary>
        /// 同步单个商品
        /// http://fendan.dgjapp.com/Product/SyncSingleProduct?token=F721C4B8F2292D61B32646A40756C5EB&dbname=wdJM8OZZiF60rm73/6ttwOCsQNNRTq3m&pid=3793425503747&sid=475708
        /// </summary>
        /// <param name="pid"></param>
        /// <param name="sid"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult SyncSingleProduct(string pid, int sid)
        {
            try
            {
                if (pid.IsNullOrEmpty())
                    return FalidResult("商品Id不能为空");
                if (sid == 0)
                    return FalidResult("店铺Id不能为空");
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var bindShops = new FxUserShopService().GetBindShops(fxUserId, new List<int>() { sid });
                var shop = bindShops?.FirstOrDefault();
                if (shop == null)
                    return FalidResult($"店铺【{sid}】非当前账号绑定店铺");

                var service = PlatformFactory.GetPlatformService(shop);
                var syncFxProductService = new SyncFxProductService(fxUserId);
                syncFxProductService.SyncProduct(shop, platformId: pid);

                //var product = syncFxProductService.SyncSingleProduct(service, pid);
                //if (product == null)
                //    return FalidResult($"没有找到商品【{pid}】");

                //var productService = new ProductFxService();
                //var productFxList = productService.TransferModelToProductFx(new List<Product> { product }, fxUserId, shop.PlatformType);
                //productService.BulkMerger(productFxList, shop.Id);//保存与更新
                return SuccessResult("商品同步成功");
            }
            catch (Exception ex)
            {
                Log.WriteError($"商品：{pid}，店铺：{sid}，同步失败：{ex}");
            }
            return FalidResult("商品同步失败");
        }

        [LogForOperatorFilter("修改商品简称重量")]
        [FxMigrateLockFilter()]
        [FxAuthorize()]
        public ActionResult SaveShortTitleOrWeight(UpdateShortTitleAndWeightModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            new ProductInfoFxService().SaveShortTitleOrWeight(fxUserId, model.UpdateModels);

            return SuccessResult();
        }

        [LogForOperatorFilter("同步对方商品简称")]
        [FxMigrateLockFilter()]
        [FxAuthorize(FxPermission.SaveProductShortTitleOrWeight)]
        public ActionResult SyncShortTitle(List<string> productCodes, List<string> skuCodes, List<string> noCheckedProductCodes)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _productInfoFxService.SyncShortTitleofProductIndex(productCodes, skuCodes, noCheckedProductCodes, fxUserId);

            return SuccessResult();
        }

        [LogForOperatorFilter("商品列表-同步对方结算价")]
        [FxMigrateLockFilter()]
        [FxAuthorize(FxPermission.SetProductSettlementPrice)]
        public ActionResult SyncSettlementPrice(List<string> productCodes, List<string> skuCodes, List<string> noSyncSkuCodes)
        {
            if (productCodes == null)
                productCodes = new List<string>();
            if (noSyncSkuCodes == null)
                noSyncSkuCodes = new List<string>();
            if (skuCodes == null)
                skuCodes = new List<string>();

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _settlementProductSkuService.SyncSettlementPrice(productCodes, skuCodes, noSyncSkuCodes, fxUserId);

            return SuccessResult();
        }

        [LogForOperatorFilter("商品批量编辑-同步对方结算价")]
        [FxMigrateLockFilter()]
        [FxAuthorize(FxPermission.SetProductSettlementPrice)]
        public ActionResult SyncSettlementPriceofBatchEdit(List<string> skuCodes)
        {
            if (skuCodes == null || !skuCodes.Any())
                return SuccessResult();

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _settlementProductSkuService.SyncSettlementPriceBySkuCodes(skuCodes, fxUserId);

            return SuccessResult();
        }

        [LogForOperatorFilter("商品批量编辑-同步对方简称")]
        [FxMigrateLockFilter()]
        [FxAuthorize(FxPermission.SaveProductShortTitleOrWeight)]
        public ActionResult SyncShortTitleofBatchEdit(List<string> productCodes, List<string> skuCodes, List<string> noCheckedProductCodes)
        {
            if ((productCodes.IsNullOrEmpty()) && (skuCodes.IsNullOrEmpty()))
                return FalidResult("未查询到指定商品/规格");

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _productInfoFxService.SyncShortTitleofBatchEdit(productCodes, skuCodes, noCheckedProductCodes, fxUserId);

            return SuccessResult();
        }

        [FxMigrateLockFilter()]
        public ActionResult CreateGoodsByProudct(string productcode)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            string ownercode = _service.GetOwnerCode(fxUserId);
            var warehouse = _service.GetWareHouseRelation(ownercode);
            if (warehouse == null || warehouse.Id < 1)
            {
                return FalidResult("1");//跳转到仓库页面
            }
            else if (warehouse.Status == "Disabled")
            {
                return FalidResult("2", warehouse.WareHouseCode);//提示框启用
            }
            else if (warehouse.Status != "Enabled")
            {
                return FalidResult("1");//跳转到仓库页面
            }
            var productInfo = _productFxService.GetOne(productcode);
            var index = 1;
            productInfo.Skus.ForEach(x =>
            {
                x.CargoNumber += $"{productInfo.Id}-{index}";
                index++;
            });
            if (productInfo != null)
            {
                // 只传需要的字段，减少不必要存储
                var product = new ProductGetOneViewModel
                {
                    CargoNumber = productInfo.CargoNumber,
                    Subject = productInfo.Subject,
                    ImageUrl = productInfo.ImageUrl,
                    Skus = productInfo.Skus.Select(x => new ProductGetOneViewModel.ProductGetOneSkusViewModel
                    {
                        CargoNumber = x.CargoNumber,
                        SkuCode = x.SkuCode,
                        ImgUrl = x.ImgUrl,
                        Name = x.Name,
                        SalePrice = x.SalePrice,
                    }).ToList()
                };
                _service.ProductInfoTemp(new Warehouse.Model.Request.ProductInfoTempRequest { ProductCode = productInfo.ProductCode, Content = product.ToJson() });
            }

            return SuccessResult();
        }

        public ActionResult LoadReferenceConfig()
        {
            var refType = Request["RefType"].ToInt();
            var refCodes = Request["RefCodes"].ToList<string>();
            if (refCodes == null || refCodes.Any() == false)
                return SuccessResult();

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var _pathFlowRefService = new PathFlowReferenceService();

            var configs = _pathFlowRefService.GetPathFlowReferenceConfig(refCodes, fxUserId).ToList();
            if (refType == 2)
                configs = configs.Where(x => x.FromType != 1).ToList();
            var mergerConfigs = new List<PathFlowReferenceConfigModel>();
            configs.GroupBy(x => new { x.SupplierId, x.ConfigType, x.Config }).ToList().ForEach(x =>
            {
                var newConfig = new PathFlowReferenceConfigModel
                {
                    SupplierId = x.Key.SupplierId,
                    //SupplierName = x.Key.SupplierName,
                    //IsDefault = x.Key.IsDefault == null ? true : x.Key.IsDefault,
                    Config = x.Key.Config,
                    ConfigType = x.Key.ConfigType,
                    //MergerRelationCodes = x.Select(xx => new KeyValuePair<string, string>(xx.PathFlowRefCode, xx.RelationCode)).ToList()
                };
                mergerConfigs.Add(newConfig);
            });
            var supplierIds = configs.Select(x => x.SupplierId).Distinct().ToList();
            var suppliers = new UserFxService().GetUserFxs(supplierIds);//new SupplierUserService().GetSupplierList(fxUserId, null, null, 1, 10000);
            var suppliersinfo = new SupplierUserService().GetSupplierUserByIds(supplierIds, fxUserId, 1);
            var supplierDic = suppliers?.Select(x => new { x.NickName, x.Mobile, FxUserId = x.Id }).Distinct().ToDictionary(x => x.FxUserId, x => x.Mobile);
            
            mergerConfigs.ForEach(x =>
            {
                if (supplierDic.ContainsKey(x.SupplierId) && suppliersinfo.Exists(f => f.SupplierFxUserId == x.SupplierId))
                {
                    var info = suppliersinfo.Find(f => f.SupplierFxUserId == x.SupplierId);
                    var name = supplierDic[x.SupplierId];
                    if (!SiteContext.HasPermission(FxPermission.SupplierAccount))
                    {
                        name = Utility.SubAccount.EncryptUtil.EncryptAccount(supplierDic[x.SupplierId]);
                    }
                    x.SupplierName = name + (info.RemarkName.IsNullOrEmpty() ? "" : "(" + info.RemarkName + ")");
                }
            });

            return SuccessResult(mergerConfigs);
        }

        [FxAuthorize(FxPermission.TriggerBindSupplier)]
        public ActionResult BindLog(string refCode)
        {
            if (refCode.IsNullOrEmpty())
                return FalidResult("未找到商品");

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            PathFlowChangeLogService changeLogService = new PathFlowChangeLogService();
            var datas = changeLogService.LoadLogsV2(refCode, fxUserId);

            return SuccessResult(datas);
        }

        //[IgnoreToken]
        public ActionResult GetThreeAreaInfos()
        {
            //this.ValidLoginToken();
            var _areaCodeInfoService = new AreaCodeInfoService();
            var areaCodeInfos = _areaCodeInfoService.GetTreeAreaInfoList();
            return Json(areaCodeInfos);
        }

        /// <summary>
        /// 商品批量编辑页面查询商品数据
        /// </summary>
        /// <param name="productCodes">选中的product和选中sku对应的product</param>
        /// <param name="skuCodes">选中的sku</param>
        /// <param name="noCheckedProductCodes">未选中的product</param>
        /// <param name="noCheckedSkuCodes">未选中的sku</param>
        /// <param name="queryType">查询类型，对应前端 type = product,单个商品下所有sku，type = sku，单个sku，type = checked，勾选批量编辑</param>
        /// <returns></returns>
        [FxAuthorize(FxPermission.SetProductSettlementPrice)]
        public ActionResult GetSettlementProductList(List<string> productCodes, List<string> skuCodes, List<string> noCheckedSkuCodes, List<string> noCheckedProductCodes, string queryType = "checked")
        {
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var data = _settlementProductSkuService.GetSettlementProductList(productCodes, skuCodes, noCheckedSkuCodes, noCheckedProductCodes, fxUserId, queryType);
            return Json(data);
        }

        public ActionResult UpdateOldData()
        {
            if (CustomerConfig.IsDebug == false)
                return FalidResult("无权限");
            var url = Request.Url.AbsoluteUri.ToString2().ToLower().ReplaceList(new List<string> { "http://", "https://" }, "");
            if (url.Contains("tfx") == false && url.Contains("fendan") == false)
                return FalidResult("无权限");

            new PathFlowReferenceService().UpdateOldDataForMutiBindSupplier();
            return SuccessResult();
        }

        public ActionResult CheckIsBindSku()
        {
            var pcodes = Request["pcodes"].ToString2().ToList<string>();
            if (pcodes == null || pcodes.Any() == false)
                return FalidResult("请先选择商品");

            var isBindSku = new PathFlowReferenceService().CheckIsBindSku(pcodes);
            return Json(isBindSku);
        }

        /// <summary>
        /// 逻辑删除商品
        /// </summary>
        /// <param name="productCodes"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        [FxAuthorize()]
        public ActionResult DeleteProduct(string productCodes)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var productCodeList = productCodes.Split(',').ToList();
            var res = _productFxService.SetDelete(productCodeList, fxUserId);
            if (res.Success)
            {
                return SuccessResult("删除成功");
            }
            else
            {
                return FalidResult(res.Message, data: res.Data);
            }
        }
        /// <summary>
        /// 恢复商品
        /// </summary>
        /// <param name="productCodes"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult RecoveryProduct(string productCodes)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var productCodeList = productCodes.Split(',').ToList();
            var res = _productFxService.Recovery(productCodeList, fxUserId);
            if (res.Success)
            {
                return SuccessResult("恢复成功");
            }
            else
            {
                return FalidResult(res.Message, data: res.Data);
            }
        }

        /// <summary>
        /// 线下单商品库选择
        /// </summary>
        /// <returns></returns>
        public ActionResult OfflineProduct()
        {
            LoadDefaultConfig();
            return View();
        }


        /// <summary>
        /// 生成货源
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("生成货源")]
        public ActionResult GenerateBaseProduct(List<string> productCodes)
        {
            var dbName = Request["dbName"].IsNotNullOrEmpty() ? DES.DecryptDES(Request["dbName"],CustomerConfig.LoginCookieEncryptKey):string.Empty ;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var fxShopId = SiteContext.Current.CurrentShopId;
            productCodes = productCodes.Where(a => a.IsNotNullOrEmpty()).Distinct().ToList();

            var asyncAllProductIds = new List<string>();
            var asyncSuessProductIds = new List<string>();
            var asyncFaileProductIds = new List<string>();
            var mergeOnlineSuccess = new List<string>();
            var mergeOnlineFaile = new List<string>();
            var mergeofflineSuccess = new List<string>();
            var mergeofflineFaile = new List<string>();
            var errorSyncMessage = string.Empty;
            var errorMergMessage = string.Empty;

            if (productCodes == null)
            {
                return FalidResult("商品编码为空，生成货源失败！");
            }
            if (productCodes.Count() == 0)
            {
                return FalidResult("商品编码为空，生成货源失败！");
            }
            if (productCodes.Count() > 200)
            {
                return FalidResult("一次生成货源不能超过200个，生成货源失败！");
            }

            // 判断商品是否可以引用
            var checkProductCodes = _productFxService.CheckIsEnableQuote(fxUserId, productCodes);
            if (checkProductCodes.Count == 0)
            {
                return FalidResult("当前分销商已关闭商品资料共享使用，如有需求，请联系对方在分销设置内开启权限！");
            }

            productCodes = checkProductCodes;
            
            // 生成默认仓
            //_service.GenerateDefaultStore();后置处理
            // 线上商品
            List<Product> syncProducts = new List<Product>();
            // 线下商品
            List<ProductFx> productInfos = new List<ProductFx>();
            // 基础商品
            List<BaseProductSkuAddModel> baseProducts = new List<BaseProductSkuAddModel>();

            try
            {
                // 商品信息
                productInfos = _productFxService.GetOneDetail(productCodes, fxUserId,true);
                // 商品信息--过滤路径流中
                productInfos = _productFxService.GetProductByDownFxUser(fxUserId, productInfos, "all");

                if (productInfos == null || productInfos.Count == 0)
                {
                    return FalidResult("未找到对应的商品信息！");
                }
                else
                {
                    // 补充商品：成本价格、分销价格、采购价格
                    var skusData = productInfos.SelectMany(p => p.Skus).ToList();
                    var skus = skusData?? new List<ProductSkuFx>();
                    var skuCodes = skus.Select(p => p.SkuCode).Where(p => p.IsNotNullOrEmpty()).Distinct().ToList();
                    var allPrice = _productFxService.GetDefaultPrices(skuCodes, fxUserId);
                    // 分销价格
                    var settlemenPrices = allPrice.Item1;
                    // 结算价格
                    var distributePrices = allPrice.Item2;
                    // 成本价格
                    var costPrices = allPrice.Item3;

                    foreach (var sku in skus)
                    {
                        var settl = settlemenPrices.Where(p => p.ProductSkuCode == sku.SkuCode).OrderByDescending(p => p.Id).FirstOrDefault();
                        var distr = distributePrices.Where(p => p.ProductSkuCode == sku.SkuCode).OrderByDescending(p => p.Id).FirstOrDefault();
                        var costs = costPrices.Where(p => p.ProductSkuCode == sku.SkuCode).OrderByDescending(p => p.Id).FirstOrDefault();
                        // 成本
                        sku.DefaultSettlementPrice = costs?.Price;
                        // 分销
                        sku.SalePrice = distr?.Price;
                        // 采购
                        sku.PurchasePrice = settl?.Price;
                    }

                    // 商品绑定信息
                    var productConfigs = new List<PathFlowReferenceConfigModel>();
                    //var productConfigs =  _pathFlowRefService.GetPathFlowReferenceConfig(productCodes, fxUserId).ToList();

                // 商品原始店铺
                // 1.线下单商品不同步
                // 2.已失效删除商品不同步
                var sids = productInfos
                    .Where(a => a.ShopId != 0 && a.PlatformType != PlatformType.Virtual.ToString())
                    .Where(a => a.SystemStatus != -1 && !a.Skus.Any(s => s.SystemStatus == -1))
                    .Where(a => a.Status == "published")
                    .Select(a => a.ShopId)
                    .Distinct()
                    .ToList();
                    var shops = _shopService.GetShopByIds(sids);

                    if (shops.Count == 0)
                    {
                        Log.WriteLine($"未找到对应的商品店铺信息！");
                    }

                    #region 同步获取商品信息
                    foreach (var shop in shops)
                    {
                        var pids = productInfos
                        .Where(a => a.PlatformId.IsNotNullOrEmpty() && a.ShopId == shop.Id && a.PlatformType != PlatformType.Virtual.ToString())
                        .Where(a => a.SystemStatus != -1 && !a.Skus.Any(s => s.SystemStatus == -1))
                        .Where(a => a.Status == "published")
                            .Select(a => a.PlatformId)
                            .Distinct()
                            .ToList();
                        var service = PlatformFactory.GetPlatformService(shop, true);

                        if (pids.Count > 0)
                        {
                            Parallel.ForEach(pids, new ParallelOptions { MaxDegreeOfParallelism = 5 }, pid =>
                            {
                                asyncAllProductIds.Add(pid);
                                try
                                {
                                    var product = service.SyncProduct(pid);
                                    if (product != null)
                                    {
                                        syncProducts.Add(product);
                                        asyncSuessProductIds.Add(pid);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    errorSyncMessage += $"同步接口获取商品信息失败，失败商品ProductId：{pid},失败原因：{ex.Message}, ";
                                    Log.WriteError($"同步接口获取商品信息失败，失败商品ProductId：{pid},失败原因：{ex.Message}");
                                    asyncFaileProductIds.Add(pid);
                                }
                            });
                        }
                    }
                    #endregion
               
                    #region 基础信息组装
                    if (syncProducts != null || syncProducts.Count > 0)
                    {
                        // 基础信息组装
                        Parallel.ForEach(syncProducts, new ParallelOptions { MaxDegreeOfParallelism = 5 }, syncProduct =>
                        {
                            var productInfo = productInfos.FirstOrDefault(a => a.PlatformId == syncProduct.PlatformId);
                            if (productInfo != null)
                            {
                                var productCode = productInfo.ProductCode;
                                var sourceUserId = productInfo.SourceUserId;
                                try
                                {
                                    var baseProduct = _productFxService.MergeOnline(syncProduct, productInfo, productCode, sourceUserId, productConfigs);
                                    if (baseProduct != null)
                                    {
                                    if (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString() && syncProduct.Status == "2")
                                    {
                                        // 抖店云排除已删商品
                                        return;
                                    }
                                    else
                                    {
                                        baseProducts.Add(baseProduct);
                                        mergeOnlineSuccess.Add(productCode);
                                    }
                                }
                            }
                                catch (Exception ex)
                                {
                                    //errorMergMessage += $"组装同步接口商品信息失败详情，失败商品Id;{syncProduct.PlatformId},失败原因：{ex.Message}, ";
                                    Log.WriteError($"组装同步接口商品信息失败详情，失败商品Id;{syncProduct.PlatformId},失败原因：{ex.Message}");
                                    mergeOnlineFaile.Add(productCode);
                                }
                            }
                        });
                    }
                //Log.WriteLine(
                //   $"同步接口获取商品信息：" +
                //   $"总条数：{asyncAllProductIds.Count}, " +
                //   $"成功条数：{asyncSuessProductIds.Count}，" +
                //   $"失败条数：{asyncFaileProductIds.Count}，" +
                //   $"成功商品ProductId：{asyncSuessProductIds.ToJson()}," +
                //   $"失败商品ProductId：{asyncFaileProductIds.ToJson()}");

                    var successProductCodes = baseProducts.Select(p => p.FromProductUid).ToList();

                    var failProductCodes = productCodes.Except(successProductCodes).ToList();
                    // 同步获取商品失败
                    if (failProductCodes.Count > 0)
                    {
                        var failProductInfos = productInfos.Where(p => failProductCodes.Contains(p.ProductCode)).ToList();
                        // 基础信息组装
                        Parallel.ForEach(failProductInfos, new ParallelOptions { MaxDegreeOfParallelism = 5 }, productInfo =>
                        {
                            var productCode = productInfo.ProductCode;
                            var sourceUserId = productInfo.SourceUserId;
                            try
                            {
                                var baseProduct = _productFxService.Mergeoffline(productInfo, productCode, sourceUserId, productConfigs);
                                if (baseProduct != null)
                                {
                                    baseProducts.Add(baseProduct);
                                    mergeofflineSuccess.Add(productCode);
                                }
                            }
                            catch (Exception ex)
                            {
                                errorMergMessage += $"组装数据库商品信息失败，失败商品Id;{productInfo.PlatformId},失败原因：{ex.Message}, ";
                                Log.WriteError($"组装数据库商品信息失败，失败商品Id;{productInfo.PlatformId},失败原因：{ex.Message}");
                                mergeofflineFaile.Add(productCode);
                            }
                        });

                        //Log.WriteLine(
                        //   $"组装数据库商品信息：" +
                        //   $"总条数：{failProductInfos.Count}, " +
                        //   $"成功条数：{mergeofflineSuccess.Count}，" +
                        //   $"失败条数：{mergeofflineFaile.Count}，" +
                        //   $"成功商品ProductId：{mergeofflineSuccess.ToJson()}," +
                        //   $"失败商品ProductId：{mergeofflineFaile.ToJson()}");
                    }
                    #endregion

                    var fountCodesY = baseProducts.Select(p => p.FromProductUid).ToList(); ;
                    var fountCodesN = productCodes.Except(fountCodesY).ToList();
                List<BaseProductEntity> results = null;

                    if (baseProducts != null && baseProducts.Count > 0)
                    {
                        // 当前为精选平台 
                        if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                        {
                            try
                            {
                                results = new BaseProductSkuService().CreateBaseProductSkus(baseProducts, fxUserId);
                            }
                            catch (Exception ex)
                            {

                                Log.WriteError($"基础资料生成失败：{ex.Message}");
                                throw ex;
                            }
                        }

                        // 跨云添加基础资料
                        else
                        {
                            var apiUrl = "/BaseProductApi/GenerateBaseProductV2";
                            var aliCloudHost = string.Empty;
                            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
                            {
                                aliCloudHost = CustomerConfig.AlibabaMessageDomainForPdd.TrimEnd("/") + apiUrl;
                            }
                            else
                            {
                                aliCloudHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/") + apiUrl;
                            }
                            try
                            {
                                results = Common.PostFxSiteApi<List<BaseProductSkuAddModel>, List<BaseProductEntity>>(aliCloudHost, fxUserId, baseProducts, "跨云平台基础资料生成", isEncrypt: true);
                            }
                            catch (Exception ex)
                            {

                                Log.WriteError($"基础资料跨云生成失败：{ex.Message}");
                                throw ex;
                            }
                        }

                    #region 由店铺商品生成基础商品，添加生成记录
                    Task.Run(() =>
                    {
                        try
                        {
                            var connectionString = string.Empty;
                            if(dbName.IsNotNullOrEmpty())
                                connectionString = SiteContext.Current.CurrentDbAreaConfig?.FirstOrDefault(d => d.DbNameConfig.DbName == dbName)?.ConnectionString;


                            new GenerateBaseProductRecordService(connectionString).SaveGenerateBaseProductRecord(results);
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"保存店铺商品生成基础商品记录发生异常：{ex}", ModuleFileName.BaseProduct);
                        }
                    });
                    #endregion

                    }
                    var messageError = string.Empty;
                    if (errorMergMessage.IsNotNullOrEmpty())
                    {
                        messageError = $"失败原因：{errorMergMessage}";
                    }
                    var message =
                        $"成功：{fountCodesY.Count}条，" +
                        $"失败：{fountCodesN.Count}条，" + messageError;
                    return SuccessResult(message);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"生成货源失败:{ex.ToJson()}");
                return FalidResult("生成失败");
            }
        }

        /// <summary>
        /// 获取京东供销平台绑定供应商提醒
        /// </summary>
        /// <returns></returns>
        public ActionResult JingdongPurchaseBindSupplierRemind(string productCodes)
        {
            if (productCodes.IsNullOrEmpty())
                return SuccessResult(false);
            var shopId = SiteContext.Current.CurrentShopId;
            var remind = _commonSettingService.GetBool("JingdongPurchaseBindSupplierRemind", shopId);
            if (!remind)
            {
                //需要提醒
                var products = _productFxService.GetFxProductListByCodes(productCodes.Split(',').ToList(), new List<string> { "Id", "PlatformType" });
                if (products.Any(t => t.PlatformType == PlatformType.JingdongPurchase.ToString()))
                {
                    return SuccessResult(true);
                }
            }
            return SuccessResult(false);
        }

        /// <summary>
        /// 店铺绑定厂家弹窗需要的数据
        /// </summary>
        /// <param name="fromType">来源，分销设置打开传0，商品列表打开传1</param>
        /// <returns></returns>
        public ActionResult GetShopBindSupplierData(int fromType)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var supplierUserService = new SupplierUserService();

            //店铺
            var _reqModel = new FxUserShopQueryModel { FxUserId = fxUserId, Status = FxUserShopStatus.Binded };
            List<FxUserShop> fxUserShops = fxUserShops = (_fxUserShopService.GetList(_reqModel)?.Item2 ?? new List<FxUserShop>()).ToList();

            //平台
            List<SuportPlatformAuthEntryModel> pts = new List<SuportPlatformAuthEntryModel>();

            //商品列表打开
            if (fromType == 1)
            {
                if (CustomerConfig.CloudPlatformType == PlatformType.Pinduoduo.ToString())
                    fxUserShops = fxUserShops.Where(x => 
                        CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(x.PlatformType) ||
                        x.PlatformType == PlatformType.Virtual.ToString()).ToList();
                else if (CustomerConfig.CloudPlatformType == PlatformType.Jingdong.ToString())
                    fxUserShops = fxUserShops.Where(x => 
                        CustomerConfig.FxJingDongCloudPlatformTypes.Contains(x.PlatformType) || 
                        x.PlatformType == PlatformType.Virtual.ToString()).ToList();
                else if (CustomerConfig.CloudPlatformType == PlatformType.TouTiao.ToString())
                    fxUserShops = fxUserShops.Where(x =>
                        CustomerConfig.FxDouDianCloudPlatformTypes.Contains(x.PlatformType) ||
                        x.PlatformType == PlatformType.Virtual.ToString()).ToList();
                else
                    fxUserShops = fxUserShops.Where(x =>
                        CustomerConfig.FxJingDongCloudPlatformTypes.Contains(x.PlatformType) == false &&
                        CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(x.PlatformType) == false &&
                        CustomerConfig.FxDouDianCloudPlatformTypes.Contains(x.PlatformType) == false).ToList();
            }
            else
            {
                //平台
                pts = CustomerConfig.GetAllPlatformAuthLinks().ToList();
                if (!pts.Any(x => x.PlatformType == "Toutiao"))
                {
                    pts.Add(new SuportPlatformAuthEntryModel
                    {
                        PlatformType = "TouTiao",
                        Name = "抖音",
                        Index = 4
                    });
                }
                if (!pts.Any(x => x.PlatformType == PlatformType.Virtual.ToString()))
                {
                    pts.Add(new SuportPlatformAuthEntryModel
                    {
                        PlatformType = "Virtual",
                        Name = "线下单",
                        Index = 6
                    });
                }
            }

            //厂家数据源-只显示绑定成功的
            var suppliers = supplierUserService.GetSupplierList(fxUserId, status: AgentBingSupplierStatus.Binded, onlyGetCurDb: fromType == 1,needEncryptAccount:true)
                                               ?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, Status = x.Status, IsTop = x.IsTop, x.IsFilter })
                                               .Distinct().ToList();
            var shopList = fxUserShops?.Select(s => new { s.ShopId, s.NickName, s.PlatformType }).ToList();
            var platformTypes = pts.Where(x => shopList.Any(n => n.PlatformType.ToLower() == x.PlatformType.ToLower())).OrderBy(x => x.Index).Select(x => new { x.PlatformType, x.Name }).ToList();

            return SuccessResult(new { Shops = shopList, PlatformTypes = platformTypes, Suppliers = suppliers });
        }

        /// <summary>
        /// 获取店铺绑定厂家列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public ActionResult GetShopDefaultSupplierList(ShopDefaultSupplierListRequestModel request)
        {
            if ((request.FromType != 0 && request.FromType != 1) || request.PageIndex < 1 || request.PageSize > 500)
            {
                return FalidResult("参数有误");
            }
            var tuple = _productFxService.GetShopDefaultSupplierList(request);
            return SuccessResult(new { Total = tuple.Item1, List = tuple.Item2 });
        }


        /// <summary>
        /// 中转按店铺商品绑定厂家，分销设置页面使用
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        
        public ActionResult TransShopBindSupplier(BindSupplierRequestModel request)
        {
            if (request.BindShopId <1)
            {
                return FalidResult("请选择需要绑定的店铺");
            }

            int fxUserId = SiteContext.Current.CurrentFxUserId;

            var _reqModel = new FxUserShopQueryModel { FxUserId = fxUserId, ShopId = request.BindShopId };
            var fxUserShop = (_fxUserShopService.GetList(_reqModel)?.Item2 ?? new List<FxUserShop>()).FirstOrDefault();

            if (fxUserShop == null)
            {
                return FalidResult("无店铺信息");
            }

            request.IP = Request.UserHostAddress;

            //跨云处理
            AjaxResult ajaxRes = null;
            var apiUrl = "/ProductApi/TriggerShopBindSupplier";
            BindSupplierRequestForApiModel requestApiModel = new BindSupplierRequestForApiModel();
            requestApiModel.CurFxUserId = fxUserId;
            requestApiModel.RequestModel = request;
            if (fxUserShop.PlatformType == PlatformType.Pinduoduo.ToString() || fxUserShop.PlatformType == PlatformType.KuaiTuanTuan.ToString())
            {
                
                var pddCloudHostUrl = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/") + apiUrl;
                Log.Debug($"店铺商品绑定厂家，转发到拼多多云，{fxUserId}，{request.ToJson()},{pddCloudHostUrl}");
                //转发到拼多多云处理
                ajaxRes = Common.PostFxSiteApi<BindSupplierRequestForApiModel, AjaxResult>(
                   pddCloudHostUrl,
                   fxUserId,
                   requestApiModel,
                   "店铺绑定厂家代发-转发到拼多多云");
            }
            else if (fxUserShop.PlatformType == PlatformType.Jingdong.ToString())
            {
               
                var jdCloudHostUrl = CustomerConfig.JingdongFenFaSystemUrl.TrimEnd("/") + apiUrl;
                Log.Debug($"店铺商品绑定厂家，转发到京东云，{fxUserId}，{request.ToJson()}.{jdCloudHostUrl}");
                ajaxRes = Common.PostFxSiteApi<BindSupplierRequestForApiModel, AjaxResult>(
                    jdCloudHostUrl,
                    fxUserId,
                    requestApiModel,
                    "店铺绑定厂家代发-转发到京东云");
            }
            else if (fxUserShop.PlatformType == PlatformType.TouTiao.ToString())
            {
                
                var toutiaoCloudHostUrl = CustomerConfig.ToutiaoFenFaSystemUrl.TrimEnd("/") + apiUrl;
                Log.Debug($"店铺商品绑定厂家，转发到抖店云，{fxUserId}，{request.ToJson()}，{toutiaoCloudHostUrl}");
                ajaxRes = Common.PostFxSiteApi<BindSupplierRequestForApiModel, AjaxResult>(
                    toutiaoCloudHostUrl,
                    fxUserId,
                    requestApiModel,
                    "店铺绑定厂家代发-转发到抖店云");
            }
            else
            {
                //直接处理
                var _res = _productFxService.TriggerShopBindSupplier(request);
                if (_res.Success == false)
                {
                    return FalidResult(_res.Message);
                }
                return SuccessResult(_res.Data);
            }

            if (ajaxRes == null)
            {
                return FalidResult("操作失败");
            }

            if (ajaxRes.Success == false)
            { 
                return FalidResult(ajaxRes.Message);
            }


            return SuccessResult(ajaxRes.Data);
        }


        /// <summary>
        /// 店铺绑定厂家代发,商品列表页面使用
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        [IgnoreDoubleAuth]
        public ActionResult TriggerShopBindSupplier(BindSupplierRequestModel request)
        {
            //站点云平台
            var siteCloudPlatformType = CustomerConfig.CloudPlatformType;

            var dbname = Request["dbname"] ?? "";
            var token = Request["token"] ?? "";
            if (string.IsNullOrEmpty(token))
            {
                return FalidResult($"发往云平台{siteCloudPlatformType}的TriggerShopBindSupplier请求token丢失");
            }
            request.IP = Request.UserHostAddress;

            var _res =  _productFxService.TriggerShopBindSupplier(request,dbname);
            if (_res.Success == false)
            {
                return FalidResult(_res.Message);
            }

            return SuccessResult(_res.Data);
        }
    }

    public class UpdateShortTitleAndWeightModel
    {
        public UpdateShortTitleAndWeightModel()
        {
            UpdateModels = new List<SaveShortTitleOrWeightModel>();
        }

        public List<SaveShortTitleOrWeightModel> UpdateModels { get; set; }
    }
}