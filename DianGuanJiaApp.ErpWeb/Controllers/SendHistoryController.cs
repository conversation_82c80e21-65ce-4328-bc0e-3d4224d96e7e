
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    public class SendHistoryController : BaseController
    {
        private SendHistoryService _service = new SendHistoryService();
        private PrintTemplateService _printTemplateService = new PrintTemplateService();

        [FxAuthorize(FxPermission.SendHistoryIndex)]
        public ActionResult Index()
        {
            ViewBag.FromOrderPrint = Request["FromOrderPrint"].ToInt();
            return View();
        }


        /// <summary>
        /// 加载发往的省份
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadProvinces()
        {
            var shopIds = new List<int> { SiteContext.Current.CurrentShopId };
            var provinces = _service.GetToProvinces(shopIds);
            var selectItemList = new List<SelectListItem>() { new SelectListItem() { Text = "==所有省份==", Value = "0" } };
            if (provinces != null && provinces.Count > 0)
            {
                provinces.ForEach(item =>
                {
                    if (string.IsNullOrWhiteSpace(item) == false)
                        selectItemList.Add(new SelectListItem() { Text = item, Value = item });
                });
            }
            return Json(selectItemList);
        }

        /// <summary>
        /// 加载发货快递名称
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("发货记录查询模板条件")]
        public ActionResult LoadExpressNames()
        {
            int shopId = SiteContext.Current.CurrentShopId;
            var dataList = _printTemplateService.LoadExpressNamesByShopId(shopId);
            var selectItemList = new List<SelectListItem>() { new SelectListItem() { Text = "==所有快递==", Value = "0" } };
            if (dataList != null && dataList.Count > 0)
            {
                dataList.ForEach(item =>
                {
                    if (string.IsNullOrWhiteSpace(item) == false)
                        selectItemList.Add(new SelectListItem() { Text = item, Value = item });
                });
            }
            return Json(selectItemList);
        }


        /// <summary>
        /// 加载买家数/订单数
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("发货记录查询数据统计")]
        public ActionResult LoadStatisticsCount(SendHistoryRequestModel requestModel)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            requestModel.FxUserId = fxUserId;

            var isPdd = Request["Pt"]?.ToLower() == PlatformType.Pinduoduo.ToString().ToLower();
            var countResult = _service.StatisticsCount(requestModel, isPdd);
            var result = new { BuyerCount = countResult.Item1, OrderCount = countResult.Item2, ExpressCodeCount = countResult.Item3, LogicOrderCount = countResult.Item4 };
            return Json(result);
        }

        /// <summary>
        /// 加载列表数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        [LogForOperatorFilter("发货记录查询列表")]
        [PageDepthControlFilter]
        public ActionResult LoadList(SendHistoryRequestModel requestModel)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            requestModel.FxUserId = fxUserId;

            var isPdd = Request["Pt"]?.ToLower() == PlatformType.Pinduoduo.ToString().ToLower();
            var pageModel = _service.LoadList(requestModel, isPdd);

            FxPlatformEncryptService.EncryptSendHistory(pageModel.Rows, encryptSender: true);

            #region  收件人信息脱敏处理
            //EncryptionService DataMaskservice = new EncryptionService();
            //var noVirtualOrders = DataMaskservice.getPlatformType(pageModel.Rows);
            //EncryptionService.DataMaskingExpression(pageModel.Rows.Where(w=> noVirtualOrders.Any(a=>a.LogicOrderId == w.OrderId) )?.ToList());//EncryptionService.DataMaskingReflection(pageModel.Rows);

            var notEncryptOrders = pageModel.Rows.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.ReciverPhone?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
            notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
            if (notEncryptOrders.Any())
                EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);


            #endregion
            if (pageModel.Rows.IsNotNullAndAny())
            {
                pageModel.Rows.ForEach(t =>
                {
                    t.Weight = t.Weight.ConvertGToKg();
                });
            }
            return Json(pageModel);
        }

        public ActionResult GetTodaySendedCount()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            SendHistoryRequestModel requestModel = new SendHistoryRequestModel
            {
                FxUserId = fxUserId,
                StartDate = DateTime.Now.ToString("yyyy-MM-dd"),
                EndDate = DateTime.Now.ToString("yyyy-MM-dd 23:59:59"),
                PageSize = 1
            };

            var isPdd = Request["Pt"]?.ToLower() == PlatformType.Pinduoduo.ToString().ToLower();
            var pageModel = _service.LoadList(requestModel, isPdd);

            var shop = SiteContext.Current.CurrentLoginShop;
            if (shop.PlatformType == Data.Enum.PlatformType.Jingdong.ToString())
            {
                var pids = new List<string>();
                pageModel.Rows?.ForEach(t =>
                {
                    if (!string.IsNullOrEmpty(t.OrderJoin))
                    {
                        pids.AddRange(t.OrderJoin.Split(','));
                    }
                    else
                    {
                        pids.Add(t.OrderId.Trim('C'));
                    }
                    t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
                });
                jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
            }
            return Json(pageModel.Total);
        }
    }
}