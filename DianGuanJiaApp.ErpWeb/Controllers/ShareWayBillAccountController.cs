using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.WaybillService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using Microsoft.Ajax.Utilities;
using Newtonsoft.Json.Linq;
using NPOI.HPSF;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System.Threading;
using System.Data;

using DianGuanJiaApp.Data.Enum;
using NPOI.XSSF.UserModel;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Model;
using DianGuanJiaApp.Services.ServicesExtension;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    public class ShareWayBillAccountController : BaseController
    {
        private BranchShareRelationService _service = new BranchShareRelationService();
        private BranchShareRelationOtherInfoService _shareOtherInfoService = new BranchShareRelationOtherInfoService();
        private LogisticAddServiceService _logisticAddServiceService = new LogisticAddServiceService();
        private CaiNiaoAuthInfoService _caiNiaoAuthInfoService = new CaiNiaoAuthInfoService();
        private BranchShareRelationLogService _logService = new BranchShareRelationLogService();
        private CommService _commService = new CommService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private ShopService _shopService = new ShopService();
        private ShopRelationService _relationService = new ShopRelationService();
        private ExpressCpCodeMappingService _expressCpCodeMappingService = new ExpressCpCodeMappingService();
        private ExpressCompanyService _expressCompanyService = new ExpressCompanyService();
        private PrintTemplateService _printTemplateService = new PrintTemplateService();
        private ExportTaskService _exportTaskService = new ExportTaskService();
        private EBillAccountExtensionService _ebillAccountService = new EBillAccountExtensionService();
        //private BranchShareExportExcelTaskService _taskService = new BranchShareExportExcelTaskService();

        private LogisticBusinessTypesService _logisticBusinessTypesService = new LogisticBusinessTypesService();
        private LogisticPayTypesService _logisticPayTypesService = new LogisticPayTypesService();

        public ActionResult Index(int? id)
        {
            var hasPermission = SiteContext.HasPermission(FxPermission.ShareWayBillAccount);
            //hasPermission = true;
            ViewBag.ViewPermission = hasPermission == true ? "true" : "false";
            if (!hasPermission) return View();
            ViewBag.ActiveId = id ?? 1;
            var masterShop = SiteContext.Current.MasterShop;
            ViewBag.MainShop = new { masterShop.Id, masterShop.ShopName, masterShop.NickName, masterShop.ShopId }.ToJson();
            ViewBag.CanShareExpressAndBranch = (new TemplateSetController()).LoadExpressAndBranchByLoginShop();

            //最后导出时间               
            var currShopId = SiteContext.Current.CurrentShopId;
            var keys = new List<string> { "/Export/BranchShare/UpdateTime" };
            var commSets = _commonSettingService.GetSets(keys, currShopId);
            var exportUpdateTimeSet = commSets.FirstOrDefault(m => m.Key == "/Export/BranchShare/UpdateTime");
            var defaultExportUpdateTime = exportUpdateTimeSet?.Value.ToDateTime() ?? null;
            var defaultExportUpdateTimeSetVal = defaultExportUpdateTime == null ? "" : defaultExportUpdateTime.Value.ToString("yyyy-MM-ddTHH:mm:ss");
            // 默认导出间隔时长（300s）
            var defaultExportExpireSecondsSet = _commonSettingService.Get("/Export/BranchShare/ExpireSeconds", 0);
            var defaultExportExpireSeconds = defaultExportExpireSecondsSet?.Value.ToInt() ?? 0;
            defaultExportExpireSeconds = defaultExportExpireSeconds <= 0 ? 300 : defaultExportExpireSeconds;

            ViewBag.ExportUpdateTimeSet = defaultExportUpdateTimeSetVal;
            ViewBag.ExportExpireSeconds = defaultExportExpireSeconds;

            //前台获取导出任务
            var exportTask = _exportTaskService.GetExportTask(SiteContext.Current.CurrentShopId, ExportType.ErpBranchShare.ToInt());
            // 显示未完成任务或已完成一天前的导出任务
            exportTask = exportTask != null && ((exportTask.Status >= 0 && exportTask.Status < 4) || (exportTask.Status >= 4 && exportTask.UploadToServerTime != null && DateTime.Now < exportTask.UploadToServerTime.Value.AddDays(1))) ? exportTask : null;
            var task = GetExportTaskToWeb(exportTask);
            ViewBag.BranchShareExportTask = task?.ToJson() ?? "null";
            new OpenEbillSendTaskService().ExecuteCheckEbillOpenStatusAsync();
            return View();
        }

        public ActionResult LoadConfig()
        {
            var curShopId = SiteContext.Current.CurrentShopId;
            var brandModel = _service.GetListByFromId(curShopId);
            var isFromShop = brandModel != null && brandModel.Any();
            var list = _service.GetShareShopsAndCompanys(curShopId, isFromShop, needDeleteShare: true);

            // 店铺名空值处理
            list.ForEach(m =>
            {
                if (string.IsNullOrWhiteSpace(m.ShopName))
                    m.ShopName = m.ShopId.ToString();
            });

            var shareShops = list.GroupBy(m => new { m.ShopId, m.ShopName }).Select(m => m.Key).OrderBy(m => m.ShopName).ToList();
            var shareExpressCompanys = list.GroupBy(m => new { m.CompanyCode, m.Names, m.AuthType, m.BillVersion }).Select(m => m.Key).OrderBy(m => m.Names).ToList();

            var model = new
            {
                ShareShops = shareShops,
                ShareExpressCompanys = shareExpressCompanys,
                IsFromShop = isFromShop
            };
            return Json(model.ToJson());
        }

        /// <summary>
        /// 加载分享账号列表
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("分享列表查询")]
        public ActionResult LoadList(OrderSearchModel model)
        {
            var curShopId = SiteContext.Current.CurrentShopId;
            // 当前店铺是分享店铺，查询分享店铺列表，否则只能查看自己被分享的内容
            var brandModel = _service.GetListByFromId(curShopId);
            var isFromShop = brandModel != null && brandModel.Any();

            var authTypeFilter = model.Filters.FirstOrDefault(m => m.Name == "AuthType");
            if (authTypeFilter != null)
            {
                model.Filters.Remove(authTypeFilter);
            }

            //单号分享列表：显示当前店铺作为分享店铺或被分享店铺的统计，其他统计列表只以当前分享状态统计
            var shopIdFilter = model.Filters.FirstOrDefault(m => m.Name == "ToId");
            if (shopIdFilter == null)
            {
                model.MultiFilters.Add(new List<OrderSearchFieldModel>
                {
                    new OrderSearchFieldModel
                    {
                        TableAlias = "s",
                        FieldType = FieldTypeFilter.Int32,
                        Name = "FromId",
                        Value = curShopId.ToString(),
                        Contract = ContractFilter.equal,
                        Operator = "OR"
                    },
                    new OrderSearchFieldModel
                    {
                        TableAlias = "s",
                        FieldType = FieldTypeFilter.Int32,
                        Name = "ToId",
                        Value = curShopId.ToString(),
                        Contract = ContractFilter.equal,
                        Operator = "OR"
                    }
                });
            }
            else
            {
                var shopId = shopIdFilter.Value.ToInt();
                model.MultiFilters.Add(new List<OrderSearchFieldModel>
                {
                    new OrderSearchFieldModel
                    {
                        TableAlias = "s",
                        FieldType = FieldTypeFilter.Int32,
                        Name = isFromShop ? "ToId":"FromId",
                        Value = shopId.ToString(),
                        Contract = ContractFilter.equal,
                        Operator = "AND"
                    },
                    new OrderSearchFieldModel
                    {
                        TableAlias = "s",
                        FieldType = FieldTypeFilter.Int32,
                        Name = isFromShop ? "FromId":"ToId",
                        Value = curShopId.ToString(),
                        Contract = ContractFilter.equal,
                        Operator = "AND"
                    }
                });
            }

            //if (shopIdFilter != null)
            //{
            //    shopIdFilter.Name = isFromShop ? "ToId" : "FromId";
            //    model.Filters.Add(new OrderSearchFieldModel
            //    {
            //        TableAlias = "s",
            //        FieldType = FieldTypeFilter.Int32,
            //        Name = isFromShop ? "FromId" : "ToId",
            //        Value = curShopId.ToString(),
            //        Contract = ContractFilter.equal
            //    });
            //}
            //else
            //    model.Filters.Add(new OrderSearchFieldModel
            //    {
            //        TableAlias = "s",
            //        FieldType = FieldTypeFilter.Int32,
            //        Name = isFromShop ? "FromId" : "ToId",
            //        Value = curShopId.ToString(),
            //        Contract = ContractFilter.equal
            //    });

            var log = LogForOperatorContext.Current.logInfo;
            var requestMoel = model.ToJson().ToObject<OrderSearchModel>();
            if (authTypeFilter != null)
                requestMoel.Filters.Add(authTypeFilter);
            log.Request = requestMoel;

            var list = _service.GetList(model, isLoadDeleted: true);

            var _caiNiaoAuthInfoService = new CaiNiaoAuthInfoService();
            var _shopService = new ShopService();

            var authShopIds = list.Where(m => m.AuthSourceType.ToInt() == 1).Select(m => m.CaiNiaoAuthInfoId).Distinct().ToList();
            var authShops = authShopIds.Any() ? _shopService.GetShopByIds(authShopIds) : new List<Shop>();

            var caiNiaoAuthInfoIds = list.Where(m => m.AuthSourceType.ToInt() == 0).Select(m => m.CaiNiaoAuthInfoId).Distinct().ToList();
            var authInfos = caiNiaoAuthInfoIds.Any() ? _caiNiaoAuthInfoService.GetByIds(caiNiaoAuthInfoIds) : new List<CaiNiaoAuthInfo>();
            list.ForEach(bs =>
            {
                bs.AuthType = bs.AuthSourceType.ToInt() == 1 ?
                        authShops.FirstOrDefault(m => m.Id == bs.CaiNiaoAuthInfoId)?.PlatformType :
                        authInfos?.Where(m => m.Id == bs.CaiNiaoAuthInfoId).FirstOrDefault()?.AuthType ?? "";
                //bs.AuthType = bs.AuthSourceType.ToInt() == 1 ? (isFromShop ? bs.ToPlatformType.ToString2() : bs.FromPlatformType.ToString2()) : authInfos?.Where(m => m.Id == bs.CaiNiaoAuthInfoId).FirstOrDefault()?.AuthType ?? "";
            });

            if (authTypeFilter != null && list.Any())
                list = list.Where(m => m.AuthType.ToString2().ToLower() == authTypeFilter.Value.ToString2().ToLower()).ToList();

            //return Json(list);
            return Json(new { Rows = list, TotalCount = model.TotalCount });
        }

        /// <summary>
        /// 创建分享状态
        /// </summary>
        /// <returns></returns>

        [LogForOperatorFilter("创建单号分享")]
        public ActionResult CreateShareWayBillAccount(BranchShareRelation model)
        {
            model.CreateTime = DateTime.Now;
            model.Status = string.Empty;
            model.Version = Guid.NewGuid().ToString("N");

            if (string.IsNullOrWhiteSpace(model.AuthType))
                model.AuthType = "";
            if (string.IsNullOrWhiteSpace(model.SegmentCode))
                model.SegmentCode = "";
            if (string.IsNullOrWhiteSpace(model.BrandCode))
                model.BrandCode = "";

            //校验是否可以添加
            var checkResult = ShareShopCheck(model.FromId, model.ToId, model.ExpressCompanyCode, model.BranchHashCode, model.AuthType, model.SegmentCode, model.BrandCode);
            if (checkResult.Item1 > 0)
            {
                return FalidResult(checkResult.Item2);
            }

            model.Id = _service.Add(model);

            if (model.OtherInfoList != null && model.OtherInfoList.Any())
            {
                model.OtherInfoList.ForEach(item => item.ShareId = model.Id);
                _shareOtherInfoService.AddList(model.OtherInfoList);
            }

            if (model.EBillAccountExtList != null && model.EBillAccountExtList.Any())
            {
                _ebillAccountService.AddOrUpdateList(model.EBillAccountExtList);
            }

            if (model.Id > 0)
            {
                // 添加操作日志
                AddLog(model, ShareLogType.Add, model.TotalQuantity);

                //自动添加模板
                ThreadPool.QueueUserWorkItem(item =>
                {
                    try
                    {
                        AutoAddTemplate(model);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"分享后自动添加模板失败：{ex}");
                    }
                });
            }
            return SuccessResult();
        }

        private void AutoAddTemplate(BranchShareRelation model)
        {
            var expressCompany = _expressCompanyService.GetModel(model.ExpressCompanyCode);
            var templateList = _printTemplateService.LoadExpressTemplates(expressCompany.Id);
            //查询出授权信息
            var cainiaoAuthInfo = _caiNiaoAuthInfoService.GetModel(model.CaiNiaoAuthInfoId, model.AuthSourceType);
            //获取开通的网点
            var waybillAccountDetailInfos = new List<BranchAddress>();
            var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
            var cpCodeMapping = new ExpressCpCodeMapping();
            //1：传统模板
            //3：网点模板
            //10：丰桥模板 
            //5：菜鸟二联
            //6：菜鸟留底联
            //7：菜鸟快运
            //9：菜鸟一联
            //10：顺丰丰桥
            //21：拼多多二联
            //23：拼多多一联
            //40：菜鸟官方二联
            //41：菜鸟官方一联
            //60: 无界电子面单
            //81：京东快递
            PrintTemplate template = null;
            var templateName = "";
            var expressCompanyName = expressCompany?.Names;
            switch (cainiaoAuthInfo.AuthType.ToLower())
            {
                case "link":
                    template = templateList.FirstOrDefault(f => f.TemplateType == 41);
                    if (template == null)
                    {
                        templateName = $"【菜鸟官方】{expressCompanyName}二联(分享)";
                        template = templateList.FirstOrDefault(f => f.TemplateType == 40);
                    }
                    else
                    {
                        templateName = $"【菜鸟官方】{expressCompanyName}一联(分享)";
                    }
                    cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "Top");
                    waybillAccountDetailInfos = TopCaiNiaoApiService.GetTaoBaoBranchAddressList(waybillAuthConfig, new List<string> { cpCodeMapping.CpCode });
                    break;

                case "taobao":
                    template = templateList.FirstOrDefault(f => f.TemplateType == 9);
                    if (template == null)
                    {
                        templateName = $"【菜鸟】{expressCompanyName}二联(分享)";
                        template = templateList.FirstOrDefault(f => f.TemplateType == 5);
                    }
                    else
                    {
                        templateName = $"【菜鸟】{expressCompanyName}一联(分享)";
                    }
                    if (model.ExpressCompanyCode == "FENGWANG")
                    {
                        var cpCodeMappings = _expressCpCodeMappingService.GetList(model.ExpressCompanyCode, "Top");
                        cpCodeMapping = cpCodeMappings.FirstOrDefault(f => f.CpCode == model.BranchCpCode);
                    }
                    else
                        cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "Top");
                    waybillAccountDetailInfos = TopCaiNiaoApiService.GetTaoBaoBranchAddressList(waybillAuthConfig, null);
                    break;

                case "pddwaybill":
                case "pinduoduo":
                    template = templateList.FirstOrDefault(f => f.TemplateType == 23);
                    if (template == null)
                    {
                        templateName = $"【拼多多】{expressCompanyName}二联(分享)";
                        template = templateList.FirstOrDefault(f => f.TemplateType == 21);
                    }
                    else
                    {
                        templateName = $"【拼多多】{expressCompanyName}一联(分享)";
                    }
                    cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "Pdd");
                    waybillAccountDetailInfos = (new PddWaybillApiService(waybillAuthConfig)).GetPinduoduoBranchAddressList();
                    break;

                case "jingdong":
                    template = templateList.FirstOrDefault(f => f.TemplateType == 96);
                    if (template == null)
                    {
                        templateName = $"【京东无界】{expressCompanyName}二联(分享)";
                        template = templateList.FirstOrDefault(f => f.TemplateType == 97);
                    }
                    if (template == null)
                    {
                        templateName = $"【京东无界】{expressCompanyName}三联(分享)";
                        template = templateList.FirstOrDefault(f => f.TemplateType == 98);
                    }
                    if (template == null)
                    {
                        templateName = $"【京东无界】{expressCompanyName}一联(分享)";
                    }
                    cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "JDWJ");
                    waybillAccountDetailInfos = new WuJieWaybillApiService(waybillAuthConfig).GetJingDongBranchAddressList(cpCodeMapping.CpCode);
                    break;

                case "toutiao":
                    template = templateList.FirstOrDefault(f => f.TemplateType == 17);
                    if (template == null)
                    {
                        templateName = $"【抖店】{expressCompanyName}二联(分享)";
                        template = templateList.FirstOrDefault(f => f.TemplateType == 18);
                    }
                    else
                    {
                        templateName = $"【抖店】{expressCompanyName}一联(分享)";
                    }
                    cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "TouTiao");
                    string logId = string.Empty;
                    waybillAccountDetailInfos = new TouTiaoWaybillApiService(waybillAuthConfig).GetBranchAddressList(cpCodeMapping.CpCode, out logId);
                    break;

                case "kuaishou":
                    template = templateList.FirstOrDefault(f => f.TemplateType == 116);
                    if (template == null)
                    {
                        templateName = $"【快手】{expressCompanyName}二联(分享)";
                        template = templateList.FirstOrDefault(f => f.TemplateType == 111);
                    }
                    else
                    {
                        templateName = $"【快手】{expressCompanyName}一联(分享)";
                    }
                    cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "KuaiShou");
                    waybillAccountDetailInfos = new KsWaybillApiService(waybillAuthConfig).GetBranchAddressList(cpCodeMapping.CpCode);
                    break;

                case "xiaohongshu":
                    //兼容以前分享无数值的
                    if (model.BillVersion == 0)
                        model.BillVersion = 1;
                    //新版小红书
                    if (model.BillVersion == 2)
                    {
                        template = templateList.FirstOrDefault(f => f.TemplateType == 180);
                        if (template == null)
                        {
                            templateName = $"【小红书】{expressCompanyName}二联(分享)";
                            template = templateList.FirstOrDefault(f => f.TemplateType == 182);
                        }
                        else
                        {
                            templateName = $"【小红书】{expressCompanyName}一联(分享)";
                        }
                    }
                    else
                    {
                        template = templateList.FirstOrDefault(f => f.TemplateType == 150);
                        if (template == null)
                        {
                            templateName = $"【小红书】{expressCompanyName}二联(分享)";
                            template = templateList.FirstOrDefault(f => f.TemplateType == 151);
                        }
                        else
                        {
                            templateName = $"【小红书】{expressCompanyName}一联(分享)";
                        }
                    }
                    cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "XiaoHongShu");
                    waybillAccountDetailInfos = new XiaoHongShuWaybillApiService(waybillAuthConfig).GetBranchAddressListBybillVersion(cpCodeMapping.CpCode, model.BillVersion);
                    break;

                case "wxvideo":
                    template = templateList.FirstOrDefault(f => f.TemplateType == 160);
                    if (template == null)
                    {
                        templateName = $"【视频号】{expressCompanyName}二联(分享)";
                        template = templateList.FirstOrDefault(f => f.TemplateType == 161);
                    }
                    else
                    {
                        templateName = $"【视频号】{expressCompanyName}一联(分享)";
                    }
                    cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "WxVideo");
                    waybillAccountDetailInfos = new WxVideoWaybillApiService(waybillAuthConfig).GetBranchAddressList(cpCodeMapping.CpCode);
                    break;
                default:
                    //return FalidResult($"无法识别电子面单授权账号的类型【{authInfo.AuthType}】");
                    break;
            }
            if (template == null)
            {
                throw new LogicException("未找到对应快递的系统模板，自动添加模板失败！");
            }
            //获取当前分享的快递网点
            if (!string.IsNullOrWhiteSpace(model.BrandCode))
            {
                waybillAccountDetailInfos = waybillAccountDetailInfos.Where(f =>
                f.CpCode == cpCodeMapping.CpCode &&
                f.BranchHashCode == model.BranchHashCode &&
                f.BrandCode == model.BrandCode).ToList();
            }
            else
                waybillAccountDetailInfos = waybillAccountDetailInfos.Where(f => f.CpCode == cpCodeMapping.CpCode && f.BranchHashCode == model.BranchHashCode).ToList();

            var branch = waybillAccountDetailInfos.FirstOrDefault();
            //判断 网点是否有必填服务，有必填服务则不能给用户自动添加模板
            if (branch.ServiceInfoCols.Any(f => f.Required == true))
            {
                throw new LogicException("检测到网点开通必填服务，需用户确认，自动添加模板失败！");
            }

            //添加模板
            var addModel = new TemplateAddModel()
            {
                AddToShopId = model.ToId,
                AuthSourceType = cainiaoAuthInfo.AuthSourceType,
                CaiNiaoAuthAccountId = cainiaoAuthInfo.Id,
                ExpressCode = model.ExpressCompanyCode,
                ExpressId = expressCompany.Id,
                TemplateId = template.Id,
                TemplateName = templateName,
                TemplateType = template.TemplateType,
                IsDefault = true,
                FromId = template.FromId,
                Branch = new BranchInfo()
                {
                    ShareRelationId = model.Id,
                    CpType = (int)branch.CpType,
                    BranchCode = branch.BranchCode,
                    BranchName = branch.BranchName,
                    Province = branch.Province,
                    City = branch.City,
                    Area = branch.Area,
                    Town = branch.Town,
                    Detail = branch.Detail,
                    CpCode = branch.CpCode,
                    BrandCode = branch.BrandCode,
                    SegmentCode = branch.SegmentCode,
                    BranchHashCode = model.BranchHashCode
                }
            };
            //视频号面单接口EMS合并了国内邮政小包&电商标快,需要分开
            //修改业务类型对应接口
            if (model.AuthType == "WxVideo" && model.BranchCpCode == "EMS")
            {
                if (model.ExpressCompanyCode == "POSTB")
                {
                    addModel.SfExpType = "2";//EMS -快递包裹（国内邮政小包）
                }
                else if (model.ExpressCompanyCode == "POST_DSBK")
                {
                    addModel.SfExpType = "3";//EMS -邮政电商标快（yzdsbk)
                }
                else
                {
                    addModel.SfExpType = "1";//EMS -特快专递（EMS）
                }
            }
            new TemplateSetController().AddTemplateSubmit(addModel);
        }

        /// <summary>
        /// 更新分享状态(status : 0 恢复，1停止，-1 删除)
        /// </summary>
        /// <returns></returns>     
        [LogForOperatorFilter("更新单号分享状态")]
        public ActionResult UpdateShareStatus(int id, int status)
        {
            if (id == 0)
                return FalidResult("分享店铺不存在");
            var model = _service.GetById(id);
            if (model == null)
                return FalidResult("分享店铺不存在");
            if (model.FromId != SiteContext.Current.CurrentShopId)
            {
                if (status == -1)
                    return FalidResult("请前往分享主店铺删除单号分享");
                else if (status == 1)
                    return FalidResult("请前往分享主店铺终止单号分享");
                else
                    return FalidResult("请前往分享主店铺恢复单号分享");
            }

            var statusStr = string.Empty;
            ShareLogType logType;
            if (status == -1)
            {
                statusStr = BranchShareStatus.Deleted.ToString();
                logType = ShareLogType.Delete;
            }
            else if (status == 1)
            {
                statusStr = BranchShareStatus.Stoped.ToString();
                logType = ShareLogType.Stop;
            }
            else
            {
                statusStr = "";
                logType = ShareLogType.Resume;
                var checkResult = ShareShopCheck(model.FromId, model.ToId, model.ExpressCompanyCode, model.BranchHashCode, model.AuthType, model.SegmentCode, model.BrandCode, isResumeCheck: true);
                if (checkResult.Item1 > 0)
                {
                    return FalidResult("恢复电子面单分享失败！" + checkResult.Item2);
                }

                // 关联关系
                var relationShop = _relationService.GetMainShopByRelationId(model.ToId);
                if (relationShop != null)
                {
                    // 分享关系（判断分享店铺与该店铺的主店铺是否有分享关系）
                    var shareList2 = GetBranchShareRelations(model.FromId, relationShop.ShopId, model.ExpressCompanyCode, model.BranchHashCode, model.AuthType, model.SegmentCode, model.BrandCode, true, false);
                    if (shareList2.Any())
                        return FalidResult($"恢复电子面单分享失败！店铺【{model.ToShopName}】与【{relationShop.MainShop?.ShopName ?? ""}】已经关联，可直接使用电子面单共享。");
                }
            }

            _service.UpdateShareStatus(id, statusStr);
            // 添加操作日志
            AddLog(model, logType);
            return SuccessResult();
        }

        private string GetUpdateStatusMessage(BranchShareRelation model, Tuple<int, string> result, ShareLogType type)
        {
            var errStatus = result.Item1;
            var errMsg = result.Item2;
            if (errStatus == 1)
            {
                if (type == ShareLogType.Delete)
                    errMsg = "删除电子面单分享失败！不能删除自己店铺";
                else if (type == ShareLogType.Stop)
                    errMsg = "终止电子面单分享失败！不能终止自己店铺";
                else if (type == ShareLogType.Resume)
                    errMsg = "恢复电子面单分享失败！不能恢复自己店铺";
            }
            else if (errStatus == 3 || errStatus == 4 || errStatus == 5 || errStatus == 6 || errStatus == 7)
            {
                if (type == ShareLogType.Resume)
                {
                    var relationShop = _relationService.GetMainShopByRelationId(model.ToId);
                    errMsg = $"恢复电子面单分享失败！店铺【{model.ToShopName}】与【{relationShop?.MainShop?.ShopName ?? ""}】已经关联，可直接使用主店铺电子面单共享。";
                }
            }
            return errMsg;
        }

        /// <summary>
        /// 更新分享备注
        /// </summary>
        /// <returns></returns>          
        [LogForOperatorFilter("更新单号分享备注")]
        public ActionResult UpdateRemark(int id, string remark)
        {
            if (id == 0)
                return FalidResult("分享店铺不存在");
            var model = _service.GetById(id);
            if (model == null)
                return FalidResult("分享店铺不存在");
            if (model.FromId != SiteContext.Current.CurrentShopId)
                return FalidResult("请前往分享店铺，更改分享备注信息");
            _service.UpdateRemark(id, remark);
            return SuccessResult();
        }

        /// <summary>
        /// 更新分享码
        /// </summary>
        /// <param name="id"></param>
        /// <param name="remark"></param>
        /// <returns></returns>      
        [LogForOperatorFilter("更新单号分享码")]
        public ActionResult UpdateShopShareCode()
        {
            var shop = _shopService.UpdateShopShareCode(SiteContext.Current.CurrentShopId);
            return SuccessResult(shop.ShareCode.ToString2());
        }

        /// <summary>
        /// 获取操作日志列表
        /// </summary>
        /// <returns></returns>                
        [LogForOperatorFilter("查看分享操作日志")]
        public ActionResult LoadLogList(int id)
        {
            var logs = _logService.GetLogs(id);
            return Json(logs);
        }

        /// <summary>
        /// 获取月份使用明细
        /// </summary>
        /// <returns></returns>     
        [LogForOperatorFilter("查看月份统计使用明细列表")]
        public ActionResult LoadUsedDetailList(int id, int preCount)
        {
            if (id == 0)
                return FalidResult("分享店铺不存在");
            var model = _service.GetById(id);
            if (model == null)
                return FalidResult("分享店铺不存在");

            var sDateStr = DateTime.Now.AddMonths(-preCount).ToString("yyyy-MM") + "-01";
            var eDateStr = DateTime.Now.AddMonths(-preCount).AddMonths(1).ToString("yyyy-MM") + "-01";

            var list = _service.GetUsedDetailList(new List<int> { id }, null, sDateStr, eDateStr, toSystemShopIds: new List<int> { model.ToId });
            list = list.OrderByDescending(m => m.CreateTime).ToList();
            //list?.ForEach(l =>
            //{
            //    if (l.PlatformType == PlatformType.Pinduoduo.ToString()
            //        || l.PlatformType == PlatformType.Taobao.ToString()
            //        || l.PlatformType == PlatformType.Jingdong.ToString())
            //    {
            //        l.Reciver = l.Reciver.ToEncryptName();
            //    }
            //});
            return Json(list);
        }

        [LogForOperatorFilter("追加分享单号")]
        public ActionResult AppendAccountCount(int id, int addCount, bool isConfirm, string remark)
        {
            if (id == 0)
                return FalidResult("分享店铺不存在");
            var model = _service.GetById(id);
            if (model == null)
                return FalidResult("分享店铺不存在");
            if (addCount == 0)
                return SuccessResult(0);
            if (model.FromId != SiteContext.Current.CurrentShopId)
                return FalidResult("请前往分享主店铺追加分享单号");

            // 关联关系
            var relationShop = _relationService.GetMainShopByRelationId(model.ToId);
            if (relationShop != null)
            {
                // 分享关系（判断分享店铺与该店铺的主店铺是否有分享关系）
                var shareList2 = GetBranchShareRelations(model.FromId, relationShop.ShopId, model.ExpressCompanyCode, model.BranchHashCode, model.AuthType, model.SegmentCode, model.BrandCode, true, false);
                if (shareList2.Any())
                    return FalidResult($"追加电子面单分享失败！店铺【{model.ToShopName}】与【{relationShop.MainShop?.ShopName ?? ""}】已经关联，可直接使用主店铺电子面单共享。");
            }

            //var appendCount = model.Balance + addCount < 0 ? -model.Balance : addCount;
            if (isConfirm)
                //model.TotalQuantity = model.Balance + addCount < 0 ? model.TotalUsedQuantity.ToInt() : (model.TotalQuantity + addCount);
                model.AppendCount = addCount;
            else
            {
                var branch = GetBranchByHashCode(model.BranchHashCode, model.AuthType, model.SegmentCode, model.BrandCode, model.BillVersion);
                if (addCount > 0 && ((branch.CpType != 1 && branch.CpType != 4 && branch.CpType != 5) || (branch.AuthType == "Taobao" && branch.CpCode == "SF" && branch.BrandCode == "FW")))
                {
                    if (branch.Quantity - branch.PrintQuantity >= addCount)
                        //model.TotalQuantity += addCount;
                        model.AppendCount = addCount;
                    else
                        return SuccessResult(new { Code = 1, Branch = branch }); // 待确认，追加单号总数量大于网点余额
                }
                else
                {
                    if (model.Balance + addCount >= 0)
                        //model.TotalQuantity += addCount;
                        model.AppendCount = addCount;
                    else
                        return SuccessResult(2); // 待确认，减少单号总数量大于当前店铺分享可用单号总数量
                }
            }
            model.Remark = remark.ToString2();
            model.Version = Guid.NewGuid().ToString("N");
            //_service.Update(model);
            model = _service.AppendCount(model);
            if (model.IsUpdateSuccess == true)
            {
                // 添加操作日志
                AddLog(model, addCount > 0 ? ShareLogType.Append : ShareLogType.Reduce, addCount);
            }
            return SuccessResult(0);
        }

        /// <summary>
        /// 实时检查网点可用单号是否够分享
        /// </summary>
        /// <param name="shareCount"></param>
        /// <param name="branchHashCode"></param>
        /// <returns></returns>
        public ActionResult CheckAvaliableQuanty(int shareCount, string branchHashCode, string authType, string segmentCode, string brandCode,int? billVersion =0)
        {
            var branch = GetBranchByHashCode(branchHashCode, authType, segmentCode, brandCode,billVersion ?? 0);
            if (branch.Quantity < shareCount)
                return FalidResult(string.Empty, branch);
            if (branch.Quantity - branch.PrintQuantity < shareCount)
                return FalidResult(string.Empty, branch);

            return SuccessResult(branch);
        }

        private BranchAddress GetBranchByHashCode(string branchHashCode, string authType, string segmentCode, string brandCode,int billVersion = 0)
        {
            var branchList = (new TemplateSetController()).GetBranchListbyCurrentShop();

            BranchAddress branch;
            if (string.IsNullOrWhiteSpace(brandCode) == false)
            {
                var branchAddresses = branchList.Where(f => f.BranchHashCode == branchHashCode && f.BrandCode == brandCode).ToList();
                branch = billVersion == 2 
                    ? branchAddresses.FirstOrDefault(item => item.IsNewBillVersion) 
                    : branchAddresses.FirstOrDefault(item => !item.IsNewBillVersion);
            }
            else
            {
                var branchAddresses = branchList.Where(f => f.BranchHashCode == branchHashCode).ToList();
                branch = billVersion == 2
                    ? branchAddresses.FirstOrDefault(item => item.IsNewBillVersion)
                    : branchAddresses.FirstOrDefault(item => !item.IsNewBillVersion);
            }
            if (branch == null)
            {
                throw new LogicException("快递网点未找到，请确认账号是否取消了相应网点");
            }
            //接口数量够用，再检测分享出去了多少数量
            var shareList = _service.GetShareListByFromIdAndBranchHashCode(SiteContext.Current.CurrentShopId, branch.BranchHashCode, authType, segmentCode, brandCode,billVersion);
            var usedQty = shareList.Sum(f => (f.TotalQuantity - f.TotalUsedQuantity));
            branch.PrintQuantity = usedQty;
            return branch;
        }

        private long CheckAuthAccountBalance(BranchShareRelation model)
        {
            var shopId = model.FromId;
            var waybillAccountDetailInfos = new List<BranchAddress>();
            CaiNiaoAuthInfo cainiaoAuthInfo = null;
            cainiaoAuthInfo = _caiNiaoAuthInfoService.GetModel(model.CaiNiaoAuthInfoId, model.AuthSourceType);

            var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
            var cpCodeMapping = new ExpressCpCodeMapping();

            switch (cainiaoAuthInfo.AuthType.ToLower())
            {
                case "link":
                    cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "Top");
                    waybillAccountDetailInfos = (new CloudCaiNiaoApiService(waybillAuthConfig)).GetCaiNiaoBranchAddressList();
                    break;

                case "taobao":
                    cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "Top");
                    waybillAccountDetailInfos = TopCaiNiaoApiService.GetTaoBaoBranchAddressList(waybillAuthConfig);
                    break;

                case "pddwaybill":
                case "pinduoduo":
                    cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "Pdd");
                    waybillAccountDetailInfos = (new PddWaybillApiService(waybillAuthConfig)).GetPinduoduoBranchAddressList();
                    break;

                case "jingdong":
                    cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "JDWJ");
                    waybillAccountDetailInfos = (new WuJieWaybillApiService(waybillAuthConfig)).GetJingDongBranchAddressList(cpCodeMapping.CpCode);
                    break;

                case "toutiao":
                    cpCodeMapping = _expressCpCodeMappingService.Get(model.ExpressCompanyCode, "Toutiao");
                    string logId = string.Empty;
                    waybillAccountDetailInfos = (new TouTiaoWaybillApiService(waybillAuthConfig)).GetBranchAddressList(cpCodeMapping.CpCode, out logId);
                    break;

                default:
                    //return FalidResult($"无法识别电子面单授权账号的类型【{authInfo.AuthType}】");
                    break;
            }

            waybillAccountDetailInfos = waybillAccountDetailInfos.Where(f => f.CpCode == cpCodeMapping.CpCode && f.BranchHashCode == model.BranchHashCode).ToList();
            //兼容
            if (waybillAccountDetailInfos == null || waybillAccountDetailInfos.Any() == false)
                waybillAccountDetailInfos = waybillAccountDetailInfos.Where(f => f.CpCode == cpCodeMapping.CpCode && f.BranchHashCodeOld == model.BranchHashCode).ToList();

            var first = waybillAccountDetailInfos.FirstOrDefault();
            if (first.CpType != 1 || first.CpType != 4)
                return int.MaxValue;
            else
                return first?.Quantity ?? 0;
        }

        /// <summary>
        /// 分享关系能否建立的条件检查
        /// </summary>
        /// <param name="fromId"></param>
        /// <param name="toId"></param>
        /// <returns></returns>
        private Tuple<int, string> ShareShopCheck(int fromId, int toId, string expressCompanyCode, string branchHashCode, string authType, string segmentCode, string brandCode, bool isResumeCheck = false)
        {
            if (fromId == toId)
                return new Tuple<int, string>(1, "不能分享给自己");

            //A分享给B
            //1.检查A是否有接受其他人的分享,如果有接受分享，则不允许分享。不允许多级分享，分享关系只一层。
            //2.检查A是否是单独的店铺，或者主店铺。如果A是子店铺则不允许分享。子店铺不能分享给别人。
            //3.检查A是否有关联B，A如果有关联B，则直接走关联关系。关联关系优先。
            //4.检查B是否是子店铺，如果B被他人关联，B作为店铺不能接受分享，需分享给B的主店铺。
            //5.检查B店铺是否存在与A店铺对该网点的分享。
            //6.检查B店铺有没有分享给其他店铺

            var shareList = _service.GetListByToId(fromId);
            if (shareList.Any())
            {
                //1.A存在与接受其他的分享，不能再分享
                return new Tuple<int, string>(3, "当前店铺接受过分享，不能再建立分享给别的店铺");
            }

            var relationListByRelationId = _relationService.GetRelationListByRelationId(fromId);
            if (relationListByRelationId.Any())
            {
                //2.A是子店铺，不允许建立分享
                return new Tuple<int, string>(4, "当前店铺是子店铺，不能再建立分享给别的店铺");
            }

            var relationByShopIdAndRelationShopId = _relationService.GetRelationByShopIdAndRelationId(fromId, toId);
            if (relationByShopIdAndRelationShopId.Any())
            {
                //3.A有关联B，不允许再建立分享
                return new Tuple<int, string>(5, "当前店铺与要分享的店铺是关联关系，不能再建立分享");
            }

            var relationListByRelationId2 = _relationService.GetRelationListByRelationId(toId);
            if (relationListByRelationId2.Any())
            {
                //4.B是子店铺，子店铺不允许接受分享
                return new Tuple<int, string>(6, "被分享店铺是子店铺，子店铺不能接受分享");
            }

            if (!isResumeCheck)
            {
                //var shareList2 = GetBranchShareRelations(fromId, toId, expressCompanyCode, branchHashCode, false, false);
                var shareList2 = GetBranchShareRelations02(fromId, toId, expressCompanyCode, branchHashCode, authType, segmentCode, brandCode, false, false);
                if (shareList2.Any()) //这个校验只有创建时校验，恢复操作不校验
                {
                    //4.A店铺已经分享过该网点给B店铺
                    return new Tuple<int, string>(2, "已经分享过该网点给店铺,不能重复建立分享。");
                }
            }

            var shareList3 = _service.GetListByFromId(toId);
            if (shareList3.Any())
            {
                //7.A是被分享店铺，A店铺已经分享过该网点给B店铺，不能再对A创建分享
                return new Tuple<int, string>(7, "该店铺分享过面单给其他店铺，不能再接受分享");
            }
            return new Tuple<int, string>(0, string.Empty);
        }

        private List<BranchShareRelation> GetBranchShareRelations(int fromId, int toId, string expressCompanyCode, string branchHashCode, string authType, string segmentCode, string brandCode, bool isLoadDeleted = true, bool isLoadSumData = true)
        {
            var shareList2 = _service.GetList(new OrderSearchModel()
            {
                Filters = new List<OrderSearchFieldModel>() {
                    new Data.Model.OrderSearchFieldModel() {
                        TableAlias="s",
                        FieldType=FieldTypeFilter.Int32,
                        Name="FromId",
                        Value=fromId.ToString(),
                        Contract=ContractFilter.equal
                    },
                    new Data.Model.OrderSearchFieldModel() {
                        TableAlias="s",
                        FieldType=FieldTypeFilter.Int32,
                        Name="ToId",
                        Value=toId.ToString(),
                        Contract=ContractFilter.equal
                    },
                    new Data.Model.OrderSearchFieldModel() {
                        TableAlias="s",
                        FieldType=FieldTypeFilter.String,
                        Name="expressCompanyCode",
                        Value=expressCompanyCode.ToString(),
                        Contract=ContractFilter.equal
                    },
                    new Data.Model.OrderSearchFieldModel() {
                        TableAlias="s",
                        FieldType=FieldTypeFilter.String,
                        Name="BranchHashCode",
                        Value=branchHashCode,
                        Contract=ContractFilter.equal
                    },
                    new Data.Model.OrderSearchFieldModel() {
                        TableAlias="s",
                        FieldType=FieldTypeFilter.String,
                        Name="AuthType",
                        Value=authType,
                        Contract=ContractFilter.equal
                    },
                    new Data.Model.OrderSearchFieldModel() {
                        TableAlias="s",
                        FieldType=FieldTypeFilter.String,
                        Name="SegmentCode",
                        Value=segmentCode,
                        Contract=ContractFilter.equal
                    },
                    new Data.Model.OrderSearchFieldModel() {
                        TableAlias="s",
                        FieldType=FieldTypeFilter.String,
                        Name="BrandCode",
                        Value=brandCode,
                        Contract=ContractFilter.equal
                    }
                }
            }, isLoadDeleted, isLoadSumData);
            return shareList2;
        }

        /// <summary>
        /// 加了 authtype，为了兼容
        /// 新写了一个方法
        /// </summary>
        /// <param name="fromId"></param>
        /// <param name="toId"></param>
        /// <param name="expressCompanyCode"></param>
        /// <param name="branchHashCode"></param>
        /// <param name="authType"></param>
        /// <param name="segmentCode"></param>
        /// <param name="isLoadDeleted"></param>
        /// <param name="isLoadSumData"></param>
        /// <returns></returns>
        private List<BranchShareRelation> GetBranchShareRelations02(int fromId, int toId, string expressCompanyCode, string branchHashCode, string authType, string segmentCode, string brandCode, bool isLoadDeleted = true, bool isLoadSumData = true)
        {

            var shareList2 = _service.GetList(new OrderSearchModel()
            {
                Filters = new List<OrderSearchFieldModel>() {
                    new Data.Model.OrderSearchFieldModel() {
                        TableAlias="s",
                        FieldType=FieldTypeFilter.Int32,
                        Name="FromId",
                        Value=fromId.ToString(),
                        Contract=ContractFilter.equal
                    },
                    new Data.Model.OrderSearchFieldModel() {
                        TableAlias="s",
                        FieldType=FieldTypeFilter.Int32,
                        Name="ToId",
                        Value=toId.ToString(),
                        Contract=ContractFilter.equal
                    },
                    new Data.Model.OrderSearchFieldModel() {
                        TableAlias="s",
                        FieldType=FieldTypeFilter.String,
                        Name="expressCompanyCode",
                        Value=expressCompanyCode.ToString(),
                        Contract=ContractFilter.equal
                    },
                    new Data.Model.OrderSearchFieldModel() {
                        TableAlias="s",
                        FieldType=FieldTypeFilter.String,
                        Name="BranchHashCode",
                        Value=branchHashCode,
                        Contract=ContractFilter.equal
                    }
                }
            }, isLoadDeleted, isLoadSumData);
            //authtype另外过滤，segment暂时不过滤，历史数据没法处理
            shareList2 = shareList2.Where(f => f.AuthType == authType).ToList();
            if (string.IsNullOrWhiteSpace(brandCode) == false)
            {
                shareList2 = shareList2.Where(f => f.BrandCode == brandCode).ToList();
            }
            return shareList2;
        }

        [LogForOperatorFilter("导出月份统计使用明细列表")]
        public ActionResult ExportExcel()
        {
            var options = Request.Form["options"].ToString2();
            options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
            options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果
            var jToken = options.ToObject<JToken>();
            var id = jToken?.Value<int>("Id") ?? 0;
            var preCount = jToken?.Value<int>("PreCount") ?? 0;
            //var names = jToken?.Value<string>("Names");
            //if (names.IsNullOrEmpty())
            //{
            //    Log.WriteError("分享电子面单导出异常：无Excel表头数据");
            //    return FalidResult("分享电子面单导出异常：无Excel表头数据");
            //}
            if (id == 0)
                return FalidResult("分享店铺不存在");
            var model = _service.GetById(id);
            if (model == null)
                return FalidResult("分享店铺不存在");

            var sDateStr = DateTime.Now.AddMonths(-preCount).ToString("yyyy-MM") + "-01";
            var eDateStr = DateTime.Now.AddMonths(-preCount).AddMonths(1).ToString("yyyy-MM") + "-01";

            var isWdUser = SiteContext.Current.CurrentLoginUser?.Type == 1;
            var list = _service.GetUsedDetailList(new List<int> { id }, null, sDateStr, eDateStr, isWdUser, new List<int> { model.ToId });
            if (list == null || !list.Any())
                return FalidResult("无打印数据");

            var fileName = DateTime.Now.AddMonths(-preCount).Month + "月电子面单使用明细.xlsx";
            fileName = ExcelHelper.GetFileName(fileName, Request);
            //var headNames = names.ToList<string>();
            var isFromShop = model.FromId == SiteContext.Current.CurrentShopId;
            //var workbook = BuildExcel(new List<BranchShareRelation> { model }, fileName, list, isWdUser);
            var workbook = BuildExccelService.BuildBranchShareExcel(new List<BranchShareRelation> { model }, fileName, list, isFromShop, isWdUser);//BuildExcel(new List<BranchShareRelation> { model }, fileName, list, isWdUser);
            Response.Cookies.Add(new HttpCookie("downloadToken", Request.Form["downloadToken"].ToString2()));
            //var buffer = new byte[] { };

            var rootPath = Server.MapPath("../Files") + $"\\{DateTime.Now.AddMonths(-preCount).Month}月电子面单使用明细{SiteContext.Current.CurrentShopId}.xlsx";
            using (var fs = new FileStream(rootPath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(fs);
            }
            var memoryStream = new MemoryStream();
            using (var fileStream = new FileStream(rootPath, FileMode.Open))
            {
                fileStream.CopyTo(memoryStream);
            }
            memoryStream.Position = 0;
            if (System.IO.File.Exists(rootPath))
                System.IO.File.Delete(rootPath);
            return File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);

            //using (MemoryStream ms = new MemoryStream())
            //{
            //    workbook.Write(ms);
            //    buffer = ms.GetBuffer();
            //    //ms.Close();
            //}
            ////application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
            ////application/vnd.ms-excel     application/ms-excel
            //return File(buffer, "application/openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        }

        [LogForOperatorFilter("导出使用明细列表")]
        public ActionResult ExportUsedToExcel()
        {
            var curShop = SiteContext.Current.CurrentLoginShop;
            // 是否查询缓存数据
            const string cacheKey = ExportTaskService.ExportTaskCacheKey;
            var isUseCache =  (new CommonSettingService().Get(cacheKey, 0)?.Value.ToInt() ?? 0) == 1;
            if (isUseCache)
            {
                var redisKey = CacheKeys.ExportTaskQueryKey
                    .Replace("{ExportType}", ExportType.BranchShare.ToInt().ToString())
                    .Replace("{FxUserId}", SiteContext.Current.CurrentFxUserId.ToString());
                var cacheModel = RedisHelper.Get<ExportTaskCacheModel>(redisKey);
                if (cacheModel != null) return FalidResult("已存在订单导出任务，如需重新导出，请先取消再创建新导出任务", cacheModel);
            }
            else
            {
                var task = _exportTaskService.GetExportTask(curShop.Id, ExportType.BranchShare.ToInt());
                if (task != null && task.Status >= 0 && task.Status < 4)
                    return FalidResult("已存在订单导出任务，如需重新导出，请先取消再创建新导出任务，如没有权限请联系主账号或系统管理员操作", GetExportTaskToWeb(task));
            }
            
            var options = Request.Form["options"].ToString2();
            options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
            options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果
            var model = JsonExtension.ToObject<BranchShareDetaiSearchModel>(options) ?? new BranchShareDetaiSearchModel();

            var idLst = model.ShareIds;//Request.Params["Ids"].ToString2();
            var startTime = model.StartTime.ToString2();// Request.Params["startTime"].ToString2();
            var endTime = model.EndTime.ToString2();// Request.Params["endTime"].ToString2();
            var totalCount = model.TotalCount.ToInt();// Request.Params["TotalCount"].ToInt();

            if (idLst == null || !idLst.Any())
            {
                Log.WriteError("无使用明细可导出");
                return FalidResult("无使用明细可导出");
            }

            var sDate = new DateTime();
            if (!DateTime.TryParse(startTime, out sDate))
            {
                Log.WriteError("开始时间设置无效");
                return FalidResult("开始时间设置无效");
            }
            var eDate = new DateTime();
            if (!DateTime.TryParse(endTime, out eDate))
            {
                Log.WriteError("结束时间设置无效");
                return FalidResult("结束时间设置无效");
            }
            ////var idLst = ids.ToList<int>();
            //var curShopId = curShop.Id;
            //// 当前店铺是分享店铺，查询分享店铺列表，否则只能查看自己被分享的内容
            //var brandModel = _service.GetListByFromId(curShopId);
            //var isFromShop = brandModel != null && brandModel.Any();

            //var models = _service.GetByIds(idLst);

            //var sDateStr = sDate.ToString("yyyy-MM-dd HH:mm:ss");
            //var eDateStr = eDate.ToString("yyyy-MM-dd HH:mm:ss");

            //TemplateRelationAuthInfoService _tmpRelatetionService = new TemplateRelationAuthInfoService();
            //var printTemplates = _tmpRelatetionService.GetByShareRelationIds(idLst);
            //var tids = printTemplates?.Select(m => m.TemplateId).Distinct().ToList() ?? new List<int>();
            //if (!tids.Any())
            //    return FalidResult("当前分享账号没有创建模板使用分享单号，无需导出");

            //var baseSql = $@"
            //     SELECT t1.TemplateId,t1.GetDate AS CreateTime,t1.ExpressWayBillCode,t1.OrderId,t1.Status,t1.ShopId,t1.Reciver,t1.ToProvince,t1.ToCity,t1.ToDistrict FROM dbo.P_WaybillCode AS t1 WITH(NOLOCK)
            //     WHERE t1.TemplateId IN({string.Join(",", tids)}) AND t1.GetDate >= '{sDateStr}' AND t1.GetDate < '{eDateStr}' AND t1.Status != 2 
            //     ORDER BY t1.GetDate DESC";
            var _taskService = new ExportTaskService();
            var newTask = new ExportTask
            {
                IP = Request.UserHostAddress,
                PlatformType = curShop.PlatformType,
                ShopId = curShop.Id,
                UserId = SiteContext.Current.CurrentLoginSubUser?.Id.ToString(),
                CreateTime = DateTime.Now,
                Type = ExportType.ErpBranchShare.ToInt(),
                //ExtField1 = string.Join(",", idLst),
                //ExecSql = baseSql,
                ParamJson = model.ToJson(),
                PageSize = 1000,
                PageIndex = 1,
                TotalCount = totalCount,
                FromModule = "分享单号使用明细--Excel导出"
            };
            newTask.HopeExecuteTime = new CommService().ExportExecuteCheck(newTask.UserId.ToInt(), newTask.Type);
            newTask.Id = _taskService.Add(newTask);
            return SuccessResult("导出任务创建成功", GetExportTaskToWeb(newTask));
        }

        //[LogForOperatorFilter("导出使用明细列表")]
        //public ActionResult ExportUsedToExcel_OLD()
        //{
        //    var options = Request.Form["options"].ToString2();
        //    options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
        //    options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果
        //    var jToken = options.ToObject<JToken>();
        //    //var model = jToken?.Value<string>("Model")?.ToObject<OrderSearchModel>() ?? new OrderSearchModel();
        //    //var names = jToken?.Value<string>("Names");
        //    var ids = jToken?.Value<string>("Ids");
        //    var startTime = jToken?.Value<string>("startTime");
        //    var endTime = jToken?.Value<string>("endTime");
        //    if (ids.IsNullOrEmpty())
        //    {
        //        Log.WriteError("无使用明细可导出");
        //        return FalidResult("无使用明细可导出");
        //    }
        //    //if (names.IsNullOrEmpty())
        //    //{
        //    //    Log.WriteError("分享电子面单导出异常：无Excel表头数据");
        //    //    return FalidResult("分享电子面单导出异常：无Excel表头数据");
        //    //}
        //    var sDate = new DateTime();
        //    if (!DateTime.TryParse(startTime, out sDate))
        //    {
        //        Log.WriteError("开始时间设置无效");
        //        return FalidResult("开始时间设置无效");
        //    }
        //    var eDate = new DateTime();
        //    if (!DateTime.TryParse(endTime, out eDate))
        //    {
        //        Log.WriteError("结束时间设置无效");
        //        return FalidResult("结束时间设置无效");
        //    }
        //    var idLst = ids.ToList<int>();
        //    var curShopId = SiteContext.Current.CurrentShopId;
        //    // 当前店铺是分享店铺，查询分享店铺列表，否则只能查看自己被分享的内容
        //    var brandModel = _service.GetListByFromId(curShopId);
        //    var isFromShop = brandModel != null && brandModel.Any();

        //    var models = _service.GetByIds(idLst);

        //    var sDateStr = sDate.ToString("yyyy-MM-dd HH:mm:ss");
        //    var eDateStr = eDate.ToString("yyyy-MM-dd HH:mm:ss");
        //    var list = _service.GetUsedDetailList(idLst, new List<int> { 1, 3 }, sDateStr, eDateStr);
        //    if (list == null || !list.Any())
        //        return FalidResult("无打印数据");

        //    var fileName = ExcelHelper.GetFileName("电子面单使用明细.xlsx", Request);
        //    //var headNames = names.ToList<string>();
        //    var workbook = BuildExcel(models, fileName, list, false);

        //    Response.Cookies.Add(new HttpCookie("downloadToken", Request.Form["downloadToken"].ToString2()));

        //    var rootPath = Server.MapPath("../Files") + $"\\电子面单使用明细{SiteContext.Current.CurrentShopId}.xlsx";
        //    using (var fs = new FileStream(rootPath, FileMode.Create, FileAccess.Write))
        //    {
        //        workbook.Write(fs);
        //    }
        //    var memoryStream = new MemoryStream();
        //    using (var fileStream = new FileStream(rootPath, FileMode.Open))
        //    {
        //        fileStream.CopyTo(memoryStream);
        //    }
        //    memoryStream.Position = 0;
        //    if (System.IO.File.Exists(rootPath))
        //        System.IO.File.Delete(rootPath);
        //    return File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);

        //    //var buffer = new byte[] { };  
        //    //using (MemoryStream ms = new MemoryStream())
        //    //{
        //    //    workbook.Write(ms);
        //    //    buffer = ms.GetBuffer();
        //    //    ms.Position = 0;
        //    //    return File(ms, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        //    //    ms.Close();
        //    //    //application / vnd.openxmlformats - officedocument.spreadsheetml.sheet
        //    //    //application / vnd.ms - excel
        //    //}

        //    //return File(buffer, "application/ms-excel", ExcelHelper.GetFileName(fileName + ".xls", Request));
        //}
        #region Excel导出

        public ActionResult LoadExportTask()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var _taskService = new BranchShareExportExcelTaskService();
            var task = _taskService.GetExportTaskByShopId(shopId);
            if (task != null)
                return Json(task);
            else
                return FalidResult("-1");
        }
        //public ActionResult CheckExportTaskStatus(int id)
        //{
        //    if (id <= 0)
        //        return FalidResult("-1");
        //    var task = _taskService.Get(id);
        //    if (task == null)
        //        return FalidResult("导出任务不存在");
        //    return Json(task);
        //}


        //[LogForOperatorFilter("更新使用明细导出任务状态")]
        //public ActionResult UpdateExportTaskStatus(int id, int status)
        //{
        //    var task = _taskService.Get(id);
        //    if (task == null)
        //        return FalidResult("任务已经被删除");
        //    task.Status = status;
        //    _taskService.Update(task);
        //    return Json(task);
        //}
        private WaybillCodeSimpleModel EncryptReceiverInfo(WaybillCodeSimpleModel order)
        {
            order.Reciver = order.Reciver.ToEncryptName();
            //order.ToDistrict = "****";
            return order;
        }

        private IWorkbook BuildExcel(List<BranchShareRelation> models, string fileName, List<WaybillCodeSimpleModel> details, bool isWdUser)
        {
            details?.ForEach(o =>
            {
                if (o.PlatformType == PlatformType.Pinduoduo.ToString()
                    || o.PlatformType == PlatformType.Taobao.ToString()
                    || o.PlatformType == PlatformType.Jingdong.ToString())
                    EncryptReceiverInfo(o);
                if (o.PlatformType != "TouTiao" && o.PlatformType != "KuaiShou")
                {
                    o.ReciverPhone = "******";
                    o.Sender = "***";
                    o.SenderPhone = "******";
                    o.SenderAddress = "********";
                }
                else
                    o.ReciverPhone = o.ReciverPhone.ToEncrytPhone();
            });
            var isFromShop = models.FirstOrDefault().FromId == SiteContext.Current.CurrentShopId;
            IWorkbook workbook = null;
            var maxRowCount = 1048500; //1048575
            if (fileName.IndexOf(".xlsx") > 0) // 2007版本以上
                workbook = new XSSFWorkbook();
            else
            {
                workbook = new HSSFWorkbook();
                maxRowCount = 65500; //65535
            }
            //DocumentSummaryInformation docInfo = PropertySetFactory.CreateDocumentSummaryInformation();
            //docInfo.Company = "店管家";
            //SummaryInformation summaryInfo = PropertySetFactory.CreateSummaryInformation();
            //summaryInfo.Subject = fileName;

            //workbook.DocumentSummaryInformation = docInfo;
            //workbook.SummaryInformation = summaryInfo;

            var sheetIndex = 0;

            ISheet sheet = workbook.CreateSheet("分享单号使用情况");

            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook);
            ICellStyle contentLongStyle = GetContentCellStyleByToLongCol(workbook);

            var headNames = new List<string> { "获取时间", "运单号", "平台", "订单编号", "收件人", "省市区", "使用状态" };

            if (isWdUser)
            {
                headNames = new List<string> { "获取时间", "运单号", "平台", "订单编号", "收件人", "收件人电话", "省市区", "发件人", "发件人电话", "发件人地址", "使用状态" };
            }

            // 第二行填充表头信息和样式    
            // 表头
            IRow header = sheet.CreateRow(0);
            header.Height = 15 * 20;
            int cellIndex = 0;
            if (isFromShop)
                headNames.InsertRange(0, new List<string> { "使用单号店铺", "备注" });
            // 设置Excel列头内容和样式
            headNames.ForEach(name =>
            {
                //设置列宽度
                sheet.SetColumnWidth(cellIndex, 20 * 256);
                // 设置列名和样式
                header.CreateCell(cellIndex).SetCellValue(name);
                header.GetCell(cellIndex).CellStyle = headStyle;
                cellIndex++;
            });

            var rowIndex = 1;  // 第2行开始填充Excel内容
            var shareIds = models.Select(m => m.Id).Distinct().ToList();
            TemplateRelationAuthInfoService _templateAuthInfoService = new TemplateRelationAuthInfoService();
            // 根据ShareId获取使用的模板 信息
            var printTemplates = _templateAuthInfoService.GetByShareRelationIds(shareIds);
            var shareTempIds = printTemplates.Where(m => m.TemplateId > 0).Select(m => m.TemplateId).Distinct().ToList();
            models.GroupBy(m => m.Id).ToList().ForEach(g =>
            {
                var model = g.FirstOrDefault();
                var tmpIds = printTemplates.Where(m => m.BranchShareRelationId == model.Id && m.TemplateId > 0).Select(m => m.TemplateId).Distinct().ToList();
                //details?.Where(m => tmpIds.Contains(m.TemplateId)).ToList().ForEach(detail =>
                tmpIds.ForEach(tid =>
                {
                    var tmpDetails = details.Where(m => m.TemplateId == tid).OrderByDescending(m => m.CreateTime).ToList();
                    tmpDetails.ForEach(detail =>
                    {
                        if (sheet.LastRowNum >= maxRowCount)
                        {
                            sheetIndex++;
                            sheet = workbook.CreateSheet("分享单号使用情况" + sheetIndex);
                            rowIndex = 1;

                            header = sheet.CreateRow(0);
                            header.Height = 15 * 20;
                            cellIndex = 0;
                            // 设置Excel列头内容和样式
                            headNames.ForEach(name =>
                            {
                                //设置列宽度
                                sheet.SetColumnWidth(cellIndex, 20 * 256);
                                // 设置列名和样式
                                header.CreateCell(cellIndex).SetCellValue(name);
                                header.GetCell(cellIndex).CellStyle = headStyle;
                                cellIndex++;
                            });
                        }
                        IRow row = sheet.CreateRow(rowIndex);
                        row.Height = 20 * 20;

                        if (isFromShop)
                        {
                            if (isWdUser)
                            {
                                //"使用单号店铺", "备注","获取时间", "运单号", "平台", "订单编号", "收件人", "收件人电话", "省市区", "发件人", "发件人电话", "发件人地址", "使用状态"
                                row.CreateCell(0).SetCellValue(model.ToShopName);
                                row.CreateCell(1).SetCellValue(model.Remark);
                                row.CreateCell(2).SetCellValue(detail.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                row.CreateCell(3).SetCellValue(detail.ExpressWayBillCode.ToString2());
                                row.CreateCell(4).SetCellValue(detail.PlatformTypeName.ToString2());
                                row.CreateCell(5).SetCellValue(detail.OrderId.ToString2());
                                row.CreateCell(6).SetCellValue(detail.Reciver.ToString2());
                                row.CreateCell(7).SetCellValue(detail.ReciverPhone.ToString2());
                                row.CreateCell(8).SetCellValue(detail.ToProvince.ToString2() + detail.ToCity.ToString2() + detail.ToDistrict.ToString2());
                                row.CreateCell(9).SetCellValue(detail.Sender.ToString2());
                                row.CreateCell(10).SetCellValue(detail.SenderPhone.ToString2());
                                row.CreateCell(11).SetCellValue(detail.SenderAddress.ToString2());
                                row.CreateCell(12).SetCellValue(detail.Status.ToString2() == "1" ? "已打印" : detail.Status.ToString2() == "2" ? "已回收" : "已发货");
                            }
                            else
                            {
                                //"使用单号店铺", "备注","获取时间", "运单号", "平台", "订单编号", "收件人", "省市区", "使用状态"
                                row.CreateCell(0).SetCellValue(model.ToShopName);
                                row.CreateCell(1).SetCellValue(model.Remark);
                                row.CreateCell(2).SetCellValue(detail.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                row.CreateCell(3).SetCellValue(detail.ExpressWayBillCode.ToString2());
                                row.CreateCell(4).SetCellValue(detail.PlatformTypeName.ToString2());
                                row.CreateCell(5).SetCellValue(detail.OrderId.ToString2());
                                row.CreateCell(6).SetCellValue(detail.Reciver.ToString2());
                                row.CreateCell(7).SetCellValue(detail.ToProvince.ToString2() + detail.ToCity.ToString2() + detail.ToDistrict.ToString2());
                                row.CreateCell(8).SetCellValue(detail.Status.ToString2() == "1" ? "已打印" : detail.Status.ToString2() == "2" ? "已回收" : "已发货");
                            }
                        }
                        else
                        {
                            if (isWdUser)
                            {
                                //"获取时间", "运单号", "平台", "订单编号", "收件人", "收件人电话", "省市区", "发件人", "发件人电话", "发件人地址", "使用状态"
                                row.CreateCell(0).SetCellValue(detail.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                row.CreateCell(1).SetCellValue(detail.ExpressWayBillCode.ToString2());
                                row.CreateCell(2).SetCellValue(detail.PlatformTypeName.ToString2());
                                row.CreateCell(3).SetCellValue(detail.OrderId.ToString2());
                                row.CreateCell(4).SetCellValue(detail.Reciver.ToString2());
                                row.CreateCell(5).SetCellValue(detail.ReciverPhone.ToString2());
                                row.CreateCell(6).SetCellValue(detail.ToProvince.ToString2() + detail.ToCity.ToString2() + detail.ToDistrict.ToString2());
                                row.CreateCell(7).SetCellValue(detail.Sender.ToString2());
                                row.CreateCell(8).SetCellValue(detail.SenderPhone.ToString2());
                                row.CreateCell(9).SetCellValue(detail.SenderAddress.ToString2());
                                row.CreateCell(10).SetCellValue(detail.Status.ToString2() == "1" ? "已打印" : detail.Status.ToString2() == "2" ? "已回收" : "已发货");
                            }
                            else
                            {
                                //"获取时间", "运单号", "平台", "订单编号", "收件人", "省市区", "使用状态"
                                row.CreateCell(0).SetCellValue(detail.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                row.CreateCell(1).SetCellValue(detail.ExpressWayBillCode.ToString2());
                                row.CreateCell(2).SetCellValue(detail.PlatformTypeName.ToString2());
                                row.CreateCell(3).SetCellValue(detail.OrderId.ToString2());
                                row.CreateCell(4).SetCellValue(detail.Reciver.ToString2());
                                row.CreateCell(5).SetCellValue(detail.ToProvince.ToString2() + detail.ToCity.ToString2() + detail.ToDistrict.ToString2());
                                row.CreateCell(6).SetCellValue(detail.Status.ToString2() == "1" ? "已打印" : detail.Status.ToString2() == "2" ? "已回收" : "已发货");
                            }
                        }

                        row.Cells.ForEach(cell =>
                        {
                            cell.CellStyle = contentStyle;
                        });
                        rowIndex++;
                    });
                });
            });
            return workbook;
        }


        private IWorkbook BuildUsedExcel(string fileName, List<BranchShareRelationLogDetail> details, List<string> headNames, bool isFromShop)
        {
            //DocumentSummaryInformation docInfo = PropertySetFactory.CreateDocumentSummaryInformation();
            //docInfo.Company = "店管家";
            //SummaryInformation summaryInfo = PropertySetFactory.CreateSummaryInformation();
            //summaryInfo.Subject = fileName;

            //HSSFWorkbook workbook = new HSSFWorkbook();
            //workbook.DocumentSummaryInformation = docInfo;
            //workbook.SummaryInformation = summaryInfo;
            var maxRowCount = 1048575;
            IWorkbook workbook = null;
            if (fileName.IndexOf(".xlsx") > 0) // 2007版本以上
                workbook = new XSSFWorkbook();
            else
            {
                workbook = new HSSFWorkbook();
                maxRowCount = 65535;
            }
            var count = Math.Ceiling(details.Count * 1.0 / maxRowCount);
            for (var i = 0; i < count; i++)
            {
                ISheet sheet = workbook.CreateSheet("分享单号使用情况" + i);

                ICellStyle headStyle = GetHeadStyle(workbook);
                ICellStyle contentStyle = GetContentStyle(workbook);
                ICellStyle contentLongStyle = GetContentCellStyleByToLongCol(workbook);

                // 第二行填充表头信息和样式    
                // 表头
                IRow header = sheet.CreateRow(0);
                header.Height = 15 * 20;
                int cellIndex = 0;

                if (isFromShop)
                    headNames.InsertRange(0, new List<string> { "使用单号店铺", "备注" });  //(fromId == SiteContext.Current.CurrentShopId ? "店管家分享店铺" : "店管家来源店铺")
                                                                                    // 设置Excel列头内容和样式
                headNames.ForEach(name =>
                {
                    //设置列宽度
                    sheet.SetColumnWidth(cellIndex, 20 * 256);
                    // 设置列名和样式
                    header.CreateCell(cellIndex).SetCellValue(name);
                    header.GetCell(cellIndex).CellStyle = headStyle;
                    cellIndex++;
                });

                var rowIndex = 1;  // 第2行开始填充Excel内容
                details.ForEach(detail =>
                {
                    IRow row = sheet.CreateRow(rowIndex);
                    row.Height = 20 * 20;
                    var authType = GetAuthType(detail.AuthType.ToString2());

                    row.CreateCell(0).SetCellValue(detail.ToShopName);
                    row.CreateCell(1).SetCellValue(detail.Remark);

                    row.CreateCell(2).SetCellValue(detail.ToShopName);
                    row.CreateCell(3).SetCellValue(detail.ToPlatformTypeName);
                    row.CreateCell(4).SetCellValue(detail.ExpressName + (detail.AuthType.IsNullOrEmpty() ? "" : authType));
                    row.CreateCell(5).SetCellValue(detail.BranchAddress.ToString2());
                    row.CreateCell(6).SetCellValue(detail.UsedCount.ToString2());

                    if (!isFromShop)
                    {
                        row.CreateCell(7).SetCellValue(detail.MainShopUsedCount.ToString2());
                        row.CreateCell(8).SetCellValue(detail.ChildShopCount.ToString2());
                        row.CreateCell(9).SetCellValue(detail.ChildShopUsedCount.ToString2());
                    }

                    row.Cells.ForEach(cell =>
                    {
                        cell.CellStyle = contentStyle;
                    });
                    rowIndex++;
                });
            }
            return workbook;
        }

        private string GetAuthType(string authType)
        {
            var returnAuthType = "";
            switch (authType.ToString2().ToLower())
            {
                case "taobao":
                    returnAuthType = "【淘宝菜鸟】";
                    break;
                case "link":
                    returnAuthType = "【菜鸟官方】";
                    break;
                case "pinduoduo":
                case "pddwaybill":
                    returnAuthType = "【拼多多】";
                    break;
                case "jingdong":
                    returnAuthType = "【京东无界】";
                    break;
                case "toutiao":
                    returnAuthType = "【抖店】";
                    break;
                case "kuaishou":
                    returnAuthType = "【快手】";
                    break;
                case "xiaohongshu":
                    returnAuthType = "【小红书】";
                    break;
                case "wxvideo":
                    returnAuthType = "【视频号】";
                    break;
            }
            return returnAuthType;
        }

        //private ICellStyle GetHeadStyle(IWorkbook workbook)
        //{
        //    IFont font = workbook.CreateFont();
        //    font.FontName = "Times New Roman";
        //    font.Boldweight = short.MaxValue;
        //    font.FontHeightInPoints = 11;

        //    ICellStyle headerStyle = workbook.CreateCellStyle();
        //    headerStyle.SetFont(font);
        //    headerStyle.Alignment = HorizontalAlignment.Center;//内容居中显示
        //    headerStyle.WrapText = true;
        //    return headerStyle;
        //}

        private ICellStyle GetContentStyle(IWorkbook excel)
        {
            IFont font = excel.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = excel.CreateCellStyle();
            contentStyle.SetFont(font);
            contentStyle.Alignment = HorizontalAlignment.Center;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.WrapText = true;

            return contentStyle;
        }

        private ICellStyle GetContentCellStyleByToLongCol(IWorkbook excel)
        {
            ICellStyle contentStyle = GetContentStyle(excel);
            contentStyle.Alignment = HorizontalAlignment.Left;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            return contentStyle;
        }

        #endregion Excel导出

        private void AddLog(BranchShareRelation model, ShareLogType type, int appendCount = 0)
        {
            var log = new BranchShareRelationLog
            {
                ShareId = model.Id,
                OperatorId = SiteContext.Current.CurrentShopId,
                UserId = SiteContext.Current.CurrentLoginUser?.Id.ToString(),
                ShopId = model.ToId,
                CreateTime = DateTime.Now,
                Type = type.ToString(),
                TotalQuantity = model.TotalQuantity,
                Balance = model.Balance,
                AppendCount = appendCount
            };

            _logService.Add(log);
        }


        [LogForOperatorFilter("查看充值明细列表")]
        public ActionResult LoadAppendLogList(OrderSearchModel model)
        {
            var curShopId = SiteContext.Current.CurrentShopId;
            var brandModel = _service.GetListByFromId(curShopId);
            var isFromShop = brandModel != null && brandModel.Any();

            var authTypeFilter = model.Filters.FirstOrDefault(m => m.Name == "AuthType");
            if (authTypeFilter != null)
            {
                model.Filters.Remove(authTypeFilter);
            }

            var shopIdFilter = model.Filters.FirstOrDefault(m => m.Name == "ToId");
            if (shopIdFilter != null)
            {
                shopIdFilter.Name = isFromShop ? "ToId" : "FromId";
                model.Filters.Add(new OrderSearchFieldModel
                {
                    TableAlias = "s",
                    FieldType = FieldTypeFilter.Int32,
                    Name = isFromShop ? "FromId" : "ToId",
                    Value = curShopId.ToString(),
                    Contract = ContractFilter.equal
                });
            }
            else
                model.Filters.Add(new OrderSearchFieldModel
                {
                    TableAlias = "s",
                    FieldType = FieldTypeFilter.Int32,
                    Name = isFromShop ? "FromId" : "ToId",
                    Value = curShopId.ToString(),
                    Contract = ContractFilter.equal
                });

            var log = LogForOperatorContext.Current.logInfo;
            var requestMoel = model.ToJson().ToObject<OrderSearchModel>();
            if (authTypeFilter != null)
                requestMoel.Filters.Add(authTypeFilter);
            log.Request = requestMoel;

            var list = _service.GetAppendLogList(model);
            var _caiNiaoAuthInfoService = new CaiNiaoAuthInfoService();
            var _shopService = new ShopService();

            var authShopIds = list.Where(m => m.AuthSourceType.ToInt() == 1).Select(m => m.CaiNiaoAuthInfoId).Distinct().ToList();
            var authShops = authShopIds.Any() ? _shopService.GetShopByIds(authShopIds) : new List<Shop>();

            var caiNiaoAuthInfoIds = list.Where(m => m.AuthSourceType.ToInt() == 0).Select(m => m.CaiNiaoAuthInfoId).Distinct().ToList();
            var authInfos = caiNiaoAuthInfoIds.Any() ? _caiNiaoAuthInfoService.GetByIds(caiNiaoAuthInfoIds) : new List<CaiNiaoAuthInfo>();
            list.ForEach(bs =>
            {
                bs.AuthType = bs.AuthSourceType.ToInt() == 1 ?
                      authShops.FirstOrDefault(m => m.Id == bs.CaiNiaoAuthInfoId)?.PlatformType :
                      authInfos?.Where(m => m.Id == bs.CaiNiaoAuthInfoId).FirstOrDefault()?.AuthType ?? "";
                //bs.AuthType = bs.AuthSourceType.ToInt() == 1 ? (isFromShop ? bs.ToPlatformType.ToString2() : bs.FromPlatformType.ToString2()) : authInfos?.Where(m => m.Id == bs.CaiNiaoAuthInfoId).FirstOrDefault()?.AuthType ?? "";
            });

            if (authTypeFilter != null && list.Any())
                list = list.Where(m => m.AuthType.ToString2().ToLower() == authTypeFilter.Value.ToString2().ToLower()).ToList();

            var rowCount = list.Count;
            var rows = list.Skip((model.PageIndex - 1) * model.PageSize).Take(model.PageSize).ToList();
            var appendTotalQuantity = list.Sum(m => m.AppendCount.ToInt());

            var pagerows = new AppendLogPageResultModel
            {
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                Total = rowCount,
                Rows = rows,
                OrderByField = model.OrderByField,
                IsOrderDesc = model.IsOrderDesc,
                AppendTotalQuantity = appendTotalQuantity
            };
            return Json(pagerows);
        }


        [LogForOperatorFilter("查看使用明细列表")]
        public ActionResult LoadUsedList(OrderSearchModel model)
        {
            var curShopId = SiteContext.Current.CurrentShopId;
            var brandModel = _service.GetListByFromId(curShopId);
            var isFromShop = brandModel != null && brandModel.Any();

            var authTypeFilter = model.Filters.FirstOrDefault(m => m.Name == "AuthType");
            if (authTypeFilter != null)
            {
                model.Filters.Remove(authTypeFilter);
            }

            var shopIdFilter = model.Filters.FirstOrDefault(m => m.Name == "ToId");
            if (shopIdFilter != null)
            {
                shopIdFilter.Name = isFromShop ? "ToId" : "FromId";
                model.Filters.Add(new OrderSearchFieldModel
                {
                    TableAlias = "s",
                    FieldType = FieldTypeFilter.Int32,
                    Name = isFromShop ? "FromId" : "ToId",
                    Value = curShopId.ToString(),
                    Contract = ContractFilter.equal
                });
            }
            else
                model.Filters.Add(new OrderSearchFieldModel
                {
                    TableAlias = "s",
                    FieldType = FieldTypeFilter.Int32,
                    Name = isFromShop ? "FromId" : "ToId",
                    Value = curShopId.ToString(),
                    Contract = ContractFilter.equal
                });

            var log = LogForOperatorContext.Current.logInfo;
            var requestMoel = model.ToJson().ToObject<OrderSearchModel>();
            if (authTypeFilter != null)
                requestMoel.Filters.Add(authTypeFilter);
            log.Request = requestMoel;

            var list = _service.GetUsedDetailList2(model, isFromShop)?.ToList() ?? new List<BranchShareRelationLogDetail>();
            //var list = _service.GetUsedList(model, isFromShop, isLoadDeleted: false, isUsedShare: true)?.OrderByDescending(m => m.CreateTime).ToList() ?? new List<BranchShareRelationLogDetail>();

            //var _caiNiaoAuthInfoService = new CaiNiaoAuthInfoService();
            //var _shopService = new ShopService();

            //var authShopIds = list.Where(m => m.AuthSourceType.ToInt() == 1).Select(m => m.CaiNiaoAuthInfoId).Distinct().ToList();
            //var authShops = authShopIds.Any() ? _shopService.GetShopByIds(authShopIds) : new List<Shop>();

            //var caiNiaoAuthInfoIds = list.Where(m => m.AuthSourceType.ToInt() == 0).Select(m => m.CaiNiaoAuthInfoId).Distinct().ToList();
            //var authInfos = caiNiaoAuthInfoIds.Any() ? _caiNiaoAuthInfoService.GetByIds(caiNiaoAuthInfoIds) : new List<CaiNiaoAuthInfo>();
            //list.ForEach(bs =>
            //{
            //    bs.AuthType = bs.AuthSourceType.ToInt() == 1 ?
            //           authShops.FirstOrDefault(m => m.Id == bs.CaiNiaoAuthInfoId)?.PlatformType :
            //           authInfos?.Where(m => m.Id == bs.CaiNiaoAuthInfoId).FirstOrDefault()?.AuthType ?? "";
            //    //bs.AuthType = bs.AuthSourceType.ToInt() == 1 ? (isFromShop ? bs.ToPlatformType.ToString2() : bs.FromPlatformType.ToString2()) : authInfos?.Where(m => m.Id == bs.CaiNiaoAuthInfoId).FirstOrDefault()?.AuthType ?? "";
            //});

            if (authTypeFilter != null && list.Any())
                list = list.Where(m => m.AuthType.ToString2().ToLower() == authTypeFilter.Value.ToString2().ToLower()).ToList();

            var shareIds = list.SelectMany(m => m.ShareIds).Distinct().ToList();
            //var rowCount = list.Count;
            //var rows = list.Skip((model.PageIndex - 1) * model.PageSize).Take(model.PageSize).ToList();
            var usedTotalQuantity = list.Sum(m => m.UsedCount.ToInt());
            var pagerows = new UsedLogPageResultModel
            {
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                Total = model.TotalCount,//rowCount,
                Rows = list,
                OrderByField = model.OrderByField,
                IsOrderDesc = model.IsOrderDesc,
                UsedTotalQuantity = usedTotalQuantity,
                ShareIds = shareIds
            };
            return Json(pagerows);
        }


        [LogForOperatorFilter("查看回收明细列表")]
        public ActionResult LoadRecycleList(OrderSearchModel model)
        {
            var curShopId = SiteContext.Current.CurrentShopId;
            var brandModel = _service.GetListByFromId(curShopId);
            var isFromShop = brandModel != null && brandModel.Any();

            var authTypeFilter = model.Filters.FirstOrDefault(m => m.Name == "AuthType");
            if (authTypeFilter != null)
            {
                model.Filters.Remove(authTypeFilter);
            }

            var shopIdFilter = model.Filters.FirstOrDefault(m => m.Name == "ToId");
            if (shopIdFilter != null)
            {
                shopIdFilter.Name = isFromShop ? "ToId" : "FromId";
                model.Filters.Add(new OrderSearchFieldModel
                {
                    TableAlias = "s",
                    FieldType = FieldTypeFilter.Int32,
                    Name = isFromShop ? "FromId" : "ToId",
                    Value = curShopId.ToString(),
                    Contract = ContractFilter.equal
                });
            }
            else
                model.Filters.Add(new OrderSearchFieldModel
                {
                    TableAlias = "s",
                    FieldType = FieldTypeFilter.Int32,
                    Name = isFromShop ? "FromId" : "ToId",
                    Value = curShopId.ToString(),
                    Contract = ContractFilter.equal
                });

            var log = LogForOperatorContext.Current.logInfo;
            var requestMoel = model.ToJson().ToObject<OrderSearchModel>();
            if (authTypeFilter != null)
                requestMoel.Filters.Add(authTypeFilter);
            log.Request = requestMoel;

            var list = _service.GetUsedDetailList2(model, isFromShop)?.ToList() ?? new List<BranchShareRelationLogDetail>();
            //var list = _service.GetUsedList(model, isFromShop, isLoadDeleted: false, isUsedShare: false)?.OrderByDescending(m => m.CreateTime).ToList() ?? new List<BranchShareRelationLogDetail>();

            var _caiNiaoAuthInfoService = new CaiNiaoAuthInfoService();
            var _shopService = new ShopService();

            var authShopIds = list.Where(m => m.AuthSourceType.ToInt() == 1).Select(m => m.CaiNiaoAuthInfoId).Distinct().ToList();
            var authShops = authShopIds.Any() ? _shopService.GetShopByIds(authShopIds) : new List<Shop>();

            var caiNiaoAuthInfoIds = list.Where(m => m.AuthSourceType.ToInt() == 0).Select(m => m.CaiNiaoAuthInfoId).Distinct().ToList();
            var authInfos = caiNiaoAuthInfoIds.Any() ? _caiNiaoAuthInfoService.GetByIds(caiNiaoAuthInfoIds) : new List<CaiNiaoAuthInfo>();
            list.ForEach(bs =>
            {
                bs.AuthType = bs.AuthSourceType.ToInt() == 1 ?
                       authShops.FirstOrDefault(m => m.Id == bs.CaiNiaoAuthInfoId)?.PlatformType :
                       authInfos?.Where(m => m.Id == bs.CaiNiaoAuthInfoId).FirstOrDefault()?.AuthType ?? "";
                //bs.AuthType = bs.AuthSourceType.ToInt() == 1 ? (isFromShop ? bs.ToPlatformType.ToString2() : bs.FromPlatformType.ToString2()) : authInfos?.Where(m => m.Id == bs.CaiNiaoAuthInfoId).FirstOrDefault()?.AuthType ?? "";
            });

            if (authTypeFilter != null && list.Any())
                list = list.Where(m => m.AuthType.ToString2().ToLower() == authTypeFilter.Value.ToString2().ToLower()).ToList();

            //var rowCount = list.Count;
            //var rows = list.Skip((model.PageIndex - 1) * model.PageSize).Take(model.PageSize).ToList();
            var recycleTotalQuantity = list.Sum(m => m.UsedCount.ToInt());

            var pagerows = new UsedLogPageResultModel
            {
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                Total = model.TotalCount,//rowCount,
                Rows = list,//rows,
                OrderByField = model.OrderByField,
                IsOrderDesc = model.IsOrderDesc,
                UsedTotalQuantity = recycleTotalQuantity
            };
            return Json(pagerows);
        }


        [LogForOperatorFilter("查询单号归属")]
        public ActionResult SearchWaybillFromWhere(List<string> waybillCodes)
        {
            if (waybillCodes == null || waybillCodes?.Any() == false)
            {
                return FalidResult("运单号解析有误");
            }

            waybillCodes = waybillCodes.Distinct().ToList();
            if (waybillCodes?.Count > 500)
                return FalidResult("一次不能查询超过500个单号");

            var searchResult = _service.SearchWaybillCodeFromWhere(waybillCodes, SiteContext.Current.CurrentShopId);
            #region 收件人信息脱敏
            //EncryptionService DataMaskservice = new EncryptionService();
            //var noVirtualOrders = DataMaskservice.getPlatformType(searchResult);
            //EncryptionService.DataMaskingExpression(searchResult.Where(w=> noVirtualOrders.Any(a=>a.LogicOrderId == w.OrderId) )?.ToList());//EncryptionService.DataMaskingReflection(searchResult);

            var notEncryptOrders = searchResult.Where(x => x.WaybillPlatform.IsNullOrEmpty() || x.WaybillPlatform == PlatformType.WeiDian.ToString() || x.WaybillPlatform == PlatformType.WxVideo.ToString() || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.WaybillPlatform.ToString2()) == false && x.WaybillPlatform != PlatformType.Virtual.ToString())).ToList();
            EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);

            #endregion
            return Json(searchResult);
        }

        public ActionResult LoadCurrenMonthStatistic(string ids)
        {
            var idLst = new List<int>();
            if (ids.IsNullOrEmpty())
                return FalidResult("无分享单号统计");

            var sDateStr = DateTime.Now.ToString("yyyy-MM") + "-01";
            var eDateStr = DateTime.Now.AddMonths(1).ToString("yyyy-MM") + "-01";
            idLst = ids.ToList<int>();

            var result = _service.GetCurrenMonthStatistic(idLst, sDateStr, eDateStr);
            if (result == null)
                return FalidResult(null);
            else if (result.Any(m => m.UsedCount == -1)) //超时
                return FalidResult(result.ToJson());
            return Json(result);
        }

        public ActionResult LoadUsedStatistic(string ids, string startTime, string endTime)
        {
            if (ids.IsNullOrEmpty())
                return null;

            var curShopId = SiteContext.Current.CurrentShopId;
            var brandModel = _service.GetListByFromId(curShopId);
            var isFromShop = brandModel != null && brandModel.Any();

            var idLst = ids.ToList<int>();
            var result = _service.GetWaybillCodeUsedList(idLst, isFromShop, startTime, endTime);
            return Json(result);
        }

        [LogForOperatorFilter("导入表格查询单号归属")]
        public ActionResult ExcelUpload()
        {
            if (Request.Files.Count == 0 || Request.Files["upfile"] == null)
                return FalidResult("没有选择文件上传");

            try
            {
                DataTable dt = new DataTable();
                var postedFile = Request.Files["upfile"];//获取上传的文件
                string fileName = postedFile.FileName;
                string errMsg = string.Empty;
                if (fileName.IndexOf(".xlsx") > 0) // 2007版本以上  
                    dt = ExcelHelper.GetExcelDataTable(out errMsg, stream: postedFile.InputStream, fileExt: ".xlsx");
                else if (fileName.IndexOf(".xls") > 0) // 2003版本  
                    dt = ExcelHelper.GetExcelDataTable(out errMsg, stream: postedFile.InputStream, fileExt: ".xls");

                if (!errMsg.IsNullOrEmpty())
                {
                    return FalidResult(errMsg);
                }

                if (dt == null || dt.Rows.Count == 0)
                {
                    return FalidResult("请勿导入空文件");
                }

                if (dt.Rows.Count > 500)
                    return FalidResult("一次不能导入超过500条");

                var waybillCodes = new List<string>();
                //解析
                foreach (DataRow row in dt.Rows)
                {
                    var wc = row[0]?.ToString();
                    waybillCodes.Add(wc);
                }

                if (waybillCodes.Any() == false)
                    return FalidResult("Excel第一列不是运单号");
                return SearchWaybillFromWhere(waybillCodes);
            }
            catch (Exception ex)
            {
                Log.WriteError("单号分享-运单号归属导入查询-解析Excel文件失败：" + ex.Message);
                return FalidResult(ex.Message);
            }
        }

        #region 结果导出

        [LogForOperatorFilter("导出单号归属查询结果")]
        public ActionResult SearchExportExcel()
        {
            try
            {

                var options = Request.Form["options"].ToString2();
                options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
                options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果

                var requestParams = JsonExtension.ToObject<dynamic>(options) ?? null;

                JArray jarray = requestParams?.waybillCodes;

                var waybillCodes = new List<string>();
                jarray.ToList().ForEach(f =>
                {
                    var wc = f.Value<string>();
                    if (string.IsNullOrWhiteSpace(wc) == false)
                        waybillCodes.Add(wc);
                });

                if (waybillCodes == null || waybillCodes?.Any() == false)
                {
                    return FalidResult("运单号解析有误");
                }

                waybillCodes = waybillCodes.Distinct().ToList();
                if (waybillCodes?.Count > 500)
                    return FalidResult("一次不能导出超过500个单号");

                var searchResult = _service.SearchWaybillCodeFromWhere(waybillCodes, SiteContext.Current.CurrentShopId);
                if ((searchResult?.Count ?? 0) == 0)
                    return FalidResult("无满足条件的数据导出");
                #region 收件人信息脱敏
                //EncryptionService DataMaskservice = new EncryptionService();
                //var noVirtualOrders = DataMaskservice.getPlatformType(searchResult);
                //EncryptionService.DataMaskingExpression(searchResult.Where(w=>  noVirtualOrders.Any(a=>a.LogicOrderId == w.OrderId) )?.ToList());//EncryptionService.DataMaskingReflection(searchResult);

                var notEncryptOrders = searchResult.Where(x => x.WaybillPlatform.IsNullOrEmpty() || x.WaybillPlatform == PlatformType.WeiDian.ToString() || x.WaybillPlatform == PlatformType.WxVideo.ToString() || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.WaybillPlatform.ToString2()) == false && x.WaybillPlatform != PlatformType.Virtual.ToString())).ToList();
                EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);

                #endregion
                var fileName = "单号归属查询结果-导出.xls";
                IWorkbook workbook = BuildExcel(searchResult, fileName);

                Response.Cookies.Add(new HttpCookie("downloadToken", Request.Form["downloadToken"].ToString2()));
                using (MemoryStream ms = new MemoryStream())
                {
                    workbook.Write(ms);
                    var buffer = ms.GetBuffer();
                    return File(buffer, "application/ms-excel", ExcelHelper.GetFileName(fileName, Request));
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"单号归属查询Excel导出失败：{ex}");
                return FalidResult("程序异常，请联系我们");
            }
        }
        private SeachWaybillCodeFromViewModel EncryptReceiverInfo(SeachWaybillCodeFromViewModel order)
        {
            order.Reciver = order.Reciver.ToEncryptName();
            return order;
        }
        private IWorkbook BuildExcel(List<SeachWaybillCodeFromViewModel> dataList, string fileName)
        {
            if (dataList != null && dataList.Any())
            {
                dataList.ForEach(o =>
                {
                    if (o.ShopPlatform == PlatformType.Pinduoduo.ToString() || o.ShopPlatform == "拼多多"
                        || o.ShopPlatform == PlatformType.Taobao.ToString() || o.ShopPlatform == "淘宝"
                        || o.ShopPlatform == PlatformType.Jingdong.ToString() || o.ShopPlatform == "京东")
                        EncryptReceiverInfo(o);
                });
            }
            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet();
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            var heads = new Dictionary<string, string>() {
                { "快递单号" ,"ExpressWayBillCode"},
                { "收件人","Reciver"},
                { "所在平台","ShopPlatform"},
                { "店铺名称","ShopName"},
                { "备注","Remark"},
                { "打印时间","GetDate"},
            };
            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;
            dataList.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;
                var dic = (model ?? new SeachWaybillCodeFromViewModel()).ToDictionary();

                colIndex = 0;
                foreach (var item in heads.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2() ?? "";
                    if (dic?.ContainsKey(key) == true)
                    {
                        var val = dic[key]?.ToString() ?? "";

                        dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                        dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                        colIndex++;
                    }
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;
            });

            return workbook;
        }

        private void SetColumnWidth(ISheet sheet, string headName, int index)
        {
            int width = 20 * 256;
            //if (headName == "序号" || headName == "商品数量")
            //    width = 10 * 256;
            //else if (headName == "省份" || headName == "重量" || headName == "订单金额")
            //    width = 16 * 256;
            //else if (headName == "快递单号" || headName == "收件人姓名" || headName == "发件人" || headName == "发货时间" || headName == "打单时间" || headName == "状态" || headName == "收件人电话")
            //    width = 20 * 256;
            //else if (headName == "快递公司" || headName == "订单编号" || headName == "买家旺旺" || headName == "店铺名称")
            //    width = 25 * 256;
            //else if (headName == "详细地址" || headName == "买家留言" || headName == "卖家备注")
            //    width = 35 * 256;
            //else if (headName == "发货内容")
            //    width = 45 * 256;
            sheet.SetColumnWidth(index, width);
        }


        private ICellStyle GetHeadStyle(IWorkbook workbook)
        {
            IFont font = workbook.CreateFont();
            font.FontName = "Times New Roman";
            font.Boldweight = short.MaxValue;
            font.FontHeightInPoints = 11;

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(font);
            headerStyle.Alignment = HorizontalAlignment.Center;//内容居中显示
            headerStyle.WrapText = true;

            headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightOrange.Index;
            headerStyle.FillPattern = FillPattern.SolidForeground;
            return headerStyle;
        }

        private ICellStyle GetContentStyle(IWorkbook workbook, HorizontalAlignment alignment)
        {
            IFont font = workbook.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = workbook.CreateCellStyle();
            contentStyle.SetFont(font);
            contentStyle.Alignment = alignment;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.WrapText = true;

            return contentStyle;
        }

        private MemoryStream StreamToMemoryStream(Stream instream)
        {
            MemoryStream outstream = new MemoryStream();
            const int bufferLen = 4096;
            byte[] buffer = new byte[bufferLen];
            int count = 0;
            while ((count = instream.Read(buffer, 0, bufferLen)) > 0)
            {
                outstream.Write(buffer, 0, count);
            }
            return outstream;
        }

        public ActionResult CheckIsSavedShareBranchUserInfo()
        {
            //先判断当前用户是否是分享主
            var shopId = SiteContext.Current.CurrentShopId;
            var shareBranchList = _service.GetListByFromId(shopId);
            if (shareBranchList.Any())
            {
                //判断是否有保存过信息
                var shareBranchUserInfo = (new ShareBranchContactInfoService()).GetListByShopId(shopId);
                if (shareBranchUserInfo.Any())
                {
                    return SuccessResult(true); //已保存
                }
                return SuccessResult(false); //未保存
            }
            return SuccessResult(true); //非分享主不需要保存
        }

        public ActionResult AddShareBranchUserInfo(ShareBranchContactInfo model)
        {
            model.ShopId = SiteContext.Current.CurrentShopId;
            var result = (new ShareBranchContactInfoService()).Add(model);
            if (result > 0)
                return SuccessResult();
            return FalidResult("保存失败");
        }

        #endregion

        /// <summary>
        /// 加载分享时需要指定的额外信息
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadSharedOtherInfo(string authType, string cpCode, string authAcountId, string authSourceType, string expressCode)
        {
            var payTypes = new List<LogisticPayTypes>(); //支付方式
            var expTypes = new List<LogisticBusinessTypes>(); //业务类型
            var ebillAccountExtensionList = new List<EBillAccountExtension>(); //快手电子面单账号的其他扩展信息，月结号、顾客编码等
            //1.快手直营快递需要 支付方式、业务类型、月结号、顾客编码（顺丰）
            if (authType == "KuaiShou")
            {
                var code = $"{authType}_{cpCode}";
                payTypes = _logisticPayTypesService.GetList(code); //获取支付类型
                expTypes = _logisticBusinessTypesService.GetList(code); //获取业务类型

                var ebillAcountCode = $"{authAcountId}_{authSourceType}_{expressCode}".ToShortMd5();
                ebillAccountExtensionList = _ebillAccountService.GetList(ebillAcountCode); //获取电子面单账号的扩展信息

                if (ebillAccountExtensionList == null || ebillAccountExtensionList.Any() == false)
                {
                    ebillAccountExtensionList.Add(new EBillAccountExtension()
                    {
                        CaiNiaoAuthInfoId = authAcountId.ToInt(),
                        AuthSourceType = authSourceType.ToInt(),
                        ExpressCompanyCode = expressCode,
                        ExtKeyName = "customerCode",
                        ExtValue = string.Empty,
                        IsChanged = false,
                        CreateTime = DateTime.Now
                    });

                    //顺丰多个顾客编码
                    if (cpCode == "SF")
                        ebillAccountExtensionList.Add(new EBillAccountExtension()
                        {
                            CaiNiaoAuthInfoId = authAcountId.ToInt(),
                            AuthSourceType = authSourceType.ToInt(),
                            ExpressCompanyCode = expressCode,
                            ExtKeyName = "isvClientCode",
                            ExtValue = string.Empty,
                            IsChanged = false,
                            CreateTime = DateTime.Now
                        });
                }
            }
            return SuccessResult(new
            {
                PayTypes = payTypes,
                ExpTypes = expTypes,
                EbillAccountExts = ebillAccountExtensionList
            });
        }
    }
}