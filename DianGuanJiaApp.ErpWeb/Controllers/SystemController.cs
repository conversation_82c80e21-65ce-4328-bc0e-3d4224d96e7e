using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Caching;
using System.Web.Mvc;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.Services.ManualOrder;
using DianGuanJiaApp.Services.Services.SyncDataInterface;
using System.Data;
using DianGuanJiaApp.Portal.Controllers;
using System.Threading;
using DianGuanJiaApp.Data.Model.OpenTelemetry;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    public class SystemController : BaseController
    {
        private UserFxService _userFxService = new UserFxService();
        private FxUserShopService _fxUserShopService = new FxUserShopService();
        private SupplierUserService _supplierUserService = new SupplierUserService();
        private FxUserAddressService _fxUserAddressService = new FxUserAddressService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private CheckRuleService checkrule = new CheckRuleService();
        private QuickSearchService qsserver = new QuickSearchService();
        private FxWeChatUserService _fxwechatuserService = new FxWeChatUserService();
        private FxWeChatQRCodeService _fxwechatqrcodeService = new FxWeChatQRCodeService();
        private readonly BusinessCardService _businessCardService = new BusinessCardService();
        private AsyncTaskService _asyncTaskService = new AsyncTaskService();
        private readonly CooperateEvaluateService _cooperateEvaluateService = new CooperateEvaluateService();
        /// <summary>
        /// 系统设置
        /// </summary>
        /// <returns></returns>
        [FxAuthorize(FxPermission.SystemIndex)]
        public ActionResult Index()
        {
            FxUserAddress defaultAddres = _fxUserAddressService.GetByFxUserId(SiteContext.Current.CurrentFxUserId); //缓存性能优化:默认不加载地址数据
            ViewBag.FxUserAddres = defaultAddres != null ? defaultAddres.ToJson() : (new FxUserAddress()).ToJson();
            return View();
        }
        public ActionResult WxCorrelationSuccess()
        {
            return View();
        }

        //[FxAuthorize(FxPermission.SubAccountManagement)]
        public ActionResult BindWxUserIndex()
        {
            // 页面按钮展示权限
            ViewBag.ShowPermDict = new Dictionary<string, bool>
            {
                {$"#{nameof(FxPermission.AddPostPermission)}",SiteContext.HasPermission(FxPermission.AddPostPermission)},
                {$".{nameof(FxPermission.UnbindSubAccount)}",SiteContext.HasPermission(FxPermission.UnbindSubAccount)},
                {$"#{nameof(FxPermission.EditPostPermission)}",SiteContext.HasPermission(FxPermission.EditPostPermission)},
                {$".{nameof(FxPermission.AddSubAccount)}",SiteContext.HasPermission(FxPermission.AddSubAccount)},
                {$"#{nameof(FxPermission.DeletePostWithPermission)}",SiteContext.HasPermission(FxPermission.DeletePostWithPermission)},
                {$".{nameof(FxPermission.EditSubAccountPost)}",SiteContext.HasPermission(FxPermission.EditSubAccountPost)},
                {$"#{nameof(FxPermission.BindWxMainAccount)}",SiteContext.HasPermission(FxPermission.BindWxMainAccount)},
                {$"#{nameof(FxPermission.SetSubAccountStatus)}",SiteContext.HasPermission(FxPermission.SetSubAccountStatus)},
            }.ToJson();
            return View();
        }

        
        public ActionResult Qualification()
        {
            
            return View();
        }
        public ActionResult Evaluates()
        {
            return View();
        }
        public ActionResult EdmitQualification()
        {
            return View();
        }

        public ActionResult DistributeSet()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var keyOrderDisplaySetting = CommonSettingService.OrderDisplaySetting;
            var IsEditSellerRemarkSetting = SystemSettingKeys.IsEditSellerRemarkSetting;//允许厂家使用卖家备注
            var IsEditAfterSaleRemarkSetting = SystemSettingKeys.IsEditAfterSaleRemarkSetting;//允许厂家使用售后备注
            List<string> keys = new List<string>() {
                 "/FenFa/System/Config/IsShowCancelUser",
                 "/FenFa/System/Config/IsSalePricePublic",
                 "/FenFa/System/Config/IsAgentSendAddress",
                "/FenFa/System/Config/IsShopNamePublic",
                "/FenFa/System/Config/StockOutType",
                "/FenFa/System/Config/StockOutZHType",
                "/ErpWeb/OutAccount/Agent/UnSetPriceDailog",
                "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog",
                "/FenFa/System/Config/AgentProductInfoPublic",
                "/FenFa/System/Config/IsEnableQuote", // 是否允许厂家使用下游分销货源创建货品信息
               keyOrderDisplaySetting,
                IsEditSellerRemarkSetting,
                IsEditAfterSaleRemarkSetting
            };
            var result = _commonSettingService.GetSets(keys, shopId);

            //没有设置则用默认
            if (result.Any(x => x.Key == "/FenFa/System/Config/IsAgentSendAddress") == false)
            {
                var defaultIsAgentSendAddress = _commonSettingService.Get("/FenFa/System/Config/IsAgentSendAddress", 0);
                if (defaultIsAgentSendAddress != null)
                    result.Add(defaultIsAgentSendAddress);
            }
            if (result.Any(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog") == false)
            {
                result.Add(new CommonSetting { Key = "/ErpWeb/OutAccount/Agent/UnSetPriceDailog", ShopId = shopId, Value = "true" });
            }
            else
            {
                var date = result.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value.toDateTime();
                if (date.AddDays(15) < DateTime.Now)
                    result.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value = "true";
                else
                    result.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value = "false";
            }
            if (result.Any(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog") == false)
            {
                result.Add(new CommonSetting { Key = "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog", ShopId = shopId, Value = "true" });
            }
            else
            {
                var date = result.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value.toDateTime();
                if (date.AddDays(15) < DateTime.Now)
                    result.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value = "true";
                else
                    result.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value = "false";
            }

            if (result.Any(x => x.Key == keyOrderDisplaySetting) == false)
            {
                //设置默认值
                result.Add(new CommonSetting { Key = keyOrderDisplaySetting, ShopId = shopId, Value = CommonSettingService.DefaultOrderDisplaySetting.ToJson() });
            }
            if (result.Any(x => x.Key == IsEditSellerRemarkSetting) == false)
            {
                //设置默认值
                result.Add(new CommonSetting { Key = IsEditSellerRemarkSetting, ShopId = shopId, Value = new OrderRemarkSetting() { Enable = false, NoteMode = 1 }.ToJson() });
            }
            if (result.Any(x => x.Key == IsEditAfterSaleRemarkSetting) == false)
            {
                //设置默认值
                result.Add(new CommonSetting { Key = IsEditAfterSaleRemarkSetting, ShopId = shopId, Value = new OrderRemarkSetting() { Enable = false, NoteMode = 1 }.ToJson() });
            }

            //默认值
            if (result.Any(x => x.Key == "/FenFa/System/Config/AgentProductInfoPublic") == false)
            {
                result.Add(new CommonSetting { Key = "/FenFa/System/Config/AgentProductInfoPublic", ShopId = shopId, Value = new AgentProductInfoPublicSetting().ToJson() });
            }
            
            // 默认开
            if (result.Any(x => x.Key == "/FenFa/System/Config/IsEnableQuote") == false)
            {
                result.Add(new CommonSetting { Key = "/FenFa/System/Config/IsEnableQuote", ShopId = shopId, Value = "true" });
            }
            ViewBag.CommonSettings = result;
            int userid = SiteContext.Current.CurrentFxUserId;
            ViewBag.CheckRule = checkrule.getRule(userid);
            int FxUserId = SiteContext.Current.CurrentFxUserId;
            //ViewBag.Shops = _fxUserShopService.GetList(new FxUserShop { FxUserId = FxUserId, ShopId = 0, Status = 0, PageIndex = 1, PageSize = 500 }).Item2;
            ViewBag.Shops = _fxUserShopService.GetShopsByFxUserId(FxUserId, false);
            ViewBag.Agents = _supplierUserService.GetAgentList(FxUserId, "", null, 1, 10000,needEncryptAccount:true).Item2;
            ViewBag.Suppliers = _supplierUserService.GetSupplierList(FxUserId, "", null, 1, 10000,needEncryptAccount:true).Item2;
            ViewBag.IsWhiteUser = SiteContext.Current.IsWhiteUser ? "true" : "false";
            return View();
        }

        public ActionResult SaveCommonSetting(string settingKey, string settingValue)
        {
            //判空处理
            if (string.IsNullOrWhiteSpace(settingKey))
            {
                return FalidResult("保存配置失败");
            }
            //检测配置键是否存在非法字符
            if (ValidCommonSettingKeyContainsIllegalCharacters(settingKey))
            {
                return FalidResult("保存配置失败");
            }
            //TODO:ShopId 待 授权做好之后,再修改为授权店铺
            var shopId = SiteContext.Current.CurrentShopId;

            if (settingKey == "/FenFa/System/Config/AgentProductInfoPublic")
            {
                //商品标题和商品图片必须有一个是true
                var agentProductInfoPublicSetting = settingValue.ToObject<AgentProductInfoPublicSetting>();
                if (agentProductInfoPublicSetting.IsProductTitlePublic == false && agentProductInfoPublicSetting.IsProductImgPublic == false)
                {
                    return FalidResult("商品标题和商品图片至少选中一个");
                }
            }

            //阿里配置
            var result = _commonSettingService.Set(settingKey, settingValue, shopId);

            //合作关系展示，移除缓存
            if (settingKey == "/FenFa/System/Config/IsShowCancelUser")
            {
                bool isUseRedis = !string.IsNullOrEmpty(CustomerConfig.ConfigRedisConnectionString);
                var cacheKey = $"FX-FxSupplierNames-{SiteContext.Current.CurrentFxUserId}";
                if (isUseRedis && RedisHelper.Exists(cacheKey))
                    RedisHelper.Del(cacheKey);
                else if (!isUseRedis && HttpRuntime.Cache[cacheKey] != null)
                    HttpRuntime.Cache.Remove(cacheKey);
            }

            ////拼多多配置
            //var result_pdd = _commonSettingService.SetPdd(settingKey, settingValue, shopId);
            ////抖店云配置
            //var result_tt = _commonSettingService.SetToTouTiao(settingKey, settingValue, shopId);

            var apiUrl = "/CommonApi/SaveCommonSetting";

            var pddCloudHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/") + apiUrl;
            var ttCloudHost = CustomerConfig.ToutiaoFenFaSystemUrl.TrimEnd("/") + apiUrl;
            var jdCloudHost = CustomerConfig.JingdongFenFaSystemUrl.TrimEnd("/") + apiUrl;
            //分单系统主站点在阿里云，所有分发到朵朵云和京东云、抖店云
            var setting = new CommonSetting { Key = settingKey, Value = settingValue, ShopId = shopId };
            //返回
            var pddResult = true;
            var ttResult = true;
            var jdResult = true;
            //拼多多配置
            if (!string.IsNullOrEmpty(CustomerConfig.PinduoduoFenFaSystemUrl))
                pddResult = SendToOtherCloudPlatform(pddCloudHost, setting);
            //抖店云配置
            if (!string.IsNullOrEmpty(CustomerConfig.ToutiaoFenFaSystemUrl))
                ttResult = SendToOtherCloudPlatform(ttCloudHost, setting);

            //京东云配置
            if (!string.IsNullOrEmpty(CustomerConfig.JingdongFenFaSystemUrl))
                jdResult = SendToOtherCloudPlatform(jdCloudHost, setting);

            if (result > 0 && pddResult && ttResult && jdResult)
                return SuccessResult();
            else
                return FalidResult("保存配置失败");
        }

        /// <summary>
        /// 检查是否存在乐观锁
        /// </summary>
        /// <param name="opId"></param>
        /// <returns></returns>
        private bool CheckLockStatus(string opId)
        {
            //增加全平台乐观锁的检查
            if (checkrule.IsLock())
            {
                throw new LogicException("检测到您上一次的变更动作还在执行中，请稍后重试...", "ERROR_LOCK");
            }
            return false;
            {
            }
        }

        /// <summary>
        /// 保存手工单设置
        /// </summary>
        /// <param name="ruleModel"></param>
        /// <returns></returns>
        [LogForOperatorFilter("订单推送设置")]
        public ActionResult SaveOrderCheckRule(OrderCheckRuleModel ruleModel)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var cloudPlatformType = CustomerConfig.CloudPlatformType;
            ruleModel.FxuserId = curFxUserId;
            var op = OptimisticLockOperationType.FxCheckRuleSet;
            var opId = $"Main:Fx{ruleModel.FxuserId}";  //主流程

            // 前置检查平台是否存在乐观锁，如果存在就返回
            if (CheckLockStatus(opId))
                return FalidResult("当前操作正在进行中，请稍后再试", "ERROR_LOCK");

            // 一分钟内只能操作一次
            var key = $"SaveOrderCheckRule:Operate:{curFxUserId}";
            if (RedisHelper.Exists(key))
                return FalidResult("由于刚进行操作，请等候一分钟再尝试操作", "ERROR_LOCK");
            RedisHelper.Set(key, "1", 60);

            var taskCode = CommUtls.GetGuidShortMd5();
            // 构建消息
            var serviceMessage = new OrderCheckRuleMessageModel
            {
                Token = Request["token"],
                DbName = Request["dbname"],
                FxUserId = curFxUserId,
                PlatformType = cloudPlatformType,
                Time = DateTime.Now,
                Rule = ruleModel,
                MainSite = true,
                OptimisticId = opId,
                TaskCode = taskCode
            };

            // 循环CloudPlatformType这个枚举，然后生成平台任务细节
            var detailsModel = new OrderCheckRuleTaskModel();
            detailsModel.OperateType = ruleModel.Type;
            var detailTaskList = Enum.GetNames(typeof(CloudPlatformType)).Select(item => new OrderCheckRuleDetailModel { PlatformType = item, Result = "进行中" }).ToList();
            detailsModel.DetailModels = detailTaskList;

            // 生成异步任务
            var taskEnt = new AsyncTask
            {
                FxUserId = curFxUserId,
                Flag = "OrderCheckRule",
                TaskCode = taskCode,
                CreateTime = DateTime.Now,
                CData = detailsModel.ToJson(),
                Status = 0,
                TotalCount = 1
            };

            // 保存异步任务
            _asyncTaskService.Add(taskEnt);
            // 延迟推送队列，确保异步任务已经入库
            Thread.Sleep(200);
            RedisHelper.RPush(CacheKeys.OrderCheckRuleToServiceQueueKey, serviceMessage);

            //try
            //{
            //    rulemodel.FxuserId = SiteContext.Current.CurrentFxUserId;
            //    #region 参数处理
            //    if (rulemodel.Messages == null)
            //    {
            //        rulemodel.Messages = "";
            //    }
            //    if (rulemodel.ShopIds == null)
            //    {
            //        rulemodel.ShopIds = "";
            //    }
            //    if (rulemodel.Suppliers == null)
            //    {
            //        rulemodel.Suppliers = "";
            //    }
            //    if (rulemodel.Agents == null)
            //    {
            //        rulemodel.Agents = "";
            //    }
            //    #endregion
            //    if (checkrule.Update(rulemodel))
            //    {
            //        //成功后还要保存到京东和拼多多平台、抖店云平台
            //        var apiUrl = "/CheckRuleApi/SaveCheckRule";
            //        var aliCloudHost = CustomerConfig.DefaultFenFaSystemUrl.TrimEnd("/") + apiUrl;
            //        var pddCloudHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/") + apiUrl;
            //        var jdCloudHost = CustomerConfig.JingdongFenFaSystemUrl.TrimEnd("/") + apiUrl;
            //        var ttCloudHost = CustomerConfig.ToutiaoFenFaSystemUrl.TrimEnd("/") + apiUrl;

            //        //分单系统主站点在阿里云，所有分发到朵朵云和京东云、抖店云平台
            //        if (!string.IsNullOrEmpty(CustomerConfig.PinduoduoFenFaSystemUrl))
            //            Common.PostFxSiteApi<OrderCheckRuleModel, bool>(pddCloudHost, SiteContext.Current.CurrentFxUserId, rulemodel, "跨云平台设置审核规则");

            //        if (!string.IsNullOrEmpty(CustomerConfig.JingdongFenFaSystemUrl))
            //            Common.PostFxSiteApi<OrderCheckRuleModel, bool>(jdCloudHost, SiteContext.Current.CurrentFxUserId, rulemodel, "跨云平台设置审核规则");

            //        if (!string.IsNullOrEmpty(CustomerConfig.ToutiaoFenFaSystemUrl))
            //            Common.PostFxSiteApi<OrderCheckRuleModel, bool>(ttCloudHost, SiteContext.Current.CurrentFxUserId, rulemodel, "跨云平台设置审核规则");

            //        return SuccessResult();
            //    }
            //    else
            //    {
            //        return FalidResult("保存配置失败");
            //    }
            //}
            //catch (Exception ex)
            //{
            //    return FalidResult("保存配置失败" + ex.Message);
            //}


            return SuccessResult("设置成功，已开始同步数据");
        }

        /// <summary>
        /// 设置订单审核推送日志
        /// </summary>
        /// <returns></returns>
        public ActionResult OrderCheckRuleLog()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var queryModel = new AsyncTaskQueryModel()
            {
                FxUserId = fxUserId,
                Flag = "OrderCheckRule",
            };

            // 获取任务列表前100条
            var list = _asyncTaskService.GetList(queryModel).OrderByDescending(x => x.CreateTime).ToList();
            return SuccessResult(list.Select(x => new
            {
                x.CreateTime,
                x.CData,
                x.FxUserId,
                x.Flag
            }).ToList());
        }

        public ActionResult LoadAddress()
        {
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var addlist = _userFxService.GetUserFxAddressInfo(fxUserId);
            try
            {
                FxUnBindTaskService unbindservice = new FxUnBindTaskService();
                var unbindUserTask = unbindservice.GetFxUser(addlist.Id);
                if (unbindUserTask != null)
                    addlist.UnBindTaskState = unbindUserTask.TaskState.ToInt(); //r.IsUnBind = true;
                else
                    addlist.UnBindTaskState = -1; // 申请解绑

                //addlist.IsUnBind = unbindservice.GetFxUser(addlist.Id) != null;
                if (!string.IsNullOrEmpty(unbindservice.TimeCheck()))
                    addlist.IsHideUnBindButton = true;
            }
            catch { }
            return SuccessResult(addlist);
        }

        public ActionResult ServeAddress(SystemDataModel _model)
        {
            if (_model.NickName.ToString2().Trim().IsNullOrEmpty())
                return FalidResult("用户名不能为空");

            _userFxService.UpdateFxUser(_model.Id, _model.NickName, _model.AvatarUrl);
            FxUserAddress _newaddres = null;
            if (_model.AddressId > 0)
            {
                _newaddres = _fxUserAddressService.Get(_model.AddressId);
                _newaddres.SenderName = _model.SenderName;
                _newaddres.SenderMobile = _model.SenderMobile ?? "";
                _newaddres.Province = _model.Province;
                _newaddres.City = _model.City;
                _newaddres.County = _model.District;
                _newaddres.Address = _model.Address;
                _newaddres.SenderTelePhone = _model.SenderTelePhone ?? "";
                _fxUserAddressService.UpdateFxUserAddress(_newaddres);
            }
            else
            {
                _newaddres = new FxUserAddress();
                _newaddres.FxUserId = _model.Id;
                _newaddres.IsDefault = true;
                _newaddres.SenderName = _model.SenderName;
                _newaddres.SenderMobile = _model.SenderMobile ?? "";
                _newaddres.Province = _model.Province;
                _newaddres.City = _model.City;
                _newaddres.County = _model.District;
                _newaddres.Address = _model.Address;
                _newaddres.SenderTelePhone = _model.SenderTelePhone ?? "";
                _fxUserAddressService.Add(_newaddres);
            }
            return SuccessResult();
        }

        public ActionResult CheckNickNmae(string key)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _userFxService.IsExistNickName(fxUserId, key);
            if (result)
                return FalidResult("用户名已经被使用");
            return SuccessResult();
        }

        public ActionResult CreateUrl()
        {
            string HostAddr = RequestHost;// CustomerConfig.ApplicationWebUrl;
            string icoPath = $"{HostAddr}/favicon.ico";//修改此处更改url图标或者图标路径，当前路径为根目录，只用修改相对路径，图标的完整路径由下方会自动生成
            var sb = new System.Text.StringBuilder();
            //var subUserId = SiteContext.Current.CurrentLoginSubUser?.Id ?? 0;
            //var fromId = LoginAuthToken?.FromId ?? 0;
            var token = Request["token"];
            sb.AppendLine("[{000214A0-0000-0000-C000-000000000046}]");
            sb.AppendLine("Prop3=19,11");
            sb.AppendLine("[InternetShortcut]");
            sb.AppendLine("IDList=");
            sb.AppendLine($"URL={HostAddr}/GeneralizeIndex/Index?token={token}"); //快捷方式的外部链接
            sb.AppendLine("IconFile=" + icoPath); //图标文件
            sb.AppendLine("IconIndex=1");
            //第一种:使用FileContentResult
            byte[] fileContents = System.Text.Encoding.Default.GetBytes(sb.ToString());
            var systemName = base.IsNewCorpReview ? "分销代发系统" : "店管家-订单分发系统";
            string fileName = System.Web.HttpUtility.UrlEncode(systemName, System.Text.Encoding.UTF8) + ".url";
            return File(fileContents, "application/octet-stream", fileName);
        }


        #region 快捷查询接
        /// <summary>
        /// 打单页面-保存快捷查询
        /// </summary>
        /// <param name="qs"></param>
        /// <returns></returns>
        public ActionResult AddQuickSearch(QuickSearch qs)
        {
            //如果长度太长就报错
            if (qs.Condition.Length > 8192)
            {
                return FalidResult("保存快捷失败: 条件过多，请重新选择");
            }
            //赋值
            qs.FxUserId = SiteContext.Current.CurrentFxUserId;
            qs.SystemShopId = SiteContext.Current.CurrentShopId;
            var saveUrl = "/QuickSearchApi/SaveQuickSearch";
            var saveUseIdUrl = "/QuickSearchApi/SaveQuickSearchUseId";
            var deleteUrl = "/QuickSearchApi/DeleteQuickSearch";
            var currentPlatformHost = "";
            var otherPlatformHost = "";
            //在精选平台或者京东平台操作保存自定义快捷,然后再拼多多进行保存同步或者反过来
            if (CustomerConfig.CloudPlatformType == "Alibaba" || CustomerConfig.CloudPlatformType == "Jingdong")
            {
                currentPlatformHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/");
                otherPlatformHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/");

            }
            else if (CustomerConfig.CloudPlatformType == "Pinduoduo")
            {
                currentPlatformHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/");
                otherPlatformHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/");
            }
            else if (CustomerConfig.CloudPlatformType == "TouTiao")
            {
                currentPlatformHost = CustomerConfig.ToutiaoFenFaSystemUrl.TrimEnd("/");
                otherPlatformHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/");
            }
            //一个平台保存,然后另外一个平台也保存,任何一个保存失败则视为失败,手动回滚
            var saveCloudUrl = currentPlatformHost + saveUrl;
            var saveUseIdCloudUrl = otherPlatformHost + saveUseIdUrl;
            var deleteCloudUrl = currentPlatformHost + deleteUrl;
            int step = 0;
            try
            {
                QuickSearchService qsservice = new QuickSearchService();
                QuickSearchMethod md = QuickSearchMethod.WaitOrder;
                switch (qs.Method.ToLower())
                {
                    case "waitorder": md = QuickSearchMethod.WaitOrder; break;
                    case "allorder": md = QuickSearchMethod.AllOrder; break;
                    case "baseproduct": md = QuickSearchMethod.BaseProduct; break;
                    default: break;
                }
                QuickSearch saveResult = qsservice.AddCustomer(qs.FxUserId, qs.SystemShopId, qs.Title, qs.Condition, md);

                //var saveResult = Common.PostFxSiteApi<QuickSearch, QuickSearch>(saveCloudUrl, SiteContext.Current.CurrentFxUserId, qs, saveCloudUrl + "云平台保存快捷查询");
                if (saveResult != null)
                {
                    //if (CustomerConfig.IsDebug) return SuccessResult();
                    return SuccessResult();
                    step = 1;
                    qs = saveResult;
                    var saveUseIdResult = Common.PostFxSiteApi<QuickSearch, long>(saveUseIdCloudUrl, SiteContext.Current.CurrentFxUserId, qs, saveUseIdCloudUrl + "跨云平台保存快捷查询(已知ID)");
                    if (saveUseIdResult != -1)
                    {
                        step = 2;
                        return SuccessResult();
                    }
                    else
                    {
                        //删除精选平台保存的                        
                        QuickSearchDelete qsd = new QuickSearchDelete();
                        qsd.id = qs.Id;
                        qsd.fxuserid = qs.FxUserId;
                        qsd.systemshopid = qs.SystemShopId;
                        var deleteResult = Common.PostFxSiteApi<QuickSearchDelete, int>(deleteCloudUrl, SiteContext.Current.CurrentFxUserId, qsd, deleteCloudUrl + "跨云平台删除快捷查询");
                    }
                    return SuccessResult();
                }
                else
                {
                    return FalidResult("保存快捷失败:");
                }
            }
            catch (Exception ex)
            {
                if (step == 1)
                {
                    QuickSearchDelete qsd = new QuickSearchDelete();
                    qsd.id = qs.Id;
                    qsd.fxuserid = qs.FxUserId;
                    qsd.systemshopid = qs.SystemShopId;
                    try
                    {
                        Common.PostFxSiteApi<QuickSearchDelete, int>(deleteCloudUrl, SiteContext.Current.CurrentFxUserId, qsd, deleteCloudUrl + "跨云平台删除快捷查询");
                    }
                    catch (Exception eexx)
                    {
                        Log.WriteError("跨云平台删除快捷查询:" + eexx.Message);
                    }
                }
                Log.WriteError("保存快捷失败:" + ex.Message + ex.StackTrace + "步骤:" + step + " 地址:" + saveCloudUrl + "，地址2:" + saveUseIdCloudUrl);
                return FalidResult("保存快捷失败:" + ex.Message);
            }

        }

        /// <summary>
        /// 打单页面-保存快捷排序和可见性-打单页面,全量保存
        /// </summary>
        /// <param name="qss"></param>
        /// <returns></returns>
        public ActionResult UpdateQuickSearch(QuickSearchSort qss)
        {
            var updateUrl = "/QuickSearchApi/UpdateQuickSearch";
            var currentPlatformHost = "";
            var otherPlatformHost = "";
            qss.systemshopid = SiteContext.Current.CurrentShopId;
            //在精选平台或者京东平台操作保存自定义快捷,然后再拼多多进行保存同步或者反过来
            if (CustomerConfig.CloudPlatformType == "Alibaba" || CustomerConfig.CloudPlatformType == "Jingdong")
            {
                currentPlatformHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/");
                otherPlatformHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/");

            }
            else if (CustomerConfig.CloudPlatformType == "Pinduoduo")
            {
                currentPlatformHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/");
                otherPlatformHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/");
            }
            else if (CustomerConfig.CloudPlatformType == "TouTiao")
            {
                currentPlatformHost = CustomerConfig.ToutiaoFenFaSystemUrl.TrimEnd("/");
                otherPlatformHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/");
            }
            var currenturl = currentPlatformHost + updateUrl;
            var otherurl = otherPlatformHost + updateUrl;
            try
            {
                QuickSearchService qsservice = new QuickSearchService();
                int result1 = qsservice.UpdateViewAndSort(qss.ids, qss.systemshopid, qss.method);
                //var result1 = Common.PostFxSiteApi<QuickSearchSort, long>(currenturl, SiteContext.Current.CurrentFxUserId, qss, currenturl + "跨云平台更新快捷查询排序可见性");
                //if (CustomerConfig.IsDebug)
                if (true)
                {
                    if (result1 != -1)
                    {
                        return SuccessResult();
                    }
                    else
                    {
                        return FalidResult("保存快捷查询排序和可见性失败");
                    }
                }
                var result2 = Common.PostFxSiteApi<QuickSearchSort, long>(otherurl, SiteContext.Current.CurrentFxUserId, qss, currenturl + "跨云平台更新快捷查询排序可见性");
                if (result1 != -1 && result2 != -1)
                {
                    return SuccessResult();
                }
                else
                {
                    return FalidResult("保存快捷查询排序和可见性失败");
                }
            }
            catch (Exception ex)
            {
                return FalidResult("保存快捷查询排序和可见性失败:" + ex.Message);
            }
        }

        /// <summary>
        /// 打单页面-删除快捷操作
        /// </summary>
        /// <param name="qsd"></param>
        /// <returns></returns>
        public ActionResult DeleteQuickSearch(QuickSearchDelete qsd)
        {
            var deleteUrl = "/QuickSearchApi/DeleteQuickSearch";
            var currentPlatformHost = "";
            var otherPlatformHost = "";
            qsd.fxuserid = SiteContext.Current.CurrentFxUserId;
            qsd.systemshopid = SiteContext.Current.CurrentShopId;
            //在精选平台或者京东平台操作保存自定义快捷,然后再拼多多进行保存同步或者反过来
            if (CustomerConfig.CloudPlatformType == "Alibaba" || CustomerConfig.CloudPlatformType == "Jingdong")
            {
                currentPlatformHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/");
                otherPlatformHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/");

            }
            else if (CustomerConfig.CloudPlatformType == "Pinduoduo")
            {
                currentPlatformHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/");
                otherPlatformHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/");
            }
            else if (CustomerConfig.CloudPlatformType == "TouTiao")
            {
                currentPlatformHost = CustomerConfig.ToutiaoFenFaSystemUrl.TrimEnd("/");
                otherPlatformHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/");
            }
            var currenturl = currentPlatformHost + deleteUrl;
            var otherurl = otherPlatformHost + deleteUrl;
            try
            {
                QuickSearchService qsservice = new QuickSearchService();
                int result1 = qsservice.DeleteById(qsd.id, qsd.fxuserid, qsd.systemshopid);
                //var result1 = Common.PostFxSiteApi<QuickSearchDelete, long>(currenturl, SiteContext.Current.CurrentFxUserId, qsd, currenturl + "跨云平台删除快捷查询");
                //if (CustomerConfig.IsDebug)
                if (true)
                {
                    if (result1 != -1)
                    {
                        return SuccessResult();
                    }
                    else
                    {
                        return FalidResult("删除快捷查询失败");
                    }
                }
                var result2 = Common.PostFxSiteApi<QuickSearchDelete, long>(otherurl, SiteContext.Current.CurrentFxUserId, qsd, currenturl + "跨云平台删除快捷查询");
                if (result1 != -1 && result2 != -1)
                {
                    return SuccessResult();
                }
                else
                {
                    return FalidResult("删除快捷查询失败");
                }
            }
            catch (Exception ex)
            {
                Log.WriteError("删除快捷查询失败:" + ex.Message + ex.StackTrace);
                return FalidResult("删除快捷查询失败:" + ex.Message);
            }
        }
        #endregion

        /// <summary>
        /// 申请解绑
        /// </summary>
        /// <param name="mobile">手机号</param>
        /// <param name="smsCode">短信验证码</param>
        /// <returns></returns>
        public ActionResult SetUnBindUser(string mobile, string smsCode)
        {
            FxUnBindTaskService unbindservice = new FxUnBindTaskService();
            try
            {
                var fxuserId = SiteContext.Current.CurrentFxUserId;
                var systemshopId = SiteContext.Current.CurrentShopId; //new ShopService().GetFxSystemShopByFxId(fxuserId);
                //if (systemshop == null)
                //    return FalidResult("用户系统店铺未找到");
                //验证码检查
                if (string.IsNullOrEmpty(smsCode))
                    return FalidResult("请填写手机验证码");
                if (string.IsNullOrEmpty(mobile))
                    return FalidResult("请填写手机号码");

                var fxuser = _userFxService.Get(fxuserId);
                if (fxuser == null)
                    return FalidResult("用户未找到");

                if (fxuser.Mobile != mobile)
                    return FalidResult("手机号码和当前用户不匹配");

                if (_userFxService.CheckPhoneCodeIsValid(mobile, smsCode) == 1)
                    return FalidResult("短信验证码不正确，请重新获取");
                if (_userFxService.CheckPhoneCodeIsValid(mobile, smsCode) == 2)
                    return FalidResult("短信验证码已过期，请重新获取");
                if (_userFxService.CheckPhoneCodeIsValid(mobile, smsCode) == 3)
                    return FalidResult("短信验证码已被使用，请重新获取");


                var result = unbindservice.SetFxUser(fxuserId, systemshopId, fxuser.Mobile);
                _userFxService.UpdateVeriCodeIsEmploy(new UserRegisterModel { Mobile = mobile, MobileMeessageCode = smsCode });//更新短信验证码的使用状态

                if (result)
                    return SuccessResult("申请解绑成功");
                return FalidResult("申请解绑失败");
            }
            catch (LogicException lex)
            {
                return FalidResult(lex.Message);
            }
            catch (Exception ex)
            {
                Log.WriteError($"SetUnBindShop:{ex.Message}");
                return FalidResult("系统异常");
            }
        }

        /// <summary>
        /// 取消解绑
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public ActionResult CancelUnBindUser()
        {
            FxUnBindTaskService unbindservice = new FxUnBindTaskService();
            try
            {
                var fxuserId = SiteContext.Current.CurrentFxUserId;
                var result = unbindservice.CancelFxUser(fxuserId);
                if (result)
                    return SuccessResult("取消解绑成功");
                return FalidResult("取消解绑失败");
            }
            catch (LogicException lex)
            {
                return FalidResult(lex.Message);
            }
            catch (Exception ex)
            {
                Log.WriteError($"SetUnBindShop:{ex.Message}");
                return FalidResult("系统异常");
            }
        }


        /// <summary>
        /// 微信绑定列表
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadBindWxUserList()
        {
            FxUnBindTaskService unbindservice = new FxUnBindTaskService();
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = unbindservice.GetWeChatUserInfo(fxUserId);
            return SuccessResult(new { Total = result.Count, List = result });
        }

        /// <summary>
        /// 生成绑定二维码（兼容子账号）
        /// </summary>
        /// <returns></returns>
        public ActionResult GetSysBindQRCode(ScanQrCodeReqModel scanQrCodeReq)
        {
            FxUnBindTaskService unbindservice = new FxUnBindTaskService();
            UserFx userFx = SiteContext.Current.CurrentFxUser;
            WeChatQRCode weChatQR = new WeChatQRCode();
            weChatQR.QRCodeId = Guid.NewGuid().ToString().ToShortMd5();//以state传到微信侧-->返回到我们系统接收
            weChatQR.FxUserId = userFx.Id;
            weChatQR.Mobile = userFx.Mobile;
            weChatQR.MaxNum = CustomerConfig.WeChatQRMaxNum;
            weChatQR.CreateTime = DateTime.Now;
            weChatQR.ExpirationTime = DateTime.Now.AddMinutes(1);
            #region 子账号
            // 如果当前是子账号环境，weChatQR.ExtField1不为空，为空可能表示是旧数据或者不是子账号环境
            if (scanQrCodeReq != null)
            {
                weChatQR.ExtField1 = scanQrCodeReq.ToJson();
                #region 判断是否已经绑定过了微信
                var fxWechatService = new FxWeChatUserService();
                // 主账号绑定微信
                if (scanQrCodeReq.IsMainUser)
                {
                    if (scanQrCodeReq.Id != userFx.Id)
                    {
                        throw new LogicException($"绑定微信的主账号与当前账号不一样！");
                    }
                    var bindedCount = fxWechatService.GetBindedCount(scanQrCodeReq.Id);
                    if(bindedCount >= CustomerConfig.WeChatQRMaxNum)
                    {
                        throw new LogicException($"一个账号最多只能绑定{CustomerConfig.WeChatQRMaxNum}个微信");
                    }
                }
                else
                {
                    // 针对已有的子账号绑定微信
                    if(scanQrCodeReq.Id > 0)
                    {
                        // 查询子账号绑定的微信数
                        var bindedCount = fxWechatService.GetBindedCount(userFx.Id, scanQrCodeReq.Id);
                        if (bindedCount >= CustomerConfig.WeChatQRMaxNum)
                        {
                            throw new LogicException($"一个子账号最多只能绑定{CustomerConfig.WeChatQRMaxNum}个微信");
                        }
                    }
                }
                #endregion
            }
            else
            {
                // 非子账号环境
                var subFxUserId = SiteContext.Current?.SubFxUserId ?? 0;

                var bindedCount = new FxWeChatUserService().GetBindedCount(userFx.Id, subFxUserId);
                //TODO：正式上线后CustomerConfig.WeChatQRMaxNum改为1
                if (subFxUserId <= 0 && bindedCount >= CustomerConfig.WeChatQRMaxNum)
                    throw new LogicException($"一个账号最多只能绑定{CustomerConfig.WeChatQRMaxNum}个微信");
                else if (subFxUserId > 0 && bindedCount >= 1)
                    throw new LogicException($"一个子账号最多只能绑定1个微信");
            }
            #endregion

            int index = unbindservice.GenerateQRCode(weChatQR);
            if (index < 1)
                return FalidResult("二维码生成失败，请重试");

            var token = Request["token"].ToString();
            var dbname = HttpUtility.UrlEncode(Request["dbname"].ToString());

            var AppId = CustomerConfig.WeChatAppID;
            var url = HttpUtility.UrlEncode(CustomerConfig.WeChatRedirectUrl + $"/FFAccount/SysScanQRCodeBind?fxuserid={userFx.Id}&token={token}&dbname={dbname}&",
                System.Text.Encoding.UTF8);
            return Json(new { QRCodeId = weChatQR.QRCodeId, ExpirationTime = weChatQR.ExpirationTime, AppId = AppId, RedirectUri = url });

        }

        /// <summary>
        /// 解绑（兼容子账号）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult UnBindWeChatUser(int id)
        {
            try
            {
                if (id <= 0) { return FalidResult("参数错误"); }

                var fxuserId = SiteContext.Current.CurrentFxUserId;

                var wxuser = _fxwechatuserService.GetWeChatUserById(id, fxuserId);
                if (wxuser == null) { return FalidResult("未找到绑定记录，请在用户中心-添加更多微信授权完成绑定"); }

                wxuser.UnBind = 1;
                wxuser.SubFxUserId = SiteContext.Current?.SubFxUserId ?? 0;//子账号
                int index = _fxwechatuserService.UpdateWeChatUnBind(wxuser);
                return index > 0 ? SuccessResult() : FalidResult("微信解绑失败");
            }
            catch (Exception ex)
            {
                Log.WriteError($"UnBindWeChatUser:{ex.Message}");
                return FalidResult("系统异常");
            }
        }
        /// <summary>
        /// 修改备注
        /// </summary>
        /// <param name="id"></param>
        /// <param name="remart"></param>
        /// <returns></returns>
        public ActionResult UpdateWeChatUserRemart(int id, string remart)
        {
            try
            {
                if (id <= 0) { return FalidResult("参数错误"); }

                var fxuserId = SiteContext.Current.CurrentFxUserId;
                var wxuser = _fxwechatuserService.GetWeChatUserById(id, fxuserId);
                if (wxuser == null) { return FalidResult("未找到绑定记录，请在用户中心-添加更多微信授权完成绑定"); }
                wxuser.Remark = remart;
                int index = _fxwechatuserService.UpdateWeChatRemark(wxuser);
                return index > 0 ? SuccessResult() : FalidResult("修改备注失败");
            }
            catch (Exception ex)
            {
                Log.WriteError($"UnBindWeChatUser:{ex.Message}");
                return FalidResult("系统异常");
            }
        }
        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult UpdatePassWord(UserRegisterModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            try
            {
                if (model == null || model.Mobile.IsNullOrEmpty())
                {
                    throw new LogicException("更新密码失败，所传参数异常！");
                }
                if (SiteContext.IsSubAccount())
                {
                    var subUser = new SubUserFxService().GetSubUserFx(SiteContext.CurrentNoThrow.SubFxUserId);
                    if (subUser == null) throw new LogicException("更新密码失败，子账号不存在或已被删除，请联系主账号确认");
                    if (subUser.Mobile != model.Mobile) throw new LogicException("更新密码失败，所传手机号与当前子账号绑定的手机号不符");
                }
                else
                {
                    var fxUser = _userFxService.Get(fxUserId) ?? throw new LogicException("更新密码失败，账号不存在或已被删除");
                    if (fxUser.Mobile != model.Mobile) throw new LogicException("更新密码失败，所传手机号与当前账号绑定的手机号不符");
                }
                return new CommonPortalController().ResetPassword(model, Request, Response);
            }
            catch (Exception ex)
            {
                if (ex is LogicException)
                {
                    return FalidResult(ex.Message);
                }
                Log.WriteError("密码修改失败: " + ex.ToString());
                return FalidResult("修改密码失败，请联系我们。");
            }
        }
        
        /// <summary>
        /// 验证短信验证码
        /// </summary>
        /// <param name="mobile"></param>
        /// <param name="smsCode"></param>
        /// <returns></returns>
        public ActionResult CheckMsgCode(string mobile, string smsCode)
        {
            if (string.IsNullOrEmpty(mobile))
                return FalidResult("请填写手机号码");
            if (string.IsNullOrEmpty(smsCode))
                return FalidResult("请填写手机验证码");
            var user = _userFxService.GetByMobile(mobile);
            if (user == null || user.Id != SiteContext.Current.CurrentFxUserId) 
                return FalidResult("请使用与当前账号绑定的手机号码");
            
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            // 获取系统配置
            var antiCrawlerConfig = AntiCrawlerConfigService.GetAntiCrawlerConfig();
            // 获取锁定时间
            var expireTime = antiCrawlerConfig.ValidCodeLockSeconds;
            // 获取失败次数
            var maxRetryCount = antiCrawlerConfig.ValidCodeMaxRetryTimes;

            // 检查是否被锁定
            var lockKey = string.Format(CacheKeys.RedisKeyValidCodeLock, fxUserId, "MessageCode");
            var isLocked = RedisHelper.Exists(lockKey);
            if (isLocked)
            {
                var retryTimes = RedisHelper.Get<int>(lockKey);
                if (retryTimes >= maxRetryCount)
                {
                    // 获取锁定时间
                    var lockSeconds = RedisHelper.Ttl(lockKey);
                    return FalidResult($"验证失败次数过多，请{lockSeconds}秒后再试");
                }
            }
            
            var result = _userFxService.CheckPhoneCodeIsValid(mobile, smsCode);

            var msg = string.Empty;

            if (result == 1) msg = "短信验证码不正确";
            else if (result == 2) msg = "短信验证码已过期";
            else if (result == 3) msg = "短信验证码已被使用";
            
            if (msg.IsNotNullOrEmpty())
            {
                // 验证失败，记录失败次数
                var redisKey = string.Format(CacheKeys.RedisKeyValidCodeLock, fxUserId, "MessageCode");
                RedisHelper.IncrBy(redisKey);
                RedisHelper.Expire(redisKey, expireTime);

                return FalidResult(msg);
            }
            
            // 判断是否有这个RedisKeyValidExist的Key，有就删除
            var redisKeyValidExist = string.Format(CacheKeys.RedisKeyValidExist, fxUserId);
            if (RedisHelper.Exists(redisKeyValidExist))
            {
                RedisHelper.Del(redisKeyValidExist);
            }
            
            return SuccessResult();
        }
        /// <summary>
        /// 保存用户名片
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult SaveBusinessCard(BusinessCardModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            if (model == null) return FalidResult("无效的输入");

            try
            {
                // 公司信息
                UpdateBusinessCardInfo(model.CompanyInfoList, BusinessCardExtFlag.CompanyInfo);
                // 业务联系人
                UpdateBusinessCardInfo(model.BusinesscontactList, BusinessCardExtFlag.BusinessContact);
                // 售后地址
                UpdateBusinessCardInfo(model.AftersaleaddressList, BusinessCardExtFlag.AfterSaleAddress);
                // 收款信息
                UpdateBusinessCardInfo(model.PaymentinfoList, BusinessCardExtFlag.PaymentInfo);
                // 平台类项
                UpdateBusinessCardInfo(model.PlatformtypeList, BusinessCardExtFlag.PlatformType);
                // 发货地址
                UpdateBusinessCardInfo(model.SendaddressList, BusinessCardExtFlag.SendAddress);
                // 自营店铺
                UpdateBusinessCardInfo(model.ShopList ?? new List<BusinessCardShop> { new BusinessCardShop() }, BusinessCardExtFlag.Shop);
                // 售后服务
                if (model.Aftesales.IsNotNullOrEmpty()) UpdateBusinessCardInfo(new List<BusinessCardAftesales> { model.Aftesales }, BusinessCardExtFlag.AfteSales);
            }
            catch (Exception)
            {
                return FalidResult("保存失败");
            }

            return SuccessResult("保存成功");

            void UpdateBusinessCardInfo<T>(List<T> list, string flag)
            {
                if (list?.Any() == true)
                {
                    _businessCardService.UpdateOrAddBusinessCard(list, flag, fxUserId);
                }
            }
        }

        /// <summary>
        /// 获取用户名片
        /// </summary>
        /// <returns></returns>
        public ActionResult GetBusinessCard()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var businessCard = _businessCardService.GetViewModel(fxUserId);

            return SuccessResult(businessCard.ToJson());
        }

        /// <summary>
        /// 同步我的店铺
        /// </summary>
        /// <returns></returns>
        public ActionResult SyncMyShop()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            var result = _businessCardService.SyncMyShop(fxUserId);

            return SuccessResult(result.ToJson());
        }

        /// <summary>
        /// 合作评价（合作留言）
        /// </summary>
        /// <returns></returns>
        public ActionResult GetCooperateEvaluation(int? forFxUserId, bool isSupplier = false)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            List<CooperateEvaluateModel> result;
            if (forFxUserId != null && forFxUserId > 0)
            {
               // 是否是当前用户关联的厂家
                var suppliers = _supplierUserService.GetAgentOrSupplierIds(SiteContext.GetCurrentFxUserId(), !isSupplier);
                if (!suppliers.Contains(forFxUserId.Value))
                {
                    return FalidResult("无权限查看");
                }
                result = _cooperateEvaluateService.GetCooperateEvaluation(forFxUserId.Value);
            }
            else
            {
                result = _cooperateEvaluateService.GetCooperateEvaluation(curFxUserId);
            }
            return SuccessResult(result);
        }
        
        /// <summary>
        /// 获取前端监控元素埋点配置，正常情况不会请求到这里
        /// </summary>
        /// <returns></returns>
        public ActionResult MonitoringConfig()
        {
            try
            {
                Log.WriteWarning("MonitoringConfig 获取监控配置，视图获取失效！");
                var monitoringConfigModel = new CommonSettingService().GetMonitoringConfig();
                var result = monitoringConfigModel ?? new MonitoringConfigModel();
                
                return SuccessResult(result);
            }
            catch (Exception ex)
            {
                Log.WriteError($"CommonApi.MonitoringConfig 获取监控配置失败：{ex}");
                return FalidResult(ex.Message);
            }
        }
    }

}
