
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.LogisticService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.WaybillService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DianGuanJiaApp.Services.Services.OrderModule;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Entity.OrderModule;
using System.Collections.Concurrent;
using DianGuanJiaApp.ErpWeb.Models;
using DianGuanJiaApp.Data.Repository;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    public class WaybillCodeListController : BaseController
    {

        private WaybillCodeService _service = new WaybillCodeService();
        private PrintHistoryService _printHistoryService;
        private SendHistoryService _sendHistoryService = new SendHistoryService();
        private PrintTemplateService _printTemplateService = new PrintTemplateService();
        private ExpressCompanyService _expressCompanyService = new ExpressCompanyService();
        private UserSiteInfoService _userSiteInfoService = new UserSiteInfoService();
        private TemplateRelationAuthInfoService _templateRelationAuthInfoService = new TemplateRelationAuthInfoService();
        private CaiNiaoAuthInfoService _caiNiaoAuthInfoService = new CaiNiaoAuthInfoService();
        private CommService _commService = new CommService();
        private CommonSettingService _commSettingService = new CommonSettingService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private ExportTaskService _exportTaskService = new ExportTaskService();
        private SupplierUserService _supplierUserService = new SupplierUserService();
        private BranchShareRelationService _branchShareRelationService = new BranchShareRelationService();

        private ShareWaybillCodeRecordService _shareWaybillCodeRecordService = new ShareWaybillCodeRecordService();
        private LogicOrderService _logicOrderService = new LogicOrderService();

        [FxAuthorize(AuthorizeType.WaybillCode)]
        public ActionResult Index()
        {
            var type = Request["type"].ToString2();
            ViewBag.FromOrderPrint = Request["FromOrderPrint"].ToInt();

            // 底单导出默认导出设置 
            var currShop = SiteContext.Current.CurrentLoginShop;
            var currShopId = SiteContext.Current.CurrentShopId;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //var setting = "[{\"Text\":\"订单编号\",\"Value\":\"OrderId\"},{\"Text\":\"收件人姓名\",\"Value\":\"Reciver\"},{\"Text\":\"收件人电话\",\"Value\":\"ReciverPhone\"},{\"Text\":\"详细地址\",\"Value\":\"ToAddress\"},{\"Text\":\"快递公司\",\"Value\":\"ExpressName\"},{\"Text\":\"快递单号\",\"Value\":\"ExpressWayBillCode\"},{\"Text\":\"商品数量\",\"Value\":\"ProductCount\"},{\"Text\":\"发货内容\",\"Value\":\"SendContent\"},{\"Text\":\"发件人\",\"Value\":\"Sender\"},{\"Text\":\"打单时间\",\"Value\":\"CreateDate\"}]";
            var setting = "[{\"Text\":\"系统单号\",\"Value\":\"OrderId\"},{\"Text\":\"收件人姓名\",\"Value\":\"Reciver\"},{\"Text\":\"收件人电话\",\"Value\":\"ReciverPhone\"},{\"Text\":\"详细地址\",\"Value\":\"ToAddress\"},{\"Text\":\"快递公司\",\"Value\":\"ExpressName\"},{\"Text\":\"快递单号\",\"Value\":\"ExpressWayBillCode\"},{\"Text\":\"快递模板\",\"Value\":\"TemplateName\"},{\"Text\":\"重量\",\"Value\":\"TotalWeight\"},{\"Text\":\"数量\",\"Value\":\"ProductCount\"},{\"Text\":\"时间\",\"Value\":\"CreateDate\"},{\"Text\":\"发货类型\",\"Value\":\"SendType\"}]";
            var isCj = type == "cj";
            var exportSetKey = isCj ? "ErpWeb/WaybillCodeCJ/ExportSetting" : "ErpWeb/WaybillCode/ExportSetting";
            var exportUpdateTimeKey = isCj ? "/Export/WaybillCodeCJ/ExportUpdateTime" : "/Export/WaybillCode/ExportUpdateTime";
            var keys = new List<string> { exportSetKey, exportSetKey };
            var commSets = _commonSettingService.GetSets(keys, currShopId);
            var checkedFieldsSet = commSets?.FirstOrDefault(m => m.Key == exportSetKey)?.Value;
            if (checkedFieldsSet.IsNullOrEmpty())
            {
                checkedFieldsSet = _commSettingService.GetFxDefaultSetting(currShop.PlatformType, "WaybillCode_ExportSetting");
                checkedFieldsSet = checkedFieldsSet.IsNullOrEmpty() ? setting : checkedFieldsSet;
                _commSettingService.Set(exportSetKey, checkedFieldsSet, currShopId);
            }
            ViewBag.ExportCheckSet = checkedFieldsSet.IsNullOrEmpty() ? "null" : checkedFieldsSet;

            //最后导出时间
            var exportUpdateTimeSet = commSets.FirstOrDefault(m => m.Key == exportUpdateTimeKey);
            var defaultExportUpdateTime = exportUpdateTimeSet?.Value.ToDateTime() ?? null;
            var defaultExportUpdateTimeSetVal = defaultExportUpdateTime == null ? "" : defaultExportUpdateTime.Value.ToString("yyyy-MM-ddTHH:mm:ss");
            // 默认导出间隔时长（300s）
            var defaultExportExpireSecondsSet = _commonSettingService.Get("/Export/WaybillCode/ExpireSeconds", 0);
            var defaultExportExpireSeconds = defaultExportExpireSecondsSet?.Value.ToInt() ?? 0;
            defaultExportExpireSeconds = defaultExportExpireSeconds <= 0 ? 300 : defaultExportExpireSeconds;
            ViewBag.ExportUpdateTimeSet = defaultExportUpdateTimeSetVal;
            ViewBag.ExportExpireSeconds = defaultExportExpireSeconds;

            //获取url参数带过来的订单id
            var platformOrderId = Request.Params["platformOrderId"];
            ViewBag.PlatformOrderId = platformOrderId;

            //前台获取导出任务
            //var exportTask = _exportTaskService.GetErpWebExportTask(currShopId, CustomerConfig.CloudPlatformType, ExportType.ErpWaybillCode.ToInt());
            //前台获取导出任务 分库 - 精选平台只显示分库创建的导出任务
            //var exportType = isCj ? ExportType.ErpWaybillCodeCJ.ToInt() : ExportType.ErpWaybillCode.ToInt();
            //var exportTask = _exportTaskService.GetErpWebExportTaskSharding(currShopId, CustomerConfig.CloudPlatformType, exportType, Request["dbname"] ?? "");
            // 显示未完成任务或已完成一天前的导出任务
            //exportTask = exportTask != null && ((exportTask.Status >= 0 && exportTask.Status < 4) || (exportTask.Status >= 4 && exportTask.UploadToServerTime != null && DateTime.Now < exportTask.UploadToServerTime.Value.AddDays(1))) ? exportTask : null;
            //var task = GetExportTaskToWeb(exportTask);
            //ViewBag.WaybillCodeExportTask = task?.ToJson() ?? "null";

            var _supplierUserService = new SupplierUserService();
            if (isCj)
            {
                //厂家数据源
                var suppliers = _supplierUserService.GetSupplierList(fxUserId, onlyGetCurDb: true,needEncryptAccount:true);
                ViewBag.Suppliers = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter }).Distinct().ToJson();
                //ViewBag.SupplierUsers = suppliers?.Item2?.Where(x => x.Status == AgentBingSupplierStatus.Binded).Select(x => new { x.UserName, FxUserId = x.SupplierFxUserId }).GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.UserName ?? "");

                //商家数据源
                var agents = _supplierUserService.GetAgentList(fxUserId, onlyGetCurDb: true, needEncryptAccount: true);
                ViewBag.Agents = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct().ToJson();
            }
            else
            {
                //商家数据源
                var agents = _supplierUserService.GetAgentList(fxUserId, onlyGetCurDb: true,needEncryptAccount:true);
                ViewBag.Agents = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct().ToJson();
            }
            ViewBag.Shops = GetFxUserShops().ToJson();

            // 页面按钮展示权限
            if (isCj)
            {
                ViewBag.ShowPermDict = new Dictionary<string, bool>
                {
                    //{$".{nameof(FxPermission.RecycleWaybillCode)}",SiteContext.HasPermission(FxPermission.RecycleWaybillCode)},
                    //{$".{nameof(FxPermission.WaybillCodeRePrintCJ)}",SiteContext.HasPermission(FxPermission.WaybillCodeRePrintCJ)},
                }.ToJson();
            }
            else
            {
                ViewBag.ShowPermDict = new Dictionary<string, bool>
                {
                    {$".{nameof(FxPermission.RecycleWaybillCode)}",SiteContext.HasPermission(FxPermission.RecycleWaybillCode)},
                    {$".{nameof(FxPermission.WaybillCodeRePrint)}",SiteContext.HasPermission(FxPermission.WaybillCodeRePrint)},
                }.ToJson();
            }
            

            return View();
        }



        /// <summary>
        /// 加载发往的省份
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadProvinces()
        {
            var shopIds = new List<int> { SiteContext.Current.CurrentShopId };
            var provinces = _service.GetToProvinces(shopIds);
            var selectItemList = new List<SelectListItem>() { new SelectListItem() { Text = "==所有省份==", Value = "0" } };
            if (provinces != null && provinces.Count > 0)
            {
                provinces.ForEach(item =>
                {
                    if (string.IsNullOrWhiteSpace(item) == false)
                        selectItemList.Add(new SelectListItem() { Text = item, Value = item });
                });
            }
            return Json(selectItemList);
        }

        /// <summary>
        /// 加载打印过的模板名称
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("底单查询模板条件")]
        public ActionResult LoadTemplateNames()
        {
            int shopId = SiteContext.Current.CurrentShopId;
            var dataList = _printTemplateService.GetTemplateNamesByShopId(shopId);
            var selectItemList = new List<SelectListItemViewModel>() { new SelectListItemViewModel() { Text = "=======所有模板=======", Value = "0" } };
            if (dataList != null && dataList.Count > 0)
            {
                dataList.ForEach(item =>
                {
                    selectItemList.Add(new SelectListItemViewModel() { Text = item.TemplateName, Value = item.Id.ToString(), TemplateType = item.TemplateType });
                });
            }
            return Json(selectItemList);
        }

        ///// <summary>
        ///// 加载运单数/订单数
        ///// </summary>
        ///// <returns></returns>
        //[LogForOperatorFilter("底单查询数据统计")]
        //public ActionResult LoadStatisticsCount(WaybillCodeRequestModel requestModel)
        //{
        //    var fxUserId = SiteContext.Current.CurrentFxUserId;
        //    requestModel.FxUserId = fxUserId;
        //    var isPdd = Request["Pt"]?.ToLower() == PlatformType.Pinduoduo.ToString().ToLower();
        //    var countResult = _service.StatisticsCount(requestModel, isPdd);
        //    var result = new { WaybillCodeCount = countResult.Item1, LogicOrderCount = countResult.Item2, OrderCount = countResult.Item3 };
        //    return Json(result);
        //}

        /// <summary>
        /// 加载列表数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        [LogForOperatorFilter("底单查询列表")]
        [PageDepthControlFilter]
        public ActionResult LoadList(WaybillCodeRequestModel requestModel)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var check = _service.CheckRequestModel(requestModel);
            if (check.Item1 == false) return FalidResult(check.Item2);
            requestModel.FxUserId = fxUserId;
            var isPdd = Request["Pt"]?.ToLower() == PlatformType.Pinduoduo.ToString().ToLower();
            var pageModel = _service.LoadListV2(requestModel, new WaybillCodeLoadListModel { needPagging = true, isPdd = isPdd, QueryType = WaybillQueryType.DataAndCount });
            //TikTok发货状态为已发货的订单才同步至底单，仅查询已发货状态
            //if (CustomerConfig.IsCrossBorderSite)
            //    requestModel.Status = "3";


            if (CustomerConfig.IsDebug)
            {
                Log.WriteError($"乱码数据排查1:{pageModel.Rows.ToJson()}", "WaybillCodeLoadList.txt");
            }

            //var totalOrderCount = 0;
            //var totalWaybillCount = 0;

            //var dataList = pageModel.Rows;
            //if (dataList != null && dataList.Count > 0)
            //{
            //    totalOrderCount = dataList.SelectMany(f =>
            //    {
            //        var orderJoin = f.OrderIdJoin?.TrimEnd(',').TrimStart(',');
            //        if (orderJoin?.IndexOf(',') > -1)
            //        {
            //            return orderJoin.Split(',');
            //        }
            //        else
            //        {
            //            return new string[] { f.OrderIdJoin };
            //        }
            //    }
            //    ).Distinct().Count();

            //    totalWaybillCount = dataList.Select(f => f.ExpressWayBillCode).Distinct().Count();
            //}

            ////底单中保存的订单编号为逻辑订单号，需要转换成原始订单编号，才能解密收件人信息
            //BuildExccelService.EncryptReceiverInfo(result.Rows,isFx: true);

            // 2025-1-22 这些平台不需要再解密-脱敏
            var notPlatformType = new string[] { PlatformType.TouTiao.ToString(), PlatformType.TouTiaoSaleShop.ToString() };
            var newWaybillCodeLst = pageModel.Rows.Where(x => !notPlatformType.Any(p => p == x.PlatformType)).ToList();

            FxPlatformEncryptService.EncryptWaybillCodes(newWaybillCodeLst, encryptSender: true);

            if (CustomerConfig.IsDebug)
            {
                Log.WriteError($"乱码数据排查2:{pageModel.Rows.ToJson()}", "WaybillCodeLoadList.txt");
            }

            var result = new WaybillCodeListPageViewModel()
            {
                IsOrderDesc = pageModel.IsOrderDesc,
                OrderByField = pageModel.OrderByField,
                PageIndex = pageModel.PageIndex,
                PageSize = pageModel.PageSize,
                Total = pageModel.Total,
                Rows = pageModel.Rows,
                //TotalOrderCount = totalOrderCount,
                //TotalWaybillCodeCount = totalWaybillCount
            };
            #region 对收件人信息进行脱敏
            //EncryptionService DataMaskservice = new EncryptionService();
            //var noVirtualOrders = DataMaskservice.getPlatformType(result.Rows);
            //EncryptionService.DataMaskingExpression(result.Rows.Where(w=> noVirtualOrders.Any(a=> a.LogicOrderId == w.OrderId ) )?.ToList());//EncryptionService.DataMaskingReflection(result.Rows);

            var notEncryptOrders = newWaybillCodeLst.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.ReciverPhone?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
            notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
            if (notEncryptOrders.Any())
                EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);

            #endregion

            if (CustomerConfig.IsDebug)
            {
                Log.WriteError($"乱码数据排查3:{pageModel.Rows.ToJson()}", "WaybillCodeLoadList.txt");
            }

            return Json(result);
        }

        /// <summary>
        /// 获取运单打印记录
        /// </summary>
        /// <param name="waybillCodeOrderId"></param>
        /// <returns></returns>
        public ActionResult LoadPrintHistory(string orderId, string waybillCode, string childWaybillCode, int fxUserId)
        {
            _printHistoryService = new PrintHistoryService(SiteContext.Current.CurrentFxUserId);
            fxUserId = fxUserId == 0 ? SiteContext.Current.CurrentFxUserId : fxUserId;
            var dataList = _printHistoryService.GetPrintHistoryByWaybillCodeId(fxUserId, orderId, waybillCode, childWaybillCode)?.OrderByDescending(f => f.PrintDate)?.ToList();
            //收件人信息 解密
            var wlst = dataList?.Select(x => new WaybillCode() { OrderId = x.PlatformOrderId, ShopId = x.ShopId, Reciver = x.Reciver, ReciverPhone = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToDistrict = x.ToDistrict, ToAddress = x.ReciverAddress, PrintHistoryID = x.ID }).ToList();
            BuildExccelService.EncryptReceiverInfo(wlst, isFx: true);

            ActionResult checkResult = null;
            try
            {
                //20241010底单校验模板 允许打印删除的模板进行重打
                checkResult = new PrintHistoryController().PerCheckTemplate(dataList, Request, Response,false);
            }
            catch (Exception ex)
            {
                checkResult = Json(new AjaxResult() { Success = false, ErrorCode = "Exception", Message = ex.Message });
            }

            dataList?.ForEach(item =>
            {
                var decryptedOrder = wlst.FirstOrDefault(x => x.OrderId == item.PlatformOrderId && x.ShopId == item.ShopId && x.PrintHistoryID == item.ID);
                if (decryptedOrder != null)
                {
                    item.Reciver = decryptedOrder.Reciver;
                    item.ReciverPhone = decryptedOrder.ReciverPhone;
                    item.BuyerMemberName = item.Reciver;
                    item.BuyerMemberId = item.Reciver;
                    item.ReciverAddress = decryptedOrder.ToAddress;
                    item.CheckResult = checkResult;
                    if (item.PlatformType.IsNullOrEmpty())
                        item.PlatformType = decryptedOrder.PlatformType;
                }
                item.TotalWeight = item.TotalWeight.ConvertGToKg();
            });

            //跨境需要把打印数据的PDF重新成生成并下载
            if (CustomerConfig.IsCrossBorderSite)
                _commService.GetTikTokPrintPdf(dataList);
            //头条需要把打印数据的签名重新生成一遍
            _commService.UpSignPrintHistory(dataList);
            #region 对收件人信息进行脱敏
            //EncryptionService DataMaskservice = new EncryptionService();
            //var noVirtualOrders = DataMaskservice.getPlatformType(dataList);
            //EncryptionService.DataMaskingExpression(dataList.Where(w=> noVirtualOrders.Any(a=> a.LogicOrderId == w.PlatformOrderId ) )?.ToList());//EncryptionService.DataMaskingReflection(dataList);

            var notEncryptOrders = dataList.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.ReciverPhone?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
            notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
            if (notEncryptOrders.Any())
                EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);
            #endregion
            return Json(dataList);
        }

        public ActionResult LoadReprintModel(int waybillCodeId)
        {
            var model = _service.GetReprintWaybillCodeModel(waybillCodeId);
            return Json(model);
        }

        /// <summary>
        /// 获取运单打印记录（批量）
        /// </summary>
        /// <param name="waybillCodeIdAndChildWaybillCodeList"></param>
        /// <returns></returns>
        public ActionResult LoadPrintHistoryList(List<GetPrintHistListRequestModel> waybillCodeIdAndChildWaybillCodeList)
        {
            _printHistoryService = new PrintHistoryService(SiteContext.Current.CurrentFxUserId);
            if (waybillCodeIdAndChildWaybillCodeList == null || waybillCodeIdAndChildWaybillCodeList.Count == 0)
                return FalidResult("后台未收到数据!");
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var dataList = _printHistoryService.LoadPrintHistoryList(waybillCodeIdAndChildWaybillCodeList, fxUserId);//?.GroupBy(f => f.WaybillCodeOrderId).ToList().Select(f => f.OrderByDescending(ob => ob.PrintDate).FirstOrDefault());


            //前置检查模板
            //20241010底单校验模板 允许打印删除的模板进行重打
            var checkResult = new PrintHistoryController().PerCheckTemplate(dataList, Request, Response,false);
            if (checkResult != null)
                return checkResult;

            //跨境需要把打印数据的PDF重新成生成并下载
            if (CustomerConfig.IsCrossBorderSite)
                new CommService().GetTikTokPrintPdf(dataList);

            new PrintHistoryController().GetPrintHistoryDataFromApi(dataList);

            //收件人信息 解密
            var wlst = dataList?.Select(x => new WaybillCode() { OrderId = x.PlatformOrderId, ShopId = x.ShopId, Reciver = x.Reciver, ReciverPhone = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToDistrict = x.ToDistrict, ToAddress = x.ReciverAddress }).ToList();
            BuildExccelService.EncryptReceiverInfo(wlst, isFx: true);
            dataList?.ForEach(item =>
            {
                var decryptedOrder = wlst.FirstOrDefault(x => x.OrderId == item.PlatformOrderId && x.ShopId == item.ShopId);
                if (decryptedOrder != null)
                {
                    item.Reciver = decryptedOrder.Reciver;
                    item.ReciverPhone = decryptedOrder.ReciverPhone;
                    item.BuyerMemberName = item.Reciver;
                    item.BuyerMemberId = item.Reciver;
                    item.ReciverAddress = decryptedOrder.ToAddress;
                    if (item.PlatformType.IsNullOrEmpty())
                        item.PlatformType = decryptedOrder.PlatformType;
                }
            });
            //跨境需要把打印数据的PDF重新成生成并下载
            if (CustomerConfig.IsCrossBorderSite)
                _commService.GetTikTokPrintPdf(dataList);

            //头条需要把打印数据的签名重新生成一遍
            _commService.UpSignPrintHistory(dataList);
            #region 对收件人信息进行脱敏
            //EncryptionService DataMaskservice = new EncryptionService();
            //var noVirtualOrders = DataMaskservice.getPlatformType(dataList);
            //EncryptionService.DataMaskingExpression(dataList.Where(w=> noVirtualOrders.Any(a=> a.LogicOrderId == w.PlatformOrderId ) )?.ToList());//EncryptionService.DataMaskingReflection(dataList);

            var notEncryptOrders = dataList.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.ReciverPhone?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
            notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
            if (notEncryptOrders.Any())
                EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);

            #endregion
            return Json(dataList);
        }

        /// <summary>
        /// 获取运单使用记录
        /// </summary>
        /// <param name="expressId"></param>
        /// <param name="waybillCode"></param>
        /// <returns></returns>
        public ActionResult LoadWaybillCodeUseRecord(int expressId, string waybillCode, int fxUserId)
        {
            fxUserId = fxUserId == 0 ? SiteContext.Current.CurrentFxUserId : fxUserId;
            var dataList = _service.GetWaybillCodeList(waybillCode, expressId, fxUserId);
            //收件人信息解密
            BuildExccelService.EncryptReceiverInfo(dataList, isFx: true);

            EncryptionService.DataMaskingExpression(dataList);
            return Json(dataList);
        }

        /// <summary>
        /// 获取运单发货记录
        /// </summary>
        /// <param name="expressCode"></param>
        /// <param name="waybillCode"></param>
        /// <returns></returns>
        public ActionResult LoadWaybillCodeSendRecord(string expressCode, string waybillCode, int fxUserId)
        {
            fxUserId = fxUserId == 0 ? SiteContext.Current.CurrentFxUserId : fxUserId;
            var dataList = _sendHistoryService.GetSendHistoryList(fxUserId, waybillCode, expressCode);

            //收件人信息解密
            var wlst = dataList?.Select(x => new WaybillCode() { OrderId = x.OrderId, ShopId = x.ShopId, Reciver = x.Reciver, ReciverPhone = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToDistrict = x.ToDistrict, ToAddress = x.ReciverAddress }).ToList();
            BuildExccelService.EncryptReceiverInfo(wlst, isFx: true);
            dataList?.ForEach(item =>
            {
                var decryptedOrder = wlst.FirstOrDefault(x => x.OrderId == item.OrderId && x.ShopId == item.ShopId);
                if (decryptedOrder != null)
                {
                    item.Reciver = decryptedOrder.Reciver;
                    item.ReciverPhone = decryptedOrder.ReciverPhone;
                    item.BuyerMemberName = item.Reciver;
                    item.BuyerMemberId = item.Reciver;
                    item.ReciverAddress = decryptedOrder.ToAddress;
                    if (item.PlatformType.IsNullOrEmpty())
                        item.PlatformType = decryptedOrder.PlatformType;
                }
            });
            #region 对收件人信息进行脱敏
            //EncryptionService DataMaskservice = new EncryptionService();
            //var noVirtualOrders = DataMaskservice.getPlatformType(dataList);
            //EncryptionService.DataMaskingExpression(dataList.Where(w=> noVirtualOrders.Any(a=> a.LogicOrderId == w.OrderId ))?.ToList());//EncryptionService.DataMaskingReflection(dataList);

            var notEncryptOrders = dataList.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.ReciverPhone?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
            notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
            if (notEncryptOrders.Any())
                EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);

            #endregion
            return Json(dataList);
        }

        /// <summary>
        /// 取消电子面单
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="orderId"></param>
        /// <param name="waybillCode"></param>
        /// <returns></returns>
        [LogForOperatorFilter("单个回收")]
        [FxMigrateLockFilter()]
        [FxAuthorize(FxPermission.RecycleWaybillCode)]
        public ActionResult CancelWaybillCode(int templateId, List<WaybillCodeRecycleViewModel> wcRecycleViewModel)
        {
            var errorList = new List<string>();
            var successList = new List<int>();

            //由于订单列表那边的回收，可能对应的模板不是实际打印单号的模板，会导致回收有问题，或者回收后还回分享单号有问题
            //所以要根据 底单去查询出实际使用的模板
            var wycIds = wcRecycleViewModel.Select(f => f.WaybillCodeId).ToList();
            var templateIdWaybillCodeId = _service.GetTemplateIdByWaybillCodeId(wycIds);
            var tIdGroup = templateIdWaybillCodeId.GroupBy(f => f.TemplateId);
            tIdGroup.ToList().ForEach(f =>
            {
                var tempId = f.Key;
                var wcyTempIds = f.Select(x => x.ID).ToList();
                var data = wcRecycleViewModel.Where(p => wcyTempIds.Contains(p.WaybillCodeId))?.ToList();

                var tempErroList = new List<string>();
                var tempSuccList = new List<int>();
                try
                {
                    CancelAction(tempErroList, tempSuccList, new BatchWaybillCodeRecycleViewModel()
                    {
                        TemplateId = tempId,
                        WaybillCodeRecycleViewModels = data
                    });
                }
                catch (Exception ex)
                {
                    var recycleError = data.ToDictionary(k => k.WaybillCodeId, v => ex.Message);
                    _service.RecycleErrorWaybillCode(recycleError);
                    throw;
                }
                

                errorList.AddRange(tempErroList);
                successList.AddRange(tempSuccList);
            });


            //批量回收，返回运单id，用户订单列表删除回收的运单号
            if (errorList.Count == 0)
            {
                return SuccessResult(data: successList);
            }
            else
                return FalidResult(errorList.ToJson(), data: successList);
        }

        /// <summary>
        /// 取消电子面单
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="orderId"></param>
        /// <param name="waybillCode"></param>
        /// <returns></returns>
        [LogForOperatorFilter("批量回收")]
        [FxMigrateLockFilter()]
        [FxAuthorize(FxPermission.RecycleWaybillCode)]
        public ActionResult BatchCancelWaybillCode(List<BatchWaybillCodeRecycleViewModel> batchWaybillCodeRecycleVmList)
        {
            var errorList = new List<string>();
            var successList = new List<int>();
            
            batchWaybillCodeRecycleVmList.ForEach(item =>
            {
                try
                {
                    CancelAction(errorList, successList, item);
                }
                catch (Exception ex)
                {
                    var recycleError = item.WaybillCodeRecycleViewModels.ToDictionary(k => k.WaybillCodeId, v => ex.Message);
                    _service.RecycleErrorWaybillCode(recycleError);
                    throw;
                }
            });

            //批量回收，返回运单id，用户订单列表删除回收的运单号
            if (errorList.Count == 0)
                return SuccessResult(data: new { IsError = false, ErrorList = new List<string>(), SuccessList = successList });
            else
                return SuccessResult(data: new { IsError = true, ErrorList = errorList, SuccessList = successList });
        }

        [LogForOperatorFilter("异常订单回收")]
        [FxMigrateLockFilter()]
        [FxAuthorize(FxPermission.RecycleWaybillCode)]
        public ActionResult WaybillCodeRecycle(List<BatchWaybillCodeRecycleViewModel> batchWaybillCodeRecycles)
        {
            var orderAbnormals = batchWaybillCodeRecycles.SelectMany(m => m.WaybillCodeRecycleViewModels).ToList();
            var waybillCodeIds = orderAbnormals.Select(m => m.WaybillCodeId).Distinct().ToList();
            var waybillCodes = _service.GetWaybillCodesByIds(waybillCodeIds);
            var mergeWaybillCodes = waybillCodes.Where(m => m.OrderId.StartsWith("C")).ToList();
            Log.Debug($"获取到合单底单，相关信息：{waybillCodes.ToJson(true)}", $"WaybillCodeRecycle_{DateTime.Now:yyyy-MM-dd}.log");
            //处理合单情况
            var orderAbnormalModels = new List<OrderAbnormal>();
            batchWaybillCodeRecycles.ForEach(recycle =>
            {
                recycle.WaybillCodeRecycleViewModels.ForEach(view =>
                {
                    //检测是否合单
                    var mergeWaybillCode = mergeWaybillCodes.FirstOrDefault(m => m.ID == view.WaybillCodeId);
                    if (mergeWaybillCode == null)
                    {
                        orderAbnormalModels.Add(new OrderAbnormal
                        {
                            Id = view.OrderAbnormalId,
                            AbnormalType = view.AbnormalType,
                            PlatformOrderId = view.PlatformOrderId,
                            LogicOrderId = view.OrderId,
                            PrintTime = view.PrintTime,
                            LogisticsBillNo = view.WaybillCode,
                            WaybillCodeId = view.WaybillCodeId
                        });
                    }
                    else
                    {
                        //更新成合单
                        view.OrderId = mergeWaybillCode.OrderId;
                        //异常订单
                        var models = mergeWaybillCode.WaybillCodeOrders.Select(m => new OrderAbnormal
                        {
                            Id = view.OrderAbnormalId,
                            AbnormalType = view.AbnormalType,
                            PlatformOrderId = m.CustomerOrderId,
                            LogicOrderId = m.OrderId,
                            PrintTime = view.PrintTime,
                            LogisticsBillNo = view.WaybillCode,
                            WaybillCodeId = view.WaybillCodeId,
                            IsMergeOrder = true
                        }).ToList();
                        orderAbnormalModels.AddRange(models);
                    }
                });
            });
            Log.Debug($"回收运单号，相关信息：{batchWaybillCodeRecycles.ToJson(true)}", $"WaybillCodeRecycle_{DateTime.Now:yyyy-MM-dd}.log");
            //回收
            var successList = new List<int>();
            var errorList = new List<string>();
            Parallel.ForEach(batchWaybillCodeRecycles, new ParallelOptions { MaxDegreeOfParallelism = 10 }, item =>
            {
                try
                {
                    CancelAction(errorList, successList, item);
                }
                catch (Exception ex)
                {
                    var recycleError = item.WaybillCodeRecycleViewModels.ToDictionary(k => k.WaybillCodeId, v => ex.Message);
                    _service.RecycleErrorWaybillCode(recycleError);
                    throw;
                }               
            });
            //成功异常订单
            var successAbnormalOrders = orderAbnormalModels.Where(m => successList.Contains(m.WaybillCodeId)).ToList();
            //非合单异常单
            var notMergeAbnormalOrderIds = successAbnormalOrders.Where(m => !m.IsMergeOrder).Select(m => m.Id).ToList();
            if (notMergeAbnormalOrderIds.Any())
            {
                new OrderAbnormalService().UpdateAssignFields(notMergeAbnormalOrderIds, "RecycleStatus");
            }
            //异常订单更新回收状态
            var mergeAbnormalOrders = successAbnormalOrders.Where(m => m.IsMergeOrder).Select(m =>
                    new
                    {
                        m.AbnormalType,
                        m.PrintTime,
                        m.LogisticsBillNo,
                        m.WaybillCodeId
                    }).Distinct()
                .ToList();
            if (mergeAbnormalOrders.Any())
            {
                Parallel.ForEach(mergeAbnormalOrders, new ParallelOptions { MaxDegreeOfParallelism = 5 }, abnormalOrder =>
                {
                    new OrderAbnormalService().UpdateAssignFields("RecycleStatus", abnormalOrder.AbnormalType,
                        abnormalOrder.LogisticsBillNo, abnormalOrder.PrintTime.Value,
                        wayBillCodeId: abnormalOrder.WaybillCodeId);
                });
                //abnormalOrders.ForEach(abnormalOrder =>
                //{
                //    if (abnormalOrder.AbnormalType != AbnormalOrderTypes.UnBindSupplier)
                //    {
                //        new OrderAbnormalService().UpdateAssignFields("RecycleStatus", abnormalOrder.AbnormalType,
                //            abnormalOrder.WaybillCode, abnormalOrder.PrintTime);
                //    }
                //    else
                //    {
                //        new OrderAbnormalService().UpdateAssignFields("RecycleStatus", abnormalOrder.AbnormalType,
                //            abnormalOrder.WaybillCode, abnormalOrder.PrintTime, abnormalOrder.OrderId,
                //            abnormalOrder.PlatformOrderId);
                //    }
                //});
            }

            //批量回收，返回运单id，用户订单列表删除回收的运单号
            if (errorList.Count == 0)
                return SuccessResult(data: new { IsError = false, ErrorList = new List<string>(), SuccessList = successList });
            else
                return SuccessResult(data: new { IsError = true, ErrorList = errorList, SuccessList = successList });
        }

        public void CancelAction(List<string> errorList, List<int> successList, BatchWaybillCodeRecycleViewModel recycleModel)
        {
            //复制一份
            var model = recycleModel.ToJson().ToObject<BatchWaybillCodeRecycleViewModel>();
            
            if (model.TemplateId == 0)
            {
                errorList.Add("模板id为0,找不到对应模板，请确认数据是否为迁移数据，迁移过来的底单不支持回收。");
                return;
            }

            //1.获取模板
            var template = _printTemplateService.Get(model.TemplateId, false);

            if (template == null)
            {
                errorList.Add("单号打印的模板未找到，请确认数据是否为迁移数据，迁移过来的底单不支持回收。");
                return;
            }
            var recycleError = new ConcurrentDictionary<int, string>();
            ////2.过滤排除 已发货的
            //for (var i = model.WaybillCodeRecycleViewModels.Count - 1; i >= 0; i--)
            //{
            //    var waybillCodeStatus = _service.GetWaybillCodeStatusById(model.WaybillCodeRecycleViewModels[i].WaybillCodeId);
            //    if (waybillCodeStatus == 3)
            //    {
            //        errorList.Add("您所勾选的运单号包含已发货状态，不可进行回收，请重新刷新页面。");
            //        model.WaybillCodeRecycleViewModels.Remove(model.WaybillCodeRecycleViewModels[i]);
            //    }
            //}

            var express = _expressCompanyService.Get(template.ExpressCompanyId);
            //去重
            model.WaybillCodeRecycleViewModels = model.WaybillCodeRecycleViewModels
                .GroupBy(m => new WaybillCodeRecycleViewModel
                { WaybillCode = m.WaybillCode, OrderId = m.OrderId, WaybillCodeId = m.WaybillCodeId })
                .Select(m => m.Key).ToList();
            Tuple<bool, string> result = null;
            //网点模板
            if (template.PrintTemplateType == Data.Enum.PrintTemplateType.LogisticTemplate)
            {
                //获取网点账号
                UserSiteInfo siteAccount = null; //_userSiteInfoService.Get("WHERE TemplateId=@TemplateId", new { TemplateId = model.TemplateId }).FirstOrDefault();
                var templateExtend = (new PrintTemplateExtendService()).GetModel(template.Id);
                siteAccount = new UserSiteInfo()
                {
                    UserName = templateExtend.CustomerName,
                    UserPassword = templateExtend.CustomerPwd
                };
                if (siteAccount == null)
                {
                    errorList.Add("网点账号不存在");
                }

                switch (express.CompanyCode)
                {
                    case "STO":
                        //errorList.Add("申通网点电子面单号回收需在大客户平台回收，具体回收步骤请联系申通快递公司!");
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = _service.WlbWaybillICancelSTO(item.OrderId, item.WaybillCode);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                                recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    case "YTO":
                        //model.WaybillCodeRecycleViewModels.ForEach(item =>
                        //{
                        //    result = _service.WlbWaybillICancelYTO(siteAccount, item.OrderId, item.WaybillCode);
                        //    if (result.Item1 == false)
                        //    {
                        //        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        //    }
                        //    else
                        //    {
                        //        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                        //    }
                        //});
                        errorList.Add("不支持电子面单账号手动回收订单,系统30天自动回收订单!");
                        break;
                    case "ZTO":
                        errorList.Add("不支持手动回收,获取单号后无扫描记录,30-60天后系统自动回收!");
                        //model.WaybillCodeRecycleViewModels.ForEach(item =>
                        //{
                        //    result = _service.WlbWaybillICancelZTO(siteAccount, item.OrderId, item.WaybillCode);
                        //    if (result.Item1 == false)
                        //    {
                        //        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        //    }
                        //    else
                        //    {
                        //        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                        //    }
                        //});
                        break;
                    case "YUNDA":
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = _service.WlbWaybillICancelYUNDA(siteAccount, item.OrderId, item.WaybillCode);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                                recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    case "HTKY":
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = _service.WlbWaybillICancelHTKY(siteAccount, item.OrderId, item.WaybillCode);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                                recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    case "2608021499_235":
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = _service.WlbWaybillICancelANE(siteAccount, item.OrderId, item.WaybillCode);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                                recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    case "SF": //顺丰，打单走快递鸟，回收也走快递鸟
                    case "PJ": //品骏，打单走快递鸟，回收也走快递鸟
                        var cpcodeMappingModel = (new ExpressCpCodeMappingService()).Get(express.CompanyCode, "KuaiDiNiao");
                        var kdnLogisticService = new KuaiDiNiaoLogisticService();
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = kdnLogisticService.CancelWaybillCode(cpcodeMappingModel?.CpCode, item.OrderId, item.WaybillCode, templateExtend.CustomerName, templateExtend.CustomerPwd);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                                recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    case "JT":
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = _service.WlbWaybillICancelJT(siteAccount, item.OrderId, item.WaybillCode);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                                recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    case "ZYKD":
                        errorList.Add("暂时不支持回收,请联系网点回收!");
                        break;
                    case "DBKD":
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = _service.WlbWaybillICancelDBKD(item.OrderId, item.WaybillCode);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                                recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    default:
                        errorList.Add(string.Format("未找到匹配的快递【{0}】", express.CompanyCode));
                        break;
                }

            }
            //菜鸟模板
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.CaiNiaoPrintTemplate || template.PrintTemplateType == Data.Enum.PrintTemplateType.CaiNiaoBQSPrintTemplate)
            {
                ////获取模板使用的淘宝授权账号
                //var authAccount = _templateRelationAuthInfoService.Get("WHERE TemplateId=@TemplateId", new { TemplateId = template.Id }).FirstOrDefault();
                //if (authAccount == null)
                //{
                //    return FalidResult("未找到授权账号");
                //}

                ////获取授权账号
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);

                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    result = TopCaiNiaoApiService.CancelCainiaoWaybillCode(waybillAuthConfig, template.ExpressCpCodeMapping.CpCode, item.WaybillCode);
                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //菜鸟官方模板
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.LinkPrintTemplate || template.PrintTemplateType == Data.Enum.PrintTemplateType.LinkKuaiYunTemplate)
            {
                ////获取授权账号
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);

                CloudCaiNiaoApiService _cloudCaiNiaoApiService = new CloudCaiNiaoApiService(waybillAuthConfig);

                var cpcodeMappingModel = (new ExpressCpCodeMappingService()).Get(express.CompanyCode, "Top");
                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    result = _cloudCaiNiaoApiService.CancelCainiaoWaybillCode(cpcodeMappingModel?.CpCode ?? express.CompanyCode, item.WaybillCode);
                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //拼多多模板
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.PddPrintTemplate || template.PrintTemplateType == Data.Enum.PrintTemplateType.PddBQSPrintTemplate)
            {
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
                var pddWaybillCodeApiService = new PddWaybillApiService(waybillAuthConfig);
                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    result = pddWaybillCodeApiService.CancelPinduoduoWaybillCode(template.ExpressCpCodeMapping.CpCode, item.WaybillCode);
                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //京东无界模板
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.JdwjPrintTemplate || template.PrintTemplateType == Data.Enum.PrintTemplateType.JdwjzjPrintTemplate)
            {
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
                var wujieWaybillCodeApiService = new WuJieWaybillApiService(waybillAuthConfig);
                var cpcodeMappingModel = (new ExpressCpCodeMappingService()).Get(express.CompanyCode, "JDWJ");
                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    result = wujieWaybillCodeApiService.CancelWaybillCode(item.WaybillCode, cpcodeMappingModel?.CpCode ?? express.CompanyCode);
                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //京东快递模板
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.JdzjPrintTemplate || template.PrintTemplateType == Data.Enum.PrintTemplateType.JdkdPrintTemplate)
            {
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
                var jdLogisticService = new JDLogisticService(false, waybillAuthConfig);

                var templateExtend = new PrintTemplateExtendService().GetModel(template.Id);

                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    if (templateExtend == null)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, "找不到模板的商家信息，请联系我们!"));
                    }
                    else
                    {
                        result = jdLogisticService.CancelWaybillCode(item.WaybillCode, templateExtend.CustomerName);
                        if (result.Item1 == false)
                        {
                            errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                            recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                        }
                        else
                            successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.FengQiaoTemplate)
            {
                var templateExtend = (new PrintTemplateExtendService()).GetModel(template.Id);
                var siteAccount = new UserSiteInfo()
                {
                    UserName = templateExtend.CustomerName,
                    UserPassword = templateExtend.CustomerPwd
                };
                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {

                    result = _service.WlbWaybillICancelSF(siteAccount, item.OrderId, item.WaybillCode);
                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                    }
                    else
                    {
                        //顺丰一单多包回收其中一个单号，还需要把相同pid的单号都回收掉（更改底单状态）
                        var ids = _service.GetOnToManyWaybillCodeIds(item.OrderId, item.WaybillCode);
                        if (ids?.Any() == true)
                            successList.AddRange(ids.Select(f => f.ID));
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //头条模板
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.TouTiaozjTemplate || template.PrintTemplateType == Data.Enum.PrintTemplateType.TouTiaoTemplate || template.PrintTemplateType == Data.Enum.PrintTemplateType.TouTiaoKuaiYunTemplate)
            {
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
                var toutiaoWaybillCodeApiService = new TouTiaoWaybillApiService(waybillAuthConfig);
                var cpcodeMappingModel = (new ExpressCpCodeMappingService()).Get(express.CompanyCode, "TouTiao");
                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    // 即时零售授权打印的快递单, 需要使用单独的api
                    result = toutiaoWaybillCodeApiService.IsTouTiaoSaleShop()
                        ? toutiaoWaybillCodeApiService.CancelWaybillCode_SaleShop(item.WaybillCode,
                            cpcodeMappingModel?.CpCode ?? express.CompanyCode,
                            cpcodeMappingModel?.ExpressCompanyCode ?? express.CompanyCode)
                        : toutiaoWaybillCodeApiService.CancelWaybillCode(item.WaybillCode,
                            cpcodeMappingModel?.CpCode ?? express.CompanyCode);
                    if (result.Item1 == false && result.Item2?.Contains("已经取消过了") == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //快手面单回收
            else if (template.PrintTemplateType == PrintTemplateType.KuaiShouTemplate)
            {
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
                var ksWaybillCodeApiService = new KsWaybillApiService(waybillAuthConfig);

                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    result = ksWaybillCodeApiService.CancelWaybillCode(template.ExpressCpCodeMapping.CpCode, item.WaybillCode);
                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //小红书面单回收
            else if (template.PrintTemplateType == PrintTemplateType.XiaoHongShuTemplate)
            {
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
                var xhsWaybillCodeApiService = new XiaoHongShuWaybillApiService(waybillAuthConfig);

                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    result = xhsWaybillCodeApiService.CancelWaybillCode(template.ExpressCpCodeMapping.CpCode, item.WaybillCode);
                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //新版小红书面单回收
            else if (template.PrintTemplateType == PrintTemplateType.NewXiaoHongShuTemplate)
            {
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
                var xhsWaybillCodeApiService = new XiaoHongShuWaybillApiService(waybillAuthConfig);

                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    //Order order = oldorder.FirstOrDefault(old => item.OrderId.Contains(old.PlatformOrderId));
                    result = xhsWaybillCodeApiService.CancelWaybillCode(template.ExpressCpCodeMapping.CpCode, item.WaybillCode, 2);

                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2, (k, v) => result.Item2);
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //微信视频面单回收
            else if (template.PrintTemplateType == PrintTemplateType.WxVideoTemplate)
            {
                var waybillCodes = model.WaybillCodeRecycleViewModels.Select(f => f.WaybillCode).ToList();
                var waybillCodeList = _service.GetWaybillCodeList(waybillCodes, "ExpressWayBillCode,EwaybillOrderId");
                var waybillCodeDict = waybillCodeList.ToDictionary(f => f.ExpressWayBillCode);

                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
                var wxvWaybillCodeApiService = new WxVideoWaybillApiService(waybillAuthConfig);

                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    WaybillCode waybillCode = null;
                    if (waybillCodeDict.ContainsKey(item.WaybillCode))
                        waybillCode = waybillCodeDict[item.WaybillCode];
                    else
                        return;
                    result = wxvWaybillCodeApiService.CancelWaybillCode(waybillCode.EwaybillOrderId, template.ExpressCpCodeMapping.CpCode, item.WaybillCode);

                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        recycleError.AddOrUpdate(item.WaybillCodeId, result.Item2,(k, v) => result.Item2);
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            if (successList.Count > 0)
            {
                //状态：1：已打印，2：单号已回收，3：已发货
                _service.UpdateWaybillCodeStatusById(2, successList, template);

                // 检查底单记录对应的订单，是否有打标：地址变更，如果有就删除该标签
                var orderIds = model.WaybillCodeRecycleViewModels.Select(a => a.OrderId).ToList();
                new OrderTagsRepository().CheckTagChangeOfAddress(orderIds);

                //（注释说明：tapdID 1009973。已打印已发货了的订单，回收后打印状态也更新成未打印了。
                //目前分单系统只有重新分配厂家这一个场景有逻辑单变更为未打印。跟打单线保持一致，回收底单不用更新逻辑单未打印）
                ////若OrderId关联的所有运单号都已回收，则对应LogicOrder的PrintState更新为0
                //var orderIdList = model.WaybillCodeRecycleViewModels.Where(p => successList.Contains(p.WaybillCodeId))?.Select(x => x.OrderId)?.ToList();
                //orderIdList.ForEach(orderId =>
                //{
                //    var haveNotRecycle = _service.ExistNotRecycleWaybillCodeByOrderId(orderId);
                //    if (haveNotRecycle == false)//没有除回收以外的状态
                //    {
                //        _logicOrderService.UpdateLogicOrderPrintStateById(orderId);
                //    }
                //});
            }
            if (recycleError.Count > 0)
            {
                //记录回收失败的数据
                _service.RecycleErrorWaybillCode(recycleError.ToDictionary(k => k.Key, v => v.Value));
            }
        }

        /// <summary>
        /// 只更改回收状态（ 快递自动回收单号）
        /// </summary>
        /// <param name="waybillCodeIds"></param>
        /// <returns></returns>
        public ActionResult AutoCancelWaybillCode(string waybillCodeIds)
        {
            if (waybillCodeIds.IsNullOrEmpty())
                return FalidResult("回收单号不能为空");
            var wids = waybillCodeIds.SplitToList(",").ConvertAll(x => x.ToInt());

            var fields = new List<string> { "Id", "ExpressWayBillCode", "TemplateId", "FxUserId" };
            var wList = _service.GetByWaybillCodeIds(wids, fields);
            if (wList.Any() == false)
                return FalidResult("未找到需要回收的单号");
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            if (wList.Any(x => x.FxUserId != fxUserId))
                return FalidResult("只能回收当前登录账号的运单号");

            var tids = wList.Select(x => x.TemplateId).Distinct().ToList();
            foreach (var tid in tids)
            {
                //1.获取模板
                var template = _printTemplateService.Get(tid, false);
                if (template == null)
                    return FalidResult("单号打印的模板未找到，请确认数据是否为迁移数据，迁移过来的底单不支持回收。");
                var ids = wList.Where(x => x.TemplateId == tid).Select(x => x.ID).Distinct().ToList();
                if (ids.Any())
                    _service.UpdateWaybillCodeStatusById(2, ids, template);
            }
            return SuccessResult();
        }

        #region 订单导出

        [FxAuthorize(AuthorizeType.ExportWaybillCode)]
        public ActionResult ExportExcel()
        {
            try
            {
                var shop = SiteContext.Current.CurrentLoginShop;
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var type = Request["type"].ToString2();
                var isCj = type == "cj";
                var dbname = Request["dbname"].ToString2();
                var options = Request.Form["options"].ToString2();
                options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
                options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果
                WaybillCodeRequestModel requestModel = JsonExtension.ToObject<WaybillCodeRequestModel>(options) ?? new WaybillCodeRequestModel();
                var check = _service.CheckRequestModel(requestModel);
                if (check.Item1 == false) return FalidResult(check.Item2);
                var timeOutStatus = false;

                //var task = _exportTaskService.GetErpWebExportTask(shop.Id, CustomerConfig.CloudPlatformType, ExportType.ErpWaybillCode.ToInt());
                // 是否查询缓存数据
                const string cacheKey = ExportTaskService.ExportTaskCacheKey;
                var isUseCache =  (new CommonSettingService().Get(cacheKey, 0)?.Value.ToInt() ?? 0) == 1;
                if (isUseCache)
                {
                    var redisKey = CacheKeys.ExportTaskQueryKey
                        .Replace("{ExportType}", ExportType.ErpWaybillCode.ToInt().ToString())
                        .Replace("{FxUserId}", SiteContext.Current.CurrentFxUserId.ToString());
                    var cacheModel = RedisHelper.Get<ExportTaskCacheModel>(redisKey);
                    if (cacheModel != null) return FalidResult("已存在订单导出任务，如需重新导出，请先取消再创建新导出任务", cacheModel);
                }
                else
                {
                    //分库 - 精选平台只显示分库创建的导出任务
                    var task = _exportTaskService.GetErpWebExportTaskSharding(shop.Id, CustomerConfig.CloudPlatformType, ExportType.ErpWaybillCode.ToInt(), Request["dbname"] ?? "");
                    if (task != null && task.Status >= 0 && task.Status < 4)
                        return FalidResult("已存在订单导出任务，如需重新导出，请先取消再创建新导出任务", GetExportTaskToWeb(task));
                    #region 导出任务验证、查询超时转换后台任务
                    var timeOutSecond = 25;
                    var typeStr = "";
                    var timeOutSet = Request["time_out_set"].ToBoolean();// 模拟任务校验超时
                    var isVerification = requestModel.DisabledTaskVerification != true; // 是否任务校验
                    if (isVerification)
                    {
                        try
                        { 
                            task = _exportTaskService.GetErpWebExportTaskSharding(shop.Id
                                , CustomerConfig.CloudPlatformType
                                , isCj ? ExportType.ErpWaybillCodeCJ.ToInt() : ExportType.ErpWaybillCode.ToInt()
                                , Request["dbname"] ?? ""
                                , timeOutSecond); ;

                            if (task != null && task.Status >= 0 && task.Status < 4)
                                return FalidResult("已存在订单导出任务，如需重新导出，请先取消再创建新导出任务", GetExportTaskToWeb(task));
                        }
                        catch (Exception ex)
                        {
                            if (ex.Message.IndexOf("执行超时已过期") != -1)
                            {
                                timeOutStatus = true;
                                typeStr = Request["type"].ToString2() == "cj" ? "厂家底单导出" : "底单导出";
                                Log.WriteError($"导出任务查询超时，转换为后台导出任务，超时原因：{ex.ToJsonExt()},超时任务类型：{typeStr}");
                            }
                        }
                    }
                    timeOutStatus = timeOutSet || timeOutStatus;
                    #endregion
                }

                if (!string.IsNullOrWhiteSpace(requestModel.StartDate) && !string.IsNullOrWhiteSpace(requestModel.EndDate))
                {
                    var sTime = requestModel.StartDate.ToDateTime();
                    var eTime = requestModel.EndDate.ToDateTime();
                    var s_e_span = eTime - sTime;
                    if (s_e_span.Value.TotalDays > 60)
                    {
                        return FalidResult("查询范围只支持60天内，请重新设置范围，再进行导出操作");
                    }
                }

                //注意：查询用的是FxUserId，读取导出配置和创建导出任务用shopId
                requestModel.FxUserId = fxUserId;
               
                var key = isCj ? "ErpWeb/WaybillCodeCJ/ExportSetting" : "ErpWeb/WaybillCode/ExportSetting";
                var checkedJson = _commSettingService.Get(key, shop.Id)?.Value.ToString2() ?? "";
                if (checkedJson.IsNullOrEmpty())
                    checkedJson = new CommonSettingService().GetFxDefaultSetting(shop.PlatformType, "WaybillCode_ExportSetting");
                var checkedFieldsLst = checkedJson.IsNullOrEmpty() ? new List<WaybillCodeCheckModel>() : JsonExtension.ToList<WaybillCodeCheckModel>(checkedJson);
                var fields = FxBuildExccelService.GetWaybillCodeExportFields(requestModel, checkedFieldsLst);

                //获取任务导出阈值和页大小
                var limitCount = 0;
                var commSets = _commonSettingService.GetSets(new List<string> { "ErpWeb/WaybillCode/ExportCountLimit", "ErpWeb/WaybillCode/ExportDefaultPageSize" }, 0);
                var limitCountSet = commSets?.FirstOrDefault(m => m.Key == "ErpWeb/WaybillCode/ExportCountLimit");
                var pageSizeSet = commSets?.FirstOrDefault(m => m.Key == "ErpWeb/WaybillCode/ExportDefaultPageSize");
                limitCount = limitCountSet?.Value.ToInt() ?? CustomerConfig.ExportByTaskWaybillCodeCountLimit;
                limitCount = limitCount > 0 ? limitCount : CustomerConfig.ExportByTaskWaybillCodeCountLimit;
                var pageSize = pageSizeSet?.Value.ToInt() ?? 1000;
                pageSize = pageSize > 0 ? pageSize : 1000;
                //查询第一页数据
                var model = requestModel.ToJson().ToObject<WaybillCodeRequestModel>();
                requestModel.PageIndex = 1;
                model.PageIndex = requestModel.PageIndex;
                model.PageSize = pageSize;
                var returnCount = model.PageSize;

                var queryModel = new WaybillCodeLoadListModel { needPagging = false, QueryType = WaybillQueryType.OnlyCount };
                var result = _service.LoadList(model, queryModel);
                var totalCount = result.Total;
                if (totalCount > limitCount || timeOutStatus)
                {
                    //使用任务方式导出
                    var newTask = new ExportTask
                    {
                        IP = Request.UserHostAddress,
                        CreateTime = DateTime.Now,
                        PlatformType = CustomerConfig.CloudPlatformType,
                        ShopId = shop.Id,
                        UserId = SiteContext.Current.CurrentFxUserId.ToString(),
                        Status = 0,
                        Type = isCj ? ExportType.ErpWaybillCodeCJ.ToInt() : ExportType.ErpWaybillCode.ToInt(),
                        PageIndex = 1,
                        PageSize = pageSize,
                        TotalCount = totalCount,
                        ParamJson = model.ToJson(),
                        FromModule = isCj ? "厂家底单--Excel导出" : "底单查询--Excel导出",
                        ExtField2 = shop.PlatformType,
                        ExtField5 = dbname,
                        ExtField4 = CustomerConfig.IsCrossBorderSite ? "3" : null,//跨境环境指定灰度3导出程序
                        ExtField6 = CustomerConfig.IsCrossBorderSite ? PlatformType.TikTok.ToString() : CustomerConfig.CloudPlatformType, //跨境
                        SubFxUserId = SiteContext.GetSubFxUserId().ToString()
                    };
                    newTask.HopeExecuteTime = new CommService().ExportExecuteCheck(newTask.UserId.ToInt(), newTask.Type);
                    newTask.Id = _exportTaskService.Add(newTask);
                    return SuccessResult("导出任务创建成功", GetExportTaskToWeb(newTask));
                }

                // 直接导出方式进行导出            
                var list = new List<WaybillCode>();
                while (returnCount > 0)
                {
                    model = requestModel.ToJson().ToObject<WaybillCodeRequestModel>();
                    model.PageIndex = requestModel.PageIndex;
                    model.PageSize = pageSize;

                    queryModel = new WaybillCodeLoadListModel { needPagging = true, QueryType = WaybillQueryType.OnlyData, fields = fields };
                    result = _service.LoadList(model, queryModel);

                    var pgList = result.Rows; //_service.LoadListForFxExportExcel(model, fields);
                    list.AddRange(pgList);
                    returnCount = pgList.Count;
                    requestModel.PageIndex++;
                }

                if ((list?.Count ?? 0) == 0)
                    return FalidResult("无满足条件的数据导出");

                var fileName = "底单查询-打印.xls";
                //IWorkbook workbook = BuildExcel(list, checkedFieldsLst, fileName);
                var workbook = FxBuildExccelService.BuildFxWaybillCodeExcel(model, isCj, list, checkedFieldsLst, fileName); //BuildExcel(list, checkedFieldsLst, fileName);

                Response.Cookies.Add(new HttpCookie("downloadToken", Request.Form["downloadToken"].ToString2()));
                using (MemoryStream ms = new MemoryStream())
                {
                    workbook.Write(ms);
                    var buffer = ms.GetBuffer();
                    return File(buffer, "application/ms-excel", ExcelHelper.GetFileName(fileName, Request));
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"底单查询Excel导出失败：{ex}");
                return FalidResult("程序异常，请联系我们");
            }
        }

        private WaybillCode EncryptReceiverInfo(WaybillCode order)
        {
            order.BuyerMemberName = order.BuyerMemberName.ToEncryptName();
            order.BuyerMemberId = order.BuyerMemberId.ToEncryptName();
            order.ReciverPhone = order.ReciverPhone.ToPddEncryptPhone();
            order.Reciver = order.Reciver.ToEncryptName();
            order.ToAddress = order.ToAddress.ToPddEncryptAddress();
            //order.ToDistrict = "****";
            return order;
        }

        private IWorkbook BuildExcel(List<WaybillCode> waybillCodeLst, List<WaybillCodeCheckModel> checkedItems, string fileName)
        {
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (waybillCodeLst != null && waybillCodeLst.Any()
                && (pt == PlatformType.Pinduoduo.ToString()
                || pt == PlatformType.Jingdong.ToString() || pt == PlatformType.Taobao.ToString()))
            {
                if (pt == PlatformType.Pinduoduo.ToString())
                {
                    var tempOrders = waybillCodeLst?.Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ToAddress }).ToList();

                    //底单中保存的订单编号为逻辑订单号，需要转换成原始订单编号，才能解密收件人信息
                    var logicOrderPidDict = _logicOrderService.GetOrders(tempOrders.Select(f => f.PlatformOrderId).ToList(),
                        fields: "o.LogicOrderId,o.PlatformOrderId".Split(",".ToArray()).ToList()).ToDictionary(f => f.LogicOrderId, f => f.PlatformOrderId);

                    tempOrders.ForEach(to =>
                    {
                        string pid = string.Empty;
                        if (logicOrderPidDict.TryGetValue(to.PlatformOrderId, out pid))
                        {
                            to.PlatformOrderId = pid;
                        }
                    });

                    BranchShareRelationService.TryToDecryptPddOrders(tempOrders, true);
                    //按店铺分组
                    waybillCodeLst?.ForEach(item =>
                    {
                        var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId && x.ShopId == item.ShopId);
                        if (decryptedOrder != null)
                        {
                            item.Reciver = decryptedOrder.ToName;
                            item.ReciverPhone = decryptedOrder.ToMobile;
                            item.BuyerMemberName = item.Reciver;
                            item.BuyerMemberId = item.Reciver;
                            item.ToAddress = decryptedOrder.ToFullAddress;
                        }
                    });
                }
                waybillCodeLst.ForEach(o =>
                {
                    EncryptReceiverInfo(o);
                });
            }

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);

            ISheet sheet = workbook.CreateSheet("底单记录");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);


            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            checkedItems.ForEach(model =>
            {
                var headName = model.Text.ToString2();
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;
            waybillCodeLst.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;
                var dic = (model ?? new WaybillCode()).ToDictionary();

                colIndex = 0;
                foreach (var item in checkedItems)
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item?.Value.ToString2() ?? "";
                    if (dic.ContainsKey(key) || key == "RowIndex" || key == "ShopName")
                    {
                        var val = key == "RowIndex" ? rowIndex.ToString2() : key == "ShopName" ? "" : dic[key].ToString2();
                        if (key != "RowIndex")
                        {
                            if (key == "ToAddress")
                                val = $"{dic["ToProvince"].ToString2()} {dic["ToCity"].ToString2()} {dic["ToDistrict"].ToString2()} {dic["ToAddress"].ToString2()}";
                            else if (key == "BuyerRemark" || key == "SellerRemark")
                            {
                                var arr = val.Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                                val = arr.Length == 0 ? "" : val;
                            }
                            else if (key == "Status")
                            {
                                if (val == "1")
                                    val = "已打印";
                                else if (val == "2")
                                    val = "已回收";
                                else if (val == "3")
                                    val = "已发货";
                                else
                                    val = "未知状态";
                            }
                            else if (key == "ShopName")
                            {
                                var shops = SiteContext.Current.AllShops;
                                val = shops?.FirstOrDefault(m => m.Id == dic["ShopId"].ToInt()).NickName ?? "";
                            }
                            else if (key == "OrderId")
                            {
                                var customerOrderId = dic["CustomerOrderId"].ToString2();
                                if (!customerOrderId.IsNullOrEmpty())
                                    val = customerOrderId;
                                else
                                {
                                    var orderIdJoin = dic["OrderIdJoin"].ToString2();
                                    if (!orderIdJoin.IsNullOrEmpty())
                                        val = orderIdJoin.Replace(",", "\n");
                                }
                            }
                            else if (key == "ExpressWayBillCode")
                            {
                                var childWaybillCode = dic["ChildWaybillCode"].ToString2();
                                val = $"{val}{(childWaybillCode.IsNullOrEmpty() ? "" : $"/{childWaybillCode}")}";
                            }
                        }

                        if (key == "BuyerRemark" || key == "SellerRemark" || key == "ToAddress" || key == "SendContent")
                            tmpStyle = leftContentStyle;

                        dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                        dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                        colIndex++;
                    }
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;
            });

            return workbook;
        }

        private void SetColumnWidth(ISheet sheet, string headName, int index)
        {
            int width = 20 * 256;
            if (headName == "序号" || headName == "商品数量")
                width = 10 * 256;
            else if (headName == "省份" || headName == "重量" || headName == "订单金额")
                width = 16 * 256;
            else if (headName == "快递单号" || headName == "收件人姓名" || headName == "发件人" || headName == "发货时间" || headName == "打单时间" || headName == "状态" || headName == "收件人电话")
                width = 20 * 256;
            else if (headName == "快递公司" || headName == "订单编号" || headName == "买家旺旺" || headName == "店铺名称")
                width = 25 * 256;
            else if (headName == "详细地址" || headName == "买家留言" || headName == "卖家备注")
                width = 35 * 256;
            else if (headName == "发货内容")
                width = 45 * 256;
            sheet.SetColumnWidth(index, width);
        }

        private ICellStyle GetHeadStyle(IWorkbook workbook)
        {
            IFont font = workbook.CreateFont();
            font.FontName = "Times New Roman";
            font.Boldweight = short.MaxValue;
            font.FontHeightInPoints = 11;

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(font);
            headerStyle.Alignment = HorizontalAlignment.Center;//内容居中显示
            headerStyle.WrapText = true;
            headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightOrange.Index;
            headerStyle.FillPattern = FillPattern.SolidForeground;
            return headerStyle;
        }

        private ICellStyle GetContentStyle(IWorkbook workbook, HorizontalAlignment alignment)
        {
            IFont font = workbook.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = workbook.CreateCellStyle();
            contentStyle.SetFont(font);
            contentStyle.Alignment = alignment;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.WrapText = true;

            return contentStyle;
        }

        private MemoryStream StreamToMemoryStream(Stream instream)
        {
            MemoryStream outstream = new MemoryStream();
            const int bufferLen = 4096;
            byte[] buffer = new byte[bufferLen];
            int count = 0;
            while ((count = instream.Read(buffer, 0, bufferLen)) > 0)
            {
                outstream.Write(buffer, 0, count);
            }
            return outstream;
        }
        #endregion

        public ActionResult GetTodayPrintedCount()
        {
            var shop = SiteContext.Current.CurrentLoginShop;
            WaybillCodeRequestModel requestModel = new WaybillCodeRequestModel
            {
                ShopId = new List<int> { shop.Id },
                StartDate = DateTime.Now.ToString("yyyy-MM-dd"),
                EndDate = DateTime.Now.ToString("yyyy-MM-dd 23:59:59"),
                PageSize = 1
            };

            var queryModel = new WaybillCodeLoadListModel { needPagging = false, QueryType = WaybillQueryType.OnlyCount };
            var pageModel = _service.LoadList(requestModel, queryModel);
            if (shop.PlatformType == Data.Enum.PlatformType.Jingdong.ToString())
            {
                var pids = new List<string>();
                pageModel?.Rows?.ForEach(t =>
                {
                    if (!string.IsNullOrEmpty(t.OrderIdJoin))
                    {
                        pids.AddRange(t.OrderIdJoin.Split(','));
                    }
                    else
                    {
                        pids.Add(t.OrderId.Trim('C'));
                    }
                    t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
                });
                jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
            }
            return Json(pageModel.Total);
        }

    }
}