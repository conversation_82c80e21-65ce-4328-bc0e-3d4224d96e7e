/// <reference path="CommonModule.js" />
/// <reference path="LodopPrinter.js" />
/// <reference path="FengqiaoPrinter.js" />
/// <reference path="orderlist/OrderTableBuilder.js" />
/// <reference path="CaiNiaoPrinter.js" />

//快递单打印组件
var expressPrinter = (function (ep, common, caiNiao, pdd, lp, fq, jingDong, touTiao, kuaiShou, xiaoHongShu, newXiaoHongShu, tmpModule, otb, $) {

    var printComponents = '';
    var isResend = '';
    ep.SetPrintComponents = function SetPrintComponents(componentName) {
        printComponents = componentName;
    }

    var isIgnoreWaybillRepeat = false; //是否忽略后端订单已打印的拦截
    var ISOrders = null;

    ep.SetIgnoreWaybillRepeat = function (val) { isIgnoreWaybillRepeat = val }
    ep.GetIgnoreWaybillRepeat = function () { return isIgnoreWaybillRepeat }

    //检查当前模板->发件人->默认发件人
    function checkTemplateIsNull() {

        //2.弹框提示
        var showCheckTips = function (isTempNull, isSellerNull) {
            if (isTempNull && isSellerNull) {
                showWarnDailog()
            }
            else if (isTempNull) {
                showWarnDailog(1)
            }
            else {
                showWarnDailog(2)
            }
            function showWarnDailog(type) {
                var checkTemplateIsNullDailog = layer.open({
                    type: 1,
                    title: false, //不显示标题
                    content: $(".checkTemplateIsNullDailog"),
                    area: 550, //宽高
                    skin: 'wu-dailog',
                    success: function () {
                        if (type == 1) {
                            $("#checkTemplateIsNullBtn_template").show()
                            $("#checkTemplateIsNullBtn_SellerInfo").hide()
                        } else if (type == 2) {
                            $("#checkTemplateIsNullBtn_SellerInfo").show()
                            $("#checkTemplateIsNullBtn_template").hide()
                        } else {
                            $("#checkTemplateIsNullBtn_template").show()
                            $("#checkTemplateIsNullBtn_SellerInfo").show()
                        }
                    },
                    btn: false,
                });

            }
        }

        //检查发件人，1.检查是否开启代打地址开了就不检查地址，2.未开启代打就查询当前账号是否添加有发件人
        var checkSellerInfo = function () {
            var isOk = false;
            var isAgentSendAddress = common.LoadCommonSetting("/FenFa/System/Config/IsAgentSendAddress", false, function (rsp) {
                if (rsp.Success && rsp.Data == "true") {
                    isOk = true;
                } else {
                    var sellerInfo = ep.getSellerInfo();
                    isOk = !sellerInfo ? false : true;
                }
            });
            return isOk;
        }

        //1.检查模板是否为空&检查默认发件人》检查当前模板是否有绑定发件人
        var checkTemplate = function () {
            var isTempNull = $('#my_select_expressTemplateWrap>li').length == 0;
            var isSellerNull = !checkSellerInfo();
            if (isTempNull || isSellerNull) {
                showCheckTips(isTempNull, isSellerNull);
                return false;
            }
            var currentTemplate = addTmplInOrderListModule.GetCurrentTemplate();
            if (!currentTemplate) {
                layer.msg('检测到您未选择模板，请先选择模板后再操作打印');
                return false;
            }
            return true;
        }

        return checkTemplate();
    }

    ep.clickCheckTemplateIsNullBtn = function () {
        var url = window.location.href;
        var tar = layer.confirm('是否已添加成功？', {
            icon: 3, title: '确认',
            btn: ['确定', '取消'],
            skin: 'wu-dailog',
            btn1: function (index) {
                window.location.href = url
                //layer.close(tar);
            }, btn2: function () {
                //window.location.href = url
            }, cancel: function () {
                //window.location.href = url
            }
            , zIndex: 100000000
        });

    }

    //获取发件人，按默认字段排序top1查询
    ep.getSellerInfo = function () {
        var sellerInfo;
        common.ajax({
            type: 'post',
            url: '/SellerInfo/GetDefaultSeller',
            async: false,
            success: function (json) {
                if (json.Success) {
                    sellerInfo = json.Data;
                }
            }
        });
        return sellerInfo;
    }

    var sfSdkToken = ''; //顺丰Sdk云打印需要的token,底单重打的时候由于保存的数据token不是最新的，所以每次重打前需要获取最新的token
    ep.SetSfSdkToken = function (token) {
        sfSdkToken = token;
    }

    //检查是否有默认发件人。
    ep.isExistSellerInfo = function (callback) {
        $("#isMoveNotSellerIntoDivId").hide();
        $("#isMoveSellerInfoShopName").html("");
        $("#isOneSellerInfoShopName").hide();
        $("#isOneSellerInfoShopName").html("");
        common.ajax({
            type: 'post',
            url: '/Order/GetDefaultSellerInfo',
            data: {},
            success: function (json) {
                if (json.Success) {
                    var dates = json.Data;
                    if (dates.IsShow) {
                        var list = dates.ListModel;
                        var str = "";
                        if (list.length > 1) {
                            for (var i = 0; i < list.length; i++) {
                                if (list[i].ShopName)
                                    str += list[i].ShopName + " ";
                            }
                            $("#isMoveSellerInfoShopName").html(str);
                            $("#isMoveNotSellerIntoDivId").show();
                        } else {
                            if (list[0].ShopName)
                                str = "店铺（" + list[0].ShopName + "）";
                            else
                                str = "店铺";
                            $("#isOneSellerInfoShopName").show();
                            $("#isOneSellerInfoShopName").html(str);
                        }

                        var addSellerDialog = layer.open({
                            type: 1,
                            title: "添加默认发件人",
                            content: $('#add-defaultseller-div-show'),
                            area: ['1200', '300'], //宽高
                            btn: ['保存', '取消'],
                            success: function () {
                                function selectCallBack(control) {
                                    var deep = control.attr('deep');

                                    if (deep > 1) {
                                        var dataValue = control.attr("data-value");
                                        var isExistsVal = control.find("option[value='" + dataValue + "']").length;
                                        if (isExistsVal > 0)
                                            control.val(dataValue).trigger('change');
                                    }
                                }
                                //加载地址级联选择
                                commonModule.LoadAreaInfoToControl('selDefaultPro', 1, function () {
                                }, selectCallBack, "name");
                                //清空地址识别框
                                $('#txt_raw_addr_default').val('');
                            },
                            cancel: function () {
                                //$('#add-defaultseller-div-show').hide();
                                if (callback)
                                    callback(false);
                                layer.close(addSellerDialog);
                            },
                            yes: function () {
                                AddSellerInfo(addSellerDialog, list);
                                if (callback)
                                    callback(true);
                            },
                            btn2: function () {
                                if (callback)
                                    callback(false);
                                layer.close(addSellerDialog);
                                //$('#add-defaultseller-div-show').hide();
                            }
                        });
                    }
                    else {
                        if (callback)
                            layer.msg("您已经设置了发件人地址信息了。");
                    }
                }
            }
        });
    }

    //默认发件人弹窗
    function AddSellerInfo(addSellerDialog, list) {
        var companyName = $('#txtDefaultCompanyName').val().trim();
        var sellerName = $('#txtDefaultSellerName').val().trim();
        var sellerMobile = $('#txtDefaultSellerMobile').val().trim();
        var sellerPhone = $('#txtDefaultSellerPhone').val().trim();
        //var sellerAddress = $('#txtDefaultSellerAddress').val().trim();

        var sellerProvince = $('#selDefaultPro').val();
        var sellerCity = $('#selDefaultCity').val();
        var sellerArea = $('#selDefaultArea').val();
        var sellerDetailAddr = $('#txtDetailAddr').val().trim();


        if (sellerName == '') {
            layer.msg('请填写发件人姓名.');
            $('#txtDefaultSellerName').focus();
            return;
        }

        if (sellerMobile == '') {
            layer.msg('请填写发件人手机号.');
            $('#txtDefaultSellerMobile').focus();
            return;
        }

        var sellerAddr = '';

        if (sellerProvince && sellerProvince != "0") {
            sellerAddr += sellerProvince;
        }
        else {
            layer.msg('请选择省份.');
            return;
        }

        if (sellerCity && sellerCity != "0") {
            sellerAddr += sellerCity;
        }
        else {
            layer.msg('请选择城市.');
            return;
        }

        if (sellerArea && sellerArea != "0") {
            sellerAddr += sellerArea;
        }
        else {
            layer.msg('请选择区域.');
            return;
        }

        sellerAddr += sellerDetailAddr;

        common.Ajax({
            url: '/SellerInfo/AddDefaultSeller',
            loading: true,
            data: { companyName: companyName, sellerName: sellerName, sellerMobile: sellerMobile, sellerPhone: sellerPhone, sellerAddress: sellerAddr },
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                if (otb) {
                    //重新渲染发件人
                    common.Foreach(otb.rows, function (i, o) {
                        common.Foreach(list, function (x, y) {
                            if (o.ShopId == y.ShopId) {
                                o.SenderName = sellerName;
                                o.SenderPhone = sellerPhone;
                                o.SenderMobile = sellerMobile;
                                o.SenderAddress = sellerAddr;
                                $('#txtSeller_' + o.Id).val(sellerName);
                                $('#txtSeller_phone_' + o.Id).val(sellerMobile);
                                $('#txtSeller_address_' + o.Id).val(sellerAddr);
                                return 'break';
                            }
                        });
                    });
                    otb.refreshColumn('SenderName');
                }
                layer.msg('保存成功');
                layer.close(addSellerDialog);
            }
        });
    }

    //打印前检查模板
    ep.CheckTemplateAvaliable = function (template, printCount, orders, pirntMethod) {
        var needCheck = false;
        if (common.IsNormalTemplate(template.TemplateType) == false && common.IsJdKdTemplate(template.TemplateType) == false
            && template.CpType != 1 && template.CpType != 4 && template.CpType != 5) {
            needCheck = true;
        }

        var batchPlatformOrderIds = "";
        //组合订单数据用来检查库存
        var stockOrders = [];
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var virtualPlatformIds = [];
            for (var j = 0; j < order.OrderTags.length; j++) {
                var tagItem = order.OrderTags[j];
                if (tagItem.Tag == 'EncryptOfflineOrder') {
                    virtualPlatformIds.push(tagItem.TagValue);
                }
            }
            batchPlatformOrderIds += order.PlatformOrderId + ",";
            var model = {
                SourceShopId: order.ShopId,
                UpFxUserId: order.FxUserId,
                SourceFxUserId: order.FxUserId,
                LogicOrderId: order.LogicOrderId,
                PlatformOrderId: order.PlatformOrderId,
                IsAllowNegativeStock: false,
                SendType: order.SendType == undefined ? 0 : order.SendType,//发货类型
                DataFlag: order.DataFlag,
                Items: [],
                DataFlag: order.DataFlag,   //区分冷热数据
                VirtualPlatformId: virtualPlatformIds.join(',') // 关联订单号
            };
            //有新参数Items，用新参数
            if (order.Items != undefined && order.OrderItems.length == order.Items.length) {
                for (var y = 0; y < order.Items.length; y++) {
                    var orderitem = order.Items[y];
                    var item = {
                        OrderItemCode: orderitem.OrderItemCode,
                        LogicOrderItemId: orderitem.OrderItemId,
                        Quantity: orderitem.Quantity
                    };
                    model.Items.push(item);
                }
            }
            else {
                for (var y = 0; y < order.OrderItems.length; y++) {
                    var orderitem = order.OrderItems[y];
                    var item = {
                        OrderItemCode: "",
                        LogicOrderItemId: orderitem
                    };
                    model.Items.push(item);
                }
            }

            stockOrders.push(model);
        };
        //更改为同一方法，后台判断是否检查余额，同时预检查库存数量
        return common.Ajax({
            url: '/TemplateSet/CheckTemplateAvaliable',
            data: {
                templateId: template.Id,
                printCount: printCount,
                needCheck: needCheck,
                orders: stockOrders,
                isResend: isResend,
                pirntMethod: pirntMethod,
                IsContinueForStock: $('#hfIsContinueForStock').val() == "1",  //部分订单项库存不足，是否继续
            },
            //loadingMessage: '检查模板网点及单号余额',
            type: 'POST',
            async: true,
            success: function (rsp) {
                //if (common.IsError(rsp)) {
                //    return;
                //}
                if (rsp.Success == false) {
                    var errorCode = rsp.Message;
                    var errorData = rsp.Data;
                    var errorType = 1;
                    var errorMsg = "";
                    switch (errorCode) {
                        case "1":
                            errorMsg = "模板绑定的电子面单账号未获取到网点，请检查模板绑定的电子面单授权是否过期，或重新授权。<label onclick='commonModule.transferUrl(\"/TemplateSet/\",\"_blank\")' style='color:blue;cursor:pointer;'>去模板列表查看</label>";
                            break;
                        case "2":
                            errorMsg = "模板绑定的电子面单账号网点不存在，请前商家后台，开通电子面单的地方检查网点【" + errorData.BranchName + "】是否已经取消。";
                            break;
                        case "3":
                            errorMsg = "模板绑定的电子面单账号单号余额【" + errorData.Balance + "】不足以打印【" + errorData.PrintCount + "】个单号，请充值单号。";
                            break;
                        case "11"://路径流检查：归属问题
                            errorMsg = "你所勾选的订单中包含不满足打单要求的订单，为保证不多发货 请强制刷新页面并点击同步按钮更新数据后再操作";
                            break;
                        case "12"://路径流检查：解绑处理中
                            try {
                                errorMsg = "存在【解绑处理中】的商家订单，请跟商家确认是否取消了绑定";
                                var removeLogicOrderIds = errorData.split(',');

                                layer.open({
                                    type: 1,
                                    title: "忽略异常订单提示", //不显示标题
                                    content: '<div style="width:500px;height:150px;padding:20px;box-sizing: border-box;"><span style="font-size:16px;line-height:35px">系统检测到您勾选的订单中包含商家处理中解绑代发订单，不满足代发订单打印发货条件，避免打印发货失败，是否暂时忽略该类订单操作？</span></div>',
                                    area: ['500'], //宽高
                                    btn: ['跳过异常订单，继续操作', '取消'],
                                    yes: function () {
                                        layer.closeAll();
                                        cancelSelected(removeLogicOrderIds, "");//取消无货的订单及订单项
                                        $(".express-print-btn").click();//重新提交
                                    },
                                    cancel: function () {
                                        layer.closeAll();
                                    }
                                });
                            }
                            catch (e) {
                                errorMsg = errorData.msg;
                            }
                            break;
                        case "10":
                            try {
                                errorMsg = errorData.msg;

                                var removeLogicOrderIds = JSON.parse(errorData.removeLogicOrderIds);
                                var removeOrderItemIds = JSON.parse(errorData.removeOrderItemIds);

                                layer.open({
                                    type: 1,
                                    title: "确定",
                                    content: '<div class="wu-f14 wu-c09">您所勾选的订单中包含部分商品库存不足，已为您跳过缺货商品，是否继续打印其他商品。</div>',
                                    area: '480px',
                                    btn: ['继续打印', '返回'],
                                    skin: 'wu-dailog',
                                    yes: function () {
                                        layer.closeAll();

                                        cancelSelected(removeLogicOrderIds, removeOrderItemIds);//取消无货的订单及订单项
                                        $('#hfIsContinueForStock').val('1');//部分无货是否继续设为继续
                                        $(".express-print-btn").click();//重新提交
                                    },
                                    cancel: function () {
                                        $('#hfIsContinueForStock').val('0');
                                        layer.closeAll();
                                    }
                                });

                            }
                            catch (e) {
                                errorMsg = errorData.msg;
                            }
                            break;
                        case "err_virtual_order":
                            try {
                                errorMsg = '您勾选的订单包含被商家撤回关联订单的密文线下单，可能导致发货错误。';
                                var removeLogicOrderIds = errorData;
                                layer.open({
                                    type: 1,
                                    title: "打印快递单提示",
                                    content: '<div class="wu-f14 wu-c09">您勾选的订单包含被商家撤回关联订单的密文线下单，可能导致发货错误。</div>',
                                    area: '480px',
                                    btn: ['过滤此类异常线下单，继续打印', '取消打印'],
                                    skin: 'wu-dailog',
                                    btn1: function () {
                                        layer.closeAll();
                                        cancelSelected(removeLogicOrderIds, ""); // 取消异常线下单
                                        $(".express-print-btn").click(); // 重新提交
                                    },
                                    btn2: function () {
                                        layer.closeAll();
                                    }
                                });
                            }
                            catch (e) {
                                errorMsg = '您勾选的订单包含被商家撤回关联订单的密文线下单，可能导致发货错误。';
                            }
                            break;
                        case "wbl_auth_expired":
                            //网点过期全部失败
                            batchPlatformOrderIds = '批次1：' + batchPlatformOrderIds;
                            var waybillDatas = rsp.Data || [];
                            if (waybillDatas.length > 0)
                                waybillDatas[0].BatchPlatformOrderIds = batchPlatformOrderIds.trimEndDgj(',');
                            var data = {
                                Template: template,
                                IsError: true,
                                totalPrintNum: printCount,
                                successCount: 0,
                                errorCount: printCount,
                                WaybillCode: waybillDatas
                            }
                            _showErrorMessage(data);
                            break;
                        default:
                    }

                    if (errorCode != "10" && errorCode != "12" && errorCode != "wbl_auth_expired") {
                        if (errorMsg == "") {
                            layer.alert(rsp.Message, { skin: 'wu-dailog' });
                        }
                        else {
                            if (errorType == 2) {
                                layer.open({
                                    type: 1,
                                    title: false,
                                    closeBtn: 1,
                                    skin: 'layui-layer-rim', //加上边框
                                    area: ['600px', '350px'],
                                    shadeClose: true,
                                    content: errorMsg
                                });
                            }
                            else {
                                if (errorCode != 'err_virtual_order') {
                                    layer.alert(errorMsg, { skin: 'wu-dailog' });
                                }
                            }   
                        }
                    }
                }
                else {
                    commonModule.ServerNowStr = rsp.Data.DateNow;
                    commonModule.DateBatch = rsp.Data.DayPrintBatchNumber;
                    // 保存Branches数据到template对象上，以便后续传递给ExpressPrint接口
                    if (rsp.Data && rsp.Data.Branches) {
                        template.Branches = rsp.Data.Branches;
                    }
                }
            }
        });
    }

    //获取模板类型信息
    function getTemplateTypeInfo(template) {
        var info = {};
        var type = template.TemplateType;
        var company = template.CompanyCode;
        var def = type != null && type != undefined && type != '';
        info.IsWayComBill = def && type == 4; //是否固定模版菜鸟电子面单
        info.IsRMWayComBill = def && (type == 5 || type == 6); //是否组件模版菜鸟电子面单
        info.IsJDkd = def && (type > 80 || type < 90); //是否京东快递
        info.IsWayBill = def && type == 2;//是否菜鸟电子面单除了固定模版
        info.IsWaySite = def && (type == 3 && company != "YUNDA");//是否网点电子面单除了韵达
        info.IsAllWaySite = def && type == 3;//是否网点电子面单
        info.IsWayStoSite = def && (type == 3 && company == "STO");//是否申通网点电子面单
        info.IsAn56WaySite = def && type == 3 && company == "2608021499_235";//是否安能网点电子面单
        info.IsWayYundaSite = def && type == 3 && company == "YUNDA";//是否韵达网点电子面单
        return info;
    }

    //获取模板打印数据（通用方法）
    function getPrintData(orders, template) {
        var request = { Orders: orders, PrintTemplate: template };
        var tinfo = getTemplateTypeInfo(template);
    }

    //取消选择
    function cancelSelected(logicOrderIds, orderItemIds) {
        if (logicOrderIds != null && logicOrderIds != undefined && logicOrderIds != "") {
            for (var i = 0; i < logicOrderIds.length; i++) {
                var logicOrderId = logicOrderIds[i];
                var obj = $(".order-chx[data-pid='" + logicOrderId + "']");
                obj.prop("checked", false);
                orderTableBuilder.CheckOrderBind(obj);
            }
        }

        if (orderItemIds != null && orderItemIds != undefined && orderItemIds != "") {
            for (var i = 0; i < orderItemIds.length; i++) {
                var orderItemId = orderItemIds[i];
                var obj = $(".orderitem-chx[data-id='" + orderItemId + "']");
                obj.prop("checked", false);
                orderTableBuilder.OrderProductItemSeletedHandler(obj, obj.attr("data-parent-id"), orderItemId);
            }
        }
    }

    //打印过的订单，再次打印提示用户
    ep.checkPrinted = function (orders, printMethod, template, isScanPrint) {
        //取出模板bind的打印机（上次打印的打印机）
        var dp = common.GetDefaultPrinter(template.Id, 1);
        var printerName = '';
        if (dp) {
            printerName = dp.PrinterName;
            ep.defaultPrinter = printerName;
        }
        //二次发货不提示再次打印
        if (printMethod == "Resend") {
            return true;
        }
        //打印过的订单,打印提示
        //前提条件是：只有正常（Normal）打印和快运打印才会提示用户，一单多包和新单号打印不会提示用户。
        //1.普通面单/网点面单
        //  提示用户确认，是否重新打印
        //2.菜鸟面单
        //  提示用户确认，是否用原单号打印，或者选择一单多包，新单号打印
        //3.快运面单
        //  快运面单打印过，提示用户先回收单号。重打会报错，不管指定的字母单号数量是否一样。（需验证？？）
        if (printMethod != "NewPrint" || printMethod != "RePrint") {
            var printedWaybillCodes = []; //打印过的单号集合，用于快运提示
            var isExistPrintedOrder = false; //是否存在打印过的订单
            var isExistPrintedWaybillCode = false; //是否找到了打印过的面单
            common.Foreach(orders, function (i, o) {
                var order = isScanPrint ? o : otb.rows[o.Index];
                //order.NoPrintFlag : 表示已打印未更新订单打印标识
                if (order.PrintState == 1 || order.PrintState == 2 || order.NoPrintFlag) {
                    isExistPrintedOrder = true; //订单打印过
                    if (template.TemplateType == 1) {//common.IsNormalTemplate(template.TemplateType)//template.TemplateType == 1 || template.TemplateType == 3 || template.TemplateType == 10) {
                        return 'break';
                    }
                    else {
                        common.Foreach(order.WaybillCodes, function (idx, wc) {
                            if (wc.TemplateId == template.Id || (wc.TemplateType == template.TemplateType && wc.ExpressCpCode == template.ExpressCompanyCode)) {
                                isExistPrintedWaybillCode = true; //找到了打印过的运单号
                                printedWaybillCodes.push(wc.WaybillCode);
                                return 'break';
                            }
                        });
                        if (isExistPrintedWaybillCode && printMethod != 'PrintKuaiYun') return 'break;';
                    }
                }
            });
            if (isExistPrintedOrder == false) {
                return true;
            }

            //弹出用户确认提示
            if (template.TemplateType == 1) { //common.IsNormalTemplate(template.TemplateType)
                //传统面单和网点面单重打提示
                layer.open({
                    type: 1,
                    title: "打印确认", //不显示标题
                    content: $('#div_rePrint_tradition_site'),
                    area: ['560px', '220px'], //宽高
                    id:'div_rePrint_tradition_site_dailog',
                    skin: 'wu-dailog',
                    btn: ['确定重新打印'],
                    yes: function () {
                        //ep.doPrint(orders, template, printMethod, 1, false, printerName);
                        ep.print(orders, printMethod, 1, false);
                    }
                });
            }
            else if (common.IsKuaiYunTemplate(template.TemplateType) || common.IsPddKuaiYunTemplate(template.TemplateType, template.ExpressCompanyCode) || common.IsTouTiaoKuaiYunTemplate(template.TemplateType)) {
                //if (isExistPrintedWaybillCode == true) { //找打了打印过的运单号
                //    //快运面单重打提示
                //    layer.alert("快运面单不支持重复打印。<br/>请先取消已打印过的快运面单【" + printedWaybillCodes.join(',') + "】");
                //}
                //else {
                layer.open({
                    type: 1,
                    title: "打印确认", //不显示标题
                    content: $('#div_rePrint_KuaiYun'),
                    area: ['560px', '260px'], //宽高
                    skin: 'wu-dailog',
                    btn: ['确定重新打印', '新单号打印'],
                    id: 'div_rePrint_KuaiYun_dailog',
                    yes: function () {
                        isIgnoreWaybillRepeat = true; //设置忽略后端订单重打拦截
                        ep.print(orders, printMethod, 1, false);
                    },
                    btn2: function () {
                        ep.print(orders, 'NewPrint', 1, false);
                        return false;
                    }
                });
                //}
            }
            else {
                if (isExistPrintedWaybillCode == true) { //找打了打印过的运单号
                    //取消按钮
                    $('#div_rePrint_cainiao_cancel').unbind('click').bind('click', function () {
                        layer.closeAll();
                    });
                    //数量选择
                    $('#ul_reprint_confirm_count li').bind('click', function () {
                        var li = $(this);
                        $('#lbl_rePrint_count').text(li.text());
                        $('#txt_reprint_confirm_count').val(li.text());
                    });
                    //数量输入
                    $('#txt_reprint_confirm_count').unbind('input').bind('input', function () {
                        var txt = $(this);
                        var val = $.trim(txt.val());
                        if (isNaN(val) == true || val == "0") {
                            val = 1;
                            txt.val(val);
                        }
                        if (val == "") {
                            val = 1;
                        }
                        $('#lbl_rePrint_count').text(val);
                    });
                    //原单号重打
                    $('#div_rePrint_cainiao_rePrint').unbind('click').bind('click', function () {
                        //ep.doPrint(orders, template, printMethod, 1, false, printerName);

                        layer.closeAll();
                        isIgnoreWaybillRepeat = true; //设置忽略后端订单重打拦截

                        ep.print(orders, printMethod, 1, false, undefined, true);

                        //清空用户选择
                        $(".sureAgainPrint_main_down").hide();
                        $('#ul_reprint_confirm_count li').first().click();

                    });
                    //新单号打印
                    $('#div_rePrint_newId_reprint').unbind('click').bind('click', function () {
                        //ep.doPrint(orders, template, "NewPrint", $('#lbl_rePrint_count').text(), false, printerName, function () {
                        //    $(".sureAgainPrint_main_down").hide();
                        //    $('#ul_reprint_confirm_count li').first().click();
                        //});

                        layer.closeAll();

                        ep.print(orders, "NewPrint", $('#lbl_rePrint_count').text(), false);

                        //清空用户选择
                        $(".sureAgainPrint_main_down").hide();
                        $('#ul_reprint_confirm_count li').first().click();

                    });
                    //菜鸟面单重打提示
                    layer.open({
                        type: 1,
                        //skin: 'layui-layer-rim', //加上边框
                        title: "打印确认",
                        btn: null,
                        area: ['560px', '280px'],
                        skin: 'wu-dailog',
                        content: $('#div_rePrint_cainiao'),
                        id: 'div_rePrint_cainiao_dailog',
                        cancel: function () { }
                    });
                }
                else {
                    layer.open({
                        type: 1,
                        title: "打印确认", //不显示标题
                        content: $('#div_rePrint_tradition_site'),
                        area: ['560px', '220px'], //宽高
                        skin: 'wu-dailog',
                        btn: ['确定重新打印'],
                        id: 'div_rePrint_tradition_site_dailog',
                        yes: function () {
                            isIgnoreWaybillRepeat = true; //设置忽略后端订单重打拦截
                            ep.print(orders, printMethod, 1, false);
                        }
                    });
                }
            }
            return false;
        }
        else {
            return true;
        }
    }

    //打印前检查
    //confirmPddDoor 针对跨境单快递上门揽件是否继续
    ep.check = function (orders, template, checkTemplateIsNull, printMethod, printCount, isCheckPrinted, isByHand, isScanPrint, confirmPddDoor, isPrimaryCode) {
        //1.根据模板类型确认检查菜鸟打印组件还是Lodop打印组件
        //TemplateType=1 普通面单   （有背景图的自定义模板）
        //TemplateType=3 自画的网点电子面单模版    (IsWaySite)
        //2/4/5/6 都是菜鸟模板
        //TemplateType=2 菜鸟模版由系统定制的 (IsWayBill)
        //TemplateType=4 菜鸟原始固定模版   (IsWayComBill)--已经废弃掉？
        //TemplateType=5和6 菜鸟最新的模版   (IsRMWayComBill)
        if (!orders || orders.length == 0) {
            if (otb.isSelectOrder()) {
                layer.alert("请选择您要打印的商品", { title: '提示', skin: 'wu-dailog' });
            }
            else {
                layer.alert("请选择您要打印的订单", { title: '提示', skin: 'wu-dailog' });
            }
            return false;
        }
        //检查模板&默认发件人
        if (typeof checkTemplateIsNull == 'function') {
            if (!checkTemplateIsNull()) return false;
        }

        //aliC2M 限制只能用菜鸟订单
        if (commonModule.CurrShop.PlatformType == "AlibabaC2M"
            && common.IsCainiaoTemplate(template.TemplateType) == false
            && common.IsLinkTemplate(template.TemplateType) == false
            && common.IsLinkKuaiYunTemplate(template.TemplateType) == false) {
            layer.alert("淘宝平台要求，淘工厂订单只支持菜鸟电子面单打印，请更换菜鸟模板打印！", { skin: 'wu-dailog' });
            return false;
        }

        if (commonModule.CurrShop.PlatformType == "TaobaoMaiCaiV2"
            && common.IsCainiaoTemplate(template.TemplateType) == false
            && common.IsLinkTemplate(template.TemplateType) == false
            && common.IsLinkKuaiYunTemplate(template.TemplateType) == false) {
            layer.alert("淘宝平台要求，淘宝买菜(新)订单只支持菜鸟电子面单打印，请更换菜鸟模板打印！", { skin: 'wu-dailog' });
            return false;
        }

        //拼多多厂家代打，只能使用自己的电子面单账号打印
        if (commonModule.IsPddFds()) {
            if (commonModule.IsPddTemplate(template.TemplateType) == false || commonModule.IsPddKuaiYunTemplate(template.TemplateType) == false) {
                layer.alert("拼多多代发订单，只支持拼多多电子面单打印，请更换拼多多模板打印。", { skin: 'wu-dailog' });
                return false;
            }
            if (template.AuthSourceType != "1" && template.CaiNiaoAuthInfoId != common.CurrShop.Id) {
                layer.alert("拼多多代发订单，只能使用自己账号开通的拼多多电子面单打印,请更换绑定自己电子面单账号的模板！", { skin: 'wu-dailog' });
                return false;
            }
        }

        //是否自由打印
        //if (commonModule.isCustomerOrder() && (commonModule.IsTouTiaoTemplate(template.TemplateType) || commonModule.IsTouTiaozjTemplate(template.TemplateType))) {
        //    layer.alert('抖音电子面单刚上线不久，目前<font color="red"><b>只能打印</b></font>【抖店】订单，暂不支持其他平台订单，抖音官方团队正在完善中。', { title: "抖音电子面单使用提示", icon: 7 });
        //    return false;
        //}

        var templateType = template.TemplateType;
        //检查模板类型为2的模板，由于菜鸟做了电子面单一致性改造，接口返回的数据加了密，自己维护的类型为2的模板，打印不了了
        if (templateType == 2) {
            var urlstr = common.rewriteUrlToMainDomain('/TemplateSet/');
            layer.alert("您好，由于菜鸟官方对电子面单信息一致性做了校验，数据进行了加密，所以自己维护的菜鸟模板将取不到集包地等信息，请使用标准的菜鸟模板进行打印。<br/> <a href=" + urlstr + " style='color:blue;'>去模板管理>></a>", { skin: 'wu-dailog' });
            return false;
        }

        //勾选订单验证
        var riskCtrlOrder = []; //拼多多风控订单
        var sendSFOrder = []; //拼多多顺丰加价订单
        var touTiaoCodOrder = []; //头条系代收货款订单
        var IsNoOwnOrder = true; //是否抖音平台的订单
        var pddDoorOrders = [];//拼多多快递上门揽收
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var rec = order.Receiver;
            var buyerName = order.Buyer.BuyerWangWang;
            if (!buyerName)
                buyerName = rec.ToName;
            //验证收件人
            //if (templateType != 1 && (!order.WaybillCode || order.WaybillCode == "" || order.WaybillCode.trim() == "")) {
            //    layer.alert("买家【" + buyerName + "】的快递单号不能为空");
            //    return false;
            //}

            //检查发件人
            var rowOrder = orderTableBuilder.rows[order.Index];
            //if (rowOrder)
            //{
            //    //检查省市区是否为空
            //    if (!rowOrder.ToProvince || !rowOrder.ToCity || !rowOrder.ToCounty)
            //        layer.alert("检查到订单【" + order.PlatformOrderId + "】收件人省市区不合法");
            //}

            if (order.PlatformType == "Pinduoduo" && order.IsWeiGong == true) {
                riskCtrlOrder.push(order.PlatformOrderId);
            }

            if (order.PlatformType == "Pinduoduo" && (order.ExtField1 == "1" || IsChildSendSF(order))) {
                sendSFOrder.push(order.PlatformOrderId);
            }
            if (order.PlatformType == "Pinduoduo" && order.IsPddCourierDoorToDoorCollect) {
                pddDoorOrders.push(order.PlatformOrderId);
            }

            if (order.PlatformType == "TouTiao" && order.TradeType == "0") {
                touTiaoCodOrder.push(order.PlatformOrderId);
            }

            if (IsNoOwnOrder && order.PlatformType != "TouTiao" && (commonModule.IsTouTiaoTemplate(template.TemplateType) || commonModule.IsTouTiaozjTemplate(template.TemplateType))) {
                IsNoOwnOrder = false;
            }

            //拼多多厂家代打订单，取单号不需要收件人信息
            if (common.IsPddFds() == false) {
                if (order.ReceiverIsEmpty == 1) {

                    layer.open({
                        type: 1,
                        title: "异常提醒",
                        content: $('#aialog_receiver_empty_show'),
                        area: ['480px'], //宽高
                        skin: 'wu-dailog',
                        btn: ['继续操作', '取消'],
                        btn1: function () {
                            layer.closeAll();
                            orderTableBuilder.FilterReceiverEmptyOrder();//过滤收件人为空的订单
                            $(".express-print-btn").click();//重新提交
                            return true;
                        },
                        btn2: function () {
                            layer.closeAll();
                            return false;
                        }
                    });

                    return false;
                }

                if (!rec || !rec.ToName || rec.ToName.trim(" ") == "") {
                    layer.alert("系统编号【" + order.PlatformOrderId + "】的收件人不能为空", { skin: 'wu-dailog' });
                    return false;
                }
                if (!rec.ToMobile || rec.ToMobile.trim(" ") == "") {
                    layer.alert("系统编号【" + order.PlatformOrderId + "】的收件人联系电话不能为空", { skin: 'wu-dailog' });
                    return false;
                }
                if (!rec.ToAddress || rec.ToAddress.trim(" ") == "") {
                    layer.alert("系统编号【" + order.PlatformOrderId + "】的收件地址不能为空", { skin: 'wu-dailog' });
                    return false;
                }
            }
            //拼多多跨境快递上门揽件
            if (pddDoorOrders.length > 0 && !confirmPddDoor) {

                //var html = "系统检测到您勾选的订单包含不建议手动打单发货的订单，请问是否听从拼多多平台建议，忽略操作该类订单？<br/>拼多多跨境托管单 - 快递上门揽收类的订单不建议自行打印(如需打印，优先顺丰快递”您只需准备包裹快递上门揽收成功后，订单状态会自动变更为已发货状态";
                var html = "";
                html += '<div style="padding:15px;">';
                html += '<div style="font-size: 16px;margin-bottom: 10px;display:flex"><i class="layui-layer-ico layui-layer-ico1" style="background-position: -90px 0;width: 30px;height: 30px;display: block;margin-right:5px;"></i>';
                html += '<span style="flex:1">系统检测到您勾选的订单包含不建议手动打单发货的订单，请问是否听从拼多多平台建议， 忽略操作该类订单?</span></div>';
                html += '<div style="display: flex;flex-direction: column;color: #ec9512;padding:8px 25px 0 35px;line-height:20px;font-size:16px">'
                html += '<div>拼多多跨境托管单-快递上门揽收类的订单不建议</div>';
                html += '<div style="">自行打印(如需打印，优先顺丰快递)</div>';
                html += '<div style="">您只需准备包裹快递上门揽收成功后，</div>';
                html += '<div style="">订单状态会自动变更为已发货状态</div>';
                html += '</div>';
                html += '</div>';


                var dialogDoorTips = layer.open({
                    type: 1,
                    title: "忽略打印提示",
                    content: html,
                    btn: ["忽略该类订单继续操作", "继续操作所有订单"],
                    shadeClose: true,
                    area: ['480px'],
                    btn1: function () {
                        layer.closeAll();
                        //过滤订单后，重新发起
                        orderTableBuilder.FilterPddDoorOrder();
                        $(".express-print-btn").click();
                        return true;
                    },
                    btn2: function () {
                        layer.close(dialogDoorTips);
                        ep.startPrint(orders, printMethod, printCount, isCheckPrinted, true, isPrimaryCode);
                        return true;
                    }
                });
                return false;
            }

            //var sender = order.Sender;
            //if (!sender || !sender.SenderName || sender.SenderName.trim(" ") == "") {
            //    layer.alert("买家【" + buyerName + "】的发件人不能为空");
            //    return false;
            //}
            //if (!sender.SenderPhone || sender.SenderPhone.trim(" ") == "") {
            //    layer.alert("买家【" + buyerName + "】的发件人联系电话不能为空");
            //    return false;
            //}
            //if (!sender.SenderAddress || sender.SenderAddress.trim(" ") == "") {
            //    layer.alert("买家【" + buyerName + "】的发件地址不能为空");
            //    return false;
            //}

            //var sender = order.Sender;
            //if (!sender || !sender.SenderName || sender.SenderName.trim(" ") == "" || !sender.SenderPhone || sender.SenderPhone.trim(" ") == "" || !sender.SenderAddress || sender.SenderAddress.trim(" ") == "") {
            //    //layer.alert("买家【" + buyerName + "】的发件地址不能为空");
            //    var hideOrShowElement = function (display) {
            //        if (display == "none") {
            //            //隐藏不需要的项
            //            $('#li_CompanyName').hide();
            //            $('#li_seller_phone').hide();
            //            $('#li_seller_address_discern').hide();
            //        }
            //        else {
            //            //还原显示不需要的项
            //            $('#li_CompanyName').show();
            //            $('#li_seller_phone').show();
            //            $('#li_seller_address_discern').show();
            //        }
            //    }
            //    //无发件人，则弹出设置发件人
            //    var addSellerInfLayer = layer.open({
            //        type: 1,
            //        title: "添加发货地址",
            //        content: $('#div_add_sellerInfo'),
            //        area: ['800'], //宽
            //        btn: ['保存', '取消'],
            //        success: function () {
            //            //隐藏不需要的项
            //            hideOrShowElement("none");

            //            function selectCallBack(control) {
            //                var deep = control.attr('deep');

            //                if (deep > 1) {
            //                    var dataValue = control.attr("data-value");
            //                    var isExistsVal = control.find("option[value='" + dataValue + "']").length;
            //                    if (isExistsVal > 0)
            //                        control.val(dataValue).trigger('change');
            //                }
            //            }

            //            //加载地址级联选择
            //            commonModule.LoadAreaInfoToControl('sellerProvince-select', 1, function () {
            //            }, selectCallBack, "name");

            //        },
            //        end: function () {
            //            //还原显示不需要的项
            //            hideOrShowElement("block");
            //        },
            //        yes: function () {
            //            //保存

            //            var companyName = $('#txtCompanyName').val().trim();
            //            var sellerName = $('#txtSellerName').val().trim();
            //            var sellerMobile = $('#txtSellerMobile').val().trim();
            //            var sellerPhone = $('#txtSellerPhone').val().trim();

            //            var sellerProvince = $('#sellerProvince-select').val();
            //            var sellerCity = $('#sellerCity-select').val();
            //            var sellerArea = $('#sellerArea-select').val();
            //            var sellerDetailAddr = $('#txtSellerDetailAddr').val().trim();


            //            if (sellerName == '') {
            //                layer.msg('请填写发件人姓名.');
            //                return false;
            //            }

            //            if (sellerMobile == '') {
            //                layer.msg('请填写发件人手机号.');
            //                return false;
            //            }

            //            var sellerAddr = '';

            //            if (sellerProvince && sellerProvince != "0") {
            //                sellerAddr += sellerProvince;
            //            }
            //            else {
            //                layer.msg('请选择省份.');
            //                return false;
            //            }

            //            if (sellerCity && sellerCity != "0") {
            //                sellerAddr += sellerCity;
            //            }
            //            else {
            //                layer.msg('请选择城市.');
            //                return false;
            //            }

            //            if (sellerArea && sellerArea != "0") {
            //                sellerAddr += sellerArea;
            //            }
            //            else {
            //                layer.msg('请选择区域.');
            //                return false;
            //            }

            //            if (sellerDetailAddr == '') {
            //                layer.msg('请填写发件人地址.');
            //                return false;
            //            }

            //            sellerAddr += sellerDetailAddr;

            //            commonModule.Ajax({
            //                url: '/SellerInfo/AddDefaultSeller',
            //                loading: true,
            //                data: {
            //                    companyName: companyName,
            //                    sellerName: sellerName,
            //                    sellerMobile: sellerMobile,
            //                    sellerPhone: sellerPhone,
            //                    sellerAddress: sellerAddr
            //                },
            //                type: 'POST',
            //                success: function (rsp) {
            //                    if (rsp.Success == false) {
            //                        layer.msg(rsp.Message, { icon: 2 });
            //                        return;
            //                    }
            //                    //自动填充发件人信息到订单列表
            //                    if (window.orderTableBuilder && orderTableBuilder.rows && orderTableBuilder.rows.length > 0) {
            //                        commonModule.Foreach(orderTableBuilder.rows, function (i, o) {
            //                            //只填充当前店铺的发件人为空的订单 o.ShopId == commonModule.CurrShop.Id &&
            //                            if ((!o.SenderName || (!o.SenderMobile && !o.SenderPhone) || !o.SenderAddress)) {
            //                                //完善数据
            //                                o.SenderName = sellerName;
            //                                o.SenderPhone = sellerPhone;
            //                                o.SenderMobile = sellerMobile;
            //                                o.SenderAddress = sellerAddr;
            //                                o.IsManualUpdateSeller = true;
            //                                //重新渲染行
            //                                orderTableBuilder.refreshRow(o);
            //                            }
            //                        });
            //                    }

            //                    //填充打印的数据
            //                    commonModule.Foreach(orders, function (i, o) {
            //                        var sender = o.Sender;
            //                        if (!sender || !sender.SenderName || sender.SenderName.trim(" ") == "" || !sender.SenderPhone || sender.SenderPhone.trim(" ") == "" || !sender.SenderAddress || sender.SenderAddress.trim(" ") == "") {
            //                            o.Sender = {
            //                                SenderName: sellerName,
            //                                SenderPhone: (sellerMobile ? sellerMobile : sellerPhone),
            //                                SenderAddress: sellerAddr
            //                            };
            //                        }
            //                    });

            //                    //关闭此窗口，显示提示窗口
            //                    layer.close(addSellerInfLayer);

            //                    //继续打印流程
            //                    if (isScanPrint) {
            //                        ep.scanPrint(orders, template, printCount, isByHand, isCheckPrinted);
            //                    }
            //                    else {
            //                        ep.startPrint(orders, printMethod, printCount, isCheckPrinted);
            //                    }
            //                }
            //            });
            //        },
            //        btn2: function () {
            //            //取消

            //            //还原显示不需要的项
            //            hideOrShowElement("block");
            //            //关闭此窗口，显示提示窗口
            //            layer.close(addSellerInfLayer);
            //        }
            //    });
            //    return false;
            //}

            var oi = order.OrderItems;
            //检查订单项是否有勾选
            if (oi.length == 0 && !common.isCustomerOrder()) {
                layer.alert("系统编号【" + order.PlatformOrderId + "】中的商品没有勾选，请展开勾选", { skin: 'wu-dailog' });
                return false;
            }

            //重新触发-设置打印内容
            order.PrintInfo = orderTableBuilder.OrderProductItemSetPrintInfo(order.Id);

        }

        if (touTiaoCodOrder.length > 0
            && common.IsCainiaoTemplate(templateType) == false //菜鸟
            && common.IsKuaiYunTemplate(templateType) == false //菜鸟快运
            && common.IsLinkTemplate(templateType) == false    //link 
            && common.IsLinkKuaiYunTemplate(templateType) == false    //link 
            && common.IsPddTemplate(templateType) == false     //pdd 
            && common.IsJdzjTemplate(templateType) == false     //jingdong
            && common.IsPddKuaiYunTemplate(template.TemplateType, template.ExpressCompanyCode) == false //拼多多快运
            && templateType != 10 && templateType != 13) { //丰桥 丰桥云模板
            layer.alert("选中的订单【" + touTiaoCodOrder.join(",") + "】为代收货款订单，请更换菜鸟或拼多多模板打印。", { skin: 'wu-dailog' });
            return false;
        }

        if (riskCtrlOrder.length > 0) {
            layer.alert("订单【" + riskCtrlOrder.join(",") + "】风控中，不允许打印面单！<label style='color:red'>当风控解除后，请及时发货。</label>", { skin: 'wu-dailog' });
            return false;
        }

        if (sendSFOrder.length > 0 && template.ExpressCompanyCode != "SF") {
            if (sendSFOrder.length == orders.length) {
                layer.alert("所选订单为“加价发顺丰”订单，请使用顺丰快递打印面单。", { skin: 'wu-dailog' });
                return false;
            }
            else {
                layer.alert("订单【" + sendSFOrder.join(",") + "】为“加价发顺丰”订单，请使用顺丰快递打印面单。", { skin: 'wu-dailog' });
                return false;
            }
        }

        //if (!IsNoOwnOrder) {
        //    layer.alert('抖音电子面单刚上线不久，目前<font color="red"><b>只能打印</b></font>【抖店】订单，暂不支持其他平台订单，抖音官方团队正在完善中。', { title: "抖音电子面单使用提示", icon: 7 });
        //    return false;
        //}

        //检查打印组件是否准备好
        var isReady = false;
        if (common.IsCainiaoTemplate(templateType) || common.IsLinkTemplate(templateType) || common.IsLinkKuaiYunTemplate(templateType))//(templateType > 3 && templateType < 20) || templateType == 2)
            isReady = caiNiao.check(true);
        else if (common.IsPddTemplate(templateType) || common.IsPddKuaiYunTemplate(templateType))//(templateType > 20 && templateType < 40)
            isReady = pdd.check(true); //TODO:检查拼多多组件是否启动 
        else if (common.IsJdzjTemplate(templateType) || common.IsJdWjzjTemplate(templateType))
            isReady = jingDong.check(true);
        else if (common.IsTouTiaozjTemplate(templateType) || common.IsTouTiaoKuaiYunTemplate(templateType))
            isReady = touTiao.check(true);
        else if (common.IsKuaiShouTemplate(templateType))
            isReady = kuaiShou.check(true);
        else if (common.IsXiaoHongShuTemplate(templateType)) 
            isReady = xiaoHongShu.check(true);
        else if (common.IsNewXiaoHongShuTemplate(templateType))
            isReady = newXiaoHongShu.check(true);
        else if (common.IsWxVideoTemplate(templateType))
            isReady = wxVideoPrinter.check(true);
        else
            isReady = lp.check(true);
        return isReady;
    }

    //打印时针对不同平台不同电子面单拦截提醒
    ep.CheckPlatformType = function (templateType, orders) {
        var PlatformTypes = [];
        for (var index in orders) {
            var platformtype = orders[index].PlatformType;
            PlatformTypes.push(platformtype);
        }
        var templatechecktype = 99;
        if (common.IsCainiaoTemplate(templateType) || common.IsKuaiYunTemplate(templateType) || common.IsLinkTemplate(templateType) || common.IsLinkKuaiYunTemplate(templateType)) {
            templatechecktype = 1; //菜鸟
        } else if (common.IsNormalTemplate(templateType)) {
            templatechecktype = 4; //网点
        } else if (common.IsPddTemplate(templateType) || common.IsPddKuaiYunTemplate(templateType)) {
            templatechecktype = 2; //拼多多
        } else if (common.IsJdWjTemplate(templateType) || common.IsJdKdTemplate(templateType)) {
            templatechecktype = 5; //京东
        } else if (common.IsKuaiShouTemplate(templateType)) {
            templatechecktype = 6; //快手
        } else if (common.IsXiaoHongShuTemplate(templateType) || common.IsNewXiaoHongShuTemplate(templateType)) {
            templatechecktype = 7; //小红书
        } else if (common.IsWxVideoTemplate(templateType)) {
            templatechecktype = 8; //视频号
        }
        var msg = "";
        var helpHref = "";
        var helpTitle = "";
        var returnType = { templatechecktype: templatechecktype, msg: "", pt: "none" };
        switch (templatechecktype) {
            //菜鸟电子面单
            case 1:
                if (PlatformTypes.indexOf("Pinduoduo") > -1) {
                    msg = "菜鸟电子面单不能打印拼多多店铺订单，如需打印，请使用拼多多电子面单或者网点面单！";
                    helpHref = "https://www.dgjapp.com/newHelpsShow.html?id=1582370513430";
                    helpTitle = "立即开通拼多多电子面单";
                    returnType.pt = "pdd";
                } else if (PlatformTypes.indexOf("Jingdong") > -1 || PlatformTypes.indexOf("JingdongPurchase") > -1) {
                    msg = "菜鸟电子面单不能打印京东店铺订单，如需打印，请使用京东快递或者京东无界电子面单！";
                    helpHref = "https://www.dgjapp.com/newHelpsShow.html?id=1581575497880";
                    helpTitle = "立即开通京东电子面单";
                    returnType.pt = "jd";
                } else if (PlatformTypes.indexOf("TouTiao") > -1) {
                    msg = "";
                }
                break;
            //拼多多电子面单
            case 2:
                if (PlatformTypes.indexOf("Taobao") > -1) {
                    //msg = "拼多多电子面单不能打印淘系平台（淘宝、阿里等）店铺订单，如需打印，请使用淘宝菜鸟电子面单！";
                    msg = "拼多多电子面单不能打印淘系平台店铺订单，如需打印，请使用淘宝菜鸟电子面单！";
                    helpHref = "https://www.dgjapp.com/newHelpsShow.html?id=1576138418389";
                    helpTitle = "立即开通菜鸟电子面单";
                    returnType.pt = "tb";
                }
                else if (PlatformTypes.indexOf("Jingdong") > -1 || PlatformTypes.indexOf("JingdongPurchase") > -1) {
                    msg = "拼多多电子面单不能打印京东店铺订单，如需打印，请使用京东快递或者京东无界电子面单！";
                    helpHref = "https://www.dgjapp.com/newHelpsShow.html?id=1581575497880";
                    helpTitle = "立即开通京东无界电子面单";
                    returnType.pt = "jd";
                }
                break;
            //头条电子面单
            case 3:
                break;
            //网点面单
            case 4:
                break;
            //京东无界电子面单
            case 5:
                if (PlatformTypes.indexOf("Taobao") > -1 || PlatformTypes.indexOf("Alibaba") > -1) {
                    msg = "京东无界电子面单不能打印淘系平台（淘宝、阿里等）店铺订单，如需打印，请使用淘宝菜鸟电子面单！";
                    helpHref = "https://www.dgjapp.com/newHelpsShow.html?id=1576138418389";
                    helpTitle = "立即开通淘宝菜鸟电子面单";
                    returnType.pt = "tb";
                } else if (PlatformTypes.indexOf("Pinduoduo") > -1) {
                    msg = "京东无界电子面单不能打印拼多多店铺订单，如需打印，请使用拼多多电子面单或者网点面单！";
                    helpHref = "https://www.dgjapp.com/newHelpsShow.html?id=1581575497880";
                    helpTitle = "立即开通拼多多电子面单或者网点面单";
                    returnType.pt = "pdd";
                }
                break;
            //快手电子面单
            case 6:
                break;
            //小红书电子面单
            case 7:
                break;
            //微信视频号电子面单
            case 8:
                break;
            default:
                break;
        }
        returnType.msg = msg;
        returnType.helpHref = helpHref;
        returnType.helpTitle = helpTitle;
        return returnType;
    }

    function IsChildSendSF(order) {
        var result = false;
        if (order.PlatformOrderId[0] == "C") {
            var row = orderTableBuilder.rows[order.Index];
            common.Foreach(row.SubOrders, function (i, subo) {
                if (subo.ExtField1 == "1") {
                    result = true;
                }
            });
        }
        return result;
    }

    //打印快递单，参数 orders:选择的订单，

    //预览
    ep.preView = function () {

    }
    //printMethod 打印方式
    //printcount 打印数量
    //isCheckPrinted 是否检查已打印过
    //confirmPddDoor 针对跨境单快递上门揽件是否继续
    //isPrimaryCode 是否原单号打印
    var noPrintFlagIdsDailog = null;
    ep.print = function print(orders, pirntMethod, printCount, isCheckPrinted, confirmPddDoor, isPrimaryCode) {
        if (pirntMethod == "Resend") {
            isResend = "1";
            //检查是否是已经发货了的订单
            var errorCount = 0;
            for (var i = 0; i < orders.length; i++) {
                var order = orders[i];
                var index = order.Index;
                var o = otb.rows[index];
                if (o.ErpState != "sended")
                    errorCount++;
                if (o.WaybillCodes && o.WaybillCodes.length > 0) {
                    var wc = o.WaybillCodes[0];
                    order.WaybillCode = wc.WaybillCode;
                    order.ExpressCompanyCode = wc.CompanyCode;
                }
            }
            if (errorCount > 0) {
                layer.alert("您选择的订单不是已发货订单，请选择已发货订单进行二次发货。", { btn: ["知道了"], skin: 'wu-dailog' });
                return false;
            }
        } else {
            isResend = "";
        }
        console.time('打印数据检查前:');
        console.log(orders);
        var template = addTmplInOrderListModule.GetCurrentTemplate()
        var isPinduoduo = common.PlatformType == "Pinduoduo" || (orders && orders.length > 0 && orders[0].PlatformType == "Pinduoduo");
        var isJingdong = common.PlatformType == "Jingdong" || (orders && orders.length > 0 && orders[0].PlatformType == "Jingdong");
        var isXhs = common.PlatformType == "XiaoHongShu" || (orders && orders.length > 0 && orders[0].PlatformType == "XiaoHongShu");
        var isTouTiao = common.PlatformType == "TouTiao" || (orders && orders.length > 0 && orders[0].PlatformType == "TouTiao");
        var isAlibaba = common.PlatformType == "Alibaba" || (orders && orders.length > 0 && orders[0].PlatformType == "Alibaba");
        var isTaobao = common.PlatformType == "Taobao" || (orders && orders.length > 0 && orders[0].PlatformType == "Taobao");
        var ck = $.cookie("pdd-send-tips-no-show");

        //3.打印
        var doPrint = function () {
            if (isPinduoduo && template != null && !common.IsPddTemplate(template.TemplateType) && !common.IsPddKuaiYunTemplate(template.TemplateType, template.ExpressCompanyCode) && !ck) {
                var dialogIndex = layer.open({
                    type: 1,
                    title: "请确认",
                    content: "<div style='font-size:14px;line-height:20px;margin:25px;'><div><p style='margin:5px;'>根据拼多多官方改造要求，5月20号起拼多多店铺订单只支持使用拼多多电子面单打单发货，请及时更换拼多多电子面单打印。</p><p style='margin:5px;'>使用教程：<a style='color:#2ebae9;' target='_blank' href='https://www.dgjapp.com/newHelpContentPc.html?id=5cbe6ccb33088732d493806c'>前往查看</a></p></div></div>",
                    btn: ["继续打印", "不再提示，继续打印", "取消"],
                    shadeClose: true,
                    area: ['550px', '250px'],
                    btn1: function () {
                        layer.close(dialogIndex);
                        ep.startPrint(orders, pirntMethod, printCount, isCheckPrinted, confirmPddDoor, isPrimaryCode);
                        return true;
                    },
                    btn2: function () {
                        layer.close(dialogIndex);
                        $.cookie("pdd-send-tips-no-show", 1, { expires: 30 });
                        ep.startPrint(orders, pirntMethod, printCount, isCheckPrinted, confirmPddDoor, isPrimaryCode);
                        return true;
                    },
                    btn3: function () {
                        return true;
                    }
                });
            }
            else {
                ep.startPrint(orders, pirntMethod, printCount, isCheckPrinted, confirmPddDoor, isPrimaryCode);
            }
        }

        //2.5 检查淘宝 清仓订单
        var doPrintBeforeCheckTaobao = function () {
            var isShow = localStorage.getItem('taobao-clearance_order-tip_' + commonModule.CurrShop.Id);
            if (isTaobao && template != null && !isShow) {
                var hasClearanceOrder = [];
                $(orders).each(function (i, o) {
                    var hasClearanceTag = commonModule.HasTag(o.OrderTags, 'clearance_order', 'Order')
                    if (hasClearanceTag) {
                        hasClearanceOrder.push(o);
                    }
                });
                if (hasClearanceOrder.length > 0) {
                    var htmlContent = "<div style='display: flex; flex-direction: column; color: #191919;font-size: 14px;'>"
                    htmlContent += "<span>您所勾选的订单内包含清仓商品（瑕疵商品）发货，请留意拣货商品。</span>"
                    htmlContent += "<span style='display: flex; align-items: center; margin-top: 16px;'><span>说明：</span>";
                    htmlContent += "<a style='color: #2020ff;' target='_blank' href='https://helpcenter.taobao.com/servicehall/knowledge_detail?spm=a21dvs.24047116.0.0.52d21130Sol4Ep&kwdContentId=594182551872171008&hcSessionId=3-1281-bbc806a1-a40a-41c3-961b-a83aa6483844'>什么是清仓商品？</a></span>";
                    htmlContent += "<div style='display: flex; align-items: center; margin-top: 24px;justify-content: space-between;'>"
                    htmlContent += "<span id='clearance_order-checkbox' style='display: flex; align-items: center; cursor: pointer;'><span class='n-newCheckbox'></span><span>不再提示</span></span>"
                    htmlContent += "<span style='display: flex; align-items: center;'><span id='clearance_order-btn1' class='n-mButton n-sActive'>取消</span><span class='n-mButton' id='clearance_order-btn2' style='margin-left: 12px;'>继续打印发货</span></span></div></div>"

                    var dialogIndex = layer.open({
                        type: 1,
                        title: "提示",
                        content: htmlContent,
                        btn: false,
                        shadeClose: true,
                        skin: 'n-skin',
                        area: ['550', '250'],
                        success: function () {
                            $("#clearance_order-checkbox").on("click", function () {
                                if ($(this).find(".n-newCheckbox").hasClass("activeF")) {
                                    $(this).find(".n-newCheckbox").removeClass("activeF");
                                } else {
                                    $(this).find(".n-newCheckbox").addClass("activeF");
                                }
                            });

                            $("#clearance_order-btn1").on("click", function () {
                                layer.close(dialogIndex);
                            });

                            $("#clearance_order-btn2").on("click", function () {
                                layer.close(dialogIndex);
                                var isSetShow = $("#clearance_order-checkbox").find(".n-newCheckbox").hasClass("activeF");
                                if (isSetShow) {
                                    localStorage.setItem('taobao-clearance_order-tip_' + commonModule.CurrShop.Id, 1);
                                }
                                doPrint();
                                return true;
                            });

                        },
                        end: function () {
                            $("#clearance_order-checkbox").off();
                            $("#clearance_order-btn1").off();
                            $("#clearance_order-btn2").off();
                        }
                    });
                } else {
                    doPrint();
                }
            }
            else {
                doPrint();
            }
        }

        //2.4 检查抖店 风控订单
        var doPrintBeforeCheckTouTiaoRisk = function () {
            if (isTouTiao && template != null) {
                var hasRiskProcessingOrders = [];
                $(orders).each(function (i, o) {
                    if (commonModule.HasTag(o.OrderTags, 'risk_processing', 'OrderItem')) {
                        hasRiskProcessingOrders.push(o);
                    }
                });
                if (hasRiskProcessingOrders.length > 0) {
                    var msgHtml = '<div style="font-size:12px;line-height:20px;padding:15px;">';
                    msgHtml += '部分订单暂被平台标记为风控订单，24 小时内平台会返回风控结果，风控订单不支持打印发货。是否跳过该类订单继续打印正常订单？';
                    msgHtml += '</div>';
                    var dialogIndex = layer.open({
                        type: 1,
                        title: "请确认",
                        content: msgHtml,
                        btn: ["取消", "跳过风控订单继续打印"],
                        shadeClose: true,
                        area: ['550', '250'],
                        btn1: function () {
                            layer.close(dialogIndex);
                            return true;
                        },
                        btn2: function () {
                            layer.close(dialogIndex);
                            orderTableBuilder.FilterTouTiaoRiskProcessingOrder();//过滤抖店风控订单
                            orders = orderTableBuilder.getSelections();//重新获取订单数据
                            doPrintBeforeCheckTaobao();
                            return true;
                        }
                    });
                } else {
                    doPrintBeforeCheckTaobao();
                }
            } else {
                doPrintBeforeCheckTaobao();
            }
        }

        //2.3 检查抖店 自选快递
        var doPrintBeforeCheckToutiao = function () {
            if (isTouTiao && template != null) {
                var hasShopExpressInfo = [];
                var toutiaoTipExpress = [];
                $(orders).each(function (i, o) {
                    if (commonModule.HasTag(o.OrderTags, 'shop_optional_express_info', 'OrderItem')) {
                        var isNotExpress = false;
                        var tempExpressName = [];
                        for (var j = 0; j < o.OrderTags.length; j++) {
                            var tag = o.OrderTags[j];
                            if (tag.Tag == "shop_optional_express_info") {
                                var tagValue = tag.TagValue;
                                var arrTagVal = tagValue.split('|');
                                if (template.ExpressCompanyCode == arrTagVal[0]) {
                                    isNotExpress = true;
                                } else {
                                    if (tempExpressName.indexOf(arrTagVal[1]) == -1) {
                                        tempExpressName.push(arrTagVal[1]);
                                    }
                                }
                            }
                        }

                        if (!isNotExpress) {
                            hasShopExpressInfo.push(o);
                            common.Foreach(tempExpressName, function (ii, ecm) {
                                if (toutiaoTipExpress.indexOf(ecm) == -1) {
                                    toutiaoTipExpress.push(ecm);
                                }
                            });
                        }
                    }
                });
                if (hasShopExpressInfo.length > 0) {
                    var dialogIndex = layer.open({
                        type: 1,
                        title: "请确认",
                        content: '<div style="padding: 20px;">已选择快递为<span style="color:#3aadff;">' + template.ExpressCompanyName + '</span>,已选择' + orders.length + '个订单，其中' + hasShopExpressInfo.length + '个订单自选快递为<span style="color:#3aadff;">' + toutiaoTipExpress.join(",") + '</span>，是否跳过自选快递订单继续打印？</div>',
                        btn: ["跳过,继续打印", "取消"],
                        shadeClose: true,
                        area: ['550', '250'],
                        btn1: function () {
                            layer.close(dialogIndex);
                            var filterLogicOrderId = [];
                            commonModule.Foreach(hasShopExpressInfo, function (i, oo) {
                                filterLogicOrderId.push(oo.LogicOrderId);
                            });
                            orderTableBuilder.FilterTouTiaoShopExpressOrder(filterLogicOrderId);//过滤中小件订单
                            orders = orderTableBuilder.getSelections();//重新获取订单数据
                            doPrintBeforeCheckTouTiaoRisk();
                            return true;
                        },
                        btn2: function () {
                            return true;
                        }
                    });
                } else {
                    doPrintBeforeCheckTouTiaoRisk();
                }
            }
            else {
                doPrintBeforeCheckTouTiaoRisk();
            }
        }
        // 2.2.1 检查阿里巴巴 官方仓发
        var doPrintBeforeCheckAlibaba = function () {
            if (isAlibaba && template != null) {
                var hashyperLinkShipOrders = [];
                $(orders).each(function (i, o) {
                    if (commonModule.HasTag(o.OrderTags, 'hyperLinkShip', 'OrderItem')) {
                        hashyperLinkShipOrders.push(o);
                    }
                });
                if (hashyperLinkShipOrders.length > 0) {
                    var msgHtml = '<div class="wu-f14 wu-c09">';
                    msgHtml += '<div class="wu-mB12">勾选订单中部分订单为1688官方仓发订单，由官方托管发货，无需在店管家内打印发货。是否跳过官方仓发订单继续打印其他订单？</div>';
                    msgHtml += '<a href = "https://peixun.1688.com/space/l2AmoEo9rYgw8zdb/detail/Gl6Pm2Db8D3moaOOTz4MdnMNJxLq0Ee4" target="_blank" class="wu-color-a wu-operate">什么是1688官方仓发托管服务？</a>';
                    msgHtml += '</div>';
                    var dialogIndex = layer.open({
                        type: 1,
                        title: "请确认",
                        content: msgHtml,
                        btn: ["跳过，继续打印", "取消"],
                        area: '560px', // 宽高
                        skin: 'wu-dailog',
                        btn1: function () {
                            layer.close(dialogIndex);
                            orderTableBuilder.FilterAlibabaHyperLinkShipOrder();//过滤1688官方仓发订单
                            orders = orderTableBuilder.getSelections();//重新获取订单数据
                            doPrintBeforeCheckToutiao();
                            return true;
                        },
                        btn2: function () {
                            layer.close(dialogIndex);
                            return true;
                        },
                    });
                } else {
                    doPrintBeforeCheckToutiao();
                }
            } else {
                doPrintBeforeCheckToutiao();
            }
        }
        //2.2 检查小红书 
        var doPrintBeforeCheckXiaoHongShu = function () {
            if (isXhs && template != null) {
                var hasHomeDeliveryDoor = [];
                $(orders).each(function (i, o) {
                    var hasMidHomeDeliveryDoor = commonModule.HasTag(o.OrderTags, 'home_delivery_door', 'OrderItem')
                    if (hasMidHomeDeliveryDoor) {
                        hasHomeDeliveryDoor.push(o);
                    }
                });
                if (hasHomeDeliveryDoor.length > 0) {
                    //获取的订单店铺上门快递信息
                    var xhsCallback = function () {
                        var supportExPressCode = "";
                        var supportExPressName = "";
                        if (commonModule.xhsDoorSupportExpressModel) {
                            supportExPressCode = commonModule.xhsDoorSupportExpressModel.ExpressCode;
                            supportExPressName = commonModule.xhsDoorSupportExpressModel.ExpressName;
                        }
                        if (supportExPressCode != "" && supportExPressCode.indexOf("," + template.ExpressCompanyCode + ",") < 0) {
                            var dialogIndex = layer.open({
                                type: 1,
                                title: "请确认",
                                content: '<div style="padding: 20px;">已选择' + orders.length + '个订单，其中' + hasHomeDeliveryDoor.length + '个订单送货上门订单，<span style="color:#fe6f4f">未按照平台要求选择指定快递发货(' + supportExPressName + ')，可能引起赔付问题</span>，是否跳过送货上门订单继续打印</div>',
                                btn: ["跳过,继续打印", "取消"],
                                shadeClose: true,
                                area: ['550', '250'],
                                btn1: function () {
                                    layer.close(dialogIndex);
                                    orderTableBuilder.FilterXhsHomeDeliveryDoorOrder();//过滤中小件订单
                                    orders = orderTableBuilder.getSelections();//重新获取订单数据
                                    doPrintBeforeCheckAlibaba();
                                    return true;
                                },
                                btn2: function () {
                                    return true;
                                }
                            });
                        } else {
                            doPrintBeforeCheckAlibaba();
                        }
                    }
                    commonModule.LoadCommonSetting("/FenFa/System/XiaoHongShu/DoorServiceSuppprtExpressCompany", false, function (res) {
                        if (res.Success && res.Data) {
                            commonModule.xhsDoorSupportExpressModel = JSON.parse(res.Data);
                        } else {
                            commonModule.xhsDoorSupportExpressModel = { ExpressName: "", ExpressCode: "" };
                        }
                        xhsCallback();
                    });
                } else {
                    doPrintBeforeCheckAlibaba();
                }
            }
            else {
                doPrintBeforeCheckAlibaba();
            }
        }
        //2.1 检查京东
        var doPrintBeforeCheckJingdong = function () {
            if (isJingdong && template != null) {
                var hasHomeDeliveryDoor = [];
                $(orders).each(function (i, o) {
                    var hasMidHomeDeliveryDoor = commonModule.HasTagValue(o.OrderTags, 'home_delivery_door', '2', 'OrderItem');
                    if (hasMidHomeDeliveryDoor) {
                        hasHomeDeliveryDoor.push(o);
                    }
                });
                if (hasHomeDeliveryDoor.length > 0) {
                    //获取的订单店铺上门快递信息
                    commonModule.Ajax({
                        url: '/TemplateSet/GetDoorServiceExpress',
                        data: { templateId: template.Id }, //查找面单所属的店铺
                        success: function (rsp) {
                            if (rsp.Success == false) {
                                doPrintBeforeCheckXiaoHongShu();
                            }
                            //店铺未开通送货上门服务，直接打印
                            var isOpenDoorService = rsp.Data.IsDoorService;
                            if (isOpenDoorService == false) {
                                doPrintBeforeCheckXiaoHongShu();
                                return true;
                            }
                            var expressCodes = rsp.Data.DoorServiceExpress;
                            //当前选择的快递不在送货上门的快递列表时，拦截提示
                            if (expressCodes.indexOf("," + template.ExpressCompanyCode + ",") < 0) {

                                var dialogIndex = layer.open({
                                    type: 1,
                                    title: "请确认",
                                    content: '<div style="padding: 20px;">已选择' + orders.length + '个订单，其中' + hasHomeDeliveryDoor.length + '个订单中小件送货上门订单，未按平台要求选择指定快递发货（中通、圆通、申通、极兔、韵达）,<span style="color:#fe6f4f">可能引起赔付问题</span>，是否跳过送货上门订单继续打印</div>',
                                    btn: ["跳过,继续打印", "忽略,继续打印"],
                                    shadeClose: true,
                                    area: ['550', '250'],
                                    btn1: function () {
                                        layer.close(dialogIndex);
                                        orderTableBuilder.FilterJdMidHomeDeliveryDoorOrder();//过滤中小件订单
                                        orders = orderTableBuilder.getSelections();//重新获取订单数据
                                        doPrintBeforeCheckXiaoHongShu();
                                        return true;
                                    },
                                    btn2: function () {
                                        doPrintBeforeCheckXiaoHongShu();
                                        return true;
                                    }
                                });
                            } else {
                                doPrintBeforeCheckXiaoHongShu();
                            }

                        }
                    });
                } else {
                    doPrintBeforeCheckXiaoHongShu();
                }
            }
            else {
                doPrintBeforeCheckXiaoHongShu();
            }
        }

        //2.检查拼多多
        var doPrintBeforeCheckPinduoduo = function () {
            if (isPinduoduo && template != null) {
                var html = "";

                var hasLocalOrder = []; //拼多多本地仓
                //检查是否含有承诺信息
                $(orders).each(function (i, o) {
                    $(orderTableBuilder.rows).each(function (i1, row) {
                        if (o.PlatformOrderId == row.PlatformOrderId && o.ShopId == row.ShopId) {
                            $(row.SubOrders).each(function (i2, so) {
                                if (so.ExtField2 && so.ExtField2 != template.ExpressCompanyCode) {
                                    html += '<tr><td>' + so.PlatformOrderId + '</td><td data-value="' + so.ExtField2 + '">' + so.PromiseExpressName + '</td></tr>';
                                }
                            });
                            return true;
                        }
                    });
                    var hasLocalTag = commonModule.HasTag(o.OrderTags, 'local_depot', 'Order')
                    if (hasLocalTag) {
                        hasLocalOrder.push(o);
                    }
                });

                var checkLocalOrder = function () {
                    if (hasLocalOrder.length > 0) {
                        var dialogIndex = layer.open({
                            type: 1,
                            title: "请确认",
                            content: '<div style="padding: 20px;">已选择' + orders.length + '个订单，其中' + hasLocalOrder.length + '个订单为拼多多本地仓订单，本地仓订单请前往拼多多官方后台处理，是否跳过本地仓订单继续打印</div>',
                            btn: ["跳过,继续打印", "取消"],
                            shadeClose: true,
                            area: ['550', '250'],
                            btn1: function () {
                                layer.close(dialogIndex);
                                orderTableBuilder.FilterPddLocalOrder();
                                orders = orderTableBuilder.getSelections();//重新获取订单数据
                                doPrintBeforeCheckJingdong();
                                return true;
                            },
                            btn2: function () {
                                return true;
                            }
                        });
                    } else {
                        doPrintBeforeCheckJingdong();
                    }
                }
                if (html) {
                    $("#pddSpecifyExpress i.express-checked").html(template.TemplateName);
                    $("#pddSpecifyExpress table.unify_table tbody").html(html);
                    var dialogIndex = layer.open({
                        type: 1,
                        title: "请确认",
                        content: $("#pddSpecifyExpress"),
                        btn: ["忽略，继续打印", "取消"],
                        shadeClose: true,
                        area: ['550', '250'],
                        btn1: function () {
                            layer.close(dialogIndex);

                            checkLocalOrder();
                            return true;
                        },
                        btn2: function () {
                            return true;
                        }
                    });
                }
                else {
                    checkLocalOrder();
                }
            }
            else {
                doPrintBeforeCheckJingdong();
            }
        }

        var doCheckExpressReach = function () {

            //1.检查是否含有快递不可达
            var expressReachOrders = [];
            $(orders).each(function (i, o) {
                var tag_el = $("#order-" + o.Index + " .address_type").children('.express-reach');
                if (tag_el.length > 0) {
                    expressReachOrders.push(o);
                }
            });
            if (isCheckPrinted != false && expressReachOrders.length > 0) {
                layer.open({
                    type: 1,
                    title: "不可达地址订单提醒",
                    content: $('#aialog_expressReach_show'),
                    area: ['460px'], //宽高
                    skin: 'wu-dailog',
                    btn: ['过滤不可达订单，只打印可达订单', '忽略，继续打印'],
                    btn1: function () {
                        layer.closeAll();
                        orderTableBuilder.FilterNotReachOrder();//过滤不可达订单
                        orders = orderTableBuilder.getSelections();//重新获取订单数据
                        doPrintBeforeCheckPinduoduo();
                        return true;
                    },
                    btn2: function () {
                        layer.closeAll();
                        doPrintBeforeCheckPinduoduo();
                        return true;
                    },
                    success: function () {
                        $("#aialog_expressReach_count").text(expressReachOrders.length);
                    }
                    //yes: function () {
                    //    layer.closeAll();
                    //    doPrintBeforeCheckPinduoduo();
                    //}
                })
            } else {
                doPrintBeforeCheckPinduoduo();
            }
        }

        var doCheckExpressShipHold = function () {
            //检查是否有拼多多暂停打印标签
            var shipholdOrders = [];
            $(orders).each(function (i, o) {
                var tag_el = $("#order-" + o.Index + " .address_type").children('.ship-hold');
                if (tag_el.length > 0) {
                    shipholdOrders.push(o);
                }
            });
            if (shipholdOrders.length > 0) {
                layer.open({
                    type: 1,
                    title: false,
                    content: $('#aialog_expressShipHold_show'),
                    closeBtn: false,
                    area: ['480px'], //宽高
                    btn: ['继续打印', '取消'],
                    skin: 'wu-dailog',
                    btn1: function () {
                        layer.closeAll();
                        orderTableBuilder.FilterNotShipHoldOrder();//过滤暂不发货订单
                        orders = orderTableBuilder.getSelections();//重新获取订单数据
                        doPrintBeforeCheckPinduoduo();
                        return true;

                    },
                    btn2: function () {


                        layer.closeAll();
                        return true;
                    },
                    success: function () {

                    }
                    //yes: function () {
                    //    layer.closeAll();
                    //    doPrintBeforeCheckPinduoduo();
                    //}
                })
                return true;
            }
            return false;
        }

        // 检查是否有订单打印标识没有
        var checkNoPrintFlag = function () {
            if (doCheckExpressShipHold()) {
                return;
            }
            // 灰度环境才做订单打印状态检查
            if (isCheckPrinted == false || commonModule.CurrShop.Version != "1") {
                doCheckExpressReach();
                return;
            }

            var html = '<div class="noPrintFlagIdsDailog">';
            html += '<div class="noPrintFlagIdsDailog-title">我们检查到以下订单已打印但未标记打印状态，为了避免重复打印，请您确认以下订单是否已经打印成功！</div>';
            html += '<div class="noPrintFlagIdsDailog-content">'
            html += '<ul class="noPrintFlagIdsDailog-content-left">'
            var noPrintFlagOrders = [];
            var noPrintFlagIds = [];
            var noPrintFlagLogicOrderIds = [];
            var allPlatformOrderId = "";
            for (var i = 0; i < orders.length; i++) {
                var o = orders[i];
                if (o.NoPrintFlag) {
                    noPrintFlagOrders.push(o);
                    noPrintFlagIds.push(o.Id);
                    noPrintFlagLogicOrderIds.push(o.PlatformOrderId);

                    //html += o.PlatformOrderId + ',';
                    allPlatformOrderId += o.PlatformOrderId + ',';
                    html += '<li><span class="noPrintFlagIdsDailog-content-title">订单编号：</span>' + o.PlatformOrderId + '</li>';
                }
            }
            html = html.trimEndDgj(',');
            html += "</ul>";
            html += '<div class="noPrintFlagIdsDailog-content-right"><button class="layui-btn layui-btn-sm" style="background-color:#77bf04;padding: 0 2px 0 5px;" onclick=\'expressPrinter.copyBatchLogicOrderSearch("' + allPlatformOrderId + '")\'>复制批量查询<i class="iconfont icon-sousuo" style="margin-left:3px"></i></button></div>'

            html += "</div>";
            html += "</div>";
            if (noPrintFlagIds.length > 0) {

                try {
                    commonModule.JsLogToMongoDB("打印标识未更新提示", JSON.stringify(noPrintFlagOrders));
                } catch (e) {
                    var errorMsg = "订单打印标识未更新写入日志记录异常》" + e.stack;
                    console.log(errorMsg);
                    commonModule.JsExcptionLog("打印标识未更新前端异常日志", errorMsg);
                }


                noPrintFlagIdsDailog = layer.open({
                    type: 1,
                    title: "订单打印标记校验",
                    content: html,
                    skin: "pirntMethodDailog",
                    btn: ["确认重打", "已打印立即发货"],
                    shadeClose: true,
                    area: ['550', '450'],
                    btn1: function () {
                        layer.close(noPrintFlagIdsDailog);
                        doCheckExpressReach();
                        return true;
                    },
                    btn2: function () {
                        console.log("已打印立即发货")
                        sendLogistic.BatchSendOrder(false, '', false, noPrintFlagIds); //发货
                        return true;

                        //layer.confirm("修复打印标记");
                        //updatePrintFlag(noPrintFlagLogicOrderIds);
                        //return true;
                    },
                    btn3: function () {
                        //sendLogistic.send(false, '', false, noPrintFlagIds); //发货
                        //return true;
                    }
                });
            }
            else {
                doCheckExpressReach();
            }
        }
        var platformRemind = function () {
            var PlatformCk = true;
            var orderPlatformTypeCheck = ep.CheckPlatformType(template.TemplateType, orders);
            if (orderPlatformTypeCheck.msg) {
                PlatformCk = false;
                var checkplatformkey = "Order_PlatformType_Check_" + orderPlatformTypeCheck.templatechecktype + "_" + orderPlatformTypeCheck.pt;
                var checkplatformkeyvalue = window.localStorage.getItem(checkplatformkey);
                var tipsmsg = "";
                tipsmsg += '<div class="checkPlatformTypeWarn">'
                tipsmsg += '<div class="checkPlatformTypeWarn-title">'
                tipsmsg += '<i class="checkPlatformTypeWarnIcon"></i>'
                tipsmsg += '<span>' + orderPlatformTypeCheck.msg + '</span>'
                tipsmsg += '</div>'
                tipsmsg += '<div class="checkPlatformTypeWarn-footer">'
                tipsmsg += '<a class="wu-color-a wu-operate wu-f14" href="' + orderPlatformTypeCheck.helpHref + '" target="_blank">' + orderPlatformTypeCheck.helpTitle + '</a>'
                tipsmsg += '</div>'
                tipsmsg += '</div>'


                if (checkplatformkeyvalue != "1") {
                    var dialogIndex2 = layer.open({
                        type: 1,
                        title: "请确认",
                        content: tipsmsg,
                        btn: ["忽略，继续打印", "不再提示，继续打印", "取消"],
                        area: ['500'],
                        skin: 'wu-dailog',
                        btn1: function () {
                            layer.close(dialogIndex2);
                            //doCheckExpressReach();
                            checkNoPrintFlag();
                            return true;
                        },
                        btn2: function () {
                            window.localStorage.setItem(checkplatformkey, "1");
                            layer.close(dialogIndex2);
                            //doCheckExpressReach();
                            checkNoPrintFlag();
                            return true;
                        },
                        btn3: function () {
                            return true;
                        }
                    });
                }
                else {
                    //doCheckExpressReach();
                    checkNoPrintFlag();
                    PlatformCk = true;
                }
            }
            else {
                checkNoPrintFlag();
            }
        }

        //【订单分发】打印时针对不同平台不同电子面单拦截提醒
        if (template != null) {
            //京东云并且快递面单不是无界京东面单
            if (isJingdong && template.TemplateTypeShort != "Jingdong"
                && (template.ExpressCompanyCode.toLowerCase() != "jingdongkuaiyun" || template.ExpressCompanyCode.toLowerCase() != "jd")) {
                //校验是否有京东配送标签
                var orderIds = [];
                for (var index in orders) {
                    var order = orders[index];
                    var tags = order.OrderTags
                    if (commonModule.HasTag(tags, 'jd_delivery', 'Order')) {
                        console.log(order);
                        orderIds.push(order.Id);
                    }
                }
                if (orderIds.length > 0) {
                    if (orders.length === orderIds.length) {
                        // 全部京东配送
                        //var tipContent = '<div class="n-font5">您选中的订单为京东配送订单，应平台要求，此类订单请用京东快递打单发货，否则会发货失败。</div>';
                        //layer.open({
                        //    type: 1,
                        //    title: '提示', // 标题
                        //    content: tipContent,
                        //    offset: '200px',
                        //    area: '480px', // 宽高
                        //    skin: 'n-skin',
                        //    btn: ['知道了'],
                        //});
                        layer.msg('京东配送订单只能用京东快递打单发货');
                        return false;
                    } else {
                        /**
                        layer.alert("您选中的订单中包含京东配送订单，应平台要求，此类订单请用京东快递打单发货，否则会发货失败。是否忽略京东配送订单继续打印其他订单。",
                            {
                                title: "提示",
                                btn: ["忽略，继续打印", "取消"],
                                btn1: function () {
                                    console.log("忽略")
                                    //移除京东配送订单
                                    orders = orders.filter(function (item) {
                                        //orderIds内存在item.Id
                                        return orderIds.indexOf(item.Id) == -1;
                                    })
                                    platformRemind();
                                },
                            });
                        */
                        var html = "";
                        html += '<div>';
                        html += '<div style="background: rgba(0, 0, 0, 0.04);padding: 12px 16px;box-sizing: border-box;display: flex;align-items: center;justify-content: space-between;">';
                        html += '<span style="display: flex;align-items: center;">';
                        html += '<i class="iconfont icon-a-info-circle-filled1x n-dColor" style="font-size: 20px;"></i>';
                        html += '<span class="n-font5" style="margin-left: 4px;">是否忽略京东配送订单继续打印其他订单</span>';
                        html += '</span>';
                        html += '<i class="iconfont icon-a-close1x hover" style="font-size: 22px;" id="close_jingdong_print_order_dialog"></i>';
                        html += '</div>';
                        html += '<div class="n-font5" style="padding: 16px 40px; box-sizing: border-box;">您选中的订单中包含京东配送订单，应平台要求，此类订单请用京东快递打单发货，否则会发货失败。</div>';
                        html += '</div>';
                        layer.open({
                            type: 1,
                            title: false, // 不显示标题
                            content: html,
                            offset: '200px',
                            closeBtn: 0,
                            area: '560px', // 宽高
                            skin: 'n-skin',
                            success: function (layero, index) {
                                $(".n-skin .layui-layer-content").css("padding", 0);
                                $("#close_jingdong_print_order_dialog").on("click", function () {
                                    layer.close(index);
                                });
                            },
                            btn: ['取消', '忽略，继续打印'],
                            btn1: function (index) {
                                layer.close(index);
                            },
                            btn2: function (index) {
                                // 移除京东配送订单
                                orders = orders.filter(function (item) {
                                    // orderIds内存在item.Id
                                    return orderIds.indexOf(item.Id) == -1;
                                });
                                layer.close(index);
                                platformRemind();
                            }
                        });
                    }
                } else platformRemind();
            } else platformRemind();
           
        } else {
            checkNoPrintFlag();
        }
    }

    var updatePrintFlag = function (oids) {
        commonModule.Ajax({
            type: "POST",
            url: "/NewOrder/UpdatePrintFlag",
            data: { logicOrderIds: oids },
            success: function (rsp) {
                if (rsp.Success) {
                    layer.confirm("已成功修复打印标记，是否刷新页面？", { skin: 'wu-dailog' }, function () {
                        $("#SeachConditions").click();
                    });
                }
                else {
                    layer.alert(rsp.Message, { skin: 'wu-dailog' });
                }
            }
        });
    }

    //printMethod 打印方式
    //printcount 打印数量
    //isCheckPrinted 是否检查已打印过
    //confirmPddDoor 针对跨境单快递上门揽件是否继续
    //isPrimaryCode 是否原单号打印
    ep.startPrint = function startPrint(orders, pirntMethod, printCount, isCheckPrinted, confirmPddDoor, isPrimaryCode) {
        var template = addTmplInOrderListModule.GetCurrentTemplate()
        if (!ep.check(orders, template, checkTemplateIsNull, pirntMethod, printCount, isCheckPrinted, false, false, confirmPddDoor, isPrimaryCode))
            return false;

        if (!pirntMethod)
            pirntMethod = "Normal";
        printComponents = common.GetUsePrintComponents(template.TemplateType);
        var printers = GetPrinter(printComponents);

        //参数初始化
        if (isCheckPrinted == undefined) isCheckPrinted = true;
        if (printCount == undefined || isNaN(printCount) == true)
            printCount = 1;
        else
            printCount = parseInt(printCount);

        if (printCount <= 0) {
            layer.alert("打印数量必须为大于0的数字", { skin: 'wu-dailog' });
            return false;
        }
        if (common.IsZhiLian(template.TemplateType, template.ExpressCompanyCode) && (printCount * orders.length) > template.Quantity) {
            layer.alert("单号不足！当前模板可用单号【" + template.Quantity + "】,本次打印所需单号【" + (printCount * orders.length) + "】，请充值或调整打印数量。", { skin: 'wu-dailog' });
            return false;
        }
        //检查打印数量是否超出了可用数量
        else if ((printCount * orders.length) > template.Quantity && (template.BrandCode == "FW" && common.IsCainiaoTemplate(template.TemplateType))) {
            layer.alert("单号不足！当前模板可用单号【" + template.Quantity + "】,本次打印所需单号【" + (printCount * orders.length) + "】，请充值或调整打印数量。", { skin: 'wu-dailog' });
            return false;
        }
        else if (common.IsNormalTemplate(template.TemplateType) == false && common.IsJdKdTemplate(template.TemplateType) == false
            && template.CpType != 1 && template.CpType != 4 && template.CpType != 5
            && (printCount * orders.length) > template.Quantity) {
            layer.alert("单号不足！当前模板可用单号【" + template.Quantity + "】,本次打印所需单号【" + (printCount * orders.length) + "】，请充值或调整打印数量。", { skin: 'wu-dailog' });
            return false;
        }

        var tempFunc = function (expressSustenance) {

            //重打确认
            if (pirntMethod != 'NewPrint' && isCheckPrinted == true && ep.checkPrinted(orders, pirntMethod, template) == false) {
                return false;
            }
            //快运包裹的长宽高 保留上一次写入的
            var packinfo = {};
            if (common.IsKuaiYunTemplate(template.TemplateType) ||
                common.IsPddKuaiYunTemplate(template.TemplateType, template.ExpressCompanyCode) ||
                common.IsTouTiaoKuaiYunTemplate(template.TemplateType) ||
                common.IsLinkKuaiYunTemplate(template.TemplateType) || 
                common.IsKuaiShouKuaiYunTemplate(template.TemplateType)) {
                pirntMethod = pirntMethod == 'NewPrint' ? "NewPrintKuaiYun" : "PrintKuaiYun"; //打印快运面单

                if (common.IsKuaiYunTemplate(template.TemplateType) && template.ExpressCompanyCode == "SFKY") {
                    var packStrInfo = $.cookie('print-express-lhwinfo-' + template.Id);
                    if (packStrInfo != null && packStrInfo != "")
                        packinfo = JSON.parse(packStrInfo);
                }
            }
            var ext = otb.getSelectionsExt(orders);
            //根据模板类型生成对应的请求数据？
            var dialog = $.templates("#print-express-dialog-tmpl");
            var showPintaiIcon = common.showPintaiIcon(template.TemplateTypeShort);

            var dialogData = {
                PrintMethod: pirntMethod,
                PrintCount: printCount,
                ExtInfo: ext,
                Orders: orders,
                Template: template,
                Printers: (printers || []),
                defaultPrinter: ep.defaultPrinter,
                IsCustomePrint: common.isCustomerOrder(),
                Packinfo: packinfo,
                PintaiIcon: showPintaiIcon,
                IsPrimaryCode: isPrimaryCode
            };
            if (template.ExpressCompanyCode == 'SF' || template.ExpressCompanyCode == 'JD' || template.ExpressCompanyCode == 'BDB') {
                //顺丰、京东
                dialogData.ExpressSustenance = expressSustenance;
            }
            var html = dialog.render(dialogData);
            var area = '560px';
            var title = "打印快递单";
            if (pirntMethod == "OneToMany") {
                title += "【一单多包】";
                // area = ['695px', '300px'];
            }
            else if (pirntMethod == "NewPrint") {
                title += "【新单号打印】";
                // area = ['700px', '300px'];
            }
            else if (pirntMethod == "RePrint")
                title += "【重打】";
            else if (pirntMethod == "PrintKuaiYun")
                title += "【快运单】";

            // title += "<strong style='color:red;' title='打印过程中，请勿进行刷新页面动作！刷新会导致打印状态标记失败'>(打印过程中，请勿进行刷新页面动作！刷新会导致打印状态标记失败)</strong>";
            var btn2Handler = function () {
                var this_btn = $('.layui-layer-btn1');
                var processing = this_btn.attr('processing'); //处理中的标记
                if (processing) {
                    layer.alert('正在处理...请不要重复点击', { skin: 'wu-dailog' });
                    return;
                }
                this_btn.attr('processing', true); //处理中的标记

                var cuurtemplate = addTmplInOrderListModule.GetCurrentTemplate();
                if (cuurtemplate.Id != template.Id)
                    template = cuurtemplate;

                var printCount = 1;
                var printer = $("#express-printer-select").val();
                if (!printer) {
                    layer.msg("请选择打印机");
                    this_btn.removeAttr('processing'); //移除处理中标识
                    return false;
                }
                if (pirntMethod == "NewPrint" || pirntMethod == "RePrint" || pirntMethod == "Normal") {
                    printCount = $("#express-package-print-count").val();
                }
                if (pirntMethod == "OneToMany") {
                    printCount = $("#express-package-print-count").val();
                    if (printCount <= 0) {
                        layer.msg("打印一单多包时，打印数量必须大于等于1");
                        this_btn.removeAttr('processing'); //移除处理中标识
                        return false;
                    }
                }
                if (pirntMethod == "PrintKuaiYun" || pirntMethod == "NewPrintKuaiYun" || common.IsTouTiaoKuaiYunTemplate(template.TemplateType)) {
                    printCount = $("#txt_package_order_count").val().trim();
                    if (printCount <= 0 || isNaN(printCount)) {
                        layer.msg("打印快运面单时，子母件数量必须大于等于1");
                        this_btn.removeAttr('processing'); //移除处理中标识
                        return false;
                    }
                }

                if (pirntMethod == "NewPrint" || pirntMethod == "RePrint" || pirntMethod == "Normal") {
                    // 点击打印时--判断是否存在小红书平台的订单且打印方式为 【按包裹最多件数】--进行前置拦截
                    var isHasXhs = orders.some(function (item) {
                        return item.PlatformType == "XiaoHongShu"
                    });

                    if (isHasXhs && printCount == '按包裹最多件数打印面单') {
                        var msgHtml = "已选的【小红书】订单，不支持按【包裹最多件数】打印。<br/>";
                        msgHtml += '请取消勾选<span class="wu-weight600">这些</span>订单，';
                        msgHtml += '或打印方式更换为<span class="wu-weight600">【按商品种类】</span>或<span class="wu-weight600">【按商品规格】</span>后重试。'
                        layer.alert(msgHtml, { title: "操作提示", skin: 'wu-dailog', area: '565px', btn: ["确定"] });
                        this_btn.removeAttr('processing'); //移除处理中标识
                        return false;
                    }
                }

                var printerIndex = 0;
                if (printComponents == "Lodop") {
                    printerIndex = printer;
                    printer = $("#express-printer-select option[value='" + printer + "']").text();
                }

                if (printComponents == "Fengqiao") {
                    printerIndex = printer;
                    printer = $("#express-printer-select option[value='" + printer + "']").text();
                }
                //保存快运包裹信息记录
                if (commonModule.IsKuaiYunTemplate(template.TemplateType) && template.ExpressCompanyCode == "SFKY") {
                    var isBool = _wirtePackInfo(template.Id);
                    if (isBool == false) {
                        this_btn.removeAttr('processing'); //移除处理中标识
                        return false;
                    }
                }
                ep.doPrint(orders, template, pirntMethod, printCount, true, printer, null, printerIndex);
                this_btn.removeAttr('processing'); //移除处理中标识
                this_btn.remove(); //直接移除按钮
            };

            var btn3Handler = function () {
                var templateId = template.Id;
                var templateType = template.TemplateType + "";
                var url = "";
                switch (templateType) {
                    case "1":
                        url = "/TemplateSet/EditTraditionTemplate?templateId=" + templateId;
                        break;
                    case "3":
                    case "2":
                    case "10":
                    case "13":
                        url = "/TemplateSet/EditSiteTemplate?templateId=" + templateId;
                        break;
                    case "4":
                    case "5":
                    case "6":
                    case "7":
                    case "8":
                    case "9":
                    case "17":
                    case "18":
                    case "19":
                    case "20":
                    case "21":
                    case "22":
                    case "23":
                        url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        break;
                    default:
                        if (templateType >= 40 && templateType < 60)
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        else if (templateType >= 60 && templateType < 70)
                            url = "/TemplateSet/EditSiteTemplate?templateId=" + templateId;
                        else if (templateType >= 91 && templateType < 100)
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        else if (templateType >= 111 && templateType < 120)
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        else if (common.IsPddTemplate(templateType) || common.IsPddKuaiYunTemplate(templateType))
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        else if (common.IsXiaoHongShuTemplate(templateType) || common.IsNewXiaoHongShuTemplate(templateType))
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        else if (common.IsWxVideoTemplate(templateType))
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        else {
                            layer.alert("未识别模板类型，无法修改", { skin: 'wu-dailog' });
                            break;
                        }
                        break;
                }
                if (url)
                    window.open(common.rewriteUrlToMainDomain(url));
            };

            var btns = ["直接打印", "修改模板"];
            var opts = {};
            if (printComponents != "Lodop" && printComponents != "TouTiao" && printComponents != "KuaiShou" && printComponents != "WxVideo") {
                btns.splice(1, 0, "  预 览  ") //菜鸟电子面单才有预览
                opts.btn2 = btn2Handler,
                    opts.btn3 = btn3Handler
            }
            if (template && common.IsWxVideoTemplate(template.TemplateType) && template.EwaybillTemplateId) {
                btns = ["直接打印"];
            }
            var options = {
                type: 1,
                title: title,
                btn: btns,
                shadeClose: true,
                area: area,
                content: html,
                skin: 'wu-dailog',
                id:'print-express-dialog',
                success: function () {
                    var $template = $("#hasTemplateWrap_show").clone();
                    $("#hasTemplateWrap_wrap").html($template);
                    var templateOffset = $("#hasTemplateWrap_wrap").offset();
                    if (templateOffset) {
                        var templateOffset_top = templateOffset.top - $(document).scrollTop() + 35
                        $("#hasTemplateWrap_wrap .my-select-02-inputWrap-content").css({ top: templateOffset_top, left: 'unset' })
                    }
                    ISOrders = orders;
                    //自动发货
                    //$(':radio[name="rdo_printed_auto_send"][value="' + commonModule.SystemoConfig.AutoSendSetVal + '"]').prop('checked', true);
                },
                btn1: function () {

                    var this_btn = $('.layui-layer-btn0');
                    var processing = this_btn.attr('processing'); //处理中的标记
                    if (processing) {
                        layer.alert('正在处理...请不要重复点击', { skin: 'wu-dailog' });
                        return;
                    }
                    this_btn.attr('processing', true); //处理中的标记
                    var cuurtemplate = addTmplInOrderListModule.GetCurrentTemplate();
                    if (cuurtemplate.Id != template.Id)
                        template = cuurtemplate;

                    var printCount = 1;
                    var printer = $("#express-printer-select").val();
                    if (!printer) {
                        layer.msg("请选择打印机");
                        this_btn.removeAttr('processing'); //移除处理中标识
                        return false;
                    }
                    if (pirntMethod == "NewPrint" || pirntMethod == "RePrint" || pirntMethod == "Normal") {
                        printCount = $("#express-package-print-count").val();
                    }
                    //if (printComponents == "KuaiShou") {
                    //    printCount = $("#express-package-print-count").val();
                    //    if (printCount > 1) {
                    //        layer.open({
                    //            type: 1,
                    //            title: "提示",
                    //            btn: null,
                    //            area: ['560px'],
                    //            content: $('#div_kuaiShouRefuseOneToMore')
                    //        });
                    //        return false;
                    //    }
                    //}
                    if (pirntMethod == "OneToMany") {
                        printCount = $("#express-package-print-count").val();
                        if (printCount <= 0) {
                            layer.msg("打印一单多包时，打印数量必须大于等于1");
                            this_btn.removeAttr('processing'); //移除处理中标识
                            return false;
                        }
                    }
                    if (pirntMethod == "PrintKuaiYun" || pirntMethod == "NewPrintKuaiYun") {
                        printCount = $("#txt_package_order_count").val().trim();
                        if (printCount <= 0 || isNaN(printCount)) {
                            layer.msg("打印快运面单时，子母件数量必须大于等于1");
                            this_btn.removeAttr('processing'); //移除处理中标识
                            return false;
                        }
                    }

                    if (pirntMethod == "NewPrint" || pirntMethod == "RePrint" || pirntMethod == "Normal") {
                        // 点击打印时--判断是否存在小红书平台的订单且打印方式为 【按包裹最多件数】--进行前置拦截
                        var isHasXhs = orders.some(function (item) {
                            return item.PlatformType == "XiaoHongShu"
                        });

                        if (isHasXhs && printCount == '按包裹最多件数打印面单') {
                            var msgHtml = "已选的【小红书】订单，不支持按【包裹最多件数】打印。<br/>";
                            msgHtml += '请取消勾选<span class="wu-weight600">这些</span>订单，';
                            msgHtml += '或打印方式更换为<span class="wu-weight600">【按商品种类】</span>或<span class="wu-weight600">【按商品规格】</span>后重试。'
                            layer.alert(msgHtml, { title: "操作提示", skin: 'wu-dailog', area: '565px', btn: ["确定"] });
                            this_btn.removeAttr('processing'); //移除处理中标识
                            return false;
                        }
                    }

                    var printerIndex = 0;
                    if (printComponents == "Lodop") {
                        printerIndex = printer;
                        printer = $("#express-printer-select option[value='" + printer + "']").text();
                    }

                    if (printComponents == "Fengqiao") {
                        printerIndex = printer;
                        printer = $("#express-printer-select option[value='" + printer + "']").text();
                    }
                    //保存快运包裹信息记录
                    if (commonModule.IsKuaiYunTemplate(template.TemplateType) && template.ExpressCompanyCode == "SFKY") {
                        var isBool = _wirtePackInfo(template.Id);
                        if (isBool == false) {
                            this_btn.removeAttr('processing'); //移除处理中标识
                            return false;
                        }
                    }
                    //isCaiNiao ? caiNiao.setPrinter(printer) : lp.setPrinter(printerIndex);
                    if (ext.RefundOrders && ext.RefundOrders.length > 0) {
                        layer.confirm('您选中的订单中有<span class="wu-color-b">' + ext.RefundOrders.length + "个订单是退款中的</span>，仍继续打印吗？", { skin: 'wu-dailog' },
                            function () {
                                ep.doPrint(orders, template, pirntMethod, printCount, false, printer, null, printerIndex);
                            }, function () {
                                layer.closeAll();
                            });
                    } else
                        ep.doPrint(orders, template, pirntMethod, printCount, false, printer, null, printerIndex);
                    this_btn.removeAttr('processing'); //移除处理中标识
                    //this_btn.remove(); //直接移除按钮
                    return false;
                },
                btn2: btn3Handler,
                cancel: function () {
                    $('#hfIsContinueForStock').val('0');    //重置部分无货是否继续标记
                }
            };

            var openOpts = $.extend({}, options, opts);

            layer.open(openOpts);
        }

        console.timeEnd('打印数据检查前');
        console.timeEnd('打印数据检查前日志');


        //检查订单退款状态、模板的可用单号
        if (isCheckPrinted == true) {

            //由于有两个 检查项。1.检查退款订单。2.检查模板网点及可用单号余额。
            //之前两个请求都是同步的，同步加不上loading状态，改为异步并行执行。
            //并行执行后，将执行结果返回到回调函数中，如果前置校验不通过，则不往下执行。
            var checkLoading = common.LoadingMsg("打印前置检查");

            var arryList = [];

            //只有对接了消息的平台才需要去检查订单状态
            if (common.PlatformType == "Alibaba" || common.PlatformType == "AlibabaC2M" || common.PlatformType == "YouZan" || common.PlatformType=="TaobaoMaiCaiV2") {
                console.time('退款订单检查');
                otb.SetCheckReturnOrdersRequestShowLoading(false); //不单独显示进度
                arryList.push(otb.checkReturnOrders(orders, true));
                console.timeEnd('退款订单检查');
            }
            console.time('模板检查');
            arryList.push(ep.CheckTemplateAvaliable(template, (printCount * orders.length), orders, pirntMethod));
            console.timeEnd('模板检查');
            common.Ajaxs(arryList, function (result) {
                layer.close(checkLoading);

                //前置校验不通过，则不往下执行
                if (result == false) {
                    return;
                } else {
                    if (common.IsXiaoHongShuTemplate(template.TemplateType)) {
                        common.LoadCommonSetting("/FenDan/XiaoHongShu/New/Ebill/Alert", true, function (rsp) {
                            if (rsp.Data != "true") {

                                var html = "";
                                html += "<div id='system-prompt-xhs-tip' style='font-family: Source Han Sans;'><div style='padding: 16px;border-bottom: 0.5px solid #dbdbdb;'><div style='color: #191919;font-size: 14px;line-height: 20px;'>小红书新版电子面单已上线！</div>"
                                html += "<div style='color: #191919;font-size: 14px;line-height: 20px;'>平台将于<span style='color: #EA572E;font-weight: 500;'>2025年3月31日</span>"
                                html += "起不再支持旧版电子面单取号，为避免无法正常打单发货，请您尽快切换为新版电子面单。<a href='https://school.xiaohongshu.com/lesson/normal/ba4395753d134b2c95b2c9b0db0f715f?jumpFrom=school&uba_pre=8.xhsschool_search_list.school_search_card.1734486745919&uba_ppre=8.school_rule_detail..1734486729715&uba_index=3' target='_blank' style='color: #0888FF;cursor: pointer;'>如何切换新版电子面单？</a></div>"
                                html += "<div style='display: flex;align-items: center;margin-top: 8px;'><i style='color:#dc8715;margin-right:4px;' class='iconfont icon-a-error-circle-filled1x'></i><span style='color: #666666;'>旧版电子面单下线前，您仍可以继续使用旧版电子面单取号打印。</span></div></div>"
                                html += "<div class='system-prompt-xhs-tip-footer'><span class='system-prompt-xhs-tip-check-box'><span class='system-prompt-xhs-tip-checkbox'></span>不再提示</span><span class='system-prompt-xhs-tip-btn1'>取消</span><span class='system-prompt-xhs-tip-btn2'>继续打印</span></div></div>"

                                var dialogIndex = layer.open({
                                    type: 1,
                                    title: "系统提示",
                                    btn: false,
                                    resize: false,
                                    skin: 'n-skin',
                                    content: html,
                                    area: ["488px", "217px"], //宽高
                                    success: function () {
                                        $(".n-skin").css("border-radius", "8px");
                                        $("#system-prompt-xhs-tip").parent('.layui-layer-content').siblings('.layui-layer-title').css({
                                            'font-family': 'Source Han Sans', 'font-size': "14px", "font-weight": "500", "line-height": "44px", "color": "#181818",
                                            "padding-left": "16px", "height": "44px", "box-sizing": "border-box", "border-radius": "8px 8px 0px 0px"
                                        });

                                        $('.system-prompt-xhs-tip-check-box').on("click", function () {
                                            if ($(this).find('i').length > 0) {
                                                $(this).find('.system-prompt-xhs-tip-checkbox').empty().css({ 'border': '1px solid #dbdbdb', 'background-color': '#fff' });
                                            } else {
                                                $(this).find(".system-prompt-xhs-tip-checkbox").css({ 'border': 'none', 'background-color': '#0888ff' }).append('<i class="iconfont icon-a-check1x"></i>')
                                            }

                                        });

                                        $('.system-prompt-xhs-tip-btn1').on("click", function () {
                                            layer.close(dialogIndex);
                                        });
                                        $('.system-prompt-xhs-tip-btn2').on("click", function () {
                                            layer.close(dialogIndex);
                                            var isTrue = $(".system-prompt-xhs-tip-checkbox").find("i").length > 0;
                                            if (isTrue) {
                                                commonModule.SaveCommonSetting("/FenDan/XiaoHongShu/New/Ebill/Alert", isTrue, function (rsp) { });
                                            }
                                            tempFunc();
                                        });
                                    },
                                    end: function () {
                                        $('.system-prompt-xhs-tip-btn1').off("click");
                                        $('.system-prompt-xhs-tip-btn2').off("click");
                                        $('.system-prompt-xhs-tip-check-box').off("click");
                                    }
                                });
                            } else if (template.ExpressCompanyCode == 'SF' || template.ExpressCompanyCode == 'JD' || template.ExpressCompanyCode == 'BDB') {
                                ep.GetExpressSustenance(template, tempFunc);
                            } else {
                                tempFunc();
                            }
                        });
                    } else if (template.ExpressCompanyCode == 'SF' || template.ExpressCompanyCode == 'JD' || template.ExpressCompanyCode == 'BDB') {
                        ep.GetExpressSustenance(template, tempFunc);
                    } else {
                        tempFunc();
                    }
                }
            });
        }
        else {
            if (template.ExpressCompanyCode == 'SF' || template.ExpressCompanyCode == 'JD' || template.ExpressCompanyCode == 'BDB') {
                ep.GetExpressSustenance(template, tempFunc);
            } else {
                tempFunc();
            }
        }

    }

    //获取顺丰托寄物信息
    ep.GetExpressSustenance = function (template, callback) {
        //顺丰需要获取托寄物信息
        common.Ajax({
            url: '/TemplateSet/GetExpressSustenance',
            data: {
                templateId: template.Id
            },
            type: 'POST',
            async: true,
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.alert(rsp.Message, { skin: 'wu-dailog' });
                    return;
                }
                callback(rsp.Data);
            }
        });
    }

    // 扫描打印弹框
    ep.scanPrint = function (orders, template, printCount, isByHand, isCheckPrinted) {
        if (!ep.check(orders, template, false, null, printCount, isCheckPrinted, isByHand, true))
            return false;

        var pirntMethod = "NewPrint"; //拿货小标签默认使用新单号打印
        printComponents = common.GetUsePrintComponents(template.TemplateType);
        var printers = GetPrinter(printComponents);
        //参数初始化
        if (isCheckPrinted == undefined) isCheckPrinted = true;
        if (printCount == undefined || isNaN(printCount) == true) printCount = 1;

        printCount = parseInt(printCount);

        if (printCount <= 0) {
            layer.alert("打印数量必须为大于0的数字", { skin: 'wu-dailog' });
            return false;
        }

        otb.SetCheckReturnOrdersUrl("/Order/GetOrdersByApi");
        var refundOrders = otb.checkReturnOrders(orders);

        //快运包裹的长宽高 保留上一次写入的
        var packinfo = {};
        if (common.IsKuaiYunTemplate(template.TemplateType) ||
            common.IsPddKuaiYunTemplate(template.TemplateType, template.ExpressCompanyCode) ||
            common.IsTouTiaoKuaiYunTemplate(template.TemplateType) ||
            common.IsLinkKuaiYunTemplate(template.TemplateType)) {
            pirntMethod = pirntMethod == 'NewPrint' ? "NewPrintKuaiYun" : "PrintKuaiYun"; //打印快运面单

            if (common.IsKuaiYunTemplate(template.TemplateType) && template.ExpressCompanyCode == "SFKY") {
                var packStrInfo = $.cookie('print-express-lhwinfo-' + template.Id);
                if (packStrInfo != null && packStrInfo != "")
                    packinfo = JSON.parse(packStrInfo);
            }
        }
        //根据模板类型生成对应的请求数据？
        var dialog = $.templates("#print-express-dialog-tmpl");
        var showPintaiIcon = common.showPintaiIcon(template.TemplateTypeShort);
        var dialogData = {
            PrintMethod: pirntMethod, PrintCount: printCount, Orders: orders, Template: template, Printers: printers, defaultPrinter: ep.defaultPrinter, Packinfo: packinfo, PintaiIcon: showPintaiIcon
        };
        var html = dialog.render(dialogData);
        var area = '560px';
        var title = "打印快递单";
        if (pirntMethod == "OneToMany") {
           title += "【一单多包】";
           // area = ['695px', '280px'];
        }
        else if (pirntMethod == "NewPrint") {
           title += "【新单号打印】";
           // area = ['700px', '280px'];
        }
        else if (pirntMethod == "RePrint")
            title += "【重打】";
        else if (pirntMethod == "PrintKuaiYun")
            title += "打印快运单";

        title += "<strong style='color:red;' title='打印过程中，请勿进行刷新页面动作！刷新会导致打印状态标记失败'>(打印过程中，请勿进行刷新页面动作！刷新会导致打印状态标记失败)</strong>";
        var btnHandler = function (isPreview) {
            var this_btn = $('.layui-layer-btn1');
            var processing = this_btn.attr('processing'); //处理中的标记
            if (processing) {
                layer.alert('正在处理...请不要重复点击', { skin: 'wu-dailog' });
                return;
            }
            this_btn.attr('processing', true); //处理中的标记

            var printCount = 1;
            var printer = isByHand ? $("#express-printer-select").val() : $("#slt_printer").val();
            if (!printer) {
                layer.msg("请选择打印机");
                this_btn.removeAttr('processing'); //移除处理中标识
                return false;
            }
            if (pirntMethod == "NewPrint" || pirntMethod == "RePrint" || pirntMethod == "Normal") {
                printCount = isByHand ? $("#express-package-print-count").val() : 1;
            }
            if (pirntMethod == "OneToMany") {
                printCount = $("#express-package-print-count").val();
                if (printCount <= 0) {
                    layer.msg("打印一单多包时，打印数量必须大于等于1");
                    this_btn.removeAttr('processing'); //移除处理中标识
                    return false;
                }
            }
            if (pirntMethod == "PrintKuaiYun" || pirntMethod == "NewPrintKuaiYun") {
                printCount = $("#txt_package_order_count").val().trim();
                if (printCount <= 0 || isNaN(printCount)) {
                    layer.msg("打印快运面单时，子母件数量必须大于等于1");
                    this_btn.removeAttr('processing'); //移除处理中标识
                    return false;
                }
            }

            if (pirntMethod == "NewPrint" || pirntMethod == "RePrint" || pirntMethod == "Normal") {
                // 点击打印时--判断是否存在小红书平台的订单且打印方式为 【按包裹最多件数】--进行前置拦截
                var isHasXhs = orders.some(function (item) {
                    return item.PlatformType == "XiaoHongShu"
                });

                if (isHasXhs && printCount == '按包裹最多件数打印面单') {
                    var msgHtml = "已选的【小红书】订单，不支持按【包裹最多件数】打印。<br/>";
                    msgHtml += '请取消勾选<span class="wu-weight600">这些</span>订单，';
                    msgHtml += '或打印方式更换为<span class="wu-weight600">【按商品种类】</span>或<span class="wu-weight600">【按商品规格】</span>后重试。'
                    layer.alert(msgHtml, { title: "操作提示", skin: 'wu-dailog', area: '565px', btn: ["确定"] });
                    this_btn.removeAttr('processing'); //移除处理中标识
                    return false;
                }
            }

            var printerIndex = 0;
            if (printComponents == "Lodop") {
                printerIndex = printer;
                printer = $("#express-printer-select option[value='" + printer + "']").text();
            }

            if (printComponents == "Fengqiao") {
                printerIndex = printer;
                printer = $("#express-printer-select option[value='" + printer + "']").text();
            }

            //保存快运包裹信息记录
            if (commonModule.IsKuaiYunTemplate(template.TemplateType) && template.ExpressCompanyCode == "SFKY") {
                var isBool = _wirtePackInfo(template.Id);
                if (isBool == false) {
                    this_btn.removeAttr('processing'); //移除处理中标识
                    return false;
                }
            }

            // 扫描打印完成后，更新数据状态
            var printedCallback = function (orders, successWaybillCodes, isPreview) {
                var datas = [];
                $(orders).each(function (i, o) {
                    var order = { "Id": o.Id, "BatchNo": o.BatchNo, "PlatformOrderId": o.PlatformOrderId, "ShopId": o.ShopId };
                    order.OrderItems = [];
                    $(o.UpdateOrderItems).each(function (ii, oi) {
                        order.OrderItems.push({ "Id": oi.Id, "PlatformOrderId": oi.PlatformOrderId, "SubItemId": oi.SubItemId, "PrintedCount": oi.StockedCount, "StockedCount": oi.StockedCount });
                    });
                    datas.push(order);
                });

                //console.log(datas);

                commonModule.Ajax({
                    url: "/ScanProductPrint/UpdateScanPrintStatus",
                    data: { "PrintInfoJson": JSON.stringify(datas), "IsPreview": isPreview },
                    success: function (result) {
                        if (result.Success) {
                            //$(".print-success").show();
                            scanProductPrint.ShowMessage('success', '打印成功');
                            //var isPreview = result.Data;
                            if (!isPreview) {
                                // 扫描打印后是否自动发货    
                                scanProductPrint.send(isByHand);
                            }
                        }
                        else {
                            //layer.msg(result.Message, { icon: 2 });
                            //$(".print-error").html(result.Message).show();
                            scanProductPrint.ShowMessage('error', result.Message);
                        }
                    }
                });

                var waybillCods = "";
                common.Foreach(successWaybillCodes, function (i, w) {
                    waybillCods += w.WaybillCode + ",";
                });
                if (waybillCods != "")
                    $('#lbl_waybill_code').text(waybillCods.trimEndDgj(','));

            }
            //ep.scanDoPrint(orders, template, pirntMethod, printCount, isPreview, printer, printedCallback, printerIndex);

            // 检测退款中订单，确认是否打印
            if (refundOrders && refundOrders.length > 0) {
                layer.confirm('您选中的订单中有<span class="wu-color-b">' + refundOrders.length + "个订单是退款中的</span>，仍继续打印吗？", { skin: 'wu-dailog' },
                    function () {
                        ep.scanDoPrint(orders, template, pirntMethod, printCount, isPreview, printer, printedCallback, printerIndex);
                    }, function () {
                        layer.closeAll();
                    });
            } else
                ep.scanDoPrint(orders, template, pirntMethod, printCount, isPreview, printer, printedCallback, printerIndex);

            this_btn.removeAttr('processing'); //移除处理中标识
            this_btn.remove(); //直接移除按钮
            return false;
        };

        // 是否手动打印
        if (isByHand) {
            // 打印
            var btnPrintHandler = function () {
                btnHandler(false);
            }
            // 预览
            var btnPreviewHandler = function () {
                btnHandler(true);
            }

            // 修改模板
            var btn3Handler = function () {
                var templateId = template.Id;
                var templateType = template.TemplateType + "";
                var url = "";
                switch (templateType) {
                    case "1":
                        url = "/TemplateSet/EditTraditionTemplate?templateId=" + templateId;
                        break;
                    case "3":
                    case "2":
                    case "10":
                    case "13":
                        url = "/TemplateSet/EditSiteTemplate?templateId=" + templateId;
                        break;
                    case "4":
                    case "5":
                    case "6":
                    case "7":
                    case "8":
                    case "9":
                    case "17":
                    case "18":
                    case "19":
                    case "20":
                    case "21":
                    case "22":
                    case "23":
                        url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        break;
                    default:
                        if (templateType >= 40 && templateType < 60)
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        else if (templateType >= 60 && templateType < 70) {
                            url = "/TemplateSet/EditSiteTemplate?templateId=" + templateId;
                            break;
                        }
                        else if (templateType >= 91 && templateType < 100) {
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                            break;
                        }
                        else if (common.IsKuaiShouTemplate(templateType)) {
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                            break;
                        }
                        else if (common.IsPddTemplate(templateType) || common.IsPddKuaiYunTemplate(templateType)) {
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                            break;
                        }
                        else if (common.IsXiaoHongShuTemplate(templateType) || common.IsNewXiaoHongShuTemplate(templateType)) {
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                            break;
                        }
                        else if (common.IsWxVideoTemplate(templateType)) {
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                            break;
                        }
                        else {
                            layer.alert("未识别模板类型，无法修改", { skin: 'wu-dailog' });

                            break;
                        }

                }
                if (url)
                    window.open(common.rewriteUrlToMainDomain(url));
            };

            var btns = ["直接打印", "修改模板"];
            var opts = {};
            if (printComponents != "Lodop" && printComponents != "TouTiao" && printComponents != "KuaiShou" && printComponents != "WxVideo") {
                btns.splice(1, 0, "  预 览  "); //菜鸟电子面单才有预览
                opts.btn2 = btnPreviewHandler;
                opts.btn3 = btn3Handler;
            }
            if (template && common.IsWxVideoTemplate(template.TemplateType) && template.EwaybillTemplateId) {
                btns = ["直接打印"];
            }
            var options = {
                type: 1,
                title: title,
                btn: btns,
                area: area,
                skin: 'wu-dailog',
                content: html,
                btn1: btnPrintHandler,
                btn2: btn3Handler,
                id: 'print-express-dialog',
            };
            var openOpts = $.extend({}, options, opts);
            layer.open(openOpts);
            $("#express-printer-select").val($("#slt_printer").val());
        }
        else {
            btnHandler(false);
        }
    }

    // 开始扫描打印
    ep.scanDoPrint = function (orders, template, printType, packageCount, isPreView, printerName, printedCallback, printerIndex) {
        document_total_count = 0; //此次打印总文档数，用于生成面单序号
        var packageCount = packageCount;//$("#express-package-print-count").val();

        var desc = ep.GetPackageAndGoodsDesc(printType, template); //获取包装描述，产品平类
        var request = {
            TemplateId: template.Id,
            PackageCount: parseInt(packageCount),
            PackageDesc: desc.packageDesc,
            GoodsDesc: desc.goodsDesc,
            PrintMethod: printType,
            PrinterName: printerName,
            IsPreview: isPreView,
            IsScanPrint: true,
            PackageLength: desc.packageLength,
            PackageWidth: desc.packageWidth,
            PackageHeight: desc.packageHeight,
            Branches: template.Branches // 传递从CheckTemplateAvaliable获取的Branches数据
        };
        ep.defaultPrinter = printerName;

        var toFullNameIsNullOrders = [];
        var models = [];
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var model = {
                WaybillCodeId: "", //printedPackageId ? printedPackageId: "",
                Id: order.Id,
                PlatformOrderId: order.PlatformOrderId,
                CustomerOrderId: order.CustomerOrderId,
                ShopId: order.ShopId,
                PrintInfo: order.PrintInfo,
                OrderItems: order.OrderItems,
                WaybillCode: order.WaybillCode,
                SellerRemark: order.SellerRemark,
                BuyerRemark: order.BuyerRemark,
                Receiver: {
                    toFullName: common.DeleteRareWords(order.Receiver.ToName),
                    toMobile: order.Receiver.ToMobile,
                    toArea: common.DeleteRareWords(order.Receiver.ToAddress),
                    toProvince: order.Receiver.ToProvince,
                    toCity: order.Receiver.ToCity,
                    toCounty: order.Receiver.ToCounty,
                    toMaskAddress: order.Receiver.ToMaskAddress,
                    buyerMemberId: order.Buyer.BuyerMemberId
                },
                Sender: {
                    SenderName: common.DeleteRareWords(order.Sender.SenderName),
                    SenderPhone: order.Sender.SenderPhone,
                    SenderAddress: common.DeleteRareWords(order.Sender.SenderAddress),
                    CompanyName: order.Sender.SenderCompany,
                },
                Buyer: {
                    BuyerMemberId: order.Buyer.buyerMemberId,
                    BuyerMemberName: order.Buyer.BuyerMemberName,
                    BuyerWangWang: order.Buyer.BuyerWangWang
                }
            };
            if (order.ChildOrderId)
                model.ChildOrderId = order.ChildOrderId;

            if (model.Receiver.toFullName == "") {
                toFullNameIsNullOrders.push(model.PlatformOrderId);
            }

            models.push(model);
        };

        if (toFullNameIsNullOrders.length > 0) {
            layer.alert("订单【" + toFullNameIsNullOrders.join(",") + "】收件人姓名为空或者全为特殊字符，会导致打单失败，请更改后再打印。", { skin: 'wu-dailog' },
            function (index) {
                layer.closeAll("");
            });
        }

        //删除危险字符
        models = JSON.parse(common.DelDangerChar(JSON.stringify(models)));
        var total_data_count = models.length; //打印的总订单数
        var models_copy = JSON.parse(JSON.stringify(models));

        function GetRequestModel(orders) {
            request.Orders = orders;
            return request;
        }

        var callback_counter = 0;
        function SingleCallBack(r) {
            try {

                callback_counter++;
                var temp_count = (callback_counter * orderPrintBatchNumber);
                var temp_number = (temp_count / total_data_count) * 100;
                var bar_width = 0;
                if (temp_number < 15)
                    bar_width = temp_number.toFixed(0);
                else
                    bar_width = temp_number.toFixed(2);
                if (bar_width > 100) bar_width = 100;
                $('#div_pro_bar').css({ width: (bar_width + '%') });
                $('#div_bar_text').text(bar_width + '%');

                if (temp_count > total_data_count) temp_count = total_data_count;
                $('#sp_curr_number').text(temp_count);

                $('#hfIsContinueForStock').val('0');    //重置部分无货是否继续标记

            } catch (e) {
                var errorMsg = "单个请求回调异常》" + e.stack;
                //console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }
            //console.log(r);
        }

        function AllDoneCallback(allRsp, requestDatas) {
            isIgnoreWaybillRepeat = false; //重置 忽略订单已打印的后端拦截
            document_total_count = 0; //先清空总面单数

            $('#hfIsContinueForStock').val('0');    //重置部分无货是否继续标记

            //先记录起所有请求返回的数据
            try {
                if (typeof printExpressAllRspLog != 'undefined' && printExpressAllRspLog == true) {
                    common.JsLogToMongoDB("所有请求回来后记录数据", JSON.stringify(allRsp));
                }
            } catch (e) {
                var errorMsg = "所有请求完成，记录所有请求的数据异常》" + e.stack;
                //console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            var printing = common.LoadingMsg("正在打印，请不要刷新或者关闭页面");

            //将所有返回结果汇总
            var result = null;
            var faildRsp = {}; //失败的响应

            try {
                var requestDict = {}; //请求数据字典{orderId+platformOrderId+shopId：次数}，用于校验请求回来的数据是否是请求发送的数据
                for (var i = 0; i < requestDatas.length; i++) {
                    var req = requestDatas[i];
                    var key = req.Id + "_" + req.PlatformOrderId + "_" + req.ShopId;
                    requestDict[key] = 1;
                }

                var notInRequestDict = []; //后台返回的数据不存在请求数据中的数据    
                var documentDict = {}; //已经汇总的文档{运单号：次数}（一个文档会打印出一个面单）   
                var taskDict = {}; //汇总发送给菜鸟组件的请求，按请求批次大小数量来决定一个task包含的documents数量。目前默认是10订单一个批次。同时要保证每个task的id必须唯一。

                //汇总
                if (Array.isArray(allRsp) && allRsp.length > 0) {
                    //将所有回来的请求数据根据请求批次号排序，保证订单按顺序打印
                    allRsp = common.SortExt(allRsp, "Data.RequestBatchNumber", false, true);
                    for (var i = 0; i < allRsp.length; i++) {
                        var tempR = allRsp[i];
                        if (tempR.Success == false) {
                            faildRsp[tempR.RequestBatch] = tempR;
                            continue;
                        }
                        if (result == null) {
                            result = tempR;
                            if (result.Data.PrintDataList) {
                                //将task汇总到taskList里(按请求批次大小数量来决定一个task包含的documents数量)
                                var request_first = JSON.parse(JSON.stringify(result.Data.PrintDataList));
                                request_first.requestID = request_first.requestID + "_" + getOnlyCode(); //保证requestID唯一
                                request_first.task.firstDocumentNumber = 1; //第一个请求面单打印序号从1开始
                                document_total_count += request_first.task.documents.length; //汇总文档数
                                taskDict[request_first.task.taskID] = request_first; //task汇入字典，用于后续判断taskID是否重复
                                result.Data.PrintDataList.TaskList = [];
                                result.Data.PrintDataList.TaskList.push(request_first);
                            }
                        }
                        else {
                            result.Data.successCount += tempR.Data.successCount; //汇总成功数
                            result.Data.errorCount += tempR.Data.errorCount;     //汇总失败数
                            //汇总waybillCode数据
                            if (Array.isArray(tempR.Data.WaybillCode) == true && tempR.Data.WaybillCode.length > 0) {
                                for (var j = 0; j < tempR.Data.WaybillCode.length; j++) {
                                    var tempWaybillCode = tempR.Data.WaybillCode[j];
                                    var key = tempWaybillCode.OrderInfo.OrderInfo.Id + "_" + tempWaybillCode.OrderInfo.OrderInfo.PlatformID + "_" + tempWaybillCode.OrderInfo.OrderInfo.ShopId;
                                    if (!requestDict[key]) {
                                        //请求回来的数据不存在于请求数据中
                                        notInRequestDict.push(tempWaybillCode);
                                    }
                                    result.Data.WaybillCode.push(tempWaybillCode);
                                }
                            }
                            //汇总lodop组件打印的数据
                            if (Array.isArray(tempR.Data.TemplateList) == true && tempR.Data.TemplateList.length > 0) {
                                for (var j = 0; j < tempR.Data.TemplateList.length; j++) {
                                    result.Data.TemplateList.push(tempR.Data.TemplateList[j]);
                                }
                            }
                            //汇总打印记录id
                            if (Array.isArray(tempR.Data.PrintHistoryIds) == true && tempR.Data.PrintHistoryIds.length > 0) {
                                for (var j = 0; j < tempR.Data.PrintHistoryIds.length; j++) {
                                    result.Data.PrintHistoryIds.push(tempR.Data.PrintHistoryIds[j]);
                                }
                            }
                            //汇总非lodop组件打印的数据（菜鸟、拼多多组件）
                            if (tempR.Data.PrintDataList != null && tempR.Data.PrintDataList.task && Array.isArray(tempR.Data.PrintDataList.task.documents) && tempR.Data.PrintDataList.task.documents.length > 0) {
                                var tempTask = tempR.Data.PrintDataList.task;
                                tempTask.firstDocumentNumber = document_total_count + 1; //面单开始序号
                                var temp_documents = []; //临时保存这个批次的douments(一个document一个面单）
                                for (var j = 0; j < tempTask.documents.length; j++) {
                                    var tempDoc = tempTask.documents[j];
                                    var docDictVal = documentDict[tempDoc.documentID];
                                    if (!docDictVal) {
                                        //不存在，数量初始为1
                                        documentDict[tempDoc.documentID] = 1;
                                        //不存在，则加入临时集合
                                        temp_documents.push(tempDoc);
                                        document_total_count++; //汇总文档数
                                    }
                                    else {
                                        //存在，则次数加一
                                        documentDict[tempDoc.documentID] = (docDictVal + 1);
                                    }
                                }
                                //判断taskID是否存在
                                if (taskDict[tempTask.taskID]) {
                                    //存在，则重新生成taskID
                                    tempTask.taskID = getOnlyCode();
                                }
                                //将新documents（排除了已存在的document，防止重复）覆盖task的documents
                                tempTask.documents = temp_documents;
                                result.Data.PrintDataList.TaskList.push({
                                    cmd: 'print',
                                    requestID: "requestID_" + getOnlyCode(),
                                    task: tempTask
                                });
                            }
                        }
                        result.Data.totalPrintNum = total_data_count; //+= tempR.Data.totalPrintNum;
                    }
                }
                else {
                    if (allRsp.Success == false)
                        faildRsp[1] = allRsp;
                    else
                        result = allRsp;
                }

                if (result == null) {
                    //全部失败
                    result = {
                        Success: true,
                        Data: {
                            Template: template,
                            IsError: true,
                            totalPrintNum: total_data_count,
                            successCount: 0,
                            errorCount: 0,
                            WaybillCode: []
                        }
                    };
                }
            } catch (e) {
                var errorMsg = "所有请求完成，数据解析异常》" + e.stack;
                //console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            try {
                //汇总错误
                for (var i in faildRsp) {
                    var batchNumber = parseInt(i);
                    var tempSize = orderPrintBatchNumber;
                    if ((batchNumber * orderPrintBatchNumber) < models_copy.length) {
                        result.Data.errorCount += orderPrintBatchNumber;
                    }
                    else {
                        var errorCount = (models_copy.length % orderPrintBatchNumber) || orderPrintBatchNumber;
                        result.Data.errorCount += errorCount;
                        tempSize = errorCount;
                    }

                    var batchPlatformOrderIds = '批次' + batchNumber + "：";

                    var idx = (batchNumber - 1) * orderPrintBatchNumber;
                    for (var j = idx; j < idx + tempSize; j++) {
                        batchPlatformOrderIds += models_copy[j].PlatformOrderId + ',';
                    }
                    if (faildRsp[i].ErrorCode == "wbl_auth_expired") {
                        result.Data.WaybillCode.push({
                            IsError: true,
                            AgentName: faildRsp[i].Data.AgentName,
                            BatchPlatformOrderIds: batchPlatformOrderIds.trimEndDgj(','),
                            ErrMsg: faildRsp[i].Data.Message,
                            Solution: faildRsp[i].Data.Solution
                        });
                    }
                    else {
                        result.Data.WaybillCode.push({
                            IsError: true,
                            BatchPlatformOrderIds: batchPlatformOrderIds.trimEndDgj(','),
                            ErrMsg: faildRsp[i].Message
                        });
                    }
                }
            } catch (e) {
                var errorMsg = "所有请求完成，汇总错误异常》" + e.stack;
                //console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            //校验服务器返回的数据与请求发送的数据的一致性
            try {
                var difference = {
                    RspsNotInReqs: [], //返回的数据不在请求中
                    RepeatDocuments: [], //返回的重复面单
                }; //差异对象
                //后台返回的数据不在请求数据中
                if (notInRequestDict.length > 0) {
                    difference.RspsNotInReqs = notInRequestDict;
                }
                for (var key in documentDict) {
                    if (documentDict[key] > 1) {
                        difference.RepeatDocuments.push(key); //重复的面单号
                    }
                }

                //数据不一致，则记录日志
                if (difference.RspsNotInReqs.length > 0 || difference.RepeatDocuments.length > 0) {
                    common.JsLogToMongoDB("快递单打印后端返回的数据和请求数据不一致", JSON.stringify(difference));
                }

            } catch (e) {
                var errorMsg = "数据不一致日志记录报错》" + e.stack;
                //console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            //处理返回的结果
            if (!result.Success) {
                try {
                    layer.closeAll();
                    if (result.ErrorCode) {
                        switch (result.ErrorCode) {
                            case 'branch_not_exist':
                                var url = common.rewriteUrlToMainDomain('/AccountList/');
                                layer.alert(result.Message + " <a href='" + url + "'>点击去电子面单账号管理</a>", { skin: 'wu-dailog' });
                                break;
                            case 'branch_addr_change':
                                var url = common.rewriteUrlToMainDomain('/TemplateSet/');
                                layer.alert(result.Message + " <a href='" + url + "'>点击去模板管理</a>", { skin: 'wu-dailog' });
                                break;
                            case 'taobao_branch_errcode27':
                                branch_past(template, false);
                                break;
                            default:
                                layer.alert(result.Message, { skin: 'wu-dailog' });
                                break;
                        }
                    }
                    else {
                        layer.alert(result.Message, { skin: 'wu-dailog' });
                    }
                } catch (e) {
                    var errorMsg = "所有请求完成，汇总提示异常》" + e.stack;
                    //console.log(errorMsg);
                    common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                }
            } else {
                var data = result.Data;
                if (data.successCount > 0) {
                    try {
                        //1.添加面单到订单行对象
                        var successWaybillCodes = [];
                        $(data.WaybillCode).each(function (index, w) {
                            if (!w.IsError) {
                                //var row = null;//otb.rows[ix];
                                //common.Foreach(otb.rows, function (i, o) {
                                //    if (o.Id == w.OrderId) {
                                //        row = o;
                                //        return 'break;';
                                //    }
                                //});

                                //if (row == null) {
                                //    throw "根据订单Id【" + w.OrderId + "】未找到订单Row对象";
                                //}
                                ////row.LastExpressPrintTime = 1;
                                ////添加到单号中,最新打印，加到前面
                                //if (!row.WaybillCodes) row.WaybillCodes = [];
                                //row.WaybillCodes.unshift({
                                //    TemplateId: template.Id,
                                //    TemplateType: template.TemplateType,
                                //    ExpressCpCode: template.ExpressCompanyCode,
                                //    WaybillCodeId: w.WaybillCodeId,
                                //    WaybillCode: w.WaybillCode,
                                //    CompanyCode: template.ExpressCompanyCode
                                //});

                                successWaybillCodes.push(w);
                                //赋值orders 对象中的waybillcode字段，让预览后点击打印按钮，后台能收到预览的waybillcode，从而打印出相同的单号
                                common.Foreach(orders, function (i, o) {
                                    if (o.Id == w.OrderId) {
                                        o.WaybillCode = w.WaybillCode;
                                        $('.scanPrint-table-title[data-pid="' + o.Id + '"]').attr("data-wcode", w.WaybillCode);
                                    }
                                });
                                scanProductPrint.PrintedOrders = orders;
                            }
                        });

                    } catch (e) {
                        var p_msg = "";
                        try {
                            p_msg += JSON.stringify(data);
                            p_msg += '此时订单行数据：' + JSON.stringify(otb.rows);
                        } catch (ex) {
                            wcs = "data 或 otb.rows 对象转String报错了." + ex.stack;
                        }
                        var errorMsg = "添加面单到订单行对象异常》" + p_msg + "》" + e.stack;
                        //console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    try {
                        //2.更新页面状态
                        otb.changeTemplate(template);
                    } catch (e) {
                        var errorMsg = "更新页面状态异常》" + e.stack;
                        //console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    try {
                        //3.记录打印日志或者预览打印操作设置
                        if (isPreView == false) {
                            _PrintedCallbackUrl = "/ScanPrint/PrintCallback";
                            //记录打印日志
                            _writePrintLogToServer(data, successWaybillCodes, template, printerName, orders, false);
                        }
                        else {
                            //设置 预览页面的打印按钮功能 //doPrint(orders, template, printType, packageCount, isPreView, printerName, printedCallback, printerIndex)
                            common.SetPrintAction(ep.scanDoPrint.bind(this, orders, template, 'Normal', packageCount, false, printerName, printedCallback, printerIndex));

                            //更新预览标志
                            _wirtePreviewFlagToOrder(orders);
                        }
                    } catch (e) {
                        var errorMsg = "记录打印日志或者预览打印操作设置异常》" + e.stack;
                        //console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    try {
                        data.ExpressCompanyCode = template.ExpressCompanyCode; //复制快递编码，用于控制 运单号条形码显不显示 文字的问题。
                        //4.发送到打印机打印
                        ep.postPrintCommond(data, printerName, printerIndex, isPreView);

                        //5.绑定打印机信息
                        common.SetPrinterBind(template.Id, 1, printerName, true);

                        //6.保存普通模板 单号
                        common.SaveTraditionWaybillCodeConfig(template);

                        //检查可用单号
                        //addTmplInOrderListModule.CheckQty(false, template);

                    } catch (e) {
                        var errorMsg = "4，5，6步异常》" + e.stack;
                        //console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    if (data.errorCount <= 0)
                        layer.closeAll();
                    if (typeof printedCallback == 'function')
                        printedCallback(orders, successWaybillCodes, isPreView);
                }
                layer.close(printing);
                if (data.errorCount > 0) {
                    //显示错误信息
                    data.Template = template;
                    _showErrorMessage(data);
                }
            }
        }

        try {
            //commonModule.ExpressRequestDatas = models;
            //ep.ExpressRequestDatas = models;
            ///Order/ExpressPrint  /Common/TestBatchAjax
            common.posts('/ScanPrint/PrintOrder', models, GetRequestModel, SingleCallBack, AllDoneCallback, orderPrintBatchNumber);
        } catch (e) {
            var errorMsg = "分批发送请求异常》" + e.stack;
            //console.log(errorMsg);
            common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
        }
    }


    //打印快递单：一单多包
    ep.printOneToMany = function printOneToMany(orderId) {
        var orders = otb.getSelections(orderId);
        try {
            var templateType = addTmplInOrderListModule.GetCurrentTemplate().TemplateType;
            printComponents = common.GetUsePrintComponents(templateType);
            if (printComponents == "KuaiShou") {
                layer.open({
                    type: 1,
                    title: "提示",
                    btn: null,
                    area: ["560px"],
                    skin: 'wu-dailog',
                    content: $('#div_kuaiShouRefuseOneToMore')
                });
                return false;

            }
            var isExistKuaishou = false;
            for (var i = 0; i < orders.length; i++) {
                var o = orders[i];
                if (o.PlatformType == "KuaiShou") {
                    isExistKuaishou = true;
                    break;
                }
            }

            //校验抖店快手订单与打印模板关系
            if (!waitOrderModule.CheckTemplate(orders)) { return; }

            //丰桥模板，提示升级云模板
            if (templateType == 10) {
                waitOrderModule.FqTempUprate(function () { doPrint(orders); });
                return;
            }

            //快手模板打印公告
            if (isExistKuaishou && common.IsKuaiShouTemplate(templateType) == false) {
                waitOrderModule.ShowOrderUprate(function () { doPrint(orders); }, "kstemp_uprate");
                return;
            }
            doPrint(orders);
        } catch (e) {
            //ep.print(orders, "OneToMany");
            doPrint(orders);
        }

        function doPrint(os) {
            ep.print(os, "OneToMany");
        }
    }

    //打印快递单：重新打印
    ep.reprint = function reprint(orderId, printedPackageId) {
        var orders = otb.getSelections(orderId);
        ep.print(orders, "RePrint", printedPackageId);
    }

    //打印快递单：新单号打印
    ep.printNew = function newPrint(orderId) {
        var orders = otb.getSelections(orderId);
        ep.print(orders, "NewPrint");
    }

    //打印快递单：普通打印（默认）
    ep.printNormal = function normalPrint(orderId) {
        var orders = otb.getSelections(orderId);
        ep.print(orders, "Normal");
    }

    ep.defaultPrinter = "";
    ep.bindPrinter = function () {
        var p = $("#express-printer-select option:selected").text();
        var t = $("#send-good-template-template-select").val();
        var template = addTmplInOrderListModule.GetCurrentTemplate();
        if (template)
            t = template.Id;
        ep.defaultPrinter = p;
        common.SetPrinterBind(t, 1, p);
    }

    ep.GetPackageAndGoodsDesc = function (printType, template) {
        var packageDesc = null, goodsDesc = null; packageLength = null; packageWidth = null; packageHeight = null; //包装描述，产品品类
        if (printType == 'PrintKuaiYun' || printType == 'NewPrintKuaiYun' || commonModule.IsTouTiaoKuaiYunTemplate(template.TemplateType)) {
            var packageDescCtrol = $('#sel_package_desc'), goodsDescCtrol = $('#sel_goods_desc');
            if (packageDescCtrol.length > 0 && packageDescCtrol.val().trim() != '0' && packageDescCtrol.val().trim() != '') {
                packageDesc = packageDescCtrol.val().trim();
            }
            if (goodsDescCtrol.length > 0 && goodsDescCtrol.val().trim() != '0' && goodsDescCtrol.val().trim() != '') {
                goodsDesc = goodsDescCtrol.val().trim();
            }
        }

        if (commonModule.IsKuaiYunTemplate(template.TemplateType) && template.ExpressCompanyCode == "SFKY") {
            var packStrInfo = $.cookie('print-express-lhwinfo-' + template.Id);
            if (packStrInfo != null && packStrInfo != "" && packStrInfo != undefined) {
                var packinfo = JSON.parse(packStrInfo);
                packageLength = packinfo.Length;
                packageWidth = packinfo.Width;
                packageHeight = packinfo.Height;
            }
            else {
                var txt_package_length = $('#txt_package_length'), txt_package_width = $('#txt_package_width'), txt_package_height = $('#txt_package_height');
                if (txt_package_length != null && txt_package_length != "0") {
                    packageLength = txt_package_length.val().trim();
                }
                if (txt_package_width != null && txt_package_width != "0") {
                    packageWidth = txt_package_width.val().trim();
                }
                if (txt_package_height != null && txt_package_height != "0") {
                    packageHeight = txt_package_height.val().trim();
                }
            }
        }

        ////极兔选择物品类型
        //if (commonModule.IsSiteTemplate(template.TemplateType) && template.ExpressCompanyCode == "JT") {
        //    goodsDesc = $('#sel_goods_desc').val().trim();
        //}

        return { packageDesc: packageDesc, goodsDesc: goodsDesc, packageLength: packageLength, packageWidth: packageWidth, packageHeight: packageHeight };
    }

    //托寄物
    ep.GetExpressSustenanceRequest = function (template) {
        var name = null, desc = null;
        // 需要寄托物的快递
        var validCodes = ['SF', 'JD', 'BDB'];
        if (validCodes.indexOf(template.ExpressCompanyCode) !== -1) {
            var packageDescCtrol = $('#sel_sustenance_desc'), goodsDescCtrol = $('#ipt-sustenance-desc');
            name = packageDescCtrol.val().trim();

            if (packageDescCtrol.val() == '自定义') {
                desc = goodsDescCtrol.val().trim();
                if (desc == '') {
                    $('.layui-layer-btn0').removeAttr('processing'); //移除处理中标识
                    layer.alert("自定义寄托物不能为空", { skin: 'wu-dailog' });
                    return false;
                }
            }
        }

        return { name: name, desc: desc };
    }

    //开始打印 
    //必填参数 orders订单数据
    //必填参数 template 模板数据
    //必填参数 printType 有四个值：Normal 正常打印 需获取上一次打印的包裹ID，OneToMany 一单多包，Reprint 重打， NewPrint 新单号打印
    //必填参数 packageCount 面单数量，仅当打印一单多包和快运时需提供，否则默认为1
    //必填参数 isPreView 是否是预览
    //必填参数 PrinterName 打印机名称
    //选填参数 printedCallback：打印后的回调函数
    ep.IsPrinting = false;
    var document_total_count = 0; //此次打印总文档数，用于生成面单序号
    ep.doPrint = function doPrint(orders, template, printType, packageCount, isPreView, printerName, printedCallback, printerIndex) {
        var packageCount = packageCount;//$("#express-package-print-count").val();

        var desc = ep.GetPackageAndGoodsDesc(printType, template); //获取包装描述，产品平类

        //顺丰托寄物
        var expressSustenanceDesc = ep.GetExpressSustenanceRequest(template);
        if (expressSustenanceDesc == false) { return; }

        var request = {
            TemplateId: template.Id,
            PackageCount: parseInt(packageCount),
            PackageDesc: desc.packageDesc,
            GoodsDesc: desc.goodsDesc,
            PrintMethod: printType,
            IsIgnoreWaybillRepeat: isIgnoreWaybillRepeat,
            PrinterName: printerName,
            IsPreview: isPreView,
            IsContinueForStock: $('#hfIsContinueForStock').val() == "1",    //部分订单项库存不足，是否继续
            PackageLength: desc.packageLength,
            PackageWidth: desc.packageWidth,
            PackageHeight: desc.packageHeight,
            ExpressSustenance: expressSustenanceDesc,
            Branches: template.Branches // 传递从CheckTemplateAvaliable获取的Branches数据
        };
        ep.defaultPrinter = printerName;

        var toFullNameIsNullOrders = [];
        var models = [];
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var model = {
                WaybillCodeId: "", //printedPackageId ? printedPackageId: "",
                Index: (i + 1), // 用于后端记录打印顺序
                Id: order.Id,
                PlatformOrderId: order.PlatformOrderId,
                LogicOrderId: order.LogicOrderId,
                PathFlowCode: order.PathFlowCode,
                CustomerOrderId: order.CustomerOrderId,
                ShopId: order.ShopId,
                PrintInfo: order.PrintInfo,
                OrderItems: order.OrderItems,
                Items: order.Items,
                WaybillCode: order.WaybillCode,
                SellerRemark: order.SellerRemark,
                BuyerRemark: order.BuyerRemark,
                FxUserId: order.FxUserId,
                IsSelectAllNotPrintedItem: order.IsSelectAllNotPrintedItem,
                SendType: order.SendType == undefined ? 0 : order.SendType,//发货类型
                DataFlag: order.DataFlag,
                Receiver: {
                    toFullName: common.DeleteRareWords(order.Receiver.ToName),
                    toMobile: order.Receiver.ToMobile,
                    toArea: common.DeleteRareWords(order.Receiver.ToAddress),
                    toProvince: order.Receiver.ToProvince,
                    toCity: order.Receiver.ToCity,
                    toCounty: order.Receiver.ToCounty,
                    toMaskAddress: order.Receiver.ToMaskAddress,
                    buyerMemberId: order.Buyer.BuyerMemberId
                },
                Sender: {
                    SenderName: common.DeleteRareWords(order.Sender.SenderName),
                    SenderPhone: order.Sender.SenderPhone,
                    SenderAddress: common.DeleteRareWords(order.Sender.SenderAddress),
                    CompanyName: order.Sender.SenderCompany,
                },
                Buyer: {
                    BuyerMemberId: order.Buyer.buyerMemberId,
                    BuyerMemberName: order.Buyer.BuyerMemberName,
                    BuyerWangWang: order.Buyer.BuyerWangWang
                }
            };

            if (model.Receiver.toFullName == "") {
                toFullNameIsNullOrders.push(model.PlatformOrderId);
            }
            models.push(model);
        };

        if (toFullNameIsNullOrders.length > 0) {
            layer.alert("订单【" + toFullNameIsNullOrders.join(",") + "】收件人姓名为空或者全为特殊字符，会导致打单失败，请更改后再打印。", { skin: 'wu-dailog' },function (index) { layer.closeAll(""); })
        }

        //删除危险字符
        models = JSON.parse(common.DelDangerChar(JSON.stringify(models)));

        var total_data_count = models.length; //打印的总订单数
        var models_copy = JSON.parse(JSON.stringify(models));

        function GetRequestModel(orders) {
            request.Orders = orders;

            if (printType == 'OneToMany' || printType == 'NewPrint' || printType == 'RePrint' || printType == 'Normal') {
                var _val = $('#express-package-print-count').val();
                if (_val == "按包裹最多件数打印面单") {
                    request.PackageType = 3
                    request.PackageSkuNum = $("#package-contain-product-count").val();
                } else if (_val == "按订单商品规格打印面单") {
                    request.PackageType = 2
                } else if (_val == "按订单商品种类打印面单") {
                    request.PackageType = 1
                }

                // 智能拆分打印
                if (isNaN(_val)) {

                    // 获取当前打印批次的Id
                    var batchOrderIds = orders.map(function (item) {
                        return item.Id
                    });

                    // 拷贝智能拆分打印的数据--以备下个批次打印继续过滤
                    var inteOrder = JSON.parse(JSON.stringify(InteOrders));

                    // 过滤当前批次打印的数据
                    var reqOrder = inteOrder.filter(function (item) {
                        return batchOrderIds.indexOf(item.Id) > -1
                    });

                    request.Orders = reqOrder;
                    request.PackageCount = 1;
                }
            }

            return request;
        }

        var callback_counter = 0;
        function SingleCallBack(r) {

            try {
                callback_counter++;
                var temp_count = (callback_counter * orderPrintBatchNumber);
                var temp_number = (temp_count / total_data_count) * 100;
                var bar_width = 0;
                if (temp_number < 15)
                    bar_width = temp_number.toFixed(0);
                else
                    bar_width = temp_number.toFixed(2);
                if (bar_width > 100) bar_width = 100;
                $('#div_pro_bar').css({ width: (bar_width + '%') });
                $('#div_bar_text').text(bar_width + '%');

                if (temp_count > total_data_count) temp_count = total_data_count;
                $('#sp_curr_number').text(temp_count);

                $('#hfIsContinueForStock').val('0');    //重置部分无货是否继续标记

            } catch (e) {
                var errorMsg = "单个请求回调异常》" + e.stack;
                //console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }
            //console.log(r);
        }

        function AllDoneCallback(allRsp, requestDatas) {
            isIgnoreWaybillRepeat = false; //重置 忽略订单已打印的后端拦截
            //库存错误处理
            try {
                ep.IsPrinting = false;
                $('#hfIsContinueForStock').val('0');    //重置部分无货是否继续标记
                if (allRsp[0].Message == '10') {
                    var msgData = JSON.parse(allRsp[0].Data.msg);
                    var tempdata = { Items: msgData };
                    var dialog = $.templates("#Stock-error-dialog-tmpl");
                    var errorHtml = dialog.render(tempdata);
                    layer.closeAll();
                    layer.open({
                        type: 1,
                        title: false,
                        closeBtn: 1,
                        skin: 'layui-layer-rim', //加上边框
                        area: ['600px', '350px'],
                        shadeClose: true,
                        content: errorHtml
                    });
                    return;
                }
            } catch (e) { }

            var getPrintDocuments = function (allRsp) {
                var documents = [];
                for (var i = 0; i < allRsp.length; i++) {
                    var rsp = allRsp[i];
                    if (!rsp.Success || rsp.errorCount > 0) {
                        continue;
                    }
                    for (var j = 0; j < rsp.Data.WaybillCode.length; j++) {
                        var wc = rsp.Data.WaybillCode[j];
                        var docs = rsp.Data.PrintDataList.task.documents;
                        var index = 0;
                        //快手返回的documentID是以子单号为主的，子单号不存在时才会赋值母单号。
                        var waybillCode = (wc.ChildWaybillCode === null || wc.ChildWaybillCode === undefined || wc.ChildWaybillCode.trim() === "") ? wc.WaybillCode : wc.ChildWaybillCode;
                        for (var k = 0; k < docs.length; k++) {
                            var doc = docs[k];
                            if (doc.documentID.indexOf(waybillCode) != -1) {
                                documents.push(doc);
                                index++;
                            }
                            else {
                                if (index > 0)
                                    index++;
                            }
                            if (index >= 2)
                                break;
                        }
                    }
                }
                return documents;
            }


            var documents = [];
            if (printComponents == "KuaiShou")
                documents = getPrintDocuments(allRsp);

            document_total_count = 0; //先清空总面单数
            var taskDocumentCount = otb.Setting.CaiNiaoBatchPrintCount;
            var tempAllRsp = [];
            //若配置菜鸟发送按一个一个来，则拆开请求(默认配置是10个)
            //快手打印documents不能超过10个
            if ((documents && documents.length > 10 && printComponents == "KuaiShou") ||
                (taskDocumentCount != 10 && !isPreView && (printComponents == "Cainiao" || printComponents == "Pinduoduo" || printComponents == "TouTiao"))) {
                taskDocumentCount = 1;
                for (var i = 0; i < allRsp.length; i++) {
                    var rsp = allRsp[i];
                    if (!rsp.Success || rsp.errorCount > 0) {
                        tempAllRsp.push(rsp);
                        continue;
                    }
                    for (var j = 0; j < rsp.Data.WaybillCode.length; j++) {
                        var wc = rsp.Data.WaybillCode[j];
                        var tempRsp = JSON.parse(JSON.stringify(rsp));
                        tempRsp.Data.PrintDataList.task.documents = [];
                        tempRsp.Data.PrintDataList.task.taskID += "_" + i + "_" + j;
                        tempRsp.Data.WaybillCode = [];
                        tempRsp.Data.WaybillCode.push(wc);
                        tempRsp.Data.PrintHistoryIds = [];
                        if (wc.WaybillCode) {
                            var phIds = rsp.Data.PrintHistoryIds;
                            for (var k = 0; k < phIds.length; k++) {
                                var phId = phIds[k];
                                if (phId.WaybillCode == wc.WaybillCode) {
                                    tempRsp.Data.PrintHistoryIds.push(phId);
                                    break;
                                }
                            }
                        }
                        if (wc.WaybillCode) {
                            tempRsp.Data.errorCount = 0;
                            tempRsp.Data.successCount = 1;
                        }
                        else {
                            tempRsp.Data.errorCount = 1;
                            tempRsp.Data.successCount = 0;
                        }
                        tempRsp.totalPrintNum = 1;
                        var docs = rsp.Data.PrintDataList.task.documents;
                        var index = 0;
                        var waybillCode;
                        //快手平台做兼容处理。快手返回的documentID是以子单号为主的，子单号不存在时才会赋值母单号。
                        if (printComponents == "KuaiShou") {
                            waybillCode = (wc.ChildWaybillCode === null || wc.ChildWaybillCode === undefined || wc.ChildWaybillCode.trim() === "") ? wc.WaybillCode : wc.ChildWaybillCode;
                        } else {
                            waybillCode = wc.WaybillCode;
                        }
                        for (var k = 0; k < docs.length; k++) {
                            var doc = docs[k];
                            if (doc.documentID.indexOf(waybillCode) != -1) {
                                tempRsp.Data.PrintDataList.task.documents.push(doc);
                                index++;
                            }
                            else {
                                if (index > 0)
                                    index++;
                            }
                            if (index >= 2)
                                break;
                        }
                        tempAllRsp.push(tempRsp);
                    }

                }
                allRsp = tempAllRsp;
            }

            //先记录起所有请求返回的数据
            try {
                if (typeof printExpressAllRspLog != 'undefined' && printExpressAllRspLog == true) {
                    common.JsLogToMongoDB("所有请求回来后记录数据", JSON.stringify(allRsp));
                }
            } catch (e) {
                var errorMsg = "所有请求完成，记录所有请求的数据异常》" + e.stack;
                //console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            var printing = common.LoadingMsg("正在打印，请不要刷新或者关闭页面");

            //将所有返回结果汇总
            var result = null;
            var faildRsp = {}; //失败的响应

            try {

                var requestDict = {}; //请求数据字典{orderId+platformOrderId+shopId：次数}，用于校验请求回来的数据是否是请求发送的数据
                for (var i = 0; i < requestDatas.length; i++) {
                    var req = requestDatas[i];
                    var key = req.Id + "_" + req.PlatformOrderId + "_" + req.ShopId;
                    requestDict[key] = 1;
                }

                var notInRequestDict = []; //后台返回的数据不存在请求数据中的数据

                var documentDict = {}; //已经汇总的文档{运单号：次数}（一个文档会打印出一个面单）

                var taskDict = {}; //汇总发送给菜鸟组件的请求，按请求批次大小数量来决定一个task包含的documents数量。目前默认是10订单一个批次。同时要保证每个task的id必须唯一。

                //汇总
                if (Array.isArray(allRsp) && allRsp.length > 0) {
                    //将所有回来的请求数据根据请求批次号排序，保证订单按顺序打印
                    allRsp = common.SortExt(allRsp, "Data.RequestBatchNumber", false, true);
                    for (var i = 0; i < allRsp.length; i++) {
                        var tempR = allRsp[i];
                        if (tempR.Success == false) {
                            faildRsp[tempR.RequestBatch] = tempR;
                            continue;
                        }
                        if (result == null) {
                            result = tempR;
                            if (result.Data.PrintDataList) {
                                //将task汇总到taskList里(按请求批次大小数量来决定一个task包含的documents数量)
                                var request_first = JSON.parse(JSON.stringify(result.Data.PrintDataList));
                                request_first.requestID = request_first.requestID + "_" + getOnlyCode(); //保证requestID唯一
                                request_first.task.firstDocumentNumber = 1; //第一个请求面单打印序号从1开始
                                document_total_count += request_first.task.documents.length; //汇总文档数
                                taskDict[request_first.task.taskID] = request_first; //task汇入字典，用于后续判断taskID是否重复
                                result.Data.PrintDataList.TaskList = [];
                                result.Data.PrintDataList.TaskList.push(request_first);
                            }
                        }
                        else {
                            result.Data.successCount += tempR.Data.successCount; //汇总成功数
                            result.Data.errorCount += tempR.Data.errorCount;     //汇总失败数
                            //汇总waybillCode数据
                            if (Array.isArray(tempR.Data.WaybillCode) == true && tempR.Data.WaybillCode.length > 0) {
                                for (var j = 0; j < tempR.Data.WaybillCode.length; j++) {
                                    var tempWaybillCode = tempR.Data.WaybillCode[j];
                                    var key = tempWaybillCode.OrderInfo.OrderInfo.Id + "_" + tempWaybillCode.OrderInfo.OrderInfo.PlatformID + "_" + tempWaybillCode.OrderInfo.OrderInfo.ShopId;
                                    if (!requestDict[key]) {
                                        //请求回来的数据不存在于请求数据中
                                        notInRequestDict.push(tempWaybillCode);
                                    }
                                    result.Data.WaybillCode.push(tempWaybillCode);
                                }
                            }
                            //汇总lodop组件打印的数据
                            if (Array.isArray(tempR.Data.TemplateList) == true && tempR.Data.TemplateList.length > 0) {
                                for (var j = 0; j < tempR.Data.TemplateList.length; j++) {
                                    result.Data.TemplateList.push(tempR.Data.TemplateList[j]);
                                }
                            }
                            //汇总打印记录id
                            if (Array.isArray(tempR.Data.PrintHistoryIds) == true && tempR.Data.PrintHistoryIds.length > 0) {
                                for (var j = 0; j < tempR.Data.PrintHistoryIds.length; j++) {
                                    result.Data.PrintHistoryIds.push(tempR.Data.PrintHistoryIds[j]);

                                }
                            }
                            //汇总非lodop组件打印的数据（菜鸟、拼多多组件）
                            if (tempR.Data.PrintDataList != null && tempR.Data.PrintDataList.task && Array.isArray(tempR.Data.PrintDataList.task.documents) && tempR.Data.PrintDataList.task.documents.length > 0) {
                                var tempTask = tempR.Data.PrintDataList.task;
                                tempTask.firstDocumentNumber = document_total_count + 1; //面单开始序号
                                var temp_documents = []; //临时保存这个批次的douments(一个document一个面单）
                                for (var j = 0; j < tempTask.documents.length; j++) {
                                    var tempDoc = tempTask.documents[j];
                                    var docDictVal = documentDict[tempDoc.documentID];
                                    if (!docDictVal) {
                                        //不存在，数量初始为1
                                        documentDict[tempDoc.documentID] = 1;
                                        //不存在，则加入临时集合
                                        temp_documents.push(tempDoc);
                                        document_total_count++; //汇总文档数
                                    }
                                    else {
                                        //存在，则次数加一
                                        documentDict[tempDoc.documentID] = (docDictVal + 1);
                                    }
                                }
                                //判断taskID是否存在
                                if (taskDict[tempTask.taskID]) {
                                    //存在，则重新生成taskID
                                    tempTask.taskID = getOnlyCode();
                                }
                                //将新documents（排除了已存在的document，防止重复）覆盖task的documents
                                tempTask.documents = temp_documents;
                                result.Data.PrintDataList.TaskList.push({
                                    cmd: 'print',
                                    requestID: "requestID_" + getOnlyCode(),
                                    task: tempTask
                                });
                            }
                        }
                        result.Data.totalPrintNum = total_data_count; //+= tempR.Data.totalPrintNum;
                    }
                }
                else {
                    if (allRsp.Success == false)
                        faildRsp[1] = allRsp;
                    else
                        result = allRsp;
                }

                if (result == null) {
                    //全部失败
                    result = {
                        Success: true,
                        Data: {
                            Template: template,
                            IsError: true,
                            totalPrintNum: total_data_count,
                            successCount: 0,
                            errorCount: 0,
                            WaybillCode: []
                        }
                    };
                }

            } catch (e) {

                var errorMsg = "所有请求完成，数据解析异常》" + e.stack;
                //console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            try {

                //汇总错误
                for (var i in faildRsp) {
                    var batchNumber = parseInt(i);
                    var tempSize = orderPrintBatchNumber;
                    if ((batchNumber * orderPrintBatchNumber) < models_copy.length) {
                        result.Data.errorCount += orderPrintBatchNumber;
                    }
                    else {
                        var errorCount = (models_copy.length % orderPrintBatchNumber) || orderPrintBatchNumber;
                        result.Data.errorCount += errorCount;
                        tempSize = errorCount;
                    }

                    var batchPlatformOrderIds = '批次' + batchNumber + "：";

                    var idx = (batchNumber - 1) * orderPrintBatchNumber;
                    for (var j = idx; j < idx + tempSize; j++) {
                        batchPlatformOrderIds += models_copy[j].PlatformOrderId + ',';
                    }
                    if (faildRsp[i].ErrorCode == "wbl_auth_expired") {
                        result.Data.WaybillCode.push({
                            IsError: true,
                            AgentName: faildRsp[i].Data.AgentName,
                            BatchPlatformOrderIds: batchPlatformOrderIds.trimEndDgj(','),
                            ErrMsg: faildRsp[i].Data.ErrMsg,
                            Solution: faildRsp[i].Data.Solution
                        });
                    }
                    else {
                        result.Data.WaybillCode.push({
                            IsError: true,
                            BatchPlatformOrderIds: batchPlatformOrderIds.trimEndDgj(','),
                            ErrMsg: faildRsp[i].Message
                        });
                    }
                }

            } catch (e) {
                var errorMsg = "所有请求完成，汇总错误异常》" + e.stack;
                //console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            //校验服务器返回的数据与请求发送的数据的一致性
            try {
                var difference = {
                    RspsNotInReqs: [], //返回的数据不在请求中
                    RepeatDocuments: [], //返回的重复面单
                }; //差异对象
                //后台返回的数据不在请求数据中
                if (notInRequestDict.length > 0) {
                    difference.RspsNotInReqs = notInRequestDict;
                }
                for (var key in documentDict) {
                    if (documentDict[key] > 1) {
                        difference.RepeatDocuments.push(key); //重复的面单号
                    }
                }

                //数据不一致，则记录日志
                if (difference.RspsNotInReqs.length > 0 || difference.RepeatDocuments.length > 0) {
                    common.JsLogToMongoDB("快递单打印后端返回的数据和请求数据不一致", JSON.stringify(difference));
                }

            } catch (e) {
                var errorMsg = "数据不一致日志记录报错》" + e.stack;
                //console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            //处理返回的结果
            if (!result.Success) {
                try {
                    layer.closeAll();
                    if (result.ErrorCode) {
                        switch (result.ErrorCode) {
                            case 'branch_not_exist':
                                var url = common.rewriteUrlToMainDomain('/AccountList/');
                                layer.alert(result.Message + " <a href='" + url + "'>点击去电子面单账号管理</a>", { skin: 'wu-dailog' });
                                break;
                            case 'branch_addr_change':
                                var url = common.rewriteUrlToMainDomain('/TemplateSet/');
                                layer.alert(result.Message + " <a href='" + url + "'>点击去模板管理</a>", { skin: 'wu-dailog' });
                                break;
                            case 'taobao_branch_errcode27':
                                branch_past(template, false);
                                break;
                            default:
                                layer.alert(result.Message, { skin: 'wu-dailog' });
                                break;
                        }
                    }
                    else {
                        layer.alert(result.Message, { skin: 'wu-dailog' });
                    }
                } catch (e) {
                    var errorMsg = "所有请求完成，汇总提示异常》" + e.stack;
                    //console.log(errorMsg);
                    common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                }
            }
            else {
                var hasReceiverChangeOrder = false;
                var successOrderIds = [];//打印成功的订单Id
                var data = result.Data;
                if (data.successCount > 0) {

                    try {

                        //1.添加面单到订单行对象
                        var successWaybillCodes = [];
                        // 子母单情况要转换下数据结构

                        console.log("打印回传数据");
                        console.log(data.WaybillCode);
                        var childWaybillCodes = {};
                        for (var i = 0; i < data.WaybillCode.length; i++) {
                            var obj = data.WaybillCode[i];
                            var waybillCode = obj.WaybillCode;
                            var createDate = obj.WaybillCodeCreateDate;
                            var childWaybillCode = obj.ChildWaybillCode;
                            if (!!childWaybillCode) {
                                if (!childWaybillCodes[waybillCode]) {

                                    if (waybillCode !== childWaybillCode) {
                                        childWaybillCodes[waybillCode] = [{
                                            CreateDate: createDate,
                                            ChildWaybillCode: childWaybillCode
                                        }];
                                    } else {
                                        childWaybillCodes[waybillCode] = [];
                                    }
                                } else {
                                    if (waybillCode !== childWaybillCode) {
                                        childWaybillCodes[waybillCode].push({
                                            CreateDate: createDate,
                                            ChildWaybillCode: childWaybillCode
                                        });
                                    }
                                }
                            }
                        }
                        console.log(childWaybillCodes);

                        $(data.WaybillCode).each(function (index, w) {
                            if (!w.IsError) {
                                //var ix = $(".order-chx[data-id='" + w.OrderId + "']").attr("data-index");
                                var row = null;//otb.rows[ix];
                                common.Foreach(otb.rows, function (i, o) {
                                    if (o.Id == w.OrderId) {
                                        if (o.ReceiverIsChange)
                                            hasReceiverChangeOrder = true;
                                        row = o;
                                        return 'break;';
                                    }
                                });

                                if (row == null) {
                                    throw "根据订单Id【" + w.OrderId + "】未找到订单Row对象";
                                }
                                //row.LastExpressPrintTime = 1;
                                //添加到单号中,最新打印，加到前面
                                if (!row.WaybillCodes) row.WaybillCodes = [];
                                //原单号重打不添加
                                var isExist = false;
                                common.Foreach(row.WaybillCodes, function (ii, wc) {
                                    if (wc.WaybillCodeId == w.WaybillCodeId && wc.WaybillCode == w.WaybillCode) {
                                        wc.ChildWaybillCodes = childWaybillCodes[w.WaybillCode];
                                        wc.CreateDate = w.WaybillCodeCreateDate;
                                        isExist = true;
                                        return 'break';
                                    }
                                });
                                if (isExist == false) {

                                    var wData = {
                                        TemplateId: template.Id,
                                        TemplateType: template.TemplateType,
                                        ExpressCpCode: template.ExpressCompanyCode,
                                        ExpressName: template.ExpressCompanyName,
                                        WaybillCodeId: w.WaybillCodeId,
                                        CreateDate: w.WaybillCodeCreateDate,
                                        WaybillCode: w.WaybillCode,
                                        ChildWaybillCodes: w.ChildWaybillCodes,
                                        UniqueKey: w.WaybillUniqueKey,
                                        Status: 1,
                                        CompanyCode: template.ExpressCompanyCode
                                    }

                                    if (w.WaybillCodeOrderProducts && w.WaybillCodeOrderProducts.length > 0) {
                                        // MasterId为0 说明是原单号打印--直接用索引赋值
                                        if (w.WaybillCodeOrderProducts[0].MasterId == 0) {
                                            wData.WaybillCodeOrderProducts.forEach(function (wItem) {
                                                wItem.MasterId = index;
                                            });
                                        } else {
                                            wData.WaybillCodeOrderProducts = w.WaybillCodeOrderProducts;
                                        }
                                    }
                                    row.LastWaybillCode = w.WaybillCode; // 更新最新打印单号
                                    row.ReceiverIsChange = false; // 重新打印之后更新标记
                                    row.WaybillCodes.unshift(wData);
                                }
                                console.log(row.WaybillCodes);
                                successOrderIds.push(row.Id);
                                successWaybillCodes.push(w);
                                //赋值orders 对象中的waybillcode字段，让预览后点击打印按钮，后台能收到预览的waybillcode，从而打印出相同的单号
                                common.Foreach(orders, function (i, o) {
                                    if (o.Id == w.OrderId) {
                                        o.WaybillCode = w.WaybillCode;
                                    }
                                });
                            }
                        });

                        if (hasReceiverChangeOrder)
                            otb.refreshColumn(["Reciever"]);
                    } catch (e) {
                        var p_msg = "";
                        //try {
                        //    p_msg += JSON.stringify(data);
                        //    p_msg += '此时订单行数据：' + JSON.stringify(otb.rows);
                        //} catch (ex) {
                        //    wcs = "data 或 otb.rows 对象转String报错了." + ex.stack;
                        //}
                        var errorMsg = "添加面单到订单行对象异常》" + e.stack;
                        //console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    //try {
                    //    //2.更新页面状态
                    //    otb.changeTemplate(template);
                    //} catch (e) {
                    //    var errorMsg = "更新页面状态异常》" + e.stack;
                    //    console.log(errorMsg);
                    //    common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    //}

                    try {
                        //3.记录打印日志或者预览打印操作设置
                        if (isPreView == false) {
                            //记录打印日志
                            _writePrintLogToServer(data, successWaybillCodes, template, printerName, orders, true, allRsp);
                        }
                        else {
                            var printType = 'Normal';
                            if (common.IsKuaiYunTemplate(template.TemplateType) || common.IsPddKuaiYunTemplate(template.TemplateType, template.ExpressCompanyCode) || common.IsTouTiaoKuaiYunTemplate(template.TemplateType)) {
                                printType = "PrintKuaiYun"; //打印快运面单
                            }
                            //设置 预览页面的打印按钮功能 //doPrint(orders, template, printType, packageCount, isPreView, printerName, printedCallback, printerIndex)
                            common.SetPrintAction(ep.doPrint.bind(this, orders, template, printType, packageCount, false, printerName, printedCallback, printerIndex));

                            //更新预览标志
                            _wirtePreviewFlagToOrder(orders);
                        }
                        common.JsLogToMongoDB("打印快递单回调日志", "成功运单号数量:" + successWaybillCodes.length);
                    } catch (e) {
                        var errorMsg = "记录打印日志或者预览打印操作设置异常》" + e.stack;
                        //console.log(errorMsg);
                        common.JsExcptionLog("快递单打印回调异常", errorMsg);
                    }

                    try {
                        data.ExpressCompanyCode = template.ExpressCompanyCode; //复制快递编码，用于控制 运单号条形码显不显示 文字的问题。
                        //4.发送到打印机打印
                        ep.postPrintCommond(data, printerName, printerIndex, isPreView);

                        //5.绑定打印机信息
                        common.SetPrinterBind(template.Id, 1, printerName, true);

                        //6.保存普通模板 单号
                        common.SaveTraditionWaybillCodeConfig(template);

                        //检查可用单号
                        addTmplInOrderListModule.CheckQty(false, template, false);

                    } catch (e) {
                        var errorMsg = "4，5，6步异常》" + e.stack;
                        //console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    if (data.errorCount <= 0)
                        layer.closeAll();
                    if (typeof printedCallback == 'function')
                        printedCallback();

                }
                layer.close(printing);

                var sendOrderId = 0; //发货的订单id。单个订单
                if (orders.length == 1)
                    sendOrderId = orders[0].Id;

                if (data.errorCount > 0) {
                    //显示错误信息
                    data.Template = template;

                    var orderPrintCount = orders.length * (parseInt(packageCount) > 0 ? parseInt(packageCount) : 1);
                    data.IsShowSendBtn = (isPreView == false && data.errorCount < orderPrintCount); //显示发货按钮
                    //if(print)
                    data.SendOrderId = sendOrderId; //发货的订单id。单个订单
                    data.SuccessOrderIds = successOrderIds;
                    _showErrorMessage(data);
                }
                else if (common.isCustomerOrder() == false && isPreView == false) {
                    //全部成功
                    if (false && commonModule.SystemoConfig.AutoSendSetVal == 1) {
                        sendLogistic.BatchSendOrder(false, sendOrderId, true); //发货
                    }
                    else {
                        data.Template = template;
                        data.IsNotShowError = true; //不显示订单错误信息
                        data.IsShowSendBtn = (isPreView == false); //显示发货按钮
                        data.SendOrderId = sendOrderId; //发货的订单id。单个订单
                        _showErrorMessage(data);
                    }
                }
            }
        }

        try {
            if (ep.IsPrinting) {
                console.log("当前有打印任务正在执行…");
                return;
            }
            ep.IsPrinting = true;
            ///Order/ExpressPrint  /Common/TestBatchAjax
            console.log("ddd");
            console.log(request);

            common.posts('/NewOrder/ExpressPrint', models, GetRequestModel, SingleCallBack, AllDoneCallback, orderPrintBatchNumber);//, "打印中...");

            /*if (window.location.href.indexOf('NewOrder/WaitOrder') > -1) {
                $('#SeachConditions').trigger("click");
            }*/
        } catch (e) {
            ep.IsPrinting = false;
            var errorMsg = "分批发送请求异常》" + e.stack;
            //console.log(errorMsg);
            common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
        }
    }

    function getOnlyCode() {
        return (new Date()).getTime() + "_" + Math.random().toString().replace('.', '').substring(0, 6);
    }

    function branch_past(template, isFree) {
        if (isFree)
            $('#div-branch-past-content-one').show();
        else
            $('#div-branch-past-content-one').hide();


        layer.open({
            type: 1,
            title: "电子面单账号授权过期",
            content: $('#div-branch-past-show'),
            area: ['1200', '300'], //宽高
            cancel: function () {
                //layer.close(branch_past_show);
                $('#div-branch-past-show').hide();
            },

        });
        var accountname = "";
        var interfaceTemplate = template;
        if (interfaceTemplate != null)
            accountname = interfaceTemplate.AuthAccountName;

        common.Ajax({
            url: '/AccountList/BranchPastLink',
            data: {},
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }

                //console.log(rsp.Data);
                //console.log(accountname);
                var dates = {
                    links: rsp.Data.taobaoAuthUrl,
                    accountname: accountname
                }

                var dialog = $.templates("#branch-past-show-tmpl");
                var htmls = dialog.render(dates);
                $("#div-branch-past-content").html(htmls);
            }
        });
    }

    //从打印组件获取打印机列表
    function GetPrinter(printComponents) {
        var printers = [];
        switch (printComponents) {
            case "Lodop":
                printers = lp.printers();
                break;
            case "Fengqiao":
                printers = fq.printers();
                break;
            case "Cainiao":
            case "Link":
                printers = caiNiao.printers;
                break;
            case "Pinduoduo":
                printers = pdd.printers;
                break;
            case "JingDong":
                printers = jingDong.printers;
                break;
            case "TouTiao":
                printers = touTiao.printers;
                break;
            case "KuaiShou":
                printers = kuaiShou.printers;
                break;
            case "XiaoHongShu":
                printers = xiaoHongShu.printers;
                break;
            case "NewXiaoHongShu":
                printers = newXiaoHongShu.printers;
                break;
            case "WxVideo":
                printers = wxVideoPrinter.printers;
                break;
        }
        return printers;
    }

    //自由打印，单个打印
    //必填参数orders订单数据
    //必填参数template 模板数据
    //必填参数printType 有四个值：Normal 正常打印 需获取上一次打印的包裹ID，OneToMany 一单多包，Reprint 重打， NewPrint 新单号打印
    //选填参数 printedPackageId：已打印的pid，当重打的时候需提供 只有一个订单
    //选填参数 packageCount：面单数量，仅当打印一单多包和快运时需提供，否则默认为1
    //选填参数 printedCallback：打印后的回调函数
    ep.doPrint_FreePrintSingle = function doPrint(orders, template, isPreView, printerName, printerIndex, packageCount, printedCallback) {
        var printType = 'OneToMany';
        if (common.IsNormalTemplate(template.TemplateType) && packageCount == 1) { //template.TemplateType == 3 || template.TemplateType == 1
            printType = "Normal";
        }
        if (common.IsKuaiYunTemplate(template.TemplateType) || common.IsPddKuaiYunTemplate(template.TemplateType, template.ExpressCompanyCode)) {//template.TemplateType == 7 || template.TemplateType == 8) {
            printType = "PrintKuaiYun"; //打印快运面单
        }

        var packageDesc = null, goodsDesc = null;
        var packageDescCtrol = $('#sel_package_desc'), goodsDescCtrol = $('#sel_goods_desc');
        if (packageDescCtrol.length > 0 && packageDescCtrol.val().trim() != '0' && packageDescCtrol.val().trim() != '') {
            packageDesc = packageDescCtrol.val().trim();
        }
        if (goodsDescCtrol.length > 0 && goodsDescCtrol.val().trim() != '0' && goodsDescCtrol.val().trim() != '') {
            goodsDesc = goodsDescCtrol.val().trim();
        }

        var request = {
            TemplateId: template.Id,
            PackageCount: (printType == "OneToMany" || printType == "PrintKuaiYun") ? packageCount : 1,
            PackageDesc: packageDesc,
            GoodsDesc: goodsDesc,
            PrintMethod: printType,
            IsPreview: isPreView,
            Branches: template.Branches // 传递从CheckTemplateAvaliable获取的Branches数据
        };

        ep.defaultPrinter = printerName;

        request.PrinterName = printerName;

        var toFullNameIsNullOrders = [];
        var models = [];
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var model = {
                WaybillCodeId: "",
                Id: order.Id,
                PlatformOrderId: order.PlatformOrderId,
                CustomerOrderId: order.CustomerOrderId,
                ShopId: order.ShopId,
                PrintInfo: order.PrintInfo,
                OrderItems: order.OrderItems,
                WaybillCode: order.WaybillCode,
                SellerRemark: order.SellerRemark,
                BuyerRemark: order.BuyerRemark,
                Receiver: {
                    toFullName: common.DeleteRareWords(order.Receiver.ToName),
                    toMobile: order.Receiver.ToMobile,
                    toArea: common.DeleteRareWords(order.Receiver.ToAddress),
                    toProvince: order.Receiver.ToProvince,
                    toCity: order.Receiver.ToCity,
                    toCounty: order.Receiver.ToCounty,
                    toMaskAddress: order.Receiver.ToMaskAddress,
                    buyerMemberId: order.Buyer.BuyerMemberId
                },
                Sender: {
                    SenderName: common.DeleteRareWords(order.Sender.SenderName),
                    SenderPhone: order.Sender.SenderPhone,
                    SenderAddress: common.DeleteRareWords(order.Sender.SenderAddress),
                    CompanyName: order.Sender.SenderCompany,
                },
                Buyer: {
                    BuyerMemberId: order.Buyer.buyerMemberId,
                    BuyerMemberName: order.Buyer.BuyerMemberName,
                    BuyerWangWang: order.Buyer.BuyerWangWang
                }
            };

            if (model.Receiver.toFullName == "") {
                toFullNameIsNullOrders.push(model.PlatformOrderId);
            }
            models.push(model);
        };

        if (toFullNameIsNullOrders.length > 0) {
            layer.alert("订单【" + toFullNameIsNullOrders.join(",") + "】收件人姓名为空或者全为特殊字符，会导致打单失败，请更改后再打印。", { skin: 'wu-dailog' },function (index) { layer.closeAll(""); })
        }

        //删除危险字符
        models = JSON.parse(common.DelDangerChar(JSON.stringify(models)));

        request.Orders = models;
        request.RequestBatchNumber = "FreeSingle/" + common.GetRequestBatch();
        common.Ajax({
            url: "/Order/ExpressPrint",
            data: request,
            loadingMessage: "正在打印",
            success: function (result) {
                //处理返回的结果
                if (!result.Success) {
                    layer.closeAll();
                    var msgFix = isPreView == true ? "预览" : "打印";
                    if (result.ErrorCode) {
                        switch (result.ErrorCode) {
                            case 'branch_not_exist':
                                var url = common.rewriteUrlToMainDomain('/AccountList/');
                                layer.alert(msgFix + '失败：' + result.Message + " <a href='" + url + "'>点击去电子面单账号管理</a>" + '<br/>订单已保存至待打印列表', { skin: 'wu-dailog' });
                                break;
                            case 'branch_addr_change':
                                var url = common.rewriteUrlToMainDomain('/TemplateSet/');
                                layer.alert(msgFix + '失败：' + result.Message + " <a href='" + url + "'>点击去模板管理</a>" + '<br/>订单已保存至待打印列表', { skin: 'wu-dailog' });
                                break;
                            case 'taobao_branch_errcode27':
                                branch_past(template, true);
                                break;
                            default:
                                layer.alert(msgFix + '失败：' + result.Message + '<br/>订单已保存至待打印列表', { skin: 'wu-dailog' });
                                break;
                        }
                    }
                    else {
                        layer.alert(msgFix + '失败：' + result.Message + '<br/>订单已保存至待打印列表', { skin: 'wu-dailog' });
                    }
                } else {
                    var data = result.Data;
                    if (data.successCount > 0) {
                        //预览
                        if (isPreView == true) {
                            $(data.WaybillCode).each(function (index, w) {
                                if (!w.IsError) {
                                    //赋值orders 对象中的waybillcode字段，让预览后点击打印按钮，后台能收到预览的waybillcode，从而打印出相同的单号
                                    common.Foreach(orders, function (i, o) {
                                        if (o.Id == w.OrderId) {
                                            o.WaybillCode = w.WaybillCode;
                                        }
                                    });
                                }
                            });
                            //设置 预览页面的打印按钮功能
                            common.SetPrintAction(ep.doPrint_FreePrintSingle.bind(this, orders, template, false, printerName, printerIndex, packageCount, printedCallback));
                            //更新预览标志
                            _wirtePreviewFlagToOrder(orders);
                        }
                        else { //打印

                            //获取打印成功的单号
                            var successWaybillCodes = [];
                            $(data.WaybillCode).each(function (index, w) {
                                if (!w.IsError) {
                                    successWaybillCodes.push(w);
                                }
                            });
                            //记录打印日志
                            _writePrintLogToServer(data, successWaybillCodes, template, printerName, orders, false);
                            //绑定打印机信息
                            common.SetPrinterBind(template.Id, 1, printerName, true);
                            if (data.errorCount <= 0)
                                layer.closeAll();
                            if (typeof printedCallback == 'function')
                                //打印回调
                                printedCallback(successWaybillCodes);
                        }

                        data.ExpressCompanyCode = template.ExpressCompanyCode; //复制快递编码，用于控制 运单号条形码显不显示 文字的问题。
                        //发送到打印机打印
                        ep.postPrintCommond(data, printerName, printerIndex, isPreView);
                    }
                    if (data.errorCount > 0) {
                        //显示错误信息
                        data.Template = template;
                        _showErrorMessage(data);
                    }
                }
            }
        });
    }

    ep.ScanPrintPostToLodop = function (data, printerIndex) {
        postToLodop(data, printerIndex);
    }

    //顺丰云模板打印数据
    function postCloudPrintDataToLodop(data, printerIndex, isPreView) {
        var dataObj = data.TemplateCloudList;
        try {
            //兼容历史数据（之前这里包敦新弄字符串，所以打印过的数据底单重打的话，这里需要转一下）
            var dataJson = JSON.parse(data.TemplateCloudList);
            dataObj = dataJson;
        } catch (ex) {
            //
            console.log("data.TemplateCloudList 是json无需转换")
        }
        var printCallback = function (result) {
            console.log(JSON.stringify(result));
        };
        var options = {
            "lodopFn": isPreView ? "PREVIEW" : "PRINT" // 默认打印，预览传PREVIEW
        };
        fq.setPrinter(printerIndex);
        if (sfSdkToken) {
            dataObj.accessToken = sfSdkToken;
        }
        fq.print(dataObj, printCallback, options);
    }

    function postToLodop(data, printerIndex) {
        var list = data.TemplateList;
        var pageSize = 20;
        var tasks = [];
        while (list.length > 0) {
            var temp = list.splice(0, pageSize);
            if (temp != null && temp.length > 0)
                tasks.push(temp);
            else
                break;
        }
        var totalDocumentCount = 0;
        for (var i = 0; i < tasks.length; i++) {
            var task = tasks[i];
            totalDocumentCount += task.length;
        }
        var startIndex = 0;
        for (var i = 0; i < tasks.length; i++) {
            var task = tasks[i];
            data.TemplateList = task;
            postToLodopByPaged(data, printerIndex, startIndex, totalDocumentCount);
            startIndex += task.length;
        }
    }

    function postToLodopByPaged(data, printerIndex, startIndex, totalDocumentCount) {
        //console.log(data);
        var expressCompanyCode = data.ExpressCompanyCode;
        //console.log(expressCompanyCode)
        try {
            var w = data.PageWidth;
            var h = data.PageHeight;
            var ox = parseFloat(data.PageX);
            var oy = parseFloat(data.PageY);
            var result;
            var isPrintTypeGlobal = 0;
            var list = data.TemplateList;
            var listDocSize = totalDocumentCount || list.length;
            LODOP.PRINT_INIT("店管家快递单打印");
            var sepid = 1;
            for (var i = 0; i < list.length; i++) {
                LODOP.SET_PRINT_PAGESIZE(1, w + "mm", h + "mm", "");
                var item = list[i];
                if (!item.InputText)
                    item.InputText = "";
                var els = item.TemplateInputList;
                for (var j = 0; j < els.length; j++) {
                    var el = els[j];
                    var top = parseFloat(el.Y) + oy;
                    var left = parseFloat(el.X) + ox;
                    var iw = el.Width;
                    var ih = el.Height;
                    var txt = unescape(el.InputText);
                    if (el.controlId == 'WaybillCodeSerialCode') {
                        txt = ((i + 1) + startIndex) + "/" + listDocSize;
                    }
                    if (txt == "")
                        continue;
                    if (el.controlType == "7") { //二维码
                        if (el.controlId == 'sfqrcode' || el.controlId == 'anqrcode') {
                            LODOP.ADD_PRINT_BARCODE(top, left, 100 + 'px', 100 + 'px', "QRCode", el.InputText);
                        }
                        else if (el.controlId == 'qrcode') {
                            LODOP.ADD_PRINT_BARCODE(top, left, iw + 'px', iw + 'px', "QRCode", el.InputText);
                        }
                    }
                    else if (el.IsCode != "" && el.IsCode != undefined) {
                        LODOP.ADD_PRINT_BARCODE(top, left, iw, ih, el.IsCode, txt);
                        if (el.controlId == "onecode_vertical")
                            LODOP.SET_PRINT_STYLEA(0, "Angle", -90);
                        if (expressCompanyCode == "SF" || expressCompanyCode == "JD")
                            LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 0);
                    } else if (txt == "sxh001" || txt == "sxs001" || txt == "xxh001" || txt == "xxs001") {
                        if (txt == "sxh001")  //横线（实体）
                            LODOP.ADD_PRINT_LINE(top, left, top, left + iw, 0, el.FontSize);

                        if (txt == "sxs001") //竖线（实体）
                            LODOP.ADD_PRINT_LINE(top, left, top + ih, left, 0, el.FontSize);

                        if (txt == "xxh001") //横线（虚线）
                            LODOP.ADD_PRINT_LINE(top, left, top, left + iw, 2, el.FontSize);

                        if (txt == "xxs001") //竖线（虚线）
                            LODOP.ADD_PRINT_LINE(top, left, top + ih, left, 2, el.FontSize);
                    } else if (el.controlType == "8" && el.InputText != '') { //图片

                        LODOP.ADD_PRINT_IMAGE(top, left, iw, ih, "<img border='0' style='" + "width:" + iw + ";height:" + ih + ";" + "' src='" + el.InputText + "' />");
                        LODOP.SET_PRINT_STYLEA(0, "Stretch", 2);
                    }
                    else {
                        if (el.IsText) {
                            if (el.highlimit == "1" || isPrintTypeGlobal == 3)
                                LODOP.ADD_PRINT_TEXT(top, left, iw, ih, txt);
                            else
                                LODOP.ADD_PRINT_TEXT(top, left, iw, ih, txt);

                            if (isPrintTypeGlobal == 3)
                                LODOP.SET_PRINT_STYLEA(sepid, "ReadOnly", 0);

                            LODOP.SET_PRINT_STYLEA(sepid, "LineSpacing", -4); //设置行间距
                            LODOP.SET_PRINT_STYLEA(sepid, "FontName", el.FontFamily); //el.FontFamily设置字体大小
                            LODOP.SET_PRINT_STYLEA(sepid, "FontSize", el.FontSize); //设置字体大小
                            LODOP.SET_PRINT_STYLEA(sepid, "Bold", el.FontWeight); //设置加粗
                            if (el.controlId == "ShortAddress") {
                                LODOP.SET_PRINT_STYLEA(sepid, "Alignment", 2); //设置行间距
                            }
                        } else {
                            var strHtml = txt.replace(eval("/<br>/gi"), "\n");
                            if (strHtml == "null")
                                continue;
                            if (el.highlimit == "1" || isPrintTypeGlobal == 3)
                                LODOP.ADD_PRINT_TEXT(top, left, iw, ih, strHtml);
                            else
                                LODOP.ADD_PRINT_TEXT(top, left, iw, ih, strHtml);

                            if (isPrintTypeGlobal == 3)
                                LODOP.SET_PRINT_STYLEA(sepid, "ReadOnly", 0);

                            LODOP.SET_PRINT_STYLEA(sepid, "LineSpacing", -4); //设置行间距
                            LODOP.SET_PRINT_STYLEA(sepid, "FontName", el.FontFamily); //设置字体大小
                            LODOP.SET_PRINT_STYLEA(sepid, "FontSize", el.FontSize); //设置字体大小
                            LODOP.SET_PRINT_STYLEA(sepid, "Bold", el.FontWeight); //设置加粗
                            if (el.controlId == "ShortAddress") {
                                LODOP.SET_PRINT_STYLEA(sepid, "Alignment", 2); //设置行间距
                            }
                            if (el.controlId == "watermark") {
                                LODOP.SET_PRINT_STYLEA(sepid, "Alpha", 100); //透明度
                            }
                            if (el.controlId.indexOf("CustomWatermark") >= 0) {
                                LODOP.SET_PRINT_STYLEA(sepid, "Alpha", 100); //透明度
                            }
                        }
                    }

                    sepid++;
                }

                if (i + 1 < list.length) {
                    LODOP.NewPageA();
                }
            }
            if (list.length <= 0)
                return;
            LODOP.SET_PRINT_COPIES(1);
            lp.setPrinter(printerIndex);
            result = LODOP.PRINT();
        } catch (e) {
            layer.alert("打印出现异常,尝试调整快递模版再打印！", { skin: 'wu-dailog' });
        }
    }

    function _showErrorMessage(data) {
        //var dialog = $.templates("#print-express-error-dialog-tmpl");
        var dialog = $.templates("#newprint-express-error-dialog-tmpl");
        data.IsCustomerOrder = common.isCustomerOrder();
        var html = dialog.render(data);

        //本次打印后台底单都存在，则显示忽略重新打印的按钮
        var isShowIgnoreRepeatPrintBtn = 1;//显示忽略，重新打印按钮
        for (var i = 0; i < data.WaybillCode.length; i++) {
            var item = data.WaybillCode[i];
            if (!item.IsError || item.ErrCode != "WaybillExists") {
                isShowIgnoreRepeatPrintBtn = 0;
                break;
            }
        }
        var opts = {
            type: 1,
            title: "快递单打印结果",
            btn: ["关闭"],
            area: ['856px', 'auto'],
            content: html,
            skin: 'wu-dailog expressPrintResult',
            success: function () {
                $(".expressPrintResult .layui-layer-btn0").css({
                    border: '1px solid #dedede',
                    backgroundColor: '#fff',
                    color: '#333'
                });
            },
            btn1: function () {
                layer.closeAll();
            },
        };
        //是否显示 忽略订单已打印，继续打印 按钮
        if (isShowIgnoreRepeatPrintBtn == 1) {
            opts.btn = ["已确认，重新打印", "关闭"];
            opts.success = function () {
                $(".expressPrintResult .layui-layer-btn0").css({
                    border: '1px solid #0888ff',
                    backgroundColor: '#0888ff',
                    color: '#fff'
                });
            }
            opts.btn1 = function () {
                //重新打印
                layer.closeAll();
                isIgnoreWaybillRepeat = true; //设置忽略后端订单重打拦截
                $(".express-print-btn").click();
            }
            opts.btn2 = function () {
                layer.closeAll(); //关闭
            }
        }
        if (data.IsShowSendBtn == true && common.isCustomerOrder() == false) {
            var fhtxt = isResend ? "二次发货" : "发货";
            opts = $.extend(opts, {
                btn: [fhtxt, "关闭"],
                success: function () {
                    $(".expressPrintResult .layui-layer-btn0").css({
                        border: '1px solid #0888ff',
                        backgroundColor: '#0888ff',
                        color: '#fff'
                    });
                },
                btn1: function (index, layero) {
                    if (isResend) {
                        if (data.SuccessOrderIds && data.SuccessOrderIds.length > 0)
                            sendLogistic.resend(false, '', false, data.SuccessOrderIds);
                        else
                            sendLogistic.resend();
                    }
                    else {
                        if (data.SuccessOrderIds && data.SuccessOrderIds.length > 0) {
                            sendLogistic.BatchSendOrder(false, '', false, data.SuccessOrderIds);
                        } else {
                            sendLogistic.BatchSendOrder();
                        }
                    }
                    layer.close(index);
                },
                btn2: function () {
                    console.log("打印快递单号发货关闭" + sendLogistic.LockButton)
                    layer.closeAll();
                }
            });
        }
        layer.open(opts);

    }


    ///合并打印 纯base64
    ep.mergeTokTokLodopByBase64 = function mergeTokTokLodopByBase64(use_lodop_reprint_datas, printerIndex, pageSize) {
        try {
            var batchFlag = common.GetRequestBatch();
            LODOP.PRINT_INIT(batchFlag);
            use_lodop_reprint_datas.forEach(function (pdfBase64, index) {

                LODOP.ADD_PRINT_PDF(0, 0, "100%", "100%", pdfBase64);
                LODOP.NewPageA();
            });

            // 设置打印样式为合并所有页面
            LODOP.SET_PRINT_STYLEA(0, "HowToPrint_PDF", 2); // 2 表示合并所有页面

            // 设置打印机
            lp.setPrinter(printerIndex);

            // 打印
            LODOP.PRINT();
        } catch (e) {
            layer.alert("打印出现异常,尝试调整快递模版再打印! 错误信息：" + e.message, { skin: 'wu-dailog' });
        }
    };

    ///TKLoddop打印  // IsPreparePrint 是否加打发货单
    ep.TokTokLoddopPrint = function TokTokLoddopPrint(lodop_base64datas, lodop_urldatas, printerIndex, templateType, templateSize, IsPreparePrint, paperSize) {
        
        var batchFlag = common.GetRequestBatch();
        //获取打印文件批次号
        LODOP.PRINT_INIT(batchFlag);
        try {


            ///此模板返回的是图片URL
            if (templateType == "SHIPPING_LABEL_PICTURE" && !IsPreparePrint) {
                LODOP.SET_PRINT_PAGESIZE(1, "10cm", "10cm", templateSize);
                lodop_base64datas.forEach(function (base64code, index) {
                    LODOP.ADD_PRINT_IMAGE(10, 10, "10cm", "10cm", base64code);
                    LODOP.SET_PRINT_STYLEA(0, "Stretch", 2);//按原图比例(不变形)缩放模式
                    LODOP.NewPageA();
                });
                LODOP.SET_PRINT_STYLEA(0, "HowToPrint_PDF", 2); // 2 表示合并所有页面


            } else {
                 // 设置页面大小
                 var pageSizeMap = {
                    'A5': [1480, 2100], // A5: 148mm x 210mm
                    'A6': [1050, 1480]  // A6: 105mm x 148mm
                };
                var size = pageSizeMap[templateSize];

                if (IsPreparePrint) {
                    if (templateSize == '10*10') {
                        LODOP.SET_PRINT_PAGESIZE(1, paperSize.paperWidth * 10, paperSize.paperHeight * 10, templateSize);
                    } else if (templateSize == 'A5') {
                        LODOP.SET_PRINT_PAGESIZE(2, size[0], size[1], templateSize);
                    } else if (templateSize == 'A6') {
                        /*var res1 = paperSize.paperWidth * paperSize.paperHeight;
                        var res2 = 105 * 148;
                        if (res1 > res2) {
                            LODOP.SET_PRINT_PAGESIZE(1, size[0], size[1], templateSize);
                        } else {
                            LODOP.SET_PRINT_PAGESIZE(1, paperSize.paperWidth * 10, paperSize.paperHeight * 10, templateSize);
                        }*/
                        LODOP.SET_PRINT_PAGESIZE(1, size[0], size[1], templateSize);
                    }
                } else {
                    if (size) {
                        if (templateSize == "A5") {
                            LODOP.SET_PRINT_PAGESIZE(2, size[0], size[1], templateSize);
                        } else {
                            LODOP.SET_PRINT_PAGESIZE(1, size[0], size[1], templateSize);
                        }
                    } else {
                        throw new Error('Unsupported page size: ' + templateSize);
                    }
                }

                // 添加 PDF 内容
                lodop_base64datas.forEach(function (pdfBase64, index) {
                    LODOP.ADD_PRINT_PDF(0, 0, "100%", "100%", pdfBase64);
                    LODOP.NewPageA();
                });
                // 设置打印样式为合并所有页面
                LODOP.SET_PRINT_STYLEA(0, "HowToPrint_PDF", 2); // 2 表示合并所有页面

            }
            // 设置打印机
            lp.setPrinter(printerIndex);
            // 打印
            LODOP.PRINT();

        } catch (e) {

        }
    }



    //发送打印内容到打印组件（菜鸟或Lodop）
    ep.postPrintCommond = function postPrintCommond(data, printerName, printerIndex, isPreView) {

        //记录发送到打印机的数据
        try {
            if (typeof printExpressAllRspLog != 'undefined' && printExpressAllRspLog == true) {
                common.JsLogToMongoDB("快递单打印发送到打印机的数据", JSON.stringify(data));
            }
        } catch (e) { }

        //发送到打印机
        if (data.TemplateList && data.TemplateList.length > 0) {
            //设置打印机
            //自定义模板
            //lp.setPrinter(printerName);
            postToLodop(data, printerIndex);
        } else if (data.TemplateCloudList) { //顺丰云模板
            //设置打印机
            //自定义模板
            //lp.setPrinter(printerName);
            postCloudPrintDataToLodop(data, printerIndex, isPreView);
        } else if (data.PrintDataList) {
            if (printComponents == "Cainiao") {
                //设置打印机
                data.PrinterConfig.printer.name = printerName;
                caiNiao.send(JSON.stringify(data.PrinterConfig));

                if (data.PrintDataList.TaskList && data.PrintDataList.TaskList.length > 0) {
                    //循环发送task
                    for (var i = 0; i < data.PrintDataList.TaskList.length; i++) {
                        var task = data.PrintDataList.TaskList[i];
                        task.task.printer = printerName;
                        task.task.totalDocumentCount = document_total_count;
                        caiNiao.send(JSON.stringify(task));
                    }
                }
                else if (data.PrintDataList.task) {
                    data.PrintDataList.task.printer = printerName;
                    caiNiao.send(JSON.stringify(data.PrintDataList));
                }
            }
            else if (printComponents == "Pinduoduo") {
                //设置打印机
                data.PrinterConfig.printer.name = printerName;
                pdd.send(JSON.stringify(data.PrinterConfig));

                if (data.PrintDataList.TaskList && data.PrintDataList.TaskList.length > 0) {
                    //循环发送task
                    for (var i = 0; i < data.PrintDataList.TaskList.length; i++) {
                        var task = data.PrintDataList.TaskList[i];
                        task.task.printer = printerName;
                        task.task.totalDocumentCount = document_total_count;
                        pdd.send(JSON.stringify(task));
                    }
                }
                else if (data.PrintDataList.task) {
                    data.PrintDataList.task.printer = printerName;
                    pdd.send(JSON.stringify(data.PrintDataList));
                }
            }
            else if (printComponents == "JingDong") {
                //设置打印机
                data.PrinterConfig.printer.name = printerName;
                var printdata = data.PrintDataList;
                //jingDong.send(JSON.stringify(data.PrinterConfig));

                if (printdata.TaskList && printdata.TaskList.length > 0) {
                    //循环发送task
                    for (var i = 0; i < printdata.TaskList.length; i++) {
                        var documents = printdata.TaskList[i].task.documents;
                        for (var d = 0; d < documents.length; d++) {
                            jingDong.send(JSON.stringify(documents[d].contents));
                        }
                    }
                }
                else if (printdata.task) {
                    var documents = printdata.task.documents;
                    for (var d = 0; d < documents.length; d++) {
                        documents[d].contents.parameters.printName = printerName;
                        jingDong.send(JSON.stringify(documents[d].contents));
                    }
                }
            }
            else if (printComponents == "TouTiao") {
                //设置打印机
                //data.PrinterConfig.printer.name = printerName;
                //touTiao.send(JSON.stringify(data.PrinterConfig));

                var totalIndex = 1;
                var sendCountPerTime = 10;
                if (data.PrintDataList.TaskList && data.PrintDataList.TaskList.length > 0) {
                    //循环发送task
                    for (var i = 0; i < data.PrintDataList.TaskList.length; i++) {
                        var task = data.PrintDataList.TaskList[i];
                        task.task.printer = printerName;
                        task.task.totalDocumentCount = document_total_count;
                        var documents = task.task.documents;
                        var taskId = task.task.taskID;
                        var docs = [];
                        var sendTimes = 0;
                        $(documents).each(function (dindex, doc) {
                            docs.push(doc);
                            if (docs.length == sendCountPerTime) {
                                task.task.taskID = taskId + "_" + sendTimes++;
                                task.task.firstDocumentNumber = totalIndex;
                                totalIndex += sendCountPerTime;
                                task.task.documents = docs;
                                touTiao.send(JSON.stringify(task));
                                docs = [];
                            }
                        });
                        //发送剩余的
                        if (docs.length > 0) {
                            task.task.taskID = taskId + "_" + sendTimes++;
                            task.task.firstDocumentNumber = totalIndex;
                            totalIndex += docs.length;
                            task.task.documents = docs;
                            touTiao.send(JSON.stringify(task));
                            docs = [];
                        }
                    }
                }
                else if (data.PrintDataList.task) {
                    data.PrintDataList.task.printer = printerName;
                    touTiao.send(JSON.stringify(data.PrintDataList));
                }
            }
            else if (printComponents == "KuaiShou") {
                //设置打印机
                data.PrinterConfig.printer.name = printerName;
                kuaiShou.send(JSON.stringify(data.PrinterConfig));

                if (data.PrintDataList.TaskList && data.PrintDataList.TaskList.length > 0) {
                    var first_doc_num = 1;
                    //循环发送task
                    for (var i = 0; i < data.PrintDataList.TaskList.length; i++) {
                        var task = data.PrintDataList.TaskList[i];
                        var taskDocuments = task.task.documents;
                        if (taskDocuments == null || taskDocuments == undefined || taskDocuments.length == 0)
                            continue;
                        task.task.printer = printerName;
                        task.task.firstDocumentNumber = first_doc_num;
                        task.task.totalDocumentCount = document_total_count > 0 ? document_total_count : data.PrintDataList.TaskList.length;
                        kuaiShou.send(JSON.stringify(task));
                        first_doc_num += task.task.documents.length;
                    }
                }
                else if (data.PrintDataList.task) {
                    data.PrintDataList.task.printer = printerName;
                    data.PrintDataList.task.firstDocumentNumber = 1;
                    data.PrintDataList.task.totalDocumentCount = data.PrintDataList.task.documents.length;
                    kuaiShou.send(JSON.stringify(data.PrintDataList));
                }
            }
            else if (printComponents == "XiaoHongShu") {
                //设置打印机
                data.PrinterConfig.printer.name = printerName;
                xiaoHongShu.send(JSON.stringify(data.PrinterConfig));

                if (data.PrintDataList.TaskList && data.PrintDataList.TaskList.length > 0) {
                    var first_doc_num = 1;
                    //循环发送task
                    for (var i = 0; i < data.PrintDataList.TaskList.length; i++) {
                        var task = data.PrintDataList.TaskList[i];
                        task.task.printer = printerName;
                        task.task.firstDocumentNumber = first_doc_num;
                        task.task.totalDocumentCount = document_total_count > 0 ? document_total_count : data.PrintDataList.TaskList.length;
                        xiaoHongShu.send(JSON.stringify(task));
                        first_doc_num += task.task.documents.length;
                    }
                }
                else if (data.PrintDataList.task) {
                    data.PrintDataList.task.printer = printerName;
                    data.PrintDataList.task.firstDocumentNumber = 1;
                    data.PrintDataList.task.totalDocumentCount = data.PrintDataList.task.documents.length;
                    xiaoHongShu.send(JSON.stringify(data.PrintDataList));
                }
            }
            else if (printComponents == "NewXiaoHongShu") {
                //设置打印机
                data.PrinterConfig.printer.name = printerName;
                newXiaoHongShu.send(JSON.stringify(data.PrinterConfig));

                if (data.PrintDataList.TaskList && data.PrintDataList.TaskList.length > 0) {
                    var first_doc_num = 1;
                    //循环发送task
                    for (var i = 0; i < data.PrintDataList.TaskList.length; i++) {
                        var task = data.PrintDataList.TaskList[i];
                        task.task.forceNoPaging = true;//强制不执行组件的内容超出自动分页
                        task.task.printer = printerName;
                        task.task.firstDocumentNumber = first_doc_num;
                        task.task.totalDocumentCount = document_total_count > 0 ? document_total_count : data.PrintDataList.TaskList.length;
                        newXiaoHongShu.send(JSON.stringify(task));
                        first_doc_num += task.task.documents.length;
                    }
                }
                else if (data.PrintDataList.task) {
                    data.PrintDataList.task.forceNoPaging = true;//强制不执行组件的内容超出自动分页
                    data.PrintDataList.task.printer = printerName;
                    data.PrintDataList.task.firstDocumentNumber = 1;
                    data.PrintDataList.task.totalDocumentCount = data.PrintDataList.task.documents.length;
                    newXiaoHongShu.send(JSON.stringify(data.PrintDataList));
                }
            }
            else if (printComponents == "WxVideo") {
                //设置打印机
                data.PrinterConfig.printer.name = printerName;
                wxVideoPrinter.send(JSON.stringify(data.PrinterConfig));

                if (data.PrintDataList.TaskList && data.PrintDataList.TaskList.length > 0) {
                    var first_doc_num = 1;
                    //循环发送task
                    for (var i = 0; i < data.PrintDataList.TaskList.length; i++) {
                        var task = data.PrintDataList.TaskList[i].task;
                        //task.task.command = 'print';
                        //task.task.printer = printerName;
                        //task.task.firstDocumentNumber = first_doc_num;
                        //task.task.totalDocumentCount = document_total_count > 0 ? document_total_count : data.PrintDataList.TaskList.length;
                        //wxVideoPrinter.send(JSON.stringify(task));
                        //first_doc_num += task.task.documents.length;

                        var printCount = document_total_count > 0 ? document_total_count : data.PrintDataList.TaskList.length;
                        var printTaskList = [];
                        for (var j = 0; j < task.documents.length; j++) {
                            printTaskList.push({
                                taskID: task.documents[j].taskID,
                                printInfo: task.documents[j].printInfo,
                                extendData: task.documents[j].extendData,
                                customInfo: task.documents[j].customInfo,
                                printNum: { curNum: first_doc_num, sumNum: printCount },
                                splitControl: task.documents[j].splitControl
                            });

                            first_doc_num++;
                        }
                        var newTask = {
                            command: 'print',
                            requestID: getOnlyCode(),
                            version: '2.0',
                            taskList: printTaskList,
                            printer: printerName
                        };
                        wxVideoPrinter.send(JSON.stringify(newTask));
                        //first_doc_num += task.documents.length;
                    }
                }
                else if (data.PrintDataList.task) {
                    var task = data.PrintDataList.task
                    //data.PrintDataList.task.command = 'print';
                    //data.PrintDataList.task.printer = printerName;
                    //data.PrintDataList.task.firstDocumentNumber = 1;
                    //data.PrintDataList.task.totalDocumentCount = data.PrintDataList.task.documents.length;

                    data.PrintDataList.task.firstDocumentNumber = 1;
                    data.PrintDataList.task.totalDocumentCount = data.PrintDataList.task.documents.length;
                    var printTaskList = [];
                    var first_doc_num = 1;
                    var printCount = document_total_count > 0 ? document_total_count : task.documents.length;
                    for (var j = 0; j < task.documents.length; j++) {
                        printTaskList.push({
                            taskID: task.documents[j].taskID,
                            printInfo: task.documents[j].printInfo,
                            extendData: task.documents[j].extendData,
                            customInfo: task.documents[j].customInfo,
                            printNum: { curNum: first_doc_num, sumNum: printCount },
                            splitControl: task.documents[j].splitControl
                        });
                        first_doc_num++;
                    }
                    var newTask = {
                        command: 'print',
                        requestID: getOnlyCode(),
                        version: '2.0',
                        taskList: printTaskList,
                        printer: printerName
                    };
                    wxVideoPrinter.send(JSON.stringify(newTask));

                    //wxVideoPrinter.send(JSON.stringify(data.PrintDataList));
                }
            }
            else {
                layer.alert("打印之前请先指定打印组件", { skin: 'wu-dailog' });
            }
        }
    }

    _PrintedCallbackUrl = "";

    ep.writePrintLogToServer = function (data, waybillCodes, template, printerName, orders, isUpdatePrintState, allRsp) {
        _writePrintLogToServer(data, waybillCodes, template, printerName, orders, isUpdatePrintState, allRsp);
    }
    //写打印日志
    //allRsp --> ExpressPrint取号回传原始结果
    function _writePrintLogToServer(data, waybillCodes, template, printerName, orders, isUpdatePrintState, allRsp) {
        //组装数据
        if (!waybillCodes || waybillCodes.length <= 0)
            return;
        var logs = [];
        //var orders = otb.getSelections();

        commonModule.ServerNowStr = ''; //清空 批次
        commonModule.DateBatch = ''; //清空  批次序号

        var printHistoryIndexModelList = [];
        common.Foreach(data.PrintHistoryIds, function (i, ph) {
            var findCount = 0;
            for (var i = 0; i < waybillCodes.length; i++) {
                var wyb = waybillCodes[i];
                if (ph.WaybillCode == wyb.WaybillCode && ph.WaybillCodeChild == wyb.ChildWaybillCode) {
                    findCount++;
                    ph.BatchIndex = (i + 1);
                }
            }
            printHistoryIndexModelList.push(ph);
        });
        console.log("打印发货单");
        console.log(orders);
        var request = { TemplateId: template.Id, Orders: [], PrintType: 1, PrintDataType: 1, PrintHistoryIndexModels: printHistoryIndexModelList };
        for (var i = 0; i < waybillCodes.length; i++) {
            var curOrder = {};
            var wbc = waybillCodes[i];
            //订单的其他数据
            for (var j = 0; j < orders.length; j++) {
                var o = orders[j];
                if (o.Id == wbc.OrderId) {
                    curOrder.Id = o.Id;
                    curOrder.PathFlowCode = o.PathFlowCode;
                    curOrder.PlatformOrderId = o.PlatformOrderId;
                    curOrder.ShopId = o.ShopId;
                    curOrder.WaybillCode = wbc.WaybillCode;
                    curOrder.OrderItems = o.OrderItems;//原参数，只有OrderItemId
                    curOrder.Items = o.Items;//新参数，增加OrderItemCode及Quantity
                    curOrder.DataFlag = o.DataFlag;
                    curOrder.IsSelectAllNotPrintedItem = o.IsSelectAllNotPrintedItem;//未打印的订单项是否全部勾选了
                    break;
                }
            }
            request.Orders.push(curOrder);
        }

        //标记为已打印2022.12.9
        if (isUpdatePrintState && allRsp != undefined && allRsp.length > 0) {
            var allResult = [];
            for (var x = 0; x < allRsp.length; x++) {
                if (allRsp[x].Data != undefined && allRsp[x].Data.UpdateOrderPrintStatusResult != undefined && allRsp[x].Data.UpdateOrderPrintStatusResult.length > 0) {
                    for (var y = 0; y < allRsp[x].Data.UpdateOrderPrintStatusResult.length; y++) {
                        allResult.push(allRsp[x].Data.UpdateOrderPrintStatusResult[y]);
                    }
                }
            }
            otb.setPrintState(allResult, 1);
        }

        common.Ajax({
            url: (_PrintedCallbackUrl || "/NewOrder/PrintCallback"),
            data: request,
            success: function (res) {
                if (res.Success && isUpdatePrintState) {
                    //if (common.isCustomerOrder())
                    //    $("#search-btn").click(); //自由打印，打印完成后，数据会到打印历史里，所以刷新界面。
                    //else
                    otb.setPrintState(res.Data, 1);
                }
                //console.log(res);
            }
        });
        return logs;
    }


    //更新预览标识
    function _wirtePreviewFlagToOrder(orders) {

        var selectKeyModelList = [];
        common.Foreach(orders, function (i, o) {
            selectKeyModelList.push({
                ShopId: o.ShopId,
                PlatformOrderId: o.PlatformOrderId,
                FxUserId: o.FxUserId
            });
        });

        common.Ajax({
            url: "/NewOrder/UpdateIsPreviewFlag",
            data: { selectKeyModelList: selectKeyModelList },
            success: function (rsp) {
                if (common.IsError(rsp)) {
                    return;
                }
            }
        });
    }

    ep.selSustenanceDesc = function () {

        //获取被选中的option标签
        var vs = $('#sel_sustenance_desc option:selected').val();
        if (vs == "自定义") {
            $("#ipt-sustenance-desc").show().select();

        } else {
            $("#ipt-sustenance-desc").hide();
        }
    }

    //保存快运包裹 长宽高 信息
    function _wirtePackInfo(templateId) {
        var length = $("#txt_package_length").val(), width = $("#txt_package_width").val(), height = $("#txt_package_height").val();
        if (length <= 0 || !/^-?\d+$/.test(length)) {
            layer.msg("包装尺寸长度,必须大于等于1的整数");
            return false;
        }
        if (length.length > 4) {
            layer.msg("包装尺寸长度,只能设置四位数的整数");
            return false;
        }
        if (width <= 0 || !/^-?\d+$/.test(width)) {
            layer.msg("包装尺寸宽度,必须大于等于1的整数");
            return false;
        }
        if (width.length > 4) {
            layer.msg("包装尺寸宽度,只能设置四位数的整数");
            return false;
        }
        if (height <= 0 || !/^-?\d+$/.test(height)) {
            layer.msg("包装尺寸高度,必须大于等于1的整数");
            return false;
        }
        if (height.length > 4) {
            layer.msg("包装尺寸高度,只能设置四位数的整数");
            return false;
        }
        var packInfo = {
            Length: length,
            Width: width,
            Height: height
        };
        $.cookie('print-express-lhwinfo-' + templateId, JSON.stringify(packInfo), { expires: 30 });
    }

    ep._showImgMessage = function () {
        layer.open({
            type: 1,
            skin: 'layui-layer-rim',
            title: "",
            btn: null,
            area: ['1033px', '516px'],
            content: $('#support_platform_helpShowImg')
        });
    }

    ep.copyBatchLogicOrderSearch = function (value) {
        var values = value.substr(0, value.length - 1);
        $("#SearchContainer input[name=LogicOrderId]").val(values);
        document.getElementById("SeachConditions").click();//查询
        layer.close(noPrintFlagIdsDailog);
    }

    var UpdatEprintExpressNum = function () {
        var $count = $('#express-package-print-count');
        var itemCount = $('#print-express-item_count').attr("data-count");
        $("#print-quantity-new-select-box").find(".active").removeClass("active");
        $("#print-quantity-new-tip").text("根据打印数量生成相对应数量的新单号");
        $("#package-contain-most_product").hide();
        $("#print-express-item_count").text(itemCount);
        $("#print-quantity-product_text").text("件商品");
        $count.prop('placeholder', '请选择或输入数量');

        var _order = $('#print-express-code-num').attr("data-order");
        var num = _order * $count.val();
        $('#print-express-code-num').text(num);
    }

    ep.onInputPrintQuantityNew = function () {
        var val = $(this).val().replace(/[^\d]/g, '').trim();
        $(this).val(val);
        if ($(this).val() > 100) {
            layer.msg("打印面单数量只能为1-100的数字");
            $(this).val(100);
        } else if ($(this).val() && $(this).val() < 1) {
            layer.msg("打印面单数量只能为1-100的数字");
            $(this).val(1);
        }
        UpdatEprintExpressNum();
    }

    ep.onFocusPrintQuantityNew = function () {
        if (event) {
            event.stopPropagation(); // 阻止事件冒泡
        }

        if ($('#print-quantity-new-select-box').css("display") == "none") {
            var val = $(this).val();
            if (isNaN(val)) {
                $(this).val('');
            }
            $('#print-quantity-icon_down').addClass("chevron-down1x_active");
            $('#print-quantity-new-select-box').show();
            $('#package-contain-product-box').hide();
        }
    }

    var oldPackageCount = 0;
    var retractSelectPanel = function () {
        $('#print-quantity-icon_down').removeClass("chevron-down1x_active");
        $('#print-quantity-new-select-box').hide();

        var pText = $('#express-package-print-count').attr('placeholder');
        if (pText != "请选择或输入数量") {
            $('#express-package-print-count').val(pText);

            if (pText == '按包裹最多件数打印面单') {
                var _val = $('#package-contain-product-count').val();
                if (!_val) {
                    $('#package-contain-product-count').val(1);
                    $('#package-contain-icon_down').removeClass("chevron-down1x_active");
                    if (oldPackageCount != 1) {
                        IntelligentSplitting();
                    }
                }
            }
        } else {
            var _order = $('#print-express-code-num').attr("data-order");
            var _val = $('#express-package-print-count').val();
            if (!_val) {
                $('#express-package-print-count').val(1);
                _val = 1;
            }
            var num = _order * _val;
            $('#print-express-code-num').text(num);
        }
    }

    ep.onClickPrintQuantityNew = function () {
        if (event) {
            event.stopPropagation(); // 阻止事件冒泡
        }

        if ($('#print-quantity-new-select-box').css("display") == "none") {
            var val = $('#express-package-print-count').val();
            if (isNaN(val)) {
                $('#express-package-print-count').val('');
            }
            $("#express-package-print-count").focus();
            var len = $('#express-package-print-count').val().length;
            $('#express-package-print-count')[0].setSelectionRange(len, len);
            $('#print-quantity-icon_down').addClass("chevron-down1x_active");
            $('#print-quantity-new-select-box').show();
            $('#package-contain-product-box').hide();
        } else {
            $('#print-quantity-icon_down').removeClass("chevron-down1x_active");
            retractSelectPanel();
        }
    }

    ep.onInputPrintQuantityMore = function () {
        var $count = $('#express-package-print-count');
        var val = $(this).val().replace(/[^\d]/g, '').trim();
        $(this).val(val);

        if ($(this).val() > 100) {
            layer.msg("打印面单数量只能为1-100的数字");
            $(this).val(100);
            $count.val(100);
        } else if ($(this).val() && $(this).val() < 1) {
            layer.msg("打印面单数量只能为1-100的数字");
            $(this).val(1);
            $count.val(1);
        } else {
            $count.val($(this).val());
        }
        UpdatEprintExpressNum();
    }

    ep.onFocusPrintQuantityMore = function () {
        var val = $(this).val();
        if (val) {
            $('#express-package-print-count').val(val);
            UpdatEprintExpressNum();
        }
    }

    $(document).on('click', function (e) {
        if (!$('#print-quantity-new-select-box').is(e.target) && $('#print-quantity-new-select-box').has(e.target).length === 0) {
            retractSelectPanel();
        }
        if (!$('#package-contain-product-box').is(e.target) && $('#package-contain-product-box').has(e.target).length === 0) {
            $('#package-contain-icon_down').removeClass("chevron-down1x_active");
            $('#package-contain-product-box').hide();
        }
    });

    // 智能拆分 用于获取打印面单数量
    var InteOrders = [];
    var IntelligentSplitting = function () {
        var printType = $("#express-package-print-widget").attr('data-pm');
        var template = addTmplInOrderListModule.GetCurrentTemplate();
        var desc = ep.GetPackageAndGoodsDesc(printType, template); //获取包装描述，产品平类

        //顺丰托寄物
        var expressSustenanceDesc = ep.GetExpressSustenanceRequest(template);
        if (expressSustenanceDesc == false) { return; }

        var request = {
            TemplateId: template.Id,
            PackageCount: 1,
            PackageDesc: desc.packageDesc,
            GoodsDesc: desc.goodsDesc,
            PrintMethod: printType,
            IsIgnoreWaybillRepeat: isIgnoreWaybillRepeat,
            PrinterName: $("#express-printer-select").val(),
            IsPreview: false,
            IsContinueForStock: $('#hfIsContinueForStock').val() == "1",    //部分订单项库存不足，是否继续
            PackageLength: desc.packageLength,
            PackageWidth: desc.packageWidth,
            PackageHeight: desc.packageHeight,
            ExpressSustenance: expressSustenanceDesc
        };

        var models = [];
        for (var i = 0; i < ISOrders.length; i++) {
            var order = ISOrders[i];
            var model = {
                WaybillCodeId: "", //printedPackageId ? printedPackageId: "",
                Index: (i + 1), // 用于后端记录打印顺序
                Id: order.Id,
                PlatformOrderId: order.PlatformOrderId,
                LogicOrderId: order.LogicOrderId,
                PathFlowCode: order.PathFlowCode,
                CustomerOrderId: order.CustomerOrderId,
                ShopId: order.ShopId,
                PrintInfo: order.PrintInfo,
                OrderItems: order.OrderItems,
                Items: order.Items,
                WaybillCode: order.WaybillCode,
                SellerRemark: order.SellerRemark,
                BuyerRemark: order.BuyerRemark,
                FxUserId: order.FxUserId,
                IsSelectAllNotPrintedItem: order.IsSelectAllNotPrintedItem,
                SendType: order.SendType == undefined ? 0 : order.SendType,//发货类型
                DataFlag: order.DataFlag,
                Receiver: {
                    toFullName: common.DeleteRareWords(order.Receiver.ToName),
                    toMobile: order.Receiver.ToMobile,
                    toArea: common.DeleteRareWords(order.Receiver.ToAddress),
                    toProvince: order.Receiver.ToProvince,
                    toCity: order.Receiver.ToCity,
                    toCounty: order.Receiver.ToCounty,
                    toMaskAddress: order.Receiver.ToMaskAddress,
                    buyerMemberId: order.Buyer.BuyerMemberId
                },
                Sender: {
                    SenderName: common.DeleteRareWords(order.Sender.SenderName),
                    SenderPhone: order.Sender.SenderPhone,
                    SenderAddress: common.DeleteRareWords(order.Sender.SenderAddress),
                    CompanyName: order.Sender.SenderCompany,
                },
                Buyer: {
                    BuyerMemberId: order.Buyer.buyerMemberId,
                    BuyerMemberName: order.Buyer.BuyerMemberName,
                    BuyerWangWang: order.Buyer.BuyerWangWang
                }
            };

            models.push(model);
        };

        models = JSON.parse(common.DelDangerChar(JSON.stringify(models)));

        var _val = $('#express-package-print-count').val();
        if (_val == "按包裹最多件数打印面单") {
            var PackageSkuNum = $("#package-contain-product-count").val();
            request.PackageType = 3
            request.PackageSkuNum = PackageSkuNum;
            oldPackageCount = PackageSkuNum;
        } else if(_val == "按订单商品规格打印面单") {
            request.PackageType = 2
        } else if (_val == "按订单商品种类打印面单") {
            request.PackageType = 1
        }

        function GetRequestModel(orders) {
            request.Orders = orders;
            return request;
        }

        //var batchSize = orderPrintBatchNumber;
        //if (!batchSize || batchSize <= 0)
        //    batchSize = 10;

        //var datas = models;
        //var perRequestDatas = datas.splice(0, batchSize);
        //var postData = GetRequestModel(perRequestDatas);
        var postData = GetRequestModel(models);

        common.Ajax({
            url: '/NewOrder/GetPrintNum',
            loading: true,
            data: postData,
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }

                //console.log("勾选前的订单数据", postData.Orders);

                if (_val == "按订单商品种类打印面单") {
                    var ProductCodes = [];
                    postData.Orders.forEach(function (pItem) {
                        pItem.Items.forEach(function (item) {
                            if (ProductCodes.indexOf(item.ProductCode) == -1) {
                                ProductCodes.push(item.ProductCode);
                            }
                        });
                    });

                    $("#print-express-item_count").text(ProductCodes.length);
                }
                else if (_val == "按订单商品规格打印面单") {
                    var SkuCodes = [];
                    postData.Orders.forEach(function (pItem) {
                        pItem.Items.forEach(function (item) {
                            if (SkuCodes.indexOf(item.SkuCode) == -1) {
                                SkuCodes.push(item.SkuCode);
                            }
                        });
                    });

                    $("#print-express-item_count").text(SkuCodes.length);
                }
                else {
                    var itemCount = $('#print-express-item_count').attr("data-count");
                    $("#print-express-item_count").text(itemCount);
                }

                if (rsp.Data && rsp.Data.length == 0) {
                    return;
                }

                var num = rsp.Data.length;
                $('#print-express-code-num').text(num);

                InteOrders = rsp.Data;
                var SubOrders = JSON.parse(JSON.stringify(ISOrders));
                InteOrders.forEach(function (row) {
                    var orderItems = {
                        SubOrders: []
                    }
                    var OrderItems = row.OrderItems;
                    var Items = row.Items;
                    var Id = row.Id;

                    OrderItems.forEach(function (item) {
                        var ItemData = Items.find(function (item1) {
                            return item1.OrderItemId == item;
                        });

                        var orderData = SubOrders.find(function (item1) {
                            return item1.Id == Id;
                        });

                        var subData = orderData.SubOrders.find(function (item1) {
                            return item1.OrderItemId == item;
                        });

                        subData.Count = ItemData.Quantity;
                        orderItems.SubOrders.push(subData);
                    })

                    row.PrintInfo = printContentFormatSetModule.ForatPrintContent(orderItems);
                })
                //console.log("智能拆分的订单数据", InteOrders);
            }
        });
    }

    ep.onSelectPrintQuantity = function (num) {
        var val = $('#express-package-print-count').val();
        var phd = $('#express-package-print-count').attr('placeholder');
        $('#print-quantity-icon_down').removeClass("chevron-down1x_active");
        $('#print-quantity-new-select-box').hide();
        if (val == num || phd == val) {
            return;
        }

        var pText = "请选择或输入数量";
        var tipText = "根据打印数量生成相对应数量的新单号";
        var sText = "件商品";

        if (num == -1) {
            pText = "按订单商品种类打印面单";
            tipText = "根据订单中商品的种类 (SPU) 数量进行拆分，打印相对应数量的新单号";
            sText = "个种类";
        } else if (num == -2) {
            pText = "按订单商品规格打印面单";
            sText = "个规格";
        } else if (num == -3) {
            pText = "按包裹最多件数打印面单";
            tipText = "";
        }

        // 包裹包含最多商品控件
        if (num == -3) {
            $("#package-contain-most_product").css("display", "flex");
        } else {
            $("#package-contain-most_product").hide();
        }

        // 正常打印数量
        if (num > 0) {
            var _order = $('#print-express-code-num').attr("data-order");
            var itemCount = $('#print-express-item_count').attr("data-count");
            var _text = _order * num;
            $('#print-express-code-num').text(_text);
            $('#express-package-print-count').val(num);
            $("#print-express-item_count").text(itemCount);
        } else {
            $('#express-package-print-count').val(pText);
            IntelligentSplitting();
        }

        $('#express-package-print-count').prop('placeholder', pText);
        $("#print-quantity-new-tip").text(tipText);
        $("#print-quantity-product_text").text(sText);

        $(this).siblings('.active').removeClass('active');
        $(this).addClass("active");
    }

    ep.onInputPackageContainNew = function () {
        var val = $(this).val().replace(/[^\d]/g, '').trim();
        $(this).val(val);
        if ($(this).val() > 10) {
            layer.msg("每个包裹最多10件商品");
            $(this).val(10);
        } else if ($(this).val() && $(this).val() < 1) {
            layer.msg("每个包裹最少1件商品");
            $(this).val(1);
        }
        $("#package-contain-product-box").find(".active").removeClass("active");
        if ($(this).val() && $(this).val() != oldPackageCount) {
            IntelligentSplitting();
        }
    }

    ep.onFocusPackageContainNew = function () {
        if (event) {
            event.stopPropagation(); // 阻止事件冒泡
        }

        if (oldPackageCount != $(this).val()) {
            oldPackageCount = $(this).val();
        }

        if ($('#package-contain-product-box').css("display") == "none") {
            $('#package-contain-icon_down').addClass("chevron-down1x_active");
            $('#package-contain-product-box').show();
        }
    }

    ep.onClickPackageContainNew = function () {
        if (event) {
            event.stopPropagation(); // 阻止事件冒泡
        }

        if ($('#package-contain-product-box').css("display") == "none") {
            $("#package-contain-product-count").focus();
            var len = $('#package-contain-product-count').val().length; // 获取输入框内文字的长度
            $('#package-contain-product-count')[0].setSelectionRange(len, len);
            $(this).addClass("chevron-down1x_active");
            $('#package-contain-product-box').show();
        } else {
            $(this).removeClass("chevron-down1x_active");
            $('#package-contain-product-box').hide();
            if (!$('#package-contain-product-count').val()) {
                $('#package-contain-product-count').val(1);
                IntelligentSplitting();
            }
        }
    }

    ep.onSelectPackageContain = function (num) {
        var val = $('#package-contain-product-count').val();
        $('#package-contain-icon_down').removeClass("chevron-down1x_active");
        $('#package-contain-product-box').hide();
        if (val == num) {
            return;
        }

        $('#package-contain-product-count').val(num);
        $(this).siblings('.active').removeClass('active');
        $(this).addClass('active');
        IntelligentSplitting();
    }

    return ep;
}(expressPrinter || {}, commonModule, caiNiaoPrinter, pinDuoDuoPrinter, lodopPrinter, fengqiaoPrinter, jingDongPrinter, touTiaoPrinter, kuaiShouPrinter, xiaoHongShuPrinter, newXiaoHongShuPrinter, addTmplInOrderListModule, orderTableBuilder, jQuery));
