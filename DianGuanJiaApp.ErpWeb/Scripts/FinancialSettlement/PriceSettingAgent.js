var ChangeAllProductCheck = null;
var ChangeAllFreightOrderCheck = null;
var ChangeExcludeOrderCheck = null;
var ChangeFilterUserCheck = null;

var PriceSettingAgentModule = (function (module, commmon, $, layer) {
    // 列表数据
    var dataList = {};
    var reqModel = {
        Key: "",
        Status: 0,
        PageIndex: 1,
        PageSize: 50,
        FxUserId: 0
    };
    var _productSkuList = []; // 商品选择弹窗已选商品
    var isReconVipFxUserStatus = false;
    $(function () {
        module.LoadList(false);
        layui.form.render("select");
        module.billSetting(true);  //获取对账设置配置
        initQueryDateBox();
        module.bindOutAccountProductRadio(); // 出账商品选择
        bindAccountTypeRadio(); // 出账类型选择
        bindProductFirstCheckEvent(); // 初始化商品结算价首次发货多选
        bindProductSecondCheckEvent(); // 初始化商品结算价二次发货多选
        bindFreightOrderFirstCheckEvent(); // 初始化商品结算价-首次发货
        bindFreightOrderSecondCheckEvent(); // 初始化商品结算价-二次发货
        getBigCustomerVersion();

        wuFormModule.initLayuiSelect("active-select-filter"); //选择框选中的样式
        wuFormModule.initblurInput(".wu-inputWrap"); //输入框有内容的样式
        wuFormModule.navActive("#tabNav_platform_agent", function(index, item) {}, 'wu-active'); // 平台选项卡的换肤
        wuFormModule.navActive("#showChangeArea", function(index, item) {}, 'wu-active'); // 分区选项卡的换肤
    
    });

    // 获取大客户版本-功能是否有白名单权限
    function getBigCustomerVersion() {
        commonModule.Ajax({
            url: '/api/Common/IsReconVipFxUser',
            loading: false,
            data: {},
            success: function (rsp) {
                if (rsp.Success) {
                    if (rsp.Data == 1) { // 有权限
                        isReconVipFxUserStatus = true;
                    } else {
                        isReconVipFxUserStatus = false;
                    }
                } else {
                    isReconVipFxUserStatus = false;
                }
            },
            error: function () {
                layer.closeAll();
                isReconVipFxUserStatus = false;
            }
        });
    }

    // 商品结算价—规格-全选
    ChangeAllProductCheck = function () {
        $(this).find('.n-newCheckbox').toggleClass('activeF');
        if ($(this).find('.n-newCheckbox').hasClass("activeF")) {
            $(".settlementPriceFirstCheck").find('.n-newCheckbox').addClass('activeF');
        } else {
            $(".settlementPriceFirstCheck").find('.n-newCheckbox').removeClass('activeF');
        }
    }
    // 运费-订单全选
    ChangeAllFreightOrderCheck = function () {
        $(this).find('.n-newCheckbox').toggleClass('activeF');
        if ($(this).find('.n-newCheckbox').hasClass("activeF")) {
            $(".freightOrderFirstCheck").find('.n-newCheckbox').addClass('activeF');
        } else {
            $(".freightOrderFirstCheck").find('.n-newCheckbox').removeClass('activeF');
        }
    }
    // 排除已结款订单
    ChangeExcludeOrderCheck = function () {
        $(this).find('.n-newCheckbox').toggleClass('activeF');
    }
    // 已过滤未产生代发订单的用户
    ChangeFilterUserCheck = function () {
        $(this).find('.n-newCheckbox').toggleClass('activeF');
    }
    // 初始化商品结算价-首次发货
    var bindProductFirstCheckEvent = function () {
        $('.settlementPriceFirstCheck').on('click', function () {
            $(this).find('.n-newCheckbox').toggleClass('activeF');
            var activeCount = $('.settlementPriceFirstCheck .n-newCheckbox.activeF').length;
            if (activeCount == 5) {
                $("#AllProductCheck").find('.n-newCheckbox').addClass('activeF').removeClass("activeP");
            }
            if (activeCount > 0 && activeCount != 5) {
                $("#AllProductCheck").find('.n-newCheckbox').addClass("activeP").removeClass("activeF");
            }
            if (activeCount == 0) {
                $("#AllProductCheck").find('.n-newCheckbox').removeClass("activeP").removeClass("activeF");
            }
        });
    }
    // 初始化商品结算价-二次发货
    var bindProductSecondCheckEvent = function () {
        $(".settlementPriceSecondCheck").on("click", function () {
            $(this).find('.n-newCheckbox').toggleClass('activeF');
        });
    }

    // 初始化运费订单-首次发货
    var bindFreightOrderFirstCheckEvent = function () {
        $('.freightOrderFirstCheck').on('click', function () {
            $(this).find('.n-newCheckbox').toggleClass('activeF');
            var activeCount = $('.freightOrderFirstCheck .n-newCheckbox.activeF').length;
            if (activeCount == 4) {
                $("#AllFreightOrderCheck").find('.n-newCheckbox').addClass('activeF').removeClass("activeP");
            }
            if (activeCount > 0 && activeCount != 4) {
                $("#AllFreightOrderCheck").find('.n-newCheckbox').addClass("activeP").removeClass("activeF");
            }
            if (activeCount == 0) {
                $("#AllFreightOrderCheck").find('.n-newCheckbox').removeClass("activeP").removeClass("activeF");
            }
        });
    }
    // 初始化运费订单-二次发货
    var bindFreightOrderSecondCheckEvent = function () {
        $(".freightOrderSecondCheck").on("click", function () {
            $(this).find('.n-newCheckbox').toggleClass('activeF');
        });
    }

    module.LoadList = function (isPaging) {
        reqModel.Key = $.trim($("#iup_key").val());
        reqModel.Status = $("#sel_status").val();
        //1.ajax
        commonModule.Ajax({
            url: '/FinancialSettlement/LoadMyAgentList',
            data: { key: reqModel.Key, status: reqModel.Status, pageIndex: reqModel.PageIndex, pageSize: reqModel.PageSize, formtype: 1 },
            async: true,
            loading: true,
            type: 'POST',
            success: function (rsp) {
                if (commonModule.IsError(rsp)) {
                    return;
                }
                var data = rsp.Data.List || [];
                //2.渲染
                var tplt = $.templates("#shareAgentList_data_tr");
                commonModule.Foreach(data, function (i, obj) {
                    dataList[obj.Id] = obj;
                });
                data.forEach(function (item) {
                    var newRemar = item.Remark ? item.Remark : '';
                    newRemar = newRemar.replaceAll(/\"/g, '').replaceAll(/\'/g, '');
                    item.Remark = newRemar
                })

                var html = tplt.render({ agentData: data });
                $("#ShareAgentList_body").html(html);

                // 根据权限隐藏某些元素
                commonModule.HideNoPermDiv(commonModule.PriceSettingShowPermDict);

                //3.分页
                if (isPaging == true) {
                    return;
                }

                layui.laypage.render({
                    elem: 'paging',
                    theme: ' wu-page wu-one',
                    count: rsp.Data.Total,
                    limit: reqModel.PageSize,
                    curr: reqModel.PageIndex,
                    limits: [50, 100, 150, 200, 300, 500],
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function (obj, first) {
                        //$(".layui-laypage-count").html()
                        if (!first) {
                            reqModel.PageSize = obj.limit;
                            reqModel.PageIndex = obj.curr;
                            module.LoadList(true);
                        }
                    }
                });
            }
        });
    }

    module.Search = function () {
        reqModel.PageIndex = 1;
        module.LoadList(false);
    }

    module.chooseTr = function (_this) {
        if ($(_this).hasClass("active")) {
            $(_this).removeClass("active");
            $(_this).find(".agent-chx")[0].checked = false;
        } else {
            $(_this).addClass("active");
            $(_this).find(".agent-chx")[0].checked = true;
        }
        var isAll = $("#ShareAgentList_body .agent-chx:checked").length == $("#ShareAgentList_body .agent-chx").length;
        $("#allCheck")[0].checked = isAll;
        $(".chooseOrderMun").text($("#ShareAgentList_body .agent-chx:checked").length);
    }

    module.allCheck = function (_this) {
        var isAll = $(_this)[0].checked;
        $("#ShareAgentList_body .agent-chx").each(function (index, item) {
            $(item)[0].checked = isAll;
            var $tr = $(item).closest("tr");
            if (isAll) {
                $tr.addClass("active");
            } else {
                $tr.removeClass("active");
            }
        })
        $(".chooseOrderMun").text($("#ShareAgentList_body .agent-chx:checked").length);

    }

    module.ClickPrice = function (id, fxUserId, name, remark, isUnSetPrice) {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.SetProductSettlementPrice);
        });

        var thisFunc = function () {
            commmon.StopPropagation(event);
            var tempname = name + (remark ? "(" + remark + ")" : "");
            isUnSetPrice = isUnSetPrice || 0;
            var hrefstr = commmon.rewriteUrl("/FinancialSettlement/PriceSettingProduct?fxUserId=" + fxUserId + "&type=2&name=" + encodeURIComponent(tempname)) + "&isUnSetPrice=" + isUnSetPrice;
            hrefstr = encodeURI(commmon.dbnameToAjaxUrl(hrefstr)); //参数携带中文需要两次编码
            //parent.window.location.href = hrefstr;
            window.open(hrefstr);
        //parent.window.location.href = commmon.rewriteUrl("/FinancialSettlement/PriceSettingProduct?fxUserId=" + fxUserId + "&type=2&name=" + encodeURIComponent(tempname));
        }
        
    }

    module.toSkuRecycle = function () {
        commmon.StopPropagation(event);
        var hrefstr = commmon.rewriteUrl("/FinancialSettlement/SkuRecycle?to=agent");
        window.open(hrefstr);
    }
    // 批量出账-对账中心展示 1  0
    var ContainsChildTask = 1;
    var IsWhiteUser = true;
    // 账单明细导出设置回显
    onRenderBillSetting = function (BillSettingData) {
        BillSettingData.forEach(function (item) {
            $('.billSettingDailog .layui-form-item .n-newCheckbox').each(function () {
                var name = $(this).attr('name');
                var $this = $(this);
                if (item.Key == name) {
                    if (item.Value && (item.Value == 'True' || item.Value == 'true')) {
                        $this.addClass('activeF');
                    } else {
                        $this.removeClass('activeF');
                    }
                }
                if (item.Key.indexOf('/FxSystem/OutAccountExtSettings/IsOutOrderDetail') > -1) {
                    $('.billSettingDailog input[name="billSetting"][value="' + item.Value + '"]').prop("checked", true);
                }
                if (item.Key == '/ErpWeb/Settlement/ContainsChildTask') {
                    ContainsChildTask = item.Value == "true" || item.Value == "True" ? 1 : 0;
                    $('.billSettingDailog input[name="/ErpWeb/Settlement/ContainsChildTask"][value="' + ContainsChildTask + '"]').prop("checked", true);
                }
                if (item.Key == '/ErpWeb/Settlement/ExportImg') {
                    var val = item.Value == "true" || item.Value == "True" ? 1 : 0;
                    $('.billSettingDailog input[name="/ErpWeb/Settlement/ExportImg"][value="' + val + '"]').prop("checked", true);
                }
            })
        });

    }
    //账单明细导出设置
    module.billSetting = function (isHide) {

        commonModule.Ajax({
            type: "GET",
            loading: true,
            data: {},
            url: "/FinancialSettlement/GetBatchSettlementPopup",
            success: function (res) {
                if (res.Success) {

                    onRenderBillSetting(res.Data);
                    //if (res.Data)
                    //    $(".billSettingDailog").find("input[value='1']").prop("checked", true);
                    //else
                    //    $(".billSettingDailog").find("input[value='0']").prop("checked", true);
                    // 是否白名单用户
                    commonModule.ajax({
                        type: 'GET',
                        url: '/api/Common/GetIsWhiteUser',
                        success: function (res) {
                            if (res.Success) {
                                IsWhiteUser = res.Data;
                            }
                        }
                    })
                    if (isHide) return
                    layer.open({
                        type: 1,
                        title: '出账设置',
                        content: $('.billSettingDailog'), //这里content是一个普通的String
                        btn: ['取消', '保存设置'],
                        skin: 'n-skin',
                        area: ['600px'],
                        success: function (layero, index) {
                            layero.find('.layui-layer-title').css({'font-weight': '500'});
                            layero.find('.billSettingDailog .layui-form-item .label').css({'font-weight': 'normal'});
                            layero.find('.billSettingDailog .layui-form-item ul li span:nth-child(2)').css({'color': 'rgba(0, 0, 0, 0.9)'});
                            layero.find('.billSettingDailog ul').css({'margin-left': '12px'});
                            $('.billSettingDailog .layui-form-item .n-newCheckbox').off('click').on('click', function () {
                                $(this).toggleClass('activeF');
                            })
                            $('.billSettingDailog .layui-form-item.ContainsChildTask').hide();
                            if (IsWhiteUser) {
                                $('.billSettingDailog .layui-form-item.ContainsChildTask').show();
                            }
                        },
                        yes: function (index, layero) {
                            layer.close(index);
                        },
                        btn2: function (index, layero) {
                            var options = getOptions();
                            layer.close(index);
                            commonModule.Ajax({
                                type: "POST",
                                loading: true,
                                contentType: 'application/json',
                                data: JSON.stringify({ Dic: options }),
                                url: "/FinancialSettlement/BatchSettlementPopup",
                                success: function (res) {
                                    if (res.Success) {
                                        layer.msg("设置成功");
                                        ContainsChildTask = $('.billSettingDailog input[name="/ErpWeb/Settlement/ContainsChildTask"]:checked').val();
                                        $('.billSettingDailog .layui-form-item .n-newCheckbox').each(function () {
                                            var name = $(this).attr('name');
                                            if (name == '/ErpWeb/Settlement/UseLastTime') {
                                                UseLastTime = $(this).hasClass('activeF') ? true : false;
                                            }
                                        });
                                        module.LoadList(false);
                                    } else {
                                        layer.msg("服务器繁忙，请稍后再试");
                                    }
                                }
                            });
                        }
                    });
                }
            }
        });
    }
    getOptions = function () {
        var ContainsChildTaskVal = $('.billSettingDailog input[name="/ErpWeb/Settlement/ContainsChildTask"]:checked').val() == "1" ? true : false;
        var ExportImg = $('.billSettingDailog input[name="/ErpWeb/Settlement/ExportImg"]:checked').val() == "1" ? true : false;
        var options = {
            "/FxSystem/OutAccountExtSettings/IsOutOrderDetail": $('.billSettingDailog input[name="billSetting"]:checked').val(),//账单展示订单明细
            "/ErpWeb/Settlement/ContainsChildTask": ContainsChildTaskVal,  //批量出账-对账中心展示
            "/ErpWeb/Settlement/ExportImg": ExportImg, //账单文件展示图片
        }
        $('.billSettingDailog .layui-form-item .n-newCheckbox').each(function () {
            var name = $(this).attr('name');
            options[name] = $(this).hasClass('activeF') ? true : false;
        });
        return options;
    }


    //以下更新的---------------


    //出账
    module.SingleOutAccount = function (id, FxUserId, name, remark) {
        commmon.StopPropagation(event);

        //清除过滤未代发用户复选框值
        $("#isHideData").prop('checked', false);
        $("#isFilterUsers").hide();

        var showOutAccountDailog = function () {
            module.clearOutAccountDailogValue();
            $(".outAccount-Products").show();
            $("#outAccountFxUserIds").val(FxUserId);
            var agentName = name + (remark ? "(" + remark + ")" : "");
            $("#outAccountDailog_agentName").text(agentName);
            module.ShowOutAccountDailog();
        }

        module.CheckIsSetPrice(id, false, FxUserId, showOutAccountDailog);
    }

    module.GetUnSetPriceSkuCount = function (toFxUserIds, callBack) {
        commonModule.Ajax({
            type: "POST",
            loading: true,
            data: {
                "toFxUserId": toFxUserIds,
                "usertype": 2
            },
            url: "/FinancialSettlement/CheckIsSetPrice",
            success: function (res) {
                if (typeof callBack == "function") {
                    if (res.Success == true && !!res.Data) {
                        var result = res.Data || [];
                        callBack(result);
                    }
                    else {
                        callBack();
                    }
                }

            }
        });
    }
    // 清除出账弹窗更改的值
    module.clearOutAccountDailogValue = function () {
        //$("input[name=outAccountProduct]").each(function () {
        //    this.checked = false;
        //})
        // 出账类型-默认选中第一个
        $('input[name="accountType"]').first().prop('checked', true);

        // 出账商品-默认选中第一个
        $('input[name="outAccountProduct"]').first().prop('checked', true);

        //$("input[name=orderStatus]").each(function () {
        //    this.checked = false;
        //})
        //$("input[name=saleStatus]").each(function () {
        //    this.checked = false;
        //})
        //$("input[name=settlementData]").each(function () {
        //    this.checked = false;
        //});
        initQueryDateBox();
        $(".lastTimeTips").html("").hide();
        $("#outAccountFxUserIds").val("");
        $("#isBatchOutAccount").val("0");
        //$(".outAccount-Products").hide();
        //$("#chooseProducts").addClass("hide");
        //$("#orderStatus").removeClass("active").text("全选");
        $("#outAccountDailog_agentName").text("");
        _productSkuList = [];
        $("#HaveSelectedProductCount").text(0);
        // 移除所有n-newCheckbox下的activeF类名
        $('#AccountBasisDailogContent .n-newCheckbox').removeClass('activeF activeP');
        $("#ShowSettlementPriceContent").show(); // 显示商品结算价-规格
        $("#ShowFreightOrderContent").hide(); // 隐藏运费-订单
        $("#ShowFreightTemplate").hide(); // 隐藏运费设置
        $("#ShowDesignProductRadio").show(); // 显示按指定商品出账radio
        $("#ShowDesignProductSelect").hide(); // 隐藏产品选择
        $("#ExcludePaidOrderCheck").find('.n-newCheckbox').addClass("activeF"); // 排除已结款订单默认选中
    }

    // 出账类型初始化事件
    var bindAccountTypeRadio = function () {
        $('input[name="accountType"]').change(function () {
            var $val = $(this).val();
            if ($val == 0) {
                $("#ShowSettlementPriceContent").show(); // 显示商品结算价-规格
                $("#ShowFreightOrderContent").hide(); // 隐藏运费-订单
                $("#ShowFreightTemplate").hide(); // 隐藏运费设置
                $("#ShowDesignProductRadio").show(); // 显示按指定商品出账radio
                var selectedValue = $('input[name="outAccountProduct"]:checked').val();
                if (selectedValue == 'part') {
                    $("#ShowDesignProductSelect").show(); // 显示产品选择
                } else {
                    $("#ShowDesignProductSelect").hide(); // 隐藏产品选择
                }
            } else if ($val == 1) {
                $("#ShowSettlementPriceContent").show(); // 显示商品结算价-规格
                $("#ShowFreightOrderContent").show(); // 显示运费-订单
                $("#ShowFreightTemplate").show(); // 显示运费设置
                $("#ShowDesignProductRadio").hide(); // 隐藏按指定商品出账radio
                $("#ShowDesignProductSelect").hide(); // 隐藏产品选择
            } else {
                $("#ShowSettlementPriceContent").hide(); // 隐藏商品结算价-规格
                $("#ShowFreightOrderContent").show(); // 显示运费-订单
                $("#ShowFreightTemplate").show(); // 显示运费设置
                $("#ShowDesignProductRadio").hide(); // 隐藏按指定商品出账radio
                $("#ShowDesignProductSelect").hide(); // 隐藏产品选择
            }
        });
    }

    // 出账商品初始化事件
    module.bindOutAccountProductRadio = function () {
        $("input[name=outAccountProduct]").on("change", function () {
            var $value = $(this).val();
            if ($value == "part") {
                // $("#chooseProducts").removeClass("hide");
                $("#ShowDesignProductSelect").show();
            } else {
                // $("#chooseProducts").addClass("hide");
                $("#ShowDesignProductSelect").hide();
            }
        });
    }

    module.outAccountCheckStatus = function (_this) {
        var isChecked = true;
        if ($(_this).hasClass("active")) {
            isChecked = false;
            $(_this).text("全选").removeClass("active");
        } else {
            isChecked = true;
            $(_this).text("反选").addClass("active");
        }
        $("input[name=orderStatus],input[name=saleStatus]").each(function () {
            this.checked = isChecked;
        });
        $("input[name=orderStatus],input[name=packageStatus]").each(function () {
            this.checked = isChecked;
        })
    }

    module.chooseProductsDailog = function () {
        console.dir(_productSkuList);
        var index = layer.open({
            type: 1,
            title: "选择对账商品",
            content: $('.outAccountProductDailog'),
            offset: '10px',
            area: ["660px"], //宽高
            skin: 'n-skin',
            btn: ['取消', '确定'],
            btn2: function () {
                triggerChildPageMonitorEvent_2();
                layer.close(index);
            },
        });
    }


    //以下又更新的---------------
    module.BatchOutAccount = function (isWhiteList) {
        module.clearOutAccountDailogValue();
        var checkedFxUserIds = [];
        var agentNameList = [];
        $("#isFilterUsers").show();
        $("#ShareAgentList_body .agent-chx:checked").each(function () {
            var thisfxUserId = $(this).data("fxuserid");
            var thisfxNickName = $(this).data("nickname");
            var thisfxRemarkName = $(this).data("remarkname");
            if (!!thisfxUserId)
                checkedFxUserIds.push(thisfxUserId);
            if (!!thisfxNickName) {
                var agentName = thisfxNickName + (thisfxRemarkName ? "(" + thisfxRemarkName + ")" : "");
                agentNameList.push(agentName);
            }
        });
        if (checkedFxUserIds.length == 0) {
            layer.msg("请先选择商家");
            return false;
        }

        var showOutAccountDailog = function () {

            $("#outAccountFxUserIds").val(checkedFxUserIds.join(','));
            $("#isBatchOutAccount").val("1");
            $("#outAccountDailog_agentName").text(agentNameList.join('、'));
            module.ShowOutAccountDailog(true, isWhiteList);
        }

        module.CheckIsSetPrice(0, true, checkedFxUserIds.join(','), showOutAccountDailog);

    }


    module.CheckIsSetPrice = function (id, isBatch, toFxUserIds, showOutAccountDailog) {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.OutAccountFxUser);
        });


        var thisFunc = function () {
            var showConfirmDailog = function (r) {
                if (r && r.length > 0) {

                    var pddTotalUnSetCount = 0;
                    var jdTotalUnSetCount = 0;
                    var toutiaoTotalUnSetCount = 0;
                    var totalUnSetCount = 0;
                var tkUnSetCount = 0;
                    $(r).each(function (i, item) {
                        if (item.PlatformType == "Pinduoduo")
                            pddTotalUnSetCount += item.Count;
                        else if (item.PlatformType == "Jingdong")
                            jdTotalUnSetCount += item.Count;
                        else if (item.PlatformType == "TouTiao")
                            toutiaoTotalUnSetCount += item.Count;
                    else if (item.PlatformType == "TikTok")
                        tkUnSetCount += item.Count;
                        else
                            totalUnSetCount += item.Count;
                    });

                    var unSetCountText = "";
                    if (totalUnSetCount > 0)
                        unSetCountText += "精选平台:【" + totalUnSetCount + "】，";
                    if (pddTotalUnSetCount > 0)
                        unSetCountText += "拼多多:【" + pddTotalUnSetCount + "】，";
                    if (jdTotalUnSetCount > 0)
                    unSetCountText += "京东:【" + jdTotalUnSetCount + "】，";
                    if (toutiaoTotalUnSetCount > 0)
                        unSetCountText += "抖店:【" + toutiaoTotalUnSetCount + "】，";
                if (tkUnSetCount > 0) {
                    unSetCountText += "TikTok:【" + tkUnSetCount + "】，";
                }
                    $(".unsetpriceskucount").text(" (" + unSetCountText.trimEndDgj("，") + ") ");
                    var btns = ["继续对账", "返回设置结算价"];
                    //if (isBatch)
                    //    btns = ["继续对账"];

                    var index = layer.open({
                        type: 1,
                        title: '出账提示',
                        //btn: false,
                        content: $(".OutAccountingWarnDailog"),//'<div class="OutAccountingWarnDailog"><div class="OutAccountingWarnDailog-title">当前出账对象有xx个Sku未设置结算价，为保证对账准确，是否返回设置结算价后再对账？</div></div>',
                        area: ["400px"], //宽高
                        skin: "wu-dailog",
                        btn: btns,
                        yes: function () {
                            layer.close(index);
                            var isChecked = $("#NotTipInHalfMonth").prop("checked");
                            if (isChecked) {
                                commonModule.SaveDateTimeCommonSetting(key, "");
                            }
                            showOutAccountDailog();
                        },
                        btn2: function () {
                            layer.close(index);
                            var isChecked = $("#NotTipInHalfMonth").prop("checked");
                            if (isChecked) {
                                commonModule.SaveDateTimeCommonSetting(key, "");
                            }
                            // 加载未设置结算价规格
                            if (id > 0) {
                                var $elmt = $("#tr-" + id).find(".setprice");
                                var onclick = $elmt.attr("onclick");
                                $elmt.attr("onclick", onclick.replace(")", ",1)"));
                                $elmt.click();
                            }
                            else {
                                var toIds = "";
                                var len = r.length;
                                $(r).each(function (i, item) {
                                    toIds += item.ToFxUserId + (i < len - 1 ? "," : "");
                                });
                                toIds = toIds;
                                module.ClickPrice(0, toIds, "批量设置结算价", "", 1);
                            }
                        }
                    });
                }
                else {
                    if (typeof showOutAccountDailog == "function")
                        showOutAccountDailog();
                }
            }

            var key = "/ErpWeb/OutAccount/Agent/UnSetPriceDailog";
            commonModule.LoadCommonSetting(key, false, function (rsp) {
                if (rsp.Success && rsp.Data) {

                    var setTimes = new Date(rsp.Data).getTime(); // 设置不弹窗时间
                    var nowTimes = new Date(commonModule.ServerNowTime).getTime(); // 设置不弹窗时间
                    var diffDays = (nowTimes - setTimes) * 1.0 / 1000 / 60 / 60 / 24;
                    //var date = new Date(rsp.Data);
                    //var diffDays = date.DateDiff('d', new Date(commonModule.ServerNowTime));
                    if (diffDays > 15) {
                        module.GetUnSetPriceSkuCount(toFxUserIds, function (r) {
                            showConfirmDailog(r);
                        });
                    }
                    else {
                        if (typeof showOutAccountDailog == "function")
                            showOutAccountDailog();
                    }
                }
                else {
                    module.GetUnSetPriceSkuCount(toFxUserIds, function (r) {
                        showConfirmDailog(r);
                    });
                }
            });
        }
        
    }
    module.formatNumber = function (n) {
        n = n.toString();
        return n[1] ? n : '0' + n;
    }

    // 确认出账
    function handleConfirmAccountClick(isBatch, isWhiteList, FxUserIds, index) {
        if (!FxUserIds) {
            layer.msg("未读取到出账对象");
            return false;
        }
        var startTime = $("#inputSelectTime .QueryDateVal").attr("start-date") || "";
        var endTime = $("#inputSelectTime .QueryDateVal").attr("end-date") || "";
        var options = getOutAccountOptions(startTime, endTime);
        if (options == null) return false;
        var fxUserIdCount = FxUserIds.split(',').length || 0;

        commonModule.Ajax({
            type: "POST",
            loading: true,
            data: {
                "supplierFxUserIds": FxUserIds,
                "isBatch": isBatch == undefined || isBatch == null ? false : isBatch,
                "usertype": 2,
                "starttime": startTime,
                "endtime": endTime,
                "options": options,
            },
            url: "/FinancialSettlement/CreateOutAccount",
            success: function (res) {
                if (res.Success == true && !!res.Data) {
                    layer.close(index);
                    // 白名单用户和对账设置 - 批量出账-对账中心不展示
                    if (ContainsChildTask != 1 && IsWhiteUser) {
                        var msg = fxUserIdCount > batchCount ? "系统识别你当前勾选的批量账单数量大于" + batchCount + "已为你拆分成多个子任务排队进行出账请您在任务中心查看任务进度" : "正在出账中，请前往任务中心查看账单";
                        layer.open({
                            type: 1,
                            title: false, //不显示标题
                            content: $("#outAccountSuccessDailog"),
                            area: '500', //宽高
                            // skin: 'adialog-Shops-skin',
                            skin: "wu-dailog",
                            btn: false,
                        });
                    } else {
                        if (isWhiteList === true)//白名单用户与普通用户跳转界面不同
                        {
                            var msg = fxUserIdCount > batchCount ? "系统识别你当前勾选的批量账单数量大于" + batchCount + "已为你拆分成多个子任务排队进行出账请您在任务中心查看任务进度" : "正在出账中，请前往任务中心查看账单";
                            layer.open({
                                type: 1,
                                title: false, //不显示标题
                                content: $("#outAccountSuccessDailog"),
                                area: '500', //宽高
                                // skin: 'adialog-Shops-skin',
                                skin: "wu-dailog",
                                btn: false,
                            });
                        }
                        else {
                            var msg = fxUserIdCount > batchCount ? "系统识别你当前勾选的批量账单数量大于" + batchCount + "已为你拆分成多个子任务排队进行出账请您在对账中心查看任务进度" : "正在出账中，请前往对账中心查看账单";
                            var linkUrl = isReconVipFxUserStatus ? '/FinancialSettlement/BillCenter?navs=DownBill&accountStatus=MyAgent&token=' + _token : '/FinancialSettlement/BillManagement?navs=DownBill&accountStatus=MyAgent&token=' + _token;
                            layer.open({
                                type: 1,
                                title: '出账中',
                                btn: false,
                                content: '<div class="OutAccountingDailog"><div class="OutAccountingDailog-title">' + msg + '</div><a class="dColor hover" target="_top" href="' + linkUrl + '">点击前往对账中心</a></div>',
                                area: ["400px"], //宽高
                                skin: "wu-dailog",
                            });
                        }
                    }

                    
                } else {
                    layer.msg(res.Message);
                }
            }
        });
        triggerChildPageMonitorEvent_1();
    }

    // 出账依据弹窗
    module.ShowOutAccountDailog = function (isBatch, isWhiteList) {
        gotoCloudPlatform();
        var FxUserIds = $("#outAccountFxUserIds").val();
        //console.log(FxUserIds);

        var users = FxUserIds.split(",");
        var showCrossBorderSelect = false;
        Object.keys(dataList).forEach(function (key) {
            var o = dataList[key];
            if (users.indexOf(o.FxUserId+'') !== -1 && o.HasCrossBorderShop === true) {
                showCrossBorderSelect = true;
                return;
            }
        });

        if (showCrossBorderSelect) {
            $("#packageStatus").show();
        } else {
            $("#packageStatus").hide();
        }


        var outAccount = function () {
            var index = layer.open({
                type: 1,
                title: '出账依据-商家',
                content: $('#AccountBasisDailogContent'),
                area: ["720px", 'auto'], // 宽高
                skin: 'n-skin',
                offset: '10px',
                move: false,
                // btn: ['确认出账', '取消'],
                success: function (layero, index) {
                    // 取消
                    $("#CancelAccountBtn").on("click", function () {
                        layer.close(index);
                        triggerChildPageMonitorEvent_1();
                    });

                    // 确认出账
                    $("#ConfirmAccountBtn").on("click", function () {
                        handleConfirmAccountClick(isBatch, isWhiteList, FxUserIds, index);
                    });

                    // 出账时间段控制
                    var endDate = new Date();
                    var endTime = endDate.getFullYear() + "-" + module.formatNumber(endDate.getMonth() + 1) + "-" + module.formatNumber(endDate.getDate()) + " 23:59:59";
                    //在当前日期上减去45天
                    var startDate = new Date(endDate.setDate(endDate.getDate() - 44));
                    var startTime = startDate.getFullYear() + "-" + module.formatNumber(startDate.getMonth() + 1) + "-" + module.formatNumber(startDate.getDate()) + " 00:00:00";
                    //时间段范围
                    var rangeTime = startTime + "~" + endTime;
                    $("#outAccountTimeRangeTip").html("(只支持 " + rangeTime + " 时间段出账)").show();

                    if (!!FxUserIds && FxUserIds.indexOf(',') == -1 && UseLastTime == true) {
                        commonModule.Ajax({
                            type: "POST",
                            data: { fxUserId: FxUserIds },
                            url: "/FinancialSettlement/GetSettlementLastTime",
                            success: function (res) {
                                if (!!res.Data) {
                                    initQueryDateBox(res.Data);

                                    //兼容日历控件不赋值时间问题 ------//2021-08-27 23:59:59
                                    try {
                                        var timestr = res.Data.substring(10, 16);// 23:59
                                        var hourstr = res.Data.substring(11, 13);//23
                                        var minutestr = res.Data.substring(14, 16);//59
                                        var secondstr = res.Data.substring(17, 19);//59
                                        $("#inputSelectTime .newwrapperTime_input i:eq(1)").html(timestr);
                                        $("#inputSelectTime .QueryDateVal").attr("start-date", res.Data);
                                        $("#inputSelectTime .newcalender-footer .hourselect").val(hourstr);
                                        $("#inputSelectTime .newcalender-footer .minuteselect").val(minutestr);
                                        $("#inputSelectTime .newcalender-footer .secondselect").val(secondstr);
                                    } catch (err) {
                                        initQueryDateBox(res.Data);
                                    }

                                    $(".lastTimeTips").html("上次出账时间：" + res.Data).show();
                                }
                            }
                        });
                    }
                },
                cancel: function () {
                    triggerChildPageMonitorEvent_1();
                },
                end: function () {
                    $("#ConfirmAccountBtn").off("click");
                    $("#CancelAccountBtn").off("click");
                }
            });
        }

        commonModule.Ajax({
            type: "POST",
            loading: true,
            url: "/FinancialSettlement/CheckIsOverstock",
            success: function (res) {
                if (res.Success == true) {
                    var isOverstock = res.Data == "1";
                    if (isOverstock) {
                        var layerIndex = layer.confirm("系统当前出账任务繁忙，预计出账时间较久您可选择其他时间出账？", {
                            title: "提示",
                            btn: ['暂不出账', '排队出账'],
                            icon: 3,
                            skin: "wu-dailog"
                        }, function () {
                            layer.close(layerIndex);
                        }, function () {
                            outAccount(); // 是：继续对账
                        });
                    }
                    else {
                        outAccount();
                    }
                } else {
                    outAccount();
                }
            }
        });

    }

    module.targetUrl = function () {

        commonModule.setStorage('tarDownBill', true);
        window.open(commmon.rewriteUrl("/ExportTask/Index?navs=DownBill&accountStatus=MyAgent"));

    }

    // 出账选择信息表单
    var getOutAccountOptions = function (startTime, endTime) {
        var options = {};
        options.Filters = [];
        options.FreightFilters = [];
        options.BillType = $('input[name=accountType]:checked').val() || 0 ;// 出账类型
        // 出账商品
        var outAccountProductType = $('input[name=outAccountProduct]:checked').val();
        // 批量出账
        var isBatchOutAccount = $("#isBatchOutAccount").val();
        if (isBatchOutAccount == "1")
            outAccountProductType = "all";
        if (outAccountProductType == "all") {
            options.Filters.push({ FieldType: "string", Name: "Product", Value: "all", ExtValue: "", Contract: "" });
            options.Filters.push({ FieldType: "string", Name: "TaskFlag", Value: "1", ExtValue: "", Contract: "in" });
        } else if (outAccountProductType == "part") {
            if (_productSkuList.length > 0) {
                options.Filters.push({ FieldType: "string", Name: "Product", Value: JSON.stringify(_productSkuList), ExtValue: "", Contract: "" });
                options.Filters.push({ FieldType: "string", Name: "TaskFlag", Value: "1", ExtValue: "", Contract: "in" });
            } else {
                layer.msg("请选择商品");
                return null;
            }
        }
        else {
            layer.msg("请选择全部或部分商品");
            return null;
        }

        // 商品结算价-规格无退款-订单状态
        var orderStatusList = [];
        //$("#outAccountDailog_status input[name=orderStatus]:checked").each(function () {
        //    var orderStatu = $(this).val();
        //    if (!!orderStatu)
        //        orderStatusList.push(orderStatu);
        //});
        $('#product_price_status .orderStatus .activeF').each(function () {
            // 获取data-value属性
            var orderStatus = $(this).data('value');
            if (orderStatus) {
                orderStatusList.push(orderStatus);
            }
        });
        if (orderStatusList.length > 0) {
            options.Filters.push({ FieldType: "string", Name: "OrderStatus", Value: orderStatusList.join(','), ExtValue: "", Contract: "in" });
        }
        // 商品结算价-规格无退款-订单包裹状态-跨境
        var packageStatusList = [];
        //$("#outAccountDailog_status input[name=packageStatus]:checked").each(function () {
        //    var packageStatus = $(this).val();
        //    if (!!packageStatus)
        //        packageStatusList.push(packageStatus);
        //});
        $('#product_price_status .packageStatus .activeF').each(function () {
            // 获取data-value属性
            var packageStatus = $(this).data('value');
            if (packageStatus) {
                packageStatusList.push(packageStatus);
            }
        });
        
        if (packageStatusList.length > 0) {
            options.Filters.push({ FieldType: "string", Name: "PackageStatus", Value: packageStatusList.join(','), ExtValue: "", Contract: "in" });
        }

        // 商品结算价-规格有退款-商品售后状态
        var saleStatusList = [];
        //$("#outAccountDailog_saleStatus input[name=saleStatus]:checked").each(function () {
        //    var saleStatus = $(this).val();
        //    if (!!saleStatus)
        //        saleStatusList.push(saleStatus);
        //});
        $('#product_price_saleStatus .saleStatus .activeF').each(function () {
            // 获取data-value属性
            var saleStatus = $(this).data('value');
            if (saleStatus) {
                saleStatusList.push(saleStatus);
            }
        });

        if (saleStatusList.length > 0) {
            options.Filters.push({ FieldType: "string", Name: "SaleStatus", Value: saleStatusList.join(','), ExtValue: "", Contract: "in" });
        }

        if ((options.BillType == 0 || options.BillType == 1) && !orderStatusList.length && !saleStatusList.length && !packageStatusList.length) {
            layer.msg("请选择查询商品规格的订单条件/商品售后状态");
            return;
        }

        // 商品结算价-二次发货
        var sendTypeList = [];
        //$("#outAccountDailog_sendType input[name=sendType]:checked").each(function () {
        //    var sendType = $(this).val();
        //    if (!!sendType)
        //        sendTypeList.push(sendType);
        //});
        $('#product_price_sendType .sendType .activeF').each(function () {
            // 获取data-value属性
            var sendType = $(this).data('value');
            if (sendType) {
                sendTypeList.push(sendType);
            }
        });
        if (sendTypeList.length > 0) {
            options.Filters.push({ FieldType: "string", Name: "SendType", Value: sendTypeList.join(','), ExtValue: "", Contract: "in" });
        }

        // 运费订单-发货状态
        var sendStatusList = [];
        $('#freight_order_sendStatus .sendStatus .activeF').each(function () {
            // 获取data-value属性
            var sendStatus = $(this).data('value');
            if (sendStatus) {
                sendStatusList.push(sendStatus);
            }
        });
        if (sendStatusList.length > 0) {
            options.FreightFilters.push({ FieldType: "string", Name: "SendStatus", Value: sendStatusList.join(','), ExtValue: "", Contract: "in" });
        }

        // 运费订单-售后状态
        var refundStateList = [];
        $('#freight_order_refundState .refundState .activeF').each(function () {
            // 获取data-value属性
            var refundState = $(this).data('value');
            if (refundState) {
                refundStateList.push(refundState);
            }
        });
        if (refundStateList.length > 0) {
            options.FreightFilters.push({ FieldType: "string", Name: "RefundState", Value: refundStateList.join(','), ExtValue: "", Contract: "in" });
        }

        if ((options.BillType == 1 || options.BillType == 2) && !sendStatusList.length && !refundStateList.length) {
            layer.msg("请选择查询运费订单的发货状态/售后状态");
            return;
        }

        // 运费订单-二次发货
        var freightSendTypeList = [];
        $("#freight_order_sendType .sendType .activeF").each(function () {
            // 获取data-value属性
            var freightSendType = $(this).data('value');
            if (freightSendType) {
                freightSendTypeList.push(freightSendType);
            }
        });
        if (freightSendTypeList.length > 0) {
            options.FreightFilters.push({ FieldType: "string", Name: "SendType", Value: freightSendTypeList.join(','), ExtValue: "", Contract: "in" });
        }

        // 是否排除已结款订单
        var isFilterPrePayOrderValue = 0;
        //if ($(".isFilterPrePayOrder").hasClass("active")) {
        //    isFilterPrePayOrderValue = 1;
        //};
        if ($("#ExcludePaidOrderCheck").find('.n-newCheckbox').hasClass("activeF")) {
            isFilterPrePayOrderValue = 1;
        }
        options.Filters.push({ FieldType: "string", Name: "IsFilterPrePayOrder", Value: isFilterPrePayOrderValue, ExtValue: "", Contract: "" });

        // 结算时间、出账周期
        var settlementData = $('input[name=settlementData]:checked').val();
        if (!!settlementData) {
            //转换成毫秒进行比较
            var oDate1 = new Date(startTime);
            var oDate2 = new Date(endTime);
            if (oDate1.getTime() == oDate2.getTime()) {
                layer.msg("出账周期起止时间不能相同");
                return null;
            }
            if (oDate1.getTime() > oDate2.getTime()) {
                layer.msg("开始时间不能大于结束时间");
                return null;
            }
            if (startTime != "" && endTime != "") {
                options.Filters.push({ FieldType: "DateTime", Name: settlementData, Value: startTime, ExtValue: endTime, Contract: "between" });
            }
            else {
                layer.msg("请选择出账周期");
                return null;
            }
        } else {
            layer.msg("请选择结算时间");
            return null;
        }

        // 过滤不展示未发货账单(批量出账)
        //var isHideNotData = $('input[name=isHideNotData]:checked').val();
        //if (!!isHideNotData) {
        //    options.Filters.push({ FieldType: "boolean", Name: "IsHideNotData", Value: "true", ExtValue: "", Contract: "" });
        //}
        if ($("#isFilterUsers").find('.n-newCheckbox').hasClass("activeF")) {
            options.Filters.push({ FieldType: "boolean", Name: "IsHideNotData", Value: "true", ExtValue: "", Contract: "" });
        }
        //用户类型(商家=2)
        options.Filters.push({ FieldType: "Int", Name: "UserType", Value: "2", ExtValue: "", Contract: "" });
        //用户（针对批量出账，后台插入时再替换对应id）
        options.Filters.push({ FieldType: "Int", Name: "FxUserId", Value: "@@FxUserId", ExtValue: "", Contract: "" });

        return options;
    }


    var initQueryDateBox = function (startDate) {
        var obj = {
            days: 7, //天数
            startDate: startDate, //startDate可以不用填 不用填调用客户端时间  格式：yyyy-MM-dd
            endDate: commonModule.ServerNowDate //endDate可以不用填 不用填调用客户端时间      格式：yyyy-MM-dd
        };
        commonModule.InitNewCalenderTime("#inputSelectTime", obj);
    }

    //发送消息 给子页面 [触发 获取选中商品方法]
    var triggerChildPageMonitorEvent_2 = function () {
        var id = document.getElementById('productFrame_');
        id.contentWindow.postMessage({ refresh: 'getCheckProductSkuList' }, '*');
        var id_pdd = document.getElementById('productFrame_Pinduoduo');
        id_pdd.contentWindow.postMessage({ refresh: 'getCheckProductSkuList' }, '*');
        var id_jd = document.getElementById('productFrame_Jingdong');
        id_jd.contentWindow.postMessage({ refresh: 'getCheckProductSkuList' }, '*');
        var id_tt = document.getElementById('productFrame_Toutiao');
        id_tt.contentWindow.postMessage({ refresh: 'getCheckProductSkuList' }, '*');

        if (commonModule.IsShowCrossBorder) {
            var id_tik = document.getElementById('productFrame_TikTok');
            id_tik.contentWindow.postMessage({ refresh: 'getCheckProductSkuList' }, '*');
        }
    }

    ///接收子页面消息 [获取已选中的商品]
    window.addEventListener('message', function (event) {
        if (event.data.refresh == "productSkuList") {
            if (!event.data.productSkuList) return false;
            commonModule.Foreach(event.data.productSkuList, function (i, newSku) {
                if (!newSku) return;
                var exist = $.grep(_productSkuList, function (e) { return newSku.ProductCode == e.ProductCode && newSku.ProductSkuCode == e.ProductSkuCode; });
                if (exist.length == 0) {
                    _productSkuList.push(newSku);
                    $("#HaveSelectedProductCount").text(_productSkuList.length);
                }
            });
        }
    });

    //发送消息 给子页面 [清除已选择商品缓存]
    var triggerChildPageMonitorEvent_1 = function () {
        var fxUserId = $("#outAccountFxUserIds").val();
        var id = document.getElementById('productFrame_');
        var id_pdd = document.getElementById('productFrame_Pinduoduo');
        var id_jd = document.getElementById('productFrame_Jingdong');
        var id_tt = document.getElementById('productFrame_Toutiao');
        id.contentWindow.postMessage({ refresh: 'localStorageClear', fxUserId: fxUserId }, '*');
        id_pdd.contentWindow.postMessage({ refresh: 'localStorageClear', fxUserId: fxUserId }, '*');
        id_jd.contentWindow.postMessage({ refresh: 'localStorageClear', fxUserId: fxUserId }, '*');
        id_tt.contentWindow.postMessage({ refresh: 'localStorageClear', fxUserId: fxUserId }, '*');

        ///跨境
        if (commonModule.IsShowCrossBorder) {
            var id_tik = document.getElementById('productFrame_TikTok');
            id_tik.contentWindow.postMessage({ refresh: 'getCheckProductSkuList' }, '*');
        }

    }

    $("#ckLastTime").on("change", function () {
        var hoSettingKey = "/ErpWeb/Settlement/UseLastTime";
        var isCheck = this.checked;
        commonModule.SaveCommonSetting(hoSettingKey, isCheck, function () {
            UseLastTime = isCheck;
        });
    });



    return module;
}(PriceSettingAgentModule || {}, commonModule, jQuery, layer));