var generalizeIndexModule = (function (module, common, $, layer) {
    var newUserGuideKey = "/ErpWeb/NewUserGuide";//新手第一次进入系统，新手指引
    var reqModel = {
        Id: 0,
        Key: "",
        Status: 0,
        PageIndex: 1,
        PageSize: 50,
        SupplierFxUserId: 0,
        SenderName: null,
        SenderMobile: null,
        Province: null,
        City: null,
        District: null,
        Address: null,
        RemarkName: null,
        SenderTelePhone: null
    };

    module.Initialize = function () {
        var pt = $.cookie('fx_login_source');
        var platformType = pt ? pt : common.PlatformType;
        //1.轮播图
        module.initSlideshow(platformType);
        //2.系统公告
        module.initPrintnotice(platformType);
        //4.更新店铺过期时间
        module.getExpiredShop();
        //3.统计
        module.loadingTotal();
        //新手手指导
        module.isShowNewUserGuideSet();

        //微信绑定失败
        if (binded != undefined && binded != "") {
            $('#binded_mobile').html(binded);
            module.againLoginQrCodeDailog();
        }

        //隐藏失效店铺
        layui.form.on('switch(switchTop01)', function (data) {
            var key = data.othis.prev().attr("name");
            var idStr = data.othis.prev().attr("data-ids");
            var isCheck = this.checked;
            commonModule.Ajax({
                url: '/common/seveFilterInvalidShopId',
                data: { 'idStr': idStr, 'isCheck': isCheck },
                loading: false,
                success: function (rsp) {
                    if (rsp.Success) {
                        $('.shopPastWrap').hide();
                        $('#AccessExpireShopCount-div').hide();
                    }
                    layer.msg(rsp.Data || rsp.Message);
                }
            });
        });
    }

    //调官网接口
    var baseUrl = "https://www.dgjapp.com/";
    //var baseUrl = "http://127.0.0.1:30002/";
    //1.轮播图
    module.initSlideshow = function (platformName) {

        var data = {};
        data.platformName = platformName ? platformName : "";
        data.articleColumntype = "distributionslideshow";
        data.releaseADVersion = common.IsReleaseVersion() ? 1 : 2;

        var urlHost = location.hostname;
        var indexLocation = urlHost.indexOf(".dgjapp.com");
        data.releaseADVersionNum = urlHost.substr(0, indexLocation);
        data.shopId = commonModule.CurrShop.Id ? commonModule.CurrShop.Id : "";
        data.shopName = commonModule.CurrShop.ShopId ? commonModule.CurrShop.ShopId : "";


        $.ajax(baseUrl + 'distribution/distributionslideshow', {
            data: data,
            dataType: 'json', //服务器返回json格式数据
            type: 'get', //HTTP请求类型
            success: function (result) {
                var html = "";
                var html02 = "";
                if (result.success) {
                    var data = result.data;
                    if (data.length > 0) {
                        for (var i = 0; i < data.length; i++) {
                            html += '<div onclick=\'generalizeIndexModule.listenClick("' + data[i].articleId + '")\'  class="slider-main-img"><a href="' + data[i].articletitle02 +
                                '" target="_blank"><img src="' + baseUrl + data[i].articleUrl + '" alt=""></a></div>';

                            html02 += '<div onclick=\'generalizeIndexModule.listenClick("' + data[i].articleId + '")\'  class="slider-new-img"><a href="' + data[i].articletitle02 +
                                '" target="_blank"><img src="' + baseUrl + data[i].articleUrl + '" alt=""></a></div>';
                        }

                    } else {
                        html +=
                            '<div class="slider-main-img"><a href="#" ><img src="./../images/fullPic-01.png" alt=""></a></div>';
                    }

                } else {
                    html += '<div class="slider-main-img"><a href="#" ><img src="./../images/fullPic-01.png" alt=""></a></div>';
                }
                $("#js_main_block").html(html);
                $("#carousel-picWrap").html(html02);
                layui.use('carousel', function () {
                    var carousel = layui.carousel;
                    carousel.render({
                        elem: '#carouselIndex'
                        , width: '100%' //设置容器宽度
                        , height: '322px'
                        , arrow: 'hover' //始终显示箭头
                        , interval: 5000
                    });
                });
                //    slideshowAnimate();
            },
            error: function () {
                var html = "";
                html += '<div class="slider-main-img"><a href="#" ><img src="./../images/fullPic-01.png" alt=""></a></div>';
                $("#js_main_block").html(html);
                //    slideshowAnimate();
            }

        });
    }
    //2.系统公告
    module.initPrintnotice = function (platformName) {
        var data = {};
        data.platformName = platformName ? platformName : "";
        data.articleColumntype = "distributionnotice";
        data.releaseADVersion = common.IsReleaseVersion() ? 1 : 2;

        $.ajax(baseUrl + 'distribution/notice', {
            data: data,
            dataType: 'json',
            type: 'get',
            success: function (result) {
                if (result.success) {
                    var html = "";
                    var data = result.data;
                    if (data) {
                        var articlecontent = data.articlecontent;
                        if (commonModule.IsNewCorp)
                            articlecontent = data.articlecontent.replace('店管家', '');
                        $("#notice_content").html(articlecontent);
                        $("#newIndexWrap_notice_content").html(articlecontent);
                    } else {
                        $("#notice_content").html("暂无公告消息")
                    }
                }
            }
        });
    }

    //3.统计
    module.loadingTotal = function () {
        var starDate = commonModule.StringToDate(_fxUserLastRefreshTime);
        var diffHour = dateDiffHours(starDate);
        var startTime = $(".QueryDateVal").attr("start-date");
        var endTime = $(".QueryDateVal").attr("end-date");
        var dataCache = localStorage.getItem("TotalData_" + _fxUserId + "_" + startTime + "_" + endTime);
        if (!_fxUserLastRefreshTime || diffHour > 4 || !dataCache) {
            module.syncOrder();
        } else {
            module.RenderTotal($.parseJSON(dataCache));
        }
    }

    //轮播图动画
    function slideshowAnimate() {
        function $$(id) {
            return document.getElementById(id)
        }
        var js_slider = $$("js_slider");
        var js_main_block = $$('js_main_block');
        var imgs = js_main_block.children;
        var slider_ctrl = $$('slider_ctrl');

        for (var i = 0; i < imgs.length; i++) {
            var span = document.createElement("span");
            span.innerHTML = (imgs.length - i); //作用数字从小到大
            span.className = "slider-ctrl-con";
            slider_ctrl.insertBefore(span, slider_ctrl.children[1]);
        }

        var spans = slider_ctrl.children; //得到所有的span

        spans[1].setAttribute("class", "slider-ctrl-con current");

        var scrollWidth = js_slider.clientWidth; //得到大盒子的宽度  也就是 后面动画要走的距离  310

        // 开始走
        // 刚开始，按第一张图留下  其它的人
        for (var i = 1; i < imgs.length; i++) {
            imgs[i].style.left = scrollWidth + "px"

        }
        //spans是8个按钮
        var iNow = 0; //用来控制播放张数的  当前索引号
        for (var k in spans) { //k是索引号   spans[k]获取元素
            spans[k].onclick = function () {
                if (this.className == "slider-ctrl-prev") { //点击左箭头

                    animate(imgs[iNow], {
                        left: scrollWidth
                    }) //当前那个图片 走到scrollWidth位置

                    iNow--; //当前的下一张

                    if (iNow < 0) {

                        iNow = imgs.length - 1;

                    }

                    imgs[iNow].style.left = -scrollWidth + "px"; //先走到：当前的下一张先快速走到右侧 和下面同一张

                    animate(imgs[iNow], {
                        left: 0
                    }) //再走到：   当前那个图片 走到scrollWidth位置

                    setSquare()

                } else if (this.className == "slider-ctrl-next") { //点击右箭头


                    //iNow=0
                    animate(imgs[iNow], {
                        left: -scrollWidth
                    }) //当前那个图片 走到scrollWidth位置

                    iNow++; //当前的下一张

                    if (iNow > imgs.length - 1) {
                        iNow = 0;

                    }

                    imgs[iNow].style.left = scrollWidth + "px"; //先走到：当前的下一张先快速走到右侧 和下面同一张

                    animate(imgs[iNow], {
                        left: 0
                    }) //再走到：   当前那个图片 走到scrollWidth位置

                    setSquare()

                } else { //点击下面几个小圆点

                    var that = this.innerHTML - 1;

                    if (that > iNow) {
                        animate(imgs[iNow], {
                            left: -scrollWidth
                        });

                        imgs[that].style.left = scrollWidth + "px";

                        animate(imgs[that], {
                            left: 0
                        });

                    } else if (that < iNow) {

                        animate(imgs[iNow], {
                            left: scrollWidth
                        });

                        imgs[that].style.left = -scrollWidth + "px";

                        animate(imgs[that], {
                            left: 0
                        });

                    }

                    iNow = that; //给当前的索引号
                    animate(imgs[iNow], {
                        left: 0
                    });
                    setSquare()
                }
            }
        }

        function setSquare() { //小横线着色

            //清除所有的span current 的样式  留下满足需要的

            for (var i = 1; i < spans.length - 1; i++) { //为什么要取i=1和i<spans.length-1开始，因为span比图片多出两个，有两个span是控制左右点击的

                spans[i].className = "slider-ctrl-con"

            }
            spans[iNow + 1].className = "slider-ctrl-con current"

        }


        //自动播放  定时器开始

        var timer = null;
        timer = setInterval(autoplay, 5000) //和右侧按钮功能一样


        function autoplay() {
            //iNow=0
            animate(imgs[iNow], {
                left: -scrollWidth
            }) //当前那个图片 走到scrollWidth位置

            iNow++; //当前的下一张

            if (iNow > imgs.length - 1) {
                iNow = 0;

            }

            imgs[iNow].style.left = scrollWidth + "px"; //先走到：当前的下一张先快速走到右侧 和下面同一张

            animate(imgs[iNow], {
                left: 0
            }) //再走到：   当前那个图片 走到scrollWidth位置

            setSquare()

        }

        //清除自动播放定时器

        js_slider.onmouseover = function () {
            clearInterval(timer)
        }

        js_slider.onmouseout = function () {
            clearInterval(timer)
            timer = setInterval(autoplay, 4000)
        }

    }

    module.userFeedback = function () {
        $("#userFeedback_textarea").val("");
        $("#userFeedback_contact").val("");
        var userFeedbackDailog = layer.open({
            type: 1,
            title: "意见反馈", //不显示标题
            content: $('.userFeedback-dialog'),
            area: ['450'], //宽高

            btn: ['取消'],
            //yes: function () {

            //    var textareaVal = $("#userFeedback_textarea").val().trim();
            //    var inputVal = $("#userFeedback_contact").val().trim();
            //    if (textareaVal.length < 5 || textareaVal.length > 200) {
            //        layer.msg("请输入大于5个字的内容反馈内容！");
            //        return;
            //    }
            //    if (!inputVal) {
            //        layer.msg("请输入联系方式！");
            //        return;
            //    }

            //    var data = {};
            //    data.articletitle = textareaVal;
            //    data.articletitle02 = inputVal;
            //    $.ajax(baseUrl + 'distribution/distributionuserFeedback', {
            //        data: data,
            //        dataType: 'json',
            //        type: 'post',
            //        success: function (result) {
            //            if (result.success) {
            //                layer.close(userFeedbackDailog);
            //                layer.msg(result.message);

            //            }
            //        }
            //    });

            //},
            cancel: function () {

            }
        });
    }

    //3.2渲染统计
    module.RenderTotal = function (data) {
        //判断绑定的店铺是否都是Pdd
        //try {
        //    var isAllPdd = true;
        //    common.Foreach(data.ShopTotal, function (i, o) {
        //        if (o.pt != "Pinduoduo")
        //            isAllPdd = false;
        //    });
        //    if (isAllPdd) {
        //        $.cookie('fx_login_source', 'Pinduoduo', { expires: 1, path: '/', domain: '.dgjapp.com'});
        //    }
        //} catch (e) {

        //}
        if (data.WaitCheckCount)
            $("#WaitCheckCount").text(data.WaitCheckCount);
        if (data.WaitSendRefundCount)
            $("#WaitSendRefundCount").text(data.WaitSendRefundCount);
        if (data.WaitSendCount)
            $("#WaitSendCount").text(data.WaitSendCount);
        if (data.SendedCount)
            $("#SendedCount").text(data.SendedCount);
        if (data.RemindShipmentCount)
            $("#RemindShipmentCount").text(data.RemindShipmentCount);
        if (data.WaitPrintAndSendCount)
            $("#WaitPrintAndSendCount").text(data.WaitPrintAndSendCount);
        if (data.PrintedWaitSendCount)
            $("#PrintedWaitSendCount").text(data.PrintedWaitSendCount);
        if (data.PlatAfterSaleCount)
            $("#PlatAfterSaleCount").text(data.PlatAfterSaleCount);
        if (data.ManualAfterSaleCount)
            $("#ManualAfterSaleCount").text(data.ManualAfterSaleCount);
        if (data.PlatAfterSaleCount)
            $("#PlatAfterSaleCount").text(data.PlatAfterSaleCount);
        if (data.ManualAfterSaleCount && data.PlatAfterSaleCount) {
            var manualAfterSaleCount = parseInt(data.ManualAfterSaleCount);
            var platAfterSaleCount = parseInt(data.PlatAfterSaleCount);
            $("#AfterSaleAllCount").text(manualAfterSaleCount + platAfterSaleCount);
        }
        if (data.PlatAfterSaleOnlyRefundCount)
            $("#PlatAfterSaleOnlyRefundCount").text(data.PlatAfterSaleOnlyRefundCount);
        if (data.WaitConfirmAgentCount == -1)
            $("#WaitConfirmAgentCount").text("/");
        else
            $("#WaitConfirmAgentCount").text(data.WaitConfirmAgentCount);
        if (data.WaitConfirmSupplierCount == -1)
            $("#WaitConfirmSupplierCount").text("/");
        else
            $("#WaitConfirmSupplierCount").text(data.WaitConfirmSupplierCount);


        if (data.BillManagementWaitConfirmAgentCount == -1)
            $("#BillManagementWaitConfirmAgentCount").text("/");
        else
            $("#BillManagementWaitConfirmAgentCount").text(data.BillManagementWaitConfirmAgentCount);
        if (data.BillManagementWaitConfirmSupplierCount == -1)
            $("#BillManagementWaitConfirmSupplierCount").text("/");
        else
            $("#BillManagementWaitConfirmSupplierCount").text(data.BillManagementWaitConfirmSupplierCount);

        //if (data.AccessExpireShopCount) {
        //    var accessExpireShopCount = parseInt(data.AccessExpireShopCount);
        //    if (accessExpireShopCount > 0) {
        //        $("#AccessExpireShopCount-div").show();
        //    }
        //    else {
        //        $("#AccessExpireShopCount-div").hide();
        //    }
        //    $("#AccessExpireShopCount").text(data.AccessExpireShopCount);
        //}

        //var tplt1 = $.templates("#myshops_wrap");
        //var html1 = tplt1.render({ ShopTotal: data.ShopTotal });
        //$("#myshops_div").html(html1);

        //var tplt2 = $.templates("#mysuppliers_wrap");
        //var html2 = tplt2.render({ SupplierTotal: data.SupplierTotal });
        //$("#mysuppliers_div").html(html2);

        //var tplt3 = $.templates("#myagents_wrap");
        //var html3 = tplt3.render({ AgentTotal: data.AgentTotal });
        //$("#myagents_div").html(html3);

        $("#sp_sync").addClass("dColor"); //启用同步按钮
        $('#lblLastSyncTime').text(data.FxUserLastRefreshTime); //更新最后刷新时间
        $("#lblSyncPercent").text('');
    }

    //3.1获取统计数据
    var auotRefreshTimer = null;
    module.GetAllTotal = function (refresh) {
        var startTime = new Date($(".QueryDateVal").attr("start-date"));
        var endTime = new Date($(".QueryDateVal").attr("end-date"));
        var diffHours = dateDiffHours(startTime, endTime);
        if (diffHours > 24 * 30) {
            layer.msg("时间间隔不能超过30天", { icon: 2, time: 2000 });
            return;
        }

        commonModule.Ajax({
            url: '/GeneralizeIndex/GetAllTotalV2',
            data: { startTime: startTime, endTime: endTime, refresh: refresh },
            success: function (rsp) {
                if (rsp.Success == false) {
                    console.error("GetAllTotalV2", rsp || {});
                    return;
                }
                var data = rsp.Data;
                if (!!data) {
                    if (data.WaitConfirmAgentCount == -1)
                        data.WaitConfirmAgentCount = '/';
                    if (data.WaitConfirmSupplierCount == -1)
                        data.WaitConfirmSupplierCount = '/';
                    if (data.BillManagementWaitConfirmAgentCount == -1)
                        data.BillManagementWaitConfirmAgentCount = '/';
                    if (data.BillManagementWaitConfirmSupplierCount == -1)
                        data.BillManagementWaitConfirmSupplierCount = '/';
                    localStorage.setItem("TotalData_" + _fxUserId + "_" + startTime + "_" + endTime, JSON.stringify(data));
                    module.RenderTotal(data);

                    if (auotRefreshTimer) {
                        clearInterval(auotRefreshTimer);
                        auotRefreshTimer = null;
                    }
                    auotRefreshTimer = setInterval(function () { module.syncOrder(); }, 14400000);//4小时后自动刷新
                }
            }
        });
    }

    //同步订单
    var SyncMinPercent = 0;//最小同步进度
    var IsFinishedCount = 0;//已同步完平台数
    //3.0.1
    module.syncOrder = function (refresh) {
        if (!$("#sp_sync").hasClass("dColor")) {
            layer.msg('数据请求中，请稍后');
            return;
        }
        $("#sp_sync").removeClass("dColor");
        SyncPercent = 0;
        IsFinishedCount = 0;
        module.TriggerOrderSync("alibaba");
        module.TriggerOrderSync("pinduoduo");
        module.TriggerOrderSync("jingdong");
        module.TriggerOrderSync("toutiao");
        module.GetAllTotal(refresh);
        console.dir("syncorder");
    }

    //3.0.2 全平台同步
    module.TriggerOrderSync = function (syncCloudPt) {
        commonModule.Ajax({
            type: "POST",
            url: "/NewOrder/TriggerSync",
            data: { cloudPlatformType: syncCloudPt },
            success: function (rsp) {
                //if (commonModule.IsError(rsp)) return;
                //getSyncStatus(syncCloudPt);
            }
        });
    }

    //3.0.3获取同步状态
    var getSyncStatus = function (syncCloudPt) {
        $("#sp_sync").removeClass("dColor");
        commonModule.Ajax({
            type: "POST",
            url: "/NewOrder/GetSyncStatus",
            data: { cloudPlatformType: syncCloudPt },
            success: function (rsp) {
                if (commonModule.IsError(rsp)) return;
                if (rsp == undefined || rsp == null || rsp.Data == undefined) return;
                var data = rsp.Data;
                if (data.IsFinished) {
                    IsFinishedCount += 1;
                    clearSyncStatusTimer(syncCloudPt);
                    //三个平台都同步完成
                    if (IsFinishedCount == 3) {
                        module.GetAllTotal();//同步完重新统计
                    }
                }
                else {
                    //只展示三个平台中最小的进度
                    var percent = parseInt(rsp.Data.Percent) || 0;
                    if (SyncMinPercent == 0 || percent < SyncMinPercent) {
                        SyncMinPercent = percent;
                        $("#lblSyncPercent").text(percent + '%');
                    }
                    setSyncStatusTimer(syncCloudPt);
                }
            }
        });
    }

    var getSyncStatusTimer_alibaba = null;
    var getSyncStatusTimer_pinduoduo = null;
    var getSyncStatusTimer_jingdong = null;
    var getSyncStatusTimer_toutiao = null;
    //清除get订单同步状态定时器
    function clearSyncStatusTimer(syncCloudPt) {
        switch (syncCloudPt) {
            case "alibaba":
                if (getSyncStatusTimer_alibaba) {
                    clearInterval(getSyncStatusTimer_alibaba);
                    getSyncStatusTimer_alibaba = null;
                }
                break;
            case "pinduoduo":
                if (getSyncStatusTimer_pinduoduo) {
                    clearInterval(getSyncStatusTimer_pinduoduo);
                    getSyncStatusTimer_pinduoduo = null;
                }
                break;
            case "jingdong":
                if (getSyncStatusTimer_jingdong) {
                    clearInterval(getSyncStatusTimer_jingdong);
                    getSyncStatusTimer_jingdong = null;
                }
                break;
            case "toutiao":
                if (getSyncStatusTimer_toutiao) {
                    clearInterval(getSyncStatusTimer_toutiao);
                    getSyncStatusTimer_toutiao = null;
                }
                break;
        }
    }
    //设置get订单同步状态定时器
    function setSyncStatusTimer(syncCloudPt) {
        switch (syncCloudPt) {
            case "alibaba":
                if (!getSyncStatusTimer_alibaba) {
                    getSyncStatusTimer_alibaba = setInterval(function () { getSyncStatus(syncCloudPt); }, 3000);
                }
                break;
            case "pinduoduo":
                if (!getSyncStatusTimer_pinduoduo) {
                    getSyncStatusTimer_pinduoduo = setInterval(function () { getSyncStatus(syncCloudPt); }, 3000);
                }
                break;
            case "jingdong":
                if (!getSyncStatusTimer_jingdong) {
                    getSyncStatusTimer_jingdong = setInterval(function () { getSyncStatus(syncCloudPt); }, 3000);
                }
                break;
            case "toutiao":
                if (!getSyncStatusTimer_toutiao) {
                    getSyncStatusTimer_toutiao = setInterval(function () { getSyncStatus(syncCloudPt); }, 3000);
                }
                break;
        }
    }

    //计算时间差
    function dateDiffHours(dateBegin, dateEnd) {
        if (!dateBegin)
            return 0;
        if (!dateEnd)
            dateEnd = new Date();
        var dateDiff = dateEnd.getTime() - dateBegin.getTime();//时间差的毫秒数
        var hours = Math.floor(dateDiff / (3600 * 1000)); //计算出小时数  
        return hours;
    }

    //更新店铺过期时间
    module.getExpiredShop = function () {
        $('#AccessExpireShopCount-div').hide();
        $('.shopPastWrapShow').hide();
        $('.shopPastWrap-num').text('');
        $('.shopPastWrap-main-last').nextAll().remove();
        $('input[lay-filter="switchTop01"]').attr('data-ids', '');
        var shoptype = $("#ulShopType .active").attr("data-shopType");
        commonModule.LoadExpiredShop({
            fromIndex: 1,
            queryType: 'top_invalid_shop',
            shoptype: shoptype
        }, function (rsp) {
            if (rsp.Success) {
                var shops = JSON.parse(rsp.Data.expiredShops) || [];

                //console.log("shopsshopsshopsshops", shops)

                if (shops.length == 0) {
                    $('.shopPastWrapShow').hide();
                    $('#AccessExpireShopCount-div').hide();
                    return;
                }
                else {
                    $('#AccessExpireShopCount-div').show();
                }

                if (rsp.Data.aboutCount && rsp.Data.aboutCount > 0 && menuId == "Partner") {
                    $("#layui_warnin").css({ display: "flex" });
                    $("#warnin_mun").text(rsp.Data.aboutCount);
                }

                var invalidIds = [];
                var html = "";
                for (var i = 0; i < shops.length; i++) {
                    html += '<li class="shopPastWrap-main-item">';
                    html += '<div class="shopPastWrap-main-item-left">';
                    html += '<i class="pintaiIcon ' + shops[i].PlatformType + '"></i>';
                    html += '<span class="shopName">' + shops[i].NickName + '</span>';
                    html += '</div>';
                    html += '<div class="shopPastWrap-main-item-right">';
                    if (shops[i].IsExpire && shops[i].PlatformPayUrl)
                        html += '<span class="status wu-color-b">服务到期，请续费</span>';
                    else {
                        if (shops[i].Status == 1)
                            html += '<span class="status wu-color-b">授权成功</span>';
                        if (shops[i].Status == 2)
                            html += '<span class="status wu-color-b">解除关联</span>';
                        if (shops[i].Status == 3)
                            html += '<span class="status wu-color-b">授权过期</span>';
                    }

                    if (shops[i].PlatformType == "TouTiao") {
                        if (shops[i].PlatformPayUrl2 != '') {
                            html += '<span class="layui-btn layui-btn-normal layui-btn-sm wu-btn wu-btn-small" onclick="commonModule.latformRenewUrlTouTiao(\'' + shops[i].PlatformType + '\',\'' + shops[i].NickName + '\',\'' + shops[i].PlatformPayUrl2 + '\',\'' + shops[i].TouTiaoOldOrNew + '\')">前往续费</span>';
                        }
                    }
                    else if (shops[i].IsExpire && shops[i].PlatformPayUrl) {
                        html += '<span class="layui-btn layui-btn-normal layui-btn-sm wu-btn wu-btn-small" onclick="commonModule.latformRenewUrl(\'' + shops[i].PlatformType + '\',\'' + shops[i].NickName + '\',\'' + shops[i].PlatformPayUrl + '\')">前往续费</span>';
                    }
                    else if ((shops[i].PlatformType == "Pinduoduo" || shops[i].PlatformType == "KuaiTuanTuan") && shops[i].PddIsUsePrintSystemApp && shops[i].PlatformPayUrl != '') {
                        html += '<span class="layui-btn layui-btn-normal layui-btn-sm wu-btn wu-btn-small" onclick="commonModule.latformRenewUrl(\'togglePinduoduo\',\'' + shops[i].NickName + '\',\'' + shops[i].PlatformPayUrl + '\')">前往续费</span>';
                    }
                    else {
                        if (shops[i].IsAuthUrl) {
                            html += '<span class="layui-btn layui-btn-normal layui-btn-sm wu-btn wu-btn-small" onclick="OnMyShopTap(\'' + shops[i].AuthUrl + '\')">重新授权</span>';
                        } else {
                            html += '<span class="layui-btn layui-btn-normal layui-btn-sm wu-btn wu-btn-small" onclick="layer.msg(\'该平台不支持重新授权，请从店铺后台进入刷新授权/解除店铺再操作绑定\');">重新授权</span>';
                        }
                    }
                    html += '</div>';
                    html += '</li>';

                    invalidIds.push(shops[i].ShopId);
                }
                $('#shopPastWrap-num').text(shops.length);
                $('.shopPastWrap-main-last').after(html);
                $('input[lay-filter="switchTop01"]').attr('data-ids', invalidIds.join());
                $('.shopPastWrapShow').show();
            }
            else {
                console.error('失效店铺', rsp);
            }
        });
    }

    //移到Index.cshtml
    //$(function () {
    //    module.Initialize();
    //});


    //监听点击数  轮播图
    var clickNum = 0;
    module.listenClick = function (id) {
        if (clickNum == 0) {
            var data = {};
            data.id = id;
            $.ajax(baseUrl + 'news/newsSddclick', {
                data: data,
                type: 'post',
                success: function (result) {

                }
            });

        }
        clickNum++;
        var $setTimeout = setTimeout(function () {
            clickNum = 0
        }, 20000)
    }
    module.invitesupplier = function (id) {
        if (event) {
            event.stopPropagation();
        } else {
            window.event.returnValue = false;
        };
        $(id).show(200);
    }

    module.closeInvitesupplier = function (id) {
        $(id).hide(200);
    }

    $(document).on("click", function () {
        $("#invitesupplier_wrap").hide();
        $("#invitesupplier_wrap02").hide();

    })

    module.showMoreNewContact = function () {
        console.log("aabb")
        $("#newRightContact_content").toggleClass("active");
    }
    var moveBox = document.getElementById("newRightContact");
    var moveEle = document.getElementById("newRightContact_top");
    startDrop(moveEle, moveBox);
    function startDrop(el, move) {
        el.onmousedown = function (event) {
            var event = event || window.event;
            var y = event.clientY - move.offsetTop;  //记录当前盒子的y位置
            var xx = event.clientX;
            var right = move.style.right ? parseInt(move.style.right) : 0;
            document.onmousemove = function (event) {
                moveEle.onclick = null;
                window.getSelection().removeAllRanges();//禁止文字被选中的问题 
                var event = event || window.event;
                var moveRight = right + (xx - event.clientX);
                var moveTop = event.clientY - y;
                if (moveRight < 0) {
                    moveRight = 0;
                }
                if (moveRight > $(window).width() - 220) {
                    moveRight = $(window).width() - 220
                }
                if (moveTop < 0) {
                    moveTop = 0;
                }
                if (moveTop > $(window).height() - 50) {
                    moveTop = $(window).height() - 50;
                }


                move.style.right = moveRight + "px";
                move.style.top = moveTop + "px";

               }
            }
            document.onmouseup = function () {  //鼠标谈起，不应该操作
                document.onmousemove = null;
                setTimeout(function () {
                    moveEle.onclick = module.showMoreNewContact;
                }, 100)
            }
        }
    module.isShowNewUserGuideSet = function () {  //开启新手手续费

        ///展示新手攻略弹框
        //commonModule.LoadCommonSetting(newUserGuideKey, false, function (rsp) {

        //    if (rsp.Success) {

        //        if (rsp.Data != "1") {

        //            module.showNewGreenhandWrap();

        //        }
        //        else {
        //            commonModule.NewUserGuideSetting = 1
        //        }

        //    }
        //});
    }


    module.showOrHide = function (isTrue, sqTrue) {
        if (isTrue) {
            if (sqTrue) {
                $("#aialog_newGreenhandWrap").removeClass("show").addClass("hide hide02");
                $("#newGreenhandWrap_setBtn").removeClass("show").addClass("hide hide02");
                $("#aialog_newGreenhandWrap .newGreenhandWrap-main-title").hide();
                $("#aialog_newGreenhandWrap .newGreenhandWrap-steps-itemContent .layui-btn hide02").hide();
                localStorage.setItem("shrinkNewGreen", true);

            } else {
                $("#aialog_newGreenhandWrap").removeClass("show").addClass("hide");
                $("#newGreenhandWrap_setBtn").removeClass("show").addClass("hide");
                $("#aialog_newGreenhandWrap .newGreenhandWrap-main-title").hide();
                $("#aialog_newGreenhandWrap .newGreenhandWrap-steps-itemContent .layui-btn").hide();
                localStorage.setItem("shrinkNewGreen", true);
            }


        } else {
            $("#aialog_newGreenhandWrap").removeClass("hide hide02").addClass("show");
            $("#newGreenhandWrap_setBtn").removeClass("hide hide02").addClass("show");
            $("#aialog_newGreenhandWrap .newGreenhandWrap-main-title").show();
            $("#aialog_newGreenhandWrap .newGreenhandWrap-steps-itemContent .layui-btn").css({ display: "inline-block" });
            localStorage.removeItem("shrinkNewGreen");
        }
    }


    //开启新手指引
    module.showNewGreenhandWrap = function () {
        $("#aialog_newGreenhandWrap").css({
            display: "block"
        });
        $("#newGreenhandWrap_setBtn").css({
            display: "flex"
        });

        var hasDonghua = localStorage.getItem("shrinkNewGreen") || null;
        if (hasDonghua) {
            module.showOrHide(true, true)
        }


    }
    //关闭新手指引
    module.hideNewGreenhandWrap = function () {

        $("#aialog_newGreenhandWrap").fadeOut(200)
        $("#newGreenhandWrap_setBtn").css({
            display: "none"
        });
        commonModule.SaveCommonSetting(newUserGuideKey, "1", function (rsp) {
            if (rsp.Success) {
                commonModule.NewUserGuideSetting = 1;
                //console.log("首次展示配置成功");
            }
        });
    }

    common.navActive("#newGreenhandWrap_main_Nav", function (index, item) {
        if (index == 0) {
            $("#agentSteps").css({ display: "flex" })
            $("#supplierSteps").css({ display: "none" })
        } else {
            $("#supplierSteps").css({ display: "flex" })
            $("#agentSteps").css({ display: "none" })
        }
    })

    module.inviteQrCode = function (isTrue) {
        var checkResult = commonModule.CheckVirtualRegMobile();
        if (!checkResult) {
            $(".aialog-newGreenhandWrap").css("z-index", 1);
            return;
        }
        var inviteDailog = layer.open({
            type: 1,
            title: isTrue ? "邀请厂家" : "邀请商家", //不显示标题
            content: $('.inviteDailogWrap'),
            area: '500px', //宽高
            btn: ['关闭'],
            success: function () {
                if (isTrue) {
                    $("#inviterQrCode_mySupplier").click();

                    $("#inviterQrCode_myAgentWrap").hide();
                    $("#inviterQrCode_mySupplierWrap").hide();
                } else {
                    $("#inviterQrCode_myAgent").click();

                    $("#inviterQrCode_myAgentWrap").hide();
                    $("#inviterQrCode_mySupplierWrap").hide();
                }

            },
            yes: function () {
                layer.close(inviteDailog);
            },

        });

    }

    //跳转
    module.jumpUrl = function (btnId) {
        var targetUrl = "";
        //是否追加开始和结束时间段
        var addTime = true;
        switch (btnId) {
            case "WaitConfirmAgentCount":
                addTime = false;
                targetUrl = "/Partner/MyAgent?status=2";
                break;
            case "WaitConfirmSupplierCount":
                addTime = false;
                targetUrl = "/Partner/MySupplier?status=2";
                break;
            case "BillManagementWaitConfirmSupplierCount":
                addTime = false;
                targetUrl = "/FinancialSettlement/BillManagement?status=888&alls=1";
                break;
            case "BillManagementWaitConfirmAgentCount":
                addTime = false;
                targetUrl = "/FinancialSettlement/BillManagement?status=888&alla=1";
                break;
            case "WaitCheckCount":
                targetUrl = "/Common/Page/NewOrder-AllOrder?isTar=waitaudit";
                break;
            case "WaitSendRefundCount":
                targetUrl = "/Common/Page/NewOrder-AllOrder?isTar=abnormal_order";
                break;
            case "WaitSendCount":
                targetUrl = "/Common/Page/NewOrder-AllOrder?isTar=waitsellersend";
                break;
            case "SendedCount":
                targetUrl = "/Common/Page/NewOrder-AllOrder?isTar=waitbuyerreceive";
                break;
            case "RemindShipmentCount":
                targetUrl = "/Common/Page/NewOrder-WaitOrder?isTar=firstdelivery";
                break;
            case "AbnormalCount":
                targetUrl = "/Common/Page/NewOrder-WaitOrder?isTar=receiverchangeorder";
                break;
            case "WaitPrintAndSendCount":
                targetUrl = "/Common/Page/NewOrder-WaitOrder?eostatus=waitsellersend&pstatus=0";
                break;
            case "PrintedWaitSendCount":
                targetUrl = "/Common/Page/NewOrder-WaitOrder?eostatus=waitsellersend&pstatus=1";
                break;
            case "PlatAfterSaleCount":
                targetUrl = "/Common/Page/AfterSale-Index?sourceflag=0&sendstate=all";
                break;
            case "ManualAfterSaleCount":
                targetUrl = "/Common/Page/AfterSale-Index?sourceflag=1";
                break;
            case "PlatAfterSaleOnlyRefundCount":
                targetUrl = "/Common/Page/AfterSale-Index?sourceflag=0&sendstate=all&astype=1";
                break;
            case "AfterSaleAllCount":
                targetUrl = "/Common/Page/AfterSale-Index";
                break;
            case "SendOrder-Index":
                targetUrl = "/Common/Page/SendOrder-Index";
                break;

        }

        if (targetUrl != "") {
            targetUrl = commonModule.rewriteUrl(targetUrl);//token
            targetUrl = commonModule.dbnameToAjaxUrl(targetUrl);//dbname
            if (addTime) {
                var startTime = $(".QueryDateVal").attr("start-date");
                var endTime = $(".QueryDateVal").attr("end-date");
                targetUrl = targetUrl + "&st=" + startTime + "&et=" + endTime;
            }
            //top.location = targetUrl;
            window.open(targetUrl, "_blank");
        }
    }

    module.productHelpDailo = function () {
        layer.open({
            type: 1,
            title: false, //不显示标题
            content: $(".productHelpDailog"),
            area: '1050', //宽高
            skin: 'wu-dailog',
            btn: false,
            success: function () {
            },
            yes: function () {


            }

        });
    }
    // 绑定厂家
    module.bindCooperationWarn = function () {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.AddBindSupplier);
        });

        var thisFunc = function () {
            var checkResult = commonModule.CheckVirtualRegMobile();
            if (!checkResult) {
                return;
            }
            layer.open({
                type: 1,
                title: false, //不显示标题
                content: $("#bindCooperationFactWarnWrap"),
                area: '380px', //宽高
                // skin: 'adialog-Shops-skin',
                skin: 'wu-dailog',
                btn: false,
            });
        }
        
    }

    // 绑定商家
    module.bindCooperationAgentWarn = function () {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.AddBindAgent);
        });

        var thisFunc = function () {
            var checkResult = commonModule.CheckVirtualRegMobile();
            if (!checkResult) {
                return;
            }
            layer.open({
                type: 1,
                title: false, //不显示标题
                content: $("#bindCooperationAgentWarnWrap"),
                area: '380px', //宽高
                // skin: 'adialog-Shops-skin',
                skin: 'wu-dailog',
                btn: false,
            });
        }
        
    }


    module.accountHelp = function () {
        layer.open({
            type: 1,
            title: false, //不显示标题
            content: $("#helpVedioWrap"),
            area: ['482px', '235px'], //宽高
            // skin: 'adialog-Shops-skin',
            skin: 'wu-dailog',
            btn: false,
        });
    }
    module.setAccountDailog = function () {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    layer.open({
                        type: 1,
                        title: false, //不显示标题
                        content: $("#setAccountDailog"),
                        area: ['482px', '235px'], //宽高
                        // skin: 'adialog-Shops-skin',
                        skin: 'wu-dailog',
                        btn: false,
                    });
                }
                else return;
            }, p.PriceSetting);
        });
        
    }

    var addWarnAddWarnDialog = null;
    module.hasRegisteredFactWarn = function () {

        addWarnAddWarnDialog = layer.open({
            type: 1,
            title: false, //不显示标题
            content: '<div style="width:600px;height:400px;position: relative;"><span onclick="generalizeIndexModule.Add()" style="display: inline-block;position: absolute;bottom: 67px;left: 239px;width: 177px;height: 46px;cursor: pointer;"></span><img src="/Content/images/noviceIntroPic/anquanWarn-2022-10-26-01.png" /></div>',
            area: ['550'], //宽高
            // skin: 'wu-dailog', 
            btn: false,
            closeBtn: false
        });

    }

    var addWarnAddWarnDialogAgent = null;
    module.hasRegisteredAgentWarn = function () {

        addWarnAddWarnDialogAgent = layer.open({
            type: 1,
            title: false, //不显示标题
            content: '<div style="width:600px;height:400px;position: relative;"><span onclick="generalizeIndexModule.AddAgent()" style="display: inline-block;position: absolute;bottom: 67px;left: 239px;width: 177px;height: 46px;cursor: pointer;"></span><img src="/Content/images/noviceIntroPic/anquanWarn-2022-10-26-02.png" /></div>',
            area: ['550'], //宽高
            // skin: 'wu-dailog',
            btn: false,
            closeBtn: false
        });

    }

    module.hasNoRegisteredWarn = function () {
        module.getQRC('fact');
        layer.open({
            type: 1,
            title: false, //不显示标题
            content: $("#inviteQrcodeFactDailog"),
            area: ['550'], //宽高
            skin: 'wu-dailog',
            btn: false,
            // skin: 'adialog-Shops-skin',
            btn: false,
        });

    }

    module.makeCode = function (elText, id) {
        $("#" + id).html("");

        var qrcode = new QRCode(document.getElementById(id), {
            width: 200,
            height: 200
        });

        qrcode.makeCode(elText);
    }

    //获取二维码
    module.getQRC = function (flag) {
        if (flag == "agent") {
            commonModule.Ajax({
                url: '/Partner/LoadMyAgentQrCode',
                loading: true,
                data: {},
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        layer.closeAll();
                        return;
                    }
                    $("#qrCodeExceedTimeIdAgent").html(rsp.Data.ExceedTime);
                    module.makeCode(rsp.Data.QrCode, 'agentQrcodeId');
                }
            });
        }
        else {
            commonModule.Ajax({
                url: '/Partner/LoadMySupplierQrCode',
                loading: true,
                data: {},
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        layer.closeAll();
                        return;
                    }
                    $("#qrCodeExceedTimeIdFact").html(rsp.Data.ExceedTime);
                    module.makeCode(rsp.Data.QrCode, 'supplierQrcodeId');
                }
            });
        }
    }

    //下载二维码
    module.onloadQRC = function (flag) {
        var id = "supplierQrcodeId";
        var fname = "MySupplie-QRC";
        if (flag == "agent") {
            id = "agentQrcodeId";
            fname = "MyAgent-QRC";
        }
        var base64 = $("#" + id + ">img").attr("src");

        if (window.navigator.msSaveOrOpenBlob) {
            var bstr = atob(base64.split(',')[1]);
            var n = bstr.length;
            var u8arr = new Uint8Array(n);
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }
            var blob = new Blob([u8arr]);
            window.navigator.msSaveOrOpenBlob(blob, 'chart-download' + '.' + 'png');
        } else {
            // 这里就按照chrome等新版浏览器来处理
            var a = document.createElement('a');
            a.href = base64;
            a.setAttribute('download', fname);
            a.click();
        }
    }

    module.hasNoRegisteredAgentWarn = function () {
        module.getQRC('agent');
        layer.open({
            type: 1,
            title: false, //不显示标题
            content: $("#inviteQrcodeAgentDailog"),
            area: ['550'], //宽高
            btn: false,
            // skin: 'adialog-Shops-skin',
            skin: 'wi-dialog',
            btn: false,
        });

    }

    module.inviteMerchantsCopy = function () {
        var html = "";
        html += '<div class="inviteMerchantsCopyWrap">';
        html += '<div class="inviteMerchantsCopyWrap-title wu-color-m">';
        html += '<span>复制以下代发教程链接发送给分销商家，</span>';
        html += '<span>请商家按教程设置自动推送代发订单。</span>';
        html += '</div>';
        html += '<div class="inviteMerchantsCopyWrap-btns">';
        html += '<span class="layui-btn layui-btn-normal layui-btn35 wu-btn wu-btn-mid" onclick=\'commonModule.CopyText("#inputTeach")\'>复制教程</span>';
        html += '</div>'
        html += '</div>';

        layer.open({
            type: 1,
            title: false, //不显示标题
            content: html,
            area: ['550'], //宽高
            btn: false,
            // skin: 'adialog-Shops-skin',
            skin: 'wu-dailog',
            btn: false,
        });

    }
    function clearGetData() {
        reqModel = {
            Id: 0,
            Key: "",
            Status: 0,
            PageIndex: reqModel.PageIndex,
            PageSize: reqModel.PageSize,
            SupplierFxUserId: 0,
            SenderName: null,
            SenderMobile: null,
            Province: null,
            City: null,
            District: null,
            Address: null,
            RemarkName: null,
            SenderTelePhone: null
        };
    }
    module.GetData = function (type, crruData) {
        clearGetData()
        var mobileReg = /^(86-[1][0-9]{10})|(86[1][0-9]{10})|([1][0-9]{10})$/;  //手机正则
        var phoneReg = /^(0\d{2,3}-?\d{7,8})|((（|\()0\d{2,3}(\)|）)\d{7,8})$/; //电话正则
        reqModel.Status = 0;
        if (type == 1) {
            reqModel.Id = 0;
            //添加验证数据格式
            reqModel.SupplierFxUserId = $("#adialog_key").attr("user_id");
            if (crruData && crruData.SupplierType == "Virtual") {
                reqModel.NickName = $("#adialog_key").val();
                if (!reqModel.NickName) {
                    layer.msg('厂家名称不能为空', { icon: 7 });
                    return false;
                }
            }
            else {
                if ($("#adialog_key").val() == "" || $("#adialog_key").val() == null) {
                    layer.msg('厂家不能为空', { icon: 7 });
                    return false;
                }
                if (reqModel.SupplierFxUserId <= 0 || reqModel.SupplierFxUserId == undefined) {
                    layer.msg($("#adialog_key").next('i').text());
                    return false;
                }
            }

        } else if (type == 2) {
            //编辑赋值
            reqModel.Id = $("#adialog_addSupplier").attr("data-id");
        } else if (type == 3) {
            //同意厂家请求绑定
            reqModel.Id = $("#adialog_addSupplier").attr("data-id");
            reqModel.Status = 1;
        }
        reqModel.SenderName = $.trim($("#adialog_name").val());
        reqModel.SenderMobile = $.trim($("#adialog_mobile").val());
        reqModel.SenderTelePhone = $.trim($("#adialog_telephone").val());
        reqModel.Province = $("#supplierProvince-select").val();
        reqModel.City = $("#supplierCity-select").val();
        reqModel.District = $("#supplierArea-select").val();
        reqModel.Address = $.trim($("#supplier-textarea").val());
        reqModel.RemarkName = $.trim($("#adialog_remarkName").val());

        //2021-10-27,项颖说隐藏。和二维码扫描绑定保持一致。只有编辑有，其他场景都没有地址修改
        if (type == 1 || type == 3)
            return true;

        if (crruData && crruData.SupplierType == "Virtual" && (type == 1 || type == 2))
            return true;

        if (reqModel.SenderName == "" || reqModel.SenderName == null) {
            layer.msg('请填写代发件人姓名', { icon: 7 });
            return false;
        }
        if (!reqModel.SenderMobile && !reqModel.SenderTelePhone) {
            layer.msg('联系手机、联系固话必填一个', { icon: 7 });
            return false;
        }
        if (reqModel.SenderMobile != "" && !mobileReg.test(reqModel.SenderMobile)) {
            layer.msg('代发手机号格式不对', { icon: 7 });
            return false;
        }

        if (reqModel.SenderTelePhone != "" && !phoneReg.test(reqModel.SenderTelePhone)) {
            layer.msg('代发固话格式不对', { icon: 7 });
            return false;
        }
        if (reqModel.Province == undefined || reqModel.Province == "0") {
            layer.msg('请选择省份', { icon: 7 });
            return false;
        }
        if (reqModel.City == undefined || reqModel.City == "0") {
            layer.msg('请选择城市', { icon: 7 });
            return false;
        }
        if (reqModel.District == undefined || reqModel.District == "0") {
            var lent = $("#supplierArea-select>option").length;
            if (lent > 1) {
                layer.msg('请选择区县', { icon: 7 });
            }
            return false;
        }
        if (reqModel.Address == '') {
            layer.msg('请填写详细地址', { icon: 7 });
            return false;
        }
        return true;
    }

    function selectCallBack(control) {
        var deep = control.attr('deep');
        if (deep > 1) {
            var dataValue = control.attr("data-value");
            var isExistsVal = control.find("option[value='" + dataValue + "']").length;
            if (isExistsVal > 0)
                control.val(dataValue).trigger('change');
        }
    };

    //绑定厂家
    module.Add = function (supplierType) {
        if (addWarnAddWarnDialog) {
            layer.close(addWarnAddWarnDialog);
        }
        //虚拟厂家 改变弹窗结构
        $("#virtual_li").remove();
        $("#virtual_li02").remove();
        if (supplierType == "Virtual") {
            var Lihtml = "", Lihtml02 = "";

            //Lihtml += '<li id="virtual_li"><div style="color:#04385d;font-size:14px;"><span>新增虚拟厂家</span></div>';
            Lihtml += '<li id="virtual_li" style="display: flex;flex-direction:column;margin-bottom:10px">';
            Lihtml += '<div style="color:#f7941f;font-size:13px;">当您的厂家没有使用店管家订单分发系统时，你又需要把订单拆分、密文导出给他们，就可以创建虚拟厂家，然后通过商品绑定虚拟帐号的方式快速分类代发订单。</div>';
            Lihtml += '<div style="color:#666;font-size:13px;margin-top:10px">注意系统禁止一切平台订单（除手工录入单）明文展示以及导出，保护消费者隐私是全平台需遵守的规则.</div>';
            Lihtml += '</li>';
            //Lihtml02 += '<li id="virtual_li02">店管家分销代发系统禁止一切平台订单（除手工录入单）明文展示以及导出，保护消费者隐私是全平台需遵守的规则.</li>'
            $("#adialog_addSupplier_content").prepend(Lihtml);
            $("#spanName").html('虚拟厂家名称<i style="color:#ff511c">*</i>');
            $("#addSupplier_address").before(Lihtml02);
            $("#addSupplier_address_title").html("");
            $("#adialog_remarkName").parent().parent().hide();
        } else {
            $("#virtual_li").remove();
            $("#virtual_li02").remove();
            $("#spanName").html('厂家账号：');
            $("#addSupplier_address_title").html("代发订单发货地址：");
            $("#adialog_remarkName").parent().parent().show();
        }

        $("#adialog_addSupplier input").val("");
        $("#adialog_key").css({ "border-width": "1px" });
        $("#adialog_addSupplier").attr("data-id", 0);
        $("#supplierProvince-select").val(0);
        $("#supplier-textarea").val("");
        $("#adialog_remarkName").val("");
        $("#isdefault").removeAttr("checked");
        var adialog_key = $("#adialog_key");
        adialog_key.nextAll().remove();
        adialog_key.attr("user_id", "0").attr("disabled", false);
        var apiUrl = "/Partner/AddBindSupplier";
        var isVirtual = supplierType == "Virtual"
        if (isVirtual) {
            apiUrl = "/Partner/AddVirtualSupplier";
            module.SupplierType = "Virtual";
        }
        else {
            module.SupplierType = "";
        }
        var virtualWord = isVirtual ? "虚拟" : "";
        //2021-10-27,项颖说隐藏。和二维码扫描绑定保持一致。只有编辑有，其他场景都没有地址修改
        $("#addSupplier_address").hide();
        $("#addSupplier_address_select").hide();

        //加载地址级联选择
        commonModule.LoadAreaInfoToControl('supplierProvince-select', 1, function () {
        }, selectCallBack, "name");
        var addDialog = layer.open({
            type: 1,
            title: "绑定" + virtualWord + "厂家", //不显示标题
            content: $('#adialog_addSupplier'),
            area: ['550'], //宽高
            skin: 'wu-dailog', 
            btn: ['保存', '取消'],
            success: function () {
                if (commonModule.FxUserAddres.Id > 0 && commonModule.FxUserAddres != null) {
                    $("#isdefault").parent().show();
                } else {
                    $("#isdefault").parent().hide();
                }
            },
            yes: function () {
                if ($(".layui-layer-btn0").attr("disabled") == "disabled") {
                    return;
                }
                var data = module.GetData(1, { SupplierType: supplierType });
                if (!data) { return false; }
                $(".layui-layer-btn0").attr("disabled", true);
                commonModule.Ajax({
                    url: apiUrl,
                    type: "POST",
                    data: { _model: reqModel },
                    loading: true,
                    success: function (rsp) {
                        $(".layui-layer-btn0").removeAttr("disabled");
                        if (commonModule.IsError(rsp)) {
                            return;
                        }
                        layer.closeAll();
                        if (isVirtual)
                            layer.msg("绑定成功", { icon: 1 });
                        else
                            layer.msg("申请绑定成功，请您通知对方及时在分销系统首页确认您的合作申请", { icon: 1 });
                        //module.LoadList(false);
                        //layer.close(addDialog);
                    }
                });
            },
            cancel: function () {
                layer.close(addDialog);
            }
        });
    }

    module.Check = function () {
        var adialog_key = $("#adialog_key");
        adialog_key.nextAll().remove();
        adialog_key.attr("user_id", "0");
        var key = adialog_key.val();
        if (key == "" || key == undefined) {
            return;
        }
        if (module.SupplierType == "Virtual")
            return;
        commonModule.Ajax({
            url: "/Partner/GetByNamebeltStatus",
            type: "POST",
            data: { key: key },
            async: true,
            success: function (rsp) {
                if (rsp.Success == false) {
                    adialog_key.parent().append('<i class="layui-icon layui-icon-close-fill" style="font-size: 15px; color: red;"><span style="font-size: 10px;">' + rsp.Message + '</span></i>');
                    return;
                }
                var data = rsp.Data;
                if (data.Id <= 0 || data == undefined) {
                    //不存在厂家
                    adialog_key.parent().append('<i class="layui-icon layui-icon-close-fill" style="font-size: 15px; color: red;"><span style="font-size: 10px;">厂家不存在</span></i>');
                } else {
                    var tmp = "";
                    if (data.NickName != null && key == data.Mobile)
                        tmp = data.NickName;
                    if (data.Mobile != null && key == data.NickName)
                        tmp = data.Mobile;

                    //用户状态（1：正常，2：禁用，3：删除）
                    switch (data.Status) {
                        case 0:
                        case 1:
                        case 2:
                            adialog_key.parent().append('<i class="layui-icon layui-icon-ok-circle" style="font-size: 15px;"><span style="font-size: 10px;">' + tmp + '</span></i>');
                            adialog_key.attr("user_id", data.Id);
                            break;
                        case 4:
                            adialog_key.parent().append('<i class="layui-icon layui-icon-close-fill" style="font-size: 15px; color: red;"><span style="font-size: 10px;">厂家已经被绑定</span></i>');
                            break;
                        case 5:
                            adialog_key.parent().append('<i class="layui-icon layui-icon-close-fill" style="font-size: 15px; color: red;"><span style="font-size: 10px;">不能绑定自己</span></i>');
                            break;
                        case 6:
                            //未取消的用户有绑定限制
                            if (data.Status2 != 4) {
                                adialog_key.parent().append('<i class="layui-icon layui-icon-close-fill" style="font-size: 15px; color: red;"><span style="font-size: 10px;">已经绑定</span></i>');
                            } else {
                                adialog_key.attr("user_id", data.Id);
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        });
    }

    module.DelCheck = function () {
        var adialog_key = $("#adialog_key");
        adialog_key.nextAll().remove();
    }

    //绑定商家
    module.AddAgent = function () {
        if (addWarnAddWarnDialogAgent) {
            layer.close(addWarnAddWarnDialogAgent);
        }
        $("#agent_key").val("");
        $(".IsNumderNull,.IsShowName").hide();
        var addDialog = layer.open({
            type: 1,
            title: "绑定商家", //不显示标题
            content: $('#adialog_addDistributor'),
            area: ['420'], //宽高
            skin: 'wu-dailog', 
            btn: ['申请绑定', '取消'],
            yes: function () {
                if ($(".layui-layer-btn0").attr("disabled") == "disabled") {
                    return;
                }
                var key = $.trim($("#agent_key").val());
                if (key == "" || key == null) {
                    $(".IsNumderNull").html("商家不能为空").show();
                    //layer.msg("商家不能为空");
                    return;
                }
                var agentId = $("#agent_key").attr("user_id");
                if (agentId <= 0 || agentId == null) {
                    //layer.msg($(".IsNumderNull").text());
                    return;
                }
                $(".layui-layer-btn0").attr("disabled", true);
                commonModule.Ajax({
                    url: "/Partner/AddBindAgent",
                    type: "POST",
                    loading: true,
                    data: { agentId: agentId, remark: $.trim($("#agent_remark").val()) },
                    success: function (rsp) {
                        if (commonModule.IsError(rsp)) {
                            //layer.closeAll();
                            $(".layui-layer-btn0").attr("disabled", false);
                            return true;
                        }
                        $(".layui-layer-btn0").removeAttr("disabled");
                        layer.closeAll();
                        layer.msg('申请绑定成功，请您通知对方及时在分销系统首页确认您的合作申请', { icon: 1 });
                        //module.LoadList(false);
                        //layer.close(addDialog);
                    }
                });
            },
            cancel: function () {
                layer.closeAll();
            }
        });
    }

    module.CheckAgent = function () {
        var agent_key = $("#agent_key");
        $(".IsNumderNull,.IsShowName").hide();
        agent_key.attr("user_id", "0");
        var key = agent_key.val();
        if (key == "" || key == undefined) {
            return;
        }
        commonModule.Ajax({
            url: "/Partner/GetByNamebeltStatusV1",
            type: "POST",
            data: { key: key },
            async: true,
            success: function (rsp) {
                if (rsp.Success == false) {
                    $(".IsShowName").hide();
                    $(".IsNumderNull").html(rsp.Message).show();
                    return;
                }
                var data = rsp.Data;
                if (data.Id <= 0 || data == undefined) {
                    $(".IsShowName").hide();
                    //不存在厂家
                    $(".IsNumderNull").html("商家不存在").show();
                } else {
                    //用户状态（1：正常，2：禁用，3：删除）
                    switch (data.Status) {
                        case 1:
                            $(".IsNumderNull").hide();
                            $(".IsShowName").html("账户名：" + (data.NickName || data.Mobile)).show();
                            agent_key.attr("user_id", data.Id);
                            break;
                        case 4:
                            $(".IsShowName").hide();
                            $(".IsNumderNull").html("商家已经被绑定").show();
                            break;
                        case 5:
                            $(".IsShowName").hide();
                            $(".IsNumderNull").html("不能绑定自己").show();
                            break;
                        case 6:
                            $(".IsShowName").hide();
                            if (data.Status2 != 4) {
                                $(".IsNumderNull").html("已经绑定").show();
                            } else {
                                agent_key.attr("user_id", data.Id);
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        });
    }

    module.DelCheckAgent = function () {
        var agent_key = $("#agent_key");
        $(".IsNumderNull,.IsShowName").hide();
    }

    module.targetUrl = function (name) {

        var url = "";
        if (name == "OfflineOrder") {
            url = "/Common/Page/NewOrder-OfflineOrder";
        }
        window.open(url, "_blank");

    }


    module.againLoginQrCodeDailog = function () {
        var addDialog = layer.open({
            type: 1,
            title: false,
            btn: false,
            skin: 'againBindQrCodeDailogskin',
            content: $("#againBindQrCodeDailog"),
            area: ["580px"], //宽高
            yes: function () {
            }
        })
    }

    /*   module.againLoginQrCodeDailog()*/



    module.BindShop = function (shop) {
        if (shop == null || shop == undefined) return;
        var html = `<div style="padding:25px;display:flex;justify-content: center;align-items: center;flex-direction: column;font-size:16px;">
              <div style="padding-bottom:20px">系统识别您当前登录的店铺信息为：<s style="color:#fe6f4f;margin-left:3px;font-weight:700">`+ shop.NickName + `</s></div>
              <div style="padding-bottom:20px">请问是否需要帮您自动绑定该店铺</div>
              <div style="width: 480px;display: flex;justify-content: center;padding: 5px;font-size: 15px;background-color: #fef0f0;color: #f56c6c;">（ 绑定成功后可自动同步后台订单 ）</div>
            </div>`;
        var bindshopDailog = layer.open({
            type: 1,
            title: "提示", //不显示标题
            content: html,
            area: '550', //宽高
            btn: ['确定绑定', '暂不绑定'],
            success: function () {
            },
            yes: function () {
                commonModule.Ajax({
                    type: "POST",
                    url: "/GeneralizeIndex/AutoBindShop",
                    data: { encryptInfo: shop.EncryptInfo },
                    success: function (rsp) {
                        if (rsp.Success) {
                            layer.msg("绑定成功");
                        } else {
                            layer.msg("绑定失败");
                        }
                        layer.close(bindshopDailog);
                    }
                });

            },
            btn2: function () {

            },
            cancel: function () {
                $(".registerInto-button .loading").hide();
            }
        });
    }

    return module;
}(generalizeIndexModule || {}, commonModule, jQuery, layer));