
///订单列表
var manualTrackingNumberInput = null;
var removeManualDeliveryRowData = null;
var manualTrackingNumberFocus = null;
var clearTrackingNoInput = null;
var mouseoverManualDeliveryDeleteTip = null;
var mouseoutManualDeliveryDeleteTip = null;
var handleAssociatedOrder = null;
var waitOrderModule = (function (module, common, $, layer) {
    /*
     * 初始化设置
     */
    var expressCompanyList = [];
    var ManualDeliveryDataObj = {
        IsManual: true,
        CompanyCode: '',
        OrderData: [],
    }
    // 当前所属平台类型
    var currentPt = '';
    if (commonModule.CloudPlatformType != undefined) {
        currentPt = commonModule.CloudPlatformType.toLowerCase();
    } else {
        currentPt = commonModule.getQueryVariable('pt').toLowerCase();
    }
    var OfflineOrderImportType = '1'; // 线下单类型，单条，关联订单
    function getExpressCompanyData() {
        //异步加载快递公司
        commonModule.Ajax({
            url: "/Common/LoadExpressCompany",
            type: "POST",
            loading: true,
            success: function (rsp) {
                if (rsp.Success) {
                    expressCompanyList = rsp.Data || [];
                    initRenderExpressSelectBox();
                }
            }
        });
    }
    // 初始化快递公司数据
    function initRenderExpressSelectBox() {
        var selectExpressList = [];
        expressCompanyList.forEach(function (item) {
            selectExpressList.push({
                Value: item.CompanyCode,
                Text: item.CompanyName,
            });
        });
        var expressSelectInit = {
            eles: '#selectWrap_selectExpress',
            emptyTitle: '请选择快递公司', // 设置没有选择属性时，出现的标题
            data: selectExpressList,
            searchType: 1, // 1出现搜索框，不设置不出现搜索框
            isRadio: true, // 有设置，下拉框改为单选
            allSelect: false,
            selectData: [], // 初始化数据
            skin: 'n-wuSelect',
            isHasCheck: false,
            selectCallBack: function (selectData) {
                var selectObj = selectData[0];
                ManualDeliveryDataObj.CompanyCode = selectObj.Value;
                $("#selectWrap_selectExpress").closest(".n-inputWrap").removeClass("warnInput");
            },
        };
        // 渲染快递选择下拉框
        var selectExpressBox = new selectBoxModule2();
        selectExpressBox.initData(expressSelectInit);
    }

    module.Init = function () {

        module.InitOrderListEvent();
    }


    var showCommonPrintWarn_parplatformArray = [];
    module.FqTempUprate = function (callback) {
        //去后台判断是否显示继续打印
        templateSetCommonModule.CheckPlatformForceUseTemp('Fengqiao', false, function (isForceStart, forceTime) {
            var ggUrl = '<div style="padding: 20px;font-size:14px;"><a href="https://open.sf-express.com/customerService/899827?activeIndex=855073&noticeTitle=%E3%80%90%E7%B3%BB%E7%BB%9F%E9%80%9A%E7%9F%A5%E3%80%91%E7%AC%AC%E4%B8%89%E6%96%B9ISV%E9%80%9A%E7%9F%A5&noticeId=53" target="_blank" style="margin:20px;color:#3aadff;">官方公告</a><a href="javascript:void(0);" style="margin:20px;color:#3aadff;" onclick="addTemplateModule.FengQiaoCloudUpgrade();return false;">一键升级云模板</a></div>';
            if (isForceStart == true) {
                //强制实施丰桥云面单打印
                var index = layer.open({
                    type: 1,
                    title: "打印快递单提示",
                    content: ('<div style="padding: 20px;font-size:14px;">应顺丰官方要求，为进一步规范电子运单模版，对电子运单模版进行统一管控，顺丰已推出顺丰云打印，请使用丰桥云模板进行打印。</div>' + ggUrl),
                    area: '480px', //宽高
                    skin: 'wu-dailog',
                    //btn: ['取消'],
                    //yes: function () {
                    //    layer.close(index);
                    //    checkTaobaoModifySku();
                    //},
                    //btn2: function () {
                    //    layer.close(index);
                    //}
                });
            }
            else {
                //可继续打印
                var index = layer.open({
                    type: 1,
                    title: "打印快递单提示",
                    content: ('<div style="padding: 20px;font-size:14px;">重要通知：应顺丰官方要求，为进一步规范电子运单模版，对电子运单模版进行统一管控，顺丰已推出顺丰云打印，请于 <label style="font-weight: bold;">' + forceTime + '</label> 前完成丰桥云模板切换。</div> ' + ggUrl),
                    area: '480px', //宽高
                    btn: ['继续打印', '取消'],
                    skin: 'wu-dailog',
                    yes: function () {
                        layer.close(index);
                        callback();
                    },
                    btn2: function () {
                        layer.close(index);
                    }
                });
            }
        });
    }

    module.ShowOrderUprate = function (callback, uprate_type, parameters) {
        if (uprate_type == "kstemp_uprate") {
            //去后台判断是否显示继续打印
            templateSetCommonModule.CheckPlatformForceUseTemp('KuaiShou', false, function (isForceStart, forceTime) {
                if (isForceStart == true) {

                    showDkPrintWarn('快手', 'https://m0ij9216j9.feishu.cn/docx/KQ72d76rvoKU5OxKwnycvA4Ln9d');
                }
                else {
                    showDkPrintWarn('快手', 'https://m0ij9216j9.feishu.cn/docx/KQ72d76rvoKU5OxKwnycvA4Ln9d');
                }
            });
        }
        else if (uprate_type == "kspintuan_uprate") {
            var html = '<div class="newCommonDailog" style="width:520px">';
            html += '<div class="newCommonDailog-main">';
            html += '<span style="font-size:18px;font-weight:700;margin:0 0 20px 0;color:#000;">忽略打印提醒</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;text-align:center;">勾选的订单中包含快手【待拼单】，该类订单暂无收件地址，</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;text-align:center;">不支持打单发货，系统已自动为您排除勾选</span>';
            html += '<div class="newCommonDailog-footer" ><span onclick="waitOrderModule.closeIgnorePrintWarnBtn()" class="layui-layer-btn1" style="width:116px;margin-right:25px;">返回上一步</span><span  onclick="waitOrderModule.sureIgnorePrintWarnBtn()" class="layui-layer-btn1 layui-layer-btn2">确认，继续打印</span></div>';
            html += '</div>'
            html += '</div>'
            var IgnorePrintWarnDailg = layer.open({
                type: 1,
                title: false, //不显示标题
                content: html,
                area: 600, //宽高
                skin: 'adialog-Shops-skin',
                btn: false,
                closeBtn: 1,

            });

            module.closeIgnorePrintWarnBtn = function () {
                layer.close(IgnorePrintWarnDailg);
            }
            module.sureIgnorePrintWarnBtn = function () {
                layer.close(IgnorePrintWarnDailg);
                callback();
            }
        }
        else if (uprate_type == "ks_presentOrder_uprate") {
            var html = '<div class="newCommonDailog" style="width:520px">';
            html += '<div class="newCommonDailog-main">';
            html += '<span style="font-size:18px;font-weight:700;margin:0 0 20px 0;color:#000;margin: 0 0 20px 20px;">异常提示</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;margin: 0 0 0 20px;">所选订单中包含未填写收货地址的送礼单，您无法对这类订</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;margin: 0 0 20px 20px;">单进行打印操作，是否跳过该类订单继续打印正常订单？</span>';
            html += '<span style="font-size:12px;color:#666;line-height:25px;margin: 0 0 0 20px;">注：收礼人未填写收货信息前送礼类订单无法正常显示和打单发货</span>';
            html += '<div class="newCommonDailog-footer" ><span onclick="waitOrderModule.closeIgnorePrintWarnBtn()" class="layui-layer-btn1" style="width:116px;margin-right:25px;">取消</span><span  onclick="waitOrderModule.sureIgnorePrintWarnBtn()" class="layui-layer-btn1 layui-layer-btn2">跳过，继续打印</span></div>';
            html += '</div>'
            html += '</div>'
            var IgnorePrintWarnDailg = layer.open({
                type: 1,
                title: false, //不显示标题
                content: html,
                area: 600, //宽高
                skin: 'adialog-Shops-skin',
                btn: false,
                closeBtn: 1,

            });

            module.closeIgnorePrintWarnBtn = function () {
                layer.close(IgnorePrintWarnDailg);
            }
            module.sureIgnorePrintWarnBtn = function () {
                layer.close(IgnorePrintWarnDailg);
                callback();
            }
        }
        else if (uprate_type == "toutiao_sf_free_shipping") {
            var html = '<div class="newCommonDailog" style="width:520px">';
            html += '<div class="newCommonDailog-main">';
            html += '<span style="font-size:18px;font-weight:700;margin:0 0 20px 0;color:#000;">打印异常提示</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;text-align:center;">您已选中<b>' + parameters.selectNum + '</b>笔订单，其中包含<b>' + parameters.logicOrderIds.length + '</b>笔异常订单不满足取号需求，</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;text-align:center;">需采用对应解决方案进行重新取号</span>';
            html += '<div class="newCommonDailog-footer" ><span onclick="waitOrderModule.closeIgnorePrintWarnBtn()" class="layui-layer-btn1" style="width:116px;margin-right:25px;">查看异常订单明细</span><span  onclick="waitOrderModule.sureIgnorePrintWarnBtn()" class="layui-layer-btn1 layui-layer-btn2">忽略异常继续打印其他订单</span></div>';
            html += '</div>'
            html += '</div>'
            var IgnorePrintWarnDailg = layer.open({
                type: 1,
                title: false, //不显示标题
                content: html,
                area: 600, //宽高
                skin: 'adialog-Shops-skin',
                btn: false,
                closeBtn: 1,

            });
            module.closeIgnorePrintWarnBtn = function () {
                //layer.close(IgnorePrintWarnDailg);
                localStorage.setItem('query_logicorderid_input', parameters.logicOrderIds.join(','));
                window.parent.postMessage({ refresh: 'IFrameRenderToExceptionOrder' }, '*');
                //window.open(window.parent.location.href + "&QuickName=ReceiverChangeOrderBtn");
            }
            module.sureIgnorePrintWarnBtn = function () {
                layer.close(IgnorePrintWarnDailg);
                callback();
            }
        }
        else if (uprate_type == "toutiao_sf_free_shipping_for_send") {
            var selectOrders = parameters.selectOrders || [];
            var logicOrderIds = parameters.logicOrderIds || [];
            var html = '<div class="newCommonDailog" style="width:520px">';
            html += '<div class="newCommonDailog-main">';
            html += '<span style="font-size:18px;font-weight:700;margin:0 0 20px 0;color:#000;">发货异常提示</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;text-align:center;">您已选中<b>' + selectOrders.length + '</b>笔订单，其中包含<b>' + logicOrderIds.length + '</b>笔异常订单不满足发货需求，</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;text-align:center;">需采用对应解决方案进行重新取号</span>';
            html += '<div class="newCommonDailog-footer" >';
            html += '<span onclick="waitOrderModule.cancelOrderWarnBtn()" class="layui-layer-btn1" style="width:100px;margin-right:20px;">回收异常订单</span>';
            html += '<span onclick="waitOrderModule.closeIgnorePrintWarnBtn()" class="layui-layer-btn1" style="width:116px;margin-right:20px;">查看异常订单明细</span>'
            html += '<span  onclick="waitOrderModule.sureIgnorePrintWarnBtn()" class="layui-layer-btn1 layui-layer-btn2">忽略异常继续发货其他订单</span>'
            html += '</div>'
            html += '</div>'
            html += '</div>'
            var IgnorePrintWarnDailg = layer.open({
                type: 1,
                title: false, //不显示标题
                content: html,
                area: 600, //宽高
                skin: 'adialog-Shops-skin',
                btn: false,
                closeBtn: 1,
            });
            module.cancelOrderWarnBtn = function () {
                //layer.close(IgnorePrintWarnDailg);
                layer.closeAll();
                var dialogDoorTips2 = layer.open({
                    type: 1,
                    title: "回收异常单号",
                    content: $("#dy_hs_percentWarn"),
                    shadeClose: true,
                    area: ['500px']
                });
                var func = function () {
                    var cancelWaybillCodes = [];
                    var continuePrintOrders = [];
                    for (var i = 0; i < selectOrders.length; i++) {
                        var _selectOrder = selectOrders[i];
                        if (logicOrderIds.indexOf(_selectOrder.LogicOrderId) != -1) {
                            var row = orderTableBuilder.rows[_selectOrder.Index];
                            var waybillCodes = row.WaybillCodes || [];
                            for (var j = 0; j < waybillCodes.length; j++) {
                                var w = waybillCodes[j];
                                var templateId = w.TemplateId || 0;
                                var waybillCodeId = w.WaybillCodeId || 0;
                                var waybillCode = w.WaybillCode || "";
                                var orderId = _selectOrder.LogicOrderId || "";
                                var data = { row: row, templateId: templateId, wcRecycleViewModel: [{ WaybillCodeId: waybillCodeId, WaybillCode: waybillCode, OrderId: orderId }] };
                                cancelWaybillCodes.push(data);
                            }
                            continuePrintOrders.push(_selectOrder);
                        } else {
                            var chx = $("input.order-chx[data-index='" + _selectOrder.Index + "']")[0];
                            var newChx = $(chx).prop("checked", !chx.checked);
                            newChx.closest(".order-row").removeClass("active");
                            newChx.closest(".layui-row-item").removeClass("active");

                            var row = orderTableBuilder.rows[_selectOrder.Index];
                            row.checked = false;
                        }
                    }
                    if (cancelWaybillCodes.length == 0) {
                        layer.msg("当前订单不具备回收能力, 请检查订单是否存在运单号");
                    }
                    else {
                        // 回收单号错误信息集合
                        var errMsgList = [];
                        var errMsgStr = "";
                        var wlen = cancelWaybillCodes.length;
                        for (var c = 0; c < wlen; c++) {
                            var row = cancelWaybillCodes[c].row;
                            var wdata = {};
                            wdata.templateId = cancelWaybillCodes[c].templateId;
                            wdata.wcRecycleViewModel = cancelWaybillCodes[c].wcRecycleViewModel;
                            commonModule.Ajax({
                                url: '/WaybillCodeList/CancelWaybillCode',
                                type: "POST",
                                loading: false,
                                async: false,
                                data: wdata,
                                success: function (rsp) {
                                    // 设置回收单号进度百分比
                                    var percent = ((c + 1) * 1.0 / wlen) * 100;
                                    $("#dy_hs_percentWarn .dgjNew-progress-bar").css("width", percent + "%");
                                    if (!rsp.Success) {
                                        var errMsg = rsp.Message || "";
                                        errMsgList.push({ WaybillCodeId: wdata.wcRecycleViewModel[0].WaybillCodeId, row: row });
                                        errMsgStr += (i + 1 + ':') + errMsg + "\r\n";
                                    }
                                },
                                error: function (rsp) {
                                    if (rsp.status == 401) {
                                        layer.msg("暂无权限，请联系管理员");
                                    } else {
                                        layer.msg(rsp.message);
                                    }
                                }
                            });
                        }

                        layer.close(dialogDoorTips2);
                        if (errMsgList.length > 0) {
                            layer.open({
                                type: 1,
                                title: "接口回收失败提示",
                                content: $("#copy_hs_warnTarget"),
                                shadeClose: true,
                                area: ['500px'],
                                btn: ["复制失败运单号联系客服", "等待自动回收"],
                                skin: "errMsgListDailog",
                                success: function () {
                                    $("#copy_hs_warnTarget_title").html(errMsgStr);
                                },
                                btn1: function () {
                                    commonModule.CopyText("#copy_hs_warnTarget_title")
                                },
                                btn2: function () {
                                    var errWaybillCodeIds = "";
                                    for (var i = 0; i < errMsgList.length; i++) {
                                        errWaybillCodeIds += errMsgList[i].WaybillCodeId + ',';
                                    }

                                    // 等待自动回收
                                    commonModule.Ajax({
                                        url: '/WaybillCodeList/AutoCancelWaybillCode',
                                        type: "POST",
                                        loading: false,
                                        async: false,
                                        data: { "waybillCodeIds": errWaybillCodeIds },
                                        success: function (rsp) {
                                            if (rsp.Success) {
                                                layer.msg("运单号回收成功");
                                                for (var i = 0; i < errMsgList.length; i++) {
                                                    var row = errMsgList[i].row;
                                                    row.WaybillCodes = [];
                                                    orderTableBuilder.refreshRow(row);
                                                }
                                            }
                                            else
                                                layer.comfirm(rsp.Message || "", { icon: 2 });
                                        }
                                    });
                                }
                            });
                        }
                        else {
                            // 回收成功
                            var dialogDoorTips3 = layer.open({
                                type: 1,
                                title: "回收结果",
                                content: $("#dy_hs_resultTarget"),
                                shadeClose: true,
                                area: ['480px'],
                                btn: ["取消操作", "打印新单号"],
                                btn1: function () {
                                    layer.close(dialogDoorTips3);
                                },
                                btn2: function () {
                                    layer.close(dialogDoorTips3);
                                    //expressPrinter.print(continuePrintOrders, 'Normal');
                                    for (var i = 0; i < cancelWaybillCodes.length; i++) {
                                        var row = cancelWaybillCodes[i].row;
                                        orderTableBuilder.rows[row.Index].WaybillCodes = [];
                                        //orderTableBuilder.refreshRow(row);
                                    }
                                    $(".express-print-btn").click(); // 重新打印异常单
                                }
                            });
                        }
                    }
                }

                func();
            }
            module.closeIgnorePrintWarnBtn = function () {
                //layer.close(IgnorePrintWarnDailg);
                localStorage.setItem('query_logicorderid_input', logicOrderIds.join(','));
                window.parent.postMessage({ refresh: 'IFrameRenderToExceptionOrder' }, '*');
                //window.open(window.parent.location.href + "&QuickName=ReceiverChangeOrderBtn");
            }
            module.sureIgnorePrintWarnBtn = function () {
                layer.close(IgnorePrintWarnDailg);
                callback();
            }
        }
        else if (uprate_type == "toutiao_recommendedexpress") {
            var html = '<div class="newCommonDailog" style="width:620px">';
            html += '<div class="newCommonDailog-main">';
            html += '<span style="font-size:18px;font-weight:700;margin:0 0 20px 0;color:#000;">异常提示</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;">根据您的发货探查设置，已校验到您勾选的订单中有<b style="margin:0 3px;color:#333">' + parameters.selectNum + '</b>笔订单 <span class="dColor hover" onclick="waitOrderModule.showOrderFailBtn()" >查看明细</span></span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;">不满足当前官方在线推荐的快递，是否继续取号？</span>';
            html += '<div class="newCommonDailog-footer recommendedexpress-btns" ><span onclick="waitOrderModule.closeIgnorePrintAllBtn()" class="layui-layer-btn1" style="margin-bottom:15px;">继续打印全部订单</span><span  onclick="waitOrderModule.sureIgnorePrintWarnBtn()" class="layui-layer-btn1">将推荐快递标注在打单备注上，暂不打印</span></div>';
            html += '</div>'
            html += '</div>'
            var IgnorePrintWarnDailg = layer.open({
                type: 1,
                title: false, //不显示标题
                content: html,
                area: 550, //宽高
                skin: 'adialog-Shops-skin',
                btn: false,
                closeBtn: 1
            });
            module.closeIgnorePrintAllBtn = function () {
                layer.close(IgnorePrintWarnDailg);
                callback();
            }
            module.sureIgnorePrintWarnBtn = function () {
                layer.close(IgnorePrintWarnDailg);
                parameters.continueFunc();
            }
            module.showOrderFailBtn = function () {
                var href = commonModule.rewriteUrlMoreArea("/NewOrder/OrderFail");
                //不关闭现有弹框
                var showOrderFailDailg = layer.open({
                    type: 2,
                    title: false, //不显示标题
                    content: href,
                    area: ['1200px', '70%'], //宽高
                    skin: 'adialog-Shops-skin',
                    btn: false
                });
            }
        }
        else if (uprate_type == "exception_order") {
            var html = '<div class="newCommonDailog" style="width:520px">';
            html += '<div class="newCommonDailog-main">';
            html += '<span style="font-size:18px;font-weight:700;margin:0 0 20px 0;color:#000;">打印异常提示</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;text-align:center;">您已选中<b>' + parameters.selectNum + '</b>笔订单，其中包含<b>' + parameters.logicOrderIds.length + '</b>笔异常订单，</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;text-align:center;">需采用对应解决方案进行重新取号</span>';
            html += '<div class="newCommonDailog-footer" ><span onclick="waitOrderModule.closeIgnorePrintWarnBtn()" class="layui-layer-btn1" style="width:116px;margin-right:25px;">查看异常订单明细</span><span  onclick="waitOrderModule.sureIgnorePrintWarnBtn()" class="layui-layer-btn1 layui-layer-btn2">忽略异常继续打印其他订单</span></div>';
            html += '</div>'
            html += '</div>'
            var IgnorePrintWarnDailg = layer.open({
                type: 1,
                title: false, //不显示标题
                content: html,
                area: 600, //宽高
                skin: 'adialog-Shops-skin',
                btn: false,
                closeBtn: 0,

            });
            module.closeIgnorePrintWarnBtn = function () {
                //layer.close(IgnorePrintWarnDailg);
                localStorage.setItem('query_logicorderid_input', parameters.logicOrderIds.join(','));
                window.parent.postMessage({ refresh: 'IFrameRenderToExceptionOrder' }, '*');
                //window.open(window.parent.location.href + "&QuickName=ReceiverChangeOrderBtn");
            }
            module.sureIgnorePrintWarnBtn = function () {
                layer.close(IgnorePrintWarnDailg);
                callback();
            }
        }
        else if (uprate_type == "toutiao_plaintext_offline_order") {
            var selectOrderList = parameters.selectOrderList || [];
            var needignorelogicOrderIds = []; // 需要过滤的订单，为空就是不过滤打印全部
            // 过滤掉ExtField2为空的订单
            selectOrderList.forEach(function (item) {
                if (item.ExtField2 == '') {
                    needignorelogicOrderIds.push(item.LogicOrderId);
                }
            });
            var html = '';
            html += '<div class="wu-flex wu-column wu-f14 wu-c09">';
            html += '<div class="wu-mB12">您勾选的订单包含明文线下单，应抖店平台要求，仅在<span class="wu-color-a">抖店分区通过关联订单方式创建的密文线下单</span>才可使用抖店快递单模板打单发货。您也可以选择其他平台快递单模板进行打印。</div>';
            html += '<span id="create_ciphertext_offline_order" class="wu-color-a wu-operate wu-mB4">前往创建密文线下单</span>';
            html += '<a href="https://m0ij9216j9.feishu.cn/wiki/Mh7kw6igMihR5akTtjIcSiiwnTd#share-CpoBdSxQtoWI5nx0PxYcwYbGnOo" target="_blank" class="wu-color-a wu-operate">查看电子面单开通教程</a>';
            html += '</div>';
            layer.open({
                type: 1,
                title: '打印快递单提示',
                content: html,
                area: '560px', // 宽高
                skin: 'wu-dailog',
                success: function () {
                    // 点击“前往创建密文线下单”，弹出“快捷创建线下单”弹窗并进入“关联订单”tab
                    $("#create_ciphertext_offline_order").on("click", function () {
                        layer.closeAll();
                        OfflineOrderImportType = '2';
                        quicklyCreateOfflineOrderDailog();
                    });
                },
                btn: ['过滤此类明文线下单，继续打印', '取消打印'],
                btn1: function (index) {
                    layer.close(index);
                    callback(needignorelogicOrderIds);
                },
                btn2: function (index) {
                    layer.close(index);
                },
            });
        }
        else if (uprate_type == "toutiao_ciphertext_offline_order") {
            layer.open({
                type: 1,
                title: '打印快递单提示',
                content: '<div class="wu-f14 wu-c09">您勾选的订单包含抖店密文线下单，该类订单仅可使用抖店快递单模板打单发货。</div>',
                area: '560px', // 宽高
                skin: 'wu-dailog',
                success: function () { },
                btn: ['过滤此类密文线下单，继续打印', '取消打印'],
                btn1: function (index) {
                    layer.close(index);
                    callback();
                },
                btn2: function (index) {
                    layer.close(index);
                },
            });
        }
        else if (uprate_type == "toutiao_plaintext_offline_order_no_toutiao_template") {
            var selectOrderList = parameters.selectOrderList || [];
            layer.open({
                type: 1,
                title: '打印快递单提示',
                content: '<div class="wu-f14 wu-c09">当前选中的快递单模板，打印的快递单号将无法回传到抖店店铺后台，请确认是否继续打印。</div>',
                area: '560px', // 宽高
                skin: 'wu-dailog',
                success: function () { },
                btn: ['无需上传抖店后台，继续打印', '取消打印'],
                btn1: function (index) {
                    layer.close(index);
                    expressPrinter.print(selectOrderList, 'Normal');
                },
                btn2: function (index) {
                    layer.close(index);
                },
            });
        }
        else if (uprate_type == "other_plaintext_offline_order_toutiao_template") {
            var html = '';
            html += '<div class="wu-flex wu-column wu-f14 wu-c09">';
            html += '<div class="wu-mB12">您勾选的订单包含明文线下单，应抖店平台要求，仅在<span class="wu-color-a">抖店分区通过关联订单方式创建的密文线下单</span>才可使用抖店快递单模板打单发货。您也可以选择其他平台快递单模板进行打印。</div>';
            html += '<a href="https://m0ij9216j9.feishu.cn/wiki/Mh7kw6igMihR5akTtjIcSiiwnTd#share-CpoBdSxQtoWI5nx0PxYcwYbGnOo" target="_blank" class="wu-color-a wu-operate">查看电子面单开通教程</a>';
            html += '</div>';
            layer.open({
                type: 1,
                title: '打印快递单提示',
                content: html,
                area: '560px', // 宽高
                skin: 'wu-dailog',
                success: function () {

                },
                btn: ['过滤此类明文线下单，继续打印', '取消打印'],
                btn1: function (index) {
                    layer.close(index);
                    callback();
                },
                btn2: function (index) {
                    layer.close(index);
                },
            });
        }
        else if (uprate_type == "toutiao_logistics_sf_order") {
            var selectedOrderTotalCount = parameters.selectedOrderTotalCount;
            var expressCompanyName = parameters.expressCompanyName;
            var logicOrderIdCount = parameters.logicOrderIdCount;
            var html = '';
            html += '<div class="wu-f14 wu-c09">';
            html += '<span>已选择' + selectedOrderTotalCount + '个订单，</span>';
            html += '<span>已选择快递为' + expressCompanyName + '，</span>';
            html += '<span>其中' + logicOrderIdCount + '个订单为顺丰配送订单，是否跳过顺丰配送订单继续处理其他订单？</span>';
            html += '</div>';
            layer.open({
                type: 1,
                title: '打印快递单提示',
                content: html,
                area: '560px', // 宽高
                skin: 'wu-dailog',
                success: function () { },
                btn: ['跳过，继续', '取消'],
                btn1: function (index) {
                    layer.close(index);
                    callback();
                },
                btn2: function (index) {
                    layer.close(index);
                },
            });
        }
        else if (uprate_type == "toutiao_logistics_jd_order") {
            var selectedOrderTotalCount = parameters.selectedOrderTotalCount;
            var expressCompanyName = parameters.expressCompanyName;
            var logicOrderIdCount = parameters.logicOrderIdCount;
            var html = '';
            html += '<div class="wu-f14 wu-c09">';
            html += '<span>已选择' + selectedOrderTotalCount + '个订单，</span>';
            html += '<span>已选择快递为' + expressCompanyName + '，</span>';
            html += '<span>其中' + logicOrderIdCount + '个订单为京东配送订单，是否跳过京东配送订单继续处理其他订单？</span>';
            html += '</div>';
            layer.open({
                type: 1,
                title: '打印快递单提示',
                content: html,
                area: '560px', // 宽高
                skin: 'wu-dailog',
                success: function () { },
                btn: ['跳过，继续', '取消'],
                btn1: function (index) {
                    layer.close(index);
                    callback();
                },
                btn2: function (index) {
                    layer.close(index);
                },
            });
        }
        else if (uprate_type == "toutiao_logistics_sf_and_jd_order") {
            var selectedOrderTotalCount = parameters.selectedOrderTotalCount;
            var expressCompanyName = parameters.expressCompanyName;
            var SFLogicOrderIdCount = parameters.SFLogicOrderIdCount;
            var JDLogicOrderIdCount = parameters.JDLogicOrderIdCount;
            var html = '';
            html += '<div class="wu-f14 wu-c09">';
            html += '<span>已选择' + selectedOrderTotalCount + '个订单，</span>';
            html += '<span>已选择快递为' + expressCompanyName + '，</span>';
            html += '<span>其中' + SFLogicOrderIdCount + '个订单为顺丰配送订单，</span>';
            html += '<span>' + JDLogicOrderIdCount + '个订单为京东配送订单，</span>';
            html += '<span>是否跳过顺丰配送、京东配送订单继续处理其他订单？</span>';
            html += '</div>';
            layer.open({
                type: 1,
                title: '打印快递单提示',
                content: html,
                area: '560px', // 宽高
                skin: 'wu-dailog',
                success: function () { },
                btn: ['跳过，继续', '取消'],
                btn1: function (index) {
                    layer.close(index);
                    callback();
                },
                btn2: function (index) {
                    layer.close(index);
                },
            });
        }
        else if (uprate_type == "toutiao_logistics_sf_and_jd_shipping") {
            var selectedOrderTotalCount = parameters.selectedOrderTotalCount;
            var logicOrderIdCount = parameters.logicOrderIdCount;
            var html = '';
            html += '<div class="wu-f14 wu-c09">';
            html += '<span>已选择' + selectedOrderTotalCount + '个订单，</span>';
            html += '<span>其中' + logicOrderIdCount + '个订单为优质快递配送订单且未使用对应快递发货，是否跳过这部分订单继续处理其他订单？</span>';
            html += '</div>';
            layer.open({
                type: 1,
                title: '发货异常提示',
                content: html,
                area: '560px', // 宽高
                skin: 'wu-dailog',
                success: function () { },
                btn: ['跳过，继续', '取消'],
                btn1: function (index) {
                    layer.close(index);
                    callback();
                },
                btn2: function (index) {
                    layer.close(index);
                },
            });
        }
        else if (uprate_type == "xhstemp_uprate") {
            var selectPtOrder = parameters.selectPtOrder || [];
            var selectAllOrder = parameters.selectAllOrder || [];
            var needignorelogicOrderIds = [];   //需要过滤的订单，为空就是不过滤打印全部
            
            //获取开通链接
            var model = {
                templateId: 0,
                caiNiaoAuthInfoId: 0,
                authSourceType: 0,
                templateTypeShort: 'XiaoHongShu'
            };
            var openUrl = "#";
            templateSetCommonModule.LoadCreateWaybillAuthUrl(model, false, function (rsp) {
                openUrl = rsp.Data.redirectAuthUrl || "#";
            });

            //弹框
            if (selectPtOrder.length != selectAllOrder.length) {
                //部分是小红书订单需要过滤处理
                //var html = '<div class="newCommonDailog" style="width:620px">';
                //html += '<div class="newCommonDailog-main">';
                //html += '<span style="font-size:18px;font-weight:700;margin:0 0 20px 0;color:#000;">提示</span>';
                //html += '<span style="font-size:16px;color:#666;line-height:25px;">您勾选订单包含小红书订单，应小红书平台要求，请使用小红书电子面单进行打单发货</span>';
                //html += '<span style="font-size:16px;color:#666;line-height:25px;"><a href="' + openUrl + '" target="_blank">开通小红书面单</a></span>';
                //html += '<div class="newCommonDailog-footer recommendedexpress-btns" >';
                //html += '<span onclick="waitOrderModule.closeWarnDailgBtn()" class="layui-layer-btn1" style="margin-bottom:15px;">取消打印</span>';
                //html += '<span onclick="waitOrderModule.sureIgnorePrintWarnBtn()" class="layui-layer-btn1">过滤小红书订单，继续打印</span>';
                //html += '</div></div></div>'
                //var IgnorePrintWarnDailg = layer.open({
                //    type: 1,
                //    title: false, //不显示标题
                //    content: html,
                //    area: 550, //宽高
                //    skin: 'adialog-Shops-skin',
                //    btn: false,
                //});
                //module.closeWarnDailgBtn = function () {
                //    layer.close(IgnorePrintWarnDailg);
                //}
                //module.sureIgnorePrintWarnBtn = function () {
                //    callback(needignorelogicOrderIds);
                //}
                for (var i = 0; i < selectPtOrder.length; i++) {
                    if (needignorelogicOrderIds.indexOf(selectPtOrder[i].LogicOrderId) == -1)
                        needignorelogicOrderIds.push(selectPtOrder[i].LogicOrderId);
                }
                var html = "";
                html += '<div class="wu-f14 wu-c09">';
                html += '<div>';
                html += '<span>您勾选订单包含小红书订单，应小红书平台要求，请使用<span class="wu-color-a">小红书电子面单</span>进行打单发货</span>';
                html += '</div>';
                html += '<div class="wu-mT16">';
                html += '<a href="' + openUrl + '" target="_blank" class="wu-color-a wu-operate">开通小红书面单</a>';
                //html += '<a style="color:#3aadff" href="https://s.kwaixiaodian.com/zone-origin/express/electronic-sheet" target="_blank" style="color: blue;font-size:13px;">立即开通</a>'
                html += '</div>';
                html += '</div>';
                var index = layer.open({
                    type: 1,
                    title: "打印快递单提示",
                    content: html,
                    area: '480px', //宽高
                    btn: ['过滤小红书订单，继续打印', '取消'],
                    skin: 'wu-dailog',
                    btn1: function (index) {
                        layer.close(index);
                        callback(needignorelogicOrderIds);
                    },
                    btn2: function (index) {
                        layer.close(index);
                    }
                });
            }
            else {
                //全部是小红书的订单，需要弹出公告
                //var html = '<div class="newCommonDailog" style="width:620px">';
                //html += '<div class="newCommonDailog-main">';
                //html += '<span style="font-size:18px;font-weight:700;margin:0 0 20px 0;color:#000;">提示</span>';
                //html += '<span style="font-size:16px;color:#666;line-height:25px;">小红书电子面单上线通知</span>';
                //html += '<span style="font-size:16px;color:#666;line-height:25px;">【重要通知】平台近期将全面切换小红书电子面单，请您务必在2023-10-14前切换，逾期未切换将无法正常打单发货，如有疑问可连续系统客服咨询。</span>';
                //html += '<span style="font-size:16px;color:#666;line-height:25px;"><a href="https://www.dgjapp.com/newHelpsShow.html?id=1695794138878" target="_blank">开通教程</a>  <a href="' + openUrl + '" target="_blank">立即开通</a></span>';
                //html += '<div class="newCommonDailog-footer recommendedexpress-btns" >';
                //html += '<span onclick="waitOrderModule.sureIgnorePrintWarnBtn()" class="layui-layer-btn1" style="margin-bottom:15px;">继续打印</span>';
                //html += '<a class="layui-layer-btn1" style="margin-bottom:15px;" href="' + openUrl +'" target="_blank">立即开通</span>';
                //html += '</div></div></div>'

                //var IgnorePrintWarnDailg = layer.open({
                //    type: 1,
                //    title: false, //不显示标题
                //    content: html,
                //    area: 550, //宽高
                //    skin: 'adialog-Shops-skin',
                //    btn: false,
                //});
                //module.sureIgnorePrintWarnBtn = function () {
                //    callback(needignorelogicOrderIds);
                //}
                templateSetCommonModule.CheckPlatformForceUseTemp('XiaoHongShu', false, function (isForceStart, forceTime) {
                    if (isForceStart == true) {
                        var html = "";
                        html += '<div class="wu-f14 wu-c09">';
                        html += '<div class="wu-alert wu-warning">';
                        html += '<i class="iconfont icon-a-error-circle-filled1x"></i>';
                        html += '<span class="wu-alert-title">小红书平台公告</span>';
                        html += '</div>';
                        html += '<div class="wu-mT16">';
                        html += '<span>应小红书平台要求，为确保消费者和商家的信息安全，请使用<span class="wu-color-a">小红书电子面单</span>进行打单发货。</span>';
                        html += '</div>';
                        html += '<div class="wu-mT16">';
                        html += '<a href="https://school.xiaohongshu.com/helper/detail/1952?entry=00020002&jumpFrom=ark" target="_blank" class="wu-color-a wu-operate">平台公告</a>';
                        html += '<a href="https://www.dgjapp.com/newHelpsShow.html?id=1695794138878" target="_blank" class="wu-color-a wu-operate wu-mL8">开通教程</a>';
                        html += '</div>';
                        html += '</div>';
                        var index = layer.open({
                            type: 1,
                            title: "打印快递单提示",
                            content: html,
                            area: '480px', //宽高
                            btn: ['立即开通'],
                            skin: 'wu-dailog',
                            yes: function () {
                                common.OpenNewTab(openUrl);
                            }
                        });
                    }
                    else {
                        var html = "";
                        html += '<div class="wu-f14 wu-c09">';
                        html += '<div class="wu-alert wu-warning">';
                        html += '<i class="iconfont icon-a-error-circle-filled1x"></i>';
                        html += '<span class="wu-alert-title">小红书电子面单上线通知</span>';
                        html += '</div>';
                        html += '<div class="wu-mT16">';
                        html += '<span>【重要通知】平台近期将全面切换<span class="wu-weight600">小红书电子面单</span>，请您务必在<span class="wu-weight600">2023-10-16</span>前切换，逾期未切换将无法正常打单发货，如有疑问可联系客服咨询。</span>';
                        html += '</div>';
                        html += '<div class="wu-mT16">';
                        html += '<a href="https://www.dgjapp.com/newHelpsShow.html?id=1695794138878" target="_blank" class="wu-color-a wu-operate">开通教程</a>';
                        html += '<a href="' + openUrl + '" target="_blank" class="wu-color-a wu-operate wu-mL8">立即开通</a>';
                        html += '</div>';
                        html += '</div>';
                        var index = layer.open({
                            type: 1,
                            title: "打印快递单提示",
                            content: html,
                            area: '480px', //宽高
                            btn: ['继续打印'],
                            skin: 'wu-dailog',
                            yes: function () {
                                layer.close(index);
                                callback(needignorelogicOrderIds, uprate_type);
                            }
                        });
                    }
                });
            }
        }
        else if (uprate_type == "wxvideotemp_uprate") {
            var selectPtOrder = parameters.selectPtOrder || [];
            var selectAllOrder = parameters.selectAllOrder || [];
            var needignorelogicOrderIds = [];   //需要过滤的订单，为空就是不过滤打印全部

            var platformType = "WxVideo";
            var platformTypeName = "视频号";
            //获取开通链接
            var model = {
                templateId: 0,
                caiNiaoAuthInfoId: 0,
                authSourceType: 0,
                templateTypeShort: platformType
            };
            var openUrl = "#";
            templateSetCommonModule.LoadCreateWaybillAuthUrl(model, false, function (rsp) {
                openUrl = rsp.Data.redirectAuthUrl || "#";
            });
            //开通教程链接
            var courseUrl = "https://www.yuque.com/caixiangmiao/ckbbwt/pd6ym0ng4ooyyazw?singleDoc#";
            //平台公告链接
            var gonggaoUrl = "https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/4jGNh29UWTTE5Ze4";

            //弹框
            if (selectPtOrder.length != selectAllOrder.length) {
                //部分是订单需要过滤处理
                for (var i = 0; i < selectPtOrder.length; i++) {
                    if (needignorelogicOrderIds.indexOf(selectPtOrder[i].LogicOrderId) == -1)
                        needignorelogicOrderIds.push(selectPtOrder[i].LogicOrderId);
                }
                var html = "";
                html += '<div class="wu-f14 wu-c09">';
                html += '<div>';
                html += '<span>您勾选订单包含' + platformTypeName + '订单，应' + platformTypeName + '平台要求，请使用<span class="wu-color-a">' + platformTypeName + '电子面单</span>进行打单发货</span>';
                html += '</div>';
                html += '<div class="wu-mT16">';
                html += '<a href="' + courseUrl + '" target="_blank" class="wu-color-a wu-operate">开通教程</a>';
                //html += '<a style="color:#3aadff" href="" target="_blank" style="color: blue;font-size:13px;">立即开通</a>'
                html += '</div>';
                html += '</div>';
                var index = layer.open({
                    type: 1,
                    title: "打印快递单提示",
                    content: html,
                    area: '480px', //宽高
                    btn: ['过滤' + platformTypeName + '订单，继续打印', '取消打印'],
                    skin: 'wu-dailog',
                    yes: function () {
                        layer.close(index);
                        callback(needignorelogicOrderIds);
                    },
                    btn2: function () {
                        layer.close(index);
                    }
                });
            }
            else {
                //全部是的订单，需要弹出公告
                templateSetCommonModule.CheckPlatformForceUseTemp('WxVideo', false, function (isForceStart, forceTime) {
                    if (isForceStart == true) {
                        var html = "";
                        html += '<div class="wu-f14 wu-c09">';
                        html += '<div class="wu-alert wu-warning">';
                        html += '<i class="iconfont icon-a-error-circle-filled1x"></i>';
                        html += '<span class="wu-alert-title">' + platformTypeName + '平台公告</span>';
                        html += '</div>';
                        html += '<div class="wu-mT16">';
                        html += '<span>应' + platformTypeName + '平台要求，为确保消费者和商家的信息安全，请使用<span class="wu-color-a">' + platformTypeName + '电子面单</span>进行打单发货。</span>';
                        html += '</div>';
                        html += '<div class="wu-mT16">';
                        html += '<a href="' + gonggaoUrl + '" target="_blank" class="wu-color-a wu-operate">平台公告</a>';
                        html += '<a href="' + courseUrl + '" target="_blank" class="wu-color-a wu-operate wu-mL8">开通教程</a>';
                        html += '</div>';
                        html += '</div>';
                        var index = layer.open({
                            type: 1,
                            title: "打印快递单提示",
                            content: html,
                            area: '480px', //宽高
                            //btn: ['立即开通'],
                            skin: 'wu-dailog',
                            yes: function () {
                                common.OpenNewTab(openUrl);
                            }
                        });
                    }
                    else {
                        var html = "";
                        html += '<div class="wu-f14 wu-c09">';
                        html += '<div class="wu-alert wu-warning">';
                        html += '<i class="iconfont icon-a-error-circle-filled1x"></i>';
                        html += '<span class="wu-alert-title">' + platformTypeName + '电子面单上线通知</span>';
                        html += '</div>';
                        html += '<div class="wu-mT16">';
                        html += '<span>【重要通知】根据' + platformTypeName + '平台要求：<span class="wu-weight600">2023-11-20</span>开始需使用' + platformTypeName + '电子面单打单发货，如未使用则无法打印发货，请您务必提前开通' + platformTypeName + '电子面单，避免影响代发。</span>';
                        html += '</div>';
                        html += '<div class="wu-mT16">';
                        html += '<a href="' + gonggaoUrl + '" target="_blank" class="wu-color-a wu-operate">平台公告</a>';
                        html += '<a href="' + courseUrl + '" target="_blank" class="wu-color-a wu-operate wu-mL8">开通教程</a>';
                        html += '</div>';
                        html += '</div>';
                        var index = layer.open({
                            type: 1,
                            title: "打印快递单提示",
                            content: html,
                            area: '480px', //宽高
                            btn: ['继续打印'],
                            skin: 'wu-dailog',
                            yes: function () {
                                layer.close(index);
                                callback(needignorelogicOrderIds, uprate_type);
                            }
                        });
                    }
                });
            }
        }
        else if (uprate_type == "kuaishou_sf_free_shipping") {
            var text = "您选中的订单中包含快手顺丰包邮订单，应平台要求，此类订单请用顺丰快递打单发货，否则会发货失败。是否忽略快手顺丰包邮订单继续打印其他订单。";
            var btns = ['忽略，继续打印', '取消'];
            var allNot = false;
            // 全部不符合条件
            if (parameters.selectNum == parameters.logicOrderIds.length) {
                text = "您选中的订单为快手顺丰包邮订单，应平台要求，此类订单请用顺丰快递打单发货，否则会发货失败。";
                btns = ['取消'];
                allNot = true;
            }
            var html = '<div class="newCommonDailog" style="width:520px">';
            html += '<div class="newCommonDailog-main">';
            html += '<span style="font-size:18px;font-weight:700;margin:0 0 20px 0;color:#000;">提示</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;text-align:left;">' + text + '</span>';
            html += '</div>'
            html += '</div>'
            var IgnorePrintWarnDailg = layer.open({
                type: 1,
                title: false, //不显示标题
                content: html,
                area: 600, //宽高
                skin: 'adialog-Shops-skin',
                btn: btns,
                closeBtn: 1,
                yes: function () {
                    layer.close(IgnorePrintWarnDailg);
                    if (!allNot)
                        callback();
                },
                btn2: function () {
                    layer.close(IgnorePrintWarnDailg);
                }
            });
        }
        else if (uprate_type === "taobao_sf_free_or_door_shipping" || uprate_type === "taobao_sf_free_or_door_shipping_send") {
            var isSend = uprate_type === "taobao_sf_free_or_door_shipping_send";
            var selectOrders = parameters.selectOrders || [];
            var logicOrderIds = parameters.logicOrderIds || [];
            var text = "已选择" + (isSend ? selectOrders.length : parameters.selectNum) + "个订单，其中包含" + logicOrderIds.length + "个订单属于淘宝顺丰包邮/上门订单，未按平台要求选择指定快递发货，可能引起赔付问题，是否跳过顺丰包邮订单继续";
            text += isSend ? "发货?" : "打印?";
            var btns = ['跳过，继续' + (isSend ? '发货' : '打印'), '忽略，继续' + (isSend ? '发货' : '打印')];
            var allNot = false;
            var html = '<div class="newCommonDailog" style="width:520px">';
            html += '<div class="newCommonDailog-main">';
            html += '<span style="font-size:18px;font-weight:700;margin:0 0 20px 0;color:#000;">提示</span>';
            html += '<span style="font-size:16px;color:#666;line-height:25px;text-align:left;">'+text+'</span>';
            html += '</div>'
            html += '</div>'
            var IgnorePrintWarnDailg = layer.open({
                type: 1,
                title: false, //不显示标题
                content: html,
                area: 600, //宽高
                skin: 'adialog-Shops-skin',
                btn: btns,
                closeBtn: 1,
                yes: function () {
                    layer.close(IgnorePrintWarnDailg);
                    if (!allNot)
                        callback(false);
                },
                btn2: function () {
                    layer.close(IgnorePrintWarnDailg);
                    callback(true);
                }
            });
    }
        else {
            callback();
        }
    }
    module.GetBaseProductSetting = function (_this, keyName, callback) {
        var orders = orderTableBuilder.getSelections();
        var unrelatedList = [];
        // 已选订单列表(存缓存，后续回显)
        var selectOrderList = [];
        if (orders.length == 0) {
            layer.msg('请选择订单')
            return
        }

        for (var i = 0; i < orders.length; i++) {
            var obj = {
                Id: orders[i].Id
            }
            selectOrderList.push(obj);
            // 过滤线下单
            //if (!orders[i].IsOfflineOrder) {
            //    for (var j = 0; j < orders[i].SubOrders.length; j++) {
            //        var item = orders[i].SubOrders[j];
            //        if (!item.IsRelationBaseProduct) {
            //            unrelatedList.push(item);
            //        }
            //    }
            //}
            // 过滤无商品线下单
            if (!orders[i].IsOfflineNoSku) {
                for (var j = 0; j < orders[i].SubOrders.length; j++) {
                    var item = orders[i].SubOrders[j];
                    if (!item.IsRelationBaseProduct) {
                        unrelatedList.push(item);
                    }
                }
            }
        }
        if (unrelatedList.length) {
            unrelatedList = commonModule.uniqueArray(unrelatedList, "SkuCode");
        } else {
            callback(orders)
            return
        }
        //if (unrelatedList.length == 0) {
        //    var _unrelatedList = localStorage.getItem("unrelatedList");
        //    _unrelatedList = _unrelatedList ? JSON.parse(_unrelatedList) : ''
        //    if (_unrelatedList) {
        //        for (var i = 0; i < orders.length; i++) {
        //            for (var j = 0; j < orders[i].SubOrders.length; j++) {
        //                var item = orders[i].SubOrders[j];
        //                for (var k = 0; k < _unrelatedList.length; k++) {
        //                    var unrelated = _unrelatedList[k];
        //                    if (item.ProductID == unrelated.ProductID) {
        //                        item = unrelated;
        //                    }
        //                }
        //            }
        //        }
        //    }
        //    callback(orders)
        //    return
        //}

        // 是否右侧未关联列表完成回调
        if (window.tarSkuRelationListWrapStatus == 'complete') {
            var _unrelatedList = localStorage.getItem("unrelatedList");
            _unrelatedList = _unrelatedList ? JSON.parse(_unrelatedList) : '';
            if (_unrelatedList) {
                for (var i = 0; i < orders.length; i++) {
                    var row = orders[i];
                    for (var j = 0; j < row.SubOrders.length; j++) {
                        var item = row.SubOrders[j];
                        for (var k = 0; k < _unrelatedList.length; k++) {
                            var unrelated = _unrelatedList[k];
                            if (item.ProductID == unrelated.ProductID) {
                                item = unrelated;
                            }
                        }
                    }
                    row.PrintInfo = printContentFormatSetModule.ForatPrintContent(row);//重新生成打印信息
                }
            }
            localStorage.removeItem("unrelatedList");
            callback(orders)
            delete window.tarSkuRelationListWrapStatus
            return
        } else {
            localStorage.setItem("unrelatedList", JSON.stringify(unrelatedList))
        }
        var titleHtml = '打印/发货拦截提醒';
        // 打印提醒
        if (keyName == 'PrintBeforeShippingWarning') {
            var sumText = unrelatedList.length ? '<span style="color: #DC8715;font-weight:600;padding: 0 4px;">' + unrelatedList.length + '</span>个' : '<span>部分</span>';
            subHtml = '<div class="" id="unrelatedSum" >您所选中的订单中' + sumText + '规格未绑定库存关联，继续操作可能会导致漏记出库数量，请确认下一步操作。</div>';
            titleHtml = '打印/发货拦截提醒';
        }

        var html = '';
        html += '<div class="dialog-wrap" style="color: rgba(0, 0, 0, 0.9);font-size:14px;">'
        html += '<div class="dialog-wrap-title" style="margin-bottom:12px;font-weight:600;" ><i class="iconfont icon-gantan" style="color: #DC8715;margin-right:5px;font-size:18px;"></i>' + titleHtml + '</div>'
        html += '<div class="dialog-wrap-cont" style="margin-bottom: 24px;padding-left:22px;">'
        html += subHtml;
        html += '</div>'
        html += '<div class="dialogBtns flex" style="display: flex;justify-content: space-between;align-items: center;padding-left:22px;">'
        html += '<div class="n-dColor hover" id="BaseProductSettingClose">不再提醒</div>';
        html += '<div class="btnBox flex"> <div class="n-mButton n-sActive" id="BaseProductSettingNext" style="margin-right:12px;">暂不关联，继续操作</div><div id="BaseProductSettingRelation" class="n-mButton">立即关联</div></div></div>'

        commonModule.Ajax({
            url: '/api/BaseProduct/GetBaseProductSetting',
            type: 'GET',
            showMasker: false,
            success: function (rsp) {
                if (rsp.Success) {
                    var _data = rsp.Data;
                    //dialogData.AbnormalOrderTagReminder = _data.AbnormalOrderTagReminder;
                    //dialogData.ExportBeforeReminder = _data.ExportBeforeReminder;
                    //dialogData.isSwitch = _data.OrderCombine;
                    //dialogData.PrintBeforeShippingWarning = _data.PrintBeforeShippingWarning;
                    //dialogData.StockPreparationReminder = _data.StockPreparationReminder;
                    if (!_data[keyName]) {
                        callback()
                        return
                    }
                    // 所选全部已关联
                    if (orders.length && unrelatedList.length == 0) {
                        callback(orders)
                        return
                    }
                    var dialogData = layer.open({
                        type: 1,
                        content: html,
                        id: 'BaseProductSetting',
                        skin: 'n-skin',
                        area: ['560px', 'auto'], //宽高
                        title: false, // 不显示标题栏
                        closeBtn: 0,
                        shadeClose: true, // 点击遮罩关闭层
                        success: function () {
                            $('#BaseProductSettingClose').on('click', function () {
                                layer.close(dialogData);
                                window.open(commonModule.rewriteUrlToMainDomain("/BaseProduct/NewBaseProduct?operateType=onSetUp"), "_blank");
                            })
                            $('#BaseProductSettingNext').on('click', function () {
                                layer.close(dialogData);
                                callback(orders)
                            })
                            $('#BaseProductSettingRelation').on('click', function () {
                                if (unrelatedList.length) {
                                    localStorage.setItem("selectOrderList", JSON.stringify(selectOrderList));
                                }
                                layer.close(dialogData)
                                var operateType = keyName == 'PrintBeforeShippingWarning' ? 'PrintBeforeShippingWarning' : 'order';
                                commonModule.tarSkuRelationList(_this, unrelatedList, operateType)
                                //commonModule.tarSkuRelation()
                            })
                        },
                        cancel: function (index, layero) {

                        }
                    });
                } else {
                    layer.msg(rsp.Message || rsp.Data || '失败');
                }
            }
        })
    }
    module.InitOrderListEvent = function () {
        var platformOrderId = commonModule.getQueryVariable("PlatformOrderId");
        var LastWaybillCode = commonModule.getQueryVariable("LastWaybillCode");
        var LogicOrderId = commonModule.getQueryVariable("LogicOrderId");
        var fromUrl = commonModule.getQueryVariable("fromUrl");
       
        // if (fromUrl != 'AfterSale' && fromUrl !='AliIncludeOrder') {
        //     $("#inputSelectTime").find(".todayDate").get(0).click();
        // }
        if (platformOrderId) {
            $('input[name=PlatformOrderId]').addClass("activeInput").val(platformOrderId);
        }
        if (LastWaybillCode) {
            $('input[name=LastWaybillCode]').addClass("activeInput").val(LastWaybillCode);
        }
        if (LogicOrderId) {
            $('input[name=LogicOrderId]').addClass("activeInput").val(LogicOrderId);
        }
        if (fromUrl == 'globalOrderSearch') { //全局搜索订单跳转
            var formatNumber = function (n) {
                n = n.toString();
                return n[1] ? n : '0' + n;
            };
            var endDate = new Date();
            var endTime = endDate.getFullYear() + "-" + formatNumber(endDate.getMonth() + 1) + "-" + formatNumber(endDate.getDate()) + " 23:59:59";
            //在当前日期上减去45天
            var startDate = new Date(endDate.setDate(endDate.getDate() - 44));
            var startTime = startDate.getFullYear() + "-" + formatNumber(startDate.getMonth() + 1) + "-" + formatNumber(startDate.getDate()) + " 00:00:00";

            initQueryDateBox(startTime, endTime);
            $('#SeachConditions').click();
            window.parent.postMessage({ fromUrl: 'globalOrderSearch' }, '*');
        }
        if (commonModule.CloudPlatformType == "Alibaba" || commonModule.CloudPlatformType == "Pinduoduo" || commonModule.CloudPlatformType == "TouTiao")
            $(".batch-send-many-btn").show();
        else
            $(".batch-send-many-btn").hide();
        showTaobaoMaiCaiDeliveryDailog();
        // 打印快递单按钮
        $(".express-print-btn").click(function (e) {
            // 进入打印逻辑清除商品归一相关缓存
            if (localStorage.getItem("selectOrderList")) {
                localStorage.removeItem("selectOrderList");
            }
            if (localStorage.getItem("unrelatedList")) {
                localStorage.removeItem("unrelatedList");
            }
            // 权限校验
            commonModule.FxPermission(function (p) {
                commonModule.CheckPermission(function (success) {
                    if (success) {
                        thisFunc();
                    }
                    else return;
                }, p.ExpressPrint);
            });
            
            var thisFunc = function () {
                module.GetBaseProductSetting($(this), 'PrintBeforeShippingWarning', function (list) {
                    console.log("打印发货")
                    orderPrintModule.LockButton = $(this);
                    sendLogistic.LockButton = $(this);
                    console.log("打印发货按钮初始化" + sendLogistic.LockButton);

                    //跨境的打印额外处理
                    if (commonModule.IsCrossBorderSite) {
                        //跨境打印处理
                        tkOrderPrintModule.Print();
                        return;
                    }

                    var curTemplate = addTmplInOrderListModule.GetCurrentTemplate();
                    var orders = list;
                    // console.log("orders=====", orders)
                    //根据是否有模板，决定打开添加模板指引，打开指引，不在往下走
                    if (false && newUserGuideCommon.OpenPrintInit()) {
                        $(this).removeAttr('processing'); //移除处理中的标记
                        return;
                    }
                    else if (false && curTemplate == null) {

                        var html = "";
                        html += '<div class="noChooseTemplateDialogs">';
                        html += '<ul class="noChooseTemplate-ul clearfix">';
                        html += $('#ul_staple_tmpls').html();
                        html += '</ul>';
                        html += '</div>';

                        var noChooseTemplat = layer.open({
                            type: 1,
                            title: "请选择快递模板", //不显示标题
                            content: html,
                            area: ['730px'], //宽高
                            btn: null,// ['下一步'],
                            success: function (layero, index) {
                                $(".noChooseTemplate-ul>li> input[type='radio']").hide();
                                $(".noChooseTemplate-ul>li>#sp_add_template").parent().hide();
                                $(".noChooseTemplate-ul>li").each(function () {
                                    var lable = this.lastElementChild;
                                    var forAttr = lable.getAttribute('for');
                                    lable.removeAttribute('for');
                                    this.setAttribute('template_radio_id', forAttr);
                                    $(this).on("click", function () {
                                        $(".noChooseTemplate-ul>li").removeClass("active");
                                        var radio_id = $(this).attr('template_radio_id');
                                        $('#' + radio_id).click(); //选中模板
                                        $(this).addClass("active");
                                        //直接触发打印
                                        layer.close(noChooseTemplat);
                                        $(".express-print-btn").click();
                                    });
                                });
                            },
                            yes: function () {
                                if (!$(".noChooseTemplate-ul>li").hasClass("active")) {
                                    layer.msg("请选择快递模板！")
                                    return;
                                }
                                $(".express-print-btn").click();
                                layer.close(noChooseTemplat);

                            }
                        });

                        $(this).removeAttr('processing'); //移除处理中的标记
                        return;
                    }
                    var orders = orderTableBuilder.getSelections();

                    var touTiaoSaleShopWarehouseData = [];
                    var filterWarehouseId = function (list) {
                        var uniqueList = [];
                        list.forEach(function (item) {
                            if (!uniqueList.includes(item)) {
                                uniqueList.push(item);
                            }
                        });
                        return uniqueList;
                    }
                    orders.forEach(function (value) {
                        if (value.PlatformType == 'TouTiaoSaleShop' && value.WarehouseId > 0) {
                            touTiaoSaleShopWarehouseData.push(value.WarehouseId);
                        }
                    });
                    if (touTiaoSaleShopWarehouseData.length > 0) {
                        var touTiaoSaleShopWarehouseList = filterWarehouseId(touTiaoSaleShopWarehouseData);
                        if (touTiaoSaleShopWarehouseList.length > 1) {
                            wuFormModule.wu_toast({ type: 3, content: '不同仓库的订单请分批打印' });
                            return false;
                        }
                    }

                    if (parseInt(addTmplInOrderListModule.GetCurrentBalance()) < orders.length) {
                        common.ProcessVersionOrderCountErrorCode("ORDER_COUNT", parseInt(addTmplInOrderListModule.GetCurrentBalance()))
                        return;
                    }

                    //1.预检查处理
                    module.PrintPreCheckHandle(orders, curTemplate, doPrint);

                    var cateOrderItem = getCategorizedOrders(orders, curTemplate);
                    var SFLogicOrderIds = cateOrderItem.toutiaoSFLogisticsOrderLogicOrderIds;
                    var JDLogicOrderIds = cateOrderItem.toutiaoJDLogisticsOrderLogicOrderIds;
                    var _mergedIds = SFLogicOrderIds.concat(JDLogicOrderIds);
                    var _newOrderList = [];
                    if (orders.length > 0 && _mergedIds && _mergedIds.length > 0) {
                        if (curTemplate.ExpressCompanyCode != 'SF' || curTemplate.ExpressCompanyCode != 'JD') {
                            for (var i = 0; i < orders.length; i++) {
                                if (_mergedIds.indexOf(orders[i].LogicOrderId) == -1) {
                                    _newOrderList.push(orders[i]);
                                }
                            }
                        }
                    }
                    //2.进入打印逻辑
                    function doPrint(os) {
                        console.log('==========提交打印的订单数据============', os);
                        var list = [];
                        if (_newOrderList && _newOrderList.length > 0) {
                            list = _newOrderList;
                        } else {
                            list = os;
                        }
                        expressPrinter.print(list, 'Normal');
                        // 进入打印逻辑清除商品归一相关缓存
                        localStorage.removeItem("selectOrderList");
                        localStorage.removeItem("unrelatedList");
                        //$("#SeachConditions").trigger("click");
                    }
                })
                console.log("打印发货")
                return
            }
        });

        //发货单
        $(".fahuo-print-btns").click(function (e) {
            //权限校验
            commonModule.FxPermission(function (p) {
                commonModule.CheckPermission(function (success) {
                    if (success) {
                        thisFunc();
                    }
                    else return;
                }, p.SendGoodsPrint);
            });

            var thisFunc = function () {
                sendGoodTemplate.print();
            }

        });

        $(function () {
            // 混合多运单号发货处理方式：0：先发单个单号，再弹窗发多运单号，1：全部使用新单号发货，空：每次弹窗确认
            var manyCodeSendAway = "/ErpWeb/SetInfo/ManyCodeSendAway";
            var manyCodeSendConfig = "/ErpWeb/SetInfo/ManyCodeSendConfig";
            commonModule.Ajax({
                url: "/Common/LoadCommonSetting",
                data: { settingKey: manyCodeSendAway },
                type: "POST",
                loading: false,
                success: function (rsp) {
                    if (!rsp.Success)
                        return;
                    commonModule.ManyCodeSendAway = rsp.Data || "";
                    // 开启多单号回传配置
                    if (rsp.Data == "0") {
                        commonModule.Ajax({
                            url: "/Common/LoadCommonSetting",
                            data: { settingKey: manyCodeSendConfig },
                            type: "POST",
                            loading: false,
                            success: function (res) {
                                if (!res.Success)
                                    return; 
                                commonModule.manyCodeSendConfigData = JSON.parse(res.Data);
                            }
                        });
                    }
                }
            });

        });

        // 批量发货
        $(".batch-send-btn").click(function (e) {
            // 权限校验
            commonModule.FxPermission(function (p) {
                commonModule.CheckPermission(function (success) {
                    if (success) {
                        thisFunc();
                    }
                    else return;
                }, p.OnlineSend);
            });

            var thisFunc = function () {
                console.log("批量发货设置按钮：" + $(this))
                sendLogistic.LockButton = $(this);

                //跨境的移入已交运额外处理
                if (commonModule.IsCrossBorderSite) {
                     //获取勾选的订单
                    var orders = orderTableBuilder.getSelections();
                    if (orders.length == 0) {
                        layer.msg("请选择订单!");
                        return;
                    }

                    var isFlag = true;
                    for (var index = 0; index < orders.length; index++) {
                        var el = orders[index];
                        if (el.ShippingType == "TIKTOK" && el.PrintState == 0) {
                            isFlag = false;
                            break;
                        }
                    }
                    if (!isFlag) {
                        layer.msg("未打印快递单的订单不允许交运/发货，请检查并打印快递单!");
                        return;
                    }

                    commonModule.LoadingMsg('加载快递公司中...');
                    //跨境移入已交运处理
                    setTimeout(function(){
                        tkOrderPrintModule.Send();
                    }, 200);
                    return;
                }

                if (commonModule.IsRepairSendHistory) {
                    layer.confirm("当前发货只修复发货记录，不会调接口再平台发货，请确认是否修复发货记录？", { skin: 'wu-dailog' }, function (index) {
                        layer.close(index);
                        sendLogistic.send();
                    });
                }
                else {
                    sendLogistic.BatchSendOrder(undefined, undefined, undefined, undefined, undefined, undefined, undefined);

                    //sendLogistic.send();
                }
            }
        });
        $(".batch-edit-logistics-btn").click(function (e) {
            //获取勾选的订单
            var orders = orderTableBuilder.getSelections();
            console.log("orders=====", orders);
            
            if (orders.length == 0) {
                layer.msg("请选择订单!");
                return;
            }
            var tkOrderList = [];
            var notTkOrderList = [];
            var flag = true;
            for (var i = 0; i < orders.length; i++) {
                var order = orders[i];

                // 只要有一条不符合，直接跳出循环
                if (!(order.PlatformStatus == 'sended' || (order.PlatformStatus == 'waitsellersend' && order.LogisticStatus == 2))) {
                    flag = false;
                    break;
                }

                if (order.ShippingType == 'SELLER') { // TIKTOK：平台订单 SELLER：第三方物流订单
                    notTkOrderList.push(order);
                } else if (order.ShippingType == 'TIKTOK') {
                    tkOrderList.push(order);
                }
            }
            if (!flag) {
                layer.msg("请勾选待发货、已交运、已发货订单!");
                return;
            }
            if (notTkOrderList.length == 0) {
                layer.msg("平台物流订单不能修改物流信息!");
                return;
            }

            if (notTkOrderList.length > 0 && tkOrderList.length > 0) {
                var html = '<div style="font-size: 14px; color: #3D3D3D;">所选订单包含平台物流订单，该类订单不能修改物流信息，是否过滤掉并进行下一步操作</div>';
                var indexClose = layer.open(
                    {
                        type: 1,
                        title: '系统提示',
                        //closeBtn: 1,
                        btn: ['过滤并进行下一步', '取消'],
                        //shadeClose: true,
                        area: ['560px','180px'],
                        content: html,
                        skin: 'n-skin',
                        success: function (layero, index) {

                        },
                        btn1: function () {
                            layer.close(indexClose);
                            commonModule.LoadingMsg('加载快递公司中...');
                            setTimeout(function(){
                                tkOrderPrintModule.shipmentOrSend(notTkOrderList,'editLogistics');
                            }, 200);

                        },
                        btn2: function () {
                            layer.close(indexClose);
                        },
                    }
                );
                return;
            }
            commonModule.LoadingMsg('加载快递公司中...');
            setTimeout(function(){
                tkOrderPrintModule.shipmentOrSend(notTkOrderList,'editLogistics');
            }, 200);
        });

        // 批量上传多运单号
        $(".batch-send-many-btn").click(function () {
            // 权限校验
            commonModule.FxPermission(function (p) {
                commonModule.CheckPermission(function (success) {
                    if (success) {
                        thisFunc();
                    }
                    else return;
                }, p.AppendPack);
            });

            var thisFunc = function () {
                console.log("批量上传多运单号设置按钮：" + $(this))
                sendLogistic.LockButton = $(this);
                var platformtype = $("#statistic_btnWrap").attr("platformtype");
                if (platformtype != "Pinduoduo") {
                    if (commonModule.IsRepairSendHistory) {
                        layer.confirm("当前发货只修复发货记录，不会调接口再平台发货，请确认是否修复发货记录？", { skin: 'wu-dailog' }, function (index) {
                            layer.close(index);
                            module.BatchSendManyV2();
                        });
                    }
                    else {
                        module.BatchSendManyV2();
                    }
                } else {
                    module.batchPinduoduoSendMany();
                }
            }
        });

        //一单多包
        $(".print-one2many").click(function (e) {
            // 权限校验
            commonModule.FxPermission(function (p) {
                commonModule.CheckPermission(function (success) {
                    if (success) {
                        thisFunc();
                    }
                    else return;
                }, p.ExpressPrint);
            });

            var thisFunc = function () {
                var curTemplate = addTmplInOrderListModule.GetCurrentTemplate();
                var orders = orderTableBuilder.getSelections();
                var platformtypes = [];
                orders.forEach(function (item) {
                    platformtypes.push(item.PlatformType);
                });
                if (platformtypes.includes("TouTiaoSaleShop")) {
                    layer.alert("即时零售订单暂不支持此功能，请取消选择后重试", { title: '提示', skin: 'wu-dailog' });
                    return;
                }

                //1.预检查处理
                module.PrintPreCheckHandle(orders, curTemplate, doPrint);

                //2.进入打印逻辑
                function doPrint(os) {
                    expressPrinter.print(os, 'OneToMany');
                }
            }

        });

        //以下是拼多多回传单号--------------------------------------

        module.batchPinduoduoSendMany = function (isPreview) {
            var manyWybOrders = [];
            $("#OrderTableList .order-chx").each(function (index, item) {
                if (item.checked && $(item).attr("data-platformtype") == "Pinduoduo") {
                    var pid = $(item).attr("data-pid");
                    $(".productShow-itemInput.orderitem-chx").each(function (cIndex, cItem) {
                        var cPid = $(cItem).attr("data-pid");
                        if (pid == cPid) {
                            if (!cItem.checked) {
                                cItem.click();
                            }
                        }
                    })
                }
            })
            var checkOrders = orderTableBuilder.getSelections(undefined, undefined, undefined, false, true);
            if (checkOrders.length == 0) {
                layer.confirm("请先选择需要上传的订单", { skin: 'wu-dailog' });
                return;
            }
            var template = addTmplInOrderListModule.GetCurrentTemplate()
            if (template == null) {
                layer.alert("请选择快递模板", { skin: 'wu-dailog' });
                return;
            }

            var configs = commonModule.manyCodeSendConfigData;
            for (var i = 0; i < checkOrders.length; i++) {
                var o = checkOrders[i];
                var row = orderTableBuilder.rows[o.Index];
                var tmpOrder = JSON.parse(JSON.stringify(row));
                tmpOrder.WaybillCodes = [];
                tmpOrder.AlreadyAppendPack = 0;
                // 运单号信息
                if (row.WaybillCodes && row.WaybillCodes.length > 0) {
                    for (var j = 0; j < row.WaybillCodes.length; j++) {
                        //底单状态为未打印1或回收失败4
                        if (commonModule.IsRepairSendHistory || row.WaybillCodes[j].Status == undefined || row.WaybillCodes[j].Status == 1 || row.WaybillCodes[j].Status == 4) {
                            tmpOrder.WaybillCodes.push(row.WaybillCodes[j]);
                        }
                        if (row.WaybillCodes[j].SendType == 10) {
                            tmpOrder.AlreadyAppendPack = 1;//已经使用过追加包裹
                        }
                    }
                }

                if (configs && configs.Pinduoduo == false || !configs) {
                    layer.confirm("拼多多平台未开启多单号发货功能，请先开启拼多多多单号发货功能再进行操作", { title: '提示', skin: 'wu-dailog' });
                    return;
                }

                // 拼多多校验
                if (tmpOrder.WaybillCodes.length <= 0) {
                    layer.confirm("拼多多平台的订单【" + row.PlatformOrderId + "】可上传未发货运单号必须大于等于1，请先取消勾选此类订单再进行操作", { title: '提示', skin: 'wu-dailog' });
                    return;
                }
                if ((row.ErpState != 'sended' || row.ErpRefundState == 'REFUND_SUCCESS') && !isPreview) {
                    layer.confirm("拼多多平台的订单【" + row.PlatformOrderId + "】只有已发货状态的订单才能回传多单号，请先取消勾选此类订单再进行操作", { title: '提示', skin: 'wu-dailog' });
                    return;
                }
                //0612：去掉拼多多限制
                //if (tmpOrder.AlreadyAppendPack != undefined && tmpOrder.AlreadyAppendPack == 1) {
                //    layer.confirm("拼多多平台的订单【" + row.PlatformOrderId + "】已使用过回传多单号，不能再次使用，请先取消勾选此类订单再进行操作", { skin: 'wu-dailog' });
                //    return;
                //}

                manyWybOrders.push(tmpOrder);
            }

            var moreCodeTmp = $.templates("#uploadPdd-many-wybcode-tmplV2");

            var manyCodeSendModel = [];

            var checkManyCodeOrders = JSON.parse(JSON.stringify(manyWybOrders));
            checkManyCodeOrders.forEach(function (oItem) {
                var WaybillCodeModels = [];

                // 获取最新的同一批次打印
                var lastNewCode = oItem.WaybillCodes.find(function (wItem) {
                    return wItem.WaybillCode == oItem.LastWaybillCode;
                });

                // 过滤已打印和回收失败的单号
                oItem.WaybillCodes = oItem.WaybillCodes.filter(function (wItem) {
                    return wItem.Status == 1 || wItem.Status == 4 || commonModule.IsRepairSendHistory
                });

                // 获取Items
                var sendProduct = checkOrders.find(function (cItem) {
                    return oItem.Id == cItem.Id;
                });

                // 获取已选择的Items的OrderItemId集合
                var ItemsOrderItemIds = sendProduct.Items.map(function (sItem) {
                    return sItem.OrderItemId - 0
                });

                // 过滤发货未选择的底单商品
                oItem.WaybillCodes.forEach(function (wItem) {
                    wItem.WaybillCodeOrderProducts = wItem.WaybillCodeOrderProducts.filter(function (pItem) {
                        return ItemsOrderItemIds.indexOf(pItem.OrderItemId) > -1
                    });
                });

                // 汇总全部底单商品数据
                var allWaybillCodeOrderProducts = [];
                oItem.WaybillCodes.forEach(function (wItem, wIndex) {
                    if (wItem.WaybillCodeOrderProducts && wItem.WaybillCodeOrderProducts.length > 0) {
                        // MasterId为0 --直接用索引进行分组
                        if (wItem.WaybillCodeOrderProducts[0].MasterId == 0) {
                            wItem.WaybillCodeOrderProducts.forEach(function (aItem) {
                                aItem.MasterId = wIndex;
                            });
                        }

                        wItem.WaybillCodeOrderProducts.forEach(function (aItem) {
                            allWaybillCodeOrderProducts.push(aItem);
                        });
                    }
                });

                oItem.WaybillCodes.forEach(function (wItem) {
                    if (wItem.WaybillCodeOrderProducts && wItem.WaybillCodeOrderProducts.length > 0) {
                        var IsErr = false;
                        var ErrMsg = null;

                        for (var i = 0; i < wItem.WaybillCodeOrderProducts.length; i++) {
                            var orderItemId = wItem.WaybillCodeOrderProducts[i].OrderItemId;
                            var fData = sendProduct.Items.find(function (fItem) {
                                return fItem.OrderItemId == orderItemId;
                            });

                            if (fData) {
                                var Quantity = 0;
                                allWaybillCodeOrderProducts.forEach(function (aItem) {
                                    if (aItem.OrderItemId == orderItemId) {
                                        Quantity += aItem.Quantity
                                    }
                                });

                                if (fData.Quantity < Quantity) {
                                    IsErr = true;
                                    ErrMsg = "发货选择的数量和底单对不上";
                                    break;
                                }
                            } else {
                                IsErr = true;
                                ErrMsg = "发货没选择这个商品";
                                break;
                            }
                        }

                        WaybillCodeModels.push({
                            MasterId: wItem.WaybillCodeOrderProducts[0].MasterId,
                            WaybillCode: wItem.WaybillCode,
                            WaybillCodeOrderProducts: wItem.WaybillCodeOrderProducts,
                            IsErr: IsErr,
                            IsLatestBatchCode: lastNewCode ? lastNewCode.CreateDate == wItem.CreateDate : true,
                            ErrMsg: ErrMsg,
                        })
                    }
                });

                if (WaybillCodeModels.length > 0) {
                    var abnormalOrderList = WaybillCodeModels.filter(function (wItem) {
                        return wItem.IsErr == true && wItem.ErrMsg == "发货选择的数量和底单对不上"
                    });

                    // 如果全是异常数据，则需要保留最新的一个单号进行匹配回显
                    if (abnormalOrderList.length == WaybillCodeModels.length) {
                        WaybillCodeModels[0].IsErr = false;
                        WaybillCodeModels[0].ErrMsg = "";
                    }
                }

                var model = {
                    Id: oItem.Id,
                    PlatformOrderId: oItem.PlatformOrderId,
                    LogicOrderId: oItem.LogicOrderId,
                    WaybillCodeModels: WaybillCodeModels
                }
                manyCodeSendModel.push(model);
            });

            manyWybOrders.forEach(function (item) {
                if (isPreview) {
                    var SubOrders = JSON.parse(JSON.stringify(item.SubOrders));

                    var onlySubOrders = [];
                    SubOrders.forEach(function (item1) {
                        if (!item1.SendedCount || commonModule.IsRepairSendHistory) {
                            item1.SendedCount = 0;
                        }
                        var remainCount = item1.Count - item1.SendedCount;
                        if ((item1.Status == "waitsellersend" && item1.checked && remainCount > 0) || commonModule.IsRepairSendHistory) {
                            onlySubOrders.push({
                                Color: item1.Color,
                                Size: item1.Size,
                                OrderItemId: item1.OrderItemId,
                                OrderItemCode: item1.OrderItemCode,
                                Count: remainCount,
                                ProductImgUrl: item1.ProductImgUrl ? item1.ProductImgUrl : "/Content/images/nopic.gif",
                                ProductID: item1.ProductID,
                                SkuId: item1.SkuId
                            });
                        }
                    });
                }

                // 预览时只保留同批次的运单号
                if (isPreview && manyCodeSendModel.length > 0 && commonModule.IsRepairSendHistory == false) {
                    var findData = manyCodeSendModel.find(function (sItem) {
                        return sItem.Id == item.Id
                    });

                    var checkWaybillCodes = [];
                    
                    findData.WaybillCodeModels.forEach(function (wItem) {
                        if(wItem.IsLatestBatchCode) {
                            checkWaybillCodes.push(wItem.WaybillCode);
                        }
                    });

                    item.WaybillCodes = item.WaybillCodes.filter(function (wItem) {
                        return checkWaybillCodes.indexOf(wItem.WaybillCode) > -1
                    });
                }

                item.WaybillCodes.forEach(function (cItem) {
                    cItem.isSelect = false;
                    cItem.TrackType = "";
                    if (isPreview) {
                        cItem.SelectProductList = [];
                    }

                    // 运单号智能匹配到的包裹内容

                    if (manyCodeSendModel.length > 0) {
                        var findData = manyCodeSendModel.find(function (sItem) {
                            return sItem.Id == item.Id
                        });

                        var wData = findData.WaybillCodeModels.find(function (sItem) {
                            return sItem.WaybillCode == cItem.WaybillCode
                        });

                        if (wData) {
                            if (wData.IsErr == true || wData.IsLatestBatchCode == false) {
                                cItem.isSelect = false;
                                wData.WaybillCodeOrderProducts = [];
                            } else if (isPreview) {
                                // 只有匹配的单号才回显包裹内容
                                wData.WaybillCodeOrderProducts.forEach(function (sItem) {
                                    var subRow = SubOrders.find(function (wItem) {
                                        return wItem.OrderItemId == sItem.OrderItemId
                                    });

                                    if (subRow) {
                                        cItem.isSelect = true;
                                        var Quantity = 1;

                                        var onlyData = SubOrders.find(function (oItem) {
                                            return oItem.OrderItemId == subRow.OrderItemId;
                                        });
                                        
                                        if (onlyData) {
                                            if (onlyData.SendedCount) {
                                                Quantity = onlyData.Count - onlyData.SendedCount;
                                            } else {
                                                Quantity = onlyData.Count;
                                            }
                                        }

                                        // 存在剩余商品数量才需要回显
                                        if (Quantity > 0) {
                                            var QuantitySelect = [];

                                            for (var i = 0; i <= Quantity; i++) {
                                                QuantitySelect.push(i);
                                            }

                                            // 如果底单商品数量大于剩余发货数量则显示剩余发货数量
                                            var ProductData = {
                                                Color: subRow && subRow.Color ? subRow.Color : "",
                                                Size: subRow && subRow.Size ? subRow.Size : "",
                                                ProductID: subRow && subRow.ProductID ? subRow.ProductID : "",
                                                Count: sItem.Quantity > Quantity ? Quantity : sItem.Quantity,
                                                OrderItemId: sItem.OrderItemId,
                                                OrderItemCode: sItem.OrderItemCode,
                                                QuantitySelect: QuantitySelect
                                            }

                                            cItem.SelectProductList.push(ProductData);
                                        }
                                    }
                                });
                            } else {
                                cItem.isSelect = true;
                            }
                        }
                    }

                    if (isPreview) {
                        cItem.AllOrderItemIds = cItem.SelectProductList.map(function (sItem) {
                            return sItem.OrderItemId;
                        });
                        cItem.AddSubOrders = onlySubOrders;
                    }
                });
            });


            var html = moreCodeTmp.render({ manyWybOrders: manyWybOrders, isPreview: isPreview });
            $("#pddMoreCodeDrawer").html(html);

            function handelSelectStatus () {
                var tableTH = $("#many-wybcode-drawer-content .pddCheckAllCodeCheck").length;
                var thActive = $("#many-wybcode-drawer-content .pddCheckAllCodeCheck.activeF").length;
                var thActivep = $("#many-wybcode-drawer-content .pddCheckAllCodeCheck.activeP").length;

                if (tableTH == thActive) {
                    $("#selectAllPddMany_checkbox").removeClass("activeP").addClass("activeF");
                } else if (thActivep || (tableTH > thActive && thActive > 0)) {
                    $("#selectAllPddMany_checkbox").removeClass("activeF").addClass("activeP");
                } else {
                    $("#selectAllPddMany_checkbox").removeClass("activeF activeP");
                }
            }

            // 商品数量联动检验
            function handeProductNumErr(isSelect, id, waybillCodeId) {
                if (isSelect) {
                    $("#package_content" + id + "_" + waybillCodeId).find(".tt_productSub-select-num_error").remove();
                    $("#package_content" + id + "_" + waybillCodeId).find(".tt_productNum").removeClass("num_error-border_color");
                }

                var selectedIds = [];
                $("#package_content" + id + "_" + waybillCodeId + " .package_content-select_item .tt_productSub").each(function () {
                    var val = $(this).attr("data-val");
                    if (val) {
                        selectedIds.push(val - 0);
                    }
                });

                //console.log(selectedIds)

                if (selectedIds.length > 0) {
                    selectedIds.forEach(function (sid) {
                        var errPackageIds = [];
                        var curPackageId = [];
                        $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + sid + '"]').each(function () {
                            var parentId = $(this).parent().parent().parent().attr("id");
                            if (parentId != "package_content" + id + "_" + waybillCodeId) {
                                if (errPackageIds.indexOf(parentId) == -1) {
                                    errPackageIds.push(parentId);
                                }
                            } else {
                                if (curPackageId.indexOf(parentId) == -1) {
                                    curPackageId.push(parentId);
                                }
                            }
                        });

                        //console.log(errPackageIds)

                        if (errPackageIds.length > 0 && curPackageId.length > 0) {
                            if (isSelect) {
                                var cId = "#" + curPackageId[0];
                                var removeProductNum = $(cId).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                var totalProductNum = $(cId).find('.tt_productSub-select_item[data-orderitemid="' + sid + '"]').attr("data-count");
                                var curProductNum = removeProductNum - 0;

                                errPackageIds.forEach(function (item) {
                                    var strs = item.split('package_content')[1].split('_');
                                    var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                                    if (isCheck) {
                                        var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                        curProductNum += (sibProductNum - 0);
                                    }
                                });

                                // 移除--红色预警
                                if (curProductNum - removeProductNum <= totalProductNum) {
                                    errPackageIds.forEach(function (item) {
                                        $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').parent().siblings(".tt_productSub-select-num_error").remove();
                                        $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                                    });
                                }
                            }
                            // 反选--与新增同理
                            else {
                                var cId = "#" + curPackageId[0];
                                var productNum = $(cId).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                var totalProductNum = $(cId).find('.tt_productSub-select_item[data-orderitemid="' + sid + '"]').attr("data-count");
                                var curProductNum = productNum - 0;

                                errPackageIds.forEach(function (item) {
                                    var strs = item.split('package_content')[1].split('_');
                                    var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                                    if (isCheck) {
                                        var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                        curProductNum += (sibProductNum - 0);
                                    }
                                });

                                // 其他包裹里的商品数量不变--并红色预警
                                errPackageIds.forEach(function (item) {
                                    var errSibLength = $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').parent().siblings().length;
                                    var strs = item.split('package_content')[1].split('_');
                                    var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");

                                    // 当前选择的数量是否大于0
                                    var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                    var isOutZero = sibProductNum > 0;

                                    // 是其父元素的唯一子元素则添加错误提醒--避免重复添加
                                    if (errSibLength == 0 && curProductNum > totalProductNum && isCheck && isOutZero && commonModule.IsRepairSendHistory == false) {
                                        var errorHtml = '<span class="tt_productSub-select-num_error">数量有误</span>';
                                        $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').parent().parent().append(errorHtml);
                                        $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').addClass("num_error-border_color");
                                    }
                                });
                            }
                        }
                    });
                }
            }
            function selectSinglePddMany (id, waybillCodeId, platformOrderId, isInit) {
                var isSelect = $("#manyCode-checkbox_" + id + waybillCodeId).hasClass("activeF");
                if (isSelect) {
                    $("#manyCode-checkbox_" + id + waybillCodeId).removeClass("activeF");
                } else {
                    $("#manyCode-checkbox_" + id + waybillCodeId).addClass("activeF");
                }

                var maxPddCheckNum = 30;
                var trackTypeVal = $("#trackType_" + platformOrderId).attr("data-val");
                if (trackTypeVal == 1) {
                    maxPddCheckNum = 30;
                } else if (trackTypeVal == 2) {
                    maxPddCheckNum = 3;
                } else if (trackTypeVal == 3) {
                    maxPddCheckNum = 1;
                }

                var tableTd = $("#moreCodeWrap_tbody" + id + " .n-newCheckbox").length;
                var tdActive = $("#moreCodeWrap_tbody" + id + " .n-newCheckbox.activeF").length;

                if (tdActive > maxPddCheckNum) {
                    $("#manyCode-checkbox_" + id + waybillCodeId).removeClass("activeF");
                    if (!isInit) {
                        layer.msg(showTrackTar(trackTypeVal));
                    }
                    return;
                }

                if (tableTd == tdActive) {
                    $("#moreCodeWrap_table" + id).find(".pddCheckAllCodeCheck").removeClass("activeP").addClass("activeF");
                } else if (tableTd > tdActive && tdActive > 0) {
                    $("#moreCodeWrap_table" + id).find(".pddCheckAllCodeCheck").removeClass("activeF").addClass("activeP");
                } else {
                    $("#moreCodeWrap_table" + id).find(".pddCheckAllCodeCheck").removeClass("activeF activeP");
                }

                handelSelectStatus();
                manyWybOrders.forEach(function (item) {
                    if (item.Id == id) {
                        item.WaybillCodes.forEach(function (cItem) {
                            if (cItem.WaybillCodeId == waybillCodeId) {
                                cItem.isSelect = !isSelect;
                                if (!isSelect) {
                                    cItem.TrackType = trackTypeVal;
                                } else {
                                    cItem.TrackType = "1";
                                }
                            }
                        })
                    }
                })

                if (!isInit && isPreview) {
                    handeProductNumErr(isSelect, id, waybillCodeId);
                }
            }

            if (manyCodeSendModel && manyCodeSendModel.length > 0) {
                manyWybOrders.forEach(function (item) {
                    item.WaybillCodes.forEach(function (cItem) {
                        if (cItem.isSelect) {
                            selectSinglePddMany(item.Id, cItem.WaybillCodeId, item.PlatformOrderId, true);
                        }
                    });
                });
            }

            $("#uploadPdd-many-drawer").addClass("active");
            $("body").css("overflow", "hidden");

            module.closePddManyDrawer = function () {
                $("#uploadPdd-many-drawer").removeClass("active");
                $("body").css("overflow", "auto");
            }

            module.submitPddManyDrawer = function () {
                var noChooseItems = [];  // 没有勾选运单号的集合
                var failManyWybOrders = []; // 勾选运单号未选择包裹集合
                var numfailManyWybOrders = []; // 勾选运单号商品数量错误集合
                var numErrManyWybOrders = []; // 商品数量未全部选择的订单集合

                manyWybOrders.forEach(function (item) {
                    var WaybillCodes = JSON.parse(JSON.stringify(item.WaybillCodes));

                    // 校验是否存在一个以上的运单号勾选
                    var isSelect = WaybillCodes.some(function (cItem) {
                        return cItem.isSelect == true;
                    });

                    if (isSelect && isPreview) {
                        // 有勾选了运单号，则要检验勾选的运单号是否选择了包裹
                        WaybillCodes.forEach(function (cItem) {
                            // 是否勾选了运单号且未未选择包裹内容
                            if (cItem.isSelect && cItem.SelectProductList && cItem.SelectProductList.length == 0) {
                                // 只存没有存过且没有选择包裹的运单号
                                if (failManyWybOrders.indexOf(cItem.WaybillCode) == -1) {
                                    failManyWybOrders.push(cItem.WaybillCode);
                                }
                            }

                            // 是否勾选了运单号且提示包裹数量有误
                            if (cItem.isSelect) {
                                var isErr = $("#package_content" + item.Id + "_" + cItem.WaybillCodeId).find(".tt_productSub-select-num_error").length;
                                if (isErr) {
                                    if (numfailManyWybOrders.indexOf(item.PlatformOrderId) == -1) {
                                        numfailManyWybOrders.push(item.PlatformOrderId);
                                    }
                                }
                            }
                        });

                        // 把所有勾选的运单号过滤出来
                        var checkCodes = WaybillCodes.filter(function (cItem) {
                            return cItem.isSelect == true;
                        });

                        var curSubOrders = checkCodes[0].AddSubOrders;

                        var selectedProductList = []; // 当前订单已选择的包裹内容

                        checkCodes.forEach(function (cItem) {
                            cItem.SelectProductList.forEach(function (sItem) {
                                selectedProductList.push(sItem);
                            });
                        });

                        var filterRepeatCodes = []; // 过滤重复的id, 相同的累计

                        selectedProductList.forEach(function (sItem) {
                            var fIndex = filterRepeatCodes.findIndex(function (fItem) {
                                return fItem.OrderItemId == sItem.OrderItemId
                            });

                            if (fIndex == -1) {
                                filterRepeatCodes.push(sItem)
                            } else {
                                filterRepeatCodes[fIndex].Count += sItem.Count;
                            }
                        });

                        // 判断filterRepeatCodes 是否与 curSubOrders 相等
                        // 先判断是否都选择了全部的商品
                        if (filterRepeatCodes.length == curSubOrders.length) {
                            // 再判断数量是否全部选择
                            var IsNoSame = filterRepeatCodes.some(function (aItem) {
                                return curSubOrders.findIndex(function (sItem) {
                                    return sItem.OrderItemId == aItem.OrderItemId && sItem.Count == aItem.Count
                                }) == -1
                            });

                            if (IsNoSame) {
                                var curData = curSubOrders.find(function (curItem) {
                                    return filterRepeatCodes.findIndex(function (sItem) {
                                        return sItem.OrderItemId == curItem.OrderItemId && sItem.Count == curItem.Count
                                    }) == -1
                                });

                                numErrManyWybOrders.push(curData);
                            }
                        } else {
                            var filterOrderItemIds = filterRepeatCodes.map(function (fItem) {
                                return fItem.OrderItemId;
                            });

                            var curData = curSubOrders.find(function (curItem) {
                                return filterOrderItemIds.indexOf(curItem.OrderItemId) == -1;
                            });

                            numErrManyWybOrders.push(curData);
                        }
                    }
                    else if (!isSelect) {
                        noChooseItems.push(item.PlatformOrderId);
                    }
                });

                if (noChooseItems.length == manyWybOrders.length && isPreview) {
                    layer.msg("请至少选择一个订单和包裹")
                    return;
                }

                if (failManyWybOrders.length > 0 && isPreview && commonModule.IsRepairSendHistory == false) {
                    var newMsg = "";
                    failManyWybOrders.forEach(function (item) {
                        newMsg += '<div style="marging-bottom:10px;">' + '运单号【<span style="color:#3aadff;">' + item + '</span>】未选择包裹内容' + '</div>'
                    });
                    layer.confirm(newMsg, { icon: 7, title: '提示', area: '450px', skin: 'wu-dailog' });
                    return;
                }

                if (numfailManyWybOrders.length > 0 && isPreview && commonModule.IsRepairSendHistory == false) {
                    var newMsg = "";
                    numfailManyWybOrders.forEach(function (item) {
                        newMsg += '<div style="marging-bottom:10px;">' + '订单【<span style="color:#3aadff;">' + item + '</span>】商品数量有误' + '</div>'
                    });
                    layer.confirm(newMsg, { icon: 7, title: '提示', area: '450px', skin: 'wu-dailog' });
                    return;
                }

                if (numErrManyWybOrders.length > 0 && isPreview && commonModule.IsRepairSendHistory == false) {
                    var skuData = numErrManyWybOrders[0];

                    var msgHtml = '订单内的所有商品需一次性发完，请选择全部商品。<br/><span class="wu-weight600">';
                    if (skuData.Color || skuData.Size) {
                        msgHtml += "【";
                        if (skuData.Color && skuData.Size) {
                            msgHtml += (skuData.Color + " | " + skuData.Size)
                        } else if (skuData.Color) {
                            msgHtml += skuData.Color
                        } else if (skuData.Size) {
                            msgHtml += skuData.Size
                        }
                        msgHtml += "】";
                    } else {
                        msgHtml += ("【商品ID：" + skuData.ProductID + "】");
                    }

                    msgHtml += '您有部分商品未选择发货。</span>';
                    layer.alert(msgHtml, { title: "提示", skin: 'wu-dailog', area: '450px' });
                    return;
                }

                //没有选择订单
                if (noChooseItems.length > 0) {
                    var noChooseHtml = '<div style="width:500px;max-heiht:300px; overflow-y:auto;">';
                    noChooseItems.forEach(function (item) {
                        noChooseHtml += '<div>订单编号：<span style="color:#3aadff">' + item + '</span>未选择，请重新选择！</div>'
                    })
                    noChooseHtml += '</div>';
                    var confirmNoChooseDailog = layer.confirm(noChooseHtml, { icon: 7, title: '提示', area: '500px', skin: 'wu-dailog' }, function () {
                        layer.close(confirmNoChooseDailog)
                    });
                } else {
                    manyWybOrders.forEach(function (item) {
                        checkOrders.forEach(function (checkItem) {
                            if (item.Id == checkItem.Id) {
                                checkItem.MultiPackSendModels = [];
                                item.WaybillCodes.forEach(function (cItem) {
                                    if (cItem.isSelect) {
                                        //商品直接取
                                        var productList = item.SubOrders;

                                        if (isPreview && cItem.SelectProductList.length > 0) {
                                            productList = cItem.SelectProductList
                                        } 

                                        productList.forEach(function (subItem) {
                                            var objExpress = {};
                                            objExpress.ExpressCompanyCode = cItem.ExpressCpCode;
                                            objExpress.PlatformExpressCode = null;
                                            objExpress.PlatformExpressName = null;
                                            objExpress.WaybillCode = cItem.WaybillCode;
                                            objExpress.OrderItemCode = subItem.OrderItemCode;
                                            objExpress.LogicOrderItemId = subItem.OrderItemId;
                                            objExpress.OrderItemId = subItem.OrderItemId;
                                            objExpress.Count = subItem.Count;
                                            objExpress.TrackType = cItem.TrackType;
                                            checkItem.MultiPackSendModels.push(objExpress);
                                        });
                                    }
                                })
                            }
                        })
                    });
                    //过滤
                    var lastOrders = [];
                    checkOrders.forEach(function (checkItem) {
                        if (checkItem.MultiPackSendModels != undefined && checkItem.MultiPackSendModels.length > 0) {
                            lastOrders.push(checkItem);
                        }
                    });
                    if (!lastOrders || lastOrders.length == 0) {
                        layer.msg('请至少选择一个订单和包裹', { icon: 7, time: 3500 });
                        return;
                    }

                    if ((manyCodeSendModel && manyCodeSendModel.length > 0 && isPreview) || commonModule.IsRepairSendHistory) {

                        var sendModel = JSON.parse(JSON.stringify(manyCodeSendModel));

                        sendModel.forEach(function (item) {

                            var row = lastOrders.find(function (aItem) {
                                return aItem.Id == item.Id;
                            });

                            if (row) {
                                var mCodes = []; // 该订单下所勾选的运单号
                                row.MultiPackSendModels.forEach(function (mItem) {
                                    if (mCodes.indexOf(mItem.WaybillCode) == -1) {
                                        mCodes.push(mItem.WaybillCode);
                                    }
                                });

                                mCodes.forEach(function (mCode) {
                                    var wIndex = item.WaybillCodeModels.findIndex(function (wItem) {
                                        return mCode == wItem.WaybillCode;
                                    });

                                    // 判断WaybillCodeModels是否包含当前运单号
                                    if (wIndex == -1) {
                                        item.WaybillCodeModels.push({
                                            MasterId: item.WaybillCodeModels.length,
                                            WaybillCode: mCode,
                                            WaybillCodeOrderProducts: [],
                                        });
                                    }
                                });

                                item.WaybillCodeModels = item.WaybillCodeModels.filter(function (wItem) {
                                    return mCodes.indexOf(wItem.WaybillCode) > -1;
                                });

                                item.WaybillCodeModels.forEach(function (wItem) {
                                    var models = row.MultiPackSendModels.filter(function (mItem) {
                                        return mItem.WaybillCode == wItem.WaybillCode;
                                    });

                                    var OrderItemIds = models.map(function (mItem) {
                                        return mItem.LogicOrderItemId;
                                    });

                                    wItem.WaybillCodeOrderProducts = wItem.WaybillCodeOrderProducts.filter(function (pItem) {
                                        return OrderItemIds.indexOf(pItem.OrderItemId) > -1;
                                    });


                                    models.forEach(function (cItem) {
                                        var findIndex = wItem.WaybillCodeOrderProducts.findIndex(function (sItem) {
                                            return sItem.OrderItemId == cItem.LogicOrderItemId;
                                        });

                                        if (findIndex > -1) {
                                            wItem.WaybillCodeOrderProducts[findIndex].Quantity = cItem.Count;
                                        } else {
                                            wItem.WaybillCodeOrderProducts.push({
                                                MasterId: wItem.MasterId,
                                                OrderItemCode: cItem.OrderItemCode,
                                                LogicOrderItemId: cItem.LogicOrderItemId,
                                                OrderItemId: cItem.LogicOrderItemId,
                                                Quantity: cItem.Count
                                            });
                                        }
                                    });
                                });
                            }
                        });

                        var findModelList = function (Id) {
                            var model = sendModel.find(function (item) {
                                return item.Id == Id
                            });

                            return model;
                        }

                        lastOrders.forEach(function (item) {
                            var model = findModelList(item.Id);
                            if (model) {
                                item.ManyCodeSendModel = model;
                                item.MultiPackSendModels = undefined;
                            }
                        });
                    }

                   
                    //提交数据
                    if (isPreview || commonModule.IsRepairSendHistory) {
                        //预览发货内容-待发货状态-走普通发货接口
                        var callbackFunc = function () {
                            //清空订单物流选中的商品信息
                            manyWybOrders.forEach(function (item) {
                                item.WaybillCodes.forEach(function (cItem) {
                                    cItem.isSelect = false;
                                });
                            });
                            module.closePddManyDrawer();
                            layer.closeAll();
                            $("#SeachConditions").click();
                        }

                        var callbackObj = {
                            CallBackFunc: callbackFunc,
                            IsSendManyOrders: true
                        };

                        sendLogistic.send(undefined, undefined, true, undefined, lastOrders, 1, callbackObj, true);
                    } else {
                        sendLogistic.appendpack(lastOrders, "Pinduoduo");
                    }
                }
            }
            module.onShowTrackTypeSelect = function () {
                var isShow = $(this).find(".pdd_trackType-select").css("display") == "block";
                module.onHideTrackTypeSelect();
                event.stopPropagation();
                if (isShow) {
                    return;
                }
                $(this).find(".pdd_trackType-select").show();
                $(this).find("i").removeClass("icon-a-chevron-down1x").addClass("icon-a-chevron-up1x");
            }

            module.onHideTrackTypeSelect = function () {
                $("#many-wybcode-drawer-content .pdd_trackType-select").hide();
                $("#many-wybcode-drawer-content .pdd_trackType > .icon-a-chevron-up1x").removeClass("icon-a-chevron-up1x").addClass("icon-a-chevron-down1x");
                if (isPreview) {
                    module.onHideProductSelect();
                }
            }

            module.onSelectTrackType = function (val, pid, id) {
                event.stopPropagation();
                $("#trackType_" + pid).attr("data-val", val);
                $("#trackType_" + pid).find("span").text($(this).text());
                $(this).siblings().removeClass("active");
                $(this).addClass("active");
                $("#trackType_" + pid).find("i").removeClass("icon-a-chevron-up1x").addClass("icon-a-chevron-down1x");
                $("#trackType_select" + pid).hide();

                $("#pddCheckAllCodeCheck_" + pid).removeClass("activeF activeP");
                $("#moreCodeWrap_tbody" + id + " .n-newCheckbox").removeClass("activeF");
                $("#selectAllPddMany_checkbox").removeClass("activeF activeP");
                manyWybOrders.forEach(function (item) {
                    if (item.Id == id) {
                        item.WaybillCodes.forEach(function (cItem) {
                            cItem.isSelect = false;
                        })
                    }
                });
            }

            module.selectAllPddMany = function () {
                var isChecked = $(this).find(".n-newCheckbox").hasClass("activeF"); // 全选
                var isSelect = $(this).find(".n-newCheckbox").hasClass("activeP"); // 半选
                var isCancel = isChecked || isSelect; // 如果是全选/半选，则取消所有勾选
                $("#many-wybcode-drawer-content").find(".pddCheckAllCodeSpan").each(function (index) {
                    var id = $(this).attr("data-id");
                    var pid = $(this).attr("data-pid");
                    module.selectPddAllCodeWrapExpress.bind(this)(id, pid, index, isCancel);
                });
            }
            module.selectPddAllCodeWrapExpress = function (id, platformOrderId, isIndex, isCancel) {
                var isChecked = $(this).find(".n-newCheckbox").hasClass("activeF");
                var isSelect = $(this).find(".n-newCheckbox").hasClass("activeP");

                if (isCancel) {
                    isChecked = true;
                    isSelect = true;
                }

                if (isChecked) {
                    $(this).find(".n-newCheckbox").removeClass("activeF");
                    if (isCancel) {
                        $(this).find(".n-newCheckbox").removeClass("activeP");
                    }
                } else if (isSelect) {
                    $(this).find(".n-newCheckbox").removeClass("activeP");
                } else {
                    $(this).find(".n-newCheckbox").addClass("activeF");
                }

                var maxPddCheckNum = 30;
                var trackTypeVal = $("#trackType_" + platformOrderId).attr("data-val");

                if (trackTypeVal == 1) {
                    maxPddCheckNum = 30;
                } else if (trackTypeVal == 2) {
                    maxPddCheckNum = 3;
                } else if (trackTypeVal == 3) {
                    maxPddCheckNum = 1;
                }

                var that = this;

                if (isSelect) {
                    isChecked = true;
                }

                $("#moreCodeWrap_tbody" + id).find(".n-newCheckbox").each(function (index, item) {
                    if (index + 1 > maxPddCheckNum) {
                        $(item).removeClass("activeF");
                        if (!isChecked) {
                            if (!isIndex) {
                                layer.msg(showTrackTar(trackTypeVal));
                            }
                            $(that).find(".n-newCheckbox").removeClass("activeF").addClass("activeP");
                        }
                    } else {
                        if (isChecked) {
                            $(item).removeClass("activeF");
                        } else {
                            $(item).addClass("activeF");
                        }
                    }
                });

                manyWybOrders.forEach(function (item) {
                    if (item.PlatformOrderId == platformOrderId) {
                        item.WaybillCodes.forEach(function (cItem, cIndex) {
                            cItem.isSelect = false;
                            if (cIndex + 1 > maxPddCheckNum) {
                                cItem.isSelect = false;
                            } else {
                                cItem.isSelect = !isChecked;
                                if (isChecked) {
                                    cItem.TrackType = "1";
                                } else {
                                    cItem.TrackType = trackTypeVal;
                                }
                            }
                        })
                    }
                })

                handelSelectStatus();

                if (isPreview) {
                    manyWybOrders.forEach(function (item) {
                        if (item.Id == id) {
                            item.WaybillCodes.forEach(function (cItem) {
                                handeProductNumErr(isChecked, id, cItem.WaybillCodeId);
                            });
                        }
                    })
                }
            }

            module.selectSinglePddMany = selectSinglePddMany;
            function showTrackTar(val) {
                var msgText = "";
                if (val == 1) {
                    msgText = '“分包发货”类型最多只支持回传30个单号';
                } else if (val == 2) {
                    msgText = '“补发商品”类型最多只支持回传3个单号';
                } else if (val == 3) {
                    msgText = '“发放赠品”类型最多只支持回传1个单号';
                }
                return msgText;
            }

            if (isPreview) {
                // 选择某个商品
                module.onSelectProductSub = function (id, waybillCodeId, newOrderItemId) {
                    event.stopPropagation();
                    var isSelect = $(this).hasClass("noselect");
                    var isDisabled = $(this).hasClass("disabled");
                    if (isSelect || isDisabled) {
                        if (isSelect) {
                            layer.msg("商品已添加，请勿重复选择");
                        }
                        return;
                    }

                    var $selectSub = $(this).parent().parent();
                    var $selectNum = $(this).parent().parent().siblings(":first").children('.tt_productNum');
                    $selectSub.removeClass("noselect");
                    $selectNum.removeClass("noselect");
                    // 商品总数量
                    var ProductNum = $(this).attr("data-count");
                    var oldOrderItemId = $selectSub.attr("data-val");
                    var oldOrderItemCode = "";
                    if (oldOrderItemId) {
                        oldOrderItemCode = $(this).siblings('.tt_productSub-select_item[data-orderitemid="' + oldOrderItemId + '"]').attr("data-code");
                    }
                    var oldText = $(this).parent().siblings(".tt_productSub-input-text").text();
                    var isExist = $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + newOrderItemId + '"]').length;
                    var selectedIds = [];  // 该运单号已经添加的商品

                    // 更换渲染数据
                    $(this).addClass("disabled");
                    var text = $(this).find(".tt_productSub-select-text").text();
                    $selectSub.find(".tt_productSub-input-text").text(text);
                    $selectSub.find("i").removeClass("icon-a-chevron-up1x").addClass("icon-a-chevron-down1x");
                    $selectSub.find(".tt_productSub-select").hide();
                    $selectSub.attr("data-val", newOrderItemId);

                    // 该运单号下的包裹内容选择器禁用该商品

                    $("#package_content" + id + "_" + waybillCodeId + " .package_content-select_item .tt_productSub").each(function () {
                        var val = $(this).attr("data-val");
                        if (val) {
                            selectedIds.push(val - 0);
                        }
                    });

                    $("#package_content" + id + "_" + waybillCodeId + " .package_content-select_item .tt_productSub").each(function () {
                        $(this).children().children().each(function () {
                            var oid = $(this).attr("data-orderitemid") - 0;
                            if (selectedIds.indexOf(oid) == -1) {
                                $(this).removeClass("disabled noselect");
                            } else {
                                $(this).addClass("disabled noselect");
                            }
                        });
                    });

                    // 判断当前运单号是否勾选
                    var isCheckWay = $("#manyCode-checkbox_" + id + waybillCodeId).hasClass("activeF");

                    // 需要提醒错误的运单号包裹id
                    var errPackageIds = [];

                    if (isCheckWay) {
                        $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + newOrderItemId + '"]').each(function () {
                            var parentId = $(this).parent().parent().parent().attr("id");
                            if (parentId != "package_content" + id + "_" + waybillCodeId) {
                                if (errPackageIds.indexOf(parentId) == -1) {
                                    errPackageIds.push(parentId);
                                }
                            }
                        });
                    }

                    // console.log(errPackageIds)

                    // oldOrderItemId ? 切换商品 : 新增商品
                    // 调换商品位置：如果切换的商品只涵盖在一个包裹里
                    // 选择的商品存在剩余商品数量，不需要调换（商品总数量 > 当前数量）
                    // 选择的商品存在被调换的运单号中，则不需要调换
                    // 当前商品与包裹商品直接调换
                    var isExchange = false;
                    var isHave = false;
                    if (isExist == 1 && oldOrderItemId && errPackageIds.length == 1 && isCheckWay) {
                        var oId = "#" + errPackageIds[0];
                        var $oldItem = $(oId).find('.tt_productSub[data-val="' + oldOrderItemId + '"]');

                        if ($oldItem.length > 0) {
                            isHave = true;
                        }

                        var $newItem = $(oId).find('.tt_productSub[data-val="' + newOrderItemId + '"]');
                        var selectedNum = $newItem.siblings(":first").children('.tt_productNum').attr("data-val");
                        if (selectedNum == ProductNum) {
                            isExchange = true;
                        }
                    }

                    // 调换商品
                    if (isExist == 1 && oldOrderItemId && errPackageIds.length == 1 && isExchange && isCheckWay && !isHave) {
                        var oId = "#" + errPackageIds[0];
                        $selectNum.find("span").text(ProductNum);
                        $selectNum.attr("data-val", ProductNum);

                        var newHtmlDiv = "";
                        for (var i = 0; i <= ProductNum; i++) {
                            if (i == ProductNum) {
                                newHtmlDiv += '<div class="active" onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + waybillCodeId + ')">' + i + '</div>'
                            } else {
                                newHtmlDiv += '<div onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + waybillCodeId + ')">' + i + '</div>'
                            }
                        }
                        $selectNum.find(".tt_productNum-select").html(newHtmlDiv);

                        var oldProductNum = $(this).siblings('.tt_productSub-select_item[data-orderitemid="' + oldOrderItemId + '"]').attr("data-count");

                        // 调换--包裹内容
                        var $domId = $(oId).find('.tt_productSub[data-val="' + newOrderItemId + '"]');
                        $domId.children('.tt_productSub-input-text').text(oldText);
                        $domId.find('.tt_productSub-select_item[data-orderitemid="' + newOrderItemId + '"]').removeClass("disabled noselect");
                        $domId.find('.tt_productSub-select_item[data-orderitemid="' + oldOrderItemId + '"]').addClass("disabled noselect");
                        $domId.attr("data-val", oldOrderItemId);

                        // 调换--商品数量--waybillCodeId也需要调换
                        var oldWaybillCodeId = errPackageIds[0].split("_")[2];
                        var htmlDiv = "";
                        for (var i = 0; i <= oldProductNum; i++) {
                            if (i == oldProductNum) {
                                htmlDiv += '<div class="active" onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + oldWaybillCodeId + ')">' + i + '</div>'
                            } else {
                                htmlDiv += '<div onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + oldWaybillCodeId + ')">' + i + '</div>'
                            }
                        }
                        $domId.siblings(":first").children('.tt_productNum').attr("data-val", oldProductNum);
                        $domId.siblings(":first").children('.tt_productNum').find('span').text(oldProductNum);
                        $domId.siblings(":first").children('.tt_productNum').find(".tt_productNum-select").html(htmlDiv);
                    }
                    // 1. 切换的商品存在单个或多个包裹
                    // 2. 把商品拆分在不同包裹
                    // 3. 把商品调换到另一个包裹
                    // 4. 切换后，商品数量默认为1，其他包裹商品数量红色预警
                    else if (isExist > 0 && errPackageIds.length > 0 && isCheckWay) {
                        $selectNum.find("span").text(1);
                        $selectNum.attr("data-val", 1);

                        // 重新渲染--商品数量
                        var htmlDiv = "";
                        for (var i = 0; i <= ProductNum; i++) {
                            if (i == 1) {
                                htmlDiv += '<div class="active" onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + waybillCodeId + ')">' + i + '</div>'
                            } else {
                                htmlDiv += '<div onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + waybillCodeId + ')">' + i + '</div>'
                            }
                        }
                        $selectNum.find(".tt_productNum-select").html(htmlDiv);

                        // 统计当前商品数量，得判断当前商品的数量+1 是否大于商品总数量
                        var curProductNum = 1;
                        errPackageIds.forEach(function (item) {
                            var strs = item.split('package_content')[1].split('_');
                            var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                            if (isCheck) {
                                var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + newOrderItemId + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                curProductNum += (sibProductNum - 0);
                            }
                        });

                        // 其他包裹里的商品数量不变--并红色预警--且移除自身红色预警todo
                        errPackageIds.forEach(function (item) {
                            var errSibLength = $("#" + item).find('.tt_productSub[data-val="' + newOrderItemId + '"]').parent().siblings().length;
                            var strs = item.split('package_content')[1].split('_');
                            var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");

                            // 当前选择的数量是否大于0
                            var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + newOrderItemId + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                            var isOutZero = sibProductNum > 0;

                            // 是其父元素的唯一子元素则添加错误提醒--避免重复添加
                            if (errSibLength == 0 && curProductNum > ProductNum && isCheck && isOutZero && commonModule.IsRepairSendHistory == false) {
                                var errorHtml = '<span class="tt_productSub-select-num_error">数量有误</span>';
                                $("#" + item).find('.tt_productSub[data-val="' + newOrderItemId + '"]').parent().parent().append(errorHtml);
                                $("#" + item).find('.tt_productSub[data-val="' + newOrderItemId + '"]').siblings(":first").children('.tt_productNum').addClass("num_error-border_color");
                            }
                        });

                        // 自己移除了之后，其他运单号下的该商品是否符合预期数量？
                        if (oldOrderItemId) {
                            if (ProductNum >= curProductNum) {
                                $selectNum.removeClass("num_error-border_color");
                                $selectSub.parent().siblings(".tt_productSub-select-num_error").remove();
                            }

                            // 该订单下是否还存在选择了该商品的运单号
                            var oldIsExist = $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + oldOrderItemId + '"]').length;
                            if (oldIsExist > 0) {
                                var oldCurProductNum = 0;
                                $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + oldOrderItemId + '"]').each(function () {
                                    var parentId = $(this).parent().parent().parent().attr("id");
                                    var strs = parentId.split('package_content')[1].split('_');
                                    var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                                    if (isCheck) {
                                        var sibProductNum = $(this).siblings(":first").children('.tt_productNum').attr("data-val");
                                        oldCurProductNum += (sibProductNum - 0);
                                    }
                                });

                                var oldProductNum = $(this).siblings('.tt_productSub-select_item[data-orderitemid="' + oldOrderItemId + '"]').attr("data-count");

                                // 统计当前商品数量，得判断当前商品的数量是否大于商品总数量
                                if (oldCurProductNum <= oldProductNum) {
                                    $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + oldOrderItemId + '"]').each(function () {
                                        $(this).siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                                        $(this).parent().siblings(".tt_productSub-select-num_error").remove();
                                    });
                                }
                            }
                        }
                    }
                    else {
                        $selectNum.find("span").text(ProductNum);
                        $selectNum.attr("data-val", ProductNum);

                        var htmlDiv = "";
                        for (var i = 0; i <= ProductNum; i++) {
                            if (i == ProductNum) {
                                htmlDiv += '<div class="active" onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + waybillCodeId + ')">' + i + '</div>'
                            } else {
                                htmlDiv += '<div onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + waybillCodeId + ')">' + i + '</div>'
                            }
                        }
                        $selectNum.find(".tt_productNum-select").html(htmlDiv);
                        if ($selectNum.hasClass("num_error-border_color")) {
                            $selectNum.removeClass("num_error-border_color");
                            $selectSub.parent().siblings(".tt_productSub-select-num_error").remove();
                        }

                        // 自己移除了之后，其他运单号下的该商品是否符合预期数量？
                        if (oldOrderItemId && isCheckWay) {
                            // 该订单下是否还存在选择了该商品的运单号
                            var oldIsExist = $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + oldOrderItemId + '"]').length;
                            if (oldIsExist > 0) {
                                var oldCurProductNum = 0;
                                $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + oldOrderItemId + '"]').each(function () {
                                    var parentId = $(this).parent().parent().parent().attr("id");
                                    var strs = parentId.split('package_content')[1].split('_');
                                    var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                                    if (isCheck) {
                                        var sibProductNum = $(this).siblings(":first").children('.tt_productNum').attr("data-val");
                                        oldCurProductNum += (sibProductNum - 0);
                                    }
                                });

                                var oldProductNum = $(this).siblings('.tt_productSub-select_item[data-orderitemid="' + oldOrderItemId + '"]').attr("data-count");

                                // 统计当前商品数量，得判断当前商品的数量是否大于商品总数量
                                if (oldCurProductNum <= oldProductNum) {
                                    $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + oldOrderItemId + '"]').each(function () {
                                        $(this).siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                                        $(this).parent().siblings(".tt_productSub-select-num_error").remove();
                                    });
                                }
                            }
                        }
                    }

                    var newOrderItemCode = $(this).attr("data-code");
                    if (oldOrderItemId) {
                        manyWybOrders.forEach(function (item) {
                            if (item.Id == id) {
                                item.WaybillCodes.forEach(function (cItem) {
                                    // 切换商品
                                    if (cItem.WaybillCodeId == waybillCodeId) {
                                        cItem.SelectProductList.forEach(function (sItem) {
                                            if (sItem.OrderItemId == oldOrderItemId) {
                                                sItem.OrderItemId = newOrderItemId - 0
                                                sItem.OrderItemCode = newOrderItemCode
                                                if (isExist == 1 && oldOrderItemId && errPackageIds.length == 1 && isExchange && isCheckWay && !isHave) {
                                                    sItem.Count = ProductNum - 0;
                                                } else {
                                                    sItem.Count = isExist > 0 && errPackageIds.length > 0 && isCheckWay ? 1 : ProductNum - 0;
                                                }
                                            }
                                        });
                                    }

                                    // 调换商品
                                    if (isCheckWay && isExchange && !isHave && isExist == 1 && oldOrderItemId && errPackageIds.length == 1 && cItem.WaybillCodeId == errPackageIds[0].split("_")[2]) {
                                        cItem.SelectProductList.forEach(function (sItem) {
                                            if (sItem.OrderItemId == newOrderItemId) {
                                                sItem.OrderItemId = oldOrderItemId - 0
                                                sItem.OrderItemCode = oldOrderItemCode
                                                sItem.Count = oldProductNum - 0
                                            }
                                        });
                                    }
                                });
                            }
                        });
                    }
                    // 未选择时--添加商品
                    else {
                        manyWybOrders.forEach(function (item) {
                            if (item.Id == id) {
                                item.WaybillCodes.forEach(function (cItem) {
                                    if (cItem.WaybillCodeId == waybillCodeId) {

                                        var fIndex = cItem.SelectProductList.findIndex(function (fItem) {
                                            return fItem.OrderItemId == newOrderItemId;
                                        });

                                        if (fIndex == -1) {
                                            cItem.SelectProductList.push({
                                                OrderItemId: newOrderItemId - 0,
                                                OrderItemCode: newOrderItemCode,
                                                Count: isExist > 0 && errPackageIds.length > 0 && isCheckWay ? 1 : ProductNum - 0
                                            });
                                        }
                                    }
                                });
                            }
                        });
                    }
                }

                // 选择商品数量
                module.onSelectProductNum = function (id, waybillCodeId) {
                    event.stopPropagation();
                    var $select = $(this).parent().parent(".tt_productNum");
                    var selectVal = $select.attr("data-val");
                    var val = $(this).text();
                    $select.find("i").removeClass("icon-a-chevron-up1x").addClass("icon-a-chevron-down1x");
                    $(this).parent().hide();

                    if (selectVal == val) {
                        return;
                    }

                    $(this).siblings().removeClass("active");
                    $(this).addClass("active");
                    $select.attr("data-val", val);
                    $select.find("span").text($(this).text());
                    var isCheck = $("#manyCode-checkbox_" + id + waybillCodeId).hasClass("activeF");

                    var orderItemId = $(this).parent().parent().parent().siblings('.tt_productSub').attr("data-val");
                    if (orderItemId) {
                        manyWybOrders.forEach(function (item) {
                            if (item.Id == id) {
                                item.WaybillCodes.forEach(function (cItem) {
                                    if (cItem.WaybillCodeId == waybillCodeId) {
                                        cItem.SelectProductList.forEach(function (sItem) {
                                            if (sItem.OrderItemId == orderItemId) {
                                                sItem.Count = val - 0
                                            }
                                        });
                                    }
                                });
                            }
                        });

                        if (!isCheck) {
                            return;
                        }

                        var errPackageIds = [];
                        var curPackageId = [];

                        // 先找出--调整的是哪一个订单--运单号--包裹内容--商品数量
                        $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + orderItemId + '"]').each(function () {
                            var parentId = $(this).parent().parent().parent().attr("id");
                            if (parentId != "package_content" + id + "_" + waybillCodeId) {
                                if (errPackageIds.indexOf(parentId) == -1) {
                                    errPackageIds.push(parentId);
                                }
                            } else {
                                if (curPackageId.indexOf(parentId) == -1) {
                                    curPackageId.push(parentId);
                                }
                            }
                        });

                        //console.log(errPackageIds)
                        //console.log(curPackageId)

                        if (errPackageIds.length > 0 && curPackageId.length > 0) {
                            var sid = orderItemId;
                            var cId = "#" + curPackageId[0];
                            var curProductNum = val - 0; // 当前选择的数量
                            // 商品的总数量
                            var totalProductNum = $(cId).find('.tt_productSub-select_item[data-orderitemid="' + sid + '"]').attr("data-count");

                            errPackageIds.forEach(function (item) {
                                var strs = item.split('package_content')[1].split('_');
                                var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                                if (isCheck) {
                                    var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                    curProductNum += (sibProductNum - 0);
                                }
                            });

                            //console.log(curProductNum)
                            //console.log(totalProductNum)

                            // 选择的数量 小于等于总数量了--移除预警
                            if (curProductNum <= totalProductNum) {
                                errPackageIds.forEach(function (item) {
                                    $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').parent().siblings(".tt_productSub-select-num_error").remove();
                                    $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                                });

                                $(cId).find('.tt_productSub[data-val="' + sid + '"]').parent().siblings(".tt_productSub-select-num_error").remove();
                                $(cId).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                            }
                            // 选择的数量 超出商品总数量--红色预警
                            else if (curProductNum > totalProductNum) {
                                var cId = "#" + curPackageId[0];
                                var errSibLength = $(cId).find('.tt_productSub[data-val="' + sid + '"]').parent().siblings().length;

                                // 是其父元素的唯一子元素则添加错误提醒--避免重复添加
                                if (val == 0) {
                                    $(cId).find('.tt_productSub[data-val="' + sid + '"]').parent().siblings(".tt_productSub-select-num_error").remove();
                                    $(cId).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                                } else if (errSibLength == 0 && commonModule.IsRepairSendHistory == false) {
                                    var errorHtml = '<span class="tt_productSub-select-num_error">数量有误</span>';
                                    $(cId).find('.tt_productSub[data-val="' + sid + '"]').parent().parent().append(errorHtml);
                                    $(cId).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').addClass("num_error-border_color");
                                }
                            }
                        }
                    }
                }

                // 隐藏选择器
                module.onHideProductSelect = function () {
                    $("#many-wybcode-drawer-content .tt_productSub-select").hide();
                    $("#many-wybcode-drawer-content .tt_productNum-select").hide();
                    $("#many-wybcode-drawer-content .tt_productSub > .icon-a-chevron-up1x").removeClass("icon-a-chevron-up1x").addClass("icon-a-chevron-down1x");
                    $("#many-wybcode-drawer-content .tt_productNum > .icon-a-chevron-up1x").removeClass("icon-a-chevron-up1x").addClass("icon-a-chevron-down1x");
                }

                module.onShowProductSubSelect = function () {
                    var isShow = $(this).find(".tt_productSub-select").css("display") == "block";
                    module.onHideProductSelect();
                    event.stopPropagation();
                    if (isShow) {
                        return;
                    }
                    $(this).find(".tt_productSub-select").show();
                    $(this).find("i").removeClass("icon-a-chevron-down1x").addClass("icon-a-chevron-up1x");
                }

                // 显示包裹内容--数量选择器
                module.onShowProductNumSelect = function () {
                    var isSelect = $(this).hasClass("noselect");
                    if (isSelect) {
                        layer.msg("请先选择商品");
                        return;
                    }

                    var isShow = $(this).find(".tt_productNum-select").css("display") == "block";
                    module.onHideProductSelect();
                    event.stopPropagation();
                    if (isShow) {
                        return;
                    }
                    $(this).find(".tt_productNum-select").show();
                    $(this).find("i").removeClass("icon-a-chevron-down1x").addClass("icon-a-chevron-up1x");
                }

                // 移除某个运单号下的商品选择器
                module.removeProductSelect = function (id, waybillCodeId) {
                    var val = $(this).parent().siblings('.tt_productSub').attr("data-val");
                    var siblingsLength = $(this).siblings().length;

                    if (val) {
                        $("#package_content" + id + "_" + waybillCodeId + " .tt_productSub-select_item").each(function () {
                            if ($(this).attr("data-orderitemid") == val) {
                                $(this).removeClass("disabled noselect");
                            }
                        });

                        // 需要移除错误的运单号包裹id
                        var errPackageIds = [];
                        $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + val + '"]').each(function () {
                            var parentId = $(this).parent().parent().parent().attr("id");
                            if (parentId != "package_content" + id + "_" + waybillCodeId) {
                                if (errPackageIds.indexOf(parentId) == -1) {
                                    errPackageIds.push(parentId);
                                }
                            }
                        });

                        //console.log(errPackageIds)

                        // 统计当前商品数量，得判断当前商品的数量 - 移除的商品数量，是否小于等于商品总数量
                        if (errPackageIds.length > 0) {
                            var removeProductNum = $(this).parent().prev().children('.tt_productNum').attr("data-val");
                            var curProductNum = removeProductNum - 0;
                            var totalProductNum = $(this).parent().siblings('.tt_productSub').find('.tt_productSub-select_item[data-orderitemid="' + val + '"]').attr("data-count");

                            errPackageIds.forEach(function (item) {
                                var strs = item.split('package_content')[1].split('_');
                                var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                                if (isCheck) {
                                    var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + val + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                    curProductNum += (sibProductNum - 0);
                                }
                            });

                            // 移除--红色预警
                            if (curProductNum - removeProductNum <= totalProductNum) {
                                errPackageIds.forEach(function (item) {
                                    $("#" + item).find('.tt_productSub[data-val="' + val + '"]').parent().siblings(".tt_productSub-select-num_error").remove();
                                    $("#" + item).find('.tt_productSub[data-val="' + val + '"]').siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                                });
                            }
                        }

                        manyWybOrders.forEach(function (item) {
                            if (item.Id == id) {
                                item.WaybillCodes.forEach(function (cItem) {
                                    if (cItem.WaybillCodeId == waybillCodeId) {
                                        cItem.SelectProductList = cItem.SelectProductList.filter(function (sItem) {
                                            return sItem.OrderItemId != val;
                                        });
                                    }
                                });
                            }
                        });
                    }

                    $(this).closest('.package_content-select_item').remove();
                    var selectLength = $("#package_content" + id + "_" + waybillCodeId + " .package_content-select_item").length;
                    if (selectLength == 0) {
                        var emptyHtml = '<i onclick="waitOrderModule.addMoreProductSelect.bind(this)(' + id + ',' + waybillCodeId + ')" class="iconfont icon-a-add-circle1x1" style="color: #0888ff; cursor: pointer;"></i>';
                        $("#package_content" + id + "_" + waybillCodeId).append(emptyHtml);
                    } else if (siblingsLength > 0) {
                        var emptyHtml = '<i onclick="waitOrderModule.addMoreProductSelect.bind(this)(' + id + ',' + waybillCodeId + ')" class="iconfont icon-a-add-circle1x1" style="color: #0888ff; cursor: pointer;"></i>';
                        $("#package_content" + id + "_" + waybillCodeId).children().last().find(".package_content-movebtn").append(emptyHtml);
                    }
                }

                // 添加某个运单号下的商品选择器
                module.addMoreProductSelect = function (id, waybillCodeId) {
                    var orderRow = manyWybOrders.find(function (item) {
                        return item.Id == id;
                    });

                    var waybillCodeRow = orderRow.WaybillCodes.find(function (item) {
                        return item.WaybillCodeId == waybillCodeId;
                    });

                    var AddSubOrders = waybillCodeRow.AddSubOrders;

                    var selectedIds = [];
                    // 已经选择的的OrderItemId
                    $("#package_content" + id + "_" + waybillCodeId + " .tt_productSub").each(function () {
                        var val = $(this).attr("data-val");
                        if (val) {
                            selectedIds.push(val);
                        }
                    });

                    var addSubOrders = [];
                    AddSubOrders.forEach(function (item) {
                        if (selectedIds.indexOf(item.OrderItemId) == -1) {
                            addSubOrders.push(item.OrderItemId);
                        }
                    });


                    if (addSubOrders.length == 0) {
                        layer.msg("暂无可添加的商品");
                        return;
                    }

                    var selectLength = $("#package_content" + id + "_" + waybillCodeId + " .package_content-select_item").length;
                    if (AddSubOrders.length == selectLength) {
                        layer.msg("该运单号最多可添加" + AddSubOrders.length + "个包裹");
                        return;
                    }

                    $(this).remove();

                    var newSelectHtml = "";
                    newSelectHtml += '<div class="package_content-select_item">';
                    newSelectHtml += '<div style="display: flex; align-items: center;">';
                    newSelectHtml += '<div onclick="waitOrderModule.onShowProductSubSelect.bind(this)()" class="tt_productSub noselect">';
                    newSelectHtml += '<span class="tt_productSub-input-text">请选择商品</span>';
                    newSelectHtml += '<i class="iconfont icon-a-chevron-down1x"></i>';
                    newSelectHtml += '<div class="tt_productSub-select" style="padding-bottom: 0px;">';
                    for (var i = 0; i < AddSubOrders.length; i++) {
                        var orderItemCode = AddSubOrders[i].OrderItemCode + "";
                        var orderItemId = AddSubOrders[i].OrderItemId + "";
                        var count = AddSubOrders[i].Count;
                        var productImgUrl = AddSubOrders[i].ProductImgUrl;
                        var color = AddSubOrders[i].Color;
                        var size = AddSubOrders[i].Size;
                        var productId = AddSubOrders[i].ProductID;

                        if (selectedIds.indexOf(orderItemId) > -1) {
                            newSelectHtml += '<div style="margin-bottom: 8px;" onclick="waitOrderModule.onSelectProductSub.bind(this)(' + id + ',' + waybillCodeId + ',' + orderItemId + ')" data-count="' + count + '" data-orderitemid="' + orderItemId + '" data-code="' + orderItemCode + '" class="disabled noselect tt_productSub-select_item">';
                        } else {
                            newSelectHtml += '<div style="margin-bottom: 8px;" onclick="waitOrderModule.onSelectProductSub.bind(this)(' + id + ',' + waybillCodeId + ',' + orderItemId + ')" data-count="' + count + '" data-orderitemid="' + orderItemId + '" data-code="' + orderItemCode + '" class="tt_productSub-select_item">';
                        }

                        newSelectHtml += '<div style="display: flex; font-size: 14px; line-height: 20px;">';
                        if (productImgUrl) {
                            newSelectHtml += '<img style="width: 56px; height: 56px; border-radius: 4px; margin-right: 8px; border: 0.5px solid rgba(0, 0, 0, 0.14);" src="' + productImgUrl + '">';
                        } else {
                            newSelectHtml += '<img style="width: 56px; height: 56px; border-radius: 4px; margin-right: 8px; border: 0.5px solid rgba(0, 0, 0, 0.14);" src="/Content/images/nopic.gif">';
                        }

                        newSelectHtml += '<div style="display: flex; width: 100%; justify-content: space-between; padding: 6px 0; ">';
                        newSelectHtml += '<div style="display: flex; flex-direction: column;">';
                        newSelectHtml += '<div class="tt_productSub-select-text">';
                        if (color || size) {
                            if (color && size) {
                                newSelectHtml += color + ' | ' + size
                            } else if (color) {
                                newSelectHtml += color
                            } else if (size) {
                                newSelectHtml += size
                            }
                        } else {
                            newSelectHtml += '商品ID：' + productId
                        }
                        newSelectHtml += '</div>';

                        if (size) {
                            newSelectHtml += '<span>' + size + '</span>';
                        }

                        newSelectHtml += '</div>';

                        newSelectHtml += '<span class="tt_productSub-select-num">x' + count + '</span>';
                        newSelectHtml += '</div></div></div>';
                    }
                    newSelectHtml += '</div></div>';
                    newSelectHtml += '<div style="display: flex; align-items: center; margin-left: 16px;">';
                    newSelectHtml += '<span style="color: #1a1a1a;width: 14px;">x</span>';
                    newSelectHtml += '<div onclick="waitOrderModule.onShowProductNumSelect.bind(this)()" class="tt_productNum noselect" data-val="0">'
                    newSelectHtml += '<span>0</span>';
                    newSelectHtml += '<i class="iconfont icon-a-chevron-down1x"></i>';
                    newSelectHtml += '<div class="tt_productNum-select">';
                    newSelectHtml += '</div></div></div>';

                    newSelectHtml += '<div class="package_content-movebtn" style="display: flex; align-items: center;">';
                    newSelectHtml += '<i onclick="waitOrderModule.removeProductSelect.bind(this)(' + id + ',' + waybillCodeId + ')" class="iconfont icon-a-minus-circle1x" style="color: #EA572E; cursor: pointer; margin-right: 8px;"></i>';
                    newSelectHtml += '<i onclick="waitOrderModule.addMoreProductSelect.bind(this)(' + id + ',' + waybillCodeId + ')" class="iconfont icon-a-add-circle1x1" style="color: #0888ff; cursor: pointer;"></i>';
                    newSelectHtml += '</div></div></div>';

                    $("#package_content" + id + "_" + waybillCodeId).append(newSelectHtml);
                }
            }
        }

        //多单号回传
        module.BatchSendManyV2 = function (sendOrders, isPreview) {
            var manyWybOrders = [];

            var $checkBoxes = $("#OrderTableList .order-chx");
            if (sendOrders && sendOrders.length > 0) {
                $checkBoxes = [];
                $(sendOrders).each(function (i, item) {
                    var loid = item.LogicOrderId;
                    var $ckb = $("#OrderTableList .order-chx[data-pid=" + loid + "]");
                    if ($ckb.length > 0)
                        $checkBoxes.push($ckb);
                });
            }

            $("#OrderTableList .order-chx").each(function (index, item) {
                if (item.checked && $(item).attr("data-platformtype") == "KuaiShou") {
                    var pid = $(item).attr("data-pid");
                    $(".productShow-itemInput.orderitem-chx").each(function (cIndex, cItem) {
                        var cPid = $(cItem).attr("data-pid");
                        if (pid == cPid) {
                            if (!cItem.checked) {
                                cItem.click();
                            }
                        }
                    })
                }
            })

            var checkOrders = sendOrders == null || sendOrders.length == 0 ? orderTableBuilder.getSelections(undefined, undefined, undefined, false, true) : sendOrders;
            if (checkOrders.length == 0) {
                layer.confirm("请先选择需要上传的订单", { title: '提示', skin: 'wu-dailog' });
                return;
            }

            var checkedOrderData = $("#OrderTableList .order-chx:checked");
            var platformtypes = [];
            checkedOrderData.each(function () {
                var ptType = $(this).data('platformtype');
                platformtypes.push(ptType);
            });
            if (platformtypes.includes("TouTiaoSaleShop")) {
                layer.alert("即时零售订单暂不支持此功能，请取消选择后重试", { title: '提示', skin: 'wu-dailog' });
                return;
            }

            var template = addTmplInOrderListModule.GetCurrentTemplate()
            if (template == null) {
                layer.alert("请选择快递模板", { title: '提示', skin: 'wu-dailog' });
                return;
            }

            var configs = commonModule.manyCodeSendConfigData;
            for (var i = 0; i < checkOrders.length; i++) {
                var o = checkOrders[i];
                if (commonModule.SupportPlatformTypes.indexOf(o.PlatformType) == -1) {
                    layer.confirm("目前仅抖音/快手/淘宝/视频号/淘工厂/拼多多/有赞/小红书/京东/自有商城平台订单支持上传多运单号，请先取消勾选其他平台订单再进行操作", { title: '提示', skin: 'wu-dailog' });
                    return;
                } else {
                    var row = orderTableBuilder.rows[o.Index];
                    var tmpOrder = JSON.parse(JSON.stringify(row));
                    tmpOrder.WaybillCodes = [];
                    tmpOrder.SubOrders = [];
                    // 运单号信息

                    if (row.WaybillCodes && row.WaybillCodes.length > 0) {
                        for (var j = 0; j < row.WaybillCodes.length; j++) {
                            if (commonModule.IsRepairSendHistory || row.WaybillCodes[j].Status == undefined || row.WaybillCodes[j].Status == 1)
                                tmpOrder.WaybillCodes.push(row.WaybillCodes[j]);
                            if (o.PlatformType.indexOf('Pinduoduo') != -1 && row.WaybillCodes[j].SendType == 10)
                                tmpOrder.AlreadyAppendPack = 1;//已经使用过追加包裹
                        }
                    }

                    var ptName = commonModule.PlatformNameDic[o.PlatformType] || "";

                     // 是否开启多单号发货设置
                    if (["TouTiao", "KuaiShou", "Taobao", "WxVideo", "XiaoHongShu", "Jingdong", "OwnShop"].indexOf(o.PlatformType) != -1 && configs && configs.TouTiao == false || !configs) {
                        layer.confirm(ptName + "平台未开启多单号发货功能，请先取消勾选此类订单再进行操作", { title: '提示', skin: 'wu-dailog' });
                        return;
                    } else if ("AlibabaC2M" == o.PlatformType && configs && configs.AlibabaC2M == false || !configs) {
                        layer.confirm("淘工厂平台未开启多单号发货功能，请先取消勾选此类订单再进行操作", { title: '提示', skin: 'wu-dailog' });
                        return;
                    } else if ("TaobaoMaiCaiV2" == o.PlatformType && configs && configs.TaobaoMaiCaiV2 == false || !configs) {
                        layer.confirm("淘宝买菜(新)平台未开启多单号发货功能，请先取消勾选此类订单再进行操作", { skin: 'wu-dailog' });
                        return;
                    } else if ("YouZan" == o.PlatformType && configs && configs.YouZan == false || !configs) {
                        layer.confirm("有赞平台未开启多单号发货功能，请先取消勾选此类订单再进行操作", { title: '提示', skin: 'wu-dailog' });
                        return;
                    }

                    
                    if (tmpOrder.WaybillCodes.length <= 0) {
                        layer.confirm(ptName + "平台的订单【" + row.PlatformOrderId + "】可上传未发货运单号必须大于等于1，请先取消勾选此类订单再进行操作", { title: '提示', skin: 'wu-dailog' });
                        return;
                    }

                    if (commonModule.IsRepairSendHistory == false && commonModule.WaitStatusSendPts.indexOf(o.PlatformType) != -1 && row.ErpState != 'waitsellersend') {
                        layer.confirm(ptName + "平台的订单【" + row.PlatformOrderId + "】只有待发货状态的订单才能回传多单号，请先取消勾选此类订单再进行操作", { title: '提示', skin: 'wu-dailog' });
                        return;
                    }
                    else if (!isPreview && commonModule.IsRepairSendHistory == false && commonModule.SendStatusSendPts.indexOf(o.PlatformType) != -1 && (row.ErpState != 'sended' || row.ErpRefundState == 'REFUND_SUCCESS')) {
                        layer.confirm(ptName + "平台的订单【" + row.PlatformOrderId + "】只有已发货状态的订单才能回传多单号，请先取消勾选此类订单再进行操作", { title: '提示', skin: 'wu-dailog' });
                        return;
                    } else if (!isPreview && commonModule.IsRepairSendHistory == false && commonModule.WaitStatusSendedPts.indexOf(o.PlatformType) != -1 && row.ErpState == 'waitsellersend') {
                        // 整单待发货--部分发货--检验--排除已发货的商品
                        var SubWaitLength = row.SubOrders.filter(function (item) {
                            return item.Status == "waitsellersend" && item.checked && ((item.SendedCount ? item.Count - item.SendedCount : item.Count) > 0);
                        }).length

                        var SubSendedLength = row.SubOrders.filter(function (item) {
                            return item.Status == "waitbuyerreceive" && item.checked;
                        }).length

                        if (SubWaitLength == 0) {
                            layer.confirm(ptName + "平台的订单【" + row.PlatformOrderId + "】没有待发货状态的商品", { skin: 'wu-dailog' });
                            return;
                        }

                        if (SubSendedLength > 1) {
                            layer.confirm(ptName + "平台的订单【" + row.PlatformOrderId + "】只有待发货状态的商品才能回传多单号，请先取消勾选已发货的商品再进行操作", { skin: 'wu-dailog' });
                            return;
                        }
                    } else if (!isPreview && commonModule.IsRepairSendHistory == false && o.PlatformType != "KuaiShou" && row.ErpState == 'sended') {
                        layer.alert(ptName + "平台的订单【" + row.PlatformOrderId + "】已发货，目前仅支持【拼多多】、【快手】追加新单号，其他平台请前往其官方后台操作。", { title: "提示", skin: 'wu-dailog', area: '500px' });
                        return;
                    }

                    // 选择的商品信息
                    for (var j = 0; j < o.OrderItems.length; j++) {
                        var oid = o.OrderItems[j];
                        $(row.SubOrders).each(function (k, item) {
                            if (item.OrderItemId == oid)
                                tmpOrder.SubOrders.push(item);
                        });
                    }
                    manyWybOrders.push(tmpOrder);
                }
            }

            var manyCodeSendModel = [];

            var checkManyCodeOrders = JSON.parse(JSON.stringify(manyWybOrders));
            checkManyCodeOrders.forEach(function (oItem) {
                var WaybillCodeModels = [];

                // 获取最新的同一批次打印
                var lastNewCode = oItem.WaybillCodes.find(function (wItem) {
                    return wItem.WaybillCode == oItem.LastWaybillCode;
                });

                // 过滤已打印和回收失败的单号
                oItem.WaybillCodes = oItem.WaybillCodes.filter(function (wItem) {
                    return wItem.Status == 1 || wItem.Status == 4 || commonModule.IsRepairSendHistory
                });

                // 获取Items
                var sendProduct = checkOrders.find(function (cItem) {
                    return oItem.Id == cItem.Id;
                });

                // 获取已选择的Items的OrderItemId集合
                var ItemsOrderItemIds = sendProduct.Items.map(function (sItem) {
                    return sItem.OrderItemId - 0
                });

                // 过滤发货未选择的底单商品
                oItem.WaybillCodes.forEach(function (wItem) {
                    wItem.WaybillCodeOrderProducts = wItem.WaybillCodeOrderProducts.filter(function (pItem) {
                        return ItemsOrderItemIds.indexOf(pItem.OrderItemId) > -1
                    });
                });

                // 汇总全部底单商品数据
                var allWaybillCodeOrderProducts = [];
                oItem.WaybillCodes.forEach(function (wItem, wIndex) {
                    if (wItem.WaybillCodeOrderProducts && wItem.WaybillCodeOrderProducts.length > 0) {
                        // MasterId为0 --直接用索引进行分组
                        if (wItem.WaybillCodeOrderProducts[0].MasterId == 0) {
                            wItem.WaybillCodeOrderProducts.forEach(function (aItem) {
                                aItem.MasterId = wIndex;
                            });
                        }

                        wItem.WaybillCodeOrderProducts.forEach(function (aItem) {
                            allWaybillCodeOrderProducts.push(aItem);
                        });
                    }
                });

                oItem.WaybillCodes.forEach(function (wItem) {
                    if (wItem.WaybillCodeOrderProducts && wItem.WaybillCodeOrderProducts.length > 0) {
                        var IsErr = false;
                        var ErrMsg = null;

                        for (var i = 0; i < wItem.WaybillCodeOrderProducts.length; i++) {
                            var orderItemId = wItem.WaybillCodeOrderProducts[i].OrderItemId;
                            var fData = sendProduct.Items.find(function (fItem) {
                                return fItem.OrderItemId == orderItemId;
                            });

                            if (fData) {
                                var Quantity = 0;
                                allWaybillCodeOrderProducts.forEach(function (aItem) {
                                    if (aItem.OrderItemId == orderItemId) {
                                        Quantity += aItem.Quantity
                                    }
                                });

                                if (fData.Quantity < Quantity) {
                                    IsErr = true;
                                    ErrMsg = "发货选择的数量和底单对不上";
                                    break;
                                }
                            } else {
                                IsErr = true;
                                ErrMsg = "发货没选择这个商品";
                                break;
                            }
                        }

                        WaybillCodeModels.push({
                            MasterId: wItem.WaybillCodeOrderProducts[0].MasterId,
                            WaybillCode: wItem.WaybillCode,
                            WaybillCodeOrderProducts: wItem.WaybillCodeOrderProducts,
                            IsErr: IsErr,
                            IsLatestBatchCode: lastNewCode ? lastNewCode.CreateDate == wItem.CreateDate : true,
                            ErrMsg: ErrMsg,
                        })
                    }
                });

                if (WaybillCodeModels.length > 0) {
                    var abnormalOrderList = WaybillCodeModels.filter(function (wItem) {
                        return wItem.IsErr == true && wItem.ErrMsg == "发货选择的数量和底单对不上"
                    });

                    // 如果全是异常数据，则需要保留最新的一个单号进行匹配回显
                    if (abnormalOrderList.length == WaybillCodeModels.length) {
                        WaybillCodeModels[0].IsErr = false;
                        WaybillCodeModels[0].ErrMsg = "";
                    }
                }

                var model = {
                    Id: oItem.Id,
                    PlatformOrderId: oItem.PlatformOrderId,
                    LogicOrderId: oItem.LogicOrderId,
                    WaybillCodeModels: WaybillCodeModels
                }
                manyCodeSendModel.push(model);
            });


            var unKSmanyWybOrders = [];
            manyWybOrders.forEach(function (item, index) {
                if (manyCodeSendModel.length == 0 && commonModule.IsRepairSendHistory == false && item.PlatformType == "KuaiShou" && item.PrintState != "1" && item.ErpState != "sended") {
                    unKSmanyWybOrders.push(item);
                }
            });

            if (unKSmanyWybOrders.length > 0) {
                var unKSMsg = "";
                unKSMsg += "<div style='padding:15px;'>";
                unKSmanyWybOrders.forEach(function (item, index) {
                    unKSMsg += '<div style="padding:5px 0">' + '快手平台的订单【<span style="color:#3aadff;">' + item.PlatformOrderId + '</span>】，不是已打印已发货，请取消选择;' + '</div>'

                })
                unKSMsg += "</div>";
                var unKSMsgDailog = layer.open({
                    type: 1,
                    title: "确认", //不显示标题
                    content: unKSMsg,
                    area: ['550px'], //宽高
                    btn: ["忽略不满足订单，继续发货", "取消发货"],
                    skin: 'unKSMsgDailog-target',
                    maxHeight: 350,
                    yes: function () {
                        unKSmanyWybOrders.forEach(function (unItem) {
                            manyWybOrders = manyWybOrders.filter(function (item) {
                                return item.PlatformOrderId != unItem.PlatformOrderId
                            });
                        })
                        layer.close(unKSMsgDailog);
                        if (manyWybOrders.length == 0) {
                            layer.msg("无符合条件的订单！")
                        } else {
                            initMoreCodeDrawer();
                        }
                    }
                });
                return;
            }


            // 全选的勾选状态
            function handelSelectStatus (pt) {
                var tableTH = $("#drawer-content" + pt + " .ttCheckAllCodeCheck").length;
                var thActive = $("#drawer-content" + pt + " .ttCheckAllCodeCheck.activeF").length;
                var thActivep = $("#drawer-content" + pt + " .ttCheckAllCodeCheck.activeP").length;

                if (tableTH == thActive) {
                    $("#selectAllTtMany_checkbox" + pt).removeClass("activeP").addClass("activeF");
                } else if (thActivep || (tableTH > thActive && thActive > 0)) {
                    $("#selectAllTtMany_checkbox" + pt).removeClass("activeF").addClass("activeP");
                } else {
                    $("#selectAllTtMany_checkbox" + pt).removeClass("activeF activeP");
                }
            }

            // 商品数量联动检验
            function handeProductNumErr(isSelect, id, waybillCodeId) {
                if (isSelect) {
                    $("#package_content" + id + "_" + waybillCodeId).find(".tt_productSub-select-num_error").remove();
                    $("#package_content" + id + "_" + waybillCodeId).find(".tt_productNum").removeClass("num_error-border_color");
                }

                var selectedIds = [];
                $("#package_content" + id + "_" + waybillCodeId + " .package_content-select_item .tt_productSub").each(function () {
                    var val = $(this).attr("data-val");
                    if (val) {
                        selectedIds.push(val - 0);
                    }
                });

                //console.log(selectedIds)

                if (selectedIds.length > 0) {
                    selectedIds.forEach(function (sid) {
                        var errPackageIds = [];
                        var curPackageId = [];
                        $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + sid + '"]').each(function () {
                            var parentId = $(this).parent().parent().parent().attr("id");
                            if (parentId != "package_content" + id + "_" + waybillCodeId) {
                                if (errPackageIds.indexOf(parentId) == -1) {
                                    errPackageIds.push(parentId);
                                }
                            } else {
                                if (curPackageId.indexOf(parentId) == -1) {
                                    curPackageId.push(parentId);
                                }
                            }
                        });

                        //console.log(errPackageIds)

                        if (errPackageIds.length > 0 && curPackageId.length > 0) {
                            if (isSelect) {
                                var cId = "#" + curPackageId[0];
                                var removeProductNum = $(cId).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                var totalProductNum = $(cId).find('.tt_productSub-select_item[data-orderitemid="' + sid + '"]').attr("data-count");
                                var curProductNum = removeProductNum - 0;

                                errPackageIds.forEach(function (item) {
                                    var strs = item.split('package_content')[1].split('_');
                                    var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                                    if (isCheck) {
                                        var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                        curProductNum += (sibProductNum - 0);
                                    }
                                });

                                // 移除--红色预警
                                if (curProductNum - removeProductNum <= totalProductNum) {
                                    errPackageIds.forEach(function (item) {
                                        $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').parent().siblings(".tt_productSub-select-num_error").remove();
                                        $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                                    });
                                }
                            }
                            // 反选--与新增同理
                            else {
                                var cId = "#" + curPackageId[0];
                                var productNum = $(cId).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                var totalProductNum = $(cId).find('.tt_productSub-select_item[data-orderitemid="' + sid + '"]').attr("data-count");
                                var curProductNum = productNum - 0;

                                errPackageIds.forEach(function (item) {
                                    var strs = item.split('package_content')[1].split('_');
                                    var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                                    if (isCheck) {
                                        var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                        curProductNum += (sibProductNum - 0);
                                    }
                                });

                                // 其他包裹里的商品数量不变--并红色预警
                                errPackageIds.forEach(function (item) {
                                    var errSibLength = $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').parent().siblings().length;
                                    var strs = item.split('package_content')[1].split('_');
                                    var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");

                                    // 当前选择的数量是否大于0
                                    var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                    var isOutZero = sibProductNum > 0;

                                    // 是其父元素的唯一子元素则添加错误提醒--避免重复添加
                                    if (errSibLength == 0 && curProductNum > totalProductNum && isCheck && isOutZero && commonModule.IsRepairSendHistory == false) {
                                        var errorHtml = '<span class="tt_productSub-select-num_error">数量有误</span>';
                                        $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').parent().parent().append(errorHtml);
                                        $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').addClass("num_error-border_color");
                                    }
                                });
                            }
                        }
                    });
                }
            }

            function selectSingleTtMany(id, waybillCodeId, pt, isInit) {
                var isSelect = $("#manyCode-checkbox_" + id + waybillCodeId).hasClass("activeF");
                if (isSelect) {
                    $("#manyCode-checkbox_" + id + waybillCodeId).removeClass("activeF");
                } else {
                    $("#manyCode-checkbox_" + id + waybillCodeId).addClass("activeF");
                }

                var tableTd = $("#moreCodeWrap_tbody" + id + " .n-newCheckbox").length;
                var tdActive = $("#moreCodeWrap_tbody" + id + " .n-newCheckbox.activeF").length;

                if (tableTd == tdActive) {
                    $("#moreCodeWrap_table" + pt + "" + id).find(".ttCheckAllCodeCheck").removeClass("activeP").addClass("activeF");
                } else if (tableTd > tdActive && tdActive > 0) {
                    $("#moreCodeWrap_table" + pt + "" + id).find(".ttCheckAllCodeCheck").removeClass("activeF").addClass("activeP");
                } else {
                    $("#moreCodeWrap_table" + pt + "" + id).find(".ttCheckAllCodeCheck").removeClass("activeF activeP");
                }

                handelSelectStatus(pt);
                manyWybOrders.forEach(function (item) {
                    if (item.Id == id) {
                        item.WaybillCodes.forEach(function (cItem) {
                            if (cItem.WaybillCodeId == waybillCodeId) {
                                cItem.isSelect = !isSelect;
                            }
                        })
                    }
                });

                if (!isInit) {
                    handeProductNumErr(isSelect, id, waybillCodeId);
                }
            }


            var isInitedTp = [];
            var initSelectSingleTtMany = function (pt) {
                if (isInitedTp && isInitedTp.indexOf(pt) != -1) {
                    return;
                }

                if (manyCodeSendModel && manyCodeSendModel.length > 0) {
                    manyWybOrders.forEach(function (item) {
                        if (item.PlatformType == pt) {
                            item.WaybillCodes.forEach(function (cItem) {
                                if (cItem.isSelect) {
                                    selectSingleTtMany(item.Id, cItem.WaybillCodeId, item.PlatformType, true);
                                }
                            });
                        }
                    });

                    if (isInitedTp && isInitedTp.indexOf(pt) == -1) {
                        isInitedTp.push(pt);
                    }
                }
            }

            var initMoreCodeDrawer = function () {
                var moreCodeTmp = $.templates("#upload-many-wybcode-tmplV2");

                manyWybOrders.forEach(function (item) {
                    var SubOrders = JSON.parse(JSON.stringify(item.SubOrders));

                    var onlySubOrders = [];
                    SubOrders.forEach(function (item1) {
                        if (!item1.SendedCount || commonModule.IsRepairSendHistory) {
                            item1.SendedCount = 0;
                        }
                        var remainCount = item1.Count - item1.SendedCount;
                        if ((item1.Status == "waitsellersend" && item1.checked && remainCount > 0) || commonModule.IsRepairSendHistory) {
                            onlySubOrders.push({
                                Color: item1.Color,
                                Size: item1.Size,
                                OrderItemId: item1.OrderItemId,
                                OrderItemCode: item1.OrderItemCode,
                                Count: remainCount,
                                ProductImgUrl: item1.ProductImgUrl ? item1.ProductImgUrl : "/Content/images/nopic.gif",
                                ProductID: item1.ProductID,
                                SkuId: item1.SkuId,
                                PlatformType: item1.PlatformType
                            });
                        }
                    });

                    // 预览时只保留同批次的运单号
                    if (isPreview && manyCodeSendModel && manyCodeSendModel.length > 0 && commonModule.IsRepairSendHistory == false) {
                        var findData = manyCodeSendModel.find(function (sItem) {
                            return sItem.Id == item.Id
                        });

                        var checkWaybillCodes = [];
                    
                        findData.WaybillCodeModels.forEach(function (wItem) {
                            if(wItem.IsLatestBatchCode) {
                                checkWaybillCodes.push(wItem.WaybillCode);
                            }
                        });

                        item.WaybillCodes = item.WaybillCodes.filter(function (wItem) {
                            return checkWaybillCodes.indexOf(wItem.WaybillCode) > -1
                        });
                    }


                    item.WaybillCodes.forEach(function (cItem) {
                        cItem.isSelect = false;
                        cItem.SelectProductList = [];

                        // 运单号智能匹配到的包裹内容

                        if (manyCodeSendModel && manyCodeSendModel.length > 0) {

                            var findData = manyCodeSendModel.find(function (sItem) {
                                return sItem.Id == item.Id
                            });

                            var wData = findData.WaybillCodeModels.find(function (sItem) {
                                return sItem.WaybillCode == cItem.WaybillCode
                            });

                            if (wData) {
                                if (wData.IsErr == true || wData.IsLatestBatchCode == false) {
                                    cItem.isSelect = false;
                                    wData.WaybillCodeOrderProducts = [];
                                } else if (!(item.PlatformType == "KuaiShou" && !isPreview)) {
                                    // 只有匹配的单号才回显包裹内容
                                    wData.WaybillCodeOrderProducts.forEach(function (sItem) {
                                        var subRow = SubOrders.find(function (wItem) {
                                            return wItem.OrderItemId == sItem.OrderItemId
                                        });

                                        if (subRow) {
                                            cItem.isSelect = true;
                                            var Quantity = 1;

                                            var onlyData = SubOrders.find(function (oItem) {
                                                return oItem.OrderItemId == subRow.OrderItemId;
                                            });

                                            if (onlyData) {
                                                if (onlyData.SendedCount) {
                                                    Quantity = onlyData.Count - onlyData.SendedCount;
                                                } else {
                                                    Quantity = onlyData.Count;
                                                }
                                            }

                                            // 存在剩余商品数量才需要回显
                                            if (Quantity > 0) {
                                                var QuantitySelect = [];

                                                if (onlyData.PlatformType == "AlibabaC2M" || onlyData.PlatformType == "TaobaoMaiCaiV2" ) {
                                                    for (var i = 0; i <= Quantity; i++) {
                                                        QuantitySelect.push(i);
                                                    }
                                                } else {
                                                    for (var i = 1; i <= Quantity; i++) {
                                                        QuantitySelect.push(i);
                                                    }
                                                }

                                                // 如果底单商品数量大于剩余发货数量则显示剩余发货数量
                                                var ProductData = {
                                                    Color: subRow && subRow.Color ? subRow.Color : "",
                                                    Size: subRow && subRow.Size ? subRow.Size : "",
                                                    ProductID: subRow && subRow.ProductID ? subRow.ProductID : "",
                                                    Count: sItem.Quantity > Quantity ? Quantity : sItem.Quantity,
                                                    OrderItemId: sItem.OrderItemId,
                                                    OrderItemCode: sItem.OrderItemCode,
                                                    QuantitySelect: QuantitySelect
                                                }

                                                cItem.SelectProductList.push(ProductData);
                                            }
                                        }
                                    });
                                } else {
                                    cItem.isSelect = true;
                                }
                            }
                        }

                        cItem.AllOrderItemIds = cItem.SelectProductList.map(function (sItem) {
                            return sItem.OrderItemId;
                        });

                        cItem.AddSubOrders = onlySubOrders;
                    });
                });

                var TouTiaoOrders = manyWybOrders.filter(function (item) {
                    return item.PlatformType == "TouTiao";
                });

                var TaobaoOrders = manyWybOrders.filter(function (item) {
                    return item.PlatformType == "Taobao";
                });

                var AlibabaC2MOrders = manyWybOrders.filter(function (item) {
                    return item.PlatformType == "AlibabaC2M";
                });

                var TaobaoMaiCaiV2Orders = manyWybOrders.filter(function (item) {
                    return item.PlatformType == "TaobaoMaiCaiV2";
                });

                var KuaiShouOrders = manyWybOrders.filter(function (item) {
                    return item.PlatformType == "KuaiShou";
                });

                var YouZanOrders = manyWybOrders.filter(function (item) {
                    return item.PlatformType == "YouZan";
                });

                var WxVideoOrders = manyWybOrders.filter(function (item) {
                    return item.PlatformType == "WxVideo";
                });

                var XiaoHongShuOrders = manyWybOrders.filter(function (item) {
                    return item.PlatformType == "XiaoHongShu";
                });

                var JingdongOrders = manyWybOrders.filter(function (item) {
                    return item.PlatformType == "Jingdong";
                });

                var OwnShopOrders = manyWybOrders.filter(function (item) {
                    return item.PlatformType == "OwnShop";
                });

                var isShowTabNav = false;
                var PtOrdersLength = [];

                for (var i = 0; i < manyWybOrders.length; i++) {
                    if (PtOrdersLength.length > 1) {
                        break;
                    }
                    if (PtOrdersLength.indexOf(manyWybOrders[i].PlatformType) == -1) {
                        PtOrdersLength.push(manyWybOrders[i].PlatformType);
                    }
                }

                if (PtOrdersLength.length > 1) {
                    isShowTabNav = true;
                }

                var renderData = {
                    "isShowTouTiao": TouTiaoOrders.length > 0,
                    "isShowTaobao": TaobaoOrders.length > 0,
                    "isShowAlibabaC2M": AlibabaC2MOrders.length > 0,
                    "isShowTaobaoMaiCaiV2": TaobaoMaiCaiV2Orders.length > 0,
                    "isShowKuaiShou": KuaiShouOrders.length > 0,
                    "isShowYouZan": YouZanOrders.length > 0,
                    "isShowWxVideo": WxVideoOrders.length > 0,
                    "isShowXiaoHongShu": XiaoHongShuOrders.length > 0,
                    "isShowJingdong": JingdongOrders.length > 0,
                    "isShowOwnShop": OwnShopOrders.length > 0,
                    "isShowTabNav": isShowTabNav,
                    "manyPtOrders": [],
                    "IsPreview": isPreview ? true : false
                }

                if (TouTiaoOrders.length > 0) {
                    renderData.manyPtOrders.push({
                        platformType: "TouTiao",
                        manyWybOrders: TouTiaoOrders
                    });
                }

                if (TaobaoOrders.length > 0) {
                    renderData.manyPtOrders.push({
                        platformType: "Taobao",
                        manyWybOrders: TaobaoOrders
                    });
                }

                if (AlibabaC2MOrders.length > 0) {
                    renderData.manyPtOrders.push({
                        platformType: "AlibabaC2M",
                        manyWybOrders: AlibabaC2MOrders
                    });
                }

                if (TaobaoMaiCaiV2Orders.length > 0) {
                    renderData.manyPtOrders.push({
                        platformType: "TaobaoMaiCaiV2",
                        manyWybOrders: TaobaoMaiCaiV2Orders
                    });
                }

                if (KuaiShouOrders.length > 0) {
                    renderData.manyPtOrders.push({
                        platformType: "KuaiShou",
                        manyWybOrders: KuaiShouOrders
                    });
                }

                if (YouZanOrders.length > 0) {
                    renderData.manyPtOrders.push({
                        platformType: "YouZan",
                        manyWybOrders: YouZanOrders
                    });
                }

                if (WxVideoOrders.length > 0) {
                    renderData.manyPtOrders.push({
                        platformType: "WxVideo",
                        manyWybOrders: WxVideoOrders
                    });
                }

                if (XiaoHongShuOrders.length > 0) {
                    renderData.manyPtOrders.push({
                        platformType: "XiaoHongShu",
                        manyWybOrders: XiaoHongShuOrders
                    });
                }

                if (JingdongOrders.length > 0) {
                    renderData.manyPtOrders.push({
                        platformType: "Jingdong",
                        manyWybOrders: JingdongOrders
                    });
                }

                if (OwnShopOrders.length > 0) {
                    renderData.manyPtOrders.push({
                        platformType: "OwnShop",
                        manyWybOrders: OwnShopOrders
                    });
                }

                var html = moreCodeTmp.render(renderData);
                $("#mainMoreCodeDrawer").html(html);

                var itPt = $("#n_tabNav_pt").find(".active").attr("data-pt");
                var initPt = itPt ? itPt : renderData.manyPtOrders[0].platformType;
                initSelectSingleTtMany(initPt);

                $("#upload-many_code-drawer").addClass("active");
                $("body").css("overflow", "hidden");
            } 

            initMoreCodeDrawer();

            module.closePtManyDrawer = function () {
                $("#upload-many_code-drawer").removeClass("active");
                $("body").css("overflow", "auto");
            }

            // 单选某个运单号
            module.selectSingleTtMany = selectSingleTtMany;

            // 切换平台
            module.onChangeTabNav = function () {
                var pt = $(this).attr("data-pt");
                var isActive = $(this).hasClass("active");
                if (!isActive) {
                    $(this).siblings().removeClass("active");
                    $(this).addClass("active");
                    $("#upload-many_code-drawer .manyPtOrders-all-checkbox-box").hide();
                    $("#drawer-all_checkbox" + pt).css("display", "flex");

                    $("#many-wybcode-drawer-content .moreCodeDailog-content").hide();
                    $("#drawer-content" + pt).show();
                    initSelectSingleTtMany(pt);
                }
            }

            //console.log(manyCodeSendModel)
            //console.log(manyWybOrders)

            // 提交校验一堆逻辑都在这里
            module.submitTdManyDrawer = function () {
                var failManyWybOrders = []; // 勾选运单号未选择包裹集合
                var numfailManyWybOrders = []; // 勾选运单号商品数量错误集合
                var noChooseItems = []; // 没有勾选运单号的集合
                var skuDistributeErrs = []; // 小红书-sku被分配到多个包裹的集合
                var numErrManyWybOrders = []; // 快手-订单商品数量未全部选择的集合
                var skuErrManyWybOrders = []; // 淘工厂--有赞--小红书，已选择的sku未全部选择数量
                var singlePackageOrders = []; // 京东--不支持用单个包裹发货

                manyWybOrders.forEach(function (item) {

                    var WaybillCodes = JSON.parse(JSON.stringify(item.WaybillCodes));

                    // 校验是否存在一个以上的运单号勾选
                    var isSelect = WaybillCodes.some(function (sItem) {
                        return sItem.isSelect == true
                    });

                    // 有勾选了运单号，则要检验勾选的运单号是否选择了包裹
                    if (isSelect) {
                        // 快手批量上传多单号时，无需选择包裹，跳过包裹检验
                        if (item.PlatformType == "KuaiShou" && !isPreview) {
                            return
                        }
                        WaybillCodes.forEach(function (cItem) {
                            // 是否勾选了运单号且未选择包裹内容
                            if (cItem.isSelect && cItem.SelectProductList && cItem.SelectProductList.length == 0) {
                                // 只存没有存过且没有选择包裹的运单号
                                if (failManyWybOrders.indexOf(cItem.WaybillCode) == -1) {
                                    failManyWybOrders.push(cItem.WaybillCode);
                                }
                            }

                            // 是否勾选了运单号且提示包裹数量有误
                            if (cItem.isSelect) {
                                var isErr = $("#package_content" + item.Id + "_" + cItem.WaybillCodeId).find(".tt_productSub-select-num_error").length;
                                if (isErr) {
                                    if (numfailManyWybOrders.indexOf(item.PlatformOrderId) == -1) {
                                        numfailManyWybOrders.push(item.PlatformOrderId);
                                    }
                                }
                            }
                        });

                        // 京东--不支持用单个包裹发完所有商品（非部分发货）
                        if (item.PlatformType == "Jingdong") {
                            var checkCodes = WaybillCodes.filter(function (cItem) {
                                return cItem.isSelect == true;
                            });

                            var isPartSend = item.SubOrders.some(function (sItem) {
                                return sItem.SendedCount && sItem.Count - sItem.SendedCount > 0
                            });

                            if (checkCodes.length == 1 && !isPartSend) {
                                if (singlePackageOrders.indexOf(item.PlatformOrderId) == -1) {
                                    singlePackageOrders.push(item.PlatformOrderId);
                                }
                            }
                        }

                        // 快手-预览发货-订单内的所有商品需一次性发完
                        // 淘工厂--有赞--已选择的sku 数量需要全部发完
                        if ((item.PlatformType == "KuaiShou" && isPreview) || item.PlatformType == "AlibabaC2M" || item.PlatformType == "YouZan" || item.PlatformType == "XiaoHongShu" || item.PlatformType =="TaobaoMaiCaiV2") {
                            // 把所有勾选的运单号过滤出来
                            var checkCodes = WaybillCodes.filter(function (cItem) {
                                return cItem.isSelect == true;
                            });

                            var curSubOrders = checkCodes[0].AddSubOrders;
                            var selectedProductList = []; // 该订单已选择的商品集合

                            checkCodes.forEach(function (cItem) {
                                cItem.SelectProductList.forEach(function (sItem) {
                                    var fIndex = selectedProductList.findIndex(function (fItem) {
                                        return fItem.OrderItemId == sItem.OrderItemId
                                    });

                                    selectedProductList.push(sItem);

                                    // 小红书-商品sku不能被分配到多个包裹的
                                    if (fIndex > -1 && item.PlatformType == "XiaoHongShu") {
                                        var curData = curSubOrders.find(function (curItem) {
                                            return curItem.OrderItemId == sItem.OrderItemId
                                        });

                                        skuDistributeErrs.push(curData);
                                    }
                                });
                            });

                            var filterRepeatCodes = []; // 过滤重复的id, 相同的累计

                            selectedProductList.forEach(function (sItem) {
                                var fIndex = filterRepeatCodes.findIndex(function (fItem) {
                                    return fItem.OrderItemId == sItem.OrderItemId
                                });

                                if (fIndex == -1) {
                                    filterRepeatCodes.push(sItem)
                                } else {
                                    filterRepeatCodes[fIndex].Count += sItem.Count;
                                }
                            });

                            // 判断filterRepeatCodes 是否与 curSubOrders 相等
                            // 先判断是否都选择了全部的商品
                            if (filterRepeatCodes.length == curSubOrders.length) {
                                // 再判断数量是否全部选择
                                var IsNoSame = filterRepeatCodes.some(function (aItem) {
                                    return curSubOrders.findIndex(function (sItem) {
                                        return sItem.OrderItemId == aItem.OrderItemId && sItem.Count == aItem.Count
                                    }) == -1
                                });

                                if (IsNoSame) {
                                    var curData = curSubOrders.find(function (curItem) {
                                        return filterRepeatCodes.findIndex(function (sItem) {
                                            return sItem.OrderItemId == curItem.OrderItemId && sItem.Count == curItem.Count
                                        }) == -1
                                    });

                                    if (item.PlatformType == "KuaiShou") {
                                        numErrManyWybOrders.push(curData);
                                    } else {
                                        skuErrManyWybOrders.push(curData);
                                    }
                                }
                            } else {
                                // 快手--缺少部分商品未选择发货--需要一次性把所有商品发出
                                if (item.PlatformType == "KuaiShou") {

                                    // 已选的商品
                                    var filterOrderItemIds = filterRepeatCodes.map(function (fItem) {
                                        return fItem.OrderItemId;
                                    });

                                    var curData = curSubOrders.find(function (curItem) {
                                        return filterOrderItemIds.indexOf(curItem.OrderItemId) == -1;
                                    });

                                    numErrManyWybOrders.push(curData);
                                } else {
                                    // 淘工厂--有赞--已选择的sku 数量需要全部发完
                                    var curData = filterRepeatCodes.find(function (curItem) {
                                        return curSubOrders.findIndex(function (sItem) {
                                            return sItem.OrderItemId == curItem.OrderItemId && sItem.Count == curItem.Count
                                        }) == -1
                                    });

                                    if (curData) {
                                        var curItem = curSubOrders.find(function (sItem) {
                                            return sItem.OrderItemId == curData.OrderItemId
                                        });

                                        skuErrManyWybOrders.push(curItem);
                                    }
                                }
                            }
                        }
                    } else {
                        noChooseItems.push(item.PlatformOrderId);
                    }
                });

                if (noChooseItems.length == manyWybOrders.length) {
                    layer.msg("请至少选择一个订单和包裹")
                    return;
                }

                if (failManyWybOrders.length > 0 && commonModule.IsRepairSendHistory == false) {
                    var newMsg = "";
                    failManyWybOrders.forEach(function (item) {
                        newMsg += '<div style="marging-bottom:10px;">' + '运单号【<span style="color:#3aadff;">' + item + '</span>】未选择包裹内容' + '</div>'
                    });
                    layer.confirm(newMsg, { area: '450px', skin: 'wu-dailog' });
                    return;
                }

                if (numfailManyWybOrders.length > 0 && commonModule.IsRepairSendHistory == false) {
                    var newMsg = "";
                    numfailManyWybOrders.forEach(function (item) {
                        newMsg += '<div style="marging-bottom:10px;">' + '订单【<span style="color:#3aadff;">' + item + '</span>】商品数量有误' + '</div>'
                    });
                    layer.confirm(newMsg, { area: '450px', skin: 'wu-dailog' });
                    return;
                }

                if (singlePackageOrders.length > 0 && commonModule.IsRepairSendHistory == false) {
                    var msgHtml = "根据京东平台要求多单号发货不支持用单个包裹发完所有商品。<br/>";
                    msgHtml += '订单' + singlePackageOrders[0] + '，请使用 【普通发货】 功能进行操作';
                    layer.alert(msgHtml, { title: "提示", skin: 'wu-dailog', area: '450px' });
                    return;
                }

                if (skuDistributeErrs.length > 0 && commonModule.IsRepairSendHistory == false) {
                    var skuData = skuDistributeErrs[0];

                    var msgHtml = '小红书平台的同个商品，必须放在一个包裹里。<br/><span class="wu-weight600">';
                    if (skuData.Color || skuData.Size) {
                        msgHtml += "【";
                        if (skuData.Color && skuData.Size) {
                            msgHtml += (skuData.Color + " | " + skuData.Size)
                        } else if (skuData.Color) {
                            msgHtml += skuData.Color
                        } else if (skuData.Size) {
                            msgHtml += skuData.Size
                        }
                        msgHtml += "】";
                    } else {
                        msgHtml += ("【商品ID：" + skuData.ProductID + "】");
                    }
                    
                    msgHtml += '已被分配到多个包裹。请将它们全部放在一个包裹里。</span>';
                    layer.alert(msgHtml, { title: "分配错误", skin: 'wu-dailog', area: '500px', btn: ["返回修改"] });
                    return;
                }

                if (skuErrManyWybOrders.length > 0 && commonModule.IsRepairSendHistory == false) {
                    var skuData = skuErrManyWybOrders[0];

                    var msgHtml = '同个商品需一次性发完，请在本次发货中选择全部数量。<br/><span class="wu-weight600">';
                    if (skuData.Color || skuData.Size) {
                        msgHtml += "【";
                        if (skuData.Color && skuData.Size) {
                            msgHtml += (skuData.Color + " | " + skuData.Size)
                        } else if (skuData.Color) {
                            msgHtml += skuData.Color
                        } else if (skuData.Size) {
                            msgHtml += skuData.Size
                        }
                        msgHtml += "】";
                    } else {
                        msgHtml += ("【商品ID：" + skuData.ProductID + "】");
                    }

                    msgHtml += '未选择全部商品数量。</span>';
                    layer.alert(msgHtml, { title: "提示", skin: 'wu-dailog', area: '450px' });
                    return;
                }


                if (numErrManyWybOrders.length > 0 && commonModule.IsRepairSendHistory == false) {
                    var skuData = numErrManyWybOrders[0];

                    var msgHtml = '订单内的所有商品需一次性发完，请选择全部商品。<br/><span class="wu-weight600">';
                    if (skuData.Color || skuData.Size) {
                        msgHtml += "【";
                        if (skuData.Color && skuData.Size) {
                            msgHtml += (skuData.Color + " | " + skuData.Size)
                        } else if (skuData.Color) {
                            msgHtml += skuData.Color
                        } else if (skuData.Size) {
                            msgHtml += skuData.Size
                        }
                        msgHtml += "】";
                    } else {
                        msgHtml += ("【商品ID：" + skuData.ProductID + "】");
                    }

                    msgHtml += '您有部分商品未选择发货。</span>';
                    layer.alert(msgHtml, { title: "提示", skin: 'wu-dailog', area: '450px' });
                    return;
                }

                manyWybOrders.forEach(function (item) {
                    checkOrders.forEach(function (checkItem) {
                        if (item.Id == checkItem.Id) {
                            checkItem.MultiPackSendModels = [];
                            item.WaybillCodes.forEach(function (cItem) {
                                if (checkItem.PlatformType == "KuaiShou" && !isPreview) {
                                    if (cItem.isSelect) {
                                        item.SubOrders.forEach(function (subItem) {
                                            var objExpress = {};
                                            objExpress.ExpressCompanyCode = cItem.ExpressCpCode;
                                            objExpress.PlatformExpressCode = null;
                                            objExpress.PlatformExpressName = null;
                                            objExpress.WaybillCode = cItem.WaybillCode;
                                            objExpress.WaybillCodeUniqueKey = cItem.UniqueKey;
                                            objExpress.TotalWeight = cItem.TotalWeight;
                                            objExpress.Count = subItem.SendedCount ? subItem.Count - subItem.SendedCount : subItem.Count;
                                            objExpress.LogicOrderItemId = subItem.OrderItemId;
                                            objExpress.OrderItemId = subItem.OrderItemId;
                                            objExpress.OrderItemCode = subItem.OrderItemCode;
                                            checkItem.MultiPackSendModels.push(objExpress);
                                        });
                                    }
                                }
                                else {
                                    if (cItem.SelectProductList.length > 0 && cItem.isSelect) {
                                        cItem.SelectProductList.forEach(function (sItem) {
                                            var objExpress = {};
                                            objExpress.ExpressCompanyCode = cItem.ExpressCpCode;
                                            objExpress.PlatformExpressCode = null;
                                            objExpress.PlatformExpressName = null;
                                            objExpress.WaybillCode = cItem.WaybillCode;
                                            objExpress.WaybillCodeUniqueKey = cItem.UniqueKey;
                                            objExpress.TotalWeight = cItem.TotalWeight;
                                            objExpress.Count = sItem.Count;
                                            objExpress.LogicOrderItemId = sItem.OrderItemId;
                                            objExpress.OrderItemId = sItem.OrderItemId;
                                            objExpress.OrderItemCode = sItem.OrderItemCode;
                                            checkItem.MultiPackSendModels.push(objExpress);
                                        });
                                    }
                                }
                            });
                        }
                    })
                })

                //过滤
                var lastOrders = [];
                var sfFreeShippingLogicOrderIds = [];
                checkOrders.forEach(function (checkItem) {
                    if (checkItem.MultiPackSendModels != undefined && checkItem.MultiPackSendModels.length > 0) {
                        lastOrders.push(checkItem);
                        var checkResult = module.CheckSfFreeShippingOrderInMultiPack(checkItem);
                        if (checkResult == false) {
                            sfFreeShippingLogicOrderIds.push(checkItem.LogicOrderId);
                        }
                    }
                });

                if (lastOrders.length == 0) {
                    layer.msg("请至少选择一个订单和包裹")
                    return;
                }

                var pStatus = lastOrders[0].PlatformStatus == "waitsellersend";

                if (manyCodeSendModel && manyCodeSendModel.length > 0 && (isPreview || pStatus || commonModule.IsRepairSendHistory)) {

                    var sendModel = JSON.parse(JSON.stringify(manyCodeSendModel));

                    sendModel.forEach(function (item) {

                        var row = lastOrders.find(function (aItem) {
                            return aItem.Id == item.Id;
                        });

                        if (row) {
                            var mCodes = []; // 该订单下所勾选的运单号
                            row.MultiPackSendModels.forEach(function (mItem) {
                                if (mCodes.indexOf(mItem.WaybillCode) == -1) {
                                    mCodes.push(mItem.WaybillCode);
                                }
                            });

                            mCodes.forEach(function (mCode) {
                                var wIndex = item.WaybillCodeModels.findIndex(function (wItem) {
                                    return mCode == wItem.WaybillCode;
                                });

                                // 判断WaybillCodeModels是否包含当前运单号
                                if (wIndex == -1) {
                                    item.WaybillCodeModels.push({
                                        MasterId: item.WaybillCodeModels.length,
                                        WaybillCode: mCode,
                                        WaybillCodeOrderProducts: [],
                                    });
                                }
                            });

                            item.WaybillCodeModels = item.WaybillCodeModels.filter(function (wItem) {
                                return mCodes.indexOf(wItem.WaybillCode) > -1;
                            });

                            item.WaybillCodeModels.forEach(function (wItem) {
                                var models = row.MultiPackSendModels.filter(function (mItem) {
                                    return mItem.WaybillCode == wItem.WaybillCode;
                                });

                                var OrderItemIds = models.map(function (mItem) {
                                    return mItem.LogicOrderItemId;
                                });

                                wItem.WaybillCodeOrderProducts = wItem.WaybillCodeOrderProducts.filter(function (pItem) {
                                    return OrderItemIds.indexOf(pItem.OrderItemId) > -1;
                                });


                                models.forEach(function (cItem) {
                                    var findIndex = wItem.WaybillCodeOrderProducts.findIndex(function (sItem) {
                                        return sItem.OrderItemId == cItem.LogicOrderItemId
                                    });

                                    if (findIndex > -1) {
                                        wItem.WaybillCodeOrderProducts[findIndex].Quantity = cItem.Count;
                                    } else {
                                        wItem.WaybillCodeOrderProducts.push({
                                            MasterId: wItem.MasterId,
                                            OrderItemCode: cItem.OrderItemCode,
                                            LogicOrderItemId: cItem.LogicOrderItemId,
                                            OrderItemId: cItem.LogicOrderItemId,
                                            Quantity: cItem.Count
                                        });
                                    }
                                });
                            });
                        }
                    });

                    var findModelList = function (Id) {
                        var model = sendModel.find(function (item) {
                            return item.Id == Id
                        });

                        return model;
                    }

                    lastOrders.forEach(function (item) {
                        var model = findModelList(item.Id);
                        if (model) {
                            item.ManyCodeSendModel = model;
                            item.MultiPackSendModels = undefined;
                        }
                    });
                }

                if (sfFreeShippingLogicOrderIds.length > 0) {
                    waitOrderModule.ShowOrderUprate(function () {
                        //过滤异常单继续发货
                        var _newOrders = [];
                        for (var i = 0; i < lastOrders.length; i++) {
                            if (sfFreeShippingLogicOrderIds.indexOf(lastOrders[i].LogicOrderId) == -1) {
                                _newOrders.push(lastOrders[i]);
                            }
                            else {
                                $('#order-' + lastOrders[i].Index).click();
                            }
                        }
                        if (_newOrders.length > 0)
                            sendLogistic.send(undefined, undefined, undefined, undefined, _newOrders, 1, callbackObj);
                        else
                            layer.msg("当前您所选的订单已被全部过滤");

                    }, "toutiao_sf_free_shipping_for_send", {
                        selectOrders: lastOrders,
                        logicOrderIds: sfFreeShippingLogicOrderIds
                    });
                    return false;
                }    

                // 预览和待发货都走发货接口，已发货走追加包裹接口
                if (isPreview || pStatus || commonModule.IsRepairSendHistory) {
                    var callbackFunc = function () {
                        //清空订单物流选中的商品信息
                        manyWybOrders.forEach(function (item, index) {
                            item.WaybillCodes.forEach(function (cItem, cIndex) {
                                cItem.SelectProductList = []
                            });
                        });
                        module.closePtManyDrawer();
                        layer.closeAll();
                        $("#SeachConditions").click();
                    }

                    var callbackObj = {
                        CallBackFunc: callbackFunc,
                        IsSendManyOrders: true
                    };

                    sendLogistic.send(undefined, undefined, true, undefined, lastOrders, 1, callbackObj, true);
                } else {
                    sendLogistic.appendpack(lastOrders);
                }
            }

            // 显示选择器
            module.onShowProductSubSelect = function () {
                var isShow = $(this).find(".tt_productSub-select").css("display") == "block";
                module.onHideProductSelect();
                event.stopPropagation();
                if (isShow) {
                    return;
                }
                $(this).find(".tt_productSub-select").show();
                $(this).find("i").removeClass("icon-a-chevron-down1x").addClass("icon-a-chevron-up1x");
            }

            // 隐藏选择器
            module.onHideProductSelect = function () {
                $("#many-wybcode-drawer-content .tt_productSub-select").hide();
                $("#many-wybcode-drawer-content .tt_productNum-select").hide();
                $("#many-wybcode-drawer-content .tt_productSub > .icon-a-chevron-up1x").removeClass("icon-a-chevron-up1x").addClass("icon-a-chevron-down1x");
                $("#many-wybcode-drawer-content .tt_productNum > .icon-a-chevron-up1x").removeClass("icon-a-chevron-up1x").addClass("icon-a-chevron-down1x");
            }

            // 选择某个商品
            module.onSelectProductSub = function (id, waybillCodeId, newOrderItemId) {
                event.stopPropagation();
                var isSelect = $(this).hasClass("noselect");
                var isDisabled = $(this).hasClass("disabled");
                if (isSelect || isDisabled) {
                    if (isSelect) {
                        layer.msg("商品已添加，请勿重复选择");
                    }
                    return;
                }

                var $selectSub = $(this).parent().parent();
                var $selectNum = $(this).parent().parent().siblings(":first").children('.tt_productNum');
                $selectSub.removeClass("noselect");
                $selectNum.removeClass("noselect");
                // 商品总数量
                var ProductNum = $(this).attr("data-count");
                var oldOrderItemId = $selectSub.attr("data-val");
                var oldOrderItemCode = "";
                if (oldOrderItemId) {
                    oldOrderItemCode = $(this).siblings('.tt_productSub-select_item[data-orderitemid="' + oldOrderItemId + '"]').attr("data-code");
                }
                var platformType = $(this).attr("data-pt");
                var oldText = $(this).parent().siblings(".tt_productSub-input-text").text();
                var isExist = $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + newOrderItemId + '"]').length;
                var selectedIds = [];  // 该运单号已经添加的商品
                
                // 更换渲染数据
                $(this).addClass("disabled");
                var text = $(this).find(".tt_productSub-select-text").text();
                $selectSub.find(".tt_productSub-input-text").text(text);
                $selectSub.find("i").removeClass("icon-a-chevron-up1x").addClass("icon-a-chevron-down1x");
                $selectSub.find(".tt_productSub-select").hide();
                $selectSub.attr("data-val", newOrderItemId);

                // 该运单号下的包裹内容选择器禁用该商品

                $("#package_content" + id + "_" + waybillCodeId + " .package_content-select_item .tt_productSub").each(function () {
                    var val = $(this).attr("data-val");
                    if (val) {
                        selectedIds.push(val - 0);
                    }
                });

                $("#package_content" + id + "_" + waybillCodeId + " .package_content-select_item .tt_productSub").each(function () {
                    $(this).children().children().each(function () {
                        var oid = $(this).attr("data-orderitemid") - 0;
                        if (selectedIds.indexOf(oid) == -1) {
                            $(this).removeClass("disabled noselect");
                        } else {
                            $(this).addClass("disabled noselect");
                        }
                    });
                });

                // 判断当前运单号是否勾选
                var isCheckWay = $("#manyCode-checkbox_" + id + waybillCodeId).hasClass("activeF");

                // 需要提醒错误的运单号包裹id
                var errPackageIds = [];

                if (isCheckWay) {
                    $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + newOrderItemId + '"]').each(function () {
                        var parentId = $(this).parent().parent().parent().attr("id");
                        if (parentId != "package_content" + id + "_" + waybillCodeId) {
                            if (errPackageIds.indexOf(parentId) == -1) {
                                errPackageIds.push(parentId);
                            }
                        }
                    });
                }

                // console.log(errPackageIds)

                // oldOrderItemId ? 切换商品 : 新增商品
                // 调换商品位置：如果切换的商品只涵盖在一个包裹里
                // 选择的商品存在剩余商品数量，不需要调换（商品总数量 > 当前数量）
                // 选择的商品存在被调换的运单号中，则不需要调换
                // 当前商品与包裹商品直接调换
                var product_num = 1;
                if (platformType == "AlibabaC2M" || platformType =="TaobaoMaiCaiV2") {
                    product_num = 0;
                }

                var isExchange = false;
                var isHave = false;
                if (isExist == 1 && oldOrderItemId && errPackageIds.length == 1 && isCheckWay) {
                    var oId = "#" + errPackageIds[0];
                    var $oldItem = $(oId).find('.tt_productSub[data-val="' + oldOrderItemId + '"]');

                    if ($oldItem.length > 0) {
                        isHave = true;
                    }

                    var $newItem = $(oId).find('.tt_productSub[data-val="' + newOrderItemId + '"]');
                    var selectedNum = $newItem.siblings(":first").children('.tt_productNum').attr("data-val");
                    if (selectedNum == ProductNum) {
                        isExchange = true;
                    }
                }

                // 调换商品
                if (isExist == 1 && oldOrderItemId && errPackageIds.length == 1 && isExchange && isCheckWay && !isHave) {
                    var oId = "#" + errPackageIds[0];
                    $selectNum.find("span").text(ProductNum);
                    $selectNum.attr("data-val", ProductNum);

                    var newHtmlDiv = "";
                    for (var i = product_num; i <= ProductNum; i++) {
                        if (i == ProductNum) {
                            newHtmlDiv += '<div class="active" onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + waybillCodeId + ')">' + i + '</div>'
                        } else {
                            newHtmlDiv += '<div onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + waybillCodeId + ')">' + i + '</div>'
                        }
                    }
                    $selectNum.find(".tt_productNum-select").html(newHtmlDiv);

                    var oldProductNum = $(this).siblings('.tt_productSub-select_item[data-orderitemid="' + oldOrderItemId + '"]').attr("data-count");

                    // 调换--包裹内容
                    var $domId = $(oId).find('.tt_productSub[data-val="' + newOrderItemId + '"]');
                    $domId.children('.tt_productSub-input-text').text(oldText);
                    $domId.find('.tt_productSub-select_item[data-orderitemid="' + newOrderItemId + '"]').removeClass("disabled noselect");
                    $domId.find('.tt_productSub-select_item[data-orderitemid="' + oldOrderItemId + '"]').addClass("disabled noselect");
                    $domId.attr("data-val", oldOrderItemId);

                    // 调换--商品数量--waybillCodeId也需要调换
                    var oldWaybillCodeId = errPackageIds[0].split("_")[2];
                    var htmlDiv = "";
                    for (var i = product_num; i <= oldProductNum; i++) {
                        if (i == oldProductNum) {
                            htmlDiv += '<div class="active" onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + oldWaybillCodeId + ')">' + i + '</div>'
                        } else {
                            htmlDiv += '<div onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + oldWaybillCodeId + ')">' + i + '</div>'
                        }
                    }
                    $domId.siblings(":first").children('.tt_productNum').attr("data-val", oldProductNum);
                    $domId.siblings(":first").children('.tt_productNum').find('span').text(oldProductNum);
                    $domId.siblings(":first").children('.tt_productNum').find(".tt_productNum-select").html(htmlDiv);
                }
                    // 1. 切换的商品存在单个或多个包裹
                    // 2. 把商品拆分在不同包裹
                    // 3. 把商品调换到另一个包裹
                    // 4. 切换后，商品数量默认为1，其他包裹商品数量红色预警
                else if (isExist > 0 && errPackageIds.length > 0 && isCheckWay) {
                    $selectNum.find("span").text(1);
                    $selectNum.attr("data-val", 1);

                    // 重新渲染--商品数量
                    var htmlDiv = "";
                    for (var i = product_num; i <= ProductNum; i++) {
                        if (i == 1) {
                            htmlDiv += '<div class="active" onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + waybillCodeId + ')">' + i + '</div>'
                        } else {
                            htmlDiv += '<div onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + waybillCodeId + ')">' + i + '</div>'
                        }
                    }
                    $selectNum.find(".tt_productNum-select").html(htmlDiv);

                    // 统计当前商品数量，得判断当前商品的数量+1 是否大于商品总数量
                    var curProductNum = 1;
                    errPackageIds.forEach(function (item) {
                        var strs = item.split('package_content')[1].split('_');
                        var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                        if (isCheck) {
                            var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + newOrderItemId + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                            curProductNum += (sibProductNum - 0);
                        }
                    });

                    // 其他包裹里的商品数量不变--并红色预警--且移除自身红色预警todo
                    errPackageIds.forEach(function (item) {
                        var errSibLength = $("#" + item).find('.tt_productSub[data-val="' + newOrderItemId + '"]').parent().siblings().length;
                        var strs = item.split('package_content')[1].split('_');
                        var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");

                        // 当前选择的数量是否大于0
                        var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + newOrderItemId + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                        var isOutZero = sibProductNum > 0;

                        // 是其父元素的唯一子元素则添加错误提醒--避免重复添加
                        if (errSibLength == 0 && curProductNum > ProductNum && isCheck && isOutZero && commonModule.IsRepairSendHistory == false) {
                            var errorHtml = '<span class="tt_productSub-select-num_error">数量有误</span>';
                            $("#" + item).find('.tt_productSub[data-val="' + newOrderItemId + '"]').parent().parent().append(errorHtml);
                            $("#" + item).find('.tt_productSub[data-val="' + newOrderItemId + '"]').siblings(":first").children('.tt_productNum').addClass("num_error-border_color");
                        }
                    });

                    // 自己移除了之后，其他运单号下的该商品是否符合预期数量？
                    if (oldOrderItemId) {
                        if (ProductNum >= curProductNum) {
                            $selectNum.removeClass("num_error-border_color");
                            $selectSub.parent().siblings(".tt_productSub-select-num_error").remove();
                        }

                        // 该订单下是否还存在选择了该商品的运单号
                        var oldIsExist = $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + oldOrderItemId + '"]').length;
                        if (oldIsExist > 0) {
                            var oldCurProductNum = 0;
                            $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + oldOrderItemId + '"]').each(function () {
                                var parentId = $(this).parent().parent().parent().attr("id");
                                var strs = parentId.split('package_content')[1].split('_');
                                var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                                if (isCheck) {
                                    var sibProductNum = $(this).siblings(":first").children('.tt_productNum').attr("data-val");
                                    oldCurProductNum += (sibProductNum - 0);
                                }
                            });

                            var oldProductNum = $(this).siblings('.tt_productSub-select_item[data-orderitemid="' + oldOrderItemId + '"]').attr("data-count");

                            // 统计当前商品数量，得判断当前商品的数量是否大于商品总数量
                            if (oldCurProductNum <= oldProductNum) {
                                $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + oldOrderItemId + '"]').each(function () {
                                    $(this).siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                                    $(this).parent().siblings(".tt_productSub-select-num_error").remove();
                                });
                            }
                        }
                    }
                }
                else {
                    $selectNum.find("span").text(ProductNum);
                    $selectNum.attr("data-val", ProductNum);

                    var htmlDiv = "";
                    for (var i = product_num; i <= ProductNum; i++) {
                        if (i == ProductNum) {
                            htmlDiv += '<div class="active" onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + waybillCodeId + ')">' + i + '</div>'
                        } else {
                            htmlDiv += '<div onclick="waitOrderModule.onSelectProductNum.bind(this)(' + id + ',' + waybillCodeId + ')">' + i + '</div>'
                        }
                    }
                    $selectNum.find(".tt_productNum-select").html(htmlDiv);
                    if ($selectNum.hasClass("num_error-border_color")) {
                        $selectNum.removeClass("num_error-border_color");
                        $selectSub.parent().siblings(".tt_productSub-select-num_error").remove();
                    }

                    // 自己移除了之后，其他运单号下的该商品是否符合预期数量？
                    if (oldOrderItemId && isCheckWay) {
                        // 该订单下是否还存在选择了该商品的运单号
                        var oldIsExist = $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + oldOrderItemId + '"]').length;
                        if (oldIsExist > 0) {
                            var oldCurProductNum = 0;
                            $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + oldOrderItemId + '"]').each(function () {
                                var parentId = $(this).parent().parent().parent().attr("id");
                                var strs = parentId.split('package_content')[1].split('_');
                                var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                                if (isCheck) {
                                    var sibProductNum = $(this).siblings(":first").children('.tt_productNum').attr("data-val");
                                    oldCurProductNum += (sibProductNum - 0);
                                }
                            });

                            var oldProductNum = $(this).siblings('.tt_productSub-select_item[data-orderitemid="' + oldOrderItemId + '"]').attr("data-count");

                            // 统计当前商品数量，得判断当前商品的数量是否大于商品总数量
                            if (oldCurProductNum <= oldProductNum) {
                                $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + oldOrderItemId + '"]').each(function () {
                                    $(this).siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                                    $(this).parent().siblings(".tt_productSub-select-num_error").remove();
                                });
                            }
                        }
                    }
                }

                var newOrderItemCode = $(this).attr("data-code");
                if (oldOrderItemId) {
                    manyWybOrders.forEach(function (item) {
                        if (item.Id == id) {
                            item.WaybillCodes.forEach(function (cItem) {
                                // 切换商品
                                if (cItem.WaybillCodeId == waybillCodeId) {
                                    cItem.SelectProductList.forEach(function (sItem) {
                                        if (sItem.OrderItemId == oldOrderItemId) {
                                            sItem.OrderItemId = newOrderItemId - 0
                                            sItem.OrderItemCode = newOrderItemCode
                                            if (isExist == 1 && oldOrderItemId && errPackageIds.length == 1 && isExchange && isCheckWay && !isHave) {
                                                sItem.Count = ProductNum - 0;
                                            } else {
                                                sItem.Count = isExist > 0 && errPackageIds.length > 0 && isCheckWay ? 1 : ProductNum - 0;
                                            }
                                        }
                                    });
                                }

                                // 调换商品
                                if (isCheckWay && isExchange && !isHave && isExist == 1 && oldOrderItemId && errPackageIds.length == 1 && cItem.WaybillCodeId == errPackageIds[0].split("_")[2]) {
                                    cItem.SelectProductList.forEach(function (sItem) {
                                        if (sItem.OrderItemId == newOrderItemId) {
                                            sItem.OrderItemId = oldOrderItemId - 0
                                            sItem.OrderItemCode = oldOrderItemCode
                                            sItem.Count = oldProductNum - 0
                                        }
                                    });
                                }
                            });
                        }
                    });
                }
                // 未选择时--添加商品
                else {
                    manyWybOrders.forEach(function (item) {
                        if (item.Id == id) {
                            item.WaybillCodes.forEach(function (cItem) {
                                if (cItem.WaybillCodeId == waybillCodeId) {

                                    var fIndex = cItem.SelectProductList.findIndex(function (fItem) {
                                        return fItem.OrderItemId == newOrderItemId;
                                    });

                                    if (fIndex == -1) {
                                        cItem.SelectProductList.push({
                                            OrderItemId: newOrderItemId - 0,
                                            OrderItemCode: newOrderItemCode,
                                            Count: isExist > 0 && errPackageIds.length > 0 && isCheckWay ? 1 : ProductNum - 0
                                        });
                                    }
                                }
                            });
                        }
                    });
                }

                // 是否支持回传
                var isCheck = $("#choosewaybill_status_" + waybillCodeId).find(".icon-a-check1x").length;
                if (!isCheck) {
                    var hasHtml = '<i style="color: #1a1a1a;" class="iconfont icon-a-check1x"></i>';
                    $("#choosewaybill_status_" + waybillCodeId).empty().append(hasHtml);
                }
            }

            // 添加某个运单号下的商品选择器
            module.addMoreProductSelect = function (id, waybillCodeId) {
                var orderRow = manyWybOrders.find(function (item) {
                    return item.Id == id;
                });

                var waybillCodeRow = orderRow.WaybillCodes.find(function (item) {
                    return item.WaybillCodeId == waybillCodeId;
                });

                var AddSubOrders = waybillCodeRow.AddSubOrders;

                var selectedIds = [];
                // 已经选择的的OrderItemId
                $("#package_content" + id + "_" + waybillCodeId + " .tt_productSub").each(function () {
                    var val = $(this).attr("data-val");
                    if (val) {
                        selectedIds.push(val);
                    }
                });

                var addSubOrders = [];
                AddSubOrders.forEach(function (item) {
                    if (selectedIds.indexOf(item.OrderItemId) == -1) {
                        addSubOrders.push(item.OrderItemId);
                    }
                });
                

                if (addSubOrders.length == 0) {
                    layer.msg("暂无可添加的商品");
                    return;
                }

                var selectLength = $("#package_content" + id + "_" + waybillCodeId + " .package_content-select_item").length;
                if (AddSubOrders.length == selectLength) {
                    layer.msg("该运单号最多可添加" + AddSubOrders.length + "个包裹");
                    return;
                }

                $(this).remove();

                var newSelectHtml = "";
                newSelectHtml += '<div class="package_content-select_item">';
                newSelectHtml += '<div style="display: flex; align-items: center;">';
                newSelectHtml += '<div onclick="waitOrderModule.onShowProductSubSelect.bind(this)()" class="tt_productSub noselect">';
                newSelectHtml += '<span class="tt_productSub-input-text">请选择商品</span>';
                newSelectHtml += '<i class="iconfont icon-a-chevron-down1x"></i>';
                newSelectHtml += '<div class="tt_productSub-select" style="padding-bottom: 0px;">';
                for (var i = 0; i < AddSubOrders.length; i++) {
                    var orderItemCode =  AddSubOrders[i].OrderItemCode + "";
                    var orderItemId = AddSubOrders[i].OrderItemId + "";
                    var count = AddSubOrders[i].Count;
                    var productImgUrl = AddSubOrders[i].ProductImgUrl;
                    var color = AddSubOrders[i].Color;
                    var size = AddSubOrders[i].Size;
                    var productId = AddSubOrders[i].ProductID;
                    var platformType = AddSubOrders[i].PlatformType + "";

                    if (selectedIds.indexOf(orderItemId) > -1) {
                        newSelectHtml += '<div style="margin-bottom: 8px;" onclick="waitOrderModule.onSelectProductSub.bind(this)(' + id + ',' + waybillCodeId + ',' + orderItemId + ')" data-pt="' + platformType + '" data-count="' + count + '" data-orderitemid="' + orderItemId + '" data-code="' + orderItemCode + '" class="disabled noselect tt_productSub-select_item">';
                    } else {
                        newSelectHtml += '<div style="margin-bottom: 8px;" onclick="waitOrderModule.onSelectProductSub.bind(this)(' + id + ',' + waybillCodeId + ',' + orderItemId + ')" data-pt="' + platformType + '" data-count="' + count + '" data-orderitemid="' + orderItemId + '" data-code="' + orderItemCode + '" class="tt_productSub-select_item">';
                    }

                    newSelectHtml += '<div style="display: flex; font-size: 14px; line-height: 20px;">';
                    if (productImgUrl) {
                        newSelectHtml += '<img style="width: 56px; height: 56px; border-radius: 4px; margin-right: 8px; border: 0.5px solid rgba(0, 0, 0, 0.14);" src="' + productImgUrl + '">';
                    } else {
                        newSelectHtml += '<img style="width: 56px; height: 56px; border-radius: 4px; margin-right: 8px; border: 0.5px solid rgba(0, 0, 0, 0.14);" src="/Content/images/nopic.gif">';
                    }

                    newSelectHtml += '<div style="display: flex; width: 100%; justify-content: space-between; padding: 6px 0; ">';
                    newSelectHtml += '<div style="display: flex; flex-direction: column;">';
                    newSelectHtml += '<div class="tt_productSub-select-text">';
                    if (color || size) {
                        if (color && size) {
                            newSelectHtml += color + ' | ' + size
                        } else if (color) {
                            newSelectHtml += color
                        } else if (size) {
                            newSelectHtml += size
                        }
                    } else {
                        newSelectHtml += '商品ID：' + productId
                    }
                    newSelectHtml += '</div>';

                    if (size) {
                        newSelectHtml += '<span>' + size + '</span>';
                    }

                    newSelectHtml += '</div>';

                    newSelectHtml += '<span class="tt_productSub-select-num">x' + count + '</span>';
                    newSelectHtml += '</div></div></div>';
                }
                newSelectHtml += '</div></div>';
                newSelectHtml += '<div style="display: flex; align-items: center; margin-left: 16px;">';
                newSelectHtml += '<span style="color: #1a1a1a;width: 14px;">x</span>';
                newSelectHtml += '<div onclick="waitOrderModule.onShowProductNumSelect.bind(this)()" class="tt_productNum noselect" data-val="0">'
                newSelectHtml += '<span>0</span>';
                newSelectHtml += '<i class="iconfont icon-a-chevron-down1x"></i>';
                newSelectHtml += '<div class="tt_productNum-select">';
                newSelectHtml += '</div></div></div>';

                newSelectHtml += '<div class="package_content-movebtn" style="display: flex; align-items: center;">';
                newSelectHtml += '<i onclick="waitOrderModule.removeProductSelect.bind(this)(' + id + ',' + waybillCodeId + ')" class="iconfont icon-a-minus-circle1x" style="color: #EA572E; cursor: pointer; margin-right: 8px;"></i>';
                newSelectHtml += '<i onclick="waitOrderModule.addMoreProductSelect.bind(this)(' + id + ',' + waybillCodeId + ')" class="iconfont icon-a-add-circle1x1" style="color: #0888ff; cursor: pointer;"></i>';
                newSelectHtml += '</div></div></div>';

                $("#package_content" + id + "_" + waybillCodeId).append(newSelectHtml);
            }

            // 移除某个运单号下的商品选择器
            module.removeProductSelect = function (id, waybillCodeId) {
                var val = $(this).parent().siblings('.tt_productSub').attr("data-val");
                var siblingsLength = $(this).siblings().length;

                if (val) {
                    $("#package_content" + id + "_" + waybillCodeId + " .tt_productSub-select_item").each(function () {
                        if ($(this).attr("data-orderitemid") == val) {
                            $(this).removeClass("disabled noselect");
                        }
                    });

                    // 需要移除错误的运单号包裹id
                    var errPackageIds = [];
                    $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + val + '"]').each(function () {
                        var parentId = $(this).parent().parent().parent().attr("id");
                        if (parentId != "package_content" + id + "_" + waybillCodeId) {
                            if (errPackageIds.indexOf(parentId) == -1) {
                                errPackageIds.push(parentId);
                            }
                        }
                    });

                    //console.log(errPackageIds)

                    // 统计当前商品数量，得判断当前商品的数量 - 移除的商品数量，是否小于等于商品总数量
                    if (errPackageIds.length > 0) {
                        var removeProductNum = $(this).parent().prev().children('.tt_productNum').attr("data-val");
                        var curProductNum = removeProductNum - 0;
                        var totalProductNum = $(this).parent().siblings('.tt_productSub').find('.tt_productSub-select_item[data-orderitemid="' + val + '"]').attr("data-count");
                        
                        errPackageIds.forEach(function (item) {
                            var strs = item.split('package_content')[1].split('_');
                            var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                            if (isCheck) {
                                var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + val + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                curProductNum += (sibProductNum - 0);
                            }
                        });

                        // 移除--红色预警
                        if (curProductNum - removeProductNum <= totalProductNum) {
                            errPackageIds.forEach(function (item) {
                                $("#" + item).find('.tt_productSub[data-val="' + val + '"]').parent().siblings(".tt_productSub-select-num_error").remove();
                                $("#" + item).find('.tt_productSub[data-val="' + val + '"]').siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                            });
                        }
                    }

                    manyWybOrders.forEach(function (item) {
                        if (item.Id == id) {
                            item.WaybillCodes.forEach(function (cItem) {
                                if (cItem.WaybillCodeId == waybillCodeId) {
                                    cItem.SelectProductList = cItem.SelectProductList.filter(function (sItem) {
                                        return sItem.OrderItemId != val;
                                    });
                                }
                            });
                        }
                    });
                }

                $(this).closest('.package_content-select_item').remove();
                var selectLength = $("#package_content" + id + "_" + waybillCodeId + " .package_content-select_item").length;
                if (selectLength == 0) {
                    var emptyHtml = '<i onclick="waitOrderModule.addMoreProductSelect.bind(this)(' + id + ',' + waybillCodeId + ')" class="iconfont icon-a-add-circle1x1" style="color: #0888ff; cursor: pointer;"></i>';
                    $("#package_content" + id + "_" + waybillCodeId).append(emptyHtml);
                    var noneHtml = '<span style="color: #EA572E; line-height: 20px;">未选择商品</span>';
                    $("#choosewaybill_status_" + waybillCodeId).empty().append(noneHtml);
                } else if (siblingsLength > 0) {
                    var emptyHtml = '<i onclick="waitOrderModule.addMoreProductSelect.bind(this)(' + id + ',' + waybillCodeId + ')" class="iconfont icon-a-add-circle1x1" style="color: #0888ff; cursor: pointer;"></i>';
                    $("#package_content" + id + "_" + waybillCodeId).children().last().find(".package_content-movebtn").append(emptyHtml);
                }
            }

            // 显示包裹内容--数量选择器
            module.onShowProductNumSelect = function () {
                var isSelect = $(this).hasClass("noselect");
                if (isSelect) {
                    layer.msg("请先选择商品");
                    return;
                }

                var isShow = $(this).find(".tt_productNum-select").css("display") == "block";
                module.onHideProductSelect();
                event.stopPropagation();
                if (isShow) {
                    return;
                }
                $(this).find(".tt_productNum-select").show();
                $(this).find("i").removeClass("icon-a-chevron-down1x").addClass("icon-a-chevron-up1x");
            }

            // 选择商品数量
            module.onSelectProductNum = function (id, waybillCodeId) {
                event.stopPropagation();
                var $select = $(this).parent().parent(".tt_productNum");
                var selectVal = $select.attr("data-val");
                var val = $(this).text();
                $select.find("i").removeClass("icon-a-chevron-up1x").addClass("icon-a-chevron-down1x");
                $(this).parent().hide();

                if (selectVal == val) {
                    return;
                }

                $(this).siblings().removeClass("active");
                $(this).addClass("active");
                $select.attr("data-val", val);
                $select.find("span").text($(this).text());
                var isCheck = $("#manyCode-checkbox_" + id + waybillCodeId).hasClass("activeF");

                var orderItemId = $(this).parent().parent().parent().siblings('.tt_productSub').attr("data-val");
                if (orderItemId) {
                    manyWybOrders.forEach(function (item) {
                        if (item.Id == id) {
                            item.WaybillCodes.forEach(function (cItem) {
                                if (cItem.WaybillCodeId == waybillCodeId) {
                                    cItem.SelectProductList.forEach(function (sItem) {
                                        if (sItem.OrderItemId == orderItemId) {
                                            sItem.Count = val - 0
                                        }
                                    });
                                }
                            });
                        }
                    });

                    if (!isCheck) {
                        return;
                    }

                    var errPackageIds = [];
                    var curPackageId = []; 

                    // 先找出--调整的是哪一个订单--运单号--包裹内容--商品数量
                    $("#moreCodeWrap_tbody" + id).find('.tt_productSub[data-val="' + orderItemId + '"]').each(function () {
                        var parentId = $(this).parent().parent().parent().attr("id");
                        if (parentId != "package_content" + id + "_" + waybillCodeId) {
                            if (errPackageIds.indexOf(parentId) == -1) {
                                errPackageIds.push(parentId);
                            }
                        } else {
                            if (curPackageId.indexOf(parentId) == -1) {
                                curPackageId.push(parentId);
                            }
                        }
                    });

                    //console.log(errPackageIds)
                    //console.log(curPackageId)

                    if (errPackageIds.length > 0 && curPackageId.length > 0) {
                        var sid = orderItemId;
                        var cId = "#" + curPackageId[0];
                        var curProductNum = val -0; // 当前选择的数量
                        // 商品的总数量
                        var totalProductNum = $(cId).find('.tt_productSub-select_item[data-orderitemid="' + sid + '"]').attr("data-count");

                        errPackageIds.forEach(function (item) {
                            var strs = item.split('package_content')[1].split('_');
                            var isCheck = $("#manyCode-checkbox_" + strs[0] + strs[1]).hasClass("activeF");
                            if (isCheck) {
                                var sibProductNum = $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').attr("data-val");
                                curProductNum += (sibProductNum - 0);
                            }
                        });

                        //console.log(curProductNum)
                        //console.log(totalProductNum)

                        // 选择的数量 小于等于总数量了--移除预警
                        if (curProductNum <= totalProductNum) {
                            errPackageIds.forEach(function (item) {
                                $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').parent().siblings(".tt_productSub-select-num_error").remove();
                                $("#" + item).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                            });

                            $(cId).find('.tt_productSub[data-val="' + sid + '"]').parent().siblings(".tt_productSub-select-num_error").remove();
                            $(cId).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                        }
                            // 选择的数量 超出商品总数量--红色预警
                        else if (curProductNum > totalProductNum) {
                            var cId = "#" + curPackageId[0];
                            var errSibLength = $(cId).find('.tt_productSub[data-val="' + sid + '"]').parent().siblings().length;

                            // 是其父元素的唯一子元素则添加错误提醒--避免重复添加
                            if (val == 0) {
                                $(cId).find('.tt_productSub[data-val="' + sid + '"]').parent().siblings(".tt_productSub-select-num_error").remove();
                                $(cId).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').removeClass("num_error-border_color");
                            } else if (errSibLength == 0 && commonModule.IsRepairSendHistory == false) {
                                var errorHtml = '<span class="tt_productSub-select-num_error">数量有误</span>';
                                $(cId).find('.tt_productSub[data-val="' + sid + '"]').parent().parent().append(errorHtml);
                                $(cId).find('.tt_productSub[data-val="' + sid + '"]').siblings(":first").children('.tt_productNum').addClass("num_error-border_color");
                            }
                        }
                    }
                }
            }

            // 全选订单
            module.selectAllTtMany = function () {
                var isChecked = $(this).find(".n-newCheckbox").hasClass("activeF"); // 全选
                var isSelect = $(this).find(".n-newCheckbox").hasClass("activeP"); // 半选
                var pt = $(this).attr("data-pt");
                var isCancel = isChecked || isSelect; // 如果是全选/半选，则取消所有勾选
                $("#drawer-content" + pt).find(".ttCheckAllCodeSpan").each(function () {
                    var id = $(this).attr("data-id");
                    module.ttcheckAllCodeWrapExpress.bind(this)(id, pt, isCancel);
                });
            }

            // 全选某个订单下的运单号
            module.ttcheckAllCodeWrapExpress = function (id, pt, isCancel) {
                var isChecked = $(this).find(".n-newCheckbox").hasClass("activeF");
                var isSelect = $(this).find(".n-newCheckbox").hasClass("activeP");

                if (isCancel) {
                    isChecked = true;
                    isSelect = true;
                }

                if (isChecked) {
                    $(this).find(".n-newCheckbox").removeClass("activeF");
                    if (isCancel) {
                        $(this).find(".n-newCheckbox").removeClass("activeP");
                    }
                } else if (isSelect) {
                    $(this).find(".n-newCheckbox").removeClass("activeP");
                } else {
                    $(this).find(".n-newCheckbox").addClass("activeF");
                }

                if (isSelect) {
                    isChecked = true;
                }

                if (isChecked) {
                    $("#moreCodeWrap_tbody" + id + " .n-newCheckbox").removeClass("activeF");
                } else {
                    $("#moreCodeWrap_tbody" + id + " .n-newCheckbox").addClass("activeF");
                }

                handelSelectStatus(pt);
                
                manyWybOrders.forEach(function (item) {
                    if (item.Id == id) {
                        item.WaybillCodes.forEach(function (cItem) {
                            cItem.isSelect = !isChecked;
                            handeProductNumErr(isChecked, id, cItem.WaybillCodeId);
                        });
                    }
                })
            }

            //校验抖店订单与打印模板关系
            if (!module.CheckTemplate(sendOrders)) { return; }
        }

        $(".second-fahuo-btn").click(function () {
            // 权限校验
            commonModule.FxPermission(function (p) {
                commonModule.CheckPermission(function (success) {
                    if (success) {
                        thisFunc();
                    }
                    else return;
                }, p.OnlineResend);
            });

            var thisFunc = function () {
                // 操作按钮实列
                // 两种方式：打印快递单号、手动快递单号
                console.log("补货设置按钮：" + $(this))
                orderPrintModule.LockButton = $(this);
                sendLogistic.LockButton = $(this);
                console.log("补货设置按钮：" + orderPrintModule.LockButton)

                //选择二次发货类型和数量
                var _select_sendtype = function () {
                    //获取数据内容，渲染模板
                    var selectedOrders = module.getToResendOrders();

                    var afterSalesDetailTmpl = $.templates("#aftersales-detail-tmpl");
                    var html = afterSalesDetailTmpl.render(selectedOrders);
                    $("#aftersales-list-body").html(html);

                    //数量判断，只能输数字且不大于原数量
                    $(".aftersales-count").keyup(function () {
                        var val = $(this).val().replace(/[^\d]/g, '');
                        var ocount = $(this).attr("ocount");
                        if (parseInt(val) <= 0) {
                            val = 1;
                        }
                        if (parseInt(val) > parseInt(ocount)) {
                            val = ocount;
                        }

                        $(this).val(val);
                    });
                    layer.open({
                        type: 1,
                        title: "请选择二次发货类型",
                        content: $('.secondfahuo_sendtype_dailog'),
                        area: '856px',
                        btn: ['确定', '关闭'],
                        skin: 'wu-dailog',
                        offset: '60px',
                        success: function () {
                            $("#sendtype").val("1");
                        },
                        yes: function () {
                            _doresend();
                        },
                        cancel: function () {
                            orderTableBuilder.ResetItemCount();//重置NewCount=-1
                        },
                        end: function () {
                            orderTableBuilder.ResetItemCount();//重置NewCount=-1
                        }
                    });


                    ////绑定包裹多选框
                    //for (x = 0; x < selectedOrders.length; x++) {
                    //    initPackSelectBox(selectedOrders[x].LogicOrderId, selectedOrders[x].Packs);
                    //}
                }

                //选中的订单数量，相同LogicOrderId算一个
                var getOrderCheckNum = function () {
                    var oichxs = $(".aftersales-orderitem-chx:checked");
                    var logicOrderIds = [];
                    if (oichxs != null && oichxs.length > 0) {
                        $(oichxs).each(function (index2, ichx) {
                            var oid = $(this).attr("data-pid");
                            if (logicOrderIds.indexOf(oid) == -1)
                                logicOrderIds.push(oid);

                        });
                    }
                    return logicOrderIds.length;
                }

                var _doresend = function () {
                    $(".secondFahuoDailog-btn").css({ display: "none" });
                    $("input[name='secondFahuo']").attr('checked', false);
                    $(".printBtn").css({ display: "none" });
                    $(".secondFahuoDailog-content").css({ display: "none" });
                    $(".secondFahuoDailog-main").css({ display: "block" });
                    $("#secondFahuo_make").attr("title", "");

                    var sendtype = $("#sendtype").val();
                    if (sendtype == '') {
                        layer.alert("请选择二次发货类型", { skin: 'wu-dailog' });
                        return;
                    }

                    //var orderCheckNum = $(".layui-mytable-tobody .order-chx:checked").length;
                    var orderCheckNum = getOrderCheckNum();
                    if (orderCheckNum == 0) {
                        layer.alert("请选择一条商品", { skin: 'wu-dailog' });
                        return;
                    }
                    else if (orderCheckNum > 1) {  //如果选择的订单大于1
                        $("#secondFahuo_make").addClass("disabled");
                        $("#secondFahuo_make input[name=secondFahuo]").attr("disabled", "disabled");
                        $("#secondFahuo_make").attr("title", "只支持操作单条订单");
                    }
                    else {
                        $("#secondFahuo_make").removeClass("disabled");
                        $("#secondFahuo_make input[name=secondFahuo]").removeAttr("disabled")
                        $("#secondFahuo_make").attr("title", "");
                    }


                    var count = orderTableBuilder.rows.length;
                    for (var i = 0; i < count; i++) {
                        var row = orderTableBuilder.rows[i];
                        if (!row.checked || row.PlatformType != "TouTiao")
                            continue;

                        ////检查包裹Id
                        ////取消检查，可能外部发货的订单，在我们系统无包裹
                        ////var selectPacks = $("#selectPacks-" + row.LogicOrderId).attr("data-values") || "";
                        //var selectPackCount = $("input[name='packid-" + row.LogicOrderId + "']:checked").length;
                        //if (selectPackCount <= 0) {
                        //    layer.alert("头条/抖音平台的订单，请先选择已发货的包裹");
                        //    return;
                        //}
                    }

                    var selectedOrders = orderTableBuilder.getAfterSaleSelections();
                    var isHasNotSupport = selectedOrders.findIndex(function (sItem) {
                        return sItem.PlatformType == "XiaoHongShu" && sItem.Packs && sItem.Packs.length > 1;
                    });

                    if (isHasNotSupport > -1) {
                        layer.alert("小红书平台仅支持选择 1 个快递单号进行二次发货", { skin: 'wu-dailog' });
                        return;
                    }

                    layer.open({
                        type: 1,
                        title: "请选择重新发货方式",
                        content: $('.secondFahuoDailog'),
                        area: "560px", //宽高
                        skin: 'wu-dailog',
                        offset: '120px',
                        success: function () {

                        },
                        btn: false,
                        cancel: function () {

                        }
                    });
                }

                var selectorders = $(".layui-mytable-tobody .order-chx:checked");
                if (selectorders.length < 1) {
                    layer.alert("请选择一条订单", { skin: 'wu-dailog' });
                    return;
                }
                if (orderTableBuilder.isAllNotSelectedOrderItem()) {
                    layer.alert("请选择一条商品", { skin: 'wu-dailog' });
                    return;
                }

                if (parseInt(addTmplInOrderListModule.GetCurrentBalance()) < selectorders.length) {
                    common.ProcessVersionOrderCountErrorCode("ORDER_COUNT", parseInt(addTmplInOrderListModule.GetCurrentBalance()))
                    return;
                }

                var template = addTmplInOrderListModule.GetCurrentTemplate();
                if (template == null) {
                    layer.alert("请选择快递模板", { skin: 'wu-dailog' });
                    return;
                }


                var notMatchStatusOrders = [];
                var notSupport = [];
                var supportPlatforms = ["Taobao", "Pinduoduo", "TouTiao", "KuaiShou", "YouZan", "XiaoHongShu", "Jingdong", "WxVideo", "Other_Heliang", "Other_JuHaoMai", "BiliBili", "TouTiaoSaleShop", "Other_HaoYouDuo"];
                for (var i = 0; i < selectorders.length; i++) {
                    var tempindex = $(selectorders[i]).attr("data-index");
                    var orderitem = orderTableBuilder.rows[tempindex];
                    if (supportPlatforms.indexOf(orderitem.PlatformType) < 0) {
                        notSupport.push(tempindex);
                    }
                    if (orderitem.ErpState != 'sended')
                        notMatchStatusOrders.push(tempindex);
                }
                if (notSupport.length == selectorders.length) {
                    orderPrintModule.SecondFahuoWarn();
                    return;
                }
                else if (notSupport.length > 0 || notMatchStatusOrders.length > 0) {
                    for (var i in notSupport) {
                        $(".order-chx[data-index='" + notSupport[i] + "']").prop("checked", false);
                    }
                    for (var i in notMatchStatusOrders) {
                        $(".order-chx[data-index='" + notMatchStatusOrders[i] + "']").prop("checked", false);
                    }

                    selectorders = $(".layui-mytable-tobody .order-chx:checked");
                    if (selectorders.length < 1) {
                        layer.alert("过滤不支持二次发货平台订单和非待收货订单后，无满足条件的订单", { skin: 'wu-dailog' });
                        return;
                    }


                    var o1 = layer.open({
                        type: 1,
                        title: "提示",
                        content: $('.secondFahuoSkipDailog'),
                        btn: ["继续二次发货", "取消"],
                        area: '560px',
                        skin: 'wu-dailog',
                        id: 'secondFahuoSkip_Dailog',
                        success: function () {

                        },
                        btn1: function () {
                            layer.close(o1);
                            _select_sendtype();
                            return true;
                        },
                        btn2: function () {
                            return true;
                        },
                        cancel: function () {

                        }
                    });
                } else {
                    _select_sendtype();
                }
            }
        })

        $("input[name=secondFahuo]").change(function () {
            console.log("二次发货选择打印快递单号")
            var $val = $(this).val();
            if ($val == 0) {
                $(".secondFahuoDailog-content").css({ display: "none" });
                $(".secondFahuoDailog-main").css({ display: "block" });
                $(".secondFahuoDailog-btn").css({ display: "block" });
                $(".printBtn").css({ display: "inline-block" });
                $(".againBtn").css({ display: "none" });


            } else {
                $(".secondFahuoDailog-content").css({ display: "block" });
                $(".secondFahuoDailog-main").css({ display: "none" })
                $(".secondFahuoDailog-btn").css({ display: "block" });
                $(".againBtn").css({ display: "inline-block" });
                $(".printBtn").css({ display: "none" });
                var orderitem = {};
                var orderCheckindex = $($(".layui-mytable-tobody .order-chx:checked")[0]).attr("data-index");
                orderitem = orderTableBuilder.rows[orderCheckindex];
                //绑定物流公司
                commonModule.Ajax({
                    url: "/NewOrder/ExpressCompanyByPlatformType",
                    type: "POST",
                    data: { platformType: orderitem.PlatformType },
                    async: true,
                    success: function (rsp) {
                        if (rsp.Success) {

                            $("#second_experss_tab").empty();
                            $("#second_exoerssnumber_tab").val("");
                            var experssdata = rsp.Data || [];

                            var temphtml = '<option value="">选择物流公司</option>';
                            var selectExpressboxArr = [];

                            for (var i in experssdata) {
                                var expressObj = {};
                                // 淘宝汇通快运转为极兔在前端展示
                                if (orderitem.PlatformType && orderitem.PlatformType == "Taobao" && experssdata[i].CompanyCode.toLocaleLowerCase() == "htky") {
                                    temphtml += ' <option value="JT">极兔快递（原百世快递）</option>';
                                    expressObj.Text = "极兔快递（原百世快递）";
                                    expressObj.Value = "JT";
                                } else {
                                    temphtml += ' <option value="' + experssdata[i].CompanyCode + '">' + experssdata[i].Names + '</option>';
                                    expressObj.Text = experssdata[i].Names;
                                    expressObj.Value = experssdata[i].CompanyCode;
                                }
                                selectExpressboxArr.push(expressObj);
                            }
                            $("#second_experss_tab").html(temphtml);
                            var initExpressSelects = {
                                eles: '#secondSelectExperss',
                                emptyTitle: '选择物流公司', //设置没有选择属性时，出现的标题
                                data: selectExpressboxArr,
                                searchType: 1, //1出现搜索框，不设置不出现搜索框
                                showWidth: '250px', //显示下拉的宽
                                isRadio: true, //有设置，下拉框改为单选
                                allSelect: true,
                                selectCallBack: function (expressData) {
                                    if (expressData.length > 0) {
                                        $("#secondSelectExperss").attr("data-text", expressData[0].Text);
                                    } else {
                                        $("#secondSelectExperss").attr("data-text", "");
                                    }
                                    $("#secondSelectExperss .selectWrap-box").hide();
                                },
                                delTitleListCallBack: function (expressData) {
                                }
                            };
                            var selectBox = new selectBoxModule2();
                            selectBox.initData(initExpressSelects);
                        }
                    }
                });
            }
        })

        //二次发货-手动输入物流信息
        $("#waitOrder_tab_againBtn").click(function () {
            var reindex = $($(".layui-mytable-tobody .order-chx:checked")[0]).attr("data-index");
            orderPrintModule.ConfirmSecondFahuo('2', reindex);
        });

        //二次发货-打印新快递单
        $("#waitOrder_tab_printBtn").click(function () {
            //layer.closeAll();
            //根据是否有模板，决定打开添加模板指引，打开指引，不在往下走
            if (false && newUserGuideCommon.OpenPrintInit()) {
                $(this).removeAttr('processing'); //移除处理中的标记
                return;
            }
            else if (false && addTmplInOrderListModule.GetCurrentTemplate() == null) {

                var html = "";
                html += '<div class="noChooseTemplateDialogs">';
                html += '<ul class="noChooseTemplate-ul clearfix">';
                html += $('#ul_staple_tmpls').html();
                html += '</ul>';
                html += '</div>';
                var noChooseTemplat = layer.open({
                    type: 1,
                    title: "请选择快递模板", //不显示标题
                    content: html,
                    area: ['730px'], //宽高
                    btn: null,// ['下一步'],
                    success: function (layero, index) {
                        $(".noChooseTemplate-ul>li> input[type='radio']").hide();
                        $(".noChooseTemplate-ul>li>#sp_add_template").parent().hide();
                        $(".noChooseTemplate-ul>li").each(function () {
                            var lable = this.lastElementChild;
                            var forAttr = lable.getAttribute('for');
                            lable.removeAttribute('for');
                            this.setAttribute('template_radio_id', forAttr);
                            $(this).on("click", function () {
                                $(".noChooseTemplate-ul>li").removeClass("active");
                                var radio_id = $(this).attr('template_radio_id');
                                $('#' + radio_id).click(); //选中模板
                                $(this).addClass("active");
                                //直接触发打印
                                layer.close(noChooseTemplat);
                                $(".express-print-btn").click();
                            });
                        });
                    },
                    yes: function () {
                        if (!$(".noChooseTemplate-ul>li").hasClass("active")) {
                            layer.msg("请选择快递模板！")
                            return;
                        }
                        $(".express-print-btn").click();
                        layer.close(noChooseTemplat);

                    }
                });

                $(this).removeAttr('processing'); //移除处理中的标记
                return;
            }
            //var orders = orderTableBuilder.getSelections();
            var orders = orderTableBuilder.getAfterSaleSelections();
            console.timeEnd('点击按钮');
            expressPrinter.print(orders, 'Resend');
        })

        //合并订单
        $(".mergered-order").click(function (e) {
            if (commonModule.CloudPlatformType == "Pinduoduo") {
                commonModule.LoadCommonSetting("/FenDan/Pinduoduo/MergerTips", true, function (rsp) {
                    //如果请求失败，直接让走正常合单流程
                    if (!rsp.Success)
                        module.MegeredOrdersHandle();
                    else {
                        if (!rsp.Data) {
                            module.PddMergerOrderTips();
                        }
                        else {
                            //数据格式（第一次点确定的时间,对应最后确认天数）
                            var recordInfo = rsp.Data.split(",");
                            var firstTipsTime = new Date(recordInfo[0]);
                            var num = recordInfo[1];
                            //超过后则走正常流程，且一天只提醒一次，连续三天
                            if (num == -1
                                || Math.abs(new Date() - firstTipsTime) / (1000 * 60 * 60 * 24) > 2
                                || Math.abs(new Date() - firstTipsTime) / (1000 * 60 * 60 * 24) < num) {
                                module.MegeredOrdersHandle();
                            }
                            else {
                                module.PddMergerOrderTips(recordInfo);
                            }
                        }
                    }
                });
            }
            else {
                module.MegeredOrdersHandle();
            }
        });

        //拆分订单
        $(".split-order").click(function (e) {
            // 权限校验
            commonModule.FxPermission(function (p) {
                commonModule.CheckPermission(function (success) {
                    if (success) {
                        thisFunc();
                    }
                    else return;
                }, p.SplitOrderFromMergeredOrder);
            });

            var thisFunc = function () {
                module.BatchSplitOrder();
            }
        });

        //二次发货打印记录
        $(".secondsend-printhistory").click(function (e) {
            commonModule.transferUrlToMainDomain("/Common/Page/PrintHistory-SecondSendList", "_parent");
        });
        //扫描打印
        $(".scan-print").click(function (e) {
            // 权限校验
            commonModule.FxPermission(function (p) {
                commonModule.CheckPermission(function (success) {
                    if (success) {
                        commonModule.transferUrlToMainDomain("/Common/Page/NewOrder-ScanPrint", "_parent");
                    }
                    else return;
                }, p.ExpressPrint);
            });
        });
        // 手工发货
        $('.manual-delivery').click(function () {
            ManualDeliveryDataObj.OrderData = JSON.parse(JSON.stringify(orderTableBuilder.getSelections()));
            var CheckedManualDeliveryTbData = ManualDeliveryDataObj.OrderData;

            if (!CheckedManualDeliveryTbData.length) {
                layer.msg('请选择订单');
                return false;
            }
            if (CheckedManualDeliveryTbData.length > 50) {
                layer.msg('最多仅支持同时操作50单');
                return false;
            }
            var hasErpStateWaitSellerSend = CheckedManualDeliveryTbData.some(function (item) {
                return item.PlatformStatus !== 'waitsellersend';
            });
            if (hasErpStateWaitSellerSend) {
                layer.msg('非待发货订单无法手工发货');
                return false;
            }
            getExpressCompanyData();
            renderManualDeliveryData(CheckedManualDeliveryTbData);
            layer.open({
                type: 1,
                title: '手工发货',
                content: $("#manual_delivery_dialog"),
                offset: '60px',
                area: ['888px', '560px'], // 宽高
                skin: 'n-skin n-manual-delivery',
                success: function () {
                    $("#selectWrap_selectExpress").closest(".n-inputWrap").removeClass("warnInput");
                },
                btn: ['取消', '发货'],
                btn2: function (index) {
                    if (!ManualDeliveryDataObj.CompanyCode) {
                        $("#selectWrap_selectExpress").closest(".n-inputWrap").addClass("warnInput");
                        return false;
                    }
                    if (checkSubmitAddTrackingNo()) {
                        return false;
                    }
                    layer.close(index); // 关闭弹层
                    if (taobaoPreSaleSkipCheck(CheckedManualDeliveryTbData, ManualDeliveryDataObj)) {
                        sendLogistic.manualSend(ManualDeliveryDataObj);
                    }
                },
                end: function () {
                    ManualDeliveryDataObj = {
                        IsManual: true,
                        CompanyCode: '',
                        OrderData: [],
                    }
                }
            });
        });
        //淘宝判断现货订单，预售订单发货提示
        function taobaoPreSaleSkipCheck(list, ManualDeliveryDataObj) {
            var taobaoPreSaleOrdersIds = []; //淘宝距离最晚发货时间大于48小时的预售订单
            var taobaoSoonSendOrderIds = []; //淘宝付款时间不足3小时的订单
            var selectedOrderWithSubCount = 0;
            for (var i = 0; i < list.length; i++) {
                var row = list[i];
                //淘宝预售订单校验
                if (row.PlatformType == "Taobao" && commonModule.HasTag(row.OrderTags, "TaoBaoPreOrder", "Order")) {
                    for (var j = 0; j < row.SubOrders.length; j++) {
                        var subOrder = row.SubOrders[j];
                        if (subOrder.checked != true) {
                            continue;
                        }
                        var isPreSaleOrder = false;
                        selectedOrderWithSubCount += subOrder.OrderItems ? subOrder.OrderItems.length : 1;
                        // 合并 - 订单支付时间
                        if (row.MergeredType == 3 || row.MergeredType == 4) {
                            subOrder.PayTime = row.PayTime
                        }
                        //现货订单判断
                        var payTime = new Date(subOrder.PayTime);
                        var lastShipTime = new Date(subOrder.LastShipTime);
                        //预售订单判断
                        if (lastShipTime > Date.now() && new Date().DateDiff("h", lastShipTime) > 48) {
                            //预售单距离当前时间大于48小时
                            taobaoPreSaleOrdersIds.push(subOrder.PlatformOrderId)
                        }
                        else if (payTime.DateDiff("h", Date.now()) < 3) {
                            //支付之间在3小时内的
                            taobaoSoonSendOrderIds.push(subOrder.PlatformOrderId)
                        }
                    }
                }
            }
            // 七天不提醒
            var preSaleOrdersSkipTag = commonModule.getStorage("taobao_preSaleOrdersSkipTag");
            //淘宝判断现货订单，预售订单发货提示
            
            if (preSaleOrdersSkipTag != "1" && (taobaoPreSaleOrdersIds.length > 0 || taobaoSoonSendOrderIds.length > 0)) {
                var html = "";
                if (taobaoPreSaleOrdersIds.length > 0 && taobaoSoonSendOrderIds.length > 0) {
                    html = "已选择<span style='color: #3aadff;margin: 0px 2px;'>" + selectedOrderWithSubCount + "</span>个订单，其中<span  style='color: #fe6f4f;margin: 0px 2px;'>" + taobaoSoonSendOrderIds.length + "</span>个现货订单<span style='color: #fe6f4f;margin: 0px 2px;'>距离付款时间不足3小时</span>，其中<span  style='color: #fe6f4f;margin: 0px 2px;'>" + taobaoPreSaleOrdersIds.length + "</span>个预售订单<span  style='color: #fe6f4f;margin: 0px 2px;'>距离最晚发货时间大于48小时</span>，是否跳过这类订单继续发货？"
                } else if (taobaoPreSaleOrdersIds.length > 0) {
                    html = "已选择<span style='color: #3aadff;margin: 0px 2px;'>" + selectedOrderWithSubCount + "</span>个订单，其中<span  style='color: #fe6f4f;margin: 0px 2px;'>" + taobaoPreSaleOrdersIds.length + "</span>个预售订单<span style='color: #fe6f4f;margin: 0px 2px;'>距离最晚发货时间大于48小时</span>，是否跳过这类订单继续发货？"
                } else {
                    html = "已选择<span style='color: #3aadff;margin: 0px 2px;'>" + selectedOrderWithSubCount + "</span>个订单，其中<span  style='color: #fe6f4f;margin: 0px 2px;'>" + taobaoSoonSendOrderIds.length + "</span>个现货订单<span style='color: #fe6f4f;margin: 0px 2px;'>距离付款时间不足3小时</span>，是否跳过这类订单继续发货？"
                }
                var tipHtml = "<p style='font-size: 13px;color: #9E9E9E;'>订单发货后24小时无揽收记录，可能会被平台判定虚假发货，客诉后有赔付风险。</p>"
                var dialogSendTips = layer.open({
                    type: 1,
                    title: "提示",
                    content: "<div style='font-size:14px;line-height:20px;'><div><p style='margin-bottom:5px;font-weight: 600;'>" + html + "</p>" + tipHtml + '</div></div>',
                    btn: ["跳过，继续", "忽略，继续", "取消"],
                    skin: 'wu-dailog',
                    area: ['550px', '250px'],
                    success: function (layero, index, that) {
                        var ignoreBtnHtml = '<div style="top: 212px;left: 20px;position: absolute;cursor: pointer"><div><input type="checkbox"id="skipTopBtn"style="margin-right:5px;cursor: pointer;"><span onClick="sendLogistic.switchTaobaoPreSaleTip(event)">本周内不再提醒</span></div></div>';
                        layero.find(".layui-layer-content").after(ignoreBtnHtml);
                    },
                    btn1: function () {
                        if ($("#skipTopBtn:checked").length > 0) {
                            commonModule.setStorage("taobao_preSaleOrdersSkipTag", "1", new Date().DateDiff("n", new Date().getNextMonday()));
                        }
                        var newOrders = []
                        for (var i = 0; i < list.length; i++) {
                            var curOrder = list[i];
                            var hasSoonAndPreSaleOrder = false;
                            for (var ii = 0; ii < curOrder.SubOrders.length; ii++) {
                                var curSubOrder = curOrder.SubOrders[ii];
                                if (taobaoPreSaleOrdersIds.includes(curSubOrder.PlatformOrderId) || taobaoSoonSendOrderIds.includes(curSubOrder.PlatformOrderId)) {

                                    //$("#order-" + curOrder.Id).click();
                                    // $("#order-" + curOrder.Id).removeClass("onClickColor");
                                    // $("input[data-id='" + curOrder.Id + "']").prop("checked", false);
                                    // orderTableBuilder.rows[curOrder.Index].checked = false;

                                    hasSoonAndPreSaleOrder = true;
                                    break;
                                }

                            }
                            if (!hasSoonAndPreSaleOrder) {
                                newOrders.push(curOrder)
                            }

                        }
                        if (newOrders.length > 0) {
                            sendLogistic.manualSend(ManualDeliveryDataObj);
                            return true;
                        }
                        else {
                            layer.closeAll();
                            layer.msg("过滤后无可发货订单！");
                            return false;
                        }
                    },
                    btn2: function () {
                         if ($("#skipTopBtn:checked").length > 0) {
                             commonModule.setStorage("taobao_preSaleOrdersSkipTag", "1", new Date().DateDiff("n", new Date().getNextMonday()));
                         }
                        sendLogistic.manualSend(ManualDeliveryDataObj);
                        return true;
                    },
                    btn3: function () {
                        return true;
                    }
                });
                return false;
            }
            
            return true;
        }


        //创建线下单
        $(".offlineOrder-create-btn").click(function (e) {
            // 权限校验
            commonModule.FxPermission(function (p) {
                commonModule.CheckPermission(function (success) {
                    if (success) {
                        OfflineOrderImportType = '1';
                        quicklyCreateOfflineOrderDailog();
                    }
                    else return;
                }, p.ImportOfflineOrder);
            });
        });


        layui.form.render("select");
    }
    // 快捷创建线下单
    function quicklyCreateOfflineOrderDailog() {
        var titleHtml = '<span class="wu-f14 wu-c09 wu-weight600">快捷创建线下单</span><span class="wu-f14 wu-c06">（本页面录入订单暂不支持推送给厂家代发打印，如有代发需求请前往<span onclick="waitOrderModule.tarUrlOffline()" class="wu-color-a wu-operate">代发线下单</span>功能）</span>';
        layer.open({
            type: 1,
            title: titleHtml,
            content: $("#offlineOrderDailog"),
            area: '1050px', //宽高
            btn: null,
            skin: 'wu-dailog',
            success: function (layero, index) {
                if (currentPt == 'toutiao') {
                    $(".showToutiaoAssociatedPlatformOrder").show();
                    $(".showToutiaoAssociatedTip").show();
                    if (OfflineOrderImportType == '2') {
                        $("#changeOfflineTabs li:nth-child(2)").addClass("active").siblings("li").removeClass("active");
                        $("#associatedPlatformOrderWrap").show();
                        $("#automaticIdentificationAddress").hide();
                    } else {
                        $("#changeOfflineTabs li:nth-child(1)").addClass("active").siblings("li").removeClass("active");
                        $("#associatedPlatformOrderWrap").hide();
                        $("#automaticIdentificationAddress").show();
                    }
                } else {
                    $(".showToutiaoAssociatedPlatformOrder").hide();
                    $(".showToutiaoAssociatedTip").hide();
                    $("#changeOfflineTabs li:nth-child(1)").addClass("active").siblings("li").removeClass("active");
                    $("#associatedPlatformOrderWrap").hide();
                    $("#automaticIdentificationAddress").show();
                }
                $("#changeOfflineTabs>li").on("click", function () {
                    var id = $(this).attr("data-id");
                    OfflineOrderImportType = id;
                    $(this).addClass("active").siblings("li").removeClass("active");
                    if (id == '2') {
                        $(".recipientInfoContent .wu-inputWrap").addClass("disabled");
                        $(".recipientInfoContent .wu-inputWrap").find(".wu-input").css("border", "1px solid rgba(0, 0, 0, 0.14)");
                        $(".showToutiaoAssociatedTip").hide();
                        $("#automaticIdentificationAddress").hide();
                        $("#associatedPlatformOrderWrap").show();
                        $("#toutiao_platform_address_wrap").show();
                        $("#other_platform_address_wrap").hide();
                    } else {
                        $(".recipientInfoContent .wu-inputWrap").removeClass("disabled");
                        if (currentPt == 'toutiao') {
                            $(".showToutiaoAssociatedTip").show();
                        }
                        $("#automaticIdentificationAddress").show();
                        $("#associatedPlatformOrderWrap").hide();
                        $("#toutiao_platform_address_wrap").hide();
                        $("#other_platform_address_wrap").show();
                    }
                });
                waitOrderModule.OffileOrderReset();
            },
            end: function () {
                OfflineOrderImportType = '1';
                $(".showToutiaoAssociatedPlatformOrder").hide();
                $(".showToutiaoAssociatedTip").hide();
            },
            yes: function () { }
        });
    }
    //function removeCitySuffix(value) {
    //    var cities = ['北京市', '上海市', '天津市', '重庆市'];
    //    // 检查输入的城市是否在列表中
    //    if (cities.includes(value)) {
    //        return value.slice(0, -1);
    //    }
    //    return value;
    //}

    // 关联
    handleAssociatedOrder = function () {
        var value = $("#associated_platform_order_no").val().trim();
        commonModule.Ajax({
            url: "/NewOrder/GetOrderInfo?platformOrderId=" + value,
            type: "GET",
            data: {},
            success: function (rsp) {
                if (rsp.Success) {
                    var resData = rsp.Data;
                    $("#input_buyer_address input[name=Name]").val(resData.ToName);
                    $("#input_buyer_address input[name=Mobile]").val(resData.ToPhone);
                    $("#input_buyer_address input[name=associationProvince]").val(resData.ToProvince);
                    $("#input_buyer_address input[name=associationCity]").val(resData.ToCity);
                    $("#input_buyer_address input[name=associationArea]").val(resData.ToCounty);
                    //var provinceValue = removeCitySuffix(resData.ToProvince);
                    //var isExistsVal = $("#input_buyer_address select[id=fromProvince-select]").find("option[value='" + provinceValue + "']").length;
                    //if (isExistsVal > 0) {
                    //    $("#input_buyer_address select[id=fromCity-select]").attr("data-value", resData.ToCity).val(resData.ToCity).closest(".wu-selectWrap").removeClass("wu-active");
                    //    $("#input_buyer_address select[id=fromArea-select]").attr("data-value", resData.ToCounty).val(resData.ToCounty).closest(".wu-selectWrap").removeClass("wu-active");
                    //    $("#input_buyer_address select[id=fromProvince-select]").attr("data-value", provinceValue).val(provinceValue).change();
                    //}
                    $("#input_buyer_address input[name=addr]").val(resData.ToAddress);
                    $("#input_buyer_address input[name=inpAddr]").val(resData.ToFullAddress);
                }
                else {
                    wuFormModule.wu_toast({ type: 2, content: rsp.Message });
                }
            }
        });
    }
    var initQueryDateBox = function (startDate, endDate) {
        var obj = {
            days: DefaultQueryDays, //天数
            startDate: null, //startDate可以不用填 不用填调用客户端时间  格式：yyyy-MM-dd
            endDate: commonModule.ServerNowDate //endDate可以不用填 不用填调用客户端时间      格式：yyyy-MM-dd
        };
        if (startDate != undefined && startDate != "" && endDate != undefined && endDate != "") {
            obj = {
                days: DefaultQueryDays, //天数
                startDate: startDate,
                endDate: endDate
            };
        }
        commonModule.InitNewCalenderTime("#inputSelectTime", obj);
    }
    // 校验快递单号-是否为空
    function checkSubmitAddTrackingNo() {
        var isReturn = false;
        $(".manual-tracking-number-wrap .n-layui-input").each(function (index, item) {
            var value = $(item).val();
            if (!value) {
                $(item).closest(".manual-tracking-number-wrap").addClass("manual-tracking-warn-input");
                isReturn = true;
                return isReturn;
            }
        });
        return isReturn;
    }
    // 渲染手工发货列表数据
    function renderManualDeliveryData(list) {
        var tplt = $.templates("#manual_delivery_table_body_data");
        var html = tplt.render({
            data: list,
        });
        $("#manual_delivery_tb_tbody").html(html);
        if (list.length === 1) {
            $(".removeSendWrap").find('i').addClass("removeSendIcon");
        } else {
            $(".removeSendWrap").find('i').removeClass("removeSendIcon");
        }
    }
    // 删除手工发货数据
    removeManualDeliveryRowData = function (index) {
        var dataIndex = Number(index);
        var ordersList = ManualDeliveryDataObj.OrderData;
        if (ordersList.length === 1) {
            return false;
        }
        ordersList.splice(dataIndex, 1);
        commonModule.w_alert({ type: 4, content: '删除成功' });
        renderManualDeliveryData(ordersList);
    }
    mouseoverManualDeliveryDeleteTip = function (index) {
        $("#ManualDeliveryDeleteTip" + index).show();
    }
    mouseoutManualDeliveryDeleteTip = function (index) {
        $("#ManualDeliveryDeleteTip" + index).hide();
    }
    // 快递单号输入
    manualTrackingNumberInput = function (id) {
        var inputValue = $(this).val().trim();
        var ordersList = ManualDeliveryDataObj.OrderData;
        if (inputValue) {
            $(this).css('padding-right', '30px').next().show();
        } else {
            $(this).css('padding-right', '0px').next().hide();
        }
        ordersList.forEach(function (item) {
            if (item.Id == id) {
                item.WaybillCode = inputValue;
            }
        });
    }
    // 清除快递单号
    clearTrackingNoInput = function () {
        $(this).closest(".manual-tracking-number-wrap").find(".n-layui-input").val('').next().hide();
    }
    // 快递单号输入框聚焦
    manualTrackingNumberFocus = function () {
        $(this).closest(".manual-tracking-number-wrap").removeClass("manual-tracking-warn-input");
    }


    module.PddMergerOrderTips = function (recordInfo) {
        var html = "";
        html += "<div id='system-prompt-xhs-tip' style='font-family: Source Han Sans;'><div style='padding: 0 16px 0px 16px;'><div style='color: #191919;font-size: 14px;line-height: 20px;padding-bottom: 10px;'>拼多多平台订单合并时间范围延长：现支持每周日至下周六时间</div>"
        html += "<div style='color: #191919;font-size: 14px;line-height: 20px;'>范围内，同收件人信息在同店铺的订单进行合并。</div>"
        html += "<div class='system-prompt-xhs-tip-footer'style='margin-top:34px;'><span style='padding-right: 10px;' class='system-prompt-xhs-tip-check-box'><span class='system-prompt-xhs-tip-checkbox'></span>不再提示</span><span class='system-prompt-xhs-tip-btn2'>确认</span></div></div></div>"

        var dialogIndex = layer.open({
            type: 1,
            title: "系统提示",
            btn: false,
            resize: false,
            skin: 'n-skin',
            content: html,
            area: ["488px", "217px"], //宽高
            success: function () {
                //$(".n-skin").css("border-radius", "8px");
                //$("#system-prompt-xhs-tip").parent('.layui-layer-content').siblings('.layui-layer-title').css({
                //    'font-family': 'Source Han Sans', 'font-size': "14px", "font-weight": "500", "line-height": "44px", "color": "#181818",
                //    "padding-left": "16px", "height": "44px", "box-sizing": "border-box", "border-radius": "8px 8px 0px 0px"
                //});

                $('.system-prompt-xhs-tip-check-box').on("click", function () {
                    if ($(this).find('i').length > 0) {
                        $(this).find('.system-prompt-xhs-tip-checkbox').empty().css({ 'border': '1px solid #dbdbdb', 'background-color': '#fff' });
                    } else {
                        $(this).find(".system-prompt-xhs-tip-checkbox").css({ 'border': 'none', 'background-color': '#0888ff' }).append('<i class="iconfont icon-a-check1x"></i>')
                    }
                });

                $('.system-prompt-xhs-tip-btn2').on("click", function () {
                    var isTrue = $(".system-prompt-xhs-tip-checkbox").find("i").length > 0;
                    var date = new Date();
                    var year = date.getFullYear(); // 获取当前年份，例如：2021
                    var month = date.getMonth() + 1; // 获取当前月份，注意需要加1，例如：9
                    var day = date.getDate(); // 获取当前日期，例如：22

                    if (!recordInfo) {
                        recordInfo = year + '-' + month + '-' + day + ',1';
                        if (isTrue) {
                            recordInfo = year + '-' + month + '-' + day + ',-1';
                        }
                        else {
                            recordInfo = year + '-' + month + '-' + day + ',1';
                        }
                        commonModule.SaveCommonSetting("/FenDan/Pinduoduo/MergerTips", recordInfo, function (rsp) { });
                    }
                    else {
                        var firstTipsTime = new Date(recordInfo[0]);
                        var num = parseInt(recordInfo[1]) + 1;
                        if (isTrue) {
                            recordInfo = year + '-' + month + '-' + day + ',-1';
                        }
                        else {
                            recordInfo = year + '-' + month + '-' + day + ',' + num;
                        }
                        commonModule.SaveCommonSetting("/FenDan/Pinduoduo/MergerTips", recordInfo, function (rsp) { });
                    }
                    layer.close(dialogIndex);
                    module.MegeredOrdersHandle();
                });
            },
            end: function () {
                $('.system-prompt-xhs-tip-check-box').off("click");
            }
        });
    }

    module.MegeredOrdersHandle = function () {
        // 权限校验
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.MergerOrder);
        });

        var thisFunc = function () {
            module.MegeredOrders();
        }
    }
    // 跳转到-代发线下单页面
    module.tarUrlOffline = function () {
        window.open(common.rewriteUrlToMainDomain("/Common/Page/NewOrder-OfflineOrder"));
    }

    module.RenderConfirmDialog = function (orders) {
        if (!orders || orders.length == 0) {
            if (orderTableBuilder.isSelectOrder()) {
                layer.alert("请选择您要发货的商品");
            }
            else {
                layer.alert("请选择您要发货的订单");
            }
            return false;
        }

        var manyCodeSendDailogHtml = '<div id="manyCodeSendDailog" style="font-family: Source Han Sans;">';
        manyCodeSendDailogHtml += '<div class="manyCodeSendDailog-content">';
        manyCodeSendDailogHtml += '<div style="flex-direction: column;align-items: unset;">';
        manyCodeSendDailogHtml += '<div style="display: flex; align-items: center;"><label>';
        manyCodeSendDailogHtml += '<input type="radio" name="ManyCodeSendAway" value="0" data-value="0" />';
        manyCodeSendDailogHtml += '<span>同一订单允许回传<span style="color: #DC8715;">多个</span>运单号至店铺后台</span></label>';
        manyCodeSendDailogHtml += '<span class="setInfo_right_content_tar">推荐</span></div>';
        manyCodeSendDailogHtml += '<div id="manyCodeSend-set_open" style="margin: 4px 24px 0;display: none;">';
        manyCodeSendDailogHtml += '<div style="font-size: 14px;line-height: 20px;color: #7f7f7f;margin-bottom: 8px;">在发货时请务必仔细核对每个包裹对应的商品和物流单号，确保准确无误</div>';
        manyCodeSendDailogHtml += '<div class="manyCodeSendDailog-content-allow"><div style="display: flex;"><span style="color: #626262;">应用平台</span>';
        manyCodeSendDailogHtml += '<div style="margin-left: 16px;"><label><span class="n-newCheckbox" id="n-newCheckbox-tt"></span>';
        manyCodeSendDailogHtml += '<span style="color: #181818;">抖店/快手/淘宝/视频号/小红书/京东</span></label>';
        manyCodeSendDailogHtml += '<div style="display: flex;align-items: center;margin-top: 16px;"><label><span class="n-newCheckbox" id="n-newCheckbox-c2m"></span>';
        manyCodeSendDailogHtml += '<span style="color: #181818;">淘工厂</span></label>';
        manyCodeSendDailogHtml += '<span style="color: #DC8715;">（同个商品仅支持回传一次）</span></div>'
        manyCodeSendDailogHtml += '<div style="display: flex;align-items: center;margin-top: 16px;"><label><span class="n-newCheckbox" id="n-newCheckbox-pdd"></span>';
        manyCodeSendDailogHtml += '<span style="color: #181818;">拼多多</span></label>';
        manyCodeSendDailogHtml += '</div>';
        manyCodeSendDailogHtml += '<div style="display: flex;align-items: center;margin-top: 16px;"><label><span class="n-newCheckbox" id="n-newCheckbox-yz"></span>';
        manyCodeSendDailogHtml += '<span style="color: #181818;">有赞</span></label>'
        manyCodeSendDailogHtml += '<span style="color: #DC8715;">（此方式仅支持回传一次）</span></div></div></div>'
        manyCodeSendDailogHtml += '<div style="display: flex;align-items: center;margin-top: 16px;white-space: nowrap;"><span style="color: #626262;">追加备注</span>';
        manyCodeSendDailogHtml += '<div style="display: flex;align-items: center;margin-left: 16px;"><label><span class="n-newCheckbox" id="n-newCheckbox-mcr"></span>';
        manyCodeSendDailogHtml += '<span style="color: #181818;">多单号备注</span></label>';
        manyCodeSendDailogHtml += '<span style="color: #181818;">（多单号将自动追加到订单的卖家备注，需要<span style="color: #DC8715;">商家先授权厂家卖家备注）</span></span>'
        manyCodeSendDailogHtml += '</div></div></div></div></div>';
        manyCodeSendDailogHtml += '<div style="margin-top: 16px;">';
        manyCodeSendDailogHtml += '<label><input type="radio" name="ManyCodeSendAway" value="1" data-value="1" />';
        manyCodeSendDailogHtml += '<span>同一订单仅允许回传<span style="color: #DC8715;">一个</span>最新运单号至店铺后台</span>';
        manyCodeSendDailogHtml += '</label></div></div>';
        manyCodeSendDailogHtml += '<div class="manyCodeSendDailog-footer">';
        manyCodeSendDailogHtml += '<label id="many_code-set_default-btn"><span class="n-newCheckbox"></span>';
        manyCodeSendDailogHtml += '<span style="color: #191919;">设置为默认，不再提醒</span></label>';
        manyCodeSendDailogHtml += '<div onclick="waitOrderModule.SendManyCodeWithSetAway()" class="n-mButton">确定</div></div></div>';

        layer.open({
            type: 1,
            title: "多单号发货提醒",
            content: manyCodeSendDailogHtml,
            area: ['720px', '416px'], //宽高
            btn: false,
            resize: false,
            skin: 'n-skin',
            success: function () {
                $("#manyCodeSendDailog").parent().css({ "padding": "0px", "height": "100%" });

                orders.forEach(function (item) {
                    if (["TouTiao", "KuaiShou", "Taobao", "WxVideo", "XiaoHongShu", "Jingdong", "OwnShop"].indexOf(item.PlatformType) != -1) {
                        if ($("#n-newCheckbox-tt").hasClass("activeF") == false) {
                            $("#n-newCheckbox-tt").addClass("activeF");
                        }
                    } else if (item.PlatformType == "AlibabaC2M" || item.PlatformType =="TaobaoMaiCaiV2") {
                        if ($("#n-newCheckbox-c2m").hasClass("activeF") == false) {
                            $("#n-newCheckbox-c2m").addClass("activeF");
                        }
                    } else if (item.PlatformType == "Pinduoduo") {
                        if ($("#n-newCheckbox-pdd").hasClass("activeF") == false) {
                            $("#n-newCheckbox-pdd").addClass("activeF");
                        }
                    } else if (item.PlatformType == "YouZan") {
                        if ($("#n-newCheckbox-yz").hasClass("activeF") == false) {
                            $("#n-newCheckbox-yz").addClass("activeF");
                        }
                    }
                });

                $('#manyCodeSendDailog input[type="radio"]').on('change', function () {
                    var val = $(this).val();
                    if (val == "0") {
                        $("#manyCodeSend-set_open").show();
                    } else {
                        $("#manyCodeSend-set_open").hide();
                    }
                });

                $("#many_code-set_default-btn").on('click', function () {
                    var isCheck = $(this).find(".n-newCheckbox").hasClass("activeF");

                    if (isCheck) {
                        $(this).find(".n-newCheckbox").removeClass("activeF");
                    } else {
                        $(this).find(".n-newCheckbox").addClass("activeF");
                    }
                });

                $("#manyCodeSend-set_open label").on("click", function () {
                    var isCheck = $(this).find(".n-newCheckbox").hasClass("activeF");

                    if (isCheck) {
                        $(this).find(".n-newCheckbox").removeClass("activeF");
                    } else {
                        $(this).find(".n-newCheckbox").addClass("activeF");
                    }
                });
            }, 
            end: function () {
                $('#manyCodeSendDailog input[type="radio"]').off();
                $("#many_code-set_default-btn").off();
                $("#manyCodeSend-set_open label").off();
            }
        });
    }

    module.SendManyCodeWithSetAway = function () {
        var val = $("#manyCodeSendDailog input:checked").attr("data-value");
        if (val == undefined) {
            layer.msg("请选择多单号发货方式");
            return;
        }

        var isSetDefault = $("#many_code-set_default-btn").find(".n-newCheckbox").hasClass("activeF");
        if (isSetDefault) {
            module.SetManyCodeSendAway(val);
            return;
        }

        layer.close(layer.index);
        sendLogistic.BatchSendOrder(undefined, undefined, undefined, undefined, undefined, undefined, val);
    }

    module.SetManyCodeSendAway = function (val) {
        var manyCodeSendAway = "/ErpWeb/SetInfo/ManyCodeSendAway";
        commonModule.Ajax({
            url: "/Common/SaveCommonSetting",
            data: { settingKey: manyCodeSendAway, settingValue: val },
            type: "POST",
            loading: true,
            success: function (rsp) {
                if (!rsp.Success) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                commonModule.ManyCodeSendAway = val;

                if (val == "1") {
                    layer.msg("保存成功", { icon: 1 });
                    layer.close(layer.index);
                    sendLogistic.BatchSendOrder(undefined, undefined, undefined, undefined, undefined, undefined, val);
                    return;
                }
                // 开启多单号设置
                var manyCodeSendConfig = "/ErpWeb/SetInfo/ManyCodeSendConfig";
                var manyCodeSendConfigData = {
                    TouTiao: $("#n-newCheckbox-tt").hasClass("activeF"),
                    AlibabaC2M: $("#n-newCheckbox-c2m").hasClass("activeF"),
                    Pinduoduo: $("#n-newCheckbox-pdd").hasClass("activeF"),
                    YouZan: $("#n-newCheckbox-yz").hasClass("activeF"),
                    ManyCodeToRemarks: $("#n-newCheckbox-mcr").hasClass("activeF")
                }

                commonModule.Ajax({
                    url: "/Common/SaveCommonSetting",
                    data: { settingKey: manyCodeSendConfig, settingValue: JSON.stringify(manyCodeSendConfigData) },
                    type: "POST",
                    loading: true,
                    success: function (res) {
                        if (!res.Success) {
                            layer.msg(res.Message, { icon: 2 });
                            return;
                        }
                        commonModule.manyCodeSendConfigData = manyCodeSendConfigData;
                        layer.msg("保存成功", { icon: 1 });
                        layer.close(layer.index);
                        sendLogistic.BatchSendOrder(undefined, undefined, undefined, undefined, undefined, undefined, val);
                    }
                });
            }
        });
    }

    // ---- 校验抖店快手订单与打印模板关系  ----
    module.CheckTemplate = function (orders) {
        if (orders == undefined)
            return true;
        var isOk = true;
        var curTemplate = addTmplInOrderListModule.GetCurrentTemplate();
        var isExistToutiao = false;
        var isExistKuaishou = false;
        for (var i = 0; i < orders.length; i++) {
            var _order = orders[i];
            if (!isExistToutiao) {
                isExistToutiao = orders[i].PlatformType == "TouTiao";
            }
            if (!isExistKuaishou) {
                isExistKuaishou = orders[i].PlatformType == "KuaiShou";
            }
        }
        var isNotTouTiaTmp = true;
        var isNotKuaishouTmp = true;
        var isNotSiteTmp = true;
        if (curTemplate != null) {
            var templateType = curTemplate.TemplateType;
            if (common.IsTouTiaoTemplate(templateType) || common.IsTouTiaozjTemplate(templateType) || commonModule.IsTouTiaoKuaiYunTemplate(templateType)) {
                isNotTouTiaTmp = false;
            }
            if (common.IsKuaiShouTemplate(templateType)) {
                isNotKuaishouTmp = false;
            }
            if (common.IsSiteTemplate(templateType)) {
                isNotSiteTmp = false;
            }
        }

        if (isExistToutiao && isNotTouTiaTmp) {
            showDkPrintWarn('抖音', 'https://docs.qq.com/doc/DQkhPWnlYWk1ZeUZT');
            return false;
        }
        else if (isExistKuaishou && isNotKuaishouTmp) {
            showDkPrintWarn('快手', 'https://m0ij9216j9.feishu.cn/docx/KQ72d76rvoKU5OxKwnycvA4Ln9d');
            return false;
        }
        return isOk;
    }

    module.AllCheck = function () {
        var $this = $("#allcheckorder")[0];
        var isChecked = $this.checked;
        var cantSendCount = 0;
        var canSendCount = 0;
        $(".layui-mytable .order-chx:visible").each(function (index, chx) {
            var index = $(chx).attr("data-index");
            var row = orderTableBuilder.rows[index];
            //if (row.IsLocked == 1) return;

            if (row.IsCantSendGood && isChecked) {
                chx.checked = false
                row.checked = false; //数据选中标识
                $("#order-" + row.Index).removeClass("onClickColor");
                cantSendCount++;
            }
            else {
                var isCheckTmp = isChecked;
                //// 快递不可达默认不勾选（过滤不可达订单默认勾选）
                //var isFilterReach = $("#ExpressReachBtn").hasClass("active");
                //var $expressReach = $(chx).closest(".layui-row-item").find(".express-reach");
                //if (!isFilterReach && $expressReach.length > 0)
                //    isCheckTmp = false;
                //else if (row.IsFake) {
                //    isCheckTmp = false;
                //}
                //else {
                //    $(row.SubOrders).each(function (i, so) {
                //        // 子订单有退款订单时不勾选
                //        if (so.RefunStatus == "REFUND_SUCCESS" || so.RefundStatus == 'WAIT_SELLER_AGREE') {
                //            isCheckTmp = false;
                //            return;
                //        }
                //    });
                //}
                $(row.SubOrders).each(function (i, so) {
                    // 子订单有退款订单时不勾选
                    if (so.RefunStatus == "REFUND_SUCCESS" || so.RefundStatus == 'WAIT_SELLER_AGREE') {
                        isCheckTmp = false;
                        return;
                    }
                });

                chx.checked = isCheckTmp;
                row.checked = isCheckTmp;
                var $tr = $(chx).closest(".layui-row-item");
                if (isCheckTmp)
                    $tr.addClass("active");
                else
                    $tr.removeClass("active");
                canSendCount++;
            }
        });
        $("#orderNum").text($(".order-chx:checked:visible").length);
        //$("#orderNum").text("[" + (isChecked ? canSendCount : 0) + "]");
    }

    module.AllShowCheck = function () {
        var $this = $("#allshoworder")[0];
        var isChecked = $this.checked;
        var hoSettingKey = "/ErpWeb/WaitPrint/WaitorderAllShow";
        var isCheck = isChecked ? 1 : 0;
        commonModule.SaveCommonSetting(hoSettingKey, isCheck, function (rsp) {
            if (rsp.Success == true) {
                // 同步缓存展开详情状态
                WaitorderAllShow = isCheck;
                if (isChecked) {
                    $(".layui-mytable .layui-mytable-tr-showItems").css({ display: "block" });
                    $(".layui-row-item .icon-zhankai1").addClass("zk");
                    $(".layui-row-item .more-productShow").addClass("zk");
                } else {
                    $(".layui-mytable .layui-mytable-tr-showItems").css({ display: "none" });
                    $(".layui-row-item .icon-zhankai1").removeClass("zk");
                    $(".layui-row-item .more-productShow").removeClass("zk");
                }
            }
        });

    }

    module.AllOrderItemCheckOld = function () {
        var $this = $("#chk_allorderitem")[0];
        var isChecked = $this.checked;
        var hoSettingKey = "/ErpWeb/WaitPrint/CheckedAllOrderItem";
        var isCheck = isChecked ? 1 : 0;
        commonModule.SaveCommonSetting(hoSettingKey, isCheck, function (rsp) {
            if (rsp.Success == true) {
                if (isChecked) {
                    $(".layui-mytable-tobody .orderitem-chx:not(:disabled)").prop("checked", true);

                    $(".layui-mytable-tobody .orderitem-chx:disabled").prop("checked", false);
                    var disabledCount = $(".layui-mytable-tobody .orderitem-chx:disabled").length;
                    if (disabledCount > 0) {
                        layer.msg("已自动过滤" + disabledCount + "条已发货商品");
                    }
                } else {
                    $(".layui-mytable-tobody .orderitem-chx").prop("checked", false);
                }
            }
        });
    }
    module.AllRecoveryItemCheck = function () {
        var $this = $("#recovery_chk_allorderitem")[0];
        var isChecked = $this.checked;
        var hoSettingKey = "/ErpWeb/WaitPrint/CheckedAllOrderItem";
        if (isChecked) {
            $("#recovery-list-body .recovery-chx:not(:disabled)").prop("checked", true);

            $("#recovery-list-body .recovery-chx:disabled").prop("checked", false);

        } else {
            $("#recovery-list-body .recovery-chx").prop("checked", false);
        }
    }

    module.AllOrderItemCheck = function () {
        var $this = $("#chk_allorderitem")[0];
        var isChecked = $this.checked;
        var hoSettingKey = "/ErpWeb/WaitPrint/CheckedAllOrderItem";
        var isCheck = isChecked ? 1 : 0;
        commonModule.SaveCommonSetting(hoSettingKey, isCheck, function (rsp) {
            var disabledCount = 0;
            if (rsp.Success == true) {
                $(orderTableBuilder.rows).each(function (index, row) {
                    $(row.SubOrders).each(function (index2, sub) {
                        if (isChecked) {
                            var disabledSub = $("#orderitem-chx-" + sub.OrderItemId + ":not(:disabled)");
                            disabledSub.prop("checked", true);
                            disabledCount += disabledSub.length;
                            //$("#orderitem-chx-" + sub.OrderItemId + ":disabled").prop("checked", false);
                        } else {
                            $("#orderitem-chx-" + sub.OrderItemId).prop("checked", false);
                        }
                        sub.checked = isChecked;
                    });

                    //3.重新生成打印内容
                    row.PrintInfo = printContentFormatSetModule.ForatPrintContent(row);
                })
                //if (disabledCount > 0) {
                //    layer.msg("已自动过滤" + disabledCount + "条已发货商品");
                //}
            }
        });
    }

    //过滤无需打印订单
    module.LoadFilterNoPrintOrder = function () {
        var $this = $("#FilterNoPrintOrder")[0];
        var isChecked = $this.checked;
        var hoSettingKey = "/ErpWeb/WaitPrint/FilterNoPrintOrder";
        var isCheck = isChecked ? 1 : 0;
        commonModule.SaveCommonSetting(hoSettingKey, isCheck, function (rsp) {
            if (rsp.Success == true) {
                $("#SeachConditions").trigger("click");
            }
        });
    }

    //合并订单
    module.MegeredOrders = function () {

        function _bindCheckBoxEvent() {
            $(".merger-order-chx-all").bind("click", function () {
                var isChecked = this.checked;;
                $(".merger-order-chx").each(function (index, chx) {
                    chx.checked = isChecked;
                })
            });
            $(".merger-order-chx").bind("click", function (e) {
                var isChecked = this.checked;
                var isAll = $(".merger-order-chx:checked").length == $(".merger-order-chx").length;
                $(".merger-order-chx-all")[0].checked = isAll;
                e.stopPropagation();
            });
        }

        //1.勾选的订单，数据校验
        var chooseOrders = [];
        var firstOrderBuyerHashCode = ""; //第一个订单的buyerhashcode
        var firstOrderPrintStatus = "";   //第一个订单的打印状态
        var hasNotSameBuyerHashCode = false; //是否勾选了不一样的BuyerHashCode的订单
        var hasNotSamePrintStatus = false;   //是否勾选了不一样的打印状态的订单（1.已打印和未打印，2.已打印的最后打印的单号不一致）
        var mergerOrderCount = 0;   //是否存在多个合并订单，进行合并。
        var printedOrderCount = 0;  //已打印的订单的个数
        var pddconsolidate = 0;//拼多多集运单计数;
        var pddshiphold = 0;//暂停发货单
        var pddCrossBorderOrder = 0;//跨境单
        var pddWaitAuthOrder = 0;//待审核单
        var toutiaoTransitOrder = 0; //头条物流中转订单
        var kuaishouTransitOrder = 0; //快手物流中转订单
        var alibabaTransitOrder = 0; //阿里巴巴跨境/物流中转订单
        var toutiaoAppointmentShipOrder = 0; //头条预约发货订单
        var toutiaoGiveGiftOrder = 0; //头条送礼订单
        var toutiaoRiskProcessingOrder = 0; //头条风控订单
        var alibabaHyperLinkShipOrder = 0; //阿里巴巴官方仓发订单

        var firstOrderShopId = ""; //第一个订单的店铺ID
        var hasNotSameOrderShop = false; //是否勾选了不一样的BuyerHashCode的订单
        var pddlocalOrder = 0;//拼多多本地仓订单
        var hasJingdongScheduleDelivery = false; //是否勾选了京东预约发货订单
        // 淘大店订单检查
        var hasTaoBigShopCount = 0;
        //清仓订单检查
        var hasQnDistrCount = 0;

        var hasPrintedOrderMsg = "";//跨境 是否已打印并已生成快递单号

        for (var i = 0; i < orderTableBuilder.rows.length; i++) {

            var row = orderTableBuilder.rows[i];
            if (row.checked) {
                if (!!row.Tags && row.Tags.length > 0 && commonModule.HasTag(row.Tags, "appointment_ship_time", "OrderItem")) {
                    toutiaoAppointmentShipOrder++;
                }

                if (row.IsMainOrder == true)
                    mergerOrderCount++; //合并订单

                if (row.PrintState == 1)
                    printedOrderCount++; //已打印的订单

                //是否已打印并已生成快递单号
                if (row.PrintState == 1 && (row.LastWaybillCode != null && row.LastWaybillCode != "")) {
                    hasPrintedOrderMsg += row.PlatformOrderId + ",";
                }


                if (firstOrderBuyerHashCode && firstOrderBuyerHashCode != row.BuyerHashCode) {
                    hasNotSameBuyerHashCode = true;
                    //break;
                }

                if (firstOrderPrintStatus && firstOrderPrintStatus != row.PrintState) {
                    hasNotSamePrintStatus = true;
                    //break;
                }
                ///判断合并订单店铺是否相同
                if (firstOrderShopId && firstOrderShopId != row.ShopId) {
                    hasNotSameOrderShop = true;
                }

                // 判断是否有京东预约发货订单
                if (!hasJingdongScheduleDelivery && row.PlatformType == 'Jingdong' && row.PublishTime) {
                    hasJingdongScheduleDelivery = true;
                }

                firstOrderShopId = row.ShopId;

                firstOrderBuyerHashCode = row.BuyerHashCode;
                firstOrderPrintStatus = row.PrintState;

                //头条送礼单不参与合并
                if (row.PlatformType == "TouTiao" && commonModule.HasTag(row.Tags, "give_gift", "OrderItem")) {
                    toutiaoGiveGiftOrder++;
                }

                //头条风控单不参与合并
                if (row.PlatformType == "TouTiao" && commonModule.HasTag(row.Tags, "risk_processing", "OrderItem")) {
                    toutiaoRiskProcessingOrder++;
                }

                //阿里巴巴官方仓发订单不参与合并
                if (row.PlatformType == "Alibaba" && commonModule.HasTag(row.Tags, "hyperLinkShip", "OrderItem")) {
                    alibabaHyperLinkShipOrder++;
                }

                //拼多多集运不参与合并
                if (commonModule.CloudPlatformType == "Pinduoduo") {
                    if (row.PddConsolidate != undefined && row.PddConsolidate != "null" && row.PddConsolidate != "") {
                        pddconsolidate++;
                    }
                    // 平台待审核单收件人为空
                    if (row.ReceiverIsEmpty == 1) {
                        pddWaitAuthOrder++;
                    }
                }

                //拼多多暂停发货单
                if (commonModule.CloudPlatformType == "Pinduoduo") {
                    if (row.PddShipHold != undefined && row.PddShipHold == "1") {
                        pddshiphold++;
                    }
                }

                //拼多多跨境单
                if (row.IsPddCrossBorderOrder) {
                    pddCrossBorderOrder++;
                }
                //拼多多本地仓订单
                if (row.PlatformType == "Pinduoduo" && row.isPddLocal)
                    pddlocalOrder++;


                //抖店中转订单
                if (row.PlatformType == "TouTiao" && row.IsTouTiaoTransit)
                    toutiaoTransitOrder++;

                //快手中转订单
                if (row.PlatformType == "KuaiShou" && row.IsKuaiShouTransit)
                    kuaishouTransitOrder++;

                //阿里巴巴跨境/中转订单
                if (row.PlatformType == "Alibaba" && row.IsAlibabaTransit)
                    alibabaTransitOrder++;

                // 淘大店订单校验，不与普通单合并
                if (row.PlatformType == "Alibaba" && row.ExtField3 == "TabBigShop") {
                    hasTaoBigShopCount++;
                }
                // 清仓订单校验，不与普通单合并
                if (row.PlatformType == "Taobao" && commonModule.HasTag(row.OrderTags, "clearance_order", "Order")) {
                    hasQnDistrCount++;
                }

                chooseOrders.push(row);
            }
        }

        // 淘大店订单不与普通单合并
        if (hasTaoBigShopCount > 0 && hasTaoBigShopCount != chooseOrders.length) {
            layer.msg("官方店订单不支持和普通订单合并");
            return;

        }

        // 清仓订单不与普通单合并
        if (hasQnDistrCount > 0 && hasQnDistrCount != chooseOrders.length) {
            layer.msg("清仓订单不支持和普通订单合并");
            return;

        }

        // 处理京东预约发货订单
        if (hasJingdongScheduleDelivery) {
            var allJingdong = true;
            var publishTime = null;

            for (var key in chooseOrders) {
                var order = chooseOrders[key];
                if (order.PlatformType !== 'Jingdong' || !order.PublishTime) {
                    allJingdong = false;
                    break;
                }

                var orderPublishDate = new Date(order.PublishTime).setHours(0, 0, 0, 0);
                if (publishTime === null) {
                    publishTime = orderPublishDate;
                } else if (publishTime !== orderPublishDate) {
                    layer.alert("预约发货日期不一致，不允许合并", { skin: 'wu-dailog' });
                    return;
                }
            }

            if (!allJingdong) {
                layer.alert("预约订单不允许与普通订单合并", { skin: 'wu-dailog' });
                return;
            }
        }


        if (mergerOrderCount.length > 1) {
            layer.alert("勾选的订单有多个合并订单，合并订单与合并订单不能再次合并", { skin: 'wu-dailog' });
            return;
        }

        if (toutiaoAppointmentShipOrder > 0 && hasNotSameBuyerHashCode) {
            layer.msg('抖店[预约发货订单]只能与*预约发货时间相同*的[预约发货订单]合并！')
            return;
        }

        if (toutiaoGiveGiftOrder > 0) {
            layer.alert("勾选的订单内包含抖音送礼单，该类订单不支持合单操作", { skin: 'wu-dailog' })
            return;
        }

        if (alibabaHyperLinkShipOrder > 0) {
            layer.alert("仓发订单不支持合并订单");
            return;
        }

        if (toutiaoRiskProcessingOrder > 0) {
            layer.alert("勾选的订单内包含抖音风控订单，该类订单不支持合单操作")
            return;
        }

        //#region 有包含头条中转订单进入的逻辑
        if (toutiaoTransitOrder > 0) {
            if (toutiaoTransitOrder == chooseOrders.length) {
                layer.msg('您选中的所有订单为抖店中转订单官方不支持合并订单！')
                return;
            }
            else if (toutiaoTransitOrder > 0) {
                var html = "";
                html += '<div class="kuaiShouPrintWarn">';
                html += '<div class="kuaiShouPrintWarn-content">';
                html += '<span>您勾选的订单中包含中转订单，部分不支持合并是否忽略中转订单，正常合并其他订单</span>'
                html += '</div>';
                html += '</div>';
                var index = layer.open({
                    type: 1,
                    title: "合并提示", //不显示标题
                    content: html,
                    area: ['500px'], //宽高
                    btn: ['过滤中转订单，继续合并  ', '取消合并'],
                    success: function () {
                        _bindCheckBoxEvent();
                    },
                    btn1: function () {
                        layer.close(index);
                        orderTableBuilder.FilteTransitOrder();
                        module.MegeredOrders();
                    },
                    btn2: function () {
                        return true;
                    }
                });
                return;
            }
        }
        //#endregion

        //#region 有包含快手中转订单进入的逻辑
        if (kuaishouTransitOrder > 0) {
            if (hasNotSameBuyerHashCode && kuaishouTransitOrder == chooseOrders.length) {
                layer.msg('您选中的中转订单为不同收件人信息官方不支持合并订单！')
                return;
            }
            else if (kuaishouTransitOrder != chooseOrders.length) {
                var html = "";
                html += '<div class="kuaiShouPrintWarn">';
                html += '<div class="kuaiShouPrintWarn-content">';
                html += '<span>您勾选的订单中包含中转订单，部分不支持合并是否忽略中转订单，正常合并其他订单</span>'
                html += '</div>';
                html += '</div>';
                var index = layer.open({
                    type: 1,
                    title: "合并提示", //不显示标题
                    content: html,
                    area: ['500px'], //宽高
                    btn: ['过滤中转订单，继续合并  ', '取消合并'],
                    success: function () {
                        _bindCheckBoxEvent();
                    },
                    btn1: function () {
                        layer.close(index);
                        orderTableBuilder.FilteTransitOrder();
                        module.MegeredOrders();
                    },
                    btn2: function () {
                        return true;
                    }
                });
                return;
            }
        }
        //#endregion

        //#region 有包含阿里巴巴跨境订单进入的逻辑
        if (alibabaTransitOrder > 0) {
            layer.msg('跨境转运订单不支持合并')
            return;
        }
        //#endregion

        if (pddlocalOrder > 0) {
            layer.msg('本地仓订单不允许合并')
            return;
        }
        if (printedOrderCount.length > 0) {
            layer.alert("已打印的订单不能参与合并，需先回收已打印的单号再合并", { skin: 'wu-dailog' });
            return;
        }

        //if (hasNotSamePrintStatus) {
        //    layer.alert("勾选的订单打印状态不一致，不满足合并条件", { skin: 'wu-dailog' });
        //    return;
        //}
        if (chooseOrders.length == 0 || chooseOrders.length == 1) {
            layer.alert("请至少勾选两个订单进行合并", { skin: 'wu-dailog' });
            return;
        }

        if (chooseOrders.length - pddconsolidate <= 1) {
            layer.msg('您选中的所有订单包含拼多多特殊集运订单，官方不支持合并订单！', {
                icon: 7,
                time: 3000 //2秒关闭（如果不配置，默认是3秒）
            });
            return;
        }

        if (pddconsolidate > 0) {
            var layerhtml = '<div style="padding:25px;display:flex;justify-content: center;align-items: center;flex-direction: column;font-size:16px;"><div style="line-height:30px">您勾选的订单中包含部分不支持合并订单<s style="color:#fe6f4f;margin-left:3px;">（集运订单不支持合并）</s>，是否忽略集运订单，正常合并其他订单</div></div>';
            layer.open({
                type: 1,
                title: "提示", //不显示标题
                content: layerhtml,
                area: ['540px'], //宽高
                btn: ['忽略，继续合并  ', '取消合并'],
                success: function () {
                    _bindCheckBoxEvent();
                },
                btn1: function () {
                    //2.弹窗让用户确认
                    var dialogData = { ToAddress: '', Orders: [] };
                    for (var i = 0; i < chooseOrders.length; i++) {
                        var row = chooseOrders[i];
                        var order = {};
                        order.AgentName = row.SubOrders[0].AgentName;
                        order.LogicOrderId = row.LogicOrderId;
                        order.PlatformOrderId = row.PlatformOrderId;
                        order.Receiver = orderTableBuilder.rederField(row, "Reciever", i);
                        //order.Receiver = orderTableBuilder.rederField(row, "Reciever", i, "noencrypt");
                        order.PrintState = row.PrintState == 1 ? "已打印" : "未打印";

                        var remark = "";
                        if (row.BuyerRemark)
                            remark += "买家留言：" + row.BuyerRemark + ";<br/>";
                        if (row.SellerRemark)
                            remark += "卖家备注：" + row.SellerRemark + ";<br/>";
                        if (row.SystemRemark)
                            remark += "分发备注：" + row.SystemRemark + ";<br/>";
                        if (row.PrintRemark)
                            remark += "打单备注：" + row.PrintRemark + ";<br/>";
                        order.Remark = remark;
                        order.checkbox = '<input class="merger-order-chx" data-id="' + row.Id + '" data-oid="' + row.LogicOrderId + '" data-pid="' + row.PlatformOrderId + '" data-sid="' + row.ShopId + '" data-index="' + row.Index + '" type="checkbox" checked="true" />';

                        //收件地址信息
                        //dialogData.ToAddress = order.RecieverAddress;

                        //拼多多集运不参与合并
                        if (row.PddConsolidate == undefined || row.PddConsolidate == null || row.PddConsolidate == "")
                            dialogData.Orders.push(order);
                    }
                    var tmpl = $.templates("#hand-merger-order-dialog-tmpl");
                    var html = tmpl.render(dialogData);
                    layer.open({
                        type: 1,
                        title: "合并订单",
                        btn: ["确认合并", "关闭"],
                        shadeClose: true,
                        area: ['856px', 'auto'],
                        content: html,
                        skin: 'wu-dailog',
                        success: function () {
                            _bindCheckBoxEvent();
                        },
                        btn1: function () {
                            var chxs = $(".merger-order-chx:checked");
                            if (chxs.length <= 0) {
                                layer.msg("请勾选要合并的订单");
                                return;
                            } else if (chxs.length == 1) {
                                layer.msg("请至少勾选2个订单进行合并");
                                return;
                            }
                            var selectedOrders = [];
                            $(chxs).each(function (index, item) {
                                var $item = $(item);
                                var logicOrderId = $item.attr("data-oid");
                                selectedOrders.push(logicOrderId);
                            });

                            //地址信息
                            var orderRequest = { Receiver: {}, Sender: {} };

                            $(".layui-layer-btn0").hide();
                            common.Ajax({
                                url: "/NewOrder/MergerOrder",
                                loadingMessage: "合并中",
                                data: { orderRequest: null, selectedLogicOrderIds: selectedOrders },
                                success: function (rsp) {
                                    if (rsp.Success) {
                                        layer.closeAll();
                                        layer.msg("合并成功");
                                        $("#SeachConditions").trigger("click");
                                    } else {
                                        layer.alert("合并失败：" + rsp.Message, { skin: 'wu-dailog' });
                                    }
                                }
                            })

                            return false;
                        },
                        btn2: function () {
                            return true;
                        }
                    });
                },
                btn2: function () {
                    return true;
                }
            });
            return;
        }

        if (pddshiphold > 0) {
            var layerhtml = '<div style="padding:25px;display:flex;justify-content: center;align-items: center;flex-direction: column;font-size:16px;"><div style="line-height:30px">您勾选的订单中包含部分不支持合并订单<s style="color:#fe6f4f;margin-left:3px;">（暂停发货订单不支持合并）</s>，是否忽略暂停发货订单，正常合并其他订单</div></div>';
            layer.open({
                type: 1,
                title: "提示", //不显示标题
                content: layerhtml,
                area: ['540px'], //宽高
                btn: ['忽略，继续合并  ', '取消合并'],
                success: function () {
                    _bindCheckBoxEvent();
                },
                btn1: function () {
                    customerconfirm();
                },
                btn2: function () {
                    return true;
                }
            });
            return;
        }

        if (pddCrossBorderOrder > 0) {
            var layerhtml = '<div style="padding:25px;display:flex;justify-content: center;align-items: center;flex-direction: column;font-size:16px;"><div style="line-height:30px">您勾选的订单中包含官方不支持合并/拆分的跨境订单，已为您取消该部分订单勾选，是否继续操作剩下订单</div></div>';
            layer.open({
                type: 1,
                title: "提示", //不显示标题
                content: layerhtml,
                area: ['540px'], //宽高
                btn: ['继续操作  ', '取消'],
                success: function () {
                    _bindCheckBoxEvent();
                },
                btn1: function () {
                    layer.closeAll();
                    orderTableBuilder.FilterCrossBorderOrder();
                    module.MegeredOrders();
                },
                btn2: function () {
                    return true;
                }
            });
            return;
        }

        if (pddWaitAuthOrder > 0) {
            var layerhtml = '<div style="padding:25px;display:flex;justify-content: center;align-items: center;flex-direction: column;font-size:16px;"><div style="line-height:30px">您勾选的订单中包含官方不支持合并/拆分的待审核订单，已为您取消该部分订单勾选，是否继续操作剩下订单</div></div>';
            layer.open({
                type: 1,
                title: "提示", //不显示标题
                content: layerhtml,
                area: ['540px'], //宽高
                btn: ['继续操作  ', '取消'],
                success: function () {
                    _bindCheckBoxEvent();
                },
                btn1: function () {
                    layer.closeAll();
                    orderTableBuilder.FilterWaitAuthOrder();
                    module.MegeredOrders();
                },
                btn2: function () {
                    return true;
                }
            });
            return;
        }

        //#region 跨境合单校验
        if (commonModule.IsCrossBorderSite) {
            ///平台校验shop
            if (hasNotSameOrderShop) {
                layer.alert("勾选的订单店铺或平台不一致，无法通过平台进行校验");
                return;
            }
            if (hasPrintedOrderMsg != null && hasPrintedOrderMsg != "") {
                layer.alert(hasPrintedOrderMsg + "已生成快递单号，不支持合并订单");
                return;
            }
        }
        //#endregion

        //2.弹窗让用户确认
        var customerconfirm = function () {
            var dialogData = { ToAddress: '', Orders: [] };
            for (var i = 0; i < chooseOrders.length; i++) {
                var row = chooseOrders[i];
                var order = {};
                order.AgentName = row.SubOrders[0].AgentName;
                order.LogicOrderId = row.LogicOrderId;
                order.PlatformOrderId = row.PlatformOrderId;
                order.Receiver = orderTableBuilder.rederField(row, "Reciever", i);
                //order.RecieverAddress = orderTableBuilder.rederField(row, "RecieverAddress", i, "noencrypt");
                order.PrintState = row.PrintState == 1 ? "已打印" : "未打印";

                var remark = "";
                if (row.BuyerRemark)
                    remark += "买家留言：" + row.BuyerRemark + ";<br/>";
                if (row.SellerRemark)
                    remark += "卖家备注：" + row.SellerRemark + ";<br/>";
                if (row.SystemRemark)
                    remark += "分发备注：" + row.SystemRemark + ";<br/>";
                if (row.PrintRemark)
                    remark += "打单备注：" + row.PrintRemark + ";<br/>";
                order.Remark = remark;
                order.checkbox = '<input class="merger-order-chx" data-id="' + row.Id + '" data-oid="' + row.LogicOrderId + '" data-pid="' + row.PlatformOrderId + '" data-sid="' + row.ShopId + '" data-index="' + row.Index + '" type="checkbox" checked="true" />';

                //收件地址信息
                //dialogData.ToAddress = order.RecieverAddress;
                dialogData.Orders.push(order);
            }
            var tmpl = $.templates("#hand-merger-order-dialog-tmpl");
            var html = tmpl.render(dialogData);
            layer.open({
                type: 1,
                title: "合并订单",
                btn: ["确认合并", "关闭"],
                shadeClose: true,
                area: ['856px', 'auto'],
                content: html,
                skin: 'wu-dailog',
                success: function () {
                    _bindCheckBoxEvent();
                },
                btn1: function () {
                    var chxs = $(".merger-order-chx:checked");
                    if (chxs.length <= 0) {
                        layer.msg("请勾选要合并的订单");
                        return;
                    } else if (chxs.length == 1) {
                        layer.msg("请至少勾选2个订单进行合并");
                        return;
                    }
                    var selectedOrders = [];
                    $(chxs).each(function (index, item) {
                        var $item = $(item);
                        var logicOrderId = $item.attr("data-oid");
                        selectedOrders.push(logicOrderId);
                    });

                    //地址信息
                    var orderRequest = { Receiver: {}, Sender: {} };

                    $(".layui-layer-btn0").hide();
                    common.Ajax({
                        url: "/NewOrder/MergerOrder",
                        loadingMessage: "合并中",
                        data: { orderRequest: null, selectedLogicOrderIds: selectedOrders },
                        success: function (rsp) {
                            if (rsp.Success) {
                                layer.closeAll();
                                layer.msg("合并成功");
                                $("#SeachConditions").trigger("click");
                            } else {
                                layer.alert("合并失败：" + rsp.Message, { skin: 'wu-dailog' });
                            }
                        }
                    })

                    return false;
                },
                btn2: function () {
                    return true;
                }
            });
        }

        //BuyerHashCode校验 若hasNotSameBuyerHashCode为true
        if (hasNotSameBuyerHashCode) {
            ///平台校验shop
            if (hasNotSameOrderShop) {
                layer.alert("勾选的订单店铺或平台不一致，无法通过平台进行校验", { skin: 'wu-dailog' });
                return;
            }
            var selectedOrders = [];
            for (var i = 0; i < chooseOrders.length; i++) {
                var row = chooseOrders[i];
                selectedOrders.push(row.PlatformOrderId);
            }
            // 合并订单校验
            commonModule.ajax({
                url: '/NewOrder/OrderMergeQuery',
                data: { sid: firstOrderShopId, selectedOrders: selectedOrders },
                loading: true,
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        layer.alert(Message, { skin: 'wu-dailog' });
                        return;
                    }
                    customerconfirm();
                }
            });
            return;
        }
        else {
            customerconfirm();
        }

    }

    //#region 批量拆单
    module.BatchSplitOrder = function () {

        //1.勾选的订单，数据校验
        var chooseOrders = [];
        var notMergerOrderCount = 0; //非合并订单的数量
        for (var i = 0; i < orderTableBuilder.rows.length; i++) {
            var row = orderTableBuilder.rows[i];
            if (row.checked) {

                if (row.IsMainOrder == false) {
                    notMergerOrderCount++;
                    continue;
                }

                chooseOrders.push(row);
            }
        }

        if (notMergerOrderCount) {
            layer.alert("勾选的订单中有 " + notMergerOrderCount + "不是合并订单", { skin: 'wu-dailog' });
            return;
        }

        if (chooseOrders.length == 0) {
            layer.msg("请选择订单进行拆分");
            return;
        }

        //2.弹窗让用户确认
        var dialogData = { Orders: [] };
        for (var i = 0; i < chooseOrders.length; i++) {
            var row = chooseOrders[i];
            var order = {};
            order.AgentName = row.SubOrders[0].AgentName;
            order.LogicOrderId = row.LogicOrderId;
            order.PlatformOrderId = row.PlatformOrderId;
            order.Receiver = orderTableBuilder.rederField(row, "Reciever", i);
            //order.RecieverAddress = orderTableBuilder.rederField(row, "RecieverAddress", i, "noencrypt");
            order.ProductCount = row.ProductItemCount;
            order.TotalAmount = row.TotalAmount;
            order.TotalWeight = row.TotalWeight;
            order.Index = row.Index;
            order.Id = row.Id;
            order.ShopId = row.ShopId;
            order.CurrentStatus = row.ErpState;
            order.PlatformType = row.PlatformType;
            order.IsCold = row.IsCold;
            order.SubOrders = [];
            var subOrderObj = {};
            for (var j = 0; j < row.SubOrders.length; j++) {
                var subRow = row.SubOrders[j];
                var subOrderIndex = subOrderObj[subRow.LogicOrderId];
                var subOrder = {};
                if (subOrderIndex != undefined) {
                    subOrder = order.SubOrders[subOrderIndex];
                    subOrder.OrderItems.push({
                        ProductImgUrl: subRow.ProductImgUrl,
                        ProductSubject: subRow.ProductSubject,
                        ProductCargoNumber: subRow.ProductCargoNumber,
                        Color: subRow.Color,
                        Size: subRow.Size,
                        Price: subRow.Price,
                        BuyerRemark: subRow.BuyerRemark,
                        SellerRemark: subRow.SellerRemark,
                        RefunStatus: subRow.RefunStatus,
                        Status: subRow.Status
                    });
                }
                else {
                    subOrderObj[subRow.LogicOrderId] = order.SubOrders.length;
                    subOrder = {
                        MergerOrderId: row.LogicOrderId,
                        LogicOrderId: subRow.LogicOrderId,
                        PlatformOrderId: subRow.PlatformOrderId,
                        PayTime: subRow.PayTime,
                        OrderItems: [{
                            ProductImgUrl: subRow.ProductImgUrl,
                            ProductSubject: subRow.ProductSubject,
                            ProductCargoNumber: subRow.ProductCargoNumber,
                            Color: subRow.Color,
                            Size: subRow.Size,
                            Price: subRow.Price,
                            BuyerRemark: subRow.BuyerRemark,
                            SellerRemark: subRow.SellerRemark,
                            RefunStatus: subRow.RefunStatus,
                            Status: subRow.Status
                        }]
                    };
                    order.SubOrders.push(subOrder);
                }
            }
            dialogData.Orders.push(order);
        }
        var tmpl = $.templates("#batchSplitOrder");
        var html = tmpl.render(dialogData);
        var batchSplitAialogShow = layer.open({
            type: 1,
            title: '批量拆分订单',
            content: html,
            area: '856px',
            btn: ['拆出已勾选订单', '关闭'],
            skin: 'wu-dailog',
            success: function () {
                InitEvent();
                $("#splitOrderModuleAllCheck").click();
            },
            yes: function () {
                var requestModel = [];
                $(":checkbox:checked.batchSplitOrder-chx").each(function (i, chk) {
                    var mainModel = {
                        Key: $(chk).data("oid"),
                        Value: {IsCold:$(chk).data("iscold"),LogicOrderIds:[]}
                    };
                    $(":checkbox[data-poid='" + $(chk).data("oid") + "']:checked.batchSplitOrder-chx-item").each(function (j, child) {
                        var logicOrderId = $(child).data("oid");
                        mainModel.Value.LogicOrderIds.push(logicOrderId);
                });
                    if (mainModel.Value.LogicOrderIds.length > 0)
                    requestModel.push(mainModel);
            });

        if (requestModel.length == 0) {
            layer.msg("请选择订单进行拆分");
            return false;
        }

        //拆单
        module.SplitOrder(requestModel);
    },
        close: function () {
            //alert('取消');
        }
});
    }

function isAllChecked() {  //是否全选
    var isAllChecked = false;
    isAllChecked = $("#batchSplitOrder_tbody .batchSplitOrder-chx:checked").length == $("#batchSplitOrder_tbody .batchSplitOrder-chx").length ? true : false;
    if (isAllChecked) {
        $("#splitOrderModuleAllCheck").prop("checked", true);

    } else {
        $("#splitOrderModuleAllCheck").prop("checked", false);

    }
}

function getChooseOrderMun() {
    var num = 0;
    $("#batchSplitOrder_tbody .batchSplitOrder-chx-item").each(function (index, item) {
        if ($(item).is(":checked")) {
            num++;
        }
    })
    $("#batchSplitOrder_down_num").html(num)
}

function InitEvent() {
    $("#batchSplitOrder_tbody>.batchSplitOrder-row .batchSplitOrder-chx").each(function (index, item) {
        $(item).on("click", function (event) {
            event.stopPropagation();
            var $thisTr = $(item).closest("tr");
            if ($(item).is(":checked")) {
                $thisTr.next().find(".batchSplitOrder-chx-item").prop("checked", true);
            } else {
                $thisTr.next().find(".batchSplitOrder-chx-item").prop("checked", false);
            }

            isAllChecked();
            getChooseOrderMun();
        })
    })

    $("#batchSplitOrder_tbody .addMoreShowOrHide .batchSplitOrder-chx-item").each(function (index, item) {
        $(item).on("click", function () {
            var $thisTr = $(item).closest(".addMoreShowOrHide");
            var $preThisTr = $thisTr.prev();
            var preCheck = $preThisTr.find(".batchSplitOrder-chx")[0];
            var isPreCheck = false;
            $thisTr.find(".batchSplitOrder-chx-item").each(function (index, item) {
                if (item.checked) {
                    isPreCheck = true;
                }
            })
            preCheck.checked = isPreCheck;
                //if (isPreCheck) {
                //    $preThisTr.addClass("active02")
                //} else {
                //    $preThisTr.removeClass("active02")
                //}
            isAllChecked();
            getChooseOrderMun();
        })
    })
}

module.allCheck = function (isThis) {
    if ($(isThis).is(":checked")) {
        $("#batchSplitOrder_tbody input[type=checkbox]").prop("checked", true);
    } else {
        $("#batchSplitOrder_tbody input[type=checkbox]").prop("checked", false);
    }
    getChooseOrderMun();
}

module.rowClick = function (isThis) {

    if ($(isThis).find("td>input[type=checkbox]").is(":checked")) {
        $(isThis).find("td>input[type=checkbox]").prop("checked", false)
        $(isThis).next().find(".batchSplitOrder-chx-item").prop("checked", false)
    } else {
        $(isThis).find("td>input[type=checkbox]").prop("checked", true)
        $(isThis).next().find(".batchSplitOrder-chx-item").prop("checked", true)
    }
    isAllChecked()
    getChooseOrderMun();
}

module.showMoreContent = function (isThis, event) {
    event.stopPropagation();
    var thisTr = $(isThis).closest("tr");
    var isShow = thisTr.hasClass("active");
    if (isShow) {
        thisTr.removeClass("active").next().hide();
    } else {
        thisTr.addClass("active").next().show();
    }
    if ($(isThis).hasClass("active")) {
        $(isThis).removeClass("active")
    } else {
        $(isThis).addClass("active")
    }
}

module.showAllMoreContent = function (isThis) {

    if ($(isThis).hasClass("active")) {
        $(isThis).removeClass("active");
        $("#batchSplitOrder_tbody .batchSplitOrder-row").removeClass("active");
            $("#batchSplitOrder_tbody .addMoreShowOrHide").css({ display: "none" });
    } else {
        $(isThis).addClass("active");
        $("#batchSplitOrder_tbody .batchSplitOrder-row").addClass("active");
            $("#batchSplitOrder_tbody .addMoreShowOrHide").css({ display: "table-row" });
    }
}
//#endregion 

//单个拆单
module.SingleSplitOrder = function (rowIndex, childOrderId, isCold) {
    var row = orderTableBuilder.rows[rowIndex];
    if (isCold) {
        row.IsCold = isCold;
    }
    if (row.IsMainOrder == false) {
            layer.alert("订单不是合并订单", { skin: 'wu-dailog' });
        return;
    }
    var confirmMsg = "";
    var childOrderIds = [];
    if (childOrderId) {
        confirmMsg = "确定将订单【" + childOrderId + "】从合并订单中拆出吗？";
        childOrderIds.push(childOrderId);
    }
    else {
        confirmMsg = "确定拆开该合并订单吗？";
        var childOrderIdObj = {};
        for (var i = 0; i < row.SubOrders.length; i++) {
            var childOrder = row.SubOrders[i];
            if (childOrderIdObj[childOrder.LogicOrderId])
                continue;

            childOrderIds.push(childOrder.LogicOrderId);
            childOrderIdObj[childOrder.LogicOrderId] = 1;
        }
    }
    var isSplit = layer.confirm(confirmMsg, { icon: 3, title: '拆单确认', btn: ['确定', '取消'], skin: 'wu-dailog' },
        function () {
            layer.close(isSplit);
            module.SplitOrder([{ Key: row.LogicOrderId, Value: { IsCold: row.IsCold, LogicOrderIds: childOrderIds } }]);
        }, function () {
            layer.close(isSplit);
        }
    );
}

//拆单请求
module.SplitOrder = function (splitOrders) {
    common.Ajax({
        url: "/NewOrder/SplitOrderFromMergeredOrder",
        loadingMessage: "拆单中",
        data: { splitOrders: splitOrders },
        success: function (rsp) {
            if (rsp.Success) {
                layer.closeAll();
                layer.msg("拆单成功");
                $("#SeachConditions").trigger("click");
            } else {
                layer.alert("拆单失败：" + rsp.Message, { skin: 'wu-dailog' });
            }
        },
        error: function (rsp) {
            if (rsp.status == 401) {
                layer.msg("暂无权限，请联系管理员");
            } else {
                layer.msg(rsp.message);
            }
        }
    })
}

module.showMoreProduct = function (isThis, isPdd) {
    if ($(isThis).hasClass("active")) {
        $(isThis).removeClass("active").closest(".moreCode-item").removeClass("activeLi").find(".showMore-products").fadeOut(200);

        var _text = "展开";
        if (!isPdd) {
            _text += "详情";
        }
        $(isThis).children("span").text(_text).next();
    } else {
        $(isThis).addClass("active").closest(".moreCode-item").addClass("activeLi").find(".showMore-products").fadeIn(200);

        var _text = "收起";
        if (!isPdd) {
            _text += "详情";
        }
        $(isThis).children("span").text(_text).next();
    }
}

////已打印待发货快捷选择
//module.FastSet = function (that) {        
//    $("#SearchContainer .ErpOrderStatus").val("waitsellersend");
//    if ($(that).hasClass("active")) {
//        $("#SearchContainer .PrintStatus").val("");
//    }
//    else {
//        $("#SearchContainer .PrintStatus").val("1");
//    }
//    $("#SeachConditions").trigger("click");
//}

////快递不可达快捷选择
//module.SelectUnreachable = function (that) {
//    if ($(that).hasClass("active")) {
//        //取消选择
//        //orderTableBuilder.checkExpressReach(null, 0);

//        $("#SeachConditions").trigger("click");

//    }
//    else {
//        //选择不可达订单
//        //orderTableBuilder.checkExpressReach(null, 1);
//    }
//}


//二次发货选择发货类型的展示数据
//头条平台的，查询已发货包裹数据
module.getToResendOrders = function () {
    var html = "";
    var $checkedList = $(".layui-mytable .order-chx:checked");
    var selectedOrders = [];
    var touTiaoLogicOrderIds = [];
    var kusishouPlatformOrderIds = [];
    var kusishouQueryModels = [];

    $checkedList.each(function (i, item) {
        var rowindex = $(this).attr("data-index");
        var pid = $(this).attr("data-pid");
        var row = orderTableBuilder.rows[rowindex];
        var order = {};
        for (var y = 0; y < selectedOrders.length; y++) {
            if (selectedOrders[y].Index != undefined && selectedOrders[y].Index == rowindex) {
                order = selectedOrders[y];
                break;
            }
        }
        if (order.Index == undefined) {
            order.Id = row.Id;
            order.Index = rowindex;
            order.PlatformOrderId = row.PlatformOrderId;
            order.LogicOrderId = row.LogicOrderId;
            order.ShopName = row.ShopName;
            order.OrderTime = row.OrderTime;
            order.PlatformType = row.PlatformType;
            order.PlatformStatus = row.ErpState;
            order.RefunStatus = row.ErpRefundState;
            order.FxUserId = row.FxUserId;
            order.AgentName = row.AgentName;
            order.SubOrders = [];
            order.Packs = [];

            if (row.PlatformType == "TouTiao" || row.PlatformType == "Jingdong" || row.PlatformType == "XiaoHongShu") {
                touTiaoLogicOrderIds.push(row.LogicOrderId);
            }

            //选中的商品项
            var orderDetailIdName = "#order-detail-" + order.Index;
            var oichxs = $(orderDetailIdName + " .orderitem-chx:checked");
            if (oichxs != null && oichxs.length > 0) {
                $(oichxs).each(function (index2, ichx) {
                    var orderItemId = $(ichx).attr("data-id");
                    var item = {};
                    item.OrderItemId = orderItemId;
                    $(row.SubOrders).each(function (ii, subRow) {
                        if (subRow.OrderItemId == orderItemId) {
                            item.OrderItemCode = subRow.OrderItemCode;
                            item.Count = subRow.Count;
                            item.Price = subRow.Price;
                            item.ErpState = subRow.ErpState;
                            item.PlatformOrderId = subRow.PlatformOrderId;
                            item.LogicOrderId = subRow.LogicOrderId;
                            item.ProductImgUrl = subRow.ProductImgUrl;
                            item.ProductSubject = subRow.ProductSubject;
                            item.ProductCargoNumber = subRow.ProductCargoNumber;
                            item.CargoNumber = subRow.CargoNumber;
                            item.Color = subRow.Color;
                            item.Size = subRow.Size;
                            item.Weight = subRow.Weight;
                            item.SkuWeight = subRow.SkuWeight;
                            item.ShortTitle = subRow.ShortTitle;
                            item.SkuShortTitle = subRow.SkuShortTitle;
                            item.RefundStatus = subRow.RefundStatus;
                            item.Status = subRow.Status;
                            item.PrintState = subRow.PrintState;

                            if (row.PlatformType == "KuaiShou") {
                                if (kusishouPlatformOrderIds.indexOf(subRow.PlatformOrderId) == -1) {
                                    kusishouQueryModels.push({
                                        PlatformOrderId: subRow.PlatformOrderId,
                                        LogicOrderId: subRow.LogicOrderId,
                                        ShopId: subRow.ShopId,
                                        PlatformType: subRow.PlatformType
                                    });
                                }
                            }
                        }
                    });
                    order.SubOrders.push(item);
                });
            }

            selectedOrders.push(order);
        }
    });

    //查询已发货包裹数据
    if (touTiaoLogicOrderIds != null && touTiaoLogicOrderIds.length > 0) {

        var checkLoading = common.LoadingMsg("包裹数据检查中");

        common.Ajax({
            url: '/NewOrder/GetSendHistoryPack',
            data: {
                LogicOrderIds: touTiaoLogicOrderIds
            },

            type: 'POST',
            async: false,
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message);
                    return null;
                }
                else {
                    layer.close(checkLoading);
                    //waitOrderModule.SendHistoryPacks = rsp.Data.DateNow;
                    for (x = 0; x < selectedOrders.length; x++) {
                        var curLogicOrderId = selectedOrders[x].LogicOrderId;

                        if (selectedOrders[x].PlatformType == "Jingdong") {
                            if (rsp.Data.DateNow && rsp.Data.DateNow.length > 0) {
                                var sDateNow = rsp.Data.DateNow.filter(function (dItem) {
                                    return dItem.SendType == 0
                                });
                                if (sDateNow.length == 1) {
                                    break;
                                }
                            }
                        }

                        for (y = 0; y < rsp.Data.DateNow.length; y++) {
                            if (curLogicOrderId == rsp.Data.DateNow[y].LogicOrderId || curLogicOrderId == rsp.Data.DateNow[y].OrderId) {
                                var pack = {};
                                pack.ID = rsp.Data.DateNow[y].ID;
                                pack.OrderId = rsp.Data.DateNow[y].OrderId;
                                pack.OLogicOrderId = rsp.Data.DateNow[y].LogicOrderId;//原始逻辑单号
                                pack.LogicOrderId = curLogicOrderId;
                                pack.ExpressName = rsp.Data.DateNow[y].ExpressName;
                                pack.LogistiscBillNo = rsp.Data.DateNow[y].LogistiscBillNo;
                                pack.PackId = rsp.Data.DateNow[y].PackId;
                                selectedOrders[x].Packs.push(pack);
                            }
                        }
                    }
                }
            }
        });
    }

    //快手查询包裹信息
    if (kusishouQueryModels != null && kusishouQueryModels.length > 0) {
        var checkLoading = common.LoadingMsg("包裹数据检查中");

        common.Ajax({
            url: '/NewOrder/LoadOrderLogisticsInfosApi',
            data: { models: kusishouQueryModels },
            type: 'POST',
            async: false,
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message);
                    return null;
                }
                else {
                    layer.close(checkLoading);
                    var apiOrders = rsp.Data;
                    var apilogisticsInfos = [];
                    //赋值包裹信息
                    for (var apio = 0; apio < apiOrders.length; apio++) {
                        var logisticsInfos = apiOrders[apio].LogisticsInfos;
                        //可能有多个包裹
                        var apilogisticsInfo = [];
                        for (var lis = 0; lis < logisticsInfos.length; lis++) {
                            //匹配包裹中的商品
                            var product_infos = logisticsInfos[lis].product_info;
                            for (var pi = 0; pi < product_infos.length; pi++) {
                                apilogisticsInfo[product_infos[pi].OrderItemCode] = logisticsInfos[lis];
                            }
                        }
                        apilogisticsInfos[apiOrders[apio].PlatformOrderId] = apilogisticsInfo;
                    }

                    for (var s = 0; s < selectedOrders.length; s++) {
                        for (var so = 0; so < selectedOrders[s].SubOrders.length; so++) {
                            var subOrder = selectedOrders[s].SubOrders[so];

                            var apiOrder = apilogisticsInfos[subOrder.PlatformOrderId];
                            if (apiOrder) {
                                var apilogisticsInfo = apiOrder[subOrder.OrderItemCode];
                                if (apilogisticsInfo) {
                                    selectedOrders[s].SubOrders[so].DeliveryId = apilogisticsInfo.delivery_id;
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    return selectedOrders;
}

//绑定包裹多选框
var initPackSelectBox = function (logicOrderId, packs) {
    packs = packs || [];
    var selectboxArr = [];
    for (var i = 0; i < packs.length; i++) {
        var obj = {};
        var packId = (packs[i].PackId == null || packs[i].PackId == undefined) ? "" : packs[i].PackId;
        obj.Value = packId + "|" + packs[i].LogistiscBillNo + "|" + packs[i].OLogicOrderId;
        obj.Text = packs[i].LogistiscBillNo + "(" + packs[i].ExpressName + ")";
        selectboxArr.push(obj);
    }

    var selectInit = {
        eles: '#selectPacks-' + logicOrderId,
        emptyTitle: '选择包裹', //设置没有选择属性时，出现的标题
        data: selectboxArr,
        searchType: 0, //1出现搜索框，不设置不出现搜索框
        showWidth: '200px', //显示下拉的宽
        isRadio: false, //有设置，下拉框改为单选
        allSelect: true,
        selectData: [selectboxArr[0]]  //初始化数据
    };

    var selectBox = new selectBoxModule2();
    selectBox.initData(selectInit);
}

module.moreCodeSelect = function () {
    event.stopPropagation();
    var $offsetTop = this.offsetTop - 0;
    var $offsetLeft = this.offsetLeft - 0;
    var $pTop = $(this).closest(".moreCodeDailog-target")[0].offsetTop - 0;
    var $pLeft = $(this).closest(".moreCodeDailog-target")[0].offsetLeft - 0;
    var $pscrollTop = $(this).closest(".layui-layer-content")[0].scrollTop;
    //$(isThis).find(".moreCode-select").css({ top: $pTop + $offsetTop - $pscrollTop + 43 + "px", left: $pLeft + $offsetLeft + "px" });
    $(this).closest(".moreCode-item-main").find(".moreCode-select").css({ top: $pTop + $offsetTop - $pscrollTop + 43 + "px", display: "block" });
}

$(document).click('click', function () {
    $(".moreCode-item-main .moreCode-select").css({ display: "none" })
});

var selectScrollTimeout;
$(window).scroll(function () {
    clearTimeout(selectScrollTimeout);

    if ($(window).scrollTop() >= 40) {
        selectScrollTimeout = setTimeout(function () {
            $('#Supplier .selectWrap-box').fadeOut();
            $('#Agent .selectWrap-box').fadeOut();
            $('#ToCountry .selectWrap-box').fadeOut();
            $('#selectShops .selectWrap-box').fadeOut();
            $('#ProductAttrCheck .selectWrap-box').fadeOut();
            $('#inputSelectTime .newwrapperTime').fadeOut();
            $('#newProductSubjectCheck .moreSelectWrap-main').fadeOut();
        })
    }
});

module.scrollSelect = function () {
    var srollTop = this.scrollTop;
    $(".moreCode-item-main .moreCode-select").scrollTop(srollTop);
}
module.inputMoreCode = function () {
    var $val = $(this).val().trim();
    if ($val != "") {
        var re = /^[0-9]+$/;
        if (re.test($val) == false) {
            layer.msg('请输入整数');
            $(this).val(0);
            return;
        }
    }
    var totalNum = $(this).attr("data-totalNum");
    var indexNum = $(this).attr("data-index");
    var codeInputs = $(this).closest(".moreCode-item-main").find(".codeInput" + indexNum);
    var totalInput = 0;
    codeInputs.each(function (index, item) {
        if ($(item).closest(".moreCode-item-main-li").find(".chooseExpress")[0].checked) {  //快递是否勾选
            totalInput = totalInput + ($(item).val() - 0);
        }
    })
    if (totalNum < totalInput) {
        layer.msg('输入数已大于规格数量');
        $(this).val("");
        return;
    }
}
module.onblurMoreCode = function () {
    var $val = $(this).val();
    if ($val.trim() == "") {
        $(this).val(0);
    }

}

module.changeCodeCheck = function (index) {
    if (this.checked) {
        $(this).closest(".moreCode-select").find(".codeInput" + index).removeAttr("disabled");
    } else {
        $(this).closest(".moreCode-select").find(".codeInput" + index).attr("disabled", "disabled").val(0);
    }
}

module.checkExpress = function () {
    $(this).closest(".moreCode-item-main-li").find(".codeCheckbox").prop("checked", false);
    $(this).closest(".moreCode-item-main-li").find(".codeInput").attr("disabled", "disabled").val(0);
}
function showDkPrintWarn(parplatformName, helpUrl, parplatformTemplateName) {
    var showDkPrintWarnTem = $.templates("#dk_printWarn_tmpl");
    var html = showDkPrintWarnTem.render({ parplatformName: parplatformName, helpUrl: helpUrl, parplatformTemplateName: parplatformTemplateName });
    layer.open({
        type: 1,
        title: '打印提示',
        content: html,
        area: '560px', //宽高
        skin: 'showDkPrintWarnDailog wu-dailog',
        btn: ['查看开通教程', '我已了解'],
        btn1: function (index) {
            window.open(helpUrl, '_blank');
            layer.close(index);
        },
        btn2: function (index) {
            layer.close(index);
        },
    });
}

//#region 1688密文打印

function showCommonPrintWarn(parplatformName, platformArray, helpUrl) {
    showCommonPrintWarn_parplatformArray = platformArray;
    var showDkPrintWarnTem = $.templates("#common_printWarn_tmpl");
    var html = showDkPrintWarnTem.render({ parplatformName: parplatformName, helpUrl: helpUrl });
    layer.open({
        type: 1,
            title: '打印提示',
            content: html,
            area: '560px', //宽高
            skin: 'showDkPrintWarnDailog wu-dailog',
            btn: ['分销订单打印规则', '取消打印'],
            btn1: function (index) {
                waitOrderModule.encryptionPrintWarn();
                layer.close(index);
            },
            btn2: function (index) {
                layer.close(index);
            },
    });
}

//密文订单打印规则
module.encryptionPrintWarn = function () {
    layer.closeAll();
    var encryptionPrintWarnTem = $.templates("#common_encryptionPrintWarn_tmpl");
    console.log("showCommonPrintWarn_parplatformArray", showCommonPrintWarn_parplatformArray)
    var html = encryptionPrintWarnTem.render({ platformArray: showCommonPrintWarn_parplatformArray });
    layer.open({
        type: 1,
            title: '按各大平台加密订单打印要求如下',
            content: html,
            area: '560px', //宽高
            skin: 'showDkPrintWarnDailog wu-dailog',
            btn: false
        });
    }

/*
 * 检查1688密文下单和选择的模板关系是否对称
 */
var check1688OutChannelTemp = function (order, templateType) {
    var outChannelType = "other";
    var orderTags = order.OrderTags || [];
    for (var i = 0; i < orderTags.length; i++) {
        var _tag = orderTags[i];
        if (_tag.Tag == "OutChannel") {
            var tagValue = _tag.TagValue;
            //明文分销订单也需要用对应电子面单打印，明文分销单添加后缀_plaintext
            var taoxi = ["thyny", "tm", "taote", "c2m"];
            if (!common.IsCainiaoTemplate(templateType) && taoxi.indexOf(tagValue) != -1) {
                outChannelType = "淘系";
                break;
            }
            if ((!common.IsTouTiaoTemplate(templateType) && !common.IsTouTiaozjTemplate(templateType) && !commonModule.IsTouTiaoKuaiYunTemplate(templateType)) && (tagValue == "douyin")) {
                outChannelType = "抖音";
                break;
            }
            if (!common.IsKuaiShouTemplate(templateType) && (tagValue == "kuaishou")) {
                outChannelType = "快手";
                break;
            }
            if (!common.IsPddTemplate(templateType) && !common.IsPddKuaiYunTemplate(templateType) && (tagValue == "pinduoduo")) {
                outChannelType = "拼多多";
                break;
            }
            if (!common.IsXiaoHongShuTemplate(templateType) && !common.IsNewXiaoHongShuTemplate(templateType) && tagValue == "xiaohongshu") {
                outChannelType = "小红书";
                break;
            }
            if (!common.IsWxVideoTemplate(templateType) && (tagValue == "weixin")) {
                outChannelType = "视频号";
                break;
            }
            if (!common.IsJdWjzjTemplate(templateType) && !common.IsJdzjTemplate(templateType) && !common.IsJdKdTemplate(templateType) && tagValue == "jingdong") {
                outChannelType = "京东";
                break;
            }
        }
    }
    return outChannelType;
}
//#endregion

/*
 * 出现地址拦截的订单，提示用户去后台修改
 */
var addressStopOrderTips = function (stopOrders, callBack) {

    var behalfpids = []; //代发
    var selfpids = [];  //自营
    var allpids = [];  //所有
    var logicOrderIds = []; //跳过订单
    var pidHtml = '';
    for (var i = 0; i < stopOrders.length; i++) {
        var stopOrder = stopOrders[i];
        if (stopOrder.UpFxUserId == 0) {
            selfpids.push(stopOrder.CustomerOrderId);
        } else {
            behalfpids.push(stopOrder.CustomerOrderId);
        }
        allpids.push(stopOrder.CustomerOrderId);
        logicOrderIds.push(stopOrder.LogicOrderId);
        pidHtml += '<li>' + (i + 1) + '、' + stopOrder.CustomerOrderId + '</li>';
    }

    var btns = ['批量复制订单编号前往后台修改', '忽略报错订单，继续打印其他订单'];
    if (selfpids.length != 0 && behalfpids.length != 0) {
        var html = '<div class="newCommonDailog" style="width:670px;padding:30px 20px; padding-bottom:0;">';
        html += '<div class="newCommonDailog-title" style="font-size: 16px;color: #000;margin-bottom: 10px;">打印报错提醒</div>';
        html += '<div class="newCommonDailog-main" style="padding-left:15px;">';
        html += '<div class="newCommonDailog-main-text"  style="padding-top:5px;max-height:unset;line-height:30px; display: flex;flex-direction: column;font-size:14px">';
        html += '<span style="font-size:16px;color:#666">系统检测到你勾选的下列订单地址，存在不符合平台要求的地址格式/非法字符</span>';
        html += '<span style="font-size:16px;color:#666">请您复制订单编号，选择下列对应按钮修改正常收件地址</span>';
        html += '<div style="max-height:200px;overflow-y: auto;">';
        html += '<table class="stockup_table_content">';
        html += '<thead>';
        html += '<tr>';
        html += '<th style="width:50%;">需商家修改</th>';
        html += '<th style="width:50%;">需自己修改</th>';
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';
        var maxLength = behalfpids.length > selfpids.length ? behalfpids.length : selfpids.length;

        for (var i = 0; i < maxLength; i++) {
            var behalfpid = behalfpids[i] ? (i + 1) + "、" + behalfpids[i] : "";
            var selfpid = selfpids[i] ? (i + 1) + "、" + selfpids[i] : "";
            html += '<tr>';
            html += '<td>' + behalfpid + '</td>';
            html += '<td>' + selfpid + '</td>';
            html += '</tr>';
        }
        html += '</tbody>';
        html += '</table>';
        html += '<input id="copyAddressStopLogicBehalfpids" type="hidden" name="name" value="' + behalfpids.join(',') + '">';
        html += '<input id="copyAddressStopLogicSelfpids" type="hidden" name="name" value="' + selfpids.join(',') + '">';

        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        btns = ['批量复制订单编号发送给商家', '批量复制订单编号前往后台修改', '忽略报错订单，继续打印其他订单'];
        var checkDyYunWarnDailg03 = layer.open({
            type: 1,
            title: false, //不显示标题
            content: html,
            area: 670, //宽高
            skin: 'adialog-Shops-skin checkDyYunWarnDailgAll',
            btn: btns,
            btn1: function () {
                commonModule.CopyText('#copyAddressStopLogicBehalfpids');
            },
            btn2: function () {
                commonModule.CopyText('#copyAddressStopLogicSelfpids');
                return false;
            },
            btn3: function () {
                if (typeof (callBack) == "function")
                    callBack(logicOrderIds);
            }
        });
    }
    else {
        var html = '<div class="newCommonDailog" style="width:650px;padding:30px 20px; padding-bottom:0;">';
        html += '<div class="newCommonDailog-title" style="font-size: 16px;color: #000;margin-bottom: 10px;">打印报错提醒</div>';
        html += '<div class="newCommonDailog-main" style="padding-left:15px;">';
        html += '<div class="newCommonDailog-main-text"  style="padding-top:5px;max-height:unset;line-height:30px; display: flex;flex-direction: column;font-size:14px">';
        html += '<span style="font-size:16px;color:#666">系统检测到你勾选的下列订单地址，存在不符合平台要求的地址格式/非法字符</span>';
        if (selfpids.length != 0 && behalfpids.length == 0) {
            html += '<span style="font-size:16px;color:#666">请您复制订单编号，前往店铺后台修改正常收件地址</span>';
            btns = ['批量复制订单编号前往后台修改', '忽略报错订单，继续打印其他订单'];
            html += '<input id="copyAddressStopLogicOrderId" type="hidden" name="name" value="' + selfpids.join(',') + '">';
        }
        else if (selfpids.length == 0 && behalfpids.length != 0) {
            html += '<span style="font-size:16px;color:#666">请您复制订单编号，发送给下游商家前往店铺后台修改正常收件地址</span>';
            btns = ['批量复制订单编号发送给商家', '忽略报错订单，继续打印其他订单'];
            html += '<input id="copyAddressStopLogicOrderId" type="hidden" name="name" value="' + behalfpids.join(',') + '">';
        }
        html += '<span style="font-size:16px;color:#666">报错订单编号:</span>';
        html += '<ul style="display:flex;flex-direction: column;font-size:14px;max-height:200px;overflow-y: auto; line-height: 25px; color: #333">';
        html += pidHtml;
        html += '</ul>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        var checkDyYunWarnDailg03 = layer.open({
            type: 1,
            title: false, //不显示标题
            content: html,
            area: 650, //宽高
            skin: 'adialog-Shops-skin',
            btn: btns,
            btn1: function () {
                commonModule.CopyText('#copyAddressStopLogicOrderId');
            },
            btn2: function () {
                if (typeof (callBack) == "function")
                    callBack(logicOrderIds);
            }
        });
    }
}

//#region 抖店顺丰包邮
/*
 * 检查是否包含顺丰包邮商品的订单
 */
module.IsExistSfFreeShippingOrder = function (order) {
    var isExit = false;
    var allTags = order.OrderTags;
    if (allTags.length != 0) {
        for (var i = 0; i < order.Items.length; i++) {
            var item = order.Items[i];
            isExit = commonModule.HasTagOiCode(allTags, item.OrderItemCode, 'sf_free_shipping', 'OrderItem');
            if (isExit) break;
        }
    }
    return isExit;
}

/*
 * 检查多单号发货里是否包含“顺丰包邮”商品且“未使用顺丰”快递的订单
 */
module.CheckSfFreeShippingOrderInMultiPack = function (order) {
    var checkResult = true;
    var allTags = order.OrderTags;
    if (allTags.length != 0) {
        for (var i = 0; i < order.MultiPackSendModels.length; i++) {
            var item = order.MultiPackSendModels[i];
            var isExit = commonModule.HasTagOiCode(allTags, item.OrderItemCode, 'sf_free_shipping', 'OrderItem');
            if (isExit && item.ExpressCompanyCode != "SF") {
                checkResult = false
                break;
            }
        }
    }
    return checkResult;
}
//#endregion

/**
 * 预检查处理，打印按钮事件前置检查，过滤
 * @method PrintPreCheckHandle
 * @param orders {object} 选择的订单
 * @param template {object} 选择的模板
 * @param callBack {function} 检查完之后回调方法
 * @param skipLogic {string} 命中某一个逻辑后，再次执行需跳过的条件
 * @return void
 */
module.PrintPreCheckHandle = function (orders, template, callBack, skipLogic) {

    //新逻辑
    return module.PrintPreCheckHandleNew(orders, template, callBack);

    if (orders == null || orders.length == 0) {
        layer.msg("请选择订单！");
        return;
    }
    if (template == null) {
        layer.msg("请选择快递模板！");
        return;
    }

    var isNotTouTiaTmp = true;  //是否未使用头条模板
    var isNotKuaishouTmp = true; //是否未使用快手模板
    var isNotXhsTmp = true;  //是否未使用小红书模板
    var isWxVideoTmp = false;  //是否使用视频号模板
    var isNotSiteTmp = true;
    if (template != null) {
        var templateType = template.TemplateType;
        if (common.IsTouTiaoTemplate(templateType) || common.IsTouTiaozjTemplate(templateType) || commonModule.IsTouTiaoKuaiYunTemplate(templateType)) {
            isNotTouTiaTmp = false;
        }
        if (common.IsKuaiShouTemplate(templateType)) {
            isNotKuaishouTmp = false;
        }
        if (common.IsSiteTemplate(templateType)) {
            isNotSiteTmp = false;
        }
        if (common.IsXiaoHongShuTemplate(templateType) || common.IsNewXiaoHongShuTemplate(templateType)) {
            isNotXhsTmp = false;
        }
        if (common.IsWxVideoTemplate(templateType)) {
            isWxVideoTmp = true;
        }
    }

    var toutiaoOrderArray = [];
    var kuaishouOrderArray = [];
    var alibabaOrderArray = [];
    var xiaoHongShuArray = [];
    var wxVideoArray = [];
    var notWxvideoOwnOrderArray = [];  //不是自己的订单，视频号面单只能打自己店铺的订单
    var isExist1688OutChannelArray = [];
    var kuaishou_pintuan_logicOrderIds = [];  //拼团订单
    var sf_free_shipping_logicOrderIds = [];  //顺丰包邮订单 
    var addressStopOrders = []; //是否省市区特殊字符订单
    var exception_logicOrderIds = [];  //异常的订单
    for (var i = 0; i < orders.length; i++) {
        var _order = orders[i];
        var rowOrder = orderTableBuilder.rows[_order.Index];

        if (_order.PlatformType == "KuaiShou") {
            kuaishouOrderArray.push(_order);
            //快手订单判断是否存在拼团单，存在需要提醒过滤
            if (commonModule.HasTag(_order.OrderTags, 'Kuaishou_orderPiecingGroup', 'Order') && (!_order.Receiver || !_order.Receiver.ToMaskAddress))
                kuaishou_pintuan_logicOrderIds.push(_order.LogicOrderId);
        }
        if (_order.PlatformType == "Alibaba") {
            alibabaOrderArray.push(_order);
            var outChannelType = check1688OutChannelTemp(_order, template.TemplateType);
            if (outChannelType != "other" && isExist1688OutChannelArray.indexOf(outChannelType) == -1)
                isExist1688OutChannelArray.push(outChannelType);
        }
        if (_order.PlatformType == "TouTiao") {
            toutiaoOrderArray.push(_order);
            //顺丰包邮
            if (module.IsExistSfFreeShippingOrder(_order))
                sf_free_shipping_logicOrderIds.push(_order.LogicOrderId);
        }

        if (_order.PlatformType == "XiaoHongShu") {
            xiaoHongShuArray.push(_order);
        }

        //if ((isWxVideoTmp && _order.PlatformType != "WxVideo") || (isWxVideoTmp && template.CaiNiaoAuthInfoId != _order.ShopId)) {
        //    notWxvideoOwnOrderArray.push(_order);
        //}
        if (_order.PlatformType == "WxVideo") {
            wxVideoArray.push(_order);
        }

        //已申请退款异常
        if (commonModule.HasTag(_order.OrderTags, 'AliFxPurchaseException', 'LogicOrder')) {
            exception_logicOrderIds.push(_order.LogicOrderId);
        }

        //检查发件人省市区是否合法
        if (rowOrder) {
            //// 判断字符串是否全是中文
            //function isAllChinese(str) {
            //    //中文 {} () （）可通过
            //    var ignoreChars = [''];
            //    str = str || "";
            //    str = str.replaceAll('{', '').replaceAll('}', '');
            //    str = str.replaceAll('(', '').replaceAll(')', '');
            //    str = str.replaceAll('（', '').replaceAll('）', '');
            //    return /^[\u4E00-\u9FA5]+$/.test(str)
            //}
            //检查省市区是否为空
            var dijishi = ["东莞市", "中山市", "潜江市", "神农架林区", "天门市", "仙桃市", "济源市", "白沙黎族自治县", "保亭黎族苗族自治县", "昌江黎族自治县", "澄迈县", "定安县", "东方市", "儋州市", "乐东黎族自治县", "临高县", "陵水黎族自治县", "琼海市", "琼中黎族苗族自治县", "文昌市", "屯昌县", "万宁市", "五指山市", "阿拉尔市", "北屯市", "胡杨河市", "可克达拉市", "昆玉市", "双河市", "石河子市", "铁门关市", "图木舒克市", "五家渠市", "嘉峪关市"];
            if (!rowOrder.ToProvince || !rowOrder.ToCity) {
                addressStopOrders.push(_order);
            }
            else if (!rowOrder.ToCounty && dijishi.indexOf(rowOrder.ToCity) == -1) {
                addressStopOrders.push(_order);
            }
            //省市区不能包含特殊字符
            else if (!commonModule.isAllChinese(rowOrder.ToProvince) || !commonModule.isAllChinese(rowOrder.ToCity) || !commonModule.isAllChinese(rowOrder.ToCounty) || (rowOrder.ToTown && !commonModule.isAllChinese(rowOrder.ToTown))) {
                addressStopOrders.push(_order);
            }
        }
    }

    //过滤订单继续打单
    var continueFunc = function (logicOrderIds, _skipLogic) {
        //过滤订单继续打印
        var _newOrders = [];
        for (var i = 0; i < orders.length; i++) {
            if (logicOrderIds.indexOf(orders[i].LogicOrderId) == -1) {
                _newOrders.push(orders[i]);
            }
            else {
                $('#order-' + orders[i].Index).click();
            }
        }
        if (_newOrders.length > 0) {
            //callBack(_newOrders);
            module.PrintPreCheckHandle(_newOrders, template, callBack, _skipLogic);
            return;
        }
        else {
            layer.msg("当前您所选的订单已被全部过滤");
            return;
        }
    }

    //公共逻辑
    var commonFunc = function (os) {
        if (template.TemplateType == 10) {
            //丰桥模板，提示升级云模板
            module.FqTempUprate(function () {
                callBack(os);
            });
        }
        else if (!isNotKuaishouTmp && addressStopOrders.length > 0) {
            //地址有特殊字符使用快手模板
            addressStopOrderTips(addressStopOrders, function (logicIds) {
                //继续打印
                continueFunc(logicIds);
            });
        }
        else {
            callBack(os);
        }
    }

    //存在已申请退款的异常单
    if (exception_logicOrderIds.length > 0) {
        module.ShowOrderUprate(function () {
            //过滤异常单继续打印
            continueFunc(exception_logicOrderIds);
        }, "exception_order", {
            selectNum: orders.length,
            logicOrderIds: exception_logicOrderIds
        });
        return;
    }

    //存在头条订单
    if (toutiaoOrderArray.length > 0) {
        if (isNotTouTiaTmp) {
            showDkPrintWarn('抖音', 'https://docs.qq.com/doc/DQkhPWnlYWk1ZeUZT');
            return;
        }

        if (sf_free_shipping_logicOrderIds.length > 0 && template.ExpressCompanyCode != "SF") {
            //包含顺丰包邮的订单没有用顺丰模板打印
            module.ShowOrderUprate(function () {
                //过滤异常单继续打印
                continueFunc(sf_free_shipping_logicOrderIds);
            }, "toutiao_sf_free_shipping", {
                selectNum: orders.length,
                logicOrderIds: sf_free_shipping_logicOrderIds
            });
        }
        else {
            touTiaoRecommendedExpress(orders, template, continueFunc, commonFunc);
        }
    }
    //存在快手订单
    else if (kuaishouOrderArray.length > 0) {
        if (isNotKuaishouTmp) {
            //快手模板打印公告
            showDkPrintWarn('快手', 'https://m0ij9216j9.feishu.cn/docx/KQ72d76rvoKU5OxKwnycvA4Ln9d');
            return;
        }

        if (kuaishou_pintuan_logicOrderIds.length > 0) {
            module.ShowOrderUprate(function () {
                //过滤拼团单继续打印
                continueFunc(kuaishou_pintuan_logicOrderIds);
            }, "kspintuan_uprate");
        }
        else {
            commonFunc(orders);
        }
    }
    //存在阿里巴巴订单
    else if (alibabaOrderArray.length > 0) {
        if (isExist1688OutChannelArray.length > 0) {
            showCommonPrintWarn('1688分销订单下单', isExist1688OutChannelArray, 'https://docs.qq.com/doc/DQkhPWnlYWk1ZeUZT');
            return;
        }
        else {
            commonFunc(orders);
        }
    }
    //存在小红书订单
    else if (xiaoHongShuArray.length > 0) {
        //校验使用小红书面单，继续打印会跳过这个逻辑
        if (isNotXhsTmp && skipLogic != "xhstemp_uprate") {
            module.ShowOrderUprate(function (needignorelogicOrderIds) {
                //过滤订单继续打印
                continueFunc(needignorelogicOrderIds, "xhstemp_uprate");
            }, "xhstemp_uprate", {
                selectAllOrder: orders,
                selectPtOrder: xiaoHongShuArray
            });
        } else {
            commonFunc(orders);
        }
    }
    //else if (notWxvideoOwnOrderArray.length > 0) {
    //    layer.msg("视频号电子面单暂不支持跨店铺、跨平台使用（含线下单）")
    //    return;
    //}
    else if (wxVideoArray.length > 0) {
        //校验使用视频号面单，继续打印会跳过这个逻辑
        if (!isWxVideoTmp && skipLogic != "wxvideotemp_uprate") {
            module.ShowOrderUprate(function (needignorelogicOrderIds) {
                //过滤订单继续打印
                continueFunc(needignorelogicOrderIds, "wxvideotemp_uprate");
            }, "wxvideotemp_uprate", {
                selectAllOrder: orders,
                selectPtOrder: wxVideoArray
            });
        } else {
            commonFunc(orders);
        }
    }
    else {
        commonFunc(orders);
    }
}

//#region 抖店推荐快递
/*
 * [抖店] 发货探查逻辑
 */
var touTiaoRecommendedExpress = function (orders, template, continueFunc, callBack) {
    var loding = null;
    var doPrint = function () {
        layer.close(loding);
        callBack(orders);
    }

    var urderIds = [];
    var shopIds = [];
    var toutiaoOrder = [];
    var notToutiaoOrder = [];
    for (var i = 0; i < orders.length; i++) {
        var _order = orders[i];
        if (_order.PlatformType == "TouTiao") {
            toutiaoOrder.push(_order);
            if (urderIds.indexOf(_order.FxUserId) == -1)
                urderIds.push(_order.FxUserId);

            if (shopIds.indexOf(_order.ShopId) == -1)
                shopIds.push(_order.ShopId);
        }
        else {
            notToutiaoOrder.push(_order);
        }
    }

    //1.获取开去白名单店铺，开关配置查询店铺归属人
    //2.过滤不可达的订单
    //3.将推荐快递写入到打单备注上
    //4.继续打印
    var openShopIds = loadOpenRecommendedExpress(urderIds, shopIds);
    if (openShopIds && openShopIds.length != 0) {

        loding = common.LoadingMsg("校验中");
        var templateAddress = '';
        if (template.BranchAddress != null && template.BranchAddress != undefined && template.BranchAddress != "")
            templateAddress = template.BranchAddress;
        else {
            templateAddress = template.SendSite;
        }
        var address = addressSpliter.parse(templateAddress, false);
        if (address.isSuccess == false) {
            console.log("地址识别失败，不走探查逻辑");
            doPrint();
            return;
        }

        var queryModel = {
            Province: address.province,
            City: address.city,
            District: address.area,
            Detail: address.addr,
            ExpressCompanyCode: template.ExpressCompanyCode,
            OrderInfos: []
        };
        var oldOrderPrintRemarks = [];  //原订单打单备注
        for (var i = 0; i < toutiaoOrder.length; i++) {
            if (openShopIds.indexOf(toutiaoOrder[i].ShopId) != -1) {
                var pids = [];
                var rowOrder = orderTableBuilder.rows[toutiaoOrder[i].Index];
                for (var s = 0; s < rowOrder.SubOrders.length; s++) {
                    if (pids.indexOf(rowOrder.SubOrders[s].PlatformOrderId) == -1)
                        pids.push(rowOrder.SubOrders[s].PlatformOrderId);
                }
                queryModel.OrderInfos.push({
                    ShopId: toutiaoOrder[i].ShopId,
                    LogicOrderId: toutiaoOrder[i].LogicOrderId,
                    PlatformOrderIds: pids
                });
                oldOrderPrintRemarks.push({
                    Index: toutiaoOrder[i].Index,
                    LogicOrderId: toutiaoOrder[i].LogicOrderId,
                    PrintRemark: rowOrder.PrintRemark
                });
            }
        }

        //请求发货探查接口
        var recommendedExpressOrder = loadRecommendedExpressOrder(queryModel);
        if (recommendedExpressOrder == null || recommendedExpressOrder.length == 0) {
            doPrint();
            return;
        }

        var hitNotRecommendOrders = [];
        var skipLogicOrderIds = [];
        //过滤出不在推荐快递的订单，并且讲原订单打单备注补充上
        for (var r = 0; r < recommendedExpressOrder.length; r++) {
            if (recommendedExpressOrder[r].IsHitNotRecommend) {
                //补充原订单打单备注
                recommendedExpressOrder[r].PrintRemark = null;
                for (var o = 0; o < oldOrderPrintRemarks.length; o++) {
                    if (recommendedExpressOrder[r].LogicOrderId == oldOrderPrintRemarks[o].LogicOrderId) {
                        recommendedExpressOrder[r].PrintRemark = oldOrderPrintRemarks[o].PrintRemark;
                        recommendedExpressOrder[r].Index = oldOrderPrintRemarks[o].Index;
                    }
                }
                hitNotRecommendOrders.push(recommendedExpressOrder[r]);
                skipLogicOrderIds.push(recommendedExpressOrder[r].LogicOrderId);
            }
        }

        //订单正常，选择的快递也在推荐列表中
        if (hitNotRecommendOrders.length == 0) {
            doPrint();
            return;
        }

        layer.close(loding);
        commonModule.setStorage("toutiao_recommended_express_order", hitNotRecommendOrders);
        //参数1=继续打印全部订单，参数2=弹框类型，参数3=跳过订单打印其他订单
        module.ShowOrderUprate(doPrint, "toutiao_recommendedexpress", {
            selectNum: hitNotRecommendOrders.length,
            continueFunc: function () {
                module.BatchUpdatePrintRemark(hitNotRecommendOrders);
                if (callBack.name == "commonFunc") {
                    continueFunc(skipLogicOrderIds);
                } else {
                    var finalOrder = continueFunc(orders, skipLogicOrderIds);
                    callBack(finalOrder);
                }
            }
        });
    }
    else {
        doPrint();
        return;
    }
}

/*
 * 获取已开启探查的白名单店铺
 */
var loadOpenRecommendedExpress = function (userIds, shopIds) {
    var openShopIds = [];
    common.Ajax({
        url: '/NewOrder/IsOpenRecommendedExpress',
        data: { userIds: userIds, shopIds: shopIds },
        async: false,
        success: function (rsp) {
            if (rsp.Success)
                openShopIds = rsp.Data;
        }
    })
    return openShopIds;
}

/*
 * 加载抖店推荐快递
 */
var loadRecommendedExpressOrder = function (queryModel) {
    var recommendedExpressOrder = [];
    common.Ajax({
        url: '/NewOrder/LoadRecommendedExpressOrder',
        data: queryModel,
        async: false,
        showMasker: false,
        success: function (rsp) {
            if (rsp.Success) {
                recommendedExpressOrder = rsp.Data;
            }
        }
    })
    return recommendedExpressOrder;
}

/*
 * 批量修改打单备注
 */
module.BatchUpdatePrintRemark = function (notRecommendOrders) {
    if (notRecommendOrders == null && notRecommendOrders.length == 0)
        return;

    var setremarklist = [];
    for (var i = 0; i < notRecommendOrders.length; i++) {
        var notRecommendOrder = notRecommendOrders[i];
        var remark = '';
        if (notRecommendOrder.RecommendExpress != null && notRecommendOrder.RecommendExpress.length != 0) {
            remark += "推荐快递："
            remark += notRecommendOrder.RecommendExpress.join('，');
        }
        if (notRecommendOrder.NotRecommendExpress != null && notRecommendOrder.NotRecommendExpress.length != 0) {
            if (remark != '') remark += '、\r';
            remark += "不可达快递："
            remark += notRecommendOrder.NotRecommendExpress.join('，');
        }
        if (remark != '') {
            setremarklist.push({
                LogicOrderId: notRecommendOrder.LogicOrderId,
                Remark: remark
            });

            if (orderTableBuilder != undefined && orderTableBuilder.rows != null) {
                //刷新本地页面的备注
                var row = orderTableBuilder.rows[notRecommendOrder.Index];
                row.PrintRemark = remark;
                orderTableBuilder.refreshRow(row);
            }
        }
    }
    common.Ajax({
        url: '/NewOrder/BatchUpdatePrintRemark',
        data: { remarkList: setremarklist },
        success: function (rsp) {
            if (rsp.Success == false)
                console.log("批量修改打单备注失败:", rsp);
        }
    });
}
//#endregion

//#region 手工订单
var checkedBuyerDatas = []; //选择后收件人

module.loadVirtualShops = function (re) {

    var selectboxData = [];
    var selectObjData = [];//初始代选中的店铺
    var lastselectObjData = [];//上一次选中的店铺
    var last_selected_virtual_shopId = localStorage.getItem('last_selected_virtual_shopId_' + commonModule.CurrShop.Id);
    for (var i = 0; i < Shops.length; i++) {
        if (Shops[i].PlatformType == "Virtual") {
            var obj = {};
            obj.Value = Shops[i].ShopId;
            obj.Text = Shops[i].NickName;
            selectboxData.push(obj);
            if (last_selected_virtual_shopId == Shops[i].ShopId)
                lastselectObjData.push(obj);
        }

    }

    if (lastselectObjData.length > 0) {
        selectObjData = lastselectObjData;
        selectVirtualShopId = lastselectObjData[0].Value;
        selectVirtualSingleShopId = lastselectObjData[0].Value;
    }
    else if (selectboxData.length > 0) {
        var selectObj = {};
        selectObj.Value = selectboxData[0].Value;
        selectObj.Text = selectboxData[0].Text;
        selectObjData.push(selectObj);
        selectVirtualShopId = selectboxData[0].Value;
        selectVirtualSingleShopId = selectboxData[0].Value;
    }

    if (!re) {
        var vshopSelectInit = {
            eles: '#vshopSelect',
            data: selectboxData,
            emptyTitle: '请选择虚拟店铺', //设置没有选择属性时，出现的标题
            searchType: 1, //1出现搜索框，不设置不出现搜索框
            showWidth: '300px', //显示下拉的宽
            isRadio: true, //有设置，下拉框改为单选
            allSelect: false,//是否显示勾选框
            selectData: selectObjData,  //初始化数据
            successSyncCallBack: function (rsp) {
            },
            selectCallBack: function (selectData) {   //selectData为选中的值
                if (selectData.length > 0) {
                    selectVirtualShopId = selectData[0].Value;
                    localStorage.setItem('last_selected_virtual_shopId_' + commonModule.CurrShop.Id, selectData[0].Value);
                } else {
                    selectVirtualShopId = "";
                }
                $(".newMysearch-selct").css("border", "1px solid transparent");

            },
            delTitleListCallBack: function (selectData) {

            }
        };

        var loadVirtualShopsCheckSelectBox = new selectBoxModule2();
        loadVirtualShopsCheckSelectBox.initData(vshopSelectInit);
    }

    var vshopSelectInit_singleStatus = {
        eles: '#vshopSelect_singleStatus',
        data: selectboxData,
        emptyTitle: '请选择虚拟店铺', //设置没有选择属性时，出现的标题
        searchType: 1, //1出现搜索框，不设置不出现搜索框
        showWidth: '300px', //显示下拉的宽
        isRadio: true, //有设置，下拉框改为单选
        allSelect: false,//是否显示勾选框
        selectData: selectObjData,  //初始化数据
        successSyncCallBack: function (rsp) {
        },
        selectCallBack: function (selectData) {   //selectData为选中的值
            if (selectData.length > 0) {
                selectVirtualSingleShopId = selectData[0].Value;
                localStorage.setItem('last_selected_virtual_shopId_' + commonModule.CurrShop.Id, selectData[0].Value);
            } else {
                selectVirtualSingleShopId = "";
            }
        },
        delTitleListCallBack: function (selectData) {

        }
    };

    var loadVirtualShopsCheckSingleStatusSelectBox = new selectBoxModule2();
    loadVirtualShopsCheckSingleStatusSelectBox.initData(vshopSelectInit_singleStatus);
}

// 添加虚拟店铺弹窗
module.AddVirtualPanel = function () {

    var addDialog = layer.open({
        type: 1,
            title: "新增虚拟店铺",
            content: $('#adialog_addSupplier'),
            area: ['550'], //宽高
            btn: ['保存', '取消'],
            skin: 'wu-dailog',
            yes: function () {
            addVirtualShop(addDialog);
        },
        cancel: function () {
            layer.close(addDialog);
        }
    });
}
// 保存虚拟店铺
var addVirtualShop = function (loadIndex) {
    var name = $("#VirtualNameInput").val() || "";
    if (name == "") {
        layer.msg("虚拟店铺名称不能为空", { icon: 2 });
        return;
    }

    commonModule.Ajax({
        url: "/NewOrder/AddVirtualShop",
        type: "POST",
        data: { name: name },
        success: function (rsp) {
            if (rsp.Success) {
                //渲染虚拟店铺下拉框
                var data = rsp.Data || {};
                Shops.push(data);
                module.loadVirtualShops();
                layer.close(loadIndex);
                layer.msg("添加成功", { icon: 1 });

            }
            else {
                    layer.confirm(rsp.Message, { icon: 2, skin: 'wu-dailog' });
            }
        }
    });
}
// 加载虚拟店铺下拉框
var selectVirtualShopId = "";
var selectVirtualSingleShopId = "";

function offileAreaSelectCallBack(control) {
    var deep = control.attr('deep');
    if (deep > 1) {
        var dataValue = control.attr("data-value");
        var isExistsVal = control.find("option[value='" + dataValue + "']").length;
        if (isExistsVal > 0)
            control.val(dataValue).trigger('change');
    }
}
//加载地址级联选择
commonModule.LoadAreaInfoToControl('fromProvince-select', 1, function () {
}, offileAreaSelectCallBack, "name");
module.OffileChangeAddress = function (name) {
        // if ($("#input_buyer_address").hasClass("edmitActive")) {
            console.log("点击了");
        var $name = name;
        var $value = $(this).val();
        if ($value) {
            $(this).closest(".wu-selectWrap").addClass("wu-active");
        }
        edmitBuyer[$name] = $value;
        $("#input_buyer_address input[name=addr]").val(edmitBuyer.Address);
        $("#input_buyer_address input[name=inpAddr]").val(edmitBuyer.ProvinceName + edmitBuyer.CityName + edmitBuyer.DistrictName + edmitBuyer.Address);
        var html = edmitBuyer.Name + "，" + edmitBuyer.Mobile + "，" + edmitBuyer.ProvinceName + edmitBuyer.CityName + edmitBuyer.DistrictName + edmitBuyer.Address;
        $("#edmit_" + edmitBuyer.Id).html(html);
        $("#show_" + edmitBuyer.Id).attr("data-name", edmitBuyer.Name).attr("data-phone", edmitBuyer.Mobile).attr("data-province", edmitBuyer.ProvinceName).attr("data-city", edmitBuyer.CityName).attr("data-area", edmitBuyer.DistrictName).attr("data-addr", edmitBuyer.Address);
        for (var i = 0; i < checkedBuyerDatas.length; i++) {
            if (checkedBuyerDatas[i].Id == edmitBuyer.Id) {
                checkedBuyerDatas[i] = edmitBuyer;
            }
        }
        // }
}

//智能识别地址
module.initCheckedBuyer = function (checkedBuyerDatas, isRight) {
    if (checkedBuyerDatas.length == 0) {

        $("#buyersAddressWrap").empty();

        $("#input_buyer_address input[name=Name]").val('');
        $("#input_buyer_address input[name=Mobile]").val('');
        $("#input_buyer_address input[name=Phone]").val('');

        $("#input_buyer_address select[id=fromProvince-select]").val('0');
        $("#input_buyer_address select[id=fromCity-select]").val('0');
        $("#input_buyer_address select[id=fromArea-select]").val('0');
        $("#input_buyer_address input[name=addr]").val('');
        $("#input_buyer_address input[name=inpAddr]").val('');

        $("#buyersAddressWrap").hide();
        $("#input_buyer_address").show();

        return;
    }
    var tplt = $.templates("#checkedBuyers_rows_li");
    var html = tplt.render({ rows: checkedBuyerDatas });
    /*$("#buyersAddressWrap").html(html);*/

    if (checkedBuyerDatas.length > 1) {
        $("#buyersAddressWrap").show();
        //$("#input_buyer_address").hide();
    } else if (checkedBuyerDatas.length == 1) {
        $("#input_buyer_address input[name=Name]").val(checkedBuyerDatas[0].Name);
        $("#input_buyer_address input[name=Mobile]").val(checkedBuyerDatas[0].Mobile);
        $("#input_buyer_address input[name=Phone]").val(checkedBuyerDatas[0].Phone);
        var address = checkedBuyerDatas[0];
        var isExistsVal = $("#input_buyer_address select[id=fromProvince-select]").find("option[value='" + address.ProvinceName + "']").length;
        if (isExistsVal > 0) {
            $("#input_buyer_address select[id=fromCity-select]").attr("data-value", address.CityName).val(address.CityName);
            $("#input_buyer_address select[id=fromArea-select]").attr("data-value", address.DistrictName).val(address.DistrictName);
            $("#input_buyer_address select[id=fromProvince-select]").attr("data-value", address.ProvinceName).val(address.ProvinceName).change();
        }
        $("#input_buyer_address input[name=addr]").val(address.Address);
        $("#input_buyer_address input[name=inpAddr]").val(address.ProvinceName + address.CityName + address.DistrictName + address.Address);

        $("#input_buyer_address").show();
    } else {
        $("#buyersAddressWrap").hide();
        $("#input_buyer_address").show();
    }
    if (!isRight) {
        var $buyersAddressWrap = document.getElementById('buyersAddressWrap');
        $buyersAddressWrap.scrollTop = $buyersAddressWrap.scrollHeight;
    }
 }

// 识别用户地址信息
function IdentifyAddressInfo(values) {
    // 正则表达式，支持识别中文逗号、英文逗号、中文分号、英文分号和加号
    var regex = /([^\s，；\+\-,]+)[\s，；\+\-,]*([0-9\-]+)[\s，；\+\-,]*([^\s，；\+\-,]+)/;
    var match = values.match(regex);
    if (match) {
        return {
            name: match[1].trim(),
            mobile: match[2].trim(),
            address: match[3].trim()
        };
    } else {
        return {
            name: '',
            mobile: '',
            address: ''
        };
    }
}

$("#identificationAddress").on("blur", function () {
    var adderval = $(this).val();
    if (!adderval) return;

    $("#needConfirm_addr").remove();
    $("#input_buyer_address").removeClass("edmitActive");
    //var obj = { "Id": new Date().getTime(), "CompanyName": "工作", "Name": "陈先生1010", "Mobile": "13723479983", "Phone": "", "ProvinceName": "广东省", "CityName": "广州市", "DistrictName": "东山区", "ProvinceCode": null, "CityCode": null, "DistrictCode": null, "Address": "工作工作", "ShopId": 10, "UserId": 0, "CreatedTime": "2020-12-01 09:30:01" };
    //var addrArray = $(this).val().split(/\n/g);
    var addrArray = [adderval];
    checkedBuyerDatas = []; //每次只识别一条地址

    for (var i = 0; i < addrArray.length; i++) {
        var str = addrArray[i];
        if (str == "" || str == null)
            continue;
        str = str.replace(/>/g, '').replace(/</g, '').replace(/\t/g, ' ');
        var address = addressSpliter.parse(str, true);
        // 用户地址信息
        var userAddressInfo = IdentifyAddressInfo(str);
        var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        var $Id = chars.substr(Math.floor(Math.random() * 52), 1);
        var obj = { Id: $Id + (new Date().getTime()), Name: '', Mobile: '', Phone: '', ProvinceName: '', CityName: '', DistrictName: '', Address: '', IsError: false };
        if (!address || address.isSuccess == false) {
            obj.IsError = true;
            obj.Address = str;
            checkedBuyerDatas.push(obj)
            continue;
        }
        if (checkedBuyerDatas.length < 1) {
            if (address.needConfirm) {
                var tiphtml = "<div style='padding-left: 82px;' id='needConfirm_addr'>提示：自动识别的省市区详细地址可能有误。请注意确认。<br/><span style='color:#fe6f4f;padding-right: 5px;'><i>" + (i + 1) + ". </i>" + address.province + "</span><span style='color:#3aadff;padding-right: 5px;'>" + address.city + "</span><span style='color:#f29a1a;padding-right: 5px;'>" + address.area + "</span><span>" + address.addr + "</span></div>";
                $(tiphtml).insertAfter("#input_buyer_address");
            }
        }
        if (userAddressInfo.name) { obj.Name = userAddressInfo.name; }

        var regRecipientMobile = /(^400[0-9]{6}$)|(^800[0-9]{6}$)|(^(1[123456789]\d{9})$)|(^0[0-9]{2,3}-[0-9]{7,8}$)|(^0[0-9]{2,3}[0-9]{7,8}$)|(^([0-9]{7,8})$)|(^(1[123456789]\d{9}-\d{4})$)/;
        if (userAddressInfo.mobile) {
            if (!regRecipientMobile.test(userAddressInfo.mobile)) {
                wuFormModule.wu_toast({ type: 3, content: '收件人电话格式错误' });
                return false;
            }
            obj.Mobile = userAddressInfo.mobile;
            //if (address.mobile.indexOf('86-') != -1) {
            //    obj.Mobile = address.mobile.replace("86-", "");
            //} else {
            //    //86  +86  会自动隐藏符号，特殊操作
            //    if (address.mobile.length == 13) {
            //        obj.Mobile = address.mobile.substr(2, 11)
            //    }
            //}
        }
        if (address.phone) { obj.Phone = address.phone; }
        if (address.province) { obj.ProvinceName = address.province; }
        if (address.city) { obj.CityName = address.city; }
        if (address.area) { obj.DistrictName = address.area; }
        if (address.addr) { obj.Address = address.addr; }

        checkedBuyerDatas.push(obj)
    }
    $(this).val(''); //清空
    module.initCheckedBuyer(checkedBuyerDatas);

});
var edmitBuyer = {};
module.edmitCheckBuyer = function (id) {
    var isActive = $(this).hasClass("active");
    $("#buyersAddressWrap>li").removeClass("active");
    if (isActive) {
        $(this).removeClass("active")
    } else {
        $(this).addClass("active")
    }
    var isHasActive = false;
    $("#buyersAddressWrap>li").each(function (index, item) {
        if ($(item).hasClass("active")) {
            isHasActive = true;
        }
    })
    if (isHasActive) {
        for (var i = 0; i < checkedBuyerDatas.length; i++) {
            if (checkedBuyerDatas[i].Id == id) {
                edmitBuyer = checkedBuyerDatas[i];
            }
        }
        $("#input_buyer_address").addClass("edmitActive")
    } else {
        $("#input_buyer_address").removeClass("edmitActive")
    }

    $("#input_buyer_address input[name=Name]").val(edmitBuyer.Name);
    $("#input_buyer_address input[name=Mobile]").val(edmitBuyer.Mobile);
    $("#input_buyer_address input[name=Phone]").val(edmitBuyer.Phone);
    var isExistsVal = $("#input_buyer_address select[id=fromProvince-select]").find("option[value='" + edmitBuyer.ProvinceName + "']").length;
    if (isExistsVal > 0) {
        $("#input_buyer_address select[id=fromCity-select]").attr("data-value", edmitBuyer.CityName).val(edmitBuyer.CityName);
        $("#input_buyer_address select[id=fromArea-select]").attr("data-value", edmitBuyer.DistrictName).val(edmitBuyer.DistrictName);
        $("#input_buyer_address select[id=fromProvince-select]").attr("data-value", edmitBuyer.ProvinceName).val(edmitBuyer.ProvinceName).change();
    } else {
        $("#input_buyer_address select[id=fromProvince-select]").attr("data-value", 0).val(0).change();
    }
    $("#input_buyer_address input[name=addr]").val(edmitBuyer.Address);
    $("#input_buyer_address input[name=inpAddr]").val(edmitBuyer.ProvinceName + edmitBuyer.CityName + edmitBuyer.DistrictName + edmitBuyer.Address);



    $("#input_buyer_address.edmitActive>li input").on("input", function () {
        if ($("#input_buyer_address").hasClass("edmitActive")) {
            var $name = $(this).attr("name");
            var $value = $(this).val();
            if ($name == "Name" || $name == "Mobile") {
                edmitBuyer[$name] = $value;
            } else if ($name == "addr") {
                edmitBuyer.Address = $value;
            }
        }
        $("#input_buyer_address input[name=addr]").val(edmitBuyer.Address);
        if ($name != "inpAddr") {
            $("#input_buyer_address input[name=inpAddr]").val(edmitBuyer.ProvinceName + edmitBuyer.CityName + edmitBuyer.DistrictName + edmitBuyer.Address);
        }

        var html = edmitBuyer.Name + "，" + edmitBuyer.Mobile + "，" + edmitBuyer.ProvinceName + edmitBuyer.CityName + edmitBuyer.DistrictName + edmitBuyer.Address;
        $("#edmit_" + edmitBuyer.Id).html(html);
        $("#show_" + edmitBuyer.Id).attr("data-name", edmitBuyer.Name).attr("data-phone", edmitBuyer.Mobile).attr("data-province", edmitBuyer.ProvinceName).attr("data-city", edmitBuyer.CityName).attr("data-area", edmitBuyer.DistrictName).attr("data-addr", edmitBuyer.Address);
        for (var i = 0; i < checkedBuyerDatas.length; i++) {
            if (checkedBuyerDatas[i].Id == edmitBuyer.Id) {
                checkedBuyerDatas[i] = edmitBuyer;
            }
        }
    })
}
module.delCheckBuyer = function (id) {
    event.stopPropagation();
    $("#input_buyer_address").removeClass("edmitActive");
    for (var i = 0; i < checkedBuyerDatas.length; i++) {
        if (checkedBuyerDatas[i].Id == id) {
            checkedBuyerDatas.splice(i, 1)
        }
    }
    if (checkedBuyerDatas.length > 0) {
        module.initCheckedBuyer(checkedBuyerDatas, true);
    } else {
        $("#buyerChoseType>.contentMain-b").hide();
        $("#buyerChoseType>.contentMain-a").show();
        $("#newBuyerAddressWrap").show();
        $("#buyersAddressWrap").hide();
        $(".contentMain-right").show();

    }
}

//手工录单重置按钮
module.OffileOrderReset = function () {
    checkedBuyerDatas = [];
    module.loadVirtualShops(true);
    $("#buyersAddressWrap").empty();
    $("#buyersAddressWrap").hide();
    $("#inputbuyerAddress").find("select").val("0");
    var inputs = $('.offlineOrderDailog-item input[type=text]');
    for (var i = 0; i < inputs.length; i++) {
        $(inputs[i]).val('');
    }
    $("#inputbuyerAddress").find(".wu-selectWrap").removeClass("wu-active");
    $("#associated_platform_order_no").val('');
}

//快捷创建线下单 create_print：创建并打印，（只能创建一个订单项）
module.CreateOffileOrder = function (type) {
    //获取订单信息
    var getOffileInfo = function () {
        var $buyerInfo = $("#input_buyer_address");
        var $buyersAddressWrap = $("#buyersAddressWrap");
        var platformNo = $("#associated_platform_order_no").val().trim() || null;
        if (!$buyerInfo.is(":visible") && !$buyersAddressWrap.is(":visible"))
            return;
        var order = {};
        order.TradeType = "OfflineNoSku";
        // 收件人信息
        if (OfflineOrderImportType == '2') {
            order.ToProvince = $buyerInfo.find("input[name=associationProvince]").val() || "";
            order.ToCity = $buyerInfo.find("input[name=associationCity]").val() || "";
            order.ToCounty = $buyerInfo.find("input[name=associationArea]").val() || "";
        } else {
            order.ToProvince = $buyerInfo.find("#fromProvince-select").val() || "";
            order.ToCity = $buyerInfo.find("#fromCity-select").val() || "";
            order.ToCounty = $buyerInfo.find("#fromArea-select").val() || "";
        }
        order.ToAddress = $buyerInfo.find("input[name='addr']").val();
        order.ToFullAddress = order.ToProvince + order.ToCity + order.ToCounty + order.ToAddress;
        order.ToName = $buyerInfo.find("input[name='Name']").val() || "";
        order.ToPhone = $buyerInfo.find("input[name='Mobile']").val() || "";
        order.ExtField1 = $("#offline_print_remark").val() || "";  //打单备注

        var oi = {};
        oi.ProductSubject = $("#offline_ProductSubject").val() || "";  //商品名称
        oi.Color = $("#offline_Color").val() || "";     //颜色尺寸
        oi.Count = $("#offline_Count").val() || "";    //数量
        oi.Weight = $("#offline_Weight").val() || "";    //重量（kg）

        return { "OrderInfo": order, "OrderItemInfos": [oi], PlatformId: platformNo };
    }
    var validOrder = function (offileOrder) {
        var innerMsg = "";
        if (!selectVirtualSingleShopId) {
            innerMsg = "请选择虚拟店铺后再创建手工单";
            return innerMsg;
        }
        var order = offileOrder.OrderInfo;
        if (OfflineOrderImportType == '2') {
            if (!order.ToName || !order.ToPhone || !order.ToProvince || !order.ToCity || !order.ToCounty || !order.ToAddress) {
                innerMsg = "收件人姓名、电话、地址信息不能为空";
            }
        } else {
            if (!order.ToName || !order.ToPhone || !order.ToProvince || order.ToProvince == "0" || !order.ToCity || order.ToCity == "0" || !order.ToCounty || order.ToCounty == "0" || !order.ToAddress) {
                innerMsg = "收件人姓名、电话、地址信息不能为空";
            }
            // 特殊字符验证
            if (order.ToFullAddress.indexOf("*") > -1 || order.ToName.indexOf("*") > -1 || order.ToPhone.toString().indexOf("*") > -1) {
                innerMsg += "收件人信息不能包含特殊字符，可能导致无法打印发货，";
            }
        }
        var orderItems = offileOrder.OrderItemInfos;
        if (!orderItems && orderItems.length > 0) {

        }
        return innerMsg;
    }

    //2.回调打印
    var callbackPrint = function (newPids) {
        if (!newPids) return;
        //2.1 获取新订单
        var options = {
            Filters: [
                { TableAlias: 'o', FieldType: 'string', TableName: 'LogicOrder', Name: 'PlatformOrderId', Value: newPids.join(','), Contract: 'in', CustomQuery: 'PlatformOrderIdSearch' }
            ],
            IsOrderDesc: false,
            OrderByField: 'o.PayTime',
            IsPdd: false,
            IsCustomerOrder: false,
            FxPageType: 2
        };
        common.Ajax({
            url: '/NewOrder/List',
            data: options,
            type: 'POST',
            loadingMessage: "执行打印中，请勿关闭页面",
            success: function (rsp) {
                if (rsp.Success) {
                    var newRows = rsp.Data.Rows;
                    var isFxAutoMerger = rsp.Data.IsFxShopAutoMergerOpened;
                    // 更新隐藏设置非合单的拆字
                    $(newRows).each(function (i, r) {
                        commonModule.getBaseProductSetting()
                        r.NoPrintFlag = false;
                        for (var i = 0; i < r.WaybillCodes.length; i++) {
                            if (r.WaybillCodes[i].IsPreviewed == false && r.PrintState == 0) {
                                r.NoPrintFlag = true; // 已打印订单打印状态未更新
                                break;
                            }
                        }
                        if (r.SubOrders && r.SubOrders.length > 0) {
                            $(r.SubOrders).each(function (j, so) {
                                so.IsFxAutoMerger = isFxAutoMerger;
                                //so.StrDisabled = (isSearchWaitSellerSendState && (so.Status == "waitbuyerreceive")) ? " disabled=\"disabled\"" : "";//搜索了“待发货”且当前状态为“已发货”=禁用；先取消此功能。
                            });
                        }
                    });
                    orderTableBuilder.CreateNewRow(newRows, true);
                    $(".express-print-btn").click();//重新提交
                }
                else {
                    if (rsp.ErrorCode == 'limit_reule_error') {
                        common.LimitTimeDailg(rsp.ErrorCode);
                    }
                    else if (rsp.ErrorCode == 'FX_PAGEDEPTHCONTROL') {
                        common.PageDepthControlDailg('当前操作频繁，请使用导出功能！');
                    }
                    else if (rsp.ErrorCode != 2 && rsp.ErrorCode != 'auth_expires') {
                        layer.msg("数据加载失败，请刷新重试");
                    }
                }
            }
        });
    }
    //1.仅创建
    var createOrder = function (callBack) {
        var order = getOffileInfo();
        var msg = validOrder(order);
        if (msg) {
            layer.alert(msg.trimEndDgj("，"), { icon: 2, skin: 'wu-dailog' });
            return;
        }
        var vshopId = selectVirtualSingleShopId;
        // 保存到数据库

        $(".myloadingDailog").css({ display: "flex" }) //加载动画
        commonModule.Ajax({
            type: 'post',
            url: '/NewOrder/BulkImportOfflineOrder',
            //loadingMessage: "正在为您创建订单，请勿关闭页面",
            data: { "ShopId": vshopId, "FileName": "", "Rows": JSON.stringify([order]), "FxPageType": 2 },
            success: function (result) {
                $(".myloadingDailog").css({ display: "none" }) //关闭加载动画
                if (result.Success) {
                    layer.closeAll();
                    var newPids = result.Data.NewPids;
                    if (typeof (callBack) == "function") {
                        callBack(newPids);
                    }
                    else {
                        layer.msg("创建成功!", { time: 1500, icon: 1 });
                        $("#SeachConditions").click();
                    }
                }
                else {
                        layer.confirm(result.Message, { skin: 'wu-dailog' });
                }
            }
        });
    }

    //execute
    if (type == "create_print")
        createOrder(callbackPrint);
    else
        createOrder();
}
//展开
module.changeOfflineProductShow = function (that) {
    $(that).toggleClass("zk");
    $("#productInfo_into").toggle();
}

//创建点击事件
module.OrderCreateEvent = function (type) {
    var _this = $(this);
    var platformNo = $("#associated_platform_order_no").val().trim();
    if (currentPt == 'toutiao' && OfflineOrderImportType == '2' && !platformNo) {
        wuFormModule.wu_toast({ type: 3, content: '请输入关联平台订单号' });
        return false;
    }
    if (_this.attr('disabled')) return;
    _this.attr('disabled', true);
    setTimeout(function () {
        _this.attr('disabled', false);
        module.CreateOffileOrder(type);
    }, 200);
}

//新手指引教程
module.kuajingShowHelps = function () {
    var noviceIntroObj = {

        backgroundColor: "#000", 	//遮罩背景色
        opacity: 0.5, 				//遮罩透明度
        isStartButton: false, 		//是否开启 跳过使用步骤 显示直接去使用按钮
        startButtonTitle: '', 		//直接去使用按钮 名称   不设置默认为跳过:开始使用
        isStartButton: false, 		//是否开启 跳过使用步骤 显示直接去使用按钮
        isStartButton02: true,      //是否开启 跳过使用步骤 显示直接去使用按钮  这个按钮是显示在步骤旁边
        callBack: function () {     //最后关闭触发回调函数
            common.SaveCommonSetting(helpStepsName, "1", function (rsp) { }); //关闭后些帮助

        },
        steps: [
            {                   //步骤
                elem: ".kuajinWarn", 	//定位到某个元素上			
                top: 0,
                left: 0,
                control: { 			//控制区
                    top: 0,
                    left: 580,
                    align: "center", 	//控制区文字 对齐
                    arrowsIcon: true,//是否出现箭头图标
                    arrowsIconDirection: "center", //箭头指向    不设置 箭头指向左
                    stepCallBack: function () {//步骤回调函数
                        var $width = $(".kuajinWarn").width() + 38;
                        $("#noviceIntrogContent").css({ width: $width, height: '60px', backgroundColor: '#DFEFFD', display: 'flex', justifyContent: 'center' })
                        $("#noviceIntrogContent").find('.oviceIntrogControlDiv');
                        $("#noviceIntrogContent").find('.oviceIntrogControlDiv-up-icon').remove();
                        $("#noviceIntrogContent").find('.oviceIntrogControlDiv-down').addClass("fristControlDiv");
                        $("#noviceIntrogContent").find('.oviceIntrogControlDiv-down').prepend('<span class="fristControlDiv-text">查看跨境发货指南，轻松掌握发货规则。</span>');
                        $("#noviceIntrogContent").find('#oviceIntrog_downButton').text('下一步 (1/3)');
                        $("#noviceIntrogContent").find('#closeNoviceIntroAialog02').text('跳过');
                    }
                }
            },
            {                   //步骤
                elem: ".express-print-btn", 	//定位到某个元素上			
                isStopOperate: true,	// 是否可操作指定区 默认为true
                top: 200,
                left: 0,
                width: 1,
                height: 1,
                control: { 			//控制区
                    top: 4,
                    left: 4,
                    align: "left", 	//控制区文字 对齐
                    arrowsIcon: false,//是否出现箭头图标
                    arrowsIconDirection: "center", //箭头指向    不设置 箭头指向左
                    buttonTitle: "开始使用", //下一步  步骤按钮标题  默认为下一步，可以不设置
                    imgSrc: "../Content/Images/noviceIntroPic/kuajingShowHelps-step.png", //说明步骤  图片

                    stepCallBack: function () {//步骤回调函数
                        $("#noviceIntrogContent").find('.oviceIntrogControlDiv').css({ position: 'absolute' });
                        $("#noviceIntrogContent").find('#oviceIntrog_downButton').text('下一步 (2/3)');
                        $("#noviceIntrogContent").find('.fristControlDiv-text').html('<span>打印 TikTok 官方物流面单，根据发货指南打包好货品。</><span style="color: rgba(0, 0, 0, 0.4)">采用第三方物流发货则跳过此步</span>');
                        $("#noviceIntrogContent").find('.oviceIntrogControlDiv-down').addClass("secondControlDiv");
                        $("#noviceIntrogContent").css({ position: 'fixed' })

                    }
                }
            }
            ,
            {                   //步骤
                elem: ".batch-send-btn", 	//定位到某个元素上			
                isStopOperate: true,	// 是否可操作指定区 默认为true
                top: 200,
                left: 0,
                width: 1,
                height: 1,
                control: { 			//控制区
                    top: -145,
                    left: 5,
                    align: "left", 	//控制区文字 对齐
                    arrowsIcon: false,//是否出现箭头图标
                    arrowsIconDirection: "center", //箭头指向    不设置 箭头指向左
                    buttonTitle: "知道了 (3/3)", //下一步  步骤按钮标题  默认为下一步，可以不设置
                    imgSrc: "../Content/Images/noviceIntroPic/kuajingShowHelps-step-03.png", //说明步骤  图片
                    stepCallBack: function () {//步骤回调函数
                        $("#noviceIntrogContent").find('.oviceIntrogControlDiv').css({ position: 'absolute' });
                        $("#noviceIntrogContent").find('.oviceIntrogControlDiv-down').addClass("thridControlDiv");
                        $("#noviceIntrogContent").css({ position: 'fixed' })
                        $("#noviceIntrogContent").find('.fristControlDiv-text').html('为避免对账漏单，完成包裹发货后，请点击【交运/发货】，记录揽收方式或发货物流');

                    }
                }
            }


        ]
    };
        
    var newFun = new noviceIntro();
    newFun.initData(noviceIntroObj);

}
//#endregion

/**
 * 预检查处理，打印按钮事件前置检查，过滤
 * @method PrintPreCheckHandleNew
 * @param orders {object[]} 选择的订单列表
 * @param template {object} 选择的模板
 * @param callBack {function} 检查完之后回调方法
 */
 module.PrintPreCheckHandleNew = function (orders, template, callBack) {
     console.log("orders=====================", orders);
    // console.log("======template========", template);
    if (!orders.length) {
        layer.msg("请选择订单！");
        return;
    }
    if (!template) {
        layer.msg("请选择快递模板！");
        return;
    }

    // 模板类型
    var templateChecker = {
        toutiao: common.IsTouTiaoTemplate(template.TemplateType) || common.IsTouTiaozjTemplate(template.TemplateType) || commonModule.IsTouTiaoKuaiYunTemplate(template.TemplateType),
        kuaishou: common.IsKuaiShouTemplate(template.TemplateType),
        xiaohongshu: common.IsXiaoHongShuTemplate(template.TemplateType) || common.IsNewXiaoHongShuTemplate(template.TemplateType),
        wxvideo: common.IsWxVideoTemplate(template.TemplateType),
        site: common.IsSiteTemplate(template.TemplateType),
        pdd: common.IsPddTemplate(template.TemplateType),
        cainiao: common.IsCainiaoTemplate(template.TemplateType),
        jingdong: common.IsJdWjTemplate(template.TemplateType) || common.IsJdzjTemplate(template.TemplateType) || common.IsJdWjzjTemplate(template.TemplateType) || common.IsJdKdTemplate(template.TemplateType),
        jingdongpurchase: common.IsJdWjTemplate(template.TemplateType) || common.IsJdzjTemplate(template.TemplateType) || common.IsJdWjzjTemplate(template.TemplateType) || common.IsJdKdTemplate(template.TemplateType),
    };

     var categorizedOrders = getCategorizedOrders(orders, template);

    // 选中了京东订单，但是未使用京东订单打印时，弹出该弹窗
    if (!templateChecker.jingdong && categorizedOrders.jingdongOrderArray.length > 0) {
        showDkPrintWarn('京东', 'https://docs.qq.com/doc/DU05rV1h6RVJzZ1JQ?nlc=1');
        return;
    }
    // 京东供销订单，但是未使用京东订单打印时，弹出该弹窗
    if (!templateChecker.jingdongpurchase && categorizedOrders.jingdongpurchaseOrderArray.length > 0) {
        showDkPrintWarn('京东', 'https://docs.qq.com/doc/DU05rV1h6RVJzZ1JQ?nlc=1');
        return;
    }

     var toutiaoSFLogicOrderIds = categorizedOrders.toutiaoSFLogisticsOrderLogicOrderIds;
     var toutiaoJDLogicOrderIds = categorizedOrders.toutiaoJDLogisticsOrderLogicOrderIds;
     var mergedSFAndJDList = toutiaoSFLogicOrderIds.concat(toutiaoJDLogicOrderIds);
    
     console.log('========打印数据sf========', toutiaoSFLogicOrderIds);
     console.log('========打印数据jd========', toutiaoJDLogicOrderIds);

     // 只勾选了一条顺丰配送订单，判断是否选择了顺丰快递
     if (template.ExpressCompanyCode != 'SF' && orders.length == 1 && toutiaoSFLogicOrderIds.length == 1 && toutiaoJDLogicOrderIds.length == 0) {
        var tips = '已选择快递为' + template.ExpressCompanyName + '，当前订单为顺丰配送订单，请更换快递模板后重试。';
        layer.alert(tips, { title: '提示', area: '420px', skin: 'wu-dailog', }, function (index) {
            layer.close(index);
        });
        return false;
     }
     // 只勾选了一条京东配送订单，判断是否选择了京东快递
     if (template.ExpressCompanyCode != 'JD' && orders.length == 1 && toutiaoSFLogicOrderIds.length == 0 && toutiaoJDLogicOrderIds.length == 1) {
         var tips = '已选择快递为' + template.ExpressCompanyName + '，当前订单为京东配送订单，请更换快递模板后重试。';
         layer.alert(tips, { title: '提示', area: '420px', skin: 'wu-dailog', }, function (index) {
             layer.close(index);
         });
         return false;
     }

    //#region 直接中断打印的逻辑
    if (!templateChecker.toutiao && categorizedOrders.toutiaoOrderArray.length > 0) {
        showDkPrintWarn('抖音', 'https://docs.qq.com/doc/DQkhPWnlYWk1ZeUZT');
        return;
    }
    if (!templateChecker.kuaishou && categorizedOrders.kuaishouOrderArray.length > 0) {
        //快手模板打印公告
        showDkPrintWarn('快手', 'https://m0ij9216j9.feishu.cn/docx/KQ72d76rvoKU5OxKwnycvA4Ln9d');
        return;
    }
    if (categorizedOrders.alibabaOutChannelArray.length > 0) {
        showCommonPrintWarn('1688密文下单', categorizedOrders.alibabaOutChannelArray, 'https://docs.qq.com/doc/DQkhPWnlYWk1ZeUZT');
        return;
    }
    if (categorizedOrders.ownShop_TouTiaoOrder_LogicOrderIds.length > 0 && !templateChecker.toutiao) {
        //自有商城的抖店平台单必须使用抖店电子面单打印
        showDkPrintWarn('抖音', 'https://docs.qq.com/doc/DQkhPWnlYWk1ZeUZT');
        return;
    }
    if (categorizedOrders.ownShop_KuaiShouOrder_LogicOrderIds.length > 0 && !templateChecker.kuaishou) {
        //自有商城的快手平台单必须使用快手电子面单打印
        showDkPrintWarn('快手', 'https://m0ij9216j9.feishu.cn/docx/KQ72d76rvoKU5OxKwnycvA4Ln9d');
        return;
    }
    if (categorizedOrders.ownShop_TaobaoOrder_LogicOrderIds.length > 0 && !templateChecker.cainiao) {
        //自有商城的淘宝平台单必须使用菜鸟电子面单打印
        showDkPrintWarn('淘宝', 'https://docs.qq.com/doc/DU2dVdXlNZVp0Yk5s', '菜鸟');
        return;
    }
    if (categorizedOrders.ownShop_PinduoduoOrder_LogicOrderIds.length > 0 && !templateChecker.pdd) {
        //自有商城的拼多多平台单必须使用拼多多电子面单打印
        showDkPrintWarn('拼多多', 'https://docs.qq.com/doc/DU1VOVlpyeldaUEJt');
        return;
    }
    if (categorizedOrders.ownShop_XiaoHongShu_LogicOrderIds.length > 0 && !templateChecker.xiaohongshu) {
        //自有商城的小红书平台单必须使用小红书电子面单打印
        showDkPrintWarn('小红书', 'https://www.dgjapp.com/newHelpsShow.html?id=1696920759302');
        return;
    }
    if (categorizedOrders.taobao_maicaiOrderArray.length > 0 && !templateChecker.cainiao) {
        //淘宝买菜订单必须使用菜鸟电子面单打印
        showDkPrintWarn('淘宝买菜', 'https://docs.qq.com/doc/DU2dVdXlNZVp0Yk5s','菜鸟');
        return;
    }
    //#endregion

    // 过滤订单继续打单
    var continueFunc = function (allOrders, needIgnoreIds) {
        //过滤订单继续打印
        var _newOrders = [];
        if (needIgnoreIds && needIgnoreIds.length > 0) {
            for (var i = 0; i < allOrders.length; i++) {
                if (needIgnoreIds.indexOf(allOrders[i].LogicOrderId) == -1) {
                    _newOrders.push(allOrders[i]);
                }
                else {
                    //$('#order-' + allOrders[i].Index).click();

                    var $orderDiv = $('#order-' + allOrders[i].Index);
                    $orderDiv.removeClass("active");
                    var $chx = $orderDiv.find(".order-chx")[0];
                    $chx.checked = false;
                    $orderDiv.closest(".layui-row-item").removeClass("active");
                    $orderDiv.closest(".layui-row").removeClass("active");

                    var row = orderTableBuilder.rows[allOrders[i].Index];
                    row.checked = false;
                    $("#orderNum").text("[" + $(".order-chx:checked").length + "]");
                    $("#allcheckorder")[0].checked = false;
                }
            }
        } else {
            _newOrders = allOrders;
        }

        if (!_newOrders || _newOrders.length == 0) {
            layer.msg("当前您所选的订单已被全部过滤");
        }
        return _newOrders;
    }

    // 过滤条件
    var validateOrderFunc = function (needCheckOrders) {
        var checkOrders = [];
        var isHitRule = false;  // 是否命中校验

        // 包含顺丰配送的订单，是否跳过顺丰配送订单继续处理其他订单
        // 选中1个订单中同时包含顺丰配送商品和京东配送商品时，选中订单+顺丰配送商品+京东配送商品，视为顺丰配送订单
        if (!isHitRule && template.ExpressCompanyCode !== "SF" && orders.length >= 1 && toutiaoSFLogicOrderIds.length > 0 && toutiaoJDLogicOrderIds.length == 0) {
            module.ShowOrderUprate(function () {
                // 过滤顺丰配送订单，继续打印
                checkOrders = continueFunc(needCheckOrders, toutiaoSFLogicOrderIds);
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                toutiaoSFLogicOrderIds = [];
                toutiaoJDLogicOrderIds = [];
                validateOrderFunc(checkOrders);
            }, "toutiao_logistics_sf_order", {
                selectedOrderTotalCount: needCheckOrders.length,
                logicOrderIdCount: toutiaoSFLogicOrderIds.length,
                expressCompanyName: template.ExpressCompanyName,
            });
            isHitRule = true;
        }
        // 包含京东配送的订单，是否跳过京东配送订单继续处理其他订单
        if (!isHitRule && template.ExpressCompanyCode !== "JD" && orders.length > 1 && toutiaoSFLogicOrderIds.length == 0 && toutiaoJDLogicOrderIds.length > 0) {
            module.ShowOrderUprate(function () {
                // 过滤京东配送订单，继续打印
                checkOrders = continueFunc(needCheckOrders, toutiaoJDLogicOrderIds);
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                toutiaoSFLogicOrderIds = [];
                toutiaoJDLogicOrderIds = [];
                validateOrderFunc(checkOrders);
            }, "toutiao_logistics_jd_order", {
                selectedOrderTotalCount: needCheckOrders.length,
                logicOrderIdCount: toutiaoJDLogicOrderIds.length,
                expressCompanyName: template.ExpressCompanyName
            });
            isHitRule = true;
        }

        // 选中多个订单中有优质快递订单，同时包含顺丰配送和京东配送，过滤京东配送、顺丰配送订单
        if (!isHitRule && orders.length > 1 && toutiaoSFLogicOrderIds.length > 0 && toutiaoJDLogicOrderIds.length > 0) {
            function processOrder() {
                module.ShowOrderUprate(function () {
                    // 过滤京东配送、顺丰配送订单，继续打印
                    checkOrders = continueFunc(needCheckOrders, mergedSFAndJDList);
                    categorizedOrders = getCategorizedOrders(checkOrders, template);
                    toutiaoSFLogicOrderIds = [];
                    toutiaoJDLogicOrderIds = [];
                    validateOrderFunc(checkOrders);
                }, "toutiao_logistics_sf_and_jd_order", {
                    selectedOrderTotalCount: needCheckOrders.length,
                    SFLogicOrderIdCount: toutiaoSFLogicOrderIds.length,
                    JDLogicOrderIdCount: toutiaoJDLogicOrderIds.length,
                    expressCompanyName: template.ExpressCompanyName
                });
            }
            if (template.ExpressCompanyCode !== "SF" || template.ExpressCompanyCode !== "JD") {
                processOrder();
                isHitRule = true;
            }
        }

        var isOrderNoValue = needCheckOrders.some(function (item) {
            return item.ExtField2 == '';
        });
        console.log("isOrderNoValue", isOrderNoValue);
        // 打单发货-抖店分区，用户选择“订单编号”字段为空的明文线下单，使用抖店快递模板发起【打印快递单】
        if (!isHitRule && currentPt == 'toutiao' && categorizedOrders.plaintextOfflineOrderLogicOrderIds.length > 0 && templateChecker.toutiao && isOrderNoValue) {
            module.ShowOrderUprate(function (needignoreIds) {
                // 过滤此类明文线下单，继续打印
                checkOrders = continueFunc(needCheckOrders, needignoreIds);
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                validateOrderFunc(checkOrders);
            }, "toutiao_plaintext_offline_order", {
                selectOrderList: needCheckOrders
            });
            isHitRule = true;
        }
        // 打单发货-抖店分区，用户选择密文线下单，使用非抖音快递模板发起【打印快递单】
        if (!isHitRule && currentPt == 'toutiao' && categorizedOrders.ciphertextOfflineOrderLogicOrderIds.length > 0 && !templateChecker.toutiao) {
            module.ShowOrderUprate(function () {
                // 过滤此类密文线下单，继续打印
                checkOrders = continueFunc(needCheckOrders, categorizedOrders.ciphertextOfflineOrderLogicOrderIds);
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                validateOrderFunc(checkOrders);
            }, "toutiao_ciphertext_offline_order");
            isHitRule = true;
        }
        // 打单发货-抖店分区，用户选择明文线下单，使用非抖音快递模板发起【打印快递单】
        if (!isHitRule && currentPt == 'toutiao' && categorizedOrders.plaintextOfflineOrderLogicOrderIds.length > 0 && !templateChecker.toutiao) {
            module.ShowOrderUprate(function () {
                // 无需上传抖店后台，继续打印
                checkOrders = continueFunc(needCheckOrders, []);
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                validateOrderFunc(checkOrders);
            }, "toutiao_plaintext_offline_order_no_toutiao_template", {
                selectOrderList: needCheckOrders
            });
            isHitRule = true;
        }
        // 在其他分区，比如在精选平台、京东平台创建的明文线下单，使用抖店快递模板发起【打印快递单】
        if (!isHitRule && currentPt != 'toutiao' && categorizedOrders.plaintextOfflineOrderLogicOrderIds.length > 0 && templateChecker.toutiao) {
            module.ShowOrderUprate(function () {
                // 过滤此类明文线下单，继续打印
                checkOrders = continueFunc(needCheckOrders, categorizedOrders.plaintextOfflineOrderLogicOrderIds);
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                validateOrderFunc(checkOrders);
            }, "other_plaintext_offline_order_toutiao_template");
            isHitRule = true;
        }
        if (!isHitRule && categorizedOrders.kuaishou_presentOrder_logicOrderIds.length > 0) {
            module.ShowOrderUprate(function () {
                //过滤快手赠礼订单继续打印
                checkOrders = continueFunc(needCheckOrders, categorizedOrders.kuaishou_presentOrder_logicOrderIds);
                //categorizedOrders.kuaishou_presentOrder_logicOrderIds = [];
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                validateOrderFunc(checkOrders);
            }, "ks_presentOrder_uprate");
            isHitRule = true;
        }

        if (!isHitRule && categorizedOrders.exception_logicOrderIds.length > 0) {
            //存在已申请退款的异常单
            module.ShowOrderUprate(function () {
                //过滤异常单继续打印
                checkOrders = continueFunc(needCheckOrders, categorizedOrders.exception_logicOrderIds);
                //categorizedOrders.exception_logicOrderIds = [];
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                validateOrderFunc(checkOrders);
            }, "exception_order", {
                selectNum: needCheckOrders.length,
                logicOrderIds: categorizedOrders.exception_logicOrderIds
            });
            isHitRule = true;
        }

        if (!isHitRule && templateChecker.kuaishou && categorizedOrders.addressStopOrders.length > 0) {
            //地址有特殊字符使用快手模板
            addressStopOrderTips(categorizedOrders.addressStopOrders, function (needignoreIds) {
                //继续打印
                checkOrders = continueFunc(needCheckOrders, needignoreIds);
                //categorizedOrders.addressStopOrders = [];
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                validateOrderFunc(checkOrders);
            });
            isHitRule = true;
        }

        if (!isHitRule && categorizedOrders.toutiaoOrderArray.length > 0) {
            if (categorizedOrders.sf_free_shipping_logicOrderIds.length > 0 && template.ExpressCompanyCode != "SF") {
                //包含顺丰包邮的订单没有用顺丰模板打印
                module.ShowOrderUprate(function () {
                    //过滤异常单继续打印
                    checkOrders = continueFunc(needCheckOrders, categorizedOrders.sf_free_shipping_logicOrderIds);
                    //categorizedOrders.sf_free_shipping_logicOrderIds = [];
                    categorizedOrders = getCategorizedOrders(checkOrders, template);
                    validateOrderFunc(checkOrders);
                }, "toutiao_sf_free_shipping", {
                    selectNum: needCheckOrders.length,
                    logicOrderIds: categorizedOrders.sf_free_shipping_logicOrderIds
                });
                isHitRule = true;
            }
            else {
                touTiaoRecommendedExpress(orders, template, continueFunc, callBack);
                isHitRule = true;
            }
        }

        if (!isHitRule && categorizedOrders.kuaishou_pintuan_logicOrderIds.length > 0) {
            module.ShowOrderUprate(function () {
                //过滤拼团单继续打印
                checkOrders = continueFunc(needCheckOrders, categorizedOrders.kuaishou_pintuan_logicOrderIds);
                //categorizedOrders.kuaishou_pintuan_logicOrderIds = [];
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                validateOrderFunc(checkOrders);
            }, "kspintuan_uprate");
            isHitRule = true;
        }

        if (!isHitRule && categorizedOrders.ks_sf_free_shipping_logicOrderIds.length > 0 && template.ExpressCompanyCode != "SF") {
            //包含顺丰包邮的订单没有用顺丰模板打印
            module.ShowOrderUprate(function () {
                //过滤顺丰包邮订单继续打印
                checkOrders = continueFunc(needCheckOrders, categorizedOrders.ks_sf_free_shipping_logicOrderIds);
                //categorizedOrders.ks_sf_free_shipping_logicOrderIds = [];
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                validateOrderFunc(checkOrders);
            }, "kuaishou_sf_free_shipping", {
                selectNum: orders.length,
                logicOrderIds: categorizedOrders.ks_sf_free_shipping_logicOrderIds
            });
            isHitRule = true;
        }

            if (!isHitRule && categorizedOrders.tb_sf_free_or_door_shipping_logicOrderIds.length > 0 && template.ExpressCompanyCode !== "SF") {
                //包含顺丰包邮的订单没有用顺丰模板打印
                module.ShowOrderUprate(function (isContinue) {
                    //过滤顺丰包邮订单继续打印
                    if (isContinue === false) {
                        checkOrders = continueFunc(needCheckOrders, categorizedOrders.tb_sf_free_or_door_shipping_logicOrderIds);
                        categorizedOrders = getCategorizedOrders(checkOrders, template);
                        validateOrderFunc(checkOrders);
                    } else {
                        categorizedOrders.tb_sf_free_or_door_shipping_logicOrderIds = [];
                        validateOrderFunc(needCheckOrders);
                    }
                }, "taobao_sf_free_or_door_shipping", {
                    selectNum: orders.length,
                    logicOrderIds: categorizedOrders.tb_sf_free_or_door_shipping_logicOrderIds
                });
                isHitRule = true;
            }

        if (!isHitRule && !templateChecker.xiaohongshu && categorizedOrders.xiaoHongShuArray.length > 0) {
            //校验使用小红书面单，继续打印会跳过这个逻辑
            module.ShowOrderUprate(function (needignoreIds) {
                //过滤订单继续打印
                checkOrders = continueFunc(needCheckOrders, needignoreIds);
                //categorizedOrders.xiaoHongShuArray = [];
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                validateOrderFunc(checkOrders);
            }, "xhstemp_uprate", {
                selectAllOrder: needCheckOrders,
                selectPtOrder: categorizedOrders.xiaoHongShuArray
            });
            isHitRule = true;
        }

        if (!isHitRule && !templateChecker.wxvideo && categorizedOrders.wxVideoArray.length > 0) {

            //校验使用视频号面单，继续打印会跳过这个逻辑
            module.ShowOrderUprate(function (needignoreIds) {
                //过滤订单继续打印
                checkOrders = continueFunc(needCheckOrders, needignoreIds);
                //categorizedOrders.wxVideoArray = [];
                categorizedOrders = getCategorizedOrders(checkOrders, template);
                validateOrderFunc(checkOrders);
            }, "wxvideotemp_uprate", {
                selectAllOrder: needCheckOrders,
                selectPtOrder: categorizedOrders.wxVideoArray
            });
            isHitRule = true;
        }
        console.log("打印needCheckOrders", needCheckOrders);
        console.log("打印isHitRule", isHitRule);
        if (isHitRule || (!needCheckOrders || needCheckOrders.length == 0)) {
            return checkOrders;
        } else {
            // 继续打印
            callBack(needCheckOrders);
        }
    }


    if (template.TemplateType == 10) {
        //丰桥模板，提示升级云模板
        module.FqTempUprate(function () {
            validateOrderFunc(orders);
        });
    }
    else {
        validateOrderFunc(orders);
    }
    return;
}

//获取分类订单过滤后数量
var getCategorizedOrders = function (orders, template) {
    // 分类订单
    var categorizedOrders = {
        toutiaoOrderArray: [], //头条订单
        kuaishouOrderArray: [], //快手订单
        alibabaOrderArray: [], //阿里巴巴订单
        xiaoHongShuArray: [], //小红书订单
        wxVideoArray: [], //视频号订单
        alibabaOutChannelArray: [], //1688密文订单类型
        kuaishou_pintuan_logicOrderIds: [], //拼团订单
        kuaishou_presentOrder_logicOrderIds: [],//快手赠礼订单
        sf_free_shipping_logicOrderIds: [], //抖店 顺丰包邮订单
        ks_sf_free_shipping_logicOrderIds: [], //快手 顺丰包邮订单
            tb_sf_free_or_door_shipping_logicOrderIds: [], //淘宝 顺丰包邮/上门订单
        addressStopOrders: [], //是否省市区特殊字符订单
        exception_logicOrderIds: [],//异常的订单
        ownShop_TouTiaoOrder_LogicOrderIds: [],//自有商城的抖店平台单
        ownShop_KuaiShouOrder_LogicOrderIds: [],//自有商城的快手平台单
        ownShop_TaobaoOrder_LogicOrderIds: [],//自有商城的淘宝平台单
        ownShop_PinduoduoOrder_LogicOrderIds: [],//自有商城的拼多多平台单
        ownShop_XiaoHongShu_LogicOrderIds: [],//自有商城的小红书平台单
        taobao_maicaiOrderArray: [], // 淘宝买菜订单
        jingdongOrderArray: [], // 京东订单
        jingdongpurchaseOrderArray: [], // 京东供销订单
        offlineOrderArray: [], // 线下单
        plaintextOfflineOrderLogicOrderIds: [], // 明文线下单
        ciphertextOfflineOrderLogicOrderIds: [], // 密文线下单
        toutiaoSFLogisticsOrderLogicOrderIds: [], // 顺丰配送订单id
        toutiaoJDLogisticsOrderLogicOrderIds: [] // 京东配送订单id
    };
    // 获取勾选的商品编码
    var $checkedList = $(".productShow-itemInput.orderitem-chx:checked");
    var CheckedProductIdList = [];
    $checkedList.each(function (i, item) {
        var $productId = $(this).attr("data-id");
        CheckedProductIdList.push($productId);
    });
    // 查找并处理符合条件的SubOrder
    function processSubOrders(order, tags, subOrders, checkedProductIds, logisticsData, logisticsOrderLogicIds) {
        tags.forEach(function (tag) {
            var matchSubOrder = subOrders.find(function (subOrder) {
                return subOrder.OrderItemCode === tag.OiCode;
            });
            if (matchSubOrder && checkedProductIds.includes(matchSubOrder.OrderItemId.toString())) {
                logisticsData.push(matchSubOrder); // 添加符合条件的SubOrder
            }
        });
        if (logisticsData.length > 0) {
            logisticsOrderLogicIds.push(order.LogicOrderId);
        }
    }
    for (var i = 0; i < orders.length; i++) {
        var _order = orders[i];
        var rowOrder = orderTableBuilder.rows[_order.Index];
        // 优质快递服务
        if (_order.PlatformType == "TouTiao") {
            if (commonModule.HasTag(_order.OrderTags, 'toutiao_high_quality_logistics', 'OrderItem')) {
                _order.SFDeliveryOrderData = [];
                _order.JDDeliveryOrderData = [];
                var relevantSFTags = _order.OrderTags.filter(function (tag) {
                    return tag.Tag === 'toutiao_high_quality_logistics' && tag.TagType === 'OrderItem' && tag.TagValue === 'shunfeng';
                });

                var relevantJDTags = _order.OrderTags.filter(function (tag) {
                    return tag.Tag === 'toutiao_high_quality_logistics' && tag.TagType === 'OrderItem' && tag.TagValue === 'jd';
                });

                // 处理顺丰和京东的SubOrder
                if (relevantSFTags.length > 0) {
                    processSubOrders(_order, relevantSFTags, _order.SubOrders, CheckedProductIdList, _order.SFDeliveryOrderData, categorizedOrders.toutiaoSFLogisticsOrderLogicOrderIds);
                }
                if (relevantJDTags.length > 0) {
                    processSubOrders(_order, relevantJDTags, _order.SubOrders, CheckedProductIdList, _order.JDDeliveryOrderData, categorizedOrders.toutiaoJDLogisticsOrderLogicOrderIds);
                }
                categorizedOrders.toutiaoJDLogisticsOrderLogicOrderIds = categorizedOrders.toutiaoJDLogisticsOrderLogicOrderIds.filter(function (item) {
                    return !categorizedOrders.toutiaoSFLogisticsOrderLogicOrderIds.includes(item);
                });
            }
        }

        // 京东订单
        if (_order.PlatformType == "Jingdong") {
            categorizedOrders.jingdongOrderArray.push(_order);
        }
        // 京东供销订单JingdongPurchase
        if (_order.PlatformType == "JingdongPurchase") {
            categorizedOrders.jingdongpurchaseOrderArray.push(_order);
        }
        // 线下单
        if (_order.PlatformType == "Virtual") {
            categorizedOrders.offlineOrderArray.push(_order);
            // 是否存在明文线下单
            //if (!commonModule.HasTag(_order.OrderTags, 'EncryptOfflineOrder', 'Order') || !commonModule.HasTag(_order.OrderTags, 'OutChannel', 'Order')) {
            //    categorizedOrders.plaintextOfflineOrderLogicOrderIds.push(_order.LogicOrderId);
            //}
            // 是否存在密文线下单
            if (commonModule.HasTag(_order.OrderTags, 'EncryptOfflineOrder', 'Order')) {
                categorizedOrders.ciphertextOfflineOrderLogicOrderIds.push(_order.LogicOrderId);
            } else {
                categorizedOrders.plaintextOfflineOrderLogicOrderIds.push(_order.LogicOrderId);
            }
            console.log('====明文线下单====', categorizedOrders.plaintextOfflineOrderLogicOrderIds);
            console.log('=====密文线下单====', categorizedOrders.ciphertextOfflineOrderLogicOrderIds);
        }

        if (_order.PlatformType == "KuaiShou") {
            categorizedOrders.kuaishouOrderArray.push(_order);

            //快手订单判断是否存在拼团单，存在需要提醒过滤
            if (commonModule.HasTag(_order.OrderTags, 'Kuaishou_orderPiecingGroup', 'Order') && (!_order.Receiver || !_order.Receiver.ToMaskAddress))
                categorizedOrders.kuaishou_pintuan_logicOrderIds.push(_order.LogicOrderId);

            //快手订单判断是否存在赠礼订单，存在需要提醒过滤
            if (commonModule.HasTag(_order.OrderTags, 'Present_Order', 'OrderItem') && !_order.Receiver.ToAddress)
                categorizedOrders.kuaishou_presentOrder_logicOrderIds.push(_order.LogicOrderId);

            // 是否存在顺丰包邮订单
            if (commonModule.HasTag(_order.OrderTags, 'sf_free_shipping', 'Order')) {
                categorizedOrders.ks_sf_free_shipping_logicOrderIds.push(_order.LogicOrderId);
            }
        }
        if (_order.PlatformType == "Alibaba") {
            categorizedOrders.alibabaOrderArray.push(_order);

            var outChannelType = check1688OutChannelTemp(_order, template.TemplateType);
            if (outChannelType != "other" && categorizedOrders.alibabaOutChannelArray.indexOf(outChannelType) == -1)
                categorizedOrders.alibabaOutChannelArray.push(outChannelType);
        }
        if (_order.PlatformType == "TouTiao") {
            categorizedOrders.toutiaoOrderArray.push(_order);

            //顺丰包邮
            if (module.IsExistSfFreeShippingOrder(_order))
                categorizedOrders.sf_free_shipping_logicOrderIds.push(_order.LogicOrderId);
        }
        if (_order.PlatformType == "XiaoHongShu") {
            categorizedOrders.xiaoHongShuArray.push(_order);
        }
        if (_order.PlatformType == "WxVideo") {
            categorizedOrders.wxVideoArray.push(_order);
        }
            if (_order.PlatformType == "TaobaoMaiCai") {
                categorizedOrders.taobao_maicaiOrderArray.push(_order);
            }
        //已申请退款异常
        if (commonModule.HasTag(_order.OrderTags, 'AliFxPurchaseException', 'LogicOrder')) {
            categorizedOrders.exception_logicOrderIds.push(_order.LogicOrderId);
        }

        //检查发件人省市区是否合法
        if (rowOrder) {
            //检查省市区是否为空
            var dijishi = ["东莞市", "中山市", "潜江市", "神农架林区", "天门市", "仙桃市", "济源市", "白沙黎族自治县", "保亭黎族苗族自治县", "昌江黎族自治县", "澄迈县", "定安县", "东方市", "儋州市", "乐东黎族自治县", "临高县", "陵水黎族自治县", "琼海市", "琼中黎族苗族自治县", "文昌市", "屯昌县", "万宁市", "五指山市", "阿拉尔市", "北屯市", "胡杨河市", "可克达拉市", "昆玉市", "双河市", "石河子市", "铁门关市", "图木舒克市", "五家渠市", "嘉峪关市"];
            if (!rowOrder.ToProvince || !rowOrder.ToCity) {
                categorizedOrders.addressStopOrders.push(_order);
            }
            else if (!rowOrder.ToCounty && dijishi.indexOf(rowOrder.ToCity) == -1) {
                categorizedOrders.addressStopOrders.push(_order);
            }
            //省市区不能包含特殊字符
            else if (!commonModule.isAllChinese(rowOrder.ToProvince) || !commonModule.isAllChinese(rowOrder.ToCity) || !commonModule.isAllChinese(rowOrder.ToCounty) || (rowOrder.ToTown && !commonModule.isAllChinese(rowOrder.ToTown))) {
                categorizedOrders.addressStopOrders.push(_order);
            }
        }
        //检查自有商城订单是否有平台标签
        if (_order.PlatformType == "OwnShop") {
            if (commonModule.HasTag(_order.OrderTags, 'OwnShop_EncryptOrder', 'Order')) {
                var firstTagValue = commonModule.FirstTagValue(_order.OrderTags, "OwnShop_EncryptOrder", "Order");
                if (firstTagValue == 'TouTiao') {
                    categorizedOrders.ownShop_TouTiaoOrder_LogicOrderIds.push(_order.LogicOrderId);//抖店平台单
                } else if (firstTagValue == 'KuaiShou') {
                    categorizedOrders.ownShop_KuaiShouOrder_LogicOrderIds.push(_order.LogicOrderId);//快手平台单
                } else if (firstTagValue == 'Taobao') {
                    categorizedOrders.ownShop_TaobaoOrder_LogicOrderIds.push(_order.LogicOrderId);//淘宝平台单
                } else if (firstTagValue == 'Pinduoduo') {
                    categorizedOrders.ownShop_PinduoduoOrder_LogicOrderIds.push(_order.LogicOrderId);//拼多多平台单
                } else if (firstTagValue == 'XiaoHongShu') {
                    categorizedOrders.ownShop_XiaoHongShu_LogicOrderIds.push(_order.LogicOrderId);//小红书平台单
                }
                }
            }

            // 检查淘宝是否存在顺丰包邮或上门标签
            if (_order.PlatformType === "Taobao") {
                // 是否存在顺丰包邮订单
                if (commonModule.HasTag(_order.OrderTags, 'sf_free_shipping', 'Order') ||
                    commonModule.HasTag(_order.OrderTags, 'sf_door_shipping', 'Order') 
                ) {
                    categorizedOrders.tb_sf_free_or_door_shipping_logicOrderIds.push(_order.LogicOrderId);
            }
        }
    }
    return categorizedOrders;
}

var showTaobaoMaiCaiDeliveryDailog = function () {
    var isShow = false
    for (var i = 0; i < PlatformTypes.length; i++) {
        if (PlatformTypes[i].PlatformType == 'TaobaoMaiCai') {
            isShow = true;
            break;
        }
    }
    if (isShow && commonModule.CloudPlatformType == "Alibaba")
        $("#taobaoMaiCaiDeliveryDailog").show();
    else
        $("#taobaoMaiCaiDeliveryDailog").hide();
}

return module;
}(waitOrderModule || {}, commonModule, jQuery, layer));