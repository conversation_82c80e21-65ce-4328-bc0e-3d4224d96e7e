var closeSellLevelGuideLayer = null;
var myAgentModule = (function (module, commmon, $, layer) {

    var getBindSupplierStatus_INTERVAL = null;
    //列表数据
    var dataList = {};
    var reqModel = {
        Key: "",
        Status: 0,
        PageIndex: 1,
        PageSize: 50,
        FxUserId: 0
    };
    var IsWhiteUserFlag = true;
    $(function () {
        var getSettingByKey = function (key) {
            for (var index in commonModule.FxUserSettings) {
                var item = commonModule.FxUserSettings[index];
                if (item.Key == key) {
                    return item.Value;
                }
            }
            return null;
        }
        getIsWhiteUserData();
        var curShopId = commonModule.CurrShop.ShopId;
        if (getSettingByKey("/FenFa/System/Config/UnSetMemberLevelDialog") != 'true') {
            // 首次访问，显示入口提示
            ShowLevelEntryDialog();
            window.sessionStorage.setItem('hasVisitedMyAgentPage', true);
        }

        //外部过来的参数
        if (outStatus != undefined && outStatus != "") {
            $("#sel_status").val(outStatus);
        }

        var iupKey = commmon.getQueryVariable('key') ? commmon.getQueryVariable('key') : '';

        //reqModel.HideCancelUser = HideCancelAgent;
        if (iupKey) {
            $("#iup_key").val(iupKey);
        } 
        module.LoadList(false);
        layui.form.render();
        // layui.form.render("select");
        //// 是否显示已取消厂家
        //$(".hideCancelAgent").change(function () {
        //    var val = $(this).prop("checked") ? "1" : "0";
        //    commmon.SaveCommonSetting("/ErpWeb/MyAgent/HideCancelAgent", val, function (rsp) {
        //        if (rsp.Success) {
        //            reqModel.HideCancelUser = val;
        //            module.LoadList();
        //        }
        //    });
        //});

        //$("input[name=prePayType]").on("change", function () {
        //    var val = $(this).val();
        //    if (val == 1) {

        //        var tplt = $.templates("#advancePaymentSetDailog_data")
        //        var html = tplt.render();
        //        layer.open({
        //            type: 1,
        //            title: "确定", //不显示标题
        //            content: html,
        //            area: '900', //宽高
        //            btn: ['已与分销商确认并开启', '暂不开启'],
        //            yes: function () { },
        //            cancel: function () { }
        //        });

        //    }
        //})

        /* 不显示非合作关系 操作逻辑*/
        var FxUserSettings = commmon.FxUserSettings;
        FxUserSettings.forEach(function (item, i) {
            if (item.Key.indexOf("IsShowCancelUser") != -1) {
                $("input[name='IsShowCancelUser']").attr("checked", item.Value == "true" ? true : false);
                layui.form.render('checkbox');
            }
        })
        layui.form.on('switch(IsShowCancelUser)', function (data) {
            commonModule.Ajax({
                url: "/System/SaveCommonSetting",
                type: "POST",
                data: { settingKey: "/FenFa/System/Config/IsShowCancelUser", settingValue: this.checked },
                success: function (rsp) {
                    module.LoadList();
                    if (rsp.Success == false) {
                        return;
                    }
                }
            });
        });
        wuFormModule.initblurInput('#searchWrap_iup_key');
        wuFormModule.initLayuiSelect('active-select-filter');
    });

    closeSellLevelGuideLayer = function () {
        $(".sell_level_guide_wrapper").hide();
        commmon.SaveCommonSetting("/FenFa/System/Config/UnSetMemberLevelDialog", true, function (rsp) {
           // console.log("SaveCommonSetting====", rsp)
        });
    }

    // 新增分销等级入口提示
    function ShowLevelEntryDialog() {
        $(".sell_level_guide_wrapper").show();
        var sellLevelSettingBtnPosition = $("#sell_level_setting_btn_dom").offset();
        var countX = 9;
        var countY = 8;
        $(".sell_level_guide_content").css({
            left: (sellLevelSettingBtnPosition.left - countX) + 'px',
            top: (sellLevelSettingBtnPosition.top - countY) + 'px'
        });
    }


    module.LoadList = function (isPaging) {
        reqModel.Key = $.trim($("#iup_key").val());
        reqModel.Status = $("#sel_status").val();
        //1.ajax
        commonModule.Ajax({
            url: '/Partner/LoadMyAgentList',
            data: { key: reqModel.Key, status: reqModel.Status, pageIndex: reqModel.PageIndex, pageSize: reqModel.PageSize, hideCancelUser: reqModel.HideCancelUser },
            async: true,
            loading: true,
            type: 'POST',
            success: function (rsp) {
                if (commonModule.IsError(rsp)) {
                    return;
                }
                var data = rsp.Data.List || [];
                //2.渲染
                var tplt = $.templates("#shareAgentList_data_tr");
                commonModule.Foreach(data, function (i, obj) {
                    dataList[obj.Id] = obj;
                });
                var html = tplt.render({ agentData: data });
                $("#ShareAgentList_body").html(html);

                $("#newSteps_01").css({ display: 'flex' });
                $("#newSteps_02").css({ display: 'flex' });

                commonModule.HideNoPermDiv(commonModule.MyAgentShowPermDict);

                //3.分页
                if (isPaging == true) {
                    return;
                }

                layui.laypage.render({
                    elem: 'paging',
                    theme: ' wu-page',
                    count: rsp.Data.Total,
                    limit: reqModel.PageSize,
                    curr: reqModel.PageIndex,
                    limits: [50, 100, 150, 200, 300, 500],
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function (obj, first) {
                        //$(".layui-laypage-count").html()
                        if (!first) {
                            reqModel.PageSize = obj.limit;
                            reqModel.PageIndex = obj.curr;
                            module.LoadList(true);
                        }
                    }
                });
            }
        });
    }

    // 是否白名单用户
    function getIsWhiteUserData() {
        commonModule.ajax({
            type: 'GET',
            url: '/api/Common/GetIsWhiteUser',
            success: function (res) {
                if (res.Success) {
                    IsWhiteUserFlag = res.Data;
                }
            }
        });
    }

    // 查看商家名片
    module.CheckQualification = function (id, status) {
        if (status === '2' || status === '3' || status === '4') {
            commonModule.w_alert({ type: 3, content: '当前绑定状态不能查看！' });
            return;
        }
        // sessionStorage.removeItem("PartnerCardData_" + id);
        commmon.Ajax({
            url: "/Partner/GetAgentCardById",
            type: "GET",
            data: { id: id },
            success: function (rsp) {
                // sessionStorage.setItem("PartnerCardData_" + id, JSON.stringify(rsp.Data));
                if (IsWhiteUserFlag) {
                    window.open(commmon.rewriteUrl("/GeneralizeIndex/MyStationCard?id=" + id + "&userType=agent"), '_self');
                } else {
                    window.open(commmon.rewriteUrl("/Partner/CheckQualification?id=" + id + "&userType=agent"), '_self');
                }
            },
            error: function (error) {
                if (error.status == 401) {
                    commonModule.w_alert({ type: 3, content: '暂无权限，请联系管理员' });
                }
            }
        });
    }

    module.Search = function () {
        reqModel.PageIndex = 1;
        module.LoadList(false);
    }

    var addWarnAddWarnDialog = null;
    module.showAddWarn = function () {
        addWarnAddWarnDialog = layer.open({
            type: 1,
            title: false, //不显示标题
            content: '<div style="width:600px;height:400px;position: relative;"><span onclick="myAgentModule.Add()" style="display: inline-block;position: absolute;bottom: 55px;left: 246px;width: 178px;height: 46px;cursor: pointer;"></span><img src="/Content/images/noviceIntroPic/anquanWarn-2022-10-26-02.png" /></div>',
            area: ['550'], //宽高
            // skin: 'wu-dailog',
            btn: false,
            closeBtn: false
        });
    }

    module.Add = function () {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                } else {
                    layer.close(addWarnAddWarnDialog);
                    return
                };
            }, p.AddBindAgent);
        });

        var thisFunc = function () {
            if (addWarnAddWarnDialog) {
                layer.close(addWarnAddWarnDialog);
            }
            var checkResult = commonModule.CheckVirtualRegMobile();
            if (!checkResult) {
                return;
            }
            $("#agent_key").val("");
            $(".IsNumderNull,.IsShowName").hide();
            $('input[name="prePayType"][value="0"]').prop("checked", true); // 默认选中“先发货后结款”
            var addDialog = layer.open({
                type: 1,
                title: "绑定商家", //不显示标题
                skin: 'wu-dailog',
                content: $('#adialog_addDistributor'),
                area: ['420'], //宽高
                btn: ['申请绑定', '取消'],
                yes: function () {
                    if ($(".layui-layer-btn0").attr("disabled") == "disabled") {
                        return;
                    }
                    var key = $.trim($("#agent_key").val());
                    if (key == "" || key == null) {
                        $(".IsNumderNull").html("商家不能为空").show();
                        //layer.msg("商家不能为空");
                        return;
                    }
                    var agentId = $("#agent_key").attr("user_id");
                    if (agentId <= 0 || agentId == null) {
                        //layer.msg($(".IsNumderNull").text());
                        return;
                    }
                    //1688预付设置
                    var prePayType = $('input[name="prePayType"]:checked').val();
                    if (prePayType == "1") {
                        //如果先预付，则弹出确认框
                        var tplt = $.templates("#advancePaymentSetDailog_data")
                        var html = tplt.render();
                        var confirmDialog = layer.open({
                            type: 1,
                            title: false, //不显示标题
                            content: html,
                            skin: 'adialog-Shops-skin',
                            area: '900', //宽高
                            btn: ['已与分销商确认并开启'],
                            yes: function () {

                                //module.OpenConfirmIsPrePay(addDialog);
                                //提交保存
                                module.PostAddBindAgent(addDialog);
                                layer.close(confirmDialog);
                            },
                        });
                        return false;
                    }
                    else {
                        //提交保存
                        module.PostAddBindAgent(addDialog);
                    }
                },
                cancel: function () {
                    layer.closeAll();
                }
            });
        }
    }
    //提交绑定商家
    module.PostAddBindAgent = function (addDialog) {
        //商家ID
        var agentId = $("#agent_key").attr("user_id");
        //1688预付设置
        var prePayType = $('input[name="prePayType"]:checked').val();
        var isPrePay = null;
        if (prePayType) {
            isPrePay = prePayType == "1" ? true : false;
        }
        $(".layui-layer-btn0").attr("disabled", true);
        commmon.Ajax({
            url: "/Partner/AddBindAgent",
            type: "POST",
            loading: true,
            data: { agentId: agentId, remark: $.trim($("#agent_remark").val()), isPrePay: isPrePay },
            success: function (rsp) {
                if (commonModule.IsError(rsp)) {
                    //layer.closeAll();
                    $(".layui-layer-btn0").attr("disabled", false);
                    return true;
                }
                $(".layui-layer-btn0").removeAttr("disabled");
                layer.msg('已发送合作申请，请联系商家同意合作!', { icon: 1 });
                module.LoadList(false);
                layer.close(addDialog);
            }
        });
    }

    module.OpenConfirmIsPrePay = function (addDialog) {
        var html = '<div class="checkMigrateDailog" style="width:100%;padding:0">';
        html += '<div class="checkMigrate-main">';
        html += '<div class="checkMigrate-main-text"  style="padding-top:5px;max-height:unset">';
        html +=
            '<span style="font-size:20px;font-weight:700;text-align:center;margin:10px 0 20px 0;color:#000;">请先确认以下规则</span>';
        html +=
            '<span style="font-size:14px;color:#666">1：开启先结款后发货选项时，客户需将订单下单支付到您的1688店铺，客户的代发订单才会自动推送到您的账户内。</span>';
        html +=
            '<span style="font-size:14px;color:#666">2：1688店铺接收到的代发订单<i class="sColor">无需您再次打印发货</i>，在您打印完商家的代发订单后，1688店铺采购单会自动发货。</span>';
        html += '</div>';
        html += '</div>'
        html += '</div>'
        var dailog = layer.open({
            type: 1,
            title: false, //不显示标题
            content: html,
            area: '600px', //宽高
            skin: 'adialog-Shops-skin',
            btn: ["确认并开启"],
            btnAlign: 'c',
            yes: function () {
                module.PostAddBindAgent(addDialog);
                layer.close(dailog);
            }
        });
    }

    module.UpdateRemark = function (id) {
        if (id == undefined || id <= 0)
            return;
        var crruData = dataList[id];
        var updateRemarkDialog = layer.open({
            type: 1,
            title: "修改备注", //不显示标题
            skin: 'wu-dailog',
            content: $('#adialog_UpdateRemark'),
            area: ['500px'], //宽高
            btn: ['确定'],
            success: function () {
                $("#agent_remark_up").val(crruData.Remark);
            },
            yes: function () {
                var oldremark = $("#agent_remark_up").val();
                oldremark = commonModule.DelSpecialChar(oldremark);

                commmon.Ajax({
                    url: "/Partner/EditAgentRemark",
                    type: "POST",
                    data: { id: id, remark: $.trim(oldremark) },
                    loading: true,
                    success: function (rsp) {
                        if (commonModule.IsError(rsp)) {
                            return;
                        }
                        layer.close(updateRemarkDialog);
                        module.LoadList(false);
                    }
                });
            },
            //cancel: function () {
            //    layer.close(updateRemarkDialog);
            //}
        });
    }

    module.Check = function () {
        var agent_key = $("#agent_key");
        $(".IsNumderNull,.IsShowName").hide();
        agent_key.attr("user_id", "0");
        var key = agent_key.val();
        if (key == "" || key == undefined) {
            return;
        }
        commmon.Ajax({
            url: "/Partner/GetByNamebeltStatusV1",
            type: "POST",
            data: { key: key },
            async: true,
            success: function (rsp) {
                if (rsp.Success == false) {
                    $(".IsShowName").hide();
                    $(".IsNumderNull").html(rsp.Message).show();
                    return;
                }
                var data = rsp.Data;
                if (data.Id <= 0 || data == undefined) {
                    $(".IsShowName").hide();
                    //不存在厂家
                    $(".IsNumderNull").html("商家不存在").show();
                } else {
                    //用户状态（1：正常，2：禁用，3：删除）
                    switch (data.Status) {
                        case 1:
                            $(".IsNumderNull").hide();
                            $(".IsShowName").html("账户名：" + (data.NickName || data.Mobile)).show();
                            agent_key.attr("user_id", data.Id);
                            break;
                        case 4:
                            $(".IsShowName").hide();
                            $(".IsNumderNull").html("商家已经被绑定").show();
                            break;
                        case 5:
                            $(".IsShowName").hide();
                            $(".IsNumderNull").html("不能绑定自己").show();
                            break;
                        case 6:
                            $(".IsShowName").hide();
                            if (data.Status2 != 4) {
                                $(".IsNumderNull").html("已经绑定").show();
                            } else {
                                agent_key.attr("user_id", data.Id);
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        });
    }

    module.DelCheck = function () {
        var agent_key = $("#agent_key");
        $(".IsNumderNull,.IsShowName").hide();
    }


    //解除绑定-前置检查
    module.ClickCancel = function (id, flag) {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.UnbindAgent);
        });

        var thisFunc = function () {
            var checkResult = commonModule.CheckVirtualRegMobile();
            if (!checkResult) {
                return;
            }
            //前置检查，若存在已付款待发货的预付款订单，弹窗
            commonModule.Ajax({
                type: "POST",
                url: "/Partner/Check1688UnBindAgent",
                data: { Id: id },
                async: false,
                success: function (rsp) {
                    if (rsp.Success) {
                        layer.closeAll();

                        module.ClickCancelStep2(id, flag);
                    }
                    else {
                        layer.open({
                            type: 1,
                            skin: 'wu-dailog',
                            title: "解除合作提示", //不显示标题
                            content: "<div style='padding:20px;display:flex;flex-direction:column;align-items:center;font-size:14px;'><span style='margin-bottom:10px'>您的账户当前存在待处理订单，为保证订单发货正常，</span><span style='margin-bottom:10px'>请处理完并发货订单后，再进行解绑操作。</span><a class='dColor' href=\"" + commonModule.rewriteTopUrl('/Common/Page/NewOrder-AllOrder') + "\" >查看异常明细》</a></div>",
                            area: '500px', //宽高
                        });
                    }
                }
            });
        }
        
    }

    //解除绑定-Step2
    module.ClickCancelStep2 = function (id, flag) {
        var text = "确定要解除绑定吗?";
        if (flag != undefined && flag == "retry") {
            text = "确定要重试解除绑定吗?";
        }
        layer.confirm('<div class="wu-f16 wu-c09" style="margin:20px 0;width:350px;">' + text + '</div>', {
            title: '取消合作',
            skin: 'wu-dailog',
            btn: ['确定', '取消'] //按钮
        }, function () {
            commmon.Ajax({
                url: '/Partner/UpdateSupplierBindStatus',
                loading: true,
                data: { id: id, status: 3, type: 1,from:"myAgent" },
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        //layer.closeAll();
                        return;
                    }

                    layer.closeAll();
                    //module.LoadList(false);
                    $('.show_status_' + id).html('<i class="dot" style="background-color:#faad14;"></i>解绑处理中');
                    $('.show_status_' + id).on("click", function () { module.replaceSupplierDailog(); });

                    $('.show_status2_' + id).html('解绑处理中');
                    $('.show_status2_' + id).removeAttr("onclick");
                    $('.show_status2_' + id).on("click", function () { module.replaceSupplierDailog(); });

                    module.replaceSupplierDailog();
                },
                error: function (error) {
                    if (error.status == 401) {
                        layer.msg("暂无权限，请联系管理员");
                    } else {
                        console.error(error);
                    }

                }
            });
        }, function () {

        });
    }

    //同意
    module.AgreeToApply = function (id) {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.AgreeAndRefuseAgent);
        });

        var thisFunc = function () {
            var checkResult = commonModule.CheckVirtualRegMobile();
            if (!checkResult) {
                return;
            }
            var cuurDig = layer.confirm('<div class="wu-f16 wu-c09" style="margin-top:10px;">是否同意审核!</div>', {
                title: '同意',
                skin: 'wu-dailog',
                btn: ['确定', '取消'] //按钮
            }, function () {
                commmon.Ajax({
                    url: '/Partner/UpdateSupplierBindStatus',
                    loading: true,
                    data: { id: id, status: 1, type: 1, from: "myAgent" },
                    type: 'POST',
                    success: function (rsp) {
                        if (commonModule.IsError(rsp)) {
                            //layer.closeAll();
                            return;
                        }
                        layer.msg('已成功建立合作关系', { time: 1000, icon: 1 });
                        module.LoadList(false);
                        layer.close(cuurDig);
                    },
                    error: function (error) {
                        if (error.status == 401) {
                            layer.msg("暂无权限，请联系管理员");
                        } else {
                            console.error(error);
                        }

                    }
                });
            }, function () {

            });
        }
        
    }
    //拒绝
    module.RefusalToApply = function (id) {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.AgreeAndRefuseAgent);
        });

        var thisFunc = function () {
            layer.confirm('<div class="wu-f16 wu-c09" style="margin-top:10px;">是否拒绝与该商家合作?</div>', {
                title: '拒绝',
                skin: 'wu-dailog',
                btn: ['确定', '取消'] //按钮
            }, function () {
                commmon.Ajax({
                    url: '/Partner/UpdateSupplierBindStatus',
                    loading: true,
                    data: { id: id, status: 3, type: 4, from: "myAgent" },
                    type: 'POST',
                    success: function (rsp) {
                        if (commonModule.IsError(rsp)) {
                            layer.closeAll();
                            return;
                        }

                        //定时获取任务状态
                        if (getBindSupplierStatus_INTERVAL == null) {
                            getBindSupplierStatus_INTERVAL = setInterval(function () { getBindSupplierStatus(); }, 3000);
                        }

                        layer.closeAll();
                        module.LoadList(false);
                    },
                    error: function (error) {
                        if (error.status == 401) {
                            layer.msg("暂无权限，请联系管理员");
                        } else {
                            console.error(error);
                        }

                    }
                });
            }, function () {

            });
        }
        
    }

    //重新绑定
    module.Repeat = function (id) {
        layer.confirm('<div class="wu-f16 wu-c09" style="margin-top:10px;">确定要重新绑定吗?</div>', {
            title: '重新绑定',
            skin: 'wu-dailog',
            btn: ['确定', '取消'] //按钮
        }, function () {
            commmon.Ajax({
                url: '/Partner/UpdateSupplierBindStatus',
                loading: true,
                data: { id: id, status: 2, type: 1, from: "myAgent"},
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        //layer.closeAll();
                        return;
                    }
                    layer.closeAll();
                    module.LoadList(false);
                },
                error: function (error) {
                    if (error.status == 401) {
                        layer.msg("暂无权限，请联系管理员");
                    } else {
                        console.error(error);
                    }

                }
            });
        }, function () {

        });
    }

    //取消绑定
    module.Cancellation = function (id) {
        layer.confirm('<div class="wu-f16 wu-c09" style="margin:20px 0;width:350px;">确认取消合作申请吗？</div>', {
            title: '取消绑定',
            skin: 'wu-dailog',
            btn: ['确定', '取消'] //按钮
        }, function () {
            commmon.Ajax({
                url: '/Partner/UpdateSupplierBindStatus',
                loading: true,
                data: { id: id, status: 4, type: 1 , from: "myAgent"},
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        layer.closeAll();
                        return;
                    }
                    layer.closeAll();
                    module.LoadList(false);
                },
                error: function (error) {
                    if (error.status == 401) {
                        layer.msg("暂无权限，请联系管理员");
                    } else {
                        console.error(error);
                    }

                }
            });
        }, function () {

        });
    }

    //置顶
    module.SetTop = function (id) {
        layer.confirm('<div class="wu-f16 wu-c09">确认置顶后，会在其他页面优先展示该账户信息</div>', {
            icon:3,
            title: '置顶',
            skin: 'wu-dailog',
            btn: ['确定', '取消'] //按钮
        }, function () {
            commmon.Ajax({
                url: '/Partner/SetTop',
                loading: true,
                data: { id: id },
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        //layer.closeAll();
                        return;
                    }
                    layer.closeAll();
                    module.LoadList(false);
                }
            });
        }, function () {

        });
    }

    //取消置顶
    module.CancelTop = function (id) {
        layer.confirm('<div class="wu-f16 wu-c09">确定要取消置顶吗?</div>', {
            icon: 3,
            title: '取消置顶',
            skin: 'wu-dailog',
            btn: ['确定', '取消'] //按钮
        }, function () {
            commmon.Ajax({
                url: '/Partner/CancelTop',
                loading: true,
                data: { id: id },
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        //layer.closeAll();
                        return;
                    }
                    layer.closeAll();
                    module.LoadList(false);
                }
            });
        }, function () {

        });
    }

    //解绑厂家 Dailog
    module.replaceSupplierDailog = function (result) {

        //定时获取任务状态
        if (getBindSupplierStatus_INTERVAL == null) {
            getBindSupplierStatus_INTERVAL = setInterval(function () { getBindSupplierStatus(); }, 2000);
        }

        layer.open({
            type: 1,
            title: "解绑合作关系公告", //不显示标题
            skin: 'wu-dailog',
            content: $('.replaceSupplierDailog'),
            area: ['565px'], //宽高
            btn: ['切换后台更换'],
            success: function () {
                updateProgressInfo(result);
            },

            yes: function () {
                layer.closeAll();

            },

        });
    }


    //开启/关闭预付
    module.SetPrePay = function (fxUserId, isPrePay, deliveryMode) {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.SetPrePay);
        });

        var thisFunc = function () {
            commonModule.CurFxUserId = fxUserId;
            if (isPrePay == 1) {

                //var html = '<div class="checkMigrateDailog" style="width:600px;padding-bottom: 0;">';
                //html += '<div class="checkMigrate-main">';
                //html += '<div class="checkMigrate-main-text"  style="padding-top:5px;max-height:unset">';
                //html += '<span style="font-size:20px;font-weight:700;text-align:center;margin:10px 0 20px 0;color:#000;">请先确认以下规则</span>';
                //html += '<span style="font-size:14px;color:#666">1:开启先结款后发货选项时，客户需将订单下单支付到您的1688店铺，客户的代发订单才会自动推送到您的账户内。</span>';
                //html += '<span style="font-size:14px;color:#666">2:1688店铺接收到的代发订单<i class="sColor">无需您再次打印发货</i>，在您打印完商家的代发订单后，1688店铺采购单会自动发货。</span>';
                //html += '</div>';

                //html += '</div>'
                //html += '</div>'
                //layer.open({
                //    type: 1,
                //    title: false, //不显示标题
                //    content: html,
                //    area: '600px', //宽高
                //    skin: 'adialog-Shops-skin',
                //    btn: ['已与分销商确认并开启', '暂不开启'],
                //    yes: function () {
                //        module.SaveSetPrePay(fxUserId, isPrePay);
                //    }
                //});



                var tplt = $.templates("#advancePaymentSetDailog_data")
                var html = tplt.render();
                layer.open({
                    type: 1,
                    title: false, //不显示标题
                    skin: 'wu-dailog',
                    content: html,
                    area: '900', //宽高
                    btn: ['已与分销商确认并开启', '暂不开启'],
                    yes: function () {
                        module.SaveSetPrePay(fxUserId, isPrePay);
                    },
                    cancel: function () { }
                });

            }
            else {

                //var html = '<div class="checkMigrateDailog" style="width:600px;padding-bottom: 0;">';
                //html += '<div class="checkMigrate-main">';
                //html += '<div class="checkMigrate-main-text"  style="padding-top:5px;max-height:unset">';
                //html += '<span style="font-size:20px;font-weight:700;text-align:center;margin:10px 0 20px 0;color:#000;">请先确认以下规则</span>';
                //html += '<span style="font-size:14px;color:#666;text-align: center;">关闭预付后，该商家的代发订单，不需要再下单支付到您的1688店铺。</span>';
                //html += '<span style="font-size:14px;color:#666;text-align: center;">走线下自动代发推送，您需要发货以后再进行线下结款</span>';
                //html += '</div>';

                //html += '</div>'
                //html += '</div>'
                //layer.open({
                //    type: 1,
                //    title: false, //不显示标题
                //    content: html,
                //    area: '600px', //宽高
                //    skin: 'adialog-Shops-skin',
                //    btn: ['确定', '取消'],
                //    yes: function () {
                //        module.SaveSetPrePay(fxUserId, isPrePay);
                //    }
                //});

                //设置当前发货方式



                var tplt = $.templates("#advancePaymentSetDailog_setRender")
                var html = tplt.render();
                layer.open({
                    type: 1,
                    skin: 'wu-dailog custom-set-prepay',
                    title: false, //不显示标题
                    content: html,
                    success: function () {

                        if (deliveryMode == undefined || deliveryMode == "" || deliveryMode == "null") {
                            deliveryMode = "0";
                        }

                        $('input[name="DeliveryMode"][value="' + deliveryMode + '"]').prop("checked", true);

                    },
                    area: '850', //宽高
                    btn: ['关闭预付设置', '保存修改发货设置', '取消'],
                    yes: function () {  //关闭预付设置
                        var html = '<div class="checkMigrateDailog" style="width:100%;padding:0;">';
                        html += '<div class="checkMigrate-main">';
                        html += '<div class="checkMigrate-main-text"  style="padding-top:5px;max-height:unset">';
                        html += '<span style="font-size:20px;font-weight:700;text-align:center;margin:10px 0 20px 0;color:#000;">请先确认以下规则</span>';
                        html += '<span style="font-size:14px;color:#666;text-align: center;">关闭预付后，该商家的代发订单，不需要再下单支付到您的1688店铺。</span>';
                        html += '<span style="font-size:14px;color:#666;text-align: center;">走线下自动代发推送，您需要发货以后再进行线下结款</span>';
                        html += '</div>';
                        html += '</div>'
                        html += '</div>'
                        layer.open({
                            type: 1,
                            title: false, //不显示标题
                            content: html,
                            area: '600px', //宽高
                            skin: 'wu-dailog',
                            btn: ['确定', '取消'],
                            yes: function () {

                                module.SaveSetPrePay(fxUserId, isPrePay, function () {
                                    layer.closeAll();
                                    var html = '<div style="padding:40px 30px 35px 30px;font-size:14px;display:flex;line-height:25px;align-items: center">';
                                    html += '<i class="layui-layer-ico layui-layer-ico1" style="width: 30px;height: 30px;display: inline-block;margin-right:25px;"></i>';
                                    html += '<div style="display:flex;flex-direction:column"><span style="font-size: 16px;margin-bottom: 10px;">关闭成功，代发订单已自动推送到您的订单列表</span><span onclick=\'myAgentModule.tarUrl("' + fxUserId + '")\' class="dColor hover">前往打单发货》</span></div>';
                                    html += '</div>';
                                    layer.open({
                                        type: 1,
                                        title: false, //不显示标题
                                        content: html,
                                        area: '550px', //宽高
                                        skin: 'wu-dailog',
                                        btn: false,
                                    });

                                });
                            }
                        });


                    },
                    btn2: function () {
                        var curDeliveryMode = $("input[name='DeliveryMode']:checked").val();
                        if (curDeliveryMode == undefined || curDeliveryMode == null) {
                            layer.msg('请选择发货方式', { icon: 2 });
                            return;
                        }
                        if (deliveryMode == curDeliveryMode) {
                            layer.msg('<span style="padding:15px 30px 15px 15px;">您未选中新的发货方式，请重新选择</span>', { icon: 7, closeBtn: 1, time: 3000, skin: 'adialog-Shops-skin', });

                            return false;
                        }

                        //保存修改发货设置
                        var html = '<div style="padding:30px 30px 25px 30px;font-size:14px;display:flex;line-height:25px;align-items: center">';
                        html += '<i class="layui-layer-ico layui-layer-ico7" style="width: 30px;height: 30px;display: inline-block;margin-right:15px;"></i>';
                        html += '<div class="sColor" style="display:flex;flex-direction:column"><span>修改发货方式后，系统将会重新对你不需要打印的订单进行标记，</span><span>方便您区分订单类型，请确认是否修改当前发货方式</span></div>';
                        html += '</div>';
                        layer.open({
                            type: 1,
                            title: "修改发货方式提醒", //不显示标题
                            content: html,
                            area: '550', //宽高
                            btn: ['确定修改', '取消'],
                            skin: 'wu-dailog',
                            yes: function () {

                                var deliveryM = $("input[name='DeliveryMode']:checked").val();
                                if (deliveryM == "") {
                                    layer.msg('请选择发货方式', { icon: 2 });
                                    return;
                                }
                                module.SaveSetDeliveryMode(fxUserId, deliveryM);
                            },
                            cancel: function () { }
                        });

                        return false;
                    },
                    cancel: function () { }
                });





            }
        }
        
    }

    //确认/拒绝预付
    module.confirmPrePay = function (fxUserId, mobile) {
        function distributionCooperationRenderHtml() {
            html = '';
            html += '<div class="distributionCooperationNotice" >';
            html += '<div class="distributionCooperationNotice-up">';
            html += '<img src="/Content/images/svg/aliIntroduce-icons-2024-1-24-04-11.png" alt="" style="width:635px;height:107px;">';
            html += '</div>';
            html += '<div class="distributionCooperationNotice-main" style="padding-right: 15px;">';
            html += '<div class="distributionCooperationNotice-main-title">您的下游分销商:</div>';
            html += '<ul class="distributionCooperationNotice-main-ul">';
            html += '<li>' + mobile +'</li>';
            html += '</ul>';
            html += '</div>';
            html += '<div class="distributionCooperationNotice-down">';
            html += '<div class="distributionCooperationNotice-down-footer" style="display: flex;flex-direction: column;align-items: center;position:relative">';
            //html += '<div style="position: absolute;top:-30px;font-size: 25px;font-weight: 400;color: #39592b;left:128px;">订单回流店铺 , 提升店铺销量权重</div>'
            html += '<div style="margin-top: 20px;">';
            html += '<span class="layui-btn layui-btn-normal layui-btn35" style="background-color: #39592b;width: 148px;margin-right: 25px;" onclick=\'myAgentModule.cancelDistributionCooperation("' + fxUserId +'")\'>暂不接受</span>';
            html += '<span class="layui-btn layui-btn-normal layui-btn35" style="background-color: #f59a23;" onclick=\'myAgentModule.sureDistributionCooperation("' + fxUserId +'")\'>同意线上付款收单</span>';
            html += '</div>';
            html += '</div>';
            
            html += '<div class="distributionCooperationNotice-down-warn">';
            html += '<span style="margin-bottom: 10px;">温馨提示:</span>';
            html += '<span class="tColor">线上收单需关联下游商品到您的1688店铺，对方才能正常付款下单到您的店铺请快速操作设置，避免下游客户货款无法及时支付</span>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            return html;
        }

        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    var lay = layer.open({
                        type: 1,
                        title: false, //不显示标题
                        content: distributionCooperationRenderHtml(),
                        area: '635px', //宽高
                        skin: 'wu-dailog',
                        /* closeBtn: false,*/
                        btn: false,
                    });
                }
                else return;
            }, p.SetPrePay);
        });
    }

    module.cancelDistributionCooperation = function (fxUserId) {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.SetPrePay);
        });
        var thisFunc = function () {
            var confirmPrePayDailog = layer.open({
                type: 1,
                title: false,
                content: "<div style='padding:25px;font-size:16px'>线下货款转账有风险，财务出账很麻烦，建议使用平台担保交易</div>",
                area: '600px',
                skin: 'wu-dailog',
                btn: ['确认拒绝', '同意线上支付'],
                btn1: function (index, layero) {
                    commmon.Ajax({
                        url: '/Partner/ConfirmPrePay',
                        loading: true,
                        data: { "fxUserId": [fxUserId], "isPrePay": 0 },
                        type: 'POST',
                        success: function (rsp) {
                            if (rsp.Success) {
                                layer.close(confirmPrePayDailog);
                                var html01 = '';
                                html01 += '<div class="distributionCooperationResult">';
                                html01 += '<div class="distributionCooperationResult-up">';
                                html01 += '<span class="distributionCooperationResult-up-icon"></span>';
                                html01 += '<span class="distributionCooperationResult-up-title">您已拒绝</span>';
                                html01 += '</div>';
                                html01 += '<div class="distributionCooperationResult-down" > ';
                                html01 += '<span class="sColor" > 温馨提示: 线下代发有风险，建议使用1688平台担保交易</span > ';
                                html01 += '</div>';
                                html01 += '</div>';
                                layer.open({
                                    type: 1,
                                    title: false, //不显示标题
                                    content: html01,
                                    area: '400px', //宽高
                                    skin: 'wu-dailog',
                                    btn: false,
                                    cancel: function () {
                                        layer.closeAll();
                                    }
                                });
                                //layer.open({
                                //    type: 1,
                                //    title:'',
                                //    content: '您已拒绝，温馨提示线下代发有风险，建议使用1688平台交易',
                                //    area: '500px',
                                //    btn: [],
                                //    yes: function () {
                                //        layer.msg("设置成功");
                                //        layer.closeAll();
                                //    },
                                //    cancel: function () { }
                                //});
                                module.LoadList(false);
                            }
                            else {
                                if (rsp.ErrorCode != undefined && rsp.ErrorCode != "") {
                                    showTipDailog(rsp.ErrorCode);
                                }
                            }
                        }
                    });
                },
                btn2: function (index, layero) {

                    commmon.Ajax({
                        url: '/Partner/ConfirmPrePay',
                        loading: true,
                        data: { "fxUserId": [fxUserId], "isPrePay": 1 },
                        type: 'POST',
                        success: function (rsp) {
                            layer.closeAll();
                            if (rsp.Success) {
                                layer.open({
                                    type: 1,
                                    title: '同意推单付款申请',
                                    content: '<div style="padding:15px 25px;font-size:16px;line-height:25px;"><div>您已开启担保交易线上收款，</div><div>该分销商货款会支付到你的1688店铺，</div>请前往代发品列表进行分销确认</div>',
                                    area: '500px',
                                    btn: ['查看操作视频教程', '设置单人单品价'],
                                    skin: 'wu-dailog',
                                    yes: function () {
                                        window.open('https://www.yuque.com/xiangying-len/zhy7ft/giqllry0n16c5wzg?singleDoc#', '_blank');
                                    },
                                    btn2: function () {
                                        //设置单人单品价
                                        window.open(commmon.rewriteUrl('/SupplySet1688/DistributionProductSearch'), '_blank');
                                    },
                                    cancel: function () {
                                        layer.closeAll();
                                        module.LoadList(false);
                                    }
                                });
                            }
                            else {
                                if (rsp.ErrorCode != undefined && rsp.ErrorCode != "") {
                                    showTipDailog(rsp.ErrorCode);
                                }
                            }
                        }
                    });
                }
            });
        }
        
    }

    module.sureDistributionCooperation = function (fxUserId) {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.SetPrePay);
        });
        var thisFunc = function () {
            commmon.ajax({
                url: '/Partner/ConfirmPrePay',
                loading: true,
                data: { "fxUserId": [fxUserId], "isPrePay": 1 },
                type: 'POST',
                success: function (rsp) {
                    layer.closeAll();
                    if (rsp.Success) {
                        layer.open({
                            type: 1,
                            title: '同意推单付款申请',
                            content: '<div style="padding:15px 25px;font-size:16px;line-height:25px;"><div>您已开启担保交易线上收款，</div><div>该分销商货款会支付到你的1688店铺，</div>请前往代发品列表进行分销确认</div>',
                            area: '500px',
                            btn: ['查看操作视频教程', '设置单人单品价'],
                            skin: 'wu-dailog',
                            yes: function () {
                                layer.closeAll();
                                //查看操作视频教程
                                window.open('https://www.yuque.com/xiangying-len/zhy7ft/giqllry0n16c5wzg?singleDoc#', '_blank');
                                window.location.href = window.location.href;
                            },
                            btn2: function () {
                                //设置单人单品价
                                //设置单人单品价
                                window.open(common.rewriteUrl('/DistributionProduct/ListBySupplier'), '_blank');
                            }
                        });
                    }
                    else {
                        if (rsp.ErrorCode != undefined && rsp.ErrorCode != "") {
                            layer.message(rsp.ErrorCode)
                        }
                    }
                }
            });
        }
        
    }

    module.SaveSetPrePay = function (fxUserId, isPrePay,callBack) {

        commmon.Ajax({
            url: '/Partner/SetPrePay',
            loading: true,
            data: { "fxUserId": fxUserId, "isPrePay": isPrePay },
            type: 'POST',
            success: function (rsp) {
                layer.closeAll();
                if (rsp.Success) {
                    layer.msg("设置成功");
                    layer.closeAll();
                    module.LoadList(false);
                    if (typeof callBack == "function") {
                        callBack();
                    }
                }
                else {
                    if (rsp.ErrorCode != undefined && rsp.ErrorCode != "") {
                        showTipDailog(rsp.ErrorCode);
                    }
                }
            }
        });
    }

    //保存发货方式
    module.SaveSetDeliveryMode = function (fxUserId, deliveryMode, callBack) {

        commmon.Ajax({
            url: '/Partner/SetDeliveryMode',
            loading: true,
            data: { "fxUserId": fxUserId, "deliveryMode": deliveryMode },
            type: 'POST',
            success: function (rsp) {
                layer.closeAll();
                if (rsp.Success) {
                    layer.msg('修改成功！', { icon: 1 });
                    module.LoadList(false);
                    if (typeof callBack == "function") {
                        callBack();
                    }
                }
                else {
                    if (rsp.ErrorCode != undefined && rsp.ErrorCode != "") {
                        showTipDailog(rsp.ErrorCode);
                    }
                }
            }
        });
    }

    //预付状态变化日志
    module.ShowRecords = function () {

        fxUserId = commonModule.CurFxUserId;
        commmon.Ajax({
            url: '/Partner/GetPrepayStatusChangeRecord',
            loading: true,
            data: { "fxUserId": fxUserId },
            type: 'POST',
            success: function (rsp) {
                layer.closeAll();
                if (rsp.Success) {
                    var records = rsp.Data;
                    var tplt = $.templates("#operateLogo_data");
                    var html = tplt.render({
                        records: records
                    });
                    layer.open({
                        type: 1,
                        title: "预付设置日志记录", //不显示标题
                        content: html,
                        skin: 'wu-dailog',
                        area: '500px', //宽高
                        btn: false,
                    });
                }
                else {
                    layer.msg(rsp.Message);
                }
            }
        });

       
    }

    //跳转到指定云平台，指定dbName
    module.tarUrlV2 = function (agentId, platformType, dbName) {
        if (platformType == undefined)
            platformType = "";
        if (dbName == undefined || dbName == null)
            dbName = "";
        platformType = platformType.toLowerCase();
        $("#dbname_input").val(dbName);
        var pt = "Alibaba";
        if (platformType == "toutiao") {
            pt = "TouTiao";
            dbName = "";
        }
        else if (platformType == "jingdong") {
            pt = "Jingdong";
            dbName = "";
        }
        else if (platformType == "pinduoduo") {
            pt = "Pinduoduo";
        }

        //保存对应的分区
        commonModule.SaveCommonSetting("/ErpWeb/DefaultPlatformDbArea", dbName, function (rsp) {
            if (!rsp.Success) {
                console.log(rsp);
            }
        });

        //设置默认云平台，再跳转
        commonModule.SaveCommonSetting("/ErpWeb/DefaultPlatform", pt, function (rsp) {

            var url = "Common/Page/NewOrder-WaitOrder?agentId=" + agentId + "&fromUrl=MyAgentModule&isTar=all&dbname=" + dbName;
            var newUrl = commonModule.rewriteUrlToMainDomainNotDbName(url);
            window.open(newUrl);
        });



    }

    module.GetCooperateStatusByAgent = function (agentId) {

        commonModule.Ajax({
            url: '/Partner/GetCooperateStatusByAgent',
            data: { agentId: agentId },
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success) {
                    var records = rsp.Data;
                    var tplt = $.templates("#bindOperateLog_data");
                    var html = tplt.render({
                        records: records
                    });
                    layer.open({
                        type: 1,
                        title: "绑定设置日志记录", //不显示标题
                        content: html,
                        skin: 'wu-dailog',
                        area: '700px', //宽高
                        btn: false,
                    });
                }
                else {
                    layer.msg(rsp.Message);
                }
            }
        });

    }

    //提示窗口
    var showTipDailog = function (errCode) {

        if (errCode == "QING_NOT_OPEN") {

            //未开通轻应用

            var html = '<div class="checkMigrateDailog" style="width:100%;padding:0;">';
            html += '<div class="checkMigrate-main">';
            html += '<div class="checkMigrate-main-text"  style="padding-top:5px;max-height:unset">';
            html += '<span class="sColor">您的1688店铺暂未开启分销权限，未设置分销商品，</span>';
            html += '<span>目前您的店铺商品对其他下游分销商是不可见状态，</span>';
            html += '<span>请前往店铺后台设置，分销商品设置成功后，即可设置下单商品结算价。</span>';
            html += '</div>'
            html += '</div>'
            html +='<div class="mylayui-layer-btn" style="text-align: center;"><a class="mylayui-layer-btn1 wu-btn wu-btn-mid wu-primary wu-two wu-icon-left" target="_blank" href="https://www.yuque.com/qe2gu6/orbh6n/zf5lis_ghqup4?singleDoc#"><i class="iconfont wu-btn-icon icon-wentijieda"></i>查看教程</a><a class="mylayui-layer-btn0 wu-btn wu-btn-mid wu-mL8" target="_blank" href="https://login.taobao.com/?redirect_url=https%3A%2F%2Flogin.1688.com%2Fmember%2Fjump.htm%3Ftarget%3Dhttps%253A%252F%252Flogin.1688.com%252Fmember%252FmarketSigninJump.htm%253FDone%253Dhttps%25253A%25252F%25252Fwork.1688.com%25252Fhome%25252Fpage%25252Findex.htm%25253Fspm%25253Da262jn.10526145.wkasidenav.8.4ec417681Dpxb5%252526_path_%25253DsellerBaseNew%25252F2017sellerbase_fenxiao%25252FpinpaishangIndex&style=tao_custom&from=1688web">前往1688后台设置</a></div>'
            html += '</div>'

            layer.open({
                type: 1,
                title: false, //不显示标题
                content: html,
                skin: 'wu-dailog',
                area: '600px', //宽高
                btn: false,
            });
        }
        else if (errCode == "NOT_SET_1688SHOP") {

            //未设置指定店铺

            var html = '<div class="checkMigrateDailog" style="width:100%;padding:0;">';
            html += '<div class="checkMigrate-main">';
            html += '<div class="checkMigrate-main-text"  style="padding-top:5px;max-height:unset">';
            html += '<span style="font-size:16px;color:#666;text-align: center;">请您先设置收款的店铺</span>';
            html += '</div>';

            html += '</div>'
            html += '</div>'

            layer.open({
                type: 1,
                title: false, //不显示标题
                content: html,
                area: '600px', //宽高
                skin: 'wu-dailog',
                btn: ['前往设置'],
                yes: function (index, layero) {

                    var url = commonModule.rewriteUrl("/SupplySet1688/SupplierSetBy1688");
                    window.open(url);

                }
            });
        }
        else if (errCode == "NOT_HAS_DPRODUCT") {

            //无代销商品

            var html = '<div class="checkMigrateDailog" style="width:100%;padding:0;">';
            html += '<div class="checkMigrate-main">';
            html += '<div class="checkMigrate-main-text"  style="padding-top:5px;max-height:unset">';
            html += '<span style="font-size:14px;color:#666;text-align: center;">目前您的店铺商品对其他下游分销商是不可见状态请前往店铺后台设置，</span>';
            html += '<span style="font-size:14px;color:#666;text-align: center;">分销商品设置成功后，即可设置下单商品结算价</span>';
            html += '</div>';
            html += '</div>';
            html += '<div class="mylayui-layer-btn" style="text-align: center;"><a class="mylayui-layer-btn1 wu-btn wu-btn-mid wu-primary wu-two wu-icon-left" target="_blank" href="https://www.yuque.com/qe2gu6/orbh6n/zf5lis_ghqup4?singleDoc#"><i class="iconfont wu-btn-icon icon-wentijieda"></i>查看教程</a><a class="wu-btn wu-btn-mid wu-mL8" target="_blank" href="https://login.taobao.com/?redirect_url=https%3A%2F%2Flogin.1688.com%2Fmember%2Fjump.htm%3Ftarget%3Dhttps%253A%252F%252Flogin.1688.com%252Fmember%252FmarketSigninJump.htm%253FDone%253Dhttps%25253A%25252F%25252Fwork.1688.com%25252Fhome%25252Fpage%25252Findex.htm%25253Fspm%25253Da262jn.10526145.wkasidenav.8.4ec417681Dpxb5%252526_path_%25253DsellerBaseNew%25252F2017sellerbase_fenxiao%25252FpinpaishangIndex&style=tao_custom&from=1688web">前往1688后台设置</a></div>'
            html += '</div>';

            layer.open({
                type: 1,
                title: false, //不显示标题
                content: html,
                area: '600px', //宽高
                skin: 'wu-dailog',
                btn: false,
            });
        }
        
    }

    //获取异步任务状态
    var getBindSupplierStatus = function () {
        commonModule.Ajax({
            type: "POST",
            url: "/Partner/GetReBindProductOrderStatus",
            success: function (rsp) {
                if (commonModule.IsError(rsp)) {
                    clearInterval(getBindSupplierStatus_INTERVAL);
                    getBindSupplierStatus_INTERVAL = null;
                    return;
                }
                var data = rsp.Data;

                if (rsp.Message.indexOf('执行中') > 0) {
                    updateProgressInfo(data);
                    setBatchButton(false);
                }
                else {
                    setBatchButton(true);

                    clearInterval(getBindSupplierStatus_INTERVAL);
                    getBindSupplierStatus_INTERVAL = null;
                    layer.closeAll();
                    setTimeout(function () { module.Search(); }, 2000);
                    
                }
            }
        });
    }

    //更新进度条百分比
    var updateProgressInfo = function (result) {
        var totalCount = 0;
        var successCount = 0;
        var per = 1;

        if (result != null) {
            totalCount = result.TotalCount;
            successCount = result.SuccessCount;
        }

        if (totalCount > 0) {
            per = ((parseInt(successCount) * 100) / parseInt(totalCount)).toFixed(0);
        }

        if (per > 100) { per = 100; }

        $('#loader-span-per').width(per + '%');
        $('#loader-per').text(per + '%');

    }

    //设置提示文字，flag=true为空，flag=false设置提示文字
    var setBatchButton = function (flag) {

        if (flag) {
            $('#loader-span-per').width('100%');
            $('#loader-per').text('100%');
            setTimeout(function () {
                $('#progress-tip').html("");
                layer.closeAll();
            }, 500);
        }
        else {

            $('#progress-tip').html("当前有解绑商家任务进行，请等待任务结束后操作");
        }
    }

    module.invitesupplier = function () {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.AgentInvite);
        });

        var thisFunc = function (event) {
            var checkResult = commonModule.CheckVirtualRegMobile();
            if (!checkResult) {
                return;
            }
            if (event) {
                event.stopPropagation();
            } else {
                window.event.returnValue = false;
            };

            $("#invitesupplier_tab li:eq(0)").addClass("layui-this");
            $("#invitesupplier_tab li:eq(1)").removeClass("layui-this");
            $("#invitesupplier_wrap .invitesupplier-content:eq(1)").hide();
            $("#invitesupplier_wrap .invitesupplier-content:eq(0)").show();
            $("#invitesupplier_wrap").show(200);



            commmon.Ajax({
                url: '/Partner/LoadMyAgentQrCode',
                loading: true,
                data: {},
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        layer.closeAll();
                        return;
                    }

                    $("#qrCodeExceedTimeId").html(rsp.Data.ExceedTime);
                    module.makeCode(rsp.Data.QrCode)
                }
            });

        }
    }

    module.closeInvitesupplier = function () {
        $("#invitesupplier_wrap").hide(200);

    }

    $$.navActive("#invitesupplier_tab", function (index, item) {
        $("#invitesupplier_wrap .invitesupplier-content").hide();
        $("#invitesupplier_wrap .invitesupplier-content:eq(" + index + ")").show();

    }, "layui-this")


    module.makeCode = function (elText) {
        $("#agentQrcodeId").html("");
       
        var qrcode = new QRCode(document.getElementById("agentQrcodeId"), {
            width: 200,
            height: 200
        });
        
        qrcode.makeCode(elText);
    }

    $(document).on("click", function () {
        $("#invitesupplier_wrap").hide();
    })

    module.showDeliveryModeLog = function () {

        commonModule.Ajax({
            url: '/SupplySet1688/GetDeliveryModeChangeRecord',
            loading: true,
            data: { "fxUserId": commonModule.CurFxUserId },
            type: 'POST',
            success: function (rsp) {
                layer.closeAll();
                if (rsp.Success) {
                    var records = rsp.Data;
                    var tplt = $.templates("#table_body_deliveryModelChangeData");
                    var html = tplt.render({
                        records: records
                    });
                    layer.open({
                        type: 1,
                        title: "日志记录", //不显示标题
                        content: html,
                        skin: 'wu-dailog',
                        area: '900px', //宽高
                        btn: false,
                    });
                }
                else {
                    layer.msg(rsp.Message);
                }
            }
        });
    }

    module.tarUrl = function (fxUserId) {
        var url = "/Common/Page/NewOrder-WaitOrder?agentId=" + fxUserId +"&isTar=setPrePay";
        var newUrl = commmon.rewriteUrl(url);//token
        newUrl = commmon.dbnameToAjaxUrl(newUrl);//dbname
        window.open(newUrl);

    }

    //下载二维码
    module.onloadQRC = function () {
        var base64 = $("#agentQrcodeId>img").attr("src");

        if (window.navigator.msSaveOrOpenBlob) {
            var bstr = atob(base64.split(',')[1]);
            var n = bstr.length;
            var u8arr = new Uint8Array(n);
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }
            var blob = new Blob([u8arr]);
            window.navigator.msSaveOrOpenBlob(blob, 'chart-download' + '.' + 'png');
        } else {
            // 这里就按照chrome等新版浏览器来处理
            const a = document.createElement('a');
            a.href = base64;
            a.setAttribute('download', 'MyAgent-QRC');
            a.click();
        }
    }







    return module;
}(myAgentModule || {}, commonModule, jQuery, layer));