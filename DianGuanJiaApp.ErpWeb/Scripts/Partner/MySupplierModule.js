var mySupplierModule = (function (module, commmon, $, layer) {

    var getBindSupplierStatus_INTERVAL = null;
    // 列表数据
    var dataList = {};
    var reqModel = {
        Id: 0,
        Key: "",
        Status: 0,
        PageIndex: 1,
        PageSize: 50,
        SupplierFxUserId: 0,
        SenderName: null,
        SenderMobile: null,
        Province: null,
        City: null,
        District: null,
        Address: null,
        RemarkName: null,
        SenderTelePhone: null
    };
    var IsWhiteUserFlag = true;

    $(function () {
        wuFormModule.initblurInput('#supplier_account_input');
        wuFormModule.initLayuiSelect('active-select-filter');
        getIsWhiteUserData();
        //外部过来的参数
        if (outStatus != undefined && outStatus != "") {
            $("#sel_status").val(outStatus);
        }
        //reqModel.HideCancelUser = HideCancelSupplier;
        module.LoadList(false);
        layui.form.render();
        // layui.form.render("select");
        //加载地址级联选择
        commmon.LoadAreaInfoToControl('supplierProvince-select', 1, function () {
        }, selectCallBack, "name");

        //更改厂家数据源绑定
        //bindSupplierModule.InitSuppliers(Suppliers);
        //// 是否显示已取消厂家
        //$(".hideCancelSupplier").change(function () {
        //    var val = $(this).prop("checked") ? "1" : "0";
        //    commmon.SaveCommonSetting("/ErpWeb/MySupplier/HideCancelSupplier", val, function (rsp) {
        //        if (rsp.Success) {
        //            reqModel.HideCancelUser = val;
        //            module.LoadList();
        //        }
        //    });
        //});


        /* 不显示非合作关系 操作逻辑*/
        var FxUserSettings = commmon.FxUserSettings;
        FxUserSettings.forEach(function (item, i) {
            if (item.Key.indexOf("IsShowCancelUser") != -1) {
                $("input[name='IsShowCancelUser']").attr("checked", item.Value == "true" ? true : false);
                layui.form.render('checkbox');
            }
        })
        layui.form.on('switch(IsShowCancelUser)', function (data) {
            commonModule.Ajax({
                url: "/System/SaveCommonSetting",
                type: "POST",
                data: { settingKey: "/FenFa/System/Config/IsShowCancelUser", settingValue: this.checked },
                success: function (rsp) {
                    module.LoadList();
                    if (rsp.Success == false) {
                        return;
                    }
                }
            });
        });

    });
    module.SupplierType = "";
    module.LoadList = function (isPaging) {
        reqModel.Key = $.trim($("#iup_key").val());
        reqModel.Status = $("#sel_status").val();
        //1.ajax
        commonModule.Ajax({
            url: '/Partner/LoadMySupplierList',
            data: { key: reqModel.Key, status: reqModel.Status, pageIndex: reqModel.PageIndex, pageSize: reqModel.PageSize, hideCancelUser: reqModel.HideCancelUser },
            async: true,
            loading: true,
            type: 'POST',
            success: function (rsp) {
                if (commonModule.IsError(rsp)) {
                    return;
                }
                var data = rsp.Data.List || [];
                //2.渲染
                var tplt = $.templates("#shareSupplierList_data_tr");
                commonModule.Foreach(data, function (i, obj) {
                    dataList[obj.Id] = obj;
                });
                var html = tplt.render({ supplierData: data });
                $("#ShareSupplierList_body").html(html);

                commonModule.HideNoPermDiv(commonModule.MySupplierShowPermDict);
                //3.分页
                if (isPaging == true) {
                    return;
                }

                layui.laypage.render({
                    elem: 'paging',
                    theme: ' wu-page',
                    count: rsp.Data.Total,
                    limit: reqModel.PageSize,
                    curr: reqModel.PageIndex,
                    limits: [50, 100, 150, 200, 300, 500],
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function (obj, first) {
                        //$(".layui-laypage-count").html()
                        if (!first) {
                            reqModel.PageSize = obj.limit;
                            reqModel.PageIndex = obj.curr;
                            module.LoadList(true);
                        }
                    }
                });
            }
        });
    }

    // 是否白名单用户
    function getIsWhiteUserData() {
        commonModule.ajax({
            type: 'GET',
            url: '/api/Common/GetIsWhiteUser',
            success: function (res) {
                if (res.Success) {
                    IsWhiteUserFlag = res.Data;
                }
            }
        });
    }

    // 查看厂家名片
    module.CheckQualification = function (id, status) {
        if (status === '2' || status === '3' || status === '4') {
            commonModule.w_alert({ type: 3, content: '当前绑定状态不能查看！' });
            return;
        }
        // sessionStorage.removeItem("PartnerCardData_" + id);
        commmon.Ajax({
            url: "/Partner/GetSupplierCardById",
            type: "GET",
            data: { id: id },
            success: function (rsp) {
                // sessionStorage.setItem("PartnerCardData_" + id, JSON.stringify(rsp.Data)); 
                if (IsWhiteUserFlag) {
                    window.open(commmon.rewriteUrl("/GeneralizeIndex/MyStationCard?id=" + id + "&userType=supplier"), '_self');
                } else {
                    window.open(commmon.rewriteUrl("/Partner/CheckQualification?id=" + id + "&userType=supplier"), '_self');
                }
            },
            error: function (error) {
                if (error.status == 401) {
                    commonModule.w_alert({ type: 3, content: '暂无权限，请联系管理员' });
                }
            }
        });
    }

    module.Eidt = function (id) {
        if (id == undefined || id <= 0)
            return;

        //2021-10-27,项颖说隐藏。和二维码扫描绑定保持一致。
        //添加入口是隐藏，编辑入口需要显示
        $("#addSupplier_address").show();
        $("#addSupplier_address_select").show();

        // $("#adialog_key").nextAll().remove();
        $("#adialog_key").closest('.wu-inputWrap').removeClass("wu-warn").find(".wu-warn-title").hide().text('');
        $("#addSupplier_PrePayTip").hide();
        var crruData = dataList[id];
        //虚拟厂家 改变弹窗结构
        $("#virtual_li").remove();
        $("#virtual_li02").remove();
        if (crruData.SupplierType == "Virtual") {
            var Lihtml = "", Lihtml02 = "";
            Lihtml += '<li id="virtual_li"><div style="color:#04385d;font-size:14px;"><span>新增虚拟厂家</span></div>';
            Lihtml02 += '<li id="virtual_li02">订单代发地址（选填）：用于导出订单表格给厂家的发件人信息地址</li>'
            Lihtml += '<div style="color:#f7941f;font-size:12px;padding-left:10px;">上游厂家没有使用店管家订单分发系统，需要把订单拆分、密文信息导出给到厂家。</div>';
            Lihtml += '</li>';
            $("#adialog_addSupplier_content").prepend(Lihtml);
            $("#spanName").html('虚拟厂家名称<i style="color:#ff511c">*</i>');
            $("#addSupplier_address").before(Lihtml02);
            $("#addSupplier_address_title").html("");
            $("#adialog_remarkName").parent().parent().hide();
        } else {
            $("#virtual_li").remove();
            $("#virtual_li02").remove();
            $("#spanName").html('厂家账号：');
            $("#addSupplier_address_title").html("代发订单发货地址：");
            $("#adialog_remarkName").parent().parent().show();
        }
        if (crruData.IsPrePay) {
            $("#addSupplier_PrePayTip").show();
        }
        $("#adialog_key").css({ "border-width": "0" });
        var addDialog = layer.open({
            type: 1,
            title: "编辑",
            content: $('#adialog_addSupplier'),
            area: '600px', //宽高
            btn: ['保存', '取消'],
            skin: 'wu-dailog',
            success: function () {
                $("#adialog_addSupplier").attr("data-id", crruData.Id);
                $("#adialog_key").attr("user_id", crruData.SupplierFxUserId).attr("disabled", true).val(crruData.NickName || crruData.Mobile).closest('.wu-inputWrap').addClass("disabled");
                $("#adialog_name").val(crruData.SenderName);
                $("#adialog_mobile").val(crruData.SenderMobile);
                $("#adialog_telephone").val(crruData.SenderTelePhone);
                //加载地址级联选择
                var isExistsVal = $("#supplierProvince-select").find("option[value='" + crruData.Province + "']").length;
                if (isExistsVal > 0 && crruData.Province) {
                    $("#supplierCity-select").attr("data-value", crruData.City).val(crruData.City);
                    $("#supplierArea-select").attr("data-value", crruData.District).val(crruData.District);
                    $("#supplierProvince-select").attr("data-value", crruData.Province).val(crruData.Province).change();
                }
                $('#supplier-textarea').val(crruData.Address);
                $('#adialog_remarkName').val(crruData.RemarkName);
                if (commmon.FxUserAddres.Id < 1 || commmon.FxUserAddres == null) {
                    $("#isdefault").parent().hide();
                } else {
                    if ($.trim(crruData.SenderName) == commmon.FxUserAddres.SenderName
                        && $.trim(crruData.SenderMobile) == commmon.FxUserAddres.SenderMobile
                        && $.trim(crruData.Province) == commmon.FxUserAddres.Province
                        && $.trim(crruData.City) == commmon.FxUserAddres.City
                        && $.trim(crruData.District) == commmon.FxUserAddres.County
                        && $.trim(crruData.Address) == commmon.FxUserAddres.Address
                    ) {
                        $("#isdefault").prop("checked", true);
                    } else {
                        $("#isdefault").prop("checked", false);
                    }
                    $("#isdefault").parent().show();
                }
            },
            yes: function () {
                if ($(".layui-layer-btn0").attr("disabled")) {
                    return;
                }
                var data = module.GetData(2, crruData);
                if (!data) { return false; }
                $(".layui-layer-btn0").attr("disabled", true);
                reqModel.Status = crruData.Status;
                commmon.Ajax({
                    url: "/Partner/EditBindAddress",
                    type: "POST",
                    data: { _model: reqModel },
                    loading: true,
                    success: function (rsp) {
                        $(".layui-layer-btn0").removeAttr("disabled");
                        if (commonModule.IsError(rsp)) {
                            return;
                        }
                        layer.close(addDialog);
                        module.LoadList(false);
                    }
                });
            },
            cancel: function () {
                layer.close(addDialog);
            }
        });
    }
    module.newEidt = function (id) {
        if (id == undefined || id <= 0)
            return;

        //$("#adialog_key").nextAll().remove();
        $("#adialog_key").closest('.wu-inputWrap').removeClass("wu-warn").find(".wu-warn-title").hide().text('');
        $("#addSupplier_PrePayTip").hide();
        var crruData = dataList[id];

        if (crruData.IsPrePay) {
            $("#addSupplier_PrePayTip").show();
        }
        var tplt = $.templates("#dialog_newAddSupplier");
        var html = tplt.render({
            crruData: crruData,
            isSetFactoryAddress: true
        });
        $('#adialog_newAddSupplier').html(html)
        var EnableAddress = crruData.EnableAddress;
        var addDialog = layer.open({
            type: 1,
            title: "编辑",
            content: $('#adialog_newAddSupplier'),
            area: '500', //宽高
            maxHeight: '500px',
            btn: ['保存', '取消'],
            skin: 'wu-dailog',
            success: function () {
                commmon.LoadAreaInfoToControl('newSupplierProvince-select', 1, function () {
                }, selectCallBack, "name");
                $('#adialog_newAddSupplier').show();
                $('#addSupplier_edit_switch').off()
                $('#addSupplier_edit_switch').on('click', function () {
                    if ($(this).hasClass('active')) {
                        EnableAddress = false
                        $(this).removeClass('active');
                        $('#addSupplier_edit_cont').hide();
                    } else {
                        $(this).addClass('active');
                        EnableAddress = true;
                        $('#addSupplier_edit_cont').show();
                    }
                });
                //$('#addSupplier_edit_switch').trigger('click');
                if (EnableAddress) {
                    $('#addSupplier_edit_switch').addClass('active');
                    $('#addSupplier_edit_cont').show();
                } else {
                    $('#addSupplier_edit_switch').removeClass('active');
                    $('#addSupplier_edit_cont').hide();
                }

                //加载地址级联选择
                var isExistsVal = $("#supplierProvince-select").find("option[value='" + crruData.Province + "']").length;
                if (isExistsVal > 0 && crruData.Province) {
                    $("#newSupplierCity-select").attr("data-value", crruData.City).val(crruData.City);
                    $("#newSupplierArea-select").attr("data-value", crruData.District).val(crruData.District);
                    $("#newSupplierProvince-select").attr("data-value", crruData.Province).val(crruData.Province).change();
                }
                $('#supplier_textarea').val(crruData.Address)
                if (commmon.FxUserAddres.Id < 1 || commmon.FxUserAddres == null) {
                    $("#isdefault").parent().hide();
                } else {
                    if ($.trim(crruData.SenderName) == commmon.FxUserAddres.SenderName
                        && $.trim(crruData.SenderMobile) == commmon.FxUserAddres.SenderMobile
                        && $.trim(crruData.Province) == commmon.FxUserAddres.Province
                        && $.trim(crruData.City) == commmon.FxUserAddres.City
                        && $.trim(crruData.District) == commmon.FxUserAddres.County
                        && $.trim(crruData.Address) == commmon.FxUserAddres.Address
                    ) {
                        $("#isdefault").prop("checked", true);
                    } else {
                        $("#isdefault").prop("checked", false);
                    }
                    $("#isdefault").parent().show();
                }
            },
            btn1: function () {
                if ($(".layui-layer-btn0").attr("disabled")) {
                    return;
                }

                if (EnableAddress) {
                    var data = module.newGetData(2, crruData);
                    if (!data) { return false; }
                } else {
                    reqModel.Id = crruData.Id;
                    reqModel.SenderName = crruData.SenderName;
                    reqModel.SenderMobile = crruData.SenderMobile;
                    reqModel.SenderTelePhone = crruData.SenderTelePhone;
                    reqModel.Province = crruData.Province;
                    reqModel.City = crruData.City;
                    reqModel.District = crruData.District;
                    reqModel.Address = crruData.Address;
                    reqModel.RemarkName = crruData.RemarkName;
                }
                $(".layui-layer-btn0").attr("disabled", true);
                reqModel.Id = crruData.Id;
                reqModel.Status = crruData.Status;
                reqModel.EnableAddress = EnableAddress;
                commmon.Ajax({
                    url: "/Partner/EditBindAddress",
                    type: "POST",
                    data: { _model: reqModel },
                    loading: true,
                    success: function (rsp) {
                        onClearReqModel();
                        $(".layui-layer-btn0").removeAttr("disabled");
                        if (commonModule.IsError(rsp)) {
                            return;
                        }
                        layer.close(addDialog);
                        module.LoadList(false);
                    }
                });
            },
            btn2: function () {
                layer.close(addDialog);
                onClearStyle();
            },
            end: function () {
                onClearStyle()
                onClearReqModel();
            }
        });
    }
    //新设置默认地址
    module.setDefaultAddress = function () {
        //加载之前设置好的默认代发地址
        var defaultAddress = {}
        commmon.Ajax({
            url: '/api/System/GetServeAddress',
            type: "get",
            loading: true,
            success: function (rsp) {
                if (!rsp.Success) {
                    commonModule.w_alert({ type: 2, content: rsp.Message })
                } else {
                    if(rsp.Data != null) {
                        // 如果不是第一次设置默认地址   id和接口查询到的id一致
                        defaultAddress = rsp.Data;
                        $('#adialog_newAddSupplier input[name="adialog_name"]').val(defaultAddress.SenderName);
                        $('#adialog_newAddSupplier input[name="adialog_mobile"]').val(defaultAddress.SenderMobile);
                        $('#adialog_newAddSupplier input[name="adialog_telephone"]').val(defaultAddress.SenderTelePhone);
                        $('#supplier_textarea').val(defaultAddress.Address);
                        $("#newSupplierCity-select").attr("data-value", defaultAddress.City).val(defaultAddress.City);
                        $("#newSupplierArea-select").attr("data-value", defaultAddress.County).val(defaultAddress.County);
                        $("#newSupplierProvince-select").attr("data-value", defaultAddress.Province).val(defaultAddress.Province).change();
                    } else {
                        // 如果是第一次设置默认地址   id要设置为0
                        defaultAddress.Id = 0;
                        $('#adialog_newAddSupplier input[name="adialog_name"]').val('');
                        $('#adialog_newAddSupplier input[name="adialog_mobile"]').val('');
                        $('#adialog_newAddSupplier input[name="adialog_telephone"]').val('');
                        $('#supplier_textarea').val('');
                        $("#newSupplierCity-select").attr("data-value", 0).val(0);
                        $("#newSupplierArea-select").attr("data-value", 0).val(0);
                        $("#newSupplierProvince-select").attr("data-value", 0).val(0).change();
                    }

                    
                }
            }
        })
        var tplt = $.templates("#dialog_newAddSupplier");
        var html = tplt.render({
            crruData: '',
            isSetFactoryAddress: false
        });
        $('#adialog_newAddSupplier').html(html)
        var addDialog = layer.open({
            type: 1,
            title: "默认代发地址",
            content: $('#adialog_newAddSupplier'),
            area: '500', //宽高
            maxHeight: '500px',
            btn: ['保存', '取消'],
            skin: 'wu-dailog',
            success: function () {
                $("#setDefaultAddressRemark").hide();
                //加载地址级联选择
                commmon.LoadAreaInfoToControl('newSupplierProvince-select', 1, function () {
                }, selectCallBack, "name");
            },
            btn1: function () {
                if ($(".layui-layer-btn0").attr("disabled")) {
                    return;
                }
                var data = module.newGetData(2);
                if (!data) { return false; };
                var postAddress = {
                    //默认地址id要用查询默认地址返回来的地址id
                    AddressId: defaultAddress.Id,
                    SenderName: reqModel.SenderName,
                    SenderMobile: reqModel.SenderMobile,
                    Province: reqModel.Province,
                    City: reqModel.City,
                    District: reqModel.District,
                    Address: reqModel.Address,
                    SenderTelePhone: reqModel.SenderTelePhone
                }
                commmon.Ajax({
                    url: "/api/System/SetServeAddress",
                    type: "POST",
                    data: JSON.stringify(postAddress),
                    loading: true,
                    contentType: 'application/json',
                    success: function (rsp) {
                        if (!rsp.Success) {
                            commonModule.w_alert({ type: 2, content: rsp.Message })
                        } else {
                            commonModule.w_alert({ type: 4, content: '保存成功' })
                            layer.close(addDialog);
                        }
                    }
                })

            },
            btn2: function () {
                layer.close(addDialog);
            },
        })
    }
    module.Apply = function (SupplierFxUserId) {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.ApplyPrepay);
        });

        var thisFunc = function () {
            if (SupplierFxUserId == undefined || SupplierFxUserId <= 0)
                return;
            var applyDialog = layer.open({
                type: 1,
                title: false,
                content: $(".transactionApplicationWrap"),
                area: '635px', //宽高
                skin: 'wu-dailog transactionApplicationDailog-skin adialog-Shops-skin',
                btn: ['取消担保交易申请', '查看如何线上付款', '发起线上付款交易申请'],
                btn1: function () {
                    var tipDialog = layer.open({
                        type: 1,
                        title: '取消担保交易申请', //不显示标题
                        content: $(".cancleTransactionApplicationDailog"),
                        area: '400px', //宽高
                        closeBtn: false,
                        skin: 'wu-dailog cancleTransactionApplicationSkin',
                        btn: ['坚持取消', '暂不取消'],
                        btn1: function (index, layero, that) {
                            commmon.Ajax({
                                url: "/Partner/ApplyPrepay",
                                type: "POST",
                                data: { "supplierFxUserId": SupplierFxUserId, "applyPrepayStatus": 4 },
                                loading: true,
                                success: function (rsp) {
                                    if (commonModule.IsError(rsp))
                                        return;
                                    layer.close(applyDialog);
                                    layer.close(tipDialog);
                                    module.LoadList(false);
                                }
                            });
                        },
                        btn2: function (index, layero, that) {
                            commmon.Ajax({
                                url: "/Partner/ApplyPrepay",
                                type: "POST",
                                data: { "supplierFxUserId": SupplierFxUserId, "applyPrepayStatus": 2 },
                                loading: true,
                                success: function (rsp) {
                                    if (commonModule.IsError(rsp))
                                        return;
                                    layer.close(applyDialog);
                                    layer.close(tipDialog);
                                    module.LoadList(false);
                                }
                            });
                        }
                    });
                },
                btn2: function () {
                    //layer.msg("暂时没有视频")
                    //alert("查看如何线上付款")
                    window.open('https://www.yuque.com/xiangying-len/zhy7ft/qgr8wgakl2sassmf?singleDoc#', '_blank');
                    return false;

                },
                btn3: function () {
                    commmon.Ajax({
                        url: "/Partner/ApplyPrepay",
                        type: "POST",
                        data: { "supplierFxUserId": SupplierFxUserId, "applyPrepayStatus": 2 },
                        loading: true,
                        success: function (rsp) {
                            if (commonModule.IsError(rsp))
                                return;
                            // 三秒弹窗消失TODO
                            layer.msg('已对厂家发起担保交易申请,待厂家同意', {
                                icon: 1, time: 3000, success: function () {
                                    layer.close(applyDialog);
                                    module.LoadList(false);
                                }
                            })
                        }
                    });
                }
            });
        }

    }


    module.Check = function () {
        var adialog_key = $("#adialog_key");
        // adialog_key.nextAll().remove();
        adialog_key.closest('.wu-inputWrap').removeClass("wu-warn").find(".wu-warn-title").hide().text('');
        adialog_key.attr("user_id", "0");
        var key = adialog_key.val();
        if (key == "" || key == undefined) {
            return;
        }
        if (module.SupplierType == "Virtual")
            return;
        commmon.Ajax({
            url: "/Partner/GetByNamebeltStatus",
            type: "POST",
            data: { key: key },
            async: true,
            success: function (rsp) {
                if (rsp.Success == false) {
                    adialog_key.closest('.wu-inputWrap').addClass("wu-warn").find(".wu-warn-title").show().text(rsp.Message);
                    //adialog_key.parent().append('<i class="layui-icon layui-icon-close-fill" style="font-size: 15px; color: red;"><span style="font-size: 10px;">' + rsp.Message + '</span></i>');
                    return;
                }
                var data = rsp.Data;
                if (data.Id <= 0 || data == undefined) {
                    //不存在厂家
                    // adialog_key.parent().append('<i class="layui-icon layui-icon-close-fill" style="font-size: 15px; color: red;"><span style="font-size: 10px;">厂家不存在</span></i>');
                    adialog_key.closest('.wu-inputWrap').addClass("wu-warn").find(".wu-warn-title").show().text('厂家不存在');
                } else {
                    var tmp = "";
                    if (data.NickName != null && key == data.Mobile)
                        tmp = data.NickName;
                    if (data.Mobile != null && key == data.NickName)
                        tmp = data.Mobile;
                    //用户状态（1：正常，2：禁用，3：删除）
                    switch (data.Status) {
                        case 0:
                        case 1:
                        case 2:
                            //adialog_key.parent().append('<i class="layui-icon layui-icon-ok-circle" style="font-size: 15px;"><span style="font-size: 10px;">' + tmp + '</span></i>');
                            adialog_key.closest('.wu-inputWrap').addClass("wu-warn").find(".wu-warn-title").show().text(tmp);
                            adialog_key.attr("user_id", data.Id);
                            break;
                        case 4:
                            // adialog_key.parent().append('<i class="layui-icon layui-icon-close-fill" style="font-size: 15px; color: red;"><span style="font-size: 10px;">厂家已经被绑定</span></i>');
                            adialog_key.closest('.wu-inputWrap').addClass("wu-warn").find(".wu-warn-title").show().text('厂家已经被绑定');
                            break;
                        case 5:
                            // adialog_key.parent().append('<i class="layui-icon layui-icon-close-fill" style="font-size: 15px; color: red;"><span style="font-size: 10px;">不能绑定自己</span></i>');
                            adialog_key.closest('.wu-inputWrap').addClass("wu-warn").find(".wu-warn-title").show().text('不能绑定自己');
                            break;
                        case 6:
                            //未取消的用户有绑定限制
                            if (data.Status2 != 4) {
                                // adialog_key.parent().append('<i class="layui-icon layui-icon-close-fill" style="font-size: 15px; color: red;"><span style="font-size: 10px;">已经绑定</span></i>');
                                adialog_key.closest('.wu-inputWrap').addClass("wu-warn").find(".wu-warn-title").show().text('已经绑定');
                            } else {
                                adialog_key.attr("user_id", data.Id);
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        });
    }

    module.Remark = function (id) {
        var html = "";
        html += '<div class="wu-inputWrap wu-form-mid">';
        html += '<input class="wu-input" type="text" id="' + id + 'remark" placeholder="请输入备注(选填)" style="width: 300px;" />';
        html += '<span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>';
        html += '</div>';
        var updateRemarkDialog = layer.open({
            type: 1,
            title: '修改备注',
            content: html,
            area: '400px',
            skin: 'wu-dailog',
            success: function () {
                $("#" + id + "remark").val(dataList[id].RemarkName);
            },
            btn: ['确定', '取消'],
            btn1: function () {
                var crruData = dataList[id];
                reqModel.Status = crruData.Status;
                reqModel.RemarkName = $.trim($("#" + id + "remark").val());
                var oldremark = $("#" + id + "remark").val();
                oldremark = commonModule.DelSpecialChar(oldremark);

                commmon.Ajax({
                    url: "/Partner/EditSupplierRemark",
                    type: "POST",
                    data: { id: id, remark: $.trim(oldremark) },
                    loading: true,
                    success: function (rsp) {
                        if (commonModule.IsError(rsp)) {
                            return;
                        }
                        layer.close(updateRemarkDialog);
                        module.LoadList(false);
                    }
                });
            },
            btn2: function (index, layero, that) {
                layer.close(updateRemarkDialog);
            },
        });
        //var updateRemarkDialog = layer.alert('<div><input type="text" style="width:450px;font-size: 12px;height: 24px;padding-left:10px;" id="' + id + 'remark" placeholder="请输入备注(选填)" /></div>',
        //    {
        //        title: "修改备注",
        //        area: ['500px'],
        //        success: function (layero, index) {
        //            $("#" + id + "remark").val(dataList[id].RemarkName);
        //        },
        //        yes: function () {
        //            var crruData = dataList[id];
        //            reqModel.Status = crruData.Status;
        //            reqModel.RemarkName = $.trim($("#" + id + "remark").val());
        //            var oldremark = $("#" + id + "remark").val();
        //            oldremark = commonModule.DelSpecialChar(oldremark);

        //            commmon.Ajax({
        //                url: "/Partner/EditSupplierRemark",
        //                type: "POST",
        //                data: { id: id, remark: $.trim(oldremark) },
        //                loading: true,
        //                success: function (rsp) {
        //                    if (commonModule.IsError(rsp)) {
        //                        return;
        //                    }
        //                    layer.close(updateRemarkDialog);
        //                    module.LoadList(false);
        //                }
        //            });
        //        }
        //    })
    }

    module.DelCheck = function () {
        var adialog_key = $("#adialog_key");
        // adialog_key.nextAll().remove();
        adialog_key.closest('.wu-inputWrap').removeClass("wu-warn").find(".wu-warn-title").hide().text('');
    }
    module.Search = function () {
        reqModel.PageIndex = 1;
        module.LoadList(false);
    }

    var addWarnAddWarnDialog = null;
    module.showAddWarn = function () {
        addWarnAddWarnDialog = layer.open({
            type: 1,
            title: false, //不显示标题
            content: '<div style="width:600px;height:400px;position: relative;"><span onclick="mySupplierModule.Add()" style="display: inline-block;position: absolute;bottom: 67px;left: 239px;width: 177px;height: 46px;cursor: pointer;"></span><img src="/Content/images/noviceIntroPic/anquanWarn-2022-10-26-01.png" /></div>',
            area: ['550'], //宽高
            btn: false,
            closeBtn: false
        });
    }

    // 绑定厂家
    module.Add = function (supplierType) {
        commonModule.FxPermission(function (p) {
            var permission = p.AddBindSupplier;
            if (supplierType == "Virtual") {
                permission = p.AddVirtualSupplier;
            }
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else {
                    layer.close(addWarnAddWarnDialog);
                    return;
                };
            }, permission);
        });

        var thisFunc = function () {
            if (addWarnAddWarnDialog) {
                layer.close(addWarnAddWarnDialog);
            }
            if (supplierType !== "Virtual") {
                var checkResult = commonModule.CheckVirtualRegMobile();
                if (!checkResult) {
                    return;
                }
            }
            //虚拟厂家 改变弹窗结构
            $("#virtual_li").remove();
            $("#virtual_li02").remove();
            //预付提示
            $("#addSupplier_PrePayTip").hide();
            if (supplierType == "Virtual") {
                var Lihtml = "", Lihtml02 = "";
                Lihtml += '<li id="virtual_li" class="wu-8radius wu-f12 wu-p12" style="box-sizing: border-box;">';
                Lihtml += '<div class="wu-color-c">当您的厂家没有使用店管家订单分发系统时，你又需要把订单拆分、密文导出给他们，就可以创建虚拟厂家，然后通过商品绑定虚拟帐号的方式快速分类代发订单。</div>';
                Lihtml += '<div class="wu-c09 wu-mT12">注意系统禁止一切平台订单（除手工录入单）明文展示以及导出，保护消费者隐私是全平台需遵守的规则.</div>';
                Lihtml += '</li>';
                //Lihtml02 += '<li id="virtual_li02">店管家分销代发系统禁止一切平台订单（除手工录入单）明文展示以及导出，保护消费者隐私是全平台需遵守的规则.</li>'
                $("#adialog_addSupplier_content").prepend(Lihtml);
                $("#spanName").html('<i class="wu-color-b">*</i>虚拟厂家名称：');
                $("#addSupplier_address").before(Lihtml02);
                $("#addSupplier_address_title").html("");
                $("#adialog_remarkName").parent().parent().hide();
            } else {
                $("#virtual_li").remove();
                $("#virtual_li02").remove();
                $("#spanName").html('厂家账号：');
                $("#addSupplier_address_title").html("代发订单发货地址：");
                $("#adialog_remarkName").parent().parent().show();
            }

            $("#adialog_addSupplier input").val("");
            $("#adialog_key").css({ "border-width": "1px" });
            $("#adialog_addSupplier").attr("data-id", 0);
            $("#supplierProvince-select").val(0);
            $("#supplier-textarea").val("");
            $("#adialog_remarkName").val("");
            $("#isdefault").removeAttr("checked");
            var adialog_key = $("#adialog_key");
            // adialog_key.nextAll().remove();
            adialog_key.closest('.wu-inputWrap').removeClass("wu-warn").find(".wu-warn-title").hide().text('');
            adialog_key.attr("user_id", "0").attr("disabled", false).closest('.wu-inputWrap').removeClass("disabled");
            adialog_key.closest('.wu-inputWrap').removeClass("disabled")
            var apiUrl = "/Partner/AddBindSupplier";
            var isVirtual = supplierType == "Virtual"
            if (isVirtual) {
                apiUrl = "/Partner/AddVirtualSupplier";
                module.SupplierType = "Virtual";
            }
            else {
                module.SupplierType = "";
            }
            var virtualWord = isVirtual ? "虚拟" : "";
            //2021-10-27,项颖说隐藏。和二维码扫描绑定保持一致。只有编辑有，其他场景都没有地址修改
            $("#addSupplier_address").hide();
            $("#addSupplier_address_select").hide();

            //加载地址级联选择
            commmon.LoadAreaInfoToControl('supplierProvince-select', 1, function () {
            }, selectCallBack, "name");
            var addDialog = layer.open({
                type: 1,
                title: "绑定" + virtualWord + "厂家", //不显示标题
                content: $('#adialog_addSupplier'),
                area: "600px", //宽高
                btn: ['保存', '取消'],
                skin: 'wu-dailog',
                success: function () {
                    //$("#adialog_name").val(commmon.FxUserAddres.SenderName);
                    //$("#adialog_mobile").val(commmon.FxUserAddres.SenderMobile);
                    //var isExistsVal = $("#supplierProvince-select").find("option[value='" + commmon.FxUserAddres.Province + "']").length;
                    //if (isExistsVal > 0 && commmon.FxUserAddres.Province) {
                    //    $("#supplierCity-select").attr("data-value", commmon.FxUserAddres.City).val(commmon.FxUserAddres.City);
                    //    $("#supplierArea-select").attr("data-value", commmon.FxUserAddres.County).val(commmon.FxUserAddres.County);
                    //    $("#supplierProvince-select").attr("data-value", commmon.FxUserAddres.Province).val(commmon.FxUserAddres.Province).change();
                    //}
                    //$('#supplier-textarea').val(commmon.FxUserAddres.Address);
                    if (commmon.FxUserAddres.Id > 0 && commmon.FxUserAddres != null) {
                        $("#isdefault").parent().show();
                    } else {
                        $("#isdefault").parent().hide();
                    }
                },
                yes: function () {
                    if ($(".layui-layer-btn0").attr("disabled") == "disabled") {
                        return;
                    }
                    var data = module.GetData(1, { SupplierType: supplierType });
                    if (!data) { return false; }
                    $(".layui-layer-btn0").attr("disabled", true);
                    commmon.Ajax({
                        url: apiUrl,
                        type: "POST",
                        data: { _model: reqModel },
                        loading: true,
                        success: function (rsp) {
                            $(".layui-layer-btn0").removeAttr("disabled");
                            if (commonModule.IsError(rsp)) {
                                return;
                            }
                            if (isVirtual)
                                layer.msg("绑定成功", { icon: 1 });
                            else
                                layer.msg("已发送合作申请，请联系厂家同意合作!", { icon: 1 });
                            module.LoadList(false);
                            layer.close(addDialog);
                        }
                    });
                },
                cancel: function () {
                    layer.close(addDialog);
                }
            });
        }

    }

    //解除绑定-前置检查
    module.ClickCancel = function (id, supplierFxUserId, sId) {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.UnbindSupplier);
        });

        //前置检查，若存在已付款待发货的预付款订单，弹窗
        var thisFunc = function () {
            var checkResult = commonModule.CheckVirtualRegMobile();
            if (!checkResult) {
                return;
            }
            commonModule.Ajax({
                type: "POST",
                url: "/Partner/Check1688UnBindSupplier",
                data: { Id: id },
                async: false,
                success: function (rsp) {
                    if (rsp.Success) {
                        layer.closeAll();

                        module.ClickCancelStep2(id, supplierFxUserId, sId);
                    }
                    else {
                        layer.open({
                            type: 1,
                            title: "解除合作提示", //不显示标题
                            content: "<div style='padding:20px;display:flex;flex-direction:column;align-items:center;font-size:14px;'><span style='margin-bottom:10px'>您的账户当前存在待处理订单，为保证订单发货正常，</span><span style='margin-bottom:10px'>请处理完并发货订单后，再进行解绑操作。</span><a class='dColor' href=\"" + commonModule.rewriteTopUrl('/Common/Page/NewOrder-AllOrder') + "\" >查看异常明细》</a></div>",
                            area: '500px', //宽高
                            skin: 'wu-dailog',
                        });
                    }
                }
            });
        }

    }

    //解除绑定-Step2
    module.ClickCancelStep2 = function (id, supplierFxUserId, sId) {

        //过滤当前解绑的用户，防止循环解绑
        var suppliers = [];
        if (Suppliers != null && Suppliers.length > 0) {
            for (var i = 0; i < Suppliers.length; i++) {
                var s = Suppliers[i];
                if (s.FxUserId != sId) {
                    suppliers.push(s);
                }
            }
        }
        //更改厂家数据源绑定
        bindSupplierModule.InitSuppliers(suppliers);
        commmon.Ajax({
            url: "/Partner/GetUnboundInfo",
            type: "POST",
            data: { supplierId: sId },
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.alert(rsp.Message, { icon: 2, skin: 'wu-dailog' });
                    return;
                }
                if (rsp.Data.IsBind) {
                    layer.confirm('<div class="setCanceDailog wu-f16 wu-c06"><span class="my-layer-ico" style="margin-right:15px;background-position: -90px 0;"></span><div><span>请慎重解绑合作关系,</span>' +
                        '<span class="wu-color-b">合作关系一旦解绑后,</span >' +
                        '<span class="wu-color-b">将清除所有代发订单及商品数据!</span>' +
                        '<span>如有其他需求,请先联系客服</span></div></div>',
                        {
                            title: '解除合作关系确认',
                            area: ['500px'],
                            btn: ['确定解绑', '暂不解绑'],
                            skin: 'wu-dailog'
                        }, function () {
                            layer.closeAll();
                            var resetFunc = function () {
                                bindSupplierModule.ConfigModels = [];
                                bindSupplierModule.SupplierAreaArr = [];
                                //renderChoosedHtml();
                                $("#mutil-supplier-name-select").val("");
                                bindSupplierModule.InitTimeControl();
                                // 隐藏添加更多厂家
                                $("#addMoreSuppliers").hide();
                                $("#supplier-select-box").hide();
                                $("#showMoreSuppliers").hide();
                                $(".bindsupplier-main input[value='supplier']").prop("checked", false);
                            }
                            var adialog = layer.open({
                                type: 1,
                                title: "解除绑定",
                                content: $('#batchMappingSupplier'),
                                area: '560px', //宽高
                                btn: ['确定', '取消'],
                                skin: 'wu-dailog',
                                move: false,
                                offset: '180px',
                                success: function () {
                                    $("input[name='supplier-type-rdo']").on("click", function () {
                                        var data_type = $(this).val();
                                        if (data_type == "self") {
                                            //$("#supplier-select-box").parent().hide();
                                            $("#supplier-select-box").hide();
                                        } else {
                                            //$("#supplier-select-box").parent().show();
                                            $("#supplier-select-box").show();
                                        }
                                    });
                                    $("#supplier-name-select option").show();
                                    $("#supplier-name-select").val("");
                                    $("#supplier-id-" + sId).hide();

                                    // 隐藏当前需解除的厂家
                                    $("#batchMappingSupplier").append('<input id="UnbindSupplierFxUserId" type="hidden" value="' + supplierFxUserId + '"/>');
                                    $("#supplier-name-select option[value='" + supplierFxUserId + "']").hide();
                                    //$(".hasProductBindTip").show();
                                    // 初始化省市区数据
                                    threeAreaInfoModule.Initialize();
                                },
                                yes: function () {
                                    var model = {};
                                    model.from = "supplier";
                                    var supplierType = $("input[name='supplier-type-rdo']:checked").val();
                                    if (supplierType == undefined) {
                                        layer.msg("请选择供货类型");
                                        return;
                                    }
                                    model.isSelf = $("input[name='supplier-type-rdo']:checked").val() == "self";
                                    model.supplierId = $("#supplier-name-select").val();
                                    model.isSaveHistoryData = $("#is-save-history-data:checked").length > 0;
                                    model.unbindSupplierId = id;
                                    model.unbindSupplierFxUserId = supplierFxUserId;
                                    model.Configs = [];
                                    if (model.isSelf == false && model.supplierId <= 0) {
                                        layer.msg("请选择厂家", { icon: 7 });
                                        return;
                                    }

                                    // 获取默认或配置厂家
                                    if (!model.isSelf) {
                                        $(bindSupplierModule.ConfigModels).each(function (i, item) {
                                            var configModel = {};
                                            //configModel.RefType = 1;
                                            //configModel.RefCode = productCode;
                                            //configModel.ProductCode = productCode;
                                            //configModel.IsDefault = item.IsDefault;
                                            configModel.SupplierId = item.SupplierId;
                                            configModel.ConfigType = item.ConfigType;
                                            configModel.Config = item.Config;
                                            model.Configs.push(configModel);
                                        });
                                    }

                                    commmon.Ajax({
                                        url: "/Partner/UnbundlingEvent",
                                        type: "POST",
                                        data: model,
                                        loadingMessage: '解除中...',
                                        showMasker: true,
                                        success: function (rsp) {
                                            if (rsp.Success == false)
                                                layer.alert("解除绑定失败：" + rsp.Message, { icon: 2, skin: 'wu-dailog' });
                                            else {

                                                layer.closeAll();

                                                module.replaceSupplierDailog();

                                                //resetFunc();
                                                //module.Search();
                                            }
                                        }
                                    })
                                },
                                btn2: function () {
                                    resetFunc();
                                    layer.closeAll(adialog);
                                },
                                cancel: function () {
                                    resetFunc();
                                    layer.closeAll(adialog);
                                }
                            });
                        }, function () { });

                }
                else {
                    //没有关联商品直接弹是否解除关联
                    layer.confirm('<div class="setCanceDailog wu-f16 wu-c06"><span class="my-layer-ico" style="margin-right:15px;background-position: -90px 0;"></span><div><span>请慎重解绑合作关系,</span>' +
                        '<span class="wu-color-b">合作关系一旦解绑后,</span >' +
                        '<span class="wu-color-b">将清除所有代发订单及商品数据!</span>' +
                        '<span>如有其他需求,请先联系客服</span></div></div>', {
                        title: '解除合作关系确认',
                        area: ['500px'],
                        btn: ['确定解绑', '暂不解绑'],
                        skin: 'wu-dailog'
                    }, function () {
                        commmon.Ajax({
                            url: '/Partner/UpdateSupplierBindStatus',
                            loading: true,
                            data: { id: id, status: 4, type: 2, from: "mySupplier" },
                            type: 'POST',
                            success: function (rsp) {
                                if (commonModule.IsError(rsp)) {
                                    //layer.closeAll();
                                    //layer.msg("解除成功", { icon: 1 });
                                    //return;
                                }
                                layer.closeAll();
                                //module.LoadList(false);
                                $('.show_status_' + id).html('<i class="dot" style="background-color:#faad14;"></i>解绑处理中');
                                $('.show_status_' + id).on("click", function () { module.replaceSupplierDailog(); });

                                $('.show_status2_' + id).html('解绑处理中');
                                $('.show_status2_' + id).removeAttr("onclick");
                                $('.show_status2_' + id).on("click", function () { module.replaceSupplierDailog(); });

                                module.replaceSupplierDailog();
                            },
                            error: function (error) {
                                if (error.status == 401) {
                                    layer.msg("暂无权限，请联系管理员");
                                } else {
                                    console.error(error);
                                }

                            }
                        });
                    }, function () {

                    });
                }
            }
        })
    }

    //重试解除绑定
    module.ReClickCancel = function (id, supplierFxUserId, sId) {
        layer.confirm('<div>系统在处理解绑过程中，因订单数据过多或网络中断导致解绑失败，已暂时为您拦截该厂家打印发货代发订单<br/>是否重新发起解绑申请？<br/>如有疑问，可联系客服帮助</div>', {
            title: '解绑失败提醒',
            area: '460px',
            skin: 'wu-dailog'
        }, function () {
            module.ClickCancel(id, supplierFxUserId, sId);
        });
    }

    //同意
    module.AgreeToApply = function (id) {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.AgreeAndRefuseSupplier);
        });

        var thisFunc = function () {
            var checkResult = commonModule.CheckVirtualRegMobile();
            if (!checkResult) {
                return;
            }
            if (id == undefined || id <= 0) {
                return;
            }
            
            $("#virtual_li").remove();
            $("#virtual_li02").remove();
            $("#spanName").html('厂家账号：');
            $("#adialog_remarkName").parent().parent().show();
            

           
            //2021-10-27,项颖说隐藏。和二维码扫描绑定保持一致。只有编辑有，其他场景都没有地址修改
            $("#addSupplier_address").hide();
            $("#addSupplier_address_select").hide();
            $("#addSupplier_PrePayTip").hide();
            var crruData = dataList[id];
            var addDialog = layer.open({
                type: 1,
                title: "同意绑定",
                content: $('#adialog_addSupplier'),
                area: '600px', //宽高
                btn: ['保存', '取消'],
                skin: 'wu-dailog',
                success: function () {
                    $("#adialog_remarkName").parent().parent().show();
                    $("#adialog_addSupplier").attr("data-id", crruData.Id)
                    $("#adialog_key").attr("user_id", crruData.SupplierFxUserId).attr("disabled", true).val(crruData.NickName || crruData.Mobile).closest('.wu-inputWrap').addClass("disabled");
                    $("#adialog_key").closest('.wu-inputWrap').find(".wu-warn-title").hide().text('');
                    $("#adialog_name").val(crruData.SenderName);
                    $("#adialog_mobile").val(crruData.SenderMobile);
                    $("#adialog_telephone").val(crruData.SenderTelePhone);
                    var isExistsVal = $("#supplierProvince-select").find("option[value='" + (crruData.Province) + "']").length;
                    if (isExistsVal > 0 && (crruData.Province)) {
                        $("#supplierCity-select").attr("data-value", (crruData.City)).val(crruData.City);
                        $("#supplierArea-select").attr("data-value", (crruData.District)).val(crruData.District);
                        $("#supplierProvince-select").attr("data-value", (crruData.Province)).val(crruData.Province).change();
                    }
                    $('#supplier-textarea').val(crruData.Address);
                    $('#adialog_remarkName').val(crruData.RemarkName);

                    if (commmon.FxUserAddres.Id < 1 || commmon.FxUserAddres == null) {
                        $("#isdefault").parent().hide();
                    } else {
                        $("#isdefault").parent().show();
                        $("#isdefault").removeAttr("checked");
                    }
                    if (crruData.IsPrePay) {
                        $("#addSupplier_PrePayTip").show();
                    }
                },
                yes: function () {
                    if ($(".layui-layer-btn0").attr("disabled") == "disabled") {
                        return;
                    }
                    var data = module.GetData(3);
                    if (!data) { return false; }
                    $(".layui-layer-btn0").attr("disabled", true);
                    reqModel.Status = 1;
                    commmon.Ajax({
                        url: "/Partner/EditBindAddress",
                        type: "POST",
                        data: { _model: reqModel },
                        loading: true,
                        success: function (rsp) {
                            $(".layui-layer-btn0").removeAttr("disabled");
                            if (commonModule.IsError(rsp)) {
                                return;
                            }
                            layer.msg("已成功建立合作关系", { icon: 1 });
                            layer.close(addDialog);
                            module.LoadList(false);
                        }
                    });
                },
                cancel: function () {
                    layer.close(addDialog);
                }
            });
        }

    }
    //拒绝
    module.RefusalToApply = function (id) {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.AgreeAndRefuseSupplier);
        });

        var thisFunc = function () {
            layer.confirm('<div style="font-size:16px;margin:20px 0;width:350px">是否拒绝与该厂家合作?</div>', {
                title: '拒绝',
                btn: ['确定', '取消'],
                skin: 'wu-dailog'
            }, function () {
                commmon.Ajax({
                    url: '/Partner/UpdateSupplierBindStatus',
                    loading: true,
                    data: { id: id, status: 3, type: 0, from: "mySupplier" },
                    type: 'POST',
                    success: function (rsp) {
                        if (commonModule.IsError(rsp)) {
                            layer.closeAll();
                            return;
                        }

                        layer.closeAll();

                        module.replaceSupplierDailog();

                        //module.LoadList(false);

                    },
                    error: function (error) {
                        if (error.status == 401) {
                            layer.msg("暂无权限，请联系管理员");
                        } else {
                            console.error(error);
                        }

                    }
                });
            }, function () {

            });
        }

    }

    module.RejectedToReapply = function (id) {
        if (id == undefined || id <= 0)
            return;

        //2021-10-27,项颖说隐藏。和二维码扫描绑定保持一致。只有编辑有，其他场景都没有地址修改
        $("#addSupplier_address").hide();
        $("#addSupplier_address_select").hide();

        var crruData = dataList[id];
        var addDialog = layer.open({
            type: 1,
            title: "重新申请", //不显示标题
            content: $('#adialog_addSupplier'),
            area: '600px', //宽高
            btn: ['保存', '取消'],
            skin: 'wu-dailog',
            success: function () {
                $("#adialog_addSupplier").attr("data-id", crruData.Id);
                $("#adialog_key").attr("user_id", crruData.SupplierFxUserId).attr("disabled", true).val(crruData.NickName || crruData.Mobile).closest('.wu-inputWrap').addClass("disabled");
                $("#adialog_name").val(crruData.SenderName);
                $("#adialog_mobile").val(crruData.SenderMobile);
                $("#adialog_telephone").val(crruData.SenderTelePhone);
                var isExistsVal = $("#supplierProvince-select").find("option[value='" + (crruData.Province) + "']").length;
                if (isExistsVal > 0 && (crruData.Province)) {
                    $("#supplierCity-select").attr("data-value", (crruData.City)).val(crruData.City);
                    $("#supplierArea-select").attr("data-value", (crruData.District)).val(crruData.District);
                    $("#supplierProvince-select").attr("data-value", (crruData.Province)).val(crruData.Province).change();
                }
                $('#supplier-textarea').val(crruData.Address);
                $('#adialog_remarkName').val(crruData.RemarkName);
                if (commmon.FxUserAddres.Id < 1 || commmon.FxUserAddres == null) {
                    $("#isdefault").parent().hide();
                } else {
                    $("#isdefault").parent().show();
                    $("#isdefault").removeAttr("checked");
                }
            },
            yes: function () {
                if ($(".layui-layer-btn0").attr("disabled") == "disabled") {
                    return;
                }
                var data = module.GetData(3);
                if (!data) { return false; }
                $(".layui-layer-btn0").attr("disabled", true);
                reqModel.Status = 2;
                commmon.Ajax({
                    url: "/Partner/EditBindAddress",
                    type: "POST",
                    data: { _model: reqModel },
                    loading: true,
                    success: function (rsp) {
                        $(".layui-layer-btn0").removeAttr("disabled");
                        if (commonModule.IsError(rsp)) {
                            return;
                        }
                        layer.msg("已成功重新建立合作关系", { icon: 1 });
                        layer.close(addDialog);
                        module.LoadList(false);
                    }
                });
            },
            cancel: function () {
                layer.close(addDialog);
            }
        });
    }

    //重新绑定
    module.Repeat = function (id) {
        layer.confirm('<div class="wu-f16 wu-c06">确定要重新绑定吗？</div>', {
            title: '重新绑定',
            btn: ['确定', '取消'],
            skin: 'wu-dailog'
        }, function () {
            commmon.Ajax({
                url: '/Partner/UpdateSupplierBindStatus',
                loading: true,
                data: { id: id, status: 2, type: 0, from: "mySupplier" },
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        //layer.closeAll();
                        return;
                    }
                    layer.closeAll();
                    module.LoadList(false);
                },
                error: function (error) {
                    if (error.status == 401) {
                        layer.msg("暂无权限，请联系管理员");
                    } else {
                        console.error(error);
                    }

                }
            });
        }, function () {

        });
    }
    //取消绑定
    module.Cancellation = function (id) {
        layer.confirm('<div class="wu-f16 wu-c06">确认取消合作申请吗？</div>', {
            title: '取消绑定',
            btn: ['确定', '取消'],
            skin: 'wu-dailog'
        }, function () {
            commmon.Ajax({
                url: '/Partner/UpdateSupplierBindStatus',
                loading: true,
                data: { id: id, status: 4, type: 0, from: "mySupplier" },
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        //layer.closeAll();
                        return;
                    }
                    layer.closeAll();
                    module.LoadList(false);
                },
                error: function (error) {
                    if (error.status == 401) {
                        layer.msg("暂无权限，请联系管理员");
                    } else {
                        console.error(error);
                    }

                }
            });
        }, function () {

        });
    }

    //置顶
    module.SetTop = function (id) {
        layer.confirm('<div class="wu-f16 wu-c06">确认置顶后，会在其他页面优先展示该账户信息</div>', {
            icon: 3,
            title: '置顶',
            btn: ['确定', '取消'],
            skin: 'wu-dailog'
        }, function () {
            commmon.Ajax({
                url: '/Partner/SetTop',
                loading: true,
                data: { id: id },
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        //layer.closeAll();
                        return;
                    }
                    layer.closeAll();
                    module.LoadList(false);
                }
            });
        }, function () {

        });
    }

    //取消置顶
    module.CancelTop = function (id) {
        layer.confirm('<div style="font-size:16px;">确定要取消置顶吗?</div>', {
            icon: 3,
            title: '取消置顶',
            btn: ['确定', '取消'],
            skin: 'wu-dailog'
        }, function () {
            commmon.Ajax({
                url: '/Partner/CancelTop',
                loading: true,
                data: { id: id },
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        //layer.closeAll();
                        return;
                    }
                    layer.closeAll();
                    module.LoadList(false);
                }
            });
        }, function () {

        });
    }

    module.IsDefaultAddress = function (i) {
        if ($(i).is(":checked")) {
            $("#adialog_name").val(commmon.FxUserAddres.SenderName);
            $("#adialog_mobile").val(commmon.FxUserAddres.SenderMobile);
            $("#adialog_telephone").val(commmon.FxUserAddres.SenderTelePhone);
            var isExistsVal = $("#supplierProvince-select").find("option[value='" + commmon.FxUserAddres.Province + "']").length;
            if (isExistsVal > 0 && commmon.FxUserAddres.Province) {
                $("#supplierCity-select").attr("data-value", commmon.FxUserAddres.City).val(commmon.FxUserAddres.City);
                $("#supplierArea-select").attr("data-value", commmon.FxUserAddres.County).val(commmon.FxUserAddres.County);
                $("#supplierProvince-select").attr("data-value", commmon.FxUserAddres.Province).val(commmon.FxUserAddres.Province).change();
            }
            $('#supplier-textarea').val(commmon.FxUserAddres.Address);

            // 新代发地址弹窗默认地址回显
            var defaultAddress = {}
            commmon.Ajax({
                url: '/api/System/GetServeAddress',
                type: "get",
                loading: true,
                success: function (rsp) {
                    if (!rsp.Success) {
                        commonModule.w_alert({ type: 2, content: rsp.Message })
                    } else {
                        defaultAddress = rsp.Data;
                        $('#adialog_newAddSupplier input[name="adialog_name"]').val(defaultAddress.SenderName);
                        $('#adialog_newAddSupplier input[name="adialog_mobile"]').val(defaultAddress.SenderMobile);
                        $('#adialog_newAddSupplier input[name="adialog_telephone"]').val(defaultAddress.SenderTelePhone);
                        $('#supplier_textarea').val(defaultAddress.Address);
                        $("#newSupplierCity-select").attr("data-value", defaultAddress.City).val(defaultAddress.City);
                        $("#newSupplierArea-select").attr("data-value", defaultAddress.County).val(defaultAddress.County);
                        $("#newSupplierProvince-select").attr("data-value", defaultAddress.Province).val(defaultAddress.Province).change();
                    }
                }
            })

            //设置代发地址入口没有更换时勾选默认地址的回显
            /* $('#adialog_newAddSupplier input[name="adialog_name"]').val(commmon.FxUserAddres.SenderName);
            $('#adialog_newAddSupplier input[name="adialog_mobile"]').val(commmon.FxUserAddres.SenderMobile);
            $('#adialog_newAddSupplier input[name="adialog_telephone"]').val(commmon.FxUserAddres.SenderTelePhone);

            if (isExistsVal > 0 && commmon.FxUserAddres.Province) {
                $("#newSupplierCity-select").attr("data-value", commmon.FxUserAddres.City).val(commmon.FxUserAddres.City);
                $("#newSupplierArea-select").attr("data-value", commmon.FxUserAddres.County).val(commmon.FxUserAddres.County);
                $("#newSupplierProvince-select").attr("data-value", commmon.FxUserAddres.Province).val(commmon.FxUserAddres.Province).change();
            }
            $('#supplier_textarea').val(commmon.FxUserAddres.Address) */

        } else {
            //编辑时使用
            //var id = $("#adialog_addSupplier").attr("data-id");
            //if (id != undefined && id > 0) {
            //    var crruData = dataList[id];
            //    $("#adialog_key").attr("user_id", crruData.SupplierFxUserId).attr("disabled", true).val(crruData.NickName || crruData.Mobile);
            //    $("#adialog_name").val(crruData.SenderName);
            //    $("#adialog_mobile").val(crruData.SenderMobile);
            //    //加载地址级联选择
            //    var isExistsVal = $("#supplierProvince-select").find("option[value='" + crruData.Province + "']").length;
            //    if (isExistsVal > 0 && crruData.Province) {
            //        $("#supplierCity-select").attr("data-value", crruData.City).val(crruData.City);
            //        $("#supplierArea-select").attr("data-value", crruData.District).val(crruData.District);
            //        $("#supplierProvince-select").attr("data-value", crruData.Province).val(crruData.Province).change();
            //    }
            //    $('#supplier-textarea').val(crruData.Address);
            //} else {
            //    $("#adialog_name").val("");
            //    $("#adialog_mobile").val("");
            //    $("#supplierProvince-select").attr("data-value", 0).val(0).change();
            //    $('#supplier-textarea').val("");
            //}
            $("#adialog_name").val("");
            $("#adialog_mobile").val("");
            $("#adialog_telephone").val("");
            $("#supplierProvince-select").attr("data-value", 0).val(0).change();
            $('#supplier-textarea').val("");
        }
    }

    $$.navActive("#invitesupplier_tab", function (index, item) {
        $("#invitesupplier_wrap .invitesupplier-content").hide();

        $("#invitesupplier_wrap .invitesupplier-content:eq(" + index + ")").show();

    }, "layui-this")

    function selectCallBack(control) {
        var deep = control.attr('deep');
        if (deep > 1) {
            var dataValue = control.attr("data-value");
            var isExistsVal = control.find("option[value='" + dataValue + "']").length;
            if (isExistsVal > 0)
                control.val(dataValue).trigger('change');
        }
    };
    function clearGetData() {
        reqModel = {
            Id: 0,
            Key: "",
            Status: 0,
            PageIndex: reqModel.PageIndex,
            PageSize: reqModel.PageSize,
            SupplierFxUserId: 0,
            SenderName: null,
            SenderMobile: null,
            Province: null,
            City: null,
            District: null,
            Address: null,
            RemarkName: null,
            SenderTelePhone: null
        };
    }
    module.GetData = function (type, crruData) {
        clearGetData()
        var mobileReg = /^(86-[1][0-9]{10})|(86[1][0-9]{10})|([1][0-9]{10})$/;  //手机正则
        var phoneReg = /^(0\d{2,3}-?\d{7,8})|((（|\()0\d{2,3}(\)|）)\d{7,8})$/; //电话正则
        reqModel.Status = 0;
        reqModel.Id = 0;
        if (type == 1) {
            //添加验证数据格式
            reqModel.SupplierFxUserId = $("#adialog_key").attr("user_id");
            if (crruData && crruData.SupplierType == "Virtual") {
                reqModel.NickName = $("#adialog_key").val();
                if (!reqModel.NickName) {
                    layer.msg('厂家名称不能为空', { icon: 7 });
                    return false;
                }
            }
            else {
                if ($("#adialog_key").val() == "" || $("#adialog_key").val() == null) {
                    layer.msg('厂家不能为空', { icon: 7 });
                    return false;
                }
                if ((reqModel.SupplierFxUserId <= 0 || reqModel.SupplierFxUserId == undefined)) {
                    // var msgText = $("#adialog_key").next('i').text()
                    var msgText = $("#adialog_key").closest('.wu-inputWrap').find(".wu-warn-title").text();
                    if (msgText) {
                        layer.msg(msgText);
                    }
                    return false;
                }
            }

        } else if (type == 2) {
            //编辑赋值
            reqModel.Id = $("#adialog_addSupplier").attr("data-id");
        } else if (type == 3) {
            //同意厂家请求绑定
            reqModel.Id = $("#adialog_addSupplier").attr("data-id");
            reqModel.Status = 1;
        }
        reqModel.SenderName = $.trim($("#adialog_name").val());
        reqModel.SenderMobile = $.trim($("#adialog_mobile").val());
        reqModel.SenderTelePhone = $.trim($("#adialog_telephone").val());
        reqModel.Province = $("#supplierProvince-select").val();
        reqModel.City = $("#supplierCity-select").val();
        reqModel.District = $("#supplierArea-select").val();
        reqModel.Address = $.trim($("#supplier-textarea").val());
        reqModel.RemarkName = $.trim($("#adialog_remarkName").val());

        //2021-10-27,项颖说隐藏。和二维码扫描绑定保持一致。只有编辑有，其他场景都没有地址修改
        if (type == 1 || type == 3)
            return true;

        if (crruData && crruData.SupplierType == "Virtual" && (type == 1 || type == 2))
            return true;

        if (reqModel.SenderName == "" || reqModel.SenderName == null) {
            layer.msg('请填写代发件人姓名', { icon: 7 });
            return false;
        }
        if (!reqModel.SenderMobile && !reqModel.SenderTelePhone) {
            layer.msg('联系手机、联系固话必填一个', { icon: 7 });
            return false;
        }
        if (reqModel.SenderMobile != "" && !mobileReg.test(reqModel.SenderMobile)) {
            layer.msg('代发手机号格式不对', { icon: 7 });
            return false;
        }

        if (reqModel.SenderTelePhone != "" && !phoneReg.test(reqModel.SenderTelePhone)) {
            layer.msg('代发固话格式不对', { icon: 7 });
            return false;
        }
        if (reqModel.Province == undefined || reqModel.Province == "0") {
            layer.msg('请选择省份', { icon: 7 });
            return false;
        }
        if (reqModel.City == undefined || reqModel.City == "0") {
            layer.msg('请选择城市', { icon: 7 });
            return false;
        }
        if (reqModel.District == undefined || reqModel.District == "0") {
            var lent = $("#supplierArea-select>option").length;
            if (lent > 1) {
                layer.msg('请选择区县', { icon: 7 });
            }
            return false;
        }
        if (reqModel.Address == '') {
            layer.msg('请填写详细地址', { icon: 7 });
            return false;
        }
        return true;
    }
    module.newGetData = function (type, crruData) {
        clearGetData()
        var mobileReg = /^1[3-9]\d{9}$/; // 手机正则
        var phoneReg = /^(0\d{2,3}-?\d{7,8})|((（|\()0\d{2,3}(\)|）)\d{7,8})$/; // 电话正则
        var flag = true;
        $('#addSupplier_edit_cont .input-warnTitle').each(function (index, dom) {
            $(this).css({ display: 'none' })
        })
        $('#newaddSupplier_address_select select').css({ border: '1px solid rgba(0, 0, 0, 0.14)' });
        $('#supplier_textarea').removeClass("addWarnInput");
        $('#adialog_newAddSupplier input[name="adialog_mobile"]').removeClass("addWarnInput");
        $('#adialog_newAddSupplier input[name="adialog_name"]').removeClass("addWarnInput");
        $('#newaddSupplier_address_select .newaddSupplier_address_select_box').css({ 'margin-bottom': '16px' })
        if (crruData) {
            reqModel.Id = crruData.Id;
        }
        reqModel.SenderName = $.trim($('#adialog_newAddSupplier input[name="adialog_name"]').val());
        reqModel.SenderMobile = $.trim($('#adialog_newAddSupplier input[name="adialog_mobile"]').val());
        reqModel.SenderTelePhone = $.trim($('#adialog_newAddSupplier input[name="adialog_telephone"]').val());
        reqModel.Province = $("#newSupplierProvince-select").val();
        reqModel.City = $("#newSupplierCity-select").val();
        reqModel.District = $("#newSupplierArea-select").val();
        reqModel.Address = $.trim($('#supplier_textarea').val());
        reqModel.RemarkName = $.trim($('#adialog_newAddSupplier input[name="adialog_remarkName"]').val());
        //2021-10-27,项颖说隐藏。和二维码扫描绑定保持一致。只有编辑有，其他场景都没有地址修改
        if (type == 1 || type == 3)
            flag = true;

        if (crruData && crruData.SupplierType == "Virtual" && (type == 1 || type == 2))
            flag = true;

        if (reqModel.SenderName == "" || reqModel.SenderName == null) {
            layer.msg('请填写代发件人姓名', { icon: 7 });
            var $this = $('#adialog_newAddSupplier input[name="adialog_name"]')
            $this.addClass("addWarnInput");
            $this.next().css({ display: 'inline-block' });
            flag = false;
        }
        if (!reqModel.SenderMobile) {
            var $this = $('#adialog_newAddSupplier input[name="adialog_mobile"]');
            $this.addClass("addWarnInput");
            $this.next().text("请填写联系手机").show();
            flag = false;
        }


        if (reqModel.SenderMobile != "" && !mobileReg.test(reqModel.SenderMobile)) {
            var $this = $('#adialog_newAddSupplier input[name="adialog_mobile"]');
            $this.addClass("addWarnInput");
            $this.next().text("手机号码格式错误").show();
            flag = false;
        }

        if (reqModel.SenderTelePhone != "" && !phoneReg.test(reqModel.SenderTelePhone)) {
            layer.msg('代发固话格式不对', { icon: 7 });
            var $this = $('#adialog_newAddSupplier input[name="adialog_telephone"]');
            $this.addClass("addWarnInput");
            flag = false;
        }
        if (reqModel.Province == undefined || reqModel.Province == "0") {
            newAddrTip()
            flag = false;
        }
        if (reqModel.City == undefined || reqModel.City == "0") {
            newAddrTip()
            flag = false;
        }
        if (reqModel.District == undefined || reqModel.District == "0") {
            var lent = $("#supplierArea-select>option").length;
            if (lent > 1) {
                newAddrTip()
            }
            flag = false;
        }
        if (reqModel.Address == '') {
            newAddrTip()
            flag = false;
        }
        function newAddrTip() {
            $('#newaddSupplier_address_select .input-warnTitle').css({ display: 'block' })
            $('#newaddSupplier_address_select .newaddSupplier_address_select_box').css({ 'margin-bottom': '0px' })
            $('#newaddSupplier_address_select select').css({ border: '1px solid #EA572E' });
            $('#supplier_textarea').addClass("addWarnInput")
        }
        return flag;
    }
    function onClearStyle() {
        $('#addSupplier_edit_cont .input-warnTitle').each(function (index, dom) {
            $(this).css({ display: 'none' })
        })
        $('#addSupplier_address_select select').css({ border: '1px solid rgba(0, 0, 0, 0.14)' });
        $('#supplier-textarea').removeClass("addWarnInput");
        $("#adialog_mobile").removeClass("addWarnInput");
        $('#supplier-textarea').removeClass("addWarnInput");
        $('#adialog_name').removeClass("addWarnInput");
    }
    function onClearReqModel() {
        reqModel = {
            Id: 0,
            Key: "",
            Status: 0,
            PageIndex: 1,
            PageSize: 50,
            SupplierFxUserId: 0,
            SenderName: null,
            SenderMobile: null,
            Province: null,
            City: null,
            District: null,
            Address: null,
            RemarkName: null,
            SenderTelePhone: null
        };
    }
    module.invitesupplier = function () {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.SupplierInvite);
        });

        var thisFunc = function (event) {
            var checkResult = commonModule.CheckVirtualRegMobile();
            if (!checkResult) {
                return;
            }
            if (event) {
                event.stopPropagation();
            } else {
                window.event.returnValue = false;
            };
            $("#invitesupplier_tab li:eq(0)").addClass("layui-this");
            $("#invitesupplier_tab li:eq(1)").removeClass("layui-this");
            $("#invitesupplier_wrap .invitesupplier-content:eq(1)").hide();
            $("#invitesupplier_wrap .invitesupplier-content:eq(0)").show();
            $("#invitesupplier_wrap").show(200);


            commmon.Ajax({
                url: '/Partner/LoadMySupplierQrCode',
                loading: true,
                data: {},
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        layer.closeAll();
                        return;
                    }
                    $("#qrCodeExceedTimeId").html(rsp.Data.ExceedTime);
                    module.makeCode(rsp.Data.QrCode)
                }
            });
        }
    }

    module.closeInvitesupplier = function () {
        $("#invitesupplier_wrap").hide(200);
    }

    $(document).on("click", function () {
        $("#invitesupplier_wrap").hide();
    })

    module.virtualHelp = function () {
        //iframe窗
        layer.open({
            type: 1,
            title: "虚拟厂家使用教程",
            closeBtn: 1, //不显示关闭按钮
            shade: [0.3],
            area: ['970px', '700px'],
            content: '<img src="https://www.dgjapp.com/public/images/virtualHelp-min.png" style="padding:10px">', //iframe的url，no代表不显示滚动条
            skin: 'wu-dailog',
            end: function () { //此处用于演示

            }
        });
    }

    module.makeCode = function (elText) {
        $("#supplierQrcodeId").html("");

        var qrcode = new QRCode(document.getElementById("supplierQrcodeId"), {
            width: 200,
            height: 200
        });

        qrcode.makeCode(elText);
    }


    //解绑厂家 Dailog
    module.replaceSupplierDailog = function (result) {
        //定时获取任务状态
        if (getBindSupplierStatus_INTERVAL == null) {
            getBindSupplierStatus_INTERVAL = setInterval(function () { getBindSupplierStatus(); }, 2000);
        }
        layer.open({
            type: 1,
            title: "解绑合作关系公告", //不显示标题
            content: $('.replaceSupplierDailog'),
            area: ['600px'], // 宽高
            btn: ['切换后台更换'],
            skin: 'wu-dailog',
            success: function () {
                updateProgressInfo(result);
            },

            yes: function () {
                layer.closeAll();

            },

        });


    }
    module.GetCooperateStatusBySupplier = function (supplierFxUserId) {

        commonModule.Ajax({
            url: '/Partner/GetCooperateStatusBySupplier',
            data: { supplierFxUserId: supplierFxUserId },
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success) {
                    var records = rsp.Data;
                    var tplt = $.templates("#bindOperateLog_data");
                    var html = tplt.render({
                        records: records
                    });
                    layer.open({
                        type: 1,
                        title: "合作日志",
                        skin: 'wu-dailog',
                        content: html,
                        area: '700px', //宽高
                        btn: false,
                    });
                }
                else {
                    layer.msg(rsp.Message);
                }
            }
        });

    }
    //下载二维码
    module.onloadQRC = function () {
        var base64 = $("#supplierQrcodeId>img").attr("src");

        if (window.navigator.msSaveOrOpenBlob) {
            var bstr = atob(base64.split(',')[1]);
            var n = bstr.length;
            var u8arr = new Uint8Array(n);
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }
            var blob = new Blob([u8arr]);
            window.navigator.msSaveOrOpenBlob(blob, 'chart-download' + '.' + 'png');
        } else {
            // 这里就按照chrome等新版浏览器来处理
            const a = document.createElement('a');
            a.href = base64;
            a.setAttribute('download', 'MySupplie-QRC');
            a.click();
        }
    }



    //获取异步任务状态
    var getBindSupplierStatus = function () {
        commonModule.Ajax({
            type: "POST",
            url: "/Partner/GetReBindProductOrderStatus",
            success: function (rsp) {
                if (commonModule.IsError(rsp)) {
                    clearInterval(getBindSupplierStatus_INTERVAL);
                    getBindSupplierStatus_INTERVAL = null;
                    return;
                }
                var data = rsp.Data;

                if (rsp.Message.indexOf('执行中') > 0) {
                    updateProgressInfo(data);
                    setBatchButton(false);
                }
                else {
                    setBatchButton(true);

                    clearInterval(getBindSupplierStatus_INTERVAL);
                    getBindSupplierStatus_INTERVAL = null;
                    layer.closeAll();

                    setTimeout(function () { module.Search(); }, 2000);
                }
            }
        });
    }



    //更新进度条百分比
    var updateProgressInfo = function (result) {
        var totalCount = 0;
        var successCount = 0;
        var per = 1;

        if (result != null) {
            totalCount = result.TotalCount;
            successCount = result.SuccessCount;
        }

        if (totalCount > 0) {
            per = ((parseInt(successCount) * 100) / parseInt(totalCount)).toFixed(0);
        }

        if (per > 100) { per = 100; }

        $('#loader-span-per').width(per + '%');
        $('#loader-per').text(per + '%');

    }

    //设置提示文字，flag=true为空，flag=false设置提示文字
    var setBatchButton = function (flag) {
        if (flag) {
            $('#progress-tip').hide();
            $('#progress-tip').html("");
            layer.closeAll();
        }
        else {
            $('#progress-tip').show();
            $('#progress-tip').html("当前有解绑厂家任务进行，请等待任务结束后操作");
        }
    }
    return module;
}(mySupplierModule || {}, commonModule, jQuery, layer));