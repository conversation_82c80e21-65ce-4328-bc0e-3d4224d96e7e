var GotoAuthModel = null;
var partnerModule = (function (module, commmon, $, layer) {

    var protocol = location.protocol; //站点协议
    var host = location.host; //站点域名
    //列表数据
    var shops = [];

    var reqModel = {
        ShopId: 0,
        Status: 0,
        PageIndex: 1,
        PageSize: 50
    };
    var shopId = commmon.getQueryVariable('shopId') ? commmon.getQueryVariable('shopId') : '';
    var isFirstOperate = true;
    //module.getExpireCount = function () {  //过期店铺提示框

    //    var expireCount = commonModule.AboutToExpireCount;//获取过期店铺；
    //    if (expireCount && expireCount > 0) {
    //        $("#layui_warnin").css({ display: "flex" });
    //        $("#warnin_mun").text(expireCount);
    //    }
    //}
    var selectedDeliveryModeValue = '';
    var kuaishouTimer = null;

    var setUrgentUnBindLayer = null;
    var unBindShopid = null;
    var platformTypeName = null;
    var appKey = null;

    var timeoutsss = null;
    //验证码按钮倒计时
    var count = 120;
    // 是否发送过短信验证
    var isSmsVerification = false;

    $(function () {
        // 绑定 change 事件到 radio 按钮
        $("input[name='DeliveryMode']").change(function () {
            // 获取选中的 radio 的 value
            selectedDeliveryModeValue = $("input[name='DeliveryMode']:checked").val();
        });
        // 监听页面销毁（如关闭页面或销毁DOM时）清除定时器
        $(window).on('beforeunload', function () {
            clearInterval(kuaishouTimer); // 清除定时器
        });
    });

    module.Init = function (isInitPlatform) {
        initSearchCondition();
        // 需要初始化
        if (isInitPlatform) {
            initSupportPlatform();
        }
        module.LoadList(false);
    };

    module.initSupportPlatform = function () {
        initSupportPlatform();
    }
    // 快手平台轮询
    function requestKuaiShouAuthStatus() {
        // 设置定时器，每2秒请求一次接口
        kuaishouTimer = setInterval(function () {
            commonModule.Ajax({
                url: "/Partner/GetKuaiShouAuthStatus",
                type: "POST",
                loading: false,
                success: function (rsp) {
                    if (rsp.Success == false) {
                        showSystemAuthFailure(rsp.Message, rsp.ErrorCode, rsp.Data);
                        if (rsp.ErrorCode) {
                            clearInterval(kuaishouTimer); // 清除定时器
                        }
                    }
                }
            });
        }, 5000);
    }
    // 系统提示
    function showSystemAuthFailure(mobile, ErrorCode, shopId) {
        var html = "";
        html += '<div class="wu-f16 wu-c09 wu-weight600">授权失败</div>';
        html += '<div class="wu-f14 wu-c06 wu-mT8">该店铺已被绑定至';
        html += '<span class="wu-color-b">' + mobile + '</span>';
        if (ErrorCode === 'CAN_UNBIND') {
            html += '<div>检测到该店铺近45天内无订单记录，您可以直接将该店铺绑定至您的账号</div>';
        }
        if (ErrorCode === 'NOT_UNBIND') {
            html += '<div>请前往该账号的店铺管理申请解绑并重新绑定</div>';
        }
        html += '</div>';
        var btnText = ErrorCode === 'CAN_UNBIND' ? ['立即绑定', '暂不绑定'] : ['如何解绑', '关闭'];
        layer.open({
            type: 1,
            title: '系统提示',
            content: html,
            area: '560px', //宽高
            skin: 'wu-dailog',
            success: function () { },
            btn: btnText,
            btn1: function (index) {
                if (ErrorCode === 'CAN_UNBIND') {
                    commonModule.Ajax({
                        url: "/Partner/UrgentBindShop",
                        type: "POST",
                        loading: true,
                        data: { shopId: shopId },
                        success: function (rsp) {
                            if (rsp.Success) {
                                commonModule.w_alert({ type: 4, content: '绑定成功' });
                                layer.closeAll();
                                module.LoadList(false);
                            } else {
                                commonModule.w_alert({ type: 2, content: rsp.Message });
                                layer.close(index);
                            }
                        }
                    });
                }
                if (ErrorCode === 'NOT_UNBIND') {
                    window.open('https://m0ij9216j9.feishu.cn/docx/Huecdlrg3oto5qxMbg2c8pAUncf', '_blank');
                    layer.close(index);
                }
            },
            btn2: function (index) {
                layer.close(index);
            },
        });
    }

    var uiAddPartner = null;
    //初始化支持的平台
    var platformHelpBailog = null;
    var initSupportPlatform = function () {
        var html = "<tr>";
        commonModule.Foreach(supportPlatforms, function (i, o) {
            if (o.PlatformName == "微信小店" && o.PlatformType == "WxVideo") {
                o.Style = "-5px -289px";
            }
            if (o.PlatformName == "淘宝买菜" && o.PlatformType == "TaoBaoMaiCai") {
                o.Style = "-115px -240px";
            }

            var str = "<td  platform='" + o.PlatformType + "' platformname='" + o.PlatformName + "' class='wu-pintaiIcon wu-big " + o.PlatformType + "'  style='background-position:" + o.Style + "'><div class='platformContnet'></div></td>"
            // 其他平台样式调整
            if (o.PlatformType == 'OtherPlatforms') {
                str = "<td  platform='" + o.PlatformType + "' platformname='" + o.PlatformName + "' class='wu-pintaiIcon wu-big " + o.PlatformType + "' style='background-position: -303px -257px;background-size: 500px 450px;'><div class='platformContnet'></div></td>"
            }
            // 自有商城样式调整
            if (o.PlatformType == 'OwnShop') {
                str = "<td  platform='" + o.PlatformType + "' platformname='" + o.PlatformName + "' class='wu-pintaiIcon wu-big " + o.PlatformType + "' style='background-position: -225px -288px;'><div class='platformContnet'></div></td>"
            }
            if (i > 0 && i % 6 == 0)
                html += "</tr><tr>" + str;
            else
                html += str;
        });
        html += "</tr>";
        $("#tb_support_platform").html(html);
        //绑定td选中样式
        $(".adialog-Shops tr>td").each(function () {
            $(this).on("click", function () {
                if (!$(this).attr("data-type")) {
                    $(".adialog-Shops tr>td").removeClass("active");
                    $(this).addClass("active");
                    var platform = $(this).attr("platform");
                    var platformName = $(this).attr("platformname");
                    // 头条订购双应用弹框提示
                    if (platform == "TouTiao" && HasTwoApp) {
                        var showID = 'setNoTouTiaoChangePlatformWarn';
                        commonModule.LoadCommonSetting(showID, true, function (rsp) { //是否展示
                            if (rsp.Success && rsp.Data != "1") {
                                module.showTouTiaoChangePlatformDailog(showID);
                            }
                        });
                    }
                    if (platform == "Alibaba" || platform == "KuaiShou") {
                        partnerModule.goToTouTiaoAuthModel(platform);
                    }
                    // 京东供销平台
                    if (platform == "JingdongPurchase") {
                        commonModule.Ajax({
                            url: "/partner/HasShop",
                            type: "POST",
                            loading: true,
                            data: { pt: platform },
                            success: function (rsp) {
                                if (rsp.Success) {
                                    // 如果返回true，就不需要弹窗了
                                    var resData = rsp.Data;
                                    if (!resData) {
                                        DeliveryModeSelectDialog();
                                    }   
                                }
                            }
                        });
                    }
                    //填id模式
                    if (!FillIdModel(platform, platformName)) {
                        GotoAuthModel(platform);
                    }
                }
            })
        })
    }
    // 发货模式选择
    function DeliveryModeSelectDialog() {
        var setKey = '/JingdongPurchase/DeliveryMode';
        commonModule.Ajax({
            url: "/Common/LoadCommonSetting",
            data: {
                settingKey: setKey
            },
            type: "POST",
            loading: true,
            loadingMessage: "加载中...",
            showMasker: false,
            success: function (rsp) {
                if (rsp.Success) {
                    var deliveryModeLayer = layer.open({
                        type: 1,
                        title: '发货模式选择', // 标题
                        content: $(".delivery-mode-select-container"),
                        offset: '180px',
                        area: '560px', // 宽高
                        skin: 'n-skin',
                        success: function () {
                            $("input[name='DeliveryMode'][value='" + rsp.Data + "']").prop("checked", true); // 设置选中状态
                            $(".n-skin .layui-layer-content").css("padding", 0);
                            // 使用 off() 方法移除上一次的点击事件
                            $("#CloseDeliveryModeSelect").off("click");
                            $("#ShowDeliveryModeOperateLog").off("click");
                            $("#ConfirmDeliveryModeSelect").off("click");

                            $("#CloseDeliveryModeSelect").on("click", function () {
                                layer.close(deliveryModeLayer);
                            });
                            // 修改日志
                            $("#ShowDeliveryModeOperateLog").on("click", function () {
                                commonModule.Ajax({
                                    url: "/Common/GetDeliveryModeChangeLog",
                                    type: "POST",
                                    success: function (rsp) {
                                        if (rsp.Success) {
                                            var data = rsp.Data;
                                            var tplt = $.templates("#delivery_mode_operation_log");
                                            var html = tplt.render({
                                                data: data,
                                            });
                                            var logLayer = layer.open({
                                                type: 1,
                                                title: "发货模式修改日志", // 标题
                                                content: html,
                                                area: '640px', // 宽高
                                                skin: 'n-skin',
                                                cancel: function () {
                                                    layer.close(logLayer);
                                                }
                                            });
                                        }
                                    }
                                });
                            });
                            $("#ConfirmDeliveryModeSelect").on("click", function () {
                                commonModule.Ajax({
                                    url: "/Common/SaveCommonSetting",
                                    data: { settingKey: setKey, settingValue: selectedDeliveryModeValue },
                                    type: "POST",
                                    loading: true,
                                    loadingMessage: "处理中...",
                                    showMasker: false,
                                    success: function (rsp) {
                                        if (rsp.Success) {
                                            // commonModule.w_alert({ type: 4, content: '保存成功' });
                                            layer.close(deliveryModeLayer);
                                            var jingdongPtData = null;
                                            var activePt = $("#tb_support_platform td.active").attr("platform"); // JingdongPurchase
                                            if (supportPlatforms.length > 0) {
                                                jingdongPtData = supportPlatforms.find(function (item) {
                                                    return item.PlatformType === activePt;
                                                });
                                                if (jingdongPtData.IsAuthUrl) {
                                                    var token = module.GetQueryString("token");
                                                    var authUrl = jingdongPtData.AuthUrl;
                                                    commmon.OpenNewTab(authUrl + "&rp=" + token);
                                                    var isAddOk = layer.confirm('是否授权成功？', { icon: 3, title: '授权结果确认', btn: ['确定', '取消'], skin: 'wu-dailog' },
                                                        function () {
                                                            module.Init(false);
                                                            layer.closeAll();
                                                        }, function () {
                                                            module.Init(false);
                                                            layer.close(isAddOk);
                                                        }
                                                    );
                                                   
                                                }
                                            }
                                        }
                                    }
                                });
                            });
                        },
                        cancel: function () {
                            layer.close(deliveryModeLayer);
                        }
                    });
                }
            }
        });
    }

    FillIdModel = function (platform, platformName) {
        if (platform == "WxXiaoShangDian" || platform == "WxVideo" || platform == "TuanHaoHuo" || platform == "OtherPlatforms" || platform == "OwnShop") { //|| platform == "YouZan" 
            var reg = /^[0-9a-zA-Z]+$/;
            var $content = null;
            var payurl = null;
            if (platform == "WxXiaoShangDian") {
                $content = $("#support_platform_wxinto");
                reg = /^wx[0-9a-zA-Z]+$/;
                payurl = "https://shop.weixin.qq.com/";
            }
            else if (platform == "WxVideo") {

                $content = $("#support_platform_wxvinto");
                reg = /^wx[0-9a-zA-Z]+$/;
                payurl = "https://channels.weixin.qq.com/shop";

                //if (platformName == "微信小店" && IsWxVideoOldUser == true) {  //微信小店开启双应用  到时改成==true
                //    showWxVideoAuthDailog(platform);
                //    return true;
                //}
            }
            else if (platform == "YouZan") {
                $content = $("#support_platform_yzinto");
                reg = /^[0-9]+$/;
                payurl = "https://yingyong.youzan.com/cloud-app-detail/43116";
            }
            else if (platform == "TuanHaoHuo") {
                $content = $("#support_platform_thhinto");
            }
            else if (platform == "OtherPlatforms") {
                $content = $("#support_otherplatforms");
            }
            else if (platform == "OwnShop") {
                $content = $("#support_platform_ownshop");
            }
            platformHelpBailog = layer.open({
                type: 1,
                title: false,
                closeBtn: 0,
                content: $content,
                area: '350px', // 宽高
                skin: 'wu-dailog wu-right add-support-platform-dailog',
                btn: ['添加', '取消'],
                yes: function () {
                    
                    // var shopId = $("#support_platform_" + platform + ">input[name='shopId']").val();
                    var shopId = $("#support_platform_" + platform).find("input[name='shopId']").val();
                    
                    if (platform == "OtherPlatforms") {
                        GotoOtherPlatforms();
                    }
                    else if (platform != "TuanHaoHuo") {
                        if (reg.test(shopId) != true && platform != "OwnShop") {
                            layer.msg("请填写正确的信息", { icon: 2 });
                            return;
                        }
                        commmon.Ajax({
                            url: "/Partner/AddShop",
                            type: "POST",
                            data: { pt: platform, shopId: shopId },
                            loading: true,
                            success: function (rsp) {
                                if (rsp.Success == false) {
                                    if (rsp.ErrorCode) {
                                        // 微信视频号
                                        if (platform === "WxVideo") {
                                            if (rsp.ErrorCode === 'CAN_UNBIND' || rsp.ErrorCode === 'NOT_UNBIND') {
                                                showSystemAuthFailure(rsp.Message, rsp.ErrorCode, rsp.Data);
                                            }
                                            if (rsp.ErrorCode === 'NOTAUTH') {
                                                // 或许购买了，但没有点去使用
                                                layer.alert('您可能没有订购应用，请前往服务市场订购。【<a href="' + payurl + '" target="_blank" class="wu-color-a wu-operate">订购链接</a>】<br/>如果您已订购，请先从服务市场进入【我的服务】，找到当前系统应用，点击【<span class="wu-weight600">去使用</span>】后再添加店铺。', { title: '绑定失败', area: ['200'], skin: 'wu-dailog' });
                                            }
                                            if (rsp.ErrorCode === 'NOTPAY') {
                                                // 分多个应用，并没有购买分单的
                                                layer.alert('您可能没有订购<span class="wu-weight600">分销代发</span>应用，请前往服务市场订购。【<a href="' + payurl + '" target="_blank" class="wu-color-a wu-operate">订购链接</a>】<br/>如果您已订购，请先从服务市场进入【我的服务】，找到<span class="wu-weight600">分销代发</span>应用，点击【<span class="wu-weight600">去使用</span>】后再添加店铺。', { title: '绑定失败', area: ['200'], skin: 'wu-dailog' });
                                            }
                                        } else {
                                            if (rsp.ErrorCode.indexOf("NOTAUTH") != -1) {
                                                // 或许购买了，但没有点去使用
                                                layer.alert('您可能没有订购应用，请前往服务市场订购。【<a href="' + payurl + '" target="_blank" class="wu-color-a wu-operate">订购链接</a>】<br/>如果您已订购，请先从服务市场进入【我的服务】，找到当前系统应用，点击【<span class="wu-weight600">去使用</span>】后再添加店铺。', { title: '绑定失败', area: ['200'], skin: 'wu-dailog' });
                                            } else if (rsp.ErrorCode.indexOf("NOTPAY") != -1) {
                                                // 分多个应用，并没有购买分单的
                                                layer.alert('您可能没有订购<span class="wu-weight600">分销代发</span>应用，请前往服务市场订购。【<a href="' + payurl + '" target="_blank" class="wu-color-a wu-operate">订购链接</a>】<br/>如果您已订购，请先从服务市场进入【我的服务】，找到<span class="wu-weight600">分销代发</span>应用，点击【<span class="wu-weight600">去使用</span>】后再添加店铺。', { title: '绑定失败', area: ['200'], skin: 'wu-dailog' });
                                            } else {
                                                layer.alert(rsp.Message, { icon: 2, skin: 'wu-dailog' });
                                            }
                                        }
                                    } else {
                                        layer.alert(rsp.Message, { icon: 2, skin: 'wu-dailog' });
                                    }
                                    return;
                                }
                                layer.closeAll();
                                module.Init(false);
                            }
                        });
                    }
                    else {
                        var token = module.GetQueryString("token");
                        // var shopName = $("#support_platform_" + platform + ">input[name='shopName']").val();
                        var shopName = $("#support_platform_" + platform).find("input[name='shopName']").val()
                        if ((!shopId || shopId == "") && (!shopName || shopName == "")) {
                            layer.msg("店铺ID和店铺名称不能为空");
                            return false;
                        }
                        else if (!shopId || shopId == "") {
                            layer.msg("店铺ID不能为空");
                            return false;
                        }
                        else if (!shopName || shopName == "") {
                            layer.msg("店铺名称不能为空");
                            return false;
                        }
                        commonModule.Foreach(supportPlatforms, function (i, o) {
                            if (o.PlatformType == "TuanHaoHuo") {
                                var authurl = o.AuthUrl;
                                authurl += "&shopId=" + shopId;
                                authurl += "&shopName=" + shopName;
                                authurl += "&rp=" + token;
                                commmon.OpenNewTab(authurl);
                                var isAddOk = layer.confirm('是否授权成功？', { icon: 3, title: '授权结果确认', btn: ['确定', '取消'], skin: 'wu-dailog' },
                                    function () {
                                        module.Init(false);
                                        layer.closeAll();
                                    }, function () {
                                        module.Init(false);
                                        layer.close(isAddOk);
                                    }
                                );
                            }
                        });
                    }
                },
                cancel: function () {

                }
            });
            return true;
        }
        return false;
    }
    var otherplatformHelpBailog = null;
    GotoOtherPlatforms = function () {
        var platform = $("#otherplatforms_type").val();
        var $content = null;
        var prefix = "#support_Other_Heliang";
        if (platform == 'Other_Heliang') {
            prefix = '#support_Other_Heliang';
            $("#support_Other_Heliang>ul>li").find("input[name='shopName']").val('');
            $("#support_Other_Heliang>ul>li").find("input[name='appId']").val('');
            $("#support_Other_Heliang>ul>li").find("input[name='appSecret']").val('');
            $content = $("#support_other_heliang");
        }
        else if(platform == 'Other_JuHaoMai') {
            prefix = '#support_Other_JuHaoMai';
            $(prefix + ">ul>li").find("input[name='appId']").val('');
            $(prefix + ">ul>li").find("input[name='appSecret']").val('');
            $content = $("#support_other_juhaomai");
        }
        else if (platform == 'Other_HaoYouDuo') {
            prefix = '#support_Other_HaoYouDuo'
            $(prefix + ">ul>li>label").find("input[name='appId']").val('');
            $(prefix + ">ul>li>label").find("input[name='appSecret']").val('');
            $content = $("#support_other_haoyouduo");
        }
        otherplatformHelpBailog = layer.open({
            type: 1,
            title: false,
            closeBtn: 0,
            content: $content,
            area: ['350'], //宽高
            btn: ['确定', '返回'],
            skin: 'wu-dailog add-support-platform-dailog',
            yes: function () {
                if (platform == 'Other_Heliang' || platform == 'Other_JuHaoMai' || platform == 'Other_HaoYouDuo') {
                    // 禾量平台：输入店铺名称，AppId，AppSecret
                    // 聚好麦平台：输入AppId，AppSecret
                    //好又多平台=》输入AppId，AppSecret
                    var shopName = $(prefix + ">ul>li").find("input[name='shopName']").val() || '';
                    var appId = $(prefix + ">ul>li").find("input[name='appId']").val() || '';
                    var appSecret = $(prefix + ">ul>li").find("input[name='appSecret']").val() || '';

                    if ((!shopName || shopName == "") && platform == 'Other_Heliang') {
                        wuFormModule.wu_toast({ type: 3, content: "请填写正确的店铺名称" });
                        return;
                    }
                    if (!appId || appId == "") {
                        wuFormModule.wu_toast({ type: 3, content: "请填写正确的AppId" });
                        return;
                    }
                    if (!appSecret || appSecret == "") {
                        wuFormModule.wu_toast({ type: 3, content: "请填写正确的AppSecret" });
                        return;
                    }
                    if (shopName.length > 32) {
                        wuFormModule.wu_toast({ type: 3, content: "店铺名称不能超过32个字符" });
                        return;
                    }
                    commmon.Ajax({
                        url: "/Partner/AddShopByOtherPlatforms",
                        type: "POST",
                        data: { pt: platform, shopName: shopName, appId: appId, appSecret: appSecret, isBind: true },
                        loading: true,
                        success: function (rsp) {
                            if (rsp.Success == false) {
                                layer.alert(rsp.Message, { icon: 2, title: '提示', skin: 'wu-dailog' });
                                return;
                            }
                            wuFormModule.wu_toast({ type: 4, content: "授权成功" });
                            layer.closeAll();
                            module.Init(false);
                        }
                    });
                }
            },
            cancel: function () {
            }
        })
    }


    showWxVideoAuthDailog = function (platform) {
        GotoWxVideoAuthModel(platform);
    }
    var wxVideoStatus = false;
    GotoWxVideoAuthModel = function (platform) {

        layer.open({
            type: 1,
            title: false,
            content: $("#support_platform_WxVideoDailog"),
            area: '620px',
            btn: false,
            skin:'adialog-Shops-skin',
            success: function () {
                $$.navActive("#togglePlatform_content_WxVideo", function (i, item) {

                    wxVideoStatus = $(item).attr("data-status") =="old"?false:true;
                    var $content = $("#new_support_platform_wxvinto");
                    $("#new_support_platform_" + platform + ">input[name='shopId']").val("");

                    var newPlatformHelpBailog = layer.open({
                        type: 1,
                        title: false,
                        closeBtn: 0,   
                        content: $content,
                        shade: 0,
                        skin:'new-platform-HelpBailog',
                        area: ['350'], //宽高
                        btn: ['添加', '取消'],
                        yes: function () {
                            var reg = /^wx[0-9a-zA-Z]+$/;
                            var payurl = "https://channels.weixin.qq.com/shop";
                            // var shopId = $("#new_support_platform_" + platform + ">input[name='shopId']").val();
                            var shopId = $("#new_support_platform_" + platform).find("input[name='shopId']").val(); 
                            if (reg.test(shopId) != true) {
                                layer.msg("请填写正确的信息", { icon: 2 });
                                return;
                            }
                            commmon.Ajax({
                                url: "/Partner/AddShop",
                                type: "POST",
                                data: { pt: platform, shopId: shopId, isNewWxShop: wxVideoStatus },
                                loading: true,
                                success: function (rsp) {
                                    if (rsp.Success == false) {
                                        if (rsp.ErrorCode) {
                                            if (rsp.ErrorCode.indexOf("NOTAUTH") != -1) {
                                                //或许购买了， 但没有点去使用
                                                layer.alert('您可能没有订购应用，请前往服务市场订购。 【<a href="' + payurl + '" target="_blank" style="color:#3aadff;cursor:pointer;">订购链接</a>】<br/>如果您已订购，请先从服务市场进入【我的服务】，找到当前系统应用，点击【<b>去使用</b>】后再添加店铺。', { title: '绑定失败', area: ['200'] });

                                            } else if (rsp.ErrorCode.indexOf("NOTPAY") != -1) {
                                                //分多个应用，并没有购买分单的
                                                layer.alert('您可能没有订购<b>分销代发</b>应用，请前往服务市场订购。 【<a href="' + payurl + '" target="_blank" style="color:#3aadff;cursor:pointer;">订购链接</a>】<br/>如果您已订购，请先从服务市场进入【我的服务】，找到<b>分销代发</b>应用，点击【<b>去使用</b>】后再添加店铺。', { title: '绑定失败', area: ['200'] });

                                            } else {
                                                layer.alert(rsp.Message, { icon: 2, skin: 'wu-dailog' });
                                            }
                                        }
                                        else {
                                            layer.alert(rsp.Message, { icon: 2, skin: 'wu-dailog' });
                                        }
                                        return;
                                    }
                                    layer.closeAll();
                                    module.Init(false);
                                }
                            });

                        },
                        
                        cancel: function (index) {
                            $("#support_platform_" + platform + ">input[name='shopId']").val("");
                            layer.close(index);
                        }
                    });

                });
            },
        });
    }



    GotoAuthModel = function (platform) {
        var support_platform = "#support_platform_all";
        var toggle_id = "#togglePlatform_content";
        if (platform == 'Alibaba') {
            toggle_id = "#togglePlatform_content_alibaba";
            support_platform = "#support_platform_alibaba";
        }
        if (platform == 'KuaiShou') {
            toggle_id = "#togglePlatform_content_kuaishou";
            support_platform = "#support_platform_kuaishou";
        }
        if (platform == 'Jingdong') {
            toggle_id = "#togglePlatform_content_jingdong";
            support_platform = "#support_platform_jingdong";
        }
        if (platform == 'WxVideo') {
            toggle_id = "#togglePlatform_content_WxVideo";
            support_platform = "#support_platform_WxVideo";
        }

        commmon.Ajax({
            url: "/Partner/PlatformAddShop",
            type: "POST",
            loading: true,
            data: { pt: platform },
            success: function (rsp) {
                if (rsp.Success) {
                    var data = rsp.Data.Data || [];
                    if (platform == 'KuaiShou' && data.length == 1) {
                        var oneAppInfo = data[0];
                        $("#tb_support_platform>tr>td[platform=" + platform + "]").attr('newauthurl', oneAppInfo.AuthUrl);
                        return;
                    }
                    if (data.length <= 1) return;

                    $(support_platform).addClass(platform);
                    layer.open({
                        type: 1,
                        title: false,
                        closeBtn: 0,
                        content: $(support_platform),
                        area: '620px',
                        btn: ['确定', '取消'],
                        skin: 'wu-dailog platformAddShopDailog',
                        success: function () {
                            $(".platformAddShopDailog .layui-layer-content").css({
                                padding: '0px',
                                borderRadius: '10px 10px 0 0'
                            });
                            $(toggle_id + ">.togglePlatform-content-item").removeClass("active");
                            $(toggle_id).html("");
                            for (var i = 0; i < data.length; i++) {
                                var html = "<li class='togglePlatform-content-item' data-appkey='" + data[i].Appkey + "' data-status='" + data[i].AuthUrl + "'>";
                                var styleText = 'background-image:url(/Content/images/' + data[i].Img + ')';
                                if (data[i].ImgXy && data[i].ImgXy != "")
                                    styleText += ';background-position:' + data[i].ImgXy + ';';
                                html += "<span class='togglePlatform-content-item-img' style='" + styleText + "'></span>";
                                html += "<span class='togglePlatform-content-item-title'>" + data[i].Name + "</span>";
                                html += "</li>";
                                $(toggle_id).append(html);
                            }
                            $$.navActive(toggle_id, function () { });
                            if (platform == 'Alibaba') {
                                if (commonModule.OpenInfo1688) {
                                    if (commonModule.OpenInfo1688.IsOpenQing) {
                                        if (commonModule.OpenInfo1688.IsOpenAlibabaOld == false) {
                                            $(toggle_id + ">.togglePlatform-content-item:eq(0)").hide();
                                        }
                                    }
                                    else {
                                        if (commonModule.OpenInfo1688.IsOpen1688Shops) {
                                            $(toggle_id + ">.togglePlatform-content-item:eq(1)").hide();
                                        } else {
                                            $(toggle_id + ">.togglePlatform-content-item:eq(0)").hide();
                                        }
                                    }
                                }
                            }
                        },
                        btn1: function () {
                            var authurl = "";
                            var firstUrl = "";
                            var urlCount = 0;
                            $(toggle_id + ">.togglePlatform-content-item").each(function (index, item) {
                                if ($(item).hasClass("active")) {
                                    authurl = $(item).attr("data-status")
                                }
                                firstUrl = $(item).attr("data-status");
                                urlCount++;
                            })
                            if (urlCount == 1) {
                                module.imgToAuth(firstUrl);
                                if (platform === 'KuaiShou') {
                                    requestKuaiShouAuthStatus();
                                }
                            }
                            else if (authurl == "") {
                                layer.msg("请选择应用类型！")
                            } else {
                                module.imgToAuth(authurl); 
                                if (platform === 'KuaiShou') {
                                    requestKuaiShouAuthStatus();
                                }
                            }
                        },
                        cancel: function () {

                        }
                    });
                    if (uiAddPartner != undefined && uiAddPartner != null) {
                        layer.close(uiAddPartner);
                    }
                }
            },
            content: function () {

                //alert("aaa")
            }
        });
        return;
    }

    //选择头条应用进行授权
    module.imgToAuth = function (authurl) {
        var token = module.GetQueryString("token");
        var authurl = authurl;
        if (authurl.indexOf("?") > 0)
            authurl += "&rp=" + token;
        else
            authurl += "?rp=" + token;
        commmon.OpenNewTab(authurl);
        var isAddOk = layer.confirm('是否授权成功？', { icon: 3, title: '授权结果确认', btn: ['确定', '取消'], skin: 'wu-dailog' },
            function () {
                module.Init(false);
                layer.closeAll();
            }, function () {
                module.Init(false);
                layer.close(isAddOk);
            }
        );
    }

    //选择头条应用进行授权
    module.imgToutiao = function (type) {
        for (var i = 0; i < supportPlatforms.length; i++) {
            if (supportPlatforms[i].PlatformType == "TouTiao") {
                if (type == "old") {
                    supportPlatforms[i].AuthUrl = supportPlatforms[i].AuthUrl.replace("douyinfxnew?", "douyinfx?");
                } else {
                    supportPlatforms[i].AuthUrl = supportPlatforms[i].AuthUrl.replace("douyinfx?", "douyinfxnew?");
                }
                var token = module.GetQueryString("token");
                var authurl = supportPlatforms[i].AuthUrl;
                authurl += "&rp=" + token;
                commmon.OpenNewTab(authurl);
                var isAddOk = layer.confirm('是否授权成功？', { icon: 3, title: '授权结果确认', btn: ['确定', '取消'], skin: 'wu-dailog' },
                    function () {
                        module.Init(false);
                        layer.closeAll();
                    }, function () {
                        module.Init(false);
                        layer.close(isAddOk);
                    }
                );
            }
        }
    }

    module.platformHelp = function (type) {
        
        var v = (commonModule.IsNewCorp && type == "wxv") ? "-v2" : "";
        if (type == "wx") {
            $("#platform_helpShow_img").attr("src", "/Content/images/weixinhelpstep.png")
        } else if (type == "wxv") {
            $("#support-platform-more").show();
            $("#platform_helpShow_img").attr("src", "/Content/images/weixinxsphelpstep" + v + ".png")
        }
        else if (type == "yz") {
            $("#platform_helpShow_img").attr("src", "/Content/images/youzhanhelpstep.png")
        } else if (type == "thh") {
            window.open("https://www.yuque.com/dianguanjiadadan/cg2p0s/xze7lb");
            return;
        } else if (type == "newwxv" && wxVideoStatus == false) {
            $("#support-platform-more").show();
            $("#platform_helpShow_img").attr("src", "/Content/images/noviceIntroPic/new-help-2024-11-11.png");
        } else if (type == "newwxv" && wxVideoStatus == true) {
            $("#support-platform-more").show();
            $("#platform_helpShow_img").attr("src", "/Content/images/noviceIntroPic/new-help-2024-11-12.png");
        }
        else if (type == "heliang") {
            $("#support-platform-more").show();
            $("#platform_helpShow_img").attr("src", "/Content/images/helianghelpstep.png")
        }
        else if (type == "juhaomai") {
            $("#support-platform-more").hide();
            $("#platform_helpShow_img").attr("src", "/Content/images/juhaomaihelpstep.png")
        }
        else if (type == "haoyouduo") {
            $("#support-platform-more").hide();
            $("#platform_helpShow_img").attr("src", "/Content/images/haoyouduohelpstep.png")
        }
        layer.open({
            type: 1,
            shade: false,
            offset: 'auto',
            title: false, //不显示标题
            area: ['700'], //宽高
            content: $("#support_platform_helpShow"),
            cancel: function () {

            }
        });
    }
    module.closePlatformInto = function () {
        layer.close(platformHelpBailog)
        layer.close(otherplatformHelpBailog)
    }

    module.closeHeliangPlatformInto = function () {
        layer.close(otherplatformHelpBailog)
    }
    module.closeHeliangPlatformInto = function () {
        layer.close(otherplatformHelpBailog)
    }

    var initSearchCondition = function () {
        //1.初始化状态查询条件
        var selStatus = $("#sel_status");
        selStatus.empty();
        selStatus.append("<option value='0'>授权状态</option>")
        selStatus.append("<option value='1'>授权成功</option>")
        //selStatus.append("<option value='2'>解除关联</option>")
        selStatus.append("<option value='3'>授权过期</option>")

        //2.初始化店铺查询条件
        commonModule.Ajax({
            url: '/partner/LoadShop',
            type: 'GET',
            loading: true,
            success: function (rsp) {
                if (rsp.Success == false) {
                    return;
                }
                var shops = rsp.Data.shops;
                //if (shops.length >= rsp.Data.limt)
                //{
                //    $("#addShos").attr("onclick", "partnerModule.IsDisabled()").css("background-color", "#CCC");
                //} else {
                //    $("#addShos").attr("onclick", "partnerModule.AddShopWarn()").css("background-color", "#f59c1a");
                //}


                //var selShops = $("#sel_shop");
                //selShops.empty();
                //selShops.append("<option value='0'></option>");
                //selShops.append("<option value='0'>==店铺名称==</option>");
                //commonModule.Foreach(shops, function (i, s) {
                //    if (s.ShopId == defaultShopId)
                //        selShops.append("<option value='" + s.ShopId + "' selected>" + s.NickName + "</option>");
                //    else
                //        selShops.append("<option value='" + s.ShopId + "'>" + s.NickName + "</option>");

                //});
                //layui.form.render("select");

                module.initShopsSelectBox(shops);//初始代店铺选择搜索框数据


            }
        });
        layui.form.render("select");

        //清空数据框
        $("input[name='shopId']").val("");
        $("input[name='shopName']").val("");
    }


    module.initShopsSelectBox = function (shops) {
        var selectboxArr = [];
        for (var i = 0; i < shops.length; i++) {
            var obj = {};
            obj.Value = shops[i].ShopId;
            obj.Text = shops[i].NickName;
            selectboxArr.push(obj);
        }
        var selectInit = {
            eles: '#sel_shops',
            emptyTitle: '全部店铺', //设置没有选择属性时，出现的标题
            data: selectboxArr,
            searchType: 1, //1出现搜索框，不设置不出现搜索框
            showWidth: '260px', //显示下拉的宽
            isRadio: false, //有设置，下拉框改为单选
            allSelect: true,
            selectData: [],  //初始化数据
            skin: 'wu-select-skin',
        };
        var selectBox = new selectBoxModule2();
        selectBox.initData(selectInit);
    }


    module.LoadList = function (isPaging, orderby) {

        if (shopId != '' && isFirstOperate) {
            reqModel.ShopIds = shopId;
            isFirstOperate = false;

            var timer = setInterval(function () {
                if ($("#sel_shops .selectMore-ul").length > 0) {
                    $("#sel_shops .selectMore-ul>li").each(function (i, item) {
                        var id = $(item).attr('data-id');
                        if (id == shopId) {
                            $(item).find('label').trigger("click");
                        }
                    });
                    clearInterval(timer);
                }
            }, 500)
        } else {
            //reqModel.ShopId = $("#sel_shop").val();
            var values = $("#sel_shops").attr('data-values');
            if (values == undefined || !values) {
                reqModel.ShopIds = '';
            } else {
                reqModel.ShopIds = values;
            }
        }
        reqModel.Status = $("#sel_status").val();

        //1.ajax
        commonModule.Ajax({
            url: '/partner/LoadMyShopList',
            data: { _reqModel: reqModel },
            async: true,
            loading: true,
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message);
                    return;
                }
                var rows = rsp.Data.List || [];

                console.log("rowsrows",rows)

                shops = [];
                //2.渲染
                var tplt = $.templates("#shareShopList_data_tr");
                var index = 0;
                commonModule.Foreach(rows, function (i, obj) {
                    obj.Index = index;
                    shops.push(obj);
                    index++;
                });
                var idArr = []  // 存放name，用来查找是否有相同name的情况
                var resultData = []  // 合并结果数组
                console.log("IsWhiteUserFlag", IsWhiteUserFlag);
                console.log("supportPlatforms ==>", supportPlatforms);
                for (var i = 0; i < rows.length; i++) {
                    if (idArr.indexOf(rows[i].ShopId) === -1) { // 没找到相同name的话
                        resultData.push({
                            ShopId: rows[i].ShopId,
                            PlatformTypeName: rows[i].PlatformTypeName,
                            rowSpan: 1,
                            children: [rows[i]],
                            IsWhiteUser: IsWhiteUserFlag
                        });
                        idArr.push(rows[i].ShopId)
                    } else { // 有相同name合并对象
                        for (var j = 0; j < resultData.length; j++) {
                            if (resultData[j].ShopId === rows[i].ShopId) {
                                resultData[j].rowSpan = resultData[j].rowSpan + 1;
                                resultData[j].IsWhiteUser = IsWhiteUserFlag;
                                resultData[j].children.push(rows[i]);
                                break
                            }
                        }
                    }
                }
                console.log("idArr===", idArr, "+++resultData===", resultData)
                //console.log("shops", shops)
                var html = tplt.render({ shopData: resultData });
                $("#ShareShopList_body").html(html);

                commonModule.HideNoPermDiv(commonModule.PartnerIndexShowPermDict);

                //3.分页
                if (isPaging == true) {
                    return;
                }

                layui.laypage.render({
                    elem: 'paging',
                    theme: ' wu-page',
                    count: rsp.Data.Total,
                    limit: reqModel.PageSize,
                    curr: reqModel.PageIndex,
                    limits: [50, 100, 150, 200, 300, 500],
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function (obj, first) {
                        //$(".layui-laypage-count").html()
                        if (!first) {
                            reqModel.PageSize = obj.limit;
                            reqModel.PageIndex = obj.curr;
                            module.LoadList(true);
                        }
                    }
                });
            }
        });
    }

    module.Search = function () {
        reqModel.PageIndex = 1;
        reqModel.IsWherQuery = true;
        module.LoadList(false);
    }
    var ui1AddShopWarn = null;
    // 添加新店铺
    module.AddShopWarn = function () {
        // commonModule.openBindMobilePhoneDailog();
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.AddNewShop); // AddNewShop是按钮绑定的id
        });
        var thisFunc = function () {
            var checkResult = commonModule.CheckVirtualRegMobile();
            if (!checkResult) {
                return;
            }
            module.Add();
        }
    }
    module.Add = function () {
        uiAddPartner = layer.open({
            type: 1,
            title: false,
            content: $('.adialog-Shops'),
            area: '632x', //宽高
            skin: 'adialog-Shops-skin wu-dailog wu-right add-new-shop-dailog',
            btn: ['确定', '取消'],
            success: function () {
                $("#tb_support_platform td").removeClass("active");
                layer.close(ui1AddShopWarn);
            },
            yes: function () {

                var pt = $("td.active").attr("platform");
                if (pt === 'KuaiShou') {
                    requestKuaiShouAuthStatus();
                }

                if (pt == "TikTok") {

                    var hasTikTokPt = false;
                    for (var i = 0; i < supportPlatforms.length; i++) {
                        var spt = supportPlatforms[i];
                        if (spt.PlatformType == "TikTok") {
                            hasTikTokPt = true;
                            break;
                        }
                    }
                    if (hasTikTokPt == false) {
                        supportPlatforms.push({
                            IsAuthUrl: true,
                            PlatformType: "TikTok",
                            PlatformName: "TikTok",
                            AuthUrl: "http://396ec02725.zicp.vip/fxauth/crossborder?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess",
                            Style: " - 110px - 48px; ",
                            PayUrl: null,
                            Flag: null,
                            AuthApi: null
                        });
                    }
                    
                    // 旧的授权方式不要了
                    // crossBorderModule.BindTkShop();

                    // 新授权方式
                    crossBorderModule.newBindTkShop();
                    return;
                }

                var newauthurl = $("td.active").attr("newauthurl");
                var bindShopFunc = function () {
                    var platform = null;
                    for (var i = 0; i < supportPlatforms.length; i++) {
                        if (supportPlatforms[i].PlatformType == pt) {
                            platform = supportPlatforms[i];
                        }
                    }
                    if (platform.IsAuthUrl) {
                        var token = module.GetQueryString("token");
                        var authUrl = platform.AuthUrl;
                        if (pt == "KuaiShou" && newauthurl)
                            authUrl = newauthurl;

                        commmon.OpenNewTab(authUrl + "&rp=" + token);
                        var isAddOk = layer.confirm('是否授权成功？', { icon: 3, title: '授权结果确认', btn: ['确定', '取消'], skin: 'wu-dailog' },
                            function () {
                                module.Init(false);
                                layer.closeAll();
                            }, function () {
                                module.Init(false);
                                layer.close(isAddOk);
                            }
                        );

                    } else {
                        layer.close(ui1);
                    }
                }

                if (pt == "JingDong") {
                    var html = '<div class="layui-mywrap expressTemplate" style="min-width:600px;background-color: #fce6e1;padding: 10px 15px;box-shadow: unset;">';
                    html += '<i class="icon" style="display: inline-block;height: 18px;width: 18px;border-radius: 10px;text-align: center;line-height: 18px;background-color: #ff5b28;color: #fff; font-size: 12px;margin-right: 3px;">!</i>';
                    html += '<span class="sys-warning-tip" style="color:red;font-size:14px;">';
                    html += '亲爱的京东用户您好，基于京东代发场景的特殊性，避免漏单情况出现，店管家-分销代发会默认将您授权成功店铺的多商品订单全部拆成子订单推送给厂家，并且京东后台的平台单也会拆成多个单商品的订单.确认授权后默认同意拆单操作。';
                    html += '</span>';
                    html += '</div>';
                    layer.confirm(html, { area: ["680px"], skin: 'wu-dailog' }, function () {
                        bindShopFunc();
                    });
                } else if (pt == "TaoBaoMaiCai") {
                    //全量切换时间：2025年08月31日23:59:59
                    var isAllSwitch = new Date() > new Date(2025, 7, 31, 23, 59, 59);
                    var html = '<div style="font-family: Source Han Sans;font-size: 14px;color: rgba(31, 35, 41)">';
                    html += '<div>'
                    html += '<p class="wu-color-b">官方通知：为提升系统服务能力及操作体验，淘宝买菜将于2025年08月20日起对商家工作台进行全方位升级。</p>'
                    html += '</div>'
                    html += '<div style="margin-top: 16px;">'
                    html += '<p>1.升级时间:2025年08月20日 00:00-2025年08月31日23:59(升级时间视升级进度可能有所调整)。</p>'
                    html += '<p>2.影响范围:所有已入驻商家(含新入驻未生效商家)。</p>'
                    html += '<p>3.迁移批次:新工作台逐步(by商家)放开，若解锁新工作台使用资格，商家的建品、发品、营销活动的操作将在新工作台完成。</p>'
                    html += '<p>4.过渡期:旧工作台官方将保留至2025年08月31日(如升级延期，旧工作台我们将持续保留至升级最后一天)，在此期间，商家可登录日工作台查看您的交易记录和相关信息。</p>'
                    html += '<p>5.如果您的工作台已经更新，请使用【淘宝买菜(新)】接口重新授权，避免由于无法同步订单导致发货超时。</p>'
                    html += '<p>6.如果您使用【淘宝买菜】接口授权失败，请尝试使用【淘宝买菜(新)】接口重新授权。</p>'
                    html += '</div>'
                    html += '</div>';
                    layer.confirm(html, {
                        area: ["680px", "auto"],
                        maxHeight: 500,
                        title: '淘宝买菜店铺授权升级通知',
                        btn: isAllSwitch ? ['关闭'] : ['取消', '我已知晓，继续授权'],
                        skin: 'wu-dailog',
                        success: function(layero) {
                            $(".layui-layer-title", layero).css({ "color":"#333", "font-weight" : "550"})
                            $(".layui-layer-btn0", layero).css({"background-color": "#FFF", "color":"#000","border-color" : "#dedede"})
                            $(".layui-layer-btn1", layero).css({ "background-color": "#0888ff", "color": "#FFF", "border-color": "#0888ff"})
                        },
                        yes: function (index) {                            
                            layer.close(index)
                        },
                        btn2: function () {                            
                            bindShopFunc();
                        },  
                    }); 
                }
                else if (pt == "AlibabaC2M" || pt == "TaobaoMaiCaiV2") {
                    var title = pt == "AlibabaC2M" ? "淘工厂授权注意事项" : "淘宝买菜授权注意事项";
                    var html = '<div style="font-family: Source Han Sans;font-size: 14px;color: rgba(31, 35, 41)">';
                    html += '<div>'
                    html += '<p  class="wu-color-b">官方通知：为提升系统服务能力及操作体验，淘宝买菜将于2025年08月20日起对商家工作台进行全方位升级，升级后的淘宝买菜平台将和淘工厂共用授权方式和相关业务接口。</p>'
                    html += '</div>'
                    html += '<div style="margin-top: 16px;">'
                    html += '<p style="font-weight: 600;">注意事项(淘宝买菜商家重点关注！！！)：</p>'
                    html += '<p>点击下方【我已知晓，继续授权】后，会跳转至店铺授权页，需注意:选择二级供应商时，请选择与您授权平台一致的二级供应商，避免出现系统内显示平台与店铺实际平台不一致的情况。如果已经授权错误，可以解绑店铺后重新选择正确的平台/二级供应商重新授权进系统。</p>'
                    html += '</div>'
                    html += '</div>';
                    layer.confirm(html, {
                        area: ["680px", "auto"],
                        maxHeight: 500,
                        title: title,
                        btn: ['取消', '我已知晓，继续授权'],
                        skin: 'wu-dailog',
                        success: function (layero) {
                            $(".layui-layer-title", layero).css({ "color": "#333", "font-weight": "550" })
                            $(".layui-layer-btn0", layero).css({ "background-color": "#FFF", "color": "#000", "border-color": "#dedede" })
                            $(".layui-layer-btn1", layero).css({ "background-color": "#0888ff", "color": "#FFF", "border-color": "#0888ff" })
                        },
                        yes: function (index) {
                            layer.close(index)
                        },
                        btn2: function () {
                            bindShopFunc();
                        },
                    }); 
                }
                else {
                    bindShopFunc();
                }
            },
            cancel: function () {

            }
        });

    }

    module.IsDisabled = function () {
        layer.confirm('试用的额度已经用完!', {
            title: '提示',
            btn: ['确定'],
            skin: 'wu-dailog'
        }, function () {
            layer.closeAll();
        });
    }

    //重新关联 or 授权
    module.ReBinding = function (id, authurl) {
        var model = shops[id];
        var pt = model.PlatformType;
        var platform = null;
        for (var i = 0; i < supportPlatforms.length; i++) {
            if (supportPlatforms[i].PlatformType == pt) {
                platform = supportPlatforms[i];
            }
        }
        if (platform.IsAuthUrl) {
            var token = module.GetQueryString("token");
            if (authurl != undefined && authurl != "null" && authurl != "") {
                commmon.OpenNewTab(authurl + "&rp=" + token);
            } else {
                commmon.OpenNewTab(platform.AuthUrl + "&rp=" + token);
            }
            var isAddOk = layer.confirm('是否授权成功？', { icon: 3, title: '授权结果确认', btn: ['确定', '取消'], skin: 'wu-dailog' },
                function () {
                    module.Init(false);
                    layer.closeAll();
                }, function () {
                    module.Init(false);
                }
            );
        } else {
            layer.msg("该平台不支持重新授权，请从店铺后台进入刷新授权/解除店铺再操作绑定")
            ////弹框授权
            //if (platform.PlatformType == "WxXiaoShangDian") {
            //    var ui = layer.prompt({
            //        id: "layui-layer-auth-prompt",
            //        formType: 0,
            //        title: '输入小程序ID',
            //        success: function () {
            //            $("#layui-layer-auth-prompt").prepend("<span>例: wxf414f0****1845</span>");
            //        }
            //    }, function (value, index, elem) {
            //        var reg = /^wx[0-9a-zA-Z]+$/;
            //        if (reg.test(value) != true) {
            //            layer.msg("输入正确的小程序ID", { icon: 2 });
            //            return;
            //        }
            //        commmon.Ajax({
            //            url: "/Partner/AddShop",
            //            type: "POST",
            //            data: { pt: platform.PlatformType, shopId: value },
            //            loading: true,
            //            success: function (rsp) {
            //                if (commonModule.IsError(rsp)) {
            //                    return;
            //                }
            //                module.Init(false);
            //                layer.close(ui);
            //            }
            //        });
            //    });
            //}
        }
    }

    module.ReBindingTouTiao = function (authurl) {
        var token = module.GetQueryString("token");
        commmon.OpenNewTab(authurl + "&rp=" + token);
        var isAddOk = layer.confirm('是否授权成功？', { icon: 3, title: '授权结果确认', btn: ['确定', '取消'], skin: 'wu-dailog' },
            function () {
                module.Init(false);
                layer.closeAll();
            }, function () {
                module.Init(false);
            }
        );
    }
    module.GetQueryString = function (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    module.edmitHeliang = function (appid, shopName) {
        var html = "";
        html += '<div class="wu-flex wu-yCenter wu-f14 wu-c06">';
        html += '<div style="width: 90px;text-align: right;"><i class="wu-color-b wu-mR2">*</i>店铺名称：</div>';
        html += '<div class="wu-inputWrap wu-form-mid" style="flex: 1;">';
        html += '<input class="wu-input" type="text" id="edmitHeliangShopName" value="' + shopName + '" placeholder="请输入新的店铺名称" onblur="wuFormModule.blurInput(this)" />';
        html += '<span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>';
        html += '</div>';
        html += '</div>';
        html += '<div class="wu-mT24 wu-flex wu-yCenter wu-f14 wu-c06">';
        html += '<div style="width: 90px;text-align: right;">AppSecret：</div>';
        html += '<div class="wu-inputWrap wu-form-mid" style="flex: 1;">';
        html += '<input class="wu-input" type="text" id="edmitHeliangAppSecret" placeholder="请输入新的APPSECRET" onblur="wuFormModule.blurInput(this)" />';
        html += '<span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>';
        html += '</div>';
        html += '</div>';
        layer.open({
            type: 1,
            title: "【" + shopName +"】授权调整",
            content: html,
            area: '420px', //宽高
            skin: 'wu-dailog',
            btn: ['保存', '取消'],
            yes: function () {
                var appSecret = $("#edmitHeliangAppSecret").val() || "";
                //if (appSecret == "") {
                //    layer.confirm("输入APPSECRET不能为空");
                //    return;
                //}
                var name = $("#edmitHeliangShopName").val() || "";
                if (name == "") {
                    layer.confirm("输入店铺名称不能为空", { skin: 'wu-dailog' });
                    return;
                }
                if (name.length > 32) {
                    layer.msg("店铺名称不能超过32个字符", { icon: 2 });
                    return;
                }
                commonModule.Ajax({
                    type: "POST",
                    url: "/Partner/AddShopByOtherPlatforms",
                    data: { pt: "Other_Heliang", shopName: name, appId: appid, appSecret: appSecret, isBind: false },
                    success: function (rsp) {
                        if (rsp.Success == false) {
                            layer.alert(rsp.Message, { icon: 2, skin: 'wu-dailog' });
                            return;
                        }
                        module.LoadList(false);
                        layer.msg("更新成功");
                        layer.closeAll();
                    }
                });
            },
            cancel: function () {
            }
        });
    }
    module.edmitJuHaoMai = function (appid, shopName) {
        var html = "";
        html += '<div class="edmitWrap" style="padding-bottom: 0px;">'
        html += '<span>AppSecret：</span>';
        html += '<input type="text" id="edmitJuHaoMaiAppSecret" value="" placeholder="请输入新的APPSECRET">';
        html += '</div>';
        layer.open({
            type: 1,
            title: "【" + shopName +"】授权调整",
            content: html,
            area: ['350'], //宽高
            btn: ['保存', '取消'],
            yes: function () {
                var appSecret = $("#edmitJuHaoMaiAppSecret").val() || "";
                if (appSecret == "") {
                    layer.confirm("AppSecret不能为空", { skin: 'wu-dailog' });
                    return;
                }
                commonModule.Ajax({
                    type: "POST",
                    url: "/Partner/AddShopByOtherPlatforms",
                    data: { pt: "Other_JuHaoMai", shopName: '', appId: appid, appSecret: appSecret, isBind: false },
                    success: function (rsp) {
                        if (rsp.Success == false) {
                            layer.alert(rsp.Message, { icon: 2, skin: 'wu-dailog' });
                            return;
                        }
                        module.LoadList(false);
                        layer.msg("更新成功");
                        layer.closeAll();
                    }
                });
            },
            cancel: function () {
            }
        });
    }
    module.edmitHaoYouDuo = function (appid, shopName) {
        var html = "";
        html += '<div class="edmitWrap" style="padding-bottom: 0px;">'
        html += '<span>AppSecret：</span>';
        html += '<input type="text" id="edmitHaoYouDuoAppSecret" value="" placeholder="请输入新的APPSECRET">';
        html += '</div>';
        layer.open({
            type: 1,
            title: "【" + shopName + "】授权调整",
            content: html,
            area: ['350'], //宽高
            btn: ['保存', '取消'],
            yes: function () {
                var appSecret = $("#edmitHaoYouDuoAppSecret").val() || "";
                if (appSecret == "") {
                    layer.confirm("AppSecret不能为空", { skin: 'wu-dailog' });
                    return;
                }
                commonModule.Ajax({
                    type: "POST",
                    url: "/Partner/AddShopByOtherPlatforms",
                    data: { pt: "Other_HaoYouDuo", shopName: '', appId: appid, appSecret: appSecret, isBind: false },
                    success: function (rsp) {
                        if (rsp.Success == false) {
                            layer.alert(rsp.Message, { icon: 2, skin: 'wu-dailog' });
                            return;
                        }
                        module.LoadList(false);
                        layer.msg("更新成功");
                        layer.closeAll();
                    }
                });
            },
            cancel: function () {
            }
        });
    }
    module.edmitNickName = function (id, nickName) {
        var html = "";
        html += '<div class="wu-flex wu-yCenter wu-f14 wu-c06">';
        html += '<div><i class="wu-color-b wu-mR2">*</i>虚拟店铺名称：</div>';
        html += '<div class="wu-inputWrap wu-form-mid" style="flex: 1;">';
        html += '<input class="wu-input" type="text" id="edmitNickName" value="' + nickName + '" placeholder="请输入虚拟店铺名称" onblur="wuFormModule.blurInput(this)" style="padding-right: 26px;" />';
        html += '<span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>';
        html += '</div>';
        html += '</div>';
        layer.open({
            type: 1,
            title: "编辑店铺名称", //不显示标题
            content: html,
            skin: 'wu-dailog',
            area: '360px', //宽高
            btn: ['保存', '取消'],
            move: false,
            yes: function () {
                var name = $("#edmitNickName").val() || "";
                if (name == "") {
                    layer.confirm("输入虚拟店铺名称不能为空", { skin: 'wu-dailog' });
                    return;
                }
                commonModule.Ajax({
                    type: "POST",
                    url: "/NewOrder/EditVirtualShop",
                    data: { id: id, name: name },
                    success: function (rsp) {
                        if (rsp.Success) {
                            layer.msg("更新成功");
                            $("#tr-fs-" + id + " .NickName").text(name);
                            layer.closeAll();
                        }
                        else {
                            layer.confirm(rsp.Message, { skin: 'wu-dailog' });
                        }
                    },
                    error: function (rsp) {
                        if (rsp.status == 401) {
                            layer.msg("暂无权限，请联系管理员");
                        } else {
                            layer.msg(rsp.Message);
                            console.error(rsp.Message);
                        }
                    }
                });
            },
            cancel: function () {

            }
        });

    }
    module.scrollHeight = function () {
        $("#support_platform_helpShow").closest(".layui-layer-content").animate({ scrollTop: 1500 }, "slow"); //定位tr 
    }
    var showChangePlatformWarnDailog = null;
    module.showChangePlatformWarn = function () {

        showChangePlatformWarnDailog = layer.open({
            type: 1,
            title: false, //不显示标题
            content: '<div class="changePlatformWarn"><span onclick="partnerModule.closeChangePlatformWarn()" class="changePlatformWarn-icon"></span><span  onclick="partnerModule.closeChangePlatformWarn()" class="changePlatformWarn-icon" style="left:763px;"></span><img src="/Content/images/noviceIntroPic/ssqhtz-2023-1-9.png" /></div>',
            area: '900px', //宽高
            closeBtn: 0
        });
    }
    module.closeChangePlatformWarn = function () {
        layer.close(showChangePlatformWarnDailog);
    }
    var showTouTiaoChangePlatformDailog = null;
    module.showTouTiaoChangePlatformDailog = function (type) {
        //return;
        var html = '';
        html += '<div class="changePlatformWarn">';
        html += '<span class="changePlatformWarn-icon" onclick="partnerModule.closeTouTiaoChangePlatformWarn()"></span>';
        html += '<span class="changePlatformWarn-icon" style="left:763px;" onclick="partnerModule.closeTouTiaoChangePlatformWarn()"></span>';
        html += '<span class="changePlatformWarn-icon-01"  onclick=\'partnerModule.goToTouTiaoAuthModel("TouTiao")\'></span>';
        html += '<span class="changePlatformWarn-icons-02" onclick=\'partnerModule.setNoShowTouTiaoChangePlatformWarn("' + type + '")\'></span>';
        html += '<img src="/Content/images/noviceIntroPic/ssqhtz-2023-1-9-01.png" />'
        html += '</div>';
        showTouTiaoChangePlatformDailog = layer.open({
            type: 1,
            title: false, //不显示标题
            content: html,
            area: '920px', //宽高
            zIndex: 99999999,
            closeBtn: 0
        });
    }

    module.closeTouTiaoChangePlatformWarn = function () {
        layer.closeAll();
    }

    module.setNoShowTouTiaoChangePlatformWarn = function (type) {
        commonModule.SaveCommonSetting(type, "1", function (rsp) {
            layer.close(showTouTiaoChangePlatformDailog);
        }); //关闭后不展示
    }
    module.goToTouTiaoAuthModel = function (platform) {
        var toggle_id = "#togglePlatform_content";
        if (platform == 'Alibaba') {
            toggle_id = "#togglePlatform_content_alibaba";
        }
        if (platform == 'KuaiShou') {
            toggle_id = "#togglePlatform_content_kuaishou";
        }
        var $togglePlatforms = $(toggle_id + ">.togglePlatform-content-item");
        if ($togglePlatforms.length > 0) {
            // 多应用弹窗选择触发确认按钮
            $togglePlatforms.addClass("active");
            $(toggle_id).closest('.layui-layer').find('.layui-layer-btn0').click();
        }
        else {
            // 平台弹窗选中平台触发确认按钮
            layer.closeAll();
            $(toggle_id).closest('.layui-layer').find('.layui-layer-btn0').click();
        }
    }

    // 申请解绑
    module.SetUnBind = function (Id, platformType, AppKey, PlatformTypeName) {
        if (platformType == "Alibaba") {
            //前置检查，若存在已付款待发货的预付款订单，弹窗
            commonModule.Ajax({
                type: "POST",
                url: "/Partner/Check1688UnBindShop",
                data: { Id: Id },
                async: false,
                success: function (rsp) {
                    if (rsp.Success) {
                        layer.closeAll();
                        module.SetUnBindStep2(Id, AppKey, PlatformTypeName);
                    }
                    else {
                        layer.open({
                            type: 1,
                            title: "解绑店铺提示",
                            content: "<div style='display:flex;flex-direction:column;align-items:center;font-size:14px;'><span style='margin-bottom:10px'>您的账户当前存在待处理订单，为保证订单发货正常，</span><span style='margin-bottom:10px'>请处理完并发货订单后，再进行解绑操作。</span><a class='dColor' href=\"" + commonModule.rewriteTopUrl('/Common/Page/NewOrder-AllOrder') + "\" >查看异常明细》</a></div>",
                            area: '560px', //宽高
                            skin: 'wu-dailog',
                        });
                    }
                }
            });
            return;
        }
        module.SetUnBindStep2(Id, AppKey, PlatformTypeName);
    }

    // 解绑店铺
    function unboundShopFn(id, appKey) {
        var paramsData = { Id: id, AppKey: appKey };
        commonModule.Ajax({
            type: "POST",
            url: "/Partner/SetUnBindShop",
            data: paramsData,
            success: function (rsp) {
                if (rsp.Success) {
                    layer.closeAll();
                    commonModule.w_alert({ type: 4, content: '申请成功' });
                    module.LoadList(false);
                } else {
                    layer.confirm(rsp.Message, { skin: 'wu-dailog' });
                }
            }
        });
    }

    module.SetUnBindStep2 = function (Id, AppKey, PlatformTypeName) {
        var html = "";
        html += '<div class="wu-f14 wu-c09">';
        html += '<div>解绑店铺会删除该店铺在店管家系统所有的历史代发交易数据，请提前备份</div>';
        html += '<div class="wu-c06 wu-mT8">普通解绑：申请后将在每日23:00统一处理解绑店铺</div>';
        html += '<div class="wu-c06">紧急解绑：进行身份验证，并在通过后立即解绑</div>';
        html += '</div>';
        layer.open({
            type: 1,
            title: '解绑店铺提示',
            content: html,
            offset: 'auto',
            area: '560px',
            btn: ['普通解绑', '紧急解绑', '取消解绑'],
            move: false,
            skin: 'wu-dailog untieShopTipDailog',
            success: function (layero, index, that) {
                $('.untieShopTipDailog .layui-layer-btn1').css({
                    backgroundColor: '#ea572e',
                    color: '#FFFFFF',
                    border: '1px solid #ea572e'
                });
            },
            btn1: function () {
                unboundShopFn(Id, AppKey);
            },
            btn2: function () {
                module.SetUrgentUnBind(Id, PlatformTypeName, AppKey);
            },
            btn3: function (index) {
                layer.close(index); // 关闭弹层
            },
        });
        /**
           var html = "";
            html += '<div class="setUnBindDailog">'
            html += '<span>解绑店铺本身违反正常使用流程,</span>';
            html += '<span style="color:#FF511C">解绑店铺会清除该店铺所有的历史数据，</span>';
            html += '<span>为了不影响系统性能及用户使用，</span>';
            html += '<div>暂定<span style="color:#FF511C">每日晚间十一点</span>统一处理解绑工作，</div>';
            html += '<span>申请解绑时请勿操作订单相关数据</span>';
            html += '</div>';
            layer.open({
                type: 1,
                title: "解绑店铺提示", //不显示标题
                content: html,
                area: '600px', //宽高
                skin:'setUnBindDailogSkin',
                btn: ['确认申请解绑', '取消解绑'],
                yes: function () {
                    commonModule.Ajax({
                        type: "POST",
                        url: "/Partner/SetUnBindShop",
                        data: { Id: id },
                        success: function (rsp) {
                            if (rsp.Success) {
                                layer.closeAll();
                                layer.msg("申请成功");
                                module.LoadList(false);
                            }
                            else {
                                layer.confirm(rsp.Message);
                        }
                    },
                    error: function (rsp) {
                        if (rsp.status == 401) {
                            layer.msg("暂无权限，请联系管理员");
                        } else {
                            layer.msg(rsp.Message);
                            console.error(rsp.Message);
                            }
                        }
                    });
                },
                cancel: function () {

                }
            });
         */
    }
    
    // 紧急解绑
    module.SetUrgentUnBind = function (id, PlatformTypeName, AppKey) {
        unBindShopid = id;
        platformTypeName = PlatformTypeName;
        appKey = AppKey;
        setUrgentUnBindLayer = layer.open({
            type: 1,
            title: "解绑店铺提示",
            content: $('#emergency_unbinding'),
            area: '500px', //宽高
            skin: 'wu-dailog unbind-shop-tip-dailog',
            success: function () {
                $(".unbind-shop-tip-dailog .layui-layer-content").css({
                    padding: '0px',
                    borderRadius: '0 0 10px 10px',
                });
                module.validCode()
            },
            end: function () {
                $('#loginTelePhone').val('');
                $('#loginPassword').val('');
                $('#loginValidCode').val('');
                $('#intoRegisterTelePhone').val('');
                $('#intoRegisterValidCode').val('');
            }
        });
    }
   
    // 紧急解绑 - 更新验证码
    module.validCode = function () {
        var timestamp = Date.parse(new Date())
        $("#vCodeId").attr('src', '/FxAccount/ValidCode?t=' + timestamp + "&token=" + commonModule.getToken());
    }
    // 紧急解绑 - 密码小眼睛
    module.changeShowPassType = function (type, isThis) {
        if ($(isThis).hasClass("close")) {
            $("#" + type).attr('type','text');
            $(isThis).removeClass("close")
        } else {
            $("#" + type).attr('type', 'password');
            $(isThis).addClass("close")
        }
    }
  
    function settime() {
        if (count == 0) {
            $("#LoginCode1").html("发送验证码");
            count = 120;
            return;
        } else {
            $("#LoginCode1").html("重新发送(" + count + ")");
            count--;
        }
        setTimeout(function () { settime() }, 1000)
    }
    // 紧急解绑 - 发送短信
    module.postMobileMessageCode = function (types) {
        var phone = "";
        if (types == "1")
            phone = $("#intoRegisterTelePhone").val();
        if (types == "2")
            phone = $("#againSetPhone").val();
        if (!checkPhone(phone)) { //检测手机号
            layer.msg("手机号码有误,由数字11位组成，请重新输入");
            return;
        }
        if (count < 120 && count > 0)
            return;
        $.ajax({
            type: 'post',
            url: '/FxAccount/PostMobileMessageCode',
            data: { phone: phone, types: 2, token: commonModule.getToken() },
            success: function (rsp) {
                if (rsp.Success) {
                    layer.msg(rsp.Message || '发送成功');
                    settime();
                    isSmsVerification = true;
                }
                else {
                    layer.msg(rsp.Message || '失败');
                    
                }
            }
        });
    }

    function showErrorMessage(message, isTrue) {
        $('#loginWrap_content_warn').css({
            display: 'flex'
        }).children('span').text(message);

        timeoutsss = setTimeout(function () {
            $('#loginWrap_content_warn').hide(300)
        }, 5000)

        if (isTrue) {
            $(".messageShade .messageShade_icon").css({ backgroundPosition: '-30px 0' });
        } else {
            $(".messageShade .messageShade_icon").css({ backgroundPosition: '-60px 0' });
        }

    }

    function checkPhone(str) { //匹配手机号，正确返回true 错误返回false
        if (!(/^1(3|4|5|6|7|8|9)\d{9}$/.test(str))) {
            return false;
        }
        return true;
    }
    function checkPwd(pwd) { //匹配密码，正确返回true 错误返回false
        var typeCount = 0;
        if (pwd.length < 6) {
            return false;
        }
        if (/[a-z]/.test(pwd)) { //有小写
            typeCount++;
        }
        if (/[A-Z]/.test(pwd)) { //有大写
            typeCount++;
        }
        if (/[0-9]/.test(pwd)) { //有数字
            typeCount++;
        }
        // 兼容旧密码 暂不判断特殊符号相关

        if (typeCount >= 2)
            return true;
        else return false;

        /*var reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,}$/;
        if (!reg.test(str)) {
            return false;
        }
        return true;*/
    }
    // 紧急解绑 - 验证
    module.emergencyUnbinding = function (types) {
        var mobile = types ? $("#intoRegisterTelePhone").val() :$("#loginTelePhone").val();
        var password = $("#loginPassword").val();
        var validCode = types ? $("#intoRegisterValidCode").val() : $("#loginValidCode").val()

        if (!checkPhone(mobile)) { //检测手机号
            layer.msg("手机号码有误,由数字11位组成，请重新输入");
            return;
        }
        if (!checkPwd(password) && !types) { //检测密码
            layer.msg("密码长度要大于6位，由数字、字母或特殊符号组成");
            return;
        }
        if (!validCode) {
            layer.msg("短信验证码不能为空");
            return;
        }
        if (!isSmsVerification && types) {
            layer.msg("请获取验证码");
            return;
        }
       
        var data = {
            "model": {
                Mobile: mobile,
                Password: password,
                "ValidCode": validCode,
                token: commonModule.getToken()
            },
            "isSimpleLogin": types ? true :false // 如果使用验证码，则为true，否则为null或者false
        }
        var url = "/Partner/CheckAccount";
        commonModule.Ajax({
            type: "POST",
            url: url,
            data: data,
            success: function (rsp) {
                if (rsp.Success) {
                    commonModule.Ajax({
                        type: "POST",
                        url: "/Partner/SetUnBindShop",
                        data: { Id: unBindShopid, platform: platformTypeName, IsUrgent: true, appKey: appKey },
                        success: function (rsp) {
                            if (rsp.Success) {
                                layer.closeAll();
                                commonModule.w_alert({ type: 4, content: '解绑成功' });
                                layer.close(setUrgentUnBindLayer);
                                module.LoadList(false)
                            } else {
                                layer.confirm(rsp.Message, { skin: 'wu-dailog' });
                            }
                        }
                    });
                }
                else {
                    commonModule.w_alert({ type: 2, content: rsp.Message || '失败' });
                    module.validCode();
                }
            }
        });
    }
   
    module.CancelUnBind = function (id, appKey) {
        var html = "";
        html += '<div class="wu-flex">';
        //html += '<span class="my-layer-ico" style="background-position: -90px 0;"></span>';
        html += '<i class="iconfont icon-a-help-circle-filled1x wu-color-c" style="font-size: 28px;"></i>';
        html += '<div class="wu-f14 wu-c09 wu-mL4">';
        html += '<div>每日晚间十一点前允许取消解绑申请</br></div>';
        html += '<div class="wu-mT4">是否确认取消解绑申请？</div>';
        html += '</div>';
        html += '</div>';
        layer.open({
            type: 1,
            title: "取消解绑店铺", //不显示标题
            content: html,
            area: '460px', //宽高
            skin: 'wu-dailog',
            btn: ['确认取消申请', '取消'],
            yes: function () {
                commonModule.Ajax({
                    type: "POST",
                    url: "/Partner/CancelUnBindShop",
                    data: { Id: id, AppKey: appKey },
                    success: function (rsp) {
                        if (rsp.Success) {
                            layer.closeAll();
                            layer.msg("取消申请成功");
                            module.LoadList(false);
                        }
                        else {
                            layer.confirm(rsp.Message, { skin: 'wu-dailog' });
                        }
                    }
                });
            },
            cancel: function () {

            }
        });
    }

    var handSyncExpireTimeClick = false;
    module.HandSyncExpireTime = function (index, _this) {
        if (index != undefined) {
            handSyncExpireTimeClick = true;
            if (handSyncExpireTimeClick) {
                var model = shops[index];
                commmon.Ajax({
                    url: "/Partner/HandSyncExpireTime",
                    type: "POST",
                    data: { shopId: model.ShopId, appKey: model.AppKey },
                    loading: true,
                    success: function (rsp) {
                        handSyncExpireTimeClick = false;
                        if (commmon.IsError(rsp)) {
                            return;
                        } else {
                            $(_this).prev('span.td-fs-expireTime').text(rsp.Data);
                        }
                    },
                    error: function (e) {
                        handSyncExpireTimeClick = false;
                    }
                });
            }
        }
    }

    module.orderbyColl = function (orderby) {
        if (orderby == undefined) {
            orderby = 0;
        }
        if (orderby == 1) {
            $("#AuthAscArrow").addClass("active");
            $("#AuthDescArrow").removeClass("active");
        } else if (orderby == 2) {
            $("#AuthAscArrow").removeClass("active");
            $("#AuthDescArrow").addClass("active");
        }
        reqModel.AuthTimeOrderBy = orderby;
        module.LoadList(true);
    }
    // 订购提示
    module.OrderingTips = function (IsFxListingApp, PlatformType) {
        console.log("======IsFxListingApp====", typeof IsFxListingApp, typeof PlatformType);
        if (PlatformType !== "TouTiao") {
            return;
        }
        if (IsFxListingApp) {
            commonModule.orderingTips({
                content: "您当前的店铺暂未订购店管家选品上货应用，无法开启铺货权限，如需铺货功能，请前往服务市场订购店管家选品上货。"
            });
        } else {
            commonModule.orderingTips({
                content: "您当前的店铺暂未订购店管家分销代发，无法开启订单同步，自动推单，打单发货，单号回流等能力。"
            });
        }
    }
    return module;
}(partnerModule || {}, commonModule, jQuery, layer));