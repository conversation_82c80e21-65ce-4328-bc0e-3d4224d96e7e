/// <reference path="CommonModule.js" />
/// <reference path="/Scripts/orderlist/OrderTableBuilder.js" />
/// <reference path="/Scripts/orderlist/AddTemplateInOrderListModule.js" />

var sendLogistic = (function (sl, common, tmpModule, otb, waitOrderModule, $) {
    sl.IsSending = false;
    sl.LockButton = null;
    sl.IsReSending = false;
    sl.IngoreExReceiverChangeOrder = false;
    // 标记变量，用于防止淘宝顺丰包邮检查的递归调用
    var skipTaobaoSfFreeShippingCheck = false;

    sl.BatchSendOrder = function (isDontNeedLogistic, orderId, isNotConfirm, printSuccessOrderIds, checkOrders, isFromMutilSend, sendAway) {
        var orders = orders = orderTableBuilder.getSelections();

        // 校验：抖店部分发货的订单，不能用已发过货的订单进行整单发货
        console.log("BatchSendOrder==========", orders);

        for (var i = 0; i < orders.length; i++) {
            var row = orderTableBuilder.rows[orders[i].Index];

            if (commonModule.WaitStatusSendPts.indexOf(row.PlatformType) != -1 && row.ErpState == "waitsellersend" && row.WaybillCodes) {
                var isPartSend = false;
                for (var j = 0; j < row.SubOrders.length; j++) {
                    var sub = row.SubOrders[j];
                    if (sub.SendedCount>0 && sub.Count > sub.SendedCount) {
                        isPartSend = true;
                        break;
                    }
                }
                if (isPartSend)
                {
                    var tmpOrder = JSON.parse(JSON.stringify(row));
                    tmpOrder.WaybillCodes = [];
                    tmpOrder.SubOrders = [];
                    for (var k = 0; k < row.WaybillCodes.length; k++) {
                        if (commonModule.IsRepairSendHistory || row.WaybillCodes[k].Status == undefined || row.WaybillCodes[k].Status == 1)
                            tmpOrder.WaybillCodes.push(row.WaybillCodes[k]);
                    }
                    if (tmpOrder.WaybillCodes.length <= 0) {// 所有单号已上传过
                        var ptName = commonModule.PlatformNameDic[row.PlatformType] || "";
                        layer.confirm(ptName + "平台的订单【" + row.PlatformOrderId + "】是部分发货订单且所有单号都已上传过，请为此订单打印新单号后再操作发货", { skin: 'wu-dailog' });
                        return;
                    }
                }
            }
        }

        if (sendAway == undefined) {
            var manyCodeSendAway = "/ErpWeb/SetInfo/ManyCodeSendAway";
            var manyCodeSendConfig = "/ErpWeb/SetInfo/ManyCodeSendConfig";
            commonModule.Ajax({
                url: "/Common/LoadCommonSetting",
                data: { settingKey: manyCodeSendAway },
                type: "POST",
                async: false,
                success: function (rsp) {
                    if (rsp.Success) {
                        commonModule.ManyCodeSendAway = rsp.Data || "";
                        sendAway = commonModule.ManyCodeSendAway; 

                        // 开启多单号回传配置
                        if (rsp.Data == "0") {
                            commonModule.Ajax({
                                url: "/Common/LoadCommonSetting",
                                data: { settingKey: manyCodeSendConfig },
                                async: false,
                                type: "POST",
                                success: function (res) {
                                    if (res.Success && res.Data) {
                                        commonModule.manyCodeSendConfigData = JSON.parse(res.Data);
                                    }
                                }
                            });
                        }
                    }
                }
            });
        }

        // 混合多运单号发货处理方式：0：先发单个单号，再弹窗发多运单号，1：全部使用新单号发货，空：每次弹窗确认
        if (sendAway == "1") {
            sl.send(isDontNeedLogistic, orderId, isNotConfirm, printSuccessOrderIds, checkOrders, isFromMutilSend);
        }
        else {
            if (sendAway == "0") {
                sl.send(isDontNeedLogistic, orderId, isNotConfirm, printSuccessOrderIds, checkOrders, isFromMutilSend);
            } else {
                var waitStatusOrders = [];

                for (var i = 0; i < orders.length; i++) {
                    var row = orderTableBuilder.rows[orders[i].Index];

                    var waybillCodes = [];
                    // 待发货状态上传多包裹发货平台
                    if (commonModule.WaitStatusSendPts.indexOf(row.PlatformType) != -1 && row.ErpState == "waitsellersend" && row.WaybillCodes && row.WaybillCodes.length > 1) {
                        for (var j = 0; j < row.WaybillCodes.length; j++) {
                            if (row.WaybillCodes[j].Status == undefined || row.WaybillCodes[j].Status == 1) {
                                waybillCodes.push(row.WaybillCodes[j]);
                            }
                        }
                        if (waybillCodes.length > 1)
                            waitStatusOrders.push(orders[i]);
                    }
                    // 已发货状态上传多包裹发货平台 
                    else if (commonModule.SendStatusSendPts.indexOf(row.PlatformType) != -1 && row.ErpState == "sended" && row.WaybillCodes && row.WaybillCodes.length > 1) {
                        for (var j = 0; j < row.WaybillCodes.length; j++) {
                            if (row.WaybillCodes[j].Status == undefined || row.WaybillCodes[j].Status == 1) {
                                waybillCodes.push(row.WaybillCodes[j]);
                            }
                        }
                        if (waybillCodes.length > 1)
                            waitStatusOrders.push(orders[i]);
                    }
                }

                if (waitStatusOrders.length == 0) {
                    sl.send(isDontNeedLogistic, orderId, isNotConfirm, printSuccessOrderIds, checkOrders, isFromMutilSend);
                } else {
                    waitOrderModule.RenderConfirmDialog(orders);
                }
            }
        }
    }
     //勾选/取消【本周内不再提醒】按钮
    sl.switchTaobaoPreSaleTip = function (event) {
        event.stopPropagation()

        if (document.getElementById("skipTopBtn").checked == true) {
            document.getElementById("skipTopBtn").checked = false
        } else {
            document.getElementById("skipTopBtn").checked = true
        }
    }
     sl.check = function (orders, template, isDontNeedLogistic, confirmPrinted, isFromMutilSend, confirmPddDoor, isManyCode) {
        if (!orders || orders.length == 0) {
            if (otb.isSelectOrder()) {
                layer.alert("请选择您要发货的商品", { skin: 'wu-dailog' });
            }
            else {
                layer.alert("请选择您要发货的订单", { skin: 'wu-dailog' });
            }
            return false;
        }
        if (sl.IsSending) {
            layer.alert("当前有发货任务未完成，请稍候再试", { skin: 'wu-dailog' });
            return;
        }
        //检查是否是已经发货了的订单
        var errorCount = 0;
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var index = order.Index;
            var o = otb.rows[index];
            if (o.ErpState != "waitsellersend")
                errorCount++;
            if (o.WaybillCodes && o.WaybillCodes.length > 0) {
                var wc = o.WaybillCodes[0];
                order.WaybillCode = wc.WaybillCode;
                order.ExpressCompanyCode = wc.CompanyCode;
            }
        }
        var pt = orders[0].PlatformType;
        if (errorCount == orders.length && !commonModule.IsRepairSendHistory && !isManyCode) {
            layer.alert("您选择的订单不是待发货订单，请选择待发货订单进行发货。", { btn: ["知道了"], skin: 'wu-dailog' });
            return false;
        }
        if (!isDontNeedLogistic && $("input[name='rdo_print_template']").length == 0) {
            layer.alert('您还没有添加快递模板，请点击<span id="sp_add_template" class="orderList_expressTemplate_addIcon"></span>添加快递模板', { skin: 'wu-dailog' });
            return false;
        }
        if (!isDontNeedLogistic) {
            if (template == null) {
                layer.alert("请选择您要发货的快递模板", { skin: 'wu-dailog' });
                return false;
            }
        }
                
        //return true; 
        var riskCtrlOrder = []; //拼多多风控订单
        var sendSFOrder = []; //拼多多顺丰加价订单
        var unPrintOrder = []; //获取了单号，但是没有打印标记的订单，发货提示 用户
        var pddDoorOrders = [];//拼多多快递上门揽收
        var receiverChangeOrders = [];//地址与上一次打印地址不一致订单
        var sfFreeShippingLogicOrderIds = [];//顺丰包邮的订单但未使用顺丰
        var tbSfFreeShippingLogicOrderIds = [];//淘宝顺丰包邮/上门的订单但未使用顺丰
        var taobaoPreSaleOrdersIds = []; //淘宝距离最晚发货时间大于48小时的预售订单
        var taobaoSoonSendOrderIds = []; //淘宝付款时间不足3小时的订单
        var selectedOrderWithSubCount = 0;
        var toutiaoSFLogisticsOrderLogicOrderIds = []; // 顺丰配送订单id
        var toutiaoJDLogisticsOrderLogicOrderIds = []; // 京东配送订单id

        // 获取勾选的商品编码
        var $checkedList = $(".productShow-itemInput.orderitem-chx:checked");
        var CheckedProductIdList = [];
        $checkedList.each(function (i, item) {
            var $productId = $(this).attr("data-id");
            CheckedProductIdList.push($productId);
        });
        // 查找并处理符合条件的SubOrder
        function processSubOrders(order, tags, subOrders, checkedProductIds, logisticsData, logisticsOrderLogicIds) {
            tags.forEach(function (tag) {
                var matchSubOrder = subOrders.find(function (subOrder) {
                    return subOrder.OrderItemCode === tag.OiCode;
                });
                if (matchSubOrder && checkedProductIds.includes(matchSubOrder.OrderItemId.toString())) {
                    logisticsData.push(matchSubOrder); // 添加符合条件的SubOrder
                }
            });
            if (logisticsData.length > 0) {
                logisticsOrderLogicIds.push(order.LogicOrderId);
            }
        }
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var row = otb.rows[order.Index];
            var buyerName = order.Buyer.BuyerWangWang;
            if (!buyerName || buyerName == "")
                buyerName = order.Receiver.ToName;
            if (order.PlatformType == "Pinduoduo" && order.IsWeiGong == true) {
                riskCtrlOrder.push(order.PlatformOrderId);
            }

            if (order.PlatformType == "Pinduoduo" && order.ExtField1 == "1") {
                sendSFOrder.push(order.PlatformOrderId);
            }

            if (!row.PrintState) {
                unPrintOrder.push(order.PlatformOrderId);
            }

            if (order.OrderItems == null || order.OrderItems.length <= 0) {
                layer.alert("第【" + (order.Index + 1) + "】行买家【" + buyerName + "】的订单未选择任何商品，请展开订单勾选需要发货的商品。", { skin: 'wu-dailog' });
                return false;
            }
            if (!isDontNeedLogistic && (!order.WaybillCode || order.WaybillCode.trim() == "")) {
                layer.alert("第【" + (order.Index + 1) + "】行买家【" + buyerName + "】的快递单号不能为空，请先打印快递单，再进行发货", { skin: 'wu-dailog' });
                return false;
            }
            if (order.PlatformType == "Pinduoduo" && order.IsPddCourierDoorToDoorCollect) {
                pddDoorOrders.push(order.PlatformOrderId);
            }
            if (order.ReceiverIsChange) {
                receiverChangeOrders.push(order);
            }
            
            //非“上传多运单号”时检查顺丰包邮
            if ((isFromMutilSend == undefined || isFromMutilSend != 1) && order.ExpressCompanyCode != "SF" && sl.IsExistSfFreeShippingOrder(order) ) {
                sfFreeShippingLogicOrderIds.push(order.LogicOrderId);
            }

            // 淘宝检查顺丰包邮和顺丰上门
            if (order.PlatformType === "Taobao" && order.ExpressCompanyCode !== "SF" && sl.IsExistSfFreeShippingOrder(order, true)) {
                tbSfFreeShippingLogicOrderIds.push(order.LogicOrderId);
            }
            //淘宝判断现货订单，预售订单发货提示
            if (row.PlatformType == "Taobao" && commonModule.HasTag(row.OrderTags, "TaoBaoPreOrder", "Order")) {
                for (var j = 0; j < row.SubOrders.length; j++) {
                    var subOrder = row.SubOrders[j];
                    if (subOrder.checked != true) {
                        continue;
                    }
                    var isPreSaleOrder = false;
                    //selectedOrderWithSubCount += subOrder.OrderItems.length;
                    selectedOrderWithSubCount += subOrder.OrderItems ? subOrder.OrderItems.length : 1;
                    // 合并 - 订单支付时间
                    if (row.MergeredType == 3 || row.MergeredType == 4) {
                        subOrder.PayTime = row.PayTime
                    }
                    //现货订单判断
                    var payTime = new Date(subOrder.PayTime);
                    var lastShipTime = new Date(subOrder.LastShipTime);
                    //预售订单判断
                    if (lastShipTime > Date.now() && new Date().DateDiff("h", lastShipTime) > 48) {
                        //预售单距离当前时间大于48小时
                        taobaoPreSaleOrdersIds.push(subOrder.PlatformOrderId)
                    }
                    else if (payTime.DateDiff("h", Date.now()) < 3) {
                        //支付之间在3小时内的
                        taobaoSoonSendOrderIds.push(subOrder.PlatformOrderId)
                    }
                }
            }
            // 优质快递服务，顺丰配送和京东配送
            if (order.PlatformType === "TouTiao") {
                if (commonModule.HasTag(order.OrderTags, 'toutiao_high_quality_logistics', 'OrderItem')) {
                    order.SFDeliveryOrderData = [];
                    order.JDDeliveryOrderData = [];
                    var relevantSFTags = order.OrderTags.filter(function (tag) {
                        return tag.Tag === 'toutiao_high_quality_logistics' && tag.TagType === 'OrderItem' && tag.TagValue === 'shunfeng';
                    });
                    var relevantJDTags = order.OrderTags.filter(function (tag) {
                        return tag.Tag === 'toutiao_high_quality_logistics' && tag.TagType === 'OrderItem' && tag.TagValue === 'jd';
                    });
                    // 处理顺丰和京东的SubOrder
                    if (relevantSFTags.length > 0) {
                        processSubOrders(order, relevantSFTags, order.SubOrders, CheckedProductIdList, order.SFDeliveryOrderData, toutiaoSFLogisticsOrderLogicOrderIds);
                    }
                    if (relevantJDTags.length > 0) {
                        processSubOrders(order, relevantJDTags, order.SubOrders, CheckedProductIdList, order.JDDeliveryOrderData, toutiaoJDLogisticsOrderLogicOrderIds);
                    }
                    toutiaoJDLogisticsOrderLogicOrderIds = toutiaoJDLogisticsOrderLogicOrderIds.filter(function (item) {
                        return !toutiaoSFLogisticsOrderLogicOrderIds.includes(item);
                    });
                }
            }
        }

        if (template.ExpressCompanyCode !== "SF" && orders.length == 1 && toutiaoSFLogisticsOrderLogicOrderIds.length == 1 && toutiaoJDLogisticsOrderLogicOrderIds.length == 0) {
            var tips = '当前订单为顺丰配送订单，请使用顺丰快递发货。';
            layer.alert(tips, { title: '提示', area: '420px', skin: 'wu-dailog', }, function (index) {
                layer.close(index);
            });
            return false;
        }
        if (template.ExpressCompanyCode !== 'JD' && orders.length == 1 && toutiaoSFLogisticsOrderLogicOrderIds.length == 0 && toutiaoJDLogisticsOrderLogicOrderIds.length == 1) {
            var tips = '当前订单为京东配送订单，请使用京东快递发货。';
            layer.alert(tips, { title: '提示', area: '420px', skin: 'wu-dailog', }, function (index) {
                layer.close(index);
            });
            return false;
        }
        // 选中的订单中有优质快递订单，同时包含顺丰配送和京东配送，过滤京东配送、顺丰配送订单
        if (orders.length >= 1) {
            if (toutiaoSFLogisticsOrderLogicOrderIds.length > 0 || toutiaoJDLogisticsOrderLogicOrderIds.length > 0) {
                function processOrder() {
                    var mergedList = toutiaoSFLogisticsOrderLogicOrderIds.concat(toutiaoJDLogisticsOrderLogicOrderIds);
                    waitOrderModule.ShowOrderUprate(function () {
                        // 过滤京东配送、顺丰配送订单，继续发货
                        var _newOrders = [];
                        for (var i = 0; i < orders.length; i++) {
                            if (mergedList.indexOf(orders[i].LogicOrderId) == -1) {
                                _newOrders.push(orders[i]);
                            } else {
                                $('#order-' + orders[i].Index).click();
                            }
                        }
                        if (_newOrders.length > 0) {
                            sl.send();
                        } else {
                            layer.msg("当前您所选的订单已被全部过滤");
                        }
                        toutiaoSFLogisticsOrderLogicOrderIds = [];
                        toutiaoJDLogisticsOrderLogicOrderIds = [];
                    }, "toutiao_logistics_sf_and_jd_shipping", {
                        selectedOrderTotalCount: orders.length,
                        logicOrderIdCount: mergedList.length,
                    });
                }
                if (template.ExpressCompanyCode !== "SF" || template.ExpressCompanyCode !== "JD") {
                    processOrder();
                    return false;
                }
            }
        }

        //地址与上一次打印地址不一致订单
        if (receiverChangeOrders.length > 0) {
            cancelWaybillCodeFunc(receiverChangeOrders, false)
            return false;
        }
        if (sfFreeShippingLogicOrderIds.length > 0) {
            waitOrderModule.ShowOrderUprate(function () {
                //过滤异常单继续发货
                var _newOrders = [];
                for (var i = 0; i < orders.length; i++) {
                    if (sfFreeShippingLogicOrderIds.indexOf(orders[i].LogicOrderId) == -1) {
                        _newOrders.push(orders[i]);
                    }
                    else {
                        $('#order-' + orders[i].Index).click();
                    }
                }
                if (_newOrders.length > 0)
                    sl.send();
                else
                    layer.msg("当前您所选的订单已被全部过滤");

            }, "toutiao_sf_free_shipping_for_send", {
                selectOrders: orders,
                logicOrderIds: sfFreeShippingLogicOrderIds
            });
            return false;
        }

        // 淘宝顺丰包邮和上门
        if (!skipTaobaoSfFreeShippingCheck && tbSfFreeShippingLogicOrderIds.length > 0) {
            waitOrderModule.ShowOrderUprate(function (isContinue) {
                if (isContinue){
                    // 不过滤，直接继续，设置跳过标记并发货
                    skipTaobaoSfFreeShippingCheck = true;
                    sl.send();
                    return;
                }
                //过滤异常单继续发货
                var _newOrders = [];
                for (var i = 0; i < orders.length; i++) {
                    if (tbSfFreeShippingLogicOrderIds.indexOf(orders[i].LogicOrderId) == -1) {
                        _newOrders.push(orders[i]);
                    }
                    else {
                        $('#order-' + orders[i].Index).click();
                    }
                }
                if (_newOrders.length > 0)
                    sl.send();
                else
                    layer.msg("当前您所选的订单已被全部过滤");

            }, "taobao_sf_free_or_door_shipping_send", {
                selectOrders: orders,
                logicOrderIds: tbSfFreeShippingLogicOrderIds
            });
            return false;
        }

        if (unPrintOrder.length > 0 && !confirmPrinted) {
            var html = "";
            if (unPrintOrder.length < 10) {
                html = "订单【" + unPrintOrder.join(",") + "】未检测到打印标记，请确认是否有打印面单？"
            }
            else {
                html = "检测到有<label class='wu-color-b wu-weight600' title='" + unPrintOrder.join(",") + "'>" + unPrintOrder.length + "</label>";
                html += "个订单没有打印标记，请确认是否有打印面单？";
            }
            var dialogSendTips = layer.open({
                type: 1,
                title: "请确认是否有打印面单",
                content: html,
                btn: ["确认已打印，继续发货", "取消"],
                shadeClose: true,
                skin: 'wu-dailog',
                area: ['480px', '250px'],
                btn1: function () {
                    layer.close(dialogSendTips);
                    sl.realSend(isDontNeedLogistic, orders, true); //已确认打印，继续发货
                    return true;
                },
                btn2: function () {
                    return true;
                }
            });
            return false;
        }

        if (pddDoorOrders.length > 0 && !confirmPddDoor) {
            //var html = "系统检测到您勾选的订单包含不建议手动打单发货的订单，请问是否听从拼多多平台建议，忽略操作该类订单？<br/>拼多多跨境托管单 - 快递上门揽收类的订单不建议自行打印(如需打印，优先顺丰快递”您只需准备包裹快递上门揽收成功后，订单状态会自动变更为已发货状态";
            var html = "";
            html += '<div style="padding:15px;">';
            html += '<div style="font-size: 16px;margin-bottom: 10px;display:flex"><i class="layui-layer-ico layui-layer-ico1" style="background-position: -90px 0;width: 30px;height: 30px;display: block;margin-right:5px;"></i>';
            html += '<span style="flex:1">系统检测到您勾选的订单包含不建议手动打单发货的订单，请问是否听从拼多多平台建议， 忽略操作该类订单?</span></div>';
            html += '<div style="display: flex;flex-direction: column;color: #ec9512;padding:8px 25px 0 35px;line-height:20px;font-size:16px">'
            html += '<div>拼多多跨境托管单-快递上门揽收类的订单不建议</div>';
            html += '<div style="">自行打印(如需打印，优先顺丰快递)</div>';
            html += '<div style="">您只需准备包裹快递上门揽收成功后，</div>';
            html += '<div style="">订单状态会自动变更为已发货状态</div>';
            html += '</div>';
            html += '</div>';
            var dialogDoorTips = layer.open({
                type: 1,
                title: "忽略发货提示",
                content: html,
                btn: ["忽略该类订单继续操作", "继续操作所有订单"],
                shadeClose: true,
                area: ['480px'],
                btn1: function () {
                    layer.closeAll();
                    //过滤订单后，重新发起
                    orderTableBuilder.FilterPddDoorOrder();
                    sl.send();
                    return true;
                },
                btn2: function () {
                    layer.close(dialogDoorTips);
                    sl.realSend(isDontNeedLogistic, orders, confirmPrinted, isFromMutilSend, true, undefined, isManyCode);
                    return true;
                }
            });
            return false;
        }

        if (riskCtrlOrder.length > 0) {
            layer.alert("订单【" + riskCtrlOrder.join(",") + "】风控中，不允许发货！<label style='color:red'>当风控解除后，请及时发货。</label>", { skin: 'wu-dailog' });
            return false;
        }

        if (sendSFOrder.length > 0 && template.ExpressCompanyCode != "SF") {
            if (sendSFOrder.length == orders.length) {
                layer.alert("所选订单为“加价发顺丰”订单，请使用顺丰快递发货。", { skin: 'wu-dailog' });
                return false;
            }
            else {
                layer.alert("订单【" + sendSFOrder.join(",") + "】为“加价发顺丰”订单，请使用顺丰快递发货。", { skin: 'wu-dailog' });
                return false;
            }
        }
        //return false
        var preSaleOrdersSkipTag = commonModule.getStorage("taobao_preSaleOrdersSkipTag");
        //淘宝判断现货订单，预售订单发货提示
        if (preSaleOrdersSkipTag != "1" && !confirmPrinted && (taobaoPreSaleOrdersIds.length > 0 || taobaoSoonSendOrderIds.length > 0)) {
            var html = "";
            if (taobaoPreSaleOrdersIds.length > 0 && taobaoSoonSendOrderIds.length > 0) {
                html = "已选择<span style='color: #3aadff;margin: 0px 2px;'>" + selectedOrderWithSubCount + "</span>个订单，其中<span  style='color: #fe6f4f;margin: 0px 2px;'>" + taobaoSoonSendOrderIds.length + "</span>个现货订单<span style='color: #fe6f4f;margin: 0px 2px;'>距离付款时间不足3小时</span>，其中<span  style='color: #fe6f4f;margin: 0px 2px;'>" + taobaoPreSaleOrdersIds.length + "</span>个预售订单<span  style='color: #fe6f4f;margin: 0px 2px;'>距离最晚发货时间大于48小时</span>，是否跳过这类订单继续发货？"
            } else if (taobaoPreSaleOrdersIds.length > 0) {
                html = "已选择<span style='color: #3aadff;margin: 0px 2px;'>" + selectedOrderWithSubCount + "</span>个订单，其中<span  style='color: #fe6f4f;margin: 0px 2px;'>" + taobaoPreSaleOrdersIds.length + "</span>个预售订单<span style='color: #fe6f4f;margin: 0px 2px;'>距离最晚发货时间大于48小时</span>，是否跳过这类订单继续发货？"
            } else {
                html = "已选择<span style='color: #3aadff;margin: 0px 2px;'>" + selectedOrderWithSubCount + "</span>个订单，其中<span  style='color: #fe6f4f;margin: 0px 2px;'>" + taobaoSoonSendOrderIds.length + "</span>个现货订单<span style='color: #fe6f4f;margin: 0px 2px;'>距离付款时间不足3小时</span>，是否跳过这类订单继续发货？"
            }
            var tipHtml = "<p style='font-size: 13px;color: #9E9E9E;'>订单发货后24小时无揽收记录，可能会被平台判定虚假发货，客诉后有赔付风险。</p>"
            var dialogSendTips = layer.open({
                type: 1,
                title: "提示",
                content: "<div style='font-size:14px;line-height:20px;'><div><p style='margin-bottom:5px;font-weight: 600;'>" + html + "</p>" + tipHtml + '</div></div>',
                btn: ["跳过，继续", "忽略，继续", "取消"],
                skin: 'wu-dailog',
                area: ['550px', '250px'],
                success: function (layero, index, that) {
                    var ignoreBtnHtml = '<div style="top: 212px;left: 20px;position: absolute;cursor: pointer"><div><input type="checkbox"id="skipTopBtn"style="margin-right:5px;cursor: pointer;"><span onClick="sendLogistic.switchTaobaoPreSaleTip(event)">本周内不再提醒</span></div></div>';
                    layero.find(".layui-layer-content").after(ignoreBtnHtml);
                },
                btn1: function () {
                    if ($("#skipTopBtn:checked").length > 0) {
                        commonModule.setStorage("taobao_preSaleOrdersSkipTag", "1", new Date().DateDiff("n", new Date().getNextMonday()));
                    }
                    taobaoPreSaleSkipConfirm = true
                    var newOrders = []
                    for (var i = 0; i < orders.length; i++) {
                        var curOrder = orders[i];
                        var hasSoonAndPreSaleOrder = false
                        for (var ii = 0; ii < curOrder.SubOrders.length; ii++) {
                            var curSubOrder = curOrder.SubOrders[ii];
                            if (taobaoPreSaleOrdersIds.includes(curSubOrder.PlatformOrderId) || taobaoSoonSendOrderIds.includes(curSubOrder.PlatformOrderId)) {

                                //$("#order-" + curOrder.Id).click();
                                // $("#order-" + curOrder.Id).removeClass("onClickColor");
                                // $("input[data-id='" + curOrder.Id + "']").prop("checked", false);
                                // orderTableBuilder.rows[curOrder.Index].checked = false;

                                hasSoonAndPreSaleOrder = true;
                                break;
                            }

                        }
                        if (!hasSoonAndPreSaleOrder) {
                            newOrders.push(curOrder)
                        }

                    }
                    if (newOrders.length > 0) {
                        sl.realSend(isDontNeedLogistic, newOrders, true); //已确认打印，继续发货
                        return true;
                    }
                    else {
                        layer.closeAll();
                        layer.msg("过滤后无可发货订单！");
                        return false;
                    }
                },
                btn2: function () {
                    if ($("#skipTopBtn:checked").length > 0) {
                        commonModule.setStorage("taobao_preSaleOrdersSkipTag", "1", new Date().DateDiff("n", new Date().getNextMonday()));
                    }
                    sl.realSend(isDontNeedLogistic, orders, true); //已确认打印，继续发货
                    return true;
                },
                btn3: function () {
                    return true;
                }
            });
            return false;
        }
        
        // 通过所有检查后重置标记，确保下次检查正常
        skipTaobaoSfFreeShippingCheck = false;

        return true;
    }

    sl.check_resend = function (orders, template, isDontNeedLogistic, confirmPrinted) {
        if (!orders || orders.length == 0) {
            if (otb.isSelectOrder()) {
                layer.alert("请选择您要发货的商品", { skin: 'wu-dailog' });
            }
            else {
                layer.alert("请选择您要发货的订单", { skin: 'wu-dailog' });
            }
            return false;
        }
        if (sl.IsReSending) {
            layer.alert("当前有发货任务未完成，请稍候再试", { skin: 'wu-dailog' });
            return;
        }
        //检查是否是已经发货了的订单
        var errorCount = 0;
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var index = order.Index;
            var o = otb.rows[index];
            if (o.ErpState != "sended")
                errorCount++;
            if (o.WaybillCodes && o.WaybillCodes.length > 0) {
                var wc = o.WaybillCodes[0];
                order.WaybillCode = wc.WaybillCode;
                order.ExpressCompanyCode = wc.CompanyCode;
            }
        }
        if (errorCount > 0) {
            layer.alert("您选择的订单不是已发货订单，请选择已发货订单进行二次发货。", { btn: ["知道了"], skin: 'wu-dailog' });
            return false;
        }
        if (!isDontNeedLogistic && $("input[name='rdo_print_template']").length == 0) {
            layer.alert('您还没有添加快递模板，请点击<span id="sp_add_template" class="orderList_expressTemplate_addIcon"></span>添加快递模板', { skin: 'wu-dailog' });
            return false;
        }
        if (!isDontNeedLogistic) {
            if (template == null) {
                layer.alert("请选择您要发货的快递模板", { skin: 'wu-dailog' });
                return false;
            }
        }
        //return true; 
        var riskCtrlOrder = []; //拼多多风控订单
        var sendSFOrder = []; //拼多多顺丰加价订单
        var unPrintOrder = []; //获取了单号，但是没有打印标记的订单，发货提示 用户
        var receiverChangeOrders = [];//地址与上一次打印地址不一致订单
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var row = otb.rows[order.Index];
            var buyerName = order.Buyer.BuyerWangWang;
            if (!buyerName || buyerName == "")
                buyerName = order.Receiver.ToName;
            if (order.PlatformType == "Pinduoduo" && order.IsWeiGong == true) {
                riskCtrlOrder.push(order.PlatformOrderId);
            }

            if (order.PlatformType == "Pinduoduo" && order.ExtField1 == "1") {
                sendSFOrder.push(order.PlatformOrderId);
            }

            if (!row.PrintState) {
                unPrintOrder.push(order.PlatformOrderId);
            }

            if (order.OrderItems == null || order.OrderItems.length <= 0) {
                layer.alert("第【" + (order.Index + 1) + "】行买家【" + buyerName + "】的订单未选择任何商品，请展开订单勾选需要发货的商品。", { skin: 'wu-dailog' });
                return false;
            }
            if (!isDontNeedLogistic && (!order.WaybillCode || order.WaybillCode.trim() == "")) {
                layer.alert("第【" + (order.Index + 1) + "】行买家【" + buyerName + "】的快递单号不能为空，请先打印快递单，再进行发货", { skin: 'wu-dailog' });
                return false;
            }
            if (order.ReceiverIsChange) {
                receiverChangeOrders.push(order);
            }
        }

        //地址与上一次打印地址不一致订单
        if (receiverChangeOrders.length > 0) {
            cancelWaybillCodeFunc(receiverChangeOrders, false)
            return false;
        }

        if (unPrintOrder.length > 0 && !confirmPrinted) {
            var html = "";
            if (unPrintOrder.length < 10) {
                html = "订单【" + unPrintOrder.join(",") + "】未检测到打印标记，请确认是否有打印面单？"
            }
            else {
                html = "检测到有<label class='wu-color-b wu-weight600' title='" + unPrintOrder.join(",") + "'>" + unPrintOrder.length + "</label>";
                html += "个订单没有打印标记，请确认是否有打印面单？";
            }
            var dialogSendTips = layer.open({
                type: 1,
                title: "请确认是否有打印面单",
                content: html,
                btn: ["确认已打印，继续发货", "取消"],
                shadeClose: true,
                skin: 'wu-dailog',
                area: ['480px', '250px'],
                btn1: function () {
                    layer.close(dialogSendTips);
                    sl.realResend(isDontNeedLogistic, orders, true); //已确认打印，继续发货
                    return true;
                },
                btn2: function () {
                    return true;
                }
            });
            return false;
        }

        if (riskCtrlOrder.length > 0) {
            layer.alert("订单【" + riskCtrlOrder.join(",") + "】风控中，不允许发货！<label style='color:red'>当风控解除后，请及时发货。</label>", { skin: 'wu-dailog' });
            return false;
        }

        if (sendSFOrder.length > 0 && template.ExpressCompanyCode != "SF") {
            if (sendSFOrder.length == orders.length) {
                layer.alert("所选订单为“加价发顺丰”订单，请使用顺丰快递发货。", { skin: 'wu-dailog' });
                return false;
            }
            else {
                layer.alert("订单【" + sendSFOrder.join(",") + "】为“加价发顺丰”订单，请使用顺丰快递发货。", { skin: 'wu-dailog' });
                return false;
            }
        }


        return true;
    }

    sl.checkNoLogisticInfo = function (orders) {
        //检查必填项是否填写
        var noLogisticsCondition = $("#nologistics-condition-select").val();
        var noLogisticsBillNo = $("#nologistics-noLogisticsBillNo-input").val();
        var noLogisticsName = $("#nologistics-noLogisticsName-input").val();
        var noLogisticsTel = $("#nologistics-noLogisticsTel-input").val();
        var remarks = $("#nologistics-remarks-input").val();
        if (noLogisticsCondition == 1 || noLogisticsCondition == 3) {
            if (
                !noLogisticsName || noLogisticsName.trim() == "" ||
                !noLogisticsTel || noLogisticsTel.trim() == ""
            ) {
                var pt = orders.length > 0 && orders[0].PlatformType;
                var msg = pt == "WeiDian" ? "请填写快递名称和快递单号" : "请填写物流名称和物流联系方式";
                layer.msg(msg);
                return false;
            }
        }
        else if (noLogisticsCondition == 2) {
            if (orders.length == 1) {
                if (!noLogisticsBillNo || noLogisticsBillNo.trim() == "") {
                    layer.msg("请填写运单号");
                    return false;
                } else {
                    orders[0].WaybillCode = noLogisticsBillNo;
                }
            } else {
                //验证单号是否填写
                var count = 0;
                for (var i = 0; i < orders.length; i++) {
                    var order = orders[i];
                    if (!order.WaybillCode || order.WaybillCode.trim() == "") {
                        count++;
                    }
                }
                if (count > 0) {
                    layer.alert("您有" + count + "个订单没有填写运单号，发货原因不能为【补运费、差价】<br/>您可以先返回订单列表填写或获取运单号，或选择其他发货原因",
                        { area: ["550px"], icon: 2, skin: 'wu-dailog' });
                    return false;
                }
            }

        }
        return { noLogisticsCondition: noLogisticsCondition, noLogisticsBillNo: noLogisticsBillNo, noLogisticsName: noLogisticsName, noLogisticsTel: noLogisticsTel, remarks: remarks };
    }

    var _IgnoreOrdersByWaybillCodeIsEmpty = false; //忽略面单号为空的订单，打印后发货，如果有失败的，只发货成功的。
    sl.SetIgnoreOrderFlag = function (val) {
        _IgnoreOrdersByWaybillCodeIsEmpty = val;
    }

    sl.IsNotConfirm = false; // 是否不需要弹窗确认发货

    sl.resend = function (isDontNeedLogistic, orderId, isNotConfirm, printSuccessOrderIds) {
        //orders = otb.getSelections();
        orders = otb.getAfterSaleSelections();

        //过滤打印成功的订单，正常批量发货不处理
        if (printSuccessOrderIds && printSuccessOrderIds.length > 0) {
            var os = [];
            $(orders).each(function (i, o) {
                if (printSuccessOrderIds.indexOf(o.Id) != -1)
                    os.push(o);
            });
            orders = os;
        }

        //是否忽略运单号为空的订单
        if (_IgnoreOrdersByWaybillCodeIsEmpty) {
            var os = [];
            for (var i = 0; i < orders.length; i++) {
                var o = orders[i];
                if (o.WaybillCode != "") {
                    os.push(o);
                }
            }
            orders = os;
        }

        //若是拼多多订单，且使用的非拼多多电子面单，给与提示
        var isPinduoduo = common.PlatformType == "Pinduoduo" || (orders && orders[0].PlatformType == "Pinduoduo");
        var template = tmpModule.GetCurrentTemplate();
        if (!template) {
            sl.LockCurrenButton(false);
            layer.alert("请选择发货模板", { skin: 'wu-dailog' });
            return;
        }
        var ck = $.cookie("pdd-send-tips-no-show");
        if (isPinduoduo && !common.IsPddTemplate(template.TemplateType) && !common.IsPddKuaiYunTemplate(template.TemplateType, template.ExpressCompanyCode) && !ck) {
            var dialogIndex = layer.open({
                type: 1,
                title: "请确认",
                content: "<div style='font-size:14px;line-height:20px;margin:25px;'><div><p style='margin:5px;'>您当前使用的不是拼多多电子面单，发货时可能会出现发货失败或网络超时。</p><p style='margin:5px;'>为了提升您的发货效率，根据拼多多官方建议，请使用拼多多电子面单。</p><p style='margin:5px;'>使用教程：<a style='color:#2ebae9;' target='_blank' href='https://www.dgjapp.com/newHelpContentPc.html?id=5cbe6ccb33088732d493806c'>前往查看</a></p></div></div>",
                btn: ["继续发货", "不再提示，继续发货", "取消"],
                shadeClose: true,
                area: ['550px', '250px'],
                btn1: function () {
                    layer.close(dialogIndex);
                    sl.realResend(isDontNeedLogistic, orders);
                    return true;
                },
                btn2: function () {
                    layer.close(dialogIndex);
                    $.cookie("pdd-send-tips-no-show", 1, { expires: 30 });
                    sl.realResend(isDontNeedLogistic, orders);
                    return true;
                },
                btn3: function () {
                    sl.LockCurrenButton(false);
                    return true;
                }
            });
        }
        else {
            sl.realResend(isDontNeedLogistic, orders);
        }
        return;
    }

    ///发货
    ///参数 orderId 若是针对单个订单时必填
    ///参数 isDontNeedLogistic 是否为不需要物流：true 不需要物流发货  ; false 需要物流发货 默认为false
    ///参数 confirmPrinted 确认已打印
    sl.realResend = function (isDontNeedLogistic, orders, confirmPrinted) {
        var isSingleOrder = orders.length == 1;
        var template = tmpModule.GetCurrentTemplate();
        if (!sl.check_resend(orders, template, isDontNeedLogistic, confirmPrinted)) {
            sl.LockCurrenButton(false);
            return false;
        }
        if (template == null)
            template = {};
        var ext = otb.getSelectionsExt(orders);
        var pt = otb.rows[0].PlatformType;
        var dialog = $.templates("#send-logistic-dialog-tmpl");
        var html = dialog.render({ Orders: orders, ExtInfo: ext, Template: template, isDontNeedLogistic: isDontNeedLogistic, isSingleOrder: isSingleOrder, platformType: pt, IsPddFds: common.IsPddFds() });
        var title = "二次发货";
        var height = "250px";
        if (isDontNeedLogistic) {
            title = "无需物流发货";
            height = "380px";
            if (pt == "YouZan")
                height = "250px";
        }

        var btnText = "确定二次发货";
        if (common.IsPddFds()) {
            btnText = "确定回传";
            title = "回传单号";
        }
        if (sl.IsNotConfirm == true) {
            //开始发货
            try {
                doResend(orders, ext, template, null);
            } catch (e) {
                sl.IsReSending = false;
                sl.LockCurrenButton(false);
                console.error(e);
                alert("发货异常，请联系我们：" + e.message + "\r\n异常信息：" + e.stack);
            }
        }
        else {
            layer.open({
                type: 1,
                title: title,
                btn: [btnText, "取消"],
                area: ['560px', height],
                content: html,
                skin: 'wu-dailog',
                btn1: function () {
                    var nologisticModel = null;
                    if (isDontNeedLogistic) {
                        var temp = sl.checkNoLogisticInfo(orders)
                        if (temp == false) {
                            sl.LockCurrenButton(false);
                            return false;
                        }
                        else
                            nologisticModel = temp;
                    }
                    //开始发货
                    try {
                        doResend(orders, ext, template, nologisticModel);
                    } catch (e) {
                        sl.IsReSending = false;
                        sl.LockCurrenButton(false);
                        console.error(e);
                        alert("发货异常，请联系我们：" + e.message + "\r\n异常信息：" + e.stack);
                    }
                    return false;
                },
                btn2: function () {
                    return true;
                }
            });
        }
        $("p[id^='nologistics-']").hide();
    }

    function doResend(orders, refundOrders, template, nologisticModel, isSendPreCheck) {
        //排除未发货的订单
        var request = {
            TemplateId: template.Id,
            ExpressCompanyId: template.ExpressCompanyId,
            Orders: orders,
            ExcludeOrders: refundOrders,
            NoLogisticsInfoModel: nologisticModel,
            IsSendPreCheck: (isSendPreCheck == undefined ? true : isSendPreCheck)
        };

        sl.IsReSending = true;
        sl.LockCurrenButton(true);
        common.Ajax({
            url: "/NewOrder/OnlineResend",
            data: { model: request },
            loadingMessage: "二次发货中...",
            success: function (data) {
                sl.IsReSending = false;
                sl.LockCurrenButton(false);
                if (common.IsError(data)) {
                    return;
                }
                layer.closeAll();
                if (data.Success) {
                    try {
                        var model = data.Data;
                        model.Template = template;
                        var hideOrderCount = 0;

                        if (model.ErrorCount > 0) {

                            //判断是否有发货预检查的报错，预检查报错用户可以确认继续发货，继续发货不再预检查
                            var btns = ["关闭"];
                            if (model.HasSendPreCheckError)
                                btns = ["坚持发货", "关闭"];

                            //显示详细的错误消息
                            //var dialog = $.templates("#send-logistic-error-dialog-tmpl");
                            var dialog = $.templates("#new_send_fail_warn");
                            for (var i = 0; i < model.Orders.length; i++) {
                                var ro = model.Orders[i];
                                if (ro.IsSuccess)
                                    continue;

                                //二次发货失败结果提示转换
                                if (ro.ErrorMessage == "接口错误：请先重试发货，如果问题依然存在，请在店铺后台确认店铺是否已发货") {
                                    ro.ErrorMessage = "部分发货订单不支持二次发货，若不属于该类型，请先重试发货，如果问题依然存在，请在店铺后台确认店铺是否已全部发货";
                                }
                            }
                            var html = dialog.render(model);
                            layer.open({
                                type: 1,
                                title: "发货结果",
                                //closeBtn: 1,
                                btn: btns,
                                shadeClose: true,
                                area: ['1000px', '350px'],
                                content: html,
                                yes: function () {
                                    if (model.HasSendPreCheckError) {
                                        //坚持发货，将错误订单找出，继续发货
                                        var reSendOrders = [];
                                        for (var i = 0; i < model.Orders.length; i++) {
                                            var ro = model.Orders[i];
                                            if (ro.IsSuccess)
                                                continue;
                                            for (var j = 0; j < orders.length; j++) {
                                                var o = orders[j];
                                                if (ro.OrderRequest.PlatformOrderId == o.PlatformOrderId && ro.OrderRequest.ShopId == o.ShopId) {
                                                    reSendOrders.push(o);
                                                    break;
                                                }
                                            }
                                        }
                                        //继续发货，不预检查
                                        sl.doSend(reSendOrders, refundOrders, template, nologisticModel, false);
                                    }
                                    else
                                        layer.closeAll();
                                },
                                btn2: function () {
                                    layer.closeAll();
                                },
                                cancel: function () {
                                    layer.closeAll();
                                }
                            });

                            // 发货接口兜底检测到地址变更的订单打标显示异常单
                            checkReceiverChangeAfterSended(model);
                        }
                        else {
                            $("#SeachConditions").click();
                            layer.msg("二次发货成功", { time: 500 });
                        }
                        if (model.SuccessCount > 0) {
                            $("#SeachConditions").click();
                        }

                    } catch (e) {
                        sl.LockCurrenButton(false);
                        var errorMsg = "二次发货成功后前端操作异常》" + e.stack;
                        //console.log(errorMsg);
                        common.JsExcptionLog("二次发货前端异常日志", errorMsg);

                    }

                } else {

                    //二次发货失败结果提示转换
                    if (data.Message == "接口错误：请先重试发货，如果问题依然存在，请在店铺后台确认店铺是否已发货") {
                        data.Message = "部分发货订单不支持二次发货，若不属于该类型，请先重试发货，如果问题依然存在，请在店铺后台确认店铺是否已全部发货";
                    }
                    layer.alert("二次发货失败：" + data.Message, { skin: 'wu-dailog' });
                }
            }
        });
    }

    ///发货
    ///参数 orderId 若是针对单个订单时必填
    ///参数 isDontNeedLogistic 是否为不需要物流：true 不需要物流发货  ; false 需要物流发货 默认为false，打印成功的订单Id
    // 参数 isFromMutilSend 为1表示源自抖店的上传多单号
    // 参数 isManyCode 为true表示多单号回传发货--2025-04-07
    sl.send = function (isDontNeedLogistic, orderId, isNotConfirm, printSuccessOrderIds, checkOrders, isFromMutilSend, callbackObj, isManyCode) {
        console.log("开始发货sl.send" + sl.LockButton)
        sl.LockCurrenButton(true);
        console.log("开始发货sl.send 开始锁定按钮" + sl.LockButton)

        sl.IsNotConfirm = isNotConfirm || false; // 是否不需要弹窗确认发货
        var orders = [];
        var receiverChangeOrders = []; //地址变更的订单
        if (!checkOrders || checkOrders.length == 0) {
            if (orderId)
                orders = otb.getSelections(orderId)
            else
                orders = otb.getSelections();
        }
        else {
            orders = checkOrders;
        }
        if ($("#hidBlanceCount") != undefined && parseInt($("#hidBlanceCount").val()) < parseInt($("#hidBlanceCount").val())) {
            common.ProcessVersionOrderCountErrorCode("ORDER_COUNT", parseInt($("#hidBlanceCount").val()));
            sl.LockCurrenButton(false);
            return;
        }

        //过滤打印成功的订单，正常批量发货不处理
        if (printSuccessOrderIds && printSuccessOrderIds.length > 0) {
            var os = [];
            $(orders).each(function (i, o) {
                if (printSuccessOrderIds.indexOf(o.Id) != -1)
                    os.push(o);
            });
            orders = os;
        }

        // 1.判断是否存在京东预约发货的订单
        var hasJingdongDelaySendOrder = false;
        if (!sl.SkipJingdongSend) {
            $(orders).each(function (i, item) {
                if (item.PublishTime && item.PlatformType == "Jingdong") {
                    hasJingdongDelaySendOrder = true;
                    return;
                }
            });
        }

        var enableJingdongDelaySetting = [];
        if (hasJingdongDelaySendOrder && !sl.SkipJingdongSend) {
            var fxUserIds = [];
            var currentDate = new Date();
            var currentYear = currentDate.getFullYear();
            var currentMonth = currentDate.getMonth();
            var currentDay = currentDate.getDate();
            var delayOrders = [];
            $(orders).each(function (i, order) {              
                if (order.PublishTime && order.PlatformType === "Jingdong") {
                    var publishDate = new Date(order.PublishTime);
                    var publishYear = publishDate.getFullYear();
                    var publishMonth = publishDate.getMonth();
                    var publishDay = publishDate.getDate();
                    // 预约发货日期晚于当前日期
                    var isPublishDateLater =
                        publishYear > currentYear ||
                        (publishYear === currentYear && publishMonth > currentMonth) ||
                        (publishYear === currentYear && publishMonth === currentMonth && publishDay > currentDay);

                    if (isPublishDateLater) {
                        if (fxUserIds.indexOf(order.FxUserId) == -1) {
                            fxUserIds.push(order.FxUserId);
                        }
                        delayOrders.push({
                            orderId: order.Id,
                            fxUserId: order.FxUserId,
                            enableFilter: false
                        });
                    }
                }
            });
            if (delayOrders.length > 0) {
                commonModule.Ajax({
                    url: '/NewOrder/QueryJingDongScheduleDeliverySetting',
                    type: 'post',
                    data: {
                        fxUserId: fxUserIds
                    },
                    async: false,
                    success: function (data) {
                        $(data.Data).each(function (i, item) {
                            if (JSON.parse(item.Value)) {
                                enableJingdongDelaySetting.push({
                                    FxUserId: item.FxUserId,
                                    Value: JSON.parse(item.Value)
                                });
                            }
                        });
                    }
                });
                var getJingdongDelaySetting = function (currentFxUserId) {
                    var settingValue = false;
                    $(enableJingdongDelaySetting).each(function (i, item) {
                        if (item.FxUserId === currentFxUserId) {
                            settingValue = item.Value;
                            return false;
                        }
                    });
                    return settingValue;
                }

                var btn = ['跳过，继续', '忽略影响，继续'];
                if (enableJingdongDelaySetting.length == 1 && getJingdongDelaySetting(fxUserId)) {
                    btn = ['跳过，继续'];
                }
                var tmpContent = '';
                var enableFilterOrderCount = 0;
                var orderCount = orders.length;
                var delayOrdersCount = delayOrders.length;
                for (var i in delayOrders) {
                    var item = delayOrders[i];
                    var enable = getJingdongDelaySetting(item.fxUserId);
                    if (item.fxUserId != fxUserId && enable) {
                        enableFilterOrderCount++;
                    }
                    delayOrders[i].enableFilter = enable;
                }
                if (enableFilterOrderCount > 0) {
                    tmpContent = '其中' + enableFilterOrderCount + "个预约订单源头商家设置了不允许提前发货，";
                    if (orderCount == delayOrdersCount && orderCount == enableFilterOrderCount) {
                        btn = ['跳过，继续'];
                    }
                }
                var dialogIndex = layer.open({
                    type: 1,
                    title: "请确认",
                    content: "<div style='font-size:14px;line-height:20px;margin:25px;'><div><p style='margin:5px;'>已选择" + orderCount + "个订单，其中" + delayOrdersCount + "个预约发货订单未到发货日期，" + tmpContent + "提前发货可能导致平台处罚，是否跳过这些订单继续发货？</p></div></div>",
                    btn: btn,
                    shadeClose: false,
                    area: ['500px', '250px'],
                    cancel: function () {
                        sl.LockCurrenButton(false);
                    },
                    btn1: function () {
                        layer.close(dialogIndex);
                        var newOrders = [];
                        var filterOrderIds = [];
                        for (var i in delayOrders) {
                            filterOrderIds.push(delayOrders[i].orderId);
                        }
                        $(orders).each(function (i, item) {
                            if (filterOrderIds.indexOf(item.Id) === -1) {
                                newOrders.push(item);
                            }
                        });
                        sl.realSend(isDontNeedLogistic, newOrders, undefined, isFromMutilSend, undefined, callbackObj, isManyCode);
                        sl.LockCurrenButton(false);
                        return true;
                    },
                    btn2: function () {
                        layer.close(dialogIndex);
                        var newOrders = [];
                        if (enableFilterOrderCount > 0) {
                            var filterOrderIds = [];
                            for (var i in delayOrders) {
                                if (delayOrders[i].enableFilter) {
                                    filterOrderIds.push(delayOrders[i].orderId);
                                }
                            }
                            $(orders).each(function (i, item) {
                                if (filterOrderIds.indexOf(item.Id) === -1) {
                                    newOrders.push(item);
                                }
                            });
                        } else {
                            newOrders = orders;
                        }

                        sl.realSend(isDontNeedLogistic, newOrders, undefined, isFromMutilSend, undefined, callbackObj, isManyCode);
                        sl.LockCurrenButton(false);
                        return true;
                    }
                });
                return;
            }  
          
        }

        // 检查后端是否有退款订单
        //otb.checkReturnOrders(orders);

        //// 头条平台部分发货检测
        //if (common.PlatformType == "TouTiao") {
        //    var isCheckAll = otb.checkTouTiaoOrders();
        //    if (!isCheckAll) {
        //        layer.confirm("当前平台不支持部分商品发货");
        //        return;
        //    }
        //}

        if (isDontNeedLogistic == null || isDontNeedLogistic == undefined) {
            //若是拼多多订单，且使用的非拼多多电子面单，给与提示
            var isPinduoduo = common.PlatformType == "Pinduoduo" || (orders && orders.length > 0 && orders[0].PlatformType == "Pinduoduo");
            var template = tmpModule.GetCurrentTemplate();
            if (!template) {
                layer.alert("请选择发货模板", { skin: 'wu-dailog' });
                sl.LockCurrenButton(false);
                return;
            }
            var ck = $.cookie("pdd-send-tips-no-show");
            if (isPinduoduo && !common.IsPddTemplate(template.TemplateType) && !common.IsPddKuaiYunTemplate(template.TemplateType, template.ExpressCompanyCode) && !ck) {
                var dialogIndex = layer.open({
                    type: 1,
                    title: "请确认",
                    content: "<div style='font-size:14px;line-height:20px;margin:25px;'><div><p style='margin:5px;'>您当前使用的不是拼多多电子面单，发货时可能会出现发货失败或网络超时。</p><p style='margin:5px;'>为了提升您的发货效率，根据拼多多官方建议，请使用拼多多电子面单。</p><p style='margin:5px;'>使用教程：<a style='color:#2ebae9;' target='_blank' href='https://www.dgjapp.com/newHelpContentPc.html?id=5cbe6ccb33088732d493806c'>前往查看</a></p></div></div>",
                    btn: ["继续发货", "不再提示，继续发货", "取消"],
                    shadeClose: true,
                    area: ['550px', '250px'],
                    btn1: function () {
                        layer.close(dialogIndex);
                        sl.realSend(isDontNeedLogistic, orders, undefined, isFromMutilSend, undefined, callbackObj, isManyCode);
                        return true;
                    },
                    btn2: function () {
                        layer.close(dialogIndex);
                        $.cookie("pdd-send-tips-no-show", 1, { expires: 30 });
                        sl.realSend(isDontNeedLogistic, orders, undefined, isFromMutilSend, undefined, callbackObj, isManyCode);
                        return true;
                    },
                    btn3: function () {
                        sl.LockCurrenButton(false);
                        return true;
                    }
                });
            }
            else {
                sl.realSend(isDontNeedLogistic, orders, undefined, isFromMutilSend, undefined, callbackObj, isManyCode);
            }
            return;
        }
        var isNot1688Orders = [];
        var alibabaOrders = [];
        var receiverChangeOrders = []; // 地址变更的订单
        var filterReceiverChangeOrders = []; // 过滤掉地址变更的订单
        for (var i = 0; i < orders.length; i++) {
            var o = orders[i];
            if (o.PlatformType == "Alibaba" || o.PlatformType == "1688" || o.PlatformType == "Taobao" || o.PlatformType == "YouZan" || o.PlatformType == "WeiDian" || o.PlatformType == "YunJi" || o.PlatformType == "WeiMeng")
                alibabaOrders.push(o);
            else
                isNot1688Orders.push(o);


            // 校验订单是否存在地址变更异常
            if (o.OrderTags && o.OrderTags.length > 0) { 
                var isReceiverChange = false; // 是否存在地址变更
                for (var j = 0; j < o.OrderTags.length; j++) {
                    var sub = o.OrderTags[j];
                    if (sub.Tag === 'receiver_change' && sub.TagType === 'Order') {
                        receiverChangeOrders.push(sub);
                        isReceiverChange = true;
                        break;
                    }
                }
                if (!isReceiverChange) {
                    filterReceiverChangeOrders.push(o);
                }
            } 
            
        }

        // 校验订单存在地址变更异常提醒
        // if (receiverChangeOrders.length > 0) {
        //     layer.open({
        //         type: 1,
        //         title: '异常提示',
        //         content: '<div>已勾选' + orders.length + '个订单，其中'+ receiverChangeOrders.length +'个订单为<span>地址变更</span>订单，地址变更订单无法正常发货，是否跳过地址变更订单继续发货其他订单？</div>',
        //         skin: 'n-skin',
        //         btn: ['取消', '跳过，发货其他订单'],
        //         id: 'receiverChangeLayer',
        //         success: function (layero, index) {
        //             $('#receiverChangeLayer.n-skin .layui-layer-btn a:eq(0)').css({border: '#dedede', 'background-color': '#fff', color: 'rgba(0, 0, 0, 0.9)'});
        //             $('#receiverChangeLayer.n-skin .layui-layer-btn a:eq(1)').css({border: '#0888FF', 'background-color': '#0888FF', color: '#fff'});
        //         },
        //         yes: function (index, layero) {
        //             layer.close(index);
                    
        //         },
        //         btn2: function (index, layero) {
        //             layer.close(index);
        //             sl.realSend(isDontNeedLogistic, filterReceiverChangeOrders, undefined, isFromMutilSend, undefined, callbackObj);
        //         }
        //     })
        //     return
        // }

        
        if (!isDontNeedLogistic)
            sl.realSend(isDontNeedLogistic, orders, undefined, isFromMutilSend, undefined, callbackObj, isManyCode);
        else if (isNot1688Orders.length == 0)
            sl.realSend(isDontNeedLogistic, orders, undefined, isFromMutilSend, undefined, callbackObj, isManyCode);
        else {
            if (isNot1688Orders.length == orders.length) {
                layer.alert("您选择订单不支持【无物流发货】，请使用【批量发货】", { skin: 'wu-dailog' });
                sl.LockCurrenButton(false);
            } else {
                layer.open({
                    type: 1,
                    title: "请确认",
                    content: "<div style='font-size:14px;line-height:20px;margin:25px;'><div>您选择的订单中，有<span style='color:red;font-size:18px;'>" + isNot1688Orders.length + "</span>个不是1688或淘宝的订单，不能使用【无物流发货】</div><div>是否仅对1688和淘宝的订单进行【无物流发货】？</div></div>",
                    btn: ["仅对1688和淘宝订单进行【无物流发货】", "取消"],
                    shadeClose: true,
                    area: ['550px', '200px'],
                    btn1: function () {
                        for (var i = 0; i < isNot1688Orders.length; i++) {
                            var cur = isNot1688Orders[i];
                            $("#order-" + cur.Id).removeClass("onClickColor");
                            $(".order-chx[data-id='" + cur.Id + "']")[0].checked = false;//取消勾选
                        }
                        sl.realSend(isDontNeedLogistic, alibabaOrders, undefined, isFromMutilSend, undefined, callbackObj, isManyCode);
                        return true;
                    },
                    btn2: function () {
                        sl.LockCurrenButton(false);
                        return true;
                    }
                });
            }
        }
    }

    ///发货
    ///参数 orderId 若是针对单个订单时必填
    ///参数 isDontNeedLogistic 是否为不需要物流：true 不需要物流发货  ; false 需要物流发货 默认为false
    ///参数 confirmPrinted 确认已打印
    ///参数 isFromMutilSend  为1表示源自抖店的上传多单号
    ///参数 confirmPddDoor 跨境单快递上门揽收订单是否继续发货
    ///参数 isManyCode 为true表示多单号回传发货--2025-04-07
    sl.realSend = function (isDontNeedLogistic, orders, confirmPrinted, isFromMutilSend, confirmPddDoor, callbackObj, isManyCode) {
        var isSingleOrder = orders.length == 1;
        var template = tmpModule.GetCurrentTemplate();
        if (!sl.check(orders, template, isDontNeedLogistic, confirmPrinted, isFromMutilSend, confirmPddDoor, isManyCode)) {
            sl.LockCurrenButton(false);
            return false;
        }
        if (template == null)
            template = {};
        var ext = otb.getSelectionsExt(orders);

        // 订单发货过滤
        var sendAction = function () {
            var checkOrderRows = new Array();
            orders.forEach(function (item, index) {
                var row = orderTableBuilder.rows[item.Index];
                row = JSON.parse(JSON.stringify(row));
                checkOrderRows.push(row);
            });

            //[数码产品]识别码发货
            sl.IdentificationCode(checkOrderRows, function (isSkipConfirmDialog, needSkipOrderIds) {
                if (isSkipConfirmDialog) {
                    sl.IsNotConfirm = true;
                }

                // 跳过订单，执行其他订单
                if (needSkipOrderIds && needSkipOrderIds.length > 0) {
                    var newOrders = [];
                    orders.forEach(function (item, index) {
                        if (needSkipOrderIds.indexOf(item.Id) == -1) {
                            newOrders.push(item);
                        } else {
                            //$(".order-chx[data-id='" + item.Id + "']").trigger('click');
                            var obj = $(".order-chx[data-id='" + item.Id + "']");
                            obj.prop("checked", false);
                            orderTableBuilder.CheckOrderBind(obj);
                        }
                    });
                    orders = newOrders;
                }

                sendActionDialog();
            });
        }

        // 请求发货
        var sendActionDialog = function () {
            var pt = otb.rows[0].PlatformType;
            var dialog = $.templates("#send-logistic-dialog-tmpl");
            var filterAcceptOrderData = orders.filter(function (item) {
                return item.OrderTags && item.OrderTags.length > 0 && item.OrderTags.some(function (subItem) {
                    return subItem.Tag !== 'ChangeSku_Accept_Order';
                });
            });
            var wxAcceptOrderList = orders.filter(function (item) {
                return item.OrderTags && item.OrderTags.length > 0 && item.OrderTags.some(function (subItem) {
                    return subItem.Tag === 'ChangeSku_Accept_Order' && subItem.Platform === 'WxVideo';
                });
            });
            //console.log('filterAcceptOrderData', filterAcceptOrderData);
            var html = dialog.render({
                Orders: orders,
                ExtInfo: ext,
                Template: template,
                isDontNeedLogistic: isDontNeedLogistic,
                isSingleOrder: isSingleOrder,
                platformType: pt,
                IsPddFds: common.IsPddFds()
            });
            var title = "发货";
            var height = "250px";
            if (isDontNeedLogistic) {
                title = "无需物流发货";
                height = "380px";
                if (pt == "YouZan") {
                    height = "250px";
                }
            }
            var btnText = "确定发货";
            if (common.IsPddFds()) {
                btnText = "确定回传";
                title = "回传单号";
            }
            if (sl.IsNotConfirm == true) {
                // 开始发货
                try {
                    orders.forEach(function (item) {
                        // 遍历 OrderTags 数组
                        if (item.OrderTags && item.OrderTags.length > 0) {
                            item.OrderTags.forEach(function (tag) {
                                // 如果 Tag 为 ChangeSku_Accept_Order
                                if (tag.Tag === 'ChangeSku_Accept_Order' && tag.Platform === 'WxVideo') {
                                    // 将时间字符串转换为 Date 对象
                                    var expressPrintDate = new Date(item.ExpressPrintTime);
                                    var createDate = new Date(tag.CreateTime);
                                    // 如果 ExpressPrintTime 小于 CreateTime，就显示提示弹窗
                                    if (expressPrintDate.getTime() < createDate.getTime()) {
                                        layer.closeAll();
                                        var html = "";
                                        html += '<div class="n-font5">';
                                        html += '已选择 ' + orders.length + ' 个订单，其中' + wxAcceptOrderList.length + '个订单在打印快递单之后变更为“微信礼物已换款”，已变更的订单请重新打印快递单，是否跳过变更规格订单继续发货？';
                                        html += '</div>';
                                        layer.open({
                                            type: 1,
                                            title: '提示',
                                            content: html,
                                            area: '520px', //宽高
                                            skin: 'n-skin',
                                            success: function () { },
                                            btn: ['跳过，继续', '取消'],
                                            btn1: function (index) {
                                                sl.doSend(filterAcceptOrderData, ext, template, null, undefined, isFromMutilSend, callbackObj);
                                                layer.close(index);
                                            },
                                            btn2: function (index) {
                                                layer.close(index);
                                            }
                                        });
                                    } else {
                                        sl.doSend(orders, ext, template, null, undefined, isFromMutilSend, callbackObj);
                                    }
                                } else {
                                    sl.doSend(orders, ext, template, null, undefined, isFromMutilSend, callbackObj);
                                }
                            });
                        } else {
                            sl.doSend(orders, ext, template, null, undefined, isFromMutilSend, callbackObj);
                        }
                    });
                } catch (e) {
                    sl.IsSending = false;
                    sl.LockCurrenButton(false);
                    console.error(e);
                    alert("发货异常，请联系我们：" + e.message + "\r\n异常信息：" + e.stack);
                }
            }
            else {
                if (title == "发货") {
                    var ShowSendLogisticDialog = function (expressBillNum, isSupport, isPdd, abnormalOrders, manyCodeSendModel) {
                        var dialogV2 = $.templates("#send-logistic-dialog-tmplV2");
                        var htmlV2 = dialogV2.render({
                            OrdersNum: orders.length,
                            RefundOrdersNum: ext && ext.RefundOrders ? ext.RefundOrders.length : false,
                            ExpressBillNum: expressBillNum,
                            isPdd: isPdd,
                            isSupport: isSupport,
                            abnormalOrders: abnormalOrders
                        });
                        
                        var sendOption = {
                            type: 1,
                            title: "发货",
                            btn: false,
                            shadeClose: false,
                            area: "560px",
                            skin: 'n-skin',
                            // zIndex: 1989,
                            content: htmlV2,
                            success: function () {
                                $("#many_code-send-logistic-dialog").parent().css("padding", "0px");

                                // 开启多单号回传
                                if (commonModule.ManyCodeSendAway == "0" && isSupport) {
                                    $("#confirm-send-btn").remove();

                                    if (abnormalOrders) {
                                        $("#send-logistic-dialog-tip").text("系统检测到部分商品与快递单号不匹配，为确保发货准确，仅发货匹配的商品和单号并更新到店铺后台");

                                        $("#abnormal_orders-btn").on("click", function () {
                                            window.open(commonModule.rewriteUrl("/Common/Page/NewOrder-AbnormalOrder"), '_blank');
                                        });

                                        $("#bottom_order_inquiry-btn").on("click", function () {
                                            window.open(commonModule.rewriteUrl("/Common/Page/WaybillCodeList-Index"), '_blank');
                                        });

                                    } else if (isPdd) {
                                        $("#send-logistic-dialog-tip").text("发货结果会同步到店铺后台，并拆分订单为多个运单号回传，是否确定？");
                                    }  else {
                                        $("#send-logistic-dialog-tip").text("发货成功后，订单的多个包裹信息将更新至店铺后台");
                                    }

                                    // 预览包裹内容
                                    $("#preview-send_content-btn").on("click", function () {
                                        if (isPdd) {
                                            waitOrderModule.batchPinduoduoSendMany(true);
                                        } else {
                                            waitOrderModule.BatchSendManyV2(orders, true);
                                        }              
                                    });

                                    // 多单号回传
                                    $("#many_code-postback-btn").on("click", function () {
                                        if (abnormalOrders) {
                                            if (isPdd) {
                                                waitOrderModule.batchPinduoduoSendMany(true);
                                            } else {
                                                waitOrderModule.BatchSendManyV2(orders, true);
                                            }
                                        } else {
                                            try {
                                                if (manyCodeSendModel && manyCodeSendModel.length) {

                                                    var manyCodeSendList = JSON.parse(JSON.stringify(manyCodeSendModel));

                                                    manyCodeSendList.forEach(function (item) {
                                                        item.WaybillCodeModels = item.WaybillCodeModels.filter(function (wItem) {
                                                            return wItem.IsErr == false && wItem.IsLatestBatchCode == true;
                                                        });
                                                    });

                                                    var FindModel = function (Id) {
                                                        return manyCodeSendList.find(function (item) {
                                                            return item.Id == Id
                                                        });
                                                    }

                                                    orders.forEach(function (item) {
                                                        item.ManyCodeSendModel = FindModel(item.Id);
                                                    });
                                                }

                                                sl.doSend(orders, ext, template, null, undefined, isFromMutilSend, callbackObj);
                                            } catch (e) {
                                                sl.IsSending = false;
                                                sl.LockCurrenButton(false);
                                                console.error(e);
                                                alert("发货异常，请联系我们：" + e.message + "\r\n异常信息：" + e.stack);
                                            }
                                        }
                                    });

                                    // 普通发货
                                    $("#ordinary-send-btn").on("click", function () {
                                        try {
                                            sl.doSend(orders, ext, template, null, undefined, isFromMutilSend, callbackObj);
                                        } catch (e) {
                                            sl.IsSending = false;
                                            sl.LockCurrenButton(false);
                                            console.error(e);
                                            alert("发货异常，请联系我们：" + e.message + "\r\n异常信息：" + e.stack);
                                        }
                                    });

                                }
                                else {
                                    $("#preview-send_content-btn").remove();
                                    $("#many_code-postback-btn").remove();
                                    $("#ordinary-send-btn").remove();

                                    $("#send-logistic-dialog-tip").text("发货结果会按最新打印快递单号同步至后台，是否确定发货？");

                                    // 确定发货
                                    $("#confirm-send-btn").on("click", function () {
                                        try {
                                            sl.doSend(orders, ext, template, null, undefined, isFromMutilSend, callbackObj);
                                        } catch (e) {
                                            sl.IsSending = false;
                                            sl.LockCurrenButton(false);
                                            console.error(e);
                                            alert("发货异常，请联系我们：" + e.message + "\r\n异常信息：" + e.stack);
                                        }
                                    });
                                }

                                $("#cancel-send-btn").on("click", function () {
                                    layer.close(sendConfirmDialog_for_mutil);
                                });
                            },
                            end: function () {
                                if (commonModule.ManyCodeSendAway == "0" && isSupport) {
                                    $("#preview-send_content-btn").off();
                                    $("#many_code-postback-btn").off();
                                    $("#ordinary-send-btn").off();

                                    if (abnormalOrders) {
                                        $("#abnormal_orders-btn").off();
                                        $("#bottom_order_inquiry-btn").off();
                                    }
                                } else {
                                    $("#confirm-send-btn").off();
                                }
                                $("#cancel-send-btn").off();
                            }
                        }
                        if(isSupport) {
                            sendOption.zIndex = 1989;
                        }

                        sendConfirmDialog_for_mutil = layer.open(sendOption);
                    }

                    // 多单号发货检验
                    var SendCheckLogistic = function (isPdd) {
                        var ExpressBillNum = 0;
                        var AbnormalOrders = 0;

                        var ManyCodeSendModel = [];

                        var checkOrders = JSON.parse(JSON.stringify(orders));
                        checkOrders.forEach(function (oItem) {
                            var WaybillCodeModels = [];

                            // 获取最新的同一批次打印
                            var row = orderTableBuilder.rows[oItem.Index];
                            var lastNewCode = oItem.WaybillCodes.find(function (wItem) {
                                return wItem.WaybillCode == row.LastWaybillCode;
                            });

                            // 过滤已打印和回收失败的单号
                            oItem.WaybillCodes = oItem.WaybillCodes.filter(function (wItem) {
                                return wItem.Status == 1 || wItem.Status == 4
                            });

                            // 获取已选择的Items的OrderItemId集合
                            var ItemsOrderItemIds = oItem.Items.map(function (sItem) {
                                return sItem.OrderItemId - 0
                            });

                            oItem.WaybillCodes.forEach(function (wItem) {
                                wItem.WaybillCodeOrderProducts = wItem.WaybillCodeOrderProducts.filter(function (pItem) {
                                    return ItemsOrderItemIds.indexOf(pItem.OrderItemId) > -1
                                });
                            });


                            // 汇总全部底单商品数据
                            var allWaybillCodeOrderProducts = [];
                            oItem.WaybillCodes.forEach(function (wItem, wIndex) {
                                if (wItem.WaybillCodeOrderProducts && wItem.WaybillCodeOrderProducts.length > 0) {
                                    // MasterId为0 --直接用索引进行分组
                                    if (wItem.WaybillCodeOrderProducts[0].MasterId == 0) {
                                        wItem.WaybillCodeOrderProducts.forEach(function (aItem) {
                                            aItem.MasterId = wIndex;
                                        });
                                    }

                                    wItem.WaybillCodeOrderProducts.forEach(function (aItem) {
                                        allWaybillCodeOrderProducts.push(aItem);
                                    });
                                }
                            });

                            oItem.WaybillCodes.forEach(function (wItem) {
                                if (wItem.WaybillCodeOrderProducts && wItem.WaybillCodeOrderProducts.length > 0) {
                                    var IsErr = false;
                                    var ErrMsg = null;

                                    for (var i = 0; i < wItem.WaybillCodeOrderProducts.length; i++) {

                                        var orderItemId = wItem.WaybillCodeOrderProducts[i].OrderItemId;
                                        var fData = oItem.Items.find(function (fItem) {
                                            return fItem.OrderItemId == orderItemId;
                                        });

                                        if (fData) {
                                            var Quantity = 0;
                                            allWaybillCodeOrderProducts.forEach(function (aItem) {
                                                if (aItem.OrderItemId == orderItemId) {
                                                    Quantity += aItem.Quantity
                                                }
                                            });

                                            if (fData.Quantity < Quantity) {
                                                IsErr = true;
                                                ErrMsg = "发货选择的数量和底单对不上";
                                                break;
                                            }
                                        } else {
                                            IsErr = true;
                                            ErrMsg = "发货没选择这个商品";
                                            break;
                                        }
                                    }

                                    WaybillCodeModels.push({
                                        MasterId: wItem.WaybillCodeOrderProducts[0].MasterId,
                                        WaybillCode: wItem.WaybillCode,
                                        WaybillCodeOrderProducts: wItem.WaybillCodeOrderProducts,
                                        IsErr: IsErr,
                                        IsLatestBatchCode: lastNewCode ? lastNewCode.CreateDate == wItem.CreateDate : true,
                                        ErrMsg: ErrMsg,
                                    })
                                }
                            });

                            if (WaybillCodeModels.length > 0) {
                                var abnormalOrderList = WaybillCodeModels.filter(function (wItem) {
                                    return wItem.IsErr == true && wItem.ErrMsg == "发货选择的数量和底单对不上"
                                });

                                // 如果全是异常数据，则需要保留最新的一个单号进行匹配回显
                                if (abnormalOrderList.length == WaybillCodeModels.length) {
                                    WaybillCodeModels[0].IsErr = false;
                                    WaybillCodeModels[0].ErrMsg = "";
                                }
                            }

                            var model = {
                                Id: oItem.Id,
                                PlatformOrderId: oItem.PlatformOrderId,
                                LogicOrderId: oItem.LogicOrderId,
                                WaybillCodeModels: WaybillCodeModels
                            }
                            ManyCodeSendModel.push(model);
                        });

                        ManyCodeSendModel.forEach(function (item) {
                            var SameBatchCodeLength = item.WaybillCodeModels.filter(function (wItem) {
                                return wItem.IsLatestBatchCode == true;
                            });

                            ExpressBillNum += SameBatchCodeLength.length;

                            SameBatchCodeLength.forEach(function (wItem) {
                                if (wItem.IsErr == true && wItem.ErrMsg == "发货选择的数量和底单对不上") {
                                    AbnormalOrders++;
                                }
                            });
                        });

                        var isSupport = ExpressBillNum <= orders.length ? false : true;
                        // 没有快递单者走普通发货逻辑
                        if (ExpressBillNum == 0) {
                            ExpressBillNum = orders.length;
                            isSupport = false;
                        }
                        // 对所有选中的订单，检查是否存在任何一个订单是部分发货，则直接进入多单号发货弹窗
                        var isPartSend = false;
                        for (var i = 0; i < orders.length; i++) {
                            var orderItem = orders[i];
                            // 通过数组的some方法判断是否有部分发货
                            isPartSend = orderItem.SubOrders.some(function (subItem) {
                                return subItem.SendedCount && subItem.SendedCount > 0 && subItem.Count > subItem.SendedCount
                            });
                            if (isPartSend) {
                                // 一旦发现部分发货，进行批量发货
                                waitOrderModule.BatchSendManyV2(orders, true);
                                return;  // 结束函数，避免继续循环其他订单
                            }
                        }
                        // 如果没有部分发货的订单，执行常规发货操作
                        ShowSendLogisticDialog(ExpressBillNum, isSupport, isPdd, AbnormalOrders, ManyCodeSendModel);
                    }
                    // 开启多单号回传  异常单检测 -- 批量勾选订单发货时检验
                    if (commonModule.ManyCodeSendAway == "0") {
                        // 目前对接多单号回传的平台
                        var configs = commonModule.manyCodeSendConfigData;
                        if (configs) {

                            // 检验平台发货
                            var CheckPts = orders.map(function (item) {
                                return item.PlatformType
                            });

                            // 开启拼多多
                            var IsPdd = CheckPts.every(function (item) {
                                return item == "Pinduoduo" && configs.Pinduoduo
                            });

                            // 多平台
                            var IsManyPt = CheckPts.some(function (item) {
                                return (["TouTiao", "KuaiShou", "Taobao", "WxVideo", "XiaoHongShu", "Jingdong", "OwnShop"].indexOf(item) > -1 && configs.TouTiao) || (item == "AlibabaC2M" && configs.AlibabaC2M)|| (item == "TaobaoMaiCaiV2" && configs.TaobaoMaiCaiV2) || (item == "Pinduoduo" && configs.Pinduoduo) || (item == "YouZan" && configs.YouZan)
                            });

                            //支持多单号回传的平台
                            if (IsManyPt || IsPdd) {
                                SendCheckLogistic(IsPdd);
                            } else {
                                ShowSendLogisticDialog(orders.length);
                            }
                        } else {
                            ShowSendLogisticDialog(orders.length);
                        } 
                    } else {
                        ShowSendLogisticDialog(orders.length);
                    }
                }
                else {
                    sendConfirmDialog_for_mutil = layer.open({
                        type: 1,
                        title: title,
                        closeBtn: false,
                        btn: [btnText, "取消"],
                        shadeClose: false,
                        area: ['560px', height],
                        content: html,
                    skin: 'wu-dailog',
                        btn1: function () {
                            var nologisticModel = null;
                            if (isDontNeedLogistic) {
                                var temp = sl.checkNoLogisticInfo(orders)
                                if (temp == false) {
                                    sl.LockCurrenButton(false);
                                    return false;
                                }
                                else
                                    nologisticModel = temp;
                            }
                        // 开始发货
                            try {
                            // 遍历 orders 数组
                            orders.forEach(function (item) {
                                // 遍历 OrderTags 数组
                                if (item.OrderTags && item.OrderTags.length > 0) {
                                    item.OrderTags.forEach(function (tag) {
                                        // 如果 Tag 为 ChangeSku_Accept_Order
                                        if (tag.Tag === 'ChangeSku_Accept_Order' && tag.Platform === 'WxVideo') {
                                            // 将时间字符串转换为 Date 对象
                                            var expressPrintDate = new Date(item.ExpressPrintTime);
                                            var createDate = new Date(tag.CreateTime);
                                            // 如果 ExpressPrintTime 小于 CreateTime，就显示提示弹窗
                                            if (expressPrintDate.getTime() < createDate.getTime()) {
                                                layer.closeAll();
                                                var html = "";
                                                html += '<div class="n-font5">';
                                                html += '已选择 ' + orders.length + ' 个订单，其中' + wxAcceptOrderList.length + '个订单在打印快递单之后变更为“微信礼物已换款”，已变更的订单请重新打印快递单，是否跳过变更规格订单继续发货？';
                                                html += '</div>';
                                                layer.open({
                                                    type: 1,
                                                    title: '提示',
                                                    content: html,
                                                    area: '520px', //宽高
                                                    skin: 'n-skin',
                                                    success: function () { },
                                                    btn: ['跳过，继续', '取消'],
                                                    btn1: function (index) {
                                                        sl.doSend(filterAcceptOrderData, ext, template, nologisticModel, undefined, isFromMutilSend, callbackObj);
                                                        layer.close(index);
                                                    },
                                                    btn2: function (index) {
                                                        layer.close(index);
                                                    }
                                                });
                                            } else {
                                                sl.doSend(orders, ext, template, nologisticModel, undefined, isFromMutilSend, callbackObj);
                                            }
                                        } else {
                                            sl.doSend(orders, ext, template, nologisticModel, undefined, isFromMutilSend, callbackObj);
                                        }
                                    });
                                } else {
                                    sl.doSend(orders, ext, template, nologisticModel, undefined, isFromMutilSend, callbackObj);
                                }
                            });
                            } catch (e) {
                                sl.IsSending = false;
                                sl.LockCurrenButton(false);
                                console.error(e);
                                alert("发货异常，请联系我们：" + e.message + "\r\n异常信息：" + e.stack);
                            }
                            return false;
                        },
                        btn2: function () {
                            sl.LockCurrenButton(false);
                            return true;
                        }
                    });
                }
            }
            $("p[id^='nologistics-']").hide();
        }

        sendAction();
    }

    sl.TransferToAgentList = function (type, name) {
        var token = commonModule.getToken();
        var url = "/Partner/Index?shopId=" + name + "&token=" + token; // 店铺列表
        if (type == 1) // 商家列表
            url = "/Partner/MyAgent?mobile=" + name + "&token=" + token;
        else if (type == 3) //电子面单
            url = name;
        layer.closeAll();
        commonModule.OpenNewTab(url);
    }
    
    // 手工发货
    sl.manualSend = function (options) {
        var paramsData = {
            TemplateId: null,
            ExpressCompanyId: null,
            Orders: options.OrderData,
            ExcludeOrders: null,
            NoLogisticsInfoModel: '',
            IsSendPreCheck: false,
            IsContinueForStock: false,
            IdentcodeDatas: window.identcodeDatas,
            IsManual: options.IsManual,
            CompanyCode: options.CompanyCode,
        };
        common.Ajax({
            url: "/NewOrder/OnlineSendManualBatch",
            data: { model: paramsData },
            loadingMessage: "发货中...",
            success: function (response) {
                if (response.Success) {
                    var data = response.Data;
                    var hasErrorCount = data.Orders.some(function (item) {
                        return item.IsSuccess === false;
                    });
                    // 如果存在失败订单用弹窗显示失败原因
                    if (hasErrorCount) {
                        data.TotalCount = data.ErrorCount + data.SuccessCount;
                        data.allOrderNo = '';
                        var values = data.Orders.map(function (d) {
                            return d.OrderRequest.CustomerOrderId;
                        });
                        data.allOrderNo = values.join(',');
                        data.Orders = sl.onListFomat(data.Orders);
                        var tplt = $.templates("#manual_delivery_result_dialog");
                        var html = tplt.render(data);
                        layer.open({
                            type: 1,
                            title: "发货结果", // 标题
                            content: html,
                            offset: '100px',
                            area: '560px', // 宽高
                            skin: 'n-skin n-send-result',
                            btn: ['关闭'],
                        });
                    } else {
                        commonModule.w_alert({ type: 4, content: '发货成功' }); // 全部成功用toast提示
                        setTimeout(function () {
                            window.location.href = window.location.href;
                        },1500)
                    }
                } else {
                    commonModule.w_alert({ type: 2, content: response.Message });
                }
            }
        });
    }
    // 字段相同的数据排序
    sl.onListFomat = function (list) {
        var arr = [];
        var newList = [];
        var first = true;
        // if (list.length === 0) return
        list.forEach(function (item,index)  {
            if (arr.length === 0) {
                arr.push(item);
            } 
            for (var i = 0; i < arr.length; i++) {
                var item1 = arr[i];
                if (item1.ErrorMessage !== item.ErrorMessage || item1.ErrorMessage.indexOf(item.ErrorMessage) === -1) {
                    arr.push(item);
                }
            }
        })
        for(var i = 0; i< arr.length; i++){
            first = true;
            for(var j = 0; j < list.length; j++){
                if(list[j].ErrorMessage && arr[i].ErrorMessage && (arr[i].ErrorMessage === list[j].ErrorMessage || list[j].ErrorMessage.indexOf(arr[i].ErrorMessage) > -1)){
                    if (!first) {
                        list[j].ErrorMessage = '';
                    }
                    first = false;
                    newList.push(list[j])
                }
            }
        }
        return newList
    }

    // 参数 isFromMutilSend 为1表示源自抖店的上传多单号
    sl.doSend = function(orders, refundOrders, template, nologisticModel, isSendPreCheck, isFromMutilSend, callbackObj) {
        //排除已退款的订单
        var request = {
            TemplateId: template.Id,
            ExpressCompanyId: template.ExpressCompanyId,
            Orders: orders,
            ExcludeOrders: refundOrders,
            NoLogisticsInfoModel: nologisticModel,
            IsSendPreCheck: (isSendPreCheck == undefined ? true : isSendPreCheck),
            IsContinueForStock: $('#hfIsContinueForStock').val() == "1",  //部分订单项库存不足，是否继续
            IdentcodeDatas: window.identcodeDatas
        };
        console.log("发货请求参数：", request);
        orders.forEach(function (o) {
            o.PrintInfo = "";
        });

        sl.IsSending = true;
        sl.LockCurrenButton(true);

        
        function GetRequestModel(orders) { request.Orders = orders; return request;}

        var total_data_count = request.Orders.length; //发货的总订单数
        var callback_counter = 0;

        if (typeof OnlineSendBatchCount === 'undefined' || isNaN(OnlineSendBatchCount)) {
            OnlineSendBatchCount = 20;
        }        

        function SingleCallBack(data) {
            try {
                callback_counter++;
                var temp_count = (callback_counter * OnlineSendBatchCount);
                var temp_number = (temp_count / total_data_count) * 100;
                var bar_width = 0;
                if (temp_number < 15)
                    bar_width = temp_number.toFixed(0);
                else
                    bar_width = temp_number.toFixed(2);
                if (bar_width > 100) bar_width = 100;
                $('#div_pro_bar').css({ width: (bar_width + '%') });
                $('#div_bar_text').text(bar_width + '%');

                if (temp_count > total_data_count) temp_count = total_data_count;
                $('#sp_curr_number').text(temp_count);

            } catch (e) {
                var errorMsg = "单个请求回调异常》" + e.stack;
                console.log(errorMsg);
                common.JsExcptionLog("发货打印前端异常日志", errorMsg);
            }
        }

        function AllDoneCallback(allRsp, requestDatas) {
            var failedFirst;
            for (var i = 0; i < allRsp.length; i++) {
                if (!allRsp[i].Success) {
                    failedFirst = allRsp[i];
                    break;
                }
            }

            var newData = {};
            if (failedFirst != undefined && failedFirst != null) {
                if (failedFirst.Message == "10") { // 找出批量结果里面，是否还有 Message == "10" 结果，将他们聚合在一起
                    var removeLogicOrderIds = [];
                    var removeOrderItemIds = [];
                    for (var i = 0; i < allRsp.length; i++) {
                        if (!allRsp[i].Success && allRsp[i].Message == "10") {                            
                            Array.prototype.push.apply(removeLogicOrderIds, JSON.parse(allRsp[i].Data.removeLogicOrderIds));
                            Array.prototype.push.apply(removeOrderItemIds, JSON.parse(allRsp[i].Data.removeOrderItemIds));
                        }
                    }

                    if (removeLogicOrderIds.length > 0) failedFirst.Data.removeLogicOrderIds = JSON.stringify(removeLogicOrderIds);
                    if (removeOrderItemIds.length > 0) failedFirst.Data.removeOrderItemIds = JSON.stringify(removeOrderItemIds);
                }
                else if (failedFirst.Message == "12") { // 找出批量结果里面，是否还有 Message == "12" 结果，将他们聚合在一起
                    var failLogicOrderIds = "";
                    for (var i = 0; i < allRsp.length; i++) {
                        if (!allRsp[i].Success && allRsp[i].Message == "12") {
                            if (allRsp[i].Data && allRsp[i].Data != null && allRsp[i].Data != undefined && allRsp[i].Data != "") {
                                failLogicOrderIds+= "," + allRsp[i].Data;
                            }
                        }
                    }

                    if (failLogicOrderIds.length > 0) failedFirst.Data = failLogicOrderIds.replace(/^,+|,+$/, ''); //去掉首尾多余逗号
                }
                newData = failedFirst;
            }
            else {
                newData.Success = true;
                newData.Data = {
                    ErrorCount: 0,
                    SuccessCount: 0,
                    HasReceiverChangedError: false,
                    HasSendPreCheckError: false,
                    Orders: []
                };

                for (var i = 0; i < allRsp.length; i++) {
                    if (allRsp[i].Data && allRsp[i].Data.Orders) {
                        Array.prototype.push.apply(newData.Data.Orders, allRsp[i].Data.Orders);
                    }
                    newData.Data.ErrorCount += allRsp[i].Data.ErrorCount;
                    newData.Data.SuccessCount += allRsp[i].Data.SuccessCount;
                    // 合并错误信息,如果返回的结果集有1个是true，那么 HasReceiverChangedError 就是 true
                    if (allRsp[i].Data.HasReceiverChangedError && !newData.Data.HasReceiverChangedError) {
                        newData.Data.HasReceiverChangedError = true;
                    }
                    // 合并错误信息,如果返回的结果集有1个是true，那么 HasSendPreCheckError 就是 true
                    if (allRsp[i].Data.HasSendPreCheckError && !newData.Data.HasSendPreCheckError) {
                        newData.Data.HasSendPreCheckError = true;
                    }
                }
            }

            // 处理发货后的逻辑
            delivery(newData, orders, refundOrders, template, nologisticModel, isSendPreCheck, isFromMutilSend, callbackObj);
        }

        try {
            common.posts('/NewOrder/OnlineSend', request.Orders, GetRequestModel, SingleCallBack, AllDoneCallback, OnlineSendBatchCount);
        } catch (e) {
            var errorMsg = "批量发货发送请求异常->" + e.stack;
            common.JsExcptionLog("分批发货前端异常日志", errorMsg);
        }

        /*
       common.Ajax({
           // url: "/NewOrder/OnlineSend",
           url: "/NewOrder/OnlineSendBatch", 
           data: { model: request },
           loadingMessage: "发货中...",
           success: function (data) {
              
               if (data != null && data.Success && data.Data != null && data.Data.PollingKey != undefined && data.Data.PollingKey != null) {
                   // 轮询接口拿到结果
                   var loadingIdx = commonModule.LoadingMsg("发货中...");
                   var timerId = setInterval(function () {
                       commonModule.Ajax({
                           url: "/NewOrder/OnlineSendPolling",
                           data: { PollingKey: data.Data.PollingKey },
                           success: function (data) {
                               var flagv = data == null || !data.Success || data.Data == null;
                               if (flagv || data.Data.Status == 3) {
                                   layer.alert("发货失败，请稍后重试", { skin: 'wu-dailog' });
                                   clearInterval(timerId);
                                   layer.close(loadingIdx);
                                   return;
                               }
                               else if (data.Data.Status == 2) {
                                   clearInterval(timerId);
                                   layer.close(loadingIdx);
                                   delivery(data, orders, refundOrders, template, nologisticModel, isSendPreCheck, isFromMutilSend, callbackObj);
                               }
                           }
                       })
                   }, 800);
               }
               else {
                   //发货失败，有地址变更订单，提示弹窗
                   if (data.ErrorCode == '13' ) {
                       layer.closeAll();
                       sl.IngoreExReceiverChangeOrder = true;
                       sl.IsSending = true;
                       if (data.Data && data.Data.length == 0) {
                           sl.IngoreExReceiverChangeOrder = false;
                           sl.IsSending = false;
                       }
                        
                       var btn = ["忽略异常单,先操作其他订单", "取消"];
                       var dialogIndex = layer.open({
                           type: 1,
                           title: "打印提示",
                           content: $("#dy_qh_warn"),
                           btn: btn,
                           shadeClose: true,
                           skin: 'n-skin',
                           id: 'receiverChangeLayer',
                           area: ['600px'],
                           success: function () {
                               $('#dy_qh_warn').css({ padding: '0' });
                           },
                           btn1: function () {
                               layer.close(dialogIndex);
                               if (data.Data && data.Data.length > 0) {
                                   $(".batch-send-btn").click();
                                   return false;
                               }
                               sl.IsSending = false;
                               sl.IngoreExReceiverChangeOrder = false;
                           },
                           btn2: function () {
                               
                               layer.close(dialogIndex);
                               window.location.href = window.location.href;
                           }
                       })
                       //cancelWaybillCodeFunc(data.Data, true);
                       return;
                   }
                   delivery(data, orders, refundOrders, template, nologisticModel, isSendPreCheck, isFromMutilSend, callbackObj);
               }
           },
           error: function () {
               sl.LockCurrenButton(false);
               if (rsp.status == 401) {
                   layer.msg("暂无权限，请联系管理员");
               } else {
                   layer.alert("网络错误，请稍后重试", { skin: 'wu-dailog' });
               }
           }
       });
       */
    }

    // 提取发货方法，用于回调使用
    function delivery(data, orders, refundOrders, template, nologisticModel, isSendPreCheck, isFromMutilSend, callbackObj) {
        sl.IsSending = false;
        sl.LockCurrenButton(false);
        if (isFromMutilSend == undefined || isFromMutilSend != 1) {
            layer.closeAll();
        }
        else {
            layer.close(sendConfirmDialog_for_mutil);
        }
        if (data.Success) {
            try {
                var model = data.Data;
                model.Template = template;
                var hideOrderCount = 0;
                //是否继续发多运单号订单
                var isBatchMultiSendMany = callbackObj && callbackObj.IsSendManyOrders;
                if (model.ErrorCount > 0) {

                    //判断是否有发货预检查的报错，预检查报错用户可以确认继续发货，继续发货不再预检查
                    var btns = ["关闭"];
                    if (model.HasSendPreCheckError)
                        btns = ["坚持发货", "关闭"];
                    if (isBatchMultiSendMany)
                        btns.unshift("继续上传多单号");

                    var preCheckReSend = function () {

                        if (model.HasSendPreCheckError) {
                            //坚持发货，将错误订单找出，继续发货
                            var reSendOrders = [];
                            for (var i = 0; i < model.Orders.length; i++) {
                                var ro = model.Orders[i];
                                if (ro.IsSuccess)
                                    continue;
                                for (var j = 0; j < orders.length; j++) {
                                    var o = orders[j];
                                    if (ro.OrderRequest.PlatformOrderId == o.PlatformOrderId && ro.OrderRequest.ShopId == o.ShopId) {
                                        reSendOrders.push(o);
                                        break;
                                    }
                                }
                            }
                            // 继续发货，不预检查
                            sl.doSend(reSendOrders, refundOrders, template, nologisticModel, false, isFromMutilSend, callbackObj);
                        }
                        else
                            layer.closeAll();
                    }
                    //显示详细的错误消息
                    //var dialog = $.templates("#send-logistic-error-dialog-tmpl");
                    var dialog = $.templates("#new_send_fail_warn");
                    var html = dialog.render(model);
                    var layerIndex = layer.open({
                        type: 1,
                        title: "发货结果",
                        //closeBtn: 1,
                        btn: btns,
                        shadeClose: true,
                        area: ['1000px', '460px'],
                        content: html,
                        zIndex: 100000000,
                        yes: function () {
                            if (isBatchMultiSendMany) {
                                layer.close(layerIndex);
                            }
                            else
                                preCheckReSend();
                        },
                        btn2: function () {
                            if (isBatchMultiSendMany)
                                preCheckReSend();
                            else
                                layer.closeAll();
                        },
                        cancel: function () {
                            layer.closeAll();
                        }
                    });
                }
                else {
                    $('#hfIsContinueForStock').val('0');
                    if (sl.IngoreExReceiverChangeOrder == false && (isFromMutilSend == undefined || isFromMutilSend != 1)) {
                        $("#SeachConditions").click();
                    }
                    commonModule.w_alert({ type: 4, content: '发货成功' });
                    $("#uploadPdd-many-drawer").removeClass("active");
                    $("body").css("overflow", "auto");
                    if (callbackObj && typeof callbackObj.CallBackFunc == "function") {
                        callbackObj.CallBackFunc(true);
                    }
                }
                if (model.SuccessCount > 0) {
                    $('#hfIsContinueForStock').val('0');
                    if (sl.IngoreExReceiverChangeOrder == false && (isFromMutilSend == undefined || isFromMutilSend != 1)) {
                        $("#SeachConditions").click();
                    }
                    else {
                        // 抖店回传多单号
                        //--- 更新行数据 开始 ---
                        for (var i = 0; i < model.Orders.length; i++) {
                            var ro = model.Orders[i];
                            if (!ro.IsSuccess)
                                continue;

                            var index = $(".order-chx[data-pid='" + ro.LogicOrder.LogicOrderId + "']").attr("data-index");
                            if (!isNaN(index)) {
                                var row = orderTableBuilder.rows[index];
                                if (row != undefined) {
                                    var isAllSended = 1;
                                    row.SubOrders.forEach(function (subItem, sIndex) {
                                        var sendedCount = subItem.SendedCount;
                                        var thisSendedCount = 0;//本次数量
                                        orders.forEach(function (o, index) {
                                            if (o.LogicOrderId == ro.LogicOrder.LogicOrderId && o.MultiPackSendModels != undefined && o.MultiPackSendModels.length > 0) {
                                                //各包裹累计数量
                                                o.MultiPackSendModels.forEach(function (m, mIndex) {
                                                    if (m.OrderItemCode == subItem.OrderItemCode) {
                                                        sendedCount = parseInt(sendedCount) + parseInt(m.Count);
                                                        thisSendedCount = parseInt(thisSendedCount) + parseInt(m.Count);
                                                    }
                                                })
                                            }
                                        })
                                        if (sendedCount >= subItem.Count) {
                                            subItem.Status = "waitbuyerreceive";
                                        }
                                        else {
                                            isAllSended = 0;
                                        }
                                        if (thisSendedCount > 0) {
                                            subItem.PrintState = 1;
                                        }
                                        subItem.SendedCount = sendedCount;
                                    });
                                    if (isAllSended == 1) {
                                        row.ErpState = "sended";
                                    }
                                    row.WaybillCodes.forEach(function (waybillItem, sIndex) {
                                        orders.forEach(function (o, index) {
                                            if (o.LogicOrderId == ro.LogicOrder.LogicOrderId && o.MultiPackSendModels != undefined && o.MultiPackSendModels.length > 0) {

                                                o.MultiPackSendModels.forEach(function (m, mIndex) {
                                                    if (m.WaybillCode == waybillItem.WaybillCode) {
                                                        waybillItem.Status = 3;
                                                    }
                                                })
                                            }
                                        })
                                    });
                                    orderTableBuilder.refreshRow(row);
                                    if (sl.IngoreExReceiverChangeOrder)
                                        orderTableBuilder.SetRowCheck(row, false);
                                }
                            }
                        }
                        //--- 更新行数据 结束 ---
                    }
                }

                // 发货接口兜底检测到地址变更的订单打标显示异常单
                checkReceiverChangeAfterSended(model);
            } catch (e) {

                var errorMsg = "发货成功后前端操作异常》" + e.stack;
                //console.log(errorMsg);
                common.JsExcptionLog("发货前端异常日志", errorMsg);

            }

        }
        else {
            var errorCode = data.Message;
            var errorData = data.Data;
            var errorType = 1;
            var errorMsg = "";
            switch (errorCode) {
                case "10":
                    try {
                        var removeLogicOrderIds = JSON.parse(errorData.removeLogicOrderIds);
                        var removeOrderItemIds = JSON.parse(errorData.removeOrderItemIds);
                        layer.open({
                            type: 1,
                            title: "确定",
                            content: '<div style="width:500px;height:150px;padding:20px;box-sizing: border-box;"><span style="font-size:16px;line-height:25px">您所勾选的订单中包含部分商品库存不足，已为您跳过缺货商品，是否继续发货其他商品。</span></div>',
                            area: '480px',
                            btn: ['继续发货', '返回'],
                            skin: 'wu-dailog',
                            yes: function () {
                                layer.closeAll();
                                cancelSelected(removeLogicOrderIds, removeOrderItemIds);//取消无货的订单及订单项
                                $('#hfIsContinueForStock').val('1');//部分无货是否继续设为继续

                                reSendOrders = otb.getSelections();

                                if (reSendOrders.length == 0) {
                                    layer.alert("过滤后已无订单，请重新选择您要发货的订单", { skin: 'wu-dailog' });
                                }
                                else {
                                    sl.doSend(reSendOrders, refundOrders, template, nologisticModel, false, callbackObj);
                                }

                                //sl.send();//重新提交
                            },
                            cancel: function () {
                                $('#hfIsContinueForStock').val('0');
                                layer.closeAll();
                            }
                        });

                        /*
                        if (errorData.msg.indexOf("ProductName") > -1) {
                            var msgData = JSON.parse(errorData.msg);
                            var tempdata = { Items: msgData };
                            var dialog = $.templates("#Stock-error-dialog-tmpl");
                            var errorHtml = dialog.render(tempdata);
                            errorType = 2;
                            errorMsg = errorHtml;
                        } else {
                            errorMsg = errorData.msg;
                        }
                        */

                    } catch (e) {
                        errorMsg = errorData.msg;
                    }
                    break;

                case "12"://路径流检查：解绑处理中
                    try {
                        //debugger;
                        errorMsg = "存在【解绑处理中】的商家订单，请跟商家确认是否取消了绑定";
                        var removeLogicOrderIds = errorData.split(',');

                        layer.open({
                            type: 1,
                            title: "忽略异常订单提示", //不显示标题
                            content: '<div style="width:500px;box-sizing: border-box;"><span style="font-size:16px;line-height:35px">系统检测到您勾选的订单中包含商家处理中解绑代发订单，不满足代发订单打印发货条件，避免打印发货失败，是否暂时忽略该类订单操作？</span></div>',
                            skin:'wu-dailog',
                            area: ['500'], //宽高
                            btn: ['跳过异常订单，继续操作', '取消'],
                            yes: function () {
                                layer.closeAll();
                                cancelSelected(removeLogicOrderIds, "");//取消无货的订单及订单项
                                reSendOrders = otb.getSelections();
                                //debugger;
                                if (reSendOrders.length == 0) {
                                    layer.alert("过滤后已无订单，请重新选择您要发货的订单", { skin: "wu-dailog" });
                                }
                                else {
                                    sl.doSend(reSendOrders, refundOrders, template, nologisticModel, false, callbackObj);
                                }
                            },
                            cancel: function () {
                                layer.closeAll();
                            }
                        });
                    }
                    catch (e) {
                        errorMsg = errorData.msg;
                    }
                    break;
                default:
            }
            if (errorCode != "10" && errorCode != "12") {
                if (errorData == null)
                    errorMsg = errorCode || "";
                else
                    errorMsg = errorData.msg || "";
                if (errorType == 2) {
                    layer.open({
                        type: 1,
                        title: false,
                        closeBtn: 1,
                        skin: 'layui-layer-rim', //加上边框
                        area: ['600px', '350px'],
                        shadeClose: true,
                        content: errorMsg
                    });
                }
                else
                    layer.alert("发货失败：" + errorMsg, { skin: 'wu-dailog' });
            }
        }
    }

    function checkReceiverChangeAfterSended(model) {

        // 发货接口兜底检测到地址变更的订单打标显示异常单
        if (model.HasReceiverChangedError) {
            for (var i = 0; i < model.Orders.length; i++) {
                var ro = model.Orders[i];
                if (ro.IsSuccess || ro.ReceiverIsChange == false) {
                    orderTableBuilder.SetRowCheck(row, false);
                    continue;
                }

                var index = $(".order-chx[data-pid='" + ro.LogicOrder.LogicOrderId + "']").attr("data-index");
                if (!isNaN(index)) {
                    var row = orderTableBuilder.rows[index];
                    row.ReceiverIsChange = true;
                    orderTableBuilder.refreshRow(row);
                    orderTableBuilder.SetRowCheck(row, true);//勾选地址变更的订单
                }
            }
        }

        if (sl.IngoreExReceiverChangeOrder) {
            // 选中所有地址更新订单，重新确认是否回收单号重新打印新单号再发货
            var len = sl.ReceiverChangeOrders.length;
            for (var i = 0; i < len; i++) {
                var rowIndex = sl.ReceiverChangeOrders[i].Index;
                var row = orderTableBuilder.rows[rowIndex];
                if (row.ReceiverIsChange)
                    orderTableBuilder.SetRowCheck(row, true);//勾选地址变更的订单
                else
                    orderTableBuilder.SetRowCheck(row, false);
            }
            var checkOrders = orderTableBuilder.getSelections();
            // 发货后存在异常单+发货前忽略的异常单，重新弹窗确认是否回收打印新单号发货
            cancelWaybillCodeFunc(checkOrders, true);
            sl.IngoreExReceiverChangeOrder = false;
            sl.ReceiverChangeOrders = [];
        }
    }

    function cancelWaybillCodeFunc(receiverChangeOrders, isSended) {
        if (!receiverChangeOrders || receiverChangeOrders.length == 0)
            return;

        //var html = $.templates("#dy_qh_warn").render({});
        var btn = ["回收异常单号继续重新打印异常单", "忽略异常单，先操作其他订单"];
        if (isSended)
            btn = ["回收异常单号继续重新打印异常单", "暂不发货"];

        var dialogDoorTips = layer.open({
            type: 1,
            title: "打印提示",
            content: $("#dy_qh_warn"),
            btn: btn,
            area: ['560px'],
            skin: 'wu-dailog',
            btn1: function () {
                layer.close(dialogDoorTips);
                var dialogDoorTips2 = layer.open({
                    type: 1,
                    title: "回收异常单号",
                    content: $("#dy_hs_percentWarn"),
                    shadeClose: true,
                    area: ['500px'],
                    skin: 'wu-dailog',
                });

                var func = function () {
                    var cancelWaybillCodes = [];
                    var len = receiverChangeOrders.length;
                    for (var i = 0; i < len; i++) {
                        var checkOrder = receiverChangeOrders[i];
                        var row = otb.rows[checkOrder.Index];
                        var waybillCodes = row.WaybillCodes || [];
                        for (var j = 0; j < waybillCodes.length; j++) {
                            var w = waybillCodes[j];
                            var templateId = w.TemplateId || 0;
                            var waybillCodeId = w.WaybillCodeId || 0;
                            var waybillCode = w.WaybillCode || "";
                            var orderId = checkOrder.LogicOrderId || "";
                            var data = { row: row, templateId: templateId, wcRecycleViewModel: [{ WaybillCodeId: waybillCodeId, WaybillCode: waybillCode, OrderId: orderId }] };
                            cancelWaybillCodes.push(data);
                        }
                    }

                    // 回收单号错误信息集合
                    var errMsgList = [];
                    var errMsgStr = "";
                    var wlen = cancelWaybillCodes.length;
                    for (var i = 0; i < wlen; i++) {

                        var row = cancelWaybillCodes[i].row;
                        var wdata = {};
                        wdata.templateId = cancelWaybillCodes[i].templateId;
                        wdata.wcRecycleViewModel = cancelWaybillCodes[i].wcRecycleViewModel;
                         
                        commonModule.Ajax({
                            url: '/WaybillCodeList/CancelWaybillCode',
                            type: "POST",
                            loading: false,
                            async: false,
                            data: wdata,
                            success: function (rsp) {
                                if (!rsp.Success) {
                                    var errMsg = rsp.Message || "";
                                    errMsgList.push({ WaybillCodeId: wdata.wcRecycleViewModel[0].WaybillCodeId, row: row });
                                    errMsgStr += (i + 1 + ':') + errMsg + "\r\n";
                                }
                            },
                            error: function (rsp) {
                                if (rsp.status == 401) {
                                    layer.msg("暂无权限，请联系管理员");
                                } else {
                                    layer.msg(rsp.message);
                                }
                            }
                        });
                        // 设置回收单号进度百分比
                        var percent = ((i + 1) * 1.0 / wlen) * 100;
                        $("#dy_hs_percentWarn .dgjNew-progress-bar").css("width", percent + "%");
                    }

                    layer.close(dialogDoorTips2);
                    if (errMsgList.length > 0) {
                        layer.open({
                            type: 1,
                            title: "接口回收失败提示",
                            content: $("#copy_hs_warnTarget"),
                            shadeClose: true,
                            area: ['500px'],
                            btn: ["复制失败运单号联系客服", "等待自动回收"],
                            skin: "wu-dailog",
                            success: function () {
                                $("#copy_hs_warnTarget_title").html(errMsgStr);
                            },
                            btn1: function () {
                                commonModule.CopyText("#copy_hs_warnTarget_title")
                            },
                            btn2: function () {
                                var errWaybillCodeIds = "";
                                for (var i = 0; i < errMsgList.length; i++) {
                                    errWaybillCodeIds += errMsgList[i].WaybillCodeId + ',';
                                }

                                // 等待自动回收
                                commonModule.Ajax({
                                    url: '/WaybillCodeList/AutoCancelWaybillCode',
                                    type: "POST",
                                    loading: false,
                                    async: false,
                                    data: { "waybillCodeIds": errWaybillCodeIds },
                                    success: function (rsp) {
                                        if (rsp.Success) {
                                            layer.msg("运单号回收成功");
                                            for (var i = 0; i < errMsgList.length; i++) {
                                                var row = errMsgList[i].row;
                                                row.WaybillCodes = [];
                                                orderTableBuilder.refreshRow(row);
                                            }
                                        }
                                        else
                                            layer.comfirm(rsp.Message || "", { icon: 2 });
                                    }
                                });
                            }
                        });

                        //console.log("errMsgStr", errMsgStr);
                        //layer.alert(errMsgStr, { icon: 2 }); // 显示错误信息

                    }
                    else {
                        layer.close(dialogDoorTips2);
                        // 回收成功
                        var dialogDoorTips3 = layer.open({
                            type: 1,
                            title: "回收结果",
                            content: $("#dy_hs_resultTarget"),
                            shadeClose: true,
                            area: ['480px'],
                            skin: 'wu-dailog',
                            btn: ["打印异常单"],
                            btn1: function () {
                                layer.close(dialogDoorTips3);
                                expressPrinter.print(receiverChangeOrders, 'Normal');
                                //$(".express-print-btn").click(); // 重新打印异常单
                            }
                        });
                    }
                }
                
                func();

                return true;
            },
            btn2: function () {
                if (isSended)
                    layer.close(dialogDoorTips);
                else {
                    //过滤异常单，先处理其他正常订单
                    orderTableBuilder.FilterReceiverChangeOrder();
                    sl.IngoreExReceiverChangeOrder = true;
                    sl.ReceiverChangeOrders = receiverChangeOrders;
                    $(".batch-send-btn").click();
                }
            }
        });
    }
    ///追加包裹（回传多单号）
    sl.appendpack = function (orders, platformType) {
        if (!orders || orders.length == 0) {
            layer.msg("请至少选择一个订单和包裹");
        }
        else {
            try {
                doAppendPack(orders, platformType);
            } catch (e) {
                console.error(e);
                alert("回传多单号异常，请联系我们：" + e.message + "\r\n异常信息：" + e.stack);
            }
        }
    }

    ///追加包裹（回传多单号）
    function doAppendPack(orders, platformType) {
        var request = {
            Orders: orders
        };
        common.Ajax({
            url: "/NewOrder/AppendPack",
            data: { model: request },
            loadingMessage: "处理中...",
            success: function (data) {
                if (data.Success) {
                    try {
                        var model = data.Data;

                        if (model.ErrorCount > 0) {

                            //判断是否有发货预检查的报错，预检查报错用户可以确认继续发货，继续发货不再预检查
                            var btns = ["关闭"];

                            //显示详细的错误消息
                            var dialog = $.templates("#appendpack-logistic-error-dialog-tmpl");
                            var html = dialog.render(model);
                            layer.open({
                                type: 1,
                                title: "回传多单号结果",
                                //closeBtn: 1,
                                btn: btns,
                                shadeClose: true,
                                area: ['700px', '460px'],
                                content: html,
                                yes: function () {
                                    layer.closeAll();
                                },
                                btn2: function () {
                                    layer.closeAll();
                                },
                                cancel: function () {
                                    layer.closeAll();
                                }
                            });
                        }
                        
                        if (model.SuccessCount > 0) {
                            layer.msg("回传成功", { time: 500 });

                            if (platformType != undefined && platformType == "Pinduoduo") {
                                waitOrderModule.closePddManyDrawer();
                            } else {
                                waitOrderModule.closePtManyDrawer();
                            }
                            layer.closeAll();
                            $("#SeachConditions").click();

                            //--- 更新行数据 开始 ---
                            for (var i = 0; i < model.Orders.length; i++) {
                                var ro = model.Orders[i];
                                if (!ro.IsSuccess)
                                    continue;

                                var index = $(".order-chx[data-pid='" + ro.LogicOrder.LogicOrderId + "']").attr("data-index");
                                if (!isNaN(index)) {
                                    var row = orderTableBuilder.rows[index];
                                    if (row != undefined) {
                                        row.WaybillCodes.forEach(function (waybillItem, sIndex) {
                                            orders.forEach(function (o, index) {
                                                if (o.LogicOrderId == ro.LogicOrder.LogicOrderId && o.MultiPackSendModels != undefined && o.MultiPackSendModels.length > 0) {
                                                    
                                                    o.MultiPackSendModels.forEach(function (m, mIndex) {
                                                        if (m.WaybillCode == waybillItem.WaybillCode) {
                                                            waybillItem.Status = 3;
                                                            waybillItem.SendType = 10;
                                                        }
                                                    })
                                                }
                                            })
                                        });
                                        orderTableBuilder.refreshRow(row);
                                    }
                                }
                            }
                                //--- 更新行数据 结束 ---
                        }

                    } catch (e) {

                        var errorMsg = "回传多单号成功后前端操作异常》" + e.stack;
                        //console.log(errorMsg);
                        common.JsExcptionLog("回传多单号前端异常日志", errorMsg);

                    }

                }
                else {
                    var errorCode = data.Message;
                    var errorData = data.Data;
                    var errorType = 1;
                    var errorMsg = "";

                    if (errorData == null)
                        errorMsg = errorCode || "";
                    else
                        errorMsg = errorData.msg || "";
                    if (errorType == 2) {
                        layer.open({
                            type: 1,
                            title: false,
                            closeBtn: 1,
                            skin: 'layui-layer-rim', //加上边框
                            area: ['600px', '350px'],
                            shadeClose: true,
                            content: errorMsg
                        });
                    }
                    else {
                        if (errorMsg == "") {
                            layer.alert(errorCode);
                        }
                        else
                            layer.alert("回传多单号失败：" + errorMsg);
                    }
                    
                }
            }
        });
    }

    //取消选择
    function cancelSelected(logicOrderIds, orderItemIds) {
        if (logicOrderIds != null && logicOrderIds != undefined) {
            for (var i = 0; i < logicOrderIds.length; i++) {
                var logicOrderId = logicOrderIds[i];
                var obj = $(".order-chx[data-pid='" + logicOrderId + "']");
                obj.prop("checked", false);
                orderTableBuilder.CheckOrderBind(obj);
            }
        }

        if (orderItemIds != null && orderItemIds != undefined) {
            for (var i = 0; i < orderItemIds.length; i++) {
                var orderItemId = orderItemIds[i];
                var obj = $(".orderitem-chx[data-id='" + orderItemId + "']");
                obj.prop("checked", false);
                orderTableBuilder.OrderProductItemSeletedHandler(obj, obj.attr("data-parent-id"), orderItemId);
            }
        }
    }

    sl.changeNologisticsOption = function changeNologisticsOption($this, isSingleOrder) {
        var condition = $($this).val();
        $("p[id^='nologistics-']").hide();
        $("#nologistics-remarks-span").hide();
        $("#nologistics-remarks-p").show();

        if (condition == 1 || condition == 3) {
            $("#nologistics-noLogisticsName-p").show();
            $("#nologistics-noLogisticsTel-p").show();
        }
        else if (condition == 2 && isSingleOrder) {
            $("#nologistics-noLogisticsBillNo-p").show();
        } else if (condition == 5) {
            $("#nologistics-remarks-span").show();
        }

    }
    $(document).ready(function () {
        $(".batch-send-btn-nologistic").unbind("click").bind("click", function () {
            sl.send(true);
        });
    });

    sl.newSendfailDailg = function () {
        var dialog = $.templates("#new_send_fail_warn");
        var dialogData = {};
        var html = dialog.render(dialogData);
        layer.open({
            type: 1,
            title: "发货结果", //不显示标题
            content: html,
            area: '1000px', //宽高
            btn: false,
            success: function () {
            }

        });
    }
    var isDownImg = false;
    sl.newSendailWarnHelp = function () {
        var dialog = $.templates("#new_send_fail_imgHelp");
        var html = dialog.render();
        layer.open({
            type: 1,
            title: false, //不显示标题
            content: html,
            area: '1040px', //宽高
            btn: false,
            zIndex: 900000000,
            skin:'newSendailWarnHelpSkin',
            success: function () {
            },
            cancel: function () {
                isDownImg = false;
            }

        });
    }
    sl.copyImg = function (url) {
        if (!isDownImg) {
            isDownImg = true
            var image = new Image();
            image.crossOrigin = "Anonymous";
            image.onload = function () {
                var canvas = document.createElement("canvas");
                canvas.width = image.width;
                canvas.height = image.height;
                var context = canvas.getContext("2d");
                context.drawImage(image, 0, 0);
                var dataURL = canvas.toDataURL("image/png");
                var a = document.createElement("a");
                var event = new MouseEvent("click");
                a.download = name || "默认";
                a.href = dataURL;
                a.dispatchEvent(event);
            };
            image.src = url;
        } else {
            layer.msg('图片已下载！')
        }      
    }
    sl.urlPartner = function (ShopId) {
        var hrefstr = common.rewriteUrl("/Partner/Index?shopId=" + ShopId);
        hrefstr = encodeURI(common.dbnameToAjaxUrl(hrefstr)); //参数携带中文需要两次编码
        window.open(hrefstr);
    }

    /*
     * 检查是否包含顺丰包邮商品的订单
     */
    sl.IsExistSfFreeShippingOrder = function (order, isOrder) {
        if (!order.OrderTags || order.OrderTags.length === 0) {
            return false;
        }

        var allTags = order.OrderTags;

        if (isOrder) {
            // 判断订单维度的标签
            return commonModule.HasTagOiCode(allTags, order.CustomerOrderId, 'sf_free_shipping', 'Order') ||
                commonModule.HasTagOiCode(allTags, order.CustomerOrderId, 'sf_door_shipping', 'Order');
        } else {
            // 判断订单项维度的标签
            for (var i = 0; i < order.Items.length; i++) {
                if (commonModule.HasTagOiCode(allTags, order.Items[i].OrderItemCode, 'sf_free_shipping', 'OrderItem')) {
                    return true;
                }
            }
            return false;
        }
    }

    sl.LockCurrenButton = function (locked) {
        var element = sl.LockButton;
        if (element) {
            if (element.is("button")) {
                if (locked) {
                    element.addClass("layui-btn-disabled").prop('disabled', true);
                    element.css("cursor", "no-drop");
                } else {
                    element.removeClass("layui-btn-disabled").prop('disabled', false);
                    element.css("cursor", "");
                    sl.LockButton = null
                }
            }
            else if (element.is("li")) {
                if (locked) {
                    element.css("color", "lightgray");
                    element.css("cursor", "no-drop");
                    element.css("pointer-events", "none");
                } else {
                    element.css("color", "#666");
                    element.css("cursor", "");
                    element.css("pointer-events", "");
                    sl.LockButton = null
                }
            }
            else if (element.is("span")) {
                if (locked) {
                    element.css("color", "lightgray");
                    element.css("cursor", "no-drop");
                    element.css("pointer-events", "none");
                } else {
                    element.css("color", "#666");
                    element.css("cursor", "");
                    element.css("pointer-events", "");
                    sl.LockButton = null
                }
            }
            else if (element.is("div")) {
                if (locked) {
                    element.css("color", "lightgray");
                    element.css("cursor", "no-drop");
                    element.css("pointer-events", "none");
                } else {
                    element.css("color", "#666");
                    element.css("cursor", "");
                    element.css("pointer-events", "");
                    sl.LockButton = null
                }
            }
        }
    }


    //#region [数码产品]识别码填充=》发货
    sl.IdentificationCode = function (checkOrders, callback) {
        var isSkipConfirmDialog = false;

        var manyWybOrders = new Array();
        var manyWybOrderIds = [];
        var cloneOrders = JSON.parse(JSON.stringify(checkOrders));
        var hasIdentificationCodeRow = false;
        cloneOrders.forEach(function (item, index) {
            // 是否支持数码产品的平台
            if (commonModule.DigitalProductPlatformType.indexOf(item.PlatformType) == -1) {
                return true;
            }
            var subOrders = new Array();
            item.SubOrders.forEach(function (sitem, sindex) {
                if (!sitem.checked) {
                    return true;
                }
                // 存储标识码，有两种方法，一种是按淘宝的做法存在ExtAttr5字段，一种就是根据标签来存储（推荐）
                var firstTagValue = common.FirstTagValue(sitem.OrderItemTags, "DigitalProduct", "OrderItem", sitem.OrderItemCode);
                if (firstTagValue) {
                    var identcodesplit = firstTagValue.split(':');
                    sitem.ExtAttr5 = identcodesplit[0];
                    sitem.ExtAttr5Value = identcodesplit[1];
                } else {
                    sitem.ExtAttr5 = null;
                }

                var needIdentcodeEnum = parseInt(sitem.ExtAttr5);
                if (!isNaN(needIdentcodeEnum) && needIdentcodeEnum > 0) {//有需要输识别码的
                    hasIdentificationCodeRow = true;
                    item.HasIdentificationCodeRow = true;
                    subOrders.push(sitem);
                }
            });
            if (subOrders.length > 0) {
                item.SubOrders = subOrders;
                manyWybOrderIds.push(item.Id);
                manyWybOrders.push(item);
            }
        });
        if (!hasIdentificationCodeRow) {
            callback(isSkipConfirmDialog);
            return;
        }

        // 上传识别码
        var upidentcodeFunc = function () {
            var template = tmpModule.GetCurrentTemplate();
            var renderData = { manyWybOrders: manyWybOrders, Template: template, AllOrderNum: checkOrders.length };
            var dialog = $.templates("#identificationCode_tmpl")
            var html = dialog.render(renderData);
            layer.open({
                type: 1,
                title: "数码产品设备识别码", //不显示标题
                content: html,
                area: "680px", //宽高
                offset: '10px',
                btn: ['发货', '取消'],
                yes: function (laydialogIndex) {
                    var identcodeDatas = new Array();
                    $("#identifyingCodeDailog [name=Identcode]").each(function (item, index) {
                        var t = $(this);
                        var dataItem = {
                            Identcode: t.val(),
                            PlatformOrderId: t.attr("PlatformOrderId"),
                            LogicOrderId: t.attr("LogicOrderId"),
                            ShopId: t.attr("ShopId"),
                            IdentcodeEnum: t.attr("IdentcodeEnum"),
                            OrderItemCode: t.attr("OrderItemCode"),
                            LogicOrderItemId: t.attr("LogicOrderItemId")
                        };
                        identcodeDatas.push(dataItem);
                    });
                    var isError = false;
                    identcodeDatas.forEach(function (item, index) {
                        if (!item.Identcode || item.Identcode.trim().length == 0) {
                            $(".identifyingCode-input-" + item.LogicOrderItemId).addClass('identifyingCode-input-empty');
                            $(".identifyingCode-input-empty-tips-" + item.LogicOrderItemId).show();
                            isError = true;
                        }
                    });
                    if (!isError) {
                        window.identcodeDatas = identcodeDatas;
                        isSkipConfirmDialog = true;
                        callback(isSkipConfirmDialog); //识别码发货是直接发货，无分包发货
                        layer.close(laydialogIndex);
                    }
                },
                cancel: function (laydialogIndex) {
                    layer.close(laydialogIndex);
                }
            });
        }

        // 当选中的订单包含3c订单，询问是否跳过数码产品
        if (checkOrders.length != manyWybOrders.length) {
            var msgHtml = '<div style="font-size:14px;line-height:25px;padding:15px;">';
            msgHtml += '已选择<span class="black-weighted-span">' + checkOrders.length + '</span>个订单，其中<span class="black-weighted-span">' + manyWybOrders.length + '</span>个订单包含数码产品，发货需要上传设备识别码。';
            //msgHtml += '<p style="font-size:12px;color:#999999;padding-top:15px">说明：平台会根据<span style="font-weight:600;">开通快递申请的发货地址</span>，该快递线路存在异常，可能出现无法获取运单号、揽收派送严重超时，甚至停运风险，请使用平台推荐快递发货。<a style="color:#3aadff;" href="http://dgjapp.com/newHelpsShow.html?id=1685671295998" target="_blank">了解详情</a></p>';
            msgHtml += '</div>';
            layer.open({
                type: 1,
                title: "询问",
                content: msgHtml,
                btn: ["跳过，继续", "上传识别码"],
                area: ['450px', '200px'],
                btn1: function (laydialogIndex) {
                    layer.close(laydialogIndex);
                    callback(isSkipConfirmDialog, manyWybOrderIds); //跳过3c订单，操作普通订单
                    return;
                },
                btn2: function (laydialogIndex) {
                    layer.close(laydialogIndex);
                    upidentcodeFunc();
                    return;
                }
            });
        } else {
            upidentcodeFunc();
            return;
        }
    }

    sl.showidentifyingCode = function () {
        $(this).toggleClass('active').closest('.identifyingCode-table').toggleClass('active');
        if ($(this).hasClass("active")) {
            $(this).find('.moreTitle').text("收起详情");
        } else {
            $(this).find('.moreTitle').text("展开详情");
        }
    }

    sl.showBatechIdentifyingCode = function () {
        var that = this;
        var html = '<div style="padding:15px;width:400px;box-sizing:border-box;"><textarea id="identificationCode_textarea" style="width:100%;height:130px" placeholder="每一行个识别码"></textarea></div>';
        layer.open({
            type: 1,
            title: "批量操作", //不显示标题
            content: html,
            area: '400px', //宽高
            zIndex: 100000000000,
            btn: ['确定'],
            yes: function (laydialog) {
                var allImei = $("#identificationCode_textarea").val().trim();
                var textareaValArray = allImei.split(/\n/g);
                var $input = $(that).prev();
                var allImeiInput = $input.prev();
                allImeiInput.val(allImei);
                if (textareaValArray.length > 1) {
                    $input.val(textareaValArray[0] + "...")
                } else {
                    $input.val(textareaValArray[0])
                }
                $input.removeClass('identifyingCode-input-empty');
                $(".identifyingCode-input-empty-tips-" + $input.attr('LogicOrderItemId')).hide();
                layer.close(laydialog);
            },
            cancel: function () { }
        });

    }

    sl.OnkeyupIdentificationCode = function (that) {
        var that = this;
        $(that).prev().val($(that).val());
        $(that).removeClass('identifyingCode-input-empty');
        $(".identifyingCode-input-empty-tips-" + $(that).attr('LogicOrderItemId')).hide();
    }

    //#endregion


    return sl;
}(sendLogistic || {}, commonModule, addTmplInOrderListModule, orderTableBuilder, waitOrderModule, jQuery));
