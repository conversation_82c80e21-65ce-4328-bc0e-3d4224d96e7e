
var handleViewTutorial = null;
var handleAddMerchant = null;
var handleSetPriceRules = null;
var handleGoToBasicProductPage = null;
var handleGoToSupplierSetBy1688Page = null;
var ChangeAllAgentsCheck = null;
var MultiChoiceAgentData = null;
var SearchInputAgents = null;
var DeleteSelectedAgentData = null;
var closeSetPriceRulesFullMask = null;
var selectCommonOptions = null;
var handleViewBindAgentData = null;
var handleViewVipBindedAgentData = null;
var ChangeAllVipAgentsCheck = null;
var MultiChoiceVipAgentCheck = null;
var SearchInputVipAgents = null;
var CalculatePriceInput = null;
var handleStartCalculate = null;
var saveSetLevelPriceRules = null;
var variateInputVal = null;
var distributeSetModule = (function (module, $, ly) {
    var AddBindAgentToVipList = []; // 添加绑定商家数据
    var CheckAddBindAgentToVipData = []; // 勾选添加商家至Vip数据
    var VipBindedAgentList = []; // Vip已绑定商家数据
    var CheckCancelSetVipBindAgentData = []; // 勾选取消设置-绑定商家的数据
    var MemberLevelData = [];
    var PriceRuleList = [];
    var AddBindAgentSearchResult = []; // 添加绑定商家-输入筛选数据
    var ViewVipBindedAgentSearchResult = []; // vip已绑定商家

    var SecondWrapPermDic =
    {
        hasOrderFieldShowPerm: false,
        hasAgentAddressShowPerm: false,
        hasSettingTipCardPerm: false,
    };

    var InitNavModel =
    {
        MainNavHtml: '',
        //<li data-type="li_settlePriceRemind">未设置结算价弹窗提醒</li>
        SettingTipCardNavHtml: '<div class="tag_title">失效店铺提醒设置</div>',
        HideWraps: ["#agentAddress", "#setting_tip_card", "#setOrderFieldShow"],

        MainShowPart: '',
        SettingTipCardShowPart: 'li_shopRemind',
    };
    function filtNavs() {
        commonModule.Ajax({
            url: '/SubAccount/GetSubPermissionsByParentTag',
            loading: false,
            data: {
                tag: "Authorization/DistributeSet"
            },
            success: function (rsp) {
                if (rsp && rsp.Success) {
                    var exportPermissions = sortPermission(rsp.Data);
                    var isFirstPart = false;
                    exportPermissions.forEach(function (item, i) {
                        switch (item) {
                            case 'Authorization/DistributeSet/Index':
                                InitNavModel.MainNavHtml += '<li data-type="li_index">订单推送设置</li>';
                                if (!isFirstPart) {
                                    isFirstPart = true;
                                    InitNavModel.MainShowPart = "li_index";
                                }
                                break;
                            case 'Authorization/DistributeSet/Order':
                                InitNavModel.MainNavHtml += '<li data-type="li_order">商家代发订单展示权限</li>';
                                if (!isFirstPart) {
                                    isFirstPart = true;
                                    InitNavModel.MainShowPart = "li_order";
                                }
                                break;
                            case 'Authorization/DistributeSet/AgentRemark':
                                InitNavModel.MainNavHtml += '<li data-type="li_remark">厂家操作权限设置</li>';
                                if (!isFirstPart) {
                                    isFirstPart = true;
                                    InitNavModel.MainShowPart = "li_remark";
                                }
                                break;
                            case 'Authorization/DistributeSet/LevelSell':
                                InitNavModel.MainNavHtml += '<li data-type="li_levelSell">分销等级设置</li>';
                                if (!isFirstPart) {
                                    isFirstPart = true;
                                    InitNavModel.MainShowPart = "li_levelSell";
                                }
                                break;
                        }
                    });
                    initNavs();
                }
                else {
                    layer.msg(rsp.Message);
                }

            }
        });
    }
    // 初始化tab标签选项
    function initNavs() {
        var html = InitNavModel.MainNavHtml;
        var settingHtml = InitNavModel.SettingTipCardNavHtml;
        console.log("settingHtml===", settingHtml)
        var hideWraps = InitNavModel.HideWraps;
        var showPart = InitNavModel.MainShowPart;
        var settingShowPart = InitNavModel.SettingTipCardShowPart;
        if (html && html != '') {
            //console.log("dd")
            $("#layui_mywrap").show();
            $("#distribute_setting_tab_ul").append(html);
            $("#distribute_setting_tab_ul").find("li").eq(0).addClass("layui-this");
            $("#layui_mywrap ." + showPart).removeClass("hide");
            if (showPart == "li_order") {
                $("#setOrderFieldShow").show();
                $("#setting_tip_card, #agentAddress").hide();
            }
            else if (showPart == "li_index") {
                if (settingHtml && settingHtml != '') {
                    $("#setting_tip_tab_ul").append(settingHtml);
                    $("#setting_tip_tab_ul").find("li").eq(0).addClass("layui-this");

                    $("#setting_tip_card").show();
                    $("#setting_tip_card ." + settingShowPart).removeClass("hide");
                }
                $("#agentAddress").show();
            } else {
                $("#setting_tip_card, #agentAddress, #setOrderFieldShow").hide();
            }
            var getQueryType = commonModule.getQueryVariable("type") || "";
            if (getQueryType == "invalidShop") {
                ShowActiveModuleEl('li_remark');
                $("#setting_tip_card, #agentAddress, #setOrderFieldShow").hide();
            } else if (getQueryType == 'levelSet') {
                ShowActiveModuleEl('li_levelSell');
                $("#setting_tip_card, #agentAddress, #setOrderFieldShow").hide();
            } else if (getQueryType == 'index_set') {
                ShowActiveModuleEl('li_index');
            }

        } else {
            $("#layui_mywrap").hide();
        }
    }

    function sortPermission(permissions) {
        if (!permissions) {
            return;
        }
        var sorts = [
            'Authorization/DistributeSet/Index',
            'Authorization/DistributeSet/Order',
            'Authorization/DistributeSet/AgentRemark',
            'Authorization/DistributeSet/LevelSell',
        ];

        permissions.sort(function (a, b) {
            var indexA = sorts.indexOf(a);
            var indexB = sorts.indexOf(b);
            return indexA - indexB;
        });
        return permissions;
    }

    // 根据浏览器参数显示对应的模块
    function ShowActiveModuleEl(moduleType) {
        $("#layui_mywrap .layui-tab-part").addClass("hide");
        $("#distribute_setting_tab_ul > li").each(function () {
            var $item = $(this);
            var type = $item.attr("data-type");
            $item.removeClass("layui-this");
            if (type === moduleType) {
                $item.addClass("layui-this");
                $("#layui_mywrap ." + moduleType).removeClass("hide");
            }
        });
    }

    $(function () {
        getMemberLevelList();
        initCommonSelectEvent();
        filtNavs();
        getLoadExpiredShop(); // 获取过期店铺
        commonModule.navActive("#distribute_setting_tab_ul", function (index, item) {
            $("#layui_mywrap .layui-tab-part").addClass("hide");
            var type = $(item).attr("data-type");
            $("#layui_mywrap .layui-tab-part").each(function (idx, el) {
                if ($(el).hasClass(type)) {
                    $(el).removeClass("hide");
                }
            });
            if (type == "li_order") {
                $("#setOrderFieldShow").show();
                $("#setting_tip_card").hide();
                $("#agentAddress").hide();
            }
            else if (type == "li_index") {
                $("#setting_tip_card").show();
                $("#agentAddress").show();
                $("#setOrderFieldShow").hide();
            } else {
                $("#setting_tip_card, #agentAddress, #setOrderFieldShow").hide();
            }
        }, "layui-this");
        commonModule.navActive("#setting_tip_tab_ul", function (index, item) {
            $("#setting_tip_card .layui-tab-part").addClass("hide");
            var type = $(item).attr("data-type");

            $("#setting_tip_card .layui-tab-part").each(function (idx, el) {
                if ($(el).hasClass(type)) {
                    $(el).removeClass("hide");
                }
            });
        }, "layui-this");
        var data = commonModule.FxUserSettings;
        var lent = $("input[type='checkbox']");
        for (var i = 0; i < lent.length; i++) {
            var itemName = $($("input[type='checkbox']")[i]).attr("name");
            for (var s = 0; s < data.length; s++) {
                if (data[s].Key.indexOf(itemName) >= 0) {
                    $("input[name='" + itemName + "']").attr("checked", data[s].Value == "true" ? true : false);
                    if (itemName == 'IsAgentSendAddress') {
                        var isShow = data[s].Value == "true" ? "inline-block" : "none";
                        $('#createFullMaskNameBtn').css({ display: isShow });
                    }
                }
            }
        }
        layui.form.render('checkbox');
        //初始化手工单规则----开始
        var ruledata = commonModule.FxUserCheckRule;
        if (ruledata.Type == 0) {
            $('input:radio[name="pushOrderType"][value="0"]').prop('checked', true);
            $("#AllOrderPushWrapper").show();
        } else if (ruledata.Type == 1) {
            $('input:radio[name="pushOrderType"][value="1"]').prop('checked', true);
        } else if (ruledata.Type == 2) {
            $('input:radio[name="pushOrderType"][value="2"]').prop('checked', true);
            $("#layui_tab_contentOne_ul").show(200);
        }
        layui.form.render('radio');
        //    { Value: '1', Text: '店铺01' },
        //    { Value: '2', Text: '店铺02' }]
        var usershops = commonModule.FxUserShops;
        var useragents = commonModule.FxUserAgents;
        var usersuppliers = commonModule.FxUserSuppliers;
        var remarks = commonModule.FxUserSuppliers
        var shopList = [], shopSelect = [], supplierList = [], supplierSelect = [], agentList = [], agentSelect = [], remarkList = [], remarkSelect = [];
        commonModule.FxUserShops.forEach(function (item) { shopList.push({ "Value": item.ShopId, "Text": item.NickName }) });
        commonModule.FxUserAgents.forEach(function (item) { agentList.push({ "Value": item.FxUserId, "Text": item.UserName }) });
        commonModule.FxUserSuppliers.forEach(function (item) { supplierList.push({ "Value": item.SupplierFxUserId, "Text": item.UserName }) });


        ruledata.ShopIds.split(",").forEach(function (item) {
            commonModule.FxUserShops.forEach(function (item2) {
                if (item2.ShopId == item) {
                    shopSelect.push({
                        "Value": item,
                        "Text": item2.NickName
                    })
                }
            })
        })
        ruledata.Agents.split(",").forEach(function (item) {
            commonModule.FxUserAgents.forEach(function (item2) {
                if (item2.FxUserId == item) {
                    agentSelect.push({
                        "Value": item,
                        "Text": item2.UserName
                    })
                }
            })

        })
        ruledata.Suppliers.split(",").forEach(function (item) {
            commonModule.FxUserSuppliers.forEach(function (item2) {
                if (item2.SupplierFxUserId == item) {
                    supplierSelect.push({
                        "Value": item,
                        "Text": item2.UserName
                    })
                }
            })

        })

        var remarkList = [
            { Value: '1', Text: '平台买家留言' },
            { Value: '2', Text: '平台卖家备注' },
            { Value: '3', Text: '分销代发备注' }]
        ruledata.Messages.split(",").forEach(function (item) {
            if (item == "1") {
                remarkSelect.push({
                    "Value": "1",
                    "Text": "平台买家留言"
                })
            } else if (item == "2") {
                remarkSelect.push({
                    "Value": "2",
                    "Text": "平台卖家备注"
                })
            } else if (item == "3") {
                remarkSelect.push({
                    "Value": "3",
                    "Text": "分销代发备注"
                })
            }
        })

        //监听取消
        layui.form.on('submit(rulecancel)', function (data) {
            location.reload();
        });

        initSelectModule(shopList, shopSelect, supplierList, supplierSelect, agentList, agentSelect, remarkList, remarkSelect);

        /*
                 commonModule.FxUserShops = @
                 commonModule.FxUserAgents =
                 commonModule.FxUserSuppliers
         */

        //初始化手工单规则---结束

        //监听规则提交
        layui.form.on('submit(rulesubmit)', function (data) {
            var ordercheckrule = {
                "Type": $('input:radio[name="pushOrderType"]:checked').val(),
                //"ShopIds": $("#selectShops").data().values,
                //"Suppliers": $("#selectSupplier").data().values,
                //"Agents": $("#selectAgent").data().values,
                //"Messages": $("#selectRemark").data().values                
                "ShopIds": $("#selectShops").attr("data-values") || "",
                "Suppliers": $("#selectSupplier").attr("data-values") || "",
                "Agents": $("#selectAgent").attr("data-values") || "",
                "Messages": $("#selectRemark").attr("data-values") || ""
            }
            commonModule.Ajax({
                url: "/System/SaveOrderCheckRule",
                type: "POST",
                loadingMessage:"保存中，正在更新待发货订单...",
                showMasker: true,
                loading: true,
                data: ordercheckrule,
                success: function (rsp) {
                    if (rsp.Success == false) {
                        layer.msg(rsp.Message);
                        return;
                    } else {
                        layer.msg(rsp.Data);
                    }
                }
            });
            return false;
        });



        layui.form.on('switch(switchTest01)', function (data) {
            var key = data.othis.prev().attr("name");
            setValue(key, this.checked);
        });

        layui.form.on('switch(switchTest02)', function (data) {
            var key = data.othis.prev().attr("name");
            setValue(key, this.checked);
        });
        var switchTop01DataIds = [];
        // 失效店铺列表
        function getLoadExpiredShop() {
            commonModule.LoadExpiredShop({
                fromIndex: 1,
                queryType: 'top_invalid_shop'
            }, function (rsp) {
                if (rsp.Success) {
                    var shops = JSON.parse(rsp.Data.expiredShops) || [];
                    var invalidIds = [];
                    for (var i = 0; i < shops.length; i++) {
                        invalidIds.push(shops[i].ShopId);
                    }
                    switchTop01DataIds = invalidIds;
                }
            })
        }
        layui.form.on('switch(switchTest03)', function (data) {
            var key = data.othis.prev().attr("name");
            var idStr = $('input[lay-filter="switchTop01"]').attr('data-ids') || '';
            var isCheck = this.checked;
            if (isCheck) {
                idStr = '';
                getLoadExpiredShop();
            } else {
                idStr = idStr || switchTop01DataIds.join(',');
            }
                
            commonModule.Ajax({
                url: '/common/SeveFilterInvalidShopId',
                data: { 'idStr': idStr, 'isCheck': isCheck },
                loading: false,
                success: function (rsp) {
                    try {
                        queryInvalid();
                    } catch (e) {

                    }
                    layer.msg(rsp.Data || rsp.Message);
                }
            });
        });

        layui.form.on('switch(switchTest04)', function (data) {
            var key = data.othis.prev().attr("name");
            var isCheck = this.checked;
            var val = isCheck ? 1 : 0;
            commonModule.SaveCommonSetting("/System/Fendan/AfterSale/NoSendRefund", val, function (rsp) {
                if (rsp.Success) {
                    layer.msg("操作成功");
                } else {
                    layer.msg(rsp.Message);
                }
            });
        });

        layui.form.on('switch(switchTest05)', function (data) {
            var key = data.othis.prev().attr("name");
            if (this.checked) {
                $(this).prop('checked', '');
                layui.form.render('checkbox');
                var that = this;
                var switchTest05Dailog = layer.open({
                    type: 1,
                    title: "恢复提醒弹窗", //不显示标题
                    content: '<div class="switchTest05Dailog wu-f16 wu-c09"><i class="layui-layer-ico icon-wen layui-layer-ico3"></i>请确认是否未设置结算价提醒</div>',
                    area: '500', //宽高
                    btn: ['确定恢复', '取消'],
                    skin: 'wu-dailog',
                    yes: function () {
                        $(that).prop('checked', true);
                        layui.form.render('checkbox');
                        layer.close(switchTest05Dailog);
                        commonModule.SaveDateTimeCommonSetting(key, "2000-01-01");
                    },
                });

            } else {
                commonModule.SaveDateTimeCommonSetting(key, "");
            }


            return;

            var key = data.othis.prev().attr("name");
            if (this.checked) {
                //开启弹窗，值设为无效
                commonModule.SaveDateTimeCommonSetting(key, "2000-01-01");
            }
            else {
                //关闭弹窗，值设为当前服务器时间
                commonModule.SaveDateTimeCommonSetting(key, "");
            }


        });

        layui.form.on('switch(switchTest06)', function (data) {

            var key = data.othis.prev().attr("name");
            var obj = {
                IsEditSellerRemark: '确定允许厂家使用卖家备注功能吗？',
                IsEditAfterSaleRemark: '确定允许厂家使用售后备注功能吗？',

            }
            var message = {
                IsEditSellerRemark: '开启后，所有合作厂家在处理订单时，可以使用卖家备注功能回传旗帜与留言到您的店铺后台。请谨慎操作！',
                IsEditAfterSaleRemark: '开启后，所有合作厂家在处理售后订单时，可以使用售后备注功能回传留言到您的店铺后台。请谨慎操作！'
            }
            var index = key === 'IsEditSellerRemark' ? 0 : 1;
            var urlKey = "/FenFa/System/Config/" + key;
            var setting = getSettingByKey(urlKey);
            if (setting != null && setting.Value != null) {
                var ordSet = JSON.parse(setting.Value);
                $('input:radio[name="remarks"][value="' + ordSet.NoteMode + '"]').prop('checked', true);
            } else {
                $('input:radio[name="remarks"][value="1"]').prop('checked', true);
            }
            var orderRemarkSetting = { "Enable": false, "NoteMode": $('input:radio[name="remarks"]:checked').val() };
            $(".merchant-remarks-message").removeClass("hide");
            $(".merchant-remarks").removeClass("layout");
            $('.merchant-remarks-message-text').html(message[key]);
            var that = this;
            if (this.checked) {
                $(this).prop('checked', '');
                layui.form.render('checkbox');
                var switchTest05Dailog = layer.open({
                    type: 1,
                    resize: false,
                    skin: 'wu-dailog',
                    title: obj[key], //不显示标题 
                    content: $('.merchant-remarks'),
                    area: ["560px"], //宽高
                    btn: ['确定', '取消'],
                    btn1: function () {
                        orderRemarkSetting = { "Enable": true, "NoteMode": $('input:radio[name="remarks"]:checked').val() };
                        $(that).prop('checked', true);
                        $(".manufacturer-added:eq(" + index + ")").removeClass("hide");
                        layui.form.render('checkbox');
                        layer.close(switchTest05Dailog);
                        commonModule.SaveCommonSetting(urlKey, JSON.stringify(orderRemarkSetting), function (res) {
                            if (res.Success) {
                                // 更新视图
                                setSettingByKey(urlKey, orderRemarkSetting);
                                console.log(urlKey, commonModule.FxUserSettings, 'commonModule.FxUserSettings');
                            }
                        });
                    },
                    btn2: function () {
                        layer.close(switchTest05Dailog);
                    },
                });

            } else {
                $(that).prop('checked', true);
                layui.form.render('checkbox');
                // $(".manufacturer-added:eq(" + index + ")").addClass("hide");
                closePermission(that, orderRemarkSetting, key, index);
                // commonModule.SaveCommonSetting("/FenFa/System/Config/" + key, JSON.stringify(orderRemarkSetting));
            }
        });

        layui.form.on('switch(switchTest07)', function (data) {
            var key = data.othis.prev().attr("name");
            var that = this;
            var urlKey = "/FenFa/System/Config/" + key;
            
            if (this.checked) {
                $(that).prop('checked', true);
                layui.form.render('checkbox');
                setValue(key, true);
                setSettingByKey(urlKey, true);
            }
            else {
                $(that).prop('checked', true);
                layui.form.render('checkbox');
                closePermission(that, "false", key, index);
            }
        });

        var stockOutSetting = getSettingByKey("/FenFa/System/Config/StockOutType");
        if (stockOutSetting != null && stockOutSetting.Value == "1") {
            $('input:radio[name="stock"][value="1"]').prop('checked', true);
        }
        $('input:radio[name="stock"]').on("click", function () {
            var stockval = ($(this).val());
            setValue("StockOutType", stockval);
        });

        var stockOutZhSetting = getSettingByKey("/FenFa/System/Config/StockOutZHType");
        if (stockOutZhSetting != null && stockOutZhSetting.Value == "1") {
            $('input:radio[name="stockzh"][value="1"]').prop('checked', true);
        }
        else if (stockOutZhSetting != null && stockOutZhSetting.Value == "3") {
            $('input:radio[name="stockzh"][value="3"]').prop('checked', true);
        }

        var orderDisplaySetting = getSettingByKey("/System/Setting/OrderDisplaySetting");
        if (orderDisplaySetting != null && orderDisplaySetting.Value != null) {
            var ordSet = JSON.parse(orderDisplaySetting.Value);
            $("select[name='ProductSetting']").val(ordSet.ProductSetting);
            //$("select[name='SkuSetting']").val(ordSet.SkuSetting);
            $("select[name='BizCodeSetting']").val(ordSet.BizCodeSetting);
            $("select[name='RecipientSetting']").val(ordSet.RecipientSetting);
            layui.form.render("select");
        }
        ///卖家备注 
        var sellerRemarkSetting = getSettingByKey("/FenFa/System/Config/IsEditSellerRemark");
        if (sellerRemarkSetting != null && sellerRemarkSetting.Value != null) {
            var ordSet = JSON.parse(sellerRemarkSetting.Value);
            $('input:checkbox[name="IsEditSellerRemark"]').prop('checked', ordSet.Enable);
            $('input:radio[name="remarks"][value="' + ordSet.NoteMode + '"]').prop('checked', true);
            if (ordSet.Enable) {
                var index = sellerRemarkSetting.Key == '/FenFa/System/Config/IsEditSellerRemark' ? 0 : 1;
                $(".manufacturer-added:eq(" + index + ")").removeClass("hide");
            }
            /*            layui.form.render('checkbox');*/
        }

        ///售后备注
        var afterSaleRemarkSetting = getSettingByKey("/FenFa/System/Config/IsEditAfterSaleRemark");
        if (afterSaleRemarkSetting != null && afterSaleRemarkSetting.Value != null) {
            var ordSet = JSON.parse(afterSaleRemarkSetting.Value);
            $('input:checkbox[name="IsEditAfterSaleRemark"]').prop('checked', ordSet.Enable);
            $('input:radio[name="remarks"][value="' + ordSet.NoteMode + '"]').prop('checked', true);
            if (ordSet.Enable) {
                var index = afterSaleRemarkSetting.Key == '/FenFa/System/Config/IsEditSellerRemark' ? 0 : 1;
                $(".manufacturer-added:eq(" + index + ")").removeClass("hide");
            }
            /*            layui.form.render('checkbox');*/
        }



        $('input:radio[name="stockzh"]').on("click", function () {
            var stockval = ($(this).val());
            setValue("StockOutZHType", stockval);
        });

        $("input:radio[name='pushOrderType']").on("change", function () {
            if ($(this).val() == 2) {
                $("#layui_tab_contentOne_ul").show(200);
                $("#AllOrderPushWrapper").hide();
            } else if ($(this).val() == 0) {
                $("#layui_tab_contentOne_ul").hide(100);
                $("#AllOrderPushWrapper").show();
            } else {
                $("#layui_tab_contentOne_ul").hide(100);
                $("#AllOrderPushWrapper").hide();
            }
        });

        $("select[name='ProductSetting'], select[name='SkuSetting'], select[name='BizCodeSetting'], select[name='RecipientSetting']").change(function () {

        });

        layui.form.on('select(ProductSetting)', function (data) {
            $("#orderDisplaySetting_btn").removeClass("hide");
            //saveOrderDisplaySetting(data);
        });
        layui.form.on('select(SkuSetting)', function (data) {
            $("#orderDisplaySetting_btn").removeClass("hide");
            //saveOrderDisplaySetting(data);
        });
        layui.form.on('select(BizCodeSetting)', function (data) {
            $("#orderDisplaySetting_btn").removeClass("hide");
            //saveOrderDisplaySetting(data);
        });
        layui.form.on('select(RecipientSetting)', function (data) {
            $("#orderDisplaySetting_btn").removeClass("hide");
            //saveOrderDisplaySetting(data);
        });


        //加载失效店铺开关
        loadFilterInvalid();

        //加载售后小助手开关
        loadAfterSale();

        var targetFrom = commonModule.getQueryVariable("targetFrom");
        if (targetFrom == "productIndex") {
            $("#distribute_setting_tab_ul #sync_set").trigger("click");
        }

        // 代发隐私设置
        var IsSalePricePublic = getSettingByKey("/FenFa/System/Config/IsSalePricePublic");
        if (IsSalePricePublic != null && IsSalePricePublic.Value == "true") {
            $('input[name="IsSalePricePublicNew"][value="IsSalePricePublic"]').prop('checked', true);
        }
        var AgentProductInfoPublic = getSettingByKey("/FenFa/System/Config/AgentProductInfoPublic");
        if (AgentProductInfoPublic != null) {
            var row = JSON.parse(AgentProductInfoPublic.Value);
            
            // 设置 IsProductTitlePublic 和 IsProductImgPublic 复选框的勾选状态
            $('#isGoodsName').prop('checked', row.IsProductTitlePublic);
            $('#isGoodsImg').prop('checked', row.IsProductImgPublic);

            // 根据勾选状态设置 disabled 状态
            $('#isGoodsName').prop('disabled', row.IsProductTitlePublic && !row.IsProductImgPublic);
            $('#isGoodsImg').prop('disabled', row.IsProductImgPublic && !row.IsProductTitlePublic);

            $('input[name="IsShopNamePublic"][value="IsShopNamePublic"]').prop('checked', row.IsShopNamePublic);
        }

        // 通用函数用于互斥处理
        function handleCheckboxChange(checkbox, otherCheckbox) {
            /* if ($(checkbox).is(':checked')) {
                $(checkbox).prop('disabled', true);
                $("#" + otherCheckbox).prop({
                    checked: false,
                    disabled: false
                });
            } */
            if ($(checkbox).is(':checked') && $("#" + otherCheckbox).is(':checked')) {
                $(checkbox).prop('disabled', false);
                $("#" + otherCheckbox).prop('disabled', false);

            }
            var isChecked1 = $("#isGoodsName").prop('checked');
            var isChecked2 = $("#isGoodsImg").prop('checked');
            var isChecked3 = $('input[name="IsShopNamePublic"]').prop('checked');
            var row = {
                IsProductTitlePublic: isChecked1,
                IsProductImgPublic: isChecked2,
                IsShopNamePublic: isChecked3
            }
            setValue('AgentProductInfoPublic', JSON.stringify(row));
        }

        // 商品标题和商品图片互斥
        $('#isGoodsName').change(function () {
            var isChecked = $("#isGoodsImg").prop('checked');
            if (!$(this).is(':checked') && isChecked) {
                $('#isGoodsImg').prop({
                    disabled: true,
                });
            }
            if (!isChecked) {
                $('#isGoodsName').prop({
                    checked: true,
                    disabled: true,
                });
                return;
            }
            handleCheckboxChange(this, 'isGoodsImg');
        });

        // 初始化分销等级列表
        function getMemberLevelList() {
            commonModule.Ajax({
                url: "/api/MemberLevel/LoadList",
                type: "GET",
                success: function (rsp) {
                    if (rsp.Success) {
                        var resData = rsp.Data;
                        resData.forEach(function (item) {
                            if (item.PriceRule.Operator == '*') {
                                item.PriceRule.Operator = '×';
                            }
                            if (item.PriceRule.Operator == '-') {
                                item.PriceRule.Operator = '－';
                            }
                            if (item.PriceRule.Operator == '+') {
                                item.PriceRule.Operator = '＋';
                            }
                        });
                        var tplt = $.templates("#init_sell_price_level_list");
                        var html = tplt.render({
                            data: resData.slice(1),
                        });
                        $("#sell_price_level_ul").html(html);
                        // 默认等级
                        var data = resData[0];
                        $("#default_bind_agent_count").text(data.Count);
                        var rule = '分销价=基本价格' + data.PriceRule.Operator + data.PriceRule.Variate + data.PriceRule.Unit;
                        $("#default_bind_price_rule").text(rule);
                    }
                }
            });
        }

        function renderMemberLevelTemp(list) {
            var tplt = $.templates("#member_level_list_template");
            var html = tplt.render({
                data: list,
            });
            $("#member_level_vip_ul").html(html);
        }

        // 设置分销等级价格规则-分销等级列表
        function getPriceRuleMemberLevelList() {
            commonModule.Ajax({
                url: "/api/MemberLevel/LoadList",
                type: "GET",
                success: function (rsp) {
                    if (rsp.Success) {
                        MemberLevelData = rsp.Data;
                        PriceRuleList = [];
                        MemberLevelData.forEach(function (item) {
                            PriceRuleList.push({
                                MemberLevelCode: item.MemberLevelCode,
                                PriceRule: item.PriceRule
                            });
                        });
                        var vip0_data = MemberLevelData[0]; // 默认等级
                        $("input[name='FinalDistributePriceCorrectRule'][value='" + vip0_data.FinalPriceRule + "']").prop("checked", true);
                        var Operator = null;
                        if (vip0_data.PriceRule.Operator == '*') {
                            Operator = '×';
                        }
                        if (vip0_data.PriceRule.Operator == '-') {
                            Operator = '－';
                        }
                        if (vip0_data.PriceRule.Operator == '+') {
                            Operator = '＋';
                        }
                        $("#member_level_vip0").find(".n-mySelect").addClass("hasActive").find(".n-mySelect-title-chooseItem").text(Operator);
                        $("#member_level_vip0_variate").val(vip0_data.PriceRule.Variate);
                        $("#member_level_vip0_unit").text(vip0_data.PriceRule.Unit);
                        $("#member_level_vip0_binded_agent").text(vip0_data.Count);
                        renderMemberLevelTemp(MemberLevelData.slice(1));
                    }
                }
            });
        }

        function initCommonSelectEvent() {
            //$(".n-myCommonSelect .n-mySelect-title").on("click", function (event) {
            //    console.log('测试测试测试');
            //    event.stopPropagation();
            //    $(".n-myCommonSelect").removeClass("active");
            //    $(this).closest(".n-myCommonSelect").addClass("active");
            //    $(this).closest(".n-myCommonSelect").find(".n-mySelect-showContent-ul-li").css({ display: 'flex' });
            //});

            // 模拟运算
            $("#AnalogCalculate").on("click", function (event) {
                event.stopPropagation();
                $("#calculate_price_input").val('');
                $("#ShowCalculatePriceTooltip").show();
            });
            $("#CalculateTooltip").on("click", function (event) {
                event.stopPropagation();
            });
            $(".calculate-result-wrap").on("click", function (event) {
                event.stopPropagation();
            });
        }

        // select事件委托
        $(document).on("click", ".n-myCommonSelect .n-mySelect-title", function (event) {
            event.stopPropagation();
            $(".n-myCommonSelect").removeClass("active");
            $(this).closest(".n-myCommonSelect").addClass("active");
            $(this).closest(".n-myCommonSelect").find(".n-mySelect-showContent-ul-li").css({ display: 'flex' });
        });


        // 点击document
        $(document).on("click", function () {
            $(".n-myCommonSelect").removeClass("active"); // 移除下拉选
            $("#ShowCalculatePriceTooltip").hide(); // 隐藏模拟运算Tooltip
            $(".calculate-result-wrap").hide(); // 隐藏运算价格
        });

        // 查看教程
        handleViewTutorial = function () {

        }
        // 初始化绑定商家数据
        function renderAgentsData(list) {
            var tplt = $.templates("#init_agent_select_data");
            var AgentsDataHtml = tplt.render({
                agentData: list,
            });
            $("#agent_list_li").html(AgentsDataHtml);
            var checkNum = 0;
            list.forEach(function (item) {
                if (item.IsCheck) {
                    checkNum++;
                }
            });
            var agentLen = list.length;
            if (checkNum == agentLen) {
                $("#AllAgentCheck").find(".n-newCheckbox").addClass("activeF");
            }
            if (checkNum > 0 && checkNum != agentLen) {
                $("#AllAgentCheck").find(".n-newCheckbox").addClass("activeP").removeClass("activeF");
            }
            if (checkNum == 0) {
                $("#AllAgentCheck").find(".n-newCheckbox").removeClass("activeP").removeClass("activeF");
            }
        }

        // 已选绑定商家数据
        function renderSelectedAgentsData(list) {
            var tplt = $.templates("#init_agent_has_selected_data");
            var AgentsDataHtml = tplt.render({
                agentData: list,
            });
            $("#selected_agent_list_li").html(AgentsDataHtml);
        }

        // 价格区间
        variateInputVal = function (MemberLevelCode) {
            var inputValue = $(this).val().trim();
            // $(this).val(inputValue);
            PriceRuleList.forEach(function (item, i) {
                if (item.MemberLevelCode == MemberLevelCode) {
                    item.PriceRule.Variate = inputValue;
                }
            });
            console.log("updated PriceRuleList data", PriceRuleList);
        }

        // 添加商家至VIP
        handleAddMerchant = function (level, memberLevelCode) {
            commonModule.Ajax({
                url: "/api/MemberLevel/GetAgentList?memberLevelCode=" + memberLevelCode + "&isEqual=false",
                type: "GET",
                loading: true,
                loadingMessage: "加载中...",
                success: function (rsp) {
                    if (rsp.Success) {
                        AddBindAgentToVipList = rsp.Data;
                        AddBindAgentToVipList.forEach(function (item) {
                            item.IsCheck = false;
                        });
                        CheckAddBindAgentToVipData = [];
                        layer.closeAll();
                        layer.open({
                            type: 1,
                            title: "添加商家至VIP" + level, // 标题
                            content: $('#bind_merchant_transfer_checkbox'),
                            offset: '160px',
                            area: ['560px', 'auto'], // 宽高
                            skin: 'n-skin',
                            btn: ['取消', '确认添加'],
                            cancel: function () { },
                            btn1: function (index) {
                                layer.close(index);
                            },
                            btn2: function () {
                                if (!CheckAddBindAgentToVipData.length) {
                                    commonModule.w_alert({ type: 3, content: '请先勾选商家！' });
                                    return false;
                                }
                                var agentIds = CheckAddBindAgentToVipData.map(function (item) {
                                    return item.FxUserId
                                });
                                showChangeConfirmTip(memberLevelCode, agentIds, level);
                                return false;
                            },
                            success: function () {
                                $(".n-skin .layui-layer-content").css("padding", '0');
                                renderAgentsData(AddBindAgentToVipList);
                                $("#add_bind_agent_search_input").val('');
                                renderSelectedAgentsData(CheckAddBindAgentToVipData);
                                AddBindAgentSearchResult = [];
                            },
                        });
                    }
                }
            });
        }

        // 设置分销等级价格规则
        handleSetPriceRules = function () {
            getPriceRuleMemberLevelList();
            $("#createSetPriceRulesFullMask").addClass("active");
        }
        // 跳转到-基础商品页面
        handleGoToBasicProductPage = function () {
            window.open(commonModule.rewriteUrl("/BaseProduct/NewBaseProduct"), '_blank');
        }
        // 跳转到-收单设置页面
        handleGoToSupplierSetBy1688Page = function () {
            window.open(commonModule.rewriteUrl("/SupplySet1688/SupplierSetBy1688"), '_blank');
        }

        // 添加商家至VIP-全选
        ChangeAllAgentsCheck = function () {
            var that = this;
            $(that).find(".n-newCheckbox").toggleClass('activeF');
            var list = [];
            if (AddBindAgentSearchResult.length > 0) {
                AddBindAgentSearchResult.forEach(function (item) {
                    item.IsCheck = $(that).find(".n-newCheckbox").hasClass('activeF');
                });
                list = AddBindAgentSearchResult;
            } else {
                AddBindAgentToVipList.forEach(function (item) {
                    item.IsCheck = $(that).find(".n-newCheckbox").hasClass('activeF');
                });
                list = AddBindAgentToVipList;
            }
            renderAgentsData(list);
            CheckAddBindAgentToVipData = [];
            CheckAddBindAgentToVipData = list.filter(function (item) {
                return item.IsCheck;
            });
            renderSelectedAgentsData(CheckAddBindAgentToVipData);
        }

        // 添加商家至VIP-勾选
        MultiChoiceAgentData = function (FxUserId) {
            var list = [];
            if (AddBindAgentSearchResult.length > 0) {
                AddBindAgentSearchResult.forEach(function (item) {
                    if (item.FxUserId == FxUserId) {
                        item.IsCheck = !item.IsCheck;
                    }
                });
                list = AddBindAgentSearchResult;
            } else {
                AddBindAgentToVipList.forEach(function (item) {
                    if (item.FxUserId == FxUserId) {
                        item.IsCheck = !item.IsCheck;
                    }
                });
                list = AddBindAgentToVipList;
            }
            renderAgentsData(list);
            CheckAddBindAgentToVipData = [];
            CheckAddBindAgentToVipData = list.filter(function (item) {
                return item.IsCheck;
            });
            renderSelectedAgentsData(CheckAddBindAgentToVipData);
        }

        // 添加商家至VIP-手动输入筛选商家
        SearchInputAgents = function () {
            var value = $(this).val().trim().toLowerCase(); // 获取用户输入并转为小写
            AddBindAgentSearchResult = AddBindAgentToVipList.filter(function (item) {
                return item.UserName.toLowerCase().includes(value); // 使用includes进行模糊匹配
            });
            renderAgentsData(AddBindAgentSearchResult);
        }
        // 添加商家至VIP-清除已选商家数据
        DeleteSelectedAgentData = function (FxUserId) {
            // 查找要删除的元素的索引
            var indexToDelete = CheckAddBindAgentToVipData.findIndex(function (item) {
                return item.FxUserId == FxUserId;
            });
            // 如果找到对应的索引，删除该元素
            if (indexToDelete !== -1) {
                CheckAddBindAgentToVipData.splice(indexToDelete, 1); // 从数组中删除
            }
            renderSelectedAgentsData(CheckAddBindAgentToVipData);
            // 移除已选商家之后，将商家的勾选状态移除
            AddBindAgentToVipList.forEach(function (item) {
                if (item.FxUserId == FxUserId) {
                    item.IsCheck = false;
                }
            });
            renderAgentsData(AddBindAgentToVipList);
        }

        closeSetPriceRulesFullMask = function () {
            $("#createSetPriceRulesFullMask").removeClass("active");
        }
        // 下拉选择
        selectCommonOptions = function (MemberLevelCode) {
            var value = $(this).attr("data-value");
            var text = $(this).text();
            $(this).closest(".n-mySelect").addClass("hasActive").find(".n-mySelect-title-chooseItem").html(text);
            $(this).addClass("selected-li-active").siblings().removeClass("selected-li-active");
            if (value == '*') {
                $(this).closest(".price-rules-content").find(".input-num").text('%');
            } else {
                $(this).closest(".price-rules-content").find(".input-num").text('元');
            }
            PriceRuleList.forEach(function (item, i) {
                if (item.MemberLevelCode == MemberLevelCode) {
                    item.PriceRule.Operator = value;
                    if (value == '*') {
                        item.PriceRule.Unit = '%';
                    } else {
                        item.PriceRule.Unit = '元';
                    }
                }
            });
            console.log("updated PriceRuleList data", PriceRuleList);
        }

        // 查看绑定的商家数据
        handleViewBindAgentData = function (type, level) {
            var text = "VIP" + level + " 已绑定商家";
            var title = type == 'default' ? '默认已绑定商家' : text;
            var tplt = $.templates("#init_has_binded_agent_data");
            var requestUrl = "/api/MemberLevel/GetAgentList?isEqual=true";
            if (type !== 'default') {
                requestUrl += "&memberLevelCode=" + MemberLevelData[level].MemberLevelCode;
            }
            commonModule.Ajax({
                url: requestUrl,
                type: "GET",
                success: function (rsp) {
                    if (rsp.Success) {
                        var html = "";
                        html = tplt.render({
                            agentData: rsp.Data,
                        });
                        layer.closeAll();
                        layer.open({
                            type: 1,
                            title: title, // 标题
                            content: html,
                            offset: '160px',
                            area: ['320px', 'auto'], // 宽高
                            skin: 'wu-dailog',
                            btn2: function () {},
                            success: function () {
                                $(".layui-layer-page").css('z-index', 10000000001);
                            },
                        });
                    }
                }
            });
        }

        // vip已绑定商家模版渲染
        function renderVipBindedAgentsData(list) {
            var tplt = $.templates("#init_vip_binded_agent_data");
            var renderVipBindedAgentsHtml = tplt.render({
                agentData: list,
            });
            $("#vip_bind_agent_list_li").html(renderVipBindedAgentsHtml);
            var checkNum = 0;
            list.forEach(function (item) {
                if (item.IsCheck) {
                    checkNum++;
                }
            });
            var agentLen = list.length;
            if (checkNum == agentLen) {
                $("#AllVipAgentCheck").find(".n-newCheckbox").addClass("activeF");
            }
            if (checkNum > 0 && checkNum != agentLen) {
                $("#AllVipAgentCheck").find(".n-newCheckbox").addClass("activeP").removeClass("activeF");
            }
            if (checkNum == 0) {
                $("#AllVipAgentCheck").find(".n-newCheckbox").removeClass("activeP").removeClass("activeF");
            }
        }

        // 查看-vip已绑定商家
        handleViewVipBindedAgentData = function (level, memberLevelCode) {
            var title = "VIP" + level + "已绑定商家";
            commonModule.Ajax({
                url: "/api/MemberLevel/GetAgentList?memberLevelCode=" + memberLevelCode + "&isEqual=true",
                type: "GET",
                success: function (rsp) {
                    if (rsp.Success) {
                        VipBindedAgentList = rsp.Data;
                        $("#vip_binded_agent_count").text(VipBindedAgentList.length);
                        VipBindedAgentList.forEach(function (item) {
                            item.IsCheck = false;
                        });
                        layer.closeAll();
                        layer.open({
                            type: 1,
                            title: title, // 标题
                            content: $('#vip_bind_agent_checkbox'),
                            offset: '160px',
                            area: ['320px', 'auto'], // 宽高
                            skin: 'wu-dailog',
                            btn: ['取消设置', '取消'],
                            cancel: function () {
                                CheckCancelSetVipBindAgentData = [];
                            },
                            btn1: function (index) {
                                if (!CheckCancelSetVipBindAgentData.length) {
                                    commonModule.w_alert({ type: 3, content: '请先勾选要取消绑定的商家！' });
                                    return false;
                                }
                                var agentIds = CheckCancelSetVipBindAgentData.map(function (item) {
                                    return item.FxUserId
                                });
                                showCancelConfirmTip(memberLevelCode, agentIds, level);
                                return false;
                            },
                            btn2: function () {
                                layer.close(index);
                                CheckCancelSetVipBindAgentData = [];
                            },
                            success: function () {
                                renderVipBindedAgentsData(VipBindedAgentList);
                                $("#vip_bind_agent_search_input").val('');
                                ViewVipBindedAgentSearchResult = [];
                            },
                        });
                    }
                }
            });
        }
        saveSetLevelPriceRules = function () {
            var html = '';
            html += '<div style="display: flex;">';
            html += '<i class="iconfont icon-a-error-circle-filled1x n-tColor" style="font-size: 24px;"></i>';
            html += '<div class="mLeft8" style="flex: 1;">';
            html += '<div class="flex-center" style="justify-content: space-between;">';
            html += '<span class="n-font5 fontW">分销等级价格规则生效确认</span>';
            html += '<i class="iconfont icon-a-close1x hover close_agent_level_layer" style="font-size: 20px;"></i>';
            html += '</div>';
            html += '<div class="font14 c06 mTop8">修改分销等级价格规则后，</div>';
            html += '<div class="font14 c06 mTop4">请确认已设置的历史价格是否按最新的分销等级价格规则变更？</div>';
            html += '<div class="font12 n-tColor mTop4" style="margin-bottom: 8px;">选择全部覆盖后该商家会按最新的分销等级价格规则全部覆盖已设置的历史价格。</div>';
            html += '</div>';
            html += '</div>';
            var FinalDistributePriceVal = $("input[name='FinalDistributePriceCorrectRule']:checked").val(); // 最终结算价修正规则
            layer.open({
                type: 1,
                closeBtn: 0,
                title: false, // 标题
                content: html,
                area: ['496px', 'auto'], // 宽高
                skin: 'n-skin',
                btn: ['关闭', '只针对下次新品生效', '全部覆盖更新历史价'],
                success: function (layero, index, that) {
                    $(".close_agent_level_layer").on("click", function () {
                        layer.close(index);
                    });
                },
                btn2: function (index) {
                    SavePriceRule(PriceRuleList, FinalDistributePriceVal, false, index);
                    return false;
                },
                btn3: function (index) {
                    SavePriceRule(PriceRuleList, FinalDistributePriceVal, true, index);
                    return false;
                },
            });
        }

        function SavePriceRule(priceRuleList, finalDistributePriceVal, isUpdateHistory, index) {
            var regexCount = /^[1-9]\d*$/;
            // 遍历 priceRuleList 检查每一项
            for (var j = 0; j < priceRuleList.length; j++) {
                var item = priceRuleList[j];
                if (item.PriceRule.Operator === '*') {
                    var variate = item.PriceRule.Variate;
                    if (variate && !regexCount.test(variate)) {
                        commonModule.w_alert({ type: 3, content: 'VIP' + j + '百分比值请输入正整数' });
                        return false;
                    }
                }
            }
            commonModule.Ajax({
                url: '/api/MemberLevel/SavePriceRule',
                type: "POST",
                loading: true,
                loadingMessage: "处理中...",
                showMasker: true,
                data: {
                    PriceRuleList: priceRuleList,
                    FinalDistributePriceCorrectRule: finalDistributePriceVal,
                    IsUpdateHistory: isUpdateHistory
                },
                success: function (rsp) {
                    if (rsp.Success) {
                        commonModule.w_alert({ type: 4, content: '保存成功' });
                        layer.close(index);
                        $("#createSetPriceRulesFullMask").removeClass("active");
                        getMemberLevelList();
                    } else {
                        commonModule.w_alert({ type: 2, content: rsp.Message });
                        layer.close(index);
                    }
                }
            });
        }

        // 商家分销等级取消确认
        function showCancelConfirmTip(memberLevelCode, agentIds, level) {
            var html = '';
            html += '<div style="display: flex;">';
            html += '<i class="iconfont icon-a-error-circle-filled1x n-tColor" style="font-size: 24px;"></i>';
            html += '<div class="mLeft8" style="flex: 1;">';
            html += '<div class="flex-center" style="justify-content: space-between;">';
            html += '<span class="n-font5 fontW">商家分销等级取消确认</span>';
            html += '<i class="iconfont icon-a-close1x hover close_agent_level_layer" style="font-size: 20px;"></i>';
            html += '</div>';
            html += '<div class="font14 c06 mTop8">取消所选商家等级后，用户将恢复为默认等级，</div>';
            html += '<div class="font14 c06 mTop4">请确认已设置的历史价格是否需要全部更新？</div>';
            html += '<div class="font12 n-tColor mTop4" style="margin-bottom: 8px;">选择全部覆盖后该商家会按最新的等级换算规则全部覆盖已设置的历史价格。</div>';
            html += '</div>';
            html += '</div>';
            layer.open({
                type: 1,
                closeBtn: 0,
                title: false, // 标题
                content: html,
                area: ['484px', 'auto'], // 宽高
                skin: 'n-skin',
                btn: ['关闭', '只针对下次新品生效', '全部覆盖更新历史价'],
                success: function (layero, index, that) {
                    $(".close_agent_level_layer").on("click", function () {
                        layer.close(index);
                    });
                },
                btn2: function () {
                    UpdateAgentMemberLevel(memberLevelCode, agentIds, false, true, level, 'cancel');
                },
                btn3: function () {
                    UpdateAgentMemberLevel(memberLevelCode, agentIds, true, true, level, 'cancel');
                },
            });
        }
        // 商家分销等级变更确认
        function showChangeConfirmTip(memberLevelCode, agentIds, level) {
            var html = '';
            html += '<div style="display: flex;">';
            html += '<i class="iconfont icon-a-error-circle-filled1x n-tColor" style="font-size: 24px;"></i>';
            html += '<div class="mLeft8" style="flex: 1;">';
            html += '<div class="flex-center" style="justify-content: space-between;">';
            html += '<span class="n-font5 fontW">商家分销等级变更确认</span>';
            html += '<i class="iconfont icon-a-close1x hover close_agent_level_layer" style="font-size: 20px;"></i>';
            html += '</div>';
            html += '<div class="font14 c06 mTop8">请确认该分销商变更等级后，已设置的历史价格是否需要全部更新？</div>';
            html += '<div class="font12 n-tColor mTop4" style="margin-bottom: 8px;">选择全部覆盖后该商家会按最新的等级换算规则全部覆盖已设置的历史价格。</div>';
            html += '</div>';
            html += '</div>';
            layer.open({
                type: 1,
                closeBtn: 0,
                title: false, // 标题
                content: html,
                area: ['526px', 'auto'], // 宽高
                skin: 'n-skin',
                btn: ['关闭', '只针对下次新品生效', '全部覆盖更新历史价'],
                success: function (layero, index, that) {
                    $(".close_agent_level_layer").on("click", function () {
                        layer.close(index);
                    });
                },
                btn2: function () {
                    UpdateAgentMemberLevel(memberLevelCode, agentIds, false, false, level, 'add');
                },
                btn3: function () {
                    UpdateAgentMemberLevel(memberLevelCode, agentIds, true, false, level, 'add');
                },
            });
        }

        // 更新商家分销等级
        function UpdateAgentMemberLevel(memberLevelCode, agentIds, isUpdateHistory, isCancel, level, type) {
            commonModule.Ajax({
                url: '/api/MemberLevel/UpdateAgentMemberLevel',
                type: "POST",
                loading: true,
                loadingMessage: "处理中...",
                showMasker: true,
                data: {
                    MemberLevelCode: memberLevelCode,
                    AgentIds: agentIds,
                    IsUpdateHistory: isUpdateHistory,
                    IsCancel: isCancel
                },
                success: function (rsp) {
                    if (rsp.Success) {
                        if (type == 'add') {
                            commonModule.w_alert({ type: 4, content: '已添加' + rsp.Data + '个商家至VIP' + level });
                            CheckAddBindAgentToVipData = [];
                        } else {
                            commonModule.w_alert({ type: 4, content: '等级取消成功，已为商家恢复默认等级' });
                            CheckCancelSetVipBindAgentData = [];
                        }
                        layer.closeAll();
                        getMemberLevelList();
                    } else {
                        commonModule.w_alert({ type: 2, content: rsp.Message });
                        layer.closeAll();
                    }
                }
            });
        }

        // vip已绑定商家-全选

        ChangeAllVipAgentsCheck = function () {
            var that = this;
            $(that).find(".n-newCheckbox").toggleClass('activeF');
            var list = [];
            if (ViewVipBindedAgentSearchResult.length > 0) {
                ViewVipBindedAgentSearchResult.forEach(function (item) {
                    item.IsCheck = $(that).find(".n-newCheckbox").hasClass('activeF');
                });
                list = ViewVipBindedAgentSearchResult;
            } else {
                VipBindedAgentList.forEach(function (item) {
                    item.IsCheck = $(that).find(".n-newCheckbox").hasClass('activeF');
                });
                list = VipBindedAgentList;
            }
            CheckCancelSetVipBindAgentData = [];
            CheckCancelSetVipBindAgentData = list.filter(function (item) {
                return item.IsCheck;
            });
            renderVipBindedAgentsData(list);
        }

        // vip已绑定商家-勾选

        MultiChoiceVipAgentCheck = function (FxUserId) {
            var list = [];
            if (ViewVipBindedAgentSearchResult.length > 0) {
                ViewVipBindedAgentSearchResult.forEach(function (item) {
                    if (item.FxUserId == FxUserId) {
                        item.IsCheck = !item.IsCheck;
                    }
                });
                list = ViewVipBindedAgentSearchResult;
            } else {
                VipBindedAgentList.forEach(function (item) {
                    if (item.FxUserId == FxUserId) {
                        item.IsCheck = !item.IsCheck;
                    }
                });
                list = VipBindedAgentList;
            }
            CheckCancelSetVipBindAgentData = [];
            CheckCancelSetVipBindAgentData = list.filter(function (item) {
                return item.IsCheck;
            });
            renderVipBindedAgentsData(list);
        }

        // vip已绑定商家-搜索
        SearchInputVipAgents = function () {
            var value = $(this).val().trim().toLowerCase(); // 获取用户输入并转为小写
            ViewVipBindedAgentSearchResult = VipBindedAgentList.filter(function (item) {
                return item.UserName.toLowerCase().includes(value); // 使用includes进行模糊匹配
            });
            $("#vip_binded_agent_count").text(ViewVipBindedAgentSearchResult.length);
            renderVipBindedAgentsData(ViewVipBindedAgentSearchResult);
        }

        // 手动输入价格
        CalculatePriceInput = function () {
            var value = $(this).val().trim();
            $(this).val(value);
        }

        // 运算
        handleStartCalculate = function () {
            var regex = /^(?!0(\.0+)?$)(\d+(\.\d+)?|\.\d+)$/;
            var CalculatePrice = $("#calculate_price_input").val();
            if (!CalculatePrice) {
                commonModule.w_alert({ type: 3, content: '请输入价格' });
                return;
            }
            if (!regex.test(CalculatePrice)) {
                commonModule.w_alert({ type: 3, content: '输入无效，请输入大于0的整数或小数' });
                return;
            }
            var FinalDistributePriceVal = $("input[name='FinalDistributePriceCorrectRule']:checked").val(); // 最终结算价修正规则
            commonModule.Ajax({
                url: '/api/MemberLevel/CalculatePrice',
                type: "POST",
                data: {
                    PriceRuleList: PriceRuleList,
                    FinalDistributePriceCorrectRule: FinalDistributePriceVal,
                    CalculatePrice: CalculatePrice
                },
                success: function (rsp) {
                    if (rsp.Success) {
                        var data = rsp.Data;
                        //$(".calculate-result-wrap").each(function (index) {
                        //    $(this).show();
                        //    $(this).find('span').text(data[index].Price);
                        //});
                        //
                        if (data && data.length > 0) {
                            $("#member_level_calculate_price_vip0").show().find('span').text(data[0].Price);
                            var vipCalculateResult = data.slice(1); // vip1-vip6
                            var memberLevelList = MemberLevelData.slice(1);
                            vipCalculateResult.forEach(function (item, index) {
                                if (index < memberLevelList.length) {
                                    memberLevelList[index].Price = item.Price;
                                }
                            });
                            renderMemberLevelTemp(memberLevelList);
                        }
                        commonModule.w_alert({ type: 4, content: '操作成功' });
                    } else {
                        commonModule.w_alert({ type: 2, content: rsp.Message });
                    }
                }
            });
        }

        $('#isGoodsImg').change(function () {
            var isChecked = $("#isGoodsName").prop('checked');
            if (!$(this).is(':checked') && isChecked) {
                $('#isGoodsName').prop({
                    disabled: true,
                });
            }
            if (!isChecked) {
                $('#isGoodsImg').prop({
                    checked: true,
                    disabled: true,
                });
                return;
            }
            handleCheckboxChange(this, 'isGoodsName');
        });

        // 监听所有 "IsSalePricePublicNew" 复选框的变化，获取所有选中的值
        $('input[name="IsSalePricePublicNew"]').on("change", function () {
            //console.log('change',$(this).val(),this.checked);
            var val = $(this).val();
            setValue(val, this.checked);
        });
        $('input[name="IsShopNamePublic"]').on("change", function () {
            var isChecked1 = $("#isGoodsName").prop('checked');
            var isChecked2 = $("#isGoodsImg").prop('checked');
            var row = {
                IsProductTitlePublic: isChecked1,
                IsProductImgPublic: isChecked2,
                IsShopNamePublic: this.checked
            }
            setValue('AgentProductInfoPublic', JSON.stringify(row));
        });


    });
    function closePermission(_this, params, key, index) {
        var obj = {
            IsEditSellerRemark: '确定关闭厂家使用卖家备注功能吗？',
            IsEditAfterSaleRemark: '确定关闭厂家使用售后备注功能吗？',
            IsEnableQuote: '关闭权限确认'
        }

        var btnCancel = '取消';
        var btnEnter = '确定';
        var isEnableQuote = key === 'IsEnableQuote';

        if (isEnableQuote) {
            $(".closePermission>span").text('关闭后，所有合作厂家将无法在系统内直接使用您的分销商品信息创建库存数据（关闭后，如有需求可回到该页面重新开启权限）');
            btnCancel = '取消操作';
            btnEnter = '确认关闭';
        }

        var closeIndex = layer.open({
            type: 1,
            resize: false,
            skin: 'wu-dailog',
            title: obj[key],
            content: $('.closePermission'),
            area: ["560px"], //宽高
            btn: [btnEnter, btnCancel],
            btn1: function () {
                $(_this).prop('checked', false);
                layui.form.render('checkbox');
                if (!isEnableQuote) $(".manufacturer-added:eq(" + index + ")").addClass("hide");
                layer.close(closeIndex);
                if (isEnableQuote) {
                    setValue(key, false, true);
                } else{
                    commonModule.SaveCommonSetting("/FenFa/System/Config/" + key, JSON.stringify(params));
                }
            },
            btn2: function () {
                layer.close(closeIndex);
            },
        });
    }

    module.manufacturerAdded = function (key) {

        var obj = {
            IsEditSellerRemark: '确定允许厂家使用卖家备注功能吗？',
            IsEditAfterSaleRemark: '确定允许厂家使用售后备注功能吗？',
        }

        var urlKey = "/FenFa/System/Config/" + key;
        var setting = getSettingByKey(urlKey);

        //console.log(setting,commonModule.FxUserSettings, 'manufacturerAdded');
        if (setting != null && setting.Value != null) {
            var ordSet = JSON.parse(setting.Value);
            $('input:radio[name="remarks"][value="' + ordSet.NoteMode + '"]').prop('checked', true);
        }

        $(".merchant-remarks-message").addClass("hide");
        $(".merchant-remarks").addClass("layout");

        var manufacturerAddedOpen = layer.open({
            type: 1,
            resize: false,
            skin: 'wu-dailog',
            title: obj[key], //不显示标题 
            content: $('.merchant-remarks'),
            area: ["560px"], //宽高
            btn: ['确定', '取消'],
            btn1: function () {
                layui.form.render('checkbox');
                layer.close(manufacturerAddedOpen);
                var noteMode = $('input:radio[name="remarks"]:checked').val();
                var orderRemarkSetting = { "Enable": true, "NoteMode": noteMode };
                commonModule.SaveCommonSetting(urlKey, JSON.stringify(orderRemarkSetting), function (res) {
                    if (res.Success) {
                        // 更新视图
                        setSettingByKey(urlKey, orderRemarkSetting);
                        //console.log(urlKey,commonModule.FxUserSettings, 'commonModule.FxUserSettings');
                    }
                });
            },
            btn2: function () {
                layer.close(manufacturerAddedOpen);
            },
        });
    }

    module.saveOrderDisplaySetting = function (ele) {

        //var orderDisplaySetting = { "ProductSetting": $("select[name='ProductSetting']").val(), "SkuSetting": $("select[name='SkuSetting']").val(), "BizCodeSetting": $("select[name='BizCodeSetting']").val(), "RecipientSetting": $("select[name='RecipientSetting']").val() };
        var orderDisplaySetting = { "ProductSetting": $("select[name='ProductSetting']").val(), "BizCodeSetting": $("select[name='BizCodeSetting']").val(), "RecipientSetting": $("select[name='RecipientSetting']").val() };
        commonModule.SaveCommonSetting("/System/Setting/OrderDisplaySetting", JSON.stringify(orderDisplaySetting), function (rsp) {
            if (rsp.Success) {
                commonModule.w_alert({ type: 4, content: '保存成功' });
                $("#orderDisplaySetting_btn").addClass("hide");
                //console.log("保存配置成功");
            }
        });
    };

    var detailModelsMap = {};

    module.showDetails = function (detailKey) {
        var detailModels = detailModelsMap[detailKey];

        var detailsHtml = "<div class='wu-tableWrap'><ul class='wu-table-one' id='layui-layer-content-details'>";
        detailsHtml += "<li><span>平台类型</span> <span>结果</span></li>"
        detailModels.forEach(function (model) {
            var platformType = model.PlatformType == "Alibaba"
                ? "精选平台"
                : model.PlatformType == "Pinduoduo"
                    ? "拼多多"
                    : model.PlatformType == "Jingdong"
                        ? "京东"
                        : model.PlatformType == "TouTiao"
                            ? "抖店"
                            : model.PlatformType == "ChinaAliyun"
                                ? "TikTok"
                                : "";
            detailsHtml += "<li><span> " + platformType + "</span><span>" + model.Result + "</span></li>";
        });
        detailsHtml += "</ul></div>";

        layer.open({
            type: 1,
            title: "操作详情",
            content: detailsHtml,
            skin: 'wu-dailog',
            area: ['300px', '320px'],
            btn: ['关闭']
        });
    };

    module.OrderCheckRuleLog = function () {
        commonModule.Ajax({
            url: '/System/OrderCheckRuleLog',
            loading: true,
            type: 'GET',
            success: function (rsp) {
                layer.closeAll();
                if (rsp.Success) {
                    const records = rsp.Data.map(record => {
                        if (record.CData) {
                            try {
                                record.CData = JSON.parse(record.CData);
                            } catch (e) {
                                console.error("转换时出错", record, e);
                            }
                        }
                        return record;
                    });

                    records.forEach(function (record, index) {
                        if (record.CData && record.CData.DetailModels) {
                            var detailKey = "details_" + Date.now() + "_" + index;
                            detailModelsMap[detailKey] = record.CData.DetailModels;
                            record.detailKey = detailKey;
                        }
                    });

                    const tplt = $.templates("#operateLogo_data");
                    const html = tplt.render({ records });
                    layer.open({
                        type: 1,
                        title: "订单推送设置操作记录",
                        content: html,
                        skin: 'wu-dailog',
                        area: '500px',
                        btn: false,
                    });
                } else {
                    layer.msg(rsp.Message);
                }
            }
        });
    };

    //module.showDetails = function (detailKey) {
    //    var detailModels = detailModelsMap[detailKey];

    //let detailsHtml = "<ul>";
    //detailModels.forEach(function (model) {
    //    detailsHtml += `<li>平台类型: ${model.PlatformType}, 结果: ${model.Result}, 异常描述: ${model.ExceptionDesc || "无"}</li>`;
    //});
    //detailsHtml += "</ul>";

    //layer.open({
    //    type: 1,
    //    title: "订单详情",
    //    content: detailsHtml,
    //    area: ['600px', '400px'],
    //    btn: ['关闭']
    //});
    //};

    var setValue = function (key, value, msg = false) {
        if (key == 'IsAgentSendAddress') {
            if (value) {
                $('#createFullMaskNameBtn').css({ display: "inline-block" });
            } else {
                $('#createFullMaskNameBtn').hide();
            }

        }
        commonModule.Ajax({
            url: "/System/SaveCommonSetting",
            type: "POST",
            data: { settingKey: "/FenFa/System/Config/" + key, settingValue: value },
            success: function (rsp) {
                if (rsp.Success == false) {
                    commonModule.w_alert({ type: 3, content: rsp.Message });
                    return;
                }
                if (msg) layer.msg('关闭权限操作成功', { icon: 1, time: 3000 });
                else commonModule.w_alert({ type: 4, content: '保存成功' });
            }
        });
    };

    var getSettingByKey = function (key) {
        /*        console.log(commonModule, commonModule.FxUserSettings, '111111111111111')*/
        for (var index in commonModule.FxUserSettings) {
            var item = commonModule.FxUserSettings[index];
            if (item.Key == key) {
                return item;
            }
        }
        return null;
    }
    // 设置视图实时更新
    function setSettingByKey(key, orderRemarkSetting) {
        for (var index in commonModule.FxUserSettings) {
            var item = commonModule.FxUserSettings[index];
            if (item.Key == key) {
                item.Value = JSON.stringify(orderRemarkSetting);
            }
        }
    }


    function initSelectModule(shopList, shopSelect, supplierList, supplierSelect, agentList, agentSelect, remarkList, remarkSelect) {
        //按店铺审核 下拉多选框
        //var shopList = [
        //    { Value: '1', Text: '店铺01' },
        //    { Value: '2', Text: '店铺02' }]
        var selectInit = {
            eles: '#selectShops',
            emptyTitle: '选择店铺', //设置没有选择属性时，出现的标题
            data: shopList,
            searchType: 1, //1出现搜索框，不设置不出现搜索框
            showWidth: '250px', //显示下拉的宽
            isRadio: false, //有设置，下拉框改为单选
            allSelect: true,
            //selectData: []
            selectData: shopSelect
        };
        var selectMenuShop = new selectBoxModule2();  //按店铺审核多选下拉款
        selectMenuShop.initData(selectInit);
        // var shopIds = selectMenuShop.selectDates();  获取选中数据


        //按厂家审核 下拉多选框
        //var supplierList = [
        //    { Value: '1', Text: '厂家01' },
        //    { Value: '2', Text: '厂家02' }]
        var selectSupplierInit = {
            eles: '#selectSupplier',
            emptyTitle: '选择厂家', //设置没有选择属性时，出现的标题
            data: supplierList,
            searchType: 1, //1出现搜索框，不设置不出现搜索框
            showWidth: '250px', //显示下拉的宽
            isRadio: false, //有设置，下拉框改为单选
            allSelect: true,
            //selectData: []
            selectData: supplierSelect
        };
        var selectMenuSupplier = new selectBoxModule2();  //按店铺审核多选下拉款
        selectMenuSupplier.initData(selectSupplierInit);
        // var supplierIds = selectMenuShop.selectDates();  获取选中数据


        //按商家审核 下拉多选框
        //var agentList = [
        //    { Value: '1', Text: '商家01' },
        //    { Value: '2', Text: '商家02' }]
        var selectAgentInit = {
            eles: '#selectAgent',
            emptyTitle: '选择商家', //设置没有选择属性时，出现的标题
            data: agentList,
            searchType: 1, //1出现搜索框，不设置不出现搜索框
            showWidth: '250px', //显示下拉的宽
            isRadio: false, //有设置，下拉框改为单选
            allSelect: true,
            //selectData: []
            selectData: agentSelect
        };
        var selectMenuAgent = new selectBoxModule2();  //按店铺审核多选下拉款
        selectMenuAgent.initData(selectAgentInit);
        // var agentIds = selectMenuShop.selectDates();  获取选中数据


        //按留言备注审核 下拉多选框
        //var remarkList = [
        //    {Value: '1', Text: '平台买家留言' },
        //    { Value: '2', Text: '平台卖家备注' },
        //    { Value: '3', Text: '分销代发备注'}]
        var selectRemarkInit = {
            eles: '#selectRemark',
            emptyTitle: '选择留言备注', //设置没有选择属性时，出现的标题
            data: remarkList,
            searchType: 1, //1出现搜索框，不设置不出现搜索框
            showWidth: '250px', //显示下拉的宽
            isRadio: false, //有设置，下拉框改为单选
            allSelect: true,
            //selectData: []
            selectData: remarkSelect
        };
        var selectMenuRemark = new selectBoxModule2();  //按店铺审核多选下拉款
        selectMenuRemark.initData(selectRemarkInit);
        // var remarkIds = selectMenuShop.selectDates();  获取选中数据
    }



    //监听离开页面
    // monitorLive(function () { }, function () { }, 0)
    function monitorLive(callBack01, callBack02, type) {   //callBack01为离开时触发回调  callBack02点击取消时触发回调   type取值为0时，会有提示确定窗口
        window.onbeforeunload = function b() {
            document.body.setAttribute("onbeforeunload", "goodbye()");  //监听离开页面
            setTimeout(function () {
                setTimeout(beforeloadResult, 50)
            }, 50);
            if (typeof callBack01 == "function") {
                callBack01();
            }
            if (type == 0) {
                return '是否保存已修改的内容？';
            }
        };
        function beforeloadResult() {
            if (typeof callBack01 == "function") {
                callBack02();
            }
        }
    }

    //加载失效店铺
    var loadFilterInvalid = function () {
        commonModule.Ajax({
            url: "/Common/LoadCommonSetting",
            type: "POST",
            data: { settingKey: "/System/Config/Fendan/FilterInvalidShopId" },
            success: function (rsp) {
                if (rsp.Success && rsp.Data && rsp.Data != '') {
                    $("input[name='FilterInvalid']:first-child").attr("checked", false);
                } else {
                    $("input[name='FilterInvalid']:first-child").attr('checked', true);
                }
                var cuurInvalid = $('.shopPastWrap-num').text();
                if (cuurInvalid != '' && cuurInvalid != undefined) {
                    $("input[name='FilterInvalid']:first-child").attr('checked', true);
                }
                layui.form.render('checkbox');
            }
        });
    }

    //抖店售后小助手
    var loadAfterSale = function () {
        commonModule.LoadCommonSetting("/System/Fendan/AfterSale/NoSendRefund", true, function (rsp) {
            if (rsp.Success) {
                if (rsp.Data == 1) {
                    $("input[name='NoSendRefund']:first-child").attr('checked', true);
                }
                layui.form.render('checkbox');
            }
        });
    }
    return module;
}(distributeSetModule || {}, jQuery, layer));
