using DianGuanJiaApp.Data.Entity;

namespace DianGuanJiaApp.Services.PlatformService
{
    /// <summary>
    /// 淘宝买菜(新)
    /// 直接使用淘工厂的方法
    /// </summary>
    public class TaobaoMaiCaiV2PlatformService : AlibabaC2MService
    {
        /// <summary>
        /// 淘宝买菜(新)
        /// </summary>
        /// <param name="shop"></param>
        public TaobaoMaiCaiV2PlatformService(Shop shop) : base(shop)
        {
        }
    }
}