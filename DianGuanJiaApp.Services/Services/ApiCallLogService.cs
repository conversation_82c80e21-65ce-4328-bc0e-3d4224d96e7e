using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Helpers;
using DianGuanJiaApp.Data.Enum;
using NPOI.SS.UserModel;

namespace DianGuanJiaApp.Services
{

    public partial class ApiCallLogService : BaseService<ApiCallLog>
    {

        static ApiCallLogService()
        {
            //InitLogConsumerTask();
        }

        public static Task task = null;

        public static object _taskInitLock = new object();

        public static ConcurrentQueue<ApiCallLog> LogQueue = new ConcurrentQueue<ApiCallLog>();

        /// <summary>
        /// 初始化日志消费Task
        /// </summary>
        private static void InitLogConsumerTask()
        {
            if (task == null)
            {
                lock (_taskInitLock)
                {
                    if (task == null)
                    {
                        task = Task.Factory.StartNew(() =>
                        {

                        });
                    }
                }
            }
        }

        private static void SendLog()
        {
            try
            {
                var maxSize = 10;
                if (CustomerConfig.IsDebug)
                    maxSize = 1;
                if (LogQueue.Count > maxSize)
                {
                    //取出100条日志，写入日志库
                    var logs = new List<ApiCallLog>();
                    for (int i = 0; i < maxSize; i++)
                    {
                        if (LogQueue.IsEmpty)
                            break;
                        ApiCallLog logModel;
                        LogQueue.TryDequeue(out logModel);
                        if (logModel != null)
                            logs.Add(logModel);
                    }
                    AddLog(logs); //入库
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"接口调用日志，处理失败：{ex}");
            }
        }
        public static void PutLog(ApiCallLog log)
        {
            PutLog(log, new ApiCallLogSetting() { IsEnabled = true, IsLogReq = true, IsLogRsp = true }, null);
        }
        /// <summary>
        /// 推送日志到队列
        /// </summary>
        /// <param name="log">请求日志对象</param>
        /// <param name="logSetting">日志记录的配置</param>
        /// <param name="isSuccess">请求是否成功的判断</param>
        public static void PutLog(ApiCallLog log, ApiCallLogSetting logSetting, Func<string, bool> isSuccess)
        {
            if (log == null)
                return;

            if (logSetting == null)
                return;

            try
            {
                //只记录发生错误的请求
                if (logSetting.IsOnlyLogErrReq)
                {
                    if (isSuccess(log.Rsp))
                        return; //请求成功的，不记录日志
                }

                //判断api是否启用了日志记录
                if (CheckApiLogIsEnabled(log))
                    LogQueue.Enqueue(log);
                SendLog();
            }
            catch (Exception ex)
            {
                Log.WriteError($"Api请求日志保存失败，数据：{log.ToJson()},错误：{ex.Message}", $"ApiCallLog-{log.Pt}-{DateTime.Now.ToString("yyyy-MM-dd")}.txt");
            }
        }

        private static void AddLog(List<ApiCallLog> logs)
        {
            if (logs == null || logs.Count == 0)
                return;
            ThreadPool.QueueUserWorkItem(state =>
            {
                try
                {
                    //if (CustomerConfig.IsDebug)
                    //    Log.Debug($"Api请求日志保存，数据：{logs.ToJson()}", $"ApiCallLog-{logs.FirstOrDefault()?.Pt}-debug-{DateTime.Now.ToString("yyyy-MM-dd")}.txt");

                    var log = logs.First();
                    if (log.Pt == PlatformType.TouTiao.ToString())
                    {
                        var volcanoLogService = new VolcanoLogHelper(VolcanoLogProject.FenDanProjectId, VolcanoLogTopic.ApiCallLogTopicId);
                        volcanoLogService.WriteLog(logs);
                    }
                    else
                    {
                        //var logService = new Utility.AliLog.AliLogService(15000);
                        //var putLogs = new AliPutLogs<IAliPutLogs>
                        //{
                        //    Project = "print-system-log", //打单项目
                        //    Logstore = "api-call-log",
                        //    Topic = "api-call-log",
                        //    Data = logs.Select(f => f as IAliPutLogs).ToList()
                        //};
                        //logService.PutLog(putLogs);

                        //同步日志
                        ApiCallLogHelper.Instance.WriteLog("api-call-log", logs);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"Api请求日志保存失败，数据：{logs.ToJson()},错误：{ex.Message}", $"ApiCallLog-{logs.FirstOrDefault()?.Pt}-{DateTime.Now.ToString("yyyy-MM-dd")}.txt");
                }
            });
        }

        /// <summary>
        /// 检查api是否启用日志记录
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        private static bool CheckApiLogIsEnabled(ApiCallLog log)
        {
            var enableApiLog = HttpContext.Current?.Request?.Headers["enable-api-logger"];
            if (enableApiLog == "1" || enableApiLog == "true")
                return true;
            return CheckApiLogIsEnabled(log.Pt, log.ShopId, log.ApiName).IsEnabled;
        }

        /// <summary>
        /// 检查api是否启用日志记录
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        public static ApiCallLogSetting CheckApiLogIsEnabled(string pt, int sid, string api)
        {
            ApiCallLogSetting setting = null;
            try
            {
                api = api.ToLower();

                Dictionary<string, ApiCallLogSetting> settingDict;
                //从站点缓存中获取配置
                var key = $"/ApiCallLog/Config/{pt}/{sid}";
                settingDict = HttpRuntime.Cache[key] as Dictionary<string, ApiCallLogSetting>;
                if (settingDict == null)
                {
                    //获取平台全局配置
                    key = $"/ApiCallLog/Config/{pt}";
                    settingDict = HttpRuntime.Cache[key] as Dictionary<string, ApiCallLogSetting>;
                }

                if (settingDict != null)
                {
                    setting = settingDict.ContainsKey(api) == true ? settingDict[api] : null;
                }
                else
                {
                    //配置为空，从数据库里配置表读取（有redis缓存）
                    var commonSettingService = new CommonSettingService();
                    var apiLogSetting = commonSettingService.Get($"/ApiCallLog/Config/{pt}", sid);
                    if (apiLogSetting == null)
                        apiLogSetting = commonSettingService.Get($"/ApiCallLog/Config/{pt}", 0);

                    if (apiLogSetting != null)
                    {
                        var settingList = apiLogSetting.Value?.ToList<ApiCallLogSetting>();
                        settingDict = settingList?.ToLookup(f => f.ApiName?.ToLower(), f => f).ToDictionary(f => f.Key, f => f.FirstOrDefault());
                    }

                    if (settingDict == null)
                        settingDict = new Dictionary<string, ApiCallLogSetting>();

                    //获取配置
                    setting = settingDict.ContainsKey(api) == true ? settingDict[api] : null;

                    //配置缓存到站点，15分钟过期
                    var setKey = $"/ApiCallLog/Config/{pt}";
                    if (apiLogSetting?.ShopId > 0)
                        setKey = $"/ApiCallLog/Config/{pt}/{sid}";
                    HttpRuntime.Cache.Insert(setKey, settingDict, null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
                }

                if (CustomerConfig.IsDebug)
                    Log.Debug($"Api：{api}，请求日志保存配置Key：{key}，配置值：{settingDict.ToJson()}", $"ApiCallLog-{pt}-debug-{DateTime.Now.ToString("yyyy-MM-dd")}.txt");


                //判断配置
                //如果当前api没有配置，尝试读取通用配置
                if (setting == null)
                {
                    if (settingDict != null && settingDict.ContainsKey("*"))
                    {
                        //读取通用配置
                        setting = settingDict["*"];
                    }
                }

                //再判断配置
                if (setting == null || !setting.IsEnabled)
                {
                    //不记录日志
                    setting = new ApiCallLogSetting() { IsEnabled = false };
                }
                else
                {
                    //是否指定运行时间区间
                    if (setting.LogDateRangeStart.HasValue)
                    {
                        if (DateTime.Now < setting.LogDateRangeStart)
                            setting.IsEnabled = false; //未到设定的时间，不启用
                    }

                    if (setting.LogDateRangeEnd.HasValue)
                    {
                        if (DateTime.Now > setting.LogDateRangeEnd)
                            setting.IsEnabled = false; //过了设定的时间，不启用
                    }

                    //是否指定时间区间定时记录
                    if (setting.LogTimaRange != null && setting.LogTimaRange.Any())
                    {
                        //判断当前时间是否在设定的时间区间内
                        var now = DateTime.Now;
                        var timeRange = setting.LogTimaRange.Select(f => new Tuple<DateTime, DateTime>(
                            new DateTime(now.Year, now.Month, now.Day, f.SHour, f.SMinute, 0),
                            new DateTime(now.Year, now.Month, now.Day, f.EHour, f.EMinute, 0)
                         ));

                        if (!timeRange.Any(t => now >= t.Item1 && now <= t.Item2))
                        {
                            setting.IsEnabled = false; //未在设定的时间区间内，不启用
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"检查Api是否启用调用日志报错：{ex}");
                setting = new ApiCallLogSetting() { IsEnabled = false };
            }
            return setting;
        }

    }

    public class ApiCallLogHelper
    {
        private static readonly ApiCallLogHelper instance = new ApiCallLogHelper();

        private static LogHelper _logHelper;
        private ApiCallLogHelper()
        {
            _logHelper = new LogHelper(Config.SlsProject, LogStoreNames.ApiCallLog);
        }

        public static LogHelper Instance
        {
            get
            {
                return _logHelper;
            }
        }
    }
}
