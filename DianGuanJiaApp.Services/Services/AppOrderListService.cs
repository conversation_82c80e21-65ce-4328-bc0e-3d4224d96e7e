using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Other;

namespace DianGuanJiaApp.Services
{

    public partial class AppOrderListService : BaseService<Data.Entity.AppOrderList>
    {
        #region ˽�б���

        private AppOrderListRepository _repository;

        #endregion

        public AppOrderListService()
        {
            _repository = new AppOrderListRepository();
            this._baseRepository = _repository;
        }

        public AppOrderListService(string configureDbConnectionString)
        {
            _repository = new AppOrderListRepository(configureDbConnectionString);
            this._baseRepository = _repository;
        }


        public int AddModel(AppOrderList aol)
        {
            return _repository.Add(aol);
        }


        public AppOrderList GetModel(string memberId, string createTime)
        {
            return _repository.GetModel(memberId, createTime);
        }

        public List<AppOrderList> GetModels(string platformType, List<string> memberIds, List<string> orderIds)
        {
            return _repository.DbConnection.Query<AppOrderList>("SELECT Id,SkuVersion,PlatformType,MemberId,OrderItemNum FROM AppOrderList WITH(NOLOCK) WHERE PlatformType=@PlatformType AND MemberId IN @MemberId AND OrderItemNum IN @OrderItemNum", new
            {
                PlatformType = platformType,
                MemberId = memberIds,
                OrderItemNum = orderIds
            })?.ToList();
        }

        public DateTime? GetNewTime(string memberId)
        {
            return _repository.GetNewTime(memberId);
        }
        public DateTime? GetAlibabaExpiredTime(string memberId, string appKey)
        {
            return _repository.GetAlibabaExpiredTime(memberId, appKey);
        }
        /// <summary>
        /// ��ȡ���ﶩ���İ汾����Ϣ
        /// </summary>
        /// <param name="memberId"></param>
        /// <returns></returns>
        public string GetAlibabaPlatformVersion(string memberId)
        {
            return _repository.GetAlibabaPlatformVersion(memberId);
        }
        public List<AppOrderList> GetListByMemberId(string memberId,string platformType)
        {
            return _repository.DbConnection.Query<AppOrderList>("SELECT * FROM AppOrderList WITH(NOLOCK) WHERE MemberId = @MemberId AND PlatformType=@PlatformType", new
            {
                MemberId = memberId,
                PlatformType = platformType
            })?.ToList();
        }


        public List<AppOrderList> GetListByOrderNum(string orderNum)
        {
            return _repository.DbConnection.Query<AppOrderList>("SELECT * FROM AppOrderList WITH(NOLOCK) WHERE OrderItemNum = @OrderItemNum", new
            {
                OrderItemNum = orderNum
            })?.ToList();
        }

        public AppOrderList GetByOrderNum(string orderNum, string platformType)
        {
            return _repository.DbConnection.QueryFirstOrDefault<AppOrderList>("SELECT top 1 * FROM AppOrderList WITH(NOLOCK) WHERE OrderItemNum = @OrderItemNum AND PlatformType=@platformType", new
            {
                OrderItemNum = orderNum,
                platformType
            });
        }

        public AppOrderList GetModelById(int id)
        {
            return _repository.DbConnection.Query<AppOrderList>("SELECT * FROM AppOrderList WITH(NOLOCK) WHERE Id = @Id", new { Id =id}).FirstOrDefault(); 
        }


        public int UpdateModel(AppOrderList app)
        {
          return  _repository.DbConnection.Update(app);
        }

        public int UpdateSkuVersion(AppOrderList app)
        {
            return _repository.DbConnection.Execute("UPDATE dbo.AppOrderList SET SkuVersion=@SkuVersion,SkuName=@SkuName WHERE Id=@Id",new {app.Id,app.SkuVersion,app.SkuName });
        }
        public int UpdateStatus(AppOrderList app)
        {
            return _repository.DbConnection.Execute("UPDATE dbo.AppOrderList SET BizStatus=@BizStatus,BizStatusExt=@BizStatusExt,GmtConfirm=@GmtConfirm,GmtServiceBegin=@GmtServiceBegin,GmtServiceEnd=@GmtServiceEnd WHERE Id=@Id", new { app.Id, app.BizStatus, app.BizStatusExt,app.GmtConfirm,app.GmtServiceEnd,app.GmtServiceBegin });
        }

        public DateTime? GetLastServceEndTime(string memberId,string platformType)
        {
            return _repository.GetLastServceEndTime(memberId, platformType);
        }


        /// <summary>
        /// ��ѯĳ��ʱ��εĹ����û�
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="platformTypes"></param>
        /// <returns></returns>
        public List<AppOrderList> GetMemberIdListByServiceEndTime(DateTime startTime, DateTime endTime, List<string> platformTypes)
        {
            return _repository.GetMemberIdListByServiceEndTime(startTime, endTime, platformTypes);
        }

        /// <summary>
        /// ��ѯĳ��ʱ��������û�
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="platformTypes"></param>
        /// <returns></returns>
        public List<AppOrderList> GetMemberIdListByCreateTime(DateTime startTime, DateTime endTime, List<string> platformTypes)
        {
            return _repository.GetMemberIdListByCreateTime(startTime, endTime, platformTypes);
        }
        public AppOrderList JTokenToModel(Newtonsoft.Json.Linq.JToken jtoken, PlatformType platformType)
        {

            AppOrderList model = new AppOrderList();
            model.MemberId = jtoken?.Value<string>("kdtId");
            model.LoginId = jtoken?.Value<string>("buyerId");
            model.BizStatus = jtoken?.Value<string>("status");
            model.BizStatusExt = jtoken?.Value<string>("skuIntervalText"); //�ײ���Ч��
            model.ProductName = jtoken?.Value<string>("skuVersionText");
            var price = jtoken?.Value<decimal>("price");
            if (price != null && price > 0)
                model.PaymentAmount = (price / 100).ToString();
            model.ExecutePrice = jtoken?.Value<string>("");
            model.RefundFee = jtoken?.Value<string>("");

            var createtime = jtoken?.Value<string>("payTime");
            var createDate = DateConverter.ConvertStringToDateTime(createtime);

            if (createDate != null)
            {
                model.GmtCreate = createDate.Value;

                int num = 0;
                int.TryParse(model.BizStatusExt, out num);
                var gmtServiceEnd = createDate.Value.AddDays(num);

                model.GmtServiceEnd = gmtServiceEnd;
                model.GmtServiceBegin = createDate.Value;
                model.GmtConfirm = createDate.Value;
            }


            model.OrderItemNum = jtoken?.Value<string>("orderNo");

            model.PlatformType = platformType.ToString();
            model.AppKey = jtoken?.Value<string>("appId");
            return model;
        }

        /// <summary>
        /// ��ȡ���һ���ɹ��Ķ�����¼���������˿�
        /// </summary>
        /// <param name="appkey"></param>
        /// <param name="ptShopId"></param>
        /// <returns></returns>
        public AppOrderList GetLastEndOrdersWhere(string appkey, string ptShopId)
        {
            return _repository.GetLastEndOrdersWhere(appkey, ptShopId);
        }
    }
}
