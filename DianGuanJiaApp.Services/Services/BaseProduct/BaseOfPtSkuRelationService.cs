using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Data.Model;
using System.Threading.Tasks;
using System.Web;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Warehouse.Model.Request;
using DianGuanJiaApp.Data.Repository.Settings;
using Dapper;
using Org.BouncyCastle.Asn1.Ocsp;
using DianGuanJiaApp.Utility.Web;

namespace DianGuanJiaApp.Services.BaseProduct
{
    /// <summary>
    /// BaseOfPtSkuRelation 相关服务/逻辑处理
    /// </summary>
    public partial class BaseOfPtSkuRelationService : BaseProductBaseService<BaseOfPtSkuRelation>
    {
        private BaseOfPtSkuRelationRepository _repository;
        //private BaseProductSkuSupplierConfigService _baseSkuSupplierService = new BaseProductSkuSupplierConfigService();
        //private BaseProductSkuService _baseProductSkuService = new BaseProductSkuService();
        private readonly DbConfigRepository _dbConfigRepository = new DbConfigRepository();

        public const int BATCH_SIZE = 500;//每批处理数量

        /// <summary>
        /// 关联脏数据处理策略  库存数据库与基础商品库
        /// </summary>
        public const string DIRTY_RELATION_HANDLE_POLICY = "DianGuanJiaApp:BaseProduct:DirtyRelationHandlePolicy";

        /// <summary>
        /// 默认使用当前登录账号的FxUserId
        /// </summary>
        public BaseOfPtSkuRelationService()
        {
            _repository = new BaseOfPtSkuRelationRepository();
            _baseRepository = _repository;
        }

        /// <summary>
        /// 指定fxUserId
        /// </summary>
        /// <param name="fxUserId"></param>
        public BaseOfPtSkuRelationService(int fxUserId)
        {
            _repository = new BaseOfPtSkuRelationRepository(fxUserId);
            _baseRepository = _repository;
        }

        /// <summary>
        /// 指定连接字符串
        /// </summary>
        /// <param name="connectionString"></param>
        public BaseOfPtSkuRelationService(string connectionString, bool isMySQL = false)
        {
            _repository = new BaseOfPtSkuRelationRepository(connectionString, isMySQL);
            _baseRepository = _repository;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="isBaseProductDb">是否基础商品库，false时查普通业务库</param>
        /// <param name="fxUserId">是否指定用户，为0时未指定</param>
        public BaseOfPtSkuRelationService(bool isBaseProductDb, int fxUserId = 0)
        {
            _repository = new BaseOfPtSkuRelationRepository(isBaseProductDb, fxUserId);
            _baseRepository = _repository;
        }


        /// <summary>
        /// 批量添加
        /// </summary>
        /// <param name="models"></param>
        /// <param name="fromTool"></param>
        public void BatchAdd(List<BaseOfPtSkuRelation> models, bool fromTool = false)
        {
            if (models.IsNullOrEmptyList())
                return;
            if (fromTool == false)
            {
                UpdateBaseInfo(models);
            }
            _repository.BatchAdd(models);
        }

        /// <summary>
        /// 补充基础商品信息
        /// </summary>
        /// <param name="models"></param>
        public static void UpdateBaseInfo(List<BaseOfPtSkuRelation> models)
        {
            var fxUserId = models.First().FxUserId;

            var baseSkuList = new List<BaseProductSku>();
            var baseProductList = new List<BaseProductEntity>();

            // 判断是否为精选平台
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                var baseSkuUids = models.Select(a => a.BaseProductSkuUid).Distinct().ToList();
                var baseSpuUids = models.Select(a => a.BaseProductUid).Distinct().ToList();

                // 获取基础商品sku信息
                var baseSkuService = new BaseProductSkuService();
                baseSkuList = baseSkuService.GetListBySkuUids(baseSkuUids, fxUserId);

                // 获取基础商品信息
                var baseProductService = new BaseProductEntityService();
                baseProductList = baseProductService.GetListByUids(baseSpuUids, fxUserId);
            }
            else
            {
                var skuUids = models.Select(a => a.BaseProductSkuUid).Distinct().ToList();
                var sendModel = new BaseProductSkuSimpleReq { SkuUId = skuUids, FxUserId = fxUserId };

                // 跨云获取数据
                var isPdd = CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString();
                var targetSiteUrl = isPdd ? CustomerConfig.AlibabaMessageDomainForPdd : CustomerConfig.AlibabaFenFaSystemUrl;
                const string apiUrl = "/BaseProductApi/GetBaseProductInfo";
                targetSiteUrl = targetSiteUrl.TrimEnd("/") + apiUrl;

                Log.Debug($"跨云查询基础商品信息，targetSiteUrl={targetSiteUrl}，lastModel={sendModel.ToJson()}");

                // 获取基础商品sku信息
                var result = WebCommon.PostFxSiteApi<BaseProductSkuSimpleReq, List<BaseProductEntity>>(targetSiteUrl,
                    fxUserId, sendModel, "跨云查询基础商品信息", isEncrypt: true);

                if (result != null && result.Count != 0)
                {
                    baseSkuList = result.SelectMany(x => x.Skus).ToList();
                    baseProductList = result;
                }
            }

            // 补充字段
            models.ForEach(model =>
            {
                // 删除不处理
                if (model.Status == 0) return;

                var baseSku = baseSkuList.FirstOrDefault(a => a.Uid == model.BaseProductSkuUid);
                var baseProduct = baseProductList.FirstOrDefault(a => a.Uid == model.BaseProductUid);
                if (baseSku != null)
                {
                    model.BaseProductSkuCode = baseSku.SkuCode;
                    model.BaseProductSkuShortTitle = baseSku.ShortTitle;
                    var attrs = baseSku.Attributes.ToObject<List<SkuAttr>>();
                    if (attrs != null) model.BaseProductSkuSubject = string.Join(",", attrs.Select(a => $"{a.v}"));
                }
                if (baseProduct != null)
                {
                    model.BaseProductSubject = baseProduct.Subject;
                    model.BaseProductShortTitle = baseProduct.ShortTitle;
                    model.BaseProductSpuCode = baseProduct.SpuCode;
                }

                // 标记为更新
                model.Type = 2;
            });
        }

        /// <summary>
        /// 存在更新状态，不存在添加
        /// </summary>
        /// <param name="models"></param>
        /// <param name="fromTool">工具使用</param>
        /// <param name="updateFieldNames">更新字段</param>
        public void Merger(List<BaseOfPtSkuRelation> models, bool fromTool = false, List<string> updateFieldNames = null)
        {
            if (models == null || models.Any() == false)
                return;
            var fxUserId = models.First().FxUserId;
            var skuCodes = models.Select(a => a.ProductSkuCode).ToList();
            var existRelations = GetAllListBySkuCode(skuCodes, fxUserId);

            //曾经被同一个基础规格绑定过，但状态为已解绑
            var unbindedList = new List<BaseOfPtSkuRelation>();
            //需要作为新增的数据
            var needAddList = new List<BaseOfPtSkuRelation>();
            models.ForEach(model =>
            {
                var exist = existRelations.FirstOrDefault(a => a.Status == 1 && a.ProductSkuCode == model.ProductSkuCode);

                var exist2 = existRelations.FirstOrDefault(a => a.Status != 1 && a.ProductSkuCode == model.ProductSkuCode && a.BaseProductSkuUid == model.BaseProductSkuUid);
                if (exist2 != null)
                    unbindedList.Add(exist2);

                if (exist == null && exist2 == null)
                    needAddList.Add(model);
            });

            if (unbindedList != null && unbindedList.Any())
            {
                if (fromTool == false)
                {
                    //曾经被同一个基础规格绑定过，但状态为已解绑
                    //只更新状态即可
                    unbindedList = unbindedList.Where(a => a.Status == 1).ToList();
                    BulkUpdateStatus(unbindedList, 1);
                }
                else
                {
                    unbindedList.ForEach(x =>
                    {
                        // 找到model，根据对应的字段更新
                        var model = models.FirstOrDefault(a => a.ProductSkuCode == x.ProductSkuCode);
                        if (model == null) return;

                        // 根据更新字段进行更新
                        updateFieldNames?.ForEach(field =>
                        {
                            var value = model.GetType().GetProperty(field)?.GetValue(model);
                            if (value != null)
                                x.GetType().GetProperty(field)?.SetValue(x, value);
                        });
                    });

                    BulkUpdate(unbindedList, updateFieldNames, true);
                }
            }

            if (needAddList != null && needAddList.Any())
            {
                //新增
                BatchAdd(needAddList, fromTool);
            }

        }

        /// <summary>
        /// 批量添加
        /// </summary>
        /// <param name="models"></param>
        /// <param name="updateFields"></param>
        /// <param name="fromTool"></param>
        public void BulkUpdate(List<BaseOfPtSkuRelation> models, List<string> updateFields = null, bool fromTool = false, bool isDeleteWarehouse = false)
        {
            if (models.IsNullOrEmptyList())
                return;
            // 默认更新字段
            if (updateFields.IsNullOrEmptyList())
                updateFields = new List<string>
                {
                    "BaseProductSubject", "BaseProductShortTitle", "BaseProductSkuSubject", "BaseProductSkuShortTitle",
                    "BaseProductSpuCode", "BaseProductSkuCode", "UpdateTime", "ProductSkuPtId", "ProductPtId",
                    "ProductCode", "ProductShopId", "ProductFxUserId",
                    "IsUseWarehouse", "Status"
                };

            // 工具直接更新
            if (fromTool) _repository.BulkUpdate(models, updateFields);
            else
            {
                UpdateBaseInfo(models);
                UpdateOrDelete(models, isDeleteWarehouse, updateFields);
            }
        }

        /// <summary>
        /// 批量设置库存为不使用
        /// </summary>
        /// <param name="models"></param>
        public void BulkDelete(List<BaseOfPtSkuRelation> models)
        {
            if (models.IsNullOrEmptyList())
                return;
            _repository.BulkDelete(models);
        }

        /// <summary>
        /// 根据SkuUid获取信息
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public List<BaseOfPtSkuRelation> GetListBySkuUid(long uid)
        {
            return _repository.GetListBySkuUid(uid);
        }

        public List<BaseOfPtSkuRelation> GetRelationBySkuUid(List<long> baseProductSkuUid, int fxUserId)
        {
            return _repository.GetRelationBySkuUid(baseProductSkuUid, fxUserId);
        }

        public void InsertsForDuplication(List<BaseOfPtSkuRelation> models, int isNotUpdate = 0,int fxUserId=0)
        {
            if (models == null || !models.Any())
                return;
            //清理源库ID
            models.ForEach(m => { m.Id = 0; });

            var batchSize = 500;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();

                //代码
                var codes = batchModels.Select(m => m.ProductSkuCode).Distinct().ToList();

                //存在,根据UniqueKey的字段查询
                var idAndCodes = _repository.GetListForDuplication(codes, " BaseProductSkuUid,ProductSkuCode ",fxUserId);

                //全部不存在
                if (idAndCodes.IsNullOrEmptyList())
                {
                    try
                    {
                        baseRepository.BulkWrite(batchModels, "BaseOfPtSkuRelation", maxSingleNum: 1);
                    }
                    catch (Exception e)
                    {
                        var db = baseRepository.DbConnection;
                        if (db.State == System.Data.ConnectionState.Closed)
                            db.Open();
                        using (db)
                        {
                            //单条
                            batchModels.ForEach(item =>
                            {
                                try
                                {
                                    db.Insert(item);
                                }
                                catch (Exception ex2)
                                {
                                    var errMsg = ex2.Message.ToLower();
                                    if (errMsg.Contains("duplicate key") || errMsg.Contains("pk_") ||
                                        errMsg.Contains("primary key"))
                                    {
                                        //忽略
                                    }
                                    else
                                    {
                                        throw ex2;
                                    }
                                }
                            });
                        }
                    }

                    continue;
                }

                //存在
                var existCodes = idAndCodes.Select(m => m.ProductSkuCode).ToList();
                var existKeys = idAndCodes.Select(m => m.UniqueKey).ToList();
                if (isNotUpdate == 0)
                {
                    // 需要更新的字段
                    var needUpdateFields = new List<string>()
                    {
                        "BaseProductSubject", "BaseProductShortTitle", "BaseProductSkuSubject", "BaseProductSkuShortTitle",
                        "BaseProductSpuCode", "BaseProductSkuCode", "UpdateTime", "Status"
                    };
                    var existList = _repository.GetListForDuplication(existCodes, selectFields: "*",fxUserId).Where(m=>existKeys.Contains(m.UniqueKey)).ToList();
                    var needUpdates = batchModels.Where(m => existKeys.Contains(m.UniqueKey)).ToList();

                    var keyFields = new List<string>() {  "BaseProductSkuUid", "ProductSkuCode" };
                    if (needUpdates.Any())
                    {
                        needUpdates.ForEach(o =>
                        {
                            var model = existList.FirstOrDefault(m => m.UniqueKey == o.UniqueKey);
                            if (model == null)
                            {
                                return;
                            }
                            o.Id = model.Id;
                        });
                        try
                        {
                            var tuple = EntityUtils.CompareFields(existList, needUpdates, needUpdateFields, keyFields);
                            needUpdates = tuple.Item2;
                            needUpdateFields = tuple.Item1;
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"BaseOfPtSkuRelation，与数据库旧数据比较失败：{ex}，将使用旧逻辑进行全量更新");
                        }
                        _repository.BatchUpdate(needUpdates, needUpdateFields, keyFields);
                        Log.Debug(()=>$"BaseOfPtSkuRelation 副本更新数据{needUpdates.Count}条，更新字段{needUpdateFields.ToJson()}", $"{nameof(BaseOfPtSkuRelation)}.txt");
                    }
                }

                //需要新增的记录
                var inserts = batchModels.Where(m => !existKeys.Contains(m.UniqueKey)).ToList();
                if (inserts.Any())
                {
                    try
                    {
                        baseRepository.BulkWrite(inserts, "BaseOfPtSkuRelation", maxSingleNum: 1);
                    }
                    catch (Exception e)
                    {
                        var db = baseRepository.DbConnection;
                        if (db.State == System.Data.ConnectionState.Closed)
                            db.Open();
                        using (db)
                        {
                            //单条
                            inserts.ForEach(item =>
                            {
                                try
                                {
                                    db.Insert(item);
                                }
                                catch (Exception ex2)
                                {
                                    var errMsg = ex2.Message.ToLower();
                                    if (!errMsg.Contains("duplicate key") &&!errMsg.Contains("pk_") &&!errMsg.Contains("primary key"))
                                    {
                                        throw ex2;
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取信息为复制副本
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <param name="whereFieldName"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        public List<BaseOfPtSkuRelation> GetList(List<string> codes, string selectFields = "*",
            string whereFieldName = "ProductSkuCode", int status = 1)
        {

            if (codes == null || codes.Any() == false)
                return new List<BaseOfPtSkuRelation>();

            var list = new List<BaseOfPtSkuRelation>();
            var batchSize = BATCH_SIZE;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetList(batchCodes, selectFields, whereFieldName, status);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }


        /// <summary>
        /// 从厂家库查询关联数据
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="supplierFxUserIds">需要查询的厂家用户Id</param>
        /// <param name="curDbName">当前库</param>
        /// <param name="selectFields"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<BaseOfPtSkuRelation> GetListFromSupplierDb(List<string> codes, List<int> supplierFxUserIds, string curDbName, string selectFields = "*",
            string whereFieldName = "ProductSkuCode")
        {
            //拼多多&抖店（排除拼多多旧库pdd_fendan_db） --> 从厂家库查
            if (curDbName != "pdd_fendan_db" && (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString() || CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString()))
            {
                var result = new List<BaseOfPtSkuRelation>();
                //到厂家库查询
                var supplierDbConfig = new DbConfigRepository().GetFxDbConfigModels(supplierFxUserIds, CustomerConfig.CloudPlatformType);
                supplierDbConfig?.GroupBy(g => g.ConnectionString).ToList().ForEach(g =>
                {
                    var curService = new BaseOfPtSkuRelationService(g.Key);
                    var curList = curService.GetList(codes, selectFields, whereFieldName);
                    if (curList != null && curList.Any())
                        result.AddRange(curList);
                });

                return result;
            }
            else
            {
                //当前库查询
                return GetList(codes, selectFields, whereFieldName);
            }
        }

        /// <summary>
        /// 根据SkuUid获取信息
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public Dictionary<long, List<BaseOfPtSkuRelation>> GetListBySkuUids(List<long> uids, int fxUserId)
        {
            return _repository.GetListBySkuUids(uids, fxUserId);
        }

        /// <summary>
        /// 批量更新状态
        /// </summary>
        /// <param name="models"></param>
        /// <param name="status"></param>
        public void BulkUpdateStatus(List<BaseOfPtSkuRelation> models, int status = 1)
        {
            if (models == null || models.Any() == false)
                return;

            _repository.BulkUpdateStatus(models, status);
        }

        /// <summary>
        /// 根据SkuUid获取信息
        /// </summary>
        /// <param name="skuUids"></param>
        /// <param name="skuCodes"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BaseOfPtSkuRelation> GetListBySkuModel(List<long> skuUids, List<string> skuCodes, int fxUserId, List<string> fields = null,  bool searchDelete= false)
        {
            if (skuUids.IsNullOrEmptyList() || skuCodes.IsNullOrEmptyList())
                return new List<BaseOfPtSkuRelation>();
            return _repository.GetListBySkuModel(skuUids, skuCodes, fxUserId, fields, searchDelete: searchDelete);
        }

        /// <summary>
        /// 根据平台商品code获取信息
        /// </summary>
        /// <param name="skuCodes"></param>
        /// <param name="fxUserId"></param>
        /// <param name="isCheck">是否检查有效性</param>
        /// <returns></returns>
        public List<BaseOfPtSkuRelation> GetListBySkuCode( List<string> skuCodes, int fxUserId, bool isCheck = false)
        {
            if (skuCodes.IsNullOrEmptyList())
                return new List<BaseOfPtSkuRelation>();

            // 超过500个skuCode，分批查询
            var list = new List<BaseOfPtSkuRelation>();
            var batchSize = BATCH_SIZE;
            var count = Math.Ceiling(skuCodes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = skuCodes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetListBySkuCode(batchCodes, fxUserId);
                if (isCheck)
                {
                    // 获取SkuUid
                    var skuUids = batchList.Select(a => a.BaseProductSkuUid).Distinct().ToList();
                    // 查询有无Sku
                    var existSkuUids = new BaseProductEntityService().CheckExistSkuUids(skuUids, fxUserId);
                    // 过滤无效数据
                    batchList = batchList.Where(a => existSkuUids.Contains(a.BaseProductSkuUid)).ToList();
                }
                if (batchList != null && batchList.Any())
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 根据店铺商品skuCode获取信息
        /// </summary>
        /// <param name="skuCodes"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BaseOfPtSkuRelation> GetAllListBySkuCode(List<string> skuCodes, int fxUserId)
        {
            return _repository.GetAllListBySkuCode(skuCodes, fxUserId);
        }

        /// <summary>
        /// 合并更新到业务库，仅工具使用
        /// </summary>
        /// <param name="models"></param>
        /// <param name="updateFieldNames"></param>
        public void ChoseMergeToBusinessDb(List<BaseOfPtSkuRelation> models, List<string> updateFieldNames = null)
        {
            if (models == null || !models.Any()) return;
            Log.Debug(() => $"更新基础商品关联关系Models: {models.ToJson()}");

            // 根据DbConfig.DbNameConfig.Id分组
            var groupModels = models.GroupBy(x => x.DbConfig.DbNameConfig.Id).ToList();
            if (!groupModels.Any()) return;

            const string updateSql = @"UPDATE BaseOfPtSkuRelation SET ProductSkuPtId = @ProductSkuPtId, ProductPtId = @ProductPtId, ProductCode = @ProductCode,
ProductShopId = @ProductShopId, ProductFxUserId = @ProductFxUserId, IsUseWarehouse = @IsUseWarehouse, UpdateTime = @UpdateTime, Status = @Status,
BaseProductSubject = @BaseProductSubject, BaseProductShortTitle = @BaseProductShortTitle, BaseProductSkuSubject = @BaseProductSkuSubject, 
BaseProductSkuShortTitle = @BaseProductSkuShortTitle, BaseProductSpuCode = @BaseProductSpuCode, BaseProductSkuCode = @BaseProductSkuCode
WHERE BaseProductSkuUid = @BaseProductSkuUid AND ProductSkuCode = @ProductSkuCode";

            const string insertSql = @"INSERT INTO BaseOfPtSkuRelation (BaseProductSkuUid, BaseProductUid, IsUseWarehouse, ProductSkuPtId, ProductPtId, 
ProductSkuCode, ProductCode, ProductShopId, ProductFxUserId, ProductPlatformType, FxUserId, UpdateTime, Status, CloudPlatform, 
BaseProductSubject, BaseProductShortTitle, BaseProductSkuSubject, BaseProductSkuShortTitle, BaseProductSpuCode, BaseProductSkuCode) 
VALUES (@BaseProductSkuUid, @BaseProductUid, @IsUseWarehouse, @ProductSkuPtId, @ProductPtId, @ProductSkuCode, @ProductCode, 
@ProductShopId, @ProductFxUserId, @ProductPlatformType, @FxUserId, @UpdateTime, @Status, @CloudPlatform, @BaseProductSubject,
@BaseProductShortTitle, @BaseProductSkuSubject, @BaseProductSkuShortTitle, @BaseProductSpuCode, @BaseProductSkuCode)";

            // 查询业务库，是否存在已关联的数据，若存在走更新，不存在走插入
            groupModels.ForEach(group =>
            {
                var locationModels = new List<BaseOfPtSkuRelation>();

                var dbConfig = group.First().DbConfig;
                if (dbConfig == null) return;

                var apiDbConfig = new ApiDbConfigModel { DbNameConfigId = dbConfig.DbNameConfig.Id, Location = dbConfig.DbServer.Location, PlatformType = dbConfig.DbServer.Location };
                var dbApi = new DbAccessUtility(apiDbConfig);

                // 判断是否全局开启了冷热
                var isGlobalEnableColdDb = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
                DbAccessUtility coldDbApi = null;
                if (isGlobalEnableColdDb&&dbConfig.EnableColdDb)
                {
                    var coldDbConfig = new ApiDbConfigModel { DbNameConfigId=dbConfig.ColdDbNameConfigId, Location=dbConfig.DbServer.Location, PlatformType=dbConfig.DbServer.Location };
                    coldDbApi = new DbAccessUtility(coldDbConfig);
                }

                var groupList = group.ToList();
                groupList.ForEach(x =>
                {
                    var dbLocation = x.DbConfig.DbServer.Location;

                    // 同云
                    if (dbLocation == CustomerConfig.CloudPlatformType)
                    {
                        locationModels.Add(x);
                    }
                    else
                    {
                        // 查询是否存在已关联的数据
                        const string sql = "SELECT * FROM BaseOfPtSkuRelation WHERE BaseProductSkuUid = @BaseProductSkuUid AND ProductSkuCode = @ProductSkuCode";
                        var exist = dbApi.Query<BaseOfPtSkuRelation>(sql, x).FirstOrDefault();
                        if (exist != null)
                        {
                            // 跨云
                            dbApi.ExecuteScalar(updateSql, x);
                            Log.Debug(() => $"跨云——更新数据：{x.ToJson()}");
                        }
                        else
                        {
                            dbApi.ExecuteScalar(insertSql, x);
                            Log.Debug(() => $"跨云——插入数据：{x.ToJson()}");
                        }
                        // 冷库
                        try
                        {
                            if (coldDbApi!=null)
                            {
                                var coldExist = coldDbApi.Query<BaseOfPtSkuRelation>(sql, x).FirstOrDefault();
                                if (coldExist != null)
                                {
                                    // 跨云
                                    coldDbApi.ExecuteScalar(updateSql, x);
                                    Log.Debug(() => $"跨云——更新冷库数据：{x.ToJson()}");
                                }
                                else
                                {
                                    coldDbApi.ExecuteScalar(insertSql, x);
                                    Log.Debug(() => $"跨云——插入冷库数据：{x.ToJson()}");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"跨云写入冷库数据失败,原因：{ex}");
                        }
                    }
                });
                if (locationModels.Any())
                {
                    // 查询
                    var skuUids = locationModels.Select(a => a.BaseProductSkuUid).Distinct().ToList();
                    var proSkuCodes = locationModels.Select(a => a.ProductSkuCode).Distinct().ToList();

                    var updateModels = new List<BaseOfPtSkuRelation>();
                    var baseOfPtSkuRelations = new List<BaseOfPtSkuRelation>();

                    // uid 超过 BATCH_SIZE 分批处理
                    var batchCount = (int)Math.Ceiling(skuUids.Count * 1.0 / BATCH_SIZE);
                    var repository = new BaseOfPtSkuRelationRepository(dbConfig.ConnectionString, false);
                    for (var i = 0; i < batchCount; i++)
                    {
                        var skuUidBatch = skuUids.Skip(i * BATCH_SIZE).Take(BATCH_SIZE).ToList();

                        // 分批查询并累加结果
                        var batchResult = repository.GetListBySkuModel(skuUidBatch, proSkuCodes, locationModels.First().FxUserId);
                        if (batchResult != null && batchResult.Any())
                        {
                            baseOfPtSkuRelations.AddRange(batchResult);
                        }
                    }

                    if (baseOfPtSkuRelations.Any())
                    {
                        // 从 model 中找到对应的数据进行更新
                        var existCodes = baseOfPtSkuRelations.Select(a => a.ProductSkuCode).ToList();
                        updateModels = locationModels.Where(a => existCodes.Contains(a.ProductSkuCode)).ToList();
                        Log.WriteLine($"同云——更新数据：{updateModels.ToJson()}");

                        if (updateModels.Any())
                        {
                            repository.BulkUpdate(updateModels, updateFieldNames);
                        }
                    }

                    // 插入
                    var insertModels = locationModels.Where(a => updateModels.All(b => b.ProductSkuCode != a.ProductSkuCode)).ToList();
                    Log.WriteLine($"同云——插入数据：{insertModels.ToJson()}");

                    if (insertModels.Any())
                    {
                        repository.BatchAdd(insertModels);
                    }
                    // 冷库,这里的coldDbApi仅用来判断是否需要写入冷库
                    if (coldDbApi!=null)
                    {
                        try
                        {
                            var coldRepository = new BaseOfPtSkuRelationRepository(dbConfig.ColdDbConnectionString, false);
                            var coldUpdateModels = new List<BaseOfPtSkuRelation>();
                            var coldBaseOfPtSkuRelations = new List<BaseOfPtSkuRelation>();

                            for (var i = 0; i < batchCount; i++)
                            {
                                var skuUidBatch = skuUids.Skip(i * BATCH_SIZE).Take(BATCH_SIZE).ToList();

                                // 分批查询并累加结果
                                var batchResult = coldRepository.GetListBySkuModel(skuUidBatch, proSkuCodes, locationModels.First().FxUserId);
                                if (batchResult != null && batchResult.Any())
                                {
                                    coldBaseOfPtSkuRelations.AddRange(batchResult);
                                }
                            }

                            if (coldBaseOfPtSkuRelations.Any())
                            {
                                // 从 model 中找到对应的数据进行更新
                                var existCodes = coldBaseOfPtSkuRelations.Select(a => a.ProductSkuCode).ToList();
                                coldUpdateModels = locationModels.Where(a => existCodes.Contains(a.ProductSkuCode)).ToList();
                                Log.WriteLine($"同云——更新数据：{coldUpdateModels.ToJson()}");

                                if (coldUpdateModels.Any())
                                {
                                    coldRepository.BulkUpdate(coldUpdateModels, updateFieldNames);
                                }
                            }

                            // 插入
                            var coldInsertModels = locationModels.Where(a => coldUpdateModels.All(b => b.ProductSkuCode != a.ProductSkuCode)).ToList();
                            Log.WriteLine($"同云——插入数据：{coldInsertModels.ToJson()}");

                            if (coldInsertModels.Any())
                            {
                                coldRepository.BatchAdd(coldInsertModels);
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"跨云写入冷库失败,原因:{ex}");
                        }
                    }
                }
            });
        }

        /// <summary>
        /// 关联同款
        /// </summary>
        /// <param name="model"></param>
        /// <param name="request"></param>
        /// <param name="fxUserId"></param>
        /// <param name="productSkuFx"></param>
        /// <returns></returns>
        public Tuple<bool, string> BaseProductSkuRelationBind(BaseProductSkuBindModel model, RequestHttpModel request,
            int fxUserId, List<ProductSkuFx> productSkuFx = null)
        {
            #region 关联基础
            var connectionString = SiteContext.Current.CurrentDbConfig.ConnectionString;
            var warehouseService = new WareHouseService(connectionString);
            var ptProductSkuService = new ProductSkuFxService();
            var messageService = new MessageRecordService();
            // 如果是自动关联，则不需要查询平台商品信息
            // 自动关联，用的是基础商品分库的Service
            var isAutoRelation = productSkuFx.IsNotNullOrEmpty();
            var dbName = request.DbName;
            var cloudPlatform = CustomerConfig.CloudPlatformType;
            if (dbName.IsNotNullOrEmpty())
            {
                try
                {
                    dbName = DES.DecryptDES(dbName, CustomerConfig.LoginCookieEncryptKey);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"平台dbName解析失败：{ex.Message}");
                    return new Tuple<bool, string>(false, "平台dbName解析失败！");
                }
            }
            else
            {
                return new Tuple<bool, string>(false, "平台dbName不能为空！");
            }
            model.IsAllUseWarehouse = true;//强制为true
            model.SkuList?.ForEach(a => { a.IsDisabledUseWarehouse = false; });//强制为false
            if (model.IsAllUseWarehouse == true)
            {
                // 需判断SkuCode是否存在库存系统
                var reqOne = new WarehouseSkuGetOneRequest
                {
                    WareHouseSkuCode = "-",
                    SkuCodeList = new List<string> { model.SkuCode }
                };

                var resOne = warehouseService.WarehouseSkuGetOne(reqOne);
                if (resOne.IsSucc)
                {
                    if (resOne.ExistSkuCode.Any() && resOne.ExistSkuCode.First() == model.SkuCode)
                    {
                        model.SkuList.ForEach(a => a.IsUseWarehouse = true);
                    }
                }
            }
            List<ProductSkuFx> ptProductSkus;
            // 平台商品
            if (isAutoRelation == false)
            {
                var ptProductSkuCodes = model.SkuList.Select(a => a.ProductSkuCode).Distinct().ToList();
                ptProductSkus = ptProductSkuService.GetProductSkuBySkuCode(ptProductSkuCodes);
            }
            else ptProductSkus = productSkuFx;  // 自动关联

            if (ptProductSkus == null || ptProductSkus.Count == 0)
                return new Tuple<bool, string>(false, "未找到平台商品信息！");

            // 基础商品绑定信息
            var BaseOfPtSkuRelations = new List<BaseOfPtSkuRelation>();
            var subModels = new List<MessageRecord>();
            ptProductSkus.ForEach(sku =>
            {
                var relation = new BaseOfPtSkuRelation();
                var current = model.SkuList.FirstOrDefault(a => a.ProductSkuCode == sku.SkuCode);
                relation.ProductSkuPtId = sku.SkuId;
                relation.ProductPtId = sku.PlatformId;
                relation.ProductSkuCode = sku.SkuCode;
                relation.ProductCode = sku.ProductCode;
                relation.ProductShopId = sku.ShopId;
                relation.ProductFxUserId = sku.SourceUserId.Value;
                relation.ProductPlatformType = sku.PlatformType;
                relation.CloudPlatform = cloudPlatform;

                relation.FxUserId = fxUserId;
                relation.CreateTime = DateTime.Now;
                relation.UpdateTime = DateTime.Now;
                relation.Status = 1;
                relation.IsUseWarehouse = false;
                relation.BaseProductSkuUid = model.BaseProductSkuUid;
                relation.BaseProductUid = model.BaseProductUid;
                if (isAutoRelation)
                {
                    relation.DbName = sku.DbName;
                    relation.RelationType = 1;
                    relation.CloudPlatform = sku.CloudPlatform;
                }
                BaseOfPtSkuRelations.Add(relation);
            });


            #endregion

            #region 关联同步

            // 同步到库存系统的数据
            var syncRelationList = new List<WarehouseSkuBindRequest>();
            var syncSourseList = model.SkuList
                .Where(a => a.IsUseWarehouse && a.IsDisabledUseWarehouse != true)
                .Select(a => a.ProductSkuCode)
                .ToList();
            syncRelationList = ptProductSkus
                .Where(a => syncSourseList.Contains(a.SkuCode))
                .Select(a => new WarehouseSkuBindRequest
                {
                    IsRollback = false,
                    PlatformSkuCode = a.SkuCode,
                    PlatformType = a.PlatformType,
                    ShopId = a.ShopId,
                    SkuCode = model.SkuCode,
                    DbName = a.DbName
                }).ToList();
            if (CustomerConfig.IsDebug) Log.WriteLine($"基础商品到库存系统绑定商品：{syncRelationList.ToJson()}");

            // 库存系统关联商品
            if (syncRelationList.Any() && !isAutoRelation)
            {
                // 异步处理
                syncRelationList.ForEach(req =>
                {
                    // 找到对应的关联数据
                    var relation = BaseOfPtSkuRelations.FirstOrDefault(a => a.ProductSkuCode == req.PlatformSkuCode);
                    if (relation == null) return;

                    var rep = warehouseService.BindProduct(req, true, true);
                    if (CustomerConfig.IsDebug) Log.WriteLine($"基础商品到库存系统绑定商品：{req.ToJson()}，结果：{rep.ToJson()}");
                    if (rep.IsSucc) relation.IsUseWarehouse = true;
                });
            }

            // 自动关联处理库存系统关联商品
            if (syncRelationList.Any() && isAutoRelation)
            {
                var msgList = syncRelationList.Select(req => new MessageRecord
                {
                    MsgType = BaseProductMsgType.WarehouseSkuBind,
                    FxUserId = fxUserId,
                    DbName = req.DbName,
                    ProductPlatformType = req.PlatformType,
                    DataJson = req.ToJson()
                })
                    .ToList();

                messageService.SendBusinessMessage(msgList);
            }

            // 基础商品绑定
            var uids = BaseOfPtSkuRelations.Select(a => a.BaseProductSkuUid).Distinct().ToList();
            var codes = BaseOfPtSkuRelations.Select(a => a.ProductSkuCode).Distinct().ToList();
            var relations = GetAllListBySkuCode(codes, fxUserId);

            // 已关联：所有已关联
            var relationsExistsY = BaseOfPtSkuRelations
                .Where(a => relations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.Status == 1)).ToList();
            // 未关联：所有未关联
            var relationsExistsN = BaseOfPtSkuRelations
                .Where(a => !relations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode)).ToList();
            // 未关联：当前未关联（已解绑）
            var relationsExistsCB = BaseOfPtSkuRelations
                .Where(a => relations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.BaseProductSkuUid == a.BaseProductSkuUid && b.Status == 0)).ToList();
            // 未关联：其他未关联（已解绑）
            var relationsExistsOB = BaseOfPtSkuRelations
                .Where(a => relations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.BaseProductSkuUid != a.BaseProductSkuUid && b.Status == 0)).ToList();

            // 去除重复(已解绑)
            relationsExistsOB =
                relationsExistsOB.Where(a => !relationsExistsCB.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.BaseProductSkuUid == a.BaseProductSkuUid)).ToList();


            if (relationsExistsY.Count > 0)
            {
                return new Tuple<bool, string>(false, "当前选择的平台商品，存在已关联其他基础商品！");
            }

            relationsExistsCB.ForEach(a =>
            {
                a.UpdateTime = DateTime.Now;
                a.Status = 1;
            });

            if (relationsExistsOB.Count > 0)
            {
                // 未存在绑定
                BatchAdd(relationsExistsOB);

            }
            if (relationsExistsN.Count > 0)
            {
                // 未存在绑定
                BatchAdd(relationsExistsN);
            }

            if (relationsExistsCB.Count > 0)
            {
                // 已存在绑定
                BulkUpdate(relationsExistsCB);
            }

            // 平台商品绑定（消息队列）
            var allSyncAelations = relationsExistsCB.Concat(relationsExistsN).Concat(relationsExistsOB).ToList();

            if (allSyncAelations.Count <= 0)
            {
                return new Tuple<bool, string>(false, "未存在关联商品同步数据！");
            }
            if (allSyncAelations.Count > 0 && !isAutoRelation) // 手工关联
            {
                allSyncAelations.ForEach(relation =>
                {
                    var subModel = new MessageRecord();
                    subModel.MsgType = BaseProductMsgType.BaseOfPtSkuRelationAdd;
                    subModel.FxUserId = fxUserId;
                    subModel.DbName = dbName;
                    subModel.ProductPlatformType = CloudPlatformType.Alibaba.ToString();
                    subModel.BusinessId = relation.ProductSkuCode;
                    subModel.DataJson = relation.ToJson();
                    subModels.Add(subModel);
                });

                // 关联日志
                try
                {
                    SetRelationRecords(model, allSyncAelations, fxUserId, RelationTypeEnum.Relation.ToInt());
                }
                catch (Exception ex)
                {
                    Log.WriteError($"用户{fxUserId}手动关联记录日志发生异常，关联模型：{model.ToJson()}，异常信息：{ex.StackTrace}");
                }

            }

            if (allSyncAelations.Count > 0 && isAutoRelation) // 自动关联
            {
                allSyncAelations.ForEach(relation =>
                {
                    var subModel = new MessageRecord();
                    subModel.MsgType = BaseProductMsgType.BaseOfPtSkuRelationAddByAuto;
                    subModel.FxUserId = fxUserId;
                    subModel.DbName = relation.DbName;
                    subModel.ProductPlatformType = relation.ProductPlatformType;
                    subModel.TargetCloud = relation.CloudPlatform;
                    subModel.BusinessId = relation.ProductSkuCode;
                    subModel.DataJson = relation.ToJson();
                    subModels.Add(subModel);
                });

                // 关联日志
                try
                {
                    SetRelationRecords(model, allSyncAelations, fxUserId, RelationTypeEnum.AutoRelation.ToInt());
                }
                catch (Exception ex)
                {
                    Log.WriteError($"用户{fxUserId}自动关联记录日志发生异常，关联模型：{model.ToJson()}，异常信息：{ex.StackTrace}");
                }
            }

            var res = messageService.SendBusinessMessage(subModels);

            #region 同步默认数据到平台商品sku

            if (model.IsUseSupplierFxUser == true)
            {
                if (model.SupplierFxUserId > 0 && (model.BaseProductSkuSupplierConfigs.IsNullOrEmptyList() || model.BaseProductSkuSupplierConfigs.Any(s => s.SupplierFxUserId.ToInt() <= 0)))
                {
                    return new Tuple<bool, string>(false, "请传入基础商品默认厂家配置");
                }
                else if (model.SupplierFxUserId == -1)
                {
                    if (model.BaseProductSkuSupplierConfigs.IsNullOrEmptyList())
                    {
                        model.BaseProductSkuSupplierConfigs = new List<BaseProductSkuSupplierConfigModel>()
                        {
                            new BaseProductSkuSupplierConfigModel()
                            {
                                SupplierFxUserId = fxUserId,
                                IsSelf = true,
                            }
                        };
                    }
                    else
                    {
                        model.BaseProductSkuSupplierConfigs.ForEach(bsc => { bsc.SupplierFxUserId = fxUserId; bsc.IsSelf = true; });
                    }
                }
                else if (model.BaseProductSkuSupplierConfigs==null)
                {
                    model.BaseProductSkuSupplierConfigs = new List<BaseProductSkuSupplierConfigModel>();
                }
            }

            var platRelationsDict = BaseOfPtSkuRelations.GroupBy(r => r.CloudPlatform).ToDictionary(g => g.Key, g => g.ToList());
            var sendSyncModels = new List<MessageRecord>();
            foreach (var platform in platRelationsDict.Keys)
            {
                var relationBindSyncPtSkuModel = new RelationBindSyncPtSkuModel()
                {
                    IsUpdateShortTitle = model.IsUseSkuShortTitle.HasValue && model.IsUseSkuShortTitle.Value && model.ShortTitle != null,
                    IsUpdateSettlePrice = model.IsUseSettlePrice.HasValue && model.IsUseSettlePrice.Value,
                    IsUpdateCostPrice = model.IsUseCostPrice.HasValue && model.IsUseCostPrice.Value,
                    IsUpdateDistributePrice = model.IsUseDistributePrice.HasValue && model.IsUseDistributePrice.Value,
                    IsUserDefaultSupplier = model.IsUseSupplierFxUser.HasValue && model.IsUseSupplierFxUser.Value,
                    BaseOfPtSkuRelations = platRelationsDict[platform],
                    FxUserId = fxUserId,
                    IP = request.UserHostAddress,
                    SettlePrice = model.SettlePrice,
                    DistributePrice = model.DistributePrice,
                    CostPrice = model.CostPrice,
                    ShortTitle = model.ShortTitle,
                    SupplierFxUserId = model.SupplierFxUserId,
                    BaseProductSkuSupplierConfigs = model.BaseProductSkuSupplierConfigs

                };
                var taskCode = Guid.NewGuid().ToString().ToShortMd5();
                Log.Debug($"基础商品关联同步平台商品消息：{taskCode}");
                sendSyncModels.Add(new MessageRecord()
                {
                    BusinessId = taskCode,
                    MsgType = BaseProductMsgType.RelationBindSyncPtSku,
                    FxUserId = fxUserId,
                    DbName = dbName,
                    ProductPlatformType = platform,
                    DataJson = relationBindSyncPtSkuModel.ToJson()
                });
            }
            messageService.SendBusinessMessage(sendSyncModels);
            //messageService.SyncPtSkuFromRelationBind(sendSyncModels);

            #endregion
            #endregion



            return new Tuple<bool, string>(true, "关联成功！");
        }

        /// <summary>
        /// 从数据库获取关联记录
        /// </summary>
        /// <param name="relationIds"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BaseProductRelationRecord> GetRelationRecords(List<long> relationIds, int fxUserId)
        {
            if (relationIds.IsNullOrEmptyList())
                return new List<BaseProductRelationRecord>();
            var records = _repository.GetRecordsByRelationIds(relationIds);
            if (records.IsNullOrEmptyList())
                return records;

            // 解绑关联且多厂家
            var unBindRecordIds = records.Where(r => r.RelationType == RelationTypeEnum.UnBindRelation.ToInt() && r.SupplierFxUserId.Equals("-2"))?.Select(r => r.Id).ToList();
            Dictionary<long, List<BaseProductRelationRecordExt>> extRecordDict = null;
            if (!unBindRecordIds.IsNullOrEmptyList())
            {
                extRecordDict = _repository.GetRecordExtByRecordIds(unBindRecordIds)
                                           .GroupBy(r => r.RecordDbId)
                                           .ToDictionary(r => r.Key, r => r.ToList());
            }

            var suppliers = new SupplierUserService().GetSupplierList(fxUserId, needEncryptAccount: true);//厂家
            var supplierNameDict = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");

            records.ForEach(record =>
            {
                var supplierIds = record.SupplierFxUserId.Split(',').Select(i => i.ToInt()).ToList();
                record.RelationSupplierModels = new List<BaseProductRelationRecordExt>();
                if (record.RelationType != RelationTypeEnum.UnBindRelation.ToInt()) // 关联
                {
                    if (!supplierIds.Any(i => i == -1)) // 非自营
                    {
                        supplierIds.ForEach(sid =>
                        {
                            record.RelationSupplierModels.Add(new BaseProductRelationRecordExt()
                            {
                                RecordDbId = record.Id,
                                SupplierFxUserId = sid,
                                SupplierMobileAndRemark = supplierNameDict[sid],
                                SettlePrice = record.SettlePrice
                            });
                        });

                    }
                }
                else  // 解绑关联
                {
                    if (supplierIds.Any(i => i == -2)) //多厂家
                    {
                        extRecordDict.TryGetValue(record.Id, out var extRecords);
                        if (!extRecords.IsNullOrEmptyList())
                        {
                            extRecords.ForEach(m => { m.SupplierMobileAndRemark = supplierNameDict[m.SupplierFxUserId]; });
                            record.RelationSupplierModels = extRecords;
                        }
                    }
                    else
                    {
                        var sid = supplierIds.FirstOrDefault();
                        record.RelationSupplierModels.Add(new BaseProductRelationRecordExt()
                        {
                            RecordDbId = record.Id,
                            SupplierFxUserId = sid,
                            SupplierMobileAndRemark = supplierNameDict[sid],
                            SettlePrice = record.SettlePrice
                        });
                    }
                }
            });

            return records;
        }

        /// <summary>
        /// 设置关联记录，需要在关联数据落库后再调用
        /// </summary>
        /// <param name="relationModel">绑定模型</param>
        /// <param name="relations">新增的关联，更新的不要传</param>
        /// <param name="fxUserId"></param>
        /// <param name="relationType">关联类型 0=手动关联 1=自动关联</param>
        /// <returns></returns>
        public void SetRelationRecords(BaseProductSkuBindModel relationModel, List<BaseOfPtSkuRelation> relations, int fxUserId, int relationType)
        {
            if (relationModel == null || relations.IsNullOrEmptyList())
                return;

            // 没有Id的关联，到数据库查
            var noIdRelations = relations.Where(r => r.Id <= 0).ToList();
            List<BaseOfPtSkuRelation> getIdRelations = null;
            if (!noIdRelations.IsNullOrEmptyList())
            {
                var pUids = noIdRelations.Select(r => r.BaseProductSkuUid).ToList();
                var sCodes = noIdRelations.Select(r => r.ProductSkuCode).ToList();
                getIdRelations = GetListBySkuModel(pUids, sCodes, fxUserId, new List<string>() { "Id", "BaseProductSkuUid", "ProductSkuCode" });
                //pUids = new List<long>() { 2000000500000001986, 2000004500000002273 };
                //sCodes = new List<string>() { "eba3f5c3295de549", "dedbfd71dd57219c", "260a7a7f88e1489a" };
                //getIdRelations = new BaseOfPtSkuRelationRepository(true).GetListBySkuModel(pUids, sCodes, fxUserId, new List<string>() { "Id", "BaseProductSkuUid" , "ProductSkuCode" });
            }

            var records = new List<BaseProductRelationRecord>();
            string recordSuppliers = null;
            if (relationModel.SupplierFxUserId == -1)
            {
                recordSuppliers = "-1";
            }
            else if (!relationModel.BaseProductSkuSupplierConfigs.IsNullOrEmptyList())
            {
                var supplierIds = relationModel.BaseProductSkuSupplierConfigs.Select(c => c.SupplierFxUserId.ToInt()).Where(i => i > 0).Distinct();
                recordSuppliers = string.Join(",", supplierIds);
            }
            relations.ForEach(relation =>
            {
                // 有关联Id就用关联Id，没有的用从数据库里查到的Id
                long id = relation.Id > 0 ? relation.Id
                         : getIdRelations?.FirstOrDefault(r => r.BaseProductSkuUid == relation.BaseProductSkuUid && r.ProductSkuCode == relation.ProductSkuCode)
                         ?.Id ?? 0;

                if (id > 0)
                {
                    relationModel.DistributePriceChangeType = relation.DistributePriceChangeType;
                    string changeDetailInfo = ChangeDetailInfo(relationModel);
                    records.Add(new BaseProductRelationRecord()
                    {
                        RelationDbId = id,
                        BaseProductSkuUid = relation.BaseProductSkuUid,
                        BaseProductUid = relation.BaseProductUid,
                        ProductSkuCode = relation.ProductSkuCode,
                        ProductSkuPtId = relation.ProductSkuPtId,
                        RelationType = relationType,
                        SettlePrice = relationModel.IsUseSettlePrice == true ? relationModel.SettlePrice : null,
                        DistributePrice = relationModel.IsUseDistributePrice == true ? relationModel.DistributePrice : null,
                        CostPrice = relationModel.IsUseCostPrice == true ? relationModel.CostPrice : null,
                        SupplierFxUserId = recordSuppliers,
                        FxUserId = fxUserId,
                        ChangeDetailInfo = changeDetailInfo,
                        CreateTime = relation.CreateTime,
                        DistributePriceChangeType = relation.DistributePriceChangeType
                    });

                }
            });

            new BaseProductRelationRecordRepository().BatchAddRecord(records);
        }
        /// <summary>
        /// 手动/自动关联详细内容
        /// </summary>
        /// <param name="relationModel"></param>
        /// <returns></returns>
        private string ChangeDetailInfo(BaseProductSkuBindModel relationModel)
        {
            var sb = new System.Text.StringBuilder();
            if (relationModel.IsUseSkuShortTitle == true)
            {
                //sb.Append($"简称：{relationModel.ShortTitle}，");
                sb.Append($"简称，");
            }
            if (relationModel.IsUseSupplierFxUser == true)
            {
                if (relationModel.SupplierFxUserId == 0)
                {
                    sb.Append($"供货方式：未设置，");
                }
                else if (relationModel.SupplierFxUserId > 0)
                {
                    var supplierUser = new SupplierUserService().GetSupplierList(SiteContext.GetCurrentFxUserId()).FirstOrDefault(x => x.SupplierFxUserId == relationModel.SupplierFxUserId);
                    sb.Append($"供货方式：{supplierUser.Mobile}({supplierUser.NickName})，");
                }
                else
                {
                    sb.Append($"供货方式：自营，");
                }

            }
            if (relationModel.IsUseDistributePrice == true && relationModel.DistributePrice.HasValue && relationModel.DistributePrice.Value > 0)
            {
                sb.Append($"分销价：{relationModel.DistributePrice}元({relationModel.DistributePriceType})，");
            }
            if (relationModel.IsUseCostPrice == true && relationModel.CostPrice.HasValue && relationModel.CostPrice.Value > 0)
            {
                sb.Append($"成本价：{relationModel.CostPrice}元，");
            }
            if (relationModel.IsUseSettlePrice == true && relationModel.SettlePrice.HasValue && relationModel.SettlePrice.Value > 0)
            {
                sb.Append($"采购价：{relationModel.SettlePrice}元，");
            }
            return sb.ToString().Trim('，');
        }
        /// <summary>
        /// 解绑的详细内容
        /// </summary>
        /// <param name="relationModel"></param>
        /// <returns></returns>
        private string UnbindChangeDetailInfo(BaseProductSkuUnbindModel relationModel)
        {
            var sb = new System.Text.StringBuilder();
            if (relationModel.IsRestoreShortTitle == true)
            {
                sb.Append($"简称，");
            }
            if (relationModel.IsRestoreSupplier && !relationModel.PtSkuSupplierModels.IsNullOrEmptyList())
            {
                var supplierSku = relationModel.PtSkuSupplierModels.FirstOrDefault();
                if (supplierSku?.SettlePrice > 0)
                {
                    sb.Append($"采购价：{supplierSku?.SettlePrice}元,");
                }
                //if (relationModel.PtSkuSupplierModels.Count() == 1) // 单厂家
                //{
                //    sb.Append($"采购价：{relationModel.PtSkuSupplierModels.First().SettlePrice}元,");
                //}
            }
            if (relationModel.IsRestoreDistributePrice == true && relationModel.DistributePrice.HasValue && relationModel.DistributePrice.Value > 0)
            {
                sb.Append($"分销价：{relationModel.DistributePrice}元，");
            }
            if (relationModel.IsRestoreCostPrice == true && relationModel.CostPrice.HasValue && relationModel.CostPrice.Value > 0)
            {
                sb.Append($"成本价：{relationModel.CostPrice}元，");
            }
            return sb.ToString().Trim('，');
        }

        /// <summary>
        /// 设置解除关联记录
        /// </summary>
        /// <param name="unbindModel"></param>
        /// <param name="fxUserId"></param>
        [Obsolete("请使用批量处理方法")]
        public void SetUnBindRelationRecords(BaseProductSkuUnbindModel unbindModel, int fxUserId)
        {
            if (unbindModel == null)
                return;
            if (unbindModel.ProductSkuPtId.IsNullOrEmpty())
            {
                Log.WriteError($"用户{fxUserId}设置解绑关联记录失败：请传入店铺商品SkuId");
                return;
            }

            var recordRepository = new BaseProductRelationRecordRepository();
            var now = DateTime.Now;
            var record = new BaseProductRelationRecord()
            {
                RelationDbId = unbindModel.RelationDbId,
                RelationType = RelationTypeEnum.UnBindRelation.ToInt(),
                BaseProductSkuUid = unbindModel.BaseProductSkuUid,
                ProductSkuPtId = unbindModel.ProductSkuPtId,
                ProductSkuCode = unbindModel.SkuCode,
                BaseProductUid = unbindModel.BaseProductUid,
                FxUserId = fxUserId,
                CreateTime = now,
                ChangeDetailInfo = UnbindChangeDetailInfo(unbindModel)
            };

            if (unbindModel.IsRestoreCostPrice)
                record.CostPrice = unbindModel.CostPrice;
            if (unbindModel.IsRestoreDistributePrice)
                record.DistributePrice = unbindModel.DistributePrice;
            if (unbindModel.IsRestoreSupplier && !unbindModel.PtSkuSupplierModels.IsNullOrEmptyList())
            {
                if (unbindModel.PtSkuSupplierModels.Count() == 1) // 单厂家
                {
                    record.SupplierFxUserId = unbindModel.PtSkuSupplierModels.First().SupplierFxUserId.ToString();
                    record.SettlePrice = unbindModel.PtSkuSupplierModels.First().SettlePrice;
                }
                else // 多厂家
                {
                    record.SupplierFxUserId = "-2";
                    var recordId = recordRepository.AddRecord(record);
                    var recordExts = new List<BaseProductRelationRecordExt>();
                    unbindModel.PtSkuSupplierModels.ForEach(m =>
                    {
                        recordExts.Add(new BaseProductRelationRecordExt()
                        {
                            RecordDbId = recordId,
                            SettlePrice = m.SettlePrice,
                            SupplierFxUserId = m.SupplierFxUserId,
                            CreateTime = now
                        });
                    });
                    recordRepository.BatchAddRecordExt(recordExts);
                }
            }

            recordRepository.AddRecord(record);
        }

        /// <summary>
        /// 设置解除关联记录
        /// </summary>
        /// <param name="unbindModels"></param>
        /// <param name="fxUserId"></param>
        public void SetUnBindRelationRecords(List<BaseProductSkuUnbindModel> unbindModels, int fxUserId)
        {
            if (unbindModels.IsNullOrEmptyList())
                return;

            var now = DateTime.Now;
            var records = new List<BaseProductRelationRecord>(unbindModels.Count);
            unbindModels.ForEach(unbindModel =>
            {
                var record = new BaseProductRelationRecord()
                {
                    RelationDbId = unbindModel.RelationDbId,
                    RelationType = RelationTypeEnum.UnBindRelation.ToInt(),
                    BaseProductSkuUid = unbindModel.BaseProductSkuUid,
                    ProductSkuPtId = unbindModel.ProductSkuPtId,
                    ProductSkuCode = unbindModel.ProductSkuCode,
                    BaseProductUid = unbindModel.BaseProductUid,
                    FxUserId = fxUserId,
                    CreateTime = now,
                    ChangeDetailInfo = UnbindChangeDetailInfo(unbindModel)
                };
                if (unbindModel.IsRestoreCostPrice)
                    record.CostPrice = unbindModel.CostPrice;
                if (unbindModel.IsRestoreDistributePrice)
                    record.DistributePrice = unbindModel.DistributePrice;

                if (unbindModel.IsRestoreSupplier && unbindModel.PtSkuSupplierModels.IsNotNullAndAny())
                {
                    if (unbindModel.PtSkuSupplierModels.Count() == 1) // 单厂家
                    {
                        record.SupplierFxUserId = unbindModel.PtSkuSupplierModels.First().SupplierFxUserId.ToString();
                        record.SettlePrice = unbindModel.PtSkuSupplierModels.First().SettlePrice;
                    }
                    else // 多厂家
                    {
                        record.SupplierFxUserId = "-2";
                        record.RecordExtList = new List<BaseProductRelationRecordExt>();
                        unbindModel.PtSkuSupplierModels?.ForEach(m =>
                        {
                            record.RecordExtList.Add(new BaseProductRelationRecordExt()
                            {
                                SettlePrice = m.SettlePrice,
                                SupplierFxUserId = m.SupplierFxUserId,
                                CreateTime = now
                            });
                        });
                    }
                }

                records.Add(record);
            });

            new BaseProductRelationRecordRepository().BatchAddRecordWithRecordExt(records);
        }

        /// <summary>
        /// 关联同款
        /// </summary>
        /// <param name="model"></param>
        /// <param name="request"></param>
        /// <param name="fxUserId"></param>
        /// <param name="productSkuFx"></param>
        /// <returns></returns>
        public Tuple<bool, string> BaseProductSkuRelationBindNew(BaseProductSkuBindModel model, RequestHttpModel request,
            int fxUserId, List<ProductSkuFx> productSkuFx = null)
        {
            #region 关联基础
            var connectionString = SiteContext.Current.CurrentDbConfig.ConnectionString;
            var warehouseService = new WareHouseService(connectionString);
            var ptProductSkuService = new ProductSkuFxService();
            var messageService = new MessageRecordService();
            // 如果是自动关联，则不需要查询平台商品信息
            // 自动关联，用的是基础商品分库的Service
            var isAutoRelation = productSkuFx.IsNotNullOrEmpty();
            var dbName = request.DbName;
            var cloudPlatform = CustomerConfig.CloudPlatformType;
            if (dbName.IsNotNullOrEmpty())
            {
                try
                {
                    dbName = DES.DecryptDES(dbName, CustomerConfig.LoginCookieEncryptKey);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"平台dbName解析失败：{ex.Message}");
                    return new Tuple<bool, string>(false, "平台dbName解析失败！");
                }
            }
            else
            {
                return new Tuple<bool, string>(false, "平台dbName不能为空！");
            }
            model.IsAllUseWarehouse = true;//强制为true
            model.SkuList?.ForEach(a => { a.IsDisabledUseWarehouse = false; });//强制为false
            if (model.IsAllUseWarehouse == true)
            {
                // 需判断SkuCode是否存在库存系统
                var reqOne = new WarehouseSkuGetOneRequest
                {
                    WareHouseSkuCode = "-",
                    SkuCodeList = new List<string> { model.SkuCode }
                };

                var resOne = warehouseService.WarehouseSkuGetOne(reqOne);
                if (resOne.IsSucc)
                {
                    if (resOne.ExistSkuCode.Any() && resOne.ExistSkuCode.First() == model.SkuCode)
                    {
                        model.SkuList.ForEach(a => a.IsUseWarehouse = true);
                    }
                }
            }
            List<ProductSkuFx> ptProductSkus;

            // 平台商品
            if (isAutoRelation == false)
            {
                var ptProductSkuCodes = model.SkuList.Select(a => a.ProductSkuCode).Distinct().ToList();
                ptProductSkus = ptProductSkuService.GetProductSkuBySkuCode(ptProductSkuCodes);
            }
            else ptProductSkus = productSkuFx;  // 自动关联

            // 过滤虚拟商品
            //ptProductSkus = ptProductSkus?.Where(x => x.PlatformType.ToLower() != "virtual").ToList();

            if (ptProductSkus == null || ptProductSkus.Count == 0)
                return new Tuple<bool, string>(false, "未找到平台商品信息！");

            // 基础商品绑定信息
            var BaseOfPtSkuRelations = new List<BaseOfPtSkuRelation>();
            var subModels = new List<MessageRecord>();
            ptProductSkus.ForEach(sku =>
            {
                var relation = new BaseOfPtSkuRelation();
                var current = model.SkuList.FirstOrDefault(a => a.ProductSkuCode == sku.SkuCode);
                relation.ProductSkuPtId = sku.SkuId;
                relation.ProductPtId = sku.PlatformId;
                relation.ProductSkuCode = sku.SkuCode;
                relation.ProductCode = sku.ProductCode;
                relation.ProductShopId = sku.ShopId;
                relation.ProductFxUserId = sku.SourceUserId.Value;
                relation.ProductPlatformType = sku.PlatformType;
                relation.CloudPlatform = cloudPlatform;

                relation.FxUserId = fxUserId;
                relation.CreateTime = DateTime.Now;
                relation.UpdateTime = DateTime.Now;
                relation.Status = 1;
                relation.IsUseWarehouse = current == null ? false : current.IsUseWarehouse;
                relation.BaseProductSkuUid = model.BaseProductSkuUid;
                relation.BaseProductUid = model.BaseProductUid;
                if (isAutoRelation)
                {
                    relation.DbName = sku.DbName;
                    relation.RelationType = 1;
                    //relation.CloudPlatform = sku.CloudPlatform;
                    //relation.CloudPlatform = CustomerConfig.CloudPlatformType;
                }
                BaseOfPtSkuRelations.Add(relation);
            });

            // 同步到平台商品
            // Task.Run(() => 
            // { 
            // 获取所有DbName，分组
            var dbNameList = BaseOfPtSkuRelations.Select(a => a.DbName).Distinct().ToList();
            var changeCodes = new List<string>();
            dbNameList.ForEach(relationDbName =>
            {
                // 组装模型
                var syncModel = new BaseSkuSyncToPtSkuModel();
                var codeDic = new Dictionary<string, List<string>>();
                BaseOfPtSkuRelations.ForEach(rel =>
                {
                    if (!codeDic.ContainsKey(rel.ProductCode))
                        codeDic.Add(rel.ProductCode, new List<string>());
                    codeDic[rel.ProductCode].Add(rel.ProductSkuCode);
                });
                syncModel.IsUseSupplierFxUser = model.IsUseSupplierFxUser;
                syncModel.SkuCodes = codeDic;
                syncModel.BaseProductSkuSupplierConfig = model.BaseProductSkuSupplierConfigs;
                syncModel.SettlePrice = model.SettlePrice;
                syncModel.CostPrice = model.CostPrice;
                syncModel.DistributePrice = model.DistributePrice;
                syncModel.ShortTitle = model.ShortTitle;
                syncModel.IsUseCostPrice = model.IsUseCostPrice;
                syncModel.IsUseDistributePrice = model.IsUseDistributePrice;
                syncModel.IsUseSettlePrice = model.IsUseSettlePrice;
                syncModel.BaseProductUid = model.BaseProductUid;
                syncModel.BaseProductSkuUid = model.BaseProductSkuUid;
                syncModel.DbName = relationDbName;
                syncModel.FxUserId = fxUserId;

                // 自动关联才检查
                new BaseProductSkuCommonService().SyncToPtSku(syncModel, ref changeCodes, isAutoRelation, model.IsNeedUseMemberLevel);
            });
            // });
           
            // 基础商品绑定
            var uids = BaseOfPtSkuRelations.Select(a => a.BaseProductSkuUid).Distinct().ToList();
            var codes = BaseOfPtSkuRelations.Select(a => a.ProductSkuCode).Distinct().ToList();

            // 冷库处理前置，先处理冷库数据，再处理热库数据
            // 冷库如果不需要写，不提示用户，接着处理热库数据，可以修复之前跨环境导致的数据
            BaseOfPtSkuRelationService coldService = null;
            var userDbConfig = SiteContext.Current.CurrentDbConfig;
            var enableColdGlobal = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
            if (enableColdGlobal&&userDbConfig!=null&&userDbConfig.EnableColdDb)
            {
                coldService = new BaseOfPtSkuRelationService(userDbConfig.ColdDbConnectionString, false);
                var coldRelations = coldService.GetAllListBySkuCode(codes, fxUserId);
                // 未关联：所有未关联
                var coldRelationsExistsN = BaseOfPtSkuRelations
                    .Where(a => !coldRelations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode)).ToList();
                // 未关联：当前未关联（已解绑）
                var coldRelationsExistsCB = BaseOfPtSkuRelations
                    .Where(a => coldRelations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.BaseProductSkuUid == a.BaseProductSkuUid && b.Status == 0)).ToList();
                // 未关联：其他未关联（已解绑）
                var coldRelationsExistsOB = BaseOfPtSkuRelations
                    .Where(a => coldRelations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.BaseProductSkuUid != a.BaseProductSkuUid && b.Status == 0)).ToList();

                coldRelationsExistsCB.ForEach(a =>
                {
                    a.UpdateTime = DateTime.Now;
                    a.Status = 1;
                });

                if (coldRelationsExistsOB.Count > 0)
                {
                    coldService.BatchAdd(coldRelationsExistsOB);
                }
                if (coldRelationsExistsN.Count > 0)
                {
                    coldService.BatchAdd(coldRelationsExistsN);
                }

                if (coldRelationsExistsCB.Count > 0)
                {
                    coldService.BulkUpdate(coldRelationsExistsCB);
                }
            }
            
            var relations = GetAllListBySkuCode(codes, fxUserId);

            // 已关联：所有已关联
            var relationsExistsY = BaseOfPtSkuRelations
                .Where(a => relations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.Status == 1)).ToList();
            // 未关联：所有未关联
            var relationsExistsN = BaseOfPtSkuRelations
                .Where(a => !relations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode)).ToList();
            // 未关联：当前未关联（已解绑）
            var relationsExistsCB = BaseOfPtSkuRelations
                .Where(a => relations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.BaseProductSkuUid == a.BaseProductSkuUid && b.Status == 0)).ToList();
            // 未关联：其他未关联（已解绑）
            var relationsExistsOB = BaseOfPtSkuRelations
                .Where(a => relations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.BaseProductSkuUid != a.BaseProductSkuUid && b.Status == 0)).ToList();

            // 去除重复(已解绑)
            relationsExistsOB =
                relationsExistsOB.Where(a => !relationsExistsCB.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.BaseProductSkuUid == a.BaseProductSkuUid)).ToList();


            if (relationsExistsY.Count > 0)
            {
                return new Tuple<bool, string>(false, "当前选择的平台商品，存在已关联其他基础商品！");
            }

            relationsExistsCB.ForEach(a =>
            {
                a.UpdateTime = DateTime.Now;
                a.Status = 1;
            });
           
            if (relationsExistsOB.Count > 0)
            {
                // 未存在绑定
                BatchAdd(relationsExistsOB);
            }
            if (relationsExistsN.Count > 0)
            {
                // 未存在绑定
                BatchAdd(relationsExistsN);
            }

            if (relationsExistsCB.Count > 0)
            {
                // 已存在绑定
                BulkUpdate(relationsExistsCB);
            }

            // 平台商品绑定（消息队列）
            var allSyncAelations = relationsExistsCB.Concat(relationsExistsN).Concat(relationsExistsOB).ToList();

            if (allSyncAelations.Count <= 0)
            {
                return new Tuple<bool, string>(false, "未存在关联商品同步数据！");
            }
            if (allSyncAelations.Count > 0)
            {
                allSyncAelations.ForEach(relation =>
                {
                    var subModel = new MessageRecord();
                    subModel.MsgType = BaseProductMsgType.BaseOfPtSkuRelationAdd;
                    subModel.FxUserId = fxUserId;
                    subModel.DbName = dbName;
                    subModel.ProductPlatformType = CloudPlatformType.Alibaba.ToString();
                    subModel.BusinessId = relation.ProductSkuCode;
                    subModel.DataJson = relation.ToJson();
                    subModels.Add(subModel);
                });

                // 关联日志
                try
                {
                    if (changeCodes.Any())
                    {
                        // 找到对应的关联记录
                        var changeRelations = allSyncAelations.Where(a => changeCodes.Contains(a.ProductSkuCode)).ToList();
                        changeRelations.ForEach(x => x.DistributePriceChangeType = 2);
                    }

                    SetRelationRecords(model, allSyncAelations, fxUserId,
                        isAutoRelation ? RelationTypeEnum.AutoRelation.ToInt() : RelationTypeEnum.Relation.ToInt());
                }
                catch (Exception ex)
                {
                    var isAutoRelationStr = isAutoRelation ? "自动" : "手动";
                    Log.WriteError($"用户{fxUserId}{isAutoRelationStr}关联记录日志发生异常，关联模型：{model.ToJson()}，异常信息：{ex.StackTrace}");
                }
            }
            if (CustomerConfig.IsLocalDbDebug)
            {
                messageService.SyncBaseOfPtSkuRelationToBusinessDb(subModels);
            }
            else
            {
                var res = messageService.SendBusinessMessage(subModels);
            }

            #endregion


            // 同步到库存系统的数据
            var syncRelationList = new List<WarehouseSkuBindRequest>();
            var syncSourseList = model.SkuList
                .Where(a => a.IsUseWarehouse && a.IsDisabledUseWarehouse != true)
                .Select(a => a.ProductSkuCode)
                .ToList();
            syncRelationList = ptProductSkus
                .Where(a => syncSourseList.Contains(a.SkuCode))
                .Select(a => new WarehouseSkuBindRequest
                {
                    IsRollback = false,
                    PlatformSkuCode = a.SkuCode,
                    PlatformType = a.PlatformType,
                    ShopId = a.ShopId,
                    SkuCode = model.SkuCode,
                    DbName = a.DbName
                }).ToList();
            if (CustomerConfig.IsDebug) Log.WriteLine($"基础商品到库存系统绑定商品：{syncRelationList.ToJson()}");

            // 库存系统关联商品（异步处理）
            if (syncRelationList.Any())
            {
                // 异步处理
                Task.Run(() =>
                {
                    syncRelationList.ForEach(req =>
                    {
                        var rep = warehouseService.BindProduct(req, true, true);
                        if (CustomerConfig.IsDebug) Log.WriteLine($"基础商品到库存系统绑定商品：{req.ToJson()}，结果：{rep.ToJson()}");
                    });
                });
            }

            return new Tuple<bool, string>(true, "关联成功！");
        }

        public List<BaseOfPtSkuRelation> GetListForDuplication(List<string> skuCodes,int fxUserId)
        {
            return _repository.GetListForDuplication(skuCodes, "*",fxUserId);
        }



        /// <summary>
        /// 更新或软删除关联关系
        /// </summary>
        /// <param name="models"></param>
        /// <param name="isDeleteWarehouse">是否需要解绑库存系统关联</param>
        /// <param name="updateFields">更新字段</param>
        public void UpdateOrDelete(List<BaseOfPtSkuRelation> models, bool isDeleteWarehouse = false, List<string> updateFields = null)
        {
            if (models.IsNullOrEmptyList()) return;

            // 更新
            var updateModels = models.Where(a => a.Type == 2).ToList();
            // 删除
            var deleteModels = models.Where(a => a.Type == 0).ToList();
            deleteModels.ForEach(a =>
            {
                a.Status = 0;
                a.UpdateTime = DateTime.Now;
                a.IsUseWarehouse = false;
            });

            if (updateFields.IsNullOrEmptyList()) updateFields = new List<string>
            {
                "BaseProductSubject", "BaseProductShortTitle", "BaseProductSkuSubject", "BaseProductSkuShortTitle",
                "BaseProductSpuCode", "BaseProductSkuCode", "UpdateTime"
            };

            if (updateModels.Any()) _repository.BulkUpdate(updateModels, updateFields);
            if (deleteModels.Any()) _repository.BulkUpdate(deleteModels, new List<string> { "Status", "UpdateTime", "IsUseWarehouse" });

            // 被动删除时需要解绑库存
            if (isDeleteWarehouse && deleteModels.Any())
            {
                // 库存解绑
                var wareHouseService = new WareHouseService();
                var ownCode = wareHouseService.GetOwnerCode();
                var delPtSkuCodes = deleteModels.Select(a => a.ProductSkuCode).ToList();
                // 获取库存系统数据的业务库绑定关系
                var warehouseRelations =
                    new WareHouseSkuBindRelationRepository().GetWareHouseSkuCodes(ownCode, delPtSkuCodes);

                deleteModels.ForEach(del =>
                {
                    var wareHouseSkuBindRelation = warehouseRelations.FirstOrDefault(a => a.PlatformSkuCode == del.ProductSkuCode);
                    if (wareHouseSkuBindRelation == null) return;
                    var req = new WarehouseSkuUnbindRequest
                    {
                        IsRollback = false,
                        OwnerCode = ownCode,
                        SkuBindRelationCode = wareHouseSkuBindRelation.SkuBindRelationCode
                    };

                    var rsp = wareHouseService.UnBindProduct(req, true);
                    if (CustomerConfig.IsDebug) Log.WriteLine($"基础商品到库存系统解绑商品：{req.ToJson()}，结果：{rsp.ToJson()}");
                });
            }
        }

        #region 关联日志操作

        /// <summary>
        /// 获取关联日志记录
        /// </summary>
        /// <param name="recordSearchModel"></param>
        /// <returns></returns>
        public Tuple<int, List<BpRelRecordModel>> GetBpRelRecords(BpRelRecordSearchModel recordSearchModel)
        {
            try
            {
                var fxUserId = SiteContext.GetCurrentFxUserId();
                List<BaseProductSkuSimpleRes> baseProductSkuSimpleRes = null;
                if (recordSearchModel.SpuCode.IsNotNullOrEmpty() || recordSearchModel.SkuCode.IsNotNullOrEmpty())
                {
                    // 获取基础商品sku信息
                    var req = new BpSkuSimpleConditionReq()
                    {
                        FxUserId = fxUserId,
                        SkuCode = recordSearchModel.SkuCode,
                        SpuCode = recordSearchModel.SpuCode,
                        IsContainDelete = true
                    };
                    if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                    {
                        baseProductSkuSimpleRes = new BaseProductSkuCommonService().GetBaseProductSkuSimpleByCondition(req);
                    }
                    else
                    {
                        // 跨云获取数据
                        var isPdd = CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString();
                        var targetSiteUrl = isPdd ? CustomerConfig.AlibabaMessageDomainForPdd : CustomerConfig.AlibabaFenFaSystemUrl;
                        const string apiUrl = "/BaseProductApi/GetBaseProductSkuSimpleByCondition";
                        targetSiteUrl = targetSiteUrl.TrimEnd("/") + apiUrl;

                        // 获取基础商品sku信息
                        Log.Debug($"跨云查询sku信息，地址={targetSiteUrl}，入参={req.ToJson()}");
                        baseProductSkuSimpleRes = WebCommon.PostFxSiteApi<BpSkuSimpleConditionReq, List<BaseProductSkuSimpleRes>>(targetSiteUrl, fxUserId, req, "跨云查询基础商品SKU", isEncrypt: true);
                    }
                    if (baseProductSkuSimpleRes?.Count > 0)
                    {
                        var bpSkuUids = baseProductSkuSimpleRes.Select(x => x.ProductSkuId).ToList();
                        recordSearchModel.SkuUids.AddRange(bpSkuUids);
                    }
                }

                var result = new BaseProductRelationRecordRepository().GetRecordsByCondition(recordSearchModel,fxUserId);

                var fxUserIds = result.Item2?.Select(x => x.FxUserId).ToList();
                var fxUsers = new UserFxService().GetsByIds(fxUserIds, "id,Mobile");

                //var spuUids = result.Item2?.Select(x => x.BaseProductUid).Distinct().ToList();
                var skuUids = result.Item2?.Select(x => x.BaseProductSkuUid).Distinct().ToList();
                if (baseProductSkuSimpleRes.IsNullOrNotAny())
                {
                    // 获取基础商品sku信息
                    var req = new BaseProductSkuSimpleReq(skuUids, fxUserId);
                    req.IsContainDelete = true;
                    if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                    {
                        baseProductSkuSimpleRes = new BaseProductSkuCommonService().GetBaseProductSkuDetail(req);
                    }
                    else
                    {
                        // 跨云获取数据
                        var isPdd = CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString();
                        var targetSiteUrl = isPdd ? CustomerConfig.AlibabaMessageDomainForPdd : CustomerConfig.AlibabaFenFaSystemUrl;
                        const string apiUrl = "/BaseProductApi/GetBaseProductSkuSimple";
                        targetSiteUrl = targetSiteUrl.TrimEnd("/") + apiUrl;

                        // 获取基础商品sku信息
                        Log.Debug($"跨云查询sku信息，地址={targetSiteUrl}，入参={req.ToJson()}");
                        baseProductSkuSimpleRes = WebCommon.PostFxSiteApi<BaseProductSkuSimpleReq, List<BaseProductSkuSimpleRes>>(targetSiteUrl, fxUserId, req, "跨云查询基础商品SKU", isEncrypt: true);
                    }
                }
                var models = new List<BpRelRecordModel>();
                result.Item2?.ForEach(x => {
                    var m = new BpRelRecordModel()
                    {
                        Id = x.Id,
                        Mobile = fxUsers?.FirstOrDefault(u => u.Id == x.FxUserId)?.Mobile,
                        ChangeDetailInfo = x.ChangeDetailInfo,
                        CreateTime = x.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        RelationType = x.RelationType,
                        RelationTypeText = x.RelationTypeText,
                        SkuCode = baseProductSkuSimpleRes?.FirstOrDefault(s => s.ProductSkuId == x.BaseProductSkuUid)?.ProductSkuCode,
                        SkuIdStr = x.ProductSkuPtId,
                        SpuCode = baseProductSkuSimpleRes?.FirstOrDefault(b => b.SpuUid == x.BaseProductUid)?.SpuCode,
                    };
                    models.Add(m);
                });

                return Tuple.Create(result.Item1, models);
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取关联日志异常：{ex.Message}");
            }
            return Tuple.Create(0, new List<BpRelRecordModel>());
        }
        #endregion


        /// <summary>
        /// 处理关联脏数据  库存数据库与基础商品库
        /// </summary>
        public bool HandleDirtyRelation(List<BaseOfPtSkuRelation> relations)
        {
            if (relations.IsNullOrEmptyList()) return true;
            var data = new CommonSettingService().GetString(DIRTY_RELATION_HANDLE_POLICY, 0,false).ToInt();
            var policy = (DirtyRelationHandlePolicy)data;

            switch (policy) 
            {
                case DirtyRelationHandlePolicy.Ignore:
                    return false;
                case DirtyRelationHandlePolicy.DeleteRelationOfBaseProductDb:
                    _repository.BulkDelete(relations);
                    var uids = relations.Select(r=>r.BaseProductSkuUid).ToList();
                    return !_repository.GetListBySkuUids(uids, SiteContext.GetCurrentFxUserId()).Any();
                default:
                    return false;
            }
        }

        /// <summary>
        /// 业务库关联数据补偿
        /// 如果是非精选云调用此方法，需要在构造中指定isBaseProductDb = false
        /// </summary>
        /// <param name="baseSkuUid"></param>
        /// <param name="baseProductDbRelationList"></param>
        /// <param name="isAsync"></param>
        /// <param name="filterSkuCodeList"></param>
        public Tuple<string,List<string>,bool> BusinessDbRelationDataCompensate(long baseSkuUid, List<BaseOfPtSkuRelation> baseProductDbRelationList, bool isAsync = false,List<string> filterSkuCodeList = null)
        {
            if (baseProductDbRelationList.IsNullOrEmptyList())
                return new Tuple<string, List<string>, bool>("关联数据为空", null, false);

            
            var fxUserId = SiteContext.GetCurrentFxUserId();
            if (!isAsync)
                return DoCompensate();

            Task.Run(DoCompensate);
            return new Tuple<string, List<string>,bool>("后台执行中",null,false) ;

            Tuple<string, List<string>,bool> DoCompensate()
            {
                var isDataChange = false;
                // 当前云所有业务库基础商品规格关联的店铺商品的SkuCode
                var allBusinessDbSkuCodeList = new List<string>();
                // 业务库是否有关联数据修改
                try
                {
                    var baseProductDbSkuCodeList = baseProductDbRelationList?.Select(a => a.ProductSkuCode).Distinct().ToList();
                    
                    // 如果当前云平台是精选或拼多多，要考虑分区的情况，每个分区都要遍历
                    SiteContext.Current?.CurrentDbAreaConfig?.ForEach(dbConfig =>
                    {
                        // 筛选出当前业务库关联的规格
                        var businessDbSkuCodeList = new ProductSkuFxRepository(dbConfig.ConnectionString)
                            .GetExistIdAndCodes(baseProductDbSkuCodeList).Select(m => m.Code).ToList();

                        allBusinessDbSkuCodeList.AddRange(businessDbSkuCodeList);

                        // 筛选出当前业务库的规格在基础商品库的关联数据
                        var currentBaseProductDbRelationList = baseProductDbRelationList?
                            .Where(r => businessDbSkuCodeList.Contains(r.ProductSkuCode)).ToList();
                        if (businessDbSkuCodeList.IsNullOrEmptyList() ||
                            currentBaseProductDbRelationList.IsNullOrEmptyList())
                        {
                            Log.WriteWarning(
                                $"用户{fxUserId}补偿业务库基础商品关联数据，当前云：{CustomerConfig.CloudPlatformType}，当前业务库：{dbConfig.Identity}。查询业务库规格为空！或通过规格筛选的基础商品关联为空！",
                                ModuleFileName.BaseProduct);
                            return;
                            //return new Tuple<string, List<string>, bool>("查询业务库规格为空或通过规格筛选的基础商品关联为空！", null, false);
                        }

                        // 获取业务库仓储
                        var businessRepository = new BaseOfPtSkuRelationRepository(dbConfig.ConnectionString, false);

                        // 业务库的关联数据
                        var businessDbRelationList = businessRepository.GetListBySkuModel(new List<long> { baseSkuUid },
                            businessDbSkuCodeList, fxUserId, new List<string>
                            {
                                "Id", "ProductSkuCode", "Status"
                            });

                        // 筛选出需要写入业务库的关联数据
                        var businessDbRelationDict = businessDbRelationList.GroupBy(r => r.ProductSkuCode)
                            .ToDictionary(r => r.Key, r => r.FirstOrDefault());

                        // 需要从删除状态恢复正常的
                        var restoreRelationIdList = new List<long>();
                        // 需要删除的
                        var deleteRelationIdList = new List<long>();
                        // 需要写入的
                        var insertRelationList = new List<BaseOfPtSkuRelation>();

                        currentBaseProductDbRelationList?.ForEach(baseProductDbRelation =>
                        {
                            if (businessDbRelationDict.TryGetValue(baseProductDbRelation.ProductSkuCode,
                                    out var businessDbRelation))
                            {
                                if (businessDbRelation.Status != baseProductDbRelation.Status)
                                {
                                    if (baseProductDbRelation.Status == 1)
                                    {
                                        restoreRelationIdList.Add(businessDbRelation.Id);
                                    }
                                    else if (baseProductDbRelation.Status == 0)
                                    {
                                        deleteRelationIdList.Add(businessDbRelation.Id);
                                    }
                                }
                            }
                            else
                            {
                                baseProductDbRelation.Id = 0;
                                //baseProductDbRelation.UpdateTime = DateTime.Now;
                                insertRelationList.Add(baseProductDbRelation);
                            }

                        });

                        // 恢复
                        if (restoreRelationIdList.IsNotNullAndAny())
                            businessRepository.BatchUpdateStatusByIds(restoreRelationIdList, 1);
                        // 删除
                        if (deleteRelationIdList.IsNotNullAndAny())
                            businessRepository.BatchUpdateStatusByIds(deleteRelationIdList, 0);
                        // 写入
                        if (insertRelationList.IsNotNullAndAny())
                            businessRepository.BulkInsert(insertRelationList);

                        // 遍历到的业务库是站点上下文的（当前访问）业务库
                        isDataChange = restoreRelationIdList.IsNotNullAndAny() ||
                                           deleteRelationIdList.IsNotNullAndAny() ||
                                           insertRelationList.IsNotNullAndAny();
                    });
                }
                catch (Exception ex)
                {
                    Log.WriteError($"用户{fxUserId}补偿业务库基础商品关联数据发生异常：{ex}",ModuleFileName.BaseProduct);
                }
                return new Tuple<string, List<string>,bool>("执行完成", allBusinessDbSkuCodeList, isDataChange);
            }
        }
    }

    public enum DirtyRelationHandlePolicy
    {
        /// <summary>
        /// 忽略
        /// </summary>
        Ignore = 0,

        /// <summary>
        /// 删除基础商品库的关联数据
        /// </summary>
        DeleteRelationOfBaseProductDb = 1,
    }
}
