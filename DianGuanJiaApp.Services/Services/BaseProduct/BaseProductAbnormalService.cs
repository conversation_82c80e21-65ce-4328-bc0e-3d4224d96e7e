using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Web;
using DianGuanJiaApp.Warehouse.Entity;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Warehouse.Model.Request;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Model.BaseProduct;
using System.Threading;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Utility.Other;
using System.Data;
using System.Data.Common;
using Nest;

namespace DianGuanJiaApp.Services.BaseProduct
{
    public class BaseProductAbnormalService : BaseProductBaseService<BaseProductAbnormal>
    {
        private readonly BaseProductAbnormalRepository _repository;

        /// <summary>
        /// 默认使用当前登录账号的FxUserId
        /// </summary>
        public BaseProductAbnormalService()
        {
            _repository = new BaseProductAbnormalRepository();
            _baseRepository = _repository;
        }

        /// <summary>
        /// 指定fxUserId
        /// </summary>
        /// <param name="fxUserId"></param>
        public BaseProductAbnormalService(int fxUserId)
        {
            _repository = new BaseProductAbnormalRepository(fxUserId);
            _baseRepository = _repository;            
        }

        /// <summary>
        /// 指定连接字符串
        /// </summary>
        /// <param name="connectionString"></param>
        public BaseProductAbnormalService(string connectionString, bool isMySQL = false)
        {
            _repository = new BaseProductAbnormalRepository(connectionString, isMySQL);
            _baseRepository = _repository;
        }

        /// <summary>
        /// 批量添加（存在标记为删除，不存在新增）
        /// </summary>
        /// <param name="models"></param>
        public void BatchAdd(List<BaseProductAbnormal> models)
        {
            if (models == null || models.Any() == false)
                return;

            models.ForEach(model => { model.AbnormalCode = model.GetAbnormalCode; });

            //按FxUserId分组，添加到各自的子库里
            models.GroupBy(g => g.FxUserId).ToList().ForEach(g =>
            {
                var _curRepository = new BaseProductAbnormalRepository(g.Key);
                var curModels = models.Where(a => a.FxUserId == g.Key).ToList();
                curModels.GroupBy(t => t.AbnormalType).ToList().ForEach(gr =>
                {
                    var tempModels = gr.ToList();
                    //1.查询已存在的
                    var codes = tempModels.Select(a => a.AbnormalCode).ToList();
                    var existList = _curRepository.GetExistIdAndCodes(codes, gr.Key);
                    var existIds = existList.Select(a => a.Id).ToList();

                    //2.批量新增
                    _curRepository.BatchAdd(tempModels);

                    //3.已存在的标记“已删除”
                    if (existIds != null && existIds.Any())
                        _curRepository.SetDeleted(existIds);
                });              
            });
        }

        /// <summary>
        /// 根据id获取信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public BaseProductAbnormal GetById(long id)
        {
            return _repository.GetById(id);
        }

        /// <summary>
        /// 根据id获取列表
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public List<BaseProductAbnormal> GetListByIds(List<long> ids)
        {
            if(ids.IsNullOrEmptyList())
                return new List<BaseProductAbnormal>();
            return _repository.GetListByIds(ids);
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="status">状态：0=待处理；1=已处理；2=忽略</param>
        /// <param name="fxUserId">当前用户</param>
        /// <returns></returns>
        public int UpdateStatus(List<long> ids, int status, int fxUserId)
        {
            if (ids == null || ids.Any() == false)
            {
                return 0;
            }
            return _repository.UpdateStatus(ids, status, fxUserId);
        }

        /// <summary>
        /// 暂不处理（即状态设为忽略）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int NotProcessed(BatchOptModel model)
        {
            if (model.Ids == null || model.Ids.Any() == false)
            {
                return 0;
            }
            return UpdateStatus(model.Ids, 2, model.FxUserId);
        }

        /// <summary>
        /// 获取基础商品解绑模型
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="model"></param>
        /// <param name="actionResult"></param>
        /// <returns></returns>
        public List<BaseProductSkuUnbindModel> BuildBaseProductSkuUnbindModel(int fxUserId, BaseProductSkuUnbindModel model,ref UnbindRelationResultModel actionResult)
        {
            var ids = model?.AbnormalIdList;
            if (model == null || ids.IsNullOrEmptyList())
            {
                actionResult.FailedAbnormalIdDict.Add("请选择需要解绑的关联",null);
                return null;
            }

            var dbModels = GetListByIds(ids);
            // 防止越权
            dbModels = dbModels.Where(m => m.FxUserId == fxUserId).ToList();

            if (dbModels.IsNullOrEmptyList())
            {
                actionResult.FailedAbnormalIdDict.Add("未查询到异常关联", ids);
                return null;
            }

            var baseProductSkuService = new BaseProductSkuService(fxUserId);

            var skuUidList = dbModels.Select(d => d.BaseProductSkuUid).ToList();
            var skuCodeDict = baseProductSkuService.GetListBySkuUids(skuUidList,fxUserId,"Uid,SkuCode").ToDictionary(d=>d.Uid,d=>d.SkuCode);
            if (skuCodeDict.IsNullOrEmptyList())
            {
                actionResult.FailedAbnormalIdDict.Add("未查询到基础商品规格", ids);
                return null;
            }
                

            var results = new List<BaseProductSkuUnbindModel>(skuCodeDict.Count);

            dbModels.ForEach(m =>
            {
                if (skuCodeDict.TryGetValue(m.BaseProductSkuUid, out var skuCode))
                {
                    results.Add(new BaseProductSkuUnbindModel()
                    {
                        BaseProductSkuUid = m.BaseProductSkuUid,
                        BaseProductUid = m.BaseProductUid,
                        SkuCode = skuCode,
                        ProductSkuPtId = m.ProductSkuPtId,
                        ProductCode = m.ProductCode,
                        ProductSkuCode = m.ProductSkuCode,
                        ProductDbName = m.ProductDbName,
                        ProductCloudPlatform = m.ProductCloudPlatform,
                        IsRestoreCostPrice = model.IsRestoreCostPrice,
                        IsRestoreDistributePrice = model.IsRestoreDistributePrice,
                        IsRestoreSettlePrice = model.IsRestoreSettlePrice,
                        IsRestoreShortTitle = model.IsRestoreShortTitle,
                        IsRestoreSupplier = model.IsRestoreSupplier,
                        AbnormalIdList = new List<long>{m.Id},
                        IsAllUseWarehouse = true
                    });
                }
            });

            return results;
        }

        /// <summary>
        /// 是否存在待处理的异常
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="abnormalType"></param>
        /// <returns></returns>
        public bool IsHasWaitProcessAbnormal(int fxUserId, string abnormalType = "")
        {
            return _repository.IsHasWaitProcessAbnormal(fxUserId, abnormalType);
        }

        /// <summary>
        /// 分页获取列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<BaseProductAbnormalResult>> GetPageList(BaseProductAbnormalQuery query)
        {
            var tuple = _repository.GetPageList(query);
            if (tuple != null && tuple.Item2 != null && tuple.Item2.Any())
            {
                ProcessProductFromBusinessDb(tuple.Item2);
            }
            return tuple;
        }

        /// <summary>
        /// 店铺商品赋值，基础规格图片地址转换
        /// </summary>
        /// <param name="models"></param>
        public void ProcessProductFromBusinessDb(List<BaseProductAbnormalResult> models)
        {
            if (models == null || models.Any() == false)
                return;

            int fxUserId = SiteContext.Current.CurrentFxUserId;

            var logFileName = "BaseProductAbnormal.txt";
            var fxUserIds = models.Select(a => a.FxUserId).Distinct().ToList();
            fxUserIds.AddRange(models.Select(a => a.ProductFxUserId).Distinct().ToList());
            fxUserIds = fxUserIds.Where(a => a > 0).Distinct().ToList();
            var dbConfigs = new DbConfigRepository().GetListByFxUserIds(fxUserIds, new List<string> { CloudPlatformType.Alibaba.ToString(), CloudPlatformType.TouTiao.ToString(), CloudPlatformType.Pinduoduo.ToString(), CloudPlatformType.Jingdong.ToString() });

            if (dbConfigs == null || dbConfigs.Any() == false)
                return;

            Log.Debug($"fxUserIds={fxUserIds.ToJson()}，dbConfigs={dbConfigs.Where(a => a.DbNameConfig != null).Select(a => a.DbNameConfig.DbName).ToJson()}", logFileName);

            var dicProduct = new Dictionary<string, ProductFx>();
            var dicSku = new Dictionary<string, ProductSkuFx>();
            var dicSkuFromApi = new Dictionary<string, ProductSkuFxForAbnormal>();
            var strFields = "p.Id,p.PlatformId,p.ShopId,p.SourceUserId,p.ImageUrl,p.Subject,sku.Id,sku.SkuCode,sku.SkuId,sku.ImgUrl,sku.Name,sku.CargoNumber";
            var strFieldForApi = "p.Id,p.ProductCode,p.ImageUrl,p.Subject,sku.SkuCode,sku.SkuId,sku.ImgUrl AS SkuImageUrl,sku.Name AS SkuName,sku.CargoNumber AS SkuCargoNumber";
            dbConfigs.Where(a => a.DbNameConfig != null).GroupBy(g => g.ConnectionString).ToList().ForEach(g =>
            {
                var curDbConfig = g.First();
                var dbLocation = curDbConfig.DbServer.Location;
                var curApplicationName = curDbConfig.DbNameConfig?.ApplicationName ?? "";

                var commonSettingRepository = new CommonSettingRepository();

                if (dbLocation == CustomerConfig.CloudPlatformType)
                {
                    var curProductCodes = models.Where(a => a.ProductCloudPlatform == dbLocation).Select(a => a.ProductCode).Distinct().ToList();
                    Log.Debug($"同云查，dbLocation={dbLocation}，dbName={curDbConfig.DbNameConfig?.DbName}，curProductCodes={curProductCodes.ToJson()}", logFileName);
                    //同云，直接执行
                    if (curProductCodes.Any())
                    {
                        var productFxService = new ProductFxService(curDbConfig.ConnectionString);
                        var curList = productFxService.GetProductListByCodes(curProductCodes, strFields);
                        Log.Debug($"同云查到，dbLocation={dbLocation}，dbName={curDbConfig.DbNameConfig?.DbName}，curList={curList?.Select(a => a.ProductCode).ToJson()}", logFileName);

                        #region 上下游商品信息是否可见
                        var pathflowService = new PathFlowService(curDbConfig.ConnectionString);
                        var fields = "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
                        var pathFlows = pathflowService.GetPathFlows(curProductCodes, 0, fields);

                        var logicOrderRepository = new LogicOrderRepository(curDbConfig.ConnectionString);
                        var nodes = pathFlows.SelectMany(x => x.PathFlowNodes).ToList();
                        var pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(nodes);

                        //兼容新旧数据补上Config信息
                        productFxService.CompatibleOldDataToAddConfig(pathFlows);

                        #endregion

                        curList?.ForEach(p =>
                        {
                            var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(p.ProductCode)).ToList();
                            if (dicProduct.ContainsKey(p.ProductCode) == false)
                            {
                                // 商品标题是否可见
                                foreach (var flow in productPathFlows)
                                {
                                    var isShowProductTitle = commonSettingRepository.SetIsShowProductTitle(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                                    if (isShowProductTitle == false)
                                    {
                                        p.Subject = p.Subject.GetShowProductStr(false);
                                        break;
                                    }
                                }


                                // 商品图片是否可见
                                foreach (var flow in productPathFlows)
                                {
                                    var isShowProductImg = commonSettingRepository.SetIsShowProductImg(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                                    if (isShowProductImg == false)
                                    {
                                        p.ImageUrl = p.Subject.GetShowProductStr(false);
                                        break;
                                    }
                                }

                                dicProduct.Add(p.ProductCode, p);
                            }

                            p.Skus?.ForEach(sku =>
                            {
                                if (dicSku.ContainsKey(sku.SkuCode) == false)
                                {
                                    var skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.SkuCode)).ToList();
                                    if (skuPathFlows == null || skuPathFlows.Any() == false)
                                    {
                                        //跟随商品显示
                                        skuPathFlows = productPathFlows;
                                    }

                                    // 商品标题是否可见
                                    foreach (var flow in skuPathFlows)
                                    {
                                        var isShowProductTitle = commonSettingRepository.SetIsShowProductTitle(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                                        if (isShowProductTitle == false)
                                        {
                                            sku.Subject = sku.Subject.GetShowProductStr(false);
                                            break;
                                        }
                                    }


                                    // 商品图片是否可见
                                    foreach (var flow in skuPathFlows)
                                    {
                                        var isShowProductImg = commonSettingRepository.SetIsShowProductImg(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                                        if (isShowProductImg == false)
                                        {
                                            sku.ImgUrl = sku.ImgUrl.GetShowProductStr(false);
                                            sku.ProductImage = sku.ProductImage.GetShowProductStr(false);
                                            break;
                                        }
                                    }

                                    dicSku.Add(sku.SkuCode, sku);
                                }
                            });
                        });
                    }

                }
                else
                {
                    var curProductCodes = new List<string>();
                    //跨云
                    if (curApplicationName == "fx_new")
                    {
                        //只要取当前用户的所在库
                        var curFxUserIds = g.ToList().Where(a => a.DbConfig != null).Select(a => a.DbConfig.UserId).Distinct().ToList();
                        curProductCodes = models.Where(a => a.ProductCloudPlatform == dbLocation && curFxUserIds.Contains(a.FxUserId)).Select(a => a.ProductCode).Distinct().ToList();
                    }
                    else
                    {
                        curProductCodes = models.Where(a => a.ProductCloudPlatform == dbLocation).Select(a => a.ProductCode).Distinct().ToList();
                    }
                    Log.Debug($"跨云查，dbLocation={dbLocation}，dbName={curDbConfig.DbNameConfig?.DbName}，curProductCodes={curProductCodes.ToJson()}", logFileName);

                    if (curProductCodes.Any())
                    {
                        var apiDbConfig = new ApiDbConfigModel { DbNameConfigId = curDbConfig.DbNameConfig.Id, Location = dbLocation, PlatformType = dbLocation };
                        var dbApi = new DbAccessUtility(apiDbConfig);

                        var sql = $@"SELECT {strFieldForApi} FROM Product p WITH(NOLOCK)
 LEFT JOIN ProductSku sku WITH(NOLOCK) ON p.ProductCode=sku.ProductCode  
 WHERE p.ProductCode=@code ";

                        try
                        {
                            #region 上下游商品信息是否可见

                            //查询商品路径流
                            string getPathFlowsSQL = $@"SELECT pfn.Id,pfn.DownFxUserId,pfn.UpFxUserId,pfn.FxUserId,pfn.PathFlowCode,pfr.PathFlowRefCode FROM dbo.PathFlowReference pfr WITH(NOLOCK) 
                            INNER JOIN dbo.PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode = pfr.PathFlowCode  AND pfr.Status = 0
                            INNER JOIN FunStringToTable(@codeStr, ',') t ON t.item=pfr.ProductCode";

                            var dbPathFlowList = dbApi.Query<PathFlowNode>(getPathFlowsSQL, new { codeStr = string.Join(",", curProductCodes) });
                            Dictionary<string, PathFlow> dict = new Dictionary<string, PathFlow>();
                            foreach (var item in dbPathFlowList)
                            {
                                PathFlow pf = new PathFlow() { PathFlowCode = item.PathFlowCode };
                                if (dict.ContainsKey(pf.PathFlowCode) == false)
                                {
                                    dict.Add(item.PathFlowCode, pf);
                                    pf.PathFlowNodes = new List<PathFlowNode>();
                                    pf.PathFlowReferences = new Dictionary<string, PathFlowReference>();
                                }
                                var cur = dict[pf.PathFlowCode];
                                if (cur.PathFlowNodes.Any(x => x.Id == item.Id) == false)
                                    cur.PathFlowNodes.Add(item);

                                if (cur.PathFlowReferences.ContainsKey(item.PathFlowRefCode) == false)
                                    cur.PathFlowReferences.Add(item.PathFlowRefCode, new PathFlowReference() { PathFlowRefCode = item.PathFlowRefCode });
                            }
                            var pathFlows = dict.Values.ToList();

                            var logicOrderRepository = new LogicOrderRepository();
                            var nodes = pathFlows.SelectMany(x => x.PathFlowNodes).ToList();
                            var pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(nodes);

                            #endregion

                            curProductCodes.ForEach(code =>
                            {
                                var curList = dbApi.Query<ProductSkuFxForAbnormal>(sql, new { code });

                                var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(code)).ToList();

                                curList?.ForEach(sku =>
                                {
                                    var skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.SkuCode)).ToList();
                                    if (skuPathFlows == null || skuPathFlows.Any() == false)
                                    {
                                        //跟随商品显示
                                        skuPathFlows = productPathFlows;
                                    }

                                    // 商品标题是否可见
                                    foreach (var flow in skuPathFlows)
                                    {
                                        var isShowProductTitle = commonSettingRepository.SetIsShowProductTitle(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                                        if (isShowProductTitle == false)
                                        {
                                            sku.Subject = sku.Subject.GetShowProductStr(false);
                                            break;
                                        }
                                    }


                                    // 商品图片是否可见
                                    foreach (var flow in skuPathFlows)
                                    {
                                        var isShowProductImg = commonSettingRepository.SetIsShowProductImg(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                                        if (isShowProductImg == false)
                                        {
                                            sku.ImageUrl = sku.ImageUrl.GetShowProductStr(false);
                                            sku.SkuImageUrl = sku.SkuImageUrl.GetShowProductStr(false);
                                            break;
                                        }
                                    }


                                    if (dicSkuFromApi.ContainsKey(sku.SkuCode) == false)
                                    {
                                        dicSkuFromApi.Add(sku.SkuCode, sku);
                                    }
                                });

                                Log.Debug($"跨云查到，dbLocation={dbLocation}，dbName={curDbConfig.DbNameConfig?.DbName}，code={code}，curList={curList?.Select(a => a.SkuCode).ToJson()}", logFileName);
                            });


                            

                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }
                    }
                }
            });

            //店铺商品赋值，基础规格图片地址转换
            models.ForEach(item =>
            {
                if (dicProduct.ContainsKey(item.ProductCode))
                {
                    item.ProductSubject = dicProduct[item.ProductCode].Subject;
                    item.ProductImageUrl = dicProduct[item.ProductCode].ImageUrl;
                }
                if (dicSku.ContainsKey(item.ProductSkuCode))
                {
                    item.ProductSkuCargoNumber = dicSku[item.ProductSkuCode].CargoNumber;
                    item.ProductSkuImageUrl = dicSku[item.ProductSkuCode].ImgUrl;
                    item.ProductSkuName = dicSku[item.ProductSkuCode].Name;
                    item.ProductSkuCargoNumber = dicSku[item.ProductSkuCode].CargoNumber;
                }
                else if (dicSkuFromApi.ContainsKey(item.ProductSkuCode))
                {
                    item.ProductSkuCargoNumber = dicSkuFromApi[item.ProductSkuCode].SkuCargoNumber;
                    item.ProductSkuImageUrl = dicSkuFromApi[item.ProductSkuCode].SkuImageUrl;
                    item.ProductSkuName = dicSkuFromApi[item.ProductSkuCode].SkuName;
                    item.ProductSkuCargoNumber = dicSkuFromApi[item.ProductSkuCode].SkuCargoNumber;

                    item.ProductSubject = dicSkuFromApi[item.ProductSkuCode].Subject;
                    item.ProductImageUrl = dicSkuFromApi[item.ProductSkuCode].ImageUrl;
                }
                item.BaseProductSkuImageUrl = ImgHelper.ChangeImgUrl(item.BaseProductSkuImageUrl);
                item.ProductDbName = string.IsNullOrEmpty(item.ProductDbName)
                    ? item.ProductDbName
                    : DES.EncryptDES(item.ProductDbName, CustomerConfig.LoginCookieEncryptKey);
            });

        }
    }
}