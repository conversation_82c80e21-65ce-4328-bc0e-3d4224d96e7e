
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Warehouse.Model.Request;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Utility.Other;
using static DianGuanJiaApp.Data.Repository.FinancialSettlementRepository;
using DianGuanJiaApp.Services.Services.BaseProduct;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Warehouse.Entity;
using Exception = System.Exception;
using SupplierModel = DianGuanJiaApp.Data.Model.BaseProduct.SupplierModel;
using DianGuanJiaApp.Data.Repository.Settings;
using TruncationException = MongoDB.Bson.TruncationException;
using Jd.Api.Domain;
using vipapis.vipcard;
using DianGuanJiaApp.Data.FxModel.Listing;
using Nest;
using Aop.Api.Domain;
using DianGuanJiaApp.Data.Entity.HotProduct;

namespace DianGuanJiaApp.Services.BaseProduct
{
    public class BaseProductSkuCommonService
    {
        private static readonly object _bindLock = new object();
        
        /// <summary>
        /// 基础商品解绑（从BaseProductController移过来）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public CheckResult BaseProductSkuRelationUnbind(BaseProductSkuUnbindModel model)
        {
            var result = new CheckResult();
            #region 解绑基础
            //if (string.IsNullOrEmpty(model.SkuCode))
            //{
            //    result.Message = "请输入关联商品Sku编码！";
            //    return result;
            //}
            if (string.IsNullOrEmpty(model.ProductCode))
            {
                result.Message = "请输入关联商品编码！";
                return result;
            }
            if (string.IsNullOrEmpty(model.ProductSkuCode))
            {
                result.Message = "关联商品规格SkuCode为空，请联系我们";
                return result;
            }
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService(false);
            var messageService = new MessageRecordService();
            var ptProductSkuService = new ProductSkuFxService();
            var dbName = model.ProductDbName;
            model.IsAllUseWarehouse = true;//强制为true

            // 平台商品
            var isExistPtSku = true;
            var ptProductSkus = ptProductSkuService.GetProductSkuBySkuCode(new List<string> { model.ProductSkuCode });
            if (ptProductSkus.Count == 0)
            {
                //result.Message = "未找到基础商品信息绑定的平台商品，请联系我们";
                //return result;

                // 此处存在，店铺商品已被清除的情况，会根据关联关系生成默认sku
                isExistPtSku = false;
                
            }
            var relations = baseOfPtSkuRelationService.GetListBySkuModel(new List<long> { model.BaseProductSkuUid }, new List<string> { model.ProductSkuCode }, fxUserId);
            if (relations.Count == 0)
            {
                result.Message = "未找到对应的绑定关系，请联系我们";
                return result;
            }
            relations.ForEach(r =>
            {
                r.UpdateTime = DateTime.Now;
                r.Status = 0;
                if (!isExistPtSku)
                {
                    ptProductSkus.Add(
                        new ProductSkuFx()
                        {
                            SkuCode = r.ProductSkuCode,
                            PlatformType = r.ProductPlatformType,
                        }
                     );
                }
            });

            var sku = ptProductSkus.FirstOrDefault();

            var relation = relations.FirstOrDefault();

            var subModel = new MessageRecord();
            subModel.MsgType = BaseProductMsgType.BaseOfPtSkuRelationDel;
            subModel.FxUserId = fxUserId;
            subModel.DbName = dbName;
            subModel.ProductPlatformType = CloudPlatformType.Alibaba.ToString();
            subModel.BusinessId = "";
            subModel.DataJson = relation.ToJson();

            // 平台商品绑定
            baseOfPtSkuRelationService.BulkUpdate(relations);
            // 冷库解绑
            try
            {
                var userDbConfig = SiteContext.Current.CurrentDbConfig;
                var enableColdGlobal = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
                if (enableColdGlobal&&userDbConfig!=null&&userDbConfig.EnableColdDb)
                {
                    var coldService = new BaseOfPtSkuRelationService(userDbConfig.ColdDbConnectionString, false);
                    coldService.BulkUpdate(relations);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"冷库解绑写入失败，原因:{ex}", $"{nameof(BaseProductCommonService)}-{DateTime.Now.FormatDate()}.log");
            }
            // 基础商品解绑 
            if (CustomerConfig.IsLocalDbDebug)
                messageService.SyncBaseOfPtSkuRelationToBusinessDb(new List<MessageRecord> { subModel }, 1);
            else
                messageService.SendBusinessMessage(new List<MessageRecord> { subModel });

            // 解绑日志
            try
            {
                baseOfPtSkuRelationService.SetUnBindRelationRecords(new List<BaseProductSkuUnbindModel>(){model}, fxUserId);
            }
            catch (Exception ex)
            {
                Log.WriteError($"用户{fxUserId}解绑管理记录解绑日志发生异常，解绑模型：{model.ToJson()}，异常消息：{ex.StackTrace}");
            }

            #endregion

            // 库存系统解绑关联关系
            if (model.IsAllUseWarehouse)
            {
                Task.Run(() =>
                {
                    var connectionString = SiteContext.Current.CurrentDbConfig.ConnectionString;
                    var _wareHouseService = new WareHouseService(connectionString);
                    var curUserId = SiteContext.Current.CurrentFxUserId;
                    var ownCode = _wareHouseService.GetOwnerCode(curUserId);
                    // 获取库存系统数据的业务库绑定关系
                    var wareHouseSkuBindRelation = new WareHouseSkuBindRelationRepository(connectionString).GetBindRelation(ownCode, sku.SkuCode, sku.PlatformType);
                    if (wareHouseSkuBindRelation == null) return;
                    var req = new WarehouseSkuUnbindRequest()
                    {
                        IsRollback = false,
                        OwnerCode = ownCode,
                        SkuBindRelationCode = wareHouseSkuBindRelation.SkuBindRelationCode
                    };

                    var rsp = _wareHouseService.UnBindProduct(req, true);
                    if (CustomerConfig.IsDebug) Log.WriteLine($"基础商品到库存系统解绑关联关系：{rsp.Message}");
                });
            }

            if (isExistPtSku)
            {
                // 清空简称、结算价、成本价，平台商品设为自营
                RestorePtSku(model, fxUserId, relation);
            }

            result.Success = true;
            return result;
        }

        /// <summary>
        ///  基础商品批量解绑
        /// </summary>
        /// <param name="model">解绑数据</param>
        /// <param name="isCloudOperate">是否云平台下解绑</param>
        /// <returns></returns>
        public CheckResult<BatchBaseProductSkuUnbindRes> BatchBaseProductSkuRelationUnbind(List<BatchBaseProductSkuUnbindModel> models, 
            bool isCloudOperate = true,
            string dbname = ""
            )
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService(false);
            var ptProductSkuService = new ProductSkuFxService();

            var res = new CheckResult<BatchBaseProductSkuUnbindRes>();

            var ptProductSkuCodes = models
                .SelectMany(p => p.Skus)
                .Select(p => p.ProductSkuPtCode)
                .Distinct()
                .ToList();
            var baseProductSkuUids = models
                .Select(p => p.BaseProductSkuUid)
                .Distinct().ToList();

            var ptProductSkus = ptProductSkuService.GetProductSkuBySkuCode(ptProductSkuCodes);
            if (!ptProductSkus.Any())
            {
                res.Success = false;
                res.Data = null;
                res.Message = "未找到基础商品信息绑定的平台商品，请联系我们！";
                return res;
            }

            var relations = baseOfPtSkuRelationService.GetListBySkuModel(baseProductSkuUids, ptProductSkuCodes, fxUserId, searchDelete:false);

            if (relations.Count == 0)
            {
                res.Success = false;
                res.Data = null;
                res.Message = "未找到对应的绑定关系，请联系我们！";
                return res;
            }

            var req = new BatchUnbindBaseProductSkuData
            {
                IsRestoreCostPrice = models.FirstOrDefault()?.IsRestoreCostPrice ?? false,
                IsRestoreDistributePrice = models.FirstOrDefault()?.IsRestoreDistributePrice ?? false,
                IsRestoreSettlePrice = models.FirstOrDefault()?.IsRestoreSettlePrice ?? false,
                IsRestoreShortTitle = models.FirstOrDefault()?.IsRestoreShortTitle ?? false,
                IsRestoreSupplier = models.FirstOrDefault()?.IsRestoreSupplier ?? false,
                IsAllUseWarehouse = true,
                UnbindDatas = new List<BaseProductSkuUnbindModel>()
            };

            relations.ForEach(r =>
            {
                r.UpdateTime = DateTime.Now;
                r.Status = 0;
            });

            
            var failSkuCodes = new List<string>();

            foreach (var sku in ptProductSkus)
            {
                var relation = relations.FirstOrDefault(a => a.ProductSkuCode == sku.SkuCode && a.FxUserId == fxUserId);
                if (relation != null)
                {
                    var skuData = new BaseProductSkuUnbindModel
                    {
                        RelationDbId = relation?.Id ?? 0,
                        BaseProductSkuUid = relation?.BaseProductSkuUid ?? 0,
                        BaseProductUid = relation?.BaseProductUid ?? 0,
                        ProductSkuPtId = sku.SkuId,
                        SkuCode = relation.BaseProductSkuCode,
                        ProductSkuCode = sku.SkuCode,
                        ProductCode = sku.ProductCode,
                        ProductDbName = dbname,
                        ProductCloudPlatform = CustomerConfig.CloudPlatformType,
                    };
                    req.UnbindDatas.Add(skuData);
                }

                if (isCloudOperate)
                {
                    try
                    {
                        var dbName = dbname;
                        var subModel = new MessageRecord();
                        var messageService = new MessageRecordService();
                        subModel.MsgType = BaseProductMsgType.BaseOfPtSkuRelationDel;
                        subModel.FxUserId = fxUserId;
                        subModel.DbName = dbName;
                        subModel.ProductPlatformType = CloudPlatformType.Alibaba.ToString();
                        subModel.BusinessId = "";
                        subModel.DataJson = relation.ToJson();
                        if (CustomerConfig.IsLocalDbDebug)
                            messageService.SyncBaseOfPtSkuRelationToBusinessDb(new List<MessageRecord> { subModel }, 1);
                        else
                            messageService.SendBusinessMessage(new List<MessageRecord> { subModel });
                    }
                    catch (Exception ex)
                    {
                        failSkuCodes.Add(relation.ProductSkuCode);
                        Log.WriteError($"{CustomerConfig.CloudPlatformType.ToString()}平台解绑消息发送失败，失败原因：{ex.ToJson()}");
                    }
                }

                try
                {
                    baseOfPtSkuRelationService.SetUnBindRelationRecords(req.UnbindDatas, fxUserId);// 解绑日志
                }
                catch (Exception ex)
                {
                    Log.WriteError($"用户{fxUserId}解绑管理记录解绑日志发生异常，解绑模型：{req.ToJson()}，异常消息：{ex.StackTrace}");
                }
            }

            baseOfPtSkuRelationService.BulkUpdate(relations);

            #region 冷库解绑
            try
            {
                var userDbConfig = SiteContext.Current.CurrentDbConfig;
                var enableColdGlobal = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
                if (enableColdGlobal && userDbConfig != null && userDbConfig.EnableColdDb)
                {
                    var coldService = new BaseOfPtSkuRelationService(userDbConfig.ColdDbConnectionString, false);
                    coldService.BulkUpdate(relations);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"冷库解绑写入失败，原因:{ex}", $"{nameof(BaseProductCommonService)}-{DateTime.Now.FormatDate()}.log");
            }
            #endregion

            // 清空简称、结算价、成本价，平台商品设为自营
            BatchRestorePtSku(req, fxUserId, relations, ptProductSkus);

            // 解绑所有规格数
            var allSkuCodes = ptProductSkuCodes.Distinct().ToList();
            var successSkuCodes = allSkuCodes.Except(failSkuCodes).ToList();

           
            var data = new BatchBaseProductSkuUnbindRes();
            data.SuccessCount = successSkuCodes.Count();
            data.FailCount = failSkuCodes.Count();
            data.FailSkuCodes = failSkuCodes;

            res.Success = true;
            res.Data = data;
            res.Message = "成功！";
            return res;
        }

        /// <summary>
        /// 批量解绑关联
        /// 注意：要保证models中，所有的ProductDbName都相同
        /// 确保需要操作的数据都在本业务库
        /// </summary>
        /// <param name="models"></param>
        /// <param name="isFromAbnormalUnbind">是否来自异常关联解绑</param>
        /// <returns></returns>
        public UnbindRelationResultModel BaseProductSkuRelationUnbind(List<BaseProductSkuUnbindModel> models,bool isFromAbnormalUnbind=false)
        {
            var result = new UnbindRelationResultModel(){ AllCount = models.Count,IsAbnormalUnbind = isFromAbnormalUnbind};

            #region 解绑基础

            var nullSkuCodeList = models.Where(m => m.SkuCode.IsNullOrEmpty()).ToList();
            if (!FilterCannotUnbindSku(ref models, nullSkuCodeList, "请输入关联商品Sku编码", result))
                return result;

            var nullProductCodeList = models.Where(m => m.ProductCode.IsNullOrEmpty()).ToList();
            if (!FilterCannotUnbindSku(ref models, nullProductCodeList, "请输入关联商品编码", result))
                return result;

            var nullProductSkuCodeList = models.Where(m => m.ProductSkuCode.IsNullOrEmpty()).ToList();
            if (!FilterCannotUnbindSku(ref models, nullProductSkuCodeList, "关联商品规格SkuCode为空，请联系我们", result))
                return result;

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService(false);
            var messageService = new MessageRecordService();
            var ptProductSkuService = new ProductSkuFxService();

            // model.IsAllUseWarehouse = true;//强制为true，前面构造时已做处理
            #endregion

            // 平台商品
            var productSkuCodeList = models.Select(m => m.ProductSkuCode).Distinct().ToList(); // 解绑模型传入的skuCode
            var ptProductSkus = ptProductSkuService.GetProductSkuBySkuCode(productSkuCodeList);
            var ptProductSkuCodeList = ptProductSkus.Select(p => p.SkuCode).ToList();// 查询数据库后查到的skuCode

            // 未查询到平台商品的列表
            var notFindProductSkuList = models.Where(m => productSkuCodeList.Except(ptProductSkuCodeList).Contains(m.ProductSkuCode)).ToList();
            if (!FilterCannotUnbindSku(ref models, notFindProductSkuList, "未找到基础商品信息绑定的平台商品，请联系我们", result))
                return result;

            var skuUidList = models.Select(m => m.BaseProductSkuUid).Distinct().ToList(); // 解绑模型传入的skuUid

            var relationList = baseOfPtSkuRelationService.GetListBySkuModel(skuUidList, ptProductSkuCodeList, fxUserId);
            var relationSkuUidList = relationList.Select(r => r.BaseProductSkuUid).Distinct().ToList(); // 查询数据库后得到的skuUid

            // 未查询到关联的列表
            var notFindRelationList =
                models.Where(m => skuUidList.Except(relationSkuUidList).Contains(m.BaseProductSkuUid)).ToList();
            if (!FilterCannotUnbindSku(ref models, notFindRelationList, "未找到对应的绑定关系，请联系我们", result))
                return result;

            var unbindMessageList = new List<MessageRecord>(relationList.Count);
            var dbNameDict = models.GroupBy(m=>m.ProductSkuCode).ToDictionary(m => m.Key, m => m.FirstOrDefault()?.ProductDbName);
            relationList.ForEach(r =>
            {
                r.UpdateTime = DateTime.Now;
                r.Status = 0;

                unbindMessageList.Add(new MessageRecord()
                {
                    MsgType = BaseProductMsgType.BaseOfPtSkuRelationDel,
                    FxUserId = fxUserId,
                    DbName = dbNameDict.TryGetValue(r.ProductSkuCode,out var dbname)? dbname : "",
                    ProductPlatformType = CloudPlatformType.Alibaba.ToString(),
                    BusinessId = $"{fxUserId}_{r.BaseProductSkuUid}_{r.ProductSkuCode}_{DateTime.Now.ToLongTimeString()}",
                    DataJson = r.ToJson()
                });
            });

            // 平台商品绑定
            baseOfPtSkuRelationService.BulkUpdate(relationList);

            // 基础商品解绑 
            if (CustomerConfig.IsLocalDbDebug)
                messageService.SyncBaseOfPtSkuRelationToBusinessDb(unbindMessageList, 1);
            else
                messageService.SendBusinessMessage(unbindMessageList);

            
            // 库存系统解绑关联关系
            Task.Run(() =>
            {
                try
                {
                    var connectionString = SiteContext.Current.CurrentDbConfig.ConnectionString;
                    var wareHouseService = new WareHouseService(connectionString);
                    var curUserId = SiteContext.Current.CurrentFxUserId;
                    var ownCode = wareHouseService.GetOwnerCode(curUserId);
                    var ptSkuCodeList = models?.Select(m => m.ProductSkuCode).ToList();
                    var wareHouseSkuBindRelationList =
                        new WareHouseSkuBindRelationRepository(connectionString).GetBindRelationList(ownCode, ptSkuCodeList);

                    if (wareHouseSkuBindRelationList.IsNullOrEmptyList()) return;

                    var req = new WarehouseSkuUnbindRequest()
                    {
                        IsRollback = false,
                        OwnerCode = ownCode,
                        IsBatch = true,
                        SkuBindRelationCodeList = wareHouseSkuBindRelationList.Select(r=>r.SkuBindRelationCode).ToList()
                    };

                    var rsp = wareHouseService.UnBindProduct(req, true);
                    if (CustomerConfig.IsDebug) Log.WriteLine($"基础商品到库存系统解绑关联关系：{rsp.Message}");
                }
                catch (Exception ex)
                {
                    Log.WriteError($"用户{fxUserId}库存系统解绑关联异常，解绑模型：{models.ToJson()}，异常消息：{ex.StackTrace}");
                }
                
            });

            // 清空简称、结算价、成本价，平台商品设为自营，内部记录解绑日志 
            BatchRestorePtSku(models, fxUserId, relationList);
            return result;
        }

        /// <summary>
        /// 过滤无法解绑的sku
        /// </summary>
        /// <param name="unbindList"></param>
        /// <param name="removeList"></param>
        /// <param name="failedReason"></param>
        /// <param name="result"></param>
        /// <returns></returns>
        private bool FilterCannotUnbindSku(ref List<BaseProductSkuUnbindModel> unbindList,
            List<BaseProductSkuUnbindModel> removeList, string failedReason, UnbindRelationResultModel result)
        {
            if (unbindList.IsNullOrEmptyList() || removeList.IsNullOrEmptyList() || result.FailedDict == null || failedReason == null)
                return true;

            if (result.IsAbnormalUnbind)
            {
                if (result.FailedAbnormalIdDict.TryGetValue(failedReason, out var list))
                {
                    if (list == null)
                        list = new List<long>();
                    list.AddRange(removeList.SelectMany(d => d.AbnormalIdList));
                }
                else
                {
                    result.FailedAbnormalIdDict.Add(failedReason, removeList.SelectMany(d => d.AbnormalIdList).ToList());
                }
            }
            else
            {
                if (result.FailedDict.TryGetValue(failedReason, out var list))
                {
                    if (list == null)
                        list = new List<string>();
                    list.AddRange(removeList.Select(d => d.ProductSkuCode));
                }
                else
                {
                    result.FailedDict.Add(failedReason, removeList.Select(d => d.ProductSkuCode).ToList());
                }
            }

            //result.SuccessCount -= removeList.Count;

            if (removeList.Count == unbindList.Count)
                return false;
            unbindList = unbindList.Except(removeList).ToList();
            return true;
        }

        /// <summary>
        /// 清除平台商品数据
        /// </summary>
        /// <param name="model"></param>
        /// <param name="fxUserId"></param>
        /// <param name="relation"></param>
        public void RestorePtSku(BaseProductSkuUnbindModel model, int fxUserId, BaseOfPtSkuRelation relation)
        {
            // 获取sku上下游 sku没有自己上下游的 取商品的上下游
            var agent = 0;
            var suppliers = new List<int>();
            var deletePriceModels = new List<ProductSettlementPrice>();
            if (model.IsRestoreDistributePrice || model.IsRestoreSettlePrice)
            {
                var skuCodes = new List<string>() { relation.ProductSkuCode };
                var productCodes = new List<string>() { relation.ProductCode };
                var service = new ProductFxService();
                var results = service.GetSupplierAndAgentByAllCodes(productCodes, skuCodes, fxUserId, true);
                var supplierAgentDict = results.GroupBy(r => r.ProductRefCode).ToDictionary(d => d.Key, g => g.ToList());
                service.GetSkuSupplierAndAgentFromResult(supplierAgentDict, ref suppliers, ref agent, relation.ProductSkuCode, relation.ProductCode);
            }
            // 清空简称
            if (model.IsRestoreShortTitle)
            {
                var repository = new ProductSkuInfoFxRepository();
                var exists = repository.GetExistIdAndCodes(new List<string>() { relation.ProductSkuCode }, new List<int>() { fxUserId });
                repository.BatchDelete(exists.Select(e => e.Id).ToList(), fxUserId);
            }
            // 清空分销价 对商家
            if (model.IsRestoreDistributePrice && agent > 0)
            {
                deletePriceModels.Add(new ProductSettlementPrice()
                {
                    CreateUser = fxUserId,
                    FxUserId = agent,
                    SettlementType = SettlementType.Manufacturer.ToInt(),
                    ProductSkuCode = relation.ProductSkuCode
                });
            }
            // 清空结算价 对厂家
            if (model.IsRestoreSettlePrice)
            {
                // 默认采购价格处理
                if (!suppliers.IsNullOrEmptyList())
                {
                    suppliers.ForEach(suId =>
                    {
                        deletePriceModels.Add(new ProductSettlementPrice()
                        {
                            CreateUser = fxUserId,
                            FxUserId = suId,
                            SettlementType = SettlementType.Merchant.ToInt(),
                            ProductSkuCode = relation.ProductSkuCode
                        });
                    });
                }
                else
                {
                    deletePriceModels.Add(new ProductSettlementPrice()
                    {
                        CreateUser = fxUserId,
                        FxUserId = 0,
                        SettlementType = SettlementType.DefaultMerchant.ToInt(),
                        ProductSkuCode = relation.ProductSkuCode
                    });
                    Log.WriteLine("默认结算价格清除");
                }
            }
            // 清空成本价
            if (model.IsRestoreCostPrice)
            {
                deletePriceModels.Add(new ProductSettlementPrice()
                {
                    CreateUser = fxUserId,
                    FxUserId = 0,
                    SettlementType = SettlementType.CostPrice.ToInt(),
                    ProductSkuCode = relation.ProductSkuCode
                });
            }
            new FinancialSettlementService().BatchDelete(deletePriceModels, fxUserId);

            // 厂家设为自营
            if (model.IsRestoreSupplier && !suppliers.IsNullOrEmptyList())
            {

                // 平台商品设为自营
                var bindModel = new BindSupplierRequestModel()
                {
                    productCodes = new List<string>() { relation.ProductCode },
                    skuCodes = new Dictionary<string, string>() { { relation.ProductCode, relation.ProductSkuCode } },
                    isSelf = true,
                    IsBindSku = true
                };

                for (var i = 0; i < 3; i++)
                {
                    try
                    {
                        new ExceptionHandler().ExceptionRetryHandler(() => new ProductFxService().BindSupplier(bindModel), false);
                    }
                    catch (Exception ex)
                    {
                    }
                }

            }

        }


        /// <summary>
        /// 清除平台商品数据
        /// </summary>
        /// <param name="model"></param>
        /// <param name="fxUserId"></param>
        /// <param name="relation"></param>
        public void BatchRestorePtSku(BatchUnbindBaseProductSkuData model, int fxUserId, List<BaseOfPtSkuRelation> relations, List<ProductSkuFx> ptProductSkus)
        {
            List<SupplierAgentModel> results = new List<SupplierAgentModel>();
            Dictionary<string, List<SupplierAgentModel>> supplierAgentDict = null;
            BindSupplierRequestModel bindModel = null;

            var skuCodes = relations
                .Select(p => p.ProductSkuCode).Distinct().ToList();
            var productCodes = relations
                .Select(p => p.ProductCode).Distinct().ToList();
            var service = new ProductFxService();
            var connectionString = SiteContext.Current.CurrentDbConfig.ConnectionString;
            var curUserId = SiteContext.Current.CurrentFxUserId;

            var _wareHouseService = new WareHouseService(connectionString);
            var _ownCode = _wareHouseService.GetOwnerCode(curUserId);
            var _wareHouseReq = new WarehouseSkuUnbindRequest()
            {
                IsRollback = false,
                IsBatch = true,
                OwnerCode = _ownCode,
                SkuBindRelationCodeList = new List<string>(),
            };
           
            var wareHouseSkuBindRelations = new WareHouseSkuBindRelationRepository(connectionString).GetBindRelationListBatch(_ownCode, skuCodes);

            // 分销价//采购价格
            if (model.IsRestoreDistributePrice || model.IsRestoreSettlePrice)
            {
                results = service.GetSupplierAndAgentByAllCodes(productCodes, skuCodes, fxUserId, true);
                supplierAgentDict = results.GroupBy(r => r.ProductRefCode).ToDictionary(d => d.Key, g => g.ToList());
            }

            // 清空简称
            if (model.IsRestoreShortTitle)
            {
                var repository = new ProductSkuInfoFxRepository();
                var exists = repository.GetExistIdAndCodes(skuCodes, new List<int>() { fxUserId });
                repository.BatchDelete(exists.Select(e => e.Id).ToList(), fxUserId);
            }

            var deletePriceModels = new List<ProductSettlementPrice>();
            

            foreach (var relation in relations)
            {
                var agent = 0;
                var suppliers = new List<int>();

                service.GetSkuSupplierAndAgentFromResult(supplierAgentDict, ref suppliers, ref agent, relation.ProductSkuCode, relation.ProductCode);

                // 清空分销价 对商家
                if (model.IsRestoreDistributePrice && agent > 0)
                {
                    deletePriceModels.Add(new ProductSettlementPrice()
                    {
                        CreateUser = fxUserId,
                        FxUserId = agent,
                        SettlementType = SettlementType.Manufacturer.ToInt(),
                        ProductSkuCode = relation.ProductSkuCode
                    });
                }
                // 清空结算价 对厂家
                if (model.IsRestoreSettlePrice)
                {
                    // 默认采购价格处理
                    if (!suppliers.IsNullOrEmptyList())
                    {
                        suppliers.ForEach(suId =>
                        {
                            deletePriceModels.Add(new ProductSettlementPrice()
                            {
                                CreateUser = fxUserId,
                                FxUserId = suId,
                                SettlementType = SettlementType.Merchant.ToInt(),
                                ProductSkuCode = relation.ProductSkuCode
                            });
                        });
                    }
                    else
                    {
                        deletePriceModels.Add(new ProductSettlementPrice()
                        {
                            CreateUser = fxUserId,
                            FxUserId = 0,
                            SettlementType = SettlementType.DefaultMerchant.ToInt(),
                            ProductSkuCode = relation.ProductSkuCode
                        });
                        Log.WriteLine("默认结算价格清除");
                    }
                }
                // 清空成本价
                if (model.IsRestoreCostPrice)
                {
                    deletePriceModels.Add(new ProductSettlementPrice()
                    {
                        CreateUser = fxUserId,
                        FxUserId = 0,
                        SettlementType = SettlementType.CostPrice.ToInt(),
                        ProductSkuCode = relation.ProductSkuCode
                    });
                }


                // 厂家设为自营
                if (model.IsRestoreSupplier && !suppliers.IsNullOrEmptyList())
                {
                    if(bindModel == null)
                    {
                        // 平台商品设为自营
                        bindModel = new BindSupplierRequestModel()
                        {
                            productCodes = new List<string>() { relation.ProductCode },
                            skuCodes = new Dictionary<string, string>() { { relation.ProductCode, relation.ProductSkuCode } },
                            isSelf = true,
                            IsBindSku = true
                        };
                    }
                    else
                    {
                        bindModel.productCodes.Add(relation.ProductCode);
                        if(bindModel.skuCodes.TryGetValue(relation.ProductCode,out string skuCode))
                        {
                            bindModel.skuCodes[relation.ProductCode] = $"{skuCode},{relation.ProductSkuCode}";
                        }
                        else
                        {
                            bindModel.skuCodes.Add(relation.ProductCode, relation.ProductSkuCode);
                        }
                    }
                }


                // 库存系统解绑关联关系
                if (model.IsAllUseWarehouse)
                {
                    var sku = ptProductSkus.Where(p => p.SkuCode == relation.ProductSkuCode).FirstOrDefault();
                    if (sku != null)
                    {
                        var wareHouseSkuBindRelation = wareHouseSkuBindRelations.FirstOrDefault(p => p.PlatformSkuCode == sku.SkuCode);
                        if (wareHouseSkuBindRelation != null)
                            _wareHouseReq.SkuBindRelationCodeList.Add(wareHouseSkuBindRelation.SkuBindRelationCode);
                    }
                   
                }
            }
            if(deletePriceModels.Any())
                new FinancialSettlementService().BatchDelete(deletePriceModels, fxUserId);
            if(bindModel!=null)
                new ExceptionHandler().ExceptionRetryHandler(() => new ProductFxService().BindSupplier(bindModel), false);

            // 库存批量处理
            Task.Run(() =>
            {
                var rsp = _wareHouseService.UnBindProduct(_wareHouseReq, true);
            });
        }

        /// <summary>
        /// 批量清除平台商品数据
        /// </summary>
        /// <param name="models"></param>
        /// <param name="fxUserId"></param>
        /// <param name="relations"></param>
        public void BatchRestorePtSku(List<BaseProductSkuUnbindModel> models, int fxUserId, List<BaseOfPtSkuRelation> relations)
        {
            if(models.IsNullOrEmptyList() || relations.IsNullOrEmptyList())
                return;
            
            var firstModel = models.FirstOrDefault();
            if(firstModel == null) return;

            
            var skuCodes = relations.Select(r=>r.ProductSkuCode).Distinct().ToList();
            var productFxService = new ProductFxService();
            var productCodes = relations.Select(r => r.ProductCode).Distinct().ToList(); ;
            var supplierAgentList = productFxService.GetSupplierAndAgentByAllCodes(productCodes, skuCodes, fxUserId, true);
            var supplierAgentDict = supplierAgentList.GroupBy(r => r.ProductRefCode).ToDictionary(d => d.Key, g => g.ToList());

            // 清空简称
            if (firstModel.IsRestoreShortTitle)
            {
                var repository = new ProductSkuInfoFxRepository();
                var exists = repository.GetExistIdAndCodes(skuCodes, new List<int>() { fxUserId });
                repository.BatchDelete(exists.Select(e => e.Id).ToList(), fxUserId);
            }

            // 清空成本价
            var deletePriceModels = new List<ProductSettlementPrice>();
            if (firstModel.IsRestoreCostPrice)
            {
                relations.ForEach(relation =>
                {
                    deletePriceModels.Add(new ProductSettlementPrice()
                    {
                        CreateUser = fxUserId,
                        FxUserId = 0,
                        SettlementType = SettlementType.CostPrice.ToInt(),
                        ProductSkuCode = relation.ProductSkuCode
                    });
                });
                
            }

            // 需要设为自营的模型
            var unbindSupplierModels = new List<BaseProductSkuUnbindModel>();

            // 查询结算价，用于写解绑日志
            var toFxUserIds = new HashSet<int>();
            supplierAgentList.ForEach(sa =>
            {
                if (sa.DownFxUserId != 0)
                    toFxUserIds.Add(sa.DownFxUserId);
                if (sa.UpFxUserId != 0)
                    toFxUserIds.Add(sa.UpFxUserId);
            });
            var priceTuple = new SettlementProductSkuService().GetSettlementPrice(skuCodes, toFxUserIds.ToList(),fxUserId,true,true);
            var mySettlePriceDict = priceTuple.Item2.ToDictionary(p=>p.UniqueKey);
            var costPriceDict = priceTuple.Item3.ToDictionary(p=>p.UniqueKey);

            var relationDict = relations.GroupBy(r => r.ProductSkuCode).ToDictionary(d => d.Key, d => d.ToList());
            models.ForEach(model =>
            {
                var skuCode = model.ProductSkuCode;
                var productCode = model.ProductCode;
                relationDict.TryGetValue(model.ProductSkuCode, out var relList);
                model.RelationDbId = relList?.FirstOrDefault()?.Id ?? 0;
                // 获取sku上下游 sku没有自己上下游的 取商品的上下游
                var agent = 0;
                var suppliers = new List<int>();
                
                productFxService.GetSkuSupplierAndAgentFromResult(supplierAgentDict, ref suppliers, ref agent, skuCode, productCode);

                // 给model的商家、厂家以及对应结算价、成本价赋值，方便后面写解绑记录
                model.PtSkuSupplierModels = new List<SupplierModel>();
                suppliers?.ForEach(sid =>
                {
                    if(sid == 0) return;
                    var settlePriceKey = (fxUserId + skuCode + SettlementType.Merchant.ToInt() + sid).ToShortMd5();
                    mySettlePriceDict.TryGetValue(settlePriceKey, out var settlePrice);
                    model.PtSkuSupplierModels.Add(new SupplierModel()
                    {
                        SupplierFxUserId = sid,
                        SettlePrice = settlePrice?.Price
                    });
                });

                if (agent != 0)
                {
                    var distributePriceKey = (fxUserId + skuCode + SettlementType.Manufacturer.ToInt() + agent).ToShortMd5();
                    mySettlePriceDict.TryGetValue(distributePriceKey, out var distributePrice);
                    model.AgentId = agent;
                    model.DistributePrice = distributePrice?.Price;
                }
                
                var costPriceKey = (fxUserId + skuCode + SettlementType.CostPrice.ToInt() + 0).ToShortMd5();
                costPriceDict.TryGetValue(costPriceKey, out var costPrice);
                model.CostPrice = costPrice?.Price;

                // 清空分销价 对商家
                if (firstModel.IsRestoreDistributePrice && agent > 0)
                {
                    deletePriceModels.Add(new ProductSettlementPrice()
                    {
                        CreateUser = fxUserId,
                        FxUserId = agent,
                        SettlementType = SettlementType.Manufacturer.ToInt(),
                        ProductSkuCode = skuCode
                    });
                }
                // 清空结算价 对厂家
                if (firstModel.IsRestoreSettlePrice)
                {
                    // 默认采购价格处理
                    if (!suppliers.IsNullOrEmptyList())
                    {
                        suppliers.ForEach(suId =>
                        {
                            deletePriceModels.Add(new ProductSettlementPrice()
                            {
                                CreateUser = fxUserId,
                                FxUserId = suId,
                                SettlementType = SettlementType.Merchant.ToInt(),
                                ProductSkuCode = skuCode
                            });
                        });
                    }
                    else
                    {
                        deletePriceModels.Add(new ProductSettlementPrice()
                        {
                            CreateUser = fxUserId,
                            FxUserId = 0,
                            SettlementType = SettlementType.DefaultMerchant.ToInt(),
                            ProductSkuCode = skuCode
                        });
                        //Log.WriteLine("默认结算价格清除");
                    }
                }
                // 厂家设为自营
                if (firstModel.IsRestoreSupplier && !suppliers.IsNullOrEmptyList())
                {
                    unbindSupplierModels.Add(model);
                }

            });
            // 厂家设为自营
            if (unbindSupplierModels.IsNotNullAndAny())
            {
                // 平台商品设为自营
                var bindModel = new BindSupplierRequestModel()
                {
                    productCodes = unbindSupplierModels.Select(m=>m.ProductCode).ToList(),
                    skuCodes = unbindSupplierModels.ToDictionary(m=>m.ProductCode,m=>m.ProductSkuCode),
                    isSelf = true,
                    IsBindSku = true
                };

                try
                {
                    new ExceptionHandler().ExceptionRetryHandler(() => new ProductFxService().BindSupplier(bindModel), false);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"批量异常解绑-店铺商品设为自营发生异常：{ex}", ModuleFileName.BaseProduct);
                }
            }

            // 解绑日志
            try
            {
                new BaseOfPtSkuRelationService(false).SetUnBindRelationRecords(models, fxUserId);
            }
            catch (Exception ex)
            {
                Log.WriteError($"用户{fxUserId}解绑关联记录解绑日志发生异常，解绑模型：{models.ToJson()}，异常消息：{ex}",ModuleFileName.BaseProduct);
            }

            // 清除结算价
            if(deletePriceModels.IsNullOrEmptyList())
                return;
            try
            {
                new FinancialSettlementService().BatchDelete(deletePriceModels, fxUserId);
            }
            catch (Exception ex)
            {
                Log.WriteError($"用户{fxUserId}解绑关联记录清除结算价/成本价发生异常，解绑模型：{models.ToJson()}，异常消息：{ex}", ModuleFileName.BaseProduct);
            }
            
        }

        /// <summary>
        /// 获取解绑关联 恢复初始数据消息模型
        /// </summary>
        /// <returns></returns>
        public Tuple<BindSupplierRequestModel, SyncPtSkuModel> GetUnBindSkuRestoreModel(BaseProductSkuUnbindModel model, int fxUserId, BaseOfPtSkuRelation relation)
        {
            var syncModel = new SyncPtSkuModel();
            BindSupplierRequestModel bindModel = null;
            syncModel.IsUnBind = true;
            syncModel.FxUserId = fxUserId;

            if (model.IsRestoreShortTitle)
            {
                // 清空简称
                syncModel.UpdateShortTitleModels.Add(new SaveShortTitleOrWeightModel()
                {
                    ProductCode = relation.ProductCode,
                    SkuCode = relation.ProductSkuCode,
                    SkuShortTitle = string.Empty,
                    IsShortTitleChange = true
                });
            }
            if (model.IsRestoreDistributePrice)
            {
                // 清空分销价 对商家
                syncModel.UpdateDistributePriceModels.Add(new ProductSettlementPrice()
                {
                    ProductCode = relation.ProductCode,
                    ProductSkuCode = relation.ProductSkuCode,
                    SettlementType = SettlementType.Manufacturer.ToInt(),
                    ShopId = relation.ProductShopId,
                    CreateUser = fxUserId,
                    Price = 0
                });
            }

            if (model.IsRestoreSettlePrice)
            {
                // 清空结算价 对厂家
                syncModel.UpdateSettlePriceModels.Add(new ProductSettlementPrice()
                {
                    ProductCode = relation.ProductCode,
                    ProductSkuCode = relation.ProductSkuCode,
                    SettlementType = SettlementType.Merchant.ToInt(),
                    ShopId = relation.ProductShopId,
                    CreateUser = fxUserId,
                    Price = 0
                });
            }

            if (model.IsRestoreCostPrice)
            {
                // 清空成本价
                syncModel.UpdateCostPriceModels.Add(new ProductSettlementPrice()
                {
                    ProductCode = relation.ProductCode,
                    ProductSkuCode = relation.ProductSkuCode,
                    SettlementType = SettlementType.CostPrice.ToInt(),
                    ShopId = relation.ProductShopId,
                    CreateUser = fxUserId,
                    FxUserId = 0,
                    Price = 0
                });
            }

            if (model.IsRestoreSupplier)
            {
                // 平台商品设为自营
                bindModel = new BindSupplierRequestModel()
                {
                    productCodes = new List<string>() { relation.ProductCode },
                    skuCodes = new Dictionary<string, string>() { { relation.ProductCode, relation.ProductSkuCode } },
                    isSelf = true,
                    IsBindSku = true,
                    FxUserId = fxUserId
                };
            }

            return new Tuple<BindSupplierRequestModel, SyncPtSkuModel>(bindModel, syncModel);
        }

        /// <summary>
        /// 同步基础商品数据到平台商品
        /// </summary>
        /// <param name="model"></param>
        /// <param name="changeCodes"></param>
        /// <param name="isNeedCheck">是否需要校对</param>
        /// <param name="isNeedUseMemberLevel">是否使用等级分销价</param>
        public void SyncToPtSku(BaseSkuSyncToPtSkuModel model, ref List<string> changeCodes, bool isNeedCheck = false, bool isNeedUseMemberLevel = false)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var shopId = SiteContext.Current.CurrentShopId;
            var plat = SiteContext.Current.CurrentDbConfig.DbServer.Location;
            var supplierConfigs = model.BaseProductSkuSupplierConfig;
            if (supplierConfigs == null) model.IsUseSupplierFxUser = false;
            var supplierId = -1;
            model.IsAllUseWarehouse = true;//强制为true

            Log.Debug($"基础商品同步到平台商品，是否自动关联：{isNeedCheck}, 模型：{model.ToJson()}");

            var now = DateTime.Now;
            // 供货设置  
            if (model.IsUseSupplierFxUser == true)
            {
                var skuStrDict = model.SkuCodes.ToDictionary(s => s.Key, s => string.Join(",", s.Value));
                var bindModel = new BindSupplierRequestModel()
                {
                    productCodes = model.SkuCodes.Keys.ToList(),
                    skuCodes = skuStrDict,
                    IsBindSku = true,
                    from = "Menu"
                };

                if (supplierConfigs.Any(c => c.SupplierFxUserId == fxUserId))//自营
                {
                    bindModel.isSelf = true;
                    supplierId = 0;
                }
                else
                {
                    var configs = new List<BindConfigModel>();
                    foreach (var config in supplierConfigs)
                    {
                        configs.Add(new BindConfigModel()
                        {
                            SupplierId = config.SupplierFxUserId.ToInt(),
                            Config = config.Config,
                            ConfigType = config.ConfigType
                        });
                        supplierId = config.SupplierFxUserId.ToInt();
                    }
                    bindModel.Configs = configs;
                }

                try
                {
                    lock (_bindLock)
                    {
                        new ExceptionHandler().ExceptionRetryHandler(() => new ProductFxService().BindSupplier(bindModel), false);
                    }
                }
                catch (Exception e)
                {
                    Log.WriteError($"基础商品同步到平台商品，绑定厂家失败，模型：{model.ToJson()}，异常消息：{e.StackTrace}");
                }
            }

            var allPriceModels = new List<ProductSettlementPrice>();
            var useMemberPriceModels = new List<ProductSettlementPrice>();
            var saveShortTitleModels = new List<SaveShortTitleOrWeightModel>();
            var skuCodes = model.SkuCodes.SelectMany(sc => sc.Value).ToList();
            var productCodes = model.SkuCodes.Keys.ToList();
            // 获取sku上下游 sku没有自己上下游的 取商品的上下游
            var productFxService = new ProductFxService();
            var results = productFxService.GetSupplierAndAgentByAllCodes(productCodes, skuCodes, fxUserId, true);
            var supplierAgentDict = results.GroupBy(r => r.ProductRefCode).ToDictionary(d => d.Key, g => g.ToList());
            var infoDic = new Dictionary<string, List<ProductSettlementPrice>>();
            // 初始化变量
            var finalPriceRule = FinalDistributePriceCorrectRule.RoundToWholeNumber;
            var memberLevelDict = new Dictionary<int, RuleModel>();
            var changeSkuCodes = new List<string>();
            // 使用等级分销价
            if (isNeedUseMemberLevel)
            {
                // 获取所有的下游用户
                var agentIds = results.Select(r => r.UpFxUserId).Distinct().ToList();
                agentIds.Remove(0);
                // 先获取最终结算价修正规则
                finalPriceRule = MemberLevelService.FinalPriceRule(0, SiteContext.Current.CurrentShopId);
                // 获取对应分销商用户的换算规则
                memberLevelDict = new MemberLevelService().GetMemberLevelList(agentIds, fxUserId);
            }
            
            foreach (var pcode in model.SkuCodes.Keys)
            {
                var scodes = model.SkuCodes[pcode];
                changeSkuCodes = changeCodes;
                scodes.ForEach(scode =>
                {
                    if (infoDic.ContainsKey(scode) == false) infoDic[scode] = new List<ProductSettlementPrice>();
                    var suppliers = new List<int>();
                    var agent = 0;
                    productFxService.GetSkuSupplierAndAgentFromResult(supplierAgentDict, ref suppliers, ref agent, scode, pcode);
                    // 简称
                    if (model.IsUseShortTitle == true)
                    {
                        saveShortTitleModels.Add(new SaveShortTitleOrWeightModel()
                        {
                            ProductCode = pcode,
                            SkuCode = scode,
                            SkuShortTitle = model.ShortTitle,
                            IsShortTitleChange = true
                        });
                    }
                    // 成本价
                    if (model.IsUseCostPrice == true && model.CostPrice.HasValue)
                    {
                        var info = new ProductSettlementPrice
                        {
                            ProductCode = pcode,
                            ProductSkuCode = scode,
                            ShopId = shopId,

                            CreateUser = fxUserId,
                            FxUserId = 0,
                            SettlementType = SettlementType.CostPrice.ToInt(),
                            PlatformType = plat,
                            Price = model.CostPrice.ToDecimal(),

                            CreateTime = now,
                            UpdateTime = now,
                        };
                        allPriceModels.Add(info);
                        infoDic[scode].Add(info);
                    }
                    // 分销价
                    if (model.IsUseDistributePrice == true && agent>0 && model.DistributePrice.HasValue)
                    {
                        var info = new ProductSettlementPrice
                        {
                            ProductCode = pcode,
                            ProductSkuCode = scode,
                            ShopId = shopId,

                            CreateUser = fxUserId,
                            FxUserId = agent,
                            SettlementType = SettlementType.Manufacturer.ToInt(),
                            PlatformType = plat,
                            Price = model.DistributePrice.ToDecimal(),

                            CreateTime = now,
                            UpdateTime = now,
                        };

                        // 使用等级分销价
                        if (isNeedUseMemberLevel)
                        {
                            memberLevelDict.TryGetValue(agent, out var rule);
                            if (rule != null)
                            {
                                var (newPrice, isChanged) = MemberLevelService.DistributePriceChange(info.Price, rule, finalPriceRule);
                                if (isChanged)
                                {
                                    info.Price = newPrice ?? info.Price;
                                    info.IsMemberLevelPrice = true;
                                    useMemberPriceModels.Add(info);
                                    changeSkuCodes.Add(scode);
                                }
                            }
                        }
                        // 如果不是使用等级分销价，则直接添加
                        if (useMemberPriceModels.Contains(info) == false) allPriceModels.Add(info);
                        infoDic[scode].Add(info);
                    }
                    // 采购价/结算价
                    if (model.IsUseSettlePrice == true && model.SettlePrice.HasValue)
                    {
                        // 自营默认结算价格处理
                        if (suppliers == null || suppliers.Count == 0)
                        {
                            var info = new ProductSettlementPrice
                            {
                                ProductCode = pcode,
                                ProductSkuCode = scode,
                                ShopId = shopId,
                                CreateUser = fxUserId,
                                FxUserId = 0,
                                SettlementType = SettlementType.DefaultMerchant.ToInt(),
                                PlatformType = plat,
                                Price = model.SettlePrice.ToDecimal(),
                                CreateTime = now,
                                UpdateTime = now,
                            };
                            allPriceModels.Add(info);
                            infoDic[scode].Add(info);
                            Log.WriteError($"默认结算价格处理");
                        }
                        else
                        {
                            suppliers?.ForEach(suId =>
                            {
                                var info = new ProductSettlementPrice
                                {
                                    ProductCode = pcode,
                                    ProductSkuCode = scode,
                                    ShopId = shopId,

                                    CreateUser = fxUserId,
                                    FxUserId = suId,
                                    SettlementType = SettlementType.Merchant.ToInt(),
                                    PlatformType = plat,
                                    Price = model.SettlePrice.ToDecimal(),

                                    CreateTime = now,
                                    UpdateTime = now,
                                };
                                allPriceModels.Add(info);
                                infoDic[scode].Add(info);
                            });
                        }
                    }
                });
            }
            // 简称
            new ProductInfoFxService().SaveShortTitleOrWeight(fxUserId, saveShortTitleModels,false);

            // 结算价
            if (!allPriceModels.IsNullOrEmptyList())
            {
                new FinancialSettlementService().SetProductSettlementPrice(allPriceModels, fxUserId,
                                changeType: ChangeType.SyncBaseProduct.ToInt(),
                                needSetIdFromDb: true);
            }
            
            // 含会员等级分销价的结算价
            if (!useMemberPriceModels.IsNullOrEmptyList())
            {
                new FinancialSettlementService().SetProductSettlementPrice(useMemberPriceModels, fxUserId,
                                changeType: ChangeType.MemberCalculate.ToInt(),
                                needSetIdFromDb: true);
                changeCodes = changeSkuCodes;
            }

            // 库存更新
            if (model.IsAllUseWarehouse == true && model.WarehouseRelationList.IsNotNullOrEmpty())
            {
                var connectionString = SiteContext.Current.CurrentDbConfig.ConnectionString;
                var _wareHouseService = new WareHouseService(connectionString);
                var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService(false);
                var modelWarehouseRelationList = model.WarehouseRelationList;
                //var skuUidList = modelWarehouseRelationList.Select(x => x.SkuUid).ToList();
                var productSkuCodeList = modelWarehouseRelationList.Select(x => x.ProductSkuCode).ToList();

                var baseOfPtSkuRelationList = baseOfPtSkuRelationService.GetListBySkuCode(productSkuCodeList, fxUserId);
                //var baseOfPtSkuRelationList = baseOfPtSkuRelationService.GetListBySkuModel(skuUidList, productSkuCodeList, fxUserId);
                var ownCode = _wareHouseService.GetOwnerCode(fxUserId);

                Task.Run(() =>
                {
                    var msgList = new List<MessageRecord>();
                    var updateLIst = new List<BaseOfPtSkuRelation>();
                    modelWarehouseRelationList.ForEach(item =>
                    {
                        // 找到对应的关联数据
                        var baseOfPtSkuRelation = baseOfPtSkuRelationList.FirstOrDefault(x => x.BaseProductSkuUid == item.SkuUid && x.ProductSkuCode == item.ProductSkuCode);
                        if (baseOfPtSkuRelation == null) return;

                        var req = new WarehouseSkuBindRequest
                        {
                            IsRollback = false,
                            OwnerCode = ownCode,
                            PlatformSkuCode = baseOfPtSkuRelation.ProductSkuCode,
                            PlatformType = baseOfPtSkuRelation.ProductPlatformType,
                            ShopId = item.ShopId,
                            SkuCode = item.SkuCode
                        };
                        var warehouseSkuBindResponse = _wareHouseService.BindProduct(req, true, true);

                        if (warehouseSkuBindResponse.IsSucc)
                        {
                            baseOfPtSkuRelation.IsUseWarehouse = true;

                            msgList.Add(
                                new MessageRecord
                                {
                                    MsgType = BaseProductMsgType.SyncPtRelationStatus,
                                    FxUserId = fxUserId,
                                    TargetCloud = "Alibaba",
                                    BusinessId = baseOfPtSkuRelation.BaseProductSkuUid.ToString(),
                                    DataJson = baseOfPtSkuRelation.ToJson()
                                }
                            );

                            updateLIst.Add(baseOfPtSkuRelation);
                        }
                    });

                    new MessageRecordService().SendBusinessMessage(msgList);
                    new BaseOfPtSkuRelationService(false).BulkUpdate(updateLIst);
                });
            }

            if (isNeedCheck) CheckInfoSetting(infoDic, model.BaseProductUid, model.BaseProductSkuUid, model.FxUserId, supplierId, model.DbName);
        }

        /// <summary>
        /// 获取基础商品sku详情
        /// </summary>
        /// <param name="baseProductUid"></param>
        /// <param name="baseProductSkuUid"></param>
        /// <returns></returns>
        public BaseProductSku GetBaseProductSkuDetail(long baseProductUid, long baseProductSkuUid)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var _baseProductSkuService = new BaseProductSkuService();
            var baseProductSkuInfo = _baseProductSkuService.GetBaseProductSkuDetail(baseProductSkuUid, baseProductUid, fxUserId);
            var _service = new WareHouseService();
            var _supplierUserService = new SupplierUserService();

            if (baseProductSkuInfo != null)
            {
                if (baseProductSkuInfo.SkuCode != null)
                {
                    var skuCodes = new List<string> { baseProductSkuInfo.SkuCode };
                    var stocks = _service.GetStockBySkuCode(skuCodes);
                    var stock = stocks.FirstOrDefault(s => s.SkuCargoNumber == baseProductSkuInfo.SkuCode);
                    baseProductSkuInfo.StockCount = stock?.StockCount;
                }

                //if (baseProductSkuInfo.SkuSupplierUserId > 0)
                //{
                //    // 厂家信息
                //    var suppliers = _supplierUserService.GetSupplierList(fxUserId);
                //    var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");
                //    var supplierRes = supplierList.Where(t => t.Key == baseProductSkuInfo.SkuSupplierUserId).FirstOrDefault();
                //    baseProductSkuInfo.SupplierName = supplierRes.Value;
                //}
                // 规格关联数据
                var baseProductSkuRelations = new BaseOfPtSkuRelationService().GetListBySkuUids(new List<long> { baseProductSkuInfo.Uid }, fxUserId);
                var baseProductSkuRelationsValue = baseProductSkuRelations.SelectMany(a => a.Value).ToList();

                // 规格货盘关联数据
                var supplierProductSkuRelationService = new BaseOfSupplierSkuRelationService(fxUserId);
                var supplierProudctSkuRelations =
                    supplierProductSkuRelationService.GetListByProductSkuUid(new List<long> { baseProductSkuInfo.Uid }, fxUserId);

                var relations = baseProductSkuRelationsValue.Where(a => a.BaseProductSkuUid == baseProductSkuInfo.Uid).ToList();
                var relationSuppliers = supplierProudctSkuRelations.Where(a => a.BaseProductSkuUid == baseProductSkuInfo.Uid).ToList();
                baseProductSkuInfo.RelationCount = relations == null ? 0 : relations.Count;
                if (relationSuppliers.Count > 0)
                    baseProductSkuInfo.RelationCount += relationSuppliers.Count;


                // 厂家models
                var configService = new BaseProductSkuSupplierConfigService();
                //var baseProductSkuSupplierConfigs = configService
                //    .GetBaseProductSkuSupplierConfigs(baseProductSkuInfo.Uid, baseProductSkuInfo.BaseProductUid);
                var defaultConfigs = configService.GetListBySkus(new List<BaseProductSku> { baseProductSkuInfo });

                // 规格绑定厂家
                baseProductSkuInfo.BaseProductSkuSupplierConfigsModel = defaultConfigs;
                // 默认绑定厂家
                baseProductSkuInfo.BaseProductSkuSupplierConfigs = defaultConfigs;

                var config = defaultConfigs?.FirstOrDefault();

                //前端显示逻辑 SupplierFxUserId -1:自营、0：未设置、大于0：厂家
                baseProductSkuInfo.SupplierName = null;
                baseProductSkuInfo.SupplierFxUserId = 0;
                if (config!= null)
                {
                    if (config.SupplierFxUserId > 0)
                    {
                        // 厂家
                        if (config.SupplierFxUserId != fxUserId)
                        {
                            var suppliers = _supplierUserService.GetSupplierList(fxUserId,needEncryptAccount:true);
                            var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");
                            var supplierRes = supplierList.Where(t => t.Key == config.SupplierFxUserId).FirstOrDefault();
                            baseProductSkuInfo.SupplierName = supplierRes.Value;
                            baseProductSkuInfo.SupplierFxUserId = supplierRes.Key;
                        }
                        // 自营
                        else
                        {
                            baseProductSkuInfo.SupplierFxUserId = -1;
                            baseProductSkuInfo.SupplierName = null;
                        }
                    }
                }
                baseProductSkuInfo.ImageUrl = ImgHelper.ChangeImgUrl(baseProductSkuInfo.ImageUrl);
            }

            return baseProductSkuInfo;
        }

        /// <summary>
        /// 校对信息设置
        /// </summary>
        /// <param name="infoDic"></param>
        /// <param name="proUid"></param>
        /// <param name="skuUid"></param>
        /// <param name="fxUserId"></param>
        /// <param name="supplierId"></param>
        /// <param name="dbName"></param>
        public void CheckInfoSetting(Dictionary<string, List<ProductSettlementPrice>> infoDic, long proUid, long skuUid,
            int fxUserId, int supplierId, string dbName)
        {
            // 仅针对基础商品
            if (proUid == 0 || skuUid == 0) return;

            // 拿到List的所有Code
            var allCode = infoDic.SelectMany(x => x.Value).Select(x => x.UniqueKey).ToList();
            var allSkuCode = infoDic.Select(x => x.Key).ToList();
            // 查询数据
            var allPrice = new FinancialSettlementService().GetListByUniqueKeys(allCode);
            var ptProductSku = new ProductSkuFxService().GetProductSkuBySkuCode(allSkuCode);
            var skuCodeAndSupplierIds = new ProductFxRepository().GetSkuSupplierId(allSkuCode, fxUserId);
            var messages = new List<MessageRecord>();
            // 对比数据
            foreach (var key in infoDic.Keys)
            {
                var list = infoDic[key];
                var abnormalInfo = string.Empty;
                var find = skuCodeAndSupplierIds.FirstOrDefault(x => x.ProductRefCode == key);

                // 未找到对应的厂家信息或者厂家Id不匹配
                if (supplierId != -1 && (find == null || find.DownFxUserId != supplierId))
                {
                    abnormalInfo += "，供货方式";
                }

                foreach (var item in list)
                {
                    var price = allPrice.FirstOrDefault(x => x.UniqueKey == item.UniqueKey);
                    if (price == null) continue;

                    if (price.Price != item.Price)
                    {
                        switch (item.SettlementType)
                        {
                            case 1:
                            case 4: abnormalInfo += "，采购价"; break;
                            case 2: abnormalInfo += "，分销价"; break;
                            case 3: abnormalInfo += "，成本价"; break;
                        }
                    }
                }
                var proCode = list.FirstOrDefault(x => x.ProductCode != string.Empty)?.ProductCode;

                if (abnormalInfo != string.Empty)
                {
                    // 找到对应的店铺商品
                    var sku = ptProductSku.FirstOrDefault(x => x.SkuCode == key);
                    // 去除第一个逗号
                    abnormalInfo = abnormalInfo.Substring(1);

                    // 生成异常数据
                    var ent = new BaseProductAbnormal
                    {
                        AbnormalType = BaseProductAbnormalType.ChangePtProductInfo,
                        AbnormalReason = $"关联商品成功，但部分信息同步失败：{abnormalInfo}，请手动重试",
                        AbnormalSource = "Self",
                        BaseProductUid = proUid,
                        BaseProductSkuUid = skuUid,
                        Status = 0,
                        FxUserId = fxUserId,
                        ProductPtId = sku?.PlatformId,
                        ProductSkuPtId = sku?.SkuId,
                        ProductCode = proCode,
                        ProductSkuCode = key,
                        ProductShopId = sku?.ShopId ?? 0,
                        ProductFxUserId = sku?.SourceUserId ?? 0,
                        ProductPlatformType = sku?.PlatformType,
                        ProductDbName = dbName,
                        ProductCloudPlatform = CustomerConfig.CloudPlatformType,
                        CreateTime = DateTime.Now,
                        UpdateTime = DateTime.Now
                    };
                    messages.Add(new MessageRecord
                    {
                        CreateFxUserId = fxUserId,
                        CreateTime = DateTime.Now,
                        BusinessId = ent.ProductSkuCode,
                        TargetCloud = CloudPlatformType.Alibaba.ToString(),
                        ProductPlatformType = ent.ProductPlatformType,
                        MsgType = BaseProductMsgType.AddBaseProductAbnormal,
                        FxUserId = ent.FxUserId,
                        DataJson = ent.ToJson()
                    });
                }
            }

            // 发送消息
            if (CustomerConfig.IsLocalDbDebug)
                new MessageRecordService().AddBaseProductAbnormal(messages);
            else
                new MessageRecordService().SendBusinessMessage(messages);
        }

        /// <summary>
        /// 自动关联前数据准备
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public AutoMappingModel PreModelInfo(BaseProductAutoMappingModel model, int curFxUserId)
        {
            // 获取相关Sku信息
            var baseProductSkuService = new BaseProductSkuService();
            if (!model.NoCheckedSkuUids.IsNullOrEmptyList() && !model.NoCheckedProductUids.IsNullOrEmptyList())
                baseProductSkuService.FiltedBaseProductSku(model.SkuUids, curFxUserId, model.NoCheckedSkuUids, model.NoCheckedProductUids);

            var skuUidList = model.SkuUids.SelectMany(a => a.Value).ToList();
            var skuEntList = baseProductSkuService.GetListBySkuUids(skuUidList, curFxUserId);
            model.SkuCodeList = skuEntList.Select(x => x.SkuCode).ToList();
            model.SkuCodeDic = skuEntList.ToDictionary(x => x.SkuCode, x => x.Uid);
            model.IsAllUseWarehouse = true;//强制为true
            var newModel = new AutoMappingModel
            {
                BaseProductUid = model.BaseProductUid,
                BaseProductSkuUid = model.BaseProductSkuUid,
                Request = model.Request,
                SkuCode = model.SkuCode,
                IsAllUseWarehouse = model.IsAllUseWarehouse,
                IsUseDistributePrice = model.IsUseDistributePrice,
                IsUseSupplierFxUser = model.IsUseSupplierFxUser,
                IsUseSettlePrice = model.IsUseSettlePrice,
                IsUseSkuShortTitle = model.IsUseSkuShortTitle,
                IsUseCostPrice = model.IsUseCostPrice,
                SkuCodeList = model.SkuCodeList,
                SkuCodeDic = model.SkuCodeDic,
            };

            if (newModel.SkuCodeList.Count == 0) return newModel;

            model.Clear();

            // 查询所有的Sku信息，并获取所需信息
            var needSkuList = new List<AutoMappingSkuModel>();
            skuEntList.ForEach(sku =>
            {
                needSkuList.Add(new AutoMappingSkuModel
                {
                    Uid = sku.Uid,
                    SkuCode = sku.SkuCode,
                    BaseProductUid = sku.BaseProductUid,
                    ShortTitle = sku.ShortTitle,
                    CostPrice = sku.CostPrice,
                    DistributePrice = sku.DistributePrice,
                    SettlePrice = sku.SettlePrice,
                });
            });
            newModel.BaseProductSkus = needSkuList;

            // 获取供货厂家配置
            var baseProductSkuSupplierConfigs = new BaseProductSkuSupplierConfigService().GetListBySkus(skuEntList);
            newModel.SupplierConfigList = baseProductSkuSupplierConfigs;

            var taskService = new AsyncTaskService();
            var msgList = new List<MessageRecord>();
            var allTaskCode = string.Empty;
            newModel.TaskCodeDic = new Dictionary<string, string>();
            // 生成任务并发送消息
            for (var i = 1; i < 5; i++)
            {
                var platformType = (CloudPlatformType)i;
                var task = new AsyncTask
                {
                    TaskCode = CommUtls.GetGuidShortMd5(),
                    FxUserId = curFxUserId,
                    CData = newModel.ToJson(),
                    CloudPlatformType = platformType.ToString(),
                    Flag = "BaseProductAutoRelationBind",
                    TotalCount = newModel.SkuCodeList.Count,
                    CreateTime = DateTime.Now,
                };
                newModel.TaskCodeDic[platformType.ToString()] = task.TaskCode;
                allTaskCode += task.TaskCode + "|";

                msgList.Add(new MessageRecord
                {
                    MsgType = BaseProductMsgType.AutoRelationBindNew,
                    FxUserId = curFxUserId,
                    TargetCloud = platformType.ToString(),
                    BusinessId = task.TaskCode,
                    DataJson = newModel.ToJson()
                });
                taskService.AddAsyncTask(task);
            }

            // Redis任务进度标记
            var redisProcessKey =
                CacheKeys.BaseProductAutoRelationTaskProgressKey.Replace("{FxUserId}", curFxUserId.ToString());
            var taskViewModel = new AutoRelationTaskViewModel
            {
                TaskCode = allTaskCode,
                SuccessCount = 0,
                FailCount = 0,
                Process = 0,
                CreateTime = DateTime.Now
            };
            RedisHelper.Set(redisProcessKey, taskViewModel.ToJson(), 60 * 60 * 2);

            new MessageRecordService().SendBusinessMessage(msgList);
            return newModel;
        }

        /// <summary>
        /// 获取商品SKU信息
        /// </summary>
        /// <param name="req"></param>
        public List<BaseProductSkuSimpleRes> GetBaseProductSkuDetail(BaseProductSkuSimpleReq req)
        {
            // Sku
            var baseProductSkuList = new BaseProductSkuRepository().GetByUid(req.SkuUId).Where(x => x.FxUserId == req.FxUserId).ToList();
            if (!req.IsContainDelete)
            {
                baseProductSkuList = baseProductSkuList.Where(x => x.Status == 1).ToList();
            }
            return TranBaseProductSkuSimpleFromSku(baseProductSkuList, req.IsContainDelete);
        }

        /// <summary>
        /// 获取商品SKU信息
        /// </summary>
        /// <param name="req"></param>
        public List<BaseProductSkuSimpleRes> GetBaseProductSkuSimpleByCondition(BpSkuSimpleConditionReq req)
        {
            // Sku
            var baseProductSkuList = new BaseProductSkuRepository().GetBaseProductSkuSimpleByCondition(req).ToList();
            return TranBaseProductSkuSimpleFromSku(baseProductSkuList, req.IsContainDelete);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="baseProductSkuList"></param>
        /// <param name="isContainDelete">是否包含已删除的数据</param>
        /// <returns></returns>
        private List<BaseProductSkuSimpleRes> TranBaseProductSkuSimpleFromSku(List<BaseProductSku> baseProductSkuList, bool isContainDelete = false)
        {
            List<BaseProductSkuSimpleRes> resList = new List<BaseProductSkuSimpleRes>();
            // Spu
            var proUid = baseProductSkuList.Select(x => x.BaseProductUid).ToList();
            List<BaseProductEntity> baseProductList = null;
            List<BaseProductImage> baseImgList = null;
            if (isContainDelete)
            {
                baseProductList = new BaseProductRepository().GetListByUids(proUid).ToList();
                // 商品图片
                baseImgList = new BaseProductImageRepository().GetListByProductUid(proUid).ToList();
            }
            else
            {
                baseProductList = new BaseProductRepository().GetListByUids(proUid).Where(x => x.Status == 1).ToList();
                // 商品图片
                baseImgList = new BaseProductImageRepository().GetListByProductUid(proUid).Where(x => x.Status == 1).ToList();
            }

            foreach (var baseProductSku in baseProductSkuList)
            {
                var res = new BaseProductSkuSimpleRes();
                var baseProduct = baseProductList.FirstOrDefault(x => x.Uid == baseProductSku.BaseProductUid);
                if (baseProduct == null) continue;
                var baseProductImage = baseImgList.Where(x => x.ProductUid == baseProduct.Uid).ToList();

                res.ProductSkuImgUrl = baseProductSku.ImageUrl;
                res.ProductImgUrl = baseProductImage.FirstOrDefault(x => x.ImageObjectId == baseProduct.MainImageObjectId)?.FullUrl ?? ""; // 商品主图
                res.ProductSkuImgUrl = ImgHelper.ChangeImgUrl(res.ProductSkuImgUrl);
                res.ProductImgUrl = ImgHelper.ChangeImgUrl(res.ProductImgUrl);
                res.ProductSkuId = baseProductSku.Uid; // 商品Id
                res.ProductSkuCode = baseProductSku.SkuCode; // 商品编码
                res.SkuShortTitle = baseProductSku.ShortTitle; // sku简称

                res.ProductSubject = baseProduct.Subject; // 商品名称
                res.ShortTitle = baseProduct.ShortTitle; // spu简称
                res.SpuCode = baseProduct.SpuCode;
                res.SpuUid = baseProduct.Uid;

                var attrs = baseProductSku.Attributes.ToObject<List<SkuAttr>>();
                if (attrs != null)
                {
                    res.ProductSpecs = string.Join(",", attrs.Select(p => p.v)); // 商品规格
                }

                resList.Add(res);
            }

            return resList;
        }

        /// <summary>
        /// 获取基础商品Sku信息-简版
        /// </summary>
        /// <param name="baseProductSkuIds"></param>
        /// <returns></returns>
        public Dictionary<long, BaseProductSkuSimpleRes> GetSkuListBySkuIds(List<long> baseProductSkuIds)
        {
            List<BaseProductSkuSimpleRes> baseProductSkuSimpleRes;

            if (baseProductSkuIds.Count == 0)
                return new Dictionary<long, BaseProductSkuSimpleRes>();

            try
            {

                var fxUserId = SiteContext.Current.CurrentFxUserId;

                // 获取基础商品sku信息
                var req = new BaseProductSkuSimpleReq(baseProductSkuIds, fxUserId);
                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                {
                    baseProductSkuSimpleRes = GetBaseProductSkuDetail(req);
                }
                else
                {
                    // 跨云获取数据
                    var isPdd = CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString();
                    var targetSiteUrl = isPdd ? CustomerConfig.AlibabaMessageDomainForPdd : CustomerConfig.AlibabaFenFaSystemUrl;
                    const string apiUrl = "/BaseProductApi/GetBaseProductSkuSimple";
                    targetSiteUrl = targetSiteUrl.TrimEnd("/") + apiUrl;

                    // 获取基础商品sku信息
                    Log.Debug($"跨云查询sku信息，地址={targetSiteUrl}，入参={req.ToJson()}");
                    baseProductSkuSimpleRes = WebCommon.PostFxSiteApi<BaseProductSkuSimpleReq, List<BaseProductSkuSimpleRes>>(targetSiteUrl, fxUserId, req, "跨云查询基础商品SKU", isEncrypt: true);
                }

                var baseProductSkuDic = new Dictionary<long, BaseProductSkuSimpleRes>();
                baseProductSkuSimpleRes?.ForEach(item =>
                {
                    if (!baseProductSkuDic.TryGetValue(item.ProductSkuId, out var v))
                    {
                        baseProductSkuDic.Add(item.ProductSkuId, item);
                    }
                });
                return baseProductSkuDic;
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取基础产品sku信息时出错(GetSkuListBySkuIds): {ex.Message} 堆栈：{ex.StackTrace}");
                return new Dictionary<long, BaseProductSkuSimpleRes>();
            }
        }

        /// <summary>
        /// 获取商品和Sku信息
        /// </summary>
        /// <param name="skuUids"></param>
        /// <returns></returns>
        public List<BaseProductEntity> GetBaseProductInfo(List<long> skuUids)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            // 获取Sku信息
            var listBySkuUids = new BaseProductSkuService().GetListBySkuUids(skuUids, fxUserId);
            // 获取SpuUids
            var spuUids = listBySkuUids.Select(x => x.BaseProductUid).Distinct().ToList();
            // 获取Spu信息
            var baseProductList = new BaseProductEntityService().GetListByUids(spuUids, fxUserId);

            // 组装数据
            listBySkuUids.ForEach(sku =>
            {
                var baseProduct = baseProductList.FirstOrDefault(x => x.Uid == sku.BaseProductUid);
                if (baseProduct == null) return;
                if (baseProduct.Skus == null) baseProduct.Skus = new List<BaseProductSku>();

                baseProduct.Skus.Add(sku);
            });

            return baseProductList;
        }

        /// <summary>
        /// 获取基础商品SKU列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Tuple<int, List<BaseProductSku>> LoadBaseProductSkuList(BaseProductSearchModel model)
        {
            // 查询数据
            var (listCount, list) = new BaseProductSkuService().GetBaseProductList(model, model.IsSku);
            // 数据组装
            if (list.Count > 0)
            {
                // 厂家数据
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var suppliers = new SupplierUserService().GetSupplierList(fxUserId);
                var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");
                var skus = list.SelectMany(a => a.Skus).ToList();
                // 商品资料信息
                var baseProductUid = list.Select(a => a.Uid).Distinct().ToList();
                // 绑定信息
                var supplierConfigs = new BaseProductSkuSupplierConfigService().GetAllListByProductUids(baseProductUid);

                foreach (var sku in skus)
                {
                    var skuConfig = supplierConfigs.FirstOrDefault(c => c.RefUid == sku.Uid && c.RefType == "Sku");
                    var proConfig = supplierConfigs.FirstOrDefault(c => c.RefUid == sku.BaseProductUid && c.RefType == "Product");

                    // 绑定信息采用方式
                    sku.SkuSupplierUserId = 0;
                    if (proConfig != null) sku.SkuSupplierUserId = proConfig.SupplierFxUserId;
                    if (skuConfig != null) sku.SkuSupplierUserId = skuConfig.SupplierFxUserId;
                    if (supplierList != null)
                    {
                        var supplierRes = supplierList.FirstOrDefault(t => t.Key == sku.SkuSupplierUserId);
                        sku.SupplierName = supplierRes.Value;
                    }

                    sku.ImageUrl = ImgHelper.ChangeImgUrl(sku.ImageUrl);
                }
            }

            var data = list.SelectMany(x => x.Skus).ToList();
            return Tuple.Create(listCount, data);
        }

        /// <summary>
        /// 更新关联关系到各业务库
        /// </summary>
        /// <param name="relations"></param>
        /// <param name="fxUserId"></param>
        public void SyncRelationInfo(List<BaseOfPtSkuRelation> relations, int fxUserId)
        {
            if (relations.IsNullOrEmptyList()) return;

            var syncRelationMsgList = new List<MessageRecord>();
            // 获取所有商品所属用户Id
            var productFxUserIds = relations
                .Where(x => x.ProductFxUserId != 0)
                .Select(x => x.ProductFxUserId)
                .Distinct().ToList();
            productFxUserIds.Add(fxUserId);
            // 获取所有商品所属用户Db配置，仅阿里平台
            var dbConfigs = new DbConfigRepository()
                .GetListByFxUserIds(productFxUserIds)
                .OrderByDescending(x => x.FromFxDbConfig)
                .ToList();
            // 根据所属商品用户分组
            var relationGroup = relations
                .GroupBy(x => x.ProductFxUserId)
                .ToDictionary(x => x.Key, x => x.ToList());

            var dicRelation = new Dictionary<string, List<BaseOfPtSkuRelation>>();
            var coldDicRelation = new Dictionary<string, DbConfigModel>();

            foreach (var group in relationGroup)
            {
                var userId = group.Key;
                var dbConfig = dbConfigs.FirstOrDefault(x => x.DbConfig.UserId == userId);
                var relationList = group.Value;

                relationList.ForEach(x =>
                {
                    var msgModel = new MessageRecord
                    {
                        MsgType = BaseProductMsgType.SyncRelationFromEdit,
                        FxUserId = fxUserId,
                        TargetCloud = x.CloudPlatform,
                        BusinessId = x.Id.ToString(),
                        DataJson = x.ToJson()
                    };
                    // 如果是阿里精选平台，则直接使用服务更新
                    if (x.CloudPlatform == CloudPlatformType.Alibaba.ToString() && dbConfig != null)
                    {
                        // 如果目标库是新库，则找自己的库执行
                        if (dbConfig.FromFxDbConfig == 1 || dbConfig.FromFxDbConfig == -1)
                        {
                            dbConfig = dbConfigs.FirstOrDefault(db => db.DbConfig.UserId == fxUserId);
                            Log.Debug($"更新关联更新时，查找自己的库：{dbConfig?.ToJson()}");
                            if (dbConfig == null) return;
                        }

                        Log.Debug($"关联数据：{x.ToJson()}，Db配置：{dbConfig.ToJson()}");
                        dicRelation.TryGetValue(dbConfig.ConnectionString, out var list);
                        if (list == null)
                        {
                            list = new List<BaseOfPtSkuRelation> { x };
                            dicRelation[dbConfig.ConnectionString] = list;
                            // 用于后面更新检索冷库的信息
                            coldDicRelation[dbConfig.ColdDbConnectionString]=dbConfig;
                        }
                        else
                        {
                            list.Add(x);
                        }

                        msgModel.DbName = dbConfig.DbNameConfig.DbName;
                    }
                    syncRelationMsgList.Add(msgModel);
                });
            }
            if (syncRelationMsgList.Any()) new MessageRecordService().SendBusinessMessage(syncRelationMsgList);

            // 更新当前库关联关系
            new BaseOfPtSkuRelationService().UpdateOrDelete(relations);
            // 更新到冷库
            var userDbConfig = SiteContext.Current.CurrentDbConfig;
            var enableColdGlobal = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
            if (enableColdGlobal&&userDbConfig!=null&&userDbConfig.EnableColdDb)
            {
                new BaseOfPtSkuRelationService(userDbConfig.ColdDbConnectionString).UpdateOrDelete(relations);
            }
            // 更新阿里各业务库
            if (!dicRelation.Any()) return;
            foreach (var item in dicRelation)
            {
                new BaseOfPtSkuRelationService(item.Key).UpdateOrDelete(item.Value);
                // 更新到冷库
                if (enableColdGlobal&&coldDicRelation.ContainsKey(item.Key))
                {
                    var dbConfig = coldDicRelation[item.Key];
                    if (dbConfig!=null&&dbConfig.EnableColdDb)
                    {
                        new BaseOfPtSkuRelationService(dbConfig.ColdDbConnectionString).UpdateOrDelete(item.Value);
                    }
                }
            }
        }

        /// <summary>
        /// 基于库存商品合并更新基础商品，仅初始化工具使用
        /// </summary>
        /// <param name="curWarehouseProducts"></param>
        /// <param name="curBaseProducts"></param>
        /// <param name="curFxUserId"></param>
        /// <param name="wareHouseDb"></param>
        /// <param name="curDbConn"></param>
        public void MergeBaseProduct(List<WareHouseProduct> curWarehouseProducts,
            List<BaseProductEntity> curBaseProducts, int curFxUserId, IDbConnection wareHouseDb, string curDbConn)
        {
            // sku维度处理
            var warehouseSkus = curWarehouseProducts.Where(x => x.Skus != null).SelectMany(x => x.Skus).AsList();
            var baseProductSkus = curBaseProducts.Where(x => x.Skus != null).SelectMany(x => x.Skus).AsList();
            var baseRelationList = baseProductSkus.Where(x => x.RelationList != null).SelectMany(x => x.RelationList).AsList();
            var warehouseRelationList = warehouseSkus.Where(x => x.RelationList != null).SelectMany(x => x.RelationList).AsList();

            // 准备变量
            var insertBaseProducts = new ConcurrentBag<BaseProductEntity>();
            var updateBaseProducts = new ConcurrentBag<BaseProductEntity>();
            var updateBaseProductSkus = new ConcurrentBag<BaseProductSku>();
            var insertBaseProductSkus = new ConcurrentBag<BaseProductSku>();
            var insertBaseRelationList = new ConcurrentBag<BaseOfPtSkuRelation>();
            var updateBaseRelationList = new ConcurrentBag<BaseOfPtSkuRelation>();
            var ossService = new OssObjectService();
            const int pageSize = 500;

            // 获取该用户的所有规格属性
            var existSkuAttrList = new BaseProductSkuAttributeService(curFxUserId)
                .GetList(baseProductSkus.Select(x => x.Uid).ToList(), whereFieldName: "SkuUid");
            // 获取主图相关信息
            var proUids = curBaseProducts.Select(x => x.Uid).AsList();
            var existProImgList = new BaseProductImageRepository(curFxUserId).GetListByProductUid(proUids).AsList();

            // 获取总数量的Uid
            var sum = curWarehouseProducts.Count + warehouseSkus.Count;
            var uniqueIdList = new ProductDbConfigRepository().BaseProductSystemUniqueId("", curFxUserId, sum);
            var index = 0;

            // 获取商品数据
            var shopIds = warehouseRelationList.Select(x => x.ShopId).Distinct().ToList();
            var idByShopId = new FxUserShopService().GetUserIdByShopId(shopIds);
            var fxUserIds = idByShopId.Select(x => x.FxUserId).Distinct().ToList();
            fxUserIds.Add(curFxUserId);
            var cloudPlatformTypeList = new List<string> { "TouTiao", "Pinduoduo", "Jingdong", "Alibaba" };
            var dbConfigList = new DbConfigRepository().GetListByFxUserIds(fxUserIds, cloudPlatformTypeList);
            if (dbConfigList == null || dbConfigList.Count == 0) return;
            // 按照数据库配置新库的优先级排序
            dbConfigList = dbConfigList.OrderByDescending(x => x.FromFxDbConfig).ToList();

            var platformSkuDic = new Dictionary<string, ProductSkuFx>();
            var allPlatformSkuCodes = warehouseRelationList.Select(x => x.PlatformSkuCode).Distinct().ToList();

            // 提前获取所有平台的商品Sku信息
            dbConfigList.ForEach(dbConfig =>
            {
                // 获取当前平台
                var platform = dbConfig.DbConfig.DbCloudPlatform;
                // 获取该平台的关联关系的skuCode
                var skuCodes = warehouseRelationList
                    .Where(x => CommUtls.GetPlatformCloudByType(x.PlatformType).Equals(platform, StringComparison.OrdinalIgnoreCase))
                    .Select(x => x.PlatformSkuCode)
                    .Distinct().ToList();

                // Log.Debug(() => $"第一次，获取商品SkuCodes信息：{skuCodes.ToJson()}", "MergeBaseProductRelation.txt");
                if (skuCodes.Count == 0) skuCodes = allPlatformSkuCodes;

                // Log.Debug(() => $"获取商品SkuCodes信息：{skuCodes.ToJson()}", "MergeBaseProductRelation.txt");
                // Log.Debug(() => $"获取商品Sku信息，数据库信息：{dbConfig.ToJson()}", "MergeBaseProductRelation.txt");

                if (skuCodes.Count == 0) return;

                // 超过500，分页处理
                var page = skuCodes.Count / pageSize + 1;
                for (var i = 0; i < page; i++)
                {
                    var codes = skuCodes.Skip(i * pageSize).Take(pageSize).ToList();
                    var productSku = new ProductSkuFxRepository(dbConfig.ConnectionString).GetBySkuCodes(codes, dbConfig);
                    if (productSku.IsNullOrEmptyList()) continue;
                    productSku.ForEach(x =>
                    {
                        // 不存在则添加
                        if (!platformSkuDic.ContainsKey(x.SkuCode))
                        {
                            platformSkuDic.Add(x.SkuCode, x);
                        }
                    });
                }
            });

            // Log.Debug(() => $"获取商品Sku总信息Dic：{platformSkuDic.ToJson()}", "MergeBaseProductRelation.txt");

            // 按商品维度处理
            // 可并发处理
            Parallel.ForEach(curWarehouseProducts, new ParallelOptions { MaxDegreeOfParallelism = 10 }, pro =>
            {
                var baseProduct = curBaseProducts?.FirstOrDefault(x => x.SpuCode == pro.CargoNumber);
                var img = existProImgList?.FirstOrDefault(x => x.ImageObjectId == baseProduct?.MainImageObjectId);
                var basePro = GetByWarehousePro(pro, index++, uniqueIdList, ossService, curFxUserId, baseProduct, img);
                if (baseProduct == null) insertBaseProducts.Add(basePro);
                else updateBaseProducts.Add(basePro);
                var dtNow = DateTime.Now;

                // 处理Sku
                var skus = pro.Skus;
                if (skus.IsNullOrEmptyList()) return;

                skus.ForEach(sku =>
                {
                    if (sku.SkuCargoNumber.IsNullOrEmpty()) return;
                    // 获取基础商品对应的sku
                    var baseSku = baseProductSkus.FirstOrDefault(x => x.SkuCode == sku.SkuCargoNumber);
                    var baseProductSku = GetByWarehouseSku(sku, existSkuAttrList, index++, uniqueIdList, curFxUserId, ossService, basePro, baseSku);

                    // 如果为空则新增，否则更新
                    if (baseSku == null) insertBaseProductSkus.Add(baseProductSku);
                    else updateBaseProductSkus.Add(baseProductSku);

                    // 处理关联关系
                    var relationByWarehouseList = sku.RelationList;
                    relationByWarehouseList.ForEach(relation =>
                    {
                        // 找到对应的基础商品关联关系
                        var baseOfPtSkuRelation = baseRelationList.FirstOrDefault(x => x.ProductSkuCode == relation.PlatformSkuCode);

                        // 获取当前店铺的用户
                        var userId = idByShopId.FirstOrDefault(x => x.ShopId == relation.ShopId)?.FxUserId ?? 0;
                        if (userId == 0) return;
                        // 获取当前商品的云平台类型
                        var cloudPlatform = CommUtls.GetPlatformCloudByType(relation.PlatformType);
                        // 获取当前商品所在店铺的用户数据库配置
                        var dbConfig = dbConfigList.FirstOrDefault(x => x.DbConfig.UserId == userId && x.DbConfig.DbCloudPlatform.Equals(cloudPlatform, StringComparison.OrdinalIgnoreCase));

                        platformSkuDic.TryGetValue(relation.PlatformSkuCode, out var productSku);
                        if (productSku == null || dbConfig == null) return;

                        if (baseOfPtSkuRelation != null)
                        {
                            // 判断是否同个Sku
                            if (baseOfPtSkuRelation.BaseProductSkuUid == baseProductSku.Uid) return;

                            baseOfPtSkuRelation.BaseProductSkuUid = baseProductSku.Uid;
                            baseOfPtSkuRelation.IsUseWarehouse = true;
                            baseOfPtSkuRelation.DbConfig = GetDbConfigModel(cloudPlatform, curFxUserId, dbConfigList, dbConfig);
                            baseOfPtSkuRelation.UpdateTime = DateTime.Now;
                            baseOfPtSkuRelation.BaseProductSubject = basePro.Subject;
                            baseOfPtSkuRelation.BaseProductShortTitle = basePro.ShortTitle;
                            baseOfPtSkuRelation.BaseProductSpuCode = basePro.SpuCode;
                            baseOfPtSkuRelation.BaseProductSkuCode = baseProductSku.SkuCode;
                            baseOfPtSkuRelation.BaseProductSkuShortTitle = baseProductSku.ShortTitle;
                            baseOfPtSkuRelation.Status = 1;
                            updateBaseRelationList.Add(baseOfPtSkuRelation);
                        }
                        else
                        {
                            // 新增
                            var ent = new BaseOfPtSkuRelation
                            {
                                BaseProductUid = basePro.Uid,
                                BaseProductSkuUid = baseProductSku.Uid,
                                FxUserId = curFxUserId,
                                ProductCode = productSku.ProductCode,
                                ProductFxUserId = userId,
                                ProductPlatformType = relation.PlatformType,
                                ProductSkuCode = productSku.SkuCode,
                                ProductPtId = productSku.PlatformId,
                                ProductShopId = relation.ShopId,
                                ProductSkuPtId = productSku.SkuId,
                                IsUseWarehouse = false,
                                CreateTime = dtNow,
                                UpdateTime = dtNow,
                                BaseProductSubject = basePro.Subject,
                                BaseProductShortTitle = basePro.ShortTitle,
                                BaseProductSpuCode = basePro.SpuCode,
                                BaseProductSkuCode = baseProductSku.SkuCode,
                                BaseProductSkuShortTitle = baseProductSku.ShortTitle,
                                DbConfig = GetDbConfigModel(cloudPlatform, curFxUserId, dbConfigList, dbConfig),
                                Status = 1,
                                Type = 1,
                                CloudPlatform = cloudPlatform
                            };
                            insertBaseRelationList.Add(ent);
                        }
                    });
                });
            });

            // 处理数据
            // 获取需更新图片的库存商品以及Sku
            var updateWareHouseProducts = curWarehouseProducts.Where(x => x.ImgUrlChange).ToList();
            var updateWareHouseSkus = warehouseSkus.Where(x => x.ImgUrlChange).ToList();
            // 通过SQL直接更新
            var updateWarehouseProSql = new List<string>();
            var updateWarehouseSkuSql = new List<string>();
            // 遍历组装SQL
            updateWareHouseProducts.ForEach(x =>
            {
                updateWarehouseProSql.Add($"UPDATE WareHouseProduct SET ImageUrl = '{x.ImageUrl}' WHERE Id = {x.Id};");
            });
            updateWareHouseSkus.ForEach(x =>
            {
                updateWarehouseSkuSql.Add($"UPDATE WareHouseSku SET ImageUrl = '{x.ImageUrl}' WHERE Id = {x.Id};");
            });
            // 更新库存相关
            if (updateWarehouseProSql.Any())
            {
                // 大于500，分页处理
                var page = updateWarehouseProSql.Count / pageSize + 1;
                for (var i = 0; i < page; i++)
                {
                    var tempSql = updateWarehouseProSql.Skip(i * pageSize).Take(pageSize).ToList();
                    wareHouseDb.Execute(string.Join("", tempSql));
                }
            }
            if (updateWarehouseSkuSql.Any())
            {
                // 大于500，分页处理
                var page = updateWarehouseSkuSql.Count / pageSize + 1;
                for (var i = 0; i < page; i++)
                {
                    var tempSql = updateWarehouseSkuSql.Skip(i * pageSize).Take(pageSize).ToList();
                    wareHouseDb.Execute(string.Join("", tempSql));
                }
            }

            #region 更新实体

            // 更新商品信息
            if (insertBaseProducts.Any()) new BaseProductEntityService(curFxUserId).BulkInsert(insertBaseProducts.ToList());
            if (updateBaseProducts.Any()) new BaseProductRepository(curFxUserId).BulkUpdate(updateBaseProducts.ToList());
            // 更新sku信息
            if (insertBaseProductSkus.Any()) new BaseProductSkuService(curFxUserId).BulkInsert(insertBaseProductSkus.ToList());
            if (updateBaseProductSkus.Any()) new BaseProductSkuRepository(curFxUserId).BulkUpdate(updateBaseProductSkus.ToList());
            // 插入主图信息
            var insertBaseProductImages = insertBaseProducts.Where(x => x.MainImg != null).Select(x => x.MainImg).ToList();
            insertBaseProductImages.AddRange(updateBaseProducts.Where(x => x.MainImg != null).Select(x => x.MainImg).ToList());
            if (insertBaseProductImages.Any()) new BaseProductImageRepository(curFxUserId).BulkInsert(insertBaseProductImages);
            // 更新规格属性
            var insertSkuAttrs = insertBaseProductSkus.Select(x => x.AttributeModel).ToList();
            var updateSkuAttrs = updateBaseProductSkus.Select(x => x.AttributeModel).ToList();
            if (insertSkuAttrs.Any()) new BaseProductSkuAttributeService(curFxUserId).BulkInsert(insertSkuAttrs);
            if (updateSkuAttrs.Any()) new BaseProductSkuAttributeService(curFxUserId).BulkUpdate(updateSkuAttrs);
            // 更新关联关系
            if (insertBaseRelationList.Any() || updateBaseRelationList.Any())
            {
                var updateFieldNames = new List<string>
                {
                    "ProductSkuPtId", "ProductPtId", "ProductCode", "ProductShopId", "ProductFxUserId",
                    "IsUseWarehouse", "UpdateTime", "Status", "BaseProductSubject", "BaseProductShortTitle",
                    "BaseProductSkuSubject", "BaseProductSkuShortTitle",
                    "BaseProductSpuCode", "BaseProductSkuCode"
                };
                var tempList = insertBaseRelationList.ToList();
                tempList.AddRange(updateBaseRelationList.ToList());
                // 更新业务库，需要处理冷热库
                new BaseOfPtSkuRelationService(curFxUserId).ChoseMergeToBusinessDb(tempList.ToList(), updateFieldNames);

                // 更新基础库
                new BaseOfPtSkuRelationService(curDbConn, true).Merger(tempList, true, updateFieldNames);
                
                new BaseOfPtSkuRelationRepository(curDbConn).BulkUpdate(updateBaseRelationList.ToList(), updateFieldNames);
                // new BaseOfPtSkuRelationRepository(curDbConn).BulkUpdate(insertBaseRelationList, updateFieldNames);
                //处理冷库
                var enableColdGlobal= DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
                if (enableColdGlobal)
                {
                    var dbConfig = new DbConfigService().GetFxDbConfigFxUserIds(new List<int> { curFxUserId }, new List<string> { CustomerConfig.CloudPlatformType }).Where(d => d.FromFxDbConfig==1).FirstOrDefault();
                    if (dbConfig!=null&&dbConfig.EnableColdDb)
                    {
                        new BaseOfPtSkuRelationRepository(dbConfig.ColdDbConnectionString).BulkUpdate(updateBaseRelationList.ToList(), updateFieldNames);
                    }
                }
            }

            #endregion
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="wareHouseProduct"></param>
        /// <param name="index"></param>
        /// <param name="uniqueIdList"></param>
        /// <param name="ossService"></param>
        /// <param name="curFxUserId"></param>
        /// <param name="baseProduct"></param>
        /// <param name="baseImg"></param>
        /// <returns></returns>
        public BaseProductEntity GetByWarehousePro(WareHouseProduct wareHouseProduct, int index,
            List<string> uniqueIdList, OssObjectService ossService, int curFxUserId,
            BaseProductEntity baseProduct = null, BaseProductImage baseImg = null)
        {
            var basePro = baseProduct ?? new BaseProductEntity();
            var dtNow = DateTime.Now;

            var uid = ("1" + uniqueIdList[index]).ToLong();
            basePro.Uid = baseProduct?.Uid ?? uid;
            // 更新商品信息
            basePro.Subject = wareHouseProduct.Name;
            basePro.AttributeNames = "无规格";
            basePro.CreateTime = baseProduct?.CreateTime ?? dtNow;
            basePro.UpdateTime = dtNow;
            basePro.SkuModeType = 1;
            basePro.FxUserId = curFxUserId;
            basePro.SpuCode = wareHouseProduct.CargoNumber;
            basePro.CreateFrom = baseProduct?.CreateFrom ?? "Warehouse";
            basePro.WareHouseProductCode = wareHouseProduct.WareHouseProductCode;

            // 处理图片
            // 库存图片为空，不处理，不为空且不等于基础商品主图，则处理
            if (!string.IsNullOrEmpty(wareHouseProduct.ImageUrl) && (baseImg == null || baseImg.FullUrl != wareHouseProduct.ImageUrl))
            {
                var ossEnt = ossService.ImgUrlToOssObject(wareHouseProduct.ImageUrl, isInit: true, curFxUserId: curFxUserId);
                if (ossEnt != null)
                {
                    var pathStr = ImgHelper.SplitImageUrl(ossEnt.Url);
                    var pathModel = pathStr.ToObject<BaseProductImageModel>();
                    if (pathModel == null) return basePro;
                    
                    var img = BaseProductEntityService
                        .GetAddImagesFromModel(new List<BaseProductImageModel> { pathModel }, basePro.Uid)
                        .FirstOrDefault();

                    if (img == null) return basePro;
                        
                    basePro.MainImg = img;
                    basePro.MainImageObjectId = img.ImageObjectId;
                    wareHouseProduct.ImageUrl = ossEnt.Url;
                    wareHouseProduct.ImgUrlChange = true;
                }
            }

            return basePro;
        }

        /// <summary>
        /// 根据库存Sku转换为基础商品SKu
        /// </summary>
        /// <param name="wareHouseSku"></param>
        /// <param name="existProductSkuAttributeList"></param>
        /// <param name="index"></param>
        /// <param name="uniqueIdList"></param>
        /// <param name="curFxUserId"></param>
        /// <param name="ossService"></param>
        /// <param name="baseProductEntity"></param>
        /// <param name="baseProductSku"></param>
        /// <returns></returns>
        public BaseProductSku GetByWarehouseSku(WareHouseSku wareHouseSku,
            List<BaseProductSkuAttribute> existProductSkuAttributeList, int index, List<string> uniqueIdList,
            int curFxUserId,
            OssObjectService ossService, BaseProductEntity baseProductEntity,
            BaseProductSku baseProductSku = null)
        {
            var attrJson = new List<Dictionary<string, string>>
                { new Dictionary<string, string> { { "k", "无规格" }, { "v", wareHouseSku.SkuProperty } } }.ToJsonExt();

            var baseSku = baseProductSku ?? new BaseProductSku();
            var dtNow = DateTime.Now;
            // 获取规格属性
            var existAttr = existProductSkuAttributeList.FirstOrDefault(x => x.SkuUid == baseSku.Uid);

            // 如果为空则新增
            var uid = ("2" + uniqueIdList[index]).ToLong();
            baseSku.Uid = baseProductSku?.Uid ?? uid;
            baseSku.ProSubject = baseProductSku?.ProSubject ?? baseProductEntity.Subject;
            baseSku.BaseProductUid = baseProductEntity.Uid;
            baseSku.FxUserId = baseProductEntity.FxUserId;
            baseSku.SkuCode = wareHouseSku.SkuCargoNumber;
            baseSku.IsCombineSku = wareHouseSku.ItemType == "ZH";
            baseSku.ShortTitle = wareHouseSku.ShortName;
            baseSku.CostPrice = wareHouseSku.CostPrice;
            baseSku.Subject = wareHouseSku.SkuName;
            baseSku.UpdateTime = dtNow;
            baseSku.Attributes = attrJson;
            baseSku.AttributeValue = wareHouseSku.SkuProperty;
            baseSku.Status = 1;
            baseSku.WareHouseProductCode = wareHouseSku.WareHouseProductCode;
            baseSku.WareHouseSkuCode = wareHouseSku.WareHouseSkuCode;

            // 目标图片地址不为空
            if (!string.IsNullOrEmpty(wareHouseSku.ImageUrl) && wareHouseSku.ImageUrl != baseSku.ImageUrl)
            {
                var ossEnt = ossService.ImgUrlToOssObject(wareHouseSku.ImageUrl, true, curFxUserId: curFxUserId);
                // 已存在图片信息，修改
                if (ossEnt != null)
                {
                    baseSku.ImageUrl = ossEnt.Url;
                    baseSku.ImageObjectId = ossEnt.Id;
                    wareHouseSku.ImageUrl = ossEnt.Url;
                    wareHouseSku.ImgUrlChange = true;
                }
            }

            // 处理规格属性
            var attr = existAttr ?? new BaseProductSkuAttribute();
            attr.SkuUid = baseSku.Uid;
            attr.AttributeName1 = "无规格";
            attr.AttributeValue1 = wareHouseSku.SkuProperty;
            attr.IsNew = existAttr == null;
            baseSku.AttributeModel = attr;

            return baseSku;
        }


        /// <summary>
        /// 获取数据库配置信息
        /// </summary>
        /// <param name="cloudPlatformType"></param>
        /// <param name="fxUserId"></param>
        /// <param name="dbConfigList"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        private DbConfigModel GetDbConfigModel(string cloudPlatformType, int fxUserId, List<DbConfigModel> dbConfigList, DbConfigModel model)
        {
            switch (cloudPlatformType)
            {
                // 头条-查询当前用户头条的连接串
                case "TouTiao":
                    return dbConfigList.FirstOrDefault(x =>
                        x.DbConfig.UserId == fxUserId && x.DbConfig.DbCloudPlatform == cloudPlatformType);
                // 拼多多-判断原商品所在库是否旧库
                // 是-旧库  P_DbNameConfig -> ApplicationName !=  fx_new
                // 否-查询当前用户的拼多多连接串
                case "Pinduoduo":
                    if (model.DbNameConfig.ApplicationName != "fx_new")
                        return model;
                    return dbConfigList.FirstOrDefault(x =>
                        x.DbConfig.UserId == fxUserId && x.DbConfig.DbCloudPlatform == cloudPlatformType && x.FromFxDbConfig != 0);
                case "Jingdong":
                    return dbConfigList.FirstOrDefault(x =>
                        x.DbConfig.UserId == fxUserId && x.DbConfig.DbCloudPlatform == cloudPlatformType);
                default:
                    if (model.DbNameConfig.ApplicationName != "fx_new")
                        return model;
                    return dbConfigList.FirstOrDefault(x =>
                        x.DbConfig.UserId == fxUserId && x.DbConfig.DbCloudPlatform == cloudPlatformType && x.FromFxDbConfig != 0);
            }
        }

    }
}
