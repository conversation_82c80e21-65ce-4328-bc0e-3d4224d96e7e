using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using SettlementType = DianGuanJiaApp.Data.Repository.ProductSettlementPriceRepository.SettlementType;
using static System.String;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Entity.SupplierProduct;
using DianGuanJiaApp.Utility.Other;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Warehouse.Model.Request;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.Services.BaseProduct;
using DianGuanJiaApp.Services.Services.SupplierProduct;
using DianGuanJiaApp.Warehouse.Entity;
using System.Diagnostics;
using System.Collections;
using Nest;
using System.Data.Common;
using System.Drawing;
using System.IO;
using System.Text;
using DianGuanJiaApp.Services.StorageService;
using DianGuanJiaApp.Utility.Net;
using RabbitMQ.Client.Framing.Impl;
using iTextSharp.text;
using DianGuanJiaApp.Data.Repository.Settings;
using DianGuanJiaApp.Data.Repository.SupplierProduct;
using Aop.Api.Domain;
using DianGuanJiaApp.Data.Dapper;
using Org.BouncyCastle.Tls.Crypto;

namespace DianGuanJiaApp.Services.BaseProduct
{
    /// <summary>
    /// 基础商品规格信息 相关服务/逻辑处理
    /// </summary>
    public partial class BaseProductSkuService : BaseProductBaseService<BaseProductSku>
    {
        private BaseProductSkuRepository _repository;
        private UserFxService _userFxService = new UserFxService();
        private WareHouseService _wareHouseService = new WareHouseService();
        //private BaseOfPtSkuRelationService _baseRelationService = null;
        //private MessageRecordService _messageRecordService = ;
        //private BaseProductSkuSupplierConfigService _baseSkuSupplierConfigService = new BaseProductSkuSupplierConfigService();
        private WareHouseService _service;
        public const int BATCH_SIZE = 500;//每批处理数量
        private readonly int _fxUserId;
        /// <summary>
        /// 默认使用当前登录账号的FxUserId
        /// </summary>
        public BaseProductSkuService()
        {
            _repository = new BaseProductSkuRepository();
            _baseRepository = _repository;
            _service = new WareHouseService();
        }

        /// <summary>
        /// 指定fxUserId
        /// </summary>
        /// <param name="fxUserId"></param>
        public BaseProductSkuService(int fxUserId)
        {
            _repository = new BaseProductSkuRepository(fxUserId);
            _baseRepository = _repository;
            _fxUserId = fxUserId;
        }

        /// <summary>
        /// 指定连接字符串
        /// </summary>
        /// <param name="connectionString"></param>
        public BaseProductSkuService(string connectionString, bool isMySQL = false)
        {
            _repository = new BaseProductSkuRepository(connectionString, isMySQL);
            _baseRepository = _repository;
        }

        /// <summary>
        /// 根据uid获取信息
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public BaseProductSku GetByUid(long uid)
        {
            return _repository.GetByUid(uid);
        }

        /// <summary>
        /// 获取列表（内部分批查询）
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<BaseProductSku> GetList(List<string> codes, string selectFields = "*",
            string whereFieldName = "SkuCode")
        {
            if (codes == null || codes.Any() == false)
                return new List<BaseProductSku>();

            var list = new List<BaseProductSku>();
            var batchSize = BATCH_SIZE;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetList(batchCodes);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 获取已存在列表
        /// </summary>
        /// <param name="codes">SkuCode</param>
        /// <param name="fxUserId">fxUserId</param>
        /// <returns></returns>
        public List<IdAndCodeModel> GetExistIdAndCodes(List<string> codes, int fxUserId)
        {
            if (codes == null || codes.Any() == false)
                return new List<IdAndCodeModel>();

            var list = new List<IdAndCodeModel>();
            var batchSize = BATCH_SIZE;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetExistIdAndCodes(batchCodes, fxUserId);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 基础商品列表
        /// </summary>
        /// <param name="model"></param>
        /// <param name="isSku">是否SKU维度</param>
        /// <returns></returns>
        public Tuple<int, List<BaseProductEntity>> GetBaseProductList(BaseProductSearchModel model, bool isSku = false)
        {
            var tuple = isSku ? _repository.GetBaseProductSkuList(model) : _repository.GetBaseProductList(model);
            return tuple;
        }
        /// <summary>
        /// 基础商品列表（不包含SKU查询）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Tuple<int, List<BaseProductEntity>> GetBaseProductListNoSkuQuery(BaseProductSearchModel model)
        {
            var tuple = _repository.GetBaseProductListNoSkuQuery(model);
            return tuple;
        }
        /// <summary>
        /// 基础商品列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Tuple<int, List<BaseProductSku>> GetAllBaseProductSkuList(BaseProductSearchModel model)
        {
            var tuple = _repository.GetAllBaseProductSkuList(model);
            return tuple;
        }

        /// <summary>
        /// 基础商品列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<BaseProductSku> GetBaseProductSkuList(long baseProductUid)
        {
            var res = _repository.GetBaseProductSkuList(baseProductUid);
            return res;
        }

        public BaseProductSku GetBaseProductSkuDetail(long baseProductSkuUid, long baseProductUid, int fxUserId)
        {
            var res = _repository.GetBaseProductSkuDetail(baseProductSkuUid, baseProductUid, fxUserId);
            return res;
        }

        /// <summary>
        /// 更新基础商品Sku 简称、结算价、成本价、重量、规格类型、主图
        /// </summary>
        /// <param name="modifyModels"></param>
        /// <param name="fxUserId"></param>
        public List<BaseProductSku> UpdateBaseProductSku(List<BaseProductSkuModel> modifyModels, int fxUserId, bool isAutoConvert)
        {
            if (modifyModels.IsNullOrEmptyList())
                return new List<BaseProductSku>();

            var skuShortTitleRepository = new BaseProductSkuShortTitleRepository(false);
            var skuAttributeRepository = new BaseProductSkuAttributeRepository();
            var ossObjectService = new OssObjectService();

            var skuUids = modifyModels.Where(m=>m.SkuUid>0).Select(m => m.SkuUid).ToList();
            if (skuUids.Count() < modifyModels.Count())
            {
                throw new LogicException("请传入基础商品旧规格SkuUid");
            }
            var existSkus = _repository.GetListBySkuUids(skuUids, fxUserId);
            if (existSkus.Count() < skuUids.Count())
            {
                throw new LogicException("通过SkuUid获取旧规格失败");
            }
            var existSkuShortTitles = skuShortTitleRepository.GetListBySkuUids(skuUids);
            var existSkuAttributes = skuAttributeRepository.GetList(skuUids);

            var now = DateTime.Now;

            var updateShortTitles = new List<BaseProductSkuShortTitle>();
            var addShortTitles = new List<BaseProductSkuShortTitle>();
            var updateAttributes = new List<BaseProductSkuAttribute>();
            var addAttributes = new List<BaseProductSkuAttribute>();

            var deleteImageIds = new HashSet<long>();
            existSkus.ForEach(baseSku =>
            {
                var model = modifyModels.FirstOrDefault(m => m.SkuUid == baseSku.Uid);
                //model.ShortTitle = "猫砂盆sku简称";
                if (model == null)
                {
                    throw new LogicException("更新旧规格时未传入对应更新模型");
                }

                model.IsUpdateShortTitle = !model.ShortTitle.IsNullOrEmpty() && (baseSku.ShortTitle.IsNullOrEmpty() || !model.ShortTitle.Equals(baseSku.ShortTitle));
                model.IsUpdateSettlePrice = model.SettlePrice != baseSku.SettlePrice;
                model.IsUpdateDistributePrice = model.DistributePrice != baseSku.DistributePrice;
                model.IsUpdateCostPrice = model.CostPrice != baseSku.CostPrice;

                #region 规格图和属性 
                // 属性
                var attributeJson = baseSku.Attributes;
                var existSkuAttribute = existSkuAttributes.FirstOrDefault(s => s.SkuUid == baseSku.Uid);

                // 规格主图
                long oosObjectId = baseSku.ImageObjectId;
                var isUpdateAttr = false;
                if (model.ImageUrlStr.IsNotNullOrEmpty() )
                {
                    var path = ImgHelper.GetRealPath(model.ImageUrlStr);
                    var pathStr = ImgHelper.SplitImageUrl(path);
                    var pathModel = JsonExtension.ToObject<BaseProductImageModel>(pathStr);

                    if (pathModel != null && model.ImageId == "0")
                    {
                        var ossObject = new OssObject();
                        ossObject.Md5 = pathModel.Url.ToShortMd5();
                        ossObject.Url = pathModel.Url;
                        ossObject.Domain = pathModel.Domain;
                        ossObject.Suffix = pathModel.Suffix;
                        ossObject.Name = pathModel.Name;
                        ossObject.CreateTime = now;
                        oosObjectId = ossObjectService.Add(ossObject);

                        baseSku.ImageObjectId = oosObjectId;

                        var suffix = pathModel.Suffix.IsNullOrEmpty() ? string.Empty : $".{pathModel.Suffix}";
                        baseSku.ImageUrl = $"{pathModel.Domain}/{pathModel.Url}/{pathModel.Name}{suffix}";

                        existSkuAttribute.ImageObjectId = oosObjectId;
                        isUpdateAttr = true;
                    }
                }
                else
                {
                    baseSku.ImageObjectId = 0;
                    baseSku.ImageUrl = null;
                    deleteImageIds.Add(model.ImageId.ToLong());
                }

                if (model.Attribute.IsNotNullOrEmpty())
                {
                    var attribute = new List<Dictionary<string, string>>();
                    if (!IsNullOrEmpty(model.Attribute.AttributeName1))
                    {
                        if (isAutoConvert) model.Attribute.AttributeName1 = "规格1";

                        if (string.IsNullOrWhiteSpace(model.Attribute.AttributeCode1)) // Code是null，说明是上线前的旧数据，否则就是前端bug未传此参数
                            model.Attribute.AttributeCode1 = Guid.NewGuid().ToString().ToShortMd5(); // 上线前的旧数据，编辑时自动补充Code值，才可以同步到平台资料

                        attribute.Add(new Dictionary<string, string> { { "k", model.Attribute.AttributeName1 }, { "v", model.Attribute.AttributeValue1 }, { "c", model.Attribute.AttributeCode1 } });
                    }
                    if (!IsNullOrEmpty(model.Attribute.AttributeName2))
                    {
                        if (isAutoConvert) model.Attribute.AttributeName2 = "规格2";

                        if (string.IsNullOrWhiteSpace(model.Attribute.AttributeCode2))
                            model.Attribute.AttributeCode2 = Guid.NewGuid().ToString().ToShortMd5();

                        attribute.Add(new Dictionary<string, string> { { "k", model.Attribute.AttributeName2 }, { "v", model.Attribute.AttributeValue2 }, { "c", model.Attribute.AttributeCode2 } });
                    }
                    if (!IsNullOrEmpty(model.Attribute.AttributeName3))
                    {
                        if (string.IsNullOrWhiteSpace(model.Attribute.AttributeCode3))
                            model.Attribute.AttributeCode3 = Guid.NewGuid().ToString().ToShortMd5();

                        attribute.Add(new Dictionary<string, string> { { "k", model.Attribute.AttributeName3 }, { "v", model.Attribute.AttributeValue3 }, { "c", model.Attribute.AttributeCode3 } });
                    }

                    attributeJson = attribute.ToJsonExt();
                    baseSku.AttributeModel = new BaseProductSkuAttribute();
                    baseSku.AttributeModel.AttributeName1 = model.Attribute.AttributeName1;
                    baseSku.AttributeModel.AttributeName2 = model.Attribute.AttributeName2;
                    baseSku.AttributeModel.AttributeName3 = model.Attribute.AttributeName3;

                    baseSku.AttributeModel.AttributeCode1 = model.Attribute.AttributeCode1;
                    baseSku.AttributeModel.AttributeCode2 = model.Attribute.AttributeCode2;
                    baseSku.AttributeModel.AttributeCode3 = model.Attribute.AttributeCode3;

                    if (!attributeJson.Equals(baseSku.Attributes))
                    {
                        if (existSkuAttribute.IsNotNullOrEmpty())
                        {
                            existSkuAttribute.ImgAttributeValueNo = model.Attribute.ImgAttributeValueNo;
                            existSkuAttribute.AttributeName1 = model.Attribute.AttributeName1;
                            existSkuAttribute.AttributeName2 = model.Attribute.AttributeName2;
                            existSkuAttribute.AttributeName3 = model.Attribute.AttributeName3;
                            existSkuAttribute.AttributeValue1 = model.Attribute.AttributeValue1;
                            existSkuAttribute.AttributeValue2 = model.Attribute.AttributeValue2;
                            existSkuAttribute.AttributeValue3 = model.Attribute.AttributeValue3;

                            existSkuAttribute.AttributeCode1 = model.Attribute.AttributeCode1;
                            existSkuAttribute.AttributeCode2 = model.Attribute.AttributeCode2;
                            existSkuAttribute.AttributeCode3 = model.Attribute.AttributeCode3;

                            isUpdateAttr = true;
                        }
                        else
                        {
                            addAttributes.Add(new BaseProductSkuAttribute()
                            {
                                ProductUid = baseSku.BaseProductUid,
                                SkuUid = baseSku.Uid,
                                ImageObjectId = oosObjectId,
                                CreateTime = now,

                                ImgAttributeValueNo = model.Attribute.ImgAttributeValueNo,
                                AttributeName1 = model.Attribute.AttributeName1,
                                AttributeName2 = model.Attribute.AttributeName2,
                                AttributeName3 = model.Attribute.AttributeName3,
                                AttributeValue1 = model.Attribute.AttributeValue1,
                                AttributeValue2 = model.Attribute.AttributeValue2,
                                AttributeValue3 = model.Attribute.AttributeValue3,

                                AttributeCode1 = model.Attribute.AttributeCode1,
                                AttributeCode2 = model.Attribute.AttributeCode2,
                                AttributeCode3 = model.Attribute.AttributeCode3
                            });
                        }
                    }
                }

                baseSku.ImageObjectId = oosObjectId;
                baseSku.ImageUrl = model.ImageUrl != null ? $"{model.ImageUrl.Domain}/{model.ImageUrl.Url}/{model.ImageUrl.Name}.{model.ImageUrl.Suffix}" : baseSku.ImageUrl;

                if(isUpdateAttr)
                    updateAttributes.Add(existSkuAttribute);
                #endregion

                var isUpdateWeight = model.Weight != baseSku.Weight;
                if (model.IsNewSkuCode == false || model.IsNewSkuCode == null)
                    baseSku.SkuCode = model.SkuCode;
                baseSku.Subject = model.Subject;
                baseSku.ShortTitle = model.IsUpdateShortTitle? model.ShortTitle: baseSku.ShortTitle;
                baseSku.CostPrice = model.IsUpdateCostPrice? model.CostPrice : baseSku.CostPrice;
                baseSku.SettlePrice = model.IsUpdateSettlePrice? model.SettlePrice:baseSku.SettlePrice;
                baseSku.DistributePrice = model.IsUpdateDistributePrice? model.DistributePrice :baseSku.DistributePrice;
                baseSku.Weight = isUpdateWeight ? model.Weight.ToInt() : baseSku.Weight;
                baseSku.Attributes = attributeJson;
                baseSku.UpdateTime = now;
                baseSku.AttributeValue = model.AttributeValue;

                // 简称
                if (model.IsUpdateShortTitle)
                {
                    var skuShortTitle = existSkuShortTitles.FirstOrDefault(s => s.BaseProductSkuUid == baseSku.Uid);
                    if (skuShortTitle.IsNotNullOrEmpty())
                    {
                        skuShortTitle.ShortTitle = baseSku.ShortTitle;
                        skuShortTitle.UpdateTime = now;
                        updateShortTitles.Add(skuShortTitle);
                    }
                    else
                    {
                        addShortTitles.Add(new BaseProductSkuShortTitle()
                        {
                            ShortTitle = baseSku.ShortTitle,
                            CreateTime = now,
                            UpdateTime= now,
                            Status = 1,
                            BaseProductUid = baseSku.BaseProductUid,
                            BaseProductSkuUid = baseSku.Uid
                        });
                    }
                }

            });

            // 更新
            var updateCount = _repository.BatchUpdate(existSkus);
            if (updateCount < existSkus.Count)
            {
                throw new LogicException("基础商品规格更新到数据库发生异常");
            }
            skuShortTitleRepository.BatchAdd(addShortTitles);
            skuShortTitleRepository.BulkUpdate(updateShortTitles);
            new BaseProductSkuAttributeService().BatchAdd(addAttributes);
            new BaseProductSkuAttributeService().BulkUpdate(updateAttributes);
            new BaseProductImageRepository().BatchDelete(deleteImageIds.Where(i=>i>0).ToList());
            return existSkus;
        }

        /// <summary>
        /// 重置供货厂家（设为自营）
        /// </summary>
        /// <param name="oldAgentFxUserId">原商家</param>
        /// <param name="oldSupplierFxUserId">原厂家</param>
        public void ResetSupplier(int oldAgentFxUserId, int oldSupplierFxUserId)
        {
            if (oldAgentFxUserId == oldSupplierFxUserId)
                return;

            Log.Debug($"重置供货厂家（设为自营）2：oldAgentFxUserId={oldAgentFxUserId}，oldSupplierFxUserId={oldSupplierFxUserId}", "ResetSupplier.txt");
            //商家侧处理
            _repository = new BaseProductSkuRepository(oldAgentFxUserId);
            _repository.ResetSupplier(oldAgentFxUserId, oldSupplierFxUserId);
        }

        /// <summary>
        /// 基础商品Sku保存编辑
        /// </summary>
        /// <param name="modifyModels"></param>
        /// <param name="fxUserId"></param>
        /// <param name="dbName"></param>
        public List<BaseProductSku> ModifyBaseProductSku(List<BaseProductSkuModel> modifyModels, int fxUserId,string dbName, bool isAutoConvert, bool isSyncToPtSku = true)
        {
            // 基础商品sku保存修改
            var updateSkus = UpdateBaseProductSku(modifyModels, fxUserId, isAutoConvert);
              
            // 获取关联的平台商品
            var skuUids = modifyModels.Where(m=>m.SkuUid>0).Select(m => m.SkuUid).ToList();
            var relationDict = new BaseOfPtSkuRelationService().GetListBySkuUids(skuUids, fxUserId);

            var messageRecordService = new MessageRecordService();

            #region 发送修改消息到平台商品对应平台
            if (isSyncToPtSku)
            {
                // 根据平台划分关联
                var relations = relationDict.SelectMany(d => d.Value).ToList();
                var sendModels = GetUpdatePtSkuMessageModels(modifyModels, relations, fxUserId, dbName);
                var sendCount = sendModels.Count();
                var successCount = messageRecordService.SendBusinessMessage(sendModels);
                if (successCount < sendCount)
                {
                    throw new LogicException("基础商品关联平台商品更新发送消息失败");
                }
                //messageRecordService.SyncBaseSkuDataToBusinessDb(sendModels);
                
                foreach (var relation in relations)
                {
                    var sku = updateSkus.FirstOrDefault(s => s.Uid == relation.BaseProductSkuUid);
                    if (sku == null) continue;
                    
                    // 更新关联信息
                    relation.BaseProductSkuCode = sku.SkuCode;
                    // 用逗号隔开
                    var attrs = sku.Attributes.ToObject<List<SkuAttr>>();
                    if (attrs != null) relation.BaseProductSkuSubject = Join(",", attrs.Select(a => $"{a.v}"));
                    
                    relation.BaseProductSkuShortTitle = sku.ShortTitle;
                    
                    if (sku.RelationList == null)
                        sku.RelationList = new List<BaseOfPtSkuRelation>();
                    sku.RelationList.Add(relation);
                }
            }

            #endregion

            return updateSkus;
        }

        /// <summary>
        /// 基础商品快速编辑
        /// </summary>
        /// <param name="model"></param>
        public void SaveEditBaseProduct(BaseProductSkuModifyModel model,int fxUserId,string dbName)
        {
            var productRepository = new BaseProductRepository();
            var now = DateTime.Now;
            var existSkus = GetListByProductUid(model.BaseProductUid, fxUserId);

            SaveAndSync(existSkus, model, fxUserId, dbName,true);
            
            // 保存SPU简称
            var product = productRepository.GetByUid(model.BaseProductUid);
            if (!product.ShortTitle.Equals(model.ShortTitle))
            {
                product.ShortTitle = model.ShortTitle;
                productRepository.Update(product);
                var repository = new BaseProductShorTitleRepository(false);
                var shortTitle = repository.GetListByUids(new List<long>() { product.Uid }).FirstOrDefault();
                if (shortTitle.IsNotNullOrEmpty())
                {
                    shortTitle.ShortTitle = model.ShortTitle;
                    repository.Update(shortTitle);
                }
                else
                {
                    repository.Add(new BaseProductShortTitle()
                    {
                        BaseProductUid = product.Uid,
                        ShortTitle = model.ShortTitle,
                        CreateTime = now,
                        UpdateTime = now,
                        Status = 1
                    });
                }
                #region 同步SPU简称到库存系统
                try
                {
                    Task.Run(() =>
                    {
                        var req = new WarehouseUpdateShorTitleRequest
                        {
                            Code = product.SpuCode,
                            IsSku = false,
                            ShortTitle = model.ShortTitle
                        };
                        // 同步SPU简称到各业务库
                        new WareHouseService().UpdateShortTitle(req);
                    });
                }
                catch (Exception ex)
                {
                    Log.WriteError($"商品简称同步库存商品失败，失败原因：{ex.ToJson()}");
                }
                #endregion

                // 同步数据到各业务库
                var relationService = new BaseOfPtSkuRelationService();
                var skuUids = existSkus.Select(s => s.Uid).ToList();
                var relations = relationService
                    .GetListBySkuUids(skuUids, fxUserId)
                    .SelectMany(d => d.Value)
                    .ToList();
                relations.ForEach(relation =>
                {
                    relation.BaseProductShortTitle = model.ShortTitle;
                    relation.Type = 2;
                    relation.UpdateTime = now;
                });
                
                new BaseProductSkuCommonService().SyncRelationInfo(relations, fxUserId);
            }
            var isUpdateSuccess = productRepository.Update(product);
            if (!isUpdateSuccess)
                throw new LogicException("保存基础商品到数据库失败");  
        }

        /// <summary>
        /// 基础商品快速编辑
        /// </summary>
        /// <param name="model"></param>
        public void SaveEdit(BaseProductSkuModifyModel model, int fxUserId, string dbName)
        {
            var productRepository = new BaseProductRepository();
            var now = DateTime.Now;
            var allSkuUids = model.SkuUids.SelectMany(v => v.Value).ToList();
            var existSkus = GetListBySkuUids(allSkuUids, fxUserId);

            SaveAndSync(existSkus, model, fxUserId, dbName);

            // 同步数据到各业务库
            var relationService = new BaseOfPtSkuRelationService();
            var skuUids = existSkus.Select(s => s.Uid).ToList();
            var relations = relationService
                .GetListBySkuUids(skuUids, fxUserId)
                .SelectMany(d => d.Value)
                .ToList();
            relations.ForEach(relation =>
            {
                // 找到对应的Sku
                var sku = existSkus.FirstOrDefault(s => s.Uid == relation.BaseProductSkuUid);
                if (sku == null) return;
                
                relation.BaseProductSkuShortTitle = sku.ShortTitle;
                relation.Type = 2;
                relation.UpdateTime = now;
            });

            new BaseProductSkuCommonService().SyncRelationInfo(relations, fxUserId);

            #region 同步SPU简称到库存系统
            if (model.ShortTitle != null && model.ShortTitle != string.Empty && existSkus.Any())
            {
                Parallel.ForEach(existSkus,new ParallelOptions { MaxDegreeOfParallelism=10}, (sku) => {

                    try
                    {
                        var req = new WarehouseUpdateShorTitleRequest
                        {
                            Code = sku.SkuCode,
                            IsSku = true,
                            ShortTitle = model.ShortTitle
                        };
                        // 同步SPU简称到各业务库
                        new WareHouseService().UpdateShortTitle(req);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"商品简称同步库存商品失败，失败原因：{ex.ToJson()}");
                    }
                });
            }
            #endregion
        }

        private void SaveAndSync(List<BaseProductSku> existSkus, BaseProductSkuModifyModel model, int fxUserId, string dbName,bool isProduct=false)
        {
            var shortTitleRepository = new BaseProductSkuShortTitleRepository(false);
            var existShortTitles = new List<BaseProductSkuShortTitle>();
            var updateShortTitles = new List<BaseProductSkuShortTitle>();
            var updateCostSkus = new List<WareHouseSku>();
            var now = DateTime.Now;
            if (model.ShortTitle != null)
            {
                existShortTitles = shortTitleRepository.GetListBySkuUids(existSkus.Select(s => s.Uid).ToList());
            }
            var skuModels = new List<BaseProductSkuModel>();
            existSkus.ForEach(sku =>
            {
                var isAddModel = false;
                var skuModel = new BaseProductSkuModel()
                {
                    SkuUid = sku.Uid
                };
               
                // SPU维度修改简称不同步更新到SKU
                if (!isProduct)
                {
                    // 简称
                    if (model.ShortTitle.IsNotNullOrEmpty() && !model.ShortTitle.Equals(sku.ShortTitle))
                    {
                        sku.ShortTitle = model.ShortTitle;
                        var titles = existShortTitles.FirstOrDefault(e => e.BaseProductSkuUid == sku.Uid);
                        if (titles != null)
                        {
                            titles.UpdateTime = now;
                            titles.ShortTitle = model.ShortTitle;
                            updateShortTitles.Add(titles);
                        }

                        if (model.IsUpdatePtSkuShortTitle == true)
                        {
                            isAddModel = true;
                            skuModel.ShortTitle = model.ShortTitle;
                            skuModel.IsUpdateShortTitle = true;
                        }
                    }
                }
               
                // 结算价，即采购价
                if (model.SettlePrice != null)
                {
                    sku.SettlePrice = model.SettlePrice.ToDecimal();
                    if (model.IsUpdatePtSkuSettlePrice == true)
                    {
                        isAddModel = true;
                        skuModel.SettlePrice = model.SettlePrice.ToDecimal();
                        skuModel.IsUpdatePtSkuSettlePrice = true;
                    }

                }
                // 分销价
                if (model.DistributePrice != null)
                {
                    sku.DistributePrice = model.DistributePrice.ToDecimal();
                    if (model.IsUpdatePtSkuDistributePrice == true)
                    {
                        isAddModel = true;
                        skuModel.DistributePrice = model.DistributePrice.ToDecimal();
                        skuModel.IsUpdatePtSkuDistributePrice = true;
                    }

                }
                // 成本价
                if (model.CostPrice != null)
                {
                    sku.CostPrice = model.CostPrice.ToDecimal();
                    if (model.IsUpdatePtSkuCostPrice == true)
                    {
                        isAddModel = true;
                        skuModel.CostPrice = model.CostPrice.ToDecimal();
                        skuModel.IsUpdatePtSkuCostPrice = true;
                    }
                    updateCostSkus.Add(new WareHouseSku { SkuCargoNumber = sku.SkuCode, CostPrice = model.CostPrice.ToDecimal() });
                }
                if (isAddModel)
                    skuModels.Add(skuModel);
            });
            if(updateShortTitles.Count>0)
                shortTitleRepository.BulkUpdate(updateShortTitles);
            var updateCount = _repository.BatchUpdate(existSkus);
            if (updateCount < existSkus.Count)
            {
                throw new LogicException("基础商品规格更新到数据库发生异常");
            }
            // 更新到平台商品
            if (!skuModels.IsNullOrEmptyList())
            {
                var skuUids = existSkus.Select(s => s.Uid).ToList();
                var relations = new BaseOfPtSkuRelationService().GetListBySkuUids(skuUids, fxUserId).SelectMany(d => d.Value).ToList();
                var sendModels = GetUpdatePtSkuMessageModels(skuModels, relations, fxUserId, dbName);
                var sendCount = sendModels.Count();
                var successCount = new MessageRecordService().SendBusinessMessage(sendModels);
                //var successCount = new MessageRecordService().SyncBaseSkuDataToBusinessDb(sendModels);
                if (successCount < sendCount)
                {
                    throw new LogicException("基础商品关联平台商品更新发送消息失败");
                }
            }
            
            // 更新库存SKu
            if (updateCostSkus.Any())
            {
                var warehouseService = new WareHouseService();
                var req = new WarehouseSkuUpdateCostPriceRequest
                {
                    Skus = updateCostSkus
                };
                warehouseService.UpdateCostPrice(req);
            }
        }

        /// <summary>
        /// 构建更新平台商品信息消息模型
        /// </summary>
        /// <returns></returns>
        public List<MessageRecord> GetUpdatePtSkuMessageModels(List<BaseProductSkuModel> baseSkus,List<BaseOfPtSkuRelation> relations,int fxUserId,string dbName)
        {
            var modifyModelDict = baseSkus.ToDictionary(m => m.SkuUid);
            //var baseOfSkuRelations = relationDict.Values.SelectMany(r => r).ToList();
            var platRelationsDict = relations.GroupBy(r => r.ProductPlatformType).ToDictionary(g => g.Key, g => g.ToList());
            var sendModels = new List<MessageRecord>();
            foreach (var platform in platRelationsDict.Keys)
            {
                var tempRelations = platRelationsDict[platform];
                var syncModel = new SyncPtSkuModel();
                syncModel.FxUserId = fxUserId;
                
                foreach (var relation in tempRelations)
                {
                    var modifyModel = modifyModelDict[relation.BaseProductSkuUid];
                    //modifyModel.FxUserId = fxUserId;
                    // 构建简称更新模型
                    if (modifyModel.IsUpdateShortTitle)
                    {
                        syncModel.UpdateShortTitleModels.Add(new SaveShortTitleOrWeightModel()
                        {
                            ProductCode = relation.ProductCode,
                            SkuCode = relation.ProductSkuCode,
                            SkuShortTitle = modifyModel.ShortTitle,
                            IsShortTitleChange = true
                        });
                    }
                    // 构建结算价、成本价更新模型
                    if (modifyModel.IsUpdatePtSkuSettlePrice) // 对厂家结算价
                    {
                        syncModel.UpdateSettlePriceModels.Add(new ProductSettlementPrice()
                        {
                            ProductCode = relation.ProductCode,
                            ProductSkuCode = relation.ProductSkuCode,
                            CreateUser = fxUserId,
                            SettlementType = SettlementType.Merchant.ToInt(),
                            Price = modifyModel.SettlePrice.ToDecimal(),
                            PlatformType = relation.ProductPlatformType
                        });

                    }
                    if (modifyModel.IsUpdatePtSkuCostPrice) // 成本价
                    {
                        syncModel.UpdateCostPriceModels.Add(new ProductSettlementPrice()
                        {
                            ProductCode = relation.ProductCode,
                            ProductSkuCode = relation.ProductSkuCode,
                            CreateUser = fxUserId,
                            FxUserId = 0,
                            SettlementType = SettlementType.CostPrice.ToInt(),
                            Price = modifyModel.CostPrice.ToDecimal(),
                            PlatformType = relation.ProductPlatformType
                        });
                    }
                    if (modifyModel.IsUpdatePtSkuDistributePrice) // 对商家 分销价
                    {
                        syncModel.UpdateDistributePriceModels.Add(new ProductSettlementPrice()
                        {
                            ProductCode = relation.ProductCode,
                            ProductSkuCode = relation.ProductSkuCode,
                            CreateUser = fxUserId,
                            SettlementType = SettlementType.Manufacturer.ToInt(),
                            Price = modifyModel.DistributePrice.ToDecimal(),
                            PlatformType = relation.ProductPlatformType
                        });
                    }
                }

                sendModels.Add(new MessageRecord()
                {
                    BusinessId = Guid.NewGuid().ToString().ToShortMd5(),
                    MsgType = BaseProductMsgType.SyncPtSku,
                    FxUserId = fxUserId,
                    ProductPlatformType = platform,
                    DbName = dbName,
                    DataJson = syncModel.ToJson(),
                });
            }
            return sendModels;
        }

        /// <summary>
        /// 获取解绑关联 恢复初始数据消息模型
        /// </summary>
        /// <returns></returns>
        public Tuple<BindSupplierRequestModel, SyncPtSkuModel> GetUnBindSkuRestoreModel(BaseProductSkuUnbindModel model,int fxUserId, BaseOfPtSkuRelation relation)
        {
            var syncModel = new SyncPtSkuModel();
            BindSupplierRequestModel bindModel =null;
            syncModel.IsUnBind = true;
            syncModel.FxUserId = fxUserId;

            if (model.IsRestoreShortTitle)
            {
                // 清空简称
                syncModel.UpdateShortTitleModels.Add(new SaveShortTitleOrWeightModel()
                {
                    ProductCode = relation.ProductCode,
                    SkuCode = relation.ProductSkuCode,
                    SkuShortTitle = Empty,
                    IsShortTitleChange = true
                });
            }
            if (model.IsRestoreDistributePrice)
            {
                // 清空分销价 对商家
                syncModel.UpdateDistributePriceModels.Add(new ProductSettlementPrice()
                {
                    ProductCode = relation.ProductCode,
                    ProductSkuCode = relation.ProductSkuCode,
                    SettlementType = SettlementType.Manufacturer.ToInt(),
                    ShopId = relation.ProductShopId,
                    CreateUser = fxUserId,
                    Price = 0
                });
            }

            if (model.IsRestoreSettlePrice)
            {
                // 清空结算价 对厂家
                syncModel.UpdateSettlePriceModels.Add(new ProductSettlementPrice()
                {
                    ProductCode = relation.ProductCode,
                    ProductSkuCode = relation.ProductSkuCode,
                    SettlementType = SettlementType.Merchant.ToInt(),
                    ShopId = relation.ProductShopId,
                    CreateUser = fxUserId,
                    Price = 0
                });
            }

            if (model.IsRestoreCostPrice)
            {
                // 清空成本价
                syncModel.UpdateCostPriceModels.Add(new ProductSettlementPrice()
                {
                    ProductCode = relation.ProductCode,
                    ProductSkuCode = relation.ProductSkuCode,
                    SettlementType = SettlementType.CostPrice.ToInt(),
                    ShopId = relation.ProductShopId,
                    CreateUser = fxUserId,
                    FxUserId = 0,
                    Price = 0
                });
            }

            if (model.IsRestoreSupplier)
            {
                // 平台商品设为自营
                bindModel = new BindSupplierRequestModel()
                {
                    productCodes = new List<string>() { relation.ProductCode },
                    skuCodes = new Dictionary<string, string>() { { relation.ProductCode, relation.ProductSkuCode } },
                    isSelf = true,
                    IsBindSku = true,
                    FxUserId = fxUserId
                };
            }
            
            return new Tuple <BindSupplierRequestModel, SyncPtSkuModel>(bindModel,syncModel);
        }

        /// <summary>
        /// 编码验证
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="isSku"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<string> GetBaseProductSkuByCode(List<string> codes, bool isSku, int fxUserId)
        {
            if (codes.IsNullOrEmptyList())
                return new List<string>();
            return _repository.GetBaseProductSkuByCode(codes, isSku, fxUserId);
        }

        /// <summary>
        /// 基础商品创建(单个)
        /// </summary>
        /// <param name="modifyModels"></param>
        public BaseProductEntity CreateBaseProductSku(BaseProductSkuAddModel models, int fxUserId)
        {
            return CreateBaseProductSkuBase(models, fxUserId);
        }

        /// <summary>
        /// 基础商品创建(批量)
        /// </summary>
        /// <param name="models"></param>
        /// <param name="fxUserId"></param>
        public List<BaseProductEntity> CreateBaseProductSkus(List<BaseProductSkuAddModel> models, int fxUserId)
        {
            // 上下文初始化
            // 基础商品快--捷生成
            // 基础商品快--捷导入
            var userFx = _userFxService.Get(fxUserId);
            var sc = new SiteContext(userFx);
            
            // 店铺商品编码覆盖规则
            // 已存在：初始化
            // 不存在：延用
            var codesAll = models
                .SelectMany(a => a.ProductSkus)
                .Select(a => a.SkuCode)
                .ToList();
            var codeNotR = models
                .SelectMany(a=>a.ProductSkus)
                .Select(a => a.SkuCode)
                .Where(a=>a.IsNotNullOrEmpty())
                .Distinct()
                .ToList();
            var codesDataBaeR = this.GetBaseProductSkuByCode(codeNotR, true, fxUserId);
            var codesCurrentR = codesAll
                .Where(a => a.IsNotNullOrEmpty())
                .GroupBy(a => a)
                .Where(a => a.Count() > 1)
                .Select(a => a.Key)
                .ToList();

            int indexSku = 0;
            int indexProduct = 0;
            int indexTag = 0;
            
            foreach (var p in models)
            {
                foreach (var sku in p.ProductSkus)
                {
                    // skuCode 无值
                    if (sku.SkuCode.IsNullOrEmpty())
                    {
                        sku.SkuCode = Guid.NewGuid().ToString().ToShortMd5();
                    }
                    // skuCode 有值
                    else
                    {
                        // skuCode 数据库有值
                        if (codesDataBaeR.Count > 0 && codesDataBaeR.Any(a => a == sku.SkuCode))
                        {
                            sku.SkuCode = Guid.NewGuid().ToString().ToShortMd5();
                        }
                        // skuCode 数据库无值
                        else
                        {
                            // skuCode 当前数据有值
                            if (codesCurrentR.Count > 0 && codesCurrentR.Any(a => a == sku.SkuCode))
                            {
                                // 第一次 默认
                                if (indexTag == 0)
                                {
                                    
                                }
                                // 第一次以上 初始
                                else
                                {
                                   sku.SkuCode = Guid.NewGuid().ToString().ToShortMd5();
                                }
                                indexTag++;
                            }
                        }
                    }
                    indexSku++;
                }
                indexProduct++;
            }

            var baseProduct = new List<BaseProductEntity>(models.Count);
            Parallel.ForEach(models, new ParallelOptions { MaxDegreeOfParallelism = 5 }, model =>
            {
                try
                {
                    baseProduct.Add(CreateBaseProductSkuBase(model, fxUserId));
                }
                catch (Exception ex)
                {
                    Log.WriteError($"批量创建基础资料异常：{ex.Message}，参数：{model.ToJson()}");
                }
            });

            return baseProduct;
        }

        public BaseProductEntity CreateBaseProductSkuBase(BaseProductSkuAddModel model, int fxUserId)
        {
            // 编号获取
            var now = DateTime.Now;
            var allCount = model.ProductSkus.Count() + 1;
            var uniqueIdList = new ProductDbConfigRepository().BaseProductSystemUniqueId("", fxUserId, allCount);
            if (uniqueIdList == null || uniqueIdList.Count == 0)
                throw new Exception("未获取到流水编号！");
            var uniqueIdIndex = 1;
            var productUid = (1 + uniqueIdList[0]).ToLong();
            var ossObjectService = new OssObjectService();

            // 图片附件--主图
            // 图片附件--详情
            // 图片附件--规格
            var productAttachments = new List<BaseProductImageModel>();
            var productDescriptionAttachments = new List<BaseProductImageModel>();
            var productSkuAttachments = 
                model.ProductSkus == null || 
                model.ProductSkus.Count == 0 ? new List<BaseProductSkuModel>() :
                model.ProductSkus;

            if (model.ProductImagesStr?.Count > 0)
            {
                model.ProductImagesStr.ForEach(image =>
                {
                    var path = ImgHelper.GetRealPath(image);
                    var pathStr = ImgHelper.SplitImageUrl(path);
                    var pathModel = JsonExtension.ToObject<BaseProductImageModel>(pathStr);
                    if (pathModel != null)
                        productAttachments.Add(pathModel);
                });
            }
            if (model.DescriptionStr?.Count > 0)
            {
                model.DescriptionStr.ForEach(image =>
                {
                    var path = ImgHelper.GetRealPath(image);
                    var pathStr = ImgHelper.SplitImageUrl(path);
                    var pathModel = JsonExtension.ToObject<BaseProductImageModel>(pathStr);
                    if (pathModel != null)
                        productDescriptionAttachments.Add(pathModel);
                });
            }
            // SUP主图
            var productImages = new List<BaseProductImage>();
            // SUP详情
            var productDescImages = new List<BaseProductImage>();
            // 保存SKU信息
            var productSkus = new List<BaseProductSku>();
            // 保存SKU简称
            var productSkuShortTitles = new List<BaseProductSkuShortTitle>();
            // 保存SKU规格
            var productSkuAttributes = new List<BaseProductSkuAttribute>();
            // 基础商品绑定
            var productSkuSupplierConfigList = new List<BaseProductSkuSupplierConfig>();
            // 供货厂家设置（来源货盘复制）
            var configList = new List<BaseProductSkuSupplierConfig>();
            var isFromSupplierProduct = model.ConfigModels.IsNotNullOrEmpty();
            // 分享路径流
            var sharePathFlowList = new List<SharePathFlow>();
            var sharePathFlowNodeList = new List<SharePathFlowNode>();

            foreach (var image in productAttachments)
            {
                var ossObject = new OssObject();
                //ossObject.ObjectId = image.ObjectId;
                //ossObject.Md5 = image.Url.ToShortMd5();
                ossObject.Url = image.Url;
                ossObject.Domain = image.Domain;
                ossObject.Suffix = image.Suffix;
                ossObject.Name = image.Name;
                ossObject.CreateTime = now;
                var id = ossObjectService.Add(ossObject);

                var productImage = new BaseProductImage();
                productImage.ProductUid = productUid;
                productImage.ImageObjectId = id;
                productImage.Url = image.Url;
                productImage.Name = image.Name;
                productImage.Domain = image.Domain;
                productImage.Suffix = image.Suffix;
                productImage.CreateTime = now;
                productImage.Status = 1;
                productImages.Add(productImage);
            }

            foreach (var image in productDescriptionAttachments)
            {
                var ossObject = new OssObject();
                //ossObject.ObjectId = image.ObjectId;
                //ossObject.Md5 = image.Url.ToShortMd5();
                ossObject.Url = image.Url;
                ossObject.Domain = image.Domain;
                ossObject.Suffix = image.Suffix;
                ossObject.Name = image.Name;
                ossObject.CreateTime = now;
                //var id = ossObjectService.Add(ossObject);

                var productImage = new BaseProductImage();
                productImage.ProductUid = productUid;
                //productImage.ImageObjectId = id;
                //productImage.Url = $"{image.Domain}/{image.Url}/{image.Name}.{image.Suffix}";
                if (image != null)
                {
                    var suffix = image.Suffix.IsNullOrEmpty() ? string.Empty : $".{image.Suffix}";
                    productImage.Url = $"{image.Domain}/{image.Url}/{image.Name}{suffix}";
                }
                //productImage.Url = image.Url;
                productImage.Name = image.Name;
                productImage.Domain = image.Domain;
                productImage.CreateTime = now;
                productImage.Status = 1;
                //to 附件类型
                productDescImages.Add(productImage);
            }

            foreach (var sku in productSkuAttachments)
            {
                long oosObjectId = 0;
                long productSkuUid = (2 + uniqueIdList[uniqueIdIndex]).ToLong();
                var productSkuSupplierConfigs =
                    sku.BaseProductSkuSupplierConfig == null ? new List<BaseProductSkuSupplierConfigModel>() : sku.BaseProductSkuSupplierConfig;
                var supplierFxUserId = 0;
                if (productSkuSupplierConfigs == null)
                    supplierFxUserId = 0;
                else if (productSkuSupplierConfigs != null && productSkuSupplierConfigs.Count == 0)
                    supplierFxUserId = 0;
                else if (productSkuSupplierConfigs != null && productSkuSupplierConfigs.Count == 1 && productSkuSupplierConfigs[0].SupplierFxUserId == fxUserId)
                    supplierFxUserId = -1;
                else if (productSkuSupplierConfigs != null && productSkuSupplierConfigs.Count == 1 && productSkuSupplierConfigs[0].IsSelf == true)
                    supplierFxUserId = -1;
                else if (productSkuSupplierConfigs != null && productSkuSupplierConfigs.Count > 0 && productSkuSupplierConfigs[0].SupplierFxUserId.HasValue)
                    supplierFxUserId = productSkuSupplierConfigs[0].SupplierFxUserId.Value;
                else
                    supplierFxUserId = 1;

                var attribute = new List<Dictionary<string, string>>();
                if (!IsNullOrEmpty(sku.Attribute.AttributeName1))
                {
                    BaseProductSkuModel currentCollectItem = productSkuAttachments.FirstOrDefault(b => b.Attribute.AttributeValue1 == sku.Attribute.AttributeValue1);
                    sku.Attribute.AttributeCode1 = currentCollectItem?.Attribute?.AttributeCode1 ?? Guid.NewGuid().ToString().ToShortMd5();
                    attribute.Add(new Dictionary<string, string> { { "k", sku.Attribute.AttributeName1 }, { "v", sku.Attribute.AttributeValue1 }, { "c", sku.Attribute.AttributeCode1 } });
                }
                if (!IsNullOrEmpty(sku.Attribute.AttributeName2))
                {
                    BaseProductSkuModel currentCollectItem = productSkuAttachments.FirstOrDefault(b => b.Attribute.AttributeValue2 == sku.Attribute.AttributeValue2);
                    sku.Attribute.AttributeCode2 = currentCollectItem?.Attribute?.AttributeCode2 ?? Guid.NewGuid().ToString().ToShortMd5();
                    attribute.Add(new Dictionary<string, string> { { "k", sku.Attribute.AttributeName2 }, { "v", sku.Attribute.AttributeValue2 }, { "c", sku.Attribute.AttributeCode2 } });
                }
                if (!IsNullOrEmpty(sku.Attribute.AttributeName3))
                {
                    BaseProductSkuModel currentCollectItem = productSkuAttachments.FirstOrDefault(b => b.Attribute.AttributeValue3 == sku.Attribute.AttributeValue3);
                    sku.Attribute.AttributeCode3 = currentCollectItem?.Attribute?.AttributeCode3 ?? Guid.NewGuid().ToString().ToShortMd5();
                    attribute.Add(new Dictionary<string, string> { { "k", sku.Attribute.AttributeName3 }, { "v", sku.Attribute.AttributeValue3 }, { "c", sku.Attribute.AttributeCode3 } });
                }

                var attributeJson = attribute.ToJsonExt();

                foreach (var config in productSkuSupplierConfigs)
                {
                    var productSkuSupplierConfig = new BaseProductSkuSupplierConfig();
                    productSkuSupplierConfig.ProductUid = productUid;
                    productSkuSupplierConfig.SkuUid = productSkuUid;
                    if (config.IsSelf)
                        productSkuSupplierConfig.SupplierFxUserId = fxUserId;
                    else
                        productSkuSupplierConfig.SupplierFxUserId = config.SupplierFxUserId.Value;
                    productSkuSupplierConfig.Config = config.Config;
                    productSkuSupplierConfig.ConfigType = config.ConfigType;
                    productSkuSupplierConfig.ApplyScope = config.ApplyScope;
                    productSkuSupplierConfig.CreateTime = now;
                    productSkuSupplierConfig.UpdateTime = now;
                    productSkuSupplierConfig.Status = 1;
                    productSkuSupplierConfigList.Add(productSkuSupplierConfig);
                }

                BaseProductImageModel pathModel = null;
                if (sku.ImageUrlStr.IsNotNullOrEmpty())
                {
                    var ossObject = new OssObject();
                    //ossObject.ObjectId = image.ObjectId;
                    var path = ImgHelper.GetRealPath(sku.ImageUrlStr);
                    var pathStr = ImgHelper.SplitImageUrl(path);
                    pathModel = JsonExtension.ToObject<BaseProductImageModel>(pathStr);

                    //ossObject.Md5 = pathModel.Url.ToShortMd5();
                    ossObject.Url = pathModel.Url;
                    ossObject.Domain = pathModel.Domain;
                    ossObject.Suffix = pathModel.Suffix;
                    ossObject.Name = pathModel.Name;
                    ossObject.CreateTime = now;
                    oosObjectId = ossObjectService.Add(ossObject);
                }
                var productSku = new BaseProductSku();
                productSku.Uid = productSkuUid;
                productSku.BaseProductUid = productUid;
                productSku.SkuCode = sku.SkuCode;
                productSku.ImageObjectId = oosObjectId;
                if (pathModel != null)
                {
                    var suffix = pathModel.Suffix.IsNullOrEmpty() ? string.Empty : $".{pathModel.Suffix}";
                    productSku.ImageUrl = $"{pathModel.Domain}/{pathModel.Url}/{pathModel.Name}{suffix}";
                }
                productSku.FxUserId = fxUserId;
                productSku.Subject = sku.Subject;
                productSku.ShortTitle = sku.ShortTitle;
                productSku.SupplierFxUserId = supplierFxUserId;
                productSku.CostPrice = sku.CostPrice;
                productSku.SettlePrice = sku.SettlePrice;
                productSku.DistributePrice = sku.DistributePrice;
                productSku.Weight = sku.Weight ?? 0;
                productSku.IsPublic = false;
                productSku.UpFxUserId = sku.UpFxUserId == null ? 0 : sku.UpFxUserId.Value;
                productSku.UpSkuUid = sku.UpSkuUid;
                productSku.Attributes = attributeJson;
                productSku.IsCombineSku = sku.ChildSkuList != null && sku.ChildSkuList.Count > 0;
                productSku.Status = 1;
                productSku.RootNodeFxUserId = sku.RootNodeFxUserId ?? fxUserId;
                productSku.SharePathCode = sku.SharePathCode;
                productSku.PathNodeDeep = sku.PathNodeDeep ?? 0;
                productSku.CreateTime = now;
                productSku.UpdateTime = now;
                productSku.SourceSkuCode = sku.SourceSkuCode;
                if (isFromSupplierProduct) // 来源货盘复制
                {
                    var findModel = model.ConfigModels.FirstOrDefault(x => x.RefCode == productSku.SkuCode);
                    if (findModel != null)
                    {
                        configList.Add(new BaseProductSkuSupplierConfig
                        {
                            RefType = findModel.RefType,
                            RefUid = productSku.Uid,
                            SkuUid = productSku.Uid,
                            ProductUid = productUid,
                            SupplierFxUserId = findModel.SupplierFxUserId,
                            ApplyScope = findModel.ApplyScope,
                            ConfigType = findModel.ConfigType,
                            CreateTime = DateTime.Now,
                            UpdateTime = DateTime.Now
                        });
                        productSku.SupplierFxUserId = findModel.SupplierFxUserId;
                    }
                }

                //初始库存
                productSku.StockCount = sku.StockCount;
                productSku.ChildSku = sku.ChildSkuList;

                // 自定义规格时给此属性赋值
                if(model.SkuModeType == 1)
                {
                    productSku.AttributeValue = sku.AttributeValue;
                }
                
                if (sku.IsCombineSku == true && sku.ChildSkuList != null && sku.ChildSkuList.Any())
                {
                    productSku.ChildSku = sku.ChildSkuList;
                }

                productSkus.Add(productSku);

                var productSkuShortTitle = new BaseProductSkuShortTitle();
                productSkuShortTitle.BaseProductSkuUid = productSkuUid;
                productSkuShortTitle.BaseProductUid = productUid;
                productSkuShortTitle.ShortTitle = productSku.ShortTitle;
                productSkuShortTitle.CreateTime = now;
                productSkuShortTitle.UpdateTime = now;
                productSkuShortTitle.Status = 1;
                productSkuShortTitles.Add(productSkuShortTitle);

                var productSkuAttribute = new BaseProductSkuAttribute();
                productSkuAttribute.ProductUid = productUid;
                productSkuAttribute.SkuUid = productSkuUid;
                productSkuAttribute.ImageObjectId = oosObjectId;
                productSkuAttribute.ImgAttributeValueNo = sku.Attribute.ImgAttributeValueNo;
                productSkuAttribute.AttributeName1 = sku.Attribute?.AttributeName1;
                productSkuAttribute.AttributeName2 = sku.Attribute?.AttributeName2;
                productSkuAttribute.AttributeName3 = sku.Attribute?.AttributeName3;
                productSkuAttribute.AttributeValue1 = sku.Attribute?.AttributeValue1;
                productSkuAttribute.AttributeValue2 = sku.Attribute?.AttributeValue2;
                productSkuAttribute.AttributeValue3 = sku.Attribute?.AttributeValue3;

                productSkuAttribute.AttributeCode1 = sku.Attribute?.AttributeCode1;
                productSkuAttribute.AttributeCode2 = sku.Attribute?.AttributeCode2;
                productSkuAttribute.AttributeCode3 = sku.Attribute?.AttributeCode3;

                productSkuAttribute.CreateTime = now;
                productSkuAttributes.Add(productSkuAttribute);
                uniqueIdIndex++;

                if (sku.SharePathFlow != null && sku.SharePathFlow.PathFlowNodes != null && sku.SharePathFlow.PathFlowNodes.Count > 0)
                {
                    sharePathFlowList.Add(sku.SharePathFlow);
                    sharePathFlowNodeList.AddRange(sku.SharePathFlow.PathFlowNodes);
                }
            }
            // 保存SUP信息
            var AttributeName1 = productSkuAttributes.FirstOrDefault()?.AttributeName1;
            var AttributeName2 = productSkuAttributes.FirstOrDefault()?.AttributeName2;
            var AttributeName3 = productSkuAttributes.FirstOrDefault()?.AttributeName3;
            var AttributeNames = $"" +
                $"{(IsNullOrEmpty(AttributeName1) ? "" : AttributeName1)}," +
                $"{(IsNullOrEmpty(AttributeName2) ? "" : AttributeName2)}," +
                $"{(IsNullOrEmpty(AttributeName3) ? "" : AttributeName3)}";
            var product = new BaseProductEntity();
            var productMainImage = productImages.FirstOrDefault();
            product.Uid = productUid;
            product.SpuCode = model.SpuCode;
            product.FxUserId = fxUserId;
            product.Subject = model.Subject;
            product.MainImageObjectId = productMainImage == null ? 0 : productMainImage.ImageObjectId;
            product.Description = (productDescImages != null && productDescImages.Count > 0) ? Join(",", productDescImages.Select(a => a.Url)) : null;
            product.ShortTitle = model.ShortTitle;
            product.AttributeNames = AttributeNames;
            product.CreateFrom = model.CreateFrom;
            product.FromProductUid = model.FromProductUid;
            product.FromFxUserId = model.FromFxUserId;
            product.FromShopId = model.FromShopId;
            product.FromShopPt = model.FromShopPt;
            product.CreateTime = now;
            product.UpdateTime = now;
            product.CreateFxUserId = fxUserId;
            product.IsSyncToWarehouse = false;
            product.RootNodeFxUserId = model.RootNodeFxUserId ?? fxUserId;
            product.SharePathCode = model.SharePathCode;
            product.Status = 1;
            product.PathNodeDeep = model.PathNodeDeep ?? 0;
            product.SourceSpuCode = model.SourceSpuCode;
            product.SkuModeType = model.SkuModeType;
            if (isFromSupplierProduct) product.UpBaseProductUid = model.UpBaseProductUid;
            if(productSkus.Count == 1 && product.FromProductUid.IsNotNullOrEmpty())
            {
                var productCode = product.FromProductUid;
                var productSku = new ProductSkuFxService().GetListForDuplication(new List<string> { productCode })?.FirstOrDefault();
                if(productSku != null)
                {
                    // 店铺商品无规格时，会生成一个默认规格（PlatformId == SkuId）
                    if (productSku.PlatformId == productSku.SkuId)
                    {
                        product.SkuModeType = 1;
                    }
                }
            }

            if (productMainImage != null)
            {
                var suffix = productMainImage.Suffix.IsNullOrEmpty() ? string.Empty : $".{productMainImage.Suffix}";
                product.MainImageUrl = $"{productMainImage.Domain}/{productMainImage.Url}/{productMainImage.Name}{suffix}";
            }
            product.Skus = productSkus;
            product.Images = productImages;

            // 保存SUP简称
            var productShortTitle = new BaseProductShortTitle();
            productShortTitle.BaseProductUid = productUid;
            productShortTitle.ShortTitle = product.ShortTitle;
            productShortTitle.CreateTime = now;
            productShortTitle.UpdateTime = now;
            productShortTitle.Status = 1;

            if (isFromSupplierProduct) // 来源货盘复制
            {
                var findModel = model.ConfigModels.FirstOrDefault(x => x.RefCode == product.SpuCode);
                if (findModel != null)
                {
                    configList.Add(new BaseProductSkuSupplierConfig
                    {
                        RefType = findModel.RefType,
                        RefUid = product.Uid,
                        SkuUid = 0,
                        ProductUid = productUid,
                        SupplierFxUserId = findModel.SupplierFxUserId,
                        ApplyScope = findModel.ApplyScope,
                        ConfigType = findModel.ConfigType,
                        CreateTime = DateTime.Now,
                        UpdateTime = DateTime.Now
                    });
                }

                product.FromFxUserId = model.FromFxUserId;
                product.FromProductUid = model.FromProductUid;
            }

            // 保存供货设置
            if (configList.Any()) new BaseProductSkuSupplierConfigRepository().BatchAdd(configList);
            
            if (model.SharePathFlow != null && model.SharePathFlow.PathFlowNodes.Count > 0)
            {
                sharePathFlowList.Add(model.SharePathFlow);
                sharePathFlowNodeList.AddRange(model.SharePathFlow.PathFlowNodes);
            }

            // 更新分享路径流
            if (sharePathFlowList.Any() && sharePathFlowNodeList.Any())
            {
                // 更新到商品库
                new SharePathFlowService(fxUserId).MergeAdd(sharePathFlowList);
                new SharePathFlowNodeService(fxUserId).MergeAdd(sharePathFlowNodeList);
                
                // 更新到货盘库
                new SharePathFlowService().MergeAdd(sharePathFlowList);
                new SharePathFlowNodeService().MergeAdd(sharePathFlowNodeList);
            }
            
            // 基础分库
            new BaseProductRepository().Add(product);
            new BaseProductSkuRepository().BatchAdd(productSkus);
            // new BaseProductImageRepository().BatchAdd(productDescImages);
            new BaseProductSkuAttributeService().BatchAdd(productSkuAttributes);
            if (productImages.Count > 0)
                new BaseProductImageRepository().BatchAdd(productImages);
            if (productSkuSupplierConfigList.Count > 0)
                new BaseProductSkuSupplierConfigRepository().BatchAdd(productSkuSupplierConfigList);
            // 业务分库需要确定频台
            new BaseProductBaseRepository<BaseProductShortTitle>(false).Add(productShortTitle);
            new BaseProductBaseRepository<BaseProductSkuShortTitle>(false).BulkInsert(productSkuShortTitles);

            // 检测仓库如果没有仓库，则默认创建一个
            _service.GenerateDefaultStore(fxUserId);

            //基础商品库-->库存系统
            //只处理有设置库存的SKU
            //不限制库存数，都要同步到库存系统 2024-08-29
            var wareHouseProducts = BaseProductCommonService.BaseProductToWareHouseProduct(new List<BaseProductEntity>() { product });
            wareHouseProducts = wareHouseProducts.Where(a => a.Skus != null && a.Skus.Count > 0).ToList();
            if (wareHouseProducts.Any())
            {
                //wareHouseProducts.ForEach(p => { p.Skus = p.Skus; });
                var result = new WareHouseService().WarehouseProductMerger(wareHouseProducts);
            }
            return product;
        }

        public List<BaseProductEntity> GetAllBaseProductDetail(List<BaseProductDetailReqModel> models, int fxUserId)
        {
            var list = new List<BaseProductEntity>();
            Parallel.ForEach(models, new ParallelOptions { MaxDegreeOfParallelism = 5 }, model =>
            {
                try
                {
                    var product = GetBaseProductDetail(model, fxUserId);
                    list.Add(product);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"批量创建基础资料异常：{ex.Message}，参数：{model.ToJson()}");
                }
            });
            return list;
        }

        public BaseProductEntity GetBaseProductDetail(BaseProductDetailReqModel model, int fxUserId)
        {
            var res = new BaseProductEntity();
            var tuple = new BaseProductSkuRepository().GetBaseProductDetail(model, fxUserId);
            var product = tuple.Item1;
            if (product != null)
            {
                var productSkus = tuple.Item2;
                var productAttribute = tuple.Item3;
                var productSkuConfigs = tuple.Item4;
                var productImages = tuple.Item5;

                res = product;
                res.AttributeTypes = new List<AttributeTypeModel>();
                res.Skus = productSkus;
                res.Images = productImages;
                res.ImagesStr = new List<string>();
                var imageApi = "/Common/GetImageFile?objectKey=";
                var images = productImages.Select(p => ImgHelper.ChangeImgUrl(p.FullUrl)).Where(p => p.IsNotNullOrEmpty()).ToList();
                var imagesKey = images.Select(p => p.Replace(imageApi, string.Empty)).ToList();
                if (images.Count > 0)
                {
                    res.ImagesStr = images;
                    res.ImagesKeyStr = imagesKey;
                }
                #region productSkus里的顺序可能和productAttribute的不一致（数据库查询导致的），这里强制productSkus顺序与productAttribute保持一致
                var skuUids = productSkus?.Select(x => x.Uid).ToList() ?? new List<long>();
                var attrSkuUids = productAttribute?.Select(x => x.SkuUid).ToList() ?? new List<long>();
                // 不相等代表排序对不上
                if (skuUids.Any() && attrSkuUids.Any() && !skuUids.SequenceEqual(attrSkuUids) && skuUids.OrderBy(x => x).SequenceEqual(attrSkuUids.OrderBy(x => x)))
                {
                    var tempBaseProductSkus = new List<BaseProductSku>();

                    attrSkuUids.ForEach(x => {
                        var sku = productSkus.SingleOrDefault(s => s.Uid == x);
                        tempBaseProductSkus.Add(sku);
                    });
                    res.Skus = tempBaseProductSkus;
                }
                #endregion

                if (res.Skus.Count > 0)
                {
                    // 库存数据获取
                    var warehouseService = new WareHouseService();
                    var skuCodes = res.Skus.Select(a => a.SkuCode).Distinct().ToList();
                    var stocks = warehouseService.GetStockBySkuCode(skuCodes);
                    res.Skus.ForEach(sku =>
                    {
                        var configs = productSkuConfigs.Where(a => a.ProductUid == sku.BaseProductUid && a.SkuUid == sku.Uid).ToList();
                        var attributes = productAttribute.Where(a => a.SkuUid == sku.Uid && a.ProductUid == sku.BaseProductUid).FirstOrDefault();
                        sku.BaseProductSkuSupplierConfigs = configs;
                        sku.AttributeModel = attributes;
                        var stock = stocks.Where(s => s.SkuCargoNumber == sku.SkuCode).FirstOrDefault();
                        sku.StockCount = stock?.StockCount;
                        sku.ImageUrl = ImgHelper.ChangeImgUrl(sku.ImageUrl);
                        sku.ImageUrlKey = sku.ImageUrl?.Replace(imageApi, string.Empty);
                    });
                    // 如果有组合货品
                    if (res.Skus?.Any(x => x.IsCombineSku) == true)
                    {
                        var combineSkuCodes = res.Skus.Where(x => x.IsCombineSku).Select(s => s.SkuCode).Distinct().ToList();
                        var combineSkuResponse = warehouseService.GetSkuContainChildListByCargoNumber(combineSkuCodes);

                        res.Skus.ForEach(sku =>
                        {
                            if (sku.IsCombineSku)
                            {
                                sku.ChildSku = combineSkuResponse?.Skus?.Where(x=>x.SkuCargoNumber == sku.SkuCode).FirstOrDefault()?.ChildSkuList.ToJson()?.ToObject<List<BaseProductSkuChildModel>>() ?? new List<BaseProductSkuChildModel>();
                            }
                        });
                    }
                }
                
                // 规格类型接口处理
                if (productAttribute.Count > 0)
                {
                    foreach (var item in productAttribute)
                    {
                        var url = res.Skus.FirstOrDefault(sku => sku.ImageObjectId == item.ImageObjectId)?.ImageUrl;
                        item.ValueUrl = ImgHelper.ChangeImgUrl(url);
                        item.ValueUrlKey = item.ValueUrl?.Replace(imageApi, string.Empty);
                    }
                    #region 类型数据组装
                    var type1 = productAttribute
                        .Where(a => !IsNullOrEmpty(a.AttributeName1))
                        .GroupBy(a => a.AttributeName1)
                        .Select(a => new AttributeTypeModel
                        {
                            AttributeName = a.Key,
                            AttributeValues = a.Select(b => new AttributeValueModel {
                                ImgAttributeValueNo =b.ImgAttributeValueNo,
                                Value = b.AttributeValue1 ,
                                ValueUrl= b.ValueUrl,
                                ValueUrlKey = b.ValueUrlKey,
                                ImageObjectId = b.ImageObjectId,
                                UniCode = b.AttributeCode1,
                            }).GroupBy(m=>m.Value).Select(m=>m.FirstOrDefault()).ToList()
                        }).ToList();
                    var type2 = productAttribute
                        .Where(a => !IsNullOrEmpty(a.AttributeName2))
                        .GroupBy(a => a.AttributeName2)
                        .Select(a => new AttributeTypeModel
                        {
                            AttributeName = a.Key,
                            AttributeValues = a.Select(b => new AttributeValueModel {
                                ImgAttributeValueNo = b.ImgAttributeValueNo,
                                Value = b.AttributeValue2,
                                ValueUrl = b.ValueUrl,
                                ValueUrlKey = b.ValueUrlKey,
                                ImageObjectId = b.ImageObjectId,
                                UniCode = b.AttributeCode2,
                            }).GroupBy(m => m.Value).Select(m => m.FirstOrDefault()).ToList()
                        }).ToList();
                    var type3 = productAttribute
                        .Where(a => !IsNullOrEmpty(a.AttributeName3))
                        .GroupBy(a => a.AttributeName3)
                        .Select(a => new AttributeTypeModel
                        {
                            AttributeName = a.Key,
                            AttributeValues = a.Select(b => new AttributeValueModel {
                                ImgAttributeValueNo = b.ImgAttributeValueNo,
                                Value = b.AttributeValue3,
                                ValueUrl = b.ValueUrl,
                                ValueUrlKey = b.ValueUrlKey,
                                ImageObjectId = b.ImageObjectId,
                                UniCode = b.AttributeCode3,
                            }).GroupBy(m => m.Value).Select(m => m.FirstOrDefault()).ToList()
                        }).ToList();
                    #endregion
                    res.AttributeTypes.AddRange(type1);
                    res.AttributeTypes.AddRange(type2);
                    res.AttributeTypes.AddRange(type3);
                }
                // 详情图片格式
                // 在您的逻辑中使用新方法
                if (!IsNullOrEmpty(product.Description))
                {
                    res.Descriptions = new List<BaseProductImage>();
                    var imageList = product.Description.Split(',').Where(a => a.IsNotNullOrEmpty()).Distinct().ToList();
                    if (imageList.Count > 0)
                        imageList = imageList.Select(img => img = ImgHelper.ChangeImgUrl(img)).ToList();
                    res.Description = string.Join(",", imageList);
                    res.DescriptionsStr = imageList;
                    res.DescriptionsKeyStr = imageList.Select(p => p.Replace(imageApi, string.Empty)).ToList();
                }

                res.IsCombination = res.Skus.Any(x => x.IsCombineSku) ? 1 : 0;
                //if (product.Description.IsNotNullOrEmpty())
                //{
                //    var descriptImages = product.Description.Split(',').Where(a=>a.IsNotNullOrEmpty()).ToList();
                //    res.Descriptions = new List<BaseProductImage>();
                //    var reg = @"[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+";
                //    descriptImages.ForEach(desc =>
                //    {
                //        var domain = Regex.Match(desc, reg).Value;
                //        var suffix = desc.Substring(desc.LastIndexOf(".") + 1);
                //        var name = desc.Substring(desc.LastIndexOf("/") + 1).Replace($".{suffix}", string.Empty);
                //        var url = desc.Replace($"{domain}/", string.Empty).Replace($"/{name}.{suffix}", string.Empty);

                //        var data = new BaseProductImage();
                //        data.ProductUid = res.Uid;
                //        data.Url = url;
                //        data.Domain = domain;
                //        data.Status = 1;
                //        data.CreateTime = product.CreateTime;
                //        data.Name = name;
                //        data.ImageObjectId = 0;
                //        data.Suffix = suffix;

                //        res.Descriptions.Add(data);
                //    });
                //}
                // 主图图片格式
                //if (res.Images.Count > 0)
                //{
                //    var ossObjectids = res.Images.Select(a => a.ImageObjectId.ToString()).Distinct().ToList();
                //    var ossObjects = new OssObjectRepository().GetList(ossObjectids, "*", "Id");
                //    res.Images.ForEach(image =>
                //    {
                //        var ossObject = ossObjects.FirstOrDefault(a => a.Id == image.ImageObjectId);
                //        image.Name = ossObject?.Name;
                //        image.Suffix = ossObject?.Suffix;
                //        image.Url = ossObject?.Url;
                //        image.Domain = ossObject?.Domain;
                //    });
                //}
            }
            return res;
        }

        private void ParseAndAddDescriptionImages(string description, List<string> images, long productUid, DateTime createTime)
        {
            if (IsNullOrEmpty(description)) return;

            var imageUrls = description.Split(',').Where(s => !IsNullOrEmpty(s)).ToList();

            imageUrls.ForEach(url =>
            {
                if (url.IsNotNullOrEmpty())
                {
                    images.Add(url);
                }
            });
        }

        
        public void FiltedBaseProductSku(Dictionary<long,List<long>> skuUids,int fxUserId,List<long> noCheckedSkuUids,List<long> noCheckedProductUids)
        {
            var pUids = skuUids.Keys.ToList();
            pUids = pUids.Except(noCheckedProductUids).ToList();
            
            var checkedSkuUids = skuUids.SelectMany(s => s.Value).ToList();
            var productSkuDict = _repository.GetBaseProductAndSkuDict(pUids, fxUserId);

            foreach (var pUid in productSkuDict.Keys)
            {
                var addSkuUids = productSkuDict[pUid];
                var reqSkuUids = new List<long>();
                if (skuUids.TryGetValue(pUid, out var sUids))
                {
                    reqSkuUids = sUids.Distinct().ToList();
                    sUids.AddRange(addSkuUids);
                }

                // 按照规格条件搜索后，此时全选并未全选，nocheckedSkuUids不存在数据
                if (noCheckedSkuUids.IsNullOrEmptyList())
                {
                    sUids = sUids.Where(p=> reqSkuUids.Contains(p)).Distinct().ToList();
                }   

                skuUids[pUid] = sUids.Except(noCheckedSkuUids).Distinct().ToList();

                if (skuUids[pUid].IsNullOrEmptyList())
                {
                    skuUids.Remove(pUid);
                }
            }
        }

        /// <summary>
        /// 获取基础商品sku信息
        /// </summary>
        /// <param name="productUid"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BaseProductSku> GetListByProductUid(long productUid, int fxUserId)
        {
            return _repository.GetListByProductUid(productUid, fxUserId);
        }

        /// <summary>
        /// 获取基础商品sku数量
        /// </summary>
        /// <param name="productUid"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public int GetCountByProductUid(long productUid, int fxUserId)
        {
            return _repository.GetCountByProductUid(productUid, fxUserId);
        }


        /// <summary>
        /// 根据SkuUids查询
        /// </summary>
        /// <param name="skuUids"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BaseProductSku> GetListBySkuUids(List<long> skuUids, int fxUserId,string fields = "*")
        {
            return _repository.GetListBySkuUids(skuUids, fxUserId,fields);
        }

        /// <summary>
        /// 根据SkuUids查询
        /// </summary>
        /// <param name="skuUids"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BaseProductSku> GetSkuByProductUids(List<long> productUid, int fxUserId)
        {
            return _repository.GetSkuByProductUids(productUid, fxUserId, null);
        }

        /// <summary>
        /// 根据SkuUids查询（包含删除的SKU）
        /// </summary>
        /// <param name="skuUids"></param>
        /// <param name="fxUserId"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public List<BaseProductSku> GetListContainDelBySkuUids(List<long> skuUids, int fxUserId, string selectFields = "*")
        {
            return _repository.GetListContainDelBySkuUids(skuUids, fxUserId, selectFields);
        }

        /// <summary>
        /// 批量更新，逻辑删除
        /// </summary>
        /// <param name="skuEntList"></param>
        public void BulkDelete(List<BaseProductSku> skuEntList)
        {
            _repository.BulkDelete(skuEntList);
        }

        /// <summary>
        /// 批量更新，逻辑删除；
        /// </summary>
        /// <param name="skuUids"></param>
        public void BulkDelete(List<long> skuUids)
        {
            _repository.BulkDelete(skuUids);
        }

        public ReturnedModel Delete(long uid, int fxUserId)
        {
            var result = new ReturnedModel();

            if (uid <= 0)
            {
                result.Success = false;
                result.Message = "请选择要删除的商品Sku";
                return result;
            }

            var baseProductSkuService = new BaseProductSkuService();
            var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService();

            // 获取sku信息
            var skuEnt = baseProductSkuService.GetByUid(uid);
            if (skuEnt == null || skuEnt.FxUserId != fxUserId)
            {
                result.Success = false;
                result.Message = "未找到要删除的商品Sku";
                return result;
            }

            var removeSkuUids = new List<long> { uid };
            // 获取关联信息
            var baseSkuPtRelation = baseOfPtSkuRelationService.GetListBySkuUids(removeSkuUids, fxUserId);
            if (baseSkuPtRelation != null && baseSkuPtRelation.Any())
            {
                result.Success = false;
                result.Message = "此商品规格中，有与店铺商品关联的数据，请解除关联再进行删除";
                return result;
            }

            var skuCodes = new List<string>() { skuEnt.SkuCode };

            //处理库存系统删除规格,规格删除前如果其在组合货品中，则不允许删除
            //删除库存系统里的货品
            var delRequest = new WareHouseSkuDeleteRequest { SkuCodes = skuCodes, isFormBaseProduct = true };
            var deleteResponse = new WareHouseService().SkuDelete(delRequest);
            Log.Debug(() => $"删除货品规格Delete：{string.Join(",", skuCodes)},结果={deleteResponse?.ToJson()}", "warehouse.txt");
            
            if (deleteResponse.IsSucc && deleteResponse.IsSuccess)
            {
                baseProductSkuService.BulkDelete(removeSkuUids);
                // 同步删除平台资料的Sku
                new PtProductInfoService().DeletePtSku(skuEnt, fxUserId);

                // 同步删除小站资料的Sku
                new SupplierProductService().DelSku(skuEnt.Uid, fxUserId);
            }
            else
            {
                if (deleteResponse.IsSucc)
                {
                    result.Success = false;
                    result.Message = deleteResponse.ErrorMessage;
                    return result;
                }
                else
                    throw new Exception(deleteResponse.Message);//系统级错误提示
            }

            result.Success = true;
            return result;
        }

        /// <summary>
        /// 基础商品解绑（从BaseProductController移过来）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public CheckResult BaseProductSkuRelationUnbind(BaseProductSkuUnbindModel model)
        {
            var result = new CheckResult();
            #region 解绑基础
            if (string.IsNullOrEmpty(model.SkuCode))
            {
                result.Message = "基础商品SkuCode不能为空！";
                return result;
            }
            if (string.IsNullOrEmpty(model.ProductCode))
            {
                result.Message = "关联商品ProductCode不能为空！";
                return result;
            }
            if (string.IsNullOrEmpty(model.ProductSkuCode))
            {
                result.Message = "关联商品ProductSkuCode不能为空！";
                return result;
            }
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var baseProductSkuService = new BaseProductSkuService();
            var baseProductSkuConfigService = new BaseProductSkuSupplierConfigService();
            var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService(false);
            var messageService = new MessageRecordService();
            var ptProductSkuService = new ProductSkuFxService();
            var dbName = model.ProductDbName;

            // 平台商品
            var ptProductSkus = ptProductSkuService.GetProductSkuBySkuCode(new List<string> { model.ProductSkuCode });
            if (ptProductSkus.Count == 0)
            {
                result.Message = "未找到基础商品信息绑定的平台商品！";
                return result;
            }
            var sku = ptProductSkus.FirstOrDefault();

            var relations = baseOfPtSkuRelationService.GetListBySkuModel(new List<long> { model.BaseProductSkuUid }, new List<string> { model.ProductSkuCode }, fxUserId);
            if (relations.Count == 0)
            {
                result.Message = "未找到对应的绑定关系！";
                return result;
            }
            relations.ForEach(r =>
            {
                r.UpdateTime = DateTime.Now;
                r.Status = 0;
            });
            var relation = relations.FirstOrDefault();

            var subModel = new MessageRecord();
            subModel.MsgType = BaseProductMsgType.BaseOfPtSkuRelationDel;
            subModel.FxUserId = fxUserId;
            subModel.DbName = dbName;
            subModel.ProductPlatformType = CloudPlatformType.Alibaba.ToString();
            subModel.BusinessId = "";
            subModel.DataJson = relation.ToJson();

            // 平台商品绑定
            baseOfPtSkuRelationService.BulkUpdate(relations);

            // 基础商品解绑 
            var res = messageService.SendBusinessMessage(new List<MessageRecord> { subModel });

            // 解绑日志
            baseOfPtSkuRelationService.SetUnBindRelationRecords(new List<BaseProductSkuUnbindModel>(){model}, fxUserId);
            #endregion

            // 库存系统解绑关联关系
            if (model.IsAllUseWarehouse)
            {
                Task.Run(() =>
                {
                    var connectionString = SiteContext.Current.CurrentDbConfig.ConnectionString;
                    var _wareHouseService = new WareHouseService(connectionString);
                    var curUserId = SiteContext.Current.CurrentFxUserId;
                    var ownCode = _wareHouseService.GetOwnerCode(curUserId);
                    // 获取库存系统数据的业务库绑定关系
                    var wareHouseSkuBindRelation = new WareHouseSkuBindRelationRepository(connectionString).GetBindRelation(ownCode, sku.SkuCode, sku.PlatformType);
                    if (wareHouseSkuBindRelation == null) return;
                    var req = new WarehouseSkuUnbindRequest()
                    {
                        IsRollback = false,
                        OwnerCode = ownCode,
                        SkuBindRelationCode = wareHouseSkuBindRelation.SkuBindRelationCode
                    };

                    var rsp = _wareHouseService.UnBindProduct(req, true);
                    if (CustomerConfig.IsDebug) Log.WriteLine($"基础商品到库存系统解绑关联关系：{rsp.Message}");
                });
            }

            // 清空简称、结算价、成本价，平台商品设为自营
            // 设为自营
            var tuple = baseProductSkuService.GetUnBindSkuRestoreModel(model, fxUserId, relation);
            var bindModel = tuple.Item1;
            var modifyModel = tuple.Item2;
            if (bindModel.IsNotNullOrEmpty())
            {
                messageService.SendBusinessMessage(new List<MessageRecord>()
                {
                    new MessageRecord()
                    {
                        MsgType = BaseProductMsgType.BindSupplier,
                        FxUserId = fxUserId,
                        ProductPlatformType = relation.ProductPlatformType,
                        DbName = dbName,
                        DataJson = bindModel.ToJson(),
                    }
                });
            }

            // 清空简称、结算价、成本价
            messageService.SendBusinessMessage(new List<MessageRecord>()
            {
                new MessageRecord()
                {
                    MsgType = BaseProductMsgType.SyncPtSku,
                    FxUserId = fxUserId,
                    ProductPlatformType = relation.ProductPlatformType,
                    DbName = dbName,
                    DataJson = modifyModel.ToJson(),
                }
            });

            result.Success = true;
            return result;
        }

        public List<BaseProductSku> SortBaseProductSku(List<BaseProductSku> skus)
        {
            foreach (var sku in skus)
            {
                if (sku.Attributes.IsNotNullOrEmpty())
                {
                    try
                    {
                        //var aa = "[{\"k\":\"套装明细\",\"v\":\"库勒米 37\"}]";
                        //var bb = aa.ToObject<List<SkuAttr>>();
                        var attrs = sku.Attributes.ToObject<List<SkuAttr>>();
                        var attributeGroup = attrs.GroupBy(a => a.k).ToList();
                        var attributeGroup1 = attributeGroup.Skip(0).Take(1).FirstOrDefault();
                        sku.SortField = attributeGroup1?.ToList().FirstOrDefault()?.v;
                    }
                    catch (Exception)
                    {
                        sku.SortField = "";
                    }
                }
                else
                {
                    sku.SortField = "";
                }
            }
            skus = skus.OrderBy(p => p.SortField).ThenByDescending(p => p.Id).ToList();
            return skus;
        }

        /// <summary>
        /// 价格范围处理
        /// </summary>
        /// <param name="max"></param>
        /// <param name="min"></param>
        /// <returns></returns>
        public string MakePrice(decimal? max,decimal? min)
        {
            string res = string.Empty;

            if(max == null && min == null)
            {
                res = string.Empty;
            }
            if (max != null && min == null)
            {
                res = $"{max}";
            }
            if (max == null && min != null)
            {
                res = $"{min}";
            }
            if (max != null && min != null)
            {
                if(max == min)
                {
                    res = $"{min}";
                }
                else
                {
                    res = $"{min}-{max}";
                }
            }
            return res;
        }

        /// <summary>
        /// 厂家标识
        /// </summary>
        /// <param name="p"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public int? MakeSupplierTag(BaseProductEntity p, int fxUserId)
        {
            // 供货方式 0: 未设置， 1：自营、2：多厂家、3:自营 + 厂家供货、4：自营 + 多厂家
            // 0 未设置 -1 自营  大于 0 厂家

            // 未设置
            if (p.ProductSupplierUserId == 0)
            {
                if (p.Skus.All(a => a.SkuSupplierUserId == 0))
                    p.SupplyMethod = 0;
                else if (p.Skus.All(a => a.SkuSupplierUserId == fxUserId))
                    p.SupplyMethod = 1;
                else if (p.Skus.All(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId))
                    p.SupplyMethod = 2;
                else if (p.Skus.Exists(a => a.SkuSupplierUserId == fxUserId) && p.Skus.Where(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId).ToList().Count == 1)
                    p.SupplyMethod = 3;
                else if (p.Skus.Exists(a => a.SkuSupplierUserId == fxUserId) && p.Skus.Where(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId).ToList().Count == 0)
                    p.SupplyMethod = 1;
                else if (p.Skus.Where( a => a.SkuSupplierUserId == fxUserId).ToList().Count == 0 && p.Skus.Exists(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId))
                    p.SupplyMethod = 2;
                else if (p.Skus.Exists(a => a.SkuSupplierUserId == fxUserId) && p.Skus.Exists(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId))
                    p.SupplyMethod = 4;
            }
            // 自营
            if (p.ProductSupplierUserId == fxUserId)
            {
                if (p.Skus.All(a => a.SkuSupplierUserId == fxUserId))
                    p.SupplyMethod = 1;
                else if (p.Skus.All(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId))
                    p.SupplyMethod = 4;
                else if (p.Skus.Exists(a => a.SkuSupplierUserId == fxUserId) && p.Skus.Where(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId).ToList().Count == 1)
                    p.SupplyMethod = 3;
                else if (p.Skus.Exists(a => a.SkuSupplierUserId == fxUserId) && p.Skus.Where(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId).ToList().Count == 0)
                    p.SupplyMethod = 1;
                else if (p.Skus.Where(a => a.SkuSupplierUserId == fxUserId).ToList().Count == 0 && p.Skus.Exists(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId))
                    p.SupplyMethod = 4;
                else if (p.Skus.Exists(a => a.SkuSupplierUserId == fxUserId) && p.Skus.Exists(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId))
                    p.SupplyMethod = 4;
            }
            // 厂家
            if (p.ProductSupplierUserId > 0 && p.ProductSupplierUserId != fxUserId)
            {
                if (p.Skus.All(a => a.SkuSupplierUserId == fxUserId))
                    p.SupplyMethod = 3;
                else if (p.Skus.All(a => a.SkuSupplierUserId <=0))
                    p.SupplyMethod = 2;
                else if (p.Skus.All(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId))
                    p.SupplyMethod = 2;
                else if (p.Skus.Exists(a => a.SkuSupplierUserId == fxUserId) && p.Skus.Where(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId).ToList().Count == 1)
                    p.SupplyMethod = 4;
                else if (p.Skus.Exists(a => a.SkuSupplierUserId == fxUserId) && p.Skus.Where(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId).ToList().Count == 0)
                    p.SupplyMethod = 3;
                else if (p.Skus.Where(a => a.SkuSupplierUserId == fxUserId).ToList().Count == 0 && p.Skus.Exists(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId))
                    p.SupplyMethod = 3;
                else if (p.Skus.Exists(a => a.SkuSupplierUserId == fxUserId) && p.Skus.Exists(a => a.SkuSupplierUserId > 0 && a.SkuSupplierUserId != fxUserId))
                    p.SupplyMethod = 4;
            }
            return p.SupplyMethod;
        }


        /// <summary>
        /// 资料标识
        /// </summary>
        /// <param name="supplierProductInfos"></param>
        /// <param name="baseOfPtInfos"></param>
        /// <returns></returns>
        public List<PlatformInfo> MakeInfoTage( List<BaseOfSupplierProductSkuRelation> supplierProductInfos, List<PtProductInfo> baseOfPtInfos)
        {
            var platformInfos = new List<PlatformInfo>();
          
            if (supplierProductInfos == null || supplierProductInfos.Count == 0)
            {
                // 默认值
                var data = new PlatformInfo();
                data.BaseProductUid = 0;
                data.Uid = 0;
                data.PlatformType = "PuHuo";
                data.IsHighlight = false;
                platformInfos.Add(data);
            }
            else
            {
                foreach (var s in supplierProductInfos)
                {
                    var data = new PlatformInfo();
                    data.BaseProductUid = s.BaseProductUid;
                    data.Uid = s.SupplierProductUid;
                    data.PlatformType = "PuHuo";
                    data.IsHighlight = true;
                    platformInfos.Add(data);
                }
            }

            if (baseOfPtInfos == null || baseOfPtInfos.Count == 0)
            {
                // 默认值
                var data = new PlatformInfo();
                data.BaseProductUid = 0;
                data.Uid = 0;
                data.PlatformType = "TouTiao";
                data.IsHighlight = false;
                platformInfos.Add(data);
            }
            else {
                foreach (var pt in baseOfPtInfos)
                {
                    var data = new PlatformInfo();
                    data.BaseProductUid = pt.BaseProductUid;
                    data.Uid = pt.BaseProductUid;
                    data.PlatformType = pt.PlatformType;
                    data.IsHighlight = true;
                    platformInfos.Add(data);
                }
            }
            return platformInfos;
        }
        
        /// <summary>
        /// 通过商品SkuUids获取基础商品sku信息，包含已删除的，不限制用户
        /// </summary>
        /// <param name="skuUids"></param>
        /// <returns></returns>
        public List<BaseProductSku> GetListByUids(List<long> skuUids)
        {
            return _repository.GetListByUids(skuUids);
        }
        
        /// <summary>
        /// 根据商品Uid获取SkuCode
        /// </summary>
        /// <param name="productUids"></param>
        /// <returns></returns>
        public Dictionary<long, List<string>> GetDicSkuCodeByProductUids(List<long> productUids)
        {
            return _repository.GetDicSkuCodeByProductUids(productUids);
        }

        /// <summary>
        /// 检查是否存在关联关系
        /// </summary>
        /// <param name="skuCode"></param>
        /// <param name="curFxUserId"></param>
        /// <returns>true：通过，false：不通过即存在关联关系</returns>
        public bool CheckRelationExist(string skuCode, int curFxUserId,out List<BaseOfPtSkuRelation> relations)
        {
            relations = new List<BaseOfPtSkuRelation>();
            if (IsNullOrEmpty(skuCode)) return true;
            
            // 根据SpuCode获取基础商品
            var baseProductEntities = _repository.GetList(new List<string> { skuCode });
            var ent = baseProductEntities.FirstOrDefault(x => x.FxUserId == curFxUserId && x.Status == 1);
            if (ent == null) return true;
            
            // 获取关联关系
            var uid = ent.Uid;
            var baseOfPtSkuRelationRepository = new BaseOfPtSkuRelationRepository();
            relations = baseOfPtSkuRelationRepository.GetListBySkuUid(uid,1);
            return !relations.Any();
        }

        /// <summary>
        /// 批量更新
        /// </summary>
        /// <param name="models"></param>
        public void BulkUpdate(List<BaseProductSku> models)
        {
            if (models == null || models.Count == 0) return;
            _repository.BulkUpdate(models);
        }

        /// <summary>
        /// 通过组合货品获取创建模型
        /// </summary>
        /// <param name="model"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public BaseProductEntity GetCreateModelByCombine(ProductSkuCombinationRequest model, int curFxUserId)
        {
            if (model?.ChildSkuCodes == null || model.ChildSkuCodes.Count == 0) return null;
            // 不算删除的
            if (model.ChildSkuCodes.Count(x => x.IsDeleted == false) > 50) throw new LogicException("组合货品最多支持50个子货品");
            
            var result = new BaseProductEntity();
            var sku = new BaseProductSku();

            // 组装基础商品信息
            var ownerCode = _wareHouseService.GetOwnerCode(curFxUserId);
            var wService = new Warehouse.Services.WareHouseService();
            var skuCode = wService.GenerateCargoNumber(ownerCode, GenerateCargoNumberType.Sku, "ZHHP-SKU", 1).FirstOrDefault();
            var spuCode = wService.GenerateCargoNumber(ownerCode, GenerateCargoNumberType.Product, "ZHHP", 1).FirstOrDefault();
            
            // 将model.SkuName的长度限制在60字符以内
            model.SkuName = model.SkuName.Length > 100 ? model.SkuName.Substring(0, 60) : model.SkuName;
            // SkuProperty限制在64字符以内
            model.SkuProperty = model.SkuProperty.Length > 100 ? model.SkuProperty.Substring(0, 64) : model.SkuProperty;
            
            result.SpuCode = spuCode;
            result.Subject = model.SkuName;
            result.IsCombination = 1;
            result.Skus = new List<BaseProductSku> { sku };

            sku.SkuCode = skuCode;
            sku.IsCombineSku = true;
            sku.ChildSku = new List<BaseProductSkuChildModel>();
            sku.AttributeModel = new BaseProductSkuAttribute
            {
                AttributeName1 = "无规格",
                AttributeValue1 = model.SkuProperty
            };
            sku.AttributeValue = model.SkuProperty;
            sku.Attributes = "[{\"k\":\"无规格\",\"v\":\"" + model.SkuProperty + "\"}]";
            sku.CostPrice = model.CostPrice;
            model.ChildSkuCodes.ForEach(childSku =>
            {
                var child = new BaseProductSkuChildModel
                {
                    Count = childSku.Count,
                    Name = childSku.Name,
                    WareHouseSkuCode = childSku.ChildWareHouseSkuCode,
                    IsDeleted = childSku.IsDeleted,
                    IsNew = childSku.IsNew,
                    OldCount = childSku.OldCount
                };
                sku.ChildSku.Add(child);
            }); 
            
            // 处理图片，只处理前20张
            var imgList = model.ChildSkuCodes
                .Where(x => x.IsDeleted == false && IsNullOrEmpty(x.ImageUrl) == false)
                .Select(x =>ImgHelper.GetRealPath(x.ImageUrl))
                .Take(20)
                .ToList();
            
            // 拼图逻辑，每四张一组处理
            var time = imgList.Count / 4 + (imgList.Count % 4 == 0 ? 0 : 1);
            var imgListNew = new List<string>();
            var bucketName = CustomerConfig.GetBucketNameByBusinessType("Alibaba", "BaseProduct");
            var userDirectory = curFxUserId.ToString().ToShortMd5();

            for (var i = 0; i < time; i++)
            {
                var img = imgList.Skip(i * 4).Take(4).ToList();
                Bitmap collage = null;
                try
                {
                    var bitmapList = img.Select(x => new Bitmap(GetImgStream(x))).ToList();
                    collage = ImgHelper.CreateCollage(bitmapList, 600, 600);
                }
                catch (Exception e)
                {
                    Log.WriteError($"拼图失败，错误：{e.Message}，图片地址：{Join(",", img)}, 堆栈:{e.StackTrace}");
                }

                if (collage == null) continue;
                // 将Bitmap转换为byte[]
                var imageBytes = ImgHelper.BitmapToBytes(collage);
                // 获取第一张图片的文件名
                var filename = Path.GetFileName(new Uri(img.First()).LocalPath);
                // 后缀
                var suffix = Path.GetExtension(filename).TrimStart('.');
                // 精选保存路径方式：文件名={用户目录}/{Guid().ToShortMd5()}.文件后缀
                var filePath = $"{userDirectory}/{Guid.NewGuid().ToString().ToShortMd5()}.{suffix}";
                var imgResult = new AliyunOSSStorage().UploadFile(bucketName, filePath, imageBytes);
                if (imgResult.Success == false)
                {
                    Log.WriteError($"图片上传OSS失败，错误：{imgResult.Message}");
                    continue;
                }
                imgListNew.Add(ImgHelper.GetImgeTransitUrl(imgResult.Data));
            }

            if (imgListNew.Any())
            {
                result.ImagesStr = imgListNew;
                result.ImagesKeyStr = imgListNew.Select(x => x.Replace("/Common/GetImageFile?objectKey=", Empty)).ToList();
                result.Images = imgListNew.Select(x =>
                {
                    var path = ImgHelper.GetRealPath(x);
                    var pathStr = ImgHelper.SplitImageUrl(path);
                    var pathModel = pathStr.ToObject<BaseProductImageModel>();

                    if (pathModel != null)
                    {
                        return new BaseProductImage
                        {
                            Domain = pathModel.Domain,
                            Name = pathModel.Name,
                            Status = 1,
                            Suffix = pathModel.Suffix,
                            Url = pathModel.Url
                        };
                    }

                    return null;
                }).ToList();
            }

            return result;
        }

        /// <summary>
        /// 获取图片流
        /// </summary>
        /// <param name="imgUrl"></param>
        /// <returns></returns>
        public static Stream GetImgStream(string imgUrl)
        {
            Stream httpGetStream = null;
            try
            {
                var isNeedToObj = ImgHelper.NeedsConversion(imgUrl);
                if (!isNeedToObj)
                {
                    httpGetStream = HttpMethods.HttpGetStream(imgUrl, Encoding.UTF8);
                }
                else
                {
                    imgUrl = ImgHelper.GetRealPath(imgUrl);
                    var objectStorage = CloudStorageUploaderFactory.GetObjectStorage("Alibaba", imgUrl);
                    var base64String = Convert.ToBase64String(objectStorage);
                    httpGetStream = new MemoryStream(Convert.FromBase64String(base64String));
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"获取图片流失败，图片地址：{imgUrl}，错误：{e.Message}");
            }

            return httpGetStream;
        }

        /// <summary>
        /// 更新组合商品
        /// </summary>
        /// <param name="model"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public bool UpdateCombineProduct(ProductSkuCombinationRequest model, int curFxUserId)
        {
            // 获取对应的基础商品SKu以及基础商品信息
            var baseProductSkuService = new BaseProductSkuService();
            var baseProductService = new BaseProductEntityService();
            var baseProductImgRepository = new BaseProductImageRepository();
            var ossObjectService = new OssObjectService();
            var baseAttrService = new BaseProductSkuAttributeService();
            var relationService = new BaseOfPtSkuRelationService();
            
            // 获取已存在的基础商品相关信息
            var baseSku = baseProductSkuService.GetByUid(model.SkuUid);
            if (baseSku == null && IsNullOrEmpty(model.SkuCargoNumber) == false)
            {
                // 如果SkuUid为空，则通过SkuCargoNumber获取（货品列表）
                baseSku = baseProductSkuService.GetList(new List<string> { model.SkuCargoNumber }).Where(p=>p.Status==1).FirstOrDefault();
            }
            
            // 检查基础商品信息
            if (baseSku == null || baseSku.FxUserId != curFxUserId || baseSku.Status == 0) throw new LogicException("未找到对应的基础商品SKU");
            if (baseSku.IsCombineSku == false) throw new LogicException("非组合商品SKU，无法更新");
            var baseProduct = baseProductService.GetByUid(baseSku.BaseProductUid);
            var baseSkuList = baseProductSkuService.GetListByProductUid(baseProduct.Uid, curFxUserId);
            if (baseProduct == null || baseProduct.FxUserId != curFxUserId || baseProduct.Status == 0) throw new LogicException("未找到对应的基础商品");
            var imgList = baseProductImgRepository.GetListByProductUid(new List<long> { baseProduct.Uid });
            var attr = baseAttrService.GetList(new List<long> { baseSku.Uid }).FirstOrDefault(x => x.SkuUid == baseSku.Uid);
            
            // 准备变量
            var updateImgList = new List<BaseProductImage>();
            var addImgList = new List<BaseProductImage>();
            var now = DateTime.Now;
            
            var createModel = GetCreateModelByCombine(model, curFxUserId);
            if (createModel == null) throw new LogicException("组合商品构建失败");
            
            // 更新基础商品信息
            baseProduct.UpdateTime = DateTime.Now;
            baseProduct.Subject = createModel.Subject;
            baseSku.AttributeValue = createModel.Skus.First().AttributeValue;
            baseSku.Attributes = createModel.Skus.First().Attributes;
            baseSku.ChildSku = createModel.Skus.First().ChildSku;
            var newImgList = createModel.Images;
            imgList.ForEach(x => x.Status = 0);
            updateImgList.AddRange(imgList);
            // 处理主图
            if (newImgList != null && newImgList.Count > 0)
            {
                newImgList.ForEach(x => x.Status = 1);
                foreach (var image in newImgList)
                {
                    var ossObject = new OssObject
                    {
                        Url = image.Url,
                        Domain = image.Domain,
                        Suffix = image.Suffix,
                        Name = image.Name,
                        CreateTime = now
                    };
                    var id = ossObjectService.Add(ossObject);

                    var productImage = new BaseProductImage
                    {
                        ProductUid = baseProduct.Uid,
                        ImageObjectId = id,
                        Url = image.Url,
                        Name = image.Name,
                        Domain = image.Domain,
                        Suffix = image.Suffix,
                        CreateTime = now,
                        Status = 1
                    };
                    addImgList.Add(productImage);
                }
            }
            baseProduct.MainImageObjectId = addImgList.FirstOrDefault()?.ImageObjectId ?? 0;
            baseProduct.Images = addImgList;

            if (attr == null)
            {
                var newAttr = createModel.Skus.First().AttributeModel;
                newAttr.CreateTime = now;
                newAttr.ProductUid = baseProduct.Uid;
                newAttr.SkuUid = baseSku.Uid;
                baseAttrService.Add(newAttr);
            }
            else
            {
                attr.AttributeName1 = createModel.Skus.First().AttributeModel.AttributeName1;
                attr.AttributeValue1 = createModel.Skus.First().AttributeModel.AttributeValue1;
                baseAttrService.Update(attr);
            }
            
            // 如果Sku有关联关系，则一并处理
            var skuUids = new List<long> { baseSku.Uid };
            var relations = relationService
                .GetListBySkuUids(skuUids, curFxUserId)
                .SelectMany(d => d.Value)
                .ToList();
            relations.ForEach(relation =>
            {
                relation.BaseProductSubject = baseProduct.Subject;
                relation.BaseProductSkuSubject = baseSku.AttributeValue;
                relation.Type = 2;
                relation.UpdateTime = now;
            });
                
            new BaseProductSkuCommonService().SyncRelationInfo(relations, curFxUserId);
            if (updateImgList.Any()) baseProductImgRepository.BulkUpdate(updateImgList);
            if (addImgList.Any()) baseProductImgRepository.BulkInsert(addImgList);
            baseProductService.Update(baseProduct);
            baseProductSkuService.Update(baseSku);
            
            // 处理库存系统
            baseSkuList.First(x => x.Uid == baseSku.Uid).AttributeValue = baseSku.AttributeValue;
            baseSkuList.First(x => x.Uid == baseSku.Uid).Attributes = baseSku.Attributes;
            baseSkuList.First(x => x.Uid == baseSku.Uid).ChildSku = baseSku.ChildSku;
            baseProduct.Skus = baseSkuList;
            var wareHouseProducts = BaseProductCommonService.BaseProductToWareHouseProduct(new List<BaseProductEntity> { baseProduct });
            wareHouseProducts = wareHouseProducts.Where(a => a.Skus != null && a.Skus.Count > 0).ToList();
            if (wareHouseProducts.Any()) new WareHouseService().WarehouseProductMerger(wareHouseProducts);

            return true;
        }

        /// <summary>
        /// 检查Sku属性长度是否全部符合规范（不超过64字符）
        /// </summary>
        /// <param name="modelProductSkus"></param>
        /// <returns></returns>
        public bool CheckSkuAttributeLength(List<BaseProductSkuModel> modelProductSkus)
        {
            if (modelProductSkus?.Any() != true) return true;

            // 遍历所有有属性的SKU
            return modelProductSkus
                .Where(x => x.Attribute != null)
                .Select(x => x.Attribute)
                .All(ValidateAttribute);
        }

        /// <summary>
        /// 验证单个Attribute对象的所有属性对
        /// </summary>
        /// <param name="attr"></param>
        /// <returns></returns>
        private static bool ValidateAttribute(BaseProductSkuAttributeModel attr)
        {
            // 需要校验的属性对序列
            var attributePairs = new[]
            {
                (attr.AttributeName1, attr.AttributeValue1),
                (attr.AttributeName2, attr.AttributeValue2),
                (attr.AttributeName3, attr.AttributeValue3)
            };

            // 检查所有属性对
            return attributePairs.All(pair => 
                CheckStringLength(pair.Item1) && 
                CheckStringLength(pair.Item2));
        }

        /// <summary>
        /// 校验单个字符串长度（空值通过，非空值检查长度）
        /// </summary>
        private static bool CheckStringLength(string value) => IsNullOrEmpty(value) || value.GetStringRealLen() <= 64;


        /// <summary>
        /// 批量删除基础商品||基础商品规格
        /// </summary>
        /// <param name="baseProductSkus"></param>
        public CheckResult<BatchDeleteBaseProductRes> BatchDeleteSku(DeleteBaseProductCheckModel model)
        {
            var res = new CheckResult<BatchDeleteBaseProductRes>();
            var data = new BatchDeleteBaseProductRes();

            var isDeleteSku = !model.IsBaseProduct;
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            var baseProductSkus = new List<BaseProductSku>();

            if (isDeleteSku)
            {
                baseProductSkus = GetListBySkuUids(model.BaseProductSkuUids, fxUserId);
            }
            else
            {
                baseProductSkus = GetSkuByProductUids(model.BaseProductUids, fxUserId);
            }
          
            if (!baseProductSkus.Any())
            {
                res.Success = false;
                res.Data = null;
                res.Message = "未找到已选择的规格！";
                return res;
            }
            
            var _service = new WareHouseService();
            var _BaseProductRepository = new BaseProductRepository();
            var _baseProductSkuRepository = new BaseProductSkuRepository();
            var _supplierProductRelationRepository = new BaseOfSupplierSkuRelationRepository();
            var _supplierProductRepository = new SupplierProductRepository();
            var _supplierProductSkuRepository = new SupplierProductSkuRepository();

            // 查询本批删除库存规格
            var skuCodes = baseProductSkus.Select(p => p.SkuCode).ToList();
            var deleteCombineProduts = new List<DeleteWareHouseProductModel>();
            var deleteCombineProdutSkus = new List<DeleteWarseHoseProductSkuModel>();

            var failMsg = string.Empty;

            // 递归查询组合，删除组合库存商品
            var resulst = _service.DeleteSkuCheck(skuCodes,true);

            if (resulst == null || resulst.IsSucc == false)
            {
                res.Success = false;
                res.Data = null;
                res.Message = "库存获取规格失败！";
                return res;
            }
            else
            {
                deleteCombineProduts = resulst.Data.DeleteCombineProduts;
                deleteCombineProdutSkus = resulst.Data.DeleteCombineProdutSkus;
            }

            // 查询所有的删除基础商品规格
            var combinProductCodes = deleteCombineProduts
                .Select(p => p.WareHouseProductCargoNumber)
                .Distinct()
                .ToList();

            var combinProductSkuCodes = deleteCombineProdutSkus
                .Select(p => p.SkuCargoNumber)
                .Distinct()
                .ToList();

            if (!combinProductCodes.Any())
            {
                // 避免异常数据干扰：库存系统无对应数据(其他分支有修改，已此为主)
                combinProductSkuCodes.AddRange(skuCodes);
                combinProductSkuCodes = combinProductSkuCodes.Distinct().ToList();
            }

            var baseProductSkusCombine =_repository.GetSkuByCode(combinProductSkuCodes,fxUserId);
            var baseProduct = _BaseProductRepository.GetBaseProductByCode(combinProductCodes, fxUserId);
            var baseProductUids = baseProduct.Select(p => p.Uid).ToList();
            var allDeleteSku = _repository.GetSkuByProductUids(baseProductUids, fxUserId);

            var skus = allDeleteSku.Concat(baseProductSkusCombine).ToList();
            if (skus == null || skus.Count == 0)
            {
                res.Success = false;
                res.Data = null;
                res.Message = "未找到已选择的规格！";
                return res;
            }

            #region 精选解绑关联店铺商品
            var enableColdGlobal = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
            var service = new BaseOfPtSkuRelationService(true);
            BaseOfPtSkuRelationService coldService = null;
            if (enableColdGlobal && SiteContext.Current.CurrentDbConfig.EnableColdDb)
                coldService = new BaseOfPtSkuRelationService(SiteContext.Current.CurrentDbConfig.ColdDbConnectionString, false);
        
            var baseProductSkuUid = skus.Select(a => a.Uid).Distinct().ToList();
            var productSkuCode = skus.Select(a => a.SkuCode).Distinct().ToList();
            var baseOfPtSkuRelations = service.GetRelationBySkuUid(baseProductSkuUid, fxUserId);
            if (baseOfPtSkuRelations.Count != 0)
            {
                foreach (var relation in baseOfPtSkuRelations)
                {
                    relation.Status = 0;
                    relation.UpdateTime = DateTime.Now;
                }
                service.BulkUpdate(baseOfPtSkuRelations);
                coldService?.BulkUpdate(baseOfPtSkuRelations);
            }
            #endregion

            #region 跨云解绑关联店铺商品
            var faileRelations = new List<BaseOfPtSkuRelation>();
            var successRelations = new List<BaseOfPtSkuRelation>();
            if (baseOfPtSkuRelations.Any())
            {
                var messageService = new MessageRecordService();
                var relationsGroups = baseOfPtSkuRelations.GroupBy(p => p.BaseProductSkuUid).ToList();
                var options = new ParallelOptions { MaxDegreeOfParallelism = relationsGroups.Count() };
               
                Parallel.ForEach(relationsGroups, options, (group) =>
                {
                    var currentRelations = group.ToList();

                    for (var i = 1; i < 5; i++)
                    {
                        // 分云处理
                        var platformType = (CloudPlatformType)i;
                        var relations = currentRelations.Where(p => p.CloudPlatform == platformType.ToString()).ToList();
                        if (!relations.Any())
                        {
                            continue;
                        }
                        try
                        {
                            var newmodel = new BatchBaseProductSkuUnbindModel()
                            {
                                BaseProductSkuUid = relations.FirstOrDefault()?.BaseProductSkuUid ?? 0,
                                BaseProductUid = relations.FirstOrDefault()?.BaseProductUid ?? 0,
                                IsAllUseWarehouse = model.IsAllUseWarehouse,
                                IsRestoreCostPrice = model.IsRestoreCostPrice,
                                IsRestoreDistributePrice = model.IsRestoreDistributePrice,
                                IsRestoreSettlePrice = model.IsRestoreSettlePrice,
                                IsRestoreShortTitle = model.IsRestoreShortTitle,
                                IsRestoreSupplier = model.IsRestoreSupplier,
                                Skus = new List<BatchUnbindBaseProductSku>()
                            };

                            foreach (var item in relations)
                            {
                                var skuModel = new BatchUnbindBaseProductSku();
                                skuModel.ProductPtId = item.ProductPtId;
                                skuModel.ProductPtCode = item.ProductCode;
                                skuModel.ProductSkuPtId = item.ProductSkuPtId;
                                skuModel.ProductSkuPtCode = item.ProductSkuCode;
                                newmodel.Skus.Add(skuModel);
                            }
                            var msgList = new List<MessageRecord>();
                            msgList.Add(new MessageRecord
                            {
                                MsgType = BaseProductMsgType.BatchRelationUnBind,
                                FxUserId = fxUserId,
                                TargetCloud = platformType.ToString(),
                                BusinessId = "",
                                DataJson = newmodel.ToJson()
                            });
                            // 基础商品解绑 
                            if (CustomerConfig.IsLocalDbDebug)
                                messageService.batchRelationUnBind(msgList);
                            else
                                messageService.SendBusinessMessage(msgList);
                            successRelations.AddRange(relations);
                        }
                        catch (Exception ex)
                        {
                            faileRelations.AddRange(relations);
                            var msg =
                                 $"{platformType.ToString()}平台解绑消息发送失败，" +
                                 $"失败规格:{relations.Select(p => p.BaseProductSkuUid).ToJson()}，" +
                                 $"失败原因：{ex.ToJson()}";
                            Log.WriteError(msg);
                            failMsg += $"{platformType.ToString()}平台解绑消息发送失败，失败原因：{ex.ToJson()}\n";
                        }
                    }
                });
            }
            #endregion

            #region 删除库存商品
            var delWareHouseProductCodes =
                resulst.Data.DeleteCombineProduts.Select(p => p.WareHouseProductCode).Distinct().ToList();
            var delWareHouseSkuCodes =
                resulst.Data.DeleteCombineProdutSkus.Select(p => p.WareHouseSkuCode).Distinct().ToList();
            var delRelationIds =
                resulst.DeleteRelationIds.Distinct().ToList();
            try
            {
                _wareHouseService.UpdateProductStatus(delWareHouseSkuCodes, delWareHouseProductCodes, delRelationIds);
            }
            catch (Exception ex)
            {
                Log.WriteError($"删除库存商品失败，失败原因：{ex.ToJson()}");
            }
            #endregion

            #region 删除小站商品
            var suppliserRelationsP =
                _supplierProductRelationRepository.GetListByProductUid(baseProductUids, fxUserId);
            var suppliserRelationsS = 
                _supplierProductRelationRepository.GetListByProductSkuUid(baseProductSkuUid, fxUserId);

            var allRelations =suppliserRelationsP.Concat(suppliserRelationsS).ToList();
            allRelations = 
                allRelations.GroupBy(p => p.Id).Select(p => p.FirstOrDefault()).ToList();
          
            _supplierProductRelationRepository.BulkDelete(allRelations);  // 解绑小站数据
            
            var supplierProductUids = allRelations
                .Select(p => p.SupplierProductUid)
                .Distinct()
                .ToList();
            var supplierProducts =
                _supplierProductRepository.GetSupplierProductByUids(supplierProductUids, fxUserId);
        
            var deleteSupplierProducts = new List<SupplierProductEntity>();
            var deleteSupplierProductSkus = new List<SupplierProductSku>();

            foreach (var supplierProduct in supplierProducts)
            {
                var oldSkus = supplierProduct?.Skus?.Select(p => p.Uid).Distinct().ToList();
                var newSkus = allRelations.Where(p =>p.SupplierProductUid == supplierProduct.Uid)
                    .Select(p=>p.SupplierProductSkuUid)
                    .Distinct()
                    .ToList();
                bool areEqual = !oldSkus.Except(newSkus).Any() && !newSkus.Except(oldSkus).Any();

                if (areEqual)
                {
                    deleteSupplierProducts.Add(supplierProduct);
                }
                else
                {
                    if (oldSkus.Count() == 1 && newSkus.Any(p => p == oldSkus.FirstOrDefault()))
                    {
                        deleteSupplierProducts.Add(supplierProduct);
                    }
                }
                deleteSupplierProductSkus.AddRange(supplierProduct.Skus.Where(p => newSkus.Contains(p.Uid)).ToList());
            }
            if (deleteSupplierProducts.Any())
                _supplierProductRepository.BulkDelete(deleteSupplierProducts);
            if(deleteSupplierProductSkus.Any())
                _supplierProductSkuRepository.BulkDelete(deleteSupplierProductSkus);
            #endregion

            #region 删除基础商品
            var failSkuUids = faileRelations.Select(p => p.BaseProductSkuUid).Distinct().ToList();
            skus = skus.Where(p => !failSkuUids.Contains(p.Uid)).ToList();
            var baseProdutIds =
                skus.Select(p => p.BaseProductUid).Distinct().ToList();
            var baseProductGroup =
                skus.GroupBy(p => p.BaseProductUid).ToList();

            var baseProducts = _BaseProductRepository.GetBaseProductByUids(baseProdutIds, fxUserId);
            var deleteBaseProducts = new List<BaseProductEntity>();
            var deleteBaseProductSkus = new List<BaseProductSku>();

            foreach (var item in baseProducts)
            {
                var product = baseProductGroup.FirstOrDefault(p => p.Key == item.Uid);
                if (product != null)
                {
                    var oldSkus = item?.Skus;
                    var newSkus = product.ToList();

                    var oldSkusUid = oldSkus.Select(p => p.Uid).Distinct().ToList();
                    var newSkusUid = newSkus.Select(p => p.Uid).Distinct().ToList();

                    bool areEqual = !oldSkusUid.Except(newSkusUid).Any() && !newSkusUid.Except(oldSkusUid).Any();

                    if (combinProductCodes.Any(p => p == item.SpuCode))
                    {
                        deleteBaseProducts.Add(item);
                    }
                    else if (areEqual)
                    {
                        deleteBaseProducts.Add(item);
                    }
                    else
                    {
                        if (oldSkus.Count() == 1 && newSkus.Any(p => p.Uid == oldSkus.FirstOrDefault()?.Uid))
                        {

                            deleteBaseProducts.Add(item);
                        }
                    }
                    deleteBaseProductSkus.AddRange(newSkus);
                }
            }

            if (deleteBaseProductSkus.Any() || deleteBaseProducts.SelectMany(p => p.Skus).Any())
            {
                var allDeleteSkus = deleteBaseProductSkus.Concat(deleteBaseProducts.SelectMany(p => p.Skus)).ToList();
                allDeleteSkus = allDeleteSkus.GroupBy(p => p.Uid).Select(p => p.FirstOrDefault()).ToList();
                _baseProductSkuRepository.BulkDelete(allDeleteSkus);
                _baseProductSkuRepository.BulkUpdateSkuModeType(allDeleteSkus);
            }

            if (deleteBaseProducts.Any())
            {
                _BaseProductRepository.BatchDelete(deleteBaseProducts.Select(p => p.Uid).ToList());
            }

            #endregion

            #region 跨云清理快捷添加记录
            if (deleteBaseProducts.Any())
            {
                Task.Run(() => {
                    new GenerateBaseProductRecordService().DeleteGenerateBaseProductRecord(deleteBaseProducts);
                });
            }
            #endregion

            if (model.IsBaseProduct)
            {
                var successCount = model.BaseProductUids.Distinct().Count();
                var productCodes = faileRelations.Select(p => p.BaseProductSpuCode).Distinct().ToList();
                data.FailCount = productCodes.Count();
                data.SuccessCount = successCount - data.FailCount;
                data.FailProductCodes = productCodes;
                data.DeleteUnBindRelationsCount = successRelations.Count();
            }
            else
            {
                var successCount = model.BaseProductSkuUids.Distinct().Count();
                var productSkuCodes = faileRelations.Select(p => p.BaseProductSkuCode).Distinct().ToList();
                data.FailCount = productSkuCodes.Count();
                data.SuccessCount = successCount- data.FailCount;
                data.FailSkuCodes= productSkuCodes;
                data.DeleteUnBindRelationsCount = successRelations.Count();
            }
            if (data.FailCount > 0)
            {
                data.FailMsg = failMsg;
            }
            res.Success = true;
            res.Data = data;
            res.Message = "删除成功！";
            return res;
        }
    }
}
