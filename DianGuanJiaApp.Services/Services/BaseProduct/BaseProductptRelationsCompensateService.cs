using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.Services.CrossCloud;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;
using Dapper;
using System.Data.Common;
using iTextSharp.text;
using System.Globalization;

namespace DianGuanJiaApp.Services.Services.BaseProduct
{
    public class BaseProductptRelationsCompensateService
    {

        public BaseProductptRelationsCompensateService()
        {

        }

        /// <summary>
        /// 基础商品关联关系补偿
        /// </summary>
        public void CompensateBaseProductptRelations(bool isDebug)
        {
            // 系统配置不能修改，该配置属于系统配置，增量变化，需要修改，采用-1，保存配置
            // 系统配置补偿时间
            var _commonSettingService = new CommonSettingService();
            var lastEndTimeKey = "/FxSystem/CompensatePtBaseProductSkuRelations/StartCompensateTime";
            var lastEndTimeStr = _commonSettingService.Get(lastEndTimeKey, -1, false);
            var lastEndTime =
                lastEndTimeStr.IsNullOrEmpty() ? DateTime.Now.AddDays(-5) : Convert.ToDateTime(lastEndTimeStr.Value);
            var startTime = lastEndTime.AddSeconds(-1);
            var endTime = startTime.AddHours(2).AddSeconds(1);
            if (endTime > DateTime.Now.AddMinutes(-1))
                endTime = DateTime.Now.AddMinutes(-1);

            if (isDebug)
            {
                startTime = Convert.ToDateTime("2025-05-30 16:55:13");
                endTime = Convert.ToDateTime("2025-05-30 16:55:55");
            }
            Log.WriteLine($"补偿开始，本次时间段：{startTime.ToString("yyyy-MM-dd HH:mm:ss")}至{endTime.ToString("yyyy-MM-dd HH:mm:ss")}");
            // 基础商品分库
            var baseProdutDbs = new ProductDbConfigRepository().GetAllDbConfigModel();
            if (!baseProdutDbs.Any())
            {
                Log.WriteWarning($"补偿时间范围：{startTime}-{endTime}，未找到基础商品分库!");
                return;
            }
            Log.WriteLine($"Step1，本次时间段：{startTime.ToString("yyyy-MM-dd HH:mm:ss")}至{endTime.ToString("yyyy-MM-dd HH:mm:ss")}");

            // 基础商品数据
            var baseProductSkuRelations = new List<BaseOfPtSkuRelation>();
            var wareProductSkuRelations = new List<Warehouse.Entity.WareHouseSkuBindRelation>();
            var options = new ParallelOptions { MaxDegreeOfParallelism = baseProdutDbs.Count()> 10 ? 10 : baseProdutDbs.Count() };
            Parallel.ForEach(baseProdutDbs, options, (db) =>
            {
                try
                {
                    var _tempRepository = new BaseOfPtSkuRelationRepository(db.ConnectionString);
                    var _tempRelations = _tempRepository.GetListByTime(startTime, endTime);
                    if (_tempRelations.Any())
                    {
                        baseProductSkuRelations.AddRange(_tempRelations);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"基础商品基础分库：{db.ProductDbNameConfig.DbName},获取补偿数据失败：{ex.ToJson()}");
                }
            });
                        
            baseProductSkuRelations = baseProductSkuRelations.Where(p=>!string.IsNullOrWhiteSpace(p.CloudPlatform) ).ToList();
            if (!baseProductSkuRelations.Any())
            {
                Log.WriteWarning($"基础商品基础分库未找到补偿数据");

                _commonSettingService.Set(lastEndTimeKey, endTime.ToString("yyyy-MM-dd HH:mm:ss"), -1);
                Log.WriteLine($"补偿结束，本次时间段：{startTime.ToString("yyyy-MM-dd HH:mm:ss")}至{endTime.ToString("yyyy-MM-dd HH:mm:ss")}");

                return;
            }

            baseProductSkuRelations = baseProductSkuRelations.Where(p => !string.IsNullOrWhiteSpace(p.CalculateCloudPlatform) ).ToList();

            if (!baseProductSkuRelations.Any())
            {
                Log.WriteWarning($"基础商品基础分库未找到补偿数据，CloudPlatform不能为空");
                return;
            }

            // 库存商品数据
            var stockCargNumbers = baseProductSkuRelations
                .Select(p => p.BaseProductSkuCode)
                .Where(p => p.IsNotNullOrEmpty())
                .Distinct()
                .ToList();
            if (stockCargNumbers.Any())
            {
                var stockRepository = new Warehouse.Repository.WareHouseSkuBindRelationRepository();
                var stockBindRelations = stockRepository.GetSkuByOnlyCargoNumber(stockCargNumbers);
                if (stockBindRelations.Any())
                {
                    wareProductSkuRelations.AddRange(stockBindRelations);
                }
            }

            var baseProductSkuRelationsGroups =
                baseProductSkuRelations.GroupBy(p => new { p.CalculateCloudPlatform, p.FxUserId });

            var baseProductCloudPlatforms = baseProductSkuRelationsGroups
                .Select(p=>p.Key.CalculateCloudPlatform)
                .Distinct()
                .ToList();
            var baseProductFxUserIds = baseProductSkuRelationsGroups
                .Select(p => p.Key.FxUserId)
                .Distinct()
                .ToList();
            var dbConfigRepository = new DbConfigRepository();

            //补偿逻辑：补偿分区新库;旧库暂未补偿;旧库需要补偿的数据（业务库关联失败）会冗余到新库
            var dbConfigList = dbConfigRepository.GetListByFxUserIds(baseProductFxUserIds, baseProductCloudPlatforms);

            var baseProductRelationOptions = new ParallelOptions
            {
                MaxDegreeOfParallelism = baseProductSkuRelationsGroups.Count() > 10 ? 10 : baseProductSkuRelationsGroups.Count()
            };

            Parallel.ForEach(baseProductSkuRelationsGroups, baseProductRelationOptions, (group) =>
            {
                var fxUserId = group.Key.FxUserId;
                var platformType = group.Key.CalculateCloudPlatform;
                var ownerCode = ("FenDanSystem" + fxUserId).ToShortMd5();
                try
                {
                    var baseProductSkuRelationList = group.ToList();
                    var baseProcuctSkuCodes = baseProductSkuRelationList
                        .Select(p => p.BaseProductSkuCode)
                        .Where(p => p.IsNotNullOrEmpty())
                        .Distinct()
                        .ToList();
                    var ptProductSkuCodes = baseProductSkuRelationList
                        .Select(p => p.ProductSkuCode)
                        .Where(p => p.IsNotNullOrEmpty())
                        .Distinct()
                        .ToList();

                    Log.WriteLine($"Step2.1，本次时间段：{startTime.ToString("yyyy-MM-dd HH:mm:ss")}至{endTime.ToString("yyyy-MM-dd HH:mm:ss")}，fxUserId={fxUserId}，platformType={platformType}，baseProductSkuRelationList.Count={baseProductSkuRelationList.Count}");

                    var wareProductSkuRelationList = wareProductSkuRelations.Where(p =>
                        p.OwnerCode == ownerCode &&
                        p.PlatformType == platformType &&
                        ptProductSkuCodes.Contains(p.PlatformSkuCode) &&
                        baseProcuctSkuCodes.Contains(p.SkuCargoNumber)).ToList();

                    #region 分库数据库

                    var currentDbConfigs = dbConfigList
                        .Where(p =>p.DbConfig.UserId  == fxUserId && p.DbConfig.DbCloudPlatform == platformType)
                        .OrderByDescending(p => p.DbConfig.FromFxDbConfig)
                        .ToList();

                    var db = currentDbConfigs.FirstOrDefault();
                    if (db == null)
                    {
                        return;
                    }
                    var apiDbConfig = new ApiDbConfigModel { DbNameConfigId = db.DbNameConfig.Id, Location = db.DbServer.Location, PlatformType = db.DbServer.Location };
                    var dbApi = new DbAccessUtility(apiDbConfig);
                    #endregion

                    #region 分库基础商品关联补偿
                    var productSkuRelations = new List<BaseOfPtSkuRelation>();
                    var productSkuCode =
                        baseProductSkuRelationList.Select(p => p.ProductSkuCode).Distinct().ToList();
                    var getBaseProductSkuRelationsSql = $@"SELECT * FROM BaseOfPtSkuRelation WITH(NOLOCK) WHERE ProductSkuCode IN @ProductSkuCode";
                    var pageSize = 200;
                    var pageTotal = Math.Ceiling(productSkuCode.Count() * 1.0 / pageSize);
                    for (int i = 0; i < pageTotal; i++)
                    {
                        try
                        {
                            var tempCodes = productSkuCode.Skip(i * pageSize).Take(pageSize);
                            var tempDatas = dbApi.Query<BaseOfPtSkuRelation>(getBaseProductSkuRelationsSql, new { ProductSkuCode = tempCodes }).ToList();
                            if (tempDatas != null && tempDatas.Any())
                            {
                                productSkuRelations.AddRange(tempDatas);
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"第{i + 1}次，分批查询基础商品业务库关联关系失败，失败原因：{ex.ToJson()}");
                        }
                    }

                    var addBaseProductSkuRelations = new List<BaseOfPtSkuRelation>();
                    var updteBaseProductSkuRelations = new List<BaseOfPtSkuRelation>();
                    baseProductSkuRelationList = baseProductSkuRelationList
                        .GroupBy(p => new { p.ProductSkuCode, p.BaseProductSkuUid }).Select(p => p.First()).ToList();
                    foreach (var relation in baseProductSkuRelationList)
                    {
                        var subRelation = productSkuRelations.FirstOrDefault(p =>
                            p.BaseProductSkuCode == relation.BaseProductSkuCode &&
                            p.BaseProductSkuUid == relation.BaseProductSkuUid &&
                            p.FxUserId == relation.FxUserId &&
                            p.ProductPlatformType == relation.ProductPlatformType &&
                            p.ProductCode == relation.ProductCode &&
                            p.ProductSkuCode == relation.ProductSkuCode);

                        // 基础库存在：业务库不存在
                        if (subRelation == null)
                        {
                            relation.Id = 0;
                            addBaseProductSkuRelations.Add(relation);
                        }
                        // 基础库存在：业务库已存在
                        else
                        {
                            if (relation.Status != subRelation.Status)
                            {
                                subRelation.Status = relation.Status;
                                updteBaseProductSkuRelations.Add(subRelation);
                            }
                        }
                        // 基础库不存在：业务库已存在 todo
                        // 1.业务库补偿程序
                        // 2.业务库增量补偿基础商品库
                    }
                    var syncBusinessDataService = new SyncBusinessDataService(apiDbConfig);

                    if (addBaseProductSkuRelations.Any())
                    {
                        var chunks = addBaseProductSkuRelations.ChunkList(30);
                        chunks.ForEach(chunk =>
                        {
                            syncBusinessDataService.InsertData(chunk);
                        });
                    }
                    if (updteBaseProductSkuRelations.Any())
                    {
                        var chunks = updteBaseProductSkuRelations.ChunkList(30);
                        chunks.ForEach(chunk =>
                        {
                            syncBusinessDataService.UpdateData(chunk,
                                new List<string> { "BaseProductSkuUid", "ProductSkuCode", "FxUserId", "ProductPlatformType" },
                                new List<string> { "Status" });
                        });
                    }
                    if (addBaseProductSkuRelations.Any() || updteBaseProductSkuRelations.Any())
                    {
                        var adds = addBaseProductSkuRelations
                            .Select(p=>new { ProductSkuCode =p.ProductSkuCode , BaseProductSkuUid =p.BaseProductSkuUid })
                            .ToList();
                        var updates = updteBaseProductSkuRelations
                            .Select(p => new { ProductSkuCode = p.ProductSkuCode, BaseProductSkuUid = p.BaseProductSkuUid })
                            .ToList();

                        Log.WriteLine(
                            $"用户：{fxUserId}{platformType.ToString()}云平台，" +
                            $"用户数据库：{db?.DbNameConfig?.DbName}，" +
                            $"用户补偿【基础】关联成功：" +
                            $"updteBaseProductSkuRelations={updates?.ToJson()}，" +
                            $"addBaseProductSkuRelations={adds?.ToJson()}");
                    }

                    #endregion

                    #region 分库库存商品补偿处理
                    var wareHouseSkuRelations = new List<Warehouse.Entity.WareHouseSkuBindRelation>();
                    var getWareHouseSkuRelationsSql = $@"SELECT * FROM WareHouseSkuBindRelation WITH(NOLOCK) WHERE WareHouseSkuCode IN @WareHouseSkuCode";
                    var wareHouseSkuCode = wareProductSkuRelationList.Select(p => p.WareHouseSkuCode).Distinct().ToList();
                    var stockPageSize = 200;
                    var stockPageTotal = Math.Ceiling(wareHouseSkuCode.Count() * 1.0 / stockPageSize);
                    for (int i = 0; i < stockPageTotal; i++)
                    {
                        try
                        {
                            var tempCodes = wareHouseSkuCode.Skip(i * pageSize).Take(pageSize);
                            var tempDatas = dbApi.Query<Warehouse.Entity.WareHouseSkuBindRelation>(getWareHouseSkuRelationsSql, new { WareHouseSkuCode = tempCodes }).ToList();
                            if (tempDatas != null && tempDatas.Any())
                            {
                                wareHouseSkuRelations.AddRange(tempDatas);
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"第{i + 1}次，分批查询库存商品业务库关联关系失败，失败原因：{ex.ToJson()}");
                        }
                    }

                    if (wareHouseSkuRelations.Any())
                    {
                        var addWareHouseSkuRelations =
                            new List<Warehouse.Entity.WareHouseSkuBindRelation>();
                        var updateWareHouseSkuRelations =
                            new List<Warehouse.Entity.WareHouseSkuBindRelation>();

                        wareProductSkuRelationList = wareProductSkuRelationList
                            .GroupBy(p => new { p.WareHouseSkuCode, p.OwnerCode})
                            .Select(p => p.First())
                            .ToList();

                        foreach (var relation in wareProductSkuRelationList)
                        {
                            var subRelation = wareHouseSkuRelations.FirstOrDefault(p =>
                                p.WareHouseSkuCode == relation.WareHouseSkuCode &&
                                p.PlatformType == relation.PlatformType &&
                                p.ShopId == relation.ShopId &&
                                p.PlatformSkuCode == relation.PlatformSkuCode &&
                                p.OwnerCode == relation.OwnerCode);
                            // 库存库存在：业务库不存在
                            if (subRelation == null)
                            {
                                relation.Id = 0;
                                addWareHouseSkuRelations.Add(relation);
                            }
                            // 库存库存在：业务库已存在
                            else
                            {
                                if (relation.Status != subRelation.Status)
                                {
                                    subRelation.Status = relation.Status;
                                    updateWareHouseSkuRelations.Add(subRelation);
                                }
                            }
                        }
                        if (addWareHouseSkuRelations.Any())
                        {
                            var chunks = addWareHouseSkuRelations.ChunkList(30);

                            chunks.ForEach(chunk =>
                            {
                                syncBusinessDataService.InsertData(chunk);
                            });
                        }
                        if (updateWareHouseSkuRelations.Any())
                        {
                            var chunks = updateWareHouseSkuRelations.ChunkList(30);

                            chunks.ForEach(chunk =>
                            {
                                syncBusinessDataService.UpdateData(chunk,
                                    new List<string> { "PlatformSkuCode", "WareHouseSkuCode", "OwnerCode" },
                                    new List<string> { "Status" });
                            });
                        }

                        if (addWareHouseSkuRelations.Any() || updateWareHouseSkuRelations.Any())
                        {
                            var adds = addWareHouseSkuRelations
                                .Select(p => new { WareHouseSkuCode = p.WareHouseSkuCode, PlatformSkuCode = p.PlatformSkuCode })
                                .ToList();
                            var updates = updateWareHouseSkuRelations
                                .Select(p => new { WareHouseSkuCode = p.WareHouseSkuCode, PlatformSkuCode = p.PlatformSkuCode })
                                .ToList();

                            Log.WriteLine(
                            $"用户：{fxUserId}{platformType.ToString()}云平台，" +
                            $"用户数据库：{db?.DbNameConfig?.DbName}，" +
                            $"用户补偿【库存】关联成功：" +
                            $"updateWareHouseSkuRelations={updates?.ToJson()}，" +
                            $"addWareHouseSkuRelations={adds?.ToJson()}");
                        }
                    }
                    #endregion

                }
                catch (Exception ex)
                {
                    Log.WriteError($"用户：{fxUserId}{platformType.ToString()}云平台数据补偿失败，失败原因：{ex.ToJson()}");
                }
            });

            // 补偿时间设置
            var time = endTime;
            var timeR = _commonSettingService.Set(lastEndTimeKey, time.ToString("yyyy-MM-dd HH:mm:ss"), -1);

            Log.WriteLine($"补偿结束，本次时间段：{startTime.ToString("yyyy-MM-dd HH:mm:ss")}至{endTime.ToString("yyyy-MM-dd HH:mm:ss")}");
        }


        /// <summary>
        /// 基础商品关联关系补偿补偿
        /// </summary>
        public void CompensateBaseProductptRelationsReverse(bool isDebug)
        {
            // 系统配置补偿时间
            var _commonSettingService = new CommonSettingService();
            var lastEndTimeKey = "/FxSystem/CompensatePtBaseProductSkuRelations_Reverse/StartCompensateTime";
            var lastEndTimeStr = _commonSettingService.Get(lastEndTimeKey, -1, false);
            var lastEndTime =
                lastEndTimeStr.IsNullOrEmpty() ? DateTime.Now.AddDays(-5) : Convert.ToDateTime(lastEndTimeStr.Value);
            var startTime = lastEndTime.AddSeconds(-1);
            var endTime = startTime.AddHours(4).AddSeconds(1);
            if (endTime > DateTime.Now.AddMinutes(-4))//反向补偿延迟4分钟
                endTime = DateTime.Now.AddMinutes(-4);

            if (isDebug)
            {
                //startTime = Convert.ToDateTime("2024-12-02 11:31:21.580");
                //endTime = Convert.ToDateTime("2024-12-23 11:13:50.633");

                // 定义日期格式，包含毫秒部分
                string format = "yyyy-MM-dd HH:mm:ss.fff";

                // 尝试解析
                if (DateTime.TryParseExact("2024-12-02 11:31:21.580", format, CultureInfo.InvariantCulture, DateTimeStyles.None, out startTime))
                {
                    Console.WriteLine("解析成功: " + startTime);  // 输出：2025-06-18 14:30:45.123
                }
                else
                {
                    Console.WriteLine("无效的日期格式");
                }

                if (DateTime.TryParseExact("2024-12-23 11:13:50.633", format, CultureInfo.InvariantCulture, DateTimeStyles.None, out endTime))
                {
                    Console.WriteLine("解析成功: " + endTime);  // 输出：2025-06-18 14:30:45.123
                }
                else
                {
                    Console.WriteLine("无效的日期格式");
                }
            }
            Log.WriteWarning($"反向补偿时间范围：{startTime}-{endTime}");

            // 云平台业务分库（过滤旧库）
            var dbConfigRepository = new DbConfigRepository();
            var dbConfigList = dbConfigRepository.GetAllNewDbConfigModel();
            if (dbConfigList.Any()== false)
            {
                Log.WriteWarning($"反向补偿时间范围：{startTime}-{endTime}，未找到业务分库!");
                return;
            }
            dbConfigList = dbConfigList.GroupBy(p => p.DbNameConfig.Id).Select(p => p.First()).ToList();

            // 指定数据（测试用）
            var dbs = CustomerConfig.CompensateBaseProductptRelations_ReverseDb;
            var cloud = CustomerConfig.CompensateBaseProductptRelations_ReverseDbCloud;
            if (dbs.Any() && cloud.IsNotNullOrEmpty())
            {
                dbConfigList = dbConfigList.Where(p => dbs.Contains(p.DbNameConfig.DbName) && p.DbServer.Location == cloud).ToList();
            }
            var dbConfigsOptions = new ParallelOptions
            {
                MaxDegreeOfParallelism = dbConfigList.Count() > 10 ? 10 : dbConfigList.Count()
            };

            // 基础商品关联数据
            var baseProductSkuRelations = new List<BaseOfPtSkuRelation>();
            // 库存商品关联数据
            var wareProductSkuRelations = new List<Warehouse.Entity.WareHouseSkuBindRelation>();
            // 分库查询所有数据

            Parallel.ForEach(dbConfigList, dbConfigsOptions, (db) =>
            //foreach (var db in dbConfigList)
            {
                try
                {
                    var apiDbConfig = new ApiDbConfigModel
                    {
                        DbNameConfigId = db.DbNameConfig.Id,
                        Location = db.DbServer.Location,
                        PlatformType = db.DbServer.Location
                    };
                    var dbApi = new DbAccessUtility(apiDbConfig);

                    // 增量查询基础商品关联关系
                    // 增量查询库存商品关联关系
                    #region 查询数据
                    var tempBaseSkuSql = $@"SELECT * FROM BaseOfPtSkuRelation WITH(NOLOCK) WHERE UpdateTime IS NOT NULL AND UpdateTime>=@startTime AND UpdateTime<@endTime";
                    var tempBaseSkuParam = new { startTime = startTime, endTime = endTime };
                    var tempBaseSkuDatas = dbApi.Query<BaseOfPtSkuRelation>(tempBaseSkuSql, tempBaseSkuParam).ToList();
                    if (tempBaseSkuDatas.Any() == false)
                    {
                        Log.WriteWarning(
                            $"业务分库：{db.DbNameConfig.DbName}," +
                            $"业务分库云平台：{db.DbServer.Location}, " +
                            $"未增量查询到基础商品关联数据");
                        return;
                    }
                    var wareHouseSkuCargNumbers = tempBaseSkuDatas
                        .Select(p => p.BaseProductSkuCode)
                        .Where(p => p.IsNotNullOrEmpty())
                        .Distinct()
                        .ToList();
                    var wareHouseRelPlatformSkuCodes = tempBaseSkuDatas.Select(p => p.ProductSkuCode)
                        .Where(p => p.IsNotNullOrEmpty())
                        .Distinct()
                        .ToList();

                    // 先查库存规格
                    // 再查库存关系
                    var tempWareHouseSkuData = new List<Warehouse.Entity.WareHouseSkuBindRelation>();
                    if (wareHouseSkuCargNumbers.Any())
                    {
                        var wareHouseSkuPageSize = 200;
                        var wareHouseSkuCount = wareHouseSkuCargNumbers.Count();
                        var wareHouseSkuPageCount = Math.Ceiling(wareHouseSkuCount * 1.0 / wareHouseSkuPageSize);
                        var wareHouseSkuSql = $@"SELECT * FROM WareHouseSku s WITH(NOLOCK) WHERE s.SkuCargoNumber IN @codes";
                        var wareHouseSkuRelSql = $@"SELECT * FROM WareHouseSkuBindRelation sbr WITH(NOLOCK) WHERE sbr.WareHouseSkuCode IN @wareHouseSkuCode";
                        var _wareHouseSkuRepository = new Warehouse.Repository.WareHouseSkuRepository();

                        var resWareSkus = new List<Warehouse.Entity.WareHouseSku>();

                        for (int i = 0; i < wareHouseSkuPageCount; i++)
                        {
                            var tempCargoNumbers = wareHouseSkuCargNumbers.Skip(i * wareHouseSkuPageSize).Take(wareHouseSkuPageSize).ToList();
                            var tempWareSkus = _wareHouseSkuRepository.GetWareHouseSkusByCargNumber(tempCargoNumbers);
                            if (tempWareSkus.Any())
                                resWareSkus.AddRange(tempWareSkus);
                        }

                        if (resWareSkus.Any())
                        {
                            var wareHouseSkuCodes = resWareSkus.Select(p => p.WareHouseSkuCode).Distinct().ToList();
                            var wareHouseSkuRelPageSize = 200;
                            var wareHouseSkuRelCount = wareHouseSkuCodes.Count();
                            var wareHouseSkuRelPageCount = Math.Ceiling(wareHouseSkuRelCount * 1.0 / wareHouseSkuRelPageSize);

                            for (int i = 0; i < wareHouseSkuRelPageCount; i++)
                            {
                                var tempWareHouseSkuCodes =
                                    wareHouseSkuCodes.Skip(i * wareHouseSkuRelPageSize).Take(wareHouseSkuRelPageSize);
                                var tempdata = dbApi.Query<Warehouse.Entity.WareHouseSkuBindRelation>(
                                    wareHouseSkuRelSql, new { wareHouseSkuCode = tempWareHouseSkuCodes }).ToList();
                                if (tempdata.Any())
                                    tempWareHouseSkuData.AddRange(tempdata);
                            }

                            tempWareHouseSkuData = tempWareHouseSkuData.Where(p => wareHouseRelPlatformSkuCodes.Contains(p.PlatformSkuCode)).ToList();
                        }
                    }
                    if (tempWareHouseSkuData.Any())
                    {
                        wareProductSkuRelations.AddRange(tempWareHouseSkuData);
                    }

                    if (tempBaseSkuDatas.Any())
                    {
                        baseProductSkuRelations.AddRange(tempBaseSkuDatas);
                    }
                    #endregion
                }
                catch (Exception ex)
                {
                    Log.WriteWarning(
                           $"业务分库：{db.DbNameConfig.DbName}," +
                           $"业务分库云平台：{db.DbServer.Location}, " +
                           $"增量查询数据异常：{ex.ToJson()}");
                }
            //};
            });

            // 新库业务库补偿基础商品，过滤旧库冗余到新库的数据
            // 精选分区
            // 拼多分区
            var needYFilterBaseProductSkuRelations = baseProductSkuRelations.Where(p => 
                p.CalculateCloudPlatform == CloudPlatformType.Pinduoduo.ToString()||
                p.CalculateCloudPlatform == CloudPlatformType.Alibaba.ToString())
                .ToList();

            var needNFilterBaseProductSkuRelations = baseProductSkuRelations.Where(p =>
                p.CalculateCloudPlatform == CloudPlatformType.Jingdong.ToString() ||
                p.CalculateCloudPlatform == CloudPlatformType.TouTiao.ToString())
                .ToList();

            var baseProductCloudPlatforms = 
                needYFilterBaseProductSkuRelations.Select(p => p.CalculateCloudPlatform).Distinct().ToList();
            var baseProductFxUserIds = 
                needYFilterBaseProductSkuRelations.Select(p => p.ProductFxUserId).Distinct().ToList();

            var fxUserIdSize = 400;
            var fxUserCount = baseProductFxUserIds.Count();
            var fxUserPageCount = Math.Ceiling(fxUserCount * 1.0 / fxUserIdSize);
            var fxDbConfigList = new List<DbConfigModel>();
            for (int i = 0; i < fxUserPageCount; i++)
            {
                var tempParam = baseProductFxUserIds.Skip(i * fxUserIdSize).Take(fxUserIdSize).ToList();
                var tempFxDbConfigs = dbConfigRepository.GetListByFxUserIds(tempParam, baseProductCloudPlatforms);
                if (tempFxDbConfigs.Any())
                {
                    fxDbConfigList.AddRange(tempFxDbConfigs);
                }
            }
            var needYFilterBaseProductSkuRelationsRes = new List<BaseOfPtSkuRelation>();

            foreach (var rel in needYFilterBaseProductSkuRelations)
            {
                var currentDbConfigs = fxDbConfigList
                    .Where(p => 
                        p.DbConfig.UserId == rel.ProductFxUserId && 
                        p.DbConfig.DbCloudPlatform == rel.CalculateCloudPlatform)
                    .OrderByDescending(p => p.DbConfig.FromFxDbConfig)
                    .FirstOrDefault();
                if (currentDbConfigs == null)
                {
                    continue;
                }
                if (currentDbConfigs.DbConfig.FromFxDbConfig == 0)
                {
                    continue;
                }
                needYFilterBaseProductSkuRelationsRes.Add(rel);
            }

            // 需要处理的总数据
            var allBaseProductSkuRelations = new List<BaseOfPtSkuRelation>();
            allBaseProductSkuRelations.AddRange(needYFilterBaseProductSkuRelationsRes);
            allBaseProductSkuRelations.AddRange(needNFilterBaseProductSkuRelations);
            if (allBaseProductSkuRelations.Any())
            {
                var fxUserIds = allBaseProductSkuRelations
                   .Select(p => p.FxUserId)
                   .Distinct()
                   .ToList();

                var base_ProdutDbs = new ProductDbConfigRepository().BathcGetDbConfigModel(fxUserIds);
                if (base_ProdutDbs.Any() == false)
                {
                    Log.WriteWarning($"基础商品库，未获取到分库配置！");
                    return;
                }
               
                var options = new ParallelOptions { 
                    MaxDegreeOfParallelism = base_ProdutDbs.Count() > 10 ? 10 : base_ProdutDbs.Count()
                };

                // 基础商品分库
                Parallel.ForEach(base_ProdutDbs, options, (db) =>
                //foreach (var db in base_ProdutDbs)
                {
                    var skuRelations_base = new List<BaseOfPtSkuRelation>();
                    var skuRelations_warehouse = new List<Warehouse.Entity.WareHouseSkuBindRelation>();
                    var currentFxUseId = db.ProductDbConfig.FxUserId;

                    try
                    {
                        #region 查询基础商品数据
                        var currentBaseRelations = allBaseProductSkuRelations.Where(p => p.FxUserId == currentFxUseId).ToList();
                        var currentBaseRelationsSkuCodes = currentBaseRelations.Select(p => p.ProductSkuCode)
                            .Where(p => p.IsNotNullOrEmpty()).Distinct().ToList();
                        var _tempRepository = new BaseOfPtSkuRelationRepository(db.ConnectionString);
                        var _tempRelations = _tempRepository.GetListBySkuCodes(currentBaseRelationsSkuCodes);
                        #endregion

                        #region 对比基础商品数据
                        var _tempRelationsAdd = new List<BaseOfPtSkuRelation>();
                        var _tempRelationsUpdate = new List<BaseOfPtSkuRelation>();

                        foreach (var item in currentBaseRelations)
                        {
                            var baseRelation = _tempRelations.FirstOrDefault(p =>
                                p.ProductSkuCode == item.ProductSkuCode &&
                                p.BaseProductSkuUid == item.BaseProductSkuUid &&
                                p.FxUserId == item.FxUserId &&
                                p.ProductPlatformType == item.ProductPlatformType &&
                                p.ProductCode == item.ProductCode);

                            if (baseRelation == null)
                            {
                                item.Id = 0;
                                _tempRelationsAdd.Add(item);
                            }
                            else
                            {
                                if (item.Status != baseRelation.Status)
                                {
                                    baseRelation.Status = item.Status;
                                    _tempRelationsUpdate.Add(item);
                                }
                            }
                        }
                        #endregion

                        #region 新增基础商品数据 修改基础商品数据
                        if (_tempRelationsAdd.Any())
                        {
                            var chunks = _tempRelationsAdd.ChunkList(200);
                            chunks.ForEach(chunk =>
                            {
                                _tempRepository.BatchAdd(chunk);
                            });
                        }
                        if (_tempRelationsUpdate.Any())
                        {
                            var chunks = _tempRelationsUpdate.ChunkList(200);
                            chunks.ForEach(chunk =>
                            {
                                _tempRepository.BulkUpdate(chunk, new List<string> { "Status" });
                            });
                        }
                        Log.WriteLine(
                            $"用户：{currentFxUseId}，" +
                            $"用户补偿【基础】关联成功：" +
                            $"_tempRelationsAdd={_tempRelationsAdd.Select(p => p.ProductSkuPtId)?.ToJson()}，" +
                            $"_tempRelationsUpdate={_tempRelationsUpdate.Select(p => p.ProductSkuPtId)?.ToJson()}");
                        #endregion

                        #region  查询库存商品数据
                        var _ownerCode = ("FenDanSystem" + currentFxUseId).ToShortMd5();
                        var _wareHouseSkuRepository = new Warehouse.Repository.WareHouseSkuBindRelationRepository();
                        var _wareHouseSkuPlatformCodes = currentBaseRelations
                            .Select(p => p.ProductSkuCode)
                            .Distinct().ToList();
                        var _wareHouseRelations = wareProductSkuRelations.Where(p => p.OwnerCode == _ownerCode &&
                            _wareHouseSkuPlatformCodes.Contains(p.PlatformSkuCode)).ToList(); ;
                        var _wareHouseSkuCodes = _wareHouseRelations.Select(p => p.WareHouseSkuCode).Distinct().ToList();
                        var _tempStockRelations = _wareHouseSkuRepository.GetSkuByOnlyWareHouseSkuCodes(_wareHouseSkuCodes, _ownerCode);
                        #endregion

                        #region 对比库存商品数据
                        var _tempWareHouseSkuRelationsAdd = new List<Warehouse.Entity.WareHouseSkuBindRelation>();
                        var _tempWareHouseSkuRelationsUpdate = new List<Warehouse.Entity.WareHouseSkuBindRelation>();

                        foreach (var item in _wareHouseRelations)
                        {
                            var baseStockRelation = _tempStockRelations.FirstOrDefault(p =>
                              p.WareHouseSkuCode == item.WareHouseSkuCode &&
                              p.OwnerCode == item.OwnerCode &&
                              p.PlatformType == item.PlatformType
                              );

                            if (baseStockRelation == null)
                            {
                                item.Id = 0;
                                _tempWareHouseSkuRelationsAdd.Add(item);
                            }
                            else
                            {
                                if (item.Status != baseStockRelation.Status)
                                {
                                    baseStockRelation.Status = item.Status;
                                    _tempWareHouseSkuRelationsUpdate.Add(baseStockRelation);
                                }
                            }
                        }
                        #endregion

                        #region 新增库存商品数据 修改库存商品数据
                        if (_tempWareHouseSkuRelationsAdd.Any())
                        {
                            var chunks = _tempWareHouseSkuRelationsAdd.ChunkList(200);
                            chunks.ForEach(chunk =>
                            {
                                _wareHouseSkuRepository.BulkInsert(chunk);
                            });
                        }
                        if (_tempWareHouseSkuRelationsUpdate.Any())
                        {
                            var chunks = _tempWareHouseSkuRelationsUpdate.ChunkList(200);
                            chunks.ForEach(chunk =>
                            {
                                _wareHouseSkuRepository.BatchUpdate(chunk, new List<string> { "Status" },new List<string> { "WareHouseSkuCode", "OwnerCode" });
                            });
                        }
                        Log.WriteLine(
                            $"用户：{currentFxUseId}，" +
                            $"用户补偿【库存】关联成功：" +
                            $"_tempWareHouseSkuRelationsUpdate={_tempWareHouseSkuRelationsUpdate.Select(p => p.WareHouseSkuCode)?.ToJson()}，" +
                            $"_tempWareHouseSkuRelationsAdd={_tempWareHouseSkuRelationsAdd.Select(p => p.WareHouseSkuCode)?.ToJson()}");

                        #endregion
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"反向基础商品基础分库：{db.ProductDbNameConfig.DbName},处理基础商品关联、库存货品关联偿数据失败：{ex.ToJson()}");
                    }
                //};
                });
            }

            // 补偿时间设置
            var time = endTime;
            var timeR = _commonSettingService.Set(lastEndTimeKey, time.ToString("yyyy-MM-dd HH:mm:ss"), -1);

            Log.WriteLine($"反向补偿结束，本次时间段：{startTime.ToString("yyyy-MM-dd HH:mm:ss")}至{endTime.ToString("yyyy-MM-dd HH:mm:ss")}");
        }
    }
}
