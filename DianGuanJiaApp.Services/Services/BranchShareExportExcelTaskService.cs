using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using System.Collections;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Services
{

    public partial class BranchShareExportExcelTaskService : BaseService<BranchShareExportExcelTask>
    {
        private BranchShareExportExcelTaskRepository _repository = new BranchShareExportExcelTaskRepository();

        public new BranchShareExportExcelTask Get(int id)
        {
            return _repository.Get(id);
        }

        public BranchShareExportExcelTaskService()
        {
            _repository = new BranchShareExportExcelTaskRepository();
            this._baseRepository = _repository;
        }
        public BranchShareExportExcelTask GetExportTaskByShopId(int shopId)
        {
            return _repository.GetExportTaskByShopId(shopId);
        }

        public List<BranchShareExportExcelTask> GetExportTasks(int taskCount = 0)
        {
            return _repository.GetExportTasks(taskCount);
        }

        public List<WaybillCodeSimpleModel> GetWaybillRecords(BranchShareExportExcelTask task)
        {
            var codes = _repository.GetWaybillRecords(task);
            var pddCodes = codes?.Where(x => x.PlatformType == PlatformType.Pinduoduo.ToString())?.ToList();
            var tempOrders = pddCodes?.Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver}).ToList();
            if (tempOrders != null && tempOrders.Any())
            {
                try
                {
                    BranchShareRelationService.TryToDecryptPddOrders(tempOrders, true,"ToName");
                    //按店铺分组
                    pddCodes?.ForEach(item => {
                        var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId && x.ShopId == item.ShopId);
                        if (decryptedOrder != null)
                        {
                            item.Reciver = decryptedOrder.ToName;
                        }
                    });
                }
                catch (Exception ex)
                {
                    Log.WriteError($"单号分享：导出使用明细时发生错误：{ex}");
                }
                pddCodes.ForEach(o =>
                {
                    o.Reciver = o.Reciver.ToEncryptName();
                });
            }
            return codes;
        }

        public bool IsWdUser(int shopId)
        {
            return _repository.IsWdUser(shopId);
        }
    }
}
