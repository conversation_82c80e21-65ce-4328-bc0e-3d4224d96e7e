using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using System.Collections;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using System.Threading;
using System.Collections.Concurrent;
using DianGuanJiaApp.Utility.Extension;
using vipapis.delivery;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using System.Data.Common;

namespace DianGuanJiaApp.Services
{

    public partial class BranchShareRelationService : BaseService<BranchShareRelation>
    {
        private BranchShareRelationRepository _repository = new BranchShareRelationRepository();

        private ShareWaybillCodeRecordService _shareWaybillCodeRecordService = new ShareWaybillCodeRecordService();
        private BranchShareRelationLogService _logService = new BranchShareRelationLogService();
        private ShopService _shopService = new ShopService();
        private FxUserShopService _fxUserShopService = new FxUserShopService();

        public BranchShareRelationService()
        {
            _repository = new BranchShareRelationRepository();
            this._baseRepository = _repository;
        }

        public new BranchShareRelation Get(int id)
        {
            return _repository.Get(id);
        }

        /// <summary>
        /// 根据店铺id，查找店铺的分享关系
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public List<BranchShareRelation> GetListByShopId(int id, bool isIgnoreDeleted = true)
        {
            return _repository.GetListByShopId(id, isIgnoreDeleted);
        }

        public List<BranchShareRelation> GetModelListByShopId(int id, bool isIgnoreDeleted = true)
        {
            return _repository.GetModelListByShopId(id, isIgnoreDeleted);
        }

        public List<BranchShareRelation> GetListByFromId(int shopId)
        {
            return _repository.GetListByFromId(shopId);
        }

        public List<BranchShareRelation> GetListByToId(int shopId)
        {
            return _repository.GetListByToId(shopId);
        }

        /// <summary>
        /// 被分享者  被删除的分享列表
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public List<BranchShareRelation> GetDeletedListByToId(int shopId)
        {
            return _repository.GetDeletedListByToId(shopId);
        }

        public List<BranchShareRelation> GetShareListByFromIdAndBranchHashCode(int fromId, string branchHashCode, string authType, string segmentCode,string brandCode = "",int billVersion = 0)
        {
            return _repository.GetShareListByFromIdAndBranchHashCode(fromId, branchHashCode, authType, segmentCode, brandCode,billVersion);
        }

        public BranchShareRelation GetModelById(int shareId)
        {
            //return GetList(new OrderSearchModel()
            //{
            //    Filters = new List<OrderSearchFieldModel>() {
            //         new Data.Model.OrderSearchFieldModel() {
            //            TableAlias="s",
            //            FieldType="string",
            //            Name="Id",
            //            Value=shareId.ToString(),
            //            Contract="="
            //        }
            //    }
            //}, false, false).FirstOrDefault();

            // return GetList($"WHERE t1.id={shareId}", new { })?.FirstOrDefault();
            var parameter = new DynamicParameters();
            parameter.Add("@shareId", shareId);
            return GetList($"WHERE t1.id=@shareId", parameter)?.FirstOrDefault();
        }


        public List<BranchShareRelation> GetList(OrderSearchModel model, bool isLoadDeleted = false, bool isLoadSumData = true)
        {
            var list = _repository.GetList(model, isLoadDeleted, isLoadSumData);

            var authTypeIsEmptyList = list.Where(f => string.IsNullOrWhiteSpace(f.AuthType)).ToList();
            if (authTypeIsEmptyList.Any())
            {
                var _caiNiaoAuthInfoService = new CaiNiaoAuthInfoService();
                var _shopService = new ShopService();

                var authShopIds = authTypeIsEmptyList.Where(m => m.AuthSourceType.ToInt() == 1).Select(m => m.CaiNiaoAuthInfoId).Distinct().ToList();
                var authShops = authShopIds.Any() ? _shopService.GetShopByIds(authShopIds) : new List<Shop>();

                var caiNiaoAuthInfoIds = authTypeIsEmptyList.Where(m => m.AuthSourceType.ToInt() == 0).Select(m => m.CaiNiaoAuthInfoId).Distinct().ToList();
                var authInfos = caiNiaoAuthInfoIds.Any() ? _caiNiaoAuthInfoService.GetByIds(caiNiaoAuthInfoIds) : new List<CaiNiaoAuthInfo>();
                list.ForEach(bs =>
                {
                    if (string.IsNullOrWhiteSpace(bs.AuthType))
                        bs.AuthType = bs.AuthSourceType.ToInt() == 1 ?
                                authShops.FirstOrDefault(m => m.Id == bs.CaiNiaoAuthInfoId)?.PlatformType :
                                authInfos?.Where(m => m.Id == bs.CaiNiaoAuthInfoId).FirstOrDefault()?.AuthType ?? "";
                    //bs.AuthType = bs.AuthSourceType.ToInt() == 1 ? (isFromShop ? bs.ToPlatformType.ToString2() : bs.FromPlatformType.ToString2()) : authInfos?.Where(m => m.Id == bs.CaiNiaoAuthInfoId).FirstOrDefault()?.AuthType ?? "";
                });
            }
            return list;
        }

        //public List<BranchShareRelationLogDetail> GetUsedList(OrderSearchModel model, bool isFromShop, bool isLoadDeleted = false, bool isUsedShare = false)
        //{
        //    return _repository.GetUsedList(model, isFromShop, isLoadDeleted, isUsedShare);
        //}
        public List<BranchShareRelationLogDetail> GetUsedDetailList2(OrderSearchModel model, bool isFromShop)
        {
            return _repository.GetUsedDetailList2(model, isFromShop);
        }
        public List<BranchShareRelation> GetList(string where, object param)
        {
            return _repository.GetList(where, param);
        }

        public List<BranchShareRelation> GetList2(string where, object param)
        {
            var sql = @"   
    SELECT t1.*,t2.NickName AS FromShopName,t3.NickName AS ToShopName FROM dbo.P_BranchShareRelation AS t1 WITH(NOLOCK)
	INNER JOIN dbo.P_Shop AS t2 WITH(NOLOCK) ON t1.fromid = t2.Id
	INNER JOIN dbo.p_shop AS t3 WITH(NOLOCK) ON t1.toid = t3.Id " + where;

            return _repository.DbConnection.Query<BranchShareRelation>(sql, param).ToList();
        }

        public void UpdateRemark(int id, string remark)
        {
            _repository.UpdateRemark(id, remark);
        }

        public void UpdateShareStatus(int id, string status)
        {
            _repository.UpdateShareStatus(id, status);
        }

        public void UpdateShareStatus(int id, string status, BranchShareRelationLog log)
        {
            var updateResult = _repository.UpdateShareStatus(id, status);
            if (updateResult)
                _logService.Add(log);
        }


        public BranchShareRelation GetById(int id)
        {
            return _repository.GetById(id);
        }

        public List<BranchShareRelation> GetByIds(List<int> ids)
        {
            return _repository.GetByIds(ids);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="statusList"></param>
        /// <param name="sDateStr"></param>
        /// <param name="eDateStr"></param>
        /// <param name="isWdUser"></param>
        /// <param name="toSystemShopIds">使用者系统店铺Id</param>
        /// <returns></returns>
        public List<WaybillCodeSimpleModel> GetUsedDetailList(List<int> ids, List<int> statusList, string sDateStr, string eDateStr, bool isWdUser = false, List<int> toSystemShopIds = null)
        {
            //通过系统店铺Id拿到FxUserId
            var fxUserWithDbNameIdModels = new List<FxUserWithDbNameIdModel>();
            if (toSystemShopIds != null && toSystemShopIds.Any())
            {
                fxUserWithDbNameIdModels = _fxUserShopService.GetFxUserIdBySystemShopId(toSystemShopIds);
            }

            var details = _repository.GetUsedDetailList(ids, statusList, sDateStr, eDateStr, isWdUser, fxUserWithDbNameIdModels);

            #region 收件人信息脱敏
            var tempOrders = details.Select(x => new LogicOrder { PlatformOrderId = x.CustomerOrderId.IsNullOrEmpty() ? x.OrderId : x.CustomerOrderId, LogicOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver }).ToList();
            FxPlatformEncryptService.EncryptOrders(tempOrders, fromType: EncryptFromType.WaybillCode);
            foreach (var d in details)
            {
                var decyptOrder = tempOrders.FirstOrDefault(x => x.LogicOrderId == d.OrderId);
                if (decyptOrder != null)
                    d.Reciver = decyptOrder.ToName;
            }

            var notEncryptOrders = details.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.ReciverPhone?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
            notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
            if (notEncryptOrders.Any())
                EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(resultNew.Rows);  


            #endregion

            ////针对拼多多的需要先解密
            //var pddDetails = details?.Where(x => x.PlatformType == PlatformType.Pinduoduo.ToString())?.ToList();
            //if (pddDetails != null && pddDetails.Any())
            //{
            //    try
            //    {
            //        var tempOrders = pddDetails.Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver }).ToList();
            //        TryToDecryptPddOrders(tempOrders, true, "ToName");
            //        //按店铺分组
            //        pddDetails.ForEach(item =>
            //        {
            //            var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId && x.ShopId == item.ShopId);
            //            if (decryptedOrder != null)
            //                item.Reciver = decryptedOrder.ToName;
            //            else
            //                item.Reciver = "***";
            //        });
            //    }
            //    catch (Exception ex)
            //    {
            //        Log.WriteError($"单号分享查看明细时针对拼多多底单记录进行解密时发生错误：{ex}");
            //        pddDetails?.ForEach(x =>
            //        {
            //            x.Reciver = "***";
            //        });
            //    }
            //}

            return details;
        }

        /// <summary>
        /// 尝试解析拼多多加密信息
        /// </summary>
        /// <param name="allOrders"></param>
        /// <param name="isMask">是否打码格式</param>
        /// <param name="field">指定要解析的字段，不填解析所有，支持：ToName,ToMobile,ToAddress</param>
        public static void TryToDecryptPddOrders(List<Order> allOrders, bool isMask = true, string field = "", bool isFx = false)
        {
            var allShopIds = allOrders.Select(x => x.ShopId).Distinct().ToList();
            if (allShopIds == null || allShopIds.Any() == false)
                return;
            var allShops = new List<Shop>();
            if (CustomerConfig.IsFendanSite)
            {
                var sids = allOrders.Select(f => f.ShopId);
                allShops = (new ShopService()).GetShopAndSyncStatusById(null, sids, ShopSyncType.Order).ToList();
                allShops?.ForEach(item =>
                {
                    item.LastSyncMessage = item.OrderSyncStatus?.LastSyncMessage;
                });
            }
            else
            {
                allShops = new ShopService().GetShopByIds(allShopIds);
            }
            if (allShops == null || allShops.Any() == false)
                return;
            allOrders.GroupBy(x => x.ShopId).ToList().ForEach(g =>
            {
                var os = g.ToList();
                var temp = allShops.FirstOrDefault(x => x.Id == g.Key);
                var service = PlatformFactory.GetPlatformService(temp) as PinduoduoPlatformService;
                try
                {
                    if (!isFx)
                        service.DecryptBatch(os, isMask, field);
                    else
                    {
                        if (service.PingNoThrow())
                            service.DecryptBatch(os, isMask, field);
                        else
                        {
                            allOrders.ForEach(o =>
                            {
                                o.ToName = "***";
                                o.ToPhone = "***";
                                o.ToAddress = "***";
                                o.ToFullAddress = o.ToProvince.ToString2() + o.ToCity.ToString2() + o.ToCounty.ToString2() + "***";
                            });
                            Log.WriteError($"【{temp.NickName}】授权过期，无法正常解密.");
                        }
                    }
                }
                catch (LogicException ex)
                {
                    var msg = ex.Message;
                    if (msg.Contains("频繁"))
                        throw new LogicException($"【{temp.NickName}】由于拼多多平台限制，解密收件人信息过于频繁，打印中断。请使用拼多多电子面单打印，或减少单次打印的订单数量并过会儿重试下。");
                    else if (msg.Contains("授权"))
                        throw ex;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"【{temp.NickName}】解密异常：{ex}");
                    throw ex;
                }
            });
        }


        public List<WaybillCodeUsedModel2> GetCurrenMonthStatistic(List<int> shareIds, string sDateStr, string eDateStr)
        {
            return _repository.GetShareWaybillCodeStatistic(shareIds, sDateStr, eDateStr);
        }

        public List<WaybillCodeUsedModel2> GetWaybillCodeUsedList(List<int> ids, bool isFromShop, string sDateStr, string eDateStr)
        {
            //通过分享关系Id拿到ToId对应的FxUserId
            var fxUserWithDbNameIdModels = new List<FxUserWithDbNameIdModel>();
            if (ids != null && ids.Any())
            {
                fxUserWithDbNameIdModels = _fxUserShopService.GetFxUserIdByBranchShareRelationId(ids);
            }
            return _repository.GetWaybillCodeUsedList(ids, isFromShop, sDateStr, eDateStr, fxUserWithDbNameIdModels);
        }
        /// <summary>
        /// 追加余额
        /// </summary>
        /// <returns></returns>
        public BranchShareRelation AppendCount(BranchShareRelation model)
        {
            return _repository.AppendCount(model);
        }

        /// <summary>
        /// 更新分享的使用数量
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public BranchShareRelation UpdateTotalUsedQuantity(BranchShareRelation model)
        {
            //日志开始subLog
            var subLog = LogForOperatorContext.Current.StartStep(new LogForOperator()
            {
                OperatorType = "修改单号使用数量"
            });
            subLog.Request = model;

            model.IsUpdateSuccess = false; //重置更新成功标识

            var result = _repository.UpdateTotalUsedQuantity(model);
            if (result == false)
            {
                subLog.Response = $"修改失败";
            }
            else
            {
                subLog.Response = $"修改成功";
                model.IsUpdateSuccess = true; //更新成功
            }

            //扣减使用单号成功才，记录单号使用记录
            if (model.IsUpdateSuccess == true)
            {
                LogForOperator subLog_02 = null;
                var newId = 0; //新数据自增id
                //记录使用单号记录
                //UsedType：-1:占用单号，1：打单使用，2：打单失败释放，3：回收单号释放
                if (model.UsedType == -1)
                {
                    //日志开始
                    subLog_02 = LogForOperatorContext.Current.StartStep(new LogForOperator()
                    {
                        OperatorType = "新增单号占用记录"
                    });

                    try
                    {
                        //占用单号
                        var addModel = new ShareWaybillCodeRecord()
                        {
                            ShareRelationId = model.Id,
                            LockCount = model.UseQty, //占用单号
                            Count = 0, //实际使用单号
                            LastQty = (model.Balance - model.UseQty) > 0 ? (model.Balance - model.UseQty) : 0,
                            UsedType = model.UsedType,
                            ShopId = SiteContext.Current.CurrentShopId,
                            TemplateId = model.UsedTemplateId,
                            CreateTime = DateTime.Now
                        };
                        subLog_02.Request = addModel;
                        //记录使用记录
                        newId = _shareWaybillCodeRecordService.Add(addModel);
                        subLog_02.Response = newId;
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"新增单号占用记录插入失败：{ex}");
                        subLog_02.Response = $"单号占用记录新增失败：{ex}";

                        //重置标识
                        model.IsUpdateSuccess = false;
                        //还回使用的单号
                        model.UseQty = -model.UseQty;
                        try
                        {
                            _repository.UpdateTotalUsedQuantity(model); //还单号，是减使用数量
                        }
                        catch (Exception subEx)
                        {
                            Log.WriteError($"新增单号占用记录插入失败后，还回扣减的单号失败：{subEx}");

                            //单号未还回去，记录到日志表，后续还回使用数量
                            AddRemedyData(model, 1, subEx); //还回扣除的数量
                        }
                    }
                }
                else
                {
                    //使用单号(有useRecordId则说明是打单后更改实际使用单号）
                    if (model.UseRecordId > 0)
                    {
                        //日志开始
                        subLog_02 = LogForOperatorContext.Current.StartStep(new LogForOperator()
                        {
                            OperatorType = "占用单号释放"
                        });

                        try
                        {
                            var shareWaybillCodeRecord = _shareWaybillCodeRecordService.Get(model.UseRecordId);
                            var count = shareWaybillCodeRecord.LockCount + model.UseQty; //这里的useQty为负的返回数量或者是0（当只占用10个，实际只使用5个时，为-5，全使用为0)
                            shareWaybillCodeRecord.Count = count < 0 ? 0 : count; //实际使用数量
                            shareWaybillCodeRecord.UsedType = 1; //占用状态修改为使用状态 ，后续补救占用未释放的可以根据这个状态来。UsedType：-1:占用单号，1：打单使用，2：打单失败释放，3：回收单号释放

                            subLog_02.Request = shareWaybillCodeRecord;

                            //更新
                            _shareWaybillCodeRecordService.Update(shareWaybillCodeRecord); //更新实际使用数量
                            subLog_02.Response = "占用单号释放成功";
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"占用单号释放失败：{ex}");
                            subLog_02.Response = "占用单号释放失败：" + ex.ToString();
                            AddRemedyData(model, 2, ex); //补更新使用记录
                        }

                        newId = model.UseRecordId;
                    }
                    else
                    {
                        //日志开始
                        subLog_02 = LogForOperatorContext.Current.StartStep(new LogForOperator()
                        {
                            OperatorType = "回收释放单号记录插入"
                        });

                        try
                        {
                            //回收的情况
                            var addModel = new ShareWaybillCodeRecord()
                            {
                                ShareRelationId = model.Id,
                                LockCount = 0, //占用单号
                                Count = model.UseQty, //实际使用单号
                                LastQty = (model.Balance - model.UseQty) > 0 ? (model.Balance - model.UseQty) : 0,
                                UsedType = model.UsedType,
                                ShopId = SiteContext.Current.CurrentShopId,
                                TemplateId = model.UsedTemplateId,
                                CreateTime = DateTime.Now
                            };

                            subLog_02.Request = addModel;
                            newId = _shareWaybillCodeRecordService.Add(addModel);
                            subLog_02.Response = newId;
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"回收释放单号记录插入失败：{ex}");
                            subLog_02.Response = "回收释放单号记录插入失败：" + ex.ToString();
                            AddRemedyData(model, 3, ex); //补插入使用记录
                        }
                    }
                }
                model.UseRecordId = newId; //单号使用记录表的id，用于外面与底单表存储关联关系，方便后续排查问题

                //日志结束
                LogForOperatorContext.Current.EndStep(subLog_02);
            }

            //日志开始subLog
            LogForOperatorContext.Current.EndStep(subLog);

            return model;
        }

        /// <summary>
        /// 添加后续用于补救的数据
        /// </summary>
        /// <param name="model"></param>
        /// <param name="operatoryType"></param>
        private void AddRemedyData(BranchShareRelation model, int remedyType, Exception exception)
        {
            //operatoryType：
            //0: 更新使用数量就失败了。非占用单号情况，需要【重新走扣减单号操作（1.更新使用数量，2.添加/更新使用记录）】。
            //1: 占用单号情况，更新使用数量失败了，无需处理。添加使用记录失败了，还回更新的使用数量，更新失败了，则需要补【更新使用数量】。
            //2: 占用单号释放情况，更新使用数量就失败了，参考0的情况。更新使用记录数据失败了，则需要补【更新使用记录】。
            //3: 回收单号的情况，更新使用数量就失败了，参考0的情况。添加使用记录失败的情况，则需要【补使用记录】。

            try
            {
                var sharewaybillremedymodel = new ShareWaybillRemedyData()
                {
                    ShareId = model.Id,
                    UseQty = model.UseQty,
                    UseType = model.UsedType,
                    UseShopId = SiteContext.Current.CurrentShopId,
                    UseTemplateId = model.UsedTemplateId,
                    UseRecordId = model.UseRecordId,
                    RemedyStatus = 0,
                    RemedyType = remedyType,
                    Exception = exception.ToString()
                };
                (new ShareWaybillRemedyDataService()).Add(sharewaybillremedymodel);
            }
            catch (Exception ex)
            {
                Log.WriteError($"单号分享记录补救日志数据失败：{ex}");
            }
        }

        public BranchShareRelation UpdateTotalUsedQuantity_test(BranchShareRelation model, List<int> templateIds, int realCount)
        {
            model.IsUpdateSuccess = false; //重置更新成功标识

            model.IsUpdateSuccess = _repository.UpdateTotalUsedQuantity_test(model);

            //扣减使用单号成功才，记录单号使用记录
            if (model.IsUpdateSuccess == true)
            {
                try
                {
                    var newId = 0;
                    var sumCount = _shareWaybillCodeRecordService.GetSumCountByTemplateIds(templateIds);
                    model = Get(model.Id);
                    model.UsedType = 100; // 100 为数据修复
                    model.UsedTemplateId = templateIds.First();
                    var count = realCount - sumCount;
                    if (count != 0)
                    {
                        newId = _shareWaybillCodeRecordService.Add(new ShareWaybillCodeRecord()
                        {
                            ShareRelationId = model.Id,
                            LockCount = 0, //占用单号
                            Count = count, //实际使用单号
                            LastQty = (model.Balance - model.UseQty) > 0 ? (model.Balance - model.UseQty) : 0,
                            UsedType = model.UsedType,
                            ShopId = model.ToId,
                            TemplateId = model.UsedTemplateId,
                            CreateTime = DateTime.Now
                        });
                    }
                    else
                    {
                        newId = 1;
                    }
                }
                catch
                {

                }

                model.IsUpdateSuccess = true;

                ////记录单号使用记录失败的情况，则把占用的单号还回去。
                //if (newId <= 0)
                //{
                //    //重置标识
                //    model.IsUpdateSuccess = false;
                //    //还回使用的单号
                //    model.UseQty = -model.UseQty;
                //    var r = _repository.UpdateTotalUsedQuantity(model);
                //}
                //else
                //{
                //    model.UseRecordId = newId; //单号使用记录表的id，用于外面与底单表存储关联关系，方便后续排查问题
                //}
            }

            return model;
        }

        public List<BranchShareRelationModel> GetShareShopsAndCompanys(int shopId, bool isFromShop = true, bool needDeleteShare = false)
        {
            var list = _repository.GetShareShopsAndCompanys(shopId, isFromShop, needDeleteShare);
            list.ForEach(m =>
            {
                m.PlatformType_CN = CustomerConfig.ConvertPlatformTypeToName(m.PlatformType.ToString2());
                //兼容旧版小红书之前数据，BillVersion未赋值过
                if (m.AuthType.ToLower() == "xiaohongshu" && m.BillVersion != 2)
                    m.BillVersion = 1;
            });
            return list;
        }

        public List<BranchShareRelationLogDetail> GetAppendLogList(OrderSearchModel model)
        {
            return _repository.GetAppendLogList(model);
        }

        public List<BranchSharedViewModel> GetBranchSharedViewModelList(List<int> templateIds)
        {
            return _repository.GetBranchSharedViewModelList(templateIds);
        }

        public List<SeachWaybillCodeFromViewModel> SearchWaybillCodeFromWhere(List<string> waybillCodes, int shopId)
        {
            var barnchDict = new Dictionary<int, BranchSharedViewModel>();
            var shopDict = new Dictionary<int, Shop>();
            var usedShopIds = new List<int>();

            //1.查询各个平台的底单数据
            //var dataList = (new WaybillCodeService()).GetWaybillCodeInfoInEveryDb(waybillCodes, SiteContext.Current.CurrentLoginShop.PlatformType);
            var dataList = (new WaybillCodeService()).GetWaybillCodeInfoInEveryDb(waybillCodes, shopId);
            dataList?.ForEach(d => d.TemplateId = Math.Abs(d.TemplateId));
            //2.查询出分享关系
            var templateIds = dataList?.Select(f => f.TemplateId).Distinct().ToList();
            var branchSharedViewModelList = GetBranchSharedViewModelList(templateIds);

            branchSharedViewModelList.ForEach(f =>
            {
                usedShopIds.Add(f.ToId);
                barnchDict.Add(f.TemplateId, f);
            });

            //3.查询出所有的使用店铺
            var shopIds = dataList?.Select(f => f.ShopId).Distinct().ToList();
            usedShopIds.AddRange(shopIds);
            var shops = _shopService.GetShopByIds(usedShopIds, "Id,PlatformType,NickName");

            shops.ForEach(f =>
            {
                shopDict.Add(f.Id, f);
            });

            var findedWaybill = new Dictionary<string, SeachWaybillCodeFromViewModel>();

            var currentLoginShopIsFromId = branchSharedViewModelList.Any(f => f.FromId == shopId);

            foreach (var item in dataList)
            {
                var searchItem = new SeachWaybillCodeFromViewModel()
                {
                    ExpressWayBillCode = item.ExpressWayBillCode,
                    GetDate = item.GetDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    Reciver = item.Reciver,
                    ShopId = item.ShopId,
                    TemplateId = item.TemplateId,
                    OrderId = item.OrderId
                };
                BranchSharedViewModel branchModel = null;
                if (barnchDict.Keys.Contains(item.TemplateId))
                    branchModel = barnchDict[item.TemplateId];

                //不是分享的单号，且不是当前登录店铺的分享关系下的单号显示未找到
                if (branchModel == null || (branchModel.FromId != shopId && branchModel.ToId != shopId)) continue;

                searchItem.Remark = branchModel.Remark ?? "";
                if (currentLoginShopIsFromId)
                {
                    Shop shop = null;
                    if (shopDict.Keys.Contains(branchModel.ToId))
                        shop = shopDict[branchModel.ToId];
                    if (shop == null) continue;

                    searchItem.ShopName = shop.NickName;
                    searchItem.ShopPlatform = CustomerConfig.ConvertPlatformTypeToName(shop.PlatformType);
                    searchItem.WaybillPlatform = shop.PlatformType;
                }
                else
                {
                    Shop shop = null;
                    if (shopDict.Keys.Contains(item.ShopId))
                        shop = shopDict[item.ShopId];
                    if (shop == null) continue;

                    searchItem.ShopName = shop.NickName;
                    searchItem.ShopPlatform = CustomerConfig.ConvertPlatformTypeToName(shop.PlatformType);
                    searchItem.WaybillPlatform = shop.PlatformType;
                }
                findedWaybill.Add(searchItem.ExpressWayBillCode, searchItem);
            }

            //针对拼多多的需要先解密
            var pddDetails = findedWaybill?.Where(x => x.Value.ShopPlatform == "拼多多")?.ToList();
            if (pddDetails != null && pddDetails.Any())
            {
                try
                {
                    var tempOrders = pddDetails.Select(x => x.Value).Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver }).ToList();
                    TryToDecryptPddOrders(tempOrders, true, "ToName");
                    //按店铺分组
                    pddDetails.ForEach(kv =>
                    {
                        var item = kv.Value;
                        var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId && x.ShopId == item.ShopId);
                        if (decryptedOrder != null)
                            item.Reciver = decryptedOrder.ToName;
                        item.Reciver = item.Reciver.ToEncryptName();
                    });
                }
                catch (Exception ex)
                {
                    Log.WriteError($"单号分享查看明细时针对拼多多底单记录进行解密时发生错误：{ex}");
                    pddDetails?.ForEach(x =>
                    {
                        x.Value.Reciver = "***";
                    });
                }
            }

            var result = new List<SeachWaybillCodeFromViewModel>();
            foreach (var item in waybillCodes)
            {
                if (findedWaybill.ContainsKey(item) == false)
                {
                    result.Add(new SeachWaybillCodeFromViewModel()
                    {
                        ExpressWayBillCode = item,
                        ShopId = -1,
                        Reciver = "未找到"
                    });
                }
                else
                {
                    result.Add(findedWaybill[item]);
                }
            }
            return result;
        }

        /// <summary>
        /// 获取指定单号分享使用过的店铺ID
        /// </summary>
        /// <param name="branchShareRelationId"></param>
        /// <returns></returns>
        public List<int> GetBranchShareUsedShopIds(List<int> branchShareRelationIds)
        {
            return _repository.GetBranchShareUsedShopIds(branchShareRelationIds);
        }

        public List<BranchShareRelation> GetBranchShareIdByShopIds(List<int> shopIds)
        {
            return _repository.GetBranchShareIdByShopIds(shopIds);
        }

        /// <summary>
        ///  是否有接收单号分享
        /// </summary>
        /// <param name="toId">ToId</param>
        public bool IsHasBranchShare(int toId)
        {
            return _repository.IsHasBranchShare(toId);
        }

        public void AotoBranchShareRelationBind(int oldShopId, int newShopId)
        {
            try
            {
                var branchShareList = _repository.GetBranchShareIdByShopIds(new List<int>() { oldShopId });
                Log.Debug(() => $"oldShopId={oldShopId}; newShopId={newShopId};branchShareList={branchShareList.ToJson()}; 单号分享关系转移失败：未更新任何数据。", "AotoBranchShareRelationBind.txt");
                foreach (var item in branchShareList)
                {
                    if (item.FromId == oldShopId)
                    {
                        item.FromId = newShopId;
                    }
                    if (item.ToId == oldShopId)
                    {
                        item.ToId = newShopId;
                    }
                }

                if (branchShareList.Any())
                {
                    int exeResult = _repository.BatchUpdateFields(branchShareList, new List<string>() { "FromId", "ToId" });
                    if (exeResult < 0)
                    {
                        Log.WriteError($"oldShopId={oldShopId}; newShopId={newShopId};branchShareList={branchShareList.ToJson()}; 单号分享关系转移失败：未更新任何数据。", "AotoBranchShareRelationBind.txt");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"oldShopId={oldShopId}; newShopId={newShopId}; 单号分享关系转移失败：{ex.Message}. {ex.StackTrace}", "AotoBranchShareRelationBind.txt");
            }
        }
    }
}
