using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using NPOI.SS.UserModel;
using NPOI.SS.Util;

namespace DianGuanJiaApp.Services
{
    public partial class BuildExccelService
    {
        #region 订单分发Excel导出
        public static IWorkbook BuildFxOrderExcel(List<Order> orders, ExportSetting setting, string fileName)
        {
            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("订单信息");

            if (orders == null || !orders.Any())
                return workbook;

            var curShop = SiteContext.Current.CurrentLoginShop;
            var sids = orders.Select(x => x.ShopId).Distinct().ToList();
            var shops = new ShopService().GetShopByIds(sids);

            #region 收件人加密解密
            if (shops.Any(x => x.PlatformType == PlatformType.Pinduoduo.ToString() || x.PlatformType == PlatformType.Taobao.ToString() || x.PlatformType == PlatformType.Jingdong.ToString()))
            {
                orders.GroupBy(g => g.ShopId).ToList().ForEach(g =>
                {
                    var shop = shops.FirstOrDefault(x => x.Id == g.Key) ?? new Shop();
                    var pt = shop.PlatformType;
                    if (pt == PlatformType.Taobao.ToString() || pt == PlatformType.Jingdong.ToString())
                    {
                        orders.ForEach(o =>
                        {
                            EncryptReceiverInfo(o);
                        });
                    }
                    else if (pt == PlatformType.Pinduoduo.ToString())
                    {
                        try
                        {
                            new OrderService(shop).TryToDecryptPddOrders(orders);
                            orders.ForEach(o =>
                            {
                                EncryptReceiverInfo(o);
                            });
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"拼多多店铺【{shop.Id}】解密失败：{ex}");
                            //授权过期处理
                            g.ToList().ForEach(t =>
                            {
                                t.ToMobile = "****";
                                t.ToPhone = "****";
                                t.BuyerMemberName = "***";
                                t.BuyerWangWang = "***";
                                t.ToName = "***";
                                t.ToAddress = t.ToAddress.ToPddEncryptAddress();
                                t.ToFullAddress = t.ToProvince + t.ToCity + t.ToCounty + t.ToAddress.ToPddEncryptAddress();
                            });
                        }
                    }
                });
            }
            #endregion

            //// 设置发件人
            //new OrderService().SetSellerInfo(orders);

            #region 查询底单打印记录 (已经在查询中赋值)
            //List<OrderSelectKeyModel> keys = orders.Select(m => new OrderSelectKeyModel()
            //{
            //    Id = m.Id,
            //    ShopId = curShop.Id,
            //    PlatformOrderId = m.LogicOrderId,
            //    ChildPlatformOrderIds = m.ChildLogicOrderId?.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).ToList()
            //}).ToList();

            //var needExportPrintInfo = false;
            //var printDic = new Dictionary<string, List<WaybillCode>>();
            //var printHistorys = new List<WaybillCode>();
            //if (setting.CheckedItems.Any(m => m.Value.ToString2() == "Export_LastWaybillCode" || m.Value.ToString2() == "SenderCompany"))
            //{
            //    needExportPrintInfo = true;
            //    //var fields = new List<string> { "w.ShopId", "w.OrderId", "w.ExpressWayBillCode", "w.ExpressName", "w.GetDate" };

            //    //var keys = orders.Where(o => o.LastExpressPrintTime != null || o.IsPreviewed || !string.IsNullOrEmpty(o.LastWaybillCode))
            //    //  .SelectMany(x => x.OrderItems)
            //    //  .Select(x => new OrderSelectKeyModel { PlatformOrderId = x.OrignalOrderId.IsNullOrEmpty() ? x.LogicOrderId : x.OrignalOrderId, ShopId = curShop.Id }).ToList();
            //    //printHistorys = new WaybillCodeRepository().GetWaybillCodes(keys, false)?.OrderByDescending(m => m.GetDate).ToList() ?? new List<WaybillCode>();

            //    printHistorys.ForEach(p =>
            //    {
            //        var key = p.OrderId + curShop.Id;
            //        if (!printDic.ContainsKey(key))
            //            printDic.Add(key, new List<WaybillCode> { p });
            //        else
            //            printDic[key].Add(p);
            //    });
            //}
            #endregion

            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook);
            ICellStyle contentLongStyle = GetContentCellStyleByToLongCol(workbook);
            ICellStyle contentMergeStyle = GetContentCellStyleByToMerge(workbook);
            ICellStyle contentLongMergeStyle = GetContentCellStyleByToLongColMerge(workbook);
            IRow header = sheet.CreateRow(0);
            header.Height = 15 * 20;
            // 获取勾选的商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 除商品信息外勾选的内容
            var otherChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "productMerge").ToList();
            //// 添加省市区详细地址的扩展
            //var extChkItems = JsonExtension.ToList<CheckedItem>(JsonExtension.ToJson(setting.CheckedItems));

            List<string> headNames = setting.CheckedItems.Select(m => m.Text).ToList();
            // 商品信息单列显示，列头移除单列商品信息
            if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
            {
                headNames = setting.CheckedItems.Where(m => m.From != "product").Select(m => m.Text).ToList();
            }
            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();
                    headNames.RemoveAt(headIndex);
                    headNames.InsertRange(headIndex, addr);

                    otherChkItems.RemoveAt(headIndex);
                    otherChkItems.InsertRange(headIndex, addrLst);
                }
            }

            // 第一行填充表头信息和样式
            int index = 0;
            // 设置Excel列头内容和样式
            headNames.ForEach(name =>
            {
                //设置列宽度
                //sheet.SetColumnWidth(index, 30 * 256);
                SetColumnWidth(sheet, name, index);
                // 设置列名和样式
                header.CreateCell(index).SetCellValue(name);
                header.GetCell(index).CellStyle = headStyle;
                index++;
            });

            // 第二行开始填充Excel内容
            index = 1; // 订单总行数
            //var itemRowIndex = 1; // 实际总行数
            //var orderCategorysSetting = new CommonSettingService().Get("OrderCategorySet", SiteContext.Current.CurrentShopId);
            //List<OrderCategory> orderCategorys = orderCategorysSetting == null || orderCategorysSetting.Value.IsNullOrEmpty() ? new List<OrderCategory>() : JsonExtension.ToList<OrderCategory>(orderCategorysSetting.Value.ToString2());

            orders.ForEach(order =>
            {
                var dic = order.ToDictionary();
                IRow row = sheet.CreateRow(index);
                row.Height = 20 * 20;
                var isAddrMerge = true;
                var itemRowIndex = index;

                // 合并订单行内容
                List<CellRangeAddress> regionLst = new List<CellRangeAddress>();
                if (setting.Export_ProductShowStyle == "Export_ShowMutilLine_Merge")
                {
                    if (order.OrderItems.Count > 1 && productChkItems.Count > 0)
                    {
                        otherChkItems.ForEach(item =>
                        {
                            var itemIndex = headNames.IndexOf(item.Text);
                            if (itemIndex != -1)
                            {
                                // 商品信息之前的内容行合并
                                int startRowIndex = index;
                                int endRowNumIndex = index + order.OrderItems.Count - 1;

                                var productRegion = new CellRangeAddress(startRowIndex, endRowNumIndex, itemIndex, itemIndex);
                                sheet.AddMergedRegion(productRegion);
                            }
                        });
                    }
                }

                var platformOrderIds = string.Empty;
                //if (isCustomerOrder)
                //    platformOrderIds = dic["CustomerOrderId"].ToString2().IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : dic["CustomerOrderId"].ToString2();
                //else
                //{
                var childOrderIds = dic["ChildOrderId"].ToString2();
                platformOrderIds = childOrderIds.IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : childOrderIds.Replace(",", "\n").Replace("，", "\n");
                //}

                //List<WaybillCode> printHistoryLst = null;
                //if (needExportPrintInfo)
                //{
                //    printDic.TryGetValue(order.LogicOrderId + curShop.Id, out printHistoryLst);
                //    if (printHistoryLst == null)
                //        printDic.TryGetValue("C" + order.LogicOrderId + curShop.Id, out printHistoryLst);
                //    if (printHistoryLst == null && !order.MergeredOrderId.IsNullOrEmpty())
                //        printDic.TryGetValue(order.MergeredOrderId + curShop.Id, out printHistoryLst);
                //}

                // 商品信息所在列序号集合
                var productCellIndexLst = new List<int>();
                // 填充每一列的值
                setting.CheckedItems.ForEach(chkItem =>
                {
                    var field = chkItem.Value.Replace("Export_", "");
                    var text = chkItem.Text;
                    var from = chkItem.From;

                    // 收件人地址不合并1列显示，则移除收件人地址之后列往后移动3列
                    var cellIndex = setting.CheckedItems.IndexOf(chkItem);
                    if (!isAddrMerge)
                    {
                        cellIndex = cellIndex + 3;
                    }

                    #region 填充每列的内容
                    if (from == "shop")
                    {
                        var shop = shops.FirstOrDefault(m => m.Id == order.ShopId);
                        row.CreateCell(cellIndex).SetCellValue(shop == null ? "" : shop.NickName);
                        row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    //else if (from == "ordercategory")
                    //{
                    //    var category = orderCategorys.FirstOrDefault(m => m.Id == order.CategoryId);
                    //    row.CreateCell(cellIndex).SetCellValue(category == null ? "" : category.Alias);
                    //    row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    //}
                    else if (from == "order")
                    {
                        if (field == "ErpStatus")
                        {
                            // 订单状态
                            var refundStatus = order.RefundStatus;
                            var status = order.PlatformStatus;
                            var exStatus = new List<string> { "-2", "1", "999" };
                            var val = string.Empty;
                            if (status == "waitsellersend")
                                val = "待发货";
                            else if (status == "inrefund" && (refundStatus == "WAIT_SELLER_AGREE" || refundStatus == "REFUND_SUCCESS"))
                                val = "退款单";
                            else if (status == "sended" && exStatus.Contains(order.ExtField1.ToString2()) && (refundStatus == "WAIT_SELLER_AGREE" || refundStatus == "REFUND_SUCCESS"))
                                val = "异常单";
                            else if (status == "sended")
                                val = "待收货";
                            else if (status == "success")
                                val = "交易成功";
                            else if (status == "close" || status == "cancel")
                                val = "交易取消";
                            else
                                val = "未知状态";
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "ToAddress")
                        {
                            // 收件人地址判断省市区地址是否合并一列显示
                            if (setting.Export_AddressShowStyle == "Export_Address_Merge")
                            {
                                // 省市区地址在同列显示
                                var address = order.ToProvince.ToString2() + order.ToCity.ToString2() + order.ToCounty.ToString2() + order.ToAddress.ToString2();
                                row.CreateCell(cellIndex).SetCellValue(address);
                                row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                            }
                            else
                            {
                                // 省市区地址在不同列显示
                                row.CreateCell(cellIndex).SetCellValue(order.ToProvince.ToString2());
                                row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 1).SetCellValue(order.ToCity.ToString2());
                                row.GetCell(cellIndex + 1).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 2).SetCellValue(order.ToCounty.ToString2());
                                row.GetCell(cellIndex + 2).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 3).SetCellValue(order.ToAddress.ToString2());
                                row.GetCell(cellIndex + 3).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;

                                isAddrMerge = false;
                            }
                        }
                        else if (field == "PlatformType" || field == "OrderFrom")
                        {
                            // 订单来源
                            var val = dic[field].ToString2().Trim().ToLower() == "alibaba" ? "1688" : dic[field].ToString2().Trim().ToLower();
                            val = val == "importorder" ? "导入单" : (val == "customerorder" ? "录入单" : val);
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "OrderTime")
                        {
                            // 订单日期
                            var status = dic["PlatformStatus"].ToString2().Trim().ToLower();
                            var val = status == "waitbuyerpay" || status == "confirm_goods_but_not_fund" ? dic["CreateTime"].ToString2() : dic["PayTime"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "OrderCount")
                        {
                            // 订单数
                            var count = order.SubOrders?.Count ?? 0;
                            row.CreateCell(cellIndex).SetCellValue(count);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "SellerRemark" || field == "BuyerRemark")
                        {
                            var arr = dic[field].ToString2().Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                            var str = arr == null ? "" : string.Join(";", arr);
                            row.CreateCell(cellIndex).SetCellValue(str);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "ToPhone")
                        {
                            var phone = dic["ToMobile"].ToString2().IsNullOrEmpty() ? dic["ToPhone"].ToString2() : dic["ToMobile"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(phone);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "SenderPhone")
                        {
                            var phone = dic["SenderMobile"].ToString2().IsNullOrEmpty() ? dic["SenderPhone"].ToString2() : dic["SenderMobile"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(phone);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "PlatformOrderId")
                        {
                            //var platformOrderIds = string.Empty;
                            //if (isCustomerOrder)
                            //    platformOrderIds = dic["CustomerOrderId"].ToString2().IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : dic["CustomerOrderId"].ToString2();
                            //else
                            //{
                            //    var childOrderIds = dic["ChildOrderId"].ToString2();
                            //    platformOrderIds = childOrderIds.IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : childOrderIds.Replace(",", "\n").Replace("，", "\n");
                            //}

                            row.CreateCell(cellIndex).SetCellValue(platformOrderIds);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "SenderCompany")
                        {
                            var expressName = order.WaybillCodes?.FirstOrDefault()?.ExpressName ?? "";
                            row.CreateCell(cellIndex).SetCellValue(expressName);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "LastWaybillCode")
                        {
                            var printHistoryStr = string.Empty;
                            if (order.WaybillCodes != null && order.WaybillCodes.Any())
                            {
                                var printHistory = order.WaybillCodes?.OrderByDescending(c => c.CreateDate).FirstOrDefault(); //最近一次打印记录
                                var printHistoryLst = printHistory == null ? new List<WaybillCodeViewModel>() : order.WaybillCodes.Where(m => m.CreateDate == printHistory.CreateDate).ToList();//根据打印时间判断是否是一单多包
                                                                                                                                                                                                // 一单多包导出多个运单号
                                if (printHistoryLst != null && printHistoryLst.Count() > 1)
                                    printHistoryStr = string.Join("\n", printHistoryLst?.Select(m => m.WaybillCode).ToList() ?? new List<string>());
                                else
                                    printHistoryStr = printHistory?.WaybillCode.ToString2() ?? "";
                            }
                            row.CreateCell(cellIndex).SetCellValue(printHistoryStr);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        //else if (field == "LastSendTime")
                        //{
                        //    var lastSendTime = dic["LastSendTime"].ToString2();
                        //    lastSendTime = lastSendTime.IsNullOrEmpty() ? dic["AllDeliveredTime"].ToString2() : lastSendTime;
                        //    if (lastSendTime.IsNullOrEmpty() && !order.ChildOrderId.IsNullOrEmpty())
                        //    {
                        //        var okeys = platformOrderIds.SplitWithString("\n").Where(m => !m.IsNullOrEmpty()).Select(id => new OrderSelectKeyModel { PlatformOrderId = id, ShopId = order.ShopId }).ToList();
                        //        var childOrders = new OrderService().GetOrders(okeys, fields: new List<string> { "o.Id", "o.PlatformOrderId", "o.ShopId", "o.LastSendTime", "o.AllDeliveredTime", "oi.Id" });
                        //        var newLastTime = childOrders?.Max(o => o.LastSendTime) ?? childOrders?.Max(o => o.AllDeliveredTime);
                        //        if (newLastTime != null)
                        //            lastSendTime = newLastTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                        //    }
                        //    lastSendTime = lastSendTime.IsNullOrEmpty() ? lastSendTime : lastSendTime.toDateTime().ToString("yyyy-MM-dd HH:mm:ss");
                        //    row.CreateCell(cellIndex).SetCellValue(lastSendTime);
                        //    row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        //}
                        else
                        {
                            object obj;
                            var isInDic = dic.TryGetValue(field, out obj);
                            var val = isInDic ? obj.ToString2() : "";
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = field == "SenderAddress" ? (row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle) : (row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle);
                        }
                    }
                    else if (from == "productMerge")
                    {
                        // 商品相关信息所在列的序号
                        if (!productCellIndexLst.Contains(cellIndex))
                            productCellIndexLst.Add(cellIndex);

                        if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
                        {
                            #region 商品信息合并1行1列显示
                            var productContent = new StringBuilder();
                            // 获取商品信息
                            order.OrderItems.ForEach(item =>
                            {
                                var itemDic = item.ToDictionary();
                                var productSubContent = new StringBuilder();
                                // 商品信息合并1列显示
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = field2 == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field2].ToString2();
                                    if (field2 == "Count")
                                        val = $"*{val},";
                                    else if ((field2 == "Weight" || field2 == "SkuWeight") && val == "0")
                                        val = "";
                                    productSubContent.Append($"{val},");
                                });

                                productContent.Append($"{productSubContent.ToString2().TrimEnd(",")};\n");
                            });

                            row.CreateCell(cellIndex).SetCellValue(productContent.ToString2());
                            row.GetCell(cellIndex).CellStyle = contentLongStyle;
                            #endregion
                        }
                        else
                        {
                            #region 商品多行列合并显示
                            var isFirstItemRowIndex = true;
                            itemRowIndex = index;
                            // 获取商品信息
                            order.OrderItems.ForEach(item =>
                            {
                                var itemDic = item.ToDictionary();
                                var productContent = new StringBuilder();
                                // 商品信息合并1列显示
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = field2 == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field2].ToString2();
                                    if (field2 == "Count")
                                        val = $"*{val},";
                                    else if ((field2 == "Weight" || field2 == "SkuWeight") && val == "0")
                                        val = "";
                                    productContent.Append($"{val},");
                                });

                                if (isFirstItemRowIndex)
                                {
                                    row.CreateCell(cellIndex).SetCellValue(productContent.ToString2().TrimEnd(","));
                                    row.GetCell(cellIndex).CellStyle = contentLongStyle;
                                    isFirstItemRowIndex = false;
                                }
                                else
                                {
                                    // 创建新行填充商品信息
                                    IRow row2 = sheet.GetRow(itemRowIndex) ?? sheet.CreateRow(itemRowIndex);
                                    row2.Height = 25 * 20;
                                    // 商品单独行显示，无订单项数据
                                    row2.CreateCell(cellIndex).SetCellValue(productContent.ToString2().TrimEnd(","));
                                    row2.GetCell(cellIndex).CellStyle = contentLongStyle;
                                }

                                itemRowIndex++;
                            });
                            #endregion
                        }
                    }
                    else if (from == "product" && setting.Export_ProductShowStyle != "Export_ShowOneLine")
                    {
                        // 商品相关信息所在列的序号
                        if (!productCellIndexLst.Contains(cellIndex))
                            productCellIndexLst.Add(cellIndex);

                        #region 商品属性多列显示
                        var isFirstItemRowIndex = true;
                        itemRowIndex = index;
                        // 获取商品信息
                        order.OrderItems.ForEach(item =>
                        {
                            var itemDic = item.ToDictionary();
                            var val = field == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field].ToString2();
                            if ((field == "Weight" || field == "SkuWeight") && val == "0")
                                val = "";
                            if (isFirstItemRowIndex)
                            {
                                row.CreateCell(cellIndex).SetCellValue(val);
                                row.GetCell(cellIndex).CellStyle = field == "ProductSubject" ? contentLongStyle : contentStyle;
                                isFirstItemRowIndex = false;
                            }
                            else
                            {
                                // 创建新行填充商品信息
                                IRow row2 = sheet.GetRow(itemRowIndex) ?? sheet.CreateRow(itemRowIndex);
                                row2.Height = 25 * 20;
                                row2.CreateCell(cellIndex).SetCellValue(val);
                                row2.GetCell(cellIndex).CellStyle = field == "ProductSubject" ? contentLongStyle : contentStyle;
                            }
                            itemRowIndex++;
                        });
                        #endregion
                    }
                    #endregion
                });

                // 商品多行显示，填充订单数据
                int itemCount = order.OrderItems.Count;
                if (setting.Export_ProductShowStyle == "Export_Export_Split" && itemCount > 1)
                {
                    var tmpRowIndex = index;
                    IRow firstRow = sheet.GetRow(tmpRowIndex);
                    if (firstRow != null)
                    {
                        var firstCells = firstRow.Cells;
                        for (var i = 1; i < itemCount; i++)
                        {
                            // 商品单独行显示，并填充订单项数据
                            IRow nextRow = sheet.GetRow(tmpRowIndex + i);
                            if (nextRow == null) break;
                            for (var j = 0; j < firstCells.Count; j++)
                            {
                                if (!productCellIndexLst.Contains(j))
                                {
                                    var cell = firstCells[j];
                                    if (cell == null) continue;
                                    nextRow.CreateCell(j).SetCellValue(cell.ToString2());
                                    nextRow.GetCell(j).CellStyle = cell.CellStyle;
                                }
                            }
                        }
                    }
                }

                if (itemRowIndex > index)
                    index = itemRowIndex;
                else
                    index++;

                ExcelHelper.AutoSizeRowHeight(workbook, sheet, row);
            });

            return workbook;
        }

        //public static Order LogicOrderToOrder(LogicOrder o)
        //{
        //    // LogicOrder转成Order
        //    var order = new Order
        //    {
        //        PlatformOrderId = o.PlatformOrderId,
        //        ShopId = o.ShopId,
        //        PlatformType = o.PlatformType,
        //        LogicOrderId = o.LogicOrderId,
        //        MergeredOrderId = o.MergeredOrderId,
        //        IsMergered = o.MergeredType == 2,
        //        ToName = o.ToName,
        //        ToMobile = o.ToPhone,
        //        ToProvince = o.ToProvince,
        //        ToCity = o.ToCity,
        //        ToCounty = o.ToCounty,
        //        ToAddress = o.ToAddress,
        //        ToFullAddress = o.ToFullAddress,
        //        BuyerRemark = o.BuyerRemark,
        //        SellerRemark = o.SellerRemark,
        //        CreateTime = o.CreateTime,
        //        PayTime = o.PayTime,
        //        LastSendTime = o.OnlineSendTime,
        //        TotalWeight = o.TotalWeight,
        //        TotalAmount = o.TotalAmount,
        //        LastShipTime = o.LastShipTime,
        //        LastExpressPrintTime = o.ExpressPrintTime,
        //        IsPreviewed = o.IsPreviewed,
        //        LastWaybillCode = o.LastWaybillCode,
        //        ExpressName = o.WaybillCodes?.FirstOrDefault()?.ExpressName,
        //        AllDeliveredTime = o.OnlineSendTime,
        //        WaybillCodes = o.WaybillCodes,
        //        PlatformStatus = o.ErpState,
        //        RefundStatus = o.ErpRefundState,
        //        ExtField1 = o.ExceptionStatus.ToString2(),
        //        FxUserId = o.FxUserId,
        //        UpFxUserId = o.UpFxUserId,
        //        DownFxUserId = o.DownFxUserId,
        //        CustomerOrderId = o.CustomerOrderId,
        //        IsShowSalePrice = o.IsShowSalePrice,
        //        PrintRemark = o.PrintRemark,
        //        SystemRemark = o.SystemRemark
        //    };
        //    o.LogicOrderItems.ForEach(oi =>
        //    {
        //        var item = new OrderItem();
        //        if (o.PlatformType == PlatformType.Virtual.ToString())
        //            item.OrignalPlatformOrderId = oi.OrignalOrderId.IsNotNullOrEmpty() ? oi.OrignalOrderId : oi.PlatformOrderId;
        //        else
        //            item.OrignalPlatformOrderId = oi.PlatformOrderId;

        //        item.OrignalOrderId = oi.OrignalOrderId;
        //        item.PlatformOrderId = oi.PlatformOrderId;
        //        item.LogicOrderId = oi.LogicOrderId;
        //        item.Status = oi.Status;
        //        item.RefundStatus = oi.RefundStatus;
        //        item.ProductSubject = oi.ProductSubject;
        //        item.productCargoNumber = oi.productCargoNumber;
        //        item.CargoNumber = oi.CargoNumber;
        //        item.ProductID = oi.ProductID;
        //        item.SkuID = oi.SkuID;
        //        item.ProductImgUrl = oi.ProductImgUrl;
        //        item.Price = oi.Price;
        //        item.Count = oi.Count;
        //        item.Color = oi.Color;
        //        item.Size = oi.Size;
        //        item.ShortTitle = oi.ShortTitle;
        //        item.SkuShortTitle = oi.SkuShortTitle;
        //        item.ItemAmount = oi.ItemAmount;
        //        if (oi.Weight > 0) //手动设置优先
        //            item.Weight = oi.Weight;
        //        item.SkuWeight = oi.SkuWeight;
        //        item.AuthorId = oi.AuthorId;//达人Id
        //        item.AuthorName = oi.AuthorName;//达人名称
        //        order.OrderItems.Add(item);
        //    });
        //    return order;
        //}

        #endregion

        #region 订单分发-底单导出
        public static IWorkbook BuildFxWaybillCodeExcel(List<WaybillCode> waybillCodeLst, List<WaybillCodeCheckModel> checkedItems, string fileName)
        {
            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet();
            if (waybillCodeLst == null || !waybillCodeLst.Any())
                return workbook;

            //底单中保存的订单编号为逻辑订单号，需要转换成原始订单编号，才能解密收件人信息
            var orderDic = EncryptReceiverInfo(waybillCodeLst);
            var orders = orderDic.Values.ToList();

            //if(orders!=null && orders.Any())
            //    Log.Debug("底单导出测试："+orders.ToJson());

            //获取订单对应的商家信息
            var agents = new List<UserFx>();
            var pathFlowCodes = orderDic?.Values?.Select(x => x.PathFlowCode).Where(x => x.IsNotNullOrEmpty()).Distinct().ToList();
            if (pathFlowCodes != null && pathFlowCodes.Any())
            {
                var pathFlowNodes = new PathFlowRepository().GetPathFlowNodeList(pathFlowCodes);
                var upFxUserIds = pathFlowNodes.Where(x => x.UpFxUserId > 0).Select(x => x.UpFxUserId).Distinct().ToList();
                if (upFxUserIds.Any())
                {
                    //商家
                    var userfxRepository = new UserFxRepository();
                    agents = userfxRepository.GetUsersByFxUserId(upFxUserIds);
                    pathFlowNodes.ForEach(p =>
                    {
                        //var order = orders.FirstOrDefault(x => x.PathFlowCode == p.PathFlowCode);
                        //if (order != null)
                        //    order.UpFxUserId = p.UpFxUserId;
                        var pOrderList = orders.Where(x => x.PathFlowCode == p.PathFlowCode);
                        foreach (var item in pOrderList)
                        {
                            item.UpFxUserId = p.UpFxUserId;
                        }
                    });
                }
            }

            var curShop = SiteContext.Current.CurrentLoginShop;
            var sids = orderDic.Values.Select(x => x.ShopId).Distinct().ToList();
            var shops = new ShopService().GetShopByIds(sids);

            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            checkedItems.ForEach(model =>
            {
                var headName = model.Text.ToString2();
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;
            waybillCodeLst.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;
                var dic = (model ?? new WaybillCode()).ToDictionary();

                colIndex = 0;
                foreach (var item in checkedItems)
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item?.Value.ToString2() ?? "";
                    if (dic.ContainsKey(key) || key == "RowIndex" || key == "ShopName")
                    {
                        var val = key == "RowIndex" ? rowIndex.ToString2() : key == "ShopName" ? "" : dic[key].ToString2();
                        if (key != "RowIndex")
                        {
                            if (key == "ToAddress")
                                val = $"{dic["ToProvince"].ToString2()} {dic["ToCity"].ToString2()} {dic["ToDistrict"].ToString2()} {dic["ToAddress"].ToString2()}";
                            else if (key == "BuyerRemark" || key == "SellerRemark")
                            {
                                var arr = val.Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                                val = arr.Length == 0 ? "" : val;
                            }
                            else if (key == "Status")
                            {
                                if (val == "1")
                                    val = "已打印";
                                else if (val == "2")
                                    val = "已回收";
                                else if (val == "3")
                                    val = "已发货";
                                else
                                    val = "未知状态";
                            }
                            else if (key == "ShopName")
                            {
                                //店铺名称：若当前订单是自己店铺（pfn.UpFxUserId为0）产生的，需根据o.ShopId店铺ID显示对应的店铺名称。否则是商家订单（pfn.UpFxUserId大于0）
                                LogicOrder order = null;
                                orderDic.TryGetValue(dic["OrderId"].ToString2(), out order);
                                if (order == null)
                                    val = curShop.NickName;
                                else
                                {
                                    var agent = agents?.FirstOrDefault(x => x.Id == order.UpFxUserId);
                                    var agentName = agent == null ? shops.FirstOrDefault(x => x.Id == order.ShopId)?.NickName : (agent.NickName.IsNullOrEmpty() ? agent.Mobile : agent.NickName);
                                    val = agentName;
                                }
                            }
                            else if (key == "OrderId")
                            {
                                var customerOrderId = dic["CustomerOrderId"].ToString2();
                                if (!customerOrderId.IsNullOrEmpty())
                                    val = customerOrderId;
                                else
                                {
                                    var orderIdJoin = dic["OrderIdJoin"].ToString2();
                                    if (!orderIdJoin.IsNullOrEmpty())
                                        val = orderIdJoin.Replace(",", "\n");
                                }
                            }
                            else if (key == "ExpressWayBillCode")
                            {
                                var childWaybillCode = dic["ChildWaybillCode"].ToString2();
                                val = $"{val}{(childWaybillCode.IsNullOrEmpty() ? "" : $"/{childWaybillCode}")}";
                            }
                        }

                        if (key == "BuyerRemark" || key == "SellerRemark" || key == "ToAddress" || key == "SendContent")
                            tmpStyle = leftContentStyle;

                        dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                        dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                        colIndex++;
                    }
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;
            });

            return workbook;
        }

        //底单中保存的订单编号为逻辑订单号，需要转换成原始订单编号，才能解密收件人信息
        public static Dictionary<string, LogicOrder> EncryptReceiverInfo(List<WaybillCode> wlst, string fields = "", bool isFx = false)
        {
            if (wlst == null || !wlst.Any())
                return null;
            if (fields.IsNullOrEmpty())
                fields = "o.Id,o.ShopId,o.LogicOrderId,o.PlatformOrderId,o.MergeredType,o.PlatformType,o.PathFlowCode,oi.Id,oi.PlatformOrderId";
            //底单中保存的订单编号为逻辑订单号，需要转换成原始订单编号，才能解密收件人信息
            var logicOrderDict = new Dictionary<string, LogicOrder>();
            var logicOrderIds = wlst.Select(f => f.OrderId).Distinct().ToList();
            var orders = new LogicOrderService().GetOrders(logicOrderIds, fields: fields.SplitToList(","));

            var mids = logicOrderIds.Where(x => !orders.Any(m => m.LogicOrderId == x)).Select(x => x.Replace("C", "")).ToList();
            if (mids != null && mids.Any())
            {
                var mOrders = new LogicOrderService().GetOrders(mids, fields: fields.SplitToList(","));

                mOrders.ForEach(item => { item.LogicOrderId = "C" + item.LogicOrderId; });

                orders.AddRange(mOrders);
            }

            var cids = orders?.Where(x => x.IsMainOrder).SelectMany(x => x.LogicOrderItems).Select(x => x.PlatformOrderId).Distinct().ToList();
            if (cids != null && cids.Any())
            {
                var childOrders = new LogicOrderService().GetLogicOrders(cids, new List<string> { "LogicOrderId", "PlatformOrderId", "ToName", "ToPhone", "ToAddress","PathFlowCode" }).ToList();
                orders.AddRange(childOrders);
            }

            orders?.ToList().ForEach(o =>
            {
                LogicOrder order = null;
                if (!logicOrderDict.TryGetValue(o.LogicOrderId, out order))
                    logicOrderDict.Add(o.LogicOrderId, o);
            });

            var isExistJdOrderOrTbOrder = wlst.Any(f =>
            {
                LogicOrder lo;
                if (logicOrderDict.TryGetValue(f.OrderId, out lo))
                {
                    return lo.PlatformType == PlatformType.Jingdong.ToString() || lo.PlatformType == PlatformType.Taobao.ToString();
                }
                return false;
            }) == true;

            var isExistJdOrder = wlst.Any(f =>
            {
                LogicOrder lo;
                if (logicOrderDict.TryGetValue(f.OrderId, out lo))
                {
                    return lo.PlatformType == PlatformType.Jingdong.ToString();
                }
                return false;
            }) == true;

            var isExistTbOrder = wlst.Any(f =>
            {
                LogicOrder lo;
                if (logicOrderDict.TryGetValue(f.OrderId, out lo))
                {
                    return lo.PlatformType == PlatformType.Taobao.ToString();
                }
                return false;
            }) == true;

            var isExistPddOrder = wlst.Any(f =>
            {
                LogicOrder lo;
                if (logicOrderDict.TryGetValue(f.OrderId, out lo))
                {
                    return lo.PlatformType == PlatformType.Pinduoduo.ToString();
                }
                return false;
            }) == true;

            var isExistTouTiaoOrder = wlst.Any(f =>
            {
                LogicOrder lo;
                if (logicOrderDict.TryGetValue(f.OrderId, out lo))
                {
                    var TouTiaoPlatformArr = new List<string> { PlatformType.ZhiDian.ToString(), PlatformType.TouTiao.ToString(), PlatformType.DouYinXiaoDian.ToString() };
                    return TouTiaoPlatformArr.Contains(lo.PlatformType);
                }
                return false;
            }) == true;

            var isExistKuaiShouOrder = wlst.Any(f =>
            {
                LogicOrder lo;
                if (logicOrderDict.TryGetValue(f.OrderId, out lo))
                {
                    return lo.PlatformType == PlatformType.KuaiShou.ToString();
                }
                return false;
            }) == true;

            var isExistTuanHaoHuoOrder = wlst.Any(f =>
            {
                LogicOrder lo;
                if (logicOrderDict.TryGetValue(f.OrderId, out lo))
                {
                    return lo.PlatformType == PlatformType.TuanHaoHuo.ToString();
                }
                return false;
            }) == true;

            if (isExistJdOrderOrTbOrder) //存在京东或者淘宝订单
            {
                var tbPids = new List<string>(); //淘宝订单编号
                var jdPids = new List<string>(); //京东订单编号
                wlst.ForEach(t =>
                {
                    LogicOrder lo;
                    logicOrderDict.TryGetValue(t.OrderId, out lo);
                    if (lo == null)
                        return;

                    var pids = new List<string>();
                    if (!string.IsNullOrEmpty(t.OrderIdJoin))
                    {
                        pids.AddRange(t.OrderIdJoin.Split(','));
                    }
                    else
                    {
                        pids.Add(t.OrderId.Trim('C'));
                    }

                    if (lo.PlatformType == PlatformType.Jingdong.ToString())
                        jdPids.AddRange(pids);
                    else if (lo.PlatformType == PlatformType.Taobao.ToString())
                        tbPids.AddRange(pids);


                    t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
                    t.Reciver = t.Reciver.ToEncryptName();
                    t.ToAddress = t.ToAddress.ToTaoBaoEncryptAddress();
                    if (lo?.PlatformType == PlatformType.Taobao.ToString())
                    {
                        //淘宝还需加密发件人信息
                        t.Sender = t.Sender.ToEncryptName();
                    }
                });

                //TODO:要取店铺信息，暂时先不弄，订单这里取店铺信息要通过逻辑单上的额shopId来取
                //if (isExistJdOrder)
                //{   //京东安全日志
                //    jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
                //}
                //else
                //{
                //    //记御城河日志
                //    ych_sdk.YchRequestLogger.Order(shop.Id.ToString(), "订单查询", pids);
                //}
            }
            else if (isExistPddOrder)
            {
                var tempOrders = wlst.Select(x => new Order { PlatformOrderId = x.OrderId, LogicOrderId = x.OrderId, Id = x.ShopId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ToAddress }).ToList();
                //_orderService.TryToDecryptPddOrders(tempOrders);

                var findTmpOrders = new List<Order>(); // 部分订单丢失时，无法找到正确的店铺导致无法解密
                tempOrders.ForEach(to =>
                {
                    LogicOrder lo;
                    if (logicOrderDict.TryGetValue(to.PlatformOrderId, out lo))
                    {
                        to.PlatformOrderId = lo.PlatformOrderId;
                        to.ShopId = lo.ShopId;
                        findTmpOrders.Add(to);
                    }
                });

                //地址中如果包含省市区要去掉
                findTmpOrders.ForEach(x =>
                {
                    var replaceStr = x.ToProvince + x.ToCity + x.ToCounty;
                    x.ToAddress = x.ToAddress.Replace(replaceStr, "");
                });
                
                //Log.WriteError("tempOrders:" + tempOrders.ToJson());
                try
                {
                    BranchShareRelationService.TryToDecryptPddOrders(findTmpOrders, true, isFx: isFx);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"拼多多解密异常：{ex}");
                }


                //Log.WriteError("解密后tempOrders:" + tempOrders.ToJson());

                //按店铺分组
                foreach (var item in wlst)
                {
                    var decryptedOrder = findTmpOrders.FirstOrDefault(x => x.LogicOrderId == item.OrderId && x.Id == item.ShopId);
                    if (decryptedOrder != null)
                    {
                        item.Reciver = decryptedOrder.ToName;
                        item.ReciverPhone = decryptedOrder.ToMobile;
                        item.BuyerMemberName = item.Reciver;
                        item.BuyerMemberId = item.Reciver;
                        if (string.IsNullOrWhiteSpace(decryptedOrder.ToFullAddress))//如果是非加密信息，上面TryToDecryptPddOrders方法会return抛出，导致ToFullAddress由于没赋值为null
                            decryptedOrder.ToFullAddress = decryptedOrder.ToProvince + decryptedOrder.ToCity + decryptedOrder.ToCounty + decryptedOrder.ToAddress;
                        item.ToAddress = decryptedOrder.ToFullAddress;
                        item.BuyerMemberName = item.BuyerMemberName.ToEncryptName();
                        item.BuyerMemberId = item.BuyerMemberId.ToEncryptName();
                        item.ReciverPhone = item.ReciverPhone.ToPddEncryptPhone();
                        item.Reciver = item.Reciver.ToEncryptName();
                        item.ToAddress = item.ToAddress.ToPddEncryptAddress();
                    }
                }
            }

            if (isExistTouTiaoOrder || isExistKuaiShouOrder || isExistTuanHaoHuoOrder) //存在抖店订单
            {
                wlst.ForEach(t =>
                {
                    LogicOrder lo;
                    logicOrderDict.TryGetValue(t.OrderId, out lo);
                    if (lo == null)
                        return;

                    t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
                    t.Reciver = t.Reciver.ToEncryptName();
                    t.ToAddress = t.ToAddress.ToTaoBaoEncryptAddress();
                });
            }

            return logicOrderDict;
        }

        #endregion

        #region 订单导出
        private static Order EncryptReceiverInfo(Order order)
        {
            order.ToName = order.ToName.ToEncryptName();
            order.BuyerMemberName = order.BuyerMemberName.ToEncryptName();
            order.BuyerWangWang = order.BuyerWangWang.ToEncryptName();
            order.BuyerMemberId = order.BuyerMemberId.ToEncryptName();
            order.ToPhone = order.ToPhone.ToPddEncryptPhone();
            order.ToMobile = order.ToMobile.ToPddEncryptPhone();
            order.ToAddress = order.ToAddress.ToPddEncryptAddress();
            //order.ToCounty = "**";
            if (order.PlatformType == PlatformType.Taobao.ToString())
            {
                //淘宝还要加密发件人
                order.SenderName = order.SenderName.ToEncryptName();
                order.SenderPhone = order.SenderPhone.ToEncrytPhone();
                order.SenderMobile = order.SenderMobile.ToEncrytPhone();
                order.SenderAddress = order.SenderAddress.ToTaoBaoEncryptAddress();
            }
            return order;
        }

        public static IWorkbook BuildOrderPrintExcel(List<Order> orders, ExportSetting setting, bool isCustomerOrder, string fileName)
        {
            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("订单信息");

            if (orders == null || !orders.Any())
                return workbook;
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (orders != null && orders.Any()
                && (pt == PlatformType.Taobao.ToString()
                || pt == PlatformType.Jingdong.ToString()))
            {
                if (isCustomerOrder == false)
                {
                    orders.ForEach(o =>
                    {
                        EncryptReceiverInfo(o);
                    });
                }
            }

            // 设置发件人
            new OrderService().SetSellerInfo(orders);

            #region 查询底单打印记录
            List<OrderSelectKeyModel> keys = orders.Select(m => new OrderSelectKeyModel()
            {
                Id = m.Id,
                ShopId = m.ShopId,
                PlatformOrderId = m.PlatformOrderId,
                ChildPlatformOrderIds = m.ChildOrderId?.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).ToList()
            }).ToList();

            // 查询打印记录
            var needExportPrintInfo = false;
            var printDic = new Dictionary<string, List<WaybillCode>>();
            var printHistorys = new List<WaybillCode>();
            if (setting.CheckedItems.Any(m => m.Value.ToString2() == "Export_LastWaybillCode" || m.Value.ToString2() == "SenderCompany"))
            {
                needExportPrintInfo = true;
                var fields = new List<string> { "w.ShopId", "w.OrderId", "w.ExpressWayBillCode", "w.ExpressName", "w.GetDate" };
                printHistorys = new WaybillCodeService().GetWaybillCodeList(keys, fields, 1000)?.OrderByDescending(m => m.ID).ToList() ?? new List<WaybillCode>();
                printHistorys.ForEach(p =>
                {
                    var key = p.OrderId + p.ShopId;
                    if (!printDic.ContainsKey(key))
                        printDic.Add(key, new List<WaybillCode> { p });
                    else
                        printDic[key].Add(p);
                });
            }
            #endregion

            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook);
            ICellStyle contentLongStyle = GetContentCellStyleByToLongCol(workbook);
            ICellStyle contentMergeStyle = GetContentCellStyleByToMerge(workbook);
            ICellStyle contentLongMergeStyle = GetContentCellStyleByToLongColMerge(workbook);
            IRow header = sheet.CreateRow(0);
            header.Height = 15 * 20;
            // 获取勾选的商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 除商品信息外勾选的内容
            var otherChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "productMerge").ToList();
            //// 添加省市区详细地址的扩展
            //var extChkItems = JsonExtension.ToList<CheckedItem>(JsonExtension.ToJson(setting.CheckedItems));

            List<string> headNames = setting.CheckedItems.Select(m => m.Text).ToList();
            // 商品信息单列显示，列头移除单列商品信息
            if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
            {
                headNames = setting.CheckedItems.Where(m => m.From != "product").Select(m => m.Text).ToList();
            }
            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();
                    headNames.RemoveAt(headIndex);
                    headNames.InsertRange(headIndex, addr);

                    otherChkItems.RemoveAt(headIndex);
                    otherChkItems.InsertRange(headIndex, addrLst);
                }
            }

            // 第一行填充表头信息和样式
            int index = 0;
            // 设置Excel列头内容和样式
            headNames.ForEach(name =>
            {
                //设置列宽度
                //sheet.SetColumnWidth(index, 30 * 256);
                SetColumnWidth(sheet, name, index);
                // 设置列名和样式
                header.CreateCell(index).SetCellValue(name);
                header.GetCell(index).CellStyle = headStyle;
                index++;
            });

            // 第二行开始填充Excel内容
            index = 1; // 订单总行数
            //var itemRowIndex = 1; // 实际总行数
            var shops = SiteContext.Current.AllShops;
            var orderCategorysSetting = new CommonSettingService().Get("OrderCategorySet", SiteContext.Current.CurrentShopId);
            List<OrderCategory> orderCategorys = orderCategorysSetting == null || orderCategorysSetting.Value.IsNullOrEmpty() ? new List<OrderCategory>() : JsonExtension.ToList<OrderCategory>(orderCategorysSetting.Value.ToString2());

            orders.ForEach(order =>
            {
                var dic = order.ToDictionary();
                IRow row = sheet.CreateRow(index);
                row.Height = 20 * 20;
                var isAddrMerge = true;
                var itemRowIndex = index;

                // 合并订单行内容
                List<CellRangeAddress> regionLst = new List<CellRangeAddress>();
                if (setting.Export_ProductShowStyle == "Export_ShowMutilLine_Merge")
                {
                    if (order.OrderItems.Count > 1 && productChkItems.Count > 0)
                    {
                        otherChkItems.ForEach(item =>
                        {
                            var itemIndex = headNames.IndexOf(item.Text);
                            if (itemIndex != -1)
                            {
                                // 商品信息之前的内容行合并
                                int startRowIndex = index;
                                int endRowNumIndex = index + order.OrderItems.Count - 1;

                                var productRegion = new CellRangeAddress(startRowIndex, endRowNumIndex, itemIndex, itemIndex);
                                sheet.AddMergedRegion(productRegion);
                            }
                        });
                    }
                }

                var platformOrderIds = string.Empty;
                if (isCustomerOrder)
                    platformOrderIds = dic["CustomerOrderId"].ToString2().IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : dic["CustomerOrderId"].ToString2();
                else
                {
                    var childOrderIds = dic["ChildOrderId"].ToString2();
                    platformOrderIds = childOrderIds.IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : childOrderIds.Replace(",", "\n").Replace("，", "\n");
                }

                List<WaybillCode> printHistoryLst = null;
                if (needExportPrintInfo)
                {
                    printDic.TryGetValue(order.PlatformOrderId + order.ShopId, out printHistoryLst);
                    if (printHistoryLst == null)
                        printDic.TryGetValue("C" + order.PlatformOrderId + order.ShopId, out printHistoryLst);
                    if (printHistoryLst == null && !order.MergeredOrderId.IsNullOrEmpty())
                        printDic.TryGetValue(order.MergeredOrderId + order.ShopId, out printHistoryLst);
                }

                // 商品信息所在列序号集合
                var productCellIndexLst = new List<int>();
                // 填充每一列的值
                setting.CheckedItems.ForEach(chkItem =>
                {
                    var field = chkItem.Value.Replace("Export_", "");
                    var text = chkItem.Text;
                    var from = chkItem.From;

                    // 收件人地址不合并1列显示，则移除收件人地址之后列往后移动3列
                    var cellIndex = setting.CheckedItems.IndexOf(chkItem);
                    if (!isAddrMerge)
                    {
                        cellIndex = cellIndex + 3;
                    }

                    #region 填充每列的内容
                    if (from == "shop")
                    {
                        var shop = shops.FirstOrDefault(m => m.Id == order.ShopId);
                        row.CreateCell(cellIndex).SetCellValue(shop == null ? "" : shop.NickName);
                        row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (from == "ordercategory")
                    {
                        var category = orderCategorys.FirstOrDefault(m => m.Id == order.CategoryId);
                        row.CreateCell(cellIndex).SetCellValue(category == null ? "" : category.Alias);
                        row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (from == "order")
                    {
                        // 收件人地址判断省市区地址是否合并一列显示
                        if (field == "ToAddress")
                        {
                            if (setting.Export_AddressShowStyle == "Export_Address_Merge")
                            {
                                // 省市区地址在同列显示
                                var address = order.ToProvince.ToString2() + order.ToCity.ToString2() + order.ToCounty.ToString2() + order.ToAddress.ToString2();
                                row.CreateCell(cellIndex).SetCellValue(address);
                                row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                            }
                            else
                            {
                                // 省市区地址在不同列显示
                                row.CreateCell(cellIndex).SetCellValue(order.ToProvince.ToString2());
                                row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 1).SetCellValue(order.ToCity.ToString2());
                                row.GetCell(cellIndex + 1).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 2).SetCellValue(order.ToCounty.ToString2());
                                row.GetCell(cellIndex + 2).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 3).SetCellValue(order.ToAddress.ToString2());
                                row.GetCell(cellIndex + 3).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;

                                isAddrMerge = false;
                            }
                        }
                        else if (field == "PlatformType" || field == "OrderFrom")
                        {
                            // 订单来源
                            var val = dic[field].ToString2().Trim().ToLower() == "alibaba" ? "1688" : dic[field].ToString2().Trim().ToLower();
                            val = val == "importorder" ? "导入单" : (val == "customerorder" ? "录入单" : val);
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "OrderTime")
                        {
                            // 订单日期
                            var status = dic["PlatformStatus"].ToString2().Trim().ToLower();
                            var val = status == "waitbuyerpay" || status == "confirm_goods_but_not_fund" ? dic["CreateTime"].ToString2() : dic["PayTime"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "OrderCount")
                        {
                            // 订单数
                            var count = order.SubOrders?.Count ?? 0;
                            row.CreateCell(cellIndex).SetCellValue(count);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "SellerRemark" || field == "BuyerRemark")
                        {
                            var arr = dic[field].ToString2().Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                            var str = arr == null ? "" : string.Join(";", arr);
                            row.CreateCell(cellIndex).SetCellValue(str);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "ToPhone")
                        {
                            var phone = dic["ToMobile"].ToString2().IsNullOrEmpty() ? dic["ToPhone"].ToString2() : dic["ToMobile"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(phone);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "SenderPhone")
                        {
                            var phone = dic["SenderMobile"].ToString2().IsNullOrEmpty() ? dic["SenderPhone"].ToString2() : dic["SenderMobile"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(phone);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "PlatformOrderId")
                        {
                            //var platformOrderIds = string.Empty;
                            //if (isCustomerOrder)
                            //    platformOrderIds = dic["CustomerOrderId"].ToString2().IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : dic["CustomerOrderId"].ToString2();
                            //else
                            //{
                            //    var childOrderIds = dic["ChildOrderId"].ToString2();
                            //    platformOrderIds = childOrderIds.IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : childOrderIds.Replace(",", "\n").Replace("，", "\n");
                            //}

                            row.CreateCell(cellIndex).SetCellValue(platformOrderIds);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "SenderCompany")
                        {
                            //var printHistory = printHistorys.Where(c => !c.ExpressName.IsNullOrEmpty() && c.ShopId == order.ShopId && (c.OrderId == order.PlatformOrderId || c.OrderId.ToString2().Contains(order.PlatformOrderId) || (isCustomerOrder ? c.OrderId == order.CustomerOrderId : c.OrderId == order.MergeredOrderId))).OrderByDescending(x => x.GetDate).FirstOrDefault();
                            var printHistory = printHistoryLst?.OrderByDescending(x => x.GetDate).FirstOrDefault();
                            var expressName = printHistory?.ExpressName.ToString2() ?? "";
                            row.CreateCell(cellIndex).SetCellValue(expressName);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "LastWaybillCode")
                        {
                            var printHistoryStr = string.Empty;
                            //var printHistoryLst = printHistorys.Where(c => !c.ExpressWayBillCode.IsNullOrEmpty() && c.ShopId == order.ShopId && (c.OrderId == order.PlatformOrderId || c.OrderId.ToString2().Contains(order.PlatformOrderId) || (isCustomerOrder ? c.OrderId == order.CustomerOrderId : c.OrderId == order.MergeredOrderId))); 
                            var printHistory = printHistoryLst?.OrderByDescending(c => c.GetDate).FirstOrDefault(); //最近一次打印记录
                            printHistoryLst = printHistory == null ? new List<WaybillCode>() : printHistoryLst.Where(m => m.GetDate == printHistory.GetDate).ToList();//根据打印时间判断是否是一单多包
                            // 一单多包导出多个运单号
                            if (printHistoryLst != null && printHistoryLst.Count() > 1)
                                printHistoryStr = string.Join("\n", printHistoryLst?.Select(m => m.ExpressWayBillCode).ToList() ?? new List<string>());
                            else
                                printHistoryStr = printHistory?.ExpressWayBillCode.ToString2() ?? "";

                            row.CreateCell(cellIndex).SetCellValue(printHistoryStr);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "LastSendTime")
                        {
                            var lastSendTime = dic["LastSendTime"].ToString2();
                            lastSendTime = lastSendTime.IsNullOrEmpty() ? dic["AllDeliveredTime"].ToString2() : lastSendTime;
                            if (lastSendTime.IsNullOrEmpty() && !order.ChildOrderId.IsNullOrEmpty())
                            {
                                var okeys = platformOrderIds.SplitWithString("\n").Where(m => !m.IsNullOrEmpty()).Select(id => new OrderSelectKeyModel { PlatformOrderId = id, ShopId = order.ShopId }).ToList();
                                var childOrders = new OrderService().GetOrders(okeys, fields: new List<string> { "o.Id", "o.PlatformOrderId", "o.ShopId", "o.LastSendTime", "o.AllDeliveredTime", "oi.Id" });
                                var newLastTime = childOrders?.Max(o => o.LastSendTime) ?? childOrders?.Max(o => o.AllDeliveredTime);
                                if (newLastTime != null)
                                    lastSendTime = newLastTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                            }
                            lastSendTime = lastSendTime.IsNullOrEmpty() ? lastSendTime : lastSendTime.toDateTime().ToString("yyyy-MM-dd HH:mm:ss");
                            row.CreateCell(cellIndex).SetCellValue(lastSendTime);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else
                        {
                            row.CreateCell(cellIndex).SetCellValue(dic[field].ToString2());
                            row.GetCell(cellIndex).CellStyle = field == "SenderAddress" ? (row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle) : (row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle);
                        }
                    }
                    else if (from == "productMerge")
                    {
                        // 商品相关信息所在列的序号
                        if (!productCellIndexLst.Contains(cellIndex))
                            productCellIndexLst.Add(cellIndex);

                        if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
                        {
                            #region 商品信息合并1行1列显示
                            var productContent = new StringBuilder();
                            // 获取商品信息
                            order.OrderItems.ForEach(item =>
                            {
                                var itemDic = item.ToDictionary();
                                var productSubContent = new StringBuilder();
                                // 商品信息合并1列显示
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = field2 == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field2].ToString2();
                                    productSubContent.Append(field2 == "Count" ? $"*{val}," : $"{val},");
                                });

                                productContent.Append($"{productSubContent.ToString2().TrimEnd(",")};\n");
                            });

                            row.CreateCell(cellIndex).SetCellValue(productContent.ToString2());
                            row.GetCell(cellIndex).CellStyle = contentLongStyle;
                            #endregion
                        }
                        else
                        {
                            #region 商品多行列合并显示
                            var isFirstItemRowIndex = true;
                            itemRowIndex = index;
                            // 获取商品信息
                            order.OrderItems.ForEach(item =>
                            {
                                var itemDic = item.ToDictionary();
                                var productContent = new StringBuilder();
                                // 商品信息合并1列显示
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = field2 == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field2].ToString2();
                                    productContent.Append(field2 == "Count" ? $"*{val}," : $"{val},");
                                });

                                if (isFirstItemRowIndex)
                                {
                                    row.CreateCell(cellIndex).SetCellValue(productContent.ToString2().TrimEnd(","));
                                    row.GetCell(cellIndex).CellStyle = contentLongStyle;
                                    isFirstItemRowIndex = false;
                                }
                                else
                                {
                                    // 创建新行填充商品信息
                                    IRow row2 = sheet.GetRow(itemRowIndex) ?? sheet.CreateRow(itemRowIndex);
                                    row2.Height = 25 * 20;
                                    // 商品单独行显示，无订单项数据
                                    row2.CreateCell(cellIndex).SetCellValue(productContent.ToString2().TrimEnd(","));
                                    row2.GetCell(cellIndex).CellStyle = contentLongStyle;
                                }

                                itemRowIndex++;
                            });
                            #endregion
                        }
                    }
                    else if (from == "product" && setting.Export_ProductShowStyle != "Export_ShowOneLine")
                    {
                        // 商品相关信息所在列的序号
                        if (!productCellIndexLst.Contains(cellIndex))
                            productCellIndexLst.Add(cellIndex);

                        #region 商品属性多列显示
                        var isFirstItemRowIndex = true;
                        itemRowIndex = index;
                        // 获取商品信息
                        order.OrderItems.ForEach(item =>
                        {
                            var itemDic = item.ToDictionary();
                            var val = field == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field].ToString2();
                            if (isFirstItemRowIndex)
                            {
                                row.CreateCell(cellIndex).SetCellValue(val);
                                row.GetCell(cellIndex).CellStyle = field == "ProductSubject" ? contentLongStyle : contentStyle;
                                isFirstItemRowIndex = false;
                            }
                            else
                            {
                                // 创建新行填充商品信息
                                IRow row2 = sheet.GetRow(itemRowIndex) ?? sheet.CreateRow(itemRowIndex);
                                row2.Height = 25 * 20;
                                row2.CreateCell(cellIndex).SetCellValue(val);
                                row2.GetCell(cellIndex).CellStyle = field == "ProductSubject" ? contentLongStyle : contentStyle;
                            }
                            itemRowIndex++;
                        });
                        #endregion
                    }
                    #endregion
                });

                // 商品多行显示，填充订单数据
                int itemCount = order.OrderItems.Count;
                if (setting.Export_ProductShowStyle == "Export_Export_Split" && itemCount > 1)
                {
                    var tmpRowIndex = index;
                    IRow firstRow = sheet.GetRow(tmpRowIndex);
                    if (firstRow != null)
                    {
                        var firstCells = firstRow.Cells;
                        for (var i = 1; i < itemCount; i++)
                        {
                            // 商品单独行显示，并填充订单项数据
                            IRow nextRow = sheet.GetRow(tmpRowIndex + i);
                            if (nextRow == null) break;
                            for (var j = 0; j < firstCells.Count; j++)
                            {
                                if (!productCellIndexLst.Contains(j))
                                {
                                    var cell = firstCells[j];
                                    if (cell == null) continue;
                                    nextRow.CreateCell(j).SetCellValue(cell.ToString2());
                                    nextRow.GetCell(j).CellStyle = cell.CellStyle;
                                }
                            }
                        }
                    }
                }

                if (itemRowIndex > index)
                    index = itemRowIndex;
                else
                    index++;

                ExcelHelper.AutoSizeRowHeight(workbook, sheet, row);
            });

            return workbook;
        }

        private static ICellStyle GetHeadStyle(IWorkbook workbook)
        {
            IFont font = workbook.CreateFont();
            font.FontName = "Times New Roman";
            font.Boldweight = short.MaxValue;
            font.FontHeightInPoints = 11;

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(font);
            headerStyle.Alignment = HorizontalAlignment.Center;//内容居中显示
            headerStyle.WrapText = true;

            headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightOrange.Index;
            headerStyle.FillPattern = FillPattern.SolidForeground;
            return headerStyle;
        }

        private static ICellStyle GetContentStyle(IWorkbook excel, HorizontalAlignment alignment = HorizontalAlignment.Center)
        {
            IFont font = excel.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = excel.CreateCellStyle();
            contentStyle.SetFont(font);
            contentStyle.Alignment = HorizontalAlignment.Center;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.WrapText = true;

            return contentStyle;
        }

        private static ICellStyle GetContentCellStyleByToLongCol(IWorkbook excel)
        {
            ICellStyle contentStyle = GetContentStyle(excel);
            contentStyle.Alignment = HorizontalAlignment.Left;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            return contentStyle;
        }

        private static void SetColumnWidth(ISheet sheet, string headName, int index)
        {
            int width = 20 * 256;

            if (headName == "商品数量" || headName == "订单金额" || headName == "运费" || headName == "重量" || headName == "商品单价" || headName == "单价"
                || headName == "订单数" || headName == "件数" || headName == "款数" || headName == "金额" || headName == "运费" || headName == "重量(克)" || headName == "数量")
                width = 10 * 256;
            else if (headName == "收件人" || headName == "收件人电话" || headName == "联系电话" || headName == "发件人" || headName == "发件人电话" || headName == "收件省"
                || headName == "收件市" || headName == "收件区")
                width = 15 * 256;
            else if (headName == "订单来源" || headName == "订单分类" || headName == "店铺"
                 || headName == "下单时间" || headName == "付款时间" || headName == "发货时间" || headName == "打印快递单时间" || headName == "打印发货单时间" || headName == "打印拿货标签时间"
                 || headName == "打印快递单次数" || headName == "打印发货单次数" || headName == "打印拿货标签次数"
                 || headName == "日期")
                width = 20 * 256;
            else if (headName == "买家旺旺" || headName == "买家昵称" || headName == "订单编号" || headName == "流水号" || headName == "单品货号" || headName == "商品规格" || headName == "商品销售属性" || headName == "销售属性" || headName == "商品货号" || headName == "快递单号" || headName == "商品简称" || headName == "规格简称")
                width = 25 * 256;
            else if (headName == "快递公司" || headName == "收件详细地址")
                width = 30 * 256;
            else if (headName == "收件人地址" || headName == "收件地址" || headName == "发件人地址" || headName == "发件地址" || headName == "商品标题" || headName == "商品名称" || headName == "商品信息" || headName == "买家留言" || headName == "卖家备注" || headName == "打印内容")
                width = 35 * 256;

            sheet.SetColumnWidth(index, width);
        }

        private static ICellStyle GetContentCellStyleByToMerge(IWorkbook excel)
        {
            ICellStyle contentStyle = GetContentStyle(excel);
            contentStyle.Alignment = HorizontalAlignment.Center;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            return contentStyle;
        }

        private static ICellStyle GetContentCellStyleByToLongColMerge(IWorkbook excel)
        {
            ICellStyle contentStyle = GetContentStyle(excel);
            contentStyle.Alignment = HorizontalAlignment.Left;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            return contentStyle;
        }
        #endregion

        #region 底单导出
        private static WaybillCode EncryptReceiverInfo(WaybillCode order)
        {
            order.BuyerMemberName = order.BuyerMemberName.ToEncryptName();
            order.BuyerMemberId = order.BuyerMemberId.ToEncryptName();
            order.ReciverPhone = order.ReciverPhone.ToPddEncryptPhone();
            order.Reciver = order.Reciver.ToEncryptName();
            order.ToAddress = "****";
            order.ToDistrict = "****";
            return order;
        }

        private static WaybillCode EncryptSenderInfo(WaybillCode order)
        {
            order.Sender = order.Sender.ToEncryptName();
            return order;
        }

        public static IWorkbook BuildWaybillCodeExcel(List<WaybillCode> waybillCodeLst, List<WaybillCodeCheckModel> checkedItems, string fileName)
        {
            var shopIds = waybillCodeLst.Select(x => x.ShopId).Distinct().ToList();
            var shops = new ShopRepository().GetShopByIds(shopIds);

            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (waybillCodeLst != null && waybillCodeLst.Any())
            {
                if (pt == PlatformType.Taobao.ToString() || pt == PlatformType.Jingdong.ToString())
                {
                    waybillCodeLst.ForEach(o =>
                    {
                        EncryptReceiverInfo(o);
                    });
                }
                else if (pt == PlatformType.Pinduoduo.ToString())
                {
                    var tempOrders = waybillCodeLst?.Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ToAddress }).ToList();
                    try
                    {
                        BranchShareRelationService.TryToDecryptPddOrders(tempOrders, true);
                    }
                    catch (Exception)
                    {
                    }
                    //按店铺分组
                    foreach (var item in waybillCodeLst)
                    {
                        var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId && x.ShopId == item.ShopId);
                        if (decryptedOrder != null)
                        {
                            item.Reciver = decryptedOrder.ToName.ToEncryptName();
                            item.ReciverPhone = decryptedOrder.ToMobile.ToPddEncryptPhone();
                            item.BuyerMemberName = item.Reciver.ToEncryptName();
                            item.BuyerMemberId = item.Reciver.ToEncryptName();
                            item.ToAddress = decryptedOrder.ToFullAddress.ToPddEncryptAddress();
                        }
                    }
                }
            }

            //淘宝发件人也需要打码
            if (pt == PlatformType.Taobao.ToString())
                waybillCodeLst.ForEach(o =>
                {
                    EncryptSenderInfo(o);
                });

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet();
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            checkedItems.ForEach(model =>
            {
                var headName = model.Text.ToString2();
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;
            waybillCodeLst.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;
                var dic = (model ?? new WaybillCode()).ToDictionary();

                colIndex = 0;
                foreach (var item in checkedItems)
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item?.Value.ToString2() ?? "";
                    if (dic.ContainsKey(key) || key == "RowIndex" || key == "ShopName")
                    {
                        var val = key == "RowIndex" ? rowIndex.ToString2() : key == "ShopName" ? "" : dic[key].ToString2();
                        if (key != "RowIndex")
                        {
                            if (key == "ToAddress")
                                val = $"{dic["ToProvince"].ToString2()} {dic["ToCity"].ToString2()} {dic["ToDistrict"].ToString2()} {dic["ToAddress"].ToString2()}";
                            else if (key == "BuyerRemark" || key == "SellerRemark")
                            {
                                var arr = val.Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                                val = arr.Length == 0 ? "" : val;
                            }
                            else if (key == "Status")
                            {
                                if (val == "1")
                                    val = "已打印";
                                else if (val == "2")
                                    val = "已回收";
                                else if (val == "3")
                                    val = "已发货";
                                else
                                    val = "未知状态";
                            }
                            else if (key == "ShopName")
                            {
                                val = shops?.FirstOrDefault(m => m.Id == dic["ShopId"].ToInt())?.ShopName ?? "";
                            }
                            else if (key == "OrderId")
                            {
                                var customerOrderId = dic["CustomerOrderId"].ToString2();
                                if (!customerOrderId.IsNullOrEmpty())
                                    val = customerOrderId;
                                else
                                {
                                    var orderIdJoin = dic["OrderIdJoin"].ToString2();
                                    if (!orderIdJoin.IsNullOrEmpty())
                                        val = orderIdJoin.Replace(",", "\n");
                                }
                            }
                            else if (key == "ExpressWayBillCode")
                            {
                                var childWaybillCode = dic["ChildWaybillCode"].ToString2();
                                val = $"{val}{(childWaybillCode.IsNullOrEmpty() ? "" : $"/{childWaybillCode}")}";
                            }
                        }

                        if (key == "BuyerRemark" || key == "SellerRemark" || key == "ToAddress" || key == "SendContent")
                            tmpStyle = leftContentStyle;

                        dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                        dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                        colIndex++;
                    }
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;
            });

            return workbook;
        }

        #endregion

        #region 单号分享使用明细导出
        public static bool IsWdUser(int shopId)
        {
            var select_user_sql = "SELECT t2.* FROM dbo.P_UserShopRelation AS t1 WITH(NOLOCK) INNER JOIN dbo.P_User AS t2 WITH(NOLOCK) ON t1.UserId =t2.Id WHERE t1.ShopId=@sid";
            var users = DbUtility.GetConfigureConnection().Query<User>(select_user_sql, new { sid = shopId });
            return users?.Any(f => f.Type == 1) == true;
        }

        public static IWorkbook BuildBranchShareExcel(List<BranchShareRelation> models, string fileName, List<WaybillCodeSimpleModel> details, bool isFromShop, bool isWdUser)
        {
            //var isFromShop = models.FirstOrDefault().FromId == shopId;
            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            var sheetIndex = 0;
            ISheet sheet = workbook.CreateSheet("分享单号使用情况");
            var maxRowCount = fileName.IndexOf(".xlsx") > 0 ? 1048500 : 65500;

            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook);
            ICellStyle contentLongStyle = GetContentCellStyleByToLongCol(workbook);

            var headNames = new List<string> { "获取时间", "运单号", "平台", "订单编号", "收件人", "省市区", "使用状态" };

            //var isWdUser = IsWdUser(task.ShopId);
            if (isWdUser)
            {
                headNames = new List<string> { "获取时间", "运单号", "平台", "订单编号", "收件人", "收件人电话", "省市区", "发件人", "发件人电话", "发件人地址", "使用状态" };
            }

            // 第二行填充表头信息和样式    
            // 表头
            IRow header = sheet.CreateRow(0);
            header.Height = 15 * 20;
            int cellIndex = 0;
            if (isFromShop)
                headNames.InsertRange(0, new List<string> { "使用单号店铺", "备注" });
            // 设置Excel列头内容和样式
            headNames.ForEach(name =>
            {
                //设置列宽度
                sheet.SetColumnWidth(cellIndex, 20 * 256);
                // 设置列名和样式
                header.CreateCell(cellIndex).SetCellValue(name);
                header.GetCell(cellIndex).CellStyle = headStyle;
                cellIndex++;
            });

            var rowIndex = 1;  // 第2行开始填充Excel内容
            var shareIds = models.Select(m => m.Id).Distinct().ToList();
            TemplateRelationAuthInfoService _templateAuthInfoService = new TemplateRelationAuthInfoService();
            // 根据ShareId获取使用的模板 信息
            var printTemplates = _templateAuthInfoService.GetByShareRelationIds(shareIds);
            var shareTempIds = printTemplates.Where(m => m.TemplateId > 0).Select(m => m.TemplateId).Distinct().ToList();
            models.GroupBy(m => m.Id).ToList().ForEach(g =>
            {
                var model = g.FirstOrDefault();
                var tmpIds = printTemplates.Where(m => m.BranchShareRelationId == model.Id && m.TemplateId > 0).Select(m => m.TemplateId).Distinct().ToList();
                //details?.Where(m => tmpIds.Contains(m.TemplateId)).ToList().ForEach(detail =>
                tmpIds.ForEach(tid =>
                {
                    var tmpDetails = details.Where(m => m.TemplateId == tid).OrderByDescending(m => m.CreateTime).ToList();
                    tmpDetails.ForEach(detail =>
                    {
                        if (sheet.LastRowNum >= maxRowCount)
                        {
                            sheetIndex++;
                            sheet = workbook.CreateSheet("分享单号使用情况" + sheetIndex);
                            rowIndex = 1;

                            header = sheet.CreateRow(0);
                            header.Height = 15 * 20;
                            cellIndex = 0;
                            // 设置Excel列头内容和样式
                            headNames.ForEach(name =>
                            {
                                //设置列宽度
                                sheet.SetColumnWidth(cellIndex, 20 * 256);
                                // 设置列名和样式
                                header.CreateCell(cellIndex).SetCellValue(name);
                                header.GetCell(cellIndex).CellStyle = headStyle;
                                cellIndex++;
                            });
                        }
                        IRow row = sheet.CreateRow(rowIndex);
                        row.Height = 20 * 20;

                        if (isFromShop)
                        {
                            if (isWdUser)
                            {
                                //"使用单号店铺", "备注","获取时间", "运单号", "平台", "订单编号", "收件人", "收件人电话", "省市区", "发件人", "发件人电话", "发件人地址", "使用状态"
                                row.CreateCell(0).SetCellValue(model.ToShopName);
                                row.CreateCell(1).SetCellValue(model.Remark);
                                row.CreateCell(2).SetCellValue(detail.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                row.CreateCell(3).SetCellValue(detail.ExpressWayBillCode.ToString2());
                                row.CreateCell(4).SetCellValue(detail.PlatformTypeName.ToString2());
                                row.CreateCell(5).SetCellValue(detail.OrderId.ToString2());
                                row.CreateCell(6).SetCellValue(detail.Reciver.ToString2());
                                row.CreateCell(7).SetCellValue(detail.ReciverPhone.ToString2());
                                row.CreateCell(8).SetCellValue(detail.ToProvince.ToString2() + detail.ToCity.ToString2() + detail.ToDistrict.ToString2());
                                row.CreateCell(9).SetCellValue(detail.Sender.ToString2());
                                row.CreateCell(10).SetCellValue(detail.SenderPhone.ToString2());
                                row.CreateCell(11).SetCellValue(detail.SenderAddress.ToString2());
                                row.CreateCell(12).SetCellValue(detail.Status.ToString2() == "1" ? "已打印" : detail.Status.ToString2() == "2" ? "已回收" : "已发货");
                            }
                            else
                            {
                                //"使用单号店铺", "备注","获取时间", "运单号", "平台", "订单编号", "收件人", "省市区", "使用状态"
                                row.CreateCell(0).SetCellValue(model.ToShopName);
                                row.CreateCell(1).SetCellValue(model.Remark);
                                row.CreateCell(2).SetCellValue(detail.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                row.CreateCell(3).SetCellValue(detail.ExpressWayBillCode.ToString2());
                                row.CreateCell(4).SetCellValue(detail.PlatformTypeName.ToString2());
                                row.CreateCell(5).SetCellValue(detail.OrderId.ToString2());
                                row.CreateCell(6).SetCellValue(detail.Reciver.ToString2());
                                row.CreateCell(7).SetCellValue(detail.ToProvince.ToString2() + detail.ToCity.ToString2() + detail.ToDistrict.ToString2());
                                row.CreateCell(8).SetCellValue(detail.Status.ToString2() == "1" ? "已打印" : detail.Status.ToString2() == "2" ? "已回收" : "已发货");
                            }
                        }
                        else
                        {
                            if (isWdUser)
                            {
                                //"获取时间", "运单号", "平台", "订单编号", "收件人", "收件人电话", "省市区", "发件人", "发件人电话", "发件人地址", "使用状态"
                                row.CreateCell(0).SetCellValue(detail.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                row.CreateCell(1).SetCellValue(detail.ExpressWayBillCode.ToString2());
                                row.CreateCell(2).SetCellValue(detail.PlatformTypeName.ToString2());
                                row.CreateCell(3).SetCellValue(detail.OrderId.ToString2());
                                row.CreateCell(4).SetCellValue(detail.Reciver.ToString2());
                                row.CreateCell(5).SetCellValue(detail.ReciverPhone.ToString2());
                                row.CreateCell(6).SetCellValue(detail.ToProvince.ToString2() + detail.ToCity.ToString2() + detail.ToDistrict.ToString2());
                                row.CreateCell(7).SetCellValue(detail.Sender.ToString2());
                                row.CreateCell(8).SetCellValue(detail.SenderPhone.ToString2());
                                row.CreateCell(9).SetCellValue(detail.SenderAddress.ToString2());
                                row.CreateCell(10).SetCellValue(detail.Status.ToString2() == "1" ? "已打印" : detail.Status.ToString2() == "2" ? "已回收" : "已发货");
                            }
                            else
                            {
                                //"获取时间", "运单号", "平台", "订单编号", "收件人", "省市区", "使用状态"
                                row.CreateCell(0).SetCellValue(detail.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                row.CreateCell(1).SetCellValue(detail.ExpressWayBillCode.ToString2());
                                row.CreateCell(2).SetCellValue(detail.PlatformTypeName.ToString2());
                                row.CreateCell(3).SetCellValue(detail.OrderId.ToString2());
                                row.CreateCell(4).SetCellValue(detail.Reciver.ToString2());
                                row.CreateCell(5).SetCellValue(detail.ToProvince.ToString2() + detail.ToCity.ToString2() + detail.ToDistrict.ToString2());
                                row.CreateCell(6).SetCellValue(detail.Status.ToString2() == "1" ? "已打印" : detail.Status.ToString2() == "2" ? "已回收" : "已发货");
                            }
                        }

                        row.Cells.ForEach(cell =>
                        {
                            cell.CellStyle = contentStyle;
                        });
                        rowIndex++;
                    });
                });
            });
            return workbook;
        }

        #endregion

    }
}
