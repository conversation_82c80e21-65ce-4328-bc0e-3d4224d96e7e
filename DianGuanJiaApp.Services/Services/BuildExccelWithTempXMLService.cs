using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web.UI.WebControls;
using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using NPOI.HSSF.UserModel;
using NPOI.POIFS.FileSystem;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;

namespace DianGuanJiaApp.Services
{
    public partial class BuildExccelService
    {
        #region 底单Excel导出
        public static void WaybillCodeToDataTable(DataTable dt, List<WaybillCode> waybillCodeLst, List<WaybillCodeCheckModel> checkedItems, int lastRowNum)
        {
            var shopIds = waybillCodeLst.Select(x => x.ShopId).Distinct().ToList();
            var shops = new ShopRepository().GetShopByIds(shopIds);
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;

            #region 加密解密收件人信息
            FxPlatformEncryptService.EncryptWaybillCodes(waybillCodeLst, encryptSender: true);

            #region 注释
            //try
            //{
            //    if (pt == PlatformType.Taobao.ToString() || pt == PlatformType.Jingdong.ToString())
            //    {
            //        #region 淘宝京东加密解密收件人信息
            //        waybillCodeLst.ForEach(o =>
            //        {
            //            EncryptReceiverInfo(o);
            //        });
            //        //淘宝发件人也需要打码
            //        if (pt == PlatformType.Taobao.ToString())
            //        {
            //            waybillCodeLst.ForEach(o =>
            //            {
            //                EncryptSenderInfo(o);
            //            });
            //        }
            //        #endregion
            //    }
            //    else if (pt == PlatformType.Pinduoduo.ToString())
            //    {
            //        #region 拼多多加密解密收件人信息
            //        var tempOrders = waybillCodeLst?.Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ToAddress }).ToList();
            //        try
            //        {
            //            BranchShareRelationService.TryToDecryptPddOrders(tempOrders, true);
            //        }
            //        catch (Exception)
            //        {
            //        }
            //        //按店铺分组
            //        foreach (var item in waybillCodeLst)
            //        {
            //            var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId && x.ShopId == item.ShopId);
            //            if (decryptedOrder != null)
            //            {
            //                item.Reciver = decryptedOrder.ToName.ToEncryptName();
            //                item.ReciverPhone = decryptedOrder.ToMobile.ToPddEncryptPhone();
            //                item.BuyerMemberName = item.Reciver.ToEncryptName();
            //                item.BuyerMemberId = item.Reciver.ToEncryptName();
            //                item.ToAddress = decryptedOrder.ToFullAddress.ToPddEncryptAddress();
            //            }
            //        } 
            //        #endregion
            //    }
            //}
            //catch (Exception)
            //{
            //} 
            #endregion
            #endregion

            var rowIndex = lastRowNum;
            waybillCodeLst.ForEach(model =>
            {
                rowIndex++;
                var row = dt.NewRow();
                var dic = (model ?? new WaybillCode()).ToDictionary();
                foreach (var item in checkedItems)
                {
                    var key = item?.Value.ToString2() ?? "";
                    if (!dic.ContainsKey(key) && key != "RowIndex" && key != "ShopName")
                        continue;

                    var val = key == "RowIndex" ? rowIndex.ToString2() : key == "ShopName" ? "" : dic[key].ToString2();
                    if (key != "RowIndex")
                    {
                        if (key == "ToAddress")
                            val = $"{dic["ToProvince"].ToString2()} {dic["ToCity"].ToString2()} {dic["ToDistrict"].ToString2()} {dic["ToAddress"].ToString2()}";
                        else if (key == "BuyerRemark" || key == "SellerRemark")
                        {
                            var arr = val.Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                            val = arr.Length == 0 ? "" : val;
                        }
                        else if (key == "Status")
                        {
                            if (val == "1")
                                val = "已打印";
                            else if (val == "2")
                                val = "已回收";
                            else if (val == "3")
                                val = "已发货";
                            else if (val == "4")
                                val = "回收失败";
                            else
                                val = "未知状态";
                        }
                        //发货类型：0=正常；1=补发；2=换货；3=变更单号
                        else if (key == "SendType")
                        {
                            if (val == "1")
                                val = "补发";
                            else if (val == "2")
                                val = "换货";
                            else if (val == "3")
                                val = "变更单号";
                            else
                                val = "正常";
                        }
                        else if (key == "ShopName")
                        {
                            val = shops?.FirstOrDefault(m => m.Id == dic["ShopId"].ToInt())?.ShopName ?? "";
                        }
                        else if (key == "OrderId")
                        {
                            var customerOrderId = dic["CustomerOrderId"].ToString2();
                            if (!customerOrderId.IsNullOrEmpty())
                                val = customerOrderId;
                            else
                            {
                                var orderIdJoin = dic["OrderIdJoin"].ToString2();
                                if (!orderIdJoin.IsNullOrEmpty())
                                    val = orderIdJoin.Replace(",", "\n");
                            }
                        }
                        else if (key == "ExpressWayBillCode")
                        {
                            var childWaybillCode = dic["ChildWaybillCode"].ToString2();
                            val = $"{val}{(childWaybillCode.IsNullOrEmpty() ? "" : $"/{childWaybillCode}")}";
                        }
                    }

                    row[item.Text] = val;
                }
                dt.Rows.Add(row);
            });
        }

        public static List<XmlColumn> GetWaybillCodeColumns(List<string> headlist)
        {
            var colunms = new List<XmlColumn>();
            headlist.ForEach(h =>
            {
                var col = new XmlColumn();
                col.Width = GetOrderColumnWidth(h);
                col.ColName = h;
                col.TitleStyle = CustomCellStyle.TitleCellStyle;
                col.RowStyle = CustomCellStyle.RowCellStyle;

                colunms.Add(col);
            });
            return colunms;
        }
        #endregion

        #region 订单打印（自由打印订单）Excel导出
        public static void OrderToDataTable(DataPackage data, int toalCount, List<Order> orders, ExportSetting setting, List<OrderCategory> categories, bool isCustomerOrder)
        {
            var dt = data.Model.Table;
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;

            #region 收件人相关信息加密解密
            if (!isCustomerOrder)
            {
                if (pt == PlatformType.Pinduoduo.ToString())
                {
                    #region 拼多多收件人相关信息加密解密
                    orders.GroupBy(x => x.ShopId).ToList().ForEach(g =>
                    {
                        var temp = SiteContext.Current.AllShops.FirstOrDefault(y => y.Id == g.Key);
                        if (temp != null)
                        {
                            var service = new PinduoduoPlatformService(temp);
                            if (service.Ping())
                            {
                                //解密
                                try
                                {
                                    service.DecryptBatch(g.ToList(), true);
                                }
                                catch (Exception)
                                {
                                }
                                //自定义加密
                                g.ToList().ForEach(t =>
                                {
                                    t.ToMobile = t.ToMobile.ToEncrytPhone();
                                    t.ToPhone = t.ToPhone.ToEncrytPhone();
                                    t.BuyerMemberName = t.BuyerMemberName.ToEncryptName();
                                    t.BuyerWangWang = t.BuyerWangWang.ToEncryptName();
                                    t.ToName = t.ToName.ToEncryptName();
                                    t.ToAddress = t.ToAddress.ToPddEncryptAddress();
                                    t.ToFullAddress = t.ToProvince + t.ToCity + t.ToCounty + t.ToAddress.ToPddEncryptAddress();
                                });
                            }
                            else
                            {
                                //授权过期处理
                                g.ToList().ForEach(t =>
                                {
                                    t.ToMobile = "****";
                                    t.ToPhone = "****";
                                    t.BuyerMemberName = "***";
                                    t.BuyerWangWang = "***";
                                    t.ToName = "***";
                                    t.ToAddress = t.ToAddress.ToPddEncryptAddress();
                                    t.ToFullAddress = t.ToProvince + t.ToCity + t.ToCounty + t.ToAddress.ToPddEncryptAddress();
                                });
                            }
                        }
                    });
                    #endregion
                }
                else if (pt == PlatformType.Taobao.ToString() || pt == PlatformType.Jingdong.ToString())
                {
                    #region 淘宝京东收件人相关信息加密解密
                    orders.ForEach(o =>
                    {
                        EncryptReceiverInfo(o);
                    });
                    #endregion
                }
            }
            #endregion

            // 设置发件人
            new OrderService().SetSellerInfo(orders);

            #region 查询底单打印记录
            List<OrderSelectKeyModel> keys = orders.Select(m => new OrderSelectKeyModel()
            {
                Id = m.Id,
                ShopId = m.ShopId,
                PlatformOrderId = m.PlatformOrderId,
                ChildPlatformOrderIds = m.ChildOrderId?.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).ToList()
            }).ToList();

            var needExportPrintInfo = false;
            var printDic = new Dictionary<string, List<WaybillCode>>();
            var printHistorys = new List<WaybillCode>();
            if (setting.CheckedItems.Any(m => m.Value.ToString2() == "Export_LastWaybillCode" || m.Value.ToString2() == "SenderCompany"))
            {
                needExportPrintInfo = true;
                var fields = new List<string> { "w.ShopId", "w.OrderId", "w.ExpressWayBillCode", "w.ExpressName", "w.GetDate" };
                printHistorys = new WaybillCodeService().GetWaybillCodeList(keys, fields, 1000)?.OrderByDescending(m => m.ID).ToList() ?? new List<WaybillCode>();
                printHistorys.ForEach(p =>
                {
                    var key = p.OrderId + p.ShopId;
                    if (!printDic.ContainsKey(key))
                        printDic.Add(key, new List<WaybillCode> { p });
                    else
                        printDic[key].Add(p);
                });
            }
            #endregion

            var shopIds = orders.Select(x => x.ShopId).Distinct().ToList();
            var shops = new ShopRepository().GetShopByIds(shopIds);

            //商品信息
            var productMergeChkItem = setting.CheckedItems.FirstOrDefault(m => m.From == "productMerge");
            // 获取勾选的商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 除商品信息外勾选的内容
            var otherChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "productMerge").ToList();
            //// 添加省市区详细地址的扩展
            //var extChkItems = JsonExtension.ToList<CheckedItem>(JsonExtension.ToJson(setting.CheckedItems));
            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();
                    //headNames.RemoveAt(headIndex);
                    //headNames.InsertRange(headIndex, addr);

                    otherChkItems.RemoveAt(headIndex);
                    otherChkItems.InsertRange(headIndex, addrLst);
                }
            }
            //获取列表头起始位置
            var headDic = new Dictionary<string, int>();
            for (int i = 0; i < otherChkItems.Count; i++)
            {
                if (!headDic.ContainsKey(otherChkItems[i].Text))
                    headDic.Add(otherChkItems[i].Text, i);
            }

            #region 组装DataTable

            orders.ForEach(order =>
            {
                var dic = order.ToDictionary();
                var row = dt.NewRow();
                dt.Rows.Add(row);

                var platformOrderIds = string.Empty;
                if (isCustomerOrder)
                    platformOrderIds = dic["CustomerOrderId"].ToString2().IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : dic["CustomerOrderId"].ToString2();
                else
                {
                    var childOrderIds = dic["ChildOrderId"].ToString2();
                    platformOrderIds = childOrderIds.IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : childOrderIds.Replace(",", "\n").Replace("，", "\n");
                }

                List<WaybillCode> printHistoryLst = null;
                if (needExportPrintInfo)
                {
                    printDic.TryGetValue(order.PlatformOrderId + order.ShopId, out printHistoryLst);
                    if (printHistoryLst == null)
                        printDic.TryGetValue("C" + order.PlatformOrderId + order.ShopId, out printHistoryLst);
                    if (printHistoryLst == null && !order.MergeredOrderId.IsNullOrEmpty())
                        printDic.TryGetValue(order.MergeredOrderId + order.ShopId, out printHistoryLst);
                }

                // 商品信息所在列序号集合
                //var productCellIndexLst = new List<int>();
                var oiCount = order.OrderItems.Count;
                // 填充每一列的值
                otherChkItems.ForEach(chkItem =>
                {
                    var field = chkItem.Value.Replace("Export_", "");
                    var text = chkItem.Text;
                    var from = chkItem.From;

                    #region 商品信息显示在多行(合并行)，即订单信息多行合并，商品信息多行显示 记录合并坐标
                    if (setting.Export_ProductShowStyle == "Export_ShowMutilLine_Merge" && oiCount > 1 && headDic.ContainsKey(text))
                    {
                        var beginCol = headDic[text];
                        var beginRowIndex = toalCount + dt.Rows.Count + 1;//表头占1行，从2行开始
                        var endCol = beginCol;
                        var endRowIndex = beginRowIndex + oiCount - 1;
                        data.Model.MergeInfos.Add(new MergeCellRangeAddress { BeginCol = beginCol, BeginRowIndex = beginRowIndex, EndCol = endCol, EndRowIndex = endRowIndex });
                    }
                    #endregion

                    #region 填充每列的内容
                    if (from == "shop")
                    {
                        var shop = shops.FirstOrDefault(m => m.Id == order.ShopId);
                        row[text] = shop == null ? "" : shop.NickName;
                    }
                    else if (from == "ordercategory")
                    {
                        var category = categories.FirstOrDefault(m => m.Id == order.CategoryId);
                        row[text] = category == null ? "" : category.Alias;
                    }
                    else if (from == "order")
                    {
                        // 收件人地址判断省市区地址是否合并一列显示
                        if (field == "ToAddress")
                        {
                            if (setting.Export_AddressShowStyle == "Export_Address_Merge")
                            {
                                // 省市区地址在同列显示
                                var address = order.ToProvince.ToString2() + order.ToCity.ToString2() + order.ToCounty.ToString2() + order.ToAddress.ToString2();
                                row[text] = address;
                            }
                            else
                            {
                                // 省市区地址在不同列显示
                                row["收件省"] = order.ToProvince.ToString2();
                                row["收件市"] = order.ToCity.ToString2();
                                row["收件区"] = order.ToCounty.ToString2();
                                row["收件详细地址"] = order.ToAddress.ToString2();
                            }
                        }
                        else if (field == "PlatformType" || field == "OrderFrom")
                        {
                            // 订单来源
                            var val = dic[field].ToString2().Trim().ToLower() == "alibaba" ? "1688" : dic[field].ToString2().Trim().ToLower();
                            val = val == "importorder" ? "导入单" : (val == "customerorder" ? "录入单" : val);
                            row[text] = val;
                        }
                        else if (field == "OrderTime")
                        {
                            // 订单日期
                            var status = dic["PlatformStatus"].ToString2().Trim().ToLower();
                            var val = status == "waitbuyerpay" || status == "confirm_goods_but_not_fund" ? dic["CreateTime"].ToString2() : dic["PayTime"].ToString2();
                            row[text] = val;
                        }
                        else if (field == "OrderCount")
                        {
                            // 订单数
                            var count = order.SubOrders?.Count ?? 0;
                            row[text] = count;
                        }
                        else if (field == "SellerRemark" || field == "BuyerRemark")
                        {
                            var arr = dic[field].ToString2().Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                            var str = arr == null ? "" : string.Join(";", arr);
                            row[text] = str;
                        }
                        else if (field == "ToPhone")
                        {
                            var phone = dic["ToMobile"].ToString2().IsNullOrEmpty() ? dic["ToPhone"].ToString2() : dic["ToMobile"].ToString2();
                            row[text] = phone;
                        }
                        else if (field == "SenderPhone")
                        {
                            var phone = dic["SenderMobile"].ToString2().IsNullOrEmpty() ? dic["SenderPhone"].ToString2() : dic["SenderMobile"].ToString2();
                            row[text] = phone;
                        }
                        else if (field == "PlatformOrderId")
                        {
                            row[text] = platformOrderIds;
                        }
                        else if (field == "SenderCompany")
                        {
                            //var printHistory = printHistorys.Where(c => !c.ExpressName.IsNullOrEmpty() && c.ShopId == order.ShopId && (c.OrderId == order.PlatformOrderId || c.OrderId.ToString2().Contains(order.PlatformOrderId) || (isCustomerOrder ? c.OrderId == order.CustomerOrderId : c.OrderId == order.MergeredOrderId))).OrderByDescending(x => x.GetDate).FirstOrDefault();
                            var printHistory = printHistoryLst?.OrderByDescending(x => x.GetDate).FirstOrDefault();
                            var expressName = printHistory?.ExpressName.ToString2() ?? "";
                            row[text] = expressName;
                        }
                        else if (field == "LastWaybillCode")
                        {
                            var printHistoryStr = string.Empty;
                            //var printHistoryLst = printHistorys.Where(c => !c.ExpressWayBillCode.IsNullOrEmpty() && c.ShopId == order.ShopId && (c.OrderId == order.PlatformOrderId || c.OrderId.ToString2().Contains(order.PlatformOrderId) || (isCustomerOrder ? c.OrderId == order.CustomerOrderId : c.OrderId == order.MergeredOrderId))); 
                            var printHistory = printHistoryLst?.OrderByDescending(c => c.GetDate).FirstOrDefault(); //最近一次打印记录
                            printHistoryLst = printHistory == null ? new List<WaybillCode>() : printHistoryLst.Where(m => m.GetDate == printHistory.GetDate).ToList();//根据打印时间判断是否是一单多包
                            // 一单多包导出多个运单号
                            if (printHistoryLst != null && printHistoryLst.Count() > 1)
                                printHistoryStr = string.Join("\n", printHistoryLst?.Select(m => m.ExpressWayBillCode).ToList() ?? new List<string>());
                            else
                                printHistoryStr = printHistory?.ExpressWayBillCode.ToString2() ?? "";
                            row[text] = printHistoryStr;
                        }
                        else if (field == "LastSendTime")
                        {
                            var lastSendTime = dic["LastSendTime"].ToString2();
                            lastSendTime = lastSendTime.IsNullOrEmpty() ? dic["AllDeliveredTime"].ToString2() : lastSendTime;
                            if (lastSendTime.IsNullOrEmpty() && !order.ChildOrderId.IsNullOrEmpty())
                            {
                                var okeys = platformOrderIds.SplitWithString("\n").Where(m => !m.IsNullOrEmpty()).Select(id => new OrderSelectKeyModel { PlatformOrderId = id, ShopId = order.ShopId }).ToList();
                                var childOrders = new OrderService().GetOrders(okeys, fields: new List<string> { "o.Id", "o.PlatformOrderId", "o.ShopId", "o.LastSendTime", "o.AllDeliveredTime", "oi.Id" });
                                var newLastTime = childOrders?.Max(o => o.LastSendTime) ?? childOrders?.Max(o => o.AllDeliveredTime);
                                if (newLastTime != null)
                                    lastSendTime = newLastTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                            }
                            lastSendTime = lastSendTime.IsNullOrEmpty() ? lastSendTime : lastSendTime.toDateTime().ToString("yyyy-MM-dd HH:mm:ss");
                            row[text] = lastSendTime;
                        }
                        else
                        {
                            row[text] = dic[field].ToString2();
                        }
                    }
                    #endregion
                });

                //商品信息填充
                if (productChkItems.Any())
                {
                    if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
                    {
                        #region 商品信息合并1行1列显示(商品数据都在【商品信息】)
                        var productContent = new StringBuilder();
                        // 获取商品信息
                        order.OrderItems.ForEach(item =>
                        {
                            var itemDic = item.ToDictionary();
                            var productSubContent = new StringBuilder();
                            // 商品信息合并1列显示
                            productChkItems.ForEach(chkItem2 =>
                            {
                                var field2 = chkItem2.Value.Replace("Export_", "");
                                var text2 = chkItem2.Text;
                                var from2 = chkItem2.From;
                                var val = field2 == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field2].ToString2();
                                productSubContent.Append(field2 == "Count" ? $"*{val}," : $"{val},");
                            });
                            productContent.Append($"{productSubContent.ToString2().TrimEnd(",")};\n");
                        });
                        if (headDic.ContainsKey("商品信息") && dt.Columns.Contains("商品信息") && productContent.IsNotNullOrEmpty())
                            row["商品信息"] = productContent.ToString2();
                        #endregion
                    }
                    else
                    {
                        #region 商品信息显示在多行(独立行)，即订单信息为空，商品信息多行填充显示
                        var isFirstItemRowIndex = true;
                        order.OrderItems.ForEach(oi =>
                        {
                            var itemDic = oi.ToDictionary();
                            var productSubContent = new StringBuilder();
                            if (isFirstItemRowIndex)
                            {
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = field2 == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field2].ToString2();
                                    if (productMergeChkItem != null)
                                        productSubContent.Append(field2 == "Count" ? $"*{val}," : $"{val},");
                                    row[text2] = val;
                                });
                                if (headDic.ContainsKey("商品信息") && dt.Columns.Contains("商品信息") && productSubContent.IsNotNullOrEmpty())
                                    row["商品信息"] = productSubContent.ToString2();
                                isFirstItemRowIndex = false;
                            }
                            else
                            {
                                var row2 = dt.NewRow();
                                dt.Rows.Add(row2);
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = field2 == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field2].ToString2();
                                    if (productMergeChkItem != null)
                                        productSubContent.Append(field2 == "Count" ? $"*{val}," : $"{val},");
                                    row2[text2] = val;
                                });
                                if (headDic.ContainsKey("商品信息") && productSubContent.IsNotNullOrEmpty())
                                    row2["商品信息"] = productSubContent.ToString2();
                            }
                        });
                        #endregion
                    }
                }
            });
            #endregion
        }

        public static List<XmlColumn> GetOrderColumns(List<string> headlist)
        {
            var colunms = new List<XmlColumn>();
            headlist.ForEach(h =>
            {
                var col = new XmlColumn();
                col.Width = GetOrderColumnWidth(h);
                col.ColName = h;
                col.TitleStyle = CustomCellStyle.TitleCellStyle;
                SetOrderRowStyle(col);

                colunms.Add(col);
            });
            return colunms;
        }

        private static void SetOrderRowStyle(XmlColumn col)
        {
            switch (col.ColName)
            {
                case "收件人地址":
                case "买家留言":
                case "卖家备注":
                case "发件人地址":
                case "商品信息":
                case "商品标题":
                case "订单编号":
                case "主订单编号":
                case "收件人电话":
                case "快递单号":
                    col.RowStyle = CustomCellStyle.ContentLongStyle;
                    break;
                default:
                    col.RowStyle = CustomCellStyle.RowCellStyle;
                    break;
            }
        }

        private static int GetOrderColumnWidth(string headName)
        {
            int width = 20;

            if (headName == "序号" || headName == "商品数量" || headName == "订单金额" || headName == "运费" || headName == "重量" || headName == "商品单价" || headName == "单价"
                || headName == "订单数" || headName == "件数" || headName == "款数" || headName == "金额" || headName == "运费" || headName == "重量(克)" || headName == "数量")
                width = 10;
            else if (headName == "收件人" || headName == "收件人电话" || headName == "联系电话" || headName == "发件人" || headName == "发件人电话" || headName == "收件省"
                || headName == "收件市" || headName == "收件区")
                width = 15;
            else if (headName == "订单来源" || headName == "订单分类" || headName == "店铺" || headName == "发货类型" || headName == "订单类型"
                 || headName == "下单时间" || headName == "付款时间" || headName == "发货时间" || headName == "打印快递单时间" || headName == "打印发货单时间" || headName == "打印拿货标签时间"
                 || headName == "打印快递单次数" || headName == "打印发货单次数" || headName == "打印拿货标签次数"
                 || headName == "日期")
                width = 20;
            else if (headName == "买家旺旺" || headName == "买家昵称" || headName == "订单编号" || headName == "主订单编号" || headName == "流水号" || headName == "单品货号" || headName == "商品规格" || headName == "商品销售属性" || headName == "销售属性" || headName == "商品货号" || headName == "商品简称" || headName == "快递单号")
                width = 25;
            else if (headName == "快递公司" || headName == "收件详细地址")
                width = 30;
            else if (headName == "收件人地址" || headName == "收件地址" || headName == "发件人地址" || headName == "发件地址" || headName == "商品标题" || headName == "商品名称" || headName == "商品信息" || headName == "买家留言" || headName == "卖家备注" || headName == "打印内容")
                width = 35;

            return width;
        }
        #endregion

        #region 订单分发-所有订单和待发订单导出

        public static void FxOrderToDataTable(ExportTask task, DataPackage data, int toalCount, List<Order> orders, ExportSetting setting)
        {
            var dt = data.Model.Table;
            //var sids = orders.Select(x => x.ShopId).Distinct().ToList();
            //var shops = new ShopService().GetShopByIds(sids);
            //var curShop = SiteContext.Current.CurrentLoginShop;
            var curFxUser = SiteContext.Current.CurrentFxUser;
            var fxUserId = curFxUser.Id;

            var sids = orders.Where(x => x.FxUserId == fxUserId).Select(x => x.ShopId).Distinct().ToList();
            var tuple = FxBuildExccelService.GetAgentBindShops(fxUserId, sids);
            var curUserShops = tuple.Item1;
            var agentShops = tuple.Item2;

            //商品信息
            var productMergeChkItem = setting.CheckedItems.FirstOrDefault(m => m.From == "productMerge");
            // 获取勾选的商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 除商品信息外勾选的内容
            var otherChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "productMerge").ToList();
            //// 添加省市区详细地址的扩展
            //var extChkItems = JsonExtension.ToList<CheckedItem>(JsonExtension.ToJson(setting.CheckedItems));
            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();
                    //headNames.RemoveAt(headIndex);
                    //headNames.InsertRange(headIndex, addr);

                    otherChkItems.RemoveAt(headIndex);
                    otherChkItems.InsertRange(headIndex, addrLst);
                }
            }

            #region 收件人加密解密
            if (setting.CheckedItems.Any(x => x.Value.Contains("ToFullAddress") || x.Value.Contains("ToAddress") || x.Value.Contains("ToName") || x.Value.Contains("ToPhone") || x.Value.Contains("ToMobile")))
            {
                FxPlatformEncryptService.TryToDecryptOrders(orders, isMask: true, throwEx: false);
                #region 对收件人数据进行脱敏
                //EncryptionService DataMaskservice = new EncryptionService();
                //var noVirtualOrders = DataMaskservice.getPlatformType(orders);
                //EncryptionService.DataMaskingExpression(orders.Where(w=> noVirtualOrders.Any(a=> a.LogicOrderId == w.LogicOrderId ) )?.ToList());//EncryptionService.DataMaskingReflection(orders);
                var notEncryptOrders = orders.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x?.ToMobile?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
                notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
                if (notEncryptOrders.Any())
                    EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);

                #endregion
            }


            #endregion

            //获取列表头起始位置
            var headDic = new Dictionary<string, int>();
            for (int i = 0; i < otherChkItems.Count; i++)
            {
                if (!headDic.ContainsKey(otherChkItems[i].Text))
                    headDic.Add(otherChkItems[i].Text, i);
            }

            if (productMergeChkItem != null && !headDic.ContainsKey("商品信息"))
                headDic.Add("商品信息", headDic.Count);

            // 导出商家或者店铺名称
            var supplierUsers = new List<SupplierUser>();
            var agentUsers = new List<SupplierUser>();
            if (setting.CheckedItems.Any(x => x.Value == "Export_ShopName") || setting.CheckedItems.Any(x => x.Value == "Export_SupplierName"))
            {
                var supplierUserRepository = new SupplierUserRepository();
                if (setting.CheckedItems.Any(x => x.Value == "Export_SupplierName"))
                    supplierUsers = supplierUserRepository.GetByFxUserId(fxUserId, true,needEncryptAccount:true);
                if (setting.CheckedItems.Any(x => x.Value == "Export_ShopName"))
                    agentUsers = supplierUserRepository.GetByFxUserId(fxUserId, false,needEncryptAccount:true);
            }


            #region 组装DataTable

            orders.ForEach(order =>
            {
                var dic = order.ToDictionary();
                var row = dt.NewRow();
                dt.Rows.Add(row);

                //// 销售价是否对厂家可见（订单原始节点和设置对厂家销售价可见节点）
                //var agentIsSalePrice = order.FxUserId == curFxUser.Id || (agentIsSalePrices.FirstOrDefault(x => x.FxUserId == order.UpFxUserId)?.Value ?? false);

                // 2022-03-17改为只导出系统单号
                var platformOrderIds = string.Empty;
                var childOrderIds = order.OrderItems.Where(x => x.OrignalPlatformOrderId.IsNotNullOrEmpty()).Select(x => x.OrignalPlatformOrderId).Distinct().ToList(); //dic["ChildOrderId"].ToString2();
                if (childOrderIds != null && childOrderIds.Any())
                    platformOrderIds = string.Join("\n", childOrderIds);
                else
                    platformOrderIds = dic["PlatformOrderId"].ToString2();

                // 商品信息所在列序号集合
                //var productCellIndexLst = new List<int>();
                var oiCount = order.OrderItems.Count;
                // 填充每一列的值
                otherChkItems.ForEach(chkItem =>
                {
                    var field = chkItem.Value.Replace("Export_", "");
                    var text = chkItem.Text;
                    var from = chkItem.From;

                    #region 商品信息显示在多行(合并行)，即订单信息多行合并，商品信息多行显示 记录合并坐标
                    if (setting.Export_ProductShowStyle == "Export_ShowMutilLine_Merge" && oiCount > 1 && headDic.ContainsKey(text))
                    {
                        var beginCol = headDic[text];
                        var beginRowIndex = toalCount + dt.Rows.Count + 1;//表头占1行，从2行开始
                        var endCol = beginCol;
                        var endRowIndex = beginRowIndex + oiCount - 1;
                        data.Model.MergeInfos.Add(new MergeCellRangeAddress { BeginCol = beginCol, BeginRowIndex = beginRowIndex, EndCol = endCol, EndRowIndex = endRowIndex });
                    }
                    #endregion

                    #region 填充每列的内容
                    if (from == "shop")
                    {
                        if (field == "ShopName")
                        {
                            var agent = agentUsers?.FirstOrDefault(x => x.FxUserId == order.UpFxUserId);
                            string agentName = "";
                            if (agent == null)
                            {
                                agentName = curUserShops.FirstOrDefault(x => x.Id == order.ShopId)?.NickName ?? "店铺或商家已解绑";
                            }
                            else
                            {
                                if (!string.IsNullOrWhiteSpace(order.AgentShopName))
                                {
                                    agentName = order.AgentShopName;
                                }
                                else
                                {
                                    agentName = agent.AgentMobileAndRemark;
                                }
                            }

                            row[text] = agentName;
                        }
                        else if (field == "SupplierName")
                        {
                            var defaultSupplier = curFxUser.NickName.IsNullOrEmpty() ? curFxUser.Mobile : curFxUser.NickName;
                            var supplier = supplierUsers?.FirstOrDefault(x => x.SupplierFxUserId == order.DownFxUserId);
                            var supplierName = supplier == null ? defaultSupplier : supplier.SupplierMobileAndRemark;
                            row[text] = supplierName;
                        }
                        //var shop = shops.FirstOrDefault(m => m.Id == order.ShopId);
                        //row[text] = shop == null ? "" : shop.NickName;
                    }
                    else if (from == "order")
                    {
                        // 收件人地址判断省市区地址是否合并一列显示
                        if (field == "ToAddress")
                        {
                            if (setting.Export_AddressShowStyle == "Export_Address_Merge")
                            {
                                // 省市区地址在同列显示
                                var address = order.ToProvince.ToString2() + order.ToCity.ToString2() + order.ToCounty.ToString2() + order.ToAddress.ToString2();
                                row[text] = address;
                            }
                            else
                            {
                                // 省市区地址在不同列显示
                                row["收件省"] = order.ToProvince.ToString2();
                                row["收件市"] = order.ToCity.ToString2();
                                row["收件区"] = order.ToCounty.ToString2();
                                row["收件详细地址"] = order.ToAddress.ToString2();
                            }
                        }
                        else if (field == "PlatformType" || field == "OrderFrom")
                        {
                            // 订单来源
                            var val = dic[field].ToString2().Trim().ToLower() == "alibaba" ? "1688" : dic[field].ToString2().Trim().ToLower();
                            val = val == "importorder" ? "导入单" : (val == "customerorder" ? "录入单" : val);
                            row[text] = val;
                        }
                        else if (field == "OrderTime")
                        {
                            // 订单日期
                            var status = dic["PlatformStatus"].ToString2().Trim().ToLower();
                            var val = status == "waitbuyerpay" || status == "confirm_goods_but_not_fund" ? dic["CreateTime"].ToString2() : dic["PayTime"].ToString2();
                            row[text] = val;
                        }
                        else if (field == "OrderCount")
                        {
                            // 订单数
                            var count = order.SubOrders?.Count ?? 0;
                            row[text] = count;
                        }
                        else if (field == "SellerRemark" || field == "BuyerRemark")
                        {
                            var arr = dic[field].ToString2().Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                            var str = arr == null ? "" : string.Join(";", arr);
                            row[text] = str;
                        }
                        else if (field == "ToPhone")
                        {
                            var phone = dic["ToMobile"].ToString2().IsNullOrEmpty() ? dic["ToPhone"].ToString2() : dic["ToMobile"].ToString2();
                            if (order.PlatformType == PlatformType.WxVideo.ToString())
                            {
                                if (order.ExtField2.IsNotNullOrEmpty())
                                    phone = $"{(order.ToPhone ?? order.ToMobile)}-{order.ExtField2}";
                            }
                            row[text] = phone;
                        }
                        else if (field == "SenderPhone")
                        {
                            var phone = dic["SenderMobile"].ToString2().IsNullOrEmpty() ? dic["SenderPhone"].ToString2() : dic["SenderMobile"].ToString2();
                            row[text] = phone;
                        }
                        else if (field == "PlatformOrderId")
                        {
                            //var pids = string.Join("\n", order.OrderItems.Select(x => x.OrignalOrderId.IsNotNullOrEmpty() ? x.OrignalOrderId : x.LogicOrderId));
                            row[text] = platformOrderIds;
                        }
                        else if (field == "SenderCompany")
                        {
                            string expressName = "";
                            if (!string.IsNullOrWhiteSpace(task.ExtField6) && task.ExtField6.Equals(PlatformType.TikTok.ToString()))
                            {
                                expressName = order.WaybillCodes?.FirstOrDefault(f => f.LogisticType == 1)?.ExpressName ?? "";
                            }
                            else
                            {
                                expressName = order.WaybillCodes?.FirstOrDefault()?.ExpressName ?? "";
                            }

                            row[text] = expressName;

                            ////var printHistory = printHistorys.Where(c => !c.ExpressName.IsNullOrEmpty() && c.ShopId == order.ShopId && (c.OrderId == order.PlatformOrderId || c.OrderId.ToString2().Contains(order.PlatformOrderId) || (isCustomerOrder ? c.OrderId == order.CustomerOrderId : c.OrderId == order.MergeredOrderId))).OrderByDescending(x => x.GetDate).FirstOrDefault();
                            //var printHistory = printHistoryLst?.OrderByDescending(x => x.GetDate).FirstOrDefault();
                            //var expressName = printHistory?.ExpressName.ToString2() ?? "";
                            //row[text] = expressName;
                        }
                        else if (field == "LastWaybillCode")
                        {
                            var printHistoryStr = string.Empty;
                            var isWaitSend = order.PlatformStatus == "waitsellersend";

                            if (order.WaybillCodes != null && order.WaybillCodes.Any())
                            {
                                if (!string.IsNullOrWhiteSpace(task.ExtField6) && task.ExtField6.Equals(PlatformType.TikTok.ToString()))
                                {
                                    var waybillCodes = order.WaybillCodes.Where(w => w.LogisticType == 1).OrderByDescending(x => x.CreateDate).Select(m => m.WaybillCode).Distinct().ToList();
                                    printHistoryStr = string.Join("\n", waybillCodes);
                                }
                                else
                                {
                                    var waybillCodes = order.WaybillCodes.OrderByDescending(x => x.CreateDate).Select(m => m.WaybillCode).Distinct().ToList();
                                    printHistoryStr = string.Join("\n", waybillCodes);
                                }

                                //var printHistory = order.WaybillCodes?.OrderByDescending(c => c.CreateDate).FirstOrDefault(); //最近一次打印记录
                                //var printHistoryLst = printHistory == null ? new List<WaybillCodeViewModel>() : order.WaybillCodes.Where(m => m.CreateDate == printHistory.CreateDate).ToList();//根据打印时间判断是否是一单多包
                                //                                                                                                                                                                // 一单多包导出多个运单号
                                //if (printHistoryLst != null && printHistoryLst.Count() > 1)
                                //    printHistoryStr = string.Join("\n", printHistoryLst?.Select(m => m.WaybillCode).ToList() ?? new List<string>());
                                //else
                                //    printHistoryStr = printHistory?.WaybillCode.ToString2() ?? "";
                            }
                            // 所有订单页面仅显示已打印和未打印，待打页面显示的是自己可以打印的订单（自营或者最后厂家）显示物流单号
                            if (task.Type == ExportType.ErpAllOrder.ToInt() && (string.IsNullOrWhiteSpace(task.ExtField6) || !task.ExtField6.Equals(PlatformType.TikTok.ToString())))
                            {
                                printHistoryStr = isWaitSend ? (printHistoryStr.IsNotNullOrEmpty() ? "已打印" : "未打印") : printHistoryStr;
                            }
                            row[text] = printHistoryStr;

                            //var printHistoryStr = string.Empty;
                            ////var printHistoryLst = printHistorys.Where(c => !c.ExpressWayBillCode.IsNullOrEmpty() && c.ShopId == order.ShopId && (c.OrderId == order.PlatformOrderId || c.OrderId.ToString2().Contains(order.PlatformOrderId) || (isCustomerOrder ? c.OrderId == order.CustomerOrderId : c.OrderId == order.MergeredOrderId))); 
                            //var printHistory = printHistoryLst?.OrderByDescending(c => c.GetDate).FirstOrDefault(); //最近一次打印记录
                            //printHistoryLst = printHistory == null ? new List<WaybillCode>() : printHistoryLst.Where(m => m.GetDate == printHistory.GetDate).ToList();//根据打印时间判断是否是一单多包
                            //// 一单多包导出多个运单号
                            //if (printHistoryLst != null && printHistoryLst.Count() > 1)
                            //    printHistoryStr = string.Join("\n", printHistoryLst?.Select(m => m.ExpressWayBillCode).ToList() ?? new List<string>());
                            //else
                            //    printHistoryStr = printHistory?.ExpressWayBillCode.ToString2() ?? "";
                            //row[text] = printHistoryStr;
                        }
                        else if (field == "ErpStatus")
                        {
                            var status = dic["PlatformStatus"].ToString2();
                            var val = "";
                            switch (status)
                            {
                                case "waitsellersend":
                                    val = "待发货";
                                    break;
                                case "inrefund":
                                    val = "退款单";
                                    break;
                                case "sended":
                                    val = "待收货";
                                    break;
                                case "success":
                                    val = "交易完成";
                                    break;
                                case "close":
                                case "cancel":
                                    val = "交易取消";
                                    break;
                                case "shipped":
                                    val = "已交运";
                                    break;
                                default:
                                    val = "未知状态";
                                    break;
                            }
                            row[text] = val;
                        }
                        else if (field == "TotalAmount")
                        {
                            // 销售价不可见只显示为空
                            var totalAmount = order.IsShowSalePrice ? order.OrderItems.Sum(x => (x.ItemAmount ?? 0)).ToString2() : "无权限";
                            //京东的ItemAmount 不会平摊优惠金额， 会导致订单金额和页面显示的订单实付不一致
                            if (order.PlatformType == PlatformType.Jingdong.ToString())
                                totalAmount = order.IsShowSalePrice ? order.TotalAmount.ToString2() : "无权限";
                            //禾量的ItemAmount 是商品的单价，会导致订单金额和页面显示的订单实付不一致
                            if (order.PlatformType == PlatformType.Other_Heliang.ToString())
                                totalAmount = order.IsShowSalePrice ? order.TotalAmount.ToString2() : "无权限";
                            row[text] = totalAmount;
                        }
                        else if (field == "ShippingFee") // 运费
                        {
                            // 销售价不可见只显示为空
                            var shippingFee = order.IsShowSalePrice ? order.ShippingFee.ToString2() : "无权限";
                            row[text] = shippingFee;
                        }
                        else if (field == "PlatformSubsidy") // 平台补贴
                        {
                            // 销售价不可见只显示为空
                            var platformSubsidy = order.IsShowSalePrice ? order.PlatformSubsidy.ToString2() : "无权限";
                            row[text] = platformSubsidy;
                        }
                        else if (field == "PayTotalAmount")// 实收款 
                        {
                            // 销售价不可见只显示为空
                            var payTotalAmount = order.IsShowSalePrice ? order.PayTotalAmount.ToString2() : "无权限";
                            row[text] = payTotalAmount;
                        }
                        ///是否是跨境站点
                        if (field == "ToCountry")
                        {
                            ///发往国家
                            var toCountry = order.ToCountry;
                            row[text] = toCountry;
                        }
                        else if (field == "Currency")
                        {
                            ///币种
                            row[text] = order.Currency;
                        }
                        else if (field == "PackageStatus")
                        {
                            ////暂定，待订单数据
                            ///交运状态 "未交运"
                            var val = order.LogisticStatus == 2 ? "已交运" : "未交运";
                            row[text] = val;
                        }
                        else
                        {
                            ///0729:存在上面执完获取到数据后 还会进去else 增加校验
                            object value;
                            if (dic.TryGetValue(field, out value) && string.IsNullOrEmpty(row[text].ToString()))
                                row[text] = value?.ToString2();
                        }
                    }
                    #endregion
                });

                //商品信息填充
                if (productChkItems.Any())
                {
                    if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
                    {
                        #region 商品信息合并1行1列显示(商品数据都在【商品信息】)
                        var productContent = new StringBuilder();
                        // 获取商品信息
                        order.OrderItems.ForEach(item =>
                        {
                            var itemDic = item.ToDictionary();
                            var productSubContent = new StringBuilder();
                            // 结算价的加密
                            string EncryptSettlementPrice(string value)
                            {
                                return value.IsNotNullOrEmpty() ? (value.Equals("-99") ? "**" : value) : "未设置";
                            }
                            // 商品信息合并1列显示
                            productChkItems.ForEach(chkItem2 =>
                            {
                                var field2 = chkItem2.Value.Replace("Export_", "");
                                var text2 = chkItem2.Text;
                                var from2 = chkItem2.From;
                                var val = string.Empty;
                                if (field2 == "SkuAttr")
                                    val = itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2();
                                else if (field2 == "ProductStatus")
                                {
                                    val = itemDic["RefundStatus"].ToString2();
                                    val = val == RefundStatusType.WAIT_SELLER_AGREE.ToString() ? "退款中" :
                                          val == RefundStatusType.REFUND_SUCCESS.ToString() ? "退款成功" : val;
                                }
                                else if (field2 == "Count")
                                    val = $"{itemDic[field2].ToString2()},";
                                else if (field2 == "Price")
                                    val = order.IsShowSalePrice ? $"{itemDic[field2].ToString2()}," : "";
                                else if (field2 == "Weight" || field2 == "SkuWeight")
                                    val = itemDic[field2].ToString2() == "0" ? "" : itemDic[field2].ToString2();
                                else if (field2 == "ProductSubject")
								{
                                    val = itemDic[field2].ToString2();
                                    if (string.IsNullOrWhiteSpace(val) && order.IsShowProductTitle == false)
                                    {
                                        val = "合作方已设为隐藏信息";
                                    }
                                }
                                else if (field2 == "DownFxUserSettlementPrice")
                                {
                                    val = EncryptSettlementPrice(itemDic[field2].ToString2());
                                }
                                else if (field2 == "AuthorDownSettlementPrice")
                                {
                                    val = EncryptSettlementPrice(itemDic[field2].ToString2());
                                }
                                else if (field2 == "UpFxUserSettlementPrice")
                                {
                                    val = EncryptSettlementPrice(itemDic[field2].ToString2());
                                }
                                else if (field2 == "AuthorUpSettlementPrice")
                                {
                                    val = EncryptSettlementPrice(itemDic[field2].ToString2());
                                }
                                else
                                    val = itemDic[field2].ToString2();

                                productContent.Append($"{val},");
                            });
                            productContent.Append($"{productSubContent.ToString2().TrimEnd(",")};\n");
                        });
                        if (dt.Columns.Contains("商品信息"))
                            row["商品信息"] = productContent.ToString2().TrimEnd(",");
                        #endregion
                    }
                    else
                    {
                        #region 商品信息显示在多行(独立行)，即订单信息为空，商品信息多行填充显示
                        var isFirstItemRowIndex = true;
                        order.OrderItems.ForEach(oi =>
                        {
                            var itemDic = oi.ToDictionary();
                            var productSubContent = new StringBuilder();
                            if (isFirstItemRowIndex)
                            {
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;

                                    var val = string.Empty;
                                    if (field2 == "SkuAttr")
                                        val = itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2();
                                    else if (field2 == "ProductStatus")
                                    {
                                        val = itemDic["RefundStatus"].ToString2();
                                        val = val == RefundStatusType.WAIT_SELLER_AGREE.ToString() ? "退款中" :
                                              val == RefundStatusType.REFUND_SUCCESS.ToString() ? "退款成功" : val;
                                    }
                                    else if (field2 == "Count")
                                        val = $"{itemDic[field2].ToString2()},";
                                    else if (field2 == "Price")
                                        val = order.IsShowSalePrice ? $"{itemDic[field2].ToString2()}," : "";
                                    else if (field2 == "Weight" || field2 == "SkuWeight")
                                        val = itemDic[field2].ToString2() == "0" ? "" : itemDic[field2].ToString2();
                                    else if (field2 == "ProductSubject")
                                    {
                                        val = itemDic[field2].ToString2();
                                        if (string.IsNullOrWhiteSpace(val) && order.IsShowProductTitle == false)
                                        {
                                            val = "合作方已设为隐藏信息";
                                        }
                                    }
                                    else if (field2 == "DownFxUserSettlementPrice")
                                    {
                                        var value = itemDic[field2].ToString2();
                                        if (value.IsNotNullOrEmpty())
                                        {
                                            val = value.Equals("-99") ? "**" : value;
                                        }
                                        else
                                        {
                                            val = "未设置";
                                        }

                                    }
                                    else if (field2 == "AuthorDownSettlementPrice")
                                    {
                                        var value = itemDic[field2].ToString2();
                                        if (value.IsNotNullOrEmpty())
                                        {
                                            val = value.Equals("-99") ? "**" : value;
                                        }
                                        else
                                        {
                                            val = "未设置";
                                        }
                                    }
                                    else if (field2 == "UpFxUserSettlementPrice")
                                    {
                                        var value = itemDic[field2].ToString2();
                                        if (value.IsNotNullOrEmpty())
                                        {
                                            val = value.Equals("-99") ? "**" : value;
                                        }
                                        else
                                        {
                                            val = "未设置";
                                        }
                                    }
                                    else if (field2 == "AuthorUpSettlementPrice")
                                    {
                                        var value = itemDic[field2].ToString2();
                                        if (value.IsNotNullOrEmpty())
                                        {
                                            val = value.Equals("-99") ? "**" : value;
                                        }
                                        else
                                        {
                                            val = "未设置";
                                        }
                                    }
                                    else
                                    {
                                        if (itemDic.ContainsKey(field2) == false)
                                        {
                                            Log.WriteError($"导出订单获取商品信息找不到Key={field2}，itemDic={itemDic.ToJson()}", "ExportOrderErrLog.txt");
                                            val = "";
                                        }
                                        else
                                            val = itemDic[field2].ToString2();
                                    }

                                    if (productMergeChkItem != null)
                                        productSubContent.Append($"{val},");
                                    row[text2] = val.ToString2().TrimEnd(",");
                                });
                                if (dt.Columns.Contains("商品信息"))
                                    row["商品信息"] = productSubContent.ToString2().TrimEnd(",");
                                isFirstItemRowIndex = false;
                            }
                            else
                            {
                                var row2 = dt.NewRow();
                                dt.Rows.Add(row2);
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = string.Empty;
                                    if (field2 == "SkuAttr")
                                        val = itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2();
                                    else if (field2 == "ProductStatus")
                                    {
                                        val = itemDic["RefundStatus"].ToString2();
                                        val = val == RefundStatusType.WAIT_SELLER_AGREE.ToString() ? "退款中" :
                                              val == RefundStatusType.REFUND_SUCCESS.ToString() ? "退款成功" : val;
                                    }
                                    else if (field2 == "Count")
                                        val = $"{itemDic[field2].ToString2()},";
                                    else if (field2 == "Price")
                                        val = order.IsShowSalePrice ? $"{itemDic[field2].ToString2()}," : "";
                                    else if (field2 == "Weight" || field2 == "SkuWeight")
                                        val = itemDic[field2].ToString2() == "0" ? "" : itemDic[field2].ToString2();
                                    else
                                        val = itemDic[field2].ToString2();

                                    if (productMergeChkItem != null)
                                        productSubContent.Append($"{val},");
                                    row2[text2] = val.ToString2().TrimEnd(",");
                                });
                                if (dt.Columns.Contains("商品信息"))
                                    row2["商品信息"] = productSubContent.ToString2().TrimEnd(",");
                            }
                        });
                        #endregion
                    }
                }
            });
            #endregion
        }

        /// <summary>
        /// 前端导出订单
        /// </summary>
        /// <param name="data"></param>
        /// <param name="toalCount"></param>
        /// <param name="orders"></param>
        /// <param name="setting"></param>
        /// <param name="isCrossBorderSite"></param>
        /// <param name="type"></param>
        public static void FxOrderToDataTableV2(DataPackage data, int toalCount, List<Order> orders, ExportSetting setting, bool isCrossBorderSite , ExportType type)
        {
            var dt = data.Model.Table;
            var curFxUser = SiteContext.Current.CurrentFxUser;
            var fxUserId = curFxUser.Id;

            var sids = orders.Where(x => x.FxUserId == fxUserId).Select(x => x.ShopId).Distinct().ToList();
            var tuple = FxBuildExccelService.GetAgentBindShops(fxUserId, sids);
            var curUserShops = tuple.Item1;
            var agentShops = tuple.Item2;

            //商品信息
            var productMergeChkItem = setting.CheckedItems.FirstOrDefault(m => m.From == "productMerge");
            // 获取勾选的商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 除商品信息外勾选的内容
            var otherChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "productMerge").ToList();
            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();

                    otherChkItems.RemoveAt(headIndex);
                    otherChkItems.InsertRange(headIndex, addrLst);
                }
            }

            #region 收件人加密解密
            if (setting.CheckedItems.Any(x => x.Value.Contains("ToFullAddress") || x.Value.Contains("ToAddress") || x.Value.Contains("ToName") || x.Value.Contains("ToPhone") || x.Value.Contains("ToMobile")))
            {
                FxPlatformEncryptService.TryToDecryptOrders(orders, isMask: true, throwEx: false);
                #region 对收件人数据进行脱敏
                var notEncryptOrders = orders.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x?.ToMobile?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
                notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
                if (notEncryptOrders.Any())
                    EncryptionService.DataMaskingExpression(notEncryptOrders);
                #endregion
            }

            #endregion

            //获取列表头起始位置
            var headDic = new Dictionary<string, int>();
            for (int i = 0; i < otherChkItems.Count; i++)
            {
                if (!headDic.ContainsKey(otherChkItems[i].Text))
                    headDic.Add(otherChkItems[i].Text, i);
            }

            if (productMergeChkItem != null && !headDic.ContainsKey("商品信息"))
                headDic.Add("商品信息", headDic.Count);

            // 导出商家或者店铺名称
            var supplierUsers = new List<SupplierUser>();
            var agentUsers = new List<SupplierUser>();
            if (setting.CheckedItems.Any(x => x.Value == "Export_ShopName") || setting.CheckedItems.Any(x => x.Value == "Export_SupplierName"))
            {
                var supplierUserRepository = new SupplierUserRepository();
                if (setting.CheckedItems.Any(x => x.Value == "Export_SupplierName"))
                    supplierUsers = supplierUserRepository.GetByFxUserId(fxUserId, true,needEncryptAccount:true);
                if (setting.CheckedItems.Any(x => x.Value == "Export_ShopName"))
                    agentUsers = supplierUserRepository.GetByFxUserId(fxUserId, false,needEncryptAccount:true);
            }
            #region 组装DataTable

            orders.ForEach(order =>
            {
                var dic = order.ToDictionary();
                var row = dt.NewRow();
                dt.Rows.Add(row);

                // 2022-03-17改为只导出系统单号
                var platformOrderIds = string.Empty;
                var childOrderIds = order.OrderItems.Where(x => x.OrignalPlatformOrderId.IsNotNullOrEmpty()).Select(x => x.OrignalPlatformOrderId).Distinct().ToList(); //dic["ChildOrderId"].ToString2();
                if (childOrderIds != null && childOrderIds.Any())
                    platformOrderIds = string.Join("\n", childOrderIds);
                else
                    platformOrderIds = dic["PlatformOrderId"].ToString2();

                // 商品信息所在列序号集合
                //var productCellIndexLst = new List<int>();
                var oiCount = order.OrderItems.Count;
                // 填充每一列的值
                otherChkItems.ForEach(chkItem =>
                {
                    var field = chkItem.Value.Replace("Export_", "");
                    var text = chkItem.Text;
                    var from = chkItem.From;

                    #region 商品信息显示在多行(合并行)，即订单信息多行合并，商品信息多行显示 记录合并坐标
                    if (setting.Export_ProductShowStyle == "Export_ShowMutilLine_Merge" && oiCount > 1 && headDic.ContainsKey(text))
                    {
                        var beginCol = headDic[text];
                        var beginRowIndex = toalCount + dt.Rows.Count + 1;//表头占1行，从2行开始
                        var endCol = beginCol;
                        var endRowIndex = beginRowIndex + oiCount - 1;
                        data.Model.MergeInfos.Add(new MergeCellRangeAddress { BeginCol = beginCol, BeginRowIndex = beginRowIndex, EndCol = endCol, EndRowIndex = endRowIndex });
                    }
                    #endregion

                    #region 填充每列的内容
                    if (from == "shop")
                    {
                        if (field == "ShopName")
                        {
                            var agent = agentUsers?.FirstOrDefault(x => x.FxUserId == order.UpFxUserId);
                            var agentName = agent == null ? curUserShops.FirstOrDefault(x => x.Id == order.ShopId)?.NickName ?? "店铺或商家已解绑" : agent.AgentMobileAndRemark;
                            row[text] = agentName;
                        }
                        else if (field == "SupplierName")
                        {
                            var defaultSupplier = curFxUser.NickName.IsNullOrEmpty() ? curFxUser.Mobile : curFxUser.NickName;
                            var supplier = supplierUsers?.FirstOrDefault(x => x.SupplierFxUserId == order.DownFxUserId);
                            var supplierName = supplier == null ? defaultSupplier : supplier.SupplierMobileAndRemark;
                            row[text] = supplierName;
                        }
                    }
                    else if (from == "order")
                    {
                        // 收件人地址判断省市区地址是否合并一列显示
                        if (field == "ToAddress")
                        {
                            if (setting.Export_AddressShowStyle == "Export_Address_Merge")
                            {
                                // 省市区地址在同列显示
                                var address = order.ToProvince.ToString2() + order.ToCity.ToString2() + order.ToCounty.ToString2() + order.ToAddress.ToString2();
                                row[text] = address;
                            }
                            else
                            {
                                // 省市区地址在不同列显示
                                row["收件省"] = order.ToProvince.ToString2();
                                row["收件市"] = order.ToCity.ToString2();
                                row["收件区"] = order.ToCounty.ToString2();
                                row["收件详细地址"] = order.ToAddress.ToString2();
                            }
                        }
                        else if (field == "PlatformType" || field == "OrderFrom")
                        {
                            // 订单来源
                            var val = dic[field].ToString2().Trim().ToLower() == "alibaba" ? "1688" : dic[field].ToString2().Trim().ToLower();
                            val = val == "importorder" ? "导入单" : (val == "customerorder" ? "录入单" : val);
                            row[text] = val;
                        }
                        else if (field == "OrderTime")
                        {
                            // 订单日期
                            var status = dic["PlatformStatus"].ToString2().Trim().ToLower();
                            var val = status == "waitbuyerpay" || status == "confirm_goods_but_not_fund" ? dic["CreateTime"].ToString2() : dic["PayTime"].ToString2();
                            row[text] = val;
                        }
                        else if (field == "OrderCount")
                        {
                            // 订单数
                            var count = order.SubOrders?.Count ?? 0;
                            row[text] = count;
                        }
                        else if (field == "SellerRemark" || field == "BuyerRemark")
                        {
                            var arr = dic[field].ToString2().Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                            var str = arr == null ? "" : string.Join(";", arr);
                            row[text] = str;
                        }
                        else if (field == "ToPhone")
                        {
                            var phone = dic["ToMobile"].ToString2().IsNullOrEmpty() ? dic["ToPhone"].ToString2() : dic["ToMobile"].ToString2();
                            if (order.PlatformType == PlatformType.WxVideo.ToString())
                            {
                                if (order.ExtField2.IsNotNullOrEmpty())
                                    phone = $"{(order.ToPhone ?? order.ToMobile)}-{order.ExtField2}";
                            }
                            row[text] = phone;
                        }
                        else if (field == "SenderPhone")
                        {
                            var phone = dic["SenderMobile"].ToString2().IsNullOrEmpty() ? dic["SenderPhone"].ToString2() : dic["SenderMobile"].ToString2();
                            row[text] = phone;
                        }
                        else if (field == "PlatformOrderId")
                        {
                            row[text] = platformOrderIds;
                        }
                        else if (field == "SenderCompany")
                        {
                            string expressName = "";
                            if (isCrossBorderSite)
                            {
                                expressName = order.WaybillCodes?.FirstOrDefault(f => f.LogisticType == 1)?.ExpressName ?? "";
                            }
                            else
                            {
                                expressName = order.WaybillCodes?.FirstOrDefault()?.ExpressName ?? "";
                            }

                            row[text] = expressName;
                        }
                        else if (field == "LastWaybillCode")
                        {
                            var printHistoryStr = string.Empty;
                            var isWaitSend = order.PlatformStatus == "waitsellersend";

                            if (order.WaybillCodes != null && order.WaybillCodes.Any())
                            {
                                if (isCrossBorderSite)
                                {
                                    var waybillCodes = order.WaybillCodes.Where(w => w.LogisticType == 1).OrderByDescending(x => x.CreateDate).Select(m => m.WaybillCode).Distinct().ToList();
                                    printHistoryStr = string.Join("\n", waybillCodes);
                                }
                                else
                                {
                                    var waybillCodes = order.WaybillCodes.OrderByDescending(x => x.CreateDate).Select(m => m.WaybillCode).Distinct().ToList();
                                    printHistoryStr = string.Join("\n", waybillCodes);
                                }
                            }
                            // 所有订单页面仅显示已打印和未打印，待打页面显示的是自己可以打印的订单（自营或者最后厂家）显示物流单号
                            if (type == ExportType.ErpAllOrder && isCrossBorderSite == false)
                            {
                                printHistoryStr = isWaitSend ? (printHistoryStr.IsNotNullOrEmpty() ? "已打印" : "未打印") : printHistoryStr;
                            }
                            row[text] = printHistoryStr;
                        }
                        else if (field == "ErpStatus")
                        {
                            var status = dic["PlatformStatus"].ToString2();
                            var val = "";
                            switch (status)
                            {
                                case "waitsellersend":
                                    val = "待发货";
                                    break;
                                case "inrefund":
                                    val = "退款单";
                                    break;
                                case "sended":
                                    val = "待收货";
                                    break;
                                case "success":
                                    val = "交易完成";
                                    break;
                                case "close":
                                case "cancel":
                                    val = "交易取消";
                                    break;
                                case "shipped":
                                    val = "已交运";
                                    break;
                                default:
                                    val = "未知状态";
                                    break;
                            }
                            row[text] = val;
                        }
                        else if (field == "TotalAmount")
                        {
                            // 销售价不可见只显示为空
                            var totalAmount = order.IsShowSalePrice ? order.OrderItems.Sum(x => (x.ItemAmount ?? 0)).ToString2() : "无权限";
                            //京东的ItemAmount 不会平摊优惠金额， 会导致订单金额和页面显示的订单实付不一致
                            if (order.PlatformType == PlatformType.Jingdong.ToString())
                                totalAmount = order.IsShowSalePrice ? order.TotalAmount.ToString2() : "无权限";
                            //禾量的ItemAmount 是商品的单价，会导致订单金额和页面显示的订单实付不一致
                            if (order.PlatformType == PlatformType.Other_Heliang.ToString())
                                totalAmount = order.IsShowSalePrice ? order.TotalAmount.ToString2() : "无权限";
                            row[text] = totalAmount;
                        }
                        else if (field == "ShippingFee") // 运费
                        {
                            // 销售价不可见只显示为空
                            var shippingFee = order.IsShowSalePrice ? order.ShippingFee.ToString2() : "无权限";
                            row[text] = shippingFee;
                        }
                        else if (field == "PlatformSubsidy") // 平台补贴
                        {
                            // 销售价不可见只显示为空
                            var platformSubsidy = order.IsShowSalePrice ? order.PlatformSubsidy.ToString2() : "无权限";
                            row[text] = platformSubsidy;
                        }
                        else if (field == "PayTotalAmount")// 实收款 
                        {
                            // 销售价不可见只显示为空
                            var payTotalAmount = order.IsShowSalePrice ? order.PayTotalAmount.ToString2() : "无权限";
                            row[text] = payTotalAmount;
                        }
                        ///是否是跨境站点
                        if (field == "ToCountry")
                        {
                            ///发往国家
                            var toCountry = order.ToCountry;
                            row[text] = toCountry;
                        }
                        else if (field == "Currency")
                        {
                            ///币种
                            row[text] = order.Currency;
                        }
                        else if (field == "PackageStatus")
                        {
                            ////暂定，待订单数据
                            ///交运状态 "未交运"
                            var val = order.LogisticStatus == 2 ? "已交运" : "未交运";
                            row[text] = val;
                        }
                        else
                        {
                            ///0729:存在上面执完获取到数据后 还会进去else 增加校验
                            object value;
                            if (dic.TryGetValue(field, out value) && string.IsNullOrEmpty(row[text].ToString()))
                                row[text] = value?.ToString2();
                        }
                    }
                    #endregion
                });

                //商品信息填充
                if (productChkItems.Any())
                {
                    if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
                    {
                        #region 商品信息合并1行1列显示(商品数据都在【商品信息】)
                        var productContent = new StringBuilder();
                        // 获取商品信息
                        order.OrderItems.ForEach(item =>
                        {
                            var itemDic = item.ToDictionary();
                            var productSubContent = new StringBuilder();
                            // 商品信息合并1列显示
                            productChkItems.ForEach(chkItem2 =>
                            {
                                var field2 = chkItem2.Value.Replace("Export_", "");
                                var text2 = chkItem2.Text;
                                var from2 = chkItem2.From;
                                var val = string.Empty;
                                if (field2 == "SkuAttr")
                                    val = itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2();
                                else if (field2 == "ProductStatus")
                                {
                                    val = itemDic["RefundStatus"].ToString2();
                                    val = val == RefundStatusType.WAIT_SELLER_AGREE.ToString() ? "退款中" :
                                          val == RefundStatusType.REFUND_SUCCESS.ToString() ? "退款成功" : val;
                                }
                                else if (field2 == "Count")
                                    val = $"{itemDic[field2].ToString2()},";
                                else if (field2 == "Price")
                                    val = order.IsShowSalePrice ? $"{itemDic[field2].ToString2()}," : "";
                                else if (field2 == "Weight" || field2 == "SkuWeight")
                                    val = itemDic[field2].ToString2() == "0" ? "" : itemDic[field2].ToString2();
                                else
                                    val = itemDic[field2].ToString2();

                                productContent.Append($"{val},");
                            });
                            productContent.Append($"{productSubContent.ToString2().TrimEnd(",")};\n");
                        });
                        if (dt.Columns.Contains("商品信息"))
                            row["商品信息"] = productContent.ToString2().TrimEnd(",");
                        #endregion
                    }
                    else
                    {
                        #region 商品信息显示在多行(独立行)，即订单信息为空，商品信息多行填充显示
                        var isFirstItemRowIndex = true;
                        order.OrderItems.ForEach(oi =>
                        {
                            var itemDic = oi.ToDictionary();
                            var productSubContent = new StringBuilder();
                            if (isFirstItemRowIndex)
                            {
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;

                                    var val = string.Empty;
                                    if (field2 == "SkuAttr")
                                        val = itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2();
                                    else if (field2 == "ProductStatus")
                                    {
                                        val = itemDic["RefundStatus"].ToString2();
                                        val = val == RefundStatusType.WAIT_SELLER_AGREE.ToString() ? "退款中" :
                                              val == RefundStatusType.REFUND_SUCCESS.ToString() ? "退款成功" : val;
                                    }
                                    else if (field2 == "Count")
                                        val = $"{itemDic[field2].ToString2()},";
                                    else if (field2 == "Price")
                                        val = order.IsShowSalePrice ? $"{itemDic[field2].ToString2()}," : "";
                                    else if (field2 == "Weight" || field2 == "SkuWeight")
                                        val = itemDic[field2].ToString2() == "0" ? "" : itemDic[field2].ToString2();
                                    else
                                    {
                                        if (itemDic.ContainsKey(field2) == false)
                                        {
                                            Log.WriteError($"导出订单获取商品信息找不到Key={field2}，itemDic={itemDic.ToJson()}", "ExportOrderErrLog.txt");
                                            val = "";
                                        }
                                        else
                                            val = itemDic[field2].ToString2();
                                    }

                                    if (productMergeChkItem != null)
                                        productSubContent.Append($"{val},");
                                    row[text2] = val.ToString2().TrimEnd(",");
                                });
                                if (dt.Columns.Contains("商品信息"))
                                    row["商品信息"] = productSubContent.ToString2().TrimEnd(",");
                                isFirstItemRowIndex = false;
                            }
                            else
                            {
                                var row2 = dt.NewRow();
                                dt.Rows.Add(row2);
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = string.Empty;
                                    if (field2 == "SkuAttr")
                                        val = itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2();
                                    else if (field2 == "ProductStatus")
                                    {
                                        val = itemDic["RefundStatus"].ToString2();
                                        val = val == RefundStatusType.WAIT_SELLER_AGREE.ToString() ? "退款中" :
                                              val == RefundStatusType.REFUND_SUCCESS.ToString() ? "退款成功" : val;
                                    }
                                    else if (field2 == "Count")
                                        val = $"{itemDic[field2].ToString2()},";
                                    else if (field2 == "Price")
                                        val = order.IsShowSalePrice ? $"{itemDic[field2].ToString2()}," : "";
                                    else if (field2 == "Weight" || field2 == "SkuWeight")
                                        val = itemDic[field2].ToString2() == "0" ? "" : itemDic[field2].ToString2();
                                    else
                                        val = itemDic[field2].ToString2();

                                    if (productMergeChkItem != null)
                                        productSubContent.Append($"{val},");
                                    row2[text2] = val.ToString2().TrimEnd(",");
                                });
                                if (dt.Columns.Contains("商品信息"))
                                    row2["商品信息"] = productSubContent.ToString2().TrimEnd(",");
                            }
                        });
                        #endregion
                    }
                }
            });
            #endregion
        }
        #endregion

        #region 订单分发-底单导出
        public static void FxWaybillCodeToDataTable(ExportTask task, DataTable dt, List<WaybillCode> waybillCodeLst, List<WaybillCodeCheckModel> checkedItems, int lastRowNum)
        {
            //底单中保存的订单编号为逻辑订单号，需要转换成原始订单编号，才能解密收件人信息
            if (checkedItems.Any(x => x.Value == "Reciver" || x.Value == "ReciverPhone" || x.Value == "ToAddress"))
            {

                FxPlatformEncryptService.EncryptWaybillCodes(waybillCodeLst, encryptSender: true, needSyncLog: true);

                #region 收件人信息脱敏
                var notEncryptOrders = waybillCodeLst.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.ReciverPhone?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
                notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
                if (notEncryptOrders.Any())
                    EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);

                #endregion
            }

            #region 查询逻辑单获取商家信息
            var isCj = task.Type == ExportType.ErpWaybillCodeCJ.ToInt();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var sids = waybillCodeLst.Where(x => x.FxUserId == fxUserId).Select(x => x.SourceShopId).Distinct().ToList();
            var agentBindShops = new List<Shop>();
            var agentUsers = new List<SupplierUser>();
            var orderKvs = new List<KeyValuePair<string, LogicOrder>>();
            var wOrderKvs = new List<KeyValuePair<string, WaybillCodeOrder>>();
            // 获取平台单号
            if (checkedItems.Any(x => x.Value == "OrderId"))
            {
                var oids = waybillCodeLst.Where(x => x.CustomerOrderId.IsNullOrEmpty() && x.OrderId.StartsWith("C") == false).Select(x => x.OrderId).ToList();
                var moids = waybillCodeLst.Where(x => x.OrderId.ToString2().StartsWith("C")).SelectMany(x => x.OrderIdJoin.ToString2().SplitToList(",")).ToList();
                if (moids.Any())
                    oids.AddRange(moids);
                oids = oids.Distinct().ToList();
                if (oids.Any())
                {
                    //交易完成的订单，逻辑单,会在一定时间内从数据库清除。
                    //var orders = new LogicOrderService().OnlyGetLogicOrders(oids, new List<string> { "LogicOrderId", "PlatformOrderId" });
                    //var orderDic = orders.GroupBy(x => x.LogicOrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault());
                    //orderKvs = orderDic.ToList();

                    var waybillCodeOrders = new WaybillCodeOrderService().GetWaybillCodeOrders(oids, new List<string> { "OrderId", "CustomerOrderId" });
                    var orderDic = waybillCodeOrders.GroupBy(x => x.OrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault());
                    wOrderKvs = orderDic.ToList();
                }
            }
            //获取订单对应的商家信息
            if (checkedItems.Any(x => x.Value == "ShopName"))
            {
                var pathflowCodes = waybillCodeLst.Select(x => x.PathFlowCode).Distinct().ToList();
                if (pathflowCodes.Any())
                {
                    var pathFlowNodes = new PathFlowRepository().GetPathFlowNodeList(pathflowCodes);
                    var pathFlowNodeDic = pathFlowNodes.GroupBy(x => x.PathFlowCode).ToDictionary(x => x.Key, x => x.ToList());
                    foreach (var w in waybillCodeLst)
                    {
                        List<PathFlowNode> nodes;
                        if (pathFlowNodeDic.TryGetValue(w.PathFlowCode, out nodes))
                        {
                            w.UpFxUserId = nodes.FirstOrDefault(x => x.FxUserId == fxUserId && x.UpFxUserId > 0)?.UpFxUserId ?? 0;
                        }
                    }
                }
                var supplierUserRepository = new SupplierUserRepository();
                if (isCj)
                {
                    agentUsers = supplierUserRepository.GetByFxUserId(fxUserId, false,needEncryptAccount:true); // 当前账号的商家信息
                    sids = waybillCodeLst.Select(x => x.SourceShopId).Distinct().ToList();

                    agentBindShops = new ShopService().GetShopByIds(sids);

                }
                else
                {
                    var tuple = FxBuildExccelService.GetAgentBindShops(fxUserId, sids);
                    agentBindShops = tuple.Item1;
                    agentUsers = supplierUserRepository.GetByFxUserId(fxUserId, false,needEncryptAccount:true); // 查商家信息
                }
            }
            #endregion

            var rowIndex = lastRowNum;
            waybillCodeLst.ForEach(model =>
            {
                rowIndex++;
                var row = dt.NewRow();
                var dic = (model ?? new WaybillCode()).ToDictionary();
                foreach (var item in checkedItems)
                {
                    var key = item?.Value.ToString2() ?? "";
                    if (!dic.ContainsKey(key) && key != "RowIndex" && key != "ShopName")
                        continue;

                    var val = key == "RowIndex" ? rowIndex.ToString2() : key == "ShopName" ? "" : dic[key].ToString2();
                    if (key != "RowIndex")
                    {
                        if (key == "ToAddress")
                            val = $"{dic["ToProvince"].ToString2()} {dic["ToCity"].ToString2()} {dic["ToDistrict"].ToString2()} {dic["ToAddress"].ToString2()}";
                        else if (key == "BuyerRemark" || key == "SellerRemark")
                        {
                            var arr = val.Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                            val = arr.Length == 0 ? "" : val;
                        }
                        else if (key == "Status")
                        {
                            if (val == "1")
                                val = "已打印";
                            else if (val == "2")
                                val = "已回收";
                            else if (val == "3")
                                val = "已发货";
                            else if (val == "4")
                                val = "回收失败";
                            else
                                val = "未知状态";
                        }
                        //发货类型：0=正常；1=补发；2=换货；3=变更单号
                        //简化为：首次发货和二次发货
                        else if (key == "SendType")
                        {
                            if (val == "" || val == "0")
                                val = "首次发货";
                            else
                                val = "二次发货";
                        }
                        else if (key == "ShopName")
                        {
                            var agent = agentUsers?.FirstOrDefault(x => x.FxUserId == model.UpFxUserId);
                            val = agent == null ? agentBindShops.FirstOrDefault(x => x.Id == model.SourceShopId)?.NickName ?? "店铺或商家已解绑" : agent.AgentMobileAndRemark;
                        }
                        else if (key == "OrderId")
                        {
                            var orderIds = dic["OrderIdJoin"].ToString2().SplitToList(",");
                            if (orderIds != null && orderIds.Count > 1)
                            {
                                // 合单
                                var pids = wOrderKvs.Where(x => orderIds.Contains(x.Key)).Select(x => x.Value.CustomerOrderId).Distinct().ToList();
                                val = pids?.ToStringWithSplit("\n");
                            }
                            else
                            {
                                var oid = orderIds?.FirstOrDefault() ?? "";
                                val = wOrderKvs.FirstOrDefault(x => x.Key == oid).Value?.CustomerOrderId;
                                if (val.IsNullOrEmpty())
                                    val = dic["CustomerOrderId"].ToString2();
                            }
                        }
                        else if (key == "ExpressWayBillCode")
                        {
                            var childWaybillCode = dic["ChildWaybillCode"].ToString2();
                            val = $"{val}{(childWaybillCode.IsNullOrEmpty() ? "" : $"/{childWaybillCode}")}";
                        }
                    }

                    row[item.Text] = val;
                }
                dt.Rows.Add(row);
            });
        }

        #endregion

        #region 订单分发-发货订单导出
        /// <summary>
        /// 转为发货明细导出模型，带有达人字段权限判断
        /// </summary>
        /// <param name="task"></param>
        /// <param name="data"></param>
        /// <param name="totalCount"></param>
        /// <param name="sendHistorys"></param>
        /// <param name="setting"></param>
        public static void FxSendOrderToDataTable(ExportTask task, DataPackage data, int totalCount, List<SendHistoryModel> sendHistorys, ExportSetting setting)
        {
            var dt = data.Model.Table;
            //var curShop = SiteContext.Current.CurrentLoginShop;
            //var curFxUser = SiteContext.Current.CurrentFxUser;
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();
                    setting.CheckedItems.RemoveAt(headIndex);
                    setting.CheckedItems.InsertRange(headIndex, addrLst);
                }
            }
            // 发货订单商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 子订单导出内容
            var childOrderChkItems = setting.CheckedItems.Where(m => m.From == "child").ToList();
            // 主订单导出内容
            var mainOrderChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "child" && m.From != "productMerge").ToList();

            //获取列表头起始位置
            var headDic = new Dictionary<string, int>();
            for (int i = 0; i < setting.CheckedItems.Count; i++)
            {
                if (!headDic.ContainsKey(setting.CheckedItems[i].Text))
                    headDic.Add(setting.CheckedItems[i].Text, i);
            }

            #region 收件人信息脱敏
            //EncryptionService DataMaskservice = new EncryptionService();
            //var noVirtualOrders = DataMaskservice.getPlatformType(sendHistorys);
            //EncryptionService.DataMaskingExpression(sendHistorys.Where(w=> noVirtualOrders.Any(a=> a.LogicOrderId == w.SendOrderId ))?.ToList());//EncryptionService.DataMaskingReflection(sendHistorys);

            var notEncryptOrders = sendHistorys.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.ReciverPhone?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
            notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
            if (notEncryptOrders.Any())
                EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);

            #endregion

            #region 组装DataTable

            // 第二行开始填充Excel内容
            var shIndex = (task.PageIndex - 2) * task.PageSize + 1; // 前面查询后已经task.PageIndex++
            var index = dt.Rows.Count;
            var isAddrMerge = true;
            sendHistorys.ForEach(s =>
            {
                #region 填充主发货订单的内容

                var row = dt.NewRow();
                dt.Rows.Add(row);
                // 发货订单，以及订单商品合并行
                var mainProductCount = s.SendOrders.Sum(x => x.SendProducts.Count());
                if (mainProductCount > 1 && mainOrderChkItems.Count > 0)
                {
                    // 主订单合并行
                    mainOrderChkItems.ForEach(chkItem =>
                    {
                        #region 商品信息显示在多行(合并行)，即订单信息多行合并，商品信息多行显示 记录合并坐标
                        if (setting.Export_ProductShowStyle == "Export_ShowMutilLine_Merge" && mainProductCount > 1 && headDic.ContainsKey(chkItem.Text))
                        {
                            var beginCol = headDic[chkItem.Text];
                            var endCol = beginCol;
                            var beginRowIndex = totalCount + dt.Rows.Count + 1;//表头占1行，从2行开始
                            var endRowIndex = beginRowIndex + mainProductCount - 1;
                            data.Model.MergeInfos.Add(new MergeCellRangeAddress { BeginCol = beginCol, BeginRowIndex = beginRowIndex, EndCol = endCol, EndRowIndex = endRowIndex });
                        }
                        #endregion
                    });
                }

                var platformOrderIds = s.SendOrders.Select(x => x.PlatformOrderId).Distinct().ToList();
                mainOrderChkItems.ForEach(chkItem =>
                {
                    var field = chkItem.Value.Replace("Export_", "");
                    var text = chkItem.Text;
                    var from = chkItem.From;

                    //// 收件人地址不合并1列显示，则移除收件人地址之后列往后移动3列
                    //var cellIndex = setting.CheckedItems.IndexOf(chkItem);
                    //if (!isAddrMerge)
                    //    cellIndex = cellIndex + 3;

                    if (field == "Index")
                        row[text] = shIndex;
                    else if (field == "MainPlatformOrderId")
                        row[text] = string.Join("\r\n", platformOrderIds);
                    else if (field == "ShopName")
					{
                        if (!string.IsNullOrWhiteSpace(s.AgentShopName))
                        {
                            row[text] = s.AgentShopName;
                        }
                        else
                        {
                            row[text] = s.AgentName;
                        }
                        
                    }
                        
                    else if (field == "SupplierName")
                        row[text] = s.SupplierName;
                    else if (field == "ToAddress")
                    {
                        // 收件人地址判断省市区地址是否合并一列显示
                        if (setting.Export_AddressShowStyle == "Export_Address_Merge")
                            row[text] = s.ReciverAddress;// 省市区地址在同列显示
                        else
                        {
                            // 省市区地址在不同列显示
                            row["收件省"] = s.ToProvince.ToString2();
                            row["收件市"] = s.ToCity.ToString2();
                            row["收件区"] = s.ToDistrict.ToString2();
                            var addr = s.ReciverAddress.Replace($"{s.ToProvince}{s.ToCity}{s.ToDistrict}", "");
                            row["收件详细地址"] = addr;
                            isAddrMerge = false;
                        }
                    }
                    else if (field == "PlatformType")
                        row[text] = s.PlatformType.ToString2();
                    else if (field == "LastSendTime")
                        row[text] = s.SendDate.ToString2();
                    else if (field == "ToPhone")
                        row[text] = s.ReciverPhone.ToString2();
                    else if (field == "ToName")
                        row[text] = s.Reciver.ToString2();
                    else if (field == "SenderCompany")
                        row[text] = s.ExpressName.ToString2();
                    else if (field == "LastWaybillCode")
                        row[text] = s.LogistiscBillNo;
                    else if (field == "WbyStatus")
                        row[text] = s.Status == 2 ? "回收" : "已发货";
                    else if (field == "PackageStatus")
                        row[text] = "已交运";
                });

                #endregion

                s.SendOrders.ForEach(o =>
                {
                    #region 填充子发货订单内容

                    // 子订单合并行
                    if (o.SendProducts.Count > 1 && childOrderChkItems.Count > 0)
                    {
                        childOrderChkItems.ForEach(chkItem =>
                        {
                            if (headDic.ContainsKey(chkItem.Text))
                            {
                                var beginCol = headDic[chkItem.Text];
                                var endCol = beginCol;
                                var beginRowIndex = totalCount + dt.Rows.Count + 1;//表头占1行，从2行开始
                                var endRowIndex = beginRowIndex + o.SendProducts.Count - 1;
                                data.Model.MergeInfos.Add(new MergeCellRangeAddress { BeginCol = beginCol, BeginRowIndex = beginRowIndex, EndCol = endCol, EndRowIndex = endRowIndex });
                            }
                        });
                    }

                    DataRow childRow = null;
                    if (dt.Rows.Count > index)
                        childRow = dt.Rows[index];
                    else
                    {
                        childRow = dt.NewRow();
                        dt.Rows.Add(childRow);
                    }
                    childOrderChkItems.ForEach(chkItem =>
                    {
                        var field = chkItem.Value.Replace("Export_", "");
                        var text = chkItem.Text;
                        var from = chkItem.From;
                        //var cellIndex = setting.CheckedItems.IndexOf(chkItem);

                        #region 子订单内容

                        if (field == "PlatformOrderId")
                            childRow[text] = o.PlatformOrderId;
                        else if (field == "CreateTime")
                            childRow[text] = o.CreateTime;
                        else if (field == "PayTime")
                            childRow[text] = o.PayTime;
                        else if (field == "SendType")
                        {
                            var txt = "其他";
                            if (o.SendType == 0)
                                txt = "首次发货";
                            else if (o.SendType == 1)
                                txt = "补发货";
                            else if (o.SendType == 2)
                                txt = "换货";
                            else if (o.SendType == 3)
                                txt = "变更单号";
                            else if (o.SendType == 99)
                                txt = "其他";
                            childRow[text] = txt;
                        }
                        else if (field == "OrderType")
                            childRow[text] = o.IsPartSend ? "部分发货" : "整单发货";
                        else if (field == "ShippingFee" && task.PlatformType.ToString2().Equals(PlatformType.TouTiao.ToString()))
                            childRow[text] = s.IsShowSalePrice ? o.ShippingFee.ToString2() : "无权限";
                        else if (field == "PlatformSubsidy" && task.PlatformType.ToString2().Equals(PlatformType.TouTiao.ToString()))
                            childRow[text] = s.IsShowSalePrice ? o.PlatformSubsidy.ToString2() : "无权限";
                        else if (field == "PayTotalAmount" && task.PlatformType.ToString2().Equals(PlatformType.TouTiao.ToString()))
                            childRow[text] = s.IsShowSalePrice ? o.PayTotalAmount.ToString2() : "无权限";
                        #endregion
                    });
                    #endregion

                    o.SendProducts.ForEach(p =>
                    {
                        //只有商家自己可见达人字段
                        if (s.UpFxUserId != 0)
                        {
                            p.AuthorId = "*";
                            p.AuthorName = "*";
                        }
                        #region 填充发货订单商品内容
                        DataRow productRow = null;
                        if (dt.Rows.Count > index)
                            productRow = dt.Rows[index];
                        else
                        {
                            productRow = dt.NewRow();
                            dt.Rows.Add(productRow);
                        }

                        productChkItems.ForEach(chkItem =>
                        {
                            var field = chkItem.Value.Replace("Export_", "");
                            var text = chkItem.Text;
                            var from = chkItem.From;
                            //var cellIndex = setting.CheckedItems.IndexOf(chkItem);
                            var val = string.Empty;
                            if (field == "SkuAttr")
                                val = $"{p.Color.ToString2()} {p.Size.ToString2()}_{p.SkuId}";
                            else if (field == "ProductStatus")
                            {
                                var status = string.Empty;
                                if (p.Status == OrderStatusType.waitsellersend.ToString())
                                    status = "待发货";
                                else if (p.Status == OrderStatusType.waitbuyerreceive.ToString())
                                    status = "待收货";
                                else if (p.Status == OrderStatusType.success.ToString())
                                    status = "交易成功";
                                else if (p.Status == "close" || p.Status == OrderStatusType.cancel.ToString())
                                    status = "交易取消";
                                else
                                    status = "未知";

                                var refundStatus = string.Empty;
                                if (p.RefundStatus == RefundStatusType.REFUND_SUCCESS.ToString())
                                    refundStatus = "退款成功";
                                else if (p.RefundStatus == RefundStatusType.REFUND_CLOSE.ToString())
                                    refundStatus = "退款关闭";
                                else if (p.RefundStatus == RefundStatusType.WAIT_SELLER_AGREE.ToString())
                                    refundStatus = "退款中";

                                if (p.Status == "locked")
                                    val = "待发货 退款中";
                                else
                                    val = status + " " + refundStatus;
                            }
                            else if (field == "Count")
                                val = $"{p.Quantity.ToString2()}";
                            else if (field == "Price")
                                val = s.IsShowSalePrice ? $"{p.Price.ToString2()}" : "";
                            else if (field == "UpFxUserSettlementPrice")
                            {
                                if (p.UpFxUserSettlementPrice == -99)
                                    val = "**";
                                else val = p.UpFxUserSettlementPrice == 0 ? $"/" : p.UpFxUserSettlementPrice.ToString2();
                            }
                            else if (field == "DownFxUserSettlementPrice")
                            {
                                if (p.DownFxUserSettlementPrice == -99)
                                    val = "**";
                                else val = p.DownFxUserSettlementPrice == 0 ? $"/" : p.DownFxUserSettlementPrice.ToString2();
                            }
                            else if (field == "AuthorUpSettlementPrice")
                            {
                                if (p.AuthorUpSettlementPrice == -99)
                                    val = "**";
                                else val = p.AuthorUpSettlementPrice == 0 ? $"/" : p.AuthorUpSettlementPrice.ToString2();
                            }
                            else if (field == "AuthorDownSettlementPrice")
                            {
                                if (p.AuthorDownSettlementPrice == -99)
                                    val = "**";
                                else val = p.AuthorDownSettlementPrice == 0 ? $"/" : p.AuthorDownSettlementPrice.ToString2();
                            }
                            else if (field == "Weight" || field == "SkuWeight")
                                val = p.Weight.ToString2() == "0" ? "" : p.Weight.ToString2();
                            else if (field == "productCargoNumber")
                                val = p.ProductCargoNumber;
                            else if (field == "CargoNumber")
                                val = p.CargoNumber;
                            else if (field == "SkuId")
                                val = p.SkuId;
                            else if (field == "ShortTitle")
                                val = p.ShortTitle;
                            else if (field == "SkuShortTitle")
                                val = p.SkuShortTitle;
                            else if (field == "SkuWeight")
                                val = p.SkuWeight.ToString2() == "0" ? "" : p.SkuWeight.ToString2();
                            else if (field == "ProductSubject")
                            {
                                string subject = p.ProductSubject.ToString2();
                                if (string.IsNullOrWhiteSpace(subject) && s.IsShowProductTitle == false)
                                {
                                    subject = "合作方已设为隐藏信息";
                                }
                                val = $"{subject}_{p.ProductId}"; 
                            }
                            else if (field == "AuthorId")
                                val = p.AuthorId.ToString2();
                            else if (field == "AuthorName")
                                val = p.AuthorName.ToString2();

                            productRow[text] = val;
                        });

                        #endregion

                        index++;
                    });
                });
                shIndex++;
            });
            #endregion
        }

        #endregion
    }
}
