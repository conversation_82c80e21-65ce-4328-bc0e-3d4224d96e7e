using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Repository;
using Top.Api;
using Top.Api.Request;
using Top.Api.Response;
using DianGuanJiaApp.Data.Entity;
using System.Configuration;
using System.Collections;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.WaybillService;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json.Linq;
using DianGuanJiaApp.Utility;
using Newtonsoft.Json.Converters;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Model;
using System.Web;
using DianGuanJiaApp.Data.FxModel;
using System.Collections.Concurrent;
using System.Web.UI;
using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Services.Services.SettingsService;
using Log = DianGuanJiaApp.Utility.Log;
using System.Web.UI.WebControls;

namespace DianGuanJiaApp.Services
{
    public partial class CommService
    {
        //public string TaobaoAccountAppKey = ConfigurationManager.AppSettings["Taobao:AppKey"];
        //public string TaobaoAccountAppSecret = ConfigurationManager.AppSettings["Taobao:AppSecret"];
        //public string TaobaoAccountAppKey2 = ConfigurationManager.AppSettings["Taobao:AppKey2"];
        //public string TaobaoAccountAppSecret2 = ConfigurationManager.AppSettings["Taobao:AppSecret2"];
        //public string TaobaoAccountUrls = ConfigurationManager.AppSettings["Taobao:ApiUrl"];

        //public string CainiaoAppKey = CustomerConfig.CaiNiaoAppKey;
        //public string CainiaoAppSecret = CustomerConfig.CaiNiaoAppSecret;
        //public string CainiaoUrls = CustomerConfig.CaiNiaoDailyUrl;

        //public string PinduoduoAppKey = ConfigurationManager.AppSettings["Pinduoduo:AppKey"];
        //public string PinduoduoAppSecret = ConfigurationManager.AppSettings["Pinduoduo:AppSecret"];
        //public string PinduoduoUrls = "";

        CaiNiaoAuthInfoService caiNiaoAuthInfoService = null;
        ExpressCompanyService expressCompanyService = null;
        PrintTemplateService printTemplateService = null;
        BusinessSettingsService businessSettingsService;

        /// <summary>
        /// GetAccountAuthInfoList 方法的锁
        /// </summary>
        public static object _lockByGetAccountAuthInfoList = new object();

		public CommService()
        {
            caiNiaoAuthInfoService = new CaiNiaoAuthInfoService();
            expressCompanyService = new ExpressCompanyService();
            printTemplateService = new PrintTemplateService(this);
            businessSettingsService = new BusinessSettingsService();
        }

        public string TransformValue(object values)
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(values, Newtonsoft.Json.Formatting.Indented, new IsoDateTimeConverter() { DateTimeFormat = "yyyy-MM-dd HH:mm:ss" });
        }

        //授权的账号列表及网点信息
        public List<WayBillAuthConfig> GetAccountAuthInfoList(List<int> shopIds, bool isAll)
        {
            var newWayBillAuthConfigList = new ConcurrentBag<WayBillAuthConfig>();
            var wbcList = GetWayBillAccountList(shopIds, isAll, false);
            //系统快递公司code有重复的,需要去重一下（芝麻开门、丹鸟）
            var oldexpList = expressCompanyService.GetExpressCompay();
            var expList = oldexpList.GroupBy(x => x.CompanyCode).Select(x => x.First()).ToList();

            //ArrayList list = new ArrayList();

            wbcList.AsParallel().WithDegreeOfParallelism(5).ForAll(wbc =>
            {
                var concurrentBranchAddressList = new ConcurrentBag<BranchAddressList>();
                if (wbc.Types?.ToLower() == "taobao")
                {
                    List<BranchAddress> listBranch = new List<BranchAddress>();
                    try
                    {
                        listBranch = TopCaiNiaoApiService.GetTaoBaoBranchAddressList(wbc, null);
                    }
                    catch { }


                    foreach (ExpressCompany ec in expList)
                    {
                        var ecCpcodeMappingList = ec.ExpressCpCodeMappingList.Where(f => f.PlatformType == "Top");
                        List<BranchAddress> listBranchNew = GetBranchListByExpress(ec.CompanyCode, ecCpcodeMappingList, listBranch);//listBranch.Where(t => t.CpCode == ec.CompanyCode || ecCpcodeMappingList.Any(f => f.CpCode == t.CpCode)).ToList();
                        if (listBranchNew.Count > 0)
                        {
                            BranchAddressList branchL = new BranchAddressList();
                            branchL.CompanyName = ec.Names;
                            branchL.BranchAddress = listBranchNew;

                            concurrentBranchAddressList.Add(branchL);
                        }
                    }
                }
                else if (wbc.Types.ToLower() == "link")
                {
                    List<BranchAddress> listBranch = new List<BranchAddress>();
                    var cloudCaiNiaoApiService = new CloudCaiNiaoApiService(wbc);

                    listBranch = cloudCaiNiaoApiService.GetCaiNiaoBranchAddressList();
                    foreach (ExpressCompany ec in expList)
                    {
                        List<BranchAddress> listBranchNew = listBranch.Where(t => t.CpCode == ec.CompanyCode).ToList();
                        if (listBranchNew.Count > 0)
                        {
                            BranchAddressList branchL = new BranchAddressList();
                            branchL.CompanyName = ec.Names;
                            branchL.BranchAddress = listBranchNew;

                            concurrentBranchAddressList.Add(branchL);
                        }
                    }
                }
                else if (wbc.Types.ToLower() == "pinduoduo" || wbc.Types.ToLower() == "pddwaybill")
                {
                    PddWaybillApiService pddWaybillApiService = null;
                    lock (_lockByGetAccountAuthInfoList)
                    {
						pddWaybillApiService = new PddWaybillApiService(wbc);
					} 

                    List<BranchAddress> listBranch = new List<BranchAddress>();
                    try
                    {
                        listBranch = pddWaybillApiService.GetPinduoduoBranchAddressList();
                    }
                    catch { }

                    var exressCpCodeList = expList.SelectMany(f => f.ExpressCpCodeMappingList.Where(p => p.PlatformType == "Pdd")).ToList();
                    foreach (var ec in exressCpCodeList)
                    {
                        List<BranchAddress> listBranchNew = listBranch.Where(t => t.CpCode == ec.CpCode).ToList();
                        if (listBranchNew.Count > 0)
                        {
                            BranchAddressList branchL = new BranchAddressList();
                            branchL.CompanyName = ec.PlatformExpressName;
                            branchL.BranchAddress = listBranchNew;

                            concurrentBranchAddressList.Add(branchL);
                        }
                    }
                }
                else if (wbc.Types.ToLower() == "jingdong")
                {
                    WuJieWaybillApiService wujieWaybillApiService = null;
                    lock (_lockByGetAccountAuthInfoList)
                    {
						wujieWaybillApiService = new WuJieWaybillApiService(wbc);
					}

                    List<BranchAddress> listBranch = new List<BranchAddress>();
                    try
                    {
                        listBranch = wujieWaybillApiService.GetJingDongBranchAddressList();
                    }
                    catch { }

                    var exressCpCodeList = expList.SelectMany(f => f.ExpressCpCodeMappingList.Where(p => p.PlatformType == "JDWJ")).ToList();
                    //筛选出京东快递
                    exressCpCodeList.AddRange(expList.SelectMany(f => f.ExpressCpCodeMappingList.Where(p => p.PlatformType == "JD")).ToList());
                    foreach (var ec in exressCpCodeList)
                    {
                        List<BranchAddress> listBranchNew = listBranch.Where(t => t.CpCode == ec.CpCode).ToList();
                        //将京东快递编码JD使用BDB筛选网点数据，因为京东快递的编码是BDB(250213)
                        if (ec.CpCode == "JD")
                        {
                            listBranchNew = listBranch.Where(t => t.CpCode == "BDB").ToList();
                        }
                        if (listBranchNew.Count > 0)
                        {
                            BranchAddressList branchL = new BranchAddressList();
                            branchL.CompanyName = ec.PlatformExpressName;
                            branchL.BranchAddress = listBranchNew;

                            concurrentBranchAddressList.Add(branchL);
                        }
                    }
                }
                else if (wbc.Types.ToLower() == "toutiao")
                {
                    var exressCpCodeList = expList.SelectMany(f => f.ExpressCpCodeMappingList.Where(p => p.PlatformType == "TouTiao")).ToList();
                    try
                    {
                        var logid = "";
                        var toutiaoWaybillApiService = new TouTiaoWaybillApiService(wbc);
                        var listBranch = toutiaoWaybillApiService.GetBranchAddressList("", out logid);
                        listBranch?.GroupBy(x => x.CpCode)?.ToList()?.ForEach(brs =>
                        {
                            BranchAddressList branchL = new BranchAddressList();
                            var ec = exressCpCodeList.FirstOrDefault(x => x.CpCode == brs.Key);
                            if (ec != null)
                            {
                                branchL.CompanyName = ec.PlatformExpressName;
                                branchL.BranchAddress = brs.ToList();
                                concurrentBranchAddressList.Add(branchL);
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.Message);
                    }
                }
                else if (wbc.Types.ToLower() == "toutiaosaleshop")
                {
                    var expressCpCodeList = expList.SelectMany(f => f.ExpressCpCodeMappingList.Where(p => p.PlatformType == "TouTiao")).ToList();
                    try
                    {
                        var logid = "";
                        var toutiaoWaybillApiService = new TouTiaoWaybillApiService(wbc);
                        var listBranch = toutiaoWaybillApiService.GetBranchAddressList("", out logid);
                        listBranch?.GroupBy(x => x.CpCode)?.ToList()?.ForEach(brs =>
                        {
                            BranchAddressList branchL = new BranchAddressList();
                            var ec = expressCpCodeList.FirstOrDefault(x => x.CpCode == brs.Key);
                            if (ec != null)
                            {
                                branchL.CompanyName = ec.PlatformExpressName;
                                branchL.BranchAddress = brs.ToList();
                                concurrentBranchAddressList.Add(branchL);
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.Message);
                    }
                }
                else if (wbc.Types.ToLower() == PlatformType.KuaiShou.ToString().ToLower())
                {
                    KsWaybillApiService ksWaybillApiService = null;
                    lock (_lockByGetAccountAuthInfoList)
                    {
						ksWaybillApiService = new KsWaybillApiService(wbc);
					}

					List<BranchAddress> listBranch = new List<BranchAddress>();
                    try
                    {
                        listBranch = ksWaybillApiService.GetBranchAddressList();
                    }
                    catch (Exception ex)
                    {
                        Utility.Log.WriteError($"快手电子面单获取订购网点失败:" + ex.Message);
                        //throw ex;
                    }
                    if (listBranch.Any())
                    {
                        foreach (ExpressCompany ec in expList)
                        {
                            var ecCpCodeMapping = ec.ExpressCpCodeMappingList.Where(p => p.PlatformType == "KuaiShou");
                            List<BranchAddress> listBranchNew = listBranch.Where(t => ecCpCodeMapping.Any(x => x.CpCode == t.CpCode)).ToList();
                            if (listBranchNew.Count > 0)
                            {
                                BranchAddressList branchL = new BranchAddressList();
                                branchL.CompanyName = ec.Names;
                                branchL.BranchAddress = listBranchNew;

                                concurrentBranchAddressList.Add(branchL);
                            }
                        }
                    }
                }
                else if (wbc.Types.ToLower() == PlatformType.XiaoHongShu.ToString().ToLower())
                {
                    XiaoHongShuWaybillApiService xhsWaybillApiService = null;
                    lock (_lockByGetAccountAuthInfoList)
                    {
						xhsWaybillApiService = new XiaoHongShuWaybillApiService(wbc);
					}

                    var listBranch = new List<BranchAddress>();
                    try
                    {
                        listBranch = xhsWaybillApiService.GetBranchAddressList();
                    }
                    catch (Exception ex)
                    {
                        Utility.Log.WriteError($"小红书电子面单获取订购网点失败:" + ex.Message);
                        //throw ex;
                    }
                    if (listBranch.Any())
                    {
                        var wayBillAuthConfig = wbc.ToJson().ToObject<WayBillAuthConfig>();
                        wayBillAuthConfig.BranchAddressList = new List<BranchAddressList>();

                        foreach (var ec in expList)
                        {
                            var ecCpCodes = new HashSet<string>(ec.ExpressCpCodeMappingList
                                .Where(p => p.PlatformType == "XiaoHongShu")
                                .Select(m => m.CpCode));

                            var listBranchNew = listBranch
                                .Where(t => ecCpCodes.Contains(t.CpCode) && !t.IsNewBillVersion)
                                .ToList();

                            if (listBranchNew.Any())
                            {
                                var branchAddressList = new BranchAddressList
                                {
                                    CompanyName = ec.Names,
                                    BranchAddress = listBranchNew
                                };
                                concurrentBranchAddressList.Add(branchAddressList); 
                            }

                            var listBranchNewV2 = listBranch
                                .Where(t => ecCpCodes.Contains(t.CpCode) && t.IsNewBillVersion)
                                .ToList();

                            if (!listBranchNewV2.Any())
                            {
                                continue;
                            }
                            
                            var branchAddressListV2 = new BranchAddressList
                            {
                                CompanyName = ec.Names,
                                BranchAddress = listBranchNewV2
                            };
                            wayBillAuthConfig.BranchAddressList.Add(branchAddressListV2);
                        }

                        if (wayBillAuthConfig.BranchAddressList.Any())
                        {
                            wayBillAuthConfig.BillVersion = 2;
                            newWayBillAuthConfigList.Add(wayBillAuthConfig);
                        }

                    }
                }
                else if (wbc.Types.ToLower() == PlatformType.WxVideo.ToString().ToLower())
                {
					///0829注：微信小店与视频号接口目前没有什么变化
					WxVideoWaybillApiService xhsWaybillApiService = null;
                    lock (_lockByGetAccountAuthInfoList)
                    {
						xhsWaybillApiService = new WxVideoWaybillApiService(wbc);
					}

                    List<BranchAddress> listBranch = new List<BranchAddress>();
                    try
                    {
                        listBranch = xhsWaybillApiService.GetBranchAddressList();
                    }
                    catch (Exception ex)
                    {
                        Utility.Log.WriteError($"视频号（微信小店）电子面单获取订购网点失败:" + ex);
                        //throw ex;
                    }
                    if (listBranch.Any())
                    {
                        foreach (ExpressCompany ec in expList)
                        {
                            var ecCpCodeMapping = ec.ExpressCpCodeMappingList.Where(p => p.PlatformType == "WxVideo");
                            List<BranchAddress> listBranchNew = listBranch.Where(t => ecCpCodeMapping.Any(x => x.CpCode == t.CpCode)).ToList();
                            if (listBranchNew.Count > 0)
                            {
                                BranchAddressList branchL = new BranchAddressList();
                                branchL.CompanyName = ec.Names;
                                branchL.BranchAddress = listBranchNew;

                                concurrentBranchAddressList.Add(branchL);
                            }
                        }
                    }
                }

                wbc.BranchAddressList = concurrentBranchAddressList.ToList();
            });
            wbcList.AddRange(newWayBillAuthConfigList);

            return wbcList.OrderByDescending(item=>item.Types).ToList();
        }

        /// <summary>
        /// 获取快递开通的网点信息
        /// </summary>
        /// <returns></returns>
        private List<BranchAddress> GetBranchListByExpress(string companyCode, IEnumerable<ExpressCpCodeMapping> cpCodeMapping, List<BranchAddress> listBranch)
        {
            List<BranchAddress> listBranchNew = new List<BranchAddress>();
            //顺丰（旧的链路包含了：顺丰速递，顺丰快运，丰网速运,三个快递对应的CpCode都是SF），需额外处理
            if (companyCode == "SF" || companyCode == "SFKY" || companyCode == "FENGWANG") //cpCodeMapping.Any(f => f.CpCode == "SF")
            {
                switch (companyCode)
                {
                    case "SF":
                        listBranchNew = listBranch.Where(t => t.CpCode == "SF" && t.BrandCode == "SF").ToList();
                        break;
                    case "SFKY":
                        listBranchNew = listBranch.Where(t => t.CpCode == "SF" && t.BrandCode == "FOP").ToList();
                        break;
                    case "FENGWANG":
                        // 2022-06-22:丰网速运（LE09252050）以独立cp入驻
                        listBranchNew = listBranch.Where(t => t.CpCode == "SF" && t.BrandCode == "FW" || t.CpCode == "LE09252050").ToList();
                        break;
                }
            }
            else
            {
                listBranchNew = listBranch.Where(t => t.CpCode == companyCode || cpCodeMapping.Any(f => f.CpCode == t.CpCode)).ToList();
            }
            return listBranchNew;
        }

        //微商小程序
        public List<WayBillAuthConfig> GetWsXcxAccountAuthInfoList(List<int> shopIds, bool isAll)
        {
            List<WayBillAuthConfig> wbcList = GetWayBillAccountList(shopIds, isAll);
            List<ExpressCompany> expList = expressCompanyService.GetExpressCompay();
            foreach (WayBillAuthConfig wbc in wbcList)
            {
                wbc.BranchAddressList = new List<BranchAddressList>();
                if (wbc.Types?.ToLower() == "taobao")
                {
                    List<BranchAddress> listBranch = new List<BranchAddress>();
                    try
                    {
                        listBranch = TopCaiNiaoApiService.GetTaoBaoBranchAddressList(wbc, null);
                    }
                    catch { }


                    foreach (ExpressCompany ec in expList)
                    {
                        List<BranchAddress> listBranchNew = listBranch.Where(t => t.CpCode == ec.CompanyCode).ToList();
                        if (listBranchNew.Count > 0)
                        {
                            BranchAddressList branchL = new BranchAddressList();
                            branchL.CompanyName = ec.Names;
                            branchL.BranchAddress = listBranchNew;

                            wbc.BranchAddressList.Add(branchL);
                        }
                    }
                }

            }

            return wbcList;
        }

        /// <summary>
        /// 有电子面单的。同步模板
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="ShopId"></param>
        public void SyncShopAccount(int userId, int ShopId)
        {
            //先同步单号分享的模板，以免下面的面单账号出现异常导致不能同步
            try
            {
                SyncShopAccountByShare(userId, ShopId);//（单号分享），被分享商家没有电子面单，通过分享关系生成模板
            }
            catch (Exception e) { }

            //同步淘宝的电子面单，（店铺、非店铺）
            caiNiaoAuthInfoService.CopyCainiaoAuthOwnerByShopToUser(userId, ShopId, "Taobao", "taobao");
            //同步拼多多的电子面单，（店铺、非店铺）
            caiNiaoAuthInfoService.CopyCainiaoAuthOwnerByShopToUser(userId, ShopId, "Pinduoduo", "PddWaybill");

            List<WayBillAuthConfig> list = GetXcxAccountAuthInfoList(userId, false);

            printTemplateService.SyncXcxCiniaoTemplate(userId, list);//没有则同步
        }

        /// <summary>
        /// 来自分享的商家，没有账号，同步模板
        /// </summary>
        /// <param name="userId">用户id</param>
        /// <param name="ShopId">商家店铺shopid</param>
        public void SyncShopAccountByShare(int userId, int ShopId)
        {
            try
            {
                //删除的分享，模板也相应的隐藏
                var shareListByDeleted = new BranchShareRelationService().GetDeletedListByToId(ShopId);//获取删除的分享
                var idList = shareListByDeleted.Select(t => t.Id).ToList();
                printTemplateService.WxTemplateShareRelationByDeleted(userId, idList);//移除模板
            }
            catch (Exception e) { }

            var shareList = new BranchShareRelationService().GetListByToId(ShopId);
            if (shareList.Count > 0)
                printTemplateService.SyncXcxCiniaoTemplateByShare(userId, shareList);//没有则同步
        }


        public int GetXcxListNumbr(int userId, bool isAll)
        {
            return caiNiaoAuthInfoService.GetXcxListNumbr(userId, isAll);
        }


        /// <summary>
        /// 新版关联的小程序 -- 获取列表
        /// </summary>
        /// <param name="shopIds"></param>
        /// <param name="isAll"></param>
        /// <returns></returns>
        public List<WayBillAuthConfig> GetXcxAccountAuthInfoList(int userId, bool isAll)
        {

            List<CaiNiaoAuthInfo> list = caiNiaoAuthInfoService.GetXcxList(userId, isAll);

            List<WayBillAuthConfig> acountConfigList = new List<WayBillAuthConfig>();

            foreach (CaiNiaoAuthInfo wt in list)
            {
                var wbc = CaiNiaoAuthInfoToWaybillAuthConfig(wt);
                acountConfigList.Add(wbc);
            }

            List<ExpressCompany> expList = expressCompanyService.GetExpressCompay();
            foreach (WayBillAuthConfig wbc in acountConfigList)
            {
                wbc.BranchAddressList = new List<BranchAddressList>();
                if (wbc.Types?.ToLower() == "taobao")
                {
                    List<BranchAddress> listBranch = new List<BranchAddress>();
                    try
                    {
                        listBranch = TopCaiNiaoApiService.GetTaoBaoBranchAddressList(wbc);
                    }
                    catch { }


                    foreach (ExpressCompany ec in expList)
                    {
                        List<BranchAddress> listBranchNew = listBranch.Where(t => t.CpCode == ec.CompanyCode).ToList();
                        if (listBranchNew.Count > 0)
                        {
                            BranchAddressList branchL = new BranchAddressList();
                            branchL.CompanyName = ec.Names;
                            branchL.BranchAddress = listBranchNew;

                            wbc.BranchAddressList.Add(branchL);
                        }
                    }
                }
                else if (wbc.Types.ToLower() == "link")
                {
                    List<BranchAddress> listBranch = new List<BranchAddress>();
                    var cloudCaiNiaoApiService = new CloudCaiNiaoApiService(wbc);

                    listBranch = cloudCaiNiaoApiService.GetCaiNiaoBranchAddressList();
                    foreach (ExpressCompany ec in expList)
                    {
                        List<BranchAddress> listBranchNew = listBranch.Where(t => t.CpCode == ec.CompanyCode).ToList();
                        if (listBranchNew.Count > 0)
                        {
                            BranchAddressList branchL = new BranchAddressList();
                            branchL.CompanyName = ec.Names;
                            branchL.BranchAddress = listBranchNew;

                            wbc.BranchAddressList.Add(branchL);
                        }
                    }
                }
                else if (wbc.Types.ToLower() == "pinduoduo" || wbc.Types.ToLower() == "pddwaybill")
                {
                    var pddWaybillApiService = new PddWaybillApiService(wbc);

                    List<BranchAddress> listBranch = new List<BranchAddress>();
                    try
                    {
                        listBranch = pddWaybillApiService.GetPinduoduoBranchAddressList();
                    }
                    catch { }

                    var exressCpCodeList = expList.SelectMany(f => f.ExpressCpCodeMappingList.Where(p => p.PlatformType == "Pdd")).ToList();
                    foreach (var ec in exressCpCodeList)
                    {
                        List<BranchAddress> listBranchNew = listBranch.Where(t => t.CpCode == ec.CpCode).ToList();
                        if (listBranchNew.Count > 0)
                        {
                            BranchAddressList branchL = new BranchAddressList();
                            branchL.CompanyName = ec.PlatformExpressName;
                            branchL.BranchAddress = listBranchNew;

                            wbc.BranchAddressList.Add(branchL);
                        }
                    }
                }

            }
            return acountConfigList;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="isAll">授权的账号列表,isAll为false是取有效的账号，true是取所有的账号包含已经被移除的</param>
        /// <param name="isLoadShare">是否加载分享的授权账号信息</param>
        /// <returns></returns>
        public List<WayBillAuthConfig> GetWayBillAccountList(List<int> shopIds, bool isAll, bool isLoadShare = true)
        {
            List<CaiNiaoAuthInfo> list = caiNiaoAuthInfoService.GetList(shopIds, isAll, isLoadShare);

            List<WayBillAuthConfig> acountConfigList = new List<WayBillAuthConfig>();

            foreach (CaiNiaoAuthInfo wt in list)
            {
                var wbc = CaiNiaoAuthInfoToWaybillAuthConfig(wt);
                acountConfigList.Add(wbc);
            }

            return acountConfigList;
        }

        /// <summary>
        /// 菜鸟授权电子面到账号 转换为 电子面单接口需要的对象 WayBillAuthConfig
        /// </summary>
        /// <param name="wt"></param>
        /// <returns></returns>
        public WayBillAuthConfig CaiNiaoAuthInfoToWaybillAuthConfig(CaiNiaoAuthInfo wt)
        {
            WayBillAuthConfig wbc = new WayBillAuthConfig();

            if (wt.AuthType.ToLower() == "taobao")
            {
                if (wt.AuthSourceType == 0)
                {
                    wbc.AppKey = CustomerConfig.TaobaAppKey;
                    wbc.AppSecret = CustomerConfig.TaobaoAppSecret;
                    wbc.ApiUrl = CustomerConfig.TaoboApiUrl;
                }
                else
                {
                    wbc.AppKey = CustomerConfig.TaobaAppKey2;
                    wbc.AppSecret = CustomerConfig.TaobaoAppSecret2;
                    wbc.ApiUrl = CustomerConfig.TaoboApiUrl;
                }
            }
            else if (wt.AuthType.ToLower() == "link")
            {
                wbc.AppKey = CustomerConfig.CaiNiaoAppKey;
                wbc.AppSecret = CustomerConfig.CaiNiaoAppSecret;
                wbc.ApiUrl = CustomerConfig.CaiNiaoDailyUrl;
            }
            else if (wt.AuthType.ToLower() == "pinduoduo")
            {
                wbc.AppKey = CustomerConfig.PinduoduoAppKey;
                wbc.AppSecret = CustomerConfig.PinduoduoAppSecret;
                wbc.ApiUrl = "";
            }
            if (wt.AuthType.ToLower() == "pddwaybill")
            {
                wbc.AppKey = CustomerConfig.PddWaybillAppKey;
                wbc.AppSecret = CustomerConfig.PddWaybillAppSecret;
                wbc.ApiUrl = "";
            }
            else if (wt.AuthType.ToLower() == "jingdong")
            {
                wbc.AppKey = CustomerConfig.JingDongAppKey;
                wbc.AppSecret = CustomerConfig.JingDongAppSecret;
                wbc.ApiUrl = string.Empty;
            }
            else if (wt.AuthType.ToLower() == "toutiao")
            {
                if (CustomerConfig.IsTouTiaoWaybillTest)
                {
                    //TODO：抖音电子面单上线时，需改成正式应用key和秘钥
                    wbc.AppKey = "6847386892158125581";//CustomerConfig.TouTiaoAppKey;
                    wbc.AppSecret = "e1afd82e-68de-4b35-a8c8-c9039c432bf2";//CustomerConfig.TouTiaoAppSecret;
                }
                else
                {
                    wbc.AppKey = CustomerConfig.TouTiaoAppKey;
                    wbc.AppSecret = CustomerConfig.TouTiaoAppSecret;

                    #region 注释
                    ////新增逻辑，当授权信息中有指定为打单应用AppKey时，使用指定的AppKey，不需要读取ShopExtension
                    //if (wt.AuthAppKey == CustomerConfig.TouTiaoAppKey)
                    //{
                    //    wbc.AppKey = CustomerConfig.TouTiaoAppKey;
                    //    wbc.AppSecret = CustomerConfig.TouTiaoAppSecret;
                    //}
                    //else if (wt.AuthAppKey == CustomerConfig.TouTiaoFxAppKey)
                    //{
                    //    wbc.AppKey = CustomerConfig.TouTiaoFxAppKey;
                    //    wbc.AppSecret = CustomerConfig.TouTiaoFxAppSecret;
                    //}
                    //else if (wt.AuthAppKey == CustomerConfig.TouTiaoFxNewAppKey)
                    //{
                    //    wbc.AppKey = CustomerConfig.TouTiaoFxNewAppKey;
                    //    wbc.AppSecret = CustomerConfig.TouTiaoFxNewAppSecret;
                    //}
                    //else
                    //{
                    //    if (!CustomerConfig.IsFendanSite)
                    //    {
                    //        wbc.AppKey = CustomerConfig.TouTiaoAppKey;
                    //        wbc.AppSecret = CustomerConfig.TouTiaoAppSecret;
                    //    }
                    //    else
                    //    {
                    //        wbc.AppKey = CustomerConfig.TouTiaoFxNewAppKey;
                    //        wbc.AppSecret = CustomerConfig.TouTiaoFxNewAppSecret;
                    //        if (wt.AuthSourceType == 1 && wt.Id > 0)
                    //        {
                    //            //分享店铺来源打单系统
                    //            var _shop = new ShopService().GetShopAndShopExtension(wt.Id);
                    //            if (_shop != null && _shop.ShopExtension == null && _shop.PlatformType != "System")
                    //            {
                    //                wbc.AppKey = CustomerConfig.TouTiaoAppKey;
                    //                wbc.AppSecret = CustomerConfig.TouTiaoAppSecret;
                    //            }
                    //        }
                    //    }
                    //} 
                    #endregion
                }
                wbc.IsEbillWaybill = wt.IsEbillWaybill;
                wbc.ApiUrl = "https://openapi-fxg.jinritemai.com";
            }
            else if (wt.AuthType.ToLower() == "kuaishou")
            {
                wbc.AppKey = CustomerConfig.KuaiShouAppKey;
                wbc.AppSecret = CustomerConfig.KuaiShouAppSecret;
                wbc.ApiUrl = "https://openapi.kwaixiaodian.com";
            }
            else if (wt.AuthType.ToLower() == "xiaohongshu")
            {
                wbc.AppKey = CustomerConfig.XiaoHongShuAppKey;
                wbc.AppSecret = CustomerConfig.XiaoHongShuAppSecret;
                wbc.ApiUrl = CustomerConfig.XiaoHongShuApiUrl;
                wbc.IsEbillWaybill = wt.IsEbillWaybill;
            }
            else if (wt.AuthType.ToLower() == "wxvideo")
            {
                wbc.AppKey = CustomerConfig.Fx_WxXiaoShangDianAppId;
                wbc.AppSecret = CustomerConfig.Fx_WxXiaoShangDianAppSecret;
                wbc.ApiUrl = "https://api.weixin.qq.com/";
            }
            else if (wt.AuthType.ToLower() == "toutiaosaleshop")
            {
                wbc.AppKey = CustomerConfig.FxTouTiaoSaleShopAppKey;
                wbc.AppSecret = CustomerConfig.FxTouTiaoSaleShopAppSecret;
                wbc.IsEbillWaybill = wt.IsEbillWaybill;
                wbc.ApiUrl = "https://openapi-fxg.jinritemai.com"; 
            }

            wbc.CaiNiaoAuthInfoId = wt.Id;
            wbc.Types = wt.AuthType;
            wbc.SessionKey = wt.AccessToken;
            wbc.UserId = wt.AuthAccountId;
            wbc.Refresh_Token = wt.RefreshToken;
            wbc.UserName = wt.AuthAccountName;
            wbc.AuthSourceType = wt.AuthSourceType;
            wbc.ShopId = wt.ShopId;
            return wbc;
        }


        //public void SiteContextNew(int shopId, int userId = 0)
        //{
        //    try
        //    {
        //        if (userId > 0)
        //            new SiteContext(shopId, userId);
        //        else
        //            new SiteContext(shopId);

        //    }
        //    catch (Exception e)
        //    {
        //        Utility.Log.WriteError($"小程序入口（ShareWayBillAccountController > AppendAccountCountApi）：{e.Message}");
        //    }
        //}


        #region link地址库

        public void GetCndzkChinaSubDivisionsSync()
        {
            //************* 是自己的商家角色账号:cccyyyuuu   
            CaiNiaoAuthInfo cainiaoModel = new CaiNiaoAuthInfoService().GetModel("*************", "link");

            if (cainiaoModel != null)
            {
                var wayBillAuthConfig = CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoModel);

                CloudCaiNiaoApiService _cloudCaiNiaoApiService = new CloudCaiNiaoApiService(wayBillAuthConfig);
                _cloudCaiNiaoApiService.GetCndzkChinaSubDivisions();
            }
        }

        #endregion


        #region 淘宝接口

        public ITopClient GetTopClient()
        {
            var client = new ClusterTopClient(CustomerConfig.TaoboApiUrl, "********", "d0b07cc2d59c70226c22a742585dc1ff");
            return client;
        }


        /// <summary>
        /// 批量判断快递是否可达
        /// </summary>
        /// <param name="addressList">订单ID-地址 键值对</param>
        /// <param name="partnerId">快递公司ID</param>
        /// <returns></returns>
        public List<CheckAddressReachableOrderModel> CheckReachableByApi(List<CheckAddressReachableOrderModel> orders, string companyCode)
        {
            var partnerId = GetTaobaoLogisticCompanyId(companyCode);
            var paged = new List<List<CheckAddressReachableOrderModel>>();
            if (orders.Count > 10)
            {
                var pageCount = Math.Floor(orders.Count / (10 * 1.0));
                for (var i = 0; i < pageCount; i++)
                {
                    var currents = orders.Skip(i * 10).Take(10).ToList();
                    CheckReachableByApiPaged(currents, partnerId);
                }
            }
            else
                CheckReachableByApiPaged(orders, partnerId);
            return orders;
        }

        /// <summary>
        /// 批量操作每次最多十条，分页处理
        /// </summary>
        /// <param name="orders"></param>
        /// <param name="partnerId"></param>
        /// <returns></returns>
        private List<CheckAddressReachableOrderModel> CheckReachableByApiPaged(List<CheckAddressReachableOrderModel> orders, string partnerId)
        {
            //var client = GetTopClient();
            //var req = new LogisticsAddressReachablebatchGetRequest();
            //var list = orders.Select(a => new LogisticsAddressReachablebatchGetRequest.AddressReachableDomain
            //{
            //    Address = a.Address,
            //    PartnerId = partnerId,
            //    ServiceType = 88L
            //}).ToList();
            //req.AddressList_ = list;
            //var rsp = client.Execute(req);
            //var result = new List<KeyValuePair<int, bool>>();
            ////接口请求错误，默认到达
            //if (rsp.IsError)
            //    orders.ForEach(o => o.IsReachable = true);
            //else if (rsp.ReachableResults != null && rsp.ReachableResults.Count > 0)
            //{
            //    for (var i = 0; i < rsp.ReachableResults.Count(); i++)
            //    {
            //        var isReach = false;
            //        var rec = rsp.ReachableResults[i];
            //        if (rec.ReachableResultList != null && rec.ReachableResultList.Count() > 0)
            //            isReach = rec.ReachableResultList[0].Reachable.ToBoolean();
            //        orders[i].IsReachable = isReach;
            //    }
            //}
            return orders;
        }

        public string GetTaobaoLogisticCompanyId(string companyCode)
        {
            var json = "{\"logistics_companies_get_response\":{\"logistics_companies\":{\"logistics_company\":[{\"code\":\"POST\",\"id\":1,\"name\":\"中国邮政\"},{\"code\":\"OTHER\",\"id\":-1,\"name\":\"其他\"},{\"code\":\"AIR\",\"id\":507,\"name\":\"亚风\"},{\"code\":\"CYEXP\",\"id\":511,\"name\":\"长宇\"},{\"code\":\"DTW\",\"id\":512,\"name\":\"大田\"},{\"code\":\"YUD\",\"id\":513,\"name\":\"长发\"},{\"code\":\"DISTRIBUTOR_13211725\",\"id\":1216000000124268,\"name\":\"跨越速运\"},{\"code\":\"DISTRIBUTOR_13222803\",\"id\":1216000000125358,\"name\":\"中通快运\"},{\"code\":\"PKGJWL\",\"id\":21000038002,\"name\":\"派易国际物流77\"},{\"code\":\"DISTRIBUTOR_13148625\",\"id\":6000100034229,\"name\":\"菜鸟大件-中铁配\"},{\"code\":\"DISTRIBUTOR_13159132\",\"id\":6000100034186,\"name\":\"菜鸟大件-日日顺配\"},{\"code\":\"YC\",\"id\":1139,\"name\":\"远长\"},{\"code\":\"DFH\",\"id\":1137,\"name\":\"东方汇\"},{\"code\":\"UNIPS\",\"id\":1237,\"name\":\"发网\"},{\"code\":\"MGSD\",\"id\":21000007003,\"name\":\"美国速递\"},{\"code\":\"WND\",\"id\":21000127009,\"name\":\"WnDirect\"},{\"code\":\"GZLT\",\"id\":200427,\"name\":\"飞远配送 \"},{\"code\":\"BHWL\",\"id\":21000053037,\"name\":\"保宏物流\"},{\"code\":\"DISTRIBUTOR_1710055\",\"id\":5000000178661,\"name\":\"邮政标准快递\"},{\"code\":\"ZJS\",\"id\":103,\"name\":\"宅急送\"},{\"code\":\"SF\",\"id\":505,\"name\":\"顺丰速运\"},{\"code\":\"STO\",\"id\":100,\"name\":\"申通快递\"},{\"code\":\"EMS\",\"id\":2,\"name\":\"EMS\"},{\"code\":\"YUNDA\",\"id\":102,\"name\":\"韵达快递\"},{\"code\":\"ZTO\",\"id\":500,\"name\":\"中通快递\"},{\"code\":\"HTKY\",\"id\":502,\"name\":\"百世快递\"},{\"code\":\"YTO\",\"id\":101,\"name\":\"圆通速递\"},{\"code\":\"QFKD\",\"id\":1216,\"name\":\"全峰快递\"},{\"code\":\"TTKDEX\",\"id\":504,\"name\":\"天天快递\"},{\"code\":\"EYB\",\"id\":3,\"name\":\"EMS经济快递\"},{\"code\":\"UC\",\"id\":1207,\"name\":\"优速快递\"},{\"code\":\"DBKD\",\"id\":5000000110730,\"name\":\"德邦快递\"},{\"code\":\"GTO\",\"id\":200143,\"name\":\"国通快递\"},{\"code\":\"SURE\",\"id\":201174,\"name\":\"速尔快递\"},{\"code\":\"FEDEX\",\"id\":106,\"name\":\"联邦快递\"},{\"code\":\"SHQ\",\"id\":108,\"name\":\"华强物流\"},{\"code\":\"UAPEX\",\"id\":1259,\"name\":\"全一快递\"},{\"code\":\"HOAU\",\"id\":1191,\"name\":\"天地华宇\"},{\"code\":\"BEST\",\"id\":105,\"name\":\"百世物流\"},{\"code\":\"LB\",\"id\":1195,\"name\":\"龙邦速递\"},{\"code\":\"XB\",\"id\":1186,\"name\":\"新邦物流\"},{\"code\":\"FAST\",\"id\":1204,\"name\":\"快捷快递\"},{\"code\":\"POSTB\",\"id\":200734,\"name\":\"邮政快递包裹\"},{\"code\":\"NEDA\",\"id\":1192,\"name\":\"能达速递\"},{\"code\":\"BJRFD-001\",\"id\":100034107,\"name\":\"如风达配送\"},{\"code\":\"DBL\",\"id\":107,\"name\":\"德邦物流\"},{\"code\":\"YCT\",\"id\":1185,\"name\":\"黑猫宅急便\"},{\"code\":\"LTS\",\"id\":1214,\"name\":\"联昊通\"},{\"code\":\"CNEX\",\"id\":1056,\"name\":\"佳吉快递\"},{\"code\":\"HZABC\",\"id\":1121,\"name\":\"飞远(爱彼西)配送\"},{\"code\":\"XFWL\",\"id\":202855,\"name\":\"信丰物流\"},{\"code\":\"ESB\",\"id\":200740,\"name\":\"E速宝\"},{\"code\":\"GDEMS\",\"id\":1269,\"name\":\"广东EMS\"},{\"code\":\"BESTQJT\",\"id\":105031,\"name\":\"百世快运\"},{\"code\":\"QRT\",\"id\":1208,\"name\":\"增益速递\"}]},\"request_id\":\"5w8176tzzs1x\"}}";
            var token = json.ToObject<JToken>();
            var lcs = token?.Value<JToken>("logistics_companies_get_response")?.Value<JToken>("logistics_companies")?.Value<JArray>("logistics_company")?.ToList();
            if (lcs != null && lcs.Any())
                return lcs.FirstOrDefault(l => l.Value<string>("code") == companyCode)?.Value<string>("id");
            return "-1";
        }

        #endregion

        /// <summary>
        /// 头条//TK 打印数据更新签名
        /// </summary>
        /// <param name="dataList"></param>
        public void UpSignPrintHistory(List<PrintHistory> dataList)
        {

            #region 跨境1.0 需要下载相关PDF
            //if (CustomerConfig.IsCrossBorderSite && dataList != null && dataList.Any())
            //{
            //    GetTikTokPrintPdf(dataList);
            //    return;
            //}
            #endregion

            var templateIds = dataList.Where(d =>
                CustomerConfig.IsTouTiaozjTemplate(d.TemplateType) || CustomerConfig.IsTouTiaoKuaiYunTemplate(d.TemplateType)
            ).Select(d => d.TemplateId).Distinct().ToList();
            if (!templateIds.Any())
                return;

            var tempDic = new Dictionary<int, PrintTemplate>();
            templateIds.ForEach(t =>
            {
                var template = printTemplateService.Get(t, false);
                if (template != null && !tempDic.ContainsKey(t))
                    tempDic.Add(t, template);
            });

            Parallel.ForEach(dataList, new ParallelOptions { MaxDegreeOfParallelism = 5 }, d =>
            {
                // 只有抖店的底单记录才需要修改签名
                if (CustomerConfig.IsTouTiaozjTemplate(d.TemplateType) || CustomerConfig.IsTouTiaoKuaiYunTemplate(d.TemplateType))
                {
                    try
                    {
                        if (!d.PrinterJsonData.IsNullOrEmpty() && !CustomerConfig.IsCrossBorderSite)
                        {   ///跨境无需校验模板
                            tempDic.TryGetValue(d.TemplateId, out PrintTemplate template);
                            if (template == null)
                                throw new LogicException($"快递模板【{d.TemplateId}】不存在或已被删除");

                            if (template.PrintTemplateType == PrintTemplateType.TouTiaozjTemplate || template.PrintTemplateType == PrintTemplateType.TouTiaoKuaiYunTemplate)
                            {
                                //授权关系不存在
                                if (template?.CaiNiaoAuthInfo == null)
                                {
                                    var msg = $"{template.TemplateName}的授权账号已失效，请检查模板";
                                    if (template?.TemplateRelationAuthInfo?.BranchShareRelationId > 0)
                                        msg = $"{template.TemplateName}的授权账号分享已失效，请检查模板";
                                    throw new LogicException(msg);
                                }
                                template.WayBillAuthConfig = CaiNiaoAuthInfoToWaybillAuthConfig(template.CaiNiaoAuthInfo);
                                TouTiaoWaybillApiService tiaoWaybillApiService = new TouTiaoWaybillApiService(template.WayBillAuthConfig);

                                var jsonData = d.PrinterJsonData;
                                var paramslengt = "params\":\"".Length;
                                var paramsIndex = jsonData.IndexOf("params\":\"") + paramslengt;
                                var signatureIndex = jsonData.IndexOf("\",\"signature");
                                var length = signatureIndex - paramsIndex;
                                var reStr = jsonData.Substring(paramsIndex, length);

                                //SortedDictionary<string, string> paramDic = new SortedDictionary<string, string>();
                                //var timespan = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                                //paramDic.Add("app_key", template.WayBillAuthConfig.AppKey);
                                //paramDic.Add("method", "logistics.getShopKey");
                                //paramDic.Add("timestamp", timespan);
                                //paramDic.Add("v", "2");
                                //paramDic.Add("param_json", "{}");
                                ////paramDic.Add("access_token", template.WayBillAuthConfig.SessionKey);
                                //string sign = tiaoWaybillApiService._client.Sign(paramDic, template.WayBillAuthConfig.AppSecret);
                                //var params_string = "access_token=" + template.WayBillAuthConfig.SessionKey + "&app_key=" + template.WayBillAuthConfig.AppKey + "&method=logistics.getShopKey&param_json={}&timestamp=" + timespan + "&v=2&sign=" + sign + "";

                                jsonData = jsonData.Replace(reStr, tiaoWaybillApiService.GetShopKey());
                                d.PrinterJsonData = jsonData;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Utility.Log.WriteError($"UpSignPrintHistory异常：{ex}");
                        throw ex;
                    }
                }
            });


        }

        /// <summary>
        /// 获取TK打印记录新的PDF链接以及base64
        /// </summary>
        /// <param name="dataList"></param>
        /// <returns></returns>
        public List<PrintHistory> GetTikTokPrintPdf(List<PrintHistory> dataList)
        {
            try
            {
                // 重新获取PDF链接
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                new PrintHistoryService(fxUserId).GetPrintPdfUrlToTikTok(dataList);
                // 筛选出需要下载和转换的PDF URL
                var pdfUrls = dataList
                    .Where(model => model.Type != "SHIPPING_LABEL_PICTURE"
                        && !string.IsNullOrWhiteSpace(model.PrinterJsonData)
                        && CommUtls.IsValidUrl(model.PrinterJsonData))
                    .Select(model => model.PrinterJsonData)
                    .Distinct()
                    .ToList();

                // 初始化并发字典并将PDF URL添加到字典中
                var keyValuePairs = new ConcurrentDictionary<string, string>();
                foreach (var url in pdfUrls)
                {
                    keyValuePairs.TryAdd(url, null); // 初始化值为null，稍后更新为Base64
                }

                Utility.Net.HttpMethods.DownloadPdfAndConvertToBase64(keyValuePairs, 3);

                // 更新dataList中每个元素的PrinterJsonData属性为Base64数据
                foreach (var history in dataList)
                {
                    if (history.Type == "SHIPPING_LABEL_PICTURE")
                        continue;

                    if (keyValuePairs.TryGetValue(history.PrinterJsonData, out var base64Data) && base64Data != null)
                    {
                        history.PrinterJsonData = base64Data;
                    }
                }
            }
            catch (Exception ex)
            {
                Utility.Log.WriteError($"打印记录：PDF转base64字符失败," + ex.Message);
            }
            return dataList;
        }


        public AccountViewModel GetAccountViewModel(AuthRequetModel model)
        {
            var token = model.token; //当前店铺的token
            var outMasterToken = model.masterToken;
            var newAppUrl = model.newAppUrl;
            var encodeAuhtUrl = model.UrlEncodeAuthUrl;
            var encodeauhtSuccessUrl = model.UrlEncodeAuthSuccessUrl;
            var encodeTaobaoAuthUrl = model.UrlEncodeTaobaoAuthUrl;
            var encodeEntranceUrl = model.UrlEncodeEntranceUrl;

            //var newAppUrl = RequestHost;
            var taobaoAccountRedirectUrl = ConfigurationManager.AppSettings["Taobao:Auth_RedirectUrl"];
            var pinDuoDuoWaybillAcountAuthUrl = "http://testauth.dgjapp.com/fxauth/pdd";// CustomerConfig.PinduoduoOldSystemLink;
            var pinDuoDuoDadanWaybillAcountAuthUrl = CustomerConfig.PinduoduoOldSystemLink;
            var toutiaoWaybillAcountAuthUrl = CustomerConfig.AuthCallbackUrl;
            //if (CustomerConfig.IsDebug)
            //{
            //    toutiaoWaybillAcountAuthUrl = "http://testauth1.dgjapp.com";
            //}
            var authSiteUrl = CustomerConfig.AuthCallbackUrl;
            Shop _shops = SiteContext.Current.CurrentLoginShop;

            //授权给主店铺的链接
            string taobaoAuthPriUrl = "", //top菜鸟
                cainiaoAuthPriUrl = "",  //菜鸟官方
                toutiaoAuthPriUrl = "", //头条
                toutiaoSaleShopAuthPriUrl = "", //抖店即时零售
                pinDuoDuoAuthPriUrl = string.Empty, //pdd分单应用
                pinDuoDuoDdAuthPriUrl = string.Empty, //pdd打单应用
                kuaishouAuthPriUrl = string.Empty,  //快手电子面单;
                xiaohongshuAuthPriUrl = string.Empty, //小红书电子面单
                wxVideoAuthPriUrl = string.Empty; //视频号电子面单

            //授权给当前店铺的链接
            string taobaoAuthUrl = "", //top菜鸟
                cainiaoAuthUrl = "", //菜鸟官方
                toutiaoAuthUrl = "", //头条新应用
                toutiaoOldAuthUrl = "", // 头条旧应用（铺货助手）
                toutiaoSaleShopAuthUrl = "", //抖店即时零售
                pinDuoDuoAuthUrl = string.Empty, //pdd分单应用
                pinDuoDuoDdAuthUrl = string.Empty, //pdd打单应用
                kuaishouAuthUrl = string.Empty,//快手电子面单
                xiaohongshuAuthUrl = string.Empty,//小红书电子面单
                wxVideoAuthUrl = string.Empty;//视频号电子面单

            Shop _masterShop = SiteContext.Current.MasterShop;
            var masterToken = string.Empty;
            if (_masterShop != null && _masterShop.Id != _shops.Id)
            {
                taobaoAuthPriUrl = taobaoAccountRedirectUrl + "tbAuth.aspx?callurl=" + encodeTaobaoAuthUrl + "&SuccToUrl=" + encodeauhtSuccessUrl + "&channel=newprint&print_loginid=" + _shops.ShopId + "&shopid=" + _shops.Id + "&is_pri=true&linktoken=" + token + "&newappurl=" + newAppUrl;
                cainiaoAuthPriUrl = CustomerConfig.CaiNiaoRedirectUrl + "/Auth/CaiNiaoCloud?IsShowSuccess=1&channel=newprint&print_loginid=" + _shops.ShopId + "&shopid=" + _shops.Id + "&is_pri=true&linktoken=" + token + "&newappurl=" + model.UrlEncodeAuthUrl;//+ newAppUrl;

                var masterLoginAuthToken = (new ShopService()).GetTokenByShopId(_masterShop.Id);
                if (masterLoginAuthToken != null)
                {
                    masterToken = DES.EncryptUrl($"{masterLoginAuthToken.Id}", CustomerConfig.LoginCookieEncryptKey);
                }
                {
                    masterToken = outMasterToken;
                }
                pinDuoDuoAuthPriUrl = pinDuoDuoWaybillAcountAuthUrl + "?SuccToUrl=" + encodeauhtSuccessUrl + "&rp=" + masterToken;
                pinDuoDuoDdAuthPriUrl = pinDuoDuoDadanWaybillAcountAuthUrl + "?callurl=" + encodeEntranceUrl + "&SuccToUrl=" + encodeauhtSuccessUrl + "&rp=" + masterToken;
                toutiaoAuthPriUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfxnew?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + masterToken;
                toutiaoOldAuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfx?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + masterToken;
                toutiaoSaleShopAuthPriUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/fxdouyinsaleshop?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + masterToken;
                kuaishouAuthPriUrl = authSiteUrl.TrimEnd('/') + "/auth/kuaishou?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + masterToken;
                xiaohongshuAuthPriUrl = $"{authSiteUrl.TrimEnd('/')}/fxauth/xiaohongshuv2fx?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&authtype=waybill{(model.NeedAddTemplate ? $"&callUrl={newAppUrl}%2Fcommonapi%2Fautoaddtemplate" : "")}&rp={masterToken}";
                //wxVideoAuthPriUrl = authSiteUrl.TrimEnd('/') + "/fxauth/wxvideov2?SuccToUrl=" + encodeauhtSuccessUrl + "&rp=" + masterToken;
                wxVideoAuthPriUrl = "https://channels.weixin.qq.com/shop/servicemarket/myServices?status=2";
            }
            taobaoAuthUrl = taobaoAccountRedirectUrl + "tbAuth.aspx?callurl=" + encodeTaobaoAuthUrl + "&SuccToUrl=" + encodeauhtSuccessUrl + "&channel=newprint&print_loginid=" + _shops.ShopId + "&shopid=" + _shops.Id + "&is_pri=false&linktoken=" + token + "&newappurl=" + newAppUrl;
            cainiaoAuthUrl = CustomerConfig.CaiNiaoRedirectUrl + "/Auth/CaiNiaoCloud?IsShowSuccess=1&channel=newprint&print_loginid=" + _shops.ShopId + "&shopid=" + _shops.Id + "&is_pri=false&linktoken=" + token + "&newappurl=" + model.UrlEncodeAuthUrl;// + newAppUrl;
            pinDuoDuoAuthUrl = pinDuoDuoWaybillAcountAuthUrl + "?SuccToUrl=" + encodeauhtSuccessUrl + "&rp=" + token;
            pinDuoDuoDdAuthUrl = pinDuoDuoDadanWaybillAcountAuthUrl + "?callurl=" + encodeEntranceUrl + "&SuccToUrl=" + encodeauhtSuccessUrl + "&rp=" + token;
            toutiaoAuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfxnew?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + token;
            toutiaoOldAuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/douyinfx?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + masterToken;
            toutiaoSaleShopAuthUrl = toutiaoWaybillAcountAuthUrl.TrimEnd('/') + "/auth/fxdouyinsaleshop?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + token;
            kuaishouAuthUrl = authSiteUrl.TrimEnd('/') + "/fxauth/kuaishou?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&rp=" + token;
            xiaohongshuAuthUrl = $"{authSiteUrl.TrimEnd('/')}/fxauth/xiaohongshuv2fx?SuccToUrl=http%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fauthsuccess&authtype=waybill{(model.NeedAddTemplate ? $"&callUrl={newAppUrl}%2Fcommonapi%2Fautoaddtemplate" : "")}&rp={token}";
            //wxVideoAuthUrl = authSiteUrl.TrimEnd('/') + "/fxauth/wxvideov2?SuccToUrl=" + encodeauhtSuccessUrl + "&rp=" + token;
            wxVideoAuthUrl = "https://channels.weixin.qq.com/shop/servicemarket/myServices?status=2";

            var allShops = new List<Shop>();
            allShops.AddRange(SiteContext.Current.AllShops);
            allShops.Add(SiteContext.Current.MasterShop);

            if (model.NeedDbConfig == false)
            {
                allShops.ForEach(s =>
                {
                    s.DbConfig = null;
                });
            }

            var jdAuthUrl = $"{CustomerConfig.JdAuthCallbackUrl.TrimEnd('/')}/fxauth/jingdong";
            AccountViewModel avm = new AccountViewModel()
            {
                TaobaoAuthPriUrl = taobaoAuthPriUrl,
                TaobaoAuthUrl = taobaoAuthUrl,
                CaiNiaoAuthPriUrl = cainiaoAuthPriUrl,
                CaiNiaoAuthUrl = cainiaoAuthUrl,
                PinduoduoAuthPriUrl = pinDuoDuoAuthPriUrl,
                PinduoduoAuthUrl = pinDuoDuoAuthUrl,

                PinduoduoDdAuthPriUrl = pinDuoDuoDdAuthPriUrl,
                PinduoduoDdAuthUrl = pinDuoDuoDdAuthUrl,

                KuaiShouAuthPriUrl = kuaishouAuthPriUrl,
                KuaiShouAuthUrl = kuaishouAuthUrl,

                ToutiaoAuthPriUrl = toutiaoAuthPriUrl,
                ToutiaoAuthUrl = toutiaoAuthUrl,
                ToutiaoOldAuthUrl = toutiaoOldAuthUrl,
                ToutiaoSaleShopAuthPriUrl = toutiaoSaleShopAuthPriUrl,
                ToutiaoSaleShopAuthUrl = toutiaoSaleShopAuthUrl,

                XiaoHongShuAuthPriUrl = xiaohongshuAuthPriUrl,
                XiaoHongShuAuthUrl = xiaohongshuAuthUrl,

                WxVideoAuthPriUrl = wxVideoAuthPriUrl,
                WxVideoAuthUrl = wxVideoAuthUrl,

                JdAuthUrl = jdAuthUrl,
                MasterToken = masterToken ?? "",
                Token = token,
                AllShops = allShops?.Select(x => new { x.Id, x.NickName, x.ShopId, x.ShopName }).ToList(),
            };

            return avm;
        }


        #region 大促期间逻辑限制
        /// <summary>
        /// 按用户来，如果在白名单内，不做限制
        /// </summary>
        private readonly string FxSystemWhiteFxUserIdKey = "/FxSystem/Limit/WhiteFxUserId";
        /// <summary>
        /// 导出功能限制时间
        /// </summary>
        private readonly string FxSystemExportLimitTimesKey = "/FxSystem/Export/LimitTimes";
        /// <summary>
        /// 对账功能限制时间
        /// </summary>
        private readonly string FxSystemSettlementLimitTimesKey = "/FxSystem/Settlement/LimitTimes";
        /// <summary>
        /// 订单查询功能限制时间
        /// </summary>
        private readonly string FxSystemOrderQueryLimitTimesKey = "/FxSystem/OrderQuery/LimitTimes";
        /// <summary>
        /// 查询限制规则
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="getKeyFunc"></param>
        /// <returns></returns>
        private LimitExecuteCheckModel OnExecuteCheck(int fxUserId, Func<string> getKeyFunc)
        {
            LimitExecuteCheckModel checkModel = null;
            DateTime cuurTime = DateTime.Now;
            var _commonSettingService = new CommonSettingService();
            var isWhite = _commonSettingService.Get(FxSystemWhiteFxUserIdKey, fxUserId);
            if (isWhite == null)
            {
                //获取配置Key
                var key = getKeyFunc();

                var value = _commonSettingService.Get(key, 0)?.Value;
                if (value == null) return checkModel;

                var limits = value.ToObject<List<LimitExecuteCheckModel>>();
                checkModel = limits.FirstOrDefault(l => cuurTime > l.StartTime && cuurTime < l.EndTime);
            }
            return checkModel;
        }

        /// <summary>
        /// 导出功能命中延迟时间操作
        /// </summary>
        /// <param name="fxUserId">白名单</param>
        /// <param name="exportType">导出类型 DianGuanJiaApp.Data.Enum.ExportType</param>
        /// <returns>期望延迟到某时间执行，为空就不做限制</returns>
        public DateTime? ExportExecuteCheck(int fxUserId, int exportType = 0)
        {
            var model = OnExecuteCheck(fxUserId, () =>
            {
                var key = FxSystemExportLimitTimesKey;
                if (exportType == ExportType.ErpSettlement.ToInt() || exportType == ExportType.ErpSettlementV2.ToInt())
                    key = FxSystemSettlementLimitTimesKey;
                return key;
            });
            //命中限制时间段内，执行时间延迟到限制截至时间之后操作
            return model?.EndTime;
        }

        /// <summary>
        /// 查询功能操作命中限制规则
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public LimitExecuteCheckModel QueryExecuteCheck(int fxUserId)
        {
            var model = OnExecuteCheck(fxUserId, () =>
            {
                var key = FxSystemOrderQueryLimitTimesKey;
                return key;
            });
            //命中规则
            return model;
        }

        /// <summary>
        /// 查询功能限制时间段使用规则   
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<LimitExecuteCheckModel> QueryLimitRules(int fxUserId)
        {
            List<LimitExecuteCheckModel> rules = null;
            DateTime cuurTime = DateTime.Now;
            var _commonSettingService = new CommonSettingService();
            var isWhite = _commonSettingService.Get(FxSystemWhiteFxUserIdKey, fxUserId)?.Id;
            if (isWhite > 0)
            {
                //获取配置Key
                var key = FxSystemOrderQueryLimitTimesKey;
                var value = _commonSettingService.Get(key, 0)?.Value;
                if (value == null) return rules;

                rules = value.ToObject<List<LimitExecuteCheckModel>>();
            }
            return rules;
        }

        /// <summary>
        /// 是否命中查询限制规则
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public bool IsHitLimitRule(OrderSearchModel model)
        {
            var erpState = model.Filters.FirstOrDefault(x => x.Name == "ErpState")?.Value;
            //if(erpState == null) return false;    //打印发货列表 全部订单状态没有传参数

            //查询状态为空|以下状态做限制查询
            if (string.IsNullOrWhiteSpace(erpState) || erpState == "success,close" || erpState == "close" || erpState == "success" || erpState == "all")
            {
                if (model.FxPageType == FxPageType.WaitPrint.ToInt())
                {
                    //涉及商品相关排序规则 需要提醒用户
                    var notField = new List<string>() {
                        "o.ProductItemCount",
                        "o.TotalWeight",
                        "oi.Color",
                        "oi.Size" ,
                        "oi.ProductCode",
                        "oi.SkuCode"
                    };
                    if (notField.Contains(model.OrderByField))
                        return true;
                }
                else
                {
                    //除打印发货列表排序都按创建时间降序
                    model.IsOrderDesc = true;
                    model.OrderByField = "o.CreateTime";
                }

                //查询商品表逻辑 需要提醒用户
                var productFilter = model.Filters.FirstOrDefault(x => x.CustomQuery == "ProductIdCheck" || x.CustomQuery == "SkuIdCheck");
                if (productFilter != null)
                {
                    return true;
                }

                //限制查询商品信息
                if (model.IsQueryProductList)
                {
                    return true;
                }
            }
            return false;
        }
        #endregion

        /// <summary>
        /// 请求取号接口时将虚拟号收件人信息纠正兼容格式
        /// 列：某人,18888888888-0762,某某地址 转换成兼容格式=》 某人[0762],18888888888,某某地址[0762]
        /// 解决有些平台解密之后出现分机号使用第三方电子面单获取快递单号报错问题
        /// </summary>
        /// <param name="name">某人[0762]</param>
        /// <param name="mobile">18888888888</param>
        /// <param name="address">某某地址[0762]</param>
        /// <returns></returns>
        public (string newName, string newMobile, string newAddress) ConvertExpressVirtualReceiverInfo(string name, string mobile, string address)
        {
            if (string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(mobile) || string.IsNullOrWhiteSpace(address))
                return (name, mobile, address);
            if (mobile.Contains("-") && mobile.Contains("*") == false)
            {
                var fxjihao = mobile.Split('-').LastOrDefault();
                var newMobile = mobile.Split('-').FirstOrDefault();
                var newName = $"{name}[{fxjihao}]";
                var newAddress = $"{address}[{fxjihao}]";
                return (newName, newMobile, newAddress);
            }
            return (name, mobile, address);
        }

        public AlibabaEncryptOutOrderInfoModel GetAlibabaTradeOrderListFromExtField(string extField7)
        {
            if (string.IsNullOrEmpty(extField7))
                return new AlibabaEncryptOutOrderInfoModel();

            var outOrderInfo = extField7.ToObject<AlibabaEncryptOutOrderInfoModel>();
            return outOrderInfo;
        }

        /// <summary>
        /// 根据配置发件人信息加密打码，依次返回姓名，电话，详细地址打码数据
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="settings"></param>
        /// <param name="name"></param>
        /// <param name="phone"></param>
        /// <param name="detailAddress"></param>
        /// <returns></returns>
        public Tuple<string, string, string> GetSenderEncrypt(int templateId, dynamic settings, string name, string phone, string detailAddress)
        {
            if (templateId == 0 || settings == null || settings?.ReceiverSet == null)
                return new Tuple<string, string, string>(name, phone, detailAddress);
            var templateCanEncrypt = true;
            //检查当前模板是否在排除加密打码的模板内
            if (settings?.ReceiverSet?.NotEncryptReceiverTemplateId != null)
            {
                foreach (var t in settings?.ReceiverSet?.NotEncryptReceiverTemplateId)
                {
                    int tempId = t;
                    if (templateId == tempId)
                    {
                        templateCanEncrypt = false;
                        break;
                    }
                }
            }
            if (templateCanEncrypt && settings?.ReceiverSet?.EncryptReceiverName == true)
            {
                name = name.ToEncryptName();
            }

            if (templateCanEncrypt && settings?.ReceiverSet?.EncryptReceiverPhone == true)
            {
                phone = phone.ToEncrytPhone();
            }
            if (templateCanEncrypt && settings?.ReceiverSet?.EncryptReceiverAddress == true)
            {
                detailAddress = detailAddress.ToEncryptDetailAddress();
            }
            return new Tuple<string, string, string>(name, phone, detailAddress);
        }


        public string GetDistrictNameByWaybill(Order orderEntity, string item3)
        {
            var districtName = item3;
            if (string.IsNullOrEmpty(districtName))//没有匹配到区的，就用数据库的区
                districtName = orderEntity.ToCounty;
            if (string.IsNullOrEmpty(districtName))//区没有时，镇的信息有就用镇的来赋值
                districtName = orderEntity.ToTown;
            if (string.IsNullOrEmpty(districtName))//区没有时，用市的信息来赋值
                districtName = orderEntity.ToCity;

            return districtName;
        }

        /// <summary>
        /// 请求解密出userId
        /// </summary>
        /// <param name="secretKey"></param>
        /// <returns></returns>
        public string UserIdEncryptionByFxQingWk(string secretKey)
        {
            Byte[] keyutf = System.Text.Encoding.UTF8.GetBytes(CustomerConfig.AlibabaQingAppSecret); //CustomerConfig.AlibabaQingAppSecret  9PAIEWstZcj

            if (keyutf.Length > 16)
            {
                Byte[] news = new Byte[15];
                Array.Copy(keyutf, 0, news, 0, 16);

                keyutf = news;

            }
            else if (keyutf.Length < 16)
            {
                var num = 16 - keyutf.Length;//要追加的
                Byte[] news = new Byte[num];

                keyutf = keyutf.Concat(news).ToArray();

            }

            if (string.IsNullOrEmpty(secretKey))
                return null;


            Byte[] toEncryptArray = Convert.FromBase64String(secretKey);

            System.Security.Cryptography.RijndaelManaged rDel = new System.Security.Cryptography.RijndaelManaged();
            rDel.Key = keyutf;
            rDel.Mode = System.Security.Cryptography.CipherMode.ECB;
            rDel.Padding = System.Security.Cryptography.PaddingMode.PKCS7;


            System.Security.Cryptography.ICryptoTransform cTransform = rDel.CreateDecryptor();
            Byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);

            return System.Text.Encoding.UTF8.GetString(resultArray);
        }

        public string UserReplaceByTools(string uid, string platformType)
        {

            var replaceUid = "";
            try
            {
                var setting = new CommonSettingService().GetString("/System/Config/AlibabaFenDan/ToolsUser/" + platformType, -168);
                if (!string.IsNullOrEmpty(setting))
                {
                    var list = JsonExtension.ToList<AlibabaUserReplaceByTools>(setting);
                    list.ForEach(t =>
                    {
                        if (t.Uid == uid)
                            replaceUid = t.ReplaceUid;
                    });
                }
            }
            catch (Exception e)
            {
                replaceUid = "";
                Utility.Log.WriteError($"（CommService().UserReplaceByTools()）替换uid错误：{e.Message}");
            }

            if (string.IsNullOrEmpty(replaceUid))
                replaceUid = uid;

            return replaceUid;
        }

        /// <summary>
        /// 更新用户环境版本
        /// </summary>
        /// <param name="userIds">用户 ID 列表</param>
        /// <param name="type">更新类型（0：不更新路径流，1：更新商家，2：更新厂家）</param>
        /// <param name="version">版本环境，正式为null，其他为数字</param>
        /// <param name="phones">手机号，如果传入手机号不为空，以手机号转的用户 ID 为主</param>
        public bool SwitchUserVersionHandle(List<int> userIds, int type, int? version, List<string> phones = null)
        {
            if (phones != null && phones.Count > 500) return false;

            // 如果手机号不为空，则以手机号为主
            if (phones != null && phones.Any())
            {
                var userIdList = GetUserIdsByPhone(phones);
                if (userIdList.Any())
                    userIds = userIdList;
            }

            var cloudPlatformTypeList = new List<string> { "TouTiao", "Pinduoduo", "Jingdong", "Alibaba" };
            var dbConfigList = new DbConfigRepository().GetListByFxUserIds(userIds, cloudPlatformTypeList);

            Log.WriteLine($"SwitchUserVersionHandle，更新的用户Id：{string.Join(",", userIds)}");

            if (dbConfigList == null || dbConfigList.Count == 0)
                return false;

            if (!userIds.Any())
                return false;

            if (type == 0)
            {
                // 更新版本号
                UpdateVersion(userIds, version);
                return true;
            }

            var middleSql = type == 1 ? "UpFxUserId" : "DownFxUserId";
            var pathSql =
                $"SELECT DISTINCT {middleSql}, FxUserId FROM PathFlowNode WITH(NOLOCK) WHERE FxUserId IN @userIds";

            var locationModels = new List<DbConfigModel>();
            var pathFlowNodeList = new List<PathFlowNode>();

            // 处理分组并执行查询逻辑
            ProcessGroupModels(dbConfigList, group =>
            {
                var dbApi = new DbAccessUtility(CreateApiDbConfig(group.First()));
                ProcessGroupList(group, (dbLocation, x) =>
                {
                    if (dbLocation == CustomerConfig.CloudPlatformType)
                    {
                        locationModels.Add(x);
                    }
                    else
                    {
                        // 跨云查询
                        var pathFlowNodes = dbApi.Query<PathFlowNode>(pathSql, new { userIds });
                        if (pathFlowNodes != null && pathFlowNodes.Any())
                            pathFlowNodeList.AddRange(pathFlowNodes);
                    }
                });
            });

            // 同云查询
            if (locationModels.Any())
            {
                var combinedPathFlowNodes = new List<PathFlowNode>();
                locationModels.GroupBy(x => x.ConnectionString).ToList().ForEach(conn =>
                {
                    var pathFlowNodes = new PhShopRepository(conn.Key).DbConnection
                        .Query<PathFlowNode>(pathSql, new { userIds }).ToList();
                    if (pathFlowNodes.Any()) combinedPathFlowNodes.AddRange(pathFlowNodes);
                });
                pathFlowNodeList.AddRange(combinedPathFlowNodes.Distinct());
            }

            Log.WriteLine($"SwitchUserVersionHandle，更新的用户Id：{string.Join(",", userIds)}");
            // 获取最终需要更新的用户 ID 列表
            var finalUserIds = GetFinalUserIds(type, userIds, pathFlowNodeList);
            if (!finalUserIds.Any()) return false;

            // 更新版本号
            UpdateVersion(finalUserIds, version);
            return true;
        }

        /// <summary>
        /// 更新用户环境版本
        /// </summary>
        /// <param name="userIds">用户 ID 列表</param>
        /// <param name="version">版本号</param>
        private void UpdateVersion(List<int> userIds, int? version)
        {
            if (userIds == null || !userIds.Any()) return;
            userIds = userIds.Where(x => x > 0).Distinct().ToList();

            Log.WriteLine($"UpdateVersion，最终更新的用户共{userIds.Count()}条，Id：{string.Join(",", userIds)}");
            new ShopRepository().UpdateVersion(version, userIds);
        }

        /// <summary>
        ///  根据分组处理
        /// </summary>
        /// <param name="dbConfigList"></param>
        /// <param name="action"></param>
        private void ProcessGroupModels(IEnumerable<DbConfigModel> dbConfigList,
            Action<IGrouping<int, DbConfigModel>> action)
        {
            var groupModels = dbConfigList.GroupBy(x => x.DbNameConfig.Id).ToList();
            if (groupModels.Any()) groupModels.ForEach(action);
        }

        /// <summary>
        /// 处理分组列表
        /// </summary>
        /// <param name="groupList"></param>
        /// <param name="action"></param>
        private void ProcessGroupList(IEnumerable<DbConfigModel> groupList, Action<string, DbConfigModel> action)
        {
            groupList.ToList().ForEach(x => action(x.DbServer.Location, x));
        }

        /// <summary>
        /// 创建 ApiDbConfig 配置
        /// </summary>
        /// <param name="dbConfig"></param>
        /// <returns></returns>
        private ApiDbConfigModel CreateApiDbConfig(DbConfigModel dbConfig)
        {
            return new ApiDbConfigModel
            {
                DbNameConfigId = dbConfig.DbNameConfig.Id,
                Location = dbConfig.DbServer.Location,
                PlatformType = dbConfig.DbServer.Location
            };
        }

        /// <summary>
        /// 获取最终需要更新的用户 ID 列表
        /// </summary>
        /// <param name="type">更新类型</param>
        /// <param name="userIds">原始用户 ID 列表</param>
        /// <param name="pathFlowNodeList">路径流节点列表</param>
        /// <returns></returns>
        private List<int> GetFinalUserIds(int type, List<int> userIds, List<PathFlowNode> pathFlowNodeList)
        {
            var resultSupplierFxUserIds = new List<int>();
            var dic = type == 1
                ? pathFlowNodeList.GroupBy(x => x.FxUserId).ToDictionary(g => g.Key, g => g.Select(x => x.UpFxUserId).ToList())
                : pathFlowNodeList.GroupBy(x => x.FxUserId).ToDictionary(g => g.Key, g => g.Select(x => x.DownFxUserId).ToList());

            var repository = new SupplierUserRepository();

            foreach (var userId in userIds)
            {
                var supplierFxUserIds = new List<int>();
                if (dic.TryGetValue(userId, out var upFxUserIds))
                {
                    supplierFxUserIds.AddRange(upFxUserIds);
                }

                supplierFxUserIds = supplierFxUserIds.Distinct().ToList();
                if (!supplierFxUserIds.Any()) continue;

                var result = repository.GetIdsByType(type, userId, supplierFxUserIds);
                if (result.Any()) resultSupplierFxUserIds.AddRange(result);
            }

            return userIds.Union(resultSupplierFxUserIds).Distinct().ToList();
        }

        /// <summary>
        /// 通过手机号获取用户 ID 列表
        /// </summary>
        /// <param name="phones"></param>
        /// <returns></returns>
        public List<int> GetUserIdsByPhone(List<string> phones)
        {
            var listByMobiles = new UserFxService().GetListByMobiles(phones);
            return listByMobiles.Select(x => x.Id).ToList();
        }

        /// <summary>
        /// 获取商家和厂家 ID 列表
        /// </summary>
        /// <param name="userIds"></param>
        /// <returns></returns>
        private List<int> GetSupplierAndAgentIdByUserId(List<int> userIds)
        {
            var result = new List<int>();
            var repository = new SupplierUserRepository();

            // 分批处理
            const int batchSize = 500;
            for (var i = 0; i < userIds.Count; i += batchSize)
            {
                var batchUserIds = userIds.Skip(i).Take(batchSize).ToList();

                var agentResults = repository.GetIdsByCurFxUserIds(1, batchUserIds);
                var supplierResults = repository.GetIdsByCurFxUserIds(2, batchUserIds);

                result.AddRange(agentResults);
                result.AddRange(supplierResults);
            }

            return result.Distinct().ToList();
        }

        /// <summary>
        /// 添加移出白名单
        /// </summary>
        /// <param name="userIds"></param>
        /// <param name="isAdd">是否添加</param>
        /// <param name="isUpdatePath">是否更新上下游</param>
        /// <param name="whiteUserFlag"></param>
        public void AddOrRemoveWhiteUser(List<int> userIds, bool isAdd = true, bool isUpdatePath = true,string whiteUserFlag = "white_user")
        {
            if (userIds == null || !userIds.Any()) return;

            var userFxRepository = new UserFxRepository();
            if (isUpdatePath)
            {
                // 获取上下游
                var resultIds = GetSupplierAndAgentIdByUserId(userIds);
                userIds.AddRange(resultIds);
            }
            userIds = userIds.Distinct().ToList();

            // 获取对应用户数据
            var userList = new List<UserFx>();
            const int batchSize = 500;
            for (var i = 0; i < userIds.Count; i += batchSize)
            {
                var batchUserIds = userIds.Skip(i).Take(batchSize).ToList();
                userList.AddRange(userFxRepository.GetsByIds(batchUserIds, "Id, UserFlag"));
            }

            var needUpdateList = isAdd
                ? userList.Where(x => x.UserFlag == null || x.UserFlag.Contains(whiteUserFlag) == false).ToList()
                : userList.Where(x => x.UserFlag != null && x.UserFlag.Contains(whiteUserFlag)).ToList();

            if (!needUpdateList.Any()) return;
            const string baseProductSettingKey = "/ErpWeb/FenDan/BaseProductSetting";
            var shopRepository = new ShopRepository();

            // 是否基础商品相关白名单
            var isWhiteUserFlag = whiteUserFlag == UserFxRepository.WhiteUserFlag;

            // 更新用户数据
            foreach (var user in needUpdateList)
            {
                string userFlag;
                if (isAdd)
                {
                    userFlag = string.IsNullOrEmpty(user.UserFlag)
                        ? whiteUserFlag
                        : $"{user.UserFlag},{whiteUserFlag}";
                }
                else
                {
                    userFlag = user.UserFlag.Replace(whiteUserFlag, "");
                    // 去除多余的逗号
                    userFlag = userFlag.Replace(",,", ",");
                    userFlag = userFlag.Trim(',');
                    // 如果是移出白名单，同时将基础商品归一设置关闭
                    // 获取用户的systemShopId
                    if (isWhiteUserFlag)
                    {
                        var value = new BaseProductSettingsModel
                        {
                            AbnormalOrderTagReminder = false,
                            ExportBeforeReminder = false,
                            OrderCombine = false,
                            StockPreparationReminder = false,
                            PrintBeforeShippingWarning = false
                        }.ToJson();
                        var systemShopId = shopRepository.GetFxSystemShopByFxId(user.Id).Id;
                        businessSettingsService.SaveByShopId(systemShopId, baseProductSettingKey, value, user.Id); 
                    }
                }

                userFxRepository.UpdateFlag(user.Id, userFlag);
                // 更新缓存
                FxCaching.RefeshRemoteCache(FxCachingType.FxUser, user.Id.ToString2());
            }
            Log.WriteLine($"AddOrRemoveWhiteUser，Flag: {whiteUserFlag}，更新的用户Id：{string.Join(",", needUpdateList.Select(x => x.Id))}");
        }


        /// <summary>
        /// 京东快递是否使用固定物品名称
        /// </summary>
        /// <param name="systemShopId"></param>
        /// <returns></returns>
        public bool IsUseFixedGoodsNameForJdExpress(int systemShopId)
        {
            var result = false;
            try
            {
                var csService = new CommonSettingService();
                var globalSetting = csService.GetString("/FxSystem/IsUseFixedGoodsNameForJdExpress", 0);
                //全局开关：0全关；1或空：全开；2指定用户
                if (string.IsNullOrEmpty(globalSetting) || globalSetting == "1")
                    return true;

                if (globalSetting == "0")
                    return false;

                //Value=0为关闭，其他为开启
                var setting = csService.GetString("/FxSystem/IsUseFixedGoodsNameForJdExpress", systemShopId);
                if (string.IsNullOrEmpty(setting) || setting != "0")
                {
                    setting = "1";
                }
                return setting == "1";
            }
            catch (Exception e)
            {
            }
            return result;
        }
    }
}
