using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.LogisticService;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.EntityExtension;

namespace DianGuanJiaApp.Services
{
    /// <summary>
    /// 订单相关的服务
    /// </summary>
    public partial class CustomerOrderService : BaseService<CustomerOrder>
    {
        #region 私有变量

        private CustomerOrderRepository _repository = new CustomerOrderRepository();
        private PrintControlService _printControlService = new PrintControlService();
        private PrintTemplateService _printTemplateService = new PrintTemplateService();
        private WaybillCodeService _waybillCodeService = new WaybillCodeService();
        private SellerInfoService _sellerInfoService = new SellerInfoService();
        private SendGoodTemplateRepository _sendGoodTemplateRepository = new SendGoodTemplateRepository();
        private CommService _commService = new CommService();
        private ILogisticService _logisticService;
        private UserSiteInfoService _userSiteInfoService = new UserSiteInfoService();
        private AreaCodeInfoService _areaCodeInfoService = new AreaCodeInfoService();
        private ExpressCodeMappingService _expressCodeMappingService = new ExpressCodeMappingService();
        private ExpressCompanyService _expressCompanyService = new ExpressCompanyService();
        private SendHistoryService _sendHistoryService = new SendHistoryService();
        private CommonSettingService _commonSettingService = new CommonSettingService();

        #endregion

        #region 订单查询相关

        /// <summary>
        /// 获取订单详细信息:包括订单项、订单项的属性信息
        /// </summary>
        /// <param name="orders">订单查询的key</param>
        /// <returns></returns>
        public List<CustomerOrder> GetOrders(List<CustomerOrder> orders)
        {
            if (orders == null || !orders.Any())
                return orders;
            return _repository.GetOrders(orders.Select(x => new OrderSelectKeyModel
                { Id = x.Id, PlatformOrderId = x.PlatformOrderId, ShopId = x.ShopId }).ToList());
        }

        /// <summary>
        /// 获取订单详细信息:包括订单项、订单项的属性信息
        /// </summary>
        /// <param name="keys">订单查询的key</param>
        /// <returns></returns>
        public List<CustomerOrder> GetOrders(List<OrderSelectKeyModel> keys)
        {
            if (keys == null || !keys.Any())
                return null;
            return _repository.GetOrders(keys);
        }

        /// <summary>
        /// 根据检索条件查询订单
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public PagedResultModel<CustomerOrder> GetOrders(OrderSearchModel model)
        {
            //底层有处理
            //var shopFilter = model.Filters.Where(m => m.Name.ToString2().ToLower() == "shopid").FirstOrDefault();
            //if (shopFilter == null || shopFilter.Value.IsNullOrEmpty())
            //{
            //    if (shopFilter == null)
            //        model.Filters.Add(new OrderSearchFieldModel() { TableAlias = "o", Name = "ShopId", TableName = "P_Order", Contract = "IN", Value = SiteContext.Current.CurrentShopId.ToString(), FieldType = "int" });
            //    else
            //        shopFilter.Value = SiteContext.Current.CurrentShopId.ToString();
            //}
            var isPreordainFilter = model.Filters.Where(m => m.Name.ToString2().ToLower() == "ispreordain").FirstOrDefault();
            if (isPreordainFilter != null)
                model.Filters.Remove(isPreordainFilter);


            return _repository.GetOrders(model);
        }

        /// <summary>
        /// 将自由打印的订单转换为平台订单
        /// </summary>
        /// <param name="corders"></param>
        /// <returns></returns>
        public List<Order> ConvertToOrder(List<CustomerOrder> corders)
        {
            if (corders == null || !corders.Any())
                return new List<Order>();
            var orders = new List<Order>();
            corders.ForEach(c =>
            {
                var ois = c.OrderItems;
                var order = c as Order;
                if (ois != null)
                {
                    ois.ForEach(oi =>
                    {
                        order.OrderItems.Add(oi as OrderItem);
                    });
                }
                orders.Add(order);
            });
            return orders;
        }

        public void SetAsDeleted(List<int> ids)
        {
            _repository.SetAsDeleted(ids);
        }

        public void SetAsSended(List<int> ids)
        {
            _repository.SetAsSended(ids);
        }


        public List<CustomerOrderItem> GetOrderItems(int shopId, string platformOrderId)
        {
            var ois = _repository.DbConnection.Query<CustomerOrderItem>("Select * FROM dbo.P_CustomerOrderItem WITH(NOLOCK) WHERE PlatformOrderId=@PlatformOrderId AND ShopId=@ShopId",
                new
                {
                    PlatformOrderId = platformOrderId,
                    ShopId = shopId
                });

            return ois.ToList();
        }

        #endregion

        /// <summary>
        /// 更新订单备注
        /// </summary>
        /// <param name="selectKeyModel">更新条件</param>
        /// <param name="flag">备注旗帜</param>
        /// <param name="remark">备注内容</param>
        /// <returns></returns>
        public bool UpdateOrderSellerRemark(OrderSelectKeyModel selectKeyModel, string flag, string remark)
        {
            if (selectKeyModel.Id == 0 || selectKeyModel.PlatformOrderId == string.Empty || selectKeyModel.PlatformOrderId == 0.ToString() || selectKeyModel.ShopId == 0)
                return false;

            //,自由订单 不用调接口
            //获取平台服务对象
            //var platformService = PlatformFactory.GetPlatformService(SiteContext.Current.AllShops.FirstOrDefault(f => f.Id == selectKeyModel.ShopId));
            //1.调用接口修改平台数据  
            //var apiResult = platformService.UpdateOrderRemark(selectKeyModel.PlatformOrderId, remark, flag);
            //2.更新订单卖家备注
            var updateResult = false;
            //if (apiResult)
            //{
            updateResult = _repository.UpdateOrderSellerRemark(selectKeyModel, flag, remark);
            //}
            return updateResult;
        }

        /// <summary>
        /// 更新收件人信息
        /// </summary>
        /// <param name="selectKeyModel"></param>
        /// <param name="name"></param>
        /// <param name="phone"></param>
        /// <param name="address"></param>
        /// <returns></returns>
        public bool UpdateOrderReceiver(OrderSelectKeyModel selectKeyModel, string name, string phone, string address)
        {
            //分割出 省、市、区、地址
            //var addList = address.Split(new string[] { " " }, StringSplitOptions.RemoveEmptyEntries);
            var addressList = _areaCodeInfoService.AddressSplit(address);
            return _repository.UpdateOrderReceiver(selectKeyModel, name, phone, addressList.Item1, addressList.Item2, addressList.Item3, addressList.Item4);
        }

        /// <summary>
        /// 更新发件人信息
        /// </summary>
        /// <param name="selectKeyModel"></param>
        /// <param name="name"></param>
        /// <param name="phone"></param>
        /// <param name="address"></param>
        /// <returns></returns>
        public bool UpdateOrderSeller(OrderSelectKeyModel selectKeyModel, string companyName, string name, string mobile, string phone, string address)
        {
            return _repository.UpdateOrderSeller(selectKeyModel, companyName, name, mobile, phone, address);
        }

        /// <summary>
        /// 更新订单代收货款或保价金额
        /// </summary>
        /// <param name="selectKeyModels"></param>
        /// <param name="amount"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public bool UpdateCodAmountOrInSureAmount(List<OrderSelectKeyModel> selectKeyModelList, string type, decimal? amount, string condition, decimal? when, decimal? then)
        {
            return _repository.UpdateCodAmountOrInSureAmount(selectKeyModelList, type, amount, condition, when, then);
        }


        public CustomerOrder GetOrder(OrderSelectKeyModel key)
        {
            return GetOrders(new List<OrderSelectKeyModel> { key })?.FirstOrDefault();
        }

        /// <summary>
        /// 批量插入订单
        /// </summary>
        /// <param name="customerOrderList"></param>
        /// <param name="shopId"></param>
        public void BulkInsertOrders(List<CustomerOrder> customerOrderList, int shopId, LogForOperator log = null)
        {
            var logDetails = new List<dynamic>();
            var now = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            List<int> msLst = new List<int>() { 0, 3, 7 }; // Sql datetime精确到1/300秒,所以毫秒只能出现0,3,7
            //生成订单id
            for (int i = 0; i < customerOrderList.Count; i++)
            {
                var order = customerOrderList[i];
                order.ShopId = shopId;
                order.PlatformType = PlatformType.Offline.ToString();
                order.CreateTime = now.AddMilliseconds(i * 10);
                order.TotalAmount = order.TotalAmount > 0 ? order.TotalAmount : order.OrderItems.Sum(f => f.ItemAmount); //汇总产品金;
                order.TotalWeight = order.OrderItems.Sum(f => f.Weight); //汇总产品金额
                if (string.IsNullOrWhiteSpace(order.PlatformOrderId) == true)
                {
                    order.PlatformOrderId = GenarelPlatformOrderId(shopId);
                }

                if (log != null) 
                {
                    if(order.RawRecieverAddress != order.ToFullAddress)
                        logDetails.Add(new { Pid = order.PlatformOrderId, RawAddr = order.RawRecieverAddress, Addr = order.ToFullAddress, IsReceiver = false });
                    else if(order.RawSenderAddress != order.SenderAddress)
                        logDetails.Add(new { Pid = order.PlatformOrderId, RawAddr = order.RawSenderAddress, Addr = order.SenderAddress, IsReceiver = true });
                }
            }
            if (log != null)
                log.Detail = new { TotalCount = customerOrderList.Count, DiffCount = logDetails.Count, Details = logDetails };

            _repository.BulkInsertOrders(customerOrderList);
        }

        public static string GenarelPlatformOrderId(int shopId)
        {
            return DateTime.Now.ToString("yyMMddHHmmss") + Math.Abs((shopId.ToString() + Guid.NewGuid().ToString()).GetHashCode()).ToString();
        }

        public void UpdateCustomerOrder(CustomerOrder order)
        {
            ////1.查询出订单产品
            //var orderItems = GetOrderItems(order.ShopId, order.PlatformOrderId);
            ////2.删除不存在于这次修改的产品
            //order.OrderItems.ForEach(oi =>
            //{
            //    var exist = orderItems.Where(f => (f.ProductID + f.SkuID) == (oi.ProductID + oi.SkuID));
            //    if (exist.Any())
            //    {
            //        oi.Id = exist.First().Id;
            //    }
            //});

            //var existIds = order.OrderItems.Select(f => f.Id).Distinct();
            //var delOrderItems = orderItems.Where(f => existIds.Contains(f.Id) == false);

            //if (delOrderItems.Any())
            //{
            //    var oi_ids = delOrderItems.Select(f => f.Id).ToList();
            //    _repository.DbConnection.Execute("delete from dbo.P_CustomerOrderItem WHERE Id IN@Id", new { Id = oi_ids });
            //}

            //删除原订单的订单项
            _repository.DbConnection.Execute("delete from dbo.P_CustomerOrderItem WHERE PlatformOrderId=@PlatformOrderId AND ShopId=@ShopId",
                new
                {
                    PlatformOrderId = order.PlatformOrderId,
                    ShopId = order.ShopId,
                });

            //3.更新
            _repository.UpdateCustomerOrder(order);
        }
        public void Insert(CustomerOrder order)
        {
            order.PlatformOrderId = GenarelPlatformOrderId(order.ShopId); //生成platformorderid，自由订单平台id，生成不一样，快递面单取单号需要用到。
            _repository.Insert(order);
        }


        public OrderSelectKeyModel GetScanOrderKeyModel(string orderId, List<int> shopList)
        {
            OrderSelectKeyModel orderSelectKeyModel = null;
            CustomerOrder order = _repository.GetScanOrder(orderId, shopList);

            if (order != null)
            {
                orderSelectKeyModel = new OrderSelectKeyModel { Id = order.Id, PlatformOrderId = order.PlatformOrderId, ShopId = order.ShopId };//PlatformOrderId
            }
            return orderSelectKeyModel;
        }


        /// <summary>
        /// 微商小程序通过ID获取订单
        /// </summary>
        public List<CustomerOrder> WsXcxGetOrderById(List<int> Ids, bool isAll)
        {
            return _repository.WsXcxGetOrderById(Ids, isAll);
        }

        /// <summary>
        /// 微商小程序通过ID获取订单
        /// </summary>
        public CustomerOrder WsXcxGetOrderById(int Ids)
        {
            return _repository.WsXcxGetOrderById(Ids);
        }


        /// <summary>
        /// 微商小程序--更新单个订单
        /// </summary>
        public bool WsXcxUpdateOrder(WxUpdateOrderRequestModel reqModel)
        {
            return _repository.WsXcxUpdateOrder(reqModel);
        }


        /// <summary>
        /// 微商小程序--获取订单的数量(用于主页)
        /// </summary>
        public List<CustomerOrder> WsXcxGetOrderList(List<int> ids, string typeName)
        {
            return _repository.WsXcxGetOrderList(ids, typeName);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="key"></param>
        /// <returns>Item1:主订单，Item2 子订单</returns>
        public Tuple<CustomerOrder, List<CustomerOrder>> GetMergerOrderWithChilds(OrderSelectKeyModel key)
        {
            var mergerOrder = GetOrder(key);
            if (mergerOrder == null)
                return null;
            var childs = new List<CustomerOrder>();
            if (!string.IsNullOrEmpty(mergerOrder.ChildOrderId))
            {
                var keys = mergerOrder.ChildOrderId.Split(',').Select(k => new OrderSelectKeyModel { PlatformOrderId = k, ShopId = key.ShopId }).ToList();
                childs = GetOrders(keys);
                if (childs == null)
                    return null;
            }
            return new Tuple<CustomerOrder, List<CustomerOrder>>(mergerOrder, childs);
        }

        /// <summary>
        /// 根据配置获取打印流水号
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="index"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public string GetPrintSerial(int shopId, int index, int total)
        {
            var serial = string.Empty;
            PrintSerialSetting printSerialSetting = _commonSettingService.GetPrintSerialSetting(shopId);
            if (printSerialSetting != null)
            {
                var clearFormat = printSerialSetting.ClearSet.ToString2();
                if (!clearFormat.IsNullOrEmpty())
                {
                    var clearkey = DateTime.Now.ToString(clearFormat);
                    var value = printSerialSetting.PrintSerialValues.FirstOrDefault(m => m.DateTime == clearkey);
                    if (value == null)
                    {
                        value = new PrintSerisalValue() { DateTime = clearkey, SerialNum = 0 };
                        printSerialSetting.PrintSerialValues.Add(value);
                    }
                    // 时间格式
                    if (printSerialSetting.ContainerDate)
                    {
                        var dateStr = DateTime.Now.ToString(printSerialSetting.SerialFormat);
                        serial += dateStr + "-";
                    }
                    // 页码
                    if (printSerialSetting.ContainerPageNumber)
                    {
                        string pageNum = $"{index}/{total}";
                        serial += pageNum + "-";
                    }
                    // 流水号
                    value.SerialNum++;
                    serial += value.SerialNum;

                    // 更新流水号计数配置
                    var printSerialSettingJson = JsonExtension.ToJson(printSerialSetting);
                    _commonSettingService.UpdatePrintSerialNumber(printSerialSettingJson, shopId);
                }
            }
            return serial;
        }

        /// <summary>
        /// 修改打印内容
        /// </summary>
        /// <param name="key"></param>
        /// <param name="PrintContent"></param>
        /// <returns></returns>
        public bool UpdatePrintContent(OrderSelectKeyModel key, string printContent)
        {
            return _repository.UpdatePrintContent(key, printContent);
        }



        /// <summary>
        /// 更新订单的ispreviewed字段
        /// </summary>
        /// <param name="selectKeyModelList"></param>
        public void UpdateOrderIsPreviewedFlag(List<OrderSelectKeyModel> selectKeyModelList)
        {
            _repository.UpdateOrderIsPreviewd(selectKeyModelList);
        }
    }
}
