using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Helpers;

namespace DianGuanJiaApp.Services.Services.DataEventTrackingLog
{
    public class OrderSendInvokeApiLogService
    {
        private static readonly OrderSendInvokeApiLogService instance = new OrderSendInvokeApiLogService();

        private static LogHelper _logHelper;
        private OrderSendInvokeApiLogService()
        {
            _logHelper = new LogHelper(Config.SlsProject, LogStoreNames.OrderSendInvokeApiLog);
        }

        public static LogHelper Instance
        {
            get
            {
                return _logHelper;
            }
        }
    }
    
    public class OrderSendInvokeApiLogVolcanoService
    {
        private static readonly OrderSendInvokeApiLogVolcanoService instance = new OrderSendInvokeApiLogVolcanoService();

        private static VolcanoLogHelper _logHelper;
        private OrderSendInvokeApiLogVolcanoService()
        {
            _logHelper = new VolcanoLogHelper(VolcanoLogProject.FenDanProjectId, VolcanoLogTopic.OrderSendInvokeApiLogTopicId);
        }

        public static VolcanoLogHelper Instance
        {
            get
            {
                return _logHelper;
            }
        }
    }
}