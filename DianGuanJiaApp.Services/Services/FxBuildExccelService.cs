extern alias snsdk;
using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.OrderModule;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using DianGuanJiaApp.Warehouse.Model.Response;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System;
using System.Collections.Generic;
using System.Data;
using iTextSharp.text;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;

namespace DianGuanJiaApp.Services
{
    public partial class FxBuildExccelService
    {

        /// <summary>
        /// 获取当前用户和商家绑定店铺
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public static Tuple<List<Shop>, List<FxUserShop>> GetAgentBindShops(int fxUserId, List<int> shopIds)
        {
            var supplierUserService = new SupplierUserService();
            var fxUserShopService = new FxUserShopService();

            var agentIds = supplierUserService.GetAgentUserId(fxUserId);
            var agentShops = fxUserShopService.GetFxUserShopIds(agentIds); // 商家绑定店铺

            var curUserShops = new ShopService().GetShopByIds(shopIds); // 当前用户绑定店铺
            return new Tuple<List<Shop>, List<FxUserShop>>(curUserShops, agentShops);
        }


        #region 订单分发Excel导出
        public static string GetDecryptDbName(string dbname)
        {
            var newDbName = string.Empty;
            if (!string.IsNullOrEmpty(dbname))
            {
                newDbName = DES.DecryptDES(dbname, CustomerConfig.LoginCookieEncryptKey);
            }
            return newDbName;
        }

		/// <summary>
		/// 新版订单导出（先转换为Xml 然后才转换为 Excel）
		/// </summary>
		/// <param name="lorders"></param>
		/// <param name="type"></param>
		/// <param name="setting"></param>
		/// <param name="rootPath"></param>
		/// <param name="isWhite"></param>
        public static void BuildFxOrderExcelV2(List<LogicOrder> lorders, ExportType type, ExportSetting setting, string rootPath, bool isWhite, bool isQueryColdAndHotDb)
        {
            if (lorders == null || !lorders.Any())
                return;

            var curFxUser = SiteContext.Current.CurrentFxUser;
            var fxUserId = curFxUser.Id;

            var sids = lorders.Where(x => x.FxUserId == fxUserId).Select(x => x.ShopId).Distinct().ToList();
            var tuple = GetAgentBindShops(fxUserId, sids);
            var curUserShops = tuple.Item1;
            var agentShops = tuple.Item2;
            var isCrossBorderSite = CustomerConfig.IsCrossBorderSite;//CustomerConfig.IsCrossBorderSite;//是否是跨境站点

            // 非抖店平台 或 非所有订单页面 则不导出部分金额字段
            if (!(lorders.Any(o => o.PlatformType.Equals("TouTiao"))) || !type.Equals(ExportType.ErpAllOrder))
            {
                setting.CheckedItems.RemoveAll(ci => ci.Value.Equals("Export_ShippingFee") || ci.Value.Equals("Export_PlatformSubsidy") || ci.Value.Equals("Export_PayTotalAmount"));
            }

            //白名单用户才能导出商品ID
            if (setting.CheckedItems.Any(a => a.From.Equals("product")) && setting.CheckedItems.Any(a => a.Value.Equals("Export_ProductID")) && isWhite == false)
            {
                setting.CheckedItems.Remove(setting.CheckedItems.FirstOrDefault(f => f.From.Equals("product") && f.Value.Equals("Export_ProductID")));
            }
            //是否是跨境站点，非跨境站点移除国家、币种、交运状态
            if (!isCrossBorderSite)//SiteContext.Current.IsShowCrossBorder
                setting.CheckedItems.RemoveAll(ci => ci.Value.Equals("Export_ToCountry") || ci.Value.Equals("Export_Currency") || ci.Value.Equals("Export_PackageStatus"));

            // 获取勾选的商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 除商品信息外勾选的内容
            var otherChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "productMerge").ToList();

            List<string> headNames = setting.CheckedItems.Select(m => m.Text).ToList();
            // 商品信息单列显示，列头移除单列商品信息
            if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
            {
                headNames = setting.CheckedItems.Where(m => m.From != "product").Select(m => m.Text).ToList();
            }
            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();
                    headNames.RemoveAt(headIndex);
                    headNames.InsertRange(headIndex, addr);

                    otherChkItems.RemoveAt(headIndex);
                    otherChkItems.InsertRange(headIndex, addrLst);
                }
            }

            var colunms = BuildExccelService.GetOrderColumns(headNames);

            XlsxOutputHelper xlsxOutput = new XlsxOutputHelper(rootPath);

            var existLogicOrderIds = new List<string>();
            var isGenerated = xlsxOutput.GenerateSheetNew2(colunms, "订单信息", 0, (pIndex, toalCount, flag) =>
            {
                Stopwatch sw1 = new Stopwatch();
                sw1.Start();
                var dt = new DataTable();
                headNames.ForEach(col =>
                {
                    dt.Columns.Add(new DataColumn(col));
                });
                var data = new DataPackage(new ParamModel { IsSucess = true, Table = dt, Flag = 0, MergeInfos = new List<MergeCellRangeAddress>() });
                #region 线下单获取导入订单编号（2022-03-17改为导出系统单号）

                // 线下单获取导入订单编号
                var vOrderDic = new Dictionary<string, Order>();
                var virtualOrderKeys = lorders.Where(x => x.PlatformType == PlatformType.Virtual.ToString()).SelectMany(x =>
                {
                    return x.LogicOrderItems.Select(y => new OrderSelectKeyModel { PlatformOrderId = y.PlatformOrderId, ShopId = x.ShopId, FxUserId = x.FxUserId }).ToList();
                }).ToList();
                if (virtualOrderKeys.Any())
                {
                    var virtualOrders = new OrderService().GetOrders(virtualOrderKeys);
                    vOrderDic = virtualOrders.GroupBy(x => x.PlatformOrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault());
                }

                if (vOrderDic.Any())
                {
                    lorders.Where(o => o.PlatformType == PlatformType.Virtual.ToString()).ToList().ForEach(o =>
                    {
                        o.LogicOrderItems.ForEach(oi =>
                        {
                            Order vOrder = null;
                            if (vOrderDic.TryGetValue(oi.PlatformOrderId, out vOrder) && vOrder != null && vOrder.CustomerOrderId.IsNotNullOrEmpty())
                            {
                                oi.OrignalOrderId = vOrder.CustomerOrderId;
                            }
                        });
                    });
                }

                #endregion
                var orders = ConvertToOrders(lorders, setting.CheckedItems, isQueryColdAndHotDb);
                BuildExccelService.FxOrderToDataTableV2(data, toalCount, orders, setting, isCrossBorderSite, type);
                //前端导出只需运行1次
                data.Model.IsReturn = true;
                sw1.Stop();
                return data;
            });
            if (!isGenerated)
            {
                Log.Debug($"FxUserId:{fxUserId} 订单导出成功");
            }
            xlsxOutput.EndGenerate(isGenerated, (endmsg) =>
            {
                Log.Debug($"FxUserId:{fxUserId} 订单导出成功");
            });
        }

        public static IWorkbook BuildFxOrderExcel(List<LogicOrder> lorders, ExportType type, ExportSetting setting, string fileName,bool isWhite,bool isQueryColdAndHotDb)
        {
            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("订单信息");

            if (lorders == null || !lorders.Any())
                return workbook;

            //var curShop = SiteContext.Current.CurrentLoginShop;
            var curFxUser = SiteContext.Current.CurrentFxUser;
            var fxUserId = curFxUser.Id;

            var sids = lorders.Where(x => x.FxUserId == fxUserId).Select(x => x.ShopId).Distinct().ToList();
            var tuple = GetAgentBindShops(fxUserId, sids);
            var curUserShops = tuple.Item1;
            var agentShops = tuple.Item2;
            var isCrossBorderSite = CustomerConfig.IsCrossBorderSite;//CustomerConfig.IsCrossBorderSite;//是否是跨境站点
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook);
            ICellStyle contentLongStyle = GetContentCellStyleByToLongCol(workbook);
            ICellStyle contentMergeStyle = GetContentCellStyleByToMerge(workbook);
            ICellStyle contentLongMergeStyle = GetContentCellStyleByToLongColMerge(workbook);
            IRow header = sheet.CreateRow(0);
            header.Height = 15 * 20;

            // 非抖店平台 或 非所有订单页面 则不导出部分金额字段
            if (!(lorders.Any(o => o.PlatformType.Equals("TouTiao"))) || !type.Equals(ExportType.ErpAllOrder))
            {
                setting.CheckedItems.RemoveAll(ci => ci.Value.Equals("Export_ShippingFee") || ci.Value.Equals("Export_PlatformSubsidy") || ci.Value.Equals("Export_PayTotalAmount"));
            }

            //白名单用户才能导出商品ID
            if (setting.CheckedItems.Any(a => a.From.Equals("product")) && setting.CheckedItems.Any(a => a.Value.Equals("Export_ProductID")) && isWhite == false)
            {
                setting.CheckedItems.Remove(setting.CheckedItems.FirstOrDefault(f => f.From.Equals("product") && f.Value.Equals("Export_ProductID")));
            }
            ///是否是跨境站点，非跨境站点移除国家、币种、交运状态
            if (!isCrossBorderSite)//SiteContext.Current.IsShowCrossBorder
                setting.CheckedItems.RemoveAll(ci => ci.Value.Equals("Export_ToCountry") || ci.Value.Equals("Export_Currency") || ci.Value.Equals("Export_PackageStatus"));



            // 获取勾选的商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 除商品信息外勾选的内容
            var otherChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "productMerge").ToList();
            //// 添加省市区详细地址的扩展
            //var extChkItems = JsonExtension.ToList<CheckedItem>(JsonExtension.ToJson(setting.CheckedItems));

            List<string> headNames = setting.CheckedItems.Select(m => m.Text).ToList();
            // 商品信息单列显示，列头移除单列商品信息
            if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
            {
                headNames = setting.CheckedItems.Where(m => m.From != "product").Select(m => m.Text).ToList();
            }
            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();
                    headNames.RemoveAt(headIndex);
                    headNames.InsertRange(headIndex, addr);

                    otherChkItems.RemoveAt(headIndex);
                    otherChkItems.InsertRange(headIndex, addrLst);
                }
            }


            #region 线下单获取导入订单编号（2022-03-17改为导出系统单号）

            // 线下单获取导入订单编号
            var vOrderDic = new Dictionary<string, Order>();
            var virtualOrderKeys = lorders.Where(x => x.PlatformType == PlatformType.Virtual.ToString()).SelectMany(x =>
            {
                return x.LogicOrderItems.Select(y => new OrderSelectKeyModel { PlatformOrderId = y.PlatformOrderId, ShopId = x.ShopId, FxUserId = x.FxUserId }).ToList();
            }).ToList();
            if (virtualOrderKeys.Any())
            {
                var virtualOrders = new OrderService().GetOrders(virtualOrderKeys);
                vOrderDic = virtualOrders.GroupBy(x => x.PlatformOrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault());
            }

            if (vOrderDic.Any())
            {
                lorders.Where(o => o.PlatformType == PlatformType.Virtual.ToString()).ToList().ForEach(o =>
                 {
                     o.LogicOrderItems.ForEach(oi =>
                     {
                         Order vOrder = null;
                         if (vOrderDic.TryGetValue(oi.PlatformOrderId, out vOrder) && vOrder != null && vOrder.CustomerOrderId.IsNotNullOrEmpty())
                         {
                             oi.OrignalOrderId = vOrder.CustomerOrderId;
                         }
                     });
                 });
            }

            #endregion


            #region 收件人加密解密

            if (setting.CheckedItems.Any(x => x.Value.Contains("ToFullAddress") || x.Value.Contains("ToAddress") || x.Value.Contains("ToName") || x.Value.Contains("ToPhone") || x.Value.Contains("ToMobile")))
            {
                FxPlatformEncryptService.EncryptOrders(lorders, EncryptFromType.Order, encryptSender: true, encryptWangwang: true, needSyncLog: true);
                //收件人信息脱敏

                var notEncryptOrders = lorders.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.ToPhone?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
                notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
                if (type != ExportType.ErpOfflineOrder && notEncryptOrders.Any())
                {
                    //EncryptionService DataMaskservice = new EncryptionService();
                    //var noVirtualOrders = DataMaskservice.getPlatformType(lorders);
                    EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(lorders);
                }
            }

            #endregion


            var orders = ConvertToOrders(lorders,setting.CheckedItems, isQueryColdAndHotDb);

            // 第一行填充表头信息和样式
            int index = 0;
            // 设置Excel列头内容和样式
            headNames.ForEach(name =>
            {
                //设置列宽度
                //sheet.SetColumnWidth(index, 30 * 256);
                SetColumnWidth(sheet, name, index);
                // 设置列名和样式
                header.CreateCell(index).SetCellValue(name);
                header.GetCell(index).CellStyle = headStyle;
                index++;
            });

            // 第二行开始填充Excel内容
            index = 1; // 订单总行数
            //var itemRowIndex = 1; // 实际总行数
            // 导出商家或者店铺名称
            var supplierUsers = new List<SupplierUser>();
            var agentUsers = new List<SupplierUser>();
            //var users = new List<UserFx>();
            if (setting.CheckedItems.Any(x => x.Value == "Export_ShopName") || setting.CheckedItems.Any(x => x.Value == "Export_SupplierName"))
            {
                var supplierUserRepository = new SupplierUserRepository();
                if (setting.CheckedItems.Any(x => x.Value == "Export_SupplierName"))
                    supplierUsers = supplierUserRepository.GetByFxUserId(fxUserId, true, needEncryptAccount: true);
                if (setting.CheckedItems.Any(x => x.Value == "Export_ShopName"))
                    agentUsers = supplierUserRepository.GetByFxUserId(fxUserId, false, needEncryptAccount: true);
            }

            orders.ForEach(order =>
            {
                var dic = order.ToDictionary();
                IRow row = sheet.CreateRow(index);
                row.Height = 20 * 20;
                var isAddrMerge = true;
                var itemRowIndex = index;

                // 合并订单行内容
                List<CellRangeAddress> regionLst = new List<CellRangeAddress>();
                if (setting.Export_ProductShowStyle == "Export_ShowMutilLine_Merge")
                {
                    if (order.OrderItems.Count > 1 && productChkItems.Count > 0)
                    {
                        otherChkItems.ForEach(item =>
                        {
                            var itemIndex = headNames.IndexOf(item.Text);
                            if (itemIndex != -1)
                            {
                                // 商品信息之前的内容行合并
                                int startRowIndex = index;
                                int endRowNumIndex = index + order.OrderItems.Count - 1;

                                var productRegion = new CellRangeAddress(startRowIndex, endRowNumIndex, itemIndex, itemIndex);
                                sheet.AddMergedRegion(productRegion);
                            }
                        });
                    }
                }

                // 2022-03-17改为只导出系统单号
                var platformOrderIds = string.Empty;
                var childOrderIds = order.OrderItems.Where(x => x.OrignalPlatformOrderId.IsNotNullOrEmpty()).Select(x => x.OrignalPlatformOrderId).Distinct().ToList(); //dic["ChildOrderId"].ToString2();
                if (childOrderIds != null && childOrderIds.Any())
                    platformOrderIds = string.Join("\n", childOrderIds);
                else
                    platformOrderIds = dic["PlatformOrderId"].ToString2();

                //// 销售价是否对厂家可见（订单原始节点和设置对厂家销售价可见节点）
                //var agentIsSalePrice = order.FxUserId == curFxUser.Id || (agentIsSalePrices.FirstOrDefault(x => x.FxUserId == order.UpFxUserId)?.Value ?? false);
                // 商品信息所在列序号集合
                var productCellIndexLst = new List<int>();
                // 填充每一列的值
                setting.CheckedItems.ForEach(chkItem =>
                {
                    var field = chkItem.Value.Replace("Export_", "");
                    var text = chkItem.Text;
                    var from = chkItem.From;

                    // 收件人地址不合并1列显示，则移除收件人地址之后列往后移动3列
                    var cellIndex = setting.CheckedItems.IndexOf(chkItem);
                    if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
                    {
                        int floopcellindex = cellIndex;
                        for (int i = 0; i < floopcellindex; i++)
                        {
                            if (setting.CheckedItems[i].From == "product") cellIndex--;
                        }
                    }
                    if (!isAddrMerge)
                    {
                        cellIndex = cellIndex + 3;
                    }

                    #region 填充每列的内容
                    if (from == "shop")
                    {
                        if (field == "ShopName")
                        {
                            var agent = agentUsers?.FirstOrDefault(x => x.FxUserId == order.UpFxUserId);
                            string agentName = "";
                            if (agent == null)
                            {
                                agentName = curUserShops.FirstOrDefault(x => x.Id == order.ShopId)?.NickName ?? "店铺或商家已解绑";
                            }
                            else
                            {
                                if (!string.IsNullOrWhiteSpace(order.AgentShopName))
                                {
                                    agentName = order.AgentShopName;
                                }
                                else
                                {
                                    agentName = agent.AgentMobileAndRemark;
                                }
                            }
                            row.CreateCell(cellIndex).SetCellValue(agentName);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "SupplierName")
                        {
                            var defaultSupplier = curFxUser.NickName.IsNullOrEmpty() ? curFxUser.Mobile : curFxUser.NickName;
                            var supplier = supplierUsers?.FirstOrDefault(x => x.SupplierFxUserId == order.DownFxUserId);
                            var supplierName = supplier == null ? defaultSupplier : supplier.SupplierMobileAndRemark;
                            row.CreateCell(cellIndex).SetCellValue(supplierName);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        //var shop = shops.FirstOrDefault(m => m.Id == order.ShopId);
                        //row.CreateCell(cellIndex).SetCellValue(shop == null ? "" : shop.NickName);
                        //row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (from == "order")
                    {
                        if (field == "ErpStatus")
                        {
                            // 订单状态
                            var refundStatus = order.RefundStatus;
                            var status = order.PlatformStatus;
                            var exStatus = new List<string> { "-2", "1", "999" };
                            var val = string.Empty;
                            if (status == "waitsellersend")
                                val = "待发货";
                            else if (status == "inrefund" && (refundStatus == "WAIT_SELLER_AGREE" || refundStatus == "REFUND_SUCCESS"))
                                val = "退款单";
                            else if (status == "sended" && exStatus.Contains(order.ExtField1.ToString2()) && (refundStatus == "WAIT_SELLER_AGREE" || refundStatus == "REFUND_SUCCESS"))
                                val = "异常单";
                            else if (status == "sended")
                                val = "待收货";
                            else if (status == "success")
                                val = "交易成功";
                            else if (status == "close" || status == "cancel")
                                val = "交易取消";
                            else if (status == "shipped")
                                val = "已交运";
                            else
                                val = "未知状态";
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "ToAddress")
                        {
                            // 收件人地址判断省市区地址是否合并一列显示
                            if (setting.Export_AddressShowStyle == "Export_Address_Merge")
                            {
                                // 省市区地址在同列显示
                                var address = order.ToProvince.ToString2() + order.ToCity.ToString2() + order.ToCounty.ToString2() + order.ToAddress.ToString2();
                                row.CreateCell(cellIndex).SetCellValue(address);
                                row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                            }
                            else
                            {
                                // 省市区地址在不同列显示
                                row.CreateCell(cellIndex).SetCellValue(order.ToProvince.ToString2());
                                row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 1).SetCellValue(order.ToCity.ToString2());
                                row.GetCell(cellIndex + 1).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 2).SetCellValue(order.ToCounty.ToString2());
                                row.GetCell(cellIndex + 2).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 3).SetCellValue(order.ToAddress.ToString2());
                                row.GetCell(cellIndex + 3).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;

                                isAddrMerge = false;
                            }
                        }
                        else if (field == "PlatformType" || field == "OrderFrom")
                        {
                            // 订单来源
                            var val = dic[field].ToString2().Trim().ToLower() == "alibaba" ? "1688" : dic[field].ToString2().Trim().ToLower();
                            val = val == "importorder" ? "导入单" : (val == "customerorder" ? "录入单" : val);
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "OrderTime")
                        {
                            // 订单日期
                            var status = dic["PlatformStatus"].ToString2().Trim().ToLower();
                            var val = status == "waitbuyerpay" || status == "confirm_goods_but_not_fund" ? dic["CreateTime"].ToString2() : dic["PayTime"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "OrderCount")
                        {
                            // 订单数
                            var count = order.SubOrders?.Count ?? 0;
                            row.CreateCell(cellIndex).SetCellValue(count);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "SellerRemark" || field == "BuyerRemark")
                        {
                            var arr = dic[field].ToString2().Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                            var str = arr == null ? "" : string.Join(";", arr);
                            row.CreateCell(cellIndex).SetCellValue(str);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "ToPhone")
                        {
                            var phone = dic["ToMobile"].ToString2().IsNullOrEmpty() ? dic["ToPhone"].ToString2() : dic["ToMobile"].ToString2();
                            if (order.PlatformType == PlatformType.WxVideo.ToString())
                            {
                                if (order.ExtField2.IsNotNullOrEmpty())
                                    phone = $"{(order.ToPhone ?? order.ToMobile)}-{order.ExtField2}";
                            }
                            row.CreateCell(cellIndex).SetCellValue(phone);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "SenderPhone")
                        {
                            var phone = dic["SenderMobile"].ToString2().IsNullOrEmpty() ? dic["SenderPhone"].ToString2() : dic["SenderMobile"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(phone);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "PlatformOrderId")
                        {
                            //// 2022-03-17改为仅导出系统单号
                            //var pids = order.OrderItems.Select(x => x.OrignalOrderId.IsNotNullOrEmpty() ? x.OrignalOrderId : x.LogicOrderId).Distinct().ToList();
                            //row.CreateCell(cellIndex).SetCellValue(string.Join("\n", pids));
                            row.CreateCell(cellIndex).SetCellValue(platformOrderIds);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "SenderCompany")
                        {
                            string expressName = "";
                            if (isCrossBorderSite)
                            {
                                expressName = order.WaybillCodes?.FirstOrDefault(f => f.LogisticType == 1)?.ExpressName ?? "";
                            }
                            else
                            {
                                expressName = order.WaybillCodes?.FirstOrDefault()?.ExpressName ?? "";
                            }

                            row.CreateCell(cellIndex).SetCellValue(expressName);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "LastWaybillCode")
                        {
                            var printHistoryStr = string.Empty;
                            var isWaitSend = order.PlatformStatus == "waitsellersend";

                            if (order.WaybillCodes != null && order.WaybillCodes.Any())
                            {
                                if (isCrossBorderSite)
                                {
                                    var waybillCodes = order.WaybillCodes.Where(w => w.LogisticType == 1).OrderByDescending(x => x.CreateDate).Select(m => m.WaybillCode).Distinct().ToList();
                                    printHistoryStr = string.Join("\n", waybillCodes);
                                }
                                else
                                {
                                    var waybillCodes = order.WaybillCodes.OrderByDescending(x => x.CreateDate).Select(m => m.WaybillCode).Distinct().ToList();
                                    printHistoryStr = string.Join("\n", waybillCodes);
                                }

                            }
                            // 所有订单页面仅显示已打印和未打印，待打页面显示的是自己可以打印的订单（自营或者最后厂家）显示物流单号
                            if (type == ExportType.ErpAllOrder && isCrossBorderSite == false)
                            {
                                printHistoryStr = isWaitSend ? (printHistoryStr.IsNotNullOrEmpty() ? "已打印" : "未打印") : printHistoryStr;
                            }

                            row.CreateCell(cellIndex).SetCellValue(printHistoryStr);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "TotalAmount")
                        {
                            // 销售价不可见只显示为空
                            var totalAmount = order.IsShowSalePrice ? order.OrderItems.Sum(x => x.ItemAmount ?? 0).ToString2() : "无权限";
                            //京东的ItemAmount 不会平摊优惠金额， 会导致订单金额和页面显示的订单实付不一致
                            if (order.PlatformType == PlatformType.Jingdong.ToString())
                                totalAmount = order.IsShowSalePrice ? order.TotalAmount.ToString2() : "无权限";
                            //禾量的ItemAmount 是商品的单价，会导致订单金额和页面显示的订单实付不一致
                            if (order.PlatformType == PlatformType.Other_Heliang.ToString())
                                totalAmount = order.IsShowSalePrice ? order.TotalAmount.ToString2() : "无权限";
                            row.CreateCell(cellIndex).SetCellValue(totalAmount);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "ShippingFee") // 运费
                        {
                            // 销售价不可见只显示为空
                            var shippingFee = order.IsShowSalePrice ? order.ShippingFee.ToString2() : "无权限";
                            row.CreateCell(cellIndex).SetCellValue(shippingFee);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "PlatformSubsidy") // 平台补贴
                        {
                            // 销售价不可见只显示为空
                            var platformSubsidy = order.IsShowSalePrice ? order.PlatformSubsidy.ToString2() : "无权限";
                            row.CreateCell(cellIndex).SetCellValue(platformSubsidy);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "PayTotalAmount")// 实收款 
                        {
                            // 销售价不可见只显示为空
                            var payTotalAmount = order.IsShowSalePrice ? order.PayTotalAmount.ToString2() : "无权限";
                            row.CreateCell(cellIndex).SetCellValue(payTotalAmount);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "ToCountry")
                        {
                            ///发往国家
                            var toCountry = order.ToCountry;
                            row.CreateCell(cellIndex).SetCellValue(toCountry);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "Currency")
                        {
                            ///币种
                            row.CreateCell(cellIndex).SetCellValue(order.Currency);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "PackageStatus")
                        {
                            ///交运状态
                            var packageStatus = order.LogisticStatus == 2 ? "已交运" : "未交运";
                            row.CreateCell(cellIndex).SetCellValue(packageStatus);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else
                        {
                            object obj;
                            var isInDic = dic.TryGetValue(field, out obj);
                            var val = isInDic ? obj.ToString2() : "";
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = field == "SenderAddress" ? (row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle) : (row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle);
                        }
                    }
                    else if (from == "productMerge")
                    {
                        // 商品相关信息所在列的序号
                        if (!productCellIndexLst.Contains(cellIndex))
                            productCellIndexLst.Add(cellIndex);

                        if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
                        {
                            #region 商品信息合并1行1列显示
                            var productContent = new StringBuilder();
                            // 获取商品信息
                            order.OrderItems.ForEach(item =>
                            {
                                var itemDic = item.ToDictionary();
                                var productSubContent = new StringBuilder();
                                // 商品信息合并1列显示
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;

                                    var val = string.Empty;
                                    if (field2 == "SkuAttr")
                                        val = itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2();
                                    else if (field2 == "ProductStatus")
                                    {
                                        val = itemDic["RefundStatus"].ToString2();
                                        val = val == RefundStatusType.WAIT_SELLER_AGREE.ToString() ? "退款中" :
                                              val == RefundStatusType.REFUND_SUCCESS.ToString() ? "退款成功" : val;
                                    }
                                    else if (field2 == "Count")
                                        val = $"{itemDic[field2].ToString2()},";
                                    else if (field2 == "Price")
                                        val = order.IsShowSalePrice ? $"{itemDic[field2].ToString2()}," : "";
                                    else if (field2 == "Weight" || field2 == "SkuWeight")
                                        val = itemDic[field2].ToString2() == "0" ? "" : itemDic[field2].ToString2();
                                    else if (field == "DownFxUserSettlementPrice")
                                    {
                                        var value = itemDic[field].ToString2();
                                        if (value.IsNotNullOrEmpty())
                                        {
                                            val = value.Equals("-99") ? "**" : value;
                                        }
                                        else
                                        {
                                            val = "未设置";
                                        }

                                    }
                                    else if (field == "AuthorDownSettlementPrice")
                                    {
                                        var value = itemDic[field].ToString2();
                                        if (value.IsNotNullOrEmpty())
                                        {
                                            val = value.Equals("-99") ? "**" : value;
                                        }
                                        else
                                        {
                                            val = "未设置";
                                        }
                                    }
                                    else if (field == "UpFxUserSettlementPrice")
                                    {
                                        var value = itemDic[field].ToString2();
                                        if (value.IsNotNullOrEmpty())
                                        {
                                            val = value.Equals("-99") ? "**" : value;
                                        }
                                        else
                                        {
                                            val = "未设置";
                                        }
                                    }
                                    else if (field == "AuthorUpSettlementPrice")
                                    {
                                        var value = itemDic[field].ToString2();
                                        if (value.IsNotNullOrEmpty())
                                        {
                                            val = value.Equals("-99") ? "**" : value;
                                        }
                                        else
                                        {
                                            val = "未设置";
                                        }
                                    }
                                    else if (field2 == "ProductSubject")
                                    {
                                        val = itemDic[field2].ToString2();
                                        if (!string.IsNullOrWhiteSpace(val) && order.IsShowProductTitle == false)
                                        {
                                            val = "合作方已设为隐藏信息";
                                        }
                                    }
                                    else
                                        val = itemDic[field2].ToString2();
                                    productSubContent.Append($"{val},");
                                });

                                productContent.Append($"{productSubContent.ToString2().TrimEnd(",")};\n");
                            });

                            row.CreateCell(cellIndex).SetCellValue(productContent.ToString2());
                            row.GetCell(cellIndex).CellStyle = contentLongStyle;
                            #endregion
                        }
                        else
                        {
                            #region 商品多行列合并显示
                            var isFirstItemRowIndex = true;
                            itemRowIndex = index;
                            // 获取商品信息
                            order.OrderItems.ForEach(item =>
                            {
                                var itemDic = item.ToDictionary();
                                var productContent = new StringBuilder();
                                // 商品信息合并1列显示
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = string.Empty;
                                    if (field2 == "SkuAttr")
                                        val = itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2();
                                    else if (field2 == "ProductStatus")
                                    {
                                        val = itemDic["RefundStatus"].ToString2();
                                        val = val == RefundStatusType.WAIT_SELLER_AGREE.ToString() ? "退款中" :
                                              val == RefundStatusType.REFUND_SUCCESS.ToString() ? "退款成功" : val;
                                    }
                                    else if (field2 == "Count")
                                        val = $"{itemDic[field2].ToString2()},";
                                    else if (field2 == "Price")
                                        val = order.IsShowSalePrice ? $"{itemDic[field2].ToString2()}," : "";
                                    else if (field2 == "Weight" || field2 == "SkuWeight")
                                        val = itemDic[field2].ToString2() == "0" ? "" : itemDic[field2].ToString2();
                                    else if (field == "DownFxUserSettlementPrice")
                                    {
                                        var value = itemDic[field].ToString2();
                                        if (value.IsNotNullOrEmpty())
                                        {
                                            val = value.Equals("-99") ? "**" : value;
                                        }
                                        else
                                        {
                                            val = "未设置";
                                        }

                                    }
                                    else if (field == "AuthorDownSettlementPrice")
                                    {
                                        var value = itemDic[field].ToString2();
                                        if (value.IsNotNullOrEmpty())
                                        {
                                            val = value.Equals("-99") ? "**" : value;
                                        }
                                        else
                                        {
                                            val = "未设置";
                                        }
                                    }
                                    else if (field == "UpFxUserSettlementPrice")
                                    {
                                        var value = itemDic[field].ToString2();
                                        if (value.IsNotNullOrEmpty())
                                        {
                                            val = value.Equals("-99") ? "**" : value;
                                        }
                                        else
                                        {
                                            val = "未设置";
                                        }
                                    }
                                    else if (field == "AuthorUpSettlementPrice")
                                    {
                                        var value = itemDic[field].ToString2();
                                        if (value.IsNotNullOrEmpty())
                                        {
                                            val = value.Equals("-99") ? "**" : value;
                                        }
                                        else
                                        {
                                            val = "未设置";
                                        }
                                    }
                                    else if (itemDic.ContainsKey(field2))
                                        val = itemDic[field2].ToString2();
                                    else if (field2 == "ProductSubject")
                                    {
                                        val = itemDic[field2].ToString2();
                                        if (!string.IsNullOrWhiteSpace(val) && order.IsShowProductTitle == false)
                                        {
                                            val = "合作方已设为隐藏信息";
                                        }
                                    }


                                    productContent.Append($"{val},");
                                });

                                if (isFirstItemRowIndex)
                                {
                                    row.CreateCell(cellIndex).SetCellValue(productContent.ToString2().TrimEnd(","));
                                    row.GetCell(cellIndex).CellStyle = contentLongStyle;
                                    isFirstItemRowIndex = false;
                                }
                                else
                                {
                                    // 创建新行填充商品信息
                                    IRow row2 = sheet.GetRow(itemRowIndex) ?? sheet.CreateRow(itemRowIndex);
                                    row2.Height = 25 * 20;
                                    // 商品单独行显示，无订单项数据
                                    row2.CreateCell(cellIndex).SetCellValue(productContent.ToString2().TrimEnd(","));
                                    row2.GetCell(cellIndex).CellStyle = contentLongStyle;
                                }

                                itemRowIndex++;
                            });
                            #endregion
                        }
                    }
                    else if (from == "product" && setting.Export_ProductShowStyle != "Export_ShowOneLine")
                    {
                        // 商品相关信息所在列的序号
                        if (!productCellIndexLst.Contains(cellIndex))
                            productCellIndexLst.Add(cellIndex);

                        #region 商品属性多列显示
                        var isFirstItemRowIndex = true;
                        itemRowIndex = index;
                        // 获取商品信息
                        order.OrderItems.ForEach(item =>
                        {
                            var itemDic = item.ToDictionary();
                            var val = string.Empty;
                            try
                            {
                                if (field == "SkuAttr")
                                    val = itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2();
                                else if (field == "ProductStatus")
                                {
                                    val = itemDic["RefundStatus"].ToString2();
                                    val = val == RefundStatusType.WAIT_SELLER_AGREE.ToString() ? "退款中" :
                                          val == RefundStatusType.REFUND_SUCCESS.ToString() ? "退款成功" : val;
                                }
                                else if (field == "Count")
                                    val = $"{itemDic[field].ToString2()}";
                                else if (field == "Price")
                                    val = order.IsShowSalePrice ? $"{itemDic[field].ToString2()}" : "";
                                else if (field == "Weight" || field == "SkuWeight")
                                    val = itemDic[field].ToString2() == "0" ? "" : itemDic[field].ToString2();
                                else if (field == "DownFxUserSettlementPrice")
                                {
                                    var value = itemDic[field].ToString2();
                                    if (value.IsNotNullOrEmpty())
                                    {
                                        val = value.Equals("-99") ? "**" : value;
                                    }
                                    else
                                    {
                                        val = "未设置";
                                    }

                                }
                                else if (field == "AuthorDownSettlementPrice")
                                {
                                    var value = itemDic[field].ToString2();
                                    if (value.IsNotNullOrEmpty())
                                    {
                                        val = value.Equals("-99") ? "**" : value;
                                    }
                                    else
                                    {
                                        val = "未设置";
                                    }
                                }
                                else if (field == "UpFxUserSettlementPrice")
                                {
                                    var value = itemDic[field].ToString2();
                                    if (value.IsNotNullOrEmpty())
                                    {
                                        val = value.Equals("-99") ? "**" : value;
                                    }
                                    else
                                    {
                                        val = "未设置";
                                    }
                                }
                                else if (field == "AuthorUpSettlementPrice")
                                {
                                    var value = itemDic[field].ToString2();
                                    if (value.IsNotNullOrEmpty())
                                    {
                                        val = value.Equals("-99") ? "**" : value;
                                    }
                                    else
                                    {
                                        val = "未设置";
                                    }
                                }
                                else if (field == "ProductSubject")
                                {
                                    val = itemDic[field].ToString2();
                                    if (!string.IsNullOrWhiteSpace(val) && order.IsShowProductTitle == false)
                                    {
                                        val = "合作方已设为隐藏信息";
                                    }
                                }
                                else
                                {
                                    if (itemDic.ContainsKey(field) == false)
                                    {
                                        Log.WriteError($"导出订单获取商品信息找不到Key={field}，itemDic={itemDic.ToJson()}", "ExportOrderErrLog.txt");
                                        val = "";
                                    }
                                    else
                                        val = itemDic[field].ToString2();
                                }

                                if (isFirstItemRowIndex)
                                {
                                    row.CreateCell(cellIndex).SetCellValue(val);
                                    row.GetCell(cellIndex).CellStyle = field == "ProductSubject" ? contentLongStyle : contentStyle;
                                    isFirstItemRowIndex = false;
                                }
                                else
                                {
                                    // 创建新行填充商品信息
                                    IRow row2 = sheet.GetRow(itemRowIndex) ?? sheet.CreateRow(itemRowIndex);
                                    row2.Height = 25 * 20;
                                    row2.CreateCell(cellIndex).SetCellValue(val);
                                    row2.GetCell(cellIndex).CellStyle = field == "ProductSubject" ? contentLongStyle : contentStyle;
                                }
                                itemRowIndex++;
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError($"导出订单获取商品信息找不到Key={field}，itemDic={itemDic.ToJson()}\n异常信息：{ex}", "ExportOrderErrLog.txt");
                                throw ex;
                            }
                        });
                        #endregion
                    }
                    #endregion
                });

                // 商品多行显示，填充订单数据
                int itemCount = order.OrderItems.Count;
                if (setting.Export_ProductShowStyle == "Export_Export_Split" && itemCount > 1)
                {
                    var tmpRowIndex = index;
                    IRow firstRow = sheet.GetRow(tmpRowIndex);
                    if (firstRow != null)
                    {
                        var firstCells = firstRow.Cells;
                        for (var i = 1; i < itemCount; i++)
                        {
                            // 商品单独行显示，并填充订单项数据
                            IRow nextRow = sheet.GetRow(tmpRowIndex + i);
                            if (nextRow == null) break;
                            for (var j = 0; j < firstCells.Count; j++)
                            {
                                if (!productCellIndexLst.Contains(j))
                                {
                                    var cell = firstCells[j];
                                    if (cell == null) continue;
                                    nextRow.CreateCell(j).SetCellValue(cell.ToString2());
                                    nextRow.GetCell(j).CellStyle = cell.CellStyle;
                                }
                            }
                        }
                    }
                }

                if (itemRowIndex > index)
                    index = itemRowIndex;
                else
                    index++;

                ExcelHelper.AutoSizeRowHeight(workbook, sheet, row);
            });

            return workbook;
        }
        /// <summary>
        /// 转为导出Order模型，带有达人字段权限判断
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public static Order ConvertToOrder(LogicOrder o, List<LogicOrder> lorders)
        {
            string buyerRemark = o.BuyerRemark;
            string sellerRemark = o.SellerRemark;
            string printRemark = o.PrintRemark;
			string systemRemark = o.SystemRemark;

            if (o.IsMainOrder && o.ChildLogicOrderIds != null)
            {
                buyerRemark += "\n";
				sellerRemark += "\n";
				printRemark += "\n";
				systemRemark += "\n";

				o.ChildLogicOrderIds.ForEach(item => {
                    var lorderModel = lorders.FirstOrDefault(f=>f.LogicOrderId == item);
                    if (lorderModel != null)
                    {
                        if (!string.IsNullOrWhiteSpace(lorderModel.BuyerRemark))
                        {
                            buyerRemark += lorderModel.BuyerRemark + "\n";
						}

						if (!string.IsNullOrWhiteSpace(lorderModel.SellerRemark))
						{
							sellerRemark += lorderModel.SellerRemark + "\n";
						}

						if (!string.IsNullOrWhiteSpace(lorderModel.PrintRemark))
						{
							printRemark += lorderModel.PrintRemark + "\n";
						}

						if (!string.IsNullOrWhiteSpace(lorderModel.SystemRemark))
						{
							systemRemark += lorderModel.SystemRemark + "\n";
						}
					}
				});
			}

			// LogicOrder转成Order
			var order = new Order
            {
                PlatformOrderId = o.PlatformOrderId,
                ShopId = o.ShopId,
                PlatformType = o.PlatformType,
                LogicOrderId = o.LogicOrderId,
                MergeredOrderId = o.MergeredOrderId,
                IsMergered = o.MergeredType == 2 || o.MergeredType == 1,
                ToName = o.ToName,
                ToMobile = o.ToPhone,
                ToProvince = o.ToProvince,
                ToCity = o.ToCity,
                ToCounty = o.ToCounty,
                ToAddress = o.ToAddress,
                ToFullAddress = o.ToFullAddress,
                BuyerRemark = buyerRemark,
                SellerRemark = sellerRemark,
                CreateTime = o.CreateTime,
                PayTime = o.PayTime,
                LastSendTime = o.OnlineSendTime,
                TotalWeight = o.TotalWeight,
                TotalAmount = o.TotalAmount,
                LastShipTime = o.LastShipTime,
                LastExpressPrintTime = o.ExpressPrintTime,
                IsPreviewed = o.IsPreviewed,
                LastWaybillCode = o.LastWaybillCode,
                ExpressName = o.WaybillCodes?.FirstOrDefault()?.ExpressName,
                AllDeliveredTime = o.OnlineSendTime,
                WaybillCodes = o.WaybillCodes,
                PlatformStatus = o.ErpState,
                RefundStatus = o.ErpRefundState,
                ExtField1 = o.ExceptionStatus.ToString2(),
                FxUserId = o.FxUserId,
                UpFxUserId = o.UpFxUserId,
                DownFxUserId = o.DownFxUserId,
                CustomerOrderId = o.CustomerOrderId,
                IsShowSalePrice = o.IsShowSalePrice,
                SystemRemark = systemRemark,
                PrintRemark = printRemark,
                ShippingFee = o.ShippingFee ?? 0,
                PlatformSubsidy = o.PlatformSubsidy ?? 0,
                PayTotalAmount = (o.SumTotalAmount ?? 0) + (o.PlatformSubsidy ?? 0),
                ToCountry = o.ToCountry,
                LogisticStatus = o.LogisticStatus,
                IsShowProductTitle = o.IsShowProductTitle,
                IsShowProductImg = o.IsShowProductImg,
                AgentShopName = o.AgentShopName
            };
            o.LogicOrderItems.ForEach(oi =>
            {
                //只有商家自己可见达人字段
                if (o.UpFxUserId != 0)
                {
                    oi.AuthorId = "*";
                    oi.AuthorName = "*";
                }
                var item = new OrderItem();
                if (o.PlatformType == PlatformType.Virtual.ToString())
                    item.OrignalPlatformOrderId = oi.OrignalOrderId.IsNotNullOrEmpty() ? oi.OrignalOrderId : oi.PlatformOrderId;
                else
                    item.OrignalPlatformOrderId = oi.PlatformOrderId;
                item.OrignalOrderId = oi.OrignalOrderId;
                item.PlatformOrderId = oi.PlatformOrderId;
                item.LogicOrderId = oi.LogicOrderId;
                item.Status = oi.Status;
                item.RefundStatus = oi.RefundStatus;
                item.ProductSubject = oi.ProductSubject;
                item.productCargoNumber = oi.productCargoNumber;
                item.CargoNumber = oi.CargoNumber;
                item.Price = oi.Price;
                item.Count = oi.Count;
                item.Color = oi.Color;
                item.Size = oi.Size;
                item.ShortTitle = oi.ShortTitle;
                item.SkuShortTitle = oi.SkuShortTitle;
                if (oi.Weight > 0) //手动设置优先
                    item.Weight = oi.Weight;
                item.SkuWeight = oi.SkuWeight;

                item.SkuID = oi.SkuID;
                item.UserId = o.FxUserId;
                item.ShopId = oi.ShopId;
                item.ProductID = oi.ProductID;
                item.UpFxUserSettlementPrice = oi.UpFxUserSettlementPrice;
                item.DownFxUserSettlementPrice = oi.DownFxUserSettlementPrice;
                item.AuthorDownSettlementPrice = oi.AuthorDownSettlementPrice;
                item.AuthorUpSettlementPrice = oi.AuthorUpSettlementPrice;


                item.ItemAmount = oi.ItemAmount;
                item.AuthorId = oi.AuthorId;//达人Id
                item.AuthorName = oi.AuthorName;//达人名称
                order.OrderItems.Add(item);
            });

            if (o.PlatformType == PlatformType.WxVideo.ToString())
            {
                order.ExtField1 = o.ExtField1;
                order.ExtField2 = o.ExtField2;
                order.BuyerMemberId = o.DecryptField; //对应的是openId，参与合并计算列
            }
            return order;
        }

        public static List<Order> ConvertToOrders(List<LogicOrder> lorders, List<CheckedItem> checkedItemList,bool isQueryColdAndHotDb)
        {
            var orders = new List<Order>();
            if (lorders == null || lorders.Any() == false)
                return orders;

            List<LogicOrder> childLorders = new List<LogicOrder>();
			if (lorders.Any(w => w.IsMainOrder) && (checkedItemList.Any(w => w.Value.Equals("Export_BuyerRemark") || w.Value.Equals("Export_SellerRemark") || w.Value.Equals("Export_PrintRemark") || w.Value.Equals("Export_SystemRemark"))))
            {
                //需要查子订单信息
                List<string> childOrderIdList = new List<string>();
                foreach (var item in lorders.Where(w => w.IsMainOrder && w.ChildLogicOrderIds != null))
                {
					childOrderIdList.AddRange(item.ChildLogicOrderIds);
				}

                //分页查
                int size = 500;
                int total = Math.Ceiling(childOrderIdList.Count * 1.0 / size).ToInt();

                ColdLogicOrderService logicOrderService = new ColdLogicOrderService(isQueryColdAndHotDb);
				for (var i = 0; i < total; i++)
				{
                    try
                    {
                        var newChildOrderIdList = childOrderIdList.Skip(i * size).Take(size).ToList();
                        var list = logicOrderService.GetListByExportExcel(newChildOrderIdList);
                        if (list != null && list.Count > 0)
                        {
                            childLorders.AddRange(list);
                        }
                    }
                    catch (Exception e)
                    {
                       Utility.Log.WriteError($"导出excel查询子订单数据出现异常,{e}", $"ExportExcel_{DateTime.Now:yyyy-MM-dd}.log");
                    }
				}
			}

            lorders.ForEach(lo =>
            {
                orders.Add(ConvertToOrder(lo, childLorders));
            });
            return orders;
        }
        #endregion

        #region 订单分发-发货订单 Excel导出

        public static IWorkbook BuildFxSendOrderExcel(List<SendHistoryModel> sendHistorys, ExportSetting setting, string path, string fileName)
        {
            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("发货明细");

            if (sendHistorys == null || !sendHistorys.Any())
                return workbook;

            //var curShop = SiteContext.Current.CurrentLoginShop;
            //var curFxUser = SiteContext.Current.CurrentFxUser;
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook);
            ICellStyle contentLongStyle = GetContentCellStyleByToLongCol(workbook);
            ICellStyle contentMergeStyle = GetContentCellStyleByToMerge(workbook);
            ICellStyle contentLongMergeStyle = GetContentCellStyleByToLongColMerge(workbook);
            IRow header = sheet.CreateRow(0);
            header.Height = 15 * 20;

            // 发货订单商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 子订单导出内容
            var childOrderChkItems = setting.CheckedItems.Where(m => m.From == "child").ToList();
            // 主订单导出内容
            var mainOrderChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "child" && m.From != "productMerge").ToList();
            List<string> headNames = setting.CheckedItems.Select(m => m.Text).ToList();
            // 商品信息单列显示，列头移除单列商品信息
            if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
            {
                headNames = setting.CheckedItems.Where(m => m.From != "product").Select(m => m.Text).ToList();
            }
            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();
                    headNames.RemoveAt(headIndex);
                    headNames.InsertRange(headIndex, addr);

                    mainOrderChkItems.RemoveAt(headIndex);
                    mainOrderChkItems.InsertRange(headIndex, addrLst);
                }
            }

            // 第一行填充表头信息和样式
            int index = 0;
            // 设置Excel列头内容和样式
            headNames.ForEach(name =>
            {
                //设置列宽度
                //sheet.SetColumnWidth(index, 30 * 256);
                SetColumnWidth(sheet, name, index);
                // 设置列名和样式
                header.CreateCell(index).SetCellValue(name);
                header.GetCell(index).CellStyle = headStyle;
                index++;
            });

            // 第二行开始填充Excel内容
            var shIndex = 1;
            index = 1;
            var isAddrMerge = true;
            sendHistorys.ForEach(s =>
            {
                #region 填充主发货订单的内容
                IRow mainRow = sheet.CreateRow(index);
                mainRow.Height = 20 * 20;

                // 合并订单行内容
                List<CellRangeAddress> regionLst = new List<CellRangeAddress>();
                // 发货订单，以及订单商品合并行
                var mainProductCount = s.SendOrders.Sum(x => x.SendProducts.Count());
                if (mainProductCount > 1 && mainOrderChkItems.Count > 0)
                {
                    // 主订单合并行
                    mainOrderChkItems.ForEach(item =>
                    {
                        var itemIndex = headNames.IndexOf(item.Text);
                        if (itemIndex != -1)
                        {
                            // 主订单内容行合并
                            int startRowIndex = index;
                            int endRowNumIndex = index + mainProductCount - 1;
                            var mainOrderRegion = new CellRangeAddress(startRowIndex, endRowNumIndex, itemIndex, itemIndex);
                            sheet.AddMergedRegion(mainOrderRegion);
                        }
                    });
                }

                var platformOrderIds = s.SendOrders.Select(x => x.PlatformOrderId).Distinct().ToList();
                mainOrderChkItems.ForEach(chkItem =>
                {
                    var field = chkItem.Value.Replace("Export_", "");
                    var text = chkItem.Text;
                    var from = chkItem.From;

                    // 收件人地址不合并1列显示，则移除收件人地址之后列往后移动3列
                    var cellIndex = setting.CheckedItems.IndexOf(chkItem);
                    if (!isAddrMerge)
                    {
                        cellIndex = cellIndex + 3;
                    }
                    if (field == "Index")
                    {
                        mainRow.CreateCell(cellIndex).SetCellValue(shIndex);
                        mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (field == "MainPlatformOrderId")
                    {
                        mainRow.CreateCell(cellIndex).SetCellValue(string.Join("\r\n", platformOrderIds));
                        mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (field == "ShopName")
                    {
                        mainRow.CreateCell(cellIndex).SetCellValue(s.AgentName);
                        mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (field == "SupplierName")
                    {
                        mainRow.CreateCell(cellIndex).SetCellValue(s.SupplierName);
                        mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (field == "ToAddress")
                    {
                        // 收件人地址判断省市区地址是否合并一列显示
                        if (setting.Export_AddressShowStyle == "Export_Address_Merge")
                        {
                            // 省市区地址在同列显示
                            //var address = s.ToProvince.ToString2() + s.ToCity.ToString2() + s.ToCounty.ToString2() + order.ToAddress.ToString2();
                            mainRow.CreateCell(cellIndex).SetCellValue(s.ReciverAddress);
                            mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else
                        {
                            // 省市区地址在不同列显示
                            mainRow.CreateCell(cellIndex).SetCellValue(s.ToProvince.ToString2());
                            mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                            mainRow.CreateCell(cellIndex + 1).SetCellValue(s.ToCity.ToString2());
                            mainRow.GetCell(cellIndex + 1).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                            mainRow.CreateCell(cellIndex + 2).SetCellValue(s.ToDistrict.ToString2());
                            mainRow.GetCell(cellIndex + 2).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                            var addr = s.ReciverAddress.Replace($"{s.ToProvince}{s.ToCity}{s.ToDistrict}", "");
                            mainRow.CreateCell(cellIndex + 3).SetCellValue(addr);
                            mainRow.GetCell(cellIndex + 3).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;

                            isAddrMerge = false;
                        }
                    }
                    else if (field == "PlatformType")
                    {
                        mainRow.CreateCell(cellIndex).SetCellValue(s.PlatformType);
                        mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (field == "LastSendTime")
                    {
                        // 发货时间
                        mainRow.CreateCell(cellIndex).SetCellValue(s.SendDate);
                        mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (field == "ToPhone")
                    {
                        mainRow.CreateCell(cellIndex).SetCellValue(s.ReciverPhone);
                        mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                    }
                    else if (field == "ToName")
                    {
                        mainRow.CreateCell(cellIndex).SetCellValue(s.Reciver);
                        mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                    }
                    else if (field == "SenderCompany")
                    {
                        mainRow.CreateCell(cellIndex).SetCellValue(s.ExpressName);
                        mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                    }
                    else if (field == "LastWaybillCode")
                    {
                        mainRow.CreateCell(cellIndex).SetCellValue(s.LogistiscBillNo);
                        mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                    }
                    else if (field == "WbyStatus")
                    {
                        mainRow.CreateCell(cellIndex).SetCellValue(s.Status == 2 ? "回收" : "已发货");
                        mainRow.GetCell(cellIndex).CellStyle = mainRow.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                    }

                });
                #endregion

                s.SendOrders.ForEach(o =>
                {
                    #region 填充子发货订单内容
                    IRow childRow = sheet.GetRow(index) ?? sheet.CreateRow(index);
                    childRow.Height = 25 * 20;
                    // 子订单合并行
                    if (o.SendProducts.Count > 1 && childOrderChkItems.Count > 0)
                    {
                        childOrderChkItems.ForEach(item =>
                        {
                            var colIndex = headNames.IndexOf(item.Text);
                            if (colIndex != -1)
                            {
                                // 商品信息之前的内容行合并
                                int startRowIndex = index;
                                int endRowNumIndex = index + o.SendProducts.Count - 1;
                                var productRegion = new CellRangeAddress(startRowIndex, endRowNumIndex, colIndex, colIndex);
                                sheet.AddMergedRegion(productRegion);
                            }
                        });
                    }

                    childOrderChkItems.ForEach(chkItem =>
                    {
                        var field = chkItem.Value.Replace("Export_", "");
                        var text = chkItem.Text;
                        var from = chkItem.From;
                        var cellIndex = setting.CheckedItems.IndexOf(chkItem);

                        #region 子订单内容

                        if (field == "PlatformOrderId")
                        {
                            childRow.CreateCell(cellIndex).SetCellValue(o.PlatformOrderId);
                            childRow.GetCell(cellIndex).CellStyle = childRow.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "SendType")
                        {
                            childRow.CreateCell(cellIndex).SetCellValue(o.SendType == 1 ? "二次发货" : "首次发货");
                            childRow.GetCell(cellIndex).CellStyle = childRow.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "OrderType")
                        {
                            childRow.CreateCell(cellIndex).SetCellValue(o.IsPartSend ? "部分发货" : "整单发货");
                            childRow.GetCell(cellIndex).CellStyle = childRow.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        #endregion
                    });
                    #endregion

                    o.SendProducts.ForEach(p =>
                    {
                        #region 填充发货订单商品内容
                        IRow productRow = sheet.GetRow(index) ?? sheet.CreateRow(index);
                        productRow.Height = 25 * 20;
                        productChkItems.ForEach(chkItem =>
                        {
                            var field = chkItem.Value.Replace("Export_", "");
                            var text = chkItem.Text;
                            var from = chkItem.From;
                            var cellIndex = setting.CheckedItems.IndexOf(chkItem);
                            var val = string.Empty;
                            if (field == "SkuAttr")
                                val = p.Color.ToString2() + " " + p.Size.ToString2();
                            else if (field == "ProductStatus")
                            {
                                var status = string.Empty;
                                if (p.Status == OrderStatusType.waitsellersend.ToString())
                                    status = "待发货";
                                else if (p.Status == OrderStatusType.waitbuyerreceive.ToString())
                                    status = "待收货";
                                else if (p.Status == OrderStatusType.success.ToString())
                                    status = "交易成功";
                                else if (p.Status == "close" || p.Status == OrderStatusType.cancel.ToString())
                                    status = "交易取消";
                                else
                                    status = "未知";

                                var refundStatus = string.Empty;
                                if (refundStatus == RefundStatusType.REFUND_SUCCESS.ToString())
                                    refundStatus = "退款成功";
                                else if (refundStatus == RefundStatusType.REFUND_CLOSE.ToString())
                                    refundStatus = "退款关闭";
                                else if (refundStatus == RefundStatusType.REFUND_CLOSE.ToString())
                                    refundStatus = "退款中";

                                if (p.Status == "locked")
                                    val = "待发货 退款中";
                                else
                                    val = status + " " + refundStatus;
                            }
                            else if (field == "Count")
                                val = $"{p.Count.ToString2()}";
                            else if (field == "Price")
                                val = s.IsShowSalePrice ? $"{p.Price.ToString2()}" : "";
                            else if (field == "Weight" || field == "SkuWeight")
                                val = p.Weight.ToString2() == "0" ? "" : p.Weight.ToString2();
                            else if (field == "productCargoNumber")
                                val = p.ProductCargoNumber;
                            else if (field == "ShortTitle")
                                val = p.ShortTitle;
                            else if (field == "SkuShortTitle")
                                val = p.SkuShortTitle;
                            else if (field == "SkuWeight")
                                val = p.SkuWeight.ToString2() == "0" ? "" : p.SkuWeight.ToString2();
                            else if (field == "ProductSubject")
                                val = p.ProductSubject.ToString2();

                            productRow.CreateCell(cellIndex).SetCellValue(val);
                            productRow.GetCell(cellIndex).CellStyle = field == "ProductSubject" ? contentLongStyle : contentStyle;

                            ExcelHelper.AutoSizeRowHeight(workbook, sheet, productRow);
                        });

                        #endregion
                        index++;
                    });
                });
                shIndex++;
            });

            return workbook;
        }

        public static IWorkbook BuildFxSendOrderExcelWithXml(List<SendHistoryModel> sendHistorys, ExportSetting setting, string path, string fileName)
        {
            #region 导出项对应表头处理
            //Text作为表头不能重复
            var gCheckItems = setting.CheckedItems.GroupBy(x => x.Text).Where(x => x.Count() > 1).ToList();
            if (gCheckItems.Count > 0)
            {
                gCheckItems.ForEach(g =>
                {
                    var items = setting.CheckedItems.Where(x => x.Text == g.Key).Skip(1).ToList();
                    if (items != null && items.Any())
                    {
                        for (int i = 0; i < items.Count; i++)
                        {
                            items[i].Text = items[i].Text + i;
                        }
                    }
                });
            }

            if (CustomerConfig.CloudPlatformType != CloudPlatformType.TouTiao.ToString())
            {
                //抖店云才有这三个字段
                setting.CheckedItems.RemoveAll(i => i.Value.Equals("Export_ShippingFee") || i.Value.Equals("Export_PlatformSubsidy") || i.Value.Equals("Export_PayTotalAmount"));
            }
            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();
                    setting.CheckedItems.RemoveAt(headIndex);
                    setting.CheckedItems.InsertRange(headIndex, addrLst);
                }
            }
            // 发货订单商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 子订单导出内容
            var childOrderChkItems = setting.CheckedItems.Where(m => m.From == "child").ToList();
            // 主订单导出内容
            var mainOrderChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "child" && m.From != "productMerge").ToList();

            var headNameDic = new Dictionary<string, int>();
            var headNames = setting.CheckedItems.Select(m => m.Text).ToList();
            // 商品信息单列显示，列头移除单列商品信息
            if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
            {
                headNames = setting.CheckedItems.Where(m => m.From != "product").Select(m => m.Text).ToList();
            }

            #endregion

            var colunms = BuildExccelService.GetOrderColumns(headNames);
            var guid = Guid.NewGuid().ToString().Replace("-", "").ToLower();
            //var fileName = $"发货明细-{guid}.xlsx";
            var fullPath = Path.Combine(path, fileName);

            XlsxOutputHelper xlsxOutput = new XlsxOutputHelper(fullPath);
            //xlsxOutput.BeginGenerate(new List<string> { "sheet1" });

            //1、获取数据并生成Excel
            var isGenerated = xlsxOutput.GenerateSheetNew2(colunms, "sheet1", 0, (pIndex, toalCount, flag) =>
            {
                var data = FxSendOrderToDataTable(sendHistorys, headNames, setting);
                return data;
            });
            var totalCount = xlsxOutput.TotalCnt;
            if (!isGenerated)
            {
                throw new LogicException($"Excel生成失败，请联系我们");
            }
            IWorkbook workbook = null;
            var isOk = xlsxOutput.EndGenerate(isGenerated, (endmsg) =>
            {
                try
                {
                    if (isGenerated)
                        workbook = ExcelHelper.GetWorkbook(fullPath);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"删除临时文件filePath【{fullPath}】失败，异常消息：{ex}");
                }
                finally
                {
                    //删除临时文件
                    if (File.Exists(fullPath))
                        File.Delete(fullPath);
                }
            });
            return workbook;
        }

        /// <summary>
        /// 转为发货明细导出模型，带有达人字段权限判断
        /// </summary>
        /// <param name="sendHistorys"></param>
        /// <param name="headNames"></param>
        /// <param name="setting"></param>
        /// <returns></returns>
        public static DataPackage FxSendOrderToDataTable(List<SendHistoryModel> sendHistorys, List<string> headNames, ExportSetting setting)
        {
            var dt = new DataTable();
            headNames.ForEach(col =>
            {
                dt.Columns.Add(new DataColumn(col));
            });
            var data = new DataPackage(new ParamModel { IsSucess = true, Table = dt, IsReturn = true, Flag = 0, MergeInfos = new List<MergeCellRangeAddress>() });

            var curShop = SiteContext.Current.CurrentLoginShop;
            //var curFxUser = SiteContext.Current.CurrentFxUser;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var isShowCrossBorder = new CommonSettingService().IsShowCrossBorder();//SiteContext.Current.IsShowCrossBorder;//是否开启跨境
            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();
                    setting.CheckedItems.RemoveAt(headIndex);
                    setting.CheckedItems.InsertRange(headIndex, addrLst);
                }
            }
            // 发货订单商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 子订单导出内容
            var childOrderChkItems = setting.CheckedItems.Where(m => m.From == "child").ToList();
            // 主订单导出内容
            var mainOrderChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "child" && m.From != "productMerge").ToList();

            //获取列表头起始位置
            var headDic = new Dictionary<string, int>();
            for (int i = 0; i < setting.CheckedItems.Count; i++)
            {
                if (!headDic.ContainsKey(setting.CheckedItems[i].Text))
                    headDic.Add(setting.CheckedItems[i].Text, i);
            }

            #region 收件人信息脱敏
            //EncryptionService DataMaskservice = new EncryptionService();
            //var noVirtualOrders = DataMaskservice.getPlatformType(sendHistorys);
            //EncryptionService.DataMaskingExpression(sendHistorys.Where(w=>noVirtualOrders.Any(a=>a.LogicOrderId == w.SendOrderId))?.ToList());//EncryptionService.DataMaskingReflection(sendHistorys);

            var notEncryptOrders = sendHistorys.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.ReciverPhone?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
            notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
            if (notEncryptOrders.Any())
                EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);

            #endregion
            #region 组装DataTable

            // 第二行开始填充Excel内容
            var shIndex = 1; // 前面查询后已经task.PageIndex++
            var index = dt.Rows.Count;
            var isAddrMerge = true;
            sendHistorys.ForEach(s =>
            {
                #region 填充主发货订单的内容

                var row = dt.NewRow();
                dt.Rows.Add(row);
                // 发货订单，以及订单商品合并行
                var mainProductCount = s.SendOrders.Sum(x => x.SendProducts.Count());
                if (mainProductCount > 1 && mainOrderChkItems.Count > 0)
                {
                    // 主订单合并行
                    mainOrderChkItems.ForEach(chkItem =>
                    {
                        #region 商品信息显示在多行(合并行)，即订单信息多行合并，商品信息多行显示 记录合并坐标
                        if (setting.Export_ProductShowStyle == "Export_ShowMutilLine_Merge" && mainProductCount > 1 && headDic.ContainsKey(chkItem.Text))
                        {
                            var beginCol = headDic[chkItem.Text];
                            var endCol = beginCol;
                            var beginRowIndex = dt.Rows.Count + 1;//表头占1行，从2行开始
                            var endRowIndex = beginRowIndex + mainProductCount - 1;
                            data.Model.MergeInfos.Add(new MergeCellRangeAddress { BeginCol = beginCol, BeginRowIndex = beginRowIndex, EndCol = endCol, EndRowIndex = endRowIndex });
                        }
                        #endregion
                    });
                }

                var platformOrderIds = s.SendOrders.Select(x => x.PlatformOrderId).Distinct().ToList();
                mainOrderChkItems.ForEach(chkItem =>
                {
                    var field = chkItem.Value.Replace("Export_", "");
                    var text = chkItem.Text;
                    var from = chkItem.From;

                    //// 收件人地址不合并1列显示，则移除收件人地址之后列往后移动3列
                    //var cellIndex = setting.CheckedItems.IndexOf(chkItem);
                    //if (!isAddrMerge)
                    //    cellIndex = cellIndex + 3;

                    if (field == "Index")
                        row[text] = shIndex;
                    else if (field == "MainPlatformOrderId")
                        row[text] = string.Join("\r\n", platformOrderIds);
                    else if (field == "ShopName")
                    {
                        if (!string.IsNullOrWhiteSpace(s.AgentShopName))
                        {
                            row[text] = s.AgentShopName;
                        }
                        else
                        {
                            row[text] = s.AgentName;
                        }

                    }
                    else if (field == "SupplierName")
                        row[text] = s.SupplierName;
                    else if (field == "ToAddress")
                    {
                        // 收件人地址判断省市区地址是否合并一列显示
                        if (setting.Export_AddressShowStyle == "Export_Address_Merge")
                            row[text] = s.ReciverAddress;// 省市区地址在同列显示
                        else
                        {
                            // 省市区地址在不同列显示
                            row["收件省"] = s.ToProvince.ToString2();
                            row["收件市"] = s.ToCity.ToString2();
                            row["收件区"] = s.ToDistrict.ToString2();
                            var addr = s.ReciverAddress.Replace($"{s.ToProvince}{s.ToCity}{s.ToDistrict}", "");
                            row["收件详细地址"] = addr;
                            isAddrMerge = false;
                        }
                    }
                    else if (field == "PlatformType")
                        row[text] = s.PlatformType.ToString2();
                    else if (field == "LastSendTime")
                        row[text] = s.SendDate.ToString2();
                    else if (field == "ToPhone")
                        row[text] = s.ReciverPhone.ToString2();
                    else if (field == "ToName")
                        row[text] = s.Reciver.ToString2();
                    else if (field == "SenderCompany")
                        row[text] = s.ExpressName.ToString2();
                    else if (field == "LastWaybillCode")
                        row[text] = s.LogistiscBillNo;
                    else if (field == "WbyStatus")
                        row[text] = s.Status == 2 ? "回收" : "已发货";
                    if (isShowCrossBorder)
                    {
                        if (field == "PackageStatus")//包裹状态
                        {
                            row[text] = "已交运";
                        }
                        else if (field == "Collectiontype")//揽收方式 //上门揽收/自行寄送
                        {
                            row[text] = s.Collectiontype == "DOOR_TO_DOOR" ? "上门揽收" : "自行寄送";
                        }
                        else if (field == "Currency")//币种
                        {
                            if (string.IsNullOrWhiteSpace(s.ToCountry))
                                row[text] = null;
                            row[text] = CommUtls.GetCountryCurrencyDic(s.ToCountry);
                        }
                        else if (field == "ToCountry")
                        { //国家
                            row[text] = s.ToCountry;
                        }
                    }
                });

                #endregion

                int sendOrdersIndex = 0;
                s.SendOrders.ForEach(o =>
                {
                    sendOrdersIndex++;

                    #region 填充子发货订单内容

                    // 子订单合并行
                    if (o.SendProducts.Count > 1 && childOrderChkItems.Count > 0)
                    {
                        childOrderChkItems.ForEach(chkItem =>
                        {
                            if (headDic.ContainsKey(chkItem.Text))
                            {
                                var beginCol = headDic[chkItem.Text];
                                var endCol = beginCol;
                                var beginRowIndex = dt.Rows.Count + 1;//表头占1行，从2行开始

                                if (sendOrdersIndex > 1) // 合并动作不是发生在首行数据，都需要跳过首行
                                    beginRowIndex++; 

                                var endRowIndex = beginRowIndex + o.SendProducts.Count - 1;
                                data.Model.MergeInfos.Add(new MergeCellRangeAddress { BeginCol = beginCol, BeginRowIndex = beginRowIndex, EndCol = endCol, EndRowIndex = endRowIndex });
                            }
                        });
                    }

                    DataRow childRow = null;
                    if (dt.Rows.Count > index)
                        childRow = dt.Rows[index];
                    else
                    {
                        childRow = dt.NewRow();
                        dt.Rows.Add(childRow);
                    }
                    childOrderChkItems.ForEach(chkItem =>
                    {
                        var field = chkItem.Value.Replace("Export_", "");
                        var text = chkItem.Text;
                        var from = chkItem.From;
                        //var cellIndex = setting.CheckedItems.IndexOf(chkItem);

                        #region 子订单内容

                        if (field == "PlatformOrderId")
                            childRow[text] = o.PlatformOrderId;
                        else if (field == "CreateTime")
                            childRow[text] = o.CreateTime;
                        else if (field == "PayTime")
                            childRow[text] = o.PayTime;
                        else if (field == "PlatformOrderId")
                            childRow[text] = o.PlatformOrderId;
                        else if (field == "SendType")
                        {
                            var txt = "其他";
                            if (o.SendType == 0)
                                txt = "首次发货";
                            else if (o.SendType == 1)
                                txt = "补发货";
                            else if (o.SendType == 2)
                                txt = "换货";
                            else if (o.SendType == 3)
                                txt = "变更单号";
                            else if (o.SendType == 10)
                                txt = "额外运单号";
                            else if (o.SendType == 99)
                                txt = "其他";
                            childRow[text] = txt;
                        }
                        else if (field == "OrderType")
                            childRow[text] = o.IsPartSend ? "部分发货" : "整单发货";
                        else if (field == "ShippingFee")
                            childRow[text] = s.IsShowSalePrice ? o.ShippingFee.ToString2() : "无权限";
                        else if (field == "PlatformSubsidy")
                            childRow[text] = s.IsShowSalePrice ? o.PlatformSubsidy.ToString2() : "无权限";
                        else if (field == "PayTotalAmount")
                            childRow[text] = s.IsShowSalePrice ? o.PayTotalAmount.ToString2() : "无权限";
                        #endregion
                    });
                    #endregion

                    o.SendProducts.ForEach(p =>
                    {
                        //只有商家自己可见达人字段
                        if (s.UpFxUserId != 0)
                        {
                            p.AuthorId = "*";
                            p.AuthorName = "*";
                        }
                        #region 填充发货订单商品内容
                        DataRow productRow = null;
                        if (dt.Rows.Count > index)
                            productRow = dt.Rows[index];
                        else
                        {
                            productRow = dt.NewRow();
                            dt.Rows.Add(productRow);
                        }

                        productChkItems.ForEach(chkItem =>
                        {
                            var field = chkItem.Value.Replace("Export_", "");
                            var text = chkItem.Text;
                            var from = chkItem.From;
                            //var cellIndex = setting.CheckedItems.IndexOf(chkItem);
                            var val = string.Empty;
                            if (field == "SkuAttr")
                                val = $"{p.Color.ToString2()} {p.Size.ToString2()}_{p.SkuId}";
                            else if (field == "ProductStatus")
                            {
                                var status = string.Empty;
                                if (p.Status == OrderStatusType.waitsellersend.ToString())
                                    status = "待发货";
                                else if (p.Status == OrderStatusType.waitbuyerreceive.ToString())
                                    status = "待收货";
                                else if (p.Status == OrderStatusType.success.ToString())
                                    status = "交易成功";
                                else if (p.Status == "close" || p.Status == OrderStatusType.cancel.ToString())
                                    status = "交易取消";
                                else
                                    status = "未知";

                                var refundStatus = string.Empty;
                                if (p.RefundStatus == RefundStatusType.REFUND_SUCCESS.ToString())
                                    refundStatus = "退款成功";
                                else if (p.RefundStatus == RefundStatusType.REFUND_CLOSE.ToString())
                                    refundStatus = "退款关闭";
                                else if (p.RefundStatus == RefundStatusType.WAIT_SELLER_AGREE.ToString())
                                    refundStatus = "退款中";

                                if (p.Status == "locked")
                                    val = "待发货 退款中";
                                else
                                    val = status + " " + refundStatus;
                            }
                            else if (field == "Count")
                                val = $"{p.Quantity.ToString2()}";
                            else if (field == "Price")
                                val = s.IsShowSalePrice ? $"{p.Price.ToString2()}" : "";
                            else if (field == "Price")
                                val = s.IsShowSalePrice ? $"{p.Price.ToString2()}" : "";
                            else if (field == "UpFxUserSettlementPrice")
                            {
                                if (p.UpFxUserSettlementPrice == -99)
                                    val = "**";
                                else val = p.UpFxUserSettlementPrice == 0 ? $"/" : p.UpFxUserSettlementPrice.ToString2();
                            }
                            else if (field == "DownFxUserSettlementPrice")
                            {
                                if (p.DownFxUserSettlementPrice == -99)
                                    val = "**";
                                else val = p.DownFxUserSettlementPrice == 0 ? $"/" : p.DownFxUserSettlementPrice.ToString2();
                            }
                            else if (field == "AuthorUpSettlementPrice")
                            {
                                if (p.AuthorUpSettlementPrice == -99)
                                    val = "**";
                                else val = p.AuthorUpSettlementPrice == 0 ? $"/" : p.AuthorUpSettlementPrice.ToString2();
                            }
                            else if (field == "AuthorDownSettlementPrice")
                            {
                                if (p.AuthorDownSettlementPrice == -99)
                                    val = "**";
                                else val = p.AuthorDownSettlementPrice == 0 ? $"/" : p.AuthorDownSettlementPrice.ToString2();
                            }
                            else if (field == "Weight" || field == "SkuWeight")
                                val = p.Weight.ToString2() == "0" ? "" : p.Weight.ToString2();
                            else if (field == "productCargoNumber")
                                val = p.ProductCargoNumber;
                            else if (field == "CargoNumber")
                                val = p.CargoNumber;
                            else if (field == "SkuId")
                                val = p.SkuId;
                            else if (field == "ShortTitle")
                                val = p.ShortTitle;
                            else if (field == "SkuShortTitle")
                                val = p.SkuShortTitle;
                            else if (field == "SkuWeight")
                                val = p.SkuWeight.ToString2() == "0" ? "" : p.SkuWeight.ToString2();
                            else if (field == "ProductSubject")
                            {
                                string subject = p.ProductSubject.ToString2();
                                if (string.IsNullOrWhiteSpace(subject) && s.IsShowProductTitle == false)
                                {
                                    subject = "合作方已设为隐藏信息";
                                }
                                val = $"{subject}_{p.ProductId}";

                            }
                            else if (field == "AuthorId")
                                val = p.AuthorId.ToString2();
                            else if (field == "AuthorName")
                                val = p.AuthorName.ToString2();

                            productRow[text] = val;
                        });

                        #endregion

                        index++;
                    });
                });
                shIndex++;
            });
            #endregion
            return data;
        }


        #endregion

        #region 订单分发-底单导出

        public static List<string> GetWaybillCodeExportFields(WaybillCodeRequestModel model, List<WaybillCodeCheckModel> checkedFieldsLst)
        {
            var fields = checkedFieldsLst.Select(m => m.Value).Distinct().ToList();
            if (fields.Count == 0)
                throw new LogicException("请先设置导出配置，再进行导出操作");

            #region 导出字段处理
            if (fields.Contains("RowIndex"))
                fields.Remove("RowIndex");
            if (fields.Contains("ShopName"))
            {
                fields.Remove("ShopName");
                if (fields.Contains("ShopId") == false)
                    fields.Add("ShopId");
            }
            if (fields.Contains("ToAddress"))
            {
                if (fields.Contains("ToProvince") == false)
                    fields.Add("ToProvince");
                if (fields.Contains("ToCity") == false)
                    fields.Add("ToCity");
                if (fields.Contains("ToDistrict") == false)
                    fields.Add("ToDistrict");
            }
            if (fields.Contains("OrderId") == false)
                fields.Add("OrderId");
            if (fields.Contains("CustomerOrderId") == false)
                fields.Add("CustomerOrderId");
            if (fields.Contains("OrderIdJoin") == false)
                fields.Add("OrderIdJoin");
            if (fields.Contains("Id") == false)
                fields.Add("Id");
            if (fields.Contains("ShopId") == false)
                fields.Add("ShopId");
            if (fields.Contains("ExpressWayBillCode") == false)
                fields.Add("ExpressWayBillCode");
            if (fields.Contains(model.OrderByField) == false)
                fields.Add(model.OrderByField);
            #endregion


            fields = fields.ConvertAll(f => "wc." + f).ToList();
            return fields;
        }

        public static IWorkbook BuildFxWaybillCodeExcel(WaybillCodeRequestModel reqModel, bool isCj, List<WaybillCode> waybillCodeLst, List<WaybillCodeCheckModel> checkedItems, string fileName)
        {
            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet();
            if (waybillCodeLst == null || !waybillCodeLst.Any())
                return workbook;

            if (CustomerConfig.IsCrossBorderSite)
            {//跨境合单会关联逻辑单数据 需去重
                waybillCodeLst = waybillCodeLst.GroupBy(item => item.ID).Select(item => item.First()).ToList();
            }

            //底单中保存的订单编号为逻辑订单号，需要转换成原始订单编号，才能解密收件人信息
            if (checkedItems.Any(x => x.Value == "Reciver" || x.Value == "ReciverPhone" || x.Value == "ToAddress"))
            {
                // 2025-1-22 这些平台不需要再解密-脱敏
                var notPlatformType = new string[] { PlatformType.TouTiao.ToString(), PlatformType.TouTiaoSaleShop.ToString() };

                var newWaybillCodeLst = waybillCodeLst.Where(x => !notPlatformType.Any(p => p == x.PlatformType)).ToList();

                FxPlatformEncryptService.EncryptWaybillCodes(newWaybillCodeLst, encryptSender: true, needSyncLog: true);

                #region 收件人信息脱敏
                //EncryptionService DataMaskservice = new EncryptionService();
                //var noVirtualOrders = DataMaskservice.getPlatformType(waybillCodeLst);
                //EncryptionService.DataMaskingExpression(waybillCodeLst.Where(w=> noVirtualOrders.Any(a=> a.LogicOrderId == w.OrderId) )?.ToList());//EncryptionService.DataMaskingReflection(waybillCodeLst);

                var notEncryptOrders = newWaybillCodeLst.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.ReciverPhone?.Contains("*") == false || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
                notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
                if (notEncryptOrders.Any())
                    EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);


                #endregion
            }


            #region 查询逻辑单获取商家信息
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var sids = waybillCodeLst.Where(x => x.FxUserId == fxUserId).Select(x => x.SourceShopId).Distinct().ToList();
            var agentBindShops = new List<Shop>();
            var agentUsers = new List<SupplierUser>();
            var orderKvs = new List<KeyValuePair<string, LogicOrder>>();
            var wOrderKvs = new List<KeyValuePair<string, WaybillCodeOrder>>();
            // 获取平台单号
            if (checkedItems.Any(x => x.Value == "OrderId"))
            {
                var oids = waybillCodeLst.Where(x => x.CustomerOrderId.IsNullOrEmpty() && x.OrderId.StartsWith("C") == false).Select(x => x.OrderId).ToList();
                var moids = waybillCodeLst.Where(x => x.OrderId.ToString2().StartsWith("C")).SelectMany(x => x.OrderIdJoin.ToString2().SplitToList(",")).ToList();
                if (moids.Any())
                    oids.AddRange(moids);

                if (CustomerConfig.IsCrossBorderSite)
                {
                    var oids2 = waybillCodeLst.Select(x => x.OrderId).ToList();
                    oids.AddRange(oids2);
                }

                oids = oids.Distinct().ToList();
                if (oids.Any())
                {
                    //交易完成的订单，逻辑单,会在一定时间内从数据库清除。
                    //var orders = new LogicOrderService().OnlyGetLogicOrders(oids, new List<string> { "LogicOrderId", "PlatformOrderId" });
                    //var orderDic = orders.GroupBy(x => x.LogicOrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault());
                    //orderKvs = orderDic.ToList();

                    var waybillCodeOrders = new WaybillCodeOrderService().GetWaybillCodeOrders(oids, new List<string> { "OrderId", "CustomerOrderId" });
                    var orderDic = waybillCodeOrders.GroupBy(x => x.OrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault());
                    wOrderKvs = orderDic.ToList();
                }
            }
            //获取订单对应的商家信息
            if (checkedItems.Any(x => x.Value == "ShopName"))
            {
                var pathflowCodes = waybillCodeLst.Select(x => x.PathFlowCode).Distinct().ToList();
                if (pathflowCodes.Any())
                {
                    var pathFlowNodes = new PathFlowRepository().GetPathFlowNodeList(pathflowCodes);
                    var pathFlowNodeDic = pathFlowNodes.GroupBy(x => x.PathFlowCode).ToDictionary(x => x.Key, x => x.ToList());
                    foreach (var w in waybillCodeLst)
                    {
                        List<PathFlowNode> nodes;
                        if (pathFlowNodeDic.TryGetValue(w.PathFlowCode, out nodes))
                        {
                            w.UpFxUserId = nodes.FirstOrDefault(x => x.FxUserId == fxUserId && x.UpFxUserId > 0)?.UpFxUserId ?? 0;
                            //w.DownFxUserId = nodes.FirstOrDefault(x => x.FxUserId == fxUserId && x.DownFxUserId > 0)?.DownFxUserId ?? 0;
                        }
                    }
                }
                var supplierUserRepository = new SupplierUserRepository();
                if (isCj)
                {
                    agentUsers = supplierUserRepository.GetByFxUserId(fxUserId, false, needEncryptAccount: true); // 当前账号的商家信息
                    sids = waybillCodeLst.Select(x => x.SourceShopId).Distinct().ToList();

                    agentBindShops = new ShopService().GetShopByIds(sids);

                }
                else
                {
                    var tuple = GetAgentBindShops(fxUserId, sids);
                    agentBindShops = tuple.Item1;
                    agentUsers = supplierUserRepository.GetByFxUserId(fxUserId, false, needEncryptAccount: true); // 查商家信息
                }
            }
            #endregion

            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            checkedItems.ForEach(model =>
            {
                var headName = model.Text.ToString2();

                SetColumnWidth(sheet, headName, colIndex);
                headName = headName == "系统单号" ? "订单编号" : headName;
                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;
            waybillCodeLst.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;
                var dic = (model ?? new WaybillCode()).ToDictionary();

                colIndex = 0;
                foreach (var item in checkedItems)
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item?.Value.ToString2();
                    if (dic.ContainsKey(key) || key == "RowIndex" || key == "ShopName")
                    {
                        var val = key == "RowIndex" ? rowIndex.ToString2() : key == "ShopName" ? "" : dic[key].ToString2();
                        if (key != "RowIndex")
                        {
                            if (key == "ToAddress")
                                val = $"{dic["ToProvince"].ToString2()} {dic["ToCity"].ToString2()} {dic["ToDistrict"].ToString2()} {dic["ToAddress"].ToString2()}";
                            else if (key == "BuyerRemark" || key == "SellerRemark")
                            {
                                var arr = val.Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                                val = arr.Length == 0 ? "" : val;
                            }
                            else if (key == "Status")
                            {
                                if (val == "1")
                                    val = "已打印";
                                else if (val == "2")
                                    val = "已回收";
                                else if (val == "3")
                                    val = "已发货";
                                else if (val == "4")
                                    val = "回收失败";
                                else
                                    val = "未知状态";
                            }
                            //发货类型：0=正常；1=补发；2=换货；3=变更单号；99=其他
                            //简化为首次发货、二次发货
                            else if (key == "SendType")
                            {
                                if (val == "" || val == "0")
                                    val = "首次发货";
                                else if (val == "10" || val == "11" || val == "12" || val == "13")
                                    val = "额外运单号";
                                else
                                    val = "二次发货";
                            }
                            else if (key == "ShopName")
                            {
                                var agent = agentUsers?.FirstOrDefault(x => x.FxUserId == model.UpFxUserId);
                                val = agent == null ? agentBindShops.FirstOrDefault(x => x.Id == model.SourceShopId)?.NickName ?? "店铺或商家已解绑" : agent.AgentMobileAndRemark;
                            }
                            else if (key == "OrderId")
                            {
                                var orderIds = dic["OrderIdJoin"].ToString2().SplitToList(",");
                                if (orderIds != null && orderIds.Count > 1)
                                {
                                    // 合单
                                    var pids = wOrderKvs.Where(x => orderIds.Contains(x.Key)).Select(x => x.Value.CustomerOrderId).Distinct().ToList();
                                    val = pids?.ToStringWithSplit("\n");
                                }
                                else
                                {
                                    var oid = orderIds?.FirstOrDefault() ?? "";
                                    val = wOrderKvs.FirstOrDefault(x => x.Key == oid).Value?.CustomerOrderId;
                                    if (val.IsNullOrEmpty())
                                        val = dic["CustomerOrderId"].ToString2();
                                }
                            }
                            else if (key == "ExpressWayBillCode")
                            {
                                var childWaybillCode = dic["ChildWaybillCode"].ToString2();
                                val = $"{val}{(childWaybillCode.IsNullOrEmpty() ? "" : $"/{childWaybillCode}")}";
                            }
                            else if (key == "shipped")//跨境交运
                            {
                                val = "已交运";
                            }
                        }

                        if (key == "BuyerRemark" || key == "SellerRemark" || key == "ToAddress" || key == "SendContent")
                            tmpStyle = leftContentStyle;

                        dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                        dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                        colIndex++;
                    }
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;
            });

            return workbook;
        }

        /// <summary>
        /// 导出异常订单
        /// </summary>
        /// <param name="headers"></param>
        /// <param name="models"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static IWorkbook BuildFxAbnormalOrderExcel(List<ExcelSheetHeaderModel> headers,
            List<OrderAbnormal> models, string fileName)
        {
            var workbook = ExcelHelper.GetNewWorkbook(fileName);
            var sheet = workbook.CreateSheet();
            if (models == null || !models.Any())
            {
                return workbook;
            }

            var headStyle = GetHeadStyle(workbook);
            var contentStyle = GetContentStyle(workbook);
            var headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;
            //表头处理
            var colIndex = 0;
            headers.ForEach(model =>
            {
                var headName = model.HeaderName.ToString2();

                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });
            //表数据处理
            var rowIndex = 1;
            models.ForEach(model =>
            {
                var dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;

                var dic = (model ?? new OrderAbnormal()).ToDictionary();

                colIndex = 0;
                headers.ForEach(header =>
                {
                    var key = header.HeaderField.ToString2();
                    if (dic.ContainsKey(key))
                    {
                        var value = dic[key].ToString2();
                        if (key == "PrintBatchIndex")
                        {
                            value = $"第{value}张";
                        }
                        dataRow.CreateCell(colIndex).SetCellValue(value.Trim().Trim("\n".ToArray()));
                        dataRow.GetCell(colIndex).CellStyle = contentStyle;
                        colIndex++;
                    }
                });
                var heightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = heightInPoints;
                rowIndex++;
            });

            return workbook;
        }

        //底单中保存的订单编号为逻辑订单号，需要转换成原始订单编号，才能解密收件人信息
        public static Dictionary<string, LogicOrder> EncryptReceiverInfo(List<WaybillCode> wlst, string fields = "", bool isFx = false)
        {
            if (wlst == null || !wlst.Any())
                return null;
            if (fields.IsNullOrEmpty())
                fields = "o.Id,o.ShopId,o.LogicOrderId,o.PlatformOrderId,o.MergeredType,o.PlatformType,o.PathFlowCode,oi.Id,oi.PlatformOrderId";
            //底单中保存的订单编号为逻辑订单号，需要转换成原始订单编号，才能解密收件人信息
            var logicOrderDict = new Dictionary<string, LogicOrder>();
            var logicOrderIds = wlst.Select(f => f.OrderId).Distinct().ToList();
            var orders = new LogicOrderService().GetOrders(logicOrderIds, fields: fields.SplitToList(","));

            var mids = logicOrderIds.Where(x => !orders.Any(m => m.LogicOrderId == x)).Select(x => x.Replace("C", "")).ToList();
            if (mids != null && mids.Any())
            {
                var mOrders = new LogicOrderService().GetOrders(mids, fields: fields.SplitToList(","));

                mOrders.ForEach(item => { item.LogicOrderId = "C" + item.LogicOrderId; });

                orders.AddRange(mOrders);
            }

            var cids = orders?.Where(x => x.IsMainOrder).SelectMany(x => x.LogicOrderItems).Select(x => x.PlatformOrderId).Distinct().ToList();
            if (cids != null && cids.Any())
            {
                var childOrders = new LogicOrderService().GetLogicOrders(cids, new List<string> { "LogicOrderId", "PlatformOrderId", "ToName", "ToPhone", "ToAddress", "PathFlowCode" }).ToList();
                orders.AddRange(childOrders);
            }

            orders?.ToList().ForEach(o =>
            {
                LogicOrder order = null;
                if (!logicOrderDict.TryGetValue(o.LogicOrderId, out order))
                    logicOrderDict.Add(o.LogicOrderId, o);
            });

            var isExistJdOrderOrTbOrder = wlst.Any(f =>
            {
                LogicOrder lo;
                if (logicOrderDict.TryGetValue(f.OrderId, out lo))
                {
                    return lo.PlatformType == PlatformType.Jingdong.ToString() || lo.PlatformType == PlatformType.Taobao.ToString();
                }
                return false;
            }) == true;

            var isExistJdOrder = wlst.Any(f =>
            {
                LogicOrder lo;
                if (logicOrderDict.TryGetValue(f.OrderId, out lo))
                {
                    return lo.PlatformType == PlatformType.Jingdong.ToString();
                }
                return false;
            }) == true;

            var isExistTbOrder = wlst.Any(f =>
            {
                LogicOrder lo;
                if (logicOrderDict.TryGetValue(f.OrderId, out lo))
                {
                    return lo.PlatformType == PlatformType.Taobao.ToString();
                }
                return false;
            }) == true;

            var isExistPddOrder = wlst.Any(f =>
            {
                LogicOrder lo;
                if (logicOrderDict.TryGetValue(f.OrderId, out lo))
                {
                    return lo.PlatformType == PlatformType.Pinduoduo.ToString();
                }
                return false;
            }) == true;

            var isExistTuanHaoHuoOrder = wlst.Any(f =>
            {
                LogicOrder lo;
                if (logicOrderDict.TryGetValue(f.OrderId, out lo))
                {
                    return lo.PlatformType == PlatformType.TuanHaoHuo.ToString();
                }
                return false;
            }) == true;

            if (isExistJdOrderOrTbOrder) //存在京东或者淘宝订单
            {
                var tbPids = new List<string>(); //淘宝订单编号
                var jdPids = new List<string>(); //京东订单编号
                wlst.ForEach(t =>
                {
                    LogicOrder lo;
                    logicOrderDict.TryGetValue(t.OrderId, out lo);
                    if (lo == null)
                        return;

                    var pids = new List<string>();
                    if (!string.IsNullOrEmpty(t.OrderIdJoin))
                    {
                        pids.AddRange(t.OrderIdJoin.Split(','));
                    }
                    else
                    {
                        pids.Add(t.OrderId.Trim('C'));
                    }

                    if (lo.PlatformType == PlatformType.Jingdong.ToString())
                        jdPids.AddRange(pids);
                    else if (lo.PlatformType == PlatformType.Taobao.ToString())
                        tbPids.AddRange(pids);


                    t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
                    t.Reciver = t.Reciver.ToEncryptName();
                    t.ToAddress = t.ToAddress.ToTaoBaoEncryptAddress();
                    if (lo?.PlatformType == PlatformType.Taobao.ToString())
                    {
                        //淘宝还需加密发件人信息
                        t.Sender = t.Sender.ToEncryptName();
                    }
                });

                //TODO:要取店铺信息，暂时先不弄，订单这里取店铺信息要通过逻辑单上的额shopId来取
                //if (isExistJdOrder)
                //{   //京东安全日志
                //    jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
                //}
                //else
                //{
                //    //记御城河日志
                //    ych_sdk.YchRequestLogger.Order(shop.Id.ToString(), "订单查询", pids);
                //}
            }
            else if (isExistPddOrder)
            {
                var tempOrders = wlst.Select(x => new Order { PlatformOrderId = x.OrderId, LogicOrderId = x.OrderId, Id = x.ShopId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ToAddress }).ToList();
                //_orderService.TryToDecryptPddOrders(tempOrders);

                var findTmpOrders = new List<Order>(); // 部分订单丢失时，无法找到正确的店铺导致无法解密
                tempOrders.ForEach(to =>
                {
                    LogicOrder lo;
                    if (logicOrderDict.TryGetValue(to.PlatformOrderId, out lo))
                    {
                        to.PlatformOrderId = lo.PlatformOrderId;
                        to.ShopId = lo.ShopId;
                        findTmpOrders.Add(to);
                    }
                });

                //地址中如果包含省市区要去掉
                findTmpOrders.ForEach(x =>
                {
                    var replaceStr = x.ToProvince + x.ToCity + x.ToCounty;
                    x.ToAddress = x.ToAddress.Replace(replaceStr, "");
                });

                //Log.WriteError("tempOrders:" + tempOrders.ToJson());
                try
                {
                    BranchShareRelationService.TryToDecryptPddOrders(findTmpOrders, true, isFx: isFx);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"拼多多解密异常：{ex}");
                }


                //Log.WriteError("解密后tempOrders:" + tempOrders.ToJson());

                //按店铺分组
                foreach (var item in wlst)
                {
                    var decryptedOrder = findTmpOrders.FirstOrDefault(x => x.LogicOrderId == item.OrderId && x.Id == item.ShopId);
                    if (decryptedOrder != null)
                    {
                        item.Reciver = decryptedOrder.ToName;
                        item.ReciverPhone = decryptedOrder.ToMobile;
                        item.BuyerMemberName = item.Reciver;
                        item.BuyerMemberId = item.Reciver;
                        if (string.IsNullOrWhiteSpace(decryptedOrder.ToFullAddress))//如果是非加密信息，上面TryToDecryptPddOrders方法会return抛出，导致ToFullAddress由于没赋值为null
                            decryptedOrder.ToFullAddress = decryptedOrder.ToProvince + decryptedOrder.ToCity + decryptedOrder.ToCounty + decryptedOrder.ToAddress;
                        item.ToAddress = decryptedOrder.ToFullAddress;
                        item.BuyerMemberName = item.BuyerMemberName.ToEncryptName();
                        item.BuyerMemberId = item.BuyerMemberId.ToEncryptName();
                        item.ReciverPhone = item.ReciverPhone.ToPddEncryptPhone();
                        item.Reciver = item.Reciver.ToEncryptName();
                        item.ToAddress = item.ToAddress.ToPddEncryptAddress();
                    }
                }
            }

            if (isExistTuanHaoHuoOrder) //存在团好货订单
            {
                wlst.ForEach(t =>
                {
                    LogicOrder lo;
                    logicOrderDict.TryGetValue(t.OrderId, out lo);
                    if (lo == null)
                        return;

                    t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
                    t.Reciver = t.Reciver.ToEncryptName();
                    t.ToAddress = t.ToAddress.ToTaoBaoEncryptAddress();
                });
            }

            return logicOrderDict;

        }

        #endregion

        #region 订单导出
        public static Order EncryptReceiverInfo(Order order)
        {
            order.ToName = order.ToName.ToEncryptName();
            order.BuyerMemberName = order.BuyerMemberName.ToEncryptName();
            order.BuyerWangWang = order.BuyerWangWang.ToEncryptName();
            order.BuyerMemberId = order.BuyerMemberId.ToEncryptName();
            order.ToPhone = order.ToPhone.ToPddEncryptPhone();
            order.ToMobile = order.ToMobile.ToPddEncryptPhone();
            order.ToAddress = order.ToAddress.ToPddEncryptAddress();
            //order.ToCounty = "**";
            if (order.PlatformType == PlatformType.Taobao.ToString())
            {
                //淘宝还要加密发件人
                order.SenderName = order.SenderName.ToEncryptName();
                order.SenderPhone = order.SenderPhone.ToEncrytPhone();
                order.SenderMobile = order.SenderMobile.ToEncrytPhone();
                order.SenderAddress = order.SenderAddress.ToTaoBaoEncryptAddress();
            }
            return order;
        }

        public static IWorkbook BuildOrderPrintExcel(List<Order> orders, ExportSetting setting, bool isCustomerOrder, string fileName)
        {
            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("订单信息");

            if (orders == null || !orders.Any())
                return workbook;
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (orders != null && orders.Any()
                && (pt == PlatformType.Taobao.ToString()
                || pt == PlatformType.Jingdong.ToString()))
            {
                if (isCustomerOrder == false)
                {
                    orders.ForEach(o =>
                    {
                        EncryptReceiverInfo(o);
                    });
                }
            }

            // 设置发件人
            new OrderService().SetSellerInfo(orders);

            #region 查询底单打印记录
            List<OrderSelectKeyModel> keys = orders.Select(m => new OrderSelectKeyModel()
            {
                Id = m.Id,
                ShopId = m.ShopId,
                PlatformOrderId = m.PlatformOrderId,
                ChildPlatformOrderIds = m.ChildOrderId?.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).ToList()
            }).ToList();

            // 查询打印记录
            var needExportPrintInfo = false;
            var printDic = new Dictionary<string, List<WaybillCode>>();
            var printHistorys = new List<WaybillCode>();
            if (setting.CheckedItems.Any(m => m.Value.ToString2() == "Export_LastWaybillCode" || m.Value.ToString2() == "SenderCompany"))
            {
                needExportPrintInfo = true;
                var fields = new List<string> { "w.ShopId", "w.OrderId", "w.ExpressWayBillCode", "w.ExpressName", "w.GetDate" };
                printHistorys = new WaybillCodeService().GetWaybillCodeList(keys, fields, 1000)?.OrderByDescending(m => m.ID).ToList() ?? new List<WaybillCode>();
                printHistorys.ForEach(p =>
                {
                    var key = p.OrderId + p.ShopId;
                    if (!printDic.ContainsKey(key))
                        printDic.Add(key, new List<WaybillCode> { p });
                    else
                        printDic[key].Add(p);
                });
            }
            #endregion

            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook);
            ICellStyle contentLongStyle = GetContentCellStyleByToLongCol(workbook);
            ICellStyle contentMergeStyle = GetContentCellStyleByToMerge(workbook);
            ICellStyle contentLongMergeStyle = GetContentCellStyleByToLongColMerge(workbook);
            IRow header = sheet.CreateRow(0);
            header.Height = 15 * 20;
            // 获取勾选的商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 除商品信息外勾选的内容
            var otherChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "productMerge").ToList();
            //// 添加省市区详细地址的扩展
            //var extChkItems = JsonExtension.ToList<CheckedItem>(JsonExtension.ToJson(setting.CheckedItems));

            List<string> headNames = setting.CheckedItems.Select(m => m.Text).ToList();
            // 商品信息单列显示，列头移除单列商品信息
            if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
            {
                headNames = setting.CheckedItems.Where(m => m.From != "product").Select(m => m.Text).ToList();
            }
            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();
                    headNames.RemoveAt(headIndex);
                    headNames.InsertRange(headIndex, addr);

                    otherChkItems.RemoveAt(headIndex);
                    otherChkItems.InsertRange(headIndex, addrLst);
                }
            }

            // 第一行填充表头信息和样式
            int index = 0;
            // 设置Excel列头内容和样式
            headNames.ForEach(name =>
            {
                //设置列宽度
                //sheet.SetColumnWidth(index, 30 * 256);
                SetColumnWidth(sheet, name, index);
                // 设置列名和样式
                header.CreateCell(index).SetCellValue(name);
                header.GetCell(index).CellStyle = headStyle;
                index++;
            });

            // 第二行开始填充Excel内容
            index = 1; // 订单总行数
            //var itemRowIndex = 1; // 实际总行数
            var shops = SiteContext.Current.AllShops;
            var orderCategorysSetting = new CommonSettingService().Get("OrderCategorySet", SiteContext.Current.CurrentShopId);
            List<OrderCategory> orderCategorys = orderCategorysSetting == null || orderCategorysSetting.Value.IsNullOrEmpty() ? new List<OrderCategory>() : JsonExtension.ToList<OrderCategory>(orderCategorysSetting.Value.ToString2());

            orders.ForEach(order =>
            {
                var dic = order.ToDictionary();
                IRow row = sheet.CreateRow(index);
                row.Height = 20 * 20;
                var isAddrMerge = true;
                var itemRowIndex = index;

                // 合并订单行内容
                List<CellRangeAddress> regionLst = new List<CellRangeAddress>();
                if (setting.Export_ProductShowStyle == "Export_ShowMutilLine_Merge")
                {
                    if (order.OrderItems.Count > 1 && productChkItems.Count > 0)
                    {
                        otherChkItems.ForEach(item =>
                        {
                            var itemIndex = headNames.IndexOf(item.Text);
                            if (itemIndex != -1)
                            {
                                // 商品信息之前的内容行合并
                                int startRowIndex = index;
                                int endRowNumIndex = index + order.OrderItems.Count - 1;

                                var productRegion = new CellRangeAddress(startRowIndex, endRowNumIndex, itemIndex, itemIndex);
                                sheet.AddMergedRegion(productRegion);
                            }
                        });
                    }
                }

                var platformOrderIds = string.Empty;
                if (isCustomerOrder)
                    platformOrderIds = dic["CustomerOrderId"].ToString2().IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : dic["CustomerOrderId"].ToString2();
                else
                {
                    var childOrderIds = dic["ChildOrderId"].ToString2();
                    platformOrderIds = childOrderIds.IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : childOrderIds.Replace(",", "\n").Replace("，", "\n");
                }

                List<WaybillCode> printHistoryLst = null;
                if (needExportPrintInfo)
                {
                    printDic.TryGetValue(order.PlatformOrderId + order.ShopId, out printHistoryLst);
                    if (printHistoryLst == null)
                        printDic.TryGetValue("C" + order.PlatformOrderId + order.ShopId, out printHistoryLst);
                    if (printHistoryLst == null && !order.MergeredOrderId.IsNullOrEmpty())
                        printDic.TryGetValue(order.MergeredOrderId + order.ShopId, out printHistoryLst);
                }

                // 商品信息所在列序号集合
                var productCellIndexLst = new List<int>();
                // 填充每一列的值
                setting.CheckedItems.ForEach(chkItem =>
                {
                    var field = chkItem.Value.Replace("Export_", "");
                    var text = chkItem.Text;
                    var from = chkItem.From;

                    // 收件人地址不合并1列显示，则移除收件人地址之后列往后移动3列
                    var cellIndex = setting.CheckedItems.IndexOf(chkItem);
                    if (!isAddrMerge)
                    {
                        cellIndex = cellIndex + 3;
                    }

                    #region 填充每列的内容
                    if (from == "shop")
                    {
                        var shop = shops.FirstOrDefault(m => m.Id == order.ShopId);
                        row.CreateCell(cellIndex).SetCellValue(shop == null ? "" : shop.NickName);
                        row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (from == "ordercategory")
                    {
                        var category = orderCategorys.FirstOrDefault(m => m.Id == order.CategoryId);
                        row.CreateCell(cellIndex).SetCellValue(category == null ? "" : category.Alias);
                        row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (from == "order")
                    {
                        // 收件人地址判断省市区地址是否合并一列显示
                        if (field == "ToAddress")
                        {
                            if (setting.Export_AddressShowStyle == "Export_Address_Merge")
                            {
                                // 省市区地址在同列显示
                                var address = order.ToProvince.ToString2() + order.ToCity.ToString2() + order.ToCounty.ToString2() + order.ToAddress.ToString2();
                                row.CreateCell(cellIndex).SetCellValue(address);
                                row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                            }
                            else
                            {
                                // 省市区地址在不同列显示
                                row.CreateCell(cellIndex).SetCellValue(order.ToProvince.ToString2());
                                row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 1).SetCellValue(order.ToCity.ToString2());
                                row.GetCell(cellIndex + 1).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 2).SetCellValue(order.ToCounty.ToString2());
                                row.GetCell(cellIndex + 2).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 3).SetCellValue(order.ToAddress.ToString2());
                                row.GetCell(cellIndex + 3).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;

                                isAddrMerge = false;
                            }
                        }
                        else if (field == "PlatformType" || field == "OrderFrom")
                        {
                            // 订单来源
                            var val = dic[field].ToString2().Trim().ToLower() == "alibaba" ? "1688" : dic[field].ToString2().Trim().ToLower();
                            val = val == "importorder" ? "导入单" : (val == "customerorder" ? "录入单" : val);
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "OrderTime")
                        {
                            // 订单日期
                            var status = dic["PlatformStatus"].ToString2().Trim().ToLower();
                            var val = status == "waitbuyerpay" || status == "confirm_goods_but_not_fund" ? dic["CreateTime"].ToString2() : dic["PayTime"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "OrderCount")
                        {
                            // 订单数
                            var count = order.SubOrders?.Count ?? 0;
                            row.CreateCell(cellIndex).SetCellValue(count);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "SellerRemark" || field == "BuyerRemark")
                        {
                            var arr = dic[field].ToString2().Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                            var str = arr == null ? "" : string.Join(";", arr);
                            row.CreateCell(cellIndex).SetCellValue(str);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "ToPhone")
                        {
                            var phone = dic["ToMobile"].ToString2().IsNullOrEmpty() ? dic["ToPhone"].ToString2() : dic["ToMobile"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(phone);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "SenderPhone")
                        {
                            var phone = dic["SenderMobile"].ToString2().IsNullOrEmpty() ? dic["SenderPhone"].ToString2() : dic["SenderMobile"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(phone);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "PlatformOrderId")
                        {
                            //var platformOrderIds = string.Empty;
                            //if (isCustomerOrder)
                            //    platformOrderIds = dic["CustomerOrderId"].ToString2().IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : dic["CustomerOrderId"].ToString2();
                            //else
                            //{
                            //    var childOrderIds = dic["ChildOrderId"].ToString2();
                            //    platformOrderIds = childOrderIds.IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : childOrderIds.Replace(",", "\n").Replace("，", "\n");
                            //}

                            row.CreateCell(cellIndex).SetCellValue(platformOrderIds);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "SenderCompany")
                        {
                            //var printHistory = printHistorys.Where(c => !c.ExpressName.IsNullOrEmpty() && c.ShopId == order.ShopId && (c.OrderId == order.PlatformOrderId || c.OrderId.ToString2().Contains(order.PlatformOrderId) || (isCustomerOrder ? c.OrderId == order.CustomerOrderId : c.OrderId == order.MergeredOrderId))).OrderByDescending(x => x.GetDate).FirstOrDefault();
                            var printHistory = printHistoryLst?.OrderByDescending(x => x.GetDate).FirstOrDefault();
                            var expressName = printHistory?.ExpressName.ToString2();
                            row.CreateCell(cellIndex).SetCellValue(expressName);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "LastWaybillCode")
                        {
                            var printHistoryStr = string.Empty;
                            //var printHistoryLst = printHistorys.Where(c => !c.ExpressWayBillCode.IsNullOrEmpty() && c.ShopId == order.ShopId && (c.OrderId == order.PlatformOrderId || c.OrderId.ToString2().Contains(order.PlatformOrderId) || (isCustomerOrder ? c.OrderId == order.CustomerOrderId : c.OrderId == order.MergeredOrderId))); 
                            var printHistory = printHistoryLst?.OrderByDescending(c => c.GetDate).FirstOrDefault(); //最近一次打印记录
                            printHistoryLst = printHistory == null ? new List<WaybillCode>() : printHistoryLst.Where(m => m.GetDate == printHistory.GetDate).ToList();//根据打印时间判断是否是一单多包
                            // 一单多包导出多个运单号
                            if (printHistoryLst != null && printHistoryLst.Count() > 1)
                                printHistoryStr = string.Join("\n", printHistoryLst?.Select(m => m.ExpressWayBillCode).ToList() ?? new List<string>());
                            else
                                printHistoryStr = printHistory?.ExpressWayBillCode.ToString2();

                            row.CreateCell(cellIndex).SetCellValue(printHistoryStr);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "LastSendTime")
                        {
                            var lastSendTime = dic["LastSendTime"].ToString2();
                            lastSendTime = lastSendTime.IsNullOrEmpty() ? dic["AllDeliveredTime"].ToString2() : lastSendTime;
                            if (lastSendTime.IsNullOrEmpty() && !order.ChildOrderId.IsNullOrEmpty())
                            {
                                var okeys = platformOrderIds.SplitWithString("\n").Where(m => !m.IsNullOrEmpty()).Select(id => new OrderSelectKeyModel { PlatformOrderId = id, ShopId = order.ShopId, FxUserId = order.FxUserId }).ToList();
                                var childOrders = new OrderService().GetOrders(okeys, fields: new List<string> { "o.Id", "o.PlatformOrderId", "o.ShopId", "o.LastSendTime", "o.AllDeliveredTime", "oi.Id" });
                                var newLastTime = childOrders?.Max(o => o.LastSendTime) ?? childOrders?.Max(o => o.AllDeliveredTime);
                                if (newLastTime != null)
                                    lastSendTime = newLastTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                            }
                            lastSendTime = lastSendTime.IsNullOrEmpty() ? lastSendTime : lastSendTime.toDateTime().ToString("yyyy-MM-dd HH:mm:ss");
                            row.CreateCell(cellIndex).SetCellValue(lastSendTime);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else
                        {
                            row.CreateCell(cellIndex).SetCellValue(dic[field].ToString2());
                            row.GetCell(cellIndex).CellStyle = field == "SenderAddress" ? (row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle) : (row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle);
                        }
                    }
                    else if (from == "productMerge")
                    {
                        // 商品相关信息所在列的序号
                        if (!productCellIndexLst.Contains(cellIndex))
                            productCellIndexLst.Add(cellIndex);

                        if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
                        {
                            #region 商品信息合并1行1列显示
                            var productContent = new StringBuilder();
                            // 获取商品信息
                            order.OrderItems.ForEach(item =>
                            {
                                var itemDic = item.ToDictionary();
                                var productSubContent = new StringBuilder();
                                // 商品信息合并1列显示
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = field2 == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field2].ToString2();
                                    productSubContent.Append(field2 == "Count" ? $"*{val}," : $"{val},");
                                });

                                productContent.Append($"{productSubContent.ToString2().TrimEnd(",")};\n");
                            });

                            row.CreateCell(cellIndex).SetCellValue(productContent.ToString2());
                            row.GetCell(cellIndex).CellStyle = contentLongStyle;
                            #endregion
                        }
                        else
                        {
                            #region 商品多行列合并显示
                            var isFirstItemRowIndex = true;
                            itemRowIndex = index;
                            // 获取商品信息
                            order.OrderItems.ForEach(item =>
                            {
                                var itemDic = item.ToDictionary();
                                var productContent = new StringBuilder();
                                // 商品信息合并1列显示
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = field2 == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field2].ToString2();
                                    productContent.Append(field2 == "Count" ? $"*{val}," : $"{val},");
                                });

                                if (isFirstItemRowIndex)
                                {
                                    row.CreateCell(cellIndex).SetCellValue(productContent.ToString2().TrimEnd(","));
                                    row.GetCell(cellIndex).CellStyle = contentLongStyle;
                                    isFirstItemRowIndex = false;
                                }
                                else
                                {
                                    // 创建新行填充商品信息
                                    IRow row2 = sheet.GetRow(itemRowIndex) ?? sheet.CreateRow(itemRowIndex);
                                    row2.Height = 25 * 20;
                                    // 商品单独行显示，无订单项数据
                                    row2.CreateCell(cellIndex).SetCellValue(productContent.ToString2().TrimEnd(","));
                                    row2.GetCell(cellIndex).CellStyle = contentLongStyle;
                                }

                                itemRowIndex++;
                            });
                            #endregion
                        }
                    }
                    else if (from == "product" && setting.Export_ProductShowStyle != "Export_ShowOneLine")
                    {
                        // 商品相关信息所在列的序号
                        if (!productCellIndexLst.Contains(cellIndex))
                            productCellIndexLst.Add(cellIndex);

                        #region 商品属性多列显示
                        var isFirstItemRowIndex = true;
                        itemRowIndex = index;
                        // 获取商品信息
                        order.OrderItems.ForEach(item =>
                        {
                            var itemDic = item.ToDictionary();
                            var val = field == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field].ToString2();
                            if (isFirstItemRowIndex)
                            {
                                row.CreateCell(cellIndex).SetCellValue(val);
                                row.GetCell(cellIndex).CellStyle = field == "ProductSubject" ? contentLongStyle : contentStyle;
                                isFirstItemRowIndex = false;
                            }
                            else
                            {
                                // 创建新行填充商品信息
                                IRow row2 = sheet.GetRow(itemRowIndex) ?? sheet.CreateRow(itemRowIndex);
                                row2.Height = 25 * 20;
                                row2.CreateCell(cellIndex).SetCellValue(val);
                                row2.GetCell(cellIndex).CellStyle = field == "ProductSubject" ? contentLongStyle : contentStyle;
                            }
                            itemRowIndex++;
                        });
                        #endregion
                    }
                    #endregion
                });

                // 商品多行显示，填充订单数据
                int itemCount = order.OrderItems.Count;
                if (setting.Export_ProductShowStyle == "Export_Export_Split" && itemCount > 1)
                {
                    var tmpRowIndex = index;
                    IRow firstRow = sheet.GetRow(tmpRowIndex);
                    if (firstRow != null)
                    {
                        var firstCells = firstRow.Cells;
                        for (var i = 1; i < itemCount; i++)
                        {
                            // 商品单独行显示，并填充订单项数据
                            IRow nextRow = sheet.GetRow(tmpRowIndex + i);
                            if (nextRow == null) break;
                            for (var j = 0; j < firstCells.Count; j++)
                            {
                                if (!productCellIndexLst.Contains(j))
                                {
                                    var cell = firstCells[j];
                                    if (cell == null) continue;
                                    nextRow.CreateCell(j).SetCellValue(cell.ToString2());
                                    nextRow.GetCell(j).CellStyle = cell.CellStyle;
                                }
                            }
                        }
                    }
                }

                if (itemRowIndex > index)
                    index = itemRowIndex;
                else
                    index++;

                ExcelHelper.AutoSizeRowHeight(workbook, sheet, row);
            });

            return workbook;
        }

        private static ICellStyle GetHeadStyle(IWorkbook workbook)
        {
            IFont font = workbook.CreateFont();
            font.FontName = "Times New Roman";
            font.Boldweight = short.MaxValue;
            font.FontHeightInPoints = 11;

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(font);
            headerStyle.Alignment = HorizontalAlignment.Center;//内容居中显示
            headerStyle.WrapText = true;

            headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightOrange.Index;
            headerStyle.FillPattern = FillPattern.SolidForeground;
            return headerStyle;
        }

        private static ICellStyle GetContentStyle(IWorkbook excel, HorizontalAlignment alignment = HorizontalAlignment.Center)
        {
            IFont font = excel.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = excel.CreateCellStyle();
            contentStyle.SetFont(font);
            contentStyle.Alignment = HorizontalAlignment.Center;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.WrapText = true;

            return contentStyle;
        }

        private static ICellStyle GetContentCellStyleByToLongCol(IWorkbook excel)
        {
            ICellStyle contentStyle = GetContentStyle(excel);
            contentStyle.Alignment = HorizontalAlignment.Left;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            return contentStyle;
        }

        private static void SetColumnWidth(ISheet sheet, string headName, int index)
        {
            int width = 20 * 256;

            if (headName == "序号" || headName == "商品数量" || headName == "订单金额" || headName == "运费" || headName == "重量" || headName == "订单重量" || headName == "商品单价" || headName == "单价"
                || headName == "订单数" || headName == "件数" || headName == "款数" || headName == "金额" || headName == "运费" || headName == "重量(克)" || headName == "数量")
                width = 10 * 256;
            else if (headName == "收件人" || headName == "收件人电话" || headName == "联系电话" || headName == "发件人" || headName == "发件人电话" || headName == "收件省"
                || headName == "收件市" || headName == "收件区" || headName == "售后数量")
                width = 15 * 256;
            else if (headName == "订单来源" || headName == "订单分类" || headName == "店铺" || headName == "店铺名称" || headName == "厂家"
                 || headName == "下单时间" || headName == "付款时间" || headName == "发货时间" || headName == "打印快递单时间" || headName == "打印发货单时间" || headName == "打印拿货标签时间"
                 || headName == "打印快递单次数" || headName == "打印发货单次数" || headName == "打印拿货标签次数"
                 || headName == "日期")
                width = 20 * 256;
            else if (headName == "买家旺旺" || headName == "买家昵称" || headName == "订单编号" || headName == "流水号" || headName == "单品货号" || headName == "商品规格" || headName == "商品销售属性" || headName == "销售属性" || headName == "商品货号" || headName == "快递单号" || headName == "系统单号" || headName == "商品简称" || headName == "规格简称")
                width = 25 * 256;
            else if (headName == "快递公司" || headName == "收件详细地址")
                width = 30 * 256;
            else if (headName == "收件人地址" || headName == "收件地址" || headName == "发件人地址" || headName == "发件地址" || headName == "商品标题" || headName == "商品名称" || headName == "商品信息" || headName == "买家留言" || headName == "卖家备注" || headName == "打印内容")
                width = 35 * 256;
            else if (headName == "售后备注" || headName == "库存商品名称")
                width = 40 * 256;
            else if (headName == "失败原因" || headName == "账单留言（仅对方可见）")
                width = 60 * 256;
            else if (headName == "账单任务导出表头")
                width = 65 * 256;

            sheet.SetColumnWidth(index, width);
        }

        private static ICellStyle GetContentCellStyleByToMerge(IWorkbook excel)
        {
            ICellStyle contentStyle = GetContentStyle(excel);
            contentStyle.Alignment = HorizontalAlignment.Center;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            return contentStyle;
        }

        private static ICellStyle GetContentCellStyleByToLongColMerge(IWorkbook excel)
        {
            ICellStyle contentStyle = GetContentStyle(excel);
            contentStyle.Alignment = HorizontalAlignment.Left;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            return contentStyle;
        }
        #endregion

        #region 底单导出
        private static WaybillCode EncryptReceiverInfo(WaybillCode order)
        {
            order.BuyerMemberName = order.BuyerMemberName.ToEncryptName();
            order.BuyerMemberId = order.BuyerMemberId.ToEncryptName();
            order.ReciverPhone = order.ReciverPhone.ToPddEncryptPhone();
            order.Reciver = order.Reciver.ToEncryptName();
            order.ToAddress = "****";
            order.ToDistrict = "****";
            return order;
        }

        private static WaybillCode EncryptSenderInfo(WaybillCode order)
        {
            order.Sender = order.Sender.ToEncryptName();
            return order;
        }

        public static IWorkbook BuildWaybillCodeExcel(List<WaybillCode> waybillCodeLst, List<WaybillCodeCheckModel> checkedItems, string fileName)
        {
            var shopIds = waybillCodeLst.Select(x => x.ShopId).Distinct().ToList();
            var shops = new ShopRepository().GetShopByIds(shopIds);

            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (waybillCodeLst != null && waybillCodeLst.Any())
            {
                if (pt == PlatformType.Taobao.ToString() || pt == PlatformType.Jingdong.ToString())
                {
                    waybillCodeLst.ForEach(o =>
                    {
                        EncryptReceiverInfo(o);
                    });
                }
                else if (pt == PlatformType.Pinduoduo.ToString())
                {
                    var tempOrders = waybillCodeLst?.Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ToAddress }).ToList();
                    try
                    {
                        BranchShareRelationService.TryToDecryptPddOrders(tempOrders, true);
                    }
                    catch (Exception)
                    {
                    }
                    //按店铺分组
                    foreach (var item in waybillCodeLst)
                    {
                        var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId && x.ShopId == item.ShopId);
                        if (decryptedOrder != null)
                        {
                            item.Reciver = decryptedOrder.ToName.ToEncryptName();
                            item.ReciverPhone = decryptedOrder.ToMobile.ToPddEncryptPhone();
                            item.BuyerMemberName = item.Reciver.ToEncryptName();
                            item.BuyerMemberId = item.Reciver.ToEncryptName();
                            item.ToAddress = decryptedOrder.ToFullAddress.ToPddEncryptAddress();
                        }
                    }
                }
            }

            //淘宝发件人也需要打码
            if (pt == PlatformType.Taobao.ToString())
                waybillCodeLst.ForEach(o =>
                {
                    EncryptSenderInfo(o);
                });

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet();
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            checkedItems.ForEach(model =>
            {
                var headName = model.Text.ToString2();
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;
            waybillCodeLst.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;
                var dic = (model ?? new WaybillCode()).ToDictionary();

                colIndex = 0;
                foreach (var item in checkedItems)
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item?.Value.ToString2();
                    if (dic.ContainsKey(key) || key == "RowIndex" || key == "ShopName")
                    {
                        var val = key == "RowIndex" ? rowIndex.ToString2() : key == "ShopName" ? "" : dic[key].ToString2();
                        if (key != "RowIndex")
                        {
                            if (key == "ToAddress")
                                val = $"{dic["ToProvince"].ToString2()} {dic["ToCity"].ToString2()} {dic["ToDistrict"].ToString2()} {dic["ToAddress"].ToString2()}";
                            else if (key == "BuyerRemark" || key == "SellerRemark")
                            {
                                var arr = val.Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                                val = arr.Length == 0 ? "" : val;
                            }
                            else if (key == "Status")
                            {
                                if (val == "1")
                                    val = "已打印";
                                else if (val == "2")
                                    val = "已回收";
                                else if (val == "3")
                                    val = "已发货";
                                else
                                    val = "未知状态";
                            }
                            else if (key == "ShopName")
                            {
                                val = shops?.FirstOrDefault(m => m.Id == dic["ShopId"].ToInt())?.ShopName ?? "";
                            }
                            else if (key == "OrderId")
                            {
                                var customerOrderId = dic["CustomerOrderId"].ToString2();
                                if (!customerOrderId.IsNullOrEmpty())
                                    val = customerOrderId;
                                else
                                {
                                    var orderIdJoin = dic["OrderIdJoin"].ToString2();
                                    if (!orderIdJoin.IsNullOrEmpty())
                                        val = orderIdJoin.Replace(",", "\n");
                                }
                            }
                            else if (key == "ExpressWayBillCode")
                            {
                                var childWaybillCode = dic["ChildWaybillCode"].ToString2();
                                val = $"{val}{(childWaybillCode.IsNullOrEmpty() ? "" : $"/{childWaybillCode}")}";
                            }
                        }

                        if (key == "BuyerRemark" || key == "SellerRemark" || key == "ToAddress" || key == "SendContent")
                            tmpStyle = leftContentStyle;

                        dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                        dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                        colIndex++;
                    }
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;
            });

            return workbook;
        }

        #endregion

        #region 单号分享使用明细导出
        public static bool IsWdUser(int shopId)
        {
            var select_user_sql = "SELECT t2.* FROM dbo.P_UserShopRelation AS t1 WITH(NOLOCK) INNER JOIN dbo.P_User AS t2 WITH(NOLOCK) ON t1.UserId =t2.Id WHERE t1.ShopId=@sid";
            var users = DbUtility.GetConfigureConnection().Query<User>(select_user_sql, new { sid = shopId });
            return users?.Any(f => f.Type == 1) == true;
        }

        public static IWorkbook BuildBranchShareExcel(List<BranchShareRelation> models, string fileName, List<WaybillCodeSimpleModel> details, bool isFromShop, bool isWdUser)
        {
            details.ForEach(f =>
            {
                if (f.PlatformType == PlatformType.Pinduoduo.ToString()
                      || f.PlatformType == PlatformType.Taobao.ToString()
                      || f.PlatformType == PlatformType.Jingdong.ToString())
                {
                    f.Reciver = f.Reciver.ToEncryptName();
                }


                if (f.PlatformType != "TouTiao" && f.PlatformType != "KuaiShou")
                {
                    f.ReciverPhone = "******";
                    f.Sender = "***";
                    f.SenderPhone = "******";
                    f.SenderAddress = "********";
                }
                else
                    f.ReciverPhone = f.ReciverPhone.ToEncrytPhone();
            });

            //var isFromShop = models.FirstOrDefault().FromId == shopId;
            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            var sheetIndex = 0;
            ISheet sheet = workbook.CreateSheet("分享单号使用情况");
            var maxRowCount = fileName.IndexOf(".xlsx") > 0 ? 1048500 : 65500;

            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook);
            ICellStyle contentLongStyle = GetContentCellStyleByToLongCol(workbook);

            var headNames = new List<string> { "获取时间", "运单号", "平台", "订单编号", "收件人", "省市区", "使用状态" };

            //var isWdUser = IsWdUser(task.ShopId);
            if (isWdUser)
            {
                headNames = new List<string> { "获取时间", "运单号", "平台", "订单编号", "收件人", "收件人电话", "省市区", "发件人", "发件人电话", "发件人地址", "使用状态" };
            }

            // 第二行填充表头信息和样式    
            // 表头
            IRow header = sheet.CreateRow(0);
            header.Height = 15 * 20;
            int cellIndex = 0;
            if (isFromShop)
                headNames.InsertRange(0, new List<string> { "使用单号店铺", "备注" });
            // 设置Excel列头内容和样式
            headNames.ForEach(name =>
            {
                //设置列宽度
                sheet.SetColumnWidth(cellIndex, 20 * 256);
                // 设置列名和样式
                header.CreateCell(cellIndex).SetCellValue(name);
                header.GetCell(cellIndex).CellStyle = headStyle;
                cellIndex++;
            });

            var rowIndex = 1;  // 第2行开始填充Excel内容
            var shareIds = models.Select(m => m.Id).Distinct().ToList();
            TemplateRelationAuthInfoService _templateAuthInfoService = new TemplateRelationAuthInfoService();
            // 根据ShareId获取使用的模板 信息
            var printTemplates = _templateAuthInfoService.GetByShareRelationIds(shareIds);
            var shareTempIds = printTemplates.Where(m => m.TemplateId > 0).Select(m => m.TemplateId).Distinct().ToList();
            models.GroupBy(m => m.Id).ToList().ForEach(g =>
            {
                var model = g.FirstOrDefault();
                var tmpIds = printTemplates.Where(m => m.BranchShareRelationId == model.Id && m.TemplateId > 0).Select(m => m.TemplateId).Distinct().ToList();
                //details?.Where(m => tmpIds.Contains(m.TemplateId)).ToList().ForEach(detail =>
                tmpIds.ForEach(tid =>
                {
                    var tmpDetails = details.Where(m => m.TemplateId == tid).OrderByDescending(m => m.CreateTime).ToList();
                    tmpDetails.ForEach(detail =>
                    {
                        if (sheet.LastRowNum >= maxRowCount)
                        {
                            sheetIndex++;
                            sheet = workbook.CreateSheet("分享单号使用情况" + sheetIndex);
                            rowIndex = 1;

                            header = sheet.CreateRow(0);
                            header.Height = 15 * 20;
                            cellIndex = 0;
                            // 设置Excel列头内容和样式
                            headNames.ForEach(name =>
                            {
                                //设置列宽度
                                sheet.SetColumnWidth(cellIndex, 20 * 256);
                                // 设置列名和样式
                                header.CreateCell(cellIndex).SetCellValue(name);
                                header.GetCell(cellIndex).CellStyle = headStyle;
                                cellIndex++;
                            });
                        }
                        IRow row = sheet.CreateRow(rowIndex);
                        row.Height = 20 * 20;

                        if (isFromShop)
                        {
                            if (isWdUser)
                            {
                                //"使用单号店铺", "备注","获取时间", "运单号", "平台", "订单编号", "收件人", "收件人电话", "省市区", "发件人", "发件人电话", "发件人地址", "使用状态"
                                row.CreateCell(0).SetCellValue(model.ToShopName);
                                row.CreateCell(1).SetCellValue(model.Remark);
                                row.CreateCell(2).SetCellValue(detail.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                row.CreateCell(3).SetCellValue(detail.ExpressWayBillCode.ToString2());
                                row.CreateCell(4).SetCellValue(detail.PlatformTypeName.ToString2());
                                row.CreateCell(5).SetCellValue(detail.OrderId.ToString2());
                                row.CreateCell(6).SetCellValue(detail.Reciver.ToString2());
                                row.CreateCell(7).SetCellValue(detail.ReciverPhone.ToString2());
                                row.CreateCell(8).SetCellValue(detail.ToProvince.ToString2() + detail.ToCity.ToString2() + detail.ToDistrict.ToString2());
                                row.CreateCell(9).SetCellValue(detail.Sender.ToString2());
                                row.CreateCell(10).SetCellValue(detail.SenderPhone.ToString2());
                                row.CreateCell(11).SetCellValue(detail.SenderAddress.ToString2());
                                row.CreateCell(12).SetCellValue(detail.Status.ToString2() == "1" ? "已打印" : detail.Status.ToString2() == "2" ? "已回收" : "已发货");
                            }
                            else
                            {
                                //"使用单号店铺", "备注","获取时间", "运单号", "平台", "订单编号", "收件人", "省市区", "使用状态"
                                row.CreateCell(0).SetCellValue(model.ToShopName);
                                row.CreateCell(1).SetCellValue(model.Remark);
                                row.CreateCell(2).SetCellValue(detail.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                row.CreateCell(3).SetCellValue(detail.ExpressWayBillCode.ToString2());
                                row.CreateCell(4).SetCellValue(detail.PlatformTypeName.ToString2());
                                row.CreateCell(5).SetCellValue(detail.OrderId.ToString2());
                                row.CreateCell(6).SetCellValue(detail.Reciver.ToString2());
                                row.CreateCell(7).SetCellValue(detail.ToProvince.ToString2() + detail.ToCity.ToString2() + detail.ToDistrict.ToString2());
                                row.CreateCell(8).SetCellValue(detail.Status.ToString2() == "1" ? "已打印" : detail.Status.ToString2() == "2" ? "已回收" : "已发货");
                            }
                        }
                        else
                        {
                            if (isWdUser)
                            {
                                //"获取时间", "运单号", "平台", "订单编号", "收件人", "收件人电话", "省市区", "发件人", "发件人电话", "发件人地址", "使用状态"
                                row.CreateCell(0).SetCellValue(detail.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                row.CreateCell(1).SetCellValue(detail.ExpressWayBillCode.ToString2());
                                row.CreateCell(2).SetCellValue(detail.PlatformTypeName.ToString2());
                                row.CreateCell(3).SetCellValue(detail.OrderId.ToString2());
                                row.CreateCell(4).SetCellValue(detail.Reciver.ToString2());
                                row.CreateCell(5).SetCellValue(detail.ReciverPhone.ToString2());
                                row.CreateCell(6).SetCellValue(detail.ToProvince.ToString2() + detail.ToCity.ToString2() + detail.ToDistrict.ToString2());
                                row.CreateCell(7).SetCellValue(detail.Sender.ToString2());
                                row.CreateCell(8).SetCellValue(detail.SenderPhone.ToString2());
                                row.CreateCell(9).SetCellValue(detail.SenderAddress.ToString2());
                                row.CreateCell(10).SetCellValue(detail.Status.ToString2() == "1" ? "已打印" : detail.Status.ToString2() == "2" ? "已回收" : "已发货");
                            }
                            else
                            {
                                //"获取时间", "运单号", "平台", "订单编号", "收件人", "省市区", "使用状态"
                                row.CreateCell(0).SetCellValue(detail.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                row.CreateCell(1).SetCellValue(detail.ExpressWayBillCode.ToString2());
                                row.CreateCell(2).SetCellValue(detail.PlatformTypeName.ToString2());
                                row.CreateCell(3).SetCellValue(detail.OrderId.ToString2());
                                row.CreateCell(4).SetCellValue(detail.Reciver.ToString2());
                                row.CreateCell(5).SetCellValue(detail.ToProvince.ToString2() + detail.ToCity.ToString2() + detail.ToDistrict.ToString2());
                                row.CreateCell(6).SetCellValue(detail.Status.ToString2() == "1" ? "已打印" : detail.Status.ToString2() == "2" ? "已回收" : "已发货");
                            }
                        }

                        row.Cells.ForEach(cell =>
                        {
                            cell.CellStyle = contentStyle;
                        });
                        rowIndex++;
                    });
                });
            });
            return workbook;
        }

        #endregion

        #region 导出售后单
        /// <summary>
        /// 售后单导出处理
        /// </summary>
        /// <param name="list"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static IWorkbook AfterSaleOrderBuildExcel(List<AfterSalePageResult> list, string fileName)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var currentLoginShop = SiteContext.Current.CurrentLoginShop;
            FxUserShopService _fxUserShopService = new FxUserShopService();

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("售后单");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            var sourceFlag = list?.FirstOrDefault()?.Orders.FirstOrDefault()?.SourceFlag ?? 0;

            int colIndex = 0;
            var heads = new Dictionary<string, string>() {
                { "售后单号" ,"AfterSaleId"},
                { "售后申请时间" ,"ApplyTime"},
                { "订单编号" ,"PlatformOrderId"},
                { "订单创建时间" ,"OrderCreateTime"},
                { "来源类型" ,"SourceFlag"},
                { "收件人" ,"ToName"},
                { "店铺名称" ,"ShopName"},
                { "厂家" ,"SupplierName"},
                { "售后类型" ,"AfterSaleType"},
                { "售后状态" ,"AfterSaleStatus"},
                { "退款状态" ,"RefundStatus"},
                { "买家退货物流公司" ,"ReturnCompanyName"},
                { "买家退货物流号" ,"ReturnTrackingNo"},
                { "买家退回时间" ,"ReturnLogisticsTime"},
                { "二次发货物流公司" ,"ResendCompanyName"},
                { "二次发货物流号" ,"ResendTrackingNo"},
                { "二次发货时间" ,"ResendLogisticsTime"},
                { "首次发货物流公司" ,"FirstCompanyName"},
                { "首次发货物流号" ,"FirstTrackingNo"},
                { "商品标题" ,"ProductSubject"},
                { "商品信息" ,"ProductInfo"},
                { "售后数量" ,"AfterSaleCount"},
                { "SKUID" ,"SkuId"},
                { "SKU编码" ,"CargoNumber"},
                { "上游结算价" ,"UpFxUserSettlementPrice"},
                { "下游结算价" ,"DownFxUserSettlementPrice"},
                { "售后留言" ,"AfterSaleRemark"},
                { "售后备注" ,"PlatAfterSaleRemark"},
            };
            if (sourceFlag == 0)
            {
                heads = new Dictionary<string, string>() {
                { "售后单号" ,"AfterSaleId"},
                { "售后申请时间" ,"ApplyTime"},
                { "订单编号" ,"PlatformOrderId"},
                { "订单创建时间" ,"OrderCreateTime"},
                { "来源类型" ,"SourceFlag"},
                { "收件人" ,"ToName"},
                { "发货状态" ,"IsSended"},
                { "店铺名称" ,"ShopName"},
                { "厂家" ,"SupplierName"},
                { "售后类型" ,"AfterSaleType"},
                { "售后状态" ,"AfterSaleStatus"},
                { "退款状态" ,"RefundStatus"},
                { "买家退货物流公司" ,"ReturnCompanyName"},
                { "买家退货物流号" ,"ReturnTrackingNo"},
                { "买家退回时间" ,"ReturnLogisticsTime"},
                { "二次发货物流公司" ,"ResendCompanyName"},
                { "二次发货物流号" ,"ResendTrackingNo"},
                { "二次发货时间" ,"ResendLogisticsTime"},
                { "首次发货物流公司" ,"FirstCompanyName"},
                { "首次发货物流号" ,"FirstTrackingNo"},
                { "商品标题" ,"ProductSubject"},
                { "商品信息" ,"ProductInfo"},
                { "售后数量" ,"AfterSaleCount"},
                { "SKUID" ,"SkuId"},
                { "SKU编码" ,"CargoNumber"},
                { "上游结算价" ,"UpFxUserSettlementPrice"},
                { "下游结算价" ,"DownFxUserSettlementPrice"},
                { "售后留言" ,"AfterSaleRemark"},
                { "售后备注" ,"PlatAfterSaleRemark"},
                };
            }

            //在子项的字段
            var itemFields = new List<string> { "ProductInfo", "ProductSubject", "AfterSaleCount", "SkuId", "CargoNumber", "UpFxUserSettlementPrice", "DownFxUserSettlementPrice", "AuthorUpSettlementPrice", "AuthorDownSettlementPrice" };

            //t.20231229 p.改为用户决定显示那些列头
            #region 获取导出配置项信息
            ExportSetting setting = null;
            var exportSettingVal = string.Empty;
            try
            {
                var _commonSettingService = new CommonSettingService();
                var key = "/ErpWeb/AfterSale/AfterSaleOrderExportSet";
                var exportSetting = _commonSettingService.Get(key, currentLoginShop.Id);
                exportSettingVal = exportSetting?.Value.ToString2() ?? "";
                if (exportSettingVal.IsNullOrEmpty())
                    exportSettingVal = _commonSettingService.GetFxDefaultSetting(currentLoginShop.PlatformType, "AfterSaleOrderExportSet");
                if (exportSettingVal.IsNullOrEmpty())
                {
                    throw new LogicException("导出配置信息不能为空");
                }
                setting = JsonExtension.ToObject<ExportSetting>(exportSettingVal) ?? new ExportSetting();
                setting?.CheckedItems?.RemoveAll(m => !m.Value.IsNullOrEmpty() && m.Value.StartsWith("Export_Except"));
                if (setting == null || setting.CheckedItems == null || setting.CheckedItems.Count == 0)
                {
                    throw new LogicException("导出配置信息不能为空");
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"导出配置项为空或不符合规范：{exportSettingVal}，异常信息：{ex}");
                throw new LogicException($"导出配置项为空或不符合规范，异常信息：{ex.Message}");
            }

            if (setting != null)
            {
                if (setting.CheckedItems != null && setting.CheckedItems.Any())
                {
                    heads = new Dictionary<string, string>();   //清空
                    setting.CheckedItems.ForEach(head =>
                    {
                        var field = head.Value.Replace("Export_", "");
                        var text = head.Text;
                        var from = head.From;
                        if (!heads.ContainsKey(text))
                        {
                            if (text == "IsSended")
                            {
                                if (sourceFlag == 0)
                                {
                                    heads.Add(text, field);
                                }
                            }
                            else
                            {
                                heads.Add(text, field);
                            }
                        }
                    });
                }
                var AfterSaleChkItems = setting.CheckedItems.Where(m => m.From == "afterSale" || m.From == "other").ToList();
                var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
                if (productChkItems != null && productChkItems.Any())
                {
                    itemFields = new List<string>(); //清空
                    productChkItems.ForEach(chkItem =>
                    {
                        var field = chkItem.Value.Replace("Export_", "");
                        var text = chkItem.Text;
                        var from = chkItem.From;
                        if (!itemFields.Contains(field))
                            itemFields.Add(field);
                    });
                }
            }
            #endregion

            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;

            #region 对收件人信息进行脱敏
            //EncryptionService.DataMaskingExpression(list);//EncryptionService.DataMaskingReflection(list);

            var notEncryptOrders = list.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.PlatformType == PlatformType.WxVideo.ToString() || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
            notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
            if (notEncryptOrders.Any())
                EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);


            #endregion

            list.ForEach(model =>
            {
                var orders = model.Orders;
                orders.ForEach(order =>
                {

                    var dic = (order ?? new AfterSaleOrderResult()).ToDictionary();

                    // 商品单独行显示，并填充订单项数据
                    order.Items.ForEach(oitem =>
                    {
                        IRow dataRow = sheet.CreateRow(rowIndex);
                        dataRow.HeightInPoints = 20;
                        var itemDic = (oitem ?? new AfterSaleOrderItemResult()).ToDictionary();

                        colIndex = 0;
                        foreach (var item in heads.ToList())
                        {
                            try
                            {
                                ICellStyle tmpStyle = contentStyle;
                                var key = item.Value.ToString2();
                                if (key == "SourceFlag")
                                {
                                    var val = order.SourceFlag;
                                    var text = ((SourceFlagEnum)val).GetEnumDescription();
                                    dataRow.CreateCell(colIndex).SetCellValue(text);
                                }
                                else if (key == "AfterSaleType")
                                {
                                    var val = order.AfterSaleType;
                                    var text = ((AfterSaleTypeEnum)val).GetEnumDescription();
                                    dataRow.CreateCell(colIndex).SetCellValue(text);
                                }
                                else if (key == "AfterSaleStatus")
                                {
                                    var val = order.AfterSaleStatus;
                                    var text = ((AfterSaleStatusEnum)val).GetEnumDescription();
                                    dataRow.CreateCell(colIndex).SetCellValue(text);
                                }
                                else if (key == "RefundStatus")
                                {
                                    var text = "/";
                                    //退款状态，目前只有头条有，其他平台（淘系、拼多多、快手）没有此字段
                                    if (order.PlatformType == PlatformType.TouTiao.ToString() || order.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
                                    {
                                        var val = order.RefundStatus;
                                        text = ((RefundStatusEnum)val).GetEnumDescription();
                                    }
                                    dataRow.CreateCell(colIndex).SetCellValue(text);
                                }
                                else if (key == "IsSended")
                                {
                                    var val = order.IsSended;
                                    var text = val == 1 ? "已发货" : "未发货";
                                    dataRow.CreateCell(colIndex).SetCellValue(text);
                                }
                                else if (key == "ShopName")
                                {
                                    string str = "";
                                    if (!string.IsNullOrWhiteSpace(order.AgentShopName))
                                    {
                                        str = order.AgentShopName;
                                    }
                                    else
                                    {
                                        str = dic[key]?.ToString() ?? "";
                                    }
                                    dataRow.CreateCell(colIndex).SetCellValue(str);
                                }
                                else
                                {
                                    var val = "";
                                    if (itemFields.Contains(key))
                                    {
                                        if (key == "ProductInfo")
                                        {
                                            var color = itemDic["Color"]?.ToString()?.Trim(';');
                                            var size = itemDic["Size"]?.ToString()?.Trim(';');
                                            if (!string.IsNullOrWhiteSpace(color) && !string.IsNullOrWhiteSpace(size))
                                                val = color + ";" + size;
                                            else if (!string.IsNullOrWhiteSpace(color))
                                                val = color;
                                            else if (!string.IsNullOrWhiteSpace(size))
                                                val = size;
                                        }
                                        else if (key == "UpFxUserSettlementPrice" || key == "DownFxUserSettlementPrice" || key == "AuthorUpSettlementPrice" || key == "AuthorDownSettlementPrice")
                                        {
                                            var settlementPrice = itemDic[key]?.ToString2();
                                            if (string.IsNullOrWhiteSpace(settlementPrice) || settlementPrice == "0")
                                            {
                                                settlementPrice = "/";
                                            }
                                            else if (settlementPrice.Equals("-99"))
                                            {
                                                settlementPrice = "**";
                                            }
                                            val = settlementPrice;
                                        }
                                        else if (key == "ProductSubject")
                                        {
                                            val = itemDic[key]?.ToString();
                                            if (string.IsNullOrWhiteSpace(val) && order.IsShowProductTitle == false)
                                            {
                                                val = "合作方已设为隐藏信息";
                                            }
                                        }
                                        else
                                            val = itemDic[key]?.ToString() ?? "";
                                    }
                                    else
                                        val = dic[key]?.ToString() ?? "";
                                    if (key == "AfterSaleRemark" || key == "PlatAfterSaleRemark")
                                        val = val.RemoveSpecialChar();
                                    dataRow.CreateCell(colIndex).SetCellValue(val.ToString2().Trim().Trim("\n".ToArray()));
                                }

                                dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                                colIndex++;
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError($"设置KEY：{item.ToJson()}\n oitem：{oitem.ToJson()}\n处理失败：{ex}");
                                throw ex;
                            }
                        }

                        var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                        dataRow.HeightInPoints = HeightInPoints;
                        rowIndex++;
                    });


                });

            });

            return workbook;
        }

        #endregion

        #region 导出对账中心
        public static IWorkbook SettlementBillListBuildExcel(List<SettlementBill> list, string fileName)
        {
            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("对账中心");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            var heads = new Dictionary<string, string>() 
            {
                { "出账时间", "CreateTime" },
                { "出账方", "CreateUser" },
                { "结算对象", "SettlementType" },
                { "出账周期", "BeginTime" },
                { "出账方总金额", "TotalPrice" },
                { "出账方总金额-商品", "TotalProductPrice" },
                { "出账方总金额-运费", "TotalFreight" },
                { "本方商品总金额-首次", "TotalPriceFirst" },
                { "本方商品总金额-二次", "TotalPriceSecond" },
                { "对方商品总金额-首次", "OppositeTotalPriceFirst" },
                { "对方商品总金额-二次", "OppositeTotalPriceSecond" },
                { "订单数量-首次", "OrderLengthFirst" },
                { "订单数量-二次", "OrderLengthSecond" },
                { "发货包裹数量-首次", "PackageFirst" },
                { "发货包裹数量-二次", "PackageSecond" },
               
                { "账单当前状态", "BillStatus" },
                { "账单留言", "Remark" },
                { "账单备注", "BillRemark" },
                { "账单下载有效期", "ExpirationTime" },

                { "代发商品数量统计", "TotalSkuCount" },
                { "账单统计出账规格范围", "BillingSpecRange" },
                { "运费统计出账规格范围", "FreightSpecRange" }
            };

            int colIndex = 0;

            #region 获取导出配置项信息
            ExportSetting setting = null;
            var exportSettingVal = string.Empty;
            var currentLoginShop = SiteContext.Current.CurrentLoginShop;
            try
            {
                var _commonSettingService = new CommonSettingService();
                var key = "/ErpWeb/SettlementBill/SettlementBillExportSet";
                var exportSetting = _commonSettingService.Get(key, currentLoginShop.Id);
                exportSettingVal = exportSetting?.Value.ToString2() ?? "";
                if (exportSettingVal.IsNullOrEmpty())
                    exportSettingVal = _commonSettingService.GetFxDefaultSetting(currentLoginShop.PlatformType, "SettlementBillExportSet");
                if (exportSettingVal.IsNullOrEmpty())
                {
                    throw new LogicException("导出配置信息不能为空");
                }
                setting = JsonExtension.ToObject<ExportSetting>(exportSettingVal) ?? new ExportSetting();
                // setting?.CheckedItems?.RemoveAll(m => !m.Value.IsNullOrEmpty() && m.Value.StartsWith("Export_Except"));
                if (setting == null || setting.CheckedItems == null || setting.CheckedItems.Count == 0)
                {
                    throw new LogicException("导出配置信息不能为空");
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"导出配置项为空或不符合规范：{exportSettingVal}，异常信息：{ex}");
                throw new LogicException($"导出配置项为空或不符合规范，异常信息：{ex.Message}");
            }

            if (setting != null)
            {
                if (setting.CheckedItems != null && setting.CheckedItems.Any())
                {
                    heads = new Dictionary<string, string>();   //清空
                    setting.CheckedItems.ForEach(head =>
                    {
                        var field = head.Value.Replace("Export_", "");
                        var text = head.Text;
                        var from = head.From;
                        if (!heads.ContainsKey(text))
                        {
                            heads.Add(text, field);
                        }
                    });
                }
            }
            #endregion

            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;

            SupplierUserService supplierUserService = new SupplierUserService();
            var suppliers = supplierUserService.GetSupplierList(SiteContext.Current.CurrentFxUserId, needEncryptAccount: true);
            var agents = supplierUserService.GetAgentList(SiteContext.Current.CurrentFxUserId, needEncryptAccount: true);
            //var agentList = agents.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop, x.NickName, x.Remark }).Distinct().ToList();
            var agentList = agents.Distinct().ToList();

            foreach (SettlementBill item in list)
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;
                var dic = (item ?? new SettlementBill()).ToDictionary();
                colIndex = 0;

                foreach (var head in heads.ToList())
                {
                    var key = head.Value.ToString2();
                    try
                    {
                        ICellStyle tmpStyle = contentStyle;
                        if (key == "CreateUser") // 出账方
                        {
                            var text = GetFxUserName(item.CreateUser, suppliers, agentList, item.SettlementType, 1);
                            dataRow.CreateCell(colIndex).SetCellValue(text);
                        }
                        else if(key == "SettlementType") // 结算对象
                        {
                            var text = GetFxUserName(item.FxUserId, suppliers, agentList, item.SettlementType, 2);
                            dataRow.CreateCell(colIndex).SetCellValue(text);
                        }
                        else if (key == "BeginTime") // 出账周期
                        {
                            var text = $"{item.BeginTime.ToString("yyyy-MM-dd HH:mm")}至{item.EndTime.ToString("yyyy-MM-dd HH:mm")}";
                            dataRow.CreateCell(colIndex).SetCellValue(text);
                        }
                        else if (key == "TotalPriceFirst") // 本方商品总金额-首次
                        {
                            dataRow.CreateCell(colIndex).SetCellValue(item.SettlementBillStatExt?.TotalPriceFirst.ToString2() ?? "");
                        }
                        else if (key == "TotalPriceSecond") // 本方商品总金额-二次
                        {
                            dataRow.CreateCell(colIndex).SetCellValue(item.SettlementBillStatExt?.TotalPriceSecond.ToString2() ?? "");
                        }
                        else if (key == "OppositeTotalPriceFirst") // 对方商品总金额-首次
                        {
                            var param = item.ParamJson.ToObject<OrderSearchModel>();
                            if (param != null && param.Filters.Any())
                            {
                                var orderSearch_TaskFlag = param.Filters.Find(x => x.Name == "TaskFlag"); // 出账对象是我的店铺。要显示0
                                if (orderSearch_TaskFlag?.Value == "3") dataRow.CreateCell(colIndex).SetCellValue("0");
                                else dataRow.CreateCell(colIndex).SetCellValue(item.SettlementBillStatExt?.OppositeTotalPriceFirst.ToString2() ?? "");
                            }
                            else
                                dataRow.CreateCell(colIndex).SetCellValue(item.SettlementBillStatExt?.OppositeTotalPriceFirst.ToString2() ?? "");
                        }
                        else if (key == "OppositeTotalPriceSecond") // 对方商品总金额-二次
                        {
                            var param = item.ParamJson.ToObject<OrderSearchModel>();
                            if (param != null && param.Filters.Any())
                            {
                                var orderSearch_TaskFlag = param.Filters.Find(x => x.Name == "TaskFlag"); // 出账对象是我的店铺。要显示0
                                if (orderSearch_TaskFlag?.Value == "3") dataRow.CreateCell(colIndex).SetCellValue("0");
                                else dataRow.CreateCell(colIndex).SetCellValue(item.SettlementBillStatExt?.OppositeTotalPriceSecond.ToString2() ?? "");
                            }
                            else
                                dataRow.CreateCell(colIndex).SetCellValue(item.SettlementBillStatExt?.OppositeTotalPriceSecond.ToString2() ?? "");
                        }
                        else if (key == "PackageSecond")
                        {
                            dataRow.CreateCell(colIndex).SetCellValue(item.SettlementBillStatExt?.PackageSecond.ToString2() ?? "");
                        }
                        else if (key == "OrderLengthSecond")
                        {
                            dataRow.CreateCell(colIndex).SetCellValue(item.SettlementBillStatExt?.OrderLengthSecond.ToString2() ?? "");
                        }
                        else if (key == "PackageFirst")
                        {
                            dataRow.CreateCell(colIndex).SetCellValue(item.SettlementBillStatExt?.PackageFirst.ToString2() ?? "");
                        }
                        else if (key == "OrderLengthFirst")
                        {
                            dataRow.CreateCell(colIndex).SetCellValue(item.SettlementBillStatExt?.OrderLengthFirst.ToString2() ?? "");
                        }
                        else if (key == "BillStatus") // 账单当前状态
                        {
                            string text = string.Empty;
                            // // 0待出账；1已出账；2已确认；-2已删除；-11失败
                            // switch (item.BillStatus)
                            // {
                            //     case 0: text = "待出账"; break;
                            //     case 1: text = "已出账"; break;
                            //     case 2: text = "已确认"; break;
                            //     case -2: text = "已删除"; break;
                            //     case -11: text = "失败"; break;
                            // }

                            text = GenerateBillStatusStr(item.BillCode, item.BillStatus, item.Percent, item.CreateUser, item.FxUserId);
                            dataRow.CreateCell(colIndex).SetCellValue(text);
                        }
                        else if (key == "BillingSpecRange") // 账单统计出账规格范围
                        {
                            string text = string.Empty;
                            var param = item.ParamJson.ToObject<OrderSearchModel>();
                            if (param != null && param.Filters !=null && param.Filters.Any() && param.BillType != 3)
                            {
                                if (param != null)
                                {
                                    var orderSearch_OrderStatus = param.Filters.Find(x => x.Name == "OrderStatus");
                                    var orderSearch_SaleStatus = param.Filters.Find(x => x.Name == "SaleStatus");
                                    var orderSearch_PriceSendType = param.Filters.Find(x => x.Name == "SendType");

                                    var isOrderStatus = orderSearch_OrderStatus != null && !string.IsNullOrWhiteSpace(orderSearch_OrderStatus.Value);
                                    var isSaleStatus = orderSearch_SaleStatus != null && !string.IsNullOrWhiteSpace(orderSearch_SaleStatus.Value);
                                    var isPriceSendType = orderSearch_PriceSendType != null && !string.IsNullOrWhiteSpace(orderSearch_PriceSendType.Value);

                                    if (isOrderStatus)
                                    {
                                        var tempText = "";
                                        if (orderSearch_OrderStatus.Value.Contains("waitbuyerreceive"))
                                            tempText = "已发货未签收、";
                                        if (orderSearch_OrderStatus.Value.Contains("success"))
                                            tempText += "已发货已签收、";

                                        tempText = tempText.Substring(0, tempText.LastIndexOf('、'));
                                        if (!string.IsNullOrWhiteSpace(tempText))
                                            text = $"商品规格无退款（{tempText}）";
                                    }
                                    if (isSaleStatus)
                                    {
                                        var tempText = "";
                                        if (orderSearch_SaleStatus.Value.Contains("WAIT_SELLER_AGREE"))
                                            tempText = "已发货申请退款、";
                                        if (orderSearch_SaleStatus.Value.Contains("REFUND_SUCCESS"))
                                            tempText += "已发货退款成功、";

                                        if (!string.IsNullOrWhiteSpace(tempText))
                                            tempText = tempText.Substring(0, tempText.LastIndexOf('、'));

                                        if (!string.IsNullOrWhiteSpace(tempText))
                                            text += $"\n商品规格有退款（{tempText}）";

                                    }
                                    if (isPriceSendType)
                                    {
                                        var value = orderSearch_PriceSendType.Value.Replace("0,", ""); // 去掉 "0"，首次发货
                                        var priceSendTypeDic = new Dictionary<string, string>{
                                        { "1", "补发" },
                                        { "2", "换货" },
                                        { "3", "变更单号" },
                                        { "99", "其他" }};

                                        var tempText = GetKeyToName(value, priceSendTypeDic);
                                        if (!string.IsNullOrWhiteSpace(tempText))
                                            text += $"\n二次发货（{tempText}）";
                                    }
                                }
                            }
                            dataRow.CreateCell(colIndex).SetCellValue(text);
                        }
                        else if (key == "FreightSpecRange") // 运费统计出账规格范围
                        {
                            string text = string.Empty;
                            var param = item.ParamJson.ToObject<OrderSearchModel>();
                            if (param != null && param.FreightFilters != null && param.FreightFilters.Any() && param.BillType!=3)
                            {
                                var orderSearch_SendStatus = param.FreightFilters.Find(x => x.Name == "SendStatus");
                                var orderSearch_RefundState = param.FreightFilters.Find(x => x.Name == "RefundState");
                                var orderSearch_FreightSendType = param.FreightFilters.Find(x => x.Name == "SendType");

                                var isSendStatus = orderSearch_SendStatus != null && !string.IsNullOrWhiteSpace(orderSearch_SendStatus.Value);
                                var isRefundState = orderSearch_RefundState != null && !string.IsNullOrWhiteSpace(orderSearch_RefundState.Value);
                                var isFreightSendType = orderSearch_FreightSendType != null && !string.IsNullOrWhiteSpace(orderSearch_FreightSendType.Value);

                                if (isSendStatus)
                                {
                                    var tempText = "";
                                    var priceSendTypeDic = new Dictionary<string, string>{
                                      { "PartSend", "部分发货" },
                                      { "AllSend", "全部发货" } };

                                    tempText = GetKeyToName(orderSearch_SendStatus.Value, priceSendTypeDic);
                                    if (!string.IsNullOrWhiteSpace(tempText))
                                        text = $"发货状态（{tempText}）";
                                }
                                if (isRefundState)
                                {
                                    var tempText = "";
                                    var priceSendTypeDic = new Dictionary<string, string>{
                                      { "PartRefund", "部分退款" },
                                      { "AllRefund", "全部发货" } };

                                    tempText = GetKeyToName(orderSearch_RefundState.Value, priceSendTypeDic);
                                    if (!string.IsNullOrWhiteSpace(tempText))
                                        text += $"\n售后状态（{tempText}）";
                                }
                                if (isFreightSendType)
                                {
                                    var value = orderSearch_FreightSendType.Value.Replace("0,", ""); // 去掉 "0"，首次发货
                                    var priceSendTypeDic = new Dictionary<string, string>{
                                      { "1", "补发" },
                                      { "2", "换货" },
                                      { "3", "变更单号" },
                                      { "99", "其他" }};

                                    var tempText = GetKeyToName(value, priceSendTypeDic);
                                    if (!string.IsNullOrWhiteSpace(tempText))
                                        text += $"\n二次发货（{tempText}）";
                                }
                            }
                            dataRow.CreateCell(colIndex).SetCellValue(text);
                        }
                        else
                        {
                            bool exist = dic.TryGetValue(key, out object tempvalue);
                            if (!exist)
                                Log.Debug(() => $"导出对账中心,key={key} 在dic中不存在", "afterOrderAction.txt");

                            var val = dic[key]?.ToString() ?? "";
                            dataRow.CreateCell(colIndex).SetCellValue(val.ToString2().Trim().Trim("\n".ToArray()));
                        }

                        dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                        colIndex++;
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"设置KEY：{key}\n item：{item.ToJson()}\n处理失败：{ex.Message} 堆栈：{ex.StackTrace}");
                        throw;
                    }
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;
            }

            return workbook;
        }

        public static string GenerateBillStatusStr(string billCode, int billStatus, double percent = 0, int createUser=0, int fxUserId=0)
        {
            var statusStr = new System.Text.StringBuilder();
            if (billStatus == 0)
            {
                if (percent == 0)
                {
                    statusStr.Append("出账排队中");
                }
                else if (percent == -1)
                {
                    statusStr.Append("出账异常");
                }
                else
                {
                    statusStr.Append($"正在出账中");
                }
            }
            else if (billStatus == 1)
            {
                var statusText = "";
                if (createUser == SiteContext.Current.CurrentFxUserId)
                {
                    statusText = "待对方确认";
                }
                else
                {
                    statusText = "待自己确认";
                }
                statusStr.Append(statusText);
            }
            else if (billStatus == 2)
            {
                statusStr.Append("已确认");
            }
            else if (billStatus == -1)
            {
                statusStr.Append("已驳回");
            }
            else if (billStatus == -11)
            {
                statusStr.Append($"出账失败");
            }
            else
            {
            }

            return statusStr.ToString();
        }

        public static string GetKeyToName(string value, Dictionary<string, string> mapList)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            // 将字符串分割为数组
            var valueList = value.Split(',');

            // 使用Select方法转换数字为中文
            var chineseList = valueList.Select(type =>
                mapList.TryGetValue(type, out var chinese) ? chinese : type);

            // 将中文数组用顿号连接成新的字符串
            return string.Join("、", chineseList);
        }

        /// <summary>
        /// 过去自己/对方的FxUserName
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="settlementType"></param>
        /// <param name="userType">1出账人 2结算方</param>
        /// <returns></returns>
        public static string GetFxUserName(int fxUserId, List<SupplierUser> suppliers, List<SupplierUser> agentList, int settlementType, int userType)
        {
            // userType: 1出账人 2结算方
            if (fxUserId > 0)
            {
                string result = "";
                var _selfFxUserId = SiteContext.Current.CurrentFxUserId;
                if (fxUserId == _selfFxUserId)
                {
                    result = SiteContext.Current.CurrentFxUser.NickName + " (本人)";
                }
                else
                {
                    if ((settlementType == 2 && userType == 1) || (settlementType == 1 && userType == 2))
                    {
                        if (suppliers.Count > 0)
                        {
                            var supplierArr = suppliers.Where(e => e.SupplierFxUserId == fxUserId).ToList();
                            if (supplierArr.Count > 0)
                            {
                                var supplier = supplierArr[0];
                                result = supplier.NickName;
                                if (!string.IsNullOrEmpty(supplier.RemarkName))
                                {
                                    result += (SiteContext.Current.IsWhiteUser ? "\r\n" : "") + " (" + supplier.RemarkName + ")";
                                }
                            }
                        }
                        result = "厂家：" + result;
                    }
                    else
                    {
                        if (agentList.Count > 0)
                        {
                            var agentArr = agentList.Where(e => e.FxUserId == fxUserId).ToList();
                            if (agentArr.Count > 0)
                            {
                                var agent = agentArr[0];
                                result = agent.NickName;
                                if (!string.IsNullOrWhiteSpace(agent.Remark))
                                {
                                    result += (SiteContext.Current.IsWhiteUser ? "\r\n" : "") + " (" + agent.Remark + ")";
                                }
                            }
                        }
                        result = "商家：" + result;
                    }
                }
                return result;
            }
            return string.Empty;
        }

        #endregion

        #region 导出实时库存
        /// <summary>
        /// 实时库存导出处理
        /// </summary>
        /// <param name="list"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static IWorkbook StockDetailBuildExcel(List<SkuStockDetailResponse> list, string fileName)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            FxUserShopService _fxUserShopService = new FxUserShopService();

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("实时库存");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            var heads = new Dictionary<string, string>() {
                { "库存商品名称" ,"ProductName"},
                { "库存商品规格" ,"SkuProperty"},
                { "库存商品SKU编码" ,"SkuCargoNumber"},
                { "组合库存数量" ,"SelfStockCount"},
                { "子货品可组合数量" ,"ComposeChildStockCount"},
                { "总可售数量" ,"TotalStockCount"},
                { "关联商品数量" ,"TotalBindSkuCount"},
            };

            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;
            list.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;

                var dic = (model ?? new SkuStockDetailResponse()).ToDictionary();

                colIndex = 0;
                foreach (var item in heads.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    if (key == "SelfStockCount" || key == "ComposeChildStockCount")
                    {
                        if (model.ItemType == "ZH")
                        {
                            var val = dic[key].ToString2();
                            dataRow.CreateCell(colIndex).SetCellValue(val);
                        }
                        else
                        {
                            dataRow.CreateCell(colIndex).SetCellValue("/");
                        }

                    }
                    else
                    {
                        var val = dic[key]?.ToString() ?? "";
                        dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                    }

                    dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                    colIndex++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;

            });

            return workbook;
        }

        #endregion

        #region 导出发货失败
        /// <summary>
        /// 发货失败导出处理
        /// </summary>
        /// <param name="list"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static IWorkbook SendFailBuildExcel(List<SendFailResult> list, string fileName)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            FxUserShopService _fxUserShopService = new FxUserShopService();

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("发货失败");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            var heads = new Dictionary<string, string>() {
                { "发货失败原因" ,"Reason"},
                { "打印批次" ,"PrintBatch"},
                { "打印序号" ,"BatchIndex"},
                { "打印时间" ,"PrintDate"},
                { "订单编号" ,"PlatformOrderId"},
                { "系统编号" ,"LogicOrderId"},
                { "快递模版" ,"TemplateName"},
                { "运单号" ,"LogistiscBillNo"},
                { "商家信息" ,"ShopName"},
            };

            var printHistoryFields = ("PrintBatch,BatchIndex,PrintDate,TemplateName").Split(',').ToList();


            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;
            list.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;

                var dic = (model ?? new SendFailResult()).ToDictionary();
                var printDic = (model.PrintHistory ?? new PrintHistory()).ToDictionary();
                var oLogicOrderIds = string.Join(",", model.OLogicOrderIds);
                if (!string.IsNullOrEmpty(oLogicOrderIds))
                    dic["LogicOrderId"] = oLogicOrderIds;

                colIndex = 0;
                foreach (var item in heads.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    if (printHistoryFields.Contains(key))
                    {
                        var val = "";
                        if (model.PrintHistory != null)
                        {
                            val = printDic[key].ToString2();
                            if (key == "BatchIndex")
                                val = $"第{val}张";
                        }
                        dataRow.CreateCell(colIndex).SetCellValue(val);
                    }
                    else if (key == "ShopName")
                    {
                        var val = model.LogicOrder?.ShopName;
                        dataRow.CreateCell(colIndex).SetCellValue(val);
                    }
                    else
                    {
                        var val = dic[key]?.ToString2() ?? "";
                        dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                    }

                    dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                    colIndex++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;

            });

            return workbook;
        }

        #endregion

        #region 导出打印记录
        /// <summary>
        /// 打印记录导出处理
        /// </summary>
        /// <param name="list"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static IWorkbook PrintHistoryBuildExcel(List<PrintHistory> list, string fileName, int printTemplateShopId, bool isCrossBorderSite)
        {
            #region 收件人信息脱敏
            var notEncryptOrders = list.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || (x.ReciverPhone?.Contains("*") == false && x.PlatformType != PlatformType.Virtual.ToString()) || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
            EncryptionService.DataMaskingExpression(notEncryptOrders);
            #endregion

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            FxUserShopService _fxUserShopService = new FxUserShopService();

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("打印记录");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;
            int colIndex = 0;
            var heads = new Dictionary<string, string>() {
                { "打印批次" ,"PrintBatch"},
                { "打印序号" ,"BatchIndex"},
                { "打印时间" ,"PrintDate"},
                { "订单编号" ,"CustomerOrderId"},
                { "系统编号" ,"PlatformOrderJoin"},
                { "快递模版" ,"TemplateId"},
                { "运单号" ,"ExpressWaybillCode"},
                { "重量(kg)" ,"TotalWeight"},
                { isCrossBorderSite ? "国家" : "省份" ,isCrossBorderSite ? "ToCountry" : "ToProvince"},
                { "收件人" ,"Reciver"},
                { "收件人电话" ,"ReciverPhone"},
                { "单号类型" ,"SendType"}
            };
            if (isCrossBorderSite)
            {
                heads["快递模版"] = "TemplateName";
                heads.Add("交运状态", "Status");//状态，仅TK平台有值，和包裹状态一致
            }

            var printTemplateList = new PrintTemplateService().GetTemplateNamesByShopId(printTemplateShopId);


            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;
            list.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;

                var dic = (model ?? new PrintHistory()).ToDictionary();

                colIndex = 0;
                foreach (var item in heads.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    if (key == "SendType")
                    {
                        var val = dic[key]?.ToString2() ?? "";
                        if (val == "" || val == "0")
                            val = "首次发货";
                        else
                            val = "二次发货";
                        dataRow.CreateCell(colIndex).SetCellValue(val);
                    }
                    else if (key == "BatchIndex")
                    {
                        var printBatchNumber = dic["PrintBatchNumber"].ToString2();
                        var val = dic[key]?.ToString2() ?? "";
                        if (printBatchNumber == "10000")
                            val = $"第{val}张(重打)";
                        else
                            val = $"第{val}张";
                        dataRow.CreateCell(colIndex).SetCellValue(val);
                    }
                    else if (key == "TemplateId")
                    {
                        var val = dic[key]?.ToString2() ?? "";
                        var printTemplate = printTemplateList.FirstOrDefault(a => a.Id.ToString() == val);
                        val = printTemplate?.TemplateName ?? "";

                        if (printTemplate != null)
                        {
                            if (CustomerConfig.IsNewXiaoHongShuTemplate(printTemplate.TemplateType))
                            {
                                val = "【新版】" + val;
                            }
                            else if (CustomerConfig.IsXiaoHongShuTemplate(printTemplate.TemplateType))
                            {
                                val = "【旧版】" + val;
                            }
                        }

                        dataRow.CreateCell(colIndex).SetCellValue(val);
                    }
                    else if (key == "ExpressWaybillCode")
                    {
                        var val = dic[key]?.ToString2() ?? "";
                        if (dic["ExpressWaybillCodeChild"].IsNotNullOrEmpty())
                        {
                            val += " / " + dic["ExpressWaybillCodeChild"].ToString2();
                        }
                        dataRow.CreateCell(colIndex).SetCellValue(val);
                    }
                    else if (key == "Status")///包裹状态
                    {
                        var val = dic[key]?.ToString2() ?? "";
                        if (val == "2")
                            val = "已交运";
                        else
                            val = "未交运";
                        dataRow.CreateCell(colIndex).SetCellValue(val);
                    }
                    else
                    {
                        var val = dic[key]?.ToString2() ?? "";
                        dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                    }

                    dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                    colIndex++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;

            });

            return workbook;
        }

        #endregion

        #region 导出回流单
        /// <summary>
        /// 导出回流单处理
        /// </summary>
        /// <param name="list"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static IWorkbook SendHistoryReturnRecordBuildExcel(List<SendHistoryReturnRecord> list, string fileName)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            FxUserShopService _fxUserShopService = new FxUserShopService();

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("回流单");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            var heads = new Dictionary<string, string>() {
                { "订单编号" ,"PurchasePlatformOrderId"},
                { "快递名称" ,"ExpressName"},
                { "快递单号" ,"ExpressWaybillCode"},
            };


            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;


            list.ForEach(row =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new SendHistoryReturnRecord()).ToDictionary();

                colIndex = 0;
                foreach (var item in heads.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    var val = dic[key]?.ToString() ?? "";

                    dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                    colIndex++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;

            });

            return workbook;
        }

        #endregion

        #region 导出账单任务明细
        /// <summary>
        /// 导出账单任务处理
        /// </summary>
        /// <param name="list"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static IWorkbook ExportTaskBuildExcel(List<SettlementBill> list, string fileName, string mainTaskCode, int settlementType = 0)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            FxUserShopService _fxUserShopService = new FxUserShopService();

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("子任务明细");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            var tipCreateTime = list.Select(t => t.CreateTime).FirstOrDefault();
            var tipBillTotal = list.Count();
            var successBillCount = list.Where(t => t.BillStatus == 2 || t.BillStatus == 1).Count();
            var waitBillCount = list.Where(t => t.BillStatus == 0).Count();
            //var loseBill = list.Where(t => t.BillStatus != 2 && t.BillStatus != 1).Count();

            int colIndex = 0;
            if (settlementType == 3)
                sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 4));
            else
                sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 5));
            IRow tip = sheet.CreateRow(0);
            SetColumnWidth(sheet, "账单任务导出表头", colIndex);

            ICell tipCell = tip.CreateCell(0);
            tipCell.CellStyle = headStyle;
            tipCell.SetCellValue("出账时间：" + tipCreateTime.ToString("yyyy-MM-dd HH:mm:ss") + "；出账结果：总账单" + tipBillTotal + "，其中成功" + successBillCount + "份，失败" + (tipBillTotal - successBillCount - waitBillCount) + "份，出账中" + waitBillCount + "份，如有失败账单，请重新出账避免损失");

            var val = string.Empty;

            IRow headerRow = sheet.CreateRow(1);
            //列名
            var heads2 = new Dictionary<string, string>();
            if (settlementType == 3)
            {
                heads2 = new Dictionary<string, string>() {
                { "店铺名称" ,"FxUserId"} ,
                { "结算周期" ,"Time"},
                { "商品总金额" ,"TotalProductPrice"},
                { "运费总金额" ,"TotalFreight"},
                { "出账总金额" ,"TotalPrice"},
                { "出账结果" ,"BillResult"},
                { "账单备注（仅自己可见）" ,"BillRemark"},
                };
            }
            else
            {
                heads2 = new Dictionary<string, string>() {
                { "账单状态" ,"BillStatus"},
                { "结算对象" ,"FxUserId"} ,
                { "结算周期" ,"Time"},
                { "商品总金额" ,"TotalProductPrice"},
                { "运费总金额" ,"TotalFreight"},
                { "出账总金额" ,"TotalPrice"},
                { "出账结果" ,"BillResult"},
                { "账单留言（仅对方可见）" ,"Remark"},
                };
            }

            heads2.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 2;

            SupplierUserService _supplierUserService = new SupplierUserService();
            var selfNickName = SiteContext.Current.CurrentFxUser.NickName + " (本人)";
            var agentLIst = _supplierUserService.GetAgentList(fxUserId);
            var supplierList = _supplierUserService.GetSupplierList(fxUserId);
            var shopList = new ShopService().GetAllShops();
            list.ForEach(row =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new SettlementBill()).ToDictionary();
                colIndex = 0;
                foreach (var item in heads2.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();

                    #region 转换格式
                    if (key.Equals("BillStatus") || key.Equals("BillResult"))
                    {
                        if (dic["BillStatus"].Equals(0))
                        {
                            val = "正在出账中";
                        }
                        else if (dic["BillStatus"].Equals(1))
                        {
                            //if (key.Equals("BillResult"))
                            //{
                            //    val = "正在出账中";
                            //}
                            //else
                            //{
                            //    //当前用户
                            //    if (dic["CreateUser"].Equals(SiteContext.Current.CurrentFxUserId))
                            //    {
                            //        val = "待对方确认";
                            //    }
                            //    else
                            //    {
                            //        val = "待对方确认";
                            //    }
                            //}
                            //当前用户
                            if (settlementType == 3)
                            {
                                val = "成功";
                            }
                            else if (dic["CreateUser"].Equals(SiteContext.Current.CurrentFxUserId))
                            {
                                val = "待对方确认";
                            }
                            else
                            {
                                val = "待对方确认";
                            }

                        }
                        else if (dic["BillStatus"].Equals(2))
                        {
                            if (key.Equals("BillResult"))
                            {
                                val = "成功";
                            }
                            else
                            {
                                val = "已确认";
                            }
                        }
                        else if (dic["BillStatus"].Equals(-1))
                        {
                            if (key.Equals("BillResult"))
                            {
                                val = "失败";
                            }
                            else
                            {
                                val = "已驳回";
                            }
                        }
                        else if (dic["BillStatus"].Equals(-11))
                        {
                            if (key.Equals("BillResult"))
                            {
                                val = "失败";
                            }
                            else
                            {
                                val = "出账失败";
                            }
                        }
                    }
                    else if (key.Equals("FxUserId"))//结算对象
                    {

                        
                        //var supplierList = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, SupplierFxUserId = x.SupplierFxUserId, Status = x.Status, IsTop = x.IsTop, x.NickName, x.RemarkName, x.IsFilter }).Where(x => x.SupplierFxUserId == dic[key].ToInt()).ToList();
                        var it = supplierList.Where(t => t.FxUserId == (dic["FxUserId"].ToInt()));
                        var userType = 2;
                        var i = dic[key];
                        if (settlementType == 3)
                        {
                            val = shopList.FirstOrDefault(s => s.Id == row.FxUserId).ShopName;
                        }
                        else if (dic["FxUserId"].Equals(SiteContext.Current.CurrentFxUserId))
                        {
                            val = SiteContext.Current.CurrentFxUser.NickName + " (本人)";
                        }
                        else if ((dic["SettlementType"].Equals(2) && userType == 1) || (dic["SettlementType"].Equals(1) && userType == 2))
                        {
                            var supplier = supplierList?.Where(x => x.SupplierFxUserId == row.FxUserId).ToList();
                            if (!supplier.IsNullOrEmptyList())
                            {
                                val = "厂家： " + (supplier[0].NickName + (!supplier[0].RemarkName.IsNullOrEmpty() ? (" (" + supplier[0].RemarkName + ")") : ""));
                            }
                            else
                            {
                                val = "";
                            }
                        }
                        else
                        {
                            var agent = agentLIst?.Where(x => x.FxUserId == row.FxUserId).ToList();
                            if (!agent.IsNullOrEmptyList())
                            {
                                val = "商家： " + (agent[0].NickName + (!agent[0].Remark.IsNullOrEmpty() ? (" (" + agent[0].Remark + ")") : ""));
                            }
                            else
                            {
                                val = "";
                            }
                        }
                    }
                    else if (key.Equals("Time"))//处理结算周期
                    {
                        var beginTime = dic["BeginTime"].ToString().Substring(0, 16);
                        var endTime = dic["EndTime"].ToString().Substring(0, 16);
                        val = beginTime + '至' + endTime;
                    }
                    #endregion
                    else
                    {
                        val = dic[key]?.ToString() ?? "";
                        if (key.Equals("TotalProductPrice") && string.IsNullOrEmpty(val))
                        {
                            //兼容旧数据
                            val = dic["TotalPrice"]?.ToString() ?? "";
                        }
                    }
                    dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                    colIndex++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;

            });

            return workbook;
        }

        #endregion

        #region 导出1688统计数据
        /// <summary>
        /// 导出1688统计数据
        /// </summary>
        /// <param name="statList"></param>
        /// <param name="stat1688FxUserIdList"></param>
        /// <param name="poRelationList"></param>
        /// <param name="returnList"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static IWorkbook Stat1688V2BuildExcel(List<Stat1688V2> statList, List<Stat1688FxUserId> stat1688FxUserIdList, List<PurchaseOrderRelation> poRelationList, List<SendHistoryReturnRecord> returnList, string fileName)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            FxUserShopService _fxUserShopService = new FxUserShopService();

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);

            #region 表1：1688应用使用分析
            ISheet sheet = workbook.CreateSheet("1688应用使用分析");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;


            int colIndex = 0;
            var heads = new Dictionary<string, string>() {
                { "日期" ,"StatDate"},
                { "新增订购店铺数" ,"AddQingAppShopNum"},
                { "老店铺" ,"OldShopAddQingAppShopNum"},
                { "新店铺" ,"NewShopAddQingAppShopNum"},
                { "老用户" ,"AddQingAppOldUserSupplierNum"},
                { "新用户" ,"AddQingAppNewUserSupplierNum"},
                { "老用户-已设置下单店铺" ,"OldShopHasSet1688ShopSupplierNum"},
                { "老用户-未设置下单店铺" ,"OldShopNotSet1688ShopSupplierNum"},
                { "新用户-已设置下单店铺" ,"NewShopHasSet1688ShopSupplierNum"},
                { "新用户-未设置下单店铺" ,"NewShopNotSet1688ShopSupplierNum"},
                { "新用户-首打单-厂家数" ,"NewShopFirstPrintOrderSupplierNum"},
                { "新用户-首收单-厂家数" ,"NewShopFirstPurchaseOrderSupplierNum"},
                { "老店铺-已绑定下游" ,"OldShopHasBindedAgentSupplierNum"},
                { "老店铺-开通预付" ,"OldShopFirstOpenPrepaySupplierNum"},
                { "老店铺-首收单-厂家数" ,"OldShopFirstPurchaseOrderSupplierNum"},
                { "老店铺-首回传-厂家数" ,"OldShopFirstReturnSupplierNum"},
                { "老店铺-非预付-首打单-厂家数" ,"OldShopButNotOpenPrepayFirstPrintOrderSupplierNum"},
                { "新店铺-已绑定下游-厂家数" ,"NewShopHasBindedAgentSupplierNum"},
                { "新店铺-开通预付-厂家数" ,"NewShopFirstOpenPrepaySupplierNum"},
                { "新店铺-首收单-厂家数" ,"NewShopFirstPurchaseOrderSupplierNum"},
                { "新店铺-首回传-厂家数" ,"NewShopFirstReturnSupplierNum"},
                { "新店铺-非预付-首打单-厂家数" ,"NewShopButNotOpenPrepayFirstPrintOrderSupplierNum"},
                { "新用户-首推品-商家数" ,"NewShopHasPostProductAgentNum"},
                { "新用户-已推品-未关联-厂家数" ,"NewShopNotMappingProductSupplierNum"},
                { "新用户-已推品-未关联-商家数" ,"NewShopNotMappingProductAgentNum"},
                { "新用户-已绑买家账号-商家数" ,"NewShopHasBindedBuyerAgentNum"},
                { "新用户-首下单-商家数" ,"NewShopFirstPurchaseOrderAgentNum"},
                { "新用户-非预付-首打单-商家数" ,"NewShopButNotOpenPrepayFirstPrintOrderAgentNum"},
                { "老用户-首推品-商家数" ,"OldShopHasPostProductAgentNum"},
                { "老用户-已推品-未关联-厂家数" ,"OldShopNotMappingProductSupplierNum"},
                { "老用户-已推品-未关联-商家数" ,"OldShopNotMappingProductAgentNum"},
                { "老用户-已绑买家账号-商家数" ,"OldShopHasBindedBuyerAgentNum"},
                { "老用户-首下单-商家数" ,"OldShopFirstPurchaseOrderAgentNum"},
                { "老用户-非预付-首打单-商家数" ,"OldShopButNotOpenPrepayFirstPrintOrderAgentNum"},
                { "近七天总下单" ,"SevenDaysUserPurchaseOrderNum"},
                { "近七天总付款" ,"SevenDaysUserPurchaseOrderPayedNum"},
                { "近七天总回传" ,"SevenDaysUserReturnTotalNum"},
                { "历史总过期店铺数" ,"AuthExpiredNum"},
            };

            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                var width = (headName.Length * 3 + 4) * 265;
                sheet.SetColumnWidth(colIndex, width);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;

            statList.ForEach(row =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new Stat1688V2()).ToDictionary();

                colIndex = 0;
                foreach (var item in heads.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    var val = dic[key]?.ToString() ?? "";

                    dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                    colIndex++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;

            });

            #endregion

            #region 表2：新用户下游合作用户未使用明细（手机号）Flag=0
            ISheet sheet2 = workbook.CreateSheet("新用户下游合作用户未使用明细（手机号）");

            IRow headerRow2 = sheet2.CreateRow(0);
            headerRow2.HeightInPoints = 15;

            int colIndex2 = 0;
            var heads2 = new Dictionary<string, string>() {
                { "注册时间" ,"UserCreateTime"},
                { "手机号" ,"Mobile"},
            };

            heads2.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet2, headName, colIndex2);

                headerRow2.CreateCell(colIndex2).SetCellValue(headName);
                headerRow2.GetCell(colIndex2).CellStyle = headStyle;
                colIndex2++;
            });

            int rowIndex2 = 1;

            stat1688FxUserIdList?.Where(a => a.Flag == 0).ToList().ForEach(row =>
            {
                IRow dataRow = sheet2.CreateRow(rowIndex2);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new Stat1688FxUserId()).ToDictionary();

                colIndex2 = 0;
                foreach (var item in heads2.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    var val = dic[key]?.ToString() ?? "";

                    dataRow.CreateCell(colIndex2).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex2).CellStyle = tmpStyle;
                    colIndex2++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet2, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex2++;

            });

            #endregion

            #region 表3：老用户下游合作用户未使用明细（手机号）Flag=1
            ISheet sheet3 = workbook.CreateSheet("老用户下游合作用户未使用明细（手机号）");

            IRow headerRow3 = sheet3.CreateRow(0);
            headerRow3.HeightInPoints = 15;

            int colIndex3 = 0;
            var heads3 = new Dictionary<string, string>() {
                { "注册时间" ,"UserCreateTime"},
                { "手机号" ,"Mobile"},
            };

            heads3.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet3, headName, colIndex3);

                headerRow3.CreateCell(colIndex3).SetCellValue(headName);
                headerRow3.GetCell(colIndex3).CellStyle = headStyle;
                colIndex3++;
            });

            int rowIndex3 = 1;

            stat1688FxUserIdList?.Where(a => a.Flag == 1).ToList().ForEach(row =>
            {
                IRow dataRow = sheet3.CreateRow(rowIndex3);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new Stat1688FxUserId()).ToDictionary();

                colIndex3 = 0;
                foreach (var item in heads3.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    var val = dic[key]?.ToString() ?? "";

                    dataRow.CreateCell(colIndex3).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex3).CellStyle = tmpStyle;
                    colIndex3++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet3, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex3++;

            });

            #endregion

            #region 表4：新用户下游推品但未绑定明细 Flag=2
            ISheet sheet4 = workbook.CreateSheet("新用户下游推品但未绑定明细");

            IRow headerRow4 = sheet4.CreateRow(0);
            headerRow4.HeightInPoints = 15;

            int colIndex4 = 0;
            var heads4 = new Dictionary<string, string>() {
                { "注册时间" ,"UserCreateTime"},
                { "手机号" ,"Mobile"},
            };

            heads4.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet4, headName, colIndex4);

                headerRow4.CreateCell(colIndex4).SetCellValue(headName);
                headerRow4.GetCell(colIndex4).CellStyle = headStyle;
                colIndex4++;
            });

            int rowIndex4 = 1;

            stat1688FxUserIdList?.Where(a => a.Flag == 2).ToList().ForEach(row =>
            {
                IRow dataRow = sheet4.CreateRow(rowIndex3);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new Stat1688FxUserId()).ToDictionary();

                colIndex4 = 0;
                foreach (var item in heads4.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    var val = dic[key]?.ToString() ?? "";

                    dataRow.CreateCell(colIndex4).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex4).CellStyle = tmpStyle;
                    colIndex4++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet4, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex4++;

            });

            #endregion

            #region 表5：老用户下游推品但未绑定明细 Flag=3
            ISheet sheet5 = workbook.CreateSheet("老用户下游推品但未绑定明细");

            IRow headerRow5 = sheet5.CreateRow(0);
            headerRow5.HeightInPoints = 15;

            int colIndex5 = 0;
            var heads5 = new Dictionary<string, string>() {
                { "注册时间" ,"UserCreateTime"},
                { "手机号" ,"Mobile"},
            };

            heads5.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet5, headName, colIndex5);

                headerRow5.CreateCell(colIndex5).SetCellValue(headName);
                headerRow5.GetCell(colIndex5).CellStyle = headStyle;
                colIndex5++;
            });

            int rowIndex5 = 1;

            stat1688FxUserIdList?.Where(a => a.Flag == 3).ToList().ForEach(row =>
            {
                IRow dataRow = sheet5.CreateRow(rowIndex5);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new Stat1688FxUserId()).ToDictionary();

                colIndex5 = 0;
                foreach (var item in heads5.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    var val = dic[key]?.ToString() ?? "";

                    dataRow.CreateCell(colIndex5).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex5).CellStyle = tmpStyle;
                    colIndex5++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet5, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex5++;

            });

            #endregion

            #region 表6：订单数据总汇
            ISheet sheet6 = workbook.CreateSheet("订单数据总汇");

            IRow headerRow6 = sheet6.CreateRow(0);
            headerRow6.HeightInPoints = 15;

            int colIndex6 = 0;
            var heads6 = new Dictionary<string, string>() {
                { "日期" ,"StatDate"},
                { "当日下采购单总数量" ,"PurchaseOrderNum"},
                { "当日下采购单成功数总量" ,"PurchaseOrderSuccessNum"},
                { "当日下采购单支付成功总数量" ,"PurchaseOrderPayedNum"},
                { "采购单已发货总数量" ,"PurchaseOrderSendedNum"},
                { "回流成功总数量" ,"ReturnSuccessNum"},
                { "下游后台回传1688" ,"DeliveryModel0SuccessNum"},
                { "1688回传下游后台" ,"DeliveryModel1SuccessNum"},
                { "其他ERP回传1688" ,"DeliveryModel10SuccessNum"},
                { "其他ERP回传下游后台" ,"DeliveryModel11SuccessNum"},
            };


            heads6.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet6, headName, colIndex6);

                headerRow6.CreateCell(colIndex6).SetCellValue(headName);
                headerRow6.GetCell(colIndex6).CellStyle = headStyle;
                colIndex6++;
            });

            int rowIndex6 = 1;

            statList.ForEach(row =>
            {
                IRow dataRow = sheet6.CreateRow(rowIndex6);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new Stat1688V2()).ToDictionary();

                colIndex6 = 0;
                foreach (var item in heads6.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    var val = dic[key]?.ToString() ?? "";

                    dataRow.CreateCell(colIndex6).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex6).CellStyle = tmpStyle;
                    colIndex6++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet6, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex6++;

            });

            #endregion

            #region 表7：下采购失败明细
            ISheet sheet7 = workbook.CreateSheet("下采购失败明细");

            IRow headerRow7 = sheet7.CreateRow(0);
            headerRow7.HeightInPoints = 15;

            int colIndex7 = 0;
            var heads7 = new Dictionary<string, string>() {
                { "商家账号" ,"AgentMobile"},
                { "厂家账号" ,"SupplierMobile"},
                { "失败原因" ,"PurchaseErrorMessage"},
            };

            heads7.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet7, headName, colIndex7);

                headerRow7.CreateCell(colIndex7).SetCellValue(headName);
                headerRow7.GetCell(colIndex7).CellStyle = headStyle;
                colIndex7++;
            });

            int rowIndex7 = 1;

            poRelationList?.ForEach(row =>
            {
                IRow dataRow = sheet7.CreateRow(rowIndex7);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new PurchaseOrderRelation()).ToDictionary();

                colIndex7 = 0;
                foreach (var item in heads7.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    var val = dic[key]?.ToString() ?? "";

                    dataRow.CreateCell(colIndex7).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex7).CellStyle = tmpStyle;
                    colIndex7++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet7, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex7++;

            });

            #endregion

            #region 表8：回流失败明细
            ISheet sheet8 = workbook.CreateSheet("回流失败明细");

            IRow headerRow8 = sheet8.CreateRow(0);
            headerRow8.HeightInPoints = 15;

            int colIndex8 = 0;
            var heads8 = new Dictionary<string, string>() {
                { "厂家账号" ,"SupplierMobile"},
                { "商家账号" ,"AgentMobile"},
                { "回流方式" ,"DeliveryMode"},
                { "失败原因" ,"ErrorMessage"},
            };

            var dicDeliveryModeName = new Dictionary<int, string>();
            dicDeliveryModeName.Add(0, "下游后台回传1688");
            dicDeliveryModeName.Add(1, "1688回传下游后台");
            dicDeliveryModeName.Add(10, "其他ERP回传1688");
            dicDeliveryModeName.Add(11, "其他ERP回传下游后台");

            heads8.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet8, headName, colIndex8);

                headerRow8.CreateCell(colIndex8).SetCellValue(headName);
                headerRow8.GetCell(colIndex8).CellStyle = headStyle;
                colIndex8++;
            });

            int rowIndex8 = 1;

            returnList?.ForEach(row =>
            {
                IRow dataRow = sheet8.CreateRow(rowIndex8);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new SendHistoryReturnRecord()).ToDictionary();

                colIndex8 = 0;
                foreach (var item in heads8.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    var val = dic[key]?.ToString() ?? "";

                    if (key == "DeliveryMode" && dicDeliveryModeName.ContainsKey(val.ToInt()))
                    {
                        val = dicDeliveryModeName[val.ToInt()];
                    }

                    dataRow.CreateCell(colIndex8).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex8).CellStyle = tmpStyle;
                    colIndex8++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet8, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex8++;

            });

            #endregion

            return workbook;
        }

        #endregion


        #region 导出订购记录统计数据
        /// <summary>
        /// 导出订购记录统计数据
        /// </summary>
        /// <param name="list"></param>
        /// <param name="fileName"></param>
        /// <param name="dicAppPlatformType"></param>
        /// <returns></returns>
        public static IWorkbook StatServiceAppOrderBuildExcel(List<StatServiceAppOrderV2> list, string fileName, Dictionary<string, string> dicAppPlatformType)
        {

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);

            if (list == null || list.Any() == false)
                return workbook;

            list = list.OrderBy(a => a.SettlementCycle).ToList();

            #region 表1：按账期统计的数据
            ISheet sheet = workbook.CreateSheet("按账期统计原始数据");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;


            int colIndex = 0;
            var heads = new Dictionary<string, string>() {
                { "统计类型" ,"StatFlag"},
                { "统计值" ,"StatValue"},
                { "统计值名称" ,"StatValueName"},
                { "账期" ,"SettlementCycle"},
                { "账期年份" ,"SettlementCycleYear"},
                { "店铺数量" ,"ShopCount"},
                { "到期应续费数量" ,"ExpireShopCount"},
                { "到期已续费数量" ,"RenewedShopCount"},
                { "到期未续费数量" ,"LossShopCount"},
                { "续费比例" ,"RenewRatio"},
                { "流失比例" ,"LossRatio"},
                { "结算总金额（拆月后当期金额）" ,"CycleAmout"},
                { "服务费用（拆月后当期金额）" ,"ServiceAmout"},
                { "实际结算总金额（拆月后当期金额）" ,"RealSettlementAmout"},
                { "订购店铺数（不拆月）" ,"FirstCycleShopCount"},
                { "订购总金额（不拆月）" ,"FirstCycleAmout"},
                { "新店铺数（不拆月）" ,"FirstOrderFirstCycleShopCount"},
                { "新店铺订购金额（不拆月）" ,"FirstOrderFirstCycleAmout"},
                { "老店铺数（不拆月）" ,"OldShopFirstCycleShopCount"},
                { "老店铺订购金额（不拆月）" ,"OldShopFirstCycleAmout"},
                { "待结算金额（拆月后，后续待结算的金额）" ,"WaitSettlementAmout"},
                { "备注" ,"Remark"},
                { "最后更新时间" ,"UpdateTime"},
            };


            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                var width = (headName.Length * 3 + 4) * 265;
                sheet.SetColumnWidth(colIndex, width);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;

            list.Where(a => a.StatFlag == "all").ToList().ForEach(row =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new StatServiceAppOrderV2()).ToDictionary();

                colIndex = 0;
                foreach (var item in heads.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    var val = dic[key]?.ToString() ?? "";

                    if (key == "RenewRatio" || key == "LossRatio")
                        val = val.ToDecimal().ToString("F4");

                    if (key == "SettlementCycle" && val.ToDateTime().HasValue)
                        val = val.ToDateTime().Value.ToString("yyyyMM");

                    dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                    colIndex++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;

            });

            #endregion

            #region 表2：按平台统计原始数据
            ISheet sheet2 = workbook.CreateSheet("按平台统计原始数据");

            IRow headerRow2 = sheet2.CreateRow(0);
            headerRow2.HeightInPoints = 15;

            int colIndex2 = 0;
            var heads2 = heads;

            heads2.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet2, headName, colIndex2);

                headerRow2.CreateCell(colIndex2).SetCellValue(headName);
                headerRow2.GetCell(colIndex2).CellStyle = headStyle;
                colIndex2++;
            });

            int rowIndex2 = 1;

            list.Where(a => a.StatFlag == "platform").OrderBy(a => a.StatValue).OrderBy(a => a.SettlementCycle).ToList().ForEach(row =>
            {
                IRow dataRow = sheet2.CreateRow(rowIndex2);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new StatServiceAppOrderV2()).ToDictionary();

                colIndex2 = 0;
                foreach (var item in heads2.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    var val = dic[key]?.ToString() ?? "";

                    if (key == "RenewRatio" || key == "LossRatio")
                        val = val.ToDecimal().ToString("F4");

                    if (key == "SettlementCycle" && val.ToDateTime().HasValue)
                        val = val.ToDateTime().Value.ToString("yyyyMM");

                    dataRow.CreateCell(colIndex2).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex2).CellStyle = tmpStyle;
                    colIndex2++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet2, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex2++;

            });

            #endregion

            #region 表3：按应用统计原始数据
            ISheet sheet3 = workbook.CreateSheet("按应用统计原始数据");

            IRow headerRow3 = sheet3.CreateRow(0);
            headerRow3.HeightInPoints = 15;

            int colIndex3 = 0;
            var heads3 = heads;

            heads3.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet3, headName, colIndex3);

                headerRow3.CreateCell(colIndex3).SetCellValue(headName);
                headerRow3.GetCell(colIndex3).CellStyle = headStyle;
                colIndex3++;
            });

            int rowIndex3 = 1;

            list.Where(a => a.StatFlag == "app").OrderBy(a => a.StatValue).OrderBy(a => a.SettlementCycle).ToList().ForEach(row =>
            {
                IRow dataRow = sheet3.CreateRow(rowIndex3);
                dataRow.HeightInPoints = 20;

                var dic = (row ?? new StatServiceAppOrderV2()).ToDictionary();

                colIndex3 = 0;
                foreach (var item in heads3.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();
                    var val = dic[key]?.ToString() ?? "";

                    if (key == "RenewRatio" || key == "LossRatio")
                        val = val.ToDecimal().ToString("F4");

                    if (key == "SettlementCycle" && val.ToDateTime().HasValue)
                        val = val.ToDateTime().Value.ToString("yyyyMM");

                    if (key == "StatValueName" && dicAppPlatformType.ContainsKey(val))
                    {
                        val = dicAppPlatformType[val];
                    }


                    dataRow.CreateCell(colIndex3).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex3).CellStyle = tmpStyle;
                    colIndex3++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet3, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex3++;

            });

            #endregion

            return workbook;

            #region TODO：表4：汇总表
            ISheet sheet4 = workbook.CreateSheet("汇总");

            IRow headerRow4 = sheet4.CreateRow(0);
            headerRow3.HeightInPoints = 15;

            int colIndex4 = 0;

            var years = list.GroupBy(g => g.SettlementCycleYear).OrderBy(g => g.Key).Select(g => g.Key).ToList();
            var months = list.GroupBy(g => g.SettlementCycle).OrderBy(g => g.Key).Select(g => g.Key).ToList();
            var platforms = list.Where(g => g.StatFlag == "platform").GroupBy(g => g.StatValue).OrderBy(g => g.Key).Select(g => g.Key).ToList();
            var apps = list.Where(g => g.StatFlag == "app").GroupBy(g => g.StatValue).OrderBy(g => g.Key).Select(g => g.Key).ToList();

            //只取前面12
            if (years.Count() > 12)
                years = years.Take(12).ToList();

            //只取前面144
            if (months.Count() > 144)
                months = months.Take(144).ToList();

            SetColumnWidth(sheet4, "项目", colIndex4);
            headerRow4.CreateCell(colIndex4).SetCellValue("项目");
            headerRow4.GetCell(colIndex4).CellStyle = headStyle;

            colIndex4++;
            years.ForEach(y =>
            {
                var headName = y.ToString();
                SetColumnWidth(sheet4, headName, colIndex4);

                headerRow4.CreateCell(colIndex4).SetCellValue(headName);
                headerRow4.GetCell(colIndex4).CellStyle = headStyle;
                colIndex4++;
            });
            months.ForEach(y =>
            {
                var headName = y.ToString("yyyyMM");
                SetColumnWidth(sheet4, headName, colIndex4);

                headerRow4.CreateCell(colIndex4).SetCellValue(headName);
                headerRow4.GetCell(colIndex4).CellStyle = headStyle;
                colIndex4++;
            });

            int rowIndex4 = 1;


            var dicRow = new Dictionary<int, List<string>>();

            dicRow.Add(rowIndex4, new List<string>() { "运营指标" });
            var curRowIndex = rowIndex4 + 1;
            dicRow.Add(curRowIndex, new List<string>() { "在期客户数量" });
            dicRow.Add(curRowIndex + 1, new List<string>() { "流失客户数量" });
            dicRow.Add(curRowIndex + 2, new List<string>() { "本期到期客户数量" });
            dicRow.Add(curRowIndex + 3, new List<string>() { "到期已续费客户数" });
            dicRow.Add(curRowIndex + 4, new List<string>() { "流失率" });
            dicRow.Add(curRowIndex + 5, new List<string>() { "续费率" });

            years.ForEach(y =>
            {
                var shopCount = list.Where(a => a.StatFlag == "all" && a.SettlementCycleYear == y).Sum(a => a.ShopCount);
                dicRow[curRowIndex].Add(shopCount.ToString2());

                var lossShopCount = list.Where(a => a.StatFlag == "all" && a.SettlementCycleYear == y).Sum(a => a.LossShopCount);
                dicRow[curRowIndex + 1].Add(lossShopCount.ToString2());

                var expireShopCount = list.Where(a => a.StatFlag == "all" && a.SettlementCycleYear == y).Sum(a => a.ExpireShopCount);
                dicRow[curRowIndex + 2].Add(expireShopCount.ToString2());

                var renewedShopCount = list.Where(a => a.StatFlag == "all" && a.SettlementCycleYear == y).Sum(a => a.RenewedShopCount);
                dicRow[curRowIndex + 3].Add(renewedShopCount.ToString2());

                var curSumExpireShopCount = list.Where(a => a.StatFlag == "all" && a.SettlementCycleYear == y).Sum(a => a.ExpireShopCount);
                var curSumLossShopCount = list.Where(a => a.StatFlag == "all" && a.SettlementCycleYear == y).Sum(a => a.LossShopCount);
                decimal lossRatio = 0;
                if (curSumExpireShopCount > 0)
                    lossRatio = (decimal)curSumLossShopCount / (decimal)curSumExpireShopCount;
                dicRow[curRowIndex + 4].Add(lossRatio.ToString("F4"));

                var curSumRenewedShopCount = list.Where(a => a.StatFlag == "all" && a.SettlementCycleYear == y).Sum(a => a.RenewedShopCount);
                decimal renewRatio = 0;
                if (curSumExpireShopCount > 0)
                    renewRatio = (decimal)curSumRenewedShopCount / (decimal)curSumExpireShopCount;
                dicRow[curRowIndex + 5].Add(renewRatio.ToString("F4"));

            });
            months.ForEach(y =>
            {
                var exist = list.FirstOrDefault(a => a.StatFlag == "all" && a.SettlementCycle == y);
                if (exist == null)
                {
                    dicRow[curRowIndex].Add("0");
                    dicRow[curRowIndex + 1].Add("0");
                    dicRow[curRowIndex + 2].Add("0");
                    dicRow[curRowIndex + 3].Add("0");
                    dicRow[curRowIndex + 4].Add("0");
                    dicRow[curRowIndex + 5].Add("0");
                }
                else
                {
                    dicRow[curRowIndex].Add(exist.ShopCount.ToString2());
                    dicRow[curRowIndex + 1].Add(exist.LossShopCount.ToString2());
                    dicRow[curRowIndex + 2].Add(exist.ExpireShopCount.ToString2());
                    dicRow[curRowIndex + 3].Add(exist.RenewedShopCount.ToString2());
                    dicRow[curRowIndex + 4].Add(exist.LossRatio.ToString("F4"));
                    dicRow[curRowIndex + 5].Add(exist.RenewRatio.ToString("F4"));
                }
            });
            curRowIndex = curRowIndex + 5;
            #region 当月付费企业数
            dicRow.Add(curRowIndex + 1, new List<string>() { "销售收入" });
            dicRow.Add(curRowIndex + 2, new List<string>() { "当月付费企业数" });
            curRowIndex = curRowIndex + 3;
            var ptNums = platforms.Count();//平台数量


            dicRow.Add(curRowIndex + ptNums + 1, new List<string>() { "当月付费订单总金额" });
            dicRow.Add(curRowIndex + ptNums + 2, new List<string>() { "%增速（同比）" });
            dicRow.Add(curRowIndex + ptNums * 2 + 4, new List<string>() { "新购付费企业数量" });
            dicRow.Add(curRowIndex + ptNums * 3 + 5, new List<string>() { "续费企业数量" });
            dicRow.Add(curRowIndex + ptNums * 4 + 6, new List<string>() { "新购付费订单金额" });
            dicRow.Add(curRowIndex + ptNums * 5 + 7, new List<string>() { "续费订单金额" });
            dicRow.Add(curRowIndex + ptNums * 6 + 8, new List<string>() { "结算口径（按平台）" });
            dicRow.Add(curRowIndex + ptNums * 6 + 9, new List<string>() { "结算金额合计" });
            dicRow.Add(curRowIndex + ptNums * 6 + 10, new List<string>() { "新购结算收入-按平台" });
            dicRow.Add(curRowIndex + ptNums * 7 + 11, new List<string>() { "续费结算收入-按平台" });

            platforms.ForEach(pt =>
            {
                var curPtIndex = curRowIndex;
                dicRow.Add(curPtIndex, new List<string>() { pt });
                dicRow.Add(curPtIndex + ptNums + 3, new List<string>() { pt });
                dicRow.Add(curPtIndex + ptNums * 2 + 5, new List<string>() { pt });
                dicRow.Add(curPtIndex + ptNums * 3 + 6, new List<string>() { pt });
                dicRow.Add(curPtIndex + ptNums * 4 + 7, new List<string>() { pt });
                dicRow.Add(curPtIndex + ptNums * 5 + 8, new List<string>() { pt });
                dicRow.Add(curPtIndex + ptNums * 6 + 11, new List<string>() { pt });
                dicRow.Add(curPtIndex + ptNums * 7 + 12, new List<string>() { pt });

                years.ForEach(y =>
                {
                    //当月付费企业数
                    var shopCount = list.Where(a => a.StatFlag == "platform" && a.SettlementCycleYear == y && a.StatValue == pt).Sum(a => a.FirstCycleShopCount);
                    dicRow[curPtIndex].Add(shopCount.ToString2());

                    //当月付费订单总金额
                    var sumAmount = list.Where(a => a.StatFlag == "platform" && a.SettlementCycleYear == y && a.StatValue == pt).Sum(a => a.FirstCycleAmout);
                    dicRow[curPtIndex + ptNums + 3].Add(sumAmount.ToString("F2"));

                    //新购付费企业数量
                    var shopCount2 = list.Where(a => a.StatFlag == "platform" && a.SettlementCycleYear == y && a.StatValue == pt).Sum(a => a.FirstOrderFirstCycleShopCount);
                    dicRow[curPtIndex + ptNums * 2 + 5].Add(sumAmount.ToString());

                    //续费企业数量（当期应续且已续）
                    var shopCount3 = list.Where(a => a.StatFlag == "platform" && a.SettlementCycleYear == y && a.StatValue == pt).Sum(a => a.RenewedShopCount);
                    dicRow[curPtIndex + ptNums * 3 + 6].Add(sumAmount.ToString());

                    //新购付费订单金额
                    var sumAmount2 = list.Where(a => a.StatFlag == "platform" && a.SettlementCycleYear == y && a.StatValue == pt).Sum(a => a.FirstOrderFirstCycleAmout);
                    dicRow[curPtIndex + ptNums * 4 + 7].Add(sumAmount2.ToString("F2"));

                    //续费订单金额
                    var sumAmount3 = list.Where(a => a.StatFlag == "platform" && a.SettlementCycleYear == y && a.StatValue == pt).Sum(a => a.RenewedFirstCycleAmout);
                    dicRow[curPtIndex + ptNums * 5 + 8].Add(sumAmount3.ToString("F2"));

                    //新购结算收入
                    var sumAmount4 = list.Where(a => a.StatFlag == "platform" && a.SettlementCycleYear == y && a.StatValue == pt).Sum(a => a.CycleAmout);
                    dicRow[curPtIndex + ptNums * 6 + 11].Add(sumAmount4.ToString("F2"));

                    //续费结算收入
                    var sumAmount5 = list.Where(a => a.StatFlag == "platform" && a.SettlementCycleYear == y && a.StatValue == pt).Sum(a => a.RenewedCycleAmout);
                    dicRow[curPtIndex + ptNums * 7 + 12].Add(sumAmount5.ToString("F2"));

                });
                months.ForEach(y =>
                {
                    var exist = list.FirstOrDefault(a => a.StatFlag == "platform" && a.SettlementCycle == y && a.StatValue == pt);
                    if (exist == null)
                    {
                        //当月付费企业数
                        dicRow[curPtIndex].Add("0");
                        //当月付费订单总金额
                        dicRow[curPtIndex + ptNums + 3].Add("0");
                        //新购付费企业数量
                        dicRow[curPtIndex + ptNums * 2 + 5].Add("0");
                        //续费企业数量（当期应续且已续）
                        dicRow[curPtIndex + ptNums * 3 + 6].Add("0");
                        //新购付费订单金额
                        dicRow[curPtIndex + ptNums * 4 + 7].Add("0");
                        //续费订单金额
                        dicRow[curPtIndex + ptNums * 5 + 8].Add("0");
                        //新购结算收入
                        dicRow[curPtIndex + ptNums * 6 + 11].Add("0");
                        //续费结算收入
                        dicRow[curPtIndex + ptNums * 7 + 12].Add("0");
                    }
                    else
                    {
                        //当月付费企业数
                        dicRow[curPtIndex].Add(exist.FirstCycleShopCount.ToString2());
                        //当月付费订单总金额
                        dicRow[curPtIndex + ptNums + 3].Add(exist.FirstCycleAmout.ToString("F2"));
                        //新购付费企业数量
                        dicRow[curPtIndex + ptNums * 2 + 5].Add(exist.FirstOrderFirstCycleShopCount.ToString());
                        //续费企业数量（当期应续且已续）
                        dicRow[curPtIndex + ptNums * 3 + 6].Add(exist.RenewedShopCount.ToString());
                        //新购付费订单金额
                        dicRow[curPtIndex + ptNums * 4 + 7].Add(exist.FirstCycleAmout.ToString("F2"));
                        //续费订单金额
                        dicRow[curPtIndex + ptNums * 5 + 8].Add(exist.RenewedFirstCycleAmout.ToString("F2"));
                        //新购结算收入
                        dicRow[curPtIndex + ptNums * 6 + 11].Add(exist.CycleAmout.ToString("F2"));
                        //续费结算收入
                        dicRow[curPtIndex + ptNums * 7 + 12].Add(exist.RenewedCycleAmout.ToString("F2"));
                    }

                });

                curRowIndex++;
            });
            #endregion

            curRowIndex += ptNums + 3;

            #region 客单价 

            dicRow.Add(curRowIndex, new List<string>() { "客单价" });
            years.ForEach(y =>
            {
                //客单价
                var sumAmount = list.Where(a => a.StatFlag == "all" && a.SettlementCycleYear == y).Sum(a => a.FirstOrderFirstCycleAmout);
                var sumCount = list.Where(a => a.StatFlag == "all" && a.SettlementCycleYear == y).Sum(a => a.FirstCycleShopCount);
                if (sumCount > 0)
                    dicRow[curRowIndex].Add((sumAmount / (decimal)sumCount).ToString("F2"));
                else
                    dicRow[curRowIndex].Add("0");

            });
            months.ForEach(y =>
            {
                var exist = list.FirstOrDefault(a => a.StatFlag == "all" && a.SettlementCycle == y);
                if (exist == null)
                    dicRow[curRowIndex].Add("0");
                else
                    dicRow[curRowIndex].Add(exist.UnitPrice.ToString("F2"));
            });
            curRowIndex++;

            #endregion

            #region 结算口径（按产品）

            curRowIndex = curRowIndex + ptNums * 6 + 8;

            dicRow.Add(curRowIndex + 1, new List<string>() { "结算口径（按产品）" });
            dicRow.Add(curRowIndex + 2, new List<string>() { "总收入" });
            dicRow.Add(curRowIndex + 3, new List<string>() { "新增收入" });

            #endregion


            rowIndex4 = 1;

            //排序
            var sortList = dicRow.OrderBy(g => g.Key).ToList();
            sortList.ForEach(row =>
            {
                var colValues = row.Value;

                IRow dataRow = sheet4.CreateRow(rowIndex4);
                dataRow.HeightInPoints = 20;

                colIndex4 = 0;
                foreach (var val in colValues)
                {
                    ICellStyle tmpStyle = contentStyle;
                    dataRow.CreateCell(colIndex4).SetCellValue(val.Trim());

                    dataRow.GetCell(colIndex4).CellStyle = tmpStyle;
                    colIndex4++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet4, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex4++;

            });

            #endregion


            return workbook;
        }

        #endregion

        #region 导出交易支付批次记录
        /// <summary>
        /// 导出交易支付批次记录
        /// </summary>
        /// <param name="list"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static IWorkbook PaymentStatementBuildExcel(List<PaymentStatement> list, string fileName, Dictionary<string, string> heads)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            FxUserShopService _fxUserShopService = new FxUserShopService();

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("交易明细记录");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;


            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;


            list.ForEach(row =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;

                var dic = (row).ToDictionary();

                colIndex = 0;
                foreach (var item in heads.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();

                    var val = dic.ContainsKey(key) ? dic[key]?.ToString() ?? "" : "";
                    if (key == nameof(PaymentStatement.PayChannel))
                    {
                        val = val == "wangshangpay" ? "网商支付" : string.Empty;
                    }
                    else if (key == "PayResult")
                    {
                        if (row.Status == 1 || row.Status == -1)
                        {
                            val = $"成功：{row.OrderTotalPaySuccessAmount}" +
                            $"{(row.OrderTotalPayFaildAmount > 0 ? $",\n失败：{row.OrderTotalPayFaildAmount}" : "")}" +
                            $"{(row.TotalRefundSuccessAmount > 0 ? $",\n退回：{row.TotalRefundSuccessAmount}" : "")}";
                        }
                        else
                        {
                            val = string.Empty;
                        }

                    }
                    else if (key == "Index")
                    {
                        val = $"{rowIndex}";
                    }

                    dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                    colIndex++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;

            });

            return workbook;
        }

        #endregion

        #region 导出交易支付批次订单明细记录
        /// <summary>
        /// 导出交易支付批次订单明细记录
        /// </summary>
        /// <param name="list"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static IWorkbook PaymentStatementOrderBuildExcel(List<PaymentStatementOrder> list, string fileName, Dictionary<string, string> heads)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            FxUserShopService _fxUserShopService = new FxUserShopService();

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("回流单");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;



            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;


            list.ForEach(row =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;

                var dic = (row).ToDictionary();

                colIndex = 0;
                foreach (var item in heads.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2();

                    var val = dic.ContainsKey(key) ? dic[key]?.ToString() ?? "" : "";
                    if (key == "Index")
                    {
                        val = $"{rowIndex}";
                    }
                    else if (key == "PayStatus")
                    {
                        val = PaymentStatementService.ConvertPayStatus(val);
                    }

                    dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));

                    dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                    colIndex++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;

            });

            return workbook;
        }

        #endregion
    }
}
