using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using System.Collections.Generic;
using System;
using System.Linq;
using System.Threading;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Services.Services
{
    public class FxDbConfigService : BaseService<FxDbConfig>
    {
        private readonly FxDbConfigRepository _repository;
        public FxDbConfigService()
        {
            _repository = new FxDbConfigRepository();
            this._baseRepository = _repository;
        }

        public FxDbConfigService(string connectionString) { 
            _repository= new FxDbConfigRepository(connectionString);
            this._baseRepository = _repository;
        }

        /// <summary>
        /// 获取数据配置信息，按用户ID
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="cloudPlatformType"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public FxDbConfig GetByFxUserId(int fxUserId, string cloudPlatformType, string selectFields = "*")
        {
            return _repository.GetByFxUserId(fxUserId, cloudPlatformType, selectFields);
        }
        /// <summary>
        /// 获取用户信息 按UserId Alibaba
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public FxDbConfig GetByFxUserIdAndAlibaba(int fxUserId, string selectFields = "*")
        {
            return _repository.GetByFxUserIdAndAlibaba(fxUserId, selectFields);
        }

        /// <summary>
        /// 设置FromFxDbConfig为1
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fxUserId"></param>
        public void SetFxDbConfig(int id, int fxUserId)
        {
            _repository.SetFxDbConfig(id, fxUserId);
        }

        /// <summary>
        /// 设置FromFxDbConfig为1
        /// </summary>
        /// <param name="fxUserIds"></param>
        public void SetFxDbConfigByFxUserId(List<int> fxUserIds)
        {
            _repository.SetFxDbConfigByFxUserId(fxUserIds);
        }

        /// <summary>
        /// 更新DbNameConfigId
        /// </summary>
        /// <param name="id"></param>
        /// <param name="dbNameConfigId"></param>
        /// <param name="fxUserId"></param>
        public void UpdateFxDbConfig(int id, int dbNameConfigId, int fxUserId)
        {
            _repository.UpdateFxDbConfig(id, dbNameConfigId, fxUserId);
        }

        /// <summary>
        /// 更新ColdDbNameConfigId
        /// </summary>
        /// <param name="id"></param>
        /// <param name="coldDbNameConfigId"></param>
        /// <param name="coldDbStatus"></param>
        /// <param name="fxUserId"></param>
        public bool UpdateColdDbConfig(int id, int? coldDbNameConfigId, int? coldDbStatus, int fxUserId)
        {
            return _repository.UpdateColdDbConfig(id, coldDbNameConfigId, coldDbStatus, fxUserId);
        }

        /// <summary>
        /// 创建新版业务库配置
        /// </summary>
        /// <param name="fxDbConfigs">FxUserId,SystemShopId,FromFxDbConfig必填</param>
        /// <param name="cloudPts"></param>
        /// <param name="checkExist">是否需要检查已存在</param>
        public void TryToCreateCloudFxDbConfig(List<FxDbConfig> fxDbConfigs, List<string> cloudPts, bool checkExist = true)
        {
            if (fxDbConfigs == null || !fxDbConfigs.Any())
                return;
            if (cloudPts == null || !cloudPts.Any())
                return;
            if (fxDbConfigs.Any(a => a.FxUserId <= 0))
                throw new Exception("数据错误");
            if (fxDbConfigs.Any(a => a.SystemShopId <= 0))
                throw new Exception("数据错误2");
            if (fxDbConfigs.Any(a => a.FromFxDbConfig != 1 && a.FromFxDbConfig != -1))
                throw new Exception("数据错误3");


            //Log.WriteLine($"检查是否有预分配新版配置3fxDbConfigs=>{fxDbConfigs?.ToJson()}");

            var db = _repository.DbConnection;
            if (db.State == System.Data.ConnectionState.Closed)
                db.Open();
            var cs = new CommonSettingService();
            using (db)
            {
                var dtNow = DateTime.Now;
                var fxUserIds = fxDbConfigs.Select(a => a.FxUserId).Distinct().ToList();
                foreach (var cpt in cloudPts)
                {
                    //var key = $"/SystemSetting/DefaultNewDbNameConfigId/{cpt}Cloud/System";
                    var key = $"/System/Fendan/FxDbConfig/DbNameConfigIdList/{cpt}Cloud";
                    var dbNameConfigIdList = cs.Get(key, 0)?.Value ?? "";
                    var arrDbNameId = dbNameConfigIdList.ToString2().Split(',');

                    var existFxUserIds = new List<int>();
                    if (checkExist)
                    {
                        var existFxDbConfigs = _repository.GetFxDbConfigs(fxUserIds, cpt).ToList();
                        existFxUserIds = existFxDbConfigs.Select(a => a.FxUserId).Distinct().ToList();
                    }

                    fxDbConfigs.Where(a => !existFxUserIds.Contains(a.FxUserId)).ToList().ForEach(fxDbConfig =>
                    {
                        //未指定，使用默认
                        if (fxDbConfig.DbNameConfigId <= 0 && arrDbNameId.Count() > 0)
                        {
                            var index = fxDbConfig.FxUserId % arrDbNameId.Count();
                            var dbNameConfigId = arrDbNameId[index].ToInt();
                            fxDbConfig.DbNameConfigId = dbNameConfigId;
                        }

                        fxDbConfig.DbCloudPlatform = cpt;
                        fxDbConfig.CreateTime = dtNow;
                        if (fxDbConfig.DbNameConfigId > 0)
                        {
                            for (int i = 0; i < 3; i++)
                            {
                                try
                                {
                                    db.Insert(fxDbConfig);
                                    break;
                                }
                                catch (Exception ex)
                                {
                                    //非阿里云平台，反向添加
                                    if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                                    {
                                        //获取阿里云配置库
                                        var dbApiAccessUtility = Data.Dapper.DbApiAccessUtility.GetConfigureDb(true);
                                        var dbConfigsByAlibaba = dbApiAccessUtility.Query<FxDbConfig>(
                                            "SELECT TOP 1 * FROM FxDbConfig(NOLOCK) WHERE FxUserId=@FxUserId AND DbCloudPlatform = @DbCloudPlatform",
                                            new
                                            {
                                                fxDbConfig.FxUserId,
                                                fxDbConfig.DbCloudPlatform
                                            });
                                        //是否获取Fx数据库配置
                                        if (dbConfigsByAlibaba != null && dbConfigsByAlibaba.Any())
                                        {
                                            var dbConfigByAlibaba = dbConfigsByAlibaba.FirstOrDefault();
                                            try
                                            {
                                                _repository.BulkInsert(new List<FxDbConfig> { dbConfigByAlibaba });
                                                Log.WriteLine(
                                                    $"精选{fxDbConfig.FxUserId}用户数据库配置已经存在，{fxDbConfig.DbCloudPlatform} 平台不存在，反向添加成功！");
                                                break;
                                            }
                                            catch (Exception e)
                                            {
                                                Log.WriteLine(
                                                    $"精选{fxDbConfig.FxUserId}用户数据库配置已经存在，{fxDbConfig.DbCloudPlatform} 平台不存在，反向添加失败，失败原因：{e.Message}！");
                                            }
                                        }
                                    }

                                    //日志
                                    Log.WriteError($"第{i + 1}次尝试：TryToCreateCloudFxDbConfig创建FxDbConfig时发生错误，用户Id:{fxDbConfig.FxUserId}，DbCloudPlatform={cpt}，错误详情：{ex.Message}");
                                    if (ex.Message?.Contains("PRIMARY KEY") == true ||
                                        ex.Message?.Contains("UNIQUE KEY") == true ||
                                        ex.Message?.Contains("duplicate key") == true)
                                        break;
                                    Thread.Sleep(500);
                                }
                            }
                            //清理缓存
                            _repository.RefreshDbConfigsCache(fxDbConfig.FxUserId);
                        }
                        else
                        {
                            Log.WriteError(
                                $"TryToCreateCloudFxDbConfig，DbNameConfigId<=0，添加信息：{fxDbConfig.ToJson()},堆栈信息：{Environment.StackTrace}");
                        }

                    });
                }

            }

        }


        /// <summary>
        /// 获取新版数据配置信息，不存在创建（FromFxDbConfig=-1）。
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="cloudPlatformType"></param>
        /// <returns></returns>
        public List<FxDbConfig> GetAndTryCreateByFxUserId(List<int> fxUserIds, string cloudPlatformType)
        {
            //云平台为空，则当前云平台
            if (string.IsNullOrWhiteSpace(cloudPlatformType))
            {
                if (CustomerConfig.IsCrossBorderSite)
                    cloudPlatformType = CustomerConfig.CrossBorderCloudLocation;
                else
                    cloudPlatformType = CustomerConfig.CloudPlatformType;
            }
            //if (string.IsNullOrEmpty(cloudPlatformType) && (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString() || CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString()))
            //{
            //    cloudPlatformType = CloudPlatformType.TouTiao.ToString();
            //}
            var list = _repository.GetFxDbConfigs(fxUserIds, cloudPlatformType);

            var existFxUserIds = list.Select(a => a.FxUserId).ToList();
            var notExistFxUserId = fxUserIds.Where(a => !existFxUserIds.Contains(a)).ToList();
            Log.Debug(() => $"需要初始化FxDbConfig厂家信息：{notExistFxUserId.ToJson(true)}",
                $"InitFactoryFxDbConfig_{DateTime.Now:yyyy-MM-dd}.log");
            //Log.WriteLine($"检查是否有预分配新版配置fxUserIds=>{fxUserIds?.ToJson()},existFxUserIds=>{existFxUserIds?.ToJson()}，notExistFxUserId=>{notExistFxUserId?.ToJson()}");
            if (notExistFxUserId.Any())
            {
                var fxDbConfigs = new List<FxDbConfig>();
                //var shops = new FxUserShopService().GetSystemShops(notExistFxUserId);
                var shops = new UserFxService().GetSystemShopsByFxUserIdsWithCache(notExistFxUserId);
                notExistFxUserId.ForEach(fxUserId =>
                {
                    var systemShopId = shops.FirstOrDefault(a => a.FxUserIds == fxUserId)?.Id ?? 0;
                    if (systemShopId > 0)
                    {
                        fxDbConfigs.Add(new FxDbConfig
                        {
                            FxUserId = fxUserId,
                            SystemShopId = systemShopId,
                            FromFxDbConfig = -1,
                            DbCloudPlatform = cloudPlatformType
                        });
                    }
                });
                TryToCreateCloudFxDbConfig(fxDbConfigs, new List<string> { cloudPlatformType }, false);
                list.AddRange(fxDbConfigs);
            }
            return list;
        }

        public List<FxDbConfig> GetFxDbConfigs(List<int> fxUserIds, string dbCloudPlatform = "")
        {
            if (fxUserIds == null || !fxUserIds.Any())
                return new List<FxDbConfig>();

            var list = new List<FxDbConfig>();
            var batchSize = 500;
            var count = Math.Ceiling(fxUserIds.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = fxUserIds.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetFxDbConfigs(batchCodes, dbCloudPlatform);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }
        /// <summary>
        /// 获取DbServerId
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="dbCloudPlatform"></param>
        /// <returns></returns>
        public List<int> GetFxDbServerIds(List<int> fxUserIds, string dbCloudPlatform = "")
        {
            if (fxUserIds == null || !fxUserIds.Any())
                return new List<int>();

            var list = new List<int>();
            var batchSize = 500;
            var count = Math.Ceiling(fxUserIds.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = fxUserIds.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetFxDbServerIds(batchCodes, dbCloudPlatform);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            list = list.Distinct().ToList();
            return list;
        }


        /// <summary>
        /// 获取待添加任务的用户
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="sourceDbNameConfigIds"></param>
        /// <param name="fromFxDbConfig"></param>
        /// <param name="maxId">上次查询Id</param>
        public List<FxDbConfig> GetList(int pageIndex, int pageSize, List<int> sourceDbNameConfigIds = null, int fromFxDbConfig = 1, int maxId = 0)
        {
            if (pageIndex <= 0)
                pageIndex = 1;
            if (pageSize <= 0)
                pageIndex = 10;

            if (sourceDbNameConfigIds == null || !sourceDbNameConfigIds.Any())
            {
                return new List<FxDbConfig>();
            }

            return _repository.GetList(pageIndex, pageSize, sourceDbNameConfigIds, fromFxDbConfig, maxId);
        }

        /// <summary>
        /// 没有抖店店铺的用户
        /// </summary>
        /// <param name="topNum"></param>
        /// <returns></returns>
        public List<FxDbConfig> GetNotTouTiaoShopUserList(int topNum = 500)
        {
            return _repository.GetNotTouTiaoShopUserList(topNum);
        }

        /// <summary>
        /// 获取没有分配冷库的FxDbConfig数据
        /// </summary>
        /// <param name="topNum"></param>
        /// <param name="dbCloudPt"></param>
        /// <returns></returns>
        public List<FxDbConfig> GetFxDbConfigColdDbIsNull(int topNum, string dbCloudPt)
        {
            return _repository.GetFxDbConfigColdDbIsNull(topNum, dbCloudPt);
        }

        /// <summary>
        /// 获取没有分配冷库的FxDbConfig数据
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="dbCloudPt"></param>
        /// <returns></returns>
        public List<FxDbConfig> GetFxDbConfigByFxUserId(List<int> fxUserIds, string dbCloudPt)
        {
            return _repository.GetFxDbConfigByFxUserId(fxUserIds, dbCloudPt);
        }

        /// <summary>
        /// 获取已分库迁移用户
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="dbCloudPlatform"></param>
        /// <returns></returns>
        public List<FxDbConfig> GetMigratedFxDbConfigs(List<int> fxUserIds = null, string dbCloudPlatform = null)
        {
            return _repository.GetMigratedFxDbConfigs(fxUserIds, dbCloudPlatform);
        }

        /// <summary>
        /// 获取库中未开启冷的用户数
        /// </summary>
        /// <param name="dbNameConfigId"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public int GetDbUnColdUserCount(int dbNameConfigId)
        {
            return _repository.GetDbUnColdUserCount(dbNameConfigId);
        }

        /// <summary>
        /// 获取指定库的用户数
        /// </summary>
        /// <param name="dbNameConfigId"></param>
        /// <returns></returns>
        public int GetDbUserCount(int dbNameConfigId) { 
            return _repository.GetDbUserCount(dbNameConfigId);
        }

        public int GetDbDataRowsCount(string tableName)
        {
            return _repository.GetDbDataRowsCount(tableName);
        }
        /// <summary>
        /// 根据服务器Id，获取该实例下的数据库信息
        /// </summary>
        /// <param name="serverId"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<DbNameConfig> GetDbConfigByServerId(string serverId, string platformType)
        { 
            return _repository.GetDbConfigByServerId(serverId, platformType);
        }
    }
}