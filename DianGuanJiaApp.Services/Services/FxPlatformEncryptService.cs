using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Repository;
using Top.Api;
using Top.Api.Request;
using Top.Api.Response;
using DianGuanJiaApp.Data.Entity;
using System.Configuration;
using System.Collections;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.WaybillService;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json.Linq;
using DianGuanJiaApp.Utility;
using Newtonsoft.Json.Converters;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services.SyncDataInterface;
using NPOI.HSSF.Record;
using System.Web.UI.WebControls;

namespace DianGuanJiaApp.Services
{
    public class FxPlatformEncryptService
    {
        //public static List<string> encryptPlatformTypes = new List<string> { 
        //    PlatformType.Pinduoduo.ToString(), 
        //    PlatformType.Taobao.ToString(), 
        //    PlatformType.Alibaba.ToString(), 
        //    PlatformType.AlibabaC2M.ToString(), 
        //    PlatformType.TouTiao.ToString(), 
        //    PlatformType.Jingdong.ToString(), 
        //    PlatformType.KuaiShou.ToString(), 
        //    PlatformType.YouZan.ToString(), 
        //    PlatformType.Suning.ToString(), 
        //    PlatformType.TuanHaoHuo.ToString(), 
        //    PlatformType.XiaoHongShu.ToString(), 
        //    PlatformType.WeiDian.ToString() 
        //};
        public static List<string> encryptPlatformTypes = CustomerConfig.EncryptPlatformTypes();

        /// <summary>
        /// 需要另外查询加密字符串的平台
        /// </summary>
        public static List<string> QueryEncryptedReceiverPlatformTypes = new List<string>() {
            PlatformType.KuaiShou.ToString(),
            PlatformType.TuanHaoHuo.ToString(),
            PlatformType.XiaoHongShu.ToString()
        };

        #region 平台加密

        public static string EncryptOrders(List<LogicOrder> orders, EncryptFromType fromType, bool isMask = true, string field = "", string waybill_type = "", bool encryptSender = false, bool encryptWangwang = false, bool needSyncLog = false, bool needPlatformDecrypt = true, bool throwEx = false)
        {
            var msg = string.Empty;
            if (orders == null || orders.Any() == false)
                return msg;
            // 底单、打印记录、发货记录需要重新赋值ShopId和平台
            if (fromType == EncryptFromType.WaybillCode || fromType == EncryptFromType.PrintHistory || fromType == EncryptFromType.SendHistory)
            {
                var _logicOrderService = new LogicOrderService();
                var fields = new List<string> { "o.Id", "o.PlatformOrderId", "o.ShopId", "o.LogicOrderId", "o.PlatformType", "o.MergeredType", "o.ChildOrderId", "oi.Id", "oi.OrignalOrderId", "oi.PlatformOrderId", "oi.LogicOrderId" };
                var ids = orders.Select(x => x.LogicOrderId).Distinct().ToList();
                var olst = _logicOrderService.GetOrders(ids, fields: fields, queryReceiver: new QueryReceiverModel { IsOnlyGetMask = false }).ToList();
                var oDic = olst?.GroupBy(x => x.LogicOrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault()) ?? new Dictionary<string, LogicOrder>();

                // 打印记录没有订单原始店铺Id，订单删除后无法正常解密，改为从底单再查找一次
                if (fromType == EncryptFromType.PrintHistory)
                {
                    var userFx = SiteContext.Current.CurrentFxUser;
                    if (olst == null || olst.Any() == false)
                    {
                        var dbArea = SiteContext.Current.CurrentDbAreaConfig.Select(x => x.DbNameConfig).Select(y => new { DbName = y.DbName, y.NickName }).ToList();
                        if (dbArea != null && dbArea.Any())
                        {
                            foreach (var _db in dbArea)
                            {
                                //指定分库
                                var sc = new SiteContext(userFx, _db.DbName, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
                                olst = _logicOrderService.GetOrders(ids, fields: fields, queryReceiver: new QueryReceiverModel { IsOnlyGetMask = false }).ToList();
                                oDic = olst?.GroupBy(x => x.LogicOrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault()) ?? new Dictionary<string, LogicOrder>();
                                break;
                            }
                        }
                    }

                    var notFoundOrders = orders.Where(x => oDic.ContainsKey(x.LogicOrderId) == false).ToList();
                    var notFoundWybCodes = notFoundOrders.Select(x => x.LastWaybillCode).Distinct().ToList();
                    if (notFoundWybCodes.Any())
                    {
                        //Utility.Log.Debug($"打印记录未找到订单，准备从底单获取原始店铺Id：{notFoundOrders.ToJson()}");
                        var wybService = new WaybillCodeService();
                        var wybList = wybService.GetWaybillCodeListByWaybillCodes(notFoundWybCodes, status: "");
                        //Utility.Log.Debug($"打印记录未找到订单，获取底单数据：{wybList.ToJson()}");
                        foreach (var o in notFoundOrders)
                        {
                            var wyb = wybList.FirstOrDefault(x => x.ExpressWayBillCode == o.LastWaybillCode);
                            o.ShopId = wyb?.SourceShopId ?? o.ShopId;
                        }
                        //Utility.Log.Debug($"打印记录未找到订单，从底单获取原始店铺Id：{notFoundOrders.ToJson()}");
                    }
                }

                orders.ForEach(o =>
                {
                    LogicOrder lo;
                    var key = o.LogicOrderId;
                    //底单、打印记录、发货记录校正字段数据（平台单号、店铺Id）
                    if (oDic.TryGetValue(key, out lo) && lo != null)
                    {
                        o.PlatformOrderId = lo.PlatformOrderId;
                        //o.LogicOrderId = lo.LogicOrderId;
                        o.ShopId = lo.ShopId;
                        //o.MergeredType = lo.MergeredType;
                        o.PlatformType = lo.PlatformType;
                        o.LogicOrderItems = lo.LogicOrderItems;
                        o.ExtField1 = lo.ExtField1;
                    }
                    else if (key.StartsWith("C"))
                    {
                        // 合单未找到（被拆除），取对应子单
                        //key = key.StartsWith("C") ? o.LogicOrderId.Substring(1) : o.LogicOrderId;
                        var keys = o.ChildOrderId.SplitToList(",");
                        keys.Add(o.LogicOrderId.Substring(1));

                        var order = _logicOrderService.GetOrders(keys, queryReceiver: new QueryReceiverModel { IsOnlyGetMask = false })?.FirstOrDefault(x => x.PlatformOrderId == o.PlatformOrderId);
                        if (order != null)
                        {
                            o.PlatformOrderId = order.PlatformOrderId;
                            //o.LogicOrderId = order.LogicOrderId;
                            o.ShopId = order.ShopId;
                            o.MergeredType = 3;
                            o.ToName = order.ToName;
                            o.ToPhone = order.ToPhone;
                            o.ToAddress = order.ToAddress;
                            o.ToFullAddress = order.ToFullAddress;
                            o.PlatformType = order.PlatformType;
                            o.LogicOrderItems = order.LogicOrderItems;
                            o.ExtField1 =order.ExtField1;

                            Utility.Log.Debug($"解密前处理合单未找到，取任意子单：\n合单：{o.ToJson()}子单：{order.ToJson()}");
                        }
                        else
                        {
                            Utility.Log.Debug($"【{fromType.ToString()}】查询=>订单【{o.LogicOrderId}】未找到相关信息");
                        }
                    }
                });
            }

            var _shopService = new ShopService();
            var allShopIds = orders.Select(x => x.ShopId).Distinct().ToList();
            var allShops = _shopService.GetShopByIds(allShopIds);
            if (allShops == null || allShops.Any() == false)
                throw new LogicException($"未找到店铺【{string.Join(",", allShopIds)}】信息");
            orders?.Where(x => x.ShopId > 0).GroupBy(o => o.ShopId).ToList().ForEach(g =>
            {
                //判断是否需要加密的平台
                var hasEncrypt = g.Any(x => encryptPlatformTypes.Contains(x.PlatformType.ToString2()));
                if (!hasEncrypt)
                    return;

                var first = g.FirstOrDefault();
                var pids = new List<string>();
                //if (first.PlatformType == PlatformType.Virtual.ToString()) 线下单的手机号也做了加密
                //    return;
                var shop = allShops.FirstOrDefault(s => s.Id == g.Key);
                if (shop == null)
                    throw new LogicException($"店铺【{g.Key}】不存在，无法进行加密");

                var pt = shop.PlatformType; // 订单平台类型
                var gorders = g.ToList();
                gorders.ForEach(o =>
                {
                    if (o.PlatformType.IsNullOrEmpty())
                        o.PlatformType = pt;
                    if (o.ChildOrderId.IsNullOrEmpty())
                        o.ChildOrderId = o.LogicOrderItems.Select(x => x.OrignalOrderId).Distinct().ToStringWithSplit(",");
                });

                var needDecryptOrders = new List<LogicOrder>();
                var noNeedDecryptOrders = new List<LogicOrder>();
                var noMaskHistoryOrders = new List<LogicOrder>();
                if (pt == PlatformType.Pinduoduo.ToString() || pt == PlatformType.TouTiao.ToString() || pt == PlatformType.TouTiaoSaleShop.ToString())
                {
                    #region 头条和拼多多解密
                    try
                    {
                        if (needPlatformDecrypt)
                        {
                            gorders.ForEach(o =>
                            {
                                if (pt == PlatformType.Pinduoduo.ToString())
                                {
                                    if (PinduoduoPlatformService.IsEncryptData(o.ToName) || PinduoduoPlatformService.IsEncryptData(o.ToPhone) || PinduoduoPlatformService.IsEncryptData(o.ToAddress))
                                        needDecryptOrders.Add(o);
                                    else
                                        noNeedDecryptOrders.Add(o);
                                }
                                else
                                {
                                    if (ZhiDianNewPlatformService.IsEncryptData(o.ToName) || ZhiDianNewPlatformService.IsEncryptData(o.ToPhone) || ZhiDianNewPlatformService.IsEncryptData(o.ToAddress))
                                        needDecryptOrders.Add(o);
                                    else
                                        noNeedDecryptOrders.Add(o);
                                }
                            });

                            //Utility.Log.WriteLine($"待解密订单：{needDecryptOrders.Count},不需解密订单数：{noNeedDecryptOrders.Count}");

                            if (needDecryptOrders.Any())
                                DecryptOrders(needDecryptOrders, fromType, shops: allShops, isMask: isMask, field: field, waybill_type: waybill_type);
                            //历史未加密数据加密处理
                            if (isMask && noNeedDecryptOrders.Any())
                                HistoryOrderMask(noNeedDecryptOrders);
                        }
                        else
                            DecryptOrderNoPlatform(gorders);
                        //历史未加密数据加密处理
                        if (isMask)
                            HistoryOrderMask(gorders, true);
                    }
                    catch (LogicException ex)
                    {
                        msg = DecryptErrMethod(throwEx, _shopService, shop, gorders, ex);
                    }
                    catch (Exception ex)
                    {
                        msg = DecryptErrMethod(throwEx, _shopService, shop, gorders, ex);
                    }
                    #endregion
                }
                else if (pt == PlatformType.Alibaba.ToString() || pt == PlatformType.AlibabaC2M.ToString() || pt == PlatformType.Taobao.ToString() || pt == PlatformType.TaobaoMaiCaiV2.ToString())
                {
                    #region Alibaba、AlibabaC2M、淘宝解密
                    try
                    {
                        if (needSyncLog && pt == PlatformType.Taobao.ToString())
                            pids = gorders.Select(x => x.PlatformOrderId).ToList();

                        // 淘宝数据库加密是脱敏的（不需要脱敏，直接返回明文）
                        gorders.ForEach(o =>
                        {
                            //需要排除IsDecrypted=true的订单，传入的订单可能有不需要解密的订单，比如1688分销订单，默认是false;
                            if (o.ToPhone.ToString2().Contains("*") && !o.IsDecrypted)
                                needDecryptOrders.Add(o);
                            else
                                noNeedDecryptOrders.Add(o);
                        });
                        // 淘宝数据库加密是脱敏的（不需要脱敏，直接返回明文）
                        if (needPlatformDecrypt && !isMask && needDecryptOrders.Any())
                            DecryptOrders(needDecryptOrders, fromType, shops: allShops, isMask: isMask, field: field, waybill_type: waybill_type);
                        //历史未加密数据加密处理
                        if (isMask && noNeedDecryptOrders.Any())
                            HistoryOrderMask(noNeedDecryptOrders);
                    }
                    catch (Exception ex)
                    {
                        msg = DecryptErrMethod(throwEx, _shopService, shop, gorders, ex);
                    }
                    #endregion
                }
                else if (pt == PlatformType.Jingdong.ToString() || pt == PlatformType.JingdongPurchase.ToString())
                {
                    #region 京东解密
                    try
                    {
                        if (needSyncLog)
                            pids = gorders.Select(x => x.PlatformOrderId).ToList();
                        var service = PlatformFactory.GetPlatformService(shop);
                        gorders.ForEach(o =>
                        {
                            // 不需要脱敏，直接返回明文
                            if (isMask)
                            {
                                //替换密文详细地址，再追加解密后详细地址
                                o.ToPhone = o.ToPhone.ToEncrytPhone();
                                o.ToGPhone = o.ToGPhone.ToEncrytPhone();
                                o.ToName = o.ToName.ToEncryptName();
                                //京东集运订单地址不打码。地址最后面有防合单码，需要展示
                                if (o.OrderTags.Where(x => x.Tag == OrderTag.consolidateInfo.ToString2()) == null || !o.OrderTags.Where(x => x.Tag == OrderTag.consolidateInfo.ToString2()).Any()) {
                                    o.ToFullAddress = o.ToFullAddress.TrimEnd(o.ToAddress);
                                    o.ToAddress = o.ToAddress.ToTaoBaoEncryptAddress();
                                    o.ToFullAddress = o.ToFullAddress + o.ToAddress;
                                }
                                return;
                            }

                            if (encryptWangwang)
                                o.BuyerWangWang = o.BuyerWangWang.ToEncryptName();

                            //京东收件人电话加密存储在extfield4字段中，要解密。
                            Utility.Log.WriteLine($"京东解密：DecryptField:{o.DecryptField}");
                            if (o.DecryptField.IsNotNullOrEmpty())
                            {
                                var jdService = service as JingDongPlatformService;
                                if (o.DecryptField.Length == 70)
                                {
                                    var tuple = jdService.GetMobilelist(o.PlatformOrderId.TrimStart("C"));
                                    if (tuple.Item1.IsNotNullOrEmpty())
                                    {
                                        o.ToPhone = tuple.Item1;
                                        //京东解密后是虚拟号，需将虚拟号的分机号剔除出来，追加到姓名和地址后面[xxxx]
                                        if (o.ToPhone.Contains("-"))
                                        {
                                            var splitMobile = o.ToPhone.Split('-');
                                            if (splitMobile.Length == 2)
                                            {
                                                o.ToPhone = splitMobile[0];
                                                var vritualNumber = $"({splitMobile[1]})";
                                                o.ToName += vritualNumber;
                                                o.ToAddress += vritualNumber;
                                            }
                                        }
                                        o.ToGPhone = "";
                                    }
                                    else if (tuple.Item2.IsNotNullOrEmpty())
                                    {
                                        o.ToPhone = "";
                                        o.ToGPhone = tuple.Item2;
                                    }
                                }
                                else
                                {
                                    Utility.Log.WriteLine($"京东解密DecryptString：DecryptField:{o.DecryptField}");
                                    var phone = jdService.DecryptString(o.DecryptField);
                                    o.ToPhone = phone;
                                }
                            }
                            else
                                o.ToPhone = o.ToPhone;
                            //o.ToCounty = o.ToCounty;
                        });

                        //历史未加密数据加密处理
                        if (isMask)
                            HistoryOrderMask(gorders, true);
                    }
                    catch (Exception ex)
                    {
                        msg = DecryptErrMethod(throwEx, _shopService, shop, gorders, ex);
                    }
                    #endregion
                }
                else if (pt == PlatformType.KuaiShou.ToString())
                {
                    #region 快手解密
                    try
                    {
                        //区分加密的订单
                        gorders.ForEach(o =>
                        {
                            if (string.IsNullOrWhiteSpace(o.ExtField1) == false //有加密信息
                            //并且是密文,或者是脱敏信息且需要解密成明文
                            && ((KuaiShouPlatformService.IsEncryptData(o.ToName)
                            || KuaiShouPlatformService.IsEncryptData(o.ToPhone)
                            || KuaiShouPlatformService.IsEncryptData(o.ToAddress))
                            || ((KuaiShouPlatformService.IsDesensitiseData(o.ToName, KuaiShouEncryptType.Name)
                            || KuaiShouPlatformService.IsDesensitiseData(o.ToPhone, KuaiShouEncryptType.Mobile)
                            || KuaiShouPlatformService.IsDesensitiseData(o.ToAddress, KuaiShouEncryptType.Address)) && isMask == false)))
                                needDecryptOrders.Add(o);
                            else
                                noNeedDecryptOrders.Add(o);
                        });
                        if (needDecryptOrders.Any())
                        {
                            //无需再查2022.11.23
                            ////快手的加密信息另外存储的，需另外查询
                            //var kSEncryptedReceiverService = new KuaiShouEncryptedReceiverInfoService();
                            //kSEncryptedReceiverService.SetOrderEncryptedReceiverInfoByLogicOrder(needDecryptOrders);
                            //解密
                            DecryptOrders(needDecryptOrders, fromType, shops: allShops, isMask: isMask, field: field, waybill_type: waybill_type);
                        }
                        //历史未加密数据加密处理
                        if (isMask && noNeedDecryptOrders.Any())
                        {
                            HistoryOrderMask(noNeedDecryptOrders, true);
                        }
                            
                    }
                    catch (Exception ex)
                    {
                        msg = DecryptErrMethod(throwEx, _shopService, shop, gorders, ex);
                        //Utility.Log.WriteError($"{shop.PlatformType}：店铺【{shop.NickName}】解密失败：{ex}");
                        ////授权过期处理
                        //if (!throwEx)
                        //    DecryptOrderWithXing(gorders);
                        //else
                        //    throw ex;
                    }
                    #endregion
                }
                else if (pt == PlatformType.YouZan.ToString())
                {
                    #region 有赞解密
                    try
                    {
                        if (needPlatformDecrypt)
                        {
                            gorders.ForEach(o =>
                            {
                                if (YouZanPlatformService.IsEncryptData(o.ToName)
                                || YouZanPlatformService.IsEncryptData(o.ToPhone)
                                || YouZanPlatformService.IsEncryptData(o.ToAddress)
                                || YouZanPlatformService.IsEncryptData(o.BuyerWangWang))
                                    needDecryptOrders.Add(o);
                                else
                                    noNeedDecryptOrders.Add(o);
                            });
                            if (needDecryptOrders.Any())
                                DecryptOrders(needDecryptOrders, fromType, shops: allShops, isMask: isMask, field: field, waybill_type: waybill_type);
                            //历史未加密数据加密处理
                            if (isMask && noNeedDecryptOrders.Any())
                                HistoryOrderMask(noNeedDecryptOrders, true);
                        }
                        else
                            DecryptOrderNoPlatform(gorders);
                    }
                    catch (Exception ex)
                    {
                        msg = DecryptErrMethod(throwEx, _shopService, shop, gorders, ex);
                    }
                    #endregion
                }
                else if (pt == PlatformType.Suning.ToString())
                {
                    #region 苏宁解密
                    try
                    {
                        gorders.ForEach(o =>
                        {
                            if (SuningPlatformService.IsEncryptData(o.ToName)
                            || SuningPlatformService.IsEncryptData(o.ToPhone)
                            || SuningPlatformService.IsEncryptData(o.ToAddress)
                            || SuningPlatformService.IsEncryptData(o.BuyerWangWang))
                                needDecryptOrders.Add(o);
                            else
                                noNeedDecryptOrders.Add(o);
                        });
                        if (needDecryptOrders.Any())
                            DecryptOrders(needDecryptOrders, fromType, shops: allShops, isMask: isMask, field: field, waybill_type: waybill_type);
                        //历史未加密数据加密处理
                        if (isMask && noNeedDecryptOrders.Any())
                            HistoryOrderMask(noNeedDecryptOrders, true);
                    }
                    catch (Exception ex)
                    {
                        msg = DecryptErrMethod(throwEx, _shopService, shop, gorders, ex);
                        //Utility.Log.WriteError($"{shop.PlatformType}：店铺【{shop.NickName}】解密失败：{ex}");
                        ////授权过期处理
                        //if (!throwEx)
                        //    DecryptOrderWithXing(gorders);
                        //else
                        //    throw ex;
                    }
                    #endregion
                }
                else if (pt == PlatformType.XiaoHongShu.ToString())
                {
                    #region 小红书解密
                    try
                    {
                        gorders.ForEach(o =>
                        {
                            if (o.ExtField1.IsNotNullOrEmpty())
                            {
                                if (((XiaoHongShuV2PlatformService.IsEncryptData(o.ToName)
                                    || XiaoHongShuV2PlatformService.IsEncryptData(o.ToPhone)
                                    || XiaoHongShuV2PlatformService.IsEncryptData(o.ToAddress))
                                    || ((XiaoHongShuV2PlatformService.IsDesensitiseData(o.ToName, KuaiShouEncryptType.Name)
                                    || XiaoHongShuV2PlatformService.IsDesensitiseData(o.ToPhone, KuaiShouEncryptType.Mobile)
                                    || XiaoHongShuV2PlatformService.IsDesensitiseData(o.ToAddress, KuaiShouEncryptType.Address)) && isMask == false)))
                                    needDecryptOrders.Add(o);
                                else
                                    noNeedDecryptOrders.Add(o);
                            }
                            else
                                noMaskHistoryOrders.Add(o);

                            //if (string.IsNullOrWhiteSpace(o.ExtField1) == false //有加密信息
                            //                                                    //并且是密文,或者是脱敏信息且需要解密成明文
                            //&& ((XiaoHongShuV2PlatformService.IsEncryptData(o.ToName)
                            //|| XiaoHongShuV2PlatformService.IsEncryptData(o.ToPhone)
                            //|| XiaoHongShuV2PlatformService.IsEncryptData(o.ToAddress))
                            //|| ((XiaoHongShuV2PlatformService.IsDesensitiseData(o.ToName, KuaiShouEncryptType.Name)
                            //|| XiaoHongShuV2PlatformService.IsDesensitiseData(o.ToPhone, KuaiShouEncryptType.Mobile)
                            //|| XiaoHongShuV2PlatformService.IsDesensitiseData(o.ToAddress, KuaiShouEncryptType.Address)) && isMask == false)))
                            //    needDecryptOrders.Add(o);
                            //else 
                            //    noNeedDecryptOrders.Add(o);
                        });
                        if (needDecryptOrders.Any())
                        {
                            //无需再查2022.11.23
                            ////小红书的加密信息另外存储的，需另外查询 与快手存放在同一表
                            //var kSEncryptedReceiverService = new KuaiShouEncryptedReceiverInfoService();
                            //kSEncryptedReceiverService.SetOrderEncryptedReceiverInfoByLogicOrder(needDecryptOrders);
                            DecryptOrders(needDecryptOrders, fromType, shops: allShops, isMask: isMask, field: field, waybill_type: waybill_type);

                        }
                        ////历史未加密数据加密处理
                        if (isMask && noMaskHistoryOrders.Any())
                            HistoryOrderMask(noMaskHistoryOrders, true);
                    }
                    catch (Exception ex)
                    {
                        msg = DecryptErrMethod(throwEx, _shopService, shop, gorders, ex);
                        //Utility.Log.WriteError($"{shop.PlatformType}：店铺【{shop.NickName}】解密失败：{ex}");
                        ////授权过期处理
                        //if (!throwEx)
                        //    DecryptOrderWithXing(gorders);
                        //else
                        //    throw ex;
                    }
                    #endregion
                }
                else if (pt == PlatformType.TuanHaoHuo.ToString())
                {
                    #region 团好货解密
                    try
                    {
                        //区分加密的订单
                        gorders.ForEach(o =>
                        {
                            if (string.IsNullOrWhiteSpace(o.ExtField1) == false && isMask == false) //有加密信息//并且是脱敏信息且需要解密成明文
                                needDecryptOrders.Add(o);
                            else
                                noNeedDecryptOrders.Add(o);
                        });
                        if (needDecryptOrders.Any())
                        {
                            //无需再查2022.11.23
                            ////团好货的加密信息另外存储的，需另外查询
                            //var kSEncryptedReceiverService = new KuaiShouEncryptedReceiverInfoService();
                            //kSEncryptedReceiverService.SetOrderEncryptedReceiverInfoByLogicOrder(needDecryptOrders);
                            //解密
                            DecryptOrders(needDecryptOrders, fromType, shops: allShops, isMask: isMask, field: field, waybill_type: waybill_type);
                        }
                        //历史未加密数据加密处理
                        if (isMask && noNeedDecryptOrders.Any())
                            HistoryOrderMask(noNeedDecryptOrders, true);
                    }
                    catch (Exception ex)
                    {
                        msg = DecryptErrMethod(throwEx, _shopService, shop, gorders, ex);
                        //Utility.Log.WriteError($"{shop.PlatformType}：店铺【{shop.NickName}】解密失败：{ex}");
                        ////授权过期处理
                        //if (!throwEx)
                        //    DecryptOrderWithXing(gorders);
                        //else
                        //    throw ex;
                    }
                    #endregion
                }
                else if (pt == PlatformType.WeiDian.ToString())
                {
                    #region 微店解密

                    try
                    {
                        gorders.ForEach(o =>
                        {
                            var needDecryptPhone = o.ToPhone.IsNotNullOrEmpty() && o.ToPhone.Length > 11 && o.ToPhone.EndsWith("=");
                            if (needDecryptPhone || o.ToName.EndsWith("=") || o.ToAddress.EndsWith("="))//??待校验大部分数据，官方无判断接口，看数据目前是所有都以=结尾(且加密手机号超过11位)
                                needDecryptOrders.Add(o);
                            else
                                noNeedDecryptOrders.Add(o);
                        });
                        //不需要打码,需要解密
                        if (needDecryptOrders.Any())
                            DecryptOrders(needDecryptOrders, fromType, shops: allShops, isMask: isMask, field: field, waybill_type: waybill_type);
                    }
                    catch (Exception ex)
                    {
                        msg = DecryptErrMethod(throwEx, _shopService, shop, gorders, ex);
                    }
                    #endregion
                }
                else if (pt == PlatformType.WxVideo.ToString())
                {
                    #region 视频号解密
                    try
                    {
                        gorders.ForEach(o =>
                        {
                            if (o.ToPhone?.Contains("*") == true && o.DecryptField.IsNotNullOrEmpty())
                                needDecryptOrders.Add(o);
                            else
                                noNeedDecryptOrders.Add(o);
                        });
                        //不需要打码,需要解密
                        if (needDecryptOrders.Any())
                            DecryptOrders(needDecryptOrders, fromType, shops: allShops, isMask: isMask, field: field, waybill_type: waybill_type);
                    }
                    catch (Exception ex)
                    {
                        msg = DecryptErrMethod(throwEx, _shopService, shop, gorders, ex);
                    }
                    #endregion
                }
                else if (pt == PlatformType.Virtual.ToString())
                {
                    #region 线下单解密
                    try
                    {
                        gorders.ForEach(o =>
                        {
                            if (o.ToPhone?.Contains("*") == true)
                                needDecryptOrders.Add(o);
                            else
                                noNeedDecryptOrders.Add(o);
                        });
                        //不需要打码,需要解密
                        if (needDecryptOrders.Any())
                            DecryptOrders(needDecryptOrders, fromType, shops: allShops, isMask: isMask, field: field, waybill_type: waybill_type);
                    }
                    catch (Exception ex)
                    {
                        msg = DecryptErrMethod(throwEx, _shopService, shop, gorders, ex);
                    }
                    #endregion
                }
                //同步查询记录到平台
                if (needSyncLog)
                {
                    //if (pt == PlatformType.Taobao.ToString())
                    //    ych_sdk.YchRequestLogger.Order(shop.Id.ToString(), "订单查询", pids);
                    //else if (pt == PlatformType.Jingdong.ToString())
                    //    jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
                    PlatformLogHelper.OrderLog(gorders.ToList(), pt, isMask);
                }
            });
            return msg;
        }

        private static string DecryptErrMethod(bool throwEx, ShopService _shopService, Shop shop, List<LogicOrder> gorders, Exception ex)
        {
            // 订单是否代发，代发订单获取商家名称
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var curBindShopDic = new FxUserShopService().GetBindShops(fxUserId).GroupBy(x => x.Id).ToDictionary(x => x.Key, x => x.First());
            var pathFlowCodes = gorders.Select(x => x.PathFlowCode).Where(x => x.IsNotNullOrEmpty()).Distinct().ToList();
            var agentDic = new Dictionary<string, UserFx>();
            if (pathFlowCodes.Any())
                agentDic = new UserFxRepository().GetAgentByPathFlowCodes(pathFlowCodes);
            var exMsg = ex?.Message ?? "";
            var shopId = gorders?.FirstOrDefault()?.ShopId ?? 0;
            if (shopId > 0 && curBindShopDic.ContainsKey(shopId) == false)
            {
                // 代发订单解密失败不显示店铺名称，改为商家账号
                var pathFlowCode = gorders?.FirstOrDefault()?.PathFlowCode ?? "";
                UserFx agent = null;
                if (agentDic.TryGetValue(pathFlowCode, out agent))
                {
                    exMsg = exMsg.Replace(shop.NickName, agent.Mobile).Replace(shop.ShopName, agent.Mobile).Replace("店铺", "商家");
                }
            }

            string msg = $"{shop.PlatformType}：店铺【{shop.NickName}】解密失败：{ex}";
            Utility.Log.WriteError($"{shop.PlatformType}：店铺【{shop.NickName}】解密失败：{ex}");
            var errType = (msg.Contains("授权过期") || msg.Contains("授权已过期") || msg.Contains("授权已被关闭") ? DecryptErrType.AuthErr :
                          (msg.Contains("余额不足") || msg.Contains("无解密额度") || msg.Contains("额度不足") || msg.Contains("额度已用完") || msg.Contains("额度不够") || msg.Contains("解密额度上限") || msg.Contains("解密上限") ? DecryptErrType.LimitErr :
                          (msg.Contains("平台接口：") || msg.Contains("解密接口") || msg.Contains("接口")) ? DecryptErrType.ApiErr : DecryptErrType.ServerErr)).ToInt();

            if (errType != DecryptErrType.AuthErr.ToInt() && errType != DecryptErrType.LimitErr.ToInt())
            {
                _shopService.WriteSqlToLog(msg, null, $"DecryptErr-{shop.Id}.txt", "DecryptErr");
            }

            //异常处理
            var decryptErrMsg = errType == DecryptErrType.ApiErr.ToInt() ? (ex.InnerException == null ? ex.Message : ex.InnerException.Message) : "";
            DecryptOrderWithXing(gorders, errType, decryptErrMsg);
            if (throwEx)
            {
                throw new Exception(exMsg);
            }
            return msg;
        }

        public static void HistoryOrderMask(List<LogicOrder> orders, bool encryptWangwang = false)
        {
            //历史未加密数据加密处理
            orders?.ForEach(t =>
            {
                if (t.PlatformType == PlatformType.Pinduoduo.ToString() ||
                    t.PlatformType == PlatformType.TouTiao.ToString() ||
                    t.PlatformType == PlatformType.TouTiaoSaleShop.ToString() ||
                    t.PlatformType == PlatformType.WeiDian.ToString())
                {
                    t.ToPhone = t.ToPhone.ToPddEncryptPhone();
                    t.ToName = t.ToName.ToEncryptName();
                    t.BuyerWangWang = t.ToName;
                    if (encryptWangwang)
                        t.BuyerWangWang = t.BuyerWangWang.ToEncryptName();
                    t.ToFullAddress = t.ToFullAddress.TrimEnd(t.ToAddress);
                    t.ToAddress = t.ToAddress.ToPddEncryptAddress();
                    t.ToFullAddress = t.ToFullAddress + t.ToAddress;
                }
                else if (t.PlatformType == PlatformType.Taobao.ToString() || t.PlatformType == PlatformType.AlibabaC2M.ToString() || t.PlatformType == PlatformType.Alibaba.ToString() || t.PlatformType == PlatformType.TaobaoMaiCaiV2.ToString())
                {
                    if (t.PlatformType == PlatformType.Alibaba.ToString())
                    {
                        t.ToPhone = t.ToPhone.ToEncrytPhone();
                        t.ToName = t.ToName.ToEncryptName();
                    }
                    else
                    {
                        t.ToPhone = t.ToPhone.ToTaobaoEncrytPhone();
                        if (encryptWangwang)
                            t.BuyerWangWang = t.BuyerWangWang.ToTaobaoEncryptName();
                        t.ToName = t.ToName.ToTaobaoEncryptName();
                    }

                    t.ToFullAddress = t.ToFullAddress.TrimEnd(t.ToAddress);
                    t.ToAddress = t.ToAddress.ToTaoBaoEncryptAddress();
                    t.ToFullAddress = t.ToFullAddress + t.ToAddress;
                }
                else if (t.PlatformType == PlatformType.YouZan.ToString() || t.PlatformType == PlatformType.Suning.ToString()
                || t.PlatformType == PlatformType.TuanHaoHuo.ToString() || t.PlatformType == PlatformType.XiaoHongShu.ToString())
                {
                    t.ToPhone = t.ToPhone.ToTaobaoEncrytPhone();
                    if (encryptWangwang)
                        t.BuyerWangWang = t.BuyerWangWang.ToTaobaoEncryptName();
                    t.ToName = t.ToName.ToTaobaoEncryptName();
                    t.ToFullAddress = t.ToFullAddress.TrimEnd(t.ToAddress);
                    t.ToAddress = t.ToAddress.ToTaoBaoEncryptAddress();
                    t.ToFullAddress = t.ToFullAddress + t.ToAddress;
                }
                else if (t.PlatformType == PlatformType.KuaiShou.ToString())
                {
                    t.ToPhone = KuaiShouPlatformService.PlainTextDesensitise(t.ToPhone, KuaiShouEncryptType.Mobile);
                    if (encryptWangwang)
                        t.BuyerWangWang = KuaiShouPlatformService.PlainTextDesensitise(t.BuyerWangWang, KuaiShouEncryptType.Name);
                    t.ToName = KuaiShouPlatformService.PlainTextDesensitise(t.ToName, KuaiShouEncryptType.Name);

                    //替换密文详细地址，再追加解密后详细地址
                    t.ToFullAddress = t.ToFullAddress.TrimEnd(t.ToAddress);
                    t.ToAddress = KuaiShouPlatformService.PlainTextDesensitise(t.ToAddress, KuaiShouEncryptType.Address);
                    t.ToFullAddress = t.ToFullAddress + t.ToAddress;
                }
                else if (t.PlatformType == PlatformType.Jingdong.ToString()) {
                    t.ToPhone = t.ToPhone.ToTaobaoEncrytPhone();
                    if (encryptWangwang)
                        t.BuyerWangWang = t.BuyerWangWang.ToTaobaoEncryptName();
                    t.ToName = t.ToName.ToTaobaoEncryptName();
                    //京东集运订单地址不打码。地址最后面有防合单码，需要展示
                    if (t.OrderTags.Where(x => x.Tag == OrderTag.consolidateInfo.ToString()) == null || !t.OrderTags.Where(x => x.Tag == OrderTag.consolidateInfo.ToString()).Any())
                    {
                        t.ToFullAddress = t.ToFullAddress.TrimEnd(t.ToAddress);
                        t.ToAddress = t.ToAddress.ToTaoBaoEncryptAddress();
                        t.ToFullAddress = t.ToFullAddress + t.ToAddress;
                    }
                }
            });
        }

        /// <summary>
        /// 校验解密结果，部分字段失败抛异常
        /// </summary>
        /// <param name="waitPtDecryptOrders"></param>
        /// <param name="platformType"></param>
        /// <param name="field"></param>
        public static void CheckEncryptResult(List<Order> waitPtDecryptOrders, string platformType, string field = "")
        {
            var allEncryptSuccess = true;
            if (platformType == PlatformType.TouTiao.ToString() || platformType == PlatformType.TouTiaoSaleShop.ToString())
            {
                if (string.IsNullOrEmpty(field))
                {
                    waitPtDecryptOrders.ForEach(order =>
                    {
                        if (order.ToName != null && !string.IsNullOrEmpty(order.ToName) && order.ToName.IsDyEncryptData())
                            allEncryptSuccess = false;
                        if (order.ToMobile != null && !string.IsNullOrEmpty(order.ToMobile) && order.ToMobile.IsDyEncryptData())
                            allEncryptSuccess = false;
                        if (order.ToAddress != null && !string.IsNullOrEmpty(order.ToAddress) && order.ToAddress.IsDyEncryptData())
                            allEncryptSuccess = false;

                        if (!allEncryptSuccess)
                            throw new LogicException($"抖音解密失败：{order.PlatformOrderId}");
                    });
                }
                else
                {
                    waitPtDecryptOrders.ForEach(order =>
                    {
                        if (field.ToLower() == "toname" && !string.IsNullOrEmpty(order.ToName) && order.ToName.IsDyEncryptData())
                            allEncryptSuccess = false;
                        if (field.ToLower() == "tomobile" && !string.IsNullOrEmpty(order.ToMobile) && order.ToMobile.IsDyEncryptData())
                            allEncryptSuccess = false;
                        if (field.ToLower() == "toaddress" && !string.IsNullOrEmpty(order.ToAddress) && order.ToAddress.IsDyEncryptData())
                            allEncryptSuccess = false;

                        if (!allEncryptSuccess)
                            throw new LogicException($"抖音解密失败：{order.PlatformOrderId}");
                    });
                }
            }
            else if (platformType == PlatformType.Pinduoduo.ToString())
            {
                if (string.IsNullOrEmpty(field))
                {
                    waitPtDecryptOrders.ForEach(order =>
                    {
                        if (order.ToName != null && !string.IsNullOrEmpty(order.ToName) && PinduoduoPlatformService.IsEncryptData(order.ToName))
                            allEncryptSuccess = false;
                        if (order.ToMobile != null && !string.IsNullOrEmpty(order.ToMobile) && PinduoduoPlatformService.IsEncryptData(order.ToMobile))
                            allEncryptSuccess = false;
                        if (order.ToAddress != null && !string.IsNullOrEmpty(order.ToAddress) && PinduoduoPlatformService.IsEncryptData(order.ToAddress))
                            allEncryptSuccess = false;

                        if (!allEncryptSuccess)
                            throw new LogicException($"拼多多解密失败：{order.PlatformOrderId}");
                    });
                }
                else
                {
                    waitPtDecryptOrders.ForEach(order =>
                    {
                        if (field.ToLower() == "toname" && !string.IsNullOrEmpty(order.ToName) && PinduoduoPlatformService.IsEncryptData(order.ToName))
                            allEncryptSuccess = false;
                        if (field.ToLower() == "tomobile" && !string.IsNullOrEmpty(order.ToMobile) && PinduoduoPlatformService.IsEncryptData(order.ToMobile))
                            allEncryptSuccess = false;
                        if (field.ToLower() == "toaddress" && !string.IsNullOrEmpty(order.ToAddress) && PinduoduoPlatformService.IsEncryptData(order.ToAddress))
                            allEncryptSuccess = false;

                        if (!allEncryptSuccess)
                            throw new LogicException($"拼多多解密失败：{order.PlatformOrderId}");
                    });
                }
            }
        }

        public static void ConvertDecryptOrderToLogicOrder(List<LogicOrder> lorders, List<Order> orders)
        {
            var oDic = new Dictionary<string, Order>();
            orders.ForEach(o =>
            {
                Order order = null;
                if (!oDic.TryGetValue(o.PlatformOrderId, out order))
                    oDic.Add(o.PlatformOrderId, o);
            });
            lorders.ForEach(o =>
            {
                Order order = null;
                if (oDic.TryGetValue(o.PlatformOrderId, out order) && order != null)
                {
                    o.ToName = order.ToName;
                    o.ToPhone = order.ToMobile.IsNullOrEmpty() ? order.ToPhone : order.ToMobile;
                    o.ToGPhone = order.ToPhone;
                    o.ToProvince = order.ToProvince;
                    o.ToCity = order.ToCity;
                    o.ToCounty = order.ToCounty;
                    o.ToTown = order.ToTown;
                    o.ToAddress = order.ToAddress;
                    o.ToFullAddress = order.ToFullAddress;
                    o.BuyerWangWang = order.BuyerWangWang;
                    o.IsDecrypted = order.IsDecrypted;
                    o.DecryptErrorType = order.DecryptErrorType;
                    o.DecryptErrorMsg = order.DecryptErrorMsg;
                }
                else
                {
                    // 使用非主单去C的子单解密，遍历OrderItem获取对应订单解密信息
                    o.LogicOrderItems.ForEach(x =>
                    {
                        if (oDic.TryGetValue(x.PlatformOrderId, out order) && order != null)
                        {
                            o.ToName = order.ToName;
                            o.ToPhone = order.ToMobile.IsNullOrEmpty() ? order.ToPhone : order.ToMobile;
                            o.ToGPhone = order.ToPhone;
                            o.ToProvince = order.ToProvince;
                            o.ToCity = order.ToCity;
                            o.ToCounty = order.ToCounty;
                            o.ToTown = order.ToTown;
                            o.ToAddress = order.ToAddress;
                            o.ToFullAddress = order.ToFullAddress;
                            o.BuyerWangWang = order.BuyerWangWang;
                            o.IsDecrypted = order.IsDecrypted;
                            o.DecryptErrorType = order.DecryptErrorType;
                            o.DecryptErrorMsg = order.DecryptErrorMsg;
                            return;
                        }
                    });
                }
            });
        }

        public static List<Order> ConvertLogicOrderToDecryptOrder(List<LogicOrder> lorders)
        {
            if (lorders == null || lorders.Any() == false)
                return new List<Order>();

            var orders = lorders.Select(x =>
            {
                var order = new Order { PlatformOrderId = x.PlatformOrderId, ShopId = x.ShopId, ToName = x.ToName, ToMobile = x.ToPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToCounty, ToTown = x.ToTown, ToAddress = x.ToAddress, ToFullAddress = x.ToFullAddress };
                if (x.PlatformType == PlatformType.Taobao.ToString() || x.PlatformType == PlatformType.AlibabaC2M.ToString() || x.PlatformType == PlatformType.Alibaba.ToString() | x.PlatformType == PlatformType.TaobaoMaiCaiV2.ToString())
                    order.ExtField3 = x.DecryptField;
                else if (x.PlatformType == PlatformType.Jingdong.ToString())
                    order.ExtField5 = x.DecryptField;
                else if (x.PlatformType == PlatformType.Suning.ToString())
                    order.ExtField1 = x.DecryptField;
                else if (x.PlatformType == PlatformType.KuaiShou.ToString() || x.PlatformType == PlatformType.TuanHaoHuo.ToString() || x.PlatformType == PlatformType.XiaoHongShu.ToString())
                {
                    order.ExtField1 = x.ExtField1;
                    order.ExtField2 = x.ExtField2;
                    order.EncryptedReceiverInfo = x.EncryptedReceiverInfo;
                }
                order.UserId = x.FxUserId;
                order.IsDecrypted = x.IsDecrypted;
                return order;
            }).ToList();
            return orders;
        }

        public static void EncryptWaybillCodes(List<WaybillCode> waybillCodeLst, bool isMask = true, string field = "", string waybill_type = "", bool encryptSender = false, bool needSyncLog = false, bool needPlatformDecrypt = true)
        {
            if (waybillCodeLst == null || waybillCodeLst.Any() == false)
                return;

            var fxUserId = SiteContext.Current.CurrentFxUserId;

            //收件人信息加密
            //var orders = waybillCodeLst?.Select(x => new LogicOrder { PlatformOrderId = x.CustomerOrderId, LogicOrderId = x.OrderId, MergeredType = x.OrderId.ToString2().StartsWith("C") ? 3 : 0, ChildOrderId = x.OrderId.ToString2().StartsWith("C") ? x.OrderIdJoin : "", ShopId = x.SourceShopId, ToName = x.Reciver, ToPhone = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ToAddress,LastWaybillCode = x.ExpressWayBillCode }).ToList();

            var needQueryOrderIds = new List<string>();
            var needQueryShopIds = new List<int>();
            var fxUserPlatformOrderIdModels = new List<FxUserPlatformOrderIdModel>();
            var orders = new List<LogicOrder>();
            waybillCodeLst?.ForEach(x =>
            {
                var o = new LogicOrder
                {
                    PlatformOrderId = x.CustomerOrderId,
                    LogicOrderId = x.OrderId,
                    MergeredType = x.OrderId.ToString2().StartsWith("C") ? 3 : 0,
                    ChildOrderId = x.OrderId.ToString2().StartsWith("C") ? x.OrderIdJoin : "",
                    ShopId = x.SourceShopId,
                    ToName = x.Reciver,
                    ToPhone = x.ReciverPhone,
                    ToProvince = x.ToProvince,
                    ToCity = x.ToCity,
                    ToCounty = x.ToDistrict,
                    ToAddress = x.ToAddress,
                    LastWaybillCode = x.ExpressWayBillCode
                };
                orders.Add(o);

                //收件人为空或只有*号，取原单数据
                if (string.IsNullOrEmpty(x.Reciver) || x.Reciver.Replace("*", "") == "")
                {
                    needQueryOrderIds.Add(x.CustomerOrderId);
                    needQueryShopIds.Add(x.SourceShopId);
                    fxUserPlatformOrderIdModels.Add(new FxUserPlatformOrderIdModel { FxUserId = x.UserId, ShopId = x.SourceShopId, PlatformOrderId = x.CustomerOrderId });
                }

            });

            //查询原始单数据
            if (needQueryOrderIds.Any() && needQueryShopIds.Any())
            {
                needQueryOrderIds = needQueryOrderIds.Distinct().ToList();
                needQueryShopIds = needQueryShopIds.Distinct().ToList();
                var queryFields = new List<string> { "o.Id", "o.PlatformOrderId", "o.ToName", "o.ToPhone", "o.ToMobile", "o.ToAddress", "o.ShopId", "oi.Id" };
                var queryResult = new OrderService().GetOrders(needQueryOrderIds, needQueryShopIds, false, queryFields, queryReceiver: new QueryReceiverModel { IsOnlyGetMask = false, IsFromApi = true });
                //var queryResult = new SyncDataInterfaceService(fxUserId).GetOrders(fxUserPlatformOrderIdModels, needQueryOrderIds, needQueryShopIds, false, queryFields, queryReceiver: new QueryReceiverModel { IsOnlyGetMask = false, IsFromApi = true });

                queryResult.ForEach(o =>
                {
                    var existList = orders.Where(a => a.PlatformOrderId == o.PlatformOrderId)?.ToList();
                    existList?.ForEach(exist =>
                    {
                        exist.ToName = o.ToName;
                        exist.ToPhone = string.IsNullOrEmpty(o.ToMobile) ? o.ToPhone : o.ToMobile;
                        exist.ToAddress = o.ToAddress;
                    });
                });
            }

            //Utility.Log.WriteWarning($"解密WaybillCode转LogicOrder：{orders.ToJson()}");

            EncryptOrders(orders, EncryptFromType.WaybillCode, isMask: isMask, field: field, waybill_type: waybill_type, encryptSender: encryptSender, needSyncLog: needSyncLog, needPlatformDecrypt: needPlatformDecrypt);

            var oDic = orders?.Where(x => x.LogicOrderId.IsNotNullOrEmpty() && x.LastWaybillCode.IsNotNullOrEmpty()).GroupBy(x => x.LogicOrderId + "_" + x.LastWaybillCode).ToDictionary(x => x.Key, x => x.FirstOrDefault() ?? new LogicOrder());

            //Utility.Log.WriteWarning($"解密后LogicOrder转WaybillCode前：{oDic.ToJson()}");
            waybillCodeLst?.ForEach(w =>
            {
                LogicOrder o = null;
                var key = w.OrderId + "_" + w.ExpressWayBillCode;
                if (oDic.TryGetValue(key, out o))
                {
                    //w.BuyerMemberName = o.BuyerMemberName;
                    //w.BuyerMemberId = o.BuyerMemberId;
                    //w.CustomerOrderId = o.PlatformOrderId;
                    w.ReciverPhone = o.ToPhone;
                    w.Reciver = o.ToName;
                    w.ToAddress = o.ToTown + o.ToAddress;
                    w.ToDistrict = o.ToCounty;
                    if (w.PlatformType.IsNullOrEmpty())
                        w.PlatformType = o.PlatformType;
                    //w.Sender = o.SenderName;
                }
                else if (o != null && encryptPlatformTypes.Contains(o.PlatformType.ToString2()) && isMask)
                {
                    w.BuyerMemberName = "***";
                    w.ReciverPhone = "***";
                    w.Reciver = "***";
                    w.ToAddress = "***";
                }
                else
                {
                    Utility.Log.Debug($"订单【{w.OrderId}】未找到解密信息");
                }
            });

            //回更到底单数据
            if (needQueryOrderIds.Any() && needQueryShopIds.Any())
            {
                var needUpdateList = new List<WaybillCode>();
                needQueryOrderIds.ForEach(oid =>
                {
                    var existList = waybillCodeLst.Where(a => a.CustomerOrderId == oid)?.ToList();
                    existList.ForEach(exist =>
                    {
                        if (!string.IsNullOrEmpty(exist.Reciver) && exist.Reciver != "***")
                        {
                            needUpdateList.Add(exist);
                        }
                    });
                });
                if (needUpdateList.Any())
                    new WaybillCodeService().UpdateMaskReceiver(needUpdateList);
            }

            //Utility.Log.WriteWarning($"解密后LogicOrder转WaybillCode后：{waybillCodeLst.ToJson()}");

        }

        public static void EncryptPrintHistory(List<PrintHistory> dataList, bool isMask = true, string field = "", string waybill_type = "", bool encryptSender = false, bool needSyncLog = false, bool needPlatformDecrypt = true)
        {
            if (dataList == null || dataList.Any() == false)
                return;

            ////收件人信息加密
            //var orders = dataList?.Select(x =>
            //{
            //    var pcc = x.ToProvince.ToString2() + x.ToCity.ToString2() + x.ToDistrict.ToString2();
            //    return new LogicOrder { LogicOrderId = x.PlatformOrderId, PlatformOrderId = x.CustomerOrderId, MergeredType = x.PlatformOrderId.ToString2().StartsWith("C") ? 3 : 0, ChildOrderId = x.PlatformOrderId.ToString2().StartsWith("C") ? x.PlatformOrderJoin : "", ShopId = x.ShopId, ToName = x.Reciver, ToPhone = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ReciverAddress.ToString2().TrimStart(pcc), ToFullAddress = x.ReciverAddress,LastWaybillCode = x.ExpressWaybillCode };
            //}).ToList();

            var needQueryOrderIds = new List<string>();
            var orders = new List<LogicOrder>();
            dataList?.ForEach(x =>
            {
                var pcc = x.ToProvince.ToString2() + x.ToCity.ToString2() + x.ToDistrict.ToString2();
                var o = new LogicOrder
                {
                    LogicOrderId = x.PlatformOrderId,
                    PlatformOrderId = x.CustomerOrderId,
                    MergeredType = x.PlatformOrderId.ToString2().StartsWith("C") ? 3 : 0,
                    ChildOrderId = x.PlatformOrderId.ToString2().StartsWith("C") ? x.PlatformOrderJoin : "",
                    ShopId = x.ShopId,
                    ToName = x.Reciver,
                    ToPhone = x.ReciverPhone,
                    ToProvince = x.ToProvince,
                    ToCity = x.ToCity,
                    ToCounty = x.ToDistrict,
                    ToAddress = x.ReciverAddress.ToString2().TrimStart(pcc),
                    ToFullAddress = x.ReciverAddress,
                    LastWaybillCode = x.ExpressWaybillCode,
                    PrintedSerialNumber = x.PrintBatchNumber + "_" + x.ID///PrintedSerialNumber 此代码仅作区分

                };
                orders.Add(o);

                //收件人为空或只有*号，取原单数据
                if (string.IsNullOrEmpty(x.Reciver) || x.Reciver.Replace("*", "") == "")
                {
                    needQueryOrderIds.Add(x.PlatformOrderId);//逻辑单号
                }

            });
            Utility.Log.Debug($"dataList:{dataList.ToJson()}\r\norders:{orders.ToJson()}");
            //查询逻辑单数据
            if (needQueryOrderIds.Any())
            {
                needQueryOrderIds = needQueryOrderIds.Distinct().ToList();
                var queryFields = new List<string> { "o.Id", "o.LogicOrderId", "o.ToName", "o.ToPhone", "o.ToAddress" };
                var queryResult = new LogicOrderService().GetLogicOrders(needQueryOrderIds, queryFields)?.ToList();

                queryResult.ForEach(o =>
                {
                    var existList = orders.Where(a => a.LogicOrderId == o.LogicOrderId)?.ToList();
                    existList?.ForEach(exist =>
                    {
                        exist.ToName = o.ToName;
                        exist.ToPhone = o.ToPhone;
                        exist.ToAddress = o.ToAddress;
                    });
                });
            }

            //Utility.Log.Debug($"打印记录解密前：{orders.Select(x => new { x.PlatformOrderId, x.LogicOrderId, x.MergeredType, x.MergeredOrderId, x.ChildOrderId, x.ShopId, x.ToName, x.ToPhone, x.ToProvince, x.ToCity, x.ToCounty, x.ToAddress, x.ToFullAddress }).ToJson()}");

            EncryptOrders(orders, EncryptFromType.PrintHistory, isMask: isMask, field: field, waybill_type: waybill_type, encryptSender: encryptSender, needSyncLog: needSyncLog, needPlatformDecrypt: needPlatformDecrypt);
            var oDic = orders?.Where(x => x.LogicOrderId.IsNotNullOrEmpty() && x.LastWaybillCode.IsNotNullOrEmpty())
                .GroupBy(x => x.LogicOrderId + "_" + x.LastWaybillCode + "_" + x.PrintedSerialNumber)//20240620 多个打印记录LastWaybillCode相同 GroupBy增加PrintedSerialNumber进行分组取 PrintHistory.PrintBatch
                .ToDictionary(x => x.Key, x => x.FirstOrDefault() ?? new LogicOrder());
            //Utility.Log.Debug($"打印记录解密后：{oDic.ToJson()}");


            dataList?.ForEach(w =>
            {
                LogicOrder o = null;
                var key = w.PlatformOrderId + "_" + w.ExpressWaybillCode + "_" + w.PrintBatchNumber;
                if (oDic.TryGetValue(key, out o))
                {
                    //w.BuyerMemberName = o.BuyerMemberName;
                    //w.BuyerMemberId = o.BuyerMemberId;
                    w.ReciverPhone = o.ToPhone;
                    w.Reciver = o.ToName;
                    w.ReciverAddress = o.ToFullAddress;
                    w.ToDistrict = o.ToCounty;
                    if (w.PlatformType.IsNullOrEmpty())
                        w.PlatformType = o.PlatformType;
                    //w.Sender = o.SenderName;
                }
                else if (o != null && encryptPlatformTypes.Contains(o.PlatformType.ToString2()) && isMask)
                {
                    w.BuyerMemberName = "***";
                    w.ReciverPhone = "***";
                    w.Reciver = "***";
                    w.ReciverAddress = "***";
                }
                else
                {
                    Utility.Log.Debug($"订单【{w.PlatformOrderId}】未找到解密信息");
                }
            });

            //回更到打印记录
            if (needQueryOrderIds.Any())
            {
                var needUpdateList = new List<PrintHistory>();
                needQueryOrderIds.ForEach(oid =>
                {
                    var existList = dataList.Where(a => a.PlatformOrderId == oid)?.ToList();
                    existList?.ForEach(exist =>
                    {
                        if (!string.IsNullOrEmpty(exist.Reciver) && exist.Reciver != "***")
                        {
                            needUpdateList.Add(exist);
                        }
                    });
                });
                if (needUpdateList.Any())
                    new PrintHistoryService(SiteContext.Current.CurrentFxUserId).UpdateMaskReceiver(needUpdateList);
            }
        }

        public static void EncryptSendHistory(List<SendHistory> dataList, bool isMask = true, string field = "", string waybill_type = "", bool encryptSender = false, bool needSyncLog = false, bool needPlatformDecrypt = true)
        {
            if (dataList == null || dataList.Any() == false)
                return;

            ////收件人信息加密
            //var orders = dataList?.Select(x =>
            //{
            //    var pcc = x.ToProvince.ToString2() + x.ToCity.ToString2() + x.ToDistrict.ToString2();
            //    return new LogicOrder { LogicOrderId = x.OrderId, ShopId = x.ShopId, MergeredType = x.OrderId.StartsWith("C") ? 3 : 0, ChildOrderId = x.OrderJoin.StartsWith("C") ? x.OrderJoin : "", ToName = x.Reciver, ToPhone = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ReciverAddress.ToString2().TrimStart(pcc), ToFullAddress = x.ReciverAddress, LastWaybillCode = x.LogistiscBillNo};
            //}).ToList();


            var needQueryOrderIds = new List<string>();
            var orders = new List<LogicOrder>();
            dataList?.ForEach(x =>
            {
                var pcc = x.ToProvince.ToString2() + x.ToCity.ToString2() + x.ToDistrict.ToString2();
                var o = new LogicOrder
                {
                    LogicOrderId = x.OrderId,
                    ShopId = x.ShopId,
                    MergeredType = x.OrderId.StartsWith("C") ? 3 : 0,
                    ChildOrderId = x.OrderJoin.StartsWith("C") ? x.OrderJoin : "",
                    ToName = x.Reciver,
                    ToPhone = x.ReciverPhone,
                    ToProvince = x.ToProvince,
                    ToCity = x.ToCity,
                    ToCounty = x.ToDistrict,
                    ToAddress = x.ReciverAddress.ToString2().TrimStart(pcc),
                    ToFullAddress = x.ReciverAddress,
                    LastWaybillCode = x.LogistiscBillNo
                };
                orders.Add(o);

                //收件人为空或只有*号，取逻辑单数据
                if (string.IsNullOrEmpty(x.Reciver) || x.Reciver.Replace("*", "") == "")
                {
                    needQueryOrderIds.Add(x.OrderId);//逻辑单订单编号
                }

            });

            //查询逻辑单数据
            if (needQueryOrderIds.Any())
            {
                needQueryOrderIds = needQueryOrderIds.Distinct().ToList();
                var queryFields = new List<string> { "o.Id", "o.LogicOrderId", "o.ToName", "o.ToPhone", "o.ToAddress" };
                var queryResult = new LogicOrderService().GetLogicOrders(needQueryOrderIds, queryFields)?.ToList();

                queryResult?.ForEach(o =>
                {
                    var existList = orders.Where(a => a.LogicOrderId == o.LogicOrderId)?.ToList();
                    existList?.ForEach(exist =>
                    {
                        exist.ToName = o.ToName;
                        exist.ToPhone = o.ToPhone;
                        exist.ToAddress = o.ToAddress;
                    });
                });
            }

            EncryptOrders(orders, EncryptFromType.SendHistory, isMask: isMask, field: field, waybill_type: waybill_type, encryptSender: encryptSender, needSyncLog: needSyncLog, needPlatformDecrypt: needPlatformDecrypt);
            var oDic = orders?.Where(x => x.LogicOrderId.IsNotNullOrEmpty() && x.LastWaybillCode.IsNotNullOrEmpty()).GroupBy(x => x.LogicOrderId + "_" + x.LastWaybillCode).ToDictionary(x => x.Key, x => x.FirstOrDefault() ?? new LogicOrder());
            dataList?.ForEach(w =>
            {
                LogicOrder o = null;
                var key = w.OrderId + "_" + w.LogistiscBillNo;
                if (oDic.TryGetValue(key, out o))
                {
                    //w.BuyerMemberName = o.BuyerMemberName;
                    //w.BuyerMemberId = o.BuyerMemberId;
                    w.ReciverPhone = o.ToPhone;
                    w.Reciver = o.ToName;
                    w.ReciverAddress = o.ToTown + o.ToAddress;
                    w.ToDistrict = o.ToCounty;
                    if (w.PlatformType.IsNullOrEmpty())
                        w.PlatformType = o.PlatformType;
                    //w.Sender = o.SenderName;
                }
                else if (o != null && encryptPlatformTypes.Contains(o.PlatformType.ToString2()) && isMask)
                {
                    w.BuyerMemberName = "***";
                    w.ReciverPhone = "***";
                    w.Reciver = "***";
                    w.ReciverAddress = "***";
                }
                else
                {
                    Utility.Log.Debug($"订单【{w.OrderId}】未找到解密信息");
                }
            });


            //回更到发货记录
            if (needQueryOrderIds.Any())
            {
                var needUpdateList = new List<SendHistory>();
                needQueryOrderIds.ForEach(oid =>
                {
                    var existList = dataList.Where(a => a.OrderId == oid)?.ToList();
                    existList?.ForEach(exist =>
                    {
                        if (!string.IsNullOrEmpty(exist.Reciver) && exist.Reciver != "***")
                        {
                            needUpdateList.Add(exist);
                        }
                    });
                });
                if (needUpdateList.Any())
                    new SendHistoryService().UpdateMaskReceiver(needUpdateList);
            }
        }

        /// <summary>
        /// 单号分享收件人加密
        /// </summary>
        /// <param name="dataList"></param>
        /// <param name="isMask"></param>
        /// <param name="field"></param>
        /// <param name="waybill_type"></param>
        /// <param name="encryptSender"></param>
        /// <param name="needSyncLog"></param>
        /// <param name="needPlatformDecrypt"></param>
        public static void EncryptBranchShare(List<WaybillCodeSimpleModel> dataList, bool isMask = true, string field = "", string waybill_type = "", bool encryptSender = false, bool needSyncLog = false, bool needPlatformDecrypt = true)
        {
            if (dataList == null || dataList.Any() == false)
                return;

            //收件人信息加密
            var orders = dataList.Select(x => new LogicOrder { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver, ToPhone = x.ReciverPhone }).ToList();
            EncryptOrders(orders, EncryptFromType.BranchShare, isMask: isMask, field: field, waybill_type: waybill_type, encryptSender: encryptSender, needSyncLog: needSyncLog, needPlatformDecrypt: needPlatformDecrypt);
            var oDic = orders?.Where(x => x.PlatformOrderId.IsNotNullOrEmpty()).GroupBy(x => x.PlatformOrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault() ?? new LogicOrder());
            dataList?.ForEach(w =>
            {
                LogicOrder o = null;
                if (oDic.TryGetValue(w.OrderId, out o))
                {
                    w.Reciver = o.ToName;
                    w.ReciverPhone = o.ToPhone;
                }
                else if (o != null && encryptPlatformTypes.Contains(o.PlatformType.ToString2()) && isMask)
                {
                    w.Reciver = "***";
                    w.ReciverPhone = "***";
                }
                else
                {
                    Utility.Log.Debug($"订单【{w.OrderId}】未找到解密信息");
                }
            });
        }



        /// <summary>
        /// 授权过期，无法通过接口解密，默认显示*
        /// DecryptErrorType：1=>店铺过期，2=>解密额度上限
        /// </summary>
        /// <param name="orders"></param>
        private static void DecryptOrderWithXing(List<LogicOrder> orders, int DecryptErrorType = 0, string decryptErrorMsg = "")
        {
            orders?.ForEach(t =>
            {
                t.ToPhone = "****";
                //t.BuyerMemberName = "***";
                t.BuyerWangWang = "***";
                t.ToName = "***";
                t.ToAddress = "****";
                t.ToFullAddress = t.ToProvince + t.ToCity + t.ToCounty + t.ToTown + t.ToAddress.ToPddEncryptAddress();
                t.DecryptErrorType = DecryptErrorType;
                t.DecryptErrorMsg = decryptErrorMsg;
            });
        }

        /// <summary>
        /// 不需要调平台接口简单解密
        /// </summary>
        /// <param name="orders"></param>
        /// <param name="encryptSender"></param>
        /// <param name="encryptWangwang"></param>
        private static void DecryptOrderNoPlatform(List<LogicOrder> orders, bool encryptSender = false, bool encryptWangwang = false)
        {
            orders.GroupBy(o => o.PlatformType).ToList().ForEach(g =>
            {
                var pt = g.Key;
                g.ToList().ForEach(o =>
                {
                    if (pt == PlatformType.Taobao.ToString())
                    {
                        //o.BuyerMemberName = o.BuyerMemberName.ToTaobaoEncryptName();
                        //o.BuyerMemberId = o.BuyerMemberId.ToTaobaoEncryptName();
                        o.ToName = o.ToName.ToTaobaoEncrytPhone();
                        o.ToPhone = o.ToPhone.ToTaobaoEncrytPhone();
                        o.ToAddress = o.ToAddress.ToTaoBaoEncryptAddress();
                        if (encryptWangwang)
                            o.BuyerWangWang = o.BuyerWangWang.ToTaobaoEncryptName();
                        ////淘宝还需加密发件人信息
                        //if (encryptSender)
                        //    o.SenderName = o.SenderName.ToTaobaoEncryptName();
                    }
                    else if (pt == PlatformType.Pinduoduo.ToString())
                    {
                        //o.BuyerMemberName = o.BuyerMemberName.ToEncryptName();
                        //o.BuyerMemberId = o.BuyerMemberId.ToEncryptName();
                        o.ToName = o.ToName.ToEncryptName();
                        o.ToPhone = o.ToPhone.ToPddEncryptPhone();
                        o.ToAddress = o.ToAddress.ToPddEncryptAddress();
                        if (encryptWangwang)
                            o.BuyerWangWang = o.BuyerWangWang.ToEncryptName();
                    }
                    else
                    {
                        //o.BuyerMemberName = o.BuyerMemberName.ToEncryptName();
                        //o.BuyerMemberId = o.BuyerMemberId.ToEncryptName();
                        o.ToName = o.ToName.ToEncryptName();
                        //o.ToMobile = o.ToMobile.ToPddEncryptPhone();
                        o.ToPhone = o.ToPhone.ToPddEncryptPhone();
                        o.ToAddress = o.ToAddress.ToPddEncryptAddress();
                        if (encryptWangwang)
                            o.BuyerWangWang = o.BuyerWangWang.ToEncryptName();
                    }
                });
            });
        }

        /// <summary>
        /// 尝试解析拼多多加密信息
        /// </summary>
        /// <param name="allOrders"></param>
        /// <param name="isMask">是否打码格式</param>
        /// <param name="field">指定要解析的字段，不填解析所有，支持：ToName,ToMobile,ToAddress</param>
        /// <param name="field">当isMask为false时，waybii_type必填，可选值：面单类型(0-拼多多面单,1-友商平台面单,2-快递公司面单,99-其他)</param>
        public static void TryToDecryptOrders(List<LogicOrder> allOrders, bool isMask = true, string field = "", string waybii_type = "", bool isBranchShare = false, bool throwEx = false)
        {
            var allShopIds = allOrders.Select(x => x.ShopId).Distinct().ToList();
            var allShops = new ShopService().GetShopByIds(allShopIds);
            if (allShops == null || allShops.Any() == false)
                return;

            //更新订单平台
            var shopDic = allShops.GroupBy(x => x.Id).ToDictionary(s => s.Key, s => s.FirstOrDefault());
            var hasPddOrder = false;
            allOrders.ForEach(o =>
            {
                Shop shop = null;
                if (!shopDic.TryGetValue(o.ShopId, out shop))
                    o.PlatformType = shop.PlatformType;
                hasPddOrder = o.PlatformType == PlatformType.Pinduoduo.ToString();
            });

            var existShopIds = allShops.Select(x => x.Id).ToList();
            if (existShopIds == null || existShopIds.Any() == false)
                throw new Exception("店铺数据异常，请重新登录");
            if (isMask == false && string.IsNullOrEmpty(waybii_type) && hasPddOrder)
                throw new LogicException("该面单类型暂无法解密，请更换拼多多面单打印");
            foreach (var shopId in allShopIds)
            {
                if (existShopIds.Contains(shopId) == false)
                    throw new LogicException("您要打印的订单中有些店铺已经被取消关联了，请确认重新关联或者刷新重试。");
                //授权过期了
                var curShop = allShops.FirstOrDefault(x => x.Id == shopId);
                var isAuthExpired = curShop?.IsAuthExpired == true;
                if (isAuthExpired)
                    throw new LogicException($"店铺【{curShop.NickName}】已经授权过期了，无法继续操作，请刷新页面按提示重新授权。", "auth_expires");
            }

            var _orderService = new LogicOrderService();

            //开始解密
            EncryptOrders(allOrders, EncryptFromType.LogicOrder, isMask: isMask, field: field, waybill_type: waybii_type, throwEx: throwEx);

            #region 注释
            //allOrders.GroupBy(x => x.ShopId).ToList().ForEach(g =>
            //{
            //    var os = g.ToList();
            //    try
            //    {
            //        var temp = allShops.FirstOrDefault(x => x.Id == g.Key);
            //        var service = new PinduoduoPlatformService(temp);
            //        var tmpOrders = _orderService.ConvertOrders(os);
            //        service.DecryptBatch(tmpOrders, isMask, field, waybii_type);
            //        ConvertDecryptOrderToLogicOrder(os, tmpOrders);
            //    }
            //    catch (LogicException ex)
            //    {
            //        var msg = ex.Message;
            //        if (msg.Contains("频繁"))
            //            throw new LogicException("由于拼多多平台限制，解密收件人信息过于频繁，打印中断。请使用拼多多电子面单打印，或减少单次打印的订单数量并过会儿重试下。");
            //        else if (msg.Contains("额度"))
            //            throw new LogicException($"解密收件人信息额度不够，请<a href='https://fuwu.pinduoduo.com/service-market/decrypt' target='_blank' style='color:#3aadff;margin:0px 5px;' title='点击去申请额度'>点击去申请更高额度</a>。或者更换拼多多电子面单打印。");
            //        else
            //            throw ex;
            //    }
            //    catch (Exception ex)
            //    {
            //        throw ex;
            //    }
            //}); 
            #endregion
        }

        public static void TryToDecryptOrders(List<Order> allOrders, List<Shop> shops = null, bool isMask = true, string field = "", string waybii_type = "", bool isBranchShare = false, bool throwEx = false)
        {
            var allShopIds = allOrders.Select(x => x.ShopId).Distinct().ToList();
            if (shops == null || shops.Any() == false)
            {
                shops = new ShopService().GetShopByIds(allShopIds);
            }

            if (shops == null || shops.Any() == false)
                return;

            //更新订单平台
            var shopDic = shops.GroupBy(x => x.Id).ToDictionary(s => s.Key, s => s.FirstOrDefault());
            var hasPddOrder = false;
            allOrders.ForEach(o =>
            {
                Shop shop = null;
                if (!shopDic.TryGetValue(o.ShopId, out shop))
                    o.PlatformType = shop.PlatformType;
                hasPddOrder = o.PlatformType == PlatformType.Pinduoduo.ToString();
            });

            var existShopIds = shops.Select(x => x.Id).ToList();
            if (existShopIds == null || existShopIds.Any() == false)
                throw new Exception("店铺数据异常，请重新登录");
            if (isMask == false && string.IsNullOrEmpty(waybii_type) && hasPddOrder)
                throw new LogicException("该面单类型暂无法解密，请更换拼多多面单打印");
            foreach (var shopId in allShopIds)
            {
                if (existShopIds.Contains(shopId) == false)
                    throw new LogicException("您要打印的订单中有些店铺已经被取消关联了，请确认重新关联或者刷新重试。");
                //授权过期了
                //var curShop = shops.FirstOrDefault(x => x.Id == shopId);
                //var isAuthExpired = curShop?.IsAuthExpired == true;
                //if (isAuthExpired)
                //    throw new LogicException($"店铺【{curShop.NickName}】已经授权过期了，无法继续操作，请刷新页面按提示重新授权。", "auth_expires");
            }

            var logicOrders = ConvertToLogicOrder(allOrders);
            //开始解密
            EncryptOrders(logicOrders, EncryptFromType.Order, isMask: isMask, field: field, waybill_type: waybii_type, needSyncLog: true, throwEx: throwEx);
            // 解密后收件人信息
            var loDic = logicOrders?.Where(x => x.PlatformOrderId.IsNotNullOrEmpty()).GroupBy(g => g.PlatformOrderId).ToDictionary(x => x.Key, x => x.ToList());

            if (loDic == null || loDic.Any() == false) return;

            allOrders.ForEach(o =>
            {
                List<LogicOrder> oLst;
                var key = o.OriginOrderId.IsNotNullOrEmpty() ? o.OriginOrderId : o.PlatformOrderId;
                if (!loDic.TryGetValue(key, out oLst))
                    return;
                var first = oLst.FirstOrDefault(x => x.PlatformOrderId == key);
                if (first != null)
                {
                    o.ToName = first.ToName;
                    o.ToMobile = first.ToPhone;
                    o.ToPhone = first.ToGPhone;
                    o.ToAddress = first.ToAddress;
                    o.ToFullAddress = first.ToFullAddress;
                    o.IsDecrypted = first.IsDecrypted;
                    o.DecryptErrorType = first.DecryptErrorType;
                    o.DecryptErrorMsg = first.DecryptErrorMsg;
                }
            });
        }

        /// <summary>
        /// 打印订单转LogicOrder，其中PlatformOrderId和ChildOrderId都是存放逻辑单号，
        /// </summary>
        /// <param name="orders"></param>
        /// <returns></returns>
        public static List<LogicOrder> ConvertToLogicOrder(List<Order> orders)
        {
            var logicOrders = new List<LogicOrder>();
            if (orders == null || orders.Any() == false)
                return logicOrders;

            // 分单打印订单转逻辑单
            orders.ForEach(o =>
            {
                var lo = new LogicOrder();
                lo.ShopId = o.ShopId;
                lo.PlatformType = o.PlatformType;
                lo.LogicOrderId = o.PlatformOrderId;
                lo.PlatformOrderId = o.OriginOrderId.IsNotNullOrEmpty() ? o.OriginOrderId : o.PlatformOrderId;
                lo.ToName = o.ToName;
                lo.ToPhone = o.ToMobile.IsNullOrEmpty() ? o.ToPhone : o.ToMobile;
                lo.ToGPhone = o.ToPhone;
                lo.ToAddress = o.ToAddress;
                lo.ToFullAddress = o.ToFullAddress;
                lo.ToProvince = o.ToProvince;
                lo.ToCity = o.ToCity;
                lo.ToCounty = o.ToCounty;
                lo.ToTown = o.ToTown;
                lo.ShopName = o.AgentName;
                lo.SupplierName = o.SupplierName;
                lo.PathFlowCode = o.PathFlowCode;
                lo.FxUserId = o.FxUserId;
                if (o.PlatformOrderId.StartsWith("C"))
                    lo.MergeredType = o.IsMergeredByHand ? 4 : 3;
                else
                    lo.MergeredType = o.IsSplitedByUser ? 5 :
                    o.IsMergeredByHand ? 2 :
                    o.IsMergered == true ? 1 : 0;
                //加密字段赋值
                if (lo.PlatformType == PlatformType.Taobao.ToString() || lo.PlatformType == PlatformType.AlibabaC2M.ToString() || lo.PlatformType == PlatformType.Alibaba.ToString() || lo.PlatformType == PlatformType.TaobaoMaiCaiV2.ToString())
                    lo.DecryptField = o.ExtField3;
                else if (lo.PlatformType == PlatformType.Jingdong.ToString())
                    lo.DecryptField = o.ExtField5;
                else if (lo.PlatformType == PlatformType.Suning.ToString())
                    lo.DecryptField = o.ExtField1;
                else if (lo.PlatformType == PlatformType.KuaiShou.ToString() || lo.PlatformType == PlatformType.TuanHaoHuo.ToString() || lo.PlatformType == PlatformType.XiaoHongShu.ToString())
                {
                    lo.DecryptField = o.ExtField2;
                    lo.ExtField1 = o.ExtField1;
                    lo.ExtField2 = o.ExtField2;
                }
                else if (lo.PlatformType == PlatformType.WxVideo.ToString())
                {
                    lo.ExtField1 = o.ExtField1;
                    lo.ExtField2 = o.ExtField2;
                    lo.DecryptField = o.ExtField4;
                }
                lo.IsDecrypted = o.IsDecrypted;

                var lois = new List<LogicOrderItem>();
                o.OrderItems.ForEach(oi =>
                {
                    var loi = new LogicOrderItem();
                    lo.ShopId = o.ShopId;
                    loi.LogicOrderId = oi.PlatformOrderId;
                    loi.OrignalOrderId = oi.OrignalOrderId;
                    loi.PlatformOrderId = oi.OrignalPlatformOrderId;
                    lois.Add(loi);
                });
                logicOrders.Add(lo);
            });

            return logicOrders;
        }

        #endregion

        #region 解密收件人信息（包含合单处理）

        private static List<LogicOrder> DecryptOrders(List<LogicOrder> orders, EncryptFromType fromType, List<Shop> shops = null, bool isMask = true, string field = "", string waybill_type = "")
        {
            //Utility.Log.WriteWarning($"待解密的订单数量：{orders?.Count ?? 0}");

            if (orders == null || orders.Any() == false)
                return orders;

            if (shops == null)
            {
                var shopIds = orders.Select(x => x.ShopId).Distinct().ToList();
                shops = new ShopService().GetShopByIds(shopIds);
            }

            if (shops == null || shops.Any() == false)
                throw new LogicException("未找到店铺相关信息");

            var _lorderService = new LogicOrderService();
            //var _orderService = new OrderService();
            var waitDecryptOrders = orders.Where(x => !x.IsMainOrder).ToList();

            //合单对应的子单修改收件人信息，会导致合单无法解密，需要用子单去解密后再将收件人信息赋值给合单
            var mergerOrders = orders.Where(x => x.IsMainOrder).ToList();
            if (mergerOrders.Any())
            {
                // 未查询ChildOrderId,此处重新查一次
                mergerOrders.Where(x => x.ChildOrderId.IsNullOrEmpty()).ToList().ForEach(x =>
                {
                    if (x.LogicOrderItems != null)
                        x.ChildOrderId = x.LogicOrderItems.Select(o => o.OrignalOrderId).Distinct().ToStringWithSplit(",");
                });

                var noChildMOrderIds = mergerOrders.Where(x => x.ChildOrderId.IsNullOrEmpty()).Select(x => x.LogicOrderId).Distinct().ToList();
                if (noChildMOrderIds.Any())
                {
                    var noChildMOrders = _lorderService.GetOrders(noChildMOrderIds, fields: new List<string> { "o.Id", "o.PlatformOrderId", "o.ShopId", "o.LogicOrderId", "o.ChildOrderId", "oi.Id", "oi.OrignalOrderId", "oi.PlatformOrderId", "oi.LogicOrderId" }, queryReceiver: new QueryReceiverModel { IsOnlyGetMask = false });
                    //Utility.Log.WriteWarning($"合并订单重置ChildOrder：{noChildMOrders.Select(x => new { x.PlatformOrderId, x.LogicOrderId, x.ShopId, x.ToName, x.ToPhone, x.ToAddress, x.ToFullAddress }).ToJson()}");
                    noChildMOrders.ForEach(x =>
                    {
                        var morder = mergerOrders.FirstOrDefault(o => o.LogicOrderId == x.LogicOrderId);
                        if (morder != null)
                        {
                            morder.LogicOrderItems = x.LogicOrderItems;
                            morder.ChildOrderId = x.LogicOrderItems.Select(o => o.OrignalOrderId).Distinct().ToStringWithSplit(",");
                        }
                    });
                    //Utility.Log.WriteWarning($"合并订单重置ChildOrder后：{noChildMOrders.Select(x => new { x.PlatformOrderId, x.LogicOrderId, x.ShopId, x.ToName, x.ToPhone, x.ToAddress, x.ToFullAddress }).ToJson()}");
                }
                
                var keys = new List<string>();
                mergerOrders.GroupBy(x => x.ShopId).ToList().ForEach(g =>
                {
                    keys = g.SelectMany(x => x.ChildOrderId.SplitToList(",")).Distinct().ToList();
                    if (keys.Any() == false)
                        return;
                    
                    //先取合单订单编号对应的子单，如果没有取任意一个子单（合单对应的子单可能会被拆出）
                    var childOrders = _lorderService.GetOrders(keys, queryReceiver: new QueryReceiverModel { IsOnlyGetMask = false });
                    g.ToList().ForEach(m =>
                    {
                        // MergeredOrderId 合单可能会被拆掉，需要用平台单号重新匹配
                        var mChildOrders = childOrders.Where(x => x.MergeredOrderId == m.LogicOrderId).ToList();
                        if (mChildOrders.Any() == false)
                            mChildOrders = childOrders.Where(x => x.PlatformOrderId == m.PlatformOrderId).ToList();

                        var first = mChildOrders.FirstOrDefault(x => m.PlatformOrderId == x.PlatformOrderId);
                        if (first != null)
                        {
                            //if (QueryEncryptedReceiverPlatformTypes.Contains(first.PlatformType) && !string.IsNullOrEmpty(first.ExtField1))
                            //    new KuaiShouEncryptedReceiverInfoService().SetOrderEncryptedReceiverInfoByLogicOrder(new List<LogicOrder> { first });

                            waitDecryptOrders.Add(first);
                        }
                        else if (first == null && mChildOrders.Any())
                        {
                            first = mChildOrders.FirstOrDefault();
                            //if (QueryEncryptedReceiverPlatformTypes.Contains(first.PlatformType) && !string.IsNullOrEmpty(first.ExtField1))
                            //    new KuaiShouEncryptedReceiverInfoService().SetOrderEncryptedReceiverInfoByLogicOrder(new List<LogicOrder> { first });

                            waitDecryptOrders.Add(first);

                            Utility.Log.WriteWarning($@"合单未找到，取任意子单：
\n合单：{new { m.PlatformOrderId, m.LogicOrderId, m.ShopId, m.ChildOrderId, m.ToName, m.ToPhone, m.ToAddress, m.ToFullAddress }.ToJson()}
\n子单：{new { first.PlatformOrderId, first.LogicOrderId, first.ShopId, first.ChildOrderId, first.ToName, first.ToPhone, first.ToAddress, first.ToFullAddress }.ToJson()}");
                        }
                        else
                        {
                            //m.DecryptErrorType = DecryptErrType.ServerErr.ToInt();
                            Utility.Log.WriteError($"合单【{m.LogicOrderId}】未找到对应的子单信息");
                        }
                    });
                });
            }

            //Utility.Log.WriteWarning($"解密前合单处理：{waitDecryptOrders.Select(x => new { x.PlatformOrderId, x.LogicOrderId, x.ShopId, x.ToName, x.ToPhone, x.ToAddress, x.ToFullAddress }).ToJson()}");
            waitDecryptOrders.GroupBy(g => g.ShopId).ToList().ForEach(g =>
            {
                var itemShop = shops.FirstOrDefault(s => s.Id == g.Key);
                if (itemShop == null)
                    return;

                //判断是否需要加密的平台
                var hasEncrypt = orders.Any(x => encryptPlatformTypes.Contains(itemShop.PlatformType.ToString2()));
                if (!hasEncrypt)
                    return;

                var logPids = new List<string>();
                var waitPtDecryptOrders = new List<Order>();
                if (encryptPlatformTypes.Contains(itemShop.PlatformType))
                {
                    waitPtDecryptOrders = ConvertLogicOrderToDecryptOrder(g.ToList());
                }
                //Utility.Log.WriteWarning($"解密前Order：{waitPtDecryptOrders.Select(x => new { x.PlatformOrderId, x.LogicOrderId, x.ShopId, x.ToName, x.ToMobile, x.ToPhone, x.ToProvince, x.ToCity, x.ToCounty, x.ToAddress, x.ToFullAddress }).ToJson()}");
                var service = PlatformFactory.GetPlatformService(itemShop);
                
                if (itemShop.PlatformType == PlatformType.Taobao.ToString() && fromType == EncryptFromType.Order)
                {
                    // 淘宝新增接口解密
                    var tbOrders = g.Where(x => x.DecryptField.IsNotNullOrEmpty()).ToList();
                    if (tbOrders.Any())
                    {
                        var tbService = service as TaobaoPlatformService;
                        tbService.DecryptBatchOrders(waitPtDecryptOrders);
                    }
                }
                else if ((itemShop.PlatformType == PlatformType.AlibabaC2M.ToString()|| itemShop.PlatformType == PlatformType.TaobaoMaiCaiV2.ToString()) && fromType == EncryptFromType.Order)
                {
                    // AlibabaC2M新增接口解密
                    var tbOrders = g.Where(x => x.DecryptField.IsNotNullOrEmpty()).ToList();
                    if (tbOrders.Any())
                    {
                        var c2mService = service as AlibabaC2MService;
                        c2mService.DecryptBatchOrders(waitPtDecryptOrders);
                    }
                }
                else if (itemShop.PlatformType == PlatformType.Alibaba.ToString() && fromType == EncryptFromType.Order)
                {
                    // Alibaba新增接口解密
                    var tbOrders = g.Where(x => x.DecryptField.IsNotNullOrEmpty()).ToList();
                    if (tbOrders.Any())
                    {
                        var alibabaService = service as AlibabaPlatformService;
                        alibabaService.DecryptBatchOrders(waitPtDecryptOrders);
                    }
                }
                else if (itemShop.PlatformType == PlatformType.Pinduoduo.ToString() && fromType == EncryptFromType.Order)
                {
                    var pddService = service as PinduoduoPlatformService;
                    pddService.DecryptBatch(waitPtDecryptOrders, isMask: isMask, field: field, waybill_type: waybill_type);
                }
                else if ((itemShop.PlatformType == PlatformType.TouTiao.ToString() ||
                          itemShop.PlatformType == PlatformType.TouTiaoSaleShop.ToString()) &&
                         fromType == EncryptFromType.Order)
                {
                    var zdService = service as ZhiDianNewPlatformService;
                    //头条的解密要处理一下数据
                    waitPtDecryptOrders.ForEach(f => f.ToAddress = f.ToAddress.TouTiaoExtractDyAllIndex());
                    zdService.DecryptBatch(waitPtDecryptOrders, isMask: isMask, field: field, waybill_type: waybill_type);
                }
                else if (itemShop.PlatformType == PlatformType.KuaiShou.ToString() && fromType == EncryptFromType.Order)
                {
                    var kdService = service as KuaiShouPlatformService;
                    kdService.DecryptBatch(waitPtDecryptOrders, isMask: isMask, field: field);
                }
                else if (itemShop.PlatformType == PlatformType.YouZan.ToString())
                {
                    //汇总需要解密的字段
                    var decryptFields = new List<string>();
                    if (string.IsNullOrWhiteSpace(field))
                    {
                        foreach (var item in waitPtDecryptOrders)
                        {
                            if (YouZanPlatformService.IsEncryptData(item.ToName))
                                decryptFields.Add(item.ToName);
                            if (YouZanPlatformService.IsEncryptData(item.ToPhone))
                                decryptFields.Add(item.ToPhone);
                            if (YouZanPlatformService.IsEncryptData(item.ToMobile))
                                decryptFields.Add(item.ToMobile);
                            if (YouZanPlatformService.IsEncryptData(item.ToAddress))
                                decryptFields.Add(item.ToAddress);
                            if (YouZanPlatformService.IsEncryptData(item.BuyerWangWang))
                                decryptFields.Add(item.BuyerWangWang);
                        }
                    }
                    else
                    {
                        foreach (var item in waitPtDecryptOrders)
                        {
                            if (field?.ToLower() == "toname" && YouZanPlatformService.IsEncryptData(item.ToName))
                                decryptFields.Add(item.ToName);
                            else if (field?.ToLower() == "tophone" && YouZanPlatformService.IsEncryptData(item.ToPhone))
                                decryptFields.Add(item.ToPhone);
                            else if (field?.ToLower() == "tomobile" && YouZanPlatformService.IsEncryptData(item.ToMobile))
                                decryptFields.Add(item.ToMobile);
                            else if (field?.ToLower() == "toaddress" && YouZanPlatformService.IsEncryptData(item.ToAddress))
                                decryptFields.Add(item.ToAddress);
                            else if (field?.ToLower() == "buyerwagnwang" && YouZanPlatformService.IsEncryptData(item.BuyerWangWang))
                                decryptFields.Add(item.BuyerWangWang);
                        }
                    }
                    var yzService = service as YouZanPlatformService;
                    var decryptResultDict = yzService.DecryptBatch(decryptFields);
                    foreach (var item in waitPtDecryptOrders)
                    {
                        if (item.ToName != null && decryptResultDict.ContainsKey(item.ToName))
                        {
                            item.ToName = decryptResultDict[item.ToName];
                            if (isMask)
                                item.ToName = item.ToName?.ToEncryptName();
                        }

                        if (item.ToPhone != null && decryptResultDict.ContainsKey(item.ToPhone))
                        {
                            item.ToPhone = decryptResultDict[item.ToPhone];
                            if (isMask)
                                item.ToPhone = item.ToPhone?.ToEncrytPhone();
                        }

                        if (item.ToMobile != null && decryptResultDict.ContainsKey(item.ToMobile))
                        {
                            item.ToMobile = decryptResultDict[item.ToMobile];
                            if (isMask)
                                item.ToMobile = item.ToMobile?.ToEncrytPhone();
                        }

                        if (item.ToAddress != null && decryptResultDict.ContainsKey(item.ToAddress))
                        {
                            item.ToAddress = decryptResultDict[item.ToAddress];
                            if (isMask)
                                item.ToAddress = item.ToAddress?.ToPddEncryptAddress();
                            item.ToFullAddress = item.ToProvince + item.ToCity + item.ToCounty + item.ToTown + item.ToAddress;
                        }

                        if (item.BuyerWangWang != null && decryptResultDict.ContainsKey(item.BuyerWangWang))
                        {
                            item.BuyerWangWang = decryptResultDict[item.BuyerWangWang];
                            if (isMask)
                                item.BuyerWangWang = item.BuyerWangWang?.ToPddEncryptAddress();
                        }
                    }
                }
                else if (itemShop.PlatformType == PlatformType.Suning.ToString())
                {
                    var snservice = service as SuningPlatformService;
                    snservice.DecryptBatch(waitPtDecryptOrders);
                }
                else if (itemShop.PlatformType == PlatformType.TuanHaoHuo.ToString())
                {
                    var thhservice = service as TuanHaoHuoPlatformService;
                    thhservice.DecryptBatch(waitPtDecryptOrders);
                }
                else if (itemShop.PlatformType == PlatformType.XiaoHongShu.ToString())
                {
                    var thhservice = service as XiaoHongShuV2PlatformService;
                    thhservice.DecryptBatch(waitPtDecryptOrders, isMask: isMask, field: field);
                }
                else if (itemShop.PlatformType == PlatformType.WeiDian.ToString())
                {
                    var wdService = service as WeiDianPlatformService;
                    wdService.DecryptBatch(waitPtDecryptOrders, isMask: isMask);
                }
                else if (itemShop.PlatformType == PlatformType.WxVideo.ToString() && fromType == EncryptFromType.Order)
                {
                    var wdService = service as WxVideoPlatformService;
                    if (isMask == false) //非打码才解密
                    {
                        wdService.DecryptBatch(waitPtDecryptOrders);
                    }
                }
                else if (itemShop.PlatformType == PlatformType.Virtual.ToString())
                {
                    //if (isMask) //非打码才解密
                    //{
                    //    //获取打码的数据
                    //    waitPtDecryptOrders.ForEach(item =>
                    //    {
                    //        var encrypt = string.Empty;
                    //        if (item.ToMobile?.Contains("#") == true)
                    //            encrypt = item.ToMobile;
                    //        else if (item.ToPhone?.Contains("#") == true)
                    //            encrypt = item.ToPhone;

                    //        if (!string.IsNullOrWhiteSpace(encrypt))
                    //        {
                    //            var splitArrary = encrypt.Split("#".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                    //            item.ToPhone = splitArrary[0];
                    //            item.ToMobile = item.ToPhone;
                    //        }
                    //    });
                    //}
                    //else
                    //{
                    //解密 线下单不脱敏展示，直接明文展示，所以都有都解密
                    waitPtDecryptOrders.ForEach(item =>
                    {
                        var encrypt = string.Empty;
                        if (item.ToMobile?.Contains("#") == true)
                            encrypt = item.ToMobile;
                        else if (item.ToPhone?.Contains("#") == true)
                            encrypt = item.ToPhone;

                        if (!string.IsNullOrWhiteSpace(encrypt))
                        {
                            var splitArrary = encrypt.Split("#".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                            if (splitArrary.Length > 1)
                            {
                                var mobile = splitArrary[0];
                                try
                                {
                                    var encryptMobile = splitArrary[1];
                                    mobile = CustomEncryptionService.MobileDecrypt(encryptMobile, item.ShopId);
                                }
                                catch (Exception ex)
                                {
                                    DianGuanJiaApp.Utility.Log.WriteError(ex.ToString());
                                }

                                item.ToPhone = mobile;
                                item.ToMobile = item.ToPhone;
                            }
                        }
                    });
                    //}
                }
                //Utility.Log.WriteWarning($"解密后Order：{waitPtDecryptOrders.Select(x => new { x.PlatformOrderId, x.LogicOrderId, x.ShopId, x.ToName, x.ToMobile, x.ToPhone, x.ToProvince, x.ToCity, x.ToCounty, x.ToAddress, x.ToFullAddress }).ToJson()}");
               
                //解密后校验解密结果，部分字段失败抛异常
                CheckEncryptResult(waitPtDecryptOrders, itemShop.PlatformType, field);
                
                //解密后Order收件人信息更新回LogicOrder
                ConvertDecryptOrderToLogicOrder(g.ToList(), waitPtDecryptOrders);
                
                //Utility.Log.WriteWarning($"解密后Order=>LogicOrder：{g.Select(x => new { x.PlatformOrderId, x.LogicOrderId, x.ShopId, x.ToName, x.ToPhone, x.ToProvince, x.ToCity, x.ToCounty, x.ToAddress, x.ToFullAddress }).ToJson()}");

                /*
                if (itemShop.PlatformType == PlatformType.Taobao.ToString())
                {
                    // 淘宝新增接口解密
                    var tbOrders = g.Where(x => x.DecryptField.IsNotNullOrEmpty()).ToList();
                    var orderAddressDict = tbOrders.GroupBy(x => x.LogicOrderId).ToDictionary(f => f.Key, f => f.FirstOrDefault().ToAddress);
                    //TODO:解密之后，看下订单地址是否修改过，修改过的订单地址，则不显示街道拼接的详细地址
                    foreach (var order in tbOrders)
                    {
                        if (orderAddressDict.ContainsKey(order.LogicOrderId) == false) return;
                        var orderToAddress = orderAddressDict[order.LogicOrderId];
                        if (orderToAddress != order.ToAddress)
                        {
                            order.ToAddress = order.ToAddressNotHasTown;
                            order.ToFullAddress = order.ToProvince + order.ToCity + order.ToCounty + order.ToTown + order.ToAddress;
                        }
                    }
                }
                */
            });

            
            //合单更新解密后的收件人信息
            //Utility.Log.WriteWarning($"合并订单解密mergerOrders：{mergerOrders.Select(x => new { x.PlatformOrderId, x.LogicOrderId, x.ShopId, x.ToName, x.ToPhone, x.ToProvince, x.ToCity, x.ToCounty, x.ToAddress, x.ToFullAddress }).ToJson()}");
            mergerOrders.ForEach(m =>
            {
                //先根据合单取解密结果，如果未找到则取子单的解密的结果
                var decryptChildOrder = waitDecryptOrders.FirstOrDefault(x => x.PlatformOrderId == m.PlatformOrderId);
                if (decryptChildOrder == null)
                    decryptChildOrder = waitDecryptOrders.FirstOrDefault(x => m.LogicOrderItems.Any(oi => oi.PlatformOrderId == x.PlatformOrderId));
                if (decryptChildOrder != null)
                {
                    m.ToName = decryptChildOrder.ToName;
                    m.ToPhone = decryptChildOrder.ToPhone;
                    m.ToGPhone = decryptChildOrder.ToGPhone;
                    m.IsDecrypted = decryptChildOrder.IsDecrypted;
                    m.DecryptErrorType = decryptChildOrder.DecryptErrorType;
                    m.DecryptErrorMsg = decryptChildOrder.DecryptErrorMsg;
                    if (m.PlatformType == PlatformType.Pinduoduo.ToString() ||
                        m.PlatformType == PlatformType.TouTiao.ToString() ||
                        m.PlatformType == PlatformType.TouTiaoSaleShop.ToString() ||
                        m.PlatformType == PlatformType.WeiDian.ToString()) 
                    {
                        m.BuyerWangWang = m.ToName;
                    }

                    if (m.ToFullAddress.ToString2().Contains(m.ToAddress))
                    {
                        m.ToFullAddress = m.ToFullAddress.TrimEnd(m.ToAddress);
                        m.ToAddress = decryptChildOrder.ToAddress;
                        m.ToFullAddress = m.ToFullAddress + m.ToAddress;
                    }
                    else
                    {
                        m.ToFullAddress = decryptChildOrder.ToFullAddress;
                        m.ToAddress = decryptChildOrder.ToAddress;
                    }
                }
            });
            //Utility.Log.WriteWarning($"合并订单解密后LogicOrder：{mergerOrders.Select(x => new { x.PlatformOrderId, x.LogicOrderId, x.ShopId, x.ToName, x.ToPhone, x.ToProvince, x.ToCity, x.ToCounty, x.ToAddress, x.ToFullAddress }).ToJson()}");
            return orders;
        }

        #endregion
    }
}
