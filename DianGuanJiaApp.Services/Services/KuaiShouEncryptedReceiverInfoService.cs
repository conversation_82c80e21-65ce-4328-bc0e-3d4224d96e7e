using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.LogisticService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Services.WaybillService;
using DianGuanJiaApp.Utility.Extension;
using System.Collections;
using DianGuanJiaApp.Data.Enum;
using System.IO;
using DianGuanJiaApp.Data.EntityExtension;
using Newtonsoft.Json.Linq;
using System.Net.Http;
using System.Web;
using DianGuanJiaApp.Data.MongoRepository;
using DianGuanJiaApp.Services.ServicesExtension;
using System.Threading;
using System.Collections.Concurrent;
using DianGuanJiaApp.Data;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using System.Runtime.InteropServices;
using System.Web.ModelBinding;
using NPOI.XSSF.UserModel;
using vipapis.vipmax.order;

namespace DianGuanJiaApp.Services
{
    /// <summary>
    /// 订单相关的服务
    /// </summary>
    public partial class KuaiShouEncryptedReceiverInfoService : BaseService<KuaiShouEncryptedReceiverInfo>
    {
        private KuaiShouEncryptedReceiverInfoRepository _repository;
        public KuaiShouEncryptedReceiverInfoService()
        {
            _repository = new KuaiShouEncryptedReceiverInfoRepository();
        }

        public KuaiShouEncryptedReceiverInfoService(string connectionString) : base(connectionString)
        {
            _repository = new KuaiShouEncryptedReceiverInfoRepository(connectionString);
        }

        public List<KuaiShouEncryptedReceiverInfo> GetList(List<string> relationCodes, List<int> sids, string fields = "*")
        {
            return _repository.GetList(relationCodes, sids, fields);
        }

        /// <summary>
        /// 快手订单(Order)赋值 EncryptedReceiverInfo
        /// </summary>
        /// <param name="orders"></param>
        public void SetOrderEncryptedReceiverInfoByOrder(List<Order> orders)
        {
            var ksOrders = orders.Where(f => f.PlatformType == PlatformType.KuaiShou.ToString());
            //快手的加密信息另外存储的，需另外查询
            var relationCodes = ksOrders.Select(f => f.ExtField1).Distinct().ToList();
            var sids = ksOrders.Select(f => f.ShopId).Distinct().ToList();
            var encryptedReceiverInfoList = GetList(relationCodes, sids);
            var receiverInfoDict = encryptedReceiverInfoList.ToDictionary(f => f.RelationCode, f => f);
            foreach (var item in ksOrders)
            {
                KuaiShouEncryptedReceiverInfo model;
                if (receiverInfoDict.TryGetValue(item.ExtField1, out model))
                    item.EncryptedReceiverInfo = model;
            }
        }

        /// <summary>
        /// 快手订单(LogicOrder)赋值 EncryptedReceiverInfo[此方法作废2022-11-23]
        /// </summary>
        /// <param name="orders"></param>
        public void SetOrderEncryptedReceiverInfoByLogicOrder(List<LogicOrder> orders)
        {
            var ksOrders = orders.Where(f => FxPlatformEncryptService.QueryEncryptedReceiverPlatformTypes.Contains(f.PlatformType));
            //快手的加密信息另外存储的，需另外查询
            var relationCodes = ksOrders.Select(f => f.ExtField1).Distinct().ToList();
            var sids = ksOrders.Select(f => f.ShopId).Distinct().ToList();
            var encryptedReceiverInfoList = GetList(relationCodes, sids);
            var receiverInfoDict = encryptedReceiverInfoList.ToDictionary(f => f.RelationCode, f => f);
            foreach (var item in ksOrders)
            {
                KuaiShouEncryptedReceiverInfo model;
                if (receiverInfoDict.TryGetValue(item.ExtField1, out model))
                    item.EncryptedReceiverInfo = model;
            }
        }

        /// <summary>
        /// 订单赋值 EncryptedReceiverInfo[此方法作废2022-11-23]
        /// </summary>
        /// <param name="orders"></param>
        /// <param name="needSetValOnOrderField">需要为订单上的收件人信息字段赋值</param>
        public void SetOrderEncryptedReceiverInfo(List<Order> orders, bool needSetValOnOrderField = false)
        {
            var ksOrders = orders.Where(f => FxPlatformEncryptService.QueryEncryptedReceiverPlatformTypes.Contains(f.PlatformType));
            //快手的加密信息另外存储的，需另外查询
            var relationCodes = ksOrders.Where(f => string.IsNullOrWhiteSpace(f.ExtField1) == false).Select(f => f.ExtField1).Distinct().ToList();
            var sids = ksOrders.Select(f => f.ShopId).Distinct().ToList();
            var encryptedReceiverInfoList = GetList(relationCodes, sids);
            if (encryptedReceiverInfoList == null || encryptedReceiverInfoList.Any() == false)
                return;
            var receiverInfoDict = encryptedReceiverInfoList.ToDictionary(f => f.RelationCode, f => f);
            foreach (var item in ksOrders)
            {
                KuaiShouEncryptedReceiverInfo model;
                if (receiverInfoDict.TryGetValue(item.ExtField1, out model))
                {
                    item.EncryptedReceiverInfo = model;
                    if (needSetValOnOrderField)
                    {
                        item.ToName = model.EncryptedToName;
                        item.ToMobile = model.EncryptedToMobile;
                        item.ToFullAddress = item.ToFullAddress.TrimEnd(item.ToAddress) + model.EncryptedToAddress;
                        item.ToAddress = model.EncryptedToAddress;
                        item.Warehouse = model.PlatformOrderId;
                    }
                }
            }
        }

        /// <summary>
        /// 批量插入
        /// </summary>
        /// <param name="list"></param>
        public void BulkInsert(List<KuaiShouEncryptedReceiverInfo> list)
        {
            //1.先查询
            var relationCodes = list.Select(f => f.RelationCode).Distinct().ToList();
            var sids = list.Select(f => f.ShopId).Distinct().ToList();
            var listFromDb = GetList(relationCodes, sids);
            var existDict = listFromDb.ToDictionary(f => f.RelationCode, f => f);

            var insertList = new List<KuaiShouEncryptedReceiverInfo>();
            foreach (var item in list)
            {
                if (existDict.ContainsKey(item.RelationCode))
                    continue;
                insertList.Add(item);
            }

            //2.只插入不存在
            _repository.BulkInserts(insertList);
        }

    }
}
