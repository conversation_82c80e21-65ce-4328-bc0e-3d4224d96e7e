using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.MongoRepository;
using DianGuanJiaApp.Services.LogisticService;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using WebSocketSharp.Frame;

namespace DianGuanJiaApp.Services
{

    public partial class LogisticTracesService : BaseService<Data.Entity.LogisticTraces>
    {
        /// <summary>
        /// Ӧ����Ϣ
        /// </summary>
        private LogisticsAppInfo _app { get; set; }

        public LogisticTracesRepository _repository = new LogisticTracesRepository();
        public LogisticCodesRepository _codesRepository = new LogisticCodesRepository();
        public LogisticsAppInfoRepository _appRepository = new LogisticsAppInfoRepository();
        public ExpressCpCodeMappingService _cpCodeMappingService = new ExpressCpCodeMappingService();
        public TracesMessageRepository _traceMessageRepo = new TracesMessageRepository();
        /// <summary>
        /// ��ȡ����������Դ���ͣ���Դ������ָ��������Դ����Ϊ׼����δָ������ƽ̨�жϣ�ƴ���ƽ̨��ʹ��ƴ����������������ƽ̨��ʹ�ÿ�������������
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        private TraceSourceType GetLogisticTrackingSourceType(LogisticCodes code)
        {
            var st = TraceSourceType.Pinduoduo;
            if (string.IsNullOrEmpty(code.ShipperCode))
                return st;
            switch (code.PlatformType)
            {
                case "Pinduoduo":
                    st = TraceSourceType.Pinduoduo;
                    break;
                default:
                    st = TraceSourceType.Pinduoduo;
                    break;
            }
            return st;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="app">Ӧ����Ϣ</param>
        public LogisticTracesService(LogisticsAppInfo app)
        {
            if (app == null)
                throw new ArgumentException("Ӧ����Ϣ����Ϊ��");
            _app = app;
        }


        /// <summary>
        /// ���յ��Ĺ켣��Ϣ����һ�ݣ���������ͳ��
        /// </summary>
        /// <param name="message"></param>
        public void AddMessage(List<TracesMessage> messages)
        {
            foreach (var item in messages)
            {
                _traceMessageRepo.Add(item);
            }
        }

        /// <summary>
        /// ����˵���
        /// </summary>
        /// <param name="codes"></param>
        public void Add(List<LogisticCodePushRequest> codes)
        {
            if (codes == null || !codes.Any())
                return;
            //ģ��ת��
            var models = codes.Select(c => new LogisticCodes
            {
                AppKey = _app.AppKey,
                ShipperCode = c.ShipperCode,
                LogisticCode = c.LogisticCode,
                Sender = c.Sender,
                Receiver = c.Receiver,
                State = c.State,
                PlatformType = c.PlatformType,
                OwnerId = c.OwnerId,
                TraceStatus = TraceStatusType.Pending,
                TraceSource = c.TraceType,
                IsPddWaybill = c.IsPddWaybill,
                TemplateType = c.TemplateType,
                CreateTime = DateTime.Now,
            });
            ThreadPool.QueueUserWorkItem(state=> {
                try
                {
                    foreach (var model in models)
                    {
                        TryAdd(model);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"����Web�˶�����Ϣ��ָ���켣Դ����ʱ��������{ex}");
                }

            });
        }

        /// <summary>
        /// ��������켣��Ϣ
        /// </summary>
        /// <param name="trace"></param>
        /// <param name="isContainAllTraces">�Ƿ����ȫ���������켣��Ϣ��ƴ����ֻ��������һ������Ҫ����������кϲ���</param>
        public void Add(LogisticTraces trace, bool isContainAllTraces = true)
        {
            Add(new List<LogisticTraces>() { trace }, isContainAllTraces);
        }

        /// <summary>
        /// ��������켣��Ϣ
        /// </summary>
        /// <param name="traces"></param>
        /// <param name="isContainAllTraces">�Ƿ����ȫ���������켣��Ϣ��ƴ����ֻ��������һ������Ҫ����������кϲ���</param>
        public void Add(List<LogisticTraces> traces, bool isContainAllTraces = true)
        {
            if (traces == null || !traces.Any())
                return;
            //ģ��ת��

            foreach (var trace in traces)
            {
                try
                {
                    TryAdd(trace, isContainAllTraces);
                }
                catch (Exception ex)
                {
                    var json = trace.ToJson();
                    Log.WriteError($"���������켣��MongoDBʱ�������󣬹켣��Ϣ��{json}��������Ϣ��{ex}");
                    new Data.Repository.ShopRepository().WriteLogisticsConsumerLog(json, ex.ToString(), "SaveTraceToMongoDB");
                }
            }
        }

        /// <summary>
        /// ���������Ϣ����
        /// </summary>
        /// <param name="code"></param>
        public void TryAdd(LogisticCodes code)
        {
            if (code == null)
                return;
            if (code.TraceSource == null)
                code.TraceSource = GetLogisticTrackingSourceType(code);
            var old = _codesRepository.GetByKey(code.SearchKey);
            if (old != null)
                return;
            else
            {
                try
                {
                    //TODO:���ݲ�ͬ�ĸ�����Դ������ͬ�Ľӿ�
                    code.ThirdShipperCode = code.ShipperCode;
                    var cpCode = GetCpCodeFromCache(code.ShipperCode, code.TraceSource.ToString());
                    if (!string.IsNullOrEmpty(cpCode))
                        code.ThirdShipperCode = cpCode;
                    else
                        throw new Exception($"�޷�ʶ��Ŀ�����ͣ�{code.ShipperCode}");
                    ////����񲻿����ˣ�ȥ����
                    //PushToKuaidiNiao(code);

                    if (code.IsPddWaybill || (code.PlatformType == PlatformType.Pinduoduo.ToString() && PinduoduoPlatformService.IsEncryptData(code?.Receiver?.Mobile)))
                        code.TraceContent = $"ƴ����ӡ��ƴ����浥���趩��";
                    else
                    {
                        PushToPinduoduo(code); //����ƴ���

                        //������ͨ�켣
                        ZTOSubscribe(code);
                    }

                    //������ͨ�켣
                    STOSubscribe(code);

                    //����Բͨ�켣
                    YTOSubscribe(code);

                    //���İ����켣
                    HTKYSubscribe(code);

                    //���ĵ°�켣
                    DBKDSubscribe(code);

                    //�������ʹ켣
                    ZYKDSubscribe(code);

                    //TODO: ����yto��sto������
                    //do something

                    code.TraceStatus = TraceStatusType.Tracking;
                }
                catch (Exception ex)
                {
                    code.TraceStatus = TraceStatusType.Error;
                    code.TraceContent = $"��������" + ex.Message;
                    Utility.Log.WriteError($"���������Ϣʱ��������������Ϣ��{code?.ToJson()}\r\n������Ϣ{ex}");
                }
                finally
                {
                    _codesRepository.Add(code);
                }
            }

        }

        public string GetCpCodeFromCache(string shipperCode, string traceSource)
        {
            var cpCode = "";
            var key = $"/System/Config/CpCodeMapping/{traceSource}/{shipperCode}";
            cpCode = HttpRuntime.Cache[key]?.ToString();
            if (cpCode == null)
            {
                cpCode = _cpCodeMappingService.Get(shipperCode, traceSource)?.CpCode ?? "";
                HttpRuntime.Cache.Insert(key, cpCode, null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
            }
            return cpCode;
        }

        /// <summary>
        /// ���Ͷ��ĵ�ƴ���
        /// </summary>
        /// <param name="code"></param>
        private void PushToPinduoduo(LogisticCodes code)
        {
            //�ֻ��ż��ܵĲ�����
            if (string.IsNullOrEmpty(code.Receiver?.Mobile))
                throw new ArgumentException("�ռ����ֻ����벻��Ϊ��");
            if (code.Receiver.Mobile?.Contains("**") == true)
                throw new ArgumentException("�ռ����ֻ����벻�Ϸ�");
            if (code == null)
                throw new ArgumentException("����code����Ϊ��");
            if (string.IsNullOrEmpty(code.LogisticCode))
                throw new ArgumentException("����������Ϣ����Ϊ��");
            if (string.IsNullOrEmpty(code.ShipperCode))
                throw new ArgumentException("��ݹ�˾���벻��Ϊ��");
            var pdd = new PinduoduoPlatformService(new Shop { });
            var ship_code = code.ThirdShipperCode;
            if (ship_code == "JT")
                ship_code = "JTSD";
            var result = pdd.TraceNotifySubscrible(ship_code, code.Receiver.Mobile, code.LogisticCode);
            if (result.Key == false)
                throw new Exception($"���������켣��Ϣʧ�ܣ�ƴ���ӿ��쳣,{result.Value}");
        }

        /// <summary>
        /// ������ͨ�����켣��Ϣ
        /// </summary>
        /// <param name="code"></param>
        private void ZTOSubscribe(LogisticCodes code)
        {
            if (code.ShipperCode != "ZTO")
                return;

            if (string.IsNullOrEmpty(code.LogisticCode))
                throw new ArgumentException("����������Ϣ����Ϊ��");
            if (string.IsNullOrEmpty(code.ShipperCode))
                throw new ArgumentException("��ݹ�˾���벻��Ϊ��");

            var result = (new NewZTOLogisticService()).SubscribeData(new List<string> { code.LogisticCode });
            if (result.Key == false)
                throw new Exception($"������ͨ�����켣��Ϣʧ�ܣ�{result.Value}");
        }

        /// <summary>
        /// ������ͨ�����켣��Ϣ
        /// </summary>
        /// <param name="code"></param>
        private void STOSubscribe(LogisticCodes code)
        {
            if (code.ShipperCode != "STO")
                return;

            if (string.IsNullOrEmpty(code.LogisticCode))
                throw new ArgumentException("����������Ϣ����Ϊ��");
            if (string.IsNullOrEmpty(code.ShipperCode))
                throw new ArgumentException("��ݹ�˾���벻��Ϊ��");

            var result = (new NewSTOLogisticService()).SubscribeData(new List<string> { code.LogisticCode });
            if (result.Key == false)
                throw new Exception($"{code.LogisticCode}������ͨ�����켣��Ϣʧ�ܣ�{result.Value}");
        }

        /// <summary>
        /// ����Բͨ�����켣��Ϣ
        /// </summary>
        /// <param name="code"></param>
        private void YTOSubscribe(LogisticCodes code)
        {
            if (code.ShipperCode != "YTO")
                return;

            if (string.IsNullOrEmpty(code.LogisticCode))
                throw new ArgumentException("����������Ϣ����Ϊ��");
            if (string.IsNullOrEmpty(code.ShipperCode))
                throw new ArgumentException("��ݹ�˾���벻��Ϊ��");

            var result = (new NewYTOLogisticService()).SubscribeData(code.LogisticCode);
            if (result.Key == false)
                throw new Exception($"{code.LogisticCode}����Բͨ�����켣��Ϣʧ�ܣ�{result.Value}");
        }

        /// <summary>
        /// ���İ�����������켣��Ϣ
        /// </summary>
        /// <param name="code"></param>
        private void HTKYSubscribe(LogisticCodes code)
        {
            if (code.ShipperCode != "HTKY")
                return;

            if (string.IsNullOrEmpty(code.LogisticCode))
                throw new ArgumentException("����������Ϣ����Ϊ��");
            if (string.IsNullOrEmpty(code.ShipperCode))
                throw new ArgumentException("��ݹ�˾���벻��Ϊ��");

            var order = new Order()
            {
                ToName = code.Receiver?.Name.CutString(255),
                ToMobile = code.Receiver?.Mobile.CutString(255),
                ToProvince = code.Receiver?.Province.CutString(255),
                ToCity = code.Receiver?.City.CutString(255),
                ToCounty = code.Receiver?.County.CutString(255),
                ToAddress = code.Receiver?.Street.CutString(255),
                SenderAddress = (code.Sender?.Province + code.Sender?.City + code.Sender?.County + code.Sender?.Street).CutString(255),
                SenderName = code.Sender?.Name.CutString(255)
            };

            var result = (new HTKYLogisticService()).SubscribeData(code.LogisticCode, order);
            if (result.Key == false)
                throw new Exception($"{code.LogisticCode}���İ�����������켣��Ϣʧ�ܣ�{result.Value}");
        }

        /// <summary>
        /// ���ĵ°��������켣��Ϣ
        /// </summary>
        /// <param name="code"></param>
        private void DBKDSubscribe(LogisticCodes code)
        {
            if (code.ShipperCode != "DBKD")
                return;

            if (string.IsNullOrEmpty(code.LogisticCode))
                throw new ArgumentException("����������Ϣ����Ϊ��");
            if (string.IsNullOrEmpty(code.ShipperCode))
                throw new ArgumentException("��ݹ�˾���벻��Ϊ��");

            var result = (new DBKDLogisticService()).StandTraceSubscribe(code.LogisticCode);
            if (result.Item1 == false)
                throw new Exception($"{code.LogisticCode}���ĵ°��������켣��Ϣʧ�ܣ�{result.Item2}");
        }

        /// <summary>
        /// �������ʿ�������켣��Ϣ
        /// </summary>
        /// <param name="code"></param>
        private void ZYKDSubscribe(LogisticCodes code)
        {
            if (code.ShipperCode != "ZYKD")
                return;

            if (string.IsNullOrEmpty(code.LogisticCode))
                throw new ArgumentException("����������Ϣ����Ϊ��");
            if (string.IsNullOrEmpty(code.ShipperCode))
                throw new ArgumentException("��ݹ�˾���벻��Ϊ��");

            var result = (new ZYKDLogisticService()).OrderTracesSubscribe(code.LogisticCode);
            if (result.Key == false)
                throw new Exception($"{code.LogisticCode}�������ʿ�������켣��Ϣʧ�ܣ�{result.Value}");
        }

        /// <summary>
        /// ��ӻ���������켣��Ϣ
        /// </summary>
        /// <param name="trace"></param>
        private void TryAdd(LogisticTraces trace, bool isContainAllTraces = true)
        {
            if (trace == null || string.IsNullOrEmpty(trace.SearchKey))
                return;
            var model = _repository.GetByKey(trace.SearchKey);
            //���Ѵ�������������켣��code��Ϣ��������������ӣ�������code��Ϣ
            var isfirstTrace = false;
            if (model == null)
            {
                //��ӹ켣��Ϣ
                trace.lastModified = DateTime.Now;
                _repository.Add(trace);
                model = trace;
                isfirstTrace = true;
            }
            else
            {
                model.Content = trace.Content;
                model.Success = trace.Success;
                model.Status = trace.Status;
                model.lastModified = DateTime.Now;
                if (isContainAllTraces == false)
                {
                    var current = trace.Traces?.FirstOrDefault();
                    if (current != null)
                    {
                        var old = model.Traces?.FirstOrDefault(t => t.Time == current.Time.ToUniversalTime());
                        if (old == null)
                        {
                            model.Traces.Add(current);
                            //var traces = model.Traces.OrderBy(t => t.Time).ToList();
                            //model.Traces = traces;
                            model.OriginalResponse += "," + trace.OriginalResponse;
                            _repository.Update(model);
                        }
                        else
                            //û�и���ֱ�ӷ��أ�����������
                            return;
                    }
                }
                else
                {
                    model.Traces = trace.Traces;
                    _repository.Update(model);
                }
            }
            //����code��Ϣ
            var codes = _codesRepository.FindCodes(model.ShipperCode, model.LogisticCode);
            if (codes != null && codes.Any())
            {
                codes.ForEach(c =>
                {
                    c.LastGetTime = DateTime.Now;
                    c.TraceContent = model.Content;
                    c.TraceStatus = TraceStatusType.Tracking;
                    c.Status = model.Status;
                    if (isfirstTrace)
                        c.FirstTraceTime = DateTime.Now; //��һ�εĹ켣ʱ��
                    c.HasGot = model.HasGot;
                    c.HasSigned = model.HasSigned;
                    c.TraceCount = model.TraceCount;
                    //TODO:����TraceStatus������Ϊ���
                    _codesRepository.Update(c);
                    ThreadPool.QueueUserWorkItem(state =>
                    {
                        var temp = state as LogisticCodes;
                        try
                        {
                            PushToApp(temp);
                        }
                        catch (Exception ex)
                        {
                            Utility.Log.WriteError($"���������켣���ĵ���������ʱ��������{ex}");
                            new Data.Repository.ShopRepository().WriteLogisticsConsumerLog(temp.ToJson(), ex.ToString(), "PushTraceToApp");
                        }
                    }, c);
                });
            }
        }

        /// <summary>
        /// ���Ͷ��ĵ������
        /// </summary>
        /// <param name="code"></param>
        private void PushToKuaidiNiao(LogisticCodes code)
        {
            if (code == null)
                throw new ArgumentException("����code����Ϊ��");
            if (string.IsNullOrEmpty(code.LogisticCode))
                throw new ArgumentException("����������Ϣ����Ϊ��");
            if (string.IsNullOrEmpty(code.ShipperCode))
                throw new ArgumentException("��ݹ�˾���벻��Ϊ��");
            //ֻ��˳���ӡ�ģ�������֧�ֻ�ȡ�����켣
            if (code.LogisticCode != "SF")
                return;
            ThreadPool.QueueUserWorkItem(state =>
            {
                try
                {
                    var kdn = new LogisticService.KuaiDiNiaoLogisticService();
                    var result = kdn.OrderTracesSubscribeV2(new KuaiDiNiaoOrderTracesSubRequestModel
                    {
                        ShipperCode = code.ThirdShipperCode,
                        LogisticCode = code.LogisticCode
                    });
                    if (!result.Item1)
                        throw new Exception($"���������켣��Ϣʧ�ܣ�{result.Item2}");
                }
                catch (Exception ex)
                {
                    Utility.Log.WriteError($"�˵�����Ϣ��{code?.ToJson()} �쳣��Ϣ��{ex}");
                }
            });
        }

        /// <summary>
        /// ��ѯ�˵��Ź켣��Ϣ
        /// </summary>
        /// <param name="requests"></param>
        /// <returns></returns>
        public List<LogisticCodeQueryResponse> Query(List<LogisticCodeQueryRequest> requests)
        {
            var rsps = new List<LogisticCodeQueryResponse>();
            //�Ȳ�ѯ�˵�����Ϣ
            var codes = _codesRepository.GetByKey(requests.Select(t => $"{_app.AppKey}-{t.ShipperCode}-{t.LogisticCode}").ToList());
            var traces = new List<LogisticTraces>();
            //�ٲ�ѯ�켣��Ϣ
            if (codes != null)
            {
                var tKeys = codes.Select(t => $"{t.ShipperCode}-{t.LogisticCode}").ToList();
                traces = _repository.GetByKey(tKeys);
            }
            requests.ForEach(req =>
            {
                var code = codes.FirstOrDefault(c => c.LogisticCode == req.LogisticCode && c.ShipperCode == req.ShipperCode);
                var trace = traces.FirstOrDefault(c => c.LogisticCode == req.LogisticCode && c.ShipperCode == req.ShipperCode);
                var rsp = new LogisticCodeQueryResponse
                {
                    ShipperCode = req.ShipperCode,
                    LogisticCode = req.LogisticCode,
                    PlatformType = code?.PlatformType,
                    OwnerId = code?.OwnerId,
                    State = code?.State,
                    IsSuccess = true
                };
                if (trace != null)
                {
                    rsp.Trace = new LogisticCodeQueryTraceResponse
                    {
                        Content = trace?.Content,
                        Status = trace?.Status,
                        Traces = trace?.Traces?.Select(t => new TraceStep
                        {
                            Time = t.Time,
                            Content = t.Content,
                            Location = t.Location,
                            Status = t.Status,
                            Remark = t.Remark
                        })?.ToList()
                    };
                }
                if (code == null)
                {
                    rsp.IsSuccess = false;
                    rsp.ErrorMessage = "û�в�ѯ�����˵��Ŷ�����Ϣ";
                }
                else if (trace == null)
                {
                    rsp.ErrorMessage = "���޹켣��Ϣ";
                }
                rsps.Add(rsp);
            });
            return rsps;
        }

        public void PushToApp(LogisticCodes code)
        {
            if (code == null)
                return;
            if (string.IsNullOrEmpty(code.AppKey))
                return;
            var app = new LogisticsAppInfoService().GetByAppKey(code.AppKey);
            if (app == null || string.IsNullOrEmpty(app.PushUrl))
                return;
            //Ĭ��ʹ��1688���������ӣ�ƴ���;����е�������������
            var pushUrl = app.PushUrl;
            if (app.PushUrls != null && app.PushUrls.Any())
            {
                var url = app.PushUrls.FirstOrDefault(p => string.Equals(p.PlatformType, code.PlatformType, StringComparison.OrdinalIgnoreCase))?.PushUrl;
                if (!string.IsNullOrEmpty(url))
                    pushUrl = url;
            }
            if (string.IsNullOrEmpty(pushUrl))
                return;
            //Ŀǰ�����͵�ƴ����վ��
            //ƴ�����������temp.State�жϵ��̵�PushUrl
            //if (code.PlatformType == PlatformType.Pinduoduo.ToString())
            //{
            //    var shopId = code.State.ToInt();
            //    if(shopId>0)
            //    {
            //        var shop = SystemApiQueueService.GetShopFromCache(shopId.ToString(), PlatformType.Pinduoduo.ToString());
            //        if (shop == null)
            //            return;
            //        var dbconfig = shop.DbConfig;
            //        //���Ƕ�����ϵ�ƴ��������Ҫ���͵����������
            //        if(dbconfig?.DbServer.Location != PlatformType.Pinduoduo.ToString())
            //        {
            //            pushUrl = app.PushUrl;
            //        }
            //    }
            //}
            var model = Query(new List<LogisticCodeQueryRequest> {
                new LogisticCodeQueryRequest
                {
                    LogisticCode = code.LogisticCode,
                    ShipperCode = code.ShipperCode
                }
            });
            if (model == null || !model.Any())
                return;
            var request = model.ToJson();
            var rsp = "";
            try
            {
                rsp = Utility.Net.WebRequestHelper.PostJsonRequest(pushUrl, request);
                Utility.Log.Debug($"���������켣��Ӧ��ʱ��Ӧ��Key:{app.AppKey}���������ӣ�{pushUrl}���������ݣ�{request} ���������ݣ�{rsp}");
            }
            catch (Exception ex)
            {
                Utility.Log.WriteError($"���������켣��Ӧ��ʱ��������Ӧ��Key:{app.AppKey}���������ӣ�{pushUrl}���������ݣ�{request} ���������ݣ�{rsp}��������Ϣ��{ex}");
            }
        }

        public List<LogisticCodeQueryResponse> QueryByTime(LogisticCodeIncrimentQueryRequest request)
        {
            var rsps = new List<LogisticCodeQueryResponse>();
            var codes = _codesRepository.GetByTime(request);
            if (codes == null || codes.Any() == false)
                return rsps;
            var tKeys = codes.Select(t => $"{t.ShipperCode}-{t.LogisticCode}").ToList();
            var traces = _repository.GetByKey(tKeys);
            codes.ForEach(code =>
            {
                var trace = traces.FirstOrDefault(c => c.LogisticCode == code.LogisticCode && c.ShipperCode == code.ShipperCode);
                var rsp = new LogisticCodeQueryResponse
                {
                    ShipperCode = code.ShipperCode,
                    LogisticCode = code.LogisticCode,
                    PlatformType = code?.PlatformType,
                    OwnerId = code?.OwnerId,
                    State = code?.State,
                    IsSuccess = true
                };
                if (trace != null)
                {
                    rsp.Trace = new LogisticCodeQueryTraceResponse
                    {
                        Content = trace?.Content,
                        Status = trace?.Status,
                        Traces = trace?.Traces?.Select(t => new TraceStep
                        {
                            Time = t.Time,
                            Content = t.Content,
                            Location = t.Location,
                            Status = t.Status,
                            Remark = t.Remark
                        })?.ToList()
                    };
                }
                if (code == null)
                {
                    rsp.IsSuccess = false;
                    rsp.ErrorMessage = "û�в�ѯ�����˵��Ŷ�����Ϣ";
                }
                else if (trace == null)
                {
                    rsp.ErrorMessage = "���޹켣��Ϣ";
                }
                rsps.Add(rsp);
            });
            return rsps;
        }
    }
}
