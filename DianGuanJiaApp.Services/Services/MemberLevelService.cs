using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Data.Repository.SupplierProduct;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.Services.SettingsService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Services.Services
{
    public class MemberLevelService : BaseService<MemberLevel>
    {
        private readonly MemberLevelRepository _repository = new MemberLevelRepository(); 
        private readonly SupplierUserRepository _supplierUserRepository = new SupplierUserRepository();

        /// <summary>
        /// 最终结算价修正规则设置键
        /// </summary>
        private const string FinalPriceRuleKey = "/FenFa/System/Config/FinalDistributePriceCorrectRule";

        /// <summary>
        /// 获取会员等级列表
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="isCalculatePrice"></param>
        /// <param name="price"></param>
        /// <returns></returns>
        public List<MemberLevelViewModel> LoadMemberList(int fxUserId, bool isCalculatePrice = false, decimal? price = null)
        {
            // 先获取该用户的会员等级列表
            var memberLevels = _repository.GetListByFxUserId(fxUserId);
            
            if (memberLevels.Count == 0)
            {
                // 如果没有会员等级，生成默认6个会员等级
                memberLevels = GetMemberLevels(fxUserId);
            }
            
            // 新增一个默认会员等级
            var defaultMemberLevel = new MemberLevel
            {
                Name = "默认等级",
                Level = 0,
                PriceRule = new RuleModel
                {
                    Operator = "*",
                    Variate = 100,
                    Unit = "%"
                }.ToJson(),
                Switch = false,
                MemberLevelCode = null,
                FxUserId = fxUserId,
                IsCustom = false,
                Status = 1
            };
            memberLevels.Insert(0, defaultMemberLevel);
            
            var result = memberLevels.Select(memberLevel => new MemberLevelViewModel(memberLevel)).ToList();
            
            const string fields = "t1.Id,t1.FxUserId,t1.Status,t1.IsTop,t1.SupplierType,t1.Remark,t1.RemarkName,t1.MemberLevelCode";
            var status = new List<AgentBingSupplierStatus> { AgentBingSupplierStatus.Binded, AgentBingSupplierStatus.UnBinding, AgentBingSupplierStatus.UnBindFail };
            var agentList = _supplierUserRepository.GetAgentList(fxUserId, fields: fields, status: status, isIgnoreHideCancelUserSetting: true);
            // 根据等级分组，统计每个等级的人数
            var groupByLevel = agentList.GroupBy(x => x.MemberLevelCode).ToList();
            foreach (var memberLevel in result)
            {
                var count = groupByLevel.FirstOrDefault(x => x.Key == memberLevel.MemberLevelCode)?.Count() ?? 0;
                memberLevel.Count = count;
            }

            var finalPriceRule = FinalPriceRule(fxUserId, SiteContext.Current.CurrentShopId);
            result.ForEach(x => x.FinalPriceRule = finalPriceRule);
            if (!isCalculatePrice || price == null) return result;
            
            // 如果需要计算价格，将价格计算后返回
            foreach (var memberLevel in result)
            {
                var (newPrice, isChanged) = DistributePriceChange(price, memberLevel.PriceRule, finalPriceRule);
                if (isChanged)
                {
                    memberLevel.Price = newPrice;
                }
            }

            return result;
        }
        
        /// <summary>
        /// 生成默认6个会员等级
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<MemberLevel> GetMemberLevels(int fxUserId)
        {
            var result = new List<MemberLevel>();
            var rule = new RuleModel
            {
                Operator = "*",
                Variate = 100,
                Unit = "%"
            }.ToJson();

            for (var i = 0; i < 6; i++)
            {
                var level = i + 1;
 
                // 生成默认基本会员等级
                var defaultMemberLevel = new MemberLevel
                {
                    Name = "分销等级V" + level,
                    Level = level,
                    PriceRule = rule,
                    Switch = true,
                    MemberLevelCode = Guid.NewGuid().ToString().ToShortMd5(),
                    FxUserId = fxUserId,
                    IsCustom = false,
                    Status = 1
                }; 
                result.Add(defaultMemberLevel);
            }
            
            // 初始化最多6条
            result.ForEach(x => _repository.Add(x));
            return result;
        }

        /// <summary>
        /// 加载分销商列表含会员等级
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="memberLevelCode">会员等级唯一Code</param>
        /// <param name="isEqual">是否与Code相等</param>
        /// <returns></returns>
        public List<SupplierUser> LoadAgentList(int fxUserId, string memberLevelCode, bool isEqual = false)
        {
            const string fields = "t1.Id,t1.FxUserId,t1.Status,t1.IsTop,t1.SupplierType,t1.Remark,t1.RemarkName,t1.MemberLevelCode";
            var status = new List<AgentBingSupplierStatus> { AgentBingSupplierStatus.Binded, AgentBingSupplierStatus.UnBinding, AgentBingSupplierStatus.UnBindFail };
            var agentList = _supplierUserRepository.GetAgentList(fxUserId, fields: fields, status: status, isIgnoreHideCancelUserSetting: true,needEncryptAccount:true);
            
            // 获取所有的MemberLevelCode
            var memberLevelCodes = agentList
                .Where(x => x.MemberLevelCode != null)
                .Select(x => x.MemberLevelCode)
                .Distinct().ToList();
            // 获取所有的会员等级
            var memberLevels = _repository.GetListByCodes(memberLevelCodes);
            
            // 将会员等级信息合并到代理商列表中
            foreach (var agent in agentList)
            {
                var memberLevel = memberLevels.FirstOrDefault(x => x.MemberLevelCode == agent.MemberLevelCode);
                if (memberLevel != null)
                {
                    agent.MemberLevel = memberLevel.Level;
                }
            }
            
            agentList = isEqual
                ? agentList.Where(x => x.MemberLevelCode == memberLevelCode).ToList()
                : agentList.Where(x => x.MemberLevelCode != memberLevelCode).ToList();
            // 按等级排序
            agentList = agentList.OrderBy(x => x.MemberLevel).ToList();
            agentList.ForEach(x => x.UserName = string.IsNullOrEmpty(x.Remark) ? x.UserName : $"{x.Mobile}({x.Remark})");
        
            return agentList;
        }

        /// <summary>
        /// 更新分销商会员等级
        /// </summary>
        /// <param name="model"></param>
        /// <param name="fxUserId"></param>
        public int UpdateAgentMemberLevel(UpdateAgentMemberLevelModel model, int fxUserId)
        {
            // 更新业务库
            var result = _supplierUserRepository.UpdateMemberLevel(fxUserId, model.MemberLevelCode, model.AgentIds, model.IsCancel);
            // 更新货盘库
            new UserSupplierStatusRepository().UpdateMemberLevel(fxUserId, model.MemberLevelCode, model.AgentIds, model.IsCancel);
            
            return result;
        }
            
        /// <summary>
        /// 获取最终结算价修正规则
        /// </summary>
        public static FinalDistributePriceCorrectRule FinalPriceRule(int fxUserId, int fxShopId)
        {
            if (fxUserId == 0 && fxShopId == 0) return new FinalDistributePriceCorrectRule();

            if (fxShopId == 0)
            {
                fxShopId = new UserFxRepository().GetSystemShops(fxUserId)?.Id ?? 0;
                if (fxShopId == 0) return new FinalDistributePriceCorrectRule();
            }

            var commonSetting = new CommonSettingRepository().Get(FinalPriceRuleKey, fxShopId);
            var model = commonSetting?.Value?.ToObject<FinalDistributePriceCorrectRule>();
            return model ?? new FinalDistributePriceCorrectRule();
        } 

        /// <summary>
        /// 根据运算规则换算价格
        /// </summary>
        /// <param name="price"></param>
        /// <param name="rule"></param>
        /// <param name="correctRule"></param>
        /// <returns></returns>
        public static (decimal? newPrice, bool isChanged) DistributePriceChange(decimal? price, RuleModel rule, FinalDistributePriceCorrectRule correctRule)
        {
            // 如果价格为 null 或价格小于等于 0，保持不变，返回 false 表示没有变化
            if (price == null || price <= 0) return (price, false);

            // Step 1: 根据RuleModel执行基本价格运算
            decimal? newPrice;

            // Unit 除了 "%"，其他不参与计算
            switch (rule.Operator)
            {
                case "+": newPrice = rule.Unit == "%" ? price * (1 + rule.Variate / 100) : price + rule.Variate;
                    break;
                case "-": newPrice = rule.Unit == "%" ? price * (1 - rule.Variate / 100) : price - rule.Variate;
                    break;
                case "*": newPrice = rule.Unit == "%" ? price * (rule.Variate / 100) : price * rule.Variate;
                    break;
                // 无效操作符，返回原价格和 false
                default: return (price, false);  
            }

            // Step 2: 根据FinalDistributePriceCorrectRule进行小数位处理
            switch (correctRule)
            {
                case FinalDistributePriceCorrectRule.RoundToWholeNumber:
                    // 四舍五入到整数
                    newPrice = Math.Round(newPrice.Value, 0, MidpointRounding.AwayFromZero);
                    break;
                case FinalDistributePriceCorrectRule.RoundToTwoDecimals:
                    // 四舍五入保留两位小数
                    newPrice = Math.Round(newPrice.Value, 2, MidpointRounding.AwayFromZero);
                    break;
                // 无效的舍入规则，返回原价格和 false
                default: return (price, false);  
            }

            // Step 3: 检查计算后的新价格是否为负数，如果是负数或者为0，返回原价格
            if (newPrice <= 0) return (price, false);  

            // 如果新价格与旧价格不同，返回变化的价格和 true
            return newPrice != price ? 
                (newPrice, true) :
                (price, true); // 如果没有变化，可能存在默认价格，返回原价格和 true
        }

        /// <summary>
        /// 设置开关
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="memberLevelCode"></param>
        /// <param name="switchValue"></param>
        /// <returns></returns>
        public bool SetSwitch(int fxUserId, string memberLevelCode, bool switchValue)
        {
            var memberLevel = _repository.GetListByFxUserId(fxUserId).FirstOrDefault(x => x.MemberLevelCode == memberLevelCode);
            if (memberLevel == null) return false;

            memberLevel.Switch = switchValue;
            return _repository.Update(memberLevel);
        }

        /// <summary>
        /// 设置价格规则
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="modelMemberLevelCode"></param>
        /// <param name="modelPriceRule"></param>
        /// <param name="isAll"></param>
        /// <returns></returns>
        public string SetPriceRule(int fxUserId, string modelMemberLevelCode, RuleModel modelPriceRule, bool isAll)
        {
            var memberLevel = _repository.GetListByFxUserId(fxUserId).FirstOrDefault(x => x.MemberLevelCode == modelMemberLevelCode);
            if (memberLevel == null) return null;
            // 如果价格规则发生变化，才更新
            if (memberLevel.PriceRule == modelPriceRule.ToJson() && isAll == false) return null;

            memberLevel.PriceRule = modelPriceRule.ToJson();
            memberLevel.UpdateTime = DateTime.Now;
            memberLevel.Switch = true;
            var result = _repository.Update(memberLevel);
            return result ? memberLevel.MemberLevelCode : null;
        }

        /// <summary>
        /// 获取对应分销商的换算规则
        /// </summary>
        /// <param name="agentIds"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public Dictionary<int, RuleModel> GetMemberLevelList(List<int> agentIds, int fxUserId)
        {
            if (agentIds == null || agentIds.Count == 0) return new Dictionary<int, RuleModel>();
            var result = _repository.GetAgentRuleModel(fxUserId, agentIds);
            // 补全不存在的商家，使用默认规则
            if (result.Count != agentIds.Count)
            {
                var diff = agentIds.Except(result.Keys).ToList();
                diff.ForEach(x => result.Add(x, new RuleModel
                {
                    Operator = "*",
                    Variate = 100,
                    Unit = "%"
                }));
            }
            return result;
        }
        
        /// <summary>
        /// 获取厂家对自己的规则模型
        /// </summary>
        /// <param name="supplierIds"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public Dictionary<int, SupplierRuleModel> GetSupplierRuleModel(List<int> supplierIds, int fxUserId)
        {
            var supplierRuleDic = _repository.GetSupplierRuleModel(fxUserId, supplierIds);
            return supplierRuleDic.ToDictionary(x => x.Key, x => new SupplierRuleModel
            {
                PriceRule = x.Value,
                FinalDistributePriceCorrectRule = FinalPriceRule(x.Key, 0)
            });
        }
        
        /// <summary>
        /// 获取对应分销商的会员等级
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="agentIds"></param>
        /// <returns></returns>
        public List<MemberLevelSupplierViewModel> GetDicByAgentIds(int fxUserId, List<int> agentIds)
        {
            var loadAgentList = LoadAgentList(fxUserId, null);
            // 按筛选agentIds
            loadAgentList = loadAgentList.Where(x => agentIds.Contains(x.FxUserId)).ToList();
            // 按等级分组
            var groupByLevel = loadAgentList.GroupBy(x => x.MemberLevel).ToList()
                .ToDictionary(x => x.Key, x => x.ToList());
            // 去除等级为0的
            groupByLevel.Remove(0);

            return (from item in groupByLevel
                let userNames = item.Value.Select(x => x.UserName).ToList()
                select new MemberLevelSupplierViewModel(item.Key, userNames)).ToList();
        }

        /// <summary>
        /// 检查Code是否存在
        /// </summary>
        /// <param name="modelMemberLevelCode"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool CheckCode(string modelMemberLevelCode, int fxUserId)
        {
            return _repository.CheckCode(modelMemberLevelCode, fxUserId);
        }
        
        /// <summary>
        /// 更新已关联的历史结算价
        /// </summary>
        /// <param name="model"></param>
        /// <param name="fxUserId"></param>
        public void UpdatePriceHistory(UpdateAgentMemberLevelModel model, int fxUserId)
        {
            // 获取AgentIds在精选下的所有库
            var agentIds = model.AgentIds;
            agentIds.Add(fxUserId);
            agentIds = agentIds.Distinct().ToList();
            var cloudPlatformTypeList = new List<string> { "Alibaba" };
            var dbConfigList = new DbConfigRepository().GetListByFxUserIds(agentIds, cloudPlatformTypeList);
            if (dbConfigList == null || dbConfigList.Count == 0) return;
            var agentDic = new Dictionary<string, List<int>>();
            var hashSetDbConn = new HashSet<string>();
            var shopId = SiteContext.Current.CurrentShopId;
            
            // 按照数据库配置新库的优先级排序
            dbConfigList = dbConfigList.OrderByDescending(x => x.FromFxDbConfig).ToList();
            
            // 找到自己的新库
            var selfDbConfig = dbConfigList.FirstOrDefault(x => x.FromFxDbConfig == 1 && x.DbConfig.UserId == fxUserId);
            
            // 用户维度循环
            // 判断用户所属库为新库或旧库
            agentIds.ForEach(id =>
            {
                var dbConfig = dbConfigList.FirstOrDefault(x => x.DbConfig.UserId == id);
                if (dbConfig == null || id == fxUserId) return;
      
                // 若为新库，则取自己的新库进行处理
                // 若为旧库，则取用户的旧库处理
                var isOldDb = dbConfig.FromFxDbConfig == 0;
                var dbConn = isOldDb ? dbConfig.ConnectionString : selfDbConfig?.ConnectionString;
                if (string.IsNullOrEmpty(dbConn)) return;
                agentDic.TryGetValue(dbConn, out var agentIdsByDbConn);
                if (agentIdsByDbConn == null)
                {
                    agentIdsByDbConn = new List<int> { id };
                    agentDic.Add(dbConn, agentIdsByDbConn);
                    hashSetDbConn.Add(dbConn);
                }
                else agentIdsByDbConn.Add(id);
            });
            
            // 先获取最终结算价修正规则
            var finalPriceRule = FinalPriceRule(0, SiteContext.Current.CurrentShopId);
            // 获取对应分销商用户的换算规则
            var memberLevelDict = GetMemberLevelList(agentIds, fxUserId);

            var updateModel = new SettlementPriceMsgModel
            {
                AgentIds = agentIds,
                FxUserId = fxUserId,
                ShopId = shopId,
                MemberLevelRule = memberLevelDict,
                FinalPriceRule = finalPriceRule,
                IsCancel = model.IsCancel
            };
            
            // 遍历DbConn
            foreach (var conn in hashSetDbConn)
            {
                agentDic.TryGetValue(conn, out var agentIdsByDbConn);
                if (agentIdsByDbConn == null || agentIdsByDbConn.Count == 0) continue;
                var tempModel = new SettlementPriceMsgModel
                {
                    AgentIds = agentIdsByDbConn,
                    FxUserId = fxUserId,
                    ShopId = shopId,
                    MemberLevelRule = memberLevelDict,
                    FinalPriceRule = finalPriceRule,
                    IsCancel = model.IsCancel
                };
                // 处理精选平台的数据
                UpdatePriceHistoryByBaseProduct(tempModel, conn);
            }
            DataPreBy1688(updateModel);

            var msgList = new List<MessageRecord>();
            var count = Enum.GetNames(typeof(CloudPlatformType)).Length;
            for (var i = 0; i < count; i++)
            {
                // 遍历云平台
                var cloudPlatformType = ((CloudPlatformType)i).ToString();
                // 跳过精选，前面已处理
                if (cloudPlatformType == CloudPlatformType.Alibaba.ToString()) continue;
                // 发消息到各平台处理
                var msgModel = new MessageRecord
                {
                    BusinessId = Guid.NewGuid().ToString().ToShortMd5(),
                    CreateFxUserId = fxUserId,
                    CreateTime = DateTime.Now,
                    DataJson = updateModel.ToJson(),
                    MsgType = BaseProductMsgType.MemberSettlementPriceChange,
                    FxUserId = fxUserId,
                    SourceCloud = CustomerConfig.CloudPlatformType,
                    TargetCloud = cloudPlatformType,
                    ProductPlatformType = cloudPlatformType
                }; 
                
                msgList.Add(msgModel);
            }
            Log.Debug(() => $"变更等级，更新历史结算价消息：{msgList.ToJson()}");
           
            var result = new MessageRecordService().SendBusinessMessage(msgList);
            Log.Debug(() => $"变更等级，更新历史结算价消息发送结果：{result}");
        }

        /// <summary>
        /// 1688相关数据预处理
        /// </summary>
        /// <param name="updateModel"></param>
        private void DataPreBy1688(SettlementPriceMsgModel updateModel)
        {
            // 先获取对应分销商已关联的商品数据
            var infos = new DistributorProductSkuMappingRepository().GetListByAgentIds(updateModel.AgentIds, updateModel.FxUserId);
            // 获取被关联的商品SkuCode
            var upSkuCodes = infos.Select(x => x.UpSkuCode).Distinct().ToList();
            // 获取对应的默认结算价（即页面上的统一采购价）
            var skus = new ProductSkuFxService().GetListBySkuCode(upSkuCodes);
            // 生成对应的价格映射
            var skuPriceDic = skus.ToDictionary(x => x.SkuCode, x => x.DefaultSettlementPrice);
            // 获取下游所有的SKuCode，按平台划分
            var skuDic = new Dictionary<string, List<DistributeSkuMappingModel>>();
            infos.ForEach(info =>
            {
                var cloudType = CommUtls.GetPlatformCloudByType(info.DownPlatformType);
                skuDic.TryGetValue(cloudType, out var skuList);
                skuPriceDic.TryGetValue(info.UpSkuCode, out var price);
                var temp = new DistributeSkuMappingModel
                {
                    AgentId = info.DownFxUserId,
                    ProductCode = info.DownProductCode,
                    SkuCode = info.DownSkuCode,
                    Price = price
                };
                if (skuList == null)
                {
                    skuList = new List<DistributeSkuMappingModel> { temp };
                    skuDic.Add(cloudType, skuList);
                }
                else skuList.Add(temp);
            });

            updateModel.SkuPriceDicBy1688 = skuPriceDic;
            updateModel.DownSkuDic = skuDic;
            // 精选平台调用
            UpdatePriceHistoryBy1688(updateModel);
        }

        /// <summary>
        /// 根据1688商品关联关系更新商品结算价 
        /// </summary>
        /// <param name="updateModel"></param>
        public void UpdatePriceHistoryBy1688(SettlementPriceMsgModel updateModel)
        {
            if (updateModel.DownSkuDic == null || updateModel.DownSkuDic.Count == 0) return;
            var logFileName = $"MemberSettlementPriceChange-{DateTime.Now.Format("yyyyMMdd")}.txt";
            // 当前云平台类型
            var cloudType = CustomerConfig.CloudPlatformType;
            // 获取对应云平台的数据
            updateModel.DownSkuDic.TryGetValue(cloudType, out var skuList);
            if (skuList == null || skuList.Count == 0) return;

            var fxUserId = updateModel.FxUserId;
            var shopId = updateModel.ShopId;
            var now = DateTime.Now;
            
            var allPriceModels = new List<ProductSettlementPrice>();  
            skuList.ForEach(sku =>
            {
                // 找到对应的换算规则
                updateModel.MemberLevelRule.TryGetValue(sku.AgentId, out var rule);
                if (rule == null && sku.Price > 0) return;
                
                var info = new ProductSettlementPrice
                {
                    ProductCode = sku.ProductCode,
                    ProductSkuCode = sku.SkuCode,
                    ShopId = shopId,
                    CreateUser = fxUserId,
                    FxUserId = sku.AgentId,
                    SettlementType = FinancialSettlementRepository.SettlementType.Manufacturer.ToInt(),
                    PlatformType = CustomerConfig.CloudPlatformType,
                    Price = sku.Price ?? 0,
                    CreateTime = now,
                    UpdateTime = now
                };
                var (newPrice, isChanged) = DistributePriceChange(sku.Price, rule, updateModel.FinalPriceRule);
                if (!isChanged) return;
                info.Price = newPrice ?? info.Price;

                allPriceModels.Add(info);
                Log.Debug(() => $"更新等级分销价换算后的结算价：{info.ToJson()}", logFileName); 
            });
            if (allPriceModels.Any())
                new FinancialSettlementService().SetProductSettlementPrice(allPriceModels, fxUserId,
                    changeType: FinancialSettlementRepository.ChangeType.MemberChange.ToInt(),
                    needSetIdFromDb: true); 
        }

        /// <summary>
        /// 根据基础商品关联关系更新商品结算价
        /// </summary>
        /// <param name="updateModel"></param>
        /// <param name="dbConn"></param>
        public void UpdatePriceHistoryByBaseProduct(SettlementPriceMsgModel updateModel, string dbConn = null)
        {
            var logFileName = $"MemberSettlementPriceChange-{DateTime.Now.Format("yyyyMMdd")}.txt";
            var agentIds = updateModel.AgentIds;
            var shopId = updateModel.ShopId;
            var fxUserId = updateModel.FxUserId;
            var productRepository = dbConn == null ? new ProductRepository() : new ProductRepository(dbConn);
            var financialSettlementService = dbConn == null ? new FinancialSettlementService() : new FinancialSettlementService(dbConn);
            
            var infos = productRepository.GetInfoByAgentIds(agentIds, fxUserId);
            if (infos == null || infos.Count == 0) return;
            var now = DateTime.Now;
            
            // 获取所有的Uid
            var productUids = infos.Select(x => x.BaseProductSkuUid).Distinct().ToList();
            var baseProductSkus = new List<BaseProductSku>();
            
            // 获取所有的分销价
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                // 如果是精选平台直接获取
                baseProductSkus = new BaseProductSkuRepository().GetListBySkuUids(productUids, fxUserId);
            }
            // 非精选平台跨云获取数据
            else
            {
                var model = new GetBaseProductSkuModel
                {
                    Uids = productUids,
                    FxUserId = fxUserId
                };
                try
                {
                    const string apiUrl = "/BaseProductApi/GetBaseSkuInfo";
                    var targetSiteUrl = CommUtls.GetTargetSiteUrl(apiUrl);
                    Log.Debug(() => $"跨云查询基础商品关联数据，targetSiteUrl={targetSiteUrl}，lastModel={model.ToJson()}");

                    baseProductSkus = WebCommon.PostFxSiteApi<GetBaseProductSkuModel, List<BaseProductSku>>(targetSiteUrl,
                        fxUserId, model, "跨云查询基础商品Sku信息", isEncrypt: true);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"跨云查询基础商品SKu数据：{ex.Message}", logFileName);
                } 
            }
            if (baseProductSkus.Any() == false) return;
            
            var allPriceModels = new List<ProductSettlementPrice>(); 
            // 构建结算价模型
            infos.ForEach(sku =>
            {
                var baseSku = baseProductSkus.FirstOrDefault(x => x.Uid == sku.BaseProductSkuUid);
                if (baseSku == null || baseSku.DistributePrice.HasValue == false || baseSku.DistributePrice.Value == 0) return;
                
                // 找到对应的换算规则
                updateModel.MemberLevelRule.TryGetValue(sku.UpFxUserId, out var rule);
                if (rule == null) return;
                
                var info = new ProductSettlementPrice
                {
                    ProductCode = sku.ProductCode,
                    ProductSkuCode = sku.SkuCode,
                    ShopId = shopId,
                    CreateUser = fxUserId,
                    FxUserId = sku.UpFxUserId,
                    SettlementType = FinancialSettlementRepository.SettlementType.Manufacturer.ToInt(),
                    PlatformType = CustomerConfig.CloudPlatformType,
                    Price = baseSku.DistributePrice ?? 0,
                    CreateTime = now,
                    UpdateTime = now
                };
                var (newPrice, isChanged) = DistributePriceChange(baseSku.DistributePrice, rule, updateModel.FinalPriceRule);
                if (!isChanged) return;
                info.Price = newPrice ?? info.Price;

                allPriceModels.Add(info);
                Log.Debug(() => $"更新等级分销价换算后的结算价：{info.ToJson()}", logFileName);
            });
                
            if (allPriceModels.Any())
                financialSettlementService.SetProductSettlementPrice(allPriceModels, fxUserId,
                    changeType: FinancialSettlementRepository.ChangeType.MemberChange.ToInt(),
                    needSetIdFromDb: true);
        }

        /// <summary>
        /// 保存价格规则
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="modelPriceRuleList"></param>
        public void SavePriceRule(int fxUserId, SavePriceRuleModel modelPriceRuleList)
        {
            if (modelPriceRuleList?.PriceRuleList == null || modelPriceRuleList.PriceRuleList.Count == 0) return;
            var priceRuleList = modelPriceRuleList.PriceRuleList;
            var memberLevels = _repository.GetListByFxUserId(fxUserId);
            if (memberLevels == null || memberLevels.Count == 0) return;

            var isAll = false;
            // 获取最终结算价修正规则
            var finalPriceRule = FinalPriceRule(fxUserId, SiteContext.Current.CurrentShopId);
            // 判断是否一致
            if (modelPriceRuleList.FinalDistributePriceCorrectRule != finalPriceRule)
            {
                // 更新最终结算价修正规则
                new BusinessSettingsService().SaveByCurrentUser(FinalPriceRuleKey,
                    modelPriceRuleList.FinalDistributePriceCorrectRule.ToInt().ToString());
                isAll = true;
            }
            
            // 更新价格规则
            var updatedMemberLevelCodes = priceRuleList.Where(priceRule => priceRule.MemberLevelCode != null)
                .Select(priceRule => SetPriceRule(fxUserId, priceRule.MemberLevelCode, priceRule.PriceRule, isAll))
                .Where(code => code != null).ToList();
     
            // 更新历史结算价
            if (modelPriceRuleList.IsUpdateHistory)
            {
                var needUpdateAgentIds = _supplierUserRepository.GetAgentIdByMemberCodes(fxUserId, updatedMemberLevelCodes);
                // 如果更新了最终结算价修正规则，所有的等级都需要更新
                if (isAll) needUpdateAgentIds.AddRange(LoadAgentList(fxUserId, null, true).Select(x => x.FxUserId).ToList());
                if (needUpdateAgentIds == null || needUpdateAgentIds.Count == 0) return;
                if (needUpdateAgentIds.Contains(fxUserId)) needUpdateAgentIds.Remove(fxUserId);
                needUpdateAgentIds = needUpdateAgentIds.Distinct().ToList();

                var historyModel = new UpdateAgentMemberLevelModel
                {
                    AgentIds = needUpdateAgentIds,
                    MemberLevelCode = null,
                    IsCancel = false
                };
                
                UpdatePriceHistory(historyModel, fxUserId);
            }
        }

        /// <summary>
        /// 计算价格
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<MemberLevelViewModel> CalculatePrice(SavePriceRuleModel model)
        {
            if (model.CalculatePrice == null) return null;
            if (model.PriceRuleList == null || model.PriceRuleList.Any() == false) return null;
            
            var result = new List<MemberLevelViewModel>();
            foreach (var priceRule in model.PriceRuleList)
            {
                var memberLevel = new MemberLevelViewModel
                {
                    Price = model.CalculatePrice.Value,
                    PriceRule = priceRule.PriceRule,
                    FinalPriceRule = model.FinalDistributePriceCorrectRule
                };
                var (newPrice, isChanged) = DistributePriceChange(model.CalculatePrice, priceRule.PriceRule, model.FinalDistributePriceCorrectRule);
                if (isChanged)
                {
                    memberLevel.Price = newPrice;
                }
                result.Add(memberLevel);
            }

            return result;
        }
    }
}