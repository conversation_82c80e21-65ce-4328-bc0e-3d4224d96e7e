using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Services.PlatformService;
using System.Data;
using Newtonsoft.Json.Linq;
using DianGuanJiaApp.Data.Enum;
using System.Collections.Concurrent;
using NPOI.SS.Formula.Functions;

namespace DianGuanJiaApp.Services
{
    /// <summary>
    /// 拿货小标签服务类
    /// </summary>
    public partial class NaHuoLabelService : BaseService<Data.Entity.NaHuoLabelBatch>
    {
        public NaHuoLabelService()
        {
            _orderRepository = new NaHuoLabelOrderRepository();
            _orderItemRepository = new NaHuoLabelOrderItemRepository();
            _batchRepository = new NaHuoLabelBatchRepository();
            _rulesRepository = new NaHuoLabelSortingRulesRepository();
            _tempRepository = new NaHuoLabelTemplateRepository();

            _commonSettingService = new CommonSettingService();
            _shopService = new ShopService();

        }
        public NaHuoLabelService(string connectionString)
        {
            _orderRepository = new NaHuoLabelOrderRepository(connectionString);
            _orderItemRepository = new NaHuoLabelOrderItemRepository(connectionString);
            _batchRepository = new NaHuoLabelBatchRepository(connectionString);
            _rulesRepository = new NaHuoLabelSortingRulesRepository(connectionString);
            _tempRepository = new NaHuoLabelTemplateRepository(connectionString);

            _commonSettingService = new CommonSettingService();
            _shopService = new ShopService();
        }

        #region 私有变量

        private NaHuoLabelOrderRepository _orderRepository = null;
        private NaHuoLabelOrderItemRepository _orderItemRepository = null;
        private NaHuoLabelBatchRepository _batchRepository = null;
        private NaHuoLabelSortingRulesRepository _rulesRepository = null;
        private NaHuoLabelTemplateRepository _tempRepository = null;

        private CommonSettingService _commonSettingService = null;
        private ShopService _shopService = null;

        private string _NaHuoLabelSerialNumberKey = "NaHuoLabelSerialNumber";
        private string _SetNaHuoLabelDataKey = "SetNaHuoLabelData";

        #endregion


        /// <summary>
        /// 根据模板Id获取模板信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public NaHuoLabelTemplate GetNaHuoTemplate(int id, int shopId)
        {
            return _tempRepository.GetNaHuoTemplate(id, shopId);
        }

        /// <summary>
        /// 根据订单编号和店铺ID获取当前批次的所有数据
        /// </summary>
        /// <param name="pids">订单编号</param>
        /// <param name="shopIds">店铺ID</param>
        /// <param name="isReturnAllBatchOrders">是否返回此批次的所有订单信息</param>
        /// <returns></returns>
        public List<NaHuoLabelBatch> GetNaHuoBatchInfos(List<string> pids, List<int> shopIds, bool isReturnAllBatchOrders = false)
        {
            return _orderRepository.GetNaHuoBatchInfos(pids, shopIds, isReturnAllBatchOrders);
        }


        public NaHuoLabelBatch GetNewBatch(int ShopId)
        {
            return _orderRepository.DbConnection.Query<NaHuoLabelBatch>("SELECT * FROM  P_NaHuoLabelBatch WITH(NOLOCK) where  ShopId=@ShopId  order by CreateTime desc ", new { ShopId = ShopId }).FirstOrDefault();
        }

        /// <summary>
        /// 当前批次拿货小标签信息批量插入数据库中
        /// </summary>
        /// <param name="batch">当前批次数据</param>
        /// <param name="rules">当前批次条码数据(已排序)</param>
        public void BulkInsertNaHuoLabelBatch(NaHuoLabelBatch batch, List<NaHuoLabelSortingRules> rules = null)
        {
            _batchRepository.BulkInsertNaHuoLabelBatch(batch, rules);
        }

        /// <summary>
        /// 扫描商品条码
        /// </summary>
        /// <returns></returns>
        public List<NaHuoLabelSortingRules> ScanProductByBarcode(string barcode)
        {
            var list = _rulesRepository.Get(barcode);

            return list;
        }


        /// <summary>
        /// 根据订单编号、子订单编号、店铺ID查询已打印过的小标签
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        public List<NaHuoLabelSortingRules> GetTunNaHuoLabelByKeys(List<OrderItemSelectKeyModel> keys)
        {
            return _rulesRepository.GetTunNaHuoLabelByKeys(keys);
        }

        public Tuple<List<string>, int, List<NaHuoLabelFullInfoModel>> GetListSortingRules(List<int> ShopIdList, string batchNo, int pageIndex, int pageSize)
        {
            return _rulesRepository.GetListSortingRules(ShopIdList, batchNo, pageIndex, pageSize);
        }

        public void SortingRulesPrintCallback(List<int> ShopIdList, string batchNo, int pageIndex, int pageSize)
        {
            _rulesRepository.SortingRulesPrintCallback(ShopIdList, batchNo, pageIndex, pageSize);
        }

        /// <summary>
        /// 根据扫码条码获取当前订单详细信息和备货列表信息
        /// </summary>
        /// <param name="barcode"></param>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public NaHuoOrderModel GetOrdersByBarcode(string barcode, List<int> shopIds)
        {

            //日志开始
            var subLog = LogForOperatorContext.Current.StartStep(new LogForOperator()
            {
                OperatorType = "查询订单",
                Request = new { BarCode = barcode, Sids = shopIds }
            });

            var systemSetting = _commonSettingService.GetSystemSetting(SiteContext.Current.CurrentShopId);
            var settings = _commonSettingService.Get<ScanProductSettingModel>("ScanProductPrintSetting", SiteContext.Current.CurrentShopId);
            var tuple = _orderRepository.GetOrdersByBarcode(barcode, shopIds, settings, systemSetting);
            var currItem = tuple.Item1;
            var orders = tuple.Item2;

            subLog.Response = orders;

            //日志结束
            LogForOperatorContext.Current.EndStep(subLog);

            //var subLog_02 = LogForOperatorContext.Current.StartStep(new LogForOperator()
            //{
            //    OperatorType = "同步订单"
            //});
            ////数据返回之前，先同步订单,以防订单已经退款。
            ////1.循环同步订单
            //var newOrders = SyncOrder(orders);

            ////日志结束
            //LogForOperatorContext.Current.EndStep(subLog_02);

            ////日志开始
            //var subLog_03 = LogForOperatorContext.Current.StartStep(new LogForOperator()
            //{
            //    OperatorType = "更新订单状态"
            //});

            ////2.更新订单状态
            //newOrders?.ForEach(newO =>
            //{
            //    var o = orders.FirstOrDefault(f => f.ShopId == newO.ShopId && f.PlatformOrderId == newO.PlatformOrderId);
            //    o.PlatformStatus = newO.PlatformStatus;
            //    o.RefundStatus = newO.RefundStatus;
            //    newO.OrderItems?.ForEach(noi =>
            //    {
            //        var oi = o.OrderItems?.FirstOrDefault(f => f.PlatformOrderId == noi.PlatformOrderId && f.ShopId == noi.ShopId && f.SubItemID == noi.SubItemID);
            //        if (oi != null)
            //        {
            //            oi.RefundStatus = noi.RefundStatus;
            //        }
            //    });
            //});

            ////日志结束
            //LogForOperatorContext.Current.EndStep(subLog_03);

            if (currItem == null)
                return null;

            orders?.ForEach(o =>
            {
                GetOrderRefundStatus(o);
            });

            NaHuoCurrentOrderInfo currNaHuoOrder = new NaHuoCurrentOrderInfo();
            var currOrder = orders.FirstOrDefault(o => o.OrderItems.Any(oi => oi.SubItemID == currItem.SubItemID && oi.PlatformOrderId == currItem.PlatformOrderId && oi.ShopId == currItem.ShopId));
            if (currOrder == null)
                throw new LogicException("未找到相应订单");
            var currOrderItem = currOrder.OrderItems.FirstOrDefault(oi => oi.SubItemID == currItem.SubItemID && oi.PlatformOrderId == currItem.PlatformOrderId && oi.ShopId == currItem.ShopId);

            currNaHuoOrder.OrderItemId = currOrderItem.Id;
            currNaHuoOrder.BatchNo = currOrder.BatchNo.ToString2();
            currNaHuoOrder.Barcode = barcode;
            currNaHuoOrder.ShopId = currItem.ShopId;
            currNaHuoOrder.ShopName = SiteContext.Current.AllShops.FirstOrDefault(m => m.Id == currItem.ShopId)?.NickName ?? "";
            currNaHuoOrder.BuyerWangWang = currOrder.BuyerWangWang.ToString2();
            currNaHuoOrder.ToName = currOrder.ToName.ToString2();
            currNaHuoOrder.PlatformOrderId = currOrder.PlatformOrderId.ToString2();
            currNaHuoOrder.PlatformStatus = currOrder.PlatformStatus.ToString2();
            currNaHuoOrder.LastWaybillCode = currOrder.LastWaybillCode.ToString2();
            currNaHuoOrder.BuyerRemark = currOrder.BuyerRemark.ToString2();
            currNaHuoOrder.SellerRemark = currOrder.SellerRemark.ToString2();
            currNaHuoOrder.PrintedCount = currOrder.PrintedCount.ToInt();
            currNaHuoOrder.ProductItemCount = currOrder.ProductItemCount.ToInt();
            currNaHuoOrder.ProductAttr = currOrderItem.Color.ToString2() + " " + currOrderItem.Size.ToString2();
            currNaHuoOrder.ProductSubject = currOrderItem.ProductSubject.ToString2();
            currNaHuoOrder.ImgUrl = currOrderItem.ProductImgUrl.ToString2();
            currNaHuoOrder.RefundStatus = currOrder.RefundStatus.ToString2();

            if (SiteContext.Current.CurrentLoginShop.PlatformType == PlatformType.Pinduoduo.ToString())
            {
                var order = new Order { ToName = currNaHuoOrder.ToName, ShopId = currNaHuoOrder.ShopId, PlatformOrderId = currNaHuoOrder.PlatformOrderId };
                var service = new PinduoduoPlatformService(SiteContext.Current.AllShops.FirstOrDefault(x => x.Id == currNaHuoOrder.ShopId));
                service.DecryptBatch(new List<Order> { order }, true, "ToName");
                currNaHuoOrder.ToName = order.ToName;
                currNaHuoOrder.BuyerWangWang = order.ToName;
            }

            //// 组合合并订单
            //NaHuoOrder order = currOrder.ToJson().ToObject<NaHuoOrder>();      
            //if (orders.Count > 1)
            //{
            //    // 确保扫描商品订单在第一个显示
            //    orders.Remove(currOrder);
            //    orders.Insert(0, currOrder);

            //    var childOrderId = string.Empty;
            //    var buyerRemark = string.Empty;
            //    var sellerRemark = string.Empty;
            //    orders.ForEach(o =>
            //    {
            //        childOrderId += o.PlatformOrderId + ",";
            //        buyerRemark += o.BuyerRemark.ToString2() + "|||";
            //        sellerRemark += o.SellerRemark.ToString2() + "|||";

            //        o.OrderItems.ForEach(oi =>
            //        {
            //            oi.OrignalOrderId = o.PlatformOrderId;
            //            oi.PlatformOrderId = order.PlatformOrderId;
            //            order.OrderItems.Add(oi);
            //        });

            //        var subOrder = o.ToJson().ToObject<NaHuoOrder>();
            //        subOrder.MergeredOrderId = order.PlatformOrderId;
            //        subOrder.ShopName = SiteContext.Current.AllShops.FirstOrDefault(m => m.Id == o.ShopId)?.ShopName ?? "";
            //        order.SubOrders.Add(subOrder);
            //    });
            //    childOrderId = childOrderId.TrimEnd(',');
            //    buyerRemark = buyerRemark.Substring(0, buyerRemark.Length - 3);
            //    buyerRemark = buyerRemark.Substring(0, buyerRemark.Length - 3);
            //    order.ChildOrderId = childOrderId;
            //    order.ShopName = SiteContext.Current.AllShops.FirstOrDefault(m => m.Id == currItem.ShopId)?.ShopName ?? "";
            //}

            // 确保扫描商品订单在第一个显示
            orders.Remove(currOrder);
            orders.Insert(0, currOrder);

            NaHuoOrderModel model = new NaHuoOrderModel
            {
                ScanProductSetting = settings,
                CurrOrder = currNaHuoOrder,
                Orders = orders,
            };

            return model;
        }

        public List<Order> SyncOrder(List<NaHuoOrder> orders)
        {
            var sids = orders?.Select(f => f.ShopId)?.Distinct();
            var shops = new List<Shop>();
            sids?.ToList()?.ForEach(sid =>
            {
                var shop = _shopService.Get(sid);
                shops.Add(shop);
            });
            var newOrders = new ConcurrentBag<Order>();
            Parallel.ForEach(orders, new ParallelOptions { MaxDegreeOfParallelism = 15 }, (order) =>
            {
                try
                {
                    var shop = shops.SingleOrDefault(f => f.Id == order.ShopId);
                    var newOrder = (new SyncOrderService()).SyncSingleOrder(order.PlatformOrderId, shop);
                    newOrders.Add(newOrder);
                }
                catch
                {
                    //这里同步失败，不处理。
                }
            });
            return newOrders.ToList();
        }

        public void GetOrderRefundStatus(NaHuoOrder order)
        {
            if (order == null) return;

            //异常情况处理（阿里接口数据返回可能有误，订单上有退款状态refundclose，但产品上都没有；实际上订单是正常的，没有退款）
            if (order.OrderItems.All(oi => string.IsNullOrEmpty(oi.RefundStatus)))
                order.RefundStatus = null;
            else
            {
                //如果有订单项中有退款中，则订单为退款中
                var oiHasRefunding = order.OrderItems.Any(f => f.RefundStatus == RefundStatusType.WAIT_SELLER_AGREE.ToString());
                if (oiHasRefunding)
                {
                    order.RefundStatus = RefundStatusType.WAIT_SELLER_AGREE.ToString();
                }
                //如果订单项中退款的产品退款状态都为已完成则为退款完成
                var oiAllRefunded = order.OrderItems.All(f => f.RefundStatus == RefundStatusType.REFUND_SUCCESS.ToString());
                if (oiAllRefunded)
                {
                    order.RefundStatus = RefundStatusType.REFUND_SUCCESS.ToString();
                    //order.PlatformStatus = OrderStatusType.cancel.ToString();
                }
                //如果订单项中退款的产品退款状态都为关闭则为退款关闭
                var oiAllRefundColse = order.OrderItems.All(f => f.RefundStatus == RefundStatusType.REFUND_CLOSE.ToString());
                if (oiAllRefundColse)
                {
                    order.RefundStatus = RefundStatusType.REFUND_CLOSE.ToString();
                }
            }
        }

        public bool UpdateScanStatusAndScannedTime(string barcode, bool isTun)
        {
            return _rulesRepository.UpdateScanStatusAndScannedTime(barcode, isTun);
        }

        public bool UpdatePrintedTime(List<string> barcodes)
        {
            return _rulesRepository.UpdatePrintedTime(barcodes);
        }

        public bool UpdateNahuoInfo(List<UpdateNaHuoOrderModel> printDatas)
        {
            return _rulesRepository.UpdateNahuoInfo(printDatas);
        }


        public List<object> SinglePrintLabelByPurchases(JToken jtoken, List<NaHuoLabelFullInfoModel> entriesList, string bazaarStallCanShu, bool isPrintOneBazaarStall, out string lastBazaarStall)
        {
            var printControlList = jtoken?.Value<JArray>("NaHuoempsetlist");
            var templateList = new List<object>();

            var index = 1;
            var model = new NaHuoLabelSerialNumberModel();
            var shopId = SiteContext.Current.CurrentShopId;
            CommonSettingService _commService = new CommonSettingService();
            var commSets = _commService.GetSets(new List<string> { _NaHuoLabelSerialNumberKey, _SetNaHuoLabelDataKey }, shopId);
            var orderIndexSet = commSets.FirstOrDefault(m => m.Key == _NaHuoLabelSerialNumberKey);
            // 拿货小标签流水号
            if (orderIndexSet != null && !orderIndexSet.Value.IsNullOrEmpty())
            {
                model = orderIndexSet.Value.ToObject<NaHuoLabelSerialNumberModel>() ?? new NaHuoLabelSerialNumberModel();
                index = model.UpdateTime.ToString("yyyy-MM-dd") == DateTime.Now.ToString("yyyy-MM-dd") && model != null && model.Index > 0 ? model.Index : 1;
            }
            // 店铺标记
            NaHuoLabelTemplateConfigModel naHuoConfigSet = null;
            var sortShopSignSet = commSets.FirstOrDefault(m => m.Key == "SetNaHuoLabelData");
            var shopSigns = new List<NaHuoLabelShopSign>();
            if (sortShopSignSet != null && !sortShopSignSet.Value.IsNullOrEmpty())
            {
                naHuoConfigSet = sortShopSignSet.Value.ToObject<NaHuoLabelTemplateConfigModel>();
                shopSigns = naHuoConfigSet?.ShopSignList ?? new List<NaHuoLabelShopSign>();
            }

            string linshiBazaarStall = bazaarStallCanShu;

            foreach (NaHuoLabelFullInfoModel entries in entriesList)
            {
                entries.ShopSign = shopSigns.FirstOrDefault(m => m.ShopId == entries.ShopId)?.ShopSign ?? "";
                var TemplateInputList = new List<object>();

                var bazaarStall = Convert.ToString(entries.Bazaar) + Convert.ToString(entries.Stall);

                //每次有一个新的档口就打印一张在前面
                if (!string.IsNullOrEmpty(bazaarStall) && bazaarStall != linshiBazaarStall && isPrintOneBazaarStall)
                {
                    linshiBazaarStall = bazaarStall;

                    TemplateInputList.Add(new
                    {
                        controlId = "customText",
                        InputText = Convert.ToString(entries.Bazaar) + "  " + Convert.ToString(entries.Stall),
                        Width = 139,
                        Height = 50,
                        X = 8,
                        Y = 43,
                        FontSize = 12,
                        FontFamily = "normal",
                        FontWeight = "1",
                        highlimit = "0",
                        IsCode = "",
                        controlType = 2,
                        IsText = false,
                    });
                    templateList.Add(new { TemplateInputList });
                    TemplateInputList = new List<object>();
                }

                foreach (var pc in printControlList)
                {
                    var isChecked = pc?.Value<bool>("isChecked") ?? false;
                    if (isChecked && pc != null)
                    {
                        var controlId = pc.Value<string>("textId") ?? "";
                        var tests = pc.Value<string>("customText") ?? "";

                        var controlName = controlId == "SerialId" ? index.ToString() : GetControlName(controlId, tests, entries);
                        var isCode = "";
                        if (controlId == "OrderBarCode" || controlId == "BarCode")
                            isCode = "128Auto";

                        TemplateInputList.Add(new
                        {
                            controlId = controlId,
                            InputText = controlName,
                            Width = pc.Value<int>("width"),
                            Height = pc.Value<int>("height"),
                            X = pc.Value<int>("left"),
                            Y = pc.Value<int>("top"),
                            FontSize = pc.Value<int>("fontSize"),
                            FontFamily = pc.Value<string>("fontWeight"),
                            FontWeight = pc.Value<string>("fontWeight") == "normal" ? "0" : "1",
                            highlimit = "0",
                            IsCode = isCode,
                            controlType = 2,
                            IsText = false,
                        });
                    }
                }
                index++;
                templateList.Add(new { TemplateInputList });
            }//商品END

            model = new NaHuoLabelSerialNumberModel { UpdateTime = DateTime.Now, Index = index };
            _commService.Set(_NaHuoLabelSerialNumberKey, model.ToJson(), shopId);

            lastBazaarStall = linshiBazaarStall;
            return templateList;
        }
        public List<object> DoublePrintLabelByPurchases(JToken jtoken, List<NaHuoLabelFullInfoModel> entriesList, string bazaarStallCanShu, bool isPrintOneBazaarStall, out string lastBazaarStall)
        {
            var printControlList = jtoken?.Value<JArray>("NaHuoempsetlist");
            var templateList = new List<object>();
            var TemplateInputList = new List<object>();

            int position = 0; //1为左边，2为右边

            var index = 1;
            var model = new NaHuoLabelSerialNumberModel();
            var shopId = SiteContext.Current.CurrentShopId;
            CommonSettingService _commService = new CommonSettingService();
            var commSets = _commService.GetSets(new List<string> { _NaHuoLabelSerialNumberKey, _SetNaHuoLabelDataKey }, shopId);
            var orderIndexSet = commSets.FirstOrDefault(m => m.Key == _NaHuoLabelSerialNumberKey);
            // 拿货小标签流水号
            if (orderIndexSet != null && !orderIndexSet.Value.IsNullOrEmpty())
            {
                model = orderIndexSet.Value.ToObject<NaHuoLabelSerialNumberModel>() ?? new NaHuoLabelSerialNumberModel();
                index = model.UpdateTime.ToString("yyyy-MM-dd") == DateTime.Now.ToString("yyyy-MM-dd") && model != null && model.Index > 0 ? model.Index : 1;
            }
            // 店铺标记
            NaHuoLabelTemplateConfigModel naHuoConfigSet = null;
            var sortShopSignSet = commSets.FirstOrDefault(m => m.Key == "SetNaHuoLabelData");
            var shopSigns = new List<NaHuoLabelShopSign>();
            if (sortShopSignSet != null && !sortShopSignSet.Value.IsNullOrEmpty())
            {
                naHuoConfigSet = sortShopSignSet.Value.ToObject<NaHuoLabelTemplateConfigModel>();
                shopSigns = naHuoConfigSet?.ShopSignList ?? new List<NaHuoLabelShopSign>();
            }
            string linshiBazaarStall = bazaarStallCanShu;

            foreach (NaHuoLabelFullInfoModel entries in entriesList)
            {
                if (position == 0)
                    position = 1;
                else if (position == 1)
                    position = 2;
                else
                    position = 1;

                var bazaarStall = Convert.ToString(entries.Bazaar) + Convert.ToString(entries.Stall);

                //每次有一个新的档口就打印一张在前面
                if (!string.IsNullOrEmpty(bazaarStall) && bazaarStall != linshiBazaarStall && isPrintOneBazaarStall)
                {
                    linshiBazaarStall = bazaarStall;
                    int xx = 8;
                    if (position == 2)
                        xx = xx + 200;

                    TemplateInputList.Add(new
                    {
                        controlId = "customText",
                        InputText = Convert.ToString(entries.Bazaar) + "  " + Convert.ToString(entries.Stall),
                        Width = 139,
                        Height = 50,
                        X = xx,
                        Y = 43,
                        FontSize = 12,
                        FontFamily = "normal",
                        FontWeight = "1",
                        highlimit = "0",
                        IsCode = "",
                        controlType = 2,
                        IsText = false,
                    });

                    if (position == 2)
                    {
                        templateList.Add(new { TemplateInputList });
                        TemplateInputList = new List<object>();
                    }

                    if (position == 1)
                        position = 2;
                    else
                        position = 1;
                }



                entries.ShopSign = shopSigns.FirstOrDefault(m => m.ShopId == entries.ShopId)?.ShopSign ?? "";
                foreach (var pc in printControlList)
                {
                    var isChecked = pc?.Value<bool>("isChecked") ?? false;
                    if (isChecked && pc != null)
                    {
                        var controlId = pc.Value<string>("textId") ?? "";
                        var tests = pc.Value<string>("customText") ?? "";
                        var controlName = controlId == "SerialId" ? index.ToString() : GetControlName(controlId, tests, entries);
                        var isCode = "";
                        if (controlId == "OrderBarCode" || controlId == "BarCode")
                            isCode = "128Auto";


                        var xx = pc.Value<int>("left");
                        if (position == 2)
                            xx = xx + 200;

                        TemplateInputList.Add(new
                        {
                            controlId = controlId,
                            InputText = controlName,
                            Width = pc.Value<int>("width"),
                            Height = pc.Value<int>("height"),
                            X = xx,
                            Y = pc.Value<int>("top"),
                            FontSize = pc.Value<int>("fontSize"),
                            FontFamily = pc.Value<string>("fontWeight"),
                            FontWeight = pc.Value<string>("fontWeight") == "normal" ? "0" : "1",
                            highlimit = "0",
                            IsCode = isCode,
                            controlType = 2,
                            IsText = false,
                        });
                    }
                }

                //双数时，添加并清空TemplateInputList
                if (position == 2)
                {
                    templateList.Add(new { TemplateInputList });
                    TemplateInputList = new List<object>();
                }

                index++;
            }//商品END

            //如果最后一个是单数，则补充添加数组中
            if (TemplateInputList.Count > 1 && position == 1)
            {
                templateList.Add(new { TemplateInputList });
            }

            model = new NaHuoLabelSerialNumberModel { UpdateTime = DateTime.Now, Index = index };
            _commService.Set(_NaHuoLabelSerialNumberKey, model.ToJson(), shopId);

            lastBazaarStall = linshiBazaarStall;
            return templateList;
        }




        public string GetControlName(string controlId, string tests, NaHuoLabelFullInfoModel entries)
        {
            var times = DateTime.Now.ToString("MM-dd HH:mm");
            var controlName = "";
            if (controlId == "PlatformOrderId")//订单编号
            {
                controlName = entries.PlatformOrderId.ToString2();

            }
            else if (controlId == "OrderBarCode")//订单号条形码
            {
                controlName = entries.PlatformOrderId;
            }
            else if (controlId == "PrintDate")//打印日期
            {
                controlName = times;
            }
            //else if (controlId == "SerialId")//流水号
            //{                                                      
            //    controlName = "";
            //}
            else if (controlId == "ProductSubject")//商品标题
            {
                controlName = entries.ProductSubject.ToString2();
            }
            else if (controlId == "OneOrMore")//单件/多件
            {
                controlName = entries.TotalCount > 1 ? "多" : "单";
            }
            else if (controlId == "ProductWyNumber")//商品唯一码
            {
                controlName = entries.Barcode.ToString2();
            }
            else if (controlId == "BarCode")//商品唯一条形码
            {
                controlName = entries.Barcode;
            }
            else if (controlId == "FjmCode")//分拣编码
            {
                controlName = entries.SortingCode.ToString2();
            }
            else if (controlId == "StockSign")//屯货标记
            {
                controlName = entries.IsTun ? "屯" : "";
            }
            else if (controlId == "StockoutSign")//缺货标记
            {
                controlName = "缺";
            }
            else if (controlId == "UrgentSign")//加急标记
            {
                controlName = "急";
            }
            else if (controlId == "ProductCargoNumber")//商家编码
            {
                controlName = entries.ProductCargoNumber.ToString2();
            }
            else if (controlId == "ShopSign") //店铺标记
            {
                controlName = entries.ShopSign.ToString2();
            }
            else if (controlId == "Bazaar") //市场
            {
                controlName = entries.Bazaar.ToString2();
            }
            else if (controlId == "Stall")//档口
            {
                controlName = entries.Stall;
            }
            else if (controlId == "ToName")//收件人
            {
                controlName = entries.ToName.ToString2();
            }
            //else if (controlId == "ProductContent")//商品内容
            //{
            //    controlName = entries.ProductContent;
            //}
            else if (controlId == "ShortTitle")//商品简称
            {
                controlName = entries.ShortTitle.ToString2();
            }
            else if (controlId == "TotalCount")//总数量
            {
                controlName = Convert.ToString(entries.TotalCount);
            }
            else if (controlId == "Color")//商品颜色
            {
                controlName = entries.Color.ToString2();
            }
            else if (controlId == "Size") //商品尺码
            {
                controlName = entries.Size.ToString2();
            }
            //else if (controlId == "SkuName")//规格名称
            //{

            //}
            else if (controlId == "SkuShortTitle")//规格简称
            {
                controlName = entries.SkuShortTitle.ToString2();
            }
            else if (controlId == "CargoNumber")//规格编码
            {
                controlName = entries.CargoNumber.ToString2();
            }
            else if (controlId == "NhPrice") //拿货单价
            {
                controlName = Convert.ToString(entries.Price);
            }
            else if (controlId == "CostPrice") //成本价
            {
                controlName = Convert.ToString(Convert.ToDouble(entries.CostPrice));
            }
            else if (controlId == "customText")//自定义
            {
                controlName = tests;
            }

            return controlName;

        }

        public List<ShortTitleReturnModel> GetProductShortTitle(List<SearchShortTitleModel> list)
        {
            return _orderRepository.GetProductShortTitle(list);
        }

        public List<NaHuoLabelSortingRules> GetByBarcodes(List<string> barcodes)
        {
            return _rulesRepository.GetByBarcodes(barcodes);
        }
    }
}
