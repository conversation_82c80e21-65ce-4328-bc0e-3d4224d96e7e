using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Services
{

    public partial class OpenPlatformAppService : BaseService<Data.Entity.OpenPlatformApp>
    {
        private OpenPlatformAppRepository repository = new OpenPlatformAppRepository();

        public OpenPlatformAppService()
        {
            _baseRepository = repository;
        }
        public OpenPlatformApp GetByAppKey(string appKey)
        {
            return repository.GetByAppKey(appKey);
        }

        public OpenPlatformApp GetByField(string appKey,string field)
        {
            return repository.GetByAppKey(appKey, field);
        }

        public void PreRequest(ApiCommonRequestModel requestModel)
        {
            if (requestModel == null)
                throw new LogicException("�����������Ϊ��");
            if (string.IsNullOrEmpty(requestModel.appKey))
                throw new LogicException("appKey����Ϊ��");
            if (string.IsNullOrEmpty(requestModel.method))
                throw new LogicException("����������Ϊ��");
            if (string.IsNullOrEmpty(requestModel.timestamp))
                throw new LogicException("ʱ�������Ϊ��");
            var date = requestModel.timestamp.ConvertTimeStampToDateTime();
            //ʱ�����²���5����
            var maxExpiredMinute = 10;
            if (date == null || date < DateTime.Now.AddMinutes(-maxExpiredMinute) || date>DateTime.Now.AddMinutes(maxExpiredMinute))
            {
                if(CustomerConfig.IsDebug == false)
                    throw new LogicException("�����ѹ��ڣ�����²�����������");
            }
            if (string.IsNullOrEmpty(requestModel.sign))
                throw new LogicException("ǩ������Ϊ��");
            if (string.IsNullOrEmpty(requestModel.shopId))
                throw new LogicException("����ID����Ϊ��");
            var app = GetByField(requestModel.appKey, "Id,AppSecret");
            if (string.IsNullOrEmpty(app?.AppSecret))
                throw new LogicException("appKey�����ڻ����¼�");
            var dict = new Dictionary<string, string>();
            dict.Add("appKey", requestModel.appKey);
            dict.Add("timestamp", requestModel.timestamp);
            dict.Add("param", requestModel.param ?? "");
            dict.Add("method", requestModel.method);
            dict.Add("shopId", requestModel.shopId);
            if(CustomerConfig.IsDebug == false)
            {
                var sign = OpenPlatformApiClient.sign(dict, app.AppSecret);
                if (sign != requestModel.sign)
                    throw new LogicException("ǩ������");
            }
            var shopService = new ShopService();
            var shop = shopService.GetShopByMemberId(new List<string> { requestModel.shopId }, PlatformType.OpenV1.ToString())?.FirstOrDefault();
            if (shop == null || shop.OpenPlatformAppId != app.Id)
                throw new LogicException("����ID���󣬻��Ӧ�ĵ��̲����ڣ�����Ȩ�����ܼҺ������ԡ�");
            requestModel.Shop = shop;
        }
    }
}
