extern alias snsdk;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Enum;
using System.Threading;
using System.Diagnostics;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Services.PlatformService.Track.Base;
using DianGuanJiaApp.Services.Services.SyncDataInterface;
using DianGuanJiaApp.Services.Services.ProfitStatistics;
using DianGuanJiaApp.Services.Model;
using DianGuanJiaApp.Data.Extension;
using System.Threading.Tasks;
using iTextSharp.text.pdf;
using DianGuanJiaApp.Data.Repository.Settings;
using DianGuanJiaApp.Services.Services.UniversalModule;

namespace DianGuanJiaApp.Services
{
    /// <summary>
    /// 订单相关的服务
    /// </summary>
    public partial class OrderService
    {

        private UserFxService _fxUserService = new UserFxService();
        private ShopService _shopService = new ShopService();
        private FxUserShopService _fxUserShopService = new FxUserShopService();
        private ProductFxService _prodcutFxService = new ProductFxService();
        private PathFlowReferenceService _pathFlowRefService = new PathFlowReferenceService();
        private LogicOrderService _logicOrderService = new LogicOrderService();
        private OrderTagService _orderTagService = new OrderTagService();

        /// <summary>
        /// 手动同步订单锁
        /// </summary>
        public const string MANUAL_SYNC_ORDER_LOCK_KEY = "DianGuanJiaApp:NewOrder:ManualSyncOrderLock";
        /// <summary>
        /// 手动同步订单结果
        /// </summary>
        public const string MANUAL_SYNC_ORDER_RESULT_KEY = "DianGuanJiaApp:NewOrder:ManualSyncOrderResult";
        /// <summary>
        /// 手动触发商家同步锁
        /// </summary>
        public const string AGENT_SYNC_ORDER_LOCK_KEY = "DianGuanJiaApp:NewOrder:AgentSyncOrderLock";

        //private OrderBindSupplierService _orderBindSupplierService = new OrderBindSupplierService();
        //private AgentSkuBindSupplierSkuService _agentSkuBindSupplierSkuService = new AgentSkuBindSupplierSkuService();
        //private SupplierProductService _supplierProductService = new SupplierProductService();

        //private PurchaseOrderRelationService _purchaseOrderRelationService = new PurchaseOrderRelationService();
        //private AfterSaleActionRecordService _afterSaleActionRecordService = new AfterSaleActionRecordService();

        //private OperatLogService _operatLogService = new OperatLogService();

        ///// <summary>
        ///// 合并数据插入【移至ColdOrderService】
        ///// </summary>
        ///// <param name="orders"></param>
        ///// <param name="mergerParameterModel"></param>
        ///// <param name="isReleaseOrders"></param>
        ///// <param name="tag"></param>
        ///// <returns></returns>
        //public List<Order> BulkMergerFxOrder(List<Order> orders, OrderMergerParameterModel mergerParameterModel, bool isReleaseOrders = true, string tag = "")
        //{
        //    //Log.WriteLine($"ProcessBuyerFxMessage.BulkMergerFxOrder21，DB={_repository.DbConnection.Database}，orders.PlatformOrderId:{orders.Select(a => a.PlatformOrderId).ToJson(true)}", $"1688-buyer-{DateTime.Now.ToString("yyyyMMdd")}.txt");

        //    var result = _repository.BulkMergerFxOrder(orders, mergerParameterModel, isReleaseOrders, tag);

        //    //Log.WriteLine($"ProcessBuyerFxMessage.BulkMergerFxOrder22，DB={_repository.DbConnection.Database}，result:{result.ToJson(true)}", $"1688-buyer-{DateTime.Now.ToString("yyyyMMdd")}.txt");

        //    //#region 数据变更日志
        //    //List<DataChangeLog> dcLogs = orders?.Select(o => new DataChangeLog
        //    //{
        //    //    DataChangeType = DataChangeTypeEnum.INSERT,
        //    //    TableTypeName = DataChangeTableTypeName.Order,
        //    //    SourceShopId = o.ShopId,
        //    //    SourceFxUserId = o.UserId,
        //    //    RelationKey = o.OrderCode,
        //    //    ExtField1 = "BulkMergerFxOrder"
        //    //}).ToList();
        //    //new DataChangeLogRepository().Add(dcLogs);
        //    //#endregion
        //    return result;
        //}

        ///// <summary>
        ///// 同步完成：将订单批量合并到数据，仅同步后调用 【移至CostOrderService】
        ///// </summary>
        ///// <param name="orders"></param>
        ///// <param name="mergerParameterModel"></param>
        ///// <param name="isOfflineOrder"></param>
        ///// <param name="tag"></param>
        //public List<Order> BulkMergerFx(List<Order> orders, OrderMergerParameterModel mergerParameterModel = null,
        //    bool isOfflineOrder = false, string tag = "")
        //{
        //    //校验平台
        //    if(orders != null && orders.Any())
        //    {
        //        //_shopService.CheckTouTiaoShopCloudPlatformType(orders.First().UserId);
        //        _shopService.CheckShopCloudPlatformType(orders.First().PlatformType, orders.First().ShopId);
        //    }

        //    //已下步骤不能打乱。要先补商品，因为订单经过保存原始数据的方法，有些字段的数据会丢失。所以要先补订单商品
        //    //另外接口数据状态可能有延迟，状态有可能逆向更新，保存订单数据的方法有处理，所以要先保存数据，再进行逻辑单状态计算。
        //    try
        //    {
        //        //1.检查订单的商品是否存在，不存在补商品（1.同步商品，2.同步不到自动补填）
        //        //正常情况下只需要检查新增的订单即可,为了安全起见，每次都检查所有同步到的商品
        //        CheckOrderProduct(orders.Where(f => f.PlatformStatus?.ToLower() == "waitsellersend"), isOfflineOrder: isOfflineOrder, isFromBulkMergerFx: true);

        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriteError("检查订单商品信息报错：" + ex);
        //        throw ex;
        //    }

        //    if (mergerParameterModel == null)
        //    {
        //        mergerParameterModel = new OrderMergerParameterModel()
        //        {
        //            IsDontUpdateRefundStatus = false,
        //            IsFromMessage = false
        //        };
        //    }
        //    var result = new List<Order>();
        //    try
        //    {
        //        //2.保存原始订单
        //        if (mergerParameterModel.IsFromMessage == false && orders?.FirstOrDefault()?.PlatformType == PlatformType.Taobao.ToString())
        //        {
        //            mergerParameterModel.IsTaobaoSyncUseCursor = _commonSettingService.IsTaobaoSyncUseCursor();
        //        }

        //        //Log.WriteLine($"ProcessBuyerFxMessage.BulkMergerFx，orders.PlatformOrderId:{orders.Select(a => a.PlatformOrderId).ToJson(true)}", $"1688-buyer-{DateTime.Now.ToString("yyyyMMdd")}.txt");

        //        //保存收件人信息，原订单白名单配置：保留原始数据或打码数据
        //        SaveReceiver(orders, isOfflineOrder);

        //        //快手/团好货/小红书保存订单之前，先保存订单收件人加密信息
        //        //移除 2022.11.23
        //        //if (isOfflineOrder == false && FxPlatformEncryptService.QueryEncryptedReceiverPlatformTypes.Contains(orders?.FirstOrDefault()?.PlatformType))
        //        //{
        //        //    var receiverInfoService = new KuaiShouEncryptedReceiverInfoService();
        //        //    var receiverEncryptList = orders.Where(f => f.EncryptedReceiverInfo != null).Select(f => f.EncryptedReceiverInfo).ToList();
        //        //    receiverEncryptList.ForEach(re => { re.OrderCode = (re.PlatformOrderId + re.ShopId).ToShortMd5(); });
        //        //    receiverInfoService.BulkInsert(receiverEncryptList);
        //        //}

        //        //Log.WriteError("同步订单：" + orders.ToJson(true), "OrderData.log");

        //        //订单标签保存动作
        //        var orderTags = orders.Where(f => f.Tags?.Any() == true).SelectMany(f => f.Tags).ToList();
        //        var orderItemTags = orders.SelectMany(x => x.OrderItems).Where(f => f.Tags?.Any() == true).SelectMany(f => f.Tags).ToList();
        //        orderItemTags.ForEach(oit =>
        //        {
        //            if (orderTags.Any(ot => ot.OiCode == oit.OiCode && ot.Tag == oit.Tag) == false)
        //                orderTags.Add(oit);
        //        });
        //        var ordeTagService = new OrderTagService(_connectionString);
        //        ordeTagService.BulkInsert(orderTags);

        //        //原始单处理
        //        //Log.Debug($"原始单处理 {orders.ToJson()}");
        //        result = BulkMergerFxOrder(orders, mergerParameterModel, isReleaseOrders: false, tag: tag);

        //        #region 链路收集，保存原始单，成功
        //        //链路收集
        //        TraceDataCollect(tag, TraceOperationType.OrderSync, TraceType.SaveOrder,
        //            mergerParameterModel.BatchId, TraceStatus.Success, orders);
        //        #endregion

        //    }
        //    catch (Exception ex)
        //    {
        //        var message = "保存原始订单报错：" + ex;
        //        Log.WriteError(message);

        //        #region 链路收集，保存原始单，失败
        //        //链路收集
        //        TraceDataCollect(tag, TraceOperationType.OrderSync, TraceType.SaveOrder,
        //            mergerParameterModel.BatchId, TraceStatus.Fail, orders, message);
        //        #endregion
        //        throw ex;
        //    }

        //    try
        //    {
        //        Log.Debug($"同步到的订单总数,orders.Count:{orders.Count()}");

        //        //3.更新逻辑单的状态(旧单才更新逻辑单)
        //        var updateOrders = orders.Where(f => f.IsNewOrder == false).ToList();
        //        UpdateLogicOrder(updateOrders);

        //        #region 逆向回流
        //        //逆向回流：1688平台，原始单维度
        //        var changeToSendedOrders = new Dictionary<string, Order>();
        //        orders.Where(a => a.PlatformType == PlatformType.Alibaba.ToString()).ToList().ForEach(o =>
        //        {
        //            var newErpStatus = GetLogicErpStatus(o.OrderItems); //Erp状态
        //            if ((newErpStatus == "sended" || newErpStatus == "success") && changeToSendedOrders.ContainsKey(o.PlatformOrderId) == false)
        //            {
        //                changeToSendedOrders.Add(o.PlatformOrderId, o);
        //            }
        //        });

        //        if (changeToSendedOrders.Any())
        //        {
        //            var sendReturnLogs = TryCompleteSendHistoryReturnRecord(changeToSendedOrders);

        //            sendReturnLogs?.ForEach(x => {
        //                x.BatchId = "1688逆向回流";
        //                x.MethodName = "TryCompleteSendHistoryReturnRecord";
        //                x.SubBusinessType = "BulkMergerFx";
        //            });
        //            BusinessLogDataEventTrackingService.Instance.WriteLog(sendReturnLogs);
        //        }

        //        #endregion


        //        #region 链路收集，更新逻辑单状态，成功
        //        //链路收集
        //        TraceDataCollect(tag, TraceOperationType.OrderSync, TraceType.UpdateLogicOrderStatus,
        //            mergerParameterModel.BatchId, TraceStatus.Success, orders);
        //        #endregion
        //    }
        //    catch (Exception ex)
        //    {
        //        //异常信息
        //        var message = "更新逻辑单状态报错：" + ex;
        //        Log.WriteError(message);

        //        #region 链路收集，更新逻辑单状态，失败
        //        //链路收集
        //        TraceDataCollect(tag, TraceOperationType.OrderSync, TraceType.UpdateLogicOrderStatus,
        //            mergerParameterModel.BatchId, TraceStatus.Fail, orders, message);
        //        #endregion

        //        throw ex;
        //    }

        //    #region 采购关系信息处理，只发MQ消息
        //    try
        //    {
        //        var fxUserId = orders.FirstOrDefault()?.UserId ?? 0;
        //        _purchaseOrderRelationService.ProcessPurchaseStatusSendRabbitMQMessage(orders, fxUserId, tag);
        //    }
        //    catch (Exception purEx)
        //    {
        //        Log.WriteError($"ProcessPurchaseStatusSendRabbitMQMessage异常：{purEx}", "ProcessPurchaseStatusSendRabbitMQMessageError.txt");
        //    }
        //    #endregion

        //    #region 自动售后处理
        //    try
        //    {
        //        _afterSaleActionRecordService.AddRecordForOrder(orders);
        //    }
        //    catch (Exception asarEx)
        //    {
        //        Log.WriteError($"AddRecordForOrder异常：{asarEx}", "AddRecordForOrder.txt");
        //    }
        //    #endregion

        //    //#region 数据变更日志
        //    //List<DataChangeLog> dcLogs = orders.GroupBy(a => new { a.ShopId, a.UserId, a.OrderCode }).Select(o => new DataChangeLog
        //    //{
        //    //    DataChangeType = DataChangeTypeEnum.INSERT,
        //    //    TableTypeName = DataChangeTableTypeName.Order,
        //    //    SourceShopId = o.Key.ShopId,
        //    //    SourceFxUserId = o.Key.UserId,
        //    //    RelationKey = o.Key.OrderCode,
        //    //    ExtField1 = "OrderFxService.BulkMergerFx"
        //    //}
        //    //).ToList();
        //    //new DataChangeLogRepository().Add(dcLogs);
        //    //#endregion

        //    return result;
        //}
        /// <summary>
        /// 数据埋点
        /// </summary>
        /// <typeparam name="TCollectionData"></typeparam>
        /// <param name="trackingType"></param>
        /// <param name="baseModel"></param>
        /// <param name="collectionData"></param>
        private void DataEventTracking<TCollectionData>(string tag, EventTrackingType trackingType,
            DataEventTrackingBaseModel baseModel, TCollectionData collectionData)
        {
            var tags = new List<string> { "SyncOrder", "SyncOrderByShop" };
            if (!tags.Contains(tag))
            {
                return;
            }
            var service = DataEventTrackingFactoryService.Instance;
            service.AddTracking(EventTrackingOperationType.OrderSync, trackingType, baseModel, collectionData);
        }

        #region 链路监控收集 【移至CostOrderService】

        ///// <summary>
        ///// 链路监控收集
        ///// </summary>
        ///// <param name="tag"></param>
        ///// <param name="operationType"></param>
        ///// <param name="traceType"></param>
        ///// <param name="batchId"></param>
        ///// <param name="traceStatus"></param>
        ///// <param name="orders"></param>
        ///// <param name="message"></param>
        ///// <param name="hasMetaData"></param>
        ///// <param name="extensionField01"></param>
        ///// <returns></returns>
        //public ReturnedModel TraceDataCollect(string tag, TraceOperationType operationType, TraceType traceType,
        //    string batchId, string traceStatus, List<Order> orders, string message = "", bool hasMetaData = false,
        //    string extensionField01 = null)
        //{
        //    //是否订单同步，保存原始单流程
        //    var tags = new List<string> { "SyncOrder", "SyncOrderByShop", "SyncSingleOrder", "SyncSingleOrderOld" };
        //    if (!tags.Contains(tag))
        //    {
        //        return new ReturnedModel();
        //    }

        //    //链路批次ID为空不记录
        //    if (string.IsNullOrWhiteSpace(batchId))
        //    {
        //        return new ReturnedModel();
        //    }

        //    //判空处理
        //    if (orders == null || !orders.Any())
        //    {
        //        return new ReturnedModel();
        //    }

        //    //构建链路监控模型
        //    var traceDataModels = orders.Select(m => new TraceDataModel<TraceDataMetaDataModel>
        //    {
        //        BatchId = batchId,
        //        ShopId = m.ShopId,
        //        BusinessId = m.PlatformOrderId,
        //        CloudPlatformType = CustomerConfig.CloudPlatformType,
        //        PlatformType = m.PlatformType,
        //        OperationType = operationType.ToString(),
        //        TraceType = traceType.ToString(),
        //        CreateTime = DateTime.Now,
        //        TraceStatus = traceStatus,
        //        Message = message,
        //        MetaData = hasMetaData
        //            ? new TraceDataMetaDataModel
        //            {
        //                RefundStatus = m.RefundStatus,
        //                PlatformStatus = m.PlatformStatus,
        //                CreateTime = m.CreateTime,
        //                ModifyTime = m.ModifyTime,
        //                ExtensionField01 = extensionField01
        //            }
        //            : new TraceDataMetaDataModel()
        //    }).ToList();
        //    //链路收集
        //    return TraceDataCollectionService.GetInstance().Collect(traceDataModels);
        //}

        //public ReturnedModel TraceDataCollect(TraceOperationType operationType, TraceType traceType,
        //    string batchId, string traceStatus, List<AfterSaleOrder> orders, string message = "",
        //    bool hasMetaData = false)
        //{
        //    //链路批次ID为空不记录
        //    if (string.IsNullOrWhiteSpace(batchId))
        //    {
        //        return new ReturnedModel();
        //    }

        //    //判空处理
        //    if (orders == null || !orders.Any())
        //    {
        //        return new ReturnedModel();
        //    }
        //    //构建链路监控模型
        //    var traceDataModels = orders.Select(m => new TraceDataModel<TraceDataMetaDataModel>
        //    {
        //        BatchId = batchId,
        //        ShopId = m.ShopId,
        //        BusinessId = m.AfterSaleId,
        //        CloudPlatformType = CustomerConfig.CloudPlatformType,
        //        PlatformType = m.PlatformType,
        //        OperationType = operationType.ToString(),
        //        TraceType = traceType.ToString(),
        //        CreateTime = DateTime.Now,
        //        TraceStatus = traceStatus,
        //        Message = message,
        //        MetaData = hasMetaData
        //            ? new TraceDataMetaDataModel
        //            {
        //                PlatformOrderId = m.PlatformOrderId,
        //                AfterSaleType = m.PlatAfterSaleType,
        //                RefundStatus = m.PlatRefundStatus,
        //                PlatformStatus = m.PlatAfterSaleStatus,
        //                CreateTime = m.ApplyTime,
        //                ModifyTime = m.PlatUpdateTime
        //            }
        //            : new TraceDataMetaDataModel()
        //    }).ToList();
        //    //链路收集
        //    return TraceDataCollectionService.GetInstance().Collect(traceDataModels);
        //}
        //public ReturnedModel TraceDataCollect(TraceDataModel<TraceDataMetaDataModel> model)
        //{
        //    //链路批次ID为空不记录
        //    return string.IsNullOrWhiteSpace(model.BatchId)
        //        ? new ReturnedModel()
        //        : TraceDataCollectionService.GetInstance().Collect(model);
        //}

        #endregion

        /// <summary>
        /// 复制副本推送消息
        /// </summary>
        /// <param name="orders"></param>
        public void PushMessageForDuplication(List<Order> orders)
        {
            ///改用定时任务推送触发消息2023.04.28
            return;

            //判空处理
            if (orders == null || !orders.Any())
            {
                return;
            }

            ////分库方案添加逻辑
            //orders.GroupBy(a => new { a.ShopId, a.UserId }).ToList().ForEach(group =>
            //{
            //    DuplicationFactoryService.Instance(DataChangeTableTypeName.LogicOrder)
            //        .PushMessage(group.Key.UserId, group.Key.ShopId);
            //});
        }

        /// <summary>
        /// 消息拿到订单后保存订单
        /// </summary>
        /// <param name="orders"></param>
        /// <param name="mergerParameterModel"></param>
        public List<Order> BulkMergerFxFromMessage(List<Order> orders, OrderMergerParameterModel mergerParameterModel = null)
        {
            var result = new List<Order>();
            var log = new LogForOperator();

            try
            {
                log = new LogForOperator()
                {
                    Description = new OperationDescription
                    {
                        Route = $"FxFromMessage",
                        //Referrer = request?.UrlReferrer?.ToString(),
                        //UserAgent = request?.UserAgent,
                        //Url = request?.Url?.ToString(),
                        Name = "分单订单消息"
                    },
                    //TraceId = request?["traceId"],
                    OperatorType = "分单订单消息",
                    //DBType = (SiteContext.CurrentNoThrow?.DataBaseType ?? DatabaseTypeEnum.SQLServer).ToString(),
                    ShopId = SiteContext.GetCurrentFxUserId(),
                    UserId = SiteContext.GetCurrentFxUserId(),//SiteContext.CurrentNoThrow?.CurrentLoginUser?.Id ?? 0,
                    PlatformType = SiteContext.CurrentNoThrow?.CurrentLoginShop?.PlatformType,
                    DbNameConfigId = SiteContext.CurrentNoThrow?.CurrentDbConfig?.DbConfig?.DbNameConfigId ?? 0,
                    //IP = ip,
                    ServerIP = DianGuanJiaApp.Utility.Net.HttpUtility.GetServerIP()
                };
                LogForOperatorContext.Current.Begin(log);

                #region 2.保存原始订单
                try
                {
                    //2.保存原始订单
                    if (mergerParameterModel == null)
                        mergerParameterModel = new OrderMergerParameterModel() { IsDontUpdateRefundStatus = false, IsFromMessage = false };
                    if (mergerParameterModel.IsFromMessage == false && orders?.FirstOrDefault()?.PlatformType == PlatformType.Taobao.ToString())
                    {
                        mergerParameterModel.IsTaobaoSyncUseCursor = _commonSettingService.IsTaobaoSyncUseCursor();
                    }

                    //保存收件人信息
                    SaveReceiver(orders);


                    ////快手保存订单之前，先保存订单收件人加密信息
                    //移除 2022.11.23
                    //if (FxPlatformEncryptService.QueryEncryptedReceiverPlatformTypes.Contains(orders?.FirstOrDefault()?.PlatformType))
                    //{
                    //    var receiverInfoService = new KuaiShouEncryptedReceiverInfoService();
                    //    var receiverEncryptList = orders.Where(f => f.EncryptedReceiverInfo != null).Select(f => f.EncryptedReceiverInfo).ToList();
                    //    receiverInfoService.BulkInsert(receiverEncryptList);
                    //}

                    result = BulkMergerFxOrder(orders, mergerParameterModel, isReleaseOrders: false);

                }
                catch (Exception ex)
                {
                    var errMsg = "保存原始订单报错：" + ex.ToString() + ",订单信息：" + orders.ToJson() + ",订单项信息：" + orders.SelectMany(f => f.OrderItems).ToJson();
                    log.Exception = errMsg;
                    Log.WriteError(errMsg);
                    throw ex;
                }
                #endregion

                #region 3.更新逻辑单的状态(旧单才更新逻辑单)
                try
                {
                    var outUpdatePackageStatusModel = new List<Tuple<string, string, string>>();

                    //3.更新逻辑单的状态(旧单才更新逻辑单)
                    UpdateLogicOrder(orders?.Where(f => f.IsNewOrder == false).ToList(), out outUpdatePackageStatusModel);

                    if (CustomerConfig.IsCrossBorderSite) {
                        //更新Tk订单的发货记录的 Package Status
                        new SendHistoryService().UpdatePackageStatus(outUpdatePackageStatusModel);
                    }
                }
                catch (Exception ex)
                {
                    var errMsg = "更新逻辑单状态报错：" + ex.ToString();
                    log.Exception = errMsg;
                    Log.WriteError(errMsg);
                    throw ex;
                }
                #endregion

                #region 逆向回流
                //逆向回流：1688平台，原始单维度
                var changeToSendedOrders = new Dictionary<string, Order>();
                orders.Where(a => a.PlatformType == PlatformType.Alibaba.ToString()).ToList().ForEach(o =>
                {
                    var newErpStatus = GetLogicErpStatus(o.OrderItems); //Erp状态
                    if ((newErpStatus == "sended" || newErpStatus == "success") && changeToSendedOrders.ContainsKey(o.PlatformOrderId) == false)
                    {
                        changeToSendedOrders.Add(o.PlatformOrderId, o);
                    }
                });

                if (changeToSendedOrders.Any())
                {
                    var sendReturnLogs = TryCompleteSendHistoryReturnRecord(changeToSendedOrders);
                    sendReturnLogs?.ForEach(x =>
                    {
                        x.BatchId = "1688逆向回流";
                        x.MethodName = "TryCompleteSendHistoryReturnRecord";
                        x.SubBusinessType = "BulkMergerFxFromMessage";
                    });
                    BusinessLogDataEventTrackingService.Instance.WriteLog(sendReturnLogs);
                }

                #endregion

                #region 4.拆单
                List<OrderSplitRecord> waitsplit = new List<OrderSplitRecord>();
                OrderSplitRecordService spliteRecord = new OrderSplitRecordService();
                try
                {
                    List<int> shopidss = orders.Select(x => x.ShopId).Distinct().ToList();
                    ShopService shopservice = new ShopService();
                    //var shops = shopservice.GetShopByIds(shopidss, " Id,Version ");
                    //if (shops == null || !shops.Any())
                    //{
                    //    return result;//店铺信息有问题的话,直接不走拆单了,拆单也可能拆错的
                    //}
                    List<int> shopids = new List<int>() { 249702, 970, 247734 };//测试环境的店铺值 
                                                                                //List<int> shopids = shops.Where(s => s.Version == "1").Select(s => s.Id).Distinct().ToList();//灰度环境的可以走
                    var testorders = orders.Where(o => o.IsNewOrder == true && o.PlatformStatus == "waitsellersend").Where(o => shopids.Any(shop => shop == o.ShopId));

                    //使用平台订单去查询拆单状态表,获取对应平台单号的记录,然后逻辑处理,拆过的不拆了,有拆单状态记录的不插入了       
                    var splitedorders = spliteRecord.getByPlatFormIdOnly(testorders.Select(o => o.PlatformOrderId).ToList());
                    //只有还没有拆过的单需要继续拆,这里很关键,拆单重复就GG
                    testorders = testorders.Where(o => !splitedorders.Any(s => s.PlatformOrderId == o.PlatformOrderId && s.IsSplited == true)).ToList();
                    //待插入的拆单状态记录
                    var needinsertrecord = testorders.Where(o => !splitedorders.Any(s => s.PlatformOrderId == o.PlatformOrderId)).ToList();

                    if (testorders.Any())
                    {
                        //先保存至拆单记录表,再拆单,利用记录表的IsSplited来排斥其他数据同时拆单
                        needinsertrecord.ToList().ForEach(o =>
                        {
                            OrderSplitRecord split = new OrderSplitRecord();
                            split.PlatformOrderId = o.PlatformOrderId;
                            split.ShopId = o.ShopId;
                            split.IsSplited = true;
                            split.CreateTime = o.CreateTime;
                            split.Source = 3;
                            waitsplit.Add(split);
                        });
                        spliteRecord.Merge(waitsplit);

                        //4.新单拆分插入逻辑单
                        CheckOrderProduct(testorders, false);
                        SplitLogicOrdersNew(testorders.ToList(), false, tag: "BulkMergerFxFromMessage");
                    }
                }
                catch (Exception ex)
                {
                    var errMsg = "拆分逻辑订单报错：" + ex.ToString();
                    log.Exception = errMsg;

                    //5没有拆成的逻辑单,把拆单记录的拆单状态修改为未拆单
                    if (waitsplit.Any())
                    {
                        waitsplit.ForEach(s => s.IsSplited = false);
                        spliteRecord.Update(waitsplit);
                    }
                    Log.WriteError(errMsg);
                    //测试过程中先不抛出异常
                }

                #endregion

            }
            catch (Exception ex)
            {
                var errMsg = "消息更新订单报错：" + ex.ToString();
                log.Exception = errMsg;
                throw ex;
            }
            finally
            {
                //LogForOperatorContext.Current.End();
            }

            //#region 数据变更日志
            //List<DataChangeLog> dcLogs = orders.GroupBy(a => new { a.ShopId, a.UserId, a.OrderCode }).Select(o => new DataChangeLog
            //{
            //    DataChangeType = DataChangeTypeEnum.INSERT,
            //    TableTypeName = DataChangeTableTypeName.Order,
            //    SourceShopId = o.Key.ShopId,
            //    SourceFxUserId = o.Key.UserId,
            //    RelationKey = o.Key.OrderCode,
            //    ExtField1 = "OrderFxService.BulkMergerFxFromMessage"
            //}
            //).ToList();
            //new DataChangeLogRepository().Add(dcLogs);
            //#endregion

            //分库方案添加逻辑
            PushMessageForDuplication(orders);

            return result;
        }

        ///// <summary>
        ///// 保存收件人信息【移至CostOrderService】
        ///// </summary>
        ///// <param name="orders"></param>
        ///// <param name="isOfflineOrder"></param>
        //private void SaveReceiver(List<Order> orders, bool isOfflineOrder = false)
        //{
        //    #region 保存收件人信息
        //    var dtNow = DateTime.Now;
        //    //当前店铺所属的FxUserId
        //    var fxUserId = orders.FirstOrDefault()?.UserId ?? 0;

        //    //--只针对分单系统即fxUserId>0
        //    if (fxUserId > 0)
        //    {
        //        var receivers = new List<Receiver>();
        //        //店铺收件人白名单配置信息
        //        //全部用新方式：订单表只存收件人打码数据
        //        //var shopIds = orders.Select(a => a.ShopId).Distinct().ToList();
        //        //var commSets = new CommonSettingService().GetSettingByShopIds("/Shop/Fendan/Receiver/OnlySaveNewVersion",shopIds);

        //        orders.ForEach(o =>
        //        {
        //            if (o.PlatformType == PlatformType.XiaoHongShu.ToString() && o.PlatformStatus != OrderStatusType.waitsellersend.ToString())
        //                return;

        //            var decryptField = "";
        //            var extField1 = o.ExtField1;
        //            var extField2 = o.ExtField2;
        //            var isEncrypt = false;

        //            if (o.PlatformType == PlatformType.Taobao.ToString() || o.PlatformType == PlatformType.Alibaba.ToString() || o.PlatformType == PlatformType.AlibabaC2M.ToString())
        //                decryptField = o.ExtField3;
        //            else if (o.PlatformType == PlatformType.Jingdong.ToString())
        //                decryptField = o.ExtField5;
        //            else if (o.PlatformType == PlatformType.KuaiShou.ToString() || o.PlatformType == PlatformType.TuanHaoHuo.ToString() || o.PlatformType == PlatformType.XiaoHongShu.ToString())
        //            {
        //                decryptField = o.ExtField2;
        //                extField2 = "";
        //                isEncrypt = o.ToName.IsKsEncryptedData();
        //            }
        //            else if (o.PlatformType == PlatformType.Suning.ToString())
        //            {
        //                decryptField = o.ExtField1;
        //                extField1 = "";
        //            }
        //            else if (o.PlatformType == PlatformType.Pinduoduo.ToString())
        //            {
        //                isEncrypt = o.ToName.IsPddEncryptedData();
        //                extField1 = "";
        //                extField2 = "";
        //                //拼多多厂家代打订单
        //                if (o.PlatformStatus == "0" || o.PlatformStatus == "1")
        //                {
        //                    extField1 = "fds";
        //                }
        //                if (o.BusinessType == "2")
        //                {
        //                    //跨境单
        //                    extField1 = "2";
        //                    extField2 = "ffm";
        //                }
        //            }
        //            else if (o.PlatformType == PlatformType.TouTiao.ToString())
        //            {
        //                isEncrypt = o.ToName.IsDyEncryptData();
        //                decryptField = o.ExtField3;
        //            }
        //            else if(o.PlatformType == PlatformType.WxVideo.ToString())
        //            {
        //                if (!string.IsNullOrWhiteSpace(o.ExtField4))
        //                {
        //                    //视频号数据加密后，接口会返回收件人信息的唯一标识，按此标识来合单，标识值存储在ExtField4字段内
        //                    //文档：https://doc.weixin.qq.com/doc/w3_AeQAsQaWAFgmUwV2735R72fQr4dmo?scode=AJEAIQdfAAoKr80J88AH4ARAZHAEo
        //                    decryptField = o.ExtField4;
        //                }
        //                else
        //                {
        //                    //下单用户唯一标识，用于生成合并计算列
        //                    decryptField = o.BuyerMemberId;
        //                }
        //            }

        //            var receiver = o.Receiver;
        //            if (receiver == null)
        //            {
        //                receiver = new Receiver();

        //                //非加密才保存打码数据
        //                if (!isEncrypt)
        //                {
        //                    receiver.ToNameMask = o.ToName.ToCutString(64);
        //                    receiver.ToPhoneMask = string.IsNullOrEmpty(o.ToMobile) ? o.ToPhone.ToCutString(64) : o.ToMobile.ToCutString(64);
        //                    receiver.ToAddressMask = o.ToAddress.ToCutString(512);
        //                }
        //            }
        //            else
        //            {
        //                //字段长度处理
        //                receiver.ToNameMask = receiver.ToNameMask.ToCutString(64);
        //                receiver.ToPhoneMask = receiver.ToPhoneMask.ToCutString(64);
        //                receiver.ToAddressMask = receiver.ToAddressMask.ToCutString(512);
        //            }

        //            receiver.ShopId = o.ShopId;
        //            receiver.PlatformType = o.PlatformType;
        //            receiver.PlatformOrderId = o.PlatformOrderId;
        //            receiver.OrderCode = o.OrderCode;
        //            receiver.ToPhone = o.ToPhone;
        //            receiver.ToMobile = o.ToMobile;
        //            receiver.ToName = o.ToName;
        //            receiver.ToProvince = o.ToProvince;
        //            receiver.ToCity = o.ToCity;
        //            receiver.ToCounty = o.ToCounty;
        //            receiver.ToStreet = o.ToTown;
        //            receiver.ToAddress = o.ToAddress;
        //            receiver.DecryptField = decryptField;
        //            receiver.ExtField1 = extField1;
        //            receiver.ExtField2 = extField2;
        //            receiver.CreateTime = dtNow;
        //            receiver.PlatformStatus = o.PlatformStatus;
        //            //快手/团好货/小红书 Receiver取加密原文
        //            if (!isOfflineOrder && FxPlatformEncryptService.QueryEncryptedReceiverPlatformTypes.Contains(orders?.FirstOrDefault()?.PlatformType) && o.EncryptedReceiverInfo != null)
        //            {
        //                receiver.ToName = o.EncryptedReceiverInfo.EncryptedToName;
        //                receiver.ToMobile = o.EncryptedReceiverInfo.EncryptedToMobile;
        //                receiver.ToAddress = o.EncryptedReceiverInfo.EncryptedToAddress;
        //            }

        //            if (!receivers.Any(a => a.OrderCode == receiver.OrderCode))
        //                receivers.Add(receiver);

        //            //平台订单Receiver、ReceiverHashCode、ToPhoneIndex赋值
        //            o.Receiver = receiver;
        //            o.ReceiverHashCode = receiver.ReceiverHashCode;
        //            o.ToPhoneIndex = receiver.ToPhoneIndex;

        //            //订单表只存收件人打码数据
        //            if (!string.IsNullOrEmpty(receiver.ToNameMask))
        //                o.ToName = receiver.ToNameMask;
        //            if (!string.IsNullOrEmpty(receiver.ToPhoneMask))
        //                o.ToMobile = receiver.ToPhoneMask;
        //            if (!string.IsNullOrEmpty(receiver.ToAddressMask))
        //                o.ToAddress = receiver.ToAddressMask;

        //            var pcc = o.ToProvince.ToString2() + o.ToCity.ToString2() + o.ToCounty.ToString2() + o.ToTown.ToString2();
        //            //var street = o.ToStreet;
        //            //if (string.IsNullOrEmpty(street) || o.ToAddress?.StartsWith(street) == true)
        //            //    o.ToFullAddress = pcc + o.ToAddress;
        //            //else
        //            //    o.ToFullAddress = pcc + street + o.ToAddress;
        //            if (o.PlatformType == PlatformType.XiaoHongShu.ToString())
        //                o.ToFullAddress = o.ToAddress;
        //            else
        //                o.ToFullAddress = pcc + o.ToAddress;
        //        });

        //        //保存或更新收件人信息 
        //        if (receivers.Any())
        //            new ReceiverService(fxUserId).BulkMerger(receivers, false);
        //    }
        //    #endregion
        //}

        ///// <summary>
        ///// 检查订单商品 【移至CostOrderService】
        ///// </summary>
        ///// <param name="orders"></param>
        ///// <param name="isOfflineOrder"></param>
        ///// <param name="isFromBulkMergerFx"></param>
        //private void CheckOrderProduct(IEnumerable<Order> orders, bool isOfflineOrder = false, bool isFromBulkMergerFx = false)
        //{
        //    if (isOfflineOrder)
        //        orders = orders.Where(f => f.TradeType != "OfflineNoSku");//线下单 order.TradeType == "OfflineNoSku"的不插入商品数据

        //    if (orders == null || orders.Any() == false) return;

        //    SyncOrderLog.Debug($"开始检查订单商品", SiteContext.Current.CurrentFxUserId, orders.First().ShopId);

        //    1.查询订单的商品
        //    var productIds = orders.SelectMany(f => f.OrderItems).Where(oi => !oi.ProductID.IsNullOrEmpty()).Select(f => f.ProductID).Distinct().ToList();
        //    var shopIds = orders.Select(o => o.ShopId).Distinct().ToList();

        //    var queryModels = new List<ProductFxQueryModel>();
        //    shopIds.ForEach(sid =>
        //    {
        //        var tempPids = orders.Where(a => a.ShopId == sid)?.SelectMany(f => f.OrderItems).Where(oi => !oi.ProductID.IsNullOrEmpty()).Select(f => f.ProductID).Distinct().ToList();
        //        if (tempPids != null && tempPids.Any())
        //        {
        //            queryModels.Add(new ProductFxQueryModel { ShopId = sid, ProductIds = tempPids });
        //        }
        //    });

        //    var fields = isOfflineOrder ? "" : "p.Id,p.PlatformId,p.ShopId,p.SourceUserId,sku.Id,sku.SkuId,sku.SkuCode";
        //    var products = _prodcutFxService.GetProductListV2(queryModels, fields);

        //    2.找出不存在的商品,或者SKU
        //   var ois = orders.SelectMany(f => f.OrderItems).GroupBy(f => f.SkuCode).Select(f => f.FirstOrDefault()); //汇总所有的oi
        //    var notExistProductOis = new List<OrderItem>(); //商品不存在的oi
        //    var notExistSkuDict = new Dictionary<ProductFx, List<OrderItem>>();//sku不存在的产品
        //    CheckOrderItemProduct(ois, products, out notExistProductOis, out notExistSkuDict);

        //    #region 更新线下单商品信息
        //    if (isOfflineOrder && isFromBulkMergerFx)
        //    {
        //        var oiSkuDic = ois.GroupBy(x => x.ProductID).ToDictionary(x => x.Key, x => x.GroupBy(y => y.SkuID).SelectMany(y => y));
        //        var existProducts = products.Where(p => oiSkuDic.ContainsKey(p.PlatformId)).ToList();
        //        var needUpdateProducts = new List<ProductFx>();
        //        foreach (var p in existProducts)
        //        {
        //            var isNeedUpdate = false;
        //            var oiProduct = oiSkuDic[p.PlatformId].First();
        //            if (p.CargoNumber != oiProduct.productCargoNumber)
        //            {
        //                p.CargoNumber = oiProduct.productCargoNumber;
        //                isNeedUpdate = true;
        //            }
        //            if (p.Subject != oiProduct.ProductSubject)
        //            {
        //                p.Subject = oiProduct.ProductSubject;
        //                isNeedUpdate = true;
        //            }
        //            var oiSkus = oiSkuDic[p.PlatformId].ToList();
        //            if (oiSkus.Any())
        //            {
        //                foreach (var sku in oiSkus)
        //                {
        //                    var oldSku = p.Skus.FirstOrDefault(x => x.SkuId == sku.SkuID);
        //                    if (oldSku == null)
        //                        continue;

        //                    if (oldSku.CargoNumber != sku.CargoNumber)
        //                    {
        //                        oldSku.CargoNumber = sku.CargoNumber;
        //                        isNeedUpdate = true;
        //                    }
        //                    if (oldSku.Name != sku.Color)
        //                    {
        //                        oldSku.Name = sku.Color;
        //                        oldSku.AttributeValue1 = sku.Color;
        //                        isNeedUpdate = true;
        //                    }
        //                    if (oldSku.SalePrice != sku.Price)
        //                    {
        //                        oldSku.SalePrice = sku.Price;
        //                        isNeedUpdate = true;
        //                    }
        //                    if (oldSku.ImgUrl != sku.ProductImgUrl)
        //                    {
        //                        oldSku.ImgUrl = sku.ProductImgUrl;
        //                        isNeedUpdate = true;
        //                    }
        //                }
        //            }

        //            if (isNeedUpdate)
        //                needUpdateProducts.Add(p);
        //        }

        //        needUpdateProducts.GroupBy(f => f.ShopId).ToList().ForEach(g =>
        //        {
        //            _prodcutFxService.BulkMerger(g.ToList(), g.Key);
        //        });
        //    }
        //    #endregion

        //    都存在，不补商品
        //    if (notExistProductOis.Any() == false && notExistSkuDict.Any() == false)
        //        return;

        //    3.同步不存在的商品
        //    var syncProducts = new List<ProductFx>();
        //    var waitSyncProductOis = notExistProductOis.Concat(notExistSkuDict.SelectMany(f => f.Value));
        //    var waitSyncProductGroups = waitSyncProductOis.GroupBy(f => f.ShopId);
        //    var sids = waitSyncProductGroups.Select(f => f.Key).ToList();
        //    var shops = _shopService.GetShopByIds(sids);
        //    try
        //    {
        //        var fxUserId = SiteContext.Current.CurrentFxUserId;
        //        if (!isOfflineOrder)
        //        {
        //            非线下单才通过接口同步商品信息
        //            waitSyncProductGroups.ToList().ForEach(g =>
        //            {
        //                var sid = g.Key;
        //                var pids = g.Select(f => f.ProductID).Distinct();
        //                var shop = shops.FirstOrDefault(f => f.Id == sid);
        //                同步产品
        //                var syncFxProductService = new SyncFxProductService(fxUserId);
        //                var syncResult = syncFxProductService.SyncProduct(string.Join(",", pids), shop);
        //                if (syncResult.Any())
        //                    syncProducts.AddRange(syncResult);
        //            });
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriteError("检查订单商品信息后，同步缺失的产品报错：" + ex.ToString());
        //    }

        //    4.判断是否同步到了产品，未同步到，再根据订单项信息补填产品信息
        //    CheckOrderItemProduct(waitSyncProductOis, syncProducts, out notExistProductOis, out notExistSkuDict);

        //    同步到了，直接返回
        //    if (notExistProductOis.Any() == false && notExistSkuDict.Any() == false)
        //        return;

        //    5.补商品数据
        //    var needAddProductsDict = new Dictionary<string, ProductFx>();//需要更新或者插入的商品
        //    商品不存在
        //    if (notExistProductOis.Any())
        //    {
        //        var orderPlatformTypeDict = orders.GroupBy(x => x.OrderCode).ToDictionary(o => o.Key, o => o.FirstOrDefault().PlatformType); //订单的平台
        //        补商品
        //        foreach (var oi in notExistProductOis)
        //        {
        //            判断商品是否已生成
        //            if (!needAddProductsDict.ContainsKey(oi.ProductCode))
        //            {
        //                var p = OrderItemToProductFx(oi, orderPlatformTypeDict[oi.OrderCode]);
        //                needAddProductsDict.Add(p.ProductCode, p);
        //            }
        //            else
        //            {
        //                var p = needAddProductsDict[oi.ProductCode];
        //                var sku = OrderItemToFxProductSku(oi, oi.ProductID, p.CreateBy);
        //                p.Skus.Add(sku);
        //            }
        //        }
        //    }
        //    sku不存在
        //    if (notExistSkuDict.Any())
        //    {
        //        补商品SKU
        //        foreach (var dict in notExistSkuDict)
        //        {
        //            var p = dict.Key; //存在的产品
        //            var pois = dict.Value;  //缺失sku的oi

        //            1.生成sku
        //            var skuList = new List<ProductSkuFx>();
        //            foreach (var oi in pois)
        //            {
        //                var sku = OrderItemToFxProductSku(oi, oi.ProductID, p.CreateBy);
        //                if (skuList.Any(x => x.PlatformId == oi.ProductID && x.SkuId == oi.SkuID) == false)
        //                    skuList.Add(sku);
        //            }

        //            2.判断商品是否已生成
        //            ProductFx pModel;
        //            if (needAddProductsDict.TryGetValue(p.ProductCode, out pModel))
        //            {
        //                pModel.Skus.AddRange(skuList);
        //            }
        //            else
        //            {
        //                pModel = p;
        //                if (pModel.Skus == null) pModel.Skus = new List<ProductSkuFx>();
        //                pModel.Skus.AddRange(skuList);
        //                needAddProductsDict.Add(pModel.ProductCode, pModel);
        //            }
        //        }
        //    }
        //    插入或者更新商品
        //    if (!needAddProductsDict.Any())
        //        return;

        //    //TODO:调试
        //    Log.WriteError("needAddProductsDict:" + needAddProductsDict.ToJson());
        //    Log.WriteError("notExistProductOis:" + needAddProductsDict.ToJson());
        //    Log.WriteError("notExistSkuDict:" + needAddProductsDict.ToJson());

        //    needAddProductsDict.Values.GroupBy(f => f.ShopId).ToList().ForEach(g =>
        //    {
        //        var shop = shops.FirstOrDefault(f => f.Id == g.Key);
        //        _prodcutFxService.BulkMerger(g.ToList(), shop.Id);
        //    });
        //}

        ///// <summary>
        ///// 订单项转换为分销产品【移至CostOrderService】
        ///// </summary>
        ///// <param name="oi"></param>
        ///// <returns></returns>
        //private ProductFx OrderItemToProductFx(OrderItem oi, string platformType)
        //{
        //    var p = new ProductFx();
        //    p.CargoNumber = oi.productCargoNumber ?? "";
        //    p.CreateBy = oi.UserId;
        //    p.CreateTime = DateTime.Now;
        //    p.ImageUrl = oi.ProductImgUrl;
        //    p.PlatformId = oi.ProductID;
        //    p.PlatformType = platformType;//orderPlatformTypeDict[oi.OrderCode];
        //    p.ShopId = oi.ShopId;
        //    //p.ProductCode = (p.PlatformId + p.ShopId).ToShortMd5();
        //    p.SourceUserId = oi.UserId;
        //    p.SystemStatus = 0;
        //    p.Subject = oi.ProductSubject;
        //    p.UpdateTime = DateTime.Now;

        //    p.ListingTime = null;
        //    p.Status = string.Empty;// oi.;
        //    p.SystemCategoryId = string.Empty;// oi.;
        //    p.CategoryId = string.Empty;//oi.;
        //    p.CategoryName = string.Empty;// oi.;
        //    p.ProductSelfCode = string.Empty;// oi.;

        //    p.From = "Order";

        //    var sku = OrderItemToFxProductSku(oi, oi.ProductID, p.CreateBy);

        //    p.Skus = new List<ProductSkuFx>() { sku };

        //    return p;
        //}

        ///// <summary>
        ///// 转换ProductSkuFx 【移至CostOrderService】
        ///// </summary>
        ///// <param name="oi"></param>
        ///// <param name="productId"></param>
        ///// <param name="createBy"></param>
        ///// <returns></returns>
        //private ProductSkuFx OrderItemToFxProductSku(OrderItem oi, string productId, int createBy)
        //{
        //    var sku = new ProductSkuFx();

        //    sku.ImgUrl = oi.ProductImgUrl;
        //    sku.PlatformId = productId;
        //    sku.ProductCode = oi.ProductCode;
        //    sku.SalePrice = oi.Price;

        //    sku.SkuId = oi.SkuID ?? productId;
        //    sku.SkuCode = CustomerConfig.GetFxSkuCode(productId, sku.SkuId, oi.ShopId, oi.UserId);
        //    sku.SkuSelfCode = string.Empty;
        //    sku.SkuBarCode = string.Empty;
        //    sku.PurchasePrice = null;
        //    sku.UpdateBy = createBy;
        //    sku.AvailableCount = 0;
        //    sku.CargoNumber = oi.CargoNumber;
        //    sku.CreateBy = createBy;
        //    sku.CreateTime = DateTime.Now;
        //    sku.UpdateTime = DateTime.Now;
        //    sku.AttributeName1 = string.Empty;
        //    sku.AttributeName2 = string.Empty;
        //    sku.AttributeName3 = string.Empty;
        //    sku.AttributeValue1 = oi.Color;
        //    sku.AttributeValue2 = oi.Size;
        //    sku.AttributeValue3 = string.Empty;

        //    sku.Name = oi.Color + oi.Size;

        //    sku.From = "Order";

        //    return sku;
        //}

        ///// <summary>
        ///// 检查订单项的产品是否存在 【移至CostOrderService】
        ///// 分两种情况：
        ///// 1.产品不存在
        ///// 2.sku不存在
        ///// <paramref name="orderItems">需要检查的订单项</paramref>
        ///// <paramref name="orderItems">订单项上的产品Id对应数据库中的产品数据</paramref>
        ///// <paramref name="notExistProductOis">订单项对应产品不存在</paramref>
        ///// <paramref name="notExistSkuDict">订单项对应Sku不存在</paramref>
        ///// </summary>
        //private void CheckOrderItemProduct(IEnumerable<OrderItem> orderItems, List<ProductFx> products, out List<OrderItem> notExistProductOis, out Dictionary<ProductFx, List<OrderItem>> notExistSkuDict)
        //{
        //    notExistProductOis = new List<OrderItem>(); //商品不存在的oi
        //    notExistSkuDict = new Dictionary<ProductFx, List<OrderItem>>();//sku不存在的产品

        //    foreach (var oi in orderItems)
        //    {
        //        //产品信息需保证一个产品ID（ProductID在一个店铺是里是唯一的）
        //        var p = products.FirstOrDefault(f => f.ProductCode == oi.ProductCode);
        //        if (p == null)
        //        {
        //            //产品不存在
        //            notExistProductOis.Add(oi);
        //        }
        //        else if (p.Skus.Any(f => f.SkuId == oi.SkuID) == false)
        //        {
        //            //sku不存在
        //            List<OrderItem> notFoundSkuOrderItemList;
        //            if (!notExistSkuDict.TryGetValue(p, out notFoundSkuOrderItemList))
        //            {
        //                notFoundSkuOrderItemList = new List<OrderItem>() { oi };
        //                notExistSkuDict.Add(p, notFoundSkuOrderItemList);
        //            }
        //            else
        //            {
        //                notFoundSkuOrderItemList.Add(oi);
        //            }
        //        }
        //    }
        //}

        ///// <summary>
        ///// 更新逻辑单的信息【移至CostOrderService】
        ///// </summary>
        ///// <param name="orders"></param>
        ////private void UpdateLogicOrderStatus(IEnumerable<Order> orders)
        //public void UpdateLogicOrder(IEnumerable<Order> orders)
        //{
        //    if (orders == null || orders.Any() == false) return;
        //    var orderFirst = orders.FirstOrDefault();
        //    var pts = orders.Select(x => x.PlatformType).Distinct().ToList();
        //    var extFields = "";
        //    var hasLastShipTimePlatforms = CustomerConfig.HasLastShipTimePlatformType();
        //    var needAddTagPlatformTypes = CustomerConfig.ReceiverChangeNeedAddTagPlatformType();
        //    var splitNeedHandleTagPlatformType = CustomerConfig.SplitNeedHandleTagPlatformType;
        //    var hasLastShipOrder = pts.Any(x => hasLastShipTimePlatforms.Contains(x));
        //    if (hasLastShipOrder)
        //        extFields = ",o.LastShipTime";
        //    extFields += ",o.PrintState";

        //    //var apiOrderDict = orders.ToDictionary(f => f.OrderCode, f => f);
        //    var updateAddrOrderDic = new Dictionary<string, LogicOrder>();
        //    var logicOrderFields = $@"o.Id,o.PlatformOrderId,o.ShopId,o.PlatformType,o.LogicOrderId,o.OrderCode,o.MergeredType,o.MergeredOrderId,o.ErpState,o.ExceptionStatus,o.ExceptionReason,o.ErpRefundState, 
        //                             o.FxUserId, o.PathFlowCode, o.ToName, o.ToPhone, o.ToProvince, o.ToCity, o.ToCounty, o.ToTown, o.ToAddress,o.ToFullAddress,o.ToShortAddress {extFields},
        //                             o.BuyerRemark,o.SellerRemark,o.SellerRemarkFlag,o.DecryptField,
        //                             o.OrderBuyerHashCode,o.BuyerHashCodeV2,o.ReceiverHashCode,o.ToPhoneIndex,o.LastWaybillCode".Split(",".ToArray()).ToList();
        //    if (orderFirst.PlatformType == PlatformType.XiaoHongShu.ToString())
        //        logicOrderFields.Add("o.ExtField1");
        //    if (orderFirst.PlatformType == PlatformType.WxVideo.ToString())
        //    {
        //        logicOrderFields.Add("o.ExtField1");
        //        logicOrderFields.Add("o.ExtField2");
        //    }
        //    if (orderFirst.PlatformType == PlatformType.TouTiao.ToString())
        //    {
        //        logicOrderFields.Add("o.ExtField2");
        //    }

        //    var logicOrderItemFields = "oi.Id,oi.OrderItemCode,oi.ProductCode,oi.SkuCode".Split(",".ToArray()).ToList();
        //    logicOrderFields.AddRange(logicOrderItemFields);
        //    //1.查询出逻辑订单
        //    //var orderCodes = orders.Select(f => f.OrderCode);
        //    //var logicOrders = _logicOrderService.GetOrdersByOrderCode(orderCodes,
        //    //                    isNeedProduct: false,
        //    //                    fields: logicOrderFields);
        //    var pids = orders.Select(x => x.PlatformOrderId).Distinct().ToList();
        //    var sids = orders.Select(x => x.ShopId).Distinct().ToList();
        //    //只取打码收件人数据
        //    var logicOrders = _logicOrderService.GetOrderByPlatformOrderIds(pids, sids: sids, isNeedProduct: false, fields: logicOrderFields, queryReceiver: new QueryReceiverModel { IsOnlyGetMask = true, IsFromApi = true });
        //    if (logicOrders.Any() == false)
        //        return;

        //    ////2.查询出合并订单
        //    //var logicMegeredOrderIds = logicOrders.Where(f => f.IsChildOrder && f.MergeredOrderId.IsNotNullOrEmpty()).Select(f => f.MergeredOrderId).Distinct();
        //    //var megeredLogicOrders = _logicOrderService.GetOrders(logicMegeredOrderIds,
        //    //                    isNeedProduct: false,
        //    //                    fields: logicOrderFields);

        //    var orderItemCodeDic = orders.SelectMany(f => f.OrderItems).GroupBy(x => x.OrderItemCode).ToDictionary(x => x.Key, x => x.ToList());
        //    var megeredLogicOrders = new List<LogicOrder>();
        //    var waitsellersendStatus = new List<string> { "waitsellersend" }; // "inrefund"
        //    var waitsendAndInrefundStatus = new List<string> { "waitsellersend", "inrefund" };
        //    //3.重新计算状态,且更新逻辑单的备注，未打印的逻辑订单的收件信息
        //    var updateFieldValLst = new List<LogicOrderChangeModel>();
        //    var pathChangeDic = new Dictionary<string, LogicOrderPathChangeModel>();
        //    var needUpdateLogicOrders = new List<LogicOrder>();
        //    var logicOrderList = logicOrders.Concat(megeredLogicOrders);
        //    //var updateForSettlementOrderItems = new List<OrderItem>();
        //    var UpdateForSkuCodeLogicOrderItems = new List<LogicOrderItem>();

        //    //var guid = Guid.NewGuid().ToString("N");
        //    //查出合单-->所有子单
        //    var platformOrderIds = logicOrderList.Where(x => x.IsMainOrder && needAddTagPlatformTypes.Contains(x.PlatformType)).Select(x => x.PlatformOrderId).ToList() ;
        //    var receiverChangeOrderDic = new Dictionary<string,List<LogicOrder>>();
        //    //1688分销业务新增：收集【接口订单是部分发货货已发货】且【数据库订单是待发货】,Key:LogicOrderId,Value:Order
        //    var changeToSendedOrders = new Dictionary<string,Order>();
        //    var fx1688SupportPlatformTypes = CustomerConfig.Fx1688SupportPlatformTypes;

        //    foreach (var logicOrder in logicOrderList)
        //    {
        //        var updateFields = new List<string>();
        //        //3.1 找出逻辑单的订单项
        //        var orderItemCodes = logicOrder.LogicOrderItems.Select(f => f.OrderItemCode);
        //        //var orderItems = orders.SelectMany(f => f.OrderItems).Where(f => orderItemCodes.Contains(f.OrderItemCode)).ToList();
        //        var orderItems = new List<OrderItem>();
        //        foreach (var code in orderItemCodes)
        //        {
        //            var ois = new List<OrderItem>();
        //            if (orderItemCodeDic.TryGetValue(code, out ois))
        //                orderItems.AddRange(ois);
        //        }

        //        //合单：追加合单另外的子单项
        //        if (logicOrder.IsMainOrder)
        //        {
        //            logicOrder.LogicOrderItems?.ForEach(item =>
        //            {
        //                if (!orderItems.Any(a => a.OrderItemCode == item.OrderItemCode))
        //                    orderItems.Add(new OrderItem
        //                    {
        //                        PlatformOrderId = item.PlatformOrderId,
        //                        SubItemID = item.SubItemId,
        //                        ShopId = item.ShopId,
        //                        Status = logicOrder.ErpState,
        //                        RefundStatus = logicOrder.ErpRefundState
        //                    });
        //            });
        //        }

        //        if (orderItems.Any() == false)
        //            continue;

        //        var pathChangeModel = new LogicOrderPathChangeModel();
        //        if (orderItemCodes.Count() != orderItems.Count)
        //        {
        //            var orignalOrderItemCodes = orderItems.Select(f => f.OrderItemCode).ToList();
        //            var notFoundLogicOrderItemCodes = orderItemCodes.Where(f => orignalOrderItemCodes.Contains(f) == false);
        //            //SyncOrderLog.Debug($"异常：逻辑订单【{logicOrder.LogicOrderId}】的订单项Code【{string.Join(",", notFoundLogicOrderItemCodes)}】对应的原始订单项未找到。", SiteContext.Current.CurrentFxUserId, orders.First().ShopId);
        //        }

        //        //3.2 重新计算状态
        //        bool needUpdate = false;
        //        var erpExceptionStatus = GetLogicExceptionStatus(orderItems); //异常状态
        //        if (logicOrder.ExceptionStatus != erpExceptionStatus.Key)
        //        {
        //            needUpdate = true;
        //            if (logicOrder.ExceptionStatus != 999 && logicOrder.ExceptionStatus != -1 && logicOrder.ExceptionStatus != -2)
        //            {
        //                updateFields.Add("ExceptionStatus");
        //                logicOrder.ExceptionStatus = erpExceptionStatus.Key;//厂家或操作过的异常单不更新异常状态
        //            }
        //            updateFields.Add("ExceptionReason");
        //            logicOrder.ExceptionReason = erpExceptionStatus.Value;
        //        }

        //        var isUpdateStatus = false;
        //        //Erp状态
        //        var newErpStatus = GetLogicErpStatus(orderItems);
        //        //退款状态
        //        var refundStatus = BasePlatformService.TransferOrderRefundStatus(new Order() { OrderItems = orderItems }).RefundStatus;
        //        var isNeedUpdateBuyerHashCode = false;
        //        var apiOrder = orders.FirstOrDefault(f => f.OrderCode == logicOrder.OrderCode);
        //        if (logicOrder.ErpState != newErpStatus)
        //        {
        //            //增加异常订单处理，发送异常订单处理消息(zhb)
        //            //系统中打印状态：已打印，订单状态：待发货，同步最新订单状态：非待发货
        //            if (logicOrder.PrintState >= 1 && logicOrder.ErpState ==
        //                "waitsellersend" && newErpStatus != "waitsellersend")
        //            {
        //                var pathNodes = new OrderCheckRepository().GetPathFlowByPathFlowCodes(new List<string> { logicOrder.PathFlowCode });
        //                OrderAbnormalMessageService.SendMessage(new OrderAbnormalMessageModel
        //                {
        //                    FxUserId = logicOrder.FxUserId,
        //                    ShopId = logicOrder.ShopId,
        //                    AbnormalSource = AbnormalOrderSources.SyncOrder,
        //                    AbnormalType = string.IsNullOrWhiteSpace(refundStatus)
        //                        ? AbnormalOrderTypes.NonDgjShip
        //                        : AbnormalOrderTypes.NonDgjShipAfterSale,
        //                    LogicOrderId = logicOrder.LogicOrderId,
        //                    PlatformOrderId = logicOrder.PlatformOrderId,
        //                    PathFlowCode = logicOrder.PathFlowCode,
        //                    PlatformType = logicOrder.PlatformType,
        //                    AbnormalTime = DateTime.Now,
        //                    Logistics = orders.FirstOrDefault(m => m.PlatformOrderId == logicOrder.PlatformOrderId)
        //                        ?.LogisticsInfos,
        //                    PathFlowNodes = pathNodes.FirstOrDefault()?.PathFlowNodes?.Select(m =>
        //                        new OrderAbnormalPathFlowNode
        //                        {
        //                            UniqueKey = m.UniqueKey,
        //                            PathFlowCode = m.PathFlowCode,
        //                            PathFlowNodeCode = m.PathFlowNodeCode,
        //                            FxUserId = m.FxUserId,
        //                            UpFxUserId = m.UpFxUserId,
        //                            DownFxUserId = m.DownFxUserId,
        //                            Status = m.Status,
        //                            CreateTime = DateTime.Now
        //                        }).ToList()
        //                });
        //            }
        //            ////zouyi 2021/02/23 需求变更：微信小商店逻辑单待发货状态由业务操作【标记发货】操作来变更；增量同步不自动从待发货状态变为其他状态，“退款中”除外。
        //            //if (logicOrder.PlatformType == PlatformType.WxXiaoShangDian.ToString() && logicOrder.ErpState == "waitsellersend" && newErpStatus != "inrefund")
        //            //    needUpdate = false;
        //            //else
        //            //    needUpdate = true;
        //            isUpdateStatus = true;
        //            needUpdate = true;
        //            if (needUpdate)
        //            {
        //                updateFields.Add("ErpState");
        //                //状态是待发货、退款关闭（用户取消退款=》代发货）都需要更新一下BuyerHashCode
        //                if (logicOrder.ErpState == "waitsellersend" || logicOrder.ErpState == "inrefund")
        //                    isNeedUpdateBuyerHashCode = true;
        //                if (newErpStatus == "waitsellersend" && updateAddrOrderDic.ContainsKey(logicOrder.LogicOrderId) == false) // 有其他状态改为待发货状态需要重新推单
        //                    updateAddrOrderDic.Add(logicOrder.LogicOrderId, logicOrder);
        //                //订单由退款或待发货状态，变更为已发货状态
        //                if ((newErpStatus == "sended" || newErpStatus == "success")
        //                    && fx1688SupportPlatformTypes.Contains(logicOrder.PlatformType)
        //                    && (logicOrder.ErpState == "inrefund" || logicOrder.ErpState == "waitsellersend") 
        //                    && changeToSendedOrders.ContainsKey(logicOrder.LogicOrderId) == false
        //                    && apiOrder != null)
        //                {
        //                    apiOrder.LogicLastWaybillCode = logicOrder.LastWaybillCode;
        //                    apiOrder.LogicOrderId = logicOrder.LogicOrderId;
        //                    changeToSendedOrders.Add(logicOrder.LogicOrderId, apiOrder);
        //                }
        //                logicOrder.ErpState = newErpStatus;
        //            }
        //        }

        //        //补回流记录：不考虑数据库现有的状态
        //        if ((newErpStatus == "sended" || newErpStatus == "success")
        //                    && fx1688SupportPlatformTypes.Contains(logicOrder.PlatformType)
        //                    && changeToSendedOrders.ContainsKey(logicOrder.LogicOrderId) == false
        //                    && apiOrder != null)
        //        {
        //            apiOrder.LogicLastWaybillCode = logicOrder.LastWaybillCode;
        //            apiOrder.LogicOrderId = logicOrder.LogicOrderId;
        //            changeToSendedOrders.Add(logicOrder.LogicOrderId, apiOrder);
        //        }

        //        logicOrder.ErpRefundState = logicOrder.ErpRefundState ?? "";
        //        if (logicOrder.ErpRefundState != refundStatus)
        //        {
        //            //增加异常订单处理，发送异常订单处理消息(zhb)
        //            //系统中打印状态：已打印，订单状态：待发货，同步最新订单状态：待发货，退款状态：退款中，退款关闭
        //            if (logicOrder.PrintState >= 1 && logicOrder.ErpState ==
        //                "waitsellersend" && newErpStatus == "waitsellersend" &&
        //                !string.IsNullOrWhiteSpace(refundStatus))
        //            {
        //                var pathNodes =
        //                    new OrderCheckRepository().GetPathFlowByPathFlowCodes(new List<string>
        //                        { logicOrder.PathFlowCode });
        //                OrderAbnormalMessageService.SendMessage(new OrderAbnormalMessageModel
        //                {
        //                    FxUserId = logicOrder.FxUserId,
        //                    ShopId = logicOrder.ShopId,
        //                    AbnormalSource = AbnormalOrderSources.SyncOrder,
        //                    AbnormalType = AbnormalOrderTypes.NonDgjShipAfterSale,
        //                    LogicOrderId = logicOrder.LogicOrderId,
        //                    PlatformOrderId = logicOrder.PlatformOrderId,
        //                    PathFlowCode = logicOrder.PathFlowCode,
        //                    PlatformType = logicOrder.PlatformType,
        //                    AbnormalTime = DateTime.Now,
        //                    Logistics = orders.FirstOrDefault(m => m.PlatformOrderId == logicOrder.PlatformOrderId)
        //                        ?.LogisticsInfos,
        //                    PathFlowNodes = pathNodes.FirstOrDefault()?.PathFlowNodes?.Select(m =>
        //                        new OrderAbnormalPathFlowNode
        //                        {
        //                            UniqueKey = m.UniqueKey,
        //                            PathFlowCode = m.PathFlowCode,
        //                            PathFlowNodeCode = m.PathFlowNodeCode,
        //                            FxUserId = m.FxUserId,
        //                            UpFxUserId = m.UpFxUserId,
        //                            DownFxUserId = m.DownFxUserId,
        //                            Status = m.Status,
        //                            CreateTime = DateTime.Now
        //                        }).ToList()
        //                });
        //            }

        //            isUpdateStatus = true;
        //            needUpdate = true;
        //            updateFields.Add("ErpRefundState");
        //            logicOrder.ErpRefundState = refundStatus;
        //        }

        //        foreach (var item in orderItems)
        //        {
        //            //if (isUpdateStatus && updateForSettlementOrderItems.Any(x => x.OrderItemCode == item.OrderItemCode) == false)
        //            //    updateForSettlementOrderItems.Add(item);
        //            // 待发货订单修改规格后需要更新SkuCode
        //            if (waitsendAndInrefundStatus.Contains(logicOrder.ErpState)) 
        //            {
        //                var loi = logicOrder.LogicOrderItems.FirstOrDefault(x => x.OrderItemCode == item.OrderItemCode);
        //                if (loi != null && loi.SkuCode != item.SkuCode)
        //                {
        //                    loi.LogicOrderId = logicOrder.LogicOrderId;
        //                    loi.SkuCode = item.SkuCode;
        //                    loi.ItemCount = item.Count;
        //                    loi.ItemColor = item.Color;
        //                    loi.ItemSize = item.Size;
        //                    UpdateForSkuCodeLogicOrderItems.Add(loi);
        //                    // 多厂家：提取SkuCode改变的订单，后面逻辑判断是否需要修改新厂家
        //                    if (waitsellersendStatus.Contains(newErpStatus) && updateAddrOrderDic.ContainsKey(logicOrder.LogicOrderId) == false)
        //                        updateAddrOrderDic.Add(logicOrder.LogicOrderId, logicOrder);
        //                }
        //            }
        //        }

        //        //3.3 覆盖逻辑单的收件信息（和打单系统的处理方式一样）
        //        if (apiOrder == null)
        //            continue;
        //        FixNullValueToEmpty(apiOrder, logicOrder);

        //        var isReceiverChange = false;
        //        var addrIsChange = false; //地址是否更新
        //        var isShortAddrChange = false;
        //        //2021/09/18/zouyi:头条平台 加密信息 按加密索引判断是否有变化（头条平台订单每次请求加密数据都会变化，加密索引不会变）
        //        if (apiOrder.PlatformType == PlatformType.TouTiao.ToString())
        //        {
        //            //收件人姓名加密索引判断
        //            var apiToName = apiOrder.ToName.IsDyEncryptData() ? apiOrder.ToName.ExtractDySearchIndex() : apiOrder.ToName; //api 数据
        //            var loToName = logicOrder.ToName.IsDyEncryptData() ? logicOrder.ToName.ExtractDySearchIndex() : logicOrder.ToName; // db 数据
        //            if (apiToName.IsNotNullOrEmpty() && loToName != apiToName)
        //            {
        //                updateFields.Add("ToName");
        //                logicOrder.ToName = apiOrder.ToName; //收件人更新
        //                isNeedUpdateBuyerHashCode = true;
        //            }
        //            //收件人电话加密索引判断
        //            var toMobile = apiOrder.ToMobile.IsNullOrEmpty() ? apiOrder.ToPhone : apiOrder.ToMobile; //api 数据
        //            var apiToMobile = toMobile.IsDyEncryptData() ? toMobile.ExtractDySearchIndex() : toMobile;
        //            var loToPone = logicOrder.ToPhone.IsDyEncryptData() ? logicOrder.ToPhone.ExtractDySearchIndex() : logicOrder.ToPhone; // db 数据
        //            if (apiToMobile.IsNotNullOrEmpty() && loToPone != apiToMobile)
        //            {
        //                updateFields.Add("ToPhone");
        //                logicOrder.ToPhone = toMobile; //收件人电话更新
        //                isNeedUpdateBuyerHashCode = true;
        //            }
        //            //收件人地址加密索引判断
        //            var apiToAddress = apiOrder.ToAddress.IsDyEncryptData() ? apiOrder.ToAddress.ExtractDySearchIndex() : apiOrder.ToAddress; //api 数据
        //            var loToAddress = logicOrder.ToAddress.IsDyEncryptData() ? logicOrder.ToAddress.ExtractDySearchIndex() : logicOrder.ToAddress; // db 数据
        //            if (apiToAddress.IsNotNullOrEmpty() && loToAddress != apiToAddress)
        //            {
        //                addrIsChange = true;
        //                updateFields.Add("ToAddress");
        //                logicOrder.ToAddress = apiOrder.ToAddress;
        //            }
        //            else
        //            {
        //                //历史数据没有镇，当再次更新订单时需补充上
        //                var oldPrefix = logicOrder.ToFullAddress?.Split('#')?.FirstOrDefault();
        //                var newPrefix = apiOrder.ToFullAddress?.Split('#')?.FirstOrDefault();
        //                if (oldPrefix != newPrefix)
        //                {
        //                    addrIsChange = true;
        //                    updateFields.Add("ToAddress");
        //                    logicOrder.ToAddress = apiOrder.ToAddress;
        //                }
        //            }
        //            //收件人标识open_address_id判断
        //            var apiDecryptField = apiOrder.ExtField3; //api 数据
        //            var loDecryptField = logicOrder.DecryptField; // db 数据
        //            if (apiDecryptField.IsNotNullOrEmpty() && loDecryptField != apiDecryptField)
        //            {
        //                if(loDecryptField.IsNotNullOrEmpty())
        //                    addrIsChange = true;
        //                updateFields.Add("DecryptField");
        //                logicOrder.DecryptField = apiOrder.ExtField3;
        //                isNeedUpdateBuyerHashCode = true;
        //            }

        //            if(logicOrder.ExtField2 != apiOrder.ExtField2)
        //            {
        //                updateFields.Add("ExtField2");
        //                logicOrder.ExtField2 = apiOrder.ExtField2;
        //                isNeedUpdateBuyerHashCode = true;
        //            }
        //        }
        //        else if (apiOrder.PlatformType == PlatformType.XiaoHongShu.ToString() && waitsendAndInrefundStatus.Contains(logicOrder.ErpState) == false)
        //        {
        //            //如果收件人信息一致,接口状态不是代发货,收件人信息就会返回****,数据库是旧数据就不修改
        //        }
        //        else
        //        {
        //            //非头条平台，判断变化，走之前的逻辑
        //            if (apiOrder.ToName.IsNotNullOrEmpty() && (logicOrder.ToName != apiOrder.ToName || logicOrder.ToName != logicOrder.ToOrderName))
        //            {
        //                updateFields.Add("ToName");
        //                logicOrder.ToName = apiOrder.ToName; //收件人更新
        //                isNeedUpdateBuyerHashCode = true;
        //            }
        //            var apiToMobile = apiOrder.ToMobile.IsNullOrEmpty() ? apiOrder.ToPhone : apiOrder.ToMobile;
        //            if (apiToMobile.IsNotNullOrEmpty() && (logicOrder.ToPhone != apiToMobile || logicOrder.ToPhone != logicOrder.ToOrderPhone))
        //            {
        //                updateFields.Add("ToPhone");
        //                logicOrder.ToPhone = apiToMobile; //收件人电话更新
        //                isNeedUpdateBuyerHashCode = true;
        //            }
        //            if (apiOrder.ToAddress.IsNotNullOrEmpty() && (logicOrder.ToAddress != apiOrder.ToAddress || logicOrder.ToAddress != logicOrder.ToOrderAddress))
        //            {
        //                addrIsChange = true;
        //                updateFields.Add("ToAddress");
        //                logicOrder.ToAddress = apiOrder.ToAddress;
        //            }
        //        }

        //        if (apiOrder.ToPhoneIndex.IsNotNullOrEmpty() && logicOrder.ToPhoneIndex != apiOrder.ToPhoneIndex)
        //        {
        //            updateFields.Add("ToPhoneIndex");
        //            logicOrder.ToPhoneIndex = apiOrder.ToPhoneIndex;
        //            isNeedUpdateBuyerHashCode = true;
        //        }

        //        if (!string.IsNullOrEmpty(apiOrder.ReceiverHashCode) && apiOrder.ReceiverHashCode != logicOrder.ReceiverHashCode)
        //        {
        //            addrIsChange = true;
        //            isReceiverChange = true;
        //            updateFields.Add("ReceiverHashCode");
        //            logicOrder.ReceiverHashCode = apiOrder.ReceiverHashCode; //收件人HashCode
        //            isNeedUpdateBuyerHashCode = true;
        //        }
        //        if (!string.IsNullOrEmpty(apiOrder.BuyerHashCodeV2) && apiOrder.BuyerHashCodeV2 != logicOrder.OrderBuyerHashCode)
        //        {
        //            addrIsChange = true;
        //            updateFields.Add("OrderBuyerHashCode");
        //            logicOrder.OrderBuyerHashCode = apiOrder.BuyerHashCodeV2; //平台单BuyerHashCode
        //            isNeedUpdateBuyerHashCode = true;
        //        }

        //        if (apiOrder.ToProvince.IsNotNullOrEmpty() && logicOrder.ToProvince != apiOrder.ToProvince)
        //        {
        //            if (pathChangeDic.TryGetValue(logicOrder.LogicOrderId, out pathChangeModel) == false)
        //            {
        //                pathChangeModel = new LogicOrderPathChangeModel
        //                {
        //                    LogicOrderId = logicOrder.LogicOrderId,
        //                    PlatformOrderId = logicOrder.PlatformOrderId,
        //                    Old = new LogicOrderPathChangeLogModel { Province = logicOrder.ToProvince, City = logicOrder.ToCity, Code = logicOrder.PathFlowCode },
        //                    New = new LogicOrderPathChangeLogModel { Province = apiOrder.ToProvince, City = apiOrder.ToCity },
        //                };
        //                pathChangeDic.Add(logicOrder.LogicOrderId, pathChangeModel);
        //            }
        //            else
        //            {
        //                pathChangeModel.New.Province = apiOrder.ToProvince;
        //            }
        //            addrIsChange = true;
        //            isShortAddrChange = true;
        //            updateFields.Add("ToProvince");
        //            logicOrder.ToProvince = apiOrder.ToProvince;
        //            // 多厂家：提取省市改变的订单，后面逻辑判断是否需要修改新厂家
        //            if (waitsellersendStatus.Contains(newErpStatus) && updateAddrOrderDic.ContainsKey(logicOrder.LogicOrderId) == false)
        //                updateAddrOrderDic.Add(logicOrder.LogicOrderId, logicOrder);
        //        }
        //        if (apiOrder.ToCity.IsNotNullOrEmpty() && logicOrder.ToCity != apiOrder.ToCity)
        //        {
        //            if (pathChangeDic.TryGetValue(logicOrder.LogicOrderId, out pathChangeModel) == false)
        //            {
        //                pathChangeModel = new LogicOrderPathChangeModel
        //                {
        //                    LogicOrderId = logicOrder.LogicOrderId,
        //                    PlatformOrderId = logicOrder.PlatformOrderId,
        //                    Old = new LogicOrderPathChangeLogModel { Province = logicOrder.ToProvince, City = logicOrder.ToCity, Code = logicOrder.PathFlowCode },
        //                    New = new LogicOrderPathChangeLogModel { Province = apiOrder.ToProvince, City = apiOrder.ToCity },
        //                };
        //                pathChangeDic.Add(logicOrder.LogicOrderId, pathChangeModel);
        //            }
        //            else
        //            {
        //                pathChangeModel.New.City = apiOrder.ToCity;
        //            }
        //            addrIsChange = true;
        //            isShortAddrChange = true;
        //            updateFields.Add("ToCity");
        //            logicOrder.ToCity = apiOrder.ToCity;
        //            if (waitsellersendStatus.Contains(newErpStatus) && updateAddrOrderDic.ContainsKey(logicOrder.LogicOrderId) == false)
        //                updateAddrOrderDic.Add(logicOrder.LogicOrderId, logicOrder);
        //        }
        //        if (apiOrder.ToCounty.IsNotNullOrEmpty() && logicOrder.ToCounty != apiOrder.ToCounty)
        //        {
        //            addrIsChange = true;
        //            isShortAddrChange = true;
        //            updateFields.Add("ToCounty");
        //            logicOrder.ToCounty = apiOrder.ToCounty;
        //        }
        //        if (apiOrder.ToTown.IsNotNullOrEmpty() && logicOrder.ToTown != apiOrder.ToTown)
        //        {
        //            addrIsChange = true;
        //            isShortAddrChange = true;
        //            updateFields.Add("ToTown");
        //            logicOrder.ToTown = apiOrder.ToTown;
        //        }

        //        //地址更新，需要更新全地址
        //        if (addrIsChange)
        //        {
        //            //var fullAddress = logicOrder.ToProvince + logicOrder.ToCity + logicOrder.ToCounty + logicOrder.ToAddress;
        //            var fullAddress = apiOrder.ToFullAddress;
        //            if (apiOrder.PlatformType == PlatformType.Pinduoduo.ToString()) 
        //            {
        //                var pcc = (apiOrder.ToProvince + apiOrder.ToCity + apiOrder.ToCounty)?.Replace(" ", "");
        //                fullAddress = apiOrder.ToAddress?.StartsWith(pcc) == true ? apiOrder.ToAddress : (pcc + apiOrder.ToAddress)?.Replace(" ", "");
        //            }
        //            updateFields.Add("ToFullAddress");
        //            logicOrder.ToFullAddress = fullAddress;
        //            isNeedUpdateBuyerHashCode = true;
        //        }

        //        if (isShortAddrChange)
        //        {
        //            updateFields.Add("ToShortAddress");
        //            isNeedUpdateBuyerHashCode = true;
        //        }

        //        if (apiOrder.BuyerRemark != logicOrder.BuyerRemark)
        //        {
        //            needUpdate = true;
        //            updateFields.Add("BuyerRemark");
        //            logicOrder.BuyerRemark = apiOrder.BuyerRemark.ToCutString(512);
        //        }

        //        if (apiOrder.SellerRemark != logicOrder.SellerRemark)
        //        {
        //            needUpdate = true;
        //            updateFields.Add("SellerRemark");
        //            logicOrder.SellerRemark = apiOrder.SellerRemark;
        //        }
        //    isNeedUpdateBuyerHashCode = true;
        //}
        //if (pt == PlatformType.Alibaba.ToString() && apiOrder.ExtField5.IsNotNullOrEmpty() && apiOrder.ExtField5 != logicOrder.ExtField1)
        //{
        //    updateFields.Add("ExtField1");
        //    logicOrder.ExtField1 = apiOrder.ExtField5;

        //        if (apiOrder.SellerRemarkFlag != logicOrder.SellerRemarkFlag)
        //        {
        //            needUpdate = true;
        //            updateFields.Add("SellerRemarkFlag");
        //            logicOrder.SellerRemarkFlag = apiOrder.SellerRemarkFlag;
        //        }

        //        if (hasLastShipTimePlatforms.Contains(apiOrder.PlatformType)
        //            && apiOrder.PlatformStatus == OrderStatusType.waitsellersend.ToString()
        //            && apiOrder.LastShipTime != null && apiOrder.LastShipTime != logicOrder.LastShipTime)
        //        {
        //            needUpdate = true;
        //            updateFields.Add("LastShipTime");
        //            logicOrder.LastShipTime = apiOrder.LastShipTime;
        //        }
        //        var pt = apiOrder.PlatformType;
        //        // 淘宝和淘工厂加密字段
        //        if ((pt == PlatformType.Taobao.ToString() || pt == PlatformType.AlibabaC2M.ToString() || pt == PlatformType.Alibaba.ToString()) && apiOrder.ExtField3.IsNotNullOrEmpty() && apiOrder.ExtField3 != logicOrder.DecryptField)
        //        {
        //            needUpdate = true;
        //            updateFields.Add("DecryptField");
        //            logicOrder.DecryptField = apiOrder.ExtField3;
        //            isNeedUpdateBuyerHashCode = true;
        //        }
        //        if (pt == PlatformType.Jingdong.ToString() && apiOrder.ExtField5.IsNotNullOrEmpty() && apiOrder.ExtField5 != logicOrder.DecryptField)
        //        {
        //            needUpdate = true;
        //            updateFields.Add("DecryptField");
        //            logicOrder.DecryptField = apiOrder.ExtField5;
        //            isNeedUpdateBuyerHashCode = true;
        //        }
        //        if (FxPlatformEncryptService.QueryEncryptedReceiverPlatformTypes.Contains(pt) && apiOrder.ExtField2.IsNotNullOrEmpty() && apiOrder.ExtField2 != logicOrder.DecryptField)
        //        {
        //            needUpdate = true;
        //            updateFields.Add("DecryptField");
        //            logicOrder.DecryptField = apiOrder.ExtField2;//P_Order.ExtField2，可用于合并，等同于淘宝平台的OAID作用

        //            updateFields.Add("ExtField1");
        //            logicOrder.ExtField1 = apiOrder.ExtField1; //P_Order.ExtField1，即对应：KuaiShouEncryptedReceiverInfo.RelationCode
        //            isNeedUpdateBuyerHashCode = true;
        //        }
        //        if (pt == PlatformType.Suning.ToString() && apiOrder.ExtField1.IsNotNullOrEmpty() && apiOrder.ExtField1 != logicOrder.DecryptField)
        //        {
        //            needUpdate = true;
        //            updateFields.Add("DecryptField");
        //            logicOrder.DecryptField = apiOrder.ExtField1;
        //            isNeedUpdateBuyerHashCode = true;
        //        }
        //        if (pt == PlatformType.XiaoHongShu.ToString() && apiOrder.ExtField1.IsNotNullOrEmpty() && apiOrder.ExtField1 != logicOrder.ExtField1)
        //        {
        //            updateFields.Add("DecryptField");
        //            updateFields.Add("ExtField1");
        //            logicOrder.ExtField1 = apiOrder.ExtField1;
        //            logicOrder.DecryptField = apiOrder.ExtField2;
        //            isNeedUpdateBuyerHashCode = true;
        //        }
        //        if (pt == PlatformType.WxVideo.ToString())
        //        {
        //            if (apiOrder.ExtField1 != logicOrder.ExtField1)
        //            {
        //                updateFields.Add("ExtField1");
        //                logicOrder.ExtField1 = apiOrder.ExtField1;
        //                isNeedUpdateBuyerHashCode = true;
        //            }
        //            if (apiOrder.ExtField2 != logicOrder.ExtField2)
        //            {
        //                updateFields.Add("ExtField2");
        //                logicOrder.ExtField2 = apiOrder.ExtField2;
        //            }
        //            if(apiOrder.ExtField4 != logicOrder.DecryptField)
        //            {
        //                updateFields.Add("DecryptField");
        //                logicOrder.DecryptField = apiOrder.ExtField4;
        //                isNeedUpdateBuyerHashCode = true;
        //            }
        //        }
        //        /// 是否需要更新BuyerHashCode
        //        /// 1.订单状态从waitsellersend更新到其他状态
        //        /// 2.订单收件人信息变更
        //        if (isNeedUpdateBuyerHashCode)
        //        {
        //            updateFields.Add("BuyerHashCode");
        //            updateFields.Add("BuyerHashCodeV2");
        //        }
        //        //只有需要更新的才加入更新列表
        //        if (updateFields.Any())
        //        {
        //            updateFieldValLst.Add(new LogicOrderChangeModel { Fields = updateFields, Order = logicOrder });
        //            if (CustomerConfig.IsDebug)
        //                CommUtls.WriteToLog($"更新的字段：{string.Join(",", updateFields)}，更新的逻辑单：{logicOrder.ToJson()}", $"UpdateLogicOrder-{logicOrder.ShopId}.txt", "UpdateLogicOrder");
        //        }

        //        #region 地址变更-->增加标签
        //        //if (CustomerConfig.IsDebug && isReceiverChange && needAddTagPlatformTypes.Contains(logicOrder.PlatformType))
        //        //{
        //        //    CommUtls.WriteToLog($"【{guid}】收件人变更订单信息：{new { logicOrder.LogicOrderId,logicOrder.PlatformOrderId,logicOrder.PrintState, logicOrder.ErpState,logicOrder.ReceiverHashCode}.ToJson()}", $"LogicOrderReceiverChange-{logicOrder.ShopId}.txt", "UpdateLogicOrder");
        //        //}
        //        if (isReceiverChange && needAddTagPlatformTypes.Contains(logicOrder.PlatformType) && logicOrder.PrintState != 0 && logicOrder.ErpState == OrderStatusType.waitsellersend.ToString())
        //        {
        //            if (receiverChangeOrderDic.ContainsKey(logicOrder.PlatformType) == false)
        //                receiverChangeOrderDic.Add(logicOrder.PlatformType, new List<LogicOrder> { logicOrder });
        //            else
        //                receiverChangeOrderDic[logicOrder.PlatformType].Add(logicOrder);
        //        }
        //        #endregion
        //    }

        //    //回流记录补偿：追加1688平台，原始单维度 --移到外围处理
        //    /*
        //    orders.Where(a => a.PlatformType == PlatformType.Alibaba.ToString()).ToList().ForEach(o =>
        //    {
        //        var newErpStatus = GetLogicErpStatus(o.OrderItems); //Erp状态
        //        if ((newErpStatus == "sended" || newErpStatus == "success") && changeToSendedOrders.ContainsKey(o.PlatformOrderId) == false)
        //        {
        //            var logicOrder = logicOrderList.FirstOrDefault(f => f.OrderCode == o.OrderCode && string.IsNullOrEmpty(f.LastWaybillCode) == false);
        //            o.LogicLastWaybillCode = logicOrder?.LastWaybillCode;

        //            changeToSendedOrders.Add(o.PlatformOrderId, o);
        //        }
        //    });
        //    */

        //    #region 剩余发货时间、抖店地址更新，处理合单
        //    try
        //    {
        //        var now = DateTime.Now;
        //        var orderTags = new List<OrderTags>();
        //        var groups = logicOrders.GroupBy(x => x.PlatformType).ToList();
        //        foreach (var g in groups)
        //        {
        //            // 处理没有合并的地址变更的订单
        //            var needAddTagOrders = g.Where(x => receiverChangeOrderDic.ContainsKey(g.Key) && receiverChangeOrderDic[g.Key].Any(y => y.LogicOrderId == x.LogicOrderId)).ToList();
        //            if (needAddTagOrders.Any()) 
        //            {
        //                foreach (var c in needAddTagOrders)
        //                {
        //                    if (orderTags.Any(x => x.OiCode == c.LogicOrderId))
        //                        continue;
        //                    var orderTag = new OrderTags();
        //                    orderTag.Tag = OrderTag.receiver_change.ToString();
        //                    orderTag.OiCode = c.LogicOrderId;
        //                    orderTag.Sid = c.ShopId;
        //                    orderTag.Platform = g.Key;
        //                    orderTag.TagType = TagType.LogicOrder.ToString();
        //                    orderTag.Status = 0;
        //                    orderTag.CreateTime = now;
        //                    orderTag.TagValue = "UpdateLogicOrder";
        //                    orderTag.SourceFxUserId = c.FxUserId;
        //                    orderTags.Add(orderTag);
        //                }
        //            }

        //            // 处理没有合并的订单
        //            var exceptionOrderManageTagNames = CustomerConfig.ExceptionOrderManageTagNames;
        //            if (splitNeedHandleTagPlatformType.Contains(g.Key))
        //            {
        //                var handleLogicOrders = g.ToList().Where(f => f.IsMainOrder == false);
        //                if (CustomerConfig.IsDebug)
        //                {
        //                    CommUtls.WriteToLog($"1.需要更新的逻辑单：{handleLogicOrders.Select(l => l.LogicOrderId).ToJson()}", $"OrderTag.txt", "UpdateLogicOrder");
        //                }
        //                foreach (var handleLogicOrder in handleLogicOrders)
        //                {
        //                    var orderTag = new OrderTags();
        //                    orderTag.Tag = OrderTag.ExceptionOrder.ToString();
        //                    orderTag.OiCode = handleLogicOrder.LogicOrderId;
        //                    orderTag.Sid = handleLogicOrder.ShopId;
        //                    orderTag.Platform = g.Key;
        //                    orderTag.TagType = TagType.LogicOrder.ToString();
        //                    orderTag.Status = -1;
        //                    orderTag.CreateTime = now;
        //                    orderTag.TagValue = "UpdateLogicOrder";
        //                    orderTag.SourceFxUserId = handleLogicOrder.FxUserId;

        //                    // 汇总接口最新的原始订单项数据
        //                    var handleLogicOrderItemCodes = handleLogicOrder.LogicOrderItems.Select(oi => oi.OrderItemCode).Distinct().ToList();
        //                    var handleApiOrderItems = new List<OrderItem>();
        //                    handleLogicOrderItemCodes.ForEach(hlItemCode =>
        //                    {
        //                        var apiOrderItems = new List<OrderItem>();
        //                        if (orderItemCodeDic.TryGetValue(hlItemCode, out apiOrderItems))
        //                            handleApiOrderItems.AddRange(apiOrderItems);
        //                    });
        //                    if (CustomerConfig.IsDebug)
        //                    {
        //                        CommUtls.WriteToLog($"2.需要汇总的订单项：{handleApiOrderItems.Select(l => new { l.LogicOrderId, l.Status, l.RefundStatus }).ToJson()}", $"OrderTag.txt", "UpdateLogicOrder");
        //                    }
        //                    //过滤在待发货且没有退款
        //                    var waitApiOrderItems = handleApiOrderItems.Where(x => x.Status == "waitsellersend" && (x.RefundStatus.IsNullOrEmpty() || x.RefundStatus?.ToUpper() == "REFUND_CLOSE"));
        //                    //判断是否存在异常范围标签
        //                    var handleLogicOrderItemTags = waitApiOrderItems.Where(oi => oi.Tags != null).SelectMany(x => x.Tags).Distinct().ToList();
        //                    if (CustomerConfig.IsDebug)
        //                    {
        //                        CommUtls.WriteToLog($"3.汇总好的的订单项标签：{handleLogicOrderItemTags.Select(l => new { l.OiCode, l.Status, l.Tag, l.TagType }).ToJson()}", $"OrderTag.txt", "UpdateLogicOrder");
        //                    }
        //                    var isExitExceptionTag = handleLogicOrderItemTags.Any(h => exceptionOrderManageTagNames.Contains(h.Tag));
        //                    if (isExitExceptionTag)
        //                    {
        //                        orderTag.Status = 0;
        //                    }
        //                    orderTags.Add(orderTag);
        //                }
        //            }


        //            // 更新合单的剩余发货时间和地址更新
        //            var logicMegeredOrderIds = g.Where(f => (
        //                hasLastShipTimePlatforms.Contains(f.PlatformType) || 
        //                needAddTagPlatformTypes.Contains(f.PlatformType) ||
        //                splitNeedHandleTagPlatformType.Contains(f.PlatformType)) 
        //                && f.IsChildOrder && f.MergeredOrderId.IsNotNullOrEmpty()).Select(f => f.MergeredOrderId).Distinct().ToList();
        //            if (logicMegeredOrderIds == null || logicMegeredOrderIds.Any() == false)
        //                continue;

        //            var fields = new List<string> { "o.Id", "o.MergeredType", "o.MergeredOrderId", "o.ErpState", "o.LastShipTime", "o.PrintState", "o.LogicOrderId", "o.PlatformorderId", "o.ShopId", "o.PlatformType", "oi.Id", "oi.LogicOrderId", "oi.OrignalOrderId", "oi.OrderItemCode" };
        //            // 合单与所有子单集合
        //            var mergerChildOrders = _logicOrderService.GetMegerOrderAndChildOrders(logicMegeredOrderIds, fields: fields).ToList();
        //            if (mergerChildOrders == null || mergerChildOrders.Any() == false)
        //                continue;

        //            var receiverChangeOrders = receiverChangeOrderDic.ContainsKey(g.Key) ? receiverChangeOrderDic[g.Key] : new List<LogicOrder>();
        //            var receiverChangeMergerOrderDic = receiverChangeOrders.GroupBy(x => x.MergeredOrderId).ToDictionary(x => x.Key, x => x.ToList());
        //            var updateFields = new List<string>();
        //            var mergerOrders = mergerChildOrders.Where(x => x.IsMainOrder).ToList();
        //            foreach (var item in mergerOrders)
        //            {
        //                var childOrders = mergerChildOrders.Where(x => x.MergeredOrderId == item.LogicOrderId).ToList();
        //                // 剩余发货时间更新
        //                if (hasLastShipTimePlatforms.Contains(item.PlatformType)) 
        //                {
        //                    foreach (var corder in childOrders)
        //                    {
        //                        var apiOrder = orders.FirstOrDefault(x => x.PlatformOrderId == corder.PlatformOrderId);
        //                        if (apiOrder != null)
        //                            corder.LastShipTime = apiOrder.LastShipTime;
        //                    }
        //                    var minLastShipTime = childOrders.Where(x => x.LastShipTime != null).Min(x => x.LastShipTime);
        //                    if (minLastShipTime != item.LastShipTime)
        //                    {
        //                        item.LastShipTime = minLastShipTime;
        //                        updateFields.Add("LastShipTime");
        //                        updateFieldValLst.Add(new LogicOrderChangeModel { Fields = updateFields, Order = item });
        //                    }
        //                }
        //                //抖店地址更新
        //                if (receiverChangeMergerOrderDic.ContainsKey(item.LogicOrderId))
        //                {
        //                    // 合单和所有子单
        //                    var mergerAndChildOrders = new List<LogicOrder>();
        //                    mergerAndChildOrders.AddRange(childOrders);
        //                    mergerAndChildOrders.Add(item);

        //                    foreach (var c in mergerAndChildOrders)
        //                    {
        //                        if (orderTags.Any(x => x.OiCode == c.LogicOrderId))
        //                            continue;
        //                        if (c.PrintState != 0 && c.ErpState == OrderStatusType.waitsellersend.ToString()) 
        //                        {
        //                            var orderTag = new OrderTags();
        //                            orderTag.Tag = OrderTag.receiver_change.ToString();
        //                            orderTag.OiCode = c.LogicOrderId;
        //                            orderTag.Sid = c.ShopId;
        //                            orderTag.Platform = g.Key;
        //                            orderTag.TagType = TagType.LogicOrder.ToString();
        //                            orderTag.Status = 0;
        //                            orderTag.CreateTime = now;
        //                            orderTag.TagValue = $"UpdateLogicOrder-{item.LogicOrderId}";
        //                            orderTag.SourceFxUserId = c.FxUserId;
        //                            orderTags.Add(orderTag);
        //                        }
        //                    }
        //                }
        //                // 处理异常单标签
        //                if (splitNeedHandleTagPlatformType.Contains(item.PlatformType))
        //                {
        //                    var orderTag = new OrderTags();
        //                    orderTag.Tag = OrderTag.ExceptionOrder.ToString();
        //                    orderTag.OiCode = item.LogicOrderId;
        //                    orderTag.Sid = item.ShopId;
        //                    orderTag.Platform = g.Key;
        //                    orderTag.TagType = TagType.LogicOrder.ToString();
        //                    orderTag.Status = -1;
        //                    orderTag.CreateTime = now;
        //                    orderTag.TagValue = "UpdateLogicOrder";
        //                    orderTag.SourceFxUserId = item.FxUserId;
        //                    //先检查数据库子单(排除接口的)是否存在异常范围标签，存在说明之前已经打过异常单标签，也可能有漏标
        //                    var handleLogicOrderItemDic = childOrders.SelectMany(x => x.LogicOrderItems)
        //                        .Where(li=> orderItemCodeDic.ContainsKey(li.OrderItemCode) == false)
        //                        .SelectMany(x => x.Tags).Distinct().ToList();
        //                    var isExitExceptionTag = handleLogicOrderItemDic.Any(h => exceptionOrderManageTagNames.Contains(h.Tag));
        //                    if (isExitExceptionTag)
        //                    {
        //                        orderTag.Status = 0;
        //                    }
        //                    else
        //                    {
        //                        //不存在，就检查接口返回的订单项是否存在异常范围标签
        //                        // 汇总接口最新的原始订单项数据
        //                        var handleLogicOrderItemCodes = childOrders.SelectMany(c => c.LogicOrderItems).Select(oi => oi.OrderItemCode).Distinct().ToList();
        //                        var handleApiOrderItems = new List<OrderItem>();
        //                        handleLogicOrderItemCodes.ForEach(hlItemCode =>
        //                        {
        //                            var apiOrderItems = new List<OrderItem>();
        //                            if (orderItemCodeDic.TryGetValue(hlItemCode, out apiOrderItems))
        //                                handleApiOrderItems.AddRange(apiOrderItems);
        //                        });
        //                        //过滤在待发货且没有退款
        //                        var waitApiOrderItems = handleApiOrderItems.Where(x => x.Status == "waitsellersend" && (x.RefundStatus.IsNullOrEmpty() || x.RefundStatus?.ToUpper() == "REFUND_CLOSE"));
        //                        //判断是否存在异常范围标签
        //                        var handleLogicOrderItemTags = waitApiOrderItems.Where(oi => oi.Tags != null).SelectMany(x => x.Tags).Distinct().ToList();
        //                        var isExitExceptionTag2 = handleLogicOrderItemTags.Any(h => exceptionOrderManageTagNames.Contains(h.Tag));
        //                        if (isExitExceptionTag2)
        //                        {
        //                            orderTag.Status = 0;
        //                        }
        //                    }
        //                    orderTags.Add(orderTag);
        //                }
        //            }
        //        }


        //        if (orderTags.Any()) 
        //            new OrderTagService(_connectionString).BulkInsert(orderTags);

        //        if (CustomerConfig.IsDebug && orderTags.Any())
        //            CommUtls.WriteToLog($"标签变更订单信息：{orderTags.ToJson()}", $"OrderTag.txt", "UpdateLogicOrder");

        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriteError($"店铺【{string.Join(",", sids)}】增量更新合单/子单异常：{ex}");
        //    }
        //    #endregion

        //    var sw = new Stopwatch();
        //    sw.Start();
        //    //4.更新订单
        //    var affectedCount = _logicOrderService.BulkUpdateOrderStatus(updateFieldValLst);
        //    // 待发货订单更新SkuId，系统单需要更新SkuCode，避免无法创建新的结算规格影响对账
        //    if (UpdateForSkuCodeLogicOrderItems.Any())
        //        _logicOrderService.BulkUpdateLogicOrderItemSkuCode(UpdateForSkuCodeLogicOrderItems);
        //    sw.Stop();
        //    //SyncOrderLog.Debug($"更新逻辑单状态,耗时 ：{sw.ElapsedMilliseconds}ms，同步到的旧订单:{orders.Count()},需要更新的逻辑单:{updateFieldValLst.Count()},更新受影响行数：{affectedCount}", SiteContext.Current.CurrentFxUserId, orders.First().ShopId);
        //    Log.Debug($"更新逻辑单状态,耗时 ：{sw.Elapsed.TotalSeconds}s，同步到的旧订单:{orders.Count()},需要更新的逻辑单:{updateFieldValLst.Count()},更新受影响行数：{affectedCount}");

        //    //try
        //    //{
        //    //    // 更新对账结算记录中的订单商品状态
        //    //    if (updateForSettlementOrderItems.Any())
        //    //        UpdateSettlementStatus(updateForSettlementOrderItems);
        //    //}
        //    //catch (Exception ex)
        //    //{
        //    //    Log.WriteError($"更新对账结算记录中的订单商品状态：{ex}");
        //    //}

        //    #region 自动更新订单厂家
        //    // 判断是否需要更新厂家路径
        //    var waitSendOrders = updateAddrOrderDic.Values.Where(x => x.IsMainOrder == false).ToList();
        //    if (waitSendOrders.Any())
        //    {
        //        var pCodes = waitSendOrders.SelectMany(x => x.LogicOrderItems).Select(x => x.ProductCode).Distinct().ToList();
        //        var pathflowService = new PathFlowService(_connectionString);
        //        var productFxService = new ProductFxService(_connectionString);
        //        var logContext = LogForOperatorContext.Current;
        //        BindSupplierRequestModel model = new BindSupplierRequestModel
        //        {
        //            productCodes = pCodes,
        //            isSaveHistoryData = false,
        //            IsFromUpdateLogicOrder = true,
        //            BatchId = Guid.NewGuid().ToString().Replace("-", string.Empty)
        //        };
        //        productFxService.SaveBindSupplierChanges(model, pathflowService, null, logContext, waitSendOrders);
        //        waitSendOrders.GroupBy(x => x.ShopId).ToList().ForEach(g =>
        //        {
        //            var pathChangeList = new List<LogicOrderPathChangeModel>();
        //            foreach (var item in g)
        //            {
        //                var pathChangeModel = new LogicOrderPathChangeModel();
        //                if (pathChangeDic.TryGetValue(item.LogicOrderId, out pathChangeModel))
        //                {
        //                    pathChangeModel.New.Code = item.PathFlowCode;
        //                    pathChangeList.Add(pathChangeModel);
        //                }
        //            }
        //            var updateLog = pathChangeList.ToJson();
        //            WriteSqlToLog(updateLog, null, $"UpdateLogicOrder-{g.Key}.txt", "UpdateLogicOrderPathFlow");
        //        });
        //    }
        //    #endregion

        //    #region 数据变更日志
        //    List<DataChangeLog> dcLogs = logicOrderList?.Select(o => new DataChangeLog
        //    {
        //        DataChangeType = DataChangeTypeEnum.UPDATE,
        //        TableTypeName = DataChangeTableTypeName.LogicOrder,
        //        SourceShopId = o.ShopId,
        //        SourceFxUserId = o.FxUserId,
        //        RelationKey = o.LogicOrderId,
        //        ExtField1 = "OrderFxService.UpdateLogicOrder"
        //    }
        //    ).ToList();
        //    new DataChangeLogRepository().Add(dcLogs);
        //    #endregion

        //    if(changeToSendedOrders.Any())
        //    {
        //        var sendReturnLogs = TryCompleteSendHistoryReturnRecord(changeToSendedOrders);
        //        sendReturnLogs?.ForEach(x => {
        //            x.BatchId = "回流补偿";
        //            x.MethodName = "TryCompleteSendHistoryReturnRecord";
        //            x.SubBusinessType = "UpdateLogicOrder";
        //        });
        //        BusinessLogDataEventTrackingService.Instance.WriteLog(sendReturnLogs);
        //    }

        //    ////分库方案添加逻辑
        //    ///改用定时任务推送触发消息2023.04.28
        //    //logicOrders.GroupBy(a => new { a.ShopId, a.FxUserId }).ToList().ForEach(group =>
        //    //{
        //    //    DuplicationFactoryService.Instance(DataChangeTableTypeName.LogicOrder)
        //    //        .PushMessage(group.Key.FxUserId, group.Key.ShopId);
        //    //});
        //}

        /// <summary>
        /// 更新逻辑单的信息（仅处理订单状态和退款状态）
        /// </summary>
        /// <param name="orders"></param>
        public void UpdateLogicOrderOnlyStatus(List<Order> orders)
        {
            //判空处理
            if (orders == null || !orders.Any())
            {
                return;
            }
            //查询字段
            var logicOrderFields =
                @"o.Id,o.PlatformOrderId,o.ShopId,o.PlatformType,o.LogicOrderId,o.OrderCode,o.MergeredType,o.MergeredOrderId,o.ErpState,o.ExceptionStatus,o.ExceptionReason,o.ErpRefundState,o.FxUserId"
                    .Split(",".ToArray()).ToList();
            var logicOrderItemFields = "oi.Id,oi.OrderItemCode,oi.SubItemId,oi.ShopId,oi.PlatformOrderId"
                .Split(",".ToArray()).ToList();
            logicOrderFields.AddRange(logicOrderItemFields);

            var pids = orders.Select(x => x.PlatformOrderId).Distinct().ToList();
            var sids = orders.Select(x => x.ShopId).Distinct().ToList();
            var logicOrderList = _logicOrderService.GetOrderByPlatformOrderIds(pids, sids: sids, isNeedProduct: false,
                fields: logicOrderFields,
                queryReceiver: new QueryReceiverModel { IsOnlyGetMask = false, IsFromApi = true });
            if (!logicOrderList.Any())
            {
                return;
            }
            //订单项代码
            var orderItemCodeDic = orders.SelectMany(f => f.OrderItems).GroupBy(x => x.OrderItemCode)
                .ToDictionary(x => x.Key, x => x.ToList());
            //3.重新计算状态
            var updateFieldValLst = new List<LogicOrderChangeModel>();
            //var updateForSettlementOrderItems = new List<OrderItem>();
            //查出合单-->所有子单
            foreach (var logicOrder in logicOrderList)
            {
                var updateFields = new List<string>();
                //3.1 找出逻辑单的订单项
                var orderItemCodes = logicOrder.LogicOrderItems.Select(f => f.OrderItemCode);
                var orderItems = new List<OrderItem>();
                foreach (var code in orderItemCodes)
                {
                    var ois = new List<OrderItem>();
                    if (orderItemCodeDic.TryGetValue(code, out ois))
                        orderItems.AddRange(ois);
                }
                //合单：追加合单另外的子单项
                if (logicOrder.IsMainOrder)
                {
                    logicOrder.LogicOrderItems?.ForEach(item =>
                    {
                        if (!orderItems.Exists(a => a.OrderItemCode == item.OrderItemCode))
                        {
                            orderItems.Add(new OrderItem
                            {
                                PlatformOrderId = item.PlatformOrderId,
                                SubItemID = item.SubItemId,
                                ShopId = item.ShopId,
                                Status = logicOrder.ErpState,
                                RefundStatus = logicOrder.ErpRefundState
                            });
                        }
                    });
                }
                if (!orderItems.Any())
                {
                    continue;
                }
                //3.2 重新计算状态
                //异常状态
                var erpExceptionStatus = GetLogicExceptionStatus(orderItems);
                if (logicOrder.ExceptionStatus != erpExceptionStatus.Key)
                {
                    if (logicOrder.ExceptionStatus != 999 && logicOrder.ExceptionStatus != -1 &&
                        logicOrder.ExceptionStatus != -2)
                    {
                        updateFields.Add("ExceptionStatus");
                        //厂家或操作过的异常单不更新异常状态
                        logicOrder.ExceptionStatus = erpExceptionStatus.Key;
                    }
                    updateFields.Add("ExceptionReason");
                    logicOrder.ExceptionReason = erpExceptionStatus.Value;
                }
                var isUpdateStatus = false;
                //Erp状态
                var newErpStatus = GetLogicErpStatus(orderItems);
                if (logicOrder.ErpState != newErpStatus)
                {
                    isUpdateStatus = true;
                    updateFields.Add("ErpState");
                    logicOrder.ErpState = newErpStatus;
                }
                //退款状态
                var refundStatus = BasePlatformService.TransferOrderRefundStatus(new Order { OrderItems = orderItems })
                    .RefundStatus;
                logicOrder.ErpRefundState = logicOrder.ErpRefundState ?? "";
                if (logicOrder.ErpRefundState != refundStatus)
                {
                    isUpdateStatus = true;
                    updateFields.Add("ErpRefundState");
                    logicOrder.ErpRefundState = refundStatus;
                }

                //foreach (var item in orderItems)
                //{
                //    if (isUpdateStatus &&
                //        updateForSettlementOrderItems.Any(x => x.OrderItemCode == item.OrderItemCode) == false)
                //    {
                //        updateForSettlementOrderItems.Add(item);
                //    }
                //}
                //只有需要更新的才加入更新列表
                if (updateFields.Any())
                {
                    updateFieldValLst.Add(new LogicOrderChangeModel { Fields = updateFields, Order = logicOrder });
                    if (CustomerConfig.IsDebug)
                    {
                        CommUtls.WriteToLog(
                            $"更新的字段[UpdateLogicOrderOnlyStatus]：{string.Join(",", updateFields)}，更新的逻辑单：{logicOrder.ToJson()}",
                            $"UpdateLogicOrder-{logicOrder.ShopId}.txt", "UpdateLogicOrder");
                    }
                }
            }
            var sw = new Stopwatch();
            sw.Start();
            //4.更新订单
            var affectedCount = _logicOrderService.BulkUpdateOrderStatus(updateFieldValLst);
            sw.Stop();
            Log.WriteLine(
                $"更新逻辑单状态[UpdateLogicOrderOnlyStatus],耗时 ：{sw.Elapsed.TotalSeconds}s，同步到的旧订单:{orders.Count},需要更新的逻辑单:{updateFieldValLst.Count},更新受影响行数：{affectedCount}");

            //try
            //{
            //    //更新对账结算记录中的订单商品状态
            //    if (updateForSettlementOrderItems.Any())
            //    {
            //        UpdateSettlementStatus(updateForSettlementOrderItems);
            //    }
            //}
            //catch (Exception ex)
            //{
            //    Log.WriteError($"更新对账结算记录中的订单商品状态[UpdateLogicOrderOnlyStatus]：{ex}");
            //}
        }

        ///// <summary>
        ///// 重试发货回流【移至CostOrderService】
        ///// </summary>
        ///// <param name="orderDict">以LogicOrderId为Key的，Order.LogicOrderId需赋值</param>
        ///// <returns></returns>
        //public List<BusinessLogModel> TryCompleteSendHistoryReturnRecord(Dictionary<string, Order> orderDict)
        //{
        //    var businessLogs = new List<BusinessLogModel>();
        //    try
        //    {
        //        var logFileName = $"TryCompleteSendHistoryReturnRecord-{DateTime.Now.ToString("yyyyMMdd")}.log";

        //        Log.Debug($"1开始：{orderDict.Count()}条，{orderDict.Keys.ToJson(true)}", logFileName);
        //        if (orderDict == null || orderDict.Any() == false)
        //            return businessLogs;

        //        Log.Debug($"1开始：{orderDict.Count()}条，{orderDict.Keys.ToJson(true)}", logFileName);
        //        //Log.Debug($"1.1开始：{orderDict.Count()}条，{orderDict.ToJson(true)}", logFileName);
        //        var firstOrder = orderDict.FirstOrDefault().Value;
        //        //判断用户是否【开通过】代扣功能
        //        var fxUserId = firstOrder.UserId;
        //        var isOpenPrepayment = false;
        //        if (orderDict.Values.Any(a => a.PlatformType == PlatformType.Alibaba.ToString()))
        //            isOpenPrepayment = new SupplierUserRepository().SupplierOrAgentIsOpenedPrePay(fxUserId);
        //        else
        //            isOpenPrepayment = new SupplierUserRepository().IsOpenedPrePay(fxUserId);

        //        Log.Debug($"2相关信息：fxUserId={fxUserId},isOpenPrepayment={isOpenPrepayment}", logFileName);

        //        if (isOpenPrepayment == false)
        //            return businessLogs;

        //        var porService = new PurchaseOrderRelationService(_connectionString);
        //        var fields = new List<string>
        //            {
        //                "PurchasePlatformOrderId", "CreateFxUserId", "PurchaseOrderShopId", "PurchaseOrderFxUserId",
        //                "PurchaseOrderCode", "PurchaseRelationCode", "SourceLogicOrderId", "SourcePathFlowCode", "SourcePlatformType", "SourceShopId", "SourcePlatformOrderId"
        //            };

        //        var fx1688Pids = orderDict.Values.Where(x => x.PlatformType == PlatformType.Alibaba.ToString()).Select(x => x.PlatformOrderId).Distinct().ToList();

        //        //非1688取逻辑单号
        //        var logicOrderIds = orderDict.Keys.Where(x => fx1688Pids.Contains(x) == false).ToList();
        //        var platformType = orderDict.First().Value.PlatformType;
        //        var purchaseOrderRelations = porService.Get(logicOrderIds, fields);

        //        //追加1688订单：取原始单号
        //        //var fx1688Pids = orderDict.Values.Where(x => x.PlatformType == PlatformType.Alibaba.ToString()).Select(x => x.PlatformOrderId).Distinct().ToList();
        //        var purchaseOrderRelationsFromPids = porService.GetByPids(fx1688Pids, null, fields, 1);
        //        if (purchaseOrderRelations == null)
        //            purchaseOrderRelations = new List<PurchaseOrderRelation>();
        //        if (purchaseOrderRelationsFromPids != null && purchaseOrderRelationsFromPids.Any())
        //            purchaseOrderRelations.AddRange(purchaseOrderRelationsFromPids);

        //        if (purchaseOrderRelations.IsNullOrEmptyList())
        //            return businessLogs;
        //        var newOrderDict = new Dictionary<string, Order>();
        //        foreach (var purchaseOrder in purchaseOrderRelations)
        //        {
        //            var key = purchaseOrder.SourceLogicOrderId;
        //            if (orderDict.ContainsKey(key) && newOrderDict.ContainsKey(key) == false)
        //                newOrderDict.Add(key, orderDict[key]);

        //            var pid = purchaseOrder.PurchasePlatformOrderId;
        //            if (orderDict.ContainsKey(pid) && newOrderDict.ContainsKey(pid) == false)
        //                newOrderDict.Add(pid, orderDict[pid]);
        //        }

        //        if (newOrderDict.Any() == false)
        //            return businessLogs;

        //        Log.Debug($"newOrderDict => {newOrderDict.Count()}条，{newOrderDict.Keys.ToJson()}", logFileName);

        //        var allItemRelations = new PurchaseOrderItemRelationService(_connectionString).GetListForDuplication(
        //            purchaseOrderRelations.Select(x => x.PurchaseRelationCode).ToList(),
        //            new List<string>
        //            {
        //                "PurchaseRelationCode", "PurchaseOrderItemRelationCode", "SourceOrderItemCode",
        //                "PurchaseOrderSubItemId", "PurchaseOrderItemCount", "PurchaseOrderItemCode", "SourceOrderSubItemId", "SourceOrderItemCount"
        //            });
        //        if (allItemRelations == null || allItemRelations.Any() == false)
        //            return businessLogs;
        //        //判断是否存在回流数据
        //        var returnRecordService = new SendHistoryReturnRecordService(_connectionString);
        //        //生成回流数据
        //        logicOrderIds = newOrderDict.Values.Where(x => x.PlatformType != PlatformType.Alibaba.ToString()).Where(x => string.IsNullOrEmpty(x.LogicOrderId) == false).Select(x => x.LogicOrderId).Distinct().ToList(); 
        //        var returnRecords = returnRecordService.GetListForDuplication(logicOrderIds);
        //        fx1688Pids = newOrderDict.Values.Where(x => x.PlatformType == PlatformType.Alibaba.ToString()).Select(x => x.PlatformOrderId).Distinct().ToList();
        //        var returnRecordFromPids = returnRecordService.GetListForDuplication(fx1688Pids, null, "PurchasePlatformOrderId");
        //        if (returnRecordFromPids != null && returnRecordFromPids.Any())
        //            returnRecords.AddRange(returnRecordFromPids);

        //        var notExistRecords = new List<SendHistoryReturnRecord>();
        //        var logisticCompany = orderDict.Values.SelectMany(x => x.LogisticsInfos).Select(x => x.company)
        //            .Distinct().ToList();
        //        var dictCompany = new Dictionary<string, string>();

        //        if (platformType == PlatformType.TouTiao.ToString())
        //        {
        //            var expresCommpanys = new ExpressCpCodeMappingService().GetListByCpCodes(logisticCompany, platformType);
        //            foreach (var company in logisticCompany)
        //            {
        //                var express = expresCommpanys.FirstOrDefault(x => x.CpCode == company)?.ExpressCompanyCode ??
        //                              company;
        //                dictCompany.Add(company, express);
        //            }
        //        }
        //        else
        //        {
        //            var expresCommpanys = new ExpressCodeMappingService().GetExpressCodeMappingsByPlatformExpressCode(platformType, logisticCompany);
        //            foreach (var company in logisticCompany)
        //            {
        //                var express = expresCommpanys.FirstOrDefault(x => x.PlatformExpressCode == company)?.ExpressCompanyCode ??
        //                              company;
        //                dictCompany.Add(company, express);
        //            }
        //        }

        //        Log.Debug($"3已存在的回流记录returnRecords => {returnRecords.Count()}条，{returnRecords.Select(a => a.SourceLogicOrderId).ToJson()}", logFileName);

        //        foreach (var kv in newOrderDict)
        //        {
        //            var businessLog = new BusinessLogModel() { 
        //                MethodName = "TryCompleteSendHistoryReturnRecord",
        //                BusinessType = "PurchaseOrderRelation",
        //                SubBusinessType = "同步订单时补偿回流记录",
        //                BusinessId = kv.Value?.PlatformOrderId,
        //                SysBusinessId = kv.Key
        //            };
        //            businessLogs.Add(businessLog);
        //            var porder = kv.Value;
        //            var oldRecords = returnRecords?.Where(x => x.SourceLogicOrderId == kv.Key || x.PurchasePlatformOrderId == kv.Key)?.ToList();
        //            var orderLogStr =
        //                $"，PlatformOrderId:{kv.Value.PlatformOrderId}，LogicOrderId:{kv.Key},用户ID:{kv.Value.UserId}，店铺ID:{kv.Value.ShopId}";

        //            var logistics = porder.LogisticsInfos;
        //            Log.Debug($"4porder => {porder.PlatformOrderId} ==> {logistics.ToJson(true)}", logFileName);
        //            var notExisitLogistics = new List<logistics_info>();
        //            if (oldRecords != null && oldRecords.Any())
        //                notExisitLogistics = logistics?.Where(x =>
        //                        oldRecords.Any(y =>
        //                            y.SendHistoryData != null && y.SendHistoryData.Contains(x.tracking_no)) == false)
        //                    .ToList();
        //            else
        //                notExisitLogistics = logistics;
        //            //已经全部生成过回流记录，则不需要补充
        //            if (notExisitLogistics == null || notExisitLogistics.Any() == false)
        //                continue;
        //            var purchaseOrder = purchaseOrderRelations.FirstOrDefault(x => x.SourceLogicOrderId == kv.Key || x.PurchasePlatformOrderId == kv.Key);
        //            if (purchaseOrder == null)
        //            {
        //                businessLog.Remark = "采购关系不存在，跳出";
        //                continue;
        //            }
        //            Log.Debug(
        //                $"notExisitLogistics：{notExisitLogistics.ToJson(true)}", logFileName);

        //            var purchaseOrderItems = allItemRelations
        //                .Where(x => x.PurchaseRelationCode == purchaseOrder.PurchaseRelationCode)?.ToList();
        //            if (purchaseOrderItems == null || purchaseOrderItems.Any() == false)
        //            {
        //                Log.Debug(
        //                    $"采购单项不存在，purchaseOrder.PurchaseRelationCode：{purchaseOrder.PurchaseRelationCode}，CreateFxUserId:{purchaseOrder.CreateFxUserId}", logFileName);
        //                businessLog.Remark = "采购单项不存在，跳出";
        //                continue;
        //            }
        //            businessLog.Remark = "匹配到采购信息";
        //            //每一条物流信息对应一个回流记录，多条说明是分包发货
        //            var curLogContent = new {ApiLogisticsInfos = notExisitLogistics,SendHistoryReturnRecords = new List<SendHistoryReturnRecord>(),MatchRemarks = new List<string>() };
        //            var dtNow = DateTime.Now;
        //            var threeMonthsAgo = dtNow.AddMonths(-3);

        //            foreach (var item in notExisitLogistics)
        //            {
        //                //发货模式：0发下游订单；1发1688订单； 10其他软件发下游订单；11其他软件发1688订单
        //                var deliveryModel = 0;
        //                if (kv.Value.PlatformType == PlatformType.Alibaba.ToString())
        //                {
        //                    deliveryModel = 1;
        //                }
        //                if (kv.Value.LogicLastWaybillCode != item.tracking_no)
        //                {
        //                    deliveryModel += 10;
        //                }

        //                if (notExistRecords.Any(a => a.PurchasePlatformOrderId == purchaseOrder.PurchasePlatformOrderId && a.SourceLogicOrderId == purchaseOrder.SourceLogicOrderId))
        //                    continue;

        //                var purchaseSubItemIds = new List<PurchaseSendItemInfo>();
        //                if (platformType == PlatformType.TouTiao.ToString())
        //                {
        //                    foreach (var product_info in item.product_info)
        //                    {
        //                        var orderItem =
        //                            porder.OrderItems.FirstOrDefault(oi => oi.SubItemID == product_info.sku_order_id);
        //                        if (orderItem == null)
        //                        {
        //                            Log.WriteWarning(
        //                                $"根据订单物流数据无法匹配到对应的订单项，{orderLogStr}，OrderItems详细信息：{porder.OrderItems?.ToJson()},物流信息：{porder.LogisticsInfos?.ToJson()}", logFileName);
        //                            continue;
        //                        }

        //                        var purchaseOrderItem =
        //                            purchaseOrderItems.FirstOrDefault(x =>
        //                                x.SourceOrderItemCode == orderItem.OrderItemCode);
        //                        if (purchaseOrderItem == null)
        //                        {
        //                            Log.WriteWarning(
        //                                $"根据订单物流数据无法匹配到对应的采购订单项purchaseOrderItem，{orderLogStr}，OrderItems详细信息：{porder.OrderItems?.ToJson()},物流信息：{porder.LogisticsInfos?.ToJson()}，purchaseOrderItems：{purchaseOrderItems?.ToJson()}", logFileName);
        //                            continue;
        //                        }

        //                        var subitem = new PurchaseSendItemInfo
        //                        {
        //                            PurchaseOIRelationCode =
        //                                purchaseOrderItem.PurchaseOrderItemRelationCode, //TODO:获取采购单项的Code
        //                            SubItemId = purchaseOrderItem.PurchaseOrderSubItemId, //TODO:验证是否和子单的order_id一致
        //                            Quantity = product_info.product_count,
        //                        };
        //                        purchaseSubItemIds.Add(subitem);
        //                    }
        //                }
        //                else
        //                {
        //                    //其他平台，以我们数据库上的PurchaseOrderItem为准
        //                    //不同发货模式，取不同的值
        //                    if (deliveryModel == 1 || deliveryModel == 11 || platformType == PlatformType.Alibaba.ToString())
        //                    {
        //                        var subItems = purchaseOrderItems.Select(x => new PurchaseSendItemInfo
        //                        {
        //                            PurchaseOIRelationCode = x.PurchaseOrderItemRelationCode,
        //                            SubItemId = x.SourceOrderSubItemId,//商家
        //                            Quantity = x.SourceOrderItemCount,//商家
        //                        }).ToList();
        //                        purchaseSubItemIds.AddRange(subItems);
        //                    }
        //                    else
        //                    {
        //                        var subItems = purchaseOrderItems.Select(x => new PurchaseSendItemInfo
        //                        {
        //                            PurchaseOIRelationCode = x.PurchaseOrderItemRelationCode,
        //                            SubItemId = x.PurchaseOrderSubItemId,//厂家1688店铺
        //                            Quantity = x.PurchaseOrderItemCount,//厂家1688店铺
        //                        }).ToList();
        //                        purchaseSubItemIds.AddRange(subItems);
        //                    }
        //                }

        //                if (purchaseSubItemIds.Any() == false)
        //                {
        //                    Log.WriteWarning(
        //                        $"根据订单物流数据无法匹配到对应的采购订单项purchaseOrderItem，{orderLogStr}，OrderItems详细信息：{porder.OrderItems?.ToJson()},物流信息：{porder.LogisticsInfos?.ToJson()}，purchaseOrderItems：{purchaseOrderItems?.ToJson()}", logFileName);
        //                    curLogContent.MatchRemarks.Add($"[Error]根据订单物流数据无法匹配到对应的采购订单项purchaseOrderItem，{orderLogStr}");
        //                    continue;
        //                }
        //                curLogContent.MatchRemarks.Add($"[OK]根据订单物流数据匹配到对应的采购订单项purchaseOrderItem，{orderLogStr}");
        //                var cpCode = dictCompany[item.company];
        //                var sendHistoryDataModel = new PurchaseOrderSendDataModel
        //                {
        //                    PurchaseRelationCode = purchaseOrder.PurchaseRelationCode,
        //                    SendType = 0,
        //                    Source = "OrderSync",
        //                    ExpressWaybillCode = item.tracking_no,
        //                    ExpressCpCode = string.IsNullOrEmpty(cpCode) ? item.company : cpCode,
        //                    PurchaseSubItemIds = purchaseSubItemIds
        //                };
        //                var returnRecordCode = Guid.NewGuid().ToString().ToShortMd5();
        //                var record = new SendHistoryReturnRecord
        //                {
        //                    ReturnRecordCode = returnRecordCode,
        //                    SourceReturnRecordCode = returnRecordCode,
        //                    SendHistoryCode = "",
        //                    SendHistoryOrderCode = "",
        //                    SendFxUserId = purchaseOrder.PurchaseOrderFxUserId,
        //                    SendPathFlowCode = purchaseOrder.SourcePathFlowCode ?? "",
        //                    SendTime = item.ship_time > threeMonthsAgo ? item.ship_time : dtNow,
        //                    PurchasePlatformOrderId = purchaseOrder.PurchasePlatformOrderId,
        //                    PurchaseOrderShopId = purchaseOrder.PurchaseOrderShopId,
        //                    PurchaseOrderCode = purchaseOrder.PurchaseOrderCode,
        //                    SourceFxUserId = purchaseOrder.CreateFxUserId,
        //                    SourceShopId = purchaseOrder.SourceShopId,
        //                    SourcePlatformOrderId = purchaseOrder.SourcePlatformOrderId,
        //                    SourcePlatformType = purchaseOrder.SourcePlatformType,
        //                    PurchaseFxUserId = purchaseOrder.PurchaseOrderFxUserId,
        //                    SourceCloudPt = CustomerConfig.CloudPlatformType,
        //                    SourceLogicOrderId = purchaseOrder.SourceLogicOrderId,
        //                    SendHistoryData = sendHistoryDataModel.ToJson(),
        //                    Status = 0,
        //                    CreateFxUserId = purchaseOrder.CreateFxUserId,
        //                    CreateTime = dtNow,
        //                    UpdateTime = dtNow,
        //                    DeliveryMode = deliveryModel
        //                };
        //                notExistRecords.Add(record);
        //                curLogContent.SendHistoryReturnRecords.Add(record);
        //            }

        //        }

        //        if (notExistRecords.Any() == false)
        //            return businessLogs;
        //        try
        //        {
        //            returnRecordService.Save(notExistRecords);

        //            Log.Debug($"9保存回流记录日志，新增记录：{notExistRecords.Count()}条，{notExistRecords.Select(a => a.SourceLogicOrderId).ToJson()}，{notExistRecords.Select(a => a.ReturnRecordCode).ToJson()}", logFileName);

        //        }
        //        catch (Exception ex)
        //        {
        //            Log.WriteError($"保存回流记录时发生异常:{ex}");
        //        }
        //    }
        //    catch (Exception e)
        //    {
        //        Log.WriteError($"保存回流记录时发生异常:{e.Message}，堆栈信息：{e.StackTrace}", "TryCompleteSendHistoryReturnRecordError.log");
        //    }
        //    return businessLogs;
        //}

        public void UpdateSettlementStatus(List<OrderItem> ois)
        {
            var updateFieldValLst = new List<SettlementStatusChangeModel>();
            var orderItemCodes = ois.Select(x => x.OrderItemCode).Distinct().ToList();
            var settlementFields = "Id,OrderItemCode,Status,RefundStatus,PathflowCode,FxUserId,SendHistoryCode";
            var settlementInfoService = new SettlementInfoService(_connectionString);
            var settlementInfos = settlementInfoService.GetSettlementInfosByOrderItemCodes(orderItemCodes, settlementFields);
            if (settlementInfos == null || settlementInfos.Any() == false)
                return;

            foreach (var item in settlementInfos)
            {
                var apiOrderItem = ois.FirstOrDefault(f => f.OrderItemCode == item.OrderItemCode);
                if (apiOrderItem == null)
                    continue;
                var updateFields = new List<string>();
                if (apiOrderItem.Status != item.Status)
                {
                    item.Status = apiOrderItem.Status;
                    updateFields.Add("Status");
                }
                if (apiOrderItem.RefundStatus != item.RefundStatus)
                {
                    item.RefundStatus = apiOrderItem.RefundStatus;
                    updateFields.Add("RefundStatus");
                }
                updateFieldValLst.Add(new SettlementStatusChangeModel { Fields = updateFields, SettlementInfo = item });
            }

            //更新结算状态
            var affectedCount = settlementInfoService.BulkUpdateSettlementInfoStatus(updateFieldValLst);

            #region 数据变更日志
            ////当前用户的系统店铺Id
            //var systemShopId = SiteContext.Current?.CurrentFxUserId ?? 0;
            var dcLogs = updateFieldValLst.Select(a => a.SettlementInfo).Select(o => new DataChangeLog
            {
                DataChangeType = DataChangeTypeEnum.UPDATE,
                TableTypeName = DataChangeTableTypeName.Product,
                SourceShopId = o.SourceShopId,//用户维度的存当前用户的系统店铺Id，2023-05-06改为源店铺ShopId
                SourceFxUserId = o.SourceUserId,//2023-05-06改为源商家Id
                RelationKey = o.ProductCode,
                ExtField1 = "BulkUpdateSettlementInfoStatus"
            }
            ).ToList();
            new DataChangeLogRepository().Add(dcLogs, 1);
            #endregion

            #region 调用同步数据接口服务
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var pathFlowCodes = updateFieldValLst.Select(a => a.SettlementInfo.PathflowCode).Distinct().ToList();
            new SyncDataInterfaceService(fxUserId).PathFlowOpt(pathFlowCodes, (string targetConnectionString, List<string> targetPathFlowCodes) =>
            {
                if (targetPathFlowCodes != null && targetPathFlowCodes.Any())
                {
                    var targetModels = updateFieldValLst.Where(a => targetPathFlowCodes.Contains(a.SettlementInfo.PathflowCode)).ToList();
                    new SettlementInfoService(targetConnectionString).BulkUpdateSettlementInfoStatus(targetModels);
                }
            });
            #endregion
        }

        ///// <summary>
        ///// 【移至CostOrderService】
        ///// </summary>
        ///// <param name="order"></param>
        ///// <param name="logicOrder"></param>
        //private void FixNullValueToEmpty(Order order, LogicOrder logicOrder)
        //{
        //    if (order != null)
        //    {
        //        if (string.IsNullOrEmpty(order.ToName))
        //            order.ToName = "";
        //        if (string.IsNullOrEmpty(order.ToMobile))
        //            order.ToMobile = "";
        //        if (string.IsNullOrEmpty(order.ToProvince))
        //            order.ToProvince = "";
        //        if (string.IsNullOrEmpty(order.ToCity))
        //            order.ToCity = "";
        //        if (string.IsNullOrEmpty(order.ToCounty))
        //            order.ToCounty = "";
        //        if (string.IsNullOrEmpty(order.ToTown))
        //            order.ToTown = "";
        //        if (string.IsNullOrEmpty(order.ToAddress))
        //            order.ToAddress = "";
        //        if (string.IsNullOrEmpty(order.BuyerRemark))
        //            order.BuyerRemark = "";
        //        if (string.IsNullOrEmpty(order.SellerRemark))
        //            order.SellerRemark = "";
        //        if (string.IsNullOrEmpty(order.SellerRemarkFlag))
        //            order.SellerRemarkFlag = "";
        //        if (string.IsNullOrEmpty(order.ReceiverHashCode))
        //            order.ReceiverHashCode = "";
        //        if (string.IsNullOrEmpty(order.DbReceiverHashCode))
        //            order.DbReceiverHashCode = "";
        //        if (string.IsNullOrEmpty(order.DbBuyerHashCodeV2))
        //            order.DbBuyerHashCodeV2 = "";
        //        if (string.IsNullOrEmpty(order.ToPhoneIndex))
        //            order.ToPhoneIndex = "";
        //    }

        //    if (logicOrder != null)
        //    {
        //        if (string.IsNullOrEmpty(logicOrder.ToName))
        //            logicOrder.ToName = "";
        //        if (string.IsNullOrEmpty(logicOrder.ToPhone))
        //            logicOrder.ToPhone = "";
        //        if (string.IsNullOrEmpty(logicOrder.ToProvince))
        //            logicOrder.ToProvince = "";
        //        if (string.IsNullOrEmpty(logicOrder.ToCity))
        //            logicOrder.ToCity = "";
        //        if (string.IsNullOrEmpty(logicOrder.ToCounty))
        //            logicOrder.ToCounty = "";
        //        if (string.IsNullOrEmpty(logicOrder.ToTown))
        //            logicOrder.ToTown = "";
        //        if (string.IsNullOrEmpty(logicOrder.ToAddress))
        //            logicOrder.ToAddress = "";
        //        if (string.IsNullOrEmpty(logicOrder.BuyerRemark))
        //            logicOrder.BuyerRemark = "";
        //        if (string.IsNullOrEmpty(logicOrder.SellerRemark))
        //            logicOrder.SellerRemark = "";
        //        if (string.IsNullOrEmpty(logicOrder.SellerRemarkFlag))
        //            logicOrder.SellerRemarkFlag = "";
        //        if (string.IsNullOrEmpty(logicOrder.ReceiverHashCode))
        //            logicOrder.ReceiverHashCode = "";
        //        if (string.IsNullOrEmpty(logicOrder.OrderBuyerHashCode))
        //            logicOrder.OrderBuyerHashCode = "";
        //        if (string.IsNullOrEmpty(logicOrder.ToPhoneIndex))
        //            logicOrder.ToPhoneIndex = "";
        //    }
        //}

        /// <summary>
        /// 拆系统单入口
        /// </summary>
        /// <param name="shop"></param>
        /// <param name="isOfflineOrder"></param>
        /// <param name="logId"></param>
        /// <param name="tag"></param>
        /// <param name="currentUserId"></param>
        /// <param name="fxPageType"></param>
        /// <exception cref="Exception"></exception>
        public void SplitLogicOrdersWithLog(Shop shop, bool isOfflineOrder, string logId = "", string tag = "",
            int currentUserId = 0, int fxPageType = 0)
        {
            //判空处理
            if (shop == null)
                return;
            

            if (!isOfflineOrder
                && string.Equals(shop.PlatformType, "TouTiao", StringComparison.OrdinalIgnoreCase) == false
                && !CustomerConfig.IsCrossBorderSite)
            {
                //非线下单需先进行授权校验
                var result = true;
                try
                {
                    var platformService = PlatformFactory.GetPlatformService(shop);
                    result = platformService.Ping();
                }
                catch (Exception ex)
                {
                    result = false;
                }

                if (result == false)
                {
                    return;
                }
            }
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //插入拆单的mongdb日志
            var logname = "增量分销同步订单拆单1";
            var logcontext = LogForOperatorContext.Current;
            var mongdblog = logcontext.Begin(new LogForOperator
            {
                StartTime = DateTime.Now,
                Description = new OperationDescription()
                {
                    Url = System.Web.HttpContext.Current?.Request?.Url?.ToString(),
                    Referrer = System.Web.HttpContext.Current?.Request?.UrlReferrer?.ToString(),
                    UserAgent = System.Web.HttpContext.Current?.Request?.UserAgent,
                    Name = logname
                },
                TotalCount = 0,
                TraceId = System.Web.HttpContext.Current?.Request?["traceId"],
                //DBType = SiteContext.Current?.DataBaseType.ToString(),
                CurObjectId = logId,
                UserId = SiteContext.Current.CurrentFxUserId,
                OperatorType = logname,
                ShopId = shop.Id,
            });
            //乐观锁
            int _sleepSeconds = 3;
            if (isOfflineOrder)
            {
                _sleepSeconds = 0;
            }
            
            var sid = shop.Id;
            var op = OptimisticLockOperationType.SplitToLogicOrder;
            var opId = $"FX{sid}";

            #region 拆单历史日志收集对象
            //拆单历史日志
            var splitLogicOrderHistoryLog = new SplitLogicOrderHistoryLogModel
            {
                BatchId = logId,
                FxUserId = fxUserId,
                ShopId = shop.Id,
                StartSplitTime = DateTime.Now
            };
            //订单查询耗时
            var orderSearchDurations = new Dictionary<int, Tuple<double, int>>();
            var updateFlagDurations = new Dictionary<int, double>(); 
            var checkProductDurations = new Dictionary<int, double>(); 
            var pathFlowDurations = new Dictionary<int, double>();
            var splitPrevDurations = new Dictionary<int, double>();
            var splitTotalDurations = new Dictionary<int, double>(); 
            #endregion
           
            try
            {
                //最后一次拆单的P_Order.Id
                var lastOrderId = 0;
                //暂未用到，注释掉，且分库迁移后会有问题
                //var isLastOrderIdEnabled = _commonSettingService.Get("/Shop/IsLastSplitOrderIdEnabled", sid)?.Value == "1";
                //if (isLastOrderIdEnabled)
                //    lastOrderId = _commonSettingService.Get("/Shop/Fendan/LastSplitOrderId", sid)?.Value?.ToInt() ?? 0;
                var result = _commonSettingService.ExecuteWithOptimisticLock(() =>
                {
                    //拆单历史日志
                    splitLogicOrderHistoryLog.IsGetLock = true;
                    
                    var totalSpendTime = 0.0;
                    //4.拆单，查询出店铺数据库中所有待发货且未拆的单进行拆单
                    const int pageSize = 500;
                    int maxLoopCount;
                    if (CustomerConfig.IsDebug && CustomerConfig.MaxSplitLoopCount > 0)
                    {
                        maxLoopCount = CustomerConfig.MaxSplitLoopCount;
                    }
                    else
                    {
                        //从配置中获取，默认值：20
                        maxLoopCount = _commonSettingService.MaxSplitOrderLoopCountWithCache();
                    }
                    //日志信息收集
                    splitLogicOrderHistoryLog.MaxSplitLoop = maxLoopCount;
                    //一次拆500单，最多拆100次
                    for (var i = 0; i < maxLoopCount; i++)
                    {
                        //日志信息收集
                        splitLogicOrderHistoryLog.LastSplitIndex = i;
                        //耗时监控
                        var sw = new Stopwatch();
                        //1.查询出数据库未拆分逻辑单的待发货订单  增加一下灰度的判断                    
                        var notSplitOrders = new List<Order>();
                        //判断是否使用优化版本的查询逻辑，上次没有更新失败则使用之前的查询逻辑
                        var isUserOldQuery = _commonSettingService.Get("/Shop/Fendan/Config/IsUpdateOrderAsSplitedFailed", sid)?.Value == "1";
                        //日志信息收集
                        splitLogicOrderHistoryLog.IsUseOldQuery = isUserOldQuery;
                        //耗时监控开始
                        sw.Start();
                        if (isUserOldQuery)
                            CommUtls.WriteToLog($"获拆单方式配置，检查到店铺使用的是老版的拆单逻辑：ShopId:{sid},isUserOldQuery = {isUserOldQuery}", $"UpdateSplitFlagExt.txt", "UpdateSplitFlagExt");
                        // 2022.01.11 不管订单是不是已经拆分都更新一下P_Order.IsPreordain，保证P_Order拆分标记是正常的
                        if (isUserOldQuery)
                            _repository.UpdateSplitFlagExt(sid);
                        sw.Stop();
                        //日志信息收集
                        if (updateFlagDurations.ContainsKey(i) == false)
                        {
                            updateFlagDurations.Add(i, sw.Elapsed.TotalMilliseconds);
                        }
                        sw.Restart();
                        if (isUserOldQuery)
                        {
                            mongdblog.Remark = "GetNotSplitOrders";
                            notSplitOrders = _repository.GetNotSplitOrders(sid, pageSize, null)?.ToList();
                            Log.Debug(
                                () =>
                                    $"SplitLogicOrdersWithLog，查询到待拆原始单：{notSplitOrders.Select(p => p.PlatformOrderId).ToJson()}",
                                "wm_order2.txt");
                        }
                        else
                        {
                            mongdblog.Remark = "GetNotSplitOrdersExt";
                            notSplitOrders = _repository.GetNotSplitOrdersExt(sid, pageSize, i, lastOrderId)?.ToList();
                        }
                        //// 查出了重复订单的情况
                        //var repeatOrders = notSplitOrders.SelectMany(x => x.OrderItems).GroupBy(x => x.PlatformOrderId + x.OrderItemCode).Where(x => x.Count() > 1);
                        //if (repeatOrders.Any()) 
                        //{
                        //    mongdblog.Remark = mongdblog.Remark + " ==>" + repeatOrders.Select(x => new { x.Key , Value = x.Select(y => new { y.Id,y.PlatformOrderId, y.ShopId, y.OrderCode,y.CreateTime }) }).ToJson();
                        //}
                        sw.Stop();
                        //日志信息收集
                        if (orderSearchDurations.ContainsKey(i) == false)
                        {
                            orderSearchDurations.Add(i,
                                new Tuple<double, int>(sw.Elapsed.TotalMilliseconds, notSplitOrders.Count));
                        }

                        //本地测试全店铺,发布测试的话,部分店铺
                        mongdblog.TotalCount += notSplitOrders.Count;
                        if (notSplitOrders == null || notSplitOrders.Any() == false)
                        {
                            if (i == 0)
                            { 
                   
                            }
                            break;
                        }

                        //满一页睡眠1秒
                        if (notSplitOrders.Count == pageSize)
                        {
                            Thread.Sleep(1000);
                        }
                        totalSpendTime += sw.Elapsed.TotalSeconds;

                        sw.Restart();

                        CheckOrderProduct(notSplitOrders, isOfflineOrder);

                        sw.Stop();
                        
                        //日志信息收集
                        if (checkProductDurations.ContainsKey(i) == false)
                        {
                            checkProductDurations.Add(i, sw.Elapsed.TotalMilliseconds);
                        }

                        //耗时
                        var durations = sw.Elapsed.TotalSeconds;

                        totalSpendTime += durations;
                        
                        sw.Restart();

                        //拆单内部收集日志
                        var splitOrderLogItem = new SplitLogicOrderHistoryLogItemModel();

                        //拆单主要逻辑
                        SplitLogicOrders(notSplitOrders, isOfflineOrder, tag, currentUserId, traceBatchId: logId,
                            splitOrderLogItem: splitOrderLogItem);

                        sw.Stop();

                        #region 日志信息收集
                        //日志信息收集
                        if (pathFlowDurations.ContainsKey(i) == false)
                        {
                            pathFlowDurations.Add(i, splitOrderLogItem.GetPathFlowDurations);
                        }
                        if (splitPrevDurations.ContainsKey(i) == false)
                        {
                            splitPrevDurations.Add(i, splitOrderLogItem.SplitPrevDurations);
                        }
                        if (splitTotalDurations.ContainsKey(i) == false)
                        {
                            splitTotalDurations.Add(i, sw.Elapsed.TotalMilliseconds);
                        }
                        #endregion
                        
                        totalSpendTime += sw.Elapsed.TotalSeconds;
                        
                        //淘宝订单全链路状态回传:拆单后进入审单状态
                        //Log.Debug($"淘宝订单全链路状态回传:拆单后进入审单状态：{notSplitOrders.Select(x=>x.PlatformOrderId).ToJson()}");
                        PlatformTrackSendUtils.SendTrackOrderStatus(notSplitOrders, OrderTrackType.Split, shop);//推送打单状态到平台
                        
                        logcontext.EndStep();
                    }
                    mongdblog.Remark += " 拆单完成V1";

                    //logcontext.End();
                    return true;
                }, () =>
                {
                    //老日志
                    mongdblog.Exception += $"店铺【{sid}】同步完成后，拆单动作未获取到锁";
                    Log.Debug(() => $"店铺【{sid}】同步完成后，拆单动作未获取到锁");
                }, op, opId, maxLockMinutes: 240, sleepSeconds: _sleepSeconds);
            }
            catch (Exception ex)
            {
                #region 拆单历史日志收集
                //收集错误信息日志
                splitLogicOrderHistoryLog.IsError = true;
                splitLogicOrderHistoryLog.ErrorMessage = ex.Message;
                splitLogicOrderHistoryLog.StackTrace = ex.StackTrace;
                splitLogicOrderHistoryLog.InnerErrorMessage = ex.InnerException?.Message;
                splitLogicOrderHistoryLog.InnerStackTrace = ex.InnerException?.StackTrace;
                #endregion
                
                mongdblog.Exception += $"拆单异常：{ex}";
                throw ex;
            }
            finally
            {
                #region 拆单历史日志写入
                try
                { 
                    //拆单流程耗时统计
                    splitLogicOrderHistoryLog.OrderSearchDurations = string.Join(",",
                        orderSearchDurations.Select(m => $"{m.Key}|{m.Value.Item1}|{m.Value.Item2}").ToList());
                    splitLogicOrderHistoryLog.UpdateFlagDurations = string.Join(",",
                        updateFlagDurations.Select(m => $"{m.Key}|{m.Value}").ToList());
                    splitLogicOrderHistoryLog.CheckProductDurations = string.Join(",",
                        checkProductDurations.Select(m => $"{m.Key}|{m.Value}").ToList());
                    splitLogicOrderHistoryLog.PathFlowDurations = string.Join(",",
                        pathFlowDurations.Select(m => $"{m.Key}|{m.Value}").ToList());
                    splitLogicOrderHistoryLog.SplitPrevDurations = string.Join(",",
                        splitPrevDurations.Select(m => $"{m.Key}|{m.Value}").ToList());
                    splitLogicOrderHistoryLog.SplitTotalDurations = string.Join(",",
                        splitTotalDurations.Select(m => $"{m.Key}|{m.Value}").ToList());
                    //最终写入日志
                    SplitLogicOrderHistoryLogDataTrackingService.Instance.WriteLog(splitLogicOrderHistoryLog);
                }
                catch
                {
                   //ignore
                }
                #endregion
               
                logcontext.End();
            }
        }
        //public void SplitLogicOrdersWithLogNew(List<Order> porders, Shop shop, bool isOfflineOrder, string logid = "")
        //{
        //    if (shop == null)
        //        return;

        //    var sid = shop.Id;
        //    SyncOrderLog.Debug($"店铺【{shop.NickName}】同步完成，开始拆单", SiteContext.Current.CurrentFxUserId, sid);
        //    Log.Debug($"店铺【{shop.NickName}】同步完成，开始拆单");


        //    if (!isOfflineOrder)
        //    {
        //        //非线下单需先进行授权校验
        //        var result = true;
        //        try
        //        {
        //            var platfromService = PlatformFactory.GetPlatformService(shop);
        //            result = platfromService.Ping();
        //        }
        //        catch (Exception ex)
        //        {
        //            SyncOrderLog.Debug($"店铺【{shop.NickName}】拆单之前检查授权报错：{ex.ToString()}", SiteContext.Current.CurrentFxUserId, sid);
        //            result = false;
        //        }

        //        if (result == false)
        //        {
        //            SyncOrderLog.Debug($"店铺【{shop.NickName}】已过期，不执行拆单！", SiteContext.Current.CurrentFxUserId, sid);
        //            Log.Debug($"店铺【{shop.NickName}】已过期，不执行拆单！");
        //            return;
        //        }

        //    }

        //    //插入拆单的mongdb日志
        //    var logname = "增量分销同步订单拆单";
        //    var logcontext = LogForOperatorContext.Current;
        //    var mongdblog = logcontext.Begin(new LogForOperator
        //    {
        //        StartTime = DateTime.Now,
        //        Description = new OperationDescription()
        //        {
        //            Url = System.Web.HttpContext.Current?.Request?.Url?.ToString(),
        //            Referrer = System.Web.HttpContext.Current?.Request?.UrlReferrer?.ToString(),
        //            UserAgent = System.Web.HttpContext.Current?.Request?.UserAgent,
        //            Name = logname
        //        },
        //        TotalCount = 0,
        //        TraceId = System.Web.HttpContext.Current?.Request?["traceId"],
        //        //DBType = SiteContext.Current?.DataBaseType.ToString(),
        //        CurObjectId = logid,
        //        UserId = SiteContext.Current.CurrentFxUserId,
        //        OperatorType = logname,
        //        ShopId = shop.Id,
        //    });
        //    //乐观锁
        //    var op = OptimisticLockOperationType.SplitToLogicOrder;
        //    var opId = $"FX{sid}";
        //    try
        //    {
        //        _commonSettingService.ExcuteWithOptimisticLock(() =>
        //        {
        //            //4.拆单，查询出店铺数据库中所有待发货且未拆的单进行拆单
        //            int pageSize = 500;
        //            int totalpage = porders.Count / pageSize + (porders.Count % pageSize == 0 ? 0 : 1);
        //            for (int i = 0; i < totalpage; i++)
        //            {
        //                var notSplitOrders = porders.Skip(i * pageSize).Take(pageSize).ToList();
        //                //排除已经拆过的单子
        //                var exitsorder = _logicOrderService.getLogicOrderIdByPlatformId(notSplitOrders.Select(x => x.PlatformOrderId).Distinct().ToList());
        //                notSplitOrders = notSplitOrders.Where(o => !exitsorder.Any(eo => eo == o.PlatformOrderId)).ToList();

        //                mongdblog.TotalCount += notSplitOrders.Count;
        //                if (notSplitOrders == null || notSplitOrders.Any() == false)
        //                {
        //                    if (i == 0)
        //                    {
        //                        SyncOrderLog.Debug($"没有待拆单的订单", SiteContext.Current.CurrentFxUserId, sid);
        //                    }
        //                    break;
        //                }
        //                if (notSplitOrders.Count() == pageSize)
        //                    Thread.Sleep(1000);
        //                SyncOrderLog.Debug($"获取到{notSplitOrders.Count()}个待拆订单", SiteContext.Current.CurrentFxUserId, sid);
        //                CheckOrderProduct(notSplitOrders, isOfflineOrder);
        //                //SplitLogicOrders(notSplitOrders, isOfflineOrder);
        //                SplitLogicOrdersNew(notSplitOrders, isOfflineOrder, mongdblog, tag);

        //                //logcontext.StartStep(SplitLogicOrdersWithLog(notSplitOrders, isOfflineOrder));                    
        //                logcontext.EndStep();
        //            }
        //            SyncOrderLog.Debug($"拆单完成！", SiteContext.Current.CurrentFxUserId, sid);
        //            mongdblog.Remark = "拆单完成";
        //            //logcontext.End();
        //            return true;
        //        }, () =>
        //        {
        //            mongdblog.Exception += $"店铺【{sid}】同步完成后，拆单动作未获取到锁";
        //            SyncOrderLog.Debug($"店铺【{sid}】同步完成后，拆单动作未获取到锁", SiteContext.Current.CurrentFxUserId, sid);
        //            Log.Debug($"店铺【{sid}】同步完成后，拆单动作未获取到锁");
        //        }, op, opId);
        //    }
        //    catch (Exception ex)
        //    {
        //        mongdblog.Exception += $"拆单异常：{ex}";
        //        throw ex;
        //    }
        //    finally
        //    {
        //        logcontext.End();
        //    }
        //}

        /// <summary>
        /// 待发货订单拆分逻辑单
        /// </summary>
        /// <param name="waitSendOrders"></param>
        /// <param name="isOfflineOrder"></param>
        /// <param name="tag"></param>
        /// <param name="currentUserId"></param>
        /// <param name="fxPageType">执行拆单来源订单页面</param>
        /// <param name="traceBatchId"></param>
        /// <param name="isSendColdHotStorageMessage">是否发送冷热存储消息</param>
        /// <param name="splitOrderLogItem"></param>
        public void SplitLogicOrders(List<Order> waitSendOrders, bool isOfflineOrder,
            string tag = "", int currentUserId = 0, string traceBatchId = null, int fxPageType = 0, 
            bool isSendColdHotStorageMessage = false, SplitLogicOrderHistoryLogItemModel splitOrderLogItem = null)
        {
            //Log.Debug($"SplitLogicOrders 进入拆单逻辑，待拆订单：{waitSendOrders.ToJson()}", Log.LogDirectory + "wm_order.txt");
            if (waitSendOrders == null || !waitSendOrders.Any())
            {
                return;
            }

            //耗时监控
            var sw = new Stopwatch();
            sw.Start();

            #region 1.汇总订单项产品编码,根据产品编码查询路径流
            var allOrderItems = waitSendOrders.SelectMany(f => f.OrderItems);
            var productCodes = allOrderItems.Select(f => f.ProductCode).Distinct().ToList();
            var pathFlowService = new PathFlowService(_connectionString);
            var pathFlows = pathFlowService.GetPathFlows(productCodes, 0);
            //停止耗时监控
            sw.Stop();
            //拆单日志收集
            if (splitOrderLogItem != null)
            {
                splitOrderLogItem.GetPathFlowDurations = sw.Elapsed.TotalMilliseconds;
            }
            sw.Restart();
            //兼容新旧数据补上Config信息
            new ProductFxService(_connectionString).CompatibleOldDataToAddConfig(pathFlows);
            
            if (pathFlows.Any() == false)
            {
                var message =
                    $@"拆单异常，订单产品未找到路径流信息，要检查产品生成路径流的代码。店铺Id：【{waitSendOrders.Select(f => f.ShopId).Distinct().ToJson()}】，订单Id：【{waitSendOrders.Select(f => f.PlatformOrderId).ToJson()}】";

                //SyncOrderLog.Debug($"拆单异常，订单产品未找到路径流信息，要检查产品生成路径流的代码。店铺Id：【{waitSendOrders.Select(f => f.ShopId).Distinct().ToJson()}】，订单Id：【{waitSendOrders.Select(f => f.PlatformOrderId).ToJson()}】", SiteContext.Current.CurrentFxUserId, waitSendOrders.First().ShopId);
                Log.WriteError(message);
            }

            //按产品code分组
            //var pathFlowDict = productPathFlowRefs.ToDictionary(f => f.PathFlowRefCode, f => f);
            //由于PathFlowReference表数据有重复，所以先分组，再取最新的。（商品同步生成路径流及商品引用，未找到重复原因，可定能是同步商品，和全量同步，同步商品并发插入的）
            // 商品绑定多厂家，可能有多条重复路径数据
            //var pathFlowDict = productPathFlowRefs.GroupBy(f => f.PathFlowRefCode).ToDictionary(f => f.Key, f => f.OrderByDescending(p => p.Id).ToList());

            // 提前解析省市区Model
            foreach (var item in pathFlows)
            {
                foreach (var kv in item.PathFlowReferences)
                {
                    var configs = kv.Value.ReferenceConfigs;
                    foreach (var config in configs)
                    {
                        if (config.ConfigType == 1)
                        {
                            config.ProvinceInfos = config.Config.ToList<ThreeAreaFisrtModel>();
                        }
                    }
                }
            }
            #endregion

            Dictionary<int, OfflineOrderSwitchModel> offlineOrderSwitchDic = new Dictionary<int, OfflineOrderSwitchModel>();
            if (isOfflineOrder)
            {
                #region 1.1 线下单自定义配置开关
                var configKey = "/ErpWeb/Offline/OfflineOrderSwitch";
                var waitSendOrderUserIds = waitSendOrders.Select(x => x.UserId).Distinct().ToList();
                var waitSendOrderUserShops = _fxUserShopService.GetShopListByPlatformType(waitSendOrderUserIds, PlatformType.System.ToString(), null);
                var waitSendOrderUserShopIds = waitSendOrderUserShops.Select(x => x.ShopId).Distinct().ToList();
                var configList = _commonSettingService.GetSettingByShopIds(configKey, waitSendOrderUserShopIds);
                waitSendOrderUserShops.ForEach(s =>
                {
                    if (offlineOrderSwitchDic.ContainsKey(s.FxUserId) == false)
                    {
                        var configModel = configList.FirstOrDefault(x => x.ShopId == s.ShopId);
                        if (configModel != null)
                            offlineOrderSwitchDic.Add(s.FxUserId, configModel.Value.ToObject<OfflineOrderSwitchModel>());
                        else
                            offlineOrderSwitchDic.Add(s.FxUserId, null);
                    }
                });
                #endregion
            }

            #region 2.遍历订单做拆单动作
            var logicOrderList = new List<LogicOrder>();
            var firstOrder = waitSendOrders.First();
            var defaultPathFlowCode = PathFlowService.CreateDefaultPathFlowCode(firstOrder.ShopId, firstOrder.UserId);
            var defaultPathFlowRef = new PathFlowReference { PathFlowCode = defaultPathFlowCode };
            var failSplitPlatformOrderId = new List<string>();
            var lastOrderId = firstOrder.Id;
            var repeatOrderItemDic = new Dictionary<string, List<LogicOrderItem>>();
            foreach (var order in waitSendOrders)
            {
                //是否是TikTok订单
                var isCrossBorderOrder = order.PlatformType == PlatformType.TikTok.ToString();

                //2.1.匹配订单项的路劲流
                //var isAllMatch = true;
                if (order.Id > lastOrderId)
                    lastOrderId = order.Id;
                foreach (var oi in order.OrderItems)
                {
                    List<PathFlow> pflows = pathFlows.Where(x =>
                        x.PathFlowReferences.ContainsKey(oi.SkuCode) ||
                        x.PathFlowReferences.ContainsKey(oi.ProductCode)).ToList();

                    //如果是跨境订单，订单项PackageStatus 不为 TO_FULFILL ，则设置为自营
                    //2024 0920 与产品确认 PackageStatus为 TO_FULFILL、PROCESSING 、NULL（未知） 可进行匹配
                    if (pflows.Any() && (isCrossBorderOrder && (oi.PackageStatus == "TO_FULFILL"|| oi.PackageStatus == "PROCESSING" || string.IsNullOrEmpty(oi.PackageStatus)) || !isCrossBorderOrder))
                    {
                        // 添加匹配规则判断
                        var flowRef = pflows.SelectMany(x => x.PathFlowReferences)
                            .FirstOrDefault(x =>
                                (x.Key == oi.SkuCode && x.Value.PathFlowRefType == "Sku") ||
                                (x.Key == oi.ProductCode && x.Value.PathFlowRefType == "Product")).Value;
                        var o = new GetPathflowCodeModel()
                        {
                            PlatformOrderId = order.PlatformOrderId, ToProvince = order.ToProvince,
                            ToCity = order.ToCity, CreateTime = order.CreateTime, ProductCode = oi.ProductCode,
                            SkuCode = oi.SkuCode
                        };
                        var newPathFlowCode = _prodcutFxService.IsMatchConfigNew(o, pflows);
                        if (newPathFlowCode.IsNotNullOrEmpty())
                            flowRef.PathFlowCode = newPathFlowCode;
                        oi.PathFlowRef = flowRef.ToJson().ToObject<PathFlowReference>();
                    }
                    else
                    {
                        oi.PathFlowRef = defaultPathFlowRef;
                        var warningMessage =
                            $"拆单警告，订单：【{order.PlatformOrderId}】的订单项【{oi.SubItemID}】，用户ID:【{firstOrder.UserId}】，店铺ID：【{firstOrder.ShopId}】产品ID【{oi.ProductID}】，产品Code【{oi.ProductCode}】未找到路径流，已经给设置了默认的路径流：{defaultPathFlowCode}";
                        //SyncOrderLog.Debug(warningMessage, SiteContext.Current.CurrentFxUserId, waitSendOrders.First().ShopId);
                        Log.WriteWarning(warningMessage);
                        //订单项的商品没有设置流，则匹配系统，分给自己...必须要有路径流，不然即使分给自己，也查询不出来数据，查询依赖路径流
                        //oiProductPathFlow.Add();
                        //(new PathFlowService(_connectionString)).CreateDefaultPathFlow(oi.ShopId, oi.UserId);
                    }
                }

                ////存在订单项没有路劲流，则放弃订单拆分
                //if (isAllMatch == false)
                //    continue;

                // 2.2.按叶子节点的的处理人id分组拆单（2021-02-01 按最终处理人拆分有问题，订单中间流程会有问题）
                //var splitOrderGroup = order.OrderItems.GroupBy(oi => oi.PathFlow.LeafNode.FxUserId);

                // 2.2.按订单项的路劲流code分组拆单
                var splitOrderGroup = order.OrderItems.GroupBy(oi => oi.PathFlowRef.PathFlowCode);
                foreach (var group in splitOrderGroup)
                {
                    var lo = CreateLogicOrder(order, group.ToList(), group.Key, isOfflineOrder);
                    var isExist = false;
                    foreach (var oi in lo.LogicOrderItems)
                    {
                        var key = $"{oi.PlatformOrderId}_{oi.OrderItemCode}";
                        if (repeatOrderItemDic.ContainsKey(key) == false)
                            repeatOrderItemDic.Add(key, new List<LogicOrderItem> { oi });
                        else
                        {
                            repeatOrderItemDic[key].Add(oi);
                            isExist = true;
                        }
                    }

                    if (isExist == false)
                        logicOrderList.Add(lo);
                }

                if (!logicOrderList.Where(x => x.PlatformOrderId == order.PlatformOrderId).Any())
                    failSplitPlatformOrderId.Add(order.PlatformOrderId);
            }

            if (failSplitPlatformOrderId.Any())
                Log.WriteWarning($"店铺【{firstOrder.ShopId}】拆单时有些订单拆单失败，订单号：{failSplitPlatformOrderId.ToJson()}");
            #endregion

            #region 3.生成逻辑单id
            var now = DateTime.Now; // 同一批次保存的系统单统一创建时间
            logicOrderList.GroupBy(f => new { f.PlatformType, f.FxUserId }).ToList().ForEach(group =>
            {
                var logicOrderIds = ProduceSystemUniqueId(group.Key.PlatformType, group.Key.FxUserId, group.Count());
                var logicOrderGroup = group.ToList();
                for (var j = 0; j < logicOrderGroup.Count; j++)
                {
                    var logicOrder = logicOrderGroup[j];
                    logicOrder.LogicOrderId = logicOrderIds[j];
                    logicOrder.LogicOrderItems.ForEach(loi =>
                    {
                        loi.LogicOrderId = logicOrder.LogicOrderId;
                        loi.CreateTime = now;
                        Log.Debug($"逻辑单项冗余字段日志（{loi.LogicOrderId}），相关信息：{loi.ToJson(true)}", "LogicOrderItemBuild.log");
                    });
                    logicOrder.Tags.ForEach(t => t.OiCode = logicOrder.LogicOrderId);
                    // 3.1 线下单是否要审核状态，默认是待审核
                    if (isOfflineOrder)
                    {
                        offlineOrderSwitchDic.TryGetValue(group.Key.FxUserId, out OfflineOrderSwitchModel switchModel);
                        // 线下单页面执行拆单，按配置来是否审核
                        if (switchModel == null || switchModel?.IsOrderCheck == false)
                            logicOrder.ApprovalStatus = ApprovalType.OfflineApproved.ToInt();
                        // 待发页面执行线下单，是不需要审核的
                        if (fxPageType == FxPageType.WaitPrint.ToInt())
                            logicOrder.ApprovalStatus = ApprovalType.OfflineApproved.ToInt();
                    }
                }
            });

            #endregion

            //停止耗时监控
            sw.Stop();
            
            //拆单日志收集
            if (splitOrderLogItem != null)
            {
                splitOrderLogItem.SplitPrevDurations = sw.Elapsed.TotalMilliseconds;
            }

            //IsOutDbFieldLen(logicOrderList);
            var exceptionMessages = new List<string>();

            try
            {
                //增量同步拆单分配的路径
                #region 4.审核流程
                try
                {
                    var newLogicOrderList = logicOrderList;
                    //4.1 新线下单默认状态是待审核，线下单过滤已审核的订单
                    if (isOfflineOrder)
                    {
                        newLogicOrderList = logicOrderList.Where(l => l.ApprovalStatus != ApprovalType.OfflineApproved.ToInt()).ToList();
                    }
                    //保存逻辑订单之前先进行手工单审核流程的创建
                    var checkOrder = new TemplateCheckOrder(new AssignLogicOrderFactory(),
                        new SplistLogicOrderFactory(), new CheckOrderFactory());
                    checkOrder.LogicOrderGenCheck(newLogicOrderList, traceBatchId, isAddChangeLog: false);
                }
                catch (Exception ex)
                {
                    var message = $"手工审核异常：{ex}";
                    //异常消息
                    exceptionMessages.Add(message);
                    //记录异常
                    Log.WriteError(message);

                    throw ex;
                }
                //sw.Stop();
                //SyncOrderLog.Debug($"手工单审核耗时：{sw.Elapsed.TotalSeconds}s", SiteContext.Current.CurrentFxUserId, firstOrder.ShopId);
                //sw.Restart();

                #endregion

                #region 京东供销平台的订单被拆成多单之后需要打上订单标签
                if (logicOrderList.Any(t=>t.PlatformType == PlatformType.JingdongPurchase.ToString()))
                {
                    var jdLogicOrderList = logicOrderList.Where(t => t.PlatformType == PlatformType.JingdongPurchase.ToString()).ToList();
                    if (jdLogicOrderList.GroupBy(t=>t.PlatformOrderId).Any(t=>t.Count()>1))
                    {
                        jdLogicOrderList.GroupBy(t => t.PlatformOrderId).Where(t => t.Count() > 1).ToList().ForEach(t =>
                        {
                            //所有路经流上都需要标签
                            t.GroupBy(x => x.PathFlowCode).ToList().ForEach(item =>
                            {
                                var first = item.First();
                                first.OrderTags.Add(new OrderTags()
                                {
                                    OiCode = first.PlatformOrderId,
                                    Sid = first.ShopId,
                                    Platform = first.PlatformType,
                                    Tag = JingdongPurchaseTagType.JingdongPurchase_splitOrder.ToString(),
                                    TagType = TagType.Order.ToString(),
                                    TagValue = "1",
                                    Status = 0,
                                    CreateTime = DateTime.Now,
                                    PathFlowCode = item.Key
                                });
                            });
                        });
                    }
                }
                #endregion

                #region 5.保存逻辑单标签
                try
                {
                    //5.保存逻辑单标签
                    var orderTags = logicOrderList.SelectMany(s => s.OrderTags).Where(t => t.OiCode.IsNotNullOrEmpty())
                        .ToList();
                    _orderTagService.BulkInsert(orderTags, isAddChangeLog: false);
                }
                catch (Exception ex)
                {
                    var message = $"保存逻辑单标签异常：{ex}";
                    CommUtls.WriteToLog(message, $"BulkSplitTagsExt-{firstOrder.ShopId}.txt", "UpdateSplitFlagExt");
                }
                #endregion

                #region 6.保存逻辑单
                var saveLogicOrderExceptionStep = 0;
                var pids = logicOrderList?.Select(x => x.PlatformOrderId)?.Distinct()?.ToList();
                try
                {
                    //调整拆单标记顺序，先将标记标上
                    TryUpdateOrderAsSplited(pids, firstOrder.ShopId);
                    saveLogicOrderExceptionStep = 1;
                    //6.保存逻辑单
                    _logicOrderService.BulkInsert(logicOrderList, tag: tag);
                    saveLogicOrderExceptionStep = 2;
                    //这个未用到，先注释
                    //_commonSettingService.Set("/Shop/Fendan/LastSplitOrderId", lastOrderId.ToString(), firstOrder.ShopId);
                    //已发状态，拆单，发送冷热分离消息，进行冷热分离（暂时仅用于售后单同步）
                    SendColdHotStorageMessages(currentUserId, logicOrderList, isSendColdHotStorageMessage);
                }
                catch (Exception ex)
                {
                    //保存配置有可能会失败，新增重试逻辑
                    for (var i = 0; i < 3; i++)
                    {
                        try
                        {
                            var settingId = _commonSettingService.Set("/Shop/Fendan/Config/IsUpdateOrderAsSplitedFailed", "1", firstOrder.ShopId);
                            if (settingId > 0)
                            {
                                var commonSettingModel = _commonSettingService.Get("/Shop/Fendan/Config/IsUpdateOrderAsSplitedFailed", firstOrder.ShopId);
                                if (commonSettingModel != null && commonSettingModel.Value == "1")
                                {
                                    break;
                                }
                            }

							if (i == 2)
							{
								exceptionMessages.Add($"保存/Shop/Fendan/Config/IsUpdateOrderAsSplitedFailed配置失败：settingId={settingId}");
							}
						}
                        catch (Exception e)
                        {
                            if (i == 2)
                            {
								//异常消息
								exceptionMessages.Add($"保存/Shop/Fendan/Config/IsUpdateOrderAsSplitedFailed配置出现异常：{e}");
							}
                        }
                    }

                    //根据异常步骤来判断是否需要回滚
                    if (saveLogicOrderExceptionStep == 0)
                        TryResetOrderSplitedTag(pids, firstOrder.ShopId);
                    else if (saveLogicOrderExceptionStep == 1)
                    {
                        //保存逻辑单异常
                        //将未保存成功的逻辑单对应的平台单的拆单标记重置为0，下次继续拆
                        var savedLogicOrders = _logicOrderService.GetOrdersByPlatformOrderIds(pids, firstOrder.ShopId, new List<string> { "PlatformOrderId" }) ?? new List<LogicOrder>();
                        var savedPids = savedLogicOrders.Select(x => x.PlatformOrderId).Distinct().ToList() ?? new List<string>();
                        var needResetPids = pids.Where(x => savedPids.Contains(x) == false).ToList() ?? new List<string>();
                        //重置未保存成功的逻辑单的拆单标记
                        if (needResetPids.Any())
                            TryResetOrderSplitedTag(needResetPids, firstOrder.ShopId);
                    }
                    //查询标记
                    var message = $"保存逻辑单异常：{ex}，{(saveLogicOrderExceptionStep == 0 ? "更新原始单拆单标记为1失败" : "保存系统订单失败")}";
                    //异常消息
                    exceptionMessages.Add(message);
                    //记录异常日志
                    throw ex;
                }
                #endregion

                #region 7.处理1688关联的逻辑单的发货方式
                try
                {
                    new PurchaseOrderDeliveryModeService().ProcessLogicOrderDeliveryMode(logicOrderList);
                }
                catch (Exception ex)
                {
                    CommUtls.WriteToLog($"处理1688的发货方式时异常，logicOrderList.PlatformOrderId={logicOrderList?.Select(a => a.PlatformOrderId).ToJson()}，异常信息：{ex.ToJson()}", $"PurchaseOrderDeliveryMode-{firstOrder.ShopId}.txt", "PurchaseOrderDeliveryMode");
                }
                #endregion

                #region 利润统计模块同步利润订单
                try
                {
                    if (ProfitOrderService.SUPPORTED_PLATFORM.Contains(logicOrderList?.FirstOrDefault()?.PlatformType?.ToLower()))
                    {
                        // 只同步已发货 已完结 已关闭的订单
                        var syncState = new List<string>() { "sended", "success","close" };
                        var syncLogicOrderIds = logicOrderList.Where(lo => syncState.Contains(lo.ErpState)).Select(lo => lo.LogicOrderId).ToList();
                        new ProfitStatisticsCloudMessageService().SendMsgForSyncOrder(syncLogicOrderIds);
                    }
                    
                }
                catch(Exception ex)
                {
                    Log.WriteError($"利润统计-拆单发送同步利润订单消息发生异常：{ex.ToJson()}", ModuleFileName.ProfitStatistics);
                }
                #endregion
                //sw.Stop();
                //SyncOrderLog.Debug($"更新拆单标识耗时：{sw.Elapsed.TotalSeconds}s", SiteContext.Current.CurrentFxUserId, firstOrder.ShopId);
                //sw.Restart();
            }
            catch (Exception ex)
            {
                var warningMessage =
                    $"拆单批量入库 用户ID:【{firstOrder.UserId}】，店铺ID：【{firstOrder.ShopId}】，异常信息：{ex},内部异常信息：{ex.InnerException}";
                throw new Exception(warningMessage);
            }
            finally
            {
                WriteBusinessLogs("OrderFxService.SplitLogicOrders", logicOrderList, traceBatchId, currentUserId,
                    string.IsNullOrWhiteSpace(tag) ? "SyncAfterSaleWithOrder" : tag,
                    isOfflineOrder, exceptionMessages);
            }
        }

        public void TryResetOrderSplitedTag(List<string> platformOrderIds, int shopId)
        {
            new OrderRepository().TryResetOrderSplitedTag(platformOrderIds, shopId);
        }

        /// <summary>
        /// 发送冷热分离消息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="logicOrderList"></param>
        /// <param name="isSendColdHotStorageMessage"></param>
        private static void SendColdHotStorageMessages(int fxUserId, List<LogicOrder> logicOrderList,
            bool isSendColdHotStorageMessage = false)
        {
            try
            {
                //是否满足发送消息条件
                if (!isSendColdHotStorageMessage ||
                    !DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage() ||
                    !SiteContext.Current.IsWriteToColdDb || !logicOrderList.Any())
                {
                    return;
                }

                //逻辑订单待发货状态
                var logicOrderWaitSellerSendStatus = new List<string>
                    { "waitbuyerpay", "waitaudit", "waitsellersend", "inrefund" };
                //取冷逻辑单ID
                var logicOrderIdsByCold = logicOrderList
                    .Where(m => !logicOrderWaitSellerSendStatus.Contains(m.ErpState))
                    .Select(m => m.LogicOrderId).Distinct().ToList();
                if (!logicOrderIdsByCold.Any())
                {
                    return;
                }
                //分片发送冷热分离消息
                var logicOrderIdsByChunks = logicOrderIdsByCold.ChunkList(50);
                logicOrderIdsByChunks.ForEach(chunk =>
                {
                    //发送冷热分离消息
                    DataSyncStorageMessageService.SendMessage(new DataSyncStorageMessageModel
                    {
                        MessageId = Guid.NewGuid().ToString().ToShortMd5(),
                        CloudPlatformType = CustomerConfig.CloudPlatformType,
                        SourceFxUserId = fxUserId,
                        TargetFxUserId = fxUserId,
                        ColdHotType = 1,
                        BusinessIds = chunk,
                        Source = "SplitLogicOrder"
                    });
                    //日志
                    Log.WriteLine($"售后单同步存在已关闭订单拆单，用户ID:{fxUserId}，逻辑单：{chunk.ToJson()}",
                        $"SplitLogicOrderColdHotStorage_{DateTime.Now:yyyy-MM-dd}.log");
                });
            }
            catch (Exception ex)
            {
                Log.WriteError($"售后单同步存在已关闭订单拆单，发送冷热分离消息异常，用户ID:{fxUserId}，异常信息：{ex}",
                    $"SplitLogicOrderColdHotStorageError_{DateTime.Now:yyyy-MM-dd}.log");
            }
        }

        /// <summary>
        /// 写业务日志
        /// </summary>
        /// <param name="methodName"></param>
        /// <param name="logicOrders"></param>
        /// <param name="traceBatchId"></param>
        /// <param name="creatorId"></param>
        /// <param name="sourceType"></param>
        /// <param name="isOfflineOrder"></param>
        /// <param name="exceptionMessages"></param>
        public void WriteBusinessLogs(string methodName, List<LogicOrder> logicOrders, string traceBatchId,
            int creatorId,
            string sourceType,
            bool isOfflineOrder,
            List<string> exceptionMessages)
        {
            try
            {
                var logs = logicOrders.Select(m => new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = traceBatchId,
                    SourceType = sourceType,
                    PlatformType = m.PlatformType,
                    CreatorId = creatorId,
                    FxUserId = m.FxUserId,
                    ShopId = m.ShopId,
                    BusinessType = BusinessTypes.Order.ToString(),
                    BusinessId = m.PlatformOrderId,
                    SysBusinessId = m.LogicOrderId,
                    SubBusinessType = SubBusinessTypes.SplitLogicOrder.ToString(),
                    Content = new
                    {
                        m.PathFlowCode,
                        m.MergeredType,
                        m.MergeredOrderId,
                        m.ChildOrderId,
                        IsOfflineOrder = isOfflineOrder,
                        m.CreateTime,
                        m.DecryptField,
                        Items = m.LogicOrderItems?.Select(s => new { s.SubItemId, s.ProductID, s.ProductCode })
                    }
                        .ToJson(true),
                    ExceptionMessage = string.Join(" > ", exceptionMessages)
                }).ToList();
                BusinessLogDataEventTrackingService.Instance.WriteLog(logs);
            }
            catch (Exception e)
            {
                Log.WriteError($"写业务日志异常，异常原因：{e.Message}，堆栈信息：{e.StackTrace}",
                    $"BusinessLogError_{DateTime.Now:yyyy-MM-dd}.log");
            }
        }

        public void TryUpdateOrderAsSplited(List<string> platformOrderIds, int shopId)
        {
            var hasError = new OrderRepository(_connectionString).TryUpdateOrderAsSplited(platformOrderIds, shopId);
            var isUpdateOrderAsSplitedFailed = hasError ? "1" : "0";
            _commonSettingService.Set("/Shop/Fendan/Config/IsUpdateOrderAsSplitedFailed", isUpdateOrderAsSplitedFailed, shopId);

            //CommUtls.WriteToLog($"TryUpdateOrderAsSplited：更新IsUpdateOrderAsSplitedFailed = {IsUpdateOrderAsSplitedFailed}", $"UpdateSplitFlagExt-{shopId}.txt", "UpdateSplitFlagExt");
        }

        public void SplitLogicOrdersNew(List<Order> waitSendOrders, bool isOfflineOrder, LogForOperator mongdblog = null, string tag = "")
        {
            if (waitSendOrders == null || !waitSendOrders.Any())
            {
                return;
            }
            //1.汇总订单项产品编码,根据产品编码查询路径流
            var allOrderItems = waitSendOrders.SelectMany(f => f.OrderItems);
            var productCodes = allOrderItems.Select(f => f.ProductCode).Distinct().ToList();
            var pathFlowService = new PathFlowService(_connectionString);
            var pathFlows = pathFlowService.GetPathFlows(productCodes, 0);
            //兼容新旧数据补上Config信息
            new ProductFxService(_connectionString).CompatibleOldDataToAddConfig(pathFlows);

            if (pathFlows.Any() == false)
            {
                //SyncOrderLog.Debug($"拆单异常，订单产品未找到路径流信息，要检查产品生成路径流的代码。店铺Id：【{waitSendOrders.Select(f => f.ShopId).Distinct().ToJson()}】，订单Id：【{waitSendOrders.Select(f => f.PlatformOrderId).ToJson()}】", SiteContext.Current.CurrentFxUserId, waitSendOrders.First().ShopId);
                Log.WriteError($@"拆单异常，订单产品未找到路径流信息，要检查产品生成路径流的代码。店铺Id：【{waitSendOrders.Select(f => f.ShopId).Distinct().ToJson()}】，订单Id：【{waitSendOrders.Select(f => f.PlatformOrderId).ToJson()}】");
                //return;
            }
            //按产品code分组
            //var pathFlowDict = productPathFlowRefs.ToDictionary(f => f.PathFlowRefCode, f => f);
            //由于PathFlowReference表数据有重复，所以先分组，再取最新的。（商品同步生成路径流及商品引用，未找到重复原因，可定能是同步商品，和全量同步，同步商品并发插入的）
            // 商品绑定多厂家，可能有多条重复路径数据
            //var pathFlowDict = productPathFlowRefs.GroupBy(f => f.PathFlowRefCode).ToDictionary(f => f.Key, f => f.OrderByDescending(p => p.Id).ToList());

            // 提前解析省市区Model
            foreach (var item in pathFlows)
            {
                foreach (var kv in item.PathFlowReferences)
                {
                    var configs = kv.Value.ReferenceConfigs;
                    foreach (var config in configs)
                    {
                        if (config.ConfigType == 1)
                        {
                            config.ProvinceInfos = config.Config.ToList<ThreeAreaFisrtModel>();
                        }
                    }
                }
            }

            //2.遍历订单做拆单动作
            var logicOrderList = new List<LogicOrder>();
            var firstOrder = waitSendOrders.First();
            var defaultPathFlowCode = PathFlowService.CreateDefaultPathFlowCode(firstOrder.ShopId, firstOrder.UserId);
            var defaultPathFlowRef = new PathFlowReference { PathFlowCode = defaultPathFlowCode };
            var failSplitPlatformOrderId = new List<string>();
            foreach (var order in waitSendOrders)
            {

                //2.1.匹配订单项的路劲流
                var isAllMatch = true;
                foreach (var oi in order.OrderItems)
                {
                    List<PathFlow> pflows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(oi.ProductCode) || x.PathFlowReferences.ContainsKey(oi.SkuCode)).ToList();
                    if (pflows.Any())
                    {
                        // 添加匹配规则判断
                        var flowRef = pflows.SelectMany(x => x.PathFlowReferences).FirstOrDefault(x => x.Key == oi.SkuCode || x.Key == oi.ProductCode).Value;
                        //var newPathFlowCode = IsMatchConfig(order, oi, pflows);
                        var o = new GetPathflowCodeModel() { PlatformOrderId = order.PlatformOrderId, ToProvince = order.ToProvince, ToCity = order.ToCity, CreateTime = order.CreateTime, ProductCode = oi.ProductCode, SkuCode = oi.SkuCode };
                        var newPathFlowCode = _prodcutFxService.IsMatchConfigNew(o, pflows);
                        if (newPathFlowCode.IsNotNullOrEmpty())
                            flowRef.PathFlowCode = newPathFlowCode;
                        oi.PathFlowRef = flowRef.ToJson().ToObject<PathFlowReference>();
                    }
                    else
                    {
                        oi.PathFlowRef = defaultPathFlowRef;
                        var warningMessage = $"拆单警告，订单：【{order.PlatformOrderId}】的订单项【{oi.SubItemID}】，用户ID:【{firstOrder.UserId}】，店铺ID：【{firstOrder.ShopId}】产品ID【{oi.ProductID}】，产品Code【{oi.ProductCode}】未找到路径流，已经给设置了默认的路径流：{defaultPathFlowCode}";
                        //SyncOrderLog.Debug(warningMessage, SiteContext.Current.CurrentFxUserId, waitSendOrders.First().ShopId);
                        Log.WriteWarning(warningMessage);
                        //订单项的商品没有设置流，则匹配系统，分给自己...必须要有路径流，不然即使分给自己，也查询不出来数据，查询依赖路径流
                        //oiProductPathFlow.Add();
                        //(new PathFlowService()).CreateDefaultPathFlow(oi.ShopId, oi.UserId);
                    }
                }

                //存在订单项没有路劲流，则放弃订单拆分
                if (isAllMatch == false)
                    continue;

                // 2.2.按叶子节点的的处理人id分组拆单（2021-02-01 按最终处理人拆分有问题，订单中间流程会有问题）
                //var splitOrderGroup = order.OrderItems.GroupBy(oi => oi.PathFlow.LeafNode.FxUserId);

                // 2.2.按订单项的路劲流code分组拆单
                var splitOrderGroup = order.OrderItems.GroupBy(oi => oi.PathFlowRef.PathFlowCode);
                foreach (var group in splitOrderGroup)
                {
                    logicOrderList.Add(CreateLogicOrder(order, group.ToList(), group.Key, isOfflineOrder));
                }
                if (!logicOrderList.Where(x => x.PlatformOrderId == order.PlatformOrderId).Any())
                    failSplitPlatformOrderId.Add(order.PlatformOrderId);
            }

            //3.生成逻辑单id
            logicOrderList.GroupBy(f => new { f.PlatformType, f.FxUserId }).ToList().ForEach(group =>
            {
                var logicOrderIds = ProduceSystemUniqueId(group.Key.PlatformType, group.Key.FxUserId, group.Count());
                var logicOrderGroup = group.ToList();
                for (var j = 0; j < logicOrderGroup.Count; j++)
                {
                    var logicOrder = logicOrderGroup[j];
                    logicOrder.LogicOrderId = logicOrderIds[j];
                    logicOrder.LogicOrderItems.ForEach(loi => loi.LogicOrderId = logicOrder.LogicOrderId);
                    logicOrder.Tags.ForEach(t => t.OiCode = logicOrder.LogicOrderId);
                }
            });
            //IsOutDbFieldLen(logicOrderList);            
            try
            {
                // 增量同步拆单分配的路径
                if (mongdblog != null)
                {
                    var orderPaths = logicOrderList.GroupBy(x => x.PathFlowCode).Select(x => new
                    {
                        Code = x.Key,
                        Order = x.Select(y => new { oid = y.LogicOrderId, pid = y.PlatformOrderId })
                    }).ToList();
                    mongdblog.Detail = orderPaths;
                }

                //保存逻辑订单之前先进行手工单审核流程的创建
                TemplateCheckOrder checkorder = new TemplateCheckOrder(new AssignLogicOrderFactory(), new SplistLogicOrderFactory(), new CheckOrderFactory());
                checkorder.LogicOrderGenCheck(logicOrderList);
                //4.保存逻辑单
                _logicOrderService.BulkInsert(logicOrderList, tag: tag);
                //更新IsPreordain=1
                TryUpdateOrderAsSplited(logicOrderList?.Select(x => x.PlatformOrderId)?.ToList(), firstOrder.ShopId);
                //_logicOrderService.UpdateRemark
            }
            catch (Exception ex)
            {
                //拆单失败的话,需要更新IsSplist标志
                //5.更新P_Order是否拆单标识(IsSplit)
                OrderSplitRecordService spliteRecord = new OrderSplitRecordService();
                List<OrderSplitRecord> splitrecords = new List<OrderSplitRecord>();
                waitSendOrders.Where(o => o.PlatformStatus == "waitsellersend").ToList().ForEach(lo =>
                  {
                      OrderSplitRecord record = new OrderSplitRecord();
                      record.PlatformOrderId = lo.PlatformOrderId;
                      record.IsSplited = false;
                      splitrecords.Add(record);
                  });
                spliteRecord.Update(splitrecords);

                var warningMessage = $"拆单批量入库异常，{ex.Message}，用户ID:【{firstOrder.UserId}】，店铺ID：【{firstOrder.ShopId}】";
                if (mongdblog != null)
                    mongdblog.Exception += warningMessage;
                //SyncOrderLog.Debug(warningMessage, SiteContext.Current.CurrentFxUserId, waitSendOrders.First().ShopId);
                throw ex;
            }

        }


        private bool IsOutDbFieldLen(List<LogicOrder> orders)
        {
            var isOut = false;
            try
            {
                if (orders == null || orders.Any() == false)
                    return isOut;

                var oFields = _logicOrderService.GetDbSchemaFieldInfos("LogicOrder")?.Where(x => x.Type == "string").ToList();
                var oiFields = _logicOrderService.GetDbSchemaFieldInfos("LogicOrderItem")?.Where(x => x.Type == "string").ToList();

                var outSb = new StringBuilder();
                foreach (var o in orders)
                {
                    var outField = string.Empty;
                    var oDic = o.ToDictionary();
                    oFields.ForEach(f =>
                    {
                        object val;
                        var len = 0;
                        if (oDic.TryGetValue(f.Name, out val) && (len = val.ToString2().Length) > f.Len)
                        {
                            //outField = CustomerConfig.DbFieldToName(f.Name, "P_Order");
                            outSb.AppendLine($"订单【{o.PlatformOrderId}】字段【{f.Name}】长度为：{len} ，超出数据库长度【{f.Len}】");
                        }
                    });

                    o.LogicOrderItems.ForEach(oi =>
                    {
                        var oiDic = oi.ToDictionary();
                        oiFields.ForEach(f =>
                        {
                            object val;
                            var len = 0;
                            if (oiDic.TryGetValue(f.Name, out val) && (len = val.ToString2().Length) > f.Len)
                            {
                                //outField = CustomerConfig.DbFieldToName(f.Name, "P_OrderItem");
                                outSb.AppendLine($"订单【{o.PlatformOrderId}】字段【{f.Name}】长度为：{len} ，超出数据库长度【{f.Len}】");
                            }
                        });
                    });
                }
                Log.WriteError(outSb.ToString2());
            }
            catch (Exception ex)
            {
                Log.WriteError($"验证订单是否超出字段长度异常：{ex}");
            }
            return isOut;
        }

        #region 获取PathflowCode统一放到ProductFxService处理
        //public string IsMatchConfig(Order o, OrderItem oi, List<PathFlow> pathFlows)
        //{
        //    var newPathFlowCode = string.Empty;
        //    if (pathFlows == null || pathFlows.Any() == false)
        //        return newPathFlowCode;

        //    var maxLen = pathFlows.Max(x => x.PathFlowNodes.Count);
        //    // 补全路径排序长度
        //    pathFlows.ForEach(x =>
        //    {
        //        var pathIndex = x.PathConfigIndex;
        //        if (pathIndex.Length < maxLen)
        //        {
        //            var str = "";
        //            var count = maxLen - pathIndex.Length;
        //            for (int i = 0; i < count; i++)
        //            {
        //                str += "9";
        //            }
        //            x.PathConfigIndex = pathIndex + str;
        //        }
        //    });

        //    var prePathFlowCode = string.Empty;
        //    var skuRefPaths = pathFlows.Where(x => x.PathFlowReferences.Values.Any(y => y.PathFlowRefCode == oi.SkuCode && y.PathFlowRefType == "Sku")).ToList();
        //    skuRefPaths = skuRefPaths.OrderBy(x => x.PathConfigIndex).ToList();

        //    Log.Debug($"PlatformOrderId:{oi.PlatformOrderId},ProductCode:{oi.ProductCode},SkuCode:{oi.SkuCode},\nskuRefPaths={skuRefPaths.ToJson()}", "huanbang.txt");

        //    //1. 先匹配Sku规则
        //    foreach (var skuPathFlow in skuRefPaths)
        //    {
        //        if (skuPathFlow.PathFlowReferences.ContainsKey(oi.SkuCode) == false)
        //            continue;

        //        var configs = skuPathFlow.PathFlowReferences[oi.SkuCode].ReferenceConfigs;
        //        var sortNodes = skuPathFlow.SortedNodes;
        //        foreach (var node in sortNodes)
        //        {
        //            if (!skuPathFlow.IsSelf && node.DownFxUserId == 0)
        //                continue;

        //            var config = configs.FirstOrDefault(x => x.PathFlowRefCode == oi.SkuCode && x.FxUserId == node.FxUserId && x.PathFlowCode == node.PathFlowCode);
        //            if (config != null)
        //            {
        //                if (config.ConfigType == 1)
        //                {
        //                    //省市区规则匹配
        //                    var isMatched = IsAddressMatched(o, config);
        //                    if (isMatched)
        //                        newPathFlowCode = config.PathFlowCode;
        //                    else
        //                    {
        //                        // 路径上一个节点未匹配，则继续匹配下一条
        //                        newPathFlowCode = string.Empty;
        //                        break;
        //                    }
        //                }
        //                else if (config.ConfigType == 2)
        //                {
        //                    //时间规则匹配
        //                    var isMatched = IsTimeMatched(o, config);
        //                    if (isMatched)
        //                        newPathFlowCode = config.PathFlowCode;
        //                    else
        //                    {
        //                        newPathFlowCode = string.Empty;
        //                        break;
        //                    }
        //                }
        //                else if (config.ConfigType == 0)
        //                {
        //                    //默认厂家
        //                    newPathFlowCode = config.PathFlowCode;
        //                }
        //            }
        //            else
        //            {
        //                //默认厂家（兼容旧数据）
        //                newPathFlowCode = node.PathFlowCode;
        //            }
        //        }

        //        if (newPathFlowCode.IsNotNullOrEmpty())
        //            return newPathFlowCode;
        //    }

        //    if (newPathFlowCode.IsNotNullOrEmpty())
        //        return newPathFlowCode;

        //    // 2. Sku无满足条件匹配，再查商品规则
        //    var productRefPaths = pathFlows.Where(x => x.PathFlowReferences.Values.Any(y => y.ProductCode == oi.ProductCode && y.PathFlowRefType == "Product")).OrderByDescending(x => x.Id).ToList();
        //    productRefPaths = productRefPaths.OrderBy(x => x.PathConfigIndex).ToList();

        //    Log.Debug($"newPathFlowCode:{newPathFlowCode},PlatformOrderId:{oi.PlatformOrderId},ProductCode:{oi.ProductCode},SkuCode:{oi.SkuCode},\nproductRefPaths={productRefPaths.ToJson()}", "huanbang.txt");

        //    foreach (var productPathFlow in productRefPaths)
        //    {
        //        if (productPathFlow.PathFlowReferences.ContainsKey(oi.ProductCode) == false)
        //            continue;

        //        var configs = productPathFlow.PathFlowReferences[oi.ProductCode].ReferenceConfigs.OrderBy(x => x.ConfigType).ToList();
        //        var sortNodes = productPathFlow.SortedNodes;
        //        foreach (var node in sortNodes)
        //        {
        //            if (!productPathFlow.IsSelf && node.DownFxUserId == 0)
        //                continue;

        //            var config = configs.FirstOrDefault(x => x.PathFlowRefCode == oi.ProductCode && x.FxUserId == node.FxUserId && x.PathFlowCode == node.PathFlowCode);
        //            if (config != null)
        //            {
        //                // 配置厂家
        //                if (config.ConfigType == 1)
        //                {
        //                    //省市区规则匹配
        //                    var isMatched = IsAddressMatched(o, config);
        //                    if (isMatched)
        //                        newPathFlowCode = config.PathFlowCode;
        //                    else
        //                        break;
        //                }
        //                else if (config.ConfigType == 2)
        //                {
        //                    //时间规则匹配
        //                    var isMatched = IsTimeMatched(o, config);
        //                    if (isMatched)
        //                        newPathFlowCode = config.PathFlowCode;
        //                    else
        //                        break;
        //                }
        //                else if (config.ConfigType == 0)
        //                {
        //                    //默认厂家
        //                    newPathFlowCode = config.PathFlowCode;
        //                }
        //            }
        //            else
        //            {
        //                //默认厂家（兼容旧数据）
        //                newPathFlowCode = node.PathFlowCode;
        //            }
        //        }

        //        if (newPathFlowCode.IsNotNullOrEmpty())
        //            return newPathFlowCode;
        //    }
        //    return newPathFlowCode;
        //}

        //private bool IsAddressMatched(Order o, PathFlowReferenceConfig config)
        //{
        //    var isMatched = false;
        //    config.ProvinceInfos?.ForEach(pc =>
        //    {
        //        var province = pc.ProvinceInfo;
        //        if (province != null && o.ToProvince.ToString2().Contains(province.Name.ToString2()))
        //        {
        //            if (province.isCheckAll)
        //            {
        //                isMatched = true;
        //                return;
        //            }
        //            else
        //            {
        //                var cityLst = pc.CityLst;
        //                if (cityLst != null && cityLst.Any())
        //                {
        //                    isMatched = cityLst.Any(x => o.ToCity.ToString2().Contains(x.CityInfo.Name.ToString2()));
        //                    if (isMatched)
        //                        return;
        //                }
        //            }
        //        }
        //    });
        //    return isMatched;
        //}
        //private bool IsTimeMatched(Order o, PathFlowReferenceConfig config)
        //{
        //    var isMatched = false;
        //    var timeArr = config.Config.SplitToList(",");
        //    if (timeArr.Count == 2)
        //    {
        //        var startTime = (o.CreateTime.Value.ToString("yyyy-MM-dd") + " " + timeArr[0]).toDateTime();
        //        var endTime = (o.CreateTime.Value.ToString("yyyy-MM-dd") + " " + timeArr[1]).toDateTime();
        //        isMatched = o.CreateTime.Value >= startTime && o.CreateTime.Value <= endTime;
        //    }
        //    return isMatched;
        //} 
        #endregion

        /// <summary>
        /// 订单拆分逻辑单
        /// </summary>
        /// <param name="order"></param>
        /// <param name="orderItems"></param>
        /// <param name="pathFlowCode"></param>
        /// <param name="isOfflineOrder"></param>
        private LogicOrder CreateLogicOrder(Order order, List<OrderItem> orderItems, string pathFlowCode, bool isOfflineOrder)
        {
            var logicOrder = new LogicOrder();
            var erpExceptionStatus = GetLogicExceptionStatus(orderItems);
            logicOrder.PlatformOrderId = order.PlatformOrderId;
            logicOrder.ErpState = GetLogicErpStatus(orderItems);
            logicOrder.ErpRefundState = BasePlatformService.TransferOrderRefundStatus(new Order() { OrderItems = orderItems }).RefundStatus;
            logicOrder.ExceptionStatus = erpExceptionStatus.Key;
            logicOrder.ExceptionReason = erpExceptionStatus.Value;
            logicOrder.SystemRemark = string.Empty;
            logicOrder.PrintRemark = string.Empty;
            logicOrder.MergeredType = 0;
            logicOrder.MergeredOrderId = string.Empty;
            logicOrder.ChildOrderId = string.Empty;
            #region 
            logicOrder.ToName = order.ToName;
            logicOrder.ToPhone = order.ToMobile ?? order.ToPhone;
            logicOrder.ToProvince = order.ToProvince;
            logicOrder.ToCity = order.ToCity;
            logicOrder.ToCounty = order.ToCounty;
            logicOrder.ToTown = order.ToTown;
            logicOrder.ToAddress = order.ToAddress;
            logicOrder.ToFullAddress = order.ToFullAddress;
            #endregion
            logicOrder.OrderBuyerHashCode = order.DbBuyerHashCodeV2;//新版（去除收件信息）需要
            logicOrder.ReceiverHashCode = order.DbReceiverHashCode;
            logicOrder.ToPhoneIndex = order.ToPhoneIndex;

            logicOrder.PlatformType = order.PlatformType;
            logicOrder.CreateTime = order.CreateTime;
            logicOrder.OrderCode = order.OrderCode;
            //logicOrder.LogicOrderId = //后面统一赋值
            logicOrder.PathFlowCode = pathFlowCode;
            logicOrder.FxUserId = order.UserId;
            logicOrder.ShopId = order.ShopId;
            logicOrder.TotalWeight = (order.TotalWeight ?? 0) * 1000; // kg=>g
            logicOrder.TotalAmount = order.TotalAmount ?? 0;
            logicOrder.ProductPlatformIdCount = orderItems.Where(oi => oi.PlatformOrderId == order.PlatformOrderId).GroupBy(oi => oi.ProductID).Count();
            logicOrder.ProductCount = orderItems.Where(oi => oi.PlatformOrderId == order.PlatformOrderId).Count();
            logicOrder.ProductKindCount = orderItems.GroupBy(oi => oi.SkuID ?? (oi.ProductSubject + oi.Color + oi.Size + oi.ExtAttr1 ?? "" + oi.ExtAttr2 ?? "" + oi.ExtAttr3 ?? "" + oi.ExtAttr4 ?? "" + oi.ExtAttr5 ?? ""))?.Count() ?? 0;
            logicOrder.ProductItemCount = orderItems.Where(oi => oi.PlatformOrderId == order.PlatformOrderId)?.Sum(oi => oi.Count ?? 0) ?? 0;
            logicOrder.PayTime = order.PayTime;
            logicOrder.BuyerRemark = order.BuyerRemark.ToCutString(512);
            logicOrder.SellerRemark = order.SellerRemark.ToString2();
            logicOrder.SellerRemarkFlag = order.SellerRemarkFlag.ToString2();
            logicOrder.TradeType = order.TradeType;
            logicOrder.LastShipTime = order.LastShipTime;
            //平台补贴和运费
            logicOrder.PlatformSubsidy = order.PlatformSubsidy ?? 0;
            logicOrder.ShippingFee = order.ShippingFee ?? 0;
            logicOrder.POrderItemCount = order.OrderItems?.Count??0; //跨境新加的字段
            logicOrder.PlatformStatus = order.PlatformStatus; //跨境新加的字段
            logicOrder.FulfillmentType = order.FulfillmentType; //跨境 履约方式
            logicOrder.ToCountry = order.ToCountry; //跨境国家字段
            logicOrder.ShippingType= order.ShippingType;//跨境发货方式
            logicOrder.DeliveryOptionId= order.DeliveryOptionId;//跨境交运方式
																//logicOrder.CollectionType = order.CollectionType; //跨境新加的字段

			logicOrder.DeliveryOptionName = order.DeliveryOptionName;//跨境交运类型


			if (order.PlatformType == PlatformType.Alibaba.ToString())
            {
                logicOrder.DecryptField = order.ExtField3; //1688收件人信息加密串
                logicOrder.ExtField1 = order.ExtField5;
                logicOrder.ExtField2 = order.OrderFrom;
                logicOrder.ExtField3 = order.BusinessType;
            }
            else if (order.PlatformType == PlatformType.Taobao.ToString() || order.PlatformType == PlatformType.AlibabaC2M.ToString() || order.PlatformType == PlatformType.TaobaoMaiCaiV2.ToString())
                logicOrder.DecryptField = order.ExtField3;
            else if (order.PlatformType == PlatformType.Jingdong.ToString() || order.PlatformType == PlatformType.JingdongPurchase.ToString()) {

                // logicOrder.DecryptField = order.ExtField5.IsNullOrEmpty() ? order.ExtField4 : order.ExtField5;
                //京东新OAID模式，ExtField5 原手机号密文为空 ，ExtField6为新的OAID
                if (order.ExtField5.IsNullOrEmpty() && order.ExtField6.IsNotNullOrEmpty()) {
                    logicOrder.ExtField1 = order.ExtField6;
                    logicOrder.DecryptField = order.ExtField6;
                }
            }
            else if (order.PlatformType == PlatformType.XiaoHongShu.ToString())
            {
                if (order.ExtField3.IsNotNullOrEmpty())
                {
                    logicOrder.DecryptField = order.ExtField3;//小红书openAddressId
                }
                else
                {
                    logicOrder.DecryptField = order.ExtField2;
                }
                logicOrder.ExtField1 = order.ExtField1;
                logicOrder.ExtField2 = order.ExtField2;
            }
            else if (order.PlatformType == PlatformType.TuanHaoHuo.ToString())
            {
                logicOrder.DecryptField = order.ExtField2;//P_Order.ExtField2，可用于合并，等同于淘宝平台的OAID作用
                logicOrder.ExtField1 = order.ExtField1;//P_Order.ExtField1，即对应：KuaiShouEncryptedReceiverInfo.RelationCode
            }

            else if (order.PlatformType == PlatformType.KuaiShou.ToString())
            {
                if (order.ExtField3.IsNotNullOrEmpty())
                {
                    logicOrder.DecryptField = order.ExtField3;
                    logicOrder.ExtField3 = order.ExtField3;
                }
                else
                {
                    logicOrder.DecryptField = order.ExtField2;
                }
                logicOrder.ExtField1 = order.ExtField1;
            }

            else if (order.PlatformType == PlatformType.Suning.ToString())
                logicOrder.DecryptField = order.ExtField1;
            else if (order.PlatformType == PlatformType.Pinduoduo.ToString())
            {
                logicOrder.ExtField1 = order.BusinessType;  //业务类型：2=跨境单
                logicOrder.ExtField2 = order.IsWeiGong ? "1" : "";  //业务类型：1=风控订单
                logicOrder.DecryptField = order.ExtField3;
            }
            else if (order.PlatformType == PlatformType.TouTiao.ToString() || order.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
            {
                logicOrder.ExtField2 = order.ExtField2;
                logicOrder.DecryptField = order.ExtField3;  //open_address_id
                // 预约发货订单 
                if (order.ExtField2 == OrderTag.appointment_ship_time.ToString())
                    logicOrder.ExtField3 = order.ReceiptDate?.ToString("yyyy-MM-dd");
                // 送礼单(参与合并计算列的判断)
                if (order.ExtField5 == "1")
                    logicOrder.ExtField1 = order.ExtField5;
                // 风控订单(参与合并计算列的判断)
                if (order.ExtField6 == OrderTag.risk_processing.ToString())
                    logicOrder.ExtField1 = order.ExtField6;
                
                // 即时零售平台，如果可以转为int, 补充仓库Id
                if (order.PlatformType == nameof(PlatformType.TouTiaoSaleShop) 
                    && order.Warehouse != null
                    && int.TryParse(order.Warehouse, out var warehouseId))
                    logicOrder.WarehouseId = warehouseId;
            }
            else if (order.PlatformType == PlatformType.WxVideo.ToString())
            {
                if (order.ExtField1.IsNotNullOrEmpty() && order.ExtField2.IsNotNullOrEmpty())
                {
                    logicOrder.ExtField1 = order.ExtField1;
                    logicOrder.ExtField2 = order.ExtField2;
                    //logicOrder.DecryptField = order.BuyerMemberId; //对应的是openId，参与合并计算列
                }

                //20231103 使用平台提供的地址唯一码
                if (order.ExtField4.IsNotNullOrEmpty())
                    logicOrder.DecryptField = order.ExtField4;
            }            
            else if (order.PlatformType == PlatformType.Other_Heliang.ToString())
            {
                //将禾量平台的订单ExtField1（套餐商品的明细）作为逻辑单的分发备注
                logicOrder.SystemRemark = order.ExtField1;
            }
            else if (order.PlatformType == PlatformType.OwnShop.ToString())
            {
                //将开放平台的订单ExtField2作为逻辑单的分发备注
                logicOrder.SystemRemark = order.ExtField2;
                //开放平台密文单的收件人信息加密串
                logicOrder.DecryptField = order.ExtField3;
                //开放平台密文单来源的平台
                logicOrder.ExtField1 = order.ExtField5;
            }
            else if (order.PlatformType == nameof(PlatformType.Virtual))
            {
                // 判断是否要处理扩展字段，加密线下单逻辑
                if (order.ExtField7.IsNotNullOrEmpty())
                {
                    // 加密线下单来源平台
                    logicOrder.ExtField3 = order.ExtField5;
                    // 加密线下单来源平台单号
                    logicOrder.ExtField2 = order.ExtField2;
                    // 加密线下单来源平台的oaid
                    logicOrder.DecryptField = order.ExtField3;
                }
                // 明文线下单保存平台单号
                if (order.ExtField2.IsNotNullOrEmpty()) logicOrder.ExtField2 = order.ExtField2;
            }
            //logicOrder.BuyerHashCode =
            //logicOrder.PrintState =
            //logicOrder.SendState =
            //logicOrder.ExpressPrintTime =
            //logicOrder.ExpressPrintTimes =
            //logicOrder.PrintedSerialNumber =
            //logicOrder.LastExpressPrintTemplateId =
            //logicOrder.LastWaybillCode =
            //logicOrder.SendPrintTime =
            //logicOrder.NahuoPrintTime =
            //logicOrder.OnlineSendTime =
            //logicOrder.WarehouseId =
            //logicOrder.IsPreviewed =

            decimal totalWeight = 0;
            foreach (var oi in orderItems)
            {
                totalWeight += oi.Weight ?? 0;
                var logicOi = new LogicOrderItem()
                {
                    LogicOrderId = "", //后面统一赋值
                    OrderItemCode = oi.OrderItemCode,
                    PlatformOrderId = oi.PlatformOrderId,
                    SubItemId = oi.SubItemID,
                    ShopId = oi.ShopId,
                    ProductCode = oi.ProductCode,
                    SkuCode = oi.SkuCode,
                    OrignalOrderId = "",
                    ItemProductId = oi.ProductID,
                    ItemSkuId = oi.SkuID,
                    ItemCount = oi.Count ?? 0,
                    ItemColor = oi.Color,
                    ItemSize = oi.Size,
                    PackageId = oi.PackageId,
                    PackageStatus = oi.PackageStatus,
                    TracakingNumber = oi.TracakingNumber,
                    MergeItemJSON = oi.ExtAttr1,
                };
                Log.Debug($"逻辑单项冗余字段日志（{logicOi.LogicOrderId}），相关信息：{logicOi.ToJson(true)}", "LogicOrderItemBuild.log");
                logicOrder.LogicOrderItems.Add(logicOi);
            }
            logicOrder.TotalWeight = totalWeight;

            //线下单批量保存时更新
            if (isOfflineOrder)
            {
                logicOrder.ApprovalStatus = ApprovalType.OfflinePending.ToInt();
                logicOrder.PrintRemark = order.ExtField1;
            }
            if (CustomerConfig.SplitNeedHandleTagPlatformType.Contains(order.PlatformType))
            {
                //平台出现顺丰包邮的订单项需要打上异常单标签
                var orderTags = orderItems.SelectMany(oi => oi.Tags).Distinct().ToList();
                if (orderTags.Any(t => CustomerConfig.ExceptionOrderManageTagNames.Contains(t.Tag)))
                {
                    logicOrder.Tags.Add(new OrderTags()
                    {
                        Sid = logicOrder.ShopId,
                        Platform = logicOrder.PlatformType,
                        Tag = OrderTag.ExceptionOrder.ToString(),
                        TagType = TagType.LogicOrder.ToString(),
                        Status = 0,
                        CreateTime = DateTime.Now,
                        SourceFxUserId = logicOrder.FxUserId
                    });
                }
            }
            return logicOrder;
        }

        ///// <summary>
        ///// 获取异常状态【移至CostOrderService】
        ///// </summary>
        ///// <returns></returns>
        //private KeyValuePair<int, string> GetLogicExceptionStatus(List<OrderItem> ois)
        //{
        //    ////自营单不计算异常状态
        //    //if (ois.Any(f => f.UserId == SiteContext.Current.CurrentFxUserId))
        //    //    return new KeyValuePair<int, string>(0, "");

        //    if (ois.Any(f => f.Status?.ToLower() == "waitbuyerreceive"
        //                && f.RefundStatus.IsNotNullOrEmpty()
        //                && f.RefundStatus?.ToUpper() == "WAIT_SELLER_AGREE"))
        //        return new KeyValuePair<int, string>(1, "已发货，申请退款");
        //    else if (ois.Any(f => f.Status?.ToLower() == "waitbuyerreceive"
        //                 && f.RefundStatus.IsNotNullOrEmpty()
        //                 && (f.RefundStatus?.ToUpper() == "WAIT_SELLER_RECEIVE"
        //                 || f.RefundStatus?.ToUpper() == "WAIT_BUYER_MODIFY")))
        //        return new KeyValuePair<int, string>(1, "已发货，退款中");
        //    else if (ois.Any(f => f.Status?.ToLower() == "waitbuyerreceive"
        //                 && f.RefundStatus.IsNotNullOrEmpty()
        //                 && (f.RefundStatus?.ToUpper() == "REFUND_SUCCESS"
        //                 || f.RefundStatus?.ToUpper() == "REFUND_PART_SUCCESS")))
        //        return new KeyValuePair<int, string>(1, "已发货，退款成功");
        //    else if (ois.Any(f => f.Status?.ToLower() == "waitbuyerreceive"
        //                && f.RefundStatus.IsNotNullOrEmpty()
        //                && f.RefundStatus?.ToUpper() != "REFUND_CLOSE"))
        //        return new KeyValuePair<int, string>(1, "已发货，退款中");

        //    return new KeyValuePair<int, string>(0, "");
        //}

        ///// <summary>
        ///// 待发货waitsellersend，已发货sended，退款中inrefund，已完成 【移至CostOrderService】
        ///// </summary>
        ///// <returns></returns>
        //public static string GetLogicErpStatus(List<OrderItem> ois)
        //{
        //    string status = "";
        //    if (ois.Any(f => f.Status?.ToLower() == "waitsellersend"))
        //    {
        //        status = "waitsellersend"; //待发货
        //        if (ois.Where(x => x.Status == "waitsellersend").All(f => f.RefundStatus.IsNotNullOrEmpty() && f.RefundStatus?.ToUpper() != "REFUND_CLOSE"))
        //        {
        //            status = "inrefund";  //退款中
        //        }
        //    }
        //    else if (ois.Any(f => f.Status?.ToLower() == "waitbuyerreceive"))
        //    {
        //        status = "sended";  //已发货
        //    }
        //    else if (ois.Any(f => f.Status?.ToLower() == "success"))
        //    {
        //        status = "success";  //交易成功
        //    }
        //    else if (ois.Any(f => f.Status?.ToLower() == "close" || f.Status?.ToLower() == "cancel" || f.Status?.ToLower() == "REFUND_SUCCESS"))
        //    {
        //        status = "close";  //交易关闭
        //    }

        //    if (string.IsNullOrEmpty(status))
        //    {
        //        status = "success";  //交易成功
        //        Log.WriteError($"逻辑单ERP状态匹配错误:{ois.Select(f => new { f.PlatformOrderId, f.SubItemID, f.SkuID, f.Status, f.RefundStatus }).ToJson()}");
        //    }

        //    return status;
        //}


        /// <summary>
        /// 商家订单消息处理
        /// </summary>
        /// <param name="orders"></param>
        public void FxOrderMessageProcess(List<Order> orders)
        {
            if (orders == null || orders.Any() == false) return;
            var sids = orders.Select(f => f.ShopId); //汇总所有的订单店铺id
            //1.查询出 商家，平台店铺，及系统店铺
            var fxUser_PlatformShop_SystemShop = (new UserFxService()).GetUserFxAndShopsByShopIds(null, sids);
            if (fxUser_PlatformShop_SystemShop == null || fxUser_PlatformShop_SystemShop.Any() == false) return;
            //按店铺分组
            orders.GroupBy(f => f.ShopId).ToList().ForEach(g =>
            {
                try
                {
                    var sid = g.Key;
                    //店鋪所属的商家及系统店铺
                    var fxUserSystemShop = fxUser_PlatformShop_SystemShop.FirstOrDefault(f => f.Item2.Id == sid);
                    if (fxUserSystemShop == null) return;
                    //实例化sitecontext
                    var sc = new SiteContext(fxUserSystemShop.Item1, new SiteContextConfig() { NeedRelationShops = false, NeedShopExpireTime = false });
                    //var syncFxOrderService = new SyncFxOrderService(false, fxUserSystemShop.Item1.Id);
                    //syncFxOrderService.SyncSingleOrders(g.Select(o => o.PlatformOrderId).ToList(), fxUserSystemShop.Item2);
                    //订单推入分销用户的库
                    var agentOrderService = new OrderService();
                    var orderList = new List<Order>();
                    var fxUserId = fxUserSystemShop.Item1.Id;
                    g.ToList().ForEach(item =>
                    {
                        item.UserId = fxUserId;
                        item.OrderItems.ForEach(f =>
                        {
                            f.UserId = fxUserId;
                            f.CreateTime = DateTime.Now;
                        });
                        orderList.Add(item);
                    });
                    agentOrderService.BulkMergerFxFromMessage(orderList, new OrderMergerParameterModel { IsFromMessage = true, IsDontUpdateRefundStatus = false });
                }
                catch (Exception ex)
                {
                    Log.WriteError($"处理订单消息，订单推送到商家报错：" + ex.ToString());
                    Log.WriteError($@"参数->【order.shopid】:{g.Key},
                                        【order】:{g.ToList()?.ToJson()},
                                        【fxUser_PlatformShop_SystemShop】:{fxUser_PlatformShop_SystemShop?.FirstOrDefault(f => f.Item2.Id == g.Key)?.ToJson()}");
                }
            });

        }

        /// <summary>
        /// 替换产品信息
        /// </summary>
        /// <param name="oi"></param>
        /// <param name="skuBindSku"></param>
        public void ReplaceOrderItemInfo(OrderItem oi, string supplierSku)
        {
            //var sku = product.Skus.FirstOrDefault(f => f.SKU == supplierSku);
            ////圖片
            //if (string.IsNullOrWhiteSpace(sku.ImgUrl) == false)
            //    oi.ProductImgUrl = sku.ImgUrl;
            ////产品名称
            //oi.ProductSubject = product.Name;
            ////规格
            //oi.Color = sku.Name;
            //if (oi.Size != null && oi.Color?.Contains(oi.Size) == true)
            //    oi.Size = "";
            ////产品Id
            //oi.ProductID = product.Id.ToString();
            ////skuId
            //oi.SkuID = sku.SKU;
            ////货号，厂家打印面单用于配货
            //oi.CargoNumber = sku.SKU;
            //oi.productCargoNumber = sku.SKU;
        }

        /// <summary>
        /// 根据所有订单项重新计算主单的打印状态
        /// </summary>
        /// <param name="childs"></param>
        /// <returns></returns>
        public static int GetLogicOrderPrintState(List<LogicOrderItem> childs)
        {
            if (childs.All(x => x.PrintState == 1))
                return 1;//全部已打印
            else if (childs.Any(x => x.PrintState == 1))
                return 2;//部分打印
            else
                return 0;//未打印
        }

        /// <summary>
        /// 针对指定PlatformOrderId且未拆单的订单进行拆单
        /// </summary>
        /// <param name="platformOrderIds"></param>
        /// <param name="sid">Shop.Id</param>
        /// <param name="logId"></param>
        /// <param name="isSendColdHotStorageMessage"></param>
        public void SplitLogicOrderByPlatformOrderId(List<string> platformOrderIds, int sid, string logId = "", 
            bool isSendColdHotStorageMessage = false)
        {
            //插入拆单的mongdb日志
            var logname = "同步订单拆单-ByPlatformOrderId";
            var logcontext = LogForOperatorContext.Current;
            var mongdblog = logcontext.Begin(new LogForOperator
            {
                StartTime = DateTime.Now,
                Description = new OperationDescription()
                {
                    Url = System.Web.HttpContext.Current?.Request?.Url?.ToString(),
                    Referrer = System.Web.HttpContext.Current?.Request?.UrlReferrer?.ToString(),
                    UserAgent = System.Web.HttpContext.Current?.Request?.UserAgent,
                    Name = logname
                },
                TotalCount = 0,
                TraceId = System.Web.HttpContext.Current?.Request?["traceId"],
                //DBType = SiteContext.Current?.DataBaseType.ToString(),
                CurObjectId = logId,
                UserId = SiteContext.Current.CurrentFxUserId,
                OperatorType = logname,
                ShopId = sid,
            });

            //乐观锁
            var op = OptimisticLockOperationType.SplitToLogicOrder;
            var opId = $"FX{sid}";
            try
            {
                _commonSettingService.ExecuteWithOptimisticLock(() =>
                {
                    var isUserOldQuery = _commonSettingService.Get("/Shop/Fendan/Config/IsUpdateOrderAsSplitedFailed", sid)?.Value == "1";
                    if (isUserOldQuery)
                        _repository.UpdateSplitFlagExt(sid);

                    var batchSize = 500;
                    var count = Math.Ceiling(platformOrderIds.Count * 1.0 / batchSize);
                    for (var i = 0; i < count; i++)
                    {
                        var batchCodes = platformOrderIds.Skip(i * batchSize).Take(batchSize).ToList();

                        //拆单，查询出店铺数据库中指定PlatformOrderId且未拆的单进行拆单
                        var notSplitOrders =
                            _repository.GetNotSplitOrdersByPlatformOrderId(batchCodes, sid, isUserOldQuery);
                        if (notSplitOrders == null || !notSplitOrders.Any())
                        {
                            return true;
                        }
                        Log.Debug(
                            () =>
                                $"SplitLogicOrderByPlatformOrderId，查询到待拆原始单：{notSplitOrders.Select(p => p.PlatformOrderId).ToJson()}",
                            "wm_order2.txt");
                        
                        mongdblog.TotalCount += notSplitOrders.Count();

                        //SyncOrderLog.Debug($"ByPlatformOrderId获取到{notSplitOrders.Count()}个待拆订单", SiteContext.Current.CurrentFxUserId, sid);
                        CheckOrderProduct(notSplitOrders.ToList(), false);
                        //拆单处理
                        SplitLogicOrders(notSplitOrders.ToList(), false,
                            currentUserId: SiteContext.Current.CurrentFxUserId,
                            traceBatchId: logId, isSendColdHotStorageMessage: isSendColdHotStorageMessage);

                    }
                    logcontext.EndStep();
                    //SyncOrderLog.Debug($"ByPlatformOrderId拆单完成！", SiteContext.Current.CurrentFxUserId, sid);
                    mongdblog.Remark = "拆单完成";
                    return true;
                }, () =>
                {
                    mongdblog.Exception += $"店铺【{sid}】同步售后完成后，拆单动作未获取到锁";
                    //SyncOrderLog.Debug($"店铺【{sid}】同步售后完成后，拆单动作未获取到锁", SiteContext.Current.CurrentFxUserId, sid);
                    Log.Debug($"店铺【{sid}】同步售后完成后，拆单动作未获取到锁");
                }, op, opId);
            }
            catch (Exception ex)
            {
                mongdblog.Exception += $"售后-拆单异常：{ex}";
                throw ex;
            }
            finally
            {
                logcontext.End();
            }
        }

        /// <summary>
        /// 统计订单数量
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        public int GetStatOrder(int shopId, string status = "waitsellersend")
        {
            return _repository.GetStatOrder(shopId, status);
        }
        /// <summary>
        /// 获取不存在平台订单号列表
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="platformOrderIds"></param>
        /// <returns></returns>
        public List<string> GetExistsPlatformOrderIds(int shopId, List<string> platformOrderIds)
        {
            //判空处理
            if (platformOrderIds == null || !platformOrderIds.Any())
            {
                return new List<string>();
            }

            //存在的平台订单
            var orders = _repository.GetOrderIdAndOrderFromId(platformOrderIds, shopId);
            return orders.Select(m => m.PlatformOrderId).ToList();
        }

        /// <summary>
        /// 按路径流统计逻辑单数量
        /// </summary>
        /// <param name="pathFlowCode"></param>
        /// <returns></returns>
        public int GetStatLogicOrderByPathFlow(string pathFlowCode)
        {
            return _repository.GetStatLogicOrderByPathFlow(pathFlowCode);
        }

        /// <summary>
        /// 通过订单编号手动同步订单
        /// </summary>
        /// <param name="shop"></param>
        /// <param name="isEnableAsync"></param>
        /// <param name="pidList"></param>
        /// <param name="userAction"></param>
        /// <returns></returns>
        public Tuple<bool, string,List<string>> ManualSyncOrderByIds(List<string> pidList, Shop shop,bool isEnableAsync = false,UserActionRecord userAction = null)
        {
            //var actionService = new UserActionRecordService();

            if (pidList.IsNullOrEmptyList())
            {
                //actionService.SetStatus(userAction, -1);
                return Tuple.Create(false, "未传入需要同步的订单Id", pidList);
            }

            if (shop == null)
            {
                //actionService.SetStatus(userAction, -1);
                throw new ArgumentNullException(nameof(shop));
            }
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            
            // 当前库如果不是用户的库，则需要切换到用户的库
            // 当前库
            var currentDb = SiteContext.Current.CurrentDbConfig;
            // 用户所在的库
            var userDb = DbPolicyExtension.GetConfigFx(new List<int> { SiteContext.GetCurrentShopId() })
                .OrderByDescending(a => a.DbConfig.FromFxDbConfig).FirstOrDefault();
            var userDbName = userDb?.DbNameConfig?.DbName;
            if (userDb == null || userDbName.IsNullOrEmpty())
            {
                Log.WriteError($"{fxUserId}用户通过订单编号手动同步订单，未查询到用户所在库！", ModuleFileName.ManualSyncOrder);
                //actionService.SetStatus(userAction, -1);
                return Tuple.Create(false, "系统繁忙，请稍后再试",pidList);
            }

            var userFx = SiteContext.Current.CurrentFxUser;
            if (currentDb?.DbNameConfig?.DbName != userDbName)
            {
                var sc = new SiteContext(userFx, userDbName);
            }

            if (!isEnableAsync)
            {
                var tuple = DoManualSyncOrderByIds(pidList, shop);
                if (tuple.Item1)
                {
                    if(tuple.Item3.IsNotNullAndAny())
                        return Tuple.Create(true, "订单不存在或同步失败", tuple.Item3);
                    else
                        return Tuple.Create(true, "同步完成", new List<string>());
                }
                else
                {
                    return Tuple.Create(false, tuple.Item2, tuple.Item3);
                }
                    

                //actionService.SetStatus(userAction, -1,customDetail:tuple.Item2);
            }
                

            // 异步执行
            Task.Run(() =>
            {
                Tuple<bool, string, List<string>> result=null;
                try
                {
                    result = DoManualSyncOrderByIds(pidList, shop);
                    if (!result.Item1)
                    {
                        //actionService.SetStatus(userAction, -1,customDetail: result.Item2);
                    }
                }
                catch(Exception ex)
                {
                    result = Tuple.Create(false, "系统繁忙，请稍后再试", pidList);
                    if (userAction != null)
                    {
                        //actionService.SetStatus(userAction,-1,customDetail: $"{ex}");
                    }
                }
                finally
                {
                    if (RedisPool.IsInit)
                    {
                        RedisHelper.Set($"{MANUAL_SYNC_ORDER_RESULT_KEY}:{fxUserId}", result, 60 * 8);
                    }
                    
                }
            });

            return Tuple.Create(true, "201",new List<string>());
        }

        private Tuple<bool,string,List<string>> DoManualSyncOrderByIds(List<string> pidList,Shop shop)
        {
            var fxUserId = SiteContext.GetCurrentFxUserId();
            var failedOrderIds = new List<string>();
            try
            {
                //淘宝同步：忽略任何异常
                var orders = new SyncFxOrderService(fxUserId).SyncSingleOrders(pidList, shop,isIgnoreAnyException:true);
                var orderIds = orders.Select(o => o.PlatformOrderId).ToList();
                failedOrderIds = pidList.Except(orderIds).ToList();
                Log.Debug(() => $"{fxUserId}用户通过订单编号手动同步订单成功。数量：{orders.Count}，成功单号：{orderIds.ToJson()}，失败单号：{failedOrderIds.ToJson()}", ModuleFileName.ManualSyncOrder);
            }
            catch (Exception ex)
            {
                Log.WriteError($"{fxUserId}用户通过订单编号手动同步订单发生异常：{ex}", ModuleFileName.ManualSyncOrder);
                if ((ex.Message?.Contains("订单不存在") ?? false) || (ex.InnerException?.Message.Contains("订单不存在") ?? false))
                    return Tuple.Create(false, "订单不存在或同步失败", pidList);

                if ((ex.Message?.Contains("暂不支持该平台") ?? false) || (ex.InnerException?.Message.Contains("暂不支持该平台") ?? false))
                    return Tuple.Create(false, ex.Message, pidList);

                return Tuple.Create(false, "系统繁忙，请稍后再试", pidList);
            }

            try
            {
                SplitLogicOrdersWithLog(shop, isOfflineOrder: false);
            }
            catch (Exception ex)
            {
                Log.WriteError($"{fxUserId}用户通过订单编号手动同步订单后执行拆单发生异常：{ex}", ModuleFileName.ManualSyncOrder);
            }

            return failedOrderIds.IsNullOrEmptyList() ? Tuple.Create(true,string.Empty,failedOrderIds): Tuple.Create(true, "订单不存在", failedOrderIds);
        }

        
        /// <summary>
        /// 获取订单数量（按条件）
        /// </summary>
        /// <param name="conditions"></param>
        /// <returns></returns>
        public int GetOrderCountByConditions(GetOrderCountConditionsModel conditions)
        {
            return _repository.GetOrderCountByConditions(conditions);
        }

        /// <summary>
        /// 按条件查询
        /// </summary>
        /// <param name="conditions"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public List<Order> GetOrdersByConditions(GetOrderCountConditionsModel conditions, string selectFields = "*")
        {
            return _repository.GetOrdersByConditions(conditions, selectFields);
        }
    }
}
