using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Services.LogisticService;
using Newtonsoft.Json.Linq;
using System.Threading;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Data;
using System.Diagnostics;
using DianGuanJiaApp.Services.PlatformService;
using System.Data.Common;
using System.Collections.Concurrent;
using vipapis.inventory;
using Jd.Api.Domain;
using System.Runtime.InteropServices;

namespace DianGuanJiaApp.Services
{

    public partial class OrderLogisticInfoService : BaseService<OrderLogisticInfo>
    {
        private OrderLogisticInfoRepository _repository;

        private CommonSettingService _commonSettingService = new CommonSettingService();
        private List<LogisticQueryEnableSettingModel> ptEnableList = new List<LogisticQueryEnableSettingModel>();

        private string settingKey = "OrderLogisticTracesSet";
        private string settingKey2 = "/LogisticQuery/LogisticWaring";

        public OrderLogisticInfoService()
        {
            ptEnableList = _commonSettingService.GetLogisticQueryEnableSetting();
            _repository = new OrderLogisticInfoRepository(ptEnableList);
        }

        public OrderLogisticInfoService(string connectionString) : base(connectionString)
        {
            ptEnableList = _commonSettingService.GetLogisticQueryEnableSetting();
            _repository = new OrderLogisticInfoRepository(connectionString, ptEnableList);
        }

        public List<OrderLogisticInfo> GetByIds(List<int> ids)
        {
            return _repository.GetByIds(ids);
        }

        public List<OrderLogisticInfo> GetByLogisticCodes(List<LogisticQuerySearchModel> keys, List<string> pts = null)
        {
            return _repository.GetByLogisticCodes(keys, pts);
        }

        public List<OrderLogisticInfo> GetByLogisticCodes(List<OrderLogisticSelectKeyModel> keys)
        {
            return _repository.GetByLogisticCodes(keys);
        }

        /// <summary>
        /// 添加到物流轨迹监控
        /// </summary>
        /// <param name="models"></param>
        public void AddLogisticTracesSubject(List<OrderLogisticInfoModel> models, bool needAdd = true)
        {
            if (models == null || models.Count == 0)
                return;

            //日志开始
            var logContext = LogForOperatorContext.CurrentOnlynInThisThread;
            var log = logContext.Begin(new LogForOperator()
            {
                OperatorType = "添加物流轨迹监控",
                ShopId = SiteContext.GetCurrentShopId(),
                Remark = models.ToJson()
            });

            var addSuccessCount = 0;

            //先查询是否存在，不重复加入监控
            var list = _repository.GetList(models.Select(f => new OrderLogisticInfo()
            {
                ShopId = f.ShopId, //shopId
                LogisticCode = f.LogisticCode,
                LogisticOrderId = f.LogisticOrderId
            }).ToList());

            //找出未添加
            var addList = new List<OrderLogisticInfo>();
            models.ForEach(item =>
            {
                if (list == null || list.Any(f => f.ShopId == item.ShopId && f.LogisticCode == item.LogisticCode && f.LogisticOrderId == item.LogisticOrderId) == false)
                {
                    //不存在,则添加
                    addList.Add(new OrderLogisticInfo()
                    {
                        CreateTime = DateTime.Now,
                        IsIgnorException = false,
                        LastTraces = string.Empty,
                        LogisticStatus = (int)PlatformLogisticStatus.PendingPackage, //待揽件
                        LogisticName = item.LogisticName,
                        LogisticCode = item.LogisticCode,
                        LogisticOrderId = item.LogisticOrderId,
                        ShopId = item.ShopId,
                        PrintTime = item.PrintTime,
                        SendTime = item.SendTime,
                        PlatformOrderId = item.PlatformOrderId,
                        PlatformType = item.PlatformType,
                        ToName = item.Receiver.Name,
                        ToMobile = item.Receiver.Mobile,
                        ToAddress = item.Receiver.Province.ToString2() + item.Receiver.City.ToString2() + item.Receiver.County.ToString2() + item.Receiver.Street.ToString2(),
                        SenderName = item.Sender.Name,
                        SenderMobile = item.Sender.Mobile,
                        SenderAddress = item.Sender.Province.ToString2() + item.Sender.City.ToString2() + item.Sender.County.ToString2() + item.Sender.Street.ToString2()
                    });
                }
            });

            //批量添加 
            if (needAdd)
                _repository.AddList(addList);

            //查询出id
            var tempList = _repository.GetList(addList);

            // 判断是否是拼多多电子面单
            var wKeys = models.Select(x => new OrderSelectKeyModel { PlatformOrderId = x.PlatformOrderId, ShopId = x.ShopId }).ToList();
            var waybillCodeList = new WaybillCodeService().GetWaybillCodeList(wKeys, new List<string> { "w.ID", "w.ExpressWayBillCode", "w.TemplateType", "c.OrderId" }, 500);

            //生成推送物流中心的数据
            var pushToLogisticCenterDatas = new List<LogisticCodePushRequest>();
            models.ForEach(item =>
            {
                var first = tempList.FirstOrDefault(f => f.ShopId == item.ShopId && f.LogisticCode == item.LogisticCode && f.LogisticOrderId == item.LogisticOrderId);
                if (first != null)
                {
                    var waybillCode = waybillCodeList?.FirstOrDefault(x => x.ExpressWayBillCode == item.LogisticOrderId);
                    addSuccessCount++;
                    //item.OwnerId = first.Id.ToString();
                    pushToLogisticCenterDatas.Add(new LogisticCodePushRequest()
                    {
                        LogisticCode = first.LogisticOrderId,
                        OwnerId = first.Id.ToString(),
                        PlatformType = first.PlatformType,
                        Receiver = item.Receiver,
                        Sender = item.Sender,
                        ShipperCode = first.LogisticCode,
                        State = item.ShopId.ToString(),
                        IsPddWaybill = waybillCode?.TemplateType == 21 || waybillCode?.TemplateType == 23 //21：拼多多二联，23：拼多多一联
                    });
                }
            });

            //记录总数量，成功数量
            log.TotalCount = models.Count;
            log.SuccessCount = addSuccessCount;

            PushToLogisticCenter(pushToLogisticCenterDatas);

            //日志结束
            logContext.End();
        }

        /// <summary>
        /// 推送物流中心
        /// </summary>
        /// <param name="requests"></param>
        /// <returns></returns>
        public bool PushToLogisticCenter(List<LogisticCodePushRequest> requests)
        {
            var isSuccess = false;
            var logContext = LogForOperatorContext.CurrentOnlynInThisThread;
            //日志开始
            var subLog_02 = logContext.StartStep(new LogForOperator()
            {
                OperatorType = "推送到物流中心"
            });

            var result = string.Empty;
            try
            {
                var reqsList = new List<List<LogisticCodePushRequest>>();
                var pageSize = 50;
                var count = Math.Ceiling(requests.Count * 1.0 / pageSize);
                for (var i = 0; i < count; i++)
                {
                    var tmpList = requests.Skip(i * pageSize).Take(pageSize).ToList();
                    reqsList.Add(tmpList);
                }
                var shopId = requests?.FirstOrDefault()?.State?.ToInt() ?? 0;
                var errReqs = new ConcurrentBag<LogisticTraceSubscribleLog>();
                var platformType = SiteContext.Current.CurrentLoginShop.PlatformType;
                var shipperCode = requests?.FirstOrDefault()?.ShipperCode;
                Parallel.ForEach(reqsList, new ParallelOptions { MaxDegreeOfParallelism = 5 }, reqs =>
                {
                    var isError = false;
                    var json = reqs.ToJson();
                    var errorMessage = "";
                    for (int i = 0; i < 2; i++)
                    {
                        try
                        {
                            result = (new LogisticCenterApiService()).Push(json);
                            var response = result.ToObject<CustomerApiResult<string>>();
                            isError = !response.IsSuccess;
                            errorMessage = response?.ErrorMessage;
                            break;
                        }
                        catch (Exception ex)
                        {
                            errorMessage = ex.ToString();
                            Log.WriteError($"物流轨迹订阅时发生错误，请求参数：{json},错误详情：{ex}");
                            Thread.Sleep(200);
                        }
                    }
                    if (isError)
                    {
                        errReqs.Add(new LogisticTraceSubscribleLog
                        {
                            Request = json,
                            Response = result,
                            CreateTime = DateTime.Now,
                            ErrorMessage = errorMessage,
                            PlatformType = platformType,
                            ShopId = shopId,
                            Status = "Error",
                            RetryTimes = 0,
                            ShipperCode = shipperCode
                        });
                    }

                    //if (isSuccess)
                    //{
                    //    //TODO: 这里应该将推送失败的回写到OrderLogisticInfo的某个字段上，让前端能知道 
                    //    // ……

                    //    subLog_02.Remark = "推送成功";
                    //    //推送成功后 ，获取最新的物流信息
                    //    var queryRequests = new List<LogisticCodeQueryRequest>();
                    //    try
                    //    {
                    //        queryRequests = reqs.Select(m => new LogisticCodeQueryRequest
                    //        {
                    //            ShipperCode = m.ShipperCode,
                    //            LogisticCode = m.LogisticCode
                    //        }).ToList();
                    //        QueryByLogisticCenter(queryRequests);
                    //        subLog_02.Remark += "，获取最新物流轨迹信息成功";
                    //    }
                    //    catch (Exception ex)
                    //    {
                    //        var response = QueryByLogisticCenter(queryRequests);
                    //        if (response.IsSuccess)
                    //        {
                    //            subLog_02.Remark += "，获取最新物流轨迹信息成功";
                    //        }
                    //        else 
                    //        {
                    //            subLog_02.Remark += "获取最新轨迹信息失败";
                    //            throw new LogicException($"物流轨迹订阅成功，但获取最新轨迹信息失败：{ex.Message}");
                    //        }
                    //    }
                    //}
                    //else
                    //{
                    //    subLog_02.Remark = "推送失败";
                    //}
                });

                if (errReqs.Any())
                {
                    isSuccess = false;
                    subLog_02.Exception = $"物流轨迹订阅错误数据：{errReqs.ToList().ToJson()}";
                    //记录错误日志
                    new ShopRepository().WriteLogisticTraceSubscribleLog(errReqs.ToList(), "Subscrible");
                }
                else
                    isSuccess = true;
            }
            catch (Exception ex)
            {
                subLog_02.Exception = ex.ToString();
                Log.WriteError($"推送失败：{ex.Message}");
                isSuccess = false;
                throw ex;
            }

            //日志结束
            logContext.EndStep(subLog_02);
            //TODO:这里应该还需要验证部分成功的问题
            return isSuccess;
        }

        /// <summary>
        /// 调物流中心的接口更新物流信息
        /// </summary>
        /// <param name="requests"></param>
        /// <returns></returns>
        public CustomerApiResult<List<LogisticCodeQueryResponse>> QueryByLogisticCenter(List<LogisticCodeQueryRequest> requests)
        {
            var requestJson = requests.ToJson();
            //接口查询物流轨迹信息
            var result = (new LogisticCenterApiService()).Query(requestJson);
            var err = result?.ToObject<dynamic>();
            if (err == null || err.IsSuccess == false)
            {
                return new CustomerApiResult<List<LogisticCodeQueryResponse>>()
                {
                    IsSuccess = false,
                    ErrorCode = err?.ErrorCode,
                    ErrorMessage = err?.ErrorMessage ?? "无订阅轨迹记录",
                };// (false, $"ERROR:{err.ErrorCode},ErrorMssage:{err.ErrorMessage}");
            }
            var response = result.ToObject<CustomerApiResult<List<LogisticCodeQueryResponse>>>();
            if (CustomerConfig.IsLogQueryLogisticResponse)
                Log.WriteError($"请求参数：{requestJson}，响应结果：{response.ToJson()}");
            var successDatas = response?.Data?.Where(f => f.IsSuccess).ToList(); //成功查询的数据才更新

            //#region 未与物流中心进行订阅，重新进行订阅获取最新物流轨迹
            //var unSubjectTraces = successDatas?.Where(m => m.OwnerId.IsNullOrEmpty()).ToList();
            //if (unSubjectTraces.Any())
            //{
            //    var keys = unSubjectTraces.Select(m => new LogisticQuerySearchModel { ExpressCode = m.ShipperCode, ExpressNo = m.LogisticCode }).ToList();
            //    var list = GetByLogisticCodes(keys);
            //    if (list.Any())
            //    {
            //        var expressIds = list.Select(m => m.LogisticOrderId).Distinct().ToList();
            //        Log.WriteWarning($"1. 检测到{expressIds.Count}条运单号【{string.Join(",", expressIds)}】未与物流中心进行订阅");
            //        var pushToLogisticCenterDatas = list.Select(m => new LogisticCodePushRequest
            //        {
            //            LogisticCode = m.LogisticOrderId,
            //            OwnerId = m.Id.ToString(),
            //            PlatformType = m.PlatformType,
            //            ShipperCode = m.LogisticCode,
            //            State = m.ShopId.ToString(),
            //        }).ToList();

            //        var pageSize = 50;
            //        var count = Math.Ceiling(pushToLogisticCenterDatas.Count * 1.0 / pageSize);
            //        for (var i = 0; i < count; i++)
            //        {
            //            var tempPushList = pushToLogisticCenterDatas.Skip(i * pageSize).Take(pageSize).ToList();
            //            var isPushed = PushToLogisticCenter(tempPushList);
            //            Thread.Sleep(200);
            //        }
            //        //  订阅成功，更新最新的物流轨迹信息                       
            //        Log.WriteWarning($"2. {expressIds.Count}条运单号与物流中心订阅成功,并获取到最新物流轨迹信息");
            //    }
            //}
            //#endregion

            //更新物流数据
            if (successDatas != null && successDatas.Any())
                UpdateLogisticTraces(successDatas);
            return response;
        }

        /// <summary>
        /// 更新物流信息
        /// </summary>
        /// <param name="tracesList"></param>
        public void UpdateLogisticTraces(List<LogisticCodeQueryResponse> tracesList)
        {
            tracesList = tracesList?.Where(m => !m.OwnerId.IsNullOrEmpty()).ToList();
            if (tracesList == null || tracesList.Count() == 0)
                return;

            // 物流中心为UTC时间=>北京时间
            tracesList.ForEach(res =>
            {
                res?.Trace?.Traces?.ForEach(t =>
                {
                    t.Time = t.Time.AddHours(8);
                });
            });

            var currentLoginShopPlatformType = tracesList?.FirstOrDefault()?.PlatformType ?? "";
            if (ptEnableList.Any(x => x.PlatformType == currentLoginShopPlatformType && x.RuleType == 1))
            {
                UpdatePddLogisticTraces(tracesList);
            }

            ////其他平台处理方式，根据平台分组，不同平台数据存储在不同的库
            //tracesList?.GroupBy(f => f.State).ToList().ForEach(item =>
            //{
            //    var dbconfig = DbPolicyExtension.GetConfig(item.Key.ToInt());
            //    var connStr = CustomerConfigExt.GetConnectString(dbconfig);
            //    _repository = new OrderLogisticInfoRepository(connStr);
            //    var datas = item.ToList().Select(d =>
            //    {
            //        var lastTraces = d?.Trace?.Traces?.OrderByDescending(t => t.Time)?.FirstOrDefault(); //最后的物流轨迹
            //        return new OrderLogisticInfo()
            //        {
            //            Id = int.Parse(d.OwnerId), //ownerid 存储的 orderLogistic表的id
            //            LogisticStatus = LogisticStatusConverter(d.Trace.Status, d?.Trace?.Traces),
            //            LastTraces = lastTraces?.Content
            //        };
            //    });

            //    //更新物流信息
            //    _repository.UpdateLogisticInfo(datas.ToList());

            //});
        }

        /// <summary>
        /// 更新物流信息
        /// </summary>
        /// <param name="tracesList"></param>
        public void UpdatePddLogisticTraces(List<LogisticCodeQueryResponse> tracesList)
        {
            if (tracesList == null || tracesList.Count() == 0)
                return;

            //var json = tracesList.ToJson();
            // 找出数据库中存在的物流轨迹（比较第一、二条轨迹是否需要重新获取）
            var ids = tracesList?.Select(m => m.OwnerId.ToInt()).Where(m => m > 0).Distinct().ToList();
            //var pts = tracesList?.GroupBy(m => m.PlatformType).Select(m => m.Key).Where(m => !m.IsNullOrEmpty()).Distinct().ToList();
            var keys = tracesList.Select(x => new LogisticQuerySearchModel { ShopId = x.State.ToInt(), ExpressNo = x.LogisticCode }).ToList();
            if (keys == null || !keys.Any())
                return;

            var orderLogisticInfos = _repository.GetByLogisticCodes(keys);
            //var orderLogisticInfos = _repository.GetByIds(ids, pts);
            if (!orderLogisticInfos.Any())
                return;

            //// 拼多多接口间隔1小时查询1次
            //var lastQueryTimeSet = _commonSettingService.Get("PddApiLogisticQueryTime", SiteContext.Current.CurrentShopId);
            //var lastQueryTime = Convert.ToDateTime(lastQueryTimeSet?.Value);

            var platformType = SiteContext.Current.CurrentLoginShop.PlatformType;
            //var expressCompanys = (new ExpressCompanyService()).GetExpressCompay();

            //// 物流轨迹表中订单无发货时间，则取订单的发货时间
            //var okeys = orderLogisticInfos.Where(m => m.SendTime == null).Select(m => new OrderSelectKeyModel { PlatformOrderId = m.PlatformOrderId, ShopId = m.ShopId }).ToList();
            //var orders = new List<Order>();
            //if (okeys != null && okeys.Any())
            //{
            //    OrderService _orderService = new OrderService();
            //    orders = _orderService.GetOrders(okeys, isNeedProduct: false, fields: new List<string> { "o.Id", "o.platformorderId", "o.shopId", "o.AllDeliveredTime", "oi.Id" });
            //}

            //转换物流轨迹在软件中的状态
            tracesList.ForEach(t =>
            {
                var traces = t.Trace?.Traces;
                if (traces != null && traces.Any())
                {
                    traces.ForEach(ts =>
                    {
                        ts.Status = LogisticStatusConverter(ts.Status, traces).ToString2();
                    });
                }
            });

            orderLogisticInfos.ForEach(item =>
            {
                /*
                 * 物流状态：
                 * 1: 待揽件
                 * 2: 已揽件，无物流
                 * 3: 有物流，未签收
                 * 4: 派件中
                 * 5: 已签收   
                 * 6: 问题件
                 */
                //item.SendTime = item.SendTime != null ? item.SendTime : orders.FirstOrDefault(m => m.PlatformOrderId == item.PlatformOrderId && m.ShopId == item.ShopId)?.AllDeliveredTime;
                //tracesList = json.ToList<LogisticCodeQueryResponse>(); //多个订单使用1个运单号打印
                var traceObj = tracesList.FirstOrDefault(m => m.LogisticCode == item.LogisticOrderId);
                var traces = traceObj?.Trace?.Traces?.OrderBy(m => m.Time)?.ToList() ?? new List<TraceStep>();
                //var lastStatus = LogisticStatusConverter(traceObj?.Trace?.Status, traces);
                var lastStatus = traces?.OrderByDescending(m => m.Time)?.FirstOrDefault()?.Status.ToInt() ?? 0;

                //traces.ForEach(x => x.Status = LogisticStatusConverter(x.Status, traces).ToString2());
                var firstTraces = traces.Where(m => m.Status == "2").OrderBy(m => m.Time).FirstOrDefault(); //已揽件，无物流
                TraceStep secondTraces = null;
                if (firstTraces != null)
                    secondTraces = traces.Where(m => m.Time > firstTraces.Time).OrderBy(m => m.Time).FirstOrDefault();  //第一条物流信息 
                //没有揽收，第二条就没有比较依据
                //else
                //{
                //    //物流中心物流轨迹没有揽件信息，有其他物流信息
                //    secondTraces = traces.Where(m => m.Status.ToInt() > 2).FirstOrDefault();
                //    firstTraces = secondTraces;
                //}
                //更新最后一条轨迹信息     
                var lastTraces = traces.OrderBy(m => m.Time).LastOrDefault();
                item.LastTraces = lastTraces?.Content;
                item.LogisticStatus = lastTraces == null ? (int)PlatformLogisticStatus.PendingPackage : lastStatus;
                item.UpdateTime = DateTime.Now;

                //第一条物流信息(已揽件，无物流)
                item.FirstLogisticDate = firstTraces != null ? firstTraces?.Time : item.FirstLogisticDate;
                item.FirstLogisticStatus = firstTraces != null ? firstTraces?.Status.ToInt() : item.FirstLogisticStatus;

                //第二条物流信息(第一条物流信息)
                item.SecondLogisticDate = secondTraces != null ? secondTraces?.Time : item.SecondLogisticDate;
                item.SecondLogisticStatus = secondTraces != null ? 3 : 0; // item.SecondLogisticStatus.ToInt() != 3 ? secondTraces?.Status.ToInt() : item.SecondLogisticStatus;
                
                //更新最后一条轨迹包裹状态
                if (item.SecondLogisticDate != null)
                    item.LogisticStatus = item.LogisticStatus > 3 ? item.LogisticStatus : 3;
                else if (item.SecondLogisticDate == null && item.FirstLogisticDate != null)
                    item.LogisticStatus = item.LogisticStatus > 2 ? item.LogisticStatus : 2;

                #region 注释代码
                //var pddApiTracesList = new List<PddApiLogisticTraceResponse>();
                //物流中心不包含揽件信息，重新调用拼多多接口查询
                //if (platformType == PlatformType.Pinduoduo.ToString() &&
                //(item.FirstLogisticDate == null || item.FirstLogisticStatus.ToInt() != 2
                //|| item.SecondLogisticDate == null || item.SecondLogisticStatus.ToInt() != 3))
                //{
                //    var expressCompany = expressCompanys.FirstOrDefault(m => m.CompanyCode == item.LogisticCode);
                //    var expressName = expressCompany?.Names.ToString2() ?? "";
                //    var pddLogisticModel = PinduoduoPlatformService.GetPddLogisticByExpressCompanyName(expressName);
                //    TracesSearch tracesSearch = new TracesSearch();
                //    var tracesJson = tracesSearch.PddGetTraces(pddLogisticModel.code, item.LogisticOrderId);
                //    Log.WriteLine($"快递编码【{pddLogisticModel.code}】运单号【{item.LogisticOrderId}】通过拼多多物流接口获取物流轨迹信息：{tracesJson.ToString2()}");
                //    if (!tracesJson.IsNullOrEmpty())
                //    {
                //        var tracesArray = JToken.Parse(tracesJson).SelectToken("logistics_ordertrace_get_resposne.trace_list") as JArray;
                //        if (tracesArray != null)
                //        {
                //            pddApiTracesList = tracesArray.Select(m => new PddApiLogisticTraceResponse
                //            {
                //                status_desc = m.Value<string>("status_desc"),
                //                action = m.Value<string>("action"),
                //                node_description = m.Value<string>("node_description"),
                //                status_time = m.Value<DateTime>("status_time"),
                //                time = m.Value<DateTime>("time"),
                //                desc = m.Value<string>("desc")
                //            }).ToList();
                //            var tracesItem = pddApiTracesList?.Where(m => m.action == "GOT").OrderByDescending(m => m.time).FirstOrDefault();
                //            item.FirstLogisticDate = tracesItem?.time;
                //            item.FirstLogisticStatus = tracesItem == null ? 0 : 2;
                //        }
                //    }

                //    if (pddApiTracesList != null && pddApiTracesList.Any())
                //    {
                //        //重新调用拼多多接口时，取最新API返回结果
                //        //第一条物流信息
                //        var tracesItem = pddApiTracesList.Where(m => m.action != "GOT").OrderBy(m => m.time).FirstOrDefault();
                //        if (tracesItem != null)
                //        {
                //            item.SecondLogisticDate = tracesItem?.time;
                //            item.SecondLogisticStatus = ConvertPddActionToStatus(tracesItem.action.ToString2());
                //        }
                //        //最后一条物流信息
                //        var lastTracesItem = pddApiTracesList.OrderByDescending(m => m.time).FirstOrDefault();
                //        if (lastTracesItem != null)
                //        {
                //            item.LastTraces = lastTracesItem.desc.ToString2();
                //            item.LogisticStatus = ConvertPddActionToStatus(lastTracesItem.action.ToString2());
                //            item.UpdateTime = lastTracesItem.time;
                //        }
                //    }
                //}
                //if (item.SendTime == null)
                //    return; 
                #endregion

                var now = DateTime.Now;
                //异常件判定规则：1、非手动标记正常件，2、发货后24小时内无揽件信息或揽件时间超过24小时，3、揽件后24小时内无第一条物流信息，4、无发货时间表示未发货不能判定异常件，5、未发货，但已揽件超过24小时无第一条物流信息
                item.IsException =
                            (item.SendTime == null && item.FirstLogisticDate != null && item.SecondLogisticDate == null && (now - item.FirstLogisticDate.Value).TotalHours > 24) || //3
                            (item.SendTime == null && item.FirstLogisticDate != null && item.SecondLogisticDate != null && (item.SecondLogisticDate.Value - item.FirstLogisticDate.Value).TotalHours > 24) ||
                            (item.SendTime != null && (
                                 (item.FirstLogisticDate == null && (now - item.SendTime.Value).TotalHours > 24) //2 发货后，没有揽件信息
                                 || (item.FirstLogisticDate != null && (item.FirstLogisticDate.Value - item.SendTime.Value).TotalHours > 24)//2 发货后，揽件时间超过24小时
                                 || (item.FirstLogisticDate != null && item.SecondLogisticDate == null && (now - item.FirstLogisticDate.Value).TotalHours > 24)// 发货后，揽件后，24小时内无第一条物流信息
                                 || (item.FirstLogisticDate != null && item.SecondLogisticDate != null && (item.SecondLogisticDate.Value - item.FirstLogisticDate.Value).TotalHours > 24) //发货后，揽件后，和揽件时间相差大于24小时
                             ));

                //正常件判定规则：1、无发货时间表示未发货不能判定正常件，2、发货后24小时内有揽件信息，3、揽件后24小时内有物流信息，4、手动标记正常件
                if (item.SendTime != null)
                    item.IsNormal = item.FirstLogisticDate != null && item.SecondLogisticDate != null
                             && (item.FirstLogisticDate.Value - item.SendTime.Value).TotalHours <= 24 && (item.SecondLogisticDate.Value - item.FirstLogisticDate.Value).TotalHours <= 24;
            });

            //更新物流信息
            BulkMergerUpdate(orderLogisticInfos);
            //Log.Debug($"更新物流轨迹信息：==>\n {orderLogisticInfos.ToJson()}");
        }

        public void UpdateSendTime(List<OrderLogisticInfoModel> datas)
        {
            _repository.UpdateSendTime(datas);
        }

        public bool BulkMergerUpdate(List<OrderLogisticInfo> orderLogisticInfos)
        {
            try
            {
                _repository.BulkMergerUpdate(orderLogisticInfos);
                return true;
            }
            catch (Exception ex)
            {
                Log.WriteError($"店铺【{SiteContext.Current.CurrentShopId}】更新物流轨迹失败：{ex.Message}");
            }
            return false;
        }

        /// <summary>
        /// 拼多多物流API接口返回状态转换
        /// </summary>
        /// <param name="action"></param>
        /// <returns></returns>
        public int ConvertPddActionToStatus(string action)
        {
            if (action.IsNullOrEmpty())
                return 0;

            var status = 0;
            PddApiActionStatus actionStatus;
            var isParsed = Enum.TryParse(action, out actionStatus);
            if (!isParsed)
                return 0;

            switch (actionStatus)
            {
                case PddApiActionStatus.GOT:
                    status = 2;
                    break;
                case PddApiActionStatus.DEPARTURE:
                case PddApiActionStatus.ARRIVAL:
                case PddApiActionStatus.OTHER:
                    status = 3;
                    break;
                case PddApiActionStatus.SEND:
                case PddApiActionStatus.IN_CABINET:
                    status = 4;
                    break;
                case PddApiActionStatus.OUT_CABINET:
                case PddApiActionStatus.SIGN:
                    status = 5;
                    break;
                default:
                    status = (int)PlatformLogisticStatus.PendingPackage;
                    break;
            }
            return status;
        }

        /// <summary>
        /// 批量忽略异常
        /// </summary>
        /// <param name="models"></param>
        public void IgnoreException(List<OrderLogisticInfo> models)
        {
            if (models == null || models.Count == 0)
                return;

            //忽略异常
            _repository.IgnoreException(models);
        }

        public string GetExecSql(LogisticQueryListRequestModel queryModel)
        {
            if (queryModel.ShopId == null || !queryModel.ShopId.Any())
            {
                queryModel.ShopId = SiteContext.Current.SamePlatformShopIds;
            }
            var userSettings = _commonSettingService.Get(settingKey, SiteContext.Current.CurrentShopId)?.Value.ToObject<WarningSetModel>();
            //var currPlatformType = SiteContext.Current.CurrentLoginShop.PlatformType;
            //var warningVal = userSettings?.WarningStatusSet ?? 6; //预警设置，未设置，默认为6。
            //var exceptionVal = userSettings?.ExceptionStatusSet ?? 24; //异常设置，未设置，默认为24。

            var deliveredSetting = _commonSettingService.Get("/LogisticQuery/LogisticWaring", SiteContext.Current.CurrentShopId)?.Value.ToObject<DeliveredSetModel>();
            deliveredSetting = deliveredSetting ?? new DeliveredSetModel { DeliveredWarningHour = 12, PickedWarningHour = 12 };
            //var deliveredWaringHour = deliveredSetting?.DeliveredWarningHour ?? 12; //发货后，超过X小时未更新揽件信息，预警提醒，默认为12。
            //var pickedWaringHour = deliveredSetting?.PickedWarningHour ?? 12; //揽件后，超过X小时未更新物流信息，预警提醒，默认为12。

            var execSql = _repository.GetExecSql(queryModel, deliveredSetting, userSettings);
            return execSql;
        }

        public LogisticQueryPageResultModel LoadList(LogisticQueryListRequestModel queryModel)
        {
            if (queryModel.ShopId == null || !queryModel.ShopId.Any())
            {
                queryModel.ShopId = SiteContext.Current.SamePlatformShopIds;
            }
            //拼多多加密处理
            var isPdd = SiteContext.Current.CurrentLoginShop.PlatformType == PlatformType.Pinduoduo.ToString();
            if (string.IsNullOrEmpty(queryModel.ToName) == false && isPdd)
            {
                queryModel.ToNameIndex = PinduoduoPlatformService.GetSearchEncryptString(queryModel.ToName, false);
            }

            var currPlatformType = SiteContext.Current.CurrentLoginShop.PlatformType;
            var userSettings = _commonSettingService.Get(settingKey, SiteContext.Current.CurrentShopId)?.Value.ToObject<WarningSetModel>();
            var warningVal = userSettings?.WarningStatusSet ?? 6; //预警设置，未设置，默认为6。
            var exceptionVal = userSettings?.ExceptionStatusSet ?? 24; //异常设置，未设置，默认为24。

            var deliveredSetting = _commonSettingService.Get("/LogisticQuery/LogisticWaring", SiteContext.Current.CurrentShopId)?.Value.ToObject<DeliveredSetModel>();
            deliveredSetting = deliveredSetting ?? new DeliveredSetModel { DeliveredWarningHour = 12, PickedWarningHour = 12 };
            var deliveredWaringHour = deliveredSetting?.DeliveredWarningHour ?? 12; //发货后，超过X小时未更新揽件信息，预警提醒，默认为12。
            var pickedWaringHour = deliveredSetting?.PickedWarningHour ?? 12; //揽件后，超过X小时未更新物流信息，预警提醒，默认为12。

            //重新查询
            var data = _repository.LoadList(queryModel, deliveredSetting, userSettings);

            #region 注释
            ////检查返回结果中异常件，再次调用API接口确认是否有物流信息
            //if (ptEnableList.Any(x => x.PlatformType == currPlatformType && x.IsDefaultEnable))
            //{
            //    //获取异常需重新同步的订单

            //    // 更新当前页异常件物流轨迹信息
            //    //var pids = data?.Item2?.Select(x => x.PlatformOrderId).ToList();
            //    //queryModel.PlatformOrderId = pids == null || !pids.Any() ? "" : string.Join(",", pids);

            //    //更新3天内，距离上次更新时间300s后的异常件
            //    var exceptionRows = _repository.LoadReSyncList(queryModel, deliveredSetting, userSettings);
            //    if (exceptionRows.Any())
            //    {
            //        var totalSeconds = 0.0;
            //        Stopwatch watch = new Stopwatch();
            //        watch.Start();
            //        Log.WriteError($"店铺【{SiteContext.Current.CurrentShopId}】【{exceptionRows.Count}】条异常件待更新最新轨迹信息");
            //        try
            //        {
            //            var queryRequests = exceptionRows.Select(m => new LogisticCodeQueryRequest
            //            {
            //                ShipperCode = m.LogisticCode,
            //                LogisticCode = m.LogisticOrderId
            //            }).ToList();

            //            var pageSize = 50;
            //            var count = Math.Ceiling(queryRequests.Count * 1.0 / pageSize);
            //            var reqList = new List<List<LogisticCodeQueryRequest>>();
            //            for (var i = 0; i < count; i++)
            //            {
            //                var tmpList = queryRequests.Skip(i * pageSize).Take(pageSize).ToList();
            //                reqList.Add(tmpList);
            //            }
            //            Parallel.ForEach(reqList, new ParallelOptions { MaxDegreeOfParallelism = 5 }, reqs =>
            //            {
            //                try
            //                {
            //                    QueryByLogisticCenter(reqs);
            //                }
            //                catch (Exception ex)
            //                {
            //                    throw ex;
            //                }
            //            });
            //        }
            //        catch (Exception ex)
            //        {
            //            watch.Stop();
            //            totalSeconds = watch.Elapsed.TotalSeconds;
            //            Log.WriteError($"店铺【{SiteContext.Current.CurrentShopId}】获取【{exceptionRows.Count}】条异常件最新轨迹信息耗时：{totalSeconds}s，异常信息：{ex}");
            //            //throw new LogicException("更新异常件失败");
            //        }
            //        watch.Stop();
            //        totalSeconds = watch.Elapsed.TotalSeconds;
            //        Log.WriteError($"店铺【{SiteContext.Current.CurrentShopId}】获取【{exceptionRows.Count}】条异常件最新轨迹信息耗时：{totalSeconds}s");
            //    }
            //} 
            #endregion


            var rows = new List<OrderLogisticInfoModel>();
            //设置物流状态
            rows = SetExceptionStatus(data.Item2, deliveredWaringHour, pickedWaringHour, warningVal, exceptionVal);
            //if (queryModel.ExceptionStatus > 0)
            //{
            //    data.Item2.ForEach(item =>
            //    {
            //        if (queryModel.ExceptionStatus == 1)
            //            item.ExceptionStatus = "预警件";
            //        else if (queryModel.ExceptionStatus == 2)
            //            item.ExceptionStatus = "异常件";
            //        else if (queryModel.ExceptionStatus == 3)
            //            item.ExceptionStatus = "正常件";
            //        else
            //            item.ExceptionStatus = "--";
            //        rows.Add(item);
            //    });
            //}
            //else
            //{
            //    //前端显示异常状态
            //    rows = SetExceptionStatus(data.Item2, deliveredWaringHour, pickedWaringHour, warningVal, exceptionVal);
            //}

            ////更新发货时间
            //var orders = new List<Order>();
            //var needUpdateModels = rows.Where(x => x.SendTime == null).ToList();
            //var keys = needUpdateModels.Select(x => new OrderSelectKeyModel { ShopId = x.ShopId, PlatformOrderId = x.PlatformOrderId }).ToList();
            //if (keys != null && keys.Any() && queryModel.IsDontLoadSendTime == false)
            //{
            //    OrderService _orderService = new OrderService();
            //    orders = _orderService.GetOrders(keys, isNeedProduct: false, fields: new List<string> { "o.Id", "o.platformorderId", "o.shopId", "o.AllDeliveredTime","0 AS Id" });
            //    var needUpdateRows = new List<OrderLogisticInfo>();
            //    needUpdateModels.ForEach(m =>
            //    {
            //        var order = orders?.FirstOrDefault(x => x.PlatformOrderId == m.PlatformOrderId && x.ShopId == m.ShopId);
            //        if (order != null && order.AllDeliveredTime != null)
            //        {
            //            m.SendTime = order.AllDeliveredTime;
            //            needUpdateRows.Add(m);
            //        }
            //    });
            //    if (needUpdateRows.Any())
            //        BulkMergerUpdate(needUpdateRows);
            //}

            //设置包裹状态
            rows.ForEach(row =>
            {
                if (row.LogisticStatus == 0 || row.LogisticStatus == 1)
                {
                    if (row.SendTime != null && row.SendTime < DateTime.Now.AddDays(-3))
                        row.LogisticStatusStr = "待揽件"; //发货后3内未有物流更新显示包裹状态
                    else if (row.CreateTime < DateTime.Now.AddDays(7))
                        row.LogisticStatusStr = "待揽件"; //创建监听后7内未有物流更新显示包裹状态
                    else
                        row.LogisticStatusStr = "待揽件";
                }
                else if (row.LogisticStatus == 2)
                    row.LogisticStatusStr = "已揽件，无物流";
                else if (row.LogisticStatus == 3)
                    row.LogisticStatusStr = "已揽件，有物流";
                else if (row.LogisticStatus == 4)
                    row.LogisticStatusStr = "派件中";
                else if (row.LogisticStatus == 5)
                    row.LogisticStatusStr = "已签收";
                else if (row.LogisticStatus == 6)
                    row.LogisticStatusStr = "问题件";
                else
                    row.LogisticStatusStr = "未知状态";
            });
            if (isPdd && queryModel.IsDontDecryptWhenIsPdd == false)
            {
                //只解密手机号
                var tempOrders = rows?.Select(x => new Order { PlatformOrderId = x.PlatformOrderId, ShopId = x.ShopId, ToName = x.ToName }).ToList();
                BranchShareRelationService.TryToDecryptPddOrders(tempOrders, true, "ToName");
                //按店铺分组
                rows?.ForEach(item =>
                {
                    var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.PlatformOrderId && x.ShopId == item.ShopId);
                    if (decryptedOrder != null)
                    {
                        item.ToName = decryptedOrder.ToName;
                    }
                    item.ToName = item.ToName.ToEncryptName();
                });
            }
            return new LogisticQueryPageResultModel()
            {
                Total = data.Item1,
                IsOrderDesc = queryModel.IsOrderDesc,
                OrderByField = queryModel.OrderByField,
                PageIndex = queryModel.PageIndex,
                PageSize = queryModel.PageSize,
                RealOrderTotal = data.Item1,
                Rows = rows,
                UnPickCount = data.Item3,
                UnDeliverCount = data.Item4
            };
        }

        public List<OrderLogisticInfoModel> SetExceptionStatus(List<OrderLogisticInfoModel> list, int deliveredWaringHour, int pickedWaringHour, int warningVal, int exceptionVal)
        {
            var curShop = SiteContext.Current.CurrentLoginShop;
            var rows = new List<OrderLogisticInfoModel>();
            if (list?.Count > 0)
            {
                list?.ForEach(obj =>
                {
                    var now = DateTime.Now;
                    var item = obj;

                    var currPlatformType = SiteContext.Current.CurrentLoginShop.PlatformType;
                    if (ptEnableList.Any(x => x.PlatformType == currPlatformType))
                    {
                        var excepTime = DateTime.Now.AddHours(-24);
                        var deliveredWarningTime = now.AddHours(-deliveredWaringHour);
                        var pickedWarningTime = now.AddHours(-pickedWaringHour);
                        /*
                        ********* 正常件判定规则：***********
                        * 1、正常件，2、手工标记正常件，3、非异常件
                        * 4、无揽件时：当前时间-发货时间 < 24h
                        * 5、有揽件无第一条物流信息：揽件时间-发货时间  < 24h
                        * 6、有揽件并有第一条物流信息： 揽件时间-发货时间 < 24h && 第一条物流时间-揽件时间 < 24h
                         
                        *********  异常件判定规则：********* 
                        * 1、非正常件，2、非手工标记正常件，
                        * 3、已被标记为异常件
                        * 4、无揽件时：当前时间-发货时间 > 24h 
                        * 5、有揽件无第一条物流信息：揽件时间-发货时间 > 24h
                        * 6、有揽件并有第一条物流信息： 揽件时间-发货时间 > 24h || 第一条物流时间-揽件时间 > 24h
                        * 7、未发货，有揽件无第一条物流信息：当前时间-揽件时间 > 24h 
                         
                        *********  预警件判定规则：********* 
                        * 1、非正常件，2、非异常件，3、非手工标记正常件，
                        * 4、无揽件时：当前时间-发货时间 < 24h && 当前时间-发货时间 > 揽件预警时间
                        * 5、有揽件并无第一条物流信息：揽件时间-发货时间 < 24h && 揽件时间-发货时间 > 揽件预警时间 && 当前时间-揽件时间 > 揽件预警时间 &&  当前时间-揽件时间 < 24h
                       */
                        //var diffNowHour = (now - item.SendTime.Value).TotalHours;
                        var exceptionStatus0 = (item.SendTime == null && item.FirstLogisticDate != null && item.SecondLogisticDate == null && (now - item.FirstLogisticDate.Value).TotalHours > 24);
                        var exceptionStatus1 = (item.SendTime == null && item.FirstLogisticDate != null && item.SecondLogisticDate != null && (item.SecondLogisticDate.Value - item.FirstLogisticDate.Value).TotalHours > 24);
                        var exceptionStatus2 = item.SendTime != null && (item.FirstLogisticDate == null && (now - item.SendTime.Value).TotalHours > 24);
                        var exceptionStatus3 = item.SendTime != null && (item.FirstLogisticDate != null && (item.FirstLogisticDate.Value - item.SendTime.Value).TotalHours > 24);
                        var exceptionStatus4 = item.SendTime != null && (item.FirstLogisticDate != null && item.SecondLogisticDate == null && (now - item.FirstLogisticDate.Value).TotalHours > 24);
                        var exceptionStatus5 = item.SendTime != null && (item.FirstLogisticDate != null && item.SecondLogisticDate != null && (item.SecondLogisticDate.Value - item.FirstLogisticDate.Value).TotalHours > 24);
                        //预警状态
                        var predictionStatu0 = (item.SendTime == null && item.FirstLogisticDate != null && item.SecondLogisticDate == null && (now - item.FirstLogisticDate.Value).TotalHours > pickedWaringHour && (now - item.FirstLogisticDate.Value).TotalHours < 24);
                        var predictionStatu1 = item.SendTime != null && (item.FirstLogisticDate == null && item.SecondLogisticDate == null && (now - item.SendTime.Value).TotalHours >= deliveredWaringHour && (now - item.SendTime.Value).TotalHours < 24);
                        var predictionStatu2 = item.SendTime != null && (item.FirstLogisticDate != null && item.SecondLogisticDate == null &&
                                     ((now - item.FirstLogisticDate.Value).TotalHours > pickedWaringHour && (now - item.FirstLogisticDate.Value).TotalHours < 24));

                        if (item.IsNormal)
                        {
                            item.ExceptionStatus = "正常件";
                        }
                        else if (exceptionStatus1 || exceptionStatus2 || exceptionStatus3 || exceptionStatus4 || exceptionStatus5)
                        {
                            if (item.IsIgnorException)
                                item.ExceptionStatus = "正常件(<lable style='color:red;'>已忽略异常</lable>)";
                            else
                                item.ExceptionStatus = "异常件";

                            if (exceptionStatus0)
                                item.ExceptionReason = "已揽件，超过24小时无第二条物流";
                            if (exceptionStatus1)
                                item.ExceptionReason = "已揽件，第二条物流超过24小时";
                            else if (exceptionStatus2)
                                item.ExceptionReason = "已发货，超过24小时无揽件";
                            else if (exceptionStatus3)
                                item.ExceptionReason = "已发货，揽件超过24小时";
                            else if (exceptionStatus4)
                                item.ExceptionReason = "已发货，已揽件，超过24小时无第二条";
                            else if (exceptionStatus5)
                                item.ExceptionReason = "已发货，已揽件，第二条超过24小时";
                        }
                        else
                        {
                            // 前两条物流记录不完整时，判断是否预警件       
                            if (predictionStatu0 || predictionStatu1 || predictionStatu2)
                            {
                                if (item.IsIgnorException)
                                    item.ExceptionStatus = "正常件(<lable style='color:red;'>已忽略预警</lable>)";
                                else
                                    item.ExceptionStatus = "预警件";

                                if (predictionStatu0)
                                    item.ExceptionReason = $"已揽件，超【{pickedWaringHour}】小时无第二条物流";
                                if (predictionStatu1)
                                    item.ExceptionReason = $"已发货，超过【{deliveredWaringHour}】小时无揽件";
                                else if (predictionStatu2)
                                    item.ExceptionReason = $"已发货，已揽件，超过【{pickedWaringHour}】小时无第二条物流";
                            }
                            else
                            {
                                item.ExceptionStatus = "正常件";
                            }
                        }

                        //已发货3天内没有揽件信息显示异常状态
                        if (item.LogisticStatus < 2 && item.SendTime != null && (now - item.SendTime.Value).TotalDays > 3)
                        {
                            item.ExceptionStatus = "异常件";
                            item.ExceptionReason = "超过三天无物流";
                        }
                    }
                    else
                    {
                        var warningTime = DateTime.Now.AddHours(-warningVal);//预警件时间
                        var exceptionTime = DateTime.Now.AddHours(-exceptionVal); //异常件时间
                        if (item.UpdateTime == null && item.CreateTime < warningTime)
                        {
                            item.ExceptionStatus = "预警件";
                            if (item.IsIgnorException)
                            {
                                item.ExceptionStatus = "正常件(<lable style='color:red;'>已忽略预警</lable>)";
                            }
                            else if (item.LogisticStatus == (int)PlatformLogisticStatus.Delivered)
                            {
                                item.ExceptionStatus = "正常件(<lable style='color:red;'>已签收忽略预警</lable>)";
                            }
                        }
                        else if (item.UpdateTime < warningTime)
                        {
                            item.ExceptionStatus = "预警件";
                            if (item.IsIgnorException)
                            {
                                item.ExceptionStatus = "正常件(<lable style='color:red;'>已忽略预警</lable>)";
                            }
                            else if (item.LogisticStatus == (int)PlatformLogisticStatus.Delivered)
                            {
                                item.ExceptionStatus = "正常件(<lable style='color:red;'>已签收忽略预警</lable>)";
                            }
                        }
                        else
                        {
                            item.ExceptionStatus = "正常件";
                        }

                        if (item.UpdateTime == null && item.CreateTime < exceptionTime)
                        {
                            item.ExceptionStatus = "异常件";
                            if (item.IsIgnorException)
                            {
                                item.ExceptionStatus = "正常件(<labe style='color:red;'>已忽略异常</lable>)";
                            }
                            else if (item.LogisticStatus == (int)PlatformLogisticStatus.Delivered)
                            {
                                item.ExceptionStatus = "正常件(<labe style='color:red;'>已签收忽略异常</lable>)";
                            }
                        }
                        else if (item.UpdateTime < exceptionTime)
                        {
                            item.ExceptionStatus = "异常件";
                            if (item.IsIgnorException)
                            {
                                item.ExceptionStatus = "正常件(<labe style='color:red;'>已忽略异常</lable>)";
                            }
                            else if (item.LogisticStatus == (int)PlatformLogisticStatus.Delivered)
                            {
                                item.ExceptionStatus = "正常件(<labe style='color:red;'>已签收忽略异常</lable>)";
                            }
                        }
                    }
                    rows.Add(item);
                });
            }
            return rows;
        }

        /// <summary>
        /// 物流中心的物流状态转换为平台物流状态
        /// </summary>
        /// <param name="state"></param>
        /// <returns></returns>
        private int LogisticStatusConverter(string state, List<TraceStep> traces)
        {
            //快递鸟 物流状态：
            //物流状态 ： 0-无轨迹 1-已揽收 2-在途中 3-签收 4-问题件
            //增值物流状态： 1 - 已揽收 2 - 在途中 201 - 到达派件城市 202 - 派件中 211 - 已放入快递柜或驿 站 3 - 已签收 301 - 正常签收 302 - 派件异常后最终签 收 304 - 代收签收 311 - 快递柜或驿站签收 4 - 问题件 401 - 发货无信息 402 - 超时未签收 403 - 超时未更新 404 - 拒收(退件) 405 - 派件异常 406 - 退货签收 407 - 退货未签收 412 - 快递柜或驿站超时 未取

            //咱们系统物流状态：
            //<option value = "1" > 待揽件 </ option >
            //< option value = "2" > 已揽件，无物流 </ option >
            //< option value = "3" > 有物流，未签收 </ option >
            //< option value = "4" > 派件中 </ option >
            //< option value = "5" > 已签收 </ option >  
            //< option value = "6" > 问题件 </ option >

            if (state.IsNullOrEmpty() || traces == null || !traces.Any())
                return 1;

            int status = 1;
            switch (state)
            {
                case null: //
                case "0"://无轨迹
                    status = 1; //待揽件
                    break;
                case "1": //已揽收
                    status = 2; //待揽件
                    break;
                case "202": //派件中
                case "211":
                    status = 4;
                    break;
                case "2": //在途中
                    status = 3;
                    //var lastTrace = traces.OrderByDescending(f => f.Time).FirstOrDefault();
                    //var result = int.TryParse(lastTrace.Status, out tempState);
                    //if (result && tempState >= 202)
                    //{
                    //    status = 4; //派件中
                    //}
                    //else
                    //{
                    //    status = 3; //有物流，未签收
                    //}
                    break;
                case "3": //签收
                case "201":
                    status = 5; //已签收
                    break;
                case "4":
                case "401":
                case "404": //问题件
                    status = 6;
                    break;

            }
            return status;
        }

        /// <summary>
        /// 订单打印页面统计预发货数量和物流预警数量
        /// </summary>
        /// <returns></returns>
        public int GetWarningCount()
        {
            var curShop = SiteContext.Current.CurrentLoginShop;
            var sets = _commonSettingService.GetSets(new List<string> { settingKey, "/Logistic/SwitchStatusUpdateTime", "/LogisticQuery/LogisticWaring" }, curShop.Id);
            var userSettings = sets.FirstOrDefault(x => x.Key == settingKey)?.Value.ToObject<WarningSetModel>();
            //物流轨迹启用状态
            if (userSettings == null || userSettings.SubjectSet == 0)
                return 0;

            //最后修改物流轨迹状态时间                               
            var set = sets.FirstOrDefault(x => x.Key == "/Logistic/SwitchStatusUpdateTime");
            var switchStatusUpdateTime = set?.Value.ToDateTime();
            var switchStatusUpdateTimeStr = switchStatusUpdateTime == null ? "" : switchStatusUpdateTime.Value.Format();

            set = sets.FirstOrDefault(x => x.Key == "/LogisticQuery/LogisticWaring");
            var deliveredSetting = set?.Value.ToObject<DeliveredSetModel>();
            deliveredSetting = deliveredSetting ?? new DeliveredSetModel { DeliveredWarningHour = 12, PickedWarningHour = 12 };
            deliveredSetting.DeliveredWarningHour = deliveredSetting?.DeliveredWarningHour ?? 12; //发货后，超过X小时未更新揽件信息，预警提醒，默认为12。
            deliveredSetting.PickedWarningHour = deliveredSetting?.PickedWarningHour ?? 12; //揽件后，超过X小时未更新物流信息，预警提醒，默认为12。

            var shopIds = SiteContext.Current.SamePlatformShopIds;
            return _repository.GetWarningCount(shopIds, deliveredSetting, switchStatusUpdateTimeStr);
        }

        public Tuple<int, int> GetStatisticWarningCount(LogisticQueryListRequestModel queryModel)
        {
            var curShop = SiteContext.Current.CurrentLoginShop;
            var sets = _commonSettingService.GetSets(new List<string> { settingKey, "/Logistic/SwitchStatusUpdateTime", "/LogisticQuery/LogisticWaring" }, curShop.Id);

            //var userSettings = _commonSettingService.Get(settingKey, SiteContext.Current.CurrentShopId)?.Value.ToObject<WarningSetModel>();
            var userSettings = sets.FirstOrDefault(x => x.Key == settingKey)?.Value.ToObject<WarningSetModel>();
            //物流轨迹启用状态
            if (userSettings == null || userSettings.SubjectSet == 0)
                return new Tuple<int, int>(0, 0);

            //最后修改物流轨迹状态时间    
            var set = sets.FirstOrDefault(x => x.Key == "/Logistic/SwitchStatusUpdateTime");
            var switchStatusUpdateTime = set?.Value.ToDateTime();
            var switchStatusUpdateTimeStr = switchStatusUpdateTime == null ? "" : switchStatusUpdateTime.Value.Format();

            var deliveredSetting = sets.FirstOrDefault(x => x.Key == "/LogisticQuery/LogisticWaring")?.Value.ToObject<DeliveredSetModel>();
            deliveredSetting = deliveredSetting ?? new DeliveredSetModel { DeliveredWarningHour = 12, PickedWarningHour = 12 };
            deliveredSetting.DeliveredWarningHour = deliveredSetting?.DeliveredWarningHour ?? 12; //发货后，超过X小时未更新揽件信息，预警提醒，默认为12。
            deliveredSetting.PickedWarningHour = deliveredSetting?.PickedWarningHour ?? 12; //揽件后，超过X小时未更新物流信息，预警提醒，默认为12。

            var shopIds = SiteContext.Current.SamePlatformShopIds;
            queryModel.ShopId = queryModel.ShopId == null || !queryModel.ShopId.Any() ? shopIds : queryModel.ShopId;
            return _repository.GetStatisticWarningCount(queryModel, deliveredSetting, userSettings, switchStatusUpdateTimeStr);
        }

        public List<Order> GetLogisticCodePushRequests(string whereSql)
        {
            var sql = $@"
                    SELECT ol.Id,o.PlatformOrderId,o.ShopId,o.ToName,o.ToMobile,o.ToProvince,o.ToCity,o.ToCounty,o.ToAddress,ol.LogisticOrderId AS LastWaybillCode,ol.LogisticCode AS PlatformStatus FROM dbo.P_OrderLogisticInfo ol
                    INNER JOIN dbo.P_Order o ON ol.ShopId=o.ShopId AND ol.PlatformOrderId=o.PlatformOrderId
                    {whereSql}";
            var orders = _repository.DbConnection.Query<Order>(sql).ToList();
            return orders;
        }
    }
}
