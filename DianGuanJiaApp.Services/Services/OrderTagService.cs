using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Services.Services.SyncDataInterface;
using Dapper;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Services
{
    public partial class OrderTagService : BaseService<OrderTags>
    {
        public OrderTagService()
        {
            _repository = new OrderTagsRepository();
        }

        public OrderTagService(string connectionString) : base(connectionString)
        {
            _repository = new OrderTagsRepository(connectionString);
        }
        #region 私有变量

        private OrderTagsRepository _repository = null;

        #endregion

        public int Insert(OrderTags tag)
        {
            var dbModel = GetModel(tag.OiCode, tag.Tag);
            if (dbModel == null)
                return _repository.Insert(tag);
            else if (dbModel.Status == 1)
            {
                BulkUpdate(new List<string>() { dbModel.OiCode }, dbModel.Tag, 0);
            }
            return 1;
        }

        public OrderTags GetModel(string oiCode, string tagName)
        {
            return _repository.GetModel(oiCode, tagName);
        }

        public List<OrderTags> GetList(List<string> oiCodes, int? status)
        {
            return _repository.GetList(oiCodes, status);
        }
        public List<OrderTags> GetList(List<string> oiCodes, List<string> tagNames, int? status)
        {
            return _repository.GetList(oiCodes, tagNames, status);
        }

        public void BulkUpdate(List<string> oiCodes, string tag, int status)
        {
            _repository.BulkUpdate(oiCodes, tag, status);
        }

        /// <summary>
        /// 逻辑删除数据
        /// </summary>
        /// <param name="tags"></param>
        /// <param name="tagName"></param>
        /// <param name="tagValue"></param>
        public void SetDeleted(List<OrderTags> tags, string tagName, string tagValue = null)
        {
            var oiCodes = tags.Select(a => a.OiCode).ToList();
            _repository.SetDeleted(oiCodes, tagName, tagValue);

            #region 调用同步数据接口服务
            var fxUserId = SiteContext.Current?.CurrentFxUser?.Id ?? 0;
            var pathFlowCodes = tags.Select(a => a.PathFlowCode).Distinct().ToList();
            new SyncDataInterfaceService(fxUserId).PathFlowOpt(pathFlowCodes, (string targetConnectionString, List<string> targetPathFlowCodes) =>
            {
                if (targetPathFlowCodes != null && targetPathFlowCodes.Any())
                {
                    var targetOiCodes = tags.Where(a => targetPathFlowCodes.Contains(a.PathFlowCode)).Select(a => a.OiCode).ToList();
                    if (targetOiCodes != null && targetOiCodes.Any())
                        new OrderTagsRepository(targetConnectionString).SetDeleted(targetOiCodes, tagName, tagValue);
                }
            });
            #endregion
        }

        /// <summary>
        /// 存在更新状态，不存在添加
        /// </summary>
        /// <param name="orderTags"></param>
        /// <param name="isFx">是否为分单系统，默认true</param>
        /// <param name="isNeedSDI">是否要同步到其他分区</param>
        /// <param name="isAddChangeLog">是否添加变更日志</param>
        public void BulkInsert(List<OrderTags> orderTags, bool isFx = true, bool isNeedSDI = false,
            bool isAddChangeLog = true)
        {
            if (orderTags == null || orderTags.Any() == false)
                return;
            _repository.BulkInsert(orderTags);

            if (isFx == false)
                return;

            #region 调用同步数据接口服务

            if (isNeedSDI)
            {
                var fxUserId = SiteContext.Current?.CurrentFxUser?.Id ?? 0;
                var pathFlowCodes = orderTags.Select(a => a.PathFlowCode).Distinct().ToList();
                new SyncDataInterfaceService(fxUserId).PathFlowOpt(pathFlowCodes,
                    (string targetConnectionString, List<string> targetPathFlowCodes) =>
                    {
                        if (targetPathFlowCodes != null && targetPathFlowCodes.Any())
                        {
                            var targetOrderTags = orderTags.Where(a => targetPathFlowCodes.Contains(a.PathFlowCode))
                                .ToList();
                            if (targetOrderTags != null && targetOrderTags.Any())
                                new OrderTagsRepository(targetConnectionString).BulkInsert(targetOrderTags);
                        }
                    });
            }

            #endregion

            #region 数据变更日志

            //仅LogicOrder的变更日志才有意义，Order、OrderItem变更日志没有对应的处理逻辑
            //var dcLogs = orderTags.Where(x => x.TagType == "LogicOrder").Select(tag =>
            //    new DataChangeLog
            //    {
            //        DataChangeType = DataChangeTypeEnum.UPDATE,
            //        TableTypeName = tag.TagType == "OrderItem"
            //            ? DataChangeTableTypeName.OrderItem
            //            : (tag.TagType == "LogicOrder"
            //                ? DataChangeTableTypeName.LogicOrder
            //                : DataChangeTableTypeName.Order),
            //        SourceShopId = tag.Sid,
            //        SourceFxUserId = tag.SourceFxUserId,
            //        RelationKey = tag.OrderCode,
            //        ExtField1 = "OrderTags-BulkInsert",
            //        ColdHotType = tag.DataFlag
            //    }).ToList();
            //new DataChangeLogRepository().Add(dcLogs);

            var orderTagsByLogicOrder = orderTags.Where(x => x.TagType == "LogicOrder")
                .Select(m => new { m.SourceFxUserId, m.Sid, m.DataFlag, m.OrderCode }).Distinct().ToList();
            if (orderTagsByLogicOrder.Any())
            {
                //是否添加逻辑单变更日志
                var isAddLog = true;
                if (isAddChangeLog == false)
                {
                    isAddLog = new CommonSettingService().IsOrderTagNeedAddChangeLog();
                }
                //如果不需要添加逻辑订单变更日志，则添加订单标签变更日志
                var tableType = DataChangeTableTypeName.LogicOrder;
                if (isAddLog == false)
                {
                    tableType = DataChangeTableTypeName.OrderTags;
                }
                var dcLogs = orderTagsByLogicOrder.Select(tag =>
                    new DataChangeLog
                    {
                        DataChangeType = DataChangeTypeEnum.INSERT,
                        TableTypeName = tableType,
                        SourceShopId = tag.Sid,
                        SourceFxUserId = tag.SourceFxUserId,
                        RelationKey = tag.OrderCode,
                        ExtField1 = "OrderTags-BulkInsert",
                        ColdHotType = tag.DataFlag
                    }).ToList();
                new DataChangeLogRepository().Add(dcLogs);
            }
            #endregion

            #region 分库方案增加逻辑， 推送复制副本消息

            ////分库方案增加逻辑
            ///改用定时任务推送触发消息2023.04.28
            //var duplicationMessages = orderTags.GroupBy(m => new { m.Sid, m.SourceFxUserId }).ToList();
            ////推送复制副本消息
            //duplicationMessages.ForEach(group =>
            //{
            //    DuplicationFactoryService.Instance(DataChangeTableTypeName.LogicOrder)
            //        .PushMessage(group.Key.Sid, group.Key.SourceFxUserId);
            //});

            #endregion
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="orderTags"></param>
        /// <param name="isNeedSDI">是否要同步到其他分区</param>
        public void BulkInsertWithNoDataChangeLog(List<OrderTags> orderTags, bool isNeedSDI = false)
        {
            if (orderTags == null || orderTags.Any() == false)
                return;
            _repository.BulkInsert(orderTags);

            #region 调用同步数据接口服务
            if (isNeedSDI)
            {
                var fxUserId = SiteContext.Current?.CurrentFxUser?.Id ?? 0;
                var pathFlowCodes = orderTags.Select(a => a.PathFlowCode).Distinct().ToList();
                new SyncDataInterfaceService(fxUserId).PathFlowOpt(pathFlowCodes, (string targetConnectionString, List<string> targetPathFlowCodes) =>
                {
                    if (targetPathFlowCodes != null && targetPathFlowCodes.Any())
                    {
                        var targetOrderTags = orderTags.Where(a => targetPathFlowCodes.Contains(a.PathFlowCode)).ToList();
                        if (targetOrderTags != null && targetOrderTags.Any())
                            new OrderTagsRepository(targetConnectionString).BulkInsert(targetOrderTags);
                    }
                });
            }
            #endregion
        }

        public void BulkCopyInsert(List<OrderTags> models)
        {
            if (models == null || models.Any() == false)
                return;

            _repository.SqlBulkCopy(models);
        }

        /// <summary>
        /// 获取标签信息，为复制副本
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="sids"></param>
        /// <param name="tagType"></param>
        /// <param name="whereFieldName"></param>
        /// <param name="tag">标签</param>
        /// <returns></returns>
        public List<OrderTags> GetListForDuplication(List<string> codes, List<int> sids, string tagType,
            string whereFieldName = "OiCode", string tag = null)
        {
            if (codes == null || !codes.Any())
                return new List<OrderTags>();

            var list = new List<OrderTags>();
            var batchSize = 500;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                //var batchList = _repository.GetListForDuplication(batchCodes, sids, tagType, whereFieldName);
                var batchList = _repository.GetListForDuplicationNew(batchCodes, sids, tagType, whereFieldName);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 批量插入数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        public void InsertsForDuplication(List<OrderTags> models, int isNotUpdate = 0)
        {
            if (models == null || !models.Any())
                return;
            //清理源库ID
            models.ForEach(m => { m.Id = 0; });

            var batchSize = 500;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();

                //代码
                var codes = batchModels.Select(m => m.UniqueKey).Distinct().ToList();
                var sids = batchModels.Select(m => m.Sid).Distinct().ToList();
                
                //存在的OrderTag列表
                var idAndCodes = _repository.GetExistIdAndCodes(codes);
                
                //全部不存在
                if (idAndCodes.IsNullOrEmptyList())
                {
                    try
                    {
                        baseRepository.BulkWrite(batchModels, "P_OrderTags", maxSingleNum: 1);
                    }
                    catch (Exception e)
                    {
                        var db = baseRepository.DbConnection;
                        if (db.State == System.Data.ConnectionState.Closed)
                            db.Open();
                        using (db)
                        {
                            //单条
                            batchModels.ForEach(item =>
                            {
                                try
                                {
                                    db.Insert(item);
                                }
                                catch (Exception ex2)
                                {
                                    var errMsg = ex2.Message.ToLower();
                                    if (errMsg.Contains("duplicate key") || errMsg.Contains("pk_") ||
                                        errMsg.Contains("primary key"))
                                    {
                                        //忽略
                                    }
                                    else
                                    {
                                        throw ex2;
                                    }
                                }
                            });
                        }
                    }
                    
                    continue;
                }

                //存在
                var existCodes = idAndCodes.Select(m => m.Code).ToList();
                if (isNotUpdate == 0)
                {
                    // 需要更新的字段
                    var needUpdateFields = new List<string>()
                    {
                        "Status","Sort","TagValue"
                    };
                    var selectFields = string.Join(",", new List<string>(needUpdateFields) { "OiCode", "Id" ,"Sid","Tag","TagType"});
                    var existList = _repository.GetListForDuplication(existCodes,selectFields:selectFields);

                    //existList.AddRange(_repository.GetListForDuplication(existCodes,selectFields: selectFields));
                    //existList.AddRange(_repository.GetListForDuplication(existCodes,selectFields: selectFields));
                    var needUpdates = batchModels.Where(m => existCodes.Contains(m.UniqueKey)).ToList();

                    var keyFields = new List<string>() { "UniqueKey" };
                    if (needUpdates.Any())
                    {
                        needUpdates.ForEach(o =>
                        {
                            var model = idAndCodes.FirstOrDefault(
                                m => m.Code == o.UniqueKey);
                            if (model == null)
                            {
                                return;
                            }
                            o.Id = model.Id;
                        });
                        try
                        {
                            var tuple = EntityUtils.CompareFields<OrderTags>(existList, needUpdates, needUpdateFields,
                                keyFields);
                            needUpdates = tuple.Item2;
                            needUpdateFields = tuple.Item1;
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"OrderTags，与数据库旧数据比较失败：{ex}，将使用旧逻辑进行全量更新");
                        }

                        _repository.BatchUpdate(needUpdates, needUpdateFields, keyFields);
                        Log.Debug(() => $"OrderTags 副本更新数据{needUpdates.Count}条，更新字段{needUpdateFields.ToJson()}",
                            "wm_order2.txt");
                    }
                }

                //不存在
                var inserts = batchModels
                    .Where(m => !existCodes.Contains(m.UniqueKey)).ToList();
                if (inserts.Any())
                {
                    try
                    {
                        baseRepository.BulkWrite(inserts, "P_OrderTags", maxSingleNum: 1);
                    }
                    catch (Exception e)
                    {
                        var db = baseRepository.DbConnection;
                        if (db.State == System.Data.ConnectionState.Closed)
                            db.Open();
                        using (db)
                        {
                            //单条
                            inserts.ForEach(item =>
                            {
                                try
                                {
                                    db.Insert(item);
                                }
                                catch (Exception ex2)
                                {
                                    var errMsg = ex2.Message.ToLower();
                                    if (errMsg.Contains("duplicate key") || errMsg.Contains("pk_") ||
                                        errMsg.Contains("primary key"))
                                    {
                                        //忽略
                                    }
                                    else
                                    {
                                        throw ex2;
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="orderTags"></param>
        /// <param name="tagNames"></param>
        /// <param name="tagValue"></param>
        /// <param name="fxUserId">当前用户</param>
        /// <param name="isAddChangeLog"></param>
        public void BulkInsertOrUpdateTags(List<OrderTags> orderTags, List<string> tagNames, string tagValue,int fxUserId = 0,
            bool isAddChangeLog = false)
        {
            if (orderTags == null || orderTags.Any() == false)
                return;
            _repository.BulkInsertOrUpdateTags(orderTags, tagNames, tagValue);

            //是否添加逻辑单变更日志
            var isAddLog = true;
            if (isAddChangeLog == false)
            {
                isAddLog = new CommonSettingService().IsOrderTagNeedAddChangeLog();
            }

            #region 同步接口复制副本

            if (isAddLog == false)
            {
                var pathFlowCodes = orderTags.Select(a => a.PathFlowCode).Distinct().ToList();
                new SyncDataInterfaceService(fxUserId).PathFlowOpt(pathFlowCodes,
                    (targetConnectionString, targetPathFlowCodes) =>
                    {
                        if (targetPathFlowCodes != null && targetPathFlowCodes.Any())
                        {
                            var targetOrderTags = orderTags.Where(a => targetPathFlowCodes.Contains(a.PathFlowCode))
                                .ToList();
                            if (targetOrderTags.Any())
                                new OrderTagsRepository(targetConnectionString).BulkInsertOrUpdateTags(targetOrderTags,
                                    tagNames, tagValue);
                        }
                    });
            }

            #endregion

            #region 数据变更日志

            //仅LogicOrder的变更日志才有意义，Order、OrderItem变更日志没有对应的处理逻辑
            //var dcLogs = orderTags.Where(x => x.TagType == "LogicOrder").Select(tag =>
            //    new DataChangeLog
            //    {
            //        DataChangeType = DataChangeTypeEnum.UPDATE,
            //        TableTypeName = tag.TagType == "OrderItem"
            //            ? DataChangeTableTypeName.OrderItem
            //            : (tag.TagType == "LogicOrder"
            //                ? DataChangeTableTypeName.LogicOrder
            //                : DataChangeTableTypeName.Order),
            //        SourceShopId = tag.Sid,
            //        SourceFxUserId = tag.SourceFxUserId,
            //        RelationKey = tag.OrderCode,
            //        ExtField1 = "OrderTags-BulkInsertOrUpdateTags"
            //    }).ToList();
            //new DataChangeLogRepository().Add(dcLogs);

            var orderTagsByLogicOrder = orderTags.Where(x => x.TagType == "LogicOrder")
                .Select(m => new { m.SourceFxUserId, m.Sid, m.TagType, m.OrderCode }).Distinct().ToList();
            if (orderTagsByLogicOrder.Any())
            {
                //如果不需要添加逻辑订单变更日志，则添加订单标签变更日志
                var tableType = DataChangeTableTypeName.LogicOrder;
                if (isAddLog == false)
                {
                    tableType = DataChangeTableTypeName.OrderTags;
                }
                //仅LogicOrder的变更日志才有意义，Order、OrderItem变更日志没有对应的处理逻辑
                var dcLogs = orderTagsByLogicOrder.Where(x => x.TagType == "LogicOrder").Select(tag =>
                    new DataChangeLog
                    {
                        DataChangeType = DataChangeTypeEnum.UPDATE,
                        TableTypeName = tableType,
                        SourceShopId = tag.Sid,
                        SourceFxUserId = tag.SourceFxUserId,
                        RelationKey = tag.OrderCode,
                        ExtField1 = "OrderTags-BulkInsertOrUpdateTags"
                    }).ToList();
                new DataChangeLogRepository().Add(dcLogs);
            }

            #endregion
        }
        public static void SetOrderTag(Order order, string tagKey, string tagVal, OrderItem oi)
        {
            if (order == null || oi == null)
                return;

            OrderTags tagModel;
            if (string.IsNullOrWhiteSpace(tagVal))
            {
                //去标
                tagModel = new OrderTags()
                {
                    OiCode = oi.OriginalOrderItemCode,
                    Sid = order.ShopId,
                    Platform = order.PlatformType,
                    Tag = tagKey,
                    TagType = TagType.OrderItem.ToString(),
                    Status = -1, //有可能去标，去标的不会插入，只有存在的标签，去标会更新
                    CreateTime = DateTime.Now
                };
            }
            else
            {
                //打标
                tagModel = new OrderTags()
                {
                    OiCode = oi.OriginalOrderItemCode,
                    Sid = order.ShopId,
                    Platform = order.PlatformType,
                    Tag = tagKey,
                    TagValue = tagVal,
                    TagType = TagType.OrderItem.ToString(),
                    Status = 0, //有可能去标，去标的不会插入，只有存在的标签，去标会更新
                    CreateTime = DateTime.Now
                };
            }
            oi.Tags.Add(tagModel);
        }

        /// <summary>
        /// 检查逻辑单是否带有“地址变更”标签
        /// </summary>
        public List<string> CheckLogicOrderReceiverChange(List<string> LogicOrderIdList)
        {
            List<string> failList = _repository.CheckLogicOrderReceiverChange(LogicOrderIdList);
            return failList;
        }

        /// <summary>
        /// 检查订单是否带有“商品变更”的标签
        /// </summary>
        /// <param name="logicOrderIdList"></param>
        /// <returns></returns>
        public List<string> GetProductChangeTag(List<string> logicOrderIdList)
        {
            List<string> failList = _repository.GetProductChangeTag(logicOrderIdList);
            return failList;
        }

        /// <summary>
        /// 去掉带有“商品变更”标签的订单
        /// </summary>
        /// <param name="logicOrderIdList"></param>
        public void InvalidateOrderTags(List<string> logicOrderIdList)
        {
            _repository.InvalidateOrderTags(logicOrderIdList);
        }

        public void AddHighQualityExpress(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return;

            var pair = value.ToObject<List<Data.Model.HighQualityExpressModel>>();
            if (pair == null || !pair.Any())
                return;

            foreach (var item in pair)
            {
                OrderTags orderTags = new OrderTags()
                {
                    OiCode = item.OriginalOrderItemCode,
                    Sid = item.ShopId,
                    Platform = PlatformType.TouTiao.ToString(),
                    Tag = OrderTag.toutiao_high_quality_logistics.ToString(),
                    TagValue = item.Value,
                    TagType = TagType.OrderItem.ToString(),
                    Status = 0,
                    Sort = 0,
                    CreateTime = DateTime.Now
                };

                var tag = GetModel(orderTags.OiCode, orderTags.Tag);
                if (tag == null)
                    baseRepository.Add(orderTags);
                else
                {
                    orderTags.Id = tag.Id;
                    baseRepository.BatchUpdateFields(new List<OrderTags>() { orderTags },
                        new List<string>() { "TagValue" });
                }
            }
        }
    }
}
