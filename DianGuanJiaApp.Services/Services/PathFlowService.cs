using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Services.Services.SyncDataInterface;

namespace DianGuanJiaApp.Services
{

    public partial class PathFlowService : BaseService<PathFlow>
    {
        private PathFlowRepository _repository = null;
        private SyncStatusService syncStatusService = new SyncStatusService();

        public PathFlowService()
        {
            _repository = new PathFlowRepository();
            this._baseRepository = _repository;

        }
        public PathFlowService(string connectionString) : base(connectionString)
        {
            _repository = new PathFlowRepository(connectionString);

        }


        public void BulkMerger(List<PathFlow> pathFlowList)
        {
            if (pathFlowList != null && pathFlowList.Any())
            {
                _repository.BulkMerger(pathFlowList);

                //冷热路径流复制副本-调整完成
                if (SiteContext.Current.IsWriteToColdDb)
                {
                    new PathFlowRepository(SiteContext.Current.CurrentDbConfig.ColdDbConnectionString).BulkMerger(
                        pathFlowList);
                }

                #region 数据变更日志
                List<DataChangeLog> dcLogs = pathFlowList.Select(o => new DataChangeLog
                {
                    DataChangeType = DataChangeTypeEnum.INSERT,
                    TableTypeName = DataChangeTableTypeName.PathFlow,
                    SourceShopId = o.SourceShopId,
                    SourceFxUserId = o.SourceFxUserId,
                    RelationKey = o.PathFlowCode,
                    ExtField1 = "PathFlow.BulkMerger"
                }
                    ).ToList();
                new DataChangeLogRepository().Add(dcLogs, 1);
                #endregion

                #region 调用同步数据接口服务
                //TODO:同步接口需要冷热调整(冷热路径流复制副本)-调整完成
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                new SyncDataInterfaceService(fxUserId).PathFlowOpt(pathFlowList, (targetConnectionString, targetPathFlows, targetFxUserIds) =>
                {
                    if (targetPathFlows != null && targetPathFlows.Any())
                        new PathFlowRepository(targetConnectionString).BulkMerger(targetPathFlows);
                }, callbackByDbConfig: (targetDbConfig, targetPathFlows) =>
                {
                    if (targetPathFlows != null && targetPathFlows.Any() && targetDbConfig.IsWriteToColdDb)
                    {
                        new PathFlowRepository(targetDbConfig.ColdDbConnectionString).BulkMerger(targetPathFlows);
                    }
                });
                #endregion
            }
        }

        /// <summary>
        /// 根据最终处理人查询路径流
        /// </summary>
        /// <param name="fxUserId">当前登录分销用户id</param>
        ///  <param name="filterExpiredFlag">判断是否需要过滤掉授权过期的店铺</param>
        /// <returns></returns>
        public IEnumerable<SimplePathFlow> GetPathFlowByFxUserId(int fxUserId, bool filterExpiredFlag = false)
        {
            var pathFlowList = _repository.GetPathFlowByFxUserIdV2(fxUserId);
            //商家数据源（过滤非绑定合作关系路径流，防止同步解绑合作商家店铺信息）
            var _supplierUserService = new SupplierUserService();
            var fields = "t1.Id,t1.Status,t1.FxUserId";
            var unbindAgentIds = _supplierUserService.GetAgentList(fxUserId, fields: fields)?.Where(x => x.Status != AgentBingSupplierStatus.Binded).Select(x => x.FxUserId).ToList();
            if (unbindAgentIds != null && unbindAgentIds.Any())
                pathFlowList = pathFlowList.Where(x => unbindAgentIds.Contains(x.SourceFxUserId) == false).ToList();
            // 过滤已解绑店铺
            var fxUserShopService = new FxUserShopService();
            var bindShops = fxUserShopService.GetCloudPlatformTypeShopsByFxUserId(fxUserId);
            if (filterExpiredFlag)
            {
                bindShops = bindShops.Where(x => !x.IsExpire).ToList();
            }

            pathFlowList = pathFlowList.Where(x => x.SourceFxUserId != fxUserId || (x.SourceFxUserId == fxUserId && bindShops.Any(y => y.ShopId == x.SourceShopId)));

            return pathFlowList;
        }

        public List<PathFlow> GetPathFlowWithFxUserId(int fxUserId)
        {
            return _repository.GetPathFlowWithFxUserId(fxUserId);
        }

        public PathFlow GetPathFlow(string PathFlowCode)
        {
            return _repository.GetPathFlow(PathFlowCode);
        }

        /// <summary>
        /// 根据账号店铺查询路径流
        /// </summary>
        /// <param name="shopIds">SourceShopId</param>
        /// <returns></returns>
        public List<PathFlow> GetPathFlowByShopIds(List<int> shopIds)
        {
            return _repository.GetPathFlowByShopIds(shopIds);
        }

        /// <summary>
        /// 根据账号店铺及用户查询路径流
        /// </summary>
        /// <param name="shopIds">SourceShopId</param>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<PathFlow> GetPathFlowByShopIds(List<int> shopIds, List<int> fxUserIds)
        {
            return _repository.GetPathFlowByShopIds(shopIds, fxUserIds);
        }

        /// <summary>
        /// 厂家FxUserId与路径流关系（迁移）
        /// </summary>
        /// <param name="shopIds">SourceShopId</param>
        /// <param name="fxUserIds">SourceFxUserId</param>
        /// <returns></returns>
        public Dictionary<int, List<string>> GetSupplierPathFlowByShopIds(List<int> shopIds, List<int> fxUserIds)
        {
            if (shopIds == null || !shopIds.Any() || fxUserIds == null || !fxUserIds.Any())
                return null;

            var result = new Dictionary<int, List<string>>();
            var list = _repository.GetSupplierPathFlowByShopIds(shopIds, fxUserIds);
            list.ForEach(p =>
            {
                //考虑到迁移完成，路径流已打乱，还要补偿的情况
                var fxUserId = Math.Abs(p.FxUserId);
                var pathFlowCode = p.PathFlowCode.Replace("-", "");
                var codes = new List<string>();
                if (result.TryGetValue(fxUserId, out codes))
                {
                    if (!codes.Contains(pathFlowCode))
                        codes.Add(pathFlowCode);
                }
                else
                    result.Add(fxUserId, new List<string> { pathFlowCode });
            });
            return result;
        }

        /// <summary>
        /// 返回带PathFlowNode数据
        /// </summary>
        /// <param name="listPathFlowCode"></param>
        /// <returns></returns>
        public List<PathFlow> GetPathFlowList(List<string> listPathFlowCode)
        {
            return _repository.GetPathFlowList(listPathFlowCode);
        }
        /// <summary>
        /// 获取当前登录FxUserId指定路径流的上下级Id
        /// </summary>
        /// <param name="PathFlowCodes"></param>
        /// <param name="FxUserId"></param>
        /// <returns></returns>
        public List<PathFlowNode> GetPathFlowNodeListByFxUserId(List<string> PathFlowCodes, int FxUserId)
        {
            return _repository.GetPathFlowNodeListByFxUserId(PathFlowCodes, FxUserId);
        }

        public List<Shop> GetCloudPlatformTypeSourceShopIdByPathFlowUserId(int fxUserId)
        {
            var pathFlowList = GetPathFlowByFxUserId(fxUserId);
            pathFlowList = pathFlowList.Where(f => f.SourceShopId > 0);
            var sids = pathFlowList.Select(f => f.SourceShopId).Distinct().ToList();
            var shopList = new ShopService().GetShopByIds(sids);
            var cloudPlatformType = CustomerConfig.CloudPlatformType;
            if (cloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
                shopList = shopList.Where(t => t.PlatformType == PlatformType.Pinduoduo.ToString()).ToList();
            else if (cloudPlatformType == CloudPlatformType.Jingdong.ToString())
                shopList = shopList.Where(t => t.PlatformType == PlatformType.Jingdong.ToString()).ToList();
            else if (cloudPlatformType == CloudPlatformType.TouTiao.ToString())
                shopList = shopList.Where(t => t.PlatformType == PlatformType.TouTiao.ToString()).ToList();
            else
                shopList = shopList.Where(t => t.PlatformType != PlatformType.Pinduoduo.ToString() && t.PlatformType != PlatformType.Jingdong.ToString()).ToList();

            shopList.ForEach(s =>
            {
                var userId = 0;
                pathFlowList.ToList().ForEach(p =>
                {
                    if (s.Id == p.SourceShopId)
                        userId = p.SourceFxUserId;
                });
                s.FxUserIds = userId;
            });

            return shopList;
        }

        /// <summary>
        /// 和商品一样开始同步，路径流和引用
        /// </summary>
        /// <param name="product"></param>
        /// <param name="shopId"></param>
        public void SetPathFlowAndReference(List<ProductFx> product, int shopId)
        {
            if (product.Count == 0)
                return;

            var fxUserId = SiteContext.GetCurrentFxUserId();
            var syncStatus = syncStatusService.InitializeSyncStatus(shopId, fxUserId, ShopSyncType.PathFlow);

            try
            {
                //同步状态修改为【同步中...】
                syncStatus.LastSyncStatus = "Pending";
                syncStatusService.UpdateSyncStatusByStatus(syncStatus);

                //转换model【PathFlow】
                var pathFlowList = TransferModelToProductFx(product);

                //很多商品，去重路径流，处理过滤，没有查数据
                var pathFlowDataList = new List<PathFlow>();
                pathFlowList.ForEach(pf =>
                {
                    var pathflows = pathFlowDataList.Where(p => p.PathFlowCode == pf.PathFlowCode && p.SourceFxUserId == pf.SourceFxUserId && p.SourceShopId == pf.SourceShopId).FirstOrDefault();
                    if (pathflows == null)
                        pathFlowDataList.Add(pf);
                });

                //过滤后，路径流修改、保存
                BulkMerger(pathFlowDataList);

                //路径流同步状态修改为【同步完成】
                syncStatus.LastSyncStatus = "Finished";
                syncStatusService.UpdateSyncStatusByStatus(syncStatus);



                //为了避免重复查询。product和pathFlowDataList传入，其他地方用于检查，就传空数组
                //路径流和商品引用关系，开始添加
                new PathFlowReferenceService().SetPathFlowReference(fxUserId, product, pathFlowDataList);

            }
            catch (Exception e)
            {
                //同步状态修改为【同步异常】
                syncStatus.LastSyncStatus = "Error";
                syncStatus.LastSyncMessage = e.Message;
                syncStatusService.UpdateSyncStatusByStatus(syncStatus);
            }
        }


        public List<PathFlow> TransferModelToProductFx(List<ProductFx> product)
        {
            var fxUserId = SiteContext.GetCurrentFxUserId();

            var listPathFlow = new List<PathFlow>();
            if (product == null || (product != null && product.Count == 0))
                return listPathFlow;

            var times = DateTime.Now;

            product.ForEach(p =>
            {
                var pathFlow = new PathFlow();
                pathFlow.PathFlowCode = PathFlowService.CreateDefaultPathFlowCode(p.ShopId, p.SourceUserId);
                pathFlow.SourceShopId = p.ShopId;
                pathFlow.SourceFxUserId = fxUserId;
                pathFlow.CreateTime = times;
                pathFlow.UpdateTime = times;
                pathFlow.CreateBy = fxUserId;
                pathFlow.UpdateBy = fxUserId;

                var listPathFlowByNode = new List<PathFlowNode>();

                var pathFlowNode = new PathFlowNode();
                pathFlowNode.PathFlowCode = pathFlow.PathFlowCode;
                //pathFlowNode.PathFlowNodeCode = (p.ShopId + p.SourceUserId + p.ProductCode).ToShortMd5();
                pathFlowNode.FxUserId = fxUserId;
                pathFlowNode.UpFxUserId = 0;
                pathFlowNode.DownFxUserId = 0;
                pathFlowNode.Status = 0;
                listPathFlowByNode.Add(pathFlowNode);

                //目前只有一个，因为新创建的话，路径流的PathFlowNode，指向自己。
                pathFlow.PathFlowNodes = listPathFlowByNode;

                listPathFlow.Add(pathFlow);
            });


            return listPathFlow;
        }





        //public void ProductFxGlobalScanning(List<ProductFx> product, Shop shop)
        //{
        //    var fxUserId = SiteContext.Current.CurrentFxUser==null ? 0 : SiteContext.Current.CurrentFxUserId;

        //    var syncStatus = syncStatusService.InitializeSyncStatus(shop.Id, fxUserId, ShopSyncType.PathFlow);
        //    try
        //    {
        //        syncStatus.LastSyncStatus = "Pending";
        //        syncStatusService.UpdateSyncStatusByStatus(syncStatus);

        //        _repository.GlobalScanning(product, shop, fxUserId);

        //        syncStatus.LastSyncStatus = "Finished";
        //        syncStatusService.UpdateSyncStatusByStatus(syncStatus);
        //    }
        //    catch (Exception e) {
        //        syncStatus.LastSyncStatus = "Error";
        //        syncStatus.LastSyncMessage = e.Message;
        //        syncStatusService.UpdateSyncStatusByStatus(syncStatus);

        //    }
        //}

        /// <summary>
        /// 只有一个节点的PathFlowCode生成
        /// </summary>
        /// <param name="sourceShopId">源店铺ID</param>
        /// <param name="sourceFxUserId">源商家ID</param>
        /// <returns></returns>
        public static string CreateDefaultPathFlowCode(int sourceShopId, int sourceFxUserId)
        {
            var code = ("S" + sourceShopId + "S" + "P" + sourceFxUserId + "P" + "N" + sourceFxUserId + "N").ToShortMd5();
            return code;
        }

        /// <summary>
        /// 检查是否存在路径流
        /// </summary>
        /// <param name="pathFlowCode">路径流Code</param>
        /// <returns>存在返回：true,不存在返回：false</returns>
        public bool CheckIfExistPathFlow(string pathFlowCode)
        {
            return _repository.CheckIfExistPathFlow(pathFlowCode);
        }

        /// <summary>
        /// 创建默认路径流
        /// </summary>
        /// <param name="sourceShopId">源店铺ID</param>
        /// <param name="sourceFxUserId">源商家ID</param>
        /// <returns>返回路径流Code</returns>
        public string CreateDefaultPathFlow(int sourceShopId, int sourceFxUserId)
        {
            var code = CreateDefaultPathFlowCode(sourceShopId, sourceFxUserId);
            if (CheckIfExistPathFlow(code) == false)
            {
                //创建默认路径流
                var pathFlow = BuildDefaultPathFlow(sourceShopId, sourceFxUserId);
                CreatePathFlow(new List<PathFlow> { pathFlow });
            }
            return code;
        }

        /// <summary>
        /// 创建默认路径流
        /// </summary>
        /// <param name="sourceShopId">源店铺ID</param>
        /// <param name="sourceFxUserId">源商家ID</param>
        /// <returns>返回路径流Code</returns>
        public static PathFlow BuildDefaultPathFlow(int sourceShopId, int sourceFxUserId)
        {
            var code = CreateDefaultPathFlowCode(sourceShopId, sourceFxUserId);
            //创建默认路径流
            var pathFlow = new PathFlow
            {
                SourceFxUserId = sourceFxUserId,
                SourceShopId = sourceShopId,
                CreateBy = sourceFxUserId,
                CreateTime = DateTime.Now,
                UpdateBy = sourceFxUserId,
                PathFlowCode = code,
                PathFlowNodes = new List<PathFlowNode>
                {
                    new PathFlowNode
                    {
                        UpFxUserId =0,
                        FxUserId = sourceFxUserId,
                        DownFxUserId =0,
                        NodeIndex = 0,
                        //PathFlowNodeCode = new Guid().ToString().ToShortMd5(),
                        Status = 0,
                        PathFlowCode = code
                    }
                }
            };
            return pathFlow;
        }

        /// <summary>
        /// 只有一个节点的PathFlowCode生成
        /// </summary>
        /// <param name="pathFlow">路径流信息</param>
        /// <returns></returns>
        public static string CreatePathFlowCode(PathFlow pathFlow)
        {
            var sourceShopId = pathFlow.SourceShopId;
            var sourceFxUserId = pathFlow.SourceFxUserId;
            var nodeFxUserIds = pathFlow.SortedNodes.Select(x => x.FxUserId);
            if (nodeFxUserIds == null || nodeFxUserIds.Any() == false)
                nodeFxUserIds = new List<int> { sourceFxUserId };
            var pfns = string.Join("", nodeFxUserIds.Select(x => $"N{x}N"));
            var code = ("S" + sourceShopId + "S" + "P" + sourceFxUserId + "P" + pfns).ToShortMd5();
            return code;
        }

        /// <summary>
        /// 根据产品Code查询路径流的完整信息
        /// </summary>
        /// <param name="refCodes">产品Codes</param>
        /// <param name="refType">0：全部商品和SKU，1：PathFlowRefCode 2 PathFlowCode</param>
        /// <param name="pfrStatus">pfr表状态 0正常 -1删除 2不判断状态字段</param>
        /// <returns></returns>
        public List<PathFlow> GetPathFlows(List<string> refCodes, int refType, string fields = "", int pfrStatus=0)
        {
            return _repository.GetPathFlows(refCodes, refType, fields,pfrStatus);
        }

        /// <summary>
        ///  获取路径流信息，结算价用
        /// </summary>
        /// <param name="refCodes">PathFlowCode</param>
        /// <returns></returns>
        public List<PathFlow> GetPathFlowsBySettlement(List<string> refCodes)
        {
            return _repository.GetPathFlowsBySettlement(refCodes);
        }

        public void CreatePathFlow(List<PathFlow> pathFlows)
        {
            if (pathFlows != null && pathFlows.Any())
            {
                //判断路径中是否存在循环路径
                foreach (var path in pathFlows)
                {
                    var loopNodes = path.PathFlowNodes.GroupBy(x => x.FxUserId).Where(x => x.Count() > 1);
                    if (loopNodes.Any())
                        throw new LogicException("您选择的厂家存在循环绑定，请更换其他厂家");
                }

                _repository.CreatePathFlow(pathFlows);

                //冷热路径流复制副本-调整完成
                if (SiteContext.Current.IsWriteToColdDb)
                {
                    new PathFlowRepository(SiteContext.Current.CurrentDbConfig.ColdDbConnectionString).CreatePathFlow(
                        pathFlows);
                }

                #region 数据变更日志

                List<DataChangeLog> dcLogs = pathFlows.Select(o => new DataChangeLog
                {
                    DataChangeType = DataChangeTypeEnum.INSERT,
                    TableTypeName = DataChangeTableTypeName.PathFlow,
                    SourceShopId = o.SourceShopId,
                    SourceFxUserId = o.SourceFxUserId,
                    RelationKey = o.PathFlowCode,
                    ExtField1 = "PathFlowService.CreatePathFlow"
                }
                ).ToList();
                new DataChangeLogRepository().Add(dcLogs, 1);

                #endregion

                #region 调用同步数据接口服务

                //TODO:同步接口需要冷热调整(冷热路径流复制副本)-调整完成
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                new SyncDataInterfaceService(fxUserId).PathFlowOpt(pathFlows,
                    (targetConnectionString, targetPathFlows, targetFxUserIds) =>
                    {
                        if (targetPathFlows != null && targetPathFlows.Any())
                        {
                            new PathFlowRepository(targetConnectionString).CreatePathFlow(targetPathFlows);
                        }
                    }, callbackByDbConfig: (targetDbConfig, targetPathFlows) =>
                    {
                        if (targetPathFlows != null && targetPathFlows.Any() && targetDbConfig.IsWriteToColdDb)
                        {
                            new PathFlowRepository(targetDbConfig.ColdDbConnectionString).CreatePathFlow(
                                targetPathFlows);
                        }
                    });
                #endregion
            }
        }

        public List<PathFlow> GetPathFlowsByRelationCodes(List<string> relationCodes, string fields = "")
        {
            return _repository.GetPathFlowsByRelationCodes(relationCodes, fields);
        }

        public List<PathFlow> GetSupplierPathFlows(int supplierId, int fxUserId, string fields = "")
        {
            return _repository.GetSupplierPathFlows(supplierId, fxUserId, fields);
        }

        public List<PathFlowNode> GetPathFlowNodeList(List<string> PathFlowCodes, bool needOtherPlatform = false)
        {
            return _repository.GetPathFlowNodeList(PathFlowCodes, needOtherPlatform);
        }

        /// <summary>
        /// 获取路径流信息为复制副本
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="pageSize"></param>
        /// <param name="isMigrate">是否为迁移，是：兼容打乱后的路径流</param>
        /// <returns></returns>
        public List<PathFlow> GetListForDuplication(DuplicationConditionModel condition, int pageSize = 100, bool isMigrate = false)
        {
            var list = new List<PathFlow>();
            if (isMigrate)
            {
                var fxUserIds = new List<int> { condition.FxUserId, condition.FxUserId * -1 };
                var shopIds = new List<int> { condition.ShopId, condition.ShopId * -1 };
                var curList = _repository.GetListForDuplicationForMigrate(condition, pageSize, fxUserIds, shopIds);

                //处理兼容
                Dictionary<string, PathFlow> dict = new Dictionary<string, PathFlow>();
                curList.ForEach(p =>
                {
                    p.PathFlowCode = p.PathFlowCode.Replace("-", "");
                    p.SourceFxUserId = Math.Abs(p.SourceFxUserId);
                    p.SourceShopId = Math.Abs(p.SourceShopId);
                    if (!dict.ContainsKey(p.PathFlowCode))
                    {
                        dict.Add(p.PathFlowCode, p);
                    }
                });
                list = dict.Values.ToList();
            }
            else
            {
                list = _repository.GetListForDuplication(condition, pageSize);
            }
            return list;
        }
        /// <summary>
        /// 获取路径流信息为复制副本
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<PathFlow> GetListForSameCloudMigrate(DuplicationConditionModel condition, int pageSize = 100)
        {
            var list = _repository.GetListForSameCloudMigrate(condition, pageSize);
            return list;
        }

        /// <summary>
        /// 获取路径流信息为复制副本，按路径流代码列表
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <param name="isMigrate">是否为迁移，是：兼容打乱后的路径流</param>
        /// <returns></returns>
        public List<PathFlow> GetListForDuplication(List<string> codes, string selectFields = "*", bool isMigrate = false)
        {
            if (codes == null || !codes.Any())
                return new List<PathFlow>();
            var list = new List<PathFlow>();
            if (isMigrate)
            {
                //兼容“-”
                codes.AddRange(codes.Select(p => $"-{p}").ToList());

                var curList = _repository.GetListForDuplication(codes, selectFields);

                //处理兼容
                Dictionary<string, PathFlow> dict = new Dictionary<string, PathFlow>();
                curList.ForEach(p =>
                {
                    p.PathFlowCode = p.PathFlowCode.Replace("-", "");
                    p.SourceFxUserId = Math.Abs(p.SourceFxUserId);
                    p.SourceShopId = Math.Abs(p.SourceShopId);
                    if (!dict.ContainsKey(p.PathFlowCode))
                    {
                        dict.Add(p.PathFlowCode, p);
                    }
                });
                list = dict.Values.ToList();
            }
            else
            {
                list = _repository.GetListForDuplication(codes, selectFields);
            }
            return list;
        }

        /// <summary>
        /// 批量插入数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        public void InsertsForDuplication(List<PathFlow> models)
        {
            if (models == null || !models.Any())
                return;
            //清理源库ID
            models.ForEach(m => { m.Id = 0; });
            //路径流代码
            var pathFlowCodes = models.Select(m => m.PathFlowCode).ToList();
            //存在的路径流代码列表
            var idAndCodes = _repository.GetExistIdAndCodes(pathFlowCodes);


            //Log.WriteLine($"迁移插入目标路径流：{pathFlowCodes.ToJson()}，idAndCodes：{idAndCodes?.ToJson()}");

            //全部不存在
            if (idAndCodes == null || !idAndCodes.Any())
            {
                //设置UpdateTime
                models.ForEach(m => { m.UpdateTime = DateTime.Now; });
                _repository.BulkInsert(models);
                return;
            }
            //存在
            var existsPathFlowCodes = idAndCodes.Select(m => m.Code).ToList();
            //var updates = models.Where(m => existsPathFlowCodes.Contains(m.PathFlowCode)).ToList();
            //if (updates.Any())
            //{
            //    updates.ForEach(pathFlow =>
            //    {
            //        var model = idAndCodes.FirstOrDefault(m => m.Code == pathFlow.PathFlowCode);
            //        if (model == null)
            //        {
            //            return;
            //        }
            //        pathFlow.Id = model.Id;
            //    });
            //    _repository.BulkUpdate(updates);
            //}
            //不存在
            var inserts = models.Where(m => !existsPathFlowCodes.Contains(m.PathFlowCode)).ToList();
            if (inserts.Any())
            {
                //设置UpdateTime
                inserts.ForEach(m => { m.UpdateTime = DateTime.Now; });
                _repository.BulkInsert(inserts);
            }
        }

        /// <summary>
        /// 打乱路径流信息
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public int UpsetPathFlow(List<int> shopIds)
        {
            return _repository.UpsetPathFlow(shopIds);
        }

        /// <summary>
        /// 获取用户相关联的用户Id
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<int> GetRelationFxUserIds(List<int> fxUserIds)
        {
            if (fxUserIds == null || !fxUserIds.Any())
                return new List<int>();
            return _repository.GetRelationFxUserIds(fxUserIds);

        }

        /// <summary>
        /// 获取用户路径流所有关联的用户Id（递归）
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<int> GetAllRelationFxUserIds(int fxUserId)
        {
            var result = new List<int>();
            var needQuery = new List<int>();
            result.Add(fxUserId);
            needQuery.Add(fxUserId);
            var depth = 0;

            while (needQuery.Any() && depth <= 5)
            {
                var relFxUserIds = GetRelationFxUserIds(needQuery);

                needQuery = relFxUserIds.Where(fxId => !result.Contains(fxId)).ToList();

                result.AddRange(relFxUserIds);
                result = result.Distinct().ToList();
                depth++;
            }

            return result;
        }

        /// <summary>
        /// 统计节点数量
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<PathFlowCodeAndTotalCount> GetStatPathFlowNodeNum(List<int> fxUserIds)
        {
            if (fxUserIds == null || !fxUserIds.Any())
                return new List<PathFlowCodeAndTotalCount>();
            return _repository.GetStatPathFlowNodeNum(fxUserIds);

        }

        /// <summary>
        /// 获取指定源店铺的所有路径节点
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public List<PathFlowNode> GetPathFlowNodeList(List<int> fxUserIds, List<int> shopIds)
        {
            return _repository.GetPathFlowNodeList(fxUserIds, shopIds);

        }

        /// <summary>
        /// 获取用户相关联的源路径流
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<PathFlow> GetPathFlowWithFxUserId(List<int> fxUserIds)
        {
            return _repository.GetPathFlowWithFxUserId(fxUserIds);
        }

        /// <summary>
        /// 获取指定用户相关的所有子路径流
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<PathFlowNode> GetPathFlowNodeListByFxUserId(List<int> fxUserIds)
        {
            return _repository.GetPathFlowNodeListByFxUserId(fxUserIds);
        }

        /// <summary>
        /// 获取关联的商家或厂家
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="field">UpFxUserId：商家，DownFxUserId:厂家</param>
        /// <returns></returns>
        public List<int> GetRelatedFxUserId(int fxUserId, string field = "UpFxUserId")
        {
            if (string.IsNullOrEmpty(field) || (field.ToLower() != "upfxuserid" && field.ToLower() != "downfxuserid"))
                return new List<int>();
            return _repository.GetRelatedFxUserId(fxUserId, field);
        }
    }
}
