using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Extension;
using System.Threading;
using DianGuanJiaApp.Services.PlatformService;

namespace DianGuanJiaApp.Services
{

    public partial class PreordainService : BaseService<Data.Entity.Preordain>
    {
        private PreordainRepository _repository;

        private OrderLogisticInfoService orderLogisticInfoService = new OrderLogisticInfoService();
        private PrintTemplateService _printTemplateService = new PrintTemplateService();
        private ExpressCompanyService _expressCompanyService = new ExpressCompanyService();
        private AreaCodeInfoService _areaCodeInfoService = new AreaCodeInfoService();

        public PreordainService()
        {
            _repository = new PreordainRepository();
        }

        public PreordainService(string connectionString) : base(connectionString)
        {
            _repository = new PreordainRepository(connectionString);
        }

        public bool AddPreordain(AddPreordainRequestModel model)
        {
            if (model.UpdateIsPreordainOrder == null || model.UpdateIsPreordainOrder.Count == 0)
            {
                throw new LogicException("参数错误");
            }
            var _orderService = new OrderService();
            var orderUpdateIsPreordainModels = new List<OrderForUpdateIsPreordain>();

            //1.修改订单状态为预发货
            if (model.UpdateIsPreordainOrder != null)
            {
                model.UpdateIsPreordainOrder.ForEach(item =>
                {
                    orderUpdateIsPreordainModels.Add(
                        new OrderForUpdateIsPreordain()
                        {
                            IsPreordain = true,
                            ShopId = item.ShopId,
                            PlatformOrderId = item.PlatformOrderId,
                            Id = item.Id,
                            LastExpressTemplateId = model.ExpressId,
                            LastWaybillCode = item.WaybillCode,
                        });
                });
            }

            //2.看看是不是合并订单的所有子订单都加入了预发货，如果所有子订单都加入了预发货，则合并订单也加入预发货
            if (model.AddPreordainOrder != null && model.AddPreordainOrder.Count > 0)
            {
                var selectKeyModel = model.AddPreordainOrder.Select(f => new OrderSelectKeyModel()
                {
                    PlatformOrderId = f.PlatformOrderId,
                    ShopId = f.ShopId,
                });

                var mergerdOrders = _orderService.GetOrders(selectKeyModel.Distinct(new OrderSelectKeyModelComparer()).ToList());

                if (mergerdOrders.Any())
                {
                    //汇总所有子订单key
                    var allMergerdChildOrderKeys = new List<OrderSelectKeyModel>();
                    mergerdOrders.ForEach(item =>
                    {
                        var childOrderIds = item.ChildOrderId.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                        foreach (var oid in childOrderIds)
                        {
                            allMergerdChildOrderKeys.Add(new OrderSelectKeyModel()
                            {
                                ShopId = item.ShopId,
                                PlatformOrderId = oid
                            });
                        }
                    });

                    //查询出所有子订单
                    var mergerdChildOrders = _orderService.GetOrders(allMergerdChildOrderKeys.Distinct(new OrderSelectKeyModelComparer()).ToList());

                    //循环父订单，看看是否所有子订单都加入了预发货
                    mergerdOrders.ForEach(item =>
                    {
                        var flag = true;
                        var waybillCode = string.Empty;
                        var childOrderIds = item.ChildOrderId.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                        foreach (var oid in childOrderIds)
                        {
                            var waitAddProdain = orderUpdateIsPreordainModels.FirstOrDefault(p => p.PlatformOrderId == oid);
                            var addedProdain = mergerdChildOrders.FirstOrDefault(p => p.PlatformOrderId == oid && p.IsPreordain == true);
                            flag = (waitAddProdain != null || addedProdain != null); //是否找到
                            if (flag == true)
                            {
                                waybillCode = waitAddProdain == null ? addedProdain?.LastWaybillCode : waitAddProdain.LastWaybillCode;
                            }
                            else
                            {
                                break;
                            }
                        }
                        if (flag)
                        {
                            orderUpdateIsPreordainModels.Add(new OrderForUpdateIsPreordain()
                            {
                                IsPreordain = true,
                                ShopId = item.ShopId,
                                PlatformOrderId = item.PlatformOrderId,
                                //Id = item.Id,
                                LastExpressTemplateId = model.ExpressId,
                                LastWaybillCode = string.IsNullOrWhiteSpace(item.LastWaybillCode) ? waybillCode : item.LastWaybillCode,
                            });
                        }
                    });

                }

            }
            if (orderUpdateIsPreordainModels.Any())
            {
                //更新订单的预发货状态及保存预发货的快递及单号
                _orderService.BulkUpdateIsPreordain(orderUpdateIsPreordainModels);

                //物流轨迹监控，异步处理
                ThreadPool.QueueUserWorkItem(action =>
                {
                    try
                    {
                        //需监控物流轨迹的数据
                        var olInfos = new List<OrderLogisticInfoModel>();
                        var templateCompany = _expressCompanyService.Get(model.ExpressId);
                        var orders = _orderService.GetOrders(orderUpdateIsPreordainModels.Select(f => new OrderSelectKeyModel()
                        {
                            PlatformOrderId = f.PlatformOrderId,
                            ShopId = f.ShopId,
                        }).ToList());
                        //设置 发件人
                        orders = _orderService.SetSellerInfo(orders);
                        orders.ForEach(item =>
                        {
                            var tempModel = orderUpdateIsPreordainModels.FirstOrDefault(f => f.ShopId == item.ShopId && f.PlatformOrderId == item.PlatformOrderId);
                            var senderAddressList = _areaCodeInfoService.AddressSplit(item.SenderAddress);
                            var reciverAddressList = _areaCodeInfoService.AddressSplit(item.ToFullAddress);
                            //物流轨迹监控
                            olInfos.Add(new OrderLogisticInfoModel()
                            {
                                LogisticCode = templateCompany?.CompanyCode,
                                LogisticName = templateCompany?.Names,
                                LogisticOrderId = tempModel.LastWaybillCode,
                                PlatformOrderId = tempModel.PlatformOrderId,
                                PrintTime = item.LastExpressPrintTime,
                                //SendTime=
                                ShopId = item.ShopId,
                                Sender = new TraceAddress()
                                {
                                    Name = item.SenderName,
                                    Mobile = item.SenderMobile,
                                    Province = senderAddressList.Item1,
                                    City = senderAddressList.Item2,
                                    County = senderAddressList.Item3,
                                    Street = senderAddressList.Item4,
                                },
                                Receiver = new TraceAddress()
                                {
                                    Name = item.ToName,
                                    Mobile = item.ToMobile,
                                    Province = reciverAddressList.Item1,
                                    City = reciverAddressList.Item2,
                                    County = reciverAddressList.Item3,
                                    Street = reciverAddressList.Item4,
                                },
                                PlatformType = item.PlatformType,
                            });
                        });

                        //监控物流轨迹
                        (new OrderLogisticInfoService()).AddLogisticTracesSubject(olInfos);

                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"监控物流轨迹时发生错误：{ex}");
                    }
                });
            }
            return true;
        }

        public bool UpdatePreordain(PreordainViewModel model)
        {
            var _orderService = new OrderService();
            //var sql = "UPDATE P_Preordain SET ExpressId=@ExpressId ,ExpressCode=@ExpressCode WHERE Id = @Id;";
            //return _repository.DbConnection.Execute(sql, new { ExpressId = model.ExpressId, ExpressCode = model.ExpressCode, Id = model.Id }) > 0;
            _orderService.BulkUpdateIsPreordain(new List<OrderForUpdateIsPreordain>() {
                new OrderForUpdateIsPreordain()
                        {
                            IsPreordain = true,
                            ShopId = model.ShopId,
                            PlatformOrderId = model.PlatformOrderId,
                            Id = model.Id,
                            LastExpressTemplateId = model.ExpressId,
                            LastWaybillCode = model.ExpressOrderId,
                        }
            });
            return true;
        }

        public bool DeletePreordain(List<OrderSelectKeyModel> oskmList)
        {
            ////1.删除预发货数据，
            ////2.修改订单相应的状态字段
            //var preordainModelList = _repository.Get("WHERE Id IN@Ids", new { Ids = ids });
            //if (preordainModelList == null || preordainModelList.Count() == 0)
            //    return false;
            //var deleteResult = _repository.DbConnection.Execute("Delete From P_Preordain WHERE Id IN@Ids", new { Ids = ids });

            //if (deleteResult == 0)
            //    return false;

            var _orderService = new OrderService();
            //查询出需要修改ispreordain状态的订单
            var orderList = _orderService.GetOrders(oskmList).ToList();

            UpdateOrderPreordainStatus(orderList);

            return true;
        }

        public void UpdateOrderPreordainStatus(List<Order> orderList)
        {
            //查询出合并订单
            var mergeredOrderQueryModel = new List<OrderSelectKeyModel>();
            var _orderService = new OrderService();
            orderList.ForEach(item =>
            {
                if (item.IsMergered == true && string.IsNullOrWhiteSpace(item.MergeredOrderId) == false)
                {
                    mergeredOrderQueryModel.Add(new OrderSelectKeyModel()
                    {
                        PlatformOrderId = item.MergeredOrderId,
                        ShopId = item.ShopId
                    });
                }
            });

            if (mergeredOrderQueryModel != null && mergeredOrderQueryModel.Count > 0)
            {
                //合并订单
                var mergeredOrderList = _orderService.GetOrders(mergeredOrderQueryModel);

                //汇总子订单和合并订单
                orderList.AddRange(mergeredOrderList);
            }

            //更新
            var orderForUpdateIsPreordainList = new List<OrderForUpdateIsPreordain>();
            orderList.ToList().ForEach(item =>
            {
                orderForUpdateIsPreordainList.Add(new OrderForUpdateIsPreordain()
                {
                    ShopId = item.ShopId,
                    PlatformOrderId = item.PlatformOrderId,
                    Id = item.Id,

                    IsPreordain = false
                });
            });
            _orderService.BulkUpdateIsPreordain(orderForUpdateIsPreordainList);
        }

        /// <summary>
        /// 预发货订单发货
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<DeliverySendOrderResultModel> SendPreordain(List<OrderSelectKeyModel> oskmList)
        {
            //1.查询出预发货订单
            //var preordainModelList = _repository.Get("WHERE Id IN@Ids", new { Ids = ids }).ToList();
            //if (preordainModelList == null || preordainModelList.Count == 0)
            //    throw new LogicException("未找到预发货订单信息");
            ////订单数据
            //var orderList = _orderService.GetOrders(preordainModelList.Select(f => new OrderSelectKeyModel()
            //{
            //    Id = f.OrderId,
            //    ShopId = f.ShopId,
            //    PlatformOrderId = f.PlatformOrderId
            //}).ToList());

            //var orderDict = orderList.ToDictionary<Order, int>(f => f.Id);
            //1.查询出预发货订单
            var _orderService = new OrderService();
            var preordainOrderList = _orderService.GetOrders(oskmList);

            //2.组合成发货模型（先按快递分组，发货只能同一个快递的单一起发）
            var sendModelList = new List<OnlineSendRequestModel>();
            preordainOrderList.GroupBy(f => f.LastExpressTemplateId).ToList().ForEach(item =>
            {
                var sendModel = new OnlineSendRequestModel();
                sendModel.ExpressCompanyId = item.Key ?? 0;
                sendModel.Orders = item.ToList().Select(f =>
                {
                    var sender = new OrderSenderRequestModel { CompanyName = string.Empty, SenderAddress = f.SenderAddress, SenderPhone = f.SenderPhone };
                    // 预发货没有发件人信息，取默认发件人
                    if (f.SenderName.IsNullOrEmpty())
                    {
                        SellerInfoService _sellerService = new SellerInfoService();
                        var defaultSeller = _sellerService.GetOneDefaultSeller(f.ShopId);
                        if (defaultSeller != null)
                        {
                            sender = new OrderSenderRequestModel()
                            {
                                CompanyName = defaultSeller.CompanyName.ToString2(),
                                SenderAddress = defaultSeller.SellerAddress.ToString2(),
                                SenderName = defaultSeller.SellerName.ToString2(),
                                SenderPhone = defaultSeller.SellerPhone.IsNullOrEmpty() ? defaultSeller.SellerMobile.ToString2() : defaultSeller.SellerPhone.ToString2(),
                            };
                        }
                    }

                    return new OrderRequestModel()
                    {
                        Id = f.Id,
                        ShopId = f.ShopId,
                        PlatformOrderId = f.PlatformOrderId,
                        OrderItems = f.OrderItems.Select(oi => oi.Id).ToList(),
                        //.Where(oi =>
                        //        {   //过滤待发货的订单项
                        //            return oi.Status == OrderStatusType.waitsellersend.ToString();
                        //        })
                        WaybillCode = f.LastWaybillCode,
                        Receiver = new OrderReceiverRequestModel()
                        {
                            buyerMemberId = f.BuyerMemberId,
                            toFullName = f.ToName,
                            toPhone = f.ToPhone,
                            toMobile = f.ToMobile,
                            toProvince = f.ToProvince,
                            toCity = f.ToCity,
                            toArea = f.ToProvince.ToString2() + f.ToCity.ToString2() + f.ToCounty.ToString2(),
                            toCounty = f.ToCounty,
                            toPost = f.ToPost
                        },
                        Sender = sender,
                        Buyer = new OrderRequestBuyerModel()
                        {
                            BuyerMemberId = f.BuyerMemberId,
                            BuyerMemberName = f.BuyerMemberName,
                            BuyerWangWang = f.BuyerWangWang
                        },

                    };

                }).ToList();

                //有待发货订单才汇总
                if (sendModel.Orders != null && sendModel.Orders.Count > 0)
                {
                    sendModelList.Add(sendModel);
                }
            });

            //3.发货
            var sendResult = new List<DeliverySendOrderResultModel>();

            sendModelList.ForEach(item =>
            {
                sendResult.AddRange(_orderService.OnlineSend(item));
            });

            //4.移除预发货
            //Delete(ids);
            var succesOrderList = sendResult.Where(f => f.IsSuccess == true).Select(f => f.OrderEntity).ToList();
            UpdateOrderPreordainStatus(succesOrderList);

            return sendResult;
        }

        /// <summary>
        /// 删除预发货
        /// </summary>
        /// <param name="ids"></param>
        public void Delete(List<int> ids)
        {
            _repository.Delete("WHERE Id IN@Ids", new { Ids = ids });
        }


        public PreordainPageResultModel LoadList(PreordainListRequestModel queryModel)
        {

            //var fields = @"t1.Id,t1.ShopId,t1.PlatformOrderId,t1.LastShipTime,t1.LastWaybillCode AS ExpressOrderId,t1.BuyerWangWang AS BuyerMemberName,
            //                t1.ToName AS Reciver,t1.ToMobile,t1.ToPhone,t1.ToFullAddress AS ReciverAddress,t1.ModifyTime AS CreateTime,
            //                t3.Id AS ExpressId,t3.Names AS ExpressName,t3.CompanyCode AS ExpressCode,t4.LogisticStatus,t4.LastTraces";

            //var sql = @"SELECT {0} FROM dbo.P_Order AS t1
            //                LEFT JOIN dbo.P_PrintTemplate AS t2 ON t1.LastExpressTemplateId = t2.Id
            //                LEFT JOIN dbo.P_ExpressCompany AS t3 ON t2.ExpressCompanyId = t3.Id
            //                LEFT JOIN P_OrderLogisticInfo AS t4 ON t1.ShopId = t4.ShopId AND t1.LastWaybillCode = t4.LogisticOrderId AND t3.CompanyCode = t4.LogisticCode
            //                WHERE t1.IsPreordain=1 ";  //t1.LastExpressPrintTime < t4.CreateTime
            //拼多多加密处理
            queryModel.IsPdd = SiteContext.Current.CurrentLoginShop.PlatformType == PlatformType.Pinduoduo.ToString();
            if(queryModel.IsPdd)
            {
                if (string.IsNullOrEmpty(queryModel.Reciver) == false)
                    queryModel.ReciverIndex = PinduoduoPlatformService.GetSearchEncryptString(queryModel.Reciver, false);
                if (string.IsNullOrEmpty(queryModel.ReciverPhone) == false)
                    queryModel.ReciverPhoneIndex = PinduoduoPlatformService.GetSearchEncryptString(queryModel.ReciverPhone, true);
                if (string.IsNullOrEmpty(queryModel.ReciverAddr) == false)
                    queryModel.ReciverAddrIndex = PinduoduoPlatformService.GetSearchEncryptString(queryModel.ReciverAddr, false);
                if (string.IsNullOrEmpty(queryModel.BuyerMemberName) == false)
                    queryModel.BuyerMemberNameIndex = PinduoduoPlatformService.GetSearchEncryptString(queryModel.BuyerMemberName, false);
            }
            //查询出所有快递
            var expressCompanyList = _expressCompanyService.Get();


            //预发货订单，LastExpressTemplateId 储存的是ExpressId（快递id），所以不用通过模板去找
            ////汇总模板Id，查询出模板
            //var templateIds = data?.Item2?.Select(f => f.TemplateId)?.ToList();
            //var templateList = _printTemplateService.LoadTemplates(templateIds);
            var data = _repository.LoadList(queryModel);
            var rows = new List<PreordainViewModel>();
            data.Item1?.ForEach(item =>
            {
                //var template = templateList.FirstOrDefault(f => f.Id == item.TemplateId);
                var express = expressCompanyList.FirstOrDefault(f => f.Id == item.TemplateId);
                if (express != null)
                {
                    item.ExpressId = express.Id;
                    item.ExpressName = express.Names;
                    item.ExpressCode = express.CompanyCode;
                }
                rows.Add(item);
            });

            return new PreordainPageResultModel()
            {
                Total = data.Item2,//分页总数
                TotalCount = data.Item3,//全部总数
                RefundCount = data.Item4,
                NoRefundCount = data.Item3 - data.Item4,
                CollectedCount = data.Item5,
                NoCollectedCount = data.Item3 - data.Item5,
                HasLogisticsCount = data.Item6,
                SoonTimeOutCount = data.Item7,
                IsOrderDesc = queryModel.IsOrderDesc,
                OrderByField = queryModel.OrderByField,
                PageIndex = queryModel.PageIndex,
                PageSize = queryModel.PageSize,
                RealOrderTotal = data.Item2,
                Rows = rows
            };
        }

        private string GetWhereCondition(PreordainListRequestModel queryModel, DynamicParameters parameters)
        {
            var sqlCondition = new StringBuilder();

            //拼多多加密处理
            var isPdd = SiteContext.Current.CurrentLoginShop.PlatformType == PlatformType.Pinduoduo.ToString();

            if (queryModel.ShopId != null && queryModel.ShopId.Count > 0)
            {
                var shopIds = queryModel.ShopId.Where(f => f != 0).ToList();
                sqlCondition.Append(" AND t1.ShopId IN@ShopIds");
                parameters.Add("ShopIds", queryModel.ShopId);
            }

            if (string.IsNullOrWhiteSpace(queryModel.PlatformOrderId) == false)
            {
                sqlCondition.Append(" AND t1.PlatformOrderId IN@PlatformOrderId");
                parameters.Add("PlatformOrderId", queryModel.PlatformOrderId.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
            }

            if (string.IsNullOrWhiteSpace(queryModel.WaybillCode) == false)
            {
                sqlCondition.Append(" AND t1.LastWaybillCode IN@WaybillCode");
                parameters.Add("WaybillCode", queryModel.WaybillCode.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
            }

            if (string.IsNullOrWhiteSpace(queryModel.ExpressCode) == false
                && queryModel.ExpressCode != "0"
                && queryModel.ExpressCode != "-1")
            {
                sqlCondition.Append($" AND t4.LogisticCode = @expressCode ");
                parameters.Add("expressCode", queryModel.ExpressCode);
            }

            //if (queryModel.LogisticStatus != 0)
            //{
            //    sqlCondition.Append($" AND t4.LogisticStatus = @LogisticStatus ");
            //    parameters.Add("LogisticStatus", queryModel.LogisticStatus);
            //}

            if (queryModel.ResidueTime != 0)
            {
                sqlCondition.Append($" AND t1.LastShipTime <= '{DateTime.Now.AddHours(queryModel.ResidueTime).ToString("yyyy-MM-dd HH:ss:mm")}' ");
            }

            if (string.IsNullOrWhiteSpace(queryModel.ProvinceNames) == false
                && queryModel.ProvinceNames.Trim() != "0"
                && queryModel.ProvinceNames.Trim() != "-1")
            {
                sqlCondition.Append(" AND t1.ToProvince IN@ToProvince");
                parameters.Add("ToProvince", queryModel.ProvinceNames.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
            }

            if (string.IsNullOrWhiteSpace(queryModel.BuyerMemberName) == false)
            {
                if(isPdd)
                {
                    sqlCondition.Append(" AND (t1.BuyerWangWang=@BuyerWangWang OR t1.BuyerWangWang like @BuyerWangWangIndex)");
                    parameters.Add("BuyerWangWang", queryModel.BuyerMemberName);
                    var index = PinduoduoPlatformService.GetSearchEncryptString(queryModel.BuyerMemberName,false);
                    if(string.IsNullOrWhiteSpace(index))
                        parameters.Add("BuyerWangWangIndex", queryModel.BuyerMemberName);
                    else
                        parameters.Add("BuyerWangWangIndex", $"%{index}%");
                }
                else
                {
                    sqlCondition.Append(" AND t1.BuyerWangWang=@BuyerWangWang");
                    parameters.Add("BuyerWangWang", queryModel.BuyerMemberName);
                }
            }

            if (string.IsNullOrWhiteSpace(queryModel.Reciver) == false)
            {
                if (isPdd)
                {
                    sqlCondition.Append(" AND (t1.ToName=@ToName OR t1.ToName like @ToNameIndex)");
                    parameters.Add("ToName", queryModel.Reciver);
                    var index = PinduoduoPlatformService.GetSearchEncryptString(queryModel.Reciver, false);
                    if (string.IsNullOrWhiteSpace(index))
                        parameters.Add("ToNameIndex", queryModel.Reciver);
                    else
                        parameters.Add("ToNameIndex", $"%{index}%");
                }
                else
                {
                    sqlCondition.Append(" AND t1.ToName=@ToName");
                    parameters.Add("ToName", queryModel.Reciver);
                }
            }

            if (string.IsNullOrWhiteSpace(queryModel.ReciverPhone) == false)
            {
                if (isPdd)
                {
                    sqlCondition.Append(" AND (t1.ToMobile=@ToMobile OR t1.ToMobile like @ToMobileIndex)");
                    parameters.Add("ToMobile", queryModel.ReciverPhone);
                    var index = PinduoduoPlatformService.GetSearchEncryptString(queryModel.ReciverPhone, true);
                    if (string.IsNullOrWhiteSpace(index))
                        parameters.Add("ToMobileIndex", queryModel.ReciverPhone);
                    else
                        parameters.Add("ToMobileIndex", $"%{index}%");
                }
                else
                {
                    //ToMobile or ToPhone
                    sqlCondition.Append($" AND (t1.ToMobile=@phone OR t1.ToPhone=@phone )");
                    parameters.Add("phone", queryModel.ReciverPhone);
                }
            }

            if (string.IsNullOrWhiteSpace(queryModel.ReciverAddr) == false)
            {
                if (isPdd)
                {
                    sqlCondition.Append(" AND (t1.ToAddress like @ToAddress OR t1.ToAddress like @ToAddressIndex)");
                    parameters.Add("ToAddress", $"'%{queryModel.ReciverAddr}%'");
                    var index = PinduoduoPlatformService.GetSearchEncryptString(queryModel.ReciverAddr, false);
                    if (string.IsNullOrWhiteSpace(index))
                        parameters.Add("ToAddressIndex", $"%{queryModel.ReciverAddr}%");
                    else
                        parameters.Add("ToAddressIndex", $"%{index}%");
                }
                else
                {
                    //ReciverAddr
                    sqlCondition.Append($" AND t1.ToFullAddress like @likeAddr");
                    parameters.Add("likeAddr", $"%{queryModel.ReciverAddr}%");
                }
            }


            var condition = sqlCondition.ToString();

            return condition.TrimEnd(';');
        }

        /// <summary>
        /// 更新物流信息
        /// </summary>
        /// <returns></returns>
        public CustomerApiResult<List<LogisticCodeQueryResponse>> UpdateLogisticTraces(List<PreordainViewModel> list)
        {
            var request = list.Select(f => new LogisticCodeQueryRequest
            {
                ShipperCode = f.ExpressCode,
                LogisticCode = f.ExpressOrderId,
            });
            return orderLogisticInfoService.QueryByLogisticCenter(request.ToList());
        }

        /// <summary>
        /// 获取即将超时订单数量（目前针对拼多多平台）
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="hours"></param>
        /// <returns></returns>
        public int GetSoonTimeOutOrderCount(List<int> shopIds, int hours = 6)
        {
            return _repository.GetSoonTimeOutOrderCount(shopIds, hours);
        }

        public string GetExecSql(PreordainListRequestModel queryModel)
        {
            if (queryModel.ShopId == null || !queryModel.ShopId.Any())
            {
                queryModel.ShopId = SiteContext.Current.SamePlatformShopIds;
            }

            var execSql = _repository.GetExecSql(queryModel);
            return execSql;
        }
    }
}
