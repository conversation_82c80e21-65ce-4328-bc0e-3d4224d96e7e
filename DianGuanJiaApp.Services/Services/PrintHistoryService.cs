using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services.PlatformService;
using System.Data.Common;
using NPOI.SS.UserModel;
using System.Web;
using Polly.Caching;
using System.Diagnostics;

namespace DianGuanJiaApp.Services
{

    public partial class PrintHistoryService : BaseService<Data.Entity.PrintHistory>
    {
        private PrintHistoryRepository _repository = null;
        //private PrintHistoryOrderService _pho_service = new PrintHistoryOrderService();
        //private PrintOrderProductService _pop_service = new PrintOrderProductService();

        /// <summary>
        /// 默认使用旧库
        /// </summary>
        public PrintHistoryService()
        {
            var fxUserId = SiteContext.GetCurrentFxUserId();
            _repository = new PrintHistoryRepository(fxUserId);
        }

        /// <summary>
        /// 默认使用旧库
        /// </summary>
        /// <param name="connectionString"></param>
        public PrintHistoryService(string connectionString) : base(connectionString)
        {
            _repository = new PrintHistoryRepository(connectionString);
            //_pho_service = new PrintHistoryOrderService(connectionString);
            //_pop_service = new PrintOrderProductService(connectionString);
        }

        
        /// <summary>
        /// 新库
        /// </summary>
        /// <param name="fxUserId"></param>
        public PrintHistoryService(int fxUserId): base()
        {
            _repository = new PrintHistoryRepository(fxUserId);
            //_pho_service = new PrintHistoryOrderService();
            //_pop_service = new PrintOrderProductService();
        }

        /// <summary>
        /// 新库
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="fxUserId"></param>
        public PrintHistoryService(string connectionString,int fxUserId) : base(connectionString)
        {
            _repository = new PrintHistoryRepository(connectionString,fxUserId);
            //_pho_service = new PrintHistoryOrderService(connectionString);
            //_pop_service = new PrintOrderProductService(connectionString);
        }


        public List<string> GetToProvinces(List<int> shopIds)
        {
            return _repository.GetToProvinces(shopIds);
        }

        public List<PrintTemplate> GetTemplateNames(List<int> shopIds, int printType, string sDate, string eDate)
        {
            return _repository.GetTemplateNames(shopIds, printType, sDate, eDate);
        }

        /// <summary>
        /// 内部用
        /// </summary>
        /// <param name="orders"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        public int UpdateStatusByOrders(List<int> waybillCodeIds, int status,int shopId)
        {
            return _repository.UpdateStatusByWaybillCodeId(waybillCodeIds, status, shopId);
        }

        public PrintHistory GetNewPrintHistory(List<int> shopIds, string code)
        {
            return null;
            //return _repository.GetNewPrintHistory(shopIds, code);
        }


        /// <summary>
        /// 统计数量
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public Tuple<int, int, int> StatisticsCount(PrintHistoryRequestModel queryModel, bool? isPdd = null)
        {
            DynamicParameters parameters = new DynamicParameters();
            var platformType = SiteContext.Current.CurrentLoginShop.PlatformType;
            //（8月1号前的P_PrintHistoryOrder数据没有，先都根据P_PrintHistory关联）
            //var sql_logicorder_count = "SELECT COUNT(DISTINCT pho.OrderId) FROM P_PrintHistory AS ph LEFT JOIN P_PrintHistoryOrder pho ON ph.ID=pho.PrintHistoryId ";
            var sql_logicorder_count = "SELECT COUNT(DISTINCT ph.PlatformOrderId) FROM P_PrintHistory AS ph LEFT JOIN P_PrintHistoryOrder pho ON ph.ID=pho.PrintHistoryId ";
            //if (string.IsNullOrWhiteSpace(queryModel.PlatformOrderId) == false)
            //{
            //    var temp_sql = " INNER JOIN P_PrintHistoryOrder AS pho ON ph.ID = pho.PrintHistoryId ";
            //    sql_buyer_count += temp_sql;
            //    sql_order_count += temp_sql;
            //}

            var sql_part = GetSqlPart(queryModel, parameters, isPdd);
            var sql_condition = GetWhereCondition("ph", queryModel, parameters, isPdd);

            var sql = new StringBuilder();

            //查询总买家数
            //sql.Append("select count(num) as TotalBuyer from ( ");
            //sql.Append(sql_buyer_count + sql_part + sql_condition);
            //sql.Append(") as tmp1;");
            sql.Append("select 0 as TotalBuyer ;");

            //查询总订单数（8月1号前的P_PrintHistoryOrder数据没有，先都根据P_PrintHistory关联）
            var ptSql = $@"
SELECT COUNT(0) FROM (
	SELECT pho.CustomerOrderId AS PlatformOrderId FROM P_PrintHistory AS ph
	LEFT JOIN P_PrintHistoryOrder pho ON ph.ID=pho.PrintHistoryId  
	{sql_part + sql_condition}
	GROUP BY pho.CustomerOrderId
) AS tmp;";
            //sql.Append("select count(num) as TotalOrder from ( ");
            //sql.Append(sql_order_count + sql_part + sql_condition);
            //sql.Append(") as tmp1;");
            sql.AppendLine(ptSql);

            //查询系统单数
            sql.Append(sql_logicorder_count + sql_condition + ";");

            var counts = _repository.StatisticsCount(sql.ToString(), parameters);

            return new Tuple<int, int, int>(counts.Item1, counts.Item2, counts.Item3);
        }

        public PagedResultModel<PrintHistory> LoadList(PrintHistoryRequestModel queryModel)
        {
            var isPdd = queryModel.IsPdd;
            var strFields = queryModel.StrFields;
            var croborderFields = string.Empty;
            if (queryModel.IsCrossBorderSite)//跨境相关
                croborderFields = ",ph.ToCountry,ph.FromCountry,ph.PackageId,ph.Status";
            if (string.IsNullOrEmpty(strFields))
            {
                strFields = $@"ph.ID,
                               ph.ShopId,
                               ph.WaybillCodeOrderId,
                               ph.PlatformOrderId,
                               ph.PlatformOrderJoin,
                               ph.CustomerOrderId,
                               ph.ExpressWaybillCode,
                               ph.ExpressWaybillCodeChild,
                               ph.BuyerMemberName,
                               ph.Reciver,
                               ph.ReciverPhone,
                               ph.ReciverAddress,
                               ph.ToProvince,
                               ph.ToCity,
                               ph.ToDistrict,
                               ph.Sender,
                               ph.SenderPhone,
	                           ph.TotalWeight,
	                           ph.ProductCount,
	                           ph.TemplateType,
	                           ph.TemplateId,
	                           ph.TemplateName,
                               ph.PrintDate,
                               ph.PrintBatch,
                               ph.BatchIndex,
                               ph.PrintBatchNumber,
                               ph.UserId,
                               ph.SendType
                              {croborderFields}";
            }


            var parameters = new DynamicParameters();
            var sql_data = $@"SELECT {strFields} 
                                        FROM P_PrintHistory ph ";
            var sql_count = "SELECT ph.ID FROM P_PrintHistory AS ph ";

            if (string.IsNullOrWhiteSpace(queryModel.PlatformOrderId) == false)
            {
                var temp_sql = " INNER JOIN P_PrintHistoryOrder AS pho ON ph.ID = pho.PrintHistoryId ";
                sql_data += temp_sql;
                sql_count += temp_sql;
            }

            var sql_part = GetSqlPart(queryModel, parameters, isPdd);
            var sql_condition = GetWhereCondition("ph", queryModel, parameters, isPdd);

            //总数据条数
            var totalCountSql = string.Empty;  
            var sql = string.Empty;
            var mysqlExecSql = string.Empty;

            totalCountSql = "SELECT COUNT(1) FROM ( " + sql_count + sql_part + sql_condition + ") tx; ";
            sql = sql_data + sql_part + sql_condition + string.Format(" ORDER BY ph.{0} {1} OFFSET {2} ROWS FETCH NEXT {3} ROWS ONLY; ", queryModel.OrderByField, queryModel.IsOrderDesc ? "DESC" : "ASC", ((queryModel.PageIndex - 1) * queryModel.PageSize), queryModel.PageSize);
            
            // MySQL延迟回表查询优化：先查ID，再根据ID查完整数据
            var mysqlIdSql = $@"SELECT ph.ID 
                FROM P_PrintHistory ph FORCE INDEX (IX_FxUserId_PrintType_billCode_PlatformOrderId_ShopId_PrintDate)";
            
            // 如果需要关联P_PrintHistoryOrder表，同样添加到ID查询中
            if (string.IsNullOrWhiteSpace(queryModel.PlatformOrderId) == false)
            {
                mysqlIdSql += " INNER JOIN P_PrintHistoryOrder AS pho ON ph.ID = pho.PrintHistoryId ";
            }
            
            mysqlIdSql += sql_part + sql_condition + $" ORDER BY ph.PrintBatch DESC, ph.BatchIndex ASC LIMIT {((queryModel.PageIndex - 1) * queryModel.PageSize)}, {queryModel.PageSize}";
            
            // 延迟回表查询：根据ID获取完整数据
            var mysqlDataSql = $@"{sql_data}
                JOIN ({mysqlIdSql}) AS tmp ON ph.ID = tmp.ID";
            
            mysqlDataSql += " ORDER BY ph.PrintBatch DESC, ph.BatchIndex ASC";
            
            mysqlExecSql = mysqlDataSql.ToLower().Replace("with(nolock)", "");
            if (queryModel.IsExport)
            {
                totalCountSql = "SELECT 0; ";
                mysqlExecSql = totalCountSql + mysqlExecSql;
            }
            else
                mysqlExecSql = totalCountSql.ToLower().Replace("with(nolock)", "") + mysqlExecSql;

            var execSql = totalCountSql + sql;
            var data = _repository.LoadList(execSql, mysqlExecSql, parameters);

            //子母单 子单打印批次赋值 BatchIndex(不包括重打的记录)
            var checkedCode = new HashSet<string>();
            data.Item2.Where(t => t.PrintBatchNumber != "10000").ToList().ForEach(ph =>
            {
                var code = ph.ExpressWaybillCode;
                var index = ph.BatchIndex;
                if (code.IsNotNullOrEmpty() && index > 0 && !checkedCode.Contains(code))
                {
                    var allHistories = data.Item2.FindAll(p => p.ExpressWaybillCode.IsNotNullOrEmpty() && p.ExpressWaybillCode.Equals(code));
                    allHistories.ForEach(p => { p.BatchIndex = index; });
                    checkedCode.Add(code);
                }
                ph.TotalWeight = ph.TotalWeight.ConvertGToKg();
            });
            return new PagedResultModel<PrintHistory>()
            {
                IsOrderDesc = queryModel.IsOrderDesc,
                OrderByField = queryModel.OrderByField,
                PageIndex = queryModel.PageIndex,
                PageSize = queryModel.PageSize,
                Rows = data.Item2,
                Total = data.Item1
            };
        }

        /// <summary>
        /// 是否开启排除迁移路径流
        /// </summary>
        /// <returns></returns>
        private bool IsEnabledExcludeMigratePathFlow()
        {
            return RedisHelper.Get<bool>("DianGuanJia:FenXiao:PrintHistory:ExcludeMigratePathFlow:Switch");
        }

        private string GetSqlPart(PrintHistoryRequestModel queryModel, DynamicParameters parameters, bool? isPdd)
        {
            var temp_sql = string.Empty;
            var partSql = string.Empty;
            if (string.IsNullOrWhiteSpace(queryModel.PlatformOrderId) == false)
            {
                temp_sql = " INNER JOIN P_PrintHistoryOrder AS t2 With(NoLock) ON t1.ID = t2.PrintHistoryId ";
            }

            //精选区排除迁移
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                if (IsEnabledExcludeMigratePathFlow())
                {
                    var leftSql =
                        " LEFT JOIN P_PrintHistory_MigratePathFlow phmp ON t1.PathFlowCode = phmp.PathFlowCode";
                    temp_sql += leftSql;
                }
            }

            if (queryModel.PrintCount > 0)
            {
                var sqlCondition = GetWhereCondition("t1", queryModel, parameters, isPdd);
                switch (queryModel.PrintCountCondition)
                {
                    case 0: //等于
                        partSql = string.Format(" INNER JOIN (SELECT t1.PlatformOrderId from P_PrintHistory as t1 With(NoLock) {2} {1} group by t1.PlatformOrderId having COUNT(t1.ID) = {0}) as t3 on t3.PlatformOrderId = ph.PlatformOrderId ",
                            queryModel.PrintCount.ToString(),
                            sqlCondition,
                            temp_sql);
                        break;
                    case 1: //大于
                        partSql = string.Format(" INNER JOIN (SELECT t1.PlatformOrderId from P_PrintHistory as t1 With(NoLock) {2} {1} group by t1.PlatformOrderId having COUNT(t1.ID) > {0}) as t3 on t3.PlatformOrderId = ph.PlatformOrderId ",
                            queryModel.PrintCount.ToString(),
                            sqlCondition,
                            temp_sql);
                        break;
                    case 2: //小于
                        partSql = string.Format(" INNER JOIN (SELECT t1.PlatformOrderId from P_PrintHistory as t1 With(NoLock) {2} {1} group by t1.PlatformOrderId having COUNT(t1.ID) < {0}) as t3 on t3.PlatformOrderId = ph.PlatformOrderId ",
                            queryModel.PrintCount.ToString(),
                            sqlCondition,
                            temp_sql);
                        break;
                }
            }
            //精选区排除迁移
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                if (IsEnabledExcludeMigratePathFlow())
                {
                    var leftSql =
                        " LEFT JOIN P_PrintHistory_MigratePathFlow phmp ON ph.PathFlowCode = phmp.PathFlowCode";
                    partSql += leftSql;
                }
            }

            return partSql;
        }

        private string GetWhereCondition(string tableAsName, PrintHistoryRequestModel queryModel,
            DynamicParameters parameters, bool? isPdd)
        {
            var pt = CustomerConfig.CloudPlatformType;
            //拼多多加密处理
            //if (isPdd == null)
            //    isPdd = SiteContext.Current.CurrentLoginShop.PlatformType == Data.Enum.PlatformType.Pinduoduo.ToString();
            //拼多多没有买家旺旺，用的是收件人姓名
            if (pt == PlatformType.Pinduoduo.ToString())
            {
                if (string.IsNullOrEmpty(queryModel.Reciver) == false)
                    queryModel.BuyerMemberName = "";
                if (string.IsNullOrEmpty(queryModel.BuyerMemberName) == false &&
                    string.IsNullOrEmpty(queryModel.Reciver))
                {
                    queryModel.Reciver = queryModel.BuyerMemberName;
                    queryModel.BuyerMemberName = "";
                }
            }

            var sqlCondition = new StringBuilder();

            var isFxUser = string.IsNullOrEmpty(Convert.ToString(queryModel.FxUserId)) == false &&
                           queryModel.FxUserId > 0;
            if (isFxUser) //订单分发用FxUserId查询，不用ShopId
            {
                sqlCondition.AppendFormat(" AND {0}.FxUserId=@{0}_FxUserId", tableAsName);
                parameters.Add(string.Format("{0}_FxUserId", tableAsName), queryModel.FxUserId);
            }

            if (queryModel.ShopId != null && queryModel.ShopId.Count > 0)
            {
                var shopIds = queryModel.ShopId.Where(f => f != 0).ToList();
                sqlCondition.AppendFormat(" AND {0}.ShopId IN@{0}_ShopIds", tableAsName);
                parameters.Add(string.Format("{0}_ShopIds", tableAsName), queryModel.ShopId);
            }
            else if (!isFxUser)
            {
                sqlCondition.AppendFormat(" AND {0}.ShopId =@{0}_ShopIds", tableAsName);
                parameters.Add(string.Format("{0}_ShopIds", tableAsName), SiteContext.Current.CurrentShopId);
            }

            if (queryModel.PrintType != 0)
            {
                sqlCondition.AppendFormat(" AND {0}.PrintType=@{0}_PrintType", tableAsName);
                parameters.Add(string.Format("{0}_PrintType", tableAsName), queryModel.PrintType);
            }

            //发货状态 查询二次打印未发货的
            if(queryModel.SendStatus.HasValue && queryModel.SendStatus == -1)
            {
                sqlCondition.AppendFormat(" AND {0}.SendStatus = 0", tableAsName);
            }

            //发货类型
            if (queryModel.SendType.HasValue)
            {
                if (queryModel.SendType.Value == 200)
                {
                    //所有二次发货
                    sqlCondition.AppendFormat(" AND {0}.SendType>=1", tableAsName);
                }
                else
                {
                    sqlCondition.AppendFormat(" AND {0}.SendType=@{0}_SendType", tableAsName);
                    parameters.Add(string.Format("{0}_SendType", tableAsName), queryModel.SendType.Value);
                }
            }

            //是否隐藏
            if (queryModel.IsHide.HasValue)
            {
                sqlCondition.AppendFormat(" AND {0}.IsHide=@{0}_IsHide", tableAsName);
                parameters.Add(string.Format("{0}_IsHide", tableAsName), queryModel.IsHide.Value);
            }

            var sTime = queryModel.StartDate.ToDateTime();
            var eTime = queryModel.EndDate.ToDateTime();
            var s_e_span = eTime - sTime;
            if (sTime == null || eTime == null)
            {
                if (queryModel.Ids == null || queryModel.Ids.Any() == false)
                {
                    queryModel.StartDate = DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd");
                    queryModel.EndDate = DateTime.Now.ToString("yyyy-MM-dd 23:59:59");
                    //queryModel.StartDate = "2022-06-01";
                    //queryModel.EndDate = "2022-08-01";
                }
            }
            else if (s_e_span != null && s_e_span.Value.TotalDays > 60)
            {
                throw new LogicException($"查询范围只支持60天内，请重新设置范围.");
            }

            if (string.IsNullOrWhiteSpace(queryModel.StartDate) == false)
            {
                sqlCondition.AppendFormat(" AND {0}.PrintDate>=@{0}_PrintDate_Start", tableAsName);
                parameters.Add(string.Format("{0}_PrintDate_Start", tableAsName), queryModel.StartDate);
            }

            if (string.IsNullOrWhiteSpace(queryModel.EndDate) == false)
            {
                sqlCondition.AppendFormat(" AND {0}.PrintDate<=@{0}_PrintDate_End", tableAsName);
                parameters.Add(string.Format("{0}_PrintDate_End", tableAsName), queryModel.EndDate);
            }

            if (queryModel.PrintDataType != 0)
            {
                sqlCondition.AppendFormat(" AND {0}.PrintDataType=@{0}_PrintDataType", tableAsName);
                parameters.Add(string.Format("{0}_PrintDataType", tableAsName), queryModel.PrintDataType);
            }

            if (string.IsNullOrWhiteSpace(queryModel.TemplateName) == false && queryModel.TemplateName != 0.ToString())
            {
                sqlCondition.AppendFormat(" AND {0}.TemplateId=@{0}_TemplateId", tableAsName);
                if (int.TryParse(queryModel.TemplateName, out var templateId))
                    parameters.Add(string.Format("{0}_TemplateId", tableAsName), templateId);
                else
                    parameters.Add(string.Format("{0}_TemplateId", tableAsName), queryModel.TemplateName);
            }

            if (string.IsNullOrWhiteSpace(queryModel.ToProvince) == false && queryModel.ToProvince != 0.ToString())
            {
                sqlCondition.AppendFormat(" AND {0}.ToProvince like @{0}_ToProvince", tableAsName);
                parameters.Add(string.Format("{0}_ToProvince", tableAsName), $"{queryModel.ToProvince}%");
            }

            if (string.IsNullOrWhiteSpace(queryModel.ToCountry) == false && queryModel.ToCountry != 0.ToString())
            {
                sqlCondition.AppendFormat(" AND {0}.ToCountry IN @{0}_ToCountry", tableAsName);
                parameters.Add($"{tableAsName}_ToCountry", queryModel.ToCountry.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
            }

            if (string.IsNullOrWhiteSpace(queryModel.ReciverPhone) == false)
            {
                sqlCondition.AppendFormat(" AND {0}.ReciverPhone = @{0}_ReciverPhone", tableAsName);
                parameters.Add(string.Format("{0}_ReciverPhone", tableAsName), $"{queryModel.ReciverPhone}");
            }

            #region 收件人信息

            //打印/底单/发货记录，不再保存密文，只存打码电话 2022.6.22
            //GetReceiveConditionV2(tableAsName, sqlCondition, queryModel, parameters);

            #endregion

            if (string.IsNullOrWhiteSpace(queryModel.PlatformOrderId) == false)
            {
                var platformOrderId = queryModel.PlatformOrderId.TrimEnd(',').TrimStart(',');
                if (platformOrderId.IndexOf(",") != -1)
                {
                    sqlCondition.AppendFormat(" AND ({1}.OrderId IN@{0}_PlatformOrderId )", tableAsName,
                        tableAsName == "ph" ? "pho" : "t2");
                    parameters.Add(string.Format("{0}_PlatformOrderId", tableAsName),
                        platformOrderId.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
                }
                else
                {
                    sqlCondition.AppendFormat(" AND ({1}.OrderId =@{0}_PlatformOrderId )", tableAsName,
                        tableAsName == "ph" ? "pho" : "t2");
                    parameters.Add(string.Format("{0}_PlatformOrderId", tableAsName), platformOrderId);
                }
            }

            if (string.IsNullOrWhiteSpace(queryModel.CustomerOrderId) == false)
            {
                var customerOrderId = queryModel.CustomerOrderId.TrimEnd(',').TrimStart(',');
                if (customerOrderId.IndexOf(",") != -1)
                {
                    sqlCondition.AppendFormat(" AND ({0}.CustomerOrderId IN@{0}_CustomerOrderId)", tableAsName);
                    parameters.Add(string.Format("{0}_CustomerOrderId", tableAsName),
                        customerOrderId.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
                }
                else
                {
                    sqlCondition.AppendFormat(" AND ({0}.CustomerOrderId =@{0}_CustomerOrderId)", tableAsName);
                    parameters.Add(string.Format("{0}_CustomerOrderId", tableAsName),
                        customerOrderId.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
                }
            }

            if (string.IsNullOrWhiteSpace(queryModel.ExpressWaybillCode) == false)
            {
                var expressWaybillCode = queryModel.ExpressWaybillCode.TrimEnd(',').TrimStart(',');
                if (expressWaybillCode.IndexOf(",") != -1)
                {
                    sqlCondition.AppendFormat(" AND {0}.ExpressWaybillCode IN @{0}_ExpressWaybillCode", tableAsName);
                    parameters.Add(string.Format("{0}_ExpressWaybillCode", tableAsName),
                        expressWaybillCode.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
                }
                else
                {
                    sqlCondition.AppendFormat(" AND {0}.ExpressWaybillCode=@{0}_ExpressWaybillCode", tableAsName);
                    parameters.Add(string.Format("{0}_ExpressWaybillCode", tableAsName), expressWaybillCode);
                }
            }

            if (string.IsNullOrWhiteSpace(queryModel.BuyerMemberName) == false)
            {
                sqlCondition.AppendFormat(" AND {0}.BuyerMemberName=@{0}_BuyerMemberName", tableAsName);
                parameters.Add(string.Format("{0}_BuyerMemberName", tableAsName), queryModel.BuyerMemberName);
            }

            if (string.IsNullOrWhiteSpace(queryModel.SenderName) == false)
            {
                sqlCondition.AppendFormat(" AND {0}.Sender=@{0}_Sender", tableAsName);
                parameters.Add(string.Format("{0}_Sender", tableAsName), queryModel.SenderName);
            }

            if (string.IsNullOrWhiteSpace(queryModel.PrintBatch) == false)
            {
                sqlCondition.AppendFormat(" AND {0}.PrintBatch=@{0}_PrintBatch", tableAsName);
                parameters.Add(string.Format("{0}_PrintBatch", tableAsName), queryModel.PrintBatch);
            }

            if (queryModel.Ids != null && queryModel.Ids.Any())
            {
                sqlCondition.AppendFormat(" AND {0}.Id IN@{0}_Ids", tableAsName);
                parameters.Add(string.Format("{0}_Ids", tableAsName), queryModel.Ids);
            }

            //精选区排除迁移
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                if (IsEnabledExcludeMigratePathFlow())
                {
                    sqlCondition.Append(" AND phmp.PathFlowCode IS NULL");
                }
            }

            if (sqlCondition.Length > 0)
            {
                return " WHERE 1=1 " + sqlCondition.ToString();
            }

            return string.Empty;
        }


        private void GetReceiveCondition(string tableAsName, StringBuilder sqlCondition, PrintHistoryRequestModel queryModel, DynamicParameters parameters)
        {
            if (queryModel.ReciverPhone.IsNullOrEmpty())
                return;

            var platformType = CustomerConfig.CloudPlatformType;
            if (platformType == PlatformType.Alibaba.ToString())
            {
                // 1.获取当前登录用户路劲流上的店铺
                var _pathFlowService = new PathFlowService();
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var pathFlowList = _pathFlowService.GetPathFlowByFxUserId(fxUserId);
                var selectSids = pathFlowList.Where(f => f.SourceShopId > 0).Select(f => f.SourceShopId).Distinct().ToList();
                //通过店铺id查询店铺
                var shopService = new ShopService();
                var shops = shopService.GetShopsAndShopExtension(selectSids);

                //如果没有店铺id和平台，则说明用户没有需要处理的订单
                if (selectSids.Any() == false)
                {
                    sqlCondition.Append(" AND 1!=1");
                    return;
                }

                var receiverSqlCondition = new StringBuilder();
                var shopGroups = shops.GroupBy(x => x.PlatformType).ToList();
                foreach (var item in shopGroups)
                {
                    var pt = item.Key;
                    var gshops = item.ToList();
                    if (pt == PlatformType.TouTiao.ToString() || pt == PlatformType.TouTiaoSaleShop.ToString())
                    {
                        // 抖音加密查询
                        var index = PlatformService.ZhiDianNewPlatformService.GetSearchIndex(queryModel.ReciverPhone, DouyinEncryptType.MobiePhone);
                        if (index.IsNotNullOrEmpty())
                        {
                            receiverSqlCondition.Append($" OR {tableAsName}.ReciverPhone like @{tableAsName}_zdPhoneIndex");
                            parameters.Add($"{tableAsName}_zdPhoneIndex", $"%{index}%");
                        }
                    }
                    else if (pt == PlatformType.YouZan.ToString())
                    {
                        foreach (var s in gshops)
                        {
                            try
                            {
                                var youzanPtService = new YouZanPlatformService(gshops.FirstOrDefault());
                                var index = youzanPtService.EncryptSingle(queryModel.ReciverPhone);
                                if (index.IsNotNullOrEmpty())
                                {
                                    receiverSqlCondition.Append($" OR {tableAsName}.ReciverPhone like @{tableAsName}_yzPhoneIndex");
                                    parameters.Add($"{tableAsName}_yzPhoneIndex", $"%{index}%");
                                }
                                break;
                            }
                            catch (Exception)
                            {

                            }
                        }
                    }
                    else if (pt == PlatformType.Alibaba.ToString() || pt == PlatformType.AlibabaC2M.ToString() || pt == PlatformType.Taobao.ToString() || pt == PlatformType.KuaiShou.ToString() || pt == PlatformType.TaobaoMaiCaiV2.ToString())
                    {
                        //  178****1559  alibaba
                        //  *******6936  taobao
                        //  13*******59  alibabac2m
                        var phone = queryModel.ReciverPhone;
                        if (phone.Length == 11)
                        {
                            if (pt == PlatformType.Alibaba.ToString() || pt == PlatformType.KuaiShou.ToString())
                                phone = phone.Substring(0, 3) + "****" + phone.Substring(7, 4);
                            else if (pt == PlatformType.Taobao.ToString())
                                phone = "*******" + phone.Substring(7, 4);
                            else if (pt == PlatformType.AlibabaC2M.ToString() || pt == PlatformType.TaobaoMaiCaiV2.ToString())
                                phone = phone.Substring(0, 2) + "*******" + phone.Substring(9, 2);
                        }
                        receiverSqlCondition.Append($" OR {tableAsName}.ReciverPhone=@{tableAsName}_{pt}PhoneIndex");
                        parameters.Add($"{tableAsName}_{pt}PhoneIndex", phone);

                    }
                }

                if (receiverSqlCondition.IsNullOrEmpty())
                {
                    sqlCondition.Append($" AND {tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone");
                    parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                }
                else
                {
                    var tmpSqlCondition = receiverSqlCondition.ToString().Trim().TrimEnd("OR").TrimStart("OR");
                    sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tmpSqlCondition})");
                    parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                }

            }
            else if (platformType == PlatformType.Pinduoduo.ToString())
            {
                sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tableAsName}.ReciverPhone like @{tableAsName}_pddPhoneIndex)");
                parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                var index = PlatformService.PinduoduoPlatformService.GetSearchEncryptString(queryModel.ReciverPhone, true);
                if (string.IsNullOrWhiteSpace(index))
                    parameters.Add($"{tableAsName}_pddPhoneIndex", $"%{queryModel.ReciverPhone}%");
                else
                    parameters.Add($"{tableAsName}_pddPhoneIndex", $"%{index}%");
            }
            else if (platformType == PlatformType.Jingdong.ToString())
            {
                var phone = queryModel.ReciverPhone;
                if (phone.Length == 11)
                    phone = phone.Substring(0, 3) + "********" + phone.Substring(10, 1);
                sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tableAsName}.ReciverPhone=@{tableAsName}_jdPhoneIndex)");
                parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                parameters.Add($"{tableAsName}_jdPhoneIndex", phone);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tableAsName"></param>
        /// <param name="sqlCondition"></param>
        /// <param name="queryModel"></param>
        /// <param name="parameters"></param>
        private void GetReceiveConditionV2(string tableAsName, StringBuilder sqlCondition, PrintHistoryRequestModel queryModel, DynamicParameters parameters)
        {
            if (queryModel.ReciverPhone.IsNullOrEmpty())
                return;

            var platformType = CustomerConfig.CloudPlatformType;
            if (platformType == PlatformType.Alibaba.ToString())
            {

                var phone = queryModel.ReciverPhone;

                #region 兼容密文搜索，正式上线一段时间后可移除密文搜索兼容

                var receiverSqlCondition = new StringBuilder();

                // 1.获取当前登录用户路劲流上的店铺
                var _pathFlowService = new PathFlowService();
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var pathFlowList = _pathFlowService.GetPathFlowByFxUserId(fxUserId);
                var selectSids = pathFlowList.Where(f => f.SourceShopId > 0).Select(f => f.SourceShopId).Distinct().ToList();
                //通过店铺id查询店铺
                var shopService = new ShopService();
                var shops = shopService.GetShopsAndShopExtension(selectSids);

                //如果没有店铺id和平台，则说明用户没有需要处理的订单
                if (selectSids.Any() == false)
                {
                    sqlCondition.Append(" AND 1!=1");
                    return;
                }

                var shopGroups = shops.GroupBy(x => x.PlatformType).ToList();
                foreach (var item in shopGroups)
                {
                    var pt = item.Key;
                    var gshops = item.ToList();
                    if (pt == PlatformType.TouTiao.ToString() || pt == PlatformType.TouTiaoSaleShop.ToString())
                    {
                        // 抖音加密查询
                        var index = PlatformService.ZhiDianNewPlatformService.GetSearchIndex(queryModel.ReciverPhone, DouyinEncryptType.MobiePhone);
                        if (index.IsNotNullOrEmpty())
                        {
                            receiverSqlCondition.Append($" OR {tableAsName}.ReciverPhone like @{tableAsName}_zdPhoneIndex");
                            parameters.Add($"{tableAsName}_zdPhoneIndex", $"${index}%");
                        }
                    }
                    else if (pt == PlatformType.YouZan.ToString())
                    {
                        foreach (var s in gshops)
                        {
                            try
                            {
                                var youzanPtService = new YouZanPlatformService(gshops.FirstOrDefault());
                                var index = youzanPtService.EncryptSingle(queryModel.ReciverPhone);
                                if (index.IsNotNullOrEmpty())
                                {
                                    receiverSqlCondition.Append($" OR {tableAsName}.ReciverPhone like @{tableAsName}_yzPhoneIndex");
                                    parameters.Add($"{tableAsName}_yzPhoneIndex", $"${index}%");
                                }
                                break;
                            }
                            catch (Exception)
                            {

                            }
                        }
                    }
                }

                #endregion

                if (receiverSqlCondition.IsNullOrEmpty())
                {
                    //  178****1559  alibaba,kuaishou
                    //  *******6936  taobao
                    //  13*******59  alibabac2m,toutiao,pingduoduo
                    if (phone.Length == 11)
                    {
                        var phone1 = phone.Substring(0, 3) + "****" + phone.Substring(7, 4);
                        var phone2 = "*******" + phone.Substring(7, 4);
                        var phone3 = phone.Substring(0, 2) + "*******" + phone.Substring(9, 2);

                        var phoneList = new List<string> { phone, phone1, phone2, phone3 };

                        sqlCondition.Append($" AND {tableAsName}.ReciverPhone IN @{tableAsName}_ReciverPhone ");
                        parameters.Add($"{tableAsName}_ReciverPhone", phoneList);
                    }
                    else
                    {
                        sqlCondition.Append($" AND {tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone");
                        parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                    }
                }
                else
                {
                    //兼容密文搜索，正式上线一段时间后可移除密文搜索兼容

                    var tmpSqlCondition = receiverSqlCondition.ToString().Trim().TrimEnd("OR").TrimStart("OR");

                    //  178****1559  alibaba,kuaishou
                    //  *******6936  taobao
                    //  13*******59  alibabac2m,toutiao,pingduoduo
                    if (phone.Length == 11)
                    {
                        var phone1 = phone.Substring(0, 3) + "****" + phone.Substring(7, 4);
                        var phone2 = "*******" + phone.Substring(7, 4);
                        var phone3 = phone.Substring(0, 2) + "*******" + phone.Substring(9, 2);

                        var phoneList = new List<string> { phone, phone1, phone2, phone3 };

                        sqlCondition.Append($" AND ({tableAsName}.ReciverPhone IN @{tableAsName}_ReciverPhone OR {tmpSqlCondition})");
                        parameters.Add($"{tableAsName}_ReciverPhone", phoneList);
                    }
                    else
                    {
                        sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tmpSqlCondition})");
                        parameters.Add($"{tableAsName}_ReciverPhone", phone);
                    }

                }


            }
            else if (platformType == PlatformType.Pinduoduo.ToString())
            {
                //sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tableAsName}.ReciverPhone like @{tableAsName}_pddPhoneIndex)");
                //parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                //var index = PlatformService.PinduoduoPlatformService.GetSearchEncryptString(queryModel.ReciverPhone, true);
                //if (string.IsNullOrWhiteSpace(index))
                //    parameters.Add($"{tableAsName}_pddPhoneIndex", $"%{queryModel.ReciverPhone}%");
                //else
                //    parameters.Add($"{tableAsName}_pddPhoneIndex", $"%{index}%");

                sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tableAsName}.ReciverPhone like @{tableAsName}_pddPhoneIndex)");

                var phone = queryModel.ReciverPhone;
                var index = PlatformService.PinduoduoPlatformService.GetSearchEncryptString(phone, true);
                if (string.IsNullOrWhiteSpace(index))
                    parameters.Add($"{tableAsName}_pddPhoneIndex", $"%{phone}%");
                else
                    parameters.Add($"{tableAsName}_pddPhoneIndex", $"%{index}%");

                if (phone.Length == 11)
                {
                    var phone3 = phone.Substring(0, 2) + "*******" + phone.Substring(9, 2);
                    //var phoneList = new List<string> { phone, phone3 };
                    parameters.Add($"{tableAsName}_ReciverPhone", phone3);
                }
                else
                {
                    parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                }
            }
            else if (platformType == PlatformType.Jingdong.ToString())
            {
                var phone = queryModel.ReciverPhone;
                if (phone.Length == 11)
                    phone = phone.Substring(0, 3) + "********" + phone.Substring(10, 1);
                sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tableAsName}.ReciverPhone=@{tableAsName}_jdPhoneIndex)");
                parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                parameters.Add($"{tableAsName}_jdPhoneIndex", phone);
            }
        }


        /// <summary>
        /// 为了适应多多云迁移，改为按底单查询，之前是底单id查询
        /// </summary>
        /// <param name="shopIds"></param>
        /// <param name="waybillCodeOrderId"></param>
        /// <param name="childWaybillCode"></param>
        /// <returns></returns>
        public List<PrintHistory> GetPrintHistoryByWaybillCodeId(List<int> shopIds, int waybillCodeOrderId, string childWaybillCode)
        {
            string sql = $" WHERE WaybillCodeOrderId={waybillCodeOrderId} ";
            DynamicParameters dps = new DynamicParameters();
            if (string.IsNullOrWhiteSpace(childWaybillCode) == false)
            {
                sql += $" AND ExpressWaybillCodeChild=@ExpressWaybillCodeChild ";
                dps.Add("ExpressWaybillCodeChild", childWaybillCode);
            }
            if (shopIds != null && shopIds.Any())
                sql += $" AND ShopId IN({string.Join(",", shopIds)})";


            var dengerous = new List<string> { "truncate", "delete", "insert", "alter", "update", "drop" };
            dengerous.ForEach(f =>
            {
                if (sql.ToLower().Contains(f))
                {
                    throw new LogicException("请不要恶意操作");
                }
            });


            return _repository.GetPrintHistoryByWaybillCodeId(sql, dps);
        }

        /// <summary>
        /// 获取打印历史信息（异常订单使用）
        /// </summary>
        /// <param name="shopIds"></param>
        /// <param name="waybillCodeIds"></param>
        /// <returns></returns>
        public List<PrintHistory> GetPrintHistoriesForAbnormalOrder(List<int> shopIds, List<int> waybillCodeIds)
        {
            return _repository.GetPrintHistoriesForAbnormalOrder(shopIds, waybillCodeIds);
        }
        /// <summary>
        /// 获取打印历史信息（异常订单使用），性能问题不再使用
        /// </summary>
        /// <param name="logicOrderId"></param>
        /// <param name="pathFlowCode"></param>
        /// <returns></returns>
        public List<PrintHistory> GetPrintHistoriesForAbnormalOrder(string logicOrderId, string pathFlowCode)
        {
            return _repository.GetPrintHistoriesForAbnormalOrder(logicOrderId, pathFlowCode);
        }
        /// <summary>
        /// 获取运单的打印记录
        /// </summary>
        /// <param name="waybillCodeOrderId"></param>
        /// <returns></returns>
        public List<PrintHistory> GetPrintHistoryByWaybillCodeId(List<int> shopIds, string orderId, string waybillCode, string childWaybillCode)
        {
            var result = LoadPrintHistoryList(new List<GetPrintHistListRequestModel> {
                new GetPrintHistListRequestModel(){
                    OrderId=orderId,
                    WaybillCode=waybillCode,
                    ChildWaybillCode=childWaybillCode
                }
            }, shopIds, 30); //一个底单最多获取30个打印记录，再多去 打印记录里查
            return result;
        }

        /// <summary>
        /// 获取运单的打印记录(批量）
        /// </summary>
        /// <param name="waybillCodeIdAndChildWaybillCodeList"></param>
        /// <returns></returns>
        public List<PrintHistory> LoadPrintHistoryList(List<GetPrintHistListRequestModel> waybillCodeIdAndChildWaybillCodeList, List<int> shopIds, int lastRowCount = 2)
        {
            if (waybillCodeIdAndChildWaybillCodeList == null || waybillCodeIdAndChildWaybillCodeList.Any() == false)
                throw new LogicException("参数错误");

            var waybillCodeList = waybillCodeIdAndChildWaybillCodeList.Where(f => string.IsNullOrWhiteSpace(f.WaybillCode) == false).Select(f => f.WaybillCode);
            var childWaybillCodeList = waybillCodeIdAndChildWaybillCodeList.Where(f => string.IsNullOrWhiteSpace(f.ChildWaybillCode) == false).Select(f => f.ChildWaybillCode);
            var orderIds = waybillCodeIdAndChildWaybillCodeList.Where(f => string.IsNullOrWhiteSpace(f.OrderId) == false).Select(f => f.OrderId);

            if (waybillCodeList.Any() == false)
            {
                throw new LogicException("运单号为空");
            }
            if (orderIds.Any() == false)
            {
                throw new LogicException("订单编号为空");
            }

            string sql = @"SELECT t1.id FROM P_PrintHistory AS t1 WHERE t1.ShopId IN @sids AND t1.PlatformOrderId IN @oid AND t1.ExpressWaybillCode IN @wyc";

            if (childWaybillCodeList.Any(f => string.IsNullOrWhiteSpace(f) == false))
            {
                sql += $" AND t1.ExpressWaybillCodeChild IN @childwyc";
            }

            sql = $@"select * from P_PrintHistory where id in ( {sql} )";

            var phs = _repository.DbConnection.Query<PrintHistory>(sql, new
            {
                wyc = waybillCodeList,
                oid = orderIds,
                sids = shopIds,
                childwyc = childWaybillCodeList
            });
            //分组取
            if (lastRowCount <= 2)
                lastRowCount = 2;
            var rst = phs.GroupBy(x => x.ExpressWaybillCode).SelectMany(y => y.OrderByDescending(x => x.ID).Take(lastRowCount - 1)).ToList();
            return rst;
        }

        /// <summary>
        /// 获取运单的打印记录
        /// </summary>
        /// <param name="waybillCodeOrderId"></param>
        /// <returns></returns>
        public List<PrintHistory> GetPrintHistoryByWaybillCodeId(int fxUserId, string orderId, string waybillCode, string childWaybillCode)
        {
            var result = LoadPrintHistoryList(new List<GetPrintHistListRequestModel> {
                new GetPrintHistListRequestModel(){
                    OrderId=orderId,
                    WaybillCode=waybillCode,
                    ChildWaybillCode=childWaybillCode
                }
            }, fxUserId, 30); //一个底单最多获取30个打印记录，再多去 打印记录里查

            // 获取订单上下游关系
            var logicOrderRepository = new LogicOrderRepository();
            var pathFlowRepository = new PathFlowRepository();
            var pathFlowNodes = new List<PathFlowNode>();
            var pathFlowNodeDic = new Dictionary<string, List<PathFlowNode>>();
            var pathFlowCodes = result.Where(x => x.PathFlowCode.IsNotNullOrEmpty()).GroupBy(x => x.PathFlowCode).Select(x => x.Key).ToList();
            if (pathFlowCodes.Any())
            {
                pathFlowNodes = pathFlowRepository.GetPathFlowNodeList(pathFlowCodes);
                pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(pathFlowNodes);
            }
            var commonSettingRep = new CommonSettingRepository();
            foreach (var item in result)
            {
                var pathFlowNode = pathFlowNodes.FirstOrDefault(a => a.FxUserId == fxUserId && a.PathFlowCode == item.PathFlowCode);
                if (pathFlowNode != null && pathFlowNode.UpFxUserId == 0)
                {
                    //自己的订单显示销售价
                    item.IsSelf = true;
                    item.IsShowSalePrice = true;
                }
                if (!item.IsSelf)
                    item.IsShowSalePrice = commonSettingRep.SetIsShowSalePrice(fxUserId, item.PathFlowCode, pathFlowNodeDic);
            }
            return result;
        }


        /// <summary>
        /// 订单分发2.0 获取运单的打印记录(批量）
        /// </summary>
        /// <returns></returns>
        public List<PrintHistory> LoadPrintHistoryList(List<GetPrintHistListRequestModel> waybillCodeIdAndChildWaybillCodeList, int fxUserId, int lastRowCount = 2)
        {
            if (waybillCodeIdAndChildWaybillCodeList == null || waybillCodeIdAndChildWaybillCodeList.Any() == false)
                throw new LogicException("参数错误");

            var waybillCodeList = waybillCodeIdAndChildWaybillCodeList.Where(f => string.IsNullOrWhiteSpace(f.WaybillCode) == false).Select(f => f.WaybillCode);
            var childWaybillCodeList = waybillCodeIdAndChildWaybillCodeList.Where(f => string.IsNullOrWhiteSpace(f.ChildWaybillCode) == false).Select(f => f.ChildWaybillCode);
            var orderIds = waybillCodeIdAndChildWaybillCodeList.Where(f => string.IsNullOrWhiteSpace(f.OrderId) == false).Select(f => f.OrderId);

            if (waybillCodeList.Any() == false)
            {
                throw new LogicException("运单号为空");
            }
            if (orderIds.Any() == false)
            {
                throw new LogicException("订单编号为空");
            }

            string sql = @"SELECT t1.id FROM P_PrintHistory AS t1 WHERE t1.FxUserId = @uid AND t1.PlatformOrderId IN @oid AND t1.ExpressWaybillCode IN @wyc";

            if (childWaybillCodeList.Any(f => string.IsNullOrWhiteSpace(f) == false))
            {
                sql += $" AND t1.ExpressWaybillCodeChild IN @childwyc";
            }

            sql = $@"select * from P_PrintHistory where id in ({sql})";

            var phs = _repository.DbConnection.Query<PrintHistory>(sql, new
            {
                wyc = waybillCodeList,
                oid = orderIds,
                uid = fxUserId,
                childwyc = childWaybillCodeList
            }).ToList();
            //分组取
            if (lastRowCount <= 2)
                lastRowCount = 2;
            var rst = phs.GroupBy(x => x.ExpressWaybillCode).SelectMany(y => y.OrderByDescending(x => x.ID).Take(lastRowCount - 1)).ToList();

            SetPrintHistoryData(rst);

            return rst;
        }


        public void SetPrintHistoryData(List<PrintHistory> phs)
        {
            new PrintHistoryDataRepository().SetHistoryPrintData(phs);
        }

        public void GetAllPrintHistoryDataMaxIds()
        {
            new PrintHistoryDataRepository().GetAllPrintHistoryDataMaxIds();
        }

        public void CreatePrintHistoryDataNewTable()
        {
            new PrintHistoryDataRepository().CreatePrintHistoryDataNewTable();
        }

        /// <summary>
        /// 保存打印记录
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public bool SavelPrintHistory(PrintHistory model)
        {
            return _repository.SavelPrintHistory(model);
        }


        private bool IsMultipleQuery(string queryValue)
        {
            var tempValue = queryValue.TrimEnd(',').TrimStart(',');
            if (tempValue.IndexOf(",") != -1)
            {
                return true;
            }
            return false;
        }

        public void Add(List<PrintHistory> historys, bool fromMigrate = false)
        {
            if (!fromMigrate)
            {
                var needEncryptHistorys = historys.Where(x => x.ReciverPhone?.Contains("*") == false && x.ReciverPhone?.Length < 20 || x.ReciverPhone?.StartsWith("$") == false)?.ToList();
                if (needEncryptHistorys?.Any() == true)
                    FxPlatformEncryptService.EncryptPrintHistory(needEncryptHistorys, encryptSender: true);
            }
            _repository.Add(historys);

            //20230925：注释以下副本日志动作，打印记录使用的是同一个mysql库，不需要副本动作
            //#region 数据变更日志 
            //if (historys != null && historys.Any() && !fromMigrate)
            //{
            //    ////当前用户的系统店铺Id
            //    //var systemShopId = SiteContext.Current?.CurrentFxUserId ?? 0;
            //    List<DataChangeLog> dcLogs = historys.Select(o => new DataChangeLog
            //    {
            //        DataChangeType = DataChangeTypeEnum.INSERT,
            //        TableTypeName = DataChangeTableTypeName.PrintHistory,
            //        SourceShopId = o.SourceShopId,//用户维度的存当前用户的系统店铺Id，2023-05-06改为源店铺ShopId
            //        SourceFxUserId = o.UserId,//打印人的用户Id，2023-05-06改为源商家Id
            //        RelationKey = o.ID.ToString(),
            //        ExtField1 = "PrintHistoryService.Add"
            //    }
            //    ).ToList();
            //    //仅迁移中时记录，其他情况不用记
            //    new DataChangeLogRepository().Add(dcLogs, 1);
            //}
            //#endregion
        }

        /// <summary>
        /// 查询打印记录未确认的数据
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public List<PrintHistory> GetWaitConfirmPrintHistoryList(List<int> shopIds, bool isCustomerOrder)
        {
            return _repository.GetWaitConfirmPrintHistoryList(shopIds, isCustomerOrder);
        }

        public bool Delete(List<long> printHistoryIds)
        {
            return _repository.Delete(printHistoryIds);
        }

        /// <summary>
        /// 获取打印记录详细信息
        /// </summary>
        /// <param name="keys">订单查询的key</param>
        /// <param name="isCustomerOrder">是否自由打印</param>
        /// <returns></returns>
        public List<PrintHistory> Get(List<OrderSelectKeyModel> keys, bool isCustomerOrder = false)
        {
            return _repository.GetPrintHistorys(keys, isCustomerOrder);
        }

        /// <summary>
        /// 是否存在打印记录
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public bool HasPrintHistory(int shopId)
        {
            return _repository.HasPrintHistory(shopId);
        }

        public List<PrintHistory> GetListByIds(List<long> printHistoryIds)
        {
            var phs = _repository.GetListByIds(printHistoryIds);

            SetPrintHistoryData(phs);

            return phs;
        }

        /// <summary>
        /// 更新打印记录收件人信息
        /// </summary>
        /// <param name="list"></param>
        public void UpdateMaskReceiver(List<PrintHistory> list)
        {
            _repository.UpdateMaskReceiver(list);
        }

        /// <summary>
        /// 标记为隐藏
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="fxUserId">打印人FxUserId</param>
        public int SetHide(List<long> ids, int fxUserId)
        {
            return _repository.SetHide(ids, fxUserId);
        }

        /// <summary>
        /// 获取二次发货的打印记录，获取全部数据并过滤已发货
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="isPdd"></param>
        /// <returns></returns>
        public PagedResultModel<PrintHistory> LoadSecondSendList(PrintHistoryRequestModel queryModel, bool? isPdd = null)
        {

            var allList = new List<PrintHistory>();
            var allSendHistoryList = new List<SendHistoryQueryResult>();
            var sendHistroyService = new SendHistoryService();
            var sendHFields = new List<string> { "sh.OrderId", "sh.ExpressName", "sh.LogistiscBillNo", "sh.SendType", "sh.PackId", "sho.OrderId AS LogicOrderId" };
            queryModel.SendHistroyStrFields = string.Join(",", sendHFields);
            queryModel.IsPdd = isPdd;
            queryModel.StrFields = @"ph.ID,ph.ShopId,ph.WaybillCodeOrderId,ph.PlatformOrderId,ph.PlatformOrderJoin,ph.CustomerOrderId,ph.ExpressWaybillCode,ph.ExpressWaybillCodeChild,ph.BuyerMemberName,ph.Reciver,ph.ReciverPhone,ph.ReciverAddress,ph.ToProvince,ph.ToCity,ph.ToDistrict,ph.Sender,ph.SenderPhone,ph.TotalWeight,ph.ProductCount,ph.TemplateType,ph.TemplateId,ph.TemplateName,ph.PrintDate,ph.PrintBatch,ph.BatchIndex,ph.PrintBatchNumber,ph.UserId,ph.SendType,ph.SendContent,ph.PathFlowCode";

            // 是否使用新查询逻辑
            var enableNewQuery = false;
            var enableTimeStr = new CommonSettingService().Get("PrintHistory:ResendPage:NewQueryEnableTime", 0)?.Value;
            if (enableTimeStr.IsNotNullOrEmpty())
            {
                var enableTime = enableTimeStr.ToDateTime();
                if (queryModel.StartDate.ToDateTime() > enableTime)
                    enableNewQuery = true;
            }
            queryModel.UseNewQuery = enableNewQuery;
            //queryModel.EndDate = null;
            //queryModel.StartDate = null;

            if (!enableNewQuery)
            {
                queryModel.PageIndex = 1;
                queryModel.PageSize = 100; // 默认每页10条，改为100减少翻页次数
            }
            else if(queryModel.SendType == 200)
            {
                queryModel.SendStatus = -1;
            }

            //查询打印记录
            var firstPagedResult = LoadList(queryModel);
            var resultList = firstPagedResult.Rows;

            if (enableNewQuery)
                allList = resultList;

            //查询发货记录
            var logicOrderIds = allList.Select(a => a.PlatformOrderId)?.ToList();
            if (logicOrderIds != null && logicOrderIds.Any())
                allSendHistoryList = sendHistroyService.GetSendHistoryByOrderId(logicOrderIds, false, sendHFields);


            //1.逐页查询
            if (!enableNewQuery && firstPagedResult.Total > queryModel.PageSize)
            {
                while (true)
                {
                    queryModel.PageIndex++;
                    var curPagedResult = LoadList(queryModel);
                    allList.AddRange(curPagedResult.Rows);

                    //2.查询发货记录
                    var curLogicOrderIds = curPagedResult.Rows.Select(a => a.PlatformOrderId)?.ToList();
                    if (curLogicOrderIds != null && curLogicOrderIds.Any())
                        allSendHistoryList.AddRange(sendHistroyService.GetSendHistoryByOrderId(curLogicOrderIds, false, sendHFields));

                    if (curPagedResult.Rows.Count() < queryModel.PageSize)
                        break;
                }
            }

            //3.只取没有发货记录的数据
            allList.ForEach(o =>
            {
                if (!enableNewQuery)
                {
                    var exist = allSendHistoryList.FirstOrDefault(a => a.LogistiscBillNo == o.ExpressWaybillCode && a.SendType > 0 && (a.OrderId == o.PlatformOrderId || a.LogicOrderId == o.PlatformOrderId));
                    if (exist == null)
                        resultList.Add(o);
                    else return;
                }
                
                o.Packs = new List<SendHistoryQueryResult>();
                //已发货包裹数据
                var gPacks = allSendHistoryList.Where(a => !string.IsNullOrEmpty(a.PackId) && (a.OrderId == o.PlatformOrderId || a.LogicOrderId == o.PlatformOrderId))?.GroupBy(b => b.PackId).ToList();
                gPacks?.ForEach(g => { o.Packs.Add(g.Last()); });
                o.Packs = allSendHistoryList.Where(a => a.OrderId == o.PlatformOrderId || a.LogicOrderId == o.PlatformOrderId).GroupBy(b => b.PackId).Select(g => g.First())?.ToList();
            });

            //4.获取商家信息
            if (resultList != null && resultList.Any())
            {
                var fxUserId = queryModel.FxUserId;

                var pathFlowCodes = resultList.Select(a => a.PathFlowCode).Distinct().ToList();
                if (pathFlowCodes != null && pathFlowCodes.Any())
                {
                    var sids = resultList.Select(x => x.ShopId).Distinct().ToList();
                    var shops = new ShopRepository().GetShopByIds(sids);

                    // 当前用户的商家信息
                    var supplierUserRepository = new SupplierUserRepository();
                    var agentUsers = supplierUserRepository.GetByFxUserId(fxUserId, false,needEncryptAccount:true);
                    var pfnList = new PathFlowService().GetPathFlowNodeListByFxUserId(pathFlowCodes, fxUserId);

                    resultList.ForEach(o =>
                    {
                        var exist = pfnList.FirstOrDefault(a => a.FxUserId == fxUserId && a.PathFlowCode == o.PathFlowCode);
                        if (exist != null)
                        {
                            if (exist.UpFxUserId == 0)
                            {
                                o.IsSelf = true;
                                o.AgentName = shops.FirstOrDefault(x => x.Id == o.ShopId)?.NickName;
                            }
                            else
                            {
                                var agentUser = agentUsers.FirstOrDefault(x => x.FxUserId == exist.UpFxUserId && x.SupplierFxUserId == fxUserId);
                                if (agentUser != null)
                                    o.AgentName = agentUser.AgentMobileAndRemark;
                                else
                                    o.AgentName = shops.FirstOrDefault(x => x.Id == o.ShopId)?.NickName;
                            }
                        }
                    });
                }

            }

            //5.获取取已发货包裹数据
            //var list = sendHistroyService.GetSendHistoryByOrderId(logicOrderIds, true);

            firstPagedResult.Rows = resultList;
            if(!enableNewQuery)
                firstPagedResult.Total = resultList.Count();
            firstPagedResult.UseNewQuery = enableNewQuery;

            return firstPagedResult;
        }


        /// <summary>
        /// 根据Ids获取打印数据（联P_PrintHistoryOrder/P_PrintOrderProduct表）
        /// </summary>
        /// <param name="printHistoryIds"></param>
        /// <returns></returns>
        public List<PrintHistory> GetDetailListByIds(List<long> printHistoryIds)
        {
            return _repository.GetDetailListByIds(printHistoryIds);
        }


        /// <summary>
        /// 获取打印信息列表，以路径流查，为迁移数据（带子表数据）
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="pageSize"></param>
        /// <param name="platformType">指定店铺类型</param>
        /// <returns></returns>
        public List<PrintHistory> GetListForDuplication(DuplicationConditionModel condition,
            int pageSize, out int curCount, string platformType = "TouTiao")
        {

            //Log.WriteLine($"查询打印记录：{condition.ToJson()}");
            if (string.IsNullOrEmpty(condition.PathFlowCode))
                throw new Exception("PrintHistory查询条件中路径流不能为空");
            curCount = 0;
            var list = _repository.GetListForDuplication(condition, pageSize);
            curCount = list.Count();
            if (curCount == 0)
                return list;

            condition.MaxId = list.Max(a => a.ID);

            //已经使用源商家的路径流，不需要再过滤2023.04.12
            //list = FilterPlatformType(list, platformType, condition.FxUserId);

            //查询子表相关数据
            var ids = list.Select(a => a.ID).ToList();
            var orderList = GetOrderListForDuplication(ids);

            var masterIds = orderList.Select(a => a.ID).ToList();
            var productList = GetProductListForDuplication(masterIds);

            list.ForEach(ph =>
            {
                ph.PrintHistoryOrders = orderList.Where(a => a.PrintHistoryId == ph.ID).ToList();
                ph.PrintHistoryOrders.ForEach(pho =>
                {
                    pho.PrintOrderProducts = productList.Where(a => a.MasterId == pho.ID).ToList();
                });
            });

            return list;
        }

        /// <summary>
        /// 批量插入数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        public void InsertsForDuplication(List<PrintHistory> models)
        {
            Add(models, true);
        }
        /// <summary>
        /// 获取信息为复制副本，按Ids（带子表数据）
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="selectFieldNames"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<PrintHistory> GetListForDuplication(List<long> ids, string selectFieldNames = "*",
            string whereFieldName = "Id", string platformType = "TouTiao")
        {
            if (ids == null || !ids.Any())
                return new List<PrintHistory>();
            var list = _repository.GetListForDuplication(ids, selectFieldNames, whereFieldName);
            if (list == null || !list.Any())
                return list;

            //查询子表相关数据
            var orderList = GetOrderListForDuplication(ids);

            list = FilterPlatformType(list, platformType);

            var masterIds = orderList.Select(a => a.ID).ToList();
            var productList = GetProductListForDuplication(masterIds);

            list.ForEach(ph =>
            {
                ph.PrintHistoryOrders = orderList.Where(a => a.PrintHistoryId == ph.ID).ToList();
                ph.PrintHistoryOrders.ForEach(pho =>
                {
                    pho.PrintOrderProducts = productList.Where(a => a.MasterId == pho.ID).ToList();
                });
            });

            return list;
        }

        /// <summary>
        /// 获取子表信息为复制副本，按PrintHistoryId
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFieldNames"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<PrintHistoryOrder> GetOrderListForDuplication(List<long> codes, string selectFieldNames = "*",
            string whereFieldName = "PrintHistoryId")
        {
            if (codes == null || !codes.Any())
                return new List<PrintHistoryOrder>();
            return _repository.GetOrderListForDuplication(codes, selectFieldNames, whereFieldName);
        }

        /// <summary>
        /// 获取子表信息为复制副本，按MasterId
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFieldNames"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<PrintOrderProduct> GetProductListForDuplication(List<long> codes, string selectFieldNames = "*",
            string whereFieldName = "MasterId")
        {
            if (codes == null || !codes.Any())
                return new List<PrintOrderProduct>();
            return _repository.GetProductListForDuplication(codes, selectFieldNames, whereFieldName);
        }


        /// <summary>
        /// 只取指定平台的数据
        /// </summary>
        /// <param name="list"></param>
        /// <param name="platformType"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<PrintHistory> FilterPlatformType(List<PrintHistory> list, string platformType, int fxUserId = 0)
        {
            if (list == null || !list.Any())
                return list;

            var pathFlowCodes = list.Select(a => a.PathFlowCode).Where(a => !string.IsNullOrEmpty(a)).Distinct().ToList();
            var models = new List<PathFlowPlatformTypeModel>();
            var cacheKey = "PathFlowPlatformType";

            //1.先从缓存取
            var cache = HttpRuntime.Cache[cacheKey];
            if (cache != null)
            {
                models = cache as List<PathFlowPlatformTypeModel>;
                var existCodes = models.Select(a => a.PathFlowCode).ToList();

                //过滤已存在的
                pathFlowCodes = pathFlowCodes.Where(a => !existCodes.Contains(a)).ToList();
            }

            //2.缓存不存在路径流，逐个业务库查询
            if (pathFlowCodes.Any())
            {
                Data.Extension.DbPolicyExtension.ParellelForeachAllDbs((db, dbName) =>
                {
                    var batchSize = 500;
                    var count = Math.Ceiling(pathFlowCodes.Count * 1.0 / batchSize);
                    for (var i = 0; i < count; i++)
                    {
                        var curCodes = pathFlowCodes.Skip(i * batchSize).Take(batchSize).ToList();
                        var sql = "SELECT PathFlowCode,SourceShopId,SourceFxUserId FROM PathFlow WHERE PathFlowCode IN @Codes";
                        var curModels = db.Query<PathFlowPlatformTypeModel>(sql, new { Codes = curCodes });
                        if (curModels != null && curModels.Any())
                            models.AddRange(curModels);
                    }

                }, " AND dn.ApplicationName IN('fx') ", fxUserId);
            }

            var lastModels = new List<PathFlowPlatformTypeModel>();
            models.ForEach(model =>
            {
                if (!lastModels.Any(a => a.PathFlowCode == model.PathFlowCode))
                {
                    lastModels.Add(model);
                }
            });

            //3.从配置库查询ShopId所属PlatformType
            var sourceShopIds = lastModels.Where(a => string.IsNullOrEmpty(a.PlatformType)).Select(a => a.SourceShopId).Distinct().ToList();
            if (sourceShopIds.Any())
            {
                var shops = new ShopService().GetListByShopIds(sourceShopIds);
                lastModels.Where(a => string.IsNullOrEmpty(a.PlatformType)).ToList().ForEach(model =>
                {
                    var exist = shops.FirstOrDefault(a => a.Id == model.SourceShopId);
                    if (exist != null)
                    {
                        model.PlatformType = exist.PlatformType;
                    }
                });
            }

            //4.重新写入缓存，并过滤数据
            if (lastModels.Any(a => !string.IsNullOrEmpty(a.PlatformType)))
            {
                //写入缓存
                cache = lastModels.Where(a => !string.IsNullOrEmpty(a.PlatformType)).ToList();
                HttpRuntime.Cache.Insert(cacheKey, cache, null, DateTime.Now.AddDays(10), System.Web.Caching.Cache.NoSlidingExpiration);
            }

            //过滤
            var needCodes = lastModels.Where(a => a.PlatformType == platformType).Select(a => a.PathFlowCode).Distinct().ToList();
            list = list.Where(a => needCodes.Contains(a.PathFlowCode)).ToList();

            return list;
        }

        /// <summary>
        /// 根据底单id和店铺id获取打印记录
        /// </summary>
        /// <param name="sids"></param>
        /// <param name="wcIds"></param>
        /// <returns></returns>
        public List<PrintHistory> GetPrintHistoryBySidAndWcId(List<int> sids, List<int> wcIds)
        {
            return _repository.GetPrintHistoryBySidAndWcId(sids, wcIds);
        }

        public void GetPrintPdfUrlToTikTok(List<PrintHistory> printHistories)
        {
            if (printHistories == null || !printHistories.Any())
                return;

            // 获取所有相关ShopId
            var shopIds = printHistories.Select(ph => ph.ShopId).Distinct().ToList();
            var shops = new ShopService().GetShopByIds(shopIds);
            var shopMap = shops.ToDictionary(s => s.Id);

            var dicPackages = printHistories.GroupBy(x => x.PackageId).ToDictionary(x => x.Key, x => x.First());

            var sw = Stopwatch.StartNew();

            // 使用预构建的映射和并行循环来更新 PrintHistory 对象
            Parallel.ForEach(dicPackages, new ParallelOptions { MaxDegreeOfParallelism = 10 }, model =>
            {
                try
                {
                    if (shopMap.TryGetValue(model.Value.ShopId, out var shop))
                    {
                        var server = PlatformFactory.GetPlatformService(shop) as TikTokPlatformService;
                        var rst = server.GetPackageShippingDoc(model.Value.PackageId, model.Value.Type, model.Value.Size);
                        if (rst.Item1 && !string.IsNullOrWhiteSpace(rst.Item2))
                        {
                            model.Value.PrinterJsonData = rst.Item2;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"获取TikTok 打印PDF链接异常：{ex.ToString()}");
                }
            });
            printHistories.ForEach(x =>
            {
                x.PrinterJsonData = dicPackages[x.PackageId].PrinterJsonData;
            });
            if (CustomerConfig.IsDebug)
            {
                Log.WriteError($@"获取TikTok 打印PDF链接调用耗时：{sw.ElapsedMilliseconds}");
            }
        }
    }
}
