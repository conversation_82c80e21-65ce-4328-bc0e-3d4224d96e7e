using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services.Services.CrossCloud;
using DianGuanJiaApp.Utility.Extension;
using RabbitMQ.Client.Framing.Impl;
using DianGuanJiaApp.Data.Entity.BaseProduct;

namespace DianGuanJiaApp.Services
{

    public partial class ProductInfoFxService : BaseService<Data.Entity.ProductInfoFx>
    {
        private ProductFxRepository _productFxRepository ;
        private DbConfigRepository _dbConfigRepository;
        private Dictionary<int, List<DbConfigModel>> _queryedDbConfigModels;
        private ProductFxService _productFxService;
        public ProductInfoFxService()
        {
            _repository = new ProductInfoFxRepository();
            _productFxRepository = new ProductFxRepository();
            _dbConfigRepository = new DbConfigRepository();
            _productFxService = new ProductFxService();
            
        }
        public ProductInfoFxService(string connectionString) : base(connectionString)
        {
            _repository = new ProductInfoFxRepository(connectionString);
            _productFxRepository = new ProductFxRepository(connectionString);
            _productFxService = new ProductFxService(connectionString);
        }
        #region 私有变量

        private ProductInfoFxRepository _repository = null;

        #endregion

        public List<ProductInfoFx> GetProductInfoList(int fxUserId, List<string> pCodes)
        {
            return _repository.GetProductInfoList(fxUserId, pCodes);
        }
        public List<BaseOfPtSkuRelation> GetProductSkuRelationList(int fxUserId, List<string> sCodes)
        {
            return _repository.GetProductSkuRelationList(fxUserId, sCodes);
        }

        public List<BaseOfPtSkuRelation> GetProductSkuRelationList(int fxUserId, List<string> shortTitle, int type, int contain)
        {
            return _repository.GetProductSkuRelationList(fxUserId, shortTitle,type,contain);
        }

        /// <summary>
        /// 更新商品或规格的简称和重量
        /// SkuCode为空表示更新商品信息，否则表示更新规格信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="models"></param>
        public void SaveShortTitleOrWeight(int fxUserId, List<SaveShortTitleOrWeightModel> models,bool isOptimisticLock = true)
        {
            if (fxUserId <= 0 || models == null || !models.Any())
                return;
            var codes = models.Where(x => !string.IsNullOrEmpty(x.ProductCode)).Select(x => x.ProductCode).Distinct().ToList();
            if (codes == null || codes.Any() == false)
                throw new LogicException("未找到对应的商品数据");
            if (isOptimisticLock)
            {
                var op = OptimisticLockOperationType.SaveShortTitleOrWeight;
                var opId = $"FX{fxUserId}";
                var result = new CommonSettingService().ExecuteWithOptimisticLock(() =>
                {
                    var products = new ProductFxRepository().GetProducts(codes, fxUserId);
                    _repository.SaveShortTitleOrWeight(fxUserId, models, products);
                    return true;
                }, () => {
                    Log.Debug(() => $"更新商品或规格的简称和重量【{fxUserId}】动作未获取到锁");
                }, op, opId, maxLockMinutes: 240, sleepSeconds: 3);
            }
            else
            {
                var products = new ProductFxRepository().GetProducts(codes, fxUserId);
                _repository.SaveShortTitleOrWeight(fxUserId, models, products);
            }

            #region 数据变更日志 --简称和重量只有自己使用，不需要同步副本
            //var dcLogs = products?.Select(o => new DataChangeLog
            //{
            //    DataChangeType = DataChangeTypeEnum.INSERT,
            //    TableTypeName = DataChangeTableTypeName.Product,
            //    SourceShopId = o.ShopId,
            //    SourceFxUserId = o.SourceUserId,
            //    RelationKey = o.ProductCode,
            //    ExtField1 = "ProductInfoFx.SaveShortTitleOrWeight"
            //}
            //).ToList();
            //new DataChangeLogRepository().Add(dcLogs);
            #endregion
        }

        #region 暂时弃置 通过模型更新商品或规格的简称
        /// <summary>
        /// 通过模型更新商品或规格的简称
        /// SkuCode为空表示更新商品信息，否则表示更新规格信息
        /// </summary>
        /// <param name="fxUserId">当前登录用户</param>
        /// <param name="models"></param>
        /*public void SyncShortTitleFromSyncUser(int fxUserId, List<SaveShortTitleOrWeightModel> models)
        {
            if (fxUserId <= 0 || models == null || !models.Any())
                return;
            var codes = models.Where(x => !string.IsNullOrEmpty(x.ProductCode)).Select(x => x.ProductCode).Distinct().ToList();

            if (codes == null || codes.Any() == false)
                throw new LogicException("未找到对应的商品数据");

            // 通过ProductCodes和当前用户的Id获取需要同步简称的商品列表
            var products = new ProductFxRepository().GetProducts(codes, fxUserId);

            // 通过ProductCodes和对方用户Id获取同步简称的源信息商品列表
            var syncDict = models
                .GroupBy(m => m.SyncFxUserId)
                .ToDictionary(g => g.Key, g => g.Select(m => m.ProductCode).Distinct().ToList());

            var sourceProducts = new List<ProductInfoFx>();
            foreach (var kvp in syncDict)
            {
                int syncFxUserId = kvp.Key;
                List<string> productCodes = kvp.Value;

                // 对于商品或规格没有设置简称的数据，应该排除掉 
                var filteredProducts = new ProductInfoFxRepository().GetProductInfoList(syncFxUserId, productCodes);
                filteredProducts = filteredProducts.Where(p => !string.IsNullOrEmpty(p.ShortTitle)
                                            && p.Skus.All(s => !string.IsNullOrEmpty(s.ShortTitle))).ToList();

                sourceProducts.AddRange(filteredProducts);

            }
            // 将源信息商品列表的简称提取到models中
            foreach (var m in models)
            {
                var prods = sourceProducts.Where(p => m.ProductCode == p.ProductCode && m.SyncFxUserId == p.FxUserId).ToList();
                if (string.IsNullOrEmpty(m.SkuCode))//修改的是商品的简称
                {
                    m.ShortTitle = prods?.FirstOrDefault()?.ShortTitle;
                    m.IsShortTitleChange = !string.IsNullOrEmpty(m.ShortTitle);
                }
                else  // 修改的商品的sku的简称
                {
                    m.SkuShortTitle = prods?.SelectMany(p => p.Skus).FirstOrDefault(s => m.SkuCode == s.SkuCode)?.ShortTitle;
                    m.IsShortTitleChange = !string.IsNullOrEmpty(m.SkuShortTitle);
                }  
            }
            // 更新商品列表的简称
            _repository.SaveShortTitleOrWeight(fxUserId, models, products);
        }*/
        #endregion


        /// <summary>
        /// 商品列表同步对方简称
        /// </summary>
        /// <param name="productCodes">sku勾选框中的productcode 去重</param>
        /// <param name="skuCodes">全部勾选的skucode</param>
        /// <param name="noSyncSkuCodes">未勾选的skucode</param>
        /// <param name="noCheckedProductCodes">未勾选的productcode</param>
        /// <param name="fxUserId"></param>
        public void SyncShortTitleofProductIndex(List<string> productCodes,List<string> skuCodes, List<string> noCheckedProductCodes, int fxUserId)
        {
            if(productCodes == null)
            {
                productCodes = new List<string>();
            }
            if (skuCodes == null)
            {
                skuCodes = new List<string>();
            }
            if (noCheckedProductCodes == null)
            {
                noCheckedProductCodes = new List<string>();
            }

            // 获取需要同步简称的商品列表 (包含sku)
            var products = _productFxRepository.GetProductListByCodesV2(productCodes);
            //商品匹配路径流
            var listData = _productFxService.GetProductByDownFxUser(fxUserId, products, "all");
            var skuCodeSet = new HashSet<string>(skuCodes);
            var productSkuDic = new Dictionary<string, string>();
            foreach (var product in products)
            {
                var curSkuCodes = product.Skus.Select(t => t.SkuCode).ToList();
                //skuCodes中包含当前商品的skuCode
                if (skuCodeSet.Intersect(curSkuCodes).Any())
                {
                    //存在勾选了的sku，表示是按sku 同步
                    foreach (var sku in product.Skus)
                    {
                        if (skuCodeSet.Contains(sku.SkuCode))
                        {
                            productSkuDic[sku.SkuCode] = sku.ProductCode;
                        }
                    }
                }
                else
                {
                    //不存在勾选的sku，表示是按商品同步
                    //获取属于这个厂家的所有sku
                    var allSkuCodes =listData.FirstOrDefault(t => product.ProductCode == t.ProductCode)?.Skus?.Select(t => t.SkuCode)?.ToList();
                    skuCodes.AddRange(allSkuCodes);
                    allSkuCodes.ForEach(t =>
                    {
                        productSkuDic[t] = product.ProductCode;
                    });
                }
            }

            // 获取上下游
            var SupplierAgentModels = _productFxRepository.GetSupplierAndAgentByAllCodes(productCodes,skuCodes, fxUserId,true);
            // 过滤自营商品的上下游
            SupplierAgentModels.RemoveAll(sa => productCodes.Contains(sa.ProductRefCode) && sa.UpFxUserId < 1 && sa.DownFxUserId < 1);
            
            SyncShortTitle(productCodes, noCheckedProductCodes, skuCodes, fxUserId, SupplierAgentModels,productSkuDic,products);
        }

        /// <summary>
        /// 商品批量编辑 同步对方简称
        /// </summary>
        /// <param name="productCodes">sku勾选框中的productcode 去重</param>
        /// <param name="skuCodes">全部勾选的skucode</param>
        /// <param name="noCheckedProductSkuCodes">未勾选的productcode</param>
        /// <param name="fxUserId"></param>
        public void SyncShortTitleofBatchEdit(List<string> productCodes, List<string> skuCodes,List<string> noCheckedProductSkuCodes, int fxUserId)
        {
            if (productCodes == null)
            {
                productCodes = new List<string>();
            }
            if(skuCodes == null)
            {
                skuCodes = new List<string>();
            }
            if(noCheckedProductSkuCodes == null)
            {
                noCheckedProductSkuCodes = new List<string>();
            }

            // 前端勾选了sku但是未勾选product的sku对应的商品productCode
            var products = _productFxRepository.GetProductListByCodesV2(productCodes);


            var skuCodeSet = new HashSet<string>(skuCodes);
            var productSkuDic = new Dictionary<string, string>();
            foreach (var product in products)
            {
                foreach (var sku in product.Skus)
                {
                    if (skuCodeSet.Contains(sku.SkuCode))
                    {
                        productSkuDic[sku.SkuCode] = sku.ProductCode;
                    }
                }
            }

            var SupplierAgentModels = _productFxRepository.GetSupplierAndAgentByAllCodes(productCodes,skuCodes, fxUserId,true);
            // 过滤自营商品的上下游
            SupplierAgentModels.RemoveAll(sa => productCodes.Contains(sa.ProductRefCode) && sa.UpFxUserId < 1 && sa.DownFxUserId < 1);

            SyncShortTitle(productCodes,noCheckedProductSkuCodes, skuCodes, fxUserId, SupplierAgentModels,productSkuDic, products);
        }

        /// <summary>
        /// 同步对方简称
        /// </summary>
        /// <param name="productCodes">所有roductcode</param>
        /// <param name="noCheckedProductCodes">未勾选的product</param>
        /// <param name="skuCodes">勾选的skucode</param>
        /// <param name="fxUserId"></param>
        /// <param name="supplierAgents">商品/sku对应的上下游用户Id</param>
        /// <param name="productSkuDic">商品和sku的对应关系</param>
        public void SyncShortTitle(List<string> productCodes, List<string> noCheckedProductCodes, List<string> skuCodes, int fxUserId,List<SupplierAgentModel> supplierAgents, Dictionary<string, string> productSkuDic,List<ProductFx> products)
        {
            if (supplierAgents.IsNullOrEmpty())
                return;
            // 获取对方设置的商品和sku信息
            var sourceSkuInfo= GetInfoListOnlySku(supplierAgents, productSkuDic, productCodes,skuCodes,fxUserId);

            // 移除未勾选的productCode
            productCodes.RemoveAll(c => noCheckedProductCodes.Contains(c));
            var sourceProductInfo = GetInfoListOnlyProduct(supplierAgents.Where(sa => productCodes.Contains(sa.ProductRefCode)).ToList());
            if (!sourceSkuInfo.Any() && !sourceProductInfo.Any()) return;

            // 获取自己设置的商品信息
            var ownProductInfo = _repository.GetInfoListOnlyProduct(new List<int>() { fxUserId }, productCodes);
            var ownSkuInfo = _repository.GetInfoListOnlySku(new List<int>() { fxUserId }, skuCodes);

            // 构建更新简称模型
            var models = BuildShortTitleSaveModel(sourceProductInfo, ownProductInfo, sourceSkuInfo, ownSkuInfo, supplierAgents,productSkuDic);
            if (models == null || !models.Any()) return;
            _repository.SaveShortTitleOrWeight(fxUserId, models,products);
        }

        private List<DbConfigModel> GetDbConfigModelsByFxUserId(int fxUserId)
        {
            if (_queryedDbConfigModels.TryGetValue(fxUserId,out var models))
            {
               return models;
            }

            var newModels = _dbConfigRepository.GetListByFxUserIds(new List<int>() { fxUserId });
            if (newModels.IsNotNullOrEmpty())
            {
                _queryedDbConfigModels[fxUserId] = newModels;
            }
            return newModels;

        }

        /// <summary>
        /// 获取对方的商品简称信息（到对方库去查）
        /// </summary>
        /// <param name="supplierAgents">商品的上下游关系</param>
        /// <returns></returns>
        private List<ProductInfoFx> GetInfoListOnlyProduct(List<SupplierAgentModel> supplierAgents)
        {
            var results = new List<ProductInfoFx>();
            
            // 查找对方的数据库信息，这里有优化空间：如果dbConfigRepository.GetListByFxUserIds传入所有fxUserId可以获取到fxUserId-DbConfigModel的关联数据，就不用一个个传进去查了
            var fxUserConnGroup = supplierAgents.Where(sa => sa.UpFxUserId > 0 || sa.DownFxUserId > 0)
                                            .SelectMany(sa => GetDbConfigModelsByFxUserId(sa.UpFxUserId > 0 ? sa.UpFxUserId : sa.DownFxUserId)
                                                ?.Where(models => models.ConnectionString.IsNotNullOrEmpty())
                                                .Select(config => new
                                                {
                                                    ConnectionString = config.ConnectionString,
                                                    SupplierAgent = sa
                                                }))
                                            .GroupBy(x => x.ConnectionString)
                                            .ToList();
            //当前上下文是否是新分区
            var isNewArea = SiteContext.Current.CurrentDbConfig.DbNameConfig.ApplicationName == "fx_new";

            foreach (var group in fxUserConnGroup)
            {
                var connectionString = group.Key;
                var fxUserIds = new HashSet<int>();
                var productCodes = new HashSet<string>();

                foreach (var item in group)
                {
                    var sa = item.SupplierAgent;
                    if (sa.UpFxUserId > 0)
                        fxUserIds.Add(sa.UpFxUserId);
                    if (sa.DownFxUserId > 0)
                        fxUserIds.Add(sa.DownFxUserId);
                    productCodes.Add(sa.ProductRefCode);
                }

                // 精选有分区，必须与当前上下文一致
                ProductInfoFxRepository productInfoFxRepository = null;
                if (isNewArea == false)
                    productInfoFxRepository = new ProductInfoFxRepository();
                else
                    productInfoFxRepository = new ProductInfoFxRepository(connectionString);

                //Log.Debug($"connectionString={connectionString},productInfoFxRepository.DbConnection={productInfoFxRepository.DbConnection.ConnectionString}");
                var result = productInfoFxRepository.GetInfoListOnlyProduct(fxUserIds.ToList(), productCodes.ToList());
                //Log.Debug($"GetInfoListOnlyProduct==>{result.ToJson()}");
                results.AddRange(result);
            }

            return results;
        }

        /// <summary>
        /// 获取对方的sku简称信息（到对方库去查）
        /// </summary>
        /// <param name="supplierAgents">商品和sku的上下游关系</param>
        /// <returns></returns>
        private List<ProductSkuInfoFx> GetInfoListOnlySku(List<SupplierAgentModel> supplierAgents, Dictionary<string, string> productSkuDic, List<string> productCodes, List<string> skuCodes, int fxUserId)
        {
            _queryedDbConfigModels = new Dictionary<int, List<DbConfigModel>>();
            var results = new List<ProductSkuInfoFx>();
            //var fxUserDb = _dbConfigRepository.GetConfigFxUserId(fxUserId).ConnectionString;
            var skuSupplierAgents = new List<SupplierAgentModel>();

            skuCodes.ForEach(scode =>
            {
                var skuSA = supplierAgents?.Where(sa => sa.ProductRefCode.Equals(scode)).ToList() ?? new List<SupplierAgentModel>();
                if (skuSA.Any()) // sku有自己的上下游
                {
                    if(!skuSA.Where(s => s.DownFxUserId < 1 && s.UpFxUserId < 1).Any()) //  且 不是自营商品
                        skuSupplierAgents.AddRange(skuSA);
                }
                else // sku没有自己的上下游 用商品的上下游
                {
                    var produstSA = supplierAgents.Where(sa => sa.ProductRefCode.Equals(productSkuDic[scode])).ToList();
                    produstSA.ForEach(psa =>
                    {
                        skuSupplierAgents.Add(new SupplierAgentModel
						{ 
                            ProductRefCode = scode,
                            UpFxUserId = psa.UpFxUserId,
                            DownFxUserId = psa.DownFxUserId
                        });
                    });
                    
                }
            });
            //当前上下文是否是新分区
            var isNewArea = SiteContext.Current.CurrentDbConfig.DbNameConfig.ApplicationName == "fx_new";

            var dbConfigModels = skuSupplierAgents.Where(sa => sa.UpFxUserId > 0 || sa.DownFxUserId > 0)
                                .SelectMany(sa => GetDbConfigModelsByFxUserId(sa.UpFxUserId > 0 ? sa.UpFxUserId : sa.DownFxUserId)
                                    ?.Where(models => models.ConnectionString.IsNotNullOrEmpty())
                                    .Select(config => new
                                    {
                                        ConnectionString = config.ConnectionString,
                                        SupplierAgent = sa
                                    }))
                                .GroupBy(x => x.ConnectionString)
                                .ToDictionary(x => x.Key, x => x.Select(item => item.SupplierAgent).ToList());

            foreach (var group in dbConfigModels)
            {
                var connectionString = group.Key;
                var fxUserIds = new HashSet<int>();
                var codes = new HashSet<string>();

                foreach (var sa in group.Value)
                {
                    if (sa.UpFxUserId > 0)
                        fxUserIds.Add(sa.UpFxUserId);
                    if (sa.DownFxUserId > 0)
                        fxUserIds.Add(sa.DownFxUserId);
                    codes.Add(sa.ProductRefCode);
                }

                // 精选有分区，必须与当前上下文一致
                ProductInfoFxRepository productInfoFxRepository = null;
                if (isNewArea == false)
                    productInfoFxRepository = new ProductInfoFxRepository();
                else
                    productInfoFxRepository = new ProductInfoFxRepository(connectionString);

                //Log.Debug($"[{CustomerConfig.CloudPlatformType}] ==> GetInfoListOnlySku  connectionString={connectionString},productInfoFxRepository.DbConnection={productInfoFxRepository.DbConnection.ConnectionString}");
                results.AddRange(productInfoFxRepository.GetInfoListOnlySku(fxUserIds.ToList(), codes.ToList()));
            }

            return results;
        }

        /// <summary>
        /// 构建更新简称模型
        /// </summary>
        /// <param name="sourceProductInfo">对方的商品简称信息</param>
        /// <param name="ownProductInfo">我的商品简称信息</param>
        /// <param name="sourceSkuInfo">对方的sku简称信息</param>
        /// <param name="ownSkuInfo">我的sku简称信息</param>
        /// <param name="supplierAgents">商品和sku的上下游关系</param>
        /// <param name="productSkuDic">商品和sku的对应关系</param>
        /// <returns></returns>
        private List<SaveShortTitleOrWeightModel> BuildShortTitleSaveModel(List<ProductInfoFx> sourceProductInfo, List<ProductInfoFx> ownProductInfo,
                List<ProductSkuInfoFx> sourceSkuInfo, List<ProductSkuInfoFx> ownSkuInfo,
                List<SupplierAgentModel> supplierAgents, Dictionary<string, string> productSkuDic)
        {
            var models = new List<SaveShortTitleOrWeightModel>();

            var addedProducts = new HashSet<string>();
            var addedSkus = new HashSet<string>();

            var upFxUserByProductCode = supplierAgents
                .Where(sa => addedProducts.Contains(sa.ProductRefCode))
                .ToLookup(sa => sa.ProductRefCode, sa => sa.UpFxUserId);

            var upFxUserBySkuCode = supplierAgents
                .Where(sa => addedSkus.Contains(sa.ProductRefCode))
                .ToLookup(sa => sa.ProductRefCode, sa => sa.UpFxUserId);
            //处理商品简称
            foreach (var spi in sourceProductInfo)
            {
                var pcode = spi.ProductCode;
                var upFxUser = upFxUserByProductCode[pcode].FirstOrDefault();

                bool addFlag = false;

                if (!addedProducts.Contains(pcode)) // 未添加更新模型的product
                {
                    addFlag = true;
                    addedProducts.Add(pcode);
                }
                else if (upFxUser > 0 && upFxUser == spi.FxUserId) // 已添加更新模型，但是这是上游的简称记录，优先级更高
                {
                    addFlag = true;
                    models.RemoveAll(m => m.ProductCode == pcode); // 移除之前同一个商品添加过的更新模型
                }

                if (addFlag)
                {
                    var own = ownProductInfo.FirstOrDefault(opi => opi.ProductCode == spi.ProductCode);
                    if (own == null || !own.ShortTitle.Equals(spi.ShortTitle))
                    {
                        models.Add(new SaveShortTitleOrWeightModel()
                        {
                            ProductCode = spi.ProductCode,
                            ShortTitle = spi.ShortTitle,
                            RowShortTitle = own?.ShortTitle,
                            IsWeightChange = false,
                            IsShortTitleChange = true
                        });
                    }
                }
            }
            //剔除商品简称是空的数据
            models.RemoveAll(m => string.IsNullOrWhiteSpace(m.ShortTitle));
            //处理规格简称
            foreach (var ssi in sourceSkuInfo)
            {
                var scode = ssi.SkuCode;
                // sku自己的上下游关系
                var skuSupplierAgents = supplierAgents.Where(sa => sa.ProductRefCode == scode).ToList();

                bool addFlag = false;

                if (skuSupplierAgents.Any()) // sku有自己的上下游
                {
                    if (!addedSkus.Contains(scode)) // 未添加sku的更新模型
                    {
                        addFlag = true;
                        addedSkus.Add(scode);
                    }
                    else if (skuSupplierAgents.Any(sa => upFxUserBySkuCode[ssi.SkuCode].Contains(sa.UpFxUserId))) // 已添加sku的更新模型，但这条是sku的上游设置的简称记录，优先级更高
                    {
                        addFlag = true;
                        models.RemoveAll(m => m.SkuCode == scode);
                    }
                }
                else // sku没有自己的上下游，使用商品的默认上下游
                {
                    // sku对应的商品的上游
                    var up = supplierAgents.Where(sa => sa.ProductRefCode.Equals(productSkuDic[scode])).ToList().Select(sa => sa.UpFxUserId).FirstOrDefault();

                    if (!addedSkus.Contains(scode)) // 未添加sku的更新模型
                    {
                        addFlag = true;
                        addedSkus.Add(scode);
                    }
                    else if (up > 0 && up == ssi.FxUserId) // 已添加，但这条是默认上游设置的简称记录，优先级更高
                    {
                        addFlag = true;
                        models.RemoveAll(m => m.SkuCode == scode);
                    }
                }

                if (addFlag)
                {
                    var own = ownSkuInfo.FirstOrDefault(opi => opi.SkuCode == ssi.SkuCode);
                    if (own == null || !own.ShortTitle.Equals(ssi.ShortTitle))
                    {
                        models.Add(new SaveShortTitleOrWeightModel()
                        {
                            ProductCode = ssi.ProductCode,
                            SkuCode = ssi.SkuCode,
                            SkuShortTitle = ssi.ShortTitle,
                            RawSkuShortTitle = own?.ShortTitle,
                            IsWeightChange = false,
                            IsShortTitleChange = true
                        });
                    }
                }
            }
            //剔除规格简称是空的数据(skucode不为空表示同步的维度是sku级别)
            models.RemoveAll(m => string.IsNullOrWhiteSpace(m.SkuShortTitle) && !string.IsNullOrWhiteSpace(m.SkuCode));
            return models;
        }

        /// <summary>
        /// 查询简称相关
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public Tuple<List<ProductInfoFx>, List<ProductInfoFx>> GetProductInfosByRequestModel(int fxUserId, ProductFxRequertModule model)
        {
            return _repository.GetProductInfosByRequestModel(fxUserId, model);
        }

        /// <summary>
        /// 获取商品简称信息为复制副本
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        public List<ProductInfoFx> GetListForDuplication(List<string> codes)
        {
            if (codes == null || !codes.Any())
                return new List<ProductInfoFx>();

            var list = new List<ProductInfoFx>();
            var batchSize = 500;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetListForDuplication(batchCodes);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 批量插入数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        /// <param name="isUpdate"></param>
        /// <param name="updateFields"></param>
        public void InsertsForDuplication(List<ProductInfoFx> models, bool isUpdate = false,
            List<string> updateFields = null)
        {
            if (models == null || !models.Any())
            {
                return;
            }
            //清理源库ID
            models.ForEach(m => { m.Id = 0; });
            //分批插入
            var batchSize = 500;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();
                //路径流代码
                var codes = batchModels.Select(m => m.ProductCode).Distinct().ToList();
                //用户ID列表
                var fxUserIds = batchModels.Select(m => m.FxUserId).Distinct().ToList();
                //存在的唯一代码列表
                var idAndCodes = _repository.GetExistIdAndCodes(codes, fxUserIds);
                //全部不存在
                if (idAndCodes == null || !idAndCodes.Any())
                {
                    baseRepository.BulkWrite(batchModels, "ProductInfo", maxSingleNum: 1);
                    continue;
                }
                //存在
                if (isUpdate && updateFields != null && updateFields.Any())
                {
                    var updates = batchModels.Where(x =>
                            idAndCodes.Any(m => m.Code == x.ProductCode && m.FxUserId == x.FxUserId))
                        .ToList();
                    if (updates.Any())
                    {
                        _repository.BatchUpdate(updates, updateFields,
                            new List<string> { "FxUserId", "ProductCode" });
                    }
                }
                //不存在
                var inserts = batchModels.Where(x => !idAndCodes.Any(m => m.Code == x.ProductCode && m.FxUserId == x.FxUserId))
                    .ToList();
                if (inserts.Any())
                {
                    baseRepository.BulkWrite(inserts, "ProductInfo", maxSingleNum: 1);
                }
            }
        }

        /// <summary>
        /// 同步到其他平台
        /// </summary>
        /// <param name="models"></param>
        public void SyncToOtherPlatform(List<ProductInfoFx> models)
        {
            //判空处理
            if (models == null || !models.Any())
            {
                return;
            }

            Log.WriteLine($"设置商品简称，需要同步到其他云，同步商品简信息：{models.ToJson()}",
                $"SetProductShortTitle_{DateTime.Now:yyyy-MM-dd}.log");
            //并发数
            const int parallelism = 10;
            var options = new ParallelOptions { MaxDegreeOfParallelism = CustomerConfig.IsDebug ? 2 : parallelism };

            /*
            //按到平台类型，分销用户编号
            var groups = models.GroupBy(m => m.FxUserId).ToList();
            Parallel.ForEach(groups, options, group =>
            {
                //获取
                var dbConfigRepository = new DbConfigRepository();
                var dbConfig = dbConfigRepository.GetConfigFxUserId(group.Key,
                    new List<string> { CloudPlatformType.Alibaba.ToString(), CloudPlatformType.TouTiao.ToString() });
                if (dbConfig == null)
                {
                    return;
                }
                Log.WriteLine($"设置商品简称，需要同步到其他云，其他云数据库配置信息：{dbConfig.ToJson()}",
                    $"SetProductShortTitle_{DateTime.Now:yyyy-MM-dd}.log");
                var groupModels = group.ToList();
                //同云设置，否非同云设置
                if (dbConfig.DbServer.Location == CustomerConfig.CloudPlatformType)
                {
                    var repository = new ProductInfoFxService(dbConfig.ConnectionString);
                    repository.InsertsForDuplication(groupModels, true, new List<string> { "ShortTitle" });
                }
                else
                {
                    //初始化同步业务数据服务
                    var apiDbConfig = new ApiDbConfigModel(dbConfig.DbServer.Location, dbConfig.DbServer.Location, dbConfig.DbNameConfig.Id);
                    Log.WriteLine($"设置商品简称，需要同步到其他云，其他云API数据库配置信息：{apiDbConfig.ToJson()}",
                        $"SetProductShortTitle_{DateTime.Now:yyyy-MM-dd}.log");
                    //商品简称跨云同步
                    var syncBusinessDataService = new SyncBusinessDataService(apiDbConfig);
                    //并发
                    var chunks = groupModels.OrderBy(m => m.ProductCode).ToList().ChunkList(50);
                    Parallel.ForEach(chunks, options, chunk =>
                    {
                        //跨云确认是否存在数据
                        var fxUserIds = chunk.Select(m => m.FxUserId).ToList();
                        var productCodes = chunk.Select(m => m.ProductCode).ToList();
                        var existsSql =
                            "SELECT Id, FxUserId, ProductCode FROM dbo.ProductInfo(NOLOCK) WHERE FxUserId IN @FxUserIds AND ProductCode IN @ProductCodes";
                        var existsModels = syncBusinessDataService.Gets<ProductInfoFx>(existsSql,
                            new { FxUserIds = fxUserIds, ProductCodes = productCodes });
                        //数据不存在
                        if (existsModels == null || !existsModels.Any())
                        {
                            syncBusinessDataService.InsertData(chunk);
                            return;
                        }

                        //跨云更新数据
                        var updateModels = chunk.Where(m =>
                            existsModels.Any(e => e.FxUserId == m.FxUserId && e.ProductCode == m.ProductCode)).ToList();
                        if (updateModels.Any())
                        {
                            syncBusinessDataService.UpdateData(updateModels,
                                new List<string> { "FxUserId", "ProductCode" },
                                new List<string> { "ShortTitle" });
                        }

                        //跨云插入数据
                        var insertModels = chunk.Where(m =>
                                !existsModels.Any(e => e.FxUserId == m.FxUserId && e.ProductCode == m.ProductCode))
                            .ToList();
                        if (insertModels.Any())
                        {
                            syncBusinessDataService.InsertData(insertModels);
                        }
                    });
                }
            });
            */


            var dbConfigRepository = new DbConfigRepository();
            //源商家
            var sourceFxUserIds = models.Where(m => m.CloudPlatformType != CloudPlatformType.TouTiao.ToString()).Select(m => m.SourceUserId).Distinct().ToList();
            //厂家
            var fxUserIds = models.Where(m => m.CloudPlatformType == CloudPlatformType.TouTiao.ToString()).Select(m => m.FxUserId).Distinct().ToList();
            sourceFxUserIds.AddRange(fxUserIds);

            //所在精选/京东/拼多多云库（厂家、商家都取）
            var targetDbConfigs = dbConfigRepository.GetListByFxUserIds(sourceFxUserIds, new List<string> { CloudPlatformType.Alibaba.ToString(), CloudPlatformType.Jingdong.ToString(), CloudPlatformType.Pinduoduo.ToString() }, true);
            if (targetDbConfigs == null)
                targetDbConfigs = new List<DbConfigModel>();

            //抖店云所在库（只取厂家）
            var targetTouTiaoDbConfigs = dbConfigRepository.GetListByFxUserIds(fxUserIds, CloudPlatformType.TouTiao.ToString());
            if (targetTouTiaoDbConfigs != null && targetTouTiaoDbConfigs.Any())
                targetDbConfigs.AddRange(targetTouTiaoDbConfigs);

            //排除当前库
            //var curService = new ProductInfoFxService();
            targetDbConfigs = targetDbConfigs.Where(m => m.DbNameConfig.DbName != baseRepository.DbConnection.Database).ToList();

            var groups = targetDbConfigs.GroupBy(m => m.ConnectionString).ToList();
            Parallel.ForEach(groups, options, group =>
            {
                var dbConfig = group.First();
                var groupModels = new List<ProductInfoFx>();
                var curFxUserIds = targetDbConfigs.Where(m => m.ConnectionString == group.Key).Select(m => m.DbConfig.UserId).Distinct().ToList();
                if (dbConfig.DbServer.Location == CloudPlatformType.TouTiao.ToString())
                {
                    //抖店云按FxUserId取
                    groupModels = models.Where(m => curFxUserIds.Contains(m.FxUserId) && m.CloudPlatformType == dbConfig.DbServer.Location).ToList();
                }
                else
                {
                    //其他云按SourceFxUserId取
                    groupModels = models.Where(m => curFxUserIds.Contains(m.SourceUserId) && m.CloudPlatformType == dbConfig.DbServer.Location).ToList();
                }

                if (groupModels == null || groupModels.Any() == false)
                    return;

                //同云平台
                if (dbConfig.DbServer.Location == CustomerConfig.CloudPlatformType)
                {
                    var repository = new ProductInfoFxService(dbConfig.ConnectionString);
                    repository.InsertsForDuplication(groupModels, true, new List<string> { "ShortTitle" });
                }
                else
                {
                    //不同云
                    //初始化同步业务数据服务
                    var apiDbConfig = new ApiDbConfigModel(dbConfig.DbServer.Location, dbConfig.DbServer.Location,
                        dbConfig.DbNameConfig.Id);
                    //商品简称跨云同步
                    var syncBusinessDataService = new SyncBusinessDataService(apiDbConfig);
                    var chunks = groupModels.OrderBy(m => m.ProductCode).ToList().ChunkList(50);
                    Parallel.ForEach(chunks, options, chunk =>
                    {
                        //跨云确认是否存在数据
                        var createFxUserIds = chunk.Select(m => m.FxUserId).Distinct().ToList();
                        var productCodes = chunk.Select(m => m.ProductCode).ToList();
                        var existsSql =
                            "SELECT Id, FxUserId, ProductCode FROM dbo.ProductInfo(NOLOCK) WHERE FxUserId IN @FxUserIds AND ProductCode IN @ProductCodes";
                        var existsModels = syncBusinessDataService.Gets<ProductInfoFx>(existsSql,
                            new { FxUserIds = createFxUserIds, ProductCodes = productCodes });
                        //数据不存在
                        if (existsModels == null || !existsModels.Any())
                        {
                            syncBusinessDataService.InsertData(chunk);
                            return;
                        }

                        //跨云更新数据
                        var updateModels = chunk.Where(m =>
                            existsModels.Any(e => e.FxUserId == m.FxUserId && e.ProductCode == m.ProductCode)).ToList();
                        if (updateModels.Any())
                        {
                            syncBusinessDataService.UpdateData(updateModels,
                                new List<string> { "FxUserId", "ProductCode" },
                                new List<string> { "ShortTitle" });
                        }

                        //跨云插入数据
                        var insertModels = chunk.Where(m =>
                                !existsModels.Any(e => e.FxUserId == m.FxUserId && e.ProductCode == m.ProductCode))
                            .ToList();
                        if (insertModels.Any())
                        {
                            syncBusinessDataService.InsertData(insertModels);
                        }
                    });
                }

            });
        }
    }
}
