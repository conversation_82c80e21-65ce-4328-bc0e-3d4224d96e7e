using System.Collections.Generic;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using System.Linq;
using System;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.Services.CrossCloud;
using DianGuanJiaApp.Utility;
using System.Threading.Tasks;
using DianGuanJiaApp.Core.Extensions;

namespace DianGuanJiaApp.Services
{
    public partial class ProductSkuInfoFxService : BaseService<Data.Entity.ProductSkuInfoFx>
    {
        private readonly ProductSkuInfoFxRepository _repository;
        public ProductSkuInfoFxService()
        {
            _repository = new ProductSkuInfoFxRepository();
        }
        public ProductSkuInfoFxService(string connectionString) : base(connectionString)
        {
            _repository = new ProductSkuInfoFxRepository(connectionString);
        }

        /// <summary>
        /// 获取商品规格简称信息为复制副本
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        public List<ProductSkuInfoFx> GetListForDuplication(List<string> codes)
        {
            if (codes == null || !codes.Any())
                return new List<ProductSkuInfoFx>();

            var list = new List<ProductSkuInfoFx>();
            var batchSize = 500;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetListForDuplication(batchCodes);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 批量插入数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        /// <param name="isUpdate"></param>
        /// <param name="updateFields"></param>
        public void InsertsForDuplication(List<ProductSkuInfoFx> models, bool isUpdate = false,
            List<string> updateFields = null)
        {
            if (models == null || !models.Any())
                return;
            //清理源库ID
            models.ForEach(m => { m.Id = 0; });

            var batchSize = 500;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();
                //代码
                var codes = batchModels.Select(m => m.SkuCode).Distinct().ToList();
                //用户ID
                var fxUserIds = batchModels.Select(m => m.FxUserId).Distinct().ToList();
                //存在的唯一代码列表
                var idAndCodes = _repository.GetExistIdAndCodes(codes, fxUserIds);
                //全部不存在
                if (idAndCodes == null || !idAndCodes.Any())
                {
                    baseRepository.BulkWrite(batchModels, "ProductSkuInfo", maxSingleNum: 1);
                    continue;
                }
                //存在
                if (isUpdate && updateFields != null && updateFields.Any())
                {
                    var updates = batchModels
                        .Where(x => idAndCodes.Any(m => m.Code == x.SkuCode && m.FxUserId == x.FxUserId)).ToList();
                    if (updates.Any())
                    {
                        _repository.BatchUpdate(updates, updateFields, new List<string> { "FxUserId", "SkuCode" });
                    }
                }
                //不存在
                var inserts = batchModels.Where(x => !idAndCodes.Any(m => m.Code == x.SkuCode && m.FxUserId == x.FxUserId)).ToList();
                if (inserts.Any())
                {
                    baseRepository.BulkWrite(inserts, "ProductSkuInfo", maxSingleNum: 1);
                }
            }
        }
        /// <summary>
        /// 同步到其他平台
        /// </summary>
        /// <param name="models"></param>
        public void SyncToOtherPlatform(List<ProductSkuInfoFx> models)
        {
            //判空处理
            if (models == null || !models.Any())
            {
                return;
            }
            //并发数
            const int parallelism = 10;
            var options = new ParallelOptions { MaxDegreeOfParallelism = CustomerConfig.IsDebug ? 2 : parallelism };

            var dbConfigRepository = new DbConfigRepository();
            //源商家
            var sourceFxUserIds = models.Where(m => m.CloudPlatformType != CloudPlatformType.TouTiao.ToString()).Select(m => m.SourceUserId).Distinct().ToList();
            //厂家
            var fxUserIds = models.Where(m => m.CloudPlatformType == CloudPlatformType.TouTiao.ToString()).Select(m => m.FxUserId).Distinct().ToList();
            sourceFxUserIds.AddRange(fxUserIds);

            //所在精选/京东/拼多多云库（厂家、商家都取）
            var targetDbConfigs = dbConfigRepository.GetListByFxUserIds(sourceFxUserIds, new List<string> { CloudPlatformType.Alibaba.ToString(), CloudPlatformType.Jingdong.ToString(), CloudPlatformType.Pinduoduo.ToString() }, true);
            if (targetDbConfigs == null)
                targetDbConfigs = new List<DbConfigModel>();

            //抖店云所在库（只取厂家）
            var targetTouTiaoDbConfigs = dbConfigRepository.GetListByFxUserIds(fxUserIds, CloudPlatformType.TouTiao.ToString());
            if(targetTouTiaoDbConfigs != null && targetTouTiaoDbConfigs.Any())
                targetDbConfigs.AddRange(targetTouTiaoDbConfigs);

            //排除当前库
            //var curService = new ProductSkuInfoFxService();
            targetDbConfigs = targetDbConfigs.Where(m => m.DbNameConfig.DbName != baseRepository.DbConnection.Database).ToList();

            var groups = targetDbConfigs.GroupBy(m => m.ConnectionString).ToList();
            Parallel.ForEach(groups, options, group =>
            {
                var dbConfig = group.First();
                var groupModels = new List<ProductSkuInfoFx>();
                var curFxUserIds = targetDbConfigs.Where(m => m.ConnectionString == group.Key).Select(m => m.DbConfig.UserId).Distinct().ToList();
                if (dbConfig.DbServer.Location == CloudPlatformType.TouTiao.ToString())
                {
                    //抖店云按FxUserId取
                    groupModels = models.Where(m => curFxUserIds.Contains(m.FxUserId) && m.CloudPlatformType == dbConfig.DbServer.Location).ToList();
                }
                else
                {
                    //其他云按SourceFxUserId取
                    groupModels = models.Where(m => curFxUserIds.Contains(m.SourceUserId) && m.CloudPlatformType == dbConfig.DbServer.Location).ToList();
                }

                if (groupModels == null || groupModels.Any() == false)
                    return;

                //同云平台
                if (dbConfig.DbServer.Location == CustomerConfig.CloudPlatformType)
                {
                    var repository = new ProductSkuInfoFxService(dbConfig.ConnectionString);
                    repository.InsertsForDuplication(groupModels, true, new List<string> { "ShortTitle" });
                }
                else
                {
                    //不同云
                    //初始化同步业务数据服务
                    var apiDbConfig = new ApiDbConfigModel(dbConfig.DbServer.Location, dbConfig.DbServer.Location,
                        dbConfig.DbNameConfig.Id);
                    //商品简称跨云同步
                    var syncBusinessDataService = new SyncBusinessDataService(apiDbConfig);
                    var chunks = groupModels.OrderBy(m => m.SkuCode).ToList().ChunkList(50);
                    Parallel.ForEach(chunks, options, chunk =>
                    {
                        //跨云确认是否存在数据
                        var createFxUserIds = chunk.Select(m => m.FxUserId).Distinct().ToList();
                        var codes = chunk.Select(m => m.SkuCode).ToList();
                        var existsSql =
                            "SELECT Id, FxUserId, SkuCode FROM dbo.ProductSkuInfo(NOLOCK) WHERE FxUserId IN @FxUserIds AND SkuCode IN @SkuCodes";
                        var existsModels = syncBusinessDataService.Gets<ProductSkuInfoFx>(existsSql,
                            new { FxUserIds = createFxUserIds, SkuCodes = codes });
                        //数据不存在
                        if (existsModels == null || !existsModels.Any())
                        {
                            syncBusinessDataService.InsertData(chunk);
                            return;
                        }

                        //跨云更新数据
                        var updateModels = chunk.Where(m =>
                            existsModels.Any(e => e.FxUserId == m.FxUserId && e.SkuCode == m.SkuCode)).ToList();
                        if (updateModels.Any())
                        {
                            syncBusinessDataService.UpdateData(updateModels, new List<string> { "FxUserId", "SkuCode" },
                                new List<string> { "ShortTitle" });
                        }

                        //跨云插入数据
                        var insertModels = chunk.Where(m =>
                            !existsModels.Any(e => e.FxUserId == m.FxUserId && e.SkuCode == m.SkuCode)).ToList();
                        if (insertModels.Any())
                        {
                            syncBusinessDataService.InsertData(insertModels);
                        }
                    });
                }

            });

        }
    }
}
