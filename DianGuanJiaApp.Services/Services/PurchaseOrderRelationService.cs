using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.Services.CrossCloud;
using DianGuanJiaApp.Utility;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Utility.Extension;
using Dapper;
using System;
using DianGuanJiaApp.Services.Services.DataDuplication;
using DianGuanJiaApp.Services.Services.SyncDataInterface;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data;
using System.Threading;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.RabbitMQ;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services.SettingsService;
using System.Collections.Concurrent;
using DianGuanJiaApp.Utility.Helpers;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Data.Model.LogModel;
using System.Text;
using DianGuanJiaApp.Data.Repository.Settings;
using DianGuanJiaApp.Utility.Web;

namespace DianGuanJiaApp.Services.Services
{
    public class PurchaseOrderRelationService : BaseService<PurchaseOrderRelation>
    {
        private readonly PurchaseOrderRelationRepository _repository;
        private readonly DbConfigRepository _configRepository;
        private string _connectionString = string.Empty;
        private static LogHelper _logHelper;
        private readonly PurchaseOrderItemRelationService _purchaseOrderItemRelationService;
        private readonly ShopService _shopService;
        public PurchaseOrderRelationService()
        {
            _repository = new PurchaseOrderRelationRepository();
            _baseRepository = _repository;
            //数据库配置
            _configRepository = new DbConfigRepository();
            _logHelper = new LogHelper(Config.SlsProject, LogStoreNames.PurchaseOrderRelationLog);
            //采购单项关系
            _purchaseOrderItemRelationService = new PurchaseOrderItemRelationService();
            //店铺
            _shopService = new ShopService();

        }
        public PurchaseOrderRelationService(string connectionString)
        {
            _repository = new PurchaseOrderRelationRepository(connectionString);
            _baseRepository = _repository;
            //数据库配置
            _configRepository = new DbConfigRepository();
            _logHelper = new LogHelper(Config.SlsProject, LogStoreNames.PurchaseOrderRelationLog);
            _connectionString = connectionString;
            //采购单项关系
            _purchaseOrderItemRelationService = new PurchaseOrderItemRelationService(connectionString);
            //店铺
            _shopService = new ShopService();
        }

        public static LogHelper Instance
        {
            get
            {
                return _logHelper;
            }
        }

        /// <summary>
        /// 根据SourceLogicOrderId获取采购单关系信息
        /// </summary>
        /// <param name="logicOrderIds">逻辑单号</param>
        /// <param name="fields"></param>
        /// <param name="status">采购关系状态，1：正常，0：已取消</param>
        /// <returns></returns>
        public List<PurchaseOrderRelation> Get(List<string> logicOrderIds, List<string> fields = null, int status = 1)
        {
            if (logicOrderIds == null || !logicOrderIds.Any())
                return new List<PurchaseOrderRelation>();

            var list = new List<PurchaseOrderRelation>();
            var batchSize = 500;
            var count = Math.Ceiling(logicOrderIds.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = logicOrderIds.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.Get(batchCodes, fields, status);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
            //return _repository.Get(logicOrderIds, fields, status);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logicOrderIds"></param>
        /// <param name="fields"></param>
        /// <param name="status"></param>
        /// <param name="isAllAlibabaDb">是否要查所有精选库</param>
        /// <returns></returns>
        public List<PurchaseOrderRelation> GetInAllAlibabaDb(List<string> logicOrderIds, List<string> fields = null, int status = 1, bool isAllAlibabaDb = false)
        {
            var purchaseOrders = new List<PurchaseOrderRelation>();
            if (isAllAlibabaDb && CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                var logicOrderIdDict = new Dictionary<string, int>();
                #region 所有精选库都查询
                //1.查出精选库
                var dbList = new DbConfigRepository().GetFxAllCloudPlatformBusinessDbConfigsByAlibaba();
                var alibabaDbs = dbList.ToList();

                //2.逐个库查询
                alibabaDbs.ForEach(oDb =>
                {
                    var curConnectionString = oDb.ConnectionString;
                    var curList = new PurchaseOrderRelationService(curConnectionString).Get(logicOrderIds, fields, status);
                    curList?.ForEach(por =>
                    {
                        //相同只保留一条
                        if (logicOrderIdDict.ContainsKey(por.SourceLogicOrderId) == false)
                        {
                            purchaseOrders.Add(por);
                            logicOrderIdDict.Add(por.SourceLogicOrderId, 1);
                        }
                    });
                });
                #endregion
            }
            else
            {
                purchaseOrders = Get(logicOrderIds, fields, status);
            }

            return purchaseOrders;
        }

        /// <summary>
        /// 通过SourceLogicOrderId查询数据，返回结果带Items
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="fields"></param>
        /// <param name="status"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<PurchaseOrderRelation> GetListAndItems(List<string> codes, List<string> fields = null,
            int status = 1, string whereFieldName = "o.SourceLogicOrderId")
        {
            if (codes == null || !codes.Any())
                return new List<PurchaseOrderRelation>();

            var list = new List<PurchaseOrderRelation>();
            var batchSize = 500;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetListAndItems(batchCodes, fields, status, whereFieldName);
                if (batchList != null)
                    list.AddRange(batchList);
            }

            return list;
            //return _repository.GetListAndItems(logicOrderIds, fields, status);
        }

        /// <summary>
        /// 通过SourcePlatformOrderId查询数据，返回结果带Items
        /// </summary>
        /// <param name="pIds"></param>
        /// <param name="fields"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        public List<PurchaseOrderRelation> GetListAndItemByPids(List<string> pIds, List<string> fields = null, int status = 1)
        {
            return GetListAndItems(pIds, fields, status, "o.SourcePlatformOrderId");
        }

        /// <summary>
        /// 同步业务数据到阿里云
        /// </summary>
        /// <param name="models"></param>
        /// <param name="items">附加子项</param>
        /// <param name="isCompensate">是否是补偿：true时=存在忽略，不存在插入</param>
        public void SyncDataToAlibabaCloud(List<PurchaseOrderRelation> models, List<PurchaseOrderItemRelation> items = null, bool isCompensate = false)
        {
            //判空处理
            if (models == null || !models.Any())
            {
                return;
            }
            //采购单项关系信息
            var itemModels = models.Where(m => m.ItemRelations != null && m.ItemRelations.Any())
                .SelectMany(m => m.ItemRelations).ToList();
            if (items != null && items.Any())
                itemModels.AddRange(items);
            var logFileName = $"CompensatePurchaseOrderRelation-{DateTime.Now.ToString("yyMMdd")}.txt";
            if (isCompensate)
                Log.WriteLine($"SyncDataToAlibabaCloud，DbName={baseRepository?.DbConnection?.Database}，itemModels【{itemModels.Count()}】条", logFileName);

            if (!itemModels.Any())
            {
                return;
            }
            try
            {
                //基准云平台类型
                var baseCloudPlatformType = CloudPlatformType.Alibaba.ToString();
                //1688供应商用户ID
                var supplierFxUserIds = models.Select(m => m.PurchaseOrderFxUserId).Distinct().ToList();
                var supplierDbConfigs =
                    _configRepository.GetListByFxUserIds(supplierFxUserIds, baseCloudPlatformType);
                WriteSyncToAlibabaCloudDebugLog(
                    $@"采购单关系同步到阿里云，同步获取1688数据库配置，相关信息：{supplierDbConfigs?.Select(m => new
                    {
                        m.DbConfig?.UserId,
                        m.ConnectionString
                    }).Distinct().ToList().ToJson(true)}","info");
                //同步采购单关系到阿里云
                SyncPurchaseOrderRelationToAlibabaCloud(supplierDbConfigs, models, isCompensate);
                //同步采购单项关系到阿里云
                SyncPurchaseOrderItemRelationToAlibabaCloud(supplierDbConfigs, itemModels, isCompensate);

                if (isCompensate)
                    Log.WriteLine(
                        $"SyncDataToAlibabaCloud,supplierDbConfigs.DbNames={supplierDbConfigs?.Select(a => a.DbNameConfig.DbName).ToJson()}",
                        logFileName);
            }
            catch (Exception e)
            {
                WriteSyncToAlibabaCloudDebugLog($"采购单关系同步到阿里云失败，失败原因：{e.Message}，堆栈信息：{e.StackTrace}","error");
                throw;
            }
        }

        /// <summary>
        /// 检测超时未支付，则做取消订单
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="logicOrderId"></param>
        /// <param name="purchasePlatformOrderId"></param>
        public void CheckTimeOutUnPaidCancelPurchaseOrder(int fxUserId, string logicOrderId,
            string purchasePlatformOrderId)
        {
            //逻辑单列表
            var logicOrderIds = new List<string> { logicOrderId };
            //检查逻辑单是否存在
            var logicOrderService = new LogicOrderService(_connectionString);
            var orders = logicOrderService.GetOrdersWithPOrderItem(logicOrderIds,
                pOrderItemFields: new List<string> { "Count", "ProductID", "SkuID" });
            if (orders == null || !orders.Any())
            {
                Log.WriteLine($"检测超时未支付采购单时，订单不存在或已被删除（{logicOrderId}|{purchasePlatformOrderId}）！",
                    $"CheckTimeOutUnPaidCancelPurchaseOrder_{DateTime.Now:yyyy-MM-dd}.log");
                return;
            }

            //查询采购单信息，判断采购单是否都是待支付状态
            var purchaseOrders = GetListAndItems(logicOrderIds, null, 1);
            //获取等待支付的单子
            var waitPayPurchaseOrders = purchaseOrders
                ?.Where(x => x.PurchaseOrderStatus == PurchaseOrderRelationEnumStatus.waitpay.ToString())?.ToList();
            if (waitPayPurchaseOrders == null || !waitPayPurchaseOrders.Any())
            {
                Log.WriteLine($"检测超时未支付采购单时，采购单已是非待支付状态（{logicOrderId}|{purchasePlatformOrderId}）！",
                    $"CheckTimeOutUnPaidCancelPurchaseOrder_{DateTime.Now:yyyy-MM-dd}.log");
                return;
            }
            //获取买家账号
            var buyerShop = GetAlibabaBuyerShop(fxUserId);
            if (buyerShop == null)
            {
                Log.WriteLine($"检测超时未支付采购单时，获取买家账号信息为空（{fxUserId}|{logicOrderId}|{purchasePlatformOrderId}）！",
                    $"CheckTimeOutUnPaidCancelPurchaseOrder_{DateTime.Now:yyyy-MM-dd}.log");
                return;
            }
            //买家下单平台服务
            var buyerPlatformService = new AlibabaZhuKePlatformService(buyerShop);
            //取消支付
            var returned = buyerPlatformService.CancelTrade(purchasePlatformOrderId, cancelReason: "buyerCancel");
            if (returned.Success)
            {
                try
                {
                    //取消状态
                    var cancelStatus = PurchaseOrderRelationEnumStatus.waitpay_cancel.ToString();
                    //更新取消订单状态
                    purchaseOrders.ForEach(m =>
                    {
                        m.PurchaseOrderStatus = cancelStatus;
                        m.ItemRelations.ForEach(ir => { ir.PurchaseOrderItemStatus = cancelStatus; });
                    });
                    //更新状态信息
                    var updatePurchaseStatusModels = purchaseOrders.Select(m => new UpdatePurchaseStatusModel
                    {
                        PlatformOrderId = m.PurchasePlatformOrderId,
                        PlatformStatus = m.PurchaseOrderStatus,
                        UserId = fxUserId,
                        Items = m.ItemRelations.Select(ir => new UpdatePurchaseItemStatusModel
                        {
                            PlatformOrderId = m.PurchasePlatformOrderId,
                            SubItemID = ir.PurchaseOrderSubItemId,
                            Status = ir.PurchaseOrderItemStatus
                        }).ToList()
                    }).ToList();
                    //更新状态，同时更新厂家端状态
                    UpdatePurchaseStatus(updatePurchaseStatusModels);
                    //跨云->更新采购单关系表
                    SyncUpdateStatusToAlibabaCloud(purchaseOrders);
                    //跨云->更新采购单项关系表
                    var itemRelations = purchaseOrders
                        .Where(m => m.ItemRelations != null && m.ItemRelations.Any()).SelectMany(m => m.ItemRelations)
                        .ToList();
                    if (itemRelations.Any())
                    {
                        SyncItemRelationUpdateStatusToAlibabaCloud(itemRelations);
                    }
                }
                catch (Exception e)
                {
                    Log.WriteError(
                        $"检测超时未支付采购单，取消采购单后，更新状态时失败，失败原因（{logicOrderId}|{purchasePlatformOrderId}）：{e.Message}，堆栈信息：{e.StackTrace}！",
                        $"CheckTimeOutUnPaidCancelPurchaseOrderError_{DateTime.Now:yyyy-MM-dd}.log");
                }

                return;
            }
            //取消采购单失败，失败日志
            Log.WriteLine($"检测超时未支付采购单，取消采购单失败，失败原因（{logicOrderId}|{purchasePlatformOrderId}）：{returned.Message}！",
                $"CheckTimeOutUnPaidCancelPurchaseOrder_{DateTime.Now:yyyy-MM-dd}.log");
        }

        /// <summary>
        /// 保存采购单相关信息
        /// </summary>
        /// <param name="models">采购订单关系数据</param>
        /// <param name="orders">采购订单对应的原始平台订单</param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public void Save(List<PurchaseOrderRelation> models, List<Order> orders, int curFxUserId)
        {
            #region 保存采购单关系
            //添加采购单关系项
            var itemModels = models.Where(m => m.ItemRelations != null && m.ItemRelations.Any())
                .SelectMany(m => m.ItemRelations).ToList();
            _purchaseOrderItemRelationService.BulkWrite(itemModels);
            //添加采购单关系
            _repository.BulkWrite(models, "PurchaseOrderRelation", maxSingleNum: 1);
            #endregion

            #region 添加逻辑单发货方式
            //获取厂家设置的发货方式
            var dtNow = DateTime.Now;
            var podModes = new List<PurchaseOrderDeliveryMode>();
            var supplierFxUserIds = models.Select(a => a.PurchaseOrderFxUserId).Distinct().ToList();
            var suppliers = new SupplierUserService().GetByFxIds(new List<int> { curFxUserId }, supplierFxUserIds);
            models.Where(m => m.Status == 1).ToList().ForEach(m =>
            {
                var supplier = suppliers.FirstOrDefault(a => a.SupplierFxUserId == m.PurchaseOrderFxUserId);
                var curDeliveryModel = supplier?.DeliveryMode ?? 0;
                var curIsNeedPrint = curDeliveryModel == 0 ? 1 : 0;
                var curMode = new PurchaseOrderDeliveryMode
                {
                    Flag = 0,
                    IsNeedPrint = curIsNeedPrint,
                    PurchaseRelationCode = m.PurchaseRelationCode,
                    LogicOrderId = m.SourceLogicOrderId,
                    CreateTime = dtNow
                };
                podModes.Add(curMode);
                m.DeliveryModes.Add(curMode);
            });
            new PurchaseOrderDeliveryModeService(_connectionString).Merger(podModes);
            #endregion

            #region 数据变更日志
            //冷热分离是以逻辑单为维度，需要改走逻辑单复制副本，这里的只做迁移使用。
            var dcLogs = models.Select(o => new DataChangeLog
            {
                DataChangeType = DataChangeTypeEnum.INSERT,
                TableTypeName = DataChangeTableTypeName.PurchaseOrderRelation,
                SourceShopId = o.SourceShopId,
                SourceFxUserId = o.CreateFxUserId,
                RelationKey = o.PurchaseRelationCode,
                ExtField1 = "",
            }).ToList();
            //20240314临时放开，不限制迁移状态 邱
            //TODO：待冷热分离的副本正式上线后，改回DataChangeLogRepository().Add(dcLogs, 1);
            new DataChangeLogRepository().Add(dcLogs);
            //20231226 改成基于逻辑单维度保存数据变更日志
            var dcLogsByLogicOrder = models.Select(o => new DataChangeLog
            {
                DataChangeType = DataChangeTypeEnum.PurchaseOrderRelation,
                TableTypeName = DataChangeTableTypeName.LogicOrder,
                SourceShopId = o.SourceShopId,
                SourceFxUserId = o.CreateFxUserId,
                RelationKey = o.SourceLogicOrderId,
                ExtField1 = "PurchaseOrderRelation"
            }).ToList();
            new DataChangeLogRepository().Add(dcLogsByLogicOrder);
            #endregion

            #region 推送复制副本消息(抖音云)，20231226 改成基于逻辑单维度推送消息。

            var duplicationMessages = models.Select(m => new
            {
                m.SourceShopId,
                m.CreateFxUserId
            })
                .Distinct()
                .Select(
                    m => new DuplicationMessageModel
                    {
                        FxUserId = m.CreateFxUserId,
                        ShopId = m.SourceShopId
                    }).ToList();
            //获取版本信息，增加灰度环境
            var fxUserVersions =
                _shopService.GetFxUserSystemVersions(duplicationMessages.Select(m => m.FxUserId).Distinct()
                    .ToList());
            duplicationMessages.GroupBy(m=>m.FxUserId).ToList().ForEach(group =>
            {
                //用户版本信息
                var fxUserVersion = fxUserVersions.FirstOrDefault(m => m.FxUserId == group.Key);
                //推送复制副本消息
                DuplicationFactoryService.Instance(DataChangeTableTypeName.LogicOrder)
                    .PushMessage(group.ToList(), fxUserVersion?.Version);
            });
            //增加采购关系推送消息 2024.04.16
            if (!DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage())
            {
                DuplicationFactoryService.Instance(DataChangeTableTypeName.PurchaseOrderRelation)
                    .PushMessage(duplicationMessages);
            }
            #endregion

            #region 跨云平台数据，保存采购单关系
            //跨云平台数据
            SyncDataToAlibabaCloud(models);
            #endregion

            #region 保存原始单
            //保存原始单
            new OrderService(_connectionString).BulkWrite(orders);
            var orderItems = orders.Where(m => m.OrderItems != null && m.OrderItems.Any()).SelectMany(m => m.OrderItems)
                .ToList();
            //保存订单项
            new OrderItemService(_connectionString).BulkWrite(orderItems);
            #endregion
        }

        /// <summary>
        /// 同步采购单关系到阿里云
        /// </summary>
        /// <param name="supplierDbConfigs">1688供应商数据库配置</param>
        /// <param name="models"></param>
        /// <param name="isCompensate">是否是补偿：true时=存在忽略，不存在插入</param>
        private void SyncPurchaseOrderRelationToAlibabaCloud(List<DbConfigModel> supplierDbConfigs,
            List<PurchaseOrderRelation> models, bool isCompensate = false)
        {
            WriteSyncToAlibabaCloudDebugLog(
                $@"采购单关系同步到阿里云，同步开始，相关信息：{models?.Select(m => new
                {
                    m.PurchaseOrderFxUserId,
                    m.PurchaseOrderCode,
                    m.CreateFxUserId,
                    m.SourceLogicOrderId
                }).Distinct().ToList().ToJson(true)}", "info");
            //判空处理
            if (models == null || !models.Any())
            {
                return;
            }
            //判空处理
            if (supplierDbConfigs == null || !supplierDbConfigs.Any())
            {
                return;
            }
            //兼容精选分库逻辑
            supplierDbConfigs = supplierDbConfigs.GroupBy(m => m.DbConfig.UserId)
                .Select(m => m.OrderByDescending(o => o.FromFxDbConfig).First()).ToList();
            //基准云平台类型
            var baseCloudPlatformType = CloudPlatformType.Alibaba.ToString();
            //最大并发数
            var maxDegreeOfParallelism = 1;
            //同云判断
            if (CustomerConfig.CloudPlatformType == baseCloudPlatformType)
            {
                var currentDbConfig = SiteContext.Current.CurrentDbConfig;
                var targetDbConfigs = supplierDbConfigs
                    .Where(m => !m.ConnectionString.Equals(currentDbConfig.ConnectionString)).ToList();
                if (targetDbConfigs.Any())
                {
                    var targetDbConfigsByGroup = targetDbConfigs.GroupBy(m => m.ConnectionString).ToList();
                    //并发执行保存
                    maxDegreeOfParallelism = targetDbConfigsByGroup.Count;
                    Parallel.ForEach(targetDbConfigsByGroup,
                        new ParallelOptions { MaxDegreeOfParallelism = maxDegreeOfParallelism }, group =>
                        {
                            //同云同步
                            var fxUserIds = group.Select(m => m.DbConfig.UserId).Distinct();
                            //过滤数据
                            var targetModels = models.Where(m => fxUserIds.Contains(m.PurchaseOrderFxUserId)).ToList();
                            var service = new PurchaseOrderRelationService(group.Key);
                            service.InsertsForDuplication(targetModels, isCompensate);

                            //逻辑单发货方式
                            //不需要同步 2023.11.28
                            //var deliveryModes = targetModels.Where(m => m.DeliveryModes != null && m.DeliveryModes.Any()).SelectMany(m => m.DeliveryModes).ToList();
                            //if (deliveryModes != null && deliveryModes.Any())
                            //{
                            //    new PurchaseOrderDeliveryModeService(group.Key).Merger(deliveryModes);
                            //}
                        });
                }
                return;
            }

            //跨云同步
            var supplierDbConfigsByDbConfigNameId = supplierDbConfigs.GroupBy(m =>
                new
                {
                    m.DbServer.Location,
                    DbNameConfigId = m.DbNameConfig.Id
                }).ToList();
            //并发执行保存
            maxDegreeOfParallelism = supplierDbConfigsByDbConfigNameId.Count;
            Parallel.ForEach(supplierDbConfigsByDbConfigNameId,
                new ParallelOptions { MaxDegreeOfParallelism = maxDegreeOfParallelism }, grouping =>
                {
                    //初始化同步业务数据服务
                    var apiDbConfig = new ApiDbConfigModel(grouping.Key.Location, baseCloudPlatformType,
                        grouping.Key.DbNameConfigId);
                    var syncBusinessDataService = new SyncBusinessDataService(apiDbConfig);
                    //过滤数据
                    var targetFxUserIds = grouping.Select(m => m.DbConfig.UserId).Distinct().ToList();
                    var targetModels = models.Where(m => targetFxUserIds.Contains(m.PurchaseOrderFxUserId)).ToList();
                    //日志
                    WriteSyncToAlibabaCloudDebugLog(
                        $@"采购单关系同步到阿里云，跨云同步中，相关信息：{new { grouping.Key.Location, grouping.Key.DbNameConfigId, targetFxUserIds }}", "info");
                    //跨云同步-分销商品关系映射数据
                    var chunks = targetModels.ChunkList(50);
                    chunks.ForEach(chunk =>
                    {
                        syncBusinessDataService.SyncData(chunk, "PurchaseRelationCode",
                            new List<string> { "PurchaseRelationCode" }, isCompensate: isCompensate);

                        //逻辑单发货方式
                        //不需要同步 2023.11.28
                        //var deliveryModes = chunk.Where(m => m.DeliveryModes != null && m.DeliveryModes.Any()).SelectMany(m => m.DeliveryModes).ToList();
                        //if (deliveryModes != null && deliveryModes.Any())
                        //{
                        //    syncBusinessDataService.SyncData(deliveryModes, "DeliveryModeCode",
                        //        new List<string> { "DeliveryModeCode" });
                        //}
                    });
                });
        }

        /// <summary>
        /// 同步采购单项关系到阿里云
        /// </summary>
        /// <param name="supplierDbConfigs"></param>
        /// <param name="models"></param>
        /// <param name="isCompensate">是否是补偿：true时=存在忽略，不存在插入</param>
        private void SyncPurchaseOrderItemRelationToAlibabaCloud(List<DbConfigModel> supplierDbConfigs,
            List<PurchaseOrderItemRelation> models, bool isCompensate = false)
        {
            //判空处理
            if (models == null || !models.Any())
            {
                return;
            }
            //判空处理
            if (supplierDbConfigs == null || !supplierDbConfigs.Any())
            {
                return;
            }
            //兼容精选分库逻辑
            supplierDbConfigs = supplierDbConfigs.GroupBy(m => m.DbConfig.UserId)
                .Select(m => m.OrderByDescending(o => o.FromFxDbConfig).First()).ToList();
            //基准云平台类型
            var baseCloudPlatformType = CloudPlatformType.Alibaba.ToString();
            //最大并发数
            var maxDegreeOfParallelism = 1;
            //同云判断
            if (CustomerConfig.CloudPlatformType == baseCloudPlatformType)
            {
                var currentDbConfig = SiteContext.Current.CurrentDbConfig;
                var targetDbConfigs = supplierDbConfigs
                    .Where(m => !m.ConnectionString.Equals(currentDbConfig.ConnectionString)).ToList();
                if (targetDbConfigs.Any())
                {
                    var targetDbConfigsByGroup = targetDbConfigs.GroupBy(m => m.ConnectionString).ToList();
                    //并发保存
                    maxDegreeOfParallelism = targetDbConfigsByGroup.Count;
                    Parallel.ForEach(targetDbConfigsByGroup,
                        new ParallelOptions { MaxDegreeOfParallelism = maxDegreeOfParallelism }, group =>
                        {
                            //同云同步
                            var fxUserIds = group.Select(m => m.DbConfig.UserId).Distinct();
                            //过滤数据
                            var targetModels = models.Where(m => fxUserIds.Contains(m.PurchaseOrderFxUserId)).ToList();
                            var service = new PurchaseOrderItemRelationService(group.Key);
                            service.InsertsForDuplication(targetModels, isCompensate);
                        });
                }
                return;
            }
            //跨云同步
            maxDegreeOfParallelism = supplierDbConfigs.Count;
            Parallel.ForEach(supplierDbConfigs,
                new ParallelOptions { MaxDegreeOfParallelism = maxDegreeOfParallelism }, dbConfig =>
                {
                    //初始化同步业务数据服务
                    var apiDbConfig = new ApiDbConfigModel(dbConfig.DbServer.Location, baseCloudPlatformType,
                        dbConfig.DbNameConfig.Id);
                    var syncBusinessDataService = new SyncBusinessDataService(apiDbConfig);
                    //过滤数据
                    var targetModels = models.Where(m => m.PurchaseOrderFxUserId == dbConfig.DbConfig.UserId).ToList();
                    //跨云同步-分销商品关系映射数据
                    var chunks = targetModels.ChunkList(50);
                    chunks.ForEach(chunk =>
                    {
                        syncBusinessDataService.SyncData(chunk, "PurchaseOrderItemRelationCode",
                            new List<string> { "PurchaseOrderItemRelationCode" }, isCompensate: isCompensate);
                    });
                });
        }

        /// <summary>
        /// 写同步到阿里云验证日志
        /// </summary>
        private void WriteSyncToAlibabaCloudDebugLog(string message,string level)
        {
            if (level == "info")
            {
				Log.WriteLine(message, $"SyncToAlibabaCloud-{DateTime.Now.ToString("yyyyMMdd")}.log");
			}
            else
            {
				Log.WriteError(message, $"SyncToAlibabaCloud-{DateTime.Now.ToString("yyyyMMdd")}.log");
			}
            
        }
        /// <summary>
        /// 同步更新采购单项关系到阿里云
        /// </summary>
        /// <param name="models">
        /// 模型中必须包含更新条件字段，需要更新的字段，采购单用户ID（确定同步到具体的1688商家数据库）
        /// 参考字段：PurchaseRelationCode,PurchaseOrderFxUserId,PurchaseOrderStatus
        /// </param>
        /// <param name="updateFieldNames">需要更新的字段名称列表，默认：PurchaseOrderStatus，指定，则需要包含模型中。</param>
        public void SyncUpdateStatusToAlibabaCloud(List<PurchaseOrderRelation> models,
            List<string> updateFieldNames = null)
        {
            //判空处理
            if (models == null || !models.Any())
            {
                return;
            }
            //更新字段处理
            if (updateFieldNames == null || !updateFieldNames.Any())
            {
                updateFieldNames = new List<string> { "PurchaseOrderStatus" };
            }
            //更新条件
            var updateWhereFields = new List<string> { "PurchaseRelationCode" };
            //基准云平台类型
            var baseCloudPlatformType = CloudPlatformType.Alibaba.ToString();
            //1688供应商用户ID
            var supplierFxUserIds = models.Select(m => m.PurchaseOrderFxUserId).Distinct().ToList();
            var supplierDbConfigs = _configRepository.GetListByFxUserIds(supplierFxUserIds, baseCloudPlatformType);
            //判空处理
            if (supplierDbConfigs == null || !supplierDbConfigs.Any())
            {
                return;
            }
            //兼容精选分库逻辑
            supplierDbConfigs = supplierDbConfigs.GroupBy(m => m.DbConfig.UserId)
                .Select(m => m.OrderByDescending(o => o.FromFxDbConfig).First()).ToList();
            //同云判断
            if (CustomerConfig.CloudPlatformType == baseCloudPlatformType)
            {
                var currentDbConfig = SiteContext.Current.CurrentDbConfig;
                var targetDbConfigs = supplierDbConfigs
                    .Where(m => !m.ConnectionString.Equals(currentDbConfig.ConnectionString)).ToList();
                var targetDbConfigsByGroup = targetDbConfigs.GroupBy(m => m.ConnectionString).ToList();
                targetDbConfigsByGroup.ForEach(group =>
                {
                    //同云同步
                    var fxUserIds = group.Select(m => m.DbConfig.UserId).Distinct();
                    //过滤数据
                    var targetModels = models.Where(m => fxUserIds.Contains(m.PurchaseOrderFxUserId)).ToList();
                    //同步更新
                    var repository = new PurchaseOrderRelationRepository(group.Key);
                    var result = repository.BatchUpdate(targetModels, updateFieldNames, updateWhereFields);
                });
                return;
            }
            //跨云同步
            supplierDbConfigs.ForEach(dbConfig =>
            {
                //初始化同步业务数据服务
                var apiDbConfig = new ApiDbConfigModel(dbConfig.DbServer.Location, baseCloudPlatformType,
                    dbConfig.DbNameConfig.Id);
                var syncBusinessDataService = new SyncBusinessDataService(apiDbConfig);
                //过滤数据
                var targetModels = models.Where(m => m.PurchaseOrderFxUserId == dbConfig.DbConfig.UserId).ToList();
                //跨云同步-分销商品关系映射数据
                var chunks = targetModels.ChunkList();
                chunks.ForEach(chunk =>
                {
                    var result = syncBusinessDataService.UpdateData(targetModels, updateWhereFields, updateFieldNames);
                });
            });
        }

        /// <summary>
        /// 同步更新采购单项关系状态到阿里云
        /// </summary>
        /// <param name="models">
        /// 模型中必须包含更新条件字段，需要更新的字段，采购单用户ID（确定同步到具体的1688商家数据库）
        /// 参考字段：PurchaseOrderItemRelationCode,PurchaseOrderFxUserId,PurchaseOrderItemStatus
        /// </param>
        /// <param name="updateFieldNames">需要更新的字段名称列表，默认：PurchaseOrderItemStatus，指定，则需要包含模型中。</param>
        public void SyncItemRelationUpdateStatusToAlibabaCloud(List<PurchaseOrderItemRelation> models,
            List<string> updateFieldNames = null)
        {
            //判空处理
            if (models == null || !models.Any())
            {
                return;
            }
            //更新字段处理
            if (updateFieldNames == null || !updateFieldNames.Any())
            {
                updateFieldNames = new List<string> { "PurchaseOrderItemStatus" };
            }
            //更新条件
            var updateWhereFields = new List<string> { "PurchaseOrderItemRelationCode" };
            //基准云平台类型
            var baseCloudPlatformType = CloudPlatformType.Alibaba.ToString();
            //1688供应商用户ID
            var supplierFxUserIds = models.Select(m => m.PurchaseOrderFxUserId).Distinct().ToList();
            var supplierDbConfigs =
                _configRepository.GetListByFxUserIds(supplierFxUserIds, baseCloudPlatformType);
            //判空处理
            if (supplierDbConfigs == null || !supplierDbConfigs.Any())
            {
                return;
            }
            //兼容精选分库逻辑
            supplierDbConfigs = supplierDbConfigs.GroupBy(m => m.DbConfig.UserId)
                .Select(m => m.OrderByDescending(o => o.FromFxDbConfig).First()).ToList();
            //同云判断
            if (CustomerConfig.CloudPlatformType == baseCloudPlatformType)
            {
                var currentDbConfig = SiteContext.Current.CurrentDbConfig;
                var targetDbConfigs = supplierDbConfigs
                    .Where(m => !m.ConnectionString.Equals(currentDbConfig.ConnectionString)).ToList();
                var targetDbConfigsByGroup = targetDbConfigs.GroupBy(m => m.ConnectionString).ToList();
                targetDbConfigsByGroup.ForEach(group =>
                {
                    //同云同步
                    var fxUserIds = group.Select(m => m.DbConfig.UserId).Distinct();
                    //过滤数据
                    var targetModels = models.Where(m => fxUserIds.Contains(m.PurchaseOrderFxUserId)).ToList();
                    //同步更新
                    var repository = new PurchaseOrderItemRelationRepository(group.Key);
                    var result = repository.BatchUpdate(targetModels, updateFieldNames, updateWhereFields);
                });
                return;
            }

            //跨云同步
            supplierDbConfigs.ForEach(dbConfig =>
            {
                //初始化同步业务数据服务
                var apiDbConfig = new ApiDbConfigModel(dbConfig.DbServer.Location, baseCloudPlatformType,
                    dbConfig.DbNameConfig.Id);
                var syncBusinessDataService = new SyncBusinessDataService(apiDbConfig);
                //过滤数据
                var targetModels = models.Where(m => m.PurchaseOrderFxUserId == dbConfig.DbConfig.UserId).ToList();
                //跨云同步-分销商品关系映射数据
                var chunks = targetModels.ChunkList();
                chunks.ForEach(chunk =>
                {
                    var result = syncBusinessDataService.UpdateData(chunk, updateWhereFields, updateFieldNames);
                });
            });
        }

        /// <summary>
        /// 获取信息为复制副本
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<PurchaseOrderRelation> GetListForDuplication(List<string> codes, string selectFields = null,
            string whereFieldName = "PurchaseRelationCode")
        {
            //判空处理
            if (codes == null || !codes.Any())
            {
                return new List<PurchaseOrderRelation>();
            }
            //返回数据
            var results = new List<PurchaseOrderRelation>();
            //分片查询
            var chunks = codes.ChunkList(500);
            chunks.ForEach(chunk =>
            {
                var models = _repository.GetListForDuplication(chunk, selectFields, whereFieldName);
                if (models != null && models.Any())
                {
                    results.AddRange(models);
                }
            });
            //返回
            return results;
        }
        public List<PurchaseOrderRelation> GetByCodes(List<string> codes)
        {
            return _repository.GetListForDuplication(codes);
        }
        public List<PurchaseOrderRelation> GetByPids(List<string> pids, string purchaseOrderStatus = null, List<string> fields = null, int? status = null)
        {
            if (pids == null || !pids.Any())
                return new List<PurchaseOrderRelation>();

            var list = new List<PurchaseOrderRelation>();
            var batchSize = 500;
            var count = Math.Ceiling(pids.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = pids.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetByPids(batchCodes, purchaseOrderStatus, fields, status);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pids">SourcePlatformOrderId</param>
        /// <returns></returns>
        public List<PurchaseOrderRelation> GetBySourcePids(List<string> pids, int? status = null, List<string> fields = null)
        {
            if (pids == null || !pids.Any())
                return new List<PurchaseOrderRelation>();

            var list = new List<PurchaseOrderRelation>();
            var batchSize = 500;
            var count = Math.Ceiling(pids.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = pids.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetBySourcePids(batchCodes, status, fields);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }
        public List<PurchaseOrderRelation> GetByLogicOrderIds(List<string> logicOrderIds)
        {
            if (logicOrderIds == null || !logicOrderIds.Any())
                return new List<PurchaseOrderRelation>();

            var list = new List<PurchaseOrderRelation>();
            var batchSize = 500;
            var count = Math.Ceiling(logicOrderIds.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = logicOrderIds.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetByLogicOrderIds(batchCodes);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 获取逻辑单【已退款成功】的【待发货】采购单
        /// </summary>
        /// <param name="purchaseFxUserId">厂家FxUserId</param>
        /// <param name="purchasePlatformOrderIds">指定采购单号（不考虑逻辑单状态）</param>
        /// <returns></returns>
        public List<PurchaseOrderRelation> GetRefundSuccessByPurchaseFxUserId(int purchaseFxUserId, List<string> purchasePlatformOrderIds = null)
        {
            return _repository.GetRefundSuccessByPurchaseFxUserId(purchaseFxUserId, purchasePlatformOrderIds);
        }

        /// <summary>
        /// 【所有精选业务库+抖店库】获取逻辑单【已退款成功】的【待发货】采购单
        /// </summary>
        /// <param name="purchaseFxUserId">厂家FxUserId</param>
        /// <param name="purchasePlatformOrderIds">指定采购单号（不考虑逻辑单状态）</param>
        /// <returns></returns>
        public List<PurchaseOrderRelation> GetRefundSuccessByPurchaseFxUserIdWithAllDb(int purchaseFxUserId, List<string> purchasePlatformOrderIds = null)
        {
            var logFileName = $"AutoRefund_{DateTime.Now.ToString("yy-MM-dd")}.txt";

            #region 1.查出相关库（所有精选业务库+抖店库）
            var dbs = new DbConfigRepository().GetFxAllCloudPlatformBusinessDbConfigs();
            var touTiaoDbs = new FxDbConfigRepository().GetFxAllCloudPlatformBusinessNewDbConfigs(purchaseFxUserId);
            var needDbs = dbs.Where(a => a.DbServer.Location == CloudPlatformType.Alibaba.ToString()).ToList();
            if (touTiaoDbs != null && touTiaoDbs.Any())
                needDbs.AddRange(touTiaoDbs);

            var needPurchaseOrderRelations = new List<PurchaseOrderRelation>();
            var sql = $@"SELECT por.PurchaseRelationCode,por.PurchasePlatformOrderId,por.PurchaseOrderShopId,por.CreateFxUserId,por.PurchaseOrderHopeSettlementPrice,por.ShippingFee,por.SourceLogicOrderId,poir.PurchaseOrderSubItemId
FROM PurchaseOrderRelation por WITH(NOLOCK) 
INNER JOIN PurchaseOrderItemRelation poir WITH(NOLOCK) ON por.PurchaseRelationCode=poir.PurchaseRelationCode
INNER JOIN LogicOrder lo WITH(NOLOCK) ON lo.LogicOrderId =por.SourceLogicOrderId 
WHERE por.PurchaseOrderFxUserId={purchaseFxUserId}
AND lo.ErpState ='close' AND por.PurchaseOrderStatus='waitsellersend' AND (por.[PurchaseOrderRefundStatus] IS NULL OR por.PurchaseOrderRefundStatus='')";

            //不关联逻辑单
            if (purchasePlatformOrderIds != null && purchasePlatformOrderIds.Where(x => string.IsNullOrEmpty(x) == false).Any())
            {
                purchasePlatformOrderIds = purchasePlatformOrderIds.Where(x => string.IsNullOrEmpty(x) == false).ToList();
                sql = $@"SELECT por.PurchaseRelationCode,por.PurchasePlatformOrderId,por.PurchaseOrderShopId,por.CreateFxUserId,por.PurchaseOrderHopeSettlementPrice,por.ShippingFee,por.SourceLogicOrderId,poir.PurchaseOrderSubItemId
FROM PurchaseOrderRelation por WITH(NOLOCK) 
INNER JOIN PurchaseOrderItemRelation poir WITH(NOLOCK) ON por.PurchaseRelationCode=poir.PurchaseRelationCode
WHERE por.PurchaseOrderFxUserId={purchaseFxUserId} AND por.PurchasePlatformOrderId IN ('{string.Join("','", purchasePlatformOrderIds)}')
AND por.PurchaseOrderStatus='waitsellersend' AND (por.[PurchaseOrderRefundStatus] IS NULL OR por.PurchaseOrderRefundStatus='')";
            }

            #endregion


            Log.WriteLine($"purchaseFxUserId={purchaseFxUserId}，purchasePlatformOrderIds={purchasePlatformOrderIds?.ToJson()}，needDbs={needDbs.Select(a => a.DbNameConfig.DbName).ToJson()}", logFileName);

            #region 2.跨库/跨云平台查询需要退款的订单
            needDbs.ForEach(oDb =>
            {
                var dbLocation = oDb.DbServer.Location;
                if (dbLocation == CustomerConfig.CloudPlatformType)
                {
                    //同云，直接查询
                    var curList = new PurchaseOrderRelationService(oDb.ConnectionString).GetRefundSuccessByPurchaseFxUserId(purchaseFxUserId, purchasePlatformOrderIds).ToList();
                    if (curList != null && curList.Any())
                        needPurchaseOrderRelations.AddRange(curList);

                    Log.WriteLine($"purchaseFxUserId={purchaseFxUserId}，{oDb.DbNameConfig.DbName}同云查询到需要退款的数据：{curList?.Select(a => a.PurchasePlatformOrderId).ToJson()}", logFileName);
                }
                else
                {
                    //跨云
                    var dbApi = new DbAccessUtility(new ApiDbConfigModel { DbNameConfigId = oDb.DbNameConfig.Id, Location = dbLocation, PlatformType = dbLocation });
                    var curList = dbApi.Query<PurchaseOrderRelation>(sql).ToList();
                    if (curList != null && curList.Any())
                        needPurchaseOrderRelations.AddRange(curList);

                    Log.WriteLine($"purchaseFxUserId={purchaseFxUserId}，{oDb.DbNameConfig.DbName}跨云查询到需要退款的数据：{curList?.Select(a => a.PurchasePlatformOrderId).ToJson()}", logFileName);
                }
            });

            if (needPurchaseOrderRelations == null || needPurchaseOrderRelations.Any() == false)
            {
                Log.WriteLine($"purchaseFxUserId={purchaseFxUserId}，未查询到需要退款的数据", logFileName);
                return new List<PurchaseOrderRelation>();
            }

            //整理子项
            var dicPor = new Dictionary<string, PurchaseOrderRelation>();
            needPurchaseOrderRelations.ForEach(por =>
            {
                if (dicPor.ContainsKey(por.PurchaseRelationCode) == false)
                    dicPor.Add(por.PurchaseRelationCode, por);

                var p = dicPor[por.PurchaseRelationCode];
                if (p.PurchaseOrderSubItemIds == null)
                    p.PurchaseOrderSubItemIds = new List<string>();

                if (p.PurchaseOrderSubItemIds.Any(a => a == por.PurchaseOrderSubItemId) == false)
                    p.PurchaseOrderSubItemIds.Add(por.PurchaseOrderSubItemId);
            });
            needPurchaseOrderRelations = dicPor.Values.ToList();
            #endregion


            Log.WriteLine($"purchaseFxUserId={purchaseFxUserId}，needPurchaseOrderRelations={needPurchaseOrderRelations.Select(a => a.PurchasePlatformOrderId).ToJson()}", logFileName);

            #region 3.精选各业务库查原始单
            var orders = new List<Order>();
            var fields = new List<string> { "o.Id", "o.ShopId", "o.PlatformOrderId", "o.TotalAmount", "o.ShippingFee" };
            //var sqlQueryOrder = $@"SELECT {string.Join(",", fields)} FROM P_Order o WITH(NOLOCK) WHERE o.PlatformOrderId IN @pids AND o.ShopId IN @sids";
            var sqlQueryOrder = $@"SELECT {string.Join(",", fields)} FROM P_Order o WITH(NOLOCK) INNER JOIN dbo.FunStringToTable(@pidStr,',') fstt ON o.PlatformOrderId = fstt.item WHERE o.ShopId IN @sids";

            var chunks = needPurchaseOrderRelations.ChunkList(500);
            chunks.ForEach(chunk =>
            {
                var pids = chunk.Select(a => a.PurchasePlatformOrderId).Distinct().ToList();
                var sids = chunk.Select(a => a.PurchaseOrderShopId).Distinct().ToList();
                var parameters = new DynamicParameters();
                parameters.Add("pidStr", string.Join(",", pids));
                parameters.Add("sids", sids);

                needDbs.Where(a => a.DbServer.Location == CloudPlatformType.Alibaba.ToString()).ToList().ForEach(oDb =>
                {
                    var dbLocation = oDb.DbServer.Location;
                    if (dbLocation == CustomerConfig.CloudPlatformType)
                    {
                        //同云，直接查询
                        var curList = new OrderService(oDb.ConnectionString).GetOrdersForSimple(pids, sids, fields).ToList();
                        if (curList != null && curList.Any())
                            orders.AddRange(curList);
                    }
                    else
                    {
                        //跨云
                        var dbApi = new DbAccessUtility(new ApiDbConfigModel { DbNameConfigId = oDb.DbNameConfig.Id, Location = dbLocation, PlatformType = dbLocation });
                        var curList = dbApi.Query<Order>(sqlQueryOrder, new { pidStr = string.Join(",", pids), sids }).ToList();
                        if (curList != null && curList.Any())
                            orders.AddRange(curList);
                    }
                });
            });
            #endregion

            Log.WriteLine($"purchaseFxUserId={purchaseFxUserId}，orders={orders?.Select(a => a.PlatformOrderId).ToJson()}", logFileName);

            #region 4.退款金额\运费使用原始单
            needPurchaseOrderRelations.ForEach(por =>
            {
                var exist = orders.FirstOrDefault(a => a.PlatformOrderId == por.PurchasePlatformOrderId);
                if (exist != null)
                {
                    por.PurchaseOrderHopeSettlementPrice = exist.TotalAmount ?? 0;
                    por.ShippingFee = exist.ShippingFee;
                }
            });
            #endregion

            return needPurchaseOrderRelations;
        }

        /// <summary>
        /// 获取待发货及之前状态的数据
        /// </summary>
        /// <param name="purchaseFxUserId"></param>
        /// <param name="fxUserId"></param>
        /// <param name="strFields"></param>
        /// <returns></returns>
        public List<PurchaseOrderRelation> GetWaitSendByPurchaseFxUserId(int purchaseFxUserId, int fxUserId = 0 , string strFields = "PurchaseRelationCode,SourcePlatformType,SourceLogicOrderId,CreateFxUserId")
        {
            return _repository.GetWaitSendByPurchaseFxUserId(purchaseFxUserId, fxUserId, strFields);
        }

        /// <summary>
        /// 是否有待付款的采购单（所有精选库都查）
        /// </summary>
        /// <param name="agentFxUserId">商家Id</param>
        /// <returns></returns>
        public Tuple<bool, string> HasWaitPayPurchaseOrderSourcePlatformTypeByAgent(int agentFxUserId)
        {
            var poRelations = new List<PurchaseOrderRelation>();
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                #region 所有精选库都查询
                //1.查出商家所有精选库
                var dbList = new DbConfigRepository().GetListByFxUserIds(new List<int> { agentFxUserId },
                    new List<string>
                    {
                        CloudPlatformType.Alibaba.ToString(),
                        CloudPlatformType.TouTiao.ToString(),
                        CloudPlatformType.Pinduoduo.ToString(),
                        //CloudPlatformType.Jingdong.ToString()
                    });
                //2.存在新旧库情况，则需要按FromFxDbConfig标识分组排序
                var alibabaDbs = dbList
                    .GroupBy(m => new { m.DbServer.Location, m.DbConfig.UserId })
                    .Select(m => m.OrderByDescending(o => o.FromFxDbConfig).First()).ToList();
                //3.逐个库查询
                alibabaDbs.ForEach(m =>
                {
                    if (m.DbServer.Location == CustomerConfig.CloudPlatformType)
                    {
                        var models = new PurchaseOrderRelationRepository(m.ConnectionString)
                            .HasWaitPayPurchaseOrderSourcePlatformTypeByAgent(agentFxUserId);
                        if (models != null && models.Any())
                            poRelations.AddRange(models);
                    }
                    else
                    {
                        const string sql =
                            "SELECT SourcePlatformType,SourceLogicOrderId FROM PurchaseOrderRelation (NOLOCK) WHERE CreateFxUserId=@agentFxUserId AND Status=1 AND PurchaseOrderStatus='waitpay' AND CreateTime>=DATEADD(DAY,-3,GETDATE())";
                        var apiDbConfig = new ApiDbConfigModel
                        {
                            DbNameConfigId = m.DbNameConfig.Id, Location = m.DbServer.Location,
                            PlatformType = m.DbServer.Location
                        };
                        var dbApi = new DbAccessUtility(apiDbConfig);
                        var models = dbApi.Query<PurchaseOrderRelation>(sql, new { agentFxUserId });
                        if (models != null && models.Any())
                        {
                            poRelations.AddRange(models);
                        }
                    }
                });
                #endregion
            }
            else
                poRelations = _repository.HasWaitPayPurchaseOrderSourcePlatformTypeByAgent(agentFxUserId);

            //结果转换：各个平台有多少数量的待付款采购单
            var cloudPlatformTypes = new List<string>();
            var dicLogicId = new Dictionary<string, string>();
            var dicPlatformType = new Dictionary<string, int>();
            poRelations.ForEach(por => {
                if (dicLogicId.ContainsKey(por.SourceLogicOrderId) == false)
                {
                    if (string.IsNullOrEmpty(por.SourcePlatformType))
                        por.SourcePlatformType = "其他";
                    dicLogicId.Add(por.SourceLogicOrderId, por.SourcePlatformType);
                    if (dicPlatformType.ContainsKey(por.SourcePlatformType) == false)
                    {
                        dicPlatformType.Add(por.SourcePlatformType,1);
                    }
                    else
                    {
                        dicPlatformType[por.SourcePlatformType] += 1;
                    }
                }
            });

            var strResult = new StringBuilder();
            dicPlatformType.ToList().ForEach(p => {
                strResult.Append($"{CustomerConfig.ConvertPlatformTypeToName(p.Key)}：{p.Value}单<br/>");
            });

            if (dicPlatformType.Any())
                return Tuple.Create(true, strResult.ToString());
            else
                return Tuple.Create(false, "");
        }

        /// <summary>
        /// 待拆单的平台单号
        /// </summary>
        /// <param name="pids"></param>
        /// <returns></returns>
        public List<string> GetWaitSplitPids(List<string> pids)
        {
            if (pids == null || pids.Any() == false)
                return new List<string>();

            var list = new List<string>();
            var batchSize = 500;
            var count = Math.Ceiling(pids.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = pids.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetWaitSplitPids(batchCodes);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 是否存在待发货状态的数据
        /// </summary>
        /// <param name="purchaseOrderShopId"></param>
        /// <param name="agentFxUserId">指定商家，若小于等于0不参与条件</param>
        /// <returns></returns>
        public bool IsHasWaitSendByPurchaseOrderShopId(int purchaseOrderShopId, int agentFxUserId = 0)
        {
            return _repository.IsHasWaitSendByPurchaseOrderShopId(purchaseOrderShopId, agentFxUserId);
        }

        /// <summary>
        /// 是否存在待发货状态的数据
        /// </summary>
        /// <param name="supplierFxUserId">厂家用户Id</param>
        /// <param name="agentFxUserId">指定商家，若小于等于0不参与条件</param>
        /// <returns></returns>
        public bool IsHasWaitSendBySupplierId(int supplierFxUserId, int agentFxUserId = 0)
        {
            return _repository.IsHasWaitSendBySupplierId(supplierFxUserId, agentFxUserId);
        }

        /// <summary>
        /// 1688采购单订单支付成功，接受到成功支付消息后更改相应的状态
        /// </summary>
        /// <param name="purchasePlatformOrderIds"></param>
        /// <param name="curFxUserId"></param>
        /// <param name="fromMessage">是否来自消息消费</param>
        public void UpdatePurchasePayOrderStatus(List<string> purchasePlatformOrderIds, int curFxUserId, bool fromMessage = false)
        {
            if (purchasePlatformOrderIds == null || purchasePlatformOrderIds.Any() == false)
                return;

            var logFileName = $"1688-QingPay-{DateTime.Now.ToString("yyyyMMdd")}.txt";

            Log.WriteLine($"UpdatePurchasePayOrderStatus{baseRepository.DbConnection.Database},purchasePlatformOrderIds={purchasePlatformOrderIds.ToJson()}", logFileName);

            _repository.UpdatePurchasePayOrderStatus(purchasePlatformOrderIds);
            //同步到副本
            //必须先同步副本，再操作审核，不然可能订单可能无法在厂家那边审核通过！
            //1688采购单支付成功后，自动审核通过订单
            var purchaseOrders = GetByPids(purchasePlatformOrderIds, status: 1);
            if (purchaseOrders == null || purchaseOrders.Any() == false)
            {
                Log.WriteWarning($"根据采购单ID没有查询到逻辑单信息，{_repository.DbConnection.Database}，curFxUserId：{curFxUserId}，采购单ID:{purchasePlatformOrderIds?.ToJson()}", logFileName);
                return;
            }
            var sourceLogicOrderIds = purchaseOrders.Select(x => x.SourceLogicOrderId).Where(x => string.IsNullOrEmpty(x) == false).ToList();
            if (sourceLogicOrderIds == null || sourceLogicOrderIds.Any() == false)
            {
                Log.WriteWarning($"采购单数据中缺少有逻辑单信息，所在库={_repository.DbConnection.Database}，curFxUserId：{curFxUserId}，采购单ID:{purchasePlatformOrderIds?.ToJson()}，sourceLogicOrderIds：{sourceLogicOrderIds?.ToJson()}", logFileName);
                return;
            }

            if (fromMessage && CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                #region 针对消息：精选云平台，三个库都处理
                //1.查出精选库
                var dbList = new DbConfigRepository().GetFxAllCloudPlatformBusinessDbConfigsByAlibaba();
                var alibabaDbs = dbList.ToList();

                //2.三个精选库都执行
                alibabaDbs.ForEach(oDb =>
                {
                    var curConnectionString = oDb.ConnectionString;
                    var checkorder2 = new TemplateCheckOrder(new AssignLogicOrderFactory(), new SplistLogicOrderFactory(), new CheckOrderFactory(), curConnectionString);
                    var logicorders2 = checkorder2.GetOrdersByLogicOrderIds(sourceLogicOrderIds);
                    Log.WriteLine($"{oDb.DbNameConfig?.DbName},logicorders2={logicorders2?.Select(a => a.LogicOrderId).ToJson()}", logFileName);
                    if (logicorders2 != null && logicorders2.Any())
                    {
                        #region 生成标签信息
                        var orderTagService = new OrderTagService(curConnectionString);
                        var now = DateTime.Now;
                        var tasg = logicorders2.Select(x => new OrderTags
                        {
                            Sid = x.ShopId,
                            OiCode = x.LogicOrderId,
                            Platform = "",
                            Tag = OrderTag.AliFxPurchase.ToString(),
                            TagType = "LogicOrder",
                            Status = 0,
                            CreateTime = now,
                            TagValue = "",
                            PathFlowCode = x.PathFlowCode
                        }).ToList();
                        orderTagService.BulkInsertWithNoDataChangeLog(tasg, false);
                        #endregion
                        //不需要
                        //new SyncDataInterfaceService(curFxUserId).PayAlibabaFxOrder(purchaseOrders, logicorders);
                        checkorder2.LogicOrderDoCheck(curFxUserId, logicorders2);
                    }
                });
                #endregion

                return;
            }

            var checkorder = new TemplateCheckOrder(new AssignLogicOrderFactory(), new SplistLogicOrderFactory(), new CheckOrderFactory(), _connectionString);
            var logicorders = checkorder.GetOrdersByLogicOrderIds(sourceLogicOrderIds);
            Log.WriteLine($"{baseRepository.DbConnection.Database},logicorders={logicorders?.Select(a => a.LogicOrderId).ToJson()}", logFileName);
            if (logicorders != null && logicorders.Any())
            {
                #region 生成标签信息
                var orderTagService = new OrderTagService(_connectionString);
                var now = DateTime.Now;
                var tasg = logicorders.Select(x => new OrderTags
                {
                    Sid = x.ShopId,
                    OiCode = x.LogicOrderId,
                    Platform = "",
                    Tag = OrderTag.AliFxPurchase.ToString(),
                    TagType = "LogicOrder",
                    Status = 0,
                    CreateTime = now,
                    TagValue = "",
                    PathFlowCode = x.PathFlowCode
                }).ToList();
                orderTagService.BulkInsertWithNoDataChangeLog(tasg, true);
                #endregion

                //Log.WriteLine($"{orderTagService.baseRepository.DbConnection.Database},tasg={tasg.ToJson(true)}");

                new SyncDataInterfaceService(curFxUserId).PayAlibabaFxOrder(purchaseOrders, logicorders);
                //var checkorder = new TemplateCheckOrder(new AssignLogicOrderFactory(), new SplistLogicOrderFactory(), new CheckOrderFactory());
                checkorder.LogicOrderDoCheck(curFxUserId, logicorders);
            }
        }

        /// <summary>
        /// 向各云平台发送RabbitMQ消息
        /// </summary>
        /// <param name="models"></param>
        /// <param name="curFxUserId"></param>
        /// <param name="tag"></param>
        public void ProcessPurchaseStatusSendRabbitMQMessage(List<Order> models, int curFxUserId, string tag = "")
        {
            if (models == null || models.Any() == false)
                return;

            //过滤：只取1688平台的订单
            var needModels = models.Where(a => a.PlatformType == PlatformType.Alibaba.ToString()).ToList();
            if (needModels.Any() == false)
                return;

            //判断用户是否开启过预付
            var isOpenedPrePay = new SupplierUserRepository().SupplierOrAgentIsOpenedPrePay(curFxUserId);
            if (isOpenedPrePay == false)
                return;

            var logFileName = $"ProcessPurchaseStatusSendRabbitMQMessage-{DateTime.Now.ToString("yyyyMMdd")}.txt";
            var purchasePlatformOrderIds = needModels.Select(a => a.PlatformOrderId).ToList();

            //日志
            Log.WriteLine($"发送1688采购单状态变更消息到消息队列，curFxUserId={curFxUserId}，tag={tag}，purchasePlatformOrderIds：{purchasePlatformOrderIds.ToJson(true)}", logFileName);

            var purchaseOrderRelations = GetByPids(purchasePlatformOrderIds, status: 1);
            //日志
            Log.WriteLine($"发送1688采购单状态变更消息到消息队列，curFxUserId={curFxUserId}，purchaseOrderRelations：{purchaseOrderRelations?.ToJson(true)}", logFileName);
            if (purchaseOrderRelations == null || purchaseOrderRelations.Any() == false)
            {
                Log.WriteWarning($"UpdatePurchaseRefundStatus根据采购单ID没有查询到关系信息，curFxUserId：{curFxUserId}，采购单ID:{purchasePlatformOrderIds?.ToJson()}", logFileName);
                return;
            }

            #region 组装消息内容
            var messageList = new List<UpdatePurchaseStatusModel>();
            purchaseOrderRelations.ForEach(po =>
            {
                var model = needModels.FirstOrDefault(a => a.PlatformOrderId == po.PurchasePlatformOrderId);
                if (model != null)
                {
                    var items = new List<UpdatePurchaseItemStatusModel>();
                    model.OrderItems?.ForEach(oi =>
                    {
                        items.Add(new UpdatePurchaseItemStatusModel
                        {
                            PlatformOrderId = oi.PlatformOrderId,
                            SubItemID = oi.SubItemID,
                            Status = oi.Status,
                            RefundStatus = oi.RefundStatus
                        });
                    });
                    var message = new UpdatePurchaseStatusModel
                    {
                        PlatformOrderId = po.PurchasePlatformOrderId,
                        PathFlowCode = po.SourcePathFlowCode,
                        SourcePlatformType = po.SourcePlatformType,
                        PlatformStatus = model.PlatformStatus,
                        RefundStatus = model.RefundStatus,
                        TotalAmount = model.TotalAmount,
                        ShippingFee = model.ShippingFee,
                        UserId = model.UserId,
                        ShopId = model.ShopId,
                        Items = items,
                    };
                    messageList.Add(message);
                }
            });
            #endregion

            //日志
            Log.WriteLine($"发送1688采购单状态变更消息到消息队列，消息列表={messageList.ToJson(true)}", logFileName);

            WriteAliLog(messageList, "发送MQ消息", tag);

            var wu = new WebUtils();
            var pddMessageUrl = CustomerConfig.PddMessageUrl;

            #region 向各云平台发送消息
            ThreadPool.QueueUserWorkItem(state =>
            {
                messageList.ForEach(message =>
                {
                    try
                    {
                        //发送消息到消息队列
                        RabbitMQService.SendMessage(message, RabbitMQService.UpdatePurchaseStatusMessageDescription);
                        ////发消息到抖音云消息队列
                        //RabbitMQChannelQueuePoolByTouTiao.SendMessage(message, RabbitMQService.UpdatePurchaseStatusMessageDescription);

                        if (message.SourcePlatformType == PlatformType.TouTiao.ToString() || message.SourcePlatformType == PlatformType.TouTiaoSaleShop.ToString())
                        {
                            //发消息到抖店云
                            RabbitMQQueueToTouTiao.SendMessage(message, RabbitMQService.UpdatePurchaseStatusMessageDescription);
                        }
                        else if (message.SourcePlatformType == PlatformType.Pinduoduo.ToString() || message.SourcePlatformType == PlatformType.KuaiTuanTuan.ToString())
                        {
                            //发消息到拼多多云
                            try
                            {
                                var curMsg = new CloudMessageModel() { 
                                    Msg = message.ToJson(true),
                                    SourceCloud = CustomerConfig.CloudPlatformType,
                                    TargetCloud = CloudPlatformType.Pinduoduo.ToString(),
                                    MsgType = "UpdatePurchaseStatus"
                                };
                                //加密
                                var lastMsg = DES.EncryptDES(curMsg.ToJson(true), CustomerConfig.LoginCookieEncryptKey);
                                var requestBody = Encoding.UTF8.GetBytes(lastMsg);
                                string content = wu.DoPostByRequestStream(pddMessageUrl, requestBody);

                                Log.WriteLine($"发送1688采购单状态变更消息到消息队列，单条消息到PDD，URL：{pddMessageUrl}，lastMsg={lastMsg}，结果：{content}", logFileName);
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError($"发送1688采购单状态消息到拼多多云{pddMessageUrl}时发生错误，消息内容：{message.ToJson(true)}，错误信息：{ex}", $"ProcessPurchaseStatusSendRabbitMQMessage-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                            }
                        }

                        //日志
                        Log.WriteLine($"发送1688采购单状态变更消息到消息队列，单条消息：{message.ToJson(true)}", logFileName);
                    }
                    catch (Exception e)
                    {
                        Log.WriteError($"发送1688采购单状态变更消息到消息队列时异常，异常原因：{e.Message}，单条消息：{message.ToJson(true)}，堆栈信息：{e.StackTrace}", "ProcessPurchaseStatusSendRabbitMQMessageError.txt");
                    }
                });
            });
            #endregion

        }

        /// <summary>
        /// 更新采购单关系信息状态（Status或RefundStatus有变化时更新）
        /// </summary>
        /// <param name="models"></param>
        /// <param name="isNeedSDI">是否需要同步副本</param>
        public void UpdatePurchaseStatus(List<UpdatePurchaseStatusModel> models, bool isNeedSDI = true)
        {
            if (models == null || models.Any() == false)
                return;

            ////支付状态更新补偿
            //if (CustomerConfig.IsDebug)
            //{
            //    var paidPids = models.Where(a => a.PlatformStatus == OrderStatusType.waitsellersend.ToString()).Select(a => a.PlatformOrderId).Distinct().ToList();
            //    if (paidPids != null && paidPids.Any())
            //    {
            //        Log.WriteLine($"UpdatePurchasePayOrderStatus开始，所在库={baseRepository.DbConnection.Database}，paidPids={paidPids.ToJson()}");
            //        UpdatePurchasePayOrderStatus(paidPids, models.Where(a => a.PlatformStatus == OrderStatusType.waitsellersend.ToString()).First().UserId);
            //    }
            //}

            var logFileName = $"1688-UpdatePurchaseStatus-{DateTime.Now.ToString("yyyyMMdd")}.txt";

            //日志
            var batchNo = Guid.NewGuid().ToString().ToShortMd5();
            WriteAliLog(models, "初始数据", batchNo);

            //第5步，移到前面 2024-05-30
            #region 5.实时同步到其他副本库 && 其他精选库
            if (isNeedSDI)
            {
                var curFxUserId = models.First().UserId;
                new SyncDataInterfaceService(curFxUserId).UpdatePurchaseStatus(models);

                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                {
                    //查出其他精选库
                    var dbList = _configRepository.GetFxAllCloudPlatformBusinessDbConfigsByAlibaba();
                    var otherAlibabaDbs = dbList.Where(a => a.DbNameConfig.DbName != _repository.DbConnection.Database).ToList();

                    //其他精选库也要同步执行
                    otherAlibabaDbs?.ForEach(oDb =>
                    {
                        WriteAliLog(models, oDb.DbNameConfig?.DbName, batchNo);
                        new PurchaseOrderRelationService(oDb.ConnectionString).UpdatePurchaseStatus(models, false);
                    });

                }
                else if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
                {
                    //查出拼多多旧库
                    var dbList = _configRepository.GetFxAllCloudPlatformBusinessDbConfigs();
                    var otherPinduoduoDbs = dbList.Where(a => a.DbServer.Location == CloudPlatformType.Pinduoduo.ToString() && a.DbNameConfig.DbName != _repository.DbConnection.Database).ToList();

                    //其他拼多多旧库也要同步执行
                    otherPinduoduoDbs?.ForEach(oDb =>
                    {
                        WriteAliLog(models, oDb.DbNameConfig?.DbName, batchNo);
                        new PurchaseOrderRelationService(oDb.ConnectionString).UpdatePurchaseStatus(models, false);
                    });

                }
            }
            #endregion

            #region 1.获取采购单关系信息
            var purchasePlatformOrderIds = models.Select(a => a.PlatformOrderId).ToList();
            var purchaseOrderRelations = GetByPids(purchasePlatformOrderIds, status: 1);
            if (purchaseOrderRelations == null || purchaseOrderRelations.Any() == false)
            {
                Log.WriteWarning($"UpdatePurchaseStatus，所在库={_repository.DbConnection.Database}根据采购单ID没有查询到关系信息，采购单ID:{purchasePlatformOrderIds?.ToJson()}", logFileName);
                return;
            }
            var sourceLogicOrderIds = purchaseOrderRelations.Select(x => x.SourceLogicOrderId).Where(x => string.IsNullOrEmpty(x) == false).ToList();
            if (sourceLogicOrderIds == null || sourceLogicOrderIds.Any() == false)
            {
                Log.WriteWarning($"UpdatePurchaseStatus，所在库={_repository.DbConnection.Database}采购单数据中缺少有关系信息，采购单ID:{purchasePlatformOrderIds?.ToJson()}，sourceLogicOrderIds：{sourceLogicOrderIds?.ToJson()}", logFileName);
                return;
            }

            //过滤：只取有关联关系的数据
            var purchaseRelationPlatformOrderIds = purchaseOrderRelations.Select(x => x.PurchasePlatformOrderId).Where(x => string.IsNullOrEmpty(x) == false).ToList();
            models = models.Where(a => purchaseRelationPlatformOrderIds.Contains(a.PlatformOrderId)).ToList();

            WriteAliLog(models, "有关联关系的数据", batchNo);

            //采购单关系项信息
            var purchaseRelationCodes = purchaseOrderRelations.Select(m => m.PurchaseRelationCode).Distinct().ToList();
            var purchaseOrderItemRelationService = new PurchaseOrderItemRelationService(_connectionString);
            var purchaseOrderItemRelations = purchaseOrderItemRelationService.GetListForDuplication(purchaseRelationCodes, new List<string>() { "PurchaseRelationCode", "PurchasePlatformOrderId", "PurchaseOrderSubItemId", "PurchaseOrderItemStatus" });

            #endregion

            //已付款的状态
            var payedStatus = new List<string> { OrderStatusType.waitsellersend.ToString(), OrderStatusType.waitbuyerreceive.ToString(), OrderStatusType.success.ToString(), "waitbuyerreceiver" };

            #region 2.更新RefundStatus、退款成功触发退审
            //需要更新RefundStatus的订单，直接更新
            var needUpdateRefundStatusOrders = models.Where(a => string.IsNullOrEmpty(a.RefundStatus) == false).ToList();

            //存在买家申请退款，但买家在平台上查不到退款记录，RefundStatus为空的情况，PurchaseRefundStatus更新为空
            needUpdateRefundStatusOrders.AddRange(models.Where(a => string.IsNullOrEmpty(a.RefundStatus) && payedStatus.Contains(a.PlatformStatus)));

            if (needUpdateRefundStatusOrders != null && needUpdateRefundStatusOrders.Any())
            {
                //Log.WriteLine($"更新退款状态：needUpdateRefundStatusOrders={needUpdateRefundStatusOrders.ToJson(true)}", logFileName);
                //已退款成功的 --> 作一次退审：从商家身份账户退审到待审核列表，对厂家是不可见
                var uncheckPurchasePIds = needUpdateRefundStatusOrders.Where(a => a.RefundStatus?.ToLower() == "refundsuccess").Select(a => a.PlatformOrderId).Distinct().ToList();
                var uncheckLogicOrderIds = purchaseOrderRelations.Where(a => uncheckPurchasePIds.Contains(a.PurchasePlatformOrderId) && a.Status == 1).Select(a => a.SourceLogicOrderId).Distinct().ToList();
                if (uncheckLogicOrderIds != null && uncheckLogicOrderIds.Any())
                {
                    Log.WriteLine($"存在退款成功的订单：uncheckLogicOrderIds={uncheckLogicOrderIds.ToJson()}", logFileName);
                    try
                    {
                        if (uncheckLogicOrderIds != null && uncheckLogicOrderIds.Any())
                        {
                            var checkorder = new TemplateCheckOrder(new AssignLogicOrderFactory(), new SplistLogicOrderFactory(), new CheckOrderFactory(), _connectionString);
                            var uncheckLogicOrders = new LogicOrderService(_connectionString).GetLogicOrders(uncheckLogicOrderIds).ToList();

                            //只取待发货状态的逻辑单 --> 退审
                            uncheckLogicOrders = uncheckLogicOrders?.Where(a => a.ErpState == OrderStatusType.waitsellersend.ToString()).ToList();

                            //Log.WriteLine($"需要退审的逻辑单：uncheckLogicOrders={uncheckLogicOrders.Select(a => a.LogicOrderId).ToJson()}", logFileName);

                            if (uncheckLogicOrders != null && uncheckLogicOrders.Any())
                            {
                                //按商家维度处理
                                uncheckLogicOrders.GroupBy(g => g.FxUserId).ToList().ForEach(g =>
                                {
                                    //Log.WriteLine($"执行退审：FxUserId={g.Key}，LogicOrderIds={g.ToList().Select(a => a.LogicOrderId).ToJson()}", logFileName);
                                    checkorder.LogicOrderUnCheck(g.Key, g.ToList());
                                });
                            }
                        }
                    }
                    catch(Exception ex)
                    {
                        Log.WriteError($"UpdatePurchaseStatus-LogicOrderUnCheck，所在库={_repository.DbConnection.Database}退审时异常，逻辑单ID:{uncheckLogicOrderIds?.ToJson()}，异常：{ex}");
                        WriteAliLog(needUpdateRefundStatusOrders.Where(a => a.RefundStatus == "refundsuccess").ToList(), "退款成功-触发退审时异常", batchNo);
                    }

                }

                _repository.UpdatePurchaseRefundStatus(needUpdateRefundStatusOrders);
            }

            #endregion

            #region 3.更新Status，只能正向更新 | 更新金额相关
            //对比状态：只能正向更新
            var dicSeqNumber = new Dictionary<string, int>();
            dicSeqNumber.Add("waitbuyerpay", 1);          //待支付
            dicSeqNumber.Add("waitpay", 1);               //待支付

            dicSeqNumber.Add("waitpay_cancel", 2);        //待支付-取消

            dicSeqNumber.Add("paying", 2);               //付款中
            dicSeqNumber.Add("paying_back_waitpay", 3);  //付款中-->退回待支付

            dicSeqNumber.Add("waitsellersend", 3);        //待发货
            dicSeqNumber.Add("waitlogisticstakein", 3);   //等待物流公司揽件

            dicSeqNumber.Add("sended", 4);                //已发货
            dicSeqNumber.Add("waitbuyerreceive", 4);      //待收货
            dicSeqNumber.Add("waitbuyerreceiver", 4);     //待收货
            dicSeqNumber.Add("waitbuyersign", 4);         //等待买家签收

            dicSeqNumber.Add("signinsuccess", 5);         //买家已签收
            dicSeqNumber.Add("confirm_goods", 5);         //已确认收货

            dicSeqNumber.Add("refund", 6);                //已退款（所有子项都退款）
            dicSeqNumber.Add("success", 7);               //交易成功

            dicSeqNumber.Add("close", 8);                 //交易关闭
            dicSeqNumber.Add("cancel", 8);                //交易取消
            dicSeqNumber.Add("terminated", 8);            //交易终止

            //需要更新PurchaseOrderStatus的订单
            var needUpdateStatusOrders = new List<UpdatePurchaseStatusModel>();
            //需要更新PurchaseOrder金额相关的订单 运费/总金额
            var needUpdateAmountPurchaseOrders = new List<UpdatePurchaseStatusModel>();
            purchaseOrderRelations.ForEach(o =>
            {
                var existModel = models.FirstOrDefault(a => a.PlatformOrderId == o.PurchasePlatformOrderId);
                if (existModel != null)
                {
                    //路径流赋值
                    existModel.PathFlowCode = o.SourcePathFlowCode;
                    existModel.PurchaseRelationCode = o.PurchaseRelationCode;

                    var dbStatusSeqNumber = 0;
                    var apiStatusSeqNumber = 0;
                    if (string.IsNullOrEmpty(o.PurchaseOrderStatus) == false && dicSeqNumber.ContainsKey(o.PurchaseOrderStatus))
                        dbStatusSeqNumber = dicSeqNumber[o.PurchaseOrderStatus];
                    if (string.IsNullOrEmpty(existModel.PlatformStatus) == false && dicSeqNumber.ContainsKey(existModel.PlatformStatus))
                        apiStatusSeqNumber = dicSeqNumber[existModel.PlatformStatus];

                    var items = new List<UpdatePurchaseItemStatusModel>();
                    if (existModel.Items != null && existModel.Items.Any())
                    {
                        existModel.Items.Where(a => string.IsNullOrEmpty(a.Status) == false).ToList().ForEach(item =>
                        {
                            var existItem = purchaseOrderItemRelations.FirstOrDefault(a => a.PurchasePlatformOrderId == item.PlatformOrderId && a.PurchaseOrderSubItemId == item.SubItemID);
                            if (existItem != null)
                            {
                                var dbItemStatusSeqNumber = 0;
                                var apiItemStatusSeqNumber = 0;
                                if (string.IsNullOrEmpty(existItem.PurchaseOrderItemStatus) == false && dicSeqNumber.ContainsKey(existItem.PurchaseOrderItemStatus))
                                    dbItemStatusSeqNumber = dicSeqNumber[existItem.PurchaseOrderItemStatus];
                                if (string.IsNullOrEmpty(item.Status) == false && dicSeqNumber.ContainsKey(item.Status))
                                    apiItemStatusSeqNumber = dicSeqNumber[item.Status];

                                //只能正向更新
                                if (apiItemStatusSeqNumber > dbItemStatusSeqNumber)
                                    items.Add(item);
                            }
                        });
                    }

                    //只能正向更新
                    if (apiStatusSeqNumber > dbStatusSeqNumber)
                    {
                        //未付款退款
                        if (existModel.PlatformStatus == PurchaseOrderRelationEnumStatus.cancel.ToString() && existModel.RefundStatus.IsNullOrEmpty() && o.PurchaseOrderStatus == PurchaseOrderRelationEnumStatus.waitpay.ToString())
                            existModel.PlatformStatus = PurchaseOrderRelationEnumStatus.waitpay_cancel.ToString();

                        //从付款中-->退回到待支付
                        if (existModel.PlatformStatus == PurchaseOrderRelationEnumStatus.paying_back_waitpay.ToString())
                            existModel.PlatformStatus = PurchaseOrderRelationEnumStatus.waitpay.ToString();
                        existModel.Items = items;
                        needUpdateStatusOrders.Add(existModel);
                    }
                    
                    //更新采购单总金额和运费字段
                    if(existModel.PlatformStatus == PurchaseOrderRelationEnumStatus.waitpay.ToString())
                    {
                        bool neetUpdateAmount = false;
                        if (existModel.TotalAmount != null && existModel.TotalAmount != o.PurchaseTotalAmount)
                            neetUpdateAmount = true;
                        else
                            existModel.TotalAmount = o.PurchaseTotalAmount;

                        if (existModel.ShippingFee != null && existModel.ShippingFee != o.ShippingFee)
                            neetUpdateAmount = true;
                        else
                            existModel.ShippingFee = o.ShippingFee;

                        if (neetUpdateAmount)
                            needUpdateAmountPurchaseOrders.Add(existModel);
                    }
                }
            });

            if (needUpdateStatusOrders != null && needUpdateStatusOrders.Any())
            {
                //已付款状态单独更新：同步更新支付状态
                var paidPids = needUpdateStatusOrders.Where(a => a.PlatformStatus == OrderStatusType.waitsellersend.ToString()).Select(a => a.PlatformOrderId).Distinct().ToList();
                if (paidPids != null && paidPids.Any())
                {
                    UpdatePurchasePayOrderStatus(paidPids, models.Where(a => a.PlatformStatus == OrderStatusType.waitsellersend.ToString()).First().UserId);
                    Log.WriteLine($"UpdatePurchasePayOrderStatus完成，所在库={baseRepository.DbConnection.Database}，paidPids={paidPids.ToJson()}", $"1688-QingPay-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                }

                _repository.UpdatePurchaseStatus(needUpdateStatusOrders);

                //日志
                WriteAliLog(needUpdateStatusOrders, "needUpdateStatusOrders", batchNo);
            }
            if(needUpdateAmountPurchaseOrders != null && needUpdateAmountPurchaseOrders.Any())
            {
                _repository.needUpdateAmountPurchaseOrders(needUpdateAmountPurchaseOrders);
                //日志
                WriteAliLog(needUpdateAmountPurchaseOrders, "needUpdateAmountPurchaseOrders", batchNo);
            }
            #endregion

            #region 4.加标或去标
            if (isNeedSDI)
            {
                //加标的退款状态
                var needAddTagRefundStatus = new List<string> { "waitselleragree", "waitbuyermodify", "waitbuyersend", "waitsellerreceive", "waitsellerreceiver" };
                //去标的退款状态
                var needRemoveTagRefundStatus = new List<string> { "refundclose", "refundsuccess" };

                var orderTagService = new OrderTagService(_connectionString);
                var now = DateTime.Now;
                var tags = new List<OrderTags>();
                purchaseOrderRelations.ForEach(o =>
                {
                    var existModel = models.FirstOrDefault(a => a.PlatformOrderId == o.PurchasePlatformOrderId);
                    if (existModel != null)
                    {
                        existModel.PathFlowCode = o.SourcePathFlowCode;

                        if (needAddTagRefundStatus.Contains(existModel.RefundStatus))
                        {
                            tags.Add(new OrderTags
                            {
                                Sid = o.SourceShopId,
                                OiCode = o.SourceLogicOrderId,
                                Platform = "",
                                Tag = OrderTag.AliFxPurchaseException.ToString(),
                                TagType = "LogicOrder",
                                Status = 0,
                                CreateTime = now,
                                TagValue = "",
                                PathFlowCode = o.SourcePathFlowCode
                            });
                        }
                        //存在买家申请退款，但买家在平台上查不到退款记录，RefundStatus为空的情况，按去标处理
                        else if (needRemoveTagRefundStatus.Contains(existModel.RefundStatus) || (string.IsNullOrEmpty(existModel.RefundStatus) && payedStatus.Contains(existModel.PlatformStatus)))
                        {
                            tags.Add(new OrderTags
                            {
                                Sid = o.SourceShopId,
                                OiCode = o.SourceLogicOrderId,
                                Platform = "",
                                Tag = OrderTag.AliFxPurchaseException.ToString(),
                                TagType = "LogicOrder",
                                Status = -1,//去标
                                CreateTime = now,
                                TagValue = "",
                                PathFlowCode = o.SourcePathFlowCode
                            });
                        }
                    }
                });

                orderTagService.BulkInsertWithNoDataChangeLog(tags, true);
            }
            #endregion

            #region 5.实时同步到其他副本库 && 其他精选库 -- 已移到前面，此处注释掉
            /*
            if (isNeedSDI)
            {
                var curFxUserId = models.First().UserId;
                new SyncDataInterfaceService(curFxUserId).UpdatePurchaseStatus(models);

                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                {
                    //查出其他精选库
                    var dbList = _configRepository.GetFxAllCloudPlatformBusinessDbConfigs();
                    var otherAlibabaDbs = dbList.Where(a => a.DbServer.Location == CloudPlatformType.Alibaba.ToString() && a.DbNameConfig.DbName != _repository.DbConnection.Database).ToList();

                    //其他精选库也要同步执行
                    otherAlibabaDbs?.ForEach(oDb =>
                    {
                        WriteAliLog(models, oDb.DbNameConfig?.DbName, batchNo);
                        new PurchaseOrderRelationService(oDb.ConnectionString).UpdatePurchaseStatus(models, false);
                    });

                }
                else if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
                {
                    //查出拼多多旧库
                    var dbList = _configRepository.GetFxAllCloudPlatformBusinessDbConfigs();
                    var otherPinduoduoDbs = dbList.Where(a => a.DbServer.Location == CloudPlatformType.Pinduoduo.ToString() && a.DbNameConfig.DbName != _repository.DbConnection.Database).ToList();

                    //其他拼多多旧库也要同步执行
                    otherPinduoduoDbs?.ForEach(oDb =>
                    {
                        WriteAliLog(models, oDb.DbNameConfig?.DbName, batchNo);
                        new PurchaseOrderRelationService(oDb.ConnectionString).UpdatePurchaseStatus(models, false);
                    });

                }
            }
            */
            #endregion

        }

        /// <summary>
        /// 批量插入数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        /// <param name="isCompensate">是否是补偿：true时=存在忽略，不存在插入</param>
        public void InsertsForDuplication(List<PurchaseOrderRelation> models, bool isCompensate = false)
        {
            //判空处理
            if (models == null || !models.Any())
            {
                return;
            }

            var batchSize = 50;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();

                //唯一代码列表
                var codes = batchModels.Select(m => m.PurchaseRelationCode).Distinct().ToList();
                
                //存在的关联列表
                var idAndCodes = _repository.GetExistIdAndCodes(codes);
                
                //全部不存在
                if (idAndCodes.IsNullOrEmptyList())
                {
                    try
                    {
                        baseRepository.BulkWrite(batchModels, "PurchaseOrderRelation", maxSingleNum: 1);
                    }
                    catch (Exception ex)
                    {
                        var db = baseRepository.DbConnection;
                        if (db.State == System.Data.ConnectionState.Closed)
                        {
                            db.Open();
                        }
                        using (db)
                        {
                            //单条
                            batchModels.ForEach(item =>
                            {
                                try
                                {
                                    db.Insert(item);
                                }
                                catch (Exception ex2)
                                {
                                    var errMsg = ex2.Message.ToLower();
                                    if (errMsg.Contains("pk_") || errMsg.Contains("primary key"))
                                    {
                                        //忽略
                                    }
                                    else
                                    {
                                        throw ex2;
                                    }
                                }
                            });
                        }
                    }
                    continue;
                }
                //存在
                var existsCodes = idAndCodes.Select(m => m.Code).ToList();
                if (isCompensate == false)
                {
                    var needUpdateFields = new List<string>()
                    {
                        "PurchaseTotalAmount","PurchaseOrderHopeSettlementPrice","ShippingFee","PurchaseOrderStatus","PurchaseErrorMessage","Status",
                        "UpdateTime","PurchaseOrderRefundStatus",
                    };
                    var selectFields = new List<string>(needUpdateFields) { "PurchaseRelationCode", "Id" };
                    var existList = _repository.GetListForDuplication(existsCodes, string.Join(",", selectFields));

                    var needUpdates = batchModels.Where(m => existsCodes.Contains(m.PurchaseRelationCode)).ToList();
                    var keyFields = new List<string>() { "PurchaseRelationCode" };
                    if (needUpdates.Any())
                    {
                        needUpdates.ForEach(o =>
                        {
                            var model = existList.FirstOrDefault(m => m.PurchaseRelationCode == o.PurchaseRelationCode);
                            if (model == null)
                            {
                                return;
                            }
                            o.Id = model.Id;
                        });

                        try
                        {
                            var tuple = EntityUtils.CompareFields<PurchaseOrderRelation>(existList, needUpdates, needUpdateFields, keyFields);
                            needUpdates = tuple.Item2;
                            needUpdateFields = tuple.Item1;
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"PurchaseOrderRelation 更新，与数据库旧数据比较失败：{ex}，将使用旧逻辑进行全量更新");
                        }
                        _repository.BatchUpdate(needUpdates, needUpdateFields, keyFields);
                        Log.Debug(
                            () => $"PurchaseOrderRelation 副本更新数据{needUpdates.Count}条，更新字段{needUpdateFields.ToJson()}",
                            "wm_order2.txt");

                    }
                }
                //不存在
                var inserts = batchModels.Where(m => !existsCodes.Contains(m.PurchaseRelationCode)).ToList();
                if (inserts.Any())
                {
                    try
                    {
                        baseRepository.BulkWrite(inserts, "PurchaseOrderRelation", maxSingleNum: 1);
                    }
                    catch (Exception ex)
                    {
                        var db = baseRepository.DbConnection;
                        if (db.State == System.Data.ConnectionState.Closed)
                        {
                            db.Open();
                        }
                        using (db)
                        {
                            //单条
                            inserts.ForEach(item =>
                            {
                                try
                                {
                                    db.Insert(item);
                                }
                                catch (Exception ex2)
                                {
                                    var errMsg = ex2.Message.ToLower();
                                    if (errMsg.Contains("pk_") || errMsg.Contains("primary key"))
                                    {
                                        //忽略
                                    }
                                    else
                                    {
                                        throw ex2;
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 更新退款状态
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="status"></param>
        public void UpdateRefundStatus(List<string> codes, string status = "AUTO_REFUND")
        {
            //判空处理
            if (codes == null || !codes.Any())
            {
                return;
            }
            _repository.UpdateRefundStatus(codes, status);
        }

        /// <summary>
        /// 从厂家所在库查询采购单的Order数据
        /// </summary>
        /// <param name="logicOrderIds">SourceLogicOrderIds</param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public List<Order> GetOrdersFromAlibabaCloud(List<string> logicOrderIds, int curFxUserId)
        {
            var models = GetListAndItems(logicOrderIds);
            var lastResult = new List<Order>();
            if (models == null || models.Any() == false)
                return lastResult;
            Log.WriteLine($"获取采购单信息，存在采购单关系，逻辑单号：{logicOrderIds.ToJson()}", $"GetPurchaseOrderInfo_{DateTime.Now:yyyy-MM-dd}.log");
            //只能是商家或厂家才有权限
            models = models.Where(a => a.CreateFxUserId == curFxUserId || a.PurchaseOrderFxUserId == curFxUserId).ToList();

            #region 处理买家店铺名称、商家厂家名称
            SetBuyerShopInfo(models, curFxUserId);
            #endregion

            //基准云平台类型
            var baseCloudPlatformType = CloudPlatformType.Alibaba.ToString();
            //1688供应商用户ID
            var supplierFxUserIds = models.Select(m => m.PurchaseOrderFxUserId).Distinct().ToList();
            var supplierDbConfigs = _configRepository.GetListByFxUserIds(supplierFxUserIds, baseCloudPlatformType);
            //最后分库标识最大的配置
            supplierDbConfigs = supplierDbConfigs.GroupBy(m => m.DbConfig.UserId)
                .Select(m => m.OrderByDescending(o => o.FromFxDbConfig).First()).ToList();

            #region 同云获取数据
            if (CustomerConfig.CloudPlatformType == baseCloudPlatformType)
            {
                //日志
                Log.WriteLine($"获取采购单信息，同云获取", $"GetPurchaseOrderInfo_{DateTime.Now:yyyy-MM-dd}.log");
                var targetDbConfigsByGroup = supplierDbConfigs.GroupBy(m => m.ConnectionString).ToList();
                targetDbConfigsByGroup.ForEach(group =>
                {
                    //同云同步
                    var fxUserIds = group.Select(m => m.DbConfig.UserId).Distinct();
                    //过滤数据
                    var targetModels = models.Where(m => fxUserIds.Contains(m.PurchaseOrderFxUserId)).ToList();
                    var targetPlatformOrderIds = targetModels.Select(m => m.PurchasePlatformOrderId).Distinct().ToList();
                    var targetShopIds = targetModels.Select(m => m.PurchaseOrderShopId).Distinct().ToList();
                    //获取数据
                    var service = new OrderService(group.Key);
                    var result = service.GetOrders(targetPlatformOrderIds, targetShopIds);
                    if (result != null && result.Any())
                    {
                        result.ForEach(o =>
                        {
                            //o.IsSelfShop = o.UserId == curFxUserId;
                            o.PurchaseOrderItems = o.OrderItems;
                            o.PurchaseOrderRelation = targetModels.FirstOrDefault(m => m.PurchasePlatformOrderId == o.PlatformOrderId);
                            //是否为买家自己
                            if (o.PurchaseOrderRelation != null && o.PurchaseOrderRelation.CreateFxUserId == curFxUserId)
                                o.IsSelfShop = true;
                            //处理价格数量，使用ItemRelation里的数量
                            var seqNumber = 0;
                            o.PurchaseOrderItems?.ForEach(oi =>
                            {
                                seqNumber++;
                                oi.IsFirst = seqNumber == 1;
                                //不需要处理，直接用接口的数据 2023.10.8
                                //var itemExist = o.PurchaseOrderRelation?.ItemRelations.FirstOrDefault(a => a.PurchaseOrderItemCode == oi.OrderItemCode);
                                //if (itemExist != null)
                                //{
                                //    oi.Price = itemExist.HopePurchasePrice;
                                //    oi.Count = itemExist.PurchaseOrderItemCount;
                                //}
                            });
                        });
                        lastResult.AddRange(result);
                    }
                });
                return lastResult;
            }
            #endregion

            #region 跨云获取数据
            //日志
            Log.WriteLine($"获取采购单信息，跨云获取", $"GetPurchaseOrderInfo_{DateTime.Now:yyyy-MM-dd}.log");

            var targetDbConfigsByGroup2 = supplierDbConfigs.GroupBy(m => m.ConnectionString).ToList();
            targetDbConfigsByGroup2.ForEach(group =>
            {
                //日志
                Log.WriteLine($"获取采购单信息，跨云获取，跨云库：{group.First().DbNameConfig?.DbName}", $"GetPurchaseOrderInfo_{DateTime.Now:yyyy-MM-dd}.log");
                //初始化同步业务数据服务
                var apiDbConfig = new ApiDbConfigModel(group.First().DbServer.Location, baseCloudPlatformType,
                    group.First().DbNameConfig.Id);
                var _apiAccessUtility = new DbApiAccessUtility(apiDbConfig);
                //日志
                Log.WriteLine($"获取采购单信息，跨云获取，跨云相关信息： {group.First().DbServer.Location}|{group.First().DbNameConfig.Id}", $"GetPurchaseOrderInfo_{DateTime.Now:yyyy-MM-dd}.log");
                //同云同步
                var fxUserIds = group.Select(m => m.DbConfig.UserId).Distinct();
                //过滤数据
                var targetModels = models.Where(m => fxUserIds.Contains(m.PurchaseOrderFxUserId)).ToList();
                var targetPlatformOrderIds = targetModels.Select(m => m.PurchasePlatformOrderId).Distinct().ToList();
                var targetShopIds = targetModels.Select(m => m.PurchaseOrderShopId).Distinct().ToList();

                //获取数据
                //var sql = $@"SELECT * FROM P_Order WITH(NOLOCK) WHERE ShopId IN ({string.Join(",", targetShopIds)}) AND PlatformOrderId IN ('{string.Join("','", targetPlatformOrderIds)}')";
                var sql = $"SELECT o.* FROM P_Order o WITH(NOLOCK) INNER JOIN dbo.FunStringToTable(@pidStr,',') fstt ON o.PlatformOrderId = fstt.item WHERE o.ShopId IN @shopIds";
                var result = _apiAccessUtility.Query<Order>(sql,new { pidStr = string.Join(",", targetPlatformOrderIds), shopIds = targetShopIds });
                //日志
                Log.WriteLine($"获取采购单信息，跨云获取，跨云SQL：{sql}", $"GetPurchaseOrderInfo_{DateTime.Now:yyyy-MM-dd}.log");

                //sql = $@"SELECT * FROM P_OrderItem WITH(NOLOCK) WHERE PlatformOrderId IN ('{string.Join("','", targetPlatformOrderIds)}')";
                sql = $"SELECT oi.* FROM P_OrderItem oi WITH(NOLOCK) INNER JOIN dbo.FunStringToTable(@pidStr,',') fstt ON oi.PlatformOrderId = fstt.item";
                var orderItems = _apiAccessUtility.Query<OrderItem>(sql, new { pidStr = string.Join(",", targetPlatformOrderIds) });

                result?.ForEach(o =>
                {
                    //o.IsSelfShop = o.UserId == curFxUserId;
                    o.PurchaseOrderRelation = targetModels.FirstOrDefault(m => m.PurchasePlatformOrderId == o.PlatformOrderId);
                    //是否为买家自己
                    if (o.PurchaseOrderRelation != null && o.PurchaseOrderRelation.CreateFxUserId == curFxUserId)
                        o.IsSelfShop = true;
                    o.OrderItems = orderItems?.Where(m => m.PlatformOrderId == o.PlatformOrderId).ToList();
                    o.PurchaseOrderItems = o.OrderItems;
                    //处理价格数量，使用ItemRelation里的数量
                    var seqNumber = 0;
                    o.PurchaseOrderItems?.ForEach(oi =>
                    {
                        seqNumber++;
                        oi.IsFirst = seqNumber == 1;
                        //不需要处理，直接用接口的数据 2023.10.8
                        //var itemExist = o.PurchaseOrderRelation?.ItemRelations.FirstOrDefault(a => a.PurchaseOrderItemCode == oi.OrderItemCode);
                        //if (itemExist != null)
                        //{
                        //    oi.Price = itemExist.HopePurchasePrice;
                        //    oi.Count = itemExist.PurchaseOrderItemCount;
                        //}
                    });
                });

                if (result != null && result.Any())
                    lastResult.AddRange(result);
            });
            return lastResult;
            #endregion
        }

        /// <summary>
        /// 设置买家账号、商家厂家名称等相关信息
        /// </summary>
        /// <param name="models"></param>
        public void SetBuyerShopInfo(List<PurchaseOrderRelation> models, int curFxUserId)
        {
            var buyerFxUserIds = models.Select(a => a.CreateFxUserId).Distinct().ToList();
            var buyerShopRelations = new FxAlibabaBuyerShopRelationService().GetListByFxUserIds(buyerFxUserIds);

            // 当前用户的商家和厂家信息
            var supplierUserRepository = new SupplierUserRepository();
            var supplierUsers = supplierUserRepository.GetByFxUserId(curFxUserId, true);
            //var agentUsers = supplierUserRepository.GetByFxUserId(curFxUserId, false);

            var buyerShopIds = models.Where(a => a.BuyerShopId.HasValue && a.BuyerShopId.Value > 0)
                .Select(a => a.BuyerShopId.Value).Distinct().ToList();
            buyerShopIds.AddRange(buyerShopRelations.Select(a => a.BuyerShopId).Distinct());
            buyerShopIds = buyerShopIds.Distinct().ToList();

            if (buyerShopIds.Any())
            {
                var sids = models.Select(x => x.PurchaseOrderShopId).Distinct().ToList();
                buyerShopIds.AddRange(sids);
                var shops = new ShopService().GetListByShopIds(buyerShopIds);
                var curFxUser = BaseSiteContext.Current.CurrentFxUser;

                models.ForEach(m =>
                {
                    //var agentUser = agentUsers.FirstOrDefault(x => x.FxUserId == m.CreateFxUserId && x.SupplierFxUserId == curFxUserId);
                    var supplierUser = supplierUsers.FirstOrDefault(x => x.SupplierFxUserId == m.PurchaseOrderFxUserId && x.FxUserId == curFxUserId);
                    m.ShopName = shops.FirstOrDefault(x => x.Id == m.PurchaseOrderShopId)?.NickName;
                    m.SupplierName = supplierUser == null ? (curFxUser.NickName.IsNullOrEmpty() ? curFxUser.Mobile : curFxUser.NickName) : supplierUser.SupplierMobileAndRemark;

                    if (m.BuyerShopId.HasValue && m.BuyerShopId.Value > 0)
                    {
                        var exist = shops.FirstOrDefault(a => a.Id == m.BuyerShopId.Value);
                        m.BuyerShopName = exist?.ShopName;
                    }
                    else
                    {
                        var buyerShopRelation = buyerShopRelations.FirstOrDefault(a => a.FxUserId == m.CreateFxUserId);
                        if (buyerShopRelation != null)
                        {
                            var exist = shops.FirstOrDefault(a => a.Id == buyerShopRelation.BuyerShopId);
                            m.BuyerShopName = exist?.ShopName;
                        }
                    }
                });
            }
        }

        public Shop GetAlibabaBuyerShop(int fxUserId)
        {
            var buyerShopRelationService = new FxAlibabaBuyerShopRelationService();
            var buyerShopRelation = buyerShopRelationService.GetByFxUserId(fxUserId);
            if (buyerShopRelation == null)
            {
                return null;
            }
            //检查买家账号：提示订购、续费（使用平台类获取过期时间 - 查询的订购记录信息表）、或重新授权（接口调用ping不通时）；
            var shopService = new ShopService();
            var buyerShop = shopService.GetShopAndShopExtension(buyerShopRelation.BuyerShopId);
            return buyerShop;
        }

        /// <summary>
        /// 检查1688买家支付配置
        /// </summary>
        /// <returns></returns>
        public ReturnedModel<string> Check1688BuyerPaySet(int fxUserId)
        {
            //测试
            //return new ReturnedModel<string>
            //{
            //    Data = "2",
            //    Success = true
            //};
            //1.检查是否授权买家账号
            var buyerShop = GetAlibabaBuyerShop(fxUserId);
            if (buyerShop == null)
            {
                return new ReturnedModel<string>
                {
                    //未授权买家账号
                    Data = "1688_BUYER_ACCOUNT_UNAUTHORIZATION",
                    Message = "未授权买家账号",
                    Success = false
                };
            }
            //2.校验是否过期
            var service = PlatformFactory.GetPlatformService(buyerShop) as AlibabaPlatformService;
            var ping = service.PingNoThrow();
            if (!ping)
            {
                return new ReturnedModel<string>
                {
                    Data = "1688_BUYER_ACCOUNT_AUTHORIZATION_EXPIRE",
                    Message = "授权已过期",
                    Success = false
                };
            }
            //3.检查买家是否开启免密支付
            var settings = new BusinessSettingsService().GetsByCurrentUser(new List<string>
            {
                BusinessSettingKeys.SupplyBy1688.PasswordFreePaySet
            });
            var passwordFreePaySet = settings
                .FirstOrDefault(m => m.Key == BusinessSettingKeys.SupplyBy1688.PasswordFreePaySet)
                ?.Value ?? "3";
            if (passwordFreePaySet == "2")
            {
                var zhuKeService = new AlibabaZhuKePlatformService(buyerShop);
                var isOpenPasswordFreePay = zhuKeService.CheckProtocolPayIsOpen();
                if (!isOpenPasswordFreePay)
                {
                    return new ReturnedModel<string>
                    {
                        Data = "1688_BUYER_ACCOUNT_UN_PASSWORD_FREE_PAY",
                        Message = "未开启免密支付",
                        Success = false
                    };
                }
            }
            else
            {
                return new ReturnedModel<string>
                {
                    Data = passwordFreePaySet,
                    Message = "未优先使用免密支付",
                    Success = true
                };
            }
            //返回正常
            return new ReturnedModel<string>
            {
                Data = passwordFreePaySet
            };
        }

        /// <summary>
        /// 检查1688买家是否开通了免密支付
        /// </summary>
        /// <returns></returns>
        public ReturnedModel<string> Check1688BuyerIsOpenPasswordFreePay(int fxUserId)
        {
            //1.检查是否授权买家账号
            var buyerShop = GetAlibabaBuyerShop(fxUserId);
            if (buyerShop == null)
            {
                return new ReturnedModel<string>
                {
                    //未授权买家账号
                    Data = "1688_BUYER_ACCOUNT_UNAUTHORIZATION",
                    Message = "未授权买家账号",
                    Success = false
                };
            }
            //2.校验是否过期
            var service = PlatformFactory.GetPlatformService(buyerShop) as AlibabaPlatformService;
            var ping = service.PingNoThrow();
            if (!ping)
            {
                return new ReturnedModel<string>
                {
                    Data = "1688_BUYER_ACCOUNT_AUTHORIZATION_EXPIRE",
                    Message = "授权已过期",
                    Success = false
                };
            }
            //3.检查买家是否开启免密支付
            var zhuKeService = new AlibabaZhuKePlatformService(buyerShop);
            var isOpenPasswordFreePay = zhuKeService.CheckProtocolPayIsOpen();
            if (!isOpenPasswordFreePay)
            {
                return new ReturnedModel<string>
                {
                    Data = "1688_BUYER_ACCOUNT_UN_PASSWORD_FREE_PAY",
                    Message = "未开启免密支付",
                    Success = false
                };
            }
            else
            {
                //返回正常
                return new ReturnedModel<string>
                {
                    Success = true
                };
            }
        }

        /// <summary>
        /// 免密支付采购单
        /// </summary>
        /// <param name="logicOrderIds"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<PurchasePasswordFreePayResultModel> PasswordFreePayPurchaseOrders(
            List<string> logicOrderIds, int fxUserId, int fromRepay = 0)

        {
            //测试
            //return new List<PurchasePasswordFreePayResultModel>
            //{
            //    new PurchasePasswordFreePayResultModel
            //    {
            //        IsPaySuccess = true, Items = new List<PurchasePasswordFreePayItemResultModel>
            //        {
            //            new PurchasePasswordFreePayItemResultModel
            //            {
            //                LogicOrderId = "100000588461361",
            //                PurchaseFxUserId = 11,
            //                PurchaseShopId = 11,
            //                PurchasePlatformOrderId = "2309051752181798652163",
            //                PurchaseTotalAmount = 12,
            //                IsPaySuccess = true
            //            }
            //        },OrderTotals = 1,
            //        OverTotalAmount = 12
            //    },
            //    new PurchasePasswordFreePayResultModel
            //    {
            //        IsPaySuccess = false, Items = new List<PurchasePasswordFreePayItemResultModel>
            //        {
            //            new PurchasePasswordFreePayItemResultModel
            //            {
            //                LogicOrderId = "100000526061364",
            //                PurchaseFxUserId = 11,
            //                PurchaseShopId = 11,
            //                PurchasePlatformOrderId = "2309051758281771122285",
            //                PurchaseTotalAmount = 12,
            //                IsPaySuccess = false,
            //                FailMessage = "账号余额不足"
            //            }
            //        },OrderTotals = 1,
            //        OverTotalAmount = 12
            //    }
            //};
            //排空处理
            if (logicOrderIds == null || !logicOrderIds.Any())
            {
                throw new LogicException("请选择要支付的订单");
            }
            //初始化检查
            var checkResult = Check1688BuyerIsOpenPasswordFreePay(fxUserId);
            if (!checkResult.Success)
            {
                throw new LogicException(checkResult.Message);
            } 
            //查询采购单信息，判断采购单是否都是待支付状态
            var purchaseOrders = Get(logicOrderIds, null, 1);
            //获取等待支付的单子
            var waitPayPurchaseOrders = purchaseOrders
                ?.Where(x => x.PurchaseOrderStatus == PurchaseOrderRelationEnumStatus.waitpay.ToString())?.ToList();
            if (waitPayPurchaseOrders == null || !waitPayPurchaseOrders.Any())
            {
                throw new LogicException("您选择的订单，没有需要支付的订单");
            }

            //检查逻辑单是否存在
            var logicOrderService = new LogicOrderService(_connectionString);
            List<LogicOrder> orders = new List<LogicOrder>();
            if (fromRepay == 1 && CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                orders = GetCloudDbLogicOrderByRelation(purchaseOrders);
            }
            else
                orders = logicOrderService.GetOrdersWithPOrderItem(logicOrderIds, pOrderItemFields: new List<string> { "Count", "ProductID", "SkuID" });

            //var logicOrderService = new LogicOrderService(_connectionString);
            //var orders = logicOrderService.GetOrdersWithPOrderItem(logicOrderIds,
            //    pOrderItemFields: new List<string> { "Count", "ProductID", "SkuID" });
            if (orders == null || !orders.Any())
            {
                throw new LogicException("您选择的订单不存在或已被删除，请刷新重试");
            }
            ////查询采购单信息，判断采购单是否都是待支付状态
            //var purchaseOrders = Get(logicOrderIds, null, 1);
            ////获取等待支付的单子
            //var waitPayPurchaseOrders = purchaseOrders
            //    ?.Where(x => x.PurchaseOrderStatus == PurchaseOrderRelationEnumStatus.waitpay.ToString())?.ToList();
            //if (waitPayPurchaseOrders == null || !waitPayPurchaseOrders.Any())
            //{
            //    throw new LogicException("您选择的订单，没有需要支付的订单");
            //}
            //获取买家账号
            var buyerShop = GetAlibabaBuyerShop(fxUserId);
            //买家下单平台服务
            var buyerPlatformService = new AlibabaZhuKePlatformService(buyerShop);
            //并发数
            const int parallelism = 6;
            //收集免密支付结果
            var itemResults = new ConcurrentBag<PurchasePasswordFreePayItemResultModel>();
            //并行免密支付
            Parallel.ForEach(waitPayPurchaseOrders,
                new ParallelOptions { MaxDegreeOfParallelism = CustomerConfig.IsDebug ? 1 : parallelism },
                order =>
                {
                    var payResult = buyerPlatformService.PreparePay(order.PurchasePlatformOrderId);
                    if (payResult == null)
                    {
                        return;
                    }

                    //添加到支付结果
                    itemResults.Add(new PurchasePasswordFreePayItemResultModel
                    {
                        LogicOrderId = order.SourceLogicOrderId,
                        PurchasePlatformOrderId = order.PurchasePlatformOrderId,
                        PurchaseFxUserId = order.PurchaseOrderFxUserId,
                        PurchaseShopId = order.PurchaseOrderShopId,
                        PurchaseTotalAmount = order.PurchaseOrderHopeSettlementPrice,
                        IsPaySuccess = payResult.Item1,
                        FailMessage = payResult.Item2
                    });
                });
            //更新状态支付成功状态
            var paySuccessItems = itemResults.Where(m => m.IsPaySuccess).ToList();
            if (paySuccessItems.Any())
            {
                var purchasePlatformOrderIds =
                    paySuccessItems.Select(m => m.PurchasePlatformOrderId).Distinct().ToList();
                try
                {
                    UpdatePurchasePayOrderStatus(purchasePlatformOrderIds, fxUserId);
                }
                catch (Exception e)
                {
                    Log.WriteError(
                        $"免密支付成功，更新状态时失败，失败原因：{e.Message}，相关信息({fxUserId})：{purchasePlatformOrderIds.ToJson()}");
                }
            }

            //处理结果
            var resultsByGroup = itemResults.GroupBy(m => m.IsPaySuccess).ToList();
            var results = resultsByGroup.Select(grouping => new PurchasePasswordFreePayResultModel
            {
                IsPaySuccess = grouping.Key,
                Items = grouping.ToList(),
                OrderTotals = grouping.Count(),
                OverTotalAmount = grouping.Sum(m => m.PurchaseTotalAmount)
            }).ToList();

            //结果，错误代码转为文字
            ErrorCodeToErrorText(results);

            //返回
            return results;
        }

        /// <summary>
        /// 错误代码转为前端显示文本
        /// </summary>
        /// <param name="models"></param>
        private void ErrorCodeToErrorText(List<PurchasePasswordFreePayResultModel> models)
        {
            if (models == null || models.Count == 0)
                return;

            var dicError = new Dictionary<string, string>();
            dicError.Add("USER_BALANCE_NOT_ENOUGH", "余额不足");
            //TODO：有新的错误码再一一追加

            models.ForEach(model =>
            {
                model.Items?.ForEach(item =>
                {
                    if (item.IsPaySuccess == false)
                    {
                        item.FailMessageText = item.FailMessage;
                        if (dicError.ContainsKey(item.FailMessage))
                            item.FailMessageText = dicError[item.FailMessage];
                    }
                });
            });
        }

        /// <summary>
        /// 写入AliLog
        /// </summary>
        /// <param name="models"></param>
        /// <param name="tag"></param>
        /// <param name="batchNo"></param>
        private void WriteAliLog(List<UpdatePurchaseStatusModel> models, string tag, string batchNo)
        {
            if (models == null || !models.Any())
                return;
            //开关，默认为关
            var key = "/System/Fendan/PurchaseOrderRelation/WriteAliLog";
            var isOpenValue = new CommonSettingRepository().Get(key, 0)?.Value ?? "";
            if (isOpenValue == "" || isOpenValue == "0" || isOpenValue == "false")
                return;

            var dbName = _repository.DbConnection.Database;
            var currentFxUserId = BaseSiteContext.CurrentNoThrow?.CurrentFxUserId ?? 0;
            var batchSize = 500;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();
                var model = new PurchaseOrderRelationLogModel() { Models = batchModels.ToJson(true), PlatformOrderIds = batchModels.Select(a => a.PlatformOrderId).ToJson() };
                model.FxUserId = batchModels.First().UserId;
                model.BatchNo = $"{batchNo}";
                model.BatchNum = i;
                model.HostIp = HostHelper.IpAddress();
                model.HostName = Environment.MachineName;
                model.CurFxUserId = currentFxUserId;
                model.Tag = tag;
                model.DbName = dbName;
                model.CloudPlatformType = CustomerConfig.CloudPlatformType;
                Instance.WriteLog(model);
            }
        }

        /// <summary>
        /// 根据采购单关系跨云查询
        /// </summary>
        /// <param name="purchaseOrderRelations"></param>
        /// <returns></returns>
        public List<LogicOrder> GetCloudDbLogicOrderByRelation(List<PurchaseOrderRelation> purchaseOrderRelations)
        {
            var orders = new List<LogicOrder>();
            if (purchaseOrderRelations?.Any() != true)
            {
                return orders;
            }

            var notAliCloudPlatformTypes = new List<string> { PlatformType.TouTiao.ToString(), PlatformType.Pinduoduo.ToString(), PlatformType.KuaiTuanTuan.ToString(), PlatformType.Jingdong.ToString() };
            var toutiaoCloudRelation = purchaseOrderRelations.Where(x => x.SourcePlatformType == PlatformType.TouTiao.ToString()).ToList();
            var aliCloudRelation = purchaseOrderRelations.Where(x => notAliCloudPlatformTypes.Contains(x.SourcePlatformType) == false).ToList();
            //var jingDongCloudRelation = purchaseOrderRelations.Where(x => x.SourcePlatformType != CloudPlatformType.Jingdong.ToString()).ToList();

            Log.WriteLine($"aliCloudRelation={aliCloudRelation.Select(a => a.SourceLogicOrderId).ToJson()}，toutiaoCloudRelation={toutiaoCloudRelation.Select(a => a.SourceLogicOrderId).ToJson()}", "GetCloudDbLogicOrderByRelation.txt");

            if (aliCloudRelation.Any())
            {
                var logicOrderService = new LogicOrderService();
                var newLogicOrderIds = aliCloudRelation.Select(x => x.SourceLogicOrderId).Distinct().ToList();
                var logicOrderList = logicOrderService.GetOrdersWithPOrderItem(newLogicOrderIds, pOrderItemFields: new List<string> { "Count", "ProductID", "SkuID" });
                orders.AddRange(logicOrderList);
            }
            if (toutiaoCloudRelation.Any())
            {
                var logicOrderService = new LogicOrderService();
                Dictionary<string, int> sidDict = new Dictionary<string, int>();
                var fxUserIds = toutiaoCloudRelation.Select(x => x.CreateFxUserId).Distinct().ToList();
                var dbConfigs = new DbConfigRepository().GetListByFxUserIds(fxUserIds, CloudPlatformType.TouTiao.ToString());
                dbConfigs?.GroupBy(g => g.ConnectionString).ToList().ForEach(dbConfig =>
                {
                    var curFxUserIds = dbConfig.Select(a => a.DbConfig.UserId).ToList();
                    var curSids = toutiaoCloudRelation.Where(x => curFxUserIds.Contains(x.CreateFxUserId)).Select(x => x.SourceLogicOrderId).ToList();
                    var apiDbConfigModel = new ApiDbConfigModel(dbConfig.First().DbServer.Location, dbConfig.First().DbServer.Location,
                        dbConfig.First().DbNameConfig.Id);
                    Log.WriteLine($"apiDbConfigModel={apiDbConfigModel.ToJson()}，curSids={curSids.ToJson()}", "GetCloudDbLogicOrderByRelation.txt");
                    var curList = logicOrderService.GetOrdersWithPOrderItemFromCloud(apiDbConfigModel, curSids, pOrderItemFields: new List<string> { "Count", "ProductID", "SkuID" });
                    curList?.ForEach(x =>
                    {
                        //相同只保留一条
                        if (sidDict.ContainsKey(x.LogicOrderId) == false)
                        {
                            sidDict.Add(x.LogicOrderId, 1);
                            orders.Add(x);
                        }
                    });
                });
            }

            return orders;
        }

        #region 1688分销数据校验与纠正逻辑

        /// <summary>
        /// 检查采购单关系数据是否在1688精选库存在
        /// </summary>
        /// <param name="batchId"></param>
        /// <param name="lastPurchaseOrderRelationId"></param>
        public List<BusinessLogModel> IfPurchaseOrderRelationNotExisitInAlibabaThenReSync(string batchId, int lastPurchaseOrderRelationId = 0 ,bool isRepair = false)
        {
            //查询商家的采购单关系数据，然后校验是否都同步到了【1688厂家-精选平台】
            //以及：是否同步到了厂家库
            var fields = "Id,PurchaseRelationCode,PurchaseOrderStatus,PurchaseOrderRefundStatus,PurchaseOrderFxUserId,PurchaseOrderShopId,CreateFxUserId,SourceLogicOrderId,PurchasePlatformOrderId ";
            var sql = $"SELECT {fields} FROM PurchaseOrderRelation (NOLOCK) WHERE  [Status]=1 AND CreateTime > DATEADD(DAY,-7,GETDATE())";
            if (lastPurchaseOrderRelationId > 0)
                sql += $" AND Id> {lastPurchaseOrderRelationId}";
            var db = _repository.DbConnection;
            var models = db.Query<PurchaseOrderRelation>(sql).ToList();
            if (models == null || models.Any() == false)
                return new List<BusinessLogModel>();
            var curPurDict = models.ToDictionary(x => x.PurchaseRelationCode);
            var notExisitRelations = new List<PurchaseOrderRelation>();
            //纠正：不存在的，直接同步过去
            //纠正：状态不一致的：需要判断谁的是正确的，额外到厂家精选库获取1688订单的状态，以此为准
            //var supplierUserIds = models.Select(x=>x.PurchaseOrderFxUserId).Distinct().ToList();
            //不查厂家所在数据库，直接到精选的三个区进行查询
            var albabaFendanDb1 = new DbApiAccessUtility(new ApiDbConfigModel { DbNameConfigId = 11122, Location = "Aliababa", PlatformType = "Aliababa" });
            var albabaFendanDb2 = new DbApiAccessUtility(new ApiDbConfigModel { DbNameConfigId = 11179, Location = "Aliababa", PlatformType = "Aliababa" });
            var albabaFendanDb3 = new DbApiAccessUtility(new ApiDbConfigModel { DbNameConfigId = 11057, Location = "Aliababa", PlatformType = "Aliababa" });
            var alibabaDbs = new List<DbApiAccessUtility>() { albabaFendanDb2, albabaFendanDb1, albabaFendanDb3 };
            var targetSql = $"SELECT PurchaseRelationCode FROM PurchaseOrderRelation (NOLOCK) where PurchaseRelationCode IN@codes";
            var dictExistPurs = new Dictionary<string, PurchaseOrderRelation>();
            foreach (var alibabaDbApi in alibabaDbs)
            {
                var curKeys = curPurDict.Where(x => dictExistPurs.ContainsKey(x.Key) == false).Select(x => x.Key).ToList();
                var chunks = curKeys.ChunkList(500);
                foreach (var chunk in chunks)
                {
                    var temps = alibabaDbApi.Query<PurchaseOrderRelation>(targetSql, new { codes = chunk }).ToList();
                    temps?.ForEach(x =>
                    {
                        if (dictExistPurs.ContainsKey(x.PurchaseRelationCode) == false)
                            dictExistPurs.Add(x.PurchaseRelationCode, x);
                    });
                }
            }
            //按照厂家所在库查询结果对比
            var notExisitRelationKvs = curPurDict.Where(x => dictExistPurs.ContainsKey(x.Key) == false).Select(x => x).ToList();
            //记录日志，并给纠正
            var dbName = _repository.DbConnection.Database;
            //var batchId = Guid.NewGuid().ToString().ToShortMd5();
            var logs = notExisitRelationKvs?.Select(x => new BusinessLogModel
            {
                BatchId = batchId,
                CloudPlatformType = CustomerConfig.CloudPlatformType,
                PlatformType = CustomerConfig.CloudPlatformType,
                BusinessType = "1688分销数据校验",
                SubBusinessType = "检查到未同步给Aibaba精选库采购关系1/2",
                MethodName = "CheckPurchaseOrderRelationIsExisitInAlibaba",
                FxUserId = x.Value.CreateFxUserId,
                ShopId = x.Value.SourceShopId,
                BusinessId = x.Value.SourcePlatformOrderId,
                SysBusinessId = x.Value.SourceLogicOrderId,
                DbName = dbName,
                Content = x.Value.ToJson(true),
                Remark = isRepair?"配置修复":"配置不做修复"
            }).ToList();
            BusinessLogDataEventTrackingService.Instance.WriteLog(logs);

            //纠正处理
            if (notExisitRelationKvs == null || notExisitRelationKvs.Any() == false)
            {
                Log.WriteLine($"当前库【{dbName}】无未同步给Aibaba精选库采购关系数据");
                return new List<BusinessLogModel>();
            }
            var dbModels = new List<PurchaseOrderRelation>();
            var purItemRp = new PurchaseOrderItemRelationRepository(_connectionString);
            var curChunks = notExisitRelationKvs.Select(x=>x.Key).ToList().ChunkList(500);
            
            foreach (var chunk in curChunks)
            {
                var codes = chunk;
                var curModels =  GetByCodes(codes);
                var curItems = purItemRp.GetListForDuplication(codes,new List<string> { "*" });
                curModels.ForEach(model =>
                {
                    model.ItemRelations = curItems?.Where(m => m.PurchaseRelationCode == model.PurchaseRelationCode)
                        .ToList();
                });
                SyncDataToAlibabaCloud(curModels);
            }
            logs.ForEach(x => {
                x.MethodName = "1688分销数据校验";
                x.SubBusinessType = "重新同步采购关系给Alibaba精选库2/2";
                x.Remark = "已触发修复";
            });
            BusinessLogDataEventTrackingService.Instance.WriteLog(logs);
            //两边库采购单状态不一致的数量
            //var statusNotSameRelations = new List<PurchaseOrderRelation>();
            return logs;
        }

        /// <summary>
        /// 补偿采购关系数据
        /// </summary>
        /// <param name="batchId"></param>
        public int CompensatePurchaseOrderRelation(string batchId)
        {
            //是否要同步副本
            var isNeedSDI = true;

            //精选和京东，没有副本概念
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString() || CustomerConfig.CloudPlatformType == CloudPlatformType.Jingdong.ToString())
            {
                isNeedSDI = false;
            }

            var db = _repository.DbConnection;
            var dbName = db.Database;

            //拼多多旧库，没有副本概念
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString() && dbName == "pdd_fendan_db")
            {
                isNeedSDI = false;
            }

            var logFileName = $"CompensatePurchaseOrderRelation-{DateTime.Now.ToString("yyMMdd")}.txt";
            var totalCount = 0;
            var csService = new CommonSettingService();
            var pathFlowNodeService = new PathFlowNodeService(_connectionString);
            var dbConfigRepository = new DbConfigRepository();
            var userFxService = new UserFxService();
            var key = $"/FxSystem/CompensatePurchaseOrderRelation/LastId/{dbName}";
            //上次执行的Id
            var lastId = csService.Get(key, -1, false)?.Value.ToLong() ?? 0;
            var maxLoopCount = 100;   //最大循环次数
            var pageSize = 500;       //每次数量
            for (var x = 0; x < maxLoopCount; x++)
            {
                //查询采购单关系数据
                var sql = $"SELECT TOP {pageSize} * FROM PurchaseOrderRelation WITH(NOLOCK) WHERE [Status]=1 AND CreateTime > DATEADD(DAY,-3,GETDATE())";
                if (lastId > 0)
                    sql += $" AND Id> {lastId}";
                sql += " ORDER BY Id ASC";

                Log.WriteLine($"PurchaseOrderRelation,dbName={dbName},lastId={lastId}", logFileName);

                var models = db.Query<PurchaseOrderRelation>(sql).ToList();
                if (models == null || models.Any() == false)
                    return totalCount;

                totalCount += models.Count();
                var codes = models.Select(a => a.PurchaseRelationCode).ToList();
                var sqlItem = $@"SELECT * FROM PurchaseOrderItemRelation poir WITH(NOLOCK)
INNER JOIN FunStringToTable(@codes,',') t ON t.item = poir.PurchaseRelationCode";
                var purchaseOrderItemRelations = db.Query<PurchaseOrderItemRelation>(sqlItem, new { codes = string.Join(",", codes) }).ToList();

                Log.WriteLine($"PurchaseOrderRelation,dbName={dbName},models={models.Count()}条，codes={codes.ToJson()}，purchaseOrderItemRelations={purchaseOrderItemRelations?.Count()}条", logFileName);

                //记录日志
                var logs = models?.Select(m => new BusinessLogModel
                {
                    BatchId = batchId,
                    CloudPlatformType = CustomerConfig.CloudPlatformType,
                    PlatformType = CustomerConfig.CloudPlatformType,
                    BusinessType = "补偿采购关系数据",
                    SubBusinessType = "补偿采购关系数据1/2",
                    MethodName = "CompensatePurchaseOrderRelation",
                    FxUserId = m.CreateFxUserId,
                    ShopId = m.SourceShopId,
                    BusinessId = m.SourceLogicOrderId,
                    SysBusinessId = m.PurchasePlatformOrderId,
                    DbName = dbName,
                    Content = m.PurchaseRelationCode,
                    Remark = $"{lastId}"
                }).ToList();
                BusinessLogDataEventTrackingService.Instance.WriteLog(logs);

                #region 副本逻辑

                Log.WriteLine($"PurchaseOrderRelation,dbName={dbName},查到【{models.Count()}】条，isNeedSDI={isNeedSDI}", logFileName);

                if (isNeedSDI)
                {
                    //路径流代码列表
                    var pathFlowCodes = models.Select(m => m.SourcePathFlowCode).Distinct().ToList();
                    //获取路径流节点上用户信息
                    var pathFlowFxUserIdNodes = new List<PathFlowFxUserIdNodeModel>();
                    //获取路径流节点上用户信息
                    pathFlowFxUserIdNodes = pathFlowNodeService.GetFxUserByPathFlowCodes(pathFlowCodes);
                    //按用户ID分组
                    var fxUserIdNodesByGroup = pathFlowFxUserIdNodes.GroupBy(m => m.FxUserId).ToList();

                    //路径节点所有用户ID
                    var factoryFxUserIds = fxUserIdNodesByGroup.Select(m => m.Key).Distinct().ToList();
                    //获取需要复制副本数据库配置信息
                    var factoryDbConfigs = dbConfigRepository.GetListByFxUserIdsForCurrentCloudPlatform(factoryFxUserIds);
                    //排除当前库
                    factoryDbConfigs = factoryDbConfigs.Where(a => a.DbNameConfig.DbName != dbName).ToList();
                    var needDuplicationDbConfigs = factoryDbConfigs.GroupBy(a => a.ConnectionString).ToList();

                    Log.WriteLine($"PurchaseOrderRelation,needDuplicationDbConfigs={needDuplicationDbConfigs.Count()}个库", logFileName);

                    //遍历目标库补偿副本
                    needDuplicationDbConfigs.ForEach(dbConfig =>
                    {
                        //厂家数据库连接字符串
                        var factoryConnectionString = dbConfig.Key;
                        var factoryDbName = dbConfig.FirstOrDefault()?.DbNameConfig?.DbName ?? "";
                        //当前的用户ID
                        var fxUserIds = dbConfig.Select(m => m.DbConfig.UserId).Distinct().ToList();
                        //当前的路径流
                        var factoryPathFlowCodes = pathFlowFxUserIdNodes.Where(m => fxUserIds.Contains(m.FxUserId))
                            .Select(m => m.PathFlowCode).Distinct().ToList();

                        //复制副本采购单关系信息
                        var factoryPurchaseOrderRelations = models.Where(m => factoryPathFlowCodes.Contains(m.SourcePathFlowCode)).ToList();
                        new PurchaseOrderRelationService(factoryConnectionString).InsertsForDuplication(factoryPurchaseOrderRelations, true);

                        Log.WriteLine($"PurchaseOrderRelation,factoryDbName={factoryDbName},factoryPurchaseOrderRelations={factoryPurchaseOrderRelations.Select(a => a.PurchaseRelationCode).ToJson()}", logFileName);

                        //复制副本采购单关系项信息
                        var factoryPurchaseOrderRelationCodes = factoryPurchaseOrderRelations.Select(m => m.PurchaseRelationCode).Distinct().ToList();
                        var factoryPurchaseOrderItemRelations = purchaseOrderItemRelations.Where(m => factoryPurchaseOrderRelationCodes.Contains(m.PurchaseRelationCode)).ToList();
                        new PurchaseOrderItemRelationService(factoryConnectionString).InsertsForDuplication(factoryPurchaseOrderItemRelations, true);

                        //记录日志
                        var factoryLogs = factoryPurchaseOrderRelations?.Select(m => new BusinessLogModel
                        {
                            BatchId = batchId,
                            CloudPlatformType = CustomerConfig.CloudPlatformType,
                            PlatformType = CustomerConfig.CloudPlatformType,
                            BusinessType = "补偿采购关系数据",
                            SubBusinessType = "补偿采购关系数据2/2",
                            MethodName = "CompensatePurchaseOrderRelation",
                            FxUserId = m.CreateFxUserId,
                            ShopId = m.SourceShopId,
                            BusinessId = m.SourceLogicOrderId,
                            SysBusinessId = m.PurchasePlatformOrderId,
                            DbName = factoryDbName,
                            Content = m.PurchaseRelationCode,
                            Remark = m.SourcePlatformOrderId
                        }).ToList();
                        BusinessLogDataEventTrackingService.Instance.WriteLog(factoryLogs);

                    });
                }

                #endregion


                #region 补偿-厂家的精选库

                var sourceFxUserIds = models.Select(a => a.CreateFxUserId).Distinct().ToList();
                var userFxs = userFxService.GetUserFxs(sourceFxUserIds);
                if (userFxs.Any())
                {
                    new SiteContext(userFxs.First(), dbName);
                    SyncDataToAlibabaCloud(models, purchaseOrderItemRelations, true);

                }
                #endregion

                var maxId = models.Max(a => a.Id);
                //保存最后Id
                if (maxId > 0)
                {
                    csService.Set(key, maxId.ToString(), -1);
                    lastId = maxId;
                }

                if (models.Count < pageSize)
                    break;
            }

            return totalCount;
        }

        /// <summary>
        /// 如果采购单状态和下游分销单状态不一致（可能是未回流、未退款），记录日志
        /// </summary>
        /// <param name="batchId"></param>
        /// <param name="lastPurchaseOrderRelationId"></param>
        /// <param name="isRepair">是否修复</param>
        public List<BusinessLogModel> IfPurchaseOrderRelationStatusIsNotSameAsLogicOrderThenRepair(string batchId, int lastPurchaseOrderRelationId = 0,bool isRepair = false)
        {
            //
            var fiveDaysAgo = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd");
            var sql = $@"SELECT
pur.PurchaseRelationCode, 
pur.SourcePlatformOrderId,
pur.SourceLogicOrderId,
pur.SourceShopId,
pur.CreateFxUserId,
pur.PurchasePlatformOrderId,
pur.PurchaseOrderShopId,
pur.PurchaseOrderFxUserId,
pur.PurchaseOrderStatus,
pur.PurchaseOrderRefundStatus,
lo.ErpState AS 'SourcePathFlowCode',
lo.ErpRefundState AS 'PurchaseErrorMessage'
FROM PurchaseOrderRelation pur WITH (NOLOCK)
    INNER JOIN LogicOrder lo WITH (NOLOCK) ON lo.LogicOrderId = pur.SourceLogicOrderId
WHERE pur.Status =1 AND pur.PurchaseOrderStatus = 'waitsellersend' AND pur.CreateTime='{fiveDaysAgo}' AND lo.ErpState IN('sended','success')
";
            var db = _repository.DbConnection;
            var dbName = db.Database;
            var purs = db.Query<PurchaseOrderRelation>(sql);
            //记录日志
            var logs = purs?.Select(x => new BusinessLogModel
            {
                BatchId = batchId,
                CloudPlatformType = CustomerConfig.CloudPlatformType,
                PlatformType = CustomerConfig.CloudPlatformType,
                BusinessType = "1688分销数据校验",
                SubBusinessType = "检查到采购单状态和下游订单状态不一致",
                MethodName = "IfPurchaseOrderRelationStatusIsNotSameAsLogicOrderThenRepair",
                FxUserId = x.CreateFxUserId,
                ShopId = x.SourceShopId,
                BusinessId = x.SourcePlatformOrderId,
                SysBusinessId = x.SourceLogicOrderId,
                DbName = dbName,
                Content = new {
                    x.PurchaseRelationCode,
                    x.SourcePlatformOrderId,
                    x.SourceLogicOrderId,
                    x.SourceShopId,
                    x.CreateFxUserId,
                    x.PurchasePlatformOrderId,
                    x.PurchaseOrderShopId,
                    x.PurchaseOrderFxUserId,
                    x.PurchaseOrderStatus,
                    x.PurchaseOrderRefundStatus,
                    LogicOrderErpState = x.SourcePathFlowCode,
                    LogicOrderErpRefundState = x.PurchaseErrorMessage
                }.ToJson(true)
            }).ToList();
            BusinessLogDataEventTrackingService.Instance.WriteLog(logs);
            if (purs == null || purs.Any() == false)
            {
                Log.WriteLine($"当前库【{dbName}】无采购单状态异常数据");
                return new List<BusinessLogModel>();
            }
            //纠正之补偿回流
            //分组从平台接口同步订单获取物流信息，补偿回流记录
            var logicOrderIdMapping = new Dictionary<string, List<string>>();
            foreach (var pur in purs)
            {
                if (logicOrderIdMapping.ContainsKey(pur.SourcePlatformOrderId) == false)
                    logicOrderIdMapping.Add(pur.SourcePlatformOrderId, new List<string> { pur.SourceLogicOrderId });
                else
                    logicOrderIdMapping[pur.SourcePlatformOrderId].Add(pur.SourceLogicOrderId);
            }
            var groups = purs.GroupBy(x=>$"{x.CreateFxUserId}_{x.SourceShopId}").ToList();
            var userFxDict = new Dictionary<int, UserFx>();
            var userRp = new UserFxRepository();
            var models = new List<BusinessLogModel>();
            foreach (var group in groups)
            {
                var first = group.First();
                var curFxUserId = first.CreateFxUserId;
                var curShopId = first.SourceShopId;
                UserFx userFx = null;
                if(userFxDict.ContainsKey(curFxUserId))
                    userFx = userFxDict[curFxUserId];
                else
                {
                    userFx = userRp.Get(curFxUserId);
                    userFxDict.Add(curFxUserId, userFx);
                }
                var tempSiteContext = new SiteContext(userFx,new SiteContextConfig { NeedLoadFxUser =false ,NeedRelationShops = false,NeedShopExpireTime = false });
                //开始同步订单
                var ptService = PlatformFactory.GetPlatformService(curShopId);
                var ptOrders = new List<Order>();
                try
                {
                    ptOrders = ptService.SyncOrders(group.Select(x => x.SourcePlatformOrderId).Distinct().ToList()) ?? new List<Order>();
                }
                catch (Exception ex)
                {
                    Log.WriteError($"IfPurchaseOrderRelationStatusIsNotSameAsLogicOrderThenRepair同步订单发生异常：{ex}");
                }
                if (ptOrders == null || ptOrders.Any() == false)
                    continue;
                //补偿
                var orderService = new OrderService(_connectionString);
                var ptOrderDict = new Dictionary<string, Order>();
                foreach (var order in ptOrders)
                {
                    if (logicOrderIdMapping.ContainsKey(order.PlatformOrderId) == false)
                        continue;
                    var sourceLogicOrderIds = logicOrderIdMapping[order.PlatformOrderId].Distinct().ToList();
                    sourceLogicOrderIds?.ForEach(x => {
                        if (ptOrderDict.ContainsKey(x) == false)
                        {
                            order.LogicOrderId = x;
                            ptOrderDict.Add(x, order);
                        }
                    });
                }
                var curLogs = logs.Where(x=>ptOrderDict.ContainsKey(x.SysBusinessId))?.ToList();
                curLogs?.ForEach(x => {
                    x.MethodName = "1688分销数据校验";
                    x.SubBusinessType = "重新触发补偿回流记录2/3";
                });
                BusinessLogDataEventTrackingService.Instance.WriteLog(curLogs);
                var sendReturnLogs = orderService.TryCompleteSendHistoryReturnRecord(ptOrderDict);
                sendReturnLogs?.ForEach(x => {
                    x.BatchId = batchId;
                    x.MethodName = "1688分销数据校验";
                    x.SubBusinessType = "同步订单补偿回流记录3/3";
                });
                BusinessLogDataEventTrackingService.Instance.WriteLog(sendReturnLogs);
                if (sendReturnLogs != null && sendReturnLogs.Any())
                    models.AddRange(sendReturnLogs);
            }
            return models;
        }

        /// <summary>
        /// 如果采购单状态和下游分销单状态不一致（可能是未回流、未退款），记录日志
        /// </summary>
        /// <param name="batchId"></param>
        public List<ReturnedModel<string>> IfLogicOrderIsRefundThenRepair(string batchId,bool isRepair = false)
        {
            ////仅1688云平台时可以处理退款情况
            //if (CustomerConfig.CloudPlatformType != "Alibaba")
            //    return new List<ReturnedModel<string>>();

            var sql = $@"SELECT
            distinct pur.PurchaseOrderFxUserId
            FROM PurchaseOrderRelation pur WITH (NOLOCK)
                INNER JOIN LogicOrder lo WITH (NOLOCK) ON lo.LogicOrderId = pur.SourceLogicOrderId
            WHERE pur.Status =1 AND (pur.PurchaseOrderRefundStatus IS NULL OR pur.PurchaseOrderRefundStatus='') AND lo.ErpState IN('close') AND pur.CreateTime>DATEADD(DAY,-7,getdate())";
            var db = _repository.DbConnection;
            var dbName = db.Database;
            var supplierFxUserIds = db.Query<int>(sql);
            //记录日志
            var logs = supplierFxUserIds?.Select(x => new BusinessLogModel
            {
                BatchId = batchId,
                CloudPlatformType = CustomerConfig.CloudPlatformType,
                PlatformType = CustomerConfig.CloudPlatformType,
                BusinessType = "1688分销数据校验",
                SubBusinessType = "检查到采购单退款状态和下游订单退款状态不一致",
                MethodName = "IfLogicOrderIsRefundThenRepair",
                FxUserId = x,
                ShopId = 0,
                BusinessId = "",
                SysBusinessId = "",
                DbName = dbName,
                Content = "",
                Remark = ""
            }).ToList();
            if (supplierFxUserIds == null || supplierFxUserIds.Any() == false)
            {
                Log.WriteLine($"当前库【{dbName}】无退款状态异常数据");
                return new List<ReturnedModel<string>>();
            }
            if (isRepair == false)
            {
                logs.ForEach(x=>x.Remark = $"当前库【{dbName}】查询到【{supplierFxUserIds.Count()}】个用户有退款状态异常数据，【退款】配置不做修复，退出");
                BusinessLogDataEventTrackingService.Instance.WriteLog(logs);
            }
            else
                Log.WriteLine($"当前库【{dbName}】查询到【{supplierFxUserIds.Count()}】个用户有退款状态异常数据，开始修复  用户ID：{supplierFxUserIds.ToJson()}");

            //自动纠正退款状态
            var alibabaFxService = new AlibabaFxOrderService();
            var returnModels = new List<ReturnedModel<string>>();
            foreach (var supplierFxUserId in supplierFxUserIds)
            {
                var log = logs.FirstOrDefault(x=>x.FxUserId == supplierFxUserId);
                var results = alibabaFxService.AutoExeRefundBySupplier(supplierFxUserId, null);
                if (results != null)
                    returnModels.AddRange(results);
                if (log != null)
                {
                    log.Content = results.ToJson();
                    log.Remark = "退款修复完成";
                }
            }
            Log.WriteLine($"当前库【{dbName}】查询到【{supplierFxUserIds.Count()}】个用户有退款状态异常数据，修复逻辑执行完成");
            BusinessLogDataEventTrackingService.Instance.WriteLog(logs);
            return returnModels;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="batchId"></param>
        /// <returns>Tuple.Item1：待支付数，Item2：全部数量</returns>
        public Dictionary<string,int> LogNotPaidPurchaseOrderRelationInfo(string batchId)
        {
            //查询待支付订单
            var sql = @"SELECT PurchaseOrderStatus,COUNT(1) AS 'PurchaseOrderFxUserId' FROM dbo.PurchaseOrderRelation WITH(NOLOCK) WHERE Status=1 GROUP BY PurchaseOrderStatus";
            var db = _repository.DbConnection;
            var dbName = db.Database;
            var results = db.Query<PurchaseOrderRelation>(sql);
            //记录日志
            var waitPayCount = results?.Where(x => x.PurchaseOrderStatus == "waitpay")?.Count() ?? 0;
            if(results != null || results.Any() == false)
                Log.WriteLine($"当前库【{dbName}】无未支付的采购单数据");
            var log = new BusinessLogModel
            {
                BatchId = batchId,
                CloudPlatformType = CustomerConfig.CloudPlatformType,
                PlatformType = CustomerConfig.CloudPlatformType,
                BusinessType = "1688分销数据校验",
                SubBusinessType = "统计采购单的状态",
                MethodName = "LogPurchaseOrderRelationInfo",
                FxUserId = 0,
                ShopId = 0,
                BusinessId = "",
                SysBusinessId = ""+ waitPayCount,
                DbName = dbName,
                Content = results?.Select(x=>new { x.PurchaseOrderStatus,Count = x.PurchaseOrderFxUserId }).ToJson(),
                Remark = $"待支付数：{waitPayCount}"
            };
            BusinessLogDataEventTrackingService.Instance.WriteLog(new List<BusinessLogModel> { log });
            var dict = new Dictionary<string, int>();
            results?.GroupBy(x => x.PurchaseOrderStatus)?.ToList()?.ForEach(x => {
                if (dict.ContainsKey(x.Key) == false)
                    dict.Add(x.Key, x.Sum(y => y.PurchaseOrderFxUserId));
            });
            return dict;
        }

        #endregion

        /// <summary>
        /// 检查是否有预付关系以及相关设置是否开启
        /// </summary>
        /// <param name="currentFxUserId"></param>
        /// <param name="shopIds"></param>
        /// <param name="role">供应商：Supplier 分销商：Agent</param>
        /// <returns></returns>
        public Dictionary<string, bool> CheckPrePayStatus(int currentFxUserId, string shopIds, string role)
        {
            // 是否开启预付
            var prePayStatus = new SupplierUserRepository().CheckPrePayStatus(currentFxUserId, role);

            // 是否有运费模版
            var shippingFeeTemplateStatus = new ShippingFeeTemplateRepository().CheckShippingFeeTemplateStatus(currentFxUserId);

            // 是否有1688货源商品信息且映射
            //var productCount = new ProductFxRepository().CheckProductFxMappingStatus(shopIds);
            var productCount = 0;
            var distributorCount = new DistributorProductSkuMappingRepository().CheckDistributorMappingStatus(currentFxUserId);

            var result = new Dictionary<string, bool>
            {
                { "prePayStatus", prePayStatus > 0 },
                { "shippingFeeTemplateStatus", shippingFeeTemplateStatus > 0 },
                { "productMappingStatus", productCount > 0 },
                { "distributorMappingStatus", distributorCount > 0 }
            };

            return result;
        }

        /// <summary>
        /// 从源商家库查询采购关系子项（含跨云）
        /// </summary>
        /// <param name="purchaseOrderRelations"></param>
        /// <param name="pids">所有库都查</param>
        /// <returns></returns>
        public Tuple<List<PurchaseOrderRelation>, List<PurchaseOrderItemRelation>> GetPurchaseOrderRelationsFromSourceDb(List<PurchaseOrderRelation> purchaseOrderRelations, List<string> pids = null)
        {
            if ((purchaseOrderRelations == null || purchaseOrderRelations.Any() == false) && (pids == null || pids.Any() == false))
                return null;

            if (purchaseOrderRelations == null)
                purchaseOrderRelations = new List<PurchaseOrderRelation>();

            var sourceFxUserIds = purchaseOrderRelations?.Select(a => a.CreateFxUserId).Distinct().ToList();
            var currentFxUserId = BaseSiteContext.CurrentNoThrow?.CurrentFxUserId ?? 0;
            if (sourceFxUserIds == null)
                sourceFxUserIds = new List<int>();
            if (currentFxUserId > 0)
                sourceFxUserIds.Add(currentFxUserId);

            var dbConfigs = new DbConfigRepository().GetListByFxUserIds(sourceFxUserIds, new List<string> { CloudPlatformType.Alibaba.ToString(), CloudPlatformType.TouTiao.ToString(), CloudPlatformType.Pinduoduo.ToString(), CloudPlatformType.Jingdong.ToString() });

            if (dbConfigs == null || dbConfigs.Any() == false)
                return null;

            var relations = new List<PurchaseOrderRelation>();
            var items = new List<PurchaseOrderItemRelation>();

            dbConfigs.Where(a => a.ConnectionString != _connectionString).GroupBy(g => g.ConnectionString).ToList().ForEach(g =>
            {
                var curDb = g.First();
                var dbLocation = curDb.DbServer.Location;

                var curSourceFxUserIds = g.ToList().Select(a => a.DbConfig.UserId).Distinct().ToList();
                var curPurchaseRelationCodes = new List<string>();

                //各所在云只取相关平台的数据
                if (dbLocation == CloudPlatformType.TouTiao.ToString())
                    curPurchaseRelationCodes = purchaseOrderRelations.Where(a => curSourceFxUserIds.Contains(a.CreateFxUserId) && a.SourcePlatformType == PlatformType.TouTiao.ToString()).Select(a => a.PurchaseRelationCode).ToList();
                else if (dbLocation == CloudPlatformType.Pinduoduo.ToString())
                    curPurchaseRelationCodes = purchaseOrderRelations.Where(a => curSourceFxUserIds.Contains(a.CreateFxUserId) && (a.SourcePlatformType == PlatformType.Pinduoduo.ToString() || a.SourcePlatformType == PlatformType.KuaiTuanTuan.ToString())).Select(a => a.PurchaseRelationCode).ToList();
                else if (dbLocation == CloudPlatformType.Jingdong.ToString())
                    curPurchaseRelationCodes = purchaseOrderRelations.Where(a => curSourceFxUserIds.Contains(a.CreateFxUserId) && a.SourcePlatformType == PlatformType.Jingdong.ToString()).Select(a => a.PurchaseRelationCode).ToList();
                else
                {
                    var notInAlibabaCloudPlatformTypes = new List<string> { PlatformType.TouTiao.ToString(), PlatformType.Pinduoduo.ToString(), PlatformType.KuaiTuanTuan.ToString(), PlatformType.Jingdong.ToString() };
                    curPurchaseRelationCodes = purchaseOrderRelations.Where(a => curSourceFxUserIds.Contains(a.CreateFxUserId) && notInAlibabaCloudPlatformTypes.Contains(a.SourcePlatformType) == false).Select(a => a.PurchaseRelationCode).ToList();
                }

                if (curPurchaseRelationCodes != null && curPurchaseRelationCodes.Any())
                {
                    if (dbLocation == CustomerConfig.CloudPlatformType)
                    {
                        //同云，直接查询
                        var curConnectionString = g.Key;
                        var curItems = new PurchaseOrderItemRelationService(curConnectionString).GetListForDuplication(curPurchaseRelationCodes, fields: null);
                        if (curItems != null && curItems.Any())
                            items.AddRange(curItems);
                    }
                    else
                    {
                        //跨云
                        var dbApi = new DbAccessUtility(new ApiDbConfigModel { DbNameConfigId = curDb.DbNameConfig.Id, Location = dbLocation, PlatformType = dbLocation });
                        var sql = "SELECT * FROM PurchaseOrderItemRelation(NOLOCK) WHERE PurchaseRelationCode IN @Codes";

                        var batchSize = 500;
                        var count = Math.Ceiling(curPurchaseRelationCodes.Count * 1.0 / batchSize);
                        for (var i = 0; i < count; i++)
                        {
                            var batchCodes = curPurchaseRelationCodes.Skip(i * batchSize).Take(batchSize).ToList();
                            var curItems = dbApi.Query<PurchaseOrderItemRelation>(sql, new { Codes = batchCodes }).ToList();
                            if (curItems != null && curItems.Any())
                                items.AddRange(curItems);
                        }
                    }
                }

                //所有库都查
                if (pids != null && pids.Any())
                {
                    if (dbLocation == CustomerConfig.CloudPlatformType)
                    {
                        //同云，直接查询
                        var curConnectionString = g.Key;
                        var curRelations = new PurchaseOrderRelationService(curConnectionString).GetListAndItems(pids, whereFieldName: "o.PurchasePlatformOrderId");
                        if (curRelations != null && curRelations.Any())
                        {
                            relations.AddRange(curRelations);
                            items.AddRange(curRelations.Where(a => a.ItemRelations != null).SelectMany(a => a.ItemRelations));
                        }
                    }
                    else
                    {
                        //跨云
                        var dbApi = new DbAccessUtility(new ApiDbConfigModel { DbNameConfigId = curDb.DbNameConfig.Id, Location = dbLocation, PlatformType = dbLocation });
                        var sql = "SELECT * FROM PurchaseOrderRelation(NOLOCK) WHERE PurchasePlatformOrderId IN @Codes AND Status=1";
                        var sqlItem = "SELECT * FROM PurchaseOrderItemRelation(NOLOCK) WHERE PurchasePlatformOrderId IN @Codes";

                        var batchSize = 500;
                        var count = Math.Ceiling(pids.Count * 1.0 / batchSize);
                        for (var i = 0; i < count; i++)
                        {
                            var batchCodes = pids.Skip(i * batchSize).Take(batchSize).ToList();
                            var curRelations = dbApi.Query<PurchaseOrderRelation>(sql, new { Codes = batchCodes }).ToList();
                            if (curRelations != null && curRelations.Any())
                                relations.AddRange(curRelations);

                            var curItems = dbApi.Query<PurchaseOrderItemRelation>(sqlItem, new { Codes = batchCodes }).ToList();
                            if (curItems != null && curItems.Any())
                                items.AddRange(curItems);
                        }
                    }
                }
            });

            return Tuple.Create(relations, items);
        }
        /// <summary>
        /// 获取所有采购单分销用户ID
        /// </summary>
        /// <returns></returns>
        public List<int> GetAllPurchaseOrderFxUserIds()
        {
            return _repository.GetAllPurchaseOrderFxUserIds();
        }

        /// <summary>
        /// 获取采购单 按采购单分销用户ID
        /// </summary>
        /// <param name="purchaseOrderFxUserId"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public List<PurchaseOrderRelation> GetListByPurchaseOrderFxUserId(int purchaseOrderFxUserId,
            string selectFields = "*")
        {
            return _repository.GetListByPurchaseOrderFxUserId(purchaseOrderFxUserId, selectFields: selectFields);
        }
    }
}