using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Services
{

    public partial class SellerInfoService : BaseService<Data.Entity.SellerInfo>
    {
        private SellerInfoRepository _repository;

        public SellerInfoService() : base(CustomerConfig.SellerInfoConfigureDBConnectionString)
        {
            _repository = new SellerInfoRepository();
        }

        public SellerInfoService(string connectionString) : base(connectionString)
        {
            _repository = new SellerInfoRepository(connectionString);
        }

        private string GetFilterSql()
        {
            //var filterSql = " AND dn.ApplicationName='fx' ";
            var filterSql = " AND dn.DbName IN('alibaba_fendan_db','alibaba_test_fendan_db') ";
            if (CustomerConfig.IsLocalDbDebug && CustomerConfig.ConfigureDbConnectionString.Contains("192.168"))
                filterSql = " AND dn.DbName ='AlibabaFenFaDB' ";
            return filterSql;
        }

        private string GetFilterSqlNew()
        {
            var filterSql = " AND dn.DbName IN @DbName";
            if (CustomerConfig.IsLocalDbDebug && CustomerConfig.ConfigureDbConnectionString.Contains("192.168"))
                filterSql = " AND dn.DbName ='AlibabaFenFaDB' ";
            return filterSql;
        }

        private DynamicParameters GetFilterSqlNewParamters()
        {
            //var filterSql = " AND dn.ApplicationName='fx' ";
            DynamicParameters filterParamters = new DynamicParameters();
            filterParamters.Add($"@DbName", new List<string> { "alibaba_fendan_db", "alibaba_test_fendan_db" });
            if (CustomerConfig.IsLocalDbDebug && CustomerConfig.ConfigureDbConnectionString.Contains("192.168"))
                filterParamters = null;
            return filterParamters;
        }

        /// <summary>
        /// 获取默认发件人信息
        /// </summary>
        /// <param name="shopId">店铺ID</param>
        /// <returns></returns>
        public SellerInfo GetByIdFromApi(int Id, int shopId)
        {
            //SellerInfo sellerInfo = _repository.DbConnection.Query<SellerInfo>($"select TOP 1 * from P_SellerInfo WITH(NOLOCK) WHERE Id={Id} AND ShopId={shopId} ORDER BY IsDefault DESC").FirstOrDefault();
            //if (sellerInfo != null)
            //    return sellerInfo;

            var sellerInfos = new List<SellerInfo>();
            DbPolicyExtension.ParellelForeachAllDbs(db =>
            {
                var sellerInfo = db.Query<SellerInfo>($"select TOP 1 * from P_SellerInfo WITH(NOLOCK) WHERE Id={Id} AND ShopId={shopId} ORDER BY IsDefault DESC").FirstOrDefault();
                if (sellerInfo != null)
                    sellerInfos.Add(sellerInfo);
            }, 
            filter:GetFilterSqlNew(),
            filterParamters: GetFilterSqlNewParamters());
            return sellerInfos.OrderByDescending(a => a.IsDefault).FirstOrDefault();
        }


        /// <summary>
        /// 获取默认发件人信息
        /// </summary>
        /// <param name="shopId">店铺ID</param>
        /// <returns></returns>
        public SellerInfo GetDefaultSeller(int shopId)
        {
            //SellerInfo sellerInfo = _repository.DbConnection.Query<SellerInfo>("select top 1 * from P_SellerInfo WHERE ShopId=@ShopId order by IsDefault DESC", new { ShopId = shopId }).FirstOrDefault();
            //if (sellerInfo != null)
            //    return sellerInfo;

            var sellerInfos = new List<SellerInfo>();
            DbPolicyExtension.ParellelForeachAllDbs(db =>
            {
                var sellerInfo = db.Query<SellerInfo>($"select top 1 * from P_SellerInfo WITH(NOLOCK) WHERE ShopId={shopId} order by IsDefault DESC").FirstOrDefault();
                if (sellerInfo != null)
                    sellerInfos.Add(sellerInfo);
            }, filter: GetFilterSqlNew(),
            filterParamters: GetFilterSqlNewParamters());
            if (sellerInfos.IsNotNullAndAny())
            {
                return sellerInfos.Where(a => a != null).OrderByDescending(a => a.IsDefault).FirstOrDefault();
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 获取默认发件人信息
        /// </summary>
        /// <param name="shopId">店铺ID</param>
        /// <returns></returns>
        public SellerInfo GetDefaultSellerByPlatfromType(int shopId, ApiDbConfigModel targetApiDbConfig)
        {
            var dbAccessUtility = new DbAccessUtility(SiteContext.Current.CurrentLoginShopApiRequestModel, targetApiDbConfig);
            return dbAccessUtility.Query<SellerInfo>("select top 1 * from P_SellerInfo WITH(NOLOCK) WHERE ShopId=@ShopId order by IsDefault DESC", new { ShopId = shopId }).FirstOrDefault();
        }


        /// <summary>
        /// 获取主店铺发件人信息
        /// </summary>
        /// <param name="shopId">店铺ID</param>
        /// <returns></returns>
        public List<SellerInfo> GetMasterSellerInfo(List<int> shopIds, ApiDbConfigModel targetApiDbConfig)
        {
            var dbAccessUtility = new DbAccessUtility(SiteContext.Current.CurrentLoginShopApiRequestModel, targetApiDbConfig);
            return dbAccessUtility.Query<SellerInfo>("select * from P_SellerInfo WITH(NOLOCK) WHERE ShopId in @sids", new { sids = shopIds });
        }


        /// <summary>
        /// 获取主店铺默认发件人信息
        /// </summary>
        /// <returns></returns>
        public SellerInfo GetMasterShopDefaultSeller()
        {
            var shopId = SiteContext.Current.MasterShop.Id;
            var sql = "select top 1 * from P_SellerInfo WITH(NOLOCK) WHERE ShopId=@ShopId order by IsDefault DESC";
            var dbAccessUtility = new DbAccessUtility(SiteContext.Current.CurrentLoginShopApiRequestModel, SiteContext.Current.MasterShopApiRequestModel);
            var seller = dbAccessUtility.Query<SellerInfo>(sql, new { ShopId = shopId })?.FirstOrDefault();
            return seller;
        }


        public SellerInfo GetOneDefaultSeller(int shopId)
        {
            SellerInfo sellerInfo = null;
            DbPolicyExtension.ParellelForeachAllDbs(db =>
            {
                // (2021-12-02 拼多多和京东发件人Id与阿里不一致，此处先不查询其他平台，3个平台发件人Id一致后再恢复)
                if (db.TargetDbLocation == PlatformType.Alibaba.ToString())
                    sellerInfo = db.Query<SellerInfo>($"select top 1 * from P_SellerInfo WITH(NOLOCK) WHERE ShopId=@ShopId and IsDefault=1").FirstOrDefault();
            }, filter: GetFilterSqlNew(),
            filterParamters: GetFilterSqlNewParamters());
            return sellerInfo;
            //return _repository.DbConnection.Query<SellerInfo>("select top 1 * from P_SellerInfo WHERE ShopId=@ShopId and IsDefault=1 ", new { ShopId = shopId }).FirstOrDefault();
        }


        //public SellerInfo GetOneDefaultSellerByPlatformType(int shopId, string platformType)
        //{
        //    var sql = "select top 1 * from P_SellerInfo WHERE ShopId=@ShopId and IsDefault=1";
        //    if (CustomerConfig.IsDbAvaliableInCurrentSite(SiteContext.Current.CurrentLoginShop.PlatformType, platformType) == false)
        //    {
        //        var utitly = new DianGuanJiaApp.Data.Dapper.DbApiAccessUtility((PlatformType)Enum.Parse(typeof(PlatformType), platformType));
        //        return utitly.Query<SellerInfo>(sql, new { ShopId = shopId })?.ToList()?.FirstOrDefault();
        //    }
        //    else
        //    {
        //        var db = _repository.GetDb(platformType);
        //        return db.Query<SellerInfo>(sql, new { ShopId = shopId }).FirstOrDefault();
        //    }
        //}

        public SellerInfo GetOneDefaultSellerByPlatformType(int shopId, string platformType)
        {
            var sql = "select top 1 * from P_SellerInfo WITH(NOLOCK) WHERE ShopId=@ShopId and IsDefault=1";
            var dbAccessUtility = new DbAccessUtility(SiteContext.Current.CurrentLoginShopApiRequestModel, SiteContext.Current.MasterShopApiRequestModel);
            return dbAccessUtility.Query<SellerInfo>(sql, new { ShopId = shopId })?.ToList()?.FirstOrDefault();
        }

        /// <summary>
        /// 获取发件人信息
        /// </summary>
        /// <param name="shopId">店铺ID</param>
        /// <returns></returns>
        public List<SellerInfo> GetSellerInfoList(int shopId)
        {
            List<SellerInfo> sellerInfos = null;
            DbPolicyExtension.ParellelForeachAllDbs(db =>
            {
                // (2021-12-02 拼多多和京东发件人Id与阿里不一致，此处先不查询其他平台，3个平台发件人Id一致后再恢复)
                if (db.TargetDbLocation == PlatformType.Alibaba.ToString())
                    sellerInfos = db.Query<SellerInfo>($"select * from P_SellerInfo WITH(NOLOCK) WHERE ShopId={shopId} order by IsDefault DESC").ToList();
            }, filter: GetFilterSqlNew(),
            filterParamters: GetFilterSqlNewParamters());
            return sellerInfos;

            //return _repository.DbConnection.Query<SellerInfo>("select * from P_SellerInfo WHERE ShopId=@ShopId order by IsDefault DESC", new { ShopId = shopId }).ToList();
        }

        public SellerInfo GetSellerInfoById(int shopId, int sellerId)
        {
            return _repository.DbConnection.Query<SellerInfo>(@"SELECT * FROM dbo.P_SellerInfo WITH(NOLOCK) WHERE ShopId = @ShopId AND Id = @Id", new { ShopId = shopId, Id = sellerId }).FirstOrDefault();
        }

        /// <summary>
        /// 获取老系统发件人信息
        /// </summary>
        /// <param name="memberid">店铺ID</param>
        /// <returns></returns>
        public List<SellerInfo> GetOldSellerInfoList(string memberId, string platformType)
        {
            var db = _repository.GetOldDb(platformType);
            return db.Query<SellerInfo>("select SellerName,SellerPhone,SellerAddress from P_SellerInfo WITH(NOLOCK) WHERE MemberId=@MemberId order by AddDate DESC", new { MemberId = memberId }).ToList();
        }


        /// <summary>
        /// 获取发件人信息
        /// </summary>
        /// <param name="shopId">店铺ID</param>
        /// <returns></returns>
        public List<SellerInfo> GetSellerInfoList(int shopId, string name, string mobile, string phone, string addr)
        {
            var sql = "select * from P_SellerInfo WITH(NOLOCK) WHERE ShopId=@ShopId {0} order by IsDefault DESC";

            DynamicParameters dps = new DynamicParameters();
            dps.Add("ShopId", shopId, System.Data.DbType.Int32, System.Data.ParameterDirection.Input);

            var where = string.Empty;
            if (string.IsNullOrWhiteSpace(name) == false)
            {
                where += " AND SellerName like @name ";
                dps.Add("name", "%" + name?.Trim() + "%", System.Data.DbType.String, System.Data.ParameterDirection.Input, 50);
            }
            if (string.IsNullOrWhiteSpace(mobile) == false)
            {
                where += " AND SellerMobile=@mobile ";
                dps.Add("mobile", mobile?.Trim(), System.Data.DbType.String, System.Data.ParameterDirection.Input, 32);
            }
            if (string.IsNullOrWhiteSpace(phone) == false)
            {
                where += " AND SellerPhone=@phone ";
                dps.Add("phone", phone?.Trim(), System.Data.DbType.String, System.Data.ParameterDirection.Input, 50);
            }
            if (string.IsNullOrWhiteSpace(addr) == false)
            {
                where += " AND SellerAddress like @addr ";
                dps.Add("addr", "%" + addr?.Trim() + "%", System.Data.DbType.String, System.Data.ParameterDirection.Input, 255);
            }
            sql = string.Format(sql, where);
            return _repository.DbConnection.Query<SellerInfo>(sql, dps).ToList();
        }


        /// <summary>
        /// 获取发件人信息
        /// </summary>
        /// <param name="shopId">店铺ID</param>
        /// <param name="keyWord">搜索关键字</param>
        /// <returns></returns>
        public List<SellerInfo> GetSellerInfoList(int shopId, string keyWord)
        {
            keyWord = WebHelper.StrFilter(keyWord);
            var sql = "select * from P_SellerInfo WITH(NOLOCK) WHERE ShopId=@ShopId ";
            if (string.IsNullOrWhiteSpace(keyWord) == false)
            {
                sql += " AND (CompanyName LIKE @keyWord OR SellerName LIKE @keyWord OR SellerPhone LIKE @keyWord OR SellerAddress LIKE @keyWord) ";
            }
            sql += " order by IsDefault DESC";
            return _repository.DbConnection.Query<SellerInfo>(sql, new { ShopId = shopId, keyWord = "'%" + keyWord + "%'" }).ToList();
        }


        /// <summary>
        /// 获取发件人信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public SellerInfo GetSellerInfo(int id)
        {
            return _repository.DbConnection.Query<SellerInfo>("select * from P_SellerInfo WHERE Id=@Id;", new { Id = id }).FirstOrDefault();
        }

        public SellerInfo GetSellerInfo(SellerInfo seller, int shopId)
        {
            if (seller == null)
                return seller;
            return _repository.GetSellerInfo(seller, shopId);
        }

        /// <summary>
        /// 设置默认发件人
        /// </summary>
        /// <param name="id"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public bool SetDefault(int id, int shopId)
        {
            var effectCount = _repository.SetDefault(id, shopId);
            return effectCount > 0;
        }

        /// <summary>
        /// 添加发件人
        /// </summary>
        /// <param name="model"></param>
        public int AddSellerInfo(SellerInfo model)
        {
            var newSellerId = 0;
            //1.先判断是否存在，已存在，则不再重复添加
            var sql = "select * from P_SellerInfo WITH(NOLOCK) WHERE ShopId=@ShopId AND SellerName=@SellerName AND (SellerMobile=@SellerMobile Or SellerPhone=@SellerPhone) AND SellerAddress=@SellerAddress";
            var seller = _repository.DbConnection.Query<SellerInfo>(sql, new
            {
                ShopId = model.ShopId,
                SellerName = model.SellerName,
                SellerMobile = model.SellerMobile,
                SellerPhone = model.SellerPhone,
                SellerAddress = model.SellerAddress,
            });


            //日志开始
            var subLog = LogForOperatorContext.Current.StartStep(new LogForOperator()
            {
                OperatorType = "添加发件人"
            });

            if (seller == null || seller.Count() == 0)
            {
                subLog.Remark = "发件人不存在，执行了添加";

                //不存在则添加
                newSellerId = _repository.Add(model);
            }
            else
            {
                //存在，则修改
                var first = seller.OrderByDescending(f => f.IsDefault).FirstOrDefault();
                if (first.IsDefault != model.IsDefault)
                {

                    subLog.Remark = "发件存在，执行了修改";

                    first.IsDefault = model.IsDefault;
                    _repository.Update(first);
                    newSellerId = first.Id;
                }
            }
            return newSellerId;
        }

        /// <summary>
        /// 添加发件人
        /// </summary>
        /// <param name="model"></param>
        public void AddSellerInfoByPlatformType(SellerInfo model, ApiDbConfigModel apiDbConfigModel)
        {
            //SiteContext.Current.CurrentLoginShop.PlatformType
            //1.先判断是否存在，已存在，则不再重复添加
            var sql = "select * from P_SellerInfo WITH(NOLOCK) WHERE ShopId=@ShopId AND SellerName=@SellerName AND (SellerMobile=@SellerMobile Or SellerPhone=@SellerPhone) AND SellerAddress=@SellerAddress";
            var dbAccessUtility = new DbAccessUtility(SiteContext.Current.CurrentLoginShopApiRequestModel, apiDbConfigModel);
            var apiSqlParams = new
            {
                ShopId = model.ShopId,
                SellerName = model.SellerName,
                SellerMobile = model.SellerMobile,
                SellerPhone = model.SellerPhone,
                SellerAddress = model.SellerAddress,
            };
            var seller = dbAccessUtility.Query<SellerInfo>(sql, apiSqlParams)?.ToList();


            //日志开始
            var subLog = LogForOperatorContext.Current.StartStep(new LogForOperator()
            {
                OperatorType = "添加发件人到主店铺"
            });

            if (seller == null || seller.Count() == 0)
            {
                //不存在则添加
                var insertSql = @"INSERT INTO dbo.P_SellerInfo( CompanyName ,SellerName ,SellerPhone ,SellerMobile ,SellerAddress ,AddDate ,SellerProvince ,SellerCity ,SellerArea ,ProvinceCode ,CityCode ,AreaCode ,IsDefault ,Type ,ShopId ,SellerDetailsAddress )
                                    VALUES  ( @CompanyName ,@SellerName ,@SellerPhone ,@SellerMobile ,@SellerAddress ,GETDATE() ,@SellerProvince ,@SellerCity ,@SellerArea ,'' ,'' ,'' ,@IsDefault ,@Type ,@ShopId ,@SellerDetailsAddress);";
                var insertParams = new
                {
                    CompanyName = model.CompanyName,
                    SellerName = model.SellerName,
                    SellerPhone = model.SellerPhone,
                    SellerMobile = model.SellerMobile,
                    SellerAddress = model.SellerAddress,
                    SellerProvince = model.SellerProvince,
                    SellerCity = model.SellerCity,
                    SellerArea = model.SellerArea,
                    IsDefault = model.IsDefault,
                    Type = model.Type,
                    ShopId = model.ShopId,
                    SellerDetailsAddress = model.SellerAddress,
                };
                var result = dbAccessUtility.ExecuteScalar(insertSql, insertParams);

                subLog.Remark = "发件人不存在，执行了添加";
            }
            else
            {
                //存在，则修改为默认
                var first = seller.OrderByDescending(f => f.IsDefault).FirstOrDefault();
                if (first.IsDefault != model.IsDefault)
                {
                    subLog.Remark = "发件存在，执行了修改";

                    first.IsDefault = model.IsDefault;
                    dbAccessUtility.ExecuteScalar("UPDATE dbo.P_SellerInfo SET IsDefault=1 WHERE Id=@id", new { id = first.Id });
                }
            }


        }
        /// <summary>
        /// 检查店铺是否有默认的发件人信息
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="apiDbConfigModel"></param>
        /// <returns></returns>
        public bool IsShopHasDefaultSellerInfo(int shopId, ApiDbConfigModel apiDbConfigModel)
        {
            var sql = "select Id from P_SellerInfo WITH(NOLOCK) WHERE ShopId=@ShopId AND IsDefault=1";
            var dbAccessUtility = new DbAccessUtility(SiteContext.Current.CurrentLoginShopApiRequestModel, apiDbConfigModel);
            Log.Debug($"跨库获取发件人信息，目标数据库信息：{dbAccessUtility.TargetDbInformation}");
            return dbAccessUtility.Query<SellerInfo>(sql, new { shopId })?.FirstOrDefault() != null;
        }


        public bool BulkWrite(List<SellerInfo> sellers)
        {
            return _repository.BulkWrite(sellers);
        }
        /// <summary>
        /// 新版关联的小程序--添加发件人
        /// </summary>
        /// <param name="model"></param>
        /// <param name="AddDefaul">只添加默认发件人</param>
        public void WxAddSellerInfo(SellerInfo model, bool AddDefaul = false)
        {
            SellerInfo sl = GetOneDefaultSeller(model.ShopId);
            if (sl == null)
                model.IsDefault = true;

            if (AddDefaul)
            {
                if (sl == null)
                    _repository.Add(model);
            }
            else
            {
                //1.先判断是否存在，已存在，则不再重复添加
                var sql = @"select * from P_SellerInfo WITH(NOLOCK) WHERE ShopId=@ShopId AND SellerName=@SellerName AND SellerMobile=@SellerMobile AND SellerProvince=@SellerProvince  
                       AND SellerCity=@SellerCity AND SellerArea=@SellerArea AND SellerAddress=@SellerAddress";

                var seller = _repository.DbConnection.Query<SellerInfo>(sql, new
                {
                    ShopId = model.ShopId,
                    SellerName = model.SellerName,
                    SellerMobile = model.SellerMobile,
                    SellerPhone = model.SellerPhone,
                    SellerProvince = model.SellerProvince,
                    SellerCity = model.SellerCity,
                    SellerArea = model.SellerArea,
                    SellerAddress = model.SellerAddress,
                }).FirstOrDefault();
                if (seller == null)
                    _repository.Add(model);
            }



        }


        /// <summary>
        /// 微商小程序添加发件人
        /// </summary>
        /// <param name="model"></param>
        public void WsXcxAddSellerInfo(SellerInfo model)
        {
            //1.先判断是否存在，已存在，则不再重复添加
            var sql = @"select * from P_SellerInfo WITH(NOLOCK) WHERE ShopId=@ShopId AND SellerName=@SellerName AND SellerMobile=@SellerMobile AND SellerProvince=@SellerProvince  
                       AND SellerCity=@SellerCity AND SellerArea=@SellerArea AND SellerAddress=@SellerAddress";

            var seller = _repository.DbConnection.Query<SellerInfo>(sql, new
            {
                ShopId = model.ShopId,
                SellerName = model.SellerName,
                SellerMobile = model.SellerMobile,
                SellerPhone = model.SellerPhone,
                SellerProvince = model.SellerProvince,
                SellerCity = model.SellerCity,
                SellerArea = model.SellerArea,
                SellerAddress = model.SellerAddress,
            }).FirstOrDefault();

            if (seller == null)
            {
                SellerInfo sl = GetOneDefaultSeller(model.ShopId);
                if (sl == null)
                    model.IsDefault = true;

                _repository.Add(model);
            }
        }
        /// <summary>
        /// 微商小程序- 更新卖家
        /// </summary>
        /// <returns></returns>
        public bool UpdateSellerInfo(WxUpdateSellerAndUserRequestModel model, int shopId)
        {
            bool isOk = false;

            var sql = "select * from P_SellerInfo WITH(NOLOCK) WHERE ShopId=@ShopId  AND Id=@Id ";
            var seller = _repository.DbConnection.Query<SellerInfo>(sql, new
            {
                ShopId = shopId,
                Id = model.ids
            }).FirstOrDefault();

            if (seller != null)
            {
                seller.SellerName = model.name;
                seller.SellerMobile = model.telephone;
                seller.SellerPhone = model.telephone;
                seller.SellerProvince = model.province;
                seller.SellerCity = model.city;
                seller.SellerArea = model.area;
                seller.SellerAddress = model.otherAddress;
                isOk = _repository.Update(seller);
            }
            return isOk;
        }

        /// <summary>
        /// 数据越权校验
        /// </summary>
        /// <param name="id"></param>
        /// <returns>true 允许 false 不允许</returns>
        public bool DataPurviewCheck(int id)
        {
            var seller = this.GetSellerInfo(id);
            if (seller == null) return false;
            return SiteContext.Current.AllShopsWithMasterShop.Select(f => f.Id).Contains(seller.ShopId);
        }
    }
}
