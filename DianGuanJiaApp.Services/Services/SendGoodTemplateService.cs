using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility.Extension;
using System.Collections;
using System.Data;
using System.Text.RegularExpressions;
using System.Web;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services.Services;
using System.IO;
using System.Threading.Tasks;
using System.Diagnostics;
using Org.BouncyCastle.Tls.Crypto;
using System.Collections.Concurrent;
using DianGuanJiaApp.Services.Model.ExceptionModel;
using Newtonsoft.Json;

namespace DianGuanJiaApp.Services
{

    public partial class SendGoodTemplateService : BaseService<Data.Entity.SendGoodTemplate>
    {

        public SendGoodTemplateService()
        {
            _repository = new SendGoodTemplateRepository();
            this._baseRepository = _repository;
        }

        private SendGoodTemplateRepository _repository;
        //private OrderService _orderService = new OrderService();
        private ProductService _productService = new ProductService();
        private ProductFxService _productFxService = new ProductFxService();
        private CommonSettingService _commonSettingService = new CommonSettingService();

        public int _shopId
        {
            get
            {
                return SiteContext.Current.SendGoodTemplateShopId;
            }
        }

        public new SendGoodTemplate Get(int id, bool isLoadConfig = true)
        {
            var model = base.Get(id);
            if (isLoadConfig && string.IsNullOrWhiteSpace(model.Config) && model.RefTemplateId > 0)
            {
                var refTemplate = base.Get(model.RefTemplateId.Value);
                model.Config = refTemplate.Config;
            }
            return model;
        }

        /// <summary>
        /// 保存发货单模板
        /// </summary>
        /// <param name="tid"></param>
        /// <param name="templateName"></param>
        /// <param name="config"></param>
        /// <returns></returns>
        public int Save(int tid, string templateName, string config)
        {
            SendGoodTemplate template = null;
            if (tid > 0)
            {
                template = _repository.Get(tid);
                if (template == null)
                    throw new LogicException("未能查询到当前发货单模板信息，请刷新重试");
                if (!template.IsSystemTemplate && SiteContext.Current.ShopIds.Contains(template.ShopId) == false)
                    throw new LogicException("未能查询到当前发货单模板信息，请返回重试");
                else
                {
                    var sameNameTemplate = _repository.CheckExistSameName(tid, templateName, template.ShopId > 0 ? template.ShopId : SiteContext.Current.CurrentShopId);
                    if (sameNameTemplate != null)
                        throw new LogicException($"已经存在名为【{sameNameTemplate.TemplateName}】的发货单模板了，请更换模板名称");
                    template.TemplateName = templateName;
                    template.Config = config;
                    if (template.IsSystemTemplate)
                    {
                        template.Id = 0;
                        template.ShopId = _shopId;
                        tid = _repository.Add(template);
                    }
                    else
                    {
                        if (template.IsDeleted)
                            throw new LogicException("模板已被删除，无法编辑，请创建新模板");
                        _repository.Update(template);
                    }
                }
            }
            else
            {
                template = new SendGoodTemplate
                {
                    TemplateName = templateName,
                    Config = config,
                    IsDefault = false,
                    IsDeleted = false,
                    ShopId = _shopId,
                };
                tid = _repository.Add(template);
            }

            return tid;
        }

        /// <summary>
        /// 获取用户模板
        /// </summary>
        /// <returns></returns>
        public List<SendGoodTemplate> GetTemplates()
        {
            //获取老版本的模板
            var old = _repository.GetOldSendGoodTemplate(_shopId);
            var list = _repository.Get("where ShopId=@sid AND (IsDeleted=0 OR IsDeleted IS NULL) Order By Id desc ", new
            {
                sid = _shopId,
            }).ToList();
            var systems = GetSystemTemplates()?.ToList();
            if (list == null || !list.Any())
            {
                var news = new List<SendGoodTemplate>();

                //判断是否启用发货单新的优化逻辑，启用，则不复制发货单内容，复制系统模板id，使用的地方根据id去加载内容
                var enableNewLogic = _commonSettingService.GetBool("EnabeldFahuoTemplateNewLogic", 0);

                systems.ForEach(s =>
                {
                    var config = s.Config;
                    int? refTid = null;
                    if (enableNewLogic)
                    {
                        config = null;
                        refTid = s.Id;
                    }
                    var c = new SendGoodTemplate
                    {
                        ShopId = _shopId,
                        IsDefault = false,
                        Config = config,
                        IsDeleted = false,
                        TemplateName = s.TemplateName,
                        SizeInfo = s.SizeInfo,
                        DefaultPrinter = s.DefaultPrinter,
                        Snapshot = s.Snapshot,
                        RefTemplateId = refTid
                    };
                    news.Add(c);
                });
                _repository.Add(news);
                list.AddRange(news);
            }
            list.AddRange(systems);
            if (old != null)
            {
                list.Add(new SendGoodTemplate
                {
                    Id = old.Id,
                    TemplateName = string.IsNullOrEmpty(old.TemplateName) ? "老版发货单" : old.TemplateName,
                    IsTranslated = old.IsTranslated,
                    Config = "",
                    IsDefault = old.IsDefault,
                    IsOldTemplate = true,
                    ShopId = _shopId,
                    SizeInfo = new SendGoodTemplateSizeInfo
                    {
                        Width = old.Width.ToInt(),
                        Height = old.Height.ToInt(),
                        OffsetHeight = old.OffsetY.ToInt(),
                        OffsetWidth = old.OffsetX.ToInt()
                    },
                });
            }
            return list?.OrderByDescending(l => l.IsOldTemplate).ThenByDescending(l => l.Id)?.ToList();
        }

        /// <summary>
        /// 获取系统模板
        /// </summary>
        /// <returns></returns>
        public List<SendGoodTemplate> GetSystemTemplates()
        {
            var list = _repository.Get("where ShopId=0 AND (IsDeleted=0 OR IsDeleted IS NULL) Order By Id desc ", new { }).ToList();
            return list;
        }

        public void UpdateAsDefault(int templateId, bool isOldTemplate)
        {
            _repository.UpdateAsDefault(templateId, _shopId, isOldTemplate);
        }


        public List<SendGoodTemplateDataModel> GetOrderTemplateModel(PrintSendGoodTemplateRequestModel model, string host, string token, SendGoodTemplateModel config = null)
        {
            TkShipmentsInfoTranslateRecordService tkShipmentsInfoTranslateRecordService = new TkShipmentsInfoTranslateRecordService();
            int IsTranslated = 0;
            if (config != null && config.Config != null)
            {
                IsTranslated = config.Config.IsTranslated;
            }
            //判空处理
            if (model?.Orders == null || !model.Orders.Any())
            {
                throw new LogicException("未选择订单数据，请刷新重试");
            }
            var datas = new List<SendGoodTemplateDataModel>();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var orderEntities = new List<Order>();
            //兼容冷热分离
            var ordersByGroup = model.Orders.GroupBy(m => m.DataFlag).ToList();
            ordersByGroup.ForEach(grouping =>
            {
                //冷数据
                var isColdData = grouping.Key == 1;
                //订单信息
                var orderModels =
                    new ColdOrderService(isColdData).GetOrders(grouping.ToList(), model.IsCustomerOrder, isBaseProduceCombine: SiteContext.Current.BaseProductSetting.OrderCombine);
                if (orderModels != null && orderModels.Any())
                {
                    /*商品归一的图片处理 START*/
                    foreach (var item in orderModels)
                    {
                        foreach (var orderItem in item.OrderItems)
                        {
                            if (orderItem.BaseProductSkuUid > 0 && !orderItem.ProductImgUrl.Contains("http"))
                            {
                                // 异常图片无法显示在打印预览
                                // newpath = "https://6tfxali.dgjapp.com/Common/GetImageFile?objectKey=0739C566C17F587817753D01A0A0021E7592DC8DB42CD34C17968D222725D09582F54C63628DB9C10E3D89F2803C6CD524AB89B0942E6141E68FA2FB471200F9C809B8D05176D6E12A002D2C0A29B4710E3DF58A994EA9C7B5EBAA40714A78DC&platform=Alibaba&token=7B0821975FC5A62DCFD2FAC296FDDE3A";
                                // newpath = "https://6tfxali.dgjapp.com/Common/GetImageFile?objectKey=0739C566C17F587817753D01A0A0021E7592DC8DB42CD34C17968D222725D09582F54C63628DB9C10E3D89F2803C6CD524AB89B0942E6141E68FA2FB471200F9C809B8D05176D6E1BCC28D86AE390DC173537F5847999539C42F45EA26EAC157&platform=Alibaba&token=7B0821975FC5A62DCFD2FAC296FDDE3A";
                                // var newpath = $"{host}/ImageFile/Get?objectKey={orderItem.ProductImgUrl.Replace("/Common/GetImageFile?objectKey=", "")}";

                                var newpath = CustomerConfig.AlibabaFenFaSystemUrl + orderItem.ProductImgUrl.Replace("Common/GetImageFile", "ImageFile/Get");
                                orderItem.ProductImgUrl = newpath;
                            }
                        }
                    }
                    /*商品归一的图片处理 END*/

                    orderEntities.AddRange(orderModels);
                }
            });

            var orderService = new OrderService();
            //var orderEntities = orderService.GetOrders(model.Orders, model.IsCustomerOrder,true);

            if (orderEntities == null || !orderEntities.Any())
                throw new LogicException("未能查询到您选择的订单数据，请刷新重试");

            if (fxUserId > 0)
            {
                //是否显示价格处理
                #region 获取订单上下游关系 
                var pathFlowRepository = new PathFlowRepository();
                var pathFlowCodes = orderEntities.Select(x => x.PathFlowCode).Distinct().ToList();
                var logicOrderRepository = new LogicOrderRepository();
                var pathFlowNodes = new List<PathFlowNode>();
                var pathFlowNodeDic = new Dictionary<string, List<PathFlowNode>>();
                if (pathFlowCodes.Any())
                {
                    pathFlowNodes = pathFlowRepository.GetPathFlowNodeList(pathFlowCodes);
                    pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(pathFlowNodes);
                }
                #endregion

                // 当前用户的商家信息
                var supplierUserRepository = new SupplierUserRepository();
                var agentUsers = supplierUserRepository.GetByFxUserId(fxUserId, false);
                var commonSettingRepository = new CommonSettingRepository();
                orderEntities.ForEach(o =>
                {
                    var agentUser = agentUsers.FirstOrDefault(x => x.FxUserId == o.UpFxUserId && x.SupplierFxUserId == fxUserId);
                    var isSelf = (agentUser == null);
                    //价格设置
                    if (!isSelf)
                    {
                        o.IsShowSalePrice = commonSettingRepository.SetIsShowSalePrice(fxUserId, o.PathFlowCode, pathFlowNodeDic);
                        o.IsShowProductTitle = commonSettingRepository.SetIsShowProductTitle(fxUserId, o.PathFlowCode, pathFlowNodeDic);
                        o.IsShowProductImg = commonSettingRepository.SetIsShowProductImg(fxUserId, o.PathFlowCode, pathFlowNodeDic);
                        if (!o.IsShowSalePrice)
                        {
                            o.TotalAmount = null;
                        }

                        o.OrderItems?.ForEach(item =>
                        {
                            if (!o.IsShowSalePrice)
                            {
                                item.Price = null;
                                item.ItemAmount = null;
                            }
                            if (!o.IsShowProductTitle)
                            {
                                item.ProductSubject = "";
                            }
                            if (!o.IsShowProductImg)
                            {
                                item.ProductImgUrl = "";
                            }
                        });
                    }
                    else
                    {
                        o.IsShowSalePrice = true;
                    }
                });
            }

            var products = new List<Product>();
            var productsByFx = new List<ProductFx>();
            var dict = new Dictionary<string, List<string>>();
            var pts = new List<string> { PlatformType.Pinduoduo.ToString(), PlatformType.Jingdong.ToString() };
            var shop = SiteContext.Current.CurrentLoginShop;
            //拼多多解密：分单系统判断平台类型，登录店铺平台是System，改为第一个订单的类型，为空就读取当前配置文件的平台
            var _CuurentPlatformType = shop.PlatformType;
            if (_CuurentPlatformType == "System")
            {
                _CuurentPlatformType = model.Orders[0].PlatformType;
            }
            if (string.IsNullOrEmpty(_CuurentPlatformType))
            {
                _CuurentPlatformType = CustomerConfig.CloudPlatformType;
            }
            if (_CuurentPlatformType == PlatformType.Pinduoduo.ToString())
            {
                orderService.TryToDecryptPddOrders(orderEntities, true);
                //orderEntitys.GroupBy(x => x.ShopId).ToList().ForEach(g => {
                //    var temp = SiteContext.Current.AllShops.FirstOrDefault(y => y.Id == g.Key);
                //    if (temp != null)
                //    {
                //        var service = new PinduoduoPlatformService(temp);
                //        service.DecryptBatch(g.ToList(), true);
                //    }
                //});
                //历史未加密数据加密处理
                orderEntities.ForEach(t =>
                {
                    t.ToMobile = t.ToMobile.ToEncrytPhone();
                    t.ToPhone = t.ToPhone.ToEncrytPhone();
                    t.BuyerMemberName = t.BuyerMemberName.ToEncryptName();
                    t.BuyerWangWang = t.BuyerWangWang.ToEncryptName();
                    t.ToName = t.ToName.ToEncryptName();
                    t.ToAddress = t.ToAddress.ToPddEncryptAddress();
                    t.ToFullAddress = t.ToProvince + t.ToCity + t.ToCounty + t.ToAddress.ToPddEncryptAddress();
                });
            }
            ShopService shopService = new ShopService();
            foreach (var order in model.Orders)
            {
                var curOrderEntity = orderEntities.FirstOrDefault(o => o.PlatformOrderId == order.PlatformOrderId && o.ShopId == order.ShopId);
                if (curOrderEntity == null)
                    throw new LogicException("未能查询到您选择的订单数据，请刷新重试");
                //如果是京东的订单，需要解密电话号码
                if (pts.Contains(curOrderEntity.PlatformType) && !string.IsNullOrEmpty(order.Receiver.toMobile) && order.Receiver.toMobile.Contains("*"))
                {
                    order.Receiver.toMobile = string.IsNullOrEmpty(curOrderEntity.ToMobile) ? curOrderEntity.ToPhone : curOrderEntity.ToMobile;
                }
                if (curOrderEntity.PlatformType == PlatformType.Pinduoduo.ToString())
                {
                    if (order.Receiver.toFullName?.Contains("*") == true)
                        order.Receiver.toFullName = curOrderEntity.ToName;
                    if (order.Receiver.toArea?.Contains("*") == true)
                        order.Receiver.toArea = curOrderEntity.ToFullAddress;
                    if (order.Receiver.BuyerWangWang?.Contains("*") == true)
                        order.Receiver.BuyerWangWang = curOrderEntity.BuyerWangWang;
                }
                CustomerOrder customerOrder = null;
                try
                {
                    customerOrder = curOrderEntity as CustomerOrder;
                }
                catch
                {
                }
                Shop orderShop = shopService.Get(order.ShopId);
                var selectedOrderItems = curOrderEntity.OrderItems?.Where(oi => order.OrderItems != null && order.OrderItems.Contains(oi.Id)).ToList();
                var data = new SendGoodTemplateDataModel() { Order = curOrderEntity };
                data.BuyerName = order.Buyer.BuyerMemberName;
                data.BuyerWangwang = order.Buyer.BuyerWangWang;
                data.BuyerPhone = order.Receiver.toPhone;
                data.BuyerAddress = order.Receiver.toArea;
                data.PayTime = curOrderEntity.PayTime?.ToString("yyyy-MM-dd HH:mm:ss");
                //自由打印的订单号应为用户自己填写的
                if (customerOrder != null && !string.IsNullOrEmpty(customerOrder.CustomerOrderId))
                {
                    data.OrderNumber = customerOrder.CustomerOrderId;
                    data.SinglePlatformOrderId = customerOrder.CustomerOrderId;
                }
                else
                {
                    var entityOrderId = string.IsNullOrWhiteSpace(curOrderEntity.CustomerOrderId) ? curOrderEntity.PlatformOrderId : curOrderEntity.CustomerOrderId;
                    data.OrderNumber = string.IsNullOrEmpty(curOrderEntity.ChildOrderId) ? entityOrderId : string.Join(";", selectedOrderItems?.Select(s => s.OrignalPlatformOrderId??"")?.Distinct() ?? new List<string>());
                    var orderIds = selectedOrderItems.Select(oi => string.IsNullOrEmpty(oi.OrignalOrderId) ? (string.IsNullOrWhiteSpace(oi.OrignalPlatformOrderId) ? oi.PlatformOrderId : oi.OrignalPlatformOrderId) : oi.OrignalOrderId).Distinct().ToList();
                    if (orderIds == null || !orderIds.Any())
                        data.SinglePlatformOrderId = entityOrderId;
                    else if (orderIds != null && orderIds.Count() == 1)
                        data.SinglePlatformOrderId = orderIds.FirstOrDefault();
                    // 合单使用合并后的单号
                    else if (orderIds != null && orderIds.Count() > 1)
                    {
                        if (selectedOrderItems.All(s => s.PlatformOrderId == selectedOrderItems.FirstOrDefault()?.PlatformOrderId))
                            data.SinglePlatformOrderId = selectedOrderItems.FirstOrDefault().PlatformOrderId;
                        else
                            data.SinglePlatformOrderId = selectedOrderItems.FirstOrDefault().OrignalPlatformOrderId;
                        Log.Debug(() => $"合单订单号：{data.SinglePlatformOrderId}，原始订单号PlatformOrderId：{selectedOrderItems.FirstOrDefault()?.PlatformOrderId} OrignalPlatformOrderId：{selectedOrderItems.FirstOrDefault()?.OrignalPlatformOrderId}");
                    }
                    if (config != null && config.TemplateItems.Any(t => t.textId == "sumPayment" && t.SubConfigs != null && t.SubConfigs.Any(s => s.textId == "totalWeight" && s.show)))
                        data.TotalWeight = GetOrderItemTotalWeight(selectedOrderItems.ToList()).ToFloat();
                }
                data.WaybillCode = order.WaybillCode;
                data.BuyerMessage = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, orderService.GetPrintRemark(curOrderEntity, order, isBuyerRemark: true, returnLineStr: ";"), fxUserId, orderShop.Id);//string.Join(";", curOrderEntity.BuyerRemark?.Split(new string[] { "|||" }, StringSplitOptions.RemoveEmptyEntries) ?? new string[] { "" });


                data.SellerRemark = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, orderService.GetPrintRemark(curOrderEntity, order, isBuyerRemark: false, returnLineStr: ";"), fxUserId, orderShop.Id);//string.Join(";", curOrderEntity.SellerRemark?.Split(new string[] { "|||" }, StringSplitOptions.RemoveEmptyEntries) ?? new string[] { "" });
                if (selectedOrderItems != null && selectedOrderItems.Count() == curOrderEntity.OrderItems.Count())
                    data.SumPayment = curOrderEntity.TotalAmount.ToFloat();
                else
                    data.SumPayment = (selectedOrderItems?.Sum(s => s.ItemAmount).ToFloat() ?? 0) + curOrderEntity.ShippingFee.ToFloat();
                data.CarriageFee = curOrderEntity.ShippingFee.ToFloat();
                data.TotalDiscount = selectedOrderItems?.Sum(s => (s.Price * s.Count) - s.ItemAmount).ToFloat() ?? 0;
                data.PlatformType = curOrderEntity.PlatformType;

                selectedOrderItems?.ToList()?.ForEach(s =>
                {
                    var skuSpecs = new List<string> { s.Color, s.Size };
                    if (curOrderEntity.PlatformType != "Alibaba" && curOrderEntity.PlatformType != PlatformType.TikTok.ToString())
                        skuSpecs.Add(s.ExtAttr1);
                    //if(config?.Config?.skuSort==1)
                    //    skuSpecs = skuSpecs.OrderBy(a=>a).ToList();
                    var skuHuoHao = s.productCargoNumber;
                    var huoHao = string.IsNullOrEmpty(s.CargoNumber) ? s.productCargoNumber : s.CargoNumber;
                    if (data.Order.PlatformType == PlatformType.YouZan.ToString())
                    {
                        skuHuoHao = s.CargoNumber;
                        huoHao = string.IsNullOrEmpty(s.productCargoNumber) ? s.CargoNumber : s.productCargoNumber;
                    }
                    var product = new SendGoodTemplateProductModel()
                    {
                        Id = s.Id,
                        EntryDiscount = (s.ItemAmount - (s.Price * (s.Count ?? 0))).ToFloat(),
                        LittleTotal = s.ItemAmount.ToFloat(),
                        Quantity = s.Count.ToInt(),
                        Price = s.Price.ToFloat(),
                        SkuSpecific = string.Join("", skuSpecs),
                        ImageUrl = s.ProductImgUrl.GetShowProductStr(curOrderEntity.IsShowProductImg),
                        ShortTitle = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, s.ShortTitle, fxUserId, orderShop.Id),//是否需要获取产品上的
                        PlatformTitle = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, s.ProductSubject.GetShowProductStr(curOrderEntity.IsShowProductTitle), fxUserId, orderShop.Id),
                        PlatformId = s.ProductID,
                        SkuId = s.SkuID,
                        CargoNumber = huoHao,
                        productCargoNumber = skuHuoHao,
                        PlatformOrderId = string.IsNullOrEmpty(s.OrignalOrderId) ? (string.IsNullOrWhiteSpace(s.OrignalPlatformOrderId) ? s.PlatformOrderId : s.OrignalPlatformOrderId) : s.OrignalOrderId,
                        ExtAttr4 = s.ExtAttr4
                    };
                    //有赞平台的所有商品属性存放在Color字段中
                    if (data.PlatformType == Data.Enum.PlatformType.YouZan.ToString() || data.PlatformType == Data.Enum.PlatformType.KuaiShou.ToString())
                        product.SkuSpecific = s.Color ?? "";
                    //if (curOrderEntity.PlatformType=="Taobao")
                    //    product.LittleTotal = product.Price * (s.Count??0) - Math.Abs(product.EntryDiscount/100);
                    //if (curOrderEntity.PlatformType == "Alibaba")
                    //    product.EntryDiscount = (-s.EntryDiscount.ToFloat() / 100);
                    if (!dict.ContainsKey(curOrderEntity.PlatformType))
                        dict.Add(curOrderEntity.PlatformType, new List<string> { s.ProductID });
                    else if (!dict[curOrderEntity.PlatformType].Contains(s.ProductID))
                        dict[curOrderEntity.PlatformType].Add(s.ProductID);

                    data.Products.Add(product);
                });
                //data.SumPayment = data.Products.Sum(p=>p.LittleTotal)+curOrderEntity.ShippingFee.ToFloat();
                data.Order = curOrderEntity;
                data.Order.OrderRequest = order;
                datas.Add(data);
            }
            dict.ToList().ForEach(kv =>
            {
                var pids = kv.Value?.Where(v => !string.IsNullOrEmpty(v)).ToList();
                var sids = orderEntities.Select(oe => oe.ShopId).Distinct().ToList();
                if (pids != null && pids.Any())
                {
                    //分发的用户
                    if (fxUserId > 0)
                    {
                        var ps = _productFxService.GetListByFxUserId(fxUserId, pids);
                        if (ps != null && ps.Any())
                            productsByFx.AddRange(ps);
                    }
                    else
                    {
                        var ps = _productService.GetList(sids, pids);
                        if (ps != null && ps.Any())
                            products.AddRange(ps);
                    }
                }

            });
            datas.ForEach(d =>
            {
                Shop orderShop = new Shop();
                if (d.Order != null && d.Order.ShopId != 0)
                {
                    orderShop = shopService.Get(d.Order.ShopId);
                }
                d.Products.ForEach(p =>
                {
                    //分发的用户
                    if (fxUserId > 0)
                    {
                        var temp = productsByFx.FirstOrDefault(t => t.PlatformId == p.PlatformId);
                        if (temp != null)
                        {
                            var skuShortTitle = temp.Skus?.FirstOrDefault(x => x.SkuId == p.SkuId)?.ShortTitle;
                            if (!string.IsNullOrEmpty(skuShortTitle))
                                p.ShortTitle = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, skuShortTitle, fxUserId, orderShop.Id);
                            else
                                p.ShortTitle = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, temp.ShortTitle, fxUserId, orderShop.Id);
                        }
                    }
                    else
                    {
                        var temp = products.FirstOrDefault(t => t.PlatformId == p.PlatformId);
                        if (temp != null)
                        {
                            var skuShortTitle = temp.Skus?.FirstOrDefault(x => x.SkuId == p.SkuId)?.ProductSkuAttr?.ShortTitle;
                            if (!string.IsNullOrEmpty(skuShortTitle))
                                p.ShortTitle = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, skuShortTitle, fxUserId, orderShop.Id);
                            else
                                p.ShortTitle = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, temp.ShortTitle, fxUserId, orderShop.Id);
                        }
                    }
                });
            });
            return datas;
        }


        /// <summary>
        /// 准备发货单打印需要的数据
        /// </summary>
        /// <param name="config"></param>
        /// <param name="datas"></param>
        /// <returns></returns>
        public List<SendGoodTemplatePageModel> PrepareSendTemplatePrintData(SendGoodTemplateModel config, List<SendGoodTemplateDataModel> datas)
        {
            //1.数据准备
            //1.1 订单数据：合并订单只打印一个发货单
            //1.2 商品数据：商品需要合并，另同一个订单中的商品，需判断是否需要合并同名称的商品，合并后排序
            //1.3 模板数据
            //1.4 数据整合
            //1.4.1合并相同商品名称
            var result = new List<SendGoodTemplatePageModel>();
            config.TemplateItems.ForEach(t =>
            {
                t.top += config.Config.listsTop * 3.779528f;
                t.left += config.Config.listsLeft * 3.779528f;
            });
            config.Config.tableTranslateY += (config.Config.listsTop * 3.779528f).ToInt();
            config.Config.tableTranslateX += (config.Config.listsLeft * 3.779528f).ToInt();
            datas.ForEach(d =>
            {
                d.Products = d.Products.OrderBy(p => p.Id).ThenBy(p => p.SkuSpecific).ToList();
                if (config.Config.offerSort == 2)
                    d.Products = d.Products.OrderBy(p => p.CargoNumber).ThenBy(p => p.SkuSpecific).ToList();
                if (config.Config.offerSort == 3)
                    d.Products = d.Products.OrderBy(p => p.PlatformTitle).ThenBy(p => p.SkuSpecific).ToList();
                if (config.Config.offerSort == 4)
                    d.Products = d.Products.OrderBy(p => p.CargoNumber).ThenBy(p => p.PlatformTitle).ThenBy(p => p.SkuSpecific).ToList();
                if (config.Config.offerSort == 5)
                    d.Products = d.Products.OrderBy(p => p.PlatformTitle).ThenBy(p => p.CargoNumber).ThenBy(p => p.SkuSpecific).ToList();
                if (config.Config.offerSort == 6)
                    d.Products = d.Products.OrderBy(p => p.ShortTitle).ThenBy(p => p.SkuSpecific).ToList();
                if (config.Config.offerSort == 7)
                    d.Products = d.Products.OrderBy(p => p.SkuSpecific).ThenBy(p => p.SkuSpecific).ToList();
                //2.table拼接
                //2.1 表头拼接
                //2.2 表内容拼接
                //3.表格下方元素top重算 top >表格的bottom:表格top+表头高度+表脚高度+行高*产品数量
                //4.数据替换
                config.TemplateItems.ForEach(t =>
                {
                    if (t.type == 1)
                    {
                        var or = d.Order?.OrderRequest;
                        var tofullName = or?.Receiver.toFullName;
                        var toMobile = string.IsNullOrEmpty(or?.Receiver.toMobile) ? or?.Receiver.toPhone : or?.Receiver.toMobile;
                        var toAddress = or?.Receiver.toArea;
                        if (or != null && or.PlatformType == PlatformType.Virtual.ToString())
                        {
                            tofullName = tofullName.ToTaobaoEncryptName();
                            toMobile = toMobile.ToEncrytPhone();
                            toAddress = toAddress.ToTaoBaoEncryptAddress();
                        }
                        switch (t.textId)
                        {
                            case "sellerName":
                                t.RenderText = or?.Sender?.SenderName;
                                break;
                            case "sellerPhone":
                                t.RenderText = or?.Sender?.SenderPhone;
                                break;
                            case "toFullName":
                                t.RenderText = tofullName;
                                break;
                            case "tologinId":
                                t.RenderText = d.Order?.BuyerWangWang;
                                break;
                            case "toMobile":
                                t.RenderText = toMobile;
                                break;
                            case "toArea":
                                t.RenderText = toAddress;
                                break;
                            case "printTime":
                                t.RenderText = d.PrintTime;
                                break;
                            case "gmtPayment":
                                t.RenderText = d.PayTime;
                                break;
                            case "id":
                                t.RenderText = d.OrderNumber;
                                break;
                            default:
                                t.RenderText = "";
                                break;
                        }
                    }
                    else if (t.type == 3)
                    {
                        if (t.textId.StartsWith("customize"))
                            t.RenderText = t.futext;
                        else if (t.textId == "buyerFeedBack")
                            t.RenderText = d.BuyerMessage;
                        else if (t.textId == "memo")
                            t.RenderText = d.SellerRemark;
                        else
                            t.RenderText = t.futext;
                        t.ContentType = "text";
                    }
                    //5:二维码 6图片 7订单条码 8跟踪号条码
                    else if (t.type == 5)
                    {
                        t.RenderText = t.futext;
                        t.ContentType = "qrcode";
                    }
                    else if (t.type == 6)
                    {
                        t.RenderText = t.futext;
                        t.ContentType = "image";
                    }
                    //TODO：条码不支持拆分订单和合并订单
                    else if (t.type == 7)
                    {
                        t.ContentType = "barcode";
                        t.RenderText = d.SinglePlatformOrderId;
                    }
                    else if (t.type == 8)
                    {
                        t.ContentType = "barcode";
                        t.RenderText = d.WaybillCode;
                    }
                    if (string.IsNullOrEmpty(t.ContentType))
                        t.ContentType = "text";
                });
                var tableTop = config.Config.tableTranslateY;
                var tableLeft = config.Config.tableTranslateX;
                var tableHeight = config.Config.tableHeight; //theaderHeight + tfooterHeight + d.Products.Count + 2*config.Config.TrHeight;//表头+表脚+表格内容 应与页面上的保持一致
                var underTableItems = config.TemplateItems.Where(t => t.top >= tableHeight + tableTop && t.type != 2).ToList();
                List<SendGoodTemplateItemModel> aboveOrInTableItems = null;
                if (underTableItems != null && underTableItems.Any())
                {
                    aboveOrInTableItems = config.TemplateItems.Where(t => t.type != 2 && !underTableItems.Any(u => u.textId == t.textId && u.id == t.id)).ToList();
                    //重算top
                    //underTableItems.ForEach(u => u.top = (u.top - tableHeight-tableTop) > 0 ? (u.top - tableHeight-tableTop) : 5);
                    //重算top left,转换为相对位置
                    underTableItems = underTableItems.OrderBy(item => item.top).ToList();
                    underTableItems.ForEach(current =>
                    {
                        current.relativeTop = current.top - tableTop - tableHeight;
                        current.relativeLeft = current.left - tableLeft;
                    });
                }
                else
                    aboveOrInTableItems = config.TemplateItems.Where(t => t.type != 2).ToList();
                var tableHtml = BuildTableHtml(d, config);
                //5.生成命令并排好顺序
                var commands = new List<SendGoodTemplateCommandModel>();
                if (aboveOrInTableItems != null && aboveOrInTableItems.Any())
                {
                    var temps = aboveOrInTableItems.Select(a => new SendGoodTemplateCommandModel
                    {
                        IsAbsolute = true,
                        Top = a.top,
                        Left = a.left,
                        RelativeLeft = a.relativeLeft,
                        RelativeTop = a.relativeTop,
                        Height = a.height,
                        Width = a.width,
                        Content = a.RenderText,
                        ContentType = a.ContentType,
                        FontFamily = a.fontFamily,
                        FontSize = a.fontSize,
                        FontWeight = a.fontWeight,
                    });
                    commands.AddRange(temps);
                }
                if (!string.IsNullOrEmpty(tableHtml))
                {
                    var headers = config.TemplateItems.Where(t => t.type == 2 && t.textId != "sumPayment").ToList();
                    var sumPayment = headers.FirstOrDefault(h => h.textId == "sumPayment");
                    commands.Add(new SendGoodTemplateCommandModel
                    {
                        IsAbsolute = true,
                        Top = config.Config.tableTranslateY,
                        Left = config.Config.tableTranslateX,
                        Width = headers == null ? sumPayment == null ? sumPayment.width : 100 : headers.Sum(t => t.width),
                        Height = headers == null ? sumPayment == null ? sumPayment.width : 100 : headers.Sum(t => t.height),
                        Content = tableHtml,
                        ContentType = "table"
                    });
                }
                if (underTableItems != null && underTableItems.Any())
                {
                    var temps = underTableItems.Select(a => new SendGoodTemplateCommandModel
                    {
                        IsAbsolute = false,
                        Top = a.top,
                        RelativeLeft = a.relativeLeft,
                        RelativeTop = a.relativeTop,
                        Left = a.left,
                        Height = a.height,
                        Width = a.width,
                        Content = a.RenderText,
                        ContentType = a.ContentType,
                        FontFamily = a.fontFamily,
                        FontSize = a.fontSize,
                        FontWeight = a.fontWeight,
                    });
                    commands.AddRange(temps);
                }
                result.Add(new SendGoodTemplatePageModel
                {
                    Commands = commands,
                    marginLeft = config.Config.listsTop,
                    marginTop = config.Config.listsLeft,
                    paperHeight = config.Config.listsHeight.ToString(),
                    paperWidth = config.Config.listsWidth.ToString(),
                    IsPrintInOnePage = config.Config.PrintSendNum == 1,
                    LogicOrderId = d.SinglePlatformOrderId
                });
            });
            return result;
        }

        /// <summary>
        /// 生成拣货单Table-HTML代码
        /// </summary>
        /// <param name="model"></param>
        /// <param name="config"></param>
        /// <returns></returns>
        public static string BuildTableHtml(SendGoodTemplateDataModel model, SendGoodTemplateModel config)
        {
            //表头
            var headers = config.TemplateItems.Where(t => t.type == 2)?.OrderBy(t => (t.order).ToInt()).ToList();
            if (headers == null || !headers.Any())
                return "";
            //TODO:生成样式
            //var tableHeader = new StringBuilder(string.Format("<tr width='{0}' height='{1}'>", headers.Select(h => h.width).Max(), headers.Select(h => h.height).Max()));
            var mersument = "px";
            var isYouzan = SiteContext.Current.CurrentLoginShop.PlatformType == Data.Enum.PlatformType.YouZan.ToString();
            var isKuaiShou = SiteContext.Current.CurrentLoginShop.PlatformType == Data.Enum.PlatformType.KuaiShou.ToString();
            //headers.ForEach(h =>
            //{
            //    var text = "颜色/尺寸";
            //    if (h.textId == "specInfo" && isYouzan)
            //    {
            //        text = "商品规格";
            //        tableHeader.Append(string.Format("<td width='{0}{3}' height='{1}{3}'>{2}</td>", h.width - 2, h.height, text, mersument));
            //    }
            //    else
            //        tableHeader.Append(string.Format("<td width='{0}{3}' height='{1}{3}'>{2}</td>", h.width - 2, h.height, h.futext, mersument));
            //});
            //tableHeader.Append("</tr>");
            var products = new List<SendGoodTemplateProductModel>();
            if (config.Config.TitleMerge == 1)
            {
                var groups = model.Products.GroupBy(p => p.PlatformTitle).ToList();
                foreach (var g in groups)
                {
                    if (g.Count() > 1)
                    {
                        products.Add(new SendGoodTemplateProductModel
                        {
                            Id = g.First().Id,
                            MegerCount = g.Count(),
                            EntryDiscount = g.Sum(k => k.EntryDiscount),
                            Quantity = g.Sum(k => k.Quantity),
                            Price = g.Sum(k => k.Price),
                            SkuSpecific = string.Join(";", g.Select(k => k.SkuSpecific).ToArray()),
                            ImageUrl = g.FirstOrDefault().ImageUrl,
                            ShortTitle = g.FirstOrDefault().ShortTitle,
                            PlatformTitle = g.FirstOrDefault().PlatformTitle,
                            Skus = g.ToList()?.Select(k => new SendGoodTemplateProductSkuModel
                            {
                                SkuSpecific = k.SkuSpecific,
                                CargoNumber = k.CargoNumber,
                                productCargoNumber = k.productCargoNumber,
                                Quantity = k.Quantity,
                                Price = k.Price,
                                EntryDiscount = k.EntryDiscount,
                                LittleTotal = k.LittleTotal,
                                PlatformOrderId = k.PlatformOrderId,
                                ExtAttr4 = k.ExtAttr4
                            }).ToList()
                        });
                    }
                    else
                    {
                        var first = g.FirstOrDefault();
                        if (first != null)
                        {
                            //first.EntryDiscount /= 100;
                            //first?.Skus?.ForEach(s=> {
                            //    s.EntryDiscount /= 100;
                            //});
                            products.Add(first);
                        }
                    }
                }
            }
            else
            {
                //model?.Products?.ForEach(p=> {
                //    p.EntryDiscount /= 100;
                //});
                products = model.Products;
            }
            var tableBody = new StringBuilder("<tr class='firstTr'>");
            //表头
            for (var i = 0; i < headers.Count(); i++)
            {
                var h = headers[i];
                if (h.textId != "sumPayment")
                {
                    var text = "颜色/尺寸";
                    if (h.textId == "specInfo" && isYouzan)
                        text = "商品规格";
                    //else if (h.textId == "specInfo" && isKuaiShou)
                    //    text = "SKU编码";
                    else
                        text = h.futext;
                    if (i == headers.Count - 1)//最后一列宽度设为自动
                        tableBody.Append(string.Format("<td style='font-size:{1};font-weight:{2};font-family:{3};text-align:center;'>{0}</td>", text, h.fontSize, h.fontWeight, h.fontFamily));
                    else
                        tableBody.Append(string.Format("<td style='width:{2}{6};font-size:{3};font-weight:{4};font-family:{5};text-align:center;' width='{0}{6}'>{1}</td>", h.width - 2, text, h.width, h.fontSize, h.fontWeight, h.fontFamily, mersument));

                }
            }
            tableBody.Append("</tr>");
            //if (config.Config.skuSort == 1)
            //    products = products.OrderBy(p => p.SkuSpecific).ToList();
            //else
            //    products = products.OrderBy(p => p.Id).ToList();
            var sumPayment = headers.FirstOrDefault(h => h.textId == "sumPayment");
            for (var i = 0; products != null && products.Any() && i < products.Count(); i++)
            {
                var isLastTr = i == products.Count - 1;
                var p = products[i];
                if (p.MegerCount > 1)
                {
                    for (var j = 0; j < p.Skus.Count(); j++)
                    {
                        var tr = new StringBuilder($"<tr class='{(isLastTr ? "lastTr" : "")}'>");
                        var sku = p.Skus[j];
                        for (var k = 0; k < headers.Count(); k++)
                        {
                            var h = headers[k];
                            if (
                                    (h.textId == "seqid"
                            || h.textId == "productPic"
                            || h.textId == "productName"
                            || h.textId == "orderId"
                            || h.textId == "productallName"
                            || h.textId == "productshortName") && j == 0 //第一行
                            )
                            {
                                if (h.textId == "productPic")
                                    tr.Append($"<td style='border-bottom:none;font-size:{h.contentFontSize};font-weight:{h.contentFontWeight};font-family:{h.contentFontFamily};' class='{h.textId}'><div><img src='{CaculateProductValueByName(p, h, i + 1)}' width='{config.Config.picSize}'/></div></td>");
                                else if (h.textId != "sumPayment")
                                    tr.Append($"<td class='{h.textId}' style='border-bottom:none;'><div  style='font-size:{h.contentFontSize};font-weight:{h.contentFontWeight};font-family:{h.contentFontFamily};width:{h.width}px;'>{CaculateProductValueByName(p, h, i + 1)}</div></td>");
                            }
                            else
                            {
                                if (h.textId == "seqid"
                                    || h.textId == "productPic"
                                    || h.textId == "productName"
                                    || h.textId == "orderId"
                                    || h.textId == "productallName"
                                    || h.textId == "productshortName"
                                    || h.textId == "sumPayment"
                                )
                                {
                                    if (h.textId == "sumPayment")
                                        continue;
                                    else
                                    {
                                        var borderBottom = "border-bottom:none;";
                                        if (j == p.Skus.Count() - 1)
                                            borderBottom = "";
                                        tr.Append($"<td style='border-top:none;{borderBottom}'  class='{h.textId}'></td>");
                                    }
                                }
                                else
                                {
                                    var str = $"<td class='{h.textId}'><div style='font-size:{h.contentFontSize};font-weight:{h.contentFontWeight};font-family:{h.contentFontFamily};width:{h.width}px;' >{CaculateSkuValueByName(sku, h, i + 1)}</div></td>";
                                    tr.Append(str);
                                }
                            }
                        }
                        tr.Append("</tr>");
                        tableBody.Append(tr.ToString());
                    }

                    //for (var j = 0; j < p.Skus.Count(); j++)
                    //{
                    //    var tr = new StringBuilder($"<tr class='{(isLastTr ? "lastTr" : "")}'>");
                    //    var sku = p.Skus[j];
                    //    for (var k = 0; k < headers.Count(); k++)
                    //    {
                    //        var h = headers[k];
                    //        if (
                    //                (h.textId == "seqid"
                    //        || h.textId == "productPic"
                    //        || h.textId == "productName"
                    //        || h.textId == "orderId"
                    //        || h.textId == "productallName"
                    //        || h.textId == "productshortName") && j == 0 //第一行
                    //        )
                    //        {
                    //            if (h.textId == "productPic")
                    //                tr.Append($"<td rowspan='{p.MegerCount}' style='font-size:{h.contentFontSize};font-weight:{h.contentFontWeight};font-family:{h.contentFontFamily};' class='{h.textId}'><div><img src='{CaculateProductValueByName(p, h, i + 1)}' width='{config.Config.picSize}'/></div></td>");
                    //            else if (h.textId != "sumPayment")
                    //                tr.Append($"<td rowspan='{p.MegerCount}'  class='{h.textId}'><div  style='font-size:{h.contentFontSize};font-weight:{h.contentFontWeight};font-family:{h.contentFontFamily};width:{h.width}px;'>{CaculateProductValueByName(p, h, i + 1)}</div></td>");
                    //        }
                    //        else
                    //        {
                    //            if (h.textId == "seqid"
                    //                || h.textId == "productPic"
                    //                || h.textId == "productName"
                    //                || h.textId == "orderId"
                    //                || h.textId == "productallName"
                    //                || h.textId == "productshortName"
                    //                || h.textId == "sumPayment"
                    //            )
                    //                continue;
                    //            else
                    //            {
                    //                var str = $"<td class='{h.textId}'><div style='font-size:{h.contentFontSize};font-weight:{h.contentFontWeight};font-family:{h.contentFontFamily};width:{h.width}px;' >{CaculateSkuValueByName(sku, h, i + 1)}</div></td>";
                    //                tr.Append(str);
                    //            }
                    //        }
                    //    }
                    //    tr.Append("</tr>");
                    //    tableBody.Append(tr.ToString());
                    //}
                }
                else
                {
                    var tr = new StringBuilder($"<tr class='{(isLastTr && sumPayment == null ? "lastTr" : "")}'>");
                    for (var k = 0; k < headers.Count(); k++)
                    {
                        var h = headers[k];
                        if (h.textId == "sumPayment")
                            continue;
                        if (h.textId == "productPic")
                            tr.Append($"<td style='text-align:center;' class='{h.textId}'><div style='font-size:{h.contentFontSize};font-weight:{h.contentFontWeight};font-family:{h.contentFontFamily};'><img src='{CaculateProductValueByName(p, h, i + 1)}' width='{config.Config.picSize}'/></div></td>");
                        else
                            tr.Append($"<td  class='{h.textId}'><div style='font-size:{h.contentFontSize};font-weight:{h.contentFontWeight};font-family:{h.contentFontFamily};width:{h.width}px;'>{CaculateProductValueByName(p, headers[k], i + 1)}</div></td>");
                    }
                    tr.Append("</tr>");
                    tableBody.Append(tr.ToString());
                }
            }
            if (sumPayment != null)
            {
                var subs = sumPayment.SubConfigs;
                //默认配置
                var totalDiscountString = "";
                if (model.TotalDiscount != 0)
                {
                    var totalDisCount = Math.Abs(model.TotalDiscount).ToString("f2");
                    if (model.TotalDiscount > 0)
                        totalDiscountString = "活动优惠：-￥" + totalDisCount + "元，";
                    else
                        totalDiscountString = "活动优惠：+￥" + totalDisCount + "元，";
                }
                if (subs == null || !subs.Any())
                    tableBody.Append(string.Format("<tr style='border-top:none;text-align:right;font-size:{4};font-weight:{5};font-family:{6};' class='lastTr'><td  style='border-top:none;' colspan='{3}'>合计：{7}总价：￥{0}元（含运费{1}元），总共{2}件</td></tr>", model.SumPayment, model.CarriageFee, model.ProductTotalCount, headers.Count - 1,
                        sumPayment.fontSize, sumPayment.fontWeight, sumPayment.fontFamily, totalDiscountString));
                else
                {
                    var str = string.Format("<tr style='text-align:right;font-size:{1};font-weight:{2};font-family:{3};' class='lastTr'><td style='border-top:none;' colspan='{0}'>合计：$ReplaceMent$ </td></tr>", headers.Count - 1, sumPayment.fontSize, sumPayment.fontWeight, sumPayment.fontFamily);
                    var rp = new List<string>();
                    subs.ForEach(sub =>
                    {
                        if (sub.show)
                        {
                            switch (sub.textId)
                            {
                                case "totalPrice":
                                    rp.Add($"{totalDiscountString}总价：￥{model.SumPayment}元（含运费{model.CarriageFee}元）");
                                    break;
                                case "totalCount":
                                    rp.Add($"总共{model.ProductTotalCount}件");
                                    break;
                                case "totalWeight":
                                    rp.Add($"总重{model.TotalWeight}kg");
                                    break;
                            }

                        }
                    });
                    str = str.Replace("$ReplaceMent$", string.Join("，", rp)).Trim('，');
                    tableBody.Append(str);
                }
            }
            var style = BuildStyle(config);
            var tableWidth = "auto";
            if (headers != null)
            {
                var other = headers.Where(h => h.textId != "sumPayment");
                if (other != null)
                    tableWidth = (other.Sum(h => h.width) + other.Count()) + mersument;
            }
            return string.Format("{1}<table style='width:{0};'>", tableWidth, style) + tableBody.ToString() + "</table>";
        }
        /// <summary>
        /// 根据发货单设置生成表格样式
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        private static string BuildStyle(SendGoodTemplateModel config)
        {
            var style = new StringBuilder("<style>table{border-collapse:collapse;} ");
            var lineType = config.Config.lineType;
            switch (lineType)
            {
                case 0://无线
                    style.Append("");
                    break;
                case 1://虚线
                    style.Append("td{border:1px dotted black;}");
                    break;
                case 2://实线
                    style.Append("td{border:1px solid black;}");
                    break;
                case 3://单实线
                    //style.Append("table tr:first-child td,firstTr td {border-bottom:1px solid black;}");
                    style.Append(".firstTr td{border-bottom:1px solid black;} table tr:first-child td{border-bottom:1px solid black;} ");
                    break;
                case 4://单虚线
                    //style.Append("table tr:first-child td ,firstTr td{border-bottom:1px dotted black;}");
                    style.Append(".firstTr td{border-bottom:1px dotted black;} table tr:first-child td {border-bottom:1px dotted black;}");
                    break;
                case 5://双实线
                    //style.Append("td{border-bottom:1px solid black;} table tr:last-child td,lastTr td{border-bottom:none;}");
                    //style.Append("td{border-bottom:1px solid black;} table tr:last-child td{border-bottom:none;}");
                    style.Append(".firstTr td {border-bottom:1px solid black;border-top:1px solid black;} tr:first-child td {border-bottom:1px solid black;border-top:1px solid black;}");
                    break;
                case 6://双虚线
                    //style.Append("td{border-bottom:1px dotted black;} table tr:last-child td,,lastTr td{border-bottom:none;}");
                    style.Append(".firstTr td {border-bottom:1px dotted black;border-top:1px dotted black;} tr:first-child td {border-bottom:1px dotted black;border-top:1px dotted black;}");
                    break;
                case 7://三实线
                    //style.Append("td{border-bottom:1px solid black;} table tr:first-child td,firstTr td{border-top:1px solid black;}");
                    style.Append(".firstTr td {border-bottom:1px solid black;border-top:1px solid black;} tr:first-child td {border-bottom:1px solid black;border-top:1px solid black;}");
                    style.Append(".lastTr td {border-top:1px solid black;} tr:last-child td {border-top:1px solid black;}");
                    break;
                case 8://三虚线
                    //style.Append("td{border-bottom:1px dotted black;} table tr:first-child td,firstTr td{border-top:1px dotted black;}");
                    style.Append(".firstTr td{border-bottom:1px dotted black;border-top:1px dotted black;} tr:first-child td {border-bottom:1px dotted black;border-top:1px dotted black;}");
                    style.Append(".lastTr td{border-top:1px dotted black;} tr:last-child td {border-top:1px dotted black;}");
                    break;
                case 9://实横线
                    style.Append("td{border-bottom:1px solid black;border-top:1px solid black;}");
                    break;
                case 10://虚横线
                    style.Append("td{border-bottom:1px dotted black;border-top:1px dotted black;}");
                    break;
            }
            style.Append(" .productPic div, .seqid div,.cargoNumber div,.product_cargoNumber div, .specInfo div,.quantity div,.orderId div,.singleQuantity div,.Price div,.onePrice div{text-align:center;} img{padding:2px;}");
            style.Append("</style>");
            return style.ToString();

        }

        /// <summary>
        /// 产品属性替换
        /// </summary>
        /// <param name="p"></param>
        /// <param name="model"></param>
        /// <param name="index"></param>
        /// <returns></returns>
        private static string CaculateProductValueByName(SendGoodTemplateProductModel p, SendGoodTemplateItemModel model, int index)
        {
            var value = "";
            switch (model.textId)
            {
                case "orderId":
                    var count = p.Skus != null && p.Skus.Any() ? p.Skus.Sum(s => s.Quantity) : p.Quantity;
                    value = count.ToString();
                    break;
                case "seqid":
                    value = index.ToString();
                    break;
                case "id":
                    value = "";
                    break;
                case "productPic":
                    value = p.ImageUrl;
                    break;
                case "productName":
                    value = p.PlatformTitleOrShortTiltle;
                    break;
                case "productallName":
                    value = p.PlatformTitle;
                    break;
                case "productshortName":
                    value = p.ShortTitle;
                    break;
                case "cargoNumber":
                    value = p.CargoNumber;
                    break;
                case "product_cargoNumber":
                    value = p.productCargoNumber;
                    break;
                case "specInfo":
                    value = p.SkuSpecific;
                    break;
                case "quantity":
                    value = p.Quantity.ToString();
                    break;
                case "onePrice":
                    value = p.Price.ToString("f2");
                    break;
                case "singleQuantity":
                    if (p.EntryDiscount > 0)
                        value = "+" + (p.EntryDiscount).ToString("f2");
                    else
                        value = (p.EntryDiscount).ToString("f2");
                    break;
                case "Price":
                    value = p.LittleTotal.ToString("f2");
                    break;
                case "extAttr4":
                    value = p.ExtAttr4;
                    break;
                default: break;
            }
            return ConvertSpecialCharsToHtml(value);
        }
        /// <summary>
        /// SKU属性替换
        /// </summary>
        /// <param name="p"></param>
        /// <param name="model"></param>
        /// <param name="index"></param>
        /// <returns></returns>
        private static string CaculateSkuValueByName(SendGoodTemplateProductSkuModel p, SendGoodTemplateItemModel model, int index)
        {
            var value = "";
            switch (model.textId)
            {
                case "seqid":
                    value = index.ToString();
                    break;
                case "cargoNumber":
                    value = p.CargoNumber;
                    break;
                case "product_cargoNumber":
                    value = p.productCargoNumber;
                    break;
                case "specInfo":
                    value = p.SkuSpecific;
                    break;
                case "quantity":
                    value = p.Quantity.ToString();
                    break;
                case "onePrice":
                    value = p.Price.ToString("f2");
                    break;
                case "singleQuantity":
                    value = (p.EntryDiscount).ToString("f2");
                    break;
                case "Price":
                    value = p.LittleTotal.ToString("f2");
                    break;
                case "sumPayment":
                    value = (p.Quantity * p.Price).ToString("f2");
                    break;
                case "extAttr4":
                    value = p.ExtAttr4;
                    break;
                default: break;
            }
            return value;
        }

        public OldSendGoodTemplate GetOldSendGoodTemplate()
        {
            var t = _repository.GetOldSendGoodTemplate(_shopId);
            if (t.ShopId == 0)
            {
                t.ShopId = _shopId;
                t.Id = 0;
            }
            return t;
        }

        public OldSendGoodTemplate SaveOldSendGoodTemplate(OldSendGoodTemplate model)
        {
            var templateShopId = model.ShopId;
            var allShopIds = SiteContext.Current.ShopIds;
            if (allShopIds.Contains(templateShopId) == false)
                throw new LogicException("未能查询到当前发货单模板信息，请返回重试");
            //Id和店铺ID必须得匹配的上
            if (model.Id > 0)
            {
                var old = GetOldSendGoodTemplate();
                if (old != null && old.ShopId != model.ShopId)
                    throw new LogicException("未能查询到当前发货单模板信息，请返回重试");
            }
            return _repository.SaveOldSendGoodTemplate(model);
        }

        /// <summary>
        /// 获取订单项的总重量
        /// </summary>
        /// <param name="ois"></param>
        /// <returns></returns>
        public decimal GetOrderItemTotalWeight(List<OrderItem> ois)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            if (ois == null || !ois.Any())
                return 0;
            var shopid = ois.FirstOrDefault().ShopId;
            var platformIds = ois.Select(oi => oi.ProductID).ToList();
            if (shopid <= 0 || platformIds == null || !platformIds.Any())
                return 0;
            var pwms = new List<ProductWeightModel>();
            if (fxUserId > 0)
                pwms = _productFxService.GetProductWeightInfo(fxUserId, platformIds);
            else
                pwms = _productService.GetProductWeightInfo(new List<int> { shopid }, platformIds);

            decimal weight = 0;
            if (pwms != null && pwms.Any())
            {
                ois.ForEach(oi =>
                {
                    var temp = pwms.OrderByDescending(p => p.SkuWeight + p.ProductWeight).FirstOrDefault(p => p.SkuId == oi.SkuID);
                    if (temp != null)
                    {
                        if (temp.SkuWeight > 0)
                            weight += (temp.SkuWeight / 1000 * (oi.Count ?? 0)); // 商品库重量g=>kg
                        else if (temp.ProductWeight > 0)
                            weight += (temp.ProductWeight / 1000 * (oi.Count ?? 0));
                    }
                });
            }
            return weight;
        }

        /// <summary>
        /// 将字符串中的特殊符号转换为HTML可以显示的内容
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private static string ConvertSpecialCharsToHtml(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;

            // 正则表达式检查是否含特殊符号
            // 如果不符合条件，则返回原字符串
            return Regex.IsMatch(input, @"[^\w\s<>]") ? HttpUtility.HtmlEncode(input) : input;
        }

        #region 老版发货单打印数据

        /// <summary>
        /// 准备发货单打印需要的数据
        /// </summary>
        /// <param name="config"></param>
        /// <param name="datas"></param>
        /// <param name="isCustomerOrder">是否是自由打印的订单</param>
        /// <returns></returns>
        public List<SendGoodTemplatePageModel> PrepareSendTemplatePrintDataOld(OldSendGoodTemplate model, List<SendGoodTemplateDataModel> datas, bool isCustomerOrder)
        {
            var curPlatformType = SiteContext.Current.CurrentLoginShop.PlatformType.ToString2();
            var buyerWangWang = curPlatformType == PlatformType.KuaiShou.ToString() ? "买家昵称" : "买家旺旺";
            var productAttr = curPlatformType == PlatformType.KuaiShou.ToString() || curPlatformType == PlatformType.YouZan.ToString() ? "商品规格" : "颜色/尺寸";
            var carboNumber = curPlatformType == PlatformType.KuaiShou.ToString() ? "SKU编码" : "商品货号";

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var shop = SiteContext.Current.CurrentLoginShop;
            //翻译判断
            int IsTranslated = model.IsTranslated;
            TkShipmentsInfoTranslateRecordService tkShipmentsInfoTranslateRecordService = new TkShipmentsInfoTranslateRecordService();

            //表格样式
            //if (model.PrintScope.ToInt() >= 100)
            //    model.PrintScope = "99";
            var pageStyle = " <style type='text/css'>.distance{padding-right:10%;}#mainTable{width:" + model.PrintScope + "%;align:center;font-size:" + model.FontSize + "pt;font-weight:" + (model.FontWeight == "0" ? "normal" : "bolder")
    + ";font-family:" + model.FontFamily + ";} ";
            if (model.LineType != null)
            {
                if (model.LineType == "2")
                    pageStyle += "table,td{ border: 1px solid Black;BORDER-COLLAPSE:COLLAPSE;word-break:break-all;}";
                else
                    pageStyle += "table,td{ border: 1px dashed Black;BORDER-COLLAPSE:COLLAPSE;word-break:break-all;}";
            }
            else
                pageStyle += "table,td{ border: 1px dashed Black;BORDER-COLLAPSE:COLLAPSE;word-break:break-all;}";
            pageStyle += "</style>";
            pageStyle += "<link href='/Content/css/printsend.css?var=18' type='text/css' rel='stylesheet' />";

            //表格内容
            var hsTemplate = (Hashtable)PluSoft.Utils.JSON.Decode(model.PrintContent);
            var hsTdWidth = (Hashtable)PluSoft.Utils.JSON.Decode(model.TdWidth);
            var pageList = new List<SendGoodTemplatePageModel>();
            ShopService shopService = new ShopService();
            datas.ForEach(d =>
            {
                Shop orderShop = new Shop();
                if (d.Order != null && d.Order.ShopId != 0)
                {
                    orderShop = shopService.Get(d.Order.ShopId);
                }
                var page = new SendGoodTemplatePageModel() { marginLeft = model.OffsetX.ToInt(), marginTop = model.OffsetY.ToInt(), paperHeight = model.Height, paperWidth = model.Width };
                //dynamic page = new { PageHeight = model.Height, PageWidth = model.Width };
                var or = d.Order?.OrderRequest;
                var ois = new List<OrderItem>();
                if (or.OrderItems != null && or.OrderItems.Any() && d.Order.OrderItems != null && d.Order.OrderItems.Any())
                    ois = d.Order?.OrderItems.Where(oi => or.OrderItems.Contains(oi.Id))?.ToList();
                var strcolspan = 0;
                if (hsTemplate["seqid"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["productPic"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["productName"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["productallName"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["productshortName"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["cargoNumber"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["product_cargoNumber"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["specInfo"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["quantity"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["singleQuantity"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["onePrice"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["discount"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["Price"]?.ToInt() == 1)
                    strcolspan++;
                if (hsTemplate["extAttr4"]?.ToInt() == 1)
                    strcolspan++;
                //发货单标题
                var TemplateNameHtml = "";
                if (!string.IsNullOrEmpty(model.TemplateName))
                    TemplateNameHtml = "<h2 class='deliverset_h2'>" + model.TemplateName + "</h2>";
                else
                    TemplateNameHtml = "<h2 class='deliverset_h2_Null'></h2>";
                var tableHtml = "<table align='center' style='width:" + model.PrintScope + "%;' class='dborder' id='mainTable'><tr><td colspan='" + strcolspan.ToString() + "'>" + TemplateNameHtml;
                tableHtml += "<ul class='d3'>";

                var tofullName = or?.Receiver.toFullName;
                var toMobile = string.IsNullOrEmpty(or.Receiver.toMobile) ? or.Receiver.toPhone : or.Receiver.toMobile;
                var toAddress = or.Receiver.toArea?.Replace("\t", "");
                if (or != null && or.PlatformType == PlatformType.Virtual.ToString())
                {
                    tofullName = tofullName.ToTaobaoEncryptName();
                    toMobile = toMobile.ToEncrytPhone();
                    toAddress = toAddress.ToTaoBaoEncryptAddress();
                }

                if (hsTemplate["sellerName"]?.ToInt() == 1)
                    tableHtml += "<li id='sellerName'>卖家姓名：" + or.Sender.SenderName + "</li>";
                if (hsTemplate["sellerPhone"]?.ToInt() == 1)
                    tableHtml += "<li id='sellerPhone'>卖家电话：" + or.Sender.SenderPhone + "</li>";
                if (hsTemplate["printTime"]?.ToInt() == 1)
                    tableHtml += "<li id='printTime'>打印日期：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "</li>";
                if (hsTemplate["toFullName"]?.ToInt() == 1)
                    tableHtml += "<li id='toFullName'>买家姓名：" + tofullName + "</li>"; //买家旺旺
                if (hsTemplate["tologinId"]?.ToInt() == 1)
                    tableHtml += "<li id='sellerName'>" + buyerWangWang + "：" + or.Buyer.BuyerWangWang + "</li>";
                if (hsTemplate["toMobile"]?.ToInt() == 1)
                    tableHtml += "<li id='toMobile'>买家电话：" + toMobile + "</li>";
                if (hsTemplate["gmtPayment"]?.ToInt() == 1)
                    tableHtml += "<li id='gmtPayment'>付款时间：" + d.Order.PayTime?.ToString("yyyy-MM-dd HH:mm:ss") + "</li>";
                tableHtml += "</ul>";
                if (hsTemplate["toArea"]?.ToInt() == 1)
                    tableHtml += "<ul class='d1' id='u2'><li id='toArea'>买家地址：" + toAddress + "</li></ul>";
                if (hsTemplate["id"]?.ToInt() == 1)
                    tableHtml += "<ul class='d1' id='u3'><li id='id' style='float:white-space:nowrap;'>订单编号：" + d.OrderNumber + "</li></ul>";
                tableHtml += "</td></tr>";

                //商品显示的列
                tableHtml += "<tr align='center'>";
                //if(hsTemplate!=null)
                //{
                //    foreach (var key in hsTemplate.Keys)
                //    {
                //        var k = key?.ToString();
                //        if(hsTemplate[k]?.ToInt()==1)
                //            tableHtml += $"<th id='{k}' align='center' width='" + hsTdWidth["[k_width"].ToString() + "%'>序号</th>";
                //    }
                //}
                //表头

                if (hsTemplate["seqid"]?.ToInt() == 1)
                    tableHtml += "<th id='seqid' align='center' width='" + (hsTdWidth.ContainsKey("seqid_width") ? hsTdWidth["seqid_width"].ToString() : "5") + "%'>序号</th>";
                if (hsTemplate["productPic"]?.ToInt() == 1)
                    tableHtml += "<th id='productPic' align='center' width='" + (hsTdWidth.ContainsKey("productPic_width") ? hsTdWidth["productPic_width"].ToString() : "10") + "%'>图片</th>";
                if (hsTemplate["productName"]?.ToInt() == 1)
                    tableHtml += "<th id='productName' align='center' width='" + (hsTdWidth.ContainsKey("productName_width") ? hsTdWidth["productName_width"].ToString() : "24") + "%'>名称</th>";
                if (hsTemplate["productallName"]?.ToInt() == 1)
                    tableHtml += "<th id='productallName' align='center' width='" + (hsTdWidth.ContainsKey("productName_width") ? hsTdWidth["productName_width"].ToString() : "24") + "%'>商品名称</th>";
                if (hsTemplate["productshortName"]?.ToInt() == 1)
                    tableHtml += "<th id='productshortName' align='center' width='" + (hsTdWidth.ContainsKey("productshortName_width") ? hsTdWidth["productshortName_width"].ToString() : "5") + "%'>商品简称</th>";
                if (hsTemplate["cargoNumber"]?.ToInt() == 1)
                    tableHtml += "<th id='cargoNumber' align='center' width='" + (hsTdWidth.ContainsKey("cargoNumber_width") ? hsTdWidth["cargoNumber_width"].ToString() : "15") + "%'>" + carboNumber + "</th>";
                if (hsTemplate["product_cargoNumber"]?.ToInt() == 1)
                    tableHtml += "<th id='product_cargoNumber' align='center' width='" + (hsTdWidth.ContainsKey("product_cargoNumber_width") ? hsTdWidth["product_cargoNumber_width"].ToString() : "15") + "%'>单品货号</th>";
                if (hsTemplate["specInfo"]?.ToInt() == 1)
                    tableHtml += "<th id='specInfo' align='center' width='" + (hsTdWidth.ContainsKey("specInfo_width") ? hsTdWidth["specInfo_width"].ToString() : "15") + "%'>" + productAttr + "</th>"; //商品规格 、 颜色/尺寸 
                if (hsTemplate["extAttr4"]?.ToInt() == 1)
                    tableHtml += "<th id='extAttr4' align='center' width='" + (hsTdWidth.ContainsKey("extAttr4_width") ? hsTdWidth["extAttr4_width"].ToString() : "15") + "%'>定制服务</th>";
                if (hsTemplate["quantity"]?.ToInt() == 1)
                    tableHtml += "<th id='quantity' align='center' width='" + (hsTdWidth.ContainsKey("quantity_width") ? hsTdWidth["quantity_width"].ToString() : "5") + "%'>数量</th>";
                if (hsTemplate["singleQuantity"]?.ToInt() == 1)
                    tableHtml += "<th id='singleQuantity' align='center' width='" + (hsTdWidth.ContainsKey("singleQuantity_width") ? hsTdWidth["singleQuantity_width"].ToString() : "10") + "%'>总数</th>";
                if (hsTemplate["onePrice"]?.ToInt() == 1)
                    tableHtml += "<th id='onePrice' align='center' width='" + (hsTdWidth.ContainsKey("onePrice_width") ? hsTdWidth["onePrice_width"].ToString() : "8") + "%'>单价</th>";
                if (hsTemplate["discount"]?.ToInt() == 1)
                    tableHtml += "<th id='discount' align='center' width='" + (hsTdWidth.ContainsKey("discount_width") ? hsTdWidth["discount_width"].ToString() : "8") + "%'>优惠</th>";
                if (hsTemplate["Price"]?.ToInt() == 1)
                    tableHtml += "<th id='Price' align='center' width='" + (hsTdWidth.ContainsKey("Price_width") ? hsTdWidth["Price_width"].ToString() : "8") + "%'>小计</th>";
                //排序字段
                var productNameSort = "productName";
                if (hsTemplate["productName"]?.ToInt() == 1)
                    productNameSort = "productName";
                if (hsTemplate["productallName"]?.ToInt() == 1)
                    productNameSort = "productallName";
                if (hsTemplate["productshortName"]?.ToInt() == 1)
                    productNameSort = "productshortName";

                //商品信息表
                DataTable dtProductInfo = new DataTable();
                dtProductInfo.Columns.Add("sourceId", Type.GetType("System.String"));
                dtProductInfo.Columns.Add("productName", Type.GetType("System.String"));
                dtProductInfo.Columns.Add("productallName", Type.GetType("System.String"));
                dtProductInfo.Columns.Add("productshortName", Type.GetType("System.String"));
                dtProductInfo.Columns.Add("productPic", Type.GetType("System.String"));
                dtProductInfo.Columns.Add("huoHao", Type.GetType("System.String"));
                dtProductInfo.Columns.Add("skuHuoHao", Type.GetType("System.String"));
                dtProductInfo.Columns.Add("discount", typeof(float));
                dtProductInfo.Columns.Add("specInfo", Type.GetType("System.String"));
                dtProductInfo.Columns.Add("isShowSalePrice", Type.GetType("System.String"));
                dtProductInfo.Columns.Add("extAttr4", Type.GetType("System.String"));

                //商品规格、数量表
                DataTable dtSpecInfo = new DataTable();
                dtSpecInfo.Columns.Add("productName", Type.GetType("System.String"));
                dtSpecInfo.Columns.Add("productallName", Type.GetType("System.String"));
                dtSpecInfo.Columns.Add("productshortName", Type.GetType("System.String"));
                dtSpecInfo.Columns.Add("sourceId", Type.GetType("System.String"));
                dtSpecInfo.Columns.Add("productPic", Type.GetType("System.String"));
                dtSpecInfo.Columns.Add("huoHao", Type.GetType("System.String"));
                dtSpecInfo.Columns.Add("skuHuoHao", Type.GetType("System.String"));
                dtSpecInfo.Columns.Add("specId", Type.GetType("System.String"));
                dtSpecInfo.Columns.Add("specInfo", Type.GetType("System.String"));
                dtSpecInfo.Columns.Add("price", typeof(float));
                dtSpecInfo.Columns.Add("quantity", Type.GetType("System.Int32"));
                dtSpecInfo.Columns.Add("subtotal", typeof(float));
                dtSpecInfo.Columns.Add("payment", typeof(float));
                dtSpecInfo.Columns.Add("discount", typeof(float));
                dtSpecInfo.Columns.Add("isShowSalePrice", Type.GetType("System.String"));
                dtSpecInfo.Columns.Add("extAttr4", Type.GetType("System.String"));
                DataView dvProductInfo = dtProductInfo.DefaultView;
                DataView dvSpecInfo = dtSpecInfo.DefaultView;
                //TODO:生成商品表格：排序、合并商品
                ois.ForEach(oe =>
                {
                    var product = d.Products.FirstOrDefault(x => x.PlatformId == oe.ProductID && x.SkuId == oe.SkuID);
                    if (product != null)
                        oe.ShortTitle = product.ShortTitle;
                    if (isCustomerOrder && string.IsNullOrEmpty(oe.SpecId))
                        oe.SpecId = oe.SkuID;
                    dvProductInfo.RowFilter = "sourceId='" + (oe.ProductID ?? oe.Id.ToString()) + "'";
                    var dtTmp1 = dvProductInfo.ToTable();
                    var specInfo = $"{oe.Color} {oe.Size} {oe.ExtAttr1} {oe.ExtAttr2}";
                    if (d.Order.PlatformType == PlatformType.Alibaba.ToString())
                        specInfo = $"{oe.Color} {oe.Size}";
                    else if (d.Order.PlatformType == PlatformType.YouZan.ToString())
                        specInfo = $"{oe.ExtAttr1} {oe.ExtAttr2}";
                    else if (d.Order.PlatformType == PlatformType.TikTok.ToString())
                        specInfo = $"{oe.Color} {oe.Size}";

                    var skuHuoHao = oe.productCargoNumber;
                    var huoHao = string.IsNullOrEmpty(oe.CargoNumber) ? oe.productCargoNumber : oe.CargoNumber;
                    if (d.Order.PlatformType == PlatformType.YouZan.ToString())
                    {
                        huoHao = string.IsNullOrEmpty(oe.productCargoNumber) ? oe.CargoNumber : oe.productCargoNumber;
                        skuHuoHao = oe.CargoNumber;
                    }
                    if (dtTmp1.Rows.Count == 0)
                    {
                        DataRow drTmp1 = dtProductInfo.NewRow();
                        drTmp1["sourceId"] = oe.ProductID ?? oe.Id.ToString();
                        drTmp1["productName"] = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, oe.ProductSubject, fxUserId, orderShop.Id);
                        drTmp1["productallName"] = string.IsNullOrEmpty(oe.ShortTitle) ? tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, oe.ProductSubject, fxUserId, orderShop.Id) : tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, oe.ShortTitle, fxUserId, orderShop.Id);
                        drTmp1["productshortName"] = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, oe.ShortTitle, fxUserId, orderShop.Id);//TODO:从商品中读取
                        drTmp1["productPic"] = oe.ProductImgUrl ?? "";
                        drTmp1["huoHao"] = "";
                        drTmp1["skuHuoHao"] = "";
                        if (hsTemplate["cargoNumber"]?.ToInt() == 1)
                            drTmp1["huoHao"] = huoHao;
                        if (hsTemplate["product_cargoNumber"]?.ToInt() == 1)
                            drTmp1["skuHuoHao"] = skuHuoHao;
                        drTmp1["specInfo"] = specInfo;
                        drTmp1["isShowSalePrice"] = (fxUserId <= 0 || d.Order.IsShowSalePrice) ? "1" : "0";
                        drTmp1["extAttr4"] = oe.ExtAttr4;
                        dtProductInfo.Rows.Add(drTmp1);
                        DataRow drTmp2 = dtSpecInfo.NewRow();
                        drTmp2["productallName"] = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, oe.ProductSubject, fxUserId, orderShop.Id);
                        drTmp2["productName"] = string.IsNullOrEmpty(oe.ShortTitle) ? tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, oe.ProductSubject, fxUserId, orderShop.Id) : tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, oe.ShortTitle, fxUserId, orderShop.Id);
                        drTmp2["productshortName"] = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, oe.ShortTitle, fxUserId, orderShop.Id);
                        drTmp2["sourceId"] = oe.ProductID ?? oe.Id.ToString();
                        drTmp2["productPic"] = oe.ProductImgUrl ?? "";
                        drTmp2["huoHao"] = "";
                        if (hsTemplate["cargoNumber"]?.ToInt() == 1)
                            drTmp2["huoHao"] = huoHao;
                        if (hsTemplate["product_cargoNumber"]?.ToInt() == 1)
                            drTmp2["skuHuoHao"] = skuHuoHao;
                        drTmp2["discount"] = (oe.ItemAmount - (oe.Count * oe.Price)).ToFloat();
                        drTmp2["specId"] = oe.SpecId;
                        drTmp2["specInfo"] = specInfo;
                        drTmp2["price"] = oe.Price ?? 0;
                        drTmp2["quantity"] = oe.Count ?? 0;
                        drTmp2["payment"] = oe.ItemAmount ?? 0;
                        drTmp2["subtotal"] = (oe.Count * oe.Price) ?? 0;
                        drTmp2["isShowSalePrice"] = (fxUserId <= 0 || d.Order.IsShowSalePrice) ? "1" : "0";
                        drTmp2["extAttr4"] = oe.ExtAttr4;
                        dtSpecInfo.Rows.Add(drTmp2);
                    }
                    else
                    {
                        string specInfoSql = "sourceId='" + (oe.ProductID ?? oe.Id.ToString()) + "'";
                        if (oe.SpecId != null)
                            specInfoSql = specInfoSql + " and specId='" + oe.SpecId + "'";

                        DataRow[] drTmp3 = dtSpecInfo.Select(specInfoSql);
                        var curDiscount = (oe.ItemAmount - (oe.Count * oe.Price)).ToFloat();
                        if (drTmp3.Length > 0 && model.TitleMerge == "2")
                        {
                            drTmp3[0]["discount"] = curDiscount + drTmp3[0]["discount"].ToFloat();
                            drTmp3[0]["quantity"] = (oe.Count + int.Parse(drTmp3[0]["quantity"].ToString())) ?? 0;
                            drTmp3[0]["payment"] = float.Parse(drTmp3[0]["payment"].ToString()) + oe.ItemAmount.ToFloat();
                            var subtotal = (oe.Price * oe.Count)?.ToFloat() + float.Parse(drTmp3[0]["subtotal"].ToString());
                            drTmp3[0]["subtotal"] = subtotal ?? 0;
                            drTmp3[0]["isShowSalePrice"] = (fxUserId <= 0 || d.Order.IsShowSalePrice) ? "1" : "0";
                            drTmp3[0]["extAttr4"] = oe.ExtAttr4;
                        }
                        else
                        {
                            DataRow drTmp2 = dtSpecInfo.NewRow();
                            drTmp2["productPic"] = oe.ProductImgUrl;
                            drTmp2["huoHao"] = "";
                            if (hsTemplate["cargoNumber"]?.ToInt() == 1)
                                drTmp2["huoHao"] = huoHao;
                            if (hsTemplate["product_cargoNumber"]?.ToInt() == 1)
                                drTmp2["skuHuoHao"] = skuHuoHao;
                            drTmp2["productallName"] = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, oe.ProductSubject, fxUserId, orderShop.Id);
                            drTmp2["productName"] = string.IsNullOrEmpty(oe.ShortTitle) ? tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, oe.ProductSubject, fxUserId, orderShop.Id) : tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, oe.ShortTitle, fxUserId, orderShop.Id);
                            drTmp2["productshortName"] = tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, oe.ShortTitle, fxUserId, orderShop.Id);
                            drTmp2["sourceId"] = oe.ProductID ?? oe.Id.ToString();
                            drTmp2["discount"] = curDiscount;
                            drTmp2["specId"] = oe.SpecId;
                            drTmp2["specInfo"] = specInfo;
                            drTmp2["price"] = oe.Price ?? 0;
                            drTmp2["quantity"] = oe.Count ?? 0;
                            drTmp2["payment"] = oe.ItemAmount ?? 0;
                            drTmp2["subtotal"] = (oe.Count * oe.Price) ?? 0;
                            drTmp2["isShowSalePrice"] = (fxUserId <= 0 || d.Order.IsShowSalePrice) ? "1" : "0";
                            drTmp2["extAttr4"] = oe.ExtAttr4;
                            dtSpecInfo.Rows.Add(drTmp2);
                        }
                    }
                });

                var productHtml = "";
                float productCount = 0;
                var rowCount = 1;
                if (model.TitleMerge == "1")
                {
                    string sort1 = "";
                    if (model.OfferSort == "3")
                        sort1 = productNameSort + " Asc";

                    if (model.OfferSort == "5")
                        sort1 = productNameSort + " Asc,huoHao Asc";

                    if (model.OfferSort == "4")
                        sort1 = "huoHao Asc," + productNameSort + " Asc";

                    if (model.OfferSort == "2")
                        sort1 = "huoHao Asc";

                    if (model.OfferSort == "6")
                        sort1 = "productshortName Asc";

                    if (model.OfferSort == "7")
                        sort1 = "specInfo Asc";

                    if (model.SkuSort == "2")
                    {
                        if (sort1 != "")
                            sort1 = sort1 + ",specInfo Asc";
                        else
                            sort1 = "specInfo Asc";
                    }
                    //按货号或颜色规格排序
                    if (sort1 != "")
                    {
                        DataView dv = dtSpecInfo.DefaultView;
                        dv.RowFilter = "1=1";
                        dv.Sort = sort1;
                        dtSpecInfo = dv.ToTable();
                    }
                    productHtml = BuildProductTableHtmlNormal(ref productCount, ref rowCount, hsTemplate, dtSpecInfo, model);
                }
                else
                {
                    DataView dvProductSort = dtProductInfo.DefaultView;

                    //按货号排序
                    if (model.OfferSort == "2")
                    {
                        dvProductSort.RowFilter = "1=1";
                        dvProductSort.Sort = "huoHao Asc";
                        dtProductInfo = dvProductSort.ToTable();
                    }

                    //按标题排序
                    if (model.OfferSort == "3")
                    {
                        dvProductSort.RowFilter = "1=1";
                        dvProductSort.Sort = productNameSort + " Asc";
                        dtProductInfo = dvProductSort.ToTable();
                    }

                    //按货号标题
                    if (model.OfferSort == "4")
                    {
                        dvProductSort.RowFilter = "1=1";
                        dvProductSort.Sort = "huoHao," + productNameSort + " Asc";
                        dtProductInfo = dvProductSort.ToTable();
                    }

                    //按标题货号
                    if (model.OfferSort == "5")
                    {
                        dvProductSort.RowFilter = "1=1";
                        dvProductSort.Sort = productNameSort + ",huoHao Asc";
                        dtProductInfo = dvProductSort.ToTable();
                    }

                    //按简称排序
                    if (model.OfferSort == "6")
                    {
                        dvProductSort.RowFilter = "1=1";
                        dvProductSort.Sort = "productshortName Asc";
                        dtProductInfo = dvProductSort.ToTable();
                    }

                    //按简称排序
                    if (model.OfferSort == "7")
                    {
                        dvProductSort.RowFilter = "1=1";
                        dvProductSort.Sort = "specInfo Asc";
                        dtProductInfo = dvProductSort.ToTable();
                    }

                    foreach (DataRow dr1 in dtProductInfo.Rows)
                    {
                        DataView dv1 = dtSpecInfo.DefaultView;
                        dv1.RowFilter = "sourceId='" + dr1["sourceId"].ToString() + "'";
                        string sort2 = "";
                        if (model.OfferSort == "2")
                            sort2 = "huoHao Asc";
                        else if (model.OfferSort == "7")
                            sort2 = "specInfo Asc";
                        if (model.SkuSort == "2" && model.OfferSort != "7")
                        {
                            if (sort2 != "")
                                sort2 = sort2 + ",specInfo Asc";
                            else
                                sort2 = "specInfo Asc";
                        }

                        if (sort2 != "")
                            dv1.Sort = sort2;

                        DataTable dtTmpSI = dv1.ToTable();

                        if (model.TitleMerge == "2")
                            tableHtml += BuildProductTableHtmlMergered(ref productCount, ref rowCount, hsTemplate, dtTmpSI, model);
                    }
                }
                tableHtml += productHtml;
                //所有商品总计
                tableHtml += "<tr><td colspan='" + strcolspan.ToString() + "' align='right'>";
                if (hsTemplate["sumPayment"]?.ToInt() == 1)
                {
                    tableHtml += "<div id='sumPayment'>";
                    if (fxUserId <= 0 || d.Order.IsShowSalePrice)
                    {
                        if (d.TotalDiscount != 0)
                        {
                            var totalDisCount = Math.Abs(d.TotalDiscount).ToString("f2");
                            if (d.TotalDiscount > 0)
                                tableHtml += "活动优惠：-￥" + totalDisCount + "元&nbsp;";
                            else
                                tableHtml += "活动优惠：+￥" + totalDisCount + "元&nbsp;";
                        }
                        tableHtml += "总价：￥" + d.SumPayment.ToString("f2") + " 元（含运费"
                            + d.CarriageFee.ToString("f2") + " 元），";
                    }

                    tableHtml += "总共 " + d.ProductTotalCount + " 件&nbsp;&nbsp;</div>";
                }
                tableHtml += "</td></tr>";
                //留言备注
                tableHtml += "<tr><td colspan='" + strcolspan.ToString() + "' style='padding-top: 5px;line-height:20px;word-break:break-all;'>";
                if (hsTemplate["buyerFeedBack"]?.ToInt() == 1)
                    tableHtml += $"买家留言：{tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, d.BuyerMessage, fxUserId, orderShop.Id)}</br>";
                if (hsTemplate["memo"]?.ToInt() == 1)
                    tableHtml += $"卖家备注：{tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, d.SellerRemark, fxUserId, orderShop.Id)}</br>";
                if (hsTemplate["tips"]?.ToInt() == 1)
                    tableHtml += $"温馨提示：{tkShipmentsInfoTranslateRecordService.GetLanguageTextByTranslatingUsingMachine(IsTranslated, model.Tips, fxUserId, orderShop.Id)}</br>";
                tableHtml += "</td></tr>";
                tableHtml += "<tr><td colspan='" + strcolspan.ToString() + "' style='padding-top: 5px;width:100%;'>";
                tableHtml += "<span class='distance'>发货人：&#12288;&#12288;&#12288;</span><span class='distance'>核对人：&#12288;&#12288;&#12288;</span><span class='distance'>应发件数：" + d.ProductTotalCount + "</span><span class='distance'>&#12288;实发件数：</span>";
                tableHtml += "</td></tr>";
                tableHtml += "</table>";

                ///跨境替换货币字符
                if (CustomerConfig.IsCrossBorderSite)
                {
                    var currStr = GetCurrencyStr(d.Order.Currency);
                    if (!string.IsNullOrEmpty(currStr))
                    {
                        tableHtml = tableHtml.Replace("元", "").Replace("￥", currStr);
                    }
                }

                page.Commands = new List<SendGoodTemplateCommandModel> {
                    new SendGoodTemplateCommandModel {
                        Content=$"{pageStyle}<body>{tableHtml}</body>",
                        ContentType = "oldtable"
                    }
                };
                page.LogicOrderId = d.SinglePlatformOrderId;
                pageList.Add(page);
            });

            return pageList;
        }

        /*public MemoryStream OldGeneratePdfFromJson(List<SendGoodTemplatePageModel> printData)
        {
            // 定义输出 PDF 的流
            MemoryStream outputStream = new MemoryStream();

            // iTextSharp 使用 points，1 mm ~ 2.83465 points
            float mmToPoints = 2.83465f;

            // 创建 iTextSharp 文档（默认初始页面大小是 A4，这里先不指定）
            Document doc = new Document(PageSize.A4, 0, 0, 0, 0);

            // 创建 PdfWriter，把文档内容写入 outputStream
            PdfWriter writer = PdfWriter.GetInstance(doc, outputStream);

            doc.Open();

            // 遍历每个 "pageData" 相当于 LODOP 里的每个 Page
            foreach (var pageData in printData)
            {
                // 在 iTextSharp 里，如果不是第一个页面，需要手动 NewPage
                if (doc.PageSize != null && doc.PageNumber > 1)
                {
                    doc.NewPage();
                }

                // 自定义页面大小 (iTextSharp 在切换页面大小时，可以使用 doc.SetPageSize)
                float pageWidth = (float)(Convert.ToDouble(pageData.paperWidth) * mmToPoints);
                float pageHeight = (float)(Convert.ToDouble(pageData.paperHeight) * mmToPoints);
                doc.SetPageSize(new iTextSharp.text.Rectangle(pageWidth, pageHeight));
                doc.NewPage();

                // 获取 PdfContentByte，用于绝对定位
                PdfContentByte cb = writer.DirectContent;

                // 遍历 Commands
                foreach (var cmd in pageData.Commands)
                {
                    if (cmd.Content == null)
                    {
                        continue;
                    }
                    if (cmd.ContentType == "oldtable" || cmd.ContentType == "table")
                    {
                        string rawHtml = cmd.Content;

                        // 解析 HTML
                        HtmlDocument docHtml = new HtmlDocument();
                        docHtml.LoadHtml(rawHtml);

                        // **1. 修正 colspan=0**
                        foreach (var td in docHtml.DocumentNode.SelectNodes("//td[@colspan='0']") ?? new HtmlNodeCollection(null))
                        {
                            td.SetAttributeValue("colspan", "1"); // 改为 colspan=1
                        }

                        // **2. 确保 <html>、<head>、<body> 存在**
                        if (docHtml.DocumentNode.SelectSingleNode("//html") == null)
                        {
                            rawHtml = "<!DOCTYPE html><html><head><meta charset='utf-8'><style>" +
                                      "table { border-collapse: collapse; width: 100%; }" +
                                      "td, th { border: 1px solid black; padding: 5px; }" +
                                      ".distance { padding-right: 10%; }" +
                                      "</style></head><body>" + rawHtml + "</body></html>";
                        }

                        // **3. 移除 <link>，避免 CSS 解析问题**
                        foreach (var link in docHtml.DocumentNode.SelectNodes("//link") ?? new HtmlNodeCollection(null))
                        {
                            link.Remove();
                        }

                        // **1. 确保所有 <img> 标签是自闭合的**
                        // 先处理 img 标签，确保它们是自闭合的
                        /*foreach (var img in docHtml.DocumentNode.SelectNodes("//img") ?? new HtmlNodeCollection(null))
                        {
                            // 获取当前 img 标签
                            var imgTag = img;

                            // 如果 img 标签没有自闭合标记，即 ">" 结尾
                            if (!imgTag.OuterHtml.EndsWith("/>"))
                            {
                                // 获取当前 img 标签的原始 HTML 内容，并将 ">" 替换成 "/>"
                                var newOuterHtml = imgTag.OuterHtml.TrimEnd('>') + " />";

                                // 使用 SetAttributeValue 或其他方式替换
                                imgTag.ParentNode.ReplaceChild(HtmlNode.CreateNode(newOuterHtml), imgTag);
                            }
                        }

                        // **2. 去除 div 包裹 img 的标签结构**
                        var divsToReplace = new List<HtmlNode>(); // 用于保存需要替换的 div 标签
                        foreach (var div in docHtml.DocumentNode.SelectNodes("//div") ?? new HtmlNodeCollection(null))
                        {
                            // 如果 div 只包含 img，去掉 div 标签
                            if (div.Descendants("img").Any() && div.ChildNodes.Count == 1 && div.FirstChild.Name == "img")
                            {
                                divsToReplace.Add(div); // 收集 div
                            }
                        }

                        // 替换 div 标签
                        foreach (var div in divsToReplace)
                        {
                            var imgTag = div.Descendants("img").FirstOrDefault();
                            if (imgTag != null)
                            {
                                div.ParentNode.ReplaceChild(imgTag, div); // 替换 div 为 img 标签
                            }
                        }

                        // **3. 确保所有 <td> 标签内没有不合规的标签嵌套**
                        var divsToReplaceInTd = new List<HtmlNode>(); // 用于保存 td 中需要替换的 div 标签
                        foreach (var td in docHtml.DocumentNode.SelectNodes("//td") ?? new HtmlNodeCollection(null))
                        {
                            // 检查 td 标签下是否有 div 标签
                            foreach (var div in td.Descendants("div") ?? new HtmlNodeCollection(null))
                            {
                                divsToReplaceInTd.Add(div); // 收集 td 内的 div
                            }
                        }

                        // 替换 td 中的 div 标签
                        foreach (var div in divsToReplaceInTd)
                        {
                            // 获取 div 的内容
                            var innerHtml = div.InnerHtml;

                            // 创建一个新的文本节点（或者使用 HtmlNode 来封装该内容）
                            var newNode = HtmlNode.CreateNode(innerHtml);

                            // 替换 div 标签为新创建的节点
                            div.ParentNode.ReplaceChild(newNode, div);
                        }

                        foreach (var td in docHtml.DocumentNode.SelectNodes("//td") ?? new HtmlNodeCollection(null))
                        {
                            // 收集需要处理的 img 标签
                            var imgsToProcess = td.Descendants("img").ToList();

                            foreach (var img in imgsToProcess)
                            {
                                // 获取当前 img 标签
                                var imgTag = img;

                                // 如果 img 是直接嵌套在 td 中，确保没有不合规的标签包裹它
                                if (img.ParentNode.Name != "td")
                                {
                                    // 如果有不合规的父标签，将 img 标签移到 td 中
                                    img.ParentNode.ReplaceChild(img, img.ParentNode);
                                }
                            }
                        }


                        // 获取修正后的 HTML
                        string cleanedHtml = docHtml.DocumentNode.WriteTo();
                        string updatedHtml = Regex.Replace(cleanedHtml, @"<img(?![^>]*\/>)", "<img$0/>");

                        using (var srHtml = new StringReader(updatedHtml))
                        {
                            // 添加 CSS 样式
                            CssFilesImpl cssFiles = new CssFilesImpl();
                            StyleAttrCSSResolver cssResolver = new StyleAttrCSSResolver(cssFiles);
                            cssResolver.AddCss("table { border-collapse: collapse; width: 100%; } td, th { border: 1px solid black; padding: 5px; }", true);

                            // 创建 HTML 管道
                            HtmlPipelineContext htmlContext = new HtmlPipelineContext(null);
                            htmlContext.SetTagFactory(Tags.GetHtmlTagProcessorFactory());

                            // 写入 PDF
                            PdfWriterPipeline pdf = new PdfWriterPipeline(doc, writer);
                            HtmlPipeline html = new HtmlPipeline(htmlContext, pdf);
                            CssResolverPipeline css = new CssResolverPipeline(cssResolver, html);

                            // 解析并渲染 HTML 内容
                            XMLWorkerHelper.GetInstance().ParseXHtml(writer, doc, srHtml);
                        }
                    }
                    else if (cmd.ContentType == "text")
                    {
                        // 绝对定位文本
                        float x = cmd.Left;
                        float y = pageHeight - cmd.Top;

                        // 创建文本
                        BaseFont baseFont = BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, false);
                        float fontSize = 12f;
                        if (!string.IsNullOrEmpty(cmd.FontSize))
                        {
                            float.TryParse(cmd.FontSize.Replace("pt", ""), out fontSize);
                        }

                        cb.BeginText();
                        cb.SetFontAndSize(baseFont, fontSize);
                        cb.SetTextMatrix(x, y);
                        cb.ShowText(cmd.Content);
                        cb.EndText();
                    }
                    else if (cmd.ContentType == "qrcode" || cmd.ContentType == "barcode")
                    {
                        // 生成条码/二维码 (ZXing)
                        BarcodeWriter barcodeWriter = new BarcodeWriter
                        {
                            Format = cmd.ContentType == "qrcode" ? BarcodeFormat.QR_CODE : BarcodeFormat.CODE_128,
                            Options = new EncodingOptions
                            {
                                Width = (int)cmd.Width,
                                Height = (int)cmd.Height,
                                Margin = 0
                            },
                            Renderer = new BitmapRenderer()
                        };

                        using (Bitmap bitmap = barcodeWriter.Write(cmd.Content))
                        using (MemoryStream barcodeStream = new MemoryStream())
                        {
                            bitmap.Save(barcodeStream, ImageFormat.Png);
                            byte[] barcodeBytes = barcodeStream.ToArray();

                            // 在 iTextSharp 中插入图像 (绝对定位)
                            iTextSharp.text.Image img = iTextSharp.text.Image.GetInstance(barcodeBytes);
                            float x = cmd.Left;
                            float y = pageHeight - cmd.Top - cmd.Height; // 左下角
                            img.SetAbsolutePosition(x, y);
                            img.ScaleAbsolute(cmd.Width, cmd.Height);

                            doc.Add(img);
                        }
                    }
                    else if (cmd.ContentType == "image")
                    {
                        // 处理 Base64 图片
                        byte[] imageBytes = Convert.FromBase64String(cmd.Content);
                        iTextSharp.text.Image img = iTextSharp.text.Image.GetInstance(imageBytes);

                        float x = cmd.Left;
                        float y = pageHeight - cmd.Top - cmd.Height;
                        img.SetAbsolutePosition(x, y);
                        img.ScaleAbsolute(cmd.Width, cmd.Height);

                        doc.Add(img);
                    }
                }
            }

            // 关闭文档
            doc.Close();

            // 写入到指定文件
            string outputPath = @"C:\workspace\Xinban\DianGuanJiaApp.ErpWeb\output.pdf";
            File.WriteAllBytes(outputPath, outputStream.ToArray());

            // 重置流位置
            return outputStream;
        }*/


        /*public void GeneratePdfPdfSharp(List<SendGoodTemplatePageModel> printData)
        {
            PdfDocument document = new PdfDocument();
            XGraphics gfx;
            XFont font;

            foreach (var page in printData)
            {
                // 创建一个新页面
                PdfPage pdfPage = document.AddPage();
                pdfPage.Width = XUnit.FromMillimeter(Convert.ToDouble(page.paperWidth));
                pdfPage.Height = XUnit.FromMillimeter(Convert.ToDouble(page.paperHeight));
                gfx = XGraphics.FromPdfPage(pdfPage);

                // 遍历当前页面的命令列表
                foreach (var cmd in page.Commands)
                {
                    if (cmd.Content == null)
                    {
                        continue;
                    }
                    double top = cmd.IsAbsolute ? cmd.Top : cmd.RelativeTop + page.marginTop;
                    double left = cmd.IsAbsolute ? cmd.Left : cmd.RelativeLeft + page.marginLeft;
                    double width = cmd.Width;
                    double height = cmd.Height;

                    if (cmd.ContentType == "qrcode" && !string.IsNullOrEmpty(cmd.Content))
                    {
                        // 生成二维码
                        QRCodeGenerator qrGenerator = new QRCodeGenerator();
                        QRCodeData qrCodeData = qrGenerator.CreateQrCode(cmd.Content, QRCodeGenerator.ECCLevel.Q);
                        QRCode qrCode = new QRCode(qrCodeData);

                        // 将二维码图像转换为Bitmap
                        using (Bitmap qrBitmap = qrCode.GetGraphic(20))  // 20是二维码的大小
                        {
                            // 保存Bitmap为临时文件
                            string tempFilePath = Path.Combine(Path.GetTempPath(), "qrTempImage.png");
                            qrBitmap.Save(tempFilePath, System.Drawing.Imaging.ImageFormat.Png);

                            // 加载保存的PNG文件作为XImage
                            XImage xImage = XImage.FromFile(tempFilePath);

                            // 绘制二维码到PDF
                            gfx.DrawImage(xImage, left, top, width, height);

                            // 删除临时文件
                            File.Delete(tempFilePath);
                        }
                    }
                    else if (cmd.ContentType == "barcode" && !string.IsNullOrEmpty(cmd.Content))
                    {
                        // 创建条形码生成器
                        BarcodeWriter barcodeWriter = new BarcodeWriter
                        {
                            Format = BarcodeFormat.CODE_128,  // 设置条形码类型（可以根据需要修改）
                            Options = new ZXing.Common.EncodingOptions
                            {
                                Width = (int)width,  // 设置条形码的宽度
                                Height = (int)height  // 设置条形码的高度
                            }
                        };

                        // 生成条形码的 Bitmap 图像
                        using (Bitmap barcodeBitmap = barcodeWriter.Write(cmd.Content))
                        {
                            // 将 Bitmap 转换为 XImage（PdfSharp 不直接支持 Bitmap）
                            using (MemoryStream ms = new MemoryStream())
                            {
                                barcodeBitmap.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
                                XImage barcodeXImage = XImage.FromStream(ms);

                                // 绘制条形码到 PDF
                                gfx.DrawImage(barcodeXImage, left, top, width, height);
                            }
                        }
                    }
                    else if (cmd.ContentType == "image" && !string.IsNullOrEmpty(cmd.Content))
                    {
                        // 绘制图片
                        var img = XImage.FromFile(cmd.Content); // 读取图像文件
                        gfx.DrawImage(img, left, top, width, height);
                    }
                    else if (cmd.ContentType == "text")
                    {
                        font = new XFont("SimSun", Convert.ToDouble(cmd.FontSize.Replace("pt", "")),
                                         cmd.FontWeight == "bold" ? XFontStyle.Bold : XFontStyle.Regular);
                        gfx.DrawString(cmd.Content, font, XBrushes.Black, new XPoint(left, top));
                    }
                    else if (cmd.ContentType == "table" || cmd.ContentType == "oldtable")
                    {
                        // 绘制表格 (示例简化处理，只绘制框架)
                        DrawTable(gfx, left, top, width, height, cmd.Content);
                    }
                }
            }

            // 保存PDF文件
            document.Save(@"C:\workspace\Xinban\DianGuanJiaApp.ErpWeb\output.pdf");
        }

        // 查找字体的文件路径
        public static string GetFontFilePath(string fontFamilyName)
        {
            // 1. 系统字体文件夹路径
            string[] fontDirectories = new string[]
            {
            @"C:\Windows\Fonts",  // Windows 系统字体文件夹
            @"C:\Program Files\Common Files\Microsoft Shared\Fonts",  // 其他可能的字体目录（可以根据需要扩展）
            @"C:\Windows\WinSxS" // 一些字体可能存放在系统的 WinSxS 目录下
            };

            // 2. 在指定目录中查找字体文件
            foreach (var dir in fontDirectories)
            {
                if (Directory.Exists(dir))
                {
                    var fontFiles = Directory.GetFiles(dir, $"{fontFamilyName}.ttf", SearchOption.AllDirectories); // 查找所有的 .ttf 文件
                    if (fontFiles.Length > 0)
                    {
                        return fontFiles[0]; // 返回找到的第一个字体文件路径
                    }
                }
            }

            return null; // 如果找不到字体文件，返回 null
        }

        private void DrawTable(XGraphics gfx, double left, double top, double width, double height, string tableContent)
        {
            // 解析HTML
            HtmlDocument doc = new HtmlDocument();
            doc.LoadHtml(tableContent);

            var table = doc.DocumentNode.SelectSingleNode("//table");
            var rows = table.SelectNodes("tr");
            double rowHeight = 20;
            int currentRow = 0;

            foreach (var row in rows)
            {
                var cells = row.SelectNodes("td");
                double currentCellX = left;

                foreach (var cell in cells)
                {
                    // 绘制单元格
                    gfx.DrawRectangle(XPens.Black, currentCellX, top + currentRow * rowHeight, width / cells.Count, rowHeight);

                    // 绘制单元格内容
                    gfx.DrawString(cell.InnerText, new XFont("宋体", 12, XFontStyle.Regular), XBrushes.Black, new XPoint(currentCellX + 2, top + currentRow * rowHeight + 2));

                    // 移动到下一个单元格
                    currentCellX += width / cells.Count;
                }
                currentRow++;
            }
        }*/


        private string BuildProductTableHtmlMergered(ref float productCount, ref int RowCount, Hashtable hsTemplate, DataTable dtTmpSI, OldSendGoodTemplate model)
        {
            int singleCount = 0; //统计单款数量小计
            string styletd = "";
            string pageList = "";
            string strProductName = "";
            string strProductallName = "";
            string strProductshortName = "";

            float rounzj_db = (float)dtTmpSI.Rows.Count / 2;
            string roundzhongj = Convert.ToString(Math.Round(rounzj_db + 0.5));
            if (roundzhongj == "0")
                roundzhongj = "1";

            //统计单款数量小计和货号是否一样
            for (int i = 0; i < dtTmpSI.Rows.Count; i++)
            {
                singleCount += int.Parse(dtTmpSI.Rows[i]["quantity"].ToString());
            }

            for (int i = 0; i < dtTmpSI.Rows.Count; i++)
            {
                if (hsTemplate["productName"]?.ToInt() == 1)
                {
                    strProductName = dtTmpSI.Rows[i]["productName"].ToString();
                }
                if (hsTemplate["productallName"]?.ToInt() == 1)
                {
                    strProductallName = dtTmpSI.Rows[i]["productallName"].ToString();
                }
                if (hsTemplate["productshortName"]?.ToInt() == 1)
                {
                    strProductshortName = dtTmpSI.Rows[i]["productshortName"].ToString();
                }
                productCount += int.Parse(dtTmpSI.Rows[i]["quantity"].ToString());
                styletd = "style='border-top-style:none;border-bottom-style:none;'";
                if (dtTmpSI.Rows.Count <= 1)
                {
                    styletd = "";
                }
                else
                {
                    if (i == 0)
                        styletd = "style='border-bottom-style:none;'";

                    if ((i + 1) == dtTmpSI.Rows.Count)
                        styletd = "style='border-top-style:none;'";
                }

                if (model.TrHeight.ToInt() <= 15)
                    model.TrHeight = "15";
                pageList += "<tr align='center' style='line-height:" + model.TrHeight + "px;'>";
                if (hsTemplate["seqid"]?.ToInt() == 1)
                {
                    if (int.Parse(roundzhongj) == i + 1)
                        pageList += "<td " + styletd + ">" + RowCount.ToString() + "</td>";
                    else
                        pageList += "<td " + styletd + "></td>";
                }
                if (hsTemplate["productPic"]?.ToInt() == 1)
                {
                    if (int.Parse(roundzhongj) == i + 1)
                        pageList += "<td " + styletd + "><img src='" + dtTmpSI.Rows[i]["productPic"].ToString() + "' width='" + model.PicSize + "' /></td>";
                    else
                        pageList += "<td " + styletd + "></td>";

                }
                if (hsTemplate["productName"]?.ToInt() == 1)
                {
                    if (int.Parse(roundzhongj) == i + 1)
                        pageList += "<td " + styletd + ">" + strProductName + "</td>";
                    else
                        pageList += "<td " + styletd + "></td>";
                }
                if (hsTemplate["productallName"]?.ToInt() == 1)
                {
                    if (int.Parse(roundzhongj) == i + 1)
                        pageList += "<td " + styletd + ">" + strProductallName + "</td>";
                    else
                        pageList += "<td " + styletd + "></td>";
                }
                if (hsTemplate["productshortName"]?.ToInt() == 1)
                {
                    if (int.Parse(roundzhongj) == i + 1)
                        pageList += "<td " + styletd + ">" + strProductshortName + "</td>";
                    else
                        pageList += "<td " + styletd + "></td>";
                }

                if (hsTemplate["cargoNumber"]?.ToInt() == 1)
                {
                    pageList += "<td align='center' style='word-break:break-all;'>" + dtTmpSI.Rows[i]["huoHao"].ToString() + "</td>";
                }
                if (hsTemplate["specInfo"]?.ToInt() == 1)
                {
                    pageList += "<td align='center'>" + dtTmpSI.Rows[i]["specInfo"].ToString() + "</td>";
                }
                if (hsTemplate["extAttr4"]?.ToInt() == 1)
                {
                    pageList += "<td align='center' style='word-break:break-all;'>" + dtTmpSI.Rows[i]["extAttr4"].ToString() + "</td>";
                }
                if (hsTemplate["quantity"]?.ToInt() == 1)
                {
                    pageList += "<td align='center'>" + dtTmpSI.Rows[i]["quantity"].ToString() + "</td>";
                }
                if (hsTemplate["singleQuantity"]?.ToInt() == 1)
                {
                    if (int.Parse(roundzhongj) == i + 1)
                        pageList += "<td " + styletd + ">" + singleCount + "</td>";
                    else
                        pageList += "<td " + styletd + "></td>";
                }
                if (hsTemplate["onePrice"]?.ToInt() == 1)
                {
                    if (dtTmpSI.Rows[i]["isShowSalePrice"].ToString() == "1")
                        pageList += "<td align='center'>￥" + dtTmpSI.Rows[i]["price"].ToString() + "</td>";
                    else
                        pageList += "<td align='center'>*</td>";
                }
                if (hsTemplate["discount"]?.ToInt() == 1)
                {
                    if (dtTmpSI.Rows[i]["isShowSalePrice"].ToString() == "1")
                    {
                        var tempDicount = dtTmpSI.Rows[i]["discount"].ToFloat();
                        if (tempDicount > 0)
                            pageList += "<td align='center'>+" + dtTmpSI.Rows[i]["discount"].ToString() + "</td>";
                        else
                            pageList += "<td align='center'>" + dtTmpSI.Rows[i]["discount"].ToString() + "</td>";
                    }
                    else
                        pageList += "<td align='center'>*</td>";
                }
                if (hsTemplate["Price"]?.ToInt() == 1)
                {
                    if (dtTmpSI.Rows[i]["isShowSalePrice"].ToString() == "1")
                    {
                        float pricesum = float.Parse(dtTmpSI.Rows[i]["payment"].ToString());
                        //float pricesum = float.Parse(dtTmpSI.Rows[i]["price"].ToString()) * int.Parse(dtTmpSI.Rows[i]["quantity"].ToString()) - float.Parse(dtTmpSI.Rows[i]["discount"].ToString());
                        pageList += "<td align='center'>￥" + pricesum.ToString("f2") + "</td>";
                    }
                    else
                        pageList += "<td align='center'>*</td>";
                }
                pageList += "</tr>";
            }

            RowCount++;
            return pageList;
        }

        private string BuildProductTableHtmlNormal(ref float productCount, ref int RowCount, Hashtable hsTemplate, DataTable dtTmpSI, OldSendGoodTemplate model)
        {
            string styletd = "";
            string pageList = "";
            string strProductName = "";
            string strProductallName = "";
            string strProductshortName = "";

            float rounzj_db = (float)dtTmpSI.Rows.Count / 2;
            string roundzhongj = Convert.ToString(Math.Round(rounzj_db + 0.5));
            if (roundzhongj == "0")
                roundzhongj = "1";
            if (model.TrHeight.ToInt() <= 15)
                model.TrHeight = "15";
            for (int i = 0; i < dtTmpSI.Rows.Count; i++)
            {
                if (hsTemplate["productName"]?.ToInt() == 1)
                {
                    strProductName = dtTmpSI.Rows[i]["productName"].ToString();
                }
                if (hsTemplate["productallName"]?.ToInt() == 1)
                {
                    strProductallName = dtTmpSI.Rows[i]["productallName"].ToString();
                }
                if (hsTemplate["productshortName"]?.ToInt() == 1)
                {
                    strProductshortName = dtTmpSI.Rows[i]["productshortName"].ToString();
                }
                productCount += int.Parse(dtTmpSI.Rows[i]["quantity"].ToString());
                styletd = "";

                pageList += "<tr align='center' style='line-height:" + model.TrHeight + "px;'>";
                if (hsTemplate["seqid"]?.ToInt() == 1)
                {
                    pageList += "<td " + styletd + ">" + RowCount.ToString() + "</td>";
                }
                if (hsTemplate["productPic"]?.ToInt() == 1)
                {
                    //测试商品二维码
                    //pageList += "<td " + styletd + "><img src='http://ma.m.1688.com/touch/code/sCode?w='" + model.PicSize + "'&h='" + model.PicSize + "'&el=m&type=offer&id=523272701089' /></td>";
                    pageList += "<td " + styletd + "><img src='" + dtTmpSI.Rows[i]["productPic"].ToString() + "' height='" + model.PicSize + "' width='" + model.PicSize + "' /></td>";
                    //if (int.Parse(roundzhongj) == i + 1)
                    //    pageList += "<td " + styletd + "><img src='" + dr1["productPic"].ToString() + "' width='" + model.PicSize + "' /></td>";
                    //else
                    //    pageList += "<td " + styletd + "></td>";

                }
                if (hsTemplate["productName"]?.ToInt() == 1)
                {
                    pageList += "<td " + styletd + ">" + strProductName + "</td>";
                    //if (int.Parse(roundzhongj) == i + 1)
                    //    pageList += "<td " + styletd + ">" + strProductName + "</td>";
                    //else
                    //    pageList += "<td " + styletd + "></td>";                                
                }
                if (hsTemplate["productallName"]?.ToInt() == 1)
                {
                    pageList += "<td " + styletd + ">" + strProductallName + "</td>";
                    //if (int.Parse(roundzhongj) == i + 1)
                    //    pageList += "<td " + styletd + ">" + strProductName + "</td>";
                    //else
                    //    pageList += "<td " + styletd + "></td>";                                
                }
                if (hsTemplate["productshortName"]?.ToInt() == 1)
                {
                    pageList += "<td " + styletd + ">" + strProductshortName + "</td>";
                    //if (int.Parse(roundzhongj) == i + 1)
                    //    pageList += "<td " + styletd + ">" + strProductName + "</td>";
                    //else
                    //    pageList += "<td " + styletd + "></td>";                                
                }

                if (hsTemplate["cargoNumber"]?.ToInt() == 1)
                {
                    pageList += "<td align='center' style='word-break:break-all;'>" + dtTmpSI.Rows[i]["huoHao"].ToString() + "</td>";
                }
                if (hsTemplate["product_cargoNumber"]?.ToInt() == 1)
                {
                    pageList += "<td align='center' style='word-break:break-all;'>" + dtTmpSI.Rows[i]["skuHuoHao"].ToString() + "</td>";
                }
                if (hsTemplate["specInfo"]?.ToInt() == 1)
                {
                    pageList += "<td align='center'>" + dtTmpSI.Rows[i]["specInfo"].ToString() + "</td>";
                }
                if (hsTemplate["extAttr4"]?.ToInt() == 1)
                {
                    pageList += "<td align='center' style='word-break:break-all;'>" + dtTmpSI.Rows[i]["extAttr4"].ToString() + "</td>";
                }
                if (hsTemplate["quantity"]?.ToInt() == 1)
                {
                    pageList += "<td align='center'>" + dtTmpSI.Rows[i]["quantity"].ToString() + "</td>";
                }
                if (hsTemplate["singleQuantity"]?.ToInt() == 1)
                {
                    pageList += "<td align='center'>" + dtTmpSI.Rows[i]["quantity"].ToString() + "</td>";
                }
                if (hsTemplate["onePrice"]?.ToInt() == 1)
                {
                    if (dtTmpSI.Rows[i]["isShowSalePrice"].ToString() == "1")
                        pageList += "<td align='center'>￥" + dtTmpSI.Rows[i]["price"].ToString() + "</td>";
                    else
                        pageList += "<td align='center'>*</td>";
                }
                if (hsTemplate["discount"]?.ToInt() == 1)
                {
                    if (dtTmpSI.Rows[i]["isShowSalePrice"].ToString() == "1")
                    {
                        var tempDicount = dtTmpSI.Rows[i]["discount"].ToFloat();
                        if (tempDicount > 0)
                            pageList += "<td align='center'>+" + dtTmpSI.Rows[i]["discount"].ToString() + "</td>";
                        else
                            pageList += "<td align='center'>" + dtTmpSI.Rows[i]["discount"].ToString() + "</td>";
                    }
                    else
                        pageList += "<td align='center'>*</td>";
                }

                if (hsTemplate["Price"]?.ToInt() == 1)
                {
                    //float pricesum = float.Parse(dtTmpSI.Rows[i]["price"].ToString()) * int.Parse(dtTmpSI.Rows[i]["quantity"].ToString()) - float.Parse(dtTmpSI.Rows[i]["discount"].ToString());
                    if (dtTmpSI.Rows[i]["isShowSalePrice"].ToString() == "1")
                    {
                        float pricesum = float.Parse(dtTmpSI.Rows[i]["payment"].ToString());
                        pageList += "<td align='center'>￥" + pricesum.ToString("f2") + "</td>";
                    }
                    else
                        pageList += "<td align='center'>*</td>";
                }
                pageList += "</tr>";

                RowCount++;

            }

            return pageList;

        }
        #endregion

        /// <summary>
        /// 封装旧模板数据组装方法
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Request"></param>
        /// <param name="PageToken"></param>
        /// <param name="template"></param>
        /// <param name="datas"></param>
        /// <param name="result"></param>
        /// <exception cref="LogicException"></exception>
        public void PrepareTemplateDataAndPrintDataOld(PrintSendGoodTemplateRequestModel model, HttpRequestBase request, string pageToken, out OldSendGoodTemplate template, out List<SendGoodTemplateDataModel> datas, out List<SendGoodTemplatePageModel> result)
        {
            if (model == null || model.Orders == null || !model.Orders.Any())
                throw new LogicException("请选择订单");
            if (!model.IsCustomerOrder && (model.Orders.Any(o => o.OrderItems == null || !o.OrderItems.Any() || o.OrderItems.Count() == 0)))
                throw new LogicException("请选择订单下要打印的商品");
            template = GetOldSendGoodTemplate();
            if (template == null)
                throw new LogicException("没有获取到发货单信息，请前往【设置】->>【发货单设置】设置发货单信息");

            datas = GetOrderTemplateModel(model, request == null ? string.Empty : $"{request.Url.Scheme}://{request.Url.Host}:{request.Url.Port}", string.IsNullOrEmpty(pageToken) ? string.Empty : pageToken);
            result = PrepareSendTemplatePrintDataOld(template, datas, model.IsCustomerOrder);
        }

        /// <summary>
        /// 封装新模板数据组装方法
        /// </summary>
        /// <param name="model"></param>
        /// <param name="template"></param>
        /// <param name="datas"></param>
        /// <param name="subLog_PrintData"></param>
        /// <param name="result"></param>
        /// <exception cref="LogicException"></exception>
        /// <exception cref="OrderBalanceException"></exception>
        public void PrepareTemplateDataAndPrintData(PrintSendGoodTemplateRequestModel model, HttpRequestBase request, string pageToken, out SendGoodTemplate template, out List<SendGoodTemplateDataModel> datas, out LogForOperator subLog_PrintData, out List<SendGoodTemplatePageModel> result)
        {
            if (model == null || model.Orders == null || !model.Orders.Any())
                throw new LogicException("请选择订单");
            if (!model.IsCustomerOrder && (model.Orders.Any(o => o.OrderItems == null || !o.OrderItems.Any() || o.OrderItems.Count() == 0)))
                throw new LogicException("请选择订单下要打印的商品");
            template = Get(model.TemplateId);
            if (template == null)
                throw new LogicException("没有获取到发货单信息，请前往【设置】->>【发货单设置】设置发货单信息");


            #region 订单量是否足够
            {
                var commonservice = new CommonSettingService();
                var ispay = commonservice.IsFxUserShouldPay(SiteContext.Current.CurrentFxUserId);
                if (ispay)
                {
                    UserOrderCountBalanceService _userOrderCountBalanceService = new UserOrderCountBalanceService();
                    var sendorderbalances = _userOrderCountBalanceService.getBalance(SiteContext.Current.CurrentFxUserId);
                    var tips = _userOrderCountBalanceService.getTips(sendorderbalances, model.Orders.Count);
                    if (!string.IsNullOrEmpty(tips))
                    {
                        throw new OrderBalanceException(tips, sendorderbalances);
                    }
                }
            }
            #endregion
            //处理指定平台默认配置显示名称
            var curPlatformType = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (curPlatformType == PlatformType.YouZan.ToString() || curPlatformType == PlatformType.KuaiShou.ToString())
            {
                template.Config = template.Config.Replace("颜色/型号", "商品规格");
                if (curPlatformType == PlatformType.KuaiShou.ToString())
                {
                    template.Config = template.Config.Replace("买家旺旺", "买家昵称");
                    template.Config = template.Config.Replace("商品货号", "单品货号");
                }
                else if (curPlatformType == PlatformType.WxXiaoShangDian.ToString())
                {
                    template.Config = template.Config.Replace("买家旺旺", "买家昵称");
                }
            }

            var config = JsonConvert.DeserializeObject<SendGoodTemplateModel>(template.Config);
            if (config == null)
                throw new LogicException("没有获取到发货单信息，请前往【设置】->>【发货单设置】设置发货单信息");

            //循环订单ID
            var buyer = config.TemplateItems.Where(t => t.textId == "customize24").ToList();

            //日志开始
            var subLog_getPrintData = LogForOperatorContext.Current.StartStep(new LogForOperator()
            {
                OperatorType = "获取打印数据"
            });

            datas = GetOrderTemplateModel(model, request == null ? string.Empty : $"{request.Url.Scheme}://{request.Url.Host}:{request.Url.Port}", string.IsNullOrEmpty(pageToken) ? string.Empty : pageToken, config);

            //日志结束
            LogForOperatorContext.Current.EndStep(subLog_getPrintData);

            //日志开始
            subLog_PrintData = LogForOperatorContext.Current.StartStep(new LogForOperator()
            {
                OperatorType = "组装打印机需要的数据"
            });
            result = PrepareSendTemplatePrintData(config, datas);
        }


        private static string GetCurrencyStr(string currencyCode)
        {
            string symbol = string.Empty;
            if (string.IsNullOrEmpty(currencyCode))
                return symbol;
            var currencySymbols = new Dictionary<string, string>
        {
            { "VND", "₫" }, // 越南盾
            { "MYR", "RM" }, // 马来西亚林吉特
            { "SGD", "S$" }, // 新加坡元
            { "PHP", "₱" }, // 菲律宾比索
            { "THB", "฿" }, // 泰铢
            { "GBP", "£" }, // 英镑
            { "USD", "$" }, // 美元
            { "CNY", "¥" }  // 人民币
        };
            if (currencySymbols.TryGetValue(currencyCode, out symbol))
            {
                return symbol;
            }
            return symbol;
        }

    }
}
