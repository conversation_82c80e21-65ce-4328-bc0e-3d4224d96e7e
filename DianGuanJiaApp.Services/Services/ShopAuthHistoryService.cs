using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json.Linq;

namespace DianGuanJiaApp.Services
{
    public class ShopAuthHistoryService : BaseService<ShopAuthHistory>
    {
        private readonly ShopAuthHistoryRepository _repository;

        public ShopAuthHistoryService()
        {
            _repository = new ShopAuthHistoryRepository();
            this._baseRepository = _repository;
        }

        /// <summary>
        /// 获取授权过期和关闭授权历史信息
        /// </summary>
        /// <returns></returns>
        public List<ShopAuthHistory> GetAuthExpiredOrClosedHistories(string platformType, string appId,
            string platformShopId,
            DateTime startTime,
            DateTime endTime)
        {
            return _repository.GetAuthExpiredOrClosedHistories(platformType, appId, platformShopId, startTime, endTime);
        }

        public void AddAuthHistory(JToken msgJToken)
        {
            try
            {
                var shopAuthHistory = new ShopAuthHistory
                {
                    PlatformShopId = msgJToken.Value<string>("shop_id"),
                    PlatformType = PlatformType.TouTiao.ToString(),
                    AppId = msgJToken.Value<string>("app_id"),
                    ActionType = msgJToken.Value<int>("action_type"),
                    HostName = Environment.MachineName,
                    CreateTime = DateTime.Now
                };
                AddAuthHistory(shopAuthHistory);
            }
            catch (Exception e)
            {
                Log.WriteError(
                    $"保存店铺授权历史失败，失败原因：{e.Message}，堆栈信息：{Environment.StackTrace}，店铺授权历史信息：{msgJToken}.",
                    "ShopAuthHistory.log");
            }
        }

        /// <summary>
        /// 保存授权历史
        /// </summary>
        /// <param name="model"></param>
        public void AddAuthHistory(ShopAuthHistory model)
        {
            //判空处理
            if (model == null)
            {
                return;
            }
            //添加授权历史
            try
            {
                _repository.Add(model);
            }
            catch (Exception e)
            {
                Log.WriteError(
                    $"保存店铺授权历史失败，失败原因：{e.Message}，堆栈信息：{Environment.StackTrace}，店铺授权历史信息：{model.ToJson(true)}.",
                    "ShopAuthHistory.log");
                return;
            }

            //同步到抖音云
            AsyncSyncTouTiaoCloud(model);
        }

        private void AsyncSyncTouTiaoCloud(ShopAuthHistory model)
        {
            //同步头条配置
            ThreadPool.QueueUserWorkItem(s =>
            {
                try
                {
                    const string fields = "PlatformShopId,PlatformType,AppId,ActionType,HostName,CreateTime";
                    var insertFields = fields.Split(',').ToList();
                    var valueParams = insertFields.Select(f => $"@{f}").ToList();
                    var sql = $"INSERT INTO P_Shop_Auth_History({fields})VALUES({string.Join(",", valueParams)});";
                    //同步到头条配置库
                    var db = DbApiAccessUtility.GetTouTiaoConfigureDb();
                    db.IsAsync = true;
                    var result = db.ExecuteScalar(sql, model);
                }
                catch (Exception ex)
                {
                    Log.WriteError(
                        $"保存店铺授权历史同步抖音云失败，失败原因：{ex.Message}，堆栈信息：{Environment.StackTrace}，店铺授权历史信息：{model.ToJson(true)}.",
                        "ShopAuthHistory.log");
                }
            });
        }
    }
}