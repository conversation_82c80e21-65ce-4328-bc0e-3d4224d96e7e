using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Entity.SupplierProduct;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository.SupplierProduct;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Services.Services.SupplierProduct
{
    /// <summary>
    /// 供应商地址服务层
    /// </summary>
    public class SupplierAddressService : SupplierProductBaseService<SupplierAddress>
    {
        private readonly SupplierAddressRepository _repository;

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public SupplierAddressService()
        {
            _repository = new SupplierAddressRepository();
        }

        /// <summary>
        /// 根据连接字符串和数据库类型获取数据库连接
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="isUseMySql"></param>
        public SupplierAddressService(string connectionString, bool isUseMySql)
        {
            _repository = new SupplierAddressRepository(connectionString, isUseMySql);
        }

        /// <summary>
        /// 添加地址
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ReturnedModel<long> AddAddress(SupplierAddress model)
        {
            var result = new ReturnedModel<long>();

            try
            {
                // 参数验证
                if (model == null)
                {
                    result.Success = false;
                    result.Message = "地址信息不能为空";
                    return result;
                }

                if (model.FxUserId <= 0)
                {
                    result.Success = false;
                    result.Message = "用户ID不能为空";
                    return result;
                }

                if (string.IsNullOrEmpty(model.AddressCode))
                {
                    model.AddressCode = Guid.NewGuid().ToString("N");
                }

                // 检查AddressCode是否已存在
                if (_repository.IsExistAddressCode(model.AddressCode))
                {
                    result.Success = false;
                    result.Message = "地址编码已存在";
                    return result;
                }

                // 设置创建时间
                model.CreateTime = DateTime.Now;
                model.UpdateTime = DateTime.Now;

                var id = _repository.Add(model);
                if (id > 0)
                {
                    result.Success = true;
                    result.Data = id;
                    result.Message = "添加成功";
                }
                else
                {
                    result.Success = false;
                    result.Message = "添加失败";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"添加地址时发生错误：{ex.Message}";
                Log.WriteError($"添加地址时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
            }

            return result;
        }

        /// <summary>
        /// 更新地址
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ReturnedModel<bool> UpdateAddress(SupplierAddress model)
        {
            var result = new ReturnedModel<bool>();

            try
            {
                // 参数验证
                if (model == null)
                {
                    result.Success = false;
                    result.Message = "地址信息不能为空";
                    return result;
                }

                if (model.Id <= 0)
                {
                    result.Success = false;
                    result.Message = "地址ID不能为空";
                    return result;
                }

                // 检查地址是否存在
                var existingAddress = _repository.GetById(model.Id);
                if (existingAddress == null)
                {
                    result.Success = false;
                    result.Message = "地址不存在";
                    return result;
                }

                // 设置更新时间
                model.UpdateTime = DateTime.Now;

                var success = _repository.Update(model);
                result.Success = success;
                result.Data = success;
                result.Message = success ? "更新成功" : "更新失败";
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"更新地址时发生错误：{ex.Message}";
                Log.WriteError($"更新地址时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
            }

            return result;
        }

        /// <summary>
        /// 根据AddressCode获取地址信息
        /// </summary>
        /// <param name="addressCode"></param>
        /// <returns></returns>
        public SupplierAddress GetByAddressCode(string addressCode)
        {
            if (string.IsNullOrEmpty(addressCode))
                return null;

            return _repository.GetByAddressCode(addressCode);
        }

        /// <summary>
        /// 根据用户ID和地址类型获取地址列表
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="addressType">地址类型 (0:商品发货地址, 1:名片地址)</param>
        /// <returns></returns>
        public List<SupplierAddress> GetByFxUserIdAndType(int fxUserId, int? addressType = null)
        {
            if (fxUserId <= 0)
                return new List<SupplierAddress>();

            return _repository.GetByFxUserIdAndType(fxUserId, addressType);
        }

        /// <summary>
        /// 根据商品ID获取发货地址
        /// </summary>
        /// <param name="productUid"></param>
        /// <returns></returns>
        public List<SupplierAddress> GetByProductUid(long productUid)
        {
            if (productUid <= 0)
                return new List<SupplierAddress>();

            return _repository.GetByProductUid(productUid);
        }

        /// <summary>
        /// 根据ID获取地址信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public SupplierAddress GetById(long id)
        {
            if (id <= 0)
                return null;

            return _repository.GetById(id);
        }

        /// <summary>
        /// 删除地址
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ReturnedModel<bool> DeleteAddress(long id)
        {
            var result = new ReturnedModel<bool>();

            try
            {
                if (id <= 0)
                {
                    result.Success = false;
                    result.Message = "地址ID不能为空";
                    return result;
                }

                // 检查地址是否存在
                var existingAddress = _repository.GetById(id);
                if (existingAddress == null)
                {
                    result.Success = false;
                    result.Message = "地址不存在";
                    return result;
                }

                var success = _repository.Delete(id);
                result.Success = success;
                result.Data = success;
                result.Message = success ? "删除成功" : "删除失败";
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"删除地址时发生错误：{ex.Message}";
                Log.WriteError($"删除地址时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
            }

            return result;
        }

        /// <summary>
        /// 根据AddressCode删除地址
        /// </summary>
        /// <param name="addressCode"></param>
        /// <returns></returns>
        public ReturnedModel<bool> DeleteByAddressCode(string addressCode)
        {
            var result = new ReturnedModel<bool>();

            try
            {
                if (string.IsNullOrEmpty(addressCode))
                {
                    result.Success = false;
                    result.Message = "地址编码不能为空";
                    return result;
                }

                // 检查地址是否存在
                var existingAddress = _repository.GetByAddressCode(addressCode);
                if (existingAddress == null)
                {
                    result.Success = false;
                    result.Message = "地址不存在";
                    return result;
                }

                var success = _repository.DeleteByAddressCode(addressCode);
                result.Success = success;
                result.Data = success;
                result.Message = success ? "删除成功" : "删除失败";
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"删除地址时发生错误：{ex.Message}";
                Log.WriteError($"删除地址时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
            }

            return result;
        }

        /// <summary>
        /// 批量添加地址
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public ReturnedModel<bool> BatchAddAddress(List<SupplierAddress> models)
        {
            var result = new ReturnedModel<bool>();

            try
            {
                if (models == null || !models.Any())
                {
                    result.Success = false;
                    result.Message = "地址列表不能为空";
                    return result;
                }

                // 为没有AddressCode的地址生成编码
                foreach (var model in models.Where(m => string.IsNullOrEmpty(m.AddressCode)))
                {
                    model.AddressCode = Guid.NewGuid().ToString("N");
                }

                // 设置创建时间
                var now = DateTime.Now;
                models.ForEach(m =>
                {
                    m.CreateTime = now;
                    m.UpdateTime = now;
                });

                _repository.BatchAdd(models);
                result.Success = true;
                result.Data = true;
                result.Message = "批量添加成功";
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"批量添加地址时发生错误：{ex.Message}";
                Log.WriteError($"批量添加地址时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
            }

            return result;
        }

        /// <summary>
        /// 将BusinessCardSendaddress转换为SupplierAddressEntity
        /// </summary>
        /// <param name="sendAddress"></param>
        /// <param name="fxUserId"></param>
        /// <param name="productUid"></param>
        /// <returns></returns>
        private SupplierAddress ConvertToSupplierAddress(BusinessCardSendaddress sendAddress, int fxUserId, long? productUid = null)
        {
            if (sendAddress == null)
                return null;

            return new SupplierAddress
            {
                // 如果 sendAddress 中包含 AddressCode 且有值，则使用它；否则生成新的
                AddressCode = !string.IsNullOrEmpty(sendAddress.AddressCode)
                    ? sendAddress.AddressCode
                    : Guid.NewGuid().ToString("N"),
                FxUserId = fxUserId,
                ProductUid = productUid,
                AddressType = productUid.HasValue ? 0 : 1, // 0:商品发货地址, 1:名片地址
                ProvinceName = sendAddress.Province,
                CityName = sendAddress.City,
                DistrictName = sendAddress.County,
                StreetName = sendAddress.Street,
                DetailAddress = sendAddress.Address,
                ContactName = sendAddress.ReceiverName,
                ContactMobile = sendAddress.ReceiverContract,
                ContactPhone = sendAddress.ReceiverTel,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now
            };
        }

        /// <summary>
        /// 保存商品发货地址，合并更新
        /// </summary>
        /// <param name="productUid">商品的唯一标识</param>
        /// <param name="addressCode">地址编码，用于定位具体要更新的地址</param>
        /// <param name="sendAddress">地址信息</param>
        /// <param name="fxUserId">用户ID</param>
        /// <returns></returns>
        public ReturnedModel<string> SaveProductAddresses(long productUid, string addressCode, BusinessCardSendaddress sendAddress, int fxUserId)
        {
            var result = new ReturnedModel<string>();

            try
            {
                if (sendAddress == null)
                {
                    result.Success = false;
                    result.Message = "地址信息不能为空";
                    return result;
                }

                if (fxUserId <= 0)
                {
                    result.Success = false;
                    result.Message = "用户ID不能为空";
                    return result;
                }

                if (productUid <= 0)
                {
                    result.Success = false;
                    result.Message = "商品ID不能为空";
                    return result;
                }

                SupplierAddress targetAddress = null;
                var isUpdate = false;

                // 1. 优先通过 addressCode 查找现有地址
                if (!string.IsNullOrEmpty(addressCode))
                {
                    targetAddress = _repository.GetByAddressCode(addressCode);
                    if (targetAddress != null && targetAddress.ProductUid == productUid && targetAddress.FxUserId == fxUserId)
                    {
                        isUpdate = true;
                        Log.Debug(() => $"通过AddressCode找到匹配地址: ProductUid={productUid}, AddressCode={addressCode}", LogModuleTypeEnum.DistributionProduct);
                    }
                    else if (targetAddress != null)
                    {
                        // AddressCode存在但不属于当前商品或用户，记录警告
                        Log.Debug(() => $"AddressCode存在但不匹配: AddressCode={addressCode}, ProductUid={productUid}, FxUserId={fxUserId}", LogModuleTypeEnum.DistributionProduct);
                        targetAddress = null;
                    }
                }

                // 2. 如果通过 addressCode 未找到，则查找该商品的现有地址进行匹配
                if (targetAddress == null)
                {
                    var existingAddresses = _repository.GetByProductUid(productUid);
                    targetAddress = FindMatchingAddress(sendAddress, existingAddresses);
                    if (targetAddress != null)
                    {
                        isUpdate = true;
                        Log.Debug(() => $"通过地址匹配找到现有地址: ProductUid={productUid}, AddressCode={targetAddress.AddressCode}", LogModuleTypeEnum.DistributionProduct);
                    }
                }

                // 3. 执行更新或新增操作
                if (isUpdate)
                {
                    // 更新现有地址
                    UpdateAddressFromSendAddress(targetAddress, sendAddress);
                    var updateSuccess = _repository.Update(targetAddress);

                    if (updateSuccess)
                    {
                        result.Success = true;
                        result.Data = targetAddress.AddressCode;
                        result.Message = "地址更新成功";
                        Log.Debug(() => $"地址更新成功: ProductUid={productUid}, AddressCode={targetAddress.AddressCode}", LogModuleTypeEnum.DistributionProduct);
                    }
                    else
                    {
                        result.Success = false;
                        result.Message = "地址更新失败";
                        Log.WriteError($"地址更新失败: ProductUid={productUid}, AddressCode={targetAddress.AddressCode}", LogModuleTypeEnum.DistributionProduct);
                    }
                }
                else
                {
                    // 新增地址
                    var newAddress = ConvertToSupplierAddress(sendAddress, fxUserId, productUid);
                    if (newAddress != null)
                    {
                        // 如果传入了 addressCode 且不存在冲突，使用传入的 addressCode
                        if (!string.IsNullOrEmpty(addressCode) && !_repository.IsExistAddressCode(addressCode))
                        {
                            newAddress.AddressCode = addressCode;
                        }

                        var addId = _repository.Add(newAddress);
                        if (addId > 0)
                        {
                            result.Success = true;
                            result.Data = newAddress.AddressCode;
                            result.Message = "地址新增成功";
                            Log.Debug(() => $"地址新增成功: ProductUid={productUid}, AddressCode={newAddress.AddressCode}", LogModuleTypeEnum.DistributionProduct);
                        }
                        else
                        {
                            result.Success = false;
                            result.Message = "地址新增失败";
                            Log.WriteError($"地址新增失败: ProductUid={productUid}", LogModuleTypeEnum.DistributionProduct);
                        }
                    }
                    else
                    {
                        result.Success = false;
                        result.Message = "地址信息转换失败";
                    }
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"保存商品地址时发生错误：{ex.Message}";
                Log.WriteError($"保存商品地址时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
            }

            return result;
        }
  
        /// <summary>
        /// 获取商品的地址信息（兼容新旧数据格式）
        /// </summary>
        /// <param name="productUid"></param>
        /// <param name="addressCode"></param>
        /// <param name="shipmentsInfo">历史数据的发货信息</param>
        /// <returns></returns>
        public List<BusinessCardSendaddress> GetProductAddresses(long productUid, string addressCode, ShipmentsInfo shipmentsInfo = null)
        {
            try
            {
                // 优先从SupplierAddress表获取地址信息
                if (!string.IsNullOrEmpty(addressCode))
                {
                    var addresses = _repository.GetByProductUid(productUid);
                    if (addresses.Any())
                    {
                        return addresses.Select(ConvertToBusinessCardSendaddress).ToList();
                    }
                }

                // 兼容历史数据，从ShipmentsInfo获取
                if (shipmentsInfo?.SendaddressList != null && shipmentsInfo.SendaddressList.Any())
                {
                    return shipmentsInfo.SendaddressList;
                }

                return new List<BusinessCardSendaddress>();
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取商品地址信息时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
                return new List<BusinessCardSendaddress>();
            }
        }

        /// <summary>
        /// 将SupplierAddressEntity转换为BusinessCardSendaddress
        /// </summary>
        /// <param name="addressEntity"></param>
        /// <returns></returns>
        private BusinessCardSendaddress ConvertToBusinessCardSendaddress(SupplierAddress addressEntity)
        {
            if (addressEntity == null)
                return null;

            return new BusinessCardSendaddress
            {
                Province = addressEntity.ProvinceName,
                City = addressEntity.CityName,
                County = addressEntity.DistrictName,
                Street = addressEntity.StreetName,
                Address = addressEntity.DetailAddress,
                ReceiverName = addressEntity.ContactName,
                ReceiverContract = addressEntity.ContactMobile,
                ReceiverTel = addressEntity.ContactPhone,
                AddressCode = addressEntity.AddressCode // 添加 AddressCode 字段的转换
            };
        }

        /// <summary>
        /// 查找匹配的地址，一般不会匹配这个逻辑
        /// </summary>
        /// <param name="sendAddress"></param>
        /// <param name="existingAddresses"></param>
        /// <returns></returns>
        private SupplierAddress FindMatchingAddress(BusinessCardSendaddress sendAddress, List<SupplierAddress> existingAddresses)
        {
            if (sendAddress == null || existingAddresses == null || !existingAddresses.Any())
                return null;

            // 通过地址信息组合判断是否为同一地址
            foreach (var existingAddress in existingAddresses)
            {
                if (IsAddressMatched(sendAddress, existingAddress))
                {
                    return existingAddress;
                }
            }

            return null;
        }

        /// <summary>
        /// 判断地址是否匹配
        /// </summary>
        /// <param name="sendAddress"></param>
        /// <param name="existingAddress"></param>
        /// <returns></returns>
        private bool IsAddressMatched(BusinessCardSendaddress sendAddress, SupplierAddress existingAddress)
        {
            if (sendAddress == null || existingAddress == null)
                return false;

            // 比较省市区和详细地址
            var provinceMatch = string.Equals(sendAddress.Province?.Trim(), existingAddress.ProvinceName?.Trim(), StringComparison.OrdinalIgnoreCase);
            var cityMatch = string.Equals(sendAddress.City?.Trim(), existingAddress.CityName?.Trim(), StringComparison.OrdinalIgnoreCase);
            var districtMatch = string.Equals(sendAddress.County?.Trim(), existingAddress.DistrictName?.Trim(), StringComparison.OrdinalIgnoreCase);
            var addressMatch = string.Equals(sendAddress.Address?.Trim(), existingAddress.DetailAddress?.Trim(), StringComparison.OrdinalIgnoreCase);

            // 省市区和详细地址都匹配才认为是同一地址
            return provinceMatch && cityMatch && districtMatch && addressMatch;
        }

        /// <summary>
        /// 从BusinessCardSendaddress更新SupplierAddress
        /// </summary>
        /// <param name="addressEntity"></param>
        /// <param name="sendAddress"></param>
        private void UpdateAddressFromSendAddress(SupplierAddress addressEntity, BusinessCardSendaddress sendAddress)
        {
            if (addressEntity == null || sendAddress == null)
                return;

            // 更新地址信息
            addressEntity.ProvinceName = sendAddress.Province;
            addressEntity.CityName = sendAddress.City;
            addressEntity.DistrictName = sendAddress.County;
            addressEntity.StreetName = sendAddress.Street;
            addressEntity.DetailAddress = sendAddress.Address;

            // 更新联系人信息
            addressEntity.ContactName = sendAddress.ReceiverName;
            addressEntity.ContactMobile = sendAddress.ReceiverContract;
            addressEntity.ContactPhone = sendAddress.ReceiverTel;

            // 更新时间
            addressEntity.UpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 检查AddressCode是否存在
        /// </summary>
        /// <param name="addressCode"></param>
        /// <returns></returns>
        public bool IsExistAddressCode(string addressCode)
        {
            if (string.IsNullOrEmpty(addressCode))
                return false;

            return _repository.IsExistAddressCode(addressCode);
        }

        /// <summary>
        /// 保存名片发货地址（名片地址模式）
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="addressCode">地址编码，用于定位具体要更新的地址</param>
        /// <param name="sendAddress">包含地址信息的 BusinessCardSendaddress 对象</param>
        /// <returns></returns>
        public ReturnedModel<string> SaveBusinessCardAddress(int fxUserId, string addressCode, BusinessCardSendaddress sendAddress)
        {
            var result = new ReturnedModel<string>();

            try
            {
                if (sendAddress == null)
                {
                    result.Success = false;
                    result.Message = "地址信息不能为空";
                    return result;
                }

                if (fxUserId <= 0)
                {
                    result.Success = false;
                    result.Message = "用户ID不能为空";
                    return result;
                }

                SupplierAddress targetAddress = null;
                bool isUpdate = false;

                // 1. 优先通过 addressCode 查找现有地址
                if (!string.IsNullOrEmpty(addressCode))
                {
                    targetAddress = _repository.GetByAddressCode(addressCode);
                    if (targetAddress != null && targetAddress.FxUserId == fxUserId && targetAddress.AddressType == 1)
                    {
                        isUpdate = true;
                        Log.Debug(() => $"通过AddressCode找到匹配的名片地址: FxUserId={fxUserId}, AddressCode={addressCode}", LogModuleTypeEnum.DistributionProduct);
                    }
                    else if (targetAddress != null)
                    {
                        // AddressCode存在但不属于当前用户或不是名片地址，记录警告
                        Log.WriteWarning($"AddressCode存在但不匹配名片地址: AddressCode={addressCode}, FxUserId={fxUserId}", LogModuleTypeEnum.DistributionProduct);
                        targetAddress = null;
                    }
                }

                // 2. 如果通过 addressCode 未找到，则查找该用户的现有名片地址进行匹配
                if (targetAddress == null)
                {
                    var existingAddresses = _repository.GetByFxUserIdAndType(fxUserId, 1); // 名片地址类型
                    targetAddress = FindMatchingAddress(sendAddress, existingAddresses);
                    if (targetAddress != null)
                    {
                        isUpdate = true;
                        Log.Debug(() => $"通过地址匹配找到现有名片地址: FxUserId={fxUserId}, AddressCode={targetAddress.AddressCode}", LogModuleTypeEnum.DistributionProduct);
                    }
                }

                // 3. 执行更新或新增操作
                if (isUpdate)
                {
                    sendAddress.AddressCode = targetAddress.AddressCode;
                    // 更新现有地址
                    UpdateAddressFromSendAddress(targetAddress, sendAddress);
                    var updateSuccess = _repository.Update(targetAddress);

                    if (updateSuccess)
                    {
                        result.Success = true;
                        result.Data = targetAddress.AddressCode;
                        result.Message = "名片地址更新成功";
                        Log.Debug(() => $"名片地址更新成功: FxUserId={fxUserId}, AddressCode={targetAddress.AddressCode}", LogModuleTypeEnum.DistributionProduct);
                    }
                    else
                    {
                        result.Success = false;
                        result.Message = "名片地址更新失败";
                        Log.WriteError($"名片地址更新失败: FxUserId={fxUserId}, AddressCode={targetAddress.AddressCode}", LogModuleTypeEnum.DistributionProduct);
                    }
                }
                else
                {
                    // 新增地址
                    var newAddress = ConvertToSupplierAddress(sendAddress, fxUserId); 
                    if (newAddress != null)
                    {
                        newAddress.AddressType = 1; // 设置为名片地址类型

                        // 处理 AddressCode 的设置逻辑
                        // 1. 优先使用传入的 sendAddress.AddressCode（如果有值且不冲突）
                        if (!string.IsNullOrEmpty(sendAddress.AddressCode) && !_repository.IsExistAddressCode(sendAddress.AddressCode))
                        {
                            newAddress.AddressCode = sendAddress.AddressCode;
                        }
                        // 2. 其次使用参数传入的 addressCode（如果有值且不冲突）
                        else if (!string.IsNullOrEmpty(addressCode) && !_repository.IsExistAddressCode(addressCode))
                        {
                            newAddress.AddressCode = addressCode;
                        }
                        // 3. 如果都没有或存在冲突，则使用系统自动生成的 AddressCode（在 Add 方法中生成）

                        var addId = _repository.Add(newAddress);
                        if (addId > 0)
                        {
                            sendAddress.AddressCode = newAddress.AddressCode;
                            result.Success = true;
                            result.Data = newAddress.AddressCode;
                            result.Message = "名片地址新增成功";
                            Log.Debug(() => $"名片地址新增成功: FxUserId={fxUserId}, AddressCode={newAddress.AddressCode}", LogModuleTypeEnum.DistributionProduct);
                        }
                        else
                        {
                            result.Success = false;
                            result.Message = "名片地址新增失败";
                            Log.WriteError($"名片地址新增失败: FxUserId={fxUserId}", LogModuleTypeEnum.DistributionProduct);
                        }
                    }
                    else
                    {
                        result.Success = false;
                        result.Message = "地址信息转换失败";
                    }
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"保存名片地址时发生错误：{ex.Message}";
                Log.WriteError($"保存名片地址时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
            }

            return result;
        }

        /// <summary>
        /// 获取名片的发货地址信息（兼容新旧数据格式）
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="addressCode">地址编码（可选）</param>
        /// <param name="historicalAddressList">历史数据的发货地址列表</param>
        /// <returns></returns>
        public List<BusinessCardSendaddress> GetBusinessCardAddresses(int fxUserId, string addressCode = null, List<BusinessCardSendaddress> historicalAddressList = null)
        {
            try
            {
                // 优先从SupplierAddress表获取名片地址信息
                if (!string.IsNullOrEmpty(addressCode))
                {
                    var address = _repository.GetByAddressCode(addressCode);
                    if (address != null && address.FxUserId == fxUserId && address.AddressType == 1)
                    {
                        return new List<BusinessCardSendaddress> { ConvertToBusinessCardSendaddress(address) };
                    }
                }

                // 如果没有指定AddressCode或未找到，则获取该用户的所有名片地址
                var addresses = _repository.GetByFxUserIdAndType(fxUserId, 1); // 名片地址类型
                if (addresses.Any())
                {
                    return addresses.Select(ConvertToBusinessCardSendaddress).ToList();
                }

                // 兼容历史数据，从传入的历史地址列表获取
                if (historicalAddressList != null && historicalAddressList.Any())
                {
                    return historicalAddressList;
                }

                return new List<BusinessCardSendaddress>();
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取名片地址信息时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
                return historicalAddressList ?? new List<BusinessCardSendaddress>();
            }
        }

        /// <summary>
        /// 获取商品的主要发货地址
        /// </summary>
        /// <param name="productUid">商品ID</param>
        /// <param name="addressCode">地址编码（可选）</param>
        /// <returns></returns>
        public SupplierAddress GetPrimaryProductAddress(long productUid, string addressCode = null)
        {
            try
            {
                // 如果提供了 addressCode，优先通过 addressCode 获取
                if (!string.IsNullOrEmpty(addressCode))
                {
                    var addressByCode = _repository.GetByAddressCode(addressCode);
                    if (addressByCode != null && addressByCode.ProductUid == productUid)
                    {
                        return addressByCode;
                    }
                }

                // 否则获取该商品的第一个地址作为主要地址
                var addresses = _repository.GetByProductUid(productUid);
                return addresses.FirstOrDefault();
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取商品主要发货地址时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
                return null;
            }
        }

        /// <summary>
        /// 获取商品的主要发货地址并转换为 BusinessCardSendaddress
        /// </summary>
        /// <param name="productUid">商品ID</param>
        /// <param name="addressCode">地址编码（可选）</param>
        /// <returns></returns>
        public BusinessCardSendaddress GetPrimaryProductAddressAsBusinessCard(long productUid, string addressCode = null)
        {
            var address = GetPrimaryProductAddress(productUid, addressCode);
            return ConvertToBusinessCardSendaddress(address);
        }

        /// <summary>
        /// 删除商品的所有地址
        /// </summary>
        /// <param name="productUid">商品ID</param>
        /// <returns></returns>
        public ReturnedModel<bool> DeleteAllProductAddresses(long productUid)
        {
            var result = new ReturnedModel<bool>();

            try
            {
                if (productUid <= 0)
                {
                    result.Success = false;
                    result.Message = "商品ID不能为空";
                    return result;
                }

                var addresses = _repository.GetByProductUid(productUid);
                var deleteCount = 0;

                foreach (var address in addresses)
                {
                    if (_repository.Delete(address.Id))
                    {
                        deleteCount++;
                    }
                }

                result.Success = true;
                result.Data = true;
                result.Message = $"成功删除{deleteCount}个地址";

                Log.Debug(() => $"删除商品所有地址完成: ProductUid={productUid}, 删除数量={deleteCount}", LogModuleTypeEnum.DistributionProduct);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"删除商品地址时发生错误：{ex.Message}";
                Log.WriteError($"删除商品地址时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
            }

            return result;
        }
    }
}
