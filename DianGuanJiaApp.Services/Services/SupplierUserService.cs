using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility.Extension;
using System.Data.Common;
using DianGuanJiaApp.Services.Services.SettingsService;
using DianGuanJiaApp.Services.Services.ManualOrder;
using System.Data;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Utility.Helpers;
using DianGuanJiaApp.Services.Services;
using Dapper;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services.SupplierProduct;
using DianGuanJiaApp.Data.Entity.SupplierProduct;

namespace DianGuanJiaApp.Services
{
    public partial class SupplierUserService : BaseService<Data.Entity.SupplierUser>
    {
        private SupplierUserRepository _repository = new SupplierUserRepository();
        public SupplierUserService()
        {
            _baseRepository = new Data.Repository.BaseRepository<SupplierUser>(CustomerConfig.ConfigureDbConnectionString);
        }

        /// <summary>
        /// 分页获取集合
        /// 返回元组格式,Item1(总数量),Item2(集合)
        /// </summary>
        /// <param name="key"></param>
        /// <param name="status"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="onlyGetCurDb">只取当前所在分区</param>
        /// <param name="formtype">来源 1：对账设置</param>
        /// <returns></returns>
        public Tuple<int, List<SupplierUser>> GetSupplierList(int fxUserId, string key, AgentBingSupplierStatus? status, int pageIndex, int pageSize, bool onlyGetCurDb = false,bool needEncryptAccount = false
            ,int formtype = 0)
        {
            List<AgentBingSupplierStatus> not_Status = new List<AgentBingSupplierStatus>();
            if (formtype == 1) not_Status = GetStatusFilter(); // 厂家

            var list = _repository.GetSupplierList(fxUserId, key, status, not_Status, pageIndex, pageSize,needEncryptAccount:needEncryptAccount);
            //只取当前所在库的厂家
            if (onlyGetCurDb)
            {
                var supplierUsers = OnlyGetCurDbResult(fxUserId, list.Item2, false);
                return Tuple.Create(supplierUsers.Count, supplierUsers);
            }
            else
            {
                return list;
            }
        }

        private List<AgentBingSupplierStatus> GetStatusFilter()
        {
            var status_filter = new List<AgentBingSupplierStatus>();
            var configsvc = new CommonSettingService();
            var cancelCooperate = configsvc.GetString("/ErpWeb/Settlement/CancelCooperate", SiteContext.Current.CurrentShopId);
            var abnormalCooperate = configsvc.GetString("/ErpWeb/Settlement/AbnormalCooperate", SiteContext.Current.CurrentShopId);
            if (string.Equals(cancelCooperate, "true", StringComparison.OrdinalIgnoreCase) &&
                string.Equals(abnormalCooperate, "true", StringComparison.OrdinalIgnoreCase))
            {
                //status_filter.Add(AgentBingSupplierStatus.Binded);
                status_filter.Add(AgentBingSupplierStatus.Apply);
                status_filter.Add(AgentBingSupplierStatus.Reject);
                status_filter.Add(AgentBingSupplierStatus.UnBinding);
                status_filter.Add(AgentBingSupplierStatus.UnBindFail);
                status_filter.Add(AgentBingSupplierStatus.UnBind);
            }
            else if (string.Equals(cancelCooperate, "true", StringComparison.OrdinalIgnoreCase)) // 出账列表账号不展示-已取消合作的账号
            {
                status_filter.Add(AgentBingSupplierStatus.UnBind);
            }
            else if (string.Equals(abnormalCooperate, "true", StringComparison.OrdinalIgnoreCase))// 出账列表账号不展示-非正常合作状态的账号（包含:申请绑定中，绑定失败，解绑处理中，解绑失败）
            {
                status_filter.Add(AgentBingSupplierStatus.Apply);
                status_filter.Add(AgentBingSupplierStatus.Reject);
                status_filter.Add(AgentBingSupplierStatus.UnBinding);
                status_filter.Add(AgentBingSupplierStatus.UnBindFail);
            }

            return status_filter;
        }

        public List<SupplierUser> GetSupplierList(int fxUserId, AgentBingSupplierStatus? status = null, bool onlyGetCurDb = false, bool isIgnoreHideCancelUserSetting = false,bool needEncryptAccount = false)
        {
            var list = _repository.GetSupplierList(fxUserId, status, isIgnoreHideCancelUserSetting, needEncryptAccount: needEncryptAccount);

            //只取当前所在库的厂家
            if (onlyGetCurDb)
                return OnlyGetCurDbResult(fxUserId, list, false);
            else
                return list;
        }

        /// <summary>
        /// 分页获取集合
        /// 返回元组格式,Item1(总数量),Item2(集合)
        /// </summary>
        /// <param name="key"></param>
        /// <param name="status"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public Tuple<int, List<SupplierUser>> GetSupplierListByFinancialSettlement(int fxUserId, string key, AgentBingSupplierStatus? status, int pageIndex, int pageSize)
        {
            var list = _repository.GetSupplierListByFinancialSettlement(fxUserId, key, status, pageIndex, pageSize);
            return list;
        }

        /// <summary>
        /// 分页获取集合
        /// 返回元组格式,Item1(总数量),Item2(集合)
        /// </summary>
        /// <param name="key"></param>
        /// <param name="status"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="formtype">来源 1: 对账设置</param>
        /// <returns></returns>
        public Tuple<int, List<SupplierUser>> GetAgentList(int fxUserId, string key, AgentBingSupplierStatus? status, int pageIndex, int pageSize, bool onlyGetCurDb = false, bool needEncryptAccount = false, int formtype = 0)
        {
            List<AgentBingSupplierStatus> not_Status = new List<AgentBingSupplierStatus>();
            if (formtype == 1)  not_Status = GetStatusFilter(); // 商家

            var list = _repository.GetAgentList(fxUserId, key, status, not_Status, pageIndex, pageSize, needEncryptAccount: needEncryptAccount);
            //只取当前所在库的商家
            if (onlyGetCurDb)
            {
                var supplierUsers = OnlyGetCurDbResult(fxUserId, list.Item2, true);
                return Tuple.Create(supplierUsers.Count, supplierUsers);
            }
            else
            {
                return list;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="status"></param>
        /// <param name="isPrePay"></param>
        /// <param name="onlyGetCurDb"></param>
        /// <param name="fields">只可以指定 t1 字段及SupplierUser字段</param>
        /// <param name="isIgnoreHideCancelUserSetting"></param>
        /// <param name="needEncryptAccount"></param>
        /// <returns></returns>
        public List<SupplierUser> GetAgentList(int fxUserId, AgentBingSupplierStatus? status = null,
            int? isPrePay = null, bool onlyGetCurDb = false, string fields = "",
            bool isIgnoreHideCancelUserSetting = false, bool needEncryptAccount = false)
        {
            var statusList = new List<int>();
            if (status != null)
                statusList = new List<int> { status.ToInt() };
            var list = _repository.GetAgentListV2(new List<int> { fxUserId }, statusList, isPrePay: isPrePay,
                fields: fields, isIgnoreHideCancelUserSetting: isIgnoreHideCancelUserSetting,
                needEncryptAccount: needEncryptAccount);

            //只取当前所在库的商家
            if (onlyGetCurDb)
                return OnlyGetCurDbResult(fxUserId, list, true);
            else
                return list;
        }

        public List<SupplierUser> GetAgentListV2(List<int> fxUserIds, List<int> statusList = null, bool getAll = false, int? isPrePay = null,bool needEncryptAccount = false)
        {
            return _repository.GetAgentListV2(fxUserIds, statusList, getAll, isPrePay: isPrePay, needEncryptAccount: needEncryptAccount);
        }

        /// <summary>
        /// 只取当前分区相关的数据（根据路径流）
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="list"></param>
        /// <param name="isGetAgent">true:获取我的商家；false:获取我的厂家</param>
        /// <returns></returns>
        public List<SupplierUser> OnlyGetCurDbResult(int fxUserId, List<SupplierUser> list, bool isGetAgent)
        {
            if (list == null && list.Any() == false)
                return list;

            var field = "DownFxUserId";
            if (isGetAgent)
                field = "UpFxUserId";

            var relatedFxUserIds = new PathFlowService().GetRelatedFxUserId(fxUserId, field);

            if (isGetAgent)
                list = list.Where(a => relatedFxUserIds.Contains(a.FxUserId)).ToList();
            else
            {
                //厂家数据源，只标记IsFilter
                list.ForEach(row =>
                {
                    if (relatedFxUserIds.Contains(row.SupplierFxUserId))
                    {
                        row.IsFilter = false;
                    }
                    else
                    {
                        row.IsFilter = true;
                    }
                });
            }

            return list;
        }

        /// <summary>
        /// 处理商家所在分区
        /// </summary>
        /// <param name="list"></param>
        /// <param name=""></param>
        public void ProcessDbArea(List<SupplierUser> list, Dictionary<string, List<SupplierUserNameModel>> agentDbs, Dictionary<string, List<SupplierUserNameModel>> agentDbClouds)
        {
            if (list == null || !list.Any() || agentDbs == null || agentDbClouds == null)
                return;

            var cloudDic = new Dictionary<string, string>();
            cloudDic.Add("pinduoduo", "拼多多");
            cloudDic.Add("jingdong", "京东");
            cloudDic.Add("toutiao", "抖店");

            list.ForEach(agent =>
            {
                if (agent.DbAreas == null)
                    agent.DbAreas = new List<DbArea>();

                //非精选平台，只要考虑云平台
                cloudDic.ToList().ForEach(dic =>
                {
                    var key = dic.Key;
                    if (agentDbClouds.ContainsKey(key) && agentDbClouds[key].Any(a => a.FxUserId == agent.FxUserId && a.DbCloudPlatform != "Alibaba"))
                    {
                        agent.DbAreas.Add(new DbArea { DbName = dic.Key, NickName = dic.Value });
                    }
                });

                //精选平台，考虑分区
                agentDbs.ToList().ForEach(dbArea =>
                {
                    dbArea.Value.Where(a => a.FxUserId == agent.FxUserId && a.DbCloudPlatform == "Alibaba").ToList().ForEach(subDb =>
                    {
                        if (!agent.DbAreas.Any(x => x.DbName == subDb.DbName))
                            agent.DbAreas.Add(new DbArea 
                            {
                                DbName = dbArea.Key, 
                                NickName = subDb.DbAreaNickName,
                                DbNameCode = DES.EncryptDES(dbArea.Key, CustomerConfig.LoginCookieEncryptKey),
                            });
                    });
                });

            });
        }

        /// <summary>
        /// 分页获取集合
        /// 返回元组格式,Item1(总数量),Item2(集合)
        /// </summary>
        /// <param name="key"></param>
        /// <param name="status"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public Tuple<int, List<SupplierUser>> GetAgentListByFinancialSettlement(int fxUserId, string key, AgentBingSupplierStatus? status, int pageIndex, int pageSize)
        {
            var list = _repository.GetAgentListByFinancialSettlement(fxUserId, key, status, pageIndex, pageSize);
            return list;
        }

        /// <summary>
        /// 查询厂家绑定关系信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public SupplierUser GetSupplierBindInfo(int id)
        {
            return _repository.GetSupplierBindInfo(id);
        }

        public List<SupplierUser> GetByFxUserId(int fxUserId, bool isSupplier,bool needEncryptAccount = false)
        {
            return _repository.GetByFxUserId(fxUserId, isSupplier, needEncryptAccount: needEncryptAccount);
        }

        /// <summary>
        /// 获取合作关系
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="selectFields">查询字段</param>
        /// <param name="isSupplier">是否厂家</param>
        /// <returns></returns>
        public List<SupplierUser> GetsByFxUserId(int fxUserId, string selectFields = "f.*, u.*", bool isSupplier = true)
        {
            return _repository.GetsByFxUserId(fxUserId, selectFields, isSupplier);
        }
        
        /// <summary>
        /// 根据厂家和商家ID查询关联关系
        /// </summary>
        /// <param name="fxUserIds">商家Id</param>
        /// <param name="supplierUserIds">厂家Id</param>
        /// <returns></returns>
        public List<SupplierUser> GetByFxIds(List<int> fxUserIds, List<int> supplierUserIds)
        {
            return _repository.GetByFxIds(fxUserIds, supplierUserIds);
        }

        /// <summary>
        /// 获取供应商关系
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="selectFields"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<SupplierUser> GetListByFxUserIds(List<int> fxUserIds, string selectFields = "*",
            string whereFieldName = "SupplierFxUserId")
        {
            return _repository.GetListByFxUserIds(fxUserIds, selectFields, whereFieldName);
        }
        /// <summary>
        /// 根据厂家和商家ID查询关联关系
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="supplierUserIds"></param>
        /// <returns></returns>
        public List<SupplierUser> GetSupplierUserAndFxUserByFxIds(List<int> fxUserIds, List<int> supplierUserIds)
        {
            return _repository.GetSupplierUserAndFxUserByFxIds(fxUserIds, supplierUserIds);
        }

        /// <summary>
        /// 修改关系状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="status">状态按枚举值为准</param>
        /// <param name="isUpdateMemberLevelCode">是否更新MemberLevelCode</param>
        public void Update(int id, AgentBingSupplierStatus status, bool isUpdateMemberLevelCode = false)
        {
            _repository.Update(id, status, isUpdateMemberLevelCode);
        }

        /// <summary>
        /// 修改代发地址信息
        /// </summary>
        /// <param name="_newData"></param>

        public bool UpdateSendInfo(SupplierUser _newData)
        {
            var result = _repository.UpdateSendInfo(_newData);
            if (result == true && (_newData.Status == AgentBingSupplierStatus.Binded || _newData.Status == AgentBingSupplierStatus.UnBind))
            {
                

            }
            return result;
        }

        /// <summary>
        /// 获取对应的厂家或商家信息
        /// </summary>
        /// <param name="fxUserId">厂家或商家Id</param>
        /// <param name="curUserId">当前用户Id</param>
        /// <param name="isSupplier">是否为厂家</param>
        public SupplierUser GetUserInfoByUserId(int fxUserId, int curUserId, bool isSupplier = false)
        {
            return _repository.GetUserInfoByUserId(fxUserId, curUserId, isSupplier);
        }

        /// <summary>
        /// 查询我的厂家的名称信息，可用于下拉框显示
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="isStatusOk">是否是状态正常的，默认true，仅筛选出状态正常的厂家名称信息</param>
        /// <returns></returns>
        public Dictionary<int, string> GetSupplierUserNames(int fxUserId, bool isStatusOk = true)
        {
            return _repository.GetSupplierUserNames(fxUserId, isStatusOk);
        }
        public Dictionary<int, string> GetAgentUserNames(int fxUserId, bool isStatusOk = true)
        {
            return _repository.GetAgentUserNames(fxUserId, isStatusOk);
        }

        public List<int> GetAgentUserId(int fxUserId, bool isStatusOk = true)
        {
            return _repository.GetAgentUserId(fxUserId, isStatusOk);
        }

        public int EditAgentRemark(int id, string remark)
        {
            return _repository.EditAgentRemark(id, remark);
        }

        public int EditSupplierRemark(int id, string remark)
        {
            return _repository.EditSupplierRemark(id, remark);
        }

        public List<SupplierUser> GetSupplierUserByIds(List<int> supplierFxUserIds, int fxUserId, int userType)
        {
            return _repository.GetSupplierUserByIds(supplierFxUserIds, fxUserId, userType);
        }
        public SupplierUser GetSupplierUserById(int supplierFxUserId, int fxUserId)
        {
            return _repository.GetSupplierUserById(supplierFxUserId, fxUserId);
        }
        public List<int> GetAvaliableSupplierId(int fxUserId, List<int> supplierIds)
        {
            return _repository.GetAvaliableSupplierId(fxUserId, supplierIds);
        }
        public List<SupplierRelationModule> GetUserRelations(int fxUserId, int rootFxUserId = 0, bool isAgent = true, int limitLv = 10)
        {
            return _repository.GetUserRelations(fxUserId, rootFxUserId, isAgent, limitLv);
        }

        public List<SupplierRelationModule> GetAgentIsShopPrice(int fxUserId, int rootFxUserId = 0)
        {
            var models = GetUserRelations(fxUserId, rootFxUserId, true);
            if (models.Any() == false)
                return models;

            var fxUserShopService = new FxUserShopService();
            var commonSettingService = new CommonSettingService();
            var agentIds = models.Select(x => x.FxUserId).Distinct().ToList();
            var agentShops = fxUserShopService.GetFxUserShopIds(agentIds);
            var salePriceSets = commonSettingService.GetSettingByShopIds("/FenFa/System/Config/IsSalePricePublic", agentShops.Select(x => x.ShopId).ToList());

            foreach (var s in agentShops)
            {
                var model = models.FirstOrDefault(x => x.FxUserId == s.FxUserId);
                if (model == null)
                    continue;
                model.IsShowSalePrice = salePriceSets.FirstOrDefault(x => x.ShopId == s.ShopId)?.Value.ToBool() ?? false;
            }
            return models;
        }

        public List<FxUserShopSupplierUserModel> GetUnbindSupplierUserByVersion(int version, bool isAgent = false) 
        {
            return _repository.GetUnbindSupplierUserByVersion(version, isAgent);
        }

        /// <summary>
        /// 获取用户关联的商家和厂家
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="supplierFxUserId"></param>
        /// <returns></returns>
        public SupplierUser GetByUser(int fxUserId, int supplierFxUserId)
        {
            return _repository.GetByUser(fxUserId, supplierFxUserId);
        }

        /// <summary>
        /// 置顶/取消置顶
        /// </summary>
        /// <param name="id">P_SupplierUser主键Id</param>
        /// <param name="istop">true:置顶,false:取消置顶</param>
        /// <returns></returns>
        public int UpdateTop(int id, bool istop)
        {
            var currentFxUserId = SiteContext.GetCurrentFxUserId();
            var supplierUser = _repository.Get(id);
            if (supplierUser.FxUserId != currentFxUserId && supplierUser.SupplierFxUserId != currentFxUserId)
            {
                throw new LogicException("您没有权限操作");
            }
            var isSupplier = supplierUser.SupplierFxUserId == currentFxUserId;

            // 设为置顶
            //var newTopStatus = 0;
            //if (istop)
            //{
            //    // TopStatus未设置过，判断IsTop。对方未设置过置顶，只设置自己的置顶
            //    if (supplierUser.TopStatus == 0 || (supplierUser.TopStatus == null && !supplierUser.IsTop))
            //    {
            //        newTopStatus = isSupplier ? 3 : 2;
            //    }
            //    // 对方已经设置过置顶的，加入当前用户改为双向置顶
            //    else if((isSupplier && supplierUser.TopStatus == 2) || (!isSupplier && supplierUser.TopStatus == 3))
            //    {
            //        newTopStatus = 1;
            //    }
            //}
            //// 取消置顶
            //else
            //{
            //    // TopStatus未设置过，判断IsTop 旧状态已经设过置顶的，根据身份取消自己的置顶
            //    if (supplierUser.TopStatus == 1 || (supplierUser.TopStatus == null && supplierUser.IsTop))
            //    {
            //        newTopStatus = isSupplier ? 2 : 3;
            //    }
            //    // 仅自己设置过置顶的，直接取消
            //    else if ((isSupplier && supplierUser.TopStatus == 3) || (!isSupplier && supplierUser.TopStatus == 2))
            //    {
            //        newTopStatus = 0;
            //    }
            //}

            return _repository.UpdateTopStatus(id,isSupplier, istop);
        }
        /// <summary>
        /// 获取关联的厂家（带SystemShopId）
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<SupplierUser> GetSuppliers(List<int> fxUserIds)
        {
            return _repository.GetSuppliers(fxUserIds);
        }

        /// <summary>
        /// 统计解绑中的合作关系数量
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns>我的厂家，我的商家</returns>
        public Tuple<int, int> GetSupplierUserStatInfo(int fxUserId)
        {
            return _repository.GetSupplierUserStatInfo(fxUserId);
        }

        /// <summary>
        /// 获取用户关联的商家和厂家用户Id
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<int> GetAgentOrSuplierIds(List<int> fxUserIds)
        {
            if (fxUserIds == null || !fxUserIds.Any())
                return new List<int>();
            return _repository.GetAgentOrSuplierIds(fxUserIds);
        }

        /// <summary>
        /// 获取用户关联的商家和厂家用户Id
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<int> GetAgentOrSupplierIds(int fxUserId,bool getAgent = false)
        {
            return _repository.GetAgentOrSupplierIds(fxUserId,getAgent);
        }

        public List<FxUserShop> GetAgentFxUserShopList(List<int> shopIds) 
        {
            return _repository.GetAgentFxUserShopList(shopIds);
        }

        /// <summary>
        /// 设置预付状态
        /// </summary>
        /// <param name="supplierFxUserId">厂家FxUserId</param>
        /// <param name="fxUserId">商家FxUserId</param>
        /// <param name="isPrePay">1:开启、其它值:关闭</param>
        /// <param name="systemShopId">当前用户的系统店铺Id</param>
        /// <param name="preCheck">是否要检查开启的前置条件，默认true</param>
        /// <returns></returns>
        public CheckResult SetPrePay(int supplierFxUserId, int fxUserId, int isPrePay, int systemShopId, bool preCheck = true)
        {
            var checkResult = new CheckResult();
            if (supplierFxUserId <= 0 || fxUserId <= 0)
            {
                checkResult.Message = "数据错误";
                return checkResult;
            }
            int? deliveryMode = null;
            //开启前置条件：已开通轻应用且在有代销商品
            if (isPrePay == 1 && preCheck)
            {
                var qingService = new QingService(supplierFxUserId);
                if (QingService.Instance.IsOpenQing == false)
                {
                    checkResult.Message = "QING_NOT_OPEN";
                    return checkResult;
                }
                var key = BusinessSettingKeys.SupplyBy1688.ShopBy1688;
                var shopId = new BusinessSettingsService().GetsByShopId(systemShopId, new List<string> { key }).FirstOrDefault()?.Value.ToInt() ?? 0;
                if (shopId <= 0)
                {
                    checkResult.Message = "NOT_SET_1688SHOP";
                    return checkResult;
                }
                if (new ProductFxService().IsHasDistributorProduct(shopId) == false)
                {
                    checkResult.Message = "NOT_HAS_DPRODUCT";
                    return checkResult;
                }

                //发货方式
                var settings = new BusinessSettingsService().GetsByCurrentUser(new List<string>
                    {
                        BusinessSettingKeys.SupplyBy1688.DeliveryMode
                    });
                deliveryMode = settings.FirstOrDefault(m => m.Key == BusinessSettingKeys.SupplyBy1688.DeliveryMode)?.Value.ToInt() ?? 0;
            }
            var result = _repository.SetPrePay(supplierFxUserId, fxUserId, isPrePay, deliveryMode);
            //刷新商家的缓存（预付状态更新触发点2）
            FxCaching.ForeRefeshCache(FxCachingType.Show1688Menu, fxUserId.ToString2());

            if (result >= 1)
            {
                //更新商家是否显示1688菜单的缓存
                if (isPrePay == 1)
                {
                    var agentSystemShopId = new FxUserShopService().GetFxUserIdMapping(new List<int> { fxUserId })?.FirstOrDefault()?.ShopId ?? 0;
                    //var _commonSettingService = new CommonSettingService();
                    //_commonSettingService.UpdateIsShow1688MenuCache(fxUserId);
                    //设置版本
                    new ShopService().SetAboutFx1688Version(fxUserId, agentSystemShopId);
                }

                //查询当前的发货模式
                if(deliveryMode == null)
                {
                    deliveryMode = Get($" WHERE SupplierFxUserId={supplierFxUserId} AND FxUserId={fxUserId}", null).FirstOrDefault()?.DeliveryMode;
                }
                //更新PurchaseOrderDeliveryMode
                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString() && deliveryMode != null)
                {
                    new PurchaseOrderDeliveryModeService().UpdateDeliveryModeWithAllDb(supplierFxUserId, deliveryMode.Value, fxUserId);
                }

                var key = BusinessSettingKeys.SupplyBy1688.PrepaidEffectiveRule;
                var prepaidEffectiveRule = new BusinessSettingsService().GetsByShopId(systemShopId, new List<string> { key }).FirstOrDefault()?.Value.ToInt() ?? 0;

                var changeModel = new ChangePrePayForCheckModel { IsPrePay = isPrePay, PrepaidEffectiveRule = prepaidEffectiveRule, SupplierFxUserId = supplierFxUserId, FxUserId = fxUserId };
                checkResult.ReturnData = changeModel;
                //预付状态变化，处理订单审批
                ChangePrePayProcessCheck(changeModel);

                //添加日志
                var model = new PrepayStatusChangeRecord()
                {
                    FxUserId = fxUserId,
                    SupplierFxUserId = supplierFxUserId,
                    PrepayStatus = isPrePay,
                    CreateFxUserId = supplierFxUserId,
                    CreateTime = DateTime.Now,
                };
                new PrepayStatusChangeRecordService().Add(model);

                if (deliveryMode.HasValue)
                {
                    var dMode = new DeliveryModeChangeRecord()
                    {
                        FxUserId = fxUserId,
                        SupplierFxUserId = supplierFxUserId,
                        DeliveryModel = deliveryMode.Value,
                        CreateFxUserId = supplierFxUserId,
                        CreateTime = DateTime.Now,
                    };
                    new DeliveryModeChangeRecordService().Add(dMode);
                }
            }
            else
            {
                checkResult.Message = "更新失败";
                return checkResult;
            }
            checkResult.Success = true;
            return checkResult;
        }

        /// <summary>
        /// 设置发货方式
        /// </summary>
        /// <param name="supplierFxUserId">厂家FxUserId</param>
        /// <param name="fxUserId">商家FxUserId</param>
        /// <param name="deliveryMode">0发下游商家订单；1发1688订单</param>
        /// <returns></returns>
        public CheckResult SetDeliveryMode(int supplierFxUserId, int fxUserId, int deliveryMode)
        {
            var checkResult = new CheckResult();
            if (supplierFxUserId <= 0 || fxUserId <= 0)
            {
                checkResult.Message = "数据错误";
                return checkResult;
            }
            var result = _repository.SetDeliveryMode(supplierFxUserId, fxUserId, deliveryMode);

            if (result >= 1)
            {
                var dMode = new DeliveryModeChangeRecord()
                {
                    FxUserId = fxUserId,
                    SupplierFxUserId = supplierFxUserId,
                    DeliveryModel = deliveryMode,
                    CreateFxUserId = supplierFxUserId,
                    CreateTime = DateTime.Now,
                };
                new DeliveryModeChangeRecordService().Add(dMode);

                //更新PurchaseOrderDeliveryMode
                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                {
                    new PurchaseOrderDeliveryModeService().UpdateDeliveryModeWithAllDb(supplierFxUserId, deliveryMode, fxUserId);
                }
            }
            else
            {
                checkResult.Message = "更新失败";
                return checkResult;
            }
            checkResult.Success = true;
            return checkResult;
        }

        /// <summary>
        /// 设置采购金状态
        /// </summary>
        /// <param name="supplierFxUserId">厂家FxUserId</param>
        /// <param name="isOpen"></param>
        /// <returns></returns>
        public int SetOpenWangshangPay(int supplierFxUserId, bool isOpen)
        {
            return _repository.SetOpenWangshangPay(supplierFxUserId, isOpen);
        }
        /// <summary>
        /// 预付状态统计
        /// </summary>
        /// <param name="supplierFxUserId">厂家FxUserId</param>
        /// <returns></returns>
        public List<IsPrePayCountModel> GetPrePayCount(int supplierFxUserId)
        {
            if (supplierFxUserId <= 0)
                return new List<IsPrePayCountModel>();
            return _repository.GetPrePayCount(supplierFxUserId);
        }

        /// <summary>
        /// 全局预付设置显示结果
        /// </summary>
        /// <param name="supplierFxUserId">厂家FxUserId</param>
        /// <returns>-1指定；0都不需要预付；1都需要预付</returns>
        public int GetPrePayConfigResult(int supplierFxUserId)
        {
            var result = -1;
            var prePayCountResult = GetPrePayCount(supplierFxUserId);
            var nullCount = prePayCountResult.FirstOrDefault(a => a.IsPrePay == null)?.AgentCount ?? 0;
            var openedCount = prePayCountResult.FirstOrDefault(a => a.IsPrePay == true)?.AgentCount ?? 0;
            var closedCount = prePayCountResult.FirstOrDefault(a => a.IsPrePay == false)?.AgentCount ?? 0;

            //null当作未开启
            closedCount += nullCount;

            if (closedCount == 0 && openedCount == 0)
                return result;

            if (openedCount > 0 && closedCount == 0)
                result = 1;
            else if (closedCount > 0 && openedCount == 0)
                result = 0;

            return result;
        }

        /// <summary>
        /// 获取已开启预付的相关厂家
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<SupplierUser> GetOpenedPrePaySupplierFxUserIds(List<int> fxUserIds)
        {
            if (fxUserIds == null || !fxUserIds.Any())
                return new List<SupplierUser>();
            return _repository.GetOpenedPrePaySupplierFxUserIds(fxUserIds);
        }

        /// <summary>
        /// 获取已开启预付的相关商家
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<int> GetOpenedPrePayAgentFxUserIds(int supplierFxUserId)
        {
            return _repository.GetOpenedPrePayAgentFxUserIds(supplierFxUserId);
        }

        /// <summary>
        /// 预付状态变化，处理订单审批
        /// </summary>
        /// <param name="changeModel"></param>
        public void ChangePrePayProcessCheck(ChangePrePayForCheckModel changeModel)
        {
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                //所有分区循环执行
                if (SiteContext.Current.CurrentDbAreaConfig != null)
                {
                    var currentdb = SiteContext.Current.CurrentDbConfig;
                    foreach (var dbconfig in SiteContext.Current.CurrentDbAreaConfig)
                    {
                        //【执行】
                        SiteContext.Current.CurrentDbConfig = dbconfig;
                        ExeChangePrePayProcessCheck(changeModel);
                    }
                    SiteContext.Current.CurrentDbConfig = currentdb;
                }
                return;
            }
            else if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
            {
                //拼多多增加旧库的处理
                //商家所在库是旧库且不和当前同一库，先执行旧库，再往下执行厂家所在库
                var currentdb = SiteContext.Current.CurrentDbConfig;
                var agentDbConfigModel = new DbConfigRepository().GetConfigFxUserId(changeModel.FxUserId, CloudPlatformType.Pinduoduo.ToString());
                if (agentDbConfigModel != null && agentDbConfigModel.DbConfig.FromFxDbConfig == 0 && currentdb != null && currentdb.DbConfig.DbNameConfigId != agentDbConfigModel.DbConfig.DbNameConfigId)
                {
                    //旧库【执行】
                    SiteContext.Current.CurrentDbConfig = agentDbConfigModel;
                    ExeChangePrePayProcessCheck(changeModel);
                    //切回
                    SiteContext.Current.CurrentDbConfig = currentdb;
                }
            }

            ExeChangePrePayProcessCheck(changeModel);
        }

        /// <summary>
        /// 【执行】预付状态变化，处理订单审批
        /// </summary>
        /// <param name="changeModel"></param>
        public void ExeChangePrePayProcessCheck(ChangePrePayForCheckModel changeModel)
        {
            //需撤回订单，PrepaidEffectiveRule为0时，默认为需撤回订单
            if (changeModel.IsPrePay == 1 && (changeModel.PrepaidEffectiveRule == 2 || changeModel.PrepaidEffectiveRule == 0))
            {
                IObserveCheckOrder objserve = new UpdateCheckOrderWhileOpenPerPay();
                objserve.Process(changeModel.SupplierFxUserId, null, new List<int> { changeModel.FxUserId });
            }
            else if (changeModel.IsPrePay == 0)
            {
                IObserveCheckOrder objserve = new UpdateCheckOrderWhileClosePerPay();
                objserve.Process(changeModel.SupplierFxUserId, null, new List<int> { changeModel.FxUserId });
            }
        }

        public List<SupplierUser> GetSupplierUsers(int fxUserId, List<int> supplierFxUserIds, List<string> fields = null)
        {
            return _repository.GetSupplierUsers(fxUserId, supplierFxUserIds, fields);
        }

        ///// <summary>
        ///// 是否被开启过预付模式
        ///// </summary>
        ///// <param name="fxUserId"></param>
        ///// <returns></returns>
        //public bool IsOpenedPrePay(int fxUserId)
        //{
        //    return _repository.IsOpenedPrePay(fxUserId);
        //}


        /// <summary>
        /// 获取对指定商家已开启过预付的厂家
        /// </summary>
        /// <param name="agentFxUserId"></param>
        /// <returns></returns>
        public List<int> GetOpenedPrePaySupplierFxUserIds(int agentFxUserId)
        {
            return _repository.GetOpenedPrePaySupplierFxUserIds(agentFxUserId);
        }

        /// <summary>
        /// 被开启预付的商家Id
        /// </summary>
        /// <param name="agentFxUserIds"></param>
        /// <returns></returns>
        public List<int> GetOpenedPrePayFxUserIdFromCache(List<int> agentFxUserIds)
        {
            bool isGetUseRedis = false;
            var key = "FxSystem:OpenedPrePayAgentFxUserIds";
            var result = new List<int>();
            try
            {
                result = RedisHelper.Get<List<int>>(key) ?? new List<int>();
                isGetUseRedis = true;
            }
            catch (Exception ex)
            {
                isGetUseRedis = false;
                Log.WriteError($"GetOpenedPrePayFxUserIdFromCache，RedisHelper.Get异常：{ex}");
            }

            //1.缓存有数据，直接过滤返回结果
            if (result != null && result.Any())
            {
                result = result.Where(a => agentFxUserIds.Contains(a)).ToList();
                return result;
            }

            //2.缓存无数据，从数据库查出所有已开启预付的商家
            result = _repository.GetAllOpenedPrePayAgentFxUserId();
            if (isGetUseRedis && result.Any())
            {
                //保存到缓存
                RedisHelper.Set(key, result, expireSeconds: 300);
            }

            //3.过滤返回结果
            result = result.Where(a => agentFxUserIds.Contains(a)).ToList();
            return result;
        }

        public new int Add(SupplierUser model)
        {
            int id = 0;
            if(model.Status == AgentBingSupplierStatus.UnBindFail)//状态为已经取消
            {
                model.UpdateTime = DateTime.Now;
                model.Status = AgentBingSupplierStatus.Apply;  //绑定申请中]
                id = _repository.Update(model).ToInt();
            }
            else
            {
                model.CreateTime = DateTime.Now;
                if (model.SupplierType != "Virtual") model.Status = AgentBingSupplierStatus.Apply;  //绑定申请中
                id = _repository.Add(model);
            }
            //检查是否同库
            try
            {
                TrySetSupplierUserAsSameDb(model.SupplierFxUserId, model.FxUserId);
            }
            catch (Exception ex)
            {
                ExceptionLogDataEventTrackingService.Instance.WriteLog(ex, "尝试将商家厂家设为一样的库时发生异常：" + ex.Message);
            }

            return id;
        }

        public bool TrySetSupplierUserAsSameDb(int supplierFxUserId, int fxUserId)
        {
            //检查是否在同一个库
            //检查是都有数据：是否有绑定过店铺、绑定过其他厂商家、是否存在路径信息
            //以没有数据的一方为准，切换数据库
            //记录日志
            var fxDbConfigRp = new FxDbConfigRepository();
            var fxDbConfigs = fxDbConfigRp.GetTouTiaoFxDbConfigs(new List<int> { supplierFxUserId, fxUserId });
            if (fxDbConfigs == null || fxDbConfigs.Any() == false || fxDbConfigs.Count() == 1 || fxDbConfigs.GroupBy(x => x.DbNameConfigId).Count() == 1)
                return false;
            var fxUserDbConfig = fxDbConfigs.FirstOrDefault(x => x.FxUserId == fxUserId);
            var supplierDbConfig = fxDbConfigs.FirstOrDefault(x => x.FxUserId == fxUserId);
            var isFxUserHasData = IsExisitTouTiaoData(fxUserId, fxUserDbConfig);
            //var isSupplierFxUserHasData = IsExisitTouTiaoData(supplierFxUserId, supplierDbConfig);
            //优先将商家挪到厂家库，先判断商家是否有数据
            //商家没有数据
            if (isFxUserHasData == false)
            {
                fxDbConfigRp.UpdateTouTiaoFxDbConfigDbNameConfigId(fxUserId, supplierDbConfig.DbNameConfigId, fxUserDbConfig.DbNameConfigId);
                BusinessLogDataEventTrackingService.Instance.WriteLog(new List<Data.Model.LogModel.BusinessLogModel> { new Data.Model.LogModel.BusinessLogModel {
                    BatchId = "",
                    PlatformType = CustomerConfig.CloudPlatformType,
                    MethodName = "SupplierUserService.TrySetSupplierUserAsSameDb",
                    CreatorId = fxUserId,
                    BusinessType = BusinessTypes.SupplierUser.ToString(),
                    Remark = $"商家ID：{fxUserId},厂家ID{supplierFxUserId}，商家数据库ID:{fxUserDbConfig.DbNameConfigId}，厂家数据库ID:{supplierDbConfig.DbNameConfigId}",
                    Content = $"尝试将商家厂家设为一样的库：【商家挪到厂家库，商家无数据】，商家ID：{fxUserId},厂家ID{supplierFxUserId},isFxUserHasData:{isFxUserHasData},商家oldDbNameConfigId:{fxUserDbConfig.DbNameConfigId},商家newDbNameConfigId:{supplierDbConfig.DbNameConfigId}",
                }}); ;
            }
            //厂家没有数据
            else if(IsExisitTouTiaoData(supplierFxUserId, supplierDbConfig) == false)
            {
                fxDbConfigRp.UpdateTouTiaoFxDbConfigDbNameConfigId(supplierFxUserId, fxUserDbConfig.DbNameConfigId, supplierDbConfig.DbNameConfigId);
                BusinessLogDataEventTrackingService.Instance.WriteLog(new List<Data.Model.LogModel.BusinessLogModel> { new Data.Model.LogModel.BusinessLogModel {
                    BatchId = "",
                    PlatformType = CustomerConfig.CloudPlatformType,
                    MethodName = "SupplierUserService.TrySetSupplierUserAsSameDb",
                    CreatorId = fxUserId,
                    BusinessType = BusinessTypes.SupplierUser.ToString(),
                    Remark = $"商家ID：{fxUserId},厂家ID{supplierFxUserId}，商家数据库ID:{fxUserDbConfig.DbNameConfigId}，厂家数据库ID:{supplierDbConfig.DbNameConfigId}",
                    Content = $"尝试将商家厂家设为一样的库：【厂家挪到商家库，厂家无数据】，商家ID：{fxUserId},厂家ID{supplierFxUserId},isFxUserHasData:{isFxUserHasData},厂家oldDbNameConfigId:{supplierDbConfig.DbNameConfigId},厂家newDbNameConfigId:{fxUserDbConfig.DbNameConfigId}",
                }});
            }
            else
            {
                BusinessLogDataEventTrackingService.Instance.WriteLog(new List<Data.Model.LogModel.BusinessLogModel> { new Data.Model.LogModel.BusinessLogModel {
                    BatchId = "",
                    PlatformType = CustomerConfig.CloudPlatformType,
                    MethodName = "SupplierUserService.TrySetSupplierUserAsSameDb",
                    CreatorId = fxUserId,
                    BusinessType = BusinessTypes.SupplierUser.ToString(),
                    Remark = $"商家ID：{fxUserId},厂家ID{supplierFxUserId}，商家数据库ID:{fxUserDbConfig.DbNameConfigId}，厂家数据库ID:{supplierDbConfig.DbNameConfigId}",
                    Content = $"尝试将商家设为和尝试将商家厂家设为一样的库：【不变更】，fxUserId：{fxUserId},supplierFxUserId{supplierFxUserId}，厂家商家都有数据，不做变更"
                }});
            }
            return true;
        }

        public bool IsExisitTouTiaoData(int fxUserId,FxDbConfig dbConfigModel)
        {
            //检查是否有数据：是否有绑定过店铺、绑定过其他厂商家、是否存在路径信息
            var fxUserShopRp = new FxUserShopRepository();
            var hasShop = fxUserShopRp.CheckIsAnyTouTiaoShop(fxUserId);
            if (hasShop)
                return true;
            var hasOtherSupplierUser = _repository.IsAnySupplierUsers(fxUserId);
            if(hasOtherSupplierUser)
                return true;
            //检查数据
            var dbApi = DbApiAccessUtility.GetApiDb(dbConfigModel.DbNameConfigId,"TouTiao");
            var sql = $"SELECT TOP 1 1 FROM dbo.PathFlowNode WITH(NOLOCK) WHERE FxUserId=@fxUserId";
            var hasPathFlowNode = dbApi.Query<int>(sql, new { fxUserId })?.FirstOrDefault() == 1;
            if(hasPathFlowNode)
                return true;
            return false;
        }

        /// <summary>
        /// 统计开启了预付商家的情况
        /// </summary>
        /// <returns></returns>
        public List<AgentFor1688Model> StatFor1688Agent()
        {
            var fxUserShopRp = new FxUserShopRepository();
            var sql = $@"SELECT su.FxUserId AS AgentFxUserId,uf.Mobile AS AgentMobile,su.SupplierFxUserId,uf2.Mobile AS SupplierMobile,su.OpenPrePayTime  FROM P_SupplierUser su WITH(NOLOCK)
INNER JOIN P_UserFx uf WITH(NOLOCK) ON uf.[Id] =su.FxUserId 
INNER JOIN P_UserFx uf2 WITH(NOLOCK) ON uf2.[Id] =su.SupplierFxUserId
WHERE su.IsPrePay =1 ORDER BY su.FxUserId";

            var db = fxUserShopRp.DbConnection;
            var result = db.Query<AgentFor1688Model>(sql).ToList();
            if (result == null || result.Any() == false)
                return new List<AgentFor1688Model>();

            var agentFxUserIds = result.Select(a => a.AgentFxUserId).Distinct().ToList();
            //查询相关业务库
            var dbConfigModels = new DbConfigRepository().GetListByFxUserIds(agentFxUserIds, new List<string> { CloudPlatformType.Alibaba.ToString(), CloudPlatformType.TouTiao.ToString() });

            dbConfigModels.GroupBy(a => a.ConnectionString).ToList().ForEach(dbGroup => {
                var curDb = dbGroup.First();
                var curLocation = curDb.DbServer.Location;
                var curFxUserIds = dbGroup.ToList().Select(a => a.DbConfig.UserId).Distinct().ToList();
                var curModels = result.Where(a => curFxUserIds.Contains(a.AgentFxUserId)).ToList();

                //同云
                if(curLocation == CustomerConfig.CloudPlatformType)
                {
                    var service = new PathFlowReferenceService(dbGroup.Key);
                    var gDb = service.baseRepository.DbConnection;

                    curModels.ForEach(model => 
                    {
                        var sql2 = string.Empty;
                        if (model.IsBindProductTo1688Supplier == false)
                        {
                            sql2 = $@"SELECT TOP 1 1 FROM PathFlowReference pfr WITH(NOLOCK)
INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode =pfr.PathFlowCode
WHERE pfn.FxUserId={model.AgentFxUserId} AND pfn.DownFxUserId ={model.SupplierFxUserId} AND pfr.Status=0 ";
                            var isBindProductTo1688Supplier = gDb.QueryFirstOrDefault<int>(sql2) == 1;
                            if (isBindProductTo1688Supplier)
                                model.IsBindProductTo1688Supplier = isBindProductTo1688Supplier;
                        }

                        if (model.IsBindProductToOtherSupplier == false)
                        {
                            sql2 = $@"SELECT TOP 1 1 FROM PathFlowReference pfr WITH(NOLOCK)
INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode =pfr.PathFlowCode
WHERE pfn.FxUserId={model.AgentFxUserId} AND pfn.DownFxUserId>0 AND pfn.DownFxUserId <>{model.SupplierFxUserId} AND pfr.Status=0 ";
                            var isBindProductToOtherSupplier = gDb.QueryFirstOrDefault<int>(sql2) == 1;
                            if (isBindProductToOtherSupplier)
                                model.IsBindProductToOtherSupplier = isBindProductToOtherSupplier;
                        }

                    });
                }
                else
                {
                    var apiDbConfig = new ApiDbConfigModel(curLocation, curLocation, curDb.DbNameConfig.Id);
                    var _apiAccessUtility = new DbApiAccessUtility(apiDbConfig);

                    curModels.ForEach(model =>
                    {
                        var sql2 = string.Empty;
                        if (model.IsBindProductTo1688Supplier == false)
                        {
                            sql2 = $@"SELECT TOP 1 1 FROM PathFlowReference pfr WITH(NOLOCK)
INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode =pfr.PathFlowCode
WHERE pfn.FxUserId={model.AgentFxUserId} AND pfn.DownFxUserId ={model.SupplierFxUserId} AND pfr.Status=0 ";
                            var isBindProductTo1688Supplier = _apiAccessUtility.ExecuteScalar(sql2).ToString2() == "1";
                            if (isBindProductTo1688Supplier)
                                model.IsBindProductTo1688Supplier = isBindProductTo1688Supplier;
                        }

                        if (model.IsBindProductToOtherSupplier == false)
                        {
                            sql2 = $@"SELECT TOP 1 1 FROM PathFlowReference pfr WITH(NOLOCK)
INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode =pfr.PathFlowCode
WHERE pfn.FxUserId={model.AgentFxUserId} AND pfn.DownFxUserId>0 AND pfn.DownFxUserId <>{model.SupplierFxUserId} AND pfr.Status=0 ";
                            var isBindProductToOtherSupplier = _apiAccessUtility.ExecuteScalar(sql2).ToString2() == "1";
                            if (isBindProductToOtherSupplier)
                                model.IsBindProductToOtherSupplier = isBindProductToOtherSupplier;
                        }

                    });
                }
            });

            return result;
        }


        /// <summary>
        /// 模糊查询厂商fxUserId
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public List<int> GetSupplierListId(int fxUserId,string key)
        {
            return _repository.GetSupplierListId(fxUserId,key,AgentBingSupplierStatus.Binded);
        }

        /// <summary>
        /// 模糊查询商家fxUserId
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="key"></param>
        /// <returns></returns>

        public List<int> GetAgentListId(int fxUserId, string key)
        {
            return _repository.GetAgentListId(fxUserId, key, AgentBingSupplierStatus.Binded);
        }

        /// <summary>
        /// 校验分销店铺是否为空
        /// </summary>
        public Tuple<bool,string> CheckWangShangPayIsOpen(int fxUserId, Shop shop = null)
        {
            var shopRepository = new ShopRepository();

            //查找用户轻应用收单店铺信息
            if(shop == null)
            {
                shop = shopRepository.GetAlibabaDefalutQingShopsByFxUserId(fxUserId);
                if (shop == null)
                    throw new LogicException("检查供应商采购金支付开通失败，1688收单店铺为空或者用户未设置1688收单店铺");
            }

            var service = new AlibabaQingPlatformService(shop);
            var hasOpenWangShangPay = service.HasOpenWangShangPay();

            //更新网商支付开通状态
            var isOpen = hasOpenWangShangPay.Item1;
            _repository.UpdateWangShangPay(isOpen, fxUserId);

            return hasOpenWangShangPay;
        }

        /// <summary>
        /// 批量检查、更新所有店铺网商支付店铺状态，用于采购金支付首次上线批量校验开通状态
        /// </summary>
        public void CheckeAllAlibabaShopWangShangPay()
        {
            ShopRepository shopRepository = new ShopRepository();

            var qingAliShops = shopRepository.GetAllAlibabaDefalutQingShops();
            int pageSize = 50;
            int pageTotal = (int)System.Math.Ceiling(qingAliShops.Count / (double)pageSize);

            for (int i = 0; i < pageTotal; i++)
            {
                var checkShops = qingAliShops.Skip(i * pageSize).Take(pageSize);
                System.Collections.Concurrent.ConcurrentBag<Tuple<Shop, bool>> tuples = new System.Collections.Concurrent.ConcurrentBag<Tuple<Shop, bool>>();
                Parallel.ForEach(checkShops, new ParallelOptions(){ MaxDegreeOfParallelism = 5 },shop =>
                {
                    var service = new AlibabaQingPlatformService(shop);

                    try
                    {
                        if (service.Ping() == true)
                        {
                            var hasOpenWangPay = service.HasOpenWangShangPay();
                            tuples.Add(Tuple.Create(shop, hasOpenWangPay.Item1));
                        }
                        else
                        {
                            tuples.Add(Tuple.Create(shop, false));
                        }
                    }
                    catch(Exception ex)
                    {
                        Log.WriteError($"店铺【{shop.NickName}】【shopId:{shop.Id}】检查店铺网商支付状态请求接口异常：{ex.Message}");
                    }
                });

                foreach (var item in tuples.ToLookup(x => x.Item2))
                {
                    var fxUserIds = item.Select(x => x.Item1.FxUserIds).Distinct().ToList();
                    _repository.UpdateWangShangPay(item.Key, fxUserIds);
                }
            }
        }

        #region 担保交易申请
        /// <summary>
        /// 担保交易申请
        /// </summary>
        /// <param name="supplierFxUserId"></param>
        /// <param name="fxUserId"></param>
        /// <param name="applyPrepayStatus"></param>
        /// <returns></returns>
        public bool SetApplyPrepay(int supplierFxUserId, int fxUserId, int applyPrepayStatus)
        {
            if (supplierFxUserId <= 0 || fxUserId <= 0) return false;
            var result = _repository.SetApplyPrepay(supplierFxUserId, fxUserId, applyPrepayStatus);
            return true;
        }

        /// <summary>
        /// 担保交易确认
        /// </summary>
        /// <param name="supplierFxUserId"></param>
        /// <param name="fxUserId"></param>
        /// <param name="isPrePay"></param>
        /// <returns></returns>
        public bool SetApprovePrePay(int supplierFxUserId, int fxUserId, int? isPrePay, bool isKeep = false)
        {
            if (supplierFxUserId <= 0 || fxUserId <= 0) return false;
            var result = _repository.SetApprovePrePay(supplierFxUserId, fxUserId, isPrePay, isKeep);
            return true;
        }

        public List<SupplierUser> GetSupplierConfirm(int fxUserId,DateTime? lastTipDateTime, bool isCurrentDay)
        {
            return _repository.GetSupplierConfirm(fxUserId, lastTipDateTime, isCurrentDay);
        }

        public List<SupplierUser> GetSupplierReject(int fxUserId, DateTime? lastTipDateTime, bool isCurrentDay)
        {
            return _repository.GetSupplierReject(fxUserId, lastTipDateTime, isCurrentDay);
        }

        public List<SupplierUser> GetSupplierStartB(int fxUserId, DateTime? lastTipDateTime, bool isCurrentDay)
        {
            return _repository.GetSupplierStartB(fxUserId, lastTipDateTime, isCurrentDay);
        }

        public List<SupplierUser> GetBusineApply(int fxUserId, DateTime? lastTipDateTime, bool isCurrentDay)
        {
            return _repository.GetBusineApply(fxUserId, lastTipDateTime, isCurrentDay);
        }

        public List<SupplierUser> GetSupplierStartS(int fxUserId, DateTime? lastTipDateTime, bool isCurrentDay)
        {
            return _repository.GetSupplierStartS(fxUserId, lastTipDateTime, isCurrentDay);
        }
        #endregion

        /// <summary>
        /// 用户的厂家是否在白名单里面
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool IsSupplierInWhitelist(int fxUserId)
        {
            var csService = new CommonSettingService();
            var configWhiteList = csService.Get("/ErpWeb/1688Supply/CanApplyPrepay/Supplier/FxUserIds", 0)?.Value?.Split(',');
            if (configWhiteList == null || configWhiteList.Any() == false)
                return false;

            var supplierIds = _repository.GetSupplierFxUserId(fxUserId);
            if(supplierIds == null || supplierIds.Any() == false)
                return false;
            if(supplierIds.Any(x=> configWhiteList.Contains($"{x}")))
                return true;
            return false;
        }

        /// <summary>
        /// 同步货盘
        /// </summary>
        /// <param name="user"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        public bool TriggerSyncSupplierUser(SupplierUser user, AgentBingSupplierStatus? status = null)
        {
            
            var result = false;
            try
            {
                if (status != null)
                {
                    user.Status = status.Value;
                }
                var supplierService = new UserSupplierStatusService();
                var supplierUser = supplierService.GetByFxUserId(user.FxUserId, user.SupplierFxUserId);

                if (supplierUser == null)
                {
                    //新增
                    var addModel = new UserSupplierStatus();
                    addModel.RemarkName = user.RemarkName;
                    addModel.SupplierFxUserId = user.SupplierFxUserId;
                    addModel.FxUserId = user.FxUserId;
                    addModel.Status = user.Status;
                    addModel.Remark = user.Remark;
                    addModel.CreateTime = user.CreateTime;
                    addModel.SourceId = user.Id;
                    addModel.MemberLevelCode = user.MemberLevelCode;
                    supplierService.Add(addModel);
                    result = true;
                }
                else
                {
                    // 更新
                    supplierUser.RemarkName = user.RemarkName;
                    supplierUser.SupplierFxUserId = user.SupplierFxUserId;
                    supplierUser.FxUserId = user.FxUserId;
                    supplierUser.Status = user.Status;
                    supplierUser.Remark = user.Remark;
                    supplierUser.CreateTime = user.CreateTime;
                    supplierUser.SourceId = user.Id;
                    supplierUser.MemberLevelCode = user.MemberLevelCode;
                    supplierService.Update(supplierUser);
                    result = true;
                }
            }
            catch (Exception ex)
            {
                result = false;
                Log.WriteError($"同步货盘厂家商家绑定关系失败，失败原因：{ex.ToJson()}");
            }
            return result;
        }

        /// <summary>
        /// 同步货盘(绑定状态)
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="targetUserId"></param>
        /// <param name="userType">0 商家、1 厂家</param>
        /// <param name="status"></param>
        /// <returns></returns>
        public bool TriggerSyncSupplierUserStatu(
            int fxUserId, 
            int targetUserId, 
            int userType,
            AgentBingSupplierStatus status)
        {
            var result = false;
            try
            {
                var supplierService = new UserSupplierStatusService();
                UserSupplierStatus supplierUser = null;
                SupplierUser user = null;

                // 当前是厂家
                if (userType == 1)
                {
                    supplierUser = supplierService.GetByFxUserId(targetUserId, fxUserId);
                    user = _repository.GetByUser(targetUserId, fxUserId);
                }
                // 当前是商家
                else
                {
                    supplierUser = supplierService.GetByFxUserId(fxUserId, targetUserId);
                    user = _repository.GetByUser(fxUserId, targetUserId);
                }

                if (user == null)
                {
                    Log.WriteError($"同步货盘厂家商家绑定关系状态失败，失败原因：未找到绑定关系");
                    return false;
                }

                if (supplierUser == null)
                {
                    //新增
                    var addModel = new UserSupplierStatus();
                    addModel.RemarkName = user.RemarkName;
                    addModel.SupplierFxUserId = user.SupplierFxUserId;
                    addModel.FxUserId = user.FxUserId;
                    addModel.Status = user.Status;
                    addModel.Remark = user.Remark;
                    addModel.CreateTime = user.CreateTime;
                    addModel.SourceId = user.Id;
                    addModel.MemberLevelCode = null;
                    supplierService.Add(addModel);
                    result = true;
                }
                else
                {
                    // 更新
                    supplierUser.Status = status;
                    supplierUser.SourceId = user.Id;
                    supplierUser.MemberLevelCode = null;
                    supplierService.Update(supplierUser);
                    result = true;
                }
            }
            catch (Exception ex)
            {
                result = false;
                Log.WriteError($"同步货盘厂家商家绑定关系状态失败，失败原因：{ex.ToJson()}");
            }
            return result;
        }
        /// <summary>
        /// 用户是否绑定了指定的商家
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="supplierFxUserId"></param>
        /// <returns></returns>
        public bool IsBindSupplierUser(int fxUserId,int supplierFxUserId)
        {
            return _repository.IsBindSupplierUser(fxUserId,supplierFxUserId);
        }

        /// <summary>
        /// 获取商家等级
        /// </summary>
        /// <param name="agentFxUserIds"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<SupplierUser> GetAgentLevelByIds(List<int> agentFxUserIds, int fxUserId)
        {
            return _repository.GetAgentLevelByIds(agentFxUserIds, fxUserId);
        }


        /// <summary>
        /// 获取当前时间
        /// </summary>
        /// <returns></returns>
        public DateTime GetNowTime()
        {
            var res = _repository.GetNowTime();
            return res;
        }

        public List<SupplierUser> GetSuppliersByFxUserId(int fxUxerId, string selectFields = "f.*", bool isSupplier = true) {
            return _repository.GetSuppliersByFxUserId(fxUxerId, selectFields, isSupplier);
        }
    }
}
