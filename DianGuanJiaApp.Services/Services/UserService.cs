using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Services
{

    public partial class UserService : BaseService<Data.Entity.User>
    {
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private UserRepository repository = new UserRepository();
        private ShopRepository shopRepository = new ShopRepository();
        private UserVerificationCodeRepository verificationCodeRepository = new UserVerificationCodeRepository();

        public UserService()
        {
            _baseRepository = new BaseRepository<User>(CustomerConfig.ConfigureDbConnectionString);
        }

        /// <summary>
        /// 用户注册
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Tuple<User, Shop> Register(UserRegisterModel model)
        {
            //a.添加用户信息（需判断手机号是否有注册过，微信同理）
            User user = null;
            if (!string.IsNullOrEmpty(model.Mobile))
            {
                user = repository.GetByMobile(model.Mobile);
                if (user != null)
                    throw new LogicException("该手机号码已经注册过了，请不要重复注册。", "Has_Registered");
            }
            else if (!string.IsNullOrEmpty(model.WxOpenId))
            {
                user = repository.GetByWxOpenId(model.WxOpenId);
                if (user != null)
                    throw new LogicException("该微信号已经注册过了，请不要重复注册。", "Has_Registered");
            }
            //b.添加一个线下店（或叫微商店）、并与用户账号进行关联
            var configureDb = repository.GetConfigureDb();
            var now = DateTime.Now;
            user = new User
            {
                Mobile = model.Mobile,
                WxOpenId = model.WxOpenId,
                Type = model.From?.ToLower() == "iswd" ? 1 : 0, //判断是网点还是微商用户
                CreateTime = now,
                BrowserReferrer = model.BrowserReferrer
            };
            if (!string.IsNullOrEmpty(model.Password))
                user.Password = DES.EncryptUrl(model.Password, CustomerConfig.LoginCookieEncryptKey);
            if (!string.IsNullOrEmpty(model.WxName))
                user.NickName = model.WxName;
            user.Id = repository.Add(user);
            var expiredTime = now.AddDays(15);
            var platformType = PlatformType.Offline.ToString();
            //添加店铺及其关联信息
            if (string.IsNullOrEmpty(model.Token))
            {
                var shopId = string.IsNullOrEmpty(user.Mobile) ? user.WxOpenId : user.Mobile;
                var shop = new Shop
                {
                    ShopId = shopId,
                    ShopName = model.IsFromWeiXin ? model.WxName : shopId,
                    AccessToken = "",
                    RefreshToken = "",
                    AuthTime = now,
                    CreateTime = now,
                    ExpireTime = expiredTime,
                    PlatformType = platformType,
                    FullSyncStatus = "Close"
                };
                shop.NickName = shop.ShopName;
                var onlyCode = CommUtls.GetOnlyCode();
                shop.OnlyCode = onlyCode;
                shop.Id = configureDb.Insert(shop).Value;
                var userShop = new UserShopRelation
                {
                    CreateBy = 0,
                    CreateTime = now,
                    IsDeleted = false,
                    ShopId = shop.Id,
                    UserId = user.Id
                };
                configureDb.Insert(userShop);
                try
                {
                    new ShopService().TryToCreateDbConfig(shop);//配置数据库在哪
                }
                catch (Exception e) { }
                //添加开通的服务信息：微商版
                var order = new AppOrderList
                {
                    MemberId = shopId,
                    OrderItemNum = "",
                    BizStatus = "1",
                    BizStatusExt = "新订",
                    ProductName = "自助版",
                    ProductCode = AppOrderProductCodeType.Print_Offline.ToString(),
                    PaymentAmount = "0",
                    ExecutePrice = "0",
                    RefundFee = "0",
                    GmtCreate = now,
                    GmtServiceBegin = now,
                    GmtConfirm = now,
                    GmtServiceEnd = expiredTime,
                    PlatformType = platformType,
                    UserId = user.Id,
                    Remark = "自行注册，免费使用15天",
                };
                configureDb.Insert(order);
                return new Tuple<User, Shop>(user, shop);
            }
            else
            {
                Shop shop = null;
                try
                {
                    var tid = DES.DecryptUrl(model.Token, CustomerConfig.LoginCookieEncryptKey);
                    //var token = shopRepository.GetToken(tid.ToInt());
                    shop = shopRepository.Get(tid.ToInt());
                }
                catch (Exception ex)
                {

                }
                if (shop != null)
                {
                    AddShopRelation(shop, user);

                    //判断是否已经存在了对应的关联信息

                }
                return new Tuple<User, Shop>(user, shop);
            }
        }

        public void AddShopRelation(Shop shop, User user)
        {
            var configureDb = repository.GetConfigureDb();
            var sr = shopRepository.GetUserShop(shop.Id, user.Id);
            if (sr != null)
            {
                //更新删除状态
                configureDb.ExecuteScalar($"UPDATE dbo.P_UserShopRelation SET IsDeleted=0 WHERE UserId={user.Id} AND ShopId={shop.Id}");
                return;
            }
            var now = DateTime.Now;
            var userShop = new UserShopRelation
            {
                CreateBy = 0,
                CreateTime = now,
                IsDeleted = false,
                ShopId = shop.Id,
                UserId = user.Id
            };
            configureDb.Insert(userShop);

            var expiredTime = now.AddDays(15);
            //添加开通的服务信息
            var order = new AppOrderList
            {
                MemberId = shop.ShopId,
                OrderItemNum = "",
                BizStatus = "1",
                BizStatusExt = "新订",
                ProductName = $"【{shop.PlatformTypeName}】店管家批量打印发货_试用版",
                ProductCode = "Print_" + shop.PlatformType,
                PaymentAmount = "0",
                ExecutePrice = "0",
                RefundFee = "0",
                GmtCreate = now,
                GmtServiceBegin = now,
                GmtConfirm = now,
                GmtServiceEnd = expiredTime,
                PlatformType = shop.PlatformType,
                UserId = user.Id,
                Remark = "添加新店铺，免费试用15天",
            };

            //判断有没有试用过
            var existId = configureDb.ExecuteScalar<int>("SELECT Id FROM dbo.AppOrderList WITH(NOLOCK) WHERE MemberId = @ShopId AND ProductCode = @ProductCode AND PlatformType = @PlatformType AND ExecutePrice = '0' AND PaymentAmount = '0'", new
            {
                shop.ShopId,
                order.ProductCode,
                order.PlatformType
            });
            if (existId <= 0)
                configureDb.Insert(order);
        }

        public Tuple<User, Shop> Login(UserLoginModel model)
        {
            User user = null;
            if (model.IsFromWeiXin)
            {
                user = repository.GetByWxOpenId(model.WxOpenId);
                //若微信账号未查询到则自动注册
                if (user == null)
                {
                    return Register(new UserRegisterModel
                    {
                        WxOpenId = model.WxOpenId,
                        WxName = model.WxName,
                    });
                }
            }
            else
            {
                user = repository.GetByMobile(model.Mobile);
                if (user == null)
                    throw new LogicException("您的手机号码未注册过，请注册后再登录。");
                if (user.Password != DES.EncryptUrl(model.Password, CustomerConfig.LoginCookieEncryptKey))
                    throw new LogicException("账号或密码不正确，请检查。");
                repository.UpdateLastLoginTime(user);
            }
            var shopService = new ShopService();
            Shop shop = null;
            if (!string.IsNullOrEmpty(model.From))
            {
                shop = GetUserShops(user.Id)?.Item2?.FirstOrDefault(s => string.Equals(s.PlatformType, model.From, StringComparison.OrdinalIgnoreCase));
            }
            else if (!string.IsNullOrEmpty(model.Token))
            {
                //var authToken = shopService.ExpainToken(model.Token);
                //shop = shopService.GetShopByIds(new List<int> { authToken.ShopId })?.FirstOrDefault();
                var tid = DES.DecryptUrl(model.Token, CustomerConfig.LoginCookieEncryptKey);
                shop = shopRepository.Get(tid.ToInt());
                if (shop != null)
                    AddShopRelation(shop, user);
            }
            else
                shop = shopService.GetByUserId(user.Id);
            return new Tuple<User, Shop>(user, shop);
        }

        /// <summary>
        /// 用户查找所有关联的店铺
        /// </summary>
        /// <param name="openId"></param>
        /// <returns></returns>
        public List<XcxUserToShopListShowMode> GetUserToShopList(string openId)
        {
            int userId = 0;

            User user = GetWxUser(openId);
            if (user != null)
                userId = user.Id;

            var shopService = new ShopService();

            List<XcxUserToShopListShowMode> listMode = new List<XcxUserToShopListShowMode>();

            List<UserShopRelation> listRelation = repository.GetUserToRelation(user.Id, 0);
            List<int> shopIdList = listRelation.Select(t => t.ShopId).ToList();

            List<Shop> ShopList = shopService.GetShopByIds(shopIdList);

            ShopList.ForEach(t =>
            {
                var relation = listRelation.Where(r => r.ShopId == t.Id).FirstOrDefault();

                if (relation != null)
                {
                    XcxUserToShopListShowMode model = new XcxUserToShopListShowMode();
                    model.ShopName = t.NickName;
                    model.ShopId = t.Id;
                    model.PlatformType = t.PlatformType;
                    model.PlatformTypeName = t.PlatformTypeName;
                    model.Id = relation.Id;
                    model.CreateTime = Convert.ToDateTime(relation.CreateTime);
                    model.IsDefault = relation.IsDefault;

                    listMode.Add(model);
                }

            });

            return listMode.OrderByDescending(t => t.CreateTime).ToList();
        }


        public bool RemoveUserShopRelation(string openId, int id)
        {
            int userId = 0;

            User user = GetWxUser(openId);
            if (user != null)
                userId = user.Id;

            return repository.RemoveUserShopRelation(userId, id);
        }

        public bool SetDefaultUserShopRelation(string openId, int shopId)
        {
            int userId = 0;
            User user = GetWxUser(openId);
            if (user != null)
                userId = user.Id;

            var isOk = false;
            var userShopRelation = repository.GetUserShopRelationByShopId(userId, shopId);
            if (userShopRelation != null)
            {
                isOk = true;
                List<UserShopRelation> listRelation = repository.GetUserToRelation(user.Id, 0);
                listRelation.ForEach(t => {
                    var isDef = 0;
                    if (t.Id == userShopRelation.Id)
                        isDef = 1;

                    repository.UpdateUserShopRelation(user.Id, t.Id, isDef);
                });
            }

            return isOk;
        }

        public bool RemoveUserShopRelationForShopId(int shopId, int id)
        {
            return repository.RemoveUserShopRelationForShopId(shopId, id);
        }




        public Tuple<User, Shop> ResetPassword(UserRegisterModel model)
        {
            var user = repository.GetByMobile(model.Mobile);
            if (user == null)
                throw new LogicException("您要重置密码的账号不存在，您可以先注册一个，再登录。");
            user.Password = DES.EncryptUrl(model.Password, CustomerConfig.LoginCookieEncryptKey);
            user.LastUpdateTime = DateTime.Now;
            repository.Update(user);
            var shopService = new ShopService();
            var shop = shopService.GetByUserId(user.Id);
            return new Tuple<User, Shop>(user, shop);
        }

        /// <summary>
        /// 验证电话号码对应的验证码是否正确
        /// </summary>
        /// <param name="phone"></param>
        /// <param name="messageCode">短信验证码</param>
        /// <returns>1:不正确  2:已过期  3:已被使用</returns>
        public int CheckPhoneCodeIsValid(string phone, string messageCode)
        {
            int res = 0;

            UserVerificationCode vCode = verificationCodeRepository.GetModel(phone, messageCode);

            if (vCode == null)
                res = 1;
            else if (DateTime.Now > vCode.ExpirationTime)
                res = 2;
            else if (vCode.IsEmploy)
                res = 3;

            return res;
        }

        public Tuple<User, List<Shop>> GetUserShops(int currentLoginShopId)
        {
            return repository.GetUserShops(currentLoginShopId);
        }

        public bool IsMoveCode(string phone, string ips)
        {
            return verificationCodeRepository.IsMoveCode(phone, ips);

        }



        public bool PhoneIsExist(string phone)
        {
            var user = repository.GetByMobile(phone);
            return user == null ? false : true;
        }

        /// <summary>
        /// 判断该号码是否在2分钟内重复发送
        /// </summary>
        /// <param name="phone"></param>
        /// <returns></returns>
        public bool OneMinutes(string phone)
        {
            return verificationCodeRepository.OneMinutes(phone);

        }

        public UserShopResponseModel GetWxUserShops(string wxOpenId, bool isAll, bool isMe = false)
        {
            var user = GetWxUser(wxOpenId);
            if (user != null)
                return GetUserShops(user, isAll, isMe);
            return null;
        }

        /// <summary>
        /// 获取明细，小程序是否设置了平台店铺，以及平台名称
        /// </summary>
        /// <param name="wxOpenId">微信标识</param>
        /// <param name="isAll">是否全部（全部即包含删除的）</param>
        /// <param name="isMe">是否包含自己对应的虚拟店铺</param>
        /// <returns></returns>
        public UserShopResponseModel GetWxUserShopsByDetail(string wxOpenId, bool isAll=false,bool isMe=false)
        {
            var userShops = GetWxUserShops(wxOpenId, isAll, isMe);
            //if (userShops != null) {
            //    var shopId = userShops.User.Id;

            //    try
            //    {
            //        //PlatformType对应的中文名称
            //        var suportPlatforms = CustomerConfig.GetAllPlatformAuthLinks();
            //        List<PlatformShopModel> lists = userShops.PlatformShops;

            //        var setting = "";
            //        if (lists.Count > 1)
            //        {
            //            setting = _commonSettingService.GetString("/WeiXin/PlatformSetSelectContentSet/", shopId);
            //        }

            //        lists.ForEach(t => {
            //            //赋平台名称
            //            var res = suportPlatforms.Where(s => s.PlatformType.ToLower() == t.PlatformType.ToLower()).FirstOrDefault();
            //            if (res != null)
            //                t.PlatformName = res.Name;
            //            else
            //                t.PlatformName = t.PlatformType;

            //            if (lists.Count == 1)//如果只有一个平台则默认
            //            {
            //                t.IsDefault = true;
            //                //默认的时候，顺便设置上，以免之前删除了默认，这边自动修改默认
            //                _commonSettingService.Set("/WeiXin/PlatformSetSelectContentSet/", t.PlatformType, shopId);
            //            }
            //            else
            //            {
            //                if (!string.IsNullOrEmpty(setting) && setting.ToLower() == t.PlatformType.ToLower())//如果设置的和目前的匹配的上，则赋默认
            //                    t.IsDefault = true;
            //                else
            //                    t.IsDefault = false;
            //            }
            //        });
            //    }
            //    catch { }
            //}

            return userShops;
        }


        /// <summary>
        /// 收单需要
        /// </summary>
        /// <param name="wxOpenId"></param>
        /// <param name="isAll"></param>
        /// <param name="isMe"></param>
        /// <returns></returns>
        public UserShopResponseModel GetWxUserShopsByShouDan(User user, bool isAll = false)
        {
            var shopList = new ShopRepository().GetByUserIdByShouDan(user.Id, isAll);

            try
            {
                //获取店铺数据库配置信息
                Data.Extension.DbPolicyExtension.InitDbConfig(shopList);
            }
            catch (Exception e) { }


            var model = new UserShopResponseModel() { User = user };

            foreach (var shop in shopList)
            {
                var wxShop = ConvertShopToWxShopModel(user, shop, shopRepository);
                model.Shops.Add(wxShop);
            }
            return model;
        }


        public string GetRequestUrl(string wxOpenId)
        {
            var user = GetWxUser(wxOpenId);
            string setting = "";
            if (user != null)
            {
                string key = "/WeiXin/NewXcxSystemUrl/" + user.Id;
                setting = _commonSettingService.GetString(key, -1);
                if (setting == null)
                    setting = _commonSettingService.GetString("/WeiXin/NewXcxSystemUrl/0", 0);
            }

            return Convert.ToString(setting);
        }

        /// <summary>
        /// 获取用户店铺信息
        /// </summary>
        /// <param name="user"></param>
        /// <param name="isAll">是否全部</param>
        /// <param name="isMe">是否包含自己当前微信</param>
        /// <returns></returns>
        public UserShopResponseModel GetUserShops(User user, bool isAll = true, bool isMe = false)
        {
            //获取用户所有的店铺，按平台分类，包含Token
            var shops = repository.GetUserShops(user, isAll);
            List<Shop> shopList = new List<Shop>();
            if (!isMe)
            {
                shops.ForEach(s =>
                {
                    if (s.ShopId != user.WxOpenId) {
                        shopList.Add(s);
                    }
                });
            }
            else
                shopList = shops;

            var model = new UserShopResponseModel() { User = user };
            //model.PlatformShops = shopList
            //    ?.GroupBy(s => s.PlatformType)
            //    ?.Select(s=>new PlatformShopModel { PlatformType= s.Key,Shops = s.ToList() } )
            //    ?.ToList();
            //model?.PlatformShops?.ForEach(s=> {
            //    var token = shopRepository.CreateToken(new LoginAuthToken
            //    {
            //        PlatformType = s.PlatformType,
            //        UserId = user.Id,
            //        ShopId = s?.Shops?.FirstOrDefault().Id ?? 0,
            //    });
            //    s.Token = token.Token;
            //});
            foreach (var shop in shopList)
            {
                var wxShop = ConvertShopToWxShopModel(user, shop, shopRepository);
                model.Shops.Add(wxShop);
            }
            var defaultShopId = repository.GetDefaultShopIdByUserId(user.Id);
            if(defaultShopId>0)
            {
                var shop = model.Shops.FirstOrDefault(s=>s.Id == defaultShopId);
                shop.IsDefault = true;
            }
            return model;
        }

        public WxShopModel ConvertShopToWxShopModel(User user, Shop shop,ShopRepository rp)
        {
            var sm = new WxShopModel
            {
                Id = shop.Id,
                ShopName = shop.NickName,
                PlatformType = shop.PlatformType,
                PlatformTypeName = shop.PlatformTypeName,
                ExpiredTime = shop.ExpireTime
            };
            var token = shopRepository.CreateToken(new LoginAuthToken
            {
                UserId = user.Id,
                ShopId = shop.Id,
            });
            sm.Token = token.Token;
            sm.GateWayUrl = "";

            var gateWayUrls = new Dictionary<string, Tuple<string, string>>();
            gateWayUrls.Add(PlatformType.Alibaba.ToString(), new Tuple<string, string>("https://weixin1.dgjapp.com", "https://weixin2.dgjapp.com"));
            gateWayUrls.Add(PlatformType.Jingdong.ToString(), new Tuple<string, string>("https://jdweixin1.dgjapp.com", "https://jdweixin2.dgjapp.com"));
            gateWayUrls.Add(PlatformType.Pinduoduo.ToString(), new Tuple<string, string>("https://pddweixin1.dgjapp.com", "https://pddweixin2.dgjapp.com"));
            //gateWayUrls.Add(PlatformType.Alibaba.ToString(), new Tuple<string, string>("https://dgjapp.goho.co", "https://dgjapp.goho.co"));
            //gateWayUrls.Add(PlatformType.Jingdong.ToString(), new Tuple<string, string>("https://dgjapp.goho.co", "https://dgjapp.goho.co"));
            //gateWayUrls.Add(PlatformType.Pinduoduo.ToString(), new Tuple<string, string>("https://dgjapp.goho.co", "https://dgjapp.goho.co"));
            var gateWayUrl = gateWayUrls.First().Value;
            var pt = shop.PlatformType;
            var dbLocation = shop?.DbConfig?.DbServer?.Location;
            if (string.IsNullOrEmpty(dbLocation))
            {
                if(pt == PlatformType.Jingdong.ToString())
                    gateWayUrl = gateWayUrls[pt];
                else
                    gateWayUrl = gateWayUrls[PlatformType.Alibaba.ToString()];
            }
            else
            {
                if (gateWayUrls.ContainsKey(dbLocation))
                    gateWayUrl = gateWayUrls[dbLocation];
                else
                    Log.WriteWarning($"UserService.ConvertShopToWxShopModel获取店铺网关时可能存在问题，未能找到对应的网关信息，");
            }
            if (string.IsNullOrEmpty(shop.WxVersion) || shop.WxVersion?.ToInt() % 2 != 1)
                sm.GateWayUrl = gateWayUrl.Item1;
            else
                sm.GateWayUrl = gateWayUrl.Item2;
            return sm;
        }

        /// <summary>
        /// 添加一条短信验证码
        /// </summary>
        /// <returns></returns>
        public bool AddVerificationCod(UserVerificationCode vcodeModel)
        {
            vcodeModel.Id = verificationCodeRepository.Add(vcodeModel);

            return vcodeModel.Id > 0 ? true : false;
        }


        /// <summary>
        /// 短信验证码，更新为已使用
        /// </summary>
        /// <returns></returns>
        public void UpdateVeriCodeIsEmploy(UserRegisterModel model)
        {

            UserVerificationCode vCode = verificationCodeRepository.GetModel(model.Mobile, model.MobileMeessageCode);

            if (vCode != null)
            {
                vCode.IsEmploy = true;

                verificationCodeRepository.Update(vCode);
            }
        }

        public User GetByShopId(int shopId)
        {

            return repository.GetByShopId(shopId);
        }


        public User GetWxUser(string wxOpenId)
        {
            return repository.GetByWxOpenId(wxOpenId);
        }

        public void SaveWxUserInfo(int shopId, string openname, string gender, string province, string city, string avatarUrl)
        {
            User info = GetByShopId(shopId);
            if (info != null)
            {
                info.NickName = openname;
                info.Gender = gender;
                info.Province = province;
                info.City = city;
                info.AvatarUrl = avatarUrl;
                repository.Update(info);
            }

        }

        /// <summary>
        /// 只是添加一个P_User 用户信息，店铺关系没有添加
        /// </summary>
        /// <param name="wxOpenId">微信id</param>
        /// <param name="browserReferrer">注册来源</param>
        /// <returns></returns>
        public int AddUser(string wxOpenId, string browserReferrer, string appid)
        {
            User user = new User
            {
                Mobile = "",
                WxOpenId = wxOpenId,
                CreateTime = DateTime.Now,
                BrowserReferrer = browserReferrer,
                WxAppKey = appid,
            };

            return repository.Add(user);
        }

        /// <summary>
        /// 查看用户对于的店铺
        /// </summary>
        /// <param name="openId"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public UserShopRelation GetOneUserShopRelation(string openId, int shopId, bool isAll)
        {
            return repository.GetOneUserShopRelation(openId, shopId, isAll);
        }


        /// <summary>
        /// 更新用户表
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public bool UpdateUser(User user)
        {
            return repository.Update(user);
        }

        /// <summary>
        /// 添加用户-店铺的关系
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="shopId"></param>
        public void AddUserShopRelation(string openid, int userId, int shopId)
        {
            var configureDb = repository.GetConfigureDb();

            UserShopRelation userRelation = GetOneUserShopRelation(openid, shopId, true);

            if (userRelation != null)
            {
                userRelation.IsDeleted = false;
                userRelation.CreateTime = DateTime.Now;

                configureDb.Update(userRelation);
            }
            else
            {
                var userShop = new UserShopRelation
                {
                    CreateBy = 0,
                    CreateTime = DateTime.Now,
                    IsDeleted = false,
                    ShopId = shopId,
                    UserId = userId
                };
                configureDb.Insert(userShop);
            }


        }
        /// <summary>
        /// 添加用户-店铺的关系
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="shopId"></param>
        public void AddUserShopRelation(int userId, int shopId)
        {

            var shopService = new ShopService();
            var exist = shopService.Get(userId, shopId) != null;
            if (!exist)
            {
                var configureDb = repository.GetConfigureDb();
                var userShop = new UserShopRelation
                {
                    CreateBy = 0,
                    CreateTime = DateTime.Now,
                    IsDeleted = false,
                    ShopId = shopId,
                    UserId = userId
                };
                configureDb.Insert(userShop);
            }
        }
        /// <summary>
        /// 店铺查找用户列表
        /// </summary>
        /// <param name="shop"></param>
        /// <param name="memberId">这个是当前登录的店铺，排除在查询后，user里面也是自己该店铺的</param>
        /// <returns></returns>
        public List<XcxManageListShowMode> ShopGetUserList(int shopId,string memberId)
        {

            List<XcxManageListShowMode> listShow = new List<XcxManageListShowMode>();

            List<User> list = GetByShopIdList(new List<int> { shopId });

            //隐藏1,自助版用户，会生成一个用户。
            list = list.Where(t => t.Mobile != memberId).ToList();

            //隐藏2,拼多多无线端，虚拟一个用户
            list = list.Where(t => t.BrowserReferrer != "拼多多入端虚拟用户").ToList();


            list.ForEach(u =>
            {
                u.UsRelationList.ForEach(l =>
                {
                    if (!l.IsDeleted)
                    {
                        XcxManageListShowMode showList = new XcxManageListShowMode();
                        showList.Id = l.Id;
                        showList.NickName = u.NickName;
                        showList.Province = u.Province;
                        showList.City = u.City;
                        showList.AvatarUrl = u.AvatarUrl;
                        showList.CreateTime = Convert.ToDateTime(l.CreateTime);
                        listShow.Add(showList);
                    }

                });

            });

            return listShow;
        }

        public List<User> GetByShopIdList(List<int> listShopId)
        {
            return repository.GetByShopIdList(listShopId);
        }

        public List<UserShopRelation> GetUserToShopRelationList(int  userId,int isDelete, bool isMe = false)
        {
            return repository.GetUserToRelation(userId, isDelete, isMe);
        }

        public PagedResultModel<User> GetPageList(string stime, string etime, string nickName,
            string mobile, string status, int pageIndex, int pageSize)
        {

            DynamicParameters parameters = new DynamicParameters();

            var sql_condition = " WHERE Type=1 ";
            if (string.IsNullOrWhiteSpace(stime) == false && string.IsNullOrWhiteSpace(etime) == false)
            {
                sql_condition += "AND  CreateTime BetWeen @stime and @etime ";
                parameters.Add("stime", stime);
                parameters.Add("etime", etime);
            }
            if (string.IsNullOrWhiteSpace(nickName) == false)
            {
                sql_condition += "AND  NickName like @nickName ";
                parameters.Add("nickName", $"'%{nickName}%'");
            }
            if (string.IsNullOrWhiteSpace(mobile) == false)
            {
                sql_condition += "AND  Mobile=@mobile ";
                parameters.Add("mobile", mobile);
            }
            if (string.IsNullOrWhiteSpace(status) == false)
            {
                sql_condition += "AND  status=@status ";
                parameters.Add("status", status);
            }


            //总数据条数
            var totalCountSql = "SELECT COUNT(*) FROM P_User WITH(NOLOCK) " + sql_condition + "; ";
            var paggingSql = $" OFFSET {((pageIndex - 1) * pageSize)} ROWS FETCH NEXT {pageSize} ROWS ONLY ";
            var sql = $@"SELECT * FROM P_User as u WITH(NOLOCK) {sql_condition} ORDER BY u.CreateTime DESC {paggingSql}";
            var data = repository.GetPageList(totalCountSql + sql, parameters);

            return new PagedResultModel<User>()
            {
                IsOrderDesc = true,
                OrderByField = "CreateTime",
                PageIndex = pageIndex,
                PageSize = pageSize,
                Rows = data.Item2,
                Total = data.Item1
            };
        }

        #region 个人中心相关业务

        /// <summary>
        /// 查询用户下的所有店铺信息
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="totalCount"></param>
        /// <returns></returns>
        public List<AccountShopModel> GetAccountShops(int userId, AccountShopRequestModel request, out int totalCount)
        {
            var temps = shopRepository.GetByUserIds(userId, request.PageSize, request.PageIndex, request.PlatformType, request.ShopName, out totalCount);
            if (temps == null || !temps.Any())
                return null;
            var appShops = new List<AccountShopModel>();
            temps.ForEach(s =>
            {
                var app = shopRepository.GetCurrentAppService(s.ShopId, s.PlatformType);
                var temp = new AccountShopModel
                {
                    Id = s.Id,
                    NickName = s.NickName,
                    ShopName = s.ShopName,
                    PlatformType = s.PlatformType,
                    IsAuthExpired = s.LastSyncMessage?.Contains($"店铺【{s.ShopName}】授权过期") == true,
                    AppName = app?.ProductName,
                    ServiceBeginTime = app?.GmtServiceBegin,
                    ServiceEndTime = app?.GmtServiceEnd,
                    PlatformTypeName = s.PlatformTypeName
                };
                if (temp.PlatformType == "YunJi" || CustomerConfig.IsTouTiaoXi(temp.PlatformType))
                {
                    temp.AppKey = s.AccessToken;
                    temp.AppSecret = s.RefreshToken;
                    temp.IsUseAppKey = true;
                }
                var payLink = CustomerConfig.GetPayLink(s.PlatformType);
                var token = DES.EncryptUrl($"{userId}-{temp.Id}", CustomerConfig.LoginCookieEncryptKey);
                if (payLink.StartsWith(CustomerConfig.AppStoreUrl))
                    payLink += $"&Token={token}&Source=user";
                temp.PayLink = payLink;
                temp.AuthUrl = CustomerConfig.GetPlatformAuthLink(temp.PlatformType);
                appShops.Add(temp);
            });
            return appShops;
        }

        public Shop UpdateShopNickName(int userId, int shopId, string name)
        {
            return shopRepository.UpdateShopNickName(userId, shopId, name);
        }
        public void UpdateShopAppInfo(int shopId, string appKey, string appSecret)
        {
            shopRepository.UpdateShopAppInfo(shopId, appKey, appSecret);
        }

        #endregion


        public List<ServiceAppOrder> GetSuccessServiceOrdersByShopIds(List<int> shopIds, List<string> pShopIds,List<string> serviceAppIds)
        {
            return repository.GetSuccessServiceOrdersByShopIds(shopIds, pShopIds, serviceAppIds);
        }


        public List<ServiceAppOrder> GetLastEndOrdersByShopIds(List<int> shopIds, List<string> pShopIds)
        {
            if (shopIds == null || pShopIds == null || shopIds.Count <= 0 || pShopIds.Count <= 0) return null;
            return repository.GetLastEndOrdersByShopIds(shopIds, pShopIds);
        }

        public List<ServiceAppOrder> GetLastEndOrdersByPtShopsIds(List<string> ptShopsIds, string pt)
        {
            return repository.GetLastEndOrdersByPtShopsIds(ptShopsIds, pt);
        }
        
        /// <summary>
        /// 查询混乱的订购记录
        /// 快手uid和shopid都有可能匹配上
        /// </summary>
        /// <param name="shopIds"></param>
        /// <param name="ptShopsIds"></param>
        /// <param name="pt"></param>
        /// <returns></returns>
        public List<ServiceAppOrder> GetLastEndOrdersWhere(List<int> shopIds, List<string> ptShopsIds, string pt)
        {
            return repository.GetLastEndOrdersWhere(shopIds, ptShopsIds, pt);
        }
        
        public List<AppOrderList> GetLastEndAppOrderListByPtShopsIds(List<string> ptShopsIds, string pt)
        {
            return repository.GetLastEndAppOrderListByPtShopsIds(ptShopsIds, pt);
        }

        /// <summary>
        /// 获取最后一单成功的订购记录，过滤已退款
        /// </summary>
        /// <param name="appkey"></param>
        /// <param name="ptShopId"></param>
        /// <returns></returns>
        public ServiceAppOrder GetLastEndOrdersWhere(string appkey, string ptShopId)
        {
            return repository.GetLastEndOrdersWhere(appkey, ptShopId);
        }

    }
}
