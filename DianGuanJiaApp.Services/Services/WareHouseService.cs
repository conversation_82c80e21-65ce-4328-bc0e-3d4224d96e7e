using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Warehouse.Model.Request;
using DianGuanJiaApp.Warehouse.Sdk;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Warehouse.Services;
using DianGuanJiaApp.Warehouse.Entity;
using DianGuanJiaApp.Warehouse.Model;
using DianGuanJiaApp.Warehouse.Model.Response;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.Services;
using System.IO;
using MongoDB.Driver.Core.Configuration;
using NPOI.HSSF.Record;
using Aop.Api.Domain;
using DianGuanJiaApp.Services.Services.OrderModule;
using vipapis.overseas;
using DianGuanJiaApp.Data.Entity.OrderModule;
using DianGuanJiaApp.Services.Services.SyncDataInterface;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Services.Services.BaseProduct;
using DianGuanJiaApp.Warehouse.Repository;
using WareHouseRepository = DianGuanJiaApp.Data.Repository.WareHouseRepository;
using WareHouseSkuBindRelationRepository = DianGuanJiaApp.Data.Repository.WareHouseSkuBindRelationRepository;
using static System.String;
using WareHouseSkuBindRelation = DianGuanJiaApp.Data.Entity.WareHouseSkuBindRelation;
using DianGuanJiaApp.Data.Model.Tools;
using DianGuanJiaApp.Utility.Other;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using RabbitMQ.Client.Framing.Impl;
using System.Net.NetworkInformation;
using DianGuanJiaApp.Data.Model.CombinedProductChangeLog;
using DnsClient.Protocol;

namespace DianGuanJiaApp.Services
{
    /// <summary>
    /// 库存
    /// </summary>
    public partial class WareHouseService : BaseService<WareHouseRelation>
    {
        private ProductSkuFxRepository _productSkuFxRepository;
        private ProductFxRepository _productFxRepository;
        private WareHouseSkuBindRelationRepository _wareHouseSkuBindRelationRepository;
        private WareHouseRepository _repository;
        private IWarehouseClient _warehouseclient;
        private LogicOrderService _logicOrderService = new LogicOrderService();
        private FxUserShopService _fxUserShopService = new FxUserShopService();
        private DbConfigRepository _dbConfigRepository;
        private string _connectionString = string.Empty;

        public WareHouseService()
        {
            _productSkuFxRepository = new ProductSkuFxRepository();
            _productFxRepository = new ProductFxRepository();
            _wareHouseSkuBindRelationRepository = new WareHouseSkuBindRelationRepository();
            _repository = new WareHouseRepository();
            _warehouseclient = new WarehouseClient(CustomerConfig.WarehouseApiUrl, CustomerConfig.WarehouseAppkey, CustomerConfig.WarehouseAppsecret);
            this._baseRepository = _repository;
            _dbConfigRepository = new DbConfigRepository();
        }
        public WareHouseService(string connectionString) : base(connectionString)
        {
            _productSkuFxRepository = new ProductSkuFxRepository(connectionString);
            _productFxRepository = new ProductFxRepository(connectionString);
            _wareHouseSkuBindRelationRepository = new WareHouseSkuBindRelationRepository(connectionString);
            _repository = new WareHouseRepository();
            _warehouseclient = new WarehouseClient(CustomerConfig.WarehouseApiUrl, CustomerConfig.WarehouseAppkey, CustomerConfig.WarehouseAppsecret);
            this._baseRepository = _repository;
            _dbConfigRepository = new DbConfigRepository(connectionString);
            _connectionString = connectionString;
        }

        #region 基础验证
        /// <summary>
        /// 检测库存扣减设置是否为当前类型
        /// </summary>
        /// <param name="type">1=打单，2=发货，3=二次发货打单，4=二次发货</param>
        /// <returns></returns>
        public bool CheckStockOutType(int type, int shopId)
        {
            var model = new CommonSettingService().Get("/FenFa/System/Config/StockOutType", shopId);
            var settype = "2";
            if (model != null) settype = model.Value;
            //return settype == type.ToString();
            return settype.Contains(type.ToString());
        }

        /// <summary>
        /// 检测库存扣减设置是否为当前类型
        /// </summary>
        /// <param name="type">1=打单，2=发货，3=二次发货打单，4=二次发货</param>
        /// <param name="shopId"></param>
        /// <param name="combinationStockType">OUT 组合货品扣库存方式</param>
        /// <returns></returns>
        public bool CheckStockOutType(int type, int shopId, out int combinationStockType)
        {
            combinationStockType = 2;//组合货品扣库存方式，默认2=扣子货品
            var keys = new List<string>() { SystemSettingKeys.StockOutTypeKey, SystemSettingKeys.StockOutZHTypeKey };
            var modelList = new CommonSettingService().GetSets(keys, shopId);
            var settype = "2";

            var model = modelList.Where(a => a.Key == SystemSettingKeys.StockOutTypeKey).FirstOrDefault();
            if (model != null) settype = model.Value;

            model = modelList.Where(a => a.Key == SystemSettingKeys.StockOutZHTypeKey).FirstOrDefault();
            if (model != null)
                int.TryParse(model.Value, out combinationStockType);

            if (combinationStockType <= 0)
                combinationStockType = 2;

            //return settype == type.ToString();
            return settype.Contains(type.ToString());
        }


        /// <summary>
        /// 获取货主编号
        /// </summary>
        /// <returns></returns>
        public string GetOwnerCode()
        {
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            return ("FenDanSystem" + fxUserId).ToShortMd5();
        }

        /// <summary>
        /// 获取货主编号
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public string GetOwnerCode(int fxUserId)
        {
            return ("FenDanSystem" + fxUserId).ToShortMd5();
        }

        /// <summary>
        /// 判断仓库是否启用
        /// </summary>
        /// <param name="ownerCode"></param>
        /// <returns></returns>
        public bool WarehouseIsEnabled(string ownerCode)
        {
            var warehousemodel = GetWareHouseRelation(ownerCode);
            return warehousemodel?.Status == "Enabled";
        }

        public WareHouseRelation GetWareHouseRelation(string ownerCode)
        {
            return _repository.GetWareHouseRelation(ownerCode);
        }
        /// <summary>
        /// 创建基础商品时，如果没有仓库，则默认创建一个
        /// </summary>
        public void GenerateDefaultStore(int fxUserId)
        {
            try
            {
                //int FxUserId = SiteContext.Current.CurrentFxUserId;
                int FxUserId = fxUserId;
                string ownerCode = GetOwnerCode(FxUserId);
                var warehouse = GetWareHouseRelation(ownerCode);
                if (warehouse == null)
                {
                    var req = new WarehouseAddRequest
                    {
                        WarehouseName = "默认仓",
                        OwnerCode = ownerCode,
                        Province = "==省==",
                        City = "==市==",
                        County = "==区==",
                        Address = "未设置详细地址",
                        Contact = "未编辑联系人",
                        Mobile = "未编辑联系方式",
                        Telphone = ""
                    };
                    AddStoreManagement(req);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"创建默认仓库异常：{ex.Message}");
            }
        }

        #endregion

        #region 增加、扣减库存相关
        /// <summary>
        /// 检查订单库存数量，stockFlag = true 直接调用接口扣库存
        /// </summary>
        /// <param name="orders">订单</param>
        /// <param name="fxUserId">分销用户ID</param>
        /// <param name="shopId">店铺ID</param>
        /// <param name="type">1=打单，2=发货</param>
        /// <param name="stockFlag">是否执行扣库存</param>
        /// <param name="outErrorMsg">错误信息</param>
        /// <returns></returns>
        public List<InventoryOrderRequest> CheckStockCount(List<InventoryOrderRequest> orders, int fxUserId, int shopId, int type, bool stockFlag, out string outErrorMsg, LogForOperator log = null)
        {
            var errMsgSb = new StringBuilder();
            outErrorMsg = "NO_CHECK";
            try
            {
                #region 基础验证，是否执行库存操作
                //货主编号
                string ownerCode = GetOwnerCode(fxUserId);
                //是否执行的标志
                var warehouseIsEnabled = WarehouseIsEnabled(ownerCode);
                var isSameStockOutType = CheckStockOutType(type, shopId);
                bool flag = isSameStockOutType && warehouseIsEnabled;
                #endregion
                if (flag)
                {
                    #region 查询分单数据库，返回库存需要的字段，获取有绑定关系且该订单项库存状态!="all" 的SKU列表
                    //1.获取订单，返回库存需要的字段
                    var orderIds = orders.Select(x => x.LogicOrderId).ToList();
                    var orderItemIds = new List<int>();
                    orders.ForEach(x =>
                    {
                        if (x.Items != null && x.Items.Any())
                        {
                            x.Items.ForEach(y =>
                            {
                                if (y.LogicOrderItemId != 0)
                                    orderItemIds.Add(y.LogicOrderItemId);
                            });
                        }
                    });
                    var items = _logicOrderService.GetOrdersByStock(orderIds, ownerCode, fxUserId, orderItemIds);

                    //2.获取有绑定信息的SKU查询库存数量 并且库存状态不是配齐状态
                    var querySku = items.Item2.Where(x => !IsNullOrEmpty(x.WareHouseSkuCode) && x.StockState != "all").ToList();
                    #endregion
                    if (querySku != null && querySku.Count > 0)
                    {
                        #region 根据SKU列表去查询库存接口，获取对应的库存数量
                        WarehouseProductSkuListRequest request = new WarehouseProductSkuListRequest()
                        {
                            OwnerCode = ownerCode,
                            SkuList = querySku.Select(x => x.WareHouseSkuCode).ToList()
                        };
                        var rsp = _warehouseclient.Execute(request);
                        #endregion
                        if (rsp.IsSucc && rsp.Items != null && rsp.Items.Count > 0)
                        {
                            #region 根据接口返回的库存数量，验证订单项商品数量<库存,判断库存是否足够，不够返回错误
                            //3.排除没有库存数据的逻辑订单项
                            var stocksuks = rsp.Items.Select(x => x.WareHouseSkuCode).ToList();
                            var logicOrderItems = items.Item2.Where(x => stocksuks.Contains(x.WareHouseSkuCode)).ToList();
                            //4.验证库存数量是否足够
                            bool allsuccess = true;
                            List<Data.FxModel.StockItemError> listError = new List<Data.FxModel.StockItemError>();
                            foreach (var item in rsp.Items)
                            {
                                //获得该库存SKU下的所有订单项
                                var orderItems = logicOrderItems.Where(x => x.WareHouseSkuCode == item.WareHouseSkuCode).ToList();
                                //扣减总数
                                var reserverCount = orderItems.Sum(x => x.ReserverCount);
                                if (item.StockCount < reserverCount)
                                {
                                    allsuccess = false;
                                    string pructName = $"{orderItems[0].SkuName}";
                                    string errorMsg = $"库存：{item.StockCount}，商品数量：{reserverCount}，库存不足";
                                    string orderId = $"{orderItems[0].PlatformOrderId}";
                                    listError.Add(new Data.FxModel.StockItemError()
                                    {
                                        ErrorMsg = errorMsg,
                                        OrderId = orderId,
                                        ProductName = pructName
                                    });
                                }
                            }
                            if (listError.Any())
                            {
                                outErrorMsg = listError.ToJson();
                                errMsgSb.AppendLine($"检测到库存不足：{outErrorMsg}");
                            }
                            #endregion
                            //5.执行扣库存
                            if (allsuccess && stockFlag)
                            {
                                #region 库存数量通过，调用订单直接扣减库存接口
                                //组合接口调用请求实体
                                WarehouseOrderStockOutRequest reqeust = new WarehouseOrderStockOutRequest()
                                {
                                    OwnerCode = ownerCode,
                                    Orders = new List<InventoryOrderRequest>(),
                                };
                                items.Item1.ForEach(x =>
                                {
                                    var ois = logicOrderItems.Where(p => p.LogicOrderId == x.LogicOrderId).ToList();
                                    if (ois != null && ois.Count > 0)
                                    {
                                        reqeust.Orders.Add(new InventoryOrderRequest()
                                        {
                                            FxUserId = fxUserId,
                                            IsAllowNegativeStock = 0,
                                            LogicOrderId = x.LogicOrderId,
                                            PlatformOrderId = x.PlatformOrderId,
                                            SourceFxUserId = x.SourceFxUserId,
                                            SourceShopId = x.SourceShopId,
                                            UpFxUserId = x.UpFxUserId,
                                            StockType = type == 1 ? "OrderDeduction_P" : "OrderDeduction_S",
                                            Items = ois.Select(p => new InventoryItemRequest
                                            {
                                                DeductionCount = p.ReserverCount,
                                                ExtProperty1 = "",
                                                OrderItemCode = p.OrderItemCode,
                                                WarehouseSkuCode = p.WareHouseSkuCode,
                                                PlatformOrderId = p.PlatformOrderId
                                            }).ToList()
                                        });
                                    }
                                });
                                //调用接口
                                var response = _warehouseclient.Execute(reqeust);
                                #endregion
                                if (response.IsSucc && response.Orders != null && response.Orders.Count > 0)
                                {
                                    #region 订单直接扣减库存返回数据，返回需要处理的错误给前台，没有就进行业务库的操作
                                    //需要处理的错误
                                    var errorOrders = response.Orders.Where(x => x.Items != null && x.Items.Count(p => p.ErrorCode == "NO_AVAILIABLE_STOCKINFO"
                                    || p.ErrorCode == "SKU_DEDUTION_EXECUTE_FALURE" || p.ErrorCode == "STOCK_NOT_ENOUGH") > 0).ToList();
                                    if (errorOrders != null && errorOrders.Count > 0)
                                    {
                                        listError = new List<Data.FxModel.StockItemError>();
                                        foreach (var order in errorOrders)
                                        {
                                            foreach (var erroritem in order.Items)
                                            {
                                                string pructName = $"{items.Item2.FirstOrDefault(x => x.OrderItemCode == erroritem.OrderItemCode)?.ProductName ?? ""}";
                                                string errorMsg = $"{erroritem.ErrorMessage}";
                                                string orderId = $"{order.PlatformOrderId}";

                                                listError.Add(new Data.FxModel.StockItemError()
                                                {
                                                    ErrorMsg = errorMsg,
                                                    OrderId = orderId,
                                                    ProductName = pructName
                                                });
                                            }
                                        }

                                        if (listError.Any())
                                        {
                                            outErrorMsg = listError.ToJson();
                                            errMsgSb.AppendLine($"扣库存失败：{outErrorMsg}");
                                        }
                                    }
                                    else
                                    {
                                        //更新逻辑订单、逻辑订单项 库存状态字段
                                        DoOrderStockStateAndStockRecord(fxUserId, shopId, ownerCode, items, response, type);
                                    }
                                    #endregion

                                }
                                else
                                {
                                    errMsgSb.AppendLine($"扣库存服务异常：{response.ToJson()}");
                                    Log.WriteError($"扣库存服务无响应：{response.ToJson()}");
                                    outErrorMsg = "库存服务无响应";
                                }
                            }
                        }
                        else
                        {
                            errMsgSb.AppendLine($"查询SKU库存接口异常：{rsp.ToJson()}");
                            Log.WriteError($"库存服务无响应：{rsp.ToJson()}");
                            outErrorMsg = "库存服务无响应";
                        }
                    }
                    else
                    {
                        errMsgSb.AppendLine($"无关联SKU绑定库存==> 参数：orderIds={Join(",", orderIds)}，ownerCode={ownerCode}，fxUserId={fxUserId}，orderItemIds={Join(",", orderItemIds)}，结果：{items?.Item2?.ToJson() ?? "无"}");
                    }
                }
                else
                {
                    errMsgSb.AppendLine($"检查出库标记 ==> 参数：ownerCode={ownerCode}，type={type}，ShopId={shopId}，结果： {isSameStockOutType}={isSameStockOutType}，warehouseIsEnabled={warehouseIsEnabled}");
                }
            }
            catch (Exception ex)
            {
                outErrorMsg = ex.ToString();
                errMsgSb.AppendLine($"扣库存未知异常：{ex}");
            }
            finally
            {
                if (log != null)
                    log.Exception = errMsgSb.ToString();
            }
            return null;
        }


        /// <summary>
        /// 检查订单库存数量，stockFlag = true 直接调用接口扣库存V2版-2021.12
        /// </summary>
        /// <param name="orders">订单</param>
        /// <param name="fxUserId">分销用户ID</param>
        /// <param name="shopId">店铺ID</param>
        /// <param name="type">1=打单，2=发货，3=二次发货打单，4=二次发货</param>
        /// <param name="stockFlag">是否执行扣库存</param>
        /// <param name="isContinueForStock">当部分库存不足时，是否继续</param>
        /// <param name="outErrorMsg">错误信息</param>
        /// <param name="postRequestDatas">OUT 需要扣库存的订单数据</param>
        /// <param name="log">日志</param>
        /// <param name="isMultiPack">是否多包裹，多包裹以页面传的数量为准</param>
        /// <returns>库存不足的订单项</returns>
        public List<int> CheckStockCountV2(List<InventoryOrderRequest> orders, int fxUserId, int shopId, int type, bool stockFlag, bool isContinueForStock, out string outErrorMsg, out List<InventoryOrderRequest> postRequestDatas, LogForOperator log = null, bool isMultiPack = false)
        {
            var errMsgSb = new StringBuilder();
            outErrorMsg = "NO_CHECK";
            postRequestDatas = null;

            var excludeOrderItemIds = new List<int>();//因库存不足需排除的LogicOrderItemId

            try
            {
                #region 基础验证，是否执行库存操作
                //货主编号
                string ownerCode = GetOwnerCode(fxUserId);
                //是否执行的标志
                int combinationStockType;//组合货品扣库存方式：1=单扣组合货品自身库存；2=扣组合货品子SKU；3=组合货品不够扣子SKU
                var warehouseIsEnabled = WarehouseIsEnabled(ownerCode);
                var isSameStockOutType = CheckStockOutType(type, shopId, out combinationStockType);
                bool flag = isSameStockOutType && warehouseIsEnabled;
                Log.Debug(() => $"type={type}，ownerCode={ownerCode},warehouseIsEnabled={warehouseIsEnabled},isSameStockOutType={isSameStockOutType},flag={flag},shopId={shopId}", "stock.txt");
                #endregion
                if (flag)
                {
                    #region 查询分单数据库，返回库存需要的字段，获取有绑定关系且该订单项库存状态!="all" 的SKU列表
                    //1.获取订单，返回库存需要的字段
                    var orderIds = orders.Select(x => x.LogicOrderId).ToList();
                    var orderItems = orders.Where(x => x.Items != null).SelectMany(x => x.Items);
                    var orderItemIds = orderItems.Select(a => a.LogicOrderItemId).ToList();

                    //冷，热处理
                    Tuple<List<LogicOrderStock>, List<LogicOrderItemStock>> items;
                    var logicOrderStocks = new List<LogicOrderStock>();
                    var logicOrderItemStocks = new List<LogicOrderItemStock>();
                    try
                    {
                        //冷，热分离处理
                        var ordersByDataFlag = orders.GroupBy(m => m.DataFlag).ToList();
                        ordersByDataFlag.ForEach(grouping =>
                        {
                            var ordersByGroup = grouping.ToList();
                            var orderIdsByGroup = ordersByGroup.Select(m => m.LogicOrderId).Distinct().ToList();
                            var orderItemIdsByGroup = ordersByGroup.Where(m => m.Items != null && m.Items.Any())
                                .SelectMany(m => m.Items).Select(m => m.LogicOrderItemId).Distinct().ToList();
                            //是否冷数据
                            var isColdDb = grouping.Key == 1;
                            //获取库存
                            var stocks =
                                new ColdLogicOrderService(isColdDb).GetOrdersByStock(orderIdsByGroup, ownerCode,
                                    fxUserId,
                                    orderItemIdsByGroup);
                            if (stocks?.Item1 != null && stocks.Item1.Any())
                            {
                                stocks.Item1.ForEach(s =>
                                {
                                    //去重
                                    if (logicOrderStocks.Exists(m => m.LogicOrderId == s.LogicOrderId))
                                    {
                                        return;
                                    }

                                    logicOrderStocks.Add(s);
                                    //订单项
                                    var itemModels = stocks.Item2
                                        ?.Where(m => m.LogicOrderId == s.LogicOrderId)
                                        .ToList();
                                    if (itemModels != null && itemModels.Any())
                                    {
                                        logicOrderItemStocks.AddRange(itemModels);
                                    }
                                });
                            }
                        });
                        //最终查询结果
                        items =
                            new Tuple<List<LogicOrderStock>, List<LogicOrderItemStock>>(logicOrderStocks,
                                logicOrderItemStocks);
                        var itemStocks = items.Item2;
                        if (itemStocks != null && itemStocks.Any())
                        {
                            var skuCodes = itemStocks.Where(w => w.ProductSkuCode != null).Select(s => s.ProductSkuCode)
                                .Distinct().ToList();
                            //获取仓库库存SKU
                            var wareHouseSkuCodes =
                                _wareHouseSkuBindRelationRepository.GetWareHouseSkuCodes(ownerCode, skuCodes);
                            itemStocks.ForEach(item =>
                            {
                                var wareHouseSku = wareHouseSkuCodes.FirstOrDefault(code =>
                                    code.PlatformSkuCode == item.ProductSkuCode);
                                item.WareHouseSkuCode = wareHouseSku?.WareHouseSkuCode;
                            });
                            //获取商品名称
                            _productFxRepository.SetLogicOrderItemStocksProductName(itemStocks);
                        }
                    }
                    catch (Exception ex)
                    {
                        if (ex.Message.Contains("等待的操作过时") || ex.Message.Contains("超时"))
                            outErrorMsg = "CHECK_TIMEOUT";
                        else
                            outErrorMsg = ex.ToString();
                        errMsgSb.AppendLine($"查询订单商品库存信息==> 参数：orderIds={Join(",", orderIds)}，ownerCode={ownerCode}，fxUserId={fxUserId}，orderItemIds={Join(",", orderItemIds)}，\n异常消息：{ex}");
                        return excludeOrderItemIds;
                    }

                    //2.1 获取有绑定信息的SKU查询库存数量 并且库存状态不是配齐状态
                    var querySku = new List<LogicOrderItemStock>();
                    //2.2 isMutiPack为true时数量以页面传过来为准

                    //二次发货和二次发货打印，不检查StockState 2024-09-10
                    if (type == 3 || type == 4)
                    {
                        //不限制库存状态
                        querySku = items.Item2.Where(x => !string.IsNullOrEmpty(x.WareHouseSkuCode)).ToList();
                    }
                    else
                    {
                        if (isMultiPack)
                        {
                            items.Item2.ForEach(item =>
                            {
                                var exist = orderItems.FirstOrDefault(a => a.LogicOrderItemId == item.LogicOrderItemId);
                                if (exist != null && exist.Quantity > 0)
                                    item.ReserverCount = exist.Quantity;
                            });
                            //不限制库存状态
                            querySku = items.Item2.Where(x => !IsNullOrEmpty(x.WareHouseSkuCode)).ToList();
                        }
                        else
                            querySku = items.Item2.Where(x => !IsNullOrEmpty(x.WareHouseSkuCode) && x.StockState != "all").ToList();
                    }

                    //请求批次号
                    var batchCode = Guid.NewGuid().ToString().ToShortMd5();
                    #endregion
                    if (querySku != null && querySku.Count > 0)
                    {
                        #region 根据SKU列表去查询库存接口，获取对应的库存数量
                        WarehouseProductSkuListRequestV2 request = new WarehouseProductSkuListRequestV2()
                        {
                            OwnerCode = ownerCode,
                            SkuList = querySku.Select(x => x.WareHouseSkuCode).ToList()
                        };
                        var rsp = _warehouseclient.Execute(request);
                        #endregion

                        if (rsp.IsSucc && rsp.Items != null && rsp.Items.Count > 0)
                        {
                            bool allsuccess = true;
                            var wareHouseCode = rsp.Items[0].WareHouseCode;
                            List<Data.FxModel.StockItemError> listError = new List<Data.FxModel.StockItemError>();

                            //3.排除没有库存数据的逻辑订单项
                            var stocksuks = rsp.Items.Select(x => x.WareHouseSkuCode).ToList();
                            var logicOrderItems = new List<Data.FxModel.LogicOrderItemStock>();

                            //不限制库存状态
                            //二次发货和二次发货打印，不检查StockState 2024-09-10
                            if (isMultiPack || type == 3 || type == 4)
                                logicOrderItems = items.Item2.Where(x => stocksuks.Contains(x.WareHouseSkuCode)).ToList();
                            else
                                logicOrderItems = items.Item2.Where(x => stocksuks.Contains(x.WareHouseSkuCode) && x.StockState != "all").ToList();

                            var needOrders = new List<InventoryOrderRequest>();//需要扣库存的订单项
                            var stockType = StockChangeRecordType.OrderDeduction_S.ToString();
                            if (type == 1)
                                stockType = StockChangeRecordType.OrderDeduction_P.ToString();
                            else if (type == 3)
                                stockType = StockChangeRecordType.OrderResendPrint.ToString();
                            else if (type == 4)
                                stockType = StockChangeRecordType.OrderResend.ToString();

                            items.Item1.ForEach(x =>
                            {
                                var ois = logicOrderItems.Where(p => p.LogicOrderId == x.LogicOrderId).ToList();
                                if (ois != null && ois.Count > 0)
                                {
                                    needOrders.Add(new InventoryOrderRequest()
                                    {
                                        FxUserId = fxUserId,
                                        IsAllowNegativeStock = 0,
                                        LogicOrderId = x.LogicOrderId,
                                        PlatformOrderId = x.PlatformOrderId,
                                        SourceFxUserId = x.SourceFxUserId,
                                        SourceShopId = x.SourceShopId,
                                        UpFxUserId = x.UpFxUserId,
                                        StockType = stockType,
                                        Items = ois.Select(p => new InventoryItemRequest
                                        {
                                            DeductionCount = p.ReserverCount,
                                            ExtProperty1 = isMultiPack ? "MultiPack" : "",//多包裹
                                            OrderItemCode = p.OrderItemCode,
                                            WarehouseSkuCode = p.WareHouseSkuCode,
                                            PlatformOrderId = p.PlatformOrderId,
                                            LogicOrderItemId = p.LogicOrderItemId,
                                            SkuName = p.SkuName,
                                            BatchCode = batchCode
                                        }).ToList()
                                    });
                                }
                            });

                            //逐个订单项校验库存
                            List<CheckStockResult> checkStockResultList = new WareHouseCommonService().CheckOrderItemStock(needOrders, rsp.Items, combinationStockType, wareHouseCode, ownerCode);

                            var failList = checkStockResultList.Where(a => a.CheckStatus == 0)?.ToList();
                            if (failList != null && failList.Any())
                            {
                                allsuccess = false;
                                failList.ForEach(b =>
                                {
                                    string pructName = $"{b.SkuName}";
                                    string errorMsg = $"{(b.IsCombinationChild == 1 ? "子货品" : "")}库存：{b.BeforeCount}，商品数量：{b.ChangeCount}，库存不足";
                                    string orderId = $"{b.PlatformOrderId}";

                                    excludeOrderItemIds.Add(b.LogicOrderItemId);

                                    listError.Add(new Data.FxModel.StockItemError()
                                    {
                                        ErrorMsg = errorMsg,
                                        OrderId = orderId,
                                        ProductName = pructName
                                    });
                                });

                                excludeOrderItemIds = excludeOrderItemIds.Distinct().ToList();
                            }

                            if (listError.Any() && !isContinueForStock)
                            {
                                outErrorMsg = listError.ToJson();
                                errMsgSb.AppendLine($"检测到库存不足：{outErrorMsg}");
                            }


                            Log.Debug(() => $"stockFlag={stockFlag},allsuccess={allsuccess}", "stock.txt");
                            //5.执行扣库存
                            if ((allsuccess || isContinueForStock) && stockFlag)
                            {
                                if (isContinueForStock && excludeOrderItemIds.Any())
                                {
                                    //过滤库存不足的订单项
                                    needOrders = new List<InventoryOrderRequest>();

                                    items.Item1.ForEach(x =>
                                    {
                                        var ois = logicOrderItems.Where(p => p.LogicOrderId == x.LogicOrderId && !excludeOrderItemIds.Contains(p.LogicOrderItemId)).ToList();
                                        if (ois != null && ois.Count > 0)
                                        {
                                            needOrders.Add(new InventoryOrderRequest()
                                            {
                                                FxUserId = fxUserId,
                                                IsAllowNegativeStock = 0,
                                                LogicOrderId = x.LogicOrderId,
                                                PlatformOrderId = x.PlatformOrderId,
                                                SourceFxUserId = x.SourceFxUserId,
                                                SourceShopId = x.SourceShopId,
                                                UpFxUserId = x.UpFxUserId,
                                                StockType = stockType,
                                                Items = ois.Select(p => new InventoryItemRequest
                                                {
                                                    DeductionCount = p.ReserverCount,
                                                    ExtProperty1 = isMultiPack ? "MultiPack" : "",//多包裹
                                                    OrderItemCode = p.OrderItemCode,
                                                    WarehouseSkuCode = p.WareHouseSkuCode,
                                                    PlatformOrderId = p.PlatformOrderId,
                                                    LogicOrderItemId = p.LogicOrderItemId,
                                                    SkuName = p.SkuName,
                                                    BatchCode = batchCode
                                                }).ToList()
                                            });
                                        }
                                    });
                                }

                                if (needOrders == null || !needOrders.Any())
                                {
                                    errMsgSb.AppendLine($"所有订单项都无可用库存");
                                    outErrorMsg = "所有订单项都无可用库存";
                                    return excludeOrderItemIds;
                                }

                                #region 库存数量通过，调用订单直接扣减库存接口
                                //组合接口调用请求实体
                                WarehouseOrderStockOutRequestV2 reqeust = new WarehouseOrderStockOutRequestV2()
                                {
                                    OwnerCode = ownerCode,
                                    Orders = needOrders,
                                    CombinationStockType = combinationStockType
                                };

                                //调用接口
                                var response = _warehouseclient.Execute(reqeust);
                                Log.Debug(() => $"调用接口 reqeust={reqeust.ToJson()}，response={response.ToJson()}", "stock.txt");
                                #endregion
                                if (response.IsSucc && response.Orders != null && response.Orders.Count > 0)
                                {
                                    postRequestDatas = needOrders;

                                    #region 订单直接扣减库存返回数据，返回需要处理的错误给前台，没有就进行业务库的操作
                                    //需要处理的错误
                                    var errorOrders = response.Orders.Where(x => x.Items != null && x.Items.Count(p => p.ErrorCode == "NO_AVAILIABLE_STOCKINFO"
                                    || p.ErrorCode == "SKU_DEDUTION_EXECUTE_FALURE" || p.ErrorCode == "STOCK_NOT_ENOUGH") > 0).ToList();
                                    if (errorOrders != null && errorOrders.Count > 0)
                                    {
                                        listError = new List<Data.FxModel.StockItemError>();
                                        foreach (var order in errorOrders)
                                        {
                                            foreach (var erroritem in order.Items)
                                            {
                                                string pructName = $"{items.Item2.FirstOrDefault(x => x.OrderItemCode == erroritem.OrderItemCode)?.ProductName ?? ""}";
                                                string errorMsg = $"{erroritem.ErrorMessage}";
                                                string orderId = $"{order.PlatformOrderId}";

                                                listError.Add(new Data.FxModel.StockItemError()
                                                {
                                                    ErrorMsg = errorMsg,
                                                    OrderId = orderId,
                                                    ProductName = pructName
                                                });
                                            }
                                        }
                                        if (listError.Any()) outErrorMsg = listError.ToJson();
                                    }
                                    else
                                    {
                                        response.Orders?.ForEach(o =>
                                        {
                                            var exist = needOrders.FirstOrDefault(a => a.LogicOrderId == o.LogicOrderId);
                                            if (exist != null)
                                            {
                                                o.SourceShopId = exist.SourceShopId;
                                                o.SourceFxUserId = exist.SourceFxUserId;
                                            }
                                        });
                                        //更新逻辑订单、逻辑订单项 库存状态字段
                                        DoOrderStockStateAndStockRecord(fxUserId, shopId, ownerCode, items, response, type, stockType);
                                    }
                                    #endregion
                                }
                                else
                                {
                                    errMsgSb.AppendLine($"扣库存服务异常：{response.ToJson()}");
                                    Log.WriteError($"扣库存服务无响应：{response.ToJson()}");
                                    outErrorMsg = "库存服务无响应";
                                }
                            }
                        }
                        else
                        {
                            errMsgSb.AppendLine($"查询SKU库存接口异常：{rsp.ToJson()}");
                            Log.WriteError($"库存服务无响应：{rsp.ToJson()}");
                            outErrorMsg = "库存服务无响应";
                        }
                    }
                    else
                    {
                        errMsgSb.AppendLine($"无关联SKU绑定库存==> 参数：orderIds={Join(",", orderIds)}，ownerCode={ownerCode}，fxUserId={fxUserId}，orderItemIds={Join(",", orderItemIds)}，结果：{items?.Item2?.ToJson() ?? "无"}");
                    }
                }
                else
                {
                    errMsgSb.AppendLine($"检查出库标记 ==> 参数：ownerCode={ownerCode}，type={type}，ShopId={shopId}，结果： {isSameStockOutType}={isSameStockOutType}，warehouseIsEnabled={warehouseIsEnabled}");
                }
            }
            catch (Exception ex)
            {
                outErrorMsg = ex.ToString();
                errMsgSb.AppendLine($"扣库存未知异常：{ex}");
            }
            finally
            {
                if (log != null)
                    log.Exception = errMsgSb.ToString();
            }
            return excludeOrderItemIds;
        }

        /// <summary>
        /// 回滚库存
        /// </summary>
        /// <param name="items"></param>
        /// <param name="fxUserId"></param>
        /// <param name="shopId"></param>
        /// <param name="type">1=打单，2=发货，3=二次发货打单，4=二次发货</param>
        /// <param name="log"></param>
        /// <returns>失败的回滚结果</returns>
        public List<OrderStockOutRecollbackModel> OrderStockOutRecollback(List<OrderStockOutRecollbackModel> items, int fxUserId, int shopId, int type = 0, LogForOperator log = null)
        {
            //货主编号
            string ownerCode = GetOwnerCode(fxUserId);
            List<Data.FxModel.StockItemError> listError = new List<Data.FxModel.StockItemError>();
            List<OrderStockOutRecollbackModel> result = new List<OrderStockOutRecollbackModel>();

            var request = new WarehouseOrderStockOutRecollbackRequest()
            {
                OwnerCode = ownerCode,
                Items = items
            };

            //调用接口
            var response = _warehouseclient.Execute(request);

            if (response.IsSucc && response.CheckStatus && response.Items != null && response.Items.Count > 0)
            {
                result = response.Items.Where(x => !x.IsSuccess)?.ToList();

                response.Items.ForEach(item =>
                {
                    var exist = items.FirstOrDefault(a => a.LogicOrderId == item.LogicOrderId);
                    if (exist != null)
                    {
                        item.SourceShopId = exist.SourceShopId;
                        item.SourceFxUserId = exist.SourceFxUserId;
                    }
                });

                //更新逻辑订单项库存状态、库存记录状态
                DoOrderStockStateRecollback(response.Items, type);
            }
            else
            {
                Log.WriteError($"回滚库存服务无响应：{response.ToJson()}");
            }

            return result;
        }

        /// <summary>
        /// 业务系统 更新逻辑订单、逻辑订单项 库存状态字段，新增逻辑订单项库存记录
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopId"></param>
        /// <param name="ownerCode"></param>
        /// <param name="items"></param>
        /// <param name="response"></param>
        /// <param name="type">1=打单，2=发货，3=二次发货打单，4=二次发货</param>
        /// <param name="recordType">出入库类型</param>
        private void DoOrderStockStateAndStockRecord(int fxUserId, int shopId, string ownerCode, Tuple<List<Data.FxModel.LogicOrderStock>, List<Data.FxModel.LogicOrderItemStock>> items, WarehouseOrderStockOutResponse response, int type, string recordType = "")
        {
            //更新逻辑订单、逻辑订单项 库存状态字段
            //二次发货和二次发货打印，不需要更新库存状态字段 2024-09-10
            if (type != 3 && type != 4)
            {
                List<Data.FxModel.LogicOrderStockState> listState = new List<Data.FxModel.LogicOrderStockState>();
                foreach (var _order in response.Orders)
                {
                    string state = "none";
                    int orderstate = 1;
                    foreach (var _item in _order.Items)
                    {
                        string itemstate = "all";
                        if (_item.IsSuccess == false && _item.ErrorCode != "ORDERITEM_ALREADY_RESERVER_CONFIRMED")
                        {
                            itemstate = "none";
                            orderstate = 2;
                        }
                        Data.FxModel.LogicOrderStockState _orderItemItem = new Data.FxModel.LogicOrderStockState()
                        {
                            LogicOrderId = _order.LogicOrderId,
                            OrderType = 2,
                            State = itemstate,
                            OrderItemCode = _item.OrderItemCode,
                            ShopId = _order.SourceShopId,
                            FxUserId = _order.SourceFxUserId,
                        };
                        listState.Add(_orderItemItem);
                    }
                    if (orderstate == 1) state = "all";
                    else if (orderstate == 2) state = "part";
                    Data.FxModel.LogicOrderStockState _orderItem = new Data.FxModel.LogicOrderStockState()
                    {
                        LogicOrderId = _order.LogicOrderId,
                        OrderType = 1,
                        State = state,
                        ShopId = _order.SourceShopId,
                        FxUserId = _order.SourceFxUserId,
                    };
                    listState.Add(_orderItem);
                }
                _logicOrderService.UpdateStockState(listState);
            }
            //新增逻辑订单项库存记录
            List<OrderItemStockRecord> listOrderItemStockRecords = new List<OrderItemStockRecord>();
            foreach (var _order in response.Orders)
            {
                foreach (var _item in _order.Items)
                {
                    if (_item.IsSuccess && IsNullOrEmpty(_item.ErrorCode))
                    {
                        var logicItem = items.Item2.FirstOrDefault(x => x.OrderItemCode == _item.OrderItemCode);
                        listOrderItemStockRecords.Add(new OrderItemStockRecord()
                        {
                            CreateTime = DateTime.Now,
                            FxUserId = fxUserId,
                            LogicOrderId = _order.LogicOrderId,
                            OrderItemCode = _item.OrderItemCode,
                            OwnerCode = ownerCode,
                            PlatformOrderId = _order.PlatformOrderId,
                            ProductCode = logicItem?.ProductCode ?? "",
                            ProductSkuCode = logicItem?.ProductSkuCode ?? "",
                            ShopId = shopId,
                            SourceFxUserId = _order.SourceFxUserId,
                            SourceShopId = _order.SourceShopId,
                            Status = 1,
                            StockChangeCode = _item.StockChangeCode,
                            UpdateTime = DateTime.Now,
                            UpFxUserId = _order.UpFxUserId,
                            WareHouseSkuCode = _item.WarehouseSkuCode,
                            RecordType = recordType
                        });
                    }
                }
            }
            _logicOrderService.AddListOrderItemStockRecord(listOrderItemStockRecords);
        }

        /// <summary>
        /// 更新逻辑订单项库存状态字段，更新逻辑订单项库存记录状态
        /// </summary>
        /// <param name="items"></param>
        /// <param name="type">1=打单，2=发货，3=二次发货打单，4=二次发货</param>
        private void DoOrderStockStateRecollback(List<OrderStockOutRecollbackModel> items, int type)
        {
            //二次发货和二次发货打印，不需要更新库存状态字段 2024-09-10
            if (type != 3 && type != 4)
            {
                List<Data.FxModel.LogicOrderStockState> listState = new List<Data.FxModel.LogicOrderStockState>();
                //1.更新逻辑订单项库存状态
                var successList = items.Where(a => a.IsSuccess)?.ToList();//回滚成功的更新状态
                if (successList.Any())
                {
                    successList.ForEach(item =>
                    {
                        Data.FxModel.LogicOrderStockState _orderItem = new Data.FxModel.LogicOrderStockState()
                        {
                            LogicOrderId = item.LogicOrderId,
                            OrderType = 1,
                            State = "",
                            ShopId = item.SourceShopId,
                            FxUserId = item.SourceFxUserId,
                        };
                        listState.Add(_orderItem);

                        _orderItem = new Data.FxModel.LogicOrderStockState()
                        {
                            LogicOrderId = item.LogicOrderId,
                            OrderType = 2,
                            State = "",
                            OrderItemCode = item.OrderItemCode,
                            ShopId = item.SourceShopId,
                            FxUserId = item.SourceFxUserId,
                        };
                        listState.Add(_orderItem);
                    });
                    _logicOrderService.UpdateStockState(listState);
                }

                //2.更新逻辑订单项库存记录状态
                var stockChangeCodes = successList.Select(a => a.StockChangeCode).ToList();
                _logicOrderService.UpdateOrderItemStockRecord(stockChangeCodes, -1);
            }
        }

        /// 初始化货主
        /// </summary>
        /// <param name="userId">用户id</param>
        /// <param name="userName">用户姓名</param>
        /// <param name="mobile">手机号</param>
        /// <param name="source">货主数据来源：目前仅两个值： FenDanSystem、 PrintSystem</param>
        public string OwnerInit(int userId, string userName, string source)
        {
            WarehouseOwnerInitRequest req = new WarehouseOwnerInitRequest
            {
                OuterId = userId.ToString(),
                Name = userName ?? "",
                Source = source,
            };
            return _warehouseclient.Execute(req).OwnerCode;

        }

        #region 处理订单入库：自动入库、手动入库

        /// <summary>
        /// 
        /// </summary>
        /// <param name="models"></param>
        /// <param name="fxUserId"></param>
        /// <param name="recordMode">出入库方式：0自动；1手动</param>
        /// <returns></returns>
        public ReturnedModel<Dictionary<string, string>> OrderStockIn(List<OrderStockInModel> models, int fxUserId, int recordMode = 1)
        {
            var result = new ReturnedModel<Dictionary<string, string>>();
            if (models == null || models.Count == 0 || fxUserId <= 0)
            {
                result.Success = false;
                result.Message = "数据不能为空";
                return result;
            }

            models.ForEach(m =>
            {
                if (m.FxUserId <= 0)
                    m.FxUserId = fxUserId;
            });

            //售后单入库处理
            if (models.First().From.ToString2().ToLower() == "aftersale")
                result = ProcessStockInForAfterSale(models, fxUserId, recordMode);
            //异常单处理
            else if (models.First().From.ToString2().ToLower() == "orderabnormal")
                result = ProcessStockInForOrderAbnormal(models, fxUserId, recordMode);

            return result;
        }

        /// <summary>
        /// 处理售后入库消息
        /// </summary>
        /// <param name="models"></param>
        public void ProcessStockInFromMessage(List<AfterSaleOrderStockMessageModel> models)
        {
            if (models == null || models.Any() == false)
                return;
            //转换Model
            var orderStockInModels = models.Select(a => new OrderStockInModel
            {
                FxUserId = a.FxUserId,
                AfterSaleCode = a.AfterSaleCode,
                PlatformOrderId = a.PlatformOrderId,
                OrderItemCodes = a.OrderItemCodes
            }).ToList();

            var firstModel = orderStockInModels.First();
            var fxUserId = firstModel.FxUserId;

            #region 业务日志1，处理前的数据
            var methodName = "ProcessStockInFromMessage";
            var businessLogs = new List<BusinessLogModel>
            {
                new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = "",
                    SourceType = "处理售后入库消息-开始",
                    PlatformType = firstModel.PlatformOrderId,
                    CreatorId = fxUserId,
                    FxUserId = fxUserId,
                    BusinessType = BusinessTypes.StockIn.ToString(),
                    SubBusinessType = SubBusinessTypes.StockInFromMessage.ToString(),
                    BusinessId = firstModel.AfterSaleCode,
                    Content = models.ToJson(),
                    DbName = "",
                    Remark = firstModel.OrderAbnormalMatchUniqueCode,
                }
            };
            #endregion

            try
            {
                var key = $"DianGuanJia:FenXiao:ProcessStockInForAfterSale/{firstModel.AfterSaleCode}/{fxUserId}";
                var isLocked = RedisHelper.Exists(key);

                //锁60秒
                if (isLocked == false && RedisHelper.Set(key, DateTime.Now, 60))
                {
                    var result = ProcessStockInForAfterSale(orderStockInModels, fxUserId, 0, fromMessage: true);
                    businessLogs.First().Remark = result.ToJson();
                    //删除锁
                    RedisHelper.Del(key);
                }
                else
                {
                    businessLogs.First().Remark = "当前售后单存在锁";
                }
                //上传日志-TODO:正式上线可移除，只保留异常时的日志
                BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs);
            }
            catch (Exception ex)
            {
                businessLogs.First().ExceptionMessage = ex.Message;
                businessLogs.First().DbName = new AfterSaleOrderRepository().DbConnection?.Database;
                //上传日志
                BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs);

                throw ex;
            }
        }

        /// <summary>
        /// 处理售后单库存入库
        /// </summary>
        /// <param name="models"></param>
        /// <param name="fxUserId"></param>
        /// <param name="recordMode">出入库方式：0自动；1手动</param>
        /// <param name="fromMessage">是否来自消息</param>
        public ReturnedModel<Dictionary<string, string>> ProcessStockInForAfterSale(List<OrderStockInModel> models, int fxUserId, int recordMode, bool fromMessage = false)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var result = new ReturnedModel<Dictionary<string, string>>();
            if (models == null || !models.Any())
            {
                result.Success = false;
                result.Message = "数据为空";
                return result;
            }
            var afterSaleOrderService = new AfterSaleOrderService(_connectionString);
            //查询售后单信息
            var afterSaleCodes = models.Select(a => a.AfterSaleCode).Distinct().ToList();
            var afterSaleItemCodes = models.Where(a => string.IsNullOrEmpty(a.OrderItemCode) == false).Select(a => a.OrderItemCode).Distinct().ToList();
            var fields = new List<string>() { "aso.*", "asoi.*" };
            var afterSaleOrders = afterSaleOrderService.GetListByCodes(afterSaleCodes, fields, whereFieldName: "aso.AfterSaleCode");
            if (afterSaleOrders == null || afterSaleOrders.Any() == false)
            {
                result.Success = false;
                result.Message = "查无相关的售后数据";
                return result;
            }

            var logicOrderService = new LogicOrderService(_connectionString);
            var orderItemCodes = afterSaleOrders.Where(a => a.AfterSaleOrderItems != null).SelectMany(a => a.AfterSaleOrderItems).Select(a => a.OrderItemCode).Distinct().ToList();
            var shopIds = afterSaleOrders.Select(a => a.ShopId).Distinct().ToList();
            var logicOrderPathFlows = logicOrderService.GetLogicOrderPathFlows(shopIds, orderItemCodes);

            //前置检查StockState状态，若不包括“all”或“part”可直接返回
            if (logicOrderPathFlows == null || logicOrderPathFlows.Any(a => a.StockState.ToString2() == "all" || a.StockState.ToString2() == "part") == false)
            {
                result.Success = false;
                result.Message = "前置检查StockState无出库记录";
                return result;
            }

            var wareHouseService = new WareHouseService();
            var stockChangeRecords = new List<StockChangeDetailRecord>();

            if (fromMessage)
            {   
                var platformOrderIds = afterSaleOrders.Select(a => a.PlatformOrderId).ToList();
                #region 前置查询出入库记录（库存系统）
                stockChangeRecords = wareHouseService.QueryStockChangeDetailRecord("", platformOrderIds: platformOrderIds);
                if (stockChangeRecords == null || stockChangeRecords.Count == 0 || stockChangeRecords.Any(a => a.Status == 2 && a.ChangeCount < 0) == false)
                {
                    result.Success = false;
                    result.Message = $"前置查询查无相关的出库记录，platformOrderIds={platformOrderIds.ToJson()}";
                    return result;
                }
                #endregion
            }

            if (afterSaleItemCodes != null && afterSaleItemCodes.Any())
            {
                //只取指定的售后单项
                afterSaleOrders.ForEach(asOrder =>
                {
                    asOrder.AfterSaleOrderItems = asOrder.AfterSaleOrderItems?.Where(a => afterSaleItemCodes.Contains(a.OrderItemCode)).ToList();
                });
            }
            afterSaleOrders = afterSaleOrders.Where(a => a.SourceFlag == 0).ToList();
            if (afterSaleOrders == null || afterSaleOrders.Any() == false)
            {
                result.Success = false;
                result.Message = "查无相关的售后数据";
                return result;
            }

            //查询逻辑单及路径流Code
            //var logicOrderService = new LogicOrderService(_connectionString);
            var pathFlowService = new PathFlowService(_connectionString);
            // 兼容正式冷热分离处理
            var dbConfig = SiteContext.Current.CurrentDbConfig;
            if (dbConfig!=null&&dbConfig.IsUseColdDb)
            {
                logicOrderService=new LogicOrderService(dbConfig.ColdDbConnectionString);
                pathFlowService=new PathFlowService(dbConfig.ColdDbConnectionString);
            }
            var shopService = new ShopService();
            var csService = new CommonSettingService();
            var userFxService = new UserFxService();
            var afterSaleOrderItemStockRecordService = new AfterSaleOrderItemStockRecordService(_connectionString);
                        

            //查询路径流，拿到最后一级（即厂家）FxUserId
            var pathFlowCodes = logicOrderPathFlows.Select(a => a.PathFlowCode).Distinct().ToList();
            var pfs = pathFlowService.GetPathFlowList(pathFlowCodes);
            var supplierFxUserIds = pfs.Where(a => a.PathFlowNodes != null).SelectMany(a => a.PathFlowNodes).Where(a => a.DownFxUserId == 0).Select(a => a.FxUserId).Distinct().ToList();
            var logicOrderIds = logicOrderPathFlows.Select(a => a.LogicOrderId).Distinct().ToList();
            var logicOrders = logicOrderService.GetOnlyLogicOrderByLoIds(logicOrderIds);

            //手动入库，只能是当前用户
            if (recordMode == 1)
                supplierFxUserIds = new List<int> { fxUserId };

            //var supplierUserFxs = new List<UserFx>();
            //if (recordMode == 0 && supplierFxUserIds.Any())
            //{
            //    supplierUserFxs = userFxService.GetsByIds(supplierFxUserIds);
            //}

            //查询最后一级（即厂家）的入库配置信息
            var systemShops = shopService.GetFxSystemShopByFxIdV1(supplierFxUserIds, "s.id,fus.FxUserId AS FxUserIds");
            var systemShopIds = systemShops.Select(a => a.Id).ToList();
            var key = SystemSettingKeys.StockInTypeKey;
            var key2 = SystemSettingKeys.AfterSaleAutoStockInKey;
            var stockInConfigs = csService.GetSettingByShopIds(key, systemShopIds);
            var autoStockInConfigs = csService.GetSettingByShopIds(key2, systemShopIds);

            //入库配置-->对比售后单类型-->确认需要处理的数据
            var needAsItemList = new List<AfterSaleOrderItem>();
            afterSaleOrders.SelectMany(a => a.AfterSaleOrderItems).ToList().ForEach(asItem =>
            {
                var curOrderItemCode = asItem.OrderItemCode;
                var curLogicOrderPathFlow = logicOrderPathFlows.FirstOrDefault(a => a.OrderItemCode == curOrderItemCode);
                if (curLogicOrderPathFlow != null)
                {
                    var curSupplierFxUserId = pfs.FirstOrDefault(a => a.PathFlowCode == curLogicOrderPathFlow.PathFlowCode)?.PathFlowNodes.FirstOrDefault(a => a.DownFxUserId == 0).FxUserId ?? 0;
                    var curSystemShopId = systemShops.FirstOrDefault(a => a.FxUserIds == curSupplierFxUserId)?.Id;
                    var curConfigValue = stockInConfigs.FirstOrDefault(a => a.ShopId == curSystemShopId)?.Value ?? "";
                    var curAutoConfigValue = autoStockInConfigs.FirstOrDefault(a => a.ShopId == curSystemShopId)?.Value ?? "";

                    //手动入库，厂家只能是当前用户
                    if (recordMode == 1 && curSupplierFxUserId != fxUserId)
                        return;

                    // 此处往下开始，自动入库需要切换到厂家的上下文。确保后面的操作都是基于厂家的！
                    // 自动入库只有单条售后单的操作，所以可以放在循环中，如果是多条操作，需要处理！
                    if (recordMode == 0 && models.First().FxUserId != curSupplierFxUserId)
                    {
                        models.First().FxUserId = curSupplierFxUserId;
                        //var curUserFx = supplierUserFxs.FirstOrDefault(a => a.Id == curSupplierFxUserId);
                        //if (curUserFx == null)
                        //{
                        //    result.Success = false;
                        //    result.Message = $"未找到厂家({curSupplierFxUserId})的UserFx";
                        //    return ;
                        //}
                        //new SiteContext(curUserFx);
                        //afterSaleOrderItemStockRecordService = new AfterSaleOrderItemStockRecordService();
                    }

                    //是否要处理
                    var isNeedProcess = false;

                    var asOrder = afterSaleOrders.First(a => a.AfterSaleCode == asItem.AfterSaleCode);

                    //售后状态未完成-不处理
                    if (asOrder.AfterSaleStatus != 5)
                        return;

                    if (recordMode == 1)
                    {
                        //手动入库，和配置无关
                        isNeedProcess = true;
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(curConfigValue))
                            return;

                        //自动入库，但厂家未配置自动入库
                        if (recordMode == 0 && curAutoConfigValue != "1")
                            return;


                        //退货入库配置：待发货退款入库-对应值=1；已发货退款入库-对应值=2；换货入库-对应值=3；退货入库-对应值=4

                        /*
                         /// 售后类型 AfterSaleType
                        /// RETURN_REFUND=0,//退货退款
                        /// ONLY_REFUND=1,//仅退款
                        /// EXCHANGE=2,//换货
                        /// REISSUE=3,//补发
                        /// CHANGE_WAYBILLCODE=4,//变更快递单号
                        /// PRICE_PROTECTION=5,//价保
                        /// OTHER= 99,//其他
                         */

                        var existLogicOrder = logicOrders.FirstOrDefault(a => a.LogicOrderId == curLogicOrderPathFlow.LogicOrderId);
                        //是否发货
                        bool isSended = false;
                        if (existLogicOrder != null)
                        {
                            if (existLogicOrder.PlatformType == PlatformType.TouTiao.ToString() || existLogicOrder.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
                            {
                                isSended = existLogicOrder.OnlineSendTime != null || existLogicOrder.ErpState == "sended" || existLogicOrder.ErpState == "success" || asOrder.AfterSaleStatus == AfterSaleStatusEnum.WAIT_SELLER_CONFIRM_RECEIPT.ToInt() || asOrder.AfterSaleStatus == AfterSaleStatusEnum.SELLER_REFUSED.ToInt() || asOrder.AfterSaleStatus == AfterSaleStatusEnum.EXCHANGE_SELLER_SENDED.ToInt() || asOrder.AfterSaleStatus == AfterSaleStatusEnum.EXCHANGE_BUYER_RECEIVED.ToInt();
                            }
                            else
                            {
                                isSended = existLogicOrder.OnlineSendTime != null || existLogicOrder.ErpState == "sended" || existLogicOrder.ErpState == "success";
                            }
                        }

                        if (asOrder.AfterSaleType == 0 && existLogicOrder != null)
                        {
                            //已发货退款入库
                            if (curConfigValue.Contains("2"))
                                isNeedProcess = true;
                            else
                                isNeedProcess = false;
                        }
                        else if (asOrder.AfterSaleType == 2 && existLogicOrder != null)
                        {
                            //换货入库
                            if (curConfigValue.Contains("3"))
                                isNeedProcess = true;
                            else
                                isNeedProcess = false;
                        }
                        else if (asOrder.AfterSaleType == 1 && existLogicOrder != null)
                        {
                            //待发货退款入库
                            if (isSended == false && curConfigValue.Contains("1"))
                                isNeedProcess = true;
                            //已发货退款入库
                            else if (isSended && curConfigValue.Contains("2"))
                                isNeedProcess = true;
                            else
                                isNeedProcess = false;

                        }
                        else if (curConfigValue.Contains("4"))
                        {
                            //退货入库
                            isNeedProcess = true;
                        }
                    }

                    asItem.SupplierFxUserId = curSupplierFxUserId;
                    asItem.PathFlowCode = curLogicOrderPathFlow.PathFlowCode;
                    asItem.LogicOrderId = curLogicOrderPathFlow.LogicOrderId;

                    if (isNeedProcess)
                        needAsItemList.Add(asItem);
                }
            });

            if (needAsItemList == null || needAsItemList.Any() == false)
            {
                result.Success = false;
                result.Message = "过滤后无可处理的订单";
                return result;
            }

            #region 检查售后单是否存在入库记录（业务系统）
            var errorDic = new Dictionary<string, string>();

            var needAfterSaleCodes = needAsItemList.Select(a => a.AfterSaleCode).Distinct().ToList();
            var needOrderItemCodes = needAsItemList.Select(a => a.OrderItemCode).Distinct().ToList();
            var existRecords = afterSaleOrderItemStockRecordService.GetListForDuplication(needAfterSaleCodes, whereField: "AfterSaleCode");
            existRecords?.ForEach(re =>
            {
                if (needAsItemList.Any(a => a.OrderItemCode == re.OrderItemCode && a.SupplierFxUserId == re.FxUserId))
                {
                    var curKey = re.AfterSaleCode + re.OrderItemCode + re.FxUserId;
                    if (errorDic.ContainsKey(re.AfterSaleId) == false)
                        errorDic.Add(re.AfterSaleId, "业务系统存在入库记录");

                    needAsItemList.Where(a => a.OrderItemCode == re.OrderItemCode && a.SupplierFxUserId == re.FxUserId).ToList().ForEach(a =>
                    {
                        a.IsFilter = true;
                    });
                }
            });

            if (errorDic != null && errorDic.Any())
            {
                result.Success = false;
                result.Message = $"存在已经入过库的数据";
                result.Data = errorDic;

                //手动入库-->直接返回
                if (recordMode != 0)
                    return result;
                else
                {
                    //自动入库-->过滤已有入库记录的数据
                    needAsItemList = needAsItemList.Where(a => a.IsFilter == false).ToList();
                }
            }
            #endregion

            #region 检查是否存在出库记录（库存系统）
            var ownerCode = GetOwnerCode(models.First().FxUserId);
            if (fromMessage == false)
            {
                stockChangeRecords = wareHouseService.QueryStockChangeDetailRecord(ownerCode, orderItemCodes: needOrderItemCodes);
                if (stockChangeRecords == null || stockChangeRecords.Count == 0)
                {
                    result.Success = false;
                    result.Message = $"查无相关的出库记录，FxUserId={models.First().FxUserId}";
                    return result;
                }
            }

            //2、校验及组装请求Request
            var items = new List<OrderStockOutRecollbackModel>();
            var existLogicOrderIds = stockChangeRecords.Where(a => string.IsNullOrEmpty(a.LogicOrderId) == false).Select(a => a.LogicOrderId).ToList();
            needAsItemList.ForEach(exsitItem =>
            {
                //只取出库记录即ChangeCount<0
                var exsitRecords = stockChangeRecords.Where(a => a.OwnerCode == ownerCode && a.OrderItemCode == exsitItem.OrderItemCode && a.Status == 2 && a.ChangeCount < 0).ToList();

                //是否存在入库记录
                var isExsitStockInRecord = stockChangeRecords.Any(a => a.OwnerCode == ownerCode && a.OrderItemCode == exsitItem.OrderItemCode && a.Status == 2 && a.ChangeCount > 0 && a.OriginalStockChangeCode.IsNotNullOrEmpty());
                if (isExsitStockInRecord)
                {
                    if (errorDic.ContainsKey(exsitItem.AfterSaleId) == false)
                        errorDic.Add(exsitItem.AfterSaleId, "存在入库记录");
                    return;
                }

                var exsitModel = models.FirstOrDefault(a => a.OrderItemCode == exsitItem.OrderItemCode && a.AfterSaleCode == exsitItem.AfterSaleCode);
                if (exsitModel == null)
                    exsitModel = models.FirstOrDefault(a => a.AfterSaleCode == exsitItem.AfterSaleCode);
                if (exsitRecords != null && exsitRecords.Any() && exsitModel != null)
                {
                    //自动入库，优先用售后单里的数量，数量也为0，再用出库记录的数量
                    if (exsitModel.ChangeCount == 0 && recordMode == 0)
                    {
                        exsitModel.ChangeCount = exsitItem.AfterSaleCount;
                    }
                    if (exsitModel.ChangeCount == 0 && recordMode == 0)
                    {
                        var childSkuCount = exsitRecords.First().ChildSkuCount;
                        if (childSkuCount <= 0)
                            childSkuCount = 1;
                        exsitModel.ChangeCount = Math.Abs(Convert.ToInt32(exsitRecords.First().ChangeCount / childSkuCount));
                    }

                    var asOrder = afterSaleOrders.First(a => a.AfterSaleCode == exsitItem.AfterSaleCode);
                    var existLogicOrder = logicOrders.FirstOrDefault(a => a.LogicOrderId == exsitItem.LogicOrderId);

                    var recordType = string.Empty;//入库类型

                    //退货入库
                    recordType = StockChangeRecordType.ReturnIn.ToString();

                    if (asOrder.AfterSaleType == 0)
                    {
                        //已发货退款入库
                        recordType = StockChangeRecordType.SendedRefundIn.ToString();
                    }
                    else if (asOrder.AfterSaleType == 2)
                    {
                        //换货入库
                        recordType = StockChangeRecordType.ExchangeIn.ToString();
                    }
                    else if (asOrder.AfterSaleType == 1 && existLogicOrder != null)
                    {
                        //是否发货
                        bool isSended = false;
                        if (existLogicOrder.PlatformType == PlatformType.TouTiao.ToString() || existLogicOrder.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
                        {
                            isSended = existLogicOrder.OnlineSendTime != null || existLogicOrder.ErpState == "sended" || existLogicOrder.ErpState == "success" || asOrder.AfterSaleStatus == AfterSaleStatusEnum.WAIT_SELLER_CONFIRM_RECEIPT.ToInt() || asOrder.AfterSaleStatus == AfterSaleStatusEnum.SELLER_REFUSED.ToInt() || asOrder.AfterSaleStatus == AfterSaleStatusEnum.EXCHANGE_SELLER_SENDED.ToInt() || asOrder.AfterSaleStatus == AfterSaleStatusEnum.EXCHANGE_BUYER_RECEIVED.ToInt();
                        }
                        else
                        {
                            isSended = existLogicOrder.OnlineSendTime != null || existLogicOrder.ErpState == "sended" || existLogicOrder.ErpState == "success";
                        }

                        //待发货退款入库
                        if (isSended == false)
                            recordType = StockChangeRecordType.WaitSendRefundIn.ToString();
                    }

                    //可能多条-->按WareHouseSkuCode分组，取第一条
                    exsitRecords.GroupBy(a => a.WareHouseSkuCode).ToList().ForEach(g =>
                    {
                        var exsitRecord = g.First();

                        var curModel = new OrderStockOutRecollbackModel();

                        curModel.OriginalStockChangeCode = exsitRecord.StockChangeCode;
                        curModel.WarehouseSkuCode = exsitRecord.WareHouseSkuCode;

                        curModel.SourceShopId = exsitItem.ShopId;
                        curModel.PlatformOrderId = exsitItem.PlatformOrderId;
                        curModel.LogicOrderId = exsitItem.LogicOrderId;
                        curModel.OrderItemCode = exsitItem.OrderItemCode;
                        curModel.SupplierFxUserId = exsitItem.SupplierFxUserId;
                        curModel.RecordType = recordType;
                        curModel.SourceFxUserId = exsitRecord.SourceFxUserId;
                        curModel.UpFxUserId = exsitRecord.UpFxUserId;

                        curModel.ChangeCount = exsitModel.ChangeCount;//页面传过来的数量

                        curModel.StockDetailCode = exsitRecord.StockDetailCode;

                        items.Add(curModel);
                    });
                }
                else
                {
                    if (errorDic.ContainsKey(exsitItem.AfterSaleId) == false)
                        errorDic.Add(exsitItem.AfterSaleId, "查无出库记录");
                }
            });

            if (errorDic != null && errorDic.Any())
            {
                result.Success = false;
                result.Message = $"存在异常数据";
                result.Data = errorDic;

                //手动入库-->有异常直接返回；自动入库-->继续
                if (recordMode != 0)
                    return result;
            }
            #endregion

            #region 手动入库：检查库存Sku是否存在
            if (recordMode == 1)
            {
                var failWarehouseSkuCodes = CheckWarehouseSku(items, ownerCode);
                //库存规格不存在，直接返回
                if (failWarehouseSkuCodes != null && failWarehouseSkuCodes.Count > 0)
                {
                    result.Success = false;
                    result.Message = $"库存规格不存在";

                    var dicError = new Dictionary<string, string>();
                    items.Where(a => failWarehouseSkuCodes.Contains(a.WarehouseSkuCode)).ToList().ForEach(m =>
                    {
                        if (dicError.ContainsKey(m.OrderItemCode) == false)
                        {
                            dicError.Add(m.OrderItemCode, "库存规格不存在");
                        }
                    });
                    result.Data = dicError;

                    return result;
                }
            }
            #endregion

            var records = new List<AfterSaleOrderItemStockRecord>();
            var dicFail = new Dictionary<int, string>();

            //按厂家维度调用接口
            items.GroupBy(a => a.SupplierFxUserId).ToList().ForEach(g =>
            {
                var exeResult = wareHouseService.ProcessOrderStockIn(g.ToList(), g.Key, recordMode, false, (response) =>
                {
                    if (response == null || response.Items == null)
                        return;
                    //处理日志
                    var successList = response.Items.Where(a => a.IsSuccess).ToList();
                    successList.ForEach(re =>
                    {
                        var existAsItem = needAsItemList.FirstOrDefault(a => a.LogicOrderId == re.LogicOrderId && a.OrderItemCode == re.OrderItemCode);
                        if (existAsItem != null)
                        {
                            var existAsOrder = afterSaleOrders.First(a => a.AfterSaleCode == existAsItem.AfterSaleCode);
                            var curRecord = new AfterSaleOrderItemStockRecord
                            {
                                AfterSaleCode = existAsItem.AfterSaleCode,
                                AfterSaleId = existAsItem.AfterSaleId,
                                PlatformOrderId = existAsItem.PlatformOrderId,
                                LogicOrderId = existAsItem.LogicOrderId,
                                PathFlowCode = existAsItem.PathFlowCode,
                                SubItemId = existAsItem.SubItemId,
                                OrderItemCode = existAsItem.OrderItemCode,
                                FxUserId = g.Key,
                                ProductId = existAsItem.ProductId,
                                ProductCode = existAsItem.ProductCode,
                                SkuId = existAsItem.SkuId,
                                SkuCode = existAsItem.SkuCode,

                                OriginalStockChangeCode = re.OriginalStockChangeCode,
                                StockDetailCode = re.StockDetailCode,
                                WareHouseSkuCode = re.WarehouseSkuCode,
                                ItemCount = re.ChangeCount,

                                PlatformType = existAsOrder.PlatformType,
                                SourceShopId = existAsOrder.ShopId,
                                SourceFxUserId = existAsOrder.FxUserId,

                                RecordMode = recordMode,
                                Status = 1,
                                CreateTime = DateTime.Now,
                                UpdateTime = DateTime.Now,
                            };
                            records.Add(curRecord);
                        }
                    });
                });

                if (exeResult.Success == false)
                {
                    dicFail.Add(g.Key, exeResult.Message);
                }
            });

            //添加售后库存记录

            if (records.Any())
            {
                afterSaleOrderItemStockRecordService.InsertsForDuplication(records);

                #region 调用同步数据接口服务
                new SyncDataInterfaceService(records.First().FxUserId).AfterSaleOrderItemStockRecordOpt(records, callback: (string targetConnectionString, List<AfterSaleOrderItemStockRecord> targetModels) =>
                {
                    if (targetModels != null && targetModels.Any())
                        new AfterSaleOrderItemStockRecordService(targetConnectionString).InsertsForDuplication(targetModels);
                });
                #endregion

            }

            result.Success = true;
            if (dicFail.Count > 0)
            {
                result.Success = false;
                if (records.Any())
                    result.Message = $"部分处理失败：{string.Join("；", dicFail.Values.ToList())}";
                else
                    result.Message = $"处理失败：{string.Join("；", dicFail.Values.ToList())}";

                var upFxUserIds = dicFail.Keys.ToList();
                var dicError = new Dictionary<string, string>();
                items.Where(a => upFxUserIds.Contains(a.SupplierFxUserId)).ToList().ForEach(m =>
                {
                    if (dicError.ContainsKey(m.OrderItemCode) == false)
                    {
                        dicError.Add(m.OrderItemCode, "处理失败");
                    }
                });
                result.Data = dicError;
            }

            return result;
        }

        /// <summary>
        /// 处理异常单库存入库
        /// </summary>
        /// <param name="models"></param>
        /// <param name="fxUserId"></param>
        /// <param name="recordMode">出入库方式：0自动；1手动</param>
        public ReturnedModel<Dictionary<string, string>> ProcessStockInForOrderAbnormal(List<OrderStockInModel> models, int fxUserId, int recordMode)
        {
            var result = new ReturnedModel<Dictionary<string, string>>();
            if (models == null || !models.Any())
            {
                result.Success = false;
                result.Message = "数据为空";
                return result;
            }

            var ids = models.Where(a => a.OrderAbnormalId > 0).Select(a => a.OrderAbnormalId).ToList();
            var codes = models.Where(a => string.IsNullOrEmpty(a.OrderAbnormalMatchUniqueCode) == false).Select(a => a.OrderAbnormalMatchUniqueCode).ToList();
            if (ids.Any() == false && codes.Any() == false)
            {
                result.Success = false;
                result.Message = "数据为空";
                return result;
            }

            #region 自动入库:配置检查
            if (recordMode == 0)
            {
                var shopService = new ShopService();
                var csService = new CommonSettingService();

                //查询入库配置信息
                var systemShops = shopService.GetFxSystemShopByFxIdV1(new List<int> { fxUserId }, "s.Id,fus.FxUserId AS FxUserIds");
                var systemShopId = systemShops.FirstOrDefault()?.Id ?? 0;
                var key = SystemSettingKeys.StockInTypeKey;
                var key2 = SystemSettingKeys.AfterSaleAutoStockInKey;
                var stockInConfig = csService.Get(key, systemShopId);
                var autoStockInConfig = csService.Get(key2, systemShopId);

                if (autoStockInConfig == null || autoStockInConfig.Value != "1")
                {
                    result.Success = false;
                    result.Message = "未开启自动入库";
                    result.ExtField1 = autoStockInConfig?.ToJson();
                    result.Key = $"autoStockInConfig,fxUserId={fxUserId},systemShopId={systemShopId}";
                    return result;
                }

                if (stockInConfig == null || stockInConfig.Value.Contains("5") == false)
                {
                    result.Success = false;
                    result.Message = "未开启异常单自动入库";
                    result.ExtField1 = stockInConfig?.ToJson();
                    result.Key = $"stockInConfig,fxUserId={fxUserId},systemShopId={systemShopId}";
                    return result;
                }
            }
            #endregion

            var orderAbnormalService = new OrderAbnormalService();
            var wareHouseService = new WareHouseService();

            Tuple<List<OrderAbnormal>, List<StockChangeDetailRecord>> tuple = null;
            if (ids != null && ids.Any())
                tuple = orderAbnormalService.GetListByIds(ids, fxUserId);
            else if (codes != null && codes.Any())
                tuple = orderAbnormalService.GetListByCodes(codes, fxUserId);

            if (tuple == null || tuple.Item1 == null || tuple.Item1.Any() == false)
            {
                result.Success = false;
                result.Message = "数据为空";
                result.ExtField1 = codes?.ToJson();
                result.Remark = ids?.ToJson();
                result.Key = fxUserId.ToString();
                return result;
            }

            if (recordMode == 1 && tuple.Item1.Any(a => a.IsHasStockInRecord.HasValue && a.IsHasStockInRecord.Value == true))
            {
                result.Success = false;
                result.Message = "存在已入库的数据";
                return result;
            }

            //不存在
            var failMatchUniqueCodes = new List<string>();
            //2、校验及组装请求Request
            var items = new List<OrderStockOutRecollbackModel>();
            var needList = tuple.Item1.Where(a => a.IsShowStockIn.HasValue && a.IsShowStockIn.Value == true && a.IsHasStockInRecord.HasValue && a.IsHasStockInRecord.Value == false).SelectMany(a => a.Items).ToList();
            Log.Debug(() => $"fxUserI{fxUserId},tuple={tuple?.ToJson()},needList={needList?.ToJson()}", $"stock2.txt");
            needList.ForEach(exsitItem =>
            {
                OrderStockInModel exsitStockInModel = null;
                if (exsitItem.OrderAbnormalId > 0)
                    exsitStockInModel = models.FirstOrDefault(a => a.OrderAbnormalId == exsitItem.OrderAbnormalId);

                if (exsitStockInModel == null && string.IsNullOrEmpty(exsitItem.OrderAbnormalMatchUniqueCode) == false)
                    exsitStockInModel = models.FirstOrDefault(a => a.OrderAbnormalMatchUniqueCode == exsitItem.OrderAbnormalMatchUniqueCode);

                Log.Debug(() => $"fxUserId={fxUserId},exsitItem={exsitItem.ToJson()},exsitStockInModel={exsitStockInModel?.ToJson()}", $"stock2.txt");
                if (exsitStockInModel == null)
                {
                    failMatchUniqueCodes.Add(exsitItem.OrderAbnormalMatchUniqueCode);
                    return;
                }

                //只取出库记录即ChangeCount<0
                var exsitRecords = tuple.Item2.Where(a => a.OrderItemCode == exsitItem.OrderItemCode && a.Status == 2 && a.ChangeCount < 0).ToList();

                Log.Debug(() => $"fxUserId={fxUserId},exsitRecords={exsitRecords?.ToJson()}", $"stock2.txt");
                if (exsitRecords != null && exsitRecords.Any())
                {
                    var recordType = StockChangeRecordType.AbnormalOrderIn.ToString();//入库类型

                    //可能多条-->按WareHouseSkuCode分组，取第一条
                    exsitRecords.GroupBy(a => a.WareHouseSkuCode).ToList().ForEach(g =>
                    {
                        var exsitRecord = g.First();

                        var curModel = new OrderStockOutRecollbackModel();

                        curModel.OriginalStockChangeCode = exsitRecord.StockChangeCode;
                        curModel.WarehouseSkuCode = exsitRecord.WareHouseSkuCode;
                        curModel.SourceShopId = exsitRecord.SourceShopId;
                        curModel.SourceFxUserId = exsitRecord.SourceFxUserId;
                        curModel.UpFxUserId = exsitRecord.UpFxUserId;

                        curModel.PlatformOrderId = exsitItem.PlatformOrderId;
                        curModel.LogicOrderId = exsitItem.LogicOrderId;
                        curModel.OrderItemCode = exsitItem.OrderItemCode;
                        curModel.SupplierFxUserId = exsitItem.FxUserId;
                        curModel.RecordType = recordType;

                        if (exsitStockInModel.ChangeCount > 0)
                            curModel.ChangeCount = exsitStockInModel.ChangeCount;//页面传过来的数量
                        else
                            curModel.ChangeCount = exsitItem.OriginalStockCount;//原数据库的数量

                        curModel.StockDetailCode = exsitRecord.StockDetailCode;

                        items.Add(curModel);
                    });
                }
            });

            #endregion

            Log.Debug(() => $"fxUserId={fxUserId},items={items?.ToJson()}", $"stock2.txt");

            #region 手动入库：检查库存Sku是否存在
            if (recordMode == 1)
            {
                var ownerCode = GetOwnerCode(models.First().FxUserId);
                var failWarehouseSkuCodes = CheckWarehouseSku(items, ownerCode);
                //库存规格不存在，直接返回
                if (failWarehouseSkuCodes != null && failWarehouseSkuCodes.Count > 0)
                {
                    result.Success = false;
                    result.Message = $"库存规格不存在";

                    var dicError = new Dictionary<string, string>();
                    items.Where(a => failWarehouseSkuCodes.Contains(a.WarehouseSkuCode)).ToList().ForEach(m =>
                    {
                        if (dicError.ContainsKey(m.OrderItemCode) == false)
                        {
                            dicError.Add(m.OrderItemCode, "库存规格不存在");
                        }
                    });
                    result.Data = dicError;

                    return result;
                }
            }
            #endregion

            var successMatchUniqueCodes = new List<string>();
            var dicFail = new Dictionary<int, string>();
            //按厂家维度调用接口
            items.GroupBy(a => a.SupplierFxUserId).ToList().ForEach(g =>
            {
                var exeResult = wareHouseService.ProcessOrderStockIn(g.ToList(), g.Key, recordMode, false, (response) =>
                {
                    if (response == null || response.Items == null)
                        return;
                    //处理日志
                    var successList = response.Items.Where(a => a.IsSuccess).ToList();
                    successList.ForEach(re =>
                    {
                        var existAsItem = needList.FirstOrDefault(a => a.LogicOrderId == re.LogicOrderId && a.OrderItemCode == re.OrderItemCode);
                        if (existAsItem != null)
                        {
                            successMatchUniqueCodes.Add(existAsItem.OrderAbnormalMatchUniqueCode);
                        }
                    });
                });

                if (exeResult.Success == false)
                {
                    dicFail.Add(g.Key, exeResult.Message);
                }
            });

            //更新异常单的库存状态
            if (successMatchUniqueCodes.Any())
                orderAbnormalService.UpdateStockState(successMatchUniqueCodes, 1);

            result.Remark = failMatchUniqueCodes?.ToJson();
            result.ExtField1 = successMatchUniqueCodes?.ToJson();
            result.Success = true;
            if (dicFail.Count > 0)
            {
                result.Success = false;
                if (successMatchUniqueCodes.Any())
                    result.Message = $"部分处理失败：{string.Join("；", dicFail.Values.ToList())}";
                else
                    result.Message = $"处理失败：{string.Join("；", dicFail.Values.ToList())}";

                var upFxUserIds = dicFail.Keys.ToList();
                var dicError = new Dictionary<string, string>();
                items.Where(a => upFxUserIds.Contains(a.SupplierFxUserId)).ToList().ForEach(m =>
                {
                    if (dicError.ContainsKey(m.OrderItemCode) == false)
                    {
                        dicError.Add(m.OrderItemCode, "处理失败");
                    }
                });
                result.Data = dicError;
            }
            return result;
        }

        /// <summary>
        /// 获取库存货品Sku
        /// </summary>
        /// <param name="wareHouseSkuCodes"></param>
        /// <param name="ownerCode"></param>
        /// <returns></returns>
        public List<WareHouseSku> GetWareHouseSkus(List<string> wareHouseSkuCodes, string ownerCode)
        {
            if (wareHouseSkuCodes.IsNullOrEmptyList())
                return new List<WareHouseSku>();
            var getSkuListRequest = new WarehouseSkuGetListRequest
            {
                OwnerCode = ownerCode,
                WareHouseSkuCodes = wareHouseSkuCodes
            };
            var warehouseSkuGetListResponse = _warehouseclient.Execute(getSkuListRequest);
            if (warehouseSkuGetListResponse.IsSucc && warehouseSkuGetListResponse.Skus != null)
                return warehouseSkuGetListResponse.Skus;
            return new List<WareHouseSku>();
        }

        /// <summary>
        /// 检查库存货品Sku
        /// </summary>
        /// <param name="items"></param>
        /// <param name="ownerCode"></param>
        /// <returns></returns>
        public List<string> CheckWarehouseSku(List<OrderStockOutRecollbackModel> items, string ownerCode)
        {
            var failWarehouseSkuCodes = new List<string>();
            if (items == null || !items.Any())
                return failWarehouseSkuCodes;

            var warehouseSkuCodes = items.Select(a => a.WarehouseSkuCode).ToList();
            var getSkuListRequest = new WarehouseSkuGetListRequest
            {
                OwnerCode = ownerCode,
                WareHouseSkuCodes = warehouseSkuCodes
            };

            var warehouseSkuGetListResponse = _warehouseclient.Execute(getSkuListRequest);
            Log.Debug(() => $"CheckWarehouseSku，getSkuListRequest={getSkuListRequest.ToJson()},warehouseSkuGetListResponse={warehouseSkuGetListResponse?.ToJson()}", "stock2.txt");
            if (warehouseSkuGetListResponse.IsSucc && warehouseSkuGetListResponse.Skus != null)
            {
                warehouseSkuCodes.ForEach(code =>
                {
                    if (warehouseSkuGetListResponse.Skus.Any(a => a.WareHouseSkuCode == code) == false)
                    {
                        failWarehouseSkuCodes.Add(code);
                    }
                });
            }
            return failWarehouseSkuCodes;
        }

        /// <summary>
        /// 处理订单入库
        /// </summary>
        /// <param name="items">所需字段：LogicOrderId、ChangeCount、WarehouseSkuCode、</param>
        /// <param name="fxUserId"></param>
        /// <param name="recordMode">出入库方式：0自动；1手动</param>
        /// <param name="isCheckOriginalRecord">是否要校验原出库记录</param>
        /// <param name="callback"></param>
        /// <returns></returns>
        public ReturnedModel ProcessOrderStockIn(List<OrderStockOutRecollbackModel> items, int fxUserId, int recordMode, bool isCheckOriginalRecord, Action<WarehouseOrderStockInResponse> callback)
        {
            var result = new ReturnedModel();
            if (items == null || items.Count == 0 || fxUserId <= 0)
            {
                result.Success = false;
                result.Message = "数据不能为空";
                return result;
            }

            //货主编号
            string ownerCode = GetOwnerCode(fxUserId);

            if (isCheckOriginalRecord)
            {
                //1、调用库存API查询出库记录
                var logicOrderIds = items.Where(a => string.IsNullOrEmpty(a.LogicOrderId) == false).Select(a => a.LogicOrderId).ToList();
                var stockChangeRecords = QueryStockChangeDetailRecord(ownerCode, logicOrderIds);
                if (stockChangeRecords == null || stockChangeRecords.Count == 0)
                {
                    result.Success = false;
                    result.Message = "查无相关的入库记录";
                    return result;
                }

                //2、校验及组装请求Request
                var errorLogicOrderIds = new List<string>();
                var existLogicOrderIds = stockChangeRecords.Where(a => string.IsNullOrEmpty(a.LogicOrderId) == false).Select(a => a.LogicOrderId).ToList();
                logicOrderIds.ForEach(logicOrderId =>
                {
                    var exsitItem = items.FirstOrDefault(a => a.LogicOrderId == logicOrderId);
                    //只取出库记录即ChangeCount<0
                    var exsitRecord = stockChangeRecords.FirstOrDefault(a => a.LogicOrderId == logicOrderId && a.Status == 2 && a.ChangeCount < 0);
                    if (exsitItem != null && exsitRecord != null)
                    {
                        exsitItem.OriginalStockChangeCode = exsitRecord.StockChangeCode;
                        if (string.IsNullOrEmpty(exsitItem.PlatformOrderId))
                            exsitItem.PlatformOrderId = exsitRecord.PlatformOrderId;
                        if (string.IsNullOrEmpty(exsitItem.OrderItemCode))
                            exsitItem.OrderItemCode = exsitRecord.OrderItemCode;
                        if (string.IsNullOrEmpty(exsitItem.StockDetailCode))
                            exsitItem.StockDetailCode = exsitRecord.StockDetailCode;
                        if (exsitItem.ChangeCount == 0)
                            exsitItem.ChangeCount = exsitRecord.ChangeCount;
                    }
                    else
                    {
                        errorLogicOrderIds.Add(logicOrderId);
                    }
                });
                if (errorLogicOrderIds != null && errorLogicOrderIds.Any())
                {
                    result.Success = false;
                    result.Message = $"{string.Join(",", errorLogicOrderIds)}查无相关的入库记录";
                    return result;
                }
            }

            var request = new WarehouseOrderStockInRequest()
            {
                OwnerCode = ownerCode,
                Items = items,
                RecordMode = recordMode,
            };

            //3、调用订单入库API，执行入库
            var response = _warehouseclient.Execute(request);

            Log.Debug(() => $"request={request.ToJson()},response={response?.ToJson()}", "stock2.txt");

            //4、结果回传给外部处理
            if (response != null)
                callback(response);

            if (response.IsSucc == false)
            {
                result.Success = false;
                result.Message = $"{response.Message?.Replace("SKU_NOT_EXISIT", "SKU不存在")}";
                return result;
            }

            return result;
        }

        /// <summary>
        /// 查询库存变更记录
        /// </summary>
        /// <param name="ownerCode"></param>
        /// <param name="logicOrderIds"></param>
        /// <param name="orderItemCodes"></param>
        /// <returns></returns>
        public List<StockChangeDetailRecord> QueryStockChangeDetailRecord(string ownerCode, List<string> logicOrderIds = null, List<string> orderItemCodes = null, List<string> platformOrderIds = null)
        {
            List<Data.FxModel.StockItemError> listError = new List<Data.FxModel.StockItemError>();
            var result = new List<StockChangeDetailRecord>();

            var request = new WarehouseStockChangeDetailRecordRequest()
            {
                OwnerCode = ownerCode,
                LogicOrderIds = logicOrderIds,
                OrderItemCodes = orderItemCodes,
                PlatformOrderIds = platformOrderIds
            };

            //调用接口
            var response = _warehouseclient.Execute(request);
            if (response.IsSucc && response.Items != null && response.Items.Count > 0)
            {
                result = response.Items;
            }
            else
            {
                if (response.IsSucc == false)
                    Log.WriteError($"查询异常：{response.ToJson()}");
            }

            return result;
        }

        #endregion

        #region 列表数据相关
        /// <summary>
        /// 获取实时库存列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public WarehouseStockListResponse LoadStockDetailList(WarehouseStockListRequest request)
        {
            //临时做一下处理，此处做转换链接处理
            var response = _warehouseclient.Execute(request);
            List<SkuStockDetailResponse> SkuStockDetails = response.SkuStockDetails;
            SkuStockDetails.ForEach(detail =>
            {
                if (!string.IsNullOrEmpty(detail.ImageUrl) && (detail.ImageUrl.Contains("fx-baseproduct") || detail.ImageUrl.Contains("fx-aftersale")))
                {
                    detail.ImageUrl = CommUtls.GetSplicingEncryptUrl(detail.ImageUrl);
                }
                if (!string.IsNullOrEmpty(detail.ProductImageUrl) && (detail.ProductImageUrl.Contains("fx-baseproduct") || detail.ProductImageUrl.Contains("fx-aftersale")))
                {
                    detail.ProductImageUrl = CommUtls.GetSplicingEncryptUrl(detail.ProductImageUrl);
                }
            });
            response.SkuStockDetails = SkuStockDetails;
            return response;
        }

        /// <summary>
        /// 获取变更记录列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public WarehouseStockRecordListResponse LoadChangeDetailList(WarehouseStockRecordListRequest request)
        {
            return _warehouseclient.Execute(request);
        }
        #endregion

        #region 货品相关
        public WarehouseProductAddResponse WarehouseProductAdd(WarehouseProductAddRequest req)
        {
            req.OwnerCode = GetOwnerCode();
            req.ShortName = "";
            req.ImageUrl = req.GetDecryptUrl() ?? "";
            req.CargoNumber = req.CargoNumber ?? "";

            if (req.Skus != null)
            {
                foreach (var item in req.Skus)
                {
                    item.SkuProperty = item.SkuName ?? "";
                    item.ItemType = "";
                    item.ShortName = "";
                    item.SkuBarCode = "";
                    item.ImageUrl = item.GetDecryptUrl() ?? "";
                    item.SkuName = "";
                    item.SkuCargoNumber = IsNullOrEmpty(item.SkuCargoNumber) ? Guid.NewGuid().ToString().ToShortMd5() : item.SkuCargoNumber;
                }
            }

            var result = _warehouseclient.Execute(req);

            #region 生成基础商品数据

            if (result.IsSucc)
            {
                Task.Run(() =>
                {
                    try
                    {
                        // 根据Code获取商品信息，并更新到商品表
                        var curFxUserId = SiteContext.Current.CurrentFxUserId;
                        PushToBaseProduct(new List<string> { result.WarehouseProductCode }, req.OwnerCode, curFxUserId);
                    }
                    catch (Exception e)
                    {
                        Log.WriteError($"生成基础商品数据出错：{e.ToJson()}");
                    }
                });
            }

            #endregion

            return result;
        }

        /// <summary>
        /// 合并商品（存在更新，不存在添加）
        /// </summary>
        /// <param name="models"></param>
        /// <param name="ownerCode"></param>
        public WarehouseProductMergerResponse WarehouseProductMerger(List<WareHouseProduct> models, string ownerCode = null)
        {
            if (models == null || models.Any() == false)
                return new WarehouseProductMergerResponse();
            ownerCode = ownerCode ?? GetOwnerCode();
            models.ForEach(model =>
            {
                model.OwnerCode = ownerCode;
                if (model.Skus != null)
                {
                    foreach (var item in model.Skus)
                    {
                        item.OwnerCode = ownerCode;
                    }
                }
            });

            if (models.Any(a => a.CargoNumber.IsNullOrEmpty()))
                return new WarehouseProductMergerResponse() { Code = "400", Message = "货品编码不能为空", OwnerCode = ownerCode };

            var req = new WarehouseProductMergerRequest
            {
                OwnerCode = ownerCode,
                Products = models
            };
            Log.Debug(() => $"合并库存商品请求：{req.ToJson()}");
            var result = _warehouseclient.Execute(req);
            Log.Debug(() => $"基础商品库-->库存系统：{result.ToJson()}");
            return result;
        }

        #region 基础商品相关接口

        /// <summary>
        /// 基础商品初始化
        /// </summary>
        /// <param name="modelList"></param>
        public void BaseProdcutInfoInit(List<OwnCodeWithUserIdModel> modelList)
        {
            // 并发处理, 并发度为5
            var paraNum = new ParallelOptions { MaxDegreeOfParallelism = 5 };
            if (CustomerConfig.IsDebug) paraNum.MaxDegreeOfParallelism = 1;
            var _warehouseOwnerRepository = new WareHouseOwnerRepository();

            Parallel.ForEach(modelList, paraNum, model =>
            {
                var req = new WarehouseProductCodeListGetRequest { OwnerCode = model.OwnerCode };
                var result = _warehouseclient.Execute(req);

                if (!result.IsSucc) return;
                var codeList = result.WarehouseProductCodes;
                if (!codeList.Any()) return;

                try
                {
                    PushToBaseProduct(codeList, model.OwnerCode, model.FxuserId, true);
                    _warehouseOwnerRepository.ChangeIsPush(model.OwnerCode);
                }
                catch (Exception e)
                {
                    Log.WriteError($"基础商品初始化出错: FxUserId[{model.FxuserId}]  OwnerCode[{model.OwnerCode}], 错误信息：{e.ToJson()}", "BaseProductInit.txt");
                }
            });
        }

        /// <summary>
        /// 推送至基础商品
        /// </summary>
        public List<BaseProductEntity> PushToBaseProduct(List<string> warehouseProductCodes, string ownerCode, int curFxUserId, bool isInit = false)
        {
            var request = new WarehouseProductGetRequest()
            {
                WareHouseProductCodes = warehouseProductCodes,
                OwnerCode = ownerCode
            };

            var result = _warehouseclient.Execute(request);
            if (!result.IsSucc) return null;

            // 以货品编号分组
            var productsByCargoNumber = result.WarehouseProducts.GroupBy(x => x.CargoNumber).ToList();
            var baseProductList = new List<BaseProductEntity>();
            var baseProductSkuList = new List<BaseProductSku>();
            var baseProductSkuAttributeList = new List<BaseProductSkuAttribute>();
            var baseProductMainImgList = new List<BaseProductImage>();

            // 更新的实体列表
            var baseProductImageUpdateList = new List<BaseProductImage>();
            var baseProductUpdateList = new List<BaseProductEntity>();
            var baseProductSkuUpdateList = new List<BaseProductSku>();
            var baseProductSkuAttributeUpdateList = new List<BaseProductSkuAttribute>();

            var ossService = new OssObjectService();
            // 计算商品以及商品规格数量
            var sum = productsByCargoNumber.Count + productsByCargoNumber.Sum(x => x.Max(a => a.Skus.Count));

            // 获取总数量的Uid
            var uniqueIdList = new ProductDbConfigRepository().BaseProductSystemUniqueId("", curFxUserId, sum);
            var index = 0;

            var _baseProductEntityService = new BaseProductEntityService(curFxUserId);
            var _baseProductSkuService = new BaseProductSkuService(curFxUserId);
            var _baseProductSkuAttributeService = new BaseProductSkuAttributeService(curFxUserId);

            var _baseProductRepository = new BaseProductRepository(curFxUserId);
            var _baseProductSkuRepository = new BaseProductSkuRepository(curFxUserId);
            var _baseProductImageRepository = new BaseProductImageRepository(curFxUserId);

            // 获取已存在的基础商品信息
            var existProductList = _baseProductEntityService.GetList(productsByCargoNumber.Select(x => x.Key).ToList());
            if (existProductList.Any()) existProductList = existProductList.Where(x => x.FxUserId == curFxUserId && x.Status == 1).ToList();

            // 获取已存在的基础商品规格信息
            var existProductSkuList = _baseProductSkuService.GetList(existProductList.Select(x => x.Uid.ToString()).ToList(), whereFieldName: "BaseProductUid");
            if (existProductSkuList.Any()) existProductSkuList = existProductSkuList.Where(x => x.FxUserId == curFxUserId && x.Status == 1).ToList();

            // 获取已存在的基础商品规格属性信息
            var existProductSkuAttributeList = new List<BaseProductSkuAttribute>();
            if (existProductSkuList.Any()) existProductSkuAttributeList = _baseProductSkuAttributeService.GetList(existProductSkuList.Select(x => x.Uid).ToList(), whereFieldName: "SkuUid");
            var dtNow = DateTime.Now;

            productsByCargoNumber.ForEach(x =>
            {
                var product = x.FirstOrDefault(pro => !pro.IsDeleted);
                if (product == null) return;
                var baseProductEntity = existProductList.Find(p => p.SpuCode == product.CargoNumber);

                // 组合货品不进行处理
                // 移除限制条件：组合货品也要转换 2024-08-29
                //if (product.Skus.Any() && product.Skus.First().ItemType == "ZH") return;

                // 相同货品编码合并商品规格信息
                var products = x.ToList();
                products.Remove(product);
                if (products.Count > 0) products.ForEach(pro => product.Skus.AddRange(pro.Skus));

                // 存在实体，进行更新
                if (baseProductEntity != null)
                {
                    // 更新商品信息
                    baseProductEntity.Subject = product.Name;
                    baseProductEntity.AttributeNames = "无规格";
                    baseProductEntity.UpdateTime = dtNow;
                    baseProductEntity.WareHouseProductCode = product.WareHouseProductCode;

                    var baseProductMainImg = _baseProductImageRepository
                        .GetListByIds(new List<long> { baseProductEntity.MainImageObjectId }).FirstOrDefault();

                    // 找到对应的主图信息，并修改
                    if (product.ImageUrl != null && baseProductMainImg != null && product.ImageUrl != baseProductMainImg.Url)
                    {
                        var img = GeneratorImg(product.ImageUrl, isInit, baseProductEntity.Uid, ossService, out _);
                        if (img != null)
                        {
                            baseProductMainImg = img;
                            baseProductEntity.MainImageObjectId = img.ImageObjectId;
                            baseProductImageUpdateList.Add(baseProductMainImg);
                        }
                    }
                    else if (baseProductEntity.MainImageObjectId != 0) // 不存在图片信息，需要删除
                    {
                        if (baseProductMainImg != null)
                        {
                            baseProductMainImg.Status = 0;
                            baseProductImageUpdateList.Add(baseProductMainImg);
                        }

                        baseProductEntity.MainImageObjectId = 0;
                    }

                    baseProductUpdateList.Add(baseProductEntity);

                    // 存在规格维度
                    var baseProductSkus = existProductSkuList.FindAll(sku => sku.BaseProductUid == baseProductEntity.Uid);
                    product.Skus.ForEach(sku =>
                    {
                        var attrJson = new List<Dictionary<string, string>>
                                { new Dictionary<string, string> { { "k", "无规格" }, { "v", sku.SkuProperty } } }.ToJsonExt();

                        // 更新已有的商品规格信息
                        var baseSku = baseProductSkus.FirstOrDefault(bSku => bSku.SkuCode == sku.SkuCargoNumber);
                        if (baseSku != null)
                        {
                            baseSku.IsCombineSku = sku.ItemType == "ZH";
                            baseSku.ShortTitle = sku.ShortName;
                            baseSku.CostPrice = sku.CostPrice;
                            baseSku.Subject = sku.SkuName;
                            baseSku.UpdateTime = dtNow;
                            baseSku.Attributes = attrJson;
                            baseSku.Status = sku.Status == "Enabled" ? 1 : 0;
                            baseSku.WareHouseSkuCode = sku.WareHouseSkuCode;
                            baseSku.WareHouseProductCode = sku.WareHouseProductCode;

                            // 目标图片地址不为空
                            if (sku.ImageUrl.IsNotNullOrEmpty())
                            {
                                var img = GeneratorImg(sku.ImageUrl, isInit, baseSku.BaseProductUid, ossService, out var newImgUrl);
                                if (img != null)
                                {
                                    baseSku.ImageUrl = newImgUrl;
                                    baseSku.ImageObjectId = img.ImageObjectId;
                                }
                            }
                            else // 不存在目标图片地址
                            {
                                if (baseSku.ImageObjectId != 0)
                                {
                                    baseSku.ImageObjectId = 0;
                                    baseSku.ImageUrl = string.Empty;
                                }
                            }

                            var baseProductSkuAttribute = existProductSkuAttributeList.FirstOrDefault(attr => attr.SkuUid == baseSku.Uid);
                            // 更新规格属性
                            if (baseProductSkuAttribute != null)
                            {
                                baseProductSkuAttribute.SetAttribute("无规格", sku.SkuProperty);
                                if (baseSku.ImageObjectId != 0) baseProductSkuAttribute.ImageObjectId = baseSku.ImageObjectId;
                                baseProductSkuAttributeUpdateList.Add(baseProductSkuAttribute);
                            }
                            else // 不存在属性信息，新增
                            {
                                var skuAttr = new BaseProductSkuAttribute
                                {
                                    ProductUid = baseProductEntity.Uid,
                                    SkuUid = baseSku.Uid,
                                    AttributeName1 = "无规格",
                                    AttributeValue1 = sku.SkuProperty,
                                    CreateTime = dtNow
                                };

                                if (baseSku.ImageObjectId != 0) skuAttr.ImageObjectId = baseSku.ImageObjectId;
                                baseProductSkuAttributeList.Add(skuAttr);
                            }

                            baseProductSkuUpdateList.Add(baseSku);
                            if (isInit) PushBaseProductRelation(product.OwnerCode, sku.WareHouseSkuCode, sku.SkuCargoNumber, curFxUserId, baseProductEntity.Uid, baseSku.Uid);
                        }
                        else
                        {
                            // 若没有，则新增
                            var skuEnt = new BaseProductSku
                            {
                                SkuCode = sku.SkuCargoNumber,
                                FxUserId = baseProductEntity.FxUserId,
                                BaseProductUid = baseProductEntity.Uid,
                                Subject = sku.SkuName,
                                CostPrice = sku.CostPrice,
                                ShortTitle = sku.ShortName,
                                Uid = (2 + uniqueIdList[index++]).ToLong(),
                                IsCombineSku = sku.ItemType == "ZH",
                                CreateTime = dtNow,
                                UpdateTime = dtNow,
                                Attributes = attrJson,
                                WareHouseProductCode = sku.WareHouseProductCode,
                                WareHouseSkuCode = sku.WareHouseSkuCode
                            };

                            var skuAttr = new BaseProductSkuAttribute
                            {
                                ProductUid = baseProductEntity.Uid,
                                SkuUid = skuEnt.Uid,
                                AttributeName1 = "无规格",
                                AttributeValue1 = sku.SkuProperty,
                                CreateTime = dtNow
                            };
                            baseProductSkuAttributeList.Add(skuAttr);

                            if (sku.ImageUrl.IsNotNullOrEmpty())
                            {
                                var img = GeneratorImg(sku.ImageUrl, isInit, skuEnt.BaseProductUid, ossService, out var newImgUrl);
                                if (img != null)
                                {
                                    skuEnt.ImageUrl = newImgUrl;
                                    skuEnt.ImageObjectId = img.ImageObjectId;
                                    skuAttr.ImageObjectId = img.ImageObjectId;
                                }
                            }

                            baseProductSkuList.Add(skuEnt);
                            if (isInit) PushBaseProductRelation(product.OwnerCode, sku.WareHouseSkuCode, sku.SkuCargoNumber, curFxUserId, baseProductEntity.Uid, skuEnt.Uid);
                        }
                    });
                }
                else
                {
                    // 新增商品信息
                    var ent = new BaseProductEntity
                    {
                        SpuCode = product.CargoNumber,
                        CreateFrom = "Copy",
                        FxUserId = curFxUserId,
                        CreateFxUserId = curFxUserId,
                        Subject = product.Name,
                        Uid = (1 + uniqueIdList[index++]).ToLong(),
                        CreateTime = dtNow,
                        UpdateTime = dtNow,
                        AttributeNames = "无规格",
                        SkuModeType = isInit ? 1 : 0, //库存转基础商品，规格模式为：自定义
                        WareHouseProductCode = product.WareHouseProductCode
                    };

                    // 针对图片URL进行处理，获取图片内容推送至OSS，记录相关信息
                    if (product.ImageUrl.IsNotNullOrEmpty())
                    {
                        var img = GeneratorImg(product.ImageUrl, isInit, ent.Uid, ossService, out _);
                        if (img != null)
                        {
                            ent.MainImg = img;
                            ent.MainImageObjectId = img.ImageObjectId;
                            baseProductMainImgList.Add(img);
                        }
                    }
                    baseProductList.Add(ent);

                    // 存在规格维度
                    if (product.Skus.Any())
                    {
                        product.Skus.ForEach(sku =>
                        {
                            var attrJson = new List<Dictionary<string, string>>
                                { new Dictionary<string, string> { { "k", "无规格" }, { "v", sku.SkuProperty } } }.ToJsonExt();

                            var skuEnt = new BaseProductSku
                            {
                                SkuCode = sku.SkuCargoNumber,
                                FxUserId = ent.FxUserId,
                                BaseProductUid = ent.Uid,
                                Subject = sku.SkuName,
                                CostPrice = sku.CostPrice,
                                ShortTitle = sku.ShortName,
                                Uid = (2 + uniqueIdList[index++]).ToLong(),
                                IsCombineSku = sku.ItemType == "ZH",
                                CreateTime = dtNow,
                                UpdateTime = dtNow,
                                Attributes = attrJson,
                                AttributeValue = sku.SkuProperty,
                                WareHouseProductCode = sku.WareHouseProductCode,
                                WareHouseSkuCode = sku.WareHouseSkuCode
                            };

                            var skuAttr = new BaseProductSkuAttribute
                            {
                                ProductUid = ent.Uid,
                                SkuUid = skuEnt.Uid,
                                AttributeName1 = "无规格",
                                AttributeValue1 = sku.SkuProperty,
                                CreateTime = dtNow
                            };

                            baseProductSkuAttributeList.Add(skuAttr);

                            if (sku.ImageUrl.IsNotNullOrEmpty())
                            {
                                var img = GeneratorImg(sku.ImageUrl, isInit, skuEnt.BaseProductUid, ossService, out var newImgUrl);
                                if (img != null)
                                {
                                    skuEnt.ImageUrl = newImgUrl;
                                    skuEnt.ImageObjectId = img.ImageObjectId;
                                    skuAttr.ImageObjectId = img.ImageObjectId;
                                }
                            }

                            baseProductSkuList.Add(skuEnt);
                            if (isInit) PushBaseProductRelation(product.OwnerCode, sku.WareHouseSkuCode, sku.SkuCargoNumber, curFxUserId, ent.Uid, skuEnt.Uid);
                        });
                    }
                }
            });

            try
            {
                // 批量添加各实体列表
                new BaseProductSkuAttributeService().BatchAdd(baseProductSkuAttributeList);
                _baseProductSkuRepository.BatchAdd(baseProductSkuList);
                _baseProductRepository.BatchAdd(baseProductList);
                _baseProductImageRepository.BatchAdd(baseProductMainImgList);

                // 批量更新各实体列表
                new BaseProductSkuAttributeService().BulkUpdate(baseProductSkuAttributeUpdateList);
                _baseProductSkuRepository.BulkUpdate(baseProductSkuUpdateList);
                _baseProductImageRepository.BulkUpdate(baseProductImageUpdateList);
                _baseProductRepository.BulkUpdate(baseProductUpdateList);
            }
            catch (Exception e)
            {
                Log.WriteError($"基础商品推送时出错: FxUserId[{curFxUserId}]  OwnerCode[{ownerCode}], 错误信息：{e.ToJson()}", "PushBaseProduct.txt");
            }

            return baseProductList;
        }

        /// <summary>
        /// 生成oss对象并返回图片实体
        /// </summary>
        /// <param name="imgUrl"></param>
        /// <param name="isInit"></param>
        /// <param name="uid"></param>
        /// <param name="ossService"></param>
        /// <param name="newImgUrl"></param>
        /// <returns></returns>
        private static BaseProductImage GeneratorImg(string imgUrl, bool isInit, long uid, OssObjectService ossService, out string newImgUrl)
        {
            var ossEnt = ossService.ImgUrlToOssObject(imgUrl, isInit);
            newImgUrl = ossEnt?.Url;
            if (ossEnt == null) return null;

            var pathStr = ImgHelper.SplitImageUrl(ossEnt.Url);
            var pathModel = pathStr.ToObject<BaseProductImageModel>();
            if (pathModel == null) return null;

            var img = BaseProductEntityService
                .GetAddImagesFromModel(new List<BaseProductImageModel> { pathModel }, uid)
                .FirstOrDefault();

            return img;
        }

        /// <summary>
        /// 推送基础商品关联信息到分库与业务库
        /// </summary>
        /// <param name="ownerCode"></param>
        /// <param name="wareHouseSkuCode"></param>
        /// <param name="skuCargoNumber"></param>
        /// <param name="curFxUserId"></param>
        /// <param name="baseProductUid"></param>
        /// <param name="baseProductSkuUid"></param>
        public void PushBaseProductRelation(string ownerCode, string wareHouseSkuCode, string skuCargoNumber, int curFxUserId, long baseProductUid, long baseProductSkuUid)
        {
            return; // 2024-10-15，已不需要此方法
            var req = new WareHouseSkuBindRelationListRequest
            {
                PageIndex = 1,
                PageSize = 100000,
                WareHouseSkuCode = wareHouseSkuCode,
                SkuCargoNumber = skuCargoNumber,
                OwnerCode = ownerCode,
                isAll = true
            };

            var cloudPlatform = CustomerConfig.CloudPlatformType;
            var result = _warehouseclient.Execute(req);
            if (!result.IsSucc) return;

            var basePtSkuRelationAddList = new List<BaseOfPtSkuRelation>();
            var basePtSkuRelationUpdateList = new List<BaseOfPtSkuRelation>();
            var dtNow = DateTime.Now;

            var rep = result.WareHouseSkuBindRelations;
            if (rep != null && rep.Count > 0)
            {
                var _baseOfPtSkuRelationRepository = new BaseOfPtSkuRelationRepository(curFxUserId);
                var _baseOfPtSkuRelationService = new BaseOfPtSkuRelationService(false, curFxUserId);
                var messageService = new MessageRecordService();

                // 根据baseProductSkuUid查询关联信息
                var entBaseOfPtRelList = _baseOfPtSkuRelationRepository.GetListBySkuUid(baseProductSkuUid);

                entBaseOfPtRelList = entBaseOfPtRelList?.Where(x => x.BaseProductSkuUid == baseProductSkuUid).ToList();

                var shopIds = rep.Select(x => x.ShopId).Distinct().ToList();
                var idByShopId = _fxUserShopService.GetUserIdByShopId(shopIds);
                var fxUserIds = idByShopId.Select(x => x.FxUserId).Distinct().ToList();
                fxUserIds.Add(curFxUserId);
                var cloudPlatformTypeList = new List<string> { "TouTiao", "Pinduoduo", "Jingdong", "Alibaba" };
                var dbConfigList = _dbConfigRepository.GetListByFxUserIds(fxUserIds, cloudPlatformTypeList);

                if (dbConfigList == null || dbConfigList.Count == 0) return;
                // 按照数据库配置新库的优先级排序
                dbConfigList = dbConfigList.OrderByDescending(x => x.FromFxDbConfig).ToList();
                // Log.WriteLine($"PushBaseProductRelation dbConfigList:{dbConfigList.ToJson()}", "BaseProductRelation.txt");

                rep.ForEach(item =>
                {
                    // 获取当前店铺的用户
                    var userId = idByShopId.FirstOrDefault(x => x.ShopId == item.ShopId)?.FxUserId ?? 0;
                    if (userId == 0) return;
                    // 获取当前商品的云平台类型
                    var cloudPlatformType = GetPlatformType(item.PlatformType);
                    // 获取当前商品所在店铺的用户数据库配置
                    var dbConfig = dbConfigList.FirstOrDefault(x => x.DbConfig.UserId == userId && x.DbConfig.DbCloudPlatform == cloudPlatformType);
                    if (dbConfig == null) return;

                    var productSku = new ProductSkuFxRepository(dbConfig.ConnectionString)
                            .GetBySkuCodes(new List<string> { item.PlatformSkuCode }, dbConfig).FirstOrDefault();

                    // Log.WriteLine($"当前商品：{productSku.ToJson()}，db配置:{dbConfig.ToJson()}", "BaseProductRelation.txt");

                    if (productSku == null) return;

                    var baseOfPtSkuRelation = entBaseOfPtRelList?.FirstOrDefault(x => x.ProductSkuCode == item.PlatformSkuCode);
                    if (baseOfPtSkuRelation != null)
                    {
                        // 更新
                        baseOfPtSkuRelation.ProductCode = productSku.ProductCode;
                        baseOfPtSkuRelation.ProductFxUserId = userId;
                        baseOfPtSkuRelation.ProductPlatformType = productSku.PlatformType;
                        baseOfPtSkuRelation.ProductPtId = productSku.PlatformId;
                        baseOfPtSkuRelation.ProductShopId = item.ShopId;
                        baseOfPtSkuRelation.ProductSkuPtId = productSku.SkuId;
                        baseOfPtSkuRelation.IsUseWarehouse = false;
                        baseOfPtSkuRelation.UpdateTime = dtNow;
                        baseOfPtSkuRelation.DbConn = GetDbConnection(cloudPlatformType, curFxUserId, dbConfigList,
                            dbConfig.DbNameConfig.ApplicationName, dbConfig.ConnectionString);
                        baseOfPtSkuRelation.DbConfig = GetDbConfigModel(cloudPlatformType, curFxUserId, dbConfigList, dbConfig);
                        baseOfPtSkuRelation.Status = item.Status.ToInt() + 1;
                        baseOfPtSkuRelation.Type = 2;
                        baseOfPtSkuRelation.CloudPlatform = cloudPlatform;

                        // 不删除仅更新字段
                        // 2024-10-15，同步删除状态
                        if (item.Status.ToInt() == -1)
                        {
                            baseOfPtSkuRelation.Status = 0;
                            baseOfPtSkuRelation.IsUseWarehouse = false;
                            baseOfPtSkuRelation.Type = 0;
                        }
                        basePtSkuRelationUpdateList.Add(baseOfPtSkuRelation);
                    }
                    else // 如果没有，则新增
                    {
                        var ent = new BaseOfPtSkuRelation()
                        {
                            BaseProductUid = baseProductUid,
                            BaseProductSkuUid = baseProductSkuUid,
                            FxUserId = curFxUserId,
                            ProductCode = productSku.ProductCode,
                            ProductFxUserId = userId,
                            ProductPlatformType = item.PlatformType,
                            ProductSkuCode = productSku.SkuCode,
                            ProductPtId = productSku.PlatformId,
                            ProductShopId = item.ShopId,
                            ProductSkuPtId = productSku.SkuId,
                            IsUseWarehouse = false,
                            CreateTime = dtNow,
                            UpdateTime = dtNow,
                            Status = item.Status.ToInt() + 1,
                            DbConn = GetDbConnection(cloudPlatformType, curFxUserId, dbConfigList, dbConfig.DbNameConfig.ApplicationName, dbConfig.ConnectionString),
                            DbConfig = GetDbConfigModel(cloudPlatformType, curFxUserId, dbConfigList, dbConfig),
                            Type = 1,
                            CloudPlatform = cloudPlatform
                        };
                        basePtSkuRelationAddList.Add(ent);
                    }
                });

                try
                {
                    var allRelationList = new List<BaseOfPtSkuRelation>();
                    // 批量添加各实体列表
                    new BaseOfPtSkuRelationService().BatchAdd(basePtSkuRelationAddList);
                    allRelationList.AddRange(basePtSkuRelationAddList);

                    // 批量更新各实体列表
                    _baseOfPtSkuRelationRepository.BulkUpdate(basePtSkuRelationUpdateList);
                    allRelationList.AddRange(basePtSkuRelationUpdateList);

                    // 批量删除相关信息
                    if (entBaseOfPtRelList != null)
                    {
                        var deleteSkuList = entBaseOfPtRelList.Except(basePtSkuRelationUpdateList).ToList();
                        deleteSkuList = deleteSkuList.Where(x => x.Status != 0).ToList();

                        // 给删除的数据添加业务库数据
                        deleteSkuList.ForEach(sku =>
                        {
                            sku.IsUseWarehouse = false;
                            // 获取当前店铺的用户
                            var userId = idByShopId.FirstOrDefault(x => x.ShopId == sku.ProductShopId)?.FxUserId ?? 0;
                            if (userId == 0) return;
                            // 获取当前商品的云平台类型
                            var cloudPlatformType = sku.CloudPlatform ?? GetPlatformType(sku.ProductPlatformType);
                            // 获取当前商品所在店铺的用户数据库配置
                            var dbConfig = dbConfigList.FirstOrDefault(x => x.DbConfig.UserId == userId && x.DbConfig.DbCloudPlatform == cloudPlatformType);
                            if (dbConfig == null) return;

                            sku.DbConn = GetDbConnection(cloudPlatformType, curFxUserId, dbConfigList,
                                dbConfig.DbNameConfig.ApplicationName, dbConfig.ConnectionString);
                            sku.DbConfig = GetDbConfigModel(cloudPlatformType, curFxUserId, dbConfigList, dbConfig);
                        });

                        _baseOfPtSkuRelationRepository.BulkDelete(deleteSkuList);
                        allRelationList.AddRange(deleteSkuList);
                    }
                    _baseOfPtSkuRelationService.ChoseMergeToBusinessDb(allRelationList);
                }
                catch (Exception e)
                {
                    Log.WriteError($"基础商品关联信息推送时出错: FxUserId[{curFxUserId}]  OwnerCode[{ownerCode}], 错误信息：{e.ToJson()}", "BaseProductRelation.txt");
                }

                string GetPlatformType(string platform)
                {
                    switch (platform.ToLower())
                    {
                        case "toutiao": return "TouTiao";
                        case "pinduoduo": return "Pinduoduo";
                        case "kuaituantuan": return "Pinduoduo";
                        case "jingdong": return "Jingdong";
                        default: return "Alibaba";
                    }
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ownerCode"></param>
        /// <param name="wareHouseSkuCode"></param>
        /// <param name="skuCargoNumber"></param>
        /// <param name="curFxUserId"></param>
        /// <param name="baseProductUid"></param>
        /// <param name="baseProductSkuUid"></param>
        public void MergeRelationToBaseProduct(string ownerCode, string wareHouseSkuCode, string skuCargoNumber, int curFxUserId, long baseProductUid, long baseProductSkuUid)
        {
            var req = new WareHouseSkuBindRelationListRequest
            {
                PageIndex = 1,
                PageSize = 100000,
                WareHouseSkuCode = wareHouseSkuCode,
                SkuCargoNumber = skuCargoNumber,
                OwnerCode = ownerCode,
                isAll = true
            };

            // 获取库存系统的商品关联信息
            var result = _warehouseclient.Execute(req);
            if (!result.IsSucc) return;
            var rep = result.WareHouseSkuBindRelations;
            if (rep == null || rep.Count <= 0) return;

            Log.Debug($"库存系统关联关系去重前：{rep.ToJson()}");

            // 根据更新时间最后的时间进行排序然后根据PlatformSkuCode去重
            rep = rep.OrderByDescending(x => x.UpdateTime)
                .GroupBy(x => x.PlatformSkuCode)
                .Select(x => x.First())
                .ToList();

            // 定义变量
            var basePtSkuRelationAddList = new List<BaseOfPtSkuRelation>();
            var basePtSkuRelationUpdateList = new List<BaseOfPtSkuRelation>();
            var dtNow = DateTime.Now;
            var baseOfPtSkuRelationRepository = new BaseOfPtSkuRelationRepository(false, curFxUserId);
            var messageService = new MessageRecordService();
            var messageList = new List<MessageRecord>();

            // 根据baseProductSkuUid查询当前平台业务库关联信息
            var entBaseOfPtRelList = baseOfPtSkuRelationRepository.GetListBySkuUid(baseProductSkuUid);
            entBaseOfPtRelList = entBaseOfPtRelList?.Where(x => x.BaseProductSkuUid == baseProductSkuUid).ToList();

            // 获取所有的商品信息
            var proCodes = rep.Select(x => x.PlatformSkuCode).Distinct().ToList();
            var productSku = new ProductSkuFxRepository().GetBySkuCodes(proCodes);
            Log.Debug($"库存系统关联关系去重后：{rep.ToJson()}");

            // 循环处理
            rep.ForEach(relation =>
            {
                // 查找对应的商品信息
                var productSkuInfo = productSku.FirstOrDefault(x => x.SkuCode == relation.PlatformSkuCode);
                if (productSkuInfo == null) return;
                // 查找对应基础商品的关联关系
                var baseOfPtSkuRelation = entBaseOfPtRelList?.FirstOrDefault(x => x.ProductSkuCode.Trim() == relation.PlatformSkuCode);

                if (baseOfPtSkuRelation == null)
                {
                    // 如果不存在，则新增
                    var ent = new BaseOfPtSkuRelation
                    {
                        BaseProductUid = baseProductUid,
                        BaseProductSkuUid = baseProductSkuUid,
                        FxUserId = curFxUserId,
                        ProductCode = productSkuInfo.ProductCode,
                        ProductFxUserId = productSkuInfo.FxUserId,
                        ProductPlatformType = relation.PlatformType,
                        ProductSkuCode = relation.PlatformSkuCode,
                        ProductPtId = productSkuInfo.PlatformId,
                        ProductShopId = relation.ShopId,
                        ProductSkuPtId = productSkuInfo.SkuId,
                        IsUseWarehouse = true,
                        CreateTime = dtNow,
                        UpdateTime = dtNow,
                        Status = relation.Status.ToInt() + 1,
                        Type = 1
                    };
                    basePtSkuRelationAddList.Add(ent);
                    messageList.Add(new MessageRecord
                    {
                        BusinessId = ent.BaseProductSkuUid.ToString(),
                        CreateFxUserId = curFxUserId,
                        FxUserId = curFxUserId,
                        DataJson = ent.ToJsonExt(),
                        TargetCloud = "Alibaba",
                        MsgType = BaseProductMsgType.WareHouseRelationToBaseProductNew
                    });
                }
                else
                {
                    // 如果存在，则更新
                    baseOfPtSkuRelation.ProductCode = productSkuInfo.ProductCode;
                    baseOfPtSkuRelation.ProductFxUserId = productSkuInfo.FxUserId;
                    baseOfPtSkuRelation.ProductPlatformType = relation.PlatformType;
                    baseOfPtSkuRelation.ProductPtId = productSkuInfo.PlatformId;
                    baseOfPtSkuRelation.ProductShopId = relation.ShopId;
                    baseOfPtSkuRelation.ProductSkuPtId = productSkuInfo.SkuId;
                    baseOfPtSkuRelation.IsUseWarehouse = relation.Status != "-1";
                    baseOfPtSkuRelation.UpdateTime = dtNow;
                    baseOfPtSkuRelation.Status = relation.Status.ToInt() == -1 ? baseOfPtSkuRelation.Status : 1;
                    baseOfPtSkuRelation.Type = 2;

                    messageList.Add(new MessageRecord
                    {
                        BusinessId = baseOfPtSkuRelation.BaseProductSkuUid.ToString(),
                        CreateFxUserId = curFxUserId,
                        FxUserId = curFxUserId,
                        DataJson = baseOfPtSkuRelation.ToJsonExt(),
                        TargetCloud = "Alibaba",
                        MsgType = BaseProductMsgType.WareHouseRelationToBaseProductNew
                    });

                    basePtSkuRelationUpdateList.Add(baseOfPtSkuRelation);
                }
            });

            // 剩下的删除
            if (entBaseOfPtRelList != null && entBaseOfPtRelList.Any())
            {
                var deleteSkuList = entBaseOfPtRelList.Except(basePtSkuRelationUpdateList).ToList();
                deleteSkuList.ForEach(x =>
                {
                    x.Type = 0;
                    messageList.Add(new MessageRecord
                    {
                        BusinessId = x.BaseProductSkuUid.ToString(),
                        CreateFxUserId = curFxUserId,
                        FxUserId = curFxUserId,
                        DataJson = x.ToJsonExt(),
                        TargetCloud = "Alibaba",
                        MsgType = BaseProductMsgType.WareHouseRelationToBaseProductNew
                    });
                });
                baseOfPtSkuRelationRepository.BulkDelete(deleteSkuList);
            }
            if (basePtSkuRelationAddList.Any()) new BaseOfPtSkuRelationService().BatchAdd(basePtSkuRelationAddList);
            if (basePtSkuRelationUpdateList.Any()) baseOfPtSkuRelationRepository.BulkUpdate(basePtSkuRelationUpdateList);
            Log.Debug($"消息记录：{messageList.ToJson()}");
            if (messageList.Any())
            {
                var mesResult = messageService.SendBusinessMessage(messageList);
                Log.Debug($"消息发送结果：{mesResult.ToJson()}");
            }
        }

        /// <summary>
        /// 合并单个关联关系到基础商品
        /// </summary>
        /// <param name="curFxUserId"></param>
        /// <param name="baseProductUid"></param>
        /// <param name="baseProductSkuUid"></param>
        /// <param name="relation"></param>
        private void MergeRelationToBaseProductSingle(int curFxUserId, long baseProductUid, long baseProductSkuUid, WareHouseSkuBindRelation relation)
        {
            // 定义变量
            var basePtSkuRelationAddList = new List<BaseOfPtSkuRelation>();
            var basePtSkuRelationUpdateList = new List<BaseOfPtSkuRelation>();
            var dtNow = DateTime.Now;
            var baseOfPtSkuRelationRepository = new BaseOfPtSkuRelationRepository(false, curFxUserId);
            var messageService = new MessageRecordService();
            var messageList = new List<MessageRecord>();

            // 根据baseProductSkuUid查询当前平台业务库关联信息
            var entBaseOfPtRelList = baseOfPtSkuRelationRepository.GetListBySkuUid(baseProductSkuUid);
            entBaseOfPtRelList = entBaseOfPtRelList?.Where(x => x.BaseProductSkuUid == baseProductSkuUid).ToList();

            // 获取商品信息
            var productSkuInfo = new ProductSkuFxRepository().GetBySkuCodes(new List<string> { relation.PlatformSkuCode }).FirstOrDefault();

            if (productSkuInfo == null) return;

            // 查找对应基础商品的关联关系
            var baseOfPtSkuRelation =
                entBaseOfPtRelList?.FirstOrDefault(x => x.ProductSkuCode.Trim() == relation.PlatformSkuCode);

            if (baseOfPtSkuRelation == null)
            {
                // 如果不存在，则新增
                var ent = new BaseOfPtSkuRelation
                {
                    BaseProductUid = baseProductUid,
                    BaseProductSkuUid = baseProductSkuUid,
                    FxUserId = curFxUserId,
                    ProductCode = productSkuInfo.ProductCode,
                    ProductFxUserId = productSkuInfo.FxUserId,
                    ProductPlatformType = relation.PlatformType,
                    ProductSkuCode = relation.PlatformSkuCode,
                    ProductPtId = productSkuInfo.PlatformId,
                    ProductShopId = relation.ShopId,
                    ProductSkuPtId = productSkuInfo.SkuId,
                    IsUseWarehouse = true,
                    CreateTime = dtNow,
                    UpdateTime = dtNow,
                    Status = relation.Status.ToInt() + 1,
                    Type = 1
                };
                basePtSkuRelationAddList.Add(ent);
                messageList.Add(new MessageRecord
                {
                    BusinessId = ent.BaseProductSkuUid.ToString(),
                    CreateFxUserId = curFxUserId,
                    FxUserId = curFxUserId,
                    DataJson = ent.ToJsonExt(),
                    TargetCloud = "Alibaba",
                    MsgType = BaseProductMsgType.WareHouseRelationToBaseProductNew
                });
            }
            else
            {
                // 如果存在，则更新
                baseOfPtSkuRelation.ProductCode = productSkuInfo.ProductCode;
                baseOfPtSkuRelation.ProductFxUserId = productSkuInfo.FxUserId;
                baseOfPtSkuRelation.ProductPlatformType = relation.PlatformType;
                baseOfPtSkuRelation.ProductPtId = productSkuInfo.PlatformId;
                baseOfPtSkuRelation.ProductShopId = relation.ShopId;
                baseOfPtSkuRelation.ProductSkuPtId = productSkuInfo.SkuId;
                baseOfPtSkuRelation.IsUseWarehouse = relation.Status != "-1";
                baseOfPtSkuRelation.UpdateTime = dtNow;
                baseOfPtSkuRelation.Status = relation.Status.ToInt() + 1;
                baseOfPtSkuRelation.Type = 0;

                messageList.Add(new MessageRecord
                {
                    BusinessId = baseOfPtSkuRelation.BaseProductSkuUid.ToString(),
                    CreateFxUserId = curFxUserId,
                    FxUserId = curFxUserId,
                    DataJson = baseOfPtSkuRelation.ToJsonExt(),
                    TargetCloud = "Alibaba",
                    MsgType = BaseProductMsgType.WareHouseRelationToBaseProductNew
                });

                basePtSkuRelationUpdateList.Add(baseOfPtSkuRelation);
            }

            if (basePtSkuRelationAddList.Any()) baseOfPtSkuRelationRepository.BatchAdd(basePtSkuRelationAddList);
            if (basePtSkuRelationUpdateList.Any()) baseOfPtSkuRelationRepository.BulkUpdate(basePtSkuRelationUpdateList);
            Log.Debug($"消息记录：{messageList.ToJson()}");
            if (!messageList.Any()) return;
            var mesResult = messageService.SendBusinessMessage(messageList);
            Log.Debug($"消息发送结果：{mesResult.ToJson()}");
        }

        /// <summary>
        /// 获取业务库连接串
        /// </summary>
        /// <param name="cloudPlatformType"></param>
        /// <param name="fxUserId"></param>
        /// <param name="dbConfigList"></param>
        /// <param name="applicationName"></param>
        /// <param name="conn"></param>
        /// <returns></returns>
        private string GetDbConnection(string cloudPlatformType, int fxUserId, List<DbConfigModel> dbConfigList, string applicationName, string conn)
        {
            switch (cloudPlatformType)
            {
                // 头条-查询当前用户头条的连接串
                case "TouTiao":
                    return dbConfigList.FirstOrDefault(x =>
                            x.DbConfig.UserId == fxUserId && x.DbConfig.DbCloudPlatform == cloudPlatformType)?.ConnectionString;
                // 拼多多-判断原商品所在库是否旧库
                // 是-旧库  P_DbNameConfig -> ApplicationName !=  fx_new
                // 否-查询当前用户的拼多多连接串
                case "Pinduoduo":
                    return applicationName != "fx_new" ? conn : dbConfigList.FirstOrDefault(x =>
                                               x.DbConfig.UserId == fxUserId && x.DbConfig.DbCloudPlatform == cloudPlatformType && x.FromFxDbConfig != 0)?.ConnectionString;
                case "Jingdong":
                    return dbConfigList.FirstOrDefault(x =>
                        x.DbConfig.UserId == fxUserId && x.DbConfig.DbCloudPlatform == cloudPlatformType)?.ConnectionString;
                default:
                    return applicationName != "fx_new" ? conn : dbConfigList.FirstOrDefault(x =>
                        x.DbConfig.UserId == fxUserId && x.DbConfig.DbCloudPlatform == cloudPlatformType && x.FromFxDbConfig != 0)?.ConnectionString;
            }
        }

        /// <summary>
        /// 获取数据库配置信息
        /// </summary>
        /// <param name="cloudPlatformType"></param>
        /// <param name="fxUserId"></param>
        /// <param name="dbConfigList"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        private DbConfigModel GetDbConfigModel(string cloudPlatformType, int fxUserId, List<DbConfigModel> dbConfigList, DbConfigModel model)
        {
            switch (cloudPlatformType)
            {
                // 头条-查询当前用户头条的连接串
                case "TouTiao":
                    return dbConfigList.FirstOrDefault(x =>
                        x.DbConfig.UserId == fxUserId && x.DbConfig.DbCloudPlatform == cloudPlatformType);
                // 拼多多-判断原商品所在库是否旧库
                // 是-旧库  P_DbNameConfig -> ApplicationName !=  fx_new
                // 否-查询当前用户的拼多多连接串
                case "Pinduoduo":
                    if (model.DbNameConfig.ApplicationName != "fx_new")
                        return model;
                    return dbConfigList.FirstOrDefault(x =>
                        x.DbConfig.UserId == fxUserId && x.DbConfig.DbCloudPlatform == cloudPlatformType && x.FromFxDbConfig != 0);
                case "Jingdong":
                    return dbConfigList.FirstOrDefault(x =>
                        x.DbConfig.UserId == fxUserId && x.DbConfig.DbCloudPlatform == cloudPlatformType);
                default:
                    if (model.DbNameConfig.ApplicationName != "fx_new")
                        return model;
                    return dbConfigList.FirstOrDefault(x =>
                        x.DbConfig.UserId == fxUserId && x.DbConfig.DbCloudPlatform == cloudPlatformType && x.FromFxDbConfig != 0);
            }
        }

        /// <summary>
        /// 同步删除基础商品
        /// </summary>
        /// <param name="spu"></param>
        private void DeleteBaseProduct(string spu)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var _baseProductRepository = new BaseProductRepository();
            var _baseProductSkuRepository = new BaseProductSkuRepository();
            var _baseProductImageRepository = new BaseProductImageRepository();

            var baseProductEntities = _baseProductRepository.GetList(new List<string> { spu });
            var ent = baseProductEntities.FirstOrDefault(x => x.FxUserId == curFxUserId && x.Status == 1);

            if (ent != null)
            {
                ent.Status = 0;
                ent.UpdateTime = DateTime.Now;
                _baseProductRepository.Update(ent);

                var baseProductSkus = _baseProductSkuRepository.GetList(new List<string> { ent.Uid.ToString() }, whereFieldName: "BaseProductUid");
                baseProductSkus = baseProductSkus.Where(x => x.FxUserId == curFxUserId && x.Status == 1).ToList();
                baseProductSkus.ForEach(x => x.Status = 0);
                _baseProductSkuRepository.BulkUpdate(baseProductSkus);

                var baseProductImages = _baseProductImageRepository.GetListByIds(new List<long> { ent.MainImageObjectId });
                baseProductImages.ForEach(x => x.Status = 0);
                _baseProductImageRepository.BulkUpdate(baseProductImages);
            }
        }

        /// <summary>
        /// 同步修改基础商品关联关系
        /// </summary>
        /// <param name="skuCode"></param>
        /// <param name="productSkuCode"></param>
        /// <param name="curFxUserId"></param>
        /// <param name="isDelete"></param>
        private void ModifyBasePtRelation(string skuCode, string productSkuCode, int curFxUserId, bool isDelete = false)
        {
            var cloudPlatform = CustomerConfig.CloudPlatformType;
            var productSku = new ProductSkuFxRepository().GetBySkuCodes(new List<string> { productSkuCode }).FirstOrDefault();
            var productFxRepository = new ProductFxRepository();
            var dbName = productFxRepository.DbConnection.Database;

            if (productSku == null) return;
            var productFx = productFxRepository.GetBySkuCodes(new List<string> { productSku.SkuCode })
                .FirstOrDefault();
            if (productFx == null) return;

            var model = new BaseProductRelationModifyModel
            {
                SkuCode = skuCode,
                ProductSkuCode = productSkuCode,
                FxUserId = curFxUserId,
                IsDelete = isDelete,
                CloudPlatform = cloudPlatform,
                ProductSkuPtId = productSku.SkuId,
                ProductPtId = productFx.PlatformId,
                ProductPlatformType = productFx.PlatformType,
                ProductCode = productFx.ProductCode,
                ProductShopId = productFx.ShopId,
                ProductFxUserId = productFx.CreateBy
            };
            var messageList = new List<MessageRecord>
            {
                new MessageRecord
                {
                    BusinessId = skuCode,
                    CreateFxUserId = curFxUserId,
                    FxUserId = curFxUserId,
                    DataJson = model.ToJsonExt(),
                    TargetCloud = "Alibaba",
                    DbName = dbName,
                    MsgType = BaseProductMsgType.WareHouseRelationToBaseProduct
                }
            };

            Log.Debug($"消息发出前：{messageList.ToJson()}");
            var result = new MessageRecordService().SendBusinessMessage(messageList);
            Log.Debug($"消息发出结果：{result}");
        }

        private void PushBasePtRelation(string ownerCode, string wareHouseSkuCode, string skuCargoNumber,
            int curFxUserId, long baseProductUid, long baseProductSkuUid, WareHouseSkuBindRelation ent)
        {
            if (baseProductUid == 0 || baseProductSkuUid == 0) return;
            try
            {
                // MergeRelationToBaseProduct(ownerCode, wareHouseSkuCode, skuCargoNumber, curFxUserId, baseProductUid, baseProductSkuUid);
                MergeRelationToBaseProductSingle(curFxUserId, baseProductUid, baseProductSkuUid, ent);
            }
            catch (Exception e)
            {
                Log.WriteError($"基础商品关联信息推送出错: FxUserId[{curFxUserId}]  OwnerCode[{ownerCode}]  WareHouseSkuCode[{wareHouseSkuCode}]  SkuCargoNumber[{skuCargoNumber}], 错误信息：{e.Message}", "BaseProductRelation.txt");
            }
        }

        #endregion

        public WarehouseProductUpdateResponse WarehouseProductEdit(WarehouseProductUpdateRequest req)
        {
            req.OwnerCode = GetOwnerCode();
            req.ShortName = "";
            req.ImageUrl = req.GetDecryptUrl() ?? "";
            req.CargoNumber = req.CargoNumber ?? "";

            if (req.Skus != null)
            {
                foreach (var item in req.Skus)
                {
                    item.SkuProperty = item.SkuName ?? "";
                    item.ItemType = "";
                    item.ShortName = "";
                    item.SkuBarCode = "";
                    item.ImageUrl = item.GetDecryptUrl() ?? "";
                    item.SkuName = "";
                }
            }


            var result = _warehouseclient.Execute(req);

            #region 修改基础商品数据

            if (result.IsSucc && req.IsPushToBaseProduct)
            {
                Task.Run(() =>
                {
                    try
                    {
                        // 根据Code获取商品信息，并更新到商品表
                        var curFxUserId = SiteContext.Current.CurrentFxUserId;
                        EditBaseProduct(req, curFxUserId);
                    }
                    catch (Exception e)
                    {
                        Log.WriteError($"修改基础商品信息出错：{e.Message}");
                    }
                });
            }

            #endregion

            return result;
        }

        /// <summary>
        /// 编辑更新基础商品
        /// </summary>
        /// <param name="req"></param>
        /// <param name="curFxUserId"></param>
        private void EditBaseProduct(WarehouseProductUpdateRequest req, int curFxUserId)
        {
            // 根据旧的获取商品数据
            var spuCode = req.OldCargoNumber;

            var baseProductService = new BaseProductEntityService();
            var baseSkuService = new BaseProductSkuService();
            var baseProImgRepository = new BaseProductImageRepository();
            var baseSkuAttributeService = new BaseProductSkuAttributeService();

            // 获取基础商品数据
            var baseProduct = baseProductService.GetList(new List<string> { spuCode })
                .FirstOrDefault(x => x.FxUserId == curFxUserId && x.Status == 1);
            if (baseProduct == null) return;
            var skuList = baseSkuService.GetListByProductUid(baseProduct.Uid, curFxUserId);
            var existAttributeList = baseSkuAttributeService.GetList(skuList.Select(x => x.Uid).ToList(), whereFieldName: "SkuUid");
            var dtNow = DateTime.Now;
            var baseProductMainImg = baseProImgRepository.GetListByProductUid(new List<long> { baseProduct.Uid })
                .FirstOrDefault(x => x.ImageObjectId == baseProduct.MainImageObjectId);

            // 局部变量
            var insertAttributeList = new List<BaseProductSkuAttribute>();
            var updateAttributeList = new List<BaseProductSkuAttribute>();
            var updateSkuList = new List<BaseProductSku>();
            var insertSkuList = new List<BaseProductSku>();
            var baseProductImageUpdateList = new List<BaseProductImage>();

            baseProduct.SkuModeType = 1;
            baseProduct.UpdateTime = dtNow;
            baseProduct.Subject = req.Name;
            baseProduct.SpuCode = req.CargoNumber;
            baseProduct.WareHouseProductCode = req.WareHouseProductCode;
            // 找到对应的主图信息，并修改
            if (req.ImageUrl != null)
            {
                if (baseProductMainImg != null && req.ImageUrl != baseProductMainImg.FullUrl)
                {
                    // 更新主图
                    var pathStr = ImgHelper.SplitImageUrl(req.ImageUrl);
                    var pathModel = pathStr.ToObject<BaseProductImageModel>();
                    baseProductMainImg.Url = pathModel.Url;
                    baseProductMainImg.Domain = pathModel.Domain;
                    baseProductMainImg.Name = pathModel.Name;
                    baseProductMainImg.Suffix = pathModel.Suffix;
                    baseProductImageUpdateList.Add(baseProductMainImg);
                }
            }
            else // 不存在图片信息，需要删除
            {
                if (baseProduct.MainImageObjectId != 0)
                {
                    if (baseProductMainImg != null)
                    {
                        baseProductMainImg.Status = 0;
                        baseProductImageUpdateList.Add(baseProductMainImg);
                    }

                    baseProduct.MainImageObjectId = 0;
                }
            }

            // 判断不存在基础商品Sku的数量
            var noExistBaseSkuCount = req.Skus.Count(x => skuList.All(y => y.SkuCode != x.OldSkuCargoNumber));
            // 获取对应的Uid
            var uniqueIdList = new ProductDbConfigRepository().BaseProductSystemUniqueId("", curFxUserId, noExistBaseSkuCount);
            var index = 0;

            req.Skus.ForEach(sku =>
            {
                // 获取sku
                var baseSku = skuList.FirstOrDefault(x => x.SkuCode == sku.OldSkuCargoNumber);
                var baseSkuAttr = existAttributeList.FirstOrDefault(x => x.SkuUid == baseSku?.Uid);

                var attrJson = new List<Dictionary<string, string>>
                    { new Dictionary<string, string> { { "k", "无规格" }, { "v", sku.SkuProperty } } }.ToJsonExt();
                if (baseSku == null)
                {
                    // 新增
                    var skuEnt = new BaseProductSku
                    {
                        SkuCode = sku.SkuCargoNumber,
                        FxUserId = baseProduct.FxUserId,
                        BaseProductUid = baseProduct.Uid,
                        Subject = sku.SkuName,
                        CostPrice = sku.CostPrice,
                        ShortTitle = sku.ShortName,
                        Uid = (2 + uniqueIdList[index++]).ToLong(),
                        IsCombineSku = sku.ItemType == "ZH",
                        CreateTime = dtNow,
                        UpdateTime = dtNow,
                        Attributes = attrJson,
                        WareHouseProductCode = req.WareHouseProductCode,
                        WareHouseSkuCode = sku.WareHouseSkuCode,
                        AttributeValue = sku.SkuProperty,
                        ImageUrl = ImgHelper.GetRealPath(sku.ImageUrl)
                    };

                    var skuAttr = new BaseProductSkuAttribute
                    {
                        ProductUid = baseProduct.Uid,
                        SkuUid = skuEnt.Uid,
                        AttributeName1 = "无规格",
                        AttributeValue1 = sku.SkuProperty,
                        CreateTime = dtNow
                    };
                    insertAttributeList.Add(skuAttr);
                    insertSkuList.Add(skuEnt);
                }
                else
                {
                    // 更新
                    baseSku.SkuCode = sku.SkuCargoNumber;
                    baseSku.Subject = sku.SkuName;
                    baseSku.ImageUrl = ImgHelper.GetRealPath(sku.ImageUrl);
                    baseSku.CostPrice = sku.CostPrice;
                    baseSku.UpdateTime = dtNow;
                    baseSku.Attributes = attrJson;
                    baseSku.WareHouseProductCode = req.WareHouseProductCode;
                    baseSku.WareHouseSkuCode = sku.WareHouseSkuCode;
                    baseSku.AttributeValue = sku.SkuProperty;
                    updateSkuList.Add(baseSku);

                    if (baseSkuAttr != null && baseSkuAttr.AttributeValue1 != sku.SkuProperty)
                    {
                        baseSkuAttr.AttributeValue1 = sku.SkuProperty;
                        updateAttributeList.Add(baseSkuAttr);
                    }
                    else if (baseSkuAttr == null)
                    {
                        var skuAttr = new BaseProductSkuAttribute
                        {
                            ProductUid = baseProduct.Uid,
                            SkuUid = baseSku.Uid,
                            AttributeName1 = "无规格",
                            AttributeValue1 = sku.SkuProperty,
                            CreateTime = dtNow
                        };
                        insertAttributeList.Add(skuAttr);
                    }
                }
            });

            // 更新数据
            try
            {
                if (baseProductImageUpdateList.Any()) baseProImgRepository.BulkUpdate(baseProductImageUpdateList);
                if (insertAttributeList.Any()) baseSkuAttributeService.BatchAdd(insertAttributeList);
                if (updateAttributeList.Any()) baseSkuAttributeService.BulkUpdate(updateAttributeList);
                if (updateSkuList.Any()) baseSkuService.BulkUpdate(updateSkuList);
                if (insertSkuList.Any()) baseSkuService.BulkInsert(insertSkuList);
                baseProductService.Update(baseProduct);
            }
            catch (Exception e)
            {
                Log.WriteError($"基础商品信息更新出错：{e.Message}");
            }
        }

        public WarehouseProductListResponse LoadProductSkuList(WarehouseProductListRequest req)
        {
            req.OwnerCode = GetOwnerCode();

            return _warehouseclient.Execute(req);
        }

        public WarehouseStockInResponse WarehouseStockIn(WarehouseStockInRequest req)
        {
            req.OwnerCode = GetOwnerCode();

            return _warehouseclient.Execute(req);
        }

        public WarehouseStockOutResponse WarehouseStockOut(WarehouseStockOutRequest req)
        {
            req.OwnerCode = GetOwnerCode();

            return _warehouseclient.Execute(req);
        }

        public WarehouseProductGetResponse WarehouseProductGet(WarehouseProductGetRequest req)
        {
            req.OwnerCode = GetOwnerCode();

            return _warehouseclient.Execute(req);
        }

        public WarehouseSkuGetOneResponse WarehouseSkuGetOne(WarehouseSkuGetOneRequest req)
        {
            req.OwnerCode = GetOwnerCode();

            return _warehouseclient.Execute(req);
        }

        public WareHouseSkuBindRelationListResponse WareHouseSkuBindRelationList(WareHouseSkuBindRelationListRequest req, string productName)
        {
            req.OwnerCode = GetOwnerCode();
            string SkuCargoNumber = req.SkuCargoNumber;
            req.SkuCargoNumber = "";
            var rsp = _warehouseclient.Execute(req);

            if (rsp != null && rsp.WareHouseSkuBindRelations != null && rsp.WareHouseSkuBindRelations.Count > 0)
            {
                //找平台商品信息
                List<ProductSkuFx> productSkuList = new List<ProductSkuFx>();
                var platformSkuCodes = rsp.WareHouseSkuBindRelations.Select(s => s.PlatformSkuCode).ToList();
                productSkuList = _productFxRepository.ProductSkuGetListByCodes(platformSkuCodes);
                //此处需要排除【已迁移至】抖店的商品数据，防止解绑异常
                if (CustomerConfig.CloudPlatformType == "Alibaba")
                {
                    var tempShopIds = productSkuList?.Select(x => x.ShopId)?.Distinct()?.ToList();
                    if (tempShopIds?.Any() == true)
                    {
                        var migratedToTouTiaoShopIds = new FxUserShopService().GetMigratedToTouTiaoCloudShopIds(tempShopIds);
                        productSkuList = productSkuList.Where(x => migratedToTouTiaoShopIds.Contains(x.ShopId) == false).ToList();
                    }
                }

                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var supplierUserRepository = new SupplierUserRepository();
                //var supplierUsers = supplierUserRepository.GetByFxUserId(fxUserId, true);
                var agentUsers = supplierUserRepository.GetByFxUserId(fxUserId, false);

                //找店铺信息
                var shopIds = rsp.WareHouseSkuBindRelations.Select(s => s.ShopId).ToList();
                FxUserShopService fxUserShopService = new FxUserShopService();
                var shops = fxUserShopService.GetUserIdByShopId(shopIds);

                #region 上下游商品信息展示
                var pathflowService = new PathFlowService(_connectionString);
                var pCodes = productSkuList.Select(x => x.ProductCode).Distinct().ToList();
                var fields = "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
                var pathFlows = pathflowService.GetPathFlows(pCodes, 0, fields);

                var logicOrderRepository = new LogicOrderRepository(_connectionString);
                var nodes = pathFlows.SelectMany(x => x.PathFlowNodes).ToList();
                var pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(nodes);
                var commonSettingRepository = new CommonSettingRepository();
                #endregion


                foreach (var item in rsp.WareHouseSkuBindRelations)
                {
                    var pro = productSkuList.FirstOrDefault(w => w.SkuCode == item.PlatformSkuCode);//?? new ProductSkuFx();
                    if (pro == null)
                    {
                        item.ShopId = -item.ShopId;
                        continue;
                    }
                    item.ImgUrl = pro.ImgUrl;
                    item.ProductName = pro.Subject;
                    item.SkuName = pro.Name;

                    var shopUser = shops.FirstOrDefault(w => w.ShopId == item.ShopId);
                    if (shopUser != null)
                    {
                        // 取商家电话备注或者店铺名称
                        var agentUser = agentUsers.FirstOrDefault(x => x.FxUserId == shopUser.FxUserId && x.SupplierFxUserId == fxUserId);
                        item.ShopName = agentUser == null ? shops.FirstOrDefault(x => x.ShopId == item.ShopId)?.NickName : agentUser.AgentMobileAndRemark;
                        //item.ShopName = shops.Where(w => w.ShopId == item.ShopId).Select(s => s.NickName).FirstOrDefault();
                    }

                    item.WareHouseSkuCode = pro.CargoNumber;

                    var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(pro.SkuCode)).ToList();
                    if (productPathFlows == null || productPathFlows.Count == 0)
                    {
                        productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(pro.ProductCode)).ToList();
                    }

                    // 商品标题是否可见
                    foreach (var flow in productPathFlows)
                    {
                        var isShowProductTitle = commonSettingRepository.SetIsShowProductTitle(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                        if (isShowProductTitle == false)
                        {
                            item.ProductName = item.ProductName.GetShowProductStr(false);
                            break;
                        }
                    }


                    // 商品图片是否可见
                    foreach (var flow in productPathFlows)
                    {
                        var isShowProductImg = commonSettingRepository.SetIsShowProductImg(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                        if (isShowProductImg == false)
                        {
                            item.ImgUrl = item.ImgUrl.GetShowProductStr(false);
                            break;
                        }
                    }

                }
                if (!IsNullOrEmpty(productName) && !IsNullOrEmpty(SkuCargoNumber))
                    rsp.WareHouseSkuBindRelations = rsp.WareHouseSkuBindRelations.Where(x => x.ProductName.ToString2().Contains(productName) && x.WareHouseSkuCode == SkuCargoNumber).ToList();
                else if (!IsNullOrEmpty(productName))
                    rsp.WareHouseSkuBindRelations = rsp.WareHouseSkuBindRelations.Where(x => x.ProductName.ToString2().Contains(productName)).ToList();
                else if (!IsNullOrEmpty(SkuCargoNumber))
                    rsp.WareHouseSkuBindRelations = rsp.WareHouseSkuBindRelations.Where(x => x.WareHouseSkuCode == SkuCargoNumber).ToList();
                rsp.WareHouseSkuBindRelations = rsp.WareHouseSkuBindRelations.Where(x => x.ShopId > 0).ToList();
            }

            return rsp;
        }

        public WarehouseSkuBindResponse BindProduct(WarehouseSkuBindRequest requestModel,
            bool isFormBaseProduct = false, bool isNotCheck = false)
        {
            requestModel.OwnerCode = GetOwnerCode();
            WarehouseSkuBindResponse rsp = new WarehouseSkuBindResponse();
            if (requestModel.PlatformSkuCode.IsNotNullOrEmpty())
            {
                var baseOfPtSkuRelations = new BaseOfPtSkuRelationService(false).GetAllListBySkuCode(new List<string> { requestModel.PlatformSkuCode },
                    SiteContext.GetCurrentFxUserId());
                baseOfPtSkuRelations = baseOfPtSkuRelations.Where(x => x.Status == 1).ToList();
                if (baseOfPtSkuRelations.Count > 0 && !isNotCheck)
                {
                    var platformSkuName = "";
                    var first = baseOfPtSkuRelations.First();
                    var platSkuInfo = _productSkuFxRepository.GetOneBySkuCode(first.ProductSkuCode);
                    if (platSkuInfo != null) platformSkuName = platSkuInfo.Subject + "-" + platSkuInfo.Name;

                    rsp.Code = "500";
                    rsp.Message = $"平台商品'{platformSkuName}'已经被基础商品绑定了";
                    rsp.OwnerCode = requestModel.OwnerCode;
                    return rsp;
                }
            }

            requestModel.OwnerCode = GetOwnerCode();

            //插入数据库之前判断平台商品是否已经绑定过库存SKU。一个平台商品只能绑定一个库存SKU。
            var dd = _wareHouseSkuBindRelationRepository.GetBindRelation(requestModel.OwnerCode, requestModel.PlatformSkuCode, requestModel.PlatformType);
            if (dd != null)
            {
                string platformSkuName = "";
                var platSkuInfo = _productSkuFxRepository.GetOneBySkuCode(dd.PlatformSkuCode);
                if (platSkuInfo != null)
                {
                    platformSkuName = platSkuInfo.Subject + "-" + platSkuInfo.Name;
                }

                string whSkuName = "";
                var whSkuInfo = _warehouseclient.Execute(new WarehouseSkuGetOneRequest { OwnerCode = requestModel.OwnerCode, WareHouseSkuCode = dd.WareHouseSkuCode });
                //检查whSkuInfo是否已被删除，若已被删除，可以将当前的【绑定关系】取消，已删除时，无法查询到whSkuInfo.ProductName、whSkuInfo.SkuProperty信息
                if (IsNullOrEmpty(whSkuInfo?.ProductName) && IsNullOrEmpty(whSkuInfo.SkuProperty))
                {
                    //库存货品被删除，可将当前绑定关系重置为失效，继续后续的绑定逻辑
                    _wareHouseSkuBindRelationRepository.UpdateStatusBySkuPlatformSkuCode("-1", requestModel.PlatformSkuCode);
                }
                else
                {
                    if (whSkuInfo != null)
                    {
                        whSkuName = whSkuInfo.ProductName + "-" + whSkuInfo.SkuProperty;
                    }

                    rsp.Code = "500";
                    rsp.Message = $"平台商品'{platformSkuName}'已经被库存货品'{whSkuName}'绑定了";
                    rsp.OwnerCode = requestModel.OwnerCode;
                    return rsp;
                }

            }

            Log.Debug($"货盘绑定商品请求参数：{requestModel.ToJson()}");
            rsp = _warehouseclient.Execute(requestModel);

            if (rsp.IsSucc)
            {
                var rsp2 = 0;

                try
                {
                    var now = DateTime.Now;

                    //查询是否存在
                    var data = _wareHouseSkuBindRelationRepository.GetOneBySkuBindRelationCode(rsp.SkuBindRelationCode);
                    if (data == null)
                    {
                        rsp2 = _wareHouseSkuBindRelationRepository.Insert(new DianGuanJiaApp.Data.Entity.WareHouseSkuBindRelation
                        {
                            CreateTime = now,
                            UpdateTime = now,
                            OwnerCode = requestModel.OwnerCode,
                            PlatformSkuCode = requestModel.PlatformSkuCode,
                            PlatformType = requestModel.PlatformType,
                            ShopId = requestModel.ShopId,
                            WareHouseSkuCode = rsp.WareHouseSkuCode ?? requestModel.WareHouseSkuCode,
                            SkuBindRelationCode = rsp.SkuBindRelationCode,
                            Status = "0",
                            FxUserId = SiteContext.Current.CurrentFxUserId
                        });
                    }
                    else
                    {
                        rsp2 = _wareHouseSkuBindRelationRepository.UpdateStatusBySkuBindRelationCode("0", rsp.SkuBindRelationCode);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteLine($"BindProduct关联商品请求参数：{requestModel.ToJson()}，异常信息：{ex}");
                }

                if (rsp2 <= 0)
                {
                    //回滚
                    requestModel.IsRollback = true;
                    rsp = _warehouseclient.Execute(requestModel);
                }

                if (rsp2 > 0 && !isFormBaseProduct)
                {
                    Task.Run(() =>
                    {
                        try
                        {
                            var ent = _wareHouseSkuBindRelationRepository.GetOneBySkuBindRelationCode(rsp.SkuBindRelationCode);
                            PushBasePtRelation(ent.OwnerCode, ent.WareHouseSkuCode, rsp.SkuCargoNumber,
                                SiteContext.Current.CurrentFxUserId, requestModel.BaseProductUid,
                                requestModel.BaseProductSkuUid, ent);
                        }
                        catch (Exception e)
                        {
                            Log.WriteError($"绑定库存货品关联关系到基础商品时发生错误，绑定数据：{requestModel?.ToJson()}\r\n错误信息：{e}");
                        }
                    });
                }
            }

            return rsp;
        }

        public WarehouseSkuUnbindResponse UnBindProduct(WarehouseSkuUnbindRequest requestModel, bool isFormBaseProduct = false)
        {
            //货品和商品绑定关系解绑，修改为先更新当前数据库的绑定关系，再调用接口，因为扣库存是以当前数据库的绑定关系为准，两边逻辑保持一致。2023-06-28 by 吴自飞
            requestModel.OwnerCode = GetOwnerCode();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            UnbindCurDbProduct(requestModel);
            try
            {
                var rsp = _warehouseclient.Execute(requestModel);
                if (rsp.IsSucc && !isFormBaseProduct && !requestModel.IsBatch)
                {
                    Task.Run(() =>
                    {
                        try
                        {
                            var ent = _wareHouseSkuBindRelationRepository.GetOneBySkuBindRelationCode(requestModel.SkuBindRelationCode);
                            PushBasePtRelation(ent.OwnerCode, ent.WareHouseSkuCode, rsp.SkuCargoNumber, fxUserId,
                                requestModel.BaseProductUid, requestModel.BaseProductSkuUid, ent);
                        }
                        catch (Exception e)
                        {
                            Log.WriteError($"解绑库存货品绑定关系到基础商品时发生错误，解绑数据：{requestModel?.ToJson()}\r\n错误信息：{e}");
                        }
                    });
                }
                return rsp;
            }
            catch (Exception ex)
            {
                Log.WriteError($"解绑库存货品绑定关系时发生错误，解绑数据：{requestModel?.ToJson()}\r\n错误信息：{ex}");
                throw;
            }
        }

        private void UnbindCurDbProduct(WarehouseSkuUnbindRequest requestModel)
        {
            try
            {
                if (requestModel.IsBatch)
                    _wareHouseSkuBindRelationRepository.BatchUpdateStatusByRelationCode("-1", requestModel.SkuBindRelationCodeList);
                else 
                    _wareHouseSkuBindRelationRepository.UpdateStatusBySkuBindRelationCode("-1", requestModel.SkuBindRelationCode);
            }
            catch (Exception ex)
            {
                Log.WriteError($"解绑库存货品绑定关系时发生错误，解绑数据：{requestModel?.ToJson()}\r\n错误信息：{ex}");
                //Console.WriteLine($"UnBindProduct==> 参数：{requestModel.ToJson()}，更新异常：{ex}");
                //回滚
                requestModel.IsRollback = true;
                //Console.WriteLine($"UnBindProduct==> 回滚更新配置库成功");
            }
        }

        public ProductInfoTempResponse ProductInfoTemp(ProductInfoTempRequest requestModel)
        {
            ProductInfoTempResponse rsp = new ProductInfoTempResponse();

            requestModel.OwnerCode = GetOwnerCode();
            rsp = _warehouseclient.Execute(requestModel);

            return rsp;
        }

        public ProductInfoTempGetOneResponse ProductInfoTempGetOne(ProductInfoTempGetOneRequest requestModel)
        {
            requestModel.OwnerCode = GetOwnerCode();
            return _warehouseclient.Execute(requestModel);
        }

        public WarehouseSkuUpdatePreAlertCountResponse WarehouseSkuUpdatePreAlertCount(WarehouseSkuUpdatePreAlertCountRequest requestModel)
        {
            requestModel.OwnerCode = GetOwnerCode();
            return _warehouseclient.Execute(requestModel);
        }

        public WarehouseSkuUpdatePreAlertCountByProductResponse WarehouseSkuUpdatePreAlertCountByProduct(WarehouseSkuUpdatePreAlertCountByProductRequest requestModel)
        {
            requestModel.OwnerCode = GetOwnerCode();
            return _warehouseclient.Execute(requestModel);
        }

        public ProductSkuCombinationAddResponse CombinationProductAdd(ProductSkuCombinationAddRequest req)
        {
            req.OwnerCode = GetOwnerCode();
            req.ConvertImgUrl();
            req.FxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _warehouseclient.Execute(req);


            #region 生成基础商品信息

            if (result.IsSucc)
            {
                try
                {
                    // 根据Code获取商品信息，并更新到商品表
                    var fxUserId = SiteContext.Current.CurrentFxUserId;
                    //生成对应的基础商品
                    PushToBaseProduct(result.WareHouseProductCodes, req.OwnerCode, fxUserId, true);
                }
                catch (Exception e)
                {
                    Log.WriteError($"生成基础商品信息失败：{e.Message}");
                }
            }

            #endregion

            return result;
        }

        /// <summary>
        /// 删除货品
        /// </summary>
        /// <param name="req"></param>
        /// <param name="syncBaseProduct">同步更新基础商品</param>
        /// <returns></returns>
        public WareHouseProductDeleteResponse ProductDelete(WareHouseProductDeleteRequest req, bool syncBaseProduct = false)
        {
            req.OwnerCode = GetOwnerCode();
            var result = _warehouseclient.Execute(req);

            #region 删除基础商品信息
            if (result.IsSucc && result.IsSuccess && syncBaseProduct)
            {
                // 根据Code获取商品信息，并更新到商品表
                Task.Run(() =>
                {
                    try
                    {
                        DeleteBaseProduct(req.CargoNumber);
                    }
                    catch (Exception e)
                    {
                        Log.WriteError($"删除基础商品信息失败：{e.Message}");
                    }
                });
            }
            #endregion
            return result;
        }

        /// <summary>
        /// 删除Sku
        /// </summary>
        /// <param name="req"></param>
        /// <param name="syncBaseProductSku"></param>
        /// <returns></returns>
        public WareHouseSkuDeleteResponse SkuDelete(WareHouseSkuDeleteRequest req, bool syncBaseProductSku = false)
        {
            req.OwnerCode = GetOwnerCode();
            var result = _warehouseclient.Execute(req);

            #region 删除基础商品信息
            if (result.IsSucc && result.IsSuccess && syncBaseProductSku)
            {
                // 根据Code获取商品信息，并更新到商品表
                Task.Run(() =>
                {
                    try
                    {
                        DeleteBaseProductSku(req.SkuCargoNumber);
                    }
                    catch (Exception e)
                    {
                        Log.WriteError($"删除基础商品信息失败：{e.Message}");
                    }
                });
            }
            #endregion
            return result;
        }

        /// <summary>
        /// 删除基础商品Sku
        /// </summary>
        /// <param name="skuCode"></param>
        private void DeleteBaseProductSku(string skuCode)
        {
            // 删除基础商品
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var baseSkuService = new BaseProductSkuService();
            var baseProductSku = baseSkuService.GetList(new List<string> { skuCode }).FirstOrDefault(x => x.FxUserId == curFxUserId && x.Status == 1);
            if (baseProductSku != null)
            {
                baseProductSku.Status = 0;
                baseProductSku.UpdateTime = DateTime.Now;
                baseSkuService.Update(baseProductSku);
            }
        }

        #endregion

        #region 仓库相关
        /// <summary>
        /// 查询列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public WarehouseListResponse LoadStoreManagementList(WarehouseListRequest req)
        {
            req.OwnerCode = GetOwnerCode();

            return _warehouseclient.Execute(req);
        }

        /// <summary>
        /// 启用停用
        /// </summary>
        /// <param name="warehouseCode"></param>
        /// <param name="status"></param>
        public void UpdateStoreManagementStatus(string warehouseCode, string status)
        {
            if (status == "Enabled")
            {
                WarehouseDisableRequest req = new WarehouseDisableRequest
                {
                    OwnerCode = GetOwnerCode(),
                    WarehouseCode = warehouseCode,
                };

                _warehouseclient.Execute(req);

                _repository.UpdateStatusByWareHouseCode("Disabled", warehouseCode);
            }
            else if (status == "Disabled")
            {
                WarehouseEnableRequest req = new WarehouseEnableRequest
                {
                    OwnerCode = GetOwnerCode(),
                    WarehouseCode = warehouseCode,
                };

                _warehouseclient.Execute(req);

                _repository.UpdateStatusByWareHouseCode("Enabled", warehouseCode);
            }
        }

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        public WarehouseAddResponse AddStoreManagement(WarehouseAddRequest requestModel)
        {
            //初始化货主
            OwnerInit(SiteContext.Current.CurrentFxUserId, SiteContext.Current.CurrentFxUser.Name, "FenDanSystem");
            requestModel.OwnerCode = GetOwnerCode();
            var rsp = _warehouseclient.Execute(requestModel);

            if (rsp.IsSucc)
            {
                _repository.AddWareHouseRelation(new WareHouseRelation
                {
                    CreateTime = DateTime.Now,
                    FxUserId = SiteContext.Current.CurrentFxUserId,
                    OwnerCode = requestModel.OwnerCode,
                    ShopId = 0,
                    Status = "Enabled",
                    UpdateTime = DateTime.Now,
                    WareHouseCode = rsp.WarehouseCode,
                });
            }

            return rsp;
        }

        /// <summary>
        /// 编辑
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        public WarehouseUpdateResponse EditStoreManagement(WarehouseUpdateRequest requestModel)
        {
            requestModel.OwnerCode = GetOwnerCode();
            return _warehouseclient.Execute(requestModel);
        }

        /// <summary>
        /// 查看货品
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public WarehouseStockListResponse LoadStoreManagementSkuList(WarehouseStockListRequest req)
        {
            req.OwnerCode = GetOwnerCode();

            return _warehouseclient.Execute(req);
        }

        public WarehouseCountResponse GetWareHouseCount(WarehouseCountRequest req)
        {
            req.OwnerCode = GetOwnerCode();

            return _warehouseclient.Execute(req);
        }
        #endregion


        /// <summary>
        /// 获取信息为复制副本，按PlatformSkuCode
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFieldNames"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<DianGuanJiaApp.Data.Entity.WareHouseSkuBindRelation> GetListForDuplication(List<string> codes, string selectFieldNames = "*",
        string whereFieldName = "PlatformSkuCode")
        {
            if (codes == null || !codes.Any())
                return new List<Data.Entity.WareHouseSkuBindRelation>();

            var list = new List<Data.Entity.WareHouseSkuBindRelation>();
            var batchSize = 500;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _wareHouseSkuBindRelationRepository.GetListForDuplication(batchCodes, selectFieldNames, whereFieldName);
                if (batchList != null)
                    list.AddRange(batchList);
            }

            return list;
        }

        /// <summary>
        /// 获取已存在列表
        /// </summary>
        /// <returns></returns>
        public List<IdAndCodeModel> GetExistIdAndCodes(List<string> codes)
        {
            if (codes == null || !codes.Any())
                return new List<IdAndCodeModel>();

            var list = new List<IdAndCodeModel>();
            var batchSize = 500;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _wareHouseSkuBindRelationRepository.GetExistIdAndCodes(batchCodes);
                if (batchList != null)
                    list.AddRange(batchList);
            }

            return list;
        }

        /// <summary>
        /// 批量插入数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        public void InsertsForDuplication(List<DianGuanJiaApp.Data.Entity.WareHouseSkuBindRelation> models)
        {
            if (models == null || !models.Any())
                return;
            //清理源库ID
            models.ForEach(m => { m.Id = 0; });

            var batchSize = 500;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();
                //代码
                var codes = batchModels.Select(m => m.SkuBindRelationCode).Distinct().ToList();
                //存在的代码列表
                var idAndCodes = GetExistIdAndCodes(codes);
                //全部不存在
                if (idAndCodes == null || !idAndCodes.Any())
                {
                    _wareHouseSkuBindRelationRepository.BulkInsert(batchModels);
                    //baseRepository.BulkWrite(batchModels, "WareHouseSkuBindRelation", maxSingleNum: 1);
                    continue;
                }
                //存在
                var existsCodes = idAndCodes.Select(m => m.Code).ToList();
                //var updates = batchModels.Where(m => existsCodes.Contains(m.SkuBindRelationCode)).ToList();
                //if (updates.Any())
                //{
                //    updates.ForEach(o =>
                //    {
                //        var model = idAndCodes.FirstOrDefault(m => m.Code == o.SkuBindRelationCode);
                //        if (model == null)
                //        {
                //            return;
                //        }
                //        o.Id = model.Id;
                //    });
                //    _wareHouseSkuBindRelationRepository.BulkUpdate(updates);
                //}
                //不存在
                var inserts = batchModels.Where(m => !existsCodes.Contains(m.SkuBindRelationCode)).ToList();
                if (inserts.Any())
                {
                    _wareHouseSkuBindRelationRepository.BulkInsert(inserts);
                    //baseRepository.BulkWrite(inserts, "WareHouseSkuBindRelation", maxSingleNum: 1);
                }
            }
        }

        /// <summary>
        /// 设置状态为-1
        /// </summary>
        /// <param name="shopIds"></param>
        public void SetWareHouseSkuBindRelationStatus(List<int> shopIds)
        {
            if (shopIds == null || !shopIds.Any())
                return;
            _wareHouseSkuBindRelationRepository.SetWareHouseSkuBindRelationStatus(shopIds);
        }


        /// <summary>
        /// 通过规格编码获取库存
        /// </summary>
        /// <param name="skuCodes"></param>
        public List<WarehouseProductSkuStockDetailResponse> GetStockBySkuCode(List<string> skuCodes)
        {
            WarehouseProductSkuListRequest request = new WarehouseProductSkuListRequest();
            request.OwnerCode = GetOwnerCode();
            request.SkuList = skuCodes;
            request.SkuType = true;
            var rsp = _warehouseclient.Execute(request);
            var res = new List<WarehouseProductSkuStockDetailResponse>();
            if (rsp.IsSucc && rsp.Items != null && rsp.Items.Count > 0)
            {
                res = rsp.Items;
            }
            return res;
        }

        /// <summary>
        /// 通过SkuCodes获取子规格信息
        /// </summary>
        /// <param name="skuCodes"></param>
        public WarehouseProductSkuContainChildResponse GetSkuContainChildListByCargoNumber(List<string> skuCodes)
        {
            var request = new WarehouseProductSkuContainChildGetRequest();
            request.OwnerCode = GetOwnerCode();
            request.SkuCodes = skuCodes;
            var rsp = _warehouseclient.Execute(request);

            return rsp;
        }

        /// <summary>
        /// 只更新Sku成本价
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public WarehouseSkuUpdateCostPriceResponse UpdateCostPrice(WarehouseSkuUpdateCostPriceRequest req)
        {
            if (req.Skus == null || req.Skus.Any() == false)
            {
                return new WarehouseSkuUpdateCostPriceResponse
                {
                    Code = "400",
                    Message = "参数错误"
                };
            }
            req.OwnerCode = GetOwnerCode();
            return _warehouseclient.Execute(req);
        }

        /// <summary>
        /// 只更新Sku成本价
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public WarehouseSkuUpdateShortTitleResponse UpdateShortTitle(WarehouseUpdateShorTitleRequest req)
        {
            if (req.Code.IsNullOrEmpty())
            {
                return new WarehouseSkuUpdateShortTitleResponse
                {
                    Code = "400",
                    Message = "参数错误"
                };
            }
            req.OwnerCode = GetOwnerCode();
            return _warehouseclient.Execute(req);
        }

        /// <summary>
        /// 获取组合商品变更日志
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public PageResultApiModel<CombinedProductChangeLogRes> GetCombinedProductChangeLog(ChangeLogLoadListRequest request)
        {
            request.OwnerCode = GetOwnerCode();
            var result = new PageResultApiModel<CombinedProductChangeLogRes>();
            var rsp = _warehouseclient.Execute(request);
            if (rsp == null || !rsp.IsSucc) throw new LogicException(rsp?.Message);

            var logs = rsp.ChangeLogs;
            var total = rsp.Total;
            var viewLogs = logs.Select(log => new CombinedProductChangeLogRes
            {
                ChangeType = log.ChangeType,
                Id = log.Id,
                CreateTime = log.CreateTime.ToString(CultureInfo.CurrentCulture),
                ChangeDetail = log.ChangeDetail
            }).ToList();
                
            result.Rows.AddRange(viewLogs);
            result.Total = total;
            return result;
        }



        /// <summary>
        /// 通过SkuCodes获取子规格信息
        /// </summary>
        /// <param name="skuCodes"></param>
        public WarehouseProductSkuDeleteCheckResponse DeleteSkuCheck(List<string> skuCodes, bool isOnlyCheck = false)
        {
            var request = new WarehouseProductSkuDeleteCheckRequest();
            request.OwnerCode = GetOwnerCode();
            request.IsOnlyCheck = isOnlyCheck;
            request.SkuCodes = skuCodes;
            var rsp = _warehouseclient.Execute(request);

            return rsp;
        }



        /// <summary>
        /// 通过SkuCodes获取子规格信息
        /// </summary>
        /// <param name="skuCodes"></param>
        public BaseWarehouseRespone UpdateProductStatus(List<string> skuCodes, List<string> productCodes,List<int> delRelationIds)
        {
            var request = new WarehouseProductUpdateStatusRequest();
            request.OwnerCode = GetOwnerCode();
            request.WareHouseSkuCodes = skuCodes;
            request.WareHouseProductCodes = productCodes;
            request.WareHouseRelationIds = delRelationIds;
            var rsp = _warehouseclient.Execute(request);
            return rsp;
        }
    }
}
