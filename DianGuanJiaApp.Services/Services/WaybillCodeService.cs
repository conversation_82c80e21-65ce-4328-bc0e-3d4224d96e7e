using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using System.Collections;
using Newtonsoft.Json;
using DianGuanJiaApp.Services.WaybillService;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Services.LogisticService;
using DianGuanJiaApp.Utility;
using System.Collections.Concurrent;
using DianGuanJiaApp.Data.Enum;
using sf_fengqiao_sdk.Model;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services.SyncDataInterface;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data;
using System.Diagnostics;
using System.Data;
using DianGuanJiaApp.Data.Model.Tools;
using DianGuanJiaApp.Services.Services;
using Nest;
using Org.BouncyCastle.Crypto;

namespace DianGuanJiaApp.Services
{

    public partial class WaybillCodeService : BaseService<Data.Entity.WaybillCode>
    {
        private WaybillCodeRepository _repository;
        private BranchShareRelationRepository branchShareRelationRepo = new BranchShareRelationRepository();

        private WaybillCodeChildRepository _waybillCodeChildRepo;
        private WaybillCodeOrderRepository _waybillCodeOrderRepo;
        private WaybillCodeOrderProductRepository _waybillCodeOrderProductRepo;
        private WaybillCodeUseRecordRepository _waybillCodeUseRecordRepo;
        private LogicOrderRepository _logicOrderRepo;
        private readonly WaybillCodeRecycleRepository waybillCodeRecycleRepository;

        private BranchShareRelationService _branchShareRelationService = new BranchShareRelationService();

        public WaybillCodeService()
        {
            _repository = new WaybillCodeRepository();

            _waybillCodeChildRepo = new WaybillCodeChildRepository();
            _waybillCodeOrderRepo = new WaybillCodeOrderRepository();
            _waybillCodeOrderProductRepo = new WaybillCodeOrderProductRepository();
            _waybillCodeUseRecordRepo = new WaybillCodeUseRecordRepository();
            _logicOrderRepo = new LogicOrderRepository();
            waybillCodeRecycleRepository = new WaybillCodeRecycleRepository();
        }

        public WaybillCodeService(string connectionString) : base(connectionString)
        {
            _repository = new WaybillCodeRepository(connectionString);

            _waybillCodeChildRepo = new WaybillCodeChildRepository(connectionString);
            _waybillCodeOrderRepo = new WaybillCodeOrderRepository(connectionString);
            _waybillCodeOrderProductRepo = new WaybillCodeOrderProductRepository(connectionString);
            _waybillCodeUseRecordRepo = new WaybillCodeUseRecordRepository(connectionString);
            _logicOrderRepo = new LogicOrderRepository(connectionString);
            waybillCodeRecycleRepository = new WaybillCodeRecycleRepository(connectionString);
        }
        /// <summary>
        /// 获取菜鸟电子面单的最后一次包裹ID
        /// </summary>
        /// <param name="platformOrderId">平台订单号(可以是合并订单ID)</param>
        /// <param name="shopId">店铺ID</param>
        /// <returns></returns>
        public string GetLastPid(string platformOrderId, int shopId)
        {
            return _repository.DbConnection.Query<string>("select Pid from P_WaybillCode WITH(NOLOCK) where PlatformOrderId=@platformOrderId AND ShopId=@shopId order by ID desc", new { platformOrderId, shopId })?.FirstOrDefault();
        }

        /// <summary>
        /// 获取电子面单打印记录详细信息
        /// </summary>
        /// <param name="keys">订单查询的key</param>
        /// <returns></returns>
        public List<WaybillCode> Get(List<OrderSelectKeyModel> keys, bool isCustomerOrder = false)
        {
            return _repository.GetWaybillCodes(keys, isCustomerOrder);
        }

        public List<WaybillCode> GetWaybillCodeListByWaybillCodes(List<string> codes, List<int> shopIds = null, List<string> fields = null, int pageSize = 500, string status = "1,3")
        {
            return _repository.GetWaybillCodeListByWaybillCodes(codes, shopIds, fields, pageSize, status);
        }

        public List<WaybillCode> GetWaybillCodeList(List<OrderSelectKeyModel> keys, List<string> fields, int pageSize = 50)
        {
            return _repository.GetWaybillCodeList(keys, fields, pageSize);
        }

        public List<WaybillCode> GetOnToManyWaybillCodeIds(string orderId, string waybillCode)
        {
            var model = _repository.GetPidByOrderIdAndWaybillCode(orderId, waybillCode);
            return _repository.GetWaybillCodeByPid(model?.Pid, model.ExpressId?.ToString(), model.TemplateType?.ToString(), model.ShopId, model.PathFlowCode);
        }


        public List<ScanPrintWaybillListModel> GetScanPrintOrderWaybill(string orderId, int shopId)
        {
            return _repository.GetScanPrintOrderWaybill(orderId, shopId);
        }

        /// <summary>
        /// 获取电子面单打印记录详细信息
        /// </summary>
        /// <param name="keys">订单查询的key</param>
        /// <param name="templateType">模板type</param>
        /// <param name="expressCpCode">kuaidi cp code</param>
        /// <param name="lastCount">包裹数量，用于获取最后的pid数量</param>
        /// <param name="isCustomerOrder">是否是自由打印订单</param>
        /// <param name="ignoreCancel">是否忽略取消的面单，pid改为自增的模式，原单号打印，获取上一次的pid，才需要排除回收，新单号打印不用排除</param>
        /// <returns></returns>
        public List<WaybillCode> GetLast(List<OrderSelectKeyModel> keys, int templateType, string expressCpCode, int lastCount = 1, bool isCustomerOrder = false, bool ignoreCancel = true, bool isFx = false)
        {
            var result = new List<WaybillCode>();

            //日志开始
            var subLog_getorders = LogForOperatorContext.Current.StartStep(new LogForOperator()
            {
                OperatorType = "获取订单最后打印pid信息"
            });
            result = _repository.GetLastWaybillCodes(keys, templateType, expressCpCode, lastCount, isCustomerOrder, ignoreCancel, isFx);
            //日志结束
            LogForOperatorContext.Current.EndStep(subLog_getorders);
            return result;
        }

        /// <summary>
        /// 添加电子面单记录
        /// </summary>
        /// <param name="request"></param>
        /// <param name="wcs"></param>
        public void Add(List<WaybillCode> wcs, ExpressPrintRequestModel request, int lockCount, int usedRecordId, out List<WaybillCode> existsWaybillCode)
        {
            //日志开始
            var log = LogForOperatorContext.Current.StartStep(new LogForOperator()
            {
                OperatorType = "保存WaybillCode",
            });
            var needEncryptWcs = wcs.Where(x => x.ReciverPhone?.Contains("*") == false && x.ReciverPhone?.Length < 20 || x.ReciverPhone?.StartsWith("$") == false)?.ToList();
            if (needEncryptWcs?.Any() == true)
                FxPlatformEncryptService.EncryptWaybillCodes(needEncryptWcs, encryptSender: true);
            var needUpdateOrderIsPreview = new List<OrderSelectKeyModel>();
            var needAddWcs = _repository.GetNeedAddWcs(wcs, log, needUpdateOrderIsPreview, out existsWaybillCode);
            //更新订单的预览状态（先更新预览状态，再保存底单）
            if (needUpdateOrderIsPreview?.Any() == true)
            {
                var subLog0 = new LogForOperator
                {
                    OperatorType = "更新逻辑单预览标识"
                };
                try
                {
                    log?.SubLogs.Add(subLog0);
                    _logicOrderRepo.UpdateOrderIsPreviewd(needUpdateOrderIsPreview);
                    List<DataChangeLog> tempDcLogs = needUpdateOrderIsPreview.Select(o => new DataChangeLog
                    {
                        DataChangeType = DataChangeTypeEnum.UPDATE_ISPREVIEWED,
                        TableTypeName = DataChangeTableTypeName.LogicOrder,
                        SourceShopId = o.ShopId,
                        SourceFxUserId = o.FxUserId,
                        RelationKey = o.PlatformOrderId,
                        ExtField1 = "UpdateOrderIsPreviewd"
                    }).ToList();
                    new DataChangeLogRepository().Add(tempDcLogs, 1);
                }
                catch (Exception ex)
                {
                    subLog0.Detail = needUpdateOrderIsPreview.ToJson();
                    subLog0.Exception = $"更新逻辑单预览标识异常：{ex}";
                    Log.WriteError(subLog0.Exception);
                    //throw ex;
                }
                finally
                {
                    subLog0.End();
                }
            }

            var addCount = _repository.Add(needAddWcs, log);

            //模板账号是否来自分享
            if (request.Template?.TemplateRelationAuthInfo?.BranchShareRelationId > 0 && usedRecordId > 0)
            {
                //加载分享信息
                var branchShareRelation = _branchShareRelationService.Get(request.Template.TemplateRelationAuthInfo.BranchShareRelationId.Value);
                if (branchShareRelation != null)
                {
                    branchShareRelation.UseRecordId = usedRecordId; //单号使用记录Id
                    //更新最后使用的数量
                    var returnQty = lockCount - addCount; //返回数量=占用的数量-使用的数量
                    branchShareRelation.UseQty = returnQty < 0 ? 0 : -returnQty; // 返回数量为负数
                    branchShareRelation.UsedType = 1; //-1:占用单号，1：打单使用，2：打单失败释放，3：回收单号释放
                    branchShareRelation.UsedTemplateId = request.Template.Id;
                    branchShareRelation = _branchShareRelationService.UpdateTotalUsedQuantity(branchShareRelation);
                }
            }

            LogForOperatorContext.Current.EndStep(log);

            //var log2 = LogForOperatorContext.Current.StartStep(new LogForOperator()
            //{
            //    OperatorType = "保存变更日志",
            //});
            #region 数据变更日志
            var dcLogs = wcs.Select(o => new DataChangeLog
            {
                DataChangeType = DataChangeTypeEnum.INSERT,
                TableTypeName = DataChangeTableTypeName.WaybillCode,
                SourceShopId = o.SourceShopId, 
                SourceFxUserId = o.UserId,
                RelationKey = o.UniqueKey,
                ExtField1 = "WaybillCodeService.Add"
            }).ToList();
            new DataChangeLogRepository().Add(dcLogs, 1);
            #endregion

            //LogForOperatorContext.Current.EndStep(log2);

            var log3 = LogForOperatorContext.Current.StartStep(new LogForOperator()
            {
                OperatorType = "副本数据同步",
            });
            #region 调用同步数据接口服务
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            new SyncDataInterfaceService(fxUserId).AddWaybillCodes(wcs, request?.IsCustomerOrder);

            /*
            var fxUserId = SiteContext.Current?.CurrentFxUser?.Id ?? 0;
            new SyncDataInterfaceService(fxUserId).WaybillCodeOpt(wcs, (string targetConnectionString, List<WaybillCode> targetWaybillCodes) =>
            {
                if (targetWaybillCodes != null && targetWaybillCodes.Any())
                {
                    //重置ID、WaybillCodeId、MasterId
                    targetWaybillCodes.ForEach(wc =>
                    {
                        wc.ID = 0;
                        wc.WaybillCodeOrders?.ForEach(wco =>
                        {
                            wco.ID = 0;
                            wco.WaybillCodeId = 0;
                            wco.WaybillCodeOrderProducts.ForEach(wcop =>
                            {
                                wcop.MasterId = 0;
                            });
                        });
                        wc.ChildWaybillCodes?.ForEach(child =>
                        {
                            child.Id = 0;
                            child.WaybillCodeId = 0;
                        });
                    });
                    //Utility.CommUtls.WriteToLog($"{targetConnectionString}==>{targetWaybillCodes.ToJson()}","WaybillCodeAdd.txt", "WaybillCode");
                    new WaybillCodeRepository(targetConnectionString).Add(targetWaybillCodes, request?.IsCustomerOrder, null, false);
                }
            });
            */
            #endregion
            LogForOperatorContext.Current.EndStep(log3);
        }

        /// <summary>
        /// 获取发往省份 去重
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public List<string> GetToProvinces(List<int> shopIds)
        {
            return _repository.GetToProvinces(shopIds);
        }

        /// <summary>
        /// 获取打印模板 去重
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public List<PrintTemplate> GetTemplateNames(List<int> shopIds, string sDate, string eDate)
        {
            return _repository.GetTemplateNames(shopIds, sDate, eDate);
        }

        ///// <summary>
        ///// 统计数量
        ///// </summary>
        ///// <param name="sql"></param>
        ///// <param name="paramList"></param>
        ///// <returns></returns>
        //public Tuple<int, int, int> StatisticsCount(WaybillCodeRequestModel queryModel, bool? isPdd = null)
        //{
        //    return _repository.StatisticsCount(queryModel, isPdd);
        //    var sqlStr = new StringBuilder();
        //    DynamicParameters parameters = new DynamicParameters();
        //    var agentIds = queryModel.AgentIds;//记录查询的商家id,公用的GetWhereCondition会天然拼接它,但是这里我们性能优化拆开了,所以公共方法里面我们不拼接,本方法内独立处理
        //    var supplierIds = queryModel.SupplierIds;//记录查询的厂家id,公用的GetWhereCondition会天然拼接它,但是这里我们性能优化拆开了,所以公共方法里面我们不拼接,本方法内独立处理
        //    queryModel.AgentIds = new List<int>();//为了调用共同方法不拼接此条件
        //    queryModel.SupplierIds = new List<int>();//为了调用共同方法不拼接此条件
        //    string sqlwhere = GetWhereCondition("wc", queryModel, parameters, isPdd);
        //    queryModel.AgentIds = agentIds;//调用完后再赋值回来
        //    queryModel.SupplierIds = supplierIds;//调用完后再赋值回来
        //    //var sql_count = "SELECT COUNT(DISTINCT wc.ExpressWayBillCode) AS WaybillCodeCount,COUNT(DISTINCT wc.OrderId) AS OrderCount FROM P_WaybillCode AS wc WITH(NOLOCK) LEFT JOIN P_WaybillCodeChild AS wcc  WITH(NOLOCK) ON wc.UniqueKey = wcc.ParentUniqueKey ";
        //    //优化
        //    //var wcJoin = CustomerConfig.CloudPlatformType != PlatformType.TouTiao.ToString() ? "wc.ID=wco.WaybillCodeId" : "wc.UniqueKey=wco.ParentUniqueKey";
        //    var wcJoin = "wc.ID=wco.WaybillCodeId";
        //    sqlStr.Append($@"  SELECT wc.ExpressWayBillCode,wco.OrderId
        //                        INTO #temp
        //                        FROM P_WaybillCode AS wc WITH(NOLOCK) 
        //                        INNER JOIN dbo.P_WaybillCodeOrder wco WITH(NOLOCK)  ON {wcJoin} ");
        //    sqlStr.Append(sqlwhere);
        //    //parameters.Add("FxUserId", queryModel.FxUserId, DbType.Int32);
        //    //parameters.Add("GetDate1", queryModel.StartDate, DbType.DateTime);
        //    //parameters.Add("GetDate2", queryModel.EndDate, DbType.DateTime);
        //    sqlStr.Append(@"  SELECT o.LogicOrderId,o.PlatformOrderId
        //                            INTO #temp2 
        //                            FROM LogicOrder AS o WITH(NOLOCK)  
        //                            INNER JOIN PathFlowNode AS pfn WITH(NOLOCK) on o.PathFlowCode = pfn.PathFlowCode
        //                       WHERE pfn.FxUserId = @wc_FxUserId ");
        //    //商家
        //    if (queryModel.AgentIds.Any())
        //    {
        //        sqlStr.Append($" And  pfn.UpFxUserId IN({string.Join(",", queryModel.AgentIds)})  ");
        //    }
        //    //厂家
        //    if (queryModel.SupplierIds.Any())
        //    {
        //        sqlStr.Append($" And  pfn.DownFxUserId IN({string.Join(",", queryModel.SupplierIds)})  ");
        //    }
        //    sqlStr.Append(@" SELECT COUNT(DISTINCT a.ExpressWayBillCode) AS WaybillCodeCount,COUNT(DISTINCT a.OrderId) AS OrderCount ,COUNT(DISTINCT b.PlatformOrderId) AS PlatCount
        //                    FROM #temp AS a
        //                    INNER JOIN	#temp2 AS b ON a.OrderId = b.LogicOrderId ");
        //    sqlStr.Append(@" DROP TABLE #temp
        //                    DROP TABLE #temp2 ");
        //    return _repository.StatisticsCount(sqlStr.ToString(), parameters);
        //}


        public List<ExpressWaybillCodeModel> LoadList(List<string> waybillCodes, List<int> shopIds, int defaultMonth = 2)
        {
            if (waybillCodes == null || waybillCodes.Any() == false)
                return new List<ExpressWaybillCodeModel>();
            waybillCodes = waybillCodes.Where(w => !string.IsNullOrEmpty(w)).Distinct().OrderBy(f => f).ToList();

            return _repository.LoadList(waybillCodes, shopIds, defaultMonth);
        }

        /// <summary>
        /// 查询底单记录(遍历各业务库)
        /// </summary>
        /// <param name="waybillCodes"></param>
        /// <param name="fxUserId"></param>
        /// <param name="defaultMonth"></param>
        /// <returns></returns>
        public List<ExpressWaybillCodeModel> LoadListByFxUserIds(List<string> waybillCodes, int fxUserId, int defaultMonth = 2)
        {
            if (waybillCodes == null || waybillCodes.Any() == false)
                return new List<ExpressWaybillCodeModel>();
            waybillCodes = waybillCodes.Where(w => !string.IsNullOrEmpty(w)).Distinct().OrderBy(f => f).ToList();

            return _repository.LoadListByFxUserIds(waybillCodes, fxUserId, defaultMonth);
        }

        public List<WaybillCode> LoadList(string startDate, string endDate, List<int> shopIds, int expressId)
        {
            var sql_data = "SELECT ID,ShopId,ExpressWayBillCode,ToProvince,TotalWeight,TotalPayAomount,ProductCount,TemplateName,BuyerMemberName,Reciver,Status,UniqueKey FROM P_WaybillCode WITH(NOLOCK) WHERE 1=1 ";
            var sql_count = "SELECT COUNT(*) FROM P_WaybillCode WITH(NOLOCK) WHERE 1=1 ";
            var condition = string.Empty;
            if (string.IsNullOrWhiteSpace(startDate) == false || string.IsNullOrWhiteSpace(endDate) == false)
            {
                DateTime sdate;
                var result_01 = DateTime.TryParse(startDate, out sdate);
                DateTime edate;
                var result_02 = DateTime.TryParse(endDate, out edate);
                if (result_01 == false || result_02 == false)
                {
                    var date_str = DateTime.Now.ToString("yyyy-MM-dd");
                    condition += $" AND [GetDate] BETWEEN '{date_str} 00:00:00' AND '{date_str} 23:59:59' ";
                }
                else
                {
                    var ts = edate - sdate;
                    if (ts.TotalDays > 32)
                    {
                        throw new LogicException("时间跨度不能操作一个月（30天）");
                    }
                    condition += $" AND [GetDate] BETWEEN '{sdate.ToString("yyyy-MM-dd HH:mm:ss")}' AND '{edate.ToString("yyyy-MM-dd HH:mm:ss")}' ";
                }
            }
            else
            {
                var date_str = DateTime.Now.ToString("yyyy-MM-dd");
                condition += $" AND [GetDate] BETWEEN '{date_str} 00:00:00' AND '{date_str} 23:59:59' ";
            }
            if (shopIds != null && shopIds.Count > 0)
            {
                if (shopIds.Count == 1)
                    condition += $" AND ShopId = {shopIds.First()}";
                else
                    condition += $" AND ShopId IN({string.Join(",", shopIds)})";
            }
            if (expressId != 0)
            {
                condition += $" AND ExpressId={expressId} ";
            }

            var data_count = _repository.DbConnection.ExecuteScalar<int>(sql_count + condition);
            if (data_count > 50000)
            {
                throw new LogicException($"查询数据量过大，请缩小时间范围【{data_count}】");
            }

            return _repository.DbConnection.Query<WaybillCode>(sql_data + condition).ToList();
        }

        /// <summary>
        /// 分页获取数据
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="paramList"></param>
        /// <returns></returns>
        [Obsolete("请使用LoadListV2方法")]
        public PagedResultModel<WaybillCode> LoadList(WaybillCodeRequestModel queryModel, WaybillCodeLoadListModel model = null)
        {
            return LoadListV2(queryModel, model);
            
            model = model != null ? model : new WaybillCodeLoadListModel
            {
                needPagging = true,
                QueryType = WaybillQueryType.DataAndCount
            };
            PagedResultModel<WaybillCode> result =  _repository.LoadList(queryModel, model);

            //if (CustomerConfig.IsCrossBorderSite)
            //{
            //    //物流类型
            //    result.Rows.ForEach(f => {
            //        f.LogisticType= 1; //国际段物流
            //    });

            //    //包裹id
            //    var orderItemList = new LogicOrderItemRepository().GetListByOrderIds(result.Rows.Select(s=>s.CustomerOrderId).ToList());
            //    if (orderItemList.Any())
            //    {
            //        result.Rows.ForEach(f=> {
            //            var orderItemModel = orderItemList.FirstOrDefault(w => w.PlatformOrderId.Equals(f.CustomerOrderId));
            //            if (orderItemModel != null)
            //            {
            //                f.PackageId = orderItemModel.PackageId ?? "";
            //            }
            //        });
            //    }

            //    //自寄物流
            //    Dictionary<string, OrderSelfDelivery> selfDeliveryDict = new Dictionary<string, OrderSelfDelivery>();
            //    var tkSelfDeliveryOrders = result.Rows.Where(f => !string.IsNullOrWhiteSpace(f.OrderSelfDeliveryBatchNo)).ToList();
            //    var tkSelfDeliveryBathNoList = tkSelfDeliveryOrders.Select(f => f.OrderSelfDeliveryBatchNo).Distinct().ToList();
            //    if (tkSelfDeliveryBathNoList.Any())
            //    {
            //        var selfDeliveryList = new OrderSelfDeliveryRepository().GetList(tkSelfDeliveryBathNoList);
            //        if (selfDeliveryList.Any())
            //        {
            //            result.Rows.ForEach(f => {

            //                var selfModel = selfDeliveryList.FirstOrDefault(w => w.BatchNo.Equals(f.OrderSelfDeliveryBatchNo));
            //                if (selfModel != null)
            //                {
            //                    f.LogisticType = 2;
            //                    f.ExpressCpCode = selfModel.ExpressCpCode;
            //                }
            //            });
            //        }
            //    }
            //}
            return result;
            //// 导出第一页已查询出数量，翻页不再查询
            //if (queryModel.IsExport)
            //    totalCountSql = "SELECT 0 AS total";
            //if(queryModel.IsExport)
            //    orderBySql = "wc.GetDate DESC,wc.Id DESC";

        }

        /// <summary>
        /// 分页获取数据
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="paramList"></param>
        /// <returns></returns>
        public PagedResultModel<WaybillCode> LoadListV2(WaybillCodeRequestModel queryModel, WaybillCodeLoadListModel model = null)
        {
            model = model != null ? model : new WaybillCodeLoadListModel
            {
                needPagging = true,
                QueryType = WaybillQueryType.DataAndCount
            };
            var page = _repository.LoadListV2(queryModel, model);
            //补充回收底单失败信息
            if (page.Total >0 && page.Rows.Any(t=>t.Status == 4))
            {
                var uniqueKeys = page.Rows.Where(t => t.Status == 4).Select(t => t.UniqueKey).ToList();
                var failList = waybillCodeRecycleRepository.GetLatestOneList(uniqueKeys);
                if (failList.Any())
                {
                    page.Rows.ForEach(t =>
                    {
                        var fail = failList.FirstOrDefault(w => w.UniqueKey == t.UniqueKey);
                        if (fail != null)
                        {
                            t.RecycleErrorMessage = fail.RecycleErrorMessage;
                            t.RecycleErrorDate = fail.CreateTime;
                        }
                    });
                }
            }
            return page;
        }

        ///// <summary>
        ///// 分页获取数据
        ///// </summary>
        ///// <param name="sql"></param>
        ///// <param name="paramList"></param>
        ///// <returns></returns>
        //public PagedResultModel<WaybillCode> LoadList2(WaybillCodeRequestModel queryModel, WaybillCodeLoadListModel model = null)
        //{
        //    DynamicParameters parameters = new DynamicParameters();
        //    StringBuilder sqlstr = new StringBuilder();

        //    var fieldStr = model == null || model.fields == null ? "wc.ID,wc.ShopId,wc.OrderId,wc.OrderIdJoin,wc.CustomerOrderId,wc.ExpressWayBillCode,wc.GetDate,wc.Reciver,wc.ReciverPhone,wc.ToProvince,wc.ToCity,wc.ToDistrict,wc.ToAddress,wc.ExpressId,wc.ExpressNo,wc.TemplateName,wc.Status,wc.TemplateId,wc.Sender,wc.ExpressName,wc.TotalWeight,wc.ProductCount,wc.TemplateType,wc.BuyerMemberName,wc.SendContent,wc.BuyerRemark,wc.SellerRemark,wc.TotalPayAomount,wc.SendDate,wc.CreateDate,wcc.ChildWaybillCode,wco.OrderId AS OrignalOrderId"
        //        : string.Join(",", model.fields);
        //    if (model != null && model.fields != null && model.fields.Any(x => x.Contains("wco.OrderId")) == false)
        //        fieldStr += ",wco.OrderId AS OrignalOrderId";

        //    sqlstr.Append($@" SELECT {fieldStr}
        //                    Into #temp
        //                    FROM P_WaybillCode AS wc
        //                    LEFT JOIN P_WaybillCodeChild AS wcc ON wc.UniqueKey = wcc.ParentUniqueKey   
        //                    INNER JOIN P_WaybillCodeOrder AS wco ON wc.UniqueKey = wco.ParentUniqueKey ");
        //    var tempagent = queryModel.AgentId;
        //    queryModel.AgentId = 0;
        //    var sql_condition = GetWhereCondition("wc", queryModel, parameters, model?.isPdd ?? false);
        //    queryModel.AgentId = tempagent;
        //    sqlstr.Append(sql_condition);
        //    sqlstr.Append(@"  SELECT o.LogicOrderId,o.PlatformOrderId
        //                            INTO #temp2 
        //                            FROM LogicOrder AS o WITH(NOLOCK)  
        //                            INNER JOIN PathFlowNode AS pfn WITH(NOLOCK) on o.PathFlowCode = pfn.PathFlowCode
        //                       WHERE pfn.FxUserId = @wc_FxUserId ");

        //    //商家
        //    if (queryModel.AgentId > 0)
        //    {
        //        sqlstr.Append(@" And  pfn.UpFxUserId=@UpFxUserId  ");
        //        parameters.Add("UpFxUserId", queryModel.AgentId, DbType.Int32);
        //    }

        //    if (model != null && model.QueryType == WaybillQueryType.OnlyData)
        //    {
        //        sqlstr.Append("SELECT 0 AS total");
        //    }
        //    else
        //    {
        //        sqlstr.Append(@" SELECT COUNT(DISTINCT a.ID) as total
        //                    FROM #temp AS a
        //                    INNER JOIN	#temp2 AS b ON a.OrignalOrderId = b.LogicOrderId ");
        //    }

        //    if (model != null && model.QueryType == WaybillQueryType.OnlyCount)
        //    {
        //        sqlstr.Append("SELECT 0 AS Id");
        //    }
        //    else
        //    {


        //        sqlstr.Append(string.Format(@" SELECT a.*
        //                    FROM #temp AS a
        //                    INNER JOIN ( SELECT *
        //                    FROM   ( SELECT    a.ID ,
        //                                        MIN(a.GetDate) GetDate
        //                              FROM      #temp AS a
        //                                        INNER JOIN #temp2 AS b ON a.OrignalOrderId = b.LogicOrderId
        //                              GROUP BY  a.ID
        //                            ) AS b
        //                    ORDER BY b.GetDate DESC,b.ID
        //                    OFFSET {0} ROWS FETCH NEXT {1} ROWS ONLY
        //           ) AS c ON c.ID = a.ID 
        //           ORDER BY c.GetDate DESC,c.ID;"
        //        , (queryModel.PageIndex - 1) * queryModel.PageSize, queryModel.PageSize));

        //    }

        //    sqlstr.Append(" Drop table #temp ");
        //    sqlstr.Append(" Drop table #temp2 ");


        //    //总数据条数


        //    #region 记录执行的SQL语句
        //    try
        //    {
        //        if (CustomerConfig.IsDebug)
        //        {
        //            var sqllog = GetRealSql(sqlstr.ToString(), parameters);
        //            sqllog += "\r\n-----------------------------------------------------\r\n";
        //            var path = AppDomain.CurrentDomain.BaseDirectory + "log\\";
        //            if (!Directory.Exists(path))
        //                Directory.CreateDirectory(path);
        //            using (StreamWriter sr = new StreamWriter(path + @"FxWaybillCodeSql.log", true))
        //            {
        //                sr.WriteLine(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "  " + sqllog);
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Utility.Log.WriteError($"店铺【{BaseSiteContext.Current.CurrentShopId}】底单日志记录异常：{ex}");
        //    }
        //    #endregion

        //    var data = _repository.LoadList(sqlstr.ToString(), parameters);
        //    return new PagedResultModel<WaybillCode>()
        //    {
        //        IsOrderDesc = queryModel.IsOrderDesc,
        //        OrderByField = queryModel.OrderByField,
        //        PageIndex = queryModel.PageIndex,
        //        PageSize = queryModel.PageSize,
        //        Rows = data.Item2,
        //        Total = data.Item1
        //    };
        //}

        /// <summary>
        /// 分页获取数据
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="paramList"></param>
        /// <returns></returns>

        public Tuple<List<WaybillCode>, int> LoadListForExportExcel(WaybillCodeRequestModel queryModel, List<string> fields, bool? isPdd = null)
        {
            var tuple = _repository.LoadListForExportExcel(queryModel, fields, isPdd);
            return tuple;

            //var list = new List<WaybillCode>();
            //var pageSize = 1000;
            //var returnCount = pageSize;
            //var pageIndex = 0;
            //while (returnCount == pageSize)
            //{
            //    var tmpSql = $@"{sql}
            //                    OFFSET { pageIndex * pageSize} ROWS FETCH NEXT { pageSize} ROWS ONLY  ";
            //    var tmpList = new List<WaybillCode>();
            //    for (var i = 0; i < 3; i++)
            //    {
            //        try
            //        {
            //            tmpList = _repository.LoadListForExportExcel(tmpSql, parameters);
            //            returnCount = tmpList.Count;
            //            pageIndex++;
            //            if (tmpList.Any())
            //                list.AddRange(tmpList);
            //            break;
            //        }
            //        catch (Exception ex)
            //        {
            //            if (i == 2)
            //                throw ex;
            //            continue;
            //        }
            //    }
            //}
            //list.Reverse();
            //return list;
        }

        /// <summary>
        /// 获取某个运单的使用记录
        /// </summary>
        /// <param name="waybillCodeOrderId"></param>
        /// <returns></returns>
        public List<WaybillCode> GetWaybillCodeList(string waybillCode, int expressId, int fxUserId = 0)
        {
            var whereSql = string.Empty;
            if (fxUserId > 0)
                whereSql = $" WHERE FxUserId={fxUserId} AND ExpressWayBillCode=@ExpressWayBillCode AND ExpressId=@ExpressId  ";
            else
                whereSql = $" WHERE ExpressWayBillCode=@ExpressWayBillCode AND ExpressId=@ExpressId  ";
            var dataList = _repository.Get(whereSql, new { ExpressWayBillCode = waybillCode, ExpressId = expressId }).ToList();
            dataList.ForEach(t =>
            {
                t.TotalWeight = t.TotalWeight.ConvertGToKg();
            });
            return dataList;
        }

        public ReprintWaybillCodeModel GetReprintWaybillCodeModel(int waybillCodeId)
        {
            return _repository.GetReprintWaybillCodeModel(waybillCodeId);
        }

        private void GetReceiveCondition(string tableAsName, StringBuilder sqlCondition, WaybillCodeRequestModel queryModel, DynamicParameters parameters)
        {
            if (queryModel.ReciverPhone.IsNullOrEmpty())
                return;

            var platformType = CustomerConfig.CloudPlatformType;
            if (platformType == PlatformType.Alibaba.ToString())
            {
                // 1.获取当前登录用户路劲流上的店铺
                var _pathFlowService = new PathFlowService();
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var pathFlowList = _pathFlowService.GetPathFlowByFxUserId(fxUserId);
                var selectSids = pathFlowList.Where(f => f.SourceShopId > 0).Select(f => f.SourceShopId).Distinct().ToList();
                //通过店铺id查询店铺
                var shopService = new ShopService();
                var shops = shopService.GetShopsAndShopExtension(selectSids);

                //如果没有店铺id和平台，则说明用户没有需要处理的订单
                if (selectSids.Any() == false)
                {
                    sqlCondition.Append(" AND 1!=1");
                    return;
                }

                var receiverSqlCondition = new StringBuilder();
                var shopGroups = shops.GroupBy(x => x.PlatformType).ToList();
                foreach (var item in shopGroups)
                {
                    var pt = item.Key;
                    var gshops = item.ToList();
                    if (pt == PlatformType.TouTiao.ToString() || pt == PlatformType.TouTiaoSaleShop.ToString())
                    {
                        // 抖音加密查询
                        var index = PlatformService.ZhiDianNewPlatformService.GetSearchIndex(queryModel.ReciverPhone, DouyinEncryptType.MobiePhone);
                        if (index.IsNotNullOrEmpty())
                        {
                            receiverSqlCondition.Append($" OR {tableAsName}.ReciverPhone like @{tableAsName}_zdPhoneIndex");
                            parameters.Add($"{tableAsName}_zdPhoneIndex", $"%{index}%");
                        }
                    }
                    else if (pt == PlatformType.YouZan.ToString())
                    {
                        foreach (var s in gshops)
                        {
                            try
                            {
                                var youzanPtService = new YouZanPlatformService(gshops.FirstOrDefault());
                                var index = youzanPtService.EncryptSingle(queryModel.ReciverPhone);
                                if (index.IsNotNullOrEmpty())
                                {
                                    receiverSqlCondition.Append($" OR {tableAsName}.ReciverPhone like @{tableAsName}_yzPhoneIndex");
                                    parameters.Add($"{tableAsName}_yzPhoneIndex", $"%{index}%");
                                }
                                break;
                            }
                            catch (Exception)
                            {

                            }
                        }
                    }
                    else if (pt == PlatformType.Alibaba.ToString() || pt == PlatformType.AlibabaC2M.ToString() || pt == PlatformType.Taobao.ToString() || pt == PlatformType.KuaiShou.ToString() || pt == PlatformType.TaobaoMaiCaiV2.ToString())
                    {
                        //  178****1559  alibaba
                        //  *******6936  taobao
                        //  13*******59  alibabac2m
                        var phone = queryModel.ReciverPhone;
                        if (phone.Length == 11)
                        {
                            if (pt == PlatformType.Alibaba.ToString() || pt == PlatformType.KuaiShou.ToString())
                                phone = phone.Substring(0, 3) + "****" + phone.Substring(7, 4);
                            else if (pt == PlatformType.Taobao.ToString())
                                phone = "*******" + phone.Substring(7, 4);
                            else if (pt == PlatformType.AlibabaC2M.ToString() || pt == PlatformType.TaobaoMaiCaiV2.ToString())
								phone = phone.Substring(0, 2) + "*******" + phone.Substring(9, 2);
                        }
                        receiverSqlCondition.Append($" OR {tableAsName}.ReciverPhone=@{tableAsName}_{pt}PhoneIndex");
                        parameters.Add($"{tableAsName}_{pt}PhoneIndex", phone);

                    }
                }

                if (receiverSqlCondition.IsNullOrEmpty())
                {
                    sqlCondition.Append($" AND {tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone");
                    parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                }
                else
                {
                    var tmpSqlCondition = receiverSqlCondition.ToString().Trim().TrimEnd("OR").TrimStart("OR");
                    sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tmpSqlCondition})");
                    parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                }

            }
            else if (platformType == PlatformType.Pinduoduo.ToString())
            {
                sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tableAsName}.ReciverPhone like @{tableAsName}_pddPhoneIndex)");
                parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                var index = PlatformService.PinduoduoPlatformService.GetSearchEncryptString(queryModel.ReciverPhone, true);
                if (string.IsNullOrWhiteSpace(index))
                    parameters.Add($"{tableAsName}_pddPhoneIndex", $"%{queryModel.ReciverPhone}%");
                else
                    parameters.Add($"{tableAsName}_pddPhoneIndex", $"%{index}%");
            }
            else if (platformType == PlatformType.Jingdong.ToString())
            {
                var phone = queryModel.ReciverPhone;
                if (phone.Length == 11)
                    phone = phone.Substring(0, 3) + "********" + phone.Substring(10, 1);
                sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tableAsName}.ReciverPhone=@{tableAsName}_jdPhoneIndex)");
                parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                parameters.Add($"{tableAsName}_jdPhoneIndex", phone);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tableAsName"></param>
        /// <param name="sqlCondition"></param>
        /// <param name="queryModel"></param>
        /// <param name="parameters"></param>
        private void GetReceiveConditionV2(string tableAsName, StringBuilder sqlCondition, WaybillCodeRequestModel queryModel, DynamicParameters parameters)
        {
            if (queryModel.ReciverPhone.IsNullOrEmpty())
                return;

            var platformType = CustomerConfig.CloudPlatformType;
            if (platformType == PlatformType.Alibaba.ToString())
            {
                var phone = queryModel.ReciverPhone;

                #region 兼容密文搜索，正式上线一段时间后可移除密文搜索兼容

                var receiverSqlCondition = new StringBuilder();

                // 1.获取当前登录用户路劲流上的店铺
                var _pathFlowService = new PathFlowService();
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var pathFlowList = _pathFlowService.GetPathFlowByFxUserId(fxUserId);
                var selectSids = pathFlowList.Where(f => f.SourceShopId > 0).Select(f => f.SourceShopId).Distinct().ToList();
                //通过店铺id查询店铺
                var shopService = new ShopService();
                var shops = shopService.GetShopsAndShopExtension(selectSids);

                //如果没有店铺id和平台，则说明用户没有需要处理的订单
                if (selectSids.Any() == false)
                {
                    sqlCondition.Append(" AND 1!=1");
                    return;
                }

                var shopGroups = shops.GroupBy(x => x.PlatformType).ToList();
                foreach (var item in shopGroups)
                {
                    var pt = item.Key;
                    var gshops = item.ToList();
                    if (pt == PlatformType.TouTiao.ToString() || pt == PlatformType.TouTiaoSaleShop.ToString())
                    {
                        // 抖音加密查询
                        var index = PlatformService.ZhiDianNewPlatformService.GetSearchIndex(queryModel.ReciverPhone, DouyinEncryptType.MobiePhone);
                        if (index.IsNotNullOrEmpty())
                        {
                            receiverSqlCondition.Append($" OR {tableAsName}.ReciverPhone like @{tableAsName}_zdPhoneIndex");
                            parameters.Add($"{tableAsName}_zdPhoneIndex", $"${index}%");
                        }
                    }
                    else if (pt == PlatformType.YouZan.ToString())
                    {
                        foreach (var s in gshops)
                        {
                            try
                            {
                                var youzanPtService = new YouZanPlatformService(gshops.FirstOrDefault());
                                var index = youzanPtService.EncryptSingle(queryModel.ReciverPhone);
                                if (index.IsNotNullOrEmpty())
                                {
                                    receiverSqlCondition.Append($" OR {tableAsName}.ReciverPhone like @{tableAsName}_yzPhoneIndex");
                                    parameters.Add($"{tableAsName}_yzPhoneIndex", $"${index}%");
                                }
                                break;
                            }
                            catch (Exception)
                            {

                            }
                        }
                    }
                }

                #endregion

                if (receiverSqlCondition.IsNullOrEmpty())
                {
                    //  178****1559  alibaba,kuaishou
                    //  *******6936  taobao
                    //  13*******59  alibabac2m,toutiao,pingduoduo
                    if (phone.Length == 11)
                    {
                        var phone1 = phone.Substring(0, 3) + "****" + phone.Substring(7, 4);
                        var phone2 = "*******" + phone.Substring(7, 4);
                        var phone3 = phone.Substring(0, 2) + "*******" + phone.Substring(9, 2);

                        var phoneList = new List<string> { phone, phone1, phone2, phone3 };

                        sqlCondition.Append($" AND {tableAsName}.ReciverPhone IN @{tableAsName}_ReciverPhone ");
                        parameters.Add($"{tableAsName}_ReciverPhone", phoneList);
                    }
                    else
                    {
                        sqlCondition.Append($" AND {tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone");
                        parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                    }
                }
                else
                {
                    //兼容密文搜索，正式上线一段时间后可移除密文搜索兼容

                    var tmpSqlCondition = receiverSqlCondition.ToString().Trim().TrimEnd("OR").TrimStart("OR");

                    //  178****1559  alibaba,kuaishou
                    //  *******6936  taobao
                    //  13*******59  alibabac2m,toutiao,pingduoduo
                    if (phone.Length == 11)
                    {
                        var phone1 = phone.Substring(0, 3) + "****" + phone.Substring(7, 4);
                        var phone2 = "*******" + phone.Substring(7, 4);
                        var phone3 = phone.Substring(0, 2) + "*******" + phone.Substring(9, 2);

                        var phoneList = new List<string> { phone, phone1, phone2, phone3 };

                        sqlCondition.Append($" AND ({tableAsName}.ReciverPhone IN @{tableAsName}_ReciverPhone OR {tmpSqlCondition})");
                        parameters.Add($"{tableAsName}_ReciverPhone", phoneList);
                    }
                    else
                    {
                        sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tmpSqlCondition})");
                        parameters.Add($"{tableAsName}_ReciverPhone", phone);
                    }

                }


            }
            else if (platformType == PlatformType.Pinduoduo.ToString())
            {
                //sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tableAsName}.ReciverPhone like @{tableAsName}_pddPhoneIndex)");
                //parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                //var index = PlatformService.PinduoduoPlatformService.GetSearchEncryptString(queryModel.ReciverPhone, true);
                //if (string.IsNullOrWhiteSpace(index))
                //    parameters.Add($"{tableAsName}_pddPhoneIndex", $"%{queryModel.ReciverPhone}%");
                //else
                //    parameters.Add($"{tableAsName}_pddPhoneIndex", $"%{index}%");

                sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tableAsName}.ReciverPhone like @{tableAsName}_pddPhoneIndex)");

                var phone = queryModel.ReciverPhone;
                var index = PlatformService.PinduoduoPlatformService.GetSearchEncryptString(phone, true);
                if (string.IsNullOrWhiteSpace(index))
                    parameters.Add($"{tableAsName}_pddPhoneIndex", $"%{phone}%");
                else
                    parameters.Add($"{tableAsName}_pddPhoneIndex", $"%{index}%");

                if (phone.Length == 11)
                {
                    var phone3 = phone.Substring(0, 2) + "*******" + phone.Substring(9, 2);
                    //var phoneList = new List<string> { phone, phone3 };
                    parameters.Add($"{tableAsName}_ReciverPhone", phone3);
                }
                else
                {
                    parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                }
            }
            else if (platformType == PlatformType.Jingdong.ToString())
            {
                var phone = queryModel.ReciverPhone;
                if (phone.Length == 11)
                    phone = phone.Substring(0, 3) + "********" + phone.Substring(10, 1);
                sqlCondition.Append($" AND ({tableAsName}.ReciverPhone=@{tableAsName}_ReciverPhone OR {tableAsName}.ReciverPhone=@{tableAsName}_jdPhoneIndex)");
                parameters.Add($"{tableAsName}_ReciverPhone", queryModel.ReciverPhone);
                parameters.Add($"{tableAsName}_jdPhoneIndex", phone);
            }
        }

        /// <summary>
        /// 更新回收异常的底单
        /// </summary>
        /// <param name="dic"></param>
        public void RecycleErrorWaybillCode(Dictionary<int,string> dic)
        {
            if (dic.IsNullOrEmptyList())
                return;
            var subLog = LogForOperatorContext.Current.StartStep(new LogForOperator()
            {
                OperatorType = "更新底单状态"
            });
            var waybillCodeIdList = dic.Keys.ToList();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var wcs = GetByWaybillCodeIds(waybillCodeIdList, fields: new List<string> { "Id", "ExpressWayBillCode", "Status", "PathFlowCode", "UniqueKey" });
            var list = (from w in wcs
                       join d in dic on w.ID equals d.Key
                       select new WaybillCodeRecycle()
                       {
                           WaybillCodeId = w.ID,
                           UniqueKey = w.UniqueKey,
                           RecycleErrorMessage = d.Value,
                           CreateTime = DateTime.Now,
                           FxUserId = fxUserId
                       }).ToList();
            Log.Debug($"回收单号失败，list=>{list.ToJson()}", "WaybillCodRecycleErrore.txt");
            var count = waybillCodeRecycleRepository.AddWaybillCodeRecycle(list);
            subLog.Detail = count;// 底单更新受影响行数
            #region 调用同步数据接口服务
            new SyncDataInterfaceService(fxUserId).WaybillCodeOpt(wcs, (string targetConnectionString, List<WaybillCode> targetWcs) =>
            {
                if (targetWcs.IsNotNullAndAny())
                {
                    var targetList = (from w in targetWcs
                                      join d in dic on w.ID equals d.Key
                                      select new WaybillCodeRecycle()
                                      {
                                          WaybillCodeId = w.ID,
                                          UniqueKey = w.UniqueKey,
                                          RecycleErrorMessage = d.Value,
                                          CreateTime = DateTime.Now,
                                          FxUserId = fxUserId
                                      }).ToList();
                    Log.Debug($"回收单号失败副本，db：{targetConnectionString}，targetWcs=>{targetList.ToJson()}", "WaybillCodRecycleErrore.txt");
                    new WaybillCodeRecycleRepository(targetConnectionString).AddWaybillCodeRecycle(targetList);
                }
            });
            #endregion
            //日志结束
            LogForOperatorContext.Current.EndStep(subLog);
        }

        /// <summary>
        /// 更新电子面单的状态
        /// status ： 1：已打印，2：单号已回收，3：已发货
        /// </summary>
        /// <param name="status"></param>
        /// <param name="waybillCodeIdList"></param>
        /// <param name="template"></param>
        /// <returns></returns>
        public int UpdateWaybillCodeStatusById(int status, List<int> waybillCodeIdList, PrintTemplate template)
        {
            //日志开始
            var subLog = LogForOperatorContext.Current.StartStep(new LogForOperator()
            {
                OperatorType = "更新底单状态"
            });

            var sql = "UPDATE P_WaybillCode SET [Status]=@Status WHERE ID IN@Ids AND [Status]<>2";
            sql = sql.Replace("@Status", status.ToString()).Replace("@Ids", $"({string.Join(",", waybillCodeIdList)})");
            var count = _repository.DbConnection.Execute(sql);

            subLog.Detail = count;// 底单更新受影响行数

            //回收成功，如果使用的是分享的单号，则回退分享的单号
            if (count > 0)
            {
                BranchShareRelation branchShareInfo = null;
                //如果是分享的单号，则加载分享关系
                if (template.TemplateRelationAuthInfo?.BranchShareRelationId > 0)
                {
                    branchShareInfo = _branchShareRelationService.Get(template.TemplateRelationAuthInfo.BranchShareRelationId.Value);
                }

                if (branchShareInfo != null)
                {
                    branchShareInfo.UseQty = -count; //批量回收数量
                    branchShareInfo.UsedType = 3; //-1：锁定数量，1：打单使用，2：打单失败释放，3：回收单号释放
                    branchShareInfo.UsedTemplateId = template.Id;
                    //zouyi 2021-12-16 需求变更：回收单号后，单号加回余额。需求：https://www.tapd.cn/61741848/prong/stories/view/1161741848001008116
                    //branchShareInfo = _branchShareRelationService.UpdateTotalUsedQuantity(branchShareInfo);

                    if (branchShareInfo.IsUpdateSuccess == false)
                        subLog.Remark = "还回可用单号失败";
                    else
                        subLog.Remark = "还回可用单号成功";

                    try
                    {
                        // 上月打印快递单，这个月回收，更新上月使用数量
                        var curMonthDate = DateTime.Now.ToString("yyyy-MM-01").toDateTime();
                        var lastMonthDate = DateTime.Now.AddMonths(-1).ToString("yyyy-MM-01").toDateTime();
                        var lastBeforeMonthDate = DateTime.Now.AddMonths(-2).ToString("yyyy-MM-01").toDateTime();
                        var waybillList = _repository.GetWaybillCodeListByIds(waybillCodeIdList, new List<string> { "ID", "GetDate" }, 500);
                        if (waybillList != null && waybillList.Any())
                        {
                            var lastMonthCancelCount = waybillList.Count(w => w.GetDate >= lastMonthDate && w.GetDate < curMonthDate);
                            var lastBeforeMonthCancelCount = waybillList.Count(w => w.GetDate >= lastBeforeMonthDate && w.GetDate < lastMonthDate);
                            if (lastMonthCancelCount > 0 || lastBeforeMonthCancelCount > 0)
                            {
                                BranchShareUsedStatisticRepository bsr = new BranchShareUsedStatisticRepository();
                                var list = bsr.GetByShareId(branchShareInfo.Id);
                                if (list != null && list.Any())
                                {
                                    var needUpdateList = new List<BranchShareUsedStatistic>();
                                    // 跨上个月回收单号
                                    if (lastMonthCancelCount > 0)
                                    {
                                        var item = list.FirstOrDefault(b => b.Month == lastMonthDate.Month && b.Year == lastMonthDate.Year && b.UsedCount > 0);
                                        if (item != null)
                                        {
                                            subLog.Remark += $",{lastMonthDate.Month}月原使用【{item.UsedCount}】回收【{lastMonthCancelCount}】个分享单号";
                                            item.UsedCount = item.UsedCount > lastMonthCancelCount ? item.UsedCount - lastMonthCancelCount : 0;
                                            needUpdateList.Add(item);
                                        }
                                    }
                                    //跨上上个月回收单号
                                    if (lastBeforeMonthCancelCount > 0)
                                    {
                                        var item = list.FirstOrDefault(b => b.Month == lastBeforeMonthDate.Month && b.Year == lastBeforeMonthDate.Year && b.UsedCount > 0);
                                        if (item != null)
                                        {
                                            subLog.Remark += $",{lastBeforeMonthDate.Month}月原使用【{item.UsedCount}】回收【{lastBeforeMonthCancelCount}】个分享单号";
                                            item.UsedCount = item.UsedCount > lastBeforeMonthCancelCount ? item.UsedCount - lastBeforeMonthCancelCount : 0;
                                            needUpdateList.Add(item);
                                        }
                                    }
                                    if (needUpdateList.Any())
                                    {
                                        bsr.BulkMerge(needUpdateList);
                                        subLog.Remark += "，更新历史使用单号成功";
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        subLog.Remark += $"，跨月回收单号异常：{ex}";
                        Utility.Log.WriteError($"跨月回收单号异常：{ex}");
                    }
                }
            }
            

            #region 调用同步数据接口服务
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var wcs = GetByWaybillCodeIds(waybillCodeIdList, fields: new List<string> { "Id", "ExpressWayBillCode", "Status", "PathFlowCode" });
            new SyncDataInterfaceService(fxUserId).WaybillCodeOpt(wcs, (string targetConnectionString, List<WaybillCode> targetWcs) =>
            {
                //Log.Debug($"回收单号副本同步，targetWcs=>{targetWcs.ToJson()}");
                if (targetWcs != null && targetWcs.Any())
                {
                    var codes = targetWcs.Select(x => x.ExpressWayBillCode).Distinct().ToList();
                    var execSql = $@"UPDATE wc SET [Status]='{status}' FROM P_WaybillCode wc 
INNER JOIN dbo.FunStringToTable(@Codes,',') t ON t.item=wc.ExpressWayBillCode
WHERE [Status]<>2";
                    //Log.Debug($"回收单号副本同步，SQL=>{execSql}");
                    new WaybillCodeRepository(targetConnectionString).DbConnection.Execute(execSql,
                        new { Codes = string.Join(",", codes) });
                }
            });
            #endregion

            //日志结束
            LogForOperatorContext.Current.EndStep(subLog);

            return count;
        }

        /// <summary>
        /// 修改底单未发货类型 通过ID
        /// </summary>
        /// <param name="sendFailType"></param>
        /// <param name="waybillCodeIds"></param>
        /// <returns></returns>
        public int UpdateSendFailTypeById(int sendFailType, List<int> waybillCodeIds)
        {
            if (waybillCodeIds.IsNullOrEmpty()) return 0;

            var count = _repository.UpdateSendFailTypeById(sendFailType, waybillCodeIds);

            #region 调用同步数据接口服务
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var wcs = GetByWaybillCodeIds(waybillCodeIds, fields: new List<string> { "ID", "ExpressWayBillCode", "UniqueKey"});
            new SyncDataInterfaceService(fxUserId).WaybillCodeOpt(wcs, (string targetConnectionString, List<WaybillCode> targetWcs) =>
            {
                if (targetWcs != null && targetWcs.Any())
                {
                    new WaybillCodeRepository(targetConnectionString).UpdateSendFailTypeByUniqueKey(sendFailType, targetWcs.Select(w=>w.UniqueKey).ToList());
                }
            });
            #endregion

            return count;
        }

        /// <summary>
        /// 修改底单未发货类型 通过运单号ExpressWayBillCode
        /// </summary>
        /// <param name="sendFailType"></param>
        /// <param name="waybillCodes"></param>
        /// <returns></returns>
        public int UpdateSendFailTypeByWaybillCodes(int sendFailType, List<string> waybillCodes)
        {
            if (waybillCodes.IsNullOrEmpty()) return 0;

            var count = _repository.UpdateSendFailTypeByWaybillCodes(sendFailType, waybillCodes);

            #region 调用同步数据接口服务
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var wcs = GetWaybillCodeListByWaybillCodes(waybillCodes, fields: new List<string> { "ID", "ExpressWayBillCode", "UniqueKey" });
            new SyncDataInterfaceService(fxUserId).WaybillCodeOpt(wcs, (string targetConnectionString, List<WaybillCode> targetWcs) =>
            {
                if (targetWcs != null && targetWcs.Any())
                {
                    new WaybillCodeRepository(targetConnectionString).UpdateSendFailTypeByUniqueKey(sendFailType, targetWcs.Select(w => w.UniqueKey).ToList());
                }
            });
            #endregion

            return count;
        }

        public int UpdateSendFailTypeByWaybillCodes(int sendFailType,List<OrderRequestModel> orders)
        {
            if (orders.IsNullOrEmpty()) return 0;

            var waybillCodes = new List<string>();
            orders.ForEach(o =>
            {
                if(o.WaybillCode.IsNotNullOrEmpty())
                    waybillCodes.Add(o.WaybillCode);
                if(o.MultiPackSendModels.IsNotNullOrEmpty())
                    waybillCodes.AddRange(o.MultiPackSendModels.Select(m=>m.WaybillCode));
            });
            return UpdateSendFailTypeByWaybillCodes(sendFailType, waybillCodes.Where(c=>c.IsNotNullOrEmpty()).Distinct().ToList());
        }

        /// <summary>
        /// 更新发货状态
        /// </summary>
        /// <param name="waybillCodes"></param>
        /// <param name="sendDate"></param>
        /// <returns></returns>
        public void UpdateSendStatusWithSyncInterface(List<WaybillCode> waybillCodes, DateTime sendDate)
        {
            //判空处理
            if (waybillCodes == null || !waybillCodes.Any())
            {
                return;
            }

            try
            {
                //更新发货状态
                _repository.UpdateSendStatusByExpressWayBillCodes(waybillCodes.Select(m => m.ExpressWayBillCode).ToList(), sendDate);

                #region 调用同步数据接口服务
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                new SyncDataInterfaceService(fxUserId).WaybillCodeOpt(waybillCodes, (targetConnectionString, targetWcs) =>
                {
                    if (targetWcs != null && targetWcs.Any())
                    {
                        var codes = targetWcs.Select(x => x.ExpressWayBillCode).Distinct().ToList();
                        new WaybillCodeRepository(targetConnectionString).UpdateSendStatusByExpressWayBillCodes(codes, sendDate);
                    }
                });
                #endregion
            }
            catch (Exception e)
            {
                Log.WriteError($"更新底单发货状态失败，失败原因：{e.Message}，堆栈信息：{e.StackTrace}",
                    $"UpdateSendStatusWithSyncInterface_{DateTime.Now:yyyy-MM-dd}.log");
            }
        }
        
        /// <summary>
        /// 更新电子面单的状态
        /// status ： 1：已打印，2：单号已回收，3：已发货
        /// </summary>
        /// <param name="status"></param>
        /// <param name="expressId"></param>
        /// <param name="waybillCodeList"></param>
        /// <param name="dcLogs"></param>
        /// <returns></returns>
        public void UpdateWaybillCodeStatusByCode(int status, int expressId, List<KeyValuePair<int, string>> waybillCodeList, List<DataChangeLog> dcLogs = null)
        {
            var updateSendDate = ",SendDate=(GETDATE())";
            if (CustomerConfig.IsRepairSendHistory())
                updateSendDate = $",SendDate='{CustomerConfig.RepairSendHistoryDate()}'";

            var sql = $"UPDATE P_WaybillCode SET [Status]=@Status{updateSendDate} WHERE Status!=@Status AND ExpressWayBillCode IN@ExpressWayBillCode ";
            waybillCodeList?.GroupBy(f => f.Key)?.ToList()?.ForEach(g =>
            {
                var shopId = g.Key;
                var wcl = g.Select(f => $"'{f.Value}'").ToList();
                wcl.Add("'a'");
                var excs = $"({string.Join(",", wcl)})";
                var tempSql = sql.Replace("@Status", status.ToString()).Replace("@ExpressId", expressId.ToString()).Replace("@ExpressWayBillCode", excs);
                //_repository.DbConnection.Execute(sql, new { Status = status, shopId = shopId, ExpressWayBillCode = wcl?.Distinct() });
                _repository.DbConnection.Execute(tempSql);
            });

            #region 数据变更日志
            if (dcLogs != null && dcLogs.Any())
                new DataChangeLogRepository().Add(dcLogs, 1);
            #endregion
            //已前置处理了同步接口服务，此处不用再处理
        }

        /// <summary>
        /// 更新底单“单号类型”
        /// </summary>
        /// <param name="sendType"></param>
        /// <param name="curLoginShopId"></param>
        /// <param name="waybillCodes"></param>
        public int UpdateSendTypeByCode(int sendType, int curLoginShopId, List<string> waybillCodes)
        {
            var sql = $"UPDATE P_WaybillCode SET SendType=@SendType WHERE ExpressWayBillCode IN@ExpressWayBillCode AND ShopId=@ShopId ";
            return _repository.DbConnection.Execute(sql, new { SendType = sendType, ExpressWayBillCode = waybillCodes, ShopId = curLoginShopId });

            //相关索引添加后，再用下面的方法，所有子表也都更新2023.2.13
            //var db = _repository.DbConnection;
            //var sql = $"SELECT Id,UniqueKey FROM P_WaybillCode WITH(NOLOCK) WHERE ExpressWayBillCode IN@ExpressWayBillCode AND ShopId=@ShopId ";
            //var list = db.Query<WaybillCode>(sql, new { ExpressWayBillCode = waybillCodeList, ShopId = curLoginShopId }).ToList();
            //var uniqueKeys = list.Where(a => !string.IsNullOrEmpty(a.UniqueKey)).Select(a => a.UniqueKey).ToList();
            //var wcOrderList = new List<WaybillCodeOrder>();
            //var updateSql = string.Empty;
            //if (uniqueKeys.Any())
            //{
            //    updateSql += $"UPDATE P_WaybillCodeOrder SET SendType=@SendType WHERE ParentUniqueKey IN('{string.Join("','", uniqueKeys)}') ;";

            //    sql = "SELECT OrderId,ParentUniqueKey FROM P_WaybillCodeOrder WITH(NOLOCK) WHERE ParentUniqueKey IN @UniqueKeys";
            //    wcOrderList = db.Query<WaybillCodeOrder>(sql, new { UniqueKeys = uniqueKeys }).ToList();
            //    var keys = wcOrderList.Where(a => !string.IsNullOrEmpty(a.UniqueKey)).Select(a => a.UniqueKey).ToList();
            //    if(keys.Any())
            //        updateSql += $"UPDATE P_WaybillCodeOrderProduct SET SendType=@SendType WHERE ParentUniqueKey IN('{string.Join("','",keys)}') ;";
            //}
            
            //list.ForEach(wc => {
            //    updateSql += $"UPDATE P_WaybillCode SET SendType=@SendType WHERE UniqueKey='{wc.UniqueKey}' ;";
            //});
            //if(!string.IsNullOrEmpty(updateSql))
            //    db.Execute(updateSql, new { SendType = sendType });
        }

        /// <summary>
        /// 更新底单“单号类型”
        /// </summary>
        /// <param name="sendType"></param>
        /// <param name="curLoginShopId"></param>
        /// <param name="waybillCodeList"></param>
        /// <param name="dcLogs"></param>
        public void UpdateWaybillCodeSendTypeByCode(int sendType, int curLoginShopId, List<string> waybillCodeList, List<DataChangeLog> dcLogs = null)
        {
            //更新单号类型
            UpdateSendTypeByCode(sendType, curLoginShopId, waybillCodeList);

            #region 数据变更日志
            if (dcLogs != null && dcLogs.Any())
                new DataChangeLogRepository().Add(dcLogs, 1);
            #endregion
            //已前置处理了同步接口服务，此处不用再处理
        }

        #region 网点单号回收
        public Tuple<bool, string> WlbWaybillICancelHTKY(UserSiteInfo siteAccount, string orderId, string waybillCode)
        {
            string name = siteAccount.UserName;
            string pwd = siteAccount.UserPassword;
            //var pid = _repository.GetPidByOrderIdAndWaybillCode(orderId, waybillCode)?.Pid;
            var result = (new HTKYLogisticService()).CancelOrder(name, pwd, waybillCode);
            return result;

            //string url = "http://edi-q9.ns.800best.com/kd/api/process";//对应的地址
            //string partnerID = "65754";//根据实际partnerID
            //string partnerKey = "ajt1fb24dhs5";//根据实际partnerKey
            //string format = "JSON";//如果是JSON的数据格式，填JSON
            //Client client = new Client(url, partnerKey, partnerID, format);//partnerID,partnerKey是我们自己应用的秘钥信息

            //KdWaybillDeliveryCancelNotifyReq kdWaybillDeliveryCancelNotify = new KdWaybillDeliveryCancelNotifyReq();
            //List<RemovePrintFeedbackList> lists = new List<RemovePrintFeedbackList>();
            //RemovePrintFeedbackList removePrintFeedbackList = new RemovePrintFeedbackList();
            //removePrintFeedbackList.mailNo = waybillCode;
            //lists.Add(removePrintFeedbackList);
            //kdWaybillDeliveryCancelNotify.removePrintFeedbackList = lists;

            //kdWaybillDeliveryCancelNotifyReq.Auth auth = new kdWaybillDeliveryCancelNotifyReq.Auth();
            //auth.username = name;
            //auth.pass = pwd;

            ////auth.username = "DZMD";
            ////auth.pass = "800best"; //用于联调的账号密码，模拟用户的账号信息

            //kdWaybillDeliveryCancelNotify.auth = auth;

            //KdWaybillDeliveryCancelNotifyRsp response = client.execute<KdWaybillDeliveryCancelNotifyRsp>(kdWaybillDeliveryCancelNotify);
            //bool isSuccess = response == null ? false : response.result;
            //string ErrCode = "";
            //string ErrMsg = "";
            //bool result = false;
            //if (isSuccess)
            //{
            //    result = isSuccess;
            //}
            //else
            //{
            //    ErrCode = response.errorCode;
            //    ErrMsg = response.errorDescription;
            //}
            //return new Tuple<bool, string>(result, ErrCode + ":" + ErrMsg);

        }

        public Tuple<bool, string> WlbWaybillICancelYUNDA(UserSiteInfo siteAccount, string orderId, string waybillCode)
        {
            string name = siteAccount.UserName;
            string pwd = siteAccount.UserPassword;


            //先找出pid
            var pid = _repository.GetPidByOrderIdAndWaybillCode(orderId, waybillCode)?.Pid;

            //string name = siteAccount.UserName;
            //string pwd = siteAccount.UserPassword;


            //string orderid = orderId;
            //string code = waybillCode;
            //string contxt = "<orders><order><order_serial_no>" + orderid + "</order_serial_no></order></orders>";

            //YUNDA.RequestVO requestVO = new YUNDA.RequestVO();
            //requestVO.xmldata = YUNDA.DataTransform.xmlformat(contxt);
            //requestVO.partnerid = name;
            //requestVO.password = pwd;
            //requestVO.version = "1.0";
            //requestVO.request = "cancel_order";
            //string postdata = YUNDA.DataTransform.signData(requestVO);
            //string retStr = YUNDA.DataTransform.xmlformat(YUNDA.HttpClient.post(YUNDAClass.GetCancelUrl(), postdata));
            //System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
            //xml.LoadXml(retStr);

            //string json = JsonConvert.SerializeXmlNode(xml);
            //Hashtable bootHS = (Hashtable)PluSoft.Utils.JSON.Decode(json);
            //Hashtable resHS2 = (Hashtable)bootHS["responses"];
            //Hashtable response = (Hashtable)resHS2["response"];
            //string status = Convert.ToString(response["status"]);
            //string ErrMsg = Convert.ToString(response["msg"]);

            //bool result = false;
            //if (status == "1")
            //    result = true;

            //return new Tuple<bool, string>(result, ErrMsg);

            var result = (new NewYunDaLogisticService()).CancelOrder(name, pwd, pid, waybillCode);
            return result;
        }

        public Tuple<bool, string> WlbWaybillICancelYTO(UserSiteInfo siteAccount, string orderId, string waybillCode)
        {
            string name = siteAccount.UserName;
            string pwd = siteAccount.UserPassword;


            //先找出pid
            var pid = _repository.GetPidByOrderIdAndWaybillCode(orderId, waybillCode)?.Pid;
            var result = (new NewYTOLogisticService()).CancelOrder(pid, name, pwd);
            return result;
            //string name = siteAccount.UserName;
            //string pwd = siteAccount.UserPassword;


            ////组织报文
            //string apiUrl = YTO.YTOClass.GetApiUrl();//上传订单的测试环境地址

            //string parternId = pwd;
            //string clientId = name;
            //string customerId = name;

            //string orderid = orderId;
            //string code = waybillCode;

            //string xmlBuilder = "<UpdateInfo>"
            //+ "    <logisticProviderID>YTO</logisticProviderID>"
            //+ "    <clientID>" + clientId + "</clientID>"
            //+ "    <mailNo>" + code + "</mailNo>"
            //+ "    <txLogisticID>" + orderid + "</txLogisticID>"
            //+ "    <infoType>INSTRUCTION</infoType>"
            //+ "    <infoContent>WITHDRAW</infoContent>"
            //+ "    <remark></remark>"
            //+ "</UpdateInfo>";

            //System.Security.Cryptography.MD5 md5Hasher = System.Security.Cryptography.MD5.Create();
            //string postData = "logistics_interface=" + System.Web.HttpUtility.UrlEncode(xmlBuilder, System.Text.Encoding.UTF8)
            //                + "&data_digest=" + System.Web.HttpUtility.UrlEncode(Convert.ToBase64String(md5Hasher.ComputeHash(System.Text.Encoding.UTF8.GetBytes(xmlBuilder + parternId))), System.Text.Encoding.UTF8)
            //                + "&clientId=" + System.Web.HttpUtility.UrlEncode(clientId, System.Text.Encoding.UTF8);

            //System.Text.Encoding encoding = System.Text.Encoding.UTF8;
            //byte[] data = encoding.GetBytes(postData);

            //System.Net.HttpWebRequest req = (System.Net.HttpWebRequest)System.Net.HttpWebRequest.Create(apiUrl);
            //req.Method = "POST";
            //req.ContentType = "application/x-www-form-urlencoded; charset=UTF-8";
            //req.ContentLength = data.Length;

            ////发送数据
            //System.IO.Stream newStream = req.GetRequestStream();
            //newStream.Write(data, 0, data.Length);
            //System.Net.HttpWebResponse res = (System.Net.HttpWebResponse)req.GetResponse();
            //newStream.Close();

            ////获取响应
            //System.Net.HttpWebResponse myResponse = (System.Net.HttpWebResponse)req.GetResponse();
            //StreamReader reader = new StreamReader(myResponse.GetResponseStream(), System.Text.Encoding.UTF8);
            //string content = reader.ReadToEnd();

            //System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
            //xml.LoadXml(content);

            //string json = JsonConvert.SerializeXmlNode(xml);
            //Hashtable bootHS = (Hashtable)PluSoft.Utils.JSON.Decode(json);
            //Hashtable resHS2 = (Hashtable)bootHS["Response"];
            //string issuccess = Convert.ToString(resHS2["success"]);
            //string ErrMsg = Convert.ToString(resHS2["reason"]);

            ////Hashtable resht = new Hashtable();
            ////resht.Add("orderid", orderid);
            ////resht.Add("wlbcode", code);
            ////resht.Add("result", issuccess);
            ////resht.Add("ErrMsg", ErrMsg);
            ////resht.Add("ErrCode", "");
            ////arrRet.Add(resht);

            //var result = false;
            //Boolean.TryParse(issuccess, out result);

            //return new Tuple<bool, string>(result, ErrMsg);
        }

        public Tuple<bool, string> WlbWaybillICancelZTO(UserSiteInfo siteAccount, string orderId, string waybillCode)
        {
            string name = siteAccount.UserName;
            string pwd = siteAccount.UserPassword;

            string orderid = orderId;
            string code = waybillCode;

            string contxt = "{\"id\":\"" + orderid + "\",\"remark\":\"取消\"}";

            string date = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            string contentBase = ZTO.PostDateFunc.Strtobase(contxt.Trim());
            string verify = ZTO.PostDateFunc.GetMd5(name + date + contentBase + pwd, "urf-8");
            System.Collections.Specialized.NameValueCollection postValues = new System.Collections.Specialized.NameValueCollection();
            postValues.Add("style", "json"); //消息签名
            postValues.Add("func", "order.cancel");
            postValues.Add("partner", name);
            postValues.Add("datetime", date);
            postValues.Add("content", contentBase);
            postValues.Add("verify", verify);
            string posturl = ZTO.ZTOClass.GetApiUrl();
            string returnMases = ZTO.PostDateFunc.PostDate2(posturl, postValues);//,500,Encoding.UTF8,null

            Hashtable bootHS = (Hashtable)PluSoft.Utils.JSON.Decode(returnMases);
            string resHS = Convert.ToString(bootHS["result"]);
            string remark = Convert.ToString(bootHS["remark"]);
            string remarkcode = Convert.ToString(bootHS["code"]);


            //Hashtable resht = new Hashtable();
            //resht.Add("orderid", orderid);
            //resht.Add("wlbcode", code);
            //resht.Add("result", resHS);
            //resht.Add("ErrMsg", remark);
            //resht.Add("ErrCode", remarkcode);


            var result = false;
            Boolean.TryParse(resHS, out result);

            return new Tuple<bool, string>(result, remarkcode + ":" + remark);
        }

        public Tuple<bool, string> WlbWaybillICancelANE(UserSiteInfo siteAccount, string orderId, string waybillCode)
        {
            string name = siteAccount.UserName;
            string pwd = siteAccount.UserPassword;


            string orderid = orderId;
            string code = waybillCode;

            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add("ewbNo", code);
            string json = JsonConvert.SerializeObject(param, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            string sign = ANE.ANEClass.md5By64Bit(json + ANE.ANEClass.GetCode() + ANE.ANEClass.GetAppSecret());
            Dictionary<string, string> dic = new Dictionary<string, string>();
            dic.Add("params", json);
            dic.Add("timestamp", ANE.ANEClass.GetTimeStampLong());
            dic.Add("digest", sign);
            dic.Add("code", ANE.ANEClass.GetCode());
            string result = ANE.ANEClass.GetResponseData(
                JsonConvert.SerializeObject(dic,
                new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }),
                ANE.ANEClass.GetCancelApiUrl());

            Hashtable bootHS = (Hashtable)PluSoft.Utils.JSON.Decode(result);
            string resHS = Convert.ToString(bootHS["result"]).ToLower();
            string remark = Convert.ToString(bootHS["resultInfo"]);
            string remarkcode = Convert.ToString(bootHS["resultCode"]);

            //Hashtable resht = new Hashtable();
            //resht.Add("orderid", orderid);
            //resht.Add("wlbcode", code);
            //resht.Add("result", resHS);
            //resht.Add("ErrMsg", remark);
            //resht.Add("ErrCode", remarkcode);
            //arrRet.Add(resht);


            var rst = false;
            Boolean.TryParse(resHS, out rst);

            return new Tuple<bool, string>(rst, remarkcode + ":" + remark);
        }

        public Tuple<bool, string> WlbWaybillICancelJT(UserSiteInfo siteAccount, string orderId, string waybillCode)
        {
            string name = siteAccount.UserName;
            string pwd = siteAccount.UserPassword;

            var pid = _repository.GetPidByOrderIdAndWaybillCode(orderId, waybillCode)?.Pid;

            var result = (new JTLogisticService()).Cancel(pid, name, pwd, waybillCode);

            return result;
        }


        public Tuple<bool, string> WlbWaybillICancelSF(UserSiteInfo siteAccount, string orderId, string waybillCode)
        {
            string name = siteAccount.UserName;
            string pwd = siteAccount.UserPassword;
            var fengQiaoApiService = new FengQiaoApiService(name, pwd);
            //先找出pid
            var pid = _repository.GetPidByOrderIdAndWaybillCode(orderId, waybillCode)?.Pid;

            var temp = fengQiaoApiService.OrderConfirmService(new OrderConfirmRequest()
            {
                orderid = FengQiaoLogisticService.GetOrderId(orderId, pid),
                dealtype = "2"
            });

            if (temp?.ERROR?.ERROR == "未下单")
            {
                temp = fengQiaoApiService.OrderConfirmService(new OrderConfirmRequest()
                {
                    orderid = $"{orderId}_{pid}",
                    dealtype = "2"
                });
            }

            if (temp?.Head?.ToLower() == "ok")
            {
                return new Tuple<bool, string>(true, "");
            }
            else
            {
                //如果是已取消，则不报错
                if (temp?.ERROR?.code == "8037" || temp?.ERROR?.ERROR == "已消单")
                    return new Tuple<bool, string>(true, string.Empty);

                return new Tuple<bool, string>(false, temp?.ERROR?.ERROR);
            }
        }

        public Tuple<bool, string> WlbWaybillICancelDBKD(string orderId, string waybillCode)
        {
            var _service = new DBKDLogisticService();
            //先找出pid
            var pid = _repository.GetPidByOrderIdAndWaybillCode(orderId, waybillCode)?.Pid;
            return _service.CancelOrder(pid, waybillCode);
        }

        public Tuple<bool, string> WlbWaybillICancelSTO(string orderId, string waybillCode)
        {
            var _service = new NewSTOLogisticService();
            //先找出pid
            var pid = _repository.GetPidByOrderIdAndWaybillCode(orderId, waybillCode)?.Pid;
            return _service.CancelOrder(pid, waybillCode);
        }
        #endregion

        public List<int> GetShopIdByTemplateId(List<int> templateIds, string currentCloudPlatformType, string sDate, string eDate)
        {
            var shopIds = new ConcurrentBag<int>();

            //var sql = $"SELECT ShopId FROM dbo.P_WaybillCode WITH(NOLOCK) WHERE  GetDate>='2020-02-01' AND Templateid IN @tids AND status<>2";
            var sql = $"SELECT DISTINCT ShopId FROM dbo.P_WaybillCode WITH(NOLOCK) WHERE  GetDate>='2020-02-01' AND Templateid IN @templateIds ";

            //var apiSqlParams = new List<ApiSqlParamModel>()
            //{
            //    new ApiSqlParamModel(templateIds,"templateIds")
            //};
            var p = new
            {
                templateIds = templateIds
            };

            //var dbList = new List<PlatformType>() {
            //           PlatformType.Alibaba, //"1688",
            //            PlatformType.Pinduoduo,//"pdd",
            //            PlatformType.Taobao,//"tb",
            //            PlatformType.Jingdong,//"tb",
            //            PlatformType.YouZan,//"other"
            //            PlatformType.Offline,//"Offline"
            //    };

            //Parallel.ForEach(dbList, new ParallelOptions { MaxDegreeOfParallelism = 1 }, pt =>
            //{
            //    try
            //    {
            //        var dbAccessUtility = new DbAccessUtility(new ApiDbConfigModel(currentCloudPlatformType), new ApiDbConfigModel(pt.ToString()));
            //        var ids = dbAccessUtility.Query<int>(sql, new { templateIds });
            //        ids?.ForEach(id =>
            //        {
            //            shopIds.Add(id);
            //        });
            //    }
            //    catch (Exception ex)
            //    {
            //        Utility.Log.WriteError($"当前云平台【{currentCloudPlatformType}】跨平台获取【{pt}】底单数据异常：{ex.Message}\n SQL语句===>{sql}");
            //    }
            //});

            //查询分库分表的数据库
            Data.Extension.DbPolicyExtension.ParellelForeachAllDbs(db =>
            {
                var ids = db.Query<int>(sql, new { templateIds });
                ids?.ForEach(id =>
                {
                    shopIds.Add(id);
                });
            });
            return shopIds.Distinct().ToList();
        }

        public int GetCountByTemplateId(List<int> usedTemplateIds, List<Shop> usedShopInfos, string sDate, string eDate)
        {
            // int result = 0; //GetDate>='{sDate}' AND GetDate < '{eDate}'

            //var sql = $"SELECT COUNT(*) FROM dbo.P_WaybillCode  WITH(NOLOCK) WHERE shopid IN @sids AND GetDate>='2020-02-01' AND Templateid IN @tids AND status<>2";
            var sql = $"SELECT COUNT(*) FROM dbo.P_WaybillCode  WITH(NOLOCK) WHERE  GetDate>='2020-02-01' AND Templateid IN @tids AND status<>2";

            //参照平台，当前默认为拼多多
            var currentPlatformType = PlatformType.Pinduoduo.ToString();
            //TODO:分库时应按照DbConfig来分组
            ////初始化数据库配置
            //DianGuanJiaApp.Data.Extension.DbPolicyExtension.InitShopDbConfigs(usedShopInfos);
            //var groups = usedShopInfos.GroupBy(f => f.PlatformType).ToList();
            //groups.ForEach(g =>
            //{
            //    var pt = g.Key;
            //    var sids = g.Select(f => f.Id).ToList();
            //    var count = 0;
            //    try
            //    {
            //        var dbAccessUtility = new DbAccessUtility(new ApiDbConfigModel(currentPlatformType), new ApiDbConfigModel(pt));
            //        count = dbAccessUtility.ExecuteScalar(sql, new { sids = sids, tids = usedTemplateIds }).ToInt();
            //    }
            //    catch (Exception ex)
            //    {
            //        Utility.Log.WriteError($"当前平台【{PlatformType.Pinduoduo.ToString()}】跨平台获取【{pt}】底单数据异常：{ex.Message}\n SQL语句===>{sql}");
            //    }

            //    result += count;
            //});


            //分库分表的数据库
            var countBags = new ConcurrentBag<int>();
            Data.Extension.DbPolicyExtension.ParellelForeachAllDbs(db =>
            {
                var count = db.ExecuteScalar(sql, new { tids = usedTemplateIds }).ToInt();
                countBags.Add(count);
            });
            return countBags.Sum();
        }

        /// <summary>
        /// 从所有平台DB中查询数据
        /// </summary>
        /// <param name="waybillCodes"></param>
        /// <param name="currentCloudPlatformType"></param>
        /// <returns></returns>
        public List<WaybillCode> GetWaybillCodeInfoInEveryDb(List<string> waybillCodes, string currentCloudPlatformType)
        {
            if (waybillCodes == null || waybillCodes.Any() == false)
                return new List<WaybillCode>();
            waybillCodes = waybillCodes.Where(w => !string.IsNullOrEmpty(w)).Distinct().OrderBy(f => f).ToList();

            int pageSize = 2000;
            var list = new ConcurrentDictionary<string, WaybillCode>();
            var sql = $"SELECT OrderId,ExpressWayBillCode,Status,ShopId,Reciver,TemplateId,GetDate,UniqueKey FROM dbo.P_WaybillCode WITH(NOLOCK) WHERE ExpressWaybillCode IN @waybillCodes";


            var pageCount = (int)Math.Ceiling(waybillCodes.Count / (float)pageSize);

            for (int i = 0; i < pageCount; i++)
            {
                var tempList = waybillCodes.Skip(i * pageSize).Take(pageSize).ToList();

                if (tempList != null && tempList.Any() == false)
                    break;
                Data.Extension.DbPolicyExtension.ParellelForeachAllDbs(db =>
                {
                    var result = db.Query<WaybillCode>(sql, new { waybillCodes = tempList });
                    result?.ForEach(r =>
                    {
                        list.TryAdd(r.ExpressWayBillCode, r);
                    });
                });
            }

            return list.Values.ToList();
        }


        /// <summary>
        /// 从所有平台DB中查询数据
        /// </summary>
        /// <param name="waybillCodes"></param>
        /// <param name="currentCloudPlatformType"></param>
        /// <returns></returns>
        public List<WaybillCode> GetWaybillCodeInfoInEveryDb(List<string> waybillCodes, int shopId)
        {
            if (waybillCodes == null || waybillCodes.Any() == false)
                return new List<WaybillCode>();
            waybillCodes = waybillCodes.Where(w => !string.IsNullOrEmpty(w)).Distinct().OrderBy(f => f).ToList();

            int pageSize = 2000;
            //var sql = $"SELECT ExpressWayBillCode,OrderId,Status,ShopId,Reciver,TemplateId,GetDate,UniqueKey FROM dbo.P_WaybillCode WITH(NOLOCK) WHERE ExpressWaybillCode IN @waybillCodes";

            //var isFromShop = false;
            //var supplierShopIds = new List<int>() { shopId }; // 用于查商家库
            var shareRelations = _branchShareRelationService.GetBranchShareIdByShopIds(new List<int>() { shopId });
            var shopIds = new List<int>() { shopId };
            List<int> sharedIds = null;
            if (shareRelations != null && shareRelations.Any())
            {
                // 当前账号所有相关分享店铺信息
                var toIds = shareRelations.Select(x => x.ToId).ToList();
                var fromIds = shareRelations.Select(x => x.ToId).ToList();
                shopIds.AddRange(toIds);
                shopIds.AddRange(fromIds);

                sharedIds = shareRelations.Select(x => x.Id).Distinct().ToList();
                var tmpShopIds = _branchShareRelationService.GetBranchShareUsedShopIds(sharedIds);
                shopIds.AddRange(tmpShopIds);
                shopIds = shopIds.Distinct().ToList();
                //isFromShop = shareRelations.Any(x => x.FromId == shopId);
            }
            // 找出所有当前账号的分享主和被分享店铺（只有分享关系，没有商家和厂家合作关系）

            // 分单系统还需找出商家ShopId：1、分享主需要查出被分享账号的所有商家Id；2、被分享账号只需要查出商家Id即可（接收者那里可以查到，分享主那里查不到）
            var fxUserId = SiteContext.Current.CurrentFxUser.Id;
            //if (isFromShop)
            //{
            //    var toIds = shareRelations.Where(x => x.FromId == shopId).Select(x => x.ToId).Distinct().ToList();
            //    supplierShopIds.AddRange(toIds);
            //}

            //Log.Debug($"所有厂家店铺Id：{supplierShopIds.ToJson()}");
            //var agentFxShops = new SupplierUserService().GetAgentFxUserShopList(supplierShopIds);
            //Log.Debug($"所有商家信息：{agentFxShops.ToJson()}");
            //if (agentFxShops.Any())
            //    shopIds.AddRange(agentFxShops.Select(x => x.ShopId));
            //分单系统增加路径流关联
            var pathFlowWhere = string.Empty;
            var toFxDbNameIds = new List<int>();
            if (sharedIds != null && sharedIds.Any())
            {
                //通过分享关系Id拿到ToId对应的FxUserId
                var toFxUserModels = new FxUserShopService().GetFxUserIdByBranchShareRelationId(sharedIds);
                toFxDbNameIds = toFxUserModels.Select(x => x.DbNameConfigId).Distinct().ToList();
                if (toFxUserModels != null && toFxUserModels.Any())
                    pathFlowWhere = $" INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON t1.PathFlowCode=pfn.PathFlowCode AND pfn.FxUserId IN({string.Join(",", toFxUserModels.Select(x => x.FxUserId).Distinct())}) ";
            }

            var sql = $"SELECT t1.ExpressWayBillCode,t1.OrderId,t1.Status,t1.ShopId,t1.Reciver,t1.TemplateId,t1.GetDate FROM dbo.P_WaybillCode t1 WITH(NOLOCK) WHERE ExpressWaybillCode IN @waybillCodes";
            var sqlForFxSystem = $"SELECT t1.ExpressWayBillCode,t1.OrderId,t1.Status,t1.ShopId,t1.Reciver,t1.TemplateId,t1.GetDate,t1.UniqueKey FROM dbo.P_WaybillCode t1 WITH(NOLOCK) {pathFlowWhere} WHERE ExpressWaybillCode IN @waybillCodes";

            var pageCount = (int)Math.Ceiling(waybillCodes.Count / (float)pageSize);
            var list = new List<WaybillCode>();
            for (int i = 0; i < pageCount; i++)
            {
                var tempResult = new ConcurrentDictionary<string, WaybillCode>();
                var tempList = waybillCodes.Skip(i * pageSize).Take(pageSize).ToList();

                if (tempList != null && tempList.Any() == false)
                    break;
                Data.Extension.DbPolicyExtension.ParellelForeachAllDbs(db =>
                {
                    // 打单系统没有UniqueKey，需要移除
                    var appName = db.TargetDbConfig.ApplicationName.ToString2().ToLower();
                    //if (appName != "fx" && appName != "fx_cloud" && appName != "fx_new")
                    //    sql = sql.Replace(",t1.UniqueKey", "");

                    var curSql = sql;
                    //分单库追加路流关联
                    if (appName.StartsWith("fx") == true)
                        curSql = sqlForFxSystem;

                    Log.Debug($"appName:{appName},库【{db.ToJson()}】\n查询SQL=>{curSql}，参数：{tempList.ToJson()}");
                    var result = db.Query<WaybillCode>(curSql, new { waybillCodes = tempList });
                    Log.Debug($"appName:{appName},库【{db.ToJson()}】\n查询SQL=>{curSql}，参数：{tempList.ToJson()}\n查询结果：{result.ToJson()}");

                    result?.ForEach(r =>
                    {
                        tempResult.TryAdd(r.ExpressWayBillCode, r);
                    });
                }, () => { return tempResult.Count >= tempList.Count; }, shopIds, fxUserId: fxUserId, cpt: "all", appendFxDbNameIds: toFxDbNameIds);

                list.AddRange(tempResult.Values);
            }

            return list;
        }

        /// <summary>
        /// 获取打印模板Id
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public List<WaybillCode> GetTemplateIdByWaybillCodeId(List<int> ids)
        {
            if (ids?.Any() == true)
                return _repository.GetTemplateIdByWaybillCodeId(ids);
            else
                return new List<WaybillCode>();
        }

        public List<WaybillCode> GetByWaybillCodeIds(List<int> ids, List<string> fields = null) 
        {
            if (ids?.Any() == true)
                return _repository.GetByWaybillCodeIds(ids);
            else
                return new List<WaybillCode>();
        }

        /// <summary>
        /// 根据系统订单编号查询（结果带子表数据）
        /// </summary>
        /// <param name="logicOrderIds"></param>
        /// <param name="fxUserId"></param>
        /// <param name="fields"></param>
        /// <returns></returns>
        public List<WaybillCode> GetByLogicOrderIds(List<string> logicOrderIds, int fxUserId, List<string> fields = null)
        {
            if (logicOrderIds?.Any() == true)
                return _repository.GetByLogicOrderIds(logicOrderIds, fxUserId, fields);
            else
                return new List<WaybillCode>();
        }

        /// <summary>
        /// 根据底单编号 获取订单信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<WaybillCode> GetWaybillCodesByIds(List<int> ids)
        {
            return ids?.Any() == true ? _repository.GetWaybillCodesByIds(ids) : new List<WaybillCode>();
        }

        public List<WaybillCode> GetWaybillCodeList(List<PrintHistory> printHistorys)
        {
            var result = _repository.GetWaybillCodeList(printHistorys);
            return result;
        }

        public int GetWaybillCodeStatusById(int WaybillCodeId)
        {
            var result = _repository.GetWaybillCodeStatusById(WaybillCodeId);
            return result;
        }

        public bool ExistNotRecycleWaybillCodeByOrderId(string orderId)
        {
            var result = _repository.ExistNotRecycleWaybillCodeByOrderId(orderId);
            return result;
        }

        public void AddResend(List<WaybillCode> waybillcodes)
        {
            _repository.BulkWrite(waybillcodes, "P_WaybillCode");
        }

        /// <summary>
        /// 更新电子面单的状态-异常补偿，SendDate=记录异常的时间
        /// status ： 1：已打印，2：单号已回收，3：已发货
        /// </summary>
        /// <param name="status"></param>
        /// <param name="expressId"></param>
        /// <param name="waybillCodeList"></param>
        /// <param name="dateTime">指定发货时间</param>
        /// <returns></returns>
        public void UpdateWaybillCodeStatusByCodeForAsync(int status, int expressId, List<KeyValuePair<int, string>> waybillCodeList, DateTime dateTime)
        {
            _repository.UpdateWaybillCodeStatusByCodeForAsync(status, expressId, waybillCodeList, dateTime);
        }

        public List<WaybillCode> GetWaybillCodes(string startDate, string endDate, int pageIndex, int pageSize, int fxUserId = 0)
        {
            return _repository.GetWaybillCodes(startDate, endDate, pageIndex, pageSize, fxUserId);
        }

        public List<WaybillCodeOrderProduct> GetWybOrderProductByMasterIds(List<int> masterIds)
        {
            return new WaybillCodeOrderProductRepository().GetWybOrderProductByMasterIds(masterIds);
        }

        public List<WaybillCodeOrderProduct> GetWybOrderProductByParentUniqueKeys(List<string> keys)
        {
            return new WaybillCodeOrderProductRepository().GetWybOrderProductByParentUniqueKeys(keys);
        }

        /// <summary>
        /// 更新底单收件人信息
        /// </summary>
        /// <param name="list"></param>
        public void UpdateMaskReceiver(List<WaybillCode> list)
        {
            _repository.UpdateMaskReceiver(list);
        }


        public List<WaybillCode> GetLostSendHistoryWaybillCodes(int pageindex, int pagesize, string startTime, string endTime, int fxUserId = 0)
        {
            return _repository.GetLostSendHistoryWaybillCodes(pageindex, pagesize, startTime, endTime, fxUserId);
        }


        public int GetLostSendHistoryWaybillCodesTotal(string startTime, string endTime, int fxUserId = 0)
        {
            return _repository.GetLostSendHistoryWaybillCodesTotal(startTime, endTime, fxUserId);
        }

        /// <summary>
        /// 获取底单信息列表，为迁移数据（带子表数据）
        /// </summary>
        /// <param name="condition">必要条件：PathFlowCode</param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<WaybillCode> GetListForDuplication(DuplicationConditionModel condition,
            int pageSize, out int curCount)
        {
            //Log.WriteLine($"查询底单记录：{condition.ToJson()}");
            if (string.IsNullOrEmpty(condition.PathFlowCode))
                throw new Exception("WaybillCode查询条件中路径流不能为空");
            curCount = 0;
            var list = _repository.GetListForDuplication(condition, pageSize);
            curCount = list.Count();
            if (curCount == 0)
                return list;

            condition.MaxId = list.Max(a => a.ID);

            list = GetSubTableData(list);
            return list;
        }

        /// <summary>
        /// 获取信息列表，为副本补偿数据（带子表数据）
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<WaybillCode> GetListForDuplicationCompensate(DuplicationConditionModel condition,
            int pageSize)
        {
            var mainModels = _repository.GetListForDuplicationCompensate(condition, pageSize);
            mainModels = GetSubTableData(mainModels);
            return mainModels;
        }

        /// <summary>
        /// 获取信息为复制副本，按UniqueKey（带子表数据）
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFieldNames"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<WaybillCode> GetListForDuplication(List<string> codes, string selectFieldNames = "*",
            string whereFieldName = "UniqueKey")
        {
            if (codes == null || !codes.Any())
                return new List<WaybillCode>();
            var mainModels = _repository.GetListForDuplication(codes, selectFieldNames, whereFieldName);
            mainModels = GetSubTableData(mainModels);
            return mainModels;
        }

        /// <summary>
        /// 获取子表数据
        /// </summary>
        /// <param name="mainModels"></param>
        /// <returns></returns>
        public List<WaybillCode> GetSubTableData(List<WaybillCode> mainModels)
        {
            //代码
            var uniqueKeys = mainModels.Select(m => m.UniqueKey).Distinct().ToList();
            //与主业务相关信息
            var childs = _waybillCodeChildRepo.GetListForDuplication(uniqueKeys);
            var orders = _waybillCodeOrderRepo.GetListForDuplication(uniqueKeys);
            var orderUniqueKeys = orders?.Select(m => m.UniqueKey).Distinct().ToList();
            var products = _waybillCodeOrderProductRepo.GetListForDuplication(orderUniqueKeys);
            var useRecords = _waybillCodeUseRecordRepo.GetListForDuplication(uniqueKeys);

            childs?.ForEach(c => { c.Id = 0; c.WaybillCodeId = 0; });
            products?.ForEach(p => { p.Id = 0; p.MasterId = 0; });

            mainModels.ForEach(model => {

                model.OldID = model.ID;
                model.ID = 0;

                var curOrders = orders?.Where(a => a.ParentUniqueKey == model.UniqueKey).ToList();
                curOrders?.ForEach(o => {
                    o.ID = 0;
                    o.WaybillCodeId = 0;
                    o.WaybillCodeOrderProducts = products?.Where(a => a.ParentUniqueKey == o.UniqueKey).ToList();
                });

                model.WaybillCodeOrders = curOrders;
                model.ChildWaybillCodes = childs?.Where(a => a.ParentUniqueKey == model.UniqueKey).ToList();
                model.UseRecords = useRecords?.Where(a => a.ParentUniqueKey == model.UniqueKey).ToList();
            });

            return mainModels;
        }

        /// <summary>
        /// 批量插入数据为复制副本（带子表数据）
        /// </summary>
        /// <param name="models"></param>
        public void InsertsForDuplication(List<WaybillCode> models)
        {
            if (models == null || !models.Any())
                return;

            var batchSize = 300;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();
                InsertsForDuplicationV2(batchModels);
            }

            return;

            //清理源库ID
            models.ForEach(m => { m.ID = 0; });
            //代码
            var codes = models.Select(m => m.UniqueKey).ToList();
            //存在的代码列表
            var idAndCodes = _repository.GetExistIdAndCodes(codes);
            //全部不存在
            if (idAndCodes == null || !idAndCodes.Any())
            {
                //_repository.BulkInsert(models);
                baseRepository.BulkWrite(models, "P_WaybillCode");
                return;
            }
            //存在
            var existsCodes = idAndCodes.Select(m => m.Code).ToList();
            var updates = models.Where(m => existsCodes.Contains(m.UniqueKey)).ToList();
            if (updates.Any())
            {
                updates.ForEach(o =>
                {
                    var model = idAndCodes.FirstOrDefault(m => m.Code == o.UniqueKey);
                    if (model == null)
                    {
                        return;
                    }
                    o.ID = model.Id;
                });
                _repository.BulkUpdate(updates);
            }
            //不存在
            var inserts = models.Where(m => !existsCodes.Contains(m.UniqueKey)).ToList();
            if (inserts.Any())
            {
                //_repository.BulkInsert(inserts);
                baseRepository.BulkWrite(inserts, "P_WaybillCode");
            }
        }
        /// <summary>
        /// 批量插入数据为复制副本（带子表数据）
        /// </summary>
        /// <param name="models"></param>
        public void InsertsForDuplicationV2(List<WaybillCode> models)
        {
            //使用UniqueKey判断：存在忽略，不存在插入

            //清理源库ID
            models.ForEach(m => { m.ID = 0; });

            var needOrders = new List<WaybillCodeOrder>();
            var needProducts = new List<WaybillCodeOrderProduct>();
            var needChilds = new List<WaybillCodeChild>();
            var needUseRecords = new List<WaybillCodeUseRecord>();

            var uniqueKeys = models.Select(e => e.UniqueKey).ToList();
            var orderUniqueKeys = models.SelectMany(a => a.WaybillCodeOrders).Select(e => e.UniqueKey).ToList();

            var existList = _repository.GetExistIdAndCodes(uniqueKeys);
            //var existOrderlist = _waybillCodeOrderRepo.GetExistIdAndCodes(orderUniqueKeys);
            //var existProductlist = _waybillCodeOrderProductRepo.GetExistIdAndCodes(orderUniqueKeys);
            var existOrderlist = new List<IdAndCodeModel>();
            var existProductlist = new List<IdAndCodeModel>();
            var existChildlist = _waybillCodeChildRepo.GetExistIdAndCodes(uniqueKeys);
            var existUseRecordlist = _waybillCodeUseRecordRepo.GetExistIdAndCodes(uniqueKeys);

            var batchSize = 500;
            var count = Math.Ceiling(orderUniqueKeys.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchKeys = orderUniqueKeys.Skip(i * batchSize).Take(batchSize).ToList();
                var curExistOrderlist = _waybillCodeOrderRepo.GetExistIdAndCodes(batchKeys);
                var curExistProductlist = _waybillCodeOrderProductRepo.GetExistIdAndCodes(batchKeys);

                existOrderlist.AddRange(curExistOrderlist);
                existProductlist.AddRange(curExistProductlist);
            }


            //检查需要添加的记录
            var needMains = new List<WaybillCode>();
            var needUpdateMains = new List<WaybillCode>();
            models.ForEach(model =>
            {
                var exist = existList.FirstOrDefault(e => e.Code == model.UniqueKey);
                if (exist == null)
                    needMains.Add(model);
                else
                {
                    //需要更新的
                    if ((model.Status.HasValue && model.Status.Value != 0) || model.SendType > 3)
                        needUpdateMains.Add(model);
                }

                model.WaybillCodeOrders?.ForEach(order =>
                {
                    if (existOrderlist.Any(f => f.Code == order.UniqueKey) == false)
                        needOrders.Add(order);

                    order.WaybillCodeOrderProducts?.ForEach(product =>
                    {
                        if (existProductlist.Any(f => f.Code == product.ParentUniqueKey && f.ItemCode == product.OrderItemCode) == false)
                            needProducts.Add(product);
                    });
                });

                model.ChildWaybillCodes?.ForEach(child =>
                {
                    if (existChildlist.Any(f => f.Code == child.ParentUniqueKey && f.ItemCode == child.ChildWaybillCode) == false)
                        needChilds.Add(child);
                });

                model.UseRecords?.ForEach(ur =>
                {
                    if (existUseRecordlist.Any(f => f.Code == ur.ParentUniqueKey && f.ItemCode == ur.WaybillCode) == false)
                        needUseRecords.Add(ur);
                });
            });

            //小于等于此值使用单条插入
            var SINGLE_COUNT = 200;
            var db = _repository.DbConnection;

            #region 带保存Id

            #region 写入P_WaybillCode表
            if (needMains.Count <= SINGLE_COUNT)
            {
                //使用单条
                if (db.State == ConnectionState.Closed)
                    db.Open();
                using (db)
                {
                    needMains.ForEach(item =>
                    {
                        db.Insert(item);
                    });
                }
            }
            else
            {
                //使用批量
                _repository.BulkWrite(needMains, "P_WaybillCode");
            }
            #endregion

            #region 更新P_WaybillCode表：Status和SendType字段
            if (needUpdateMains.Any())
            {
                db = _repository.DbConnection;
                //使用单条
                if (db.State == ConnectionState.Closed)
                    db.Open();
                using (db)
                {
                    needUpdateMains.ForEach(item =>
                    {
                        var sql = $"UPDATE P_WaybillCode SET Status={item.Status},SendType={item.SendType} WHERE UniqueKey='{item.UniqueKey}' ; ";
                        db.Execute(sql);
                    });
                }
            }
            #endregion


            #region 写入P_WaybillCodeOrder表，P_WaybillCodeChild表，P_WaybillCodeUseRecord表
            //查询并为WaybillCodeId赋值
            var insertedList = _repository.GetListForDuplication(uniqueKeys, "Id,UniqueKey");
            models.ForEach(model =>
            {
                var exist = insertedList.FirstOrDefault(a => a.UniqueKey == model.UniqueKey);
                if (exist != null)
                {
                    model.WaybillCodeOrders?.ForEach(order =>
                    {
                        order.WaybillCodeId = exist.ID;
                    });

                    model.ChildWaybillCodes?.ForEach(child =>
                    {
                        child.WaybillCodeId = exist.ID;
                    });

                    model.UseRecords?.ForEach(child =>
                    {
                        child.WaybillCodeId = exist.ID;
                    });
                }
            });


            db = _repository.DbConnection;
            if (db.State == ConnectionState.Closed)
                db.Open();

            using (db)
            {
                if (needOrders.Count <= SINGLE_COUNT)
                {
                    //使用单条
                    needOrders.ForEach(item =>
                    {
                        db.Insert(item);
                    });
                }
                else
                {
                    _repository.BulkWrite(needOrders, "P_WaybillCodeOrder");
                }

                if (needChilds.Count <= SINGLE_COUNT)
                {
                    //使用单条
                    needChilds.ForEach(item =>
                    {
                        db.Insert(item);
                    });
                }
                else
                {
                    //使用批量
                    _repository.BulkWrite(needChilds, "P_WaybillCodeChild");
                }

                if (needUseRecords.Count <= SINGLE_COUNT)
                {
                    //使用单条
                    needUseRecords.ForEach(item =>
                    {
                        db.Insert(item);
                    });
                }
                else
                {
                    //使用批量
                    _repository.BulkWrite(needUseRecords, "P_WaybillCodeUseRecord");
                }
            }
            #endregion

            #region 写入P_WaybillCodeOrderProduct表
            //查询并为MasterId赋值
            //var insertedOrderlist = _waybillCodeOrderRepo.GetListForDuplication(orderUniqueKeys, "Id,ParentUniqueKey,OrderId", "UniqueKey");
            var insertedOrderlist = new List<WaybillCodeOrder>();
            for (var i = 0; i < count; i++)
            {
                var batchKeys = orderUniqueKeys.Skip(i * batchSize).Take(batchSize).ToList();
                var curInsertedOrderlist = _waybillCodeOrderRepo.GetListForDuplication(batchKeys, "Id,ParentUniqueKey,OrderId", "UniqueKey");

                insertedOrderlist.AddRange(curInsertedOrderlist);
            }
            needProducts?.ForEach(item =>
            {
                var exist = insertedOrderlist.FirstOrDefault(a => a.UniqueKey == item.ParentUniqueKey);
                if (exist != null)
                {
                    item.MasterId = exist.ID;
                }
            });

            if (needProducts.Count <= SINGLE_COUNT)
            {
                db = _repository.DbConnection;
                //使用单条
                if (db.State == ConnectionState.Closed)
                    db.Open();
                using (db)
                {
                    needProducts.ForEach(item =>
                    {
                        db.Insert(item);
                    });
                }
            }
            else
            {
                //使用批量
                _repository.BulkWrite(needProducts, "P_WaybillCodeOrderProduct");
            }
            #endregion

            #endregion
        }


        public void RepairSendHistoryWithWaybillCode(string startTime, string endTime, int splitMin, List<int> userIds, Action<string> action)
        {
            List<DbConfigModel> dbConfigModels = null;

            //阿里云和拼多多云的店铺区分
            //先按平台分组，再按数据库分组
            var conn = CustomerConfig.ConfigureDbConnectionString;
            var cofigDb = DbUtility.GetConnection(conn);
            //var cofigDb = DbApiAccessUtility.GetConfigureDb();
            //if (CustomerConfig.CloudPlatformType == PlatformType.Pinduoduo.ToString())
            //    cofigDb = DbApiAccessUtility.GetPddConfigureDb();
            //else if (CustomerConfig.CloudPlatformType == PlatformType.TouTiao.ToString())
            //    cofigDb = DbApiAccessUtility.GetTouTiaoConfigureDb();
            var systemSids = new List<int>();
            if (userIds.Any())
            {
                systemSids = cofigDb.Query<int>($@"SELECT s.Id FROM dbo.P_FxUserShop f WITH(NOLOCK)
INNER JOIN dbo.P_Shop s WITH(NOLOCK) ON f.ShopId=s.Id
WHERE f.FxUserId IN({string.Join(",", userIds)}) AND f.PlatformType='System'").ToList();
                dbConfigModels = new DbConfigRepository().GetDbConfigModelFx(systemSids)?.ToList();
            }
            var filter = string.Empty;
            DynamicParameters filterParamters = null;

            if (dbConfigModels != null && dbConfigModels.Any())
            {
                filter = $" AND dn.DbName IN @dbName";
                filterParamters = new DynamicParameters();
                filterParamters.Add("@dbName", dbConfigModels.Select(x => x.DbNameConfig.DbName));
            }
            else
            {
                var cloundType = CustomerConfig.CloudPlatformType.ToLower();
                if (cloundType == "alibaba")
                    filter = $" AND dn.RunningStatus='Running' AND dn.DbPlatformType NOT IN ('pinduoduo','kuaituantuan','jingdong','TouTiao') AND dn.DbName Like 'alibaba_fendan_db%'";
                else if (cloundType == "pinduoduo")
                    filter = $" AND dn.RunningStatus='Running' AND dn.DbPlatformType IN ('pinduoduo','kuaituantuan') AND dn.DbName = 'pdd_fendan_db'";
                else if (cloundType == "jingdong")
                    filter = $" AND dn.RunningStatus='Running' AND dn.DbPlatformType IN ('jingdong') AND dn.DbName = 'jd_fendan_db'";
                else if (cloundType == "toutiao")
                    filter = $" AND dn.RunningStatus='Running' AND dn.DbPlatformType IN ('toutiao') AND dn.DbName Like 'doudian_fendan%'";
                else
                {
                    action($"未设置云平台类型");
                    return;
                }
            }

            if (CustomerConfig.IsLocalDbDebug && conn.Contains("192.168.1.168"))
                filter = $" AND dn.RunningStatus='Running' AND dn.DbPlatformType NOT IN ('pinduoduo','kuaituantuan','jingdong') AND dn.DbName Like 'AlibabaFenFaDB'";

            Log.WriteLine($"filter=>{filter}");

            Stopwatch sw = new Stopwatch();
            sw.Start();
            //按时间分片(一个小时一片)
            var startDate = DateTime.Parse(startTime);
            var endDate = DateTime.Parse(endTime);
            var pageCount = Math.Ceiling((endDate - startDate).TotalMinutes / splitMin * 1D);
            //分片查询
            var tmpStartTime = startDate;
            var tmpEndTime = tmpStartTime.AddMinutes(splitMin);
            for (int i = 0; i < pageCount; i++)
            {
                if (tmpEndTime > endDate)
                    tmpEndTime = endDate;
                if (tmpStartTime >= tmpEndTime)
                    break;

                action($"开始修复分片{tmpStartTime.ToString("yyyy/MM/dd HH:mm:ss")} - {tmpEndTime.ToString("yyyy/MM/dd HH:mm:ss")} 的发货记录数据...");

                DbPolicyExtension.ParellelForeachAllDbs(db =>
                {
                    var _connectionstring = string.Empty;
                    var _targetDbConfig = db.TargetDbConfig;
                    //if (dbConfigModel != null && dbConfigModel.DbNameConfig.Id != _targetDbConfig.DbNameConfigId)
                    //    return;

                    var dbid = db?.TargetDbConfig?.DbNameConfig?.DbName ?? (db?.TargetDbConfig?.DbNameConfigId.ToString());
                    Stopwatch sw0 = new Stopwatch();
                    sw0.Start();
                    var errMsg = string.Empty;
                    try
                    {
                        var dbConfig = Data.Extension.DbPolicyExtension.GetDbConfigModelByDbNameId(_targetDbConfig.DbNameConfigId);
                        if (dbConfig == null || string.IsNullOrEmpty(dbConfig.ConnectionString))
                            throw new ArgumentException($"目标数据库配置不正确，ID:{_targetDbConfig.DbNameConfigId}", "targetDbConfig");
                        if (CustomerConfig.IsLocalDbDebug && dbConfig?.DbServer?.LocalConnectionString.IsNullOrEmpty() == true && dbConfig.ConnectionString.Contains("192.168.1.168") == false)
                            _connectionstring = CustomerConfigExt.GetConnectString(dbConfig);
                        else
                            _connectionstring = dbConfig.ConnectionString;
                        //var wdb = new WaybillCodeRepository(_connectionstring).DbConnection;

                        var sendHistorys = new List<SendHistory>();
                        var needInsertWcos = new List<WaybillCodeOrder>();
                        var needInsertWops = new List<WaybillCodeOrderProduct>();

                        var repository = new WaybillCodeRepository(_connectionstring);
                        if (userIds.Any())
                        {
                            foreach (var uid in userIds)
                            {
                                var tuple = repository.ExecuteRepairSendHistoryWithWaybillCode(db, tmpStartTime.Format(), tmpEndTime.Format(), uid);
                                SaveRepairSendHistoryDatas(action, cofigDb, _connectionstring, tuple);
                            }
                        }
                        else
                        {
                            var tuple = repository.ExecuteRepairSendHistoryWithWaybillCode(db, tmpStartTime.Format(), tmpEndTime.Format(), 0);
                            SaveRepairSendHistoryDatas(action, cofigDb, _connectionstring, tuple);
                        }



                    }
                    catch (Exception ex)
                    {
                        errMsg = $"库【{dbid}】，修复分片{tmpStartTime.ToString("yyyy/MM/dd HH:mm:ss")}-{tmpEndTime.ToString("yyyy/MM/dd HH:mm:ss")} 从已发货底单修复发货记录失败：{ex}";
                        Utility.Log.WriteError(errMsg);
                        action(errMsg);
                        throw ex;
                    }
                    finally
                    {
                        sw0.Stop();
                        action($"库【{dbid}】，修复分片{tmpStartTime.ToString("yyyy/MM/dd HH:mm:ss")}-{tmpEndTime.ToString("yyyy/MM/dd HH:mm:ss")} 从已发货底单修复发货记录{(errMsg.IsNullOrEmpty() ? "成功" : "失败")}，耗时：{sw0.Elapsed.TotalSeconds.ToString("f2")}s...");
                    }

                }, filter,filterParamters: filterParamters, cpt: null);

                tmpStartTime = tmpEndTime;
                tmpEndTime = tmpStartTime.AddMinutes(splitMin);

            }

            sw.Stop();
            action($"从已发货底单修复发货记录完成，总耗时：{sw.Elapsed.TotalSeconds.ToString("f2")}s...");
        }

        private static void SaveRepairSendHistoryDatas(Action<string> action, IDbConnection cofigDb, string _connectionstring, Tuple<List<SendHistory>, List<WaybillCodeOrder>, List<WaybillCodeOrderProduct>> tuple)
        {
            var sendHistorys = tuple.Item1;
            var needInsertWcos = tuple.Item2;
            var needInsertWops = tuple.Item3;
            if (sendHistorys.Any() == false)
                return;

            var fxUserIds = sendHistorys.Select(x => x.FxUserId.Value).Distinct().ToList();
            var fxSystemShops = new FxUserShopRepository().GetSystemShops(fxUserIds);
            var userFxSql = $@"SELECT * FROM dbo.P_UserFx f WITH(NOLOCK) WHERE f.Id IN({string.Join(",", fxUserIds)})";
            var userFxs = cofigDb.Query<UserFx>(userFxSql).ToList();
            Log.WriteLine($"userFxSql=>{userFxSql},\n{userFxs.ToJson()}");

            var fxUserGroups = sendHistorys.GroupBy(x => x.FxUserId).ToList();
            foreach (var fg in fxUserGroups)
            {
                var fxUserId = fg.Key.Value;
                var userfx = userFxs.FirstOrDefault(x => x.Id == fxUserId);
                if (userfx == null)
                {
                    Log.WriteError($"未找到账号【{fxUserId}】");
                    action($"未找到账号【{fxUserId}】");
                    continue;
                }

                var historys = fg.ToList();
                new SiteContext(userfx);
                var syncDataInterfaceService = new SyncDataInterfaceService(fxUserId);

                #region 修补缺失WaybillCodeOrder和WaybillCodeOrderProduct
                if (needInsertWcos.Count > 0)
                {
                    var waybillCodeOrderService = new WaybillCodeOrderService(_connectionstring);
                    var waybillCodeOrderProductService = new WaybillCodeOrderProductService(_connectionstring);
                    Log.WriteLine($"needInsertWcos ==>{waybillCodeOrderService.baseRepository.DbConnection.ConnectionString}\n{needInsertWcos.Count()}");
                    foreach (var wco in needInsertWcos)
                    {
                        var wcoId = waybillCodeOrderService.Add(wco);
                        foreach (var wop in wco.WaybillCodeOrderProducts)
                        {
                            wop.MasterId = wcoId;
                            waybillCodeOrderProductService.Add(wop);
                        }
                    }

                    Log.WriteLine($"账号[{fxUserId}]修复[{needInsertWcos.Count}]个WaybillCodeOrder成功");
                }

                if (needInsertWops.Count > 0)
                {
                    var waybillCodeOrderProductService = new WaybillCodeOrderProductService(_connectionstring);
                    Log.WriteLine($"needInsertWops ==>{waybillCodeOrderProductService.baseRepository.DbConnection.ConnectionString}\n{needInsertWops.Count()}");
                    foreach (var wop in needInsertWops)
                    {
                        waybillCodeOrderProductService.Add(wop);
                    }
                    Log.WriteLine($"账号[{fxUserId}]修复[{needInsertWops.Count}]个WaybillCodeOrderProduct成功");
                }

                #endregion

                #region 修补发货记录相关数据
                if (historys.Any())
                {
                    var sendHistoryService = new SendHistoryService(_connectionstring);
                    var logicOrderService = new LogicOrderService(_connectionstring);
                    var settlementProductSkuService = new SettlementProductSkuService(_connectionstring);

                    #region 重新保存发货记录
                    //Log.Debug($"添加发货记录：{sendHistoryService.baseRepository.DbConnection.ConnectionString}\n{historys.Count()}");

                    sendHistoryService.Add(historys, false);
                    //分库方案增加逻辑，增加发货记录
                    syncDataInterfaceService.AddSendHistories(historys, isAppendProductQuantity: false);

                    Log.WriteLine($"账号[{fxUserId}]修复[{historys.Count}]个发货记录成功");
                    #endregion

                    #region 修复结算规格
                    var settlementInfos = new List<SettlementInfo>(); // 对账结算数据
                    var dictSettlementProductSku = new Dictionary<string, SettlementProductSku>(); //对账商品映射
                    foreach (var sh in historys)
                    {
                        var loginShopId = fxSystemShops.FirstOrDefault(x => x.FxUserIds == sh.FxUserId).Id;
                        foreach (var sho in sh.Orders)
                        {
                            // 过滤无商品线下单，不参与对账结算
                            if (sh.PlatformType == PlatformType.Virtual.ToString() && sho.OrderType == "OfflineNoSku")
                                continue;
                            foreach (var sop in sho.Products)
                            {
                                var sinfo = new SettlementInfo();
                                sinfo.SendHistoryId = sh.OldSendHistoryId > 0 ? sh.OldSendHistoryId : sh.ID; // 已存在的发货记录取存在的发货Id
                                sinfo.PathflowCode = sh.PathFlowCode;
                                sinfo.FxUserId = sh.FxUserId ?? fxUserId;
                                sinfo.SendType = sh.SendType;
                                sinfo.ShopId = loginShopId;
                                sinfo.SourceShopId = sh.ShopId;
                                sinfo.SendTime = sh.SendDate;
                                sinfo.CreateTime = sh.CreateTime;
                                sinfo.PlatformType = sh.PlatformType;
                                sinfo.LogistiscBillNo = sh.LogistiscBillNo;
                                sinfo.PlatformOrderId = sho.PlatformOrderId;
                                sinfo.LogicOrderId = sho.OrderId;
                                sinfo.IsPartSend = sho.IsPartSend;
                                sinfo.OrderItemCode = sop.OrderItemCode;
                                sinfo.ProductCode = sop.ProductCode;
                                sinfo.SkuCode = sop.SkuCode;
                                sinfo.ProductId = sop.ProductId;
                                sinfo.SkuId = sop.SkuId;
                                sinfo.ProductSubject = sop.ProductSubject;
                                sinfo.ProductCargoNumber = sop.ProductCargoNumber?.ToCutString(1024);
                                sinfo.CargoNumber = sop.CargoNumber;
                                //sinfo.ProductImg = sop.ProductImg; // 商品图片对账时再获取
                                sinfo.SkuImg = sop.SkuImg;
                                sinfo.Color = sop.Color?.ToCutString(64);
                                sinfo.Size = sop.Size?.ToCutString(64);
                                sinfo.Price = sop.Price;
                                sinfo.SendCount = sop.Quantity;
                                sinfo.RefundStatus = sop.RefundStatus;
                                sinfo.Status = sop.Status;
                                sinfo.SourceUserId = sh.UserId;
                                sinfo.SendHistoryCode = sh.SendHistoryCode;

                                settlementInfos.Add(sinfo);

                                //出账结算商品映射
                                var key = sop.SkuCode + sh.PathFlowCode + sop.OrderItemCode;
                                if (dictSettlementProductSku.ContainsKey(key) == false)
                                {
                                    var settlementProductSku = new SettlementProductSku
                                    {
                                        ProductCode = sop.ProductCode,
                                        SkuCode = sop.SkuCode,
                                        PathFlowCode = sh.PathFlowCode,
                                        SourceShopId = sh.SourceShopId,
                                        SourceUserId = sh.SourceUserId,
                                        FxUserId = SiteContext.Current.CurrentFxUserId,
                                        LastFxUserId = sh.UpFxUserId,
                                        CreateTime = DateTime.Now,
                                        Status = 0,
                                        OrderItemCode = sop.OrderItemCode // 发货订单项OrderItemCode
                                    };
                                    dictSettlementProductSku.Add(key, settlementProductSku);
                                }
                            }
                        }
                    }

                    //Log.Debug($"添加结算规格：{settlementProductSkuService.baseRepository.DbConnection.ConnectionString}\n{dictSettlementProductSku.Values.Count()}");
                    var settlementProductSkus = dictSettlementProductSku.Values.Where(x => x.ProductCode.IsNotNullOrEmpty() && x.SkuCode.IsNotNullOrEmpty()).ToList();
                    settlementProductSkuService.AddSettlementProductSku(settlementProductSkus);

                    Log.WriteLine($"账号[{fxUserId}]修复[{settlementProductSkus.Count}]个结算规格成功");
                    #endregion

                    #region 更新订单发货状态和发货时间

                    historys.GroupBy(x => x.SendDate).ToList().ForEach(g =>
                    {
                        try
                        {
                            var orders = g.SelectMany(x => x.Orders).ToList();
                            var batchSize = 500;
                            var count = Math.Ceiling(orders.Count * 1.0 / batchSize);
                            for (int j = 0; j < count; j++)
                            {
                                var tmpOrders = orders.Skip(j * batchSize).Take(batchSize).ToList();
                                var logicOrderIds = tmpOrders.Select(x => x.OrderId).Distinct().ToList();
                                var oiCodes = tmpOrders.SelectMany(x => x.Products).Select(x => x.OrderItemCode).Distinct().ToList();

                                //Log.Debug($"更新订单发货状态：{logicOrderService.baseRepository.DbConnection.ConnectionString}\n{string.Join(",", logicOrderIds)}");

                                logicOrderService.UpdateLogicOrderErpStateV2_Sended(logicOrderIds, $"'{g.Key.Format()}'", false);
                                logicOrderService.UpdateLogicOrderErpStateV2_LogicOrderItem(oiCodes, $"'{g.Key.Format()}'", false);

                                Log.WriteLine($"账号[{fxUserId}]更新[{logicOrderIds.Count}]个系统单发货状态成功");
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"更新订单发货状态和发货时间，更新数据：{g.ToJson()}，异常信息：{ex}");
                            throw ex;
                        }
                    });
                    #endregion

                }
                #endregion
            }
        }



        public void RepairSendHistoryWithPrintHistory(string startTime, string endTime, int splitMin, List<int> userIds, Action<string, bool> action)
        {
            var conn = CustomerConfig.ConfigureDbConnectionString;
            var cofigDb = DbUtility.GetConnection(conn);

            var sourceConn = new CommonSettingRepository().Get($"/System/Fendan/PrintHistory/ConnectiongString", 0)?.Value ?? "";
            if (string.IsNullOrEmpty(sourceConn))
                sourceConn = CustomerConfig.PrintHistoryDefaultMysqlConnectionString;

            foreach (var userId in userIds)
            {
                var printHistoryRepository = new PrintHistoryRepository(sourceConn, userId);
                Stopwatch sw = new Stopwatch();
                sw.Start();
                //按时间分片(一个小时一片)
                var startDate = DateTime.Parse(startTime);
                var endDate = DateTime.Parse(endTime);
                var pageCount = Math.Ceiling((endDate - startDate).TotalMinutes / splitMin * 1D);
                //分片查询
                var tmpStartTime = startDate;
                var tmpEndTime = tmpStartTime.AddMinutes(splitMin);
                for (int i = 0; i < pageCount; i++)
                {
                    if (tmpEndTime > endDate)
                        tmpEndTime = endDate;
                    if (tmpStartTime >= tmpEndTime)
                        break;
                    action($"开始分片{tmpStartTime.ToString("yyyy/MM/dd HH:mm:ss")} - {tmpEndTime.ToString("yyyy/MM/dd HH:mm:ss")} 从打印记录找缺失底单记录...", true);

                    // 查询底单记录
                    var sql = $"SELECT * FROM P_PrintHistory WHERE PrintDate >='{tmpStartTime.ToString("yyyy/MM/dd HH:mm:ss")}' AND PrintDate <'{tmpEndTime.ToString("yyyy/MM/dd HH:mm:ss")}'";
                    if (userIds.Any())
                        sql += $" AND FxUserId IN({string.Join(",", userIds)})";
                    action($"{printHistoryRepository.DbConnection.ConnectionString}\nsql=>{sql}", true);
                    var printHisotrys = printHistoryRepository.DbConnection.Query<PrintHistory>(sql).ToList();
                    action($"{printHistoryRepository.DbConnection.ConnectionString}\nsql=>{sql}\n找到【{printHisotrys.Count}】个打印记录", true);
                    if (printHisotrys.Any() == false)
                    {
                        tmpStartTime = tmpEndTime;
                        tmpEndTime = tmpStartTime.AddMinutes(splitMin);
                        continue;
                    }

                    var fxUserIds = printHisotrys.Select(x => x.FxUserId).Distinct().ToList();
                    var userSql = $@"SELECT u.* FROM dbo.P_UserFx u(NOLOCK)
                                    INNER JOIN dbo.FunStringToIntTable(@Codes,',') t ON t.item=u.Id
                                    INNER JOIN dbo.FxDbConfig f(NOLOCK) ON u.Id=f.FxUserId
                                    INNER JOIN dbo.P_DbNameConfig dn(NOLOCK) ON f.DbNameConfigId=dn.Id
                                    WHERE dn.DbServerConfigId = 52";
                    var userFxs = cofigDb.Query<UserFx>(userSql, new { Codes = string.Join(",", fxUserIds) }).ToList();
                    //Log.Debug($"userSql==> {userSql}", "diffSql.txt");
                    if (userFxs == null || userFxs.Any() == false)
                    {
                        tmpStartTime = tmpEndTime;
                        tmpEndTime = tmpStartTime.AddMinutes(splitMin);
                        continue;
                    }
                    // 暂时只查DbServerConfigId = 52 的数据库
                    printHisotrys = printHisotrys.Where(x => userFxs.Any(y => y.Id == x.FxUserId)).ToList();
                    var groups = printHisotrys.GroupBy(x => x.FxUserId).ToList();
                    action($"在DbServerConfigId = 52的有个【{userFxs.Count}】用户，总共【{printHisotrys.Count}】个打印记录", true);
                    foreach (var g in groups)
                    {
                        var userFx = userFxs.FirstOrDefault(x => x.Id == g.Key);
                        if (userFx == null)
                        {
                            action($"用户【{g.Key}】信息未找到...", true);
                            Log.Debug($"用户【{g.Key}】信息未找到...");
                            continue;
                        }
                        new SiteContext(userFx);

                        var lostWaybillCodes = new List<WaybillCode>();
                        var groupPrintHistorys = g.ToList();
                        var bathSize = 1000;
                        var count = Math.Ceiling(groupPrintHistorys.Count * 1.0 / bathSize);
                        for (int j = 0; j < count; j++)
                        {
                            var excodes = groupPrintHistorys.Skip(j * bathSize).Take(bathSize).Select(x => x.ExpressWaybillCode).Distinct().ToList();
                            var str = new StringBuilder();
                            foreach (var item in excodes)
                            {
                                str.AppendLine($"   SELECT '{item}' AS pid UNION ALL ");
                            }
                            // 根据打印记录找出缺失P_WaybillCode
                            var diffSql = $@"SELECT * INTO #tmp FROM (
{str.ToString().TrimEnd(" UNION ALL \r\n")}
) AS t

SELECT t.pid FROM #tmp t
LEFT JOIN dbo.P_WaybillCode w(NOLOCK) ON t.pid = w.ExpressWayBillCode
WHERE w.ExpressWayBillCode IS NULL";
                            var waybillCodeRepository = new WaybillCodeRepository();
                            //Log.Debug($"{waybillCodeRepository.DbConnection.ConnectionString}\ndiffSql==>{diffSql}", "diffSql.txt");
                            var lostWcodes = waybillCodeRepository.DbConnection.Query<string>(diffSql).ToList();
                            if (lostWcodes.Any() == false)
                                continue;
                            var minTime = groupPrintHistorys.Where(x => lostWcodes.Contains(x.ExpressWaybillCode)).Min(x => x.PrintDate).Format();
                            action($"用户【{g.Key}】找到【{lostWcodes.Count}】个缺失底单记录，最小时间【{minTime}】...", false);
                            Log.Debug($"{waybillCodeRepository.DbConnection.ConnectionString}\n用户【{g.Key}】，最小时间【{minTime}】找到【{lostWcodes.Count}】个缺失底单记录：\n{lostWcodes.ToJson()}");

                            // 找出P_PrintHistoryOrder用于修复底单
                            var phIds = groupPrintHistorys.Where(x => lostWcodes.Contains(x.ExpressWaybillCode)).Select(x => x.ID).Distinct().ToList();
                            var lostPrintHistorys = printHistoryRepository.GetDetailListByIds(phIds);
                            if (lostPrintHistorys.Any() == false)
                                continue;

                            // 查询订单补充SourceShopId和OrderItemCode
                            var logicOrderRepository = new LogicOrderRepository();
                            var oids = lostPrintHistorys.SelectMany(x => x.PrintHistoryOrders).Select(x => x.OrderId).Distinct().ToList();
                            var orders = logicOrderRepository.GetOrders(oids, isNeedProduct: true);
                            var oiCodeDic = orders.SelectMany(x => x.LogicOrderItems).GroupBy(x => x.OrderItemCode).ToDictionary(x => x.Key, x => x.First());

                            // 查询模板补充模板名称
                            var templateIds = groupPrintHistorys.Select(x => x.TemplateId).Distinct().ToList();
                            var tmpSql = $@"SELECT pt.Id,pt.TemplateName,ec.Names AS ExpressPic,ec.CompanyCode AS RulePic FROM dbo.P_PrintTemplate pt WITH(NOLOCK)
INNER JOIN dbo.P_ExpressCompany ec WITH(NOLOCK) ON pt.ExpressCompanyId=ec.Id
 WHERE pt.Id IN({string.Join(",", templateIds)})";
                            var templateInfos = cofigDb.Query<PrintTemplate>(tmpSql).ToList();

                            foreach (var ph in lostPrintHistorys)
                            {
                                var order = orders.FirstOrDefault(x => x.LogicOrderId == ph.PlatformOrderId);
                                if (order == null)
                                    order = orders.FirstOrDefault(x => ph.PrintHistoryOrders != null && ph.PrintHistoryOrders.Any(y => y.OrderId == x.LogicOrderId));

                                var template = templateInfos.FirstOrDefault(x => x.Id == ph.TemplateId);
                                var wc = new WaybillCode()
                                {
                                    ShopId = ph.ShopId,
                                    SourceShopId = order?.ShopId ?? 0,
                                    OrderId = ph.PlatformOrderId,
                                    OrderIdJoin = ph.PlatformOrderJoin,
                                    CustomerOrderId = ph.CustomerOrderId,
                                    ExpressWayBillCode = ph.ExpressWaybillCode,
                                    GetDate = ph.PrintDate,
                                    BuyerMemberId = ph.BuyerMemberId,
                                    BuyerMemberName = ph.BuyerMemberName,
                                    PlatformType = ph.PlatformType,//平台类型

                                    //收件人信息只存打码
                                    Reciver = ph.Reciver,
                                    ReciverPhone = ph.ReciverPhone,
                                    ToProvince = ph.ToProvince,
                                    ToCity = ph.ToCity,
                                    ToDistrict = ph.ToDistrict,
                                    ToAddress = ph.ReciverAddress,

                                    Sender = ph.Sender,
                                    FromProvince = ph.FromProvince,
                                    FromCity = ph.FromCity,
                                    ProductCount = ph.ProductCount,
                                    TotalWeight = ph.TotalWeight,
                                    TotalPayAomount = ph.TotalPayAomount,
                                    //Pid = ph.p,
                                    Status = 1, //状态：1：已打印，2：单号已回收，3：已发货
                                    ExpressId = ph.ExpressId,
                                    ExpressName = template?.ExpressPic, // 临时存快递公司名称
                                    ExpressNo = template?.RulePic,// 临时存快递公司编码
                                    TemplateId = ph.TemplateId,
                                    TemplateName = template?.TemplateName,
                                    TemplateType = ph.TemplateType,
                                    CreateDate = ph.PrintDate,
                                    PrintDataType = ph.PrintDataType, //11-15：线下单状态
                                    WaybillCodeOrders = ph.PrintHistoryOrders.Select(x => new WaybillCodeOrder
                                    {
                                        OrderId = x.OrderId,
                                        CustomerOrderId = x.CustomerOrderId,
                                        SendType = x.SendType,//发货类型
                                        WaybillCodeOrderProducts = x.PrintOrderProducts.Select(y => new WaybillCodeOrderProduct
                                        {
                                            OrderItemId = y.OrderItemId,
                                            Quantity = y.Quantity,
                                            OrderItemCode = order?.LogicOrderItems?.FirstOrDefault(z => z.Id == y.OrderItemId)?.OrderItemCode,
                                            SendType = y.SendType,
                                        }).ToList()
                                    }).ToList(), //取单号的订单
                                                 //ChildWaybillCodes = childWlbCodes, //子单号
                                    PrintMethod = ph.PrintMethod,//打印方式
                                    PrintBatchNumber = ph.PrintBatchNumber, //打印批次
                                    SendContent = ph.SendContent,
                                    UserId = ph.UserId,//SiteContext.Current.CurrentLoginUser == null ? 0 : SiteContext.Current.CurrentLoginUser.Id,
                                    UseRecordId = ph.UserId, //单号使用记录表的id
                                    FxUserId = ph.FxUserId,
                                    PathFlowCode = ph.PathFlowCode, //LogicOrderEntiy?.PathFlowCode ?? "",
                                    SendType = ph.SendType,//发货类型
                                };
                                lostWaybillCodes.Add(wc);
                            }

                            //保存底单数据
                            if (lostWaybillCodes.Any())
                                waybillCodeRepository.Add(lostWaybillCodes, null);

                            // 调用接口检查缺失单号是否已发货且缺失发货记录
                            var lostSendCodeLists = new List<WaybillCode>();
                            var shopIds = lostWaybillCodes.Select(x => x.SourceShopId).Distinct().ToList();
                            var shops = new ShopService().GetShopByIds(shopIds);
                            var groupLostWaybillCodess = lostWaybillCodes.GroupBy(x => x.SourceShopId).ToList();
                            foreach (var sg in groupLostWaybillCodess)
                            {
                                var shop = shops.FirstOrDefault(x => x.Id == sg.Key);
                                if (shop == null)
                                {
                                    action($"账号【{userFx.Id}】缺失店铺【{sg.Key}】信息，可能已解绑", true);
                                    continue;
                                }
                                var wcs = sg.ToList();
                                var pids = wcs.Select(x => x.CustomerOrderId).Distinct().ToList();
                                var ptService = PlatformFactory.GetPlatformService(shop);
                                var os = ptService.SyncOrders(pids);

                                var logistincInfos = os.SelectMany(x => x.LogisticsInfos).ToList();
                                foreach (var w in wcs)
                                {
                                    var logistincInfo = logistincInfos.FirstOrDefault(x => x.tracking_no == w.ExpressWayBillCode);
                                    if (logistincInfo != null)
                                    {
                                        w.SendDate = logistincInfo.ship_time;
                                        lostSendCodeLists.Add(w);
                                    }
                                }
                            }
                            // 缺失发货记录的，根据底单修复发货记录
                            if (lostSendCodeLists.Any())
                            {
                                var needInsertSendHistorys = new List<SendHistory>();
                                #region 补发货记录
                                try
                                {
                                    foreach (var w in lostSendCodeLists)
                                    {
                                        var sendDate = w.SendDate ?? DateTime.Now;
                                        var history = new SendHistory
                                        {
                                            ShopId = w.SourceShopId,
                                            OrderId = w.OrderId,
                                            CreateTime = w.SendDate,
                                            LogistiscBillNo = w.ExpressWayBillCode,//使用MutliPack里的数据
                                            ExpressCode = w.ExpressNo,
                                            ExpressName = w.ExpressName,
                                            BuyerMemberId = w.BuyerMemberId,
                                            BuyerMemberName = w.BuyerMemberName,
                                            Reciver = w.Reciver,
                                            ReciverPhone = w.ReciverPhone,
                                            ReciverAddress = w.ToAddress,
                                            ToProvince = w.ToProvince,
                                            ToCity = w.ToCity,
                                            ToDistrict = w.ToDistrict,
                                            FromProvince = w.FromProvince,
                                            FromCity = w.FromCity,
                                            FromDistrict = "from_wc",
                                            Sender = w.Sender,
                                            SenderPhone = "",
                                            SenderAddress = "",
                                            Weight = (w.TotalWeight * 1000).ToInt(),
                                            SendDate = sendDate,//DateTime.Now,
                                            SenderCompany = "",
                                            PathFlowCode = w.PathFlowCode,
                                            FxUserId = w.FxUserId,
                                            UserId = w.UserId,//SiteContext.Current.CurrentFxUser == null ? 0 : SiteContext.Current.CurrentFxUserId,
                                                              //UpFxUserId = w.UpFxUserId,
                                            SourceShopId = w.SourceShopId,
                                            SourceUserId = w.UserId,
                                            OrderJoin = w.OrderIdJoin,//只取当前运单号关联的LogicOrderId
                                            SendType = w.SendType,
                                            PlatformType = w.PlatformType,
                                        };

                                        foreach (var wo in w.WaybillCodeOrders)
                                        {
                                            var so = new SendHistoryOrder()
                                            {
                                                OrderId = wo.OrderId,
                                                SendType = wo.SendType,
                                                PlatformOrderId = wo.CustomerOrderId,
                                                //数量使用MutliPack里的数据
                                                ProductItemCount = wo.WaybillCodeOrderProducts.Sum(x => x.Quantity),
                                                ProductKindCount = wo.WaybillCodeOrderProducts.GroupBy(x => x.OrderItemCode).Count(),
                                                //ProductIdCount = wo.WaybillCodeOrderProducts.GroupBy(x => x.).Count(),
                                                //IsPartSend = isPartSendDic.ContainsKey(g.Key) && isPartSendDic[g.Key],
                                                PayTime = wo.PayTime,
                                                CreateTime = wo.CreateTime,
                                            };

                                            foreach (var wop in wo.WaybillCodeOrderProducts)
                                            {
                                                if (wop.OrderItemCode.IsNotNullOrEmpty())
                                                {
                                                    Log.WriteWarning($"账号【{userFx.Id}】未找到订单【{w.CustomerOrderId}】-[{wop.OrderItemId}]的信息");
                                                }

                                                var oi = wop.OrderItemCode.IsNotNullOrEmpty() && oiCodeDic.ContainsKey(wop.OrderItemCode) ? oiCodeDic[wop.OrderItemCode] : null;
                                                var sop = new SendOrderProduct
                                                {
                                                    OrderItemId = wop.OrderItemId,
                                                    Quantity = wop.Quantity,
                                                    OrderItemCode = wop.OrderItemCode,
                                                    SendType = wop.SendType,
                                                    ProductCode = oi?.ProductCode,
                                                    ProductId = oi?.ProductID,
                                                    SkuCode = oi?.SkuCode,
                                                    SkuId = oi?.SkuID,

                                                };
                                                so.Products.Add(sop);
                                            }
                                            history.Orders.Add(so);
                                        }

                                        needInsertSendHistorys.Add(history);
                                    }

                                    action($"账号【{userFx.Id}】缺失【{lostSendCodeLists.Count}】个发货记录，最新发货时间【{needInsertSendHistorys.Min(x => x.SendDate).Format()}】", true);
                                    var tuple = new Tuple<List<SendHistory>, List<WaybillCodeOrder>, List<WaybillCodeOrderProduct>>(needInsertSendHistorys, new List<WaybillCodeOrder>(), new List<WaybillCodeOrderProduct>());
                                    SaveRepairSendHistoryDatas((s) => { action(s, false); }, cofigDb, waybillCodeRepository.DbConnection.ConnectionString, tuple);
                                }
                                catch (Exception ex)
                                {
                                    Log.WriteError($"重新保存发货记录，异常信息：{ex}");
                                    throw ex;
                                }
                                #endregion
                            }
                        }
                    }

                    tmpStartTime = tmpEndTime;
                    tmpEndTime = tmpStartTime.AddMinutes(splitMin);

                }

                sw.Stop();
                action($"从打印记录找缺失底单记录完成，总耗时：{sw.Elapsed.TotalSeconds.ToString("f2")}s...", true);
            }
        }

        public void RepairSendHistoryWithPrintHistoryOld(string startTime, string endTime, int splitMin, List<int> userIds, Action<string, bool> action)
        {
            //List<DbConfigModel> dbConfigModels = null;

            //阿里云和拼多多云的店铺区分
            //先按平台分组，再按数据库分组
            var conn = CustomerConfig.ConfigureDbConnectionString;
            var cofigDb = DbUtility.GetConnection(conn);
            
            var sourceConn = new CommonSettingRepository().Get($"/System/Fendan/PrintHistory/ConnectiongString", 0)?.Value ?? "";
            if (string.IsNullOrEmpty(sourceConn))
                sourceConn = CustomerConfig.PrintHistoryDefaultMysqlConnectionString;
            var printHistoryRepository = new PrintHistoryRepository(sourceConn);
            Stopwatch sw = new Stopwatch();
            sw.Start();
            //按时间分片(一个小时一片)
            var startDate = DateTime.Parse(startTime);
            var endDate = DateTime.Parse(endTime);
            var pageCount = Math.Ceiling((endDate - startDate).TotalMinutes / splitMin * 1D);
            //分片查询
            var tmpStartTime = startDate;
            var tmpEndTime = tmpStartTime.AddMinutes(splitMin);
            for (int i = 0; i < pageCount; i++)
            {
                if (tmpEndTime > endDate)
                    tmpEndTime = endDate;
                if (tmpStartTime >= tmpEndTime)
                    break;
                action($"开始分片{tmpStartTime.ToString("yyyy/MM/dd HH:mm:ss")} - {tmpEndTime.ToString("yyyy/MM/dd HH:mm:ss")} 从打印记录找缺失底单记录...", true);

                // 查询底单记录
                var sql = $"SELECT * FROM P_PrintHistory WHERE PrintDate >='{tmpStartTime.ToString("yyyy/MM/dd HH:mm:ss")}' AND PrintDate <'{tmpEndTime.ToString("yyyy/MM/dd HH:mm:ss")}'";
                if (userIds.Any())
                    sql += $" AND FxUserId IN({string.Join(",", userIds)})";
                action($"{printHistoryRepository.DbConnection.ConnectionString}\nsql=>{sql}", true);
                var printHisotrys = printHistoryRepository.DbConnection.Query<PrintHistory>(sql).ToList();
                action($"{printHistoryRepository.DbConnection.ConnectionString}\nsql=>{sql}\n找到【{printHisotrys.Count}】个打印记录", true);
                if (printHisotrys.Any() == false)
                {
                    tmpStartTime = tmpEndTime;
                    tmpEndTime = tmpStartTime.AddMinutes(splitMin);
                    continue;
                }

                var fxUserIds = printHisotrys.Select(x => x.FxUserId).Distinct().ToList();
                var userSql = $@"SELECT u.* FROM dbo.P_UserFx u(NOLOCK)
                                    INNER JOIN dbo.FunStringToIntTable(@Codes,',') t ON t.item=u.Id
                                    INNER JOIN dbo.FxDbConfig f(NOLOCK) ON u.Id=f.FxUserId
                                    INNER JOIN dbo.P_DbNameConfig dn(NOLOCK) ON f.DbNameConfigId=dn.Id
                                    WHERE dn.DbServerConfigId = 52";
                var userFxs = cofigDb.Query<UserFx>(userSql, new { Codes = string.Join(",", fxUserIds) }).ToList();
                //Log.Debug($"userSql==> {userSql}", "diffSql.txt");
                if (userFxs == null || userFxs.Any() == false)
                {
                    tmpStartTime = tmpEndTime;
                    tmpEndTime = tmpStartTime.AddMinutes(splitMin);
                    continue;
                }
                // 暂时只查DbServerConfigId = 52 的数据库
                printHisotrys = printHisotrys.Where(x => userFxs.Any(y => y.Id == x.FxUserId)).ToList();
                var groups = printHisotrys.GroupBy(x => x.FxUserId).ToList();
                action($"在DbServerConfigId = 52的有个【{userFxs.Count}】用户，总共【{printHisotrys.Count}】个打印记录", true);
                foreach (var g in groups)
                {
                    var userFx = userFxs.FirstOrDefault(x => x.Id == g.Key);
                    if (userFx == null)
                    {
                        action($"用户【{g.Key}】信息未找到...", true);
                        Log.Debug($"用户【{g.Key}】信息未找到...");
                        continue;
                    }
                    new SiteContext(userFx);

                    var lostWaybillCodes = new List<WaybillCode>();
                    var groupPrintHistorys = g.ToList();
                    var bathSize = 1000;
                    var count = Math.Ceiling(groupPrintHistorys.Count * 1.0 / bathSize);
                    for (int j = 0; j < count; j++)
                    {
                        var excodes = groupPrintHistorys.Skip(j * bathSize).Take(bathSize).Select(x => x.ExpressWaybillCode).Distinct().ToList();
                        var str = new StringBuilder();
                        foreach (var item in excodes)
                        {
                            str.AppendLine($"   SELECT '{item}' AS pid UNION ALL ");
                        }
                        // 根据打印记录找出缺失P_WaybillCode
                        var diffSql = $@"SELECT * INTO #tmp FROM (
{str.ToString().TrimEnd(" UNION ALL \r\n")}
) AS t

SELECT t.pid FROM #tmp t
LEFT JOIN dbo.P_WaybillCode w(NOLOCK) ON t.pid = w.ExpressWayBillCode
WHERE w.ExpressWayBillCode IS NULL";
                        var waybillCodeRepository = new WaybillCodeRepository();
                        //Log.Debug($"{waybillCodeRepository.DbConnection.ConnectionString}\ndiffSql==>{diffSql}", "diffSql.txt");
                        var lostWcodes = waybillCodeRepository.DbConnection.Query<string>(diffSql).ToList();
                        if (lostWcodes.Any() == false)
                            continue;
                        var minTime = groupPrintHistorys.Where(x => lostWcodes.Contains(x.ExpressWaybillCode)).Min(x => x.PrintDate).Format();
                        action($"用户【{g.Key}】找到【{lostWcodes.Count}】个缺失底单记录，最小时间【{minTime}】...", false);
                        Log.Debug($"{waybillCodeRepository.DbConnection.ConnectionString}\n用户【{g.Key}】，最小时间【{minTime}】找到【{lostWcodes.Count}】个缺失底单记录：\n{lostWcodes.ToJson()}");

                        // 找出P_PrintHistoryOrder用于修复底单
                        var phIds = groupPrintHistorys.Where(x => lostWcodes.Contains(x.ExpressWaybillCode)).Select(x => x.ID).Distinct().ToList();
                        var lostPrintHistorys = printHistoryRepository.GetDetailListByIds(phIds);
                        if (lostPrintHistorys.Any() == false)
                            continue;

                        // 查询订单补充SourceShopId和OrderItemCode
                        var logicOrderRepository = new LogicOrderRepository();
                        var oids = lostPrintHistorys.SelectMany(x => x.PrintHistoryOrders).Select(x => x.OrderId).Distinct().ToList();
                        var orders = logicOrderRepository.GetOrders(oids, isNeedProduct: true);
                        var oiCodeDic = orders.SelectMany(x => x.LogicOrderItems).GroupBy(x => x.OrderItemCode).ToDictionary(x => x.Key, x => x.First());

                        // 查询模板补充模板名称
                        var templateIds = groupPrintHistorys.Select(x => x.TemplateId).Distinct().ToList();
                        var tmpSql = $@"SELECT pt.Id,pt.TemplateName,ec.Names AS ExpressPic,ec.CompanyCode AS RulePic FROM dbo.P_PrintTemplate pt WITH(NOLOCK)
INNER JOIN dbo.P_ExpressCompany ec WITH(NOLOCK) ON pt.ExpressCompanyId=ec.Id
 WHERE pt.Id IN({string.Join(",", templateIds)})";
                        var templateInfos = cofigDb.Query<PrintTemplate>(tmpSql).ToList();

                        foreach (var ph in lostPrintHistorys)
                        {
                            var order = orders.FirstOrDefault(x => x.LogicOrderId == ph.PlatformOrderId);
                            if (order == null)
                                order = orders.FirstOrDefault(x => ph.PrintHistoryOrders != null && ph.PrintHistoryOrders.Any(y => y.OrderId == x.LogicOrderId));

                            var template = templateInfos.FirstOrDefault(x => x.Id == ph.TemplateId);
                            var wc = new WaybillCode()
                            {
                                ShopId = ph.ShopId,
                                SourceShopId = order?.ShopId ?? 0,
                                OrderId = ph.PlatformOrderId,
                                OrderIdJoin = ph.PlatformOrderJoin,
                                CustomerOrderId = ph.CustomerOrderId,
                                ExpressWayBillCode = ph.ExpressWaybillCode,
                                GetDate = ph.PrintDate,
                                BuyerMemberId = ph.BuyerMemberId,
                                BuyerMemberName = ph.BuyerMemberName,
                                PlatformType = ph.PlatformType,//平台类型

                                //收件人信息只存打码
                                Reciver = ph.Reciver,
                                ReciverPhone = ph.ReciverPhone,
                                ToProvince = ph.ToProvince,
                                ToCity = ph.ToCity,
                                ToDistrict = ph.ToDistrict,
                                ToAddress = ph.ReciverAddress,

                                Sender = ph.Sender,
                                FromProvince = ph.FromProvince,
                                FromCity = ph.FromCity,
                                ProductCount = ph.ProductCount,
                                TotalWeight = ph.TotalWeight,
                                TotalPayAomount = ph.TotalPayAomount,
                                //Pid = ph.p,
                                Status = 1, //状态：1：已打印，2：单号已回收，3：已发货
                                ExpressId = ph.ExpressId,
                                ExpressName = template?.ExpressPic, // 临时存快递公司名称
                                ExpressNo = template?.RulePic,// 临时存快递公司编码
                                TemplateId = ph.TemplateId,
                                TemplateName = template?.TemplateName,
                                TemplateType = ph.TemplateType,
                                CreateDate = ph.PrintDate,
                                PrintDataType = ph.PrintDataType, //11-15：线下单状态
                                WaybillCodeOrders = ph.PrintHistoryOrders.Select(x => new WaybillCodeOrder
                                {
                                    OrderId = x.OrderId,
                                    CustomerOrderId = x.CustomerOrderId,
                                    SendType = x.SendType,//发货类型
                                    WaybillCodeOrderProducts = x.PrintOrderProducts.Select(y => new WaybillCodeOrderProduct
                                    {
                                        OrderItemId = y.OrderItemId,
                                        Quantity = y.Quantity,
                                        OrderItemCode = order?.LogicOrderItems?.FirstOrDefault(z => z.Id == y.OrderItemId)?.OrderItemCode,
                                        SendType = y.SendType,
                                    }).ToList()
                                }).ToList(), //取单号的订单
                                //ChildWaybillCodes = childWlbCodes, //子单号
                                PrintMethod = ph.PrintMethod,//打印方式
                                PrintBatchNumber = ph.PrintBatchNumber, //打印批次
                                SendContent = ph.SendContent,
                                UserId = ph.UserId,//SiteContext.Current.CurrentLoginUser == null ? 0 : SiteContext.Current.CurrentLoginUser.Id,
                                UseRecordId = ph.UserId, //单号使用记录表的id
                                FxUserId = ph.FxUserId,
                                PathFlowCode = ph.PathFlowCode, //LogicOrderEntiy?.PathFlowCode ?? "",
                                SendType = ph.SendType,//发货类型
                            };
                            lostWaybillCodes.Add(wc);
                        }

                        //保存底单数据
                        if (lostWaybillCodes.Any())
                            waybillCodeRepository.Add(lostWaybillCodes, null);

                        // 调用接口检查缺失单号是否已发货且缺失发货记录
                        var lostSendCodeLists = new List<WaybillCode>();
                        var shopIds = lostWaybillCodes.Select(x => x.SourceShopId).Distinct().ToList();
                        var shops = new ShopService().GetShopByIds(shopIds);
                        var groupLostWaybillCodess = lostWaybillCodes.GroupBy(x => x.SourceShopId).ToList();
                        foreach (var sg in groupLostWaybillCodess)
                        {
                            var shop = shops.FirstOrDefault(x => x.Id == sg.Key);
                            if (shop == null)
                            {
                                action($"账号【{userFx.Id}】缺失店铺【{sg.Key}】信息，可能已解绑", true);
                                continue;
                            }
                            var wcs = sg.ToList();
                            var pids = wcs.Select(x => x.CustomerOrderId).Distinct().ToList();
                            var ptService = PlatformFactory.GetPlatformService(shop);
                            var os = ptService.SyncOrders(pids);

                            var logistincInfos = os.SelectMany(x => x.LogisticsInfos).ToList();
                            foreach (var w in wcs)
                            {
                                var logistincInfo = logistincInfos.FirstOrDefault(x => x.tracking_no == w.ExpressWayBillCode);
                                if (logistincInfo != null)
                                {
                                    w.SendDate = logistincInfo.ship_time;
                                    lostSendCodeLists.Add(w);
                                }
                            }
                        }
                        // 缺失发货记录的，根据底单修复发货记录
                        if (lostSendCodeLists.Any())
                        {
                            var needInsertSendHistorys = new List<SendHistory>();
                            #region 补发货记录
                            try
                            {
                                foreach (var w in lostSendCodeLists)
                                {
                                    var sendDate = w.SendDate ?? DateTime.Now;
                                    var history = new SendHistory
                                    {
                                        ShopId = w.SourceShopId,
                                        OrderId = w.OrderId,
                                        CreateTime = w.SendDate,
                                        LogistiscBillNo = w.ExpressWayBillCode,//使用MutliPack里的数据
                                        ExpressCode = w.ExpressNo,
                                        ExpressName = w.ExpressName,
                                        BuyerMemberId = w.BuyerMemberId,
                                        BuyerMemberName = w.BuyerMemberName,
                                        Reciver = w.Reciver,
                                        ReciverPhone = w.ReciverPhone,
                                        ReciverAddress = w.ToAddress,
                                        ToProvince = w.ToProvince,
                                        ToCity = w.ToCity,
                                        ToDistrict = w.ToDistrict,
                                        FromProvince = w.FromProvince,
                                        FromCity = w.FromCity,
                                        FromDistrict = "from_wc",
                                        Sender = w.Sender,
                                        SenderPhone = "",
                                        SenderAddress = "",
                                        Weight = (w.TotalWeight * 1000).ToInt(),
                                        SendDate = sendDate,//DateTime.Now,
                                        SenderCompany = "",
                                        PathFlowCode = w.PathFlowCode,
                                        FxUserId = w.FxUserId,
                                        UserId = w.UserId,//SiteContext.Current.CurrentFxUser == null ? 0 : SiteContext.Current.CurrentFxUserId,
                                                          //UpFxUserId = w.UpFxUserId,
                                        SourceShopId = w.SourceShopId,
                                        SourceUserId = w.UserId,
                                        OrderJoin = w.OrderIdJoin,//只取当前运单号关联的LogicOrderId
                                        SendType = w.SendType,
                                        PlatformType = w.PlatformType,
                                    };

                                    foreach (var wo in w.WaybillCodeOrders)
                                    {
                                        var so = new SendHistoryOrder()
                                        {
                                            OrderId = wo.OrderId,
                                            SendType = wo.SendType,
                                            PlatformOrderId = wo.CustomerOrderId,
                                            //数量使用MutliPack里的数据
                                            ProductItemCount = wo.WaybillCodeOrderProducts.Sum(x => x.Quantity),
                                            ProductKindCount = wo.WaybillCodeOrderProducts.GroupBy(x => x.OrderItemCode).Count(),
                                            //ProductIdCount = wo.WaybillCodeOrderProducts.GroupBy(x => x.).Count(),
                                            //IsPartSend = isPartSendDic.ContainsKey(g.Key) && isPartSendDic[g.Key],
                                            PayTime = wo.PayTime,
                                            CreateTime = wo.CreateTime,
                                        };

                                        foreach (var wop in wo.WaybillCodeOrderProducts)
                                        {
                                            if (wop.OrderItemCode.IsNotNullOrEmpty())
                                            {
                                                Log.WriteWarning($"账号【{userFx.Id}】未找到订单【{w.CustomerOrderId}】-[{wop.OrderItemId}]的信息");
                                            }

                                            var oi = wop.OrderItemCode.IsNotNullOrEmpty() && oiCodeDic.ContainsKey(wop.OrderItemCode) ? oiCodeDic[wop.OrderItemCode] : null;
                                            var sop = new SendOrderProduct
                                            {
                                                OrderItemId = wop.OrderItemId,
                                                Quantity = wop.Quantity,
                                                OrderItemCode = wop.OrderItemCode,
                                                SendType = wop.SendType,
                                                ProductCode = oi?.ProductCode,
                                                ProductId = oi?.ProductID,
                                                SkuCode = oi?.SkuCode,
                                                SkuId = oi?.SkuID,

                                            };
                                            so.Products.Add(sop);
                                        }
                                        history.Orders.Add(so);
                                    }

                                    needInsertSendHistorys.Add(history);
                                }

                                action($"账号【{userFx.Id}】缺失【{lostSendCodeLists.Count}】个发货记录，最新发货时间【{needInsertSendHistorys.Min(x => x.SendDate).Format()}】", true);
                                var tuple = new Tuple<List<SendHistory>, List<WaybillCodeOrder>, List<WaybillCodeOrderProduct>>(needInsertSendHistorys, new List<WaybillCodeOrder>(), new List<WaybillCodeOrderProduct>());
                                SaveRepairSendHistoryDatas((s) => { action(s, false); }, cofigDb, waybillCodeRepository.DbConnection.ConnectionString, tuple);
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError($"重新保存发货记录，异常信息：{ex}");
                                throw ex;
                            }
                            #endregion
                        }
                    }
                }

                tmpStartTime = tmpEndTime;
                tmpEndTime = tmpStartTime.AddMinutes(splitMin);

            }

            sw.Stop();
            action($"从打印记录找缺失底单记录完成，总耗时：{sw.Elapsed.TotalSeconds.ToString("f2")}s...", true);
        }

        public List<WaybillCode> GetWaybillCodeList(List<string> waybillCodes, string fields)
        {
            return _repository.GetWaybillCodeList(waybillCodes, fields);
        }

        /// <summary>
        /// 获取底单信息，异常订单使用
        /// </summary>
        /// <param name="pathFlowCode"></param>
        /// <param name="orderId"></param>
        /// <returns></returns>
        public List<WaybillCode> GetsByAbnormalOrder(string pathFlowCode, string orderId)
        {
            return _repository.GetsByAbnormalOrder(pathFlowCode, orderId);
        }

        /// <summary>
        /// 查询底层
        /// </summary>
        /// <param name="expressWaybillCodes"></param>
        /// <returns></returns>
        public List<WaybillCode> GetWaybillCodesByExpressWaybillCodes(List<string> expressWaybillCodes)
        {
            //排空处理
            if (expressWaybillCodes == null || !expressWaybillCodes.Any())
            {
                return new List<WaybillCode>();
            }
            //底单模型
            var models = new List<WaybillCode>();
            //分块查询
            var chunks = expressWaybillCodes.ChunkList(200);
            chunks.ForEach(chunk =>
            {
                var waybillCodes = _repository.GetWaybillCodesByExpressWaybillCodes(expressWaybillCodes);
                if (waybillCodes != null && waybillCodes.Any())
                {
                    models.AddRange(waybillCodes);
                }
            });
            //返回
            return models;
        }

        /// <summary>
        /// 查询底单信息（订单，商品） 按系统单号
        /// </summary>
        /// <param name="orderIds"></param>
        /// <returns></returns>
        public List<WaybillCode> GetWaybillCodesByOrderIds(List<string> orderIds)
        {
            return _repository.GetWaybillCodesByOrderIds(orderIds);
        }
        
        /// <summary>
        /// 获取ID列表（分页）
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<int> GetIdsBySendHistoryAnalysis(SendHistoryAnalysisConditionModel condition,
            int pageSize = 500)
        {
            return _repository.GetIdsBySendHistoryAnalysis(condition, pageSize);
        }

        /// <summary>
        /// 查询底单信息（发货记录分析）
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<WaybillCode> GetListBySendHistoryAnalysis(List<int> ids)
        {
            return _repository.GetListBySendHistoryAnalysis(ids);
        }

        /// <summary>
        /// 更新底单发货时间
        /// </summary>
        /// <param name="orderIds"></param>
        /// <param name="sendDate"></param>
        /// <returns></returns>
        public int UpdateWaybillCodeSendDateByOrderIds(List<string> orderIds, DateTime sendDate)
        {
            return _repository.UpdateWaybillCodeSendDateByOrderIds(orderIds, sendDate);
        }

        /// <summary>
        /// 根据order查询
        /// </summary>
        /// <param name="orderIds"></param>
        /// <returns></returns>
        public List<WaybillCode> GetListByOrdesrIds(List<string> orderIds)
        {
            return _repository.GetListByOrdesrIds(orderIds);
        }

        /// <summary>
        /// 查询近45天该店铺是否有数据，按5天间隔查询，查到即返回
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="cpt"></param>
        /// <returns></returns>
        public bool IsExistWaybillCodeByShopId(int shopId, string cpt)
        {
            if (shopId <= 0) return false;
    
            // 查询该店铺的用户
            var fxUserId = new FxUserShopService().GetUserIdByShopId(new List<int> { shopId }, "FxUserId").FirstOrDefault()?.FxUserId ?? 0;
            if (fxUserId <= 0)
                return false;
    
            // 查询该用户所在库
            var dbConfigRepository = new DbConfigRepository();
            var fxUserDbs = dbConfigRepository.GetListByFxUserIds(new List<int> { fxUserId }, cpt);
            if (fxUserDbs == null || !fxUserDbs.Any())
                return false;
    
            // 检查是否已迁移到新库
            var newDb = fxUserDbs.FirstOrDefault(x => x.DbNameConfig?.ApplicationName == "fx_new" && x.FromFxDbConfig == 1);
            if (newDb != null && !string.IsNullOrEmpty(newDb.ConnectionString))
            {
                // 优先查询新库
                var tempRepository = new WaybillCodeRepository(newDb.ConnectionString);
                if (tempRepository.IsExistWaybillCodeByShopId(shopId))
                    return true;
            }
            else
            {
                // 查询用户库的底单数据
                foreach (var dbModel in fxUserDbs.Where(db => !string.IsNullOrEmpty(db.ConnectionString)))
                {
                    var tempRepository = new WaybillCodeRepository(dbModel.ConnectionString);
                    if (tempRepository.IsExistWaybillCodeByShopId(shopId))
                        return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 验证请求参数
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        public Tuple<bool, string> CheckRequestModel(WaybillCodeRequestModel requestModel)
        {
            if (requestModel == null)
            {
                return new Tuple<bool, string>(false, "请求参数不能为空");
            }
            
            if (string.IsNullOrEmpty(requestModel.OrderId) == false)
            {
                var orderIds = requestModel.OrderId.SplitToList(",").ConvertAll(x => x.Trim()).Where(x => x.IsNotNullOrEmpty()).ToList();
                if (orderIds.Count > 500) return new Tuple<bool, string>(false, "系统单号搜索不能超过500个");
            }
            
            if (string.IsNullOrEmpty(requestModel.ExpressWaybillCode) == false)
            {
                var expressWaybillCodes = requestModel.ExpressWaybillCode.SplitToList(",").ConvertAll(x => x.Trim()).Where(x => x.IsNotNullOrEmpty()).ToList();
                if (expressWaybillCodes.Count > 500) return new Tuple<bool, string>(false, "运单号搜索不能超过500个");
            }
            
            if (string.IsNullOrEmpty(requestModel.CustomerOrderId) == false)
            {
                var customerOrderIds = requestModel.CustomerOrderId.SplitToList(",").ConvertAll(x => x.Trim()).Where(x => x.IsNotNullOrEmpty()).ToList();
                if (customerOrderIds.Count > 500) return new Tuple<bool, string>(false, "订单编号搜索不能超过500个");
            }
            
            return new Tuple<bool, string>(true, "");
        }
    }
}
