using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services.ServicesExtension
{
    /// <summary>
    /// 向平台回传当前账号开通的面单情况
    /// </summary>
    public class OpenEbillSendTaskService
    {
        private readonly string OpenEbillLastSendTimeKey = "DianGuanJia:FenXiao:OpenEbillLastSendTime";
        private ShopService _shopService = new ShopService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private AliOpenEbillSendRecordService _aliOpenEbillSendRecordService = new AliOpenEbillSendRecordService();
        private readonly List<string> NeedCheckPlatformTypes = new List<string>() { PlatformType.Alibaba.ToString() };
        private string LogFileName = $"AliOpenEbillSendRecordService-{DateTime.Now.ToString("yyyyMMdd")}.txt";
        private ConcurrentBag<BusinessLogModel> CurrentBatchLogs = new ConcurrentBag<BusinessLogModel>();
        private readonly CommService _commService = new CommService();

        /// <summary>
        /// 执行任务入口
        /// </summary>
        public void ExecuteCheckEbillOpenStatusAsync()
        {
            //系统店铺ID
            var shopId = SiteContext.Current.CurrentShopId;
            if (!IsEnabledCheckEbillOpenStatus(shopId))
            {
                return;
            }

            //判断上次上传的日期,10分钟上传一次
            var key = this.OpenEbillLastSendTimeKey;
            if (!CustomerConfig.IsDebug)
            {
                var lastSendTimeSet = _commonSettingService.Get(key, shopId);
                if (lastSendTimeSet != null && !string.IsNullOrWhiteSpace(lastSendTimeSet.Value))
                {
                    if (DateTime.TryParse(lastSendTimeSet.Value, out var lastSendTime) &&
                        (DateTime.Now - lastSendTime).TotalMinutes <= 30)
                    {
                        return;
                    }
                }
            }

            //检测当前绑定的店铺是否有1688
            var userShops = SiteContext.Current.UserShops;
            if (userShops == null ||
                !userShops.Any() && !userShops.Any(u => NeedCheckPlatformTypes.Contains(u.PlatformType)))
            {
                return;
            }

            var logView = new BusinessLogModel()
            {
                MethodName = "OpenEbillSendTaskService",
                BatchId = Guid.NewGuid().ToString().Replace("-", string.Empty),
                SourceType = "ExecuteCheckEbillOpenStatusAsync",
                CreatorId = SiteContext.Current.CurrentFxUserId,
                FxUserId = SiteContext.Current.CurrentFxUserId,
                ShopId = shopId,
                BusinessType = BusinessTypes.OpenEbillSendTask.ToString()
            };

            //异步处理
            ThreadPool.QueueUserWorkItem((state) =>
            {
                try
                {
                    _commonSettingService.ExecuteWithOptimisticLock(() =>
                        {
                            //设置最后执行回传的时间
                            _commonSettingService.Set(key, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), shopId);

                            CheckEbillOpenStatusSendToAli(logView);

                            //记录日志
                            BusinessLogDataEventTrackingService.Instance.WriteLog(CurrentBatchLogs.ToList());
                            return string.Empty;
                        }, () =>
                        {
                            var newLogView = logView.ToJson().ToObject<BusinessLogModel>();
                            newLogView.SubBusinessType = "异步处理";
                            newLogView.ExceptionMessage =
                                $"【{SiteContext.Current.CurrentLoginShop.ShopId}】回传开通非淘系面单情况给阿里的操作，未能进入并发锁，线程ID:{Thread.CurrentThread.ManagedThreadId}";
                            CurrentBatchLogs.Add(newLogView);
                            BusinessLogDataEventTrackingService.Instance.WriteLog(CurrentBatchLogs.ToList());

                            var str =
                                $"【{SiteContext.Current.CurrentLoginShop.ShopId}】回传开通非淘系面单情况给阿里的操作，未能进入并发锁，线程ID:{Thread.CurrentThread.ManagedThreadId}";
                            Log.Debug(str);
                        }, OptimisticLockOperationType.FxCheckOpenEbillSend, $"op{shopId}", 10); //最多锁定10分钟
                }
                catch (Exception ex)
                {
                    var newLogView = logView.ToJson().ToObject<BusinessLogModel>();
                    newLogView.SubBusinessType = "异步处理";
                    newLogView.ExceptionMessage =
                        $"【{SiteContext.Current.CurrentLoginShop.ShopId}】检查阿里店铺电子面单开通情况异常：{ex}";
                    CurrentBatchLogs.Add(newLogView);
                    BusinessLogDataEventTrackingService.Instance.WriteLog(CurrentBatchLogs.ToList());

                    Log.WriteError($"【{SiteContext.Current.CurrentLoginShop.ShopId}】检查阿里店铺电子面单开通情况异常：{ex}",
                        LogFileName);
                }
            });
        }

        /// <summary>
        /// 检查 阿里店铺电子面单开通情况
        /// 开通回传Y，取消开通回传N
        /// </summary>
        private void CheckEbillOpenStatusSendToAli(BusinessLogModel logView)
        {
            var logText = string.Empty;
            //系统店铺ID
            var currentLoginShop = SiteContext.Current.CurrentLoginShop;
            try
            {
                var sw = Stopwatch.StartNew();
                //执行存储过程=》检查电子面单开通情况
                var loginFxShopId = currentLoginShop.Id;
                var openedEbillInfoList = _shopService.GetAliOpenEbillInfo(loginFxShopId, currentLoginShop.PlatformType)
                    .Where(f => string.IsNullOrWhiteSpace(f.Type) == false && f.Type?.ToLower() != "link").ToList();
                sw.Stop();
                logText += $"\r\n查询开通情况存储过程执行，耗时：{sw.ElapsedMilliseconds}ms，结果【{openedEbillInfoList.ToJson()}】";

                //过滤出需要回传的电子面单
                var needHandlePlatform = openedEbillInfoList.Select(item => item.Type?.ToLower()).Distinct().ToList();

                var needHandeShopIds = openedEbillInfoList
                    .SelectMany(item => new[] { item.Id, item.SourceShopId })
                    .Distinct()
                    .ToList();

                var wayBillAccountList = _commService.GetAccountAuthInfoList(needHandeShopIds, false);

                sw = Stopwatch.StartNew();

                var allPlatform = new HashSet<string>
                    { "kuaishou", "pinduoduo", "toutiao", "xiaohongshu", "jingdong", "wxvideo" , "taobao"};

                var billSendRecords = new List<AliOpenEbillSendRecord>(wayBillAccountList.Count);

                foreach (var wayBillAuthConfig in wayBillAccountList)
                {
                    if (!needHandlePlatform.Contains(wayBillAuthConfig.Types?.ToLower()))
                    {
                        continue;
                    }

                    var branchAddressList = wayBillAuthConfig.BranchAddressList;

                    var validCpTypes = new HashSet<long> { 1L, 4L, 5L };

                    var billSendRecord = new AliOpenEbillSendRecord
                    {
                        EbillType = wayBillAuthConfig.Types
                    };

                    if (branchAddressList == null || !branchAddressList.Any())
                    {
                        billSendRecord.Status = "N";
                    }
                    else if (branchAddressList.SelectMany(item => item.BranchAddress)
                                 .FirstOrDefault(item => item.Quantity > 0 || validCpTypes.Contains(item.CpType)) ==
                             null)
                    {
                        billSendRecord.Status = "N";
                    }
                    else
                    {
                        billSendRecord.Status = "Y";
                    }

                    billSendRecords.Add(billSendRecord);
                }

                // 如果 ebillSendRecords 的 EbillType 不在 allPlatform 中，则将创建平台 Status 设置为 N
                foreach (var platform in allPlatform)
                {
                    var record = billSendRecords.Find(item => item.EbillType.ToLower() == platform);

                    if (record != null)
                    {
                        continue;
                    }

                    var sendRecord = new AliOpenEbillSendRecord()
                    {
                        EbillType = platform,
                        Status = "N"
                    };

                    billSendRecords.Add(sendRecord);
                }

                billSendRecords.ForEach(item =>
                {
                    var tmpName = item.EbillType.ToLower();

                    if (tmpName == "pddwaybill")
                    {
                        tmpName = "pinduoduo";
                    }
                    else if (tmpName == "taobao")
                    {
                        // thyny 为淘宝电子面单渠道编码
                        tmpName = "thyny";
                    }
                    item.EbillType = tmpName;
                });

                // 对 ebillSendRecords 进行处理，根据 EbillType 进行分组，取每组中 Status 为 Y 的记录, 如果没有 Y 的记录，则取 N 的记录
                billSendRecords = billSendRecords.GroupBy(item => item.EbillType).Select(item =>
                {
                    var yRecord = item.FirstOrDefault(i => i.Status == "Y");
                    return yRecord ?? item.FirstOrDefault(i => i.Status == "N");
                }).ToList();

                sw.Stop();
                
                sw = Stopwatch.StartNew();
                //回传
                var statusParams = billSendRecords.ToDictionary(item => item.EbillType, item => item.Status);
                
                logText += $"\r\n开始回传数据，参数：{statusParams.ToJson()}";
                
                SendOpenStatusToAli(currentLoginShop, statusParams, logView);
                sw.Stop();
                logText += $"\r\n回传数据，耗时：{sw.ElapsedMilliseconds}ms";

                var newLogView = logView.ToJson().ToObject<BusinessLogModel>();
                newLogView.SourceType = "CheckEbillOpenStatusSendToAli";
                newLogView.SubBusinessType = "处理中的耗时计算";
                newLogView.Content = logText;
                CurrentBatchLogs.Add(newLogView);

                Log.Debug(() => $"{logText}【{currentLoginShop.ShopId}】", LogFileName);
            }
            catch (Exception ex)
            {
                var newLogView = logView.ToJson().ToObject<BusinessLogModel>();
                newLogView.SourceType = "CheckEbillOpenStatusSendToAli";
                newLogView.SubBusinessType = "阿里店铺电子面单开通情况";
                newLogView.ExceptionMessage = $"【{SiteContext.Current.CurrentLoginShop.ShopId}】检查阿里店铺电子面单开通情况异常：{ex}";
                CurrentBatchLogs.Add(newLogView);

                Log.WriteError($"【{currentLoginShop.ShopId}】检查阿里店铺电子面单开通情况失败：{ex}", LogFileName);
            }
        }

        /// <summary>
        /// 遍历当前绑定的账号发送开通电子面单情况
        /// </summary>
        /// <param name="currentLoginShop"></param>
        /// <param name="statusParams"></param>
        /// <param name="logView"></param>
        private void SendOpenStatusToAli(Shop currentLoginShop, Dictionary<string, string> statusParams,
            BusinessLogModel logView)
        {
            var sendRecordList = new List<AliOpenEbillSendRecord>();
            var sendLogList = new ConcurrentBag<AliSendOpenEbillStatusLog>();
            //当前绑定的店铺
            var userShopIds = SiteContext.Current.UserShops
                .Where(w => w.PlatformType == PlatformType.Alibaba.ToString()).Select(u => u.Id).ToList();
            var userShops = _shopService.GetShopsAndShopExtension(userShopIds);

            var newLogView = logView.ToJson().ToObject<BusinessLogModel>();
            newLogView.SourceType = "SendOpenStatusToAli";
            newLogView.SubBusinessType = "当前账户绑定的1688店铺";
            newLogView.Content = new
            {
                UserShops = userShops.Select(x => new
                {
                    x.Id,
                    x.NickName,
                    AppKey = x?.ShopExtension?.AppKey ?? ""
                }),
                ebillOpenInfos = statusParams
            }.ToJson(true);
            CurrentBatchLogs.Add(newLogView);

            Parallel.ForEach(userShops, new ParallelOptions { MaxDegreeOfParallelism = 5 }, sendShop =>
            {
                if (!(PlatformFactory.GetPlatformService(sendShop) is AlibabaPlatformService ptService))
                {
                    Log.WriteError($"{sendShop.NickName}：调用阿里巴巴接口回传失败：未找到对应的平台服务", LogFileName);
                    return;
                }

                foreach (var keyValuePair in statusParams)
                {
                    try
                    {
                        var ebillType = keyValuePair.Key;
                        var status = keyValuePair.Value;
                        var rst = ptService.SendEbillOpenStatus(ebillType, status);

                        if (rst == null)
                        {
                            Log.WriteError($"{sendShop.NickName}：调用阿里巴巴接口回传请求：{keyValuePair.ToJson()}，，无响应,",
                                LogFileName);
                            return;
                        }

                        var sendLog = new AliSendOpenEbillStatusLog()
                        {
                            ShopId = currentLoginShop.Id,
                            EbillType = ebillType,
                            Status = status,
                            IsSucc = true,
                            CreateTime = DateTime.Now,
                        };
                        if (rst.Item1)
                        {
                            //只有回传成功的才保存回传记录
                            sendRecordList.Add(new AliOpenEbillSendRecord
                            {
                                ShopId = sendShop.Id,
                                EbillType = ebillType,
                                Status = status,
                                CreateTime = DateTime.Now
                            });
                        }
                        else
                        {
                            sendLog.IsSucc = false;
                            sendLog.ErrMsg = rst.Item2;
                        }

                        sendLogList.Add(sendLog);
                    }
                    catch (Exception ex)
                    {
                        var chiLogView = logView.ToJson().ToObject<BusinessLogModel>();
                        chiLogView.SourceType = "SendEbillOpenStatus";
                        chiLogView.SubBusinessType = "调用阿里巴巴接口回传失败";
                        chiLogView.ExceptionMessage = $"{sendShop.NickName}：调用阿里巴巴接口回传失败：{ex}";
                        CurrentBatchLogs.Add(chiLogView);

                        Log.WriteError($"{sendShop.NickName}：调用阿里巴巴接口回传失败：{ex}", LogFileName);
                    }
                }
            });

            //回流成功的记录入库
            SaveSendRecord(sendRecordList, logView);

            //记录请求接口日志
            SaveLog(sendLogList.ToList(), logView);
        }

        /// <summary>
        /// 保存回流记录
        /// </summary>
        /// <param name="models"></param>
        private void SaveSendRecord(List<AliOpenEbillSendRecord> models, BusinessLogModel logView)
        {
            if (models == null || models.Count == 0)
                return;

            try
            {
                _aliOpenEbillSendRecordService.SaveSendRecord(models);

                var _newLogView = logView.ToJson().ToObject<BusinessLogModel>();
                _newLogView.SourceType = "SaveSendRecord";
                _newLogView.SubBusinessType = "保存回流记录";
                _newLogView.Content = $"保存成功";
                CurrentBatchLogs.Add(_newLogView);
            }
            catch (Exception ex)
            {
                var _newLogView = logView.ToJson().ToObject<BusinessLogModel>();
                _newLogView.SourceType = "SaveSendRecord";
                _newLogView.SubBusinessType = "保存回流记录";
                _newLogView.ExceptionMessage = $"保存/更新回流记录失败：{ex}";
                CurrentBatchLogs.Add(_newLogView);

                Log.WriteError($"保存/更新回流记录失败：{ex}", LogFileName);
            }
        }


        /// <summary>
        /// 记录请求接口日志
        /// </summary>
        /// <param name="models"></param>
        private void SaveLog(List<AliSendOpenEbillStatusLog> models, BusinessLogModel logView)
        {
            if (models == null || models.Count == 0)
                return;

            var _newLogView = logView.ToJson().ToObject<BusinessLogModel>();
            _newLogView.SourceType = "SaveLog";
            _newLogView.SubBusinessType = "记录请求接口日志";
            _newLogView.Content = models.ToJson(true);
            CurrentBatchLogs.Add(_newLogView);
        }

        /// <summary>
        /// 是否开启电子面单开通检测
        /// </summary>
        /// <param name="myShopId"></param>
        /// <returns></returns>
        private bool IsEnabledCheckEbillOpenStatus(int myShopId = 0)
        {
            var cloudPlformType = CustomerConfig.CloudPlatformType;
            //只有精选平台才推送该任务
            if (cloudPlformType != PlatformType.Alibaba.ToString())
                return false;

            //var key = $"IsEnabledCheckEbillOpenStatus_{cloudPlformType}";
            //var shopId = myShopId;
            //if(shopId == 0)
            //{
            //    shopId = SiteContext.Current.CurrentShopId;
            //}

            ////按用户获取配置开关
            //var configValue = _commonSettingService.Get(key, shopId);
            //var isEnabledCheckEbillOpenStatus = false;
            //if (configValue != null)
            //{
            //    isEnabledCheckEbillOpenStatus = Convert.ToBoolean(configValue.Value);
            //}
            //是否全局开启
            if (CustomerConfig.IsEnabledCheckEbillOpenStatus)
            {
                return true;
            }

            return false;
        }
    }
}