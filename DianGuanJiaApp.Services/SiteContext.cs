using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Messaging;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Services.Services;

namespace DianGuanJiaApp.Services
{
    /// <summary>
    /// 
    /// </summary>
    [Serializable]
    public class SiteContext : BaseSiteContext
    {
        #region 店铺体系初始化方法

        public SiteContext(int shopId, SiteContextConfig config = null) :base(shopId, config)
        {
            //InitFunctionSetting();
        }

        public SiteContext(Shop shop, SiteContextConfig config = null) : base(shop, config)
        {
            //InitFunctionSetting();
        }

        #endregion

        #region UserFx分单用户体系初始化方法

        public SiteContext(UserFx userFx, SiteContextConfig config = null,SubUserFx subUserFx = null) : base(userFx, config, subUserFx)
        {
        }

        //新增分库
        public SiteContext(UserFx userFx,string dbname, SiteContextConfig config = null,SubUserFx subUserFx = null) : base(userFx,dbname, config,subUserFx)
        {
        }

        /// <summary>
        /// 新增懒加载模式
        /// </summary>
        public SiteContext(LoginAuthToken authToken, string dbname, SiteContextConfig config = null) : base(authToken, dbname, config)
        {
        }

        #endregion

        /// <summary>
        /// 当前站点上下文信息，若未获取到抛出异常
        /// </summary>
        public new static SiteContext Current
        {
            get
            {
                var data = IsAvailableToThread ? CallContext.LogicalGetData(_siteKey) : CallContext.GetData(_siteKey);
                if (data != null)
                    return data as SiteContext;
                else
                    throw new LogicException("无法获取站点信息，请重新登录");
            }
        }

        /// <summary>
        /// 当前站点上下文信息，若未获取到不会抛出异常
        /// </summary>
        public new static SiteContext CurrentNoThrow
        {
            get
            {
                var data = IsAvailableToThread ? CallContext.LogicalGetData(_siteKey) : CallContext.GetData(_siteKey);
                if (data != null)
                    return data as SiteContext;
                return null;
            }
        }


        /// <summary>
        /// 获取店铺购买链接
        /// </summary>
        public override Shop GetShopExpireTime(Shop shop)
        {
            if (string.IsNullOrEmpty(shop.PayUrl))
            {
                //shop.ExpireTime = DateTime.Now.AddDays(30);
                return shop;
            }
            var ps = PlatformService.PlatformFactory.GetPlatformService(shop);
            try
            {
                shop.ExpireTime = ps.GetExpiredTime();
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取店铺【{shop?.Id}】时发生错误：{ex}");
            }
            return shop;
        }

        //protected override void InitAlibabaSystemVersion()
        //{
        //    var shops = AllShops.Where(s => s.PlatformType == PlatformType.Alibaba.ToString()).ToList();
        //    if (shops != null && shops.Any())
        //    {
        //        var appService = new AppOrderListService();
        //        foreach (var shop in shops)
        //        {
        //            var platformVersion = appService.GetAlibabaPlatformVersion(shop.ShopId) ?? "";
        //            var setting = CommonSettingService.GetSystemVersionControlSettings(shop.PlatformType);
        //            var systemVersion = setting?.VersionMappings?.FirstOrDefault(x => x.PlatformVersion == platformVersion)?.SystemVersion;
        //            if(string.IsNullOrEmpty(systemVersion) == false)
        //                shop.SystemVersion = systemVersion;
        //        }
        //    }
        //}


        #region 获取平台类型

        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public override Tuple<User, List<Shop>> GetUserShops(int shopId)
        {
            var us = new UserService();
            var tuple = us.GetUserShops(this.CurrentShopId);
            return tuple;
        }

        #endregion

        #region 数据库策略-静态方法-线程安全

        /// <summary>
        /// 判断店铺订单数据是否转移到MongoDB
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public static bool IsMongoDBConfigured(int shopId)
        {
            return IsMongoDBConfigured(new List<int> { shopId });
        }

        /// <summary>
        /// 判断店铺订单数据是否转移到MongoDB
        /// </summary>
        /// <param name="shopIds">多个店铺ID，其中一个启用，所有的就认为都已启用</param>
        /// <returns></returns>
        public static bool IsMongoDBConfigured(List<int> shopIds)
        {
            if (shopIds == null || !shopIds.Any())
                return false;
            var sr = new ShopRepository();
            var ccs = sr.GetDatabaseConfigs(shopIds);
            if (ccs!=null && ccs.Any(c => c != null && c.DatabaseType == Data.Enum.DatabaseTypeEnum.MongoDB))
                return true;
            return false;
        }

        #endregion

        #region 打单系统配置信息

        //private bool? _isUseOldTheme = null;
        ///// <summary>
        ///// 是否使用的老版主题
        ///// </summary>
        //public bool IsUseOldTheme
        //{
        //    get
        //    {
        //        if (_isUseOldTheme == null)
        //        {
        //            var commonSetting = new CommonSettingService();
        //            _isUseOldTheme = commonSetting.IsUseOldTheme(this.CurrentShopId);
        //        }
        //        return _isUseOldTheme.Value;
        //        //return true;
        //    }
        //}
        ///// <summary>
        ///// 功能配置信息，用于判断用户是否参与内测某些功能
        ///// </summary>
        //public FunctionSettingModel FunctionSetting { get; private set; }
        ///// <summary>
        ///// 初始化功能设置（缓存15分钟）
        ///// </summary>
        //private void InitFunctionSetting()
        //{
        //    FunctionSetting = new FunctionSettingModel();
        //    var rp = new CommonSettingService();
        //    FunctionSetting = rp.GetFunctionSetting(FunctionSetting, this.CurrentShopId);
        //}

        #endregion

        #region 店铺相关的信息
        public List<Shop> AllShopsWithMasterShop
        {
            get
            {
                var shops = new List<Shop>();
                shops.Add(CurrentLoginShop);
                if (ChildShop != null && ChildShop.Any())
                {
                    shops.AddRange(ChildShop);
                }
                if (MasterShop != null)
                {
                    if (SiblingShop != null && SiblingShop.Any())
                    {
                        shops.AddRange(SiblingShop);
                    }
                    shops.Add(MasterShop);
                    //shops.Add(Mast);
                }
                if (UserShops != null && UserShops.Any())
                {
                    UserShops.ForEach(x =>
                    {
                        if (!shops.Any(y => y.Id == x.Id))
                            shops.Add(x);
                    });
                }
                return shops;
            }
        }
        #endregion

        #region 分单配置信息
        private bool? _isOldNavStyle = null;
        /// <summary>
        /// 是否使用的旧导航模板
        /// </summary>
        public bool IsOldNavStyle
        {
            get
            {
                //默认全部为新版 2025.06.10
                return false;

                if (_isOldNavStyle == null)
                {
                    //纯铺货用户固定为旧菜单版本2024.08.13
                    var userFlag = SiteContext.Current.CurrentFxUser.UserFlag.ToString2();
                    if (userFlag.Contains("only_listing"))
                        return true;

                    var commonSetting = new CommonSettingService();
                    //1688相关用户固定为旧菜单版本
                    //var fxUserId = this.CurrentFxUserId;
                    //var isShow1688Menu = commonSetting.GetIsShow1688Menu(fxUserId, this.CurrentShopId);
                    //if (isShow1688Menu)
                    //    return true;
                    _isOldNavStyle = commonSetting.Get("/System/Config/Fendan/OldNavStyle", this.CurrentShopId)?.Value == "1";
                }
                return _isOldNavStyle.Value;
            }
        }
        #endregion
                
        /// <summary>
        /// 获取分单系统用户Id(无异常)
        /// </summary>
        public static int GetCurrentFxUserId(int defaultValue = 0)
        {
            int fxUserId = defaultValue;
            if (SiteContext.CurrentNoThrow != null)
            {
                try
                {
                    fxUserId =  SiteContext.CurrentNoThrow.CurrentFxUserId;
                }
                catch (Exception ex) 
                {
                    ///防止未初始化数据导致的异常
                    fxUserId = defaultValue;
                }
                
            }
            return fxUserId;
        }

        /// <summary>
        /// 获取分单系统店铺Id(无异常)
        /// </summary>
        public static int GetCurrentShopId (int defaultValue = 0)
        {
            int currentShopId = defaultValue;
            if (SiteContext.CurrentNoThrow != null)
            {
                try
                {
                    currentShopId = SiteContext.CurrentNoThrow.CurrentShopId;
                }
                catch (Exception ex)
                {
                    ///防止未初始化数据导致的异常
                    currentShopId = defaultValue;
                }

            }
            return currentShopId;
        }
        /// <summary>
        /// 当前登录账号是否为子账号(无异常)
        /// </summary>
        /// <returns></returns>
        public static bool IsSubAccount(bool defaultValue = false)
        {
            bool isSubAccount = defaultValue;
            if (SiteContext.CurrentNoThrow != null)
            {
                try
                {
                    isSubAccount = SiteContext.CurrentNoThrow.SubFxUserId > 0 ? true : false;
                }
                catch (Exception ex)
                {
                    ///防止未初始化数据导致的异常
                    isSubAccount = defaultValue;
                }

            }
            return isSubAccount;
        }

        /// <summary>
        /// 当前登录账号是否为(子账号)超级管理员
        /// </summary>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        public static bool IsSuperAdmin(bool defaultValue = false)
        {
            if (!IsSubAccount())
                return true;
            var postCode = new UserFxPostRelationService().QueryUserFxPostRel(GetCurrentFxUserId(),GetSubFxUserId())?.PostCode;
            return postCode == CustomerConfig.SubAccountAdminPostCode;
        }

        /// <summary>
        /// 获取分单系统子账户Id(无异常)
        /// </summary>
        public static int GetSubFxUserId(int defaultValue = 0)
        {
            int subUserId = defaultValue;
            if (SiteContext.CurrentNoThrow != null)
            {
                try
                {
                    subUserId = SiteContext.CurrentNoThrow.SubFxUserId;
                }
                catch (Exception ex)
                {
                    ///防止未初始化数据导致的异常
                    subUserId = defaultValue;
                }

            }
            return subUserId;
        }

        /// <summary>
        /// 当前用户是否拥有权限
        /// </summary>
        /// <param name="permission"></param>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        public static bool HasPermission(string permission,bool defaultValue = false)
        {
            var has = defaultValue;
            // 当用户是纯铺货用户（UserFlag包含only_listing）时，拥有 ShowSettlePrice 权限也视为拥有 SetProductSettlementPrice 的权限
            if (Current.IsOnlyListingUser && permission == FxPermission.SetProductSettlementPrice)
            {
                permission = FxPermission.ShowSettlePrice;
            }
            if (SiteContext.CurrentNoThrow != null)
            {
                try
                {
                    has = SiteContext.CurrentNoThrow.PermissionTags.Contains(permission);
                }
                catch (Exception ex)
                {
                    // 防止未初始化数据导致的异常
                    has = defaultValue;
                }

            }
            return has;
        }

        /// <summary>
        /// 强制刷新权限
        /// </summary>
        public List<string> RefreshPermission()
        {
            FxCaching.RefeshRemoteCache(FxCachingType.FxUserPermission,
                IsSubAccount() ? $"sub_{SubFxUserId}" : $"all_{CustomerConfig.FxPermissionVersion}");
            LoadPermission(false);
            return PermissionTags; 
        }
    }
}
