using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace DianGuanJiaApp.Services.WaybillService
{
    public class KsWaybillApiService
    {

        public KuaiShouPlatformService _ksPtService;

        public WayBillAuthConfig WaybillAuthConfig { get; private set; }

        private ShopService shopService = new ShopService();

        public KsWaybillApiService(Shop shop)
        {
            if (shop == null)
                throw new LogicException("参数错误,shop不能为null");

            if (shop.PlatformType != PlatformType.KuaiShou.ToString())
                throw new LogicException("快手电子面单只支持快手授权");
            if(shop.ShopExtension == null)
                shop = shopService.GetShopAndShopExtension(shop.Id);
            if (shop.ShopExtension != null)
            {
                shop.AppKey = shop.ShopExtension.AppKey;
                shop.AppSecret = shop.ShopExtension.AppSecret;
                shop.AccessToken = shop.ShopExtension.AccessToken;
                shop.RefreshToken = shop.ShopExtension.RefreshToken;
                shop.LastRefreshTokenTime = shop.ShopExtension.LastRefreshTokenTime;
            }
            _ksPtService = new KuaiShouPlatformService(shop);
            var cainiaoAuthInfo = shop.ToWaybillAuthInfo();
            WaybillAuthConfig = new CommService().CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
        }

        public KsWaybillApiService(WayBillAuthConfig authconfig)
        {
            if (authconfig == null)
                throw new LogicException("参数错误,authconfig不能为null");
            if (authconfig.Types != PlatformType.KuaiShou.ToString())
                throw new LogicException("快手电子面单只支持快手授权");

            Shop shop = null;
            WaybillAuthConfig = authconfig;
            if (authconfig.AuthSourceType == 1)
            {
                shop = SiteContext.CurrentNoThrow?.AllShops?.FirstOrDefault(s => s.Id == authconfig.CaiNiaoAuthInfoId);
                if (shop == null || shop.ShopExtension == null)
                    shop = shopService.GetShopAndShopExtension(authconfig.CaiNiaoAuthInfoId);// shopService.Get(authconfig.CaiNiaoAuthInfoId);
                if (shop.ShopExtension != null)
                {
                    shop.AppKey = shop.ShopExtension.AppKey;
                    shop.AppSecret = shop.ShopExtension.AppSecret;
                    shop.AccessToken = shop.ShopExtension.AccessToken;
                    shop.RefreshToken = shop.ShopExtension.RefreshToken;
                    shop.LastRefreshTokenTime = shop.ShopExtension.LastRefreshTokenTime;
                    // 通过店铺面单授权时需要更新为分销应用的AppKey
                    if (authconfig.AuthSourceType == 1)
                    {
                        authconfig.AppKey = shop.AppKey;
                        authconfig.AppSecret = shop.AppSecret;
                    }
                }
                _ksPtService = new KuaiShouPlatformService(shop);
            }
            else
                throw new LogicException("快手电子面单授权类型不正确，快手电子面单授权应该是店铺授权");
        }

        /// <summary>
        /// 网点列表,为空是取所有
        /// </summary>
        /// <param name="authconfig">授权信息</param>
        /// <param name="cp_code"></param>
        /// <returns></returns>
        public List<BranchAddress> GetBranchAddressList(string cp_code = "")
        {
            var branchList = new List<BranchAddress>();

            var json = _ksPtService.OpenExpressQuery(cp_code);

            JToken jtoken = null;
            try
            {
                jtoken = JsonConvert.DeserializeObject<JToken>(json);
            }
            catch (Exception ex)
            {
                Log.WriteError($"快手电子面单订购接口响应解析报错:{ex.Message}");
                throw new LogicException($"快手电子面单订购接口响应解析报错:{ex.Message}");
            }
            //系统错误
            var result = jtoken.Value<int>("result");
            if (result != 1)
            {
                //系统错误
                var error_msg = jtoken.Value<string>("error_msg");
                var msg = jtoken.Value<string>("msg");
                if (msg.Contains("鉴权失败"))
                {
                    msg = "电子面单账号授权过期，请前往【设置】-》【电子面单账户管理】页面重新授权";
                    throw new LogicException($"订购查询报错：{msg}");
                }
                throw new LogicException($"订购查询报错：{msg},{error_msg} {result}");
            }
            //结果解析
            var jarray = jtoken.Value<JArray>("data");
            if (jarray != null && jarray.Count() > 0)
            {
                jarray.ToList().ForEach(item =>
                {
                    if (item != null)
                        branchList.AddRange(TransferJTokenToBranch(item));
                });
            }

            return branchList;
        }

        private List<BranchAddress> TransferJTokenToBranch(JToken token)
        {
            var branchList = new List<BranchAddress>();

            var cpCode = token.Value<string>("expressCompanyCode"); //公司编码
            var cpType = token.Value<long>("expressCompanyType");//公司类型，枚举值：(1, "直营型"), (2, "加盟型")
            var branchName = token.Value<string>("netSiteName"); //网点名称
            var branchCode = token.Value<string>("netSiteCode"); //网点编码
            var quantity = token.Value<int>("availableQuantity"); //可用余额
            var cancelQuantity = token.Value<int>("cancelQuantity"); //取消数量
            var usedQuantity = token.Value<int>("usedQuantity"); //累计已使用数量（即已产生物流轨迹量）
            var recycledQuantity = token.Value<int>("recycledQuantity"); //回收数量
            var allocatedQuantity = token.Value<int>("allocatedQuantity"); //累计已取号的数量

            var AddressJArray = token.Value<JArray>("senderAddress"); //发货地址

            if (AddressJArray != null && AddressJArray.Count() > 0)
            {
                AddressJArray.ToList().ForEach(addr =>
                {
                    ////开通的服务
                    //var jarray_services = item.Value<JArray>("service_info_cols");
                    ////账号信息
                    //var vas_account_cols = item.Value<JArray>("vas_account_cols");

                    //发货地址
                    var province = addr.Value<string>("provinceName"); //省
                    var city = addr.Value<string>("cityName"); //市
                    var district = addr.Value<string>("districtName"); //区
                    var town = addr.Value<string>("streetName"); //街道
                    var detail = addr.Value<string>("detailAddress"); //详细地址

                    var branch = new BranchAddress();
                    branch.AuthType = PlatformType.KuaiShou.ToString(); //网点是什么类型的授权账号
                    branch.UserId = this.WaybillAuthConfig.UserId;
                    branch.BranchCode = branchCode;
                    branch.BranchName = branchName;
                    branch.Quantity = quantity;
                    branch.CancelQuantity = cancelQuantity;
                    branch.PrintQuantity = allocatedQuantity;
                    branch.Province = province;
                    branch.City = city;
                    branch.Area = district;
                    branch.Town = town;
                    branch.Detail = detail;
                    branch.CpCode = cpCode;
                    //branch.CpType = cpType;
                    branch.CpType = GetCpType(cpCode, cpType);//物流服务商业务类型 1：直营  2：加盟 3：落地配 4：直营带网点

                    branch.AuthInfoId = this.WaybillAuthConfig.CaiNiaoAuthInfoId;
                    branch.AuthSourceType = this.WaybillAuthConfig.AuthSourceType;

                    branch.ServiceInfoCols = new List<ServiceInfoDtoDomain>();
                    ////遍历开通的服务
                    //if (jarray_services != null && jarray_services.Count() > 0)
                    //{
                    //    jarray_services.ToList().ForEach(svc =>
                    //    {
                    //        ServiceInfoDtoDomain service = new ServiceInfoDtoDomain();

                    //        service.Required = svc.Value<bool>("required");
                    //        service.ServiceCode = svc.Value<string>("service_code");
                    //        service.ServiceName = svc.Value<string>("service_name");
                    //        service.ServiceDesc = svc.Value<string>("service_desc");

                    //        service.ServiceAttributes = new List<ServiceAttributeDtoDomain>();
                    //        //遍历服务属性
                    //        var jarray_attrs = svc.Value<JArray>("service_attributes");
                    //        if (jarray_attrs != null && jarray_attrs.Count() > 0)
                    //        {
                    //            jarray_attrs.ToList().ForEach(attr =>
                    //            {
                    //                var serviceAttr = new ServiceAttributeDtoDomain();
                    //                serviceAttr.AttributeCode = attr.Value<string>("attribute_code");
                    //                serviceAttr.AttributeName = attr.Value<string>("attribute_name");
                    //                serviceAttr.AttributeType = attr.Value<string>("attribute_type");
                    //                serviceAttr.TypeDesc = attr.Value<string>("type_desc");

                    //                service.ServiceAttributes.Add(serviceAttr);
                    //            });
                    //        }

                    //        branch.ServiceInfoCols.Add(service);

                    //    });
                    //}
                    branchList.Add(branch);
                });
            }
            return branchList;
        }


        /// <summary>
        /// 物流服务商业务类型 1：直营  2：加盟 3：落地配 4：直营带网点
        /// </summary>
        /// <param name="cpCode"></param>
        /// <param name="cpTypeVal"></param>
        /// <returns></returns>
        private long GetCpType(string cpCode, long cpTypeVal)
        {
            if (cpCode?.ToUpper() == "SHUNXIN")
                return 1; //顺心捷达无库存，余额为0：ISV对接时需注意去除余额为0的校验
            return cpTypeVal;
        }


        /// <summary>
        /// TemplateType to PddTemplateType
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static int GetKuaiShouTemplateType(int templateType)
        {
            //枚举值：(1, "快递标准面单(二联单)"), (2, "快递三联面单"), (3, "快递便携式三联单"), (4, "快运标准面单"), (5, "快运三联面单"), (6, "快递一联单")
            var urltype = 1;
            switch (templateType)
            {
                case 111:
                    urltype = 1; //快手 快递标准面单(二联单)
                    break;
                case 116:
                    urltype = 6; //快手 快递一联单
                    break;
                case 114:
                    urltype = 4; //快手 快运标准面单（二联单）
                    break;
            }
            return urltype;
        }

        /// <summary>
        /// 获取标准电子面单模板
        /// </summary>
        /// <param name="cp_code"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public Tuple<long, string> GetStdtemplatesId(string cp_code, int type)
        {
            var json = _ksPtService.OpenExpressStandardTemplateList(cp_code);

            JToken jtoken = null;
            try
            {
                jtoken = JsonConvert.DeserializeObject<JToken>(json);
            }
            catch (Exception ex)
            {
                Log.WriteError($"快手标准面单接口响应解析报错:{ex.Message}");
                throw new LogicException($"快手标准面单接口响应解析报错:{ex.Message}");
            }

            //系统错误
            var result = jtoken.Value<int>("result");
            if (result != 1)
            {
                //系统错误
                var error_msg = jtoken.Value<string>("error_msg");
                throw new LogicException($"快手标准面单查询报错code:{result},error:{error_msg}");
            }

            var jarray = jtoken?.Value<JArray>("data");
            if (jarray != null && jarray.Count() > 0)
            {
                foreach (var item in jarray.ToList())
                {
                    if (item != null)
                    {
                        var wp_code = item.Value<string>("expressCompanyCode");
                        var waybillType = item.Value<int>("waybillType");
                        var templateName = item.Value<string>("templateName");
                        //拿一联单就只拿一联单的模板url,其他的过滤
                        if (wp_code == cp_code && type == waybillType && cp_code == "FENGWANG" && type == 6 && templateName.Contains("一联单") == false)
                        {
                            continue;
                        }
                        if (wp_code == cp_code && type == waybillType)
                        {
                            var standard_template_url = item.Value<string>("templateUrl");
                            return new Tuple<long, string>(waybillType, standard_template_url);
                        }
                    }
                }
            }

            throw new LogicException($"快递【{cp_code}】未找到对应快手云模板");
        }

        /// <summary>
        /// 取消PDD电子面单
        /// </summary>
        /// <param name="authconfig"></param>
        /// <param name="cp_code"></param>
        /// <param name="waybillCode"></param>
        /// <returns></returns>
        public Tuple<bool, string> CancelWaybillCode(string cp_code, string waybillCode)
        {
            var json = _ksPtService.OpenExpressEbillCancel(cp_code, waybillCode);

            JToken jtoken = null;
            try
            {
                jtoken = JsonConvert.DeserializeObject<JToken>(json);
            }
            catch (Exception ex)
            {
                Log.WriteError($"快手电子面单取消结果解析报错:{ex.Message}");
                return new Tuple<bool, string>(false, $"取消结果解析报错:{json}");
            }
            var result = jtoken.Value<int>("result");
            var error_msg = jtoken.Value<string>("error_msg");
            return new Tuple<bool, string>(result == 1, error_msg);
        }

        /// <summary>
        /// 取号
        /// </summary>
        /// <returns></returns>
        public GetWaybillResponse GetWaybillCode(List<GetEbillOrderRequest> requests)
        {
            var apiJson = _ksPtService.OpenExpressEbillGet(requests);
            var apiResult = GetWaybillResult(apiJson);
            return apiResult;
        }

        private GetWaybillResponse GetWaybillResult(string json)
        {
            var rstModel = new GetWaybillResponse()
            {
                data = new List<GetEbillOrderResponse>()
            };

            JToken jtoken = null;
            try
            {
                jtoken = JsonConvert.DeserializeObject<JToken>(json);
            }
            catch (Exception ex)
            {
                Log.WriteError($"快手电子面单取号结果解析报错:{ex.Message}");
                //接口结果解析报错
                rstModel.result = 500;
                rstModel.error_msg = $"取号结果解析报错：{json}";
                return rstModel;
            }

            var result = jtoken.Value<int>("result");
            rstModel.result = result;
            if (result != 1)
            {
                //系统错误
                var error_msg = jtoken.Value<string>("error_msg");
                rstModel.error_msg = error_msg;
                return rstModel;
            }

            //解析数据
            var jarray = jtoken.Value<JArray>("data");
            if (jarray != null && jarray.Count() > 0)
            {
                foreach (var item in jarray)
                {
                    if (item == null)
                        continue;
                    var baseResponse = item.Value<JToken>("baseResponse"); //取号是否成功
                    var dataJArray = item.Value<JArray>("data"); //取号结果
                    var model = new GetEbillOrderResponse()
                    {
                        data = new List<GetEbillOrderDTO>(),
                        requestId = item.Value<string>("requestId")
                    };

                    if (baseResponse != null)
                        model.baseResponse = new KwaishopScmEbillBaseResponse()
                        {
                            result = baseResponse.Value<int>("result"),
                            success = baseResponse.Value<bool>("success"),
                            message = baseResponse.Value<string>("message")
                        };

                    if (dataJArray != null && dataJArray.Any())
                        foreach (var data in dataJArray)
                        {
                            if (data == null)
                                continue;

                            var waybillResult = new GetEbillOrderDTO()
                            {
                                waybillCode = data.Value<string>("waybillCode"),
                                parentWaybillCode = data.Value<string>("parentWaybillCode"),
                                printData = data.Value<string>("printData"),
                                signature = data.Value<string>("signature"),
                                version = data.Value<string>("version"),
                                key = data.Value<string>("key"),
                            };
                            model.data.Add(waybillResult);
                        }

                    rstModel.data.Add(model);
                }
            }
            return rstModel;
        }

        /// <summary>
        /// 更新面单
        /// </summary>
        /// <returns></returns>
        public UpdateWaybillResponse UpdateWaybillCode(GetEbillOrderRequest request)
        {
            var apiJson = _ksPtService.OpenExpressEbillUpdate(request);
            var apiResult = GetUpdateWaybillResult(apiJson);
            return apiResult;
        }

        private UpdateWaybillResponse GetUpdateWaybillResult(string json)
        {
            var rstModel = new UpdateWaybillResponse()
            {
                data = new List<GetEbillOrderDTO>(),
            };

            JToken jtoken = null;
            try
            {
                jtoken = JsonConvert.DeserializeObject<JToken>(json);
            }
            catch (Exception ex)
            {
                Log.WriteError($"快手电子面单更新结果解析报错:{ex.Message}");
                //接口结果解析报错
                rstModel.result = 500;
                rstModel.error_msg = $"面单更新结果解析报错：{json}";
                return rstModel;
            }

            var result = jtoken.Value<int>("result");
            rstModel.result = result;
            if (result != 1)
            {
                //系统错误
                var error_msg = jtoken.Value<string>("error_msg");
                rstModel.error_msg = error_msg;
                return rstModel;
            }

            //解析数据
            var jarray = jtoken.Value<JArray>("data");
            if (jarray != null && jarray.Count() > 0)
            {
                foreach (var item in jarray)
                {
                    if (item == null)
                        continue;

                    var model = new GetEbillOrderDTO()
                    {
                        waybillCode = item.Value<string>("waybillCode"),
                        parentWaybillCode = item.Value<string>("parentWaybillCode"),
                        printData = item.Value<string>("printData"),
                        signature = item.Value<string>("signature"),
                        version = item.Value<string>("version"),
                        key = item.Value<string>("key"),
                    };

                    rstModel.data.Add(model);
                }
            }
            return rstModel;
        }

        /// <summary>
        /// 生成自定义区域模板布局Xml
        /// </summary>
        /// <param name="printContorlList"></param>
        /// <returns></returns>
        public static string PrintControlsToCustomAreaTemplate(List<PrintControl> printContorlList, int templateType, string exCode = "")
        {
            string OuterXml = string.Empty;

            XmlDocument xmldoc = new XmlDocument();
            //XmlDeclaration xmldecl = xmldoc.CreateXmlDeclaration("1.0", "UTF-8", null);
            //xmldoc.AppendChild(xmldecl);

            var areaSize = GetCustomerAreaSize(templateType, exCode);//宽、高、top
            var layoutTop = areaSize.Item3; //
            XmlElement layoutRoot = xmldoc.CreateElement("", "layout", "");
            layoutRoot.SetAttribute("id", "CUSTOM_AREA");
            layoutRoot.SetAttribute("width", areaSize.Item1.ToString());
            layoutRoot.SetAttribute("height", areaSize.Item2.ToString());
            layoutRoot.SetAttribute("left", "0");
            layoutRoot.SetAttribute("top", layoutTop.ToString());
            xmldoc.AppendChild(layoutRoot);

            var i = 0;
            foreach (var item in printContorlList)
            {
                i++;

                string fontFamily = "";
                string fontSize = item.FontSize > 1 ? item.FontSize.ToString() : "auto"; //字体、线宽、形状边框宽度默认单位是磅（pt），可以省略不写，如 fontSize:8 或者 fontSize:8pt，一期仅支持使用磅值。
                string fontWeight = "";

                if (item.FontWeight == true)
                    fontWeight = "bold";
                else
                    fontWeight = "normal";

                if (item.FontFamily.ToLower() == "simhei")
                    fontFamily = "黑体";
                else
                    fontFamily = "宋体";


                double left = CommUtls.PxToMm(item.XOffset);
                double top = CommUtls.PxToMm(item.YOffset) - layoutTop;
                double width = CommUtls.PxToMm(item.ControlWidth);
                double height = CommUtls.PxToMm(item.ControlHeight);

                //XmlElement layout = xmldoc.CreateElement("", "layout", "");
                //layout.SetAttribute("id", i.ToString());
                //layout.SetAttribute("left", left.ToString("F2"));
                //layout.SetAttribute("top", (top).ToString("F2"));
                //layout.SetAttribute("width", width.ToString("F2"));
                //layout.SetAttribute("height", height.ToString("F2"));

                XmlElement text = xmldoc.CreateElement("", "text", "");
                //text.SetAttribute("style", "fontFamily:" + fontFamily + ";fontSize:" + fontSize + ";fontWeight:" + fontWeight + "");
                //text.InnerText = "<![CDATA[<%=_data." + item.ControlId + "%>]]>";


                //text.SetAttribute("name", item.ControlId);

                text.SetAttribute("left", left < 0 ? "0" : left.ToString("F0"));
                text.SetAttribute("top", top < 0 ? "0" : top.ToString("F0"));
                text.SetAttribute("width", width < 0 ? "10" : width.ToString("F0"));
                text.SetAttribute("height", height < 0 ? "5" : height.ToString("F0"));

                var styleVal = string.Empty;
                //text.SetAttribute("fontFamily", fontFamily);
                styleVal += $"fontFamily:{fontFamily};"; // 字体类型，宋体SimSun 黑体SimHei 楷体 KaiTi 仿宋 FangSong，中文 / 英文都支持

                //text.SetAttribute("fontWeight", fontWeight);
                styleVal += $"fontWeight:{fontWeight};"; // 字体粗细，取值：normal 表示正常，bold表示加粗

                if (!string.IsNullOrWhiteSpace(fontSize))
                {
                    //text.SetAttribute("fontSize", fontSize);
                    styleVal += $"fontSize:{fontSize};"; //字体大小，如fontSize:8。默认单位pt，可省略；支持自适应字体大小，如fontSize:auto。
                }
                if (item.ControlId == "watermark")
                {
                    //text.SetAttribute("Alpha", "600");
                    styleVal += $"alpha:{180};"; //透明度，数值0~255，255为完全不透明，0为完全透明
                }
                text.SetAttribute("style", styleVal);

                XmlCDataSection cd = xmldoc.CreateCDataSection($"<%=_data.{item.ControlId}%>");
                text.AppendChild(cd);

                //layout.AppendChild(text);
                layoutRoot.AppendChild(text);
            }

            OuterXml = xmldoc.OuterXml;
            OuterXml = OuterXml.Replace("&lt;", "<").Replace("&gt;", ">");

            return OuterXml;
        }

        /// <summary>
        /// 获取自定义区域的 宽、高、top
        /// </summary>
        /// <param name="tempalteType"></param>
        /// <param name="exCode"></param>
        /// <returns></returns>
        private static Tuple<int, int, int> GetCustomerAreaSize(int tempalteType, string exCode)
        {
            Tuple<int, int, int> size = null; //宽、高、top
            switch (tempalteType)
            {
                case 116: //一联
                    size = new Tuple<int, int, int>(76, 50, 80);//宽、高、top
                    break;
                default: //标准二连
                    if (exCode == "DBKD")
                    {
                        size = new Tuple<int, int, int>(100, 30, 142);//宽、高、top
                    }
                    else
                    {
                        size = new Tuple<int, int, int>(100, 30, 150);//宽、高、top
                    }
                    break;
            }
            return size;
        }

        /// <summary>
        /// 获取订单通道
        /// </summary>
        /// <param name="orderPlatform"></param>
        /// <returns></returns>
        public static string GetOrderChannel(string orderPlatform)
        {
            var supportChannel = new Dictionary<string, string>() {
                {"Alibaba","1688" },
                {"AlibabaC2M","TB" },
                {"TaobaoMaiCaiV2","TB" },
				{"AlibabaUfuwu","1688" },
                {"AlibabaZhiBo","1688" },
                {"AlibabaZhuKe","1688" },
                {"BeiBei","BEI_BEI" },
                {"DuXiaoDian","OTHERS" },
                {"Jingdong","JD" },
                {"KuaiShou","KUAI_SHOU" },
                {"MengTui","OTHERS" },
                {"MoGuJie","MGJ" },
                {"MoKuai","OTHERS" },
                {"Offline","OTHERS" },
                {"OpenV1","OTHERS" },
                {"Pinduoduo","PIN_DUO_DUO" },
                {"Suning","SN" },
                {"Supplier","OTHERS" },
                {"System","OTHERS" },
                {"Taobao","TB" },
                {"TouTiao","DOU_YIN" },
                {"TouTiaoFenFa","DOU_YIN" },
                {"TuanHaoHuo","MTShanGou" },
                {"VipShop","WPH" },
                {"Virtual","OTHERS" },
                {"WeiDian","WEI_DIAN" },
                {"WeiMeng","WEI_MENG" },
                {"WxXiaoShangDian","WEI_DIAN" },
                {"WxVideo","WEI_DIAN" },
                {"XiaoDian","DOU_YIN" },
                {"XiaoHongShu","XIAO_HONG_SHU" },
                {"YouZan","YOU_ZAN" },
                {"YunJi","YUN_JI" },
                {"ZhiDian","DOU_YIN" },
            };

            if (supportChannel.Keys.Contains(orderPlatform))
                return supportChannel[orderPlatform];

            return "OTHERS";
        }
    }
}
