using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Nest;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace DianGuanJiaApp.Services.WaybillService
{
    public class WxVideoWaybillApiService
    {
        public WxVideoPlatformService _wxVideoPtService;
        public WayBillAuthConfig WaybillAuthConfig { get; private set; }
        private ShopService shopService = new ShopService();
        public Shop _shop = null;
        public WxVideoWaybillApiService(Shop shop)
        {
            if (shop == null)
                throw new LogicException("参数错误,shop不能为null");

            if (shop.PlatformType != PlatformType.WxVideo.ToString())
                throw new LogicException("视频号电子面单只支持微信视频号授权");
            if (shop.ShopExtension == null)
                shop = shopService.GetShopAndShopExtension(shop.Id);
            if (shop.ShopExtension != null)
            {
                shop.AppKey = shop.ShopExtension.AppKey;
                shop.AppSecret = shop.ShopExtension.AppSecret;
                shop.AccessToken = shop.ShopExtension.AccessToken;
                shop.RefreshToken = shop.ShopExtension.RefreshToken;
                shop.LastRefreshTokenTime = shop.ShopExtension.LastRefreshTokenTime;
            }
            _wxVideoPtService = new WxVideoPlatformService(shop);
            var cainiaoAuthInfo = shop.ToWaybillAuthInfo();
            WaybillAuthConfig = new CommService().CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
        }

        public WxVideoWaybillApiService(WayBillAuthConfig authconfig)
        {
            if (authconfig == null)
                throw new LogicException("参数错误,authconfig不能为null");
            if (authconfig.Types != PlatformType.WxVideo.ToString())
                throw new LogicException("视频号电子面单只支持视频号授权");

            Shop shop = null;
            WaybillAuthConfig = authconfig;
            if (authconfig.AuthSourceType == 1)
            {
                shop = SiteContext.CurrentNoThrow?.AllShops?.FirstOrDefault(s => s.Id == authconfig.CaiNiaoAuthInfoId);
                if (shop == null || shop.ShopExtension == null)
                    shop = shopService.GetShopAndShopExtension(authconfig.CaiNiaoAuthInfoId);
                if (shop.ShopExtension != null)
                {
                    shop.AppKey = shop.ShopExtension.AppKey;
                    shop.AppSecret = shop.ShopExtension.AppSecret;
                    shop.AccessToken = shop.ShopExtension.AccessToken;
                    shop.RefreshToken = shop.ShopExtension.RefreshToken;
                    shop.LastRefreshTokenTime = shop.ShopExtension.LastRefreshTokenTime;
                    // 通过店铺面单授权时需要更新为分销应用的AppKey
                    if (authconfig.AuthSourceType == 1)
                    {
                        authconfig.AppKey = shop.AppKey;
                        authconfig.AppSecret = shop.AppSecret;
                    }
                }
                _wxVideoPtService = new WxVideoPlatformService(shop);
            }
            else
                throw new LogicException("视频号电子面单授权类型不正确，视频号电子面单授权应该是店铺授权");
        }

        /// <summary>
        /// 网点列表,为空是取所有
        /// </summary>
        /// <param name="authconfig">授权信息</param>
        /// <param name="cp_code"></param>
        /// <returns></returns>
        public List<BranchAddress> GetBranchAddressList(string cp_code = "")
        {

            var branchList = new List<BranchAddress>();
            var jtoken = _wxVideoPtService.EwaybillAccountGet(cp_code);
            var jarray = jtoken.Value<JArray>("account_list");
            if (jarray != null && jarray.Count() > 0)
            {
                jarray.ToList().ForEach(item =>
                {
                    if (item != null)
                        branchList.AddRange(TransferJTokenToBranch(item));
                });
            }
            return branchList;
        }
        private List<BranchAddress> TransferJTokenToBranch(JToken token)
        {
            var branchList = new List<BranchAddress>();
            //acct_id  店铺id
            //shop_id  网点id 
            var status = token.Value<int>("status"); //1绑定审核中 2取消绑定审核中 3已绑定 4已解除绑定 5绑定未通过 6取消绑定未通过
            if (status != 3) return branchList;
            var cpCode = token.Value<string>("delivery_id"); //公司编码
            var cpType = token.Value<int>("company_type");
            // 接口：快递公司类型 1 加盟型，2 直营型
            // 系统：类型 1：直营 2：加盟 3：落地配 4：直营带网点 5:“统采”(例如：淘特官方快递）
            if (cpType == 1) cpType = 2;
            else if (cpType == 2) cpType = 1;

            var ebShopId = token.Value<string>("shop_id");  //面单店铺Id ，取号需传
            var ebAcctId = token.Value<string>("acct_id");  //面单Id，取号需传
            var acctType = token.Value<int>("acct_type");  //面单账号类型 0：普通账号 1：共享账号

            var quantity = token.Value<int>("available"); //可用余额
            var cancelQuantity = token.Value<int>("cancel"); //取消数量
            var allocatedQuantity = token.Value<int>("allocated"); //累计已使用数量（即已产生物流轨迹量）
            var recycledQuantity = token.Value<int>("recycled"); //回收数量

            //var brandCode = token.Value<string>("brandCode");//品牌编码
            //var customerCode = token.Value<string>("customerCode");//品牌编码
            //if (branchCode.IsNullOrEmpty()) branchCode = brandCode; // 调试 直营的 不会返回网点编码，去品牌编码
            var siteInfo = token?.Value<JToken>("site_info");
            //if(siteInfo == null) 
            //    return branchList;
            var share = token?.Value<JToken>("share"); //共享账号发起方信息，acct_type 为共享账号时有效

            var siteStatus = siteInfo?.Value<int>("site_status");//网点状态, 1 表示正常，其余不正常
            if (siteStatus != null && siteStatus != 1)
                return branchList;
            var address = siteInfo?.Value<JToken>("address");
            var branchName = siteInfo?.Value<string>("site_name"); //网点名称
            var branchCode = siteInfo?.Value<string>("site_code"); //网点编码
            //发货地址
            var province = address?.Value<string>("province_name"); //省
            var city = address?.Value<string>("city_name"); //市
            var district = address?.Value<string>("district_name"); //区
            var town = address?.Value<string>("street_name"); //街道
            var detail = address?.Value<string>("detail_address"); //详细地址

            //直营快递
            if (siteInfo == null)
            {
                var monthly_card = token?.Value<string>("monthly_card");    //月结号
                var sender_address = token?.Value<JToken>("sender_address");
                province = sender_address?.Value<string>("province"); //省
                city = sender_address?.Value<string>("city"); //市
                district = sender_address?.Value<string>("county"); //区
                town = sender_address?.Value<string>("street"); //街道
                detail = sender_address?.Value<string>("address"); //详细地址
            }

            var shareNickName = "";
            var shareAcctId = "";
            var shareId = "";
            var shareShopId = "";
            //共享账号
            if (acctType == 1 && share != null)
            {
                shareNickName = share.Value<string>("nickname"); //发起共享方店铺名
                shareAcctId = share.Value<string>("acct_id"); //电子面单账号id
                shareId = share.Value<string>("share_id"); //共享id
                shareShopId = share.Value<string>("shop_id"); //店铺id，全局唯一，一个店铺分配一个shop_id
            }


            var branch = new BranchAddress();
            branch.AuthType = PlatformType.WxVideo.ToString(); //网点是什么类型的授权账号
            branch.UserId = this.WaybillAuthConfig.UserId;
            branch.BranchCode = branchCode;
            branch.BranchName = branchName;
            branch.Quantity = quantity;
            branch.CancelQuantity = cancelQuantity;
            branch.PrintQuantity = allocatedQuantity;
            branch.Province = province;
            branch.City = city;
            branch.Area = district;
            branch.Town = town;
            branch.Detail = detail;
            branch.CpCode = cpCode;
            branch.CpType = cpType;
            branch.EwayBillShopId = ebShopId;
            branch.EwayBillAcctId = ebAcctId;
            branch.NickName = shareNickName;
            //branch.SettlementCode = customerCode;
            //branch.BrandCode = brandCode;

            branch.AuthInfoId = this.WaybillAuthConfig.CaiNiaoAuthInfoId;
            branch.AuthAccountName = this.WaybillAuthConfig.UserName;
            branch.AuthSourceType = this.WaybillAuthConfig.AuthSourceType;

            branch.ServiceInfoCols = new List<ServiceInfoDtoDomain>();
            branchList.Add(branch);
            return branchList;
        }

        public Tuple<bool, string, string, List<dynamic>> GetWaybillCode(WxVideoCreateEbillOrderRequest request)
        {
            try
            {
                var apiJson = _wxVideoPtService.EwaybillOrderCreate(request);
                return new Tuple<bool, string, string, List<dynamic>>(true, apiJson.Item1, apiJson.Item2,apiJson.Item3);
            }
            catch (Exception ex)
            {
                Log.WriteError($"微信视频号电子面单取号报错:{ex.Message}", "WxVideoWayBill.txt");
                return new Tuple<bool, string, string, List<dynamic>>(false, $"{ex.Message}", "",new List<dynamic>());
            }
        }
        public Tuple<bool, string> GetWaybillCodePrecreate(WxVideoCreateEbillOrderRequest request)
        {
            try
            {
                var apiJson = _wxVideoPtService.EwaybillOrderPrecreate(request);
                return new Tuple<bool, string>(true, apiJson);
            }
            catch (Exception ex)
            {
                Log.WriteError($"微信视频号电子面单预取号报错:{ex.Message}", "WxVideoWayBill.txt");
                return new Tuple<bool, string>(false, $"{ex.Message}");
            }
        }
        /// <summary>
        /// 取消视频号电子面单
        /// </summary>
        /// <param name="ewaybillOrderId">预取号的电子面单订单号</param>
        /// <param name="cp_code">快递公司</param>
        /// <param name="waybillCode">运单号</param>
        /// <returns></returns>
        public Tuple<bool, string> CancelWaybillCode(string ewaybillOrderId, string cp_code, string waybillCode)
        {
            try
            {
                var result = _wxVideoPtService.EwaybillOrderCancel(ewaybillOrderId, cp_code, waybillCode);
                return new Tuple<bool, string>(true, "");
            }
            catch (Exception ex)
            {
                Log.WriteError($"微信视频号电子面单取消报错:{ex.Message}", "WxVideoWayBill.txt");
                return new Tuple<bool, string>(false, $"{ex.Message}");
            }
        }
        public Tuple<bool, string> GetPrintConfigInfo(string ewaybillOrderId, string template_id, PrintNum print, string sub_waybill_id = "")
        {
            try
            {
                var apiJson = _wxVideoPtService.GetPrintConfigInfo(ewaybillOrderId, template_id, print, sub_waybill_id);
                return new Tuple<bool, string>(true, apiJson);
            }
            catch (Exception ex)
            {
                Log.WriteError($"微信视频号电子面单获取打印内容报错:{ex.Message}", "WxVideoWayBill.txt");
                return new Tuple<bool, string>(false, $"{ex.Message}");
            }
        }

        //public static string GetWxVideoTemplateType(int templateType)
        //{
        //    //枚举值：
        //    var urltype = "76*130";
        //    //switch (templateType)
        //    //{
        //    //    case 150:
        //    //        urltype = "76*130";  //小红书 快递一联单
        //    //        break;
        //    //    case 151:
        //    //        urltype = "100*150";  //小红书 快递二联单
        //    //        break;
        //    //    case 152:
        //    //        urltype = "100*180"; //小红书 快递三联面单
        //    //        break;
        //    //}
        //    return urltype;
        //}
        public string GetStdtemplatesId(string cp_code, string ewaybillTemplateId, decimal height = 130)
        {
            var best_template_id = ewaybillTemplateId;
            try
            {
                var resultArr = _wxVideoPtService.EwaybillTemplateGet(cp_code);
                if (resultArr != null && resultArr.Count > 0)
                {
                    foreach (var item in resultArr)
                    {
                        //先取默认的id，再找到对应模板
                        best_template_id = item.Value<string>("default_template_id");
                        var templatelist = item.Value<JArray>("template_list");
                        if (templatelist != null && templatelist.Count > 0)
                        {
                            foreach (var it in templatelist)
                            {
                                var template_id = it.Value<string>("template_id");
                                var template_name = it.Value<string>("template_name");
                                if (template_id == ewaybillTemplateId)
                                {
                                    best_template_id = it.Value<string>("template_id");
                                    break;
                                }
                            }
                            if (string.IsNullOrWhiteSpace(best_template_id))
                            {
                                var firsttemplate = templatelist.FirstOrDefault();
                                var template_id = firsttemplate.Value<string>("template_id");
                                var template_name = firsttemplate.Value<string>("template_name");
                                best_template_id = template_id;
                            }
                        }
                        
                        if (best_template_id.IsNotNullOrEmpty())
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"微信视频号电子面单获取模板:{ex.Message}", "WxVideoWayBill.txt");
            }
            return best_template_id;
        }
        /// <summary>
        /// 创建商家模板
        /// </summary>
        /// <param name="cpCode"></param>
        /// <param name="templateName"></param>
        /// <returns></returns>
        public bool CreatePrintTemplate(string cpCode, string templateName)
        {
            try
            {
                var request = new WxVideoEbillTemplateRequest()
                {
                    delivery_id = cpCode,
                    info = new WxVideoEbillTemplateInfo()
                    {
                        template_name = templateName,
                        template_desc = templateName,
                        template_type = "single",
                        is_default = true,
                        options = new List<WxVideoEbillTemplateOptionPro>() {
                            new WxVideoEbillTemplateOptionPro{ option_id =0,font_size=14,is_bold=false,is_open=false},
                            new WxVideoEbillTemplateOptionPro{ option_id =1,font_size=14,is_bold=false,is_open=false},
                            new WxVideoEbillTemplateOptionPro{ option_id =2,font_size=14,is_bold=false,is_open=true},
                            new WxVideoEbillTemplateOptionPro{ option_id =3,font_size=14,is_bold=false,is_open=false},
                            new WxVideoEbillTemplateOptionPro{ option_id =4,font_size=14,is_bold=false,is_open=false},
                            new WxVideoEbillTemplateOptionPro{ option_id =5,font_size=14,is_bold=false,is_open=false},
                            new WxVideoEbillTemplateOptionPro{ option_id =6,font_size=14,is_bold=false,is_open=true},
                            new WxVideoEbillTemplateOptionPro{ option_id =7,font_size=14,is_bold=false,is_open=true}
                        }
                    }
                };
                return _wxVideoPtService.EwaybillTemplateCreate(request);
            }
            catch (Exception ex)
            {
                Log.WriteError($"微信视频号电子面单创建模板:{ex.Message}", "WxVideoWayBill.txt");
                return false;
            }
        }
        /// <summary>
        /// 获取商家模板列表
        /// </summary>
        /// <param name="cpCode"></param>
        /// <returns></returns>
        public List<EwaybillTemplateDomain> GetEwaybillTemplates(string cpCode)
        {
            var ewaybillTemplateDomains = new List<EwaybillTemplateDomain>();

            try
            {
                var resultArr = _wxVideoPtService.EwaybillTemplateGet(cpCode);
                if (resultArr != null && resultArr.Count > 0)
                {
                    foreach (var item in resultArr)
                    {
                        var templatelist = item.Value<JArray>("template_list");
                        if (templatelist != null && templatelist.Count > 0)
                        {
                            foreach (var it in templatelist)
                            {
                                var template_id = it.Value<string>("template_id");
                                var template_name = it.Value<string>("template_name");
                                var is_default = it.Value<bool>("is_default");

                                var templateDomain = new EwaybillTemplateDomain
                                {
                                    Template_Id = template_id,
                                    Template_Name = template_name,
                                    Is_Default = is_default
                                };
                                ewaybillTemplateDomains.Add(templateDomain);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"微信视频号获取商家模板列表:{ex.Message}", "WxVideoWayBill.txt");
            }
            return ewaybillTemplateDomains.OrderByDescending(e => e.Is_Default).ToList();
        }

        /// <summary>
        /// 生成自定义区域模板布局Html
        /// </summary>
        /// <param name="printContorlList"></param>
        /// <returns></returns>
        public static string PrintControlsToCustomAreaTemplate(List<PrintControl> printContorlList, int templateType, string exCode = "")
        {
            /* 自定义模板示例
             *  <div>
                    <div style="font-weight: 700;">
                    店铺名称 {{ shopName }}!
                    </div>
                    {{#productInfo}}
                        <div>{{name}},{{count}},{{code}}</div>
                    {{/productInfo}}
                    <img src="{{imgSrc}}" width="300" height="50"/>
                    <img src="{{productBarcode}}" width="300" height="60" />
                </div>
            */
            string OuterXml = string.Empty;
            XmlDocument xmldoc = new XmlDocument();
            var areaSize = GetCustomerAreaSize(templateType, exCode);//宽、高、top
            var layoutTop = areaSize.Item3; //
            XmlElement divRoot = xmldoc.CreateElement("", "div", "");
            //divRoot.SetAttribute("id", "CUSTOM_AREA");
            //divRoot.SetAttribute("width", areaSize.Item1.ToString());
            //divRoot.SetAttribute("height", areaSize.Item2.ToString());
            //divRoot.SetAttribute("left", "0");
            //divRoot.SetAttribute("top", layoutTop.ToString());
            xmldoc.AppendChild(divRoot);

            var i = 0;
            foreach (var item in printContorlList.OrderBy(x=>x.YOffset))
            {
                i++;

                string fontFamily = item.FontFamily.ToLower() == "simhei" ? "黑体" : "宋体";
                string fontSize = (item.FontSize * 2.64).ToString();//item.FontSize > 1 ? item.FontSize.ToString() : "auto"; //字体、线宽、形状边框宽度默认单位是磅（pt），可以省略不写，如 fontSize:8 或者 fontSize:8pt，一期仅支持使用磅值。
                string fontWeight = item.FontWeight ? "bold": "normal";
                double left = item.XOffset * 2.64 - 50;
                double top = item.YOffset * 2.64 - layoutTop;
                double width = item.ControlWidth * 2.64;
                double height = item.ControlHeight * 2.64;
                XmlElement text_div = xmldoc.CreateElement("", "div", "");

                var styleVal = string.Empty;
                styleVal += $"position: absolute;"; //设置偏移必须
                styleVal += $"left:{left}px;";
                styleVal += $"top:{top}px;";
                styleVal += $"width:{width}px;";
                styleVal += $"height:{height}px;";
                styleVal += $"font-Family:{fontFamily};"; // 字体类型，宋体SimSun 黑体SimHei 楷体 KaiTi 仿宋 FangSong，中文 / 英文都支持
                styleVal += $"font-Weight:{fontWeight};"; // 字体粗细，取值：normal 表示正常，bold表示加粗
                styleVal += "white-space:pre-wrap;";

                if (!string.IsNullOrWhiteSpace(fontSize))
                {
                    styleVal += $"font-Size:{fontSize}px;"; //字体大小，如fontSize:8。默认单位pt，可省略；支持自适应字体大小，如fontSize:auto。
                }
                if (item.ControlId == "watermark")
                {
                    styleVal += $"alpha:{180};"; //透明度，数值0~255，255为完全不透明，0为完全透明
                }
                text_div.SetAttribute("style", styleVal);
                //XmlCDataSection cd = xmldoc.CreateCDataSection("{{"+ item.ControlId + "}}");
                text_div.InnerText = "{{" + item.ControlId + "}}";
                divRoot.AppendChild(text_div);
            }
            OuterXml = xmldoc.OuterXml;
            OuterXml = OuterXml.Replace("&lt;", "<").Replace("&gt;", ">");
            return OuterXml;
        }

        /// <summary>
        /// 获取自定义区域的 宽、高、top
        /// </summary>
        /// <param name="tempalteType"></param>
        /// <param name="exCode"></param>
        /// <returns></returns>
        private static Tuple<int, int, int> GetCustomerAreaSize(int tempalteType, string exCode)
        {
            Tuple<int, int, int> size = null; //宽、高、top
            switch (tempalteType)
            {
                case 160: //一联
                    size = new Tuple<int, int, int>(760, 1300, 890);//宽、高、top
                    break;
                default: //标准二连
                    size = new Tuple<int, int, int>(100, 30, 150);//宽、高、top //待调试，暂无二联单
                    break;
            }
            return size;
        }
    }
}
