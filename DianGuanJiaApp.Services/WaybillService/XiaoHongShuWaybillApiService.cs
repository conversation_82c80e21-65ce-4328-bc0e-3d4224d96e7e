using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace DianGuanJiaApp.Services.WaybillService
{
    public class XiaoHongShuWaybillApiService
    {
        public XiaoHongShuV2PlatformService _xhsPtService;
        public WayBillAuthConfig WaybillAuthConfig { get; private set; }
        private ShopService shopService = new ShopService();
        public Shop _shop = null;
        public XiaoHongShuWaybillApiService(Shop shop)
        {
            if (shop == null)
                throw new LogicException("参数错误,shop不能为null");

            if (shop.PlatformType != PlatformType.XiaoHongShu.ToString())
                throw new LogicException("小红书电子面单只支持小红书授权");
            if (shop.ShopExtension == null)
                shop = shopService.GetShopAndShopExtension(shop.Id);
            if (shop.ShopExtension != null)
            {
                shop.AppKey = shop.ShopExtension.AppKey;
                shop.AppSecret = shop.ShopExtension.AppSecret;
                shop.AccessToken = shop.ShopExtension.AccessToken;
                shop.RefreshToken = shop.ShopExtension.RefreshToken;
                shop.LastRefreshTokenTime = shop.ShopExtension.LastRefreshTokenTime;
            }
            _xhsPtService = new XiaoHongShuV2PlatformService(shop);
            var cainiaoAuthInfo = shop.ToWaybillAuthInfo();
            WaybillAuthConfig = new CommService().CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
        }
        public XiaoHongShuWaybillApiService(WayBillAuthConfig authconfig)
        {
            if (authconfig == null)
                throw new LogicException("参数错误,authconfig不能为null");
            if (authconfig.Types != PlatformType.XiaoHongShu.ToString())
                throw new LogicException("小红书电子面单只支持小红书授权");

            Shop shop = null;
            WaybillAuthConfig = authconfig;
            if (authconfig.AuthSourceType == 1)
            {
                shop = SiteContext.CurrentNoThrow?.AllShops?.FirstOrDefault(s => s.Id == authconfig.CaiNiaoAuthInfoId);
                if (shop == null || shop.ShopExtension == null)
                    shop = shopService.GetShopAndShopExtension(authconfig.CaiNiaoAuthInfoId);// shopService.Get(authconfig.CaiNiaoAuthInfoId);
                if (shop.ShopExtension != null)
                {
                    shop.AppKey = shop.ShopExtension.AppKey;
                    shop.AppSecret = shop.ShopExtension.AppSecret;
                    shop.AccessToken = shop.ShopExtension.AccessToken;
                    shop.RefreshToken = shop.ShopExtension.RefreshToken;
                    shop.LastRefreshTokenTime = shop.ShopExtension.LastRefreshTokenTime;
                    // 通过店铺面单授权时需要更新为分销应用的AppKey
                    if (authconfig.AuthSourceType == 1)
                    {
                        authconfig.AppKey = shop.AppKey;
                        authconfig.AppSecret = shop.AppSecret;
                    }
                }
                _xhsPtService = new XiaoHongShuV2PlatformService(shop);
            }
            else
                throw new LogicException("小红书电子面单授权类型不正确，小红书电子面单授权应该是店铺授权");
        }

        /// <summary>
        /// 获取网点列表，新旧网点同时获取
        /// </summary>
        /// <param name="cp_code"></param>
        /// <returns></returns>
        public List<BranchAddress> GetBranchAddressList(string cp_code = "")
        {

            var branchList = new List<BranchAddress>();
            //旧版电子面单网点，报错不处理
            try
            {
                var oldBranchAddressList = GetBranchAddressListBybillVersion(cp_code, 1);
                if (oldBranchAddressList != null && oldBranchAddressList.Any())
                    branchList.AddRange(oldBranchAddressList);
            }
            catch
            {
            }
            //新版电子面单网点，报错不处理
            try
            {
                var newBranchAddressList = GetBranchAddressListBybillVersion(cp_code, 2);
                if (newBranchAddressList != null && newBranchAddressList.Any())
                    branchList.AddRange(newBranchAddressList);
            }
            catch
            {
            }
            return branchList;
        }
        /// <summary>
        /// 网点列表,billVersion：1-旧面单，2-新面单
        /// </summary>
        /// <param name="authconfig">授权信息</param>
        /// <param name="cp_code"></param>
        /// <returns></returns>
        public List<BranchAddress> GetBranchAddressListBybillVersion(string cp_code = "", int billVersion = 1)
        {

            var branchList = new List<BranchAddress>();
            var json = _xhsPtService.QueryEbillSubscribes(cp_code, billVersion);
            //var json = "{\"error_msg\":null,\"data\":{\"subscribeList\":[{\"cpCode\":\"shunfeng\",\"cpType\":1,\"cpName\":\"顺丰速运\",\"usage\":{\"recycledQuantity\":0,\"quantity\":0,\"allocatedQuantity\":0,\"cancelQuantity\":0},\"customerCode\":\"5570216513\",\"senderAddressList\":[{\"address\":{\"province\":\"安徽省\",\"town\":\"东关街道\",\"city\":\"宿州市\",\"district\":\"埇桥区\",\"detail\":\"青网科技园2期\"},\"phone\":\"18949939299\",\"name\":\"面皮诱惑\"}],\"brandCode\":\"SF\"}]},\"success\":true,\"error_code\":null}";
            JToken jtoken = null;
            try
            {
                jtoken = JsonConvert.DeserializeObject<JToken>(json);
            }
            catch (Exception ex)
            {
                Log.WriteError($"小红书电子面单订购接口响应解析报错:{ex.Message}");
                throw new LogicException($"小红书电子面单订购接口响应解析报错:{ex.Message}");
            }
            //系统错误
            var success = jtoken.Value<bool>("success");
            if (!success)
            {
                //系统错误
                var error_msg = jtoken.Value<string>("error_msg");
                var error_code = jtoken.Value<string>("error_code");
                throw new LogicException($"订购查询报错code:{error_code},error:{error_msg}");
            }

            var data = jtoken.Value<JToken>("data");
            var jarray = data.Value<JArray>("subscribeList");
            if (jarray != null && jarray.Count() > 0)
            {
                jarray.ToList().ForEach(item =>
                {
                    if (item != null)
                        branchList.AddRange(TransferJTokenToBranch(item, billVersion));
                });
            }
            return branchList;
        }
        private List<BranchAddress> TransferJTokenToBranch(JToken token, int billVersion = 1)
        {
            var branchList = new List<BranchAddress>();

            var cpCode = token.Value<string>("cpCode"); //公司编码
            var cpType = token.Value<long>("cpType");//公司类型，枚举值：(1, "直营型"), (2, "加盟型")
            var branchName = token.Value<string>("cpName"); //网点名称
            var branchCode = token.Value<string>("branchCode"); //网点编码
            var usage = token.Value<JToken>("usage");
            var quantity = usage.Value<int>("quantity"); //可用余额
            var cancelQuantity = usage.Value<int>("cancelQuantity"); //取消数量
            var allocatedQuantity = usage.Value<int>("allocatedQuantity"); //累计已使用数量（即已产生物流轨迹量）
            var recycledQuantity = usage.Value<int>("recycledQuantity"); //回收数量
            var brandCode = token.Value<string>("brandCode");//品牌编码
            var customerCode = token.Value<string>("customerCode");//品牌编码

            if (branchCode.IsNullOrEmpty()) branchCode = brandCode; // 调试 直营的 不会返回网点编码，去品牌编码
            var AddressJArray = token.Value<JArray>("senderAddressList"); //发货地址
            if (AddressJArray != null && AddressJArray.Count() > 0)
            {
                AddressJArray.ToList().ForEach(addressInfo =>
                {
                    var addr = addressInfo.Value<JToken>("address");
                    //发货地址
                    var province = addr.Value<string>("province"); //省
                    var city = addr.Value<string>("city"); //市
                    var district = addr.Value<string>("district"); //区
                    var town = addr.Value<string>("town"); //街道
                    var detail = addr.Value<string>("detail"); //详细地址

                    var branch = new BranchAddress();
                    branch.AuthType = PlatformType.XiaoHongShu.ToString(); //网点是什么类型的授权账号
                    branch.UserId = this.WaybillAuthConfig.UserId;
                    branch.BranchCode = branchCode;
                    branch.BranchName = branchName;
                    branch.Quantity = quantity;
                    branch.CancelQuantity = cancelQuantity;
                    branch.PrintQuantity = allocatedQuantity;
                    branch.Province = province;
                    branch.City = city;
                    branch.Area = district;
                    branch.Town = town;
                    branch.Detail = detail;
                    branch.CpCode = cpCode;
                    branch.CpType = cpType;
                    branch.SettlementCode = customerCode;
                    branch.BrandCode = brandCode;

                    branch.AuthInfoId = this.WaybillAuthConfig.CaiNiaoAuthInfoId;
                    branch.AuthAccountName = this.WaybillAuthConfig.UserName;
                    branch.AuthSourceType = this.WaybillAuthConfig.AuthSourceType;

                    branch.ServiceInfoCols = new List<ServiceInfoDtoDomain>();
                    branch.BillVersion = billVersion;
                    branchList.Add(branch);
                });
            }

            return branchList;
        }

        public XiaoHongShuCreateEbillOrderResponse GetWaybillCode(XiaoHongShuCreateEbillOrderRequest request)
        {

            var apiJson = _xhsPtService.CreateEbillOrders(request);
            var apiResult = GetWaybillResult(apiJson, true);
            return apiResult;
        }

        public XiaoHongShuCreateEbillOrderResponse UpdateWaybillCode(XiaoHongShuUpdateEbillOrderRequest request)
        {
            var apiJson = _xhsPtService.UpdateEbillOrder(request);
            var apiResult = GetWaybillResult(apiJson, false);
            return apiResult;
        }
        private XiaoHongShuCreateEbillOrderResponse GetWaybillResult(string json, bool isCreate)
        {
            var rstModel = new XiaoHongShuCreateEbillOrderResponse()
            {
                wayBillList = new List<xhsWayBillItem>()
            };
            JToken jtoken = null;
            try
            {
                jtoken = JsonConvert.DeserializeObject<JToken>(json);
            }
            catch (Exception ex)
            {
                Log.WriteError($"小红书电子面单取号结果解析报错:{ex.Message}");
                //接口结果解析报错
                rstModel.error_code = "500";
                rstModel.error_msg = $"取号结果解析报错：{json}";
                return rstModel;
            }
            var success = jtoken.Value<bool>("success");
            rstModel.success = success;
            if (!success)
            {
                //接口错误
                var error_code = jtoken.Value<string>("error_code");
                var error_msg = jtoken.Value<string>("error_msg");
                rstModel.error_code = error_code;
                rstModel.error_msg = error_msg;
                //应官方要求，取号接口返回此错误码时，展示下方错误信息(250328)
                if (error_code == "4033004")
                {
                    rstModel.error_msg = "旧版电子面单已下线，请使用新版电子面单（旧版剩余面单辛苦联系快递网点进行迁移）「失败错误码：4033004」";
                }
                return rstModel;
            }
            if (isCreate)
            {
                var jarrayData = jtoken.Value<JToken>("data");
                var jarray = jarrayData.Value<JArray>("wayBillList");
                if (jarray != null && jarray.Count() > 0)
                {
                    foreach (var item in jarray)
                    {
                        var strPrintData = item.Value<string>("printData");

                        var printDataJtoken = JsonConvert.DeserializeObject<JToken>(strPrintData);
                        var xhsPrintDate = new xhsWayBillPrintData
                        {
                            encryptedData = printDataJtoken.Value<string>("encryptedData"),
                            signature = printDataJtoken.Value<string>("signature"),
                            templateURL = printDataJtoken.Value<string>("templateURL"),
                            ver = printDataJtoken.Value<string>("ver")
                        };
                        var xhsItem = new xhsWayBillItem()
                        {
                            cpCode = item.Value<string>("cpCode"),
                            waybillCode = item.Value<string>("waybillCode"),
                            customerPrintData = item.Value<string>("customerPrintData"),
                            printData = xhsPrintDate,
                            objectId = item.Value<string>("objectId")
                        };
                        rstModel.wayBillList.Add(xhsItem);
                    }
                }
            }
            else
            {
                var tempData = jtoken.Value<JToken>("data");

                var wayData = tempData.Value<JToken>("waybill");
                if (wayData != null)
                {
                    var strPrintData = wayData.Value<string>("printData");

                    var printDataJtoken = JsonConvert.DeserializeObject<JToken>(strPrintData);
                    var xhsPrintDate = new xhsWayBillPrintData
                    {
                        encryptedData = printDataJtoken.Value<string>("encryptedData"),
                        signature = printDataJtoken.Value<string>("signature"),
                        templateURL = printDataJtoken.Value<string>("templateURL"),
                        ver = printDataJtoken.Value<string>("ver")
                    };
                    var xhsItem = new xhsWayBillItem()
                    {
                        waybillCode = wayData.Value<string>("waybillCode"),
                        customerPrintData = wayData.Value<string>("customerPrintData"),
                        printData = xhsPrintDate
                    };
                    rstModel.wayBillList.Add(xhsItem);
                }
            }

            return rstModel;
        }


        /// <summary>
        /// 生成自定义区域模板布局Xml
        /// </summary>
        /// <param name="printContorlList"></param>
        /// <returns></returns>
        public static string PrintControlsToCustomAreaTemplate(List<PrintControl> printContorlList, int templateType, string exCode = "")
        {
            string OuterXml = string.Empty;

            XmlDocument xmldoc = new XmlDocument();
            //XmlDeclaration xmldecl = xmldoc.CreateXmlDeclaration("1.0", "UTF-8", null);
            //xmldoc.AppendChild(xmldecl);

            var areaSize = GetCustomerAreaSize(templateType, exCode);//宽、高、top
            var layoutTop = areaSize.Item3; //
            XmlElement layoutRoot = xmldoc.CreateElement("", "layout", "");
            layoutRoot.SetAttribute("id", "CUSTOM_AREA");
            layoutRoot.SetAttribute("width", areaSize.Item1.ToString());
            layoutRoot.SetAttribute("height", areaSize.Item2.ToString());
            layoutRoot.SetAttribute("left", "0");
            layoutRoot.SetAttribute("top", layoutTop.ToString());
            xmldoc.AppendChild(layoutRoot);

            var i = 0;
            foreach (var item in printContorlList)
            {
                i++;

                string fontFamily = "";
                string fontSize = item.FontSize > 1 ? item.FontSize.ToString() : "auto"; //字体、线宽、形状边框宽度默认单位是磅（pt），可以省略不写，如 fontSize:8 或者 fontSize:8pt，一期仅支持使用磅值。
                string fontWeight = "";

                if (item.FontWeight == true)
                    fontWeight = "bold";
                else
                    fontWeight = "normal";

                if (item.FontFamily.ToLower() == "simhei")
                    fontFamily = "黑体";
                else
                    fontFamily = "宋体";


                double left = CommUtls.PxToMm(item.XOffset);
                double top = CommUtls.PxToMm(item.YOffset) - layoutTop;
                double width = CommUtls.PxToMm(item.ControlWidth);
                double height = CommUtls.PxToMm(item.ControlHeight);

                //XmlElement layout = xmldoc.CreateElement("", "layout", "");
                //layout.SetAttribute("id", i.ToString());
                //layout.SetAttribute("left", left.ToString("F2"));
                //layout.SetAttribute("top", (top).ToString("F2"));
                //layout.SetAttribute("width", width.ToString("F2"));
                //layout.SetAttribute("height", height.ToString("F2"));

                XmlElement text = xmldoc.CreateElement("", "text", "");
                //text.SetAttribute("style", "fontFamily:" + fontFamily + ";fontSize:" + fontSize + ";fontWeight:" + fontWeight + "");
                //text.InnerText = "<![CDATA[<%=_data." + item.ControlId + "%>]]>";


                //text.SetAttribute("name", item.ControlId);

                text.SetAttribute("left", left < 0 ? "0" : left.ToString("F0"));
                text.SetAttribute("top", top < 0 ? "0" : top.ToString("F0"));
                text.SetAttribute("width", width < 0 ? "10" : width.ToString("F0"));
                text.SetAttribute("height", height < 0 ? "5" : height.ToString("F0"));

                var styleVal = string.Empty;
                //text.SetAttribute("fontFamily", fontFamily);
                styleVal += $"fontFamily:{fontFamily};"; // 字体类型，宋体SimSun 黑体SimHei 楷体 KaiTi 仿宋 FangSong，中文 / 英文都支持

                //text.SetAttribute("fontWeight", fontWeight);
                styleVal += $"fontWeight:{fontWeight};"; // 字体粗细，取值：normal 表示正常，bold表示加粗

                if (!string.IsNullOrWhiteSpace(fontSize))
                {
                    //text.SetAttribute("fontSize", fontSize);
                    styleVal += $"fontSize:{fontSize};"; //字体大小，如fontSize:8。默认单位pt，可省略；支持自适应字体大小，如fontSize:auto。
                }
                if (item.ControlId == "watermark")
                {
                    //text.SetAttribute("Alpha", "600");
                    styleVal += $"alpha:{180};"; //透明度，数值0~255，255为完全不透明，0为完全透明
                }
                text.SetAttribute("style", styleVal);

                XmlCDataSection cd = xmldoc.CreateCDataSection($"<%=_data.{item.ControlId}%>");
                text.AppendChild(cd);

                //layout.AppendChild(text);
                layoutRoot.AppendChild(text);
            }

            OuterXml = xmldoc.OuterXml;
            OuterXml = OuterXml.Replace("&lt;", "<").Replace("&gt;", ">");

            return OuterXml;
        }

        /// <summary>
        /// 获取自定义区域的 宽、高、top
        /// </summary>
        /// <param name="tempalteType"></param>
        /// <param name="exCode"></param>
        /// <returns></returns>
        private static Tuple<int, int, int> GetCustomerAreaSize(int tempalteType, string exCode)
        {
            Tuple<int, int, int> size = null; //宽、高、top
            switch (tempalteType)
            {
                case 150: //一联
                case 180: //新版一联
                    size = new Tuple<int, int, int>(76, 50, 80);//宽、高、top
                    break;
                default: //标准二连
                    if (exCode == "DBKD")
                    {
                        size = new Tuple<int, int, int>(100, 30, 142);//宽、高、top
                    }
                    if (exCode == "SF")
                    {
                        size = new Tuple<int, int, int>(100, 30, 105);//宽、高、top
                    }
                    else
                    {
                        size = new Tuple<int, int, int>(100, 30, 150);//宽、高、top
                    }
                    break;
            }
            return size;
        }

        /// <summary>
        /// 获取订单通道
        /// </summary>
        /// <param name="orderPlatform"></param>
        /// <returns></returns>
        public static string GetOrderChannel(string orderPlatform)
        {
            var supportChannel = new Dictionary<string, string>() {
                {"Alibaba","1688" },
                {"AlibabaC2M","OTHERS" },
                {"TaobaoMaiCaiV2","OTHERS" },
				{"AlibabaUfuwu","1688" },
                {"AlibabaZhiBo","1688" },
                {"AlibabaZhuKe","1688" },
                {"BeiBei","BEI_BEI" },
                {"DuXiaoDian","OTHERS" },
                {"Jingdong","JD" },
                {"KuaiShou","KUAI_SHOU" },
                {"KuaiShouSupplier","KUAI_SHOU" },
                {"MengTui","OTHERS" },
                {"MoGuJie","MGJ" },
                {"MoKuai","OTHERS" },
                {"Offline","OTHERS" },
                {"OpenV1","OTHERS" },
                {"Pinduoduo","PIN_DUO_DUO" },
                {"Suning","SN" },
                {"Supplier","OTHERS" },
                {"System","OTHERS" },
                {"Taobao","TB" },
                {"TouTiao","DOU_YIN" },
                {"TouTiaoFenFa","DOU_YIN" },
                {"TuanHaoHuo","OTHERS" },
                {"VipShop","WPH" },
                {"Virtual","OTHERS" },
                {"WeiDian","WEI_DIAN" },
                {"WeiMeng","WEI_MENG" },
                {"WxXiaoShangDian","WEI_DIAN" },  //??微信小店，WXXD？
                {"XiaoDian","DOU_YIN" },
                {"XiaoHongShu","XIAO_HONG_SHU" },
                {"YouZan","YOU_ZAN" },
                {"YunJi","YUN_JI" },
                {"ZhiDian","DOU_YIN" },
            };

            if (supportChannel.Keys.Contains(orderPlatform))
                return supportChannel[orderPlatform];

            return "OTHERS";
        }


        /// <summary>
        /// TemplateType to PddTemplateType
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static string GetXiaoHongShuTemplateType(int templateType)
        {
            //枚举值：
            var urltype = "76*130";
            switch (templateType)
            {
                case 150:
                case 180:
                    urltype = "76*130";  //小红书 快递一联单
                    break;
                case 151:
                case 181:
                    urltype = "100*150";  //小红书 快递二联单
                    break;
                case 152:
                case 182:
                    urltype = "100*180"; //小红书 快递三联面单
                    break;
            }
            return urltype;
        }

        /// <summary>
        /// 获取标准电子面单模板 ,取号接口处用templateId
        /// </summary>
        /// <param name="cp_code"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public Tuple<string, string> GetStdtemplatesId(string cp_code, string type,string branchCode, decimal height = 130, int billVersion = 1)
        {
            var json = _xhsPtService.QueryEbillTemplates(cp_code,billVersion);

            JToken jtoken = null;
            try
            {
                jtoken = JsonConvert.DeserializeObject<JToken>(json);
            }
            catch (Exception ex)
            {
                Log.WriteError($"小红书标准面单接口响应解析报错:{ex.Message}");
                throw new LogicException($"小红书标准面单接口响应解析报错:{ex.Message}");
            }

            //系统错误
            var success = jtoken.Value<bool>("success");
            if (!success)
            {
                //系统错误
                var error_msg = jtoken.Value<string>("error_msg");
                var error_code = jtoken.Value<string>("error_code");
                throw new LogicException($"小红书标准面单查询报错code:{error_code},error:{error_msg}");
            }
            var jarrayData = jtoken?.Value<JToken>("data");
            var jarray = jarrayData.Value<JArray>("templateList");
            if (jarray != null && jarray.Count() > 0)
            {
                var templateList = jarray.Where(x => x?.Value<string>("brandCode") == branchCode).ToList();
                if (templateList == null || templateList.Any() == false)
                    templateList = jarray.ToList();
                //Log.Debug($"branchCode={branchCode}，匹配branchCode的模板：{templateList.ToJson()}");
                foreach (var item in templateList)
                {
                    if (item != null)
                    {
                        var cpCode = item.Value<string>("cpCode");
                        var templateType = item.Value<string>("templateType");
                        var templateCustomerType = item.Value<int>("templateCustomerType"); //0 表示原始模板，其他表示面单自带的自定义区域
                        if (cpCode == cp_code && templateType == type && templateCustomerType == 0)
                        {
                            var id = item.Value<string>("id");
                            return new Tuple<string, string>(templateType, id);
                        }
                    }
                }
            }

            throw new LogicException($"快递【{cp_code}】未找到对应小红书云模板");
        }
        /// <summary>
        /// 取消小红书电子面单
        /// </summary>
        /// <param name="authconfig"></param>
        /// <param name="cp_code"></param>
        /// <param name="waybillCode"></param>
        /// <returns></returns>
        public Tuple<bool, string> CancelWaybillCode(string cp_code, string waybillCode, int billVersion = 1)
        {
            // 京东默认是取子母件，回收单号只能传母单号
            if (cp_code.ToString2().ToLower() == "jd" && waybillCode.IsNotNullOrEmpty() && waybillCode.Contains("-"))
                waybillCode = waybillCode.SplitToList("-").FirstOrDefault();

            var json = _xhsPtService.CancelEbillOrder(cp_code, waybillCode, billVersion);

            JToken jtoken = null;
            try
            {
                jtoken = JsonConvert.DeserializeObject<JToken>(json);
            }
            catch (Exception ex)
            {
                Log.WriteError($"小红书电子面单取消结果解析报错:{ex.Message}");
                return new Tuple<bool, string>(false, $"取消结果解析报错:{json}");
            }
            var result = jtoken.Value<bool>("success");
            var error_msg = jtoken?.Value<string>("error_msg") ?? "";
            return new Tuple<bool, string>(result, error_msg);
        }

        #region 新版小红书面单

        /// <summary>
        /// 生成新版自定义区域模板布局 json格式
        /// </summary>
        /// <param name="printContorlList"></param>
        /// <param name="templateType"></param>
        /// <param name="exCode"></param>
        /// <returns></returns>
        public static string PrintNewControlsToCustomAreaTemplate(List<PrintControl> printContorlList, int templateType, string exCode = "")
        {
            string result = "";
            var childList = new List<object>();
            foreach (var control in printContorlList)
            {
                var child = CreateXhsChild(control);
                childList.Add(child);
            }
            var xhsObj = new
            {
                componentName = "Page",
                children = childList,
                //size = "70x30", //必填  组件中内容超出可自动分页
                //canvasSize = "359x154",
                size = "",
                canvasSize = "",
                isSplit = true,
            };
            result = xhsObj.ToJson();
            return result;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="control"></param>
        /// <param name="typeName">OnixBarleyElectronicText   自定义文本类型,OnixBarleyElectronicBarcode 条形码</param>
        /// <returns></returns>
        public static object CreateXhsChild(PrintControl control, string typeName = "OnixBarleyElectronicText")
        {
            var strValue = $"[#data.{control.ControlId}]";
            //页码控件内容，第二页时使用，与官方模板中页码代码。
            if (control.ControlId == "PageIndex")
            {
                strValue = "(context => {\n    let documentNumber = context.documentNumber || '';\n    let documentCount = context.documentCount || '';\n    if (!documentNumber) {\n        return \"\";\n    }\n    if (!documentCount) {\n        return `第${documentNumber}个`;\n    }\n    return `第${documentNumber}/${documentCount}个`\n})(#data._context || {})";
            }
            var result = new
            {
                componentName = typeName,
                props = new
                {
                    basic = new
                    {
                        left = GetXhsPxToMm(control.XOffset * 1.35),//1.35 是前端缩放比例，前端编辑时是缩放的
                        top = GetXhsPxToMm((control.YOffset - 300) * 1.35),//自定义区域的偏移是重新计算的，不算其他快递无法编辑的区域，需要减掉300，300是前端无法编辑区域的高度
                        width = GetXhsPxToMm(control.ControlWidth * 1.35),
                        height = GetXhsPxToMm(control.ControlHeight * 1.35)
                    },
                    textSection = new
                    {
                        rotate = 0,
                        fontFamily = control.FontFamily,
                        fontSize = GetXhsPtToPx(control.FontSize),
                        fontWeight = control.FontWeight ? "bold" : "normal",
                        letterSpace = 0,
                        lineHeight = 1,
                        opacity = 10,
                        fontStyle = "normal",
                        fontJustifyContent = "flex-start",
                        fontAlignItems = "flex-start"
                    },
                    contentSection = new
                    {
                        content = $"{control.ControlName}",
                        value = strValue
                    },
                    key = $"key{control.Id}",
                    visibleSection = new
                    {
                        visible = true
                    }
                },
                id = $"{control.Id}"
            };
            return result;
        }
        /// <summary>
        /// 新版小红书px转毫米 自定义区域偏移使用的是mm
        /// </summary>
        /// <param name="px"></param>
        /// <returns></returns>
        private static double GetXhsPxToMm(double px)
        {

            return (px * 25.4) / 130;
        }
        /// <summary>
        /// 小红书pt转px 字体样式使用的是px
        /// </summary>
        /// <param name="pt"></param>
        /// <returns></returns>
        private static double GetXhsPtToPx(double pt)
        {
            return pt * 130 / 72;
        }
        #endregion
    }
}
