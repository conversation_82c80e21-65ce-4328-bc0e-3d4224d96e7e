using CSRedis;
using Dapper;
using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.Model;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Web;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace DianGuanJiaApp.Tests.PlatformService
{
    /// <summary>
    /// ZhiDianPlatformServiceTest 的摘要说明
    /// </summary>
    [TestClass]
    public class ZhiDianPlatformServiceTest
    {

        private Shop _shop;
        private SiteContext _siteContext;
        //private ZhiDianPlatformService _service;
        private ZhiDianNewPlatformService _service;
        //private ZhiDianApiClient _client;
        private ZhiDianNewApiClient _client;
        private SyncOrderService _syncService;
        private List<Shop> _shops;
        private OrderService orderService;
        private MergerOrderService mergerOrderService;
        private SyncProductService syncProductService;

        public ZhiDianPlatformServiceTest()
        {
            //
            //TODO:  在此处添加构造函数逻辑
            //   
        }

        [TestMethod]
        public void ApiTest()
        {
            var url = Utility.DES.DecryptUrl("0739C566C17F5878D3C3C51BECF188F9BE853992641A7DD5BA8DECCD45ED0244B35724466A17DA84297BFE6EB5ABFB05B608773101985220", CustomerConfig.LoginCookieEncryptKey);
            var wc = new WebUtils();
            IDictionary<string, string> dic = JsonConvert.DeserializeObject<IDictionary<string, string>>(@"{""access_token"":""0612e0a9-5588-4f18-8d4a-7c792e12cd13"",""app_key"":""7390211222756394559"",""method"":""freightTemplate.list"",""param_json"":""{\""page\"":\""0\"",\""size\"":\""100\""}"",""sign"":""e185075fa95a8e0f3641059549876cdf"",""timestamp"":""2024-09-20 14:16:03"",""v"":""2""}");
            string log;
            wc.PostToTouTiaoApiProxy(url, dic,out log);
        }

        [TestMethod]
        public void BatchEnaableShopPushDbV2()
        {
            var pushDbIds = new Dictionary<string,List<string>>() { 
                { "6925303336539309583",new List<string>{ "mysql-5e11a7f53363","mysql-02cfba75f20a" } } ,
                { "7152331588175513101",new List<string>{ "mysql-e59268572c6f","mysql-d754e309167c" } } 
            };
            var failIds = new List<string>();
            var commonSettingService = new CommonSettingService();
            var configDb = DbUtility.GetConfigureConnection();
            //分单用户开通推送库，两个应用如果有效则都需要开通
            var appKeys = new List<string> { "7152331588175513101" };//"6925303336539309583", 
            foreach (var appKey in appKeys)
            {
                var shops = configDb.Query<Shop>($@"SELECT DISTINCT
           s.Id,s.ShopId,se.AccessToken
    FROM dbo.P_Shop s WITH (NOLOCK)
        INNER JOIN dbo.ServiceAppOrder app WITH (NOLOCK)
            ON s.Id = app.ShopId
        LEFT JOIN dbo.P_CommonSetting cs WITH (NOLOCK)
            ON cs.ShopId = s.Id
               AND cs.[Key] = '/Temp/TouTiao/PushDb/{appKey}'
        INNER JOIN dbo.P_ShopExtension se (NOLOCK) ON se.ShopId=s.Id AND se.AppKey='{appKey}'
    WHERE s.PlatformType = 'TouTiao'
          AND app.ServiceEnd > GETDATE()
          AND app.PayStatus = 'TRADE_SUCCESS'
          AND app.ServiceAppId = '{appKey}'
          AND cs.ShopId IS NULL ORDER BY s.Id DESC;").ToList();
                var countBag = new ConcurrentBag<int>();
                var pushDbCount = pushDbIds.Count();
                var shopService = new ShopService();
                var shopPushDbKey =$"/Temp/TouTiao/PushDb/{appKey}";
                var shopId = shops.First().Id;
                var firstShop = shopService.GetShopAndShopExtension(shopId, appKey);
                var ptService = PlatformFactory.GetPlatformService(firstShop)  as ZhiDianNewPlatformService;
                var appKeyValue = pushDbIds[appKey];
                var pushDbInfoFromApi = ptService.QueryPushDbInfo(firstShop.ShopId);
                Parallel.ForEach(shops, new ParallelOptions { MaxDegreeOfParallelism = 20 }, shop =>
                {
                    var pushdbIdIndex = shop.Id % appKeyValue.Count();
                    //firstShop.AccessToken = shop.AccessToken;
                    //firstShop.RefreshToken = "";
                    //firstShop.ShopExtension.AccessToken = shop.AccessToken;
                    //firstShop.ShopExtension.RefreshToken = "";
                    var pushDbId = appKeyValue[pushdbIdIndex];
                    try
                    {
                        shopService.TryCreateTouTiaoPushDbTemp(commonSettingService,ptService,shop,appKey,shopPushDbKey,pushDbId);
                        countBag.Add(1);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"店铺【{shop.Id}】开通推送库失败："+ex);
                    }
                });
            }

        }

        [TestInitialize]
        public void Init()
        {
            RedisHelper.Initialization(new CSRedisClient(ConfigurationManager.ConnectionStrings["TraceRedis"].ConnectionString));

            var rp = new DianGuanJiaApp.Data.Repository.ShopRepository();
            _shop = rp.Get(" where Id=@id", new { id = 1687 })?.FirstOrDefault();
            var _userFx = new UserFxService().Get(5);
            _siteContext = new SiteContext(_userFx, new SiteContextConfig() { NeedRelationShops = false, NeedShopExpireTime = false });
            _service = new ZhiDianNewPlatformService(_shop);
            //_service = PlatformFactory.GetPlatformService(_shop) as ZhiDianNewPlatformService;
            //_syncService = new SyncOrderService();
            _client = new ZhiDianNewApiClient(_shop);
            //DapperMapping.SetEntityMapping();
            //RedisConfigService.Initialization();
            //_service.SyncOrder("6935744385527846295");
        }

        [TestMethod]
        public void TestCate()
        {
            var cateIds = System.IO.File.ReadAllLines(@"D:\Desktop\platformcategory_202409191034.txt").Select(x => x.Trim()).ToList();
            if (_shop == null)
            {
                _shop = new Shop();
            }
            // 本地调试用的参数
            _shop.AppSecret = "1b7d2145-435f-4bea-ad77-4b7d524942cb";
            _shop.AccessToken = "0612e0a9-5588-4f18-8d4a-7c792e12cd13";
            _shop.AppKey = "7390211222756394559";
            var client = new ZhiDianNewApiClient(_shop);
            Parallel.ForEach(cateIds, x =>
            {
                // 获取类目属性
                var platformCategoryServic = new PlatformCategoryService();
                var content = platformCategoryServic.GetCategoryProp(x);

                content.ForEach(c => {
                    var obj = c.ToObject<DyCategoryPropInfoV2Model>();
                    if(obj?.has_sub_property == true)
                    {
                        Log.WriteLine($"类目：{x},属性：{obj.property_id}，有子属性");
                        //obj.options?.ForEach(o => {
                        //    var dict = new Dictionary<string, dynamic>();
                        //    var logid = "";
                        //    //var cascade_info = new List<(long, string, long)>();
                        //    var cascade_info = new List<(string, string, string)>();
                        //    //cascade_info.Add((0, "是否为有机食品", 0));
                        //    cascade_info.Add((o.value, o.name, "0"));

                        //    dict.Add("category_id", x);
                        //    dict.Add("property_id", obj.property_id);
                        //    dict.Add("cascade_info", cascade_info.Select(i => new
                        //    {
                        //        value_id = i.Item1,
                        //        value_name = i.Item2,
                        //        cascade_id = string.IsNullOrEmpty(i.Item3) ? "0" : i.Item3,
                        //        //cascade_id = i.Item3,
                        //    }).ToList().ToJson());

                        //    var json = client.Execute2("product.getCascadeValue", dict, out logid);
                        //    Log.WriteLine(json);
                        //});
                    }
                });
            });
            

        }

        [TestMethod]
        public void TestCascadeValue()
        {
            if (_shop == null)
            {
                _shop = new Shop();
            }
            // 本地调试用的参数
            _shop.AppSecret = "1b7d2145-435f-4bea-ad77-4b7d524942cb";
            _shop.AccessToken = "0612e0a9-5588-4f18-8d4a-7c792e12cd13";
            _shop.AppKey = "7390211222756394559";
            var client = new ZhiDianNewApiClient(_shop);

            //var dict = new Dictionary<string, dynamic>();
            //var logid = "";
            ////var cascade_info = new List<(long, string, long)>();
            //var cascade_info = new List<(string, string, string)>();
            ////cascade_info.Add((0, "是否为有机食品", 0));
            //cascade_info.Add(("23964", "是", "0"));
            //dict.Add("category_id", "23203");
            //dict.Add("property_id", "58");
            //dict.Add("cascade_info", cascade_info.Select(x => new
            //{
            //    value_id = x.Item1,
            //    value_name = x.Item2,
            //    //cascade_id = string.IsNullOrEmpty(x.Item3) ? 0 : x.Item3.ToLong(),
            //    cascade_id = x.Item3,
            //}).ToList().ToJson());

            //var json = client.Execute2("product.getCascadeValue", dict, out logid);

            var dict = new Dictionary<string, string>();
            dict.Add("category_leaf_id", "23203");
            var logid = "";
            var json = client.Execute("product.getCatePropertyV2", dict, out logid);
        }
        /// <summary>
        /// 
        /// </summary>
        [TestMethod]
        public void TestResyncOrderByPushDb()
        {
            var db = DbUtility.GetConfigureConnection();
            var sql = @"SELECT us.ShopId,us.FxUserId FROM dbo.P_UserFx u (NOLOCK)
		INNER JOIN dbo.P_FxUserShop us (NOLOCK) ON u.Id = us.FxUserId AND us.PlatformType='Toutiao'
		INNER JOIN dbo.P_Shop s (NOLOCK) ON s.Id = us.ShopId
		 WHERE Mobile IN('13168863634','13715955987','15677551316','15113630074','15989800010','13106259729','13342759662','13719905878','13750468499','15989736390','18319771696','13600045797','13380092670')
";
            var shopIds = db.Query<FxUserShop>(sql).ToList();
            foreach (var fxus in shopIds)
            {
                try
                {
                    var _userFx = new UserFxService().Get(fxus.FxUserId);
                    _siteContext = new SiteContext(_userFx, new SiteContextConfig() { NeedRelationShops = false, NeedShopExpireTime = false });
                    var rp = new DianGuanJiaApp.Data.Repository.ShopRepository();
                    _shop = rp.Get(" where Id=@id", new { id = fxus.ShopId })?.FirstOrDefault();
                    _service = PlatformFactory.GetPlatformService(_shop) as ZhiDianNewPlatformService;
                    var json = _service.DdpAddShopTask(3, DateTime.Now.AddDays(-45));
                    Log.WriteLine($"用户【{fxus.FxUserId}】，店铺【{fxus.ShopId}】：{json}");
                }
                catch (Exception ex)
                {
                    Log.WriteLine($"用户【{fxus.FxUserId}】，店铺【{fxus.ShopId}】：{ex.Message}");

                }
            }

        }

       // [TestInitialize]
        public void FxInit()
        {
            var rp = new DianGuanJiaApp.Data.Repository.ShopRepository();
            _shop = rp.GetShopAndShopExtension(1687);
            var _userFx = new UserFxService().Get(5);
            _siteContext = new SiteContext(_userFx, new SiteContextConfig() { NeedRelationShops = false, NeedShopExpireTime = false });
            _service = PlatformFactory.GetPlatformService(_shop) as ZhiDianNewPlatformService;
        }

        [TestMethod]
        public void TestReOpenPushDb()
        {
            var sql = @"
SELECT st.ShopId FROM dbo.P_SyncStatus st (NOLOCK) 
INNER JOIN dbo.P_FxUserShop us (NOLOCK) ON st.ShopId =us.ShopId AND st.SyncType=1 AND us.PlatformType='TouTiao' AND st.Source='FendanSystem'
WHERE st.LastSyncTime>'2023-11-16' and st.LastSyncMessage not like '%授权过期%' and LastSyncMessage not like '%】个订单%'
order by st.Id";
            var doudianDb = DbApiAccessUtility.GetTouTiaoConfigureDb();
            var shopIds = doudianDb.Query<int>(sql);
            var noPushdbInfoShops = new ConcurrentBag<int>();
            var checkedCount = new ConcurrentBag<int>();
            var failedShopIds = new ConcurrentBag<int>();
            RedisConfigService.Initialization();
            Parallel.ForEach(shopIds, new ParallelOptions { MaxDegreeOfParallelism = 20 }, shopId => {
                try
                {
                    var db = DbUtility.GetConfigureConnection();
                    var shop = db.QueryFirstOrDefault<Shop>("select * from P_Shop (NOLOCK) where ID=@Id", new { Id = shopId });
                    if (shop == null)
                        return;
                    var ptservice = PlatformFactory.GetPlatformService(shop);
                    var service = ptservice as ZhiDianNewPlatformService;
                    var pushDb = service.QueryPushDbInfo();
                    checkedCount.Add(shopId);
                    if (pushDb != null)
                    {
                        return;
                    }
                    //调用接口重新开通
                    var curPushDbId = db.QueryFirstOrDefault<string>($"select Value from P_CommonSetting (NOLOCK) where ShopId=@shopId and [Key]='/TouTiao/PushDb/{shop.AppKey}'",new { shopId });
                    if (string.IsNullOrEmpty(curPushDbId) == false)
                    {
                        noPushdbInfoShops.Add(shopId);
                        //调用接口开通
                        var isok = service.EnablePushDb(curPushDbId,15);
                        Log.WriteLine($"No.[{checkedCount.Count()}] ShopId:{shopId} 重新开通推送库{(isok?"成功":"失败")}  ThreadId:{Thread.CurrentThread.ManagedThreadId}");
                    }
                }
                catch (Exception ex)
                {
                    failedShopIds.Add(shopId);
                    Log.WriteError($"检查推送库失败，店铺ID:{shopId}，异常信息：{ex}");
                }

            });
            Log.WriteLine($"检查完成，共检查【{checkedCount.Count()}】个店铺，其中【{noPushdbInfoShops.Count()}】个有问题，重新开通的店铺ID如下：\r\n" + string.Join(",", noPushdbInfoShops));
            Log.WriteLine($"检查完成，共检查【{checkedCount.Count()}】个店铺，其中【{failedShopIds.Count()}】检查异常，检查异常的店铺ID如下：\r\n" + string.Join(",", failedShopIds));
        }

        [TestMethod]
        public void TestSyncOneOrder()
        {
            _service.PingRightsInfoV2();
            _shop.ExpireTime = DateTime.Now.AddDays(-1);
            var time = _service.GetExpiredTime();
            var order = _service.SyncOrder("6921403460402615520");
        }

        [TestMethod]
        public void TestDecryptOrder()
        {
            var orderInfo = _syncService.SyncSingleOrder("5011429903254050362", _shop);
            var order = _service.DecryptBatch(new List<Order>() { orderInfo }, false);
            Thread.Sleep(1000000);
        }

        [TestMethod]
        public void TestExpire()
        {
            var ss = ZhiDianNewPlatformService.GetSearchIndex("张越", DouyinEncryptType.ChineseName);
            var time = _service.GetExpiredTime();
        }

        private TestContext testContextInstance;

        /// <summary>
        ///获取或设置测试上下文，该上下文提供
        ///有关当前测试运行及其功能的信息。
        ///</summary>
        public TestContext TestContext
        {
            get
            {
                return testContextInstance;
            }
            set
            {
                testContextInstance = value;
            }
        }
        [TestMethod]
        public void TestSplitOrder()
        {
            var ids = new List<string> { "4691527235668935393", "C4691527235668935393", "4691577448141206241", "4691600821356856033", "4691601001734472417", "4692252410835108577", "4692252462383805153", "4692252664245650145", "4692996324936516321", "4692996458100994785", "4693058507478194913", "4693063025784390369" };
            var keys = ids.Select(x => new OrderSelectKeyModel() { PlatformOrderId = x, ShopId = _shop.Id }).ToList();
            var ser = new OrderService();
            var orders = ser.GetOrders(keys);
            var isAllSame = orders.Select(x => x.BuyerHashCode).Distinct();
            var isAllSame1 = orders.Select(x => x.PlatformStatus).Distinct();
            var mainOrder = orders.FirstOrDefault(x => x.PlatformOrderId.StartsWith("C") == true);
            var childs = orders.Where(x => x.PlatformOrderId.StartsWith("C") == false).ToList();
            var notSameStatusOrder = childs.Where(c => c.PlatformStatus != mainOrder.PlatformStatus || c.BuyerHashCode != mainOrder.BuyerHashCode).ToList();
            var sameStatusOrder = childs.Where(c => c.PlatformStatus == mainOrder.PlatformStatus && c.BuyerHashCode == mainOrder.BuyerHashCode).ToList();

        }

        [TestMethod]
        public void SignTest()
        {
            //_service.GetBrandList();

            var dict = new Dictionary<string, string>();
            dict.Add("start_time", DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd HH:mm:ss"));
            dict.Add("end_time", DateTime.Now.AddHours(1).ToString("yyyy-MM-dd HH:mm:ss"));
            dict.Add("page", 0.ToString());
            dict.Add("size", 100.ToString());
            dict.Add("order_by", "update_time");

            var logId = string.Empty;
            var result = _client.Execute("order.list", dict, out logId);
        }

        [TestMethod]
        public void RefreshTokenTest()
        {
            var result = _client.RefreshToken(true);
        }


        [TestMethod]
        public void getShopNameTest()
        {
            //3398830586331275334	7042735f239a0c242e5428e438c2a015
            var accessToken = "3420723255902909540";
            var refreshToken = "0a42eb35462909c1fa3b82f5986b8899";
            ZhiDianNewPlatformService _oldService = new ZhiDianNewPlatformService(_shop);
            var shopName = _oldService.GetShopName(accessToken, refreshToken);
        }

        [TestMethod]
        public void GetShopTest()
        {

            var rmb = "129321.12122";
            var rr = rmb.ToCNRMB();
            var uu = rmb.ToCNUpper();

            rmb = "129321";
            rr = rmb.ToCNRMB();
            uu = rmb.ToCNUpper();

            rmb = "129321.0";
            rr = rmb.ToCNRMB();
            uu = rmb.ToCNUpper();

            rmb = "129321.";
            rr = rmb.ToCNRMB();
            uu = rmb.ToCNUpper();

            //var paramDic = new Dictionary<string, string>(); 
            //var result = _client.Execute("shop.brandList", paramDic);

            var shop = _service.SyncShopInfo();
        }

        [TestMethod]
        public void GetLogisticsCompanyListTest()
        {
            //var paramDic = new Dictionary<string, string>();
            //var result = _client.Execute("order.logisticsCompanyList", paramDic);

            var result = _service.GetLogisticList();
        }

        [TestMethod]
        public void getGoodsCategoryTest()
        {
            //var paramDic = new Dictionary<string, string>();
            //var result = _client.Execute("order.logisticsCompanyList", paramDic);

            var result = _service.CategoryList();
        }

        [TestMethod]
        public void GetOrderListTest()
        {
            var paramModel = new DianGuanJiaApp.Data.Model.SyncOrderParametersModel
            {
                IsFullSync = false,
                StartTime = DateTime.Now.AddDays(-1),
                EndTime = DateTime.Now,
                //OrderStatus = DianGuanJiaApp.Data.Enum.OrderStatusType.waitbuyerpay
            };

            //SyncOrderService _syncOrderService = new SyncOrderService();
            //_syncOrderService.SyncOrderByShop(paramModel, _shop);

            var orders = _service.SyncOrders(paramModel);
            //OrderService orderService = new OrderService();
            //orders.ForEach(o =>
            //{
            //    try
            //    {
            //        orderService.BulkMerger(new List<Order> { o });
            //    }
            //    catch (Exception ex)
            //    {
            //        Log.WriteLine(o.ToJson());
            //    }
            //});
            //OrderService orderService = new OrderService();
            //orderService.BulkMerger(orders);
        }


        [TestMethod]
        public void GetOrderDetailTest()
        {
            //OrderService orderService = new OrderService();
            //var keys = new List<OrderSelectKeyModel> 
            //{
            //    new OrderSelectKeyModel{ PlatformOrderId="4887610965395181338",ShopId=433997}
            //};
            //var orders = orderService.GetOrders(keys);
            //orders = _service.DecryptBatch(orders);

            var order = _service.SyncOrder("5008560469971631334");
            //orderService.BulkMerger(new List<Order> { order });
        }


        [TestMethod]
        public void GetRefundOrderListTest()
        {
            var paramModel = new DianGuanJiaApp.Data.Model.SyncOrderParametersModel
            {
                //IsFullSync = true,
                StartTime = "2021-01-06 17:00:00".toDateTime(),
                EndTime = DateTime.Now.toDateTime(),
                //OrderStatus = DianGuanJiaApp.Data.Enum.OrderStatusType.waitsellersend
            };

            new SyncOrderService().SyncOrderByShop(paramModel, _shop);

            //var orders = new ZhiDianNewPlatformService(_shop).SyncOrders(paramModel);
        }


        [TestMethod]
        public void GetRefundOrderNumTest()
        {
            _service.SyncRefundOrderNum();
        }

        [TestMethod]
        public void GetAfterSaleOrderNumTest()
        {
            _service.SyncAfterSaleOrderNum("2020-06-01", "2020-06-12");
        }

        [TestMethod]
        public void GetAfterSaleOrderListTest()
        {
            var paramModel = new DianGuanJiaApp.Data.Model.SyncOrderParametersModel
            {
                IsFullSync = false,
                StartTime = "2020-05-28".toDateTime(),
                EndTime = DateTime.Now.toDateTime(),
            };
            _service.SyncAfterSaleOrderList(paramModel);
        }

        [TestMethod]
        public void SyncOrdersNewTest()
        {
            var paramModel = new DianGuanJiaApp.Data.Model.SyncOrderParametersModel
            {
                IsFullSync = false,
                StartTime = Convert.ToDateTime("2023-06-08"),
                EndTime = Convert.ToDateTime("2023-06-09 13:56:00"),
            };
            _service.SyncOrdersNew(paramModel);
        }


        [TestMethod]
        public void GetAfterSaleOrderDetailTest()
        {
            var order = _service.SyncAfterSaleOrder("4666755384727224817");
        }


        [TestMethod]
        public void TestAutoMerger()
        {
            orderService.SplitOrderWhenPlatformStatusNotSame(new List<Shop> { _shop });
            mergerOrderService.AutoMergerOrder(new List<Shop> { _shop });
        }


        [TestMethod]
        public void ConfirmOrderTest()
        {
            var pids = new List<string> { "6742842095473656067", "6742838249682731268" };
            var result = _service.ConfirmOrder(pids);
        }

        [TestMethod]
        public void GetProductListTest()
        {
            var result = _service.SyncProduct(0);
        }

        [TestMethod]
        public void GetProductDetailTest()
        {
            //var paramDic = new Dictionary<string, string>();
            //paramDic.Add("product_id", "3358708501106050391"); 
            //var result = _client.Execute("product.detail", paramDic);

            _shop.AppKey = "7152331588175513101";
            _shop.AppSecret = "d23a152d-17ea-45fb-bb13-c06595b65efa";
            _shop.AccessToken = "6y1kyacmmx1ic8py3i6jcwd0003vsimg-11";

            var product = _service.SyncProduct("3767193910863069268", false);
        }

        [TestMethod]
        public void GetProductsTest()
        {
            var pids = new List<string>() { "2880195908", "2845215883", "1353572" };
            var product = _service.SyncProducts(pids);
        }

        [TestMethod]
        public void GetShopProductsTest()
        {
            var product = _service.SyncProduct(0);
        }

        [TestMethod]
        public void GetLogisticList()
        {
            var expressLst = _service.GetLogisticList();
        }

        [TestMethod]
        public void GetCategoryList()
        {
            System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            sw.Start();
            var categoryList = _service.CategoryList();
            sw.Stop();
            var ts = sw.Elapsed;
            Console.WriteLine("运行毫秒数：" + ts);
            Console.WriteLine(categoryList.ToJson());
        }


        [TestMethod]
        public void TestRefreshShopToken()
        {
            System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            sw.Start();
            var shop = _service.RefreshShopToken();
            sw.Stop();
            var ts = sw.Elapsed;
            Console.WriteLine("运行毫秒数：" + ts);
        }


        [TestMethod]
        public void TestGetShopId()
        {
            var expressLst = _service.GetShopId();
        }


        [TestMethod]
        public void TestGetWaitSellerSendOrderCount()
        {
            var endTime = DateTime.Now;
            var startTime = endTime.AddDays(-50);
            var count = _service.GetWaitSellerSendOrderCount(startTime, endTime);
        }

        [TestMethod]
        public void TestSyncThenCheckOrders()
        {
            SyncOrderService syncOrderService = new SyncOrderService();
            syncOrderService.SyncedThenCheckOrders(_shop, null);
        }



        [TestMethod]
        public void TestSyncSingleOrder()
        {
            SyncOrderService _syncOrderService = new SyncOrderService();
            Order apiOrder = null;
            var order = _syncOrderService.SyncSingleOrderOld("4917538812906057700", _shop, out apiOrder);
            var sc = new SiteContext(_shop, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
            //判断店铺是否被商家关联，如果被分销关联，则需要将订单更新到商家端
            (new OrderService()).FxOrderMessageProcess(new List<Order>() { apiOrder });
        }


        [TestMethod]
        public List<Order> TestSyncOrders()
        {
            try
            {
                var orderList = new List<Order>();
                var startTime = DateTime.Now.AddDays(-2);
                var endTime = startTime.AddDays(1);
                while (startTime < DateTime.Now)
                {
                    var orders = _service.SyncOrders(new DianGuanJiaApp.Data.Model.SyncOrderParametersModel
                    {
                        IsFullSync = true,
                        StartTime = startTime,
                        EndTime = endTime,
                        OrderStatus = OrderStatusType.waitsellersend
                    });
                    startTime = endTime;
                    endTime = startTime.AddDays(1);
                    orderList.AddRange(orders);
                }

                return orderList;
            }
            catch (Exception ex)
            {

            }
            return null;
        }

        [TestMethod]
        public void TestCompareOrder()
        {
            var apiorders = TestSyncOrders();
            orderService = new OrderService(_shop.PlatformType);
            var wss = apiorders.Where(x => x.PlatformStatus == "waitsellersend").ToList();
            var dbOrders = orderService.GetWaitSellerSendOrderIds(_shop.Id);
            var notExistOrders = new List<string>();
            dbOrders?.ForEach(d =>
            {
                if (!apiorders.Any(x => x.PlatformOrderId == d))
                    notExistOrders.Add(d);
            });
            var pidstring = string.Join(",", notExistOrders);

            apiorders?.ForEach(x =>
            {
                //if (!dbOrders.Contains(x.PlatformOrderId))
                //    apiNotExistOrders.Add(x.PlatformOrderId);
            });
            var ids = apiorders.Select(x => x.PlatformOrderId).Distinct().ToList();
            pidstring = string.Join(",", ids);
            //var apiNotExistOrderIds = string.Join(",", apiNotExistOrders);
        }

        [TestMethod]
        public void TestSyncedThenCheckOrders()
        {
            _syncService.SyncedThenCheckOrders(_shop, new DianGuanJiaApp.Data.Model.SyncOrderParametersModel());
        }

        [TestMethod]
        public void TestDeleteOrderItems()
        {
            var keys = new List<OrderItemSearchModel>
            {
                new OrderItemSearchModel{ ShopId=_shop.Id,PlatformOrderID="4749118568126951253",SubItemID="4749118568126951253"},
                new OrderItemSearchModel{ ShopId=_shop.Id,PlatformOrderID="4751253381609451057",SubItemID="4751253381609451057"},
                new OrderItemSearchModel{ ShopId=_shop.Id,PlatformOrderID="4751258673001076273",SubItemID="4751258673001076273"},
                new OrderItemSearchModel{ ShopId=_shop.Id,PlatformOrderID="4751258801847301681",SubItemID="4751258801847301681"},
                new OrderItemSearchModel{ ShopId=_shop.Id,PlatformOrderID="4751259712389092913",SubItemID="4751259712389092913"},
                new OrderItemSearchModel{ ShopId=_shop.Id,PlatformOrderID="4751347467161400881",SubItemID="4751347467161400881"},
                new OrderItemSearchModel{ ShopId=_shop.Id,PlatformOrderID="4751347557351730737",SubItemID="4751347557351730737"},
                new OrderItemSearchModel{ ShopId=_shop.Id,PlatformOrderID="4752045446594309973",SubItemID="4752045446594309973"},
                new OrderItemSearchModel{ ShopId=_shop.Id,PlatformOrderID="4752046945549569429",SubItemID="4752046945549569429"},
            };

            orderService = new OrderService();
            orderService.DeleteOrderItem(keys);
        }
        [TestMethod]
        public void TestPageCount()
        {
            var pageCount = Math.Ceiling(201 / (100 * 1.0)).ToInt();

        }

        [TestMethod]
        public void TestCompare()
        {
            var remoteJson = "{\"Filters\":[{\"TableAlias\":\"o\",\"FieldType\":\"bool\",\"TableName\":\"P_Order\",\"Name\":\"IsPreordain\",\"CompareName\":null,\"Value\":\"0\",\"ExtValue\":null,\"Contract\":\"=\",\"Operator\":null,\"CustomQuery\":null},{\"TableAlias\":\"o\",\"FieldType\":\"int\",\"TableName\":\"P_Order\",\"Name\":\"ShopId\",\"CompareName\":null,\"Value\":\"90610\",\"ExtValue\":null,\"Contract\":\"in\",\"Operator\":null,\"CustomQuery\":null},{\"TableAlias\":\"o\",\"FieldType\":\"string\",\"TableName\":\"P_Order\",\"Name\":\"PlatformStatus\",\"CompareName\":null,\"Value\":\"waitsellersend\",\"ExtValue\":null,\"Contract\":\"in\",\"Operator\":null,\"CustomQuery\":null},{\"TableAlias\":\"o\",\"FieldType\":\"DateTime\",\"TableName\":\"P_Order\",\"Name\":\"CreateTime\",\"CompareName\":null,\"Value\":\"2020-05-01 00:00:00\",\"ExtValue\":\"2020-06-12 23:59:59.999\",\"Contract\":\"between\",\"Operator\":null,\"CustomQuery\":null}],\"MultiFilters\":[],\"OrderByField\":\"o.CreateTime\",\"IsOrderDesc\":true,\"PageIndex\":1,\"PageSize\":10000,\"IsCustomerOrder\":false,\"NeedPagging\":true}";
            var remoteModel = remoteJson.ToObject<OrderSearchModel>();
            OrderService remoteService = new OrderService("server=39.100.57.149,22;uid=sa;pwd=******************;database=toutiao_print_3;Connect Timeout=150;");
            var remoteOrders = remoteService.GetOrders(remoteModel).Rows;

            var localJson = "{\"Filters\":[{\"TableAlias\":\"o\",\"FieldType\":\"bool\",\"TableName\":\"P_Order\",\"Name\":\"IsPreordain\",\"CompareName\":null,\"Value\":\"0\",\"ExtValue\":null,\"Contract\":\"=\",\"Operator\":null,\"CustomQuery\":null},{\"TableAlias\":\"o\",\"FieldType\":\"int\",\"TableName\":\"P_Order\",\"Name\":\"ShopId\",\"CompareName\":null,\"Value\":\"338\",\"ExtValue\":null,\"Contract\":\"in\",\"Operator\":null,\"CustomQuery\":null},{\"TableAlias\":\"o\",\"FieldType\":\"string\",\"TableName\":\"P_Order\",\"Name\":\"PlatformStatus\",\"CompareName\":null,\"Value\":\"waitsellersend\",\"ExtValue\":null,\"Contract\":\"in\",\"Operator\":null,\"CustomQuery\":null},{\"TableAlias\":\"o\",\"FieldType\":\"DateTime\",\"TableName\":\"P_Order\",\"Name\":\"CreateTime\",\"CompareName\":null,\"Value\":\"2020-05-01 00:00:00\",\"ExtValue\":\"2020-06-12 23:59:59.999\",\"Contract\":\"between\",\"Operator\":null,\"CustomQuery\":null}],\"MultiFilters\":[],\"OrderByField\":\"o.CreateTime\",\"IsOrderDesc\":true,\"PageIndex\":1,\"PageSize\":10000,\"IsCustomerOrder\":false,\"NeedPagging\":true}";
            var localModel = localJson.ToObject<OrderSearchModel>();
            OrderService localService = new OrderService();
            var localOrders = localService.GetOrders(localModel).Rows;

            var localNotExistOrderIds = new List<string>();
            var remoteNotExistOrderIds = new List<string>();

            remoteOrders.ForEach(o =>
            {
                if (!localOrders.Any(m => m.PlatformOrderId == o.PlatformOrderId))
                    localNotExistOrderIds.Add(o.PlatformOrderId);
            });

            localOrders.ForEach(o =>
            {
                if (!remoteOrders.Any(m => m.PlatformOrderId == o.PlatformOrderId))
                    remoteNotExistOrderIds.Add(o.PlatformOrderId);
            });

            Log.WriteLine($"localNotExistOrderIds：{string.Join(",", localNotExistOrderIds)}");
            Log.WriteLine($"remoteNotExistOrderIds：{string.Join(",", remoteNotExistOrderIds)}");
        }


        [TestMethod]
        public void UpdateTouTiaoVenderId()
        {
            var _shopRepository = new ShopRepository();
            var shops = _shopRepository.Query("SELECT * FROM dbo.P_Shop WHERE  PlatformType='toutiao' AND ISNULL(VenderId,'')='' AND (ISNULL(ShopId,'')='' OR ShopId=AccessToken) ORDER BY LastSyncTime DESC ", null).ToList();
            shops.ForEach(shop =>
            {
                try
                {
                    var ptService = new ZhiDianNewPlatformService(shop);
                    var shopName = ptService.GetShopName(shop.AccessToken, shop.RefreshToken);
                    if (!shopName.IsNullOrEmpty())
                    {
                        shop.VenderId = shopName;
                        _shopRepository.Update(shop);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"店铺【{shop.Id}】更新VenderId失败，消息：{ex.Message}");
                }
            });
        }
        [TestMethod]
        public void FullSyncOrder()
        {
            try
            {
                var os = new ZhiDianNewPlatformService(_shop).SyncOrders(new SyncOrderParametersModel
                {
                    IsFullSync = false,
                    StartTime = "2020-07-10 14:55:52.013".ToDateTime(),
                    EndTime = DateTime.Now,
                    OrderStatus = DianGuanJiaApp.Data.Enum.OrderStatusType.waitsellersend
                });
                var ids = string.Join("','", os.Select(o => o.PlatformOrderId).ToArray());
            }
            catch (Exception ex)
            {

            }
            Thread.Sleep(100 * 1000);
        }

        [TestMethod]
        public void TestGetRefundOrderList()
        {
            try
            {
                var os = new ZhiDianNewPlatformService(_shop).SyncRefundOrderList(new SyncOrderParametersModel
                {
                    IsFullSync = false,
                    StartTime = "2020-08-10".ToDateTime(),
                    EndTime = DateTime.Now.AddMinutes(10),
                    OrderStatus = DianGuanJiaApp.Data.Enum.OrderStatusType.waitsellersend
                });
                var ids = string.Join("','", os.Select(o => o.PlatformOrderId).ToArray());
            }
            catch (Exception ex)
            {

            }
            Thread.Sleep(100 * 1000);
        }



        [TestMethod]
        public void TestUpdateOrderRemark()
        {
            var result = _service.UpdateOrderRemark("4713017100238237769", "测试接口备注01", "3");
            Assert.IsTrue(result);
            var order = _service.SyncOrder("4713017100238237769");

        }

        [TestMethod]
        public void TestPushLogisticCenter()
        {
            var service = new OrderLogisticInfoService();
            var json = "[{\"Sender\":{\"Name\":\"红姐\",\"Mobile\":\"18585376581\",\"Province\":\"贵州省\",\"City\":\"遵义市\",\"County\":\"赤水市\",\"Street\":\"贵州省遵义市赤水市贵州省遵义市赤水市锦绣路百世快递\"},\"Receiver\":{\"Name\":\"刘英\",\"Mobile\":\"13824328922\",\"Province\":\"广东省\",\"City\":\"深圳市\",\"County\":\"龙华区\",\"Street\":\"民治街道松仔园村一区32号美佳乐\"},\"ExceptionStatus\":null,\"Id\":0,\"ShopId\":136120,\"LogisticName\":\"韵达\",\"LogisticCode\":\"YUNDA\",\"PlatformType\":\"TouTiao\",\"LogisticOrderId\":\"3104134302083\",\"PlatformOrderId\":\"4726049641629169409\",\"LogisticStatus\":0,\"LastTraces\":null,\"CreateTime\":\"0001-01-01 00:00:00\",\"UpdateTime\":null,\"IsIgnorException\":false,\"PrintTime\":\"2020-11-04 13:01:41\",\"SendTime\":null}]";
            var models = json.ToObject<List<OrderLogisticInfoModel>>();
            service.AddLogisticTracesSubject(models, false);
        }
        [TestMethod]
        public void TestUpdateOrderLogisticInfoSendTime()
        {
            var datas = new List<OrderLogisticInfoModel>
            {
                new OrderLogisticInfoModel{ PlatformOrderId="4725342032887192914",LogisticOrderId="773065173132880",ShopId=_shop.Id,SendTime="2020-11-03 14:39:48".ToDateTime()}
            };
            var _logisticService = new OrderLogisticInfoService();
            _logisticService.UpdateSendTime(datas);
        }
        [TestMethod]
        public void TestWriteLogisticTraceSubscribleLog()
        {
            new ShopRepository().WriteLogisticTraceSubscribleLog(new List<LogisticTraceSubscribleLog> {
                new LogisticTraceSubscribleLog{
                    Request = "[{\"ShipperCode\":\"YUNDA\",\"LogisticCode\":\"3104134302083\",\"Sender\":{\"Name\":\"红姐\",\"Mobile\":\"18585376581\",\"Province\":\"贵州省\",\"City\":\"遵义市\",\"County\":\"赤水市\",\"Street\":\"贵州省遵义市赤水市贵州省遵义市赤水市锦绣路百世快递\"},\"Receiver\":{\"Name\":\"刘英\",\"Mobile\":\"13824328922\",\"Province\":\"广东省\",\"City\":\"深圳市\",\"County\":\"龙华区\",\"Street\":\"民治街道松仔园村一区32号美佳乐\"},\"PlatformType\":\"TouTiao\",\"OwnerId\":\"929553\",\"State\":\"136120\",\"TraceType\":null,\"IsPddWaybill\":false,\"TemplateType\":0}]",
                    Response = "",
                    CreateTime = DateTime.Now,
                    ErrorMessage="异常",
                    PlatformType="TouTiao",
                    ShopId = 111,
                    RetryTimes = 0,
                    ShipperCode = "YUNDA",
                    Status = "Error"
                }
            }, "Subscrible");
        }


        [TestMethod]
        public void TestAddressConfirm()
        {
            _service.AddressConfirm("4779848195819992469", "0");
        }


        [TestMethod]
        public void TestGetAddressList()
        {
            _service.GetAddressList();
        }


        [TestMethod]
        public void TestAddressAppliedSwitch()
        {
            _service.AddressAppliedSwitch();
        }


        /// <summary>
        /// MySQL调试不了，conn.open时异常，改到NewOrderController
        /// </summary>
        [TestMethod]
        public void TestSyncAfterSaleOrderDetails()
        {
            var afterSaleIds = new List<string> { "7207616272211689768", "7207682242397651260", "7207693773059637539" };
            //var list = _service.SyncAfterSaleOrderDetails(afterSaleIds);
            var list = _service.SyncAfterSaleOrderDetailsFromPushDb(afterSaleIds);
        }

        [TestMethod]
        public void TestGetRecommendedExpressOrderQuery()
        {
            _service.ParallelGetRecommendedExpressOrderQuery(new TouTiaoRecommendedExpressOrderModel
            {
                Province = "广东省",
                City = "深圳市",
                District = "福田区",
                Street = "",
                Detail = "深南中路",
                OrderInfos = new List<TouTiaoRecommendedExpressOrderInfo>() { 
                    new TouTiaoRecommendedExpressOrderInfo{ 
                        LogicOrderId = "100004557250129",
                        PlatformOrderIds = new List<string>{ "6921071531606873977" },
                        ShopId = 1681
                    }
                }
            });
        }
        [TestMethod]
        public void PingTest()
        {
            var result = _service.Ping();
        }

        /// <summary>
        /// 调试，给指定店铺去同步
        /// </summary>
        [TestMethod]
        public void TestSyncFnDdCateGory()
        {
            var rp = new DianGuanJiaApp.Data.Repository.ShopRepository();
            _shop = rp.Get(" where Id=@id", new { id = 1681 })?.FirstOrDefault();
            _shop.ShopExtension = new ShopExtensionService().GetShopExtensionByShopId(_shop.Id, "6925303336539309583");
            _siteContext = new SiteContext(_shop, new SiteContextConfig() { NeedRelationShops = false, NeedShopExpireTime = false });
            _service = PlatformFactory.GetPlatformService(_shop) as ZhiDianNewPlatformService;
            _client = new ZhiDianNewApiClient(_shop);

            var sslist = new List<int>() { 1687 };
            new PlatformCategorySupplierService().SyncFnDdCateGory(1687);
        }

        // 抖店-类目属性
        [TestMethod]
        public void TestSyncFnDdCateGoryProp()
        {
            // var skey = "listing";
            var skey = string.Empty;
            var targetShops = new ShopService(skey).GetShopByIds(new List<int>() { 1687 });
            var shop = targetShops.FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: skey);

            var svc = new PlatformCategorySupplierService();
            svc.SyncFnDdCategoryProp(shop, new List<PlatformCategory>()
            {
                new PlatformCategory(){ CateId = "38126"}
            });
        }

        // 抖店-类目规则
        [TestMethod]
        public void TestSyncFnDdCategoryPublishRule()
        {
             var skey = "listing";
            //var skey = string.Empty;
            var targetShops = new ShopService(skey).GetShopByIds(new List<int>() { 1687 });
            var shop = targetShops.FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: skey);

            var svc = new PlatformCategorySupplierService();
            svc.SyncFnDdCategoryPublishRule(shop, new List<PlatformCategory>()
            {
                new PlatformCategory(){ CateId = "22994"}
            });
        }

        // 抖店-获取商品的审核状态
        [TestMethod]
        public void TestGetProductAuditList()
        {
            var skey = "listing";
            //var skey = string.Empty;
            var targetShops = new ShopService(skey).GetShopByIds(new List<int>() { 1687 });
            var shop = targetShops.FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: skey);

            var svc = new PlatformCategorySupplierService();
            // 6468839 金文的温馨小铺PLn
            ListingProductStatusEnum? productStatus = svc.GetProductAuditList(1687, "3717186766952857618", (int)ListingProductStatusEnum.PutStashAudit);
        }

        // 抖店-商品发布前置校验接口
        [TestMethod]
        public void TestCheckShopCateAuth()
        {
            var skey = "listing";
            //var skey = string.Empty;
            var targetShops = new ShopService(skey).GetShopByIds(new List<int>() { 1687 });
            var shop = targetShops.FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: skey);

            var svc = new PlatformCategorySupplierService();
            //svc.CheckShopCateAuth(1687, null, "22305");
        }

        // 抖店-商品类目预测-图片
        [TestMethod]
        public void TestProductCateForecastImg()
        {
            var shop = new ShopService("listing").GetShopByIds(new List<int>() { 1687 }).FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: "listing");
            var p2 = "/Common/GetImageFile?objectKey=0739C566C17F587817753D01A0A0021E7592DC8DB42CD34C17968D222725D09582F54C63628DB9C10E3D89F2803C6CD5A7F75D6E96E4956181EE297FB21DD74E6C6F2F9EB232C6FB31A620E0C266BEA07726918A3A684FF649CB6249BC81EBBA&platform=Alibaba&token=515A1574DA4944FDD82C2B04783E68C0";
            var picUrl = "https://6tfxali.dgjapp.com/ImageFile/Get?objectKey=0739C566C17F587817753D01A0A0021E7592DC8DB42CD34C17968D222725D09582F54C63628DB9C10E3D89F2803C6CD524AB89B0942E6141E68FA2FB471200F9C809B8D05176D6E16735A7811D6517261ABBBDFDFEF8EBA7657A19ED406903A2&platform=Alibaba&token=515A1574DA4944FDD82C2B04783E68C0";

            var categoryPredictionResult = new PlatformCategorySupplierService().ProductCateForecast("T恤男装T恤男装", 0, picUrl);
        }

        // 抖店-商品类目预测
        [TestMethod]
        public void TestProductCateForecast()
        {
            var shop = new ShopService("listing").GetShopByIds(new List<int>() { 1687 }).FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: "listing");

            // new PlatformCategorySupplierService().ProductCateForecast("书籍杂志报纸儿童读物童书绘本图画书少儿动漫书1", picUrl); // 使用这个标题，平台接口没有返回数据
            var categoryPredictionResult = new PlatformCategorySupplierService().ProductCateForecast("T恤男装T恤男装",0, "");
        }

        // 获取sku导航属性
        [TestMethod]
        public void Test_GetNavigationCateProp()
        {
            var cc = "{\"normal_rule\":{\"support\":true,\"delay_options\":[9999,1,2],\"is_special_delay_option\":false},\"step_rule\":{\"support\":false,\"delay_options\":[],\"is_special_delay_option\":false,\"step_min_delay\":0,\"step_max_delay\":0},\"product_presell_rule\":{\"support\":false,\"min_presell_price\":2000,\"support_delivery_after_pay\":false,\"support_delivery_after_presell\":false},\"sku_presell_rule\":{\"support\":false,\"min_presell_price\":0,\"support_delivery_after_pay\":false,\"support_delivery_after_presell\":false},\"time_sku_presell_with_normal_rule\":{\"support\":false,\"time_sku_spec_name\":\"发货时效\",\"time_sku_spec_detial\":[{\"is_presell_spec\":false,\"spec_code\":\"product_time_spec_same_day\",\"spec_value\":\"当日发/次日发\"},{\"is_presell_spec\":false,\"spec_code\":\"product_time_spec_next_day\",\"spec_value\":\"次日发\"},{\"is_presell_spec\":false,\"spec_code\":\"product_time_spec_48h\",\"spec_value\":\"48小时内发货\"},{\"is_presell_spec\":true,\"spec_code\":\"product_time_spec_5d\",\"spec_value\":\"5天内发货\"},{\"is_presell_spec\":true,\"spec_code\":\"product_time_spec_7d\",\"spec_value\":\"7天内发货\"},{\"is_presell_spec\":true,\"spec_code\":\"product_time_spec_10d\",\"spec_value\":\"10天内发货\"},{\"is_presell_spec\":true,\"spec_code\":\"product_time_spec_15d\",\"spec_value\":\"15天内发货\"}],\"min_presell_price\":0},\"time_sku_pure_presell_rule\":{\"support\":false,\"time_sku_spec_name\":\"\",\"time_sku_spec_detial\":[],\"min_presell_price\":0},\"after_sale_rule\":{\"three_guarantees\":{\"enable\":true,\"must_select\":false,\"options\":{\"2\":[{\"Item1\":\"30天\",\"Item2\":\"30\"},{\"Item1\":\"60天\",\"Item2\":\"60\"},{\"Item1\":\"90天\",\"Item2\":\"90\"},{\"Item1\":\"120天\",\"Item2\":\"120\"},{\"Item1\":\"180天\",\"Item2\":\"180\"},{\"Item1\":\"1年\",\"Item2\":\"365\"}]}},\"supply_day_return_rule\":{\"enable\":true,\"options\":[{\"Item1\":\"7天无理由退货\",\"Item2\":\"7-1\"}]}},\"recommend_name_rule\":{\"satisfy_prefix\":false,\"property_ids\":[]},\"component_template_rule\":{\"is_show\":true,\"must_input\":true},\"product_spec_rule\":{\"max_spec_num_limit\":3,\"spec_combination_limit\":450,\"spec_single_limit\":200,\"spec_rule_code\":null,\"support_property_diy\":false,\"support_property_sequence_variable\":true,\"required_spec_details\":[{\"sell_property_name\":\"颜色分类\",\"sell_property_id\":2752,\"support_remark\":true,\"support_diy\":true,\"is_required\":true,\"value_display_style\":\"cascader_multi_select\",\"need_paging_query_value\":false,\"property_default_display\":true,\"property_values\":[{\"sell_property_value_id\":35497,\"sell_property_value_name\":\"白色\",\"pic_value\":\"#FFFFFF\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":23838,\"sell_property_value_name\":\"乳白色\",\"pic_value\":\"#FEFBF1\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":214774,\"sell_property_value_name\":\"米白色\",\"pic_value\":\"#EBDEB5\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":27813,\"sell_property_value_name\":\"浅灰色\",\"pic_value\":\"#E4E4E4\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":35937,\"sell_property_value_name\":\"深灰色\",\"pic_value\":\"#666666\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":15345,\"sell_property_value_name\":\"灰色\",\"pic_value\":\"#808080\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":22892,\"sell_property_value_name\":\"银色\",\"pic_value\":\"#C0C0C0\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":24860,\"sell_property_value_name\":\"黑色\",\"pic_value\":\"#000000\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":219539,\"sell_property_value_name\":\"桔红色\",\"pic_value\":\"#EF7C30\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":231111,\"sell_property_value_name\":\"玫红色\",\"pic_value\":\"#CD3775\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":27884,\"sell_property_value_name\":\"粉红色\",\"pic_value\":\"#F4B9C2\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":5518,\"sell_property_value_name\":\"红色\",\"pic_value\":\"#EB3223\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":231112,\"sell_property_value_name\":\"藕色\",\"pic_value\":\"#E9D1D8\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":219541,\"sell_property_value_name\":\"西瓜红\",\"pic_value\":\"#DF615A\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":36717,\"sell_property_value_name\":\"酒红色\",\"pic_value\":\"#8C1A11\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":25280,\"sell_property_value_name\":\"卡其色\",\"pic_value\":\"#C0B095\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":231113,\"sell_property_value_name\":\"姜黄色\",\"pic_value\":\"#F7C880\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":231114,\"sell_property_value_name\":\"明黄色\",\"pic_value\":\"#FFFD55\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":35412,\"sell_property_value_name\":\"浅黄色\",\"pic_value\":\"#FFFEE3\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":56147,\"sell_property_value_name\":\"杏色\",\"pic_value\":\"#F5EDD8\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":219545,\"sell_property_value_name\":\"柠檬黄\",\"pic_value\":\"#FDEB65\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":13730,\"sell_property_value_name\":\"桔色\",\"pic_value\":\"#F3A83B\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":219544,\"sell_property_value_name\":\"荧光黄\",\"pic_value\":\"#EEFC73\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":37451,\"sell_property_value_name\":\"金色\",\"pic_value\":\"#FAD649\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":168681,\"sell_property_value_name\":\"香槟色\",\"pic_value\":\"#F7F6D5\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":7696,\"sell_property_value_name\":\"黄色\",\"pic_value\":\"#FFFD54\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":5148,\"sell_property_value_name\":\"军绿色\",\"pic_value\":\"#627435\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":231115,\"sell_property_value_name\":\"翠绿色\",\"pic_value\":\"#4AA050\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":12603,\"sell_property_value_name\":\"墨绿色\",\"pic_value\":\"#296118\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":38579,\"sell_property_value_name\":\"浅绿色\",\"pic_value\":\"#A6EB99\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":37590,\"sell_property_value_name\":\"绿色\",\"pic_value\":\"#377D22\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":219547,\"sell_property_value_name\":\"荧光绿\",\"pic_value\":\"#77F44C\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":195475,\"sell_property_value_name\":\"青色\",\"pic_value\":\"#65DCA3\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":34260,\"sell_property_value_name\":\"天蓝色\",\"pic_value\":\"#6CCCF2\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":231116,\"sell_property_value_name\":\"孔雀蓝\",\"pic_value\":\"#47A3C1\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":231117,\"sell_property_value_name\":\"宝蓝色\",\"pic_value\":\"#476CD9\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":12070,\"sell_property_value_name\":\"浅蓝色\",\"pic_value\":\"#D8EFF3\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":7861,\"sell_property_value_name\":\"深蓝色\",\"pic_value\":\"#021E8A\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":219555,\"sell_property_value_name\":\"湖蓝色\",\"pic_value\":\"#6CDCF0\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":25121,\"sell_property_value_name\":\"蓝色\",\"pic_value\":\"#0023F5\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":196446,\"sell_property_value_name\":\"藏青色\",\"pic_value\":\"#344E7A\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":219558,\"sell_property_value_name\":\"浅紫色\",\"pic_value\":\"#EBE0E6\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":34836,\"sell_property_value_name\":\"深紫色\",\"pic_value\":\"#3D1050\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":209926,\"sell_property_value_name\":\"紫红色\",\"pic_value\":\"#7F1A5F\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":2396,\"sell_property_value_name\":\"紫罗兰\",\"pic_value\":\"#E08AE8\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":21015,\"sell_property_value_name\":\"紫色\",\"pic_value\":\"#751A7C\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":56148,\"sell_property_value_name\":\"咖啡色\",\"pic_value\":\"#5B3A1A\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":10414,\"sell_property_value_name\":\"深卡其布色\",\"pic_value\":\"#BCB675\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":31737,\"sell_property_value_name\":\"巧克力色\",\"pic_value\":\"#C66E33\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":196833,\"sell_property_value_name\":\"深棕色\",\"pic_value\":\"#754C17\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":219561,\"sell_property_value_name\":\"栗色\",\"pic_value\":\"#75140C\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":194827,\"sell_property_value_name\":\"浅棕色\",\"pic_value\":\"#A8604A\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":56146,\"sell_property_value_name\":\"驼色\",\"pic_value\":\"#A28467\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":16405,\"sell_property_value_name\":\"褐色\",\"pic_value\":\"#7F5C1C\",\"pic_type\":\"rgb\"},{\"sell_property_value_id\":36653,\"sell_property_value_name\":\"花色\",\"pic_value\":\"https://lf3-cm.ecombdstatic.com/obj/ecom-ecop/1685977914c1dbb2c27c127af12a0c7be7537976a222325f92.png\",\"pic_type\":\"url\"}],\"measure_templates\":[],\"navigation_properties\":[{\"property_id\":4471,\"property_name\":\"色系\"},{\"property_id\":2752,\"property_name\":\"颜色分类\"}]},{\"sell_property_name\":\"鞋码大小\",\"sell_property_id\":4709,\"support_remark\":true,\"support_diy\":false,\"is_required\":true,\"value_display_style\":\"cascader_multi_select\",\"need_paging_query_value\":false,\"property_default_display\":true,\"property_values\":[{\"sell_property_value_id\":15350,\"sell_property_value_name\":\"1\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":176966,\"sell_property_value_name\":\"1.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":36315,\"sell_property_value_name\":\"2\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231233,\"sell_property_value_name\":\"2.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":7569,\"sell_property_value_name\":\"3\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231235,\"sell_property_value_name\":\"3.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":1639,\"sell_property_value_name\":\"4\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231236,\"sell_property_value_name\":\"4.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":16299,\"sell_property_value_name\":\"5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231239,\"sell_property_value_name\":\"5.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":38497,\"sell_property_value_name\":\"6\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":49324,\"sell_property_value_name\":\"6.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":1966,\"sell_property_value_name\":\"7\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231242,\"sell_property_value_name\":\"7.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":3981,\"sell_property_value_name\":\"8\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":52964,\"sell_property_value_name\":\"8.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":18103,\"sell_property_value_name\":\"9\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231247,\"sell_property_value_name\":\"9.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":28192,\"sell_property_value_name\":\"10\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231251,\"sell_property_value_name\":\"10.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":14984,\"sell_property_value_name\":\"11\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231252,\"sell_property_value_name\":\"11.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":9498,\"sell_property_value_name\":\"12\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":26656,\"sell_property_value_name\":\"13\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231254,\"sell_property_value_name\":\"13.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":23244,\"sell_property_value_name\":\"14\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209184,\"sell_property_value_name\":\"14.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":28193,\"sell_property_value_name\":\"15\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209138,\"sell_property_value_name\":\"15.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":5614,\"sell_property_value_name\":\"16\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209139,\"sell_property_value_name\":\"16.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":31214,\"sell_property_value_name\":\"17\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209140,\"sell_property_value_name\":\"17.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":24567,\"sell_property_value_name\":\"18\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209141,\"sell_property_value_name\":\"18.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":2033,\"sell_property_value_name\":\"19\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209142,\"sell_property_value_name\":\"19.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":25796,\"sell_property_value_name\":\"20\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209143,\"sell_property_value_name\":\"20.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":35844,\"sell_property_value_name\":\"21\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209144,\"sell_property_value_name\":\"21.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":3128,\"sell_property_value_name\":\"22\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209145,\"sell_property_value_name\":\"22.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55479,\"sell_property_value_name\":\"23\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209146,\"sell_property_value_name\":\"23.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":22767,\"sell_property_value_name\":\"24\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209147,\"sell_property_value_name\":\"24.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":28696,\"sell_property_value_name\":\"25\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55477,\"sell_property_value_name\":\"25.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":52965,\"sell_property_value_name\":\"26\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209149,\"sell_property_value_name\":\"26.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55476,\"sell_property_value_name\":\"27\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209150,\"sell_property_value_name\":\"27.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55450,\"sell_property_value_name\":\"28\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209151,\"sell_property_value_name\":\"28.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55475,\"sell_property_value_name\":\"29\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209152,\"sell_property_value_name\":\"29.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":38984,\"sell_property_value_name\":\"30\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209153,\"sell_property_value_name\":\"30.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55474,\"sell_property_value_name\":\"31\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209154,\"sell_property_value_name\":\"31.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55473,\"sell_property_value_name\":\"32\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209155,\"sell_property_value_name\":\"32.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55472,\"sell_property_value_name\":\"33\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209156,\"sell_property_value_name\":\"33.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55471,\"sell_property_value_name\":\"34\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209157,\"sell_property_value_name\":\"34.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":8087,\"sell_property_value_name\":\"35\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209158,\"sell_property_value_name\":\"35.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55469,\"sell_property_value_name\":\"36\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209159,\"sell_property_value_name\":\"36.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":9796,\"sell_property_value_name\":\"37\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209160,\"sell_property_value_name\":\"37.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55449,\"sell_property_value_name\":\"38\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209161,\"sell_property_value_name\":\"38.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55468,\"sell_property_value_name\":\"39\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55467,\"sell_property_value_name\":\"39.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":37088,\"sell_property_value_name\":\"40\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209162,\"sell_property_value_name\":\"40.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55466,\"sell_property_value_name\":\"41\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209163,\"sell_property_value_name\":\"41.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55448,\"sell_property_value_name\":\"42\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":209164,\"sell_property_value_name\":\"42.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55465,\"sell_property_value_name\":\"43\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231171,\"sell_property_value_name\":\"43.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55464,\"sell_property_value_name\":\"44\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231172,\"sell_property_value_name\":\"44.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55447,\"sell_property_value_name\":\"45\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231173,\"sell_property_value_name\":\"45.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":37974,\"sell_property_value_name\":\"46\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":210861,\"sell_property_value_name\":\"46.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55463,\"sell_property_value_name\":\"47\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55462,\"sell_property_value_name\":\"47.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":52438,\"sell_property_value_name\":\"48\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257091,\"sell_property_value_name\":\"18-19\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231208,\"sell_property_value_name\":\"48.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55461,\"sell_property_value_name\":\"49\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":231217,\"sell_property_value_name\":\"49.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":2012,\"sell_property_value_name\":\"50\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257084,\"sell_property_value_name\":\"19-20\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257085,\"sell_property_value_name\":\"20-21\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256986,\"sell_property_value_name\":\"21-22\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256987,\"sell_property_value_name\":\"22-23\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256988,\"sell_property_value_name\":\"23-24\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256989,\"sell_property_value_name\":\"24-25\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256982,\"sell_property_value_name\":\"50.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256990,\"sell_property_value_name\":\"25-26\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256991,\"sell_property_value_name\":\"26-27\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256992,\"sell_property_value_name\":\"27-28\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256993,\"sell_property_value_name\":\"28-29\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256994,\"sell_property_value_name\":\"29-30\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55459,\"sell_property_value_name\":\"51\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256995,\"sell_property_value_name\":\"30-31\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256996,\"sell_property_value_name\":\"31-32\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256997,\"sell_property_value_name\":\"32-33\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256998,\"sell_property_value_name\":\"33-34\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256999,\"sell_property_value_name\":\"34-35\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236009,\"sell_property_value_name\":\"35-36\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256983,\"sell_property_value_name\":\"51.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55458,\"sell_property_value_name\":\"52\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236010,\"sell_property_value_name\":\"36-37\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55457,\"sell_property_value_name\":\"52.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":52129,\"sell_property_value_name\":\"53\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236011,\"sell_property_value_name\":\"37-38\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256984,\"sell_property_value_name\":\"53.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55991,\"sell_property_value_name\":\"54\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236012,\"sell_property_value_name\":\"38-39\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":256985,\"sell_property_value_name\":\"54.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":55456,\"sell_property_value_name\":\"55\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236013,\"sell_property_value_name\":\"39-40\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236014,\"sell_property_value_name\":\"40-41\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236015,\"sell_property_value_name\":\"41-42\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236016,\"sell_property_value_name\":\"42-43\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236017,\"sell_property_value_name\":\"43-44\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236018,\"sell_property_value_name\":\"44-45\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236019,\"sell_property_value_name\":\"45-46\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236020,\"sell_property_value_name\":\"46-47\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236021,\"sell_property_value_name\":\"47-48\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236022,\"sell_property_value_name\":\"48-49\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":236023,\"sell_property_value_name\":\"49-50\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257086,\"sell_property_value_name\":\"50-51\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257464,\"sell_property_value_name\":\"51-52\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257465,\"sell_property_value_name\":\"52-53\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257726,\"sell_property_value_name\":\"男码19\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257803,\"sell_property_value_name\":\"女码19\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257727,\"sell_property_value_name\":\"男码19.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257804,\"sell_property_value_name\":\"女码19.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257728,\"sell_property_value_name\":\"男码20\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257805,\"sell_property_value_name\":\"女码20\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257729,\"sell_property_value_name\":\"男码20.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257806,\"sell_property_value_name\":\"女码20.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257730,\"sell_property_value_name\":\"男码21\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257807,\"sell_property_value_name\":\"女码21\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257731,\"sell_property_value_name\":\"男码21.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257808,\"sell_property_value_name\":\"女码21.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257732,\"sell_property_value_name\":\"男码22\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257809,\"sell_property_value_name\":\"女码22\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257735,\"sell_property_value_name\":\"男码22.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257810,\"sell_property_value_name\":\"女码22.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257736,\"sell_property_value_name\":\"男码23\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257811,\"sell_property_value_name\":\"女码23\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257737,\"sell_property_value_name\":\"男码23.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257812,\"sell_property_value_name\":\"女码23.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257738,\"sell_property_value_name\":\"男码24\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257813,\"sell_property_value_name\":\"女码24\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257739,\"sell_property_value_name\":\"男码24.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257814,\"sell_property_value_name\":\"女码24.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257740,\"sell_property_value_name\":\"男码25\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257815,\"sell_property_value_name\":\"女码25\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257741,\"sell_property_value_name\":\"男码25.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257816,\"sell_property_value_name\":\"女码25.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257742,\"sell_property_value_name\":\"男码26\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257817,\"sell_property_value_name\":\"女码26\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257743,\"sell_property_value_name\":\"男码26.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257818,\"sell_property_value_name\":\"女码26.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257744,\"sell_property_value_name\":\"男码27\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257819,\"sell_property_value_name\":\"女码27\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257745,\"sell_property_value_name\":\"男码27.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257820,\"sell_property_value_name\":\"女码27.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257746,\"sell_property_value_name\":\"男码28\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257821,\"sell_property_value_name\":\"女码28\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257747,\"sell_property_value_name\":\"男码28.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257822,\"sell_property_value_name\":\"女码28.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257748,\"sell_property_value_name\":\"男码29\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257823,\"sell_property_value_name\":\"女码29\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257749,\"sell_property_value_name\":\"男码29.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257824,\"sell_property_value_name\":\"女码29.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257750,\"sell_property_value_name\":\"男码30\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257825,\"sell_property_value_name\":\"女码30\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257751,\"sell_property_value_name\":\"男码30.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257826,\"sell_property_value_name\":\"女码30.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257752,\"sell_property_value_name\":\"男码31\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257827,\"sell_property_value_name\":\"女码31\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257753,\"sell_property_value_name\":\"男码31.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257828,\"sell_property_value_name\":\"女码31.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257756,\"sell_property_value_name\":\"男码32\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257829,\"sell_property_value_name\":\"女码32\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257757,\"sell_property_value_name\":\"男码32.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257830,\"sell_property_value_name\":\"女码32.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257758,\"sell_property_value_name\":\"男码33\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257831,\"sell_property_value_name\":\"女码33\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257759,\"sell_property_value_name\":\"男码33.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257832,\"sell_property_value_name\":\"女码33.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257760,\"sell_property_value_name\":\"男码34\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257833,\"sell_property_value_name\":\"女码34\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257761,\"sell_property_value_name\":\"男码34.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257834,\"sell_property_value_name\":\"女码34.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257762,\"sell_property_value_name\":\"男码35\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257835,\"sell_property_value_name\":\"女码35\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257763,\"sell_property_value_name\":\"男码35.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257836,\"sell_property_value_name\":\"女码35.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257764,\"sell_property_value_name\":\"男码36\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257837,\"sell_property_value_name\":\"女码36\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257765,\"sell_property_value_name\":\"男码36.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257838,\"sell_property_value_name\":\"女码36.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257766,\"sell_property_value_name\":\"男码37\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257839,\"sell_property_value_name\":\"女码37\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257767,\"sell_property_value_name\":\"男码37.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257840,\"sell_property_value_name\":\"女码37.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257768,\"sell_property_value_name\":\"男码38\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257841,\"sell_property_value_name\":\"女码38\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257769,\"sell_property_value_name\":\"男码38.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257842,\"sell_property_value_name\":\"女码38.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257770,\"sell_property_value_name\":\"男码39\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257843,\"sell_property_value_name\":\"女码39\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257771,\"sell_property_value_name\":\"男码39.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257844,\"sell_property_value_name\":\"女码39.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257772,\"sell_property_value_name\":\"男码40\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257845,\"sell_property_value_name\":\"女码40\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257773,\"sell_property_value_name\":\"男码40.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257846,\"sell_property_value_name\":\"女码40.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257774,\"sell_property_value_name\":\"男码41\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257847,\"sell_property_value_name\":\"女码41\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257775,\"sell_property_value_name\":\"男码41.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257848,\"sell_property_value_name\":\"女码41.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257776,\"sell_property_value_name\":\"男码42\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257849,\"sell_property_value_name\":\"女码42\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257777,\"sell_property_value_name\":\"男码42.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257850,\"sell_property_value_name\":\"女码42.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257778,\"sell_property_value_name\":\"男码43\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257851,\"sell_property_value_name\":\"女码43\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257779,\"sell_property_value_name\":\"男码43.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257852,\"sell_property_value_name\":\"女码43.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257780,\"sell_property_value_name\":\"男码44\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257853,\"sell_property_value_name\":\"女码44\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257781,\"sell_property_value_name\":\"男码44.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257854,\"sell_property_value_name\":\"女码44.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257782,\"sell_property_value_name\":\"男码45\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257855,\"sell_property_value_name\":\"女码45\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257783,\"sell_property_value_name\":\"男码45.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257856,\"sell_property_value_name\":\"女码45.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257784,\"sell_property_value_name\":\"男码46\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257857,\"sell_property_value_name\":\"女码46\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257785,\"sell_property_value_name\":\"男码46.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257858,\"sell_property_value_name\":\"女码46.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257786,\"sell_property_value_name\":\"男码47\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257859,\"sell_property_value_name\":\"女码47\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257787,\"sell_property_value_name\":\"男码47.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257860,\"sell_property_value_name\":\"女码47.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257788,\"sell_property_value_name\":\"男码48\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257861,\"sell_property_value_name\":\"女码48\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257789,\"sell_property_value_name\":\"男码48.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257862,\"sell_property_value_name\":\"女码48.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257790,\"sell_property_value_name\":\"男码49\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257863,\"sell_property_value_name\":\"女码49\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257791,\"sell_property_value_name\":\"男码49.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257864,\"sell_property_value_name\":\"女码49.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257792,\"sell_property_value_name\":\"男码50\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257865,\"sell_property_value_name\":\"女码50\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257793,\"sell_property_value_name\":\"男码50.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257866,\"sell_property_value_name\":\"女码50.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257794,\"sell_property_value_name\":\"男码51\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257867,\"sell_property_value_name\":\"女码51\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257795,\"sell_property_value_name\":\"男码51.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257868,\"sell_property_value_name\":\"女码51.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257796,\"sell_property_value_name\":\"男码52\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257869,\"sell_property_value_name\":\"女码52\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257797,\"sell_property_value_name\":\"男码52.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257870,\"sell_property_value_name\":\"女码52.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257798,\"sell_property_value_name\":\"男码53\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257871,\"sell_property_value_name\":\"女码53\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257799,\"sell_property_value_name\":\"男码53.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257872,\"sell_property_value_name\":\"女码53.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257800,\"sell_property_value_name\":\"男码54\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257873,\"sell_property_value_name\":\"女码54\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257801,\"sell_property_value_name\":\"男码54.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257874,\"sell_property_value_name\":\"女码54.5\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257802,\"sell_property_value_name\":\"男码55\",\"pic_value\":null,\"pic_type\":null},{\"sell_property_value_id\":257875,\"sell_property_value_name\":\"女码55\",\"pic_value\":null,\"pic_type\":null}],\"measure_templates\":[],\"navigation_properties\":[{\"property_id\":4705,\"property_name\":\"尺码类型\"},{\"property_id\":4709,\"property_name\":\"鞋码大小\"}]}]},\"sku_rule\":{\"sku_classification_rule\":{\"enable\":false,\"must_select\":false,\"options\":[]}}}";
            var productUpdateRuleInfo = cc.ToObject<ProductUpdateRuleInfoModel>();

            var skey = "listing";
            var targetShops = new ShopService(skey).GetShopByIds(new List<int>() { 1687 });
            var shop = targetShops.FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: skey);

            var service = new ZhiDianNewPlatformService(shop);
            var kk = new PlatformCategorySupplierService().GetNavigationCateProp(productUpdateRuleInfo, "22375", service);
        }


        // 获取sku导航属性
        [TestMethod]
        public void Test_RemoveAfterTilde()
        {
            var skey = "listing";
            var targetShops = new ShopService(skey).GetShopByIds(new List<int>() { 1687 });
            var shop = targetShops.FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: skey);

            string str = "";
            var service = new ZhiDianNewPlatformService(shop).RemoveAfterTilde("https://p3-aio.ecombdimg.com/obj/ecom-shop-material/jpeg_m_c53c4b3fa256a6ef8e998d9935caf179_sx_74107_www640-640~240x0.image", "123");
        }

        // 用来测试sku笛卡尔积计算是否正确
        [TestMethod]
        public void TranPlatformSkuFromSelfDefineSku()
        {
            var user = new UserFxService().Get(5);
            var site = new SiteContext(user);

            PtProductInfoService ptProductInfoService = new PtProductInfoService();
            List<PtProductInfoSkuModel> ptSkus = new List<PtProductInfoSkuModel>();
            var m1 = new PtProductInfoSkuModel()
            {
                Attribute = new PtProductSkuAttributeModel(),
                SkuCode = "at17304531920913",
                FromBaseProductSkuUid = "2000000500000249497",
                FromSupplierProductSkuUid = "6666666666",
                Attributes = "[{\"k\":\"规格1\",\"v\":\"白色\",\"c\":\"123\"},{\"k\":\"规格2\",\"v\":\"L\",\"c\":\"456\"}]"
            };
            m1.Attribute.AttributeName1 = "规格1";
            m1.Attribute.AttributeValue1 = "白色";
            m1.Attribute.AttributeCode1 = "123";
            m1.Attribute.AttributeName2 = "规格2";
            m1.Attribute.AttributeValue2 = "L";
            m1.Attribute.AttributeCode2 = "456";
            ptSkus.Add(m1);

            var m2 = new PtProductInfoSkuModel()
            {
                Attribute = new PtProductSkuAttributeModel(),
                SkuCode = "at17304529905372",
                FromBaseProductSkuUid = "2000000500000249498",
                FromSupplierProductSkuUid = "7777766666",
                Attributes = "[{\"k\":\"规格1\",\"v\":\"蓝色\",\"c\":\"iop\"},{\"k\":\"规格2\",\"v\":\"XL\",\"c\":\"jkl\"}]"
            };
            m2.Attribute.AttributeName1 = "规格1";
            m2.Attribute.AttributeValue1 = "蓝色";
            m2.Attribute.AttributeCode1 = "iop";

            m2.Attribute.AttributeName2 = "规格2";
            m2.Attribute.AttributeValue2 = "XL";
            m2.Attribute.AttributeCode2 = "jkl";
            ptSkus.Add(m2);
            var newptSkus = ptProductInfoService.TranSkuFission(ptSkus);
        }

        private ZhiDianNewPlatformService BuildSvc()
        {
            var targetShops = new ShopService().GetShopByIds(new List<int>() { 1687 });
            var shop = targetShops.FirstOrDefault();
            shop.AppKey = "7152331588175513101"; // 店管家_分销代发

            _service = new ZhiDianNewPlatformService(shop);
            PlatformFactory.GetPlatformService(shop);

            string ak = shop.AppKey;

            var service = new ZhiDianNewPlatformService(shop);
            return service;
        }

        /// <summary>
        /// 根据省获取全量四级地址
        /// </summary>
        [TestMethod]
        public void TestGetAreasByProvince()
        {
            BuildSvc().GetAreasByProvince();
        }

        /// <summary>
        /// 拒绝原因码列表接口
        /// </summary>
        [TestMethod]
        public void Test_GetRejectReasonCodeList()
        {
            List<AfterSaleRejectReason> saleAddressList = BuildSvc().GetRejectReasonList(null);
        }

        /// <summary>
        /// 获取——平台售后地址
        /// </summary>
        [TestMethod]
        public void Test_GetAddressList2()
        {
            // BuildSvc().GetAddressList();
            List<SellerAddressModel> sellerAddressesLsit = BuildSvc().GetAfterSaleAllAddres();
        }

        /// <summary>
        /// 批量查询地址库列表接口
        /// 创建——平台售后地址
        /// </summary>
        [TestMethod]
        public void Test_CreateAddress()
        {
            try
            {
                // 宇航的小店 48471462
                var address_id = BuildSvc().CreateAddress(new CreateAddressModel()
                {
                    address = new CreateAddressDetail()
                    {
                        province_id = 44,
                        city_id = 440300,
                        town_id = 440304,
                        street_id = 440304004,
                        detail = "国际文化大厦2407",
                        fixed_phone = "15799071811",
                        link_type = 0,
                        mobile = "15799071811",
                        user_name = "胡宇航",
                        remark = "测试用途"
                    }
                });
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 售后接口——聚合操作
        /// </summary>
        [TestMethod]
        public void TestAfterSaleOperate()
        {
            // 【未调试】
            BuildSvc().AfterSaleOperate(new AfterSaleOperateModel()
            {
                type = AfterSaleOperateEnum.AgreeOnlyRefund,
                items = new List<AfterSaleOperateItem>(),
                shopid = 1687
            });
        }

        /// <summary>
        /// 查询物流公司
        /// </summary>
        [TestMethod]
        public void TestGetLogisticList()
        {
            //var paramDic = new Dictionary<string, string>();
            //var result = _client.Execute("order.logisticsCompanyList", paramDic);

            var result = BuildSvc().GetLogisticList();
        }

        /// <summary>
        /// 查询平台地址，Code
        /// </summary>
        [TestMethod]
        public void TestGetPtAddressCode()
        {
            // Dictionary<名称，平台Id>
            var dic = new Dictionary<string, string>();
            new PlatformAreaCodeInfoRepository().GetPtAddressCode(dic, "广东省", "深圳市", "福田区", "福田街道", PlatformType.TouTiao.ToString());
        }


        /// <summary>
        /// 同步单个售后单详情
        /// </summary>
        [TestMethod]
        public void TestSyncOrderJson()
        {
            // var result0 = BuildSvc().SyncOrderJson("6943013810369140697"); // 订单编号：6943013810369140697  售后单号：146978282048608795
            // var result1 = BuildSvc().SyncAfterSaleOrderDetail("146978282048608795");
            // var result2 = BuildSvc().SyncAfterSaleOrderDetail("146978452302464459");
            // var result3 = BuildSvc().SyncAfterSaleOrderDetail("147014514828465070");

            var result4 = BuildSvc().SyncAfterSaleOrderDetail("147014780546203246");
        }

        /// <summary>
        /// 同步单个售后单详情
        /// 考拉好品
        /// </summary>
        [TestMethod]
        public void TestSyncOrderJson2()
        {
            var targetShops = new ShopService().GetShopByIds(new List<int>() { 1687 });
            var shop = targetShops.FirstOrDefault();
            shop.AppKey = "7152331588175513101"; // 店管家_分销代发

            _service = new ZhiDianNewPlatformService(shop);
            PlatformFactory.GetPlatformService(shop);

            shop.ShopExtension.AppSecret = CustomerConfig.TouTiaoAppSecret;
            shop.ShopExtension.AccessToken = shop.AccessToken = "mc6cbf666p1ic8py3i6jcwd0001n7684-11"; // 考拉好品

            var service = new ZhiDianNewPlatformService(shop);

            service.SyncAfterSaleOrderDetail("147014780546203246");
        }

        /// <summary>
        /// 查找厂家
        /// </summary>
        [TestMethod]
        public void TestSSearchForManufacturers()
        {
            List<int> fxuserId = new List<int>() { 5 };
            List<string> afterSaleCodeList = "09575b0cea1fceca,9bb438329e59c29d,1d6cb9c525d570ae".Split(',').ToList();

            // 本地找出模拟数据
            /*
             select top 10 asoi.AfterSaleCode FROM AfterSaleOrder ab  WITH(NOLOCK) 
                inner join AfterSaleOrderItem  asoi WITH(NOLOCK)
                on ab.afterSaleCode = asoi.afterSaleCode
                inner join LogicOrderItem  loi WITH(NOLOCK)
                on loi.OrderItemCode = asoi.OrderItemCode
                where ab.shopid = 1687
                order by ab.CreateTime desc;
             
            select OrderItemCode from AfterSaleOrderItem loi
             INNER JOIN FunStringToTable('09575b0cea1fceca,9bb438329e59c29d,1d6cb9c525d570ae',',') t
             on t.item = loi.AfterSaleCode;

            select loi.OrderItemCode as OrderCode, pfn.DownFxUserId as FxUserId ,lo.shopid FROM LogicOrder lo WITH(NOLOCK)
            INNER JOIN LogicOrderItem loi WITH(NOLOCK) ON lo.LogicOrderId = loi.LogicOrderId
            INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode = lo.PathFlowCode
            INNER JOIN FunStringToTable('030f03d899594dd3,be0712db8ace2584,9c7713f9a8062bb9',',') t on t.item = loi.OrderItemCode
            WHERE lo.ShopId = 1687 and pfn.FxUserId in (5)
             */

            // Dictionary《AfterSaleCode, 厂家Id》  如果厂家用户Id为0，则表示这个单，自己就是厂家
            var dic = new AfterSaleOrderRepository().SearchForManufacturers(fxuserId, afterSaleCodeList, 1687);
        }

        /// <summary>
        /// 调试，给指定店铺去同步
        /// </summary>
        [TestMethod]
        public void TestSyncOrderJsonByTouTiao()
        {
            var rp = new DianGuanJiaApp.Data.Repository.ShopRepository();
            _shop = rp.Get(" where Id=@id", new { id = 1687 })?.FirstOrDefault();
            _shop.ShopExtension = new ShopExtensionService().GetShopExtensionByShopId(_shop.Id, CustomerConfig.TouTiaoFxNewAppKey);
            _siteContext = new SiteContext(_shop, new SiteContextConfig() { NeedRelationShops = false, NeedShopExpireTime = false });
            _service = PlatformFactory.GetPlatformService(_shop) as ZhiDianNewPlatformService;
            _client = new ZhiDianNewApiClient(_shop);

            var p15370527521 = _service.SyncOrderJson("6944921250285491589");
        }

        /// <summary>
        /// 同步订单详情
        /// </summary>
        [TestMethod]
        public void SyncOrdersDetail()
        {
            var order = _service.SyncOrderNew("");
        }
    }
}
