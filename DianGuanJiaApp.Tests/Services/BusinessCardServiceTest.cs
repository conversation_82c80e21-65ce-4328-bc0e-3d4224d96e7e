using System.Collections.Generic;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Services.Services;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace DianGuanJiaApp.Tests.Services
{
    [TestClass]
    public class BusinessCardServiceTest
    {
        [TestInitialize]
        public void Init()
        {
        }

        [TestMethod]
        public void UpdateOrAddBusinessCardTest()
        {
            var model = new BusinessCardModel
            {
                CompanyInfoList = new List<BusinessCardCompanyInfo>
                {
                    new BusinessCardCompanyInfo
                    {
                        Id = 1,
                        CompanyName = "深圳店管家科技有限公司",
                        BusinessTerm = "2024-05-07",
                        USCC = "91440300MA5HDTLN3E",
                        Province = "广东省",
                        City = "深圳市",
                        County = "福田区",
                        Address = "国际文化大厦2402",
                        LicensePic = "http://example.com/license.jpg",
                        SubjectType = "企业",
                        IsLongTerm = true,
                    }
                },
                SendaddressList = new List<BusinessCardSendaddress>
                {
                    new BusinessCardSendaddress
                    {
                        Id = 1,
                        Province = "广东省",
                        City = "深圳市",
                        County = "福田区",
                        Address = "某个地址",
                        ReceiverName = "张三",
                        ReceiverContract = "123456789",
                        ReceiverTel = "12343321",
                        AddressCode = "TEST_ADDRESS_CODE_001" // 测试 AddressCode 字段
                    }
                },
                BusinesscontactList = new List<BusinessCardContact>
                {
                    new BusinessCardContact
                    {
                        Id = 1,
                        Name = "李四",
                        Tel = "987654321",
                        JobTitle = "经理",
                        ImageUrl = "http://example.com/qrcode.jpg"
                    }
                },
                AftersaleaddressList = new List<BusinessCardAfterSaleAddress>
                    {
                        new BusinessCardAfterSaleAddress
                            {
                                Id = 1,
                                Province = "广东省",
                                City = "深圳市",
                                County = "宝安区",
                                Address = "另一个地址",
                                ReceiverName = "王五",
                                ReceiverContract = "123123123",
                                ReceiverTel = "0755123456"
                            }
                    },
                PlatformtypeList = new List<BusinessCardPlatformType>
                {
                    new BusinessCardPlatformType
                    {
                        Id = 1,
                        PlatformName = "Taobao"
                    },
                    new BusinessCardPlatformType
                    {
                        Id = 2,
                        PlatformName = "TouTiao"
                    }
                },
                PaymentinfoList = new List<BusinessCardPaymentInfo>
                {
                    new BusinessCardPaymentInfo
                    {
                        Id = 1,
                        PaymentType = "银行卡号",
                        PaymentAccount = "*****************",
                        AccountName = "张三",
                        AccountBank = "广东省深圳市某银行分行"
                    },
                    new BusinessCardPaymentInfo
                    {
                        Id = 2,
                        PaymentType = "支付宝",
                        PaymentAccount = "<EMAIL>",
                        PayPicObj = "data:image/png;base64,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",
                    }   
                },
                ShopList = new List<BusinessCardShop>
                {
                    new BusinessCardShop
                    {
                        Id = 1,
                        ShopId = "123123",
                        Platform = "淘宝",
                        ShopName = "我的淘宝店",
                        Url = "http://taobao.com/myshop"
                    }
                },
                Aftesales = new BusinessCardAftesales
                {
                    Id = 1,
                    Content = "详细的售后服务内容"
                }
            };

            var service = new BusinessCardService();
            service.UpdateOrAddBusinessCard(model.BusinesscontactList, BusinessCardExtFlag.BusinessContact, 5);
            service.UpdateOrAddBusinessCard(model.CompanyInfoList, BusinessCardExtFlag.CompanyInfo, 5);
            service.UpdateOrAddBusinessCard(model.SendaddressList, BusinessCardExtFlag.SendAddress, 5);
            service.UpdateOrAddBusinessCard(model.AftersaleaddressList, BusinessCardExtFlag.AfterSaleAddress, 5);
            service.UpdateOrAddBusinessCard(model.PlatformtypeList, BusinessCardExtFlag.PlatformType, 5);
            service.UpdateOrAddBusinessCard(model.PaymentinfoList, BusinessCardExtFlag.PaymentInfo, 5);
            service.UpdateOrAddBusinessCard(model.ShopList, BusinessCardExtFlag.Shop, 5);
            service.UpdateOrAddBusinessCard(new List<BusinessCardAftesales> { model.Aftesales }, BusinessCardExtFlag.AfteSales, 5);
        }

        [TestMethod]
        public void GetListByFxUserIdTest()
        {
            var service = new BusinessCardService();
            var result = service.GetListByFxUserId(5);
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void SyncMyShopTest()
        {
            var service = new BusinessCardService();
            var result = service.SyncMyShop(5);
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void UpdateOrAddBusinessCardWithAddressCodeTest()
        {
            // 测试带有 AddressCode 的名片发货地址更新
            var sendAddressList = new List<BusinessCardSendaddress>
            {
                new BusinessCardSendaddress
                {
                    Id = 1,
                    Province = "广东省",
                    City = "深圳市",
                    County = "福田区",
                    Address = "测试地址123号",
                    ReceiverName = "测试用户",
                    ReceiverContract = "***********",
                    ReceiverTel = "0755-12345678",
                    AddressCode = "TEST_ADDR_CODE_123" // 包含 AddressCode
                }
            };

            var service = new BusinessCardService();

            // 测试更新或添加包含 AddressCode 的发货地址
            service.UpdateOrAddBusinessCard(sendAddressList, BusinessCardExtFlag.SendAddress, 6);

            // 验证地址是否正确保存（这里只是基本的调用测试，实际验证需要数据库支持）
            Assert.IsTrue(true, "AddressCode 字段处理测试完成");
        }
    }
}
