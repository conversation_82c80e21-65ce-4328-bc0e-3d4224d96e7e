using System;
using System.Collections.Generic;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Services.ServicesExtension;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace DianGuanJiaApp.Services.Services.DataEventTracking.Tests
{
    [TestClass()]
    public class BusinessLogDataEventTrackingServiceTests
    {
        [TestInitialize]
        public void Init()
        {
            RedisConfigService.Initialization();
        }
        [TestMethod()]
        public void WriteLogTest()
        {
            BusinessLogDataEventTrackingService.Instance.WriteLog(new List<BusinessLogModel>
            {
                new BusinessLogModel
                {
                    BatchId = Guid.NewGuid().ToString()
                }
            });
        }
        [TestMethod]
        public void GetLogsTest()
        {
            var logs = InvokeApiDataTrackingService.Instance.GetLogs(
                "RequestContent: 6929988096154670180 and PlatformType : TouTiao and Url: order.logisticsAdd*");
        }

        [TestMethod]
        public void GetOrderSendTest()
        {
            var logs = OrderSendInvokeApiDataTrackingService.Instance.GetLogs(
                    "RequestContent: 6929988096154670181");
        }

        [TestMethod]
        public void PushOrderSendLogTest()
        {
            OrderSendInvokeApiDataTrackingService.Instance.WriteLog(new Data.Model.InvokeApiDataLogModel()
            {
                ResponseContent = "{\"OrderId\":\"6929988096154670181\"}",
                PlatformType = "TouTiao"
            });
            Console.ReadLine();
        }


        [TestMethod]
        public void GetOnlineSendLogsTest()
        {
            var query =
                "BusinessType : OnlineSend and SubBusinessType: SendHistoryCompleteness and CloudPlatformType: TouTiao  and BusinessId: b5b2f70bb62c4fccb949d280cae744bb and (SourceType:0 or SourceType:1 or SourceType:-1) and 6929988318036497535";
            var logs = BusinessLogDataEventTrackingService.Instance.GetOnlineSendLogs(query);
        }

        [TestMethod]
        public void GetTraceDataLogsTest()
        {
            var logs = TraceDataTrackingService.Instance.GetLogs("BusinessId:4037794454381486407");
        }

        [TestMethod]
        public void WriteQueueMonitorLogTest()
        {
            var models = new List<ServiceQueueMonitorLogModel>
            {
                new ServiceQueueMonitorLogModel
                {
                    BusinessType = "SyncOrder",
                    Queues = 90,
                    Env = "Gray"
                }
            };
            ServiceQueueMonitorLogDataEventTrackingService.Instance.WriteLog(models);
            Console.ReadLine();
        }
    }
}