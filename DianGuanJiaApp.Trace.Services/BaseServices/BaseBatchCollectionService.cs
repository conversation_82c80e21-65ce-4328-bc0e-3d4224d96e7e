using System.Collections.Generic;
using System;
using System.Linq;
using DianGuanJiaApp.Trace.ViewModels.Models;
using DianGuan<PERSON>iaApp.ViewModels.Models;
using System.Threading;
using DianGuanJiaApp.Core.Config;
using DianGuanJiaApp.Trace.Services.BaseInterfaces;

namespace DianGuanJiaApp.Trace.Services.BaseServices
{
    public class BaseBatchCollectionService<T> : BaseTraceService, IBaseTraceCollectionService<T> where T : BatchBaseModel
    {
        /// <summary>
        /// 操作类型（收集开关按照业务控制）
        /// </summary>
        protected string OperationType
        {
            get;
            set;
        }
        /// <summary>
        /// 业务名称（收集开关按照业务控制）
        /// </summary>
        protected string BusinessName
        {
            get;
            set;
        }
        /// <summary>
        /// 单个收集
        /// </summary>
        /// <param name="model"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public ReturnedModel Collect(T model)
        {
            return ExecuteHandler(() =>
            {
                //判空处理
                if (DeclareModel == null)
                {
                    throw new ArgumentNullException("消息中间件申明信息为空.");
                }

                if (model == null)
                {
                    throw new ArgumentNullException("收集信息为空.");
                }
                //设置主机名称
                model.HostName = Environment.MachineName;
                //是否开启收集
                if (!IsEnabledCollect(model.OperationType))
                {
                    return new ReturnedModel();
                }
                //异步线程
                ThreadPool.QueueUserWorkItem(c =>
                {
                    ExecuteHandler(() =>
                    {
                        //推送到消息中间件
                        // RabbitMqChannelQueuePool.SendMessage(SerializeObject(model),
                        //     new MessageDescription
                        //     {
                        //         ExchangeName = DeclareModel.Exchange,
                        //         QueueName = DeclareModel.Queue,
                        //         RouterName = DeclareModel.RoutingKey
                        //     });
                    }, e =>
                    {
                        ExceptionFunc(e);
                    });
                });
                //返回
                return new ReturnedModel();
            }, ExceptionFunc);
        }

        /// <summary>
        /// 批量收集（内部循环收集）
        /// </summary>
        /// <param name="models"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public ReturnedModel Collect(List<T> models)
        {
            return ExecuteHandler(() =>
            {
                //判空处理
                if (DeclareModel == null)
                {
                    throw new ArgumentNullException("消息中间件申明信息为空.");
                }

                if (models == null || !models.Any())
                {
                    throw new ArgumentNullException("收集信息为空.");
                }
                var firstModel = models.First();
                //是否开启收集
                if (!IsEnabledCollect(firstModel.OperationType))
                {
                    return new ReturnedModel();
                }
                //异步线程
                ThreadPool.QueueUserWorkItem(c =>
                {
                    ExecuteHandler(() =>
                    {
                        models.ForEach(model =>
                        {
                            //设置主机名称
                            model.HostName = Environment.MachineName;
                            //创建MQ连接池
                            // RabbitMqChannelQueuePool.SendMessage(SerializeObject(model),
                            //     new MessageDescription
                            //     {
                            //         ExchangeName = DeclareModel.Exchange,
                            //         QueueName = DeclareModel.Queue,
                            //         RouterName = DeclareModel.RoutingKey
                            //     });
                        });
                    }, e =>
                    {
                        ExceptionFunc(e);
                    });

                });
                //返回
                return new ReturnedModel();
            }, ExceptionFunc);
        }

        /// <summary>
        /// 异常处理
        /// </summary>
        /// <param name="e"></param>
        /// <returns></returns>
        private ReturnedModel ExceptionFunc(Exception e)
        {
            //写日志
            ExceptionLogClient.PutLog(e, "收集信息时异常");
            //返回
            return new ReturnedModel
            {
                Success = false,
                Status = ReturnedStatus.Error,
                Message = e.Message
            };
        }

        public bool IsEnabledCollect(string operationType)
        {
            WriteDebugLog("检查是否开启批次收集");
            //业务名称
            var businessName =
                $"{BusinessNames.TraceCollect}:{(string.IsNullOrWhiteSpace(BusinessName) ? operationType : BusinessName)}";
            //是否开启全局开关
            var globalSwitchKey =
                CacheKeys.BatchCollectGlobalSwitchKey.Replace("{SystemName}",
                    SystemConfig.SystemName)
                    .Replace("{BusinessName}", businessName);
            var globalSwitch = GetSwitchValue(globalSwitchKey);
            if (!globalSwitch)
            {
                WriteDebugLog("检查是否开启批次收集，未开启");
                return false;
            }
            WriteDebugLog("检查是否开启批次收集，开启");
            return true;
        }
    }
}