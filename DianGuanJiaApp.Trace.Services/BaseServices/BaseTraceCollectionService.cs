using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using DianGuanJiaApp.Core.Config;
using DianGuanJiaApp.Trace.Services.BaseInterfaces;
using DianGuanJiaApp.Trace.ViewModels.Models;
using DianGuanJiaApp.ViewModels.Models;

namespace DianGuanJiaApp.Trace.Services.BaseServices
{
    public class BaseTraceCollectionService<T> : BaseTraceService, IBaseTraceCollectionService<T> where T : TraceBaseModel
    {
        /// <summary>
        /// 系统名称（主要是为MQ虚拟机）
        /// </summary>
        protected string SystemName
        {
            get;
            set;
        }
        /// <summary>
        /// 操作类型（收集开关按照业务控制）
        /// </summary>
        protected string OperationType
        {
            get;
            set;
        }
        /// <summary>
        /// 业务名称（收集开关按照业务控制）
        /// </summary>
        protected string BusinessName
        {
            get;
            set;
        }
        /// <summary>
        /// 单个收集
        /// </summary>
        /// <param name="model"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public ReturnedModel Collect(T model)
        {
            return ExecuteHandler(() =>
            {
                //判空处理
                if (DeclareModel == null)
                {
                    throw new ArgumentNullException("消息中间件申明信息为空.");
                }

                if (model == null)
                {
                    throw new ArgumentNullException("收集信息为空.");
                }
                //设置主机名称
                model.HostName = Environment.MachineName;
                //是否开启收集
                if (!IsEnabledCollect(model.OperationType, model.ShopId))
                {
                    return new ReturnedModel();
                }
                //异步线程
                ThreadPool.QueueUserWorkItem(c =>
                {
                    ExecuteHandler(() =>
                    {
                        //推送到消息中间件
                        // RabbitMqChannelQueuePool.SendMessage(SerializeObject(model),
                        //     new MessageDescription
                        //     {
                        //         ExchangeName = DeclareModel.Exchange,
                        //         QueueName = DeclareModel.Queue,
                        //         RouterName = DeclareModel.RoutingKey
                        //     });
                    }, e =>
                    {
                        ExceptionFunc(e);
                    });
                });
                //返回
                return new ReturnedModel();
            }, ExceptionFunc);
        }

        /// <summary>
        /// 批量收集（内部循环收集）
        /// </summary>
        /// <param name="models"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public ReturnedModel Collect(List<T> models)
        {
            return ExecuteHandler(() =>
            {
                //判空处理
                if (DeclareModel == null)
                {
                    throw new ArgumentNullException("消息中间件申明信息为空.");
                }

                if (models == null || !models.Any())
                {
                    throw new ArgumentNullException("收集信息为空.");
                }

                //是否开启收集
                var firstModel = models.First();
                if (!IsEnabledCollect(firstModel.OperationType, firstModel.ShopId))
                {
                    return new ReturnedModel();
                }
                //异步线程
                ThreadPool.QueueUserWorkItem(c =>
                {
                    ExecuteHandler(() =>
                    {
                        models.ForEach(model =>
                        {
                            //设置主机名称
                            model.HostName = Environment.MachineName;
                            //创建MQ连接池
                            // RabbitMqChannelQueuePool.SendMessage(SerializeObject(model),
                            //     new MessageDescription
                            //     {
                            //         ExchangeName = DeclareModel.Exchange,
                            //         QueueName = DeclareModel.Queue,
                            //         RouterName = DeclareModel.RoutingKey
                            //     });
                        });
                    }, e =>
                    {
                        ExceptionFunc(e);
                    });

                });
                //返回
                return new ReturnedModel();
            }, ExceptionFunc);
        }

        /// <summary>
        /// 异常处理
        /// </summary>
        /// <param name="e"></param>
        /// <returns></returns>
        private ReturnedModel ExceptionFunc(Exception e)
        {
            //写日志
            ExceptionLogClient.PutLog(e, "收集信息时异常");
            //返回
            return new ReturnedModel
            {
                Success = false,
                Status = ReturnedStatus.Error,
                Message = e.Message
            };
        }

        /// <summary>
        /// 是否开启收集
        /// </summary>
        /// <param name="operationType"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public bool IsEnabledCollect(string operationType, int shopId)
        {
            //系统名称
            var systemName = string.IsNullOrWhiteSpace(SystemName) ? SystemConfig.SystemName : SystemName;
            //业务名称
            var businessName =
                $"{BusinessNames.TraceCollect}:{(string.IsNullOrWhiteSpace(BusinessName) ? operationType : BusinessName)}";
            //是否开启全局开关
            var globalSwitchKey =
                CacheKeys.TraceCollectGlobalSwitchKey.Replace("{SystemName}", systemName)
                    .Replace("{BusinessName}", businessName);
            var globalSwitch = GetSwitchValue(globalSwitchKey);
            if (!globalSwitch)
            {
                return false;
            }
            //是否开启指定店铺开关
            var assignShopSwitchKey =
                CacheKeys.TraceCollectAssignShopSwitchKey.Replace("{SystemName}", systemName)
                    .Replace("{BusinessName}", businessName);
            var assignShopSwitch = GetSwitchValue(assignShopSwitchKey);
            if (!assignShopSwitch)
            {
                return true;
            }

            var shopSwitchKey = CacheKeys.TraceCollectShopSwitchKey.Replace("{SystemName}", systemName)
                .Replace("{BusinessName}", businessName);
            var shopSwitch = HGetSwitchValue(shopSwitchKey, shopId.ToString());
            return shopSwitch;
        }

        private string GetVirtualHost()
        {
            if (string.IsNullOrWhiteSpace(SystemName))
            {
                return null;
            }

            return $"dgjapp.{SystemName.ToLower()}";
        }
    }
}