using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Web;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Net;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Threading;
using System.Text;
using NPOI.SS.Formula.Functions;

namespace DianGuanJiaApp.Utility
{
    /// <summary>
    /// 常用的字符串处理函数
    /// </summary>
    public static class CustomerConfig
    {
        /// <summary>
        ///  京东云鼎 Key
        /// </summary>
        public static string JdCloudAccessKeyId
        {
            get
            {
                var val = ConfigurationManager.AppSettings.Get("JdCloudAccessKeyId");

                if (val.IsNotNullOrEmpty())
                {
                    return val;
                }

                return "JDC_F5FDF7CFC0683FA85E0BEB980E58";
            }
        }

        /// <summary>
        /// 京东云鼎 Secret
        /// </summary>
        public static string JdCloudSecretKey
        {
            get
            {
                var val = ConfigurationManager.AppSettings.Get("JdCloudSecretKey");
                
                if (val.IsNotNullOrEmpty())
                {
                    return val;
                }

                return "B7B120C880D34FAF8B76EC80B9E5845D";
            }
        }

        /// <summary>
        /// 按平台查连接
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        private static string GetConnectStringss(string platformType)
        {
            string connectString = string.Empty;
            connectString = NewAlibabaConnectionString;

            switch (platformType.ToLower())
            {
                case "1688":
                case "alibaba":
                case "alibabac2m":
                case "c2m":
                    connectString = NewAlibabaConnectionString;
                    break;
                case "pinduoduo":
                case "pdd":
                case "kuaituantuan":
                case "pddktt":
                    connectString = NewPddPrintDBConnectionString;
                    break;
                case "taobao":
                case "taobaosupplier":
                case "tb":
                    connectString = NewTaobaoPrintDBConnectionString;
                    break;
                case "jingdong":
                case "jd":
                    connectString = NewJdPrintDBConnectionString;
                    break;
                case "off":
                case "ol":
                case "weishang":
                case "ws":
                case "offline":
                    //微商版本
                    connectString = NewWeiShangPrintDBConnectionString;
                    break;
                case "xiaodian":
                case "xd":
                case "mogujie":
                case "mg":
                case "meilishuo":
                case "mls":
                case "youzan":
                case "yz":
                case "weidian":
                case "wd":
                case "weimeng":
                case "wm":
                case "vipshop":
                case "vshop":
                case "toutiao":
                case "tt":
                case "zhidian":
                case "zd":
                case "douyinxiaodian":
                case "dyxd":
                case "toutiaoxiaodian":
                case "ttxd":
                case "luban":
                case "lb":
                case "suning":
                case "sn":
                case "mengtui":
                case "mt":
                case "yunji":
                case "yj":
                case "open":
                case "openv1":
                case "openv2":
                case "kuaishou":
                case "ks":
                case "kuaishousupplier":
                case "beibei":
                case "bb":
                case "other":
                case "xiaohongshu":
                case "xhs":
                case "wxxiaoshangdian":
                case "wxvideo":
                case "wxxsd":
                case "mokuai":
                case "mk":
                case "duxiaodian":
                case "dxd":
                case "tuanhaohuo":
                case "thh":
                case "alibabaufuwu":
                    connectString = NewOhterPrintDBConnectionString;
                    break;
            }
            return connectString;
        }

        /// <summary>
        /// 根据 店铺平台 返回 对应站点域名
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static string GetShopRedirectLink(string platformType, string version = "", string venderId = "")
        {
            var link = CustomerConfig.ApplicationWebUrl;
            if (string.IsNullOrEmpty(platformType))
                platformType = "";
            switch (platformType.ToLower())
            {
                case "1688":
                case "alibaba":
                    link = $"{CustomerConfig.AlibabaNewSystemLink}";
                    break;
                case "1688c2m":
                case "alibabac2m":
                    link = $"{CustomerConfig.AlibabaC2MNewSystemLink}";
                    break;
                case "pinduoduo":
                case "pdd":
                    link = $"{CustomerConfig.PinduoduoNewSystemLink}";
                    break;
                case "taobao":
                case "tb":
                    link = $"{CustomerConfig.TaobaoNewSystemLink}";
                    break;
                case "xiaodian":
                case "xd":
                    link = $"{CustomerConfig.XiaoDianNewSystemLink}";
                    break;
                case "mogujie":
                case "mg":
                    link = $"{CustomerConfig.MoGuJieNewSystemLink}";
                    break;
                case "meilishuo":
                case "mls":
                    link = $"{CustomerConfig.MeiLiShuoNewSystemLink}";
                    break;
                case "youzan":
                case "yz":
                    link = $"{CustomerConfig.YouZanNewSystemLink}";
                    break;
                case "weidian":
                case "wd":
                    link = $"{CustomerConfig.WeiDianNewSystemLink}";
                    break;
                case "weimeng":
                case "wm":
                    link = $"{CustomerConfig.WeiMengNewSystemLink}";
                    break;
                case "vipshop":
                case "vshop":
                    link = $"{CustomerConfig.VipShopNewSystemLink}";
                    break;
                case "suning":
                case "sn":
                    link = $"{CustomerConfig.SuningNewSystemLink}";
                    break;
                case "toutiao":
                case "tt":
                case "zhidian":
                case "zd":
                case "douyinxiaodian":
                case "dyxd":
                case "toutiaoxiaodian":
                case "ttxd":
                case "luban":
                case "lb":
                    link = $"{CustomerConfig.ZhiDianNewSystemLink}";
                    break;
                case "open":
                case "openv1":
                case "openv2":
                    link = $"{CustomerConfig.OpenV1NewSystemLink}";
                    break;
                case "jingdong":
                case "jd":
                    link = $"{CustomerConfig.JingDongNewSystemLink}";
                    break;
                case "mengtui":
                case "mt":
                    link = $"{CustomerConfig.MengTuiNewSystemLink}";
                    break;
                case "yunji":
                case "yj":
                    link = $"{CustomerConfig.YunJiNewSystemLink}";
                    break;
                case "kuaishou":
                case "ks":
                    link = $"{CustomerConfig.KuaiShouNewSystemLink}";
                    break;
                case "beibei":
                case "bb":
                    link = $"{CustomerConfig.BeiBeiNewSystemLink}";
                    break;
                case "xiaohongshu":
                case "xhs":
                    link = $"{CustomerConfig.XiaoHongShuNewSystemLink}";
                    break;
                case "wxxiaoshangdian":
                case "wxxsd":
                    link = $"{CustomerConfig.WxXiaoShangDianNewSystemLink}";
                    break;
                case "wxvideo":
                    link = $"{CustomerConfig.WxVideoDianNewSystemLink}";
                    break;
                case "mokuai":
                case "mk":
                    link = $"{CustomerConfig.MoKuaiNewSystemLink}";
                    break;
                case "duxiaodian":
                case "dxd":
                    link = $"{CustomerConfig.DuXiaoDianNewSystemLink}";
                    break;
                case "tuanhaohuo":
                case "thh":
                    link = $"{CustomerConfig.TuanHaoHuoNewSystemLink}";
                    break;
                case "kuaituantuan":
                case "pttktt":
                    link = $"{CustomerConfig.KuaiTuanTuanNewSystemLink}";
                    break;
                case "offline":
                    link = $"{CustomerConfig.WeiShangLink}";
                    break;
                case "system":
                    link = $"{CustomerConfig.DefaultFenFaSystemUrl}";
                    break;
            }
            if (!string.IsNullOrEmpty(version) && !link.IsLocalHost() && !link.IsIp())
            {
                var arrs = link.Split('.');
                if (arrs != null && arrs.Length > 0)
                {
                    //版本“2”被老系统占用了，排除掉，拼多多迁移后版本为3正式、4灰度
                    //目前仅有3个版本，1为灰度版本，3为功能版本，其他值就是正式版本了
                    if (version == "1" || version.ToInt() > 2)
                    {
                        arrs[0] += version;
                        link = string.Join(".", arrs);
                    }
                }
            }
            //if (platformType == "Pinduoduo" && venderId == "2")
            //{
            //    link = CustomerConfig.PinduoduoC2MLink;
            //}
            return link;
        }

        /// <summary>
        /// 根据 店铺平台 返回 对应分单系统的域名
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static string GetFenFaRedirectLink(string platformType, string version = "")
        {
            var link = CustomerConfig.DefaultFenFaSystemUrl;
            if (string.IsNullOrEmpty(platformType))
                platformType = "";
            switch (platformType.ToLower())
            {
                case "pinduoduo":
                case "pdd":
                case "kuaituantuan":
                case "pddktt":
                    link = $"{CustomerConfig.PinduoduoFenFaSystemUrl}";
                    break;
                case "jingdong":
                case "jd":
                    link = $"{CustomerConfig.JingdongFenFaSystemUrl}";
                    break;
            }
            if (!string.IsNullOrEmpty(version) && !link.IsLocalHost() && !link.IsIp())
            {
                var arrs = link.Split('.');
                if (arrs != null && arrs.Length > 0)
                {
                    //版本“2”被老系统占用了，排除掉，拼多多迁移后版本为3正式、4灰度
                    //目前仅有3个版本，1为灰度版本，3为功能版本，其他值就是正式版本了
                    if (version == "1" || version.ToInt() > 2)
                    {
                        arrs[0] += version;
                        link = string.Join(".", arrs);
                    }
                }
            }
            return link;
        }


        /// <summary>
        /// 根据 店铺平台 返回 对应平台的授权地址
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static string GetShopAuthLink(string platformType)
        {
            var link = string.Empty;
            if (string.IsNullOrEmpty(platformType))
                platformType = "";
            switch (platformType.ToLower())
            {
                case "1688":
                case "alibaba":
                    link = $"{CustomerConfig.AlibabaOldSystemLink}";
                    break;
                case "c2m":
                case "alibabac2m":
                    link = $"{CustomerConfig.AlibabaC2MNewSystemLink}";
                    break;
                case "pinduoduo":
                case "pdd":
                    link = $"{CustomerConfig.PinduoduoOldSystemLink}";
                    break;
                case "taobao":
                case "tb":
                    link = $"{CustomerConfig.TaobaoOldSystemLink}";
                    break;
                case "taobaosupplier":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/taobaosupplier";
                    break;
                case "xiaodian":
                case "xd":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/xiaodian";
                    break;
                case "mogujie":
                case "mg":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/mogujie";
                    break;
                case "meilishuo":
                case "mls":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/meilishuo";
                    break;
                case "youzan":
                case "yz":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/youzan";
                    break;
                case "weidian":
                case "wd":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/weidian";
                    break;
                case "weimeng":
                case "wm":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/weimeng";
                    break;
                case "kaola":
                case "kl":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/kaola";
                    break;
                case "vipshop":
                case "vshop":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/vipshop";
                    break;
                case "suning":
                case "sn":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/suning";
                    break;
                case "toutiao":
                case "tt":
                case "zhidian":
                case "zd":
                case "douyinxiaodian":
                case "dyxd":
                case "toutiaoxiaodian":
                case "ttxd":
                case "luban":
                case "lb":
                case "open":
                    //case "openv1":
                    //case "openv2":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/zhidian";
                    link = "https://fxg.jinritemai.com/index.html#/ffa/open/serviceAuthorizeManage?page=1&size=10&tab=";
                    break;
                case "toutiaofenfa":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/douyinfenfa";
                    break;
                case "toutiaogongyingshang":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/douyingongyingshang";
                    break;
                case "jingdong":
                case "jd":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/jingdong";
                    break;
                case "mengtui":
                case "mt":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/mengtui";
                    break;
                case "yunji":
                case "yj":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/yunji";
                    break;
                case "kuaishou":
                case "ks":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/kuaishou";
                    break;
                case "kuaishousupplier":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/kuaishousupplier";
                    break;
                case "xiaohongshu":
                case "xhs":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/xiaohongshuv2fx";
                    break;
                case "wxxiaoshangdian":
                case "wxxsd":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/wxxiaoshangdian";
                    break;
                case "wxvideo":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/wxvideo";
                    break;
                case "mokuai":
                case "mk":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/mokuai";
                    break;
                case "duxiaodian":
                case "dxd":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/duxiaodian";
                    break;
                case "tuanhaohuo":
                case "thh":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/tuanhaohuo";
                    break;
                case "alibabaufuwu":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/alibabaufuwu";
                    break;
                case "kuaituantuan":
                case "pddktt":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/pddktt";
                    break;
                case "offline":
                    link = $"{CustomerConfig.PortalLink}";
                    break;
                case "*":
                case "all":
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/"; //选择平台页面
                    break;
                default:
                    link = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/"; //选择平台页面
                    break;
            }
            return link;
        }

        /// <summary>
        /// 站点支持的平台
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static string ConvertPlatformType(string platformType)
        {
            if (string.IsNullOrWhiteSpace(platformType))
            {
                throw new LogicException("当前站点平台不能为空", "PlatformTypeParseFaild");
            }
            var platform = string.Empty;
            switch (platformType.ToLower())
            {
                case "1688":
                case "alibaba":
                    platform = "Alibaba";
                    break;
                case "c2m":
                case "alibabac2m":
                    platform = "AlibabaC2M";
                    break;
                case "pinduoduo":
                case "pdd":
                    platform = "Pinduoduo";
                    break;
                case "kuaituantuan":
                case "pddktt":
                    platform = "KuaiTuanTuan";
                    break;
                case "taobao":
                case "tb":
                    platform = "Taobao";
                    break;
                case "jingdong":
                case "jd":
                    platform = "Jingdong";
                    break;
                case "offline":
                case "weishang":
                case "ws":
                case "ol":
                    platform = "Offline";
                    break;
                case "other":
                    platform = "XiaoDian|MoGuJie|MeiLiShuo|YouZan|WeiDian|WeiMeng|KaoLa|VipShop|TouTiao|ZhiDian|DouYinXiaoDian|TouTiaoXiaoDian|LuBan|Suning|MengTui|YunJi|Open|OpenV1|OpenV2|KuaiShou|BeiBei|XiaoHongShu|WxXiaoShangDian|WxVideo|MoKuai|DuXiaoDian|TuanHaoHuo";
                    break;
                case "*":
                    platform = "*";
                    break;
                default:
                    platform = platformType;
                    //throw new LogicException("当前站点平台未能解析", "PlatformTypeParseFaild");
                    break;
            }
            return platform;
        }


        /// <summary>
        /// 平台类型 转 汉字
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static string ConvertPlatformTypeToName(string platformType)
        {
            var name = string.Empty;
            switch (platformType.ToString2().ToLower())
            {
                case "1688":
                case "alibaba":
                    name = $"1688";
                    break;
                case "c2m":
                case "alibabac2m":
                    name = $"淘工厂";
                    break;
                case "pinduoduo":
                case "pdd":
                    name = $"拼多多";
                    break;
                case "taobao":
                case "tb":
                    name = $"淘宝";
                    break;
                case "taobaosupplier":
                    name = $"淘宝供货商";
                    break;
                case "xiaodian":
                case "xd":
                    name = $"小店";
                    break;
                case "mogujie":
                case "mg":
                    name = $"蘑菇街";
                    break;
                case "meilishuo":
                case "mls":
                    name = $"美丽说";
                    break;
                case "youzan":
                case "yz":
                    name = $"有赞";
                    break;
                case "weidian":
                case "wd":
                    name = $"微店";
                    break;
                case "weimeng":
                case "wm":
                    name = $"微盟";
                    break;
                case "kaola":
                case "kl":
                    name = $"考拉";
                    break;
                case "vipshop":
                case "vshop":
                    name = $"唯品会";
                    break;
                case "toutiao":
                    name = $"抖店";
                    break;
                case "tt":
                    name = $"头条系";
                    break;
                case "zhidian":
                case "zd":
                    name = $"值点";
                    break;
                case "douyinxiaodian":
                case "dyxd":
                    name = $"抖音小店";
                    break;
                case "toutiaoxiaodian":
                case "ttxd":
                    name = $"头条小店";
                    break;
                case "luban":
                case "lb":
                    name = $"鲁班";
                    break;
                case "toutiaofenfa":
                    name = $"厂商代发";
                    break;
                case "toutiaogongyingshang":
                    name = $"头条供应商";
                    break;
                case "suning":
                case "sn":
                    name = $"苏宁易购";
                    break;
                case "jingdong":
                case "jd":
                    name = $"京东";
                    break;
                case "jingdongpurchase":
                    name = $"京东供销平台";
                    break;
                case "offline":
                case "weishang":
                case "ws":
                case "ol":
                    name = $"微商";
                    break;
                case "mengtui":
                case "mt":
                    name = $"萌推";
                    break;
                case "yunji":
                case "yj":
                    name = $"云集";
                    break;
                case "open":
                case "openv1":
                case "openv2":
                    name = $"开放平台";
                    break;
                case "kuaishou":
                case "ks":
                    name = $"快手小店";
                    break;
                case "kuaishousupplier":
                    name = $"厂商代发";
                    break;
                case "xiaohongshu":
                case "xhs":
                    name = $"小红书";
                    break;
                case "wxxiaoshangdian":
                case "wxxsd":
                    name = "小商店";
                    break;
                case "wxvideo":
                    name = "视频号（微信小店）";
                    //name = "微信视频号";
                    break;
                case "mokuai":
                case "mk":
                    name = $"魔筷星选";
                    break;
                case "duxiaodian":
                case "dxd":
                    name = $"度小店";
                    break;
                case "tuanhaohuo":
                case "thh":
                    name = $"团好货";
                    break;
                case "kuaituantuan":
                case "pddktt":
                    name = $"快团团";
                    break;
                case "system":
                    name = "订单分发";
                    break;
                case "alibabazhuke":
                    name = $"阿里巴巴主客";
                    break;
                case "virtual":
                    name = "线下单";
                    break;
                case "baseproduct":
                    name = "基础商品";
                    break;
                case "tiktok":
                    name = "TikTok";
                    break;
                case "taobaomaicai":
                    name = "淘宝买菜";
                    break;
                case "other_heliang":
                    name = "其他平台（禾量）";
                    break;
                case "ownshop":
                    name = "自有商城";
                    break;
                case "other_juhaomai":
                    name = "其他平台（聚好麦）";
                    break;
                case "bilibili":
                    name = "B站会员购";
                    break;
                case "taobaomaicaiv2":
                    name = "淘宝买菜(新)";
                    break;
                case "toutiaosaleshop":
                    name = "抖店即时零售";
                    break;
                case "other_haoyouduo":
                    name = "其他平台（好又多）";
                    break;
            }
            return name;
        }


        /// <summary>
        /// 云平台 与 店铺平台 的映射
        /// </summary>
        public static Dictionary<string, List<string>> CloudPlatWithShopPlatMapper => new Dictionary<string, List<string>>()
        {
            {"Alibaba",new List<string>()
                {
                    "1688","alibaba","c2m","alibabac2m","taobao","tb","taobaosupplier","xiaodian","xd","mogujie","mg","meilishuo",
                    "mls","youzan","yz","weidian","wd","weimeng","wm","kaola","kl","vipshop","vshop","suning","sn","offline",
                    "weishang","ws","ol","mengtui","mt","yunji","yj","open","openv1","openv2","kuaishou","ks","kuaishousupplier",
                    "xiaohongshu","xhs","wxxiaoshangdian","wxxsd","wxvideo","mokuai","mk","duxiaodian","dxd","tuanhaohuo","thh",
                    "alibabazhuke","taobaomaicai","tiktok","other_juhaomai","other_heliang","ownshop","taobaomaicaiv2"
                }
            },
            {"Pinduoduo",new List<string>()
                {
                    "pinduoduo","pdd","kuaituantuan","pddktt"
                }
            },
            {"TouTiao",new List<string>()
                {
                    "toutiao","tt","zhidian","zd","douyinxiaodian","dyxd","toutiaoxiaodian","ttxd","luban","lb","toutiaofenfa","toutiaogongyingshang","toutiaosaleshop",
                }
            },
            {"Jingdong",new List<string>()
                {
                    "jingdong","jd","jingdongpurchase",
                }
            },
        };

        /// <summary>
        /// 平台类型所在云平台
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static string GetCloudPlatformType(string platformType)
        {
            if (string.IsNullOrEmpty(platformType))
                return "Alibaba";
            var cloudPt = "Alibaba";
            switch (platformType.ToLower())
            {
                case "toutiao":
                    cloudPt = "TouTiao";
                    break;
                case "pinduoduo":
                case "kuaituantuan":
                    cloudPt = "Pinduoduo";
                    break;
                case "jingdong":
                case "jingdongpurchase":
                    cloudPt = "Jingdong";
                    break;
            }
            return cloudPt;
        }

        /// <summary>
        /// 分单系统-各平台默认铺货应用的AppKey
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static string GetFxListingAppKey(string platformType)
        {
            if (string.IsNullOrEmpty(platformType))
                return "";
            var appKey = string.Empty;

            //TODO:后续支持更多平台时，逐一补充对应的appKey
            switch (platformType.ToLower())
            {
                case "toutiao":
                    appKey = TouTiaoFxListingAppKey;
                    break;
            }
            return appKey;
        }

        /// <summary>
        /// 分单系统-各平台默认常规应用的AppKey
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static string GetFxNormalAppKey(string platformType)
        {
            if (string.IsNullOrEmpty(platformType))
                return "";
            var appKey = string.Empty;

            switch (platformType.ToLower())
            {
                case "toutiao":
                    appKey = TouTiaoFxNewAppKey;
                    break;
                case "kuaishou":
                    appKey = KuaiShouFxAppKey;
                    break;
                case "pinduoduo":
                    appKey = PddFxAppKey;
                    break;
                case "alibaba":
                    appKey = AlibabaFxAppKey;
                    break;
                case "youzan":
                    appKey = YouZanFxAppKey;
                    break;
                case "weimeng":
                    appKey = WeiMengFxAppKey;
                    break;
                case "kaola":
                    appKey = KaoLaAppKey;
                    break;
                case "suning":
                    appKey = SuningFxAppKey;
                    break;
                case "weidian":
                    appKey = WeiDianFxAppKey;
                    break;
                case "jingdongpurchase":
                case "jingdong":
                    appKey = JingDongFxAppKey;
                    break;
                case "mengtui":
                    appKey = MengTuiAppKey;
                    break;
                case "xiaohongshu":
                    appKey = XiaoHongShuFXAppKey;
                    break;
                case "mokuai":
                    appKey = MoKuaiFxAppKey;
                    break;
                case "duxiaodian":
                    appKey = DuXiaoDianV2FxAppKey;
                    break;
                case "tuanhaohuo":
                    appKey = TuanHaoHuoAppKey;
                    break;
                case "kuaituantuan":
                    appKey = KuaiTuanTuanAppKey;
                    break;
            }
            return appKey;
        }

        /// <summary>
        /// 平台类型 转 拼多多电子面单平台类型
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static string ConvertPlatformToPddPrintName(string platformType)
        {
            if (string.IsNullOrEmpty(platformType))
                return "";
            var name = string.Empty;
            switch (platformType.ToLower())
            {
                case "1688":
                case "alibaba":
                    name = $"ALBB";
                    break;
                case "pinduoduo":
                case "pdd":
                    name = $"PDD";
                    break;
                case "taobao":
                case "taobaosupplier":
                case "tb":
                    name = $"TB";
                    break;
                case "xiaodian":
                case "xd":
                    name = $"XD";
                    break;
                case "mogujie":
                case "mg":
                    name = $"MGJ";
                    break;
                case "meilishuo":
                case "mls":
                    name = $"OTHER";
                    break;
                case "youzan":
                case "yz":
                    name = $"YZ";
                    break;
                case "weidian":
                case "wd":
                    name = $"WD";
                    break;
                case "vipshop":
                case "vshop":
                    name = $"WPH";
                    break;
                case "suning":
                case "sn":
                    name = $"SN";
                    break;
                case "jingdong":
                case "jingdongpurchase":
                case "jd":
                    name = $"JD";
                    break;
                case "mengtui":
                case "mt":
                    name = $"MT";
                    break;
                case "yunji":
                case "yj":
                    name = $"YJ";
                    break;
                case "toutiao":
                case "tt":
                case "toutiaofenfa":
                    name = $"DY";
                    break;
                case "wxxiaoshangdian":
                case "wxxsd":
                case "wxvideo":
                    name = $"WXXSD";
                    break;
                case "weimeng":
                case "wm":
                case "kaola":
                case "kl":
                case "zhidian":
                case "zd":
                case "douyinxiaodian":
                case "dyxd":
                case "toutiaoxiaodian":
                case "ttxd":
                case "luban":
                case "lb":
                case "offline":
                case "weishang":
                case "ws":
                case "ol":
                case "open":
                case "openv1":
                case "openv2":
                case "other_heliang":
                case "ownshop":
                    name = $"OTHER";
                    break;
                case "kuaishou":
                case "ks":
                case "kuaishousupplier":
                    name = $"KS";
                    break;
                case "duxiaodian":
                case "dxd":
                    name = $"DXD";
                    break;
                case "kuaituantuan":
                case "pddktt":
                    name = $"KTT";
                    break;
                case "tuanhaohuo":
                case "thh":
                    name = $"THH";
                    break;
            }
            return name;
        }


        /// <summary>
        /// 订单状态 转 汉字
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        public static string ConvertPlatformStatusToName(string status)
        {
            var name = string.Empty;
            switch (status)
            {
                case "waitbuyerpay":
                    name = "待买家付款";
                    break;
                case "waitsellersend":
                    name = "待发货";
                    break;
                case "waitbuyerreceive":
                    name = "待买家确认收货";
                    break;
                case "success":
                    name = "交易成功";
                    break;
                case "cancel":
                    name = "交易关闭";
                    break;
                case "confirm_goods_but_not_fund":
                    name = "货到付款";
                    break;
                case "locked":
                    name = "退款中";
                    break;
                default:
                    break;
            }
            return name;
        }

        /// <summary>
        /// 获取所有支持的平台的授权入口
        /// </summary>
        /// <returns></returns>
        public static List<SuportPlatformAuthEntryModel> GetAllPlatformAuthLinks()
        {
            var suportPlatforms = new List<SuportPlatformAuthEntryModel>();
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "Alibaba",
                Name = "阿里巴巴",
                AuthEntry = $"{CustomerConfig.AlibabaNewSystemLink}",
                Index = 1
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "AlibabaC2M",
                Name = "淘工厂",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/alibabac2m",
                Index = 12
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "Pinduoduo",
                Name = "拼多多",
                AuthEntry = $"{CustomerConfig.PinduoduoNewSystemLink}",
                Index = 3
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "Taobao",
                Name = "淘宝",
                AuthEntry = $"{CustomerConfig.TaobaoNewSystemLink}",
                Index = 2
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "XiaoDian",
                Name = "小店",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/xiaodian",
                Index = 19
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "MoGuJie",
                Name = "蘑菇街",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/mogujie",
                Index = 10
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "MeiLiShuo",
                Name = "美丽说",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/meilishuo",
                Index = 23
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "YouZan",
                Name = "有赞",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/youzan",
                Index = 8
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "WeiDian",
                Name = "微店",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/weidian",
                Index = 11
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "WeiMeng",
                Name = "微盟",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/weimeng",
                Index = 16
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "KaoLa",
                Name = "考拉",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/kaola",
                Index = 22
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "VipShop",
                Name = "唯品会",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/vipshop",
                Index = 21
            });

            #region 值点商城、抖音小店、头条小店、鲁班 共用一个后台
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "ZhiDian",
                Name = "值点",
                //AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/zhidian",
                AuthEntry = "https://fxg.jinritemai.com/index.html#/ffa/open/serviceAuthorizeManage?page=1&size=10&tab=",
                Index = 4
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "DouYinXiaoDian",
                Name = "抖音小店",
                //AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/zhidian",
                AuthEntry = "https://fxg.jinritemai.com/index.html#/ffa/open/serviceAuthorizeManage?page=1&size=10&tab=",
                Index = 4
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "TouTiaoXiaoDian",
                Name = "头条小店",
                //AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/zhidian",
                AuthEntry = "https://fxg.jinritemai.com/index.html#/ffa/open/serviceAuthorizeManage?page=1&size=10&tab=",
                Index = 4
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "LuBan",
                Name = "鲁班",
                //AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/zhidian",
                AuthEntry = "https://fxg.jinritemai.com/index.html#/ffa/open/serviceAuthorizeManage?page=1&size=10&tab=",
                Index = 4
            });
            #endregion

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "Suning",
                Name = "苏宁易购",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/suning",
                Index = 17
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "Jingdong",
                Name = "京东",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/jingdong",
                Index = 7
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "MengTui",
                Name = "萌推",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/mengtui",
                Index = 14
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "YunJi",
                Name = "云集",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/yunji",
                Index = 23
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "Open",
                Name = "开放平台",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/open",
                Index = 24
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "OpenV1",
                Name = "开放平台V1",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/open",
                Index = 25
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "OpenV2",
                Name = "开放平台V2",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/open",
                Index = 26
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "KuaiShou",
                Name = "快手小店",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/kuaishou",
                Index = 5
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "http://img.dgjapp.com/images/open/dianguanjia.png",
                PlatformType = "Offline",
                Name = "自助版",
                AuthEntry = CustomerConfig.PortalLink,
                Index = 6
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "http://img.dgjapp.com/images/open/xiaohong.png",
                PlatformType = "XiaoHongShu",
                Name = "小红书",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/xiaohongshuv2fx",
                Index = 20
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "http://img.dgjapp.com/images/pt/wxxsd.png",
                PlatformType = "WxXiaoShangDian",
                Name = "微信小商店",
                AuthEntry = "https://shop.weixin.qq.com/",
                Index = 9
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "http://img.dgjapp.com/images/pt/mokuai.png",
                PlatformType = "MoKuai",
                Name = "魔筷星选",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/mokuai",
                Index = 15
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "http://img.dgjapp.com/images/pt/duxiaodian.png",
                PlatformType = "DuXiaoDian",
                Name = "度小店",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/duxiaodian",
                Index = 18
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                //Img = "http://img.dgjapp.com/images/pt/tuanhaohuo.png",
                PlatformType = "TuanHaoHuo",
                Name = "团好货",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/tuanhaohuo",
                Index = 19
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "http://img.dgjapp.com/images/pt/pddktt.png",
                PlatformType = "KuaiTuanTuan",
                Name = "快团团",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/pddktt",
                Index = 20
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "http://img.dgjapp.com/images/pt/wxxsd.png",
                PlatformType = "WxVideo",
                Name = "视频号(微信小店)",//Name = "微信视频号",
                AuthEntry = "https://channels.weixin.qq.com/shop",
                Index = 21
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "http://img.dgjapp.com/images/pt/tbmc.png",
                PlatformType = "TaobaoMaiCai",
                Name = "淘宝买菜",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/taobaomaicai",
                Index = 22
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "JingdongPurchase",
                Name = "京东供销平台",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/jingdong",
                Index = 7
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "OwnShop",
                Name = "自有商城",
                AuthEntry = "",
                Index = 22
            });

            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "Other",
                Name = "其他平台",
                AuthEntry = "",
                Index = 23
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "Other_Heliang",
                Name = "其他平台（禾量）",
                AuthEntry = "",
                Index = 23
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "Other_JuHaoMai",
                Name = "其他平台（聚好麦）",
                AuthEntry = "",
                Index = 23
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "BiliBili",
                Name = "B站会员购",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/bilibili",
                Index = 23
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "http://img.dgjapp.com/images/pt/tbmc.png",
                PlatformType = "TaobaoMaiCaiV2",
                Name = "淘宝买菜(新)",
                AuthEntry = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/alibabac2m",
                Index = 23
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "TouTiaoSaleShop",
                Name = "抖店即时零售",
                AuthEntry = $"",
                Index = 24
            });
            suportPlatforms.Add(new SuportPlatformAuthEntryModel
            {
                Img = "",
                PlatformType = "Other_HaoYouDuo",
                Name = "其他平台（好又多）",
                AuthEntry = "",
                Index = 23
            });
            //新加平台，往后添加...
            return suportPlatforms;
        }

        public static string GetPlatformAuthLink(string platformType, int openPlatformAppId = 0)
        {
            var links = GetAllPlatformAuthLinks();
            var link = links.FirstOrDefault(l => l.PlatformType == platformType)?.AuthEntry;
            if (platformType == "Open" || platformType == "OpenV1" || platformType == "OpenV2" && openPlatformAppId > 0)
                link += $"?id={openPlatformAppId}";
            return link;
        }

        /// <summary>
        /// 获取续费链接
        /// </summary>
        /// <param name="platformType">平台类型</param>
        /// <returns></returns>
        public static string GetPayLink(string platformType)
        {
            if (string.IsNullOrEmpty(platformType))
                return "";
            var url = "";
            switch (platformType.ToLower())
            {
                case "1688":
                case "alibaba":
                    url = $"http://pc.1688.com/product/detail.htm?productCode=Tz%2BIZt9qCGKsMpNFCCCY9%2BmqRnw6h1ZBD3N%2Fli2CCyg%3D&productType=GROUP&tracelog=app_map_dgj";
                    break;
                case "alibabazhuke":
                    url = $"https://m-fuwu.1688.com/page/servicedetail.htm?appKey=8076003";
                    break;
                case "taobao":
                case "tb":
                    url = $"https://fuwu.taobao.com/ser/detail.htm?spm=a1z13.8114210.1234-fwlb.4.b7XqWn&service_code=FW_GOODS-1000059019&tracelog=search&from_key=%E5%BA%97%E7%AE%A1%E5%AE%B6";
                    break;
                case "youzan":
                case "yz":
                    url = $"https://yingyong.youzan.com/cloud-app-detail/43116";
                    break;
                case "weimeng":
                case "wm":
                    url = $"https://fuwu.weimob.com/serviceDetail/21201";
                    break;
                case "jingdong":
                case "jd":
                case "jingdongpurchase":
                    url = $"https://fw.jd.com/main/detail/FW_GOODS-1825002";
                    break;
                case "suning":
                case "sn":
                    url = $"http://fuwu.suning.com/detail/10003725.html#key1=version1#key2=cycle1";
                    break;
                case "pinduoduo":
                case "pdd":
                    url = "https://mms.pinduoduo.com/service-market/service-detail?detailId=172";
                    break;
                case "offline":
                case "of":
                case "ws":
                case "weishang":
                    //微商版暂不收费
                    //url = $"{CustomerConfig.AppStoreUrl}/orderApplications.html?ProductCode=Print_{platformType}";
                    url = "";
                    break;
                case "kuaishou":
                case "ks":
                    url = $"https://fuwu.kwaixiaodian.com/detail?id=1640236816583";
                    break;
                case "toutiao":
                case "tt":
                case "zhidian":
                case "zd":
                case "douyinxiaodian":
                case "dyxd":
                case "toutiaoxiaodian":
                case "ttxd":
                case "luban":
                case "lb":
                    url = $"https://fuwu.jinritemai.com/detail?service_id=7&from=isv.detail";
                    break;
                case "toutiaosaleshop":
                    url = $"https://fuwu.jinritemai.com/detail?service_id=35574&from=isv.detail";
                    break;
                case "alibabac2m":
                    url = "";
                    break;
                case "wxxiaoshangdian":
                    url = "https://shop.weixin.qq.com/";
                    break;
                case "wxvideo":
                    url = "https://store.weixin.qq.com/shop";//0829 L:微信小店适配 续费链接视频号链接跳转至微信小店
                    //url = "https://channels.weixin.qq.com/shop";//视频号
                    break;
                case "dxd":
                case "duxiaodian":
                    url = "https://fuwu.baidu.com/magnum/user/1/openShop/detail/20066356667/42939043?providerId=42939043";
                    break;
                default:
                    break;
            }
            return url;
        }

        /// <summary>
        /// 获取当前店铺的来源ID(用于众邮快递)
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static string GetSource(string platformType)
        {
            var _source = "1111";
            if (string.IsNullOrEmpty(platformType))
                platformType = "";
            switch (platformType.ToLower())
            {
                case "1688":
                case "alibaba":
                    _source = "1044";
                    break;
                case "pinduoduo":
                case "pdd":
                    _source = "1008";
                    break;
                case "taobao":
                case "tb":
                    _source = "1049";
                    break;
                case "mogujie":
                case "mg":
                    _source = "1039";
                    break;
                case "weidian":
                case "wd":
                    _source = "1002";
                    break;
                case "vipshop":
                case "vshop":
                    _source = "1034";
                    break;
                case "suning":
                case "sn":
                    _source = "1032";
                    break;
                case "toutiao":
                case "tt":
                case "zhidian":
                case "zd":
                case "douyinxiaodian":
                case "dyxd":
                case "toutiaoxiaodian":
                case "ttxd":
                case "luban":
                case "lb":
                    _source = "1010";
                    break;
                case "jingdong":
                case "jd":
                    _source = "1020";
                    break;
                case "mengtui":
                case "mt":
                    _source = "1043";
                    break;
                case "yunji":
                case "yj":
                    _source = "1042";
                    break;
                case "kuaishou":
                case "ks":
                    _source = "1011";
                    break;
                case "xiaohongshu":
                case "xhs":
                    _source = "1040";
                    break;
                case "youzan":
                case "yz":
                    _source = "1028";
                    break;
                case "kaola":
                case "kl":
                    _source = "1036";
                    break;
            }
            return _source;
        }

        #region 连接字符串

        public static string FenDanDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["FenDanDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 新版配置数据库：包括了快递模板（菜鸟授权、拼多多授权、网点信息等）、发货单模板、店铺信息、店铺关联、地址库、快递公司信息、发货码信息
        /// </summary>
        public static string ConfigureDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["ConfigureDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 原云配置库连接字符串
        /// </summary>
        public static string SourceCloudConfigureDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["SourceCloudConfigureDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 数据变更服务配置库
        /// </summary>
        public static string DataChangeConfigDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["DataChangeConfigDb"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 异常订单默认库
        /// </summary>
        public static string OrderAbnormalDefaultDbConnectionString
        {
            get
            {
                return ConfigurationManager.ConnectionStrings["OrderAbnormalDefaultDB"]?.ConnectionString ?? "";
            }
        }


        /// <summary>
        /// 基于消息数据同步状态默认库
        /// </summary>
        public static string MessageDataSyncStatusDefaultDbConnectionString
        {
            get
            {
                return ConfigurationManager.ConnectionStrings["MessageDataSyncStatusDefaultDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 【抖店云】数据变更服务配置库
        /// </summary>
        public static string TouTiaoDataChangeConfigDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["TouTiaoDataChangeConfigDb"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 数据同步状态数据库
        /// </summary>
        public static string DataSyncStatusDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["DataSyncStatusDB"]?.ConnectionString ?? "";
            }
        }
        /// <summary>
        /// 采集商品库
        /// </summary>
        public static string CollectProductDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["CollectProductDB"]?.ConnectionString ?? "";
            }
        }



        /// <summary>
        /// 数据同步状态数据库(备库)
        /// </summary>
        public static string DataSyncStatusBackupDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["DataSyncStatusBackupDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 【抖店云】数据同步状态数据库
        /// </summary>
        public static string TouTiaoDataSyncStatusDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["TouTiaoDataSyncStatusDB"]?.ConnectionString ?? "";
            }
        }

        public static string PddConfigureDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["PddConfigureDB"]?.ConnectionString ?? "";
            }
        }


        public static string TouTiaoConfigureDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["TouTiaoConfigureDB"]?.ConnectionString ?? "";
            }
        }

        public static string AlibabaConfigureDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["AlibabaConfigureDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 发件人信息配置库        
        /// </summary>
        public static string SellerInfoConfigureDBConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["SellerInfoConfigureDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 阿里客服响应配置库
        /// </summary>
        public static string AliInqueryConfigureDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["AliInqueryConfigureDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 默认的数据库连接
        /// </summary>
        public static string DefalutConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["PrintDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 新版1688的数据库连接
        /// </summary>
        public static string NewAlibabaConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["AlibabaDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 新版1688的数据库连接
        /// </summary>
        public static string NewPddPrintDBConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["PddPrintDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 新版拼多多的数据库连接
        /// </summary>
        public static string NewTaobaoPrintDBConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["TaobaoPrintDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 新版京东的数据库连接
        /// </summary>
        public static string NewJdPrintDBConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["JingDongPrintDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 新版1688的Mongo数据库连接
        /// </summary>
        public static string DefaultMongoDBConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["MongoPrintDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// MongoDB日志库
        /// </summary>
        public static string MongoLogDBConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["MongoLogDB"]?.ConnectionString ?? "";
            }
        }
        /// <summary>
        /// MongoDB物流库
        /// </summary>
        public static string MongoLogisticsDBConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["MongoLogisticsDB"]?.ConnectionString ?? "";
            }
        }
        /// <summary>
        /// 新版1688的Mongo数据库连接
        /// </summary>
        public static string NewAlibabaMongoDBConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["MongoPrintDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 新版1688的Mongo数据库连接
        /// </summary>
        public static string NewPddPrintMongoDBConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["MongoPddPrintDB"]?.ConnectionString ?? "";
            }
        }
        /// <summary>
        /// 老版拼多多的Mongo数据库连接
        /// </summary>
        public static string NewTaobaoPrintMongoDBConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["MongoTaobaoPrintDB"]?.ConnectionString ?? "";
            }
        }


        /// <summary>
        /// 新版拼多多的数据库连接
        /// </summary>
        public static string NewOhterPrintDBConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["OtherPrintDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 微商版的数据库连接
        /// </summary>
        public static string NewWeiShangPrintDBConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["WeiShangPrintDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 老版1688的数据库连接：业务数据
        /// </summary>
        public static string OldAlibabaZhPrintConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["1688ZhPrint"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 老版1688的数据库连接：订单数据
        /// </summary>
        public static string OldAlibabaSynDataConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["1688SynData"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 老版拼多多的数据库连接：业务数据
        /// </summary>
        public static string OldPinduoduoZhPrintConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["PinduoduoZhPrint"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 老版拼多多的数据库连接：订单数据
        /// </summary>
        public static string OldPinduoduoSynDataConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["PinduoduoSynData"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 老版Taobao的数据库连接：业务数据
        /// </summary>
        public static string OldTaobaoZhPrintConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["TaobaoZhPrint"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 老版Taobao的数据库连接：订单数据
        /// </summary>
        public static string OldTaobaoSynDataConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["TaobaoSynData"]?.ConnectionString ?? "";
            }
        }

        public static string GetTaobaoPushDbnConnectionString(string configName = "")
        {
            var val = System.Configuration.ConfigurationManager.ConnectionStrings["TaobaoSynData"]?.ConnectionString ?? "";
            if (string.IsNullOrEmpty(configName) == false)
            {
                var temp = System.Configuration.ConfigurationManager.ConnectionStrings[configName]?.ConnectionString ?? "";
                if (string.IsNullOrEmpty(temp) == false)
                    val = temp;
            }
            return val;
        }

        /// <summary>
        /// 老版商机助理的数据库连接：业务数据
        /// </summary>
        public static string OldSJZhPrintConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["SjZhPrint"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 老版商机助理的数据库连接：订单数据
        /// </summary>
        public static string OldSjSynDataConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["SjSynData"]?.ConnectionString ?? "";
            }
        }


        /// <summary>
        /// 入端的数据库连接：业务数据
        /// </summary>
        public static string OldWkZhPrintConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["WkZhPrint"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 入端的数据库连接：订单数据
        /// </summary>
        public static string OldWkSynDataConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["WkSynData"]?.ConnectionString ?? "";
            }
        }


        /// <summary>
        /// 老版配置数据
        /// </summary>
        public static string OldConfigureConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["ZhConfigure"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 拼多多推送库链接字符串
        /// </summary>
        public static string PinduoduoPushDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["PinduoduoPushDb"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 拼多多分单应用推送库rds实例ID
        /// </summary>
        public static string PinduoduoFxPushDbRdsId
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["PinduoduoFxPushDbRdsId"]?.ConnectionString ?? "9BCD84362EF17548";
            }
        }
        /// <summary>
        /// 是否启用拼多多推送库
        /// </summary>
        public static bool IsEnabledPddPushDb
        {
            get
            {
                var temp = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledPddPushDb") ?? "false";
                var trueValues = new List<string> { "true", "1", "yes" };
                if (trueValues.Contains(temp.ToLower()))
                    return true;
                return false;
            }
        }
        /// <summary>
        /// 是否启用拼多多退款订单消息
        /// </summary>
        public static bool IsEnabledPddRefundMessage
        {
            get
            {
                var temp = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledPddRefundMessage") ?? "false";
                var trueValues = new List<string> { "true", "1", "yes" };
                if (trueValues.Contains(temp.ToLower()))
                    return true;
                return false;
            }
        }
        /// <summary>
        /// 是否启用拼多多电子面单消息
        /// </summary>
        public static bool IsEnabledPddWaybillAppMessage
        {
            get
            {
                var temp = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledPddWaybillAppMessage") ?? "false";
                var trueValues = new List<string> { "true", "1", "yes" };
                if (trueValues.Contains(temp.ToLower()))
                    return true;
                return false;
            }
        }
        /// <summary>
        /// 获取指定平台的连接字符串
        /// </summary>
        /// <param name="platformType"></param>
        /// <param name="isMongodb">是否是mongodb</param>
        /// <returns></returns>
        public static string GetConnectionString(string platformType)
        {
            return GetConnectStringss(platformType);
        }

        /// <summary>
        /// 获取指定云平台的配置库连接字符串
        /// </summary>
        /// <param name="cpt"></param>
        /// <returns></returns>
        public static string GetConfigureDbConnectionString(string cpt)
        {
            cpt = cpt.ToLower();
            if (cpt == "pinduoduo")
                return PddConfigureDbConnectionString;
            else if (cpt == "toutiao")
                return TouTiaoConfigureDbConnectionString;
            else if (cpt == "alibaba")
                return AlibabaConfigureDbConnectionString;
            return "";
        }

        /// <summary>
        /// 获取Mongodb的连接字符串
        /// </summary>
        /// <param name="platformType"></param>
        /// <param name="isMongodb">是否是mongodb</param>
        /// <returns></returns>
        public static string GetMongoConnectionString(string dbname)
        {
            if (dbname == "Alibaba")
                return NewAlibabaMongoDBConnectionString;
            if (dbname == "Pinduoduo")
                return NewPddPrintMongoDBConnectionString;
            if (dbname == "Taobao")
                return NewTaobaoPrintMongoDBConnectionString;
            if (dbname.ToLower() == "log")
                return MongoLogDBConnectionString;
            if (dbname.ToLower() == "system.logistics")
                return MongoLogisticsDBConnectionString;
            return DefaultMongoDBConnectionString;
        }
        /// <summary>
        /// 将MongoDB的连接字符串转换为SQLServer的
        /// </summary>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        public static string ParseMongoDbConnectionstringToSqlServerS(string connectionString)
        {
            var str = connectionString.ToLower();
            if (str.Contains("mongodb"))
            {
                if (str.Contains("pdd"))
                    return NewPddPrintDBConnectionString;
                else if (str.Contains("tb") || str.Contains("taobao"))
                    return NewTaobaoPrintDBConnectionString;
                else if (str.Contains("zh_new_print"))
                    return DefalutConnectionString;

            }
            return connectionString;
        }


        /// <summary>
        /// 【货盘商品】数据库
        /// </summary>
        public static string SupplierProductDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["SupplierProductDB"]?.ConnectionString ?? "";
            }
        }
        
        #endregion

        /// <summary>
        /// 财务结算数据库-财务导入的账单明细
        /// </summary>
        public static string FinanceDataDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["FinanceDataDB"]?.ConnectionString ?? "";
            }
        }
        #region 配置信息        

        /// <summary>
        ///  子账号权限版本
        /// </summary>
        public static string FxPermissionVersion
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("FxPermissionVersion") ?? "V250612";
            }
        }

        public static int MaxUpdateLogisticSyncCount
        {
            get
            {
                var count = System.Configuration.ConfigurationManager.AppSettings.Get("Preordain:MaxUpdateLogistic")?.ToInt() ?? 50;
                return count;
            }
        }

        public static int LogisticUpdateIntervalTime
        {
            get
            {
                var count = System.Configuration.ConfigurationManager.AppSettings.Get("LogisticUpdate:IntervalTime")?.ToInt() ?? 300;
                return count;
            }
        }

        public static string LogisticUpdateStartTime
        {
            get
            {
                var count = System.Configuration.ConfigurationManager.AppSettings.Get("LogisticUpdate:StartTime") ?? "";
                return count;
            }
        }
        /// <summary>
        /// 中转代理Url（淘宝系有限制IP访问接口）
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static string GetProxyUrl(string url)
        {
            string useApiProxy = System.Configuration.ConfigurationManager.AppSettings.Get("Taobao:ApiUseProxy") ?? "0";
            string proxyUrl = System.Configuration.ConfigurationManager.AppSettings.Get("ApiProxy:Taobao") ?? "http://queue.dgjapp.com/api/tbapi";//"http://tapiproxy.dgjapp.com/taobao";

            var result = url;
            if (useApiProxy == "true" || useApiProxy == "1" || CustomerConfig.CloudPlatformType != "Alibaba" || CustomerConfig.IsLocalDbDebug)
            {
                result = proxyUrl + "?apiUrl=" + url;
            }
            return result;
        }

        public static string ProxyServer
        {
            get
            {
                var px = System.Configuration.ConfigurationManager.AppSettings.Get("ProxyServer") ?? "";
                return px;
            }
        }

        /// <summary>
        ///  淘宝接口是否使用代理
        /// </summary>
        public static bool TaobaoIsApiUseProxy
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("Taobao:IsApiUseProxy") ?? "";
                return value == "1" || value.ToLower() == "true";
            }
        }
        /// <summary>
        ///  淘宝接口代理地址
        /// </summary>
        public static string TaobaoApiProxyUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Taobao:TaobaoApiProxyUrl") ?? "";
            }
        }

        private static bool? _isDebug = null;
        /// <summary>
        /// 是否是调试模式(取一次即可)
        /// </summary>
        public static bool IsDebug
        {
            get
            {
                if (_isDebug == null)
                    _isDebug = System.Configuration.ConfigurationManager.AppSettings.Get("IsDebug") == "1";
                return _isDebug ?? false;
            }
        }
        private static bool? _isLocalDbDebug = null;
        /// <summary>
        /// 是否是使用于本地连接数据库调试(取一次即可)
        /// </summary>
        public static bool IsLocalDbDebug
        {
            get
            {
                if (_isLocalDbDebug == null)
                    _isLocalDbDebug = System.Configuration.ConfigurationManager.AppSettings.Get("IsLocalDbDebug") == "1";
                return _isLocalDbDebug ?? false;
            }
        }

        private static bool? _isTestWebSite = null;
        /// <summary>
        /// 是否是测试站点
        /// </summary>
        public static bool IsTestWebSite
        {
            get
            {
                if (_isTestWebSite == null)
                    _isTestWebSite = System.Configuration.ConfigurationManager.AppSettings.Get("IsTestWebSite") == "1";
                return _isTestWebSite ?? false;
            }
        }

        /// <summary>
        /// 采购金检查：直接返回未开通，用于测试
        /// </summary>
        public static bool IsReturnNotOpenWangShangPay
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsReturnNotOpenWangShangPay") == "1";
            }
        }

        /// <summary>
        /// 同步分片天数
        /// </summary>
        public static double SyncSplitDay
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("SyncSplitDay").ToDouble();
            }
        }

        /// <summary>
        /// 拆单最大循环次数
        /// </summary>
        public static int MaxSplitLoopCount
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MaxSplitLoopCount").ToInt();
            }
        }

        /// <summary>
        /// 是否禁用配置库数据同步
        /// </summary>
        public static bool IsDisabledConfigDbSync
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsDisabledConfigDbSync") == "1";
            }
        }

        /// <summary>
        /// 分单系统是否开启异步同步
        /// </summary>
        public static bool IsAsyncSyncOrder
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsAsyncSyncOrder") == "1";
            }
        }

        /// <summary>
        /// 是否是抖音电子面单测试
        /// </summary>
        public static bool IsTouTiaoWaybillTest
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsTouTiaoWaybillTest") == "1";
            }
        }

        /// <summary>
        /// 是否是调试模式
        /// </summary>
        public static bool IsEnabledAppStore
        {
            get
            {
                var t = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledAppStore");
                if (string.IsNullOrEmpty(t))
                    return true;
                else
                    return t == "1" || t == "true";
            }
        }

        /// <summary>
        /// 是否启用拼多多订单消息
        /// </summary>
        public static bool IsEnabledPddOrderMessage
        {
            get
            {
                var t = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledPddOrderMessage");
                if (string.IsNullOrEmpty(t))
                    return false;
                else
                    return t == "1" || t == "true";
            }
        }

        /// <summary>
        /// 是否启用拼多多代打订单消息
        /// </summary>
        public static bool IsEnabledPddFdsOrderMessage
        {
            get
            {
                var t = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledPddFdsOrderMessage");
                if (string.IsNullOrEmpty(t))
                    return false;
                else
                    return t == "1" || t == "true";
            }
        }

        /// <summary>
        /// 是否启用拼多多分单系统订单消息
        /// </summary>
        public static bool IsEnabledPddFxOrderMessage
        {
            get
            {
                var t = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledPddFxOrderMessage");
                if (string.IsNullOrEmpty(t))
                    return false;
                else
                    return t == "1" || t == "true";
            }
        }
        /// <summary>
        /// 是否启用拼多多分单系统退款订单消息
        /// </summary>
        public static bool IsEnabledPddFxRefundMessage
        {
            get
            {
                var temp = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledPddFxRefundMessage") ?? "false";
                var trueValues = new List<string> { "true", "1", "yes" };
                if (trueValues.Contains(temp.ToLower()))
                    return true;
                return false;
            }
        }
        /// <summary>
        /// 是否启用拼多多分单系统服务承诺消息
        /// </summary>
        public static bool IsEnabledPddFxChatMessage
        {
            get
            {
                var temp = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledPddFxChatMessage") ?? "false";
                var trueValues = new List<string> { "true", "1", "yes" };
                if (trueValues.Contains(temp.ToLower()))
                    return true;
                return false;
            }
        }

        /// <summary>
        /// 是否启用拼多多物流消息
        /// </summary>
        public static bool IsEnabledPddLogisticMessage
        {
            get
            {
                var t = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledPddLogisticMessage");
                if (string.IsNullOrEmpty(t))
                    return false;
                else
                    return t == "1" || t == "true";
            }
        }

        /// <summary>
        /// 是否启用ZTO物流消息
        /// </summary>
        public static bool IsEnabledZTOLogisticMessage
        {
            get
            {
                var t = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledZTOLogisticMessage");
                if (string.IsNullOrEmpty(t))
                    return false;
                else
                    return t == "1" || t == "true";
            }
        }


        /// <summary>
        /// 是否启用STO物流消息
        /// </summary>
        public static bool IsEnabledSTOLogisticMessage
        {
            get
            {
                var t = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledSTOLogisticMessage");
                if (string.IsNullOrEmpty(t))
                    return false;
                else
                    return t == "1" || t == "true";
            }
        }


        /// <summary>
        /// 是否启用YTO物流消息
        /// </summary>
        public static bool IsEnabledYTOLogisticMessage
        {
            get
            {
                var t = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledYTOLogisticMessage");
                if (string.IsNullOrEmpty(t))
                    return false;
                else
                    return t == "1" || t == "true";
            }
        }


        /// <summary>
        /// 是否启用HTKY物流消息
        /// </summary>
        public static bool IsEnabledHTKYLogisticMessage
        {
            get
            {
                var t = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledHTKYLogisticMessage");
                if (string.IsNullOrEmpty(t))
                    return false;
                else
                    return t == "1" || t == "true";
            }
        }


        /// <summary>
        /// 是否启用DBKD物流消息
        /// </summary>
        public static bool IsEnabledDBKDLogisticMessage
        {
            get
            {
                var t = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledDBKDLogisticMessage");
                if (string.IsNullOrEmpty(t))
                    return false;
                else
                    return t == "1" || t == "true";
            }
        }


        /// <summary>
        /// 是否启用ZYKD物流消息
        /// </summary>
        public static bool IsEnabledZYKDLogisticMessage
        {
            get
            {
                var t = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledZYKDLogisticMessage");
                if (string.IsNullOrEmpty(t))
                    return false;
                else
                    return t == "1" || t == "true";
            }
        }

        /// <summary>
        /// 是否仅处理拼多多退款订单消息
        /// </summary>
        public static bool IsOnlyProcessPddRefundMessage
        {
            get
            {
                var t = System.Configuration.ConfigurationManager.AppSettings.Get("IsOnlyProcessPddRefundMessage");
                if (string.IsNullOrEmpty(t))
                    return false;
                else
                    return t == "1" || t == "true";
            }
        }

        /// <summary>
        /// 是否处理拼多多对应漏单订单合单标识（不配置默认开启处理，配置了任意东西则不处理）
        /// </summary>
        public static bool IsHandlePddLouDanOrder
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsHandlePddLouDanOrder").IsNullOrEmpty();
            }
        }

        /// <summary>
        /// 店铺过期，设置多少天就提示
        /// </summary>
        public static int EndServiceDay
        {
            get
            {
                var endServiceDay = System.Configuration.ConfigurationManager.AppSettings.Get("EndServiceDay");
                return string.IsNullOrEmpty(Convert.ToString(endServiceDay)) ? 5 : Convert.ToInt32(endServiceDay);
            }
        }

        /// <summary>
        /// 拼多多全量同步的时间，开始时间为多少天
        /// </summary>
        public static int StartFullSyncTimeByPdd
        {
            get
            {
                var seconds = System.Configuration.ConfigurationManager.AppSettings.Get("StartFullSyncTimeByPdd");
                return string.IsNullOrEmpty(Convert.ToString(seconds)) ? -60 : Convert.ToInt32(seconds);
            }
        }

        /// <summary>
        /// 阿里同步订购信息，设置时间
        /// </summary>
        public static string StartBuyTime
        {
            get
            {
                var startBuyTime = System.Configuration.ConfigurationManager.AppSettings.Get("StartBuyTime");
                return string.IsNullOrEmpty(Convert.ToString(startBuyTime)) ? Convert.ToString(DateTime.Now) : Convert.ToString(startBuyTime);
            }
        }

        /// <summary>
        /// 拼多多 设置 非待发货订单男是否显示 收件人信息
        /// </summary>
        public static bool PddNotWaitSendShowReciver
        {
            get
            {
                var val = System.Configuration.ConfigurationManager.AppSettings.Get("Pdd:NotWaitSendShowReciver") ?? "1";
                return val == "1";
            }
        }

        /// <summary>
        /// 同步是否被禁用（同步订单、商品）
        /// </summary>
        public static bool IsSyncDisabled
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsSyncDisabled") == "1";
            }
        }

        /// <summary>
        /// 是否【停止】商品列表同步的检测
        /// 包括商品列表是否正在同步，以及检测还没同步，再次触发同步
        /// </summary>
        public static bool IsSyncDisabledByProductExamine
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsSyncDisabledByProductExamine") == "1";
            }
        }

        /// <summary>
        /// 是否内测
        /// </summary>
        public static bool IsInnerTest
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsInnerTest") == "1";
            }
        }

        /// <summary>
        /// API执行警告时间，若超过这个时间会有日志记录
        /// </summary>
        public static int ApiExecuteWarningSeconds
        {
            get
            {
                var seconds = System.Configuration.ConfigurationManager.AppSettings.Get("ApiExecuteWarningSeconds").ToInt();
                if (seconds <= 0)
                    seconds = 30;
                return seconds;
            }
        }

        /// <summary>
        /// 单次请求最大线程数量
        /// </summary>
        public static int MaxThreadCountOneRequest
        {
            get
            {
                var seconds = System.Configuration.ConfigurationManager.AppSettings.Get("MaxThreadCountOneRequest").ToInt();
                if (seconds <= 0)
                    seconds = 10;
                return seconds;
            }
        }

        /// <summary>
        /// 迁移时：同时迁移最大店铺数，默认10个
        /// </summary>
        public static int MaxMigrateCountSameTime
        {
            get
            {
                var seconds = System.Configuration.ConfigurationManager.AppSettings.Get("MaxMigrateCountSameTime").ToInt();
                if (seconds <= 0)
                    seconds = 10;
                return seconds;
            }
        }

        /// <summary>
        /// 分库迁移时：同时迁移最大店铺数，默认20个
        /// </summary>
        public static int MaxFxMigrateCountSameTime
        {
            get
            {
                var seconds = System.Configuration.ConfigurationManager.AppSettings.Get("MaxFxMigrateCountSameTime").ToInt();
                if (seconds <= 0)
                    seconds = 20;
                return seconds;
            }
        }

        /// <summary>
        /// 分库迁移时：是否为补迁，默认0
        /// </summary>
        public static int FxMigrateIsNotUpdate
        {
            get
            {
                var isNotUpdate = System.Configuration.ConfigurationManager.AppSettings.Get("FxMigrateIsNotUpdate").ToInt();
                return isNotUpdate;
            }
        }

        /// <summary>
        /// 迁移时：同时同步最大店铺数，默认10个
        /// </summary>
        public static int MaxSyncCountSameTime
        {
            get
            {
                var seconds = System.Configuration.ConfigurationManager.AppSettings.Get("MaxSyncCountSameTime").ToInt();
                if (seconds <= 0)
                    seconds = 10;
                return seconds;
            }
        }

        /// <summary>
        /// 检查是否有积压的时间，默认5秒
        /// </summary>
        public static int TaskMaxDegreeOfParallelismTime
        {
            get
            {
                var seconds = System.Configuration.ConfigurationManager.AppSettings.Get("TaskMaxDegreeOfParallelismTime").ToInt();
                if (seconds <= 0)
                    seconds = 5;
                return seconds;
            }
        }

        /// <summary>
        /// 对比时：同时对比最大店铺数，默认10个
        /// </summary>
        public static int MaxContrastCountSameTime
        {
            get
            {
                var seconds = System.Configuration.ConfigurationManager.AppSettings.Get("MaxContrastCountSameTime").ToInt();
                if (seconds <= 0)
                    seconds = 10;
                return seconds;
            }
        }

        /// <summary>
        /// 最大消息队列的警告长度，超过时会发出钉钉消息警告，默认200个
        /// </summary>
        public static int MaxMessageQueueWarningCount
        {
            get
            {
                var seconds = System.Configuration.ConfigurationManager.AppSettings.Get("MaxMessageQueueWarningCount").ToInt();
                if (seconds <= 0)
                    seconds = 200;
                return seconds;
            }
        }

        /// <summary>
        /// 导出与对账任务最大消息队列的警告长度，超过时会发出飞书消息警告，默认100个
        /// </summary>
        public static int MaxMessageExportQueueWarningCount
        {
            get
            {
                var seconds = System.Configuration.ConfigurationManager.AppSettings.Get("MaxMessageQueueWarningCount").ToInt();
                if (seconds <= 0)
                    seconds = 100;
                return seconds;
            }
        }

        /// <summary>
        /// 图片服务地址
        /// </summary>
        public static string ImageServer
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ImageServer") ?? "";
            }
        }

        /// <summary>
        /// 图片服务地址
        /// </summary>
        public static string ImageServerOut
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ImageServerOut") ?? "";
            }
        }

        /// <summary>
        /// 阿里图片服务地址
        /// </summary>
        public static string AlibabaImageServer
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaImageServer") ?? "http://img.dgjapp.com/";
            }
        }

        /// <summary>
        /// 平台类型，若未配置，默认为1688
        /// </summary>
        [Obsolete("不要使用该属性作为当前登录店铺的平台判断，请使用SiteContext.Current.CurrentLoginShop.PlatformType代替")]
        public static string Platform
        {
            get
            {
                var pt = System.Configuration.ConfigurationManager.AppSettings.Get("Platform")?.Trim();
                return pt ?? "";
            }
        }
        /// <summary>
        /// 需要同步售后的平台
        /// </summary>
        public static string PlatformAfterSale
        {
            get
            {
                var pt = System.Configuration.ConfigurationManager.AppSettings.Get("PlatformAfterSale")?.Trim();
                // return pt ?? "TouTiao,Pinduoduo,Alibaba,Taobao,AlibabaC2M,KuaiShou,Jingdong,WxVideo,TaobaoMaiCai";
                if (pt.IsNotNullOrEmpty())
                {
                    if (CloudPlatformType == "Alibaba")
                    {
                        if (!pt.Contains("Other_Heliang"))
                        {
                            pt += ",Other_Heliang";
                        }
                        if (!pt.Contains("Other_JuHaoMai"))
                        {
                            pt += ",Other_JuHaoMai";
                        }
                        if (!pt.Contains("BiliBili"))
                        {
                            pt += ",BiliBili";
                        }
                        if (!pt.Contains("Other_HaoYouDuo"))
                        {
                            pt += ",Other_HaoYouDuo";
                        }
                        if (!pt.Contains("TaobaoMaiCaiV2"))
                        {
                            pt += ",TaobaoMaiCaiV2";
                        }
                    }
                    return pt;
                }
                if (CloudPlatformType == "Jingdong")
                    pt = "Jingdong";
                else if (CloudPlatformType == "TouTiao")
                    pt = "TouTiao";
                else if (CloudPlatformType == "Pinduoduo")
                    pt = "Pinduoduo,KuaiTuanTuan";
                else
                    pt = "!TouTiao,Pinduoduo,KuaiTuanTuan,Jingdong";
                return pt;
            }
        }

        /// <summary>
        /// 是否写数据库日志
        /// </summary>
        public static bool IsWriteDbLog
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsWriteDbLog") == "1";
            }
        }

        /// <summary>
        /// 是否使用1688新版API
        /// </summary>
        public static bool IsUse1688NewApi
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsUse1688NewApi") == "1";
            }
        }

        /// <summary>
        /// 1688授权回调加密秘钥
        /// </summary>
        public static string AlibabaAuthCallbackKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:AuthCallbackKey") ?? "grewoiga";
            }
        }

        /// <summary>
        /// 登录cookie加密秘钥
        /// </summary>
        public static string LoginCookieEncryptKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("LoginCookieEncryptKey") ?? "grewoiga";
            }
        }

        /// <summary>
        /// 数据库请求加密秘钥
        /// </summary>
        public static string DbApiAccessEncryptKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DbApiAccessEncryptKey") ?? "fhudhgqe";
            }
        }

        /// <summary>
        /// 应用程序Web访问链接，在生成店管家快捷方式时会用到，需在外网可访问
        /// </summary>
        public static string ApplicationWebUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ApplicationWebUrl");
            }
        }

        /// <summary>
        /// 1688登录失败的重定向授权地址
        /// </summary>
        public static string AlibabaRedirectAuthUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaRedirectAuthUrl");
            }
        }

        /// <summary>
        /// 授权统一回调域名 
        /// </summary>
        public static string AuthCallbackUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AuthCallBackDomain");
            }
        }

        /// <summary>
        /// 京东授权回调域名(必须放在云鼎，所以和通用的不一样)
        /// </summary>
        public static string JdAuthCallbackUrl
        {
            get
            {
                var url = System.Configuration.ConfigurationManager.AppSettings.Get("JdAuthCallBackDomain");
                if (string.IsNullOrEmpty(url))
                    return "http://jdauth.dgjapp.com";
                else
                    return url;
            }
        }

        /// <summary>
        /// 韵达网点Apihost
        /// </summary>
        public static string YunDaApiHost
        {
            get
            {

                var url = System.Configuration.ConfigurationManager.AppSettings.Get("YunDaApiHost");
                if (string.IsNullOrEmpty(url))
                    return "http://orderdev.yundasys.com:10110";
                else
                    return url;
            }
        }
        #endregion

        #region 应用Key和Secret

        #region 微信端应用key与密钥

        /// <summary>
        /// 新版关联的小程序-AppId
        /// </summary>
        public static string WxAppId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WXAppId") ?? "wx8e023a4836a52f22";
            }
        }

        /// <summary>
        /// 新版关联的小程序-Secret
        /// </summary>
        public static string WxSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WXAppSecret") ?? "a7b6e0568975147c1661ddb9520f6ff6";
            }
        }

        /// <summary>
        /// 新版关联的小程序-绑定店铺，是否开启检查是同个用户群体才能绑定
        /// </summary>
        public static bool WxIsGroup
        {
            get
            {
                var wxIsGroup = System.Configuration.ConfigurationManager.AppSettings.Get("WxIsGroup") ?? "0";

                return wxIsGroup == "1";
            }
        }


        /// <summary>
        /// 微商小程序-AppId
        /// </summary>
        public static string WsXcxAppId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WsXcxAppId") ?? "wxbe31668c3e7f7d50";
            }
        }
        /// <summary>
        /// 微商小程序-Secret
        /// </summary>
        public static string WsXcxSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WsXcxAppSecret") ?? "1511029242e304408ce54f7483fdbc91";
            }
        }

        /// <summary>
        /// 微信收单小程序-AppId
        /// </summary>
        public static string SdXcxAppId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxSDAppId") ?? "wx67a51725fc173ec3";
            }
        }
        /// <summary>
        /// 微信收单小程序-Secret
        /// </summary>
        public static string SdXcxSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxSDAppSecret") ?? "39d456a772f4fbe3da0c5a297abd05d7";
            }
        }


        #endregion

        #region 运费模板相关配置
        /// <summary>
        /// 运费模板数据库
        /// </summary>
        public static string FreightTemplateDB
        {
            get
            {
                return ConfigurationManager.ConnectionStrings["FreightTemplateDB"]?.ConnectionString ?? "";
            }
        }
        /// <summary>
        /// 运费模板API路径
        /// </summary>
        public static string FreightTemplateApiUrlPath
        {
            get
            {
                return ConfigurationManager.AppSettings["FreightTemplateApiUrlPath"] ?? "/api/router";
            }
        }
        /// <summary>
        /// 运费模板API地址
        /// </summary>
        public static string FreightTemplateApiUrl
        {
            get
            {
                //if (IsLocalDbDebug)
                //    return "http://localhost:9093";
                //return System.Configuration.ConfigurationManager.AppSettings["FreightTemplateApiUrl"] ?? "http://FreightTemplate.dgjapp.com";
                return "http://**************";
            }
        }
        /// <summary>
        /// 运费模板API APPkey
        /// </summary>
        public static string FreightTemplateAppkey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["FreightTemplateAppkey"] ?? "23275388";
            }
        }
        /// <summary>
        /// 运费模板API AppSecret
        /// </summary>
        public static string FreightTemplateAppsecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["FreightTemplateAppsecret"] ?? "1031469n4j3482q09ZhU0JP1AYbxu87d";
            }
        }
        #endregion

        #region 淘宝应用key与密钥

        /// <summary>
        ///  淘宝 （电子面单） 应用key
        /// </summary>
        public static string TaobaWaybillAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Taobao_Waybill:AppKey") ?? "23275388";
            }
        }

        /// <summary>
        ///  淘宝 （电子面单） 应用秘钥
        /// </summary>
        public static string TaobaoWaybillAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Taobao_Waybill:AppSecret") ?? "ca0f2dbf9155a25a5883fc6b3f3421c8";
            }
        }

        /// <summary>
        ///  淘宝应用key-打单使用
        /// </summary>
        public static string AlidayuAccessKeyId
        {
            get
            {
                var alidayuAccessKeyId = System.Configuration.ConfigurationManager.AppSettings.Get("Alidayu_AccessKeyId");
                return string.IsNullOrEmpty(Convert.ToString(alidayuAccessKeyId)) ? "LTAI5tJw2mbZm359aBvTPjpG" : Convert.ToString(alidayuAccessKeyId);
            }
        }


        /// <summary>
        /// 阿里大鱼AccessKeySecret
        /// </summary>
        public static string AlidayuAccessKeySecret
        {
            get
            {
                var alidayuAccessKeySecret = System.Configuration.ConfigurationManager.AppSettings.Get("Alidayu_AccessKeySecret");
                return string.IsNullOrEmpty(Convert.ToString(alidayuAccessKeySecret)) ? "******************************" : Convert.ToString(alidayuAccessKeySecret);
            }
        }


        /// <summary>
        /// 阿里大鱼TemplateCode
        /// </summary>
        public static string AlidayuTemplateCode
        {
            get
            {
                var alidayuTemplateCode = System.Configuration.ConfigurationManager.AppSettings.Get("Alidayu_TemplateCode");
                return string.IsNullOrEmpty(Convert.ToString(alidayuTemplateCode)) ? "SMS_121857214" : Convert.ToString(alidayuTemplateCode);
            }
        }
        /// <summary>
        /// 阿里大鱼密码短信TemplateCode
        /// </summary>
        public static string AlidayuPwdTemplateCode
        {
            get
            {
                var alidayuTemplateCode = System.Configuration.ConfigurationManager.AppSettings.Get("Alidayu_Pwd_TemplateCode");
                return string.IsNullOrEmpty(Convert.ToString(alidayuTemplateCode)) ? "SMS_257780424" : Convert.ToString(alidayuTemplateCode);
            }
        }
        /// <summary>
        /// 阿里大鱼注销账户短信TemplateCode
        /// </summary>
        public static string AlidayuUnBindUserTemplateCode
        {
            get
            {
                var alidayuTemplateCode = System.Configuration.ConfigurationManager.AppSettings.Get("Alidayu_UnBindUser_TemplateCode");
                return string.IsNullOrEmpty(Convert.ToString(alidayuTemplateCode)) ? "SMS_462415427" : Convert.ToString(alidayuTemplateCode);
            }
        }

        /// <summary>
        /// 阿里大鱼更换手机号短信TemplateCode
        /// </summary>
        public static string AlidayuReplaceMobileTemplateCode
        {
            get
            {
                var alidayuTemplateCode = System.Configuration.ConfigurationManager.AppSettings.Get("Alidayu_ReplaceMobile_TemplateCode");
                return string.IsNullOrEmpty(Convert.ToString(alidayuTemplateCode)) ? "SMS_478390778" : Convert.ToString(alidayuTemplateCode);
            }
        }

        /// <summary>
        /// </summary>
        public static string TaobaAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Taobao:AppKey") ?? "";
            }
        }

        /// <summary>
        ///  淘宝应用秘钥-打单使用
        /// </summary>
        public static string TaobaoAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Taobao:AppSecret") ?? "";
            }
        }

        /// <summary>
        ///  淘宝应用key-订单接口使用
        /// </summary>
        public static string TaobaAppKey2
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Taobao:AppKey2") ?? "";
            }
        }

        /// <summary>
        ///  淘宝应用秘钥-订单接口使用
        /// </summary>
        public static string TaobaoAppSecret2
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Taobao:AppSecret2") ?? "";
            }
        }

        /// <summary>
        /// 淘宝ApiUrl
        /// </summary>
        public static string TaoboApiUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Taobao:ApiUrl") ?? "";
            }
        }

        #endregion

        #region 1688 应用key与秘钥

        /// <summary>
        ///  1688 应用key与秘钥
        /// </summary>
        public static string AlibabaAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:AppKey") ?? "1004884";
            }
        }

        /// <summary>
        ///  1688 应用秘钥
        /// </summary>
        public static string AlibabaAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:AppSecret") ?? "ZoLb9v!Mwin";
            }
        }


        #endregion


        #region 1688 分单应用key与秘钥

        /// <summary>
        ///  1688 分单应用key与秘钥
        /// </summary>
        public static string AlibabaFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:FxAppKey") ?? "3261763";
            }
        }

        /// <summary>
        ///  1688 分单应用秘钥
        /// </summary>
        public static string AlibabaFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:FxAppSecret") ?? "PyIkY9kRV6ei";
            }
        }


        #endregion


        #region 1688入端 应用key与秘钥

        /// <summary>
        ///  1688入端 应用key与秘钥
        /// </summary>
        public static string AlibabaWkAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaWk:AppKey") ?? "9761735";
            }
        }

        /// <summary>
        ///  1688入端 应用秘钥
        /// </summary>
        public static string AlibabaWkAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaWk:AppSecret") ?? "ctv5uLabdG7I";
            }
        }

        #endregion


        #region 1688商机 应用key与秘钥

        /// <summary>
        ///  1688商机 应用key与秘钥
        /// </summary>
        public static string AlibabaSjAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaSj:AppKey") ?? "2608795";
            }
        }

        /// <summary>
        ///  1688商机 应用秘钥
        /// </summary>
        public static string AlibabaSjAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaSj:AppSecret") ?? "yGCnDU1w4Ip4";
            }
        }

        #endregion


        #region 1688商机免费版 应用key与秘钥

        /// <summary>
        ///  1688商机免费版 应用key与秘钥
        /// </summary>
        public static string AlibabaSjFreeAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaSjFree:AppKey") ?? "4676472";
            }
        }

        /// <summary>
        ///  1688商机免费版 应用秘钥
        /// </summary>
        public static string AlibabaSjFreeAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaSjFree:AppSecret") ?? "WQf7xe8VKDA";
            }
        }

        #endregion

        #region 1688 C2M应用key与秘钥

        /// <summary>
        ///  1688 应用key与秘钥
        /// </summary>
        public static string AlibabaC2MAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaC2M:AppKey") ?? "3531515";
            }
        }

        /// <summary>
        ///  1688 应用秘钥
        /// </summary>
        public static string AlibabaC2MAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaC2M:AppSecret") ?? "pkV6U2U7CA";
            }
        }


        #endregion

        #region 1688 轻应用key与秘钥

        /// <summary>
        ///  1688 轻应用key与秘钥
        /// </summary>
        public static string AlibabaQingAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaQing:AppKey") ?? "5382198";
            }
        }

        /// <summary>
        ///  1688 轻应用秘钥
        /// </summary>
        public static string AlibabaQingAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaQing:AppSecret") ?? "SUCzPBBX1tPj";
            }
        }


        #endregion

        #region 1688 主客铺货应用key与秘钥

        /// <summary>
        ///  1688 主客铺货应用key与秘钥
        /// </summary>
        public static string AlibabaZkphAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:ZkphAppKey") ?? "8076003";  //新方案应用
                //return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:ZkphAppKey") ?? "3760838";
            }
        }

        /// <summary>
        ///  1688 主客铺货应用秘钥
        /// </summary>
        public static string AlibabaZkphAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:ZkphAppSecret") ?? "9PAIEWstZcj";  //新方案应用
                //return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:ZkphAppSecret") ?? "Hu5EUVDPz4q";
            }
        }


        #endregion


        #region 1688 跨境应用key与秘钥

        /// <summary>
        ///  1688 跨境应用key与秘钥
        /// </summary>
        public static string AlibabaCrossAppKey
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("Alibaba:CrossAppKey") ?? "5790791";  //新方案应用
            }
        }

        /// <summary>
        ///  1688 跨境应用秘钥
        /// </summary>
        public static string AlibabaCrossAppSecret
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("Alibaba:CrossAppSecret") ?? "dvt5mL2VcgkD";  //新方案密钥
            }
        }


        #endregion


        /// <summary>
        ///探数API【https://console.tanshuapi.com/data】
        /// </summary>
        public static string TanShuApiAppKey
        {
            get
            {
                //592b247b775889604f00037f17a83ad5 :测试阿廖
                //3bf76c510a5137e981faf9c878f7d797 :开发liub
                return ConfigurationManager.AppSettings.Get("TanShuApiAppKey") ?? "3bf76c510a5137e981faf9c878f7d797";
            }
        }



        #region 淘宝买菜 应用Key与秘钥

        /// <summary>
        ///  淘宝买菜 应用Key
        /// </summary>
        public static string TaobaoMaiCaiAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:TbmcAppKey") ?? "34704816";
            }
        }

        /// <summary>
        ///  淘宝买菜 秘钥
        /// </summary>
        public static string TaobaoMaiCaiAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:TbmcAppSecret") ?? "5d9bc7b5562e0862de69623c73169175";
            }
        }


        #endregion



        #region 拼多多 应用key与秘钥

        /// <summary>
        ///  拼多多 应用key与秘钥
        /// </summary>
        public static string PinduoduoAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Pinduoduo:AppKey") ?? "afabb0e68b3443b9a0dda11c1442a042";
            }
        }
        /// <summary>
        ///  拼多多 应用秘钥
        /// </summary>
        public static string PinduoduoAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Pinduoduo:AppSecret") ?? "bf3a8a74b9977cf0cc65e21a4cbfbca9f7962093";
            }
        }

        /// <summary>
        ///  拼多多 应用秘钥
        /// </summary>
        public static string PinduoduoApiUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Pinduoduo:ApiUrl") ?? "http://gw-api.pinduoduo.com/api/router";
            }
        }

        /// <summary>
        ///  拼多多 默认版本，默认为空
        /// </summary>
        public static string PinduoduoDefaultVersion
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Pinduoduo:DefaultVersion") ?? "3";
            }
        }

        /// <summary>
        ///  拼多多 迁移数据默认的批次数量
        /// </summary>
        public static int PinduoduoDataMigrateDefaultBatchCount
        {
            get
            {
                var count = System.Configuration.ConfigurationManager.AppSettings.Get("Pinduoduo:DataMigrateDefaultBatchCount") ?? "";
                var t = count.ToInt();
                if (t <= 0)
                    return 500;
                else
                    return t;

            }
        }

        /// <summary>
        /// 打印记录迁移数据默认的批次数量
        /// </summary>
        public static int PrintHistoryMigrateDefaultBatchCount
        {
            get
            {
                var count = System.Configuration.ConfigurationManager.AppSettings.Get("PrintHistoryMigrateDefaultBatchCount") ?? "";
                var t = count.ToInt();
                if (t <= 0)
                    return 2000;
                else
                    return t;

            }
        }

        /// <summary>
        /// 打印记录迁移是否检测重复值(默认不配置)
        /// </summary>
        public static bool IsCheckPrintHistoryMigrate
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsCheckPrintHistoryMigrate") == "1";
            }
        }

        #region 分段迁移、全量迁移

        /// <summary>
        /// 打印记录迁移：是否是初始迁移（去除自增）
        /// </summary>
        public static bool IsInitialPrintHistoryMigrate
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsInitialPrintHistoryMigrate") == "1";
            }
        }

        /// <summary>
        /// 打印记录迁移: false 分段迁移、true 整段迁移
        /// </summary>
        public static bool IsFullPrintHistoryMigrate
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsFullPrintHistoryMigrate") == "1";
            }
        }

        /// <summary>
        /// 打印记录迁移: 迁移程序最小起始ID
        /// </summary>
        public static long PrintHistoryMigrateMinStartId
        {
            get
            {
                var count = System.Configuration.ConfigurationManager.AppSettings.Get("PrintHistoryMigrateMinStartId");
                var t = count.ToLong();
                if (t <= 0)
                    return 7667750000;
                else
                    return t;
            }
        }

         /// <summary>
        /// 打印记录迁移: 迁移程序偏移量
        /// </summary>
        public static long PrintHistoryMigrateOffset
        {
            get
            {
                var count = System.Configuration.ConfigurationManager.AppSettings.Get("PrintHistoryMigrateOffset");
                var t = count.ToLong();
                if (t <= 0)
                    return 40000000;
                else
                    return t;
            }
        }


        /// <summary>
        /// 打印记录迁移：数据库是否开启关闭自增：true 开启、false 关闭
        /// </summary>
        public static bool IsAutoIncrementPrintHistoryKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsAutoIncrementPrintHistoryKey") == "1";
            }
        }

        #endregion

        #endregion

        #region 拼多多 分单应用key与秘钥

        /// <summary>
        ///  拼多多 分单应用key与秘钥
        /// </summary>
        public static string PddFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Pinduoduo:FxAppKey") ?? "b7e73126680b4a17892152ec0b277066";
            }
        }

        /// <summary>
        ///  拼多多 分单应用秘钥
        /// </summary>
        public static string PddFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Pinduoduo:FxAppSecret") ?? "ea5f0bc49cc3a45e6ff204c02ccc33afabd538df";
            }
        }

        #endregion

        #region 拼多多 智速打单 分单应用key与秘钥

        /// <summary>
        ///  拼多多 分单应用key与秘钥
        /// </summary>
        public static string PddFxFastAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Pinduoduo:FxFastAppKey") ?? "47636360e272424a9c3c131433ae9c72";
            }
        }

        /// <summary>
        ///  拼多多 分单应用秘钥
        /// </summary>
        public static string PddFxFastAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Pinduoduo:FxFastAppSecret") ?? "e6320e0c87896e08567efa91789a21e075e11dfc";
            }
        }

        #endregion

        #region 拼多多电子面单 应用key与秘钥

        /// <summary>
        ///  拼多多电子面单 应用key与秘钥
        /// </summary>
        public static string PddWaybillAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("PddWaybill:AppKey") ?? "99af635d613046799dcbab8bd684a15a";
            }
        }
        /// <summary>
        ///  拼多多电子面单 应用秘钥
        /// </summary>
        public static string PddWaybillAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("PddWaybill:AppSecret") ?? "24d3666b794159058fa5a3275543d560c49d8510";
            }
        }


        #endregion

        #region 众邮电子面单 应用key与秘钥,授权好的Token
        /// <summary>
        ///  众邮电子面单 应用key与秘钥
        /// </summary>
        public static string ZYKDWaybillAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ZYKDWaybill:AppKey") ?? "de341bea955d484bbaae762a163da871";
            }
        }
        /// <summary>
        ///  众邮电子面单 应用秘钥
        /// </summary>
        public static string ZYKDWaybillAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ZYKDWaybill:AppSecret") ?? "0eca00386a9f48999b1e7b58248c0fd8";
            }
        }
        /// <summary>
        /// 众邮电子面单 授权好的Token
        /// </summary>
        public static string ZYKDWaybillToken
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ZYKDWaybill:Token") ?? "13e5176b30da443e9b4cfd9a62a81cbc";
            }
        }
        /// <summary>
        /// 开放平台账号
        /// </summary>
        public static string ZYKDWaybillUserCode
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ZYKDWaybill:UserCode") ?? "18603065384";
            }
        }

        #endregion

        #region 中通快递
        /// <summary>
        /// 合作商编码
        /// </summary>
        public static string ZTOCompanyId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ZOT:ZOTCompanyId") ?? "208df10b4a0d4102bfb012ff1d7957b2";
            }
        }
        /// <summary>
        /// 合作商签名key
        /// </summary>
        public static string ZTODigestKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ZOT:ZOTDigestKey") ?? "8CD0F1070CE071339070936202C7D78E";
            }
        }

        /// <summary>
        /// 中通直连接口轨迹订阅回调地址
        /// </summary>
        public static string ZTOTraceSubCallbackUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ZOT:ZOTTraceSubCallbackUrl") ?? "http://pddlogistics.dgjapp.com/zto/message";
            }
        }
        #endregion

        #region 百世快递
        /// <summary>
        /// 合作商编码
        /// </summary>
        public static string HTKYpartnerId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("HTKY:HTKYpartnerId") ?? "65754";
            }
        }
        /// <summary>
        /// 合作商签名key
        /// </summary>
        public static string HTKYpartnerKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("HTKY:HTKYpartnerKey") ?? "ajt1fb24dhs5";
            }
        }
        #endregion

        #region 德邦快递电子面单
        /// <summary>
        /// 德邦电子面单 应用Key
        /// </summary>
        public static string DBKDWaybillAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DBKD:AppKey") ?? "f96e42ae0f7d04167a612dcce9621b9a";
            }
        }
        /// <summary>
        /// 德邦电子面单 sign值
        /// </summary>
        public static string DBKDWaybillSign
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DBKD:Sign") ?? "UCFQ";
            }
        }
        /// <summary>
        /// 德邦电子面单 公司编码
        /// </summary>
        public static string DBKDWaybillCode
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DBKD:CompanyCode") ?? "EWBSZDGJRJYXGS";
            }
        }
        #endregion

        #region 韵达快递
        public static string YunDaAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("YunDa:AppKey") ?? "000425";
            }
        }
        public static string YunDaAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("YunDa:AppSecret") ?? "5cd8ee35edb711ea8e21897623644e37";
            }
        }
        #endregion

        #region 菜鸟官方 应用key与秘钥

        /// <summary>
        ///  菜鸟官方 应用key
        /// </summary>
        public static string CaiNiaoAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("CaiNiao:AppKey") ?? "566703";
            }
        }
        /// <summary>
        ///  菜鸟官方 应用秘钥
        /// </summary>
        public static string CaiNiaoAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("CaiNiao:AppSecret") ?? "1031469n4j3482q09ZhU0JP1AYbxu87d";
            }
        }
        /// <summary>
        ///  菜鸟官方授权 Url
        /// </summary>
        public static string CaiNiaoUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("CaiNiao:Url") ?? "https://lcp.cloud.cainiao.com";
            }
        }

        /// <summary>
        ///  菜鸟官方请求api的 Url
        /// </summary>
        public static string CaiNiaoDailyUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("CaiNiao:DailyUrl") ?? "http://link.cainiao.com/gateway/link.do";
            }
        }



        /// <summary>
        ///  菜鸟官方授权回调 Url
        /// </summary>
        public static string CaiNiaoRedirectUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("CaiNiao:RedirectUrl") ?? "https://www.dgjapp.com:30001";
            }
        }


        #endregion

        #region 小店 应用key与秘钥

        /// <summary>
        ///  小店 应用key
        /// </summary>
        public static string XiaoDianAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("XiaoDian:AppKey") ?? "100735";
            }
        }
        /// <summary>
        ///  小店 应用秘钥
        /// </summary>
        public static string XiaoDianAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("XiaoDian:AppSecret") ?? "3FDC91EF7E02732FBEEE7E93EA57B371";
            }
        }

        #endregion

        #region 蘑菇街/美丽说 应用key与秘钥

        /// <summary>
        ///  蘑菇街/美丽说 应用key
        /// </summary>
        public static string MoGuAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MoGu:AppKey") ?? "101444";
            }
        }
        /// <summary>
        ///  蘑菇街/美丽说 应用秘钥
        /// </summary>
        public static string MoGuAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MoGu:AppSecret") ?? "007C741556A63FF1D4C2ED261FA7204F";
            }
        }

        #endregion

        #region 蘑菇街/美丽说 分销应用key与秘钥

        /// <summary>
        ///  蘑菇街/美丽说 应用key
        /// </summary>
        public static string MoGuFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MoGuFx:AppKey") ?? "103567";
            }
        }
        /// <summary>
        ///  蘑菇街/美丽说 应用秘钥
        /// </summary>
        public static string MoGuFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MoGuFx:AppSecret") ?? "93C1F6B7A01F0CD9B82B88B955001F66";
            }
        }

        #endregion

        #region 有赞 应用key与秘钥

        /// <summary>
        ///  有赞 应用key
        /// </summary>
        public static string YouZanAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("YouZan:AppKey") ?? "9dead7a0e2adca1a15";
            }
        }
        /// <summary>
        ///  有赞 应用秘钥
        /// </summary>
        public static string YouZanAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("YouZan:AppSecret") ?? "ed4c06980606f48b862f8cc4b2f0f1b9";
            }
        }

        #endregion

        #region 有赞 应用key与秘钥

        /// <summary>
        ///  有赞 分销应用key
        /// </summary>
        public static string YouZanFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("YouZanFx:AppKey") ?? "3b05414990801febfb";
            }
        }
        /// <summary>
        ///  有赞 应用秘钥
        /// </summary>
        public static string YouZanFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("YouZanFx:AppSecret") ?? "c087bad9cf0a491a13b57d8d3fa56d94";
            }
        }

        #endregion

        #region 微盟 应用key与秘钥

        /// <summary>
        ///  微盟 应用key
        /// </summary>
        public static string WeiMengAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiMeng:AppKey") ?? "B9C1D17E87EE7BF2480B67E43BF9AA87";
            }
        }
        /// <summary>
        ///  微盟 应用秘钥
        /// </summary>
        public static string WeiMengAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiMeng:AppSecret") ?? "9B4C48570AB0B51FE5B9D2FE3C60EAB3";
            }
        }

        /// <summary>
        ///  微盟消息 ProviderClientId
        /// </summary>
        public static string WeiMengProviderClientId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiMeng:ProviderClientId") ?? "08895E0C6874A388371B372A13F49B49";
            }
        }
        /// <summary>
        ///   微盟消息 ProviderClientSecret
        /// </summary>
        public static string WeiMengProviderClientSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiMeng:ProviderClientSecret") ?? "58F0ED0E0F399B6C3BC6981E40E2E524";
            }
        }

        #endregion

        #region 微盟Fx 应用key与秘钥

        /// <summary>
        ///  微盟 应用key
        /// </summary>
        public static string WeiMengFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiMengFx:AppKey") ?? "8DC41BE2A6217C1453A2A09A7804B20B";
            }
        }
        /// <summary>
        ///  微盟 应用秘钥
        /// </summary>
        public static string WeiMengFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiMengFx:AppSecret") ?? "697547D010256F58599F6741503BCBF8";
            }
        }

        #endregion

        #region 考拉 应用key与秘钥

        /// <summary>
        ///  考拉 应用key
        /// </summary>
        public static string KaoLaAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KaoLa:AppKey") ?? "b3a6feeb035f29eaf19ae65082e8593f";
            }
        }
        /// <summary>
        ///  考拉 应用秘钥
        /// </summary>
        public static string KaoLaAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KaoLa:AppSecret") ?? "5acaa489e99fb136ca64a6a63ea308189bccd3ce82222db0d5274d1f0abe1660";
            }
        }

        #endregion

        #region 苏宁 应用key与秘钥

        /// <summary>
        ///  苏宁 应用key
        /// </summary>
        public static string SuningAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Suning:AppKey") ?? "b60b52ac00505cb576a0c3e0d9372fb9";
            }
        }
        /// <summary>
        ///  苏宁 应用秘钥
        /// </summary>
        public static string SuningAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Suning:AppSecret") ?? "1286d6eb865f23344c1aa7491cd79dd0";
            }
        }

        /// <summary>
        ///  苏宁 应用收费代码（授权时需要）
        /// </summary>
        public static string SuningItemCode
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Suning:ItemCode") ?? "1";
            }
        }

        #endregion        

        #region 苏宁 分销应用key与秘钥

        /// <summary>
        ///  苏宁 分销应用key
        /// </summary>
        public static string SuningFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("SuningFx:AppKey") ?? "8077bd36bbf3ef0f3d88a823b18431b1";
            }
        }
        /// <summary>
        ///  苏宁 应用秘钥
        /// </summary>
        public static string SuningFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("SuningFx:AppSecret") ?? "13d95a314d5c00d3d945af6dbd494a44";
            }
        }

        /// <summary>
        ///  苏宁 应用收费代码（授权时需要）
        /// </summary>
        public static string SuningFxItemCode
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("SuningFx:ItemCode") ?? "2";
            }
        }

        #endregion        

        #region 快递鸟
        public static string KuaiDiNiaoAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiDiNiao:AppKey") ?? "1312996";
            }
        }

        public static string KuaiDiNiaoAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiDiNiao:AppSecret") ?? "cf2c6c94-3fae-4026-ae83-ff4f48e13234";
            }
        }

        public static string KuaiDiNiaoApiUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiDiNiao:ApiUrl");
            }
        }
        #endregion

        #region 微店 应用key与秘钥

        /// <summary>
        ///  微店 应用key
        /// </summary>
        public static string WeiDianAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiDian:AppKey") ?? "690712";
            }
        }
        /// <summary>
        ///  微店 应用秘钥
        /// </summary>
        public static string WeiDianAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiDian:AppSecret") ?? "ee934f9351f9e35357e015228f61d1d0";
            }
        }

        #endregion

        #region 微店 分销应用key与秘钥

        /// <summary>
        ///  微店 应用key
        /// </summary>
        public static string WeiDianFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiDianFx:AppKey") ?? "3703030503";
            }
        }
        /// <summary>
        ///  微店 应用秘钥
        /// </summary>
        public static string WeiDianFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiDianFx:AppSecret") ?? "1085870d06ab836be9b17cc432bf235f";
            }
        }

        #endregion

        #region 唯品会 应用key与秘钥   
        /// <summary>
        ///  唯品会 应用key
        /// </summary>
        public static string VipShopAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("VipShop:AppKey") ?? "a722074a";
            }
        }
        /// <summary>
        ///  唯品会 应用秘钥
        /// </summary>
        public static string VipShopAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("VipShop:AppSecret") ?? "6EC72934A6A874B5E2348221869F5719";
            }
        }
        #endregion

        #region 唯品会 分销应用key与秘钥   
        /// <summary>
        ///  唯品会 应用key
        /// </summary>
        public static string VipShopFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("VipShopFx:AppKey") ?? "a076017a";
            }
        }
        /// <summary>
        ///  唯品会 应用秘钥
        /// </summary>
        public static string VipShopFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("VipShopFx:AppSecret") ?? "D1D456243669234C97E644FA0DDF90E7";
            }
        }
        #endregion

        #region 头条 应用key与秘钥    
        /// <summary>
        ///  头条 应用key
        /// </summary>
        public static string TouTiaoAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiao:AppKey") ?? "6838756062733010445";
            }
        }
        /// <summary>
        ///  头条 应用秘钥
        /// </summary>
        public static string TouTiaoAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiao:AppSecret") ?? "11248815-739a-4f43-b3f6-31f267429fb9";
            }
        }
        #endregion

        #region 头条 分单应用key与秘钥    
        /// <summary>
        ///  头条 应用key
        /// </summary>
        public static string TouTiaoFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoFx:AppKey") ?? "6925303336539309583";
            }
        }
        /// <summary>
        ///  头条 应用秘钥
        /// </summary>
        public static string TouTiaoFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoFx:AppSecret") ?? "f39f3bc6-217e-4bef-8377-143914177b16";
            }
        }

        /// <summary>
        ///  抖音非店铺电子面单登录站点
        /// </summary>
        public static string TouTiaoEbillLoginUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoFx:EbillLoginUrl") ?? "https://ebill.douyinec.com/login";
            }
        }

        /// <summary>
        ///  头条 api proxy
        /// </summary>
        public static string TouTiaoApiProxyUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiao:ApiProxyUrl") ?? "http://ddecapi2.dgjapp.com";
            }
        }

        #endregion

        #region 头条 分单铺货应用key与秘钥-2024.07.12    
        /// <summary>
        ///  头条 分单铺货应用key
        /// </summary>
        public static string TouTiaoFxListingAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoFxListing:AppKey") ?? "7390211222756394559";

                //临时用主客铺货 2024.07.25
                //return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoFxListing:AppKey") ?? "6925303336539309583";

            }
        }
        /// <summary>
        ///  头条 分单铺货应用秘钥
        /// </summary>
        public static string TouTiaoFxListingAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoFxListing:AppSecret") ?? "1b7d2145-435f-4bea-ad77-4b7d524942cb";

                //临时用主客铺货 2024.07.25
                //return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoFxListing:AppSecret") ?? "f39f3bc6-217e-4bef-8377-143914177b16";

            }
        }
        /// <summary>
        ///  头条 分单铺货应用服务Id
        /// </summary>
        public static string TouTiaoFxListingServiceId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoFxListing:ServiceId") ?? "32883";
            }
        }

        #endregion

        #region 头条 分单新主体应用key与秘钥    
        /// <summary>
        ///  头条 应用key
        /// </summary>
        public static string TouTiaoFxNewAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoFxNew:AppKey") ?? "7152331588175513101";
            }
        }
        /// <summary>
        ///  头条 应用秘钥
        /// </summary>
        public static string TouTiaoFxNewAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoFxNew:AppSecret") ?? "d23a152d-17ea-45fb-bb13-c06595b65efa";
            }
        }



        #endregion

        #region 头条 即时零售 分单应用key与秘钥    
        /// <summary>
        /// 头条即时零售 分单应用key
        /// </summary>
        public static string FxTouTiaoSaleShopAppKey => ConfigurationManager.AppSettings.Get("FxTouTiaoSaleShop:AppKey") ?? "7412907893928216091";

        /// <summary>
        /// 头条即时零售 分单应用秘钥
        /// </summary>
        public static string FxTouTiaoSaleShopAppSecret => ConfigurationManager.AppSettings.Get("FxTouTiaoSaleShop:AppSecret") ?? "c25b1090-9445-459a-906a-be5f76a4c9dc";

        /// <summary>
        /// 头条即时零售 分单应用ServiceId
        /// </summary>
        public static string FxTouTiaoSaleShopAppServiceId => ConfigurationManager.AppSettings.Get("FxTouTiaoSaleShop:AppServiceId") ?? "35574";

        #endregion
        
        #region 京东 应用key与秘钥

        /// <summary>
        ///  京东 应用key
        /// </summary>
        public static string JingDongAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JingDong:AppKey") ?? "557C37C5BB73B0A55A1A01422F0705F4";
            }
        }
        /// <summary>
        ///  京东 应用秘钥
        /// </summary>
        public static string JingDongAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JingDong:AppSecret") ?? "00c553795cd44e7aa2db5853001c968a";
            }
        }

        /// <summary>
        ///  京东接口是否使用代理
        /// </summary>
        public static bool JingDongIsApiUseProxy
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("JingDong:IsApiUseProxy") ?? "1";
                return value == "1" || value.ToLower() == "true";
            }
        }
        #endregion

        #region 京东 分销应用key与秘钥

        /// <summary>
        ///  京东 应用key
        /// </summary>
        public static string JingDongFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JingDongFx:AppKey") ?? "5C3021663C9E49C79351CAF4EB2A4CF1";
            }
        }
        /// <summary>
        ///  京东 应用秘钥
        /// </summary>
        public static string JingDongFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JingDongFx:AppSecret") ?? "0e699fc410a8499fa79f8e8c90678cc4";
            }
        }
        #endregion

        #region b站 分销代发应用

        /// <summary>
        /// b站 应用key
        /// </summary>
        public static string BiliBiliFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("BiliBili:FxAppKey") ?? "530d9231490b4cc3";
            }
        }
        /// <summary>
        /// b站 应用秘钥
        /// </summary>
        public static string BiliBiliFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("BiliBili:FxAppSecret") ?? "b52e0f0f3b2441b690bcf7ab79617c5a";
            }
        }

        #endregion

        #region 丰桥 key与秘钥

        /// <summary>
        ///  丰桥key
        /// </summary>
        public static string FengQiaoAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("FengQiao:AppKey") ?? "DGJRJ";
            }
        }
        /// <summary>
        ///  丰桥秘钥
        /// </summary>
        public static string FengQiaoAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("FengQiao:AppSecret") ?? "238ffwiXUrvMCAOIXs4ceyVkSQMetbaw";
            }
        }


        public static string FengQiaoApiUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("FengQiao:ApiUrl") ?? "https://bspsw.sf-express.com/sfexpressService";
            }
        }

        public static string FengQiaoApiUrlInPdd
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("FengQiao:ApiUrl") ?? "http://bspsw.sf-express.com:29943/sfexpressService";
            }
        }

        #endregion

        #region 极兔 应用key与秘钥

        /// <summary>
        ///  极兔 应用key与秘钥
        /// </summary>
        public static string JtExpressKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JtExpress:AppKey") ?? "242127489864826939";
            }
        }

        /// <summary>
        ///  极兔 应用秘钥
        /// </summary>
        public static string JtExpressSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JtExpress:AppSecret") ?? "9b59f51093604b1090e06859e2abe842";
            }
        }

        /// <summary>
        ///  极兔 应用秘钥
        /// </summary>
        public static string JtExpressApiUrl
        {
            get
            {
                if (CloudPlatformType == "Pinduoduo")
                    return System.Configuration.ConfigurationManager.AppSettings.Get("JtExpress:ApiUrl") ?? "http://openapi.jtexpress.com.cn:29943/webopenplatformapi/api/";
                else
                    return System.Configuration.ConfigurationManager.AppSettings.Get("JtExpress:ApiUrl") ?? "https://openapi.jtexpress.com.cn/webopenplatformapi/api/";
            }
        }


        #endregion

        #region 物流中心 key与秘钥

        /// <summary>
        ///  物流中心key
        /// </summary>
        public static string LogisticCenterAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("LogisticCenter:AppKey") ?? "1";
            }
        }
        /// <summary>
        ///  物流中心秘钥
        /// </summary>
        public static string LogisticCenterAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("LogisticCenter:AppSecret") ?? "123abc~!@";
            }
        }

        /// <summary>
        ///  物流中心网关
        /// </summary>
        public static string LogisticCenterGetWay
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("LogisticCenter:ApiUrl") ?? "http://pddlogistics.dgjapp.com";
            }
        }

        #endregion

        #region 萌推 应用key与秘钥

        /// <summary>
        ///  萌推 应用key
        /// </summary>
        public static string MengTuiAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MengTui:AppKey") ?? "vKFAuWb2c8L1GzSC";
            }
        }
        /// <summary>
        ///  萌推 应用秘钥
        /// </summary>
        public static string MengTuiAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MengTui:AppSecret") ?? "RiXna53vqCwaEXtUC0ZZOomxKBQi8P0M";
            }
        }

        #endregion

        #region 云集 应用key与秘钥

        /// <summary>
        ///  云集 应用key
        /// </summary>
        //public static string YunJiAppKey
        //{
        //    get
        //    {
        //        return System.Configuration.ConfigurationManager.AppSettings.Get("YunJi:AppKey") ?? "AK1000001-1001";
        //    }
        //}
        ///// <summary>
        /////  云集 应用秘钥
        ///// </summary>
        //public static string YunJiAppSecret
        //{
        //    get
        //    {
        //        return System.Configuration.ConfigurationManager.AppSettings.Get("YunJi:AppSecret") ?? "7321620095809CE5954C34793D2CF7A3";
        //    }
        //}

        /// <summary>
        ///  云集 ISV创建应用分配的ID
        /// </summary>
        public static string YunJiAppISVID
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("YunJi:ISVID") ?? "ISV1000036";
            }
        }

        #endregion

        #region 快手 应用key与秘钥

        /// <summary>
        ///  快手 应用key
        /// </summary>
        public static string KuaiShouAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:AppKey") ?? "ks680325021400191755";
            }
        }
        /// <summary>
        ///  快手 应用秘钥
        /// </summary>
        public static string KuaiShouAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:AppSecret") ?? "HBbyCdJqvOrNCCSSRremRQ";
            }
        }
        /// <summary>
        ///  快手 API授权秘钥
        /// </summary>
        public static string KuaiShouAppSignSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:SignSecret") ?? "1f48ba9a01b54da1c64531c36feda9bf";
            }
        }
        /// <summary>
        ///  快手厂商 应用key
        /// </summary>
        public static string KuaiShouSupplierAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShouSupplier:AppKey") ?? "ks655836696731488002";
            }
        }
        /// <summary>
        ///  快手厂商 应用秘钥
        /// </summary>
        public static string KuaiShouSupplierAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShouSupplier:AppSecret") ?? "G5r7yF_VyDram5j4qm0_1w";
            }
        }

        /// <summary>
        ///  快手厂商 API授权秘钥
        /// </summary>
        public static string KuaiShouSupplierAppSignSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShouSupplier:SignSecret") ?? "2cb3730ef0a8000d729a110886b23b4c";
            }
        }

        #endregion

        #region 快手 分单应用key与秘钥

        /// <summary>
        ///  快手 分销管理 应用key
        ///  打单发货类目
        /// </summary>
        public static string KuaiShouFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:FxAppKey") ?? "ks680606494871352033";
            }
        }
        /// <summary>
        ///  快手 应用秘钥
        /// </summary>
        public static string KuaiShouFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:FxAppSecret") ?? "yTLJsLCATCnxIWTIZ9LVUw";
            }
        }
        /// <summary>
        ///  快手 API授权秘钥
        /// </summary>
        public static string KuaiShouFxAppSignSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:FxSignSecret") ?? "98b516e47adebb191a988815dfc09988";
            }
        }

        #endregion

        #region 快手 分单应用key与秘钥

        /// <summary>
        ///  快手 智速打单 应用key
        /// </summary>
        public static string KuaiShouFxFastAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:FxFastAppKey") ?? "ks683139772559161362";
            }
        }
        /// <summary>
        ///  快手 应用秘钥
        /// </summary>
        public static string KuaiShouFxFastAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:FxFastAppSecret") ?? "a1q2K3XgoA1VhIs4Hnibmg";
            }
        }
        /// <summary>
        ///  快手 API授权秘钥
        /// </summary>
        public static string KuaiShouFxFastAppSignSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:FxFastSignSecret") ?? "e95b08bd79adaa16b1a2d293a82ecbc0";
            }
        }

        #endregion


        #region 快手 分单应用key与秘钥

        /// <summary>
        ///  快手 分销代发 应用key
        ///  厂商代发类目
        /// </summary>
        public static string KuaiShouNewFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:NewFxAppKey") ?? "ks719731516199748495";
            }
        }
        /// <summary>
        ///  快手 应用秘钥
        /// </summary>
        public static string KuaiShouNewFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:NewFxAppSecret") ?? "QCQ1snjtkpinuZAU7vaSFg";
            }
        }
        /// <summary>
        ///  快手 API授权秘钥
        /// </summary>
        public static string KuaiShouNewFxAppSignSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:NewFxSignSecret") ?? "b1f30d638e6ea9ba9eaeb4ebe9dcaff7";
            }
        }

        #endregion

        #region 贝贝 应用key与秘钥

        /// <summary>
        ///  贝贝 应用key
        /// </summary>
        public static string BeiBeiAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("BeiBei:AppKey") ?? "eojj";
            }
        }
        /// <summary>
        ///  贝贝 应用秘钥
        /// </summary>
        public static string BeiBeiAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("BeiBei:AppSecret") ?? "91bef3f086e90928e9c614ebc6347585";
            }
        }

        #endregion

        #region 小红书 应用key与秘钥

        /// <summary>
        ///  小红书 应用key
        /// </summary>
        public static string XiaoHongShuAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("XiaoHongShu:AppKey") ?? "47724293314f40bd845d";
            }
        }
        /// <summary>
        ///  小红书 应用秘钥
        /// </summary>
        public static string XiaoHongShuAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("XiaoHongShu:AppSecret") ?? "ed3e169529bdc5c08ec4f0fc8ffe8502";
            }
        }

        /// <summary>
        ///  小红书 分单应用key
        /// </summary>
        public static string XiaoHongShuFXAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("XiaoHongShuFX:AppKey") ?? "8fb3aacf06e74aba91ae";
            }
        }
        /// <summary>
        ///  小红书 分单应用秘钥
        /// </summary>
        public static string XiaoHongShuFXAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("XiaoHongShuFX:AppSecret") ?? "35a63a0d6df636241c53266300676f3e";
            }
        }

        /// <summary>
        ///  小红书 api url
        /// </summary>
        public static string XiaoHongShuApiUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("XiaoHongShu:ApiUrl") ?? "https://ark.xiaohongshu.com/ark/open_api/v3/common_controller";
            }
        }

        #endregion

        #region 新申通直连 应用Key与密钥
        public static string NewStoAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("NewSto:AppKey") ?? "CAKsypICMJqWfUP";
            }
        }
        public static string NewStoAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("NewSto:AppSecret") ?? "7qIFB9YyUQXXkkf1hAUVEqndtZMJRhge";
            }
        }
        public static string NewStoAppCode
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("NewSto:AppCode") ?? "CAKsypICMJqWfUP";
            }
        }
        public static string NewStoSource
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("NewSto:Source") ?? "DGJDD";
            }
        }
        #endregion

        #region 圆通轨迹订阅 应用Key与密钥
        public static string YTOAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("YTO:AppKey") ?? "DGJDY";
            }
        }
        public static string YTOAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("YTO:AppSecret") ?? "j2UjT9XA";
            }
        }
        #endregion

        #region 微信开发平台 第三方平台打单应用配置
        #region 旧主体
        /// <summary>
        /// 打单 第三方平台AppId(旧主体)
        /// </summary>
        public static string WxXiaoShangDianAppId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDian:AppId") ?? "wx32ae4064a374b761";
            }
        }

        public static string WxXiaoShangDianAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDian:AppSecret") ?? "4c4619f55181886d5c192773a52c90ff";
            }
        }

        /// <summary>
        /// 消息校验Token
        /// </summary>
        public static string WxXiaoShangDianToken
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDian:Token") ?? "423484234322";
            }
        }
        /// <summary>
        /// 消息加解密Key
        /// </summary>
        public static string WxXiaoShangDianAESKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDian:AESKey") ?? "fsdjl42343242333fsdf5435435435453fds543543g";
            }
        }
        #endregion

        #region 新主体
        /// <summary>
        /// 打单 第三方平台AppId(新主体)
        /// </summary>
        public static string WxXiaoShangDianV2AppId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDianV2:AppId") ?? "wxa9a5c9d83a62cbb0";
            }
        }

        public static string WxXiaoShangDianV2AppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDianV2:AppSecret") ?? "92760086d6531457bdee387220f15d01";
            }
        }
        /// <summary>
        /// 消息校验Token
        /// </summary>
        public static string WxXiaoShangDianV2Token
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDianV2:Token") ?? "44843094484309";
            }
        }
        /// <summary>
        /// 消息加解密Key
        /// </summary>
        public static string WxXiaoShangDianV2AESKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDianV2:AESKey") ?? "BwwmanGMOIJO7Uy96LuQhwuLlvaqKMUD8sNfsKkh6hP";
            }
        }
        #endregion
        #endregion

        #region 微信开放平台 第三方平台分单应用配置
        #region 旧主体
        /// <summary>
        /// 分单 第三方平台AppId(旧主体)
        /// </summary>
        public static string Fx_WxXiaoShangDianAppId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDian:FxAppId") ?? "wx5167acf2ef091d79";
            }
        }

        public static string Fx_WxXiaoShangDianAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDian:FxAppSecret") ?? "4265c036b7a318d7057f77d74df57506";
            }
        }

        /// <summary>
        /// 消息校验Token
        /// </summary>
        public static string Fx_WxXiaoShangDianToken
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDian:FxToken") ?? "423484234322";
            }
        }
        /// <summary>
        /// 消息加解密Key
        /// </summary>
        public static string Fx_WxXiaoShangDianAESKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDian:FxAESKey") ?? "fsdjl42343242333fsdf5435435435453fds543543g";
            }
        }
        #endregion

        #region 新主体
        /// <summary>
        /// 分单 第三方平台AppId(新主体)
        /// </summary>
        public static string Fx_WxComponentNewAppId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxComponentNew:FxAppId") ?? "wxe24cb88137b626d6";
            }
        }

        public static string Fx_WxComponentNewAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxComponentNew:FxAppSecret") ?? "69853e35f0e084fffdf3008c95b018b3";
            }
        }

        /// <summary>
        /// 消息校验Token
        /// </summary>
        public static string Fx_WxComponentNewToken
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxComponentNew:FxToken") ?? "423484234322";
            }
        }
        /// <summary>
        /// 消息加解密Key
        /// </summary>
        public static string Fx_WxComponentNewAESKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxComponentNew:FxAESKey") ?? "fsdjl42343242333fsdf5435435435453fds543543g";
            }
        }
        #endregion

        #region 新应用新主体
        /// <summary>
        /// 分单 第三方平台AppId(新应用新主体)
        /// </summary>
        public static string Fx_WxShopNewAppId => ConfigurationManager.AppSettings.Get("Fx_WxShopNew:FxAppId") ?? "wxac6f97c7c45d687e";

        public static string Fx_WxShopNewAppSecret => ConfigurationManager.AppSettings.Get("Fx_WxShopNew:FxAppSecret") ?? "27ab43a3072d3bc36e58d59deeb07c36";

        /// <summary>
        /// 消息校验Token
        /// </summary>
        public static string Fx_WxShopNewToken => ConfigurationManager.AppSettings.Get("Fx_WxShopNew:FxToken") ?? "423484234322";

        /// <summary>
        /// 消息加解密Key
        /// </summary>
        public static string Fx_WxShopNewAESKey => ConfigurationManager.AppSettings.Get("Fx_WxShopNew:FxAESKey") ?? "fsdjl42343242333fsdf5435435435453fds543543g";

        #endregion
        #endregion

        #region 微信服务平台 应用配置
        #region 旧主体
        /// <summary>
        /// 微信服务市场AppId(旧主体)
        /// </summary>
        public static string WxfuwuAppId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Wxfuwu:AppId") ?? "wxe5ce82006fba46d4";
            }
        }
        /// <summary>
        /// 微信服务市场AppSecret
        /// </summary>
        public static string WxfuwuAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Wxfuwu:AppSecret") ?? "2fa85654e7777a0526906f3a102bd659";
            }
        }

        /// <summary>
        /// 微信服务市场消息校验Token
        /// </summary>
        public static string WxfuwuToken
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Wxfuwu:Token") ?? "423484234322";
            }
        }
        /// <summary>
        /// 微信服务市场消息加解密Key
        /// </summary>
        public static string WxfuwuAESKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Wxfuwu:AESKey") ?? "YEoECA8T7tIboshE8HIR8bQQyjiKXRt97pXYIjQsH5s";
            }
        }
        #endregion

        #region 新主体
        /// <summary>
        /// 微信服务市场AppId(新主体)
        /// </summary>
        public static string WxfuwuNewAppId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxfuwuNew:AppId") ?? "wx966938a1f52b4172";
            }
        }
        /// <summary>
        /// 微信服务市场AppSecret
        /// </summary>
        public static string WxfuwuNewAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxfuwuNew:AppSecret") ?? "10b9ec897de96a8b29a5506db80c05da";
            }
        }

        /// <summary>
        /// 微信服务市场消息校验Token
        /// </summary>
        public static string WxfuwuNewToken
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxfuwuNew:Token") ?? "44843094484309";
            }
        }
        /// <summary>
        /// 微信服务市场消息加解密Key
        /// </summary>
        public static string WxfuwuNewAESKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxfuwuNew:AESKey") ?? "BwwmanGMOIJO7Uy96LuQhwuLlvaqKMUD8sNfsKkh6hP";
            }
        }
        #endregion
        #endregion

        #region 魔筷 应用key与秘钥

        /// <summary>
        ///  魔筷 应用key
        /// </summary>
        public static string MoKuaiAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MoKuai:AppKey") ?? "fe705c624abf4ae4bc41b301322b216b";
            }
        }
        /// <summary>
        ///  魔筷 应用秘钥
        /// </summary>
        public static string MoKuaiAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MoKuai:AppSecret") ?? "80a54d468ccc4637b7167c18864a332c";
            }
        }

        #endregion

        #region 魔筷 分销应用key与秘钥

        /// <summary>
        ///  魔筷 分销应用key
        /// </summary>
        public static string MoKuaiFxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MoKuaiFx:AppKey") ?? "fe705c624abf4ae4bc41b301322b216b";
            }
        }
        /// <summary>
        ///  魔筷 应用秘钥
        /// </summary>
        public static string MoKuaiFxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MoKuaiFx:AppSecret") ?? "80a54d468ccc4637b7167c18864a332c";
            }
        }

        #endregion

        #region 度小店 应用key与秘钥

        /// <summary>
        ///  度小店 应用key
        /// </summary>
        public static string DuXiaoDianAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DuXiaoDian:AppKey") ?? "x4U8VyeVgfygBlSWYTP0LdAE";
            }
        }
        /// <summary>
        ///  度小店 应用秘钥
        /// </summary>
        public static string DuXiaoDianAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DuXiaoDian:AppSecret") ?? "l2wQDn8SEqoZKUuFYCTi8LpN6uGwqyMr";
            }
        }

        /// <summary>
        ///  度小店 新应用key
        /// </summary>
        public static string DuXiaoDianV2AppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DuXiaoDianV2:AppKey") ?? "76a72362b615d3bd10062ae9ddce9c7a";
            }
        }
        /// <summary>
        ///  度小店 新应用秘钥
        /// </summary>
        public static string DuXiaoDianV2AppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DuXiaoDianV2:AppSecret") ?? "cdae3086fd9d3cc35dcbd3728337890d";
            }
        }

        /// <summary>
        ///  度小店 分销应用key
        /// </summary>
        public static string DuXiaoDianV2FxAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DuXiaoDianV2Fx:AppKey") ?? "9ce8167244c7ce15df423d6dd7366a23";
            }
        }
        /// <summary>
        ///  度小店分销应用秘钥
        /// </summary>
        public static string DuXiaoDianV2FxAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DuXiaoDianV2Fx:AppSecret") ?? "ca343c509b842bc777bcedbfe192761f";
            }
        }

        #endregion

        #region 团好货 应用key与秘钥

        /// <summary>
        ///  团好货 应用key
        /// </summary>
        public static string TuanHaoHuoAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TuanHaoHuo:AppKey") ?? "100039";
            }
        }
        /// <summary>
        ///  团好货 应用秘钥
        /// </summary>
        public static string TuanHaoHuoAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TuanHaoHuo:AppSecret") ?? "643bb353d8f7942866367232d821afaa";
            }
        }

        #endregion

        #region 快团团 应用key与秘钥
        /// <summary>
        ///  快团团 应用key
        /// </summary>
        public static string KuaiTuanTuanAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiTuanTuan:AppKey") ?? "fff54440424a4d508f3367c2281f254d";
            }
        }

        /// <summary>
        ///  快团团 应用秘钥
        /// </summary>
        public static string KuaiTuanTuanAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiTuanTuan:AppSecret") ?? "dcbb8291911ed546ed5e09f40f68bc92b26f72f3";
            }
        }
        #endregion

        #region 分单TikTok跨境应用

        public static string TikTokServiceId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TikTok:ServiceId") ?? "7359074999474194182";
            }
        }
        public static string TikTokAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TikTok:AppKey") ?? "6c7rgbqulkbq4";
            }
        }

        /// <summary>
        /// TikTok Api 请求Url（java代理）p
        /// </summary>
        public static string TikTokApiUrl
        {
            get
            {

                return System.Configuration.ConfigurationManager.AppSettings.Get("TikTok:ApiGateWay") ?? "https://tkmessage.dgjapp.com/cross";
            }
        }

        /// <summary>
        /// TikTok铺货调用业务层的加密口令
        /// </summary>
        public static string TikTokEncryptDES
        {
            get
            {

                return System.Configuration.ConfigurationManager.AppSettings.Get("TikTok:EncryptDES") ?? "tkglobal";
            }
        }

        /// <summary>
        /// 是否是跨境站点
        /// Tiktok东南亚站点为true，后续其他的跨境站点都为true
        /// </summary>
        public static bool IsCrossBorderSite
        {
            get
            {
                try
                {
                    return (System.Configuration.ConfigurationManager.AppSettings.Get("IsCrossBorderSite") ?? "0") == "1";
                }
                catch
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 跨境站点所处位置
        /// Tiktok东南亚站点为ChinaAliyun，根据这个决定业务库所处云平台
        /// </summary>
        public static string CrossBorderCloudLocation
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("CrossBorderCloudLocation") ?? "ChinaAliyun";
            }
        }


        /// <summary>
        /// 跨境站点
        /// 打印记录存储的数据库链接
        /// </summary>
        public static string CrossBorderPrintHistoryConnStr
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["CrossBorderPrintHistoryConnStr"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        ///  跨境平台
        /// </summary>
        public static string CrossBorderSyncServiceplatform
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("CrossBorderSyncServiceplatform") ??
                       "";
            }
        }
        #endregion

        #endregion

        #region 老系统地址配置

        /// <summary>
        /// 老系统：淘宝链接
        /// </summary>
        public static string TaobaoOldSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Taobao:OldSystemLink") ?? "http://taobao2.dgjapp.com/";
            }
        }
        /// <summary>
        /// 老系统：1688链接
        /// </summary>
        public static string AlibabaOldSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:OldSystemLink") ?? "http://print1688.dgjapp.com";
            }
        }
        /// <summary>
        /// 老系统：拼多多链接
        /// </summary>
        public static string PinduoduoOldSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Pinduoduo:OldSystemLink") ?? "http://pdd2.dgjapp.com";
            }
        }

        #endregion

        #region 新系统地址配置

        /// <summary>
        /// 新系统：淘宝链接
        /// </summary>
        public static string TaobaoNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Taobao:NewSystemLink") ?? "https://taobao.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：1688链接
        /// </summary>
        public static string AlibabaNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Alibaba:NewSystemLink") ?? "https://1688.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：1688C2M链接
        /// </summary>
        public static string AlibabaC2MNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaC2M:NewSystemLink") ?? "https://c2m.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：拼多多链接
        /// </summary>
        public static string PinduoduoNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Pinduoduo:NewSystemLink") ?? "https://pdd.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：拼多多厂商用户的链接
        /// </summary>
        public static string PinduoduoC2MLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Pinduoduo:C2MLink") ?? "https://pddc2m.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：小店链接
        /// </summary>
        public static string XiaoDianNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("XiaoDian:NewSystemLink") ?? "http://xiaodian.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：蘑菇街链接
        /// </summary>
        public static string MoGuJieNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MoGuJie:NewSystemLink") ?? "http://mogujie.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：美丽说链接
        /// </summary>
        public static string MeiLiShuoNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MeiLiShuo:NewSystemLink") ?? "http://meilishuo.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：有赞链接
        /// </summary>
        public static string YouZanNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("YouZan:NewSystemLink") ?? "http://youzanyun.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：微盟链接
        /// </summary>
        public static string WeiMengNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiMeng:NewSystemLink") ?? "http://weimob.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：微店链接
        /// </summary>
        public static string WeiDianNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiDian:NewSystemLink") ?? "http://weidian.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：考拉链接
        /// </summary>
        public static string KaoLaNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KaoLa:NewSystemLink") ?? "http://kaola.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：唯品会链接
        /// </summary>
        public static string VipShopNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("VipShop:NewSystemLink") ?? "http://vipshop.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：值点链接
        /// </summary>
        public static string ZhiDianNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ZhiDian:NewSystemLink") ?? "http://zhidian.dgjapp.com";
            }
        }


        /// <summary>
        /// 新系统：苏宁链接
        /// </summary>
        public static string SuningNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Suning:NewSystemLink") ?? "http://suning.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：京东链接
        /// </summary>
        public static string JingDongNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JingDong:NewSystemLink") ?? "http://jd.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：萌推链接
        /// </summary>
        public static string MengTuiNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MengTui:NewSystemLink") ?? "http://mengtui.dgjapp.com";
            }
        }
        /// <summary>
        /// 新系统：云集链接
        /// </summary>
        public static string YunJiNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("YunJi:NewSystemLink") ?? "http://yunji.dgjapp.com";
            }
        }
        /// <summary>
        /// 新系统：快手链接
        /// </summary>
        public static string KuaiShouNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiShou:NewSystemLink") ?? "http://kuaishou.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：快手链接
        /// </summary>
        public static string BeiBeiNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("BeiBei:NewSystemLink") ?? "http://beibei.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：开放平台OpenV1链接
        /// </summary>
        public static string OpenV1NewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("OpenV1:NewSystemLink") ?? "http://openv1.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：开放平台小红书链接
        /// </summary>
        public static string XiaoHongShuNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("XiaoHongShu:NewSystemLink") ?? "http://xiaohongshu.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：开放平台微信小商店链接
        /// </summary>
        public static string WxXiaoShangDianNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxXiaoShangDian:NewSystemLink") ?? "http://wx.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：开放平台微信视频号链接
        /// </summary>
        public static string WxVideoDianNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WxVideo:NewSystemLink") ?? "https://wxvideo.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：开放平台魔筷链接
        /// </summary>
        public static string MoKuaiNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MoKuai:NewSystemLink") ?? "http://mokuai.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：开放平台度小店链接
        /// </summary>
        public static string DuXiaoDianNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DuXiaoDian:NewSystemLink") ?? "http://duxiaodian.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：团好货链接
        /// </summary>
        public static string TuanHaoHuoNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TuanHaoHuo:NewSystemLink") ?? "http://tuanhaohuo.dgjapp.com";
            }
        }

        /// <summary>
        /// 新系统：快团团链接
        /// </summary>
        public static string KuaiTuanTuanNewSystemLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("KuaiTuanTuan:NewSystemLink") ?? "https://pddktt.dgjapp.com";
            }
        }
        #endregion

        #region 日志数据库写入配置

        /// <summary>
        /// 操作超时写入日志时间(s)
        /// </summary>
        public static double LogTimeOut
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("LogTimeOut").ToString2().ToDouble();
            }
        }

        /// <summary>
        /// 不需要写入日志数据库的操作
        /// </summary>
        public static List<string> NotNeedLogOperators
        {
            get
            {
                var operators = new List<string>();
                var appStr = System.Configuration.ConfigurationManager.AppSettings.Get("NotNeedLogOperators").ToString2().Replace("，", ",");
                if (appStr.IsNullOrEmpty())
                    return operators;
                operators = appStr.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).ToList();
                return operators;
            }
        }

        #endregion

        #region Redis配置

        /// <summary>
        /// Redis连接
        /// </summary>
        public static string RedisConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["Redis"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// Redis连接2
        /// </summary>
        public static string RedisConnectionString2
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["ConfigRedis"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// Redis连接池最大连接数量
        /// </summary>
        public static int RedisPoolMaxConnectionCount
        {
            get
            {
                var limit = System.Configuration.ConfigurationManager.AppSettings.Get("RedisPoolMaxConnectionCount").ToInt();
                if (limit <= 0)
                    limit = 100;
                return limit;
            }
        }

        #endregion

        #region 移动端入口


        /// <summary>
        /// 微信小程序apiurl
        /// </summary>
        public static string WeiXinApiUrl
        {
            get
            {

                var url = System.Configuration.ConfigurationManager.AppSettings.Get("WeiXinApiUrl");
                if (string.IsNullOrEmpty(url))
                    return "https://weixin1.dgjapp.com";
                else
                    return url;
            }
        }

        /// <summary>
        /// 是否是微信小程序站点
        /// </summary>
        public static bool IsWeiXinXcxSite
        {
            get
            {
                var isWeiXinXcxSite = false;
                try
                {
                    var value = System.Configuration.ConfigurationManager.AppSettings.Get("IsWeiXinXcxSite") ?? "0";
                    if (value == "1")
                        isWeiXinXcxSite = true;
                }
                catch (Exception e)
                {
                    Log.WriteError($"读配置文件报错ConfigurationManager.AppSettings.Get(IsWeiXinXcxSite)：{e.Message}");
                }
                return isWeiXinXcxSite;
            }
        }

        /// <summary>
        /// 是否是抖音移动端的测试
        /// </summary>
        public static bool IsTouTiaoAppSite
        {
            get
            {
                var isTouTiaoAppSite = false;
                try
                {
                    var value = System.Configuration.ConfigurationManager.AppSettings.Get("IsTouTiaoAppSite") ?? "";
                    if (value == "1")
                        isTouTiaoAppSite = true;
                }
                catch (Exception e)
                {
                    Log.WriteError($"读配置文件报错ConfigurationManager.AppSettings.Get(IsWeiXinXcxSite)：{e.Message}");
                }
                return isTouTiaoAppSite;
            }
        }


        #endregion

        #region 微商版

        /// <summary>
        /// Portal入口
        /// </summary>
        public static string PortalLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("PortalLink") ?? "https://portal.dgjapp.com/";
            }
        }

        /// <summary>
        /// 子账号登录入口
        /// </summary>
        public static string SubUserLoginLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("SubUserLoginLink") ?? "https://subaccount.dgjapp.com/";
            }
        }
        /// <summary>
        /// 微商版链接
        /// </summary>
        public static string WeiShangLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeiShangLink") ?? "http://ws.dgjapp.com";
            }
        }

        /// <summary>
        /// portal site 类型
        /// wd 网点
        /// ws 微商
        /// </summary>
        public static string PortalSiteType
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("PortalSiteType") ?? "ws";
            }
        }

        /// <summary>
        /// 网点运营系统连接
        /// </summary>
        public static string WDYYLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WDYYLink") ?? "http://wd.dgjapp.com";
            }
        }

        /// <summary>
        /// 网点运营系统登录
        /// </summary>
        public static string WDYYPortalLink
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WDYYPortalLink") ?? "http://wdportal.dgjapp.com";
            }
        }

        ///// <summary>
        ///// 是否微商版
        ///// </summary>
        //public static bool IsWeiShang
        //{
        //    get
        //    {
        //        return Platform == "Offline";
        //    }
        //}
        #endregion

        #region 支付宝配置


        /// <summary>
        /// 阿里同步订购信息，设置时间
        /// </summary>
        public static string AlipayAppKey
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("AlipayAppKey");
                if (!string.IsNullOrEmpty(value))
                    return value;
                return "2019090566906925";
            }
        }
        /// <summary>
        /// 支付宝应用公钥
        /// </summary>
        public static string AlipayAppPublicSecret
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("AlipayAppPublicSecret");
                if (!string.IsNullOrEmpty(value))
                    return value;
                return "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArQlP3M7EH1xbZtrMv0JKmuQfR/xSYEwTfzpACuNMs6pLDfePtFZff2jt5fLRJqMbj6f6NeBgahcpBqT04TndVKAArkZesTPZvV/iYCCtAb8Lpv2QuWI4691MdgMaeSNMp2hcjiCFPe3QEHvnk1ArGb79e+zLZHHTXbs4xpoWzGEq5l0wRPxkU273AftHDVcMlFcKHfzcT4ojT+jXxRteUGZ580/J99qfAqIoCiLuh4c9/eqWeHSQeR3ffaWmQy8kfEDf4LoiUftUM5JNAe6cevbFRIMSTxJyc2e3Va17XFD9Yy10lQDHlEwjhWdqMErwX5vDv61hXUahC43EP9yDmwIDAQAB";
            }
        }
        /// <summary>
        /// 支付宝公钥
        /// </summary>
        public static string AlipayPublicSecret
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("AlipayPublicSecret");
                if (!string.IsNullOrEmpty(value))
                    return value;
                return "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjGrbJ1o5+dm4TMm1jQSStFKXhrCfQCzCnzq5T/mJp59aTjz+klpR8xCsl03lB8S9QGd+QDThk1FFAM5oPBtnTZme7t+71vqhilGCjI+U28x1Sw8xLuiNwmRy/lqqzZ3nalkwRluXskEuhDNe1SXTOm2B5faBopsxCcBWSXawAA1ccxPx8FBw26QQ91H6xByKI8aKR64Q1jEbv2EXipSwaJCxlvn37T4LtQn3Upv7ng3RicA9Roy3magbbKW2QhMNp/wT+GMGdZa+CfKUfcBaiH+XdSJ2OD8ksRGbOB9zfDdzDshI4x/STIOhdo4ZC1aVEC34KdtNWzyfPWe6wHyTRwIDAQAB";
            }
        }

        /// <summary>
        /// 支付宝app私钥
        /// </summary>
        public static string AlipayAppPrivateSecret
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("AlipayAppPrivateSecret");
                if (!string.IsNullOrEmpty(value))
                    return value;
                return "IqCBwvx2+cbBTNJTTCSJOw==";
            }
        }

        /// <summary>
        /// 支付宝RSA私钥
        /// </summary>
        public static string AlipayRSAPrivateSecret
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("AlipayRSAPrivateSecret");
                if (!string.IsNullOrEmpty(value))
                    return value;
                return "MIIEpAIBAAKCAQEArQlP3M7EH1xbZtrMv0JKmuQfR/xSYEwTfzpACuNMs6pLDfePtFZff2jt5fLRJqMbj6f6NeBgahcpBqT04TndVKAArkZesTPZvV/iYCCtAb8Lpv2QuWI4691MdgMaeSNMp2hcjiCFPe3QEHvnk1ArGb79e+zLZHHTXbs4xpoWzGEq5l0wRPxkU273AftHDVcMlFcKHfzcT4ojT+jXxRteUGZ580/J99qfAqIoCiLuh4c9/eqWeHSQeR3ffaWmQy8kfEDf4LoiUftUM5JNAe6cevbFRIMSTxJyc2e3Va17XFD9Yy10lQDHlEwjhWdqMErwX5vDv61hXUahC43EP9yDmwIDAQABAoIBAGJyxNxlS5JoMpyvagR7TAUaO23yck/VmNYvJxbKwe4Kuo8kxb8d0VGlOo00/KhV7q1qLeKPWWui57WfDrBCAtpN+nUtFl0NmhmzvxFgJe6zaobFB51h8+7GRuVx2JMJcWhh6R74A7b7YgCu7k+TMiKA1qm2TPZGCLLwfVoJe2UmB9zLNAXQBNG0zk6xg6etx+pWIRKE5RJ/yj3X4PMRfsLfFz2ZsfDvkQ0C46xHxaPsUVwhxeQOqbb2o2PmJGA+QT7ng5UNLERshtyoBol3zClZExGA3ED9UZWjUadX/aaxakRUTfdw/je1unEPXUpAV2tCmkUHEY8B0lUcV/A+gGECgYEA2scVieWe3QWC8uuhD0epDV5YzyyWDz0Tgo9YJfmp//IZTyupiG1zhqo3UEygsAq8YWXwyZpWPZ/kxxzYf50gpOvJWtXbhGSsdMlFuXPt2LErNldfgN2drSE9k9hRkx8emi/xcYd7Emda3oh3FR43XL2bcESyXux0RLPtEfiPIRECgYEAynn0Scu5z/x2ojT8/JM6UDKNluBqiCwn2KJli5St4//Gp31rF0OwlBFKHALiHCI/k3bh4xKG8bV1FcBzPIGb/wt8y8oLsdVT5z2nCXuZyEqIFimF31QL69nyjyAAjX2qOlnnn7/mPcUdB7ooZtr2IdLNYRV0ozonmjrXm45WmesCgYEAkRfHqRSoL10gY4rx3fhgnYUc0Ql429JdxhDbHRRfMmeVcXQ7k0dDfuQUmyS2zQnRtaat8oZw+523VHZE/x1uMPJPL/b1BvDdjvaNT1kbPMMDLZ70r7pKgC+zbwHf1/qjiQ6SygBYFvSEx2Ep2M+ZV+BH3kUxJtMaqRXK/EgkZIECgYB57uWBh3F34lbjKjvsMxGb+EWyRaWCKe05FdtHKptpwb+1X98dHIN7N8pEgiv+u1VEDgTMhXzElP1UwxfiqT7zYcyewSc3yfuEN2mfWVLr/o5jok/fhTLErvTW4nPSwYnBKCFH3/3c1xa7b40jG9u71MIgJBagscXlqGftYtOeTwKBgQCqi7tfEfSgWbROQapBuCTGudHdx8gyNE6TCHAV7XQA5q743WH7kpvnOxsKzkWXY12XKxaYrgTXnPj+w9cXTMtjNaRSxmg5+4Qha+dfXsLs2UHlOJQrkwT9HF/1RDJ0kmaWyrS/rNviBQnGXebpnLiYC/QZZovk9duO0LTnLrrW5Q==";
            }
        }
        #endregion

        #region 库存相关配置

        public static string WareHouseDB
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["WareHouseDB"]?.ConnectionString ?? "";
            }
        }
        /// <summary>
        /// 库存API地址
        /// </summary>
        public static string WarehouseApiUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["WarehouseApiUrl"] ?? "https://warehouseapi.dgjapp.com";
            }
        }
        /// <summary>
        /// 库存API地址
        /// </summary>
        public static string WarehouseApiUrlPath
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["WarehouseApiUrlPath"] ?? "/api/warehouse/router";
            }
        }

        /// <summary>
        /// 库存API APPkey
        /// </summary>
        public static string WarehouseAppkey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["WarehouseAppkey"] ?? "23275388";
            }
        }
        /// <summary>
        /// 库存API AppSecret
        /// </summary>
        public static string WarehouseAppsecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["WarehouseAppsecret"] ?? "1031469n4j3482q09ZhU0JP1AYbxu87d";
            }
        }


        public static string WeChatAppID
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["WeChatAppID"] ?? "wx4e023b8d094cff54";
            }
        }
        public static string WeChatAppsecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["WeChatAppsecret"] ?? "8ee9f596059c45df9f5b7da2ee49e0f5";
            }
        }
        public static string WeChatRedirectUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["WeChatRedirectUrl"] ?? "https://tfxportal.dgjapp.com";
            }
        }
        public static string WeChatDefaultFenFaSystemUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("WeChatDefaultFenFaSystemUrl") ?? "https://fxali.dgjapp.com";
            }
        }
        public static string AlibabaQingQrCodeRedirectUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["AlibabaQingQrCodeRedirectUrl"] ?? "https://3tfxportal.dgjapp.com";
            }
        }
        /// <summary>
        /// 最多绑定的微信数，默认1
        /// </summary>
        public static int WeChatQRMaxNum
        {
            get
            {
                var v = System.Configuration.ConfigurationManager.AppSettings.Get("WeChatQRMaxNum").ToInt();
                if (v <= 0)
                    v = 100;
                return v;
            }
        }
        #endregion

        #region 站内消息相关配置

        public static string SiteMessageDB
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["SiteMessageDB"]?.ConnectionString ?? "";
            }
        }
        /// <summary>
        /// 站内消息API地址
        /// </summary>
        public static string SiteMessageApiUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["SiteMessageApiUrl"] ?? "https://sitemessage.dgjapp.com";
            }
        }
        /// <summary>
        /// 站内消息API路径
        /// </summary>
        public static string SiteMessageApiUrlPath
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["SiteMessageApiUrlPath"] ?? "/api/sitemessage/router";
            }
        }

        /// <summary>
        /// 站内消息API APPkey
        /// </summary>
        public static string SiteMessageAppkey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["SiteMessageAppkey"] ?? "23275388";
            }
        }
        /// <summary>
        /// 站内消息API AppSecret
        /// </summary>
        public static string SiteMessageAppsecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["SiteMessageAppsecret"] ?? "1031469n4j3482q09ZhU0JP1AYbxu87d";
            }
        }

        /// <summary>
        /// 是否是内网
        /// </summary>
        public static bool SiteMessageIsIntranet
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings["SiteMessageIsIntranet"];
                return value != null && Convert.ToBoolean(value);
            }
        }

        #endregion

        #region 对账结算相关配置

        public static string FinancialSettlementDB
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["FinancialSettlementDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 任务环境配置
        /// </summary>
        public static string ExportTaskEnvirement
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ExportTask:Envirement") ?? null;
            }
        }

        /// <summary>
        /// 导出+对账，日志是否全部不显示
        /// </summary>
        public static string ExportTaskAppLogIsShow
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("ExportTaskAppLogIsShow") ?? "1";//1是显示，默认显示。
            }
        }

        /// <summary>
        /// 导出任务监听环境配置，默认为relese
        /// </summary>
        public static string ExportTaskListenEnvironment =>
            ConfigurationManager.AppSettings.Get("ExportTask:ListenEnvironment") ?? "relese";

        #endregion

        /// <summary>
        /// 系统队列Url，默认为http://172.26.74.220:8080
        /// </summary>
        public static string SystemQueueUrl
        {
            get
            {
                var pt = System.Configuration.ConfigurationManager.AppSettings.Get("SystemQueueUrl")?.Trim();
                if (string.IsNullOrEmpty(pt))
                {
                    if (CloudPlatformType == "Alibaba")
                        return "http://172.26.74.229:8089";
                    else if (CloudPlatformType == "Pinduoduo")
                        return "http://10.1.33.167:8081";
                    else if (CloudPlatformType == "Jingdong")
                        return "http://127.0.0.1:8080";
                    else if (CloudPlatformType == "TouTiao")
                        return "http://172.16.0.97:8083/";//值待定
                }
                return pt;
            }
        }

        /// <summary>
        ///  是否启用店铺同步队列，默认不启用
        /// </summary>
        public static bool IsEnabledShopSyncQueue
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledShopSyncQueue") ?? "";
                return value == "1" || value.ToLower() == "true";
            }
        }

        /// <summary>
        ///  是否启用店铺黑名单监听，默认不启用
        /// </summary>
        public static bool IsEnabledUnAuthShopListen
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledUnAuthShopListen") ?? "";
                return value == "1" || value.ToLower() == "true";
            }
        }

        /// <summary>
        ///  是否启用分销系统店铺同步队列，默认不启用
        /// </summary>
        public static bool IsEnabledFxShopSyncQueue
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledFxShopSyncQueue") ?? "";
                return value == "1" || value.ToLower() == "true";
            }
        }

        /// <summary>
        ///  是否启用分销系统导出任务队列，默认不启用
        /// </summary>
        public static bool IsEnabledFxExportTaskQueue
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledFxExportTaskQueue") ?? "";
                return value == "1" || value.ToLower() == "true";
            }
        }
        /// <summary>
        ///  是否启用分销系统店铺同步售后队列，默认不启用
        /// </summary>
        public static bool IsEnabledFxAfterSaleSyncQueue
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("IsEnabledFxAfterSaleSyncQueue") ?? "";
                return value == "1" || value.ToLower() == "true";
            }
        }

        /// <summary>
        ///  是否启用分销系统省市区短地址查询，默认启用
        /// </summary>
        public static bool IsPinduoduoUseShortAddress
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("IsPinduoduoUseShortAddress") ?? "";
                return value == "1" || value.ToLower() == "true";
            }
        }


        #region RabbitMQ配置

        /// <summary>
        /// RabbitMQ配置
        /// </summary>
        public static string RabbitMQConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["RabbitMQ"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// RocketMQ服务配置
        /// </summary>
        public static string RocketMQServer
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["RocketMQServer"]?.ConnectionString ?? "";
            }
        }

        #endregion

        /// <summary>
        /// 服务市场链接
        /// </summary>
        public static string AppStoreUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AppStoreUrl") ?? "http://www.dgjapp.com/";
            }
        }

        /// <summary>
        /// 服务市场链接
        /// </summary>
        public static bool IsLogQueryLogisticResponse
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsLogQueryLogisticResponse") == "1";
            }
        }

        /// <summary>
        /// 是否是头条系平台
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static bool IsTouTiaoXi(string platformType)
        {
            platformType = platformType.ToLower();
            bool isZhidianXi =
                  platformType == "toutiao" || platformType == "tt" ||
                  platformType == "zhidian" || platformType == "zd" ||
                  platformType == "douyinxiaodian" || platformType == "dyxd" ||
                  platformType == "toutiaoxiaodian" || platformType == "ttxd" ||
                  platformType == "luban" || platformType == "lb";
            return isZhidianXi;
        }

        /// <summary>
        /// 转换成真实的平台名称（值点、抖音小店、头条小店、鲁班 --> 头条）
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static string ConvertToRealPlatformType(string platformType)
        {
            if (IsTouTiaoXi(platformType))
                return "TouTiao";
            return platformType;
        }

        /// <summary>
        /// 需要从商品库补充更新订单相关信息的平台
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static bool IsNeedUpdateOrderItemFromProduct(string platformType)
        {
            platformType = platformType.ToLower();
            return platformType == "jingdong" || platformType == "jd" ||
                  platformType == "suning" || platformType == "sn" ||
                  platformType == "kuaishou" || platformType == "ks" ||
                  platformType == "yunji" || platformType == "yj";
        }

        /// <summary>
        /// 判断目标平台的数据库在当前平台的站点下是否能访问
        /// </summary>
        /// <param name="currentLoginShopPlatformType">当前登录店铺的平台</param>
        /// <param name="targetShopPlatformType">要访问的店铺平台</param>
        /// <returns></returns>
        public static bool IsDbAvaliableInCurrentSiteOld(string currentLoginShopPlatformType, string targetShopPlatformType, bool isEnabledDuoduoyunDb = false)
        {
            //TODO:判断多多云情况
            if (currentLoginShopPlatformType == targetShopPlatformType)
                return true;
            var dict = new Dictionary<string, List<string>>();
            dict.Add("JingDong", new List<string> { "Jingdong" });
            dict.Add("Aliyun", new List<string> { "Jingdong", "KuaiTuanTuan" });//阿里云上的为不包括
            //当拼多多默认版本为3的时候说明已经迁移至多多云了
            if (PinduoduoDefaultVersion == "3" || isEnabledDuoduoyunDb)
            {
                dict.Add("Pinduoduo", new List<string> { "Pinduoduo", "KuaiTuanTuan" });
                dict["Aliyun"].Add("Pinduoduo");
            }
            var servers = new List<string>();
            foreach (var kv in dict)
            {
                if (kv.Key == "Aliyun")
                {
                    if (kv.Value.Any(v => v == currentLoginShopPlatformType) == false)
                        servers.Add(kv.Key);
                    if (kv.Value.Any(v => v == targetShopPlatformType) == false)
                        servers.Add(kv.Key);
                }
                else
                {
                    if (kv.Value.Contains(currentLoginShopPlatformType))
                        servers.Add(kv.Key);
                    if (kv.Value.Contains(targetShopPlatformType))
                        servers.Add(kv.Key);
                }
            }
            var count = servers.Distinct().Count();
            return count == 1;
        }

        private readonly static List<string> _allCloudPts = new List<string>() { "Alibaba", "Pinduoduo", "Jingdong", "TouTiao" };

        /// <summary>
        /// 判断目标平台的数据库在当前平台的站点下是否能访问
        /// </summary>
        /// <param name="currentLoginShopPlatformType">当前登录店铺的平台</param>
        /// <param name="targetShopPlatformType">要访问的店铺平台</param>
        /// <returns></returns>
        public static bool IsDbAvaliableInCurrentSite(string currentCloudPlatformType, string targetCloudPlatformType)
        {
            //currentCloudPlatformType 当前云平台类型，不允许传入店铺类型
            //传入的平台都在云平台类型中，只需要按云平台类型判断
            //当前云平台类型只有四种，需做验证
            if (_allCloudPts.Contains(currentCloudPlatformType) == false)
                throw new ArgumentException("当前云平台类型有误");
            //目标云平台也在云平台列表内，直接比较
            if (_allCloudPts.Contains(targetCloudPlatformType))
                return currentCloudPlatformType == targetCloudPlatformType;
            //不在云平台列表中，特殊处理
            //快团团在拼多多云，排除已迁入拼多多云、抖店云的，剩下的都在阿里云
            if (targetCloudPlatformType == "KuaiTuanTuan")
                return currentCloudPlatformType == "Pinduoduo";
            else
                return currentCloudPlatformType == "Alibaba";
        }

        /// <summary>
        /// 获取Db Api Url
        /// </summary>
        /// <param name="platformType"></param>
        /// <param name="location"></param>
        /// <returns></returns>
        public static string GetDbApiUrl(string platformType, string location = "")
        {
            var apiUrl = "";
            //apiUrl = "http://localhost:6060/api/route2";
            //return apiUrl;
            if (platformType == "Pinduoduo" || platformType == "KuaiTuanTuan")
            {
                apiUrl = System.Configuration.ConfigurationManager.AppSettings.Get("PinduoduoApiUrlNew") ?? "http://pdda.dgjapp.com/api/route2";
            }
            else if (platformType == "Jingdong")
            {
                apiUrl = System.Configuration.ConfigurationManager.AppSettings.Get("JingdongApiUrl") ?? "http://jdapi.dgjapp.com/api/route2";
            }
            else if (platformType == "TouTiao")
            {
                //拼多多平台不能使用ddapi.dgjapp.com这个域名，需要继续使用config1.dgjapp.com域名做转发,config1已经指向了抖店云的配置库API
                if (CloudPlatformType == "Pinduoduo")
                    apiUrl = System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoApiUrl") ?? "http://config1.dgjapp.com/api/route2";
                else
                    apiUrl = System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoApiUrl") ?? "http://ddapi.dgjapp.com/api/route2";
            }
            else
            {
                //这里不能修改为config.dgjapp.com的站点，默认是获取的业务接口的配置（业务库接口和配置库接口虽然是同一个站点，但是部署的服务器不一样，业务库部署接口部署在249（聚石塔），
                //访问业务库是走的内网。配置库的接口站点部署在两台阿里云的服务器，配置库的访问是走的外网。）
                apiUrl = System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaApiUrl") ?? "http://aliapi.dgjapp.com/api/route2";
            }
            return apiUrl;
        }

        public static string ConfigApiUrl
        {
            get
            {
                //配置库api的站点
                var path = System.Configuration.ConfigurationManager.AppSettings.Get("ConfigApiUrlNew") ?? "http://config.dgjapp.com/api/route2";
                return path;
            }
        }
        /// <summary>
        /// 当前应用所在云平台，目前有四种值：Alibaba、Pinduoduo、Jingdong、TouTiao，分表为阿里云、多多云、京东云、抖店云
        /// </summary>
        public static string CloudPlatformType
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("CloudPlatformType") ?? "Alibaba";
            }
        }

        /// <summary>
        /// 系统版本号（以日期为版本号）
        /// </summary>
        public static string SystemVersion
        {
            get
            {
                var str = System.Configuration.ConfigurationManager.AppSettings.Get("SystemVersion") ?? DateTime.Now.ToString("yyMMdd");
                //随便追加点，和上次不同就行，以保证发布后当天版本号和之前不同。
                str = $"{str}70";
                return str;
            }
        }

        #region JCQ的配置
        public static string JcqAccessKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JcqAccessKey") ?? "JDC_F5FDF7CFC0683FA85E0BEB980E58"; //"正式";
                //return System.Configuration.ConfigurationManager.AppSettings.Get("JcqAccessKey") ?? "JDC_6AD471FC5DE8BB2F7C31285D18FA"; //"测试";
            }
        }

        public static string JcqSecretKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JcqSecretKey") ?? "B7B120C880D34FAF8B76EC80B9E5845D";// "正式"; 
                //return System.Configuration.ConfigurationManager.AppSettings.Get("JcqSecretKey") ?? "9826A77B8F5697C932441D7B38DC0FF9";// "测试"; 
            }
        }
        /// <summary>
        /// Http接入点
        /// </summary>
        public static string JcqHttpEndPoint
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JcqHttpEndPoint") ?? "https://jcq-shared-004.cn-north-1.jdcloud.com";// "正式"; 
                //return System.Configuration.ConfigurationManager.AppSettings.Get("JcqHttpEndPoint") ?? "http://jcq-internet-001-httpsrv-nlb-FI.jvessel-open-hb.jdcloud.com:8080";// "测试"; 
            }
        }
        /// <summary>
        /// 消费者组的id
        /// </summary>
        public static string JcqConsumerGroupId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JcqConsumerGroupId") ?? "open_message_119629651755";// "正式"; 
                //return System.Configuration.ConfigurationManager.AppSettings.Get("JcqConsumerGroupId") ?? "jcqcustomer001";// "测试"; 
            }
        }
        /// <summary>
        /// 商品草稿箱消息
        /// </summary>
        public static string JcqProductDarftInfoTopicName
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JcqProductDarftInfoTopicName") ?? "568091687201$Default$open_message_pop_gms_product_draftInfo_5C3021663C9E49C79351CAF4EB2A4CF1";// "正式"; 
                //return System.Configuration.ConfigurationManager.AppSettings.Get("JcqProductDarftInfoTopicName") ?? "778579441979$JCQTest$MessageTest";// "测试"; 
            }
        }
        #endregion

        /// <summary>
        /// 应用程序类型
        /// </summary>
        public static string ApplicationType
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DianGuanJiaApp:ApplicationType") ??
                       "Site";
            }
        }

        /// <summary>
        ///  店铺缓存最大缓存分钟数，整数，默认30分钟
        /// </summary>
        public static int ShopCacheMaxCacheMinute
        {
            get
            {
                var minutes = System.Configuration.ConfigurationManager.AppSettings.Get("ShopCacheMaxCacheMinute").ToInt();
                if (minutes <= 0)
                    return 30;
                else
                    return minutes;
            }
        }

        /// <summary>
        ///  取单号pid 版本
        /// </summary>
        public static string PidVersion
        {
            get
            {
                var v = System.Configuration.ConfigurationManager.AppSettings.Get("PidVersion") ?? "1";
                return v;
            }
        }
        /// <summary>
        /// 迁移打印记录时间片大小（分钟），默认60
        /// </summary>
        public static int MigratePrintHistoryTimeSizeMinutes
        {
            get
            {
                var timeSize = System.Configuration.ConfigurationManager.AppSettings.Get("MigratePrintHistoryTimeSizeMinutes").ToInt();
                if (timeSize <= 0)
                    timeSize = 60;
                return timeSize;
            }
        }
        /// <summary>
        /// 迁移底单记录时间片大小（分钟），默认12*60
        /// </summary>
        public static int MigrateWaybillCodeTimeSizeMinutes
        {
            get
            {
                var timeSize = System.Configuration.ConfigurationManager.AppSettings.Get("MigrateWaybillCodeTimeSizeMinutes").ToInt();
                if (timeSize <= 0)
                    timeSize = 12 * 60;
                return timeSize;
            }
        }
        /// <summary>
        /// 迁移发货记录时间片大小（分钟），默认12*60
        /// </summary>
        public static int MigrateSendHistoryTimeSizeMinutes
        {
            get
            {
                var timeSize = System.Configuration.ConfigurationManager.AppSettings.Get("MigrateSendHistoryTimeSizeMinutes").ToInt();
                if (timeSize <= 0)
                    timeSize = 12 * 60;
                return timeSize;
            }
        }
        /// <summary>
        /// 迁移逻辑单时间片大小（分钟），默认12*60
        /// </summary>
        public static int MigrateLogicOrderTimeSizeMinutes
        {
            get
            {
                var timeSize = System.Configuration.ConfigurationManager.AppSettings.Get("MigrateLogicOrderTimeSizeMinutes").ToInt();
                if (timeSize <= 0)
                    timeSize = 12 * 60;
                return timeSize;
            }
        }
        /// <summary>
        /// 添加迁移锁后睡眠时间（秒），默认60秒
        /// </summary>
        public static int FxMigrateLockSleepSeconds
        {
            get
            {
                var seconds = System.Configuration.ConfigurationManager.AppSettings.Get("FxMigrateLockSleepSeconds").ToInt();
                if (seconds <= 0)
                    seconds = 60;
                return seconds;
            }
        }

        /// <summary>
        ///  头条版本号配置
        /// </summary>
        public static string TouTiaoVersion
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoVersion") ?? "1";
            }
        }

        /// <summary>
        ///  忽略利润订单同步开启时间 允许执行同步
        /// </summary>
        public static bool IgnoreProfitOrderSyncOpenTime
        {
            get
            {
                if (IsDebug)
                    return true;
                return System.Configuration.ConfigurationManager.AppSettings.Get("IgnoreProfitOrderSyncOpenTime").ToBoolean(false);
            }
        }

        /// <summary>
        /// 超出订单数量使用任务导出
        /// </summary>
        public static int ExportByTaskOrderCountLimit
        {
            get
            {
                var limit = System.Configuration.ConfigurationManager.AppSettings.Get("ExportByTaskOrderCountLimit").ToInt();
                if (limit == 0)
                    limit = 5000;
                return limit;
            }
        }
        /// <summary>
        /// 超出底单数量使用任务导出，默认5000
        /// </summary>
        public static int ExportByTaskWaybillCodeCountLimit
        {
            get
            {
                var limit = System.Configuration.ConfigurationManager.AppSettings.Get("ExportByTaskWaybillCodeCountLimit").ToInt();
                if (limit == 0)
                    limit = 5000;
                return limit;
            }
        }
        /// <summary>
        /// 超出售后数量使用任务导出，默认5000
        /// </summary>
        public static int ExportByTaskAfterSaleCountLimit
        {
            get
            {
                var limit = System.Configuration.ConfigurationManager.AppSettings.Get("ExportByTaskAfterSaleCountLimit").ToInt();
                if (limit == 0)
                    limit = 1000;
                return limit;
            }
        }

        /// <summary>
        /// 超出发货回流数量使用任务导出，默认5000
        /// </summary>
        public static int ExportByTaskSendHistoryReturnRecordCountLimit
        {
            get
            {
                var limit = System.Configuration.ConfigurationManager.AppSettings.Get("ExportByTaskSendHistoryReturnRecordCountLimit").ToInt();
                if (limit == 0)
                    limit = 5000;
                return limit;
            }
        }
        /// <summary>
        /// 超出库存记录数量使用任务导出，默认2000
        /// </summary>
        public static int ExportByTaskStockDetailCountLimit
        {
            get
            {
                var limit = System.Configuration.ConfigurationManager.AppSettings.Get("ExportByTaskStockDetailCountLimit").ToInt();
                if (limit == 0)
                    limit = 2000;
                return limit;
            }
        }
        /// <summary>
        /// 超出发货失败数量使用任务导出，默认2000
        /// </summary>
        public static int ExportByTaskSendFailCountLimit
        {
            get
            {
                var limit = System.Configuration.ConfigurationManager.AppSettings.Get("ExportByTaskSendFailCountLimit").ToInt();
                if (limit == 0)
                    limit = 2000;
                return limit;
            }
        }
        /// <summary>
        /// 超出打记录数量使用任务导出，默认5000
        /// </summary>
        public static int ExportByTaskPrintHistoryCountLimit
        {
            get
            {
                if (IsDebug)
                    return 5;
                var limit = System.Configuration.ConfigurationManager.AppSettings.Get("ExportByTaskPrintHistoryCountLimit").ToInt();
                if (limit == 0)
                    limit = 5000;
                return limit;
            }
        }
        /// <summary>
        /// 修改备注的时候决定是否备份平台备注
        /// 如果之前有备份，重新对接备注修改接口后，只需要显示修改备注这里不备份，
        /// 其他几个点还需要结合备份来处理
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static bool HasModifyRemarkApi_ByUpdateRemark(string platformType)
        {
            if (platformType?.ToLower() == "alibabac2m" || platformType?.ToLower() == "taobaomaicaiv2")
                return false;
            return true;
        }

        public static bool HasModifyRemarkApi(string platformType)
        {
            if (platformType?.ToLower() == "alibabac2m" || platformType?.ToLower() == "taobaomaicaiv2")
                return false;
            return true;
        }

        public static bool NeedUpdateColorSizePlatformTypes(string platformType)
        {
            var needUpdatePts = new List<string> { "taobao", "kuaishou", "youzan", "jingdong", "alibabac2m", "tuanhaohuo", "wxvideo", "xiaohongshu", "suning", "jingdongpurchase","taobaomaicaiv2" };
            if (platformType.IsNotNullOrEmpty() && needUpdatePts.Contains(platformType.ToLower()))
                return true;
            return false;
        }

        #region 上传文件到文件服务器
        public static string UploadFileToFileServer(string localPath)
        {
            var result = UploadFileToFileServerRetry(localPath);
            if (result.IsNullOrEmpty())
            {
                for (var i = 0; i < 3; i++)
                {
                    result = UploadFileToFileServerRetry(localPath);
                    if (!result.IsNullOrEmpty())
                        break;
                }
            }
            if (result.IsNullOrEmpty())
            {
                throw new Exception($"上传失败，返回空");
            }
            return result;
        }

        /// <summary>
        /// 上传文件到文件服务器
        /// </summary>
        /// <param name="localPath"></param>
        /// <returns></returns>
        private static string UploadFileToFileServerRetry(string localPath)
        {
            using (FileStream fs = new FileStream(localPath, FileMode.Open))
            {
                int fsLen = (int)fs.Length;
                byte[] bys = new byte[fsLen];
                fs.Read(bys, 0, fsLen);
                string fileContentBase64 = Convert.ToBase64String(bys);
                var para = new Dictionary<string, string> { { "fileName", Path.GetFileName(localPath) }, { "fileContent", fileContentBase64 }, { "memberId", DateTime.Now.ToString("yyMMddHHmmssfff") + localPath.ToShortMd5() }, { "key", DES.EncryptDES(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "sda98yp3") } };

                //上传到文件服务器
                var rspJson = WebRequestHelper.PostRequest(CustomerConfig.ImageServer + "/Image.ashx?FuncName=Export&key=debug", para, "UTF-8");

                var result = rspJson.ToObject<dynamic>();
                if (result.IsOk == false)
                    return null;
                else
                    return (string)result.Data;
            }
        }
        #endregion

        #region 订单分发相关配置信息

        /// <summary>
        /// 默认的分发系统url，精选平台
        /// </summary>
        public static string DefaultFenFaSystemUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DefaultFenFaSystemUrl") ?? "";
            }
        }
        /// <summary>
        /// 分发系统url，精选平台
        /// </summary>
        public static string AlibabaFenFaSystemUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AlibabaFenFaSystemUrl") ?? "";
            }
        }
        /// <summary>
        /// 拼多多的分发系统url，仅拼多多平台
        /// </summary>
        public static string PinduoduoFenFaSystemUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("PinduoduoFenFaSystemUrl") ?? "";
            }
        }
        /// <summary>
        /// 京东的分发系统url，仅拼多多平台
        /// </summary>
        public static string JingdongFenFaSystemUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("JingdongFenFaSystemUrl") ?? "";
            }
        }
        /// <summary>
        /// 抖店的分发系统url，仅抖店平台
        /// </summary>
        public static string ToutiaoFenFaSystemUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TouTiaoFenFaSystemUrl") ?? "https://fxdd.dgjapp.com";
            }
        }
        /// <summary>
        /// TikTok的分发系统url，仅TikTok平台
        /// </summary>
        public static string TikTokFenFaSystemUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("TikTokFenFaSystemUrl") ?? "https://fxtk.dgjapp.com";
            }
        }

        /// <summary>
        /// 拼多多WebApi IP
        /// </summary>
        public static string PddFxWebApiUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("PddFxWebApiUrl") ?? "http://***********:10013";
            }
        }

        /// <summary>
        /// 绑定店铺 绑定厂家 绑定商家，绑定额度作为可配置的，暂无上限，根据运营需求调整
        /// </summary>
        public static int Fx_Authorizationlimit
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Fx_Authorizationlimit").ToInt();
            }
        }

        /// <summary>
        /// 订单分发2.0登录url
        /// </summary>
        public static string FendanLoginUrl
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("FendanLoginUrl") ?? "";
            }
        }

        /// <summary>
        /// 是否是分单系统的站点
        /// </summary>
        public static bool IsFendanSite
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsFendanSite") == "1";
            }
        }
        /// <summary>
        /// 是否全量同步程序
        /// </summary>
        public static bool IsFullSyncWinform
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsFullSyncWinform") == "1";
            }
        }
        /// <summary>
        /// 是否消息程序
        /// </summary>
        public static bool IsMessageWinform
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IsMessageWinform") == "1";
            }
        }
        /// <summary>
        /// 系统维护时间
        /// </summary>
        public static DateTime? MaintenanceTime
        {
            get
            {
                var time = System.Configuration.ConfigurationManager.AppSettings.Get("MaintenanceTime").ToDateTime();
                return time;
            }
        }

        /// <summary>
        /// 小平台上线时需要修改此处代码
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public static List<FxUserBindPlatformModel> GetFxUserPlatformNew(string token)
        {
            if (string.IsNullOrEmpty(token))
                token = HttpContext.Current.Request.QueryString["token"];
            string authhost = "https://auth.dgjapp.com";  //CustomerConfig.IsDebug ? "http://testauth1.dgjapp.com" : "https://auth.dgjapp.com";
            List<FxUserBindPlatformModel> resutl = new List<FxUserBindPlatformModel>()
            {
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "Alibaba",
                    PlatformName = "阿里巴巴",
                    AuthUrl = "https://pr1688.dgjapp.com?CallUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/entrance?authtype=3") + "&SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess"),
                    Style = "0 0;"
                },new  FxUserBindPlatformModel()
                {
                    PlatformType = "Taobao",
                    PlatformName = "淘宝",
                    AuthUrl = "https://taobao2.dgjapp.com?CallUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/entrance?authtype=3") + "&SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess"),
                    Style = "-110px 0;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "Pinduoduo",
                    PlatformName = "拼多多",
                    //AuthUrl = "https://pdd2.dgjapp.com?CallUrl=" + WebHelper.UrlEncode("http://pdd10.dgjapp.com/auth/entrance?authtype=3") + "&SuccToUrl=" + WebHelper.UrlEncode("http://pdd10.dgjapp.com/auth/authsuccess"),
                    AuthUrl = "http://testauth.dgjapp.com/fxauth/pdd?AuthType=3&SuccToUrl=" + WebHelper.UrlEncode("http://pdd10.dgjapp.com/auth/authsuccess"),
                    Style = "-440px 0;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "TouTiao",
                    PlatformName = "抖音小店",
                    AuthUrl = authhost + "/auth/douyinfxnew?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3" ,
                    Style = "-110px -48px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "KuaiShou",
                    PlatformName = "快手小店",
                    AuthUrl = authhost + "/fxauth/kuaishou?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "0 -48px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "JingDong",
                    PlatformName = "京东",
                    AuthUrl = authhost + "/fxauth/jingdong?state=" + WebHelper.UrlEncode(new { AType= "3", SuccToUrl= "http://auth.dgjapp.com/auth/authsuccess",CallUrl="http://jd3.dgjapp.com"}.ToJson()),
                    Style = "-330px 0;"
                },
                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "WxXiaoShangDian",
                    PlatformName = "微信小商店",
                    AuthUrl = "http://shop.weixin.qq.com/",
                    Style = "-220px -48px;"
                },
                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "WxVideo",
                    PlatformName = "微信视频号",
                    AuthUrl = "https://channels.weixin.qq.com/",
                    Style = "-0px -240px;"
                },
                new FxUserBindPlatformModel(){
                    IsAuthUrl = true,
                    PlatformType = "YouZan",
                    PlatformName = "有赞",
                    AuthUrl = authhost + $"/fxauth/youzan?state="+WebHelper.UrlEncode(new { SuccToUrl="http://auth.dgjapp.com/auth/authsuccess",AuthType="3",Rp=token }.ToJson()),
                    Style = "-330px -48px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "WeiMeng",
                    PlatformName = "微盟",
                    AuthUrl = authhost + "/auth/weimengfx?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-440px -48px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "AlibabaC2M",
                    PlatformName = "淘工厂",
                    AuthUrl = authhost +"/auth/alibabaC2M?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "0  -96px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "DuXiaoDian",
                    PlatformName = "度小店",
                    AuthUrl = authhost +"/auth/duxiaodian?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-110px -96px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "MoGuJie",
                    PlatformName = "蘑菇街",
                    AuthUrl = authhost +"/fxauth/mogujie?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-330px -95px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "MeiLiShuo",
                    PlatformName = "美丽说",
                    AuthUrl = authhost +"/fxauth/meilishuo?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-220px -95px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "MoKuai",
                    PlatformName = "魔筷星选",
                    AuthUrl = authhost +"/fxauth/mokuai?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "0 -144px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "WeiDian",
                    PlatformName = "微店",
                    AuthUrl = authhost +"/fxauth/weidian?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-0px -190px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "SuNing",
                    PlatformName = "苏宁",
                    AuthUrl = authhost +"/fxauth/suning?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-440px -144px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "XiaoHongShu",
                    PlatformName = "小红书",
                    AuthUrl = authhost +"/auth/xiaohongshuv2fx?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-110px -144px;"
                },
                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "TuanHaoHuo",
                    PlatformName = "团好货",
                    AuthUrl = authhost +"/auth/tuanhaohuo?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-330px -144px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "VipShop",
                    PlatformName = "唯品会",
                    AuthUrl = authhost +"/fxauth/vipshop?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-220px -144px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "KuaiTuanTuan",
                    PlatformName = "快团团",
                    AuthUrl = authhost +"/auth/pddktt?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-440px -190px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "OwnShop",
                    PlatformName = "自有商城",
                    AuthUrl = string.Empty,
                    Style = "-220px  -288px;"
                },
                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "OtherPlatforms",
                    PlatformName = "其他平台",
                    AuthUrl =string.Empty,
                    Style = "-330px -288px;"
                },
            };
            return resutl;
        }


        public static List<FxUserBindPlatformModel> GetFxUserPlatform(string token)
        {
            if (string.IsNullOrEmpty(token))
                token = HttpContext.Current.Request.QueryString["token"];

            List<FxUserBindPlatformModel> resutl = new List<FxUserBindPlatformModel>()
            {
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "TouTiao",
                    PlatformName = "抖音小店",
                    AuthUrl = "http://auth.dgjapp.com/auth/douyinfxnew?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-110px -48px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "Pinduoduo",
                    PlatformName = "拼多多",
                    //AuthUrl = "https://pdd2.dgjapp.com?CallUrl=" + WebHelper.UrlEncode("http://pdd10.dgjapp.com/auth/entrance?authtype=3") + "&SuccToUrl=" + WebHelper.UrlEncode("http://pdd10.dgjapp.com/auth/authsuccess"),
                    AuthUrl = "http://testauth.dgjapp.com/fxauth/pdd?AuthType=3&SuccToUrl=" + WebHelper.UrlEncode("http://pdd10.dgjapp.com/auth/authsuccess"),
                    Style = "-440px 0;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "KuaiShou",
                    PlatformName = "快手小店",
                    AuthUrl = "https://auth.dgjapp.com/fxauth/kuaishou?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "0 -48px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "Alibaba",
                    PlatformName = "阿里巴巴",
                    AuthUrl = "https://pr1688.dgjapp.com?CallUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/entrance?authtype=3") + "&SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess"),
                    Style = "0 0;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "Taobao",
                    PlatformName = "淘宝",
                    AuthUrl = "https://taobao2.dgjapp.com?CallUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/entrance?authtype=3") + "&SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess"),
                    Style = "-110px 0;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "AlibabaC2M",
                    PlatformName = "淘工厂",
                    AuthUrl = "http://auth.dgjapp.com/auth/alibabaC2M?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "0  -96px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "Jingdong",
                    PlatformName = "京东",
                    AuthUrl = "http://auth.dgjapp.com/fxauth/jingdong?state=" + WebHelper.UrlEncode(new { AType= "3", SuccToUrl= "http://auth.dgjapp.com/auth/authsuccess",CallUrl="http://jd3.dgjapp.com"}.ToJson()),
                    Style = "-330px 0;"
                },
                new FxUserBindPlatformModel()
                {
                    //京东供销平台走的是京东的授权地址
                    PlatformType = "JingdongPurchase",
                    PlatformName = "京东供销平台",
                    AuthUrl = "http://auth.dgjapp.com/fxauth/jingdong?state=" + WebHelper.UrlEncode(new { AType= "4", SuccToUrl= "http://auth.dgjapp.com/auth/authsuccess",CallUrl="http://jd3.dgjapp.com"}.ToJson()),
                    Style = "-114px -288px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "XiaoHongShu",
                    PlatformName = "小红书",
                    AuthUrl = "http://auth.dgjapp.com/auth/xiaohongshuv2fx?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-110px -144px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "TaoBaoMaiCai",
                    PlatformName = "淘宝买菜",
                    AuthUrl = "http://auth.dgjapp.com/auth/taobaomaicai?state="+ Convert.ToBase64String(Encoding.Default.GetBytes(new {SuccToUrl="http://auth.dgjapp.com/auth/authsuccess",AuthType="3",Rp=token }.ToJson())),
                    Style = "-110px -240px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "TaobaoMaiCaiV2",
                    PlatformName = "淘宝买菜(新)",
                    AuthUrl = "http://auth.dgjapp.com/auth/alibabaC2M?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3&Source=tbmc",
                    Style = "0  -96px;"
                },
                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "WxVideo",
                    PlatformName = "微信小店",
                    AuthUrl = "https://channels.weixin.qq.com/",
                    Style = "-0px -289px;"
                },
                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "WxVideo",
                    PlatformName = "微信视频号",
                    AuthUrl = "https://channels.weixin.qq.com/",
                    Style = "-0px -240px;"
                },

                new  FxUserBindPlatformModel()
                {
                    PlatformType = "KuaiTuanTuan",
                    PlatformName = "快团团",
                    AuthUrl = "http://auth.dgjapp.com/auth/pddktt?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-440px -190px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "DuXiaoDian",
                    PlatformName = "度小店",
                    AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/fxauth/duxiaodian?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-110px -96px;"
                },
                new FxUserBindPlatformModel(){
                    IsAuthUrl = true,
                    PlatformType = "YouZan",
                    PlatformName = "有赞",
                    AuthUrl = $"https://auth.dgjapp.com/auth/youzan?state="+WebHelper.UrlEncode(new { SuccToUrl="http://auth.dgjapp.com/auth/authsuccess",AuthType="3",Rp=token }.ToJson()),
                    Style = "-330px -48px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "WeiMeng",
                    PlatformName = "微盟",
                    AuthUrl = "http://auth.dgjapp.com/auth/weimengfx?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-440px -48px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "MoGuJie",
                    PlatformName = "蘑菇街",
                    AuthUrl = "http://auth.dgjapp.com/auth/mogujie?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-330px -95px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "MeiLiShuo",
                    PlatformName = "美丽说",
                    AuthUrl = "http://auth.dgjapp.com/auth/meilishuo?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-220px -95px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "MoKuai",
                    PlatformName = "魔筷星选",
                    AuthUrl = "http://auth.dgjapp.com/auth/mokuai?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "0 -144px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "WeiDian",
                    PlatformName = "微店",
                    AuthUrl = "http://auth.dgjapp.com/auth/weidian?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-0px -190px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "SuNing",
                    PlatformName = "苏宁",
                    AuthUrl = "http://auth.dgjapp.com/auth/suning?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-440px -144px;"
                },
                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "TuanHaoHuo",
                    PlatformName = "团好货",
                    AuthUrl = "http://auth.dgjapp.com/auth/tuanhaohuo?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-330px -144px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "VipShop",
                    PlatformName = "唯品会",
                    AuthUrl = "http://auth.dgjapp.com/auth/vipshop?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-220px -144px;"
                },
                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "OtherPlatforms",
                    PlatformName = "其他平台",
                    AuthUrl =string.Empty,
                    Style = "-330px -288px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "BiliBili",
                    PlatformName = "B站会员购",
                    AuthUrl = "http://auth.dgjapp.com/fxauth/bilibili?state=" + WebHelper.UrlEncode(new { AType= "3", SuccToUrl= "http://auth.dgjapp.com/auth/authsuccess"}.ToJson()),
                    Style = "-440px -288px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "TouTiaoSaleShop",
                    PlatformName = "即时零售",
                    AuthUrl = "http://auth.dgjapp.com/auth/fxdouyinsaleshop?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-450px -200px;"
                },
            };
            if (RedisHelper.Get<int>("DianGuanJia:FenXiao:OpenPlatform:Enable") != 0)
            {
                var ownShop = new FxUserBindPlatformModel()
                {
                    PlatformType = "OwnShop",
                    PlatformName = "自有商城",
                    AuthUrl = "",
                    Style = "-220px -288px;",
                    IsAuthUrl = false
                };
                // 如果列表中已经有至少一个元素，则将自有商城插入到倒数第二个位置
                int insertIndex = Math.Max(0, resutl.Count - 1);
                resutl.Insert(insertIndex, ownShop);

            }
            return resutl;
        }


        /// <summary>
        /// 20250122 对平台进行了排序
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public static List<FxUserBindPlatformModel> GetFxUserPlatform_Old(string token)
        {
            if (string.IsNullOrEmpty(token))
                token = HttpContext.Current.Request.QueryString["token"];
            List<FxUserBindPlatformModel> resutl = new List<FxUserBindPlatformModel>()
            {
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "TouTiao",
                    PlatformName = "抖音小店",
                    AuthUrl = "http://auth.dgjapp.com/auth/douyinfxnew?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-110px -48px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "Pinduoduo",
                    PlatformName = "拼多多",
                    //AuthUrl = "https://pdd2.dgjapp.com?CallUrl=" + WebHelper.UrlEncode("http://pdd10.dgjapp.com/auth/entrance?authtype=3") + "&SuccToUrl=" + WebHelper.UrlEncode("http://pdd10.dgjapp.com/auth/authsuccess"),
                    AuthUrl = "http://testauth.dgjapp.com/fxauth/pdd?AuthType=3&SuccToUrl=" + WebHelper.UrlEncode("http://pdd10.dgjapp.com/auth/authsuccess"),
                    Style = "-440px 0;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "KuaiShou",
                    PlatformName = "快手小店",
                    AuthUrl = "https://auth.dgjapp.com/fxauth/kuaishou?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "0 -48px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "Alibaba",
                    PlatformName = "阿里巴巴",
                    AuthUrl = "https://pr1688.dgjapp.com?CallUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/entrance?authtype=3") + "&SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess"),
                    Style = "0 0;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "Taobao",
                    PlatformName = "淘宝",
                    AuthUrl = "https://taobao2.dgjapp.com?CallUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/entrance?authtype=3") + "&SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess"),
                    Style = "-110px 0;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "Jingdong",
                    PlatformName = "京东",
                    AuthUrl = "http://auth.dgjapp.com/fxauth/jingdong?state=" + WebHelper.UrlEncode(new { AType= "3", SuccToUrl= "http://auth.dgjapp.com/auth/authsuccess",CallUrl="http://jd3.dgjapp.com"}.ToJson()),
                    Style = "-330px 0;"
                },
                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "WxXiaoShangDian",
                    PlatformName = "微信小商店",
                    AuthUrl = "http://shop.weixin.qq.com/",
                    Style = "-220px -48px;"
                },
                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "WxVideo",
                    PlatformName = "微信视频号",
                    AuthUrl = "https://channels.weixin.qq.com/",
                    Style = "-0px -240px;"
                },

                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "WxVideo",
                    PlatformName = "微信小店",
                    AuthUrl = "https://channels.weixin.qq.com/",
                    Style = "-0px -289px;"
                },

                new FxUserBindPlatformModel(){
                    IsAuthUrl = true,
                    PlatformType = "YouZan",
                    PlatformName = "有赞",
                    AuthUrl = $"https://auth.dgjapp.com/auth/youzan?state="+WebHelper.UrlEncode(new { SuccToUrl="http://auth.dgjapp.com/auth/authsuccess",AuthType="3",Rp=token }.ToJson()),
                    Style = "-330px -48px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "WeiMeng",
                    PlatformName = "微盟",
                    AuthUrl = "http://auth.dgjapp.com/auth/weimengfx?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-440px -48px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "AlibabaC2M",
                    PlatformName = "淘工厂",
                    AuthUrl = "http://auth.dgjapp.com/auth/alibabaC2M?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "0  -96px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "DuXiaoDian",
                    PlatformName = "度小店",
                    AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/fxauth/duxiaodian?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-110px -96px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "MoGuJie",
                    PlatformName = "蘑菇街",
                    AuthUrl = "http://auth.dgjapp.com/auth/mogujie?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-330px -95px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "MeiLiShuo",
                    PlatformName = "美丽说",
                    AuthUrl = "http://auth.dgjapp.com/auth/meilishuo?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-220px -95px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "MoKuai",
                    PlatformName = "魔筷星选",
                    AuthUrl = "http://auth.dgjapp.com/auth/mokuai?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "0 -144px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "WeiDian",
                    PlatformName = "微店",
                    AuthUrl = "http://auth.dgjapp.com/auth/weidian?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-0px -190px;"
                },
                //new FxUserBindPlatformModel()
                //{
                //    PlatformType = "MengTui",
                //    PlatformName = "萌推",
                //    AuthUrl = "http://auth.dgjapp.com/auth/mengtui?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                //    Style = "-440px -95px;"
                //},
                new FxUserBindPlatformModel()
                {
                    PlatformType = "SuNing",
                    PlatformName = "苏宁",
                    AuthUrl = "http://auth.dgjapp.com/auth/suning?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-440px -144px;"
                },
                new FxUserBindPlatformModel()
                {
                    PlatformType = "XiaoHongShu",
                    PlatformName = "小红书",
                    AuthUrl = "http://auth.dgjapp.com/auth/xiaohongshuv2fx?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-110px -144px;"
                },
                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "TuanHaoHuo",
                    PlatformName = "团好货",
                    AuthUrl = "http://auth.dgjapp.com/auth/tuanhaohuo?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-330px -144px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "VipShop",
                    PlatformName = "唯品会",
                    AuthUrl = "http://auth.dgjapp.com/auth/vipshop?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-220px -144px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "KuaiTuanTuan",
                    PlatformName = "快团团",
                    AuthUrl = "http://auth.dgjapp.com/auth/pddktt?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-440px -190px;"
                },
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "TaoBaoMaiCai",
                    PlatformName = "淘宝买菜",
                    AuthUrl = "http://auth.dgjapp.com/auth/taobaomaicai?state="+ Convert.ToBase64String(Encoding.Default.GetBytes(new {SuccToUrl="http://auth.dgjapp.com/auth/authsuccess",AuthType="3",Rp=token }.ToJson())),
                    Style = "-110px -240px;"
                },
                new FxUserBindPlatformModel()
                {
                    //京东供销平台走的是京东的授权地址
                    PlatformType = "JingdongPurchase",
                    PlatformName = "京东供销平台",
                    AuthUrl = "http://auth.dgjapp.com/fxauth/jingdong?state=" + WebHelper.UrlEncode(new { AType= "4", SuccToUrl= "http://auth.dgjapp.com/auth/authsuccess",CallUrl="http://jd3.dgjapp.com"}.ToJson()),
                    Style = "-114px -288px;"
                },
                new  FxUserBindPlatformModel()
                {
                    IsAuthUrl = false,
                    PlatformType = "OtherPlatforms",
                    PlatformName = "其他平台",
                    AuthUrl =string.Empty,
                    Style = "-330px -288px;"
                },
            };
            if (RedisHelper.Get<int>("DianGuanJia:FenXiao:OpenPlatform:Enable") != 0)
            {
                resutl.Add(new FxUserBindPlatformModel()
                {
                    PlatformType = "OwnShop",
                    PlatformName = "自有商城",
                    AuthUrl = "",
                    Style = "-220px  -288px;",
                    IsAuthUrl = false
                });
            }
            return resutl;
        }

        public static List<FxUserBindPlatformModel> GetFxUserCrossBorderPlatform(string token)
        {
            if (string.IsNullOrEmpty(token))
                token = HttpContext.Current.Request.QueryString["token"];
            List<FxUserBindPlatformModel> resutl = new List<FxUserBindPlatformModel>()
            {
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "TikTok",
                    PlatformName = "TikTok",
                    AuthUrl = (CustomerConfig.IsLocalDbDebug? "http://396ec02725.zicp.vip/fxauth/crossborder?SuccToUrl=":"http://auth.dgjapp.com/fxauth/crossborder?SuccToUrl=") + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess"),
                    Style = "-110px -48px;"
                },
            };
            return resutl;
        }



        /// <summary>
        /// 各平台铺货应用对应的授权地址
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public static List<FxUserBindPlatformModel> GetFxUserPlatformForListing(string token)
        {
            if (string.IsNullOrEmpty(token))
                token = HttpContext.Current.Request.QueryString["token"];
            List<FxUserBindPlatformModel> resutl = new List<FxUserBindPlatformModel>()
            {
                new  FxUserBindPlatformModel()
                {
                    PlatformType = "TouTiao",
                    PlatformName = "抖音小店",
                    AuthUrl = "http://auth.dgjapp.com/auth/douyinfxlisting?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    Style = "-110px -48px;"
                },
                //TODO:后续有新的平台支持铺货再逐一追加上来
            };
            return resutl;
        }

        /// <summary>
        /// 生成分单系统SkuCode
        /// </summary>
        /// <param name="productId"></param>
        /// <param name="skuId"></param>
        /// <param name="shopId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public static string GetFxSkuCode(string productId, string skuId, int shopId, int userId)
        {
            var skuCode = (productId + (skuId.IsNullOrEmpty() ? productId : skuId) + shopId + userId).ToShortMd5();
            return skuCode;
        }

        /// <summary>
        /// 是否启动新主体过审，隐藏和店管家相关信息
        /// </summary>
        public static bool IsEnabledNewCorpReview
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get(
                    "DianGuanJiaApp:IsEnabledNewCorpReview");
                return string.IsNullOrWhiteSpace(value) ? false : Convert.ToBoolean(value);
            }
        }

        /// <summary>
        /// 分销系统导航管理
        /// 也可以在站点目录建立一个json文件管理
        /// </summary>
        public static string FenDanSystemNavJson
        {
            get
            {
                //var navJson = "[{\"Active\":false,\"Id\":1,\"Title\":\"工作台\",\"Icon\":\"icon-shouye3\",\"IconSize\":\"15px\",\"Sort\":1,\"Url\":\"/GeneralizeIndex/Index\",\"ClassName\":\"Workbench\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":2,\"Title\":\"授权管理\",\"Icon\":\"icon-yonghu1\",\"IconSize\":\"17px\",\"Sort\":2,\"Url\":\"/Partner/Index\",\"ClassName\":\"Authorization\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":3,\"Title\":\"订单管理\",\"Icon\":\"icon-yingxiaoguanli-dingdanguanli\",\"IconSize\":\"18px\",\"Sort\":4,\"Url\":\"/Common/Page/NewOrder-AllOrder\",\"ClassName\":\"OrderManagement\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":4,\"Title\":\"财务结算\",\"Icon\":\"icon-caiwujiesuan\",\"IconSize\":\"17px\",\"Sort\":5,\"Url\":\"/FinancialSettlement/PriceSetting\",\"ClassName\":\"SettlementAccount\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":5,\"Title\":\"商品管理\",\"Icon\":\"icon-shangpinguanli\",\"IconSize\":\"14px\",\"Sort\":3,\"Url\":\"/Common/Page/Product-Index?type=all\",\"ClassName\":\"ProductManagement\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":6,\"Title\":\"库存管理\",\"Icon\":\"icon-kucunguanli\",\"IconSize\":\"16px\",\"Sort\":6,\"Url\":\"/StockControl/StoreManagement\",\"ClassName\":\"InventoryManagement\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":7,\"Title\":\"系统设置\",\"Icon\":\"icon-xitongguanli3\",\"IconSize\":\"13px\",\"Sort\":7,\"Url\":\"/System/Index\",\"ClassName\":\"SystemSettings\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":8,\"Title\":\"首页\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/GeneralizeIndex/Index\",\"ClassName\":\"GeneralizeIndex\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":8,\"Title\":\"商品介绍\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/GeneralizeIndex/aliDistributionIntroduce\",\"ClassName\":\"AliDistributionIntroduce\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":228,\"Title\":\"1688分销\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/SupplySet1688/DistributionProductSet\",\"ClassName\":\"SupplySet1688\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":9,\"Title\":\"添加店铺\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/Partner/Index\",\"ClassName\":\"Partner\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":10,\"Title\":\"绑定厂家\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/Partner/MySupplier\",\"ClassName\":\"Mysupplier\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":11,\"Title\":\"绑定商家\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/Partner/MyAgent\",\"ClassName\":\"MyAgent\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":12,\"Title\":\"快速邀请\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"\",\"ClassName\":\"inviteDailog\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":34,\"Title\":\"分销设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":5,\"Url\":\"/System/DistributeSet\",\"ClassName\":\"DistributeSet\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":13,\"Title\":\"所有订单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/Common/Page/NewOrder-AllOrder\",\"ClassName\":\"AllOrder\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":14,\"Title\":\"打单发货\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/Common/Page/NewOrder-WaitOrder\",\"ClassName\":\"WaitOrder\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":15,\"Title\":\"线下单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":20,\"Url\":\"/Common/Page/NewOrder-OfflineOrder\",\"ClassName\":\"OfflineOrder\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":16,\"Title\":\"代发售后\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":5,\"Url\":\"/Common/Page/AfterSale-Index\",\"ClassName\":\"AfterSale\",\"ParentId\":4,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":66,\"Title\":\"资金流水\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":6,\"Url\":\"/FundsManagement/TransactionDetailAgent\",\"ClassName\":\"FundsManagement\",\"ParentId\":4,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":17,\"Title\":\"备货单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":22,\"Url\":\"/Common/Page/Purchases-Index\",\"ClassName\":\"Purchases\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":18,\"Title\":\"底单查询\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":6,\"Url\":\"/Common/Page/WaybillCodeList-Index\",\"ClassName\":\"WaybillCodeList\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":19,\"Title\":\"打印记录\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":7,\"Url\":\"/Common/Page/PrintHistory-Index\",\"ClassName\":\"PrintHistory\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":20,\"Title\":\"发货记录\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":8,\"Url\":\"/Common/Page/SendHistory-Index\",\"ClassName\":\"SendHistory\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":21,\"Title\":\"打单设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":23,\"Url\":\"/SetInfo\",\"ClassName\":\"PrintSetting\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":22,\"Title\":\"快递对账\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":10,\"Url\":\"/ExpressBill/Index\",\"ClassName\":\"ExpressBillIndex\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":23,\"Title\":\"对账设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/FinancialSettlement/PriceSetting\",\"ClassName\":\"FinancialSettlement_PriceSetting\",\"ParentId\":4,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":24,\"Title\":\"对账中心\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/FinancialSettlement/BillManagement\",\"ClassName\":\"FinancialSettlement_BillManagement\",\"ParentId\":4,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":25,\"Title\":\"厂家底单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/Common/Page/WaybillCodeList-Index?type=cj\",\"ClassName\":\"WaybillCodeList_cj\",\"ParentId\":4,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":26,\"Title\":\"已发货明细\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/Common/Page/SendOrder-Index\",\"ClassName\":\"SendOrder\",\"ParentId\":4,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":27,\"Title\":\"店铺商品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/Common/Page/Product-Index?type=all\",\"ClassName\":\"Product_all\",\"ParentId\":5,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":29,\"Title\":\"代发商品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/Common/Page/Product-Index?type=other\",\"ClassName\":\"Product_other\",\"ParentId\":5,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":30,\"Title\":\"仓库管理\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/StockControl/StoreManagement\",\"ClassName\":\"StoreManagement\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":31,\"Title\":\"库存货品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/StockControl/WareHouseProduct\",\"ClassName\":\"WareHouseProduct\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":32,\"Title\":\"实时库存\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/StockControl/StockDetail\",\"ClassName\":\"StockControl_StockDetail\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":33,\"Title\":\"库存变更记录\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/StockControl/ChangeDetail\",\"ClassName\":\"StockControl_ChangeDetail\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":35,\"Title\":\"账户管理\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/System/Index\",\"ClassName\":\"SystemSet\",\"ParentId\":7,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":36,\"Title\":\"微信授权\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/System/BindWxUserIndex\",\"ClassName\":\"BindWxUser\",\"ParentId\":7,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 18:31:36\"},{\"Active\":false,\"Id\":-229,\"Title\":\"导出任务\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/ExportTask/Index\",\"ClassName\":\"ExportExcelIndex\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":-230,\"Title\":\"我的小站\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/GeneralizeIndex/MyStationCard\",\"ClassName\":\"MyStationCard\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":-231,\"Title\":\"基础商品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/BaseProduct/BaseProduct\",\"ClassName\":\"ProductBasics\",\"ParentId\":5,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":-232,\"Title\":\"厂家商品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/DistributionProduct/SelectionDistribution\",\"ClassName\":\"SelectionDistribution\",\"ParentId\":5,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":-233,\"Title\":\"铺货日志\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":6,\"Url\":\"/BaseProduct/DistributionLog\",\"ClassName\":\"DistributionLog\",\"ParentId\":5,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"}]";
                //var navJson = "[{\"Active\": false,\"Id\": 1,\"Title\": \"工作台\",\"Icon\": \"icon-shouye3\",\"IconSize\": \"15px\",\"Sort\": 1,\"Url\": \"/GeneralizeIndex/Index\",\"ClassName\": \"Workbench\",\"ParentId\": 0,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:31:54\"},{\"Active\": false,\"Id\": 2,\"Title\": \"授权管理\",\"Icon\": \"icon-yonghu1\",\"IconSize\": \"17px\",\"Sort\": 2,\"Url\": \"/Partner/Index\",\"ClassName\": \"Authorization\",\"ParentId\": 0,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:31:54\"},{\"Active\": false,\"Id\": 3,\"Title\": \"订单管理\",\"Icon\": \"icon-yingxiaoguanli-dingdanguanli\",\"IconSize\": \"18px\",\"Sort\": 3,\"Url\": \"/Common/Page/NewOrder-AllOrder\",\"ClassName\": \"OrderManagement\",\"ParentId\": 0,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:31:54\"},{\"Active\": false,\"Id\": 4,\"Title\": \"财务结算\",\"Icon\": \"icon-caiwujiesuan\",\"IconSize\": \"17px\",\"Sort\": 4,\"Url\": \"/FinancialSettlement/PriceSetting\",\"ClassName\": \"SettlementAccount\",\"ParentId\": 0,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:31:54\"},{\"Active\": false,\"Id\": 5,\"Title\": \"商品管理\",\"Icon\": \"icon-shangpinguanli\",\"IconSize\": \"14px\",\"Sort\": 2,\"Url\": \"/Common/Page/Product-Index?type=all\",\"ClassName\": \"ProductManagement\",\"ParentId\": 0,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:31:54\"},{\"Active\": false,\"Id\": 6,\"Title\": \"库存管理\",\"Icon\": \"icon-kucunguanli\",\"IconSize\": \"16px\",\"Sort\": 6,\"Url\": \"/StockControl/StoreManagement\",\"ClassName\": \"InventoryManagement\",\"ParentId\": 0,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:31:54\"},{\"Active\": false,\"Id\": 7,\"Title\": \"系统设置\",\"Icon\": \"icon-xitongguanli3\",\"IconSize\": \"13px\",\"Sort\": 7,\"Url\": \"/System/Index\",\"ClassName\": \"SystemSettings\",\"ParentId\": 0,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:31:54\"},{\"Active\": false,\"Id\": 8,\"Title\": \"首页\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 1,\"Url\": \"/GeneralizeIndex/Index\",\"ClassName\": \"GeneralizeIndex\",\"ParentId\": 1,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 8,\"Title\": \"商品介绍\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 1,\"Url\": \"/GeneralizeIndex/aliDistributionIntroduce\",\"ClassName\": \"AliDistributionIntroduce\",\"ParentId\": 1,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 228,\"Title\": \"1688分销\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 2,\"Url\": \"/SupplySet1688/DistributionProductSet\",\"ClassName\": \"SupplySet1688\",\"ParentId\": 1,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 9,\"Title\": \"添加店铺\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 1,\"Url\": \"/Partner/Index\",\"ClassName\": \"Partner\",\"ParentId\": 2,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 10,\"Title\": \"绑定厂家\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 2,\"Url\": \"/Partner/MySupplier\",\"ClassName\": \"Mysupplier\",\"ParentId\": 2,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 11,\"Title\": \"绑定商家\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 3,\"Url\": \"/Partner/MyAgent\",\"ClassName\": \"MyAgent\",\"ParentId\": 2,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 12,\"Title\": \"快速邀请\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 4,\"Url\": \"\",\"ClassName\": \"inviteDailog\",\"ParentId\": 2,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 34,\"Title\": \"分销设置\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 5,\"Url\": \"/System/DistributeSet\",\"ClassName\": \"DistributeSet\",\"ParentId\": 2,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 13,\"Title\": \"所有订单\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 1,\"Url\": \"/Common/Page/NewOrder-AllOrder\",\"ClassName\": \"AllOrder\",\"ParentId\": 3,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 14,\"Title\": \"打单发货\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 2,\"Url\": \"/Common/Page/NewOrder-WaitOrder\",\"ClassName\": \"WaitOrder\",\"ParentId\": 3,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 15,\"Title\": \"线下单\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 20,\"Url\": \"/Common/Page/NewOrder-OfflineOrder\",\"ClassName\": \"OfflineOrder\",\"ParentId\": 3,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 16,\"Title\": \"代发售后\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 5,\"Url\": \"/Common/Page/AfterSale-Index\",\"ClassName\": \"AfterSale\",\"ParentId\": 4,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 66,\"Title\": \"资金流水\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 6,\"Url\": \"/FundsManagement/TransactionDetailAgent\",\"ClassName\": \"FundsManagement\",\"ParentId\": 4,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 17,\"Title\": \"备货单\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 22,\"Url\": \"/Common/Page/Purchases-Index\",\"ClassName\": \"Purchases\",\"ParentId\": 3,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 18,\"Title\": \"底单查询\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 6,\"Url\": \"/Common/Page/WaybillCodeList-Index\",\"ClassName\": \"WaybillCodeList\",\"ParentId\": 3,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 19,\"Title\": \"打印记录\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 7,\"Url\": \"/Common/Page/PrintHistory-Index\",\"ClassName\": \"PrintHistory\",\"ParentId\": 3,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 20,\"Title\": \"发货记录\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 8,\"Url\": \"/Common/Page/SendHistory-Index\",\"ClassName\": \"SendHistory\",\"ParentId\": 3,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 21,\"Title\": \"打单设置\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 23,\"Url\": \"/SetInfo\",\"ClassName\": \"PrintSetting\",\"ParentId\": 3,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 22,\"Title\": \"快递对账\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 10,\"Url\": \"/ExpressBill/Index\",\"ClassName\": \"ExpressBillIndex\",\"ParentId\": 3,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 23,\"Title\": \"对账设置\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 1,\"Url\": \"/FinancialSettlement/PriceSetting\",\"ClassName\": \"FinancialSettlement_PriceSetting\",\"ParentId\": 4,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 24,\"Title\": \"对账中心\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 2,\"Url\": \"/FinancialSettlement/BillManagement\",\"ClassName\": \"FinancialSettlement_BillManagement\",\"ParentId\": 4,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 25,\"Title\": \"厂家底单\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 3,\"Url\": \"/Common/Page/WaybillCodeList-Index?type=cj\",\"ClassName\": \"WaybillCodeList_cj\",\"ParentId\": 4,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 26,\"Title\": \"已发货明细\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 4,\"Url\": \"/Common/Page/SendOrder-Index\",\"ClassName\": \"SendOrder\",\"ParentId\": 4,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 27,\"Title\": \"店铺商品\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 1,\"Url\": \"/Common/Page/Product-Index?type=all\",\"ClassName\": \"Product_all\",\"ParentId\": 5,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 29,\"Title\": \"代发商品\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 3,\"Url\": \"/Common/Page/Product-Index?type=other\",\"ClassName\": \"Product_other\",\"ParentId\": 5,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 30,\"Title\": \"仓库管理\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 1,\"Url\": \"/StockControl/StoreManagement\",\"ClassName\": \"StoreManagement\",\"ParentId\": 6,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 31,\"Title\": \"库存货品\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 2,\"Url\": \"/BaseProduct/NewBaseProduct?operateType=WareHouseProduct\",\"ClassName\": \"WareHouseProduct\",\"ParentId\": 6,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 32,\"Title\": \"实时库存\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 3,\"Url\": \"/StockControl/StockDetail\",\"ClassName\": \"StockControl_StockDetail\",\"ParentId\": 6,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 33,\"Title\": \"库存变更记录\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 4,\"Url\": \"/StockControl/ChangeDetail\",\"ClassName\": \"StockControl_ChangeDetail\",\"ParentId\": 6,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 35,\"Title\": \"账户管理\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 2,\"Url\": \"/System/Index\",\"ClassName\": \"SystemSet\",\"ParentId\": 7,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 36,\"Title\": \"微信授权\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 3,\"Url\": \"/System/BindWxUserIndex\",\"ClassName\": \"BindWxUser\",\"ParentId\": 7,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 18:31:36\"},{\"Active\": false,\"Id\": -229,\"Title\": \"导出任务\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 3,\"Url\": \"/ExportTask/Index\",\"ClassName\": \"ExportExcelIndex\",\"ParentId\": 1,\"IsDelete\": false,\"CreateTime\": \"2022-06-16 14:32:20\"},{\"Active\": false,\"Id\": 4,\"Title\": \"售后管理\",\"Icon\": \"icon-tuoguan_dark\",\"IconSize\": \"18px\",\"Sort\": 4,\"Url\": \"/Common/Page/AfterSale-ManualAfterSale\",\"ClassName\": \"ManualAfterSale\",\"ParentId\": 0,\"IsDelete\": false,\"CreateTime\": \"2024-04-02 10:31:54\"},{\"Active\": false,\"Id\": 41,\"Title\": \"手工售后单\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 2,\"Url\": \"/Common/Page/AfterSale-ManualAfterSale\",\"ClassName\": \"ManualAfterSale\",\"ParentId\": 4,\"IsDelete\": false,\"CreateTime\": \"2024-04-02 10:31:54\"},{\"Active\": false,\"Id\": 42,\"Title\": \"平台售后单\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 3,\"Url\": \"/Common/Page/AfterSale-Index\",\"ClassName\": \"AfterSale\",\"ParentId\": 4,\"IsDelete\": false,\"CreateTime\": \"2024-04-02 10:31:54\"},{\"Active\": false,\"Id\": -230,\"Title\": \"我的小站\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 2,\"Url\": \"/GeneralizeIndex/MyStationCard\",\"ClassName\": \"MyStationCard\",\"ParentId\": 1,\"IsDelete\": false,\"CreateTime\": \"2024-07-24\"},{\"Active\": false,\"Id\": -231,\"Title\": \"基础商品\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 4,\"Url\": \"/BaseProduct/NewBaseProduct\",\"ClassName\": \"ProductBasics\",\"ParentId\": 5,\"IsDelete\": false,\"CreateTime\": \"2024-07-24\"},{\"Active\": false,\"Id\": -232,\"Title\": \"厂家商品\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 4,\"Url\": \"/DistributionProduct/SelectionDistribution\",\"ClassName\": \"SelectionDistribution\",\"ParentId\": 5,\"IsDelete\": false,\"CreateTime\": \"2024-07-24\"},{\"Active\": false,\"Id\": -233,\"Title\": \"铺货日志\",\"Icon\": \"\",\"IconSize\": \"\",\"Sort\": 6,\"Url\": \"/BaseProduct/DistributionLog\",\"ClassName\": \"DistributionLog\",\"ParentId\": 5,\"IsDelete\": false,\"CreateTime\": \"2024-07-24\"}]";
                //var navJson = "[{\"Active\":false,\"Id\":1,\"Title\":\"工作台\",\"Icon\":\"icon-shouye3\",\"IconSize\":\"15px\",\"Sort\":1,\"Url\":\"/GeneralizeIndex/Index\",\"ClassName\":\"Workbench\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":2,\"Title\":\"授权管理\",\"Icon\":\"icon-yonghu1\",\"IconSize\":\"17px\",\"Sort\":2,\"Url\":\"/Partner/Index\",\"ClassName\":\"Authorization\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":10,\"Title\":\"订单管理\",\"Icon\":\"icon-yingxiaoguanli-dingdanguanli\",\"IconSize\":\"18px\",\"Sort\":10,\"Url\":\"/Common/Page/NewOrder-AllOrder\",\"ClassName\":\"OrderManagement\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":14,\"Title\":\"财务结算\",\"Icon\":\"icon-caiwujiesuan\",\"IconSize\":\"17px\",\"Sort\":14,\"Url\":\"/FinancialSettlement/PriceSetting\",\"ClassName\":\"SettlementAccount\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":6,\"Title\":\"商品管理\",\"Icon\":\"icon-shangpinguanli\",\"IconSize\":\"14px\",\"Sort\":6,\"Url\":\"/Common/Page/Product-Index?type=all\",\"ClassName\":\"ProductManagement\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":16,\"Title\":\"库存管理\",\"Icon\":\"icon-kucunguanli\",\"IconSize\":\"16px\",\"Sort\":16,\"Url\":\"/StockControl/StoreManagement\",\"ClassName\":\"InventoryManagement\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":18,\"Title\":\"系统设置\",\"Icon\":\"icon-xitongguanli3\",\"IconSize\":\"13px\",\"Sort\":18,\"Url\":\"/System/Index\",\"ClassName\":\"SystemSettings\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":88,\"Title\":\"首页\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/GeneralizeIndex/Index\",\"ClassName\":\"GeneralizeIndex\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":228,\"Title\":\"1688分销\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/SupplySet1688/DistributionProductSet\",\"ClassName\":\"SupplySet1688\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":98,\"Title\":\"添加店铺\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/Partner/Index\",\"ClassName\":\"Partner\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":180,\"Title\":\"绑定厂家\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/Partner/MySupplier\",\"ClassName\":\"Mysupplier\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":181,\"Title\":\"绑定商家\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/Partner/MyAgent\",\"ClassName\":\"MyAgent\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":182,\"Title\":\"快速邀请\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"\",\"ClassName\":\"inviteDailog\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":34,\"Title\":\"分销设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":5,\"Url\":\"/System/DistributeSet\",\"ClassName\":\"DistributeSet\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":183,\"Title\":\"所有订单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/Common/Page/NewOrder-AllOrder\",\"ClassName\":\"AllOrder\",\"ParentId\":10,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":184,\"Title\":\"打单发货\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/Common/Page/NewOrder-WaitOrder\",\"ClassName\":\"WaitOrder\",\"ParentId\":10,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":185,\"Title\":\"线下单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":20,\"Url\":\"/Common/Page/NewOrder-OfflineOrder\",\"ClassName\":\"OfflineOrder\",\"ParentId\":10,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":66,\"Title\":\"运费设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":6,\"Url\":\"/FreightTemplate/NewShippingFeeSet\",\"ClassName\":\"NewShippingFeeSet\",\"ParentId\":14,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":178,\"Title\":\"备货单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":22,\"Url\":\"/Common/Page/Purchases-Index\",\"ClassName\":\"Purchases\",\"ParentId\":10,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":188,\"Title\":\"底单查询\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":6,\"Url\":\"/Common/Page/WaybillCodeList-Index\",\"ClassName\":\"WaybillCodeList\",\"ParentId\":10,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":19,\"Title\":\"打印记录\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":7,\"Url\":\"/Common/Page/PrintHistory-Index\",\"ClassName\":\"PrintHistory\",\"ParentId\":10,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":20,\"Title\":\"发货记录\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":8,\"Url\":\"/Common/Page/SendHistory-Index\",\"ClassName\":\"SendHistory\",\"ParentId\":10,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":21,\"Title\":\"打单设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":23,\"Url\":\"/SetInfo\",\"ClassName\":\"PrintSetting\",\"ParentId\":10,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":22,\"Title\":\"快递对账\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":10,\"Url\":\"/ExpressBill/Index\",\"ClassName\":\"ExpressBillIndex\",\"ParentId\":10,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":23,\"Title\":\"对账设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/FinancialSettlement/PriceSetting\",\"ClassName\":\"FinancialSettlement_PriceSetting\",\"ParentId\":14,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":24,\"Title\":\"对账中心\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/FinancialSettlement/BillManagement\",\"ClassName\":\"FinancialSettlement_BillManagement\",\"ParentId\":14,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":25,\"Title\":\"厂家底单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/Common/Page/WaybillCodeList-Index?type=cj\",\"ClassName\":\"WaybillCodeList_cj\",\"ParentId\":14,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":26,\"Title\":\"已发货明细\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/Common/Page/SendOrder-Index\",\"ClassName\":\"SendOrder\",\"ParentId\":14,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":27,\"Title\":\"店铺商品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/Common/Page/Product-Index?type=all\",\"ClassName\":\"Product_all\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":29,\"Title\":\"代发商品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/Common/Page/Product-Index?type=other\",\"ClassName\":\"Product_other\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":30,\"Title\":\"仓库管理\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/StockControl/StoreManagement\",\"ClassName\":\"StoreManagement\",\"ParentId\":16,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":31,\"Title\":\"库存货品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/BaseProduct/NewBaseProduct?operateType=WareHouseProduct\",\"ClassName\":\"WareHouseProduct\",\"ParentId\":16,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":32,\"Title\":\"实时库存\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/StockControl/StockDetail\",\"ClassName\":\"StockControl_StockDetail\",\"ParentId\":16,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":33,\"Title\":\"库存变更记录\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/StockControl/ChangeDetail\",\"ClassName\":\"StockControl_ChangeDetail\",\"ParentId\":16,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":35,\"Title\":\"账户管理\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/System/Index\",\"ClassName\":\"SystemSet\",\"ParentId\":18,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":36,\"Title\":\"微信授权\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/System/BindWxUserIndex\",\"ClassName\":\"BindWxUser\",\"ParentId\":18,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 18:31:36\"},{\"Active\":false,\"Id\":-229,\"Title\":\"导出任务\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/ExportTask/Index\",\"ClassName\":\"ExportExcelIndex\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":12,\"Title\":\"售后管理\",\"Icon\":\"icon-tuoguan_dark\",\"IconSize\":\"18px\",\"Sort\":12,\"Url\":\"/Common/Page/AfterSale-ManualAfterSale\",\"ClassName\":\"ManualAfterSale\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2024-04-02 10:31:54\"},{\"Active\":false,\"Id\":41,\"Title\":\"手工售后单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/Common/Page/AfterSale-ManualAfterSale\",\"ClassName\":\"ManualAfterSale\",\"ParentId\":12,\"IsDelete\":false,\"CreateTime\":\"2024-04-02 10:31:54\"},{\"Active\":false,\"Id\":42,\"Title\":\"平台售后单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/Common/Page/AfterSale-Index\",\"ClassName\":\"AfterSale\",\"ParentId\":12,\"IsDelete\":false,\"CreateTime\":\"2024-04-02 10:31:54\"},{\"Active\":false,\"Id\":-230,\"Title\":\"我的小站\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/GeneralizeIndex/MyStationCard\",\"ClassName\":\"MyStationCard\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":-231,\"Title\":\"基础商品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/BaseProduct/NewBaseProduct\",\"ClassName\":\"ProductBasics\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":-232,\"Title\":\"厂家商品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/DistributionProduct/SelectionDistribution\",\"ClassName\":\"SelectionDistribution\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":-233,\"Title\":\"铺货日志\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":6,\"Url\":\"/BaseProduct/DistributionLog\",\"ClassName\":\"DistributionLog\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"}]";
                var navJson = "[{\"Active\":false,\"Id\":1,\"Title\":\"工作台\",\"Icon\":\"icon-shouye3\",\"IconSize\":\"15px\",\"Sort\":1,\"Url\":\"/GeneralizeIndex/Index\",\"ClassName\":\"Workbench\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":2,\"Title\":\"授权管理\",\"Icon\":\"icon-yonghu1\",\"IconSize\":\"17px\",\"Sort\":2,\"Url\":\"/Partner/Index\",\"ClassName\":\"Authorization\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":3,\"Title\":\"订单管理\",\"Icon\":\"icon-yingxiaoguanli-dingdanguanli\",\"IconSize\":\"18px\",\"Sort\":8,\"Url\":\"/Common/Page/NewOrder-AllOrder\",\"ClassName\":\"OrderManagement\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":4,\"Title\":\"财务结算\",\"Icon\":\"icon-caiwujiesuan\",\"IconSize\":\"17px\",\"Sort\":12,\"Url\":\"/FinancialSettlement/PriceSetting\",\"ClassName\":\"SettlementAccount\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":5,\"Title\":\"商品管理\",\"Icon\":\"icon-shangpinguanli\",\"IconSize\":\"14px\",\"Sort\":3,\"Url\":\"/Common/Page/Product-Index?type=all\",\"ClassName\":\"ProductManagement\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":-1,\"Title\":\"1688担保交易下单\",\"Icon\":\"icon-yingxiaoguanli-dingdanguanli\",\"IconSize\":\"13px\",\"Sort\":4,\"Url\":\"/Common/Page/NewOrder-AliIncludePayOrder\",\"ClassName\":\"SupplySet1688_1\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":-2,\"Title\":\"1688收单设置\",\"Icon\":\"icon-yingxiaoguanli-dingdanguanli\",\"IconSize\":\"13px\",\"Sort\":5,\"Url\":\"/NewOrder/AliIncludeOrder\",\"ClassName\":\"SupplySet1688\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":6,\"Title\":\"库存管理\",\"Icon\":\"icon-kucunguanli\",\"IconSize\":\"16px\",\"Sort\":14,\"Url\":\"/StockControl/StoreManagement\",\"ClassName\":\"InventoryManagement\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":7,\"Title\":\"系统设置\",\"Icon\":\"icon-xitongguanli3\",\"IconSize\":\"13px\",\"Sort\":16,\"Url\":\"/System/Index\",\"ClassName\":\"SystemSettings\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:31:54\"},{\"Active\":false,\"Id\":8,\"Title\":\"首页\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/GeneralizeIndex/Index\",\"ClassName\":\"GeneralizeIndex\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":228,\"Title\":\"1688分销\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/SupplySet1688/DistributionProductSet\",\"ClassName\":\"SupplySet1688\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":9,\"Title\":\"添加店铺\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/Partner/Index\",\"ClassName\":\"Partner\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":10,\"Title\":\"绑定厂家\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/Partner/MySupplier\",\"ClassName\":\"Mysupplier\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":11,\"Title\":\"绑定商家\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/Partner/MyAgent\",\"ClassName\":\"MyAgent\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":12,\"Title\":\"快速邀请\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"\",\"ClassName\":\"inviteDailog\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":34,\"Title\":\"分销设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":5,\"Url\":\"/System/DistributeSet\",\"ClassName\":\"DistributeSet\",\"ParentId\":2,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":13,\"Title\":\"所有订单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/Common/Page/NewOrder-AllOrder\",\"ClassName\":\"AllOrder\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":14,\"Title\":\"打单发货\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/Common/Page/NewOrder-WaitOrder\",\"ClassName\":\"WaitOrder\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":15,\"Title\":\"线下单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":20,\"Url\":\"/Common/Page/NewOrder-OfflineOrder\",\"ClassName\":\"OfflineOrder\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":66,\"Title\":\"运费设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":6,\"Url\":\"/FreightTemplate/NewShippingFeeSet\",\"ClassName\":\"NewShippingFeeSet\",\"ParentId\":4,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":17,\"Title\":\"备货单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":22,\"Url\":\"/Common/Page/Purchases-Index\",\"ClassName\":\"Purchases\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":18,\"Title\":\"底单查询\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":6,\"Url\":\"/Common/Page/WaybillCodeList-Index\",\"ClassName\":\"WaybillCodeList\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":19,\"Title\":\"打印记录\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":7,\"Url\":\"/Common/Page/PrintHistory-Index\",\"ClassName\":\"PrintHistory\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":20,\"Title\":\"发货记录\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":8,\"Url\":\"/Common/Page/SendHistory-Index\",\"ClassName\":\"SendHistory\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":21,\"Title\":\"打单设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":23,\"Url\":\"/SetInfo\",\"ClassName\":\"PrintSetting\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":22,\"Title\":\"快递对账\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":10,\"Url\":\"/ExpressBill/Index\",\"ClassName\":\"ExpressBillIndex\",\"ParentId\":3,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":23,\"Title\":\"对账设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/FinancialSettlement/PriceSetting\",\"ClassName\":\"FinancialSettlement_PriceSetting\",\"ParentId\":4,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":24,\"Title\":\"对账中心\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/FinancialSettlement/BillManagement\",\"ClassName\":\"FinancialSettlement_BillManagement\",\"ParentId\":4,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":25,\"Title\":\"厂家底单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/Common/Page/WaybillCodeList-Index?type=cj\",\"ClassName\":\"WaybillCodeList_cj\",\"ParentId\":4,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":26,\"Title\":\"已发货明细\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/Common/Page/SendOrder-Index\",\"ClassName\":\"SendOrder\",\"ParentId\":4,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":27,\"Title\":\"店铺商品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/Common/Page/Product-Index?type=all\",\"ClassName\":\"Product_all\",\"ParentId\":5,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":29,\"Title\":\"代发商品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/Common/Page/Product-Index?type=other\",\"ClassName\":\"Product_other\",\"ParentId\":5,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":30,\"Title\":\"仓库管理\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/StockControl/StoreManagement\",\"ClassName\":\"StoreManagement\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":31,\"Title\":\"库存货品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/BaseProduct/NewBaseProduct?operateType=WareHouseProduct\",\"ClassName\":\"WareHouseProduct\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":32,\"Title\":\"实时库存\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/StockControl/StockDetail\",\"ClassName\":\"StockControl_StockDetail\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":33,\"Title\":\"库存变更记录\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/StockControl/ChangeDetail\",\"ClassName\":\"StockControl_ChangeDetail\",\"ParentId\":6,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":35,\"Title\":\"账户管理\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/System/Index\",\"ClassName\":\"SystemSet\",\"ParentId\":7,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":36,\"Title\":\"微信授权\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/System/BindWxUserIndex\",\"ClassName\":\"BindWxUser\",\"ParentId\":7,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 18:31:36\"},{\"Active\":false,\"Id\":-229,\"Title\":\"导出任务\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/ExportTask/Index\",\"ClassName\":\"ExportExcelIndex\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2022-06-16 14:32:20\"},{\"Active\":false,\"Id\":166,\"Title\":\"售后管理\",\"Icon\":\"icon-tuoguan_dark\",\"IconSize\":\"18px\",\"Sort\":10,\"Url\":\"/Common/Page/AfterSale-ManualAfterSale\",\"ClassName\":\"ManualAfterSale\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2024-04-02 10:31:54\"},{\"Active\":false,\"Id\":41,\"Title\":\"手工售后单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/Common/Page/AfterSale-ManualAfterSale\",\"ClassName\":\"ManualAfterSale\",\"ParentId\":166,\"IsDelete\":false,\"CreateTime\":\"2024-04-02 10:31:54\"},{\"Active\":false,\"Id\":42,\"Title\":\"平台售后单\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/Common/Page/AfterSale-Index\",\"ClassName\":\"AfterSale\",\"ParentId\":166,\"IsDelete\":false,\"CreateTime\":\"2024-04-02 10:31:54\"},{\"Active\":false,\"Id\":-230,\"Title\":\"我的小站\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/GeneralizeIndex/MyStationCard\",\"ClassName\":\"MyStationCard\",\"ParentId\":1,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":-231,\"Title\":\"基础商品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/BaseProduct/NewBaseProduct\",\"ClassName\":\"ProductBasics\",\"ParentId\":5,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":-232,\"Title\":\"厂家商品\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/DistributionProduct/SelectionDistribution\",\"ClassName\":\"SelectionDistribution\",\"ParentId\":5,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":-233,\"Title\":\"铺货日志\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":6,\"Url\":\"/BaseProduct/DistributionLog\",\"ClassName\":\"DistributionLog\",\"ParentId\":5,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":101,\"Title\":\"待付款列表\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/Common/Page/NewOrder-AliIncludePayOrder\",\"ClassName\":\"WaitPayOrders\",\"ParentId\":-1,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":102,\"Title\":\"交易设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/SupplySet1688/DistributorSet\",\"ClassName\":\"TradeSetting\",\"ParentId\":-1,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":103,\"Title\":\"交易记录\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":4,\"Url\":\"/FundsManagement/TransactionDetailAgent\",\"ClassName\":\"TransactionDetailAgent\",\"ParentId\":-1,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":104,\"Title\":\"1688货源\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/SupplySet1688/DistributionProductSearch\",\"ClassName\":\"Source1688\",\"ParentId\":-1,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":201,\"Title\":\"收单列表\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":1,\"Url\":\"/NewOrder/AliIncludeOrder\",\"ClassName\":\"CollectOrder\",\"ParentId\":-2,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":202,\"Title\":\"关联货源\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":2,\"Url\":\"/DistributionProduct/ListBySupplier\",\"ClassName\":\"ListBySupplier\",\"ParentId\":-2,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":203,\"Title\":\"收单设置\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":3,\"Url\":\"/SupplySet1688/SupplierSetBy1688\",\"ClassName\":\"SupplierSetBy1688\",\"ParentId\":-2,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":204,\"Title\":\"交易记录\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":5,\"Url\":\"/FundsManagement/TransactionDetailSupplier\",\"ClassName\":\"TransactionDetailAgent\",\"ParentId\":-2,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"},{\"Active\":false,\"Id\":206,\"Title\":\"发货记录\",\"Icon\":\"\",\"IconSize\":\"\",\"Sort\":6,\"Url\":\"/SendHistoryReturnRecord/List\",\"ClassName\":\"ReturnRecord\",\"ParentId\":-2,\"IsDelete\":false,\"CreateTime\":\"2024-07-24\"}]";
                return navJson;
            }
        }
        /// <summary>
        /// 旧版菜单
        /// </summary>
        public static string OldFenDanSystemNavJson
        {
            get
            {
                string navJson = null;
                //var navJson = "[{\"Active\":false,\"Id\":58,\"Title\":\"工作台\",\"Icon\":null,\"IconSize\":null,\"Sort\":1,\"Url\":\"\",\"ClassName\":\"Workbench\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":59,\"Title\":\"授权管理\",\"Icon\":null,\"IconSize\":null,\"Sort\":2,\"Url\":\"/Partner/Index\",\"ClassName\":\"Authorization\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":60,\"Title\":\"订单管理\",\"Icon\":null,\"IconSize\":null,\"Sort\":3,\"Url\":\"/Common/Page/NewOrder-AllOrder\",\"ClassName\":\"OrderManagement\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":61,\"Title\":\"财务结算\",\"Icon\":null,\"IconSize\":null,\"Sort\":4,\"Url\":\"/FinancialSettlement/PriceSetting\",\"ClassName\":\"SettlementAccount\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":62,\"Title\":\"商品管理\",\"Icon\":null,\"IconSize\":null,\"Sort\":5,\"Url\":\"/Common/Page/Product-Index?type=all\",\"ClassName\":\"ProductManagement\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":63,\"Title\":\"库存管理\",\"Icon\":null,\"IconSize\":null,\"Sort\":6,\"Url\":\"/StockControl/StoreManagement\",\"ClassName\":\"InventoryManagement\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":64,\"Title\":\"系统设置\",\"Icon\":null,\"IconSize\":null,\"Sort\":7,\"Url\":\"/System/Qualification\",\"ClassName\":\"System\",\"ParentId\":0,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":65,\"Title\":\"首页\",\"Icon\":null,\"IconSize\":null,\"Sort\":1,\"Url\":\"/GeneralizeIndex/Index\",\"ClassName\":\"GeneralizeIndex\",\"ParentId\":58,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":66,\"Title\":\"1688担保交易\",\"Icon\":null,\"IconSize\":null,\"Sort\":2,\"Url\":\"/GeneralizeIndex/aliDistributionIntroduce\",\"ClassName\":\"GeneralizeIndex\",\"ParentId\":58,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":67,\"Title\":\"导出任务\",\"Icon\":null,\"IconSize\":null,\"Sort\":3,\"Url\":\"/ExportTask/Index\",\"ClassName\":\"ExportTask\",\"ParentId\":58,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":68,\"Title\":\"添加店铺\",\"Icon\":null,\"IconSize\":null,\"Sort\":1,\"Url\":\"/Partner/Index\",\"ClassName\":\"Partner\",\"ParentId\":59,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":69,\"Title\":\"绑定厂家\",\"Icon\":null,\"IconSize\":null,\"Sort\":2,\"Url\":\"/Partner/MySupplier\",\"ClassName\":\"Mysupplier\",\"ParentId\":59,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":70,\"Title\":\"绑定商家\",\"Icon\":null,\"IconSize\":null,\"Sort\":3,\"Url\":\"/Partner/MyAgent\",\"ClassName\":\"MyAgent\",\"ParentId\":59,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":71,\"Title\":\"分销设置\",\"Icon\":null,\"IconSize\":null,\"Sort\":4,\"Url\":\"/System/DistributeSet\",\"ClassName\":\"SystemSettings\",\"ParentId\":59,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":72,\"Title\":\"所有订单\",\"Icon\":null,\"IconSize\":null,\"Sort\":1,\"Url\":\"/Common/Page/NewOrder-AllOrder\",\"ClassName\":\"AllOrder\",\"ParentId\":60,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":73,\"Title\":\"打单发货\",\"Icon\":null,\"IconSize\":null,\"Sort\":2,\"Url\":\"/Common/Page/NewOrder-WaitOrder\",\"ClassName\":\"WaitOrder\",\"ParentId\":60,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":74,\"Title\":\"代发线下单\",\"Icon\":null,\"IconSize\":null,\"Sort\":3,\"Url\":\"/NewOrder/OfflineOrder\",\"ClassName\":\"OfflineOrder\",\"ParentId\":60,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":75,\"Title\":\"备货单\",\"Icon\":null,\"IconSize\":null,\"Sort\":4,\"Url\":\"/Common/Page/Purchases-Index\",\"ClassName\":\"Purchases\",\"ParentId\":60,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":76,\"Title\":\"底单查询\",\"Icon\":null,\"IconSize\":null,\"Sort\":5,\"Url\":\"/Common/Page/WaybillCodeList-Index\",\"ClassName\":\"WaybillCodeList\",\"ParentId\":60,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":77,\"Title\":\"打印记录\",\"Icon\":null,\"IconSize\":null,\"Sort\":6,\"Url\":\"/Common/Page/PrintHistory-Index\",\"ClassName\":\"PrintHistory\",\"ParentId\":60,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":78,\"Title\":\"发货记录\",\"Icon\":null,\"IconSize\":null,\"Sort\":7,\"Url\":\"/Common/Page/SendHistory-Index\",\"ClassName\":\"SendHistory\",\"ParentId\":60,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":79,\"Title\":\"打单设置\",\"Icon\":null,\"IconSize\":null,\"Sort\":8,\"Url\":\"/SetInfo\",\"ClassName\":\"PrintSetting\",\"ParentId\":60,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":80,\"Title\":\"快递对账\",\"Icon\":null,\"IconSize\":null,\"Sort\":9,\"Url\":\"/ExpressBill/Index\",\"ClassName\":\"ExpressBillIndex\",\"ParentId\":60,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":81,\"Title\":\"对账设置\",\"Icon\":null,\"IconSize\":null,\"Sort\":1,\"Url\":\"/FinancialSettlement/PriceSetting\",\"ClassName\":\"FinancialSettlement_PriceSetting\",\"ParentId\":61,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":82,\"Title\":\"对账中心\",\"Icon\":null,\"IconSize\":null,\"Sort\":2,\"Url\":\"/FinancialSettlement/BillManagement\",\"ClassName\":\"FinancialSettlement_BillManagement\",\"ParentId\":61,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":83,\"Title\":\"厂家底单\",\"Icon\":null,\"IconSize\":null,\"Sort\":3,\"Url\":\"/Common/Page/WaybillCodeList-Index?type=cj\",\"ClassName\":\"WaybillCodeList_cj\",\"ParentId\":61,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":84,\"Title\":\"已发货明细\",\"Icon\":null,\"IconSize\":null,\"Sort\":4,\"Url\":\"/Common/Page/SendOrder-Index\",\"ClassName\":\"SendOrder\",\"ParentId\":61,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":85,\"Title\":\"代发售后\",\"Icon\":null,\"IconSize\":null,\"Sort\":5,\"Url\":\"/Common/Page/AfterSale-Index\",\"ClassName\":\"AfterSale\",\"ParentId\":61,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":86,\"Title\":\"资金流水\",\"Icon\":null,\"IconSize\":null,\"Sort\":6,\"Url\":\"/FundsManagement/TransactionDetailAgent\",\"ClassName\":\"FundsManagement\",\"ParentId\":61,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":87,\"Title\":\"商品列表\",\"Icon\":null,\"IconSize\":null,\"Sort\":1,\"Url\":\"/Common/Page/Product-Index?type=all\",\"ClassName\":\"Product_all\",\"ParentId\":62,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":88,\"Title\":\"自营商品\",\"Icon\":null,\"IconSize\":null,\"Sort\":2,\"Url\":\"/Common/Page/Product-Index?type=oneself\",\"ClassName\":\"Product_oneself\",\"ParentId\":62,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":89,\"Title\":\"代发商品\",\"Icon\":null,\"IconSize\":null,\"Sort\":3,\"Url\":\"/Common/Page/Product-Index?type=other\",\"ClassName\":\"Product_other\",\"ParentId\":62,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":90,\"Title\":\"仓库管理\",\"Icon\":null,\"IconSize\":null,\"Sort\":1,\"Url\":\"/StockControl/StoreManagement\",\"ClassName\":\"StoreManagement\",\"ParentId\":63,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":91,\"Title\":\"库存货品\",\"Icon\":null,\"IconSize\":null,\"Sort\":2,\"Url\":\"/StockControl/WareHouseProduct\",\"ClassName\":\"WareHouseProduct\",\"ParentId\":63,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":92,\"Title\":\"实时库存\",\"Icon\":null,\"IconSize\":null,\"Sort\":3,\"Url\":\"/StockControl/StockDetail\",\"ClassName\":\"StockControl_StockDetail\",\"ParentId\":63,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":93,\"Title\":\"库存变更记录\",\"Icon\":null,\"IconSize\":null,\"Sort\":4,\"Url\":\"/StockControl/ChangeDetail\",\"ClassName\":\"StockControl_ChangeDetail\",\"ParentId\":63,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":94,\"Title\":\"账户管理\",\"Icon\":null,\"IconSize\":null,\"Sort\":1,\"Url\":\"/System/Qualification\",\"ClassName\":\"System\",\"ParentId\":64,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true},{\"Active\":false,\"Id\":95,\"Title\":\"微信授权\",\"Icon\":null,\"IconSize\":null,\"Sort\":2,\"Url\":\"/System/BindWxUserIndex\",\"ClassName\":\"System\",\"ParentId\":64,\"IsDelete\":false,\"CreateTime\":\"2024-06-19 10:45:20\",\"NavFrom\":true}]";
                return navJson;
            }
        }
        #endregion



        #region 分销各平台使用的AppKey，和主客铺货使用的不是同一个AppKey需要在这里添加下，否则可能会取错ShopExtension信息
        /// <summary>
        /// 分销各平台使用的AppKey，和主客铺货使用的不是同一个AppKey需要在这里添加下，否则可能会取错ShopExtension信息
        /// </summary>
        public static Dictionary<string, string> FxSystemAppKeyDict
        {
            get
            {
                return new Dictionary<string, string>()
                {
                    { PddFxAppKey , PddFxAppKey },
                    { KuaiShouFxAppKey , KuaiShouFxAppKey },
                    { TouTiaoFxAppKey , TouTiaoFxAppKey },
                    { Fx_WxXiaoShangDianAppId , Fx_WxXiaoShangDianAppId },
                    { XiaoHongShuFXAppKey , XiaoHongShuFXAppKey },
                    { WeiMengFxAppKey,WeiMengFxAppKey },
                    {KuaiShouFxFastAppKey,KuaiShouFxFastAppKey },
                    {PddFxFastAppKey,PddFxFastAppKey },
                    { YouZanFxAppKey,YouZanFxAppKey},
                    { TouTiaoFxNewAppKey,TouTiaoFxNewAppKey},
                    {WeiDianFxAppKey,WeiDianFxAppKey },
                    { MoGuFxAppKey,MoGuFxAppKey},
                    {VipShopFxAppKey,VipShopFxAppKey },
                    {SuningFxAppKey,SuningFxAppKey },
                    { MoKuaiFxAppKey,MoKuaiFxAppKey},
                    { JingDongFxAppKey,JingDongFxAppKey},
                    { DuXiaoDianV2FxAppKey,DuXiaoDianV2FxAppKey},
                    { Fx_WxComponentNewAppId,Fx_WxComponentNewAppId},
                    { Fx_WxShopNewAppId, Fx_WxShopNewAppId},
                    { AlibabaQingAppKey,AlibabaQingAppKey},
                    { TouTiaoFxListingAppKey,TouTiaoFxListingAppKey},
                    { FxTouTiaoSaleShopAppKey,FxTouTiaoSaleShopAppKey}
                };
            }
        }


        public static Dictionary<string, string> FxSystemAppKeyNameDict
        {
            get
            {
                return new Dictionary<string, string>()
                {
                    { PddFxAppKey , "店管家_分销代发" },
                    { KuaiShouFxAppKey , "店管家_分销代发" },
                    { TouTiaoFxAppKey , "店管家铺货助手" },
                    { TouTiaoFxNewAppKey,"店管家_分销代发"},
                    { Fx_WxXiaoShangDianAppId , "店管家_分销代发" },
                    { XiaoHongShuFXAppKey , "店管家_分销代发" },
                    { WeiMengFxAppKey,"店管家_分销代发" },
                    { KuaiShouFxFastAppKey,"店管家_智速打单" },
                    { PddFxFastAppKey,"店管家_智速打单" },
                    { YouZanFxAppKey,"店管家_分销代发"},
                    { WeiDianFxAppKey,"店管家_分销代发" },
                    { MoGuFxAppKey,"店管家_分销代发"},
                    { VipShopFxAppKey,"店管家_分销代发" },
                    { SuningFxAppKey,"店管家_分销代发" },
                    { MoKuaiFxAppKey,"店管家_分销代发"},
                    { JingDongFxAppKey,"店管家_分销代发"},
                    { DuXiaoDianV2FxAppKey,"店管家_分销代发"},
                    { Fx_WxComponentNewAppId , "中恒_分销代发" },
                    { Fx_WxShopNewAppId, "店管家_分销代发" },
                    { AlibabaQingAppKey , "店管家_分销代发" },
                    { TouTiaoFxListingAppKey , "店管家分销铺货" },
                };
            }
        }
        #endregion


        /// <summary>
        /// SQL like语句特殊字符替换
        /// </summary>
        /// <param name="val"></param>
        /// <returns></returns>
        public static string ReplaceSqlChar(string val)
        {
            if (val.IsNullOrEmpty())
                return val;
            var newVal = val.Replace("[", "[[").Replace("%", "[%]").Replace("_", "[_]").Replace("^", "[^]");
            return newVal;
        }

        #region 快递模板类型判断
        /// <summary>
        /// 是否使用抖音新订单接口（全量发布后弃用）
        /// </summary>
        public static bool IsUseDyNewInterface
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get("IsUseDyNewInterface") ?? "";
                if (string.IsNullOrEmpty(value))
                    return true;
                return value == "1" || value.ToLower() == "true";
            }
        }

        #region 模板相关校验

        /// <summary>
        /// 是否是菜鸟快递模板
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsCainiaoTemplate(int templateType)
        {
            var isCaiNiao = templateType == 2 || (templateType > 3 && templateType <= 9);
            return isCaiNiao;
        }

        /// <summary>
        /// 是否是菜鸟快运模板
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsKuaiYunTemplate(int templateType)
        {
            return templateType == 7 || templateType == 8;
        }

        /// <summary>
        /// 是否是菜鸟官方模板
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsLinkTemplate(int templateType)
        {
            return templateType >= 40 && templateType < 50;
        }

        /// <summary>
        /// 是否是菜鸟官方快运模板
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsLinkKuaiYunTemplate(int templateType)
        {
            return templateType >= 50 && templateType < 60;
        }

        /// <summary>
        /// 是否是普通模板 (1:传统面单，2:网点面单，3:顺丰丰桥）
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsNormalTemplate(int templateType)
        {
            return templateType == 1 || templateType == 3 || templateType == 10;
        }

        /// <summary>
        /// 是否是网点中通
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsZhiLian(int templateType, string companyCode)
        {
            if (templateType == 3 && (companyCode == "ZTO" || companyCode == "STO" || companyCode == "YTO" || companyCode == "HTKY" || companyCode == "JT"))
                return true;
            return false;
        }

        /// <summary>
        /// 是否是顺丰
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsSF(int templateType)
        {
            return templateType == 10;
        }

        /// <summary>
        /// 是否是网点模板 (1:传统面单，2:网点面单，3:顺丰丰桥）
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsSiteTemplate(int templateType)
        {
            return templateType == 3 || templateType == 10 || templateType == 13;
        }

        /// <summary>
        /// 是否是头条的电子面单 (11:头条电子面单）
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsTouTiaoTemplate(int templateType)
        {
            return templateType == 11 || templateType == 12;
        }

        /// <summary>
        /// 是否是抖店组件电子面单 (17,18:抖店组件电子面单）
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsTouTiaozjTemplate(int templateType)
        {
            return templateType == 17 || templateType == 18;
        }

        /// <summary>
        /// 是否是抖店快运 (19,20:抖店快运电子面单）
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsTouTiaoKuaiYunTemplate(int templateType)
        {
            return templateType == 19 || templateType == 20;
        }

        /// <summary>
        /// 是否是拼多多模板
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsPddTemplate(int templateType)
        {
            return templateType > 20 && templateType < 30;
        }

        /// <summary>
        /// 是否是拼多多快运模板
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsPddKuaiYunTemplate(int templateType, string companyCode = "")
        {
            if (templateType > 30 && templateType < 40)
                return true;

            var exCodes = new List<string> { "SFKY", "CN7000001021040", "CN7000001000869", "3108002701_1011", "BESTQJT", "CNEX", "2460304407_385" };  //这里是下面添加的快递公司对应的code
            if (IsPddTemplate(templateType) && exCodes.Contains(companyCode))
                return true;

            return false;
        }

        /// <summary>
        /// 是否是京东无界模板
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsJdWjTemplate(int templateType)
        {
            return templateType > 60 && templateType < 70;
        }

        /// <summary>
        /// 是否是京东快递模板
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsJdKdTemplate(int templateType)
        {
            return templateType > 80 && templateType < 90;
        }

        /// <summary>
        /// 是否是京东组件模板
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsJdzjTemplate(int templateType)
        {
            return templateType >= 91 && templateType < 96;
        }

        /// <summary>
        /// 是否是京东无界组件模板
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsJdWjzjTemplate(int templateType)
        {
            return templateType >= 96 && templateType < 100;
        }

        /// <summary>
        /// 是否是快手电子面单 (111 - 120 快手电子面单）
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsKuaiShouTemplate(int templateType)
        {
            return templateType >= 111 && templateType < 120;
        }

        /// <summary>
        /// 是否是小红书电子面单 (150 - 160小红书电子面单）
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsXiaoHongShuTemplate(int templateType)
        {
            return templateType >= 150 && templateType < 160;
        }

        /// <summary>
        /// 是否是新版小红书电子面单 (180 - 190小红书电子面单）
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsNewXiaoHongShuTemplate(int templateType)
        {
            return templateType >= 180 && templateType < 190;
        }

        /// <summary>
        /// 是否是视频号电子面单 (160 - 170视频号电子面单）
        /// </summary>
        /// <param name="templateType"></param>
        /// <returns></returns>
        public static bool IsWxVideoTemplate(int templateType)
        {
            return templateType >= 160 && templateType < 170;
        }
        #endregion

        /// <summary>
        /// 根据当前云平台返回可查询模板条件，匹配不到云平台，返回null
        /// </summary>
        /// <param name="tableField">列名</param>
        /// <param name="cloudPlatformType">云平台</param>
        /// <returns></returns>
        public static string QueryableCloudTemplateSQL(string tableField, string cloudPlatformType)
        {
            var cloud = cloudPlatformType.ToLower();
            if (cloud == "pinduoduo") return $"{tableField} BETWEEN 21 AND 39";
            if (cloud == "toutiao") return $"{tableField} IN(11,12,17,18,19,20)";
            return string.Empty;
        }
        #endregion

        public static string PrintHistoryDefaultMysqlConnectionString
        {
            get
            {
                var defaultConn = "";
                //if (CloudPlatformType == "Pinduoduo")
                //    defaultConn = "Database=printhistory_fendan;Data Source=10.1.15.7;User Id=jusr88xsm9q2;Password=******************;CharSet=utf8;port=3306";
                //else if (CloudPlatformType == "JingDong")
                //    defaultConn = "Database=printhistory_fendan;Data Source=172.26.234.54;User Id=jusr88xsm9q2;Password=******************;CharSet=utf8;port=3306";
                var url = System.Configuration.ConfigurationManager.ConnectionStrings["PrintHistoryDefaultMysqlConnectionString"]?.ConnectionString;
                if (string.IsNullOrEmpty(url))
                    return defaultConn;
                else
                    return url;
            }
        }
        /// <summary>
        /// 收件人信息数据库
        /// </summary>
        public static string ReceiverDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["ReceiverDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 【基础商品主库】数据库
        /// </summary>
        public static string BaseProductDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["BaseProductConfigureDB"]?.ConnectionString
                    ?? "Database=base_product_main_db;Data Source=rm-k2j6f0o9pfl237rl3.mysql.zhangbei.rds.aliyuncs.com;User Id=jusr88xsm9q2;Password=******************;CharSet=utf8;port=3306";
            }
        }



        /// <summary>
        /// 【爆品分析主库】数据库
        /// </summary>
        public static string HotProductDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["HotProductConfigureDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 【利润统计主库】数据库
        /// </summary>
        public static string ProfitStatisticsDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["ProfitStatisticsConfigureDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 【数据统计库】数据库
        /// </summary>
        public static string DataStatisticsDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["DataStatisticsDB"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// 批量发货异常补偿：是否主动抛出异常
        /// </summary>
        /// <returns></returns>
        public static bool IsThrowException()
        {
            return System.Configuration.ConfigurationManager.AppSettings.Get("IsThrowException") == "1";
        }
        /// <summary>
        /// 是否设为发货失败，用于调试：1=首次发货设为失败；2=二次发货设为失败
        /// </summary>
        /// <returns></returns>
        public static int SetSendFail()
        {
            return System.Configuration.ConfigurationManager.AppSettings.Get("SetSendFail").ToInt();
        }

        /// <summary>
        /// 是否是非店铺面单
        /// 用于平台头条，小红书
        /// </summary>
        /// <param name="platformType"></param>
        /// <param name="shopId">根据平台店铺Id来判断账号类型</param>
        /// <returns></returns>
        public static bool IsEbillWbyShop(string platformType, string shopId)
        {
            if (platformType.IsNullOrEmpty() || shopId.IsNullOrEmpty())
                return false;

            var lowerPt = platformType.ToLower();
            if (lowerPt == "toutiao" && shopId.StartsWith("1003") && shopId.Length == 16)
                return true;
            else if (lowerPt == "xiaohongshu" && shopId.StartsWith("ebill_"))
                return true;
            return false;
        }

        /// <summary>
        /// 是否修复发货记录（不调用发货接口）
        /// </summary>
        /// <returns></returns>
        public static bool IsRepairSendHistory()
        {
            return IsDebug && RepairSendHistoryDate().IsNotNullOrEmpty();
        }

        public static bool IsTestReceiverChange()
        {
            return IsDebug && System.Configuration.ConfigurationManager.AppSettings.Get("IsTestReceiverChange") == "1";
        }

        private static string _repairSendHistoryDate;
        public static string RepairSendHistoryDate()
        {
            //return System.Configuration.ConfigurationManager.AppSettings.Get("RepairSendHistoryDate");
            if (_repairSendHistoryDate.IsNotNullOrEmpty())
                return _repairSendHistoryDate;
            _repairSendHistoryDate = System.Configuration.ConfigurationManager.AppSettings.Get("RepairSendHistoryDate");
            return _repairSendHistoryDate;
        }

        public static void SetRepairSendHistoryDate(string date)
        {
            _repairSendHistoryDate = date;
        }

        /// <summary>
        /// 获取需要/支持同步售后的平台列表，为空表示所有
        /// </summary>
        /// <returns></returns>
        public static List<string> GetNeedAfterSalePlatform()
        {
            var result = new List<string>();
            var platformType = PlatformAfterSale;
            if (platformType?.ToLower() == "all" || platformType?.ToLower() == "*")
            {
                return result;
            }
            else if (platformType.StartsWith("!") && CloudPlatformType == "Alibaba")
            {
                //精选云指定平台类型
                platformType = "Alibaba,Taobao,AlibabaC2M,KuaiShou,WxVideo,TaobaoMaiCai,Other_Heliang,Other_JuHaoMai,BiliBili,XiaoHongShu,Other_HaoYouDuo,TaobaoMaiCaiV2";
                result = platformType.Split(',').ToList();
            }
            else if (platformType.Contains(","))
            {
                result = platformType.Split(',').Select(s => $"{s.Trim('!')}").ToList();
            }
            else
            {
                result.Add(platformType);
            }

            return result;
        }

        /// <summary>
        ///  当前程序名称
        /// </summary>
        public static string ApplicationName
        {
            get
            {
                var v = System.Configuration.ConfigurationManager.AppSettings.Get("ApplicationName");
                if (v.IsNullOrEmpty())
                    v = AppDomain.CurrentDomain.BaseDirectory;
                return v;
            }
        }

        /// <summary>
        /// 用户x小时没同步才触发，默认72小时
        /// </summary>
        public static int SyncProductHours
        {
            get
            {
                var hours = System.Configuration.ConfigurationManager.AppSettings.Get("SyncProductHours").ToInt();
                if (hours <= 0)
                    return 72;
                else
                    return hours;
            }
        }
        /// <summary>
        /// 指定ShopId调试售后同步，为0不调试
        /// </summary>
        public static int DebugSyncShopId
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DebugSyncShopId")?.ToInt() ?? 0;
            }
        }

        /// <summary>
        /// 消息消费程序默认线程数
        /// </summary>
        public static int MessageConsumerDefaultThreadCount
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("MessageConsumerDefaultThreadCount")?.ToInt() ?? 10;
            }
        }

        public static string DbFieldToName(string dbField, string tbName)
        {
            var name = string.Empty;
            if (dbField.IsNullOrEmpty())
                return name;
            if (tbName.IsNullOrEmpty())
                return name;
            dbField = dbField.ToLower();
            tbName = tbName.ToLower();

            if (tbName == "p_order" || tbName == "p_customerorder")
            {
                #region 订单字段
                switch (dbField)
                {
                    case "platformorderid":
                        name = "订单编号";
                        break;
                    case "customerorderid":
                        name = tbName == "p_customerorder" ? "订单编号" : "customerorderid";
                        break;
                    case "batchno":
                        name = "导入批次号";
                        break;
                    case "platformtype":
                        name = "平台名称";
                        break;
                    case "childorderid":
                        name = "子单号";
                        break;
                    case "parentorderid":
                        name = "父单号";
                        break;
                    case "mergeredorderid":
                        name = "合并单号";
                        break;
                    case "buyerhashcode":
                        name = "buyerhashcode";
                        break;
                    case "printedbuyerhashcode":
                        name = "printedbuyerhashcode";
                        break;
                    case "couldmergerorderid":
                        name = "可合并单号";
                        break;
                    case "couldmergerorderidext":
                        name = "可合并单号2";
                        break;
                    case "businesstype":
                        name = "businesstype";
                        break;
                    case "islocked":
                        name = "islocked";
                        break;
                    case "orderfrom":
                        name = "orderfrom";
                        break;
                    case "buyerwangwang":
                        name = "买家昵称";
                        break;
                    case "buyermemberid":
                        name = "买家id";
                        break;
                    case "toname":
                        name = "收件人姓名";
                        break;
                    case "tophone":
                        name = "收件人固话";
                        break;
                    case "tomobile":
                        name = "收件人手机号";
                        break;
                    case "toprovince":
                        name = "省份";
                        break;
                    case "tocity":
                        name = "市";
                        break;
                    case "tocounty":
                        name = "区";
                        break;
                    case "topost":
                        name = "邮编";
                        break;
                    case "toaddress":
                        name = "收件人地址";
                        break;
                    case "tofulladdress":
                        name = "收件人全地址";
                        break;
                    case "sendername":
                        name = "发件人姓名";
                        break;
                    case "senderphone":
                        name = "发件人固话";
                        break;
                    case "sendermobile":
                        name = "发件人手机号";
                        break;
                    case "senderaddress":
                        name = "发件人地址";
                        break;
                    case "sendercompany":
                        name = "发件人公司";
                        break;
                    case "buyermembername":
                        name = "buyermembername";
                        break;
                    case "buyerremark":
                        name = "买家留言";
                        break;
                    case "sellerremark":
                        name = "卖家备注";
                        break;
                    case "sellerremarkflag":
                        name = "备注旗帜";
                        break;
                    case "lastwaybillcode":
                        name = "最新运单号";
                        break;
                    case "platformstatus":
                        name = "订单状态";
                        break;
                    case "tradetype":
                        name = "tradetype";
                        break;
                    case "refundstatus":
                        name = "退款状态";
                        break;
                    case "printedserialnumber":
                        name = "打印流水号";
                        break;
                    case "backupplatformstatus":
                        name = "bk订单状态";
                        break;
                    case "printcontent":
                        name = "发货内容";
                        break;
                    case "warehouse":
                        name = "仓库";
                        break;
                    case "extfield1":
                        name = "扩展字段1";
                        break;
                    case "extfield2":
                        name = "扩展字段2";
                        break;
                    case "extfield3":
                        name = "扩展字段3";
                        break;
                    case "extfield4":
                        name = "扩展字段4";
                        break;
                    case "extfield5":
                        name = "扩展字段5";
                        break;
                    default:
                        break;
                }
                #endregion
            }
            else if (tbName == "p_orderitem" || tbName == "p_customerorderitem")
            {
                #region 订单项字段
                switch (dbField)
                {
                    case "orignalorderid":
                        name = "原始单号";
                        break;
                    case "productsubject":
                        name = "商品名称";
                        break;
                    case "productcargonumber":
                        name = "商品货号";
                        break;
                    case "cargonumber":
                        name = "单品货号";
                        break;
                    case "productid":
                        name = "商品id";
                        break;
                    case "productimgurl":
                        name = "图片地址";
                        break;
                    case "status":
                        name = "订单项状态";
                        break;
                    case "skuid":
                        name = "skuid";
                        break;
                    case "unit":
                        name = "单位";
                        break;
                    case "weightunit":
                        name = "重量单位";
                        break;
                    case "subitemid":
                        name = "subitemid";
                        break;
                    case "color":
                        name = tbName == "p_customerorderitem" ? "销售属性" : "颜色";
                        break;
                    case "size":
                        name = tbName == "p_customerorderitem" ? "销售属性" : "尺码";
                        break;
                    case "extattr1":
                        name = "商品扩展字段1";
                        break;
                    case "extattr2":
                        name = "商品扩展字段2";
                        break;
                    case "extattr3":
                        name = "商品扩展字段3";
                        break;
                    case "extattr4":
                        name = "商品扩展字段4";
                        break;
                    case "extattr5":
                        name = "商品扩展字段5";
                        break;
                    case "backupplatformstatus":
                        name = "订单项bk状态";
                        break;
                    case "refundstatus":
                        name = "订单项退款状态";
                        break;
                    case "specid":
                        name = "规格id";
                        break;
                    case "ordercode":
                        name = "ordercode";
                        break;
                    case "orderitemcode":
                        name = "orderitemcode";
                        break;
                    case "goodsinfo":
                        name = "产品信息";
                        break;
                    default:
                        break;
                }
                #endregion
            }

            return name;
        }

        /// <summary>
        /// 尝试重写拼多多的https链接，因为拼多多云平台无法直接访问https，得换成
        /// 云内调用http-tls协议地址时，需使用端口:29943，详见地址详情页中云内调用方式。http-tls协议地址云内调用示例
        ///原地址:
        ///https://www.pdd.net/test
        ///http-tls协议云内调用地址：
        ///http://www.pdd.net:29943/test
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static string TryRewritePddHttpsUrl(string url)
        {
            if (CloudPlatformType == "Pinduoduo" && url.StartsWith("https"))
            {
                var urls = url.Replace("https://", "").Replace("http://", "").Split('/').ToList();
                if (urls != null && urls.Any())
                {
                    if (urls.Count() == 1)
                        url = $"http://" + urls[0] + ":29943";
                    else
                        url = $"http://" + urls[0] + ":29943/" + string.Join("/", urls.Skip(1));
                }
            }
            return url;
        }

        /// <summary>
        /// 配置数据Redis链接
        /// </summary>
        public static string ConfigRedisConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings?["ConfigRedis"]?.ConnectionString ?? "";
            }
        }

        /// <summary>
        /// CSRedisCore组件链接字符串
        /// </summary>
        public static string CsRedisCoreConnectionString
        {
            get
            {
                var config = ConfigRedisConnectionString;
                var maxConnCount = RedisPoolMaxConnectionCount;
                if (!config.Contains(",poolsize="))
                {
                    config += $",poolsize={maxConnCount}";
                }

                return config;
            }
        }

        /// <summary>
        /// 区分打单和分单应用的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> DiffAppPlatformType()
        {
            var diffAppPlatformTypes = new List<string> { "TouTiao", "KuaiShou", "WxXiaoShangDian", "WxVideo", "Pinduoduo" };
            return diffAppPlatformTypes;
        }

        /// <summary>
        /// 有剩余时间的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> HasLastShipTimePlatformType()
        {
            var hasLastShipTimePlatformTypes = new List<string> { "WxVideo", "XiaoHongShu", "TouTiao", "KuaiShou", "Pinduoduo", "Taobao", "TouTiaoSaleShop" };
            return hasLastShipTimePlatformTypes;
        }

        /// <summary>
        /// 有预约发货时间的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> HasPublishTimePlatformType()
        {
            return new List<string> {
                "Jingdong"
            };
        }

        /// <summary>
        /// 加密的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> EncryptPlatformTypes()
        {
            return new List<string>
            {
                // 2025-07-10 将淘工厂加回来
                // 2025-04 新增需要过滤加密处理的平台：WxVideo、AlibabaC2M、JingDong，这些平台不需要加密和解密处理
                "Pinduoduo","Taobao","Alibaba", "TouTiao", "KuaiShou","YouZan","Suning","TuanHaoHuo","XiaoHongShu","WeiDian","Virtual","AlibabaC2M","TaobaoMaiCaiV2"
                // "Pinduoduo","Taobao","Alibaba","AlibabaC2M","TouTiao","Jingdong","KuaiShou","YouZan","Suning","TuanHaoHuo","XiaoHongShu","WeiDian","WxVideo","Virtual"
                // "Jingdong","YouZan","Suning","TuanHaoHuo","XiaoHongShu","WeiDian","WxVideo","Virtual"
            };
        }

        /// <summary>
        /// 不需要保存收件人信息，入库到 Receiver 的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> NoReceiverSavePlatformTypes
        {
            get
            {
                try
                {
                    const string keystr = "DianGuanJia:FenXiao:NoReceiverSavePlatformTypes";
                    var value = MemoryCacheHelper.GetCacheItem<string>(keystr);
                    if (string.IsNullOrWhiteSpace(value))
                        value = RedisHelper.Get(keystr);

                    if (string.IsNullOrWhiteSpace(value))
                        return new List<string>();

                    List<string> result = value.Split(',').ToList();
                    if (result.Any()) MemoryCacheHelper.Set(keystr, value, 60 * 5); // 缓存5分钟
                    return result;
                    // WxVideo,AlibabaC2M,Jingdong
                }
                catch (Exception ex)
                {
                    Log.WriteError($"读取[不需要保存收件人信息]配置报错：{ex.Message}. {ex.StackTrace}");
                    return new List<string>();
                }
            }
        }

        /// <summary>
        /// 使用增量同步商品的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> IncrementSyncProductPlatformType()
        {
            var needPlatformTypes = new List<string> { "TouTiao", "XiaoHongShu", "Alibaba" };
            return needPlatformTypes;
        }

        /// <summary>
        /// 已对接修改收件人信息的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> UpdateReceiverPlatformTypes()
        {
            return new List<string>
            {
                "KuaiShou",
                "Pinduoduo",
                "WxVideo",
                "Jingdong"
            };
        }

        /// <summary>
        /// 异常单标签
        /// 已打印的订单当地址变更需要加标签的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> ReceiverChangeNeedAddTagPlatformType()
        {
            //等加密改造过度一段时间后再开启视频号的异常单标签
            //var needPlatformTypes = new List<string> { "TouTiao", "KuaiShou", "WxVideo" };
            //var needPlatformTypes = new List<string> { "TouTiao", "KuaiShou"}; 快手四级地址，暂时过渡，不开启变更地址标签
            var needPlatformTypes = new List<string> { "TouTiao", "Jingdong", "KuaiShou", "BiliBili" ,"Pinduoduo", "TouTiaoSaleShop"};
            if (CustomerConfig.IsDebug)
            {
                needPlatformTypes.Add("Virtual");
            }
            return needPlatformTypes;
        }

        /// <summary>
        /// 根据订单编号查询标签的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> NeedQueryTagPlatformType()
        {
            var needPlatformTypes = new List<string> { "KuaiShou", "Alibaba", "TouTiao", "TouTiaoSaleShop", "Pinduoduo", "Virtual", "Jingdong", "JingdongPurchase", "Taobao", "OwnShop" };
            return needPlatformTypes;
        }

        /// <summary>
        /// 异常单，包含的标签类型
        /// </summary>
        /// <returns></returns>
        public static List<string> ExceptionOrderManageTagNames
        {
            get
            {
                var needTagNames = new List<string> {
                "receiver_change",
                "sf_free_shipping",
                "toutiao_high_quality_logistics"
                };
                return needTagNames;
            }
        }

        /// <summary>
        /// 顺丰包邮标签类型
        /// </summary>
        /// <returns></returns>
        public static List<string> SfFreeShippingTagNames
        {
            get
            {
                var needTagNames = new List<string> {
                "sf_free_shipping",
                };
                return needTagNames;
            }
        }

        /// <summary>
        /// [拆/合/更新]逻辑单时需要处理标签的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> SplitNeedHandleTagPlatformType
        {
            get
            {
                var needPlatformTypes = new List<string> { "TouTiao", "TouTiaoSaleShop" };
                if (CustomerConfig.IsDebug)
                {
                    needPlatformTypes.Add("Virtual");
                }
                return needPlatformTypes;
            }
        }

        /// <summary>
        /// 不需要合并的订单标签
        /// </summary>
        public static List<string> NotNeedMergeTagNames
        {
            get
            {
                var needTagNames = new List<string> {
                "logistics_transit",
                };
                return needTagNames;
            }
        }

        /// <summary>
        /// 需要查询订单项标签的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> NeedQueryOrderItemTagPlatformType
        {
            get
            {
                var needPlatformTypes = new List<string> { "KuaiShou" };
                if (CustomerConfig.IsDebug)
                {
                    needPlatformTypes.Add("Virtual");
                }
                return needPlatformTypes;
            }
        }

        #region 阿里云SLS日志配置
        public static string AliLogUrl
        {
            get
            {
                if (CustomerConfig.CloudPlatformType == "Alibaba" && !CustomerConfig.IsLocalDbDebug)
                    return System.Configuration.ConfigurationManager.AppSettings.Get("AliLog:Url") ?? "cn-zhangjiakou-intranet.log.aliyuncs.com";
                else
                    return System.Configuration.ConfigurationManager.AppSettings.Get("AliLog:Url") ?? "cn-zhangjiakou.log.aliyuncs.com";
            }
        }


        public static string AliLogAppKey
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AliLog:AppKey") ?? "LTAI5t6GF1KKBbaEHhDagHnm";
            }
        }

        public static string AliLogAppSecret
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AliLog:AppSecret") ?? "******************************";
            }
        }
        /// <summary>
        /// OSS 访问域名
        /// 私域 oss-cn-zhangjiakou-internal.aliyuncs.com
        /// </summary>
        public static string AliEndpoint
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("DianGuanJiaApp:AliyunOSS:Endpoint") ?? "oss-cn-zhangjiakou.aliyuncs.com";
            }
        }


        /// <summary>
        /// 阿里云日志Project，默认fendan-system-log
        /// </summary>
        public static string AliLogFxProject
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AliLog:FxProject") ?? "fendan-system-log";
            }
        }

        /// <summary>
        /// 阿里云日志Logstore，默认bulkinsertlog
        /// </summary>
        public static string AliLogFxLogstore
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AliLog:FxLogstore") ?? "bulkinsertlog";
            }
        }
        /// <summary>
        /// 阿里云分单异常日志Logstore
        /// </summary>
        public static string AliLogFxExceptionLogstore
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("AliLog:FxExceptionLogstore") ?? "logstore-fenxiao-service-exception";
            }
        }
        #endregion

        #region IPv4 解析
        /// <summary>
        /// IPV4解析URL
        /// </summary>
        public static string IPv4Url
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IPv4Url") ?? "https://api.ipplus360.com/ip/geo/v1/city/";
            }
        }

        /// <summary>
        /// IPV4解析KEY
        /// </summary>
        public static string IPv4Key
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("IPv4Key") ?? "sSLY0D2ZDyYZ7l2a0E1AVPg2bZ6VEJXsoBOx5MhB9BWalzWQZPavnoSpcxePNoYK";
            }
        }

        #endregion

        /// <summary>
        /// 是否启用非风控用户登录校验，1为启用
        /// </summary>
        public static bool IsCheckLoginRegion
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Login:IsCheckLoginRegion") == "1";
            }
        }

        /// <summary>
        /// 是否启用重置密码令牌设置过期开关，1为启用
        /// </summary>
        public static bool IsClearTokenAtfterResetPwd
        {
            get
            {
                var str = System.Configuration.ConfigurationManager.AppSettings.Get("Login:IsClearTokenAtfterResetPwd");
                if (string.IsNullOrWhiteSpace(str))
                {
                    return true; // 2025-04-24：默认开启
                }
                else
                {
                    return System.Configuration.ConfigurationManager.AppSettings.Get("Login:IsClearTokenAtfterResetPwd") == "1";
                }
            }
        }

        /// <summary>
        /// 用户检测范围
        /// </summary>
        public static int CheckLoginRegionScope
        {
            get
            {
                return Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("Login:CheckLoginRegionScope") ?? "0");
            }
        }

        /// <summary>
        /// 用户检测启用
        /// </summary>
        public static bool IsCheckLoginRegionScope
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings.Get("Login:IsCheckLoginRegionScope") == "1";
            }
        }

        #region 火山云日志服务
        /// <summary>
        /// 火山云日志服务
        /// </summary>
        public static string VolcanoLogUrl
        {
            get
            {
                if (CustomerConfig.CloudPlatformType == "TouTiao")
                    return ConfigurationManager.AppSettings.Get("VolcanoLog:Url") ?? "https://tls-cn-beijing.ivolces.com";
                else
                    return ConfigurationManager.AppSettings.Get("VolcanoLog:Url") ?? "https://tls-cn-beijing.volces.com";
            }
        }
        public static string VolcanoLogEndpoint
        {
            get
            {
                if (CustomerConfig.CloudPlatformType == "TouTiao")
                    return ConfigurationManager.AppSettings.Get("VolcanoLog:Endpoint") ?? "tls-cn-beijing.ivolces.com";
                else
                    return ConfigurationManager.AppSettings.Get("VolcanoLog:Endpoint") ?? "tls-cn-beijing.volces.com";
            }
        }

        /// <summary>
        /// 火山日志访问ID
        /// </summary>
        public static string VolcanoLogAppKey
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("VolcanoLog:AppKey") ??
                       "AKLTMWRiOTUxYzk4ODdkNDI0ZmJiOTU5OGM1Y2M5OTVlODk";
            }
        }
        /// <summary>
        /// 火山日志访问密钥
        /// </summary>
        public static string VolcanoLogAppSecret
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("VolcanoLog:AppSecret") ??
                       "TWpoaVlqZ3daREk0TnpkbE5EVTRNbUl5TUdRMU9HSm1OR0poWTJGbU0yUQ==";
            }
        }
        #endregion

        #region 是否开启订单同步服务
        /// <summary>
        /// 是否开启订单同步服务
        /// </summary>
        public static bool IsEnabledOrderSyncService
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledOrderSyncService");
                return value != null && Convert.ToBoolean(value);
            }
        }
        #endregion

        #region 是否开启售后单同步服务
        /// <summary>
        /// 是否开启售后单同步服务
        /// </summary>
        public static bool IsEnabledAfterSaleSyncService
        {
            get
            {
                var value = ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledAfterSaleSyncService");
                return value != null && Convert.ToBoolean(value);
            }
        }
        #endregion

        #region 是否开启紧急解绑隐藏店铺订单的开关
        /// <summary>
        /// 是否开启全局售后单订单同步服务
        /// </summary>
        public static bool IsEnabledUnbindHideOrder
        {
            get
            {
                var value = ConfigurationManager.AppSettings.Get("IsEnabledUnbindHideOrder") ?? "";
                return string.IsNullOrWhiteSpace(value) ? true : Convert.ToBoolean(value);
            }
        }
        #endregion

        #region 是否开启全局订单同步服务
        /// <summary>
        /// 是否开启全局订单同步服务
        /// </summary>
        public static bool IsEnabledGlobalOrderSyncService
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledGlobalOrderSyncService");
                return string.IsNullOrWhiteSpace(value) ? true : Convert.ToBoolean(value);

            }
        }
        #endregion

        #region 是否开启全局售后单订单同步服务
        /// <summary>
        /// 是否开启全局售后单订单同步服务
        /// </summary>
        public static bool IsEnabledGlobalAfterSaleSyncService
        {
            get
            {
                var value = ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledGlobalAfterSaleSyncService");
                return string.IsNullOrWhiteSpace(value) ? true : Convert.ToBoolean(value);

            }
        }
        #endregion

        #region 是否开启商品选择器查询v2版
        /// <summary>
        /// 是否开启商品选择器查询v2版
        /// </summary>
        public static bool IsEnabledProductSelectorSearchV2
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledProductSelectorSearchV2");
                return string.IsNullOrWhiteSpace(value) ? false : Convert.ToBoolean(value);

            }
        }
        #endregion
        /// <summary>
        /// 逻辑单笔插入阈值
        /// </summary>
        public static int LogicOrderSingleAddThreshold
        {
            get
            {
                return Convert.ToInt32(
                    ConfigurationManager.AppSettings.Get("DianGuanJia:FenXiao:LogicOrder:SingleAddThreshold") ??
                    "300");
            }
        }

        #region 是否开启商品同步服务
        /// <summary>
        /// 是否开启商品同步服务
        /// </summary>
        public static bool IsEnabledProductSyncService
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledProductSyncService");
                return value != null && Convert.ToBoolean(value);
            }
        }
        #endregion

        #region 是否开启全局商品同步服务
        /// <summary>
        /// 是否开启全局商品同步服务
        /// </summary>
        public static bool IsEnabledGlobalProductSyncService
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledGlobalProductSyncService");
                return string.IsNullOrWhiteSpace(value) ? true : Convert.ToBoolean(value);
            }
        }
        #endregion

        /// <summary>
        /// 是否开启全局数据埋点（默认开启）
        /// </summary>
        public static bool IsEnabledGlobalDataEventTracking
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledGlobalDataEventTracking");
                return string.IsNullOrWhiteSpace(value) ? true : Convert.ToBoolean(value);

            }
        }

        /// <summary>
        /// 是否开启使用新的收件人DB
        /// </summary>
        public static bool IsEnabledUseNewReceiverDb
        {
            get
            {
                var value = ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledUseNewReceiverDb");
                return string.IsNullOrWhiteSpace(value) ? false : Convert.ToBoolean(value);
            }
        }

        /// <summary>
        /// 是否指定店铺开启数据埋点（默认不指定）
        /// </summary>
        public static bool IsAssignShopEnabledDataEventTracking
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsAssignShopEnabledDataEventTracking");
                return string.IsNullOrWhiteSpace(value) ? false : Convert.ToBoolean(value);

            }
        }
        /// <summary>
        /// 是否开启数据库命令缓存
        /// </summary>
        public static bool IsEnabledDbCommandBuffered
        {
            get
            {
                var value = ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledDbCommandBuffered");
                return string.IsNullOrWhiteSpace(value) ? true : Convert.ToBoolean(value);
            }
        }

        /// <summary>
        ///  是否启用订单审核处理服务，默认启用
        /// </summary>
        public static bool IsEnableCheckRuleWinService
        {
            get
            {
                var value = ConfigurationManager.AppSettings.Get("IsEnableCheckRuleWinService") ?? "1";
                return value == "1";
            }
        }

        /// <summary>
        ///  同步服务的时候过滤跨境的店铺
        /// </summary>
        public static List<string> CrossBorderPlatformTypes
        {
            get
            {
                return new List<string> { "TikTok" };
            }
        }

        /// <summary>
        /// 是否开启复制副本任务消息推送
        /// </summary>
        public static bool IsEnabledDataDuplicationPushMessage
        {
            get
            {
                var value = ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledDataDuplicationPushMessage");
                return string.IsNullOrWhiteSpace(value) ? true : Convert.ToBoolean(value);
            }
        }
        /// <summary>
        /// 微店解密链接
        /// </summary>
        public static string WeiDianDecryptApiUrl
        {
            get
            {
                var url = System.Configuration.ConfigurationManager.AppSettings.Get("WeiDianDecryptApiUrl");
                if (string.IsNullOrEmpty(url))
                    url = IsLocalDbDebug ? "http://local5.dgjapp.com" : "http://172.26.74.230:8088";
                return url;
            }
        }

        /// <summary>
        /// 可使用抖店云的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> UseTouTiaoCloudPlatformTypes
        {
            get
            {
                return new List<string> { "TouTiao", "TouTiaoSaleShop" };
            }
        }

        /// <summary>
        /// 使用FxDbConfig作为数据库配置的云平台
        /// </summary>
        /// <returns></returns>
        public static List<string> UseFxDbConfigCloudPlatformTypes
        {
            get
            {
                return new List<string> { "TouTiao", "ChinaAliyun", "Pinduoduo" };
            }
        }

        /// <summary>
        /// 增量同步间隔时间（天）（同步进度也需要用到显示弹框）
        /// </summary>
        /// <param name="pt"></param>
        /// <param name="isFullSync"></param>
        /// <returns></returns>
        public static double GetPlatformSyncDiffTime(string pt, bool isFullSync = false)
        {
            if (pt == "KuaiShou")
                return 0.1;
            else if (pt == "XiaoHongShu")
                //增量同步不能超过30分钟，创建时间全量同步不能超过24小时
                return isFullSync ? 1 : 0.5 / 24; 
            else if (pt == "Alibaba")
                return 0.25 / 2;
            else if (pt == "Other_Heliang" || pt == "Other_JuHaoMai" || pt == "Other_HaoYouDuo") //禾量的接口是按天同步的
                return 1;
            else
                return 0.25;
        }
        

        /// <summary>
        /// 需要处理1688加密代发的订单来源，返回最终结果为准
        /// 参考：
        /// 淘宝-thyny，天猫-tm，淘特-taote，阿里巴巴C2M-c2m，京东-jingdong，拼多多-pinduoduo，微信-weixin，跨境-kuajing，快手-kuaishou，有赞-youzan，抖音-douyin，寺库-siku，美团团好货-meituan，小红书-xiaohongshu，当当-dangdang，苏宁-suning，大V店-davdian，行云-xingyun，蜜芽-miya，菠萝派商城-boluo，其他-other
        /// </summary>
        public static List<string> Need1688OutChannels
        {
            get
            {
                var channelConfig = ConfigurationManager.AppSettings.Get("Need1688OutChannel");
                var channelStrs = string.IsNullOrWhiteSpace(channelConfig) ? "douyin,kuaishou,thyny,pinduoduo,taote,tm,xiaohongshu" : channelConfig;
                if (IsDebug)
                    channelStrs += ",other";
                var channels = channelStrs.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList();
                return channels;
            }
        }

        /// <summary>
        /// 推送触发消息，查询变更日志每批查询数量，默认1000
        /// </summary>
        /// <returns></returns>
        public static int PushMessageQueryPageSize
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("PushMessageQueryPageSize").ToInt();
                v = v <= 0 ? 1000 : v;
                return v;
            }
        }
        /// <summary>
        /// 推送触发消息，查询变更日志最大查询次数，默认100
        /// </summary>
        /// <returns></returns>
        public static int PushMessageQueryBatchNum
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("PushMessageQueryBatchNum").ToInt();
                v = v <= 0 ? 100 : v;
                return v;
            }
        }
        /// <summary>
        /// 推送触发消息，数据库列表，默认为空
        /// </summary>
        /// <returns></returns>
        public static List<int> PushMessageDbNameConfigIds
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("PushMessageDbNameConfigIds") ?? "";
                if (string.IsNullOrEmpty(v))
                    return new List<int>() { };

                v = v.Replace("，", ",");
                v = v.Replace(";", ",");
                v = v.Replace("；", ",");
                var result = v.Split(',').Select(x => x.ToInt()).Distinct().ToList();
                return result;
            }
        }

        /// <summary>
        /// 补偿推送Top数，默认1000
        /// </summary>
        public static int CompensatePushMessageTopNum
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensatePushMessageTopNum").ToInt();
                v = v <= 0 ? 1000 : v;
                return v;
            }
        }

        /// <summary>
        /// 距上次同步时间（分钟），默认180
        /// </summary>
        public static int CompensatePushMessageMinutes
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensatePushMessageMinutes").ToInt();
                v = v <= 0 ? 180 : v;
                return v;
            }
        }

        /// <summary>
        /// 最大剩余数，默认50
        /// </summary>
        public static int CompensatePushMessageMaxRemainders
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensatePushMessageMaxRemainders").ToInt();
                v = v <= 0 ? 50 : v;
                return v;
            }
        }

        /// <summary>
        /// 补偿推送开始时间点，默认05:00
        /// </summary>
        /// <returns></returns>
        public static DateTime CompensatePushMessageStartTime
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensatePushMessageStartTime") ?? "05:00";
                return ($"2023-01-01 {v}").toDateTime();
            }
        }
        /// <summary>
        /// 补偿推送结束点，默认01:00
        /// </summary>
        /// <returns></returns>
        public static DateTime CompensatePushMessageEndTime
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensatePushMessageEndTime") ?? "01:00";
                return ($"2023-01-01 {v}").toDateTime();
            }
        }

        /// <summary>
        /// 补偿推送心跳时间间隔（默认120秒）
        /// </summary>
        /// <returns></returns>
        public static int CompensatePushMessageElapsedSeconds
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensatePushMessageElapsedSeconds").ToInt();
                if (v <= 0)
                    v = 120;
                return v;
            }
        }

        /// <summary>
        /// 补偿推送类型（2LogicOrder\4AfterSaleOrder\6Product\20PurchaseOrderRelation）
        /// </summary>
        /// <returns></returns>
        public static List<int> CompensatePushMessageDataChangeTableTypeNames
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensatePushMessageDataChangeTableTypeNames");
                if (string.IsNullOrEmpty(v))
                    return new List<int>() { 2 };

                v = v.Replace("，", ",");
                v = v.Replace(";", ",");
                v = v.Replace("；", ",");
                var result = v.Split(',').Select(x => x.ToInt()).Distinct().ToList();
                return result;
            }
        }

        /// <summary>
        /// 被动推送心跳时间间隔（默认5秒）
        /// </summary>
        /// <returns></returns>
        public static int PushMessageElapsedSeconds
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("PushMessageElapsedSeconds").ToInt();
                if (v <= 0)
                    v = 5;
                return v;
            }
        }

        /// <summary>
        /// 补偿推送回流消息-心跳时间间隔（默认120秒）
        /// </summary>
        /// <returns></returns>
        public static int PushSendReturnRecordToMessageElapsedSeconds
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("PushSendReturnRecordToMessageElapsedSeconds").ToInt();
                if (v <= 0)
                    v = 120;
                return v;
            }
        }

        /// <summary>
        /// 补偿推送回流消息-查询时间（默认24小时）
        /// </summary>
        /// <returns></returns>
        public static int PushSendReturnRecordToMessageHours
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("PushSendReturnRecordToMessageHours").ToInt();
                if (v <= 0)
                    v = 24;
                return v;
            }
        }

        /// <summary>
        /// 补偿采购单关联数据及Sku关联数据(默认5分钟)
        /// </summary>
        /// <returns></returns>
        public static int CompensatePurchaseOrderAndSkuMappingMinutes
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensatePurchaseOrderAndSkuMappingMinutes").ToInt();
                if (v <= 0)
                    v = 5;
                return v;
            }
        }

        /// <summary>
        /// 清理变更日志天数，默认20
        /// </summary>
        /// <returns></returns>
        public static int ClearDataChangeLogDays
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("ClearDataChangeLogDays").ToInt();
                v = v <= 0 ? 6 : v;
                return v;
            }
        }
        /// <summary>
        /// 清理变更日志每次睡眠时间（ms），默认50
        /// </summary>
        /// <returns></returns>
        public static int ClearDataChangeLogSleepMS
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("ClearDataChangeLogSleepMS").ToInt();
                v = v <= 0 ? 50 : v;
                return v;
            }
        }

        /// <summary>
        /// 清理变更日志天数，每次删除数量，默认1000
        /// </summary>
        /// <returns></returns>
        public static int ClearDataChangeLogPageSize
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("ClearDataChangeLogPageSize").ToInt();
                v = v <= 0 ? 1000 : v;
                return v;
            }
        }
        /// <summary>
        /// 清理变更日志开始时间点，默认00:00
        /// </summary>
        /// <returns></returns>
        public static DateTime ClearDataChangeLogStartTime
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("ClearDataChangeLogStartTime") ?? "00:00";
                return ($"2023-01-01 {v}").toDateTime();
            }
        }
        /// <summary>
        /// 清理变更日志结束点，默认05:00
        /// </summary>
        /// <returns></returns>
        public static DateTime ClearDataChangeLogEndTime
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("ClearDataChangeLogEndTime") ?? "05:00";
                return ($"2023-01-01 {v}").toDateTime();
            }
        }
        /// <summary>
        /// 更新库存统计开始时间，默认00:30
        /// </summary>
        /// <returns></returns>
        public static DateTime UpdateStockChangeStatStartTime
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("UpdateStockChangeStatStartTime") ?? "00:30";
                return ($"2023-01-01 {v}").toDateTime();
            }
        }
        /// <summary>
        /// 更新库存统计结束时间点，默认01:00
        /// </summary>
        /// <returns></returns>
        public static DateTime UpdateStockChangeStatEndTime
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("UpdateStockChangeStatEndTime") ?? "01:30";
                return ($"2023-01-01 {v}").toDateTime();
            }
        }
        /// <summary>
        /// 回流监控心跳时间间隔（默认2分钟）
        /// </summary>
        /// <returns></returns>
        public static int SendHistoryReturnRecordMonitorMinute
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("SendHistoryReturnRecordMonitorMinute").ToInt();
                if (v <= 0)
                    v = 2;
                return v;
            }
        }

        /// <summary>
        /// 清理DataSyncStatus数据服务间隔（秒）
        /// </summary>
        public static int ClearDataSyncStatusElapsedSeconds
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("ClearDataSyncStatusElapsedSeconds").ToInt();
                if (v <= 0)
                    v = 3600;
                return v;
            }
        }
        
        /// <summary>
        /// 统计链路日志导出SLS的开始时间，24小时制，例如凌晨3点的值是：03
        /// </summary>
        public static string QueryOpenTelemetryLogExportStartHH
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("QueryOpenTelemetryLogExportStartHH") ?? "03";
            }
        }

        /// <summary>
        /// 需要统计链路日志导出SLS的环境版本，多个用逗号隔开，值：Online（正式），Gray1（灰度1），Gray3,Test1（1t测试环境）,Test3
        /// </summary>
        public static string QueryOpenTelemetryLogExportEnvironment
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("QueryOpenTelemetryLogExportEnvironment") ?? "Gray1,Gray3,Gray6,Test3,Online";
                //return ConfigurationManager.AppSettings.Get("QueryOpenTelemetryLogExportEnvironment") ?? "Development";
            }
        }

        public static string QueryOpenTelemetryLogExportProject
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("QueryOpenTelemetryLogExportProject") ?? "proj-xtrace-be935fc58d1dbab3de839c911f69de-cn-zhangjiakou";
            }
        }

        public static string QueryOpenTelemetryLogExportLogstore
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("QueryOpenTelemetryLogExportLogstore") ?? "logstore-tracing-clickhousedata";
            }
        }

		/// <summary>
		/// StatSendHistoryOrderCountWinService数据服务间隔（秒）
		/// </summary>
		public static int StatSendHistoryOrderCountElapsedSeconds
		{
			get
			{
				var v = ConfigurationManager.AppSettings.Get("StatSendHistoryOrderCountElapsedSeconds").ToInt();
				if (v <= 0)
					v = 1200;
				return v;
			}
		}

		/// <summary>
		/// StatSendHistoryOrderCountWinService服务执行时间，格式：开始时间,结束时间
		/// </summary>
		public static string StatSendHistoryOrderCountStartTime
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("StatSendHistoryOrderCountStartTime") ?? "01:00,05:00";
            }
        }

        /// <summary>
        /// 统计发货订单数量查询发货数据的店铺数据时间,需整点，例：30 60 120 180 240 480
        /// </summary>
		public static int StatSendHistoryOrderCountSelectMinuteToUser
		{
			get
			{
				var v = ConfigurationManager.AppSettings.Get("StatSendHistoryOrderCountSelectMinuteToUser").ToInt();
				if (v <= 0)
					v = 240;
				return v;
			}
		}

		/// <summary>
		///  统计发货订单数量 SLS Project
		/// </summary>
		public static string StatSendHistoryOrderCountSlsProject
		{
			get
			{
                return ConfigurationManager.AppSettings.Get("StatSendHistoryOrderCountSlsProject") ?? "fendan-system-log";
			}
		}

		/// <summary>
		///  统计发货订单数量 SLS Logstore
		/// </summary>
		public static string StatSendHistoryOrderCountSlsLogstore
		{
			get
			{
				return ConfigurationManager.AppSettings.Get("StatSendHistoryOrderCountSlsLogstore") ?? "fx-send-report";
			}
		}

		/// <summary>
		/// 统计发货订单数量、活跃用户数 推送飞书URL
		/// </summary>
		public static string StatCountFeiShuUrl
		{
			get
			{
				return ConfigurationManager.AppSettings.Get("StatCountFeiShuUrl") ?? "https://open.feishu.cn/open-apis/bot/v2/hook/dac686f4-4058-41b3-a0ca-a405999485c4";
			}
		}

		/// <summary>
		/// 统计发货订单数量服务的并发数
		/// </summary>
		public static int StatSendHistoryOrderCountDbMaxDegree
		{
			get
			{
				var v = ConfigurationManager.AppSettings.Get("StatSendHistoryOrderCountDbMaxDegree").ToInt();
				if (v <= 0)
					v = 5;
				return v;
			}
		}

		/// <summary>
		/// StatActiveUserCountWinServices数据服务间隔（秒）
		/// </summary>
		public static int StatActiveUserCountWinServicesElapsedSeconds
		{
			get
			{
				var v = ConfigurationManager.AppSettings.Get("StatActiveUserCountWinServicesElapsedSeconds").ToInt();
				if (v <= 0)
					v = 1200;
				return v;
			}
		}

		/// <summary>
		/// StatActiveUserCount服务执行时间，格式：开始时间,结束时间
		/// </summary>
		public static string StatActiveUserCountStartTime
		{
			get
			{
				return ConfigurationManager.AppSettings.Get("StatActiveUserCountStartTime") ?? "01:00,05:00";
			}
		}

		/// <summary>
		///  统计活跃用户数量 SLS Project
		/// </summary>
		public static string StatActiveUserCountSlsProject
		{
			get
			{
				return ConfigurationManager.AppSettings.Get("StatActiveUserCountSlsProject") ?? "fendan-system-log";
			}
		}

		/// <summary>
		///  统计活跃用户数量  SLS Logstore
		/// </summary>
		public static string StatActiveUserCountSlsLogstore
		{
			get
			{
				return ConfigurationManager.AppSettings.Get("StatActiveUserCountSlsLogstore") ?? "fx-user-report";
			}
		}

		/// <summary>
		/// 统计活跃用户数量的查询时间,需整点，例：30 60 120 180 240 480
		/// </summary>
		public static int StatActiveUserCountSelectMinute
		{
			get
			{
				var v = ConfigurationManager.AppSettings.Get("StatActiveUserCountSelectMinute").ToInt();
				if (v <= 0)
					v = 1440;
				return v;
			}
		}

		/// <summary>
		/// 统计活跃店铺的并发数
		/// </summary>
		public static int StatActiveUserCountDbMaxDegree
		{
			get
			{
				var v = ConfigurationManager.AppSettings.Get("StatActiveUserCountDbMaxDegree").ToInt();
				if (v <= 0)
					v = 5;
				return v;
			}
		}

		/// <summary>
		/// 1688采购单数据监控服务
		/// </summary>
		/// <returns></returns>
		public static int PurchaseOrderDataMonitorMinute
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("PurchaseOrderDataMonitorMinute").ToInt();
                if (v <= 0)
                    v = 5;
                return v;
            }
        }

        /// <summary>
        /// 回流监控预警数，Queue1至Queue5合计数量超过此值时预警（默认2）
        /// </summary>
        /// <returns></returns>
        public static int SendHistoryReturnRecordMonitorWarnNum
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("SendHistoryReturnRecordMonitorWarnNum").ToInt();
                if (v <= 0)
                    v = 2;
                return v;
            }
        }
        /// <summary>
        /// 站内信每天清除过期信息
        /// </summary>
        /// <returns></returns>
        public static Tuple<int, int, int> SiteMessageClearMonitorHour
        {
            get
            {
                var h = ConfigurationManager.AppSettings.Get("SiteMessageClearMonitorHour").ToInt();
                var m = ConfigurationManager.AppSettings.Get("SiteMessageClearMonitorMinute").ToInt();
                var s = ConfigurationManager.AppSettings.Get("SiteMessageClearMonitorSecond").ToInt();

                if (h <= 0)
                    h = 2;
                if (m <= 0)
                    m = 0;
                if (s <= 0)
                    s = 0;
                return Tuple.Create(h, s, s);
            }
        }
        /// <summary>
        /// 每个库可分配上限订单数，默认20W
        /// </summary>
        /// <returns></returns>
        public static int DbMaxOrderCount
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DbMaxOrderCount").ToInt();
                v = v <= 0 ? 20 * 10000 : v;
                return v;
            }
        }

		/// <summary>
		/// 统计链路日志导出SLS的轮询时间，单位：分钟
		/// </summary>
		public static string QueryOpenTelemetryLogExportStartMinute
		{
			get
			{
				return ConfigurationManager.AppSettings.Get("QueryOpenTelemetryLogExportStartMinute") ?? "5";
			}
		}


		/// <summary>
		/// 分单系统登录Cookie键名
		/// </summary>
		public static string FxSystemLoginCookieKey = "DGJFXLT_$id";
        /// <summary>
        /// 分单系统双重验证Key名
        /// </summary>
        public static string FxSystemIsOpenDoubleAuthKey = "/FxSystem/IsOpenDoubleAuth";
        /// <summary>
        /// 分单系统登录Cookie过期时间配置Key名，小时，double型
        /// </summary>
        public static string FxSystemLoginExpireHours = "/FxSystem/LoginExpireHours";
        /// <summary>
        /// 统计店铺订单数量-状态，默认:waitsellersend
        /// </summary>
        public static string StatOrderStatus
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("StatOrderStatus") ?? "waitsellersend";
                return v;
            }
        }
        /// <summary>
        /// 允许厂家所在的抖店云实例Id，默认实例2\3\4，即Id=46,47,48
        /// </summary>
        public static List<int> SupplierDbServerIds
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("SupplierDbServerIds");
                if (string.IsNullOrEmpty(v))
                    return new List<int>() { 46, 47, 48 };

                v = v.Replace("，", ",");
                v = v.Replace(";", ",");
                v = v.Replace("；", ",");
                var result = v.Split(',').Select(x => x.ToInt()).Distinct().ToList();
                return result;
            }
        }

        /// <summary>
        /// 是否应用抖音open_address_id配置Key名称
        /// </summary>
        public static string FxSystemIsUseDyOpenAddressIdKey = "/FxSystem/IsUseDyOpenAddressId";


        /// <summary>
        /// 是否需要检查厂家所在实例，默认true
        /// </summary>
        public static bool IsNeedCheckSupplierDbServerId
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("IsNeedCheckSupplierDbServerId");
                if (string.IsNullOrEmpty(v))
                    return false;

                return v == "1" || v == "true";
            }
        }

        /// <summary>
        ///  Redis缓存开关配置的键值，默认空为：/System/Config/FenDan/FxCachingSetting/
        /// </summary>
        public static string FxCachingSettingKey
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("FxCaching:FxCachingSettingKey").ToStringExt("/System/Config/FenDan/FxCachingSetting/");
            }
        }

        /// <summary>
        /// 批量添加迁移任务时，指定的源库Id，默认为null，精选一区:11057,精选二区:11122,精选三区:11179
        /// </summary>
        public static List<int> SourceDbNameConfigIds
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("SourceDbNameConfigIds");
                if (string.IsNullOrEmpty(v))
                    return new List<int>() { };

                v = v.Replace("，", ",");
                v = v.Replace(";", ",");
                v = v.Replace("；", ",");
                var result = v.Split(',').Select(x => x.ToInt()).Distinct().ToList();
                return result;
            }
        }

        /// <summary>
        /// 批量添加迁移任务时，指定的目标库Id，默认为空
        /// </summary>
        public static List<int> TargetDbNameConfigIds
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("TargetDbNameConfigIds");
                if (string.IsNullOrEmpty(v))
                    return new List<int>();

                v = v.Replace("，", ",");
                v = v.Replace(";", ",");
                v = v.Replace("；", ",");
                var result = v.Split(',').Select(x => x.ToInt()).Distinct().ToList();
                return result;
            }
        }

        /// <summary>
        /// 调试模式固定值验证码
        /// </summary>
        public static string DebugValidCode
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DebugValidCode");
                if (string.IsNullOrEmpty(v))
                    v = "DVCiZ8vb28ugrZ";
                return v;
            }
        }
        /// <summary>
        /// 同步服务环境（默认为空，正式环境）(灰度：Gray，测试：Test)
        /// </summary>
        public static string SyncServiceEnvironment
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DianGuanJiaApp:SyncService:Environment") ?? string.Empty;
                return v;
            }
        }

        public static string DataDuplicationEnvironment
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DianGuanJiaApp:DataDuplication:Environment") ??
                        "production";
                return v;
            }
        }

        /// <summary>
        /// 是否写迁移云日志
        /// </summary>
        public static bool IsEnabledMigrateWriteCloudLog
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledMigrateWriteCloudLog") ?? "true";
                return string.IsNullOrWhiteSpace(value) ? true : Convert.ToBoolean(value);
            }
        }

        /// <summary>
        /// 结算价导入达到多少走任务
        /// </summary>
        public static int ImporSettlementPriceCountOfTask
        {
            get
            {
                var count = ConfigurationManager.AppSettings.Get("Preordain:DianGuanJia:ImporSettlementPriceCountOfTask")?.ToInt() ?? 1000;
                return count;
            }
        }

        /// <summary>
        /// 批量添加迁移任务时，指定的源库Id，默认为12332, 12390
        /// </summary>
        public static List<int> SameCloudSourceDbNameConfigIds
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("SameCloudSourceDbNameConfigIds");
                if (string.IsNullOrEmpty(v))
                    return new List<int>() { 12332, 12390 };

                v = v.Replace("，", ",");
                v = v.Replace(";", ",");
                v = v.Replace("；", ",");
                var result = v.Split(',').Select(x => x.ToInt()).Distinct().ToList();
                return result;
            }
        }
        /// <summary>
        /// 抖店云内迁移，可选目标库Id，默认实例8，9部分库
        /// </summary>
        public static string SameCloudTargetDbNameConfigIdList
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("SameCloudTargetDbNameConfigIdList");
                if (string.IsNullOrEmpty(v))
                    v = "12419,12436,12424,12422,12416,12442,12418,12440,12421,12423,12455,12454,12453,12452,12451,12450,12449,12448,12447,12446";
                return v;
            }
        }
        /// <summary>
        /// 可选目标库Id，默认实例6，7的所有库
        /// </summary>
        public static string TargetDbNameConfigIdList
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("TargetDbNameConfigIdList");
                if (string.IsNullOrEmpty(v))
                    v = "12356,12357,12358,12359,12360,12361,12362,12363,12364,12365,12366,12367,12368,12369,12370,12371,12372,12373,12374,12375,12376,12377,12378,12379,12380,12381,12382,12383,12384,12385,12386,12387,12388,12389,12390,12391,12392,12393,12394,12395,12396,12397,12398,12399,12400,12401,12402,12403,12404,12405,12406,12407,12408,12409,12410,12411,12412,12413,12414,12415";
                return v;
            }
        }

        /// <summary>
        /// 有效的合作关系状态（绑定成功+解绑处理中+解绑失败）
        /// </summary>
        public static List<int> AgentBingSupplierSuccessStatus
        {
            get
            {
                //1=绑定成功， 2=申请绑定中， 3=绑定失败， 4=已取消， 5=解绑处理中，6=解绑失败
                return new List<int> { 1, 5, 6 };
            }
        }

        /// <summary>
        /// 支持1688代销的平台，默认：TouTiao,Taobao,KuaiShou,XiaoHongShu,Pinduoduo
        /// </summary>
        public static List<string> Fx1688SupportPlatformTypes
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("Fx1688SupportPlatformTypes");
                if (string.IsNullOrEmpty(v))
                    v = "TouTiao,Taobao,KuaiShou,XiaoHongShu,Pinduoduo";

                v = v.Replace("，", ",");
                v = v.Replace(";", ",");
                v = v.Replace(";", ",");
                v = v.Replace(" ", "");

                return v.Split(',').ToList();
            }
        }

        /// <summary>
        /// 需要前置发1688侧订单的平台，默认：Taobao
        /// </summary>
        public static List<string> NeedPreSendSourcePlatformTypes
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("NeedPreSendSourcePlatformTypes");
                if (string.IsNullOrEmpty(v))
                    v = "Taobao";

                v = v.Replace("，", ",");
                v = v.Replace(";", ",");
                v = v.Replace(";", ",");
                v = v.Replace(" ", "");

                return v.Split(',').ToList();
            }
        }

        /// <summary>
        /// 平台类型转换成阿里巴巴Channel
        /// 淘宝-thyny，天猫-tm，淘特-taote，阿里巴巴C2M-c2m，京东-jingdong，拼多多-pinduoduo，微信-weixin，跨境-kuajing，快手-kuaishou，有赞-youzan，抖音-douyin，寺库-siku，美团团好货-meituan，小红书-xiaohongshu，当当-dangdang，苏宁-suning，大V店-davdian，行云-xingyun，蜜芽-miya，菠萝派商城-boluo，快团团-kuaituantuan，其他-other
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static string PlatformTypeToAlibabaChannel(string platformType)
        {
            platformType = platformType.ToString2().ToLower();

            //原名称返回
            var platList = new List<string> { "taote", "jingdong", "pinduoduo", "weixin", "kuajing", "kuaishou", "youzan", "douyin", "siku", "meituan", "xiaohongshu", "dangdang", "suning", "davdian", "xingyun", "miya", "boluo", "kuaituantuan" };
            if (platList.Contains(platformType))
                return platformType;

            //转换
            string result;
            switch (platformType)
            {
                case "taobao":
                    result = "thyny";
                    break;
                case "alibabac2m":
                    result = "c2m";
                    break;
                case "toutiao":
                case "toutiaofenfa":
                case "toutiaogongyingshang":
                    result = "douyin";
                    break;
                case "kuaishousupplier":
                    result = "kuaishou";
                    break;
                case "wxvideo":
                case "wxxiaoshangdian":
                    result = "weixin";
                    break;
                default:
                    result = "other";
                    break;
            }
            return result;
        }

        /// <summary>
        /// 店管家消息接收URL
        /// </summary>
        public static string DgjMessageReceiveUrl
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DgjMessageReceiveUrl") ?? "http://queue.dgjapp.com/Message/ReceiveFromDgj";
                return v;
            }
        }

        /// <summary>
        /// 店管家自动申请退款消息接收URL（不加密）
        /// </summary>
        public static string DgjAutoAfterSaleUrl
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DgjAutoAfterSaleUrl") ?? "http://queue.dgjapp.com/Message/DgjAutoAfterSale";
                return v;
            }
        }

        /// <summary>
        /// 店管家自动申请退款消息接收URL（加密版）
        /// </summary>
        public static string DgjAutoAfterSaleEncryptUrl
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DgjAutoAfterSaleEncryptUrl") ?? "http://queue.dgjapp.com/Message/DgjAutoAfterSaleEncrypt";
                return v;
            }
        }

        /// <summary>
        /// 店管家自动回流消息接收URL（不加密）
        /// </summary>
        public static string DgjSendHistoryReturnUrl
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DgjSendHistoryReturnUrl") ?? "http://queue.dgjapp.com/Message/DgjSendHistoryReturn";
                return v;
            }
        }


        /// <summary>
        /// 店管家自动回流消息接收URL（加密版）
        /// </summary>
        public static string DgjSendHistoryReturnEncryptUrl
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DgjSendHistoryReturnEncryptUrl") ?? "http://queue.dgjapp.com/Message/DgjSendHistoryReturnEncrypt";
                return v;
            }
        }

        /// <summary>
        /// 云消息中转URL
        /// </summary>
        public static string CloudMessageTransferUrl
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CloudMessageTransferUrl") ?? "http://queue.dgjapp.com/api/MessageTransfer";
                return v;
            }
        }

        /// <summary>
        /// 拼多多消息接收URL
        /// </summary>
        public static string PddMessageUrl
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("PddMessageUrl") ?? "https://pddmessage.dgjapp.com/Message/CloudMessage";
                return v;
            }
        }

        /// <summary>
        /// 精选云消息接收域名
        /// </summary>
        public static string AlibabaMessageDomain
        {
            get
            {
                //https
                var v = ConfigurationManager.AppSettings.Get("AlibabaMessageDomain") ?? "https://message.dgjapp.com";
                return v;
            }
        }

        /// <summary>
        /// 精选云消息接收域名[在拼多多云发起]
        /// </summary>
        public static string AlibabaMessageDomainForPdd
        {
            get
            {
                //http
                var v = ConfigurationManager.AppSettings.Get("AlibabaMessageDomainForPdd") ?? "http://queue.dgjapp.com";
                return v;
            }
        }

        /// <summary>
        /// 抖店云消息接收域名
        /// </summary>
        public static string ToutiaoMessageDomain
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("ToutiaoMessageDomain") ?? "https://dmessage.dgjapp.com";
                return v;
            }
        }

        /// <summary>
        /// 拼多多云消息接收域名
        /// </summary>
        public static string PddMessageDomain
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("PddMessageDomain") ?? "https://pddmessage.dgjapp.com";
                return v;
            }
        }

        /// <summary>
        /// 京东云消息接收域名
        /// </summary>
        public static string JdMessageDomain
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("JdMessageDomain") ?? "https://jdmessage.dgjapp.com";
                return v;
            }
        }

        //目标云的基础商品消息接收URL
        public static string GetMessageTargetUrl(string targetCloud)
        {
            targetCloud = targetCloud.ToString2().ToLower();
            var targetUrl = AlibabaMessageDomain;
            if (targetCloud == "toutiao")
                targetUrl = ToutiaoMessageDomain;
            else if (targetCloud == "pinduoduo")
                targetUrl = PddMessageDomain;
            else if (targetCloud == "jingdong" || targetCloud.ToLower() == "jingdongpurchase")
                targetUrl = JdMessageDomain;

            //当前为拼多多云，只能用固定域名
            if (CloudPlatformType == "Pinduoduo")
                targetUrl = AlibabaMessageDomainForPdd;

            targetUrl = targetUrl.TrimEnd('/') + "/Message/BaseProductMessage";

            return targetUrl;
        }

        //目标云的站点URL
        public static string GetTargetSiteUrl(string targetCloud)
        {
            targetCloud = targetCloud.ToString2().ToLower();
            var targetUrl = DefaultFenFaSystemUrl;
            if (targetCloud == "toutiao")
                targetUrl = ToutiaoFenFaSystemUrl;
            else if (targetCloud == "pinduoduo")
                targetUrl = PinduoduoFenFaSystemUrl;
            else if (targetCloud == "jingdong")
                targetUrl = JingdongFenFaSystemUrl;
            else if (targetCloud == "alibaba")
                targetUrl = AlibabaFenFaSystemUrl;

            return targetUrl;
        }

        /// <summary>
        /// 采购订单超过x分钟自动取消（默认46小时=46*60分钟）
        /// </summary>
        /// <returns></returns>
        public static int AutoCancelPurchaseOrderMinutes
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("AutoCancelPurchaseOrderMinutes").ToInt();
                if (v <= 0)
                    v = 46 * 60;
                ////测试环境10分钟
                //if (IsDebug)
                //    v = 10;
                return v;
            }
        }

        public static bool IsGlobalUseNewMergerSQL
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("IsGlobalUseNewMergerSQL")?.ToBool() ?? true;
                return v;
            }
        }

        #region 是否开启当前账号电子面单检测
        /// <summary>
        /// 是否开启当前账号电子面单检测，默认开启
        /// </summary>
        public static bool IsEnabledCheckEbillOpenStatus
        {
            get
            {
                var value = System.Configuration.ConfigurationManager.AppSettings.Get(
                    "DianGuanJia:IsEnabledCheckEbillOpenStatus");
                return string.IsNullOrWhiteSpace(value) ? true : Convert.ToBoolean(value);

            }
        }
        #endregion

        /// <summary>
        /// 拼多多合单时，是否查询合单标签，默认true
        /// 当版本全部更新，且历史数据更新后，需设置为0
        /// </summary>
        public static bool IsPinduoduoQueryNoMergerTag
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("IsPinduoduoQueryNoMergerTag");
                if (string.IsNullOrEmpty(v))
                    return true;

                return v == "1" || v == "true";
            }
        }

        /// <summary>
        /// 热数据订单状态
        /// </summary>
        public static List<string> HotOrderStatus = new List<string>() { "waitbuyerpay", "waitaudit", "waitsellersend", "inrefund", "all" };

        /// <summary>
        /// 限制翻页深度页数
        /// </summary>
        public static string PageMaxSize
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("PageMaxSize") ?? "500";
            }
        }

        /// <summary>
        /// 平台类型
        /// </summary>
        public static List<string> AllCloudPts
        {
            get
            {
                return _allCloudPts;
            }
        }

        public static string GetPtName(string targetCloud)
        {
            targetCloud = targetCloud.ToString2().ToLower();
            var ptName = string.Empty;
            switch (targetCloud)
            {
                case "toutiao":
                    ptName = "抖店";
                    break;
                case "pinduoduo":
                    ptName = "拼多多";
                    break;
                case "jingdong":
                    ptName = "京东";
                    break;
                case "alibaba":
                    ptName = "精选平台";
                    break;
                default:
                    ptName = "精选平台";
                    break;
            }
            return ptName;
        }

        public static string GetPtUrl(string targetCloud)
        {
            targetCloud = targetCloud.ToString2().ToLower();
            var ptUrl = string.Empty;
            switch (targetCloud)
            {
                case "toutiao":
                    ptUrl = ToutiaoFenFaSystemUrl;
                    break;
                case "pinduoduo":
                    ptUrl = PinduoduoFenFaSystemUrl;
                    break;
                case "jingdong":
                    ptUrl = JingdongFenFaSystemUrl;
                    break;
                case "alibaba":
                    ptUrl = AlibabaFenFaSystemUrl;
                    break;
                default:
                    ptUrl = AlibabaFenFaSystemUrl;
                    break;
            }
            return ptUrl;
        }
        
        #region 阿里云验证码配置

        /// <summary>
        /// 阿里云验证码AccessKeyId
        /// </summary>
        public static string AlibabaCaptchaAccessKeyId => ConfigurationManager.AppSettings.Get("DianGuanJiaApp:AlibabaCaptcha:AccessKeyId") ?? AlidayuAccessKeyId;

        /// <summary>
        /// 阿里云验证码AccessKeySecret
        /// </summary>
        public static string AlibabaCaptchaAccessKeySecret => ConfigurationManager.AppSettings.Get("DianGuanJiaApp:AlibabaCaptcha:AccessKeySecret") ?? AlidayuAccessKeySecret;

        /// <summary>
        /// 阿里云验证码Endpoint
        /// </summary>
        public static string AlibabaCaptchaEndpoint => ConfigurationManager.AppSettings.Get("DianGuanJiaApp:AlibabaCaptcha:Endpoint") ?? "captcha.cn-shanghai.aliyuncs.com";
        
        /// <summary>
        /// 场景Id（拼图类型）
        /// </summary>
        public static string AlibabaCaptchaSceneId => ConfigurationManager.AppSettings.Get("DianGuanJiaApp:AlibabaCaptcha:SceneId") ?? "a2hyrlfd";

        #endregion

        #region TouTiaoTos OOS 存储
        /// <summary>
        /// AccessKeyId
        /// </summary>
        public static string TouTiaoTosOSSAK
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("DianGuanJiaApp:TouTiaoTosS:AccessKeyId") ?? "AKLTMWRiOTUxYzk4ODdkNDI0ZmJiOTU5OGM1Y2M5OTVlODk";
            }

        }
        /// <summary>
        /// AccessKeySecret
        /// </summary>
        public static string TouTiaoTosOSSAKS
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("DianGuanJiaApp:TouTiaoTosS:AccessKeySecret") ?? "TWpoaVlqZ3daREk0TnpkbE5EVTRNbUl5TUdRMU9HSm1OR0poWTJGbU0yUQ==";
            }

        }
        /// <summary>
        /// 访问域名 注内网 tos-cn-beijing.ivolces.com/公网tos-cn-beijing.volces.com
        /// </summary>
        public static string TouTiaoTosEndpoint
        {
            get
            {
                ///外网访问//注：桶私有
                return ConfigurationManager.AppSettings.Get("DianGuanJiaApp:TouTiaoTosS:Endpoint") ?? "tos-cn-beijing.volces.com";
            }

        }
        /// <summary>
        /// 地域
        /// </summary>
        public static string TouTiaoTosRegion
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("DianGuanJiaApp:TouTiaoTosS:Region") ?? "cn-beijing";
            }

        }
        #endregion

        /// <summary>
        /// 根据业务返回对应的存储桶
        /// </summary>
        /// <param name="platform">平台</param>
        /// <param name="businType">业务类型：AfterSale、BaseProduct</param>
        /// <returns></returns>
        public static string GetBucketNameByBusinessType(string platform, string businType)
        {
            string bucketName = string.Empty;
            try
            {
                //默认配置存储桶JSon配置
                var defaultJsonValue = "{\"Alibaba\":{\"AfterSale\":\"fx-aftersale\",\"BaseProduct\":\"fx-baseproduct\",\"Order\":\"fxali-order\"},\"TouTiao\":{\"AfterSale\":\"fx-aftersale\",\"BaseProduct\":\"fx-baseproduct\",\"Order\":\"fx-order\"},\"Pinduoduo\":{\"AfterSale\":\"fx-aftersale\",\"BaseProduct\":\"fx-baseproduct\",\"Order\":\"fx-order\"}}";
                string jsonFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory + "Files\\JsonFiles", "ObjectBucket.json");

                var jsonContent = File.Exists(jsonFilePath) ? File.ReadAllText(jsonFilePath) : defaultJsonValue;

                // 解析JSON字符串到Dictionary对象
                Dictionary<string, Dictionary<string, string>> jsonData =
                    JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, string>>>(jsonContent);
                // 根据平台和字段获取值
                if (jsonData.TryGetValue(platform, out Dictionary<string, string> platformData) &&
                    platformData.TryGetValue(businType, out string fieldValue))
                {
                    bucketName = fieldValue;
                }
                ///存在未找到平台或对应业务桶赋予默认值
                bucketName = string.IsNullOrWhiteSpace(bucketName) ? "fx-aftersale" : bucketName;///赋值默认值分销存储桶
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取存储桶：平台：{platform},业务类型：{businType},出现异常:{ex.Message}", "OssStorageLog.txt");
            }

            return bucketName;
        }
        /// <summary>
        /// 获取测试环境下最大熔断订单数
        /// </summary>
        public static int GetDebugMaxSyncOrderCount
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("GetDebugMaxSyncOrderCount")?.ToInt() ?? 5000;
                return v;
            }
        }

        /// <summary>
        /// 子账号默认的密码
        /// </summary>
        public static string DefaultSubAccountPassword
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("DefaultSubAccountPassword") ?? "Dgj123456";
            }
        }

        /// <summary>
        /// 子账号高级管理员PostCode
        /// </summary>
        public static string SubAccountAdminPostCode => "4EAAAF669F356D6C5EED35CE26BC17DA";

        /// <summary>
        /// 分单系统用于铺货的AppKey列表
        /// </summary>
        public static List<string> FxListingAppKeys
        {
            get
            {
                return new List<string>() { TouTiaoFxListingAppKey };
            }
        }

        /// <summary>
        /// 支持铺货的平台类型，默认TouTiao
        /// </summary>
        public static List<string> FxListingSupportPlatformTypes
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("FxListingSupportPlatformTypes");
                if (string.IsNullOrEmpty(v))
                    v = "TouTiao";
                v = v.Replace("，", ",");
                v = v.Replace(";", ",");
                v = v.Replace(";", ",");
                v = v.Replace(" ", "");

                return v.Split(',').ToList();
            }
        }

        /// <summary>
        /// 需要单独保存到ShopExtension的AppKey列表
        /// 只有分单应用和打单应用分开的平台，才需要将分单应用的授权信息单独保存
        /// </summary>
        public static List<string> NeedSaveToShopExtensionAppKeys
        {
            get
            {
                return new List<string>() { TouTiaoFxAppKey, WeiMengFxAppKey, XiaoHongShuFXAppKey, TouTiaoFxNewAppKey, TouTiaoFxListingAppKey };
            }
        }
        /// <summary>
        /// 非站点、导出程序上下文KEY
        /// </summary>
        public static string ExportTaskAppContextCrossBorderKey
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("ExportTaskAppContextCrossBorderKey") ?? "ExportTaskAppContextCrossBorderKey";
                return v;
            }
        }

        /// <summary>
        /// 分单用户登录密码需要重置的天数
        /// </summary>
		public static int FxUserPasswordNeedResetDays
        {
            get
            {
                var v = System.Configuration.ConfigurationManager.AppSettings.Get("FxUserPasswordNeedResetDays").ToInt();
                if (v <= 0)
                    v = 90;
                return v;
            }
        }
        /// <summary>
        /// 分单用户密码是否要开启过期校验
        /// </summary>
		public static bool IsFxUserPasswordNeedReset
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("IsFxUserPasswordNeedReset");
                if (string.IsNullOrEmpty(v))
                    return true;
                return v == "1" || v == "true";
            }
        }

        /// <summary>
        ///  是否清理旧库订单和商家数据，默认启用
        /// </summary>
        public static bool IsEnabledClearOldDbDataSwitch
        {
            get
            {
                var value = ConfigurationManager.AppSettings.Get("IsEnabledClearOldDbDataSwitch") ?? "1";
                return value == "1";
            }
        }
        public static string OrderSyncAnalysisDbConnectionString
        {
            get
            {
                return System.Configuration.ConfigurationManager.ConnectionStrings["OrderSyncAnalysisDB"]
                    ?.ConnectionString ?? "";
            }
        }
        /// <summary>
        /// 支持订单分析工具Alibaba云平台类型
        /// </summary>
        public static List<string> SupportOrderSyncAnalysisAlibabaPlatformTypes
        {
            get
            {
                //return new List<string>{"Taobao"};
                return new List<string> { "Alibaba", "Taobao", "XiaoHongShu", "AlibabaC2M", "TaobaoMaiCaiV2" };
            }
        }


        /// <summary>
        /// 支持推送库的平台
        /// </summary>
        /// <returns></returns>
        public static List<string> SupportPushDbPlatformTypes
        {
            get
            {
                return new List<string>
                {
                    //"Taobao", "TouTiao", "Pinduoduo"
                    "Taobao", "Pinduoduo"
                };
            }
        }

        private static readonly List<string> DefaultCurrencyCodes = new List<string> { "IDR", "MYR", "PHP", "SGD", "THB", "VND", "BND", "KHR", "LAK", "MMK", "CNY", "USD" };
        public static List<string> NeedConvertCurrencyCodes
        {
            get
            {
                string configValue = ConfigurationManager.AppSettings.Get("NeedConvertCurrencyCodes") ?? "";
                if (!string.IsNullOrEmpty(configValue))
                {
                    return configValue.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                }
                else
                {
                    return DefaultCurrencyCodes;
                }

            }
        }
        /// <summary>
        /// 是否开启熔断切分，默认开
        /// </summary>
        public static bool GetSwitchByOrderMaxSyncSplit
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("GetSwitchByOrderMaxSyncSplit")?.ToBool() ?? true;
                return v;
            }
        }

        /// <summary>
        /// 是否为切换版本工具，默认false 
        /// </summary>
        public static bool IsSwitchVersionTool
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("IsSwitchVersionTool");
                if (string.IsNullOrEmpty(v))
                    return false;

                return v == "1" || v == "true";
            }
        }

        /// <summary>
        /// 是否开启白名单用户过滤，默认开启
        /// </summary>
        public static bool IsFxWhiteUserFilter
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("IsFxWhiteUserFilter")?.ToBool() ?? true;
                return v;
            }
        }
        #region 开放平台配置

        /// <summary>
        /// 【开放平台】数据库
        /// </summary>
        public static string OpenPlatformDbConnectionString
        {
            get
            {
                return ConfigurationManager.ConnectionStrings["OpenPlatformConfigureDB"]?.ConnectionString ?? "";
            }
        }
        private static bool? _openPlatformRecordAllLog = null;

        /// <summary>
        /// 是否开启开放平台记录所有日志
        /// </summary>
        /// </summary>
        public static bool OpenPlatformRecordAllLog
        {
            get
            {
                if (_openPlatformRecordAllLog == null)
                    _openPlatformRecordAllLog = ConfigurationManager.AppSettings.Get("OpenPlatformRecordAllLog") == "1";
                return _openPlatformRecordAllLog ?? false;
            }
        }

        /// <summary>
        /// 开发平台限流次数
        /// </summary>
        public static int OpenPlatformRateLimit
        {
            get
            {
                var limit = ConfigurationManager.AppSettings.Get("OpenPlatformRateLimit");
                return limit.ToInt(60);
            }
        }

        /// <summary>
        /// 开发平台限流时间间隔(秒)
        /// </summary>
        public static int OpenPlatformRateSeconds
        {
            get
            {
                var seconds = ConfigurationManager.AppSettings.Get("OpenPlatformRateSeconds");
                return seconds.ToInt(60);
            }
        }

        private static DateTime? _openPlatformDeprecatedGetTokenApiDate = null;
        /// <summary>
        /// 开放平台废弃获取token接口日期
        /// </summary>
        public static DateTime OpenPlatformDeprecatedGetTokenApiDate
        {
            get
            {
                if (_openPlatformDeprecatedGetTokenApiDate == null)
                {
                    var date = ConfigurationManager.AppSettings.Get("OpenPlatformDeprecatedGetTokenApiDate");
                    _openPlatformDeprecatedGetTokenApiDate = date.IsNullOrEmpty() ? "2024-12-31 23:59".toDateTime() : date.toDateTime();
                }
                return _openPlatformDeprecatedGetTokenApiDate.Value;
            }
        }

        /// <summary>
        /// 开放平台接口地址（拼多多云）
        /// </summary>
        public static string OpenPlatformApiUrlByPinduoduo
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("OpenPlatformApiUrl:Pinduoduo") ?? "http://fxpddop.dgjapp.com";
            }
        }
        /// <summary>
        /// 开放平台接口地址（京东云）
        /// </summary>
        public static string OpenPlatformApiUrlByJingdong
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("OpenPlatformApiUrl:Jingdong") ?? "http://fxjdop.dgjapp.com";
            }
        }

        /// <summary>
        /// 开放平台接口地址（抖店云）
        /// </summary>
        public static string OpenPlatformApiUrlByTouTiao
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("OpenPlatformApiUrl:TouTiao") ?? "http://fxddop.dgjapp.com";
            }
        }

        /// <summary>
        /// 开放平台接口地址（精选云）
        /// </summary>
        public static string OpenPlatformApiUrlByAlibaba
        {
            get
            {
                return ConfigurationManager.AppSettings.Get("OpenPlatformApiUrl:Alibaba") ?? "http://open.dgjapp.com";
            }
        }
        #endregion
        /// <summary>
        /// 是否执行变更日志清理数据
        /// </summary>
        /// <returns></returns>
        public static bool IsExecuteClearDataChangeLog()
        {
            var startTimeConfig = ClearDataChangeLogStartTime;
            var endTimeConfig = ClearDataChangeLogEndTime;

            var baseDateTime = new DateTime(2000, 1, 1, DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second);
            var startTime = new DateTime(2000, 1, 1, startTimeConfig.Hour, startTimeConfig.Minute, 0);
            var endTime = new DateTime(2000, 1, 1, endTimeConfig.Hour, endTimeConfig.Minute, 59);

            return baseDateTime >= startTime && baseDateTime <= endTime;
        }
        
        /// <summary>
        /// 补偿推送心跳时间间隔（默认60秒）
        /// </summary>
        /// <returns></returns>
        public static int CompensatePrintHistoryElapsedSeconds
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensatePrintHistoryElapsedSeconds").ToInt();
                if (v <= 0)
                    v = 60;
                return v;
            }
        }

        #region 禾量平台

        private static bool? _isHeliangTestUrl = null;
        /// <summary>
        /// 是否是禾量测试环境地址
        /// </summary>
        public static bool IsHeliangTestUrl
        {
            get
            {
                if (_isHeliangTestUrl == null)
                    _isHeliangTestUrl = ConfigurationManager.AppSettings.Get("IsHeliangTestUrl") == "1";
                return _isHeliangTestUrl ?? false;
            }
        }

        /// <summary>
        /// 是否使用禾量新的订单项生成规则规则
        /// </summary>
        /// <param name="createTime"></param>
        /// <returns></returns>
        public  static bool IsHeliangNewSubItemIdRule(DateTime createTime)
        {
            var dateStr = ConfigurationManager.AppSettings.Get("HeliangNewSubItemIdRuleDate");
            var date = dateStr.ToDateTime();
            if (date ==null)
                date = "2025-04-01 00:00".ToDateTime();
            return createTime >= date;
        }

        /// <summary>
        /// 获取禾量新单号生成的时间
        /// </summary>
        /// <returns></returns>
        public static DateTime GetHeliangNewSubItemIdDate()
        {
            var dateStr = ConfigurationManager.AppSettings.Get("HeliangNewSubItemIdRuleDate");
            var date = dateStr.ToDateTime();
            if (date == null)
                date = "2025-04-01 00:00".ToDateTime();
            return date.Value;
        }
        #endregion

        /// <summary>
        /// 京东云支持的平台，默认：Jingdong,JingdongPurchase
        /// </summary>
        public static List<string> FxJingDongCloudPlatformTypes
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("FxJingDongCloudPlatformTypes");
                if (string.IsNullOrWhiteSpace(v))
                    v = "Jingdong,JingdongPurchase";
                return v.Split(',').ToList();
            }
        }

        /// <summary>
        /// 抖店云支持的平台，默认：TouTiao,TouTiaoSaleShop
        /// </summary>
        public static List<string> FxDouDianCloudPlatformTypes
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("FxDouDianCloudPlatformTypes");
                if (string.IsNullOrWhiteSpace(v))
                    v = "TouTiao,TouTiaoSaleShop";
                return v.Split(',').ToList();
            }
        }

        /// <summary>
        /// 拼多多云支持的平台，默认：Pinduoduo,KuaiTuanTuan
        /// </summary>
        public static List<string> FxPinduoduoCloudPlatformTypes
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("FxPinduoduoCloudPlatformTypes");
                if (string.IsNullOrWhiteSpace(v))
                    v = "Pinduoduo,KuaiTuanTuan";
                return v.Split(',').ToList();
            }
        }

        /// <summary>
        /// 系统环境版本，根据部署的服务器环境更改不同的值。固定值：Online（正式），Gray1（灰度1），Gray3,Test1（1t测试环境）,Test3,Development （开发环境）
        /// </summary>
        public static string SystemEnvironmentVersion
        {
            get
            {
				return ConfigurationManager.AppSettings.Get("SystemEnvironmentVersion") ?? "Online";
            }
        }

		/// <summary>
		/// 是否开启链路日志
		/// </summary>
		public static bool IsOpenOpenTelemetry
		{
			get
			{
				var v = ConfigurationManager.AppSettings.Get("IsOpenOpenTelemetry")?.ToBool() ?? false;
				return v;
			}
		}

        /// <summary>
        /// 是否开启NLOG
        /// </summary>
		public static bool IsOpenNLog
		{
			get
			{
				var v = ConfigurationManager.AppSettings.Get("IsOpenNLog")?.ToBool() ?? true;
				return v;
			}
		}

		#region 配置库归档相关

		/// <summary>
		/// 是否开启并发配置库归档
		/// </summary>
		public static bool IsEnabledParallelConfigureDbArchive
        {
            get
            {
                var value =
                    ConfigurationManager.AppSettings.Get("DianGuanJiaApp:IsEnabledParallelConfigureDbArchive") ??
                    "0";
                value = string.IsNullOrWhiteSpace(value) ? "0" : value;
                return value == "1";
            }
        }

        /// <summary>
        /// 数据归档执行时间（开始），默认值：00:00
        /// </summary>
        /// <returns></returns>
        public static DateTime DataArchiveExecuteStartTime
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DianGuanJiaApp:DataArchiveExecuteStartTime") ?? "00:00";
                return ($"2023-01-01 {v}").toDateTime();
            }
        }
        /// <summary>
        /// 数据归档执行时间（结束），默认值：06:00
        /// </summary>
        /// <returns></returns>
        public static DateTime DataArchiveExecuteEndTime
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DianGuanJiaApp:DataArchiveExecuteEndTime") ?? "06:00";
                return ($"2023-01-01 {v}").toDateTime();
            }
        }

        /// <summary>
        /// 是否执行数据归档时间范围
        /// </summary>
        public static bool IsDataArchiveExecuteTimeRange
        {
            get
            {
                var startTimeConfig = DataArchiveExecuteStartTime;
                var endTimeConfig = DataArchiveExecuteEndTime;

                var baseDateTime =
                    new DateTime(2000, 1, 1, DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second);
                var startTime = new DateTime(2000, 1, 1, startTimeConfig.Hour, startTimeConfig.Minute, 0);
                var endTime = new DateTime(2000, 1, 1, endTimeConfig.Hour, endTimeConfig.Minute, 59);

                return baseDateTime >= startTime && baseDateTime <= endTime;
            }
        }
        #endregion
        
        /// <summary>
        /// 支持发货前换款的平台
        /// </summary>
        public static string[] ChangeSkuAcceptOrderPlatformType 
        {
            get
            {
                return new string[] { "WxVideo" };
            }
        }

        /// <summary>
        /// 统计工具临时数据库连接字符串
        /// </summary>
        public static string StatDbConnectionString =>
            ConfigurationManager.ConnectionStrings["StatDbConnectionString"]?.ConnectionString ??
            ConfigureDbConnectionString;

        /// <summary>
        /// 补偿基础商品平台关联关系-心跳时间间隔（默认120秒）
        /// </summary>
        /// <returns></returns>
        public static int CompensateBaseProductptRelationsElapsedSeconds
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensateBaseProductptRelationsElapsedSeconds").ToInt();
                if (v <= 0)
                    v = 120;
                return v;
            }
        }

        /// <summary>
        /// 补偿基础商品平台关联关系-心跳时间间隔（默认120秒）
        /// </summary>
        /// <returns></returns>
        public static int CompensateBaseProductptRelations_ReverseElapsedSeconds
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensateBaseProductptRelations_ReverseElapsedSeconds").ToInt();
                if (v <= 0)
                    v = 120;
                return v;
            }
        }

        /// <summary>
        /// 补偿基础商品平台关联关系-指定库
        /// </summary>
        /// <returns></returns>
        public static List<string> CompensateBaseProductptRelations_ReverseDb
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensateBaseProductptRelations_ReverseDb");
                if (v.IsNullOrEmpty())
                    return new List<string>();
                else
                    return v.Split(',').ToList();
            }
        }
        /// <summary>
        /// 补偿基础商品平台关联关系-指定库云
        /// </summary>
        /// <returns></returns>
        public static string CompensateBaseProductptRelations_ReverseDbCloud
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("CompensateBaseProductptRelations_ReverseDbCloud").ToString();
                return v;
            }
        }
         
        /// <summary>
        /// Dapper清理缓存执行时间（开始），默认值：01:00
        /// </summary>
        /// <returns></returns>
        public static DateTime DappePurgeCacheExecuteStartTime
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DianGuanJiaApp:DappePurgeCacheExecuteStartTime") ?? "01:00";
                return ($"2023-01-01 {v}").toDateTime();
            }
        }
        
        /// <summary>
        /// Dapper清理缓存执行时间（结束），默认值：04:00
        /// </summary>
        /// <returns></returns>
        public static DateTime DappePurgeCacheExecuteEndTime
        {
            get
            {
                var v = ConfigurationManager.AppSettings.Get("DianGuanJiaApp:DappePurgeCacheExecuteEndTime") ?? "04:00";
                return ($"2023-01-01 {v}").toDateTime();
            }
        }
        
        /// <summary>
        /// 是否执行Dapper清理缓存范围
        /// </summary>
        public static bool IsDappePurgeCacheExecuteTimeRange
        {
            get
            {
                var startTimeConfig = DappePurgeCacheExecuteStartTime;
                var endTimeConfig = DappePurgeCacheExecuteEndTime;

                var baseDateTime =
                    new DateTime(2000, 1, 1, DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second);
                var startTime = new DateTime(2000, 1, 1, startTimeConfig.Hour, startTimeConfig.Minute, 0);
                var endTime = new DateTime(2000, 1, 1, endTimeConfig.Hour, endTimeConfig.Minute, 59);

                return baseDateTime >= startTime && baseDateTime <= endTime;
            }
        }

        /// <summary>
        /// 商品同步支持时间切片同步平台
        /// </summary>
        public static List<string> ProductSyncSupportCutTimePlatformTypes
        {
            get
            {
                return new List<string> { "TouTiao", "XiaoHongShu", "Alibaba" };
            }
        }
        
        /// <summary>
        /// 商品同步时间切片最小切片（天）
        /// </summary>
        /// <param name="platformType"></param>
        /// <param name="isFullSync"></param>
        /// <returns></returns>
        public static double GetPlatformProductSyncDiffTime(string platformType, bool isFullSync = false)
        {
            //全量
            if (isFullSync)
            {
                return 1;
            }
            //增量
            switch (platformType)
            {
                case "XiaoHongShu":
                    return 0.5;
                default:
                    return 0.25;
            }
        }
    }

    public class SuportPlatformAuthEntryModel
    {
        public string Img { get; set; }

        public string PlatformType { get; set; }

        public string Name { get; set; }
        public string AuthEntry { get; set; }
        public int Index { get; set; }
    }

    public class FxUserBindPlatformModel
    {
        /// <summary>
        /// 是否用授权url授权,默认是不通过服务市场点使用授权
        /// </summary>
        public bool IsAuthUrl { get; set; } = true;
        public string PlatformType { get; set; }
        public string PlatformName { get; set; }
        public string AuthUrl { get; set; }
        /// <summary>
        /// 背景图定位
        /// </summary>
        public string Style { get; set; }
        /// <summary>
        /// 续费链接
        /// </summary>
        public string PayUrl { get; set; }
        /// <summary>
        /// 自定义标识
        /// </summary>
        public string Flag { get; set; }
        /// <summary>
        /// 授权接口
        /// </summary>
        public string AuthApi { get; set; }

    }

    public class AppPlatformModel
    {

        public AppPlatformModel()
        {
            Img = "fxapp-platform.png";
        }

        public string Appkey { get; set; }
        public string Name { get; set; }
        public string Img { get; set; }
        public string Platform { get; set; }
        public string AuthUrl { get; set; }
        public string MarketUrl { get; set; }
        public string ImgXy { get; set; }

        /// <summary>
        /// 是否为分单铺货应用
        /// </summary>
        public bool IsFxListingApp
        {
            get
            {
                if (string.IsNullOrEmpty(Appkey) == false && CustomerConfig.FxListingAppKeys.Contains(Appkey))
                    return true;

                return false;
            }
        }
    }
}
