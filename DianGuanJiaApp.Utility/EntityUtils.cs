using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Utility
{
    public class EntityUtils
    {
        /// <summary>
        /// 比较两个对象列表中 相同对象的若干属性值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list1"></param>
        /// <param name="list2"></param>
        /// <param name="compareFields">需要进行比较的属性名</param>
        /// <param name="keyFields">用于匹配两个列表相同下标元素的属性名</param>
        /// <returns>
        /// 一个二元组，item1 = 相同对象之间，有属性值不同的属性名，
        /// item2 = 比较之后，属性值不同的对象列表，以list2的元素构建（属性值相同的对象将被过滤）
        /// </returns>
        public static Tuple<List<string>,List<T>> CompareFields<T>(List<T> list1, List<T> list2, List<string> compareFields,List<string> keyFields)
        {
            if (list1.IsNullOrEmptyList() && list2.IsNullOrEmptyList())
            {
                return new Tuple<List<string>, List<T>>(new List<string>(), new List<T>());
            }

            if (keyFields.IsNullOrEmptyList())
            {
                throw new Exception("匹配键列表为空");
            }
            if (compareFields.IsNullOrEmptyList())
            {
                throw new Exception("需要对比的属性名列表为空");
            }

            var properties = typeof(T).GetProperties();
            var keys = properties.Where(p => keyFields.Contains(p.Name)).ToList();

            if (keys.IsNullOrEmptyList())
            {
                throw new Exception("匹配键列表为空");
            }
            var differentFields = new HashSet<string>();
            var differentList = new HashSet<T>();

            foreach(var temp in list1)
            {
                T containOne = default(T);
                foreach (var item in list2)
                {
                    if (keys.All(key => Object.Equals(key.GetValue(temp), key.GetValue(item))))
                    {
                        containOne = item;
                        break;
                    }
                }
                if (containOne == null)
                    continue;
                // 遍历需要比较的字段 
                compareFields.ForEach(field =>
                {
                    var property = properties.FirstOrDefault(p => p.Name.Equals(field));
                    if (property != null)
                    {
                        var value1 = property.GetValue(temp);
                        var value2 = property.GetValue(containOne);
                        // 属性值不同
                        if (value2 !=null && !Object.Equals(value1, value2))
                        {
                            differentFields.Add(field);
                            differentList.Add(containOne);
                        }

                    }
                });
                list2.Remove(containOne);
            }
            
            return new Tuple<List<string>, List<T>>(differentFields.ToList(),differentList.ToList());
        }
    }
}
