using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DianGuanJiaApp.Services;
using System.Collections;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models.Account;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Other;
using DianGuanJiaApp.Services.WaybillService;
using System.Threading;
using Top.Api.Util;
using DianGuanJiaApp.Models;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Configuration;
using DianGuanJiaApp.Services.ServicesExtension;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using System.Diagnostics;
using DianGuanJiaApp.Data.MongoRepository;
using System.Text;
using System.IO;
using DianGuanJiaApp.Utility.Net;
using System.Text.RegularExpressions;
using DianGuanJiaApp.Services.PlatformService;
using MongoDB.Bson;
using DianGuanJiaApp.Data;
using Dapper;
using DianGuanJiaApp.Services.Services.Login;
using AngleSharp.Network.Default;

namespace DianGuanJiaApp.Controllers
{
    public class AuthController : ToolsBaseController
    {
        private ShopService _shopService = new ShopService();
        private AdvService _advService = new AdvService();
        private UserInfoService _userInfoService = new UserInfoService();
        private CaiNiaoAuthInfoService caiNiaoAuthInfoService = new CaiNiaoAuthInfoService();
        private CainiaoAuthOwnerService cainiaoAuthOwnerService = new CainiaoAuthOwnerService();
        private WaybillCustomAreaService _waybillCustomAreaService = new WaybillCustomAreaService();
        private FxUserShopService _fxUserShopService = new FxUserShopService();
        private ShopExtensionService _shopExtensionService = new ShopExtensionService();
        private SyncStatusService _syncStatusService = new SyncStatusService();
        private SyncTaskService _syncTaskService = new SyncTaskService();

        public ActionResult Error()
        {
            return View("~/Views/Shared/Error.html");
        }

        protected ActionResult SuccessResult(dynamic data = null)
        {
            return new CustomJsonResult(new AjaxResult()
            {
                Success = true,
                Data = data
            });
        }

        public ActionResult TemplateXml(string id = "")
        {
            string res = "";
            int templateId = 0;
            try
            {
                var idValue = id;
                if (!string.IsNullOrEmpty(idValue))
                {
                    bool isNumber = Regex.IsMatch(idValue, @"^\d+$");
                    if (isNumber)
                        templateId = Convert.ToInt32(idValue);
                }
            }
            catch (Exception ex)
            {
                return Content("模板id转换异常");
            }

            try
            {
                WaybillCustomArea ar = _waybillCustomAreaService.GetModel(templateId);
                if (ar != null)
                {
                    Response.ContentType = "text/xml";
                    return Content(ar.CustomData);
                }
                else
                {
                    res = "空";
                }
            }
            catch (Exception ex)
            {
                res = "查询异常";
            }

            return Content(res);
        }

        protected ActionResult FalidResult(string message, dynamic data = null)
        {
            return new CustomJsonResult(new AjaxResult()
            {
                Success = false,
                Message = message,
                Data = data
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="shop"></param>
        /// <param name="token"></param>
        public int IfShopIsSupportWaybillThenSaveIt(Shop shop, string rp)
        {
            try
            {
                var ownerShopId = shop.Id;
                if (!string.IsNullOrEmpty(rp))
                {
                    try
                    {
                        var model = ExpainToken(rp);
                        if (model != null)
                            ownerShopId = model.ShopId;
                    }
                    catch (Exception e)
                    {
                        Log.WriteError($"解析拼多多授权返回的rp（{rp}）,shopid({shop.Id})时发生错误：{e}");
                    }
                }
                var cainiao = new CainiaoAuthOwnerService();
                var old = cainiao.Get(" WHERE ShopId=@sid AND AuthSourceType=1 AND CaiNiaoAuthInfoId=@cid", new { sid = ownerShopId, cid = shop.Id })?.FirstOrDefault();
                if (old == null)
                {
                    cainiao.Add(new CainiaoAuthOwner
                    {
                        CaiNiaoAuthInfoId = shop.Id,
                        ShopId = ownerShopId,
                        AuthSourceType = 1,
                        IsDeleted = false,
                        MemberId = shop.ShopId
                    });
                }
                else
                {
                    old.IsDeleted = false;
                    cainiao.Update(old);
                }
                //添加模板
                AddTemplate(shop.ToWaybillAuthInfo(), ownerShopId);
                //ThreadPool.QueueUserWorkItem(item =>
                //{
                //    try
                //    {
                //        AutoAddTemplate(model);
                //    }
                //    catch (Exception ex)
                //    {
                //        Log.WriteError($"分享后自动添加模板失败：{ex}");
                //    }
                //});

                return ownerShopId;
            }
            catch (Exception ex)
            {
                Log.WriteError($"授权完成后将拼多多授权保存到电子面单授权表时发生错误：{ex}");
                return shop.Id;
            }
        }

        public void AddTemplate(CaiNiaoAuthInfo cainiaoAuthInfo, int shopId, bool isAddMaster = false)
        {
            try
            {
                SiteContext sc = new SiteContext(shopId);
                (new TemplateSetController()).AutoAddTemplate(cainiaoAuthInfo, isAddMaster);
            }
            catch (Exception ex)
            {
                Log.WriteLine($"授权后添加模板失败,参数【cainiaoAuthInfo：{cainiaoAuthInfo.ToJson()}，shopId:{shopId}，isAddMaster:{isAddMaster}】,错误:{ex.ToString()}");
            }
        }

        public void OpenTaobaoMessage(Shop shop)
        {
            if (shop == null || shop.PlatformType != PlatformType.Taobao.ToString())
                return;
            try
            {
                var pt = new TaobaoPlatformService(shop);
                pt.OpenMessage();
            }
            catch (Exception ex)
            {
                Log.WriteError($"店铺【{(shop?.NickName ?? shop?.ShopName)} {shop?.Id}】订阅淘宝消息时发生错误:{ex}");
            }
        }

        public void OpenPddMessageAndPushDB(Shop shop)
        {
            if (shop == null || shop.PlatformType != PlatformType.Pinduoduo.ToString())
                return;
            try
            {
                var pt = new PinduoduoPlatformService(shop);
                pt.OpenMessage();
                pt.OpenPushDB();
            }
            catch (Exception ex)
            {
                Log.WriteError($"店铺【{(shop?.NickName ?? shop?.ShopName)} {shop?.Id}】订阅拼多多消息和推送库时发生错误:{ex}");
            }
        }

        /// <summary>
        /// 同步拼多多的中文店铺名称
        /// </summary>
        /// <param name="shop"></param>
        public void SyncPddShopNickName_Cn(Shop shop)
        {
            if (shop == null || shop.PlatformType != PlatformType.Pinduoduo.ToString())
                return;
            try
            {
                var oldNickName = shop.NickName;
                var pt = new PinduoduoPlatformService(shop);
                var newShop = pt.SyncShopInfo();

                if (newShop != null && oldNickName != newShop.NickName && string.IsNullOrWhiteSpace(newShop.NickName) == false)
                {
                    var dict = new Dictionary<int, string>();
                    dict.Add(newShop.Id, newShop.NickName);
                    _shopService.UpdateNickName(dict);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"店铺【{(shop?.NickName ?? shop?.ShopName)} {shop?.Id}】获取中文店铺名称时发生错误:{ex}");
            }
        }

        /// <summary>
        /// 判断用户是否是厂商/商家
        /// </summary>
        /// <param name="shop"></param>
        public void SyncPddShopRole(Shop shop)
        {
            if (shop == null || shop.PlatformType != PlatformType.Pinduoduo.ToString())
                return;
            try
            {
                var oldVenderId = shop.VenderId;
                var pt = new PinduoduoPlatformService(shop);
                var newShop = pt.GetShopRole();

                if (newShop != null && oldVenderId != newShop.VenderId && string.IsNullOrWhiteSpace(newShop.VenderId) == false)
                {
                    var dict = new Dictionary<int, string>();
                    dict.Add(newShop.Id, newShop.VenderId);
                    _shopService.UpadateVenderId(dict);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"店铺【{(shop?.NickName ?? shop?.ShopName)} {shop?.Id}】获取中文店铺名称时发生错误:{ex}");
            }
        }


        /// <summary>
        /// 同步拼多多的中文店铺名称
        /// </summary>
        /// <param name="shop"></param>
        public void SyncShopExpiredTime(Shop shop)
        {
            try
            {
                //先让过期,因过期后才会去调接口
                var time = shop.ExpireTime;
                shop.ExpireTime = DateTime.Now.AddDays(-1);
                var pt = PlatformFactory.GetPlatformService(shop);
                var curTime = pt.GetExpiredTime();
            }
            catch (Exception ex)
            {
                Log.WriteError($"店铺【{(shop?.NickName ?? shop?.ShopName)} {shop?.Id}】获取过期时间时发生错误:{ex}");
            }
        }

        /// <summary>
        /// 同步店铺信息
        /// </summary>
        /// <param name="shop"></param>
        public void SyncShopInfo(Shop shop)
        {
            if (shop == null)
                return;
            try
            {
                if (shop.PlatformType == PlatformType.Taobao.ToString())
                {
                    var pt = new TaobaoPlatformService(shop);
                    var newShop = pt.GetPlatformShopInfo();
                    var dict = new Dictionary<string, string>();
                    if (newShop != null)
                    {
                        if (string.IsNullOrWhiteSpace(newShop.ShopId) == false)
                        {
                            dict.Add("ShopId", newShop.ShopId);
                        }
                        if (string.IsNullOrWhiteSpace(newShop.NickName) == false)
                        {
                            dict.Add("NickName", newShop.NickName);
                        }
                        if (string.IsNullOrWhiteSpace(newShop.ShopName) == false)
                        {
                            dict.Add("ShopName", newShop.ShopName);
                        }

                        if (dict.Count > 0)
                            _shopService.UpdateShop(shop.Id, dict);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"店铺【{(shop?.NickName ?? shop?.ShopName)} {shop?.Id}】获取店铺信息时发生错误:{ex}");
            }
        }

        #region 授权



        /// <summary>
        /// 授权入口，添加店铺，并返回重定向的url
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult Entrance(AlibabaMemberToken model)
        {

            Log.WriteError("AlibabaMemberToken:===" + model.ToJson());

            if (model == null)
                return Json(new { Success = false, Message = "数据为空，或数据格式无法解析" });
            if (string.IsNullOrEmpty(model.MemberId))
                return Json(new { Success = false, Message = "MemberId 不能为空" });
            if (string.IsNullOrEmpty(model.Refresh_Token))
                return Json(new { Success = false, Message = "Refresh_Token 不能为空" });
            if (string.IsNullOrEmpty(model.PlatformType))
                return Json(new { Success = false, Message = "PlatformType 不能为空" });

            var authType = Request.QueryString["authtype"]; //授权类型

            //转换platyform
            var platformTypeStr = CustomerConfig.ConvertPlatformType(model.PlatformType);
            PlatformType platformType = SiteContext.ConvertPlatform(platformTypeStr);
            var shop = _shopService.AddShop(model, platformType);
            var redirectShopId = shop.Id;
            if ((shop.PlatformType == PlatformType.Pinduoduo.ToString()
                || shop.PlatformType == PlatformType.Taobao.ToString()
                )
                && authType != "3") //authtype=3为分发系统添加店铺授权，不需要添加授权信息。RpVal 为分发系统的用户token不是店铺token，解析会报错。
                redirectShopId = IfShopIsSupportWaybillThenSaveIt(shop, model.RpVal);
            if (redirectShopId != shop.Id)
            {
                var temp = _shopService.Get(redirectShopId);
                if (temp != null)
                    shop = temp;
            }

            //=================================================
            try
            {
                _userInfoService.AddAndUpdateModel(shop);
            }
            catch (Exception ex) { }
            //=====================end============================
            //拼多多添加消息订阅、和推送库订阅、同步店铺中文名
            if (shop.PlatformType == PlatformType.Pinduoduo.ToString())
            {
                ThreadPool.QueueUserWorkItem(item =>
                {
                    try
                    {
                        OpenPddMessageAndPushDB(shop);
                        SyncPddShopNickName_Cn(shop);
                    }
                    catch { }
                });
            }
            //淘宝添加消息订阅
            if (shop.PlatformType == PlatformType.Taobao.ToString())
            {
                ThreadPool.QueueUserWorkItem(item =>
                {
                    try
                    {
                        OpenTaobaoMessage(shop);
                    }
                    catch { }
                });
            }
            //淘宝添加消息订阅
            if (shop.PlatformType == PlatformType.Taobao.ToString())
            {
                ThreadPool.QueueUserWorkItem(item =>
                {
                    try
                    {
                        OpenTaobaoMessage(shop);
                    }
                    catch { }
                });
            }
            try
            {
                //同步过期时间
                SyncShopExpiredTime(shop);
                //同步店铺角色
                SyncPddShopRole(shop);
                //同步店铺信息
                SyncShopInfo(shop);
                //订单分发添加店铺绑定
                if (authType == "3")
                {
                    //TODO：添加商家与店铺的关系
                    //1.解析商家的rp，拿到用户信息
                    //2.插入商家与店铺的关系
                    //商家添加店铺需要做两件事
                    //1.保存关联关系，2.添加店铺订单同步任务
                    if (string.IsNullOrWhiteSpace(model.RpVal))
                    {
                        Log.WriteError("商家关联店铺授权，未传递商家的token，无法添加商家与店铺的关系");
                    }
                    else
                    {
                        //1.解析token 得到 商家id
                        var loginTokenId = DES.DecryptUrl(model.RpVal, CustomerConfig.LoginCookieEncryptKey).ToInt();
                        var loginAuthToken = _shopService.GetToken(loginTokenId);
                        var fxUserId = loginAuthToken.FxUserId;

                        //先判断店铺是否被商家关联，如果已经被其他商家关联，则不再添加关联关系
                        var shoplist = _fxUserShopService.GetList(shopId: shop.Id);
                        var isOtherUserAuthed = false; //店铺是否被其他商家关联了
                        if (shoplist.Any(f => f.FxUserId != fxUserId))
                        {
                            isOtherUserAuthed = true;
                            var fxshop = shoplist.FirstOrDefault();
                            var url = "http://auth.dgjapp.com/auth/authsuccess";
                            if (string.IsNullOrWhiteSpace(model.SuccToUrl))
                                model.SuccToUrl = url;
                            model.SuccToUrl = model.SuccToUrl.TrimEnd(',');
                            var uri = new Uri(model.SuccToUrl);
                            var showName = string.IsNullOrWhiteSpace(fxshop.FxUserName) ? fxshop.FxUserMobile : fxshop.FxUserName + "|" + fxshop.FxUserMobile;
                            if (string.IsNullOrEmpty(uri.Query))
                            {
                                url = $"{model.SuccToUrl}?message={Server.UrlEncode($"店铺已经被商家【{showName}】关联")}&type=2";
                            }
                            else if (uri.Query.StartsWith("?"))
                            {
                                url = $"{model.SuccToUrl}&message={Server.UrlEncode($"店铺已经被商家【{showName}】关联")}&type=2";
                            }
                            model.SuccToUrl = url;
                        }

                        if (isOtherUserAuthed == false)
                        {
                            //只有分单应用和打单应用分开的平台，才需要将分单应用的授权信息单独保存
                            var fendanAppAlonePts = (new CommonSettingService()).Get<List<string>>("/SystemSetting/FenDanAppAlonePt", 0);
                            if (fendanAppAlonePts != null && fendanAppAlonePts.Contains(shop.PlatformType))
                            {
                                //2.保存店铺accesstoken,refreshtoken和授权应用的AppKey，AppSecret
                                _shopExtensionService.AddShopExtension(new ShopExtension()
                                {
                                    ShopId = shop.Id,
                                    AppKey = model.AppKey ?? "",
                                    AppSecret = model.Secret ?? ""
                                });
                            }

                            //3.绑定关系
                            _fxUserShopService.AddFxUserShop(new FxUserShop()
                            {
                                FxUserId = fxUserId,
                                ShopId = shop.Id,
                                PlatformType = shop.PlatformType,
                                NickName = shop.NickName,
                                CreateTime = DateTime.Now,
                                AuthTime = shop.ExpireTime ?? DateTime.Now,
                                Status = FxUserShopStatus.Binded,
                            });

                            //4.保存店铺同步状态数据
                            //订单同步状态
                            var syncOrderStatus = new SyncStatus()
                            {
                                FxUserId = fxUserId,
                                ShopId = shop.Id,
                                SyncType = ShopSyncType.Order,
                                CreateTime = DateTime.Now,
                                Source = OwnerSource.FenDanSystem.ToString()
                            };
                            _syncStatusService.AddSyncStatus(syncOrderStatus);

                            //产品同步状态
                            var syncProductStatus = new SyncStatus()
                            {
                                FxUserId = fxUserId,
                                ShopId = shop.Id,
                                SyncType = ShopSyncType.Product,
                                CreateTime = DateTime.Now,
                                Source = OwnerSource.FenDanSystem.ToString()
                            };
                            _syncStatusService.AddSyncStatus(syncProductStatus);

                            //5.同步任务
                            var syncTask = new SyncTask()
                            {
                                FxUserId = fxUserId,
                                ShopId = shop.Id,
                                TaskType = SyncTaskType.Order,
                                Status = SyncTaskStatus.Wait,
                                CreateTime = DateTime.Now,
                                Source = OwnerSource.FenDanSystem.ToString()
                            };
                            _syncTaskService.AddTask(syncTask);
                            CommUtls.WriteToLog($"Entrance ==> syncOrderStatus：{syncOrderStatus.ToJson()}\nsyncTask：{syncTask.ToJson()}", "BindShop.txt", "AuthToBindShop");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError("保存分销用户绑定用户授权报错：" + ex.ToString());
            }

            var token = DES.EncryptUrl($"{shop.Id},{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}", CustomerConfig.AlibabaAuthCallbackKey);
            if (string.IsNullOrWhiteSpace(model.SuccToUrl))
            {
                var link = CustomerConfig.GetShopRedirectLink(shop.PlatformType, shop.Version, shop.VenderId);

                //不显示授权成功页，走callback进入系统 
                return Json(new { Success = true, RedirectUrl = $"{link}/Auth/Callback?token={token}&state={model.RpVal}", Message = "成功，请重定向到RedirectUrl进行登录，3分钟内有效" });
            }
            else
            {
                model.SuccToUrl = model.SuccToUrl.TrimEnd(',');
                var url = "";
                var uri = new Uri(model.SuccToUrl);
                if (string.IsNullOrEmpty(uri.Query))
                {
                    url = $"{model.SuccToUrl}?token={token}&state={model.RpVal}";
                }
                else if (uri.Query.StartsWith("?token"))
                {
                    url = model.SuccToUrl;
                }
                else if (uri.Query.StartsWith("?"))
                {
                    url = $"{model.SuccToUrl}&token={token}&state={model.RpVal}";
                }
                return Json(new { Success = true, RedirectUrl = $"{url}", Message = "成功，请重定向到RedirectUrl进行登录，3分钟内有效" });
            }
        }

        //public ActionResult Callback(string token = "", string state = "")
        //{
        //    if (string.IsNullOrEmpty(token))
        //        return Error();
        //    var idAndTime = DES.DecryptUrl(token, CustomerConfig.AlibabaAuthCallbackKey);
        //    if (string.IsNullOrEmpty(idAndTime))
        //        return Error();
        //    var arr = idAndTime.Split(',');
        //    if (arr.Length != 2)
        //        return Error();
        //    var shopId = arr.FirstOrDefault().ToInt();
        //    if (shopId <= 0)
        //        return Error();
        //    var timestamp = arr.LastOrDefault().toDateTime();
        //    if (timestamp < DateTime.Now.AddMinutes(-3) && !CustomerConfig.IsDebug)
        //        return Error();
        //    var shop = _shopService.Get(shopId);
        //    if (shop == null)
        //        return Error();
        //    //设置cookie
        //    //SetAuthCookie(shopId, Request, Response);
        //    string t = "";
        //    if (!string.IsNullOrEmpty(state))
        //    {
        //        var model = ExpainToken(state);
        //        var hashCode = Request.Headers.Get("User-Agent").ToShortMd5();
        //        var sign = hashCode.ToString();
        //        //同一个浏览器同一个店铺才能用，不然需要生成新的token
        //        if (model != null && sign == model.Sign && model.ShopId == shopId)
        //        {
        //            t = model.Token;
        //            _shopService.RefreshToken(model);
        //        }
        //    }
        //    if (string.IsNullOrEmpty(t))
        //        t = CreateToken(shopId, Request, response: Response);
        //    var link = CustomerConfig.GetShopRedirectLink(shop.PlatformType, shop.Version, shop.VenderId);
        //    if (shop.PlatformType == "Taobao")
        //        ych_sdk.YchRequestLogger.Login(shop.Id.ToString(), shop.ShopId, true);

        //    //厂家用户默认进入 代打页面
        //    if (shop.IsPddFactorer)
        //        link += "/OrderFds/IndexFds";
        //    else
        //        link = $"{link}/GeneralizeIndex/Index"; // 默认进入首页
        //    return Redirect($"{link}/?token=" + t);
        //}



        #endregion

        /// <summary>
        /// 授权成功页
        /// </summary>
        /// <param name="message"></param>
        /// <param name="token"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        public ActionResult AuthSuccess(string message = "", string token = "", string state = "")
        {
            try
            {
                //shopid,时间戳
                var shopId = 0;
                try
                {
                    if (string.IsNullOrEmpty(token) == false)
                        shopId = Utility.DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey)?.Split(',')?.FirstOrDefault()?.ToInt() ?? 0;
                }
                catch (Exception)
                {

                }
                //LoginAuthToken.Id
                var tokenId = 0;
                try
                {
                    if (string.IsNullOrEmpty(state) == false)
                        tokenId = Utility.DES.DecryptUrl(state, CustomerConfig.LoginCookieEncryptKey)?.ToInt() ?? 0;
                }
                catch (Exception)
                {

                }
                Shop shop = null;
                LoginAuthToken authToken = null;
                if (shopId > 0)
                    shop = _shopService.Get(shopId);
                if (tokenId > 0)
                    authToken = _shopService.GetToken(tokenId);
                if (shop != null && shop.PlatformType == PlatformType.Taobao.ToString())
                {
                    var uid = shop.Id;
                    if (authToken != null)
                        uid = authToken.ShopId;
                    ych_sdk.YchRequestLogger.Login(uid.ToString(), shop.ShopId, true);
                    Log.WriteLine($"上次御城河登录日志完成，参数：{uid},{shop.ShopId},{true}");
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"解析授权信息以上传御城河日志时发生错误,token:{token},state:{state}，异常信息：{ex}");
            }
            ViewBag.Message = message;
            ViewBag.Type = Request.QueryString["type"];
            return View();
        }

        /// <summary>
        /// 授权成功页
        /// </summary>
        /// <param name="message"></param>
        /// <param name="token"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        public ActionResult AuthFail(string message = "", string token = "", string state = "")
        {
            ViewBag.Message = message;
            return View();
        }

        ///// <summary>
        ///// 第三方平台拿到token后的入口
        ///// </summary>
        //public ActionResult Entry()
        //{
        //    var shopJson = Request["shopJson"] ?? "";
        //    var state = Request["state"] ?? "";
        //    //var json = DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey);

        //    if (!string.IsNullOrEmpty(shopJson))
        //    {
        //        var shopObj = shopJson.ToObject<AlibabaMemberToken>();

        //        var shop = new Shop()
        //        {
        //            PlatformType = shopObj.PlatformType,
        //            AccessToken = shopObj.Access_Token,
        //            RefreshToken = shopObj.Refresh_Token,
        //            ShopName = shopObj.UserId,
        //            Uid = shopObj.UserId,
        //            ShopId = shopObj.UserId
        //        };//_shopService.AddOrUpdateShopByUid(shopObj, platform);

        //        var returnUrl = CustomerConfig.GetShopAuthLink(shop.PlatformType);

        //        //同步店铺信息
        //        var platformService = PlatformFactory.GetPlatformService(shop);
        //        try
        //        {
        //            shop = platformService.SyncShopInfo();
        //            if (shop.PlatformType == PlatformType.Jingdong.ToString() && shop.SubPlatformType != "SOP" && shop.SubPlatformType != "FBP")
        //            {
        //                Log.WriteLine($"授权接口不支持此类京东店铺：{shop.ToJson()}");
        //                return Redirect($"{CustomerConfig.AuthCallbackUrl.TrimEnd("/")}/auth/authmessage?Message={Server.HtmlEncode("授权接口不支持此类京东店铺")}&Code=100&Second=3&TransferUrl={returnUrl}");
        //            }
        //            if (string.IsNullOrWhiteSpace(shop.ShopId) || string.IsNullOrWhiteSpace(shop.VenderId))
        //                throw new Exception("未获取到shopId");
        //        }
        //        catch (Exception ex)
        //        {
        //            Log.WriteError($"授权回调同步店铺信息失败，shopJson：{shopJson},state：{state},ex：{ex.Message}");
        //            //shop.ShopId = "10207058";
        //            //return Redirect($"{CustomerConfig.AuthCallbackUrl.TrimEnd("/")}/auth/authmessage?Code=100&Second=5&TransferUrl={returnUrl}&Message={Server.UrlEncode($"授权回调同步店铺信息失败：{ex.Message}")}");
        //        }

        //        var platformType = (PlatformType)Enum.Parse(typeof(PlatformType), shop.PlatformType);
        //        //保存店铺
        //        shop = _shopService.AddShop(new Data.Model.AlibabaMemberToken()
        //        {
        //            Access_Token = shop.AccessToken,
        //            Refresh_Token = shop.RefreshToken,
        //            Resource_Owner = shop.ShopName,
        //            MemberId = shop.ShopId,
        //            UserId = shop.Uid,
        //            VenderId = shop.VenderId,
        //        }, platformType);
        //        try
        //        {
        //            //同步过期时间
        //            SyncShopExpiredTime(shop);
        //        }
        //        catch
        //        {
        //        }
        //        var link = CustomerConfig.GetShopRedirectLink(shop.PlatformType, shop.Version, shop.VenderId);
        //        if (!string.IsNullOrEmpty(state))
        //        {
        //            try
        //            {
        //                var stateModel = JsonConvert.DeserializeObject<AuthRedirectModel>(state);
        //                //分单系统授权店铺处理
        //                if (stateModel.AType != "3")
        //                {
        //                    var targetToken = stateModel.Token;
        //                    if (!string.IsNullOrEmpty(stateModel.MToken))
        //                        targetToken = stateModel.MToken;
        //                    var path = stateModel.AType == "Waybill" ? "/AccountList" : "";
        //                    link = stateModel.Url + path + "?token=" + stateModel.Token;
        //                    if (stateModel.AType == "Waybill")
        //                        IfShopIsSupportWaybillThenSaveIt(shop, targetToken);
        //                }
        //                else
        //                {
        //                    try
        //                    {
        //                        if (string.IsNullOrWhiteSpace(stateModel.Token))
        //                        {
        //                            Log.WriteError("商家关联店铺授权，未传递商家的token，无法添加商家与店铺的关系");
        //                        }
        //                        else
        //                        {
        //                            //1.解析token 得到 商家id
        //                            var loginTokenId = DES.DecryptUrl(stateModel.Token, CustomerConfig.LoginCookieEncryptKey).ToInt();
        //                            var loginAuthToken = _shopService.GetToken(loginTokenId);
        //                            var fxUserId = loginAuthToken.FxUserId;

        //                            //先判断店铺是否被商家关联，如果已经被其他商家关联，则不再添加关联关系
        //                            var shoplist = _fxUserShopService.GetList(shopId: shop.Id);
        //                            var isOtherUserAuthed = false; //店铺是否被其他商家关联了
        //                            if (shoplist.Any(f => f.FxUserId != fxUserId))
        //                            {
        //                                isOtherUserAuthed = true;
        //                                var fxshop = shoplist.FirstOrDefault();
        //                                var url = "http://jd3.dgjapp.com/auth/authsuccess";
        //                                if (string.IsNullOrWhiteSpace(stateModel.SuccToUrl))
        //                                    stateModel.SuccToUrl = url;
        //                                stateModel.SuccToUrl = stateModel.SuccToUrl.TrimEnd(',');
        //                                var uri = new Uri(stateModel.SuccToUrl);
        //                                var showName = string.IsNullOrWhiteSpace(fxshop.FxUserName) ? fxshop.FxUserMobile : fxshop.FxUserName + "|" + fxshop.FxUserMobile;
        //                                if (string.IsNullOrEmpty(uri.Query))
        //                                {
        //                                    url = $"{stateModel.SuccToUrl}?message={Server.UrlEncode($"店铺已经被商家【{showName}】关联")}&type=2";
        //                                }
        //                                else if (uri.Query.StartsWith("?"))
        //                                {
        //                                    url = $"{stateModel.SuccToUrl}&message={Server.UrlEncode($"店铺已经被商家【{showName}】关联")}&type=2";
        //                                }
        //                                stateModel.SuccToUrl = url;
        //                            }

        //                            if (isOtherUserAuthed == false)
        //                            {
        //                                //只有分单应用和打单应用分开的平台，才需要将分单应用的授权信息单独保存
        //                                var fendanAppAlonePts = (new CommonSettingService()).Get<List<string>>("/SystemSetting/FenDanAppAlonePt", 0);
        //                                if (fendanAppAlonePts != null && fendanAppAlonePts.Contains(shop.PlatformType))
        //                                {
        //                                    //2.保存店铺accesstoken,refreshtoken和授权应用的AppKey，AppSecret
        //                                    _shopExtensionService.AddShopExtension(new ShopExtension()
        //                                    {
        //                                        ShopId = shop.Id,
        //                                        AppKey = shopObj.AppKey ?? "",
        //                                        AppSecret = shopObj.Secret ?? ""
        //                                    });
        //                                }

        //                                //3.绑定关系
        //                                _fxUserShopService.AddFxUserShop(new FxUserShop()
        //                                {
        //                                    FxUserId = fxUserId,
        //                                    ShopId = shop.Id,
        //                                    PlatformType = shop.PlatformType,
        //                                    NickName = shop.NickName,
        //                                    CreateTime = DateTime.Now,
        //                                    AuthTime = shop.ExpireTime ?? DateTime.Now,
        //                                    Status = FxUserShopStatus.Binded
        //                                });

        //                                //4.保存店铺同步状态数据
        //                                //订单同步状态
        //                                var syncOrderStatus = new SyncStatus()
        //                                {
        //                                    FxUserId = fxUserId,
        //                                    ShopId = shop.Id,
        //                                    SyncType = ShopSyncType.Order,
        //                                    CreateTime = DateTime.Now,
        //                                    Source = OwnerSource.FenDanSystem.ToString()
        //                                };
        //                                _syncStatusService.AddSyncStatus(syncOrderStatus);

        //                                //产品同步状态
        //                                var syncProductStatus = new SyncStatus()
        //                                {
        //                                    FxUserId = fxUserId,
        //                                    ShopId = shop.Id,
        //                                    SyncType = ShopSyncType.Product,
        //                                    CreateTime = DateTime.Now,
        //                                    Source = OwnerSource.FenDanSystem.ToString()
        //                                };
        //                                _syncStatusService.AddSyncStatus(syncProductStatus);

        //                                //5.同步任务
        //                                var syncTask = new SyncTask()
        //                                {
        //                                    FxUserId = fxUserId,
        //                                    ShopId = shop.Id,
        //                                    TaskType = SyncTaskType.Order,
        //                                    Status = SyncTaskStatus.Wait,
        //                                    CreateTime = DateTime.Now,
        //                                    Source = OwnerSource.FenDanSystem.ToString()
        //                                };
        //                                _syncTaskService.AddTask(syncTask);

        //                                CommUtls.WriteToLog($"Entry ==> syncOrderStatus：{syncOrderStatus.ToJson()}\nsyncTask：{syncTask.ToJson()}", "BindShop.txt", "AuthToBindShop");
        //                            }
        //                        }
        //                    }
        //                    catch (Exception ex)
        //                    {
        //                        Log.WriteError("保存分销用户绑定用户授权报错：" + ex.ToString());
        //                    }
        //                }
        //                //没有指定定向路径
        //                if (string.IsNullOrEmpty(stateModel.SuccToUrl))
        //                    return Redirect(link);
        //                else
        //                {
        //                    var url = "";
        //                    var uri = new Uri(stateModel.SuccToUrl);
        //                    if (string.IsNullOrEmpty(uri.Query))
        //                    {
        //                        url = $"{stateModel.SuccToUrl}?token={stateModel.Token}";
        //                    }
        //                    else if (uri.Query.StartsWith("?"))
        //                    {
        //                        url = $"{stateModel.SuccToUrl}&token={stateModel.Token}";
        //                    }
        //                    return Redirect(url);
        //                }
        //            }
        //            catch
        //            {
        //                return Redirect($"{CustomerConfig.AuthCallbackUrl.TrimEnd("/")}/auth/authmessage?Message={Server.HtmlEncode("电子面单授权回调处理失败")}&Code=100&Second=3&TransferUrl={returnUrl}");
        //            }
        //        }
        //        else
        //        {
        //            var token = CreateToken(shop.Id, Request, response: Response);

        //            var redictUrl = $"{link}/?token=" + token;
        //            return Redirect($"{link}/?token=" + token);
        //        }
        //    }
        //    else
        //    {
        //        return RedirectToLogin(shopJson);
        //    }
        //}

        public ActionResult RedirectToLogin(string token)
        {
            if (Request.IsAjaxRequest())
                throw new LogicException("登录信息过期，请重新登录", "401");
            Response.Clear();

            var authEntry = string.Empty;
            var platform = string.Empty;
            if (string.IsNullOrWhiteSpace(token) == false)
            {
                platform = SiteContext.CurrentNoThrow?.CurrentLoginShop?.PlatformType;
            }
            if (string.IsNullOrWhiteSpace(platform) == true)
            {
                platform = CustomerConfig.Platform;
            }

            authEntry = CustomerConfig.GetShopAuthLink(platform);

            var authEntryUrl = $"{authEntry}";
            if (string.IsNullOrWhiteSpace(token) == false)
            {
                authEntryUrl += $"?rp={token}";
            }

            return Redirect(authEntryUrl);
        }

        public ActionResult PddWaybillCallback()
        {
            var cainiaoAuthInfoJson = Request["cainiaoAuthInfoJson"] ?? "";
            var redirect_url = Request["redirect_url"] ?? "";
            var shopId = Request["shopId"] ?? "";

            var authInfo = cainiaoAuthInfoJson.ToObject<CaiNiaoAuthInfo>();
            AddTemplate(authInfo, shopId.ToInt());
            return Redirect("http://auth.dgjapp.com/auth/authsuccess");
        }

        #region 授权中心回调

        #region 面单回调

        public ActionResult WaybillAuthCallback(string state, string code)
        {
            var key = CustomerConfig.JingDongAppKey;
            var secret = CustomerConfig.JingDongAppSecret;
            //换取token
            var url = $"https://open-oauth.jd.com/oauth2/access_token?app_key={key}&app_secret={secret}&grant_type=authorization_code&code={code}";
            var response = WebHelper.HttpWebRequest(url);
            //解析 token
            var result = response.ToObject<dynamic>();
            var model = new Data.Model.AlibabaMemberToken()
            {
                Access_Token = result?.access_token,
                Refresh_Token = result?.refresh_token,
                Resource_Owner = result?.uid,
                MemberId = result?.open_id,
                PlatformType = PlatformType.Jingdong.ToString(),
                RpVal = state
            };
            var platformTypeStr = CustomerConfig.ConvertPlatformType(model.PlatformType);
            var platformType = SiteContext.ConvertPlatform(platformTypeStr);
            var shop = _shopService.AddShop(model, platformType);

            var stateModel = JsonConvert.DeserializeObject<AuthRedirectModel>(state);
            var targetToken = stateModel.Token;
            if (!string.IsNullOrEmpty(stateModel.MToken))
                targetToken = stateModel.MToken;
            {

                IfShopIsSupportWaybillThenSaveIt(shop, targetToken);
            }
            return Redirect("/AccountList/Index?token=" + stateModel.Token);
        }

        #endregion

        #region 店铺授权回调

        public void ShopAuthCallback()
        {

        }

        #endregion

        #endregion


        #region 菜鸟官方授权
        public ActionResult CaiNiaoCloud(CaiNiaoPostModel model)
        {
            if (model == null)
                return Json(new { Success = false, Message = "数据为空，或数据格式无法解析" });
            if (string.IsNullOrEmpty(model.MemberId))
                return Json(new { Success = false, Message = "MemberId 不能为空" });
            if (string.IsNullOrEmpty(model.LinkToken))
                return Json(new { Success = false, Message = "账号Token 不能为空" });
            if (string.IsNullOrEmpty(model.AccessCode))
                return Json(new { Success = false, Message = "Code 不能为空" });
            if (string.IsNullOrEmpty(model.NewAppUrl))
                return Json(new { Success = false, Message = "链接不能为空" });

            bool isTure = false;

            try
            {
                int shopId = Convert.ToInt32(model.ShopId);

                if (model.IsParent == "true")//是否同步给主店铺
                {
                    Shop masterShop = new SiteContext(Convert.ToInt32(model.ShopId)).MasterShop;
                    if (masterShop != null)
                        shopId = masterShop.Id;
                }
                var cainiaoUrl = CustomerConfig.CaiNiaoUrl;
                var accessCode = model.AccessCode;
                var caiNiaoAppKey = CustomerConfig.CaiNiaoAppKey;
                var caiNiaoAppSecret = CustomerConfig.CaiNiaoAppSecret;
                var sign = CommUtls.MD5(accessCode + "," + caiNiaoAppKey + "," + caiNiaoAppSecret);

                var url = cainiaoUrl + "/api/permission/exchangeToken.do?accessCode=" + accessCode + "&isvAppKey=" + caiNiaoAppKey + "&sign=" + sign;
                var response = WebHelper.HttpWebRequest(url);
                //var result = response.ToObject<dynamic>();


                var result = JsonConvert.DeserializeObject<JToken>(response);
                if (result?.Value<string>("success") == "true" || result?.Value<string>("success") == "True")
                {
                    var accessTokens = result?.Value<JArray>("accessTokens")?.ToList();

                    var access_Token = "";
                    var cainiaoMemberId = "";
                    if (accessTokens.Count > 0)
                    {
                        access_Token = Convert.ToString(accessTokens[0]["accessToken"]);
                        cainiaoMemberId = Convert.ToString(accessTokens[0]["grantBy"]);

                        isTure = true;

                        CaiNiaoAuthInfo cainiaoModel = caiNiaoAuthInfoService.GetModel(cainiaoMemberId, "link");
                        int newId = 0;
                        if (cainiaoModel != null)
                        {
                            //修改token
                            cainiaoModel.AccessToken = access_Token;
                            cainiaoModel.RefreshToken = access_Token;
                            cainiaoModel.AuthAccountId = cainiaoMemberId;
                            cainiaoModel.AuthAccountName = cainiaoMemberId;
                            cainiaoModel.AuthTime = DateTime.Now;
                            cainiaoModel.AuthType = "link";
                            caiNiaoAuthInfoService.UpdateCaiNiaoAuthInfo(cainiaoModel);
                            newId = cainiaoModel.Id;
                        }
                        else
                        {
                            //添加账号信息
                            cainiaoModel = new CaiNiaoAuthInfo();
                            cainiaoModel.AccessToken = access_Token;
                            cainiaoModel.RefreshToken = access_Token;
                            cainiaoModel.AuthAccountId = cainiaoMemberId;
                            cainiaoModel.AuthAccountName = cainiaoMemberId;
                            cainiaoModel.AuthTime = DateTime.Now;
                            cainiaoModel.CreateTime = DateTime.Now;
                            cainiaoModel.AuthAppKey = "";
                            cainiaoModel.AuthType = "link";
                            newId = caiNiaoAuthInfoService.NewCaiNiaoAuthInfo(cainiaoModel);

                            cainiaoModel.Id = newId;
                        }

                        //账号和店铺的关系
                        CainiaoAuthOwner cainiaoAuthOwner = cainiaoAuthOwnerService.GetModel(newId, shopId);
                        if (cainiaoAuthOwner != null)
                        {
                            cainiaoAuthOwner.IsDeleted = false;
                            cainiaoAuthOwnerService.UpdateCainiaoAuthOwner(cainiaoAuthOwner);
                        }
                        else
                        {
                            cainiaoAuthOwner = new CainiaoAuthOwner();
                            cainiaoAuthOwner.CaiNiaoAuthInfoId = newId;
                            cainiaoAuthOwner.ShopId = shopId;
                            cainiaoAuthOwner.MemberId = model.MemberId;
                            cainiaoAuthOwner.IsDeleted = false;
                            cainiaoAuthOwnerService.NewCainiaoAuthOwner(cainiaoAuthOwner);
                        }

                        //添加模板
                        AddTemplate(cainiaoModel, shopId);

                    }//accessTokens.Count end

                }//success true  end

            }
            catch (Exception ex)
            {
                Log.WriteError($"添加授权link账号：{ex}");
                return Json(new { Success = false, Message = "授权异常，请重试或者联系客服" });
            }

            if (isTure)
                //return Json(new { Success = true, RedirectUrl = $"{model.NewAppUrl}/AccountList/?token={model.LinkToken}", Message = "成功" });
                return Json(new { Success = true, RedirectUrl = $"{model.NewAppUrl}/auth/authsuccess/?token={model.LinkToken}", Message = "成功" });
            else
                return Json(new { Success = false, Message = "授权失败!" });
        }
        #endregion

        #region 创建授权

        //[Obsolete("该方法分单系统不能使用，请使用CreateFxToken")]
        //public static string CreateToken(int shopId, HttpRequestBase request, bool isQuickLink = false, bool isFromParent = false, int userId = 0, int fromId = 0, int subUserId = 0, HttpResponseBase response = null)
        //{
        //    throw new LogicException("登录已过期，请重新登录 INVALID_OPERATOR");
        //    var hashCode = request.Headers.Get("User-Agent").ToShortMd5();
        //    var sign = hashCode.ToString();
        //    //var _macService = new MacAddressService();
        //    //var mac = _macService.Get(request.UserHostAddress);
        //    //var sign = mac.GetHashCode().ToString();
        //    var ip = request?.Headers["X-Forwarded-For"];
        //    if (string.IsNullOrEmpty(ip))
        //        ip = request?.UserHostAddress;
        //    var model = new ShopService().CreateToken(shopId, sign, isQuickLink, isFromParent, userId, fromId, subUserId, ip);

        //    var token = DES.EncryptUrl($"{model.Id}", CustomerConfig.LoginCookieEncryptKey);

        //    //添加cookie
        //    AddLoginCookie(model.Id, response);

        //    return token;
        //}

        public static string CreateFxToken(int shopId, HttpRequestBase request, bool isQuickLink = false, bool isFromParent = false, int fxUserId = 0, int fromId = 0, int subUserId = 0, HttpResponseBase response = null)
        {
            var hashCode = request.Headers.Get("User-Agent").ToShortMd5();
            var sign = hashCode.ToString();
            var ip = request?.Headers["X-Forwarded-For"];
            if (string.IsNullOrEmpty(ip))
                ip = request?.UserHostAddress;
            var shopService = new ShopService();
            var model = shopService.CreateFxToken(fxUserId, sign, shopId, isQuickLink, isFromParent, fromId, subUserId, ip);

            var token = DES.EncryptUrl($"{model.Id}", CustomerConfig.LoginCookieEncryptKey);

            //添加cookie
            AddLoginCookie(model.Id, response);
                
            return token;
        }

        public static Tuple<string, LoginAuthToken> CreateFxTokenNew(int shopId, HttpRequestBase request, bool isQuickLink = false, bool isFromParent = false, int fxUserId = 0, int fromId = 0, int subUserId = 0, HttpResponseBase response = null)
        {
            var hashCode = request.Headers.Get("User-Agent").ToShortMd5();
            var sign = hashCode.ToString();
            var ip = request?.Headers["X-Forwarded-For"];
            if (string.IsNullOrEmpty(ip))
                ip = request?.UserHostAddress;
            var shopService = new ShopService();
            var model = shopService.CreateFxToken(fxUserId, sign, shopId, isQuickLink, isFromParent, fromId, subUserId, ip);

            var token = DES.EncryptUrl($"{model.Id}", CustomerConfig.LoginCookieEncryptKey);

            //添加cookie
            AddLoginCookie(model.Id, response);

            return Tuple.Create(token, model);
        }

        /// <summary>
        /// 写入登录Cookies
        /// </summary>
        /// <param name="loginAuthTokeId"></param>
        /// <param name="response"></param>
        private static void AddLoginCookie(int loginAuthTokeId, HttpResponseBase response)
        {
            try
            {
                //写入cookie
                var loginExpireHoursKey = CustomerConfig.FxSystemLoginExpireHours;
                double hours = new CommonSettingService().Get(loginExpireHoursKey, 0)?.Value.ToDouble() ?? 0;
                if (hours <= 0)
                    hours = 24 * 15 ;
                var expireTime = DateTime.Now.AddHours(hours);
                var key = CustomerConfig.FxSystemLoginCookieKey.Replace("$id", loginAuthTokeId.ToString());
                var val = DES.EncryptUrl($"{loginAuthTokeId},{expireTime.ToString("yyyy-MM-dd HH:mm:ss")}", CustomerConfig.LoginCookieEncryptKey);
                var cookie = new HttpCookie(key, val);
                cookie.Domain = ".dgjapp.com";
                cookie.Expires = expireTime;
                if (response == null)
                {
                    System.Web.HttpContext.Current.Response.Cookies.Add(cookie); ;
                }
                else
                {
                    response.Cookies.Add(cookie);
                }
                //日志
                LoginLogService.Instance.WriteLog(new LoginLogModel { 
                    OperationType = "CreateFxToken",
                    ExpireTime = expireTime,
                    LoginAuthTokenId = loginAuthTokeId
                });

            }
            catch (Exception ex)
            {
                //日志
                LoginLogService.Instance.WriteLog(new LoginLogModel
                {
                    OperationType = "CreateFxToken",
                    LoginAuthTokenId = loginAuthTokeId,
                    Ext1 = "Exception",
                    Ext2 = ex.Message
                });

                Log.WriteError($"CreateFxToken写入Cookies异常：{ex}");
            }
        }

        //public static string CreateToken2(int shopId, HttpRequestBase request, bool isQuickLink = false, bool isFromParent = false, int fromId = 0, int userId = 0)
        //{
        //    var hashCode = request.Headers.Get("User-Agent").ToShortMd5();
        //    var sign = hashCode.ToString();
        //    //var _macService = new MacAddressService();
        //    //var mac = _macService.Get(request.UserHostAddress);
        //    //var sign = mac.GetHashCode().ToString();
        //    var model = new ShopService().CreateToken(shopId, sign, isQuickLink, isFromParent, fromId, userId);
        //    var token = DES.EncryptUrl($"{model.Id}", CustomerConfig.LoginCookieEncryptKey);
        //    return token;
        //}

        #endregion

        #region 工具方法
        //[NeedToken]
        //public ActionResult Set(string id)
        //{
        //    if (!CustomerConfig.IsDebug)
        //        return Content("No Permission");
        //    if (string.IsNullOrEmpty(id))
        //        return Content("No Permission");

        //    //ip白名单控制，读取ip白名单
        //    if (!IpWhiteCheck(Request))
        //        return FalidResult("IP白名单限制");

        //    Shop shop = null;
        //    if (id.ToInt() > 0)
        //        shop = _shopService.Get(id.ToInt());
        //    else
        //    {
        //        shop = _shopService.Get(" where ShopId=@ShopId  ORDER BY id DESC", new { ShopId = id })?.FirstOrDefault();
        //        if (shop == null)
        //            shop = _shopService.Get(" where ShopName=@ShopId  ORDER BY id DESC", new { ShopId = id })?.FirstOrDefault();
        //        if (shop == null)
        //            shop = _shopService.Get(" where NickName=@ShopId  ORDER BY id DESC", new { ShopId = id })?.FirstOrDefault();
        //    }
        //    if (shop != null)
        //    {
        //        var url = CustomerConfig.GetShopRedirectLink(shop.PlatformType, shop.Version, shop.VenderId);
        //        //SetAuthCookie(shop.Id, Request, Response);
        //        var t = CreateToken(shop.Id, Request);
        //        return Redirect($"{url}/?token=" + t);
        //    }
        //    return Content("No Permission");
        //}

        //public ActionResult AddMany(string id, string pt = "", string time = "", int isall = 0, int isInvite = 0)
        //{
        //    var platformType = CustomerConfig.ConvertPlatformType(pt);//ConvertPlatformType(pt);
        //    if (string.IsNullOrWhiteSpace(platformType) || platformType.Contains(","))
        //        return Content("请提供迁移用户所在平台，例如：pt=1688");
        //    if (string.IsNullOrEmpty(id))
        //        return Content("请提供要迁移用户的旺旺号(1688、淘宝)，拼多多请提供用户名或登录名");
        //    var rs = new List<string>();
        //    id.Split('，', ',').ToList()?.ForEach(m =>
        //    {
        //        var r = _shopService.TryAddMigrateShopOneByOne(m, platformType, !string.IsNullOrEmpty(time), isall == 1, isInvite == 1);
        //        rs.Add(r);
        //    });
        //    //var mid = _shopService.TryAddMigrateShop(id.Split('，', ',').ToList(), platformType.Value.ToString(),!string.IsNullOrEmpty(time));
        //    return Content(string.Join("\r\n", rs));
        //}
        //public ActionResult Return(string id, string pt = "")
        //{
        //    var platformType = CustomerConfig.ConvertPlatformType(pt);//ConvertPlatformType(pt);
        //    if (string.IsNullOrWhiteSpace(platformType) || platformType.Contains(","))
        //        return Content("请提供迁移用户所在平台，例如：pt=1688");
        //    if (string.IsNullOrEmpty(id))
        //        return Content("请提供返回用户的旺旺号(1688、淘宝)，拼多多请提供用户名或登录名");
        //    var rs = new List<string>();
        //    id.Split('，', ',').ToList()?.ForEach(m =>
        //    {
        //        var r = _shopService.TryReturnShopOneByOne(m, platformType);
        //        rs.Add(r);
        //    });
        //    return Content(string.Join("\r\n", rs));
        //}
        //public ActionResult ResetMany(string id, string pt = "")
        //{
        //    var platformType = CustomerConfig.ConvertPlatformType(pt);//ConvertPlatformType(pt);
        //    if (string.IsNullOrWhiteSpace(platformType) || platformType.Contains(","))
        //        return Content("请提供迁移用户所在平台，例如：pt=1688");
        //    if (string.IsNullOrEmpty(id))
        //        return Content("请提供要迁移用户的旺旺号(1688、淘宝)，拼多多请提供用户名或登录名");
        //    var mids = _shopService.ParseWangwangToMemberId(id.Split('，', ',').ToList(), platformType);
        //    if (mids == null || !mids.Any())
        //        return Content("根据旺旺号没有查询到对应的用户，请检查用户名是否正确");
        //    var dict = new Dictionary<string, string>();

        //    var platformTypeEnum = SiteContext.ConvertPlatform(platformType);

        //    mids.ForEach(x =>
        //    {
        //        var msg = ResetMigrateShop(x, platformTypeEnum);
        //        dict.Add(x, msg);
        //    });
        //    return Content(JsonExtension.ToJson(dict));
        //}

        //public ActionResult ReFullSync(string id)
        //{
        //    if (string.IsNullOrEmpty(id))
        //        return Content("No Permission");
        //    var shop = _shopService.Get("where ShopId=@sid", new { sid = id })?.FirstOrDefault();
        //    if (shop != null)
        //    {
        //        shop.FullSyncCompleteTime = null;
        //        shop.FullSyncStatus = null;
        //        _shopService.Update(shop);
        //        return Content("操作成功，正在后台进行全量同步");
        //    }
        //    else
        //        return Content("Not Exist");
        //}

        //private string ResetMigrateShop(string id, PlatformType platformType)
        //{
        //    if (string.IsNullOrEmpty(id))
        //        return ("No Permission");
        //    var shop = _shopService.Get("where ShopId=@sid Or ShopName=@sid", new { sid = id })?.FirstOrDefault();
        //    if (shop == null)
        //    {
        //        _shopService.TryAddMigrateShop(id, platformType.ToString());
        //        return "已加入迁移列表";
        //    }
        //    else
        //    {
        //        //var sc = new SiteContext(shop.Id);
        //        //var shops = sc.AllShops;
        //        //shops.ForEach(s =>
        //        //{
        //        //    _shopService.ResetShopData(s);
        //        //});
        //        shop.DbConfig = Data.Extension.DbPolicyExtension.GetConfig(shop.Id);
        //        _shopService.ResetShopData(shop);
        //        _shopService.TryAddMigrateShop(id, platformType.ToString());
        //        return ($"操作成功，已清除该店铺所有数据");
        //    }
        //}

        public ActionResult Logout()
        {
            var ck = new HttpCookie("auth");
            ck.Expires = DateTime.Now.AddYears(-1);
            Response.Cookies.Add(ck);

            //移除Cookies
            var token = Request["token"];
            if (!string.IsNullOrEmpty(token))
            {
                var loginAuthTokeId = DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey).ToInt();
                var key = CustomerConfig.FxSystemLoginCookieKey.Replace("$id", loginAuthTokeId.ToString());
                var loginCookie = Request.Cookies[key];
                if(loginCookie != null)
                {
                    loginCookie = new HttpCookie(key);
                    loginCookie.Domain = ".dgjapp.com";
                    loginCookie.Expires = DateTime.Now.AddYears(-1);
                    Response.Cookies.Set(loginCookie);
                }
                else
                {
                    //日志
                    LoginLogService.Instance.WriteLog(new LoginLogModel
                    {
                        OperationType = "Logout",
                        LoginAuthTokenId = loginAuthTokeId,
                        Ext1 = $"找不到{key}的Cookie"
                    });
                }

                //日志
                LoginLogService.Instance.WriteLog(new LoginLogModel
                {
                    OperationType = "Logout",
                    LoginAuthTokenId = loginAuthTokeId
                });
            }

            return new EmptyResult();
        }

        private LoginAuthToken ExpainToken(string token)
        {
            if (string.IsNullOrEmpty(token))
                return null;
            try
            {
                var cilentSign = Request?.UserAgent?.ToShortMd5().ToString();
                var model = _shopService.ExpainToken(token, cilentSign);
                return model;
            }
            catch (Exception)
            {
                return null;
            }
        }

        //public ActionResult Explain(string id)
        //{
        //    var token = ExpainToken(id);
        //    return Content(JsonExtension.ToJson(token));
        //}

        //public ActionResult Empty()
        //{
        //    return View();
        //}

        //public ActionResult AddInvited(string id, string pt = "")
        //{
        //    var platformTypeStr = CustomerConfig.ConvertPlatformType(pt);
        //    if (string.IsNullOrWhiteSpace(platformTypeStr) || platformTypeStr.Contains(","))
        //        return Content("请提供迁移用户所在平台，例如：pt=1688");
        //    if (string.IsNullOrEmpty(id))
        //        return AjaxContentWithCallback("请提供要迁移用户的旺旺号(1688、淘宝)，拼多多请提供用户名或登录名");
        //    var mid = _shopService.TryAddInviteMigrateShop(id.Split('，', ',').ToList(), platformTypeStr);
        //    return AjaxContentWithCallback(mid.ToString());
        //}

        ///// <summary>
        ///// 
        ///// </summary>
        ///// <returns></returns>
        //public ActionResult SetInvitedStatus(string id, string pt, string status)
        //{
        //    if (string.IsNullOrEmpty(pt) || string.IsNullOrEmpty(id))
        //        return AjaxContentWithCallback("");
        //    pt = CustomerConfig.ConvertPlatformType(pt);
        //    if (string.IsNullOrEmpty(pt))
        //        return AjaxContentWithCallback("");
        //    if (string.IsNullOrEmpty(status))
        //        return AjaxContentWithCallback("");
        //    var model = _shopService.GetInvitedMigrateShop(pt, id);
        //    if (model == null)
        //        return AjaxContentWithCallback("");
        //    else
        //    {
        //        model.Status = status;
        //        _shopService.UpdateInvitedMigrateShop(model);
        //        return AjaxContentWithCallback(model.Status);
        //    }
        //}

        public ContentResult AjaxContentWithCallback(string msg)
        {
            var callback = Request["callback"];
            if (!string.IsNullOrEmpty(callback))
                return Content($"{callback}('{msg}')");
            else
                return Content(msg);
        }
        /// <summary>
        /// 工具页，供内部使用
        /// </summary>
        /// <returns></returns>
        [NeedToken]
        public ActionResult Tool(int id = 0)
        {
            ViewBag.Logo = "店管家内部工具";
            if (id == 777)
                ViewBag.admin = "1";
            if (CustomerConfig.IsDebug)
            {
                //线程检查处理,不影响其他操作
                ThreadPool.QueueUserWorkItem(state =>
                {
                    try
                    {
                        new CommService().GetCndzkChinaSubDivisionsSync();
                    }
                    catch { }
                });

                return View();
            }
            else
                return Content("No Permission");
        }
        #endregion

        #region 迁移至MongoDB

        //public ActionResult Mongo(string id, string pt = "")
        //{
        //    var platformType = CustomerConfig.ConvertPlatformType(pt);
        //    if (platformType == null)
        //        return Content("请提供迁移用户所在平台，例如：pt=1688");
        //    if (string.IsNullOrEmpty(id))
        //        return Content("请提供要迁移用户的旺旺号(1688、淘宝)，拼多多请提供用户名或登录名");
        //    var rs = new List<string>();
        //    id.Split('，', ',').ToList()?.ForEach(m =>
        //    {
        //        var r = _shopService.TryAddMongoShopOneByOne(m, platformType.ToString());
        //        rs.Add(r);
        //    });
        //    //var mid = _shopService.TryAddMigrateShop(id.Split('，', ',').ToList(), platformType.Value.ToString(),!string.IsNullOrEmpty(time));
        //    return Content(string.Join("\r\n", rs));
        //}

        //public ActionResult SwitchToMongo(string id, string pt = "")
        //{
        //    try
        //    {
        //        var platformType = CustomerConfig.ConvertPlatformType(pt);
        //        if (platformType == null)
        //            return Content("请提供迁移用户所在平台，例如：pt=1688");
        //        if (string.IsNullOrEmpty(id))
        //            return Content("请提供要迁移用户的旺旺号(1688、淘宝)，拼多多请提供用户名或登录名");
        //        var shopName = id.Split(',').FirstOrDefault();
        //        var msg = _shopService.TryAddMongoShopOneByOne(shopName, platformType.ToString());
        //        var shop = _shopService.Get(" where ShopName =@name AND PlatformType=@pt", new { name = shopName, pt = platformType.ToString() }).FirstOrDefault();
        //        if (shop != null && msg.Contains("添加成功"))
        //        {
        //            ThreadPool.QueueUserWorkItem(state =>
        //            {
        //                PolicyService.InitMongoDBOrders(new List<int> { shop.Id });
        //            });
        //            return Content($"正在切换至MongoDB，期间请不要操作系统");
        //        }
        //        else
        //            return Content(msg);
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriteError(ex.ToString());
        //        return Content("服务繁忙，请稍后重试");
        //    }
        //}

        #endregion


        #region 压力测试     
        private OrderMongoRepository _repository;

        //public ContentResult TestInsertOrders(string shopNames, string pt = "")
        //{
        //    List<string> shopNameLst = new List<string>();
        //    try
        //    {
        //        shopNameLst = shopNames.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).ToList();
        //        if (shopNameLst.Count == 0)
        //            return Content("必须填写店铺名称");

        //        shopNameLst.ForEach(shopName =>
        //        {
        //            SwitchToMongo(shopName, pt);
        //        });

        //        return Content("正在迁移数据……");
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriteError("MongoDB数据迁移异常：" + ex.Message);
        //        return Content("MongoDB数据迁移异常：" + ex.Message);
        //    }
        //}

        //public void TestUpdateBalance(int tryCount = 10)
        //{
        //    try
        //    {
        //        ShopService _shopService = new ShopService();
        //        var shops = _shopService.Get().ToList();

        //        List<string> status = new List<string>() { "waitbuyerpay", "waitsellersend", "waitbuyerreceive", "success", "cancel", "confirm_goods_but_not_fund" };
        //        var random = new Random();
        //        for (int i = 1; i <= tryCount; i++)
        //        {
        //            List<double> updateTimes = new List<double>();
        //            List<double> queryTimes = new List<double>();
        //            Parallel.ForEach(shops, shop =>
        //            {
        //                try
        //                {
        //                    _repository = new OrderMongoRepository();
        //                    CommonSettingService _commonSettingService = new CommonSettingService();

        //                    StringBuilder log = new StringBuilder();
        //                    var filterDoc = new BsonDocument("ShopId", shop.Id);
        //                    var count = _repository.Count(filterDoc);
        //                    if (count == 0) return;

        //                    log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】订单总数：{count}");
        //                    //int pageCount = Math.Ceiling(count * 1.0 / 500).ToInt();
        //                    int pageIndex = 1;//new Random(shop.Id).Next(1, pageCount);
        //                    var model = new OrderSearchModel() { PageIndex = pageIndex, PageSize = 500, NeedPagging = true };
        //                    model.Filters = new List<OrderSearchFieldModel>()
        //                    {
        //                        new OrderSearchFieldModel() { TableAlias="o",TableName="P_Order",Name="ShopId",Contract="in",Value=shop.Id.ToString(),FieldType="int"},
        //                        new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "PlatformStatus", Contract = "=", Value = status[new Random(shop.Id).Next(0,status.Count-1)], FieldType = "string" },
        //                        new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "CreateTime", Contract = "between", Value = "2019-03-01 00:00:00", ExtValue = "2019-06-20 15:59:59.999", FieldType = "datetime" }
        //                    };

        //                    var sw1 = new Stopwatch();
        //                    sw1.Start();
        //                    var systemSetting = _commonSettingService.GetSystemSetting(shop.Id);
        //                    var result = _repository.GetOrders(model, systemSetting);
        //                    var orders = result?.Rows?.ToList() ?? new List<Order>();
        //                    var queryCondition = result?.QueryCondition.ToString2();
        //                    log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】订单数量：{orders.Count}");
        //                    log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】查询条件：{queryCondition}");
        //                    sw1.Stop();
        //                    var queryElapsedTime = sw1.Elapsed.TotalMilliseconds;
        //                    queryTimes.Add(queryElapsedTime);
        //                    log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】查询{(pageIndex - 1) * 500}~{pageIndex * 500}条订单数据耗时：{queryElapsedTime}(ms)");

        //                    if (orders == null || orders.Count == 0)
        //                        return;

        //                    var sw2 = new Stopwatch();
        //                    sw2.Start();

        //                    orders.ForEach(o =>
        //                    {
        //                        var index = status.IndexOf(o.PlatformStatus);
        //                        o.PlatformStatus = status[random.Next(index, status.Count - 1)];
        //                        o.PayTime = DateTime.Now;
        //                        o.ModifyTime = DateTime.Now.AddMinutes(random.Next(-10, 100));
        //                        o.SellerRemark = new Guid().ToString("N");
        //                        o.BuyerRemark = new Guid().ToString("N");
        //                        o.ToName = "大锤-" + new Guid().ToString("N");
        //                        o.ToPhone = "136" + random.Next(1000, 9999) + random.Next(1000, 9999);
        //                        foreach (var item in o.OrderItems)
        //                        {
        //                            item.Color = "红色" + random.Next(1, 1000);
        //                            item.Size = "XL" + random.Next(1, 1000);
        //                        }
        //                    });
        //                    _repository.BulkMerger(orders);
        //                    sw2.Stop();
        //                    var updateElapsedTime = sw2.Elapsed.TotalMilliseconds;
        //                    log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】批量更新{orders.Count}条数据，耗时：{updateElapsedTime}(ms)");
        //                    updateTimes.Add(updateElapsedTime);

        //                    Log.WriteLine(log.ToString2());
        //                    //ThreadPool.QueueUserWorkItem(state =>
        //                    //{

        //                    //});
        //                }
        //                catch (Exception ex)
        //                {
        //                    Log.WriteError($"【TestUpdateBalance】 循环内异常：{ex.Message}\n{ex.StackTrace}");
        //                }
        //            });
        //            //Thread.Sleep(100);

        //            string statistic = string.Empty;
        //            if (queryTimes.Count > 0)
        //                statistic += $"查询最大耗时：{queryTimes.Max()}(ms),查询最小耗时：{queryTimes.Min()}";
        //            if (updateTimes.Count > 0)
        //                statistic += (statistic.IsNullOrEmpty() ? "" : "|||") + $"更新最大耗时：{updateTimes.Max()}(ms),更新最小耗时：{updateTimes.Min()}";
        //            Log.WriteLine($"第{i}次循环------{statistic}");
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriteError($"【TestUpdateBalance】异常：{ex.Message}\n{ex.StackTrace}");
        //    }
        //}

        //public void TestQueryBalance(int tryCount = 10)
        //{
        //    try
        //    {
        //        ShopService _shopService = new ShopService();
        //        var shops = _shopService.Get().ToList();
        //        List<string> status = new List<string>() { "waitbuyerpay", "waitsellersend", "waitbuyerreceive", "success", "cancel", "confirm_goods_but_not_fund" };
        //        List<string> orderByFields = new List<string>() { "o.CreateTime", "o.PayTime", "o.LastExpressPrintTime", "o.TotalAmount", "o.ToFullAddress", "oi.ProductSubject", "oi.ProductCargoNumber", "oi.Color", "o.ProductItemCount", "o.TotalWeight" };
        //        for (int i = 1; i <= tryCount; i++)
        //        {
        //            List<double> times = new List<double>();
        //            Parallel.ForEach(shops, shop =>
        //            {
        //                try
        //                {
        //                    _repository = new OrderMongoRepository();
        //                    CommonSettingService _commonSettingService = new CommonSettingService();

        //                    StringBuilder log = new StringBuilder();
        //                    var filterDoc = new BsonDocument("ShopId", shop.Id);
        //                    var count = _repository.Count(filterDoc);
        //                    if (count == 0) return;


        //                    log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】订单总数：{count}");
        //                    int pageCount = Math.Ceiling(count * 1.0 / 500).ToInt();
        //                    int pageIndex = 1;//new Random().Next(1, pageCount);
        //                    var model = new OrderSearchModel() { PageIndex = pageIndex, PageSize = 500, NeedPagging = true, OrderByField = orderByFields[new Random(shop.Id).Next(0, orderByFields.Count - 1)], IsOrderDesc = shop.Id % 2 == 0 ? true : false };
        //                    model.Filters.Add(new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "ShopId", Contract = "in", Value = shop.Id.ToString(), FieldType = "int" });
        //                    model.Filters.Add(new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "CreateTime", Contract = "between", Value = "2019-03-18 00:00:00", ExtValue = "2019-06-20 15:59:59.999", FieldType = "datetime" });
        //                    model.Filters.Add(new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "PlatformStatus", Contract = "=", Value = status[new Random(shop.Id).Next(0, status.Count - 1)], FieldType = "string" });
        //                    var filters = new List<OrderSearchFieldModel>()
        //                    {
        //                        new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="IsPreordain",Contract="=",Value="0",FieldType="bool"},
        //                        new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="productCargoNumber",Contract="!=",Value="",FieldType="string"},
        //                        new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="Color",Contract="!=",Value="黑色",FieldType="string"},
        //                        new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="Size",Contract="like",Value="XL",FieldType="string"},
        //                    };

        //                    model.Filters.Add(filters[new Random(shop.Id).Next(0, filters.Count - 1)]);
        //                    model.Filters.Add(filters[new Random(shop.Id).Next(0, filters.Count - 1)]);
        //                    model.Filters = model.Filters.Distinct().ToList();

        //                    var queryFields = model.Filters.Select(m => m.Name).ToList();

        //                    var sw1 = new Stopwatch();
        //                    sw1.Start();
        //                    var systemSetting = _commonSettingService.GetSystemSetting(shop.Id);
        //                    filterDoc = _repository.GetWhereSql(model, systemSetting);
        //                    var result = _repository.GetOrders(model, systemSetting);
        //                    var orders = result?.Rows?.ToList() ?? new List<Order>();
        //                    var queryCondition = result?.QueryCondition.ToString2();

        //                    log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】订单数量：{orders.Count}");
        //                    log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】查询条件：{queryCondition}");
        //                    log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】排序字段：{model.OrderByField}，是否倒序：{model.IsOrderDesc}");
        //                    sw1.Stop();
        //                    var elapsedTime = sw1.Elapsed.TotalMilliseconds;
        //                    log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】查询{(pageIndex - 1) * 500}~{pageIndex * 500}条订单数据耗时：{elapsedTime}(ms)");
        //                    times.Add(elapsedTime);

        //                    #region 订单打印订单数据查询条件日志记录
        //                    if (CustomerConfig.IsDebug)
        //                    {
        //                        var path = AppDomain.CurrentDomain.BaseDirectory + "log\\查询Test.log";
        //                        var logTryCount = 5;
        //                        for (int ii = 1; ii <= logTryCount; ii++)
        //                        {
        //                            try
        //                            {
        //                                var sb_log = new StringBuilder();
        //                                var sqllog = $"\r\n-------------------{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}-----------------------\r\n{log.ToString2()}";
        //                                using (StreamWriter sr = new StreamWriter(path, true))
        //                                {
        //                                    sr.WriteLine(sqllog);
        //                                }
        //                                break;
        //                            }
        //                            catch (Exception ex)
        //                            {
        //                                if (ii == logTryCount)
        //                                    Log.WriteError("写入日志异常：" + ex.Message);
        //                            }
        //                            Thread.Sleep(100);
        //                        }
        //                    }
        //                    #endregion
        //                }
        //                catch (Exception ex)
        //                {
        //                    Log.WriteError($"【TestQueryBalance】 循环内异常：{ex.Message}\n{ex.StackTrace}");
        //                }
        //            });
        //            //Thread.Sleep(100);
        //            #region 订单打印订单数据查询条件日志记录
        //            if (CustomerConfig.IsDebug)
        //            {
        //                var path = AppDomain.CurrentDomain.BaseDirectory + "log\\查询Test.log";
        //                var logTryCount = 5;
        //                for (int ii = 1; ii <= logTryCount; ii++)
        //                {
        //                    try
        //                    {
        //                        var sqllog = $"\r\n*********************************第{i}次循环*********************************************\r\n";
        //                        sqllog += $"最大耗时：{times.Max()}(ms),最小耗时：{times.Min()}";
        //                        using (StreamWriter sr = new StreamWriter(path, true))
        //                        {
        //                            sr.WriteLine(sqllog);
        //                        }
        //                        break;
        //                    }
        //                    catch (Exception ex)
        //                    {
        //                        if (ii == logTryCount)
        //                            Log.WriteError("写入日志异常：" + ex.Message);
        //                    }
        //                    Thread.Sleep(100);
        //                }
        //            }
        //            #endregion
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriteError($"【TestQueryBalance】异常：{ex.Message}\n{ex.StackTrace}");
        //    }

        //}
        #endregion

        #region 广告

        #region 广告管理

        //private string _bodyContent = null;
        ///// <summary>
        ///// 获取请求报文
        ///// </summary>
        ///// <returns></returns>
        //private string _getRequestBody()
        //{
        //    var stream = Request.InputStream;
        //    var byts = new byte[stream.Length];
        //    stream.Read(byts, 0, byts.Length);
        //    string req = System.Text.Encoding.UTF8.GetString(byts);
        //    _bodyContent = req;
        //    return req;
        //}

        //private string _getPostParameter(string key)
        //{
        //    if (_bodyContent == null)
        //        _getRequestBody();
        //    if (string.IsNullOrEmpty(_bodyContent))
        //        return "";
        //    else
        //        return JsonConvert.DeserializeObject<JToken>(_bodyContent)?.Value<string>(key);
        //}


        ///// <summary>
        ///// 文件上传
        ///// </summary>
        ///// <returns></returns>
        //[ValidateInput(false)]
        //public ActionResult UploadImage()
        //{
        //    var fileName = _getPostParameter("fileName");
        //    var fileContent = _getPostParameter("fileContent");
        //    string result = null;
        //    string url = null;
        //    try
        //    {
        //        //fileContent =_context.Server.UrlDecode(fileContent);
        //        var para = new Dictionary<string, string> { { "fileName", fileName }, { "fileContent", fileContent }, { "memberId", "Advertisement" }, { "key", DES.EncryptDES(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "sda98yp3") } };
        //        result = WebRequestHelper.PostRequest(CustomerConfig.ImageServer + "/Image.ashx?FuncName=Upload&key=debug", para, "UTF-8");
        //        url = JsonConvert.DeserializeObject<JToken>(result)?.Value<string>("Data");
        //        if (!string.IsNullOrEmpty(url))
        //            url = CustomerConfig.ImageServerOut + url;
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriteError("上传图片时发生错误：" + ex.ToString());
        //    }
        //    return SuccessResult(new { Url = url });
        //}

        ///// <summary>
        ///// 获取广告列表
        ///// </summary>
        ///// <param name="model"></param>
        ///// <returns></returns>
        //public ActionResult GetAdvList(SearchAdvModel model)
        //{
        //    var rs = _advService.GetList(model);
        //    return SuccessResult(rs);
        //}

        ///// <summary>
        ///// 更新广告
        ///// </summary>
        ///// <param name="model"></param>
        ///// <returns></returns>
        //public ActionResult UpdateAdv(Advertisement model)
        //{
        //    if (string.IsNullOrEmpty(model.WhiteShopIds) == false)
        //    {
        //        model.WhiteShopIds = model.WhiteShopIds.TrimStart(',', '，');
        //        model.WhiteShopIds = "," + model.WhiteShopIds;
        //    }
        //    var rs = _advService.Update(model);
        //    return SuccessResult(rs);
        //}

        ///// <summary>
        ///// 更新广告
        ///// </summary>
        ///// <param name="model"></param>
        ///// <returns></returns>
        //public ActionResult DeleteAdv(int advId)
        //{
        //    _advService.Delete(advId);
        //    return SuccessResult();
        //}

        ///// <summary>
        ///// 更新广告
        ///// </summary>
        ///// <param name="model"></param>
        ///// <returns></returns>
        //public ActionResult UpdateAdvStatus(int advId, bool isOnline)
        //{
        //    _advService.UpdateIsOnline(advId, isOnline);
        //    return SuccessResult();
        //}

        ///// <summary>
        ///// 更新广告
        ///// </summary>
        ///// <param name="model"></param>
        ///// <returns></returns>
        //public ActionResult AddAdv(Advertisement model)
        //{
        //    model.CreateTime = DateTime.Now;
        //    if (string.IsNullOrEmpty(model.WhiteShopIds) == false)
        //    {
        //        model.WhiteShopIds = model.WhiteShopIds.TrimStart(',', '，');
        //        model.WhiteShopIds = "," + model.WhiteShopIds;
        //    }
        //    _advService.Add(model);
        //    return SuccessResult(model);
        //}

        #endregion

        #region 广告投放

        //public ActionResult GetShowingAdv(string token)
        //{
        //    var t = ExpainToken(token);
        //    if (t == null)
        //        return SuccessResult();
        //    var shop = _shopService.Get(t.ShopId);
        //    if (shop == null)
        //        return SuccessResult();
        //    var model = _advService.GetShowingAdv(shop.Id, shop.PlatformType);
        //    return SuccessResult(model);
        //}

        //public ActionResult ClickAdv(string token, int advId)
        //{
        //    var t = ExpainToken(token);
        //    var adv = _advService.Get(advId);
        //    var redirectUrl = CustomerConfig.ApplicationWebUrl + "?token=" + token;
        //    if (adv != null && !string.IsNullOrEmpty(adv.JumpLink) && adv.JumpLink.ToLower().Contains("http"))
        //        redirectUrl = adv.JumpLink;
        //    var shopId = 0;
        //    if (t != null)
        //    {
        //        var shop = _shopService.Get(t.ShopId);
        //        if (shop != null)
        //            shopId = shop.Id;
        //    }
        //    _advService.Click(shopId, advId);
        //    return Redirect(redirectUrl);
        //}

        //public ActionResult DontShowAnyMore(string token, int advId)
        //{
        //    var t = ExpainToken(token);
        //    if (t != null)
        //        _advService.DontShowAnyMore(t.ShopId, advId);
        //    return SuccessResult();
        //}


        #endregion

        #endregion

        #region 测试

        public ActionResult Timeout(int id = 0)
        {
            Thread.Sleep(id * 1000);
            return Content("ok");
        }

        #endregion

        #region 订单自动增量同步

        //private static ConcurrentQueue<int> _toIncrimentSyncShops = new ConcurrentQueue<int>();
        //private static readonly object _lockForInitIncrimentShop = new object();

        ///// <summary>
        ///// 获取一个店铺进行增量同步
        ///// </summary>
        ///// <returns></returns>
        //public ActionResult PullSyncShop()
        //{
        //    if (!_toIncrimentSyncShops.Any())
        //    {
        //        lock (_lockForInitIncrimentShop)
        //        {
        //            try
        //            {
        //                if (!_toIncrimentSyncShops.Any())
        //                {
        //                    var service = new ShopService();
        //                    var shopIds = service.GetNeedSyncShopIds();
        //                    shopIds?.ForEach(id =>
        //                    {
        //                        _toIncrimentSyncShops.Enqueue(id);
        //                    });
        //                }
        //            }
        //            catch (Exception ex)
        //            {
        //                Log.WriteError($"获取获取需要增量同步的店铺ID时发生错误：{ex}");
        //            }
        //        }
        //    }
        //    var shopId = 0;
        //    _toIncrimentSyncShops.TryDequeue(out shopId);
        //    if (shopId > 0)
        //        return Content(shopId.ToString());
        //    return Content("");
        //}

        #endregion

        #region 订单修复

        //public ActionResult SyncProvince()
        //{
        //    var os = new OrderService(PlatformType.Alibaba);
        //    //var temps = os.GetOrdersNoProvince();
        //    var temps = new List<Order>();
        //    if (temps == null || !temps.Any())
        //        return Content("没有查询到符合条件的订单");
        //    var gs = temps.OrderByDescending(t => t.CreateTime).GroupBy(t => t.ShopId).ToList();
        //    Response.Write($"共获取{temps.Count()}个缺少省份订单，涉及到：{gs.Count()}个店铺<br/>");
        //    Response.Flush();
        //    System.Net.ServicePointManager.DefaultConnectionLimit = 20;
        //    Parallel.ForEach(gs, new ParallelOptions { MaxDegreeOfParallelism = 20 }, g =>
        //    {
        //        var list = g.ToList();
        //        var shop = _shopService.Get(g.Key);
        //        list.ForEach(a =>
        //        {
        //            try
        //            {
        //                var siteContext = new SiteContext(shop);
        //                var _syncOrderService = new SyncOrderService(shop.PlatformType);
        //                _syncOrderService.SyncSingleOrder(a.PlatformOrderId, shop);
        //                var msg = $"处理订单完成：{a.PlatformOrderId}-[{shop.Id}]<br/>";
        //                Log.WriteError(msg);
        //                Response.Write(msg);
        //            }
        //            catch (Exception ex)
        //            {
        //                Log.WriteError($"SyncProvince发生错误：{ex}");
        //            }
        //        });
        //        Response.Flush();
        //    });
        //    return Content("Finished");
        //}

        #endregion

        #region 统计单量

        //public ActionResult CaculateOrderCount()
        //{
        //    var shopService = new ShopService();
        //    var shops = shopService.Get();
        //    var gs = shops.GroupBy(s => s.PlatformType).ToList();
        //    Parallel.ForEach(gs, new ParallelOptions { MaxDegreeOfParallelism = 5 }, g =>
        //     {
        //         try
        //         {
        //             shopService.GetOrderQuantityInfo(g.ToList());
        //         }
        //         catch (Exception ex)
        //         {
        //             Log.WriteError($"统计订单数量时发生错误：{ex}");
        //         }
        //     });

        //    //gs.ForEach(g=> {
        //    //    try
        //    //    {
        //    //        shopService.GetOrderQuantityInfo(g.ToList());
        //    //    }
        //    //    catch (Exception ex)
        //    //    {
        //    //        Log.WriteError($"统计订单数量时发生错误：{ex}");
        //    //    }
        //    //});
        //    return Content("Finished");
        //}

        #endregion

        #region 拼多多收件人信息解密

        ///// <summary>
        ///// 显示拼多多加密号码
        ///// </summary>
        ///// <param name="pid">订单ID</param>
        ///// <param name="sid">店铺ID</param>
        ///// <param name="infoType">加密信息类型</param>
        ///// <returns></returns>
        //[Route("pdd/control/decrypt/v1/receiverPhone")]
        //public ActionResult PddDecryptV1ReceiverPhone(PddDecrptRequestModel model)
        //{
        //    return PddDecryptV1(model, "receiverPhone");
        //}

        ///// <summary>
        ///// 显示拼多多加密号码
        ///// </summary>
        ///// <param name="pid">订单ID</param>
        ///// <param name="sid">店铺ID</param>
        ///// <param name="infoType">加密信息类型</param>
        ///// <returns></returns>
        //[Route("pdd/control/decrypt/v1/receiverName")]
        //public ActionResult PddDecryptV1ReceiverName(PddDecrptRequestModel model)
        //{
        //    return PddDecryptV1(model, "receiverName");
        //}

        ///// <summary>
        ///// 显示拼多多加密号码
        ///// </summary>
        ///// <param name="pid">订单ID</param>
        ///// <param name="sid">店铺ID</param>
        ///// <param name="infoType">加密信息类型</param>
        ///// <returns></returns>
        //[Route("pdd/control/decrypt/v1/receiverAddress")]
        //public ActionResult PddDecryptV1ReceiverAddress(PddDecrptRequestModel model)
        //{
        //    return PddDecryptV1(model, "receiverAddress");
        //}

        ///// <summary>
        ///// 显示拼多多加密号码
        ///// </summary>
        ///// <param name="pid">订单ID</param>
        ///// <param name="sid">店铺ID</param>
        ///// <param name="infoType">加密信息类型</param>
        ///// <returns></returns>
        //[Route("pdd/control/decrypt/v1/address")]
        //public ActionResult PddDecryptV1Address(PddDecrptRequestModel model)
        //{
        //    return PddDecryptV1(model, "address");
        //}


        //private ActionResult PddDecryptV1(PddDecrptRequestModel model, string field)
        //{
        //    var rsp = new PddDecryptResponseModel
        //    {
        //        request_id = model?.request_id,
        //        client_id = CustomerConfig.PinduoduoAppKey,
        //        sub_code = model?.decrypt_set?.sub_code,
        //        sub_msg = model?.decrypt_set?.sub_msg,
        //        page_table_id = model?.page_table_id,//前端传过来的店铺ID
        //        order_sn = model?.order_sn,
        //        decrypt_report_type = model.decrypt_report_type,
        //        order_info = new PddDecrpytOrderInfo { },
        //        mall_id = model?.page_table_id ?? ""
        //    };
        //    var shopId = model?.page_table_id ?? "";
        //    if (shopId.IsNullOrEmpty())
        //    {
        //        rsp.sub_msg = "店铺信息无法解析";
        //        rsp.sub_code = "ERROR_SHOPID";
        //    }
        //    else
        //    {
        //        //var shop = _shopService.Get(shopId);
        //        var shop = _shopService.Get(shopId, PlatformType.Pinduoduo);
        //        if (shop == null)
        //        {
        //            rsp.sub_msg = "店铺信息无法解析";
        //            rsp.sub_code = "ERROR_SHOPID";
        //        }
        //        else
        //        {
        //            var sc = new SiteContext(shop.Id);
        //            //rsp.mall_id = sc.CurrentLoginShop.ShopId;
        //            var orderService = new OrderService();
        //            var info = orderService.GetPddDecryptInfoByOrderId(model.order_sn, shop.Id);//shopId);
        //            if (info != null)
        //            {
        //                //var pt = new PinduoduoPlatformService(shop);
        //                //pt.WaybillTypeReportBatch(new List<Order> { info }, "99");
        //                if (field == "receiverPhone")
        //                    rsp.order_info.receiver_phone = info.ToMobile;
        //                else if (field == "receiverName")
        //                    rsp.order_info.receiver_name = info.ToName;
        //                else if (field == "receiverAddress")
        //                    rsp.order_info.receiver_address = info.ToAddress;
        //                else if (field == "address")
        //                    rsp.order_info.receiver_address = info.ToAddress;
        //                else if (field == "cardnum")
        //                    rsp.order_info.cardnum = "";
        //                else if (field == "cardname")
        //                    rsp.order_info.cardname = "";
        //            }
        //            else
        //            {
        //                rsp.sub_msg = "查询不到该订单信息";
        //                rsp.sub_code = "ORDER_NOT_EXIST";
        //            }
        //        }
        //    }
        //    return Json(rsp, JsonRequestBehavior.AllowGet);
        //}

        ///// <summary>
        ///// 拼多多加密风控验证回调
        ///// </summary>
        ///// <returns></returns>
        //public ActionResult PddRiskVerifyCallback()
        //{
        //    var href = Request.QueryString["href"];
        //    var mall_id = Request.QueryString["mall_id"];
        //    var verifyAuthToken = Request.QueryString["verifyAuthToken"];
        //    if (mall_id.IsNullOrEmpty() == false)
        //        Response.Cookies.Add(new HttpCookie(mall_id, verifyAuthToken));
        //    return Redirect(href);
        //}

        #endregion
    }
}