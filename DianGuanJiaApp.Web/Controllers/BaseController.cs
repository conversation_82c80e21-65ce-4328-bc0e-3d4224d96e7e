using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services.Login;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DianGuanJiaApp.Utility.Serialize.Json.ContractResolver;
using Newtonsoft.Json.Serialization;
using Antlr.Runtime;


namespace DianGuanJiaApp.Controllers
{
    public class BaseController : Controller
    {
        #region 基本字段，初始化
        private ShopService _shopService = new ShopService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private OrderService _myOrderService = null;

        // 旧版数据
        private readonly string _defaultHomePage1688 = "/SupplySet1688/DistributionProductSet";
        private readonly string _newHomePage1688 = "/SupplySet1688/DistributionProductSearch";
        // 新版数据
        private readonly string _collectOrderSet = "/NewOrder/AliIncludeOrder"; // 收单设置
        private readonly string _submitOrderSet = "/Common/Page/NewOrder-AliIncludePayOrder"; // 担保下单
                                                                                              // 1688菜单处理
        /// <summary>
        /// （构建订单（相关）服务 -- 采用懒加载--此处非常重要，构建整个订单服务
        /// </summary>
        protected OrderService MyOrderService
        {
            get
            {
                if (_myOrderService == null)
                {
                    _myOrderService = new OrderService();
                }
                return _myOrderService;
            }
            set
            {
                _myOrderService = value;
            }

        }


        #endregion

        #region 重要外部调用属性

        private LoginAuthToken _LoginAuthToken = null;
        private bool _IsIgnoreDoubleAuth = false;
        protected LoginAuthToken LoginAuthToken
        {
            get
            {
                if (_LoginAuthToken == null)
                {
                    _LoginAuthToken = GetLoginToken();
                }
                return _LoginAuthToken;
            }
        }
        #endregion

        #region 分单系统加载认证（主流程）

        protected override void OnAuthorization(AuthorizationContext filterContext)
        {
            //P1：检测IP白名单
            if (!CheckWhiteIP(Request?.Url.Host, Request?.UserHostAddress, Request?.ServerVariables["REMOTE_ADDR"], Request?.Headers["X-Forwarded-For"]))
            {
                filterContext.Result = Content("IP白名单限制");
                return;
            }

            //P2：检测是否系统维护时间
            var xRequestedWith = Request.Headers["X-Requested-With"];
            var tuple = IsMaintenanceTime(xRequestedWith);
            if (tuple.Item1)
            {
                if (IsAjaxRequest(xRequestedWith))
                    filterContext.Result = Json(new AjaxResult() { Message = tuple.Item2, ErrorCode = "500" });
                else
                    filterContext.Result = Content(tuple.Item2);
                return;
            }

            //P3：判断是否分单，进入分单认证
            if (CustomerConfig.IsFendanSite)
            {
                FxSiteAuthorization(filterContext.ActionDescriptor, Request["dbname"]);
                return;
            }

        }


        #endregion

        #region 分单系统加载认证（分支流程）

        #region 白名单校验
        /// <summary>
        /// 白名单校验
        /// </summary>
        public static bool CheckWhiteIP(string hostUrl, string userHostAddress, string remoteAddr, string ipforHeader)
        {
            try
            {
                var host = hostUrl?.ToLower() ?? "";
                if (host.StartsWith("test") || host.StartsWith("tfx") || host.StartsWith("1tfx") || host.StartsWith("3tfx") || host.StartsWith("4tfx") || host.StartsWith("5tfx") || host.StartsWith("6tfx"))
                {
                    //ip白名单控制，读取ip白名单
                    if (!ToolsBaseController.IpWhiteCheck(userHostAddress, remoteAddr, ipforHeader))
                    {
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.WriteError($"白名单校验报错：{ex.Message}");
                return false;
            }
        }
        #endregion

        #region  是否是系统维护时间
        /// <summary>
        /// 是否是系统维护时间
        /// </summary>
        public static Tuple<bool, string> IsMaintenanceTime(string xRequestedWith)
        {
            var time = new CommonSettingService().GetMaintenanceTime();
            if (time <= DateTime.Now)
            {
                return new Tuple<bool, string>(false, "");
            }

            var day = time.Day;
            var nowDay = DateTime.Now.Day;
            var dayString = day + "号";
            if (day - nowDay == 0)
            {
                dayString = "今天";
            }
            if (day - nowDay == 1)
            {
                dayString = "明天";
            }
            else if (day - nowDay == 2)
            {
                dayString = "后天";
            }
            if (day - nowDay == 3)
            {
                dayString = "大后天";
            }

            var words = $"系统通知： 尊敬的用户，为了给您带来更好的打单体验及性能优化，分销代发系统正在进行服务器升级处理，将于【{dayString}{time.ToString("HH点")}】结束维护，在维护期间不能进行打单等操作。感谢理解！";
            return new Tuple<bool, string>(true, words);
        }

        public static bool IsAjaxRequest(string xRequestedWith)
        {
            if (string.IsNullOrEmpty(xRequestedWith))
                return false;

            return xRequestedWith == "XMLHttpRequest";
        }

        #endregion

        #region 进入分单认证
        private void FxSiteAuthorization(ActionDescriptor actionDescriptor, string dbname)
        {
            var ignoreToken = actionDescriptor.GetCustomAttributes(typeof(IgnoreTokenAttribute), true);
            if (ignoreToken.Any())
            {
                return;
            }

            //是否忽略双重验证
            _IsIgnoreDoubleAuth = false;
            var ignoreDoubleAuth = actionDescriptor.GetCustomAttributes(typeof(IgnoreDoubleAuthAttribute), true);
            if (ignoreDoubleAuth.Any())
            {
                _IsIgnoreDoubleAuth = true;
            }

            bool isFromWeixin = false;
            bool isFromQingWk = false;
            try
            {
                /******************此处非常重要，构建整个站点上下文****************/
                var site = new SiteContext(this.LoginAuthToken, GetCurrentDBName(dbname), new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
                isFromWeixin = IsFromWeixin(this.PageToken, Request.Headers["User-Agent"], Request.Headers["Fx-WxToken"], Request.Headers["Fx-WxOpenId"]);
                isFromQingWk = IsFromQingWk(this.PageToken, Request.Headers["X-UserId"]);
                FxSiteRiskeInterceptor();

                //==================================================ajax请求或者来源于微信退出，不继续处理以下====================================================================
                if (Request.IsAjaxRequest() || isFromWeixin == true || isFromQingWk == true)
                {
                    return;
                }

                //把数据库分区信息传入到ViewBag中
                var dbAreas = site.CurrentDbAreaConfig.Select(x => x.DbNameConfig).OrderBy(x => x.Id).Select(y => new { DbName = DES.EncryptDES(y.DbName, CustomerConfig.LoginCookieEncryptKey), y.NickName, y.ApplicationName }).ToList();
                ViewBag.DbArea = dbAreas.ToJson();
                ViewBag.AllShopsModel = "[]";//去除前端所有店铺加载，防止页面某些地方保持。若发现前端异常，可在异常页面上加上对应所有店铺数据

                //系统权限列表透出前端
                //ViewBag.FxPermission = fxPermissionsDict.ToJson();

                var fxUserId = site.CurrentFxUserId;
                //自动转入指定版本
                var requestUrl = Request.Url?.ToString()?.ToLower()?.Replace("https://", "")?.Replace("http://", "");
                var version = site.CurrentLoginShop.Version.ToString2();

                var toUrl = CustomerConfig.GetShopRedirectLink(site.CurrentLoginShop.PlatformType, version);
                var redirectUrl = toUrl?.ToLower()?.Replace("https://", "")?.Replace("http://", "");

                //对比链接，如果版本不对需要跳转到指定版本
                if (CustomerConfig.IsDebug == false && !string.IsNullOrEmpty(requestUrl) && !string.IsNullOrEmpty(redirectUrl) && !requestUrl.IsIp() && !redirectUrl.IsIp() && !requestUrl.IsLocalHost() && !redirectUrl.IsLocalHost())
                {
                    var requestVersion = requestUrl?.Split(new char[] { ':', '.' })?.FirstOrDefault();
                    var actualVersion = redirectUrl?.Split(new char[] { ':', '.' })?.FirstOrDefault();
                    if (requestVersion != actualVersion)
                    {
                        Response.Redirect(toUrl + Request.RawUrl);
                        return;
                    }
                }
                ViewBag.Token = this.PageToken;
                ViewBag.PageVersionClassName = GetFxUserVersionCssClassName(version);
                var _commonSettingService = new CommonSettingService();
                // 修复发货记录-发货时间
                if (CustomerConfig.IsDebug && CustomerConfig.IsTestWebSite)//新增IsTestWebSite，避免部分站点开启了IsDebug。
                {
                    var vdate = _commonSettingService.Get("FxSystem/RepairSendHistoryDate", -168, isUseCache: false)?.Value;
                    //Log.Debug($"requestUrl={requestUrl},vdate={vdate}");
                    if (vdate.IsNotNullOrEmpty())
                    {
                        var arr = vdate.SplitToList("|"); // 1,3,5|2024-10-10 12:00:00 => 测试版本|发货时间 
                        var versionPrefixs = arr[0]?.SplitToList(",").Select(x => $"{x}tfx").ToList() ?? new List<string>();
                        if (arr.Count == 2 && versionPrefixs.Any(x => requestUrl.Contains(x)))
                        {
                            CustomerConfig.SetRepairSendHistoryDate(arr[1]);
                            Log.Debug($"requestUrl={requestUrl},vdate={vdate},SetRepairSendHistoryDate={CustomerConfig.RepairSendHistoryDate()}", "SetRepairSendHistoryDate.txt");
                        }
                    }
                    else
                    {
                        CustomerConfig.SetRepairSendHistoryDate("");
                        Log.Debug($"requestUrl={requestUrl},vdate={vdate},SetRepairSendHistoryDate={CustomerConfig.RepairSendHistoryDate()}", "SetRepairSendHistoryDate.txt");
                    }
                }
                var curDbName = _commonSettingService.GetString("/ErpWeb/DefaultPlatformDbArea", SiteContext.Current.CurrentShopId);
                if (curDbName.IsNotNullOrEmpty() && dbAreas != null && dbAreas.Any(x => x.DbName == curDbName))
                    ViewBag.DbName = curDbName;
                else
                    ViewBag.DbName = DES.EncryptDES(site.CurrentDbConfig?.DbNameConfig.DbName, CustomerConfig.LoginCookieEncryptKey);

                //是否显示跨境相关的功能（用户配置开启了跨境功能）
                ViewBag.IsShowCrossBorder = SiteContext.Current.IsShowCrossBorder; //_commonSettingService.IsShowCrossBorder(SiteContext.Current.CurrentShopId);
                //是否是跨境站点（当前请求的站点是否是跨境站点）
                ViewBag.IsCrossBorderSite = CustomerConfig.IsCrossBorderSite;

                // 顶部按钮对应权限字典 是否展示站内信按钮/快速邀请/异常商品消息
                ViewBag.TopBtnPermDict = new Dictionary<string, bool>()
                {
                    {nameof(FxPermission.SiteMessage),SiteContext.HasPermission(FxPermission.SiteMessage)},
                    {nameof(FxPermission.AgentInvite),SiteContext.HasPermission(FxPermission.AgentInvite)},
                    {nameof(FxPermission.SupplierInvite),SiteContext.HasPermission(FxPermission.SupplierInvite)},
                    {nameof(FxPermission.AbnormalProduct),SiteContext.HasPermission(FxPermission.AbnormalProduct)},
                }.ToJson();

                ViewBag.TraceId = Guid.NewGuid().ToString().ToShortMd5();
                ViewBag.ShopName = site.CurrentLoginShop.NickName;
                ViewBag.ShopId = site.CurrentShopId;
            
                if (SiteContext.Current.SubFxUserId > 0)
                {
                    // 子账号有仅绑定微信的情况，mobile为空，产品要求显示微信的名称
                    ViewBag.Mobile = site.SubFxUser.Mobile.IsNullOrEmpty() ? site.SubFxUser.WxNickName : site.SubFxUser.Mobile;
                    //子账号兼容需求调整【ID1035813】
                    var nickName = site.SubFxUser.NickName;
                    nickName = nickName.IsNullOrEmpty() ? "" : nickName;
                    nickName = nickName.Length > 4 ? nickName.Substring(0, 4) + "..." : nickName;
                    ViewBag.NickName = $"{site.CurrentFxUser.Mobile}[{nickName}]";
                    //前端页面标题追加的值
                    if (string.IsNullOrEmpty(site.SubFxUser.NickName) == false)
                        ViewBag.ShopName = site.SubFxUser.NickName;
                    else if (string.IsNullOrEmpty(site.SubFxUser.Mobile) == false)
                        ViewBag.ShopName = site.SubFxUser.Mobile;
                }
                else
                {
                    ViewBag.Mobile = site.CurrentFxUser.Mobile;
                    //子账号兼容需求调整【ID1035813】
                    ViewBag.NickName = $"{site.CurrentFxUser.Mobile}[主账号]";
                }
                ViewBag.FxUserId = site.CurrentFxUserId;
                ViewBag.FxUserAddres = (new FxUserAddress()).ToJson(); //缓存性能优化:默认不加载地址数据
                ViewBag.CloudPlatformType = CustomerConfig.CloudPlatformType;
                var oldPddPageCode = Request.Cookies[site.CurrentFxUserId + "-pdd-pagecode"]?.Value;
                ViewBag.PddPageCode = oldPddPageCode ?? "";
                ViewBag.IsGrayUser = (site.CurrentLoginShop?.Version.IsNotNullOrEmpty() ?? false).ToString2().ToLower();
                if (string.IsNullOrEmpty(oldPddPageCode) && CustomerConfig.CloudPlatformType == "Pinduoduo")
                {
                    //判断是否有拼多多的店铺
                    ViewBag.PddPageCode = GetPddPageCode();
                }
                ViewBag.SystemVersion = CustomerConfig.SystemVersion;

                //是否显示1688菜单
                var isShow1688Menu = _commonSettingService.GetIsShow1688Menu(fxUserId, SiteContext.Current.CurrentShopId);
                //isShow1688Menu = false;
                ViewBag.IsShow1688Menu = isShow1688Menu;
                //是否是白名单用户
                var isWhite = SiteContext.Current.IsWhiteUser;
                ViewBag.IsWhiteList = isWhite;
                //菜单数据,只有使用新导航样式才查询
                var navService = new FenDanSystemNavService();
                List<FenDanSystemNav> navs = null;

                // 当前环境，根据环境获取不同的菜单
                var navVersion = CustomerConfig.FxPermissionVersion;

                if (SiteContext.Current.IsOnlyListingUser)
                    navVersion = "Only_Listing";

                if (!site.IsOldNavStyle)
                {
                    var navSetting = navService.GetNavSettings(SiteContext.Current.CurrentShopId, navVersion, true);
                    navs = navSetting.Item1;
                    //1688菜单的处理
                    HandleNav1688(isShow1688Menu, ref navs);
                    //处理利润统计白名单
                    HandleProfitStatistics(navs);
                    // 处理大客户优化白名单白名单菜单
                    HandleWhiteUserByIsReconVip(navs);

                    ///处理跨境采集菜单
                    HandleCrossBorderProfitStatistics(navs);

                    //处理短视频发布白名单
                    HandShopShopVideo(navs);
                    //子账号需验证一级导航栏的URL是否有权限
					if (SiteContext.Current.SubFxUserId > 0)
					{
						navs.ForEach(item => {
                            if (item.Children != null && !item.Children.Any(a => a.Url == item.Url))
                            {
                                item.Url = item.Children.FirstOrDefault()?.Url;
                            }
                        });
					}

					ViewBag.NavQuickEntry = navSetting.Item2.ToJson();
                    ViewBag.NavQuickEntrySort = navSetting.Item3.ToJson();
                }
                // 240619xwm：子账号功能改造，旧版菜单也由后台给
                else
                {
                    navs = navService.GetNavSettings(SiteContext.Current.CurrentShopId, navVersion).Item1;
                    //1688菜单的处理
                    HandleNav1688(isShow1688Menu, ref navs);
                    //处理利润统计白名单
                    HandleProfitStatistics(navs);
                    // 处理大客户优化白名单白名单菜单
                    HandleWhiteUserByIsReconVip(navs);

                    //处理跨境采集菜单
                    HandleCrossBorderProfitStatistics(navs);
                    //处理短视频发布白名单
                    HandShopShopVideo(navs);
                }
                //分单白名单
                if (isWhite == false && CustomerConfig.IsFxWhiteUserFilter && !SiteContext.Current.IsOnlyListingUser) HandleWhiteUserNav(ref navs);
                else RemoveNavItem(navs, "商品管理", "自营商品");

				ViewBag.FenDanSystemNavs = navs.ToJson();

                ViewBag.IsNewCorp = false;
                ViewBag.IsShowSiteMessage = _commonSettingService.IsWhiteUserBySiteMessage();
                var userFlag = SiteContext.Current.CurrentFxUser.UserFlag.ToString2();
                ViewBag.UserFlag = userFlag;

                return;
            }
            catch (LogicException ex)
            {
                if (ex.ErrorCode == "ShopExpired" || ex.ErrorCode == "PlatformTypeParseFaild" || ex.ErrorCode == "ShopMigrating" || ex.ErrorCode == "AuthVersion")
                {
                    throw ex;
                }
                else if (ex.ErrorCode == "Risk_In")
                {
                    Response.Clear();
                    Response.Redirect("/");
                    return;
                }
                else
                {
                    Log.WriteError($"授权验证失败，重新登录，{ex}");
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"授权验证失败，重新登录，{ex}");
            }
            RedirectToFxLogin(this.PageToken, isFromWeixin);
        }

        /// <summary>
        /// 处理利润统计白名单菜单
        /// </summary>
        /// <param name="navSetting"></param>
        private void HandleProfitStatistics(List<FenDanSystemNav> navSetting)
        {
            if (SiteContext.Current.IsOnlyListingUser)
                return;
            if (!_commonSettingService.IsWhiteUserByProfitStatistics())
            {
                //移除利润统计菜单
                navSetting.RemoveAll(t => t.NavCode == "统计中心");
            }
        }

        /// <summary>
        /// 处理短视频发布白名单菜单
        /// </summary>
        /// <param name="navSetting"></param>
        private void HandShopShopVideo(List<FenDanSystemNav> navSetting)
        {
            if (SiteContext.Current.IsOnlyListingUser)
                return;
            if (!_commonSettingService.IsShopVideoWhiteUser())
            {
                //移除短视频管理菜单
                RemoveNavItem(navSetting, "商品管理", "短视频发布");
            }
        }

        /// <summary>
        /// 处理大客户优化白名单菜单
        /// </summary>
        /// <param name="navSetting"></param>
        private void HandleWhiteUserByIsReconVip(List<FenDanSystemNav> navSetting)
        {
            if (_commonSettingService.IsWhiteUserByIsReconVip())
            {
                var jwnav = navSetting.Find(t => t.NavCode == "财务结算");
                var nav = jwnav?.Children?.Find(t => t.NavCode == "对账中心");
                if (nav != null)
                {
                    nav.Url = "/FinancialSettlement/BillCenter";
                }
            }
        }

        /// <summary>
        /// 处理跨境菜单
        /// </summary>
        /// <param name="navSetting"></param>
        private void HandleCrossBorderProfitStatistics(List<FenDanSystemNav> navSetting)
        {
            ///跨境白名单
            if (!_commonSettingService.IsShowCrossBorder())
            {
                RemoveNavItem(navSetting, "商品管理", "全球商品");
                RemoveNavItem(navSetting, "商品管理", "商品采集");
                RemoveNavItem(navSetting, "商品管理", "商品采集箱");
            }
        }



        /// <summary>
        /// 处理1688菜单
        /// </summary>
        /// <param name="isShow1688Menu"></param>
        /// <param name="navSetting"></param>
        /// <param name="isOldNav"></param>
        private void HandleNav1688(bool isShow1688Menu, ref List<FenDanSystemNav> navSetting)
        {

            // 修改 navSetting 中资金流水的Url
            var navIndex = navSetting.FindIndex(n => n.ParentNavCode.IsNotNullOrEmpty() && n.Title.Equals("财务结算"));
            var childIndex = -1;
            if (navIndex != -1)
            {
                childIndex = navSetting[navIndex].Children?.FindIndex(c => c.Title.Equals("资金流水")) ?? -1;
            }

            if (isShow1688Menu)
            {
                // 移除首页的1688担保交易
                var introduceUrl = "/GeneralizeIndex/aliDistributionIntroduce";
                var mainIndex = navSetting.FindIndex(n => n.ParentNavCode.IsNullOrEmpty() && (n.Title.Equals("工作台") || n.Title.Equals("首页")));
                if (mainIndex != -1)
                {
                    navSetting[mainIndex].Children?.RemoveAll(n => (n.ParentNavCode.IsNotNullOrEmpty() && n.Title.Equals("1688担保交易"))
                                            || (n.Url?.Equals(introduceUrl) == true));
                }

                if (QingService.Instance.UserRole == QingUserRole.Both)
                    return;
                if (QingService.Instance.UserRole != QingUserRole.Busines) // 不是商家，移除担保交易
                {
                    navSetting.RemoveAll(n => n.ParentNavCode.IsNullOrEmpty() && (n.Title.Equals("担保交易") || n.Title.Equals("1688担保交易下单")));
                    if (childIndex != -1)
                    {
                        // 资金流水Url改为我的商家
                        navSetting[navIndex].Children[childIndex].Url = FxPermission.TransactionDetailAgent;
                    }
                }
                if (QingService.Instance.UserRole != QingUserRole.Factory) // 不是厂家，移除1688收单
                {
                    navSetting.RemoveAll(n => n.ParentNavCode.IsNullOrEmpty() && (n.Title.Equals("1688收单") || n.Title.Equals("1688收单设置")));
                    if (childIndex != -1)
                    {
                        // 资金流水Url改为我的厂家
                        navSetting[navIndex].Children[childIndex].Url = FxPermission.TransactionDetailSupplier;
                    }
                }
            }
            else
            {
                if (navIndex != -1 && childIndex != -1)
                    navSetting[navIndex].Children.RemoveAt(childIndex);
                navSetting.RemoveAll(n => n.ParentNavCode.IsNullOrEmpty() && (n.Title.Equals("担保交易") || n.Title.Equals("1688担保交易下单")));
                navSetting.RemoveAll(n => n.ParentNavCode.IsNullOrEmpty() && (n.Title.Equals("1688收单") || n.Title.Equals("1688收单设置")));
            }
        }

        /// <summary>
        /// 非白名单菜单栏控制
        /// </summary>
        /// <param name="navSetting"></param>
        private void HandleWhiteUserNav(ref List<FenDanSystemNav> navSetting)
        {
            RemoveNavItem(navSetting, "工作台", "我的小站");
            RemoveNavItem(navSetting, "首页", "我的小站");
            RemoveNavItem(navSetting, "商品管理", "基础商品");
            RemoveNavItem(navSetting, "商品管理", "厂家商品");
            //跨境存在铺货 非跨境白名单
            if (!_commonSettingService.IsShowCrossBorder())
                RemoveNavItem(navSetting, "商品管理", "铺货日志");

            // 将库存货品的url改为/StockControl/WareHouseProduct
            // 库存货品的移到底层处理
            //ChangeNavUrl(navSetting, "库存管理", "库存货品", "/StockControl/WareHouseProduct");
        }

        /// <summary>
        ///  移除导航项
        /// </summary>
        /// <param name="navSetting"></param>
        /// <param name="parentTitle"></param>
        /// <param name="childTitle"></param>
        private void RemoveNavItem(List<FenDanSystemNav> navSetting, string parentTitle, string childTitle)
        {
            var navIndex = navSetting.FindIndex(n => n.Title.Equals(parentTitle));
            if (navIndex != -1)
            {
                var childIndex = navSetting[navIndex].Children?.FindIndex(c => c.Title.Equals(childTitle)) ?? -1;
                if (childIndex != -1) navSetting[navIndex].Children?.RemoveAt(childIndex);
            }
        }

        /// <summary>
        /// 修改导航项的Url
        /// </summary>
        /// <param name="navSetting"></param>
        /// <param name="parentTitle"></param>
        /// <param name="childTitle"></param>
        /// <param name="url"></param>
        private void ChangeNavUrl(List<FenDanSystemNav> navSetting, string parentTitle, string childTitle, string url)
        {
            var navIndex = navSetting.FindIndex(n => n.Title.Equals(parentTitle));
            if (navIndex != -1)
            {
                var childIndex = navSetting[navIndex].Children?.FindIndex(c => c.Title.Equals(childTitle)) ?? -1;
                if (childIndex != -1) navSetting[navIndex].Children[childIndex].Url = url;
            }
        }

        private string GetFxUserVersionCssClassName(string version)
        {
            var defaultClassName = "fx-release";
            if (string.IsNullOrEmpty(version))
                return defaultClassName;
            var versionNumber = version.ToInt();
            if (versionNumber <= 0)
                return defaultClassName;
            else
                return $"fx-gray fx-gray-{versionNumber}";
        }
        #endregion

        #region 风控用户拦截
        private void FxSiteRiskeInterceptor()
        {
            // 检测账号是否处于风控状态
            // 检测账号类型
            var token = this.LoginAuthToken;
            var subUserId = SiteContext.Current.SubFxUserId;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var subUser = subUserId > 0 ? true : false;
            var riskStatus = string.Empty;

            // 子账号 
            // 主账号
            if (subUser)
                riskStatus = SiteContext.Current.SubFxUser.RiskStatus;
            else
                riskStatus = SiteContext.Current.CurrentFxUser.RiskStatus;
            if (riskStatus == "in")
            {
                // 1.检测账号风控中
                // 2.拦截请求 
                // 3.作废令牌(所有可用)
                try
                {
                    _shopService.ExpiredFxTokenAll(fxUserId, subUserId, token.ShopId);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"风控拦截作废令牌过期失败，失败原因：{ex.ToJson()}");
                }

                throw new LogicException("用户处于风控中，请重新登录", "Risk_In");
            }
        }
        #endregion

        #endregion

        #region 页面请求Action

        /// <summary>
        /// Excel导出任务状态监测
        /// </summary>
        public ActionResult CheckExportTaskStatus()
        {
            var id = Request["id"].ToInt();
            var _exportTaskService = new ExportTaskService();
            var task = _exportTaskService.GetTaskByFxUser(id, SiteContext.Current.CurrentShopId);
            if (task == null || task.Status == -99)
                return FalidResult("后台任务已经被删除");

            task = GetExportTaskToWeb(task);
            return Json(task);
        }

        public ActionResult UpdateExportTaskStatus()
        {
            var id = Request["id"].ToInt();
            var status = Request["status"].ToInt();
            var _exportTaskService = new ExportTaskService();
            var task = _exportTaskService.GetTaskByFxUser(id, SiteContext.Current.CurrentShopId);
            if (task == null)
                return FalidResult("后台任务已经被删除");
            task.Status = status;
            _exportTaskService.UpdateStatus(task, false);

            task = GetExportTaskToWeb(task);
            return Json(task);
        }


        /// <summary>
        /// 检查阿里多版本权限控制
        /// </summary>
        /// <param name="shopIds"></param>
        /// <param name="controllerName"></param>
        /// <param name="actionName"></param>
        /// <returns></returns>
        public AjaxResult CheckAlibabaShopVersionControl(List<int> shopIds, string controllerName, string actionName)
        {
            if (shopIds == null || shopIds.Any() == false)
                return null;
            var shops = SiteContext.Current.AllShops.Where(x => shopIds.Contains(x.Id)).ToList();// && x.PlatformType == PlatformType.Alibaba.ToString()).ToList();
            AjaxResult result = null;
            if (shops != null && shops.Any())
            {
                var model = CommonSettingService.GetPlatformVersionTipInfoByShop(shops, controllerName, actionName);
                if (model != null && model.NeedUpgradeShopNames != null && model.NeedUpgradeShopNames.Any())
                {
                    result = new AjaxResult
                    {
                        Success = false,
                        Data = model,
                        ErrorCode = "NEED_UPGRADE_VERSION",
                        Message = "需要升级版本"
                    };
                }
            }
            return result;
        }


        #endregion

        #region 业务方法

        /// <summary>
        /// 动态渲染分布视图
        /// </summary>
        /// <param name="viewName">视图名称</param>
        /// <param name="model">模型</param>
        /// <returns>渲染后的html</returns>
        public virtual string RenderPartialViewToString(string viewName, object model)
        {
            if (string.IsNullOrEmpty(viewName))
                viewName = this.ControllerContext.RouteData.GetRequiredString("action");

            this.ViewData.Model = model;

            using (var sw = new StringWriter())
            {
                ViewEngineResult viewResult = System.Web.Mvc.ViewEngines.Engines.FindPartialView(this.ControllerContext, viewName);
                var viewContext = new ViewContext(this.ControllerContext, viewResult.View, this.ViewData, this.TempData, sw);
                viewResult.View.Render(viewContext, sw);

                return sw.GetStringBuilder().ToString();
            }
        }

        /// <summary>
        /// 是否是微信小程序请求，并且是同一个群体；
        /// 能进这个方法，都是微信端（pc和移动端）发出的。 有userAgent.Contains("MicroMessenger") 过滤
        /// </summary>
        /// <returns></returns>
        private static bool IsWeiXinRequest(string token, string FxWxTokenHeader, string FxWxOpenIdHeader)
        {
            var isWeixinRequest = false;
            try
            {
                var fxWxToken = FxWxTokenHeader ?? "";
                var fxWxOpenId = FxWxOpenIdHeader ?? "";

                //==============不是微信小程序请求，直接返回false=================
                if (string.IsNullOrEmpty(fxWxToken) || string.IsNullOrEmpty(fxWxOpenId))
                {
                    return isWeixinRequest;
                }
                //参数不同(token)，直接返回false
                if (fxWxToken != token)
                {
                    return isWeixinRequest;
                }

                var json = DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey);
                //验证token和openid是否属于同一个群体
                var wxUserFxRelationList = new WxUserFxRelationService().GetList(fxWxOpenId);
                if (wxUserFxRelationList.Count == 0)
                {
                    return isWeixinRequest;
                }

                var loginAuthToken = new ShopService().GetToken(json.ToInt());
                var wxUserFxRelation = wxUserFxRelationList.FirstOrDefault(u => u.SystemShopId == loginAuthToken?.ShopId);
                if (wxUserFxRelation != null)
                {
                    isWeixinRequest = true;
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"判断是否微信请求，报错：{e.Message}");
            }
            return isWeixinRequest;
        }
        /// <summary>
        /// 是否是轻应用请求，并且是同一个群体；
        /// 能进这个方法，都是入端发出的。 有Request.Headers["X-UserId"]过滤
        /// </summary>
        /// <returns></returns>
        private static bool IsQingWkRequest(string token, string XUserIdHeader)
        {
            var isQingWkRequest = false;
            if (string.IsNullOrEmpty(XUserIdHeader))
                return false;
            try
            {
                var encryption = XUserIdHeader ?? "";
                var shopUid = new CommService().UserIdEncryptionByFxQingWk(encryption);
                if (shopUid == "2212517650843")//dianguanjia2011
                    shopUid = new CommService().UserReplaceByTools(shopUid, PlatformType.Alibaba.ToString2());//替换的店铺uid
                else if (shopUid == "3948550980")//中恒电商
                    shopUid = new CommService().UserReplaceByTools(shopUid, PlatformType.Alibaba.ToString2());//替换的店铺uid
                else if (shopUid == "2211468320247")//dgj2021
                    shopUid = new CommService().UserReplaceByTools(shopUid, PlatformType.Alibaba.ToString2());//替换的店铺uid
                //_userId = "961855299";
                if (string.IsNullOrEmpty(shopUid))
                    return false;
                var json = DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey);
                var _shopService = new ShopService();
                var loginAuthToken = _shopService.GetToken(json.ToInt());//前面验证过token的转换。所以这边直接查逻辑。

                var aliShop = _shopService.FindShopIdByUIDAndPlatformType(shopUid, "Alibaba");
                if (aliShop != null)
                {
                    var fxUserShopByUser = new FxUserShopService().GetFxUserSystemShop(aliShop.Id);//通过店铺，查找绑定的用户
                    //验证token和userId(当前登录的店铺)是否属于同一个群体
                    if (fxUserShopByUser != null && fxUserShopByUser.ShopId == loginAuthToken?.ShopId)
                        isQingWkRequest = true;
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"判断是否轻应用请求，报错：{e.Message}");
            }
            return isQingWkRequest;
        }

        private bool? _isNewCorpReview = null;
        /// <summary>
        /// 是否开启新主体过审，隐藏和店管家相关文案链接等(懒加载)
        /// </summary>
        protected bool IsNewCorpReview
        {
            //是否新主体过审
            get
            {
                if (_isNewCorpReview == null)
                {
                    _isNewCorpReview = (CustomerConfig.IsEnabledNewCorpReview || IsNewCorpReviewByHost());
                }
                return _isNewCorpReview.Value;
            }
        }



        /// <summary>
        /// 通过host判断是否开启新主体过审，隐藏和店管家相关文案链接等
        /// </summary>
        /// <returns></returns>
        private bool IsNewCorpReviewByHost()
        {
            string domain = Request?.Url?.Host?.ToLower();
            if (domain == null) return false;

            var value = new CommonSettingService().Get("/System/Setting/FenDan/NewCorpReviewHosts", 0)?.Value;
            if (value == null) return false;
            var hostStrs = value.ToLower().Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
            if (hostStrs.Length == 0) return false;
            //所有命中
            if (hostStrs.Contains("*")) return true;

            bool isNewCorpReview = false;
            //三级域名命中
            isNewCorpReview = hostStrs.Any(h => domain == h);
            if (isNewCorpReview) return true;

            //二级域名命中
            isNewCorpReview = hostStrs.Any(h =>
            {
                var _rhost = h.Replace("*.", "");
                return domain.LastIndexOf(_rhost) != -1;
            });
            return isNewCorpReview;
        }
        protected string GetApiUrl(string platformType, string api)
        {
            var pddApiUrl = CustomerConfig.PinduoduoNewSystemLink + api;
            var jdApiUrl = CustomerConfig.JingDongNewSystemLink + api;
            var aliApiUrl = CustomerConfig.AlibabaNewSystemLink + api;

            if (CustomerConfig.IsDebug && CustomerConfig.IsLocalDbDebug == false)
            {
                pddApiUrl = "http://testpdd.dgjapp.com" + api;
                jdApiUrl = "http://testjd.dgjapp.com" + api;
                aliApiUrl = "http://testother.dgjapp.com" + api;
            }

            var apiUrl = string.Empty;

            if (platformType == PlatformType.Pinduoduo.ToString())
            {
                apiUrl = pddApiUrl;
            }
            else if (platformType == PlatformType.Jingdong.ToString())
            {
                apiUrl = jdApiUrl;
            }
            else
            {
                apiUrl = aliApiUrl;
            }
            return apiUrl;
        }

        /// <summary>
        /// 获取登录token
        /// </summary>
        /// <returns></returns>
        private LoginAuthToken GetLoginToken(HttpRequestBase request = null)
        {
            if (request != null)
            {
                this.Request = request;
            }
            var token = this.PageToken;
            if (string.IsNullOrWhiteSpace(token))
            {
                throw new LogicException("无权访问页面，请重新登录", "401");
            }

            var json = DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey);
            if (string.IsNullOrEmpty(json))
            {
                throw new LogicException("登录信息不合法，请重新登录", "401");
            }

            var tokenID = json.ToInt();
            var model = new LoginAuthCookieModel { Id = tokenID };
            var userAgent = this.UserAgent; ;
            var isWechatBrowser = userAgent.Contains("MicroMessenger");
            var isFromWeixin = IsFromWeixin(this.PageToken, Request.Headers["User-Agent"], Request.Headers["Fx-WxToken"], Request.Headers["Fx-WxOpenId"]); ;
            if (IsFromQingWk(PageToken, Request.Headers["X-UserId"]) == true)
                isFromWeixin = true;//阿里轻应用，和微信一样，都是前后端分离，api的请求方式
            var hashCode = userAgent.GetHashCode();
            var sign = hashCode.ToString();
            var md5sign = userAgent.ToShortMd5();
            var md5signForWechat = md5sign;
            if (isWechatBrowser)
            {
                //微信浏览器，首次进入是UserAgent后缀包含了：" Flue"，后面iframe中没有这个
                var extUserAgent = " Flue";
                if (userAgent.EndsWith(extUserAgent) == false)
                {
                    md5signForWechat = (userAgent + extUserAgent).ToShortMd5();
                }
                else
                {
                    md5signForWechat = (userAgent.TrimEnd(extUserAgent)).ToShortMd5();
                }
            }
            var isLocalhost = string.Equals(Request.Url.Host, "localhost");
            //双重验证
            var key = CustomerConfig.FxSystemIsOpenDoubleAuthKey;
            var isOpenDoubleAuth = 0;
            if (_IsIgnoreDoubleAuth == false)
                isOpenDoubleAuth = _commonSettingService.Get(key, 0)?.Value.ToInt() ?? 0;

            //签名必须一致，且数据库中这个token存在、未过期
            var authToken = _shopService.IsTokenValid(model, sign, md5sign, isFromWeixin, this.CurrentIP, md5signForWechat, isOpenDoubleAuth: isOpenDoubleAuth, _IsIgnoreDoubleAuth);

            if (authToken == null)
            {
                throw new LogicException("登录信息过期，请重新登录", "401");
            }
            // token二级验证过期
            //authToken = VerifyLoginToken(authToken);

            //if (authToken == null)
            //{
            //    throw new LogicException("登录信息过期，请重新登录", "401");
            //}

            //不存在分销店铺ID，加载UserID
            if (authToken.FxUserId <= 0)
            {
                authToken.FxUserId = new ShopService().GetFxUserIdByShopId(authToken.ShopId);
            }
            if (authToken.FxUserId <= 0)
            {
                Log.WriteWarning($"登录信息无法识别，通过ShopId获取FxUserId依然无数据。请求tokenId： {model.Id}，LoginAuthToken.ShopId：{authToken.ShopId} ");
                return authToken;
            }

            //本地连接&微信不校验cookie
            if (!CustomerConfig.IsLocalDbDebug && !isFromWeixin && !CustomerConfig.IsDebug && !isLocalhost)
            {
                //校验Cookie
                var cookieKey = CustomerConfig.FxSystemLoginCookieKey.Replace("$id", authToken.Id.ToString());
                var loginCookie = Request.Cookies[cookieKey];
                var checkResult = CheckLoginCookie(_commonSettingService, loginCookie?.Value, isOpenDoubleAuth, authToken, key);
                if (checkResult == false)
                {
                    RedirectToFxLogin("", false);
                    return authToken;
                }
            }
            return authToken;
        }


        /// <summary>
        /// 验证过期token
        /// </summary>
        /// <returns></returns>
        private LoginAuthToken VerifyLoginToken(LoginAuthToken token)
        {
            var tokenRes = token;
            var shopId = token.ShopId;

            // 主账号、子账号
            var fxUserId = token.FxUserId;
            var fxUserIdSub = token.SubUserId;
            var fxUserIdTemp = fxUserIdSub > 0 ? fxUserIdSub : fxUserId;
            try
            {
                // 获用户过期校验开关
                var loginTokenExpireSwitchKey = "/Fendan/Config/IsLoginTokenCanBeExpire";
                // 获用户过期校验时间
                var LoginTokenExpireHoursKey = "/Fendan/Config/LoginTokenExpireHours";

                var LoginTokenExpireSwitchSetting =
                    _commonSettingService.GetSet(loginTokenExpireSwitchKey, shopId, true, true);
                var LoginTokenExpireHoursSetting =
                    _commonSettingService.GetSet(LoginTokenExpireHoursKey, shopId, true, true);

                //var LoginTokenAllkeys = new List<string> {
                //        loginTokenExpireSwitchKey,
                //        LoginTokenExpireHoursKey };
                //var LoginTokenAllSetting = _commonSettingService.GetSets(LoginTokenAllkeys, shopId);
                //var LoginTokenExpireSwitchSetting =
                //    LoginTokenAllSetting.FirstOrDefault(p => p.Key == loginTokenExpireSwitchKey);
                //var LoginTokenExpireHoursSetting =
                //    LoginTokenAllSetting.FirstOrDefault(p => p.Key == LoginTokenExpireHoursKey);

                var LoginTokenExpireSwitch =
                    LoginTokenExpireSwitchSetting?.Value.ToInt() ?? 0;

                var LoginTokenExpireHours =
                    LoginTokenExpireHoursSetting?.Value.ToInt() ?? 0;

                // 校验用户token是否过期
                if (LoginTokenExpireSwitch == 1 && LoginTokenExpireHours > 0)
                {
                    // 历史登录有效期15天
                    var oldHours = 15 * 24;
                    var newHours = LoginTokenExpireHours;
                    var curHours = oldHours - newHours;
                    var expiredTime = token.ExpiredTime.Value.AddHours(-curHours);
                    if (expiredTime < DateTime.Now)
                    {
                        tokenRes = null;
                        Log.WriteError(
                        $"登录令牌过期过期，" +
                        $"过期用户Id:{fxUserIdTemp}," +
                        $"过期TokenId：{token.Id}");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError(
                    $"登录令牌过期校验异常，" +
                    $"异常用户Id:{fxUserIdTemp}," +
                    $"异常原因：{ex.ToJsonExt()}");
            }
            return tokenRes;
        }



        /// <summary>
        /// 是否来源于微信
        /// </summary>
        public static bool IsFromWeixin(string token, string userAgent, string FxWxTokenHeader, string FxWxOpenIdHeader)
        {
            var isWechatBrowser = userAgent.Contains("MicroMessenger");
            var isFromWeixin = isWechatBrowser && IsWeiXinRequest(token, FxWxTokenHeader, FxWxOpenIdHeader);
            return isFromWeixin;
        }
        /// <summary>
        /// 是否来源于阿里轻应用
        /// </summary>
        public static bool IsFromQingWk(string token, string XUserIdHeader)
        {
            var isFromQingWk = !string.IsNullOrEmpty(XUserIdHeader) && IsQingWkRequest(token, XUserIdHeader);
            return isFromQingWk;
        }

        public string RemoveDbNameSpecialChar(string name)
        {
            if (name.IsNullOrEmpty())
                return name;
            var newName = name.Replace("\\t", " ").Replace("\0", "").Replace("", "").Replace("\b", "").Replace("\f", "").Replace("\v", "").Replace("/", "").Replace("", "").Replace("\u001D", "").Replace("'", "\\'");
            return newName;
            ViewBag.FendanLoginUrl = CustomerConfig.FendanLoginUrl;
        }

        public void RedirectToFxLogin(string token, bool isFromWeiXin = false)
        {
            if (Request.IsAjaxRequest() || isFromWeiXin)
                throw new LogicException("登录信息过期，请重新登录", "401");
            Response.Clear();
            Response.Redirect("/");
        }

        public ExportTask GetExportTaskToWeb(ExportTask task)
        {
            if (task == null)
                return null;
            task.ExecSql = string.Empty;
            task.IP = string.Empty;
            task.ParamJson = string.Empty;
            if (task.Type == ExportType.BranchShare.ToInt())
                task.ExtField1 = string.Empty;
            else if (task.Type == ExportType.ExpressBill.ToInt())
                task.ExtField3 = string.Empty;
            //服务器异常信息隐藏
            if (task.ErrorType == TaskErrorType.ServerError.ToString())
                task.Message = string.Empty;

            return task;
        }

        protected string GetPddPageCode()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //cookie中有，不再重新获取
            var cookieKey = fxUserId + "-pdd-pagecode";
            var cookieValue = Request?.Cookies[cookieKey]?.Value?.ToString();
            if (string.IsNullOrEmpty(cookieValue) == false && cookieValue.Length > 0)
                return cookieValue;
            var pddPageCode = "";
            var pddShopIds = new List<int>();
            pddShopIds = new LogicOrderService().GetUserRelatedPddShopIds(fxUserId);
            var pddshops = new List<Shop>();
            if (pddShopIds == null || pddShopIds.Any() == false)
                pddshops = SiteContext.Current.AllShops?.Where(x => x.PlatformType == PlatformType.Pinduoduo.ToString())?.ToList();
            else
            {
                pddshops = _shopService.GetShopsAndShopExtension(pddShopIds);
            }
            var firstPddShop = pddshops?.FirstOrDefault();
            if (firstPddShop != null)
            {
                var pddPlatformService = new Services.PlatformService.PinduoduoPlatformService(firstPddShop);
                var mallIds = pddshops?.Select(f => f.ShopId);
                var pageCode = pddPlatformService.GetPddPageCode(mallIds?.ToList(), Request?.Url?.ToString(), firstPddShop?.Id.ToString());
                pddPageCode = pageCode.Key ? (pageCode.Value ?? "") : "";

                var pddPageCodeCookie = new HttpCookie(cookieKey);
                pddPageCodeCookie.Path = "/";
                pddPageCodeCookie.Expires = DateTime.Now.AddHours(1);
                if (string.IsNullOrEmpty(pddPageCode) && ViewBag.PddPageCode != null)
                    pddPageCodeCookie.Value = ViewBag.PddPageCode.ToString();
                else
                    pddPageCodeCookie.Value = pddPageCode;
                Response.Cookies.Add(pddPageCodeCookie);
            }
            return pddPageCode;
        }
        #endregion

        #region 返回ActionResult
        /// <summary>
        /// 返回结果
        /// </summary>
        /// <param name="data">返回数据</param>        
        /// <returns></returns>
        protected ActionResult SuccessResult(dynamic data = null)
        {
            return new CustomJsonResult(new AjaxResult()
            {
                Success = true,
                Data = data
            });
        }

        protected ActionResult SuccessResult(string message, dynamic data)
        {
            return new CustomJsonResult(new AjaxResult()
            {
                Message = message,
                Success = true,
                Data = data
            });
        }

        protected ActionResult FalidResult(string message, dynamic data = null)
        {
            return new CustomJsonResult(new AjaxResult()
            {
                Success = false,
                Message = message,
                Data = data
            });
        }

        protected ActionResult FalidResult(string message, string errorCode, dynamic data = null)
        {
            return new CustomJsonResult(new AjaxResult()
            {
                Success = false,
                Message = message,
                ErrorCode = errorCode,
                Data = data
            });
        }

        /// <summary>
        /// 返回json数据
        /// </summary>
        /// <param name="data">json数据</param>
        /// <returns></returns>
        public new ActionResult Json(object data)
        {
            return new CustomJsonResult(new AjaxResult
            {
                Success = true,
                Data = data
            });
        }
        /// <summary>
        /// 反序列
        /// </summary>
        private static readonly IContractResolver ContractResolver = new DisabledIgnoreContractResolver();
        /// <summary>
        /// 不能随便用（存在性能问题）
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public new ActionResult JsonDisabledIgnore(object data)
        {
            return new CustomJsonResult(new AjaxResult
            {
                Success = true,
                Data = data,
                ContractResolver = ContractResolver
            });
        }

        /// <summary>
        /// 返回json数据
        /// </summary>
        /// <param name="data">json数据</param>
        /// <returns></returns>
        public ActionResult OrderJson(object data)
        {
            return new CustomOrderJsonResult(new AjaxResult
            {
                Success = true,
                Data = data
            });
        }
        public ActionResult OriginalJson(object data)
        {
            return base.Json(data);
        }


        /// <summary>
        /// 检查登录Cookies
        /// </summary>
        public static bool CheckLoginCookie(CommonSettingService commonSettingService, string loginCookieValue, int isOpenDoubleAuth, LoginAuthToken authToken, string key)
        {
            var needCheckCookies = isOpenDoubleAuth == 1;
            if (isOpenDoubleAuth == -1)
            {
                isOpenDoubleAuth = commonSettingService.Get(key, authToken.ShopId)?.Value.ToInt() ?? 0;
                needCheckCookies = isOpenDoubleAuth == 1;
            }
            //不需要检查，直接返回true
            if (needCheckCookies == false)
                return true;

            try
            {
                if (string.IsNullOrEmpty(loginCookieValue))
                    return false;

                var loginInfo = DES.DecryptUrl(loginCookieValue, CustomerConfig.LoginCookieEncryptKey).Split(',');
                if (loginInfo.Length == 2)
                {
                    var authTokenId = loginInfo[0].ToInt();
                    var expireTime = loginInfo[1].ToDateTime();

                    if (authTokenId != authToken.Id)
                    {
                        //日志
                        LoginLogService.Instance.WriteLog(new LoginLogModel
                        {
                            OperationType = "CheckLoginCookie",
                            ExpireTime = expireTime,
                            LoginAuthTokenId = authTokenId,
                            Ext1 = $"AuthToken不一致",
                            Ext2 = $"Cookie-AuthTokenId={authTokenId} != {authToken.Id}",
                        });
                        return false;
                    }
                    if (expireTime == null)
                    {
                        //日志
                        LoginLogService.Instance.WriteLog(new LoginLogModel
                        {
                            OperationType = "CheckLoginCookie",
                            ExpireTime = expireTime,
                            LoginAuthTokenId = authTokenId,
                            Ext1 = $"过期时间为空",
                            Ext2 = $"{loginCookieValue}",
                        });
                        return false;
                    }
                    if (expireTime < DateTime.Now)
                    {
                        //日志
                        LoginLogService.Instance.WriteLog(new LoginLogModel
                        {
                            OperationType = "CheckLoginCookie",
                            ExpireTime = expireTime,
                            LoginAuthTokenId = authTokenId,
                            Ext1 = $"已过期",
                            Ext2 = $"{loginCookieValue}",
                        });
                        return false;
                    }
                }
                else
                {
                    //日志
                    LoginLogService.Instance.WriteLog(new LoginLogModel
                    {
                        OperationType = "CheckLoginCookie",
                        LoginAuthTokenId = authToken.Id,
                        Ext1 = $"Cookie数据异常",
                        Ext2 = $"{loginCookieValue}",
                    });
                    return false;
                }

            }
            catch (Exception ex)
            {
                //日志
                LoginLogService.Instance.WriteLog(new LoginLogModel
                {
                    OperationType = "CheckLoginCookie",
                    LoginAuthTokenId = authToken.Id,
                    Ext1 = $"检查Cookie数据异常",
                    Ext2 = $"{ex.Message}",
                });
                return false;
            }
            return true;
        }

        /// <summary>
        /// 返回json数据
        /// </summary>
        /// <param name="success">是否成功</param>
        /// <param name="data">json数据</param>
        /// <returns></returns>
        public ActionResult Json(bool success, object data)
        {
            return new CustomJsonResult(new AjaxResult
            {
                Success = success,
                Data = data
            });
        }

        /// <summary>
        /// 返回json数据
        /// </summary>
        /// <param name="result">json数据</param>
        /// <returns></returns>
        public ActionResult Json(AjaxResult result)
        {
            return new CustomJsonResult(result);
        }

        public ActionResult Error()
        {
            return View("~/Views/Shared/Error.html");
        }

        #endregion

        #region 页面请求属性

        private HttpRequestBase _Request = null;
        /// <summary>
        /// Http请求，可以获取当前上下文请求+增加外部传入
        /// </summary>
        protected new HttpRequestBase Request
        {
            get
            {
                if (_Request == null)
                {
                    _Request = HttpContext?.Request;
                }
                return _Request;
            }
            set
            {
                _Request = value;
            }
        }


        private string _pageToken = null;
        /// <summary>
        /// 当前页面请求Token
        /// </summary>
        public string PageToken
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_pageToken))
                {
                    _pageToken = Request["token"].ToString2();
                }
                return _pageToken;

            }
        }

        private string _CurrentDBName = null;
        /// <summary>
        /// 当前分区数据库（已解密）
        /// </summary>
        protected string CurrentDBName
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_CurrentDBName))
                {
                    //新增加的数据库分区
                    string dbname = Request["dbname"].ToString2();
                    if (!string.IsNullOrEmpty(dbname))
                    {
                        _CurrentDBName = DES.DecryptDES(dbname, CustomerConfig.LoginCookieEncryptKey);
                    }
                }
                return _CurrentDBName;
            }
        }

        public static string GetCurrentDBName(string dbname)
        {
            if (!string.IsNullOrEmpty(dbname))
            {
                dbname = HttpUtility.UrlDecode(dbname);
                return DES.DecryptDES(dbname, CustomerConfig.LoginCookieEncryptKey);
            }
            else
                return "";
        }


        private string _currentIP = null;
        /// <summary>
        /// 当前IP
        /// </summary>
        protected string CurrentIP
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_currentIP))
                {
                    _currentIP = GetCurrentIP(Request?.Headers["X-Forwarded-For"], Request?.UserHostAddress);
                }
                return _currentIP;
            }
        }

        public static string GetCurrentIP(string XForwardedForHeader, string requestUserHostAddress)
        {
            string ip = XForwardedForHeader;
            if (string.IsNullOrEmpty(ip))
            {
                ip = requestUserHostAddress;
            }
            return ip;
        }

        private string _UserAgent = null;
        /// <summary>
        /// 用户代理
        /// </summary>
        protected string UserAgent
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_UserAgent))
                {
                    _UserAgent = this.Request?.Headers.Get("User-Agent") ?? "";
                }
                return _UserAgent;
            }
        }

        public bool IsCustomerOrder
        {
            get
            {
                var fp = Request.Form["IsCustomerOrder"];
                var qp = Request.QueryString["IsCustomerOrder"];

                return (fp.IsNullOrEmpty() == false && fp.ToBool() == true)
                    || (qp.IsNullOrEmpty() == false && qp.ToBool() == true);
            }
        }

        public string RequestHost
        {
            get
            {
                var scheme = CustomerConfig.ApplicationWebUrl.ToString2().ToLower().StartsWith("https") ? "https" : "http";
                var newAppUrl = scheme + "://" + (string.IsNullOrWhiteSpace(Request?.Url?.Host) ? "1688.dgjapp.com" : Request?.Url?.Host); //HttpContext.Server.UrlEncode(CustomerConfig.ApplicationWebUrl);
                return newAppUrl;
            }
        }

        public string Get1688AuthUrl()
        {
            var host = RequestHost;
            var token = Request["token"];
            var succtourl = $"{host?.TrimEnd('/')}/SupplySet1688/BuyerAuthBindCallback?token={token}";
            var authUrl = $"https://zk.dgjapp.com/auth/fxbuyeralibaba?SuccToUrl={Server.UrlEncode(succtourl)}";

            return authUrl;
        }

        #endregion

        #region 废弃代码
        //public void RedirectToLogin(string token, bool isFromWeiXin = false)
        //{
        //    if (Request.IsAjaxRequest() || isFromWeiXin)
        //        throw new LogicException("登录信息过期，请重新登录", "401");
        //    Response.Clear();

        //    var authEntry = string.Empty;
        //    var platform = string.Empty;
        //    if (string.IsNullOrWhiteSpace(token) == false)
        //    {
        //        platform = SiteContext.CurrentNoThrow?.CurrentLoginShop?.PlatformType;
        //    }
        //    if (string.IsNullOrWhiteSpace(platform) == true)
        //    {
        //        platform = CustomerConfig.Platform;
        //    }

        //    authEntry = CustomerConfig.GetShopAuthLink(platform);

        //    var authEntryUrl = $"{authEntry}";
        //    if (string.IsNullOrWhiteSpace(token) == false)
        //    {
        //        authEntryUrl += $"?rp={token}";
        //    }
        //    try
        //    {
        //        Response.Redirect(authEntryUrl);
        //        Response.End();
        //    }
        //    catch
        //    {

        //    }
        //}

        //private int GetShopSyncProcessEnabled(Shop shop)
        //{
        //    var result = 0;
        //    if (shop.IsPddFactorer == true && Request?.Url?.ToString().ToLower().Contains("indexfds") == true)
        //    {
        //        var pddFdsSyncParamter = new CommonSettingService().GetOrderSyncStatus(shop.Id);
        //        if (pddFdsSyncParamter?.LastSyncTime <= DateTime.Now.AddDays(-0.25))
        //        {
        //            result = 1;
        //        }
        //    }
        //    else
        //        result = (shop.LastSyncTime <= DateTime.Now.AddDays(-0.25) || shop.IsSyncProcessEnabled) ? 1 : 0;
        //    return result;
        //}

        ///// <summary>
        ///// 检查店铺是否过期
        ///// </summary>
        //public Shop CheckIsExpired(Shop shop, bool isThrow)
        //{
        //    if (!string.IsNullOrEmpty(shop.PayUrl) && shop.ExpireTime < DateTime.Now && isThrow && !CustomerConfig.IsDebug)
        //        throw new LogicException("非常抱歉，您的应用已经到期了，请前往续费。", "ShopExpired");
        //    else if (SiteContext.Current.MigratingShopNames != null && SiteContext.Current.MigratingShopNames.Any())
        //    {
        //        throw new LogicException("为了提升您的订单发货效率，我们正在将您的店铺数据迁往多多云服务器，预计会持续10分钟，在此期间不能打单发货操作，请10分钟之后再刷新页面。", "ShopMigrating");
        //    }
        //    return shop;
        //}

        #endregion
    }
}