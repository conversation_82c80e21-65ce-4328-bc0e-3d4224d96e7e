using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.Controllers
{
    public class CorrelationController : BaseController
    {
        ShopService _shopService = new ShopService();
        ShopRelationService _relationService = new ShopRelationService();
        BranchShareRelationService _shareRelationService = new BranchShareRelationService();

        public ActionResult Index()
        {
            return View(SiteContext.Current);
        }

        public ActionResult LoadList()
        {
            var list = new List<Shop>();
            var shops = SiteContext.Current.ChildShop?.Where(f => f.Relation.ShopId == SiteContext.Current.CurrentShopId)?.ToList();
            if (shops?.Any() == false)
            {
                if (SiteContext.Current.MasterShop?.Id != SiteContext.Current.CurrentShopId)
                    list.Add(SiteContext.Current.MasterShop);
            }
            else
            {
                list.AddRange(shops);
            }

            return SuccessResult(list);
        }

        public ActionResult UpdateShopOnlyCode()
        {
            var onlyCode = (Guid.NewGuid().ToString("N") + DateTime.Now.ToString("yyyyMMddHHmmssfff")).ToMd5().ToUpper();
            bool result = _shopService.UpdateShopOnlyCode(SiteContext.Current.CurrentShopId, onlyCode);
            if (result)
                return SuccessResult(onlyCode);
            else
                return FalidResult("更新失败");
        }

        public ActionResult AddShopRelation(string platformType, string onlyCode, bool isUseFahuoTemplate, bool isUseExpressTemplate, bool isVisibleToEachOther, bool isStopShareByCurrentShop, bool isStopShareByFormId, bool isStopShareByToId)
        {
            if (string.IsNullOrEmpty(onlyCode))
                throw new LogicException("请输入要添加的店铺的关联代码");

            platformType = CustomerConfig.ConvertToRealPlatformType(platformType);
            var shopModel = _shopService.Get("WHERE PlatformType=@PlatformType AND OnlyCode=@OnlyCode;", new { PlatformType = platformType, OnlyCode = onlyCode }).FirstOrDefault();
            if (shopModel == null && CustomerConfig.CloudPlatformType == PlatformType.Alibaba.ToString())
            {
                //TODO:在老系统中查找，并且需要在老系统中增加关联，但老系统中取消了关联呢？？？
                var oldShop = _shopService.GetOldShopByJoinCode(onlyCode, platformType);
                if (oldShop == null)
                    return FalidResult(string.Format("在所选平台下，根据关联代码【{0}】未能查找到店铺，请确认关联代码是否正确", onlyCode));
                else
                {
                    //判断当前添加的店铺是否已经添加到其他店铺上去了
                    var rels = _shopService.GetOldRelations(oldShop.MemberId, platformType);
                    if (rels != null && rels.Any())
                        throw new LogicException($"当前关联店铺已被关联过了，不能再次进行关联");
                    else
                    {
                        //添加关联，若店铺不存在与新系统需添加
                        var curShop = _shopService.Get(" where ShopId=@mid AND PlatformType=@pt", new { mid = oldShop.MemberId, pt = platformType })?.FirstOrDefault();
                        var shopId = curShop?.Id ?? 0;
                        if (curShop == null)
                        {
                            throw new LogicException($"当前关联店铺使用的是旧系统，无法关联至新系统，请联系客服升级系统后再进行关联。");
                            //shopId = _shopService.Add(new Shop {
                            //    ShopId = oldShop.MemberId,
                            //    AccessToken = oldShop.Access_Token,
                            //    ShopName = oldShop.Resource_Owner,
                            //    RefreshToken = oldShop.Refresh_Token,
                            //    CreateTime = DateTime.Now,
                            //    PlatformType = platformType,
                            //    MigrateStatus = ShopMigrateStatusType.NotMigrated.ToString()
                            //});
                        }

                        var relation = new ShopRelation()
                        {
                            RelatedShopId = shopId,
                            IsUseExpressTemplate = isUseExpressTemplate,
                            IsUseFahuoTemplate = isUseFahuoTemplate,
                            IsVisibleToEachOther = isVisibleToEachOther,
                            ShopId = SiteContext.Current.CurrentShopId,
                            CreateTime = DateTime.Now,
                        };
                        var code = _relationService.Add(relation);
                        if (code > 0)
                            return SuccessResult(onlyCode);
                        else
                            return FalidResult("关联失败，请稍后重试");
                    }
                }
            }

            if (shopModel == null)
                return FalidResult(string.Format("在所选平台下，根据关联代码【{0}】未能查找到店铺，请确认关联代码是否正确", onlyCode));

            //校验要关联的店铺是否已经被关联过或者关联过别的店铺
            var existRelation = _relationService.Get("WHERE RelatedShopId=@ShopId OR ShopId=@ShopId", new { ShopId = shopModel.Id });
            if (existRelation != null && existRelation.Any())
            {
                //判断是否是其他店铺的子店铺
                var rel = existRelation.FirstOrDefault();
                if (rel.ShopId == SiteContext.Current.CurrentShopId)
                {
                    return SuccessResult(onlyCode);
                }
                else if (rel.ShopId != shopModel.Id)
                {
                    var master = _shopService.Get(rel.ShopId);
                    if (master != null)
                        return FalidResult($"该关联代码对应的店铺【{shopModel.NickName}】，已被店铺【{master.NickName}】关联，请先进入店铺【{master.NickName}】取消关联后，再进行关联.");
                }
                else
                {
                    return FalidResult($"关联代码对应的店铺【{shopModel.NickName}】，已经关联了其他店铺，不能再被关联.");
                }
            }

            //校验当前店铺是否已已经被关联过（子店铺不能再关联子店铺）
            var isChildShop = _relationService.Get("WHERE RelatedShopId=@ShopId", new { ShopId = SiteContext.Current.CurrentShopId });
            if (isChildShop != null && isChildShop.Any())
            {
                var rel = isChildShop.FirstOrDefault();
                var master = _shopService.Get(rel.ShopId);
                if (master != null)
                    return FalidResult($"当前店铺【{SiteContext.Current.CurrentLoginShop.NickName}】，已被店铺【{master.NickName}】关联，不能再关联其他店铺.");
            }

            //2919\12\31,zouyi,添加关联店铺与分享的关系校验
            //1.A关联B的时候，检测A是否有分享给B,如果有分享，则自动终止分享。优先关联。
            //2.A关联B的时候，检查B是否有分享面单账号给其他店铺使用，B作为关联的子店铺不能存在分享。
            //3.A关联B的时候，检查B是否有接受其他店铺的分享，B作为关联的子店铺不能存在分享。
            var shareRelationList = _shareRelationService.GetListByShopId(shopModel.Id);
            if (shareRelationList?.Count > 0)
            {
                var model = shareRelationList.Where(f => f.FromId == SiteContext.Current.CurrentShopId && f.ToId == shopModel.Id);
                if (model != null && model.Any())
                {
                    if (isStopShareByCurrentShop)
                    {
                        //1.A分享给了B，再关联，就终止分享( 终止之前，让用户确认）
                        model.ToList().ForEach(f =>
                        {
                            var log = GetLog(f);
                            _shareRelationService.UpdateShareStatus(f.Id, BranchShareStatus.StopedByRelationShop.ToString(), log);
                        });
                    }
                    else
                    {
                        return FalidResult($"关联的店铺【{shopModel.NickName}】有来自当前店铺的电子面单分享，确认关联，将终止电子面单分享。", 1);
                    }
                }
                var tempList = shareRelationList.Where(f => f.FromId == shopModel.Id);
                if (tempList != null && tempList.Any())
                {
                    if (isStopShareByFormId)
                    {
                        //2.B有分享给其他，再关联，就终止分享( 终止之前，让用户确认）
                        tempList.ToList().ForEach(f =>
                        {
                            var log = GetLog(f);
                            _shareRelationService.UpdateShareStatus(f.Id, BranchShareStatus.StopedByRelationShop.ToString(), log);
                        });
                    }
                    else
                    {
                        return FalidResult($"关联的店铺【{shopModel.NickName}】有分享电子面单给其他店铺，确认关联，将终止电子面单分享。", 2);
                    }
                }

                var tempListByToId = shareRelationList.Where(f => f.FromId != SiteContext.Current.CurrentShopId && f.ToId == shopModel.Id);
                if (tempListByToId != null && tempListByToId.Any())
                {
                    if (isStopShareByToId)
                    {
                        //2.B有接受其他店铺的分享，再关联，就终止分享( 终止之前，让用户确认）
                        tempListByToId.ToList().ForEach(f =>
                        {
                            var log = GetLog(f);
                            _shareRelationService.UpdateShareStatus(f.Id, BranchShareStatus.StopedByRelationShop.ToString(), log);
                        });
                    }
                    else
                    {
                        return FalidResult($"关联的店铺【{shopModel.NickName}】有来自其他店铺的分享，确认关联，将终止电子面单分享。", 3);
                    }
                }
            }

            var relationShop = new ShopRelation()
            {
                RelatedShopId = shopModel.Id,
                IsUseExpressTemplate = isUseExpressTemplate,
                IsUseFahuoTemplate = isUseFahuoTemplate,
                IsVisibleToEachOther = isVisibleToEachOther,
                ShopId = SiteContext.Current.CurrentShopId,
                CreateTime = DateTime.Now,
            };
            if (relationShop.RelatedShopId == relationShop.ShopId)
                throw new LogicException("您填写的关联代码是当前店铺的，请填写其他店铺的关联代码。");
            var result = _relationService.Add(relationShop);

            if (result > 0)
                return SuccessResult(onlyCode);
            else
                return FalidResult("关联失败");
        }

        public ActionResult updateRelation(int relationId, bool isUseFahuoTemplate, bool isUseExpressTemplate, bool isVisibleToEachOther)
        {
            if (relationId == 0)
            {
                return FalidResult("修改失败");
            }
            var relation = _relationService.Get(relationId);

            relation.IsUseExpressTemplate = isUseExpressTemplate;
            relation.IsUseFahuoTemplate = isUseFahuoTemplate;
            relation.IsVisibleToEachOther = isVisibleToEachOther;

            var result = _relationService.Update(relation);

            if (result)
                return SuccessResult();
            else
                return FalidResult("修改失败");
        }

        public ActionResult deleteRelation(int relationId)
        {
            bool result = _relationService.DeleteRelation(relationId, SiteContext.Current.CurrentShopId);
            if (result)
                return SuccessResult();
            else
                return FalidResult("更新失败");
        }

        //[LogForOperatorFilter("检查是否需要迁移数据")]
        //public ActionResult CheckIsNeedMigrateData(string onlyCode,int shopId=0)
        //{
        //    try
        //    {
        //        _shopService.CheckIsNeedMigrateData(onlyCode, shopId);
        //    }
        //    catch (LogicException ex)
        //    {
        //        return FalidResult(ex.Message);
        //    }
        //    catch (Exception ex)
        //    {
        //        return FalidResult("服务器繁忙，请稍后重试");
        //    }

        //    var result = new { TaskIds = new List<int> { 0 } };
        //    return SuccessResult(result);
        //}

        //[LogForOperatorFilter("检查是否需要迁移数据")]
        //public ActionResult CheckIsNeedMigrateDataV3Old(string onlyCode)
        //{
        //    var shop = _shopService.GetByOnlyCode(onlyCode);
        //    if (shop == null)
        //        return FalidResult("未查询到店铺");
        //    if (shop.PlatformType != PlatformType.Pinduoduo.ToString())
        //        return FalidResult("非拼多多店铺");
        //    var shops = SiteContext.Current.AllShops.Where(s => s.Id != shop.Id && s.PlatformType == shop.PlatformType).ToList();
        //    if (shops == null || !shops.Any())
        //        return FalidResult("该平台仅一个店铺，不需要迁移");
        //    var shopIds = shops.Select(s => s.Id).ToList();
        //    shopIds.Add(shop.Id);
        //    //暂以第一个关联的店铺数据库为目标数据库
        //    var firstShopRelation = _shopService.GetShopRelations(SiteContext.Current.CurrentShopId)
        //        .Where(s => shopIds.Contains(s.ShopId))
        //        .OrderBy(s => s.Id)
        //        .FirstOrDefault();
        //    var dbConfigs = DbPolicyExtension.GetConfig(new List<int> { shop.Id, firstShopRelation.ShopId });
        //    if (dbConfigs == null || dbConfigs.Any() == false)
        //        return FalidResult("未查询到店铺数据库配置信息");
        //    var isSame = dbConfigs.Count() == 2 && dbConfigs.GroupBy(d => d.DbServer.Id + d.DbNameConfig.DbName + d.DbNameConfig.HotTableNameTag + d.DbNameConfig.ColdTableNameTag).Count() == 1;
        //    if (isSame)
        //        return FalidResult("当前添加的关联店铺，和主店铺中的其他同平台店铺在同一数据库、同一热表、同一冷表，不需要迁移");
        //    var targetDbNameId = 0;
        //    var sourceDbNameId = 0;
        //    //都有数据库配置，若是拼多多用户则以多多云为主库，其他平台，以主店铺数据库为准
        //    if (dbConfigs.Count() == 2)
        //    {
        //        if (shop.PlatformType == PlatformType.Pinduoduo.ToString())
        //        {
        //            targetDbNameId = dbConfigs.FirstOrDefault(d => d.DbServer.Location == PlatformType.Pinduoduo.ToString())?.DbNameConfig.Id ?? 0;
        //            //TODO:分库分表暂不处理，逻辑待定
        //        }
        //        else
        //        {

        //        }
        //    }
        //    else if (dbConfigs.Count() == 1)
        //    {
        //        //其中一个有数据库配置，另一个没，则以有的为准
        //    }

        //    var targetDbConfig = dbConfigs.FirstOrDefault(d => d.DbConfig.ShopId == firstShopRelation.ShopId);
        //    var sourceDbConfig = dbConfigs.FirstOrDefault(d => d.DbConfig.ShopId == shop.Id);
        //    //查询店铺订单数量
        //    var orderCount = DbPolicyExtension.GetShopOrderCount(shop);
        //    var maxOrderCount = new CommonSettingService().GetMaxOrderCountToMigrateInTime();
        //    var task = new DataMigrateTask
        //    {
        //        SourceDbNameConfigId = sourceDbConfig.DbNameConfig.Id,
        //        TargetDbNameConfigId = targetDbConfig.DbNameConfig.Id,
        //        ShopId = shop.Id,
        //        CreateTime = DateTime.Now,
        //    };
        //    if (orderCount > maxOrderCount)
        //    {
        //        var hopeTime = DateTime.Now.AddDays(1);
        //        task.HopeMigrateTime = new DateTime(hopeTime.Year, hopeTime.Month, hopeTime.Day, 2, 0, 0);
        //    }
        //    DbPolicyExtension.CreateDataMigrateTask(task);
        //    return SuccessResult(task);
        //}

        //[LogForOperatorFilter("检查是否需要迁移数据")]
        //public ActionResult CheckIsNeedMigrateDataV2Old(string onlyCode)
        //{
        //    var log = Data.Model.LogForOperatorContext.Current;
        //    var shop = _shopService.GetByOnlyCode(onlyCode);
        //    if (shop == null)
        //        return FalidResult("未查询到店铺");
        //    if (shop.PlatformType != PlatformType.Pinduoduo.ToString())
        //        return FalidResult("非拼多多店铺");
        //    var shopDbConfig = DbPolicyExtension.GetConfig(new List<int> { shop.Id }).FirstOrDefault();
        //    shop.DbConfig = shopDbConfig;
        //    var existAllPddShops = SiteContext.Current.AllShops.Where(s => s.PlatformType == PlatformType.Pinduoduo.ToString()).ToList();
        //    if (existAllPddShops == null || existAllPddShops.Any() == false)
        //        return FalidResult("无其他拼多多用户");
        //    var allpddshops = new List<Shop>();
        //    allpddshops.Add(shop);
        //    existAllPddShops.ForEach(a =>
        //    {
        //        if (allpddshops.Any(x => x.Id == a.Id) == false)
        //            allpddshops.Add(a);
        //    });
        //    var shopLocations = new Dictionary<int, string>();
        //    allpddshops?.ForEach(a =>
        //    {
        //        if (a.DbConfig == null)
        //            shopLocations.Add(a.Id, PlatformType.Alibaba.ToString());
        //        else
        //            shopLocations.Add(a.Id, a.DbConfig.Identity);
        //    });
        //    //检查是否同云平台，同云平台同数据库不用迁移
        //    if (shopLocations.Select(s => s.Value).Distinct().Count() == 1)
        //        return FalidResult("同云平台同库，不需要迁移");
        //    //找出不在多多云的店铺
        //    //优先取多多云里面使用最多的那个库为目标库
        //    var targetDbLocationShopId = shopLocations.Where(s => s.Value != PlatformType.Alibaba.ToString()).GroupBy(s => s.Value).OrderByDescending(x => x.Count()).FirstOrDefault()?.FirstOrDefault().Key ?? 0;
        //    //其次取在多多云的库
        //    if (targetDbLocationShopId == 0)
        //        targetDbLocationShopId = shopLocations.Where(s => s.Value != PlatformType.Alibaba.ToString())?.FirstOrDefault().Key ?? 0;
        //    //再其次，使用最少数据量的库
        //    var targetDbNameConfigId = 11;
        //    var needMigrateShopIds = new List<int>();
        //    if (targetDbLocationShopId == 0)
        //    {
        //        var dbNameConfig = DbPolicyExtension.GetBestDbNameConfig(PlatformType.Pinduoduo.ToString(), PlatformType.Pinduoduo.ToString());
        //        if (dbNameConfig != null)
        //            targetDbNameConfigId = dbNameConfig.Id;
        //        //目标店铺位置为空，则在阿里云平台，但数据库不一样？
        //        needMigrateShopIds = shopLocations.Where(s => s.Value == PlatformType.Alibaba.ToString()).Select(s => s.Key).ToList();
        //    }
        //    else
        //    {
        //        var targetShopLocation = shopLocations.FirstOrDefault(s => s.Key == targetDbLocationShopId).Value;
        //        var dbNameConfigId = allpddshops.First(x => x.Id == targetDbLocationShopId)?.DbConfig?.DbNameConfig?.Id ?? 0;
        //        if (dbNameConfigId > 0)
        //            targetDbNameConfigId = dbNameConfigId;
        //        //目标店铺位置不为空
        //        needMigrateShopIds = shopLocations.Where(s => s.Value != targetShopLocation).Select(s => s.Key).ToList();
        //    }
        //    log.AppendStep(new LogForOperator
        //    {
        //        OperatorType = "查询同平台店铺",
        //        Response = new { shopLocations, allpddshops, targetDbLocationShopId, targetDbNameConfigId, needMigrateShopIds },
        //    });
        //    if (needMigrateShopIds == null || needMigrateShopIds.Any() == false)
        //        return FalidResult("没有不在多多云的店铺，需要迁移");
        //    //添加迁移任务，检查数据大小
        //    //查询店铺订单数量
        //    var totalOrderCount = 0;
        //    var tasks = new List<DataMigrateTask>();
        //    foreach (var sid in needMigrateShopIds)
        //    {
        //        var curShop = allpddshops.FirstOrDefault(s => s.Id == sid);
        //        var sourceDbNameConfigId = 4;//默认为阿里云拼多多库
        //        if (curShop.DbConfig != null)
        //            sourceDbNameConfigId = curShop.DbConfig.DbNameConfig.Id;
        //        try
        //        {
        //            var orderCount = DbPolicyExtension.GetShopOrderCount(curShop);
        //            totalOrderCount += orderCount;
        //        }
        //        catch (Exception ex)
        //        {
        //            Log.WriteError($"获取店铺[{curShop.Id}]订单数量时发生错误：{ex}");
        //        }
        //        var task = new DataMigrateTask
        //        {
        //            SourceDbNameConfigId = sourceDbNameConfigId,
        //            TargetDbNameConfigId = targetDbNameConfigId,
        //            ShopId = curShop.Id,
        //            CreateTime = DateTime.Now,
        //        };
        //        tasks.Add(task);
        //    }
        //    var maxOrderCount = new CommonSettingService().GetMaxOrderCountToMigrateInTime();
        //    DateTime? hopeTime = null;
        //    if (totalOrderCount <= maxOrderCount)
        //        hopeTime = null;
        //    else
        //        hopeTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 2, 0, 0);
        //    tasks.ForEach(t =>
        //    {
        //        t.HopeMigrateTime = hopeTime;
        //        var oldTask = DbPolicyExtension.GetDataMigrateTaskByShopId(t.ShopId);
        //        if (oldTask == null)
        //        {
        //            if (CustomerConfig.IsDebug == false)
        //                DbPolicyExtension.CreateDataMigrateTask(t);
        //        }
        //        else
        //            Log.WriteWarning($"添加关联店铺的迁移任务时，查询到迁移任务已存在，不做添加，店铺ID:{t.ShopId}");
        //        log.AppendStep(new LogForOperator
        //        {
        //            OperatorType = "添加迁移任务",
        //            ShopId = t.ShopId,
        //            Request = t,
        //            Remark = oldTask == null ? "不存在，添加成功" : "查询到迁移任务已存在，不做添加"
        //        });
        //    });
        //    return SuccessResult(new { TaskIds = tasks.Select(t => t.Id).ToList(), HopeMigrateTime = tasks.FirstOrDefault().HopeMigrateTime });
        //}

        //[LogForOperatorFilter("检查是否需要迁移数据")]
        //public ActionResult CheckIsNeedMigrateDataV2(string onlyCode)
        //{
        //    var log = Data.Model.LogForOperatorContext.Current;
        //    var shop = _shopService.GetByOnlyCode(onlyCode);
        //    if (shop == null)
        //        return FalidResult("未查询到店铺");
        //    //if (shop.PlatformType != PlatformType.Pinduoduo.ToString())
        //    //    return FalidResult("非拼多多店铺");
        //    var shopDbConfig = DbPolicyExtension.GetConfig(new List<int> { shop.Id }).FirstOrDefault();
        //    shop.DbConfig = shopDbConfig;
        //    var existAllShops = SiteContext.Current.AllShops.Where(s => s.PlatformType == shop.PlatformType).ToList();
        //    if (existAllShops == null || existAllShops.Any() == false)
        //        return FalidResult($"无其他同平台【{shop.PlatformType}】用户");
        //    var allSamePlatformShops = new List<Shop>();
        //    allSamePlatformShops.Add(shop);
        //    existAllShops.ForEach(a =>
        //    {
        //        if (allSamePlatformShops.Any(x => x.Id == a.Id) == false)
        //            allSamePlatformShops.Add(a);
        //    });
        //    var shopLocations = new Dictionary<int, string>();
        //    allSamePlatformShops?.ForEach(a =>
        //    {
        //        if (a.DbConfig == null)
        //            shopLocations.Add(a.Id, PlatformType.Alibaba.ToString());
        //        else
        //            shopLocations.Add(a.Id, a.DbConfig.Identity);
        //    });
        //    //检查是否同云平台，同云平台同数据库不用迁移
        //    if (shopLocations.Select(s => s.Value).Distinct().Count() == 1)
        //        return FalidResult("同云平台同库，不需要迁移");
        //    //找出不在多多云的店铺
        //    //优先取使用最多的那个库为目标库
        //    var targetDbLocationShopId = shopLocations.GroupBy(s => s.Value).OrderByDescending(x => x.Count()).FirstOrDefault()?.FirstOrDefault().Key ?? 0;
        //    //其次取第一个库
        //    if (targetDbLocationShopId == 0)
        //        targetDbLocationShopId = shopLocations.OrderBy(s => s.Key).FirstOrDefault().Key;
        //    //再其次，使用最少数据量的库
        //    var targetDbNameConfigId = 0;
        //    var needMigrateShopIds = new List<int>();
        //    if (targetDbLocationShopId == 0)
        //        return FalidResult($"店铺【{shop.Id} {shop.ShopName}】无法获取目标数据库的店铺ID");
        //    else
        //    {
        //        var targetShopLocation = shopLocations.FirstOrDefault(s => s.Key == targetDbLocationShopId).Value;
        //        var dbNameConfigId = allSamePlatformShops.First(x => x.Id == targetDbLocationShopId)?.DbConfig?.DbNameConfig?.Id ?? 0;
        //        if (dbNameConfigId > 0)
        //            targetDbNameConfigId = dbNameConfigId;
        //        //目标店铺位置不为空
        //        needMigrateShopIds = shopLocations.Where(s => s.Value != targetShopLocation).Select(s => s.Key).ToList();
        //    }
        //    log.AppendStep(new LogForOperator
        //    {
        //        OperatorType = "查询同平台店铺",
        //        Response = new { shopLocations, allSamePlatformShops, targetDbLocationShopId, targetDbNameConfigId, needMigrateShopIds }.ToJson(),
        //    });
        //    log.EndStep();
        //    if (needMigrateShopIds == null || needMigrateShopIds.Any() == false)
        //        return FalidResult("所有同平台店铺的数据库配置，和目标库一致，不需要迁移");
        //    //添加迁移任务，检查数据大小
        //    //查询店铺订单数量
        //    var totalOrderCount = 0;
        //    var tasks = new List<DataMigrateTask>();
        //    foreach (var sid in needMigrateShopIds)
        //    {
        //        var curShop = allSamePlatformShops.FirstOrDefault(s => s.Id == sid);
        //        var sourceDbNameConfigId = 0;//若为0，不能自动迁移，0的情况是还没有完成分库分表
        //        if (curShop.DbConfig != null)
        //            sourceDbNameConfigId = curShop.DbConfig.DbNameConfig.Id;
        //        else
        //            continue;//没有分库分表，不要自动迁移
        //        try
        //        {
        //            var orderCount = DbPolicyExtension.GetShopOrderCount(curShop);
        //            totalOrderCount += orderCount;
        //        }
        //        catch (Exception ex)
        //        {
        //            Log.WriteError($"获取店铺[{curShop.Id}]订单数量时发生错误：{ex}");
        //        }
        //        var task = new DataMigrateTask
        //        {
        //            SourceDbNameConfigId = sourceDbNameConfigId,
        //            TargetDbNameConfigId = targetDbNameConfigId,
        //            ShopId = curShop.Id,
        //            CreateTime = DateTime.Now,
        //        };
        //        tasks.Add(task);
        //    }
        //    if (tasks.Any() == false)
        //        return FalidResult("可迁移的店铺还未完成分库分表迁移，中断迁移操作。");
        //    var maxOrderCount = new CommonSettingService().GetMaxOrderCountToMigrateInTime();
        //    DateTime? hopeTime = null;
        //    if (totalOrderCount <= maxOrderCount)
        //        hopeTime = null;
        //    else
        //        hopeTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 2, 0, 0);
        //    tasks.ForEach(t =>
        //    {
        //        t.HopeMigrateTime = hopeTime;
        //        var oldTask = DbPolicyExtension.GetDataMigrateTaskByShopId(t.ShopId);
        //        if (oldTask == null)
        //        {
        //            if (CustomerConfig.IsDebug == false)
        //                DbPolicyExtension.CreateDataMigrateTask(t);
        //        }
        //        else
        //            Log.WriteWarning($"添加关联店铺的迁移任务时，查询到迁移任务已存在，不做添加，店铺ID:{t.ShopId}");
        //        log.AppendStep(new LogForOperator
        //        {
        //            OperatorType = "添加迁移任务",
        //            ShopId = t.ShopId,
        //            Request = t,
        //            Remark = oldTask == null ? "不存在，添加成功" : "查询到迁移任务已存在，不做添加"
        //        });
        //        log.EndStep();
        //    });
        //    return SuccessResult(new { TaskIds = tasks.Select(t => t.Id).ToList(), HopeMigrateTime = tasks.FirstOrDefault().HopeMigrateTime });
        //}

        //[LogForOperatorFilter("创建迁移数据任务")]
        //public ActionResult CreateDataMigrateTask(DataMigrateTask task)
        //{
        //    if(task==null)
        //        return FalidResult("添加失败，您没有提交任何数据");
        //    if(task.HopeMigrateTime!=null && task.HopeMigrateTime<DateTime.Now.AddMinutes(-5))
        //        return FalidResult($"操作中止，为保证迁移顺利，预约迁移时间最早应设为五分钟之后");
        //    if(task.Id<=0)
        //    {
        //        if (task.SourceDbNameConfigId <= 0 || task.TargetDbNameConfigId <= 0 || task.ShopId <= 0)
        //            return FalidResult("数据有误，请确认后重新提交");
        //        var shop = _shopService.Get(task.ShopId);
        //        if (SiteContext.Current.AllShops.Exists(s => s.Id == shop.Id) == false)
        //            return FalidResult($"操作终止，当前关联店铺【{shop.ShopName}】已被取消关联");
        //        task.MigrateStatus = "";
        //        task.CreateTime = DateTime.Now;
        //        task.MigrateMessage = "";
        //        DbPolicyExtension.CreateDataMigrateTask(task);
        //    }
        //    else
        //    {
        //        //更新HopeMigrateTime
        //        DbPolicyExtension.UpdateDataMigrateTask(task.Id,task.HopeMigrateTime.Value);
        //    }
        //    return SuccessResult(task.Id);
        //}

        [LogForOperatorFilter("更新迁移数据任务预约时间")]
        public ActionResult UpdateDataMigrateTask(List<int> id = null, DateTime? HopeMigrateTime = null)
        {
            if (id == null || id.Any() == false)
                return FalidResult("添加失败，您没有提交任何数据");
            if (HopeMigrateTime != null && HopeMigrateTime < DateTime.Now.AddMinutes(-5))
                return FalidResult($"操作中止，为保证迁移顺利，预约迁移时间最早应设为五分钟之后");
            //更新任务HopeMigrateTime
            var db = DbApiAccessUtility.GetConfigureDb();
            var pddDb = DbApiAccessUtility.GetPddConfigureDb();
            db.ExecuteNonQuery($"update P_DataMigrateTask set HopeMigrateTime='{HopeMigrateTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}' where Id={id.FirstOrDefault()}");
            db.ExecuteNonQuery($"update P_DataMigrateTask set HopeMigrateTime='{HopeMigrateTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}' where Id={id.FirstOrDefault()}");
            return SuccessResult("");
        }

        //public ActionResult GetMigrateTaskStatus(int id)
        //{
        //    if (id <= 0)
        //        return FalidResult("数据迁移任务ID不正确");
        //    var task = DbPolicyExtension.GetDataMigrateTask(id);
        //    if (task == null)
        //        return FalidResult("数据迁移任务不存在");
        //    return SuccessResult(task.MigrateStatus);
        //}

        private BranchShareRelationLog GetLog(BranchShareRelation model)
        {

            var log = new BranchShareRelationLog
            {
                ShareId = model.Id,
                OperatorId = SiteContext.Current.CurrentShopId,
                ShopId = model.ToId,
                CreateTime = DateTime.Now,
                Type = ShareLogType.Stop.ToString(),
                TotalQuantity = model.TotalQuantity,
                Balance = model.Balance,
                AppendCount = 0
            };

            return log;
        }

    }
}