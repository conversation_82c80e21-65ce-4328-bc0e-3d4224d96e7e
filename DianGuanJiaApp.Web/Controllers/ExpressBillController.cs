using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Net;
using DianGuanJiaApp.Utility.NPOI;
using NPOI.SS.UserModel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.Controllers
{
    public class ExpressBillController : BaseController
    {
        private WaybillCodeCheckService _service = new WaybillCodeCheckService();
        private WaybillCodeService _waybillCodeService = new WaybillCodeService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private ExportTaskService _exportTaskService = new ExportTaskService();

        //[App_Start.ActionPermissionControlFilter("快递对账")]

        public ActionResult Index()
        {
            var shopList = new List<Shop> { SiteContext.Current.CurrentLoginShop };
            var currShop = SiteContext.Current.CurrentLoginShop;
            var selectItemList = new List<SelectListItem>();
            if (SiteContext.Current.ChildShop != null && SiteContext.Current.ChildShop.Count > 0)
            {
                selectItemList.Add(new SelectListItem()
                {
                    Text = "==所有店铺==",
                    Value = 0.ToString(),
                });
                shopList.AddRange(SiteContext.Current.ChildShop);
            }

            shopList.ForEach(item =>
            {
                if (currShop.PlatformType == item.PlatformType)
                    selectItemList.Add(new SelectListItem() { Text = item.NickName, Value = item.Id.ToString() });

            });
            //阿里多版本识别与拦截
            var tempResult = CheckAlibabaShopVersionControl(SiteContext.Current.AllSamePlatformTypeShops.Select(x=>x.Id).ToList(), "ExpressBill", "Index");
            if (tempResult != null)
                ViewBag.VersionTip = JsonConvert.SerializeObject(tempResult.Data);
            var currShopId = SiteContext.Current.CurrentShopId;
            var keys = new List<string> { "/ExpressBill/WaybillCode/UpdateTime" };
            var commSets = _commonSettingService.GetSets(keys, currShopId);

            //最后导出时间
            var exportUpdateTimeSet = commSets.FirstOrDefault(m => m.Key == "/ExpressBill/WaybillCode/UpdateTime");
            var defaultExportUpdateTime = exportUpdateTimeSet?.Value.ToDateTime() ?? null;
            var defaultExportUpdateTimeSetVal = defaultExportUpdateTime == null ? "" : defaultExportUpdateTime.Value.ToString("yyyy-MM-ddTHH:mm:ss");
            // 默认导出间隔时长（300s）
            var defaultExportExpireSecondsSet = _commonSettingService.Get("/ExpressBill/WaybillCode/ExpireSeconds", 0);
            var defaultExportExpireSeconds = defaultExportExpireSecondsSet?.Value.ToInt() ?? 0;
            defaultExportExpireSeconds = defaultExportExpireSeconds <= 0 ? 300 : defaultExportExpireSeconds;
            ViewBag.ExportUpdateTimeSet = defaultExportUpdateTimeSetVal;
            ViewBag.ExportExpireSeconds = defaultExportExpireSeconds;

            //前台获取导出任务
            var exportTask = _exportTaskService.GetExportTask(currShopId, ExportType.ExpressBill.ToInt());
            // 显示未完成任务或已完成一天前的导出任务
            exportTask = exportTask != null && ((exportTask.Status >= 0 && exportTask.Status < 4) || (exportTask.Status >= 4 && exportTask.UploadToServerTime != null && DateTime.Now < exportTask.UploadToServerTime.Value.AddDays(1))) ? exportTask : null;
            var task = GetExportTaskToWeb(exportTask);
            ViewBag.ExpressBillTask = task?.ToJson() ?? "null";
            return View(selectItemList);
        }

        //[App_Start.ActionPermissionControlFilter("快递对账")]
        public ActionResult SaveFileOld(string fileDirectory)
        {
            if (string.IsNullOrWhiteSpace(fileDirectory))
            {
                //return FalidResult("没有指定文件保存路劲");
                fileDirectory = "Files/Temp";
            }

            //阿里多版本识别与拦截
            var tempResult = CheckAlibabaShopVersionControl(SiteContext.Current.AllSamePlatformTypeShops.Select(x => x.Id).ToList(), "ExpressBill", "SaveFile");
            if (tempResult != null)
                return Json(tempResult);

            var path = $"/{fileDirectory.TrimStart('/').TrimEnd('/')}/";
            var directory = Server.MapPath("~" + path);
            if (System.IO.Directory.Exists(directory) == false)
            {
                System.IO.Directory.CreateDirectory(directory);
            }

            if (Request.Files.Count > 0)
            {
                var file = Request.Files[0];
                if (file.FileName.EndsWith(".xls") == false && file.FileName.EndsWith(".xlsx") == false)
                {
                    return FalidResult("请上传Excel文件");
                }

                var fileName = (DateTime.Now.ToString("yyyyMMddHHmmssfff") + file.FileName);

                file.SaveAs(directory + fileName);
                //return SuccessResult("文件上传成功");                
                //return SuccessResult(path + fileName);

                var filePath = directory + fileName;
                if (filePath.IsNullOrEmpty() || !System.IO.File.Exists(filePath))
                {
                    //return FalidResult("文件未找到!");
                    return Content((new AjaxResult()
                    {
                        Success = false,
                        Message = "文件未找到"
                    }).ToJson());
                }

                //判断文件md5是否存在,存在，直接返回批次号
                var batchNumnber = CommUtls.GetMD5HashFromFile(filePath);
                var tempNumber = _service.ExistsBatchNumnber(batchNumnber);
                if (tempNumber > 0)
                {
                    //return SuccessResult(batchNumnber);
                    return Content((new AjaxResult()
                    {
                        Success = true,
                        Data = batchNumnber
                    }).ToJson());
                }

                if (string.IsNullOrWhiteSpace(batchNumnber))
                    batchNumnber = SiteContext.Current.CurrentShopId.ToString() + DateTime.Now.ToString("yyyyMMddHHmmssfff");

                //1.读取文件
                string errMsg = string.Empty;
                var dt = ExcelHelper.GetExcelDataTable(out errMsg, filePath);
                if (!errMsg.IsNullOrEmpty())
                {
                    //return FalidResult("文件有误：" + errMsg);

                    return Content((new AjaxResult()
                    {
                        Success = false,
                        Message = "文件有误：" + errMsg
                    }).ToJson());
                }

                var columns = dt.Columns;
                if (columns.Contains("运单号") == false)
                {
                    //return FalidResult("文件有误：未找到数据列【运单号】");
                    return Content((new AjaxResult()
                    {
                        Success = false,
                        Message = "文件有误：未找到数据列【运单号】"
                    }).ToJson());
                }

                //2.删除文件
                System.IO.File.Delete(filePath);

                if (dt?.Rows?.Count > 5000)
                {
                    //return FalidResult("文件超过5000条数据，请拆分成小于5000行数据的文件。");
                    return Content((new AjaxResult()
                    {
                        Success = false,
                        Message = "文件超过5000条数据，请拆分成小于5000行数据的文件。"
                    }).ToJson());
                }

                //3.文件数据入库
                var datalist = new List<WaybillCodeCheck>();

                foreach (DataRow row in dt.Rows)
                {
                    datalist.Add(new WaybillCodeCheck()
                    {
                        WaybillCode = row["运单号"]?.ToString()?.Trim(),
                        BatchNumber = batchNumnber,
                        ToProvince = columns.Contains("发往省份") ? row["发往省份"].ToString() : columns.Contains("省份") ? row["省份"].ToString() : ""
                    });
                }
                //4.批量插入数据
                var resultCount = _service.BulkInsert(datalist);

                //return SuccessResult(batchNumnber);
                return Content((new AjaxResult()
                {
                    Success = true,
                    Data = batchNumnber
                }).ToJson());
            }
            else
            {
                //return FalidResult("未读取到文件");
                return Content((new AjaxResult()
                {
                    Success = false,
                    Message = "未读取到文件"
                }).ToJson());
            }
        }

        [LogForOperatorFilter("快递对账")]
        public ActionResult SaveFileNew(string fileDirectory)
        {
            if (string.IsNullOrWhiteSpace(fileDirectory))
            {
                //return FalidResult("没有指定文件保存路劲");
                fileDirectory = "Files/Temp";
            }
            //阿里多版本识别与拦截
            var tempResult = CheckAlibabaShopVersionControl(SiteContext.Current.AllSamePlatformTypeShops.Select(x => x.Id).ToList(), "ExpressBill", "SaveFile");
            if (tempResult != null)
                return Json(tempResult);
            var path = $"/{fileDirectory.TrimStart('/').TrimEnd('/')}/";
            var directory = Server.MapPath("~" + path);
            if (System.IO.Directory.Exists(directory) == false)
            {
                System.IO.Directory.CreateDirectory(directory);
            }

            if (Request.Files.Count > 0)
            {
                var file = Request.Files[0];
                if (!file.FileName.EndsWith(".xls") && !file.FileName.EndsWith(".xlsx"))
                {
                    return FalidResult("只能上传Excel文件");
                }

                var fileName = (DateTime.Now.ToString("yyyyMMddHHmmssfff") + file.FileName);

                file.SaveAs(directory + fileName);
                //return SuccessResult("文件上传成功");                
                //return SuccessResult(path + fileName);

                var filePath = directory + fileName;
                if (filePath.IsNullOrEmpty() || !System.IO.File.Exists(filePath))
                {
                    return FalidResult("文件未找到");
                }


                var serverFilePath = CustomerConfig.UploadFileToFileServer(filePath);
                if (serverFilePath.IsNullOrEmpty())
                    return FalidResult("文件上传至服务器失败");

                var shopIds = SiteContext.Current.AllShops.Select(x => x.Id).ToList();
                var curShop = SiteContext.Current.CurrentLoginShop;
                //使用任务方式导出
                var newTask = new ExportTask
                {
                    IP = Request.UserHostAddress,
                    CreateTime = DateTime.Now,
                    PlatformType = curShop.PlatformType,
                    ShopId = curShop.Id,
                    UserId = SiteContext.Current.CurrentLoginSubUser?.Id.ToString(),
                    Status = 0,
                    Type = ExportType.ExpressBill.ToInt(),
                    PageIndex = 1,
                    PageSize = 1000,
                    TotalCount = 10000, //预设总数，任务开始时再获取实际数量
                    ParamJson = serverFilePath, //将文件暂存到服务器
                    ExtField1 = shopIds.ToJson(),
                    ExtField3 = file.FileName,
                    FromModule = "快递对账",
                };
                newTask.Id = _exportTaskService.Add(newTask);

                // 删除Web站点临时文件
                System.IO.File.Delete(filePath);

                return SuccessResult("导出任务创建成功", GetExportTaskToWeb(newTask));
            }
            else
            {
                return FalidResult("未读取到文件");
            }
        }

        //public ActionResult AnalysisFile(string batchNumber, string startDate, string endDate, List<int> shopIds, int expressId)
        public ActionResult AnalysisFile(string batchNumber)
        {
            if (string.IsNullOrWhiteSpace(batchNumber))
                return FalidResult("文件批次号为空");

            //1.根据文件批次号获取文件数据
            var datalist = _service.GetFileDataByBatchNum(batchNumber);
            if (datalist == null || datalist.Count == 0)
                return FalidResult($"根据批次号【{batchNumber}】未找到文件数据");

            ////2.查询数据
            //if (shopIds != null && shopIds.Count == 1 && shopIds.First() == 0)
            //{
            //    shopIds = SiteContext.Current.ShopIds;
            //}
            var shopIds = SiteContext.Current.AllShops.Select(m => m.Id).ToList();
            var waybillCodes = datalist.Select(m => m.WaybillCode).ToList();
            //var waybillCodeList = _waybillCodeService.LoadList(startDate, endDate, shopIds, expressId);
            var waybillCodeList = _waybillCodeService.LoadList(waybillCodes, shopIds, defaultMonth: 2);
            if (waybillCodeList == null) waybillCodeList = new List<ExpressWaybillCodeModel>();

            //使用的单号 字典
            var useCodeDict = new Dictionary<string, ExpressWaybillCodeModel>();//waybillCodeList.Select(f => f.ExpressWayBillCode?.Trim()).ToList();
            waybillCodeList.ForEach(item =>
            {
                if (useCodeDict.ContainsKey(item.ExpressWayBillCode) == false)
                    useCodeDict.Add(item.ExpressWayBillCode, item);
            });

            //所有使用的单号数据
            var useList = waybillCodeList.Select(f =>
            {
                f.TemplateName = SiteContext.Current.AllShops.Where(s => s.Id == f.ShopId).FirstOrDefault()?.NickName; //存储店铺名称
                f.Sender = "use_list"; //存储当前数据集合的类型，前端循环需要用到
                return f;
            });

            var foundList = new List<ExpressWaybillCodeModel>();//找到的数据
            var notFoundList = new List<ExpressWaybillCodeModel>();//未找到的数据
            datalist.ForEach(item =>
            {
                if (useCodeDict.ContainsKey(item.WaybillCode))
                {
                    var waybillCodeModel = useCodeDict[item.WaybillCode];
                    var instance = new ExpressWaybillCodeModel()
                    {
                        ExpressWayBillCode = item.WaybillCode,
                        ToProvince = item.ToProvince,
                        Sender = "found_list",//存储当前数据集合的类型，前端循环需要用到
                        TemplateName = SiteContext.Current.AllShops.Where(s => s.Id == waybillCodeModel.ShopId).FirstOrDefault()?.NickName, //存储店铺名称
                    };
                    foundList.Add(instance);
                }
                else
                {
                    var instance = new ExpressWaybillCodeModel()
                    {
                        ExpressWayBillCode = item.WaybillCode,
                        ToProvince = item.ToProvince,
                        Sender = "not_found_list",//存储当前数据集合的类型，前端循环需要用到
                    };
                    notFoundList.Add(instance);
                }
            });


            ////找到的数据
            //var foundList = datalist.Where(f => useCodeList.Contains(f.WaybillCode)).Select(f =>
            //{
            //    var waybillCodeModel = waybillCodeList.FirstOrDefault(w => w.ExpressWayBillCode == f.WaybillCode);
            //    var instance = new WaybillCode()
            //    {
            //        ExpressWayBillCode = f.WaybillCode,
            //        ToProvince = f.ToProvince,
            //        Sender = "found_list",//存储当前数据集合的类型，前端循环需要用到
            //        TemplateName = SiteContext.Current.AllShops.Where(s => s.Id == waybillCodeModel.ShopId).FirstOrDefault()?.ShopName, //存储店铺名称
            //    };
            //    return instance;
            //});

            ////未找到的数据
            //var notFoundList = datalist.Where(f => useCodeList.Contains(f.WaybillCode) == false).Select(f =>
            //{
            //    var instance = new WaybillCode()
            //    {
            //        ExpressWayBillCode = f.WaybillCode,
            //        ToProvince = f.ToProvince,
            //        Sender = "not_found_list",//存储当前数据集合的类型，前端循环需要用到
            //    };
            //    return instance;
            //});

            ////已回收数据 //状态：1：已打印，2：单号已回收，3：已发货
            //var recycleList = waybillCodeList.Where(f => f.Status == 2).Select(f =>
            //{
            //    f.TemplateName = SiteContext.Current.AllShops.Where(s => s.Id == f.ShopId).FirstOrDefault()?.ShopName; //存储店铺名称
            //    f.Sender = "recycle_list"; //存储当前数据集合的类型，前端循环需要用到
            //    return f;
            //});

            //已回收数据 //状态：1：已打印，2：单号已回收，3：已发货
            var recycleList = new List<ExpressWaybillCodeModel>();
            foundList.ForEach(item =>
            {
                if (item.Status == 2)
                {
                    recycleList.Add(item);
                }
            });

            //重复数据
            var repeatCodeDict = new Dictionary<string, int>(); //重复数据字典,单号，数量
            var repeatList = new List<ExpressWaybillCodeModel>();
            var tempCodeList = datalist.GroupBy(f => f.WaybillCode).Where(f => f.Count() > 1);
            tempCodeList.ToList().ForEach(item =>
            {
                ExpressWaybillCodeModel instance = null;//waybillCodeList.Where(c => c.ExpressWayBillCode == f.Key).FirstOrDefault();
                if (useCodeDict.ContainsKey(item.Key))
                    instance = useCodeDict[item.Key];
                if (instance == null)
                {
                    var model = item.First();
                    instance = new ExpressWaybillCodeModel()
                    {
                        ID = 0, //id==0的为重复且未找到
                        PrintDataType = item.Count(), //存储重复次数
                        ExpressWayBillCode = model.WaybillCode,
                        ToProvince = model.ToProvince,
                        Sender = "repeat_list",//存储当前数据集合的类型，前端循环需要用到
                    };
                }
                else
                {
                    instance.TemplateName = SiteContext.Current.AllShops.Where(s => s.Id == instance.ShopId).FirstOrDefault()?.NickName; //存储店铺名称
                    instance.PrintDataType = item.Count(); //存储重复次数
                    instance.Sender = "repeat_list";//存储当前数据集合的类型，前端循环需要用到
                }
                repeatList.Add(instance);
                repeatCodeDict.Add(instance.ExpressWayBillCode, instance.PrintDataType);
            });

            //所有检查的数据
            var checkList = datalist.Select(f =>
            {
                //是否重复
                int repeatCount = 0;//repeatList.Where(r => r.ExpressWayBillCode == f.WaybillCode).FirstOrDefault();
                if (repeatCodeDict.ContainsKey(f.WaybillCode))
                    repeatCount = repeatCodeDict[f.WaybillCode];

                //能否找到
                ExpressWaybillCodeModel foundModel = null;//waybillCodeList.Where(c => c.ExpressWayBillCode == f.WaybillCode).FirstOrDefault();
                if (useCodeDict.ContainsKey(f.WaybillCode))
                    foundModel = useCodeDict[f.WaybillCode];

                if (foundModel == null)
                {
                    foundModel = new ExpressWaybillCodeModel()
                    {
                        ID = 0, //id==0的为重复且未找到
                        PrintDataType = repeatCount, //存储重复次数
                        ExpressWayBillCode = f.WaybillCode,
                        ToProvince = f.ToProvince,
                        Sender = "check_list",//存储当前数据集合的类型，前端循环需要用到
                    };
                }
                else
                {
                    foundModel.PrintDataType = repeatCount;//存储重复次数
                    foundModel.Sender = "check_list";//存储当前数据集合的类型，前端循环需要用到
                    foundModel.TemplateName = SiteContext.Current.AllShops.Where(s => s.Id == foundModel.ShopId).FirstOrDefault()?.NickName; //存储店铺名称
                }
                return foundModel;
            }).OrderByDescending(ob => ob.PrintDataType);

            //统计
            //var useCount = useCodeList.Count; //使用单号数
            //var checkCount = datalist.Count;      //对账单号数
            //var foundCount = foundList.Count(); //找到单号数
            //var notFoundCount = datalist.Count - foundCount; //未找到单号数
            //var recycleCount = recycleList.Count();//回收单号数
            //var repeatCount = repeatList.Count();//重复的单号数

            return SuccessResult(new
            {
                CurrentShowType = "check_list",
                UseList = useList,
                CheckList = checkList,
                FoundList = foundList,
                NotFoundList = notFoundList,
                RecycleList = recycleList,
                RepeatList = repeatList
            });
        }

        [LogForOperatorFilter("快递对账导出")]
        public ActionResult ExportExcel()
        {
            var log = LogForOperatorContext.Current.logInfo;
            try
            {
                var curShop = SiteContext.Current.CurrentLoginShop;
                string options = Request.Form["options"].ToString2();
                options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
                options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果

                var waybillCodeList = options.ToList<WaybillCode>();
                var fileName = ExcelHelper.GetFileName("快递对账.xlsx", Request);

                if (waybillCodeList == null || !waybillCodeList.Any()) 
                    return FalidResult("无对账数据，不需要导出");
                
                var workbook = BuildExcel(waybillCodeList, fileName);
                Response.Cookies.Add(new HttpCookie("downloadToken", Request.Form["downloadToken"].ToString2()));
                var rootPath = Server.MapPath("../Files") + $"\\快递对账-{curShop.Id}-{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx";
                using (var fs = new FileStream(rootPath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(fs);
                }
                var memoryStream = new MemoryStream();
                using (var fileStream = new FileStream(rootPath, FileMode.Open))
                {
                    fileStream.CopyTo(memoryStream);
                }
                memoryStream.Position = 0;
                if (System.IO.File.Exists(rootPath))
                    System.IO.File.Delete(rootPath);
                return File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                log.Exception = ex.ToString();
                throw ex;
            }
        }

        private WaybillCode EncryptReceiverInfo(WaybillCode order)
        {
            order.BuyerMemberName = order.BuyerMemberName.ToEncryptName();
            order.BuyerMemberId = order.BuyerMemberId.ToEncryptName();
            order.ReciverPhone = order.ReciverPhone.ToPddEncryptPhone();
            order.Reciver = order.Reciver.ToEncryptName();
            order.ToAddress = order.ToAddress.ToPddEncryptAddress();
            //order.ToDistrict = "****";
            return order;
        }

        private IWorkbook BuildExcel(List<WaybillCode> waybillCodeLst, string fileName)
        {
            var platformType = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (platformType == PlatformType.Pinduoduo.ToString())
            {
                try
                {
                    //var tempOrders = waybillCodeLst?.Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ToAddress }).ToList();
                    var tempOrders = new List<Order>();
                    foreach (var x in waybillCodeLst)
                    {
                        if (x == null) continue;
                        var order = new Order
                        {
                            PlatformOrderId = x.OrderId.ToString2(),
                            ShopId = x.ShopId.ToInt(),
                            ToName = x.Reciver.ToString2(),
                            ToMobile = x.ReciverPhone.ToString2(),
                            ToProvince = x.ToProvince.ToString2(),
                            ToCity = x.ToCity.ToString2(),
                            ToCounty = x.ToDistrict.ToString2(),
                            ToAddress = x.ToAddress.ToString2()
                        };
                        tempOrders.Add(order);
                    }

                    BranchShareRelationService.TryToDecryptPddOrders(tempOrders, true);
                    //按店铺分组
                    waybillCodeLst.ForEach(item =>
                    {
                        var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId.ToString2() && x.ShopId == item.ShopId.ToInt());
                        if (decryptedOrder != null)
                        {
                            item.Reciver = decryptedOrder.ToName;
                            item.ReciverPhone = decryptedOrder.ToMobile;
                            item.BuyerMemberName = item.Reciver;
                            item.BuyerMemberId = item.Reciver;
                            item.ToAddress = decryptedOrder.ToFullAddress;
                        }
                    });
                    waybillCodeLst.ForEach(o =>
                    {
                        EncryptReceiverInfo(o);
                    });
                }
                catch (Exception ex)
                {
                    Log.WriteError($"拼多多收件人信息解密失败：{ex}");
                    throw;
                }
            }

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("快递对账");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            var heads = new Dictionary<string, string>() {
                { "匹配情况" ,"Result"},
                { "快递" ,"ExpressName"},
                { "快递单号" ,"ExpressWayBillCode"},
                { "省份" ,"ToProvince"},
                { "重量","TotalWeight"},
                { "订单金额","TotalPayAomount"},
                { "商品数量","ProductCount"},
                { "店铺名称","TemplateName"},
                { "买家旺旺","BuyerMemberName"},
                { "收件人","Reciver"},
                { "状态","Status"},
            };
            heads.ToList().ForEach(h =>
            {
                var headName = h.Key;
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });


            int rowIndex = 1;
            waybillCodeLst.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;
                var dic = (model ?? new WaybillCode()).ToDictionary();

                colIndex = 0;
                foreach (var item in heads.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2() ?? "";
                    if (dic?.ContainsKey(key) == true)
                    {
                        var val = dic[key]?.ToString() ?? "";
                        if (key == "Status")
                        {
                            var text = val == "1" ? "已打印" :
                                       val == "2" ? "已回收" :
                                       val == "3" ? "已发货" : "未知";
                            dataRow.CreateCell(colIndex).SetCellValue(text);
                        }
                        else
                            dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                    }
                    else
                    {
                        if (key == "Result")
                        {
                            var val = model.ID == 0 ? "未找到" : "找到";
                            val = val + (model.PrintDataType > 0 ? $" - 重复({model.PrintDataType})" : "");
                            dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                        }
                    }
                    dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                    colIndex++;
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;
            });

            return workbook;
        }


        private void SetColumnWidth(ISheet sheet, string headName, int index)
        {
            int width = 20 * 256;
            //if (headName == "序号" || headName == "商品数量")
            //    width = 10 * 256;
            //else if (headName == "省份" || headName == "重量" || headName == "订单金额")
            //    width = 16 * 256;
            //else if (headName == "快递单号" || headName == "收件人姓名" || headName == "发件人" || headName == "发货时间" || headName == "打单时间" || headName == "状态" || headName == "收件人电话")
            //    width = 20 * 256;
            //else if (headName == "快递公司" || headName == "订单编号" || headName == "买家旺旺" || headName == "店铺名称")
            //    width = 25 * 256;
            //else if (headName == "详细地址" || headName == "买家留言" || headName == "卖家备注")
            //    width = 35 * 256;
            //else if (headName == "发货内容")
            //    width = 45 * 256;
            sheet.SetColumnWidth(index, width);
        }


        private ICellStyle GetHeadStyle(IWorkbook workbook)
        {
            IFont font = workbook.CreateFont();
            font.FontName = "Times New Roman";
            font.Boldweight = short.MaxValue;
            font.FontHeightInPoints = 11;

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(font);
            headerStyle.Alignment = HorizontalAlignment.Center;//内容居中显示
            headerStyle.WrapText = true;

            headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightOrange.Index;
            headerStyle.FillPattern = FillPattern.SolidForeground;
            return headerStyle;
        }

        private ICellStyle GetContentStyle(IWorkbook workbook, HorizontalAlignment alignment)
        {
            IFont font = workbook.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = workbook.CreateCellStyle();
            contentStyle.SetFont(font);
            contentStyle.Alignment = alignment;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.WrapText = true;

            return contentStyle;
        }
    }
}