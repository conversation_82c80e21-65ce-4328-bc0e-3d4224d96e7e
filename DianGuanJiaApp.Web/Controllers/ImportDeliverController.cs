using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.Controllers
{
    public class ImportDeliverController : BaseController
    {
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private ExpressImportMappingService _expressImportMappingService = new ExpressImportMappingService();
        private ExpressCompanyService _expressCompanyService = new ExpressCompanyService();

        private string mappingKey = "Import/SendOrder/FieldMapping";

        [App_Start.ActionPermissionControlFilter("导入发货")]
        public ActionResult Index()
        {
            LoadDefaultConfig();
            var shops = SiteContext.Current.AllSamePlatformTypeShops;
            ViewBag.CurShopId = SiteContext.Current.CurrentShopId;
            return View(shops);
        }

        public void LoadDefaultConfig()
        {
            var currShop = SiteContext.Current.CurrentLoginShop;
            var isUseOldTheme = false;// SiteContext.Current?.IsUseOldTheme ?? false;
            var platformType = isUseOldTheme ? "Old" : SiteContext.Current.CurrentLoginShop.PlatformType;

            var keys = new List<string> { mappingKey };
            var commSets = _commonSettingService.GetSets(keys, currShop.Id);

            var mappingSet = commSets?.FirstOrDefault(m => m.Key == mappingKey);

            var defaultmappingSetVal = mappingSet?.Value.ToString2() ?? "";
            if (defaultmappingSetVal.IsNullOrEmpty())
            {
                defaultmappingSetVal = GetDefaultSetting(platformType, mappingKey);
            }

            var expressMappings = _expressImportMappingService.GetExpressImportMappings(new List<string> { "dgjapp" });
            ViewBag.FieldMappingSet = defaultmappingSetVal;
            ViewBag.ExpressCompanys = expressMappings.ToJson();
        }

        public string GetDefaultSetting(string platformType, string key)
        {
            var keys = new List<string> { key };
            var commSets = _commonSettingService.GetSets(keys, 0);
            var commonSet = commSets.FirstOrDefault(m => m.Key == key);
            return commonSet?.Value.ToString2() ?? "";
        }

        /// <summary>
        /// 上传Excel文件，并解析返回
        /// </summary>
        /// <returns></returns>
        //[App_Start.ActionPermissionControlFilter("导入发货")]
        [LogForOperatorFilter("导入发货文件")]
        public ActionResult ExcelUpload()
        {
            if (Request.Files.Count == 0 || Request.Files["upfile"] == null)
                return FalidResult("没有选择文件上传");

            var log = LogForOperatorContext.Current.logInfo;
            var id = log._Id;
            try
            {
                DataTable dt = new DataTable();
                var postedFile = Request.Files["upfile"];//获取上传的文件     

                var size = postedFile.ContentLength * 1.0 / 1024 / 1024;
                if (size == 0)
                {
                    log.Exception = $"Excel导入文件中无数据";
                    return FalidResult("请勿导入空文件");
                }
                else if (size > 5)
                {
                    log.Exception = $"导入文件大小：{size}M，超过5M";
                    return FalidResult("导入文件请不要超过5M");
                }

                string fileName = postedFile.FileName;
                string errMsg = string.Empty;
                if (fileName.IndexOf(".xlsx") > 0) // 2007版本以上  
                    dt = ExcelHelper.GetExcelDataTable(out errMsg, stream: postedFile.InputStream, fileExt: ".xlsx", needRowIndex: true);
                else if (fileName.IndexOf(".xls") > 0) // 2003版本  
                    dt = ExcelHelper.GetExcelDataTable(out errMsg, stream: postedFile.InputStream, fileExt: ".xls", needRowIndex: true);

                if (!errMsg.IsNullOrEmpty())
                    return FalidResult(errMsg);

                if (dt == null || dt.Rows.Count == 0)
                {
                    log.Exception = $"Excel导入文件中无数据";
                    return FalidResult("请勿导入空文件");
                }

                var excelModel = DataTableToCustomOrder(dt);
                if (excelModel.ColumnNames.Count <= 0)
                {
                    log.Exception = $"Excel导入表头名称为空";
                    return FalidResult("Excel表头不能为空");
                }
                else if (excelModel.Rows.Count > 2001)
                {
                    log.Exception = $"Excel导入订单总条数：{excelModel.Rows.Count - 1}，超过2000条";
                    return FalidResult("Excel导入订单请不要超过2000条");
                }

                //检查表头是否错误
                var dic = new Dictionary<string, int>();
                var currShop = SiteContext.Current.CurrentLoginShop;
                var commSet = _commonSettingService.Get(mappingKey, currShop.Id);
                if (commSet == null)
                    commSet = _commonSettingService.Get(mappingKey, 0);

                var fieldMappingModel = new ImportDeliverFieldMappingModel();
                if (commSet != null && !commSet.Value.IsNullOrEmpty())
                {
                    fieldMappingModel = commSet.Value.ToObject<ImportDeliverFieldMappingModel>();
                }

                // 获取列头数据
                var colNames = new List<string> { "订单编号", "快递公司", "快递单号" };
                for (var j = 0; j < excelModel.ColumnNames.Count; j++)
                {
                    if (fieldMappingModel.PlatformOrderId.Contains(excelModel.ColumnNames[j]))
                    {
                        if (dic.ContainsKey("PlatformOrderId"))
                            return FalidResult($"订单编号别名：【{string.Join(",", fieldMappingModel.PlatformOrderId)}】,请检查表格中是否有重复的列出现。");
                        else
                            dic.Add("PlatformOrderId", j);
                        colNames.Remove("订单编号");
                    }
                    else if (fieldMappingModel.ExpressCompanyName.Contains(excelModel.ColumnNames[j]))
                    {
                        if (dic.ContainsKey("ExpressCompanyName"))
                            return FalidResult($"快递公司别名：【{string.Join(",", fieldMappingModel.ExpressCompanyName)}】,请检查表格中是否有重复的列出现。");
                        else
                            dic.Add("ExpressCompanyName", j);
                        colNames.Remove("快递公司");
                    }
                    else if (fieldMappingModel.ExpressNo.Contains(excelModel.ColumnNames[j]))
                    {
                        if (dic.ContainsKey("ExpressNo"))
                            return FalidResult($"快递单号别名：【{string.Join(",", fieldMappingModel.ExpressNo)}】,请检查表格中是否有重复的列出现。");
                        else
                            dic.Add("ExpressNo", j);
                        colNames.Remove("快递单号");
                    }
                }

                var json = string.Empty;
                ImportDeliverModel model = new ImportDeliverModel();
                if (colNames.Any())
                {
                    log.Exception = $"Excel导入列必须包含列-{string.Join("，", colNames)}";
                    //return FalidResult($"Excel导入列必须包含列-{string.Join("，", colNames)}");
                    var index = excelModel.ColumnNames.IndexOf("RowIndex");
                    if (index != -1)
                    {
                        excelModel.ColumnNames.Remove("RowIndex");
                        excelModel.Rows.ForEach(row =>
                        {
                            row.RemoveAt(index);
                        });
                    }

                    model.ExcelDatas = excelModel;
                    model.Responses = new List<ImportDeliverSendResponseModel>();
                    model.SuccessCount = 0;
                    model.TotalCount = excelModel.Rows.Count;
                    json = JsonExtension.ToJson(model);
                    return SuccessResult(json);
                }
                // 验证必填数据是否为空  
                var curShop = SiteContext.Current.CurrentLoginShop;
                var shopId = Request.Params["ShopId"].ToInt();
                var shops = shopId == 0 ? SiteContext.Current.AllSamePlatformTypeShops : SiteContext.Current.AllShops.Where(m => m.Id == shopId).ToList();
                var shopIds = shops.Select(m => m.Id).Distinct().ToList();

                //阿里多版本识别与拦截
                var tempResult = CheckAlibabaShopVersionControl(shopIds, "ImportDeliver", "ExcelUpload");
                if (tempResult != null)
                    return Json(tempResult);

                // 同步方式同步相同平台订单        
                var syncLog = new LogForOperator() { OperatorType = "增量同步订单" };
                LogForOperatorContext.Current.StartStep(syncLog);
                StartSyncOrder(syncLog);
                if (curShop.PlatformType == PlatformType.Pinduoduo.ToString())
                {
                    //拼多多，还需要去同步退款订单，回传取消发货结果，目的是协助平台做极速退款
                    (new SyncOrderService()).PddSyncRefundOrder(new DianGuanJiaApp.Data.Model.SyncOrderParametersModel { });
                }
                LogForOperatorContext.Current.EndStep();

                var requests = new List<ImportDeliverSendRequestModel>();
                for (var i = 0; i < excelModel.Rows.Count; i++)
                {
                    var request = new ImportDeliverSendRequestModel
                    {
                        Index = excelModel.Rows[i][excelModel.ColumnNames.Count - 1].ToInt(), // 跳过表头对应Excel行号
                        PlatformOrderId = excelModel.Rows[i][dic["PlatformOrderId"]].ToString2().Trim(),
                        ExpressCompany = excelModel.Rows[i][dic["ExpressCompanyName"]].ToString2().Trim(),
                        ExpressCode = excelModel.Rows[i][dic["ExpressNo"]].ToString2().Trim(),
                    };
                    requests.Add(request);
                }

                // 获取数据库的订单，验证订单状态是否正确  
                var pids = requests.Select(m => m.PlatformOrderId).Distinct().ToList();
                var orders = MyOrderService.GetOrders(pids, shopIds, false, new List<string> { "o.Id", "o.PlatformOrderId", "o.ShopId", "o.PlatformStatus", "o.PlatformType", "o.RefundStatus", "oi.Id", "oi.RefundStatus", "oi.IsSended" });

                // 导入订单进行校验         
                var expressMappings = _expressImportMappingService.GetExpressImportMappings(new List<string> { curShop.PlatformType, "dgjapp" });
                var responses = new List<ImportDeliverSendResponseModel>();
                requests.ForEach(req =>
                {
                    var response = new ImportDeliverSendResponseModel { Index = req.Index, PlatformOrderId = req.PlatformOrderId, ExpressCompany = req.ExpressCompany, ExpressCode = req.ExpressCode, IsSuccess = false };
                    var order = orders.FirstOrDefault(m => m.PlatformOrderId == req.PlatformOrderId);
                    if (order == null)
                    {
                        response.IsPlatformOrderIdEmpty = true;
                        response.ImportMessage = "未发现订单编号";
                        responses.Add(response);
                    }
                    else
                    {
                        if (order.PlatformStatus != "waitsellersend")
                        {
                            response.IsSuccess = false;
                            response.ImportMessage = "当前订单非待发货状态";
                        }
                        else if (order.PlatformStatus == "waitsellersend" && order.PlatformType == PlatformType.TouTiao.ToString() && order.TradeType == "1")
                        {
                            response.IsSuccess = false;
                            response.ImportMessage = "暂不支持货到付款订单发货";
                        }
                        else if (req.ExpressCompany.IsNullOrEmpty())
                        {
                            response.IsExpressCompanyEmpty = true;
                            response.ImportMessage = "未发现快递公司";
                        }
                        else if (req.ExpressCode.IsNullOrEmpty())
                        {
                            response.IsExpressCodeEmpty = true;
                            response.ImportMessage = "未发现快递单号";
                        }
                        else
                        {
                            var mapping = expressMappings.FirstOrDefault(m => m.CompanyName == req.ExpressCompany);
                            if (mapping == null)
                            {
                                response.IsExpressCompanyEmpty = true;
                                response.ImportMessage = "未发现快递公司";
                            }
                            else if (order.RefundStatus == "waitselleragree" || order.OrderItems.Any(oi => oi.RefundStatus == "WAIT_SELLER_AGREE"))
                            {   // 退款中订单                          
                                response.CompanyCode = mapping.CompanyCode;
                                response.IsRefundStatus = true;
                                response.IsSuccess = true;
                                response.ImportMessage = "导入成功";
                            }
                            else
                            {
                                response.CompanyCode = mapping.CompanyCode;
                                response.IsSuccess = true;
                                response.ImportMessage = "导入成功";
                            }
                        }
                        responses.Add(response);
                    }
                });
                responses = responses.OrderBy(m => m.IsSuccess).ThenBy(m => m.Index).ToList();
                model = new ImportDeliverModel()
                {
                    ExcelDatas = excelModel,
                    Responses = responses,
                    SuccessCount = responses.Count(m => m.IsSuccess),
                    TotalCount = responses.Count,
                };

                if (responses.Any(m => !m.IsSuccess))
                    log.Exception = $"Excel导入识别错误信息：{responses.Where(m => !m.IsSuccess).ToList().ToJson()}";

                json = JsonExtension.ToJson(model);
                return SuccessResult(json);
            }
            catch (Exception ex)
            {
                log.Exception = ex.Message;
                Log.WriteError(ex.Message);
                return FalidResult(ex.Message);
            }
        }

        [LogForOperatorFilter("批量导入发货")]
        public ActionResult OnlineSend()
        {
            var datas = Request.Params["Datas"].ToString2();
            var shopId = Request.Params["ShopId"].ToInt();
            var isSendPreCheck = true;
            if (Request.Params["IsSendPreCheck"] != null)
                isSendPreCheck = Request.Params["IsSendPreCheck"].ToBool();

            var shops = shopId == 0 ? SiteContext.Current.AllSamePlatformTypeShops : SiteContext.Current.AllShops.Where(m => m.Id == shopId).ToList();
            var shopIds = shops.Select(m => m.Id).Distinct().ToList();

            var log = LogForOperatorContext.Current.logInfo;
            var id = log._Id;
            var sendModels = datas.IsNullOrEmpty() ? null : datas.ToList<ImportDeliverSendResponseModel>();
            if (sendModels == null || !sendModels.Any())
            {
                log.Exception = "当前选中的订单都不是待发货状态，已取消发货";
                throw new LogicException("当前选中的订单都不是待发货状态，已取消发货");
            }

            var tmpSendPids = sendModels.Where(m => m.CompanyCode.IsNullOrEmpty()).Select(m => m.PlatformOrderId).ToList();
            if (tmpSendPids.Any())
            {
                log.Exception = $"当前选中的订单【{string.Join(",", tmpSendPids)}】，不满足发货条件";
                throw new LogicException($"当前选中的订单【{string.Join(",", tmpSendPids)}】，不满足发货条件");
            }

            List<DeliverySendOrderResultModel> orders = new List<DeliverySendOrderResultModel>();
            var totalSuccessCount = 0;
            var totalErrorCount = 0;

            var expressCompanys = _expressCompanyService.GetExpressCompay();
            List<OnlineSendRequestModel> models = GetSendRequestModel(sendModels, expressCompanys, shopIds, log);
            models.ForEach(model =>
            {
                model.IsSendPreCheck = isSendPreCheck; //拼多多是否发货预检查

                var sublog = new LogForOperator();
                var expressCompany = expressCompanys.FirstOrDefault(m => m.Id == model.ExpressCompanyId);
                if (expressCompany == null)
                {
                    sublog.OperatorType = "无快递公司 -> 导入发货";
                    sublog.Exception = $"未找到快递公司ID【{model.ExpressCompanyId}】";
                    throw new LogicException("未找到快递公司");
                }
                sublog = new LogForOperator() { OperatorType = expressCompany.CompanyName + " -> 导入发货" };
                LogForOperatorContext.Current.StartStep(sublog);
                sublog.Detail = model;

                if (model.Orders == null || !model.Orders.Any())
                {
                    sublog.Exception = "当前选中的订单都不是待发货状态，已取消发货";
                    throw new LogicException("当前选中的订单都不是待发货状态，已取消发货");
                }
                sublog.TotalCount = model.Orders.Count();
                var results = MyOrderService.OnlineSend(model);
                sublog.SuccessCount = results.Count(r => r.IsSuccess);
                sublog.Exception = sublog.SuccessCount < sublog.TotalCount ? "Error" : "";

                orders.AddRange(results);
                var successCount = results?.Where(r => r.IsSuccess).Count() ?? 0;
                var errorCount = results?.Where(r => !r.IsSuccess || !string.IsNullOrEmpty(r.ErrorMessage)).Count() ?? 0;
                totalSuccessCount += successCount;
                totalErrorCount += errorCount;

                var result = new DeliverySendResponseModel
                {
                    Orders = results,
                    SuccessCount = successCount,
                    ErrorCount = errorCount
                };
                sublog.Response = result;
                LogForOperatorContext.Current.EndStep();
            });

            var mergeResult = new DeliverySendResponseModel
            {
                Orders = orders,
                SuccessCount = totalSuccessCount,
                ErrorCount = totalErrorCount
            };
            LogForOperatorContext.Current.logInfo.Response = mergeResult;
            sendModels.ForEach(m =>
            {
                var order = orders.FirstOrDefault(o => o.OrderEntity.PlatformOrderId == m.PlatformOrderId);
                if (order == null)
                {
                    m.IsSuccess = false;
                    m.SendMessage = "发货失败，订单状态已改变";
                }
                else
                {
                    if (order.IsSuccess)
                    {
                        m.IsSuccess = true;
                        m.SendMessage = "发货成功";
                    }
                    else
                    {
                        m.IsSuccess = false;
                        m.SendMessage = order.ErrorMessage;
                        m.HasSendPreCheckError = order.ErrorCode == "SendPreCheckError"; //拼多多：是否是发货预检查接口报错
                    }
                }
            });
            sendModels = sendModels.OrderByDescending(m => m.IsSuccess).ThenBy(m => m.Index).ToList();
            return Json(sendModels);
        }

        /// <summary>
        /// 获取发货请求模型
        /// </summary>
        /// <param name="datas"></param>
        /// <param name="expressCompanys"></param>
        /// <param name="shopIds"></param>
        /// <param name="log"></param>
        /// <returns></returns>
        public List<OnlineSendRequestModel> GetSendRequestModel(List<ImportDeliverSendResponseModel> datas, List<ExpressCompany> expressCompanys, List<int> shopIds, LogForOperator log)
        {
            var orderFields = new List<string> { "o.Id", "o.PlatformOrderId", "o.ShopId", "o.PlatformStatus", "o.PlatformType", "o.RefundStatus", "o.toProvince", "o.ToCity", "o.ToCounty", "o.ToPost", "o.ToPhone", "o.ToMobile", "o.ToName", "o.ToFullAddress", "o.BuyerMemberId", "o.BuyerWangWang", "o.BuyerMemberName", "o.SenderName", "o.SenderAddress", "o.SenderPhone", "o.SenderCompany" };
            var orderItemFields = new List<string> { "oi.Id ", "oi.ShopId", "oi.PlatformOrderId", "oi.SubItemID", "oi.Status", "oi.RefundStatus", "oi.IsSended" };
            var fields = new List<string>();
            fields.AddRange(orderFields);
            fields.AddRange(orderItemFields);

            var pids = datas.Select(m => m.PlatformOrderId).Distinct().ToList();
            var orders = MyOrderService.GetOrders(pids, shopIds, false, fields);
            orders = MyOrderService.SetSellerInfo(orders);
            if (orders == null || !orders.Any())
            {
                log.Exception = $"未查到订单信息，查询条件：Pids：{pids.ToJson()}，ShopIds：{shopIds.ToJson()}";
                throw new LogicException("未查询到您所请求的订单数据，请检查订单是否存在");
            }

            var onlineSendRequests = new List<OnlineSendRequestModel>();
            // 不同快递公司进行分组组装批量发货对象
            var group = datas.GroupBy(m => m.CompanyCode).ToList();
            group.ForEach(g =>
            {
                // 订单根据店铺分组获取发货请求对象
                var tmpPids = g.Select(m => m.PlatformOrderId).Distinct().ToList();
                var tmpOrders = orders.Where(o => tmpPids.Contains(o.PlatformOrderId)).ToList();
                var tmpGroups = tmpOrders.GroupBy(o => o.ShopId).ToList();
                List<OrderRequestModel> sendOrders = new List<OrderRequestModel>();
                tmpGroups.ForEach(tg =>
                {
                    OnlineSendRequestModel model = new OnlineSendRequestModel();
                    tg.ToList().ForEach(o =>
                    {
                        OrderRequestModel order = new OrderRequestModel();
                        order.Id = o.Id;
                        order.PlatformOrderId = o.PlatformOrderId;
                        order.ShopId = tg.Key;
                        order.OrderItems = o.OrderItems.Where(m => !(m.IsSended ?? false) || m.RefundStatus != "REFUND_SUCCESS").Select(m => m.Id).ToList();
                        order.WaybillCode = g.FirstOrDefault(m => m.PlatformOrderId == o.PlatformOrderId)?.ExpressCode ?? "";

                        order.Buyer = new OrderRequestBuyerModel
                        {
                            BuyerMemberId = o.BuyerMemberId,
                            BuyerMemberName = o.BuyerMemberName,
                            BuyerWangWang = o.BuyerWangWang
                        };
                        order.Receiver = new OrderReceiverRequestModel
                        {
                            toProvince = o.ToProvince,
                            toCity = o.ToCity,
                            toCounty = o.ToCounty,
                            buyerMemberId = o.BuyerMemberId,
                            toPost = o.ToPost,
                            toPhone = o.ToPhone,
                            toMobile = o.ToMobile,
                            toFullName = o.ToName,
                            BuyerWangWang = o.BuyerWangWang,
                            toArea = o.ToFullAddress
                        };
                        order.Sender = new OrderSenderRequestModel
                        {
                            SenderName = o.SenderName,
                            SenderAddress = o.SenderAddress,
                            SenderPhone = o.SenderPhone,
                            CompanyName = o.SenderCompany
                        };
                        sendOrders.Add(order);
                    });
                    model.Orders = sendOrders;
                    model.ExpressCompanyId = expressCompanys.FirstOrDefault(m => m.CompanyCode == g.Key)?.Id ?? 0;
                    onlineSendRequests.Add(model);
                });
            });
            return onlineSendRequests;
        }

        private ExcelDatasModel DataTableToCustomOrder(DataTable dt)
        {
            ExcelDatasModel model = new ExcelDatasModel();
            model.ColumnNames = ExcelHelper.GetColumnsByDataTable(dt).ToList();

            var rows = dt.Rows;
            for (var i = 0; i < rows.Count; i++)
            {
                List<string> row = new List<string>();
                foreach (var col in model.ColumnNames)
                {
                    row.Add(rows[i][col].ToString2());
                }
                model.Rows.Add(row);
            }
            return model;
        }

        private void StartSyncOrder(LogForOperator log)
        {
            try
            {
                if (SiteContext.Current.CurrentLoginShop.PlatformType != PlatformType.Offline.ToString())
                {
                    var _syncOrderService = new SyncOrderService();
                    _syncOrderService.SyncOrder(new DianGuanJiaApp.Data.Model.SyncOrderParametersModel { });
                }
            }
            catch (Exception ex)
            {
                if (log != null)
                {
                    log.IsError = true;
                    log.Exception = ex.ToString();
                }
                Log.WriteError($"同步订单时发生错误：{ex}");
            }
        }

        //private void SyncByPlatformOrderIds(List<ImportDeliverSendRequestModel> requests,Shop shop)
        //{
        //    var progressKey = "/ImportDeliver/SyncOrder/Progress";
        //    var _syncOrderService = new SyncOrderService();
        //    //var commSet = _commonSettingService.Get(progressKey, shop.Id);
        //    var progress = 0;
        //    var count = requests.Count;
        //    var syncedCount = 0;
        //    Parallel.ForEach(requests, new ParallelOptions { MaxDegreeOfParallelism = 10 }, (req) => {

        //        try
        //        {
        //            _syncOrderService.SyncSingleOrder(req.PlatformOrderId, shop);
        //        }
        //        catch (Exception ex)
        //        {
        //            Log.WriteError($"导入发货同步订单【{req.PlatformOrderId}】失败：{ex.Message}");
        //        }

        //        syncedCount++;    
        //        progress = (syncedCount * 1f / count * 100).ToInt();
        //        progress = progress >= 100 ? 100 : progress;
        //        _commonSettingService.Set(progressKey, progress.ToString(), shop.Id);

        //        Thread.Sleep(2000);
        //    });
        //}

        //public ActionResult GetImportDeliverProgress()
        //{
        //    var shopId = Request.Params["ShopId"].ToInt();
        //    var progressKey = "/ImportDeliver/SyncOrder/Progress";
        //    var _syncOrderService = new SyncOrderService();
        //    var commSet = _commonSettingService.Get(progressKey, shopId);  
        //    var progress = commSet?.Value.ToInt() ?? 0;
        //    return Json(progress);
        //}

        public ActionResult SaveMappingConfig(string mappingSet)
        {
            if (mappingSet.IsNullOrEmpty())
                return FalidResult("配置信息为空");
            _commonSettingService.Set(mappingKey, mappingSet, SiteContext.Current.CurrentShopId);
            return SuccessResult();
        }
    }

}