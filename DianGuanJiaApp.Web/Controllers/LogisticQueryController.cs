using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.Controllers
{
    public class LogisticQueryController : BaseController
    {
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private OrderLogisticInfoService _orderLogisticInfoService = new OrderLogisticInfoService();
        private ExportTaskService _taskService = new ExportTaskService();


        public ActionResult Index()
        {
            //截止xxx前的订单监控信息不显示
            ViewBag.LogisticUpdateStartTime = _commonSettingService.GetLogisticUpdateStartTime();
            //ViewBag.UpdateIntervalTime = _commonSettingService.GetLogisticUpdateIntervalSeconds();
            ViewBag.FromOrderPrint = Request["FromOrderPrint"].ToInt();
            var shopList = new List<Shop>();
            var currShop = SiteContext.Current.CurrentLoginShop;
            var selectItemList = new List<SelectListItem>();
            if (SiteContext.Current.AllShops != null && SiteContext.Current.AllShops.Count > 0)
            {
                selectItemList.Add(new SelectListItem()
                {
                    Text = "==所有店铺==",
                    Value = 0.ToString(),
                });
                shopList.AddRange(SiteContext.Current.AllShops?.Where(m => m.PlatformType == currShop.PlatformType && (m.IsServiceEnd == false || m.Id == currShop.Id)).ToList());
            }

            shopList.ForEach(item =>
            {
                if (currShop.PlatformType == item.PlatformType)
                    selectItemList.Add(new SelectListItem() { Text = item.NickName, Value = item.Id.ToString() });

            });

            //多版本识别与拦截
            var tempResult = CheckAlibabaShopVersionControl(shopList.Where(f => f.IsServiceEnd == false).Select(x => x.Id).ToList(), "LogisticQuery", "Index");
            if (tempResult != null)
                ViewBag.VersionTip = JsonConvert.SerializeObject(tempResult.Data);

            var keys = new List<string> { "/LogisticQuery/LogisticUpdateStartTime", "/Logistic/SwitchStatusUpdateTime" };
            var commSets = _commonSettingService.GetSets(keys, currShop.Id);
            //最后修改物流轨迹状态时间
            var lastSwitchStatusUpdateTimeSet = commSets.FirstOrDefault(m => m.Key == "/Logistic/SwitchStatusUpdateTime");
            var defaultLastSwitchStatusUpdateTimeVal = lastSwitchStatusUpdateTimeSet?.Value ?? "";
            ViewBag.LastSwitchStatusUpdateTimeSet = defaultLastSwitchStatusUpdateTimeVal;

            //最后物流轨迹更新时间
            var lastUpdateTimeSet = commSets.FirstOrDefault(m => m.Key == "/LogisticQuery/LogisticUpdateStartTime");
            var defaultLastUpdateTimeSetVal = lastUpdateTimeSet?.Value ?? "";
            ViewBag.LastUpdateTimeSet = defaultLastUpdateTimeSetVal;

            // 默认导出间隔时长（300s）
            ViewBag.UpdateIntervalSeconds = _commonSettingService.GetLogisticUpdateIntervalSeconds();
            //前台获取导出任务
            var exportTask = _taskService.GetExportTask(currShop.Id, ExportType.OrderLogisticInfo.ToInt());
            // 显示未完成任务或已完成一天前的导出任务
            exportTask = exportTask != null && exportTask.Status >= 0 && exportTask.Status <= 4 ? exportTask : null;
            var task = GetExportTaskToWeb(exportTask);
            ViewBag.OrderLogisticInfoUpdateTask = task?.ToJson() ?? "null";
            return View(selectItemList);
        }

        /// <summary>
        /// 加载列表数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        [App_Start.ActionPermissionControlFilter("物流预警")]
        public ActionResult LoadList(LogisticQueryListRequestModel requestModel)
        {
            var curShop = SiteContext.Current.CurrentLoginShop;
            //var json = requestModel.ToJson();
            if (requestModel.ShopId != null && requestModel.ShopId.Count > 0)
            {
                var shopIds = requestModel.ShopId.Where(f => f != 0).ToList();
                if (shopIds.Count == 0)
                    shopIds = SiteContext.Current.AllShops?.Where(m => m.PlatformType == curShop.PlatformType && (m.IsServiceEnd == false || m.Id == curShop.Id)).Select(f => f.Id).ToList();
                requestModel.ShopId = shopIds;
            }
            else
            {
                requestModel.ShopId = SiteContext.Current.AllShops?.Where(m => m.PlatformType == curShop.PlatformType && (m.IsServiceEnd == false || m.Id == curShop.Id)).Select(f => f.Id).ToList();
            }

            //阿里多版本识别与拦截
            var tempResult = CheckAlibabaShopVersionControl(requestModel.ShopId, "LogisticQuery", "LoadList");
            if (tempResult != null)
                return Json(tempResult);


            //物流轨迹启用状态
            var sets = _commonSettingService.GetSets(new List<string> { "OrderLogisticTracesSet" }, curShop.Id);
            var enableSet = sets.FirstOrDefault(x => x.Key == "OrderLogisticTracesSet")?.Value.ToObject<WarningSetModel>(); ;
            if (enableSet == null || enableSet.SubjectSet == 0)
                return FalidResult("请先启动物流预警后再查询");

            var pageModel = _orderLogisticInfoService.LoadList(requestModel);
            return Json(pageModel);
        }

        /// <summary>
        /// 订单打印页面显示预警件数量
        /// </summary>
        /// <returns></returns>
        public ActionResult GetWarningCount()
        {
            var count = _orderLogisticInfoService.GetWarningCount();
            return SuccessResult(count.ToString());
        }

        /// <summary>
        /// 物流预警未揽件和揽件未更新物流预警
        /// </summary>
        /// <returns></returns>
        public ActionResult GetStatisticWarningCount(LogisticQueryListRequestModel queryModel)
        {
            var tuple = _orderLogisticInfoService.GetStatisticWarningCount(queryModel);
            var result = new { UnPickCount = tuple.Item1, UnDdeliverCount = tuple.Item2 };
            return SuccessResult(result);
        }


        public ActionResult ExportExcel()
        {
            string options = Request.Form["options"].ToString2();
            options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
            options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果
            var requestModel = JsonExtension.ToObject<LogisticQueryListRequestModel>(options) ?? new LogisticQueryListRequestModel();
            var list = GetOrderLogisticList(requestModel);

            var fileName = "物流轨迹.xls";
            fileName = ExcelHelper.GetFileName(fileName, Request);
            var workbook = BuildExcel(list, fileName);
            Response.Cookies.Add(new HttpCookie("downloadToken", Request.Form["downloadToken"].ToString2()));
            var rootPath = Server.MapPath("../Files") + fileName;
            using (var fs = new FileStream(rootPath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(fs);
            }
            var memoryStream = new MemoryStream();
            using (var fileStream = new FileStream(rootPath, FileMode.Open))
            {
                fileStream.CopyTo(memoryStream);
            }
            memoryStream.Position = 0;
            if (System.IO.File.Exists(rootPath))
                System.IO.File.Delete(rootPath);
            return File(memoryStream, "application/ms-excel", fileName);
            //return File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);

        }

        /// <summary>
        /// 批量忽略异常
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public ActionResult IgnoreException(List<OrderLogisticInfo> models)
        {
            _orderLogisticInfoService.IgnoreException(models);
            return SuccessResult();
        }

        public ActionResult UpdateLogisticTraces(LogisticQueryListRequestModel requestModel)
        {
            var curShop = SiteContext.Current.CurrentLoginShop;
            //ExportTaskService _taskService = new ExportTaskService();
            //var execSql = _orderLogisticInfoService.GetExecSql(requestModel);   
            //物流轨迹启用状态          
            var sets = _commonSettingService.GetSets(new List<string> { "OrderLogisticTracesSet" }, curShop.Id);
            var enableSet = sets.FirstOrDefault(x => x.Key == "OrderLogisticTracesSet")?.Value.ToObject<WarningSetModel>(); ;
            if (enableSet == null || enableSet.SubjectSet == 0)
                return FalidResult("请先启动物流预警后再更新物流轨迹");

            requestModel.ShopId = requestModel.ShopId == null || !requestModel.ShopId.Any() ? SiteContext.Current.SamePlatformShopIds : requestModel.ShopId;

            var set = _commonSettingService.Get("UpdateLogisticDefaultPageSize", 0);
            var pageSize = set != null && set.Value.ToInt() > 0 ? set.Value.ToInt() : 100;
            var task = new ExportTask
            {
                IP = Request.UserHostAddress,
                CreateTime = DateTime.Now,
                Status = 0,
                //ExecSql = execSql,
                ParamJson = requestModel.ToJson(),
                Type = ExportType.OrderLogisticInfo.ToInt(),
                PageSize = pageSize,
                PageIndex = 1,
                PlatformType = curShop.PlatformType,
                ShopId = curShop.Id,
                UserId = SiteContext.Current.CurrentLoginSubUser?.Id.ToString(),
                FromModule = "物流轨迹更新",
            };
            var newId = _taskService.Add(task);
            task.Id = newId;
            return Json(GetExportTaskToWeb(task));
        }

        public ActionResult UpdateLogisticTraces_NoTask(LogisticQueryListRequestModel requestModel)
        {
            var list = GetOrderLogisticList(requestModel);
            if (list != null && !list.Any())
                return FalidResult("无物流订单信息");

            var request = list.Select(f => new LogisticCodeQueryRequest
            {
                ShipperCode = f.LogisticCode,
                LogisticCode = f.LogisticOrderId,
            });
            var result = _orderLogisticInfoService.QueryByLogisticCenter(request.ToList());
            return Json(result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public ActionResult LoadUpdateTask(int type)
        {
            var task = _taskService.GetUpdateTaskByShopId(SiteContext.Current.CurrentShopId, type);
            if (task != null)
                return Json(task);
            else
                return FalidResult("-1");
        }

        public ActionResult GetLastUpdateLogisticTask(int type)
        {
            var task = _taskService.GetLastUpdateLogisticTask(SiteContext.Current.SamePlatformShopIds, type);
            if (task != null)
                return Json(task);
            else
                return FalidResult("-1");
        }

        public ActionResult CheckUpdateTaskStatus(int id)
        {
            if (id <= 0)
                return FalidResult("-1");
            var task = _taskService.Get(id);
            if (task == null)
                return FalidResult("更新任务不存在");
            return Json(task);
        }

        public ActionResult UpdateTaskStatus(int id, int status)
        {
            var task = _taskService.Get(id);
            if (task == null)
                return FalidResult("任务已经被删除");
            task.Status = status;
            _taskService.Update(task);
            return Json(task);
        }

        /// <summary>
        /// 获取满足条件的物流订单 
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        private List<OrderLogisticInfoModel> GetOrderLogisticList(LogisticQueryListRequestModel requestModel)
        {
            if (requestModel.ShopId != null && requestModel.ShopId.Count > 0)
            {
                var shopIds = requestModel.ShopId.Where(f => f != 0).ToList();
                if (shopIds.Count == 0)
                {
                    shopIds = SiteContext.Current.ShopIds;
                }
                requestModel.ShopId = shopIds;
            }
            else
            {
                var shopList = new List<Shop> { SiteContext.Current.CurrentLoginShop };
                if (SiteContext.Current.ChildShop != null && SiteContext.Current.ChildShop.Count > 0)
                {
                    SiteContext.Current.ChildShop.ForEach(item =>
                    {
                        if (SiteContext.Current.CurrentLoginShop.PlatformType == item.PlatformType)
                            shopList.Add(item);
                    });
                }
                requestModel.ShopId = shopList.Select(f => f.Id).ToList();
            }

            var list = new List<OrderLogisticInfoModel>();
            requestModel.PageSize = 1000;
            requestModel.PageIndex = 1;
            var returnSize = requestModel.PageSize;
            while (returnSize == requestModel.PageSize)
            {
                var pageModel = _orderLogisticInfoService.LoadList(requestModel);
                returnSize = pageModel?.Rows?.Count ?? 0;
                requestModel.PageIndex++;
                var rows = pageModel?.Rows ?? new List<OrderLogisticInfoModel>();
                if (rows.Any())
                {
                    list.AddRange(pageModel.Rows);
                }
            }
            return list;
        }

        [LogForOperatorFilter("更改物流预警启用状态")]
        public ActionResult UpdateLogisticSwitchStatus()
        {
            var log = LogForOperatorContext.Current.logInfo;
            var curShopId = SiteContext.Current.CurrentShopId;
            //更新物流预警设置状态
            var sets = _commonSettingService.GetSets(new List<string> { "OrderLogisticTracesSet", "/Logistic/SwitchStatusUpdateTime" }, curShopId);
            var enableSet = sets.FirstOrDefault(x => x.Key == "OrderLogisticTracesSet")?.Value.ToObject<WarningSetModel>() ?? new WarningSetModel();
            var status = Request["Status"].ToInt();
            enableSet.SubjectSet = status;
            _commonSettingService.Set("OrderLogisticTracesSet", enableSet.ToJson(), curShopId);

            var now = DateTime.Now;
            var switchUpdateTimeSet = sets.FirstOrDefault(x => x.Key == "/Logistic/SwitchStatusUpdateTime");
            var switchUpdateTime = switchUpdateTimeSet == null || switchUpdateTimeSet.Value.IsNullOrEmpty() ? "" : switchUpdateTimeSet.Value;
            var diffDays = 7;
            if (status == 1)
            {
                var remark = "启用物流预警";
                // 启用物流预警时，记录最新开启时间
                var updateTime = string.Empty;
                if (switchUpdateTime.IsNullOrEmpty())
                {
                    //第一次启用，默认当前时间
                    remark = "第一次启用，默认当前时间";
                    updateTime = now.ToString("yyyy-MM-dd HH:mm:ss");
                }
                else
                {
                    //上次启用时间超过N天，最新启用时间更新为N天前       
                    var totalDays = (now - switchUpdateTime.toDateTime()).TotalDays;
                    remark = totalDays > diffDays ? $"上次启用时间大于{diffDays}天，默认最近更新时间为【{diffDays}】天前" : $"上次启用时间小于{diffDays}天";
                    if (totalDays > diffDays)
                        updateTime = now.AddDays(-diffDays).ToString("yyyy-MM-dd HH:mm:ss");
                    else
                        updateTime = switchUpdateTime;
                }

                _commonSettingService.Set("/Logistic/SwitchStatusUpdateTime", updateTime, curShopId);
                var days = (now - updateTime.toDateTime()).TotalDays.ToInt();
                log.Detail = new { LastUpdateTime = updateTime, Days = days, Remark = remark };

                return SuccessResult(new { UpdateTime = updateTime, Days = days });
            }
            else
            {
                var remark = "关闭物流预警";
                var updateTime = switchUpdateTime;
                var days = diffDays;
                // 关闭物流预警，配置中没有更新时间时，默认修改时间为N天前（下次开启时已此时间与开始时间比较作为查询开始时间）
                if (switchUpdateTime.IsNullOrEmpty())
                {
                    remark = $"第一次记录关闭物流预警，默认最近更新时间为【{days}】天前";
                    updateTime = now.AddDays(-diffDays).Format();
                    _commonSettingService.Set("/Logistic/SwitchStatusUpdateTime", updateTime, curShopId);
                }
                days = (now - updateTime.toDateTime()).TotalDays.ToInt();
                log.Detail = new { LastUpdateTime = updateTime, Days = days, Remark = remark };
            }
            return SuccessResult();
        }

        #region Excel导出
        private IWorkbook BuildExcel(List<OrderLogisticInfoModel> dataList, string fileName)
        {
            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet();
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);

            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            var heads = new Dictionary<string, string>() {
                //{ "打印时间" ,"PrintTime"},
                { "发货时间","SendTime"},
                //{ "添加时间","CreateTime"},
                { "快递","LogisticName"},
                { "收件人","ToName"},
                { "订单编号","PlatformOrderId"},
                { "快递单号","LogisticOrderId"},
                { "异常状态","ExceptionStatus"},
                { "包裹状态","LogisticStatusStr"},
                { "最新物流","LastTraces"},
            };
            heads.ToList().ForEach(h =>
            {
                //设置列宽度
                sheet.SetColumnWidth(colIndex, 20 * 256);
                // 设置列名和样式
                headerRow.CreateCell(colIndex).SetCellValue(h.Key);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;
            dataList.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;
                var dic = model.ToDictionary();
                colIndex = 0;
                foreach (var item in heads.ToList())
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item.Value.ToString2() ?? "";
                    if (dic?.ContainsKey(key) == true)
                    {
                        var val = dic[key]?.ToString2() ?? "";
                        if ((key == "PrintTime" || key == "SendTime" || key == "LastTraces") && val.IsNullOrEmpty())
                        {
                            val = "--";
                        }
                        else if (key == "ToName" && SiteContext.Current.CurrentLoginShop.PlatformType == PlatformType.Pinduoduo.ToString())
                        {
                            val = val.ToEncryptName();
                        }
                        //else if (key == "LogisticStatus")
                        //{
                        //    var status = val.ToInt();
                        //    val = status < 2 ? "--" : val == "2" ? "已揽件，无物流" : val == "3" ? "已揽件，有物流" : val == "4" ? "派件中" : val == "5" ? "已签收" : "未知状态";
                        //}

                        dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                        dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                        colIndex++;
                    }
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;
            });

            return workbook;
        }

        private ICellStyle GetHeadStyle(IWorkbook workbook)
        {
            IFont font = workbook.CreateFont();
            font.FontName = "Times New Roman";
            font.Boldweight = short.MaxValue;
            font.FontHeightInPoints = 11;

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(font);
            headerStyle.Alignment = HorizontalAlignment.Center;//内容居中显示
            headerStyle.WrapText = true;

            headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightOrange.Index;
            headerStyle.FillPattern = FillPattern.SolidForeground;
            return headerStyle;
        }

        private ICellStyle GetContentStyle(IWorkbook workbook, HorizontalAlignment alignment)
        {
            IFont font = workbook.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = workbook.CreateCellStyle();
            contentStyle.SetFont(font);
            contentStyle.Alignment = alignment;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.WrapText = true;

            return contentStyle;
        }
        #endregion
    }
}