using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DianGuanJiaApp.Services;
using System.Collections;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models.Account;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Services.PlatformService;
using System.Text;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace DianGuanJiaApp.Controllers
{
    public class MessageController : Controller
    {
        private ShopService _shopService = new ShopService();
        private SyncOrderService _syncOrderService;
        private SyncProductService _syncProductService = new SyncProductService();

        #region 公共方法

        private string _body = "";

        /// <summary>
        /// 请求报文
        /// </summary>
        public string RequestBody
        {
            get
            {
                if (string.IsNullOrEmpty(_body))
                {
                    try
                    {
                        var req = Request.InputStream;
                        if (req != null && req.Length > 0)
                        {
                            req.Seek(0, System.IO.SeekOrigin.Begin);
                            var json = new System.IO.StreamReader(req).ReadToEnd();
                            _body = json;
                        }
                    }
                    catch
                    {
                    }
                }
                return _body;
            }
        }

        #endregion

        private ContentResult FailMessage(string msg)
        {
            if (Response != null)
                Response.StatusCode = 400;
            return Content(msg);
        }

        public ActionResult Check(string id)
        {
            var shop = _shopService.Get(id, Data.Enum.PlatformType.Alibaba);
            if (shop == null)
                return Content("0");
            else
                return Content("1");
        }

        public ActionResult Receive()
        {
            var json = "";
            try
            {
                var bytes = new Byte[Request.InputStream.Length];
                Request.InputStream.Read(bytes, 0, bytes.Length);
                json = Encoding.UTF8.GetString(bytes);
                if (CustomerConfig.IsDebug)
                    Log.WriteLine($"接收到消息：{json}");
            }
            catch (Exception ex)
            {
                Log.WriteError($"解析消息时发生错误：{ex}");
                return FailMessage("Invalid Message");
            }
            var msg = json.ToObject<AlibabaMessageModel>();
            if (msg == null)
                return FailMessage("消息不能为空");
            var message = msg.ToJson();
            if (msg == null || string.IsNullOrEmpty(msg.userInfo) || string.IsNullOrEmpty(msg.type))
            {
                Log.WriteError($"处理消息信息失败，系统无法解析：{message}");
                return FailMessage("处理消息信息失败，系统无法解析");
            }
            //判断消息类型
            //过滤一些不需要的消息，仅接受发货、付款相关的消息
            //var tp = msg.type;
            //var validMsg = new List<string>() { "ORDER_ANNOUNCE_SENDGOODS", "ORDER_PART_PART_SENDGOODS", "ORDER_ORDER_PRICE_MODIFY", "ORDER_ORDER_STEP_PAY", "ORDER_PAY"};
            //if (!validMsg.Any(x=>x==tp))
            //    return Content("1");

            var shop = _shopService.Get(msg.userInfo, Data.Enum.PlatformType.Alibaba);
            if (shop == null)
            {
                Log.WriteWarning($"没有查询到ShopID为:{msg.userInfo}的1688店铺信息,消息内容：{message}");
                return FailMessage($"没有查询到ShopID为:{msg.userInfo}的1688店铺信息");
            }

            try
            {
                return Process1688Message(msg, shop);
            }
            catch (Exception ex)
            {
                try
                {
                    return Process1688Message(msg, shop);
                }
                catch (Exception ex2)
                {
                    Log.WriteError($"处理消息信息失败，系统发生错误：店铺ID：{shop?.Id}，消息内容：{json}，错误信息：{ex2}");
                    return FailMessage($"处理消息信息失败，系统发生错误：{ex.Message}");
                }
            }
        }

        private ContentResult Process1688Message(AlibabaMessageModel msg,Shop shop)
        {
            var sc = new SiteContext(shop);
            _syncOrderService = new SyncOrderService();
            if (msg.type.Contains("ORDER"))
                _syncOrderService.SyncSingleOrder(msg.data.orderId, shop);
            else if (msg.type.Contains("PRODUCT"))
                _syncProductService.SyncProduct(msg.data.productIds, shop);
            return Content("1");
        }

        public ActionResult Receiveold(AlibabaMessageModel msg)
        {
            Log.WriteLine($"接收到消息：{msg.ToJson()}");
            if (msg == null)
                return FailMessage("消息不能为空");
            var message = msg.ToJson();
            if (msg == null || string.IsNullOrEmpty(msg.userInfo))
            {
                Log.WriteError($"处理消息信息失败，系统无法解析：{message}");
                return FailMessage("处理消息信息失败，系统无法解析");
            }
            //判断消息类型
            var shop = _shopService.Get(msg.userInfo, Data.Enum.PlatformType.Alibaba);
            if (shop == null)
            {
                Log.WriteWarning($"没有查询到ShopID为:{msg.userInfo}的1688店铺信息,消息内容：{message}");
                return FailMessage($"没有查询到ShopID为:{msg.userInfo}的1688店铺信息");
            }

            try
            {
                if (msg.type.Contains("ORDER"))
                    _syncOrderService.SyncSingleOrder(msg.data.orderId, shop);
                else if (msg.type.Contains("PRODUCT"))
                    _syncProductService.SyncProduct(msg.data.productIds, shop);
                return Content("1");
            }
            catch (Exception ex)
            {
                Log.WriteError($"处理消息信息失败，系统发生错误：{ex}");
                return FailMessage($"处理消息信息失败，系统发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 自动评论客户端，重刷Token
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public ActionResult RefreshToken(int shopId)
        {   
            try
            {
                var shop = _shopService.Get(shopId);
                if (shop == null)
                    return Content("RefreshToken_Error");

                var service = new AlibabaPlatformService(shop);
                shop = service.RefreshShopToken(true);
               
                return Content(shop.ToJson());
            }
            catch (Exception ex)
            {       
                Log.WriteError($"自动评论客户端，重刷Token失败：{ex}");
            }
            
            return Content("RefreshToken_Error");
        }

        #region 有赞消息处理

        /// <summary>
        /// 接收有赞消息
        /// </summary>
        /// <returns></returns>
        public ActionResult Youzan()
        {
            var headers = Request.Headers;
            var eventSign = headers["Event-Sign"]?.ToString();
            var eventType = headers["Event-Type"]?.ToString();
            var clientId = headers["Client-Id"]?.ToString();
            var successResult = Json(new { code = 0, msg = "success" },JsonRequestBehavior.AllowGet); 
            var failedResult = Json(new { code = 0, msg = "failed" },JsonRequestBehavior.AllowGet);
            var json = "";
            if (string.IsNullOrEmpty(eventSign) || string.IsNullOrEmpty(eventType) || string.IsNullOrEmpty(clientId))
                return failedResult;
            else
                json = RequestBody??"";
            //Log.WriteLine($"接收有赞消息,eventSign:{eventSign},eventType:{eventType},clientId:{clientId},msg:{json}");
            var md5 = Utility.CommUtls.MD5(clientId+json+CustomerConfig.YouZanAppSecret);
            if (!string.Equals(md5, eventSign, StringComparison.OrdinalIgnoreCase))
                return failedResult;
            JToken jtoken = null;
            var shopId = "";
            try
            {
                jtoken = JsonConvert.DeserializeObject<JToken>(json);
                shopId = jtoken?.Value<string>("kdt_id");
            }
            catch (Exception ex)
            {
                Log.WriteError($"解析有赞消息时发生错误：消息内容：{json}，错误信息：{ex}");
                return failedResult;
            }
            var shop = _shopService.Get(shopId, Data.Enum.PlatformType.YouZan);
            if(shop==null)
            {
                Log.WriteError($"根据有赞消息中的店铺ID无法查询到对应的店铺：消息内容：{json}，ShopId:{shopId}");
                return failedResult;
            }
            var sc = new SiteContext(shop);
            _syncOrderService = new SyncOrderService();
            if (eventType.ToLower().StartsWith("trade_"))
            {
                var tid = JsonConvert.DeserializeObject<JToken>(Server.UrlDecode(jtoken?.Value<string>("msg") ?? ""))?.Value<string>("tid");
                if(string.IsNullOrEmpty(tid))
                    tid = JsonConvert.DeserializeObject<JToken>(Server.UrlDecode(jtoken?.Value<string>("msg") ?? ""))?.Value<JToken>("full_order_info")?.Value<JToken>("order_info")?.Value<string>("tid");
                if (!string.IsNullOrEmpty(tid))
                {
                    try
                    {
                        _syncOrderService.SyncSingleOrder(tid, shop);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"同步有赞订单时发生错误：消息内容：{json}，错误信息：{ex.Message}");
                        return failedResult;
                    }
                }
                else
                {
                    Log.WriteError($"解析有赞消息时发生错误：消息内容：{json}，错误信息：消息内容中未找到订单ID");
                    return failedResult;
                }
            }
            else if(eventType.ToLower().Contains("item_") || eventType.ToLower().Contains("sold_out_"))
            {
                var data = JsonConvert.DeserializeObject<JToken>(Server.UrlDecode(jtoken?.Value<string>("msg") ?? ""))?.Value<string>("data");
                var pid = "";
                if(!string.IsNullOrEmpty(data))
                    pid = JsonConvert.DeserializeObject<JToken>(data)?.Value<string>("item_id");
                if(!string.IsNullOrEmpty(pid))
                {
                    try
                    {
                        _syncProductService.SyncProduct(pid, shop);
                    }
                    catch
                    {
                        //Log.WriteError($"同步有赞产品时发生错误：消息内容：{json}，错误信息：{ex.Message}");
                        return failedResult;
                    }
                }
                else
                {
                    Log.WriteError($"解析有赞消息时发生错误：消息内容：{json}，错误信息：消息内容中未找到商品ID");
                    return failedResult;
                }
            }
            //Log.WriteLine($"处理有赞消息成功，ShopId:{shopId},msg:{json}");
            return successResult;
        }

        #endregion
    }
}