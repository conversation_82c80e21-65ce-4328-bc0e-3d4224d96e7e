using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.LogisticService;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Services.WaybillService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using DianGuanJiaApp.Utility.Web;
using Jd.Api.Domain;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
using WebGrease.Css.Extensions;
using Z.BulkOperations;
using OperatorType = DianGuanJiaApp.Data.Model.OperatorType;

namespace DianGuanJiaApp.Controllers
{
    public class OrderController : BaseController
    {
        protected CustomerOrderService _customerOrderService = new CustomerOrderService();
        //private SyncOrderService _syncOrderService = new SyncOrderService();
        protected PrintTemplateService _printTemplateService = new PrintTemplateService();
        //private OrderFilterService _orderFilterService = new OrderFilterService();
        protected OrderCategoryService _orderCategory = new OrderCategoryService();
        protected AreaCodeInfoService _areaCodeInfoService = new AreaCodeInfoService();
        protected PrintHistoryService _printHistoryService = new PrintHistoryService();
        //private MergerOrderService _mergerOrderService = new MergerOrderService();
        protected ShopService _shopService = new ShopService();
        protected SellerInfoService _sellerInfoService = new SellerInfoService();
        protected CommonSettingService _commonSettingService = new CommonSettingService();
        protected WaybillCodeService _waybillCodeService = new WaybillCodeService();
        protected CommService _commService = new CommService();
        protected ExpressCpCodeMappingService _cpCodeMappingService = new ExpressCpCodeMappingService();
        protected IPlatformService _service;
        private ExportTaskService _exportTaskService = new ExportTaskService();

        private PlatformRemarkService _platformRemarkService = new PlatformRemarkService();

        protected const string OrderPrintBatchNumberKey = "OrderPrintBatchNumber";
        protected const string TradionalWaybillCodeConfigKey = "TradionalWaybillCodeConfig";
        protected const string PrintExpressAllRspLogKey = "PrintExpressAllRspLog";

        public OrderController() { }
        public OrderController(OrderService orderService)
        {
            MyOrderService = orderService;
        }

        /// <summary>
        /// 订单首页
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            var shop = SiteContext.Current.CurrentLoginShop;
            var pt = shop.PlatformType;
            if (pt == PlatformType.Offline.ToString())
            {
                if (SiteContext.Current.CurrentLoginUser?.Type == 1)
                    return Redirect("/WDYY/Index?token=" + Request["token"]);
                else
                    return Redirect("/Order/FreePrintList?token=" + Request["token"]);
            }
            else if (pt == PlatformType.Taobao.ToString())//Request?.Url?.Host?.ToLower()?.Contains("taobao1")==true && 
            {
                //订阅淘宝的消息
                var tbShops = SiteContext.Current.AllSamePlatformTypeShops;
                ThreadPool.QueueUserWorkItem(state =>
                {
                    try
                    {
                        foreach (var s in tbShops)
                        {
                            var tbPlatformService = new TaobaoPlatformService(s);
                            var isSuccess = tbPlatformService.OpenMessage();
                            Log.Debug($"店铺【{s.ShopName}】消息开通结果：{isSuccess}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"开通淘宝消息时发生错误：{ex}");
                    }
                });
            }
            else if (pt == PlatformType.Pinduoduo.ToString())
            {
                //获取解密插件初始化code
                var pddPlatformService = new PinduoduoPlatformService(shop);
                var mallIds = SiteContext.Current.AllSamePlatformTypeShops?.Select(f => f.ShopId);
                var pageCode = pddPlatformService.GetPddPageCode(mallIds?.ToList(), Request?.Url?.ToString(), shop?.Id.ToString());
                ViewBag.PddPageCode = pageCode.Key ? (pageCode.Value ?? "") : "";
            }

            //前端打印分批发送订单数量
            ViewBag.OrderPrintBatchNumber = GetOrderPrintBatchNumber();

            //加载常用模板
            var templateList = _printTemplateService.LoadTemplateList(new List<int> { SiteContext.Current.ExpressTemplateShopId }, true);
            templateList = templateList?.OrderBy(f => f.Sort)?.ToList();
            ViewBag.StapleTempalteList = templateList.ToJson();

            //传统面单 单号 递增 ，递减 ，当前单号
            var tradional_waybillcode_config = _commonSettingService.GetString(TradionalWaybillCodeConfigKey, SiteContext.Current.CurrentShopId);
            ViewBag.TradionalWaybillCodeConfig = string.IsNullOrWhiteSpace(tradional_waybillcode_config) ? "{}" : tradional_waybillcode_config;
            if (shop.PlatformType == "Taobao")
                ych_sdk.YchRequestLogger.Login(shop.Id.ToString(), shop.ShopId, true);
            else if (shop.PlatformType == PlatformType.Jingdong.ToString())
                jos_sdk_net.JdRequestLogger.Login(shop.AccessToken, shop.Id.ToString(), shop.ShopId, true);

            var printExpressAllRspLog = _commonSettingService.GetBool(PrintExpressAllRspLogKey, shop.Id);
            ViewBag.PrintExpressAllRspLog = printExpressAllRspLog ? 1 : 0;
            //前台获取导出任务
            var exportTask = _exportTaskService.GetExportTask(shop.Id, ExportType.Order.ToInt());
            // 显示未完成任务或已完成一天前的导出任务
            exportTask = exportTask != null && ((exportTask.Status >= 0 && exportTask.Status < 4) || (exportTask.Status >= 4 && exportTask.UploadToServerTime != null && DateTime.Now < exportTask.UploadToServerTime.Value.AddDays(1))) ? exportTask : null;
            var task = GetExportTaskToWeb(exportTask);
            ViewBag.OrderPrintExportTask = task?.ToJson() ?? "null";
            // 加载默认配置
            LoadDefaultConfig(false);
            //CheckIfProductIsNeedSync();
            return View();
        }

        protected int GetOrderPrintBatchNumber()
        {
            var numStr = _commonSettingService.GetString(OrderPrintBatchNumberKey, SiteContext.Current.CurrentShopId);
            if (string.IsNullOrWhiteSpace(numStr))
                return 20;
            return int.Parse(numStr);
        }

        public void LoadDefaultConfig(bool isCustomerOrder)
        {
            var currShop = SiteContext.Current.CurrentLoginShop;
            var isUseOldTheme = false;// SiteContext.Current?.IsUseOldTheme ?? false;
            var platformType = isUseOldTheme ? "Old" : SiteContext.Current.CurrentLoginShop.PlatformType;

            var keys = new List<string> {
                "/OrderPrint/DefaultLoadShop","OrderCategorySet", "BottomMoreFunSets", "ProductShowStyleSet", "PaggingOrderBySet",
                "CustomExtraConditionsSet", "CustomConditionSet", "CutomColumnsSet", "CutomAdvancedContidionSet",
                "CustomOrderExportSet","AutoSendSet","/Order/ProductShowType" };
            if (isCustomerOrder)
                keys.Add("/Export/FreePrint/UpdateTime");
            else
                keys.Add("/Export/OrderPrint/UpdateTime");
            var commSets = _commonSettingService.GetSets(keys, currShop.Id);
            var productShowTypeSet = commSets?.FirstOrDefault(m => m.Key == "/Order/ProductShowType");
            var paggingOrderBySet = commSets?.FirstOrDefault(m => m.Key == "PaggingOrderBySet");
            var customExtraConditionsSet = commSets?.FirstOrDefault(m => m.Key == "CustomExtraConditionsSet");
            var customConditionSet = commSets?.FirstOrDefault(m => m.Key == "CustomConditionSet");
            var productShowStyleSet = commSets?.FirstOrDefault(m => m.Key == "ProductShowStyleSet");
            var orderCategorysSetting = commSets?.FirstOrDefault(m => m.Key == "OrderCategorySet");
            var bottomMoreFunSets = commSets.FirstOrDefault(m => m.Key == "BottomMoreFunSets");
            //var orderTimeDefaultSet = commSets.FirstOrDefault(m => m.Key == "OrderTimeDefaultSet");
            var cutomColumnsSet = commSets.FirstOrDefault(m => m.Key == "CutomColumnsSet");
            var cutomAdvancedContidionSet = commSets.FirstOrDefault(m => m.Key == "CutomAdvancedContidionSet");
            var customOrderExportSet = commSets.FirstOrDefault(m => m.Key == "CustomOrderExportSet");
            var autoSendSet = commSets.FirstOrDefault(m => m.Key == "AutoSendSet");
            CommonSetting exportUpdateTimeSet = null;
            if (isCustomerOrder)
                exportUpdateTimeSet = commSets.FirstOrDefault(m => m.Key == "/Export/FreePrint/UpdateTime");
            else
                exportUpdateTimeSet = commSets.FirstOrDefault(m => m.Key == "/Export/OrderPrint/UpdateTime");

            var defaultLoadShopSet = commSets.FirstOrDefault(m => m.Key == "/OrderPrint/DefaultLoadShop");
            var defaultCustomExtraConditionsSetVal = customExtraConditionsSet?.Value.ToString2() ?? "";
            var defaultCutomAdvancedContidionSetVal = cutomAdvancedContidionSet?.Value.ToString2() ?? "";

            var defaultLoadShopVal = defaultLoadShopSet?.Value.ToString2() ?? "";
            if (defaultLoadShopVal.IsNullOrEmpty())
            {
                defaultLoadShopVal = "CurrShop";
            }

            var defaultPaggingOrderBySetVal = paggingOrderBySet?.Value.ToString2() ?? "";
            if (defaultPaggingOrderBySetVal.IsNullOrEmpty())
                defaultPaggingOrderBySetVal = GetDefaultSetting(platformType, "PaggingOrderBySet");

            var defaultCustomConditionSetVal = customConditionSet?.Value.ToString2() ?? "";
            if (defaultCustomConditionSetVal.IsNullOrEmpty() || isUseOldTheme)
                defaultCustomConditionSetVal = GetDefaultSetting(platformType, "CustomConditionSet");

            var defaultProductShowStyleSetVal = productShowStyleSet?.Value.ToString2() ?? "";
            if (defaultProductShowStyleSetVal.IsNullOrEmpty() || isUseOldTheme)
                defaultProductShowStyleSetVal = GetDefaultSetting(platformType, "ProductShowStyleSet");

            var defaultOrderCategorysSetVal = orderCategorysSetting?.Value.ToString2() ?? "";
            if (defaultOrderCategorysSetVal.IsNullOrEmpty())
                defaultOrderCategorysSetVal = GetDefaultSetting(platformType, "OrderCategorySet");

            var defaultBottomMoreFunSetsVal = bottomMoreFunSets?.Value.ToString2() ?? "";
            if (defaultBottomMoreFunSetsVal.IsNullOrEmpty() || isUseOldTheme)
                defaultBottomMoreFunSetsVal = GetDefaultSetting(platformType, "BottomMoreFunSets");

            var defaultCutomColumnsSetVal = cutomColumnsSet?.Value.ToString2() ?? "";
            if (defaultCutomColumnsSetVal.IsNullOrEmpty() || isUseOldTheme)
                defaultCutomColumnsSetVal = GetDefaultSetting(platformType, "CutomColumnsSet");

            var defaultCustomOrderExportSetVal = customOrderExportSet?.Value.ToString2() ?? "";
            if (defaultCustomOrderExportSetVal.IsNullOrEmpty() || isUseOldTheme)
                defaultCustomOrderExportSetVal = GetDefaultSetting(platformType, "CustomOrderExportSet");

            var autoSendSetVal = autoSendSet?.Value.ToString2() ?? "0";

            //最后导出时间
            var defaultExportUpdateTime = exportUpdateTimeSet?.Value.ToDateTime() ?? null;
            var defaultExportUpdateTimeSetVal = defaultExportUpdateTime == null ? "" : defaultExportUpdateTime.Value.ToString("yyyy-MM-ddTHH:mm:ss");
            // 默认导出间隔时长（300s）
            var defaultExportExpireSecondsSet = _commonSettingService.Get("/Export/Order/ExpireSeconds", 0);
            var defaultExportExpireSeconds = defaultExportExpireSecondsSet?.Value.ToInt() ?? 0;
            defaultExportExpireSeconds = defaultExportExpireSeconds <= 0 ? 300 : defaultExportExpireSeconds;

            var areaCodeInfos = _areaCodeInfoService.GetTreeAreaInfoList();
            ViewBag.AreaCodeInfos = areaCodeInfos?.ToJson() ?? "";

            ViewBag.Shops = SiteContext.Current.AllShops?.Where(m => m.PlatformType == currShop.PlatformType && (m.IsServiceEnd == false || m.Id == currShop.Id)).ToList().ToJson() ?? "";
            ViewBag.OrderCategorys = defaultOrderCategorysSetVal;

            ViewBag.BottomMoreFunSets = defaultBottomMoreFunSetsVal;
            ViewBag.PaggingOrderBySet = defaultPaggingOrderBySetVal;
            ViewBag.CustomExtraConditionsSet = defaultCustomExtraConditionsSetVal;
            ViewBag.ProductShowStyleSet = defaultProductShowStyleSetVal;
            ViewBag.CustomConditionSet = defaultCustomConditionSetVal;
            ViewBag.CutomColumnsSet = defaultCutomColumnsSetVal;
            ViewBag.CutomAdvancedContidionSet = defaultCutomAdvancedContidionSetVal;
            ViewBag.CustomOrderExportSet = defaultCustomOrderExportSetVal;
            ViewBag.DefaultLoadShop = defaultLoadShopVal;
            ViewBag.AutoSendSetVal = autoSendSetVal;
            ViewBag.ExportUpdateTimeSet = defaultExportUpdateTimeSetVal;
            ViewBag.ExportExpireSeconds = defaultExportExpireSeconds;
            ViewBag.ProductShowType = productShowTypeSet == null || productShowTypeSet.Value.IsNullOrEmpty() ? 1 : productShowTypeSet.Value.ToInt();
        }

        public string GetDefaultSetting(string platformType, string key)
        {
            var ptKey = $"Default_{platformType}_{key}";
            key = $"Default_{key}";
            var keys = new List<string> { key, ptKey };
            var commSets = _commonSettingService.GetSets(keys, 0);
            var commonSet = commSets.FirstOrDefault(m => m.Key == ptKey);
            if (commonSet == null)
                commonSet = commSets.FirstOrDefault(m => m.Key == key);
            return commonSet?.Value.ToString2() ?? "";
        }

        #region 微商加载订单类别

        public ActionResult WsXcxLoadConfig()
        {
            var currShop = SiteContext.Current.CurrentLoginShop;
            var orderCategorysSetting = _commonSettingService.Get("/WsXcx/OrderCategorySet", currShop.Id);
            List<OrderCategory> categoryLst = orderCategorysSetting == null || orderCategorysSetting.Value.IsNullOrEmpty() ? new List<OrderCategory>() : JsonExtension.ToList<OrderCategory>(orderCategorysSetting.Value.ToString2());
            if (categoryLst == null || categoryLst.Count == 0)
            {
                categoryLst = new List<OrderCategory>()
                {
                    new OrderCategory(){ Id= 1,Alias="衣物",Name="衣物",Color="#ff0000"},
                    new OrderCategory(){ Id= 2,Alias="鞋帽",Name="鞋帽",Color="#000000"},
                    new OrderCategory(){ Id= 3,Alias="数码产品",Name="数码产品",Color="#d3bafc"},
                    new OrderCategory(){ Id= 4,Alias="美妆",Name="美妆",Color="#ffff00"},
                    new OrderCategory(){ Id= 5,Alias="食品",Name="食品",Color="#0000ff"},
                    new OrderCategory(){ Id= 6,Alias="水果",Name="水果",Color="#000000"},
                    new OrderCategory(){ Id= 7,Alias="箱包",Name="箱包",Color="#ff00cc"},
                    new OrderCategory(){ Id= 8,Alias="百货日用",Name="百货日用",Color="#f98a07"},
                    new OrderCategory(){ Id= 9,Alias="文件",Name="文件",Color="#a30053"},
                    new OrderCategory(){ Id= 10,Alias="药品",Name="药品",Color="#7eff00"},
                    new OrderCategory(){ Id= 11,Alias="母婴用品",Name="母婴用品",Color="#7eff00"},
                    new OrderCategory(){ Id= 12,Alias="玩具",Name="玩具",Color="#7eff00"},
                    new OrderCategory(){ Id= 13,Alias="其他",Name="其他",Color="#7eff00"},
                };

                var json = JsonExtension.ToJson(categoryLst);
                var result = _commonSettingService.Set("/WsXcx/OrderCategorySet", json, currShop.Id);
            }

            return SuccessResult(new { CategoryLst = categoryLst });
        }
        #endregion

        #region 店铺过期提示

        public ActionResult EndService()
        {
            var endServiceDay = CustomerConfig.EndServiceDay;

            bool isShow = false;
            string endServiceDateNum = "";
            //string getAppPayUrl = "";
            List<EndServiceDayModel> listModel = new List<EndServiceDayModel>();
            try
            {
                List<Shop> listShop = SiteContext.Current.AllShops;

                listShop.ForEach(o =>
                {
                    _service = PlatformFactory.GetPlatformService(o);
                    var time = _service.GetExpiredTime();

                    if (!string.IsNullOrEmpty(Convert.ToString(time)))
                    {

                        TimeSpan leaveData = Convert.ToDateTime(time) - DateTime.Now;
                        double datanum = leaveData.TotalDays;
                        endServiceDateNum = ((int)Math.Ceiling(datanum)).ToString();


                        if (Convert.ToDouble(endServiceDateNum) < endServiceDay)
                        {
                            var payUrl = o.PayUrl;
                            //如果是店管家内的订购，且没有关联用户，需引导注册
                            //if (payUrl.Contains(CustomerConfig.AppStoreUrl) && SiteContext.Current.CurrentLoginUser == null)
                            //{
                            //    var token = _shopService.CreateToken(o.Id, LoginAuthToken.Sign);
                            //    payUrl = $"javascript:commonModule.register(\'pay\',\'{CustomerConfig.PortalLink}\',\'{token.Token}\')";
                            //}
                            if (!string.IsNullOrEmpty(payUrl))
                            {
                                listModel.Add(new EndServiceDayModel
                                {
                                    ShopId = o.Id,
                                    ShopName = o.NickName,
                                    EndServiceDateNum = endServiceDateNum,
                                    GetAppPayUrl = payUrl,
                                    PlatformTypeName = o.PlatformTypeName
                                });
                                isShow = true;
                            }
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                isShow = false;
            }
            return SuccessResult(new { IsShow = isShow, ListData = listModel });
        }

        #endregion

        #region 已续费手动更新过期时间
        public ActionResult GetExpireTimeFromApi()
        {
            var curShopExpireTime = DateTime.Now.AddDays(-1);
            var curShop = SiteContext.Current.CurrentLoginShop;
            var shops = SiteContext.Current.AllShops;
            shops.ForEach(s =>
            {
                var service = PlatformFactory.GetPlatformService(s);
                s.ExpireTime = DateTime.Now.AddDays(-1);
                var expireTime = service.GetExpiredTime();
                if (curShop.Id == s.Id && expireTime != null)
                {
                    curShopExpireTime = expireTime.Value;
                    s.ExpireTime = curShopExpireTime;
                }
            });
            var result = curShopExpireTime > DateTime.Now ? $"到期时间：{curShopExpireTime.Format("yyyy-MM-dd")}" : "已到期，请续费";
            return SuccessResult(result);
        }
        #endregion

        #region 检查是否存在默认发件人


        //目前打印时，没有限制，主要是考虑到界面有很多店铺中，弹出默认填写时，用户取消不填写默认。这时的限制，只是只有之前没有发件人这个限制
        public ActionResult GetDefaultSellerInfo()
        {
            bool isShow = false;
            List<SellerInfoShowModel> listModel = new List<SellerInfoShowModel>();
            try
            {
                var platformType = SiteContext.Current.CurrentLoginShop.PlatformType;

                List<Shop> listShop = SiteContext.Current.AllShops.Where(t => t.PlatformType == platformType).ToList();

                listShop.ForEach(o =>
                {
                    SellerInfo sellerInfo = _sellerInfoService.GetOneDefaultSeller(o.Id);
                    if (sellerInfo == null)
                    {
                        isShow = true;
                        listModel.Add(new SellerInfoShowModel
                        {
                            ShopId = o.Id,
                            ShopName = o.NickName
                        });
                    }
                });

            }
            catch
            {
                isShow = false;
            }

            return SuccessResult(new { IsShow = isShow, ListModel = listModel });
        }
        /// <summary>
        /// 检测当前登录的店铺是否有设置发件人信息 
        /// </summary>
        /// <returns></returns>
        public ActionResult GetCurrentShopHasSellerInfo()
        {
            var hasSeller = false;
            var sellerInfo = _sellerInfoService.GetDefaultSeller(SiteContext.Current.CurrentShopId);
            hasSeller = sellerInfo != null;
            return Json(hasSeller);
        }

        #endregion


        #region 检查是否存在地址库
        public ActionResult IsExistLogisticsAddress()
        {
            bool isShow = false;
            List<LogisticsAddressIsExistShowModel> listModel = new List<LogisticsAddressIsExistShowModel>();
            try
            {
                var platformType = SiteContext.Current.CurrentLoginShop.PlatformType;

                if (platformType == "Taobao")
                {
                    List<Shop> listShop = SiteContext.Current.AllShops.Where(t => t.PlatformType == platformType).ToList();

                    listShop.ForEach(o =>
                    {
                        var service = PlatformFactory.GetPlatformService(o);
                        if (service.GetLogisticsAddress() == 2)
                        {
                            listModel.Add(new LogisticsAddressIsExistShowModel
                            {
                                ShopId = o.Id,
                                ShopName = o.NickName
                            });

                            isShow = true;
                        }
                    });
                }
            }
            catch
            {
                isShow = false;
            }

            return SuccessResult(new { IsShow = isShow, ListModel = listModel });
        }

        public ActionResult SaveLogisticsAddress(LogisticsAddressModel model)
        {
            bool isShow = false;
            var platformType = SiteContext.Current.CurrentLoginShop.PlatformType;
            List<LogisticsAddressSaveResultShowModel> listModel = new List<LogisticsAddressSaveResultShowModel>();
            if (platformType == "Taobao")
            {
                List<Shop> listShop = SiteContext.Current.AllShops.Where(t => t.PlatformType == platformType).ToList();

                listShop.ForEach(o =>
                {
                    string errMsg = "";
                    var service = PlatformFactory.GetPlatformService(o);

                    if (service.GetLogisticsAddress() == 2) //保存时，验证下店铺是否还没有添加
                    {
                        bool isSu = service.SaveLogisticsAddress(model, out errMsg);

                        if (!isSu)
                        {
                            isShow = true;
                            listModel.Add(new LogisticsAddressSaveResultShowModel
                            {
                                ShopName = o.NickName,
                                ErrMsg = errMsg
                            });
                        }
                    }

                });
            }




            return SuccessResult(new { IsShow = isShow, ListModel = listModel });
        }

        #endregion


        public ActionResult LoadAreaInfos()
        {
            return Json(_areaCodeInfoService.GetTreeAreaInfoList());
        }

        /// <summary>
        /// 添加模板部分试图
        /// </summary>
        /// <returns></returns>
        public ActionResult AddTemplateInOrderList()
        {
            return PartialView();
        }

        /// <summary>
        /// 根据订单Id获取订单
        /// </summary>
        /// <param name="platformOrderId"></param>
        /// <returns></returns>
        public ActionResult GetOrderbyOrderId(string platformOrderId)
        {
            var order = MyOrderService.GetOrder(new OrderSelectKeyModel()
            {
                PlatformOrderId = platformOrderId,
                ShopId = SiteContext.Current.CurrentShopId
            });

            return Json(order);
        }

        /// <summary>
        /// 根据订单Id获取订单
        /// </summary>
        /// <param name="platformOrderId"></param>
        /// <returns></returns>
        public ActionResult GetOrderbyOrderIds(string platformOrderIds)
        {
            var models = new List<OrderSelectKeyModel>();
            var orders = new List<Order>();
            var oids = platformOrderIds?.ToList<string>();
            if (oids != null && oids.Any())
            {
                oids.ForEach(oid => models.Add(new OrderSelectKeyModel
                {
                    PlatformOrderId = oid,
                    ShopId = SiteContext.Current.CurrentShopId
                }));

                orders = MyOrderService.GetOrders(models);
            }

            return Json(orders);
        }

        /// <summary>
        /// 获取订单
        /// </summary>
        /// <param name="keyModel"></param>
        /// <returns></returns>
        public ActionResult GetOrder(OrderSelectKeyModel keyModel)
        {
            if (keyModel == null)
                throw new LogicException("参数错误");
            if (keyModel.ShopId == 0)
                keyModel.ShopId = SiteContext.Current.CurrentShopId;

            if (base.IsCustomerOrder)
            {
                var order = _customerOrderService.GetOrder(keyModel);
                if (order == null) throw new LogicException("订单不存在");
                //var order2 = _customerOrderService.ConvertToOrder(new List<CustomerOrder>() { order }).FirstOrDefault();
                return Json(order);
            }
            else
            {
                var order = MyOrderService.GetOrder(keyModel);
                if (order == null) throw new LogicException("订单不存在");
                return Json(order);
            }
        }

        /// <summary>
        /// 获取订单
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>                 
        public ActionResult GetOrders(OrderSearchModel model)
        {
            if (model == null)
            {
                model = new OrderSearchModel();
            }
            model.IsCustomerOrder = false;
            if (!model.Filters.Any(m => m.Name.ToLower() == "shopid"))
            {
                model.Filters.Add(new OrderSearchFieldModel()
                {
                    Name = "ShopId",
                    Value = string.Join(",", SiteContext.Current.ShopIds),
                    Contract = "in",
                    TableAlias = "o",
                    FieldType = "int",
                    TableName = "P_Order"
                });
            }

            if (model.IsCustomerOrder)
            {
                var orders = _customerOrderService.GetOrders(model);
                var os = orders.Rows?.Select(o => o as Order).ToList();
                MyOrderService.SetSellerInfo(os);

                WitespaceToHtml(os);
                return OrderJson(orders);
            }
            else
            {
                var orders = MyOrderService.GetOrders(model);
                var newOrders = MyOrderService.SetSellerInfo(orders.Rows);

                var shop = SiteContext.Current.CurrentLoginShop;
                if (
                    (shop.PlatformType == PlatformType.Taobao.ToString() || shop.PlatformType == PlatformType.Jingdong.ToString())
                    && orders != null && orders.Rows.Any())
                {
                    var pids = new List<string>();
                    orders.Rows.ForEach(t =>
                    {
                        if (string.IsNullOrEmpty(t.ChildOrderId))
                            pids.Add(t.PlatformOrderId);
                        else
                            pids.AddRange(t.ChildOrderId.Split(','));
                    });
                    if (shop.PlatformType == PlatformType.Taobao.ToString())
                        ych_sdk.YchRequestLogger.Order(shop.Id.ToString(), "订单查询", pids);
                    else if (shop.PlatformType == PlatformType.Jingdong.ToString())
                        jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
                }
                WitespaceToHtml(orders.Rows);
                return OrderJson(orders);
            }
            //return List(model);
        }

        [LogForOperatorFilter("根据店铺查询订单")]
        public ActionResult GetOrdersByApi(OrderSearchModel model)
        {
            var sidsFilter = model.Filters.FirstOrDefault(f => f.Name == "ShopId");
            if (sidsFilter == null)
                return GetOrders(model);
            var sids = sidsFilter.Value.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries);
            var shops = SiteContext.Current.AllShops.Where(f => sids.Contains(f.Id.ToString()));

            var currentLoginShopPlatformType = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (shops.Any(f => f.PlatformType == currentLoginShopPlatformType))
                return GetOrders(model);

            var shopGroupbyList = SiteContext.Current.AllShops.GroupBy(f => f.PlatformType + "_" + f.DbConfig?.Identity).ToList();
            return DoGetOrderByApi(shopGroupbyList, model);
        }
        public ActionResult DoGetOrderByApi(IEnumerable<IGrouping<string, Shop>> shopGroupbyList, OrderSearchModel model)
        {
            var api = "/OrderApi/GetOrders";
            var currentLoginShopPlatformType = SiteContext.Current.CurrentLoginShop.PlatformType;
            ConcurrentBag<ActionResult> apiResultList = new ConcurrentBag<ActionResult>();
            Parallel.ForEach(shopGroupbyList, new ParallelOptions { MaxDegreeOfParallelism = 5 }, (item, state) =>
            {
                if (apiResultList.Any())
                {
                    state.Stop();
                    return;
                }

                var sids = item.Select(f => f.Id).ToList();
                var pt = item.Key.Split("_".ToArray()).First();
                if (pt == currentLoginShopPlatformType)
                {
                    ActionResult tempResult = GetOrders(model);
                    apiResultList.Add(tempResult);
                }
                else
                {
                    var apiUrl = GetApiUrl(pt, api);
                    ActionResult tempResult = QueryOrderByApi(apiUrl, model, sids.First());
                    apiResultList.Add(tempResult);
                }
                if (apiResultList.Any())
                {
                    state.Stop();
                    return;
                }
            });

            return apiResultList.FirstOrDefault();
        }

        private ActionResult QueryOrderByApi(string apiUrl, OrderSearchModel model, int sid)
        {
            var apiResult = Common.PostApi<OrderSearchModel, AjaxResult>(apiUrl, sid, model, "通过Api查询订单");
            if (apiResult != null)
                return new CustomOrderJsonResult(apiResult);
            else
                return FalidResult("查询出错");
        }

        public ActionResult LoadProductList(OrderSearchModel model)
        {
            var result = MyOrderService.GetOrders(model);
            var products = result.ProductAtrs;
            var status = model.Filters.FirstOrDefault(x => x.Name == "PlatformStatus")?.Value ?? "waitsellersend";

            products = GetProductCheckListData(status, products);
            return Json(products);
        }

        /// <summary>
        /// 查询订单列表数据
        /// </summary>
        /// <param name="model">查询模型</param>
        /// <returns></returns>
        [LogForOperatorFilter("订单查询")]
        public ActionResult List(OrderSearchModel model)
        {
            //订单编号和运单号批量查询数量判断
            var platformOrderIdFilter = model.Filters.FirstOrDefault(x => x.CustomQuery == "PlatformOrderIdSearch" || x.CustomQuery == "CustomerPlatformOrderIdSearch");
            if (platformOrderIdFilter != null && !platformOrderIdFilter.Value.IsNullOrEmpty())
            {
                if (platformOrderIdFilter.Value.SplitWithString(",").Length > 1000)
                    return FalidResult("查询的订单请不要超过1000");
            }
            var waybillCodeFilter = model.Filters.FirstOrDefault(x => x.CustomQuery == "WaybillCode");
            if (waybillCodeFilter != null && !waybillCodeFilter.Value.IsNullOrEmpty())
            {
                if (waybillCodeFilter.Value.SplitWithString(",").Length > 1000)
                    return FalidResult("查询的运单号请不要超过1000");
            }

            var shop = SiteContext.Current.CurrentLoginShop;
            var isOrderSyncSameTime = true;//_commonSettingService.IsOrderSyncSameTime(shop.Id);
            var log = LogForOperatorContext.Current.logInfo;
            var id = log._Id;
            log.Request = model.ToJson().ToObject<OrderSearchModel>();
            if (model.IsCustomerOrder)
            {
                log.OperatorType = "订单查询-自由打印";
                var sublog = new LogForOperator() { OperatorType = "GetOrders" };
                LogForOperatorContext.Current.StartStep(sublog);

                var orders = _customerOrderService.GetOrders(model);
                orders.LogId = id;

                LogForOperatorContext.Current.EndStep();

                var platformStatus = model?.Filters?.FirstOrDefault(m => m.Name == "PlatformStatus")?.Value ?? "";
                var count = orders.Rows.Count;
                log.Detail = new LogForSearchOrderModel() { PageSize = orders.PageSize, OrderByField = orders.OrderByField, IsOrderDesc = orders.IsOrderDesc, PlatformStatus = platformStatus, ListCount = count, RealCount = count };


                sublog = new LogForOperator() { OperatorType = "SetSellerInfo" };
                LogForOperatorContext.Current.StartStep(sublog);

                var os = orders.Rows?.Select(o => o as Order).ToList();
                MyOrderService.SetSellerInfo(os);

                LogForOperatorContext.Current.EndStep();
                WitespaceToHtml(os);
                return OrderJson(orders);
            }
            else
            {
                var blockSetting = _shopService.GetOperatorBlockSetting(SiteContext.Current.CurrentShopId);
                var isBlockQuery = blockSetting != null && blockSetting.BlockEndTime > DateTime.Now && blockSetting.IsQueryBlocked;
                var isBlockSync = blockSetting != null && blockSetting.BlockEndTime > DateTime.Now && blockSetting.IsSyncBlocked;
                if (isBlockQuery)
                {
                    return OrderJson(new PagedResultModel<Order>
                    {
                        IsSyncing = true,
                        Rows = new List<Order>()
                    });
                }
                //var log = new LogForOperator() { OperatorType = "List", SearchCondition = JsonExtension.ToJson(model) };
                //LogForOperatorContext.Current.Begin(log);
                if (isOrderSyncSameTime)
                {
                    var syncLog = new LogForOperator() { OperatorType = "增量同步订单" };
                    LogForOperatorContext.Current.StartStep(syncLog);
                    if (isBlockSync)
                    {
                        syncLog.IsError = true;
                        syncLog.Remark = "该店铺订单同步已被禁用";
                    }
                    else
                        StartSyncOrder(syncLog);
                    LogForOperatorContext.Current.EndStep();

                    //var syncHotLog = new LogForOperator() { OperatorType = "增量同步热数据" };
                    //LogForOperatorContext.Current.StartStep(syncHotLog);
                    //StartSyncHotOrder(syncHotLog);
                    //LogForOperatorContext.Current.EndStep();

                    //if (shop.PlatformType == PlatformType.Pinduoduo.ToString())
                    //{
                    //    //拼多多，还需要去同步退款订单，回传取消发货结果，目的是协助平台做极速退款
                    //    ThreadPool.QueueUserWorkItem(state =>
                    //    {
                    //        //拼多多，还需要去同步退款订单，回传取消发货结果，目的是协助平台做极速退款
                    //        (new SyncOrderService()).PddSyncRefundOrder(new DianGuanJiaApp.Data.Model.SyncOrderParametersModel { });
                    //    });
                    //}
                }
                //TODO:检查当前登录店铺是否还在同步中，如果是同步中，不加载订单
                var isSyncing = false;// _shopService.GetShopSyncStatus(shop.Id) == "Syncing";
                if (SiteContext.Current.CurrentLoginShop.IsPddFactorer && Request.Form["requestUrl"]?.ToLower()?.Contains("indexfds") == true)
                    isSyncing = _commonSettingService.GetOrderSyncStatus(SiteContext.Current.CurrentShopId)?.LastSyncStatus == "Syncing";
                else
                    isSyncing = _shopService.GetShopSyncStatus(SiteContext.Current.CurrentShopId) == "Syncing";

                if (isSyncing)
                {
                    return OrderJson(new PagedResultModel<Order>
                    {
                        IsSyncing = true,
                        Rows = new List<Order>()
                    });
                }

                //灰度配置第一次同步历史订单解密数据（Caid、Oaid）
                if (shop.PlatformType == PlatformType.Taobao.ToString() || shop.PlatformType == PlatformType.AlibabaC2M.ToString() || shop.PlatformType == PlatformType.TaobaoMaiCaiV2.ToString())
                    SyncTaobaoWaitsellersendOrdersToDecrypt();

                var sublog = new LogForOperator() { OperatorType = "GetOrders" };
                LogForOperatorContext.Current.StartStep(sublog);
                _shopService.UpdateSyncInfoStep(SiteContext.Current.CurrentShopId, "查询订单");
                var orders = MyOrderService.GetOrders(model);
                orders.LogId = id;
                if (orders.Rows.Any())
                    sublog.Detail = orders.Rows?.Select(o => new { o.PlatformOrderId, o.PlatformStatus, o.RefundStatus, o.LastExpressPrintTime, o.ExpressPrintTimes, o.LastExpressTemplateId, o.LastNahuoPrintTime, o.LastSendPrintTime, OrderItems = o.OrderItems.Select(oi => new { oi.Status, oi.RefundStatus, oi.SubItemID, oi.LastExpressPrintTime, oi.LastFahuoPrintTime, oi.LastNahuoPrintTime }) })?.ToList();
                LogForOperatorContext.Current.EndStep();


                var platformStatus = model?.Filters?.FirstOrDefault(m => m.Name == "PlatformStatus")?.Value ?? "";
                var realCount = orders.RealOrderTotal;
                var listCount = orders.Total;
                log.Detail = new LogForSearchOrderModel() { PageSize = orders.PageSize, OrderByField = orders.OrderByField, IsOrderDesc = orders.IsOrderDesc, PlatformStatus = platformStatus, ListCount = listCount, RealCount = realCount };


                sublog = new LogForOperator() { OperatorType = "SetSellerInfo" };
                LogForOperatorContext.Current.StartStep(sublog);

                var newOrders = MyOrderService.SetSellerInfo(orders.Rows);

                LogForOperatorContext.Current.EndStep();
                //if (!isOrderSyncSameTime)
                //{
                //    ThreadPool.QueueUserWorkItem(state =>
                //    {
                //        StartSyncOrder(null);
                //        if (shop.PlatformType == PlatformType.Pinduoduo.ToString())
                //        {
                //            //拼多多，还需要去同步退款订单，回传取消发货结果，目的是协助平台做极速退款
                //            (new SyncOrderService()).PddSyncRefundOrder(new DianGuanJiaApp.Data.Model.SyncOrderParametersModel { });
                //        }
                //    });
                //}
                if ((shop.PlatformType == PlatformType.Taobao.ToString()
                    || shop.PlatformType == PlatformType.Jingdong.ToString())
                    //|| shop.PlatformType == PlatformType.Pinduoduo.ToString())
                    && orders != null && orders.Rows.Any())
                {
                    var pids = new List<string>();
                    orders.Rows.ForEach(t =>
                    {
                        if (string.IsNullOrEmpty(t.ChildOrderId))
                            pids.Add(t.PlatformOrderId);
                        else
                            pids.AddRange(t.ChildOrderId.Split(','));
                        if (shop.PlatformType == PlatformType.Jingdong.ToString())
                        {
                            t.BuyerMemberName = t.BuyerMemberName.ToEncryptName();
                            //t.BuyerWangWang = t.BuyerWangWang.ToEncryptName();
                            t.ToPhone = t.ToPhone.ToEncrytPhone();
                            t.ToMobile = t.ToMobile.ToEncrytPhone();
                            t.ToName = t.ToName.ToEncryptName();
                            t.ToAddress = t.ToAddress.ToTaoBaoEncryptAddress();
                            t.ToFullAddress = t.ToProvince + t.ToCity + t.ToCounty + t.ToAddress.ToTaoBaoEncryptAddress();
                        }
                        if (shop.PlatformType == PlatformType.Taobao.ToString())
                        {
                            t.BuyerMemberName = t.BuyerMemberName.ToEncryptName();
                            //t.BuyerWangWang = t.BuyerWangWang.ToEncryptName();
                            t.ToPhone = t.ToPhone.ToTaobaoEncrytPhone();
                            t.ToMobile = t.ToMobile.ToTaobaoEncrytPhone();
                            t.ToName = t.ToName.ToEncryptName();
                            //t.ToAddress = t.ToAddress.ToTaoBaoEncryptAddress();
                            //t.ToFullAddress = t.ToProvince + t.ToCity + t.ToCounty + t.ToAddress.ToTaoBaoEncryptAddress();
                            //淘宝平台发件人也需要加密
                            //t.SenderName = t.SenderName.ToEncryptName();
                            //t.SenderPhone = t.SenderPhone.ToEncrytPhone();
                            //t.SenderMobile = t.SenderMobile.ToEncrytPhone();
                            //t.SenderAddress = t.SenderAddress.ToTaoBaoEncryptAddress();
                        }
                    });
                    if (shop.PlatformType == PlatformType.Taobao.ToString())
                        ych_sdk.YchRequestLogger.Order(shop.Id.ToString(), "订单查询", pids);
                    else if (shop.PlatformType == PlatformType.Jingdong.ToString())
                    {
                        jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
                    }
                }
                WitespaceToHtml(orders.Rows);
                if (shop.PlatformType == PlatformType.Pinduoduo.ToString())
                {
                    orders.Rows.GroupBy(x => x.ShopId).ToList().ForEach(g =>
                    {
                        var temp = SiteContext.Current.AllShops.FirstOrDefault(y => y.Id == g.Key);
                        if (temp != null)
                        {
                            var service = new PinduoduoPlatformService(temp);
                            service.DecryptBatch(g.ToList(), true);
                        }
                    });
                    //历史未加密数据加密处理
                    orders.Rows.ForEach(t =>
                    {
                        t.ToMobile = t.ToMobile.ToEncrytPhone();
                        t.ToPhone = t.ToPhone.ToEncrytPhone();
                        t.BuyerMemberName = t.BuyerMemberName.ToEncryptName();
                        t.BuyerWangWang = t.BuyerWangWang.ToEncryptName();
                        t.ToName = t.ToName.ToEncryptName();
                        t.ToAddress = t.ToAddress.ToPddEncryptAddress();
                        t.ToFullAddress = t.ToProvince + t.ToCity + t.ToCounty + t.ToAddress.ToPddEncryptAddress();
                    });
                }
                return OrderJson(orders);
            }
        }


        /// <summary>
        /// 淘宝灰度店铺同步待发货加密订单
        /// </summary>
        private void SyncTaobaoWaitsellersendOrdersToDecrypt()
        {
            var curShop = SiteContext.Current.CurrentLoginShop;
            var versions = _commonSettingService.GetTaobaoDecryptVersion();
            if (!versions.Contains(curShop.Version.ToInt()))
                return;

            // 获取配置判断是否同步待发货历史加密订单
            var key = "/System/Taobao/ReceiverDecryptShopId";
            var val = _commonSettingService.GetString(key, curShop.Id);
            if (val == "1")
                return;

            var subLog = new LogForOperator() { OperatorType = "淘宝同步待发货加密订单" };
            LogForOperatorContext.Current.StartStep(subLog);

            var insertOrders = new List<Order>();
            var needUpdateOrders = new List<Order>();
            var orders = new List<Order>();

            if (curShop.PlatformType == PlatformType.Taobao.ToString())
            {
                var tbService = new TaobaoPlatformService(curShop);
                orders = tbService.GetWaitsellersendOaids();
            }
            else if (curShop.PlatformType == PlatformType.AlibabaC2M.ToString())
            {
                var tbService = new AlibabaC2MService(curShop);
                orders = tbService.GetWaitSellerSendOrderCaid();
            }
			else if (curShop.PlatformType == PlatformType.TaobaoMaiCaiV2.ToString())
			{
				var tbService = new TaobaoMaiCaiV2PlatformService(curShop);
				orders = tbService.GetWaitSellerSendOrderCaid();
			}

			if (orders != null && orders.Any())
            {
                var keys = orders.Select(x => new OrderSelectKeyModel { PlatformOrderId = x.PlatformOrderId, ShopId = curShop.Id }).ToList();
                var oldOrders = MyOrderService.GetOrders(keys);
                orders.ForEach(o =>
                {
                    var old = oldOrders.FirstOrDefault(x => x.PlatformOrderId == o.PlatformOrderId && x.ShopId == o.ShopId);
                    if (old != null)
                    {
                        if (o.ExtField3.IsNotNullOrEmpty())
                            old.ExtField3 = o.ExtField3;
                        needUpdateOrders.Add(old);
                    }
                });
            }
            //// 新订单增量同步时再插入即可
            //if (insertOrders.Any())
            //    _orderService.BulkMergerUpdate(insertOrders);
            if (needUpdateOrders.Any())
            {
                MyOrderService.UpdateTaobaoExtField3(needUpdateOrders);
            }

            subLog.Detail = new { SyncOrderCount = orders.Count, WaitUpdateCount = needUpdateOrders.Count, UpdateModel = needUpdateOrders.Select(x => new { x.PlatformOrderId, x.ExtField3 }) };

            // 保存配置已同步待发货历史加密订单
            _commonSettingService.Set(key, "1", curShop.Id);

            LogForOperatorContext.Current.EndStep();

        }

        public ActionResult GetProductCheckListOld()
        {
            var status = Request["PlatformStatus"].ToString2();
            var products = Request["Products"].ToString2().ToList<SimpleProduct>();// model?.Products ?? new List<SimpleProduct>();
            if (!products.Any())
                return Json(products);
            var list = MyOrderService.GetProductCheckList(products, status, SiteContext.Current.ShopIds);
            return Json(list);
        }

        public ActionResult GetProductCheckList()
        {
            var status = Request["PlatformStatus"].ToString2();
            var products = Request["Products"].ToString2().ToList<SimpleProduct>();// model?.Products ?? new List<SimpleProduct>();

            products = GetProductCheckListData(status, products);
            return Json(products);

            #region 注释
            //var status = Request["PlatformStatus"].ToString2();
            //var products = Request["Products"].ToString2().ToList<SimpleProduct>();// model?.Products ?? new List<SimpleProduct>();
            //if (products == null)
            //    products = new List<SimpleProduct>();
            //if (!products.Any())
            //    return Json(products);
            //var groups = products.GroupBy(p => p.ShopId).ToList();
            //var productService = new ProductService();
            //var pids = products.Select(p => p.ProductId).ToList();
            //var sids = products.Select(p => p.ShopId).Distinct().ToList();
            //if (sids == null || pids == null || sids.Any() == false || pids.Any() == false)
            //    return Json(products);
            //var exist = productService.GetList(sids, pids);

            //var psDict = new Dictionary<string, SimpleProduct>();
            //var notExistProductDict = new Dictionary<string, SimpleProduct>();
            //products.ForEach(p =>
            //{
            //    var key = p.ProductId + p.ShopId;
            //    if (psDict.ContainsKey(key) == false)
            //    {
            //        psDict.Add(key, p);
            //        var dbProduct = exist.FirstOrDefault(d => d.PlatformId == p.ProductId);
            //        if (dbProduct != null)
            //        {
            //            p.ProductSubject = string.IsNullOrEmpty(dbProduct.ShortTitle) ? dbProduct.Subject : dbProduct.ShortTitle;
            //            p?.Attrs?.ForEach(a =>
            //            {
            //                var sku = dbProduct.Skus.FirstOrDefault(s => s.SkuId == a.SkuId);
            //                if (sku != null)
            //                {
            //                    a.Color = sku.ProductSkuAttr?.AttributeName ?? "";
            //                    a.Size = sku.ProductSkuAttr?.AttributeValue ?? "";
            //                }
            //                else
            //                {
            //                    if (notExistProductDict.ContainsKey(key) == false)
            //                        notExistProductDict.Add(key, p);
            //                }
            //            });
            //        }
            //        else
            //        {
            //            notExistProductDict.Add(key, p);
            //        }
            //    }
            //});
            //if (notExistProductDict.Any())
            //    SetSimpleProductInfo(notExistProductDict);
            ////CheckIfProductIsNeedSync();
            //return Json(products); 
            #endregion
        }

        public List<SimpleProduct> GetProductCheckListData(string PlatformStatus, List<SimpleProduct> products)
        {
            //var status = Request["PlatformStatus"].ToString2();
            //var products = Request["Products"].ToString2().ToList<SimpleProduct>();// model?.Products ?? new List<SimpleProduct>();
            if (products == null)
                products = new List<SimpleProduct>();
            if (!products.Any())
                return products;
            var groups = products.GroupBy(p => p.ShopId).ToList();
            var productService = new ProductService();
            var pids = products.Select(p => p.ProductId).ToList();
            var sids = products.Select(p => p.ShopId).Distinct().ToList();
            if (sids == null || pids == null || sids.Any() == false || pids.Any() == false)
                return products;
            var exist = productService.GetList(sids, pids);

            var psDict = new Dictionary<string, SimpleProduct>();
            var notExistProductDict = new Dictionary<string, SimpleProduct>();
            products.ForEach(p =>
            {
                var key = p.ProductId + p.ShopId;
                if (psDict.ContainsKey(key) == false)
                {
                    psDict.Add(key, p);
                    var dbProduct = exist.FirstOrDefault(d => d.PlatformId == p.ProductId);
                    if (dbProduct != null)
                    {
                        p.ProductSubject = string.IsNullOrEmpty(dbProduct.ShortTitle) ? dbProduct.Subject : dbProduct.ShortTitle;
                        p?.Attrs?.ForEach(a =>
                        {
                            var sku = dbProduct.Skus.FirstOrDefault(s => s.SkuId == a.SkuId);
                            if (sku != null)
                            {
                                a.Color = sku.ProductSkuAttr?.AttributeName ?? "";
                                a.Size = sku.ProductSkuAttr?.AttributeValue ?? "";
                            }
                            else
                            {
                                if (notExistProductDict.ContainsKey(key) == false)
                                    notExistProductDict.Add(key, p);
                            }
                        });
                    }
                    else
                    {
                        notExistProductDict.Add(key, p);
                    }
                }
            });
            if (notExistProductDict.Any())
                SetSimpleProductInfo(notExistProductDict);
            //CheckIfProductIsNeedSync();
            return products;
        }

        public List<SimpleProduct> SetSimpleProductInfo(Dictionary<string, SimpleProduct> ps)
        {
            var gs = ps.GroupBy(p => p.Value.ShopId).ToList();
            var products = new List<Product>();
            foreach (var g in gs)
            {
                var pids = g.ToList().Select(p => p.Value.ProductId).ToList();
                var shop = SiteContext.Current.AllShops.FirstOrDefault(s => s.Id == g.Key);
                if (shop == null)
                    shop = _shopService.Get(g.Key);
                if (shop == null)
                    continue;
                var syncedProducts = new ConcurrentBag<Product>();
                Parallel.ForEach(pids, new ParallelOptions { MaxDegreeOfParallelism = 5 }, pid =>
                {
                    try
                    {
                        var service = PlatformFactory.GetPlatformService(shop);
                        var p = service.SyncProduct(pid);
                        if (p != null)
                            syncedProducts.Add(p);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"商品选择器同步单个商品时发生错误：商品ID：{pid},错误详情：{ex}");
                    }
                });
                var shoptemps = syncedProducts.ToList();
                products.AddRange(shoptemps);
                ThreadPool.QueueUserWorkItem(state =>
                {
                    try
                    {
                        var productService = new ProductService();
                        productService.BulkMerger(shoptemps, shop);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"商品选择器保存商品时发生错误：{ex}");
                    }
                });

            }
            foreach (var kv in ps)
            {
                var p = kv.Value;
                var dbProduct = products.FirstOrDefault(d => d.PlatformId == p.ProductId);
                if (dbProduct == null)
                    continue;
                p.ProductSubject = string.IsNullOrEmpty(dbProduct.ShortTitle) ? dbProduct.Subject : dbProduct.ShortTitle;
                p?.Attrs?.ForEach(a =>
                {
                    var sku = dbProduct.Skus.FirstOrDefault(s => s.SkuId == a.SkuId);
                    if (sku != null)
                    {
                        a.Color = sku.ProductSkuAttr?.AttributeName ?? "";
                        a.Size = sku.ProductSkuAttr?.AttributeValue ?? "";
                    }
                });
            }
            return ps.Values.ToList();
        }

        public void CheckIfProductIsNeedSync()
        {
            var noSyncPts = new List<string>() { PlatformType.Alibaba.ToString(), PlatformType.Taobao.ToString(), PlatformType.ZhiDian.ToString(), PlatformType.KuaiShou.ToString(), PlatformType.YouZan.ToString(), PlatformType.Offline.ToString() };
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (CustomerConfig.IsSyncDisabled || noSyncPts.Contains(pt))
                return;
            var shopList = SiteContext.Current.AllSamePlatformTypeShops;
            ThreadPool.QueueUserWorkItem(state =>
            {
                try
                {
                    var syncProductService = new SyncProductService();
                    var productStatusService = new ProductStatusService();
                    foreach (var shop in shopList)
                    {
                        var lastTime = productStatusService.LastStartSyncTime(shop);
                        //未同步过或者上次同步时间离现在超过一天
                        if (lastTime == null || lastTime < DateTime.Now.AddDays(-5))
                        {
                            syncProductService.SyncProduct(shop);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"商品选择器触发商品自动同步时发生错误：{ex}");
                }
            });
        }

        private void StartSyncOrder(LogForOperator log)
        {
            try
            {
                if (!CustomerConfig.IsSyncDisabled && SiteContext.Current.CurrentLoginShop.PlatformType != PlatformType.Offline.ToString())
                {
                    var _syncOrderService = new SyncOrderService();
                    var syncOrderParameter = new DianGuanJiaApp.Data.Model.SyncOrderParametersModel { };

                    if (SiteContext.Current.CurrentLoginShop.IsPddFactorer == true)
                        syncOrderParameter.State = Request.Form["requestUrl"] ?? "";

                    _syncOrderService.SyncOrder(syncOrderParameter);
                }
            }
            catch (Exception ex)
            {
                if (log != null)
                {
                    log.IsError = true;
                    log.Exception = ex.ToString();
                }
                Log.WriteError($"同步订单时发生错误：{ex}");
            }
        }

        //private void StartSyncHotOrder(LogForOperator log)
        //{
        //    try
        //    {
        //        var sc = SiteContext.Current;
        //        if (sc.DbConfig != null && sc.DbConfig.DbConfig.IsHotTableEnabled == true)
        //        {
        //            var syncHotOrderService = new SyncHotOrderService(sc.CurrentLoginShop, sc.ChildShop);
        //            syncHotOrderService.SyncHotOrder(new HotOrderSyncParameter() { IsFullSync = false });
        //            log.Remark = "已启用热数据，同步热数据完成";
        //        }
        //        else
        //            log.Remark = "未启用热数据，不做同步";
        //    }
        //    catch (Exception ex)
        //    {
        //        log.Remark = $"同步热数据发生错误：{ex?.Message}";
        //        log.IsError = true;
        //        log.Exception = ex.ToString();
        //        Log.WriteError($"同步热数据时发生错误：{ex}");
        //    }
        //}

        /// <summary>
        /// 多空格显示在html页面
        /// </summary>
        /// <param name="orders"></param>
        private void WitespaceToHtml(List<Order> orders)
        {
            if (orders == null || !orders.Any()) return;

            orders.ForEach(order =>
            {
                order.ToName = order.ToName.IsNullOrEmpty() ? order.ToName : order.ToName.Replace(" ", "&nbsp;");
                order.ToAddress = order.ToAddress.IsNullOrEmpty() ? order.ToAddress : order.ToAddress.Replace(" ", "&nbsp;");
                order.ToFullAddress = order.ToFullAddress.IsNullOrEmpty() ? order.ToFullAddress : order.ToFullAddress.Replace(" ", "&nbsp;");
                order.SellerRemark = order.SellerRemark.IsNullOrEmpty() ? order.SellerRemark : order.SellerRemark.Replace(" ", "&nbsp;");
                order.BuyerRemark = order.BuyerRemark.IsNullOrEmpty() ? order.BuyerRemark : order.BuyerRemark.Replace(" ", "&nbsp;");
                order.BuyerWangWang = order.BuyerWangWang.IsNullOrEmpty() ? order.BuyerWangWang : order.BuyerWangWang.Replace(" ", "&nbsp;");
                order.SenderName = order.SenderName.IsNullOrEmpty() ? order.SenderName : order.SenderName.Replace(" ", "&nbsp;");
                order.SenderAddress = order.SenderAddress.IsNullOrEmpty() ? order.SenderAddress : order.SenderAddress.Replace(" ", "&nbsp;");
                order.SenderCompany = order.SenderCompany.IsNullOrEmpty() ? order.SenderCompany : order.SenderCompany.Replace(" ", "&nbsp;");

                order.OrderItems?.ForEach(item =>
                {
                    item.ProductSubject = item.ProductSubject.IsNullOrEmpty() ? item.ProductSubject : item.ProductSubject.Replace(" ", "&nbsp;");
                    item.Color = item.Color.IsNullOrEmpty() ? item.Color : item.Color.Replace(" ", "&nbsp;");
                    item.Size = item.Size.IsNullOrEmpty() ? item.Size : item.Size.Replace(" ", "&nbsp;");
                    item.productCargoNumber = item.productCargoNumber.IsNullOrEmpty() ? item.productCargoNumber : item.productCargoNumber.Replace(" ", "&nbsp;");
                    item.CargoNumber = item.CargoNumber.IsNullOrEmpty() ? item.CargoNumber : item.CargoNumber.Replace(" ", "&nbsp;");
                    item.SkuShortTitle = item.SkuShortTitle.IsNullOrEmpty() ? item.SkuShortTitle : item.SkuShortTitle.Replace(" ", "&nbsp;");
                    item.ShortTitle = item.ShortTitle.IsNullOrEmpty() ? item.ShortTitle : item.ShortTitle.Replace(" ", "&nbsp;");
                });

                //拼多多，非待发货状态订单，不显示地址信息
                if (!CustomerConfig.PddNotWaitSendShowReciver
                && order.PlatformType == PlatformType.Pinduoduo.ToString()
                && order.PlatformStatus != OrderStatusType.waitsellersend.ToString())
                {
                    order.ToName = "";
                    order.ToPhone = "";
                    order.ToMobile = "";
                    order.ToProvince = "";
                    order.ToCity = "";
                    order.ToCounty = "";
                    order.ToAddress = "";
                    order.ToFullAddress = "";
                }
            });
        }

        public ActionResult UpdateRenderTime()
        {
            try
            {
                var ms = Request.Form["ms"].ToInt() * 1.0 / 1000;
                var logId = Request.Form["logId"].ToString2();
                LogForOperatorService _logService = new LogForOperatorService();
                var result = _logService.Update(logId, ms);
            }
            catch (Exception ex)
            {

            }
            return Json("");
        }

        public ActionResult GetWaybillCodeByOrderKey(List<OrderSelectKeyModel> keys)
        {
            if (keys == null || !keys.Any())
                return Json(new List<object>());
            var wcs = MyOrderService.GetWaybillCodeByOrderKey(keys, base.IsCustomerOrder);
            return Json(wcs);
        }

        /// <summary>
        /// 新版关联的小程序
        /// </summary>
        /// <returns></returns>
        public ActionResult ListXcxIndexShow(List<int> shopList)
        {
            var startDate = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd") + " 00:00:00";
            var endDate = DateTime.Now.ToString("yyyy-MM-dd") + " 23:59:59";

            var daiFuNumber = MyOrderService.GetOrderPlatformStatusNumber("waitbuyerpay", startDate, endDate, shopList);
            var daiFaNumber = MyOrderService.GetOrderPlatformStatusNumber("waitsellersend", startDate, endDate, shopList);
            var yiFaNumber = MyOrderService.GetOrderPlatformStatusNumber("waitbuyerreceive", startDate, endDate, shopList);
            var successNumber = MyOrderService.GetOrderPlatformStatusNumber("success", startDate, endDate, shopList);
            var jsons = new { daiFu = daiFuNumber, daiFa = daiFaNumber, yiFa = yiFaNumber, successNum = successNumber };

            return Json(jsons);
        }


        //public ActionResult SaveOrderFilter()
        //{
        //    var isCustomerOrder = Request.Form["isCustomerOrder"].ToBoolean();
        //    var shopId = Request.Form["ShopId"].ToInt();
        //    shopId = shopId == 0 ? SiteContext.Current.CurrentShopId : shopId;
        //    var result = false;
        //    var model = _orderFilterService.GetOrderFilterByShopId(shopId);
        //    if (model != null)
        //    {
        //        if (isCustomerOrder)
        //            model.CustomerConfig = Request.Form["ConfigData"].ToString2();
        //        else
        //            model.Config = Request.Form["ConfigData"].ToString2();
        //        result = _orderFilterService.Update(model);
        //    }
        //    else
        //    {
        //        model = new OrderFilter()
        //        {
        //            ShopId = shopId,
        //            CreateTime = DateTime.Now,
        //            //Priority = 1
        //        };
        //        if (isCustomerOrder)
        //            model.CustomerConfig = Request.Form["ConfigData"].ToString2();
        //        else
        //            model.Config = Request.Form["ConfigData"].ToString2();
        //        result = _orderFilterService.Add(model) > 0;
        //    }
        //    return Json(result);
        //}

        //public ActionResult GetOrderFilter()
        //{
        //    var isCustomerOrder = Request.Form["isCustomerOrder"].ToBoolean();
        //    var shopId = Request.Form["ShopId"].ToInt();
        //    shopId = shopId == 0 ? SiteContext.Current.CurrentShopId : shopId;
        //    var model = _orderFilterService.GetOrderFilterByShopId(shopId);

        //    return Json(model == null ? "" : isCustomerOrder ? model.CustomerConfig : model.Config);
        //}

        public ActionResult LockOrUnLockOrders()
        {
            var isCustomerOrder = Request.Form["isCustomerOrder"].ToBool();
            var platformOrderIds = JsonConvert.DeserializeObject<List<string>>(Request.Form["platformOrderIds"].ToString2());
            var val = Request.Form["val"].ToInt();
            var shopIdStr = Request.Form["ShopIds"].ToString2();

            var shopIds = shopIdStr.IsNullOrEmpty() ? new List<int> { SiteContext.Current.CurrentShopId } : shopIdStr.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).Select(m => m.ToInt()).ToList();
            var result = MyOrderService.LockOrUnLockOrders(platformOrderIds.Distinct().ToList(), shopIds, val, isCustomerOrder);
            return Json(result);
        }

        /// <summary>
        /// 更新订单分类项的别名
        /// </summary>
        /// <returns></returns>
        public ActionResult UpdateOrderCategoryAlias()
        {
            var result = 0;
            var json = Request.Form["Categorys"].ToString2();
            var shopIdStr = Request.Form["ShopIds"].ToString2();
            var shopIds = shopIdStr.IsNullOrEmpty() ? new List<int> { SiteContext.Current.CurrentShopId } : shopIdStr.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).Select(m => m.ToInt()).ToList();
            var Categorys = JsonConvert.DeserializeObject<List<OrderCategory>>(json);

            if (Categorys != null && Categorys.Count > 0)
            {
                shopIds.ForEach(shopId =>
                {
                    result = _commonSettingService.Set("OrderCategorySet", json, shopId);
                });
            }
            return Json(result);
        }

        /// <summary>
        /// 更新订单的分类Id
        /// </summary>
        /// <returns></returns>
        public ActionResult UpdateOrderCategoryId()
        {
            var platformOrderIds = Request.Form["PlatformOrderIds"].ToString2();
            var categoryId = Request.Form["CategoryId"].ToInt();
            var isCustomerOrder = Request.Form["isCustomerOrder"].ToBool();

            var shopIdStr = Request.Form["ShopIds"].ToString2();
            var shopIds = shopIdStr.IsNullOrEmpty() ? new List<int> { SiteContext.Current.CurrentShopId } : shopIdStr.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).Select(m => m.ToInt()).ToList();
            List<string> ids = JsonConvert.DeserializeObject<List<string>>(platformOrderIds);
            if (ids == null || ids.Count == 0)
                return FalidResult("未选择订单");

            var result = MyOrderService.UpdateOrderCategoryId(ids.Distinct().ToList(), shopIds, categoryId, IsCustomerOrder);
            return SuccessResult(result);
        }

        /// <summary>
        /// 获取区域信息
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        public ActionResult LoadAreaCodeInfo(int parentId)
        {
            var areaCodeInfoList = _areaCodeInfoService.GetTreeAreaInfoList();
            return SuccessResult(areaCodeInfoList);
        }

        /// <summary>
        /// 更新卖家备注
        /// </summary>
        /// <param name="selectKeyModel"></param>
        /// <param name="flag"></param>
        /// <param name="remark"></param>
        /// <returns></returns>  
        [LogForOperatorFilter("更新卖家备注")]
        public ActionResult UpdateOrderSellerRemark(List<OrderSelectKeyModel> selectKeyModelList, string flag, string remark, List<RemarkListModel> remarkList = null, bool isAppend = false)
        {
            if (selectKeyModelList == null || selectKeyModelList.Count == 0)
                return FalidResult("参数错误.");
            string remarkStr = string.Empty;
            var log = LogForOperatorContext.Current.logInfo;
            var orders = MyOrderService.GetOrders(selectKeyModelList);

            if (IsCustomerOrder)
            {
                var logStr = new StringBuilder();
                selectKeyModelList.ForEach(item =>
                {
                    remarkStr = remark;
                    if (isAppend)
                    {
                        if (remarkList != null)
                        {
                            remarkStr = remarkList.FirstOrDefault(f => f.Id == item.Id)?.SellerRemark + remark;
                        }
                    }
                    var order = orders?.FirstOrDefault(m => m.PlatformOrderId == item.PlatformOrderId);
                    logStr.AppendLine($"更新卖家备注==> 订单编号：{order?.PlatformOrderId}，旗帜：{order?.SellerRemarkFlag}->{flag}，备注：{order?.SellerRemark}->{remarkStr}");
                    log.Remark = logStr.ToString();
                    var updateResult = _customerOrderService.UpdateOrderSellerRemark(item, flag, remarkStr);
                });
                log.Remark = logStr.ToString();
            }
            else
            {
                //先判断平台是否支持 备注修改，不支持备注修改，须启用咱们系统的备注修改逻辑
                var remarkBackupList = new List<PlatformRemark>();
                var orderInfoList = new List<Order>();
                var supportRemarkModify = CustomerConfig.HasModifyRemarkApi_ByUpdateRemark(SiteContext.Current.CurrentLoginShop.PlatformType);
                if (supportRemarkModify == false)
                {
                    remarkBackupList = _platformRemarkService.GetList(selectKeyModelList);
                    orderInfoList = MyOrderService.GetOrders(selectKeyModelList, fields: new List<string> { "o.Id", "o.ShopId", "o.PlatformOrderId", "o.SellerRemark", "o.SellerRemarkFlag,oi.Id" });
                }

                //1.调用接口修改平台数据
                //2.更新订单卖家备注
                selectKeyModelList.ForEach(item =>
                {
                    var logStr = new StringBuilder();
                    remarkStr = remark;
                    if (isAppend)
                    {
                        if (remarkList != null)
                        {
                            remarkStr = remarkList.FirstOrDefault(f => f.Id == item.Id)?.SellerRemark + remark;
                        }
                    }
                    var order = orders?.FirstOrDefault(m => m.PlatformOrderId == item.PlatformOrderId);
                    logStr.AppendLine($"更新卖家备注==> 订单编号：{order?.PlatformOrderId}，旗帜：{order?.SellerRemarkFlag}->{flag}，备注：{order?.SellerRemark}->{remarkStr}");
                    log.Remark = logStr.ToString();
                    var updateResult = MyOrderService.UpdateOrderSellerRemark(item, flag, remarkStr);
                });
            }
            //if (updateResult)
            return SuccessResult();
            //else
            //    return FalidResult("更新备注失败.");
        }

        /// <summary>
        /// 更新订单收件人
        /// </summary>
        /// <param name="selectKeyModel"></param>
        /// <param name="flag"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        [LogForOperatorFilter("更新订单收件人")]
        public ActionResult UpdateOrderReceiver(OrderSelectKeyModel selectKeyModel, string name, string phone, string address)
        {
            if (selectKeyModel.Id == 0 || selectKeyModel.PlatformOrderId == string.Empty || selectKeyModel.PlatformOrderId == 0.ToString() || selectKeyModel.ShopId == 0)
                return FalidResult("参数错误.");


            //if (address.Split(new string[] { " " }, StringSplitOptions.RemoveEmptyEntries).Length < 4)
            //    return FalidResult("地址格式有误，省市区请用空格分隔.");
            var updateResult = true;
            var log = LogForOperatorContext.Current.logInfo;
            var order = MyOrderService.GetOrder(selectKeyModel);
            log.Remark = $"更新收件人信息：{order?.ToName}->{name}，{order?.ToMobile}->{phone}，{order?.ToFullAddress}->{address}";
            if (IsCustomerOrder)
            {
                updateResult = _customerOrderService.UpdateOrderReceiver(selectKeyModel, name, phone, address);
            }
            else
            {
                updateResult = MyOrderService.UpdateOrderReceiver(selectKeyModel, name, phone, address);
            }

            if (updateResult)
                return SuccessResult();
            else
                return FalidResult("更新失败.");
        }


        /// <summary>
        /// 更新订单发件人
        /// </summary>
        /// <param name="selectKeyModel"></param>
        /// <param name="flag"></param>
        /// <param name="remark"></param>
        /// <returns></returns>              
        [LogForOperatorFilter("更新订单发件人")]
        public ActionResult UpdateOrderSeller(List<OrderSelectKeyModel> selectKeyModelList, string companyName, string name, string mobile, string phone, string address)
        {
            //if (selectKeyModel.Id == 0 || selectKeyModel.PlatformOrderId == string.Empty || selectKeyModel.PlatformOrderId == 0.ToString() || selectKeyModel.ShopId == 0)
            //    return FalidResult("参数错误.");

            //if (address.Split(new string[] { " " }, StringSplitOptions.RemoveEmptyEntries).Length < 4)
            //    return FalidResult("地址格式有误，省市区请用空格分隔.");
            var log = LogForOperatorContext.Current.logInfo;
            log.Remark = $"更新订单发件人==> companyName：{companyName}，name：{name}，mobile：{mobile}，phone：{phone}，address：{address}";

            if (IsCustomerOrder)
            {
                selectKeyModelList.ForEach(item =>
                {
                    var updateResult = _customerOrderService.UpdateOrderSeller(item, companyName, name, mobile, phone, address);
                });
            }
            else
            {
                selectKeyModelList.ForEach(item =>
                {
                    var updateResult = MyOrderService.UpdateOrderSeller(item, companyName, name, mobile, phone, address);
                });
            }
            //if (updateResult)
            return SuccessResult();
            //else
            //    return FalidResult("更新失败.");
        }

        /// <summary>
        /// 订单保价金额和代收货款
        /// </summary>
        /// <param name="selectKeyModel"></param>
        /// <param name="flag"></param>
        /// <param name="remark"></param>
        /// <returns></returns> 
        [LogForOperatorFilter("修改保价金额和代收货款")]
        public ActionResult UpdateCodAmountOrInSureAmount(List<OrderSelectKeyModel> selectKeyModelList, string type, decimal? amount, string condition, decimal? when, decimal? then)
        {
            var log = LogForOperatorContext.Current.logInfo;

            if (IsCustomerOrder)
            {
                var orders = _customerOrderService.GetOrders(selectKeyModelList);
                var logStr = new StringBuilder();
                selectKeyModelList.ForEach(item =>
                {
                    var order = orders?.FirstOrDefault(m => m.PlatformOrderId == item.PlatformOrderId);
                    var str = GetCoodAmountOrInSureAmountStr(order, type.ToString2(), amount, condition, when, then);
                    logStr.AppendLine($"修改保价金额和代收货款==> 订单编号：{order?.PlatformOrderId ?? ""}，condition：{condition.ToString2()}，type：{type.ToString2()}，{str}");

                });
                log.Remark = logStr.ToString();
                _customerOrderService.UpdateCodAmountOrInSureAmount(selectKeyModelList, type, amount, condition, when, then);
            }
            else
            {
                var orders = MyOrderService.GetOrders(selectKeyModelList);
                var logStr = new StringBuilder();
                selectKeyModelList.ForEach(item =>
                {
                    var order = orders?.FirstOrDefault(m => m.PlatformOrderId == item.PlatformOrderId);
                    var str = GetCoodAmountOrInSureAmountStr(order, type, amount, condition, when, then);
                    logStr.AppendLine($"修改保价金额和代收货款==> 订单编号：{order?.PlatformOrderId}，condition：{condition}，type：{type}，{str}");

                });
                log.Remark = logStr.ToString();
                MyOrderService.UpdateCodAmountOrInSureAmount(selectKeyModelList, type, amount, condition, when, then);
            }
            return SuccessResult();
        }

        private string GetCoodAmountOrInSureAmountStr(Order order, string type, decimal? amount, string condition, decimal? when, decimal? then)
        {
            var result = string.Empty;
            switch (condition)
            {
                case "assign_amount": //指定值
                    if (type.ToLower() == "cod")
                        result = $" IsSvcCOD：{order.IsSvcCOD}->{(amount.HasValue && amount.Value > 0)}，SvcCODAmount：{order?.SvcCODAmount}->{amount}";
                    else if (type.ToLower() == "insure")
                        result = $" IsSvcInsure：{order.IsSvcInsure}->{(amount.HasValue && amount.Value > 0)}，SvcInsureAmount：{order?.SvcCODAmount}->{amount}";
                    else if (type.ToLower() == "weight")
                        result = $" IsSvcInsure：{order.TotalWeight}->{amount}";
                    break;
                case "order_amount": //订单金额
                    if (type.ToLower() == "cod")
                        result = $" IsSvcCOD：{order?.IsSvcCOD}->1，SvcCODAmount：{order?.SvcCODAmount}->{order?.TotalAmount}";
                    else if (type.ToLower() == "insure")
                        result = $" IsSvcInsure：{order?.IsSvcInsure}->1，SvcInsureAmount：{order?.SvcInsureAmount}->{order?.TotalAmount}";
                    break;
                case "less": //订单金额小于某个值
                    if (type.ToLower() == "cod")
                        result = $" IsSvcCOD：{order?.IsSvcCOD}->1，SvcCODAmount：{order?.SvcCODAmount}->{(order?.TotalAmount < when ? then.ToString2() : "NULL")}";
                    else if (type.ToLower() == "insure")
                        result = $" IsSvcInsure：{order?.IsSvcCOD}->1，SvcInsureAmount：{order?.SvcInsureAmount}->{(order?.TotalAmount < when ? then.ToString2() : "NULL")}";
                    break;
                case "than": //订单金额大于某个值
                    if (type.ToLower() == "cod")
                        result = $" IsSvcInsure：{order?.IsSvcCOD}->1，SvcCODAmount：{order?.SvcCODAmount}->{(order?.TotalAmount > when ? then.ToString2() : "NULL")}";
                    else if (type.ToLower() == "insure")
                        result = $" IsSvcInsure：{order?.IsSvcCOD}->1，SvcInsureAmount：{order?.SvcInsureAmount}->{(order?.TotalAmount > when ? then.ToString2() : "NULL")}";
                    break;
                default:
                    break;
            }
            return result;
        }

        /// <summary>
        /// 确认订单的打印状态
        /// </summary>
        /// <param name="dataList">待确认的订单信息</param>
        /// <param name="isPrinted">是否已打印</param>
        /// <returns></returns> 
        [LogForOperatorFilter("确认订单的打印状态")]
        public ActionResult ConfirmOrderPrintStatu(List<PrintHisotoryCallbackRequestModel> modelList, bool isPrinted)
        {
            if (isPrinted == false)
            {
                //未打印
                var printHistoryIndexModelList = new List<PrintHistoryIndexModel>();
                modelList.ForEach(item =>
                {
                    printHistoryIndexModelList.AddRange(item.PrintHistoryIndexModels);
                });
                var result = _printHistoryService.Delete(printHistoryIndexModelList.Select(f => f.Id).ToList());
                if (result == true)
                    return SuccessResult();
                else
                    return FalidResult("保存失败");
            }
            else
            {
                //已打印
                modelList.ForEach(item =>
                {
                    PrintCallback(item);
                });
                return SuccessResult();
            }
        }


        #region 快递单 按快递类型划分打印
        /// <summary>
        /// 获取打印电子面单的数据配置
        /// </summary>
        /// <param name="context"></param>
        [LogForOperatorFilter("打印快递单")]
        public ActionResult ExpressPrint(ExpressPrintRequestModel model)
        {
            try
            {
                //TODO 验证数据是否合法
                if (model == null)
                    throw new LogicException("数据有误，请刷新重试");
                if (model.TemplateId <= 0)
                    throw new LogicException("请先选择快递模板再打印");
                if (model.Orders == null || !model.Orders.Any())
                    throw new LogicException("请选择您要打印的订单");
                //1. 获取菜鸟面单授权信息、网点信息
                //2. 获取模板配置信息：商家自定义区域
                //3. 获取订单数据
                //4. 循环订单数据 
                //5. 获取最后打印的值，将这个值作为菜鸟包裹ID,根据打印方式确认
                //6. 生成打印数据（循环打印次数）
                //6.1 获取单号
                var tuple = MyOrderService.GetWaybillCode(model,new List<LogicOrder>());

                //日志开始
                var subLog_getExpressPrintData = LogForOperatorContext.Current.StartStep(new LogForOperator()
                {
                    OperatorType = "生成前端打印机所需数据"
                });
                //subLog_getExpressPrintData.Request = tuple.Item1;
                //6.2 生成模板数据
                var result = MyOrderService.GetExpressPrintData(model, tuple.Item1);

                subLog_getExpressPrintData.Response = result;
                //日志结束
                LogForOperatorContext.Current.EndStep(subLog_getExpressPrintData);

                //预览不记录打印记录
                if (model.IsPreview == false)
                {
                    //日志开始
                    var subLog_writelog = LogForOperatorContext.Current.StartStep(new LogForOperator()
                    {
                        OperatorType = "保存打印记录"
                    });

                    var hs = MyOrderService.LogExpressPrintHistory(tuple.Item1, model, tuple.Item2);

                    //日志结束
                    subLog_writelog.Response = hs;
                    LogForOperatorContext.Current.EndStep(subLog_writelog);

                    //result.PrintHistoryIds = hs?.Where(h => h.ID > 0)?.Select(h => h.ID).ToList();
                    result.PrintHistoryIds = hs?.Where(h => h.ID > 0)?.Select(h => new PrintHistoryIndexModel()
                    {
                        Id = h.ID,
                        Pid = h.PlatformOrderId,
                        WaybillCode = h.ExpressWaybillCode,
                        WaybillCodeChild = h.ExpressWaybillCodeChild,
                        BatchIndex = h.BatchIndex
                    }).ToList();
                }

                var logInfo = LogForOperatorContext.Current.logInfo;
                logInfo.TotalCount = result?.totalPrintNum ?? 0;
                logInfo.SuccessCount = result?.successCount ?? 0;
                logInfo.Exception = logInfo.SuccessCount < logInfo.TotalCount ? "Error" : "";
                return Json(result);
            }
            catch (Exception ex)
            {
                var errorMessage = $"打印快递单时发生错误：{ex}";
                Log.WriteError(errorMessage);
                if (LogForOperatorContext.Current?.logInfo != null)
                    LogForOperatorContext.Current.logInfo.Exception = errorMessage;
                var requestBatch = model.RequestBatchNumber?.Split('/')?.First();
                if (ex is LogicException)
                {
                    var logicEx = ex as LogicException;
                    if (logicEx == null)
                    {
                        if (LogForOperatorContext.Current?.logInfo != null)
                            LogForOperatorContext.Current.logInfo.ErrorCode = "PrintError";
                        return Json(new AjaxResult() { Message = "程序异常,请联系我们.", ErrorCode = "500", RequestBatch = requestBatch });
                    }
                    else
                    {
                        return Json(new AjaxResult() { Message = logicEx.Message, ErrorCode = string.IsNullOrWhiteSpace(logicEx.ErrorCode) ? "500" : logicEx.ErrorCode, RequestBatch = requestBatch });
                    }
                }
                else
                {
                    if (LogForOperatorContext.Current?.logInfo != null)
                        LogForOperatorContext.Current.logInfo.ErrorCode = "PrintError";
                    return Json(new AjaxResult() { Message = "程序异常,请联系我们.", ErrorCode = "500", RequestBatch = requestBatch });
                }
            }
        }


        #region 记录打印记录信息

        /// <summary>
        /// 打印完成后回调：包括快递单、发货单、拿货单打印，
        /// </summary>
        /// <param name="logs"></param>
        /// <returns></returns>
        [LogForOperatorFilter("更改订单打印状态")]
        public ActionResult PrintCallback(PrintHisotoryCallbackRequestModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = MyOrderService.LogPrintHistoryConfirmed(model, fxUserId);
            LogForOperatorContext.Current.logInfo.TotalCount = model?.Orders?.Count ?? 0;
            LogForOperatorContext.Current.logInfo.SuccessCount = model?.Orders?.Count ?? 0;

            return SuccessResult(result);
        }

        #endregion

        #region 更新预览标志

        /// <summary>
        /// 预览成功后回写预览标志
        /// </summary>
        /// <param name="logs"></param>
        /// <returns></returns>
        public ActionResult UpdateIsPreviewFlag(List<OrderSelectKeyModel> selectKeyModelList)
        {
            if (selectKeyModelList == null || selectKeyModelList.Count == 0)
                return FalidResult("参数错误，后台未收到OrderSelectKeyModel参数信息");
            if (base.IsCustomerOrder == true)
                _customerOrderService.UpdateOrderIsPreviewedFlag(selectKeyModelList);
            else
                MyOrderService.UpdateOrderIsPreviewedFlag(selectKeyModelList);
            return SuccessResult();
        }

        #endregion

        #endregion

        #region 发货单

        #endregion

        #region 发货
        [LogForOperatorFilter("批量发货")]
        public ActionResult OnlineSend(OnlineSendRequestModel model)
        {
            LogForOperatorContext.Current.logInfo.Request = model;
            //排除退款的订单订单项
            if (model.ExcludeOrders != null && model.ExcludeOrders.Any())
            {
                var temps = new List<OrderRequestModel>();
                model.ExcludeOrders.ForEach(r =>
                {
                    var cur = model.Orders.FirstOrDefault(x => x.Id == r.Id);
                    if (cur != null)
                    {
                        cur.OrderItems = cur.OrderItems.Where(y => !r.RefundItems.Contains(y))?.ToList();
                        if (cur.OrderItems != null && cur.OrderItems.Any())
                            temps.Add(cur);
                    }
                });
                model.Orders = temps;
            }
            if (model.Orders == null || !model.Orders.Any())
                throw new LogicException("当前选中的订单都不是待发货状态，已取消发货");
            var logInfo = LogForOperatorContext.Current.logInfo;
            //logInfo.Detail =new LogForOnlineSendDetailModel(model);
            logInfo.TotalCount = model.Orders.Count();
            var results = MyOrderService.OnlineSend(model);
            logInfo.SuccessCount = results.Count(r => r.IsSuccess);
            logInfo.Exception = logInfo.SuccessCount < logInfo.TotalCount ? "Error" : "";
            //ThreadPool.QueueUserWorkItem(state =>
            //{
            //    try
            //    {
            //        _mergerOrderService.SplitOrderWhenPlatformStatusNotSame();
            //    }
            //    catch (Exception ex)
            //    {
            //        Log.WriteError($"发货后检查拆单时发生错误：{ex}");
            //    }
            //});
            var result = new DeliverySendResponseModel
            {
                Orders = results,
                SuccessCount = results?.Where(r => r.IsSuccess).Count(),
                ErrorCount = results?.Where(r => !r.IsSuccess || !string.IsNullOrEmpty(r.ErrorMessage)).Count(),
                HasSendPreCheckError = results?.Any(f => f.ErrorCode == "SendPreCheckError") ?? false
            };
            LogForOperatorContext.Current.logInfo.Response = result;
            return Json(result);
        }

        [LogForOperatorFilter("批量发货Api")]
        public ActionResult OnlineSendApi(OnlineSendRequestModel model)
        {
            var sid = model.Orders.First().ShopId;
            var shop = SiteContext.Current.AllShops.FirstOrDefault(f => f.Id == sid);
            if (shop == null || shop.Id == SiteContext.Current.CurrentShopId)
                return OnlineSend(model);
            else
                return OnlineSendApiDo(model, sid, shop.PlatformType);
        }

        private ActionResult OnlineSendApiDo(OnlineSendRequestModel model, int sid, string pt)
        {
            var api = "/OrderApi/OnlineSend";

            var apiUrl = GetApiUrl(pt, api);

            var apiResult = Common.PostApi<OnlineSendRequestModel, AjaxResult>(apiUrl, sid, model, "通过Api发货");

            if (apiResult != null)
                return new CustomOrderJsonResult(apiResult);
            else
                return FalidResult("回调出错");
        }


        #endregion


        #region 取消单号回传
        [LogForOperatorFilter("取消单号回传")]
        public ActionResult CancelSend(OnlineSendRequestModel model)
        {
            LogForOperatorContext.Current.logInfo.Request = model;

            var logInfo = LogForOperatorContext.Current.logInfo;
            //logInfo.Detail =new LogForOnlineSendDetailModel(model);
            logInfo.TotalCount = model.Orders.Count();
            var results = MyOrderService.CancelReturn(model);
            logInfo.SuccessCount = results.Count(r => r.IsSuccess);
            logInfo.Exception = logInfo.SuccessCount < logInfo.TotalCount ? "Error" : "";

            var result = new DeliverySendResponseModel
            {
                Orders = results,
                SuccessCount = results?.Where(r => r.IsSuccess).Count(),
                ErrorCount = results?.Where(r => !r.IsSuccess || !string.IsNullOrEmpty(r.ErrorMessage)).Count()
            };
            LogForOperatorContext.Current.logInfo.Response = result;
            return Json(result);
        }

        #endregion


        #region 合并订单与拆分订单

        /// <summary>
        /// 合并订单
        /// </summary>
        /// <param name="orderRequest">请求合并的的订单数据：包括收件人发件人地址信息</param>
        /// <param name="selectedOrders">选中的订单</param>
        /// <returns></returns>
        [LogForOperatorFilter("手动合并订单")]
        public ActionResult MergerOrder(OrderRequestModel orderRequest, List<OrderSelectKeyModel> selectedOrders)
        {
            if (orderRequest == null)
                throw new LogicException("合并后的地址信息不能为空");
            if (selectedOrders == null)
                throw new LogicException("请选择您要合并的订单");
            var orders = MyOrderService.GetOrders(selectedOrders);
            if (orders == null || !orders.Any())
                throw new LogicException("没有查询到您选择的订单信息，请刷新重试");
            if (orders.Count() > 50)
                throw new LogicException("单次合并不能超过50个订单，请减少选择的订单后再操作");
            var lockedCount = orders.Count(x => x.IsLocked == "1");
            if (lockedCount > 0)
                throw new LogicException($"您选择的订单中有【{lockedCount}】个订单已经被锁定了，请解锁后再进行合并操作。");
            var status = new List<string> { OrderStatusType.waitbuyerpay.ToString(), OrderStatusType.waitsellersend.ToString() };
            var count = orders.Count(o => !status.Contains(o.PlatformStatus));
            if (count > 0)
                throw new LogicException($"您选择的订单中，有{count}个订单不是待付款或待发货状态，不能进行合并");
            ////头条订单，代收货款订单和普通的订单不能合并
            //var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            //if (pt == PlatformType.TouTiao.ToString())
            //{
            //    if (orders.Any(x => x.TradeType == "0") && orders.Any(x => x.TradeType != "0"))
            //        throw new LogicException("您选择的订单包含了【代收货款】订单和普通订单，不同支付方式的订单不能合并在一起。");
            //}
            ////自动拆单，如果合并的子订单地址不一样会被自动拆出，所以此处手工合并增加限制
            //if (orders.GroupBy(x => x.BuyerHashCode).Count() != 1)
            //    throw new LogicException($"您选择的订单收件地址信息不一样，不允许合并。");
            //检查是否已经自动合并过
            //若是自动合并过，需先拆单：只将当前选择的单从合并订单中拆出。
            //若不曾合并过，直接合并，IsMergeredByHand=true
            var originalOrders = orders.Where(o => o.IsMergered == true).ToList();
            if (originalOrders != null && originalOrders.Any())
            {
                var _syncOrderService = new SyncOrderService();
                originalOrders.ForEach(o =>
                {
                    try
                    {
                        _syncOrderService.SplitOrderFromMergeredOrder(new OrderSelectKeyModel
                        {
                            PlatformOrderId = o.MergeredOrderId,
                            ShopId = o.ShopId
                        }, new OrderSelectKeyModel
                        {
                            PlatformOrderId = o.PlatformOrderId,
                            ShopId = o.ShopId
                        });
                    }
                    catch
                    {

                    }

                });
            }
            new MergerOrderService().MergerOrderByHand(orderRequest, orders);
            return SuccessResult();
        }

        /// <summary>
        /// 从合并订单中拆出订单
        /// </summary>
        /// <returns></returns>               
        [LogForOperatorFilter("合并订单拆单")]
        public ActionResult SplitOrderFromMergeredOrder(OrderSelectKeyModel mainOrder, OrderSelectKeyModel toSplitOrder)
        {
            if (mainOrder == null || toSplitOrder == null)
                throw new LogicException("参数有误，请刷新重试");

            var log = LogForOperatorContext.Current.logInfo;
            var order = MyOrderService.GetOrder(mainOrder);
            log.Remark = $"合并订单：{order?.PlatformOrderId}，ChildOrderId：【{order?.ChildOrderId}】=> 拆出订单【{toSplitOrder.PlatformOrderId}】";
            new SyncOrderService().SplitOrderFromMergeredOrder(mainOrder, toSplitOrder, true);
            return SuccessResult();
        }
        #endregion

        #region 订单同步

        public ActionResult Sync()
        {
            StartSyncOrder(null);
            return SuccessResult();
        }

        public ActionResult SyncSingleOrder(OrderRequestModel request)
        {
            var shop = SiteContext.Current.AllShops.FirstOrDefault(s => s.Id == request.ShopId);
            if (shop == null)
                throw new LogicException($"无法同步此订单，请检查当前订单的店铺关联是否已被取消");
            if (string.IsNullOrEmpty(request.ChildOrderId))
                new SyncOrderService().SyncSingleOrder(request.PlatformOrderId, shop);
            else
            {
                var cids = request.ChildOrderId.Split(',');
                cids.ToList().ForEach(p =>
                {
                    new SyncOrderService().SyncSingleOrder(p, shop);
                });
            }
            return SuccessResult();
        }

        #endregion

        #region 自由打印相关

        public ActionResult FreePrintList(int h = 0)
        {
            var curShop = SiteContext.Current.CurrentLoginShop;
            var sender = _sellerInfoService.GetDefaultSeller(curShop.Id);
            ViewBag.DefaultSender = JsonExtension.ToJson(sender ?? new SellerInfo());
            if (h == 1)
                ViewBag.CurrentTab = "history";
            else if (h == 2)
                ViewBag.CurrentTab = "sendHistory";
            else
                ViewBag.CurrentTab = "list";

            //加载常用模板
            var templateList = _printTemplateService.LoadTemplateList(new List<int> { SiteContext.Current.ExpressTemplateShopId }, true);
            ViewBag.StapleTempalteList = templateList.ToJson();

            //前端打印分批发送订单数量
            ViewBag.OrderPrintBatchNumber = GetOrderPrintBatchNumber();

            //传统面单 单号 递增 ，递减 ，当前单号
            var tradional_waybillcode_config = _commonSettingService.GetString(TradionalWaybillCodeConfigKey, SiteContext.Current.CurrentShopId);
            ViewBag.TradionalWaybillCodeConfig = string.IsNullOrWhiteSpace(tradional_waybillcode_config) ? "{}" : tradional_waybillcode_config;

            var printExpressAllRspLog = _commonSettingService.GetBool(PrintExpressAllRspLogKey, curShop.Id);
            ViewBag.PrintExpressAllRspLog = printExpressAllRspLog ? 1 : 0;
            //前台获取导出任务
            var exportTask = _exportTaskService.GetExportTask(curShop.Id, ExportType.CustomerOrder.ToInt());
            // 显示未完成任务或已完成一天前的导出任务
            exportTask = exportTask != null && ((exportTask.Status >= 0 && exportTask.Status < 4) || (exportTask.Status >= 4 && exportTask.UploadToServerTime != null && DateTime.Now < exportTask.UploadToServerTime.Value.AddDays(1))) ? exportTask : null;
            var task = GetExportTaskToWeb(exportTask);
            ViewBag.FreePrintExportTask = task?.ToJson() ?? "null";
            //加载默认配置信息
            LoadDefaultConfig(true);

            //读取 用户上一次 地址是“选”还是“填”
            var addressInputModelConfig = _commonSettingService.GetString("FreePrint/AddrssInputModel", SiteContext.Current.CurrentShopId);
            ViewBag.AddressInputModelConfig = string.IsNullOrWhiteSpace(addressInputModelConfig) ? "{}" : addressInputModelConfig;

            return View();
        }

        #endregion

        #region 可派送查询

        /// <summary>
        /// 检查快递是否派送到收件地址
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult CheckReachable(CheckAddressReachableRequestModel model)
        {
            if (true || model == null || string.IsNullOrEmpty(model.ExpressCompanyCode) || model.Orders == null || !model.Orders.Any() || SiteContext.Current.CurrentLoginShop.PlatformType == PlatformType.Pinduoduo.ToString())
                model?.Orders?.ForEach(o => o.IsReachable = true);
            else
            {
                var comm = new CommService();
                comm.CheckReachableByApi(model.Orders, model.ExpressCompanyCode);
            }
            return SuccessResult(model.Orders.Select(o => new { o.Id, o.IsReachable }));
        }

        #endregion

        #region 自由打印订单处理

        /// <summary>
        /// 删除自由打印的订单
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [LogForOperatorFilter("标记删除")]
        public ActionResult SetAsDeleted(List<int> ids)
        {
            _customerOrderService.SetAsDeleted(ids);
            return SuccessResult();
        }
        /// <summary>
        /// 将订单设置为已发货
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [LogForOperatorFilter("标记发货")]
        public ActionResult SetAsSended(List<int> ids)
        {
            _customerOrderService.SetAsSended(ids);
            return SuccessResult();
        }

        #endregion

        #region 订单导出   
        [LogForOperatorFilter("订单导出")]
        public ActionResult ExportExcel(string settingOptions)
        {
            var curShop = SiteContext.Current.CurrentLoginShop;
            var task = _exportTaskService.GetExportTask(curShop.Id, ExportType.Order.ToInt());
            if (task != null && task.Status >= 0 && task.Status < 4)
                return FalidResult("已存在订单导出任务，如需重新导出，请先取消再创建新导出任务", GetExportTaskToWeb(task));

            var log = LogForOperatorContext.Current.logInfo;
            string options = Request.Form["options"].ToString2();
            options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
            options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果
            OrderSearchModel model = JsonExtension.ToObject<OrderSearchModel>(options) ?? new OrderSearchModel();

            #region 订单编号和运单号批量查询数量判断
            var platformOrderIdFilter = model.Filters.FirstOrDefault(x => x.CustomQuery == "PlatformOrderIdSearch" || x.CustomQuery == "CustomerPlatformOrderIdSearch");
            if (platformOrderIdFilter != null && !platformOrderIdFilter.Value.IsNullOrEmpty())
            {
                if (platformOrderIdFilter.Value.SplitWithString(",").Length > 1000)
                    return FalidResult("查询的订单请不要超过1000");
            }
            var waybillCodeFilter = model.Filters.FirstOrDefault(x => x.CustomQuery == "WaybillCode");
            if (waybillCodeFilter != null && !waybillCodeFilter.Value.IsNullOrEmpty())
            {
                if (waybillCodeFilter.Value.SplitWithString(",").Length > 1000)
                    return FalidResult("查询的运单号请不要超过1000");
            }
            #endregion

            #region 导出查询时间判断
            if (platformOrderIdFilter == null && waybillCodeFilter == null)
            {
                var timeFilter = model.Filters.FirstOrDefault(x => x.FieldType.ToString2().ToLower() == "datetime");
                if (timeFilter == null)
                    return FalidResult("必须包含时间查询");
                var startTime = timeFilter.Value.ToDateTime();
                var endTime = timeFilter.ExtValue.ToDateTime();
                if (startTime == null || endTime == null)
                    return FalidResult("请填写正确的起止时间");
                if ((endTime.Value - startTime.Value).TotalDays > 45)
                    return FalidResult("起止时间范围不能超过45天");
            }
            #endregion

            var setting = JsonExtension.ToObject<ExportSetting>(settingOptions) ?? new ExportSetting();
            var exceptSetting = setting?.CheckedItems?.Where(m => !m.Value.IsNullOrEmpty() && m.Value.StartsWith("Export_Except")).ToList();
            setting?.CheckedItems?.RemoveAll(m => !m.Value.IsNullOrEmpty() && m.Value.StartsWith("Export_Except"));
            if (setting == null || setting.CheckedItems == null || setting.CheckedItems.Count == 0)
            {
                log.Exception = "导出设置不能为空";
                return FalidResult("导出设置不能为空");
            }

            var hasExceptLockOrRefundSetting = false;
            // 根据配置是否排除指定项
            if (exceptSetting != null && exceptSetting.Any())
            {
                exceptSetting.ForEach(item =>
                {
                    if (item.Value == "Export_Except_LockOrder" || item.Value == "Export_Except_RefundOrder")
                        hasExceptLockOrRefundSetting = true;

                    if (item.Value == "Export_Except_LockOrder")
                        model.Filters.Add(new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "IsLocked", Value = "1", FieldType = "int", Contract = "!=" });
                    else if (item.Value == "Export_Except_RefundOrder")
                        model.Filters.Add(new OrderSearchFieldModel() { TableAlias = "oi", TableName = "P_OrderItem", Name = "RefundStatus", Value = "", FieldType = "string", Contract = "=", CustomQuery = "NoReturnOrder" });
                });
            }

            var commSets = _commonSettingService.GetSets(new List<string> { "ExportByTaskOrderCountLimit", "ExportDefaultPageSize" }, 0);
            var limitCountSet = commSets?.FirstOrDefault(m => m.Key == "ExportByTaskOrderCountLimit");
            var pageSizeSet = commSets?.FirstOrDefault(m => m.Key == "ExportDefaultPageSize");
            var limitCount = limitCountSet?.Value.ToInt() ?? CustomerConfig.ExportByTaskWaybillCodeCountLimit;
            limitCount = limitCount > 0 ? limitCount : CustomerConfig.ExportByTaskWaybillCodeCountLimit;
            var pageSize = pageSizeSet?.Value.ToInt() ?? 1000;
            pageSize = pageSize > 0 ? pageSize : 1000;

            var logDetail = new LogForExportDetailModel();
            IWorkbook workbook;
            var fileName = ExcelHelper.GetFileName("打印单.xlsx", Request);
            var orders = new List<Order>();
            model.NeedPagging = true;
            model.PageIndex = 1;
            model.PageSize = pageSize;
            var returnCount = model.PageSize;
            var searchModelJson = model.ToJson();
            var searchModel = searchModelJson.ToObject<OrderSearchModel>(); //内部方法修改后可能导致查询异常，需要重新序列化
            if (model.IsCustomerOrder)
            {
                // 设置默认查询店铺
                var shopFilter = model.Filters.FirstOrDefault(m => m.Name.ToString2().ToLower() == "shopid");
                var shopIds = new List<int>();
                if (shopFilter != null)
                    shopIds = shopFilter.Value.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).ToList().ConvertAll(m => m.ToInt());
                else
                {
                    shopIds = SiteContext.Current.SamePlatformShopIds;
                    shopFilter = new OrderSearchFieldModel() { TableAlias = "o", FieldType = "int", TableName = "P_CustomerOrder", Name = "ShopId", Value = string.Join(",", shopIds), Contract = "in" };
                    model.Filters.Add(shopFilter);
                }

                // 查第一页数据，获取到满足条件的总订单数量，判断是否需要使用任务方式导出
                var firstPageModel = _customerOrderService.GetOrders(searchModel);
                returnCount = firstPageModel.Rows.Count;
                if (firstPageModel.Total > limitCount)
                {
                    //使用任务方式导出
                    var newTask = new ExportTask
                    {
                        IP = Request.UserHostAddress,
                        CreateTime = DateTime.Now,
                        PlatformType = curShop.PlatformType,
                        ShopId = curShop.Id,
                        UserId = SiteContext.Current.CurrentLoginSubUser?.Id.ToString(),
                        Status = 0,
                        Type = ExportType.CustomerOrder.ToInt(),
                        PageIndex = 1,
                        PageSize = pageSize,
                        TotalCount = firstPageModel.Total,
                        ParamJson = model.ToJson(),
                        FromModule = "自由打印--Excel导出",
                    };
                    newTask.Id = _exportTaskService.Add(newTask);
                    return SuccessResult("导出任务创建成功", GetExportTaskToWeb(newTask));
                }

                // 直接导出方式进行导出，第一页 
                firstPageModel?.Rows.ForEach(row =>
                {
                    var order = row as Order;
                    order.CustomerOrderId = row.CustomerOrderId;
                    row.OrderItems.ForEach(oi =>
                    {
                        order.OrderItems.Add(oi as OrderItem);
                    });
                    orders.Add(order);
                });
                model.PageIndex++;
                if (model.PageSize == returnCount)
                {
                    //第二页之后
                    while (model.PageSize == returnCount)
                    {
                        searchModel = searchModelJson.ToObject<OrderSearchModel>();
                        searchModel.PageIndex = model.PageIndex;
                        var pageModel = _customerOrderService.GetOrders(searchModel);
                        pageModel?.Rows.ForEach(row =>
                        {
                            var order = row as Order;
                            order.CustomerOrderId = row.CustomerOrderId;
                            row.OrderItems.ForEach(oi =>
                            {
                                order.OrderItems.Add(oi as OrderItem);
                            });
                            orders.Add(order);
                        });
                        model.PageIndex++;
                        returnCount = pageModel.Rows.Count;
                    }
                }
            }
            else
            {
                // 查第一页数据，获取到满足条件的总订单数量，判断是否需要使用任务方式导出
                var firstPageModel = MyOrderService.GetOrders(searchModel);
                returnCount = firstPageModel.Rows.Count;
                if (firstPageModel.RealOrderTotal > limitCount)
                {
                    //使用任务方式导出
                    var newTask = new ExportTask
                    {
                        IP = Request.UserHostAddress,
                        CreateTime = DateTime.Now,
                        PlatformType = curShop.PlatformType,
                        ShopId = curShop.Id,
                        UserId = SiteContext.Current.CurrentLoginSubUser?.Id.ToString(),
                        Status = 0,
                        Type = ExportType.Order.ToInt(),
                        PageIndex = 1,
                        PageSize = pageSize,
                        TotalCount = firstPageModel.RealOrderTotal,
                        ParamJson = model.ToJson(),
                        FromModule = "订单打印--Excel导出",
                    };
                    newTask.Id = _exportTaskService.Add(newTask);
                    return SuccessResult("导出任务创建成功", GetExportTaskToWeb(newTask));
                }

                // 直接导出方式进行导出，第一页  
                model.PageIndex++;
                if (firstPageModel?.Rows?.Any() != null)
                    orders.AddRange(firstPageModel.Rows);

                if (model.PageSize == returnCount)
                {
                    //第二页之后
                    while (model.PageSize == returnCount)
                    {
                        searchModel = searchModelJson.ToObject<OrderSearchModel>();
                        searchModel.PageIndex = model.PageIndex;
                        var pageModel = MyOrderService.GetOrders(searchModel);
                        returnCount = pageModel.Rows.Count;
                        if (pageModel?.Rows?.Any() != null)
                            orders.AddRange(pageModel.Rows);
                        model.PageIndex++;
                    }
                }

                var pids = new List<string>();
                var shop = SiteContext.Current.CurrentLoginShop;
                orders.ForEach(t =>
                {
                    if (string.IsNullOrEmpty(t.ChildOrderId))
                        pids.Add(t.PlatformOrderId);
                    else
                        pids.AddRange(t.ChildOrderId.Split(','));
                    if (shop.PlatformType == PlatformType.Jingdong.ToString())
                    {
                        t.ToPhone = t.ToPhone.ToEncrytPhone();
                        t.ToMobile = t.ToPhone.ToEncrytPhone();
                        t.ToAddress = t.ToAddress.ToTaoBaoEncryptAddress();
                    }
                });
                if (shop.PlatformType == PlatformType.Taobao.ToString())
                    ych_sdk.YchRequestLogger.Order(shop.Id.ToString(), "订单查询", pids);
                else if (shop.PlatformType == PlatformType.Jingdong.ToString())
                    jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
            }

            if (!orders.Any())
            {
                var msg = hasExceptLockOrRefundSetting ? "由于您导出设置过滤了退款或锁定订单，当前列表无满足条件的订单可导出。若您需要导出这些订单，请修改设置。" : "无满足条件的订单";
                log.Exception = msg;
                return FalidResult(msg);
            }

            //workbook = BuildExcel(orders, setting, model.IsCustomerOrder, fileName);
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (orders != null && orders.Any() && pt == PlatformType.Pinduoduo.ToString())
            {
                if (model.IsCustomerOrder == false)
                {
                    MyOrderService.TryToDecryptPddOrders(orders);
                    orders.ForEach(o =>
                    {
                        EncryptReceiverInfo(o);
                    });
                }
            }
            workbook = BuildExccelService.BuildOrderPrintExcel(orders, setting, model.IsCustomerOrder, fileName);// BuildExcel(orders, setting, model.IsCustomerOrder, fileName);
            logDetail.TotalCount = orders?.Count ?? 0;
            logDetail.ExportSetting = settingOptions;
            logDetail.IsCustomerOrder = model.IsCustomerOrder;
            logDetail.IsExportChecked = model.Filters.Any(m => m.Name.ToString2().ToLower() == "id");
            log.Detail = logDetail;

            Response.Cookies.Add(new HttpCookie("downloadToken", Request.Form["downloadToken"].ToString2()));
            var rootPath = Server.MapPath("../Files") + $"\\打印单-{curShop.Id}-{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx";
            using (var fs = new FileStream(rootPath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(fs);
            }
            var memoryStream = new MemoryStream();
            using (var fileStream = new FileStream(rootPath, FileMode.Open))
            {
                fileStream.CopyTo(memoryStream);
            }
            memoryStream.Position = 0;
            if (System.IO.File.Exists(rootPath))
                System.IO.File.Delete(rootPath);
            return File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);

            //using (MemoryStream ms = new MemoryStream())
            //{
            //    workbook.Write(ms);
            //    var buffer = ms.GetBuffer();
            //    //ms.Close();
            //    return File(buffer, "application/ms-excel", ExcelHelper.GetFileName("打印单.xls", Request));
            //}
        }

        private Order EncryptReceiverInfo(Order order)
        {
            order.ToName = order.ToName.ToEncryptName();
            order.BuyerMemberName = order.BuyerMemberName.ToEncryptName();
            order.BuyerWangWang = order.BuyerWangWang.ToEncryptName();
            order.BuyerMemberId = order.BuyerMemberId.ToEncryptName();
            order.ToPhone = order.ToPhone.ToPddEncryptPhone();
            order.ToMobile = order.ToMobile.ToPddEncryptPhone();
            order.ToAddress = order.ToAddress.ToPddEncryptAddress();
            //order.ToCounty = "**";
            return order;
        }


        private IWorkbook BuildExcel(List<Order> orders, ExportSetting setting, bool isCustomerOrder, string fileName)
        {
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (orders != null && orders.Any() && pt == PlatformType.Pinduoduo.ToString())
            {
                if (isCustomerOrder == false)
                {
                    MyOrderService.TryToDecryptPddOrders(orders);
                    orders.ForEach(o =>
                    {
                        EncryptReceiverInfo(o);
                    });
                }
            }
            //DocumentSummaryInformation docInfo = PropertySetFactory.CreateDocumentSummaryInformation();
            //docInfo.Company = "订单";
            //SummaryInformation summaryInfo = PropertySetFactory.CreateSummaryInformation();
            //summaryInfo.Subject = "订单导出";

            //workbook.DocumentSummaryInformation = docInfo;
            //workbook.SummaryInformation = summaryInfo;

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ISheet sheet = workbook.CreateSheet("订单信息");

            if (orders == null || !orders.Any())
                return workbook;

            // 设置发件人
            MyOrderService.SetSellerInfo(orders);

            if (orders != null && orders.Any() && (pt == PlatformType.Taobao.ToString() || pt == PlatformType.Jingdong.ToString()))
            {
                orders.ForEach(o =>
                {
                    EncryptReceiverInfo(o);
                    o.ToAddress = o.ToAddress.ToTaoBaoEncryptAddress();
                });

                //淘宝还需要打码发件人
                if (pt == PlatformType.Taobao.ToString())
                    orders.ForEach(o =>
                    {
                        o.SenderName = o.SenderName.ToEncryptName();
                        o.SenderMobile = o.SenderMobile.ToPddEncryptPhone();
                        o.SenderPhone = o.SenderPhone.ToPddEncryptPhone();
                        o.SenderAddress = o.SenderAddress.ToTaoBaoEncryptAddress();
                    });
            }

            // 底单数据
            List<OrderSelectKeyModel> keys = orders.Select(m => new OrderSelectKeyModel()
            {
                Id = m.Id,
                ShopId = m.ShopId,
                PlatformOrderId = m.PlatformOrderId,
                ChildPlatformOrderIds = m.ChildOrderId?.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).ToList()
            }).ToList();

            // 查询打印记录
            var printHistorys = new List<WaybillCode>();
            if (setting.CheckedItems.Any(m => m.Value.ToString2() == "Export_LastWaybillCode" || m.Value.ToString2() == "SenderCompany"))
            {
                var fields = new List<string> { "w.ShopId", "w.OrderId", "w.OrderIdJoin", "w.ExpressWayBillCode", "w.ExpressName", "w.GetDate" };
                printHistorys = _waybillCodeService.GetWaybillCodeList(keys, fields, 1000)?.OrderByDescending(m => m.ID).ToList() ?? new List<WaybillCode>();
                //if (setting.CheckedItems.Any(m => m.Value.ToString2() == "Export_LastWaybillCode"))
                //{
                //    // 受老系统数据结构影响，自由打印记录未能迁移过来，导出运单号需要通过底单查询查找
                //    var nullKeys = new List<OrderSelectKeyModel>();
                //    keys.ForEach(key =>
                //    {
                //        var count = printHistorys.Count(m => m.OrderId == key.PlatformOrderId && m.ShopId == key.ShopId);
                //        if (count == 0)
                //            nullKeys.Add(key);
                //    });
                //    var waybillCodes = _waybillCodeService.Get(nullKeys, isCustomerOrder);
                //    nullKeys.ForEach(key =>
                //    {
                //        var waybillCode = waybillCodes.Where(m => m.OrderId == key.PlatformOrderId && m.ShopId == key.ShopId).OrderByDescending(m => m.CreateDate).FirstOrDefault();
                //        if (waybillCode != null)
                //        {
                //            printHistorys.Add(new PrintHistory()
                //            {
                //                ExpressWaybillCode = waybillCode.ExpressWayBillCode,
                //                PlatformOrderId = waybillCode.OrderId,
                //                ShopId = waybillCode.ShopId,
                //                PrintDataType = waybillCode.PrintDataType,
                //                PrintDate = waybillCode.CreateDate,
                //                ExpressName = waybillCode.ExpressName,
                //            });
                //        }
                //    });
                //}
            }

            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook);
            ICellStyle contentLongStyle = GetContentCellStyleByToLongCol(workbook);
            ICellStyle contentMergeStyle = GetContentCellStyleByToMerge(workbook);
            ICellStyle contentLongMergeStyle = GetContentCellStyleByToLongColMerge(workbook);
            IRow header = sheet.CreateRow(0);
            header.Height = 15 * 20;
            // 获取勾选的商品信息内容
            var productChkItems = setting.CheckedItems.Where(m => m.From == "product").ToList();
            // 除商品信息外勾选的内容
            var otherChkItems = setting.CheckedItems.Where(m => m.From != "product" && m.From != "productMerge").ToList();
            //// 添加省市区详细地址的扩展
            //var extChkItems = JsonExtension.ToList<CheckedItem>(JsonExtension.ToJson(setting.CheckedItems));

            List<string> headNames = setting.CheckedItems.Select(m => m.Text).ToList();
            // 商品信息单列显示，列头移除单列商品信息
            if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
            {
                headNames = setting.CheckedItems.Where(m => m.From != "product").Select(m => m.Text).ToList();
            }
            // 收件人地址分省、市、区、详细地址4列显示
            if (setting.Export_AddressShowStyle == "Export_Address_Single")
            {
                var headIndex = setting.CheckedItems.FindIndex(m => m.Value == "Export_ToAddress");
                if (headIndex != -1)
                {
                    var addrLst = new List<CheckedItem>()
                    {
                        new CheckedItem(){ Text="收件省",Value="ToProvince",From="order"},
                        new CheckedItem(){ Text="收件市",Value="ToCity",From="order"},
                        new CheckedItem(){ Text="收件区",Value="ToCounty",From="order"},
                        new CheckedItem(){ Text="收件详细地址",Value="ToAddress",From="order"},
                    };
                    var addr = addrLst.Select(m => m.Text).ToList();
                    headNames.RemoveAt(headIndex);
                    headNames.InsertRange(headIndex, addr);

                    otherChkItems.RemoveAt(headIndex);
                    otherChkItems.InsertRange(headIndex, addrLst);
                }
            }

            // 第一行填充表头信息和样式
            int index = 0;
            // 设置Excel列头内容和样式
            headNames.ForEach(name =>
            {
                //设置列宽度
                //sheet.SetColumnWidth(index, 30 * 256);
                SetColumnWidth(sheet, name, index);
                // 设置列名和样式
                header.CreateCell(index).SetCellValue(name);
                header.GetCell(index).CellStyle = headStyle;
                index++;
            });

            // 第二行开始填充Excel内容
            index = 1; // 订单总行数
            //var itemRowIndex = 1; // 实际总行数
            var shops = SiteContext.Current.AllShops;
            var orderCategorysSetting = _commonSettingService.Get("OrderCategorySet", SiteContext.Current.CurrentShopId);
            List<OrderCategory> orderCategorys = orderCategorysSetting == null || orderCategorysSetting.Value.IsNullOrEmpty() ? new List<OrderCategory>() : JsonExtension.ToList<OrderCategory>(orderCategorysSetting.Value.ToString2());

            orders.ForEach(order =>
            {
                var dic = order.ToDictionary();
                IRow row = sheet.CreateRow(index);
                row.Height = 20 * 20;
                var isAddrMerge = true;
                var itemRowIndex = index;

                // 合并订单行内容
                List<CellRangeAddress> regionLst = new List<CellRangeAddress>();
                if (setting.Export_ProductShowStyle == "Export_ShowMutilLine_Merge")
                {
                    if (order.OrderItems.Count > 1 && productChkItems.Count > 0)
                    {
                        otherChkItems.ForEach(item =>
                        {
                            var itemIndex = headNames.IndexOf(item.Text);
                            if (itemIndex != -1)
                            {
                                // 商品信息之前的内容行合并
                                int startRowIndex = index;
                                int endRowNumIndex = index + order.OrderItems.Count - 1;

                                var productRegion = new CellRangeAddress(startRowIndex, endRowNumIndex, itemIndex, itemIndex);
                                sheet.AddMergedRegion(productRegion);
                            }
                        });
                    }
                }

                var platformOrderIds = string.Empty;
                if (isCustomerOrder)
                    platformOrderIds = dic["CustomerOrderId"].ToString2().IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : dic["CustomerOrderId"].ToString2();
                else
                {
                    var childOrderIds = dic["ChildOrderId"].ToString2();
                    platformOrderIds = childOrderIds.IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : childOrderIds.Replace(",", "\n").Replace("，", "\n");
                }

                // 商品信息所在列序号集合
                var productCellIndexLst = new List<int>();
                // 填充每一列的值
                setting.CheckedItems.ForEach(chkItem =>
                {
                    var field = chkItem.Value.Replace("Export_", "");
                    var text = chkItem.Text;
                    var from = chkItem.From;

                    // 收件人地址不合并1列显示，则移除收件人地址之后列往后移动3列
                    var cellIndex = setting.CheckedItems.IndexOf(chkItem);
                    if (!isAddrMerge)
                    {
                        cellIndex = cellIndex + 3;
                    }

                    #region 填充每列的内容
                    if (from == "shop")
                    {
                        var shop = shops.FirstOrDefault(m => m.Id == order.ShopId);
                        row.CreateCell(cellIndex).SetCellValue(shop == null ? "" : shop.NickName);
                        row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (from == "ordercategory")
                    {
                        var category = orderCategorys.FirstOrDefault(m => m.Id == order.CategoryId);
                        row.CreateCell(cellIndex).SetCellValue(category == null ? "" : category.Alias);
                        row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                    }
                    else if (from == "order")
                    {
                        // 收件人地址判断省市区地址是否合并一列显示
                        if (field == "ToAddress")
                        {
                            if (setting.Export_AddressShowStyle == "Export_Address_Merge")
                            {
                                // 省市区地址在同列显示
                                var address = order.ToProvince.ToString2() + order.ToCity.ToString2() + order.ToCounty.ToString2() + order.ToAddress.ToString2();
                                row.CreateCell(cellIndex).SetCellValue(address);
                                row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                            }
                            else
                            {
                                // 省市区地址在不同列显示
                                row.CreateCell(cellIndex).SetCellValue(order.ToProvince.ToString2());
                                row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 1).SetCellValue(order.ToCity.ToString2());
                                row.GetCell(cellIndex + 1).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 2).SetCellValue(order.ToCounty.ToString2());
                                row.GetCell(cellIndex + 2).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;

                                row.CreateCell(cellIndex + 3).SetCellValue(order.ToAddress.ToString2());
                                row.GetCell(cellIndex + 3).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;

                                isAddrMerge = false;
                            }
                        }
                        else if (field == "PlatformType" || field == "OrderFrom")
                        {
                            // 订单来源
                            var val = dic[field].ToString2().Trim().ToLower() == "alibaba" ? "1688" : dic[field].ToString2().Trim().ToLower();
                            val = val == "importorder" ? "导入单" : (val == "customerorder" ? "录入单" : val);
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "OrderTime")
                        {
                            // 订单日期
                            var status = dic["PlatformStatus"].ToString2().Trim().ToLower();
                            var val = status == "waitbuyerpay" || status == "confirm_goods_but_not_fund" ? dic["CreateTime"].ToString2() : dic["PayTime"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(val);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "OrderCount")
                        {
                            // 订单数
                            var count = order.SubOrders?.Count ?? 0;
                            row.CreateCell(cellIndex).SetCellValue(count);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle;
                        }
                        else if (field == "SellerRemark" || field == "BuyerRemark")
                        {
                            var arr = dic[field].ToString2().Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                            var str = arr == null ? "" : string.Join(";", arr);
                            row.CreateCell(cellIndex).SetCellValue(str);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "ToPhone")
                        {
                            var phone = dic["ToMobile"].ToString2().IsNullOrEmpty() ? dic["ToPhone"].ToString2() : dic["ToMobile"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(phone);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "SenderPhone")
                        {
                            var phone = dic["SenderMobile"].ToString2().IsNullOrEmpty() ? dic["SenderPhone"].ToString2() : dic["SenderMobile"].ToString2();
                            row.CreateCell(cellIndex).SetCellValue(phone);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "PlatformOrderId")
                        {
                            //var platformOrderIds = string.Empty;
                            //if (isCustomerOrder)
                            //    platformOrderIds = dic["CustomerOrderId"].ToString2().IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : dic["CustomerOrderId"].ToString2();
                            //else
                            //{
                            //    var childOrderIds = dic["ChildOrderId"].ToString2();
                            //    platformOrderIds = childOrderIds.IsNullOrEmpty() ? dic["PlatformOrderId"].ToString2() : childOrderIds.Replace(",", "\n").Replace("，", "\n");
                            //}

                            row.CreateCell(cellIndex).SetCellValue(platformOrderIds);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "SenderCompany")
                        {
                            var printHistory = printHistorys.Where(c => !c.ExpressName.IsNullOrEmpty() && c.ShopId == order.ShopId && (c.OrderId == order.PlatformOrderId || c.OrderIdJoin.ToString2().Contains(order.PlatformOrderId) || (isCustomerOrder ? c.OrderId == order.CustomerOrderId : c.OrderId == order.MergeredOrderId))).FirstOrDefault();
                            var expressName = printHistory?.ExpressName.ToString2() ?? "";
                            row.CreateCell(cellIndex).SetCellValue(expressName);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "LastWaybillCode")
                        {
                            var printHistoryStr = string.Empty;
                            var printHistoryLst = printHistorys.Where(c => !c.ExpressWayBillCode.IsNullOrEmpty() && c.ShopId == order.ShopId && (c.OrderId == order.PlatformOrderId || c.OrderIdJoin.ToString2().Contains(order.PlatformOrderId) || (isCustomerOrder ? c.OrderId == order.CustomerOrderId : c.OrderId == order.MergeredOrderId)));
                            var printHistory = printHistoryLst.OrderByDescending(c => c.GetDate).FirstOrDefault(); //最近一次打印记录
                            printHistoryLst = printHistory == null ? new List<WaybillCode>() : printHistoryLst.Where(m => m.GetDate == printHistory.GetDate).ToList();//根据打印时间判断是否是一单多包
                            // 一单多包导出多个运单号
                            if (printHistoryLst != null && printHistoryLst.Count() > 1)
                                printHistoryStr = string.Join("\n", printHistoryLst?.Select(m => m.ExpressWayBillCode).ToList() ?? new List<string>());
                            else
                                printHistoryStr = printHistory?.ExpressWayBillCode.ToString2() ?? "";

                            row.CreateCell(cellIndex).SetCellValue(printHistoryStr);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else if (field == "LastSendTime")
                        {
                            var lastSendTime = dic["LastSendTime"].ToString2();
                            lastSendTime = lastSendTime.IsNullOrEmpty() ? dic["AllDeliveredTime"].ToString2() : lastSendTime;
                            if (lastSendTime.IsNullOrEmpty() && !order.ChildOrderId.IsNullOrEmpty())
                            {
                                var okeys = platformOrderIds.SplitWithString("\n").Where(m => !m.IsNullOrEmpty()).Select(id => new OrderSelectKeyModel { PlatformOrderId = id, ShopId = order.ShopId }).ToList();
                                var childOrders = MyOrderService.GetOrders(okeys, fields: new List<string> { "o.Id", "o.PlatformOrderId", "o.ShopId", "o.LastSendTime", "o.AllDeliveredTime", "oi.Id" });
                                var newLastTime = childOrders?.Max(o => o.LastSendTime) ?? childOrders?.Max(o => o.AllDeliveredTime);
                                if (newLastTime != null)
                                    lastSendTime = newLastTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                            }
                            lastSendTime = lastSendTime.IsNullOrEmpty() ? lastSendTime : lastSendTime.toDateTime().ToString("yyyy-MM-dd HH:mm:ss");
                            row.CreateCell(cellIndex).SetCellValue(lastSendTime);
                            row.GetCell(cellIndex).CellStyle = row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle;
                        }
                        else
                        {
                            row.CreateCell(cellIndex).SetCellValue(dic[field].ToString2());
                            row.GetCell(cellIndex).CellStyle = field == "SenderAddress" ? (row.GetCell(cellIndex).IsMergedCell ? contentLongMergeStyle : contentLongStyle) : (row.GetCell(cellIndex).IsMergedCell ? contentMergeStyle : contentStyle);
                        }
                    }
                    else if (from == "productMerge")
                    {
                        // 商品相关信息所在列的序号
                        if (!productCellIndexLst.Contains(cellIndex))
                            productCellIndexLst.Add(cellIndex);

                        if (setting.Export_ProductShowStyle == "Export_ShowOneLine")
                        {
                            #region 商品信息合并1行1列显示
                            var productContent = new StringBuilder();
                            // 获取商品信息
                            order.OrderItems.ForEach(item =>
                            {
                                var itemDic = item.ToDictionary();
                                var productSubContent = new StringBuilder();
                                // 商品信息合并1列显示
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = field2 == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field2].ToString2();
                                    productSubContent.Append(field2 == "Count" ? $"*{val}," : $"{val},");
                                });

                                productContent.Append($"{productSubContent.ToString2().TrimEnd(",")};\n");
                            });

                            row.CreateCell(cellIndex).SetCellValue(productContent.ToString2());
                            row.GetCell(cellIndex).CellStyle = contentLongStyle;
                            #endregion
                        }
                        else
                        {
                            #region 商品多行列合并显示
                            var isFirstItemRowIndex = true;
                            itemRowIndex = index;
                            // 获取商品信息
                            order.OrderItems.ForEach(item =>
                            {
                                var itemDic = item.ToDictionary();
                                var productContent = new StringBuilder();
                                // 商品信息合并1列显示
                                productChkItems.ForEach(chkItem2 =>
                                {
                                    var field2 = chkItem2.Value.Replace("Export_", "");
                                    var text2 = chkItem2.Text;
                                    var from2 = chkItem2.From;
                                    var val = field2 == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field2].ToString2();
                                    productContent.Append(field2 == "Count" ? $"*{val}," : $"{val},");
                                });

                                if (isFirstItemRowIndex)
                                {
                                    row.CreateCell(cellIndex).SetCellValue(productContent.ToString2().TrimEnd(","));
                                    row.GetCell(cellIndex).CellStyle = contentLongStyle;
                                    isFirstItemRowIndex = false;
                                }
                                else
                                {
                                    // 创建新行填充商品信息
                                    IRow row2 = sheet.GetRow(itemRowIndex) ?? sheet.CreateRow(itemRowIndex);
                                    row2.Height = 25 * 20;
                                    // 商品单独行显示，无订单项数据
                                    row2.CreateCell(cellIndex).SetCellValue(productContent.ToString2().TrimEnd(","));
                                    row2.GetCell(cellIndex).CellStyle = contentLongStyle;
                                }

                                itemRowIndex++;
                            });
                            #endregion
                        }
                    }
                    else if (from == "product" && setting.Export_ProductShowStyle != "Export_ShowOneLine")
                    {
                        // 商品相关信息所在列的序号
                        if (!productCellIndexLst.Contains(cellIndex))
                            productCellIndexLst.Add(cellIndex);

                        #region 商品属性多列显示
                        var isFirstItemRowIndex = true;
                        itemRowIndex = index;
                        // 获取商品信息
                        order.OrderItems.ForEach(item =>
                        {
                            var itemDic = item.ToDictionary();
                            var val = field == "SkuAttr" ? itemDic["Color"].ToString2() + " " + itemDic["Size"].ToString2() : itemDic[field].ToString2();
                            if (isFirstItemRowIndex)
                            {
                                row.CreateCell(cellIndex).SetCellValue(val);
                                row.GetCell(cellIndex).CellStyle = field == "ProductSubject" ? contentLongStyle : contentStyle;
                                isFirstItemRowIndex = false;
                            }
                            else
                            {
                                // 创建新行填充商品信息
                                IRow row2 = sheet.GetRow(itemRowIndex) ?? sheet.CreateRow(itemRowIndex);
                                row2.Height = 25 * 20;
                                row2.CreateCell(cellIndex).SetCellValue(val);
                                row2.GetCell(cellIndex).CellStyle = field == "ProductSubject" ? contentLongStyle : contentStyle;
                            }
                            itemRowIndex++;
                        });
                        #endregion
                    }
                    #endregion
                });

                // 商品多行显示，填充订单数据
                int itemCount = order.OrderItems.Count;
                if (setting.Export_ProductShowStyle == "Export_Export_Split" && itemCount > 1)
                {
                    var tmpRowIndex = index;
                    IRow firstRow = sheet.GetRow(tmpRowIndex);
                    if (firstRow != null)
                    {
                        var firstCells = firstRow.Cells;
                        for (var i = 1; i < itemCount; i++)
                        {
                            // 商品单独行显示，并填充订单项数据
                            IRow nextRow = sheet.GetRow(tmpRowIndex + i);
                            if (nextRow == null) break;
                            for (var j = 0; j < firstCells.Count; j++)
                            {
                                if (!productCellIndexLst.Contains(j))
                                {
                                    var cell = firstCells[j];
                                    if (cell == null) continue;
                                    nextRow.CreateCell(j).SetCellValue(cell.ToString2());
                                    nextRow.GetCell(j).CellStyle = cell.CellStyle;
                                }
                            }
                        }
                    }
                }

                if (itemRowIndex > index)
                    index = itemRowIndex;
                else
                    index++;

                ExcelHelper.AutoSizeRowHeight(workbook, sheet, row);
            });

            return workbook;
        }

        private void SetColumnWidth(ISheet sheet, string headName, int index)
        {
            int width = 20 * 256;

            if (headName == "商品数量" || headName == "订单金额" || headName == "运费" || headName == "重量" || headName == "商品单价" || headName == "单价"
                || headName == "订单数" || headName == "件数" || headName == "款数" || headName == "金额" || headName == "运费" || headName == "重量(克)" || headName == "数量")
                width = 10 * 256;
            else if (headName == "收件人" || headName == "收件人电话" || headName == "联系电话" || headName == "发件人" || headName == "发件人电话" || headName == "收件省"
                || headName == "收件市" || headName == "收件区")
                width = 15 * 256;
            else if (headName == "订单来源" || headName == "订单分类" || headName == "店铺"
                 || headName == "下单时间" || headName == "付款时间" || headName == "发货时间" || headName == "打印快递单时间" || headName == "打印发货单时间" || headName == "打印拿货标签时间"
                 || headName == "打印快递单次数" || headName == "打印发货单次数" || headName == "打印拿货标签次数"
                 || headName == "日期")
                width = 20 * 256;
            else if (headName == "买家旺旺" || headName == "买家昵称" || headName == "订单编号" || headName == "流水号" || headName == "单品货号" || headName == "商品规格" || headName == "商品销售属性" || headName == "销售属性" || headName == "商品货号" || headName == "商品简称" || headName == "快递单号")
                width = 25 * 256;
            else if (headName == "快递公司" || headName == "收件详细地址")
                width = 30 * 256;
            else if (headName == "收件人地址" || headName == "收件地址" || headName == "发件人地址" || headName == "发件地址" || headName == "商品标题" || headName == "商品名称" || headName == "商品信息" || headName == "买家留言" || headName == "卖家备注" || headName == "打印内容")
                width = 35 * 256;

            sheet.SetColumnWidth(index, width);
        }

        private ICellStyle GetHeadStyle(IWorkbook workbook)
        {
            IFont font = workbook.CreateFont();
            font.FontName = "Times New Roman";
            font.Boldweight = short.MaxValue;
            font.FontHeightInPoints = 11;

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(font);
            headerStyle.Alignment = HorizontalAlignment.Center;//内容居中显示
            headerStyle.WrapText = true;
            return headerStyle;
        }

        private ICellStyle GetContentStyle(IWorkbook excel)
        {
            IFont font = excel.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = excel.CreateCellStyle();
            contentStyle.SetFont(font);
            contentStyle.Alignment = HorizontalAlignment.Center;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.WrapText = true;

            return contentStyle;
        }

        private ICellStyle GetContentCellStyleByToLongCol(IWorkbook excel)
        {
            ICellStyle contentStyle = GetContentStyle(excel);
            contentStyle.Alignment = HorizontalAlignment.Left;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            return contentStyle;
        }

        private ICellStyle GetContentCellStyleByToMerge(IWorkbook excel)
        {
            ICellStyle contentStyle = GetContentStyle(excel);
            contentStyle.Alignment = HorizontalAlignment.Center;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            return contentStyle;
        }

        private ICellStyle GetContentCellStyleByToLongColMerge(IWorkbook excel)
        {
            ICellStyle contentStyle = GetContentStyle(excel);
            contentStyle.Alignment = HorizontalAlignment.Left;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            return contentStyle;
        }
        #endregion

        #region 工具方法

        public ActionResult Split()
        {
            new MergerOrderService().SplitOrderWhenPlatformStatusNotSame();
            return SuccessResult();
        }

        #endregion

        /// <summary>
        /// 检测pdd用户电子面单使用情况
        /// 1.pdd用户未添加pdd电子面单
        /// 2.pdd用户未添加电子面单，且有开通电子面单功能
        /// </summary>
        /// <returns></returns>
        public ActionResult PddUserCheckWaybillUse()
        {
            //返回结果说明：
            //0：不需要提示用户
            //1：（未添加pdd电子面单模板，自己也未开通电子面单）提示添加Pdd电子面单
            //data：（自己开通了电子面单）让用户确认是否添加相应模板


            //1.判断当前用户是否是拼多多用户
            if (SiteContext.Current.CurrentLoginShop.PlatformType != PlatformType.Pinduoduo.ToString())
            {
                return SuccessResult(0);
            }

            //2.读取配置，确定用户是否已经确认
            var shopId = SiteContext.Current.CurrentShopId;
            var setting_01 = _commonSettingService.GetString("PddUserWaybillUseChecked_01", shopId); //加载配置
            var setting_02 = _commonSettingService.GetString("PddUserWaybillUseChecked_02", shopId); //加载配置

            //根据配置判断是否用户已经确认过
            var isChecked = false;
            if (string.IsNullOrEmpty(setting_02) == false && bool.Parse(setting_02) == true)
            {
                isChecked = true;
            }

            if (isChecked == true)
            {
                return SuccessResult(0);
            }

            //3.检测用户是否添加了PDD电子面单
            var hasPddTemplate = _printTemplateService.CheckUserIsExistsTempalte(shopId, new List<int> { 21, 22 });

            //4.检测当前用户是否开通了pdd电子面单
            var waybillAuthInfo = SiteContext.Current.CurrentLoginShop.ToWaybillAuthInfo();
            var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(waybillAuthInfo);
            var pddWaybillApiService = new PddWaybillApiService(waybillAuthConfig);
            var branchs = pddWaybillApiService.GetPinduoduoBranchAddressList();

            //5.检测结果，做相应返回 //|| branchs.Any(f => f.CpType == 1 || (f.CpType == 2 && f.Quantity > 0)) == false
            if (branchs.Count == 0)
            {
                if (hasPddTemplate == false && (string.IsNullOrEmpty(setting_01) == true || bool.Parse(setting_01) == false))
                {
                    return SuccessResult(1); //未添加pdd电子面单模板，且自己也未开通pdd电子面单
                }
                else
                {
                    return SuccessResult(0); //无需处理
                }
            }
            else
            {
                //1.过滤出可用的网点快递
                var availableBranchs = branchs.ToList(); //.Where(f => f.CpType == 1 || (f.CpType == 2 && f.Quantity > 0))

                //2.排除添加过的模板
                var cpCodes = availableBranchs.Select(f => f.CpCode).ToList();
                var cpCodeMappings = _cpCodeMappingService.GetListByCpCodes(cpCodes, "Pdd");
                var existTemplates = _printTemplateService.LoadTemplates(cpCodeMappings.Select(f => f.ExpressCompany.Id).ToList(), new List<int> { 21, 22 }, shopId);

                var result = new List<BranchAddress>();
                availableBranchs.ForEach(ab =>
                {
                    //3.赋值快递名称
                    var mapping = cpCodeMappings.FirstOrDefault(f => f.CpCode == ab.CpCode);
                    if (mapping != null)
                    {
                        var t = existTemplates.FirstOrDefault(f => f.ExpressCompanyId == mapping.ExpressCompany.Id);
                        if (t == null || (t.TemplateRelationAuthInfo.AuthSourceType == 1 && t.TemplateRelationAuthInfo.CaiNiaoAuthInfoId != shopId))
                        {
                            ab.CompanyName = mapping.PlatformExpressName;
                            result.Add(ab);
                        }
                    }
                });

                return SuccessResult(result.OrderByDescending(f => f.Quantity)); //返回实际数据
            }
        }

        /// <summary>
        /// 添加网点对应的标准模板
        /// </summary>
        /// <param name="branchHashCodeList"></param>
        /// <returns></returns>
        public ActionResult AddTemplates(List<string> branchHashCodeList)
        {
            if (branchHashCodeList == null || branchHashCodeList.Count == 0) return SuccessResult();

            var shopId = SiteContext.Current.CurrentShopId;

            //获取当前用户的网点
            var waybillAuthInfo = SiteContext.Current.CurrentLoginShop.ToWaybillAuthInfo();
            var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(waybillAuthInfo);
            var pddWaybillApiService = new PddWaybillApiService(waybillAuthConfig);
            var branchs = pddWaybillApiService.GetPinduoduoBranchAddressList();

            //找出用户要添加模板的网点
            var addTemplateBranch = new List<BranchAddress>();
            branchHashCodeList.ForEach(item =>
            {
                var b = branchs.FirstOrDefault(f => f.BranchHashCode == item);
                if (b != null)
                {
                    addTemplateBranch.Add(b);
                }
            });

            if (addTemplateBranch.Count == 0)
                return SuccessResult();

            //添加电子面单模板
            //1.先检测当前用户有没有添加自己的电子面单授权
            var cainiaoAuthOwnerService = new CainiaoAuthOwnerService();
            var _printControlService = new PrintControlService();
            var _waybillCustomAreaService = new WaybillCustomAreaService();
            var _templateRelationAuthInfoService = new TemplateRelationAuthInfoService();
            var _caiNiaoAccountBranchService = new CaiNiaoAccountBranchService();
            var _stapleTemplateService = new StapleTemplateService();

            var cainiaoAuthOwner = cainiaoAuthOwnerService.GetModel(shopId, shopId, 1);
            if (cainiaoAuthOwner == null)
            {
                cainiaoAuthOwnerService.NewCainiaoAuthOwner(new CainiaoAuthOwner()
                {
                    AuthSourceType = 1,
                    CaiNiaoAuthInfoId = shopId,
                    IsDeleted = false,
                    ShopId = shopId,
                    MemberId = SiteContext.Current.CurrentLoginShop.ShopId,
                });
            };
            //2.添加模板
            var cpCodes = addTemplateBranch.Select(f => f.CpCode).ToList();
            var cpCodeMappings = _cpCodeMappingService.GetListByCpCodes(cpCodes, "Pdd");
            addTemplateBranch.ForEach(item =>
            {
                var mapping = cpCodeMappings.FirstOrDefault(f => f.CpCode == item.CpCode);
                if (mapping != null)
                {
                    //找出系统pdd标准模板
                    var systemTempalte = _printTemplateService.LoadTemplates(mapping.ExpressCompany.Id, 21).FirstOrDefault();
                    if (systemTempalte != null)
                    {
                        //1.copytempalte
                        var newTemplateId = _printTemplateService.CopyPrintTemplate(systemTempalte.Id, null, shopId);
                        _printControlService.CopyPrintControl(-1, newTemplateId);
                        //复制自定义区域
                        var customAreaId = _waybillCustomAreaService.CopyWaybillCustomArea_Pdd(systemTempalte.Id, newTemplateId, systemTempalte.TemplateType, mapping.ExpressCompany.CompanyCode, shopId);
                        //存储模板与电子面单关联关系
                        var templateRelationAuthInfoId = _templateRelationAuthInfoService.Add(new TemplateRelationAuthInfo()
                        {
                            TemplateId = newTemplateId,
                            CaiNiaoAuthInfoId = shopId,
                            AuthSourceType = 1,
                            BranchCode = item.BranchCode,
                            BranchName = item.BranchName,
                            BranchHashCode = item.BranchHashCode,
                            BranchAddress = (item.Province + item.City + item.Area + item.Town + item.Detail)?.Trim(),
                        });

                        //加入常用模板
                        _stapleTemplateService.AddStapleAndSetDefault(newTemplateId, shopId);

                        //存储电子面单账号网点信息
                        _caiNiaoAccountBranchService.BulkInsert(new List<CaiNiaoAccountBranch>() {
                            new CaiNiaoAccountBranch() {
                                ShopId=shopId,
                                CaiNiaoAuthInfoId=shopId,
                                BranchCode=item.BranchCode,
                                BranchName=item.BranchName,
                                Province=item.Province,
                                City=item.City,
                                District=item.Area,
                                Town =item.Town,
                                Detail=item.Detail,
                                CpCode=item.CpCode,
                                CpType=item.CpType,
                                CreateTime=DateTime.Now,
                                PlatformType=systemTempalte.PrintTemplateType.ToString(),
                                BranchHashCode=item.BranchHashCode
                            }
                        });
                    }
                }
            });

            return SuccessResult();
        }

        public ActionResult GetShopFullSyncStatus()
        {
            var status = SiteContext.Current.CurrentLoginShop.FullSyncStatus;
            return SuccessResult(status);
        }

        public ActionResult GetUserGuideInfo()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var isShowGreenhand = _commonSettingService.IsShowUserGuideDialog(shopId);
            var isSetAddresserComplete = true;
            var isSetElectronicOrdeComplete = true;
            var isAddElectronicOrderComplete = true;
            if (isShowGreenhand)
            {
                var sellerService = new SellerInfoService();
                isSetAddresserComplete = sellerService.GetDefaultSeller(shopId) != null;
                isSetElectronicOrdeComplete = _commService.GetAccountAuthInfoList(new List<int> { SiteContext.Current.ExpressTemplateShopId }, false).Any();
                isAddElectronicOrderComplete = _printTemplateService.LoadTemplateList(new List<int> { SiteContext.Current.ExpressTemplateShopId }, null).Any();
            }
            if (isSetAddresserComplete == true && isSetElectronicOrdeComplete == true && isAddElectronicOrderComplete == true)
                isShowGreenhand = false;
            return SuccessResult(new { isShowGreenhand, isSetAddresserComplete, isSetElectronicOrdeComplete, isAddElectronicOrderComplete });
        }

        public ActionResult SetUserGuideInfoNeverShow()
        {
            _commonSettingService.Set(CommonSettingService.IsShowUserGuideDialogKey, "false", SiteContext.Current.CurrentShopId);
            return SuccessResult();
        }

        [LogForOperatorFilter("确认订单")]
        public ActionResult ConfirmOrder(string platformOrderIds)
        {
            var log = LogForOperatorContext.Current.logInfo;
            var pids = platformOrderIds.ToList<string>();
            _service = PlatformFactory.GetPlatformService(SiteContext.Current.CurrentLoginShop);
            var result = _service.ConfirmOrder(pids);
            if (result.IsNullOrEmpty())
                return SuccessResult();
            else
            {
                log.Exception = result;
                return FalidResult(result);
            }
        }

        //public ActionResult SyncProcess(List<int> ids = null)
        //{
        //    if (ids == null)
        //        ids = SiteContext.Current.ShopIds;
        //    var syncInfos = _shopService.GetSyncInfo(ids);
        //    SyncOrderProcess temp = new SyncOrderProcess { CuurentCount = 1, TotalCount = 1, Step = "查询订单", Status = "Finished" };
        //    //多个店铺同时同步时，默认取同步最慢的那个店铺，若没有同步中的则取任意一个即可
        //    if (syncInfos != null && syncInfos.Any())
        //    {
        //        var finished = syncInfos.FirstOrDefault(s => s.Step == "查询订单");
        //        if (finished != null)
        //            temp = finished;
        //        else
        //        {
        //            var syncings = syncInfos.Where(s => s.Status == "Syncing").ToList();
        //            if (syncings != null && syncings.Any())
        //            {
        //                var slowest = syncings.OrderBy(s => s.Percent).FirstOrDefault();
        //                temp = slowest;
        //            }
        //            else
        //                temp = syncInfos.FirstOrDefault();
        //        }
        //    }
        //    return SuccessResult(new
        //    {
        //        temp.CuurentCount,
        //        temp.TotalCount,
        //        temp.Status,
        //        temp.Step,
        //        temp.Percent,
        //        temp.ErrorMessage,
        //        temp.IsFinished,
        //        temp.ShopId,
        //        temp.Id
        //    });
        //}

        public ActionResult SyncInfo(string from, string requestUrl, List<int> ids = null)
        {
            var blockSetting = _shopService.GetOperatorBlockSetting(SiteContext.Current.CurrentShopId);
            var isBlockQuery = blockSetting != null && blockSetting.BlockEndTime > DateTime.Now && blockSetting.IsQueryBlocked;
            if (isBlockQuery)
            {
                return SuccessResult(new
                {
                    CuurentCount = 1,
                    TotalCount = 2,
                    Status = "Syncing",
                    Step = "同步订单",
                    Percent = 50,
                    ErrorMessage = "blocked",
                    IsFinished = false
                });
            }

            var currentPageIsPddFdsOrderList = false; //前进入的列表是 拼多多代发订单列表,同步状态存储在配置中
            if (SiteContext.Current.CurrentLoginShop.IsPddFactorer && requestUrl?.ToLower()?.Contains("indexfds") == true)
                currentPageIsPddFdsOrderList = true;

            if (from == "list")
            {
                var isSyncing = false;
                if (currentPageIsPddFdsOrderList)
                    isSyncing = _commonSettingService.GetOrderSyncStatus(SiteContext.Current.CurrentShopId)?.LastSyncStatus == "Syncing";
                else
                    isSyncing = _shopService.GetShopSyncStatus(SiteContext.Current.CurrentShopId) == "Syncing";
                return SuccessResult(new
                {
                    CuurentCount = 1,
                    TotalCount = 2,
                    Status = isSyncing ? "Syncing" : "Finished",
                    Step = "同步订单",
                    Percent = 50,
                    ErrorMessage = "",
                    IsFinished = isSyncing ? false : true
                });
            }
            var key = "";
            if (currentPageIsPddFdsOrderList == false)
            {
                key = "/User/Shop/SyncInfo";
                if (ids == null)
                    ids = SiteContext.Current.SamePlatformShopIds;
            }
            else
            {
                key = "/User/Shop/SyncInfo_PddFds";
                if (ids == null)
                {
                    ids = new List<int> { SiteContext.Current.CurrentShopId };
                    SiteContext.Current.ChildShop?.ForEach(s =>
                    {
                        if (s.IsPddFactorer)
                            ids.Add(s.Id);
                    });
                }
            }
            var syncInfos = _commonSettingService.GetSettingByShopIds<ShopSynInfoModel>( key , ids);
            //多个店铺同时同步时，默认取同步最慢的那个店铺，若没有同步中的则取任意一个即可
            ShopSynInfoModel temp = null;
            if (syncInfos != null && syncInfos.Any())
            {
                var finished = syncInfos.FirstOrDefault(s => s.IsFinished);
                if (finished != null)
                    temp = finished;
                else
                {
                    var syncings = syncInfos.Where(s => s.IsFinished == false).ToList();
                    if (syncings != null && syncings.Any())
                    {
                        var slowest = syncings.OrderBy(s => s.Percent).FirstOrDefault();
                        temp = slowest;
                    }
                    else
                        temp = syncInfos.FirstOrDefault();
                }
            }
            else
            {
                temp = new ShopSynInfoModel()
                {
                    IsFinished = SiteContext.Current.CurrentLoginShop.LastSyncStatus != "Syncing",
                };
            }
            return SuccessResult(new
            {
                CuurentCount = temp.CurStep,
                TotalCount = temp.TotalStep,
                Status = temp.IsFinished ? "Finished" : "Syncing",
                Step = "同步订单",
                temp.Percent,
                ErrorMessage = "",
                temp.IsFinished
            });
        }


        /// <summary>
        /// 检测当前店铺是否是新用户，从而决定是否显示指引
        /// 依据：是否有打印记录
        /// </summary>
        /// <returns></returns>
        public ActionResult IsShowGuide()
        {
            var shop = SiteContext.Current.CurrentLoginShop;
            //默认不显示指引
            var isShowGuide = false;
            //先读配置，配置成了不显示指引，则不显示 0:不显示，其他情况:没有打印记录时显示
            var showGuide = _commonSettingService.GetString("ShowGuide", shop.Id);
            //为null 说明是新用户，不曾显示过指引；或历史用户
            if (showGuide != "0" && shop.CreateTime < DateTime.Now.AddDays(-15))
            {
                //然后判断是否是老用户,未曾打印过的老用户显示（新用户也没有打印过）
                var hasPrinthistory = _printHistoryService.HasPrintHistory(shop.Id);
                if (hasPrinthistory == false)
                    isShowGuide = true;
            }
            return Json(isShowGuide);
        }

        /// <summary>
        /// 显示电话号码
        /// </summary>
        /// <param name="pid">订单ID</param>
        /// <param name="sid">店铺ID</param>
        /// <returns></returns>

        [LogForOperatorFilter("京东解码收件人电话号码")]
        public ActionResult ShowPhone(string pid, int sid)
        {
            var orderService = new OrderService(SiteContext.Current.CurrentLoginShop);
            var phone = orderService.GetPhoneNumberByOrderId(pid, sid);
            return Json(phone);
        }

        /// <summary>
        /// 解码订单信息
        /// </summary>
        /// <param name="pid">订单ID</param>
        /// <param name="sid">店铺ID</param>
        /// <returns></returns>
        [LogForOperatorFilter("解码订单收件人信息")]
        public ActionResult DecryptOrderInfo(string pid, int sid)
        {
            var orderService = new OrderService(SiteContext.Current.CurrentLoginShop);
            var orderInfo = orderService.GetOrderInfoByOrderId(pid, sid);
            return Json(new { orderInfo.ToName, orderInfo.ToPhone, orderInfo.ToMobile, orderInfo.ToAddress, orderInfo.ToFullAddress });
        }

        /// <summary>
        /// 批量解码订单信息
        /// </summary>
        /// <param name="pid">订单ID</param>
        /// <param name="sid">店铺ID</param>
        /// <returns></returns>
        [LogForOperatorFilter("批量解码订单收件人信息")]
        public ActionResult BatchDecryptOrderInfo(List<OrderSelectKeyModel> models)
        {
            var orderService = new OrderService(SiteContext.Current.CurrentLoginShop);
            var orderInfo = orderService.GetOrderInfosByOrderId(models);
            return Json(orderInfo);
        }

        /// <summary>
        /// 解码订单发件人信息
        /// </summary>
        /// <param name="pid">订单ID</param>
        /// <param name="sid">店铺ID</param>
        /// <returns></returns>
        public ActionResult DecryptSenderInfo(string pid, int sid)
        {
            var orderService = new OrderService(SiteContext.Current.CurrentLoginShop);
            var orderInfo = orderService.GetOrderInfoByOrderId(pid, sid);
            var newOrderInfo = MyOrderService.SetSellerInfo(new List<Order>() { orderInfo });
            return Json(orderInfo);
        }

        /// <summary>
        /// 显示拼多多加密号码
        /// </summary>
        /// <param name="pid">订单ID</param>
        /// <param name="sid">店铺ID</param>
        /// <param name="infoType">加密信息类型</param>
        /// <returns></returns>
        public ActionResult ShowPddEncryptInfo(string pid, int sid, string infoType = "")
        {
            var orderService = new OrderService(SiteContext.Current.CurrentLoginShop);
            var info = orderService.GetPddEncryptInfoByOrderId(pid, sid, infoType);
            return Json(info);
        }
    }
}