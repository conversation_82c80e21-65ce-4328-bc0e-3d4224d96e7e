using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.LogisticService;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Services.WaybillService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using Newtonsoft.Json;
using NPOI.HPSF;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using OperatorType = DianGuanJiaApp.Data.Model.OperatorType;

namespace DianGuanJiaApp.Controllers
{
    public class OrderFdsController : OrderController
    {
        /// <summary>
        /// 订单首页
        /// </summary>
        /// <returns></returns>
        public ActionResult IndexFds()
        {
            var currentLogin = SiteContext.Current.CurrentLoginShop;
            //非拼多多厂商不能进入代打页面
            if (currentLogin.IsPddFactorer == false)
            {
                return Redirect("/Order/Index?token=" + Request["token"]);
            }

            //前端打印分批发送订单数量
            ViewBag.OrderPrintBatchNumber = GetOrderPrintBatchNumber();

            //加载常用模板
            var templateList = _printTemplateService.LoadTemplateList(new List<int> { SiteContext.Current.ExpressTemplateShopId }, true, "pdd");
            ViewBag.StapleTempalteList = templateList.ToJson();

            var shop = SiteContext.Current.CurrentLoginShop;
            if (shop.PlatformType == "Taobao")
                ych_sdk.YchRequestLogger.Login(shop.Id.ToString(), shop.ShopId, true);
            else if (shop.PlatformType == PlatformType.Jingdong.ToString())
                jos_sdk_net.JdRequestLogger.Login(shop.AccessToken, shop.Id.ToString(), shop.ShopId, true);

            var printExpressAllRspLog = _commonSettingService.GetBool(PrintExpressAllRspLogKey, SiteContext.Current.CurrentShopId);
            ViewBag.PrintExpressAllRspLog = printExpressAllRspLog ? 1 : 0;
            // 加载默认配置
            LoadDefaultConfig(false);
            return View();
        }

        public new void LoadDefaultConfig(bool isCustomerOrder)
        {
            var currShop = SiteContext.Current.CurrentLoginShop;
            var isUseOldTheme = false;// SiteContext.Current?.IsUseOldTheme ?? false;
            var platformType = isUseOldTheme ? "Old" : SiteContext.Current.CurrentLoginShop.PlatformType;

            var keys = new List<string> {
                "OrderCategorySet",
                "BottomMoreFunSets_Fds",
                "ProductShowStyleSet",
                "PaggingOrderBySet",
                "CustomExtraConditionsSet",
                "CustomConditionSet_Fds",
                "CutomColumnsSet_Fds",
                "CutomAdvancedContidionSet",
                "CustomOrderExportSet"
            };
            var commSets = _commonSettingService.GetSets(keys, currShop.Id);
            var paggingOrderBySet = commSets?.FirstOrDefault(m => m.Key == "PaggingOrderBySet");
            var customExtraConditionsSet = commSets?.FirstOrDefault(m => m.Key == "CustomExtraConditionsSet");
            var customConditionSet = commSets?.FirstOrDefault(m => m.Key == "CustomConditionSet_Fds");
            var productShowStyleSet = commSets?.FirstOrDefault(m => m.Key == "ProductShowStyleSet");
            var orderCategorysSetting = commSets?.FirstOrDefault(m => m.Key == "OrderCategorySet");
            var bottomMoreFunSets = commSets.FirstOrDefault(m => m.Key == "BottomMoreFunSets_Fds");
            //var orderTimeDefaultSet = commSets.FirstOrDefault(m => m.Key == "OrderTimeDefaultSet");
            var cutomColumnsSet = commSets.FirstOrDefault(m => m.Key == "CutomColumnsSet_Fds");
            var cutomAdvancedContidionSet = commSets.FirstOrDefault(m => m.Key == "CutomAdvancedContidionSet");
            var customOrderExportSet = commSets.FirstOrDefault(m => m.Key == "CustomOrderExportSet");

            var defaultCustomExtraConditionsSetVal = customExtraConditionsSet?.Value.ToString2() ?? "";
            var defaultCutomAdvancedContidionSetVal = cutomAdvancedContidionSet?.Value.ToString2() ?? "";

            var defaultPaggingOrderBySetVal = paggingOrderBySet?.Value.ToString2() ?? "";
            if (defaultPaggingOrderBySetVal.IsNullOrEmpty())
            {
                defaultPaggingOrderBySetVal = GetDefaultSetting(platformType, "PaggingOrderBySet");
            }

            var defaultCustomConditionSetVal = customConditionSet?.Value.ToString2() ?? "";
            if (defaultCustomConditionSetVal.IsNullOrEmpty() || isUseOldTheme)
            {
                defaultCustomConditionSetVal = GetDefaultSetting(platformType, "CustomConditionSet_Fds");
            }

            var defaultProductShowStyleSetVal = productShowStyleSet?.Value.ToString2() ?? "";
            if (defaultProductShowStyleSetVal.IsNullOrEmpty() || isUseOldTheme)
            {
                defaultProductShowStyleSetVal = GetDefaultSetting(platformType, "ProductShowStyleSet");
            }

            var defaultOrderCategorysSetVal = orderCategorysSetting?.Value.ToString2() ?? "";
            if (defaultOrderCategorysSetVal.IsNullOrEmpty())
            {
                defaultOrderCategorysSetVal = GetDefaultSetting(platformType, "OrderCategorySet");
            }

            var defaultBottomMoreFunSetsVal = bottomMoreFunSets?.Value.ToString2() ?? "";
            if (defaultBottomMoreFunSetsVal.IsNullOrEmpty() || isUseOldTheme)
            {
                defaultBottomMoreFunSetsVal = GetDefaultSetting(platformType, "BottomMoreFunSets_Fds");
            }

            var defaultCutomColumnsSetVal = cutomColumnsSet?.Value.ToString2() ?? "";
            if (defaultCutomColumnsSetVal.IsNullOrEmpty() || isUseOldTheme)
            {
                defaultCutomColumnsSetVal = GetDefaultSetting(platformType, "CutomColumnsSet_Fds");
            }

            var defaultCustomOrderExportSetVal = customOrderExportSet?.Value.ToString2() ?? "";
            if (defaultCustomOrderExportSetVal.IsNullOrEmpty() || isUseOldTheme)
            {
                defaultCustomOrderExportSetVal = GetDefaultSetting(platformType, "CustomOrderExportSet");
            }

            var areaCodeInfos = _areaCodeInfoService.GetTreeAreaInfoList();

            ViewBag.AreaCodeInfos = areaCodeInfos?.ToJson() ?? "";
            ViewBag.Shops = SiteContext.Current.AllShops?.Where(m => m.PlatformType == currShop.PlatformType).ToList().ToJson() ?? "";
            ViewBag.OrderCategorys = defaultOrderCategorysSetVal;

            ViewBag.BottomMoreFunSets = defaultBottomMoreFunSetsVal;
            ViewBag.PaggingOrderBySet = defaultPaggingOrderBySetVal;
            ViewBag.CustomExtraConditionsSet = defaultCustomExtraConditionsSetVal;
            ViewBag.ProductShowStyleSet = defaultProductShowStyleSetVal;
            ViewBag.CustomConditionSet = defaultCustomConditionSetVal;
            ViewBag.CutomColumnsSet = defaultCutomColumnsSetVal;
            ViewBag.CutomAdvancedContidionSet = defaultCutomAdvancedContidionSetVal;
            ViewBag.CustomOrderExportSet = defaultCustomOrderExportSetVal;
        }

        public new string GetDefaultSetting(string platformType, string key)
        {
            var ptKey = $"Default_{platformType}_{key}";
            key = $"Default_{key}";
            var keys = new List<string> { key, ptKey };
            var commSets = _commonSettingService.GetSets(keys, 0);
            var commonSet = commSets.FirstOrDefault(m => m.Key == ptKey);
            if (commonSet == null)
                commonSet = commSets.FirstOrDefault(m => m.Key == key);
            return commonSet?.Value.ToString2() ?? "";
        }

        #region 发货
        [LogForOperatorFilter("批量发货")]
        public new ActionResult OnlineSend(OnlineSendRequestModel model)
        {
            LogForOperatorContext.Current.logInfo.Request = model;
            //排除退款的订单订单项
            if (model.ExcludeOrders != null && model.ExcludeOrders.Any())
            {
                var temps = new List<OrderRequestModel>();
                model.ExcludeOrders.ForEach(r =>
                {
                    var cur = model.Orders.FirstOrDefault(x => x.Id == r.Id);
                    if (cur != null)
                    {
                        cur.OrderItems = cur.OrderItems.Where(y => !r.RefundItems.Contains(y))?.ToList();
                        if (cur.OrderItems != null && cur.OrderItems.Any())
                            temps.Add(cur);
                    }
                });
                model.Orders = temps;
            }
            if (model.Orders == null || !model.Orders.Any())
                throw new LogicException("当前选中的订单都不是待发货状态，已取消发货");
            var logInfo = LogForOperatorContext.Current.logInfo;
            //logInfo.Detail =new LogForOnlineSendDetailModel(model);
            logInfo.TotalCount = model.Orders.Count();
            var results = MyOrderService.OnlineSend(model);
            logInfo.SuccessCount = results.Count(r => r.IsSuccess);
            logInfo.Exception = logInfo.SuccessCount < logInfo.TotalCount ? "Error" : "";
            //ThreadPool.QueueUserWorkItem(state =>
            //{
            //    try
            //    {
            //        _mergerOrderService.SplitOrderWhenPlatformStatusNotSame();
            //    }
            //    catch (Exception ex)
            //    {
            //        Log.WriteError($"发货后检查拆单时发生错误：{ex}");
            //    }
            //});
            var result = new DeliverySendResponseModel
            {
                Orders = results,
                SuccessCount = results?.Where(r => r.IsSuccess).Count(),
                ErrorCount = results?.Where(r => !r.IsSuccess || !string.IsNullOrEmpty(r.ErrorMessage)).Count()
            };
            LogForOperatorContext.Current.logInfo.Response = result;
            return Json(result);
        }
        #endregion        
    }
}