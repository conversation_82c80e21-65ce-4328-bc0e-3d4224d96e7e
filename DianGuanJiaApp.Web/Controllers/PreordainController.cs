using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DianGuanJiaApp.Data.Enum;

namespace DianGuanJiaApp.Controllers
{
    public class PreordainController : BaseController
    {
        private PreordainService _preordainService;
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private ExportTaskService _taskService = new ExportTaskService();

        public PreordainService PreordainService
        {
            get
            {
                if (_preordainService == null)
                {
                    _preordainService = new PreordainService();
                }
                return _preordainService;
            }

            set
            {
                _preordainService = value;
            }
        }

        public ActionResult Index()
        {
            ViewBag.UpdateIntervalTime = CustomerConfig.LogisticUpdateIntervalTime;
            ViewBag.FromOrderPrint = Request["FromOrderPrint"].ToInt();
            var shopList = new List<Shop> { SiteContext.Current.CurrentLoginShop };
            var currShop = SiteContext.Current.CurrentLoginShop;
            var selectItemList = new List<SelectListItem>();
            if (SiteContext.Current.ChildShop != null && SiteContext.Current.ChildShop.Count > 0)
            {
                selectItemList.Add(new SelectListItem()
                {
                    Text = "==所有店铺==",
                    Value = 0.ToString(),
                });
                shopList.AddRange(SiteContext.Current.ChildShop);
            }

            shopList.ForEach(item =>
            {
                if (currShop.PlatformType == item.PlatformType)
                    selectItemList.Add(new SelectListItem() { Text = item.NickName, Value = item.Id.ToString() });

            });


            var keys = new List<string> { "/Preordain/LogisticUpdateStartTime", "PreordainColumnsSet" };
            var commSets = _commonSettingService.GetSets(keys, currShop.Id);
            //列表设置
            var colSet = commSets.FirstOrDefault(m => m.Key == "PreordainColumnsSet");
            ViewBag.ColumnsSetting = colSet?.Value.ToString2();
            //最后导出时间
            var lastUpdateTimeSet = commSets.FirstOrDefault(m => m.Key == "/Preordain/LogisticUpdateStartTime");
            var defaultLastUpdateTime = lastUpdateTimeSet?.Value.ToDateTime() ?? null;
            var defaultLastUpdateTimeSetVal = defaultLastUpdateTime == null ? "" : defaultLastUpdateTime.Value.ToString("yyyy-MM-ddTHH:mm:ss");
            // 默认导出间隔时长（300s）
            ViewBag.LastUpdateTimeSet = defaultLastUpdateTimeSetVal;
            ViewBag.UpdateIntervalSeconds = _commonSettingService.GetLogisticUpdateIntervalSeconds();
            //前台获取导出任务
            var exportTask = _taskService.GetExportTask(currShop.Id, ExportType.Preordain.ToInt());
            // 显示未完成任务或已完成一天前的导出任务
            exportTask = exportTask != null && exportTask.Status >= 0 && exportTask.Status <= 4 ? exportTask : null;
            var task = GetExportTaskToWeb(exportTask);
            ViewBag.PreordainLogisticInfoUpdateTask = task?.ToJson() ?? "null";
            return View(selectItemList);
        }

        /// <summary>
        /// 加载列表数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        public ActionResult LoadList(PreordainListRequestModel requestModel)
        {

            if (requestModel.ShopId != null && requestModel.ShopId.Count > 0)
            {
                var shopIds = requestModel.ShopId.Where(f => f != 0).ToList();
                if (shopIds.Count == 0)
                    shopIds = SiteContext.Current.SamePlatformShopIds;
                requestModel.ShopId = shopIds;
            }
            else
            {
                requestModel.ShopId = SiteContext.Current.SamePlatformShopIds;
            }

            var pageModel = PreordainService.LoadList(requestModel);
            var shop = SiteContext.Current.CurrentLoginShop;
            if (shop.PlatformType == Data.Enum.PlatformType.Jingdong.ToString()
                || shop.PlatformType == Data.Enum.PlatformType.Taobao.ToString())
            {
                var pids = new List<string>();
                pageModel.Rows?.ForEach(item =>
                {
                    pids.Add(item.PlatformOrderId.Trim('C'));
                    item.ToMobile = item.ToMobile.ToEncrytPhone();
                    item.ToPhone = item.ToPhone.ToEncrytPhone();
                    item.Reciver = item.Reciver.ToEncryptName();
                    item.BuyerMemberName = item.BuyerMemberName.ToEncryptName();
                    item.ReciverAddress = item.ReciverAddress.ToTaoBaoEncryptAddress();
                    //if (shop.PlatformType == Data.Enum.PlatformType.Taobao.ToString()) { 
                    //    item.s
                    //}
                });
                jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
            }
            else if (shop.PlatformType == Data.Enum.PlatformType.Pinduoduo.ToString())
            {
                var tempOrders = pageModel.Rows?.Select(x => new Order { PlatformOrderId = x.PlatformOrderId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ToMobile, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToCounty, ToAddress = x.ReciverAddress }).ToList();
                MyOrderService.TryToDecryptPddOrders(tempOrders);
                //按店铺分组
                pageModel.Rows?.GroupBy(x => x.ShopId).ToList().ForEach(g =>
                {
                    foreach (var item in g)
                    {
                        var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.PlatformOrderId && x.ShopId == item.ShopId);
                        if (decryptedOrder != null)
                        {
                            item.Reciver = decryptedOrder.ToName;
                            item.ToMobile = decryptedOrder.ToMobile;
                            item.ToPhone = item.ToMobile;
                            item.BuyerMemberName = item.Reciver;
                            item.ReciverAddress = decryptedOrder.ToFullAddress;
                        }
                    }
                });
                pageModel.Rows?.ForEach(t =>
                {
                    t.ToPhone = t.ToPhone.ToEncrytPhone();
                    t.ToMobile = t.ToMobile.ToEncrytPhone();
                    t.BuyerMemberName = t.BuyerMemberName.ToEncryptName();
                    t.Reciver = t.Reciver.ToEncryptName();
                    t.ReciverAddress = t.ReciverAddress.ToPddEncryptAddress();
                });
            }
            return Json(pageModel);
        }

        /// <summary>
        /// 获取满足条件的预发货订单 
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        private List<PreordainViewModel> GetPreordainList(PreordainListRequestModel requestModel)
        {
            if (requestModel.ShopId != null && requestModel.ShopId.Count > 0)
            {
                var shopIds = requestModel.ShopId.Where(f => f != 0).ToList();
                if (shopIds.Count == 0)
                {
                    shopIds = SiteContext.Current.ShopIds;
                }
                requestModel.ShopId = shopIds;
            }
            else
            {
                var shopList = new List<Shop> { SiteContext.Current.CurrentLoginShop };
                if (SiteContext.Current.ChildShop != null && SiteContext.Current.ChildShop.Count > 0)
                {
                    SiteContext.Current.ChildShop.ForEach(item =>
                    {
                        if (SiteContext.Current.CurrentLoginShop.PlatformType == item.PlatformType)
                            shopList.Add(item);
                    });
                }
                requestModel.ShopId = shopList.Select(f => f.Id).ToList();
            }

            var list = new List<PreordainViewModel>();
            requestModel.PageSize = 1000;
            requestModel.PageIndex = 1;
            var returnSize = requestModel.PageSize;
            while (returnSize == requestModel.PageSize)
            {
                var pageModel = PreordainService.LoadList(requestModel);
                returnSize = pageModel?.Rows?.Count ?? 0;
                requestModel.PageIndex++;
                var rows = pageModel?.Rows ?? new List<PreordainViewModel>();
                if (rows.Any())
                {
                    list.AddRange(pageModel.Rows);
                }
            }
            return list;
        }
        
        /// <summary>
        /// 获取即将超时订单数量（目前针对拼多多平台）
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="hours"></param>
        /// <returns></returns>
        public ActionResult GetSoonTimeOutOrders()
        {
            PreordainListRequestModel requestModel = new PreordainListRequestModel
            {
                ShopId = SiteContext.Current.SamePlatformShopIds,
                ResidueTime = 6,
                PageIndex = 1,
                PageSize = 50
            };
            var pageModel = PreordainService.LoadList(requestModel);
            return Json(pageModel);
        }

        /// <summary>
        /// 获取即将超时订单数量（目前针对拼多多平台）
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="hours"></param>
        /// <returns></returns>
        public ActionResult GetSoonTimeOutOrderCount()
        {
            var shopIds = SiteContext.Current.SamePlatformShopIds;
            var count = PreordainService.GetSoonTimeOutOrderCount(shopIds);
            return SuccessResult(count.ToString());
        }

        /// <summary>
        /// 添加预发货订单
        /// </summary>
        /// <param name="addPreordainOrderIds">需要添加预发货的订单id</param>
        /// <param name="updateIsPreordainOrderIds">需要修改状态为预发货的订单id（包含合并订单）</param>
        /// <param name="waybillCode">运单</param>
        /// <param name="templateId">模板id</param>
        /// <returns></returns>
        public ActionResult AddPreordainOrder(AddPreordainRequestModel model)
        {
            var result = PreordainService.AddPreordain(model);
            if (result)
                return SuccessResult();
            else
                return FalidResult("添加预发货失败");
        }

        /// <summary>
        /// 编辑预发货
        /// </summary>
        /// <param name="preordainVm"></param>
        /// <returns></returns>
        public ActionResult UpdatePreordainOrder(PreordainViewModel preordainVm)
        {
            var result = PreordainService.UpdatePreordain(preordainVm);
            if (result)
                return SuccessResult();
            else
                return FalidResult("修改失败");
        }

        /// <summary>
        /// 删除预发货
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public ActionResult DeletePreordain(List<OrderSelectKeyModel> oskmList)
        {
            var result = PreordainService.DeletePreordain(oskmList);
            if (result)
                return SuccessResult();
            else
                return FalidResult("删除失败");
        }

        /// <summary>
        /// 预发货订单发货
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public ActionResult SendPreordain(List<OrderSelectKeyModel> oskmList)
        {

            var results = PreordainService.SendPreordain(oskmList);

            return Json(new
            {
                Orders = results,
                SuccessCount = results?.Where(r => r.IsSuccess).Count(),
                ErrorCount = results?.Where(r => !r.IsSuccess).Count()
            });
        }

        /// <summary>
        /// 更新物流信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public ActionResult UpdateLogisticTraces(PreordainListRequestModel requestModel)
        {
            var curShop = SiteContext.Current.CurrentLoginShop;
            //ExportTaskService _taskService = new ExportTaskService();
            //var execSql = PreordainService.GetExecSql(requestModel);

            //物流轨迹启用状态
            var sets = _commonSettingService.GetSets(new List<string> { "OrderLogisticTracesSet" }, curShop.Id);
            var enableSet = sets.FirstOrDefault(x => x.Key == "OrderLogisticTracesSet")?.Value.ToObject<WarningSetModel>(); ;
            if (enableSet == null || enableSet.SubjectSet == 0)
                return FalidResult("请先启动物流预警后再查询");

            var set = _commonSettingService.Get("UpdateLogisticDefaultPageSize", 0);
            var pageSize = set != null && set.Value.ToInt() > 0 ? set.Value.ToInt() : 100;

            var task = new ExportTask
            {
                IP = Request.UserHostAddress,
                CreateTime = DateTime.Now,
                Status = 0,
                //ExecSql = execSql,
                ParamJson = requestModel.ToJson(),
                Type = ExportType.Preordain.ToInt(),
                PageSize = pageSize,
                PageIndex = 1,
                PlatformType = curShop.PlatformType,
                ShopId = curShop.Id,
                UserId = SiteContext.Current.CurrentLoginSubUser?.Id.ToString(),
                FromModule = "预发货-物流轨迹更新",
            };
            var newId = _taskService.Add(task);
            task.Id = newId;
            return Json(GetExportTaskToWeb(task));

            //var list = GetPreordainList(requestModel);
            //if (list == null || list.Count == 0)
            //{
            //    return FalidResult("无预发货订单");
            //}

            //if (list.Count > CustomerConfig.MaxUpdateLogisticSyncCount)
            //{
            //    ExportTaskService _taskService = new ExportTaskService();
            //    //var execSql = PreordainService.GetExecSql(requestModel);
            //    var task = new ExportTask
            //    {
            //        CreateTime = DateTime.Now,
            //        ShopId = SiteContext.Current.CurrentShopId,
            //        Status = 0,
            //        //ExecSql = execSql,
            //        ParamJson = requestModel.ToJson(),
            //        Type = 7,
            //        PageSize = 50
            //    };
            //    var newId = _taskService.Add(task);
            //    task.Id = newId;
            //    return Json(task);
            //}

            //var result = PreordainService.UpdateLogisticTraces(list);
            //return Json(result);
        }

        public SyncQueryTask UpdateLogisticTraces2(PreordainListRequestModel requestModel)
        {
            SyncQueryTaskService _taskService = new SyncQueryTaskService();
            var execSql = PreordainService.GetExecSql(requestModel);
            var task = new SyncQueryTask
            {
                CreateTime = DateTime.Now,
                ShopId = SiteContext.Current.CurrentShopId,
                Status = 0,
                ExecSql = execSql,
                Type = 2,
                PageSize = 50
            };
            var newId = _taskService.Add(task);
            task.Id = newId;
            return task;
        }
    }
}