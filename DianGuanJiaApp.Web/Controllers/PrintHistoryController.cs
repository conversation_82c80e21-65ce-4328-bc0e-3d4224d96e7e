using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.Controllers
{
    public class PrintHistoryController : BaseController
    {
        private PrintHistoryService _service = new PrintHistoryService();
        private WaybillCodeService _wayBillCodeService = new WaybillCodeService();
        public ActionResult Index()
        {
            var shopList = new List<Shop>();
            var currShop = SiteContext.Current.CurrentLoginShop;
            var selectItemList = new List<SelectListItem>();
            if (SiteContext.Current.AllShops != null && SiteContext.Current.AllShops.Count > 1)
            {
                selectItemList.Add(new SelectListItem()
                {
                    Text = "==所有店铺==",
                    Value = 0.ToString(),
                });

                shopList.AddRange(SiteContext.Current.AllShops?.Where(m => m.PlatformType == currShop.PlatformType).ToList());
            }
            else
            {
                shopList.Add(currShop);
            }

            shopList.ForEach(item =>
            {
                if (currShop.PlatformType == item.PlatformType)
                    selectItemList.Add(new SelectListItem() { Text = item.NickName, Value = item.Id.ToString() });

            });

            return View(selectItemList);
        }

        /// <summary>
        /// 加载发往的省份
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadProvinces(string shopId)
        {
            var shopIds = new List<int>();
            if (string.IsNullOrEmpty(shopId) || shopId == "0")
            {
                SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
                {
                    shopIds.Add(item.Id);
                });
            }
            else
            {
                shopIds.Add(shopId.ToInt());
            }
            var provinces = _service.GetToProvinces(shopIds);
            var selectItemList = new List<SelectListItem>() { new SelectListItem() { Text = "==所有省份==", Value = "0" } };
            if (provinces != null && provinces.Count > 0)
            {
                provinces.ForEach(item =>
                {
                    if (string.IsNullOrWhiteSpace(item) == false)
                        selectItemList.Add(new SelectListItem() { Text = item, Value = item });
                });
            }
            return Json(selectItemList);
        }

        /// <summary>
        /// 加载打印过的模板名称
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("打印记录查询模板条件")]
        public ActionResult LoadTemplateNames(string shopId, int printType, string sDate, string eDate)
        {
            var shopIds = new List<int>();

            if (string.IsNullOrEmpty(shopId) || shopId == "0")
            {
                SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
                {
                    shopIds.Add(item.Id);
                });
            }
            else
            {
                shopIds.Add(shopId.ToInt());
            }

            var dataList = _service.GetTemplateNames(shopIds, printType, sDate, eDate);
            var selectItemList = new List<SelectListItem>() { new SelectListItem() { Text = "==所有模板==", Value = "0" } };
            if (dataList != null && dataList.Count > 0)
            {
                dataList.ForEach(item =>
                {
                    selectItemList.Add(new SelectListItem() { Text = item.ExpressPic + "-" + item.TemplateName, Value = item.Id.ToString() });
                });
            }
            return Json(selectItemList);
        }


        /// <summary>
        /// 加载买家数/订单数
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("打印记录查询数据统计")]
        public ActionResult LoadStatisticsCount(PrintHistoryRequestModel requestModel)
        {
            if (requestModel.ShopId != null && requestModel.ShopId.Count > 0)
            {
                var shopIds = requestModel.ShopId.Where(f => f != 0).ToList();
                if (shopIds.Count == 0)
                {
                    SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
                    {
                        shopIds.Add(item.Id);
                    });
                }
                requestModel.ShopId = shopIds;
            }

            var countResult = _service.StatisticsCount(requestModel);
            var result = new { BuyerCount = countResult.Item1, OrderCount = countResult.Item2 };
            return Json(result);
        }

        /// <summary>
        /// 加载列表数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        [LogForOperatorFilter("打印记录查询列表")]
        public ActionResult LoadList(PrintHistoryRequestModel requestModel)
        {

            if (requestModel.ShopId != null && requestModel.ShopId.Count > 0)
            {
                var shopIds = requestModel.ShopId.Where(f => f != 0).ToList();
                if (shopIds.Count == 0)
                {
                    SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
                    {
                        shopIds.Add(item.Id);
                    });
                }
                requestModel.ShopId = shopIds;
            }
            requestModel.IsCrossBorderSite = CustomerConfig.IsCrossBorderSite;
            var pageModel = _service.LoadList(requestModel);
            var shop = SiteContext.Current.CurrentLoginShop;
            if (shop.PlatformType == PlatformType.Jingdong.ToString()||shop.PlatformType== PlatformType.Taobao.ToString())
            {
                var pids = new List<string>();
                pageModel.Rows?.ForEach(t => {
                    if (!string.IsNullOrEmpty(t.PlatformOrderJoin))
                    {
                        pids.AddRange(t.PlatformOrderJoin.Split(','));
                    }
                    else
                    {
                        pids.Add(t.PlatformOrderId.Trim('C'));
                    }
                    t.ReciverPhone = t.ReciverPhone.ToEncrytPhone(); 
                    t.Reciver = t.Reciver.ToEncryptName();
                    t.ReciverAddress = t.ReciverAddress.ToTaoBaoEncryptAddress();
                    if (shop.PlatformType == PlatformType.Taobao.ToString())
                    {
                        //淘宝还需加密发件人信息
                        t.Sender = t.Sender.ToEncryptName();
                        t.SenderPhone = t.SenderPhone.ToEncrytPhone();
                    }
                });

                if (shop.PlatformType == PlatformType.Jingdong.ToString())
                {   //京东安全日志
                    jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
                }
                else
                {
                    //记御城河日志
                    ych_sdk.YchRequestLogger.Order(shop.Id.ToString(), "订单查询", pids);
                }
            }
            else if(shop.PlatformType == Data.Enum.PlatformType.Pinduoduo.ToString())
            {
                var tempOrders = pageModel.Rows?.Select(x => new Order { PlatformOrderId = x.PlatformOrderId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ReciverAddress }).ToList();
                MyOrderService.TryToDecryptPddOrders(tempOrders);
                //按店铺分组
                pageModel.Rows?.GroupBy(x => x.ShopId).ToList().ForEach(g => {
                    foreach (var item in g)
                    {
                        var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.PlatformOrderId && x.ShopId == item.ShopId);
                        if (decryptedOrder != null)
                        {
                            item.Reciver = decryptedOrder.ToName;
                            item.ReciverPhone = decryptedOrder.ToMobile;
                            item.BuyerMemberName = item.Reciver;
                            item.BuyerMemberId = item.Reciver;
                            item.ReciverAddress = decryptedOrder.ToFullAddress;
                        }
                    }
                });
                pageModel.Rows?.ForEach(w => {
                    w.ReciverPhone = w.ReciverPhone.ToEncrytPhone();
                    w.BuyerMemberName = w.BuyerMemberName.ToEncryptName();
                    w.BuyerMemberId = w.BuyerMemberId.ToEncryptName();
                    w.Reciver = w.Reciver.ToEncryptName();
                    w.ReciverAddress = w.ReciverAddress.ToPddEncryptAddress();
                    w.ReciverPhone = w.ReciverPhone.ToPddEncryptPhone();
                });
            }
            return Json(pageModel);
        }

        /// <summary>
        /// 保存打印记录
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult SavelPrintHistory(PrintHistory model)
        {
            var result = _service.SavelPrintHistory(model);

            if (result == true)
                return SuccessResult();
            else
                return FalidResult("打印记录保存失败");
        }

        /// <summary>
        /// 批量保存打印记录
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult SavelPrintHistoryList(List<PrintHistory> models)
        {
            if (models == null && models.Any() == false) return FalidResult("后台未收到需要保存的打印记录数据");
            try
            {
                models.ForEach(item =>
                {
                    _service.SavelPrintHistory(item);
                });
            }
            catch (Exception ex)
            {
                Log.WriteError(ex.ToString());
                return FalidResult("打印记录保存失败");
            }
            return SuccessResult();
        }

        /// <summary>
        /// 加载打印记录未确认的数据
        /// </summary>
        /// <returns></returns>
        public ActionResult GetWaitConfirmPrintHistoryList()
        {
            var shopIds = SiteContext.Current.ShopIds;
            var result = _service.GetWaitConfirmPrintHistoryList(shopIds, base.IsCustomerOrder);
            return Json(result);
        }

        /// <summary>
        /// 加载回收状态，及需要的数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        public ActionResult LoadCancelData(List<PrintHistory> requestModel) {

            var result = _wayBillCodeService.GetWaybillCodeList(requestModel);
            return SuccessResult(result);
        }

        /// <summary>
        /// 加载打印数据
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadPrintData(List<long> printHistoryIds) {
            var list = _service.GetListByIds(printHistoryIds);
            return SuccessResult(list);
        }
    }
}