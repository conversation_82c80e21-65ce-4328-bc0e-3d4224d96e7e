using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.EntityExtension;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Web;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.Controllers
{
    public class ScanPrintController : BaseController
    {
        ScanPrintService _scanPrintService;
        ShopService shopService = new ShopService();
        public ActionResult Index()
        {
            return Json(new { Message = "Hello World" }, JsonRequestBehavior.AllowGet);
        }

        [LogForOperatorFilter("扫描订单查询")]
        public ActionResult QueryOrder(string orderId, bool IsCustomerOrder)
        {
            var api = "/ScanPrintApi/Scan";
            var pddApiUrl = CustomerConfig.PinduoduoNewSystemLink + api;
            var jdApiUrl = CustomerConfig.JingDongNewSystemLink + api;
            var aliApiUrl = CustomerConfig.AlibabaNewSystemLink + api;

            if (CustomerConfig.IsDebug)
            {
                pddApiUrl = "http://testpdd.dgjapp.com" + api;
                jdApiUrl = "http://testjd.dgjapp.com" + api;
                aliApiUrl = "http://testother.dgjapp.com" + api;
            }

            ScanQueryResultModel result = null;
            var currentShop = SiteContext.Current.CurrentLoginShop;

            var allShops = shopService.GetAllShops();
            var pddShop = allShops.Where(f => f.PlatformType == PlatformType.Pinduoduo.ToString());
            var jdShop = allShops.Where(f => f.PlatformType == PlatformType.Jingdong.ToString());
            var youZanShop = allShops.Where(f => f.PlatformType == PlatformType.YouZan.ToString());

            //全平台查询 分三个rds 阿里 拼多多 京东
            //扫描平台订单，先根据单号猜一下订单平台
            if (IsCustomerOrder == false)
            {
                //1.拼多多单号中间有横杠“-”,有拼多多子店铺，且当前登录店铺不为拼多多店铺，则走api查询
                if (orderId.IndexOf("-") > 0 && pddShop.Any())
                {
                    var sids = pddShop.Select(f => f.Id).ToList();
                    if (currentShop.PlatformType != PlatformType.Pinduoduo.ToString())
                        result = QueryOrderByApi(pddApiUrl, orderId, IsCustomerOrder, sids);
                    else
                        result = QueryOrderByLoginShop(orderId, IsCustomerOrder, sids);
                }
                else if (orderId.StartsWith("E") && youZanShop.Any())
                {
                    var sids = youZanShop.Select(f => f.Id).ToList();
                    if (currentShop.PlatformType != PlatformType.YouZan.ToString())
                        result = QueryOrderByApi(aliApiUrl, orderId, IsCustomerOrder, sids);
                    else
                        result = QueryOrderByLoginShop(orderId, IsCustomerOrder, sids);
                }
            }

            if (result != null)
                return SuccessResult(result);

            //1.先从当前登录店铺同平台的所有店铺下查询订单
            var sidByCurrentShop = allShops.Where(f => f.PlatformType == currentShop.PlatformType).Select(f => f.Id).ToList();
            result = QueryOrderByLoginShop(orderId, IsCustomerOrder, sidByCurrentShop);

            if (result == null)
            {
                //2.没查到，再并发去非当前登录店铺平台查询
                var childShops = allShops.Where(f => f.PlatformType != currentShop.PlatformType).GroupBy(f => f.PlatformType);
                ConcurrentBag<ScanQueryResultModel> apiResultList = new ConcurrentBag<ScanQueryResultModel>();
                Parallel.ForEach(childShops, new ParallelOptions { MaxDegreeOfParallelism = 5 }, (item, state) =>
                {
                    if (apiResultList.Any())
                    {
                        state.Stop();
                        return;
                    }

                    var sids = item.Select(f => f.Id).ToList();
                    var pt = item.Key;

                    var apiUrl = GetApiUrl(pt, api);
                    var tempResult = QueryOrderByApi(apiUrl, orderId, IsCustomerOrder, sids);
                    apiResultList.Add(tempResult);

                    if (apiResultList.Any())
                    {
                        state.Stop();
                        return;
                    }
                });

                if (apiResultList.Any(f => f != null))
                {
                    result = apiResultList.First(f => f != null);
                }
            }

            var tempIds = result?.OrderData?.Select(x => x.ShopId).Distinct().ToList();
            var tempJsonResult = CheckAlibabaShopVersionControl(tempIds, "ScanPrint", "QueryOrder");
            if (tempJsonResult != null)
                return Json(tempJsonResult);

            if (result == null)
                return FalidResult("订单未找到，请确认订单是否属于当前店铺或者关联店铺的订单！");
            var shopIds = result.OrderData?.Select(x => x.ShopId).Distinct().ToList();
            var tempJson = CheckAlibabaShopVersionControl(shopIds, "ScanPrint", "QueryOrder");
            if (tempJson != null)
                return SuccessResult(tempJson);
            return SuccessResult(result);
        }

        private ScanQueryResultModel QueryOrderByLoginShop(string orderId, bool isCustomerOrder, List<int> shopIds)
        {
            //日志开始
            var subLog = LogForOperatorContext.Current.StartStep(new LogForOperator()
            {
                OperatorType = "在当前登录店铺和同平台子店铺中查询订单"
            });
            subLog.Request = new { OrderId = orderId, IsCustomerOrder = isCustomerOrder, Sids = shopIds };

            ScanQueryResultModel result;
            try
            {
                _scanPrintService = new ScanPrintService();
                result = _scanPrintService.QueryOrder(orderId, IsCustomerOrder, shopIds);
            }
            catch (Exception ex)
            {
                Log.WriteError($"扫描订单,错误详情：{ex}");
                throw new LogicException("服务器繁忙，请稍后再试！");
            }

            //日志结束
            LogForOperatorContext.Current.EndStep(subLog);

            return result;
        }

        private ScanQueryResultModel QueryOrderByApi(string apiUrl, string orderId, bool isCustomerOrder, List<int> shopIds)
        {
            return Common.PostApi<ScanQueryModel, ScanQueryResultModel>(apiUrl,
                shopIds.First(),
                new ScanQueryModel { OrderId = orderId, IsCustomer = IsCustomerOrder, ShopId = shopIds },
                "通过api扫描查询");
        }


        [LogForOperatorFilter("扫描订单打印")]
        public ActionResult PrintOrder(ExpressPrintRequestModel model)
        {
            var api = "/ScanPrintApi/Print";
            var pddApiUrl = CustomerConfig.PinduoduoNewSystemLink + api;
            var jdApiUrl = CustomerConfig.JingDongNewSystemLink + api;
            var aliApiUrl = CustomerConfig.AlibabaNewSystemLink + api;

            if (CustomerConfig.IsDebug)
            {
                pddApiUrl = "http://testpdd.dgjapp.com" + api;
                jdApiUrl = "http://testjd.dgjapp.com" + api;
                aliApiUrl = "http://testother.dgjapp.com" + api;
            }

            if (model == null || model.Orders == null || model.Orders.Count == 0)
            {
                return FalidResult("参数错误");
            }

            var shop = shopService.Get(model.Orders.First().ShopId);
            if (shop.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType)
            {
                return (new OrderController(new OrderService())).ExpressPrint(model);
            }

            if (shop.PlatformType == PlatformType.Pinduoduo.ToString())
            {
                return PrintOrderByApi(pddApiUrl, model, shop.Id);
            }
            else if (shop.PlatformType == PlatformType.Jingdong.ToString())
            {
                return PrintOrderByApi(jdApiUrl, model, shop.Id);
            }
            else
            {
                return PrintOrderByApi(aliApiUrl, model, shop.Id);
            }
        }

        private ActionResult PrintOrderByApi(string apiUrl, ExpressPrintRequestModel model, int shopId)
        {
            var apiResult = Common.PostApi<ExpressPrintRequestModel, AjaxResult>(apiUrl, shopId, model, "通过Api到子店铺站点打印");
            if (apiResult != null)
                return new CustomJsonResult(apiResult);
            else
                return FalidResult("打印出错");
        }



        [LogForOperatorFilter("扫描订单打印回调")]
        public ActionResult PrintCallback(PrintHisotoryCallbackRequestModel model)
        {
            var api = "/ScanPrintApi/PrintCallback";

            if (model == null || model.Orders == null || model.Orders.Count == 0)
            {
                return FalidResult("参数错误");
            }

            var shop = shopService.Get(model.Orders.First().ShopId);
            if (shop.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType)
            {
                return (new OrderController(new OrderService())).PrintCallback(model);
            }

            var apiUrl = GetApiUrl(shop.PlatformType, api);
            return PrintCallbackByApi(apiUrl, model, shop.Id);
        }

        private ActionResult PrintCallbackByApi(string apiUrl, PrintHisotoryCallbackRequestModel model, int shopId)
        {
            var apiResult = Common.PostApi<PrintHisotoryCallbackRequestModel, AjaxResult>(apiUrl, shopId, model, "通过api执行打印回调");
            if (apiResult != null)
                return new CustomJsonResult(apiResult);
            else
                return FalidResult("回调出错");

        }
    }
}