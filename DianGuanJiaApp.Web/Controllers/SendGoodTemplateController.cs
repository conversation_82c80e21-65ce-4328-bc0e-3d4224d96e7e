using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Model.ExceptionModel;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Net;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.Controllers
{
    public class SendGoodTemplateController : BaseController
    {
        private SendGoodTemplateService _service = new SendGoodTemplateService();
        private PrinterBindService _printerBindService = new PrinterBindService();
        private SellerInfoService _sellerInfoService = new SellerInfoService();
        //private OrderService _orderService = new OrderService();

        public ActionResult Index()
        {
            var hasPermission = SiteContext.HasPermission(FxPermission.SendGoodTemplate);
            //hasPermission = true;
            ViewBag.ViewPermission = hasPermission == true ? "true" : "false";
            if (!hasPermission) return View();
            return View();
        }

        public ActionResult Edit()
        {
            return View();
        }

        /// <summary>
        /// 文件上传
        /// </summary>
        /// <param name="fileName">文件名称，必填，需包含后缀</param>
        /// <param name="fileContent">文件内容（base64编码）</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult UploadImage(string fileName, string fileContent)
        {
            object result = null;
            try
            {
                //fileContent =_context.Server.UrlDecode(fileContent);
                if (string.IsNullOrEmpty(fileName))
                    throw new LogicException("文件名称不能为空");
                var name = fileName.ToLower();
                if (
                    !name.EndsWith(".jpg")
                    && !name.EndsWith(".png")
                    && !name.EndsWith(".gif")
                    && !name.EndsWith(".jpeg")
                    && !name.EndsWith(".bmp")
                    )
                    throw new LogicException("文件格式必须为：png / jpg / jpeg /gif");
                var para = new Dictionary<string, string> { { "fileName", fileName }, { "fileContent", fileContent }, { "memberId", SiteContext.Current.CurrentLoginShop.ShopId }, { "key", DES.EncryptDES(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "sda98yp3") } };
                result = WebRequestHelper.PostRequest(CustomerConfig.ImageServer + "/Image.ashx?FuncName=Upload&key=debug", para, "UTF-8");
            }
            catch (LogicException lex)
            {
                result = new { IsOk = false, Message = lex.Message };
            }
            catch (Exception ex)
            {
                Log.WriteError("上传图片时发生错误：" + ex.ToString());
                result = new { IsOk = false, Message = "图片服务器暂不可用，请使用图片链接" };
            }

            return OriginalJson(result);
        }

        /// <summary>
        /// 保存发货单模板
        /// </summary>
        /// <param name="templateName"></param>
        /// <param name="templateId"></param>
        /// <param name="config"></param>
        /// <returns></returns>
        public ActionResult Save(int templateId, string templateName, string config)
        {
            if (string.IsNullOrEmpty(templateName))
                throw new ApplicationException("模板名称不能为空");
            var id = _service.Save(templateId, templateName, config);
            return SuccessResult(id);
        }


        public ActionResult GetSendGoodTemplates()
        {
            var list = _service.GetTemplates();
            return SuccessResult(list);
        }

        /// <summary>
        /// 获取发货单打印数据
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("打印发货单")]
        public ActionResult PreparePrintData(PrintSendGoodTemplateRequestModel model)
        {
            try
            {
                _service.PrepareTemplateDataAndPrintData(model, Request, PageToken, out SendGoodTemplate template, out List<SendGoodTemplateDataModel> datas, out LogForOperator subLog_PrintData, out List<SendGoodTemplatePageModel> result);
                //日志结束
                LogForOperatorContext.Current.EndStep(subLog_PrintData);
                var phs = new List<long>();
                if (model.IsPrint)
                {
                    var hs = MyOrderService.LogFahuoPrintHistory(datas, model, template);
                    phs = hs?.Where(h => h.ID > 0)?.Select(h => h.ID).ToList();
                }
                var logInfo = LogForOperatorContext.Current.logInfo;
                logInfo.TotalCount = model?.Orders?.Count ?? 0;
                logInfo.SuccessCount = result?.Count ?? 0;
                logInfo.Exception = logInfo.SuccessCount < logInfo.TotalCount ? "Error" : "";

                return SuccessResult(new { PrintData = result, PrintHistoryIds = phs });
            }
            catch (OrderBalanceException ex)
            {
                return FalidResult(ex.Message, "ORDER_COUNT", new { OrderBalanceCount = ex.OrderBalanceCount });
            }
        }



        /// <summary>
        /// 获取发货单列表
        /// </summary>
        /// <returns></returns>
        public ActionResult List()
        {
            var templates = new List<SendGoodTemplate>();
            var printerBindings = _printerBindService.GetByType(2, SiteContext.Current.CurrentShopId);
            var tmps = _service.GetTemplates();
            tmps.ForEach(t =>
            {
                if (!t.IsOldTemplate)
                    t.SizeInfo = new SendGoodTemplateSizeInfo(t.Config);
                t.Config = "";
                t.DefaultPrinter = printerBindings?.FirstOrDefault(p => p.TemplateId == t.Id)?.PrinterName;
            });
            templates.AddRange(tmps);
            if (templates.Any() && !templates.Any(t => t.IsDefault))
                templates[0].IsDefault = true;
            return SuccessResult(templates);
        }

        public ActionResult GetTemplate(int templateId)
        {
            if (templateId <= 0)
                throw new LogicException("发货单模板ID有误，请检查");
            var template = _service.Get(templateId);
            if (template == null)
                throw new LogicException("您要编辑的发货单模板不存在或已被删除");
            var oldTempShopId = SiteContext.Current.SendGoodTemplateShopId;
            if (!template.IsSystemTemplate && !template.IsSystemTemplate && SiteContext.Current.ShopIds.Contains(template.ShopId) == false && template.ShopId != oldTempShopId)
                throw new LogicException("未能查询到当前发货单模板信息，请刷新重试");
            return SuccessResult(template);
        }

        /// <summary>
        /// 删除发货单模板
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns></returns>
        public ActionResult Delete(int templateId)
        {
            if (templateId <= 0)
                throw new LogicException("发货单模板ID有误，请检查");
            var template = _service.Get(templateId, false);
            if (template == null)
                throw new LogicException("您要删除的发货单模板不存在或已被删除");
            if (template.IsSystemTemplate)
                throw new LogicException("系统模板不允许删除");
            var oldTempShopId = SiteContext.Current.SendGoodTemplateShopId;
            if (SiteContext.Current.ShopIds.Contains(template.ShopId) == false && template.ShopId != oldTempShopId)
                throw new LogicException("未能查询到当前发货单模板信息，请刷新重试");
            template.IsDeleted = true;
            _service.Update(template);
            return SuccessResult();
        }

        /// <summary>
        /// 设置默认发货单模板
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <param name="isOldTemplate">是否是老版本模板</param>
        /// <returns></returns>
        public ActionResult SetAsDefault(int templateId, bool isOldTemplate)
        {
            if (templateId <= 0)
                throw new LogicException("发货单模板ID有误，请检查");
            var template = _service.Get(templateId, false);
            if (template == null && !isOldTemplate)
                throw new LogicException("您要设置的发货单模板不存在或已被删除");
            if (!isOldTemplate && template.IsSystemTemplate)
                throw new LogicException("当前模板为系统模板，不能设置为默认模板，请添加个人模板");
            var oldTempShopId = SiteContext.Current.SendGoodTemplateShopId;
            //越权校验
            if (template != null && SiteContext.Current.ShopIds.Contains(template.ShopId) == false && template.ShopId != oldTempShopId)
                throw new LogicException("未能查询到当前发货单模板信息，请刷新重试");
            if (template == null)
            {
                var oldTemplate = _service.GetOldSendGoodTemplate();
                if (oldTemplate == null || oldTemplate.ShopId != oldTempShopId)
                    throw new LogicException("您要设置的发货单模板不存在或已被删除");
            }
            _service.UpdateAsDefault(templateId, isOldTemplate);
            return SuccessResult();
        }

        #region 旧版发货单

        public ActionResult OldVersion()
        {
            return View();
        }

        public ActionResult GetOldTemplate()
        {
            string isok = "false";
            string errMsg = "";
            string content = "";
            var model = _service.GetOldSendGoodTemplate();
            if (model != null)
            {
                //处理指定平台默认配置显示名称
                var curPlatformType = SiteContext.Current.CurrentLoginShop.PlatformType;
                if (curPlatformType == PlatformType.YouZan.ToString() || curPlatformType == PlatformType.KuaiShou.ToString())
                {
                    //template.Config = template.Config.Replace("颜色/型号", "商品规格");
                    //if (curPlatformType == PlatformType.KuaiShou.ToString())
                    //{
                    //    template.Config = template.Config.Replace("买家旺旺", "买家昵称");
                    //    template.Config = template.Config.Replace("商品货号", "单品货号");
                    //}
                }
                string items = "";
                Hashtable hs = (Hashtable)PluSoft.Utils.JSON.Decode(model.PrintContent);
                foreach (DictionaryEntry de in hs)
                {
                    if (items == "")
                    {
                        items += de.Key + "," + de.Value;
                    }
                    else
                    {
                        items += ";" + de.Key + "," + de.Value;
                    }
                }

                string items_TdWidth = "";
                if (model.TdWidth != "" && model.TdWidth != null)
                {
                    Hashtable hs_TdWidth = (Hashtable)PluSoft.Utils.JSON.Decode(model.TdWidth);
                    foreach (DictionaryEntry de_TdWidth in hs_TdWidth)
                    {
                        if (items_TdWidth == "")
                        {
                            items_TdWidth += de_TdWidth.Key + "," + de_TdWidth.Value;
                        }
                        else
                        {
                            items_TdWidth += ";" + de_TdWidth.Key + "," + de_TdWidth.Value;
                        }
                    }
                }

                string OffsetX = model.OffsetX;
                string OffsetY = model.OffsetY;

                if (OffsetX == null || OffsetX == "")
                    OffsetX = "0";

                if (OffsetY == null || OffsetY == "")
                    OffsetY = "0";

                string tips = model.Tips?.Replace("\n", "<br>");
                content = ",\"title\":\"" + model.TemplateName + "\",\"tips\":\"" + tips
                    + "\",\"width\":\"" + model.Width + "\",\"height\":\"" + model.Height
                    + "\",\"family\":\"" + model.FontFamily + "\",\"size\":\"" + model.FontSize
                    + "\",\"PrintSendNum\":\"" + model.PrintSendNum + "\",\"TrHeight\":\"" + model.TrHeight
                    + "\",\"PrintScope\":\"" + model.PrintScope + "\",\"TitleMerge\":\"" + model.TitleMerge
                    + "\",\"offerSort\":\"" + model.OfferSort + "\",\"OffsetX\":\"" + OffsetX + "\",\"OffsetY\":\"" + OffsetY + "\",\"skuSort\":\"" + model.SkuSort
                    + "\",\"weight\":\"" + model.FontWeight + "\",\"items_TdWidth\":\"" + items_TdWidth + "\",\"items\":\"" + items + "\",\"picSize\":\"" + model.PicSize + "\",\"lineType\":\"" + model.LineType + "\",\"IsTranslated\":\"" + model.IsTranslated + "\"";
                isok = "true";
            }
            return Content("{\"IsOK\":" + isok + ",\"ErrMsg\":\"" + errMsg + "\"" + content + ",\"ShopId\":\"" + model.ShopId + "\",\"Id\":\"" + model.Id + "\"}");
        }

        public ActionResult SaveOldSendGoodTemplate(OldSendGoodTemplate model)
        {
            if (model == null)
                throw new LogicException("发货单数据有误，请刷新后重新保存");
            model.Tips = model.Tips?.Replace("/n", "</br>");
            var m = _service.SaveOldSendGoodTemplate(model);
            return SuccessResult(m);
        }

        /// <summary>
        /// 获取发货单打印数据
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("打印老版发货单")]
        public ActionResult PreparePrintDataOld(PrintSendGoodTemplateRequestModel model)
        {
            _service.PrepareTemplateDataAndPrintDataOld(model, Request, PageToken, out OldSendGoodTemplate template, out List<SendGoodTemplateDataModel> datas, out List<SendGoodTemplatePageModel> result);
            var phs = new List<long>();
            if (model.IsPrint)
            {
                var hs = MyOrderService.LogFahuoPrintHistory(datas, model, new SendGoodTemplate { Id = template.Id, TemplateName = template.TemplateName });
                phs = hs?.Where(h => h.ID > 0)?.Select(h => h.ID).ToList();
            }
            return SuccessResult(new { PrintData = result, PrintHistoryIds = phs, PrintSendNum = template.PrintSendNum });
        }
        #endregion

    }
}