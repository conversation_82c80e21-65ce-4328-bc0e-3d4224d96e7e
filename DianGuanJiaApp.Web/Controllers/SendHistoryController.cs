using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.Controllers
{
    public class SendHistoryController : BaseController
    {
        private SendHistoryService _service = new SendHistoryService();

        public ActionResult Index()
        {
            var shopList = new List<Shop>();
            var currShop = SiteContext.Current.CurrentLoginShop;
            ViewBag.FromOrderPrint = Request["FromOrderPrint"].ToInt();
            var selectItemList = new List<SelectListItem>();
            if (SiteContext.Current.AllShops != null && SiteContext.Current.AllShops.Count > 1)
            {
                selectItemList.Add(new SelectListItem()
                {
                    Text = "==所有店铺==",
                    Value = 0.ToString(),
                });

                shopList.AddRange(SiteContext.Current.AllShops?.Where(m => m.PlatformType == currShop.PlatformType).ToList());
            }
            else
            {
                shopList.Add(currShop);
            }

            shopList.ForEach(item =>
            {
                if (currShop.PlatformType == item.PlatformType)
                    selectItemList.Add(new SelectListItem() { Text = item.NickName, Value = item.Id.ToString() });

            });

            return View(selectItemList);
        }


        /// <summary>
        /// 加载发往的省份
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadProvinces(string shopId)
        {
            var shopIds = new List<int>();
            if (string.IsNullOrEmpty(shopId) || shopId == "0")
            {
                SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
                {
                    shopIds.Add(item.Id);
                });
            }
            else
            {
                shopIds.Add(shopId.ToInt());
            }
            var provinces = _service.GetToProvinces(shopIds);
            var selectItemList = new List<SelectListItem>() { new SelectListItem() { Text = "==所有省份==", Value = "0" } };
            if (provinces != null && provinces.Count > 0)
            {
                provinces.ForEach(item =>
                {
                    if (string.IsNullOrWhiteSpace(item) == false)
                        selectItemList.Add(new SelectListItem() { Text = item, Value = item });
                });
            }
            return Json(selectItemList);
        }

        /// <summary>
        /// 加载发货快递名称
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("发货记录查询模板条件")]
        public ActionResult LoadExpressNames(string shopId)
        {
            var shopIds = new List<int>();
            if (string.IsNullOrEmpty(shopId) || shopId == "0")
            {
                SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
                {
                    shopIds.Add(item.Id);
                });
            }
            else
            {
                shopIds.Add(shopId.ToInt());
            }

            var dataList = _service.LoadExpressNames(shopIds);
            var selectItemList = new List<SelectListItem>() { new SelectListItem() { Text = "==所有快递==", Value = "0" } };
            if (dataList != null && dataList.Count > 0)
            {
                dataList.ForEach(item =>
                {
                    if (string.IsNullOrWhiteSpace(item) == false)
                        selectItemList.Add(new SelectListItem() { Text = item, Value = item });
                });
            }
            return Json(selectItemList);
        }


        /// <summary>
        /// 加载买家数/订单数
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("发货记录查询数据统计")]
        public ActionResult LoadStatisticsCount(SendHistoryRequestModel requestModel)
        {
            if (requestModel.ShopId != null && requestModel.ShopId.Count > 0)
            {
                var shopIds = requestModel.ShopId.Where(f => f != 0).ToList();
                if (shopIds.Count == 0)
                {
                    SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
                    {
                        shopIds.Add(item.Id);
                    });
                }
                requestModel.ShopId = shopIds;
            }

            var countResult = _service.StatisticsCount(requestModel);
            var result = new { BuyerCount = countResult.Item1, OrderCount = countResult.Item2, ExpressCodeCount = countResult.Item3 };
            return Json(result);
        }

        /// <summary>
        /// 加载列表数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        [LogForOperatorFilter("发货记录查询列表")]
        public ActionResult LoadList(SendHistoryRequestModel requestModel)
        {
            if (requestModel.ShopId != null && requestModel.ShopId.Count > 0)
            {
                var shopIds = requestModel.ShopId.Where(f => f != 0).ToList();
                if (shopIds.Count == 0)
                {
                    SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
                    {
                        shopIds.Add(item.Id);
                    });
                }
                requestModel.ShopId = shopIds;
            }

            //防止越权
            if (requestModel.FxUserId > 0 && requestModel.FxUserId != SiteContext.Current.CurrentFxUserId)
            {
                return Json(new PagedResultModel<SendHistory>());
            }

            var pageModel = _service.LoadList(requestModel);
            var shop = SiteContext.Current.CurrentLoginShop;
            if (shop.PlatformType == Data.Enum.PlatformType.Jingdong.ToString() || shop.PlatformType == PlatformType.Taobao.ToString())
            {
                var pids = new List<string>();
                pageModel.Rows?.ForEach(t => {
                    if (!string.IsNullOrEmpty(t.OrderJoin))
                    {
                        pids.AddRange(t.OrderJoin.Split(','));
                    }
                    else
                    {
                        pids.Add(t.OrderId.Trim('C'));
                    }
                    t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
                    t.Reciver = t.Reciver.ToEncryptName();
                    t.ReciverAddress = t.ReciverAddress.ToTaoBaoEncryptAddress();
                    if (shop.PlatformType == PlatformType.Taobao.ToString())
                    {
                        //淘宝还需加密发件人信息
                        t.Sender = t.Sender.ToEncryptName();
                        t.SenderPhone = t.SenderPhone.ToEncrytPhone();
                    }
                });

                if (shop.PlatformType == PlatformType.Jingdong.ToString())
                {   //京东安全日志
                    jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
                }
                else
                {
                    //记御城河日志
                    ych_sdk.YchRequestLogger.Order(shop.Id.ToString(), "订单查询", pids);
                }
            }
            else if(shop.PlatformType == Data.Enum.PlatformType.Pinduoduo.ToString())
            {
                var tempOrders = pageModel.Rows?.Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ReciverAddress }).ToList();
                MyOrderService.TryToDecryptPddOrders(tempOrders);
                //按店铺分组
                pageModel.Rows?.GroupBy(x => x.ShopId).ToList().ForEach(g => {
                    foreach (var item in g)
                    {
                        var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId && x.ShopId == item.ShopId);
                        if (decryptedOrder != null)
                        {
                            item.Reciver = decryptedOrder.ToName;
                            item.ReciverPhone = decryptedOrder.ToMobile;
                            item.BuyerMemberName = item.Reciver;
                            item.BuyerMemberId = item.Reciver;
                            item.ReciverAddress = decryptedOrder.ToFullAddress;
                        }
                    }
                });
                pageModel.Rows?.ForEach(w => {
                    w.ReciverPhone = w.ReciverPhone.ToEncrytPhone();
                    w.BuyerMemberName = w.BuyerMemberName.ToEncryptName();
                    w.BuyerMemberId = w.BuyerMemberId.ToEncryptName();
                    w.Reciver = w.Reciver.ToEncryptName();
                    w.ReciverAddress = w.ReciverAddress.ToPddEncryptAddress();
                    w.ReciverPhone = w.ReciverPhone.ToPddEncryptPhone();
                });
            }
            return Json(pageModel);
        }

        public ActionResult GetTodaySendedCount()
        {
            var shop = SiteContext.Current.CurrentLoginShop;
            SendHistoryRequestModel requestModel = new SendHistoryRequestModel
            {
                ShopId = new List<int> { shop.Id },
                StartDate = DateTime.Now.ToString("yyyy-MM-dd"),
                EndDate = DateTime.Now.ToString("yyyy-MM-dd 23:59:59"),
                PageSize = 1
            };

            var pageModel = _service.LoadList(requestModel);
            if (shop.PlatformType == Data.Enum.PlatformType.Jingdong.ToString())
            {
                var pids = new List<string>();
                pageModel.Rows?.ForEach(t => {
                    if (!string.IsNullOrEmpty(t.OrderJoin))
                    {
                        pids.AddRange(t.OrderJoin.Split(','));
                    }
                    else
                    {
                        pids.Add(t.OrderId.Trim('C'));
                    }
                    t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
                });
                jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
            }
            return Json(pageModel.Total);
        }
    }
}