using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DianGuanJiaApp.Services;
using System.Collections;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models.Account;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Models.SetOfferTitle;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Threading;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.App_Start;

namespace DianGuanJiaApp.Controllers
{
    public class SetOfferTitleController : BaseController
    {
        SyncProductService sps = new SyncProductService();
        ShopService shopService = new ShopService();
        ProductService productService = new ProductService();
        ProductStatusService productStatusService = new ProductStatusService();
        ProductSkuAttributeService productSkuAttributeService = new ProductSkuAttributeService();
        //OrderService orderService = new OrderService();
        // GET: AccountList
        public ActionResult Index()
        {
            //加载关联的店铺
            Shop _shops = SiteContext.Current.CurrentLoginShop;
            var shops = SiteContext.Current.AllShops.Where(m => m.PlatformType == _shops.PlatformType).ToList();
            //检查同步的状态            
            bool isSyncSucceed = productStatusService.IsSyncSucceed(_shops);


            SetOfferTitleViewModel svm = new SetOfferTitleViewModel()
            {
                Shops = shops,
                DefaultShopId = _shops.Id,
                SyncButton = productStatusService.GetIsShowButton(_shops),
                LastTime = productStatusService.LastTime(_shops),
                ProductCategorys = sps.CategoryListS(_shops, isSyncSucceed)//isSyncSucceed成功的时候，才去执行
            };



            //同步超过一定的时间（可能失败了），就需要重新同步
            if (!isSyncSucceed)
            {
                //var platform = CustomerConfig.ConvertPlatformType(CustomerConfig.Platform);//SiteContext.ConvertPlatformType(CustomerConfig.Platform);
                var platform = SiteContext.Current.CurrentLoginShop.PlatformType;//CustomerConfig.ConvertPlatformType(SiteContext.Current.CurrentLoginShop.PlatformType);//SiteContext.ConvertPlatformType(CustomerConfig.Platform);
                ThreadPool.QueueUserWorkItem(state =>
                {
                    foreach (Shop shop in shops)
                    {
                        if (shop.PlatformType == platform || platform.Contains(shop.PlatformType))
                        {
                            bool isSyncSucceedZi = productStatusService.IsSyncSucceed(shop);
                            if (!isSyncSucceedZi)
                            {
                                productStatusService.UpadteAutomateCount(shop.Id);
                                sps.SyncProduct(shop, 1);//同步，同步成功后状态会修改成:成功.
                            }
                        }
                    }

                });
            }


            return View(svm);
        }

        [LogForOperatorFilter("同步商品")]
        public ActionResult SyncProduct()
        {
            if (CustomerConfig.IsSyncDisabled || SiteContext.Current.CurrentLoginShop.PlatformType == PlatformType.Offline.ToString())
                return Content("");
            string lastTime = "";
            try
            {
                var shopList = SiteContext.Current.AllSamePlatformTypeShops;

                foreach (Shop _shops in shopList)
                {
                    //同步，同步成功后状态会修改成:成功.
                    sps.SyncProduct(_shops, 1);
                }
                Shop loginShop = SiteContext.Current.CurrentLoginShop;
                lastTime = productStatusService.LastTime(loginShop);

            }
            catch (Exception ex)
            {
                lastTime = "error";
                Log.WriteError($"同步商品,错误详情：{ex}");
            }




            return Content(lastTime);
        }

        public ActionResult IsProductSucceed()
        {
            Shop _shops = SiteContext.Current.CurrentLoginShop;
            string res = productStatusService.GetIsShowButton(_shops);

            return Content(res);
        }



        public ActionResult Qurey(SetOfferModule model)
        {

            //加载关联的店铺
            var shops = shopService.GetAllShops();
            Shop _shops = SiteContext.Current.CurrentLoginShop;

            List<Shop> listShop = new List<Shop>();

            int shopId = Convert.ToInt32(model.ShopSelect);

            foreach (Shop shop in shops)
            {
                if (shop.Id == shopId)
                    listShop.Add(shop);
            }

            if (listShop.Count == 0)
                listShop.Add(_shops);

            //商品列表
            //List<Product> ProductList = productService.PriductList(listShop, model);
            //ProductList.ForEach(p=> {
            //    p.PlatformType = shops.FirstOrDefault(s=>s.Id==p.ShopId)?.PlatformType;
            //});
            //int counts = ProductList.Count;
            //
            //ProductList = ProductList.Skip(model.PageSize * (model.PageIndex - 1)).Take(model.PageSize).ToList();
            //
            //SetOfferTitleViewModel svm = new SetOfferTitleViewModel()
            //{
            //    ProductList = ProductList,
            //    TotalNumber = counts
            //};

            var tuple = productService.PriductList(listShop, model);
            SetOfferTitleViewModel svm = new SetOfferTitleViewModel()
            {
                ProductList = tuple.Item2,
                TotalNumber = tuple.Item1
            };

            var html = RenderPartialViewToString("Qurey", svm);
            return Content(html);
        }


        //public ActionResult SaveOneOffer(string productId, bool isCheck, string names, string weight, string arrList)
        //{

        //    ArrayList arrItems = (ArrayList)PluSoft.Utils.JSON.Decode(arrList);
        //    string res = "1";

        //    if (isCheck)//说明是按规格显示,商品的weight不用添加
        //    {
        //        productService.UpdateOneProduct(productId, names);


        //        for (int i = 0; i < arrItems.Count; i++)
        //        {
        //            Hashtable hs = (Hashtable)arrItems[i];

        //            var attributeId = Convert.ToString(hs["attributeId"]);
        //            var shortTitle = Convert.ToString(hs["ShortTitle"]);
        //            var attWeight = Convert.ToString(hs["weight"]);

        //            productSkuAttributeService.UpdateOneProductSkuAttribute(attributeId, shortTitle, attWeight);

        //        }

        //    }
        //    else
        //        res = productService.UpdateOneProduct(productId, names, weight) ? "1" : "2";



        //    return Content(res);
        //}
        public ActionResult ShopChange(int shopId)
        {
            var shops = shopService.GetAllShops();
            Shop _shops = SiteContext.Current.CurrentLoginShop;
            Shop shopss = null;

            foreach (Shop shop in shops)
            {
                if (shop.Id == shopId)
                {
                    shopss = shop;
                    break;
                }
            }

            if (shopss == null)
                shopss = _shops;

            SetOfferTitleViewModel svm = new SetOfferTitleViewModel()
            {
                ProductCategorys = sps.CategoryListS(shopss, false)
            };

            return SuccessResult(new { ProductCategorys = svm.ProductCategorys });

        }


        [LogForOperatorFilter("修改商品简称")]
        //保存当前页面的数据
        public ActionResult SaveSetOffer(SaveOfferModule som)
        {
            string isOk = "1";

            try
            {
                bool isCk = som.isCheck;

                List<SaveOfferProduct> listProduct = som.ProductList;
                var shopIds = listProduct?.Select(l => l.ShopId)?.Distinct()?.ToList();
                List<int> productIdList = listProduct.Select(t => Convert.ToInt32(t.productId)).ToList();
                List<Product> productL = productService.ProductIdList(productIdList);
                //shopIds.AddRange(productL.Select(x => x.ShopId).Distinct());
                productL.ForEach(p =>
                {
                    var product = listProduct.FirstOrDefault(o => Convert.ToInt32(o.productId) == p.Id);
                    p.ShortTitle = product.names == null ? "" : product.names;

                    if (!isCk && !string.IsNullOrEmpty(product.weight))
                    {
                        var weight = Convert.ToDecimal(product.weight);
                        p.IsSetWeight = weight > 0 ? true : false;
                        p.SetWeight = weight;
                    }
                });

                //批量更新产品
                productService.BatchUpdateProduct(productL);

                //更新属性
                if (isCk)
                {
                    List<SaveOfferProductSkuAttribute> attributeListss = new List<SaveOfferProductSkuAttribute>();
                    listProduct.ForEach(p =>
                    {
                        if (p.SkuAttributeList != null)
                        {
                            foreach (SaveOfferProductSkuAttribute att in p.SkuAttributeList)
                                attributeListss.Add(att);

                        }
                    });

                    if (attributeListss.Count > 0)
                    {

                        List<ProductSkuAttribute> attributeL = productSkuAttributeService.AttributeIdList(attributeListss.Select(tt => Convert.ToInt32(tt.attributeId)).ToList());
                        attributeL.ForEach(att =>
                        {
                            var attribute = attributeListss.FirstOrDefault(o => Convert.ToInt32(o.attributeId) == att.Id);

                            att.ShortTitle = attribute.ShortTitle == null ? "" : attribute.ShortTitle;

                            if (!string.IsNullOrEmpty(attribute.weight))
                                att.Weight = Convert.ToDecimal(attribute.weight);
                        });

                        if (attributeL.Count > 0)
                            productSkuAttributeService.BatchUpdateAttribute(attributeL);
                    }
                }
                if (som.SyncWeightToOrder)
                    MyOrderService.SetOrderWeight(shopIds, listProduct, !isCk);
            }
            catch (Exception ex)
            {
                isOk = "2";
                Log.WriteError($"保存商品简称重量等,错误详情：{ex}");
            }


            return Content(isOk);
        }

        public ActionResult SaveShortTitle(int shopId,string orderId, string platformId, string shortTitle)
        {
            var result = productService.UpdateOneProduct(shopId,orderId, platformId, shortTitle);
            if (result)
                return SuccessResult();
            else
                return FalidResult("简称保存失败");
        }

        public ActionResult SyncOtherShop(string selectShopId, string radioValue)
        {

            //1:商品名称或商品货号相同 2:商品名称相同 3:商品货号相同

            //4:规格名称或单号货号相同 5:规格名称相同 6:单号货号相同

            string isOk = "1";

            try
            {

                Shop _shops = SiteContext.Current.CurrentLoginShop;
                int otherShopId = Convert.ToInt32(selectShopId);
                if (_shops.Id == otherShopId)
                {
                    return Content("3");
                }

                //状态修改成同步中
                productStatusService.StartSyncTimeOther(_shops.Id, otherShopId);

                productService.SyncOtherWeightInfo(_shops.Id, otherShopId, radioValue);

                //同步完成，更新状态
                productStatusService.UpdateNameWeightTime(_shops.Id, otherShopId);
            }
            catch (Exception ex)
            {
                isOk = "2";
                Log.WriteError($"同步给其他的店铺，简称和重量,错误详情：{ex}");
            }

            return Content(isOk);
        }


    }
}