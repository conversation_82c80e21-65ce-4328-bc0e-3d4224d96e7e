using System.Linq;
using System.Web.Mvc;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using System.Text;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.App_Start;
using System;
using System.Collections.Generic;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Data.Model;

namespace DianGuanJiaApp.Controllers
{
    public class ShopController : BaseController
    {
        private ShopService _shopService = new ShopService();
        private ShopRelationService _shopRelationService = new ShopRelationService();
        private LogicOrderService _orderService = new LogicOrderService();
        private FxUnBindTaskService _fxUnBindTaskService = new FxUnBindTaskService();
        private PathFlowNodeService _pathFlowNodeService = new PathFlowNodeService();

        //public ActionResult Switch(int shopId, int isFromParent = 0)
        //{
        //    //验证有没有权限
        //    var sc = SiteContext.Current;
        //    var shop = sc.AllShops?.FirstOrDefault(s => s.Id == shopId);
        //    if (sc.MasterShop.Id == shopId)
        //        shop = sc.MasterShop;
        //    if (shop == null)
        //        throw new LogicException("该店铺不是您的子店铺或已被取消关联，请重新关联后再进行切换");
        //    //设置cookie
        //    //AuthController.SetAuthCookie(shop.Id, Request, Response);
        //    if (shop.MigrateStatus == ShopMigrateStatusType.NotMigrated.ToString())
        //    {
        //        var refreshToken = _shopService.GetOldShopRefreshToken(shop);
        //        if (string.IsNullOrEmpty(refreshToken))
        //            refreshToken = shop.RefreshToken;
        //        var oldSystemLink = "";
        //        if (shop.PlatformType == PlatformType.Alibaba.ToString())
        //            oldSystemLink = $"{CustomerConfig.AlibabaOldSystemLink}/OrderList.aspx?print_loginid={shop.ShopId}&print_code={shop.RefreshToken}";
        //        else if (shop.PlatformType == PlatformType.Taobao.ToString())
        //            oldSystemLink = $"{CustomerConfig.TaobaoOldSystemLink}/OrderList.aspx?print_loginid={shop.ShopId}&print_code={shop.RefreshToken}";
        //        else if (shop.PlatformType == PlatformType.Pinduoduo.ToString())
        //            oldSystemLink = $"{CustomerConfig.PinduoduoOldSystemLink}/OrderList.aspx?print_loginid={shop.ShopId}&print_code={shop.RefreshToken}";
        //        return SuccessResult(oldSystemLink);
        //    }
        //    else
        //    {
        //        var fromId = sc.CurrentShopId;
        //        var subUserId = sc.CurrentLoginSubUser?.Id ?? 0;
        //        var token = AuthController.CreateToken(shop.Id, Request, false, isFromParent == 1, fromId: fromId, subUserId: subUserId, response: Response);
        //        var link = "";
        //        //if (shop.PlatformType == PlatformType.Alibaba.ToString())
        //        //    link = $"{CustomerConfig.AlibabaNewSystemLink}?token={token}";
        //        //else if (shop.PlatformType == PlatformType.Taobao.ToString())
        //        //    link = $"{CustomerConfig.TaobaoNewSystemLink}?token={token}";
        //        //else if (shop.PlatformType == PlatformType.Pinduoduo.ToString())
        //        //    link = $"{CustomerConfig.PinduoduoNewSystemLink}?token={token}";
        //        link = $"{CustomerConfig.GetShopRedirectLink(shop.PlatformType.ToString(), shop.Version, shop.VenderId)}?token={token}";
        //        return SuccessResult(link);
        //    }
        //}

        public ActionResult List()
        {
            var shops = SiteContext.Current.AllShops?.Where(s => s.Id != SiteContext.Current.CurrentShopId);
            var model = new { CurrentShop = SiteContext.Current.CurrentLoginShop, RelatedShops = shops };
            return SuccessResult(model);
        }

        //public ActionResult CreateShortcut()
        //{
        //    try
        //    {
        //        //添加引用 (com->Windows Script Host Object Model)，using IWshRuntimeLibrary;
        //        var folder = Server.MapPath("~/log/QuickLinks");
        //        if (!Directory.Exists(folder))
        //            Directory.CreateDirectory(folder);
        //        var fileName = Guid.NewGuid().ToString().Replace("-", "");
        //        var shortcutPath = Path.Combine(folder, $"{fileName}.lnk");
        //        var shell = new WshShell();
        //        WshShortcut shortcut = (WshShortcut)shell.CreateShortcut(shortcutPath);
        //        shortcut.TargetPath = $"{CustomerConfig.ApplicationWebUrl}/Auth/Set/{SiteContext.Current.CurrentShopId}";
        //        shortcut.Arguments = "";// 参数  
        //        shortcut.Description = "店管家批量打印发货快捷方式";
        //        shortcut.WorkingDirectory = "";//程序所在文件夹，在快捷方式图标点击右键可以看到此属性  
        //        shortcut.IconLocation = "%SystemRoot%\\System32\\SHELL32.dll,16";
        //        //图标，该图标是应用程序的资源文件  
        //        //shortcut.IconLocation = "https://cbu01.alicdn.com/img/ibank/2013/145/136/845631541_93880881.summ.jpg";
        //        //shortcut.Hotkey = "CTRL+SHIFT+W";//热键，发现没作用，大概需要注册一下  
        //        shortcut.WindowStyle = 1;
        //        shortcut.Save();
        //        var fileBytes = System.IO.File.ReadAllBytes(shortcutPath);
        //        var name = Server.UrlEncode("店管家批量打单发货.lnk");
        //        return File(shortcutPath, "application/octet-stream", name);
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriteError($"生成快捷方式失败，请稍后重试：{ex}");
        //        throw new LogicException("生成快捷方式失败，请稍后重试");
        //    }
        //}

        //public ActionResult CreateUrl()
        //{
        //    string HostAddr = RequestHost;// CustomerConfig.ApplicationWebUrl;
        //    string icoPath = $"{HostAddr}/favicon.ico";//修改此处更改url图标或者图标路径，当前路径为根目录，只用修改相对路径，图标的完整路径由下方会自动生成
        //    var sb = new StringBuilder();
        //    var subUserId = SiteContext.Current.CurrentLoginSubUser?.Id ?? 0;
        //    var fromId = LoginAuthToken?.FromId ?? 0;
        //    var token = AuthController.CreateToken(SiteContext.Current.CurrentShopId, Request, true, fromId: fromId, subUserId: subUserId, response: Response);
        //    sb.AppendLine("[{000214A0-0000-0000-C000-000000000046}]");
        //    sb.AppendLine("Prop3=19,11");
        //    sb.AppendLine("[InternetShortcut]");
        //    sb.AppendLine("IDList=");
        //    sb.AppendLine($"URL={HostAddr}?token={token}"); //快捷方式的外部链接
        //    sb.AppendLine("IconFile=" + icoPath); //图标文件
        //    sb.AppendLine("IconIndex=1");
        //    //第一种:使用FileContentResult
        //    byte[] fileContents = Encoding.Default.GetBytes(sb.ToString());
        //    string fileName = System.Web.HttpUtility.UrlEncode("店管家-批量打单发货", System.Text.Encoding.UTF8) + ".url";
        //    return File(fileContents, "application/octet-stream", fileName);
        //}

        /// <summary>
        /// 根据分享吗获取店铺对象
        /// </summary>
        /// <param name="shareCode"></param>
        /// <returns></returns>
        public ActionResult GetShopByShareCode(string shareCode)
        {
            var shop = _shopService.GetShopByShareCode(shareCode);
            return SuccessResult(shop);
        }


        /// <summary>
        /// 根据店铺名称获取店铺对象
        /// </summary>
        /// <param name="shareCode"></param>
        /// <returns></returns>
        public ActionResult GetShopByName(string name)
        {
            var shops = _shopService.GetShopByName(name);
            return SuccessResult(shops);
        }

        /// <summary>
        /// 检查店铺状态，是否存在解绑任务
        /// </summary>
        /// <param name="LogicOrderIds"></param>
        /// <returns></returns>
        public ActionResult CheckShopStatus(List<string> LogicOrderIds)
        {
            if (LogicOrderIds == null || LogicOrderIds.Count == 0)
                return FalidResult("订单号不能为空");

            var curFxUserId = SiteContext.Current.CurrentFxUserId;

            // 根据订单号获取店铺ID
            var logicOrders = _orderService.GetLogicOrders(LogicOrderIds, new List<string>{ "ShopId", "LogicOrderId", "PathFlowCode" }).ToList(); 
            var shopIds = logicOrders.Select(s => s.ShopId).Distinct().ToList();

            // 根据店铺ID获取解绑任务
            var fxUnBindTasks = _fxUnBindTaskService.GetShops(shopIds, curFxUserId)
                .Where(x => x.TaskState == UnBindTaskState.Ready || x.TaskState == UnBindTaskState.Runing).ToList();

            var existShopIds = fxUnBindTasks.Select(s => s.ShopId).Distinct().ToList();
            if (existShopIds.Count == 0)
                return SuccessResult();

            // 只有一个订单特殊处理
            if (LogicOrderIds.Count == 1 && logicOrders.Count == 1 && existShopIds.Count == 1)
            {
                var order = logicOrders.FirstOrDefault();
                if (order != null)
                {
                    var pathFlowCode = order.PathFlowCode;
                    var pathFlow = _pathFlowNodeService.GetFxUserByPathFlowCodes(new List<string> {pathFlowCode});
                    pathFlow = pathFlow.Where(x => x.SourceFxUserId == curFxUserId).ToList();
                        
                    // 商家显示店铺解绑中，厂家显示被商家解绑中
                    return FalidResult(pathFlow.Any() ? "当前店铺正在解绑中，请勿操作。" : "该店铺订单正在被商家解绑，请勿操作。");
                }
            }

            var logicList = logicOrders.Where(x => existShopIds.Contains(x.ShopId)).Select(x => x.LogicOrderId).ToList();

            return SuccessResult(logicList);
        }

        [LogForOperatorFilter("手工同步订单")]
        public ActionResult UpdateLastSyncTime(string orderIds, int shopId, int day)
        {
            if (day <= 0)
                day = 2;
            else if (day > 15)
                return FalidResult("手工同步最多只能同步最近15天的订单");

            var log = LogForOperatorContext.Current.logInfo;
            log.Request = orderIds;

            if (orderIds != null)
                orderIds = orderIds.Trim().TrimEnd(',').TrimEnd('，').TrimStart('，').TrimStart(',');
            //var shop = _shopService.Get(shopId);
            var shop = SiteContext.Current.AllShops.FirstOrDefault(f => f.Id == shopId);
            if (string.IsNullOrEmpty(orderIds))
            {
                if (shop.LastSyncStatus == ShopSyncStatusType.Syncing.ToString())
                    return FalidResult("正在增量同步中，请等待增量同步完成后再试！");
                else
                {
                    //增量同步最近2天的订单
                    var time = DateTime.Now.AddDays(-day).AddHours(-1);
                    var result = _shopService.UpdateLastSyncTime(shopId, time);
                    log.Response = result;
                    //同步时间大于6天时，淘宝和拼多多由于走的推送库，推送库数据只保存了7天，所以要触发补漏
                    if (day > 6 && (shop.PlatformType == PlatformType.Pinduoduo.ToString() || shop.PlatformType == PlatformType.Taobao.ToString()))
                    {
                        var commonSettingService = new CommonSettingService();
                        //补漏是否开启的配置
                        var key = shop.PlatformType == PlatformType.Pinduoduo.ToString() ? "/User/Shop/CheckPddWaitSellerSendOrderIds" : "/User/Shop/CheckTaobaoWaitSellerSendOrderIds";
                        var value = commonSettingService.Get<string>(key, shop.Id);
                        if (string.IsNullOrWhiteSpace(value) || value?.ToLower() == "false")
                        {
                            commonSettingService.Set(key, "true", shop.Id);
                        }
                    }
                    if (result == true)
                    {
                        //同步时间大于6天时，淘宝和拼多多由于走的推送库，推送库数据只保存了7天，所以要触发补漏
                        if (day > 6 && (shop.PlatformType == PlatformType.Pinduoduo.ToString() || shop.PlatformType == PlatformType.Taobao.ToString()))
                        {
                            var commonSettingService = new CommonSettingService();
                            //补漏是否开启的配置
                            var key = shop.PlatformType == PlatformType.Pinduoduo.ToString() ? "/User/Shop/CheckPddWaitSellerSendOrderIds" : "/User/Shop/CheckTaobaoWaitSellerSendOrderIds";
                            var value = commonSettingService.Get<string>(key, shop.Id);
                            if (string.IsNullOrWhiteSpace(value) || value?.ToLower() == "false")
                            {
                                commonSettingService.Set(key, "true", shop.Id);
                            }
                        }

                        shop.LastSyncTime = time;
                    }
                }
            }
            else
            {
                var oidList = orderIds.Split(new char[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                oidList = oidList.Distinct().ToList();
                new SyncOrderService().SyncSingleOrders(oidList, shop);
            }
            return SuccessResult(new { IsSyncProcessEnabled = shop.LastSyncTime <= DateTime.Now.AddDays(-2) });
        }
    }
}