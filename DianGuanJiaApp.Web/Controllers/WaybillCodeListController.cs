using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.LogisticService;
using DianGuanJiaApp.Services.WaybillService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.Controllers
{
    public class WaybillCodeListController : BaseController
    {

        private WaybillCodeService _service = new WaybillCodeService();
        private PrintHistoryService _printHistoryService = new PrintHistoryService();
        private SendHistoryService _sendHistoryService = new SendHistoryService();
        private PrintTemplateService _printTemplateService = new PrintTemplateService();
        private ExpressCompanyService _expressCompanyService = new ExpressCompanyService();
        private UserSiteInfoService _userSiteInfoService = new UserSiteInfoService();
        private TemplateRelationAuthInfoService _templateRelationAuthInfoService = new TemplateRelationAuthInfoService();
        private CaiNiaoAuthInfoService _caiNiaoAuthInfoService = new CaiNiaoAuthInfoService();
        private CommService _commService = new CommService();
        private CommonSettingService _commSettingService = new CommonSettingService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private ExportTaskService _exportTaskService = new ExportTaskService();

        private BranchShareRelationService _branchShareRelationService = new BranchShareRelationService();

        private ShareWaybillCodeRecordService _shareWaybillCodeRecordService = new ShareWaybillCodeRecordService();


        public ActionResult Index()
        {
            var shopList = new List<Shop>();
            var currShop = SiteContext.Current.CurrentLoginShop;
            ViewBag.FromOrderPrint = Request["FromOrderPrint"].ToInt();
            var selectItemList = new List<SelectListItem>();
            if (SiteContext.Current.AllShops != null && SiteContext.Current.AllShops.Count > 1)
            {
                selectItemList.Add(new SelectListItem()
                {
                    Text = "==所有店铺==",
                    Value = 0.ToString(),
                });

                shopList.AddRange(SiteContext.Current.AllShops?.Where(m => m.PlatformType == currShop.PlatformType).ToList());
            }
            else
            {
                shopList.Add(currShop);
            }

            shopList.ForEach(item =>
            {
                if (currShop.PlatformType == item.PlatformType)
                    selectItemList.Add(new SelectListItem() { Text = item.NickName, Value = item.Id.ToString() });

            });

            // 底单导出默认导出设置
            var currShopId = SiteContext.Current.CurrentShopId;
            var setting = "[{\"Text\":\"订单编号\",\"Value\":\"OrderId\"},{\"Text\":\"收件人姓名\",\"Value\":\"Reciver\"},{\"Text\":\"收件人电话\",\"Value\":\"ReciverPhone\"},{\"Text\":\"详细地址\",\"Value\":\"ToAddress\"},{\"Text\":\"快递公司\",\"Value\":\"ExpressName\"},{\"Text\":\"快递单号\",\"Value\":\"ExpressWayBillCode\"},{\"Text\":\"商品数量\",\"Value\":\"ProductCount\"},{\"Text\":\"发货内容\",\"Value\":\"SendContent\"},{\"Text\":\"发件人\",\"Value\":\"Sender\"},{\"Text\":\"打单时间\",\"Value\":\"CreateDate\"}]";

            var keys = new List<string> { "WaybillCodeListExportSet", "/Export/WaybillCode/UpdateTime" };
            var commSets = _commonSettingService.GetSets(keys, currShopId);
            var checkedFieldsSet = commSets?.FirstOrDefault(m => m.Key == "WaybillCodeListExportSet");
            if (checkedFieldsSet == null || checkedFieldsSet.Value.IsNullOrEmpty())
            {
                _commSettingService.Set("WaybillCodeListExportSet", setting, currShopId);
            }

            //最后导出时间
            var exportUpdateTimeSet = commSets.FirstOrDefault(m => m.Key == "/Export/WaybillCode/UpdateTime");
            var defaultExportUpdateTime = exportUpdateTimeSet?.Value.ToDateTime() ?? null;
            var defaultExportUpdateTimeSetVal = defaultExportUpdateTime == null ? "" : defaultExportUpdateTime.Value.ToString("yyyy-MM-ddTHH:mm:ss");
            // 默认导出间隔时长（300s）
            var defaultExportExpireSecondsSet = _commonSettingService.Get("/Export/WaybillCode/ExpireSeconds", 0);
            var defaultExportExpireSeconds = defaultExportExpireSecondsSet?.Value.ToInt() ?? 0;
            defaultExportExpireSeconds = defaultExportExpireSeconds <= 0 ? 300 : defaultExportExpireSeconds;
            ViewBag.ExportUpdateTimeSet = defaultExportUpdateTimeSetVal;
            ViewBag.ExportExpireSeconds = defaultExportExpireSeconds;

            //获取url参数带过来的订单id
            var platformOrderId = Request.Params["platformOrderId"];
            ViewBag.PlatformOrderId = platformOrderId;
            //前台获取导出任务
            var exportTask = _exportTaskService.GetExportTask(currShopId, ExportType.WaybillCode.ToInt());
            // 显示未完成任务或已完成一天前的导出任务
            exportTask = exportTask != null && ((exportTask.Status >= 0 && exportTask.Status < 4) || (exportTask.Status >= 4 && exportTask.UploadToServerTime != null && DateTime.Now < exportTask.UploadToServerTime.Value.AddDays(1))) ? exportTask : null;
            var task = GetExportTaskToWeb(exportTask);
            ViewBag.WaybillCodeExportTask = task?.ToJson() ?? "null";
            return View(selectItemList);
        }

        /// <summary>
        /// 加载发往的省份
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadProvinces(string shopId)
        {
            var shopIds = new List<int>();
            if (string.IsNullOrEmpty(shopId) || shopId == "0")
            {
                SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
                {
                    shopIds.Add(item.Id);
                });
            }
            else
            {
                shopIds.Add(shopId.ToInt());
            }
            var provinces = _service.GetToProvinces(shopIds);
            var selectItemList = new List<SelectListItem>() { new SelectListItem() { Text = "==所有省份==", Value = "0" } };
            if (provinces != null && provinces.Count > 0)
            {
                provinces.ForEach(item =>
                {
                    if (string.IsNullOrWhiteSpace(item) == false)
                        selectItemList.Add(new SelectListItem() { Text = item, Value = item });
                });
            }
            return Json(selectItemList);
        }

        /// <summary>
        /// 加载打印过的模板名称
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("底单查询模板条件")]
        public ActionResult LoadTemplateNames(string shopId, string sDate, string eDate)
        {
            var shopIds = new List<int>();

            if (string.IsNullOrEmpty(shopId) || shopId == "0")
            {
                SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
                {
                    shopIds.Add(item.Id);
                });
            }
            else
            {
                shopIds.Add(shopId.ToInt());
            }

            var dataList = _service.GetTemplateNames(shopIds, sDate, eDate);
            var selectItemList = new List<SelectListItem>() { new SelectListItem() { Text = "=======所有模板=======", Value = "0" } };
            if (dataList != null && dataList.Count > 0)
            {
                dataList.ForEach(item =>
                {
                    selectItemList.Add(new SelectListItem() { Text = item.ExpressPic + "-" + item.TemplateName, Value = item.Id.ToString() });
                });
            }
            return Json(selectItemList);
        }

        ///// <summary>
        ///// 加载运单数/订单数
        ///// </summary>
        ///// <returns></returns>
        //[LogForOperatorFilter("底单查询数据统计")]
        //public ActionResult LoadStatisticsCount(WaybillCodeRequestModel requestModel)
        //{
        //    if (requestModel.ShopId != null && requestModel.ShopId.Count > 0)
        //    {
        //        var shopIds = requestModel.ShopId.Where(f => f != 0).ToList();
        //        if (shopIds.Count == 0)
        //        {
        //            SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
        //            {
        //                shopIds.Add(item.Id);
        //            });
        //        }
        //        requestModel.ShopId = shopIds;
        //    }

        //    var countResult = _service.StatisticsCount(requestModel);
        //    var result = new { WaybillCodeCount = countResult.Item1, OrderCount = countResult.Item2 };
        //    return Json(result);
        //}

        /// <summary>
        /// 加载列表数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        [LogForOperatorFilter("底单查询列表")]
        public ActionResult LoadList(WaybillCodeRequestModel requestModel)
        {
            if (requestModel.ShopId == null || !requestModel.ShopId.Any())
                requestModel.ShopId = new List<int> { SiteContext.Current.CurrentShopId };
            else if (requestModel.ShopId != null && requestModel.ShopId.Count > 0)
            {
                var shopIds = requestModel.ShopId.Where(f => f != 0).ToList();
                if (shopIds.Count == 0)
                {
                    SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
                    {
                        shopIds.Add(item.Id);
                    });
                }
                requestModel.ShopId = shopIds;
            }

            var pageModel = _service.LoadList(requestModel);

            //var totalOrderCount = 0;
            //var totalWaybillCount = 0;

            //var dataList = pageModel.Rows;
            //if (dataList != null && dataList.Count > 0)
            //{
            //    totalOrderCount = dataList.SelectMany(f =>
            //    {
            //        var orderJoin = f.OrderIdJoin?.TrimEnd(',').TrimStart(',');
            //        if (orderJoin?.IndexOf(',') > -1)
            //        {
            //            return orderJoin.Split(',');
            //        }
            //        else
            //        {
            //            return new string[] { f.OrderIdJoin };
            //        }
            //    }
            //    ).Distinct().Count();

            //    totalWaybillCount = dataList.Select(f => f.ExpressWayBillCode).Distinct().Count();
            //}

            var result = new WaybillCodeListPageViewModel()
            {
                IsOrderDesc = pageModel.IsOrderDesc,
                OrderByField = pageModel.OrderByField,
                PageIndex = pageModel.PageIndex,
                PageSize = pageModel.PageSize,
                Total = pageModel.Total,
                Rows = pageModel.Rows,
                //TotalOrderCount = totalOrderCount,
                //TotalWaybillCodeCount = totalWaybillCount
            };
            var shop = SiteContext.Current.CurrentLoginShop;
            if (shop.PlatformType == PlatformType.Jingdong.ToString() || shop.PlatformType == PlatformType.Taobao.ToString())
            {
                var pids = new List<string>();
                result.Rows?.ForEach(t =>
                {
                    if (!string.IsNullOrEmpty(t.OrderIdJoin))
                    {
                        pids.AddRange(t.OrderIdJoin.Split(','));
                    }
                    else
                    {
                        pids.Add(t.OrderId.Trim('C'));
                    }
                    t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
                    t.Reciver = t.Reciver.ToEncryptName();
                    t.ToAddress = t.ToAddress.ToTaoBaoEncryptAddress();
                    if (shop.PlatformType == PlatformType.Taobao.ToString())
                    {
                        //淘宝还需加密发件人信息
                        t.Sender = t.Sender.ToEncryptName();
                    }
                });

                if (shop.PlatformType == PlatformType.Jingdong.ToString())
                {   //京东安全日志
                    jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
                }
                else
                {
                    //记御城河日志
                    ych_sdk.YchRequestLogger.Order(shop.Id.ToString(), "订单查询", pids);
                }
            }
            else if (shop.PlatformType == Data.Enum.PlatformType.Pinduoduo.ToString())
            {
                var tempOrders = result.Rows?.Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ToAddress }).ToList();
                //_orderService.TryToDecryptPddOrders(tempOrders);
                BranchShareRelationService.TryToDecryptPddOrders(tempOrders, true);
                //按店铺分组
                foreach (var item in result.Rows)
                {
                    var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId && x.ShopId == item.ShopId);
                    if (decryptedOrder != null)
                    {
                        item.Reciver = decryptedOrder.ToName;
                        item.ReciverPhone = decryptedOrder.ToMobile;
                        item.BuyerMemberName = item.Reciver;
                        item.BuyerMemberId = item.Reciver;
                        item.ToAddress = decryptedOrder.ToFullAddress;
                        item.BuyerMemberName = item.BuyerMemberName.ToEncryptName();
                        item.BuyerMemberId = item.BuyerMemberId.ToEncryptName();
                        item.ReciverPhone = item.ReciverPhone.ToPddEncryptPhone();
                        item.Reciver = item.Reciver.ToEncryptName();
                        item.ToAddress = item.ToAddress.ToPddEncryptAddress();
                    }
                }
            }
            return Json(result);
        }


        /// <summary>
        /// 微商,加载列表数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        public ActionResult WsXcxLoadList(WaybillCodeRequestModel requestModel)
        {
            List<int> shopList = new List<int>();
            shopList.Add(SiteContext.Current.CurrentShopId);

            requestModel.ShopId = shopList;

            var pageModel = _service.LoadList(requestModel);



            var result = new WaybillCodeListPageViewModel()
            {
                IsOrderDesc = pageModel.IsOrderDesc,
                OrderByField = pageModel.OrderByField,
                PageIndex = pageModel.PageIndex,
                PageSize = pageModel.PageSize,
                Total = pageModel.Total,
                Rows = pageModel.Rows,
                //TotalOrderCount = totalOrderCount,
                //TotalWaybillCodeCount = totalWaybillCount
            };

            return SuccessResult(new { Results = result });
        }

        /// <summary>
        /// 关联新版的小程序,加载底单记录列表数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        public ActionResult XcxLoadList(WaybillCodeRequestModel requestModel)
        {
            var userId = SiteContext.Current.CurrentLoginUser == null ? 0 : SiteContext.Current.CurrentLoginUser.Id;

            requestModel.PlatformType = SiteContext.Current.CurrentLoginShop == null ? "" : SiteContext.Current.CurrentLoginShop.PlatformType;
            var result = new WaybillCodeListPageViewModel()
            {
                IsOrderDesc = false,
                OrderByField = "",
                PageIndex = requestModel.PageIndex,
                PageSize = requestModel.PageSize,
                Total = 0,
                Rows = new List<WaybillCode>(),
            };

            if (userId > 0)
            {
                requestModel.UserId = userId;

                var pageModel = _service.LoadList(requestModel);

                result = new WaybillCodeListPageViewModel()
                {
                    IsOrderDesc = pageModel.IsOrderDesc,
                    OrderByField = pageModel.OrderByField,
                    PageIndex = pageModel.PageIndex,
                    PageSize = pageModel.PageSize,
                    Total = pageModel.Total,
                    Rows = pageModel.Rows,
                    //TotalOrderCount = totalOrderCount,
                    //TotalWaybillCodeCount = totalWaybillCount
                };

                var shop = SiteContext.Current.CurrentLoginShop;
                if (shop.PlatformType == Data.Enum.PlatformType.Jingdong.ToString() || shop.PlatformType == PlatformType.Taobao.ToString())
                {
                    var pids = new List<string>();
                    result.Rows?.ForEach(t =>
                    {
                        if (!string.IsNullOrEmpty(t.OrderIdJoin))
                        {
                            pids.AddRange(t.OrderIdJoin.Split(','));
                        }
                        else
                        {
                            pids.Add(t.OrderId.Trim('C'));
                        }
                        t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
                        t.Reciver = t.Reciver.ToEncryptName();
                        t.ToAddress = t.ToAddress.ToTaoBaoEncryptAddress();
                        if (shop.PlatformType == PlatformType.Taobao.ToString())
                        {
                            //淘宝还需加密发件人信息
                            t.Sender = t.Sender.ToEncryptName();
                        }
                    });

                    if (shop.PlatformType == PlatformType.Jingdong.ToString())
                    {   //京东安全日志
                        jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
                    }
                    else
                    {
                        //记御城河日志
                        ych_sdk.YchRequestLogger.Order(shop.Id.ToString(), "订单查询", pids);
                    }
                }
                else if (shop.PlatformType == Data.Enum.PlatformType.Pinduoduo.ToString())
                {
                    var tempOrders = result.Rows?.Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ToAddress }).ToList();
                    //_orderService.TryToDecryptPddOrders(tempOrders);
                    BranchShareRelationService.TryToDecryptPddOrders(tempOrders, true);
                    //按店铺分组
                    result.Rows?.ForEach(item =>
                    {
                        var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId && x.ShopId == item.ShopId);
                        if (decryptedOrder != null)
                        {
                            item.Reciver = decryptedOrder.ToName;
                            item.ReciverPhone = decryptedOrder.ToMobile;
                            item.BuyerMemberName = item.Reciver;
                            item.BuyerMemberId = item.Reciver;
                            item.ToAddress = decryptedOrder.ToFullAddress;
                        }
                    });
                    result.Rows?.ForEach(t =>
                    {
                        t.BuyerMemberName = t.BuyerMemberName.ToEncryptName();
                        t.BuyerMemberId = t.BuyerMemberId.ToEncryptName();
                        t.ReciverPhone = t.ReciverPhone.ToPddEncryptPhone();
                        t.Reciver = t.Reciver.ToEncryptName();
                        t.ToAddress = t.ToAddress.ToPddEncryptAddress();
                    });

                    //result.Rows?.ForEach(t =>
                    //{
                    //    t.BuyerMemberName = t.BuyerMemberName.ToEncryptName();
                    //    t.BuyerMemberId = t.BuyerMemberId.ToEncryptName();
                    //    t.ReciverPhone = t.ReciverPhone.ToPddEncryptPhone();
                    //    t.Reciver = t.Reciver.ToEncryptName();
                    //    t.ToAddress = t.ToAddress.ToPddEncryptAddress();
                    //});
                }

            }

            return SuccessResult(new { Results = result });
        }


        /// <summary>
        /// 微商,获取打印记录
        /// </summary>
        /// <param name="waybillCodeOrderId"></param>
        /// <returns></returns>
        public ActionResult WsLoadPrintHistory(int waybillCodeOrderId)
        {
            var sids = SiteContext.Current.AllShops?.Select(f => f.Id)?.ToList();
            var dataList = _printHistoryService.GetPrintHistoryByWaybillCodeId(sids, waybillCodeOrderId, "").OrderByDescending(t => t.PrintDate).ToList();
            return Json(dataList);
        }

        /// <summary>
        /// 获取运单打印记录
        /// </summary>
        /// <param name="waybillCodeOrderId"></param>
        /// <returns></returns>
        public ActionResult LoadPrintHistory(string orderId, string waybillCode, string childWaybillCode)
        {
            var sids = SiteContext.Current.AllShops?.Select(f => f.Id)?.ToList();
            var dataList = _printHistoryService.GetPrintHistoryByWaybillCodeId(sids, orderId, waybillCode, childWaybillCode)?.OrderByDescending(f => f.PrintDate)?.ToList();
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (pt == PlatformType.Pinduoduo.ToString())
            {
                dataList?.ForEach(w =>
                {
                    w.BuyerMemberName = w.BuyerMemberName.ToEncryptName();
                    w.BuyerMemberId = w.BuyerMemberId.ToEncryptName();
                    w.Reciver = w.Reciver.ToEncryptName();
                    w.ReciverAddress = w.ReciverAddress.ToPddEncryptAddress();
                    w.ReciverPhone = w.ReciverPhone.ToPddEncryptPhone();
                });
            }
            else if (pt == PlatformType.Jingdong.ToString() || pt == PlatformType.Taobao.ToString())
            {
                dataList?.ForEach(w =>
                {
                    w.ReciverPhone = w.ReciverPhone.ToPddEncryptPhone();
                    w.Reciver = w.Reciver.ToEncryptName();
                    w.ReciverAddress = w.ReciverAddress.ToTaoBaoEncryptAddress();
                    if (pt == PlatformType.Taobao.ToString())
                    {
                        //淘宝还需加密发件人信息
                        w.Sender = w.Sender.ToEncryptName();
                    }
                });
            }
            return Json(dataList);
        }

        public ActionResult LoadReprintModel(int waybillCodeId)
        {
            var model = _service.GetReprintWaybillCodeModel(waybillCodeId);
            return Json(model);
        }

        /// <summary>
        /// 获取运单打印记录（批量）
        /// </summary>
        /// <param name="waybillCodeIdAndChildWaybillCodeList"></param>
        /// <returns></returns>
        public ActionResult LoadPrintHistoryList(List<GetPrintHistListRequestModel> waybillCodeIdAndChildWaybillCodeList)
        {
            if (waybillCodeIdAndChildWaybillCodeList == null || waybillCodeIdAndChildWaybillCodeList.Count == 0)
                return FalidResult("后台未收到数据!");

            ////去除key为0的，printhist 表中 waybillcodeid为0的有几十万，都是迁移数据数据，所以一旦前端传了一个0过来，查询肯定会卡死。
            //var paras = waybillCodeIdAndChildWaybillCodeList.Where(f => f.Key > 0).ToList();

            var sids = SiteContext.Current.AllShops?.Select(f => f.Id)?.ToList();
            //var siblingSids = SiteContext.Current.SiblingShop?.Select(f => f.Id);
            //if (sids == null)
            //    sids = new List<int>();
            //if (siblingSids != null && siblingSids.Any())
            //    sids.AddRange(siblingSids);

            var dataList = _printHistoryService.LoadPrintHistoryList(waybillCodeIdAndChildWaybillCodeList, sids);//?.GroupBy(f => f.WaybillCodeOrderId).ToList().Select(f => f.OrderByDescending(ob => ob.PrintDate).FirstOrDefault());
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (pt == PlatformType.Pinduoduo.ToString())
            {
                dataList?.ForEach(w =>
                {
                    w.BuyerMemberName = w.BuyerMemberName.ToEncryptName();
                    w.BuyerMemberId = w.BuyerMemberId.ToEncryptName();
                    w.Reciver = w.Reciver.ToEncryptName();
                    w.ReciverAddress = w.ReciverAddress.ToPddEncryptAddress();
                    w.ReciverPhone = w.ReciverPhone.ToPddEncryptPhone();
                });
            }
            else if (pt == PlatformType.Jingdong.ToString())
            {
                dataList?.ForEach(w =>
                {
                    w.ReciverPhone = w.ReciverPhone.ToPddEncryptPhone();
                });
            }
            return Json(dataList);
        }

        /// <summary>
        /// 获取运单使用记录
        /// </summary>
        /// <param name="expressId"></param>
        /// <param name="waybillCode"></param>
        /// <returns></returns>
        public ActionResult LoadWaybillCodeUseRecord(int expressId, string waybillCode)
        {
            var dataList = _service.GetWaybillCodeList(waybillCode, expressId);
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (pt == PlatformType.Pinduoduo.ToString() || pt == PlatformType.Jingdong.ToString() || pt == PlatformType.Taobao.ToString())
            {
                dataList?.ForEach(w =>
                {
                    w.BuyerMemberName = w.BuyerMemberName.ToEncryptName();
                    w.BuyerMemberId = w.BuyerMemberId.ToEncryptName();
                    w.Reciver = w.Reciver.ToEncryptName();
                    w.ToAddress = w.ToAddress.ToPddEncryptAddress();
                    w.ReciverPhone = w.ReciverPhone.ToPddEncryptPhone();
                });
            }
            return Json(dataList);
        }

        /// <summary>
        /// 获取运单发货记录
        /// </summary>
        /// <param name="expressCode"></param>
        /// <param name="waybillCode"></param>
        /// <returns></returns>
        public ActionResult LoadWaybillCodeSendRecord(string expressCode, string waybillCode)
        {
            var sids = SiteContext.Current.AllShops?.Select(f => f.Id)?.ToList();
            //var siblingSids = SiteContext.Current.SiblingShop?.Select(f => f.Id);
            //if (sids == null)
            //    sids = new List<int>();
            //if (siblingSids != null && siblingSids.Any())
            //    sids.AddRange(siblingSids);

            var dataList = _sendHistoryService.GetSendHistoryList(sids, waybillCode, expressCode);
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (pt == PlatformType.Pinduoduo.ToString() || pt == PlatformType.Jingdong.ToString() || pt == PlatformType.Taobao.ToString())
            {
                dataList?.ForEach(w =>
                {
                    w.BuyerMemberName = w.BuyerMemberName.ToEncryptName();
                    w.BuyerMemberId = w.BuyerMemberId.ToEncryptName();
                    w.Reciver = w.Reciver.ToEncryptName();
                    w.ReciverAddress = w.ReciverAddress.ToPddEncryptAddress();
                    w.ReciverPhone = w.ReciverPhone.ToPddEncryptPhone();
                });
            }
            return Json(dataList);
        }

        /// <summary>
        /// 取消电子面单
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="orderId"></param>
        /// <param name="waybillCode"></param>
        /// <returns></returns>
        [LogForOperatorFilter("单个回收")]
        public ActionResult CancelWaybillCode(int templateId, List<WaybillCodeRecycleViewModel> wcRecycleViewModel)
        {
            var errorList = new List<string>();
            var successList = new List<int>();

            //由于订单列表那边的回收，可能对应的模板不是实际打印单号的模板，会导致回收有问题，或者回收后还回分享单号有问题
            //所以要根据 底单去查询出实际使用的模板
            var wycIds = wcRecycleViewModel.Select(f => f.WaybillCodeId).ToList();
            var templateIdWaybillCodeId = _service.GetTemplateIdByWaybillCodeId(wycIds);
            var tIdGroup = templateIdWaybillCodeId.GroupBy(f => f.TemplateId);
            tIdGroup.ToList().ForEach(f =>
            {
                var tempId = f.Key;
                var wcyTempIds = f.Select(x => x.ID).ToList();
                var data = wcRecycleViewModel.Where(p => wcyTempIds.Contains(p.WaybillCodeId))?.ToList();

                var tempErroList = new List<string>();
                var tempSuccList = new List<int>();

                CancelAction(tempErroList, tempSuccList, new BatchWaybillCodeRecycleViewModel()
                {
                    TemplateId = tempId,
                    WaybillCodeRecycleViewModels = data
                });

                errorList.AddRange(tempErroList);
                successList.AddRange(tempSuccList);
            });


            //批量回收，返回运单id，用户订单列表删除回收的运单号
            if (errorList.Count == 0)
                return SuccessResult(data: successList);
            else
                return FalidResult(errorList.ToJson(), data: successList);
        }

        /// <summary>
        /// 取消电子面单
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="orderId"></param>
        /// <param name="waybillCode"></param>
        /// <returns></returns>
        [LogForOperatorFilter("批量回收")]
        public ActionResult BatchCancelWaybillCode(List<BatchWaybillCodeRecycleViewModel> batchWaybillCodeRecycleVmList)
        {
            var errorList = new List<string>();
            var successList = new List<int>();

            batchWaybillCodeRecycleVmList.ForEach(item =>
            {
                CancelAction(errorList, successList, item);
            });

            //批量回收，返回运单id，用户订单列表删除回收的运单号
            if (errorList.Count == 0)
                return SuccessResult(data: new { IsError = false, ErrorList = new List<string>(), SuccessList = successList });
            else
                return SuccessResult(data: new { IsError = true, ErrorList = errorList, SuccessList = successList });
        }

        public void CancelAction(List<string> errorList, List<int> successList, BatchWaybillCodeRecycleViewModel model)
        {
            if (model.TemplateId == 0)
            {
                errorList.Add("模板id为0,找不到对应模板，请确认数据是否为迁移数据，迁移过来的底单不支持回收。");
                return;
            }

            //1.获取模板
            var template = _printTemplateService.Get(model.TemplateId, false);

            if (template == null)
            {
                errorList.Add("单号打印的模板未找到，请确认数据是否为迁移数据，迁移过来的底单不支持回收。");
                return;
            }

            var express = _expressCompanyService.Get(template.ExpressCompanyId);

            Tuple<bool, string> result = null;
            //网点模板
            if (template.PrintTemplateType == Data.Enum.PrintTemplateType.LogisticTemplate)
            {
                //获取网点账号
                UserSiteInfo siteAccount = null; //_userSiteInfoService.Get("WHERE TemplateId=@TemplateId", new { TemplateId = model.TemplateId }).FirstOrDefault();
                var templateExtend = (new PrintTemplateExtendService()).GetModel(template.Id);
                siteAccount = new UserSiteInfo()
                {
                    UserName = templateExtend.CustomerName,
                    UserPassword = templateExtend.CustomerPwd
                };
                if (siteAccount == null)
                {
                    errorList.Add("网点账号不存在");
                }

                switch (express.CompanyCode)
                {
                    case "STO":
                        //errorList.Add("申通网点电子面单号回收需在大客户平台回收，具体回收步骤请联系申通快递公司!");
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = _service.WlbWaybillICancelSTO(item.OrderId, item.WaybillCode);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    case "YTO":
                        //model.WaybillCodeRecycleViewModels.ForEach(item =>
                        //{
                        //    result = _service.WlbWaybillICancelYTO(siteAccount, item.OrderId, item.WaybillCode);
                        //    if (result.Item1 == false)
                        //    {
                        //        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        //    }
                        //    else
                        //    {
                        //        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                        //    }
                        //});
                        errorList.Add("不支持电子面单账号手动回收订单,系统30天自动回收订单!");
                        break;
                    case "ZTO":
                        errorList.Add("不支持手动回收,获取单号后无扫描记录,30-60天后系统自动回收!");
                        //model.WaybillCodeRecycleViewModels.ForEach(item =>
                        //{
                        //    result = _service.WlbWaybillICancelZTO(siteAccount, item.OrderId, item.WaybillCode);
                        //    if (result.Item1 == false)
                        //    {
                        //        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        //    }
                        //    else
                        //    {
                        //        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                        //    }
                        //});
                        break;
                    case "YUNDA":
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = _service.WlbWaybillICancelYUNDA(siteAccount, item.OrderId, item.WaybillCode);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    case "HTKY":
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = _service.WlbWaybillICancelHTKY(siteAccount, item.OrderId, item.WaybillCode);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    case "2608021499_235":
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = _service.WlbWaybillICancelANE(siteAccount, item.OrderId, item.WaybillCode);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    case "SF": //顺丰，打单走快递鸟，回收也走快递鸟
                    case "PJ": //品骏，打单走快递鸟，回收也走快递鸟
                        var cpcodeMappingModel = (new ExpressCpCodeMappingService()).Get(express.CompanyCode, "KuaiDiNiao");
                        var kdnLogisticService = new KuaiDiNiaoLogisticService();
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = kdnLogisticService.CancelWaybillCode(cpcodeMappingModel?.CpCode, item.OrderId, item.WaybillCode, templateExtend.CustomerName, templateExtend.CustomerPwd);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    case "JT":
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = _service.WlbWaybillICancelJT(siteAccount, item.OrderId, item.WaybillCode);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    case "ZYKD":
                        errorList.Add("暂时不支持回收,请联系网点回收!");
                        break;
                    case "DBKD":
                        model.WaybillCodeRecycleViewModels.ForEach(item =>
                        {
                            result = _service.WlbWaybillICancelDBKD(item.OrderId, item.WaybillCode);
                            if (result.Item1 == false)
                            {
                                errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                            }
                            else
                            {
                                successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                            }
                        });
                        break;
                    default:
                        errorList.Add(string.Format("未找到匹配的快递【{0}】", express.CompanyCode));
                        break;
                }

            }
            //菜鸟模板
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.CaiNiaoPrintTemplate || template.PrintTemplateType == Data.Enum.PrintTemplateType.CaiNiaoBQSPrintTemplate)
            {
                ////获取模板使用的淘宝授权账号
                //var authAccount = _templateRelationAuthInfoService.Get("WHERE TemplateId=@TemplateId", new { TemplateId = template.Id }).FirstOrDefault();
                //if (authAccount == null)
                //{
                //    return FalidResult("未找到授权账号");
                //}

                ////获取授权账号
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);

                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    result = TopCaiNiaoApiService.CancelCainiaoWaybillCode(waybillAuthConfig, express.CompanyCode, item.WaybillCode);
                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //菜鸟官方模板
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.LinkPrintTemplate)
            {
                ////获取授权账号
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);

                CloudCaiNiaoApiService _cloudCaiNiaoApiService = new CloudCaiNiaoApiService(waybillAuthConfig);

                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    result = _cloudCaiNiaoApiService.CancelCainiaoWaybillCode(express.CompanyCode, item.WaybillCode);
                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //拼多多模板
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.PddPrintTemplate || template.PrintTemplateType == Data.Enum.PrintTemplateType.PddBQSPrintTemplate)
            {
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
                var pddWaybillCodeApiService = new PddWaybillApiService(waybillAuthConfig);
                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    result = pddWaybillCodeApiService.CancelPinduoduoWaybillCode(template.ExpressCpCodeMapping.CpCode, item.WaybillCode);
                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //京东无界模板
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.JdwjPrintTemplate)
            {
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
                var wujieWaybillCodeApiService = new WuJieWaybillApiService(waybillAuthConfig);
                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    result = wujieWaybillCodeApiService.CancelWaybillCode(item.WaybillCode, template.ExpressCpCodeMapping.CpCode);
                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                    }
                    else
                    {
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            //京东快递模板
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.JdkdPrintTemplate)
            {
                CaiNiaoAuthInfo cainiaoAuthInfo = null;
                if (template.CaiNiaoAuthInfo == null)
                    cainiaoAuthInfo = _caiNiaoAuthInfoService.Get(template.TemplateRelationAuthInfo.CaiNiaoAuthInfoId);
                else
                    cainiaoAuthInfo = template.CaiNiaoAuthInfo;
                //转换为api授权账号
                var waybillAuthConfig = _commService.CaiNiaoAuthInfoToWaybillAuthConfig(cainiaoAuthInfo);
                var jdLogisticService = new JDLogisticService(false, waybillAuthConfig);

                var templateExtend = new PrintTemplateExtendService().GetModel(template.Id);

                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {
                    if (templateExtend == null)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, "找不到模板的商家信息，请联系我们!"));
                    }
                    else
                    {
                        result = jdLogisticService.CancelWaybillCode(item.WaybillCode, templateExtend.CustomerName);
                        if (result.Item1 == false)
                            errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                        else
                            successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }
            else if (template.PrintTemplateType == Data.Enum.PrintTemplateType.FengQiaoTemplate)
            {
                var templateExtend = (new PrintTemplateExtendService()).GetModel(template.Id);
                var siteAccount = new UserSiteInfo()
                {
                    UserName = templateExtend.CustomerName,
                    UserPassword = templateExtend.CustomerPwd
                };
                model.WaybillCodeRecycleViewModels.ForEach(item =>
                {

                    result = _service.WlbWaybillICancelSF(siteAccount, item.OrderId, item.WaybillCode);
                    if (result.Item1 == false)
                    {
                        errorList.Add(string.Format("{0}【单号：{1}】,回收失败：【{2}】", express.CompanyName, item.WaybillCode, result.Item2));
                    }
                    else
                    {
                        //顺丰一单多包回收其中一个单号，还需要把相同pid的单号都回收掉（更改底单状态）
                        var ids = _service.GetOnToManyWaybillCodeIds(item.OrderId, item.WaybillCode);
                        if (ids?.Any() == true)
                            successList.AddRange(ids.Select(f => f.ID));
                        successList.Add(item.WaybillCodeId); //保存 回收成功的 waybillcodeId
                    }
                });
            }

            if (successList.Count > 0)
            {
                //状态：1：已打印，2：单号已回收，3：已发货
                _service.UpdateWaybillCodeStatusById(2, successList, template);

                // 检查底单记录对应的订单，是否有打标：地址变更，如果有就删除该标签
                var orderIds = model.WaybillCodeRecycleViewModels.Select(a => a.OrderId).ToList();
                new OrderTagsRepository().CheckTagChangeOfAddress(orderIds);
            }
        }

        #region 订单导出
        public ActionResult ExportExcel()
        {
            try
            {
                var shop = SiteContext.Current.CurrentLoginShop;
                var task = _exportTaskService.GetExportTask(shop.Id, ExportType.WaybillCode.ToInt());
                if (task != null && task.Status >= 0 && task.Status < 4)
                    return FalidResult("已存在订单导出任务，如需重新导出，请先取消再创建新导出任务", GetExportTaskToWeb(task));

                var options = Request.Form["options"].ToString2();
                options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
                options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果
                WaybillCodeRequestModel requestModel = JsonExtension.ToObject<WaybillCodeRequestModel>(options) ?? new WaybillCodeRequestModel();
                if (requestModel.ShopId == null || !requestModel.ShopId.Any())
                    requestModel.ShopId = new List<int> { SiteContext.Current.CurrentShopId };
                else if (requestModel.ShopId != null && requestModel.ShopId.Count > 0)
                {
                    var shopIds = requestModel.ShopId.Where(f => f != 0).ToList();
                    if (shopIds.Count == 0)
                    {
                        SiteContext.Current.AllShops.Where(m => m.PlatformType == SiteContext.Current.CurrentLoginShop.PlatformType).ToList().ForEach(item =>
                        {
                            shopIds.Add(item.Id);
                        });
                    }
                    requestModel.ShopId = shopIds;
                }

                var checkedFieldsSet = _commSettingService.Get("WaybillCodeListExportSet", shop.Id);
                var checkedJson = checkedFieldsSet?.Value.ToString2() ?? "";
                var checkedFieldsLst = checkedJson.IsNullOrEmpty() ? new List<WaybillCodeCheckModel>() : JsonExtension.ToList<WaybillCodeCheckModel>(checkedJson);
                if (checkedFieldsLst.Count == 0)
                    return FalidResult("请先设置导出配置，再进行导出操作");

                var fields = checkedFieldsLst.Select(m => m.Value).Distinct().ToList();
                if (fields.Contains("RowIndex"))
                    fields.Remove("RowIndex");
                if (fields.Contains("ShopName"))
                {
                    fields.Remove("ShopName");
                    fields.Add("ShopId");
                }
                if (fields.Contains("ToAddress"))
                {
                    fields.Add("ToProvince");
                    fields.Add("ToCity");
                    fields.Add("ToDistrict");
                }
                if (fields.Contains("OrderId"))
                {
                    fields.Add("CustomerOrderId");
                    fields.Add("OrderIdJoin");
                }
                if (fields.Contains("ShopId") == false)
                    fields.Add("ShopId");
                if (fields.Contains("OrderId") == false)
                    fields.Add("OrderId");

                if (fields.Count == 0)
                    return FalidResult("请先设置导出配置，再进行导出操作");
                fields = fields.ConvertAll(f => "wc." + f).ToList();
                //获取任务导出阈值和页大小
                var limitCount = 0;
                var commSets = _commonSettingService.GetSets(new List<string> { "ExportByTaskWaybillCodeCountLimit", "ExportDefaultPageSize" }, 0);
                var limitCountSet = commSets?.FirstOrDefault(m => m.Key == "ExportByTaskWaybillCodeCountLimit");
                var pageSizeSet = commSets?.FirstOrDefault(m => m.Key == "ExportDefaultPageSize");
                limitCount = limitCountSet?.Value.ToInt() ?? CustomerConfig.ExportByTaskWaybillCodeCountLimit;
                limitCount = limitCount > 0 ? limitCount : CustomerConfig.ExportByTaskWaybillCodeCountLimit;
                var pageSize = pageSizeSet?.Value.ToInt() ?? 1000;
                pageSize = pageSize > 0 ? pageSize : 1000;
                //查询第一页数据
                requestModel.PageIndex = 1;
                requestModel.PageSize = pageSize;
                var returnCount = requestModel.PageSize;
                var firstTuple = _service.LoadListForExportExcel(requestModel, fields);
                var firstPageList = firstTuple.Item1;
                var totalCount = firstTuple.Item2;
                if (totalCount > limitCount)
                {
                    //使用任务方式导出
                    var newTask = new ExportTask
                    {
                        IP = Request.UserHostAddress,
                        CreateTime = DateTime.Now,
                        PlatformType = shop.PlatformType,
                        ShopId = shop.Id,
                        UserId = SiteContext.Current.CurrentLoginSubUser?.Id.ToString(),
                        Status = 0,
                        Type = ExportType.WaybillCode.ToInt(),
                        PageIndex = 1,
                        PageSize = pageSize,
                        TotalCount = totalCount,
                        ParamJson = requestModel.ToJson(),
                        FromModule = "底单查询--Excel导出",
                    };
                    newTask.Id = _exportTaskService.Add(newTask);
                    return SuccessResult("导出任务创建成功", GetExportTaskToWeb(newTask));
                }

                // 直接导出方式进行导出            
                var list = firstPageList;
                returnCount = firstPageList.Count;
                if (requestModel.PageSize == returnCount)
                {
                    requestModel.PageIndex++;//开始第二页查询
                    while (requestModel.PageSize == returnCount)
                    {
                        var tuple = _service.LoadListForExportExcel(requestModel, fields);
                        var pgList = tuple?.Item1 ?? new List<WaybillCode>();
                        list.AddRange(pgList);
                        requestModel.PageIndex++;
                        returnCount = pgList.Count;
                    }
                }

                if (shop.PlatformType == Data.Enum.PlatformType.Jingdong.ToString())
                {
                    var pids = new List<string>();
                    list?.ForEach(t =>
                    {
                        if (!string.IsNullOrEmpty(t.OrderIdJoin))
                        {
                            pids.AddRange(t.OrderIdJoin.Split(','));
                        }
                        else
                        {
                            pids.Add(t.OrderId.Trim('C'));
                        }
                        t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
                    });
                    jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
                }

                if ((list?.Count ?? 0) == 0)
                    return FalidResult("无满足条件的数据导出");

                var fileName = "底单查询-打印.xls";
                //IWorkbook workbook = BuildExcel(list, checkedFieldsLst, fileName);
                var workbook = BuildExccelService.BuildWaybillCodeExcel(list, checkedFieldsLst, fileName); //BuildExcel(list, checkedFieldsLst, fileName);

                Response.Cookies.Add(new HttpCookie("downloadToken", Request.Form["downloadToken"].ToString2()));
                using (MemoryStream ms = new MemoryStream())
                {
                    workbook.Write(ms);
                    var buffer = ms.GetBuffer();
                    return File(buffer, "application/ms-excel", ExcelHelper.GetFileName(fileName, Request));
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"底单查询Excel导出失败：{ex}");
                return FalidResult("程序异常，请联系我们");
            }
        }

        private WaybillCode EncryptReceiverInfo(WaybillCode order)
        {
            order.BuyerMemberName = order.BuyerMemberName.ToEncryptName();
            order.BuyerMemberId = order.BuyerMemberId.ToEncryptName();
            order.ReciverPhone = order.ReciverPhone.ToPddEncryptPhone();
            order.Reciver = order.Reciver.ToEncryptName();
            order.ToAddress = order.ToAddress.ToPddEncryptAddress();
            //order.ToDistrict = "****";
            return order;
        }

        private IWorkbook BuildExcel(List<WaybillCode> waybillCodeLst, List<WaybillCodeCheckModel> checkedItems, string fileName)
        {
            var pt = SiteContext.Current.CurrentLoginShop.PlatformType;
            if (waybillCodeLst != null && waybillCodeLst.Any()
                && (pt == PlatformType.Pinduoduo.ToString()
                || pt == PlatformType.Jingdong.ToString() || pt == PlatformType.Taobao.ToString()))
            {
                if (pt == PlatformType.Pinduoduo.ToString())
                {
                    var tempOrders = waybillCodeLst?.Select(x => new Order { PlatformOrderId = x.OrderId, ShopId = x.ShopId, ToName = x.Reciver, ToMobile = x.ReciverPhone, ToProvince = x.ToProvince, ToCity = x.ToCity, ToCounty = x.ToDistrict, ToAddress = x.ToAddress }).ToList();
                    BranchShareRelationService.TryToDecryptPddOrders(tempOrders, true);
                    //按店铺分组
                    waybillCodeLst?.ForEach(item =>
                    {
                        var decryptedOrder = tempOrders.FirstOrDefault(x => x.PlatformOrderId == item.OrderId && x.ShopId == item.ShopId);
                        if (decryptedOrder != null)
                        {
                            item.Reciver = decryptedOrder.ToName;
                            item.ReciverPhone = decryptedOrder.ToMobile;
                            item.BuyerMemberName = item.Reciver;
                            item.BuyerMemberId = item.Reciver;
                            item.ToAddress = decryptedOrder.ToFullAddress;
                        }
                    });
                }
                waybillCodeLst.ForEach(o =>
                {
                    EncryptReceiverInfo(o);
                });
            }

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);

            ISheet sheet = workbook.CreateSheet("底单记录");
            ICellStyle headStyle = GetHeadStyle(workbook);
            ICellStyle contentStyle = GetContentStyle(workbook, HorizontalAlignment.Center);
            ICellStyle leftContentStyle = GetContentStyle(workbook, HorizontalAlignment.Left);


            IRow headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 15;

            int colIndex = 0;
            checkedItems.ForEach(model =>
            {
                var headName = model.Text.ToString2();
                SetColumnWidth(sheet, headName, colIndex);

                headerRow.CreateCell(colIndex).SetCellValue(headName);
                headerRow.GetCell(colIndex).CellStyle = headStyle;
                colIndex++;
            });

            int rowIndex = 1;
            waybillCodeLst.ForEach(model =>
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                dataRow.HeightInPoints = 20;
                var dic = (model ?? new WaybillCode()).ToDictionary();

                colIndex = 0;
                foreach (var item in checkedItems)
                {
                    ICellStyle tmpStyle = contentStyle;
                    var key = item?.Value.ToString2() ?? "";
                    if (dic.ContainsKey(key) || key == "RowIndex" || key == "ShopName")
                    {
                        var val = key == "RowIndex" ? rowIndex.ToString2() : key == "ShopName" ? "" : dic[key].ToString2();
                        if (key != "RowIndex")
                        {
                            if (key == "ToAddress")
                                val = $"{dic["ToProvince"].ToString2()} {dic["ToCity"].ToString2()} {dic["ToDistrict"].ToString2()} {dic["ToAddress"].ToString2()}";
                            else if (key == "BuyerRemark" || key == "SellerRemark")
                            {
                                var arr = val.Split("|||".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                                val = arr.Length == 0 ? "" : val;
                            }
                            else if (key == "Status")
                            {
                                if (val == "1")
                                    val = "已打印";
                                else if (val == "2")
                                    val = "已回收";
                                else if (val == "3")
                                    val = "已发货";
                                else
                                    val = "未知状态";
                            }
                            else if (key == "ShopName")
                            {
                                var shops = SiteContext.Current.AllShops;
                                val = shops?.FirstOrDefault(m => m.Id == dic["ShopId"].ToInt()).NickName ?? "";
                            }
                            else if (key == "OrderId")
                            {
                                var customerOrderId = dic["CustomerOrderId"].ToString2();
                                if (!customerOrderId.IsNullOrEmpty())
                                    val = customerOrderId;
                                else
                                {
                                    var orderIdJoin = dic["OrderIdJoin"].ToString2();
                                    if (!orderIdJoin.IsNullOrEmpty())
                                        val = orderIdJoin.Replace(",", "\n");
                                }
                            }
                            else if (key == "ExpressWayBillCode")
                            {
                                var childWaybillCode = dic["ChildWaybillCode"].ToString2();
                                val = $"{val}{(childWaybillCode.IsNullOrEmpty() ? "" : $"/{childWaybillCode}")}";
                            }
                        }

                        if (key == "BuyerRemark" || key == "SellerRemark" || key == "ToAddress" || key == "SendContent")
                            tmpStyle = leftContentStyle;

                        dataRow.CreateCell(colIndex).SetCellValue(val.Trim().Trim("\n".ToArray()));
                        dataRow.GetCell(colIndex).CellStyle = tmpStyle;
                        colIndex++;
                    }
                }

                var HeightInPoints = ExcelHelper.AutoSizeRowHeight(workbook, sheet, dataRow);
                dataRow.HeightInPoints = HeightInPoints;
                rowIndex++;
            });

            return workbook;
        }

        private void SetColumnWidth(ISheet sheet, string headName, int index)
        {
            int width = 20 * 256;
            if (headName == "序号" || headName == "商品数量")
                width = 10 * 256;
            else if (headName == "省份" || headName == "重量" || headName == "订单金额")
                width = 16 * 256;
            else if (headName == "快递单号" || headName == "收件人姓名" || headName == "发件人" || headName == "发货时间" || headName == "打单时间" || headName == "状态" || headName == "收件人电话")
                width = 20 * 256;
            else if (headName == "快递公司" || headName == "订单编号" || headName == "买家旺旺" || headName == "店铺名称")
                width = 25 * 256;
            else if (headName == "详细地址" || headName == "买家留言" || headName == "卖家备注")
                width = 35 * 256;
            else if (headName == "发货内容")
                width = 45 * 256;
            sheet.SetColumnWidth(index, width);
        }

        private ICellStyle GetHeadStyle(IWorkbook workbook)
        {
            IFont font = workbook.CreateFont();
            font.FontName = "Times New Roman";
            font.Boldweight = short.MaxValue;
            font.FontHeightInPoints = 11;

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(font);
            headerStyle.Alignment = HorizontalAlignment.Center;//内容居中显示
            headerStyle.WrapText = true;
            headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightOrange.Index;
            headerStyle.FillPattern = FillPattern.SolidForeground;
            return headerStyle;
        }

        private ICellStyle GetContentStyle(IWorkbook workbook, HorizontalAlignment alignment)
        {
            IFont font = workbook.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = workbook.CreateCellStyle();
            contentStyle.SetFont(font);
            contentStyle.Alignment = alignment;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.WrapText = true;

            return contentStyle;
        }

        private MemoryStream StreamToMemoryStream(Stream instream)
        {
            MemoryStream outstream = new MemoryStream();
            const int bufferLen = 4096;
            byte[] buffer = new byte[bufferLen];
            int count = 0;
            while ((count = instream.Read(buffer, 0, bufferLen)) > 0)
            {
                outstream.Write(buffer, 0, count);
            }
            return outstream;
        }
        #endregion

        public ActionResult GetTodayPrintedCount()
        {
            var shop = SiteContext.Current.CurrentLoginShop;
            WaybillCodeRequestModel requestModel = new WaybillCodeRequestModel
            {
                ShopId = new List<int> { shop.Id },
                StartDate = DateTime.Now.ToString("yyyy-MM-dd"),
                EndDate = DateTime.Now.ToString("yyyy-MM-dd 23:59:59"),
                PageSize = 1
            };

            var pageModel = _service.LoadList(requestModel);
            if (shop.PlatformType == Data.Enum.PlatformType.Jingdong.ToString())
            {
                var pids = new List<string>();
                pageModel?.Rows?.ForEach(t =>
                {
                    if (!string.IsNullOrEmpty(t.OrderIdJoin))
                    {
                        pids.AddRange(t.OrderIdJoin.Split(','));
                    }
                    else
                    {
                        pids.Add(t.OrderId.Trim('C'));
                    }
                    t.ReciverPhone = t.ReciverPhone.ToEncrytPhone();
                });
                jos_sdk_net.JdRequestLogger.Order(shop.AccessToken, shop.Id.ToString(), shop.ShopId, 1, pids);
            }
            return Json(pageModel.Total);
        }
    }
}