/// <reference path="jquery-1.10.2.min.js" />
/// <reference path="layer/layer.js" />

var commonModule = (function (common, $, ly) {

    var _areaCodeInfoList = {}; //保存选择过的区域信息

    var _printerBindList = [];

    // 数组判断兼容IE8
    if (!Array.isArray) {
        Array.isArray = function (arg) {
            return Object.prototype.toString.call(arg) === '[object Array]';
        };
    }
    var _objectClone = function (instance) {
        var newObj = {};
        for (var i in instance) {
            if (instance[i] == null) {
                newObj[i] = null;
            }
            else if (Array.isArray(instance[i]) == true) {
                newObj[i] = _arrayClone(instance[i]);
            }
            else if (typeof (instance[i]) == 'object') {
                newObj[i] = _objectClone(instance[i]);
            }
            else if (typeof (instance[i]) == 'function') {
                newObj[i] = _functionClone(instance[i]);
            }
            else {
                newObj[i] = instance[i];
            }
        }
        return newObj;
    };
    var _arrayClone = function (instance) {
        var newArray = [];
        for (var i = 0; i < instance.length; i++) {
            if (instance[i] == null) {
                newArray[i] = null;
            }
            else if (Array.isArray(instance[i]) == true) {
                newArray.push(_arrayClone(instance[i]));
            }
            else if (typeof (instance[i]) == 'object') {
                newArray.push(_objectClone(instance[i]));//newArray[i] = _objectClone(instance[i]);
            }
            else if (typeof (instance[i]) == 'function') {
                newArray[i] = _functionClone(instance[i]);
            } else {
                newArray.push(instance[i]);//newArray[i] = instance[i];
            }
        }
        return newArray;
    };
    var _functionClone = function (instance) {
        var that = instance;
        var newFunc = function () {
            return that.apply(this, instance.arguments);
        };
        for (var i in this) {
            newFunc[i] = this[i];
        }
        return newFunc;
    };

    common.PlatformType = ''; //站点平台
    common.CurrentPlatformAuthEntry = ''; //平台授权入口
    // 是否是头条系平台
    common.IsTouTiaoXi = function (pt) {
        var isTouTiaoXi = false;
        var platformType = ((pt || "") == "" ? (common.PlatformType || "") : pt).toLowerCase();
        if (platformType == "toutiao" || platformType == "tt" ||
            platformType == "zhidian" || platformType == 'zd' ||
            platformType == "douyinxiaodian" || platformType == "dyxd" ||
            platformType == "toutiaoxiaodian" || platformType == "ttxd" ||
            platformType == "luban" || platformType == "lb") {
            isTouTiaoXi = true;
        }
        return isTouTiaoXi;
    }

    common.ConvertToRealPlatformType = function (pt) {
        if (common.IsTouTiaoXi(pt))
            return "TouTiao";
        return pt;
    }

    common.CanUpdateSellerRemark = function (pt) {
        pt = pt.toLowerCase();
        return pt != "mengtui" && pt != "yunji" && pt != "vipshop" && common.IsPddFds() == false; //pt != "toutiao" && 
    }

    var _flag_position = {
        '0': { x: -70, y: -20 },   //灰色
        '1': { x: 0, y: -20 },   //红色
        '4': { x: -14, y: -20 }, //黄色
        '3': { x: -28, y: -20 }, //青色
        '2': { x: -42, y: -20 }, //蓝色
        '5': { x: -56, y: -20 }, //紫色
    }

    common.StampTemplates = []; //常用模板

    common.GetFlagPosition = function () { return _flag_position; }

    //是否是菜鸟快递模板
    common.IsCainiaoTemplate = function (templateType) {
        return templateType == 2 || (templateType > 3 && templateType <= 20 && templateType != 10);
    }

    //是否是菜鸟快运模板
    common.IsKuaiYunTemplate = function (templateType) {
        return templateType == 7 || templateType == 8;
    }

    //是否是菜鸟官方模板
    common.IsLinkTemplate = function (templateType) {
        return templateType >= 40 && templateType < 60;
    }

    //是否是普通模板 (1:传统面单，2:网点面单，3:顺丰丰桥）
    common.IsNormalTemplate = function (templateType) {
        return templateType == 1 || templateType == 3 || templateType == 10;
    }

    //是否是网点中通
    common.IsZhiLian = function (templateType, companyCode) {
        if (templateType == 3 && (companyCode == "ZTO" || companyCode == "STO" || companyCode == "YTO" || companyCode == "HTKY"))
            return true;
        return false;
    }

    //是否是网点模板 (1:传统面单，2:网点面单，3:顺丰丰桥）
    common.IsSiteTemplate = function (templateType) {
        return templateType == 3 || templateType == 10;
    }

    //是否是拼多多模板
    common.IsPddTemplate = function (templateType) {
        return templateType > 20 && templateType < 30;
    }

    //是否是京东无界模板
    common.IsJdWjTemplate = function (templateType) {
        return templateType >= 60 && templateType < 80;
    }

    //是否是京东快递模板
    common.IsJdKdTemplate = function (templateType) {
        return templateType > 80 && templateType < 90;
    }

    //找出网点
    common.FindBranch = function (branchList, templateBranch) {

        var getAddress = function (p, c, a, t, d) {
            var addr = "";
            if (p && p.toLowerCase() != 'null') {
                addr += p;
            }

            if (c && c.toLowerCase() != 'null') {
                addr += c;
            }

            if (a && a.toLowerCase() != 'null') {
                addr += a;
            }

            if (t && t.toLowerCase() != 'null') {
                addr += t;
            }

            if (d && d.toLowerCase() != 'null') {
                addr += d;
            }

            return addr.replace(/ /g, '');
        }

        //取出相应的网点信息
        var tempList = [];
        common.Foreach(branchList, function (i, o) {
            if (o.BranchCode == templateBranch.BranchCode
                && (o.BranchHashCode == templateBranch.BranchHashCode
                    || templateBranch.BranchAddress.replace(/ /g, '') == getAddress(o.Province, o.City, o.Area, o.Town, o.Detail)
                    || templateBranch.BranchAddress.replace(/ /g, '') == getAddress(o.Province, o.City, o.Area, o.Detail))
            ) {
                tempList.push(o);
            }
        });
        var branch = null;
        if (tempList.length == 1)
            branch = tempList[0];
        else if (tempList.length > 1) {
            var templateServiceList = templateBranch.LogisticsServiceList;
            branch = common.FindBranchByTemplateServiceCode(tempList, templateServiceList);
        }
        return branch;
    }

    //根据模板服务查找网点
    common.FindBranchByTemplateServiceCode = function (branchList, templateServiceList) {
        var tempBranch = [];
        common.Foreach(branchList, function (i, o) {
            var isServiceNotFinded = false; //是否有服务未找到
            common.Foreach(templateServiceList, function (ii, s) {
                var serviceFinded = false; //服务是否找到
                common.Foreach(o.ServiceInfoCols, function (j, os) {
                    if (s.ServiceCode == os.ServiceCode) {
                        serviceFinded = true; //已找到
                        return 'break';
                    }
                });
                //有服务未找到，说明当前遍历的网点不符合
                if (serviceFinded == false) {
                    isServiceNotFinded = true;
                    return 'break';
                }
            });
            //服务都找到了，说明这个网点就是
            if (isServiceNotFinded == false) {
                tempBranch.push(o);
            }
        });
        if (tempBranch.length == 0)
            return null;
        if (tempBranch.length == 1)
            return tempBranch[0];

        //按网点增值服务个数排序，取增值服务个数最少的网点
        tempBranch = common.SortExt(tempBranch, "ServiceInfoCols.length");
        return tempBranch[0];
    }

    //转义正则表达式特殊字符
    common.TranRegCharts = function (str) {
        //var charts = ['{', '}', '[', ']', '(', ')', '\\', '^', '$', '*', '+', '.', '|', '?'];
        var charts = "*.?+$^[](){}|\\";
        var result = '';
        for (var i = 0; i < str.length; i++) {
            if (charts.indexOf(str[i]) > -1) {
                result += '\\' + str[i];
            }
            else {
                result += str[i];
            }
        }
        return result;
    }
    common.GelVal = function (val) {
        if (val == null || val == undefined)
            return '';
        return val;
    }
    common.Clone = function (instance) {
        if (Array.isArray(instance) == true) {
            return _arrayClone(instance);
        }
        else if (typeof instance == "object") {
            return _objectClone(instance);
        }
        else if (typeof instance == 'function') {
            return _functionClone(instance);
        } else {
            return instance;
        }
    }

    //阻止冒泡和默认行为
    common.StopPropagation = function (event) {
        if (event) {
            event.stopPropagation();
            event.preventDefault();
        } else {
            window.event.returnValue = false;
            window.event.cancelBubble = true;
        };
    }

    //复制控件文本
    common.CopyText = function (selector) {
        //event.stopPropagation();
        common.stopM(event);
        var newId = "txt" + (new Date()).getTime();
        $('body').append("<textarea id=\"" + newId + "\" style=\"width:1px; height:1px;\">" + ($(selector).val() || $(selector).text()) + "</textarea>");
        var txt = document.getElementById(newId);
        txt.select();
        document.execCommand("Copy");
        $("#" + newId).remove()
        layer.msg("已复制，可粘贴");
    }

    //复制元素内容  使用：<div id="textDiv1" onclick="commonModule.execClick(event);" oncopy="commonModule.execCopy(event,'textDiv1');">这里是要复制的文字</div>
    common.execClick = function (event) {
        event.stopPropagation();
        document.execCommand("copy");
    }
    common.execCopy = function (event, textDiv, id) {

        var textDivVal = textDiv.toString();

        if (textDiv == "allAdress") {
            textDivVal = $("#txtReceiver_" + id + "").val() + "," + $("#txtReceiver_phone_" + id + "").val() + "," + $("#txtReceiver_address_" + id + "").val();
        } else if (textDiv == "partAdress") {
            textDivVal = $("#txtReceiver_address_" + id + "").val();
        }

        if (isIE()) {
            if (window.clipboardData) {
                window.clipboardData.setData("Text", textDivVal);
                window.clipboardData.getData("Text")
                layer.msg("已复制，可粘贴");
            }
        } else {
            event.preventDefault();
            if (event.clipboardData) {
                event.clipboardData.setData("text/plain", textDivVal);
                event.clipboardData.getData("text")
                layer.msg("已复制，可粘贴");
            }
        }
    }

    common.OpenNewTab = function (url) {
        var a = document.createElement("a");
        a.setAttribute("href", url);
        a.setAttribute("target", "_blank");
        a.setAttribute("id", "camnpr" + (new Date()).getTime());
        document.body.appendChild(a);
        a.click();
        a.remove();
    }

    common.DownloadHintInstall = function () {
        layer.alert("下载打印组件后，需安装才能正常使用哦");
        //layer.msg('下载打印组件后，需安装才能正常使用哦');
    }

    function isIE() {  //判断是否ie
        var input = window.document.createElement("input");
        if (window.ActiveXObject === undefined) return null;
        if (!window.XMLHttpRequest) return 6;
        if (!window.document.querySelector) return 7;
        if (!window.document.addEventListener) return 8;
        if (!window.atob) return 9;
        if (!input.dataset) return 10;
        return 11;
    }

    //检测字符串中是否存在生僻字
    common.HasRareWords = function (str) {
        try {
            if (!str) return true;
            var tempReg = new RegExp(/\u200e|\u3002|\uFF1F|\uFF01|\uFF0C|\u3001|\uFF1B|\uFF1A|\u300C|\u300D|\u300E|\u300F|\u2018|\u2019|\u201C|\u201D|\uFF08|\uFF09|\u3014|\u3015|\u3010|\u3011|\u2014|\u2026|\u2013|\uFF0E|\u300A|\u300B|\u3008|\u3009/g);

            var rareWrodsIndex = [];
            for (var i = 0; i < str.length; i++) {
                if (str.charCodeAt(i) < 255 || str.charCodeAt(i) == 8236 || str.charCodeAt(i) == 8237)
                    continue;
                var c = str[i];

                if (c.match(tempReg) != null) continue;

                var reg = new RegExp(/[\u4e00-\u9fff]/g);
                if (c.match(reg) == null) {
                    //result = true;
                    rareWrodsIndex.push(i);
                    //break;
                }
            }
            return rareWrodsIndex;
        } catch (e) {
            console.log(e);
        }
        return str;
    }

    //删除生僻字
    common.DeleteRareWords = function (str) {
        var rareWords = common.HasRareWords(str);
        var newStr = '';
        for (var i = 0; i < str.length; i++) {
            var has = false;
            for (var j = 0; j < rareWords.length; j++) {
                if (i == rareWords[j]) {
                    has = true;
                    break;
                }
            }
            if (has == false)
                newStr += str[i];
        }
        return newStr;
    }

    /*1.用浏览器内部转换器实现html转码*/
    common.htmlEncode = function (html) {
        //1.首先动态创建一个容器标签元素，如DIV
        var temp = document.createElement("div");
        //2.然后将要转换的字符串设置为这个元素的innerText(ie支持)或者textContent(火狐，google支持)
        (temp.textContent != undefined) ? (temp.textContent = html) : (temp.innerText = html);
        //3.最后返回这个元素的innerHTML，即得到经过HTML编码转换的字符串了
        var output = temp.innerHTML;
        temp = null;
        return output;
    }

    /*2.用浏览器内部转换器实现html解码*/
    common.htmlDecode = function (text) {
        //1.首先动态创建一个容器标签元素，如DIV
        var temp = document.createElement("div");
        //2.然后将要转换的字符串设置为这个元素的innerHTML(ie，火狐，google都支持)
        temp.innerHTML = text;
        //3.最后返回这个元素的innerText(ie支持)或者textContent(火狐，google支持)，即得到经过HTML解码的字符串了。
        var output = temp.innerText || temp.textContent;
        temp = null;
        return output;
    }

    //转意符换成普通字符
    common.escape2Html = function (str) {
        var arrEntities = { 'lt': '<', 'gt': '>', 'nbsp': ' ', 'amp': '&', 'quot': '"' };
        return str.replace(/&(lt|gt|nbsp|amp|quot);/ig, function (all, t) { return arrEntities[t]; });
    }

    //普通字符转换成转意符
    common.html2Escape = function (sHtml) {
        return sHtml.replace(/[<>&"]/g, function (c) { return { '<': '&lt;', '>': '&gt;', '&': '&amp;', '"': '&quot;' }[c]; });
    }

    //删除html标签
    common.DelHtmlTag = function (data) {
        return data.replace(/<[^>]*>|<\/[^>]*>/gm, "");
    }

    //删除from提交危险字符
    common.DelDangerChar = function (data) {
        if (!data) return '';
        return data.replace(/<|>/gm, "").replace(/&#/gm, '');
    }

    //订单id格式化
    common.OrderIdFormatter = function (orderIdJoin) {

        if (orderIdJoin == undefined || orderIdJoin == null) {
            return '';
        }
        var ids = orderIdJoin.trimEndDgj(',').split(',');
        //if (ids.length > 1) {
        //    return "<lable title='" + orderIdJoin.trimEndDgj(',') + "'>" + ids[0] + "...</label>";
        //}
        //return orderIdJoin;
        return "<lable title='" + orderIdJoin.trimEndDgj(',') + "'>" + ids.join('<br/>') + "</label>";
    }

    //MM to Px
    common.MmToPx = function (w_mm, h_mm) {

        var num = 25.4;
        var dpi = 96;

        var w_px = Math.floor((w_mm / num) * dpi);
        var h_px = Math.floor((h_mm / num) * dpi);

        return { width: w_px, height: h_px };
    }

    // 时间控件初始化
    // {days:30,startDate:"2019-01-01 00:00:00",endDate:"2019-01-01 00:00:00"}
    common.InitDateRangePicker = function ($container, options) {
        Date.prototype.format = function (fmt) {
            var o = {
                "M+": this.getMonth() + 1,                 //月份
                "d+": this.getDate(),                    //日
                "h+": this.getHours(),                   //小时
                "m+": this.getMinutes(),                 //分
                "s+": this.getSeconds(),                 //秒
                "q+": Math.floor((this.getMonth() + 3) / 3), //季度
                "S": this.getMilliseconds()             //毫秒
            };
            if (/(y+)/.test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            }
            for (var k in o) {
                if (new RegExp("(" + k + ")").test(fmt)) {
                    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                }
            }
            return fmt;
        }

        function getPreMonth(date) {
            var arr = date.split('-');
            var year = arr[0]; //获取当前日期的年份
            var month = arr[1]; //获取当前日期的月份
            var day = arr[2]; //获取当前日期的日
            var days = new Date(year, month, 0);
            days = days.getDate(); //获取当前日期中月的天数
            var year2 = year;
            var month2 = parseInt(month) - 1;
            if (month2 == 0) {
                year2 = parseInt(year2) - 1;
                month2 = 12;
            }
            var day2 = day;
            var days2 = new Date(year2, month2, 0);
            days2 = days2.getDate();
            if (day2 > days2) {
                day2 = days2;
            }
            if (month2 < 10) {
                month2 = '0' + month2;
            }
            var t2 = year2 + '-' + month2 + '-' + day2;
            return t2;
        }

        var endTimes = new Date().format("yyyy-MM-dd") + " 23:59:59";  //当天时间（结束时间）
        var startTimes = getPreMonth(endTimes.substr(0, 10)) + " 00:00:00";
        if (options != undefined) {
            if (options.startDate == undefined || options.startDate == "" || options.endDate == undefined || options.endDate == "") {
                var days = parseInt(options.days) || 0;
                if (days > 0)
                    startTimes = moment().subtract(days, 'days').format('YYYY-MM-DD HH:mm:ss').substr(0, 10) + " 00:00:00";//上个月时间（开始时间，周期30天）
            }
            else {
                startTimes = options.startDate;
                endTimes = options.endDate;
            }
        }

        $container.daterangepicker({
            timePicker: true, //显示时间
            timePicker24Hour: true, //时间制
            timePickerSeconds: true, //时间显示到秒
            autoApply: false,
            linkedCalendars: false,
            startDate: startTimes,
            endDate: endTimes,
            // maxDate: moment(new Date()), //设置最大日期
            opens: "right",
            ranges: {
                // '今天': [moment(), moment()],
                // '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                '上周': [moment().subtract(6, 'days'), moment()],
                '前30天': [moment().subtract(29, 'days'), moment()],
                '本月': [moment().startOf('month'), moment().endOf('month')],
                '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            showWeekNumbers: true,
            locale: {
                format: "YYYY-MM-DD HH:mm:ss", //设置显示格式
                separator: "~",
                applyLabel: '确定', //确定按钮文本
                cancelLabel: '取消', //取消按钮文本
                customRangeLabel: '自定义',
                daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                monthNames: ['一月', '二月', '三月', '四月', '五月', '六月',
                    '七月', '八月', '九月', '十月', '十一月', '十二月'
                ],
                firstDay: 1
            },
        }, function (start, end, label) {
            $container.attr("start-date", start.format('YYYY-MM-DD HH:mm:ss'));
            $container.attr("end-date", end.format('YYYY-MM-DD HH:mm:ss'));
        });
        $container.on("click", function () {
            $(".daterangepicker").addClass("daterangepicker dropdown-menu show-calendar openscenter");
        });

        // 设置默认时间
        var dateArr = $container.val().split("~");
        var startTime = dateArr[0] || "";
        var endTime = dateArr[1] || "";
        $container.attr("start-date", startTime);
        $container.attr("end-date", endTime);
    }

    // 时间控件初始化
    // {days:30,startDate:"2019-01-01 00:00:00",endDate:"2019-01-01 00:00:00"}
    common.InitCalenderTime = function ($container, options) {

        var setStartTime = function () {
            options.endDate = commonModule.ServerNowDate.replace(' ', 'T');
            options.days = common.IsNumber(options.days) ? parseInt(options.days) : 0;
            if (options.endDate && options.days > 0) {
                var endTime = new Date(options.endDate);
                var diff = endTime.getTime() - options.days * (1000 * 60 * 60 * 24);
                var startTime = new Date(endTime.setTime(diff));
                options.startDate = startTime.Format("yyyy-MM-dd");
            }
        }
        options = options || {};

        // 没有指定起止时间时，使用服务器当前时间
        if (!options.endDate && !options.startDate && commonModule.ServerNowDate) {
            setStartTime();
        }

        if (options.days !== 0)
            options.days = options.days || "";
        options.startDate = options.startDate || "";
        options.endDate = options.endDate || "";

        // shopId大于0表示店铺授权登录的情况，否则不读取配置，使用默认时间配置信息
        var shopId = 0;
        if (commonModule.CurrShop)
            shopId = commonModule.CurrShop.Id || 0;

        if (shopId > 0) {
            var key = window.location.pathname;
            key = key == "/" ? "/Order/Index" : key;
            var defaultQueryDateVal;
            var defaultQueryDateVals = [];
            commonModule.LoadCommonSetting("DefaultQueryDateVal", false, function (res) {
                if (res.Success && res.Data) {
                    defaultQueryDateVals = JSON.parse(res.Data);
                    common.DefaultQueryDateVal = defaultQueryDateVals;
                    if (defaultQueryDateVals && defaultQueryDateVals.length > 0) {
                        for (var i = 0; i < defaultQueryDateVals.length; i++) {
                            if (defaultQueryDateVals[i].Key == key)
                                defaultQueryDateVal = defaultQueryDateVals[i];
                        }
                    }
                }
            });

            if (defaultQueryDateVal) {
                options.days = common.IsNumber(defaultQueryDateVal.DiffDays) ? parseInt(defaultQueryDateVal.DiffDays) : 0;
                if (options.endDate && options.days > 0) {
                    setStartTime();
                }
            }
        }

        calenderTimeModule.InitCalenderTime($container, options);
    }

    //判断ajax返回是否有错误
    common.IsError = function (rsp) {
        if (rsp.Success == false) {
            layer.msg(rsp.Message, { icon: 2 });
            return true;
        }
        return false;
    }

    common.LoadingMsg = function (loadingMsg, maxWidth) {
        var msg = '<div style="text-align:center;">' + loadingMsg + "</div>";
        if (!maxWidth || maxWidth <= 120)
            maxWidth = 120;
        return layer.msg('<div class="layui-layer-loading"><div id="" class="layui-layer-loading1" style="display:inline-block;"></div>' + msg + '</div>', { shade: 0.01, time: 999999, maxWidth: maxWidth });
    }
    common.isExpired = false;
    common.isExpiredShowed = false;
    //公共的Ajax方法
    common.Ajax = function (options) {
        var loding = null;

        var successHandler = null;
        if (typeof options.success == "function") {
            successHandler = options.success;
        }
        if (options.data && !options.data.IsCustomerOrder && typeof options.data != "string") {
            options.data.IsCustomerOrder = common.isCustomerOrder();
        }
        else {
            options.data = options.data || {};
            options.data.IsCustomerOrder = common.isCustomerOrder();
        }
        if (common.checkIsExpired())
            return;
        //过滤尖括号等危险字符
        if (options.data.isFilterDangerChar == undefined || options.data.isFilterDangerChar == true) {
            var safedata = common.DelDangerChar(JSON.stringify(options.data));//.replace(new RegExp(/<[^>]*>|<\/[^>]*>/gm, 'g'), '');
            options.data = JSON.parse(safedata);
        }

        var complete = options.complete;
        //if (options.complete)
        //    options = null;
        options.url = common.rewriteUrl(options.url);
        options.url = common.addTraceIdToAjaxUrl(options.url);
        var defaultOpts = {
            async: true,
            cache: false,
            type: 'POST',
            contentType: 'application/x-www-form-urlencoded',
            datatype: 'json',
            beforeSend: function (XHR) {
                if (common.checkIsExpired())
                    return;
                if (common.checkIsMigrating())
                    return;
                if (common.VersionTip && common.VersionTip != '' && common.VersionTip.TargetVersion != null) {
                    common.showUpgradeWindow(common.VersionTip, false);
                    return;
                }
                var msg = "";
                if (!options.loading && !options.loadingMessage)
                    return;
                if (options.loadingMessage && options.loadingMessage.trim() !== '')
                    loding = common.LoadingMsg(options.loadingMessage);
                else
                    loding = layer.load(2); //, { shade: [0.2, '#fff'] }
            },
            complete: function (XHR, TS) {
                if (loding != null) {
                    layer.close(loding);
                    loding = null;
                }

                var isLogout = false;
                var json = XHR.responseJSON;
                var text = XHR.responseText;
                if (json) {
                    if (json.ErrorCode == 401)
                        isLogout = true;
                    else if (json.ErrorCode == "ShopExpired")
                        common.isExpired = true;
                    else if (json.ErrorCode == "ShopMigrating")
                        common.isMigrating = true;
                }
                else if (text) {
                    try {
                        json = JSON.parse(text);
                        if (json && json.ErrorCode && json.ErrorCode == 401)
                            isLogout = true;
                    } catch (e) {

                    }
                }
                if (common.checkIsExpired())
                    return;
                if (common.checkIsMigrating())
                    return;
                if (complete && !isLogout) {
                    complete(XHR, TS);
                }
            },
            error: function (XHR, errorMsg, error) {
                layer.msg(errorMsg, { icon: 2 });
            },
            success: function (data) {

            }
        };
        common.isMigrating = false;
        var opts = $.extend({}, defaultOpts, options);
        opts.success = function (rsp) {
            if (loding != null) {
                layer.close(loding);
                loding = null;
            }
            if (rsp && (rsp.ErrorCode == "401" || rsp.ErrorCode == "ShopExpired" || rsp.ErrorCode == "auth_expires" || rsp.ErrorCode == "NEED_UPGRADE_VERSION")) {
                if (rsp.ErrorCode == "401") {
                    layer.open({
                        title: "提示",
                        content: "<h1>登陆过期，请重新登陆</h1>",
                        btn: ['重新登陆', '取消'],
                        yes: function () {
                            window.location.href = common.CurrentPlatformAuthEntry ? common.CurrentPlatformAuthEntry : "/";
                        },
                        btn2: function () {
                            layer.closeAll();
                        },
                    });
                }
                else if (rsp.ErrorCode == "ShopExpired") {
                    common.isExpired = true;
                    common.checkIsExpired();
                }
                else if (rsp.ErrorCode == "auth_expires") {
                    //layer.msg(json.Message);
                    IfShopIsAuthExpiredThenAlert(100);
                    successHandler(rsp);
                }
                else if (rsp.ErrorCode == "NEED_UPGRADE_VERSION") {
                    //检查版本控制
                    common.showUpgradeWindow(rsp.Data, true);
                    layer.closeAll();
                    return;
                    //successHandler(rsp);
                }
            }
            if (rsp && (rsp.ErrorCode == "ShopMigrating")) {
                common.isMigrating = true;
                common.checkIsMigrating();
            }
            else if (rsp.ErrorCode == "Tool_0000") {
                layer.alert(rsp.Message, function () {
                    window.location.href = "/Tools/Login";
                });
            }
            else
                successHandler(rsp);
        }

        return $.ajax(opts);
    };
    common.checkIsExpired = function () {
        if (common.isExpired) {
            //layer.closeAll();
            var tips = $("#expired-h-tips");
            if (tips.length > 0)
                return true;
            //var link = common.getPayLink();
            layer.open({
                title: "服务到期",
                offset: "100px",
                area: ["500px", "200px"],
                shadeClose: false,
                icon: 5,
                content: "<h1 id='expired-h-tips' style='margin-top:-10px;'>非常抱歉，您所使用的店管家批量打印发货服务已到期。</br>为了不影响您的使用，请尽快续费</h1>",
                btn: ['现在续费'],
                yes: function () {
                    var payLink = $("#header-pay-link");
                    var link = payLink.get(0);
                    if (link && link.href && link.href.indexOf("javascript") == -1) {
                        window.open(link.href);
                    }
                    else if (payLink.length > 0) {
                        $("#header-pay-link")[0].click();
                        $("#header-pay-link").click();
                    } else {
                        layer.alert("请前往平台后台进行续费");
                    }
                },
                cancel: function () {
                    return false;
                },
            });
            return true;
        }
    }
    common.checkIsMigrating = function () {
        var tip = $("#migrating-tip");
        if (tip && tip.length > 0)
            return;
        if (common.isMigrating) {
            layer.closeAll();
            layer.open({
                title: "提示",
                offset: "100px",
                area: ["500px", "180px"],
                shadeClose: false,
                icon: 1,
                content: "<h1 id='migrating-tip' style='margin-top:-8px;'>为了提升您的订单发货效率，我们正在将您的店铺数据迁往新的服务器，预计会持续10分钟，在此期间不能打单发货操作，请10分钟之后再刷新页面。</h1>",
                btn: [],
                cancel: function () {
                    //layer.msg("数据正在迁移中，请10分钟后再操作...");
                    return false;
                }
            });
            try {
                $(".progress-wrap").hide();
            } catch (e) {

            }
        }
        return common.isMigrating;
    }
    common.ajax = common.Ajax;
    common.post = function (url, data, callback) {
        return common.ajax({
            url: url,
            data: data,
            success: callback
        });
    }
    common.getJSON = function (url, callback) {
        return common.ajax({ url: url, success: callback });
    };
    common.get = common.getJSON;

    common.getPayLink = function () {
        if (common.PlatformType == "Alibaba") {
            return "http://pc.1688.com/product/detail.htm?productCode=Tz%2BIZt9qCGKsMpNFCCCY9%2BmqRnw6h1ZBD3N%2Fli2CCyg%3D&productType=GROUP&tracelog=app_map_dgj";
        } else if (common.PlatformType == "Taobao") {
            return "https://fuwu.taobao.com/ser/detail.htm?spm=a1z13.8114210.1234-fwlb.4.b7XqWn&service_code=FW_GOODS-1000059019&tracelog=search&from_key=%E5%BA%97%E7%AE%A1%E5%AE%B6";
        } else if (common.PlatformType == "YouZan") {
            return "https://yingyong.youzan.com/cloud-app-detail/43116";
        } else if (common.PlatformType == "JingDong") {
            return "http://fw.jd.com/975802.html?itemCode=FW_GOODS-975802-1";
        } else if (common.PlatformType == "Suning") {
            return "http://fuwu.suning.com/detail/10003725.html";
        } else if (common.PlatformType == "WeiMeng") {
            return "https://fuwu.weimob.com/product_906.html";
        } else if (common.PlatformType == "XiaoDian") {
            return "https://wechat.xiaodian.com/user/oauth/authorize.html?response_type=code&app_key=100735&redirect_uri=http://auth.dgjapp.com/auth/xiaodian/&state=YOUR_CUSTOM_CODE";
        } else if (common.PlatformType == "WeiDian") {
            return "https://h5.weidian.com/m/signin/index.html?oauth=0&redirect=https%3A%2F%2Foauth.open.weidian.com%2Foauth2%2Fauthorize%3Fappkey%3D690712%26isH5%3Dfalse%26redirect_uri%3Dhttp%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fweidian%2F%26response_type%3Dcode%26sign%3Db78f05b0c18158477bf09324cf14e170%26state%3DYOUR_CUSTOM_CODE";
        } else if (common.PlatformType == "ZhiDian" || common.PlatformType == "DouYinXiaoDian" || common.PlatformType == "TouTiaoXiaoDian" || common.PlatformType == "LuBan") {
            return "http://auth.dgjapp.com/auth/zhidian";
        } else if (common.PlatformType == "MoGuJie") {
            return "https://oauth.mogujie.com/authorize?response_type=code&app_key=101444&redirect_uri=http://auth.dgjapp.com/auth/mogujie/&state=YOUR_CUSTOM_CODE";
        } else {
            return window.location.href;
        }
    }

    common.GetRequestBatch = function () {
        if (commonModule.ServerNowStr) {
            var batch = commonModule.ServerNowStr + (commonModule.DateBatch || '');
            return batch;
        }
        else {
            var padLeft = function (s, l) {
                var str = "";
                var sl = s.toString().length;
                if (sl < l) {
                    var n = l - sl;
                    for (var i = 0; i < n; i++) {
                        str += "0";
                    }
                }
                return str + s;
            }
            var date = new Date();
            var y = date.getFullYear();
            var m = date.getMonth() + 1;
            var d = date.getDate();
            var h = date.getHours();
            var mm = date.getMinutes();
            var dateStr = y + '' + padLeft(m, 2) + '' + padLeft(d, 2) + '' + padLeft(h, 2) + '' + padLeft(mm, 2);
            return dateStr + commonModule.CurrShop.Id + commonModule.Random(1000, 9999);
        }
    }

    //批量异步发送请求
    //url:请求链接
    //datas:需要分批的请求参数
    //requestParaBuilderFunc:请求参数组装函数
    //callbackFunc:每个ajax请求回调 可用作百分百监控，百分比需乘以batchSize
    //allDoneCallbackFunc:所有请求完成后的回调，参数为数组
    //batchSize:分批后单次请求的数量，默认10
    //loadingText： loadingText有值，则不显示进度条，否则显示进度条
    common.posts = function (url, datas, requestParaBuilderFunc, callbackFunc, allDoneCallbackFunc, batchSize, loadingText) {
        var reqs = [];
        var rsps = [];
        if (!batchSize || batchSize <= 0)
            batchSize = 10;
        if (!datas)
            return;

        var datasClone = JSON.parse(JSON.stringify(datas));

        var pageCount = Math.ceil(datas.length / batchSize);
        var loading = null;
        if (loadingText) {
            loding = common.LoadingMsg(loadingText);
        } else {
            loading = layer.msg('<div style="width:300px;"><div class="layui-progress layui-progress-big">\
                <div id="div_pro_bar" class="layui-progress-bar layui-bg-red" style="width: 0%;"><span class="layui-progress-text" id="div_bar_text"></span></div>\
            </div>\
            <p style="font-size:18px;font-weight:bold;"><span id="sp_curr_number">0</span>/<span>'+ datas.length + '<span></p>\
        </div>', { shade: 0.01, time: 999999 })
        }
        var batchFlag = common.GetRequestBatch();//(new Date()).getTime() + common.Random(1000, 10000); //批次标识
        for (var i = 0; i < pageCount; i++) {
            var perRequestDatas = datas.splice(0, batchSize);
            var postData = requestParaBuilderFunc(perRequestDatas);
            postData.RequestBatchNumber = (i + 1) + "/" + pageCount + "/" + batchFlag; //请求批次 i/pageCount;
            var req = common.post(url, postData, callbackFunc);
            reqs.push(req);
        }
        var evalArgs = "";
        for (var i = 0; i < reqs.length; i++) {
            evalArgs += ",reqs[" + i + "]";
        }
        evalArgs = evalArgs.trimStartDgj(',');
        var finish = eval('$.when(' + evalArgs + ')');
        finish.then(function () {
            var args = [];
            layer.close(loading);
            if (pageCount == 1) {
                args.push(arguments[0]);
            }
            else {
                for (var i = 0; i < arguments.length; i++) {
                    args.push(arguments[i][0]);
                }
            }
            allDoneCallbackFunc(args, datasClone);
        });
    }

    common.Random = function (min, max) {
        return Math.floor(Math.random() * (max - min)) + min;
    }

    common.Ajaxs = function (defers, callback) {

        if (!defers || defers.length == 0) {
            layer.alter("没有ajax请求.");
            return;
        }

        var count = 0;
        var evalArgs = "";
        for (var i = 0; i < defers.length; i++) {
            if (defers[i]) {
                evalArgs += ",defers[" + i + "]";
                count++;
            }
        }
        evalArgs = evalArgs.trimStartDgj(',');
        var finish = eval('$.when(' + evalArgs + ')');
        finish.then(function () {
            //获取所有请求返回
            var result = true;
            if (count > 1) {
                for (var i in arguments) {
                    if (arguments[i] == undefined)
                        continue;
                    var r = arguments[i][0];
                    if (r.Success == false)
                        result = false;
                }
            }
            else {
                var r = arguments[0];
                if (r && r.Success == false)
                    result = false;
            }
            callback(result);
        });
    }

    common.AjaxFileUpload = function (options) {
        var loding = null;

        var successHandler = null;
        if (typeof options.success == "function") {
            successHandler = options.success;
        }
        options.url = common.rewriteUrl(options.url);
        options.url = common.addTraceIdToAjaxUrl(options.url);
        if (options.data && !options.data.IsCustomerOrder)
            options.data.IsCustomerOrder = common.isCustomerOrder();
        var defaultOpts = {
            async: true,
            cache: false,
            type: 'POST',
            contentType: 'application/x-www-form-urlencoded',
            dataType: 'json',
            beforeSend: function (XHR) {
                var msg = "";
                if (!options.loading && !options.loadingMessage)
                    return;
                if (options.loadingMessage && options.loadingMessage.trim() !== '') {
                    msg = '<div style="text-align:center;">' + options.loadingMessage + "</div>";
                    loding = layer.msg('<div class="layui-layer-loading"><div id="" class="layui-layer-loading1" style="display:inline-block;"></div>' + msg + '</div>', { time: 999999 });
                }
                else
                    loding = layer.load(2);
            },
            complete: function (XHR, TS) {
                layer.close(loding);
            },
            error: function (XHR, errorMsg, error) {
                layer.msg(errorMsg, { icon: 2 });
            },
            success: function (data) { }
        };

        var opts = $.extend({}, defaultOpts, options);
        opts.success = function (rsp) {
            layer.close(loding);
            var temp = $.parseJSON(rsp.replace(/<.*?>/ig, ""));
            if (temp && temp.ErrorCode == "NEED_UPGRADE_VERSION") {
                //检查版本控制
                common.showUpgradeWindow(temp.Data, true);
            }
            successHandler(rsp);
        }
        $.ajaxFileUpload(opts);
    };

    //上传文件大小判断
    common.CheckUploadFileSize = function ($id, maxSize) {
        var maxsize = maxSize * 1024 * 1024;
        var errMsg = "上传的附件文件不能超过" + maxSize + "M！";
        var tipMsg = "您的浏览器暂不支持计算上传文件的大小，确保上传文件不要超过" + maxSize + "M，建议使用FireFox、Chrome浏览器。";
        var browserCfg = {};
        var ua = window.navigator.userAgent;
        if (ua.indexOf("MSIE") >= 1) {
            browserCfg.ie = true;
        } else if (ua.indexOf("Firefox") >= 1) {
            browserCfg.firefox = true;
        } else if (ua.indexOf("Chrome") >= 1) {
            browserCfg.chrome = true;
        }
        try {
            var obj_file = document.getElementById($id);
            if (obj_file.value == "") {
                layer.confirm("请先选择上传文件");
                return false;
            }
            var filesize = 0;
            if (browserCfg.firefox || browserCfg.chrome) {
                filesize = obj_file.files[0].size;
            } else if (browserCfg.ie) {
                layer.confirm(tipMsg);
                return false;
            } else {
                layer.confirm(tipMsg);
                return false;
            }
            if (filesize == -1) {
                layer.confirm(tipMsg);
                return;
            } else if (filesize > maxsize) {
                layer.confirm(errMsg);
                return false;
            } else {
                return true;
            }
        } catch (e) {
            console.log(e);
        }
        return false;
    }

    //循环集合
    common.Foreach = function (list, callback) {
        if (Array.isArray(list) == false) {
            var i = 0;
            for (var p in list) {
                var break_continue = callback(i, list[p]);
                if (break_continue != undefined && (break_continue.toLowerCase() == 'break' || break_continue.toLowerCase() == 'break;')) {
                    break;
                }
                i++;
            }
        } else {
            for (var i = 0; i < list.length; i++) {
                var break_continue = callback(i, list[i]);
                if (break_continue != undefined && (break_continue.toLowerCase() == 'break' || break_continue.toLowerCase() == 'break;')) {
                    break;
                }
                if (break_continue != undefined && (break_continue.toLowerCase() == 'continue' || break_continue.toLowerCase() == 'continue;')) {
                    continue;
                }
            }
        }
    }

    //数组排序
    common.Sort = function (arr, key, desc) {
        var sortFun = function (a, b) {
            return !desc ? a[key] - b[key] : b[key] - a[key];
        };
        if (arr == null || arr.length < 2)
            return arr;
        return arr.sort(sortFun);
    }

    //数组排序 多个属性排序
    common.SortExt02 = function (arr, keys, desc, ignoreNull) {
        var sortFun = function (a, b) {
            var result = 0;
            for (var i = 0; i < keys.length; i++) {
                var k = keys[i];
                var x, y;
                if (ignoreNull) {
                    x = a == null ? 0 : (a[k] || 0);
                    y = b == null ? 0 : (b[k] || 0);
                }
                else {
                    x = a[k];
                    y = b[k];
                }
                if (x == y) {
                    continue;
                }
                else {

                    if (!desc) {
                        if (isNaN(x) || isNaN(y))
                            result = x.toString().localeCompare(y);
                        else
                            result = x - y;
                    }
                    else {
                        if (isNaN(x) || isNaN(y))
                            result = y.toString().localeCompare(x);
                        else
                            result = y - x;
                    }

                    break;
                }
            }
            return result;
        };

        if (arr == null || arr.length < 2)
            return arr;
        return arr.sort(sortFun);
    }

    //数组对象多级属性排序,key 示例 "Data.Attr"
    common.SortExt = function (arr, key, desc, ignoreNull) {
        var attrs = key.split('.');
        var sortFun = function (a, b) {
            for (var i = 0; i < attrs.length; i++) {
                if (ignoreNull) {
                    a = a == null ? 0 : (a[attrs[i]] || 0);
                    b = b == null ? 0 : (b[attrs[i]] || 0);
                }
                else {
                    a = a == null ? 0 : (a[attrs[i]] || 0);
                    b = b == null ? 0 : (b[attrs[i]] || 0);
                }
            }
            var result = 0;
            if (!desc) {
                if (isNaN(a) || isNaN(b))
                    result = a.toString().localeCompare(b);
                else
                    result = a - b;
            }
            else {
                if (isNaN(a) || isNaN(b))
                    result = b.toString().localeCompare(a);
                else
                    result = b - a;
            }
            return result;
        };
        if (arr == null || arr.length < 2)
            return arr;
        return arr.sort(sortFun);
    }

    //加载系统快递公司
    common.LoadExpressCompany = function (callback) {
        common.Ajax({
            url: '/Common/LoadExpressCompany',
            success: function (response) {
                callback(response);
            }
        });
    }

    //加载系统打印机绑定数据
    common.LoadPrinterBind = function () {
        common.Ajax({
            url: '/Common/LoadPrinterBind',
            success: function (response) {
                if (common.IsError(response)) {
                    return;
                }
                _printerBindList = response.Data;
            }
        });
    };
    //同步加载系统打印机绑定数据
    common.AsyncLoadPrinterBind = function () {
        common.Ajax({
            url: '/Common/LoadPrinterBind',
            async: false,
            success: function (response) {
                if (common.IsError(response)) {
                    return;
                }
                _printerBindList = response.Data;
            }
        });
    };

    //加载系统打印机绑定数据
    common.SetPrinterBind = function (templateId, templateType, printerName, isNotMsg) {
        if (!templateId)
            return;
        if (!printerName)
            return;
        var isExists = false;
        if (_printerBindList != null && _printerBindList != undefined && _printerBindList.length > 0) {
            common.Foreach(_printerBindList, function (i, bm) {
                if (bm.TemplateId == templateId && bm.TemplateType == templateType && bm.PrinterName == printerName && bm.ShopId == common.CurrShop.Id) {
                    isExists = true;
                    return 'break;';
                }
            });
        }
        if (isExists == true && isNotMsg != true) {
            layer.msg("打印机绑定成功");
            return;
        }
        common.Ajax({
            url: '/Common/BindPrinter',
            data: { model: { TemplateId: templateId, TemplateType: templateType, PrinterName: printerName } },
            success: function (response) {
                if (common.IsError(response)) {
                    return;
                } else if (isNotMsg != true)
                    layer.msg("打印机绑定成功");
                //删除之前绑定数据
                common.Foreach(_printerBindList, function (ii, pp) {
                    if (pp.TemplateId == templateId && pp.TemplateType == templateType && pp.ShopId == common.CurrShop.Id) {
                        _printerBindList.splice(ii, 1);
                    }
                });
                //添加绑定数据
                _printerBindList.push(response.Data);
            }
        });
    };


    //加载默认打印机 templateType 1快递单 2发货单 3拿货单
    common.GetDefaultPrinter = function (templateId, templateType) {
        var defaultPrinter = null;
        common.Foreach(_printerBindList, function (i, o) {
            if (o.TemplateId == templateId && o.TemplateType == templateType && o.ShopId == common.CurrShop.Id) {
                defaultPrinter = o;
                return "break";
            }
        });
        return defaultPrinter;
    }

    //根据Sid获取平台ShopId
    common.GetShopIdBySid = function (sid) {
        var shopId = '';
        common.Foreach(common.Shops, function (i, o) {
            if (o.Id == sid) {
                shopId = o.ShopId;
                return "break";
            }
        });
        return shopId;
    }


    //加载地区信息
    common.LoadAreaCodeInfo = function (parentId, callback) {
        common.Ajax({
            url: '/Common/LoadAreaCodeInfo',
            //loading: true,
            data: { parentId: parentId },
            success: function (response) {
                callback(response);
            }
        });
    }

    //加载配置
    common.LoadCommonSetting = function (settingKey, async, callback, isLoading) {
        common.Ajax({
            url: '/Common/LoadCommonSetting',
            loading: (isLoading == undefined || isLoading == false) ? false : true,
            async: async == undefined ? true : async,
            data: { settingKey: settingKey },
            success: function (response) {
                if (typeof callback == 'function')
                    callback(response);
            }
        });
    }

    //保存配置
    common.SaveCommonSetting = function (settingKey, settingValue, callback) {
        common.Ajax({
            url: '/Common/SaveCommonSetting',
            loading: true,
            data: { settingKey: settingKey, settingValue: settingValue },
            success: function (response) {
                if (typeof callback == 'function')
                    callback(response);
            }
        });
    }

    //加载区域信息到控件
    common.LoadAreaInfoToControl = function (controlId, parentId, changeCallback, setDefaultFun, valueField) {

        var defaultOption = "<option value='0' data-value='0'>==请选择==</option>";

        var __areaDataToControl = function (areaData) {

            _areaCodeInfoList[parentId] = areaData;

            common.Foreach(areaData, function (i, o) {
                if (valueField && valueField.toLowerCase() == "name")
                    control.append("<option data-value='" + o.Id + "' value='" + o.Name + "'>" + o.Name + "</option>");
                else
                    control.append("<option data-value='" + o.Id + "'  value='" + o.Id + "'>" + o.Name + "</option>");
            });

            //事件绑定
            control.unbind('change').bind('change', function () {

                var parentId = "";
                if (valueField && valueField.toLowerCase() == "name") {
                    var select = $(this);
                    var val = select.val();
                    parentId = select.find("option[value='" + val + "']").attr("data-value");
                }
                else {
                    parentId = $(this).val();
                }

                __clearNextControl($(this));

                if (changeCallback != undefined && typeof changeCallback == "function")
                    changeCallback($(this))

                if (parentId == 0) {

                    return;
                }

                var nextControlId = $(this).attr("nextControlId");
                if (nextControlId == undefined) {
                    return;
                }
                common.LoadAreaInfoToControl(nextControlId, parentId, changeCallback, setDefaultFun, valueField);
            });

            //设置默认值
            if (setDefaultFun != undefined && typeof setDefaultFun == "function")
                setDefaultFun(control);

        }

        var __clearNextControl = function (control) {

            var nextControlId = control.attr("nextControlId");
            if (nextControlId == undefined) {
                return;
            }
            var nextControl = $("#" + nextControlId);

            var defaultOptText = nextControl.attr('defaultOptText');
            if (!!defaultOptText == true)
                defaultOptText = "<option value='0' data-value='0'>" + defaultOptText + "</option>";
            else
                defaultOptText = defaultOption;
            nextControl.empty().append(defaultOptText);
            __clearNextControl(nextControl);
        }

        var control = $("#" + controlId);
        if (control.length == undefined || control.length == 0) {
            return;
        }
        __clearNextControl(control);

        var defaultOptText = control.attr('defaultOptText');
        if (!!defaultOptText == true)
            defaultOptText = "<option value='0' data-value='0'>" + defaultOptText + "</option>";
        else
            defaultOptText = defaultOption;
        control.empty().append(defaultOptText);

        if (_areaCodeInfoList[parentId] != undefined) {
            __areaDataToControl(_areaCodeInfoList[parentId])
        }
        else {
            common.LoadAreaCodeInfo(parentId, function (rsp) {

                if (common.IsError(rsp)) {
                    return;
                }
                __areaDataToControl(rsp.Data);
            });
        }

    }

    //清除本地已经加载的 地址数据
    common.ClearAreaInfo = function (parentId) {
        if (_areaCodeInfoList)
            _areaCodeInfoList[parentId] = null;
    }

    //拆分地址
    common.AddressSplit = function (address) {
        var retArra = [];
        address = address || address.trimStartDgj().trimEndDgj();
        if (address == false) {
            return ['', '', '', ''];
        }

        var splitAction = function (address, deep, parentId) {

            var areaList = _areaCodeInfoList[parentId];
            if (areaList == null || areaList == undefined || areaList.length == 0) {
                common.Ajax({
                    url: '/Common/LoadAreaCodeInfo',
                    async: false,
                    data: { parentId: parentId },
                    success: function (rsp) {
                        if (common.IsError(rsp)) {
                            return;
                        }
                        if (rsp.Data != null && rsp.Data.length > 0) {
                            _areaCodeInfoList[parentId] = rsp.Data;
                            splitAction(address, deep, parentId);
                        }
                    }
                });
            }
            else {
                var areaModel = null;
                var index = -1;
                common.Foreach(areaList, function (i, o) {
                    index = address.indexOf(o.Name);
                    if (index >= 0) {
                        areaModel = o;
                        return 'break';
                    }
                });
                if (areaModel != null) {
                    retArra.push(areaModel.Name);
                    if (deep < 2) {
                        splitAction(address, (++deep), areaModel.Id);
                    }
                    else {
                        retArra.push(address.substring((index + areaModel.Name.length)));
                    }
                }
            }
        }

        splitAction(address, 0, 1, retArra);

        if (retArra.length < 4) {

            var consignee_province = "";
            var consignee_city = "";
            var consignee_area = "";
            var consignee_address = "";

            var toAreaList = address.split(' ');

            if (toAreaList.length >= 1) {
                consignee_province = toAreaList[0];
                if (consignee_province.length > 20)
                    consignee_province = consignee_province.substring(0, 20);
            }

            if (toAreaList.length >= 2) {
                consignee_city = toAreaList[1];
                if (consignee_city.length > 20)
                    consignee_city = consignee_city.substring(0, 20);
            }

            if (toAreaList.length >= 3) {
                consignee_area = toAreaList[2];
                if (consignee_area.length > 20)
                    consignee_area = consignee_area.substring(0, 20);
            }

            if (toAreaList.length >= 4) {
                consignee_address = toAreaList[3];
                if (consignee_address.length > 64)
                    consignee_address = consignee_address.substring(0, 64);
            }

            var tempList = [];
            tempList.push(consignee_province);
            tempList.push(consignee_city);
            tempList.push(consignee_area);
            tempList.push(consignee_address);

            var startIndex = retArra.length;
            for (var i = startIndex; i < 4; i++) {
                retArra.push(tempList[i]);
            }
        }

        return retArra;
    }

    //文件上传
    common.UploadFile = function (options) {
        var defaultOpts = {
            Url: common.rewriteUrl('/common/SaveFile'), //后台接受文件的action
            FileSaveDirect: 'Files/Temp', //文件保存目录
            IsShowFilePath: true,  //选择文件后界面是否显示文件
            DisplayId: 'txtFileName', //显示文件的空间名称
            UploadedCallback: function (path, ext) { }, //文件上传完成后的回调
            FileExtCheck: function (ext) { }, //文件扩展名检查
            OtherParams: null, //其他参数OtherParams={ p1:'p1',p2:'p2'...}
            //CheckRepeat: true, //检查文件重复上传
            FileFullPath: '', //文件全路劲
            FileExt: '', //文件扩展名(带点)
        };
        defaultOpts.url = common.addTraceIdToAjaxUrl(defaultOpts.url);
        var opts = $.extend({}, defaultOpts, options);

        if (opts.Url == false) {
            layer.alert('请指定文件上传的处理方法');
            return;
        }

        if (opts.FileSaveDirect == false) {
            layer.alert('请指定文件保存路径');
            return;
        }

        var loading = null;

        var iframeName = "ifrm_" + (new Date()).getTime();
        var iframe = $('<iframe name="' + iframeName + '" />');
        //iframe.css({ width: 300, height: 100 });
        iframe.css({ width: 0, height: 0 });

        var form = $('<form>');
        form.attr("action", opts.Url);
        form.attr("method", "post");
        form.attr("enctype", "multipart/form-data");
        form.attr("target", iframeName);
        form.hide();

        var fileInput = $('<input>');
        fileInput.attr("type", "file");
        fileInput.attr("name", "fileInfo");
        form.append(fileInput);

        var param1 = $('<input>');
        param1.attr("type", "text");
        param1.attr("name", "fileDirectory");
        param1.val(opts.FileSaveDirect);
        form.append(param1);

        //var param2 = $('<input>');
        //param2.attr("type", "text");
        //param2.val(opts.CheckRepeat);
        //form.append(param2);

        if (opts.OtherParams != null) {
            for (var p in opts.OtherParams) {
                var v = opts.OtherParams[p];
                if (Array.isArray(v) == true) {
                    for (var i = 0; i < v.length; i++) {
                        var px = $('<input>');
                        px.attr("type", "text");
                        px.attr("name", p + '[' + i + ']');
                        px.val(v[i]);
                        form.append(px);
                    }
                } else {
                    var px = $('<input>');
                    px.attr("type", "text");
                    px.attr("name", p);
                    px.val(v);
                    form.append(px);
                }
            }
        }

        $('body').append(form).append(iframe);

        //iframe.load(function (rsp) {
        //    layer.close(loading);
        //    var innerText = iframe.contents().find('body').text();
        //    var firstIndex = innerText.indexOf('{');
        //    var lastIndex = innerText.lastIndexOf('}');
        //    if (firstIndex > -1 && lastIndex > firstIndex) {
        //        innerText = innerText.substring(firstIndex, (lastIndex + 1));
        //    }
        //    //alert(result);
        //    if (innerText == "")
        //        return;
        //    var result = JSON.parse(innerText);
        //    if (result.Success == false) {
        //        layer.alert('文件上传失败：' + result.Message);
        //        return;
        //    }
        //    if (typeof opts.UploadedCallback == "function") {
        //        opts.UploadedCallback(result, opts.FileFullPath, opts.FileExt);
        //    }
        //    form.remove();
        //    iframe.remove();
        //});
        iframe[0].onload = iframe[0].onreadystatechange = function () {
            if (this.readyState && this.readyState != "complete") return;
            else {
                //获取iframe里面的内容
                var innerText = iframe[0].contentDocument.body.textContent;
                //上传完成后的处理
                if (innerText != "") {
                    var firstIndex = innerText.indexOf('{');
                    var lastIndex = innerText.lastIndexOf('}');
                    if (firstIndex > -1 && lastIndex > firstIndex) {
                        innerText = innerText.substring(firstIndex, (lastIndex + 1));
                    }
                    var result = JSON.parse(innerText);
                    if (result.Success == false) {
                        layer.alert('文件上传失败：' + result.Message);
                        return;
                    }
                    if (typeof opts.UploadedCallback == "function") {
                        opts.UploadedCallback(result, opts.FileFullPath, opts.FileExt);
                    }
                    form.remove();
                    iframe.remove();
                }
            }
        }

        fileInput.change(function () {
            var fileFullName = $(this).val();
            opts.FileFullPath = fileFullName;
            if (typeof opts.FileExtCheck == "function") {
                var flagIndex = fileFullName.lastIndexOf('.');
                var ext = '';
                if (flagIndex > -1) {
                    ext = fileFullName.substring(flagIndex);
                }
                opts.FileExt = ext;
                var checkResult = opts.FileExtCheck(ext);
                if (checkResult == false) {
                    layer.alert('不支持上传【' + ext + '】的文件');
                    return;
                }
            }
            if (opts.IsShowFilePath) {
                $('#' + opts.DisplayId).val(fileFullName);
            }
            loading = common.LoadingMsg('文件上传中');
            form.submit();
        }).trigger('click');
    }

    //打印预览图片
    var _doPrint = null;
    common.SetPrintAction = function (action) {
        _doPrint = action;
    }

    common.PreviewImg = function (imgs) {
        //var imgJson = {
        //    title: "xxx", //相册标题
        //    id: 123, //相册id
        //    start: 0, //初始显示的图片序号，默认0
        //    data: [], //相册包含的图片，数组格式
        //};
        //common.Foreach(imgs, function (i, img) {
        //    imgJson.data.push(
        //          {
        //              alt: "电子面单" + (i + 1),
        //              pid: i, //图片id
        //              src: img, //原图地址
        //              thumb: "" //缩略图地址
        //          });
        //});

        //layer.photos({
        //    photos: imgJson,
        //    closeBtn: 2,
        //    anim: -1 //0-6的选择，指定弹出图片动画类型，默认随机（请注意，3.0之前的版本用shift参数）
        //});

        var _images = []; //预览的图片
        var _index = 1;
        _images = imgs;
        var img_container = $('#div_preview_imags');
        img_container.empty();
        common.Foreach(imgs, function (i, img) {
            var display = 'none';
            if (i == 0) {
                display = "block";
            }
            img_container.append('<img name="img_' + (i + 1) + '" src="' + img + '" style="display:' + display + ';width:330px;height:600px;" />');
        });

        $('.aialog_wrapper_main_iconClose').unbind('click').bind('click', function () {
            $('#div_print_preview').hide();
        });
        $('.aialog_wrapper_main_iconLeft').unbind('click').bind('click', function () {
            $('#div_preview_imags img[name="img_' + _index + '"]').hide();
            if (_index == 1) {
                _index = _images.length;
            }
            else {
                _index = _index - 1;
            }
            $('#div_preview_imags img[name="img_' + _index + '"]').show();
            $('#lbl_preview_current_count').text(_index);
        });
        $('.aialog_wrapper_main_iconRight').unbind('click').bind('click', function () {
            $('#div_preview_imags img[name="img_' + _index + '"]').hide();
            if (_index == _images.length) {
                _index = 1;
            }
            else {
                _index = _index + 1;
            }
            $('#div_preview_imags img[name="img_' + _index + '"]').show();
            $('#lbl_preview_current_count').text(_index);
        });

        $('#div_preivew_doprint').unbind('click').bind('click', function () {
            if (typeof _doPrint == 'function') {
                _doPrint();
                $('#div_print_preview').hide();
            }
        });

        $('#lbl_preview_current_count').text(_index);
        $('#lbl_preview_count').text(_images.length);
        $('#div_print_preview').show();

    }

    //获取当前模板使用的打印组件
    common.GetUsePrintComponents = function (templateType) {
        var printComponents = '';
        if (templateType == 1 || templateType == 3 || templateType == 10 || (templateType >= 60 && templateType <= 90)) {
            printComponents = 'Lodop';
        }
        else if (templateType <= 20) {
            printComponents = 'Cainiao';
        }
        else if (templateType < 40) {
            printComponents = 'Pinduoduo';
        }
        else if (templateType >= 40 && templateType < 60) {
            printComponents = 'Cainiao';
        }
        return printComponents;
    }

    common.SavePrintLog = function () {

    }

    common.getPrintSerialNum = function () {

    }

    common.clearSerialNum = function () {

    }

    common.isCustomerOrder = function () {
        var path = window.location.pathname;
        if (path.indexOf("FreePrintList") > -1 || path.indexOf("FreePrintExpress") > -1)
            return true;
        return false;
    }

    common.NameInputOnBlurHandler = function (ipt) {
        var $ipt = $(ipt);
        var val = $ipt.val();
        if (val.length > 20) {
            layer.alert("收发件人姓名不能超过20个字符");
        }
    }

    common.PhoneInputOnBlurHandler = function (ipt) {
        var $ipt = $(ipt);
        var val = $ipt.val();
        val = val.replace(/[+-]+/g, '');
        if (isNaN(val) || val.length > 18) {
            layer.alert("电话号码只能包含+、-、数字且不能超过20个字符");
        }
    }

    common.IsPddFds = function () {
        return location.href.toLowerCase().indexOf('indexfds') > -1; //是厂商代打页面
    }

    common.closeWindows = function closeWindows() {
        var browserName = navigator.appName;
        var browserVer = parseInt(navigator.appVersion);
        //alert(browserName + " : "+browserVer);
        //document.getElementById("flashContent").innerHTML = "<br>&nbsp;<font face='Arial' color='blue' size='2'><b> You have been logged out of the Game. Please Close Your Browser Window.</b></font>";
        if (browserName == "Microsoft Internet Explorer") {
            var ie7 = (document.all && !window.opera && window.XMLHttpRequest) ? true : false;
            if (ie7) {
                //This method is required to close a window without any prompt for IE7 & greater versions.
                window.open('', '_parent', '');
                window.close();
            }
            else {
                //This method is required to close a window without any prompt for IE6
                this.focus();
                self.opener = this;
                self.close();
            }
        } else {

            try {
                window.focus();
                self.opener = this;
                self.close();
            }
            catch (e) {

            }
            //For NON-IE Browsers except Firefox which doesnt support Auto Close
            try {
                window.location.href = "about:blank";
                window.close();
            } catch (e) {
            }
            try {
                window.open('', '_self', '');
                window.close();
            }
            catch (e) {

            }
        }
    }

    common.getToken = function getToken() {
        var token = $("#token_input").val();
        if (!token)
            token = "";
        return token;
    }

    common.rewriteUrl = function rewriteUrl(url) {
        var token = $("#token_input").val();
        if (!token)
            token = "";
        if (!url)
            return "?token=" + token;
        if (url.indexOf("token=") != -1)
            return url;
        if (url.indexOf("?") != -1)
            return url + "&token=" + token;
        else
            return url + "?token=" + token;
        return url;
    }

    common.addTraceIdToAjaxUrl = function addTraceIdToAjaxUrl(url) {
        var token = $("#traceid_input").val();
        if (!token)
            token = "";
        if (!url)
            return "?traceId=" + token;
        if (url.indexOf("traceId=") != -1)
            return url;
        if (url.indexOf("?") != -1)
            return url + "&traceId=" + token;
        else
            return url + "?traceId=" + token;
        return url;
    }

    common.transferUrl = function (url, target) {
        var newUrl = common.rewriteUrl(url);
        var a = $('<a>');
        if (target)
            a.attr("target", target);
        a.attr('href', newUrl);
        a.append('<p style="display:none;">跳转</p>');
        $('body').append(a);
        a.children().trigger('click').end().remove();
    }

    $(document).ready(function () {
        $(".navigations a,.leftNav_left a,a").each(function (index, a) {
            if (a.href && a.href != "#" && a.href.indexOf("javascript") == -1 && a.className.indexOf("no-token") == -1) {
                a.href = common.rewriteUrl(a.href);
            }
        });
    });

    common.isEmptyObject = function (obj) {
        for (var key in obj) {
            return false
        };
        return true
    }

    //自定：定位没有遮罩的弹框
    var positionDialogThis, positionDialogContent;
    common.positionDialog = function (isThis, newObject) {

        positionDialogThis = isThis;          //current接收点击的this;
        positionDialogContent = newObject.content;//为用弹框的class 格式比如：".dialog";
        $(positionDialogThis).children(positionDialogContent).remove();//先清理点击元素里面已经出现的弹框;
        $(positionDialogContent).clone().show().appendTo($(positionDialogThis));
        $(positionDialogContent).css({ top: newObject.top, left: newObject.left, position: "absolute" });

    }
    $(document).mouseup(function (e) {
        if (positionDialogThis) {
            var pop = $(positionDialogThis);
            if (!pop.is(e.target) && pop.has(e.target).length === 0) {
                $(positionDialogThis).children(positionDialogContent).remove();
            }
        }

    });

    //阻止浏览器的默认动作
    common.preventDefault = function (e) {
        if (e && e.preventDefault) {
            e.preventDefault();
        } else {
            window.event.returnValue = false;
        }
    }
    common.openProductUrl = function (id) {
        var platformType = (common.PlatformType || "").toLowerCase();
        var url = "";
        if (platformType == "1688" || platformType == "Alibaba" || platformType == "alibaba")
            url = "https://detail.1688.com/offer/" + id + ".html";
        else if (platformType == "tb" || platformType == "Taobao" || platformType == "taobao"
            || platformType == "AlibabaC2M" || platformType == "alibabac2m" || platformType == "TaobaoMaiCaiV2" || platformType == "taobaomaicaiv2"
        )
            url = "https://item.taobao.com/item.htm?id=" + id;
        else if (platformType == "pdd" || platformType == "Pinduoduo" || platformType == "pinduoduo")
            url = "https://mms.pinduoduo.com/goods/goods_detail?goods_id=" + id;
        else if (platformType == "jd" || platformType == "Jingdong" || platformType == "jingdong")
            url = "https://item.jd.com/" + id + ".html";
        else if (platformType == "yz" || platformType == "YouZan" || platformType == "youzan")
            url = "https://detail.youzan.com/show/goods?alias=" + id;
        else if (platformType == "wm" || platformType == "WeiMeng" || platformType == "weimeng")
            url = "https://100000080481.retail.n.weimob.com/saas/retail/100000080481/3060881/goods/detail?id=" + id;
        else if (platformType == "wd" || platformType == "WeiDian" || platformType == "weidian")
            url = "https://weidian.com/item.html?itemID=" + id + "&spider_token=" + Math.random(1000);
        else if (commonModule.IsTouTiaoXi())
            url = "https://haohuo.jinritemai.com/views/product/detail?id=" + id + "&origin_type=604&spider_token=" + Math.random(1000);
        //else if (platformType == "yunji" || platformType == "yj" )
        //    url = "http://image.yunjiglobal.com/?id=" + id + "&fxg_admin_preview=110&origin_type=604&spider_token=" + Math.random(1000);
        else if (platformType == "kuaishou" || platformType == "ks")
            url = "https://s.kwaishop.com/goods/detail?id=" + id;
        if (url != "")
            window.open(url, "_blank");
        else
            layer.msg("当前平台暂不支持查看商品信息");
    }
    common.openOrderUrl = function (id) {
        var url = "";
        var platformType = (common.PlatformType || "").toLowerCase();
        if (platformType == "1688" || platformType == "Alibaba" || platformType == "alibaba")
            url = "https://trade.1688.com/order/new_step_order_detail.htm?orderId=" + id;
        else if (platformType == "tb" || platformType == "Taobao" || platformType == "taobao"
            //|| platformType == "AlibabaC2M" || platformType == "alibabac2m"
        )
            url = "https://trade.taobao.com/trade/detail/trade_item_detail.htm?bizOrderId=" + id;
        else if (platformType == "pdd" || platformType == "Pinduoduo" || platformType == "pinduoduo")
            url = "https://mms.pinduoduo.com/order.html#/orders/order_detail/index?type=0&sn=" + id;
        else if (platformType == "jd" || platformType == "Jingdong" || platformType == "jingdong")
            url = "https://details.jd.com/normal/item.action?PassKey=4DD91555907E4EBF4732B1EA2E07FA05&orderid=" + id;
        else if (platformType == "yz" || platformType == "YouZan" || platformType == "youzan")
            url = "https://www.youzan.com/v4/trade/order/detail?orderNo=" + id;
        else if (platformType == "wm" || platformType == "WeiMeng" || platformType == "weimeng")
            url = "http://retail.console.weimob.com/#/app/100000080481/3060881/order/order/orderdetail?orderNo=" + id + "&orderType=1";
        else if (platformType == "wd" || platformType == "WeiDian" || platformType == "weidian")
            url = "https://d.weidian.com/orderNew/?orderId=" + id + "#/orderDetail/detail";
        else if (commonModule.IsTouTiaoXi())
            url = "https://fxg.jinritemai.com/index.html#/iview/order/orderPreview/" + id;
        else if (platformType == "kuaishou" || platformType == "ks")
            url = "https://s.kwaishop.com/order/detail?id=" + id;
        if (url != "")
            window.open(url, "_blank");
        else
            layer.msg("当前平台暂不支持查看后台订单信息");
    }

    common.SaveTraditionWaybillCodeConfig = function (template) {
        try {
            //如果是传统模板，有生成规则，且有单号，则记录最后的单号
            // var orderTotalVal = $("#txtOrderTotal").val();
            var addOrSub = $("#sel_logistic_generate").val();
            if (template.TemplateType == 1 && addOrSub != 0 && window.orderTableBuilder) {
                //取最后一个选中的订单的运单号+1 作为 下次自动生成的起始单号
                var lastLogisticCode = '';
                for (var i = 0; orderTableBuilder.rows && i < orderTableBuilder.rows.length; i++) {
                    var row = orderTableBuilder.rows[i];
                    if (row.checked) {
                        var input = $("#order-" + row.Id + " .LastWaybillCode_input");
                        lastLogisticCode = input.val();
                    }
                }
                if (lastLogisticCode) {
                    //加1
                    var endStr = '';
                    if (lastLogisticCode.length > 3) {
                        endStr = lastLogisticCode.substring(lastLogisticCode.length - 4);
                    }
                    else {
                        endStr = lastLogisticCode;
                    }
                    var start_num = parseInt(endStr); //起始
                    start_num++;
                    var tempNum = lastLogisticCode.substring(0, lastLogisticCode.length - start_num.toString().length);
                    lastLogisticCode = tempNum + '' + start_num;
                    //保存 单号
                    tradionalWaybillCodeConfig[template.Id] = {
                        StartWaybillCode: lastLogisticCode,
                        AddOrSub: addOrSub,
                        //OrderTotalNumber: (orderTotalVal ? parseInt(orderTotalVal) : orderTotalVal)
                    };
                    var settingKey = "TradionalWaybillCodeConfig";//配置key
                    common.LoadCommonSetting(settingKey, true, function (rsp) {

                        var params = tradionalWaybillCodeConfig;
                        if (rsp.Data != null) {
                            var settings = eval('(' + rsp.Data + ')');
                            if (settings) {
                                params = $.extend({}, settings, tradionalWaybillCodeConfig);
                            }
                        }
                        common.SaveCommonSetting(settingKey, JSON.stringify(params), function () { });
                    });
                }
            }
        } catch (e) {
            console.log("ERROR-保存普通面单最后的单号失败:" + e.message);
        }
    }

    //ctrl:添加搜索的控件
    //containerAppendIn:div_container 添加到哪个控件里面
    //searchAction: 查询操作(第一个参数是容器对象div_container,第二个参数 输入的关键字)
    //ptn: 控件的位置 取值 {pt:'offset or position',l:x,t:y}
    //dataAttrs: {属性名:值}，控件自定义属性数据
    //isRemoveFirst:bool，是否先移除，再添加，方式弹窗多次绑定，多次添加容器
    common.AddInputOrSelect = function (ctrl, containerAppendIn, searchAction, ptn, dataAttrs, isRemoveFirst) {

        var div_container = $("<div>");
        div_container.attr("class", "div_container");
        if (dataAttrs) {
            for (var i in dataAttrs) {
                if (i.isPrototypeOf() == false) {
                    div_container.attr(i, dataAttrs[i]);
                }
            }
        }
        if (isRemoveFirst)
            containerAppendIn.find('div.div_container').remove();
        containerAppendIn.append(div_container);

        div_container.unbind('click').on('click', function (event) {
            event.stopPropagation();
        });

        $(document).click('click', function () {
            div_container.hide();
        });

        ctrl.unbind('click').on('click', function (event) {
            var position = (ptn && ptn.pt == 'offset' || ptn == 'offset') ? $(this).offset() : $(this).position();
            var l = (ptn && ptn.l != undefined && isNaN(ptn.l) == false) ? ptn.l : 0;
            var t = (ptn && ptn.t != undefined && isNaN(ptn.t) == false) ? ptn.t : 0;
            position.top += $(this).outerHeight() + 2 + t;
            position.left += l;
            div_container.css({ left: position.left, top: position.top }).html('<img src="/Content/Images/loading.gif" width="20" height="20" />').show();

            var keyWord = '';//$(this).val();
            if (typeof searchAction == 'function')
                searchAction(div_container, keyWord);

            event.stopPropagation();
        }).unbind('input').on('input', function () {
            var keyWord = $(this).val();
            if (typeof searchAction == 'function')
                searchAction(div_container, keyWord);
        });
    }

    //前端报错，日志记录
    common.JsExcptionLog = function (operatorType, errorMsg) {
        common.ajax({
            url: '/Common/JavasScriptExcptionLog',
            data: { operatorType: operatorType, exception: errorMsg },
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.alert('前端出错且日志记录失败，请联系我们告知错误信息，谢谢你！error：' + rsp.Message);
                }
            }
        });
    }

    //前端日志记录
    common.JsLogToMongoDB = function (operatorType, logJson) {
        common.ajax({
            url: '/Common/JavasScriptLog',
            data: { operatorType: operatorType, logJson: logJson },
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.alert('前端日志记录失败，请联系我们告知错误信息，谢谢你！error：' + rsp.Message);
                }
            }
        });
    }

    common.GetDateDiffDays = function (startDate, endDate) {
        var startTime = new Date(Date.parse(startDate.replace(/-/g, "/"))).getTime();
        var endTime = new Date(Date.parse(endDate.replace(/-/g, "/"))).getTime();
        var dates = Math.abs((startTime - endTime)) / (1000 * 60 * 60 * 24);
        return parseInt(dates);
    }

    common.IsNumber = function (obj) {
        // return typeof obj === 'number' && isFinite(obj)
        return obj === +obj;
    }

    common.IsString = function (obj) {
        return obj === obj + ''
    }

    common.IsBoolean = function (obj) {
        return obj === !!obj
    }

    //用户注册
    common.register = function (from, portalUrl, token) {
        if (!portalUrl) {
            layer.alert("注册服务暂不可用，请稍后重试");
            return;
        }
        if (from == "pay") {
            layer.open({
                title: "提示",
                content: "<h1>您还没有注册，在续费前请先注册，注册后会自动跳转到续费页面。</h1>",
                btn: ['立即注册并续费', "已注册过，前往登录"],
                yes: function () {

                    openRegisterDialog(from, portalUrl, token);

                },
                btn2: function () {
                    window.open(portalUrl);
                },
            });
        }
        else
            openRegisterDialog("", portalUrl, token);

        //注册成功，跳转到
    }

    function openRegisterDialog(from, portalUrl, token) {
        //打开注册弹框
        //from参数为空表示用户主动注册，注册完后不进行跳转
        //from参数为“pay”,表示点击续费时弹出注册，注册完后自动跳转到续费链接（跳转链接注册接口返回：Data.PayUrl）
        //弹框
        //发送请求带上commonModule.Token

        //layer.alert("打开注册窗口");
        var html = $("#register-user-dialog-tmpl").render({ "from": from, "portalUrl": portalUrl, "token": token });
        layer.open({
            type: 1,
            title: "注册",
            skin: 'layui-layer-rim',
            area: ['380px'],
            content: html
        });
        return;
    }

    common.newRegister = function (from, portalUrl, token) { // -----------注册----------------------------------------

        if (!checkPhone("#intoRegisterTelePhone", "请输入正确的手机号！")) {
            return;
        }
        if (!checkPwd("#intoRegisterTelePass", "密码长度要大于6位，由数字和字母组成")) {
            return;
        }

        if ($('#intoRegisterTelePass').val() != $('#sureIntoRegisterTelePass').val()) {
            $('.loginWrap_content_warn').css({ display: 'flex' }).text('两次输入的密码不一致');
            timeoutsss = setTimeout(function () {
                $('.loginWrap_content_warn').hide(300)
            }, 4000)
            return;
        }

        // var body={};
        // body.Mobile= $("#intoRegisterTelePhone").val();
        // body.Password=$("#intoRegisterTelePass").val();

        var body = {
            Mobile: $("#intoRegisterTelePhone").val(),
            Password: $("#intoRegisterTelePass").val(),
            ConfirmPassword: $("#sureIntoRegisterTelePass").val(),
            MobileMeessageCode: $("#intoRegisterValidCode").val(),
            Token: common.Token
        }
        if (!token)
            body.Token = common.Token;
        else
            body.Token = token;
        $.ajax({
            url: portalUrl + '/Account/RegisterV2',
            type: 'post',
            data: body,
            success: function (rsp) {
                if (!rsp.Success) {
                    if (rsp.ErrorCode == "Has_Registered") {
                        layer.open({
                            title: "提示",
                            content: "该手机号码已经注册过了，请直接登录",
                            btn: ["前往登录", "取消"],
                            yes: function () {
                                window.open(portalUrl);
                            },
                            btn2: function () {
                                return true;
                            },
                        });
                    }
                    else
                        layer.alert(rsp.Message);
                } else {
                    $("body").append("<iframe style='display:none;' src='" + portalUrl + "/Account/WriteCookie?t=" + rsp.Data.Token + "'/>")
                    if (from && from == "pay") {
                        if (rsp.Data.PayUrl)
                            window.location = rsp.Data.PayUrl;
                        else
                            window.location = rsp.Data.RedirectUrl;
                    }
                    else {
                        //跳转到个人中心
                        window.location = rsp.Data.RedirectUrl;
                    }
                }
            }
        })

    }


    //页面层




    //短信验证-----------------------------------------------------

    common.postMobileMessageCode = function (portalUrl, types) {

        if (types == "1" && !checkPhone("#intoRegisterTelePhone", "您输入的手机码有误，请重新输入")) {       //检测手机号
            return;
        }

        if (types == "2" && !checkPhone("#againSetPhone", "您输入的手机码有误，请重新输入")) {       //检测手机号
            return;
        }

        var phone = "";
        if (types == "1")
            phone = $("#intoRegisterTelePhone").val();
        if (types == "2")
            phone = $("#againSetPhone").val();


        $.ajax({
            type: 'post',
            url: portalUrl + '/Account/PostMobileMessageCode',
            data: { phone: phone, types: types },
            success: function (data) {
                if (!data.Success) {
                    layer.alert(data.Message)
                    return;
                }

                settime();


            }
        });

    }

    function checkPhone(ele, str) {  //匹配手机号，正确返回true 错误返回false
        var phone = $(ele).val();
        if (!(/^1(3|4|5|7|8|9)\d{9}$/.test(phone))) {
            $('.loginWrap_content_warn').css({ display: 'flex' }).text(str);
            timeoutsss = setTimeout(function () {
                $('.loginWrap_content_warn').hide(300)
            }, 4000)
            return false;
        }
        return true;
    }

    function checkPwd(ele, str) {  //匹配密码，正确返回true 错误返回false
        var pwd = $(ele).val();
        var reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,}$/;
        if (!reg.test(pwd)) {
            $('.loginWrap_content_warn').css({ display: 'flex' }).text(str);
            timeoutsss = setTimeout(function () {
                $('.loginWrap_content_warn').hide(300)
            }, 4000)
            return false;
        }
        return true;
    }

    common.stopM = function (event) {  //阻止冒泡和默认行为
        if (event) {
            event.stopPropagation();
            event.preventDefault();
        } else {
            window.event.returnValue = false;
            window.event.cancelBubble = true;
        };
    }

    common.IEBrowserVersion = function IEBrowserVersion() {
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
        var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
        var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
        if (isIE) {
            var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
            reIE.test(userAgent);
            var fIEVersion = parseFloat(RegExp["$1"]);
            if (fIEVersion == 7) {
                return 7;
            } else if (fIEVersion == 8) {
                return 8;
            } else if (fIEVersion == 9) {
                return 9;
            } else if (fIEVersion == 10) {
                return 10;
            } else {
                return 6;//IE版本<=7
            }
        } else if (isEdge) {
            return 'edge';//edge
        } else if (isIE11) {
            return 11; //IE11
        } else {
            return -1;//不是ie浏览器
        }
    }

    var count = 60;
    function settime() {
        if (count == 0) {
            $("#LoginCode1").attr('disabled', false);
            $("#LoginCode2").attr('disabled', false);
            $("#LoginCode1").removeClass("messageCodeclass");
            $("#LoginCode2").removeClass("messageCodeclass");
            $("#LoginCode1").val("免费获取验证码");
            $("#LoginCode2").val("免费获取验证码");
            count = 60;
            return;
        } else {
            $("#LoginCode1").attr('disabled', true);
            $("#LoginCode2").attr('disabled', true);
            $("#LoginCode1").addClass("messageCodeclass");
            $("#LoginCode2").addClass("messageCodeclass");

            $("#LoginCode1").val("重新发送(" + count + ")");
            $("#LoginCode2").val("重新发送(" + count + ")");
            count--;
        }

        setTimeout(function () { settime() }, 1000)
    }

    common.ConvertPlatformStatusToName = function (status) {
        if (status == "waitbuyerpay")
            status = "待买家付款";
        else if (status == "waitsellersend")
            status = "待发货";
        else if (status == "waitbuyerreceive")
            status = "待买家确认收货";
        else if (status == "success")
            status = "交易成功";
        else if (status == "cancel")
            status = "交易关闭";
        else if (status == "confirm_goods_but_not_fund")
            status = "货到付款";
        else if (status == "locked")
            status = "退款中";
        else
            status = "未知";
        return status;
    }

    common.ConvertPlatformTypeToName = function (type) {
        var name = "未识别";
        switch (type) {
            case "OpenV1":
                name = "开放平台";
                break;
            case "MoGuJie":
                name = "蘑菇街";
                break;
            case "WeiDian":
                name = "微店";
                break;
            case "ZhiDian":
                name = "值点";
                break;
            case "AlibabaC2M":
                name = "阿里巴巴C2M";
                break;
            case "TaobaoMaiCaiV2":
                name = "淘宝买菜（新）";
                break; 
            case "Taobao":
                name = "淘宝";
                break;
            case "WeiMeng":
                name = "微盟";
                break;
            case "XiaoDian":
                name = "小店";
                break;
            case "MengTui":
                name = "萌推";
                break;
            case "Alibaba":
                name = "阿里巴巴";
                break;
            case "YouZan":
                name = "有赞";
                break;
            case "YunJi":
                name = "云集";
                break;
            case "TouTiao":
            case "TouTiaoXiaoDian":
                name = "头条";
                break;
            case "Offline":
                name = "微商";
                break;
            case "Pinduoduo":
                name = "拼多多";
                break;
                break;
            case "KuaiShou":
                name = "快手";
                break;
            case "Jingdong":
                name = "京东";
                break;
            case "BeiBei":
                name = "贝贝";
                break;
            case "Suning":
                name = "苏宁";
                break;
        }
        return name;
    }

    common.checkFileExt = function (fileName, arr) {  //-判断文件扩展名------fileName为文件名,arr扩展名集合比如["jpg","gif","png"]
        var flag = false;
        var $arr = arr;
        var index = fileName.lastIndexOf(".");//返回 . 所在的索引号
        var ext = fileName.substr(index + 1);//获取文件扩展名
        for (var i = 0; i < $arr.length; i++) {
            if (ext == arr[i]) {
                flag = true;//一旦找到合适的，立即退出循环
                break;
            }
        }
        return flag;
    }

    common.IsPhone = function (phone) {
        if (!phone) return false;
        var reg = /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/; //校验手机号和固定电话
        if (!reg.test(phone)) {
            return false;
        }
        return true;
    }

    common.ToFixed = function (val, count) {
        var changenum = (parseInt(val * Math.pow(10, count) + 0.5) / Math.pow(10, count)).toString();
        index = changenum.indexOf(".");
        if (index < 0 && count > 0) {
            changenum = changenum + ".";
            for (i = 0; i < count; i++) {
                changenum = changenum + "0";
            }

        } else {
            index = changenum.length - index;
            for (i = 0; i < (count - index) + 1; i++) {
                changenum = changenum + "0";
            }
        }
        return changenum;
    }
    //关闭升级续费弹窗
    common.closeUpgradeWindow = function () {
        $("#commonDialog_wrap").fadeOut(200);
        document.body.style.cssText = 'overflow-y:auto';
    }
    common.closeAdvancedEditionWindow = function () {
        $.cookie("noShowUprgradeSuggestWindow", "1", { expires: 1 });
        $("#advancedEdition_wrap").fadeOut(200);
    }
    //edition为升级版本变量，取值 standardEdition为标准版，advancedEdition为高级版
    //nowEdition为当前版本的名称
    //isShowChoseBtn 为是否显示右上角关闭按钮  默认显示，可以不设置
    common.showUpgradeWindowOld = function (edition, nowEdition, isShowChoseBtn) {

        var platformType = (common.PlatformType || "").toLowerCase();
        if (platformType == "1688" || platformType == "Alibaba" || platformType == "alibaba") {
            document.body.style.cssText = 'overflow-y:hidden';
            $("#commonDialog_wrap").fadeIn(200);
            $("#nowEditionName").html(nowEdition ? nowEdition : "");
            var isChoseBtn = true;
            if (typeof isShowChoseBtn == "undefined") {
                isChoseBtn = true
            } else {
                isChoseBtn = isShowChoseBtn
            }
            if (!isChoseBtn) {
                $("#dialog_upgrade_close").hide();
            } else {
                $("#dialog_upgrade_close").show();
            }
            var html = "";
            var html02 = "";
            var orderPeriodicTimeData = [];//订购周期
            var typeName = edition == "standardEdition" ? "标准版" : "高级版";
            if (edition == "standardEdition") { // 标准版
                html += '<li><i></i><span>快递模板绑定对应发件人信息</span></li>'
                html += '<li><i></i><span>扫描订单编号，打印快递单</span></li>'
                html += '<li><i></i><span>扫描包裹上的快递运单号发货，变更订单状态为已发货</span></li>'
                html += '<li><i></i><span>表格导入订单发货</span></li>'
                html += '<li><i></i><span>导入线下快递对账单到工具内，单号使用对账</span></li>'
                orderPeriodicTimeData = [
                    { id: 0, month: 1, price: 42 },
                    { id: 1, month: 3, price: 112 },
                    { id: 2, month: 6, price: 200 },
                    { id: 3, month: 12, price: 352 }
                ];
                $(".dialog-upgrade-btn").css({ paddingTop: 6 });

            } else if (edition == "advancedEdition") {   // 高级版
                html += '<li><i></i><span>待付款订单，一键快速改价</span></li>'
                html += '<li><i></i><span>设置评价内容和规则，订单自动评价</span></li>'
                html += '<li><i></i><span>备货单统计打印小标签，市场档口拿货，分拣打包，扫描打印快递单发货</span></li>'
                html += '<li><i></i><span>手机端打单发货</span></li>'
                orderPeriodicTimeData = [
                    { id: 0, month: 1, price: 50 },
                    { id: 1, month: 3, price: 135 },
                    { id: 2, month: 6, price: 240 },
                    { id: 3, month: 12, price: 420 }
                ];
                $(".dialog-upgrade-btn").css({ paddingTop: 18 });

            }
            $("#dialog_upgrade_funShow").html(html);
            for (var i = 0; i < orderPeriodicTimeData.length; i++) {
                html02 += '<li><span>' + typeName + '-' + orderPeriodicTimeData[i].month + '月</span><span>' + orderPeriodicTimeData[i].price + '元</span><a>立即订购</a></li>'
            }
            var html03 = ' <span class="dialog-upgrade-btns">已经订购，点击激活使用</span>';
            $(".typeEdition").html(typeName);
            $("#orderPeriodicTimeData").html(html02);
            $("#dialog_upgrade_btn").html(html03);
            return true;

        } else {
            return false;
        }

    }

    //edition为升级版本变量，取值 standardEdition为标准版，advancedEdition为高级版
    //nowEdition为当前版本的名称
    //isShowChoseBtn 为是否显示右上角关闭按钮  默认显示，可以不设置
    common.showUpgradeWindow = function (versionInfo, isShowChoseBtn) {

        //头条升级弹窗不一样
        if (commonModule.PlatformType == "TouTiao") {
            $("#customDialog_Bg").show();
            return;
        }

        var existWindows = $("#advancedEdition_wrap");
        if (existWindows && existWindows.length > 0)
            return;
        document.body.style.cssText = 'overflow-y:hidden';
        $("#commonDialog_wrap").fadeIn(200);
        $("#nowEditionName").html(versionInfo.CurrentSystemVersionName);
        var isChoseBtn = true;
        if (typeof isShowChoseBtn == "undefined") {
            isChoseBtn = true
        } else {
            isChoseBtn = isShowChoseBtn
        }
        if (!isChoseBtn) {
            $("#dialog_upgrade_close").hide();
        } else {
            $("#dialog_upgrade_close").show();
        }
        var html = "";
        var html02 = "";
        var orderPeriodicTimeData = [];//订购周期
        var targetVersion = versionInfo.TargetVersion;
        var functions = targetVersion.VersionFunctions;
        var typeName = targetVersion.Name;
        orderPeriodicTimeData = targetVersion.PriceList;
        var shopNames = "";
        for (var i = 0; i < versionInfo.NeedUpgradeShopNames.length; i++) {
            var sname = versionInfo.NeedUpgradeShopNames[i];
            shopNames += "【" + sname + "】";
        }
        $("#versionTip-ShopNames").html(shopNames);
        for (var i = 0; i < functions.length; i++) {
            var f = functions[i];
            html += '<li><i></i><span>' + f + '</span></li>';
        }
        $(".dialog-upgrade-btn").css({ paddingTop: 6 });
        $("#dialog_upgrade_funShow").html(html);
        for (var i = 0; i < orderPeriodicTimeData.length; i++) {
            html02 += '<li><span>' + typeName + '-' + orderPeriodicTimeData[i].month + '月</span><span>' + orderPeriodicTimeData[i].price + '元</span><a target="_blank" href="' + versionInfo.PayLink + '">立即订购</a></li>'
        }
        var html03 = ' <span class="dialog-upgrade-btns" onclick="getExpireTimeFromApi(true,\'激活中，正在重新加载页面...\');">已经订购，点击激活使用</span>';
        $(".typeEdition").html(typeName);
        $("#orderPeriodicTimeData").html(html02);
        $("#dialog_upgrade_btn").html(html03);
        return true;

    }

    //+---------------------------------------------------
    //| 求两个时间的天数差 日期格式为 YYYY-MM-dd
    //+---------------------------------------------------
    common.DaysBetween = function (DateOne, DateTwo) {
        var OneMonth = DateOne.substring(5, DateOne.lastIndexOf('-'));
        var OneDay = DateOne.substring(DateOne.length, DateOne.lastIndexOf('-') + 1);
        var OneYear = DateOne.substring(0, DateOne.indexOf('-'));

        var TwoMonth = DateTwo.substring(5, DateTwo.lastIndexOf('-'));
        var TwoDay = DateTwo.substring(DateTwo.length, DateTwo.lastIndexOf('-') + 1);
        var TwoYear = DateTwo.substring(0, DateTwo.indexOf('-'));

        var cha = ((Date.parse(OneMonth + '/' + OneDay + '/' + OneYear) - Date.parse(TwoMonth + '/' + TwoDay + '/' + TwoYear)) / 86400000);
        return Math.abs(cha);
    }

    //+---------------------------------------------------
    //| 日期合法性验证
    //| 格式为：YYYY-MM-DD或YYYY/MM/DD
    //+---------------------------------------------------
    common.IsValidDate = function (DateStr) {
        var sDate = DateStr.replace(/(^\s+|\s+$)/g, ''); //去两边空格;
        if (sDate == '') return true;
        //如果格式满足YYYY-(/)MM-(/)DD或YYYY-(/)M-(/)DD或YYYY-(/)M-(/)D或YYYY-(/)MM-(/)D就替换为''
        //数据库中，合法日期可以是:YYYY-MM/DD(2003-3/21),数据库会自动转换为YYYY-MM-DD格式
        var s = sDate.replace(/[\d]{ 4,4 }[\-/]{ 1 }[\d]{ 1,2 }[\-/]{ 1 }[\d]{ 1,2 }/g, '');
        if (s == '') //说明格式满足YYYY-MM-DD或YYYY-M-DD或YYYY-M-D或YYYY-MM-D
        {
            var t = new Date(sDate.replace(/\-/g, '/'));
            var ar = sDate.split(/[-/:]/);
            if (ar[0] != t.getYear() || ar[1] != t.getMonth() + 1 || ar[2] != t.getDate()) {
                //alert('错误的日期格式！格式为：YYYY-MM-DD或YYYY/MM/DD。注意闰年。');
                return false;
            }
        }
        else {
            //alert('错误的日期格式！格式为：YYYY-MM-DD或YYYY/MM/DD。注意闰年。');
            return false;
        }
        return true;
    }

    //+---------------------------------------------------
    //| 日期时间检查
    //| 格式为：YYYY-MM-DD HH:MM:SS
    //+---------------------------------------------------
    common.CheckDateTime = function (str) {
        var reg = /^(\d+)-(\d{ 1,2 })-(\d{ 1,2 }) (\d{ 1,2 }):(\d{ 1,2 }):(\d{ 1,2 })$/;
        var r = str.match(reg);
        if (r == null) return false;
        r[2] = r[2] - 1;
        var d = new Date(r[1], r[2], r[3], r[4], r[5], r[6]);
        if (d.getFullYear() != r[1]) return false;
        if (d.getMonth() != r[2]) return false;
        if (d.getDate() != r[3]) return false;
        if (d.getHours() != r[4]) return false;
        if (d.getMinutes() != r[5]) return false;
        if (d.getSeconds() != r[6]) return false;
        return true;
    }

    //+---------------------------------------------------
    //| 字符串转成日期类型
    //| 格式 MM/dd/YYYY MM-dd-YYYY YYYY/MM/dd YYYY-MM-dd
    //+---------------------------------------------------
    common.StringToDate = function (DateStr) {
        var converted = Date.parse(DateStr);
        var myDate = new Date(converted);
        if (isNaN(myDate)) {
            //var delimCahar = DateStr.indexOf('/')!=-1?'/':'-';
            var arys = DateStr.split('-');
            myDate = new Date(arys[0], --arys[1], arys[2]);
        }
        return myDate;
    }

    common.replaceArrayAll = function (str, oldstrArray, newstr) {
        if (Array.isArray(oldstrArray)) {
            for (var i in oldstrArray) {
                try {
                    str = str.replaceAll(oldstrArray[i], newstr);
                } catch (e) {
                    console.log(oldstrArray[i]);
                }
            }
        }
        return str;
    }
    //新手指引再次封装  相当给力
    common.newNoviceIntroSteps = function (obj, newIntro) {
        var isNewIntro = newIntro ? true : false;
        var newIntro = newIntro;
        var $steps = obj.steps;
        if ($steps instanceof Array || $steps.length > 0) {
            for (var i = 0; i < $steps.length; i++) {
                if ($steps[i].control.imgSrc.indexOf('/Content/Images/noviceIntroPic/') == -1) {
                    $steps[i].control.imgSrc = '/Content/Images/noviceIntroPic/' + $steps[i].control.imgSrc
                }
            }
        } else {
            $steps = [];
        }
        var noviceIntroObj = {
            backgroundColor: obj.backgroundColor ? obj.backgroundColor : "#000",
            opacity: obj.opacity ? obj.opacity : 0.7,
            isStartButton: obj.isStartButton ? obj.isStartButton : false,
            startButtonTitle: obj.startButtonTitle ? obj.startButtonTitle : '',
            startButtonTop: obj.startButtonTop ? obj.startButtonTitle : 0,
            startButtonLeft: obj.startButtonLeft ? obj.startButtonLeft : 0,
            callBack: typeof obj.callBack == "function" ? obj.callBack : function () {
                if (isNewIntro) {
                    commonModule.SaveCommonSetting(newIntro, "1", function (rsp) { });
                }
            },
            steps: $steps
        };
        if (isNewIntro) {
            commonModule.LoadCommonSetting(newIntro, true, function (rsp) {
                if (rsp.Success && rsp.Data != "1") {
                    var newFun = "newFun" + new Date().getTime();
                    var newFun = new noviceIntro();
                    newFun.initData(noviceIntroObj);

                }
            });
        }
    }

    //导航切换
    common.navActive = function (ele, callBack) {
        $(ele).on("click", function (e) {
            if (e.stopPropagation) {
                e.stopPropagation();
                e.preventDefault();
            } else {
                window.event.returnValue = false;
                window.event.cancelBubble = true;
            };
            var tarEle = e.target;
            var parentEle = tarEle.parentNode ? tarEle.parentNode : null;
            var tarEleName = tarEle.nodeName.toLocaleUpperCase();
            var parentEleName = parentEle.nodeName.toLocaleUpperCase();
            if (tarEleName == "LI" || parentEleName == "LI") {
                if (tarEleName == "LI") {
                    $(ele + ">li").removeClass("active");
                    $(tarEle).addClass("active");
                } else if (parentEleName == "LI") {
                    $(ele + ">li").removeClass("active");
                    $(tarEle).parent().addClass("active");
                }
                $(ele + ">li").each(function (index, item) {
                    if ($(item).hasClass("active")) {
                        if (callBack || typeof callBack == "function") {
                            callBack(index, item)
                        }
                    }
                })
            }
        })
    }

    //币种转换
    common.CurrencyToCharacter = function (currency) {
        var currstr = "";
        if (currency == undefined || currency == "") {
            return currstr;
        }
        var currencySymbols = {
            'VND': '₫', // 越南盾
            'MYR': 'RM', // 马来西亚林吉特
            'SGD': 'S$', // 新加坡元
            'PHP': '₱', // 菲律宾比索
            'THB': '฿', // 泰铢
            'GBP': '£', // 英镑
            'USD': '$', // 美元
            'CNY': '¥'  // 人民币
        };
        currstr = currencySymbols[currency];
        return currstr;
    }

    return common;
}(commonModule || {}, jQuery, layer));


/*---------------String原型扩展 BEGIN ---------------*/
String.prototype.trim = function () {
    var str = this.replace(/(^\s*)|(\s*$)/g, '');
    return str;
}

//去除字符串头部空格或指定字符
String.prototype.trimStartDgj = function (c) {
    if (c == null || c == "") {
        var str = this.replace(/^\s*/, '');
        return str;
    }
    else {
        var rg = new RegExp("^" + c + "*");
        var str = this.replace(rg, '');
        return str;
    }
}
//去除字符串尾部空格或指定字符
String.prototype.trimEndDgj = function (c) {
    if (c == null || c == "") {
        var str = this;
        var rg = /\s/;
        var i = str.length;
        while (rg.test(str.charAt(--i)));
        return str.slice(0, i + 1);
    }
    else {
        var str = this;
        var rg = new RegExp(c);
        var i = str.length;
        while (rg.test(str.charAt(--i)));
        return str.slice(0, i + 1);
    }
}

// 正则替换所有字符
//String.prototype.replaceAll = function (oldstr, newstr) {
//    var reg = new RegExp(oldstr, "g"); //创建正则RegExp对象   
//    if (oldstr == ".")
//        reg = new RegExp(/\./, "g");
//    else if (oldstr == "$")
//        reg = new RegExp(/\$/, "g");
//    else if (oldstr == "*")
//        reg = new RegExp(/\*/, "g");
//    else if (oldstr == "+")
//        reg = new RegExp(/\+/, "g");
//    else if (oldstr == "[")
//        reg = new RegExp(/\[/, "g");
//    else if (oldstr == "]")
//        reg = new RegExp(/\]/, "g");
//    else if (oldstr == "?")
//        reg = new RegExp(/\?/, "g");
//    else if (oldstr == "\\")
//        reg = new RegExp(/\\/, "g");
//    else if (oldstr == "^")
//        reg = new RegExp(/\^/, "g");
//    else if (oldstr == "{")
//        reg = new RegExp(/\{/, "g");
//    else if (oldstr == "}")
//        reg = new RegExp(/\}/, "g");
//    else if (oldstr == "|")
//        reg = new RegExp(/|}/, "g");
//    return this.replace(reg, newstr);
//}

// 正则替换所有字符
String.prototype.replaceAll = function (oldstr, newstr) {
    return this.split(oldstr).join(newstr);
}

//计算字符串长度(英文占1个字符，中文汉字占2个字符)
String.prototype.gblen = function () {
    var len = 0;
    for (var i = 0; i < this.length; i++) {
        if (this.charCodeAt(i) > 127 || this.charCodeAt(i) == 94) {
            len += 2;
        } else {
            len++;
        }
    }
    return len;
}
/*--------------- String原型扩展 END ---------------*/


/*---------------Date原型扩展 BEGIN ---------------*/

/*日期时间脚本库方法列表：
Date.prototype.isLeapYear 判断闰年
Date.prototype.Format 日期格式化
Date.prototype.Format2 日期格式化
Date.prototype.DateAdd 日期计算
Date.prototype.DateDiff 比较日期差
Date.prototype.toString 日期转字符串
Date.prototype.toArray 日期分割为数组
Date.prototype.DatePart 取日期的部分信息
Date.prototype.MaxDayOfDate 取日期所在月的最大天数
Date.prototype.WeekNumOfYear 判断日期所在年的第几周
StringToDate 字符串转日期型
IsValidDate 验证日期有效性
CheckDateTime 完整日期时间检查
daysBetween 日期天数差
*/

//---------------------------------------------------
// 对Date的扩展，将 Date 转化为指定格式的String   
// 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，   
// 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)   
// 例子：   
// (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423   
// (new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18  
//---------------------------------------------------
Date.prototype.Format = function (fmt) { //author: meizz   
    fmt = !fmt ? "yyyy-MM-dd hh:mm:ss" : fmt;
    var o = {
        "M+": this.getMonth() + 1,               //月份   
        "d+": this.getDate(),                    //日   
        "h+": this.getHours(),                   //小时   
        "m+": this.getMinutes(),                 //分   
        "s+": this.getSeconds(),                 //秒   
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度   
        "S": this.getMilliseconds()             //毫秒   
    };
    if (/(y+)/.test(fmt))
        fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt))
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}

//---------------------------------------------------
// 日期格式化
// 格式 YYYY/yyyy/YY/yy 表示年份
// MM/M 月份
// W/w 星期
// dd/DD/d/D 日期
// hh/HH/h/H 时间
// mm/m 分钟
// ss/SS/s/S 秒
//---------------------------------------------------
Date.prototype.Format2 = function (formatStr) {
    var str = formatStr;
    var Week = ['日', '一', '二', '三', '四', '五', '六'];

    str = str.replace(/yyyy|YYYY/, this.getFullYear());
    str = str.replace(/yy|YY/, (this.getYear() % 100) > 9 ? (this.getYear() % 100).toString() : '0' + (this.getYear() % 100));

    str = str.replace(/MM/, this.getMonth() > 9 ? this.getMonth().toString() : '0' + this.getMonth());
    str = str.replace(/M/g, this.getMonth());

    str = str.replace(/w|W/g, Week[this.getDay()]);

    str = str.replace(/dd|DD/, this.getDate() > 9 ? this.getDate().toString() : '0' + this.getDate());
    str = str.replace(/d|D/g, this.getDate());

    str = str.replace(/hh|HH/, this.getHours() > 9 ? this.getHours().toString() : '0' + this.getHours());
    str = str.replace(/h|H/g, this.getHours());
    str = str.replace(/mm/, this.getMinutes() > 9 ? this.getMinutes().toString() : '0' + this.getMinutes());
    str = str.replace(/m/g, this.getMinutes());

    str = str.replace(/ss|SS/, this.getSeconds() > 9 ? this.getSeconds().toString() : '0' + this.getSeconds());
    str = str.replace(/s|S/g, this.getSeconds());

    return str;
}

//---------------------------------------------------
// 判断闰年
//---------------------------------------------------
Date.prototype.isLeapYear = function () {
    return (0 == this.getYear() % 4 && ((this.getYear() != 0) || (this.getYear() == 0)));
}

////+---------------------------------------------------
////| 求两个时间的天数差 日期格式为 YYYY-MM-dd
////+---------------------------------------------------
//function daysBetween(DateOne, DateTwo) {
//    var OneMonth = DateOne.substring(5, DateOne.lastIndexOf('-'));
//    var OneDay = DateOne.substring(DateOne.length, DateOne.lastIndexOf('-') + 1);
//    var OneYear = DateOne.substring(0, DateOne.indexOf('-'));

//    var TwoMonth = DateTwo.substring(5, DateTwo.lastIndexOf('-'));
//    var TwoDay = DateTwo.substring(DateTwo.length, DateTwo.lastIndexOf('-') + 1);
//    var TwoYear = DateTwo.substring(0, DateTwo.indexOf('-'));

//    var cha = ((Date.parse(OneMonth + '/' + OneDay + '/' + OneYear) - Date.parse(TwoMonth + '/' + TwoDay + '/' + TwoYear)) / 86400000);
//    return Math.abs(cha);
//}


//+---------------------------------------------------
//| 日期计算
//+---------------------------------------------------
Date.prototype.DateAdd = function (strInterval, Number) {
    var dtTmp = this;
    switch (strInterval) {
        case 's': return new Date(Date.parse(dtTmp) + (1000 * Number));
        case 'n': return new Date(Date.parse(dtTmp) + (60000 * Number));
        case 'h': return new Date(Date.parse(dtTmp) + (3600000 * Number));
        case 'd': return new Date(Date.parse(dtTmp) + (86400000 * Number));
        case 'w': return new Date(Date.parse(dtTmp) + ((86400000 * 7) * Number));
        case 'q': return new Date(dtTmp.getFullYear(), (dtTmp.getMonth()) + Number * 3, dtTmp.getDate(), dtTmp.getHours(), dtTmp.getMinutes(), dtTmp.getSeconds());
        case 'm': return new Date(dtTmp.getFullYear(), (dtTmp.getMonth()) + Number, dtTmp.getDate(), dtTmp.getHours(), dtTmp.getMinutes(), dtTmp.getSeconds());
        case 'y': return new Date((dtTmp.getFullYear() + Number), dtTmp.getMonth(), dtTmp.getDate(), dtTmp.getHours(), dtTmp.getMinutes(), dtTmp.getSeconds());
    }
}

//+---------------------------------------------------
//| 比较日期差 dtEnd 格式为日期型或者 有效日期格式字符串
//+---------------------------------------------------
Date.prototype.DateDiff = function (strInterval, dtEnd) {
    var dtStart = this;
    if (typeof dtEnd == 'string')//如果是字符串转换为日期型
    {
        dtEnd = StringToDate(dtEnd);
    }
    switch (strInterval) {
        case 's': return parseInt((dtEnd - dtStart) / 1000);
        case 'n': return parseInt((dtEnd - dtStart) / 60000);
        case 'h': return parseInt((dtEnd - dtStart) / 3600000);
        case 'd': return parseInt((dtEnd - dtStart) / 86400000);
        case 'w': return parseInt((dtEnd - dtStart) / (86400000 * 7));
        case 'm': return (dtEnd.getMonth() + 1) + ((dtEnd.getFullYear() - dtStart.getFullYear()) * 12) - (dtStart.getMonth() + 1);
        case 'y': return dtEnd.getFullYear() - dtStart.getFullYear();
    }
}

//+---------------------------------------------------
//| 日期合法性验证
//| 格式为：YYYY-MM-DD或YYYY/MM/DD
//+---------------------------------------------------
//function IsValidDate(DateStr) {
//    var sDate = DateStr.replace(/(^\s+|\s+$)/g, ''); //去两边空格;
//    if (sDate == '') return true;
//    //如果格式满足YYYY-(/)MM-(/)DD或YYYY-(/)M-(/)DD或YYYY-(/)M-(/)D或YYYY-(/)MM-(/)D就替换为''
//    //数据库中，合法日期可以是:YYYY-MM/DD(2003-3/21),数据库会自动转换为YYYY-MM-DD格式
//    var s = sDate.replace(/[\d]{ 4,4 }[\-/]{ 1 }[\d]{ 1,2 }[\-/]{ 1 }[\d]{ 1,2 }/g, '');
//    if (s == '') //说明格式满足YYYY-MM-DD或YYYY-M-DD或YYYY-M-D或YYYY-MM-D
//    {
//        var t = new Date(sDate.replace(/\-/g, '/'));
//        var ar = sDate.split(/[-/:]/);
//        if (ar[0] != t.getYear() || ar[1] != t.getMonth() + 1 || ar[2] != t.getDate()) {
//            //alert('错误的日期格式！格式为：YYYY-MM-DD或YYYY/MM/DD。注意闰年。');
//            return false;
//        }
//    }
//    else {
//        //alert('错误的日期格式！格式为：YYYY-MM-DD或YYYY/MM/DD。注意闰年。');
//        return false;
//    }
//    return true;
//}

////+---------------------------------------------------
////| 日期时间检查
////| 格式为：YYYY-MM-DD HH:MM:SS
////+---------------------------------------------------
//function CheckDateTime(str) {
//    var reg = /^(\d+)-(\d{ 1,2 })-(\d{ 1,2 }) (\d{ 1,2 }):(\d{ 1,2 }):(\d{ 1,2 })$/;
//    var r = str.match(reg);
//    if (r == null) return false;
//    r[2] = r[2] - 1;
//    var d = new Date(r[1], r[2], r[3], r[4], r[5], r[6]);
//    if (d.getFullYear() != r[1]) return false;
//    if (d.getMonth() != r[2]) return false;
//    if (d.getDate() != r[3]) return false;
//    if (d.getHours() != r[4]) return false;
//    if (d.getMinutes() != r[5]) return false;
//    if (d.getSeconds() != r[6]) return false;
//    return true;
//}

//+---------------------------------------------------
//| 把日期分割成数组
//+---------------------------------------------------
Date.prototype.toArray = function () {
    var myDate = this;
    var myArray = Array();
    myArray[0] = myDate.getFullYear();
    myArray[1] = myDate.getMonth();
    myArray[2] = myDate.getDate();
    myArray[3] = myDate.getHours();
    myArray[4] = myDate.getMinutes();
    myArray[5] = myDate.getSeconds();
    return myArray;
}

//+---------------------------------------------------
//| 取得日期数据信息
//| 参数 interval 表示数据类型
//| y 年 m月 d日 w星期 ww周 h时 n分 s秒
//+---------------------------------------------------
Date.prototype.DatePart = function (interval) {
    var myDate = this;
    var partStr = '';
    var Week = ['日', '一', '二', '三', '四', '五', '六'];
    switch (interval) {
        case 'y': partStr = myDate.getFullYear(); break;
        case 'm': partStr = myDate.getMonth() + 1; break;
        case 'd': partStr = myDate.getDate(); break;
        case 'w': partStr = Week[myDate.getDay()]; break;
        case 'ww': partStr = myDate.WeekNumOfYear(); break;
        case 'h': partStr = myDate.getHours(); break;
        case 'n': partStr = myDate.getMinutes(); break;
        case 's': partStr = myDate.getSeconds(); break;
    }
    return partStr;
}

//+---------------------------------------------------
//| 取得当前日期所在月的最大天数
//+---------------------------------------------------
Date.prototype.MaxDayOfDate = function () {
    var myDate = this;
    var ary = myDate.toArray();
    var date1 = (new Date(ary[0], ary[1] + 1, 1));
    var date2 = date1.dateAdd(1, 'm', 1);
    var result = dateDiff(date1.Format('yyyy-MM-dd'), date2.Format('yyyy-MM-dd'));
    return result;
}

//+---------------------------------------------------
//| 取得当前日期所在周是一年中的第几周
//+---------------------------------------------------
Date.prototype.WeekNumOfYear = function () {
    var myDate = this;
    var ary = myDate.toArray();
    var year = ary[0];
    var month = ary[1] + 1;
    var day = ary[2];
    document.write('< script language=VBScript\> \n');
    document.write('myDate = DateValue("' + month + ' - ' + day + ' - ' + year + '") \n');
    document.write('result = DatePart("ww", myDate) \n');
    document.write(' \n');
    return result;
}

////+---------------------------------------------------
////| 字符串转成日期类型
////| 格式 MM/dd/YYYY MM-dd-YYYY YYYY/MM/dd YYYY-MM-dd
////+---------------------------------------------------
//function StringToDate(DateStr) {

//    var converted = Date.parse(DateStr);
//    var myDate = new Date(converted);
//    if (isNaN(myDate)) {
//        //var delimCahar = DateStr.indexOf('/')!=-1?'/':'-';
//        var arys = DateStr.split('-');
//        myDate = new Date(arys[0], --arys[1], arys[2]);
//    }
//    return myDate;
//}

/*--------------- Date原型扩展 END ---------------*/


//Object.prototype.clone = function () { var newObj = {}; for (var i in this) { if (typeof (this[i]) == 'object' || typeof (this[i]) == 'function') { newObj[i] = this[i].clone(); } else { newObj[i] = this[i]; } } return newObj; };

//Array.prototype.clone = function () { var newArray = []; for (var i = 0; i < this.length; i++) { if (typeof (this[i]) == 'object' || typeof (this[i]) == 'function') { newArray[i] = this[i].clone(); } else { newArray[i] = this[i]; } } return newArray; };

//Function.prototype.clone = function () { var that = this; var newFunc = function () { return that.apply(this, arguments); }; for (var i in this) { newFunc[i] = this[i]; } return newFunc; };