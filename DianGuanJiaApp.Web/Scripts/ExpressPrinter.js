/// <reference path="CommonModule.js" />
/// <reference path="LodopPrinter.js" />
/// <reference path="orderlist/OrderTableBuilder.js" />
/// <reference path="CaiNiaoPrinter.js" />

//快递单打印组件
var expressPrinter = (function (ep, common, caiNiao, pdd, lp, tmpModule, otb, $) {

    var printComponents = '';
    ep.SetPrintComponents = function SetPrintComponents(componentName) {
        printComponents = componentName;
    }

    function checkTemplateIsNull() {
        if ($("input[name='rdo_print_template']").length == 0) {
            layer.alert('您还没有添加快递模板，请点击<span id="sp_add_template" class="orderList_expressTemplate_addIcon"></span>添加快递模板');
            return false;
        }
    }

    //检查是否有默认发件人。
    ep.isExistSellerInfo = function (callback) {
        $("#isMoveNotSellerIntoDivId").hide();
        $("#isMoveSellerInfoShopName").html("");
        $("#isOneSellerInfoShopName").hide();
        $("#isOneSellerInfoShopName").html("");
        common.ajax({
            type: 'post',
            url: '/Order/GetDefaultSellerInfo',
            data: {},
            success: function (json) {
                if (json.Success) {
                    var dates = json.Data;
                    if (dates.IsShow) {
                        var list = dates.ListModel;
                        var str = "";
                        if (list.length > 1) {
                            for (var i = 0; i < list.length; i++) {
                                if (list[i].ShopName)
                                    str += list[i].ShopName + " ";
                            }
                            $("#isMoveSellerInfoShopName").html(str);
                            $("#isMoveNotSellerIntoDivId").show();
                        } else {
                            if (list[0].ShopName)
                                str = "店铺（" + list[0].ShopName + "）";
                            else
                                str = "店铺";
                            $("#isOneSellerInfoShopName").show();
                            $("#isOneSellerInfoShopName").html(str);
                        }

                        var addSellerDialog = layer.open({
                            type: 1,
                            title: "添加默认发件人",
                            content: $('#add-defaultseller-div-show'),
                            area: ['1200', '300'], //宽高
                            btn: ['保存', '取消'],
                            success: function () {
                                function selectCallBack(control) {
                                    var deep = control.attr('deep');

                                    if (deep > 1) {
                                        var dataValue = control.attr("data-value");
                                        var isExistsVal = control.find("option[value='" + dataValue + "']").length;
                                        if (isExistsVal > 0)
                                            control.val(dataValue).trigger('change');
                                    }
                                }
                                //加载地址级联选择
                                commonModule.LoadAreaInfoToControl('selDefaultPro', 1, function () {
                                }, selectCallBack, "name");
                                //清空地址识别框
                                $('#txt_raw_addr_default').val('');
                            },
                            cancel: function () {
                                //$('#add-defaultseller-div-show').hide();
                                if (callback)
                                    callback(false);
                                layer.close(addSellerDialog);
                            },
                            yes: function () {
                                AddSellerInfo(addSellerDialog, list);
                                if (callback)
                                    callback(true);
                            },
                            btn2: function () {
                                if (callback)
                                    callback(false);
                                layer.close(addSellerDialog);
                                //$('#add-defaultseller-div-show').hide();
                            }
                        });
                    }
                    else {
                        if (callback)
                            layer.msg("您已经设置了发件人地址信息了。");
                    }
                }
            }
        });
    }

    //默认发件人弹窗
    function AddSellerInfo(addSellerDialog, list) {
        var companyName = $('#txtDefaultCompanyName').val().trim();
        var sellerName = $('#txtDefaultSellerName').val().trim();
        var sellerMobile = $('#txtDefaultSellerMobile').val().trim();
        var sellerPhone = $('#txtDefaultSellerPhone').val().trim();
        //var sellerAddress = $('#txtDefaultSellerAddress').val().trim();

        var sellerProvince = $('#selDefaultPro').val();
        var sellerCity = $('#selDefaultCity').val();
        var sellerArea = $('#selDefaultArea').val();
        var sellerDetailAddr = $('#txtDetailAddr').val().trim();


        if (sellerName == '') {
            layer.msg('请填写发件人姓名.');
            $('#txtDefaultSellerName').focus();
            return;
        }

        if (sellerMobile == '') {
            layer.msg('请填写发件人手机号.');
            $('#txtDefaultSellerMobile').focus();
            return;
        }

        var sellerAddr = '';

        if (sellerProvince && sellerProvince != "0") {
            sellerAddr += sellerProvince;
        }
        else {
            layer.msg('请选择省份.');
            return;
        }

        if (sellerCity && sellerCity != "0") {
            sellerAddr += sellerCity;
        }
        else {
            layer.msg('请选择城市.');
            return;
        }

        if (sellerArea && sellerArea != "0") {
            sellerAddr += sellerArea;
        }
        else {
            layer.msg('请选择区域.');
            return;
        }

        sellerAddr += sellerDetailAddr;

        common.Ajax({
            url: '/SellerInfo/AddDefaultSeller',
            loading: true,
            data: { companyName: companyName, sellerName: sellerName, sellerMobile: sellerMobile, sellerPhone: sellerPhone, sellerAddress: sellerAddr },
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                if (otb) {
                    //重新渲染发件人
                    common.Foreach(otb.rows, function (i, o) {
                        common.Foreach(list, function (x, y) {
                            if (o.ShopId == y.ShopId) {
                                o.SenderName = sellerName;
                                o.SenderPhone = sellerPhone;
                                o.SenderMobile = sellerMobile;
                                o.SenderAddress = sellerAddr;
                                $('#txtSeller_' + o.Id).val(sellerName);
                                $('#txtSeller_phone_' + o.Id).val(sellerMobile);
                                $('#txtSeller_address_' + o.Id).val(sellerAddr);
                                return 'break';
                            }
                        });
                    });
                    otb.refreshColumn('SenderName');
                }
                layer.msg('保存成功');
                layer.close(addSellerDialog);
            }
        });
    }

    //打印前检查模板
    ep.CheckTemplateAvaliable = function (template, printCount) {

        var needCheck = false;
        if (common.IsNormalTemplate(template.TemplateType) == false && common.IsJdKdTemplate(template.TemplateType) == false
            && template.CpType != 1 && template.CpType != 4) {
            needCheck = true;
        }

        if (needCheck == false) {
            return common.Ajax({
                url: '/TemplateSet/GetDayPrintBatchNumber',
                type: 'POST',
                async: true,
                success: function (rsp) {
                    if (rsp.Success) {
                        commonModule.ServerNowStr = rsp.Data.DateNow;
                        commonModule.DateBatch = rsp.Data.DayPrintBatchNumber;
                    }
                }
            });
        }

        return common.Ajax({
            url: '/TemplateSet/CheckTemplateAvaliable',
            data: {
                templateId: template.Id,
                printCount: printCount
            },
            //loadingMessage: '检查模板网点及单号余额',
            type: 'POST',
            async: true,
            success: function (rsp) {
                if (rsp.Success == false) {
                    var errorCode = rsp.Message;
                    var errorData = rsp.Data;
                    var errorMsg = "";
                    switch (errorCode) {
                        case "1":
                            errorMsg = "模板绑定的电子面单账号未获取到网点，请检查模板绑定的电子面单授权是否过期，或重新授权。<label onclick='commonModule.transferUrl(\"/TemplateSet/\",\"_blank\")' style='color:blue;cursor:pointer;'>去模板列表查看</label>";
                            break;
                        case "2":
                            errorMsg = "模板绑定的电子面单账号网点不存在，请前往商家后台，开通电子面单的地方检查网点【" + errorData.BranchName + "】是否已经取消。";
                            break;
                        case "3":
                            errorMsg = "模板绑定的电子面单账号单号余额【" + errorData.Balance + "】不足以打印【" + errorData.PrintCount + "】个单号，请充值单号。";
                            break;
                        default:
                    }
                    if (errorMsg == "")
                        layer.alert(rsp.Message);
                    else
                        layer.alert(errorMsg);
                }
                else {
                    commonModule.ServerNowStr = rsp.Data.DateNow;
                    commonModule.DateBatch = rsp.Data.DayPrintBatchNumber;
                }
            }
        });
    }

    //获取模板类型信息
    function getTemplateTypeInfo(template) {
        var info = {};
        var type = template.TemplateType;
        var company = template.CompanyCode;
        var def = type != null && type != undefined && type != '';
        info.IsWayComBill = def && type == 4; //是否固定模版菜鸟电子面单
        info.IsRMWayComBill = def && (type == 5 || type == 6); //是否组件模版菜鸟电子面单
        info.IsJDkd = def && (type > 80 || type < 90); //是否京东快递
        info.IsWayBill = def && type == 2;//是否菜鸟电子面单除了固定模版
        info.IsWaySite = def && (type == 3 && company != "YUNDA");//是否网点电子面单除了韵达
        info.IsAllWaySite = def && type == 3;//是否网点电子面单
        info.IsWayStoSite = def && (type == 3 && company == "STO");//是否申通网点电子面单
        info.IsAn56WaySite = def && type == 3 && company == "2608021499_235";//是否安能网点电子面单
        info.IsWayYundaSite = def && type == 3 && company == "YUNDA";//是否韵达网点电子面单
        return info;
    }

    //获取模板打印数据（通用方法）
    function getPrintData(orders, template) {
        var request = { Orders: orders, PrintTemplate: template };
        var tinfo = getTemplateTypeInfo(template);
    }

    //打印过的订单，再次打印提示用户
    ep.checkPrinted = function (orders, printMethod, template, isScanPrint) {
        //取出模板bind的打印机（上次打印的打印机）
        var dp = common.GetDefaultPrinter(template.Id, 1);
        var printerName = '';
        if (dp) {
            printerName = dp.PrinterName;
            ep.defaultPrinter = printerName;
        }

        //打印过的订单,打印提示
        //前提条件是：只有正常（Normal）打印和快运打印才会提示用户，一单多包和新单号打印不会提示用户。
        //1.普通面单/网点面单
        //  提示用户确认，是否重新打印
        //2.菜鸟面单
        //  提示用户确认，是否用原单号打印，或者选择一单多包，新单号打印
        //3.快运面单
        //  快运面单打印过，提示用户先回收单号。重打会报错，不管指定的字母单号数量是否一样。（需验证？？）
        if (printMethod != "NewPrint" || printMethod != "RePrint") {
            var printedWaybillCodes = []; //打印过的单号集合，用于快运提示
            var isExistPrintedOrder = false; //是否存在打印过的订单
            var isExistPrintedWaybillCode = false; //是否找到了打印过的面单
            common.Foreach(orders, function (i, o) {
                var order = isScanPrint ? o : otb.rows[o.Index];
                if (order.LastExpressPrintTime) {
                    isExistPrintedOrder = true; //订单打印过
                    if (template.TemplateType == 1) {//common.IsNormalTemplate(template.TemplateType)//template.TemplateType == 1 || template.TemplateType == 3 || template.TemplateType == 10) {
                        return 'break';
                    }
                    else {
                        common.Foreach(order.WaybillCodes, function (idx, wc) {
                            if (wc.TemplateId == template.Id || (wc.TemplateType == template.TemplateType && wc.ExpressCpCode == template.ExpressCompanyCode)) {
                                isExistPrintedWaybillCode = true; //找到了打印过的运单号
                                printedWaybillCodes.push(wc.WaybillCode);
                                return 'break';
                            }
                        });
                        if (isExistPrintedWaybillCode && printMethod != 'PrintKuaiYun') return 'break;';
                    }
                }
            });
            if (isExistPrintedOrder == false) {
                return true;
            }

            //弹出用户确认提示
            if (template.TemplateType == 1) { //common.IsNormalTemplate(template.TemplateType)
                //传统面单和网点面单重打提示
                layer.open({
                    type: 1,
                    title: "打印确认", //不显示标题
                    content: $('#div_rePrint_tradition_site'),
                    area: ['570px', '250px'], //宽高
                    btn: ['确定重新打印'],
                    yes: function () {
                        //ep.doPrint(orders, template, printMethod, 1, false, printerName);
                        ep.print(orders, printMethod, 1, false);
                    }
                });
            }
            else if (common.IsKuaiYunTemplate(template.TemplateType)) {
                //if (isExistPrintedWaybillCode == true) { //找打了打印过的运单号
                //    //快运面单重打提示
                //    layer.alert("快运面单不支持重复打印。<br/>请先取消已打印过的快运面单【" + printedWaybillCodes.join(',') + "】");
                //}
                //else {
                layer.open({
                    type: 1,
                    title: "打印确认", //不显示标题
                    content: $('#div_rePrint_KuaiYun'),
                    area: ['570px', '250px'], //宽高
                    btn: ['确定重新打印', '新单号打印'],
                    yes: function () {
                        ep.print(orders, printMethod, 1, false);
                    },
                    btn2: function () {
                        ep.print(orders, 'NewPrint', 1, false);
                        return false;
                    }
                });
                //}
            }
            else {
                if (isExistPrintedWaybillCode == true) { //找打了打印过的运单号
                    //取消按钮
                    $('#div_rePrint_cainiao_cancel').unbind('click').bind('click', function () {
                        layer.closeAll();
                    });
                    //数量选择
                    $('#ul_reprint_confirm_count li').bind('click', function () {
                        var li = $(this);
                        $('#lbl_rePrint_count').text(li.text());
                        $('#txt_reprint_confirm_count').val(li.text());
                    });
                    //数量输入
                    $('#txt_reprint_confirm_count').unbind('input').bind('input', function () {
                        var txt = $(this);
                        var val = $.trim(txt.val());
                        if (isNaN(val) == true || val == "0") {
                            val = 1;
                            txt.val(val);
                        }
                        if (val == "") {
                            val = 1;
                        }
                        $('#lbl_rePrint_count').text(val);
                    });
                    //原单号重打
                    $('#div_rePrint_cainiao_rePrint').unbind('click').bind('click', function () {
                        //ep.doPrint(orders, template, printMethod, 1, false, printerName);

                        layer.closeAll();

                        ep.print(orders, printMethod, 1, false);

                        //清空用户选择
                        $(".sureAgainPrint_main_down").hide();
                        $('#ul_reprint_confirm_count li').first().click();

                    });
                    //新单号打印
                    $('#div_rePrint_newId_reprint').unbind('click').bind('click', function () {
                        //ep.doPrint(orders, template, "NewPrint", $('#lbl_rePrint_count').text(), false, printerName, function () {
                        //    $(".sureAgainPrint_main_down").hide();
                        //    $('#ul_reprint_confirm_count li').first().click();
                        //});

                        layer.closeAll();

                        ep.print(orders, "NewPrint", $('#lbl_rePrint_count').text(), false);

                        //清空用户选择
                        $(".sureAgainPrint_main_down").hide();
                        $('#ul_reprint_confirm_count li').first().click();

                    });
                    //菜鸟面单重打提示
                    layer.open({
                        type: 1,
                        //skin: 'layui-layer-rim', //加上边框
                        title: "打印确认",
                        btn: null,
                        area: ['565px', '310px'],
                        content: $('#div_rePrint_cainiao'),
                        cancel: function () { }
                    });
                }
                else {
                    layer.open({
                        type: 1,
                        title: "打印确认", //不显示标题
                        content: $('#div_rePrint_tradition_site'),
                        area: ['570px', '250px'], //宽高
                        btn: ['确定重新打印'],
                        yes: function () {
                            ep.print(orders, printMethod, 1, false);
                        }
                    });
                }
            }
            return false;
        }
        else {
            return true;
        }
    }

    //打印前检查
    ep.check = function (orders, template, checkTemplateIsNull, printMethod, printCount, isCheckPrinted, isByHand, isScanPrint) {
        //1.根据模板类型确认检查菜鸟打印组件还是Lodop打印组件
        //TemplateType=1 普通面单   （有背景图的自定义模板）
        //TemplateType=3 自画的网点电子面单模版    (IsWaySite)
        //2/4/5/6 都是菜鸟模板
        //TemplateType=2 菜鸟模版由系统定制的 (IsWayBill)
        //TemplateType=4 菜鸟原始固定模版   (IsWayComBill)--已经废弃掉？
        //TemplateType=5和6 菜鸟最新的模版   (IsRMWayComBill)
        if (!orders || orders.length == 0) {
            layer.alert("请选择您要打印的订单");
            return false;
        }
        if (typeof checkTemplateIsNull == 'function') {
            checkTemplateIsNull();
        }
        if (template == null) {

            layer.alert("请选择您要打印的快递模板");
            return false;

        }

        //aliC2M 限制只能用菜鸟订单
        if (commonModule.CurrShop.PlatformType == "AlibabaC2M"
            && common.IsCainiaoTemplate(template.TemplateType) == false
            && common.IsLinkTemplate(template.TemplateType) == false) {
            layer.alert("淘宝平台要求，淘工厂订单只支持菜鸟电子面单打印，请更换菜鸟模板打印！");
            return false;
        }
        if (commonModule.CurrShop.PlatformType == "TaobaoMaiCaiV2"
            && common.IsCainiaoTemplate(template.TemplateType) == false
            && common.IsLinkTemplate(template.TemplateType) == false) {
            layer.alert("淘宝买菜（新）平台要求，淘工厂订单只支持菜鸟电子面单打印，请更换菜鸟模板打印！");
            return false;
        }
        //拼多多厂家代打，只能使用自己的电子面单账号打印
        if (commonModule.IsPddFds()) {
            if (commonModule.IsPddTemplate(template.TemplateType) == false) {
                layer.alert("拼多多代发订单，只支持拼多多电子面单打印，请更换拼多多模板打印。");
                return false;
            }
            if (template.AuthSourceType != "1" && template.CaiNiaoAuthInfoId != common.CurrShop.Id) {
                layer.alert("拼多多代发订单，只能使用自己账号开通的拼多多电子面单打印,请更换绑定自己电子面单账号的模板！");
                return false;
            }
        }

        var templateType = template.TemplateType;
        //检查模板类型为2的模板，由于菜鸟做了电子面单一致性改造，接口返回的数据加了密，自己维护的类型为2的模板，打印不了了
        if (templateType == 2) {
            var urlstr = common.rewriteUrl('/TemplateSet/');
            layer.alert("您好，由于菜鸟官方对电子面单信息一致性做了校验，数据进行了加密，所以自己维护的菜鸟模板将取不到集包地等信息，请使用标准的菜鸟模板进行打印。<br/> <a href=" + urlstr + " style='color:blue;'>去模板管理>></a>");
            return false;
        }

        //勾选订单验证
        var riskCtrlOrder = []; //拼多多风控订单
        var sendSFOrder = []; //拼多多顺丰加价订单
        var touTiaoCodOrder = []; //头条系代收货款订单
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var rec = order.Receiver;
            var buyerName = order.Buyer.BuyerWangWang;
            if (!buyerName)
                buyerName = rec.ToName;
            //验证收件人
            //if (templateType != 1 && (!order.WaybillCode || order.WaybillCode == "" || order.WaybillCode.trim() == "")) {
            //    layer.alert("买家【" + buyerName + "】的快递单号不能为空");
            //    return false;
            //}

            if (order.PlatformType == "Pinduoduo" && order.IsWeiGong == true) {
                riskCtrlOrder.push(order.PlatformOrderId);
            }

            if (order.PlatformType == "Pinduoduo" && (order.ExtField1 == "1" || IsChildSendSF(order))) {
                sendSFOrder.push(order.PlatformOrderId);
            }

            if (order.PlatformType == "TouTiao" && order.TradeType == "0") {
                touTiaoCodOrder.push(order.PlatformOrderId);
            }

            //拼多多厂家代打订单，取单号不需要收件人信息
            if (common.IsPddFds() == false) {
                if (!rec || !rec.ToName || rec.ToName.trim(" ") == "") {
                    layer.alert("买家【" + buyerName + "】的收件人不能为空");
                    return false;
                }
                if (!rec.ToMobile || rec.ToMobile.trim(" ") == "") {
                    layer.alert("买家【" + buyerName + "】的收件人联系电话不能为空");
                    return false;
                }
                if (!rec.ToAddress || rec.ToAddress.trim(" ") == "") {
                    layer.alert("买家【" + buyerName + "】的收件地址不能为空");
                    return false;
                }
            }

            //var sender = order.Sender;
            //if (!sender || !sender.SenderName || sender.SenderName.trim(" ") == "") {
            //    layer.alert("买家【" + buyerName + "】的发件人不能为空");
            //    return false;
            //}
            //if (!sender.SenderPhone || sender.SenderPhone.trim(" ") == "") {
            //    layer.alert("买家【" + buyerName + "】的发件人联系电话不能为空");
            //    return false;
            //}
            //if (!sender.SenderAddress || sender.SenderAddress.trim(" ") == "") {
            //    layer.alert("买家【" + buyerName + "】的发件地址不能为空");
            //    return false;
            //}

            var sender = order.Sender;
            if (!sender || !sender.SenderName || sender.SenderName.trim(" ") == "" || !sender.SenderPhone || sender.SenderPhone.trim(" ") == "" || !sender.SenderAddress || sender.SenderAddress.trim(" ") == "") {
                //layer.alert("买家【" + buyerName + "】的发件地址不能为空");
                var hideOrShowElement = function (display) {
                    if (display == "none") {
                        //隐藏不需要的项
                        $('#li_CompanyName').hide();
                        $('#li_seller_phone').hide();
                        $('#li_seller_address_discern').hide();
                    }
                    else {
                        //还原显示不需要的项
                        $('#li_CompanyName').show();
                        $('#li_seller_phone').show();
                        $('#li_seller_address_discern').show();
                    }
                }
                //无发件人，则弹出设置发件人
                var addSellerInfLayer = layer.open({
                    type: 1,
                    title: "添加发货地址",
                    content: $('#div_add_sellerInfo'),
                    area: ['800'], //宽
                    btn: ['保存', '取消'],
                    success: function () {
                        //隐藏不需要的项
                        hideOrShowElement("none");

                        function selectCallBack(control) {
                            var deep = control.attr('deep');

                            if (deep > 1) {
                                var dataValue = control.attr("data-value");
                                var isExistsVal = control.find("option[value='" + dataValue + "']").length;
                                if (isExistsVal > 0)
                                    control.val(dataValue).trigger('change');
                            }
                        }

                        //加载地址级联选择
                        commonModule.LoadAreaInfoToControl('sellerProvince-select', 1, function () {
                        }, selectCallBack, "name");

                    },
                    end: function () {
                        //还原显示不需要的项
                        hideOrShowElement("block");
                    },
                    yes: function () {
                        //保存

                        var companyName = $('#txtCompanyName').val().trim();
                        var sellerName = $('#txtSellerName').val().trim();
                        var sellerMobile = $('#txtSellerMobile').val().trim();
                        var sellerPhone = $('#txtSellerPhone').val().trim();

                        var sellerProvince = $('#sellerProvince-select').val();
                        var sellerCity = $('#sellerCity-select').val();
                        var sellerArea = $('#sellerArea-select').val();
                        var sellerDetailAddr = $('#txtSellerDetailAddr').val().trim();


                        if (sellerName == '') {
                            layer.msg('请填写发件人姓名.');
                            return false;
                        }

                        if (sellerMobile == '') {
                            layer.msg('请填写发件人手机号.');
                            return false;
                        }

                        var sellerAddr = '';

                        if (sellerProvince && sellerProvince != "0") {
                            sellerAddr += sellerProvince;
                        }
                        else {
                            layer.msg('请选择省份.');
                            return false;
                        }

                        if (sellerCity && sellerCity != "0") {
                            sellerAddr += sellerCity;
                        }
                        else {
                            layer.msg('请选择城市.');
                            return false;
                        }

                        if (sellerArea && sellerArea != "0") {
                            sellerAddr += sellerArea;
                        }
                        else {
                            layer.msg('请选择区域.');
                            return false;
                        }

                        if (sellerDetailAddr == '') {
                            layer.msg('请填写发件人地址.');
                            return false;
                        }

                        sellerAddr += sellerDetailAddr;

                        commonModule.Ajax({
                            url: '/SellerInfo/AddDefaultSeller',
                            loading: true,
                            data: {
                                companyName: companyName,
                                sellerName: sellerName,
                                sellerMobile: sellerMobile,
                                sellerPhone: sellerPhone,
                                sellerAddress: sellerAddr
                            },
                            type: 'POST',
                            success: function (rsp) {
                                if (rsp.Success == false) {
                                    layer.msg(rsp.Message, { icon: 2 });
                                    return;
                                }
                                //自动填充发件人信息到订单列表
                                if (window.orderTableBuilder && orderTableBuilder.rows && orderTableBuilder.rows.length > 0) {
                                    commonModule.Foreach(orderTableBuilder.rows, function (i, o) {
                                        //只填充当前店铺的发件人为空的订单 o.ShopId == commonModule.CurrShop.Id &&
                                        if ((!o.SenderName || (!o.SenderMobile && !o.SenderPhone) || !o.SenderAddress)) {
                                            //完善数据
                                            o.SenderName = sellerName;
                                            o.SenderPhone = sellerPhone;
                                            o.SenderMobile = sellerMobile;
                                            o.SenderAddress = sellerAddr;
                                            o.IsManualUpdateSeller = true;
                                            //重新渲染行
                                            orderTableBuilder.refreshRow(o);
                                        }
                                    });
                                }

                                //填充打印的数据
                                commonModule.Foreach(orders, function (i, o) {
                                    var sender = o.Sender;
                                    if (!sender || !sender.SenderName || sender.SenderName.trim(" ") == "" || !sender.SenderPhone || sender.SenderPhone.trim(" ") == "" || !sender.SenderAddress || sender.SenderAddress.trim(" ") == "") {
                                        o.Sender = {
                                            SenderName: sellerName,
                                            SenderPhone: (sellerMobile ? sellerMobile : sellerPhone),
                                            SenderAddress: sellerAddr
                                        };
                                    }
                                });

                                //关闭此窗口，显示提示窗口
                                layer.close(addSellerInfLayer);

                                //继续打印流程
                                if (isScanPrint) {
                                    ep.scanPrint(orders, template, printCount, isByHand, isCheckPrinted);
                                }
                                else {
                                    ep.startPrint(orders, printMethod, printCount, isCheckPrinted);
                                }
                            }
                        });
                    },
                    btn2: function () {
                        //取消

                        //还原显示不需要的项
                        hideOrShowElement("block");
                        //关闭此窗口，显示提示窗口
                        layer.close(addSellerInfLayer);
                    }
                });
                return false;
            }

            var oi = order.OrderItems;
            //检查订单项是否有勾选
            if (oi.length == 0 && !common.isCustomerOrder()) {
                layer.alert("买家【" + buyerName + "】中的商品没有勾选，请展开勾选.");
                return false;
            }
        }

        if (touTiaoCodOrder.length > 0
            && common.IsCainiaoTemplate(templateType) == false //菜鸟
            && common.IsKuaiYunTemplate(templateType) == false //菜鸟快运
            && common.IsLinkTemplate(templateType) == false    //link 
            && common.IsPddTemplate(templateType) == false     //pdd
            && templateType != 10) { //丰桥
            layer.alert("选中的订单【" + touTiaoCodOrder.join(",") + "】为代收货款订单，请更换菜鸟或拼多多模板打印。");
            return false;
        }

        if (riskCtrlOrder.length > 0) {
            layer.alert("订单【" + riskCtrlOrder.join(",") + "】风控中，不允许打印面单！<label style='color:red'>当风控解除后，请及时发货。</albel>");
            return false;
        }

        if (sendSFOrder.length > 0 && template.ExpressCompanyCode != "SF") {
            if (sendSFOrder.length == orders.length) {
                layer.alert("所选订单为“加价发顺丰”订单，请使用顺丰快递打印面单。");
                return false;
            }
            else {
                layer.alert("订单【" + sendSFOrder.join(",") + "】为“加价发顺丰”订单，请使用顺丰快递打印面单。");
                return false;
            }
        }


        //检查打印组件是否准备好
        var isReady = false;
        if (common.IsCainiaoTemplate(templateType) || common.IsLinkTemplate(templateType))//(templateType > 3 && templateType < 20) || templateType == 2)
            isReady = caiNiao.check(true);
        else if (common.IsPddTemplate(templateType))//(templateType > 20 && templateType < 40)
            isReady = pdd.check(true); //TODO:检查拼多多组件是否启动
        else
            isReady = lp.check(true);
        return isReady;
    }

    function IsChildSendSF(order) {
        var result = false;
        if (order.PlatformOrderId[0] == "C") {
            var row = orderTableBuilder.rows[order.Index];
            common.Foreach(row.SubOrders, function (i, subo) {
                if (subo.ExtField1 == "1") {
                    result = true;
                }
            });
        }
        return result;
    }

    //打印快递单，参数 orders:选择的订单，

    //预览
    ep.preView = function () {

    }
    //printMethod 打印方式
    //printcount 打印数量
    //isCheckPrinted 是否检查已打印过
    ep.print = function print(orders, pirntMethod, printCount, isCheckPrinted) {
        console.time('打印数据检查前');
        var template = addTmplInOrderListModule.GetCurrentTemplate()
        var isPinduoduo = common.PlatformType == "Pinduoduo" || (orders && orders.length > 0 && orders[0].PlatformType == "Pinduoduo");
        var ck = $.cookie("pdd-send-tips-no-show");


        var doPrint = function () {
            if (isPinduoduo && template != null && !common.IsPddTemplate(template.TemplateType) && !ck) {
                var dialogIndex = layer.open({
                    type: 1,
                    title: "请确认",
                    content: "<div style='font-size:14px;line-height:20px;margin:25px;'><div><p style='margin:5px;'>根据拼多多官方改造要求，10月16号起拼多多店铺订单只支持使用拼多多电子面单打单发货，请及时更换拼多多电子面单打印。</p><p style='margin:5px;'>使用教程：<a style='color:#2ebae9;' target='_blank' href='https://www.dgjapp.com/newHelpContentPc.html?id=5cbe6ccb33088732d493806c'>前往查看</a></p></div></div>",
                    btn: ["继续打印", "不再提示，继续打印", "取消"],
                    shadeClose: true,
                    area: ['550px', '250px'],
                    btn1: function () {
                        layer.close(dialogIndex);
                        ep.startPrint(orders, pirntMethod, printCount, isCheckPrinted);
                        return true;
                    },
                    btn2: function () {
                        layer.close(dialogIndex);
                        $.cookie("pdd-send-tips-no-show", 1, { expires: 30 });
                        ep.startPrint(orders, pirntMethod, printCount, isCheckPrinted);
                        return true;
                    },
                    btn3: function () {
                        return true;
                    }
                });
            }
            else {
                ep.startPrint(orders, pirntMethod, printCount, isCheckPrinted);
            }
        }

        if (isPinduoduo && template != null) {
            var html = "";
            //检查是否含有承诺信息
            $(orders).each(function (i, o) {
                $(orderTableBuilder.rows).each(function (i1, row) {
                    if (o.PlatformOrderId == row.PlatformOrderId && o.ShopId == row.ShopId) {
                        $(row.SubOrders).each(function (i2, so) {
                            if (so.ExtField2 && so.ExtField2 != template.ExpressCompanyCode) {
                                html += '<tr><td>' + so.PlatformOrderId + '</td><td data-value="' + so.ExtField2 + '">' + so.PromiseExpressName + '</td></tr>';
                            }
                        });
                        return true;
                    }
                });
            });
            if (html) {
                $("#pddSpecifyExpress i.express-checked").html(template.TemplateName);
                $("#pddSpecifyExpress table.unify_table tbody").html(html);
                var dialogIndex = layer.open({
                    type: 1,
                    title: "请确认",
                    content: $("#pddSpecifyExpress"),
                    btn: ["忽略，继续打印", "取消"],
                    shadeClose: true,
                    area: ['550', '250'],
                    btn1: function () {
                        layer.close(dialogIndex);
                        doPrint();
                        return true;
                    },
                    btn2: function () {
                        return true;
                    }
                });
            }
            else {
                doPrint();
            }
        }
        else {
            doPrint();
        }
    }
    //printMethod 打印方式
    //printcount 打印数量
    //isCheckPrinted 是否检查已打印过
    ep.startPrint = function startPrint(orders, pirntMethod, printCount, isCheckPrinted) {
        var template = addTmplInOrderListModule.GetCurrentTemplate()
        if (!ep.check(orders, template, checkTemplateIsNull, pirntMethod, printCount, isCheckPrinted, false, false))
            return false;

        if (!pirntMethod)
            pirntMethod = "Normal";
        printComponents = common.GetUsePrintComponents(template.TemplateType);

        var printers = [];
        switch (printComponents) {
            case "Lodop":
                printers = lp.printers();
                break;
            case "Cainiao":
            case "Link":
                printers = caiNiao.printers;
                break;
            case "Pinduoduo":
                printers = pdd.printers;
                break;
        }

        //参数初始化
        if (isCheckPrinted == undefined) isCheckPrinted = true;
        if (printCount == undefined || isNaN(printCount) == true)
            printCount = 1;
        else
            printCount = parseInt(printCount);

        if (printCount <= 0) {
            layer.alert("打印数量必须为大于0的数字");
            return false;
        }

        if (common.IsZhiLian(template.TemplateType, template.ExpressCompanyCode) && (printCount * orders.length) > template.Quantity) {
            layer.alert("单号不足！当前模板可用单号【" + template.Quantity + "】,本次打印所需单号【" + (printCount * orders.length) + "】，请充值或调整打印数量。");
            return false;
        }
            //检查打印数量是否超出了可用数量
        else if (common.IsNormalTemplate(template.TemplateType) == false && common.IsJdKdTemplate(template.TemplateType) == false
                && template.CpType != 1 && template.CpType != 4
                && (printCount * orders.length) > template.Quantity) {
            layer.alert("单号不足！当前模板可用单号【" + template.Quantity + "】,本次打印所需单号【" + (printCount * orders.length) + "】，请充值或调整打印数量。");
            return false;
        }

        var tempFunc = function () {

            //重打确认
            if (pirntMethod != 'NewPrint' && isCheckPrinted == true && ep.checkPrinted(orders, pirntMethod, template) == false) {
                return false;
            }

            if (common.IsKuaiYunTemplate(template.TemplateType)) {//template.TemplateType == 7 || template.TemplateType == 8) {
                pirntMethod = pirntMethod == 'NewPrint' ? "NewPrintKuaiYun" : "PrintKuaiYun"; //打印快运面单
            }

            var ext = otb.getSelectionsExt(orders);


            //根据模板类型生成对应的请求数据？
            var dialog = $.templates("#print-express-dialog-tmpl");
            var dialogData = {
                PrintMethod: pirntMethod, PrintCount: printCount, ExtInfo: ext, Orders: orders, Template: template, Printers: (printers || []), defaultPrinter: ep.defaultPrinter, IsCustomePrint: common.isCustomerOrder()
            };
            var html = dialog.render(dialogData);
            var title = "打印快递单";
            if (pirntMethod == "OneToMany")
                title += "【一单多包】";
            else if (pirntMethod == "NewPrint")
                title += "【新单号打印】";
            else if (pirntMethod == "RePrint")
                title += "【重打】";
            else if (pirntMethod == "PrintKuaiYun")
                title += "打印快运单";
            var btn2Handler = function () {
                var this_btn = $('.layui-layer-btn1');
                var processing = this_btn.attr('processing'); //处理中的标记
                if (processing) {
                    layer.alert('正在处理...请不要重复点击.');
                    return;
                }
                this_btn.attr('processing', true); //处理中的标记

                var printCount = 1;
                var printer = $("#express-printer-select").val();
                if (!printer) {
                    layer.msg("请选择打印机");
                    this_btn.removeAttr('processing'); //移除处理中标识
                    return false;
                }
                if (pirntMethod == "NewPrint" || pirntMethod == "RePrint" || pirntMethod == "Normal") {
                    printCount = $("#express-package-print-count").val();
                }
                if (pirntMethod == "OneToMany") {
                    printCount = $("#express-package-print-count").val();
                    if (printCount <= 0 || isNaN(printCount)) {
                        layer.msg("打印一单多包时，打印数量必须大于等于1");
                        this_btn.removeAttr('processing'); //移除处理中标识
                        return false;
                    }
                }
                if (pirntMethod == "PrintKuaiYun" || pirntMethod == "NewPrintKuaiYun") {
                    printCount = $("#txt_package_order_count").val().trim();
                    if (printCount <= 0 || isNaN(printCount)) {
                        layer.msg("打印快运面单时，子母件数量必须大于等于1");
                        this_btn.removeAttr('processing'); //移除处理中标识
                        return false;
                    }
                }
                var printerIndex = 0;
                if (printComponents == "Lodop") {
                    printerIndex = printer;
                    printer = $("#express-printer-select option[value='" + printer + "']").text();
                }
                ep.doPrint(orders, template, pirntMethod, printCount, true, printer, null, printerIndex);
                this_btn.removeAttr('processing'); //移除处理中标识
                this_btn.remove(); //直接移除按钮
            };

            var btn3Handler = function () {
                var templateId = template.Id;
                var templateType = template.TemplateType + "";
                var url = "";
                switch (templateType) {
                    case "1":
                        url = "/TemplateSet/EditTraditionTemplate?templateId=" + templateId;
                        break;
                    case "3":
                    case "2":
                    case "10":
                        url = "/TemplateSet/EditSiteTemplate?templateId=" + templateId;
                        break;
                    case "4":
                    case "5":
                    case "6":
                    case "7":
                    case "8":
                    case "9":
                    case "21":
                    case "22":
                    case "23":
                        url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        break;
                    default:
                        if (templateType >= 40 && templateType < 60)
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        else if (templateType >= 60 && templateType < 90) {
                            url = "/TemplateSet/EditSiteTemplate?templateId=" + templateId;
                            break;
                        }
                        else {
                            layer.alert("未识别模板类型，无法修改");

                            break;
                        }

                }
                if (url)
                    window.open(common.rewriteUrl(url));
            };

            var btns = ["直接打印", "修改模板"];
            var opts = {};
            if (printComponents != "Lodop") {
                btns.splice(1, 0, "  预 览  ") //菜鸟电子面单才有预览
                opts.btn2 = btn2Handler,
                    opts.btn3 = btn3Handler
            }
            var options = {
                type: 1,
                title: title,
                //closeBtn: 1,
                btn: btns,
                shadeClose: true,
                area: ['550px', '310px'],
                content: html,
                success: function () {
                    //自动发货
                    $(':radio[name="rdo_printed_auto_send"][value="' + commonModule.SystemoConfig.AutoSendSetVal + '"]').prop('checked', true);
                },
                btn1: function () {
                    var this_btn = $('.layui-layer-btn0');
                    var processing = this_btn.attr('processing'); //处理中的标记
                    if (processing) {
                        layer.alert('正在处理...请不要重复点击.');
                        return;
                    }
                    this_btn.attr('processing', true); //处理中的标记
                    var printCount = 1;
                    var printer = $("#express-printer-select").val();
                    if (!printer) {
                        layer.msg("请选择打印机");
                        this_btn.removeAttr('processing'); //移除处理中标识
                        return false;
                    }
                    if (pirntMethod == "NewPrint" || pirntMethod == "RePrint" || pirntMethod == "Normal") {
                        printCount = $("#express-package-print-count").val();
                    }
                    if (pirntMethod == "OneToMany") {
                        printCount = $("#express-package-print-count").val();
                        if (printCount <= 0 || isNaN(printCount)) {
                            layer.msg("打印一单多包时，打印数量必须大于等于1");
                            this_btn.removeAttr('processing'); //移除处理中标识
                            return false;
                        }
                    }
                    if (pirntMethod == "PrintKuaiYun" || pirntMethod == "NewPrintKuaiYun") {
                        printCount = $("#txt_package_order_count").val().trim();
                        if (printCount <= 0 || isNaN(printCount)) {
                            layer.msg("打印快运面单时，子母件数量必须大于等于1");
                            this_btn.removeAttr('processing'); //移除处理中标识
                            return false;
                        }
                    }
                    var printerIndex = 0;
                    if (printComponents == "Lodop") {
                        printerIndex = printer;
                        printer = $("#express-printer-select option[value='" + printer + "']").text();
                    }
                    //isCaiNiao ? caiNiao.setPrinter(printer) : lp.setPrinter(printerIndex);
                    if (ext.RefundOrders && ext.RefundOrders.length > 0) {
                        layer.confirm("您选中的订单中有<span style='color:red;'>" + ext.RefundOrders.length + "个订单是退款中的</span>，仍继续打印吗？",
                            function () {
                                ep.doPrint(orders, template, pirntMethod, printCount, false, printer, null, printerIndex);
                            }, function () {
                                layer.closeAll();
                            });
                    } else
                        ep.doPrint(orders, template, pirntMethod, printCount, false, printer, null, printerIndex);
                    this_btn.removeAttr('processing'); //移除处理中标识
                    this_btn.remove(); //直接移除按钮
                    return false;
                },
                btn2: btn3Handler
            };

            var openOpts = $.extend({}, options, opts);

            layer.open(openOpts);
        }

        console.timeEnd('打印数据检查前');
        //检查订单退款状态、模板的可用单号
        if (isCheckPrinted == true) {

            //由于有两个 检查项。1.检查退款订单。2.检查模板网点及可用单号余额。
            //之前两个请求都是同步的，同步加不上loading状态，改为异步并行执行。
            //并行执行后，将执行结果返回到回调函数中，如果前置校验不通过，则不往下执行。
            var checkLoading = common.LoadingMsg("打印前置检查");

            var arryList = [];

            //只有对接了消息的平台才需要去检查订单状态
            if (common.PlatformType == "Alibaba" || common.PlatformType == "AlibabaC2M" || common.PlatformType == "YouZan" || common.PlatformType == "TaobaoMaiCaiV2") {
                console.time('退款订单检查');
                otb.SetCheckReturnOrdersRequestShowLoading(false); //不单独显示进度
                arryList.push(otb.checkReturnOrders(orders, true));
                console.timeEnd('退款订单检查');
            }

            console.time('模板检查');
            arryList.push(ep.CheckTemplateAvaliable(template, (printCount * orders.length)));
            console.timeEnd('模板检查');
            common.Ajaxs(arryList, function (result) {
                layer.close(checkLoading);

                //前置校验不通过，则不往下执行
                if (result == false)
                    return;

                tempFunc();
            });
        }
        else
            tempFunc();
    }

    // 扫描打印弹框
    ep.scanPrint = function (orders, template, printCount, isByHand, isCheckPrinted) {
        if (!ep.check(orders, template, false, null, printCount, isCheckPrinted, isByHand, true))
            return false;

        var pirntMethod = "NewPrint"; //拿货小标签默认使用新单号打印
        printComponents = common.GetUsePrintComponents(template.TemplateType);
        var printers = [];
        switch (printComponents) {
            case "Lodop":
                printers = lp.printers();
                break;
            case "Cainiao":
            case "Link":
                printers = caiNiao.printers;
                break;
            case "Pinduoduo":
                printers = pdd.printers;
                break;
        }
        //参数初始化
        if (isCheckPrinted == undefined) isCheckPrinted = true;
        if (printCount == undefined || isNaN(printCount) == true) printCount = 1;

        printCount = parseInt(printCount);

        if (printCount <= 0) {
            layer.alert("打印数量必须为大于0的数字");
            return false;
        }

        otb.SetCheckReturnOrdersUrl("/Order/GetOrdersByApi");
        var refundOrders = otb.checkReturnOrders(orders);

        if (common.IsKuaiYunTemplate(template.TemplateType)) {//template.TemplateType == 7 || template.TemplateType == 8) {
            pirntMethod = pirntMethod == 'NewPrint' ? "NewPrintKuaiYun" : "PrintKuaiYun"; //打印快运面单
        }
        //根据模板类型生成对应的请求数据？
        var dialog = $.templates("#print-express-dialog-tmpl");
        var dialogData = { PrintMethod: pirntMethod, PrintCount: printCount, Orders: orders, Template: template, Printers: printers, defaultPrinter: ep.defaultPrinter };
        var html = dialog.render(dialogData);

        var title = "打印快递单";
        if (pirntMethod == "OneToMany")
            title += "【一单多包】";
        else if (pirntMethod == "NewPrint")
            title += "【新单号打印】";
        else if (pirntMethod == "RePrint")
            title += "【重打】";
        else if (pirntMethod == "PrintKuaiYun")
            title += "打印快运单";

        var btnHandler = function (isPreview) {
            var this_btn = $('.layui-layer-btn1');
            var processing = this_btn.attr('processing'); //处理中的标记
            if (processing) {
                layer.alert('正在处理...请不要重复点击.');
                return;
            }
            this_btn.attr('processing', true); //处理中的标记

            var printCount = 1;
            var printer = isByHand ? $("#express-printer-select").val() : $("#slt_printer").val();
            if (!printer) {
                layer.msg("请选择打印机");
                this_btn.removeAttr('processing'); //移除处理中标识
                return false;
            }
            if (pirntMethod == "NewPrint" || pirntMethod == "RePrint" || pirntMethod == "Normal") {
                printCount = isByHand ? $("#express-package-print-count").val() : 1;
            }
            if (pirntMethod == "OneToMany") {
                printCount = $("#express-package-print-count").val();
                if (printCount <= 0 || isNaN(printCount)) {
                    layer.msg("打印一单多包时，打印数量必须大于等于1");
                    this_btn.removeAttr('processing'); //移除处理中标识
                    return false;
                }
            }
            if (pirntMethod == "PrintKuaiYun" || pirntMethod == "NewPrintKuaiYun") {
                printCount = $("#txt_package_order_count").val().trim();
                if (printCount <= 0 || isNaN(printCount)) {
                    layer.msg("打印快运面单时，子母件数量必须大于等于1");
                    this_btn.removeAttr('processing'); //移除处理中标识
                    return false;
                }
            }
            var printerIndex = 0;
            if (printComponents == "Lodop") {
                printerIndex = printer;
                printer = $("#express-printer-select option[value='" + printer + "']").text();
            }

            // 扫描打印完成后，更新数据状态
            var printedCallback = function (orders, successWaybillCodes, isPreview) {
                var datas = [];
                $(orders).each(function (i, o) {
                    var order = { "Id": o.Id, "BatchNo": o.BatchNo, "PlatformOrderId": o.PlatformOrderId, "ShopId": o.ShopId };
                    order.OrderItems = [];
                    $(o.UpdateOrderItems).each(function (ii, oi) {
                        order.OrderItems.push({ "Id": oi.Id, "PlatformOrderId": oi.PlatformOrderId, "SubItemId": oi.SubItemId, "PrintedCount": oi.StockedCount, "StockedCount": oi.StockedCount });
                    });
                    datas.push(order);
                });

                console.log(datas);

                commonModule.Ajax({
                    url: "/ScanProductPrint/UpdateScanPrintStatus",
                    data: { "PrintInfoJson": JSON.stringify(datas), "IsPreview": isPreview },
                    success: function (result) {
                        if (result.Success) {
                            //$(".print-success").show();
                            scanProductPrint.ShowMessage('success', '打印成功');
                            //var isPreview = result.Data;
                            if (!isPreview) {
                                // 扫描打印后是否自动发货    
                                scanProductPrint.send(isByHand);
                            }
                        }
                        else {
                            //layer.msg(result.Message, { icon: 2 });
                            //$(".print-error").html(result.Message).show();
                            scanProductPrint.ShowMessage('error', result.Message);
                        }
                    }
                });

                var waybillCods = "";
                common.Foreach(successWaybillCodes, function (i, w) {
                    waybillCods += w.WaybillCode + ",";
                });
                if (waybillCods != "")
                    $('#lbl_waybill_code').text(waybillCods.trimEndDgj(','));

            }
            //ep.scanDoPrint(orders, template, pirntMethod, printCount, isPreview, printer, printedCallback, printerIndex);

            // 检测退款中订单，确认是否打印
            if (refundOrders && refundOrders.length > 0) {
                layer.confirm("您选中的订单中有<span style='color:red;'>" + refundOrders.length + "个订单是退款中的</span>，仍继续打印吗？",
                    function () {
                        ep.scanDoPrint(orders, template, pirntMethod, printCount, isPreview, printer, printedCallback, printerIndex);
                    }, function () {
                        layer.closeAll();
                    });
            } else
                ep.scanDoPrint(orders, template, pirntMethod, printCount, isPreview, printer, printedCallback, printerIndex);

            this_btn.removeAttr('processing'); //移除处理中标识
            this_btn.remove(); //直接移除按钮
            return false;
        };

        // 是否手动打印
        if (isByHand) {
            // 打印
            var btnPrintHandler = function () {
                btnHandler(false);
            }
            // 预览
            var btnPreviewHandler = function () {
                btnHandler(true);
            }

            // 修改模板
            var btn3Handler = function () {
                var templateId = template.Id;
                var templateType = template.TemplateType + "";
                var url = "";
                switch (templateType) {
                    case "1":
                        url = "/TemplateSet/EditTraditionTemplate?templateId=" + templateId;
                        break;
                    case "3":
                    case "2":
                    case "10":
                        url = "/TemplateSet/EditSiteTemplate?templateId=" + templateId;
                        break;
                    case "4":
                    case "5":
                    case "6":
                    case "7":
                    case "8":
                    case "9":
                    case "21":
                    case "22":
                    case "23":
                        url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        break;
                    default:
                        if (templateType >= 40 && templateType < 60)
                            url = "/TemplateSet/EditWaybillTemplate?templateId=" + templateId;
                        else if (templateType >= 60 && templateType < 90) {
                            url = "/TemplateSet/EditSiteTemplate?templateId=" + templateId;
                            break;
                        }
                        else {
                            layer.alert("未识别模板类型，无法修改");

                            break;
                        }

                }
                if (url)
                    window.open(common.rewriteUrl(url));
            };

            var btns = ["直接打印", "修改模板"];
            var opts = {};
            if (printComponents != "Lodop") {
                btns.splice(1, 0, "  预 览  "); //菜鸟电子面单才有预览
                opts.btn2 = btnPreviewHandler;
                opts.btn3 = btn3Handler;
            }
            var options = {
                type: 1,
                title: title,
                //closeBtn: 1,
                btn: btns,
                shadeClose: true,
                area: ['550px', '280px'],
                content: html,
                btn1: btnPrintHandler,
                btn2: btn3Handler
            };

            var openOpts = $.extend({}, options, opts);
            layer.open(openOpts);
            $("#express-printer-select").val($("#slt_printer").val());
        }
        else {
            btnHandler(false);
        }
    }

    // 开始扫描打印
    ep.scanDoPrint = function (orders, template, printType, packageCount, isPreView, printerName, printedCallback, printerIndex) {
        document_total_count = 0; //此次打印总文档数，用于生成面单序号
        var packageCount = packageCount;//$("#express-package-print-count").val();

        var desc = ep.GetPackageAndGoodsDesc(printType); //获取包装描述，产品平类
        var request = {
            TemplateId: template.Id,
            PackageCount: parseInt(packageCount),
            PackageDesc: desc.packageDesc,
            GoodsDesc: desc.goodsDesc,
            PrintMethod: printType,
            PrinterName: printerName,
            IsPreview: isPreView,
            IsScanPrint: true
        };
        ep.defaultPrinter = printerName;

        var toFullNameIsNullOrders = [];
        var models = [];
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var model = {
                WaybillCodeId: "", //printedPackageId ? printedPackageId: "",
                Id: order.Id,
                PlatformOrderId: order.PlatformOrderId,
                CustomerOrderId: order.CustomerOrderId,
                ShopId: order.ShopId,
                PrintInfo: order.PrintInfo,
                OrderItems: order.OrderItems,
                WaybillCode: order.WaybillCode,
                SellerRemark: order.SellerRemark,
                BuyerRemark: order.BuyerRemark,
                Receiver: {
                    toFullName: common.DeleteRareWords(order.Receiver.ToName),
                    toMobile: order.Receiver.ToMobile,
                    toArea: common.DeleteRareWords(order.Receiver.ToAddress),
                    buyerMemberId: order.Buyer.BuyerMemberId
                },
                Sender: {
                    SenderName: common.DeleteRareWords(order.Sender.SenderName),
                    SenderPhone: order.Sender.SenderPhone,
                    SenderAddress: common.DeleteRareWords(order.Sender.SenderAddress),
                    CompanyName: order.Sender.SenderCompany,
                },
                Buyer: {
                    BuyerMemberId: order.Buyer.buyerMemberId,
                    BuyerMemberName: order.Buyer.BuyerMemberName,
                    BuyerWangWang: order.Buyer.BuyerWangWang
                }
            };
            if (order.ChildOrderId)
                model.ChildOrderId = order.ChildOrderId;

            if (model.Receiver.toFullName == "") {
                toFullNameIsNullOrders.push(model.PlatformOrderId);
            }

            models.push(model);
        };

        if (toFullNameIsNullOrders.length > 0) {
            layer.alert("订单【" + toFullNameIsNullOrders.join(",") + "】收件人姓名为空或者全为特殊字符，会导致打单失败，请更改后再打印。", function (index) { layer.closeAll(""); })
        }

        //删除危险字符
        models = JSON.parse(common.DelDangerChar(JSON.stringify(models)));
        var total_data_count = models.length; //打印的总订单数
        var models_copy = JSON.parse(JSON.stringify(models));

        function GetRequestModel(orders) {
            request.Orders = orders;
            return request;
        }

        var callback_counter = 0;
        function SingleCallBack(r) {
            try {

                callback_counter++;
                var temp_count = (callback_counter * orderPrintBatchNumber);
                var temp_number = (temp_count / total_data_count) * 100;
                var bar_width = 0;
                if (temp_number < 15)
                    bar_width = temp_number.toFixed(0);
                else
                    bar_width = temp_number.toFixed(2);
                if (bar_width > 100) bar_width = 100;
                $('#div_pro_bar').css({ width: (bar_width + '%') });
                $('#div_bar_text').text(bar_width + '%');

                if (temp_count > total_data_count) temp_count = total_data_count;
                $('#sp_curr_number').text(temp_count);

            } catch (e) {
                var errorMsg = "单个请求回调异常》" + e.stack;
                console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }
            //console.log(r);
        }

        function AllDoneCallback(allRsp, requestDatas) {
            document_total_count = 0; //先清空总面单数
            //先记录起所有请求返回的数据
            try {
                if (typeof printExpressAllRspLog != 'undefined' && printExpressAllRspLog == true) {
                    common.JsLogToMongoDB("所有请求回来后记录数据", JSON.stringify(allRsp));
                }
            } catch (e) {
                var errorMsg = "所有请求完成，记录所有请求的数据异常》" + e.stack;
                console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            var printing = common.LoadingMsg("正在打印，请不要刷新或者关闭页面");

            //将所有返回结果汇总
            var result = null;
            var faildRsp = {}; //失败的响应

            try {
                var requestDict = {}; //请求数据字典{orderId+platformOrderId+shopId：次数}，用于校验请求回来的数据是否是请求发送的数据
                for (var i = 0; i < requestDatas.length; i++) {
                    var req = requestDatas[i];
                    var key = req.Id + "_" + req.PlatformOrderId + "_" + req.ShopId;
                    requestDict[key] = 1;
                }

                var notInRequestDict = []; //后台返回的数据不存在请求数据中的数据    
                var documentDict = {}; //已经汇总的文档{运单号：次数}（一个文档会打印出一个面单）   
                var taskDict = {}; //汇总发送给菜鸟组件的请求，按请求批次大小数量来决定一个task包含的documents数量。目前默认是10订单一个批次。同时要保证每个task的id必须唯一。

                //汇总
                if (Array.isArray(allRsp) && allRsp.length > 0) {
                    //将所有回来的请求数据根据请求批次号排序，保证订单按顺序打印
                    allRsp = common.SortExt(allRsp, "Data.RequestBatchNumber", false, true);
                    for (var i = 0; i < allRsp.length; i++) {
                        var tempR = allRsp[i];
                        if (tempR.Success == false) {
                            faildRsp[tempR.RequestBatch] = tempR;
                            continue;
                        }
                        if (result == null) {
                            result = tempR;
                            if (result.Data.PrintDataList) {
                                //将task汇总到taskList里(按请求批次大小数量来决定一个task包含的documents数量)
                                var request_first = JSON.parse(JSON.stringify(result.Data.PrintDataList));
                                request_first.requestID = request_first.requestID + "_" + getOnlyCode(); //保证requestID唯一
                                request_first.task.firstDocumentNumber = 1; //第一个请求面单打印序号从1开始
                                document_total_count += request_first.task.documents.length; //汇总文档数
                                taskDict[request_first.task.taskID] = request_first; //task汇入字典，用于后续判断taskID是否重复
                                result.Data.PrintDataList.TaskList = [];
                                result.Data.PrintDataList.TaskList.push(request_first);
                            }
                        }
                        else {
                            result.Data.successCount += tempR.Data.successCount; //汇总成功数
                            result.Data.errorCount += tempR.Data.errorCount;     //汇总失败数
                            //汇总waybillCode数据
                            if (Array.isArray(tempR.Data.WaybillCode) == true && tempR.Data.WaybillCode.length > 0) {
                                for (var j = 0; j < tempR.Data.WaybillCode.length; j++) {
                                    var tempWaybillCode = tempR.Data.WaybillCode[j];
                                    var key = tempWaybillCode.OrderInfo.OrderInfo.Id + "_" + tempWaybillCode.OrderInfo.OrderInfo.PlatformID + "_" + tempWaybillCode.OrderInfo.OrderInfo.ShopId;
                                    if (!requestDict[key]) {
                                        //请求回来的数据不存在于请求数据中
                                        notInRequestDict.push(tempWaybillCode);
                                    }
                                    result.Data.WaybillCode.push(tempWaybillCode);
                                }
                            }
                            //汇总lodop组件打印的数据
                            if (Array.isArray(tempR.Data.TemplateList) == true && tempR.Data.TemplateList.length > 0) {
                                for (var j = 0; j < tempR.Data.TemplateList.length; j++) {
                                    result.Data.TemplateList.push(tempR.Data.TemplateList[j]);
                                }
                            }
                            //汇总打印记录id
                            if (Array.isArray(tempR.Data.PrintHistoryIds) == true && tempR.Data.PrintHistoryIds.length > 0) {
                                for (var j = 0; j < tempR.Data.PrintHistoryIds.length; j++) {
                                    result.Data.PrintHistoryIds.push(tempR.Data.PrintHistoryIds[j]);
                                }
                            }
                            //汇总非lodop组件打印的数据（菜鸟、拼多多组件）
                            if (tempR.Data.PrintDataList != null && tempR.Data.PrintDataList.task && Array.isArray(tempR.Data.PrintDataList.task.documents) && tempR.Data.PrintDataList.task.documents.length > 0) {
                                var tempTask = tempR.Data.PrintDataList.task;
                                tempTask.firstDocumentNumber = document_total_count + 1; //面单开始序号
                                var temp_documents = []; //临时保存这个批次的douments(一个document一个面单）
                                for (var j = 0; j < tempTask.documents.length; j++) {
                                    var tempDoc = tempTask.documents[j];
                                    var docDictVal = documentDict[tempDoc.documentID];
                                    if (!docDictVal) {
                                        //不存在，数量初始为1
                                        documentDict[tempDoc.documentID] = 1;
                                        //不存在，则加入临时集合
                                        temp_documents.push(tempDoc);
                                        document_total_count++; //汇总文档数
                                    }
                                    else {
                                        //存在，则次数加一
                                        documentDict[tempDoc.documentID] = (docDictVal + 1);
                                    }
                                }
                                //判断taskID是否存在
                                if (taskDict[tempTask.taskID]) {
                                    //存在，则重新生成taskID
                                    tempTask.taskID = getOnlyCode();
                                }
                                //将新documents（排除了已存在的document，防止重复）覆盖task的documents
                                tempTask.documents = temp_documents;
                                result.Data.PrintDataList.TaskList.push({
                                    cmd: 'print',
                                    requestID: "requestID_" + getOnlyCode(),
                                    task: tempTask
                                });
                            }
                        }
                        result.Data.totalPrintNum = total_data_count; //+= tempR.Data.totalPrintNum;
                    }
                }
                else {
                    if (allRsp.Success == false)
                        faildRsp[1] = allRsp;
                    else
                        result = allRsp;
                }

                if (result == null) {
                    //全部失败
                    result = {
                        Success: true,
                        Data: {
                            Template: template,
                            IsError: true,
                            totalPrintNum: total_data_count,
                            successCount: 0,
                            errorCount: 0,
                            WaybillCode: []
                        }
                    };
                }
            } catch (e) {
                var errorMsg = "所有请求完成，数据解析异常》" + e.stack;
                console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            try {
                //汇总错误
                for (var i in faildRsp) {
                    var batchNumber = parseInt(i);
                    var tempSize = orderPrintBatchNumber;
                    if ((batchNumber * orderPrintBatchNumber) < models_copy.length) {
                        result.Data.errorCount += orderPrintBatchNumber;
                    }
                    else {
                        var errorCount = (models_copy.length % orderPrintBatchNumber) || orderPrintBatchNumber;
                        result.Data.errorCount += errorCount;
                        tempSize = errorCount;
                    }

                    var batchPlatformOrderIds = '批次' + batchNumber + "：";

                    var idx = (batchNumber - 1) * orderPrintBatchNumber;
                    for (var j = idx; j < idx + tempSize; j++) {
                        batchPlatformOrderIds += models_copy[j].PlatformOrderId + ',';
                    }
                    result.Data.WaybillCode.push({
                        IsError: true,
                        BatchPlatformOrderIds: batchPlatformOrderIds.trimEndDgj(','),
                        ErrMsg: faildRsp[i].Message
                    });
                }
            } catch (e) {
                var errorMsg = "所有请求完成，汇总错误异常》" + e.stack;
                console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            //校验服务器返回的数据与请求发送的数据的一致性
            try {
                var difference = {
                    RspsNotInReqs: [], //返回的数据不在请求中
                    RepeatDocuments: [], //返回的重复面单
                }; //差异对象
                //后台返回的数据不在请求数据中
                if (notInRequestDict.length > 0) {
                    difference.RspsNotInReqs = notInRequestDict;
                }
                for (var key in documentDict) {
                    if (documentDict[key] > 1) {
                        difference.RepeatDocuments.push(key); //重复的面单号
                    }
                }

                //数据不一致，则记录日志
                if (difference.RspsNotInReqs.length > 0 || difference.RepeatDocuments.length > 0) {
                    common.JsLogToMongoDB("快递单打印后端返回的数据和请求数据不一致", JSON.stringify(difference));
                }

            } catch (e) {
                var errorMsg = "数据不一致日志记录报错》" + e.stack;
                console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            //处理返回的结果
            if (!result.Success) {
                try {
                    layer.closeAll();
                    if (result.ErrorCode) {
                        switch (result.ErrorCode) {
                            case 'branch_not_exist':
                                var url = common.rewriteUrl('/AccountList/');
                                layer.alert(result.Message + " <a href='" + url + "'>点击去电子面单账号管理</a>");
                                break;
                            case 'branch_addr_change':
                                var url = common.rewriteUrl('/TemplateSet/');
                                layer.alert(result.Message + " <a href='" + url + "'>点击去模板管理</a>");
                                break;
                            case 'taobao_branch_errcode27':
                                branch_past(template, false);
                                break;
                            default:
                                layer.alert(result.Message);
                                break;
                        }
                    }
                    else {
                        layer.alert(result.Message);
                    }
                } catch (e) {
                    var errorMsg = "所有请求完成，汇总提示异常》" + e.stack;
                    console.log(errorMsg);
                    common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                }
            } else {
                var data = result.Data;
                if (data.successCount > 0) {
                    try {
                        //1.添加面单到订单行对象
                        var successWaybillCodes = [];
                        $(data.WaybillCode).each(function (index, w) {
                            if (!w.IsError) {
                                //var row = null;//otb.rows[ix];
                                //common.Foreach(otb.rows, function (i, o) {
                                //    if (o.Id == w.OrderId) {
                                //        row = o;
                                //        return 'break;';
                                //    }
                                //});

                                //if (row == null) {
                                //    throw "根据订单Id【" + w.OrderId + "】未找到订单Row对象";
                                //}
                                ////row.LastExpressPrintTime = 1;
                                ////添加到单号中,最新打印，加到前面
                                //if (!row.WaybillCodes) row.WaybillCodes = [];
                                //row.WaybillCodes.unshift({
                                //    TemplateId: template.Id,
                                //    TemplateType: template.TemplateType,
                                //    ExpressCpCode: template.ExpressCompanyCode,
                                //    WaybillCodeId: w.WaybillCodeId,
                                //    WaybillCode: w.WaybillCode,
                                //    CompanyCode: template.ExpressCompanyCode
                                //});

                                successWaybillCodes.push(w);
                                //赋值orders 对象中的waybillcode字段，让预览后点击打印按钮，后台能收到预览的waybillcode，从而打印出相同的单号
                                common.Foreach(orders, function (i, o) {
                                    if (o.Id == w.OrderId) {
                                        o.WaybillCode = w.WaybillCode;
                                        $('.scanPrint-table-title[data-pid="' + o.Id + '"]').attr("data-wcode", w.WaybillCode);
                                    }
                                });
                                scanProductPrint.PrintedOrders = orders;
                            }
                        });

                    } catch (e) {
                        var p_msg = "";
                        try {
                            p_msg += JSON.stringify(data);
                            p_msg += '此时订单行数据：' + JSON.stringify(otb.rows);
                        } catch (ex) {
                            wcs = "data 或 otb.rows 对象转String报错了." + ex.stack;
                        }
                        var errorMsg = "添加面单到订单行对象异常》" + p_msg + "》" + e.stack;
                        console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    try {
                        //2.更新页面状态
                        otb.changeTemplate(template);
                    } catch (e) {
                        var errorMsg = "更新页面状态异常》" + e.stack;
                        console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    try {
                        //3.记录打印日志或者预览打印操作设置
                        if (isPreView == false) {
                            _PrintedCallbackUrl = "/ScanPrint/PrintCallback";
                            //记录打印日志
                            _writePrintLogToServer(data, successWaybillCodes, template, printerName, orders, false);
                        }
                        else {
                            //设置 预览页面的打印按钮功能 //doPrint(orders, template, printType, packageCount, isPreView, printerName, printedCallback, printerIndex)
                            common.SetPrintAction(ep.scanDoPrint.bind(this, orders, template, 'Normal', packageCount, false, printerName, printedCallback, printerIndex));

                            //更新预览标志
                            _wirtePreviewFlagToOrder(orders);
                        }
                    } catch (e) {
                        var errorMsg = "记录打印日志或者预览打印操作设置异常》" + e.stack;
                        console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    try {
                        data.ExpressCompanyCode = template.ExpressCompanyCode; //复制快递编码，用于控制 运单号条形码显不显示 文字的问题。
                        //4.发送到打印机打印
                        ep.postPrintCommond(data, printerName, printerIndex);

                        //5.绑定打印机信息
                        common.SetPrinterBind(template.Id, 1, printerName, true);

                        //6.保存普通模板 单号
                        common.SaveTraditionWaybillCodeConfig(template);

                        //检查可用单号
                        //addTmplInOrderListModule.CheckQty(false, template);

                    } catch (e) {
                        var errorMsg = "4，5，6步异常》" + e.stack;
                        console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    if (data.errorCount <= 0)
                        layer.closeAll();
                    if (typeof printedCallback == 'function')
                        printedCallback(orders, successWaybillCodes, isPreView);
                }
                layer.close(printing);
                if (data.errorCount > 0) {
                    //显示错误信息
                    data.Template = template;
                    _showErrorMessage(data);
                }
            }
        }

        try {
            //commonModule.ExpressRequestDatas = models;
            //ep.ExpressRequestDatas = models;
            ///Order/ExpressPrint  /Common/TestBatchAjax
            common.posts('/ScanPrint/PrintOrder', models, GetRequestModel, SingleCallBack, AllDoneCallback, orderPrintBatchNumber);
        } catch (e) {
            var errorMsg = "分批发送请求异常》" + e.stack;
            console.log(errorMsg);
            common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
        }
    }


    //打印快递单：一单多包
    ep.printOneToMany = function printOneToMany(orderId) {
        var orders = otb.getSelections(orderId);
        ep.print(orders, "OneToMany");
    }

    //打印快递单：重新打印
    ep.reprint = function reprint(orderId, printedPackageId) {
        var orders = otb.getSelections(orderId);
        ep.print(orders, "RePrint", printedPackageId);
    }

    //打印快递单：新单号打印
    ep.printNew = function newPrint(orderId) {
        var orders = otb.getSelections(orderId);
        ep.print(orders, "NewPrint");
    }

    //打印快递单：普通打印（默认）
    ep.printNormal = function normalPrint(orderId) {
        var orders = otb.getSelections(orderId);
        ep.print(orders, "Normal");
    }

    ep.defaultPrinter = "";
    ep.bindPrinter = function () {
        var p = $("#express-printer-select option:selected").text();
        var t = $("#send-good-template-template-select").val();
        var template = addTmplInOrderListModule.GetCurrentTemplate();
        if (template)
            t = template.Id;
        ep.defaultPrinter = p;
        common.SetPrinterBind(t, 1, p);
    }

    ep.GetPackageAndGoodsDesc = function (printType) {
        var packageDesc = null, goodsDesc = null; //包装描述，产品品类
        if (printType == 'PrintKuaiYun' || printType == 'NewPrintKuaiYun') {
            var packageDescCtrol = $('#sel_package_desc'), goodsDescCtrol = $('#sel_goods_desc');
            if (packageDescCtrol.length > 0 && packageDescCtrol.val().trim() != '0' && packageDescCtrol.val().trim() != '') {
                packageDesc = packageDescCtrol.val().trim();
            }
            if (goodsDescCtrol.length > 0 && goodsDescCtrol.val().trim() != '0' && goodsDescCtrol.val().trim() != '') {
                goodsDesc = goodsDescCtrol.val().trim();
            }
        }
        return { packageDesc: packageDesc, goodsDesc: goodsDesc };
    }

    //开始打印 
    //必填参数 orders订单数据
    //必填参数 template 模板数据
    //必填参数 printType 有四个值：Normal 正常打印 需获取上一次打印的包裹ID，OneToMany 一单多包，Reprint 重打， NewPrint 新单号打印
    //必填参数 packageCount 面单数量，仅当打印一单多包和快运时需提供，否则默认为1
    //必填参数 isPreView 是否是预览
    //必填参数 PrinterName 打印机名称
    //选填参数 printedCallback：打印后的回调函数
    var document_total_count = 0; //此次打印总文档数，用于生成面单序号
    ep.doPrint = function doPrint(orders, template, printType, packageCount, isPreView, printerName, printedCallback, printerIndex) {
        var packageCount = packageCount;//$("#express-package-print-count").val();

        var desc = ep.GetPackageAndGoodsDesc(printType); //获取包装描述，产品平类
        var request = {
            TemplateId: template.Id,
            PackageCount: parseInt(packageCount),
            PackageDesc: desc.packageDesc,
            GoodsDesc: desc.goodsDesc,
            PrintMethod: printType,
            PrinterName: printerName,
            IsPreview: isPreView,
        };
        ep.defaultPrinter = printerName;

        var toFullNameIsNullOrders = [];
        var models = [];
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var model = {
                WaybillCodeId: "", //printedPackageId ? printedPackageId: "",
                Index: (i + 1), // 用于后端记录打印顺序
                Id: order.Id,
                PlatformOrderId: order.PlatformOrderId,
                CustomerOrderId: order.CustomerOrderId,
                ShopId: order.ShopId,
                PrintInfo: order.PrintInfo,
                OrderItems: order.OrderItems,
                WaybillCode: order.WaybillCode,
                SellerRemark: order.SellerRemark,
                BuyerRemark: order.BuyerRemark,
                Receiver: {
                    toFullName: common.DeleteRareWords(order.Receiver.ToName),
                    toMobile: order.Receiver.ToMobile,
                    toArea: common.DeleteRareWords(order.Receiver.ToAddress),
                    buyerMemberId: order.Buyer.BuyerMemberId
                },
                Sender: {
                    SenderName: common.DeleteRareWords(order.Sender.SenderName),
                    SenderPhone: order.Sender.SenderPhone,
                    SenderAddress: common.DeleteRareWords(order.Sender.SenderAddress),
                    CompanyName: order.Sender.SenderCompany,
                },
                Buyer: {
                    BuyerMemberId: order.Buyer.buyerMemberId,
                    BuyerMemberName: order.Buyer.BuyerMemberName,
                    BuyerWangWang: order.Buyer.BuyerWangWang
                }
            };

            if (model.Receiver.toFullName == "") {
                toFullNameIsNullOrders.push(model.PlatformOrderId);
            }
            models.push(model);
        };

        if (toFullNameIsNullOrders.length > 0) {
            layer.alert("订单【" + toFullNameIsNullOrders.join(",") + "】收件人姓名为空或者全为特殊字符，会导致打单失败，请更改后再打印。", function (index) { layer.closeAll(""); })
        }

        //删除危险字符
        models = JSON.parse(common.DelDangerChar(JSON.stringify(models)));

        /*
        //request.Orders = models;
        //common.Ajax({
        //    url: "/Order/ExpressPrint",
        //    data: request,
        //    loadingMessage: "正在打印",
        //    success: function (result) {
        //        //处理返回的结果
        //        if (!result.Success) {
        //            layer.closeAll();
        //            if (result.ErrorCode) {
        //                switch (result.ErrorCode) {
        //                    case 'branch_not_exist':
        //                        var url = common.rewriteUrl('/AccountList/');
        //                        layer.alert(result.Message + " <a href='" + url + "'>点击去电子面单账号管理</a>");
        //                        break;
        //                    case 'branch_addr_change':
        //                        var url = common.rewriteUrl('/TemplateSet/');
        //                        layer.alert(result.Message + " <a href='" + url + "'>点击去模板管理</a>");
        //                        break;
        //                    case 'taobao_branch_errcode27':
        //                        branch_past(template, false);
        //                        break;
        //                    default:
        //                        layer.alert(result.Message);
        //                        break;
        //                }
        //            }
        //            else {
        //                layer.alert(result.Message);
        //            }
        //        } else {
        //            var data = result.Data;
        //            if (data.successCount > 0) {
 
        //                //1.添加面单到订单行对象
        //                var successWaybillCodes = [];
        //                $(data.WaybillCode).each(function (index, w) {
        //                    if (!w.IsError) {
        //                        var ix = $(".order-chx[data-id='" + w.OrderId + "']").attr("data-index");
        //                        var row = otb.rows[ix];
        //                        //row.LastExpressPrintTime = 1;
        //                        //添加到单号中,最新打印，加到前面
        //                        if (!row.WaybillCodes) row.WaybillCodes = [];
        //                        row.WaybillCodes.unshift({
        //                            TemplateId: template.Id,
        //                            TemplateType: template.TemplateType,
        //                            ExpressCpCode: template.ExpressCompanyCode,
        //                            WaybillCodeId: w.WaybillCodeId,
        //                            WaybillCode: w.WaybillCode,
        //                            CompanyCode: template.ExpressCompanyCode
        //                        });
        //                        successWaybillCodes.push(w);
        //                        //赋值orders 对象中的waybillcode字段，让预览后点击打印按钮，后台能收到预览的waybillcode，从而打印出相同的单号
        //                        common.Foreach(orders, function (i, o) {
        //                            if (o.Id == w.OrderId) {
        //                                o.WaybillCode = w.WaybillCode;
        //                            }
        //                        });
        //                    }
        //                });
 
        //                //2.更新页面状态
        //                otb.changeTemplate(template);
 
        //                //3.记录打印日志或者预览打印操作设置
        //                if (isPreView == false) {
        //                    //记录打印日志
        //                    _writePrintLogToServer(data, successWaybillCodes, template, printerName, orders, true);
        //                }
        //                else {
        //                    //设置 预览页面的打印按钮功能
        //                    common.SetPrintAction(ep.doPrint.bind(this, orders, template, printType, packageCount, false, printerName, printedCallback));
 
        //                    //更新预览标志
        //                    _wirtePreviewFlagToOrder(orders);
        //                }
 
        //                //4.发送到打印机打印
        //                ep.postPrintCommond(data, printerName, printerIndex);
 
        //                //绑定打印机信息
        //                common.SetPrinterBind(template.Id, 1, printerName, true);
        //                if (data.errorCount <= 0)
        //                    layer.closeAll();
        //                if (typeof printedCallback == 'function')
        //                    printedCallback();
 
        //            }
        //            if (data.errorCount > 0) {
        //                //显示错误信息
        //                data.Template = template;
        //                _showErrorMessage(data);
        //            }
        //        }
        //    }
        //});
        
        */

        var total_data_count = models.length; //打印的总订单数
        var models_copy = JSON.parse(JSON.stringify(models));

        function GetRequestModel(orders) {
            request.Orders = orders;
            return request;
        }

        var callback_counter = 0;
        function SingleCallBack(r) {
            try {

                callback_counter++;
                var temp_count = (callback_counter * orderPrintBatchNumber);
                var temp_number = (temp_count / total_data_count) * 100;
                var bar_width = 0;
                if (temp_number < 15)
                    bar_width = temp_number.toFixed(0);
                else
                    bar_width = temp_number.toFixed(2);
                if (bar_width > 100) bar_width = 100;
                $('#div_pro_bar').css({ width: (bar_width + '%') });
                $('#div_bar_text').text(bar_width + '%');

                if (temp_count > total_data_count) temp_count = total_data_count;
                $('#sp_curr_number').text(temp_count);

            } catch (e) {
                var errorMsg = "单个请求回调异常》" + e.stack;
                console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }
            //console.log(r);
        }

        function AllDoneCallback(allRsp, requestDatas) {
            document_total_count = 0; //先清空总面单数
            var taskDocumentCount = otb.Setting.CaiNiaoBatchPrintCount;
            var tempAllRsp = [];
            //若配置菜鸟发送按一个一个来，则拆开请求(默认配置是10个)
            if (taskDocumentCount != 10 && !isPreView && (printComponents == "Cainiao" || printComponents == "Pinduoduo")) {
                taskDocumentCount = 1;
                for (var i = 0; i < allRsp.length; i++) {
                    var rsp = allRsp[i];
                    if (!rsp.Success || rsp.errorCount > 0) {
                        tempAllRsp.push(rsp);
                        continue;
                    }
                    for (var j = 0; j < rsp.Data.WaybillCode.length; j++) {
                        var wc = rsp.Data.WaybillCode[j];
                        var tempRsp = JSON.parse(JSON.stringify(rsp));
                        tempRsp.Data.PrintDataList.task.documents = [];
                        tempRsp.Data.PrintDataList.task.taskID += "_" + i + "_" + j;
                        tempRsp.Data.WaybillCode = [];
                        tempRsp.Data.WaybillCode.push(wc);
                        tempRsp.errorCount = 0;
                        tempRsp.successCount = 1;
                        tempRsp.totalPrintNum = 1;
                        var docs = rsp.Data.PrintDataList.task.documents;
                        var index = 0;
                        for (var k = 0; k < docs.length; k++) {
                            var doc = docs[k];
                            if (doc.documentID.indexOf(wc.WaybillCode) != -1) {
                                tempRsp.Data.PrintDataList.task.documents.push(doc);
                                index++;
                            }
                            else {
                                if (index > 0)
                                    index++;
                            }
                            if (index >= 2)
                                break;
                        }
                        tempAllRsp.push(tempRsp);
                    }
                }
                allRsp = tempAllRsp;
            }

            //先记录起所有请求返回的数据
            try {
                if (typeof printExpressAllRspLog != 'undefined' && printExpressAllRspLog == true) {
                    common.JsLogToMongoDB("所有请求回来后记录数据", JSON.stringify(allRsp));
                }
            } catch (e) {
                var errorMsg = "所有请求完成，记录所有请求的数据异常》" + e.stack;
                console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            var printing = common.LoadingMsg("正在打印，请不要刷新或者关闭页面");

            //将所有返回结果汇总
            var result = null;
            var faildRsp = {}; //失败的响应

            try {

                var requestDict = {}; //请求数据字典{orderId+platformOrderId+shopId：次数}，用于校验请求回来的数据是否是请求发送的数据
                for (var i = 0; i < requestDatas.length; i++) {
                    var req = requestDatas[i];
                    var key = req.Id + "_" + req.PlatformOrderId + "_" + req.ShopId;
                    requestDict[key] = 1;
                }

                var notInRequestDict = []; //后台返回的数据不存在请求数据中的数据

                var documentDict = {}; //已经汇总的文档{运单号：次数}（一个文档会打印出一个面单）

                var taskDict = {}; //汇总发送给菜鸟组件的请求，按请求批次大小数量来决定一个task包含的documents数量。目前默认是10订单一个批次。同时要保证每个task的id必须唯一。

                //汇总
                if (Array.isArray(allRsp) && allRsp.length > 0) {
                    //将所有回来的请求数据根据请求批次号排序，保证订单按顺序打印
                    allRsp = common.SortExt(allRsp, "Data.RequestBatchNumber", false, true);
                    for (var i = 0; i < allRsp.length; i++) {
                        var tempR = allRsp[i];
                        if (tempR.Success == false) {
                            faildRsp[tempR.RequestBatch] = tempR;
                            continue;
                        }
                        if (result == null) {
                            result = tempR;
                            if (result.Data.PrintDataList) {
                                //将task汇总到taskList里(按请求批次大小数量来决定一个task包含的documents数量)
                                var request_first = JSON.parse(JSON.stringify(result.Data.PrintDataList));
                                request_first.requestID = request_first.requestID + "_" + getOnlyCode(); //保证requestID唯一
                                request_first.task.firstDocumentNumber = 1; //第一个请求面单打印序号从1开始
                                document_total_count += request_first.task.documents.length; //汇总文档数
                                taskDict[request_first.task.taskID] = request_first; //task汇入字典，用于后续判断taskID是否重复
                                result.Data.PrintDataList.TaskList = [];
                                result.Data.PrintDataList.TaskList.push(request_first);
                            }
                        }
                        else {
                            result.Data.successCount += tempR.Data.successCount; //汇总成功数
                            result.Data.errorCount += tempR.Data.errorCount;     //汇总失败数
                            //汇总waybillCode数据
                            if (Array.isArray(tempR.Data.WaybillCode) == true && tempR.Data.WaybillCode.length > 0) {
                                for (var j = 0; j < tempR.Data.WaybillCode.length; j++) {
                                    var tempWaybillCode = tempR.Data.WaybillCode[j];
                                    var key = tempWaybillCode.OrderInfo.OrderInfo.Id + "_" + tempWaybillCode.OrderInfo.OrderInfo.PlatformID + "_" + tempWaybillCode.OrderInfo.OrderInfo.ShopId;
                                    if (!requestDict[key]) {
                                        //请求回来的数据不存在于请求数据中
                                        notInRequestDict.push(tempWaybillCode);
                                    }
                                    result.Data.WaybillCode.push(tempWaybillCode);
                                }
                            }
                            //汇总lodop组件打印的数据
                            if (Array.isArray(tempR.Data.TemplateList) == true && tempR.Data.TemplateList.length > 0) {
                                for (var j = 0; j < tempR.Data.TemplateList.length; j++) {
                                    result.Data.TemplateList.push(tempR.Data.TemplateList[j]);
                                }
                            }
                            //汇总打印记录id
                            if (Array.isArray(tempR.Data.PrintHistoryIds) == true && tempR.Data.PrintHistoryIds.length > 0) {
                                for (var j = 0; j < tempR.Data.PrintHistoryIds.length; j++) {
                                    result.Data.PrintHistoryIds.push(tempR.Data.PrintHistoryIds[j]);

                                }
                            }
                            //汇总非lodop组件打印的数据（菜鸟、拼多多组件）
                            if (tempR.Data.PrintDataList != null && tempR.Data.PrintDataList.task && Array.isArray(tempR.Data.PrintDataList.task.documents) && tempR.Data.PrintDataList.task.documents.length > 0) {
                                var tempTask = tempR.Data.PrintDataList.task;
                                tempTask.firstDocumentNumber = document_total_count + 1; //面单开始序号
                                var temp_documents = []; //临时保存这个批次的douments(一个document一个面单）
                                for (var j = 0; j < tempTask.documents.length; j++) {
                                    var tempDoc = tempTask.documents[j];
                                    var docDictVal = documentDict[tempDoc.documentID];
                                    if (!docDictVal) {
                                        //不存在，数量初始为1
                                        documentDict[tempDoc.documentID] = 1;
                                        //不存在，则加入临时集合
                                        temp_documents.push(tempDoc);
                                        document_total_count++; //汇总文档数
                                    }
                                    else {
                                        //存在，则次数加一
                                        documentDict[tempDoc.documentID] = (docDictVal + 1);
                                    }
                                }
                                //判断taskID是否存在
                                if (taskDict[tempTask.taskID]) {
                                    //存在，则重新生成taskID
                                    tempTask.taskID = getOnlyCode();
                                }
                                //将新documents（排除了已存在的document，防止重复）覆盖task的documents
                                tempTask.documents = temp_documents;
                                result.Data.PrintDataList.TaskList.push({
                                    cmd: 'print',
                                    requestID: "requestID_" + getOnlyCode(),
                                    task: tempTask
                                });
                            }
                        }
                        result.Data.totalPrintNum = total_data_count; //+= tempR.Data.totalPrintNum;
                    }
                }
                else {
                    if (allRsp.Success == false)
                        faildRsp[1] = allRsp;
                    else
                        result = allRsp;
                }

                if (result == null) {
                    //全部失败
                    result = {
                        Success: true,
                        Data: {
                            Template: template,
                            IsError: true,
                            totalPrintNum: total_data_count,
                            successCount: 0,
                            errorCount: 0,
                            WaybillCode: []
                        }
                    };
                }

            } catch (e) {

                var errorMsg = "所有请求完成，数据解析异常》" + e.stack;
                console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            try {

                //汇总错误
                for (var i in faildRsp) {
                    var batchNumber = parseInt(i);
                    var tempSize = orderPrintBatchNumber;
                    if ((batchNumber * orderPrintBatchNumber) < models_copy.length) {
                        result.Data.errorCount += orderPrintBatchNumber;
                    }
                    else {
                        var errorCount = (models_copy.length % orderPrintBatchNumber) || orderPrintBatchNumber;
                        result.Data.errorCount += errorCount;
                        tempSize = errorCount;
                    }

                    var batchPlatformOrderIds = '批次' + batchNumber + "：";

                    var idx = (batchNumber - 1) * orderPrintBatchNumber;
                    for (var j = idx; j < idx + tempSize; j++) {
                        batchPlatformOrderIds += models_copy[j].PlatformOrderId + ',';
                    }
                    result.Data.WaybillCode.push({
                        IsError: true,
                        BatchPlatformOrderIds: batchPlatformOrderIds.trimEndDgj(','),
                        ErrMsg: faildRsp[i].Message
                    });
                }

            } catch (e) {
                var errorMsg = "所有请求完成，汇总错误异常》" + e.stack;
                console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }

            //校验服务器返回的数据与请求发送的数据的一致性
            try {
                var difference = {
                    RspsNotInReqs: [], //返回的数据不在请求中
                    RepeatDocuments: [], //返回的重复面单
                }; //差异对象
                //后台返回的数据不在请求数据中
                if (notInRequestDict.length > 0) {
                    difference.RspsNotInReqs = notInRequestDict;
                }
                for (var key in documentDict) {
                    if (documentDict[key] > 1) {
                        difference.RepeatDocuments.push(key); //重复的面单号
                    }
                }

                //数据不一致，则记录日志
                if (difference.RspsNotInReqs.length > 0 || difference.RepeatDocuments.length > 0) {
                    common.JsLogToMongoDB("快递单打印后端返回的数据和请求数据不一致", JSON.stringify(difference));
                }

            } catch (e) {
                var errorMsg = "数据不一致日志记录报错》" + e.stack;
                console.log(errorMsg);
                common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
            }


            //处理返回的结果
            if (!result.Success) {
                try {
                    layer.closeAll();
                    if (result.ErrorCode) {
                        switch (result.ErrorCode) {
                            case 'branch_not_exist':
                                var url = common.rewriteUrl('/AccountList/');
                                layer.alert(result.Message + " <a href='" + url + "'>点击去电子面单账号管理</a>");
                                break;
                            case 'branch_addr_change':
                                var url = common.rewriteUrl('/TemplateSet/');
                                layer.alert(result.Message + " <a href='" + url + "'>点击去模板管理</a>");
                                break;
                            case 'taobao_branch_errcode27':
                                branch_past(template, false);
                                break;
                            default:
                                layer.alert(result.Message);
                                break;
                        }
                    }
                    else {
                        layer.alert(result.Message);
                    }
                } catch (e) {
                    var errorMsg = "所有请求完成，汇总提示异常》" + e.stack;
                    console.log(errorMsg);
                    common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                }
            } else {

                var data = result.Data;
                if (data.successCount > 0) {

                    try {

                        //1.添加面单到订单行对象
                        var successWaybillCodes = [];
                        $(data.WaybillCode).each(function (index, w) {
                            if (!w.IsError) {
                                //var ix = $(".order-chx[data-id='" + w.OrderId + "']").attr("data-index");
                                var row = null;//otb.rows[ix];
                                common.Foreach(otb.rows, function (i, o) {
                                    if (o.Id == w.OrderId) {
                                        row = o;
                                        return 'break;';
                                    }
                                });

                                if (row == null) {
                                    throw "根据订单Id【" + w.OrderId + "】未找到订单Row对象";
                                }
                                //row.LastExpressPrintTime = 1;
                                //添加到单号中,最新打印，加到前面
                                if (!row.WaybillCodes) row.WaybillCodes = [];
                                //原单号重打不添加
                                var isExist = false;
                                common.Foreach(row.WaybillCodes, function (ii, wc) {
                                    if (wc.WaybillCodeId == w.WaybillCodeId && wc.WaybillCode == w.WaybillCode) {
                                        isExist = true;
                                        return 'break';
                                    }
                                });
                                if (isExist == false) {
                                    row.WaybillCodes.unshift({
                                        TemplateId: template.Id,
                                        TemplateType: template.TemplateType,
                                        ExpressCpCode: template.ExpressCompanyCode,
                                        WaybillCodeId: w.WaybillCodeId,
                                        WaybillCode: w.WaybillCode,
                                        CompanyCode: template.ExpressCompanyCode
                                    });
                                }
                                successWaybillCodes.push(w);
                                //赋值orders 对象中的waybillcode字段，让预览后点击打印按钮，后台能收到预览的waybillcode，从而打印出相同的单号
                                common.Foreach(orders, function (i, o) {
                                    if (o.Id == w.OrderId) {
                                        o.WaybillCode = w.WaybillCode;
                                    }
                                });
                            }
                        });

                    } catch (e) {
                        var p_msg = "";
                        try {
                            p_msg += JSON.stringify(data);
                            p_msg += '此时订单行数据：' + JSON.stringify(otb.rows);
                        } catch (ex) {
                            wcs = "data 或 otb.rows 对象转String报错了." + ex.stack;
                        }
                        var errorMsg = "添加面单到订单行对象异常》" + p_msg + "》" + e.stack;
                        console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    try {
                        //2.更新页面状态
                        otb.changeTemplate(template);
                    } catch (e) {
                        var errorMsg = "更新页面状态异常》" + e.stack;
                        console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    try {
                        //3.记录打印日志或者预览打印操作设置
                        if (isPreView == false) {
                            //记录打印日志
                            _writePrintLogToServer(data, successWaybillCodes, template, printerName, orders, true);
                        }
                        else {
                            //设置 预览页面的打印按钮功能 //doPrint(orders, template, printType, packageCount, isPreView, printerName, printedCallback, printerIndex)
                            common.SetPrintAction(ep.doPrint.bind(this, orders, template, 'Normal', packageCount, false, printerName, printedCallback, printerIndex));

                            //更新预览标志
                            _wirtePreviewFlagToOrder(orders);
                        }
                    } catch (e) {
                        var errorMsg = "记录打印日志或者预览打印操作设置异常》" + e.stack;
                        console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    try {
                        data.ExpressCompanyCode = template.ExpressCompanyCode; //复制快递编码，用于控制 运单号条形码显不显示 文字的问题。
                        //4.发送到打印机打印
                        ep.postPrintCommond(data, printerName, printerIndex);

                        //5.绑定打印机信息
                        common.SetPrinterBind(template.Id, 1, printerName, true);

                        //6.保存普通模板 单号
                        common.SaveTraditionWaybillCodeConfig(template);

                        //检查可用单号
                        addTmplInOrderListModule.CheckQty(false, template, false);

                    } catch (e) {
                        var errorMsg = "4，5，6步异常》" + e.stack;
                        console.log(errorMsg);
                        common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
                    }

                    if (data.errorCount <= 0)
                        layer.closeAll();
                    if (typeof printedCallback == 'function')
                        printedCallback();

                }
                layer.close(printing);

                var sendOrderId = 0; //发货的订单id。单个订单
                if (orders.length == 1)
                    sendOrderId = orders[0].Id;

                if (data.errorCount > 0) {
                    //显示错误信息
                    data.Template = template;
                    data.IsShowSendBtn = (isPreView == false && data.errorCount < orders.length); //显示发货按钮
                    data.SendOrderId = sendOrderId; //发货的订单id。单个订单
                    _showErrorMessage(data);
                }
                else if (common.isCustomerOrder() == false && isPreView == false) {
                    //全部成功
                    if (commonModule.SystemoConfig.AutoSendSetVal == 1) {
                        sendLogistic.send(false, sendOrderId, true); //发货
                    }
                    else {
                        data.Template = template;
                        data.IsNotShowError = true; //不显示订单错误信息
                        data.IsShowSendBtn = (isPreView == false); //显示发货按钮
                        data.SendOrderId = sendOrderId; //发货的订单id。单个订单
                        _showErrorMessage(data);
                    }
                }
            }
        }

        try {
            ///Order/ExpressPrint  /Common/TestBatchAjax
            common.posts('/Order/ExpressPrint', models, GetRequestModel, SingleCallBack, AllDoneCallback, orderPrintBatchNumber);//, "打印中...");
        } catch (e) {
            var errorMsg = "分批发送请求异常》" + e.stack;
            console.log(errorMsg);
            common.JsExcptionLog("快递单打印前端异常日志", errorMsg);
        }
    }

    function getOnlyCode() {
        return (new Date()).getTime() + "_" + Math.random().toString().replace('.', '').substring(0, 6);
    }

    function branch_past(template, isFree) {
        if (isFree)
            $('#div-branch-past-content-one').show();
        else
            $('#div-branch-past-content-one').hide();


        layer.open({
            type: 1,
            title: "电子面单账号授权过期",
            content: $('#div-branch-past-show'),
            area: ['1200', '300'], //宽高
            cancel: function () {
                //layer.close(branch_past_show);
                $('#div-branch-past-show').hide();
            },

        });
        var accountname = "";
        var interfaceTemplate = template;
        if (interfaceTemplate != null)
            accountname = interfaceTemplate.AuthAccountName;

        common.Ajax({
            url: '/AccountList/BranchPastLink',
            data: {},
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }

                console.log(rsp.Data);
                console.log(accountname);
                var dates = {
                    links: rsp.Data.taobaoAuthUrl,
                    accountname: accountname
                }

                var dialog = $.templates("#branch-past-show-tmpl");
                var htmls = dialog.render(dates);
                $("#div-branch-past-content").html(htmls);
            }
        });
    }


    //自由打印，单个打印
    //必填参数orders订单数据
    //必填参数template 模板数据
    //必填参数printType 有四个值：Normal 正常打印 需获取上一次打印的包裹ID，OneToMany 一单多包，Reprint 重打， NewPrint 新单号打印
    //选填参数 printedPackageId：已打印的pid，当重打的时候需提供 只有一个订单
    //选填参数 packageCount：面单数量，仅当打印一单多包和快运时需提供，否则默认为1
    //选填参数 printedCallback：打印后的回调函数
    ep.doPrint_FreePrintSingle = function doPrint(orders, template, isPreView, printerName, printerIndex, packageCount, printedCallback) {
        var printType = 'OneToMany';
        if (common.IsNormalTemplate(template.TemplateType) && packageCount == 1) { //template.TemplateType == 3 || template.TemplateType == 1
            printType = "Normal";
        }
        if (common.IsKuaiYunTemplate(template.TemplateType)) {//template.TemplateType == 7 || template.TemplateType == 8) {
            printType = "PrintKuaiYun"; //打印快运面单
        }

        var packageDesc = null, goodsDesc = null;
        var packageDescCtrol = $('#sel_package_desc'), goodsDescCtrol = $('#sel_goods_desc');
        if (packageDescCtrol.length > 0 && packageDescCtrol.val().trim() != '0' && packageDescCtrol.val().trim() != '') {
            packageDesc = packageDescCtrol.val().trim();
        }
        if (goodsDescCtrol.length > 0 && goodsDescCtrol.val().trim() != '0' && goodsDescCtrol.val().trim() != '') {
            goodsDesc = goodsDescCtrol.val().trim();
        }

        var request = {
            TemplateId: template.Id,
            PackageCount: (printType == "OneToMany" || printType == "PrintKuaiYun") ? packageCount : 1,
            PackageDesc: packageDesc,
            GoodsDesc: goodsDesc,
            PrintMethod: printType,
            IsPreview: isPreView,
        };

        ep.defaultPrinter = printerName;

        request.PrinterName = printerName;

        var toFullNameIsNullOrders = [];
        var models = [];
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var model = {
                WaybillCodeId: "",
                Id: order.Id,
                PlatformOrderId: order.PlatformOrderId,
                CustomerOrderId: order.CustomerOrderId,
                ShopId: order.ShopId,
                PrintInfo: order.PrintInfo,
                OrderItems: order.OrderItems,
                WaybillCode: order.WaybillCode,
                SellerRemark: order.SellerRemark,
                BuyerRemark: order.BuyerRemark,
                Receiver: {
                    toFullName: common.DeleteRareWords(order.Receiver.ToName),
                    toMobile: order.Receiver.ToMobile,
                    toArea: common.DeleteRareWords(order.Receiver.ToAddress),
                    buyerMemberId: order.Buyer.BuyerMemberId
                },
                Sender: {
                    SenderName: common.DeleteRareWords(order.Sender.SenderName),
                    SenderPhone: order.Sender.SenderPhone,
                    SenderAddress: common.DeleteRareWords(order.Sender.SenderAddress),
                    CompanyName: order.Sender.SenderCompany,
                },
                Buyer: {
                    BuyerMemberId: order.Buyer.buyerMemberId,
                    BuyerMemberName: order.Buyer.BuyerMemberName,
                    BuyerWangWang: order.Buyer.BuyerWangWang
                }
            };

            if (model.Receiver.toFullName == "") {
                toFullNameIsNullOrders.push(model.PlatformOrderId);
            }
            models.push(model);
        };

        if (toFullNameIsNullOrders.length > 0) {
            layer.alert("订单【" + toFullNameIsNullOrders.join(",") + "】收件人姓名为空或者全为特殊字符，会导致打单失败，请更改后再打印。", function (index) { layer.closeAll(""); })
        }

        //删除危险字符
        models = JSON.parse(common.DelDangerChar(JSON.stringify(models)));

        request.Orders = models;
        request.RequestBatchNumber = "FreeSingle/" + common.GetRequestBatch();
        common.Ajax({
            url: "/Order/ExpressPrint",
            data: request,
            loadingMessage: "正在打印",
            success: function (result) {
                //处理返回的结果
                if (!result.Success) {
                    layer.closeAll();
                    var msgFix = isPreView == true ? "预览" : "打印";
                    if (result.ErrorCode) {
                        switch (result.ErrorCode) {
                            case 'branch_not_exist':
                                var url = common.rewriteUrl('/AccountList/');
                                layer.alert(msgFix + '失败：' + result.Message + " <a href='" + url + "'>点击去电子面单账号管理</a>" + '<br/>订单已保存至待打印列表.');
                                break;
                            case 'branch_addr_change':
                                var url = common.rewriteUrl('/TemplateSet/');
                                layer.alert(msgFix + '失败：' + result.Message + " <a href='" + url + "'>点击去模板管理</a>" + '<br/>订单已保存至待打印列表.');
                                break;
                            case 'taobao_branch_errcode27':
                                branch_past(template, true);
                                break;
                            default:
                                layer.alert(msgFix + '失败：' + result.Message + '<br/>订单已保存至待打印列表.');
                                break;
                        }
                    }
                    else {
                        layer.alert(msgFix + '失败：' + result.Message + '<br/>订单已保存至待打印列表.');
                    }
                } else {
                    var data = result.Data;
                    if (data.successCount > 0) {
                        //预览
                        if (isPreView == true) {
                            $(data.WaybillCode).each(function (index, w) {
                                if (!w.IsError) {
                                    //赋值orders 对象中的waybillcode字段，让预览后点击打印按钮，后台能收到预览的waybillcode，从而打印出相同的单号
                                    common.Foreach(orders, function (i, o) {
                                        if (o.Id == w.OrderId) {
                                            o.WaybillCode = w.WaybillCode;
                                        }
                                    });
                                }
                            });
                            //设置 预览页面的打印按钮功能
                            common.SetPrintAction(ep.doPrint_FreePrintSingle.bind(this, orders, template, false, printerName, printerIndex, packageCount, printedCallback));
                            //更新预览标志
                            _wirtePreviewFlagToOrder(orders);
                        }
                        else { //打印

                            //获取打印成功的单号
                            var successWaybillCodes = [];
                            $(data.WaybillCode).each(function (index, w) {
                                if (!w.IsError) {
                                    successWaybillCodes.push(w);
                                }
                            });
                            //记录打印日志
                            _writePrintLogToServer(data, successWaybillCodes, template, printerName, orders, false);
                            //绑定打印机信息
                            common.SetPrinterBind(template.Id, 1, printerName, true);
                            if (data.errorCount <= 0)
                                layer.closeAll();
                            if (typeof printedCallback == 'function')
                                //打印回调
                                printedCallback(successWaybillCodes);
                        }

                        data.ExpressCompanyCode = template.ExpressCompanyCode; //复制快递编码，用于控制 运单号条形码显不显示 文字的问题。
                        //发送到打印机打印
                        ep.postPrintCommond(data, printerName, printerIndex);
                    }
                    if (data.errorCount > 0) {
                        //显示错误信息
                        data.Template = template;
                        _showErrorMessage(data);
                    }
                }
            }
        });
    }

    ep.ScanPrintPostToLodop = function (data, printerIndex) {
        postToLodop(data, printerIndex);
    }

    function postToLodop(data, printerIndex) {
        var list = data.TemplateList;
        var pageSize = 20;
        var tasks = [];
        while (list.length > 0) {
            var temp = list.splice(0, pageSize);
            if (temp != null && temp.length > 0)
                tasks.push(temp);
            else
                break;
        }
        var totalDocumentCount = 0;
        for (var i = 0; i < tasks.length; i++) {
            var task = tasks[i];
            totalDocumentCount += task.length;
        }
        var startIndex = 0;
        for (var i = 0; i < tasks.length; i++) {
            var task = tasks[i];
            data.TemplateList = task;
            postToLodopByPaged(data, printerIndex, startIndex, totalDocumentCount);
            startIndex += task.length;
        }
    }

    function postToLodopByPaged(data, printerIndex, startIndex, totalDocumentCount) {
        //console.log(data);
        var expressCompanyCode = data.ExpressCompanyCode;
        //console.log(expressCompanyCode)
        try {
            var w = data.PageWidth;
            var h = data.PageHeight;
            var ox = parseFloat(data.PageX);
            var oy = parseFloat(data.PageY);
            var result;
            var isPrintTypeGlobal = 0;
            var list = data.TemplateList;
            var listDocSize = totalDocumentCount || list.length;
            LODOP.PRINT_INIT("店管家快递单打印");
            var sepid = 1;
            for (var i = 0; i < list.length; i++) {
                LODOP.SET_PRINT_PAGESIZE(1, w + "mm", h + "mm", "");
                var item = list[i];
                if (!item.InputText)
                    item.InputText = "";
                var els = item.TemplateInputList;
                for (var j = 0; j < els.length; j++) {
                    var el = els[j];
                    var top = parseFloat(el.Y) + oy;
                    var left = parseFloat(el.X) + ox;
                    var iw = el.Width;
                    var ih = el.Height;
                    var txt = unescape(el.InputText);
                    if (el.controlId == 'WaybillCodeSerialCode') {
                        txt = ((i + 1) + startIndex) + "/" + listDocSize;
                    }
                    if (txt == "")
                        continue;
                    if (el.controlType == "7") { //二维码
                        if (el.controlId == 'sfqrcode' || el.controlId == 'anqrcode') {
                            LODOP.ADD_PRINT_BARCODE(top, left, 100 + 'px', 100 + 'px', "QRCode", el.InputText);
                        }
                        else if (el.controlId == 'qrcode') {
                            LODOP.ADD_PRINT_BARCODE(top, left, iw + 'px', iw + 'px', "QRCode", el.InputText);
                        }
                    }
                    else if (el.IsCode != "" && el.IsCode != undefined) {
                        LODOP.ADD_PRINT_BARCODE(top, left, iw, ih, el.IsCode, txt);
                        if (el.controlId == "onecode_vertical")
                            LODOP.SET_PRINT_STYLEA(0, "Angle", -90);
                        if (expressCompanyCode == "SF" || expressCompanyCode == "JD")
                            LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 0);
                    } else if (txt == "sxh001" || txt == "sxs001" || txt == "xxh001" || txt == "xxs001") {
                        if (txt == "sxh001")  //横线（实体）
                            LODOP.ADD_PRINT_LINE(top, left, top, left + iw, 0, el.FontSize);

                        if (txt == "sxs001") //竖线（实体）
                            LODOP.ADD_PRINT_LINE(top, left, top + ih, left, 0, el.FontSize);

                        if (txt == "xxh001") //横线（虚线）
                            LODOP.ADD_PRINT_LINE(top, left, top, left + iw, 2, el.FontSize);

                        if (txt == "xxs001") //竖线（虚线）
                            LODOP.ADD_PRINT_LINE(top, left, top + ih, left, 2, el.FontSize);
                    } else if (el.controlType == "8" && el.InputText != '') { //图片

                        LODOP.ADD_PRINT_IMAGE(top, left, iw, ih, "<img border='0' style='" + "width:" + iw + ";height:" + ih + ";" + "' src='" + el.InputText + "' />");
                        LODOP.SET_PRINT_STYLEA(0, "Stretch", 2);
                    }
                    else {
                        if (el.IsText) {
                            if (el.highlimit == "1" || isPrintTypeGlobal == 3)
                                LODOP.ADD_PRINT_TEXT(top, left, iw, ih, txt);
                            else
                                LODOP.ADD_PRINT_TEXT(top, left, iw, ih, txt);

                            if (isPrintTypeGlobal == 3)
                                LODOP.SET_PRINT_STYLEA(sepid, "ReadOnly", 0);

                            LODOP.SET_PRINT_STYLEA(sepid, "LineSpacing", -4); //设置行间距
                            LODOP.SET_PRINT_STYLEA(sepid, "FontName", el.FontFamily); //el.FontFamily设置字体大小
                            LODOP.SET_PRINT_STYLEA(sepid, "FontSize", el.FontSize); //设置字体大小
                            LODOP.SET_PRINT_STYLEA(sepid, "Bold", el.FontWeight); //设置加粗
                            if (el.controlId == "ShortAddress") {
                                LODOP.SET_PRINT_STYLEA(sepid, "Alignment", 2); //设置行间距
                            }
                        } else {
                            var strHtml = txt.replace(eval("/<br>/gi"), "\n");
                            if (strHtml == "null")
                                continue;
                            if (el.highlimit == "1" || isPrintTypeGlobal == 3)
                                LODOP.ADD_PRINT_TEXT(top, left, iw, ih, strHtml);
                            else
                                LODOP.ADD_PRINT_TEXT(top, left, iw, ih, strHtml);

                            if (isPrintTypeGlobal == 3)
                                LODOP.SET_PRINT_STYLEA(sepid, "ReadOnly", 0);

                            LODOP.SET_PRINT_STYLEA(sepid, "LineSpacing", -4); //设置行间距
                            LODOP.SET_PRINT_STYLEA(sepid, "FontName", el.FontFamily); //设置字体大小
                            LODOP.SET_PRINT_STYLEA(sepid, "FontSize", el.FontSize); //设置字体大小
                            LODOP.SET_PRINT_STYLEA(sepid, "Bold", el.FontWeight); //设置加粗
                            if (el.controlId == "ShortAddress") {
                                LODOP.SET_PRINT_STYLEA(sepid, "Alignment", 2); //设置行间距
                            }
                        }
                    }

                    sepid++;
                }

                if (i + 1 < list.length) {
                    LODOP.NewPageA();
                }
            }
            if (list.length <= 0)
                return;
            LODOP.SET_PRINT_COPIES(1);
            lp.setPrinter(printerIndex);
            result = LODOP.PRINT();
        } catch (e) {
            layer.alert("打印出现异常,尝试调整快递模版再打印!");
        }
    }

    function _showErrorMessage(data) {
        var dialog = $.templates("#print-express-error-dialog-tmpl");
        data.IsCustomerOrder = common.isCustomerOrder();
        var html = dialog.render(data);

        var opts = {
            type: 1,
            title: "快递单打印结果",
            //closeBtn: 1,
            btn: ["关闭"],
            shadeClose: true,
            area: ['600px', '350px'],
            content: html,
            yes: function () {
                layer.closeAll();
            },
            cancel: function () {
                layer.closeAll();
            }
        };

        if (data.IsShowSendBtn == true && common.isCustomerOrder() == false) {
            opts = $.extend(opts, {
                btn: [data.IsCustomerOrder ? "标记为已发货" : "发货", "关闭"],
                yes: function () {
                    if (data.IsCustomerOrder) {
                        freePrintOrderOperation.send();
                    }
                    else {
                        if (!data.IsNotShowError) {
                            sendLogistic.SetIgnoreOrderFlag(true); //设置发货时，忽略运单号为空的订单
                        }
                        sendLogistic.send(false, data.SendOrderId);
                    }
                },
                btn1: function () {
                    layer.closeAll();
                }
            });
        }
        layer.open(opts);

    }

    //发送打印内容到打印组件（菜鸟或Lodop）
    ep.postPrintCommond = function postPrintCommond(data, printerName, printerIndex) {

        //记录发送到打印机的数据
        try {
            if (typeof printExpressAllRspLog != 'undefined' && printExpressAllRspLog == true) {
                common.JsLogToMongoDB("快递单打印发送到打印机的数据", JSON.stringify(data));
            }
        } catch (e) { }

        //发送到打印机
        if (data.TemplateList && data.TemplateList.length > 0) {
            //设置打印机
            //自定义模板
            //lp.setPrinter(printerName);
            postToLodop(data, printerIndex);
        } else if (data.PrintDataList) {
            if (printComponents == "Cainiao") {
                //设置打印机
                data.PrinterConfig.printer.name = printerName;
                caiNiao.send(JSON.stringify(data.PrinterConfig));

                if (data.PrintDataList.TaskList && data.PrintDataList.TaskList.length > 0) {
                    //循环发送task
                    for (var i = 0; i < data.PrintDataList.TaskList.length; i++) {
                        var task = data.PrintDataList.TaskList[i];
                        task.task.printer = printerName;
                        task.task.totalDocumentCount = document_total_count;
                        caiNiao.send(JSON.stringify(task));
                    }
                }
                else if (data.PrintDataList.task) {
                    data.PrintDataList.task.printer = printerName;
                    caiNiao.send(JSON.stringify(data.PrintDataList));
                }
            }
            else if (printComponents == "Pinduoduo") {
                //设置打印机
                data.PrinterConfig.printer.name = printerName;
                pdd.send(JSON.stringify(data.PrinterConfig));

                if (data.PrintDataList.TaskList && data.PrintDataList.TaskList.length > 0) {
                    //循环发送task
                    for (var i = 0; i < data.PrintDataList.TaskList.length; i++) {
                        var task = data.PrintDataList.TaskList[i];
                        task.task.printer = printerName;
                        task.task.totalDocumentCount = document_total_count;
                        pdd.send(JSON.stringify(task));
                    }
                }
                else if (data.PrintDataList.task) {
                    data.PrintDataList.task.printer = printerName;
                    pdd.send(JSON.stringify(data.PrintDataList));
                }
            }
            else {
                layer.alert("打印之前请先指定打印组件.");
            }
        }
    }

    _PrintedCallbackUrl = "";

    //写打印日志
    function _writePrintLogToServer(data, waybillCodes, template, printerName, orders, isUpdatePrintState) {
        //组装数据
        if (!waybillCodes || waybillCodes.length <= 0)
            return;
        var logs = [];
        //var orders = otb.getSelections();

        commonModule.ServerNowStr = ''; //清空 批次
        commonModule.DateBatch = ''; //清空  批次序号

        var printHistoryIndexModelList = [];
        common.Foreach(data.PrintHistoryIds, function (i, ph) {
            var findCount = 0;
            for (var i = 0; i < waybillCodes.length; i++) {
                var wyb = waybillCodes[i];
                if (ph.WaybillCode == wyb.WaybillCode && ph.WaybillCodeChild == wyb.ChildWaybillCode) {
                    findCount++;
                    ph.BatchIndex = (i + 1);
                }
            }
            if (findCount > 1)
                console.log("Warring：回写打印记录发现多个值");
            printHistoryIndexModelList.push(ph);
        });

        var request = { TemplateId: template.Id, Orders: [], PrintType: 1, PrintDataType: 1, PrintHistoryIndexModels: printHistoryIndexModelList };
        for (var i = 0; i < waybillCodes.length; i++) {
            var curOrder = {};
            var wbc = waybillCodes[i];
            //订单的其他数据
            for (var j = 0; j < orders.length; j++) {
                var o = orders[j];
                if (o.Id == wbc.OrderId) {
                    curOrder.Id = o.Id;
                    curOrder.PlatformOrderId = o.PlatformOrderId;
                    curOrder.ShopId = o.ShopId;
                    curOrder.WaybillCode = wbc.WaybillCode;
                    curOrder.OrderItems = o.OrderItems;
                    break;
                }
            }
            request.Orders.push(curOrder);
        }
        common.Ajax({
            url: (_PrintedCallbackUrl || "/Order/PrintCallback"),
            data: request,
            success: function (res) {
                if (res.Success && isUpdatePrintState) {
                    //if (common.isCustomerOrder())
                    //    $("#search-btn").click(); //自由打印，打印完成后，数据会到打印历史里，所以刷新界面。
                    //else
                    otb.setPrintState(res.Data, 1);
                }
                //console.log(res);
            }
        });
        return logs;
    }

    //更新预览标识
    function _wirtePreviewFlagToOrder(orders) {

        var selectKeyModelList = [];
        common.Foreach(orders, function (i, o) {
            selectKeyModelList.push({
                ShopId: o.ShopId,
                PlatformOrderId: o.PlatformOrderId
            });
        });

        common.Ajax({
            url: "/Order/UpdateIsPreviewFlag",
            data: { selectKeyModelList: selectKeyModelList },
            success: function (rsp) {
                if (common.IsError(rsp)) {
                    return;
                }
            }
        });
    }

    $(document).ready(function () {
        //$(".express-print-onetomany-btn").unbind("click").bind("click", function () {
        //    ep.printOneToMany();
        //});
    });
    return ep;
}(expressPrinter || {}, commonModule, caiNiaoPrinter, pinDuoDuoPrinter, lodopPrinter, addTmplInOrderListModule, orderTableBuilder, jQuery));
