/// <reference path="PrintContentFormatSetModule.js" />
/// <reference path="CommonModule.js" />

//订单表格生成
var orderTableBuilder = (function (table, printContentFormatSetModule, common, $) {

    var _pageIndex = 1, _pageSize = 20;
    var isQueryPlatfromOrderIdOrWaybillCode = false;
    var orderClassifyArr = [];
    var quickScreenOrderField = "";
    var platformType = (commonModule.PlatformType || "").toLowerCase();
    //var pddPrivacyNumberAPIReg = /\$(1[0-9]{10})\$#([0-9]{4})#|\$(95[0-9]{5})\$#([0-9]{8})#/; //拼多多API隐私号检测正则
    var pddPrivacyNumberOPLBReg = /(1[0-9]{10})#([0-9]{4})|(95[0-9]{5})#([0-9]{8})/; //拼多多OPLB隐私号检测正则

    var defaultHeaderFormatter = function (col) {
        if (col.headerFormattter) {
            return col.headerFormattter(col);
        }
        else {
            return (col.aliasName && col.aliasName != "" ? col.aliasName : col.name);
        }

    };
    var defaultContentFormatter = function (row, col, index, ext) {
        if (!row.Id)
            return "";
        if (col.contentFormatter) {
            return col.contentFormatter(row, col, index, ext);
        }
        else if (row && col.field) {
            var content = row[col.field];
            if (!content)
                content = "";
            return content;
        }
        else
            return "";
    };
    var DetailHearderFormatter = function (col) {
        return "<div class='particulars'><span class='particulars_span' style='margin-right:20px;'>详情</span><span class='particulars_icon allZK' id='particularsIcon' title='展开所有订单'></span></div>"
    }

    var ProductHearderFormatter = function (col) {
        return "<div class='productHearder'><span class='Product_span'>" + col.name + "</span><i class='productHearder_icon' onclick='orderPrintModule.OpenSetProduct(this)'></i></div>"
    }

    var IsSendExpressHeaderFormatter = function (col) {
        return '<span style="color:#fe6f4f;" title="检查当前快递是否可派送">' + col.name + '</span>';
    }
    var OrderClassifyHearderFormatter = function () {
        return '<span>订单分类<i class="OrderClassify_iconSet" onclick="orderPrintModule.OpenUpdateOrderCategoryPanel()"></i></span>'
    }
    var IsSendExpressContentFormatter = function (row, col, index, ext) {
        return '<span  id="is-express-send-span-' + row.Id + '" style="color:green;" title="当前快递支持派送到该地区">√</span>';
        //if (row.IsExpressSend == undefined)
        //    return '<span  id="is-express-send-span-' + row.Id + '" style="color:red;"></span>';
        //var is = '<span id="is-express-send-span-' + row.Id + '" style="color:red;font-weight:bold;" title="不确定是否可达，请咨询快递公司确认">!</span>';
        //var is = '<span id="is-express-send-span-' + row.Id + '" style="color:red;font-weight:bold;">×</span>';
        //if (row.IsSendExpress)
        //    is = '<span  id="is-express-send-span-' + row.Id + '" style="color:green;font-weight:bold;">√</span>';
    }
    var PrintStateContentFormatter = function (row, col, index, isNotFromOrderList) {

        var html = '';
        html += '<div onclick="stopM(event)" class="table_content_dagou_suo" id="table_suo' + row.Id + '" onclick=\'tableSuo(this,"' + row.Id + '")\' style="' + (row.IsLocked == 1 ? "display:inline-block;" : "display:none;") + '"><img src="/Content/Images/suotou.png" width="13px" height="15px" alt="" onclick=\'tableSuo(this,"' + row.Id + '")\'></div>';
        html += '<div style="width:40px">';
        html += '<div class="table_content_dagou_moreFun">'
        html += '<div style="min-width:50px;display:inline-block;margin-left:5px;">'

        if (!commonModule.isCustomerOrder() && !commonModule.isUseOldTheme && isQueryPlatfromOrderIdOrWaybillCode) {
            var status = "";
            if (common.IsPddFds()) {
                //0:取消分配，1:待打印
                var platformStatus = row.PlatformStatus || "";
                if (platformStatus == "0") {
                    if (row.ExtField3 == "1") {
                        status = "已回传，取消分配";
                    }
                    else if (row.LastExpressPrintTime && row.ExtField3 != "1") {
                        status = "已打印，取消分配";
                    }
                    else {
                        status = "取消分配";
                    }
                }
                else if (platformStatus == "1") {
                    if (row.LastExpressPrintTime && row.ExtField3 == "1") {
                        status = "已打印，已回传";
                    }
                    else if (row.LastExpressPrintTime && row.ExtField3 != "1") {
                        status = "已打印，未回传";
                    }
                    else if (row.ExtField3 == "1") {
                        status = "已回传";
                    }
                    else {
                        status = "待打印";
                    }
                }
            }
            else {
                var platformStatus = row.PlatformStatus || "";
                if (platformStatus == "waitbuyerpay")
                    status = "待付款";
                else if (platformStatus == "waitsellersend")
                    status = "待发货";
                else if (platformStatus == "waitbuyerreceive")
                    status = "已发货";
                else if (platformStatus == "success")
                    status = "交易成功";
                else if (platformStatus == "cancel")
                    status = "交易关闭";
                else if (platformStatus == "confirm_goods_but_not_fund")
                    status = "货到付款";
            }
            html += '<span style="border-radius:3px;color:#fff;background-color:#3aadff;padding:1px 5px;margin-right:5px;display:inline-block;margin-top:2px;text-align: center;"><span style="display:inline-block;">' + status + '</span></span>';
            //if (row.IsMergered)
            //    html += '<span style="border-radius:3px;color:#fff;background-color:#f5821f;padding:1px 5px;margin-right:5px;display:inline-block;margin-top:2px;text-align: center;"><span style="display:inline-block;">被合并</span></span>';
        }

        if (row.ChildOrderId) {
            var titlePrefix = "自动";
            if (row.IsMergeredByHand)
                titlePrefix = "手工";
            html += '<span title="' + titlePrefix + '合并的订单" style="border-radius:3px;color:#fff;background-color:#f5821f;padding:1px 2px;display:inline;margin-top:2px;text-align: center;" data-num="' + row.SubOrders.length + '" class="subOrdersNum"><span style="display:inline-block;">合单</span>' + row.SubOrders.length + '</span>';
        }
        else if (row.ParentOrderId)
            html += '<span title="拆分订单" style="border-radius:3px;color:white;background-color:#09f;padding:1px;margin-left:2px;text-align: center;">拆</span>';

        html += '</div>'
        var couldMergerOrders = [];
        if (quickScreenOrderField == "TipMergeOrder" || table.Setting.IsShowMergerTips) {
            if (table.Setting.IsNotAutoMerger) {
                if (row.CouldMergerOrderIdExt)
                    couldMergerOrders = row.CouldMergerOrderIdExt.split(',');
            } else {
                if (row.CouldMergerOrderId)
                    couldMergerOrders = row.CouldMergerOrderId.split(',');
            }
        }
        if (couldMergerOrders.length > 0) {
            var length = 0;
            for (var i = 0; i < couldMergerOrders.length; i++) {
                var co = couldMergerOrders[i];
                if (co && co.length > 0 && co[0] != "C")
                    length++;
            }
            if (!isNotFromOrderList && length > 1)
                html += '<div onclick="stopM(event)" class="table_content_dagou_merge"><span>此买家存在 ' + length + '个可合并订单<i ' + (row.IsLocked == "1" ? " style='color:#444444;'" : "onclick=\'mergerOrderModule.mergerBySuggest(\"" + index + "\"," + table.Setting.IsNotAutoMerger + ")\'") + '  >手工合并</i></span></div>';
        }
        html += '</div>'

        if (row.LastExpressPrintTime)
            html += '<span style="color:#01a701;" title="' + (row.ExpressPrintedCount < row.ProductCount ? "[部分]" : "[全部]") + '已打印快递单">' + (row.ExpressPrintedCount < row.ProductCount ? "<i>\/</i><i style='position: relative;left:-5px'>\/</i>" : "√") + '</span>';
        //else
        //    html += "&nbsp;";
        if (row.LastSendPrintTime)
            html += '<span style="color:#1ec6fe" title="' + (row.FahuoPrintedCount < row.ProductCount ? "[部分]" : "[全部]") + '已打印发货单">' + (row.FahuoPrintedCount < row.ProductCount ? "<i>\/</i><i style='position: relative;left:-5px'>\/</i>" : "√") + '</span>';
        //else
        //    html += "&nbsp;";
        if (row.LastNahuoPrintTime)
            html += '<span style="color:#ff0000" title="' + (row.NaHuoPrintedCount < row.ProductCount ? "[部分]" : "[全部]") + '已打印拿货单">' + (row.NaHuoPrintedCount < row.ProductCount ? "<i>\/</i><i style='position: relative;left:-5px'>\/</i>" : "√") + '</span>';
        //else
        //    html += "&nbsp;";
        html += '</div>';
        return html;
    }
    var checkboxContentFormatter = function (row, col, index, classNamePrefix) {
        var className = "order-chx";
        if (classNamePrefix)
            className = classNamePrefix + className;
        var html = '<input type="checkbox" class="' + className + '" data-id="' + row.Id + '" data-pid="' + row.PlatformOrderId + '" data-sid="' + row.ShopId + '" data-index="' + index + '" ' + ((classNamePrefix || row.checked) ? " checked" : "") + '>';
        return html;
    }
    var ProductCountContentFormatter = function (row, col) {
        var number = row.ProductKindCount + "/" + row.ProductItemCount;
        //if (row.ChildOrderId)
        //    number += '<br><span title="合并订单" style="border-radius:3px;color:#fff;background-color:#f5821f;padding:1px;display:inline-block;margin-top:2px;width:50px;"><span style="display:inline-block;">合单</span>' + row.SubOrders.length + '</span>';
        //else if (row.ParentOrderId)
        //    number += '<span title="拆分订单" style="border-radius:3px;color:white;background-color:#09f;padding:1px;margin-left:2px;">拆</span>';
        var title = "共" + row.ProductKindCount + "款产品，总计" + row.ProductItemCount + "件";
        return '<span title="' + title + '">' + number + '</span>';
    }
    var AmountContentFormatter = function (row, col) {
        if (!row.TotalAmount)
            row.TotalAmount = 0;
        if (!row.ShippingFee)
            row.ShippingFee = 0;
        var number = row.TotalAmount + "/" + row.ShippingFee;
        var title = "支付金额：" + row.TotalAmount + (row.ShippingFee > 0 ? "，其中包括运费：" + row.ShippingFee : "，无运费");
        return '<span title="' + title + '">' + number + '</span>';
    }
    var DaiShouHuoKuanContentFormatter = function (row, col) {

        var isDsiabled = "";
        var orderStatus = row['PlatformStatus'];
        switch (orderStatus) {
            case "": //自由打印订单的状态为空
            case null: //自由打印订单的状态为空
            case "waitbuyerpay":
            case "confirm_goods_but_not_fund":
                isDsiabled = "";
                break;
            default:
                isDsiabled = "disabled title='只有待付款/货到付款的订单才能打印代收货款增值服务'";
                break;
        }

        isDsiabled = orderStatus == "waitsellersend" && commonModule.IsTouTiaoXi(row.PlatformType) ? "" : isDsiabled;
        var val = row[col.field];
        var isCod = row['IsSvcCOD'];
        val = (val == null || val == 0 || isCod == false) ? '' : val; //  || isDsiabled != '' 

        return '<input id="svcCOD_' + row.Index + '" onclick="stopM(event)" value="' + val + '" onblur="modifyOrderInfoModule.ModifyCODAmountOrInSureAmount.bind(this)(\'cod\',' + row.Index + ')" onfocus="modifyOrderInfoModule.CodOrInSureFocusHandler.bind(this)()"  type="text" style="border:1px solid #e2e2e2;height:20px;width:55px;"' + isDsiabled + ' />'
    }
    var BaoJiaJinEContentFormatter = function (row, col) {
        var val = row[col.field];
        var isInsure = row['IsSvcInsure'];
        val = (val == null || val == 0 || isInsure == false) ? '' : val;
        return '<input id="svcInsure_' + row.Index + '" onclick="stopM(event)" value="' + val + '" onblur="modifyOrderInfoModule.ModifyCODAmountOrInSureAmount.bind(this)(\'insure\',' + row.Index + ')" onfocus="modifyOrderInfoModule.CodOrInSureFocusHandler.bind(this)()"  type="text" style="border:1px solid #e2e2e2;height:20px;width:55px;"/>'
    }
    var addressTagFormatter = function (row, col) {
        var html = "";
        html += '<div class="address_type">'
        if (row.IsToCountyOrTown)
            html += '<span style="background-color:#3aadff;margin-top:3px;">乡镇</span>';
        //不管订单上有没有退款状态，按订单项上的为准

        var GAT = '澳门特别行政区香港特别行政区台湾省';
        if (GAT.indexOf(row.ToProvince) != -1) {
            html += '<span style="background-color:#973aff;margin-top:3px;">港澳台</span>';
        }


        if (row.RefundStatus || true) {
            var hasRefundSuccess = false;
            var hasRefundclose = false;
            var hasRefundPartSuccess = false;
            var hasRefundPart = false;
            var hasRefund = false;
            var refundAmount = 0;
            for (var i = 0; i < row.SubOrders.length; i++) {
                var sub = row.SubOrders[i];
                for (var j = 0; j < sub.OrderItems.length; j++) {
                    var oi = sub.OrderItems[j];
                    if (oi.RefundStatus == "REFUND_SUCCESS")
                        hasRefundSuccess = true;
                    //else if (oi.RefundStatus == "REFUND_CLOSE")
                    //    hasRefundclose = true;
                    else if (oi.RefundStatus == "REFUND_PART")
                        hasRefundPart = true;
                    else if (oi.RefundStatus == "REFUND_PART_SUCCESS")
                        hasRefundPartSuccess = true;
                    else if (oi.RefundStatus)
                        hasRefund = true;
                    refundAmount += oi.RefundAmount;
                }
            }
            if (hasRefundSuccess || row.RefundStatus == 'refundsuccess')
                html += '<span style="background-color: rgba(247,148,31,1);; " title="包含退款成功，退款金额：' + refundAmount + '元">包含退款成功</span>';
            else if (hasRefundclose || row.RefundStatus == 'refundclose')
                html += '<span style="background-color:green;opacity:0.7;display:none;" title="退款已关闭">包含退款关闭</span>';
            else if (hasRefundPart || row.RefundStatus == 'REFUND_PART')
                html += '<span style="background-color:rgba(247,148,31,1); " title="包含部分退款中，退款金额：' + refundAmount + '元">包含部分退款中</span>';
            else if (hasRefundPartSuccess || row.RefundStatus == 'REFUND_PART_SUCCESS')
                html += '<span style="background-color:rgba(247,148,31,1); " title="包含部分退款成功，退款金额：' + refundAmount + '元">包含部分退款成功</span>';
            else if (hasRefund || row.RefundStatus)
                html += '<span style="background-color:rgba(254,111,79,1)" title="包含退款中，退款金额：' + refundAmount + '元">包含退款中</span>';
        }
        if (row.SendedCount > 0 && row.SendedCount < row.ProductCount && row.PlatformStatus == 'waitsellersend')
            html += '<span style="background-color:#3cc3a2;">部分发货</span>';
        if (row.IsWeiGong) {
            if (platformType == 'pinduoduo') {
                html += '<span style="background-color:#ff6666;" title="风控订单">风控</span>';
            }
            else if (platformType == 'toutiao') {
                html += '<span style="background-color:#ff6666;" title="平台拆单">平台拆单</span>';
            }
            else if (platformType == 'jingdong') {
                html += '<span style="background-color:#ff6666;" title="用户收货不用付款，试用7天满意线上付款">先试后付</span>';
            }
            else {
                html += '<span style="background-color:#ff6666;" title="微供订单">微供</span>';
            }
        }
        if (row.PlatformType == "YouZan") {
            if (row.BusinessType == "1")
                html += '<span style="background-color:#ff6666;" title="到店自提">到店自提</span>';
            else if (row.BusinessType == "2")
                html += '<span style="background-color:#ff6666;" title="同城配送">同城配送</span>';
            if (row.OrderFrom == "3")
                html += '<span style="background-color:#ff6666;" title="分销买家订单">分销买家订单</span>';
            else if (row.OrderFrom == "-3")
                html += '<span style="background-color:#ff6666;" title="分销采购单">分销采购单</span>';
        }
        else if (row.PlatformType == "Alibaba") {
            if (row.Warehouse != undefined && row.Warehouse != '') {
                html += '<span style="background-color:brown;margin-top:3px;" title="零售通仓库类型">' + (row.Warehouse == "cainiao" ? "阿里仓" : "自有仓") + '</span>';
            }
            if (row.IsCantSendGood)
                html += '<span style="background-color:#ff6666;" title="' + row.CantSendReason + '">阻止发货</span>';
        }
        else if (row.PlatformType == "WeiMeng") {
            if (row.BusinessType == "3")
                html += '<span style="background-color:#ff6666;" title="到店自提">到店自提</span>';
        }
        if (row.PlatformType == "XiaoHongShu") {
            if (row.TradeType == "presale")
                html += '<span style="background-color:#ff6666;" title="预售">预售</span>';
            else if (row.TradeType == "exchange")
                html += '<span style="background-color:#ff6666;" title="补发货">补发货</span>';
        }
        else if (row.PlatformType == "DuXiaoDian") {
            if (row.TradeType == "PAYTYPE_OFFLINE")
                html += '<span style="background-color:#ff6666;" title="货到付款">货到付款</span>';
        }
        else if (row.PlatformType == "MoKuai") {
            //订单类型1: "正常" 3: "秒杀" 15: "拼团接龙订单" 20: "会员领样订单" 21: "免费领样订单"
            if (row.BusinessType == "3") {
                html += '<span style="background-color:#ff6666;" title="秒杀">秒杀</span>';
            }
            if (row.BusinessType == "15")
                html += '<span style="background-color:#ff6666;" title="拼团接龙订单">拼团接龙订单</span>';
            else if (row.BusinessType == "20")
                html += '<span style="background-color:#ff6666;" title="会员领样订单">会员领样订单</span>';
            else if (row.BusinessType == "21")
                html += '<span style="background-color:#ff6666;" title="免费领样订单">免费领样订单</span>';
        }
        else if (row.PlatformType == "Jingdong") {
            if (row.Warehouse != undefined && row.Warehouse != '') {
                html += '<span style="background-color:brown;margin-top:3px;" title="京东仓库类型">' + row.Warehouse + '</span>';
            }
            if (row.BusinessType == "68")
                html += '<span style="background-color:#ff6666;" title="京配">京配</span>';
            else if (row.BusinessType == "69")
                html += '<span style="background-color:#ff6666;" title="京配自提">京配自提</span>';
            else if (row.BusinessType == "67")
                html += '<span style="background-color:#ff6666;" title="第三方快递">第三方快递</span>';
            else if (row.BusinessType == "71")
                html += '<span style="background-color:#ff6666;" title="同城配送">同城配送</span>';
        }
        else if (commonModule.IsTouTiaoXi() && row.TradeType == "0")
            html += '<span style="background-color:#ff6666;" title="货到付款">货到付款</span>';
        else if (row.PlatformType == "TouTiao") {
            html += '<span style="background-color:#ff6666;" title="该订单为抖音送礼订单，该类订单不支持合单操作，请仔细检查商品后发货">送礼单</span>'
        }
        html += '</div>';
        return html;
    }

    var addressRemind = function (row, col) {
        if (row.ToProvince || row.ToProvince != "") {
            var GAT = ['澳门特别行政区', '香港特别行政区', '台湾省', '广东省'];
            for (var i = 0; i < GAT.length; i++) {
                if (GAT[i].indexOf(row.ToProvince) != -1) {

                    return '<>'
                }
            }
        }
    }

    var ReceiveAddressContentFormatter = function (row, col, index, ext) {

        //拼多多非待发货，不再显示收件人信息
        if (row.PlatformType == "Pinduoduo")
            return PddReceiveAddressContentFormatter(row, col, index, ext);
        if (row.PlatformType == "Taobao" || row.PlatformType == "Jingdong")
            return EncryptReceiveAddressContentFormatter(row, col, index, ext);

        var phone = row.ToMobile;
        if (!phone)
            phone = row.ToPhone;
        if (!phone)
            phone = "";
        if (!row.ToName)
            row.ToName = "";
        if (!row.ToProvince)
            row.ToProvince = "";
        if (!row.ToCity)
            row.ToCity = "";
        if (!row.ToCounty)
            row.ToCounty = "";
        if (!row.ToAddress)
            row.ToAddress = "";
        var address = row.ToFullAddress;
        if (!address)
            address = row.ToProvince + "" + row.ToCity + "" + row.ToCounty + "" + row.ToAddress
        var html = '';
        var detail = "";
        if (row.ToName && row.ToName != '')
            detail += row.ToName;
        if (phone && phone != '') {
            if (row.PlatformType == "Jingdong" && phone && phone.indexOf("*") != -1) {
                detail += "，" + '<a title="点击显示手机号码" href="javascript:void(0);" onclick="orderTableBuilder.ViewPhoneNumber(' + row.Index + ');">' + phone + '</a>';
            }
            else
                detail += "，" + phone;
        }
        if (address && address != '')
            detail += "，" + address;
        html += '<span style="min-width:150px;display:inline-block;">' + detail + '</span>';
        html += addressTagFormatter(row, col);
        return html;
    }

    var PddReceiveAddressContentFormatter = function (row, col, index, ext) {
        //拼多多非待发货，不再显示收件人信息
        if (row.PlatformType == "Pinduoduo" && row.PlatformStatus != "waitsellersend"
            && location.href.toLowerCase().indexOf('indexfds') == -1 //不是厂商代打页面
            && ext != "noencrypt") {
            return "<span>***,****,******</span>";
        }

        var phone = row.ToMobile;
        if (!phone)
            phone = row.ToPhone;
        if (!phone)
            phone = "";
        if (!row.ToName)
            row.ToName = "";
        if (!row.ToProvince)
            row.ToProvince = "";
        if (!row.ToCity)
            row.ToCity = "";
        if (!row.ToCounty)
            row.ToCounty = "";
        if (!row.ToAddress)
            row.ToAddress = "";
        var address = row.ToFullAddress;
        if (!address)
            address = row.ToProvince + "" + row.ToCity + "" + row.ToCounty + "" + row.ToAddress
        var html = '';
        var detail = "";
        detail += '<a title="点击显示收件人" href="javascript:void(0);" onclick="orderTableBuilder.ViewPddEncryptInfo(' + row.Index + ',\'ToName\');">' + row.ToName + '</a>';
        if (phone && phone.search(pddPrivacyNumberOPLBReg) > -1) {
            detail += '，<span>' + phone;
            detail += '<span class="privacyTips-wrap">隐私号';
            detail += '<div class="dailog-PrivacyTips">'
            detail += '<p>1.隐私号仅限于联系收件人，禁止复制隐私号用于发货。</p>'
            detail += '<p>2.若用于发货，推荐使用拼多多电子面单；非拼多多电子面单需使用真实的收件人信息，请<label style="color:dodgerblue;cursor:pointer;" onclick="movemoreFunSetMoudle.OpenShowPddRealyMobile(' + row.Index + ')">点击获取</label>。</p>'
            detail += '</div>'
            detail += '</span>'
        }
        else {
            detail += "，" + '<a title="点击显示手机号码" href="javascript:void(0);" onclick="orderTableBuilder.ViewPddEncryptInfo(' + row.Index + ',\'ToPhone\');">' + phone + '</a>';
        }
        detail += "，" + '<a title="点击显示收件地址" href="javascript:void(0);" onclick="orderTableBuilder.ViewPddEncryptInfo(' + row.Index + ',\'ToAddress\');">' + address + '</a>';
        //detail +=row.ToName;
        //detail += "，" + phone;
        //detail += "，" + address;
        //if(detail.indexOf("*")!=-1)
        //    html += '<span style="min-width:150px;display:inline-block;" onclick="orderTableBuilder.ViewPddEncryptInfo(' + row.Index + ',\'ToAddress\');">' + detail + ' <i class="lockIcon" title="应拼多多官方要求，收件人信息已加密，点击查看详细信息"></i></span>';
        //else
        //    html += '<span style="min-width:150px;display:inline-block;" onclick="orderTableBuilder.ViewPddEncryptInfo(' + row.Index + ',\'ToAddress\');">' + detail + '</span>';
        html += '<span style="min-width:150px;display:inline-block;">' + detail + '<i class="lockIcon" style="left:0;" onclick="orderTableBuilder.ViewPddEncryptInfo(' + row.Index + ',\'ToAddress\');" title="应拼多多官方要求，收件人信息已加密，点击查看详细信息"></i> </span>';
        detail += '</span>'
        html += addressTagFormatter(row, col);
        return html;
    }

    var EncryptReceiveAddressContentFormatter = function (row, col, index, ext) {

        var phone = row.ToMobile;
        if (!phone)
            phone = row.ToPhone;
        if (!phone)
            phone = "";
        if (!row.ToName)
            row.ToName = "";
        if (!row.ToProvince)
            row.ToProvince = "";
        if (!row.ToCity)
            row.ToCity = "";
        if (!row.ToCounty)
            row.ToCounty = "";
        if (!row.ToAddress)
            row.ToAddress = "";
        var address = row.ToFullAddress;
        if (!address)
            address = row.ToProvince + "" + row.ToCity + "" + row.ToCounty + "" + row.ToAddress
        var html = '';
        var detail = "";
        detail += '<a title="点击显示收件人" href="javascript:void(0);" onclick="orderTableBuilder.ViewEncryptInfo(' + row.Index + ',\'ToName\');">' + row.ToName + '</a>';
        detail += "，" + '<a title="点击显示手机号码" href="javascript:void(0);" onclick="orderTableBuilder.ViewEncryptInfo(' + row.Index + ',\'ToPhone\');">' + phone + '</a>';
        detail += "，" + '<a title="点击显示收件地址" href="javascript:void(0);" onclick="orderTableBuilder.ViewEncryptInfo(' + row.Index + ',\'ToAddress\');">' + address + '</a>';

        html += '<span style="min-width:150px;display:inline-block;">' + detail + '<i class="lockIcon" style="left:0;" onclick="orderTableBuilder.ViewEncryptInfo(' + row.Index + ',\'ToAddress\');" title="应官方要求，收件人信息已加密，点击查看详细信息"></i> </span>';
        html += addressTagFormatter(row, col);
        return html;
    }

    var LastExpressCodeContentFormatter = function (row, col) {
        var id = "txt_" + row.Id;
        var html = '<div class="expressCode-wrap" onmouseover="platformOrderShow(this)" onmouseout="platformOrderHide(this)">'
        html += '<i class="copyIcon" onclick="orderTableBuilder.CopyWaybillCode(\'' + id + '\')" style="display:none;"></i>';
        html += '<div class="lastExpressCodeContentShow"><input onclick="stopM(event)" value="" class="LastWaybillCode_input" id="' + id + '"/>'

        //html += '<div class="moreWaybillCode">\
        //                <i title="该订单打印过多个电子面单">多3</i>\
        //                <span class="moreWaybillCode-show" onclick="modifyOrderInfoModule.WaybillCodePrintDetails.bind(this)(' + row.Index + ')">查看详情</span>\
        //             </div>';

        html += '</div>'
        html += '</div>';
        return html;
    }

    table.ViewPhoneNumber = function (index) {
        common.stopM(event);

        //自由打印不做处理
        if (common.isCustomerOrder())
            return;

        var row = table.rows[index];
        common.getJSON("/Order/ShowPhone?pid=" + row.PlatformOrderId + "&sid=" + row.ShopId, function (rsp) {
            console.log(rsp);
            if (rsp && rsp.Data) {
                row.ToMobile = rsp.Data;
                table.refreshRow(row);
            }
        });
    }

    table.ViewEncryptInfo = function (index, infoType) {
        common.stopM(event);

        //自由打印不做处理
        if (common.isCustomerOrder())
            return;

        var row = table.rows[index];
        common.getJSON("/Order/DecryptOrderInfo?pid=" + row.PlatformOrderId + "&sid=" + row.ShopId, function (rsp) {
            console.log(rsp);
            if (common.IsError(rsp))
                return;
            if (rsp && rsp.Data) {
                if (infoType && false) {
                    switch (infoType) {
                        case "ToName":
                            row.ToName = rsp.Data.ToName;
                            break;
                        case "ToPhone":
                            row.ToMobile = rsp.Data.ToMobile;
                            row.ToPhone = rsp.Data.ToPhone;
                            break;
                        case "ToAddress":
                            row.ToAddress = rsp.Data.ToAddress;
                            row.ToFullAddress = rsp.Data.ToFullAddress;
                            break;
                    }
                }
                else {
                    row.ToName = rsp.Data.ToName;
                    row.ToMobile = rsp.Data.ToMobile;
                    row.ToPhone = rsp.Data.ToPhone;
                    row.ToAddress = rsp.Data.ToAddress;
                    row.ToFullAddress = rsp.Data.ToFullAddress;
                }
                table.refreshRow(row);
            }
        });
    }

    table.DecryptSenderInfo = function (index, infoType, callBack) {
        common.stopM(event);

        //自由打印不做处理
        if (common.isCustomerOrder())
            return;

        var row = table.rows[index];
        common.getJSON("/Order/DecryptSenderInfo?pid=" + row.PlatformOrderId + "&sid=" + row.ShopId, function (rsp) {
            console.log(rsp);
            if (common.IsError(rsp))
                return;
            if (rsp && rsp.Data) {
                if (infoType) {
                    row[infoType] = rsp.Data[infoType];
                }
                else {
                    row.SenderName = rsp.Data.SenderName;
                    row.SenderPhone = rsp.Data.SenderPhone;
                    row.SenderMobile = rsp.Data.SenderMobile;
                    row.SenderAddress = rsp.Data.SenderAddress;
                }
                if (callBack) {
                    callBack(row)
                }
                table.refreshRow(row);
            }
        });
    }

    table.ViewPddEncryptInfo = function (index, infoType, reportType) {
        common.stopM(event);
        var row = table.rows[index];
        var url = "/pdd/control/decrypt/v1/";
        if (infoType == "ToName")
            infoType = "receiverName";
        else if (infoType == "ToPhone")
            infoType = "receiverPhone";
        else if (infoType == "ToAddress")
            infoType = "receiverAddress";
        url += infoType;
        //var apiUrl = "/Order/ShowPddEncryptInfo?infoType=" + infoType + "&pid=" + row.PlatformOrderId + "&sid=" + row.ShopId;

        var shopId = common.GetShopIdBySid(row.ShopId);

        if (!shopId) {
            layer.alert("店铺不存在，不能解密信息");
            return;
        }

        var req = {
            "order_sn": row.PlatformOrderId.trimStartDgj('C'),
            "page_table_id": shopId,
            "decrypt_report_type": (reportType || 0)
        };
        var headers = common.IsDebug.toString().toLowerCase() == 'true' ? { "X-PDD-MustVerify": "True" } : {};
        var verifyAtuhToken_cookie = $.cookie(shopId);
        if (verifyAtuhToken_cookie) {
            headers = { "X-PDDVerifyAuthToken": verifyAtuhToken_cookie };
        }

        common.Ajax(
            {
                headers: headers,
                url: url,
                data: req,
                type: 'POST',
                success: function (rsp) {
                    console.log(JSON.stringify(rsp));
                    //触发了解密风控
                    if (rsp.error_code == "54001") {
                        var verifyAuthToken = rsp.risk_info.verify_auth_token;
                        var redirect_url = encodeURIComponent(location.protocol + "//" + location.host + "/auth/PddRiskVerifyCallback?href=" + encodeURIComponent(location.href) + "&mall_id=" + shopId);
                        //跳转到风控解除页面
                        window.location = "https://fuwu.pinduoduo.com/service-market/are-you-robot?mall_id=" + shopId + "&client_id=afabb0e68b3443b9a0dda11c1442a042&verifyAuthToken=" + verifyAuthToken + "&redirect_url=" + redirect_url;
                        //layer.open({
                        //    type: 2,
                        //    title: '解除解密风控',
                        //    shadeClose: true,
                        //    maxmin: true, //开启最大化最小化按钮
                        //    shade: 0.8,
                        //    area: ['600px', '800px'],
                        //    content: "https://fuwu.pinduoduo.com/service-market/are-you-robot?mall_id=" + shopId + "&client_id=afabb0e68b3443b9a0dda11c1442a042&verifyAuthToken=" + verifyAuthToken + "&redirect_url=" + redirect_url
                        //}); 
                        return;
                    }

                    if (rsp.sub_msg) {
                        layer.msg(rsp.sub_msg);
                    }
                    else if (rsp.error_msg) {
                        //数据解密太频繁
                        if (rsp.error_msg.indexOf("太频繁") > -1 || rsp.error_code == "11001") {
                            layer.alert('<div style="margin-bottom:4px;width:400px;"><b>' + rsp.error_msg + '</b>,请以下提示操作：</div>\
                                <div style = "margin-bottom:4px;color:#cccccc;font-size:13px;">\
                                <b>1</b>.请勿使用任何数据解密插件，如有使用请告知我们插件名称及实现的功能，我们会统一提交给拼多多官方，由官方评估提供相应插件的功能。</div>\
                                <div style = "margin-bottom:4px;color:#cccccc;font-size:13px;">\
                                <b>2</b>.等待10左右重试解密即可。</div>')
                        } //当前店铺解密额度不够
                        else if (rsp.error_msg.indexOf("额度不够") > -1 || rsp.error_code == "11001") {
                            layer.alert('<div style="margin-bottom:4px;width:400px;"><b>' + rsp.error_msg + '</b>,请以下提示操作：</div>\
                                <div style = "margin-bottom:4px;color:#cccccc;font-size:13px;">\
                                <b>1</b>.商家可通过 <a target = "_blank" href = "https://fuwu.pinduoduo.com/service-market/decrypt" > 额度申请</a> 链接申请提升额度。</div>\
                                <div style = "margin-bottom:4px;color:#cccccc;font-size:13px;">\
                                <b>2</b>.请勿使用任何数据解密插件, 使用插件爬数据会消耗额度，且导致的额度不足，平台可检测，所以会额度提升无效。</div>');
                        }
                        else
                            layer.msg(rsp.error_msg);
                    }
                    else if (rsp && !rsp.sub_code) {
                        if (infoType == "receiverName") {
                            row.ToName = rsp.order_info.receiver_name;
                            row.BuyerWangWang = row.ToName;
                        }
                        if (infoType == "receiverPhone")
                            row.ToMobile = rsp.order_info.receiver_phone;
                        if (infoType == "receiverAddress") {
                            row.ToFullAddress = row.ToProvince + row.ToCity + row.ToCounty + rsp.order_info.receiver_address;
                            row.ToAddress = rsp.order_info.receiver_address;
                        }
                        table.refreshRow(row);
                    }
                }
            });
    }

    table.CopyWaybillCode = function (iptId) {
        common.stopM(event);
        var ipt = $('#' + iptId);
        var val = ipt.val().trim();
        if (val == '' || val == '打印后返回') {
            layer.msg('暂无物流单号');
            return;
        }
        commonModule.CopyText('#' + iptId);
    }

    table.ViewLogisticTraces = function (iptId) {
        event.stopPropagation();
        var ipt = $('#' + iptId);
        var val = ipt.val().trim();
        var template = addTmplInOrderListModule.GetCurrentTemplate();
        if (val == '' || val == '打印后返回') {
            layer.msg('暂无物流单号');
            return;
        }
        var viewLogisticTracesWin = layer.open({
            type: 2,
            title: "物流轨迹", //不显示标题
            shadeClose: true,
            //maxmin: true, //开启最大化最小化按钮
            content: 'https://www.baidu.com/s?wd=' + template.ExpressCompanyName + " " + val,
            area: ['1025px', '600px'], //宽高
            btn: ['关闭'],
            //yes: function () {
            //    _clearCtrol();
            //    layer.closeAll();
            //    common.CopyText('#ul_print_result');
            //},
            cancel: function () {
                layer.closeAll();
            }
        });
    }

    var RemarkContentFormatter = function (row, col) {
        var html = "";
        html += "<div class='remarkContentOrder' style='min-width:110px;max-width:350px'>"
        html += "<div>"
        //留言
        var buyerRemark = row.BuyerRemark;
        if (buyerRemark) {
            var brs = buyerRemark.split('|||');
            $(brs).each(function (index, br) {
                //var sub = br.length > 15 ? (br.substring(0, 15) + "...") : br;
                if (br != '') {
                    var sub = br;
                    var span = "<span style=\"margin-right:2px;background-image: url('/Content/Images/allicons.png');background-position:-174px -20px;display:inline-block;" + "width:13px;height:13px;\">&nbsp;</span><span title='买家留言：" + br + "'>" + sub + "</span>";
                    html += span;
                }
            });
        }
        html += "</div>"
        //卖家备注
        //var sellerRemarkFlag = row.SellerRemarkFlag ? row.SellerRemarkFlag : "";
        //var sellerRemark = row.SellerRemark ? row.SellerRemark : "";
        //if (sellerRemarkFlag) {
        //    var srfs = sellerRemarkFlag.split('|||');
        //    var srs = sellerRemark.split('|||');
        //    $(srs).each(function (index, br) {
        //        var srf=srfs[index];
        //        if (srf == true) {
        //            var flagPosition = common.GetFlagPosition()[srf];
        //            var sub = br.length > 15 ? (br.substring(0, 15) + "...") : br;
        //            var span = "<span style=\"background-image: url('/Content/Images/allicons.png');background-position:" + flagPosition.x + "px " + flagPosition.y + "px;" + "\">&nbsp;</span><span title='卖家备注：" + br + "'>" + sub + "</span></br>";
        //            html += span;
        //        }
        //    });
        //}
        //改为取所有子订单备注
        //html += "<div onclick='stopM(event)' class='wrap_flagRemark'>";
        var temp = "";
        common.Foreach(row.SubOrders, function (i, o) {
            if (o.SellerRemarkFlag) {
                var flagPosition = common.GetFlagPosition()[o.SellerRemarkFlag];
                var flagHtml = "";
                if (flagPosition == undefined) {
                    flagHtml = "<span>【旗帜未识别】</span>"
                }
                else {
                    flagHtml = "<span class='flagRemark' style=\"margin-right:2px;background-image: url('/Content/Images/allicons.png');background-position:" + flagPosition.x + "px " + flagPosition.y + "px;" + "\">&nbsp;</span>";
                }
                //var sub = o.SellerRemark.length > 15 ? (o.SellerRemark.substring(0, 15) + "...") : o.SellerRemark;
                //var sub = o.SellerRemark;
                //var span = flagHtml + "<span title='卖家备注：" + o.SellerRemark + "'>" + sub + "</span></br>";
                temp += flagHtml;
            }
            // 平台是否可修改卖家备注
            var canUpdateInPlatformTpye = commonModule.CanUpdateSellerRemark(row.PlatformType);
            if (row.ChildOrderId) {
                if (o.SellerRemarkFlag || o.SellerRemark) {
                    temp += "<span class='sellerRemarks' title='卖家备注：" + o.SellerRemark + "' class='sellerRemark_show'>" + o.SellerRemark; //+ "<i class='setRemark_icon' title='修改' onclick=\"modifyOrderInfoModule.OpenBatchTableSellerRemarkWindow(this,'" + row.Index + "')\"></i></span></br>";
                    if (o.PlatformRemark) {
                        temp += "<label style='color:#ccc;' title='(原备注：" + o.PlatformRemark + ")'>(原备注：" + o.PlatformRemark + ")</label>"
                    }
                    if (canUpdateInPlatformTpye)
                        temp += "<i class='setRemark_icon' title='修改' onclick=\"modifyOrderInfoModule.OpenBatchTableSellerRemarkWindow(this,'" + row.Index + "')\"></i>";
                    temp += "</span></br>";
                }

                if (canUpdateInPlatformTpye && !o.SellerRemarkFlag && !o.SellerRemark) {
                    temp += "<i class='addRemark_icon' title='添加卖家备注' onclick=\"modifyOrderInfoModule.OpenBatchTableSellerRemarkWindow(this,'" + row.Index + "')\"></i>"
                }
            }
            else {
                if (o.SellerRemarkFlag || o.SellerRemark) {
                    temp += "<span  class='sellerRemarks' title='卖家备注：" + o.SellerRemark + "' class='sellerRemark_show'>" + o.SellerRemark;// + "<i class='setRemark_icon' title='修改' onclick=\"modifyOrderInfoModule.OpenModifySellerRemarkWindow(this,'" + o.Id + "','" + o.PlatformOrderId + "','" + o.ShopId + "')\"></i></span></br>";
                    if (o.PlatformRemark) {
                        temp += "<label style='color:#ccc;' title='(原备注：" + o.PlatformRemark + ")'>(原备注：" + o.PlatformRemark + ")</label>"
                    }
                    if (canUpdateInPlatformTpye)
                        temp += "<i class='setRemark_icon' title='修改' onclick=\"modifyOrderInfoModule.OpenModifySellerRemarkWindow(this,'" + o.Id + "','" + o.PlatformOrderId + "','" + o.ShopId + "')\"></i>";
                    temp += "</span></br>";
                }
                if (canUpdateInPlatformTpye && !o.SellerRemarkFlag && !o.SellerRemark) {
                    temp += "<i class='addRemark_icon' title='添加卖家备注' onclick=\"modifyOrderInfoModule.OpenModifySellerRemarkWindow(this,'" + o.Id + "','" + o.PlatformOrderId + "','" + o.ShopId + "')\"></i>"
                }
            }
        });
        html += "<div onclick='stopM(event)' class='wrap_flagRemark'>";
        if (temp.indexOf("flagRemark") != -1 || temp.indexOf("sellerRemark_show") != -1)
            temp = temp.replace(new RegExp("addRemark_icon", "g"), "addRemark_icon hideImp");
        html += temp;
        html += "</div>"
        html += "</div>"
        return html;
    }

    var ProductContentFormatter = function (row, col) {

        var html = '<div class="clearfix" style="display:flex;flex-wrap: wrap;position:relative;width:' + orderPrintModule.RowMidListCount() * 140 + 'px">';
        //最多仅显示四个
        var count = 0;
        var maxCount = orderPrintModule.RowMidAllCount();
        var isBreak = false;
        var minCount = 1;
        //var isOne = row.ProductCount==1;

        var currentStatus = row.PlatformStatus || "";
        for (var i = 0; i < row.SubOrders.length; i++) {
            var sub = row.SubOrders[i];
            if (count >= maxCount) {
                isBreak = true;
                break;
            }
            for (var j = 0; j < sub.OrderItems.length; j++) {
                if (count >= maxCount) {
                    isBreak = true;
                    break;
                }
                var oi = sub.OrderItems[j];
                var shortTitle = oi.ShortTitle ? oi.ShortTitle : "";
                var productSubject = oi.ProductSubject ? oi.ProductSubject : "";
                var attr = "";
                if (platformType == "youzan") {
                    attr += oi.ProductAttr;
                }
                else {
                    if (oi.Color)
                        attr += oi.Color + ";";
                    if (oi.Size)
                        attr += oi.Size + ";";
                    if (row.PlatformType != "Alibaba" && row.PlatformType != "AlibabaC2M" && row.PlatformType != "TaobaoMaiCaiV2") {
                        if (oi.ExtAttr1)
                            attr += oi.ExtAttr1 + ";";
                        if (oi.ExtAttr2)
                            attr += oi.ExtAttr2 + ";";
                        if (oi.ExtAttr3)
                            attr += oi.ExtAttr3 + ";";
                    }
                }
                attr = attr.trim(';');
                //if (oi.SkuAttributes && oi.SkuAttributes.length > 0) {
                //    for (var i = 0; i < oi.SkuAttributes.length; i++) {
                //        attr += oi.SkuAttributes[i].AttributeValue + " ";
                //    }
                //}
                if (!attr)
                    attr = "&nbsp;";
                var img = oi.ProductImgUrl ? oi.ProductImgUrl : "http://img.dgjapp.com/nopicurl.jpg";
                var bigImg = "";
                if (oi.ProductImgUrl) {
                    if (oi.ProductImgUrl.substr(oi.ProductImgUrl.length - 10, 10) == ".80x80.jpg") {
                        bigImg = oi.ProductImgUrl.substr(0, oi.ProductImgUrl.length - 9) + "jpg";
                    } else {
                        bigImg = oi.ProductImgUrl;
                    }
                } else {
                    bigImg = "http://img.dgjapp.com/nopicurl.jpg";

                }

                html += '<div class="table_content_tbody_products clearfix">'
                //var currentStatusSpan = $("#orderList_orderState_choose span[class='active']");
                //var currentStatus = "";
                //if (currentStatusSpan)
                //    currentStatus = currentStatusSpan.attr("data-status");
                var statusHtml = '';
                if (oi.RefundStatus == "REFUND_SUCCESS")
                    statusHtml = '<span  style="background: rgba(247,148,31,0.8);">退款成功</span>';
                else if (oi.RefundStatus == "REFUND_CLOSE")
                    statusHtml = '<span  style="background: green;opacity:0.7;display:none;">退款关闭</span>';
                else if (oi.RefundStatus == "REFUND_PART_SUCCESS")
                    statusHtml = '<span  style="background: rgba(247,148,31,0.8);">部分退款成功</span>';
                else if (oi.RefundStatus == "REFUND_PART")
                    statusHtml = '<span  style="background: rgba(254,111,79,0.8);">部分退款中</span>';
                else if (oi.RefundStatus)
                    statusHtml = '<span  style="background: rgba(254,111,79,0.8);">退款中</span>';
                else if (oi.Status == "waitbuyerpay" && oi.Status != currentStatus)
                    statusHtml = '<span  style="background: rgba(245,130,31,0.7);">待付款</span>';
                else if (oi.Status == "waitbuyerreceive" && oi.Status != currentStatus)
                    statusHtml = '<span  style="background: rgba(60,195,162,0.8);">已发货</span>';
                else if (oi.Status == "success" && oi.Status != currentStatus && oi.Status != "locked" && oi.Status != "locked_sended") /* locked、locked_sended 头条特殊退款状态,有售后子订单，其他子订单正常发货状态都被标记已完成 */
                    statusHtml = '<span  style="background: rgba(80,80,80,0.7);">已完成</span>';
                else if (oi.Status == "cancel" && oi.Status != currentStatus)
                    statusHtml = '<span  style="background: #ccc;">已取消</span>';
                if (row.PlatformType == "jd" || row.PlatformType == "Jingdong")
                    html += '<div class="table_content_img"><a class="stopHref" onclick="commonModule.openProductUrl(' + oi.SkuID + ')"  href="javascript:;" title="点击查看商品详情"><img src="' + img + '" width="40px" height="40px" alt="宝贝图片" /></a>' + statusHtml + '</div>';
                else
                    html += '<div class="table_content_img"><a class="stopHref" onclick="commonModule.openProductUrl(' + oi.ProductID + ')"  href="javascript:;" title="点击查看商品详情"><img src="' + img + '" width="40px" height="40px" alt="宝贝图片" /></a>' + statusHtml + '</div>';
                var cargoNumber = oi.CargoNumber;
                var title = "单品货号";
                if (!cargoNumber) {
                    cargoNumber = oi.productCargoNumber;
                    title = "商品货号";
                }
                if (!cargoNumber) {
                    cargoNumber = "&nbsp;";
                    title = "货号";
                }

                var $value = "";
                var upTitle = "";
                var produtType = commonModule.ProductShowType; //配置产品详情显示

                if (produtType == 1) {
                    $value = attr
                    upTitle = "规格属性";
                } else if (produtType == 2) {
                    $value = shortTitle;
                    upTitle = "简称";
                } else if (produtType == 3) {
                    $value = shortTitle != "" ? (shortTitle + ";" + attr) : attr;
                    upTitle = "简称+规格";
                } else if (produtType == 4) {
                    $value = productSubject;
                    upTitle = "商品标题";
                } else {
                    $value = attr
                    upTitle = "规格属性";
                }

                html += '<div>';
                html += '<span title="' + upTitle + '" ' + (row.ProductCount == 1 ? orderPrintModule.RowMidListCount() != 1 ? "style='width:220px'" : "" : "") + ' >' + $value + '</span>';
                html += '<span title="' + title + '"' + (row.ProductCount == 1 ? orderPrintModule.RowMidListCount() != 1 ? "style='width:220px'" : "" : "") + ' >' + cargoNumber + '</span>';
                html += '<span title="数量"' + (oi.Count > 1 ? "style='color:#ff0000;font-weight:700;font-size:14px'" : "") + ' >' + (oi.Count ? (+oi.Count) : "") + '</span>';
                html += '</div>';

                count++;
                html += '<div class="table_content_tbody_productsBigPic">'
                html += '<div class="table_content_tbody_imgDiv"><img src="' + bigImg + '" alt="Alternate Text" style="width:298px;height:298px" /></div>'
                html += '<div  class="table_content_tbody_title">'
                html += '<div style="color:#000">' + oi.ProductSubject + '</div>'
                html += '<div style="color:#888">规格属性：' + attr + '</div>'
                html += '<div style="color:#888" title="' + title + '">货号：' + cargoNumber + '</div>'
                html += '</div>'
                html += '</div>'

                html += '</div>'
            }
        }
        if (isBreak) {
            html += '<div class="productContent_moreTitle zk" onclick="product_zk_sq.bind(this)(event);">更多</div>';
        }

        html += "</div>";
        return html;

    }

    var DetailContentFormatter = function (row, col, index) {
        var wlIid = "txt_" + row.Id;
        var cls = (row.IsLocked && row.IsLocked > 0) ? "moreHide" : "";

        var html = '<div class="Detail_more" style="width:80px;" onclick="stopM(event)">';
        if (common.isCustomerOrder() == true) {
            html += '<span style="margin-right: 15px;" class="" onclick="modifyOrderInfoModule.EditOrder(' + row.Index + ')">编辑</span>';
            html += '<span style="margin-right: 15px;" class="" onclick="modifyOrderInfoModule.CopyOrder(' + row.Index + ')">复制</span>';
        }
        if (row.zk) {
            html += '<span style="margin-right: 15px;" class="table_content_spread" onclick="zk_sq.bind(this)(event)" data-index="' + row.Index + '">收起</span>';
        }
        else {
            html += '<span style="margin-right: 15px;" class="table_content_spread zk" onclick="zk_sq.bind(this)(event)" data-index="' + row.Index + '">展开</span>';
        }
        if (row.IsCantSendGood) {
            if (row.PlatformType == "Alibaba")
                html += '<span style="" class="table_content_moreOperations" data-order-id="' + row.Id + '" title="' + row.CantSendReason + '" onclick="layer.msg(\'' + row.CantSendReason + '\')">更多';
            else if (row.PlatformType == "YouZan")
                html += '<span style="" class="table_content_moreOperations" data-order-id="' + row.Id + '" title="当前订单是【分销买家订单】，不需要打单发货" onclick="layer.msg(\'当前订单是【分销买家订单】，不需要打单发货\')">更多';
        }
        else
            html += '<span class="table_content_moreOperations" data-order-id="' + row.Id + '" onmousemove="$(this).children().show();" onmouseout="$(this).children().hide();">更多';
        html += '<span class="moreOperations_aialog" onclick="stopM(event)">';
        html += '<div class="moreOperations_aialog_title">更多操作</div>';
        html += '<span class="only-in-order-list ' + cls + '" onclick="sendLogistic.send(false,' + row.Id + ')">发货</span>';
        //html += '<span class="only-in-freeprint-list ' + cls + '" onclick="freePrintOrderOperation.send(' + row.Id + ')">标记发货</span>';
        html += '<span class="' + cls + '" onclick="expressPrinter.printNormal(' + row.Id + ')">打印快递单</span>';
        html += '<span class="' + cls + '" onclick="sendGoodTemplate.print(' + row.Id + ')">打印发货单</span>';
        html += '<span class="oneToMany ' + cls + '" onclick="expressPrinter.printOneToMany(' + row.Id + ')">一单多包</span>';
        html += '<span class="newPrint ' + cls + '" onclick="expressPrinter.printNew(' + row.Id + ')">新单号打印</span>';
        html += '<span class="' + cls + '" onclick="modifyOrderInfoModule.RecylceWaybillCode(' + row.Index + ')">回收单号</span>';

        if ((row.IsLocked && row.IsLocked > 0)) {
            html += '<span class="lockOrder" onclick="orderPrintModule.LockOrUnLockOrders(' + row.Id + ',0)">解锁订单</span>';
        }
        else {
            html += '<span class="lockOrder" onclick="orderPrintModule.LockOrUnLockOrders(' + row.Id + ',1)">锁定订单</span>';
        }

        if (commonModule.IsTouTiaoXi(row.PlatformType) && row.PlatformStatus == "waitbuyerpay" && row.TradeType == "0") {
            html += '<span class="confirmOrder" onclick="orderTableBuilder.confirmOrder(\'' + row.PlatformOrderId + '\')">订单确认</span>';
        }

        html += '<span class="only-in-order-list ' + cls + '" onclick="orderTableBuilder.SyncOrder(' + index + ')" >同步此订单</span>';
        html += '<span class="only-in-freeprint-list ' + cls + '" onclick="freePrintOrderOperation.delete(' + row.Id + ')">删除订单</span>';
        html += '<span onclick="orderTableBuilder.ViewLogisticTraces(\'' + wlIid + '\')">查看轨迹</span>';
        html += ' </span>';
        html += '</span>';
        html += '</div>';

        return html;
    }
    var OrderClassifyCntentFormatter = function (row, col) {
        var html = "";
        var selectFlagColor = "";
        var selectText = "未分类订单";
        var liHtml = "";

        if (orderClassifyArr.length == 0) {
            $(".orderClassify_main>ul>li").each(function () {
                var name = $(this).find(">span").eq(0).text();
                var color = $(this).find(">span").eq(1).css("color");
                var alias = $(this).find("input").val();
                var id = $(this).find("input").attr("data-id");

                orderClassifyArr.push({ "Id": id, "Name": name, "Alias": alias, "Color": color });
            });
        }

        // 未分类订单
        liHtml += '<li onclick="orderPrintModule.UpdateOrderCategoryId(' + row.Id + ',0)"><span data-id="0" style="font-size:12px;margin-right: 8px;color:#ccc;">★</span><span style="font-size:13px;color:#ccc;font-family:微软雅黑;" class="category-alias">未分类订单</span></li>';

        for (var i = 0; i < orderClassifyArr.length; i++) {
            var name = orderClassifyArr[i].Name;
            var color = orderClassifyArr[i].Color;
            var alias = orderClassifyArr[i].Alias;
            var id = orderClassifyArr[i].Id;
            if (id == row.CategoryId) {
                selectFlagColor = color;
                selectText = alias !== "" ? alias : selectText;
            }
            var text = alias !== "" ? alias : name;
            liHtml += '<li onclick="orderPrintModule.UpdateOrderCategoryId(' + row.Id + ',' + id + ')"><span data-id="' + id + '" style="font-size:12px;margin-right: 8px;color:' + color + '">★</span><span class="category-alias" style="font-size:13px;font-family:微软雅黑;color:' + color + '">' + text + '</span></li>';
        }

        var categoryId = isNaN(parseInt(row["CategoryId"])) ? 0 : parseInt(row["CategoryId"]);

        html += '<div onclick="stopM(event)" class="OrderClassify_div" title="点击选择订单分类"><span class="OrderClassify_span"><i class="defaultActive" style="transform: scale(0.92,0.92);display: inline-block;font-size:11px;color:' + (categoryId > 0 ? selectFlagColor : "#ccc") + '" title="' + selectText + '">★</i>'; //<i class="defaultClassify" style = "color:#ccc">★</i>
        html += '<div class="OrderClassify_xinxin" onClick="stopM()">';
        html += '<ul title="">' + liHtml + '</ul >';
        //html += '<div style="padding:0 5px 5px 5px;font-family:微软雅黑;font-size:14px"  onclick=\'orderPrintModule.OpenUpdateOrderCategoryPanel("' + row.Id + '")\'><i class="OrderClassify_icon"></i>修改分类别名</div>';
        html += '</span></div></div>';

        return html;
    }

    var PlatformOorderIdFormatter = function (row, col, index) {
        var pids = [];
        var isCustomerOrder = common.isCustomerOrder();
        for (var i = 0; i < row.SubOrders.length; i++) {
            var pid = row.SubOrders[i].PlatformOrderId;
            if (isCustomerOrder && row.CustomerOrderId) {
                pid = row.CustomerOrderId;
            }
            pids.push(pid);
        }
        var html = "";

        html += '<div class="platformOorder" id="' + pids.join('-') + '" onmouseover="platformOrderShow(this)" onmouseout="platformOrderHide(this)">' + pids.join('</br>') + '<i class="copyIcon" onclick="commonModule.execClick(event);" oncopy=\'commonModule.execCopy(event,"' + pids.join(",") + '")\'></i></div>';

        return html;
    }
    var OrderSourceFormatter = function (row, col, index) {
        if (commonModule.isCustomerOrder()) {
            if (row.OrderFrom == "ImportOrder")
                return "导入单";
            else
                return "录入单";
        }
        else {
            var t = row.PlatformType.toLowerCase()
            if (t == "alibaba" || t == "1688")
                return "1688";
            else if (t == "pinduoduo" || t == "pdd")
                return "拼多多";
            else if (t == "taobao" || t == "TB")
                return "淘宝";
            else if (t == "jingdong" || t == "jd")
                return "京东";
            else if (t == "xiaodian" || t == "xd")
                return "小店";
            else if (t == "mogujie" || t == "mg")
                return "蘑菇街";
            else if (t == "meilishuo" || t == "mls")
                return "美丽说";
            else if (t == "youzan" || t == "yz")
                return "有赞";
            else if (t == "weidian" || t == "wd")
                return "微店";
            else if (t == "weimeng" || t == "wm")
                return "微盟";
            else if (t == "vipshop" || t == "vshop")
                return "唯品会";
            else if (t == "toutiao" || t == "tt")
                return "头条系";
            else if (t == "zhidian" || t == "zd")
                return "值点";
            else if (t == "douyixiaodian" || t == "dyxd")
                return "抖音小店";
            else if (t == "toutiaoxiaodian" || t == "ttxd")
                return "头条小店";
            else if (t == "luban" || t == "lb")
                return "鲁班";
            else if (t == "suning" || t == "sn")
                return "苏宁易购";
            else if (t == "mengtui" || t == "mt")
                return "萌推";
            else if (t == "yunji" || t == "yj")
                return "云集";
            else if (t == "kuaishou" || t == "ks")
                return "快手小店";
        }
    }
    var ShippingFeeFormatter = function (row, col, index) {
        if (row.ShippingFee)
            return row.ShippingFee;
        else
            return 0;
    }
    var TotalWeightFormatter = function (row, col, index) {

        var weight = '';

        if (row.TotalWeight)
            weight = row.TotalWeight;
        else
            weight = '';

        if (weight == 0) {
            weight = '';
        }

        return '<input id="order_weight_' + row.Index + '" onclick="stopM(event)" value="' + weight + '" onblur="modifyOrderInfoModule.ModifyCODAmountOrInSureAmount.bind(this)(\'weight\',' + row.Index + ')" onfocus="modifyOrderInfoModule.CodOrInSureFocusHandler.bind(this)()"  type="text" style="border:1px solid #e2e2e2;height:20px;width:55px;text-align: center;"/>'

    }



    var orderCountContentFormatter = function (row, col, index) {
        return row.SubOrders.length;
    }

    var shopNameContentFormatter = function (row, col, index) {
        if (common.IsPddFds()) {
            return row.ExtField2;
        }
        var shopName = "";
        $(commonModule.Shops).each(function (i, shop) {
            if (row.ShopId == shop.Id) {
                shopName = shop.NickName;
                return;
            }
        });
        return shopName;
    }

    var invoiceContentFormatter = function (row, col, index) {
        return row.ExtField3 || "";
    }

    var formatDate = function (date) {
        var month = date.getMonth() + 1;
        if (month < 10)
            month = "0" + month;
        var day = date.getDate();
        if (day < 10)
            day = "0" + day;
        return (date.getYear() + 1900) + "-" + (month) + "-" + (day);
    }

    var now = new Date();
    var today = formatDate(now);
    var thedayBeforeYesterday = formatDate(new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000));//前天
    var yesterday = formatDate(new Date(now.getTime() - 24 * 60 * 60 * 1000)); //昨天
    var tommorow = formatDate(new Date(now.getTime() + 24 * 60 * 60 * 1000)); //明天
    var timeFormatter = function (val, row, col, index) {
        if (common.isUseOldTheme)
            return oldTimeFormatter(val, row, col, index);
        else
            return newTimeFormatter(val, row, col, index);
    }
    var newTimeFormatter = function (val, row, col, index) {
        if (val === "") return "";
        var day = "";
        if (val.indexOf(today) != -1)
            day = "今天";
        else if (val.indexOf(yesterday) != -1)
            day = "昨天";
        else if (val.indexOf(thedayBeforeYesterday) != -1)
            day = "前天";
        if (day != "")
            return '<div title="' + val + '">' + day + '</div><div title="' + val + '">' + val.substring(11) + '</div>';
        if (col.aliasField != null && col.aliasField != undefined)
            return "<div>" + row[col.aliasField].substring(0, 10) + "</div><div>" + row[col.aliasField].substring(10, 19) + "</div>";
        else
            return "<div>" + row[col.field].substring(0, 10) + "</div><div>" + row[col.field].substring(10, 19) + "</div>";
    }

    var oldTimeFormatter = function (val, row, col, index) {
        var reg = new RegExp("-0", "g");//替换日期内容
        var reg1 = new RegExp("-", "g");//替换日期分隔符
        //val.replace(reg, "-");
        if (val === "") return "";
        var day = "";
        if (val.indexOf(today) != -1)
            day = "今天";
        else if (val.indexOf(yesterday) != -1)
            day = "昨天";
        else if (val.indexOf(thedayBeforeYesterday) != -1)
            day = "前天";
        var field = col.field;
        if (col.aliasField != null && col.aliasField != undefined)
            field = col.aliasField;
        if (day != "")
            return '<div title="' + val + '">' + day + row[field].substring(10, 16) + "</div>";
        else
            return "<div>" + row[field].substring(0, 10).replace(reg, "/").replace(reg1, "/") + "</div>"
    }

    var OrderTimeFormatter = function (row, col, index) {
        var val = row[col.aliasField] || "";
        return timeFormatter(val, row, col, index);
    }

    var PayTimeContentFormatter = function (row, col, index) {
        var val = row.PayTime || "";
        if (val === "") return "";
        return timeFormatter(val, row, col, index);
    }

    var BuyerWangWangFormatter = function (row, col, index) {
        var val = row.BuyerWangWang || "";
        if (val === "") return "";

        if (platformType == "taobao") {
            var html = '<span class="tdMoreShow_main_buyerWangWang" onclick="event.stopPropagation()" style="text-align:left;word-break: break-all;display:inherit;"><a href="aliim:sendmsg?uid=cntaobao&touid=cntaobao' + val + '&siteid=cntaobao&status=1&v=2&s=1&charset=utf-8"><img border="0" style="width:18px" src="https://amos.im.alisoft.com/online.aw?v=2&amp;uid=bonlinetest003&amp;site=cntaobao&amp;s=2&amp;charset=utf-8" alt="点击这里给我发消息">' + val + '</a></span>'
        } else {
            var html = '<span class="tdMoreShow_main_buyerWangWang" style="margin-left:8px">' + val + '</span>';

        }
        return html;
    }

    var ProductItemCountFormatter = function (row, col, index) {
        var val = row.ProductItemCount || "";
        if (val === "") return "";
        var html = "";
        if (val > 1) {
            html = '<span style="color:#ff0000;font-weight:700;font-size:14px">' + val + '</span>';
        } else {
            html = '<span>' + val + '</span>';

        }
        return html
    }

    var CreateTimeContentFormatter = function (row, col, index) {
        var val = row.CreateTime || "";
        if (val === "") return "";
        return timeFormatter(val, row, col, index);
    }

    var ConfirmedTimeContentFormatter = function (row, col, index) {
        var val = row.ConfirmedTime || "";
        if (val === "") return "";
        return timeFormatter(val, row, col, index);
    }
    var LastShipTimeContentFormatter = function (row, col, index) {

        var html = timeContentFormatter(row, "LastShipTime");
        return html;

    }

    var timeContentFormatter = function (row, field) {
        var val = "";
        if (field == "LastShipTime")
            val = row.LastShipTime || "";
        else if (field == "ExtField1")
            val = row.ExtField1 || "";
        else
            return "";

        if (val === "") return "";
        var payTime = new Date(row.PayTime).getTime();
        var nowTime = new Date().getTime();
        var LastTime = new Date(val).getTime();
        var LastShipTime = LastTime - nowTime;

        //算百分比
        var totalNum = LastTime - payTime;
        var progressNum = nowTime - payTime;

        var percent = parseInt(100 - progressNum / totalNum * 100) <= 100 ? parseInt(100 - progressNum / totalNum * 100) : 100;
        var activeClass = (percent <= 50 ? 'active' : '');//超过50%背景红变

        //时分秒换算
        function MillisecondToDate(msd) {
            var time = parseFloat(msd) / 1000;
            if (null != time && "" != time) {
                if (time > 60 && time < 60 * 60) {
                    time = parseInt(time / 60.0) + "分" + parseInt((parseFloat(time / 60.0) -
                        parseInt(time / 60.0)) * 60) + "秒";
                }
                // else if (time >= 60 * 60 && time < 60 * 60 * 24) {
                else if (time >= 60 * 60) {
                    time = parseInt(time / 3600.0) + "时" + parseInt((parseFloat(time / 3600.0) -
                        parseInt(time / 3600.0)) * 60) + "分" +
                        parseInt((parseFloat((parseFloat(time / 3600.0) - parseInt(time / 3600.0)) * 60) -
                            parseInt((parseFloat(time / 3600.0) - parseInt(time / 3600.0)) * 60)) * 60) + "秒";
                }
                else {
                    time = parseInt(time) + "秒";
                }
            }
            return time;
        }

        var html = "";

        var $CurrentStatus = $("#orderList_orderState_choose>span.active").attr("data-status");

        if ($CurrentStatus == "waitsellersend" || $CurrentStatus == "confirm_goods_but_not_fund") {
            html += '<div class="lastShipTimeWrap">'
            html += '<div class="percent">'
            html += '<span class="jing ' + activeClass + '"   style="width:' + percent + '%"></span>'
            html += '</div>'
            if (LastShipTime > 0) {
                var $time = MillisecondToDate(LastShipTime);
                html += '<span>' + $time + '</span>'

            } else {
                LastShipTime = -LastShipTime;
                var $time = MillisecondToDate(LastShipTime);
                html += '<span style="color:#fe6f4f">超:' + $time + '</span>'
            }
            if (platformType == "toutiao" && row["TradeType"] == "0") {
                html += '<div class="toutiaoHuo"><span>货</span></div>';
            }
            html += '</div>'
        }


        return html;
    }

    var BatchNoContentFormatter = function (row, col, index) {
        var val = row.BatchNo || "";
        if (val === "") return "";

        var html = "<div>" + val.substring(0, 10) + "</div><div>" + val.substring(10, 19) + "</div>"
        return html;
    }

    var PrintTimesFormatter = function (row, col, index) {
        var ExpressPrintTimes = row.ExpressPrintTimes && row.ExpressPrintTimes > 0 ? row.ExpressPrintTimes : 0;
        var SendPrintTimes = row.SendPrintTimes && row.SendPrintTimes > 0 ? row.SendPrintTimes : 0;
        var NahuoPrintTimes = row.NahuoPrintTimes && row.NahuoPrintTimes > 0 ? row.NahuoPrintTimes : 0;

        var content = '<span id="sp_expr_print_count_' + row.Id + '" style="color:green;" title="已打印快递单' + ExpressPrintTimes + '次">' + ExpressPrintTimes + '</span>-';
        content += '<span id="sp_send_print_count_' + row.Id + '" style="color:#1295c1;" title="已打印发货单' + SendPrintTimes + '次">' + SendPrintTimes + '</span>-';
        content += '<span id="sp_label_print_count_' + row.Id + '" style="color:#fe3c3c;" title="已打印拿货标签' + NahuoPrintTimes + '次">' + NahuoPrintTimes + '</span>';
        return content;
    }

    var PrintContentFormatter = function (row, col, index) {
        var val = row.PrintContent || "";
        if (val === "") return "";

        var html = "<pre id='td_print_content_" + row.Id + "'>" + val + "</pre>";
        return html;
    }

    var ToNameFormatter = function (row, col, index) {

        //拼多多非待发货，不再显示收件人信息
        if (row.PlatformType == "Pinduoduo"
            && common.IsPddFds() == false//不是厂商代打页面
            && row.PlatformStatus != "waitsellersend") {
            return "***";
        }
        if (row.PlatformType == "Pinduoduo" && row.ToName && row.ToName.indexOf("*") != -1) {
            var detail = "";
            detail += '<a title="点击显示收件人" href="javascript:void(0);" onclick="orderTableBuilder.ViewPddEncryptInfo(' + row.Index + ',\'ToName\');">' + row.ToName + '</a>';
            detail = '<span style="min-width:150px;display:inline-block;"  onclick="orderTableBuilder.ViewPddEncryptInfo(' + row.Index + ',\'ToName\');">' + detail + ' <i class="lockIcon" title="应拼多多官方要求，收件人信息已加密，点击解密"></i></span>';
            return detail;
        }
        if ((row.PlatformType == "Taobao" || row.PlatformType == "Jingdong") && row.ToName && row.ToName.indexOf("*") != -1) {
            var detail = "";
            detail += '<a title="点击显示收件人" href="javascript:void(0);" onclick="orderTableBuilder.ViewEncryptInfo(' + row.Index + ',\'ToName\');">' + row.ToName + '</a>';
            detail = '<span style="min-width:150px;display:inline-block;"  onclick="orderTableBuilder.ViewEncryptInfo(' + row.Index + ',\'ToName\');">' + detail + ' <i class="lockIcon" title="应官方要求，收件人信息已加密，点击解密"></i></span>';
            return detail;
        }
        return row.ToName;
    }

    var SenderFormatter = function (row, col, index) {

        if (row.PlatformType == "Taobao" && row.SenderName && row.SenderName.indexOf("*") != -1) {
            var detail = "";
            detail += '<a title="点击显示发件人" href="javascript:void(0);" onclick="orderTableBuilder.DecryptSenderInfo(' + row.Index + ',\'SenderName\');">' + row.SenderName + '</a>';
            detail = '<span style="min-width:150px;display:inline-block;"  onclick="orderTableBuilder.DecryptSenderInfo(' + row.Index + ',\'SenderName\');">' + detail + ' <i class="lockIcon" title="应官方要求，发件人信息已加密，点击解密"></i></span>';
            return detail;
        }
        return row.SenderName || "";
    }

    var ToMobileFormatter = function (row, col, index) {

        //拼多多非待发货，不再显示收件人信息
        if (row.PlatformType == "Pinduoduo"
            && common.IsPddFds() == false //不是厂商代打页面
            && row.PlatformStatus != "waitsellersend") {
            return "****";
        }
        if (row.PlatformType == "Pinduoduo") {
            if (row.ToMobile && row.ToMobile.indexOf("*") != -1) {
                var detail = "";
                detail += '<a title="点击显示收件人电话" href="javascript:void(0);" onclick="orderTableBuilder.ViewPddEncryptInfo(' + row.Index + ',\'ToPhone\');">' + row.ToMobile + '</a>';
                detail = '<span style="min-width:150px;display:inline-block;"  onclick="orderTableBuilder.ViewPddEncryptInfo(' + row.Index + ',\'ToPhone\');">' + detail + ' <i class="lockIcon" title="应拼多多官方要求，收件人信息已加密，点击解密"></i></span>';
                return detail;
            }
            else if (row.ToMobile && row.ToMobile.search(pddPrivacyNumberOPLBReg) > -1) {

                //return "<span>" + row.ToMobile + "<label style='color: gray;border-bottom: 1px dashed gray;margin-left:5px;' \
                //       onmouseover='movemoreFunSetMoudle.ShowPrivacyTips(" + row.Index + ")' onmouseout='movemoreFunSetMoudle.HidePrivacyTips()'>隐私号</label></span>";
                var detail = "";
                detail += '<span>' + row.ToMobile;
                detail += '<span class="privacyTips-wrap">隐私号';
                detail += '<div class="dailog-PrivacyTips">'
                detail += '<p>1.隐私号仅限于联系收件人，禁止复制隐私号用于发货。</p>'
                detail += '<p>2.若用于发货，推荐使用拼多多电子面单；非拼多多电子面单需使用真实的收件人信息，请<label style="color:dodgerblue;cursor:pointer;" onclick="movemoreFunSetMoudle.OpenShowPddRealyMobile(' + row.Index + ')">点击获取</label>。</p>'
                detail += '</div>'
                detail += '</span>'
                detail += '</span>'
                return detail;
            }
        }
        if ((row.PlatformType == "Taobao" || row.PlatformType == "Jingdong") && row.ToMobile && row.ToMobile.indexOf("*") != -1) {
            var detail = "";
            detail += '<a title="点击显示收件人电话" href="javascript:void(0);" onclick="orderTableBuilder.ViewEncryptInfo(' + row.Index + ',\'ToPhone\');">' + row.ToMobile + '</a>';
            detail = '<span style="min-width:150px;display:inline-block;"  onclick="orderTableBuilder.ViewEncryptInfo(' + row.Index + ',\'ToPhone\');">' + detail + ' <i class="lockIcon" title="应官方要求，收件人信息已加密，点击解密"></i></span>';
            return detail;
        }
        return row.ToMobile;
    }

    var ToFullAddressFormatter = function (row, col, index) {

        //拼多多非待发货，不再显示收件人信息
        if (row.PlatformType == "Pinduoduo"
            && common.IsPddFds() == false //不是厂商代打页面
            && row.PlatformStatus != "waitsellersend") {
            return "******";
        }
        if (row.PlatformType == "Pinduoduo" && row.ToFullAddress && row.ToFullAddress.indexOf("*") != -1 && common.IsPddFds() == false) {
            var detail = "";
            detail += '<a title="点击显示收件地址" href="javascript:void(0);" onclick="orderTableBuilder.ViewPddEncryptInfo(' + row.Index + ',\'ToAddress\');">' + row.ToFullAddress + '</a>';
            detail = '<span style="min-width:150px;display:inline-block;"  onclick="orderTableBuilder.ViewPddEncryptInfo(' + row.Index + ',\'ToAddress\');">' + detail + ' <i class="lockIcon" title="应拼多多官方要求，收件人信息已加密，点击解密"></i></span>';
            return detail;
        }
        if ((row.PlatformType == "Taobao" || row.PlatformType == "Jingdong") && row.ToFullAddress && row.ToFullAddress.indexOf("*") != -1) {
            var detail = "";
            detail += '<a title="点击显示收件地址" href="javascript:void(0);" onclick="orderTableBuilder.ViewEncryptInfo(' + row.Index + ',\'ToAddress\');">' + row.ToFullAddress + '</a>';
            detail = '<span style="min-width:150px;display:inline-block;"  onclick="orderTableBuilder.ViewEncryptInfo(' + row.Index + ',\'ToAddress\');">' + detail + ' <i class="lockIcon" title="应官方要求，收件人信息已加密，点击解密"></i></span>';
            return detail;
        }
        var html = row.ToFullAddress;
        if (common.IsPddFds())
            html = row.ToFullAddress.replace(/\*/g, "");
        html = '<span style="min-width:150px;display:inline-block;">' + html + '</span>';
        html += addressTagFormatter(row, col);
        addressRemind(row, col)

        return html;
    }


    var PromiseHearderFormatter = function (row, col, index) {
        var html = '';
        html += '<div class="promiseHearderTh">客服承诺';
        html += '<s title="会同步展示官方客服平台向买家承诺的【优先发货】或【指定快递】信息注:(如果您不是平台客服承诺试用的商家，暂时忽略该功能)">?</s>';
        html += '<div class="promiseHearderTh-oparete">';
        html += '<span class="promiseHearderTh-icon" id="promiseHearderTh_icon" onclick="orderTableBuilder.zkSort(this)"></span>';
        html += '<ul class="sortWrap-main" id="sortWrap_main" style="display:none">';
        html += '<li class="active" data-filter="none">所有订单</li>';
        html += '<li data-filter="only-express">仅显示指定快递订单</li>';
        html += '<li data-filter="only-send">仅显示优先发货订单</li>';
        html += '</ul>';
        html += '</div>';
        html += '</div>';

        return html;
    }

    var servicePromiseFormatter = function (row, col, index) {
        var html = "";
        var promiseOrderCount = 0;
        if (row.ChildOrderId) {
            $(row.SubOrders).each(function (i, o) {
                if (o.ExtField2 || o.ExtField1 || o.ExtAttr4)
                    promiseOrderCount++;
            });
        }

        if (promiseOrderCount > 1) {
            html += '<div class="wpromiseWrap">';
            html += '<span class="table_content_spread" onclick = "orderTableBuilder.OpenMergePromisePanel.bind(this)(' + row.Id + ')" data-index="' + row.Index + '"> 点击查看详情</span>';
            html += '<div class="hide mergeWrap merge_promise_' + row.Id + '" style="padding:10px;" onclick="commonModule.StopPropagation(event)">';
            html += '<div class="tableWrap">';
            html += '<table class="unify_table"><colgroup><col width="120"><col width="150"><col><col width="150"><col></colgroup><thead><tr><th style="width:150px">订单编号</th><th style="width:150px">承诺剩余发货时间</th><th style="width:150px">平台剩余发货时间</th><th>指定快递</th></tr></thead><tbody>';
            $(commonModule.SortExt(row.SubOrders, 'ExtField1', false, true)).each(function (i, o) {
                html += '<tr>';
                html += '<td>' + o.PlatformOrderId + '</td>';
                html += '<td class="LastShipTime">' + (o.ExtField1 ? timeContentFormatter(o, "ExtField1") : "-") + '</td>';
                html += '<td class="LastShipTime">' + timeContentFormatter(o, "LastShipTime") + '</td>';
                html += '<td data-value="' + o.ExtField2 + '">' + (o.PromiseExpressName || "-") + '</td>';
                html += '</tr>';
            });
            html += '</tbody></table></div>';
            html += '</div>'
            html += '</div>';
        }
        else {
            if (row.ChildOrderId) {
                $(row.SubOrders).each(function (i, o) {
                    if (o.ExtField2 || o.ExtField1 || o.ExtAttr4) {
                        html += '<span class="LastShipTime" style="width:100px;max-height:300;" >' + renderPromiseContent(o) + '</span>';
                    }
                });
            }
            else {
                html += '<span class="LastShipTime" style="width:100px;max-height:300;" >' + renderPromiseContent(row) + '</span>';
            }
        }
        return html;
    }

    var renderPromiseContent = function (row) {
        var content = '';
        if (row.ExtField2)
            content = '<span data-value="' + row.ExtField2 + '">指定[' + row.PromiseExpressName + ']</span>';
        if (row.ExtField1)
            content += timeContentFormatter(row, "ExtField1");
        //content += '<span class="LastShipTime" style="width:100px;max-height:300;" >' + content + '</span>';
        return content;
    }
    var wrapperDescFormatter = function (row, col, index) {
        return row.ExtField4 || "";
    }

    table.OpenMergePromisePanel = function (id) {
        if (event.stopPropagation) {
            event.stopPropagation();
            event.preventDefault();
        } else {
            window.event.returnValue = false;
            window.event.cancelBubble = true;
        };
        $(this).next().show();

    }


    //定义表格列
    var columns = commonModule.isCustomerOrder() ?
        [
            { id: 1, defaults: true, field: "PrintState", name: "打印", className: "table_content_dagou", display: true, order: 1, width: "40px", canMove: false, headerFormattter: null, contentFormatter: PrintStateContentFormatter },
            { id: 2, defaults: false, field: "checkbox", name: "勾选框", display: true, order: 2, width: "20px", canMove: true, headerFormattter: null, contentFormatter: checkboxContentFormatter },
            { id: 3, defaults: true, field: "BuyerWangWang", name: commonModule.BuyerName, display: true, order: 3, minWidth: "90px", canMove: true, headerFormattter: null, contentFormatter: BuyerWangWangFormatter },
            { id: 4, defaults: true, field: "CreateTime", name: "创建时间", display: true, order: 4, minWidth: "70px", canMove: true, headerFormattter: null, contentFormatter: CreateTimeContentFormatter },
            { id: 5, defaults: true, field: "BatchNo", name: "导入批次", display: true, order: 5, minWidth: "80px", canMove: true, headerFormattter: null, contentFormatter: BatchNoContentFormatter },
            { id: 6, defaults: true, field: "Amount", name: "金额/含运费", display: true, order: 6, minWidth: "70px", canMove: true, headerFormattter: null, contentFormatter: AmountContentFormatter },
            { id: 7, defaults: true, field: "ReceiveAddress", name: "收件信息", display: true, order: 7, align: "left", minWidth: "150px", canMove: true, headerFormattter: null, contentFormatter: ReceiveAddressContentFormatter },
            { id: 71, defaults: true, field: "ToName", name: "收件人", display: false, order: 7, align: "left", minWidth: "55px", canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 72, defaults: true, field: "ToMobile", name: "联系电话", display: false, order: 7, align: "left", minWidth: "75px", canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 73, defaults: true, field: "ToFullAddress", name: "收件地址", display: false, order: 7, align: "left", minWidth: "250px", canMove: true, headerFormattter: null, contentFormatter: ToFullAddressFormatter },
            { id: 81, defaults: true, field: "IsExpressSend", name: "派送", display: true, order: 8, minWidth: "33px", canMove: true, headerFormattter: IsSendExpressHeaderFormatter, contentFormatter: IsSendExpressContentFormatter },
            { id: 8, defaults: true, field: "LastExpressCode", name: "快递单号", display: true, order: 9, minWidth: "110px", canMove: true, headerFormattter: null, contentFormatter: LastExpressCodeContentFormatter },
            { id: 9, defaults: true, field: "Remark", name: "留言备注", display: true, order: 10, align: "left", minWidth: "150px", canMove: true, headerFormattter: null, contentFormatter: RemarkContentFormatter },
            { id: 10, defaults: true, field: "Product", className: "table_content_tbody_tdProducts", name: "产品详情", display: true, order: 11, minWidth: "280px", canMove: true, headerFormattter: ProductHearderFormatter, contentFormatter: ProductContentFormatter },
            { id: 11, defaults: true, field: "SvcCODAmount", name: "代收货款", display: false, order: 12, width: "65px", canMove: true, headerFormattter: null, contentFormatter: DaiShouHuoKuanContentFormatter },
            { id: 12, defaults: false, field: "ProductItemCount", name: "数量", display: false, order: 13, minWidth: "25px", canMove: true, headerFormattter: null, contentFormatter: ProductItemCountFormatter },
            { id: 13, defaults: false, field: "TotalWeight", name: "重量(kg)", display: false, order: 14, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: TotalWeightFormatter },
            { id: 14, defaults: false, field: "ShippingFee", name: "运费", display: false, order: 15, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: ShippingFeeFormatter },
            { id: 15, defaults: false, field: "SvcInsureAmount", name: "保价金额", display: false, order: 16, width: "65px", canMove: true, headerFormattter: null, contentFormatter: BaoJiaJinEContentFormatter },
            //{ id: 16, defaults: false, field: "CreateTime", name: "下单时间", display: false, order: 16, minWidth: "80px", canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 16, defaults: false, field: "SenderName", name: "发件人", display: false, order: 17, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 17, defaults: false, field: "PrintTimes", name: "打印次数", display: false, order: 18, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: PrintTimesFormatter },
            { id: 171, defaults: false, field: "PrintContent", name: "发货内容", display: false, order: 19, minWidth: "200px", canMove: true, headerFormattter: null, contentFormatter: PrintContentFormatter },
            //{ id: 17, defaults: false, field: "ExpressPrintTimes", name: "打快递次数", display: false, order: 17, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: null },
            //{ id: 18, defaults: false, field: "SendPrintTimes", name: "打发货次数", display: false, order: 18, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: null },
            //{ id: 20, defaults: false, field: "NahuoPrintTimes", name: "打拿货次数", display: false, order: 20, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 18, defaults: false, field: "PrintedSerialNumber", name: "流水号", display: false, order: 20, minWidth: "80px", canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 19, defaults: false, field: "OrderClassify", name: "订单分类", display: true, order: 21, minWidth: "80px", canMove: true, headerFormattter: OrderClassifyHearderFormatter, contentFormatter: OrderClassifyCntentFormatter },
            { id: 20, defaults: false, field: "PlatformOrderId", name: "订单编号", display: false, order: 22, minwidth: "100px", canMove: true, headerFormattter: null, contentFormatter: PlatformOorderIdFormatter },
            { id: 21, defaults: false, field: "OrderFrom", name: "订单来源", display: false, order: 23, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: OrderSourceFormatter },
            { id: 22, defaults: false, field: "Detail", name: "详情", display: true, className: "table_content_tbody_tdMore", order: 24, width: "80px", canMove: true, headerFormattter: DetailHearderFormatter, contentFormatter: DetailContentFormatter }
        ]
        :
        [
            { id: 1, defaults: true, field: "PrintState", name: "打印", className: "table_content_dagou", display: true, order: 1, width: "40px", canMove: false, headerFormattter: null, contentFormatter: PrintStateContentFormatter },
            { id: 2, defaults: false, field: "checkbox", name: "勾选框", display: true, order: 2, width: "20px", canMove: true, headerFormattter: null, contentFormatter: checkboxContentFormatter },
            { id: 3, defaults: true, field: "BuyerWangWang", name: commonModule.BuyerName, display: true, order: 3, minWidth: "90px", canMove: true, headerFormattter: null, contentFormatter: BuyerWangWangFormatter },
            { id: 4, defaults: true, field: "OrderTime", aliasField: "CreateTime", name: "订单时间", aliasName: "下单时间", display: true, order: 4, minWidth: "70px", canMove: true, headerFormattter: null, contentFormatter: OrderTimeFormatter },
            { id: 5, defaults: true, field: "ProductCount", name: "款/件", display: true, order: 5, minWidth: "70px", canMove: true, headerFormattter: null, contentFormatter: ProductCountContentFormatter },
            { id: 6, defaults: true, field: "Amount", name: "金额/含运费", display: true, order: 6, minWidth: "70px", canMove: true, headerFormattter: null, contentFormatter: AmountContentFormatter },
            { id: 7, defaults: true, field: "ReceiveAddress", name: "收件信息", display: true, order: 7, align: "left", minWidth: "150px", canMove: true, headerFormattter: null, contentFormatter: ReceiveAddressContentFormatter },
            { id: 71, defaults: true, field: "ToName", name: "收件人", display: false, order: 7, align: "left", minWidth: "55px", canMove: true, headerFormattter: null, contentFormatter: ToNameFormatter },
            { id: 72, defaults: true, field: "ToMobile", name: "联系电话", display: false, order: 7, align: "left", minWidth: "75px", canMove: true, headerFormattter: null, contentFormatter: ToMobileFormatter },
            { id: 73, defaults: true, field: "ToFullAddress", name: "收件地址", display: false, order: 7, align: "left", minWidth: "250px", canMove: true, headerFormattter: null, contentFormatter: ToFullAddressFormatter },
            { id: 81, defaults: true, field: "IsExpressSend", name: "派送", display: true, order: 8, minWidth: "33px", canMove: true, headerFormattter: IsSendExpressHeaderFormatter, contentFormatter: IsSendExpressContentFormatter },
            { id: 8, defaults: true, field: "LastExpressCode", name: "快递单号", display: true, order: 9, minWidth: "110px", canMove: true, headerFormattter: null, contentFormatter: LastExpressCodeContentFormatter },
            { id: 9, defaults: true, field: "Remark", name: "留言备注", display: true, order: 10, align: "left", minWidth: "150px", canMove: true, headerFormattter: null, contentFormatter: RemarkContentFormatter },
            { id: 10, defaults: true, field: "Product", className: "table_content_tbody_tdProducts", name: "产品详情", display: true, order: 11, minwidth: "280px", canMove: true, headerFormattter: ProductHearderFormatter, contentFormatter: ProductContentFormatter },
            //{ id: 11, defaults: true, field: "OrderNumber", name: "订单数量", display: false, order: 12, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 12, defaults: false, field: "ProductItemCount", name: "数量", display: false, order: 13, minWidth: "25px", canMove: true, headerFormattter: null, contentFormatter: ProductItemCountFormatter },
            { id: 121, defaults: false, field: "OrderCount", name: "订单", display: false, order: 13, minWidth: "25px", canMove: true, headerFormattter: null, contentFormatter: orderCountContentFormatter },
            { id: 13, defaults: false, field: "TotalWeight", name: "重量(kg)", display: false, order: 14, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: TotalWeightFormatter },
            { id: 14, defaults: false, field: "ShippingFee", name: "运费", display: false, order: 15, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: ShippingFeeFormatter },
            { id: 111, defaults: true, field: "SvcCODAmount", name: "代收货款", display: false, order: 16, width: "65px", canMove: true, headerFormattter: null, contentFormatter: DaiShouHuoKuanContentFormatter },
            { id: 151, defaults: false, field: "SvcInsureAmount", name: "保价金额", display: false, order: 17, width: "65px", canMove: true, headerFormattter: null, contentFormatter: BaoJiaJinEContentFormatter },
            { id: 15, defaults: false, field: "PayTime", name: "付款时间", display: false, order: 18, minWidth: "70px", canMove: true, headerFormattter: null, contentFormatter: PayTimeContentFormatter },
            { id: 16, defaults: false, field: "CreateTime", name: "下单时间", display: false, order: 19, minWidth: "70px", canMove: true, headerFormattter: null, contentFormatter: CreateTimeContentFormatter },
            { id: 17, defaults: false, field: "SenderName", name: "发件人", display: false, order: 20, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: SenderFormatter },
            { id: 18, defaults: false, field: "PrintTimes", name: "打印次数", display: false, order: 21, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: PrintTimesFormatter },
            //{ id: 18, defaults: false, field: "ExpressPrintTimes", name: "打快递次数", display: false, order: 18, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: null },
            //{ id: 19, defaults: false, field: "SendPrintTimes", name: "打发货次数", display: false, order: 19, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: null },
            //{ id: 20, defaults: false, field: "NahuoPrintTimes", name: "打拿货次数", display: false, order: 20, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 19, defaults: false, field: "PrintedSerialNumber", name: "流水号", display: false, order: 22, minWidth: "80px", canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 20, defaults: false, field: "OrderClassify", name: "订单分类", display: true, order: 23, minWidth: "80px", canMove: true, headerFormattter: OrderClassifyHearderFormatter, contentFormatter: OrderClassifyCntentFormatter },
            { id: 21, defaults: false, field: "PlatformOrderId", name: "订单编号", display: false, order: 24, minWidth: "100px", canMove: true, headerFormattter: null, contentFormatter: PlatformOorderIdFormatter },
            { id: 22, defaults: false, field: "OrderFrom", name: "平台", display: false, order: 25, minWidth: "50px", canMove: true, headerFormattter: null, contentFormatter: OrderSourceFormatter },
            { id: 23, defaults: false, field: "ConfirmedTime", name: "成团时间", display: false, order: 26, minWidth: "70px", canMove: true, headerFormattter: null, contentFormatter: ConfirmedTimeContentFormatter },
            { id: 24, defaults: false, field: "LastShipTime", name: "订单剩余时间", display: false, order: 27, minWidth: "70px", canMove: true, headerFormattter: null, contentFormatter: LastShipTimeContentFormatter },
            { id: 25, defaults: false, field: "ShopId", name: "店铺名称", display: false, order: 28, width: "90px", canMove: true, headerFormattter: null, contentFormatter: shopNameContentFormatter },
            { id: 26, defaults: false, field: "ExtField2", name: "发票类型", display: false, order: 29, width: "90px", canMove: true, headerFormattter: null, contentFormatter: invoiceContentFormatter },
            { id: 27, defaults: false, field: "ExtField1", name: "客服承诺", display: false, order: 30, width: "90px", canMove: true, headerFormattter: null, headerFormattter: PromiseHearderFormatter, contentFormatter: servicePromiseFormatter },
            { id: 28, defaults: false, field: "ExtField4", name: "指定包材", display: false, order: 31, width: "90px", canMove: true, headerFormattter: null, contentFormatter: wrapperDescFormatter },
            { id: 29, defaults: false, field: "Detail", name: "详情", display: true, className: "table_content_tbody_tdMore", order: 32, width: "80px", canMove: true, headerFormattter: DetailHearderFormatter, contentFormatter: DetailContentFormatter }
        ];

    if (!table.columns) {
        if (platformType != "pinduoduo") {
            var removeFields = [];
            if (platformType == "kuaishou")
                removeFields = ["ConfirmedTime", "LastShipTime", "SvcCODAmount", "SvcInsureAmount", "ExtField1", "ExtField4"];
            else if (platformType == "toutiao")
                removeFields = ["ConfirmedTime", "ExtField1", "ExtField4"];
            else
                removeFields = ["ConfirmedTime", "LastShipTime", "ExtField1", "ExtField4"];

            $(removeFields).each(function (i, field) {
                $(columns).each(function (ii, col) {
                    if (platformType == "kuaishou" && col.field == "BuyerWangWang" && col.name == "买家旺旺") {
                        col.name = "买家昵称";
                        return;
                    }
                    if (platformType == "wxxiaoshangdian" && col.field == "BuyerWangWang" && col.name == "买家旺旺") {
                        col.name = "买家昵称";
                        return;
                    }
                    if (col.field == field) {
                        columns.splice(ii, 1);
                        return;
                    }
                });
            });
        }
        table.columns = columns;
    }
    table.Setting = {};
    //将表格内容插入到指定的元素中
    var _render = function (element, rows) {
        if (!element)
            return;
        var html = '<table class="orderList_table_content"  style="border-collapse:separate; border-spacing:0px 1px;"><thead><tr>';
        var cols = common.Sort(table.columns, "order", false);
        //生成表格头部
        $(cols).each(function (index, col) {
            var style = "";
            if (col.width)
                style += "width:" + col.width + ";";
            if (col.minWidth)
                style += "min-width:" + col.minWidth + ";";
            if (col.align)
                style += "text-align:" + col.align + ";";
            if (!col.display)
                style += "display:none";
            if (col.field == "checkbox") {
                html += '<th style="' + style + '" class="' + col.field + '"><input class="order-chx-all" type="checkbox"></th>';
            } else {
                // 通过别名字段动态更新字段值
                var aliasField = col.aliasField && col.aliasField != "" ? 'data-aliasField="' + col.aliasField + '"' : "";
                html += '<th style="' + style + '" ' + aliasField + ' class="' + col.field + '">' + defaultHeaderFormatter(col) + '</th>';
            }
        });
        html += "</tr></thead>";
        html += '<tbody class="table_content_tbody">';

        // 单独查询订单编号或运单号
        var container = $(".orderList_search>ul>li [name]");
        var platformOrderIdVal = container.filter("[name=PlatformOrderId]").val() || "";
        var waybillCodeVal = container.filter("[name=LastWaybillCode]").val() || "";
        isQueryPlatfromOrderIdOrWaybillCode = platformOrderIdVal != "" || waybillCodeVal != "";

        // 快捷筛选字段
        quickScreenOrderField = $(".QuickScreenOrder :selected").val();

        // 快捷筛选字段
        quickScreenOrderField = $(".QuickScreenOrder :selected").val();
        // 订单分类样式
        orderClassifyArr = [];
        $(".orderClassify_main>ul>li").each(function () {
            var name = $(this).find(">span").eq(0).text();
            var color = $(this).find(">span").eq(1).css("color");
            var alias = $(this).find("input").val();
            var id = $(this).find("input").attr("data-id");

            orderClassifyArr.push({ "Id": id, "Name": name, "Alias": alias, "Color": color });
        });

        //生成表格内容
        var iscorder = common.isCustomerOrder();
        //console.time("渲染表格行");
        $(rows).each(function (index, row) {
            row.Index = index;
            row.isCustomerOrder = iscorder;
            if (row.OrderFrom == "3") {
                //row.OrderFrom = "";
                row.IsCantSendGood = false;
            }
            html += _renderRow(cols, row);
        });
        //console.timeEnd("渲染表格行");
        html += "</tbody></table>";
        //console.time("渲染表格行到dom");
        //$(element).html(html);
        element.innerHTML = html;
        //console.timeEnd("渲染表格行到dom");
        setTimeout(function () {
            InitOrderTableEvent();
            //$(".order-row").unbind("click").bind("click", row_click_handler);
            $(".order-chx-all").bind("click", function () {
                $("#footers_chooseOrderMun").html('快捷勾选');
                var isChecked = this.checked;
                var cantSendCount = 0;
                var canSendCount = 0;
                var status = $("#orderList_orderState_choose span.active").attr("data-status");
                $(".order-chx").each(function (i, chx) {
                    var index = $(chx).attr("data-index");
                    var row = table.rows[index];
                    if (row.IsLocked == 1) return;

                    if (row.IsCantSendGood && isChecked) {
                        chx.checked = false
                        row.checked = false; //数据选中标识
                        $("#order-" + row.Id).removeClass("onClickColor");
                        cantSendCount++;
                    }
                    else {
                        var isCheckTmp = isChecked;
                        if (isChecked && commonModule.IsTouTiaoXi() && (status == "waitsellersend" || status == "waitbuyerreceive")) {
                            if (row.PlatformOrderId.startsWith('C')) {
                                //合并订单全部订单有退款时，才认为是退款订单，全选时不勾选
                                var isMergerChecked = false;
                                $(row.SubOrders).each(function (i2, so) {
                                    $(so.OrderItems).each(function (i3, soi) {
                                        if (!(soi.RefundStatus == 'REFUND_SUCCESS' || soi.RefundStatus == 'WAIT_SELLER_AGREE')) {
                                            isMergerChecked = true;
                                            return;
                                        }
                                    });
                                });
                                isCheckTmp = isMergerChecked;
                            }
                            else {
                                isCheckTmp = !(row.RefundStatus == 'REFUND_SUCCESS' || row.RefundStatus == 'WAIT_SELLER_AGREE');
                            }
                        }
                        chx.checked = isCheckTmp;
                        row.checked = isCheckTmp;
                        if (isCheckTmp)
                            $("#order-" + row.Id).addClass("onClickColor");
                        else
                            $("#order-" + row.Id).removeClass("onClickColor");
                        canSendCount++;
                    }
                });


                if (cantSendCount > 0) {
                    var cantSendReason = "阻止发货";
                    if (table.rows[0].PlatformType == "YouZan")
                        cantSendReason = "分销买家";
                    if (canSendCount == 0) {
                        //当前订单是【分销买家订单】，不需要打单发货
                        layer.msg("提示：当前没有可供操作的订单，请检查是否都是【" + cantSendReason + "订单】");
                        this.checked = false;
                    }
                    else
                        layer.msg("提示：系统自动排除了【" + cantSendCount + "】个" + cantSendReason + "订单");
                }

                if (canSendCount != 0 && isChecked) {
                    $("#footers_chooseOrderMun").html('已选<i style="color:#3aadff;padding:0 5px;">' + canSendCount + '</i>单');
                } else {
                    $("#footers_chooseOrderMun").html('快捷勾选');
                }

                console.log(canSendCount)


            });
            //var isAllChecked = $(".order-chx-all")[0].checked || false;
            $(".order-chx").bind("click", function (e) {
                var isChecked = this.checked;
                var $this = $(this);
                var row = table.rows[$this.attr('data-index')];
                if (row.IsCantSendGood) {
                    $this.checked = false;
                    $this.closest(".order-row").removeClass("onClickColor");
                    layer.msg(row.CantSendReason);
                    return;
                }
                row.checked = isChecked; //数据选中标识
                if (isChecked)
                    $this.closest(".order-row").addClass("onClickColor");
                else
                    $this.closest(".order-row").removeClass("onClickColor");
                var isAll = $(".order-chx:checked").length == table.rows.length;
                $(".order-chx-all")[0].checked = isAll;
                e.stopPropagation();

                var orderChx_checkedLength = $(".order-chx:checked").length;

                if (orderChx_checkedLength != 0) {
                    $("#footers_chooseOrderMun").html('已选<i style="color:#3aadff;padding:0 5px;">' + orderChx_checkedLength + '</i>单');
                } else {
                    $("#footers_chooseOrderMun").html('快捷勾选');
                }
            });

            var template = addTmplInOrderListModule.GetCurrentTemplate();
            table.changeTemplate(template);
            //仅拼多多
            if (common.PlatformType == "Pinduoduo") {
                orderPrintModule.FilterPromise();

                $("#sortWrap_main li").removeClass("active");
                $("#sortWrap_main li[data-filter='" + commonModule.PddPromiseFilter + "']").addClass("active")
            }
        }, 1);

        common.navActive("#sortWrap_main", function (index, item) {
            if (index != 0) {
                $("#promiseHearderTh_icon").addClass("active")

            } else {
                $("#promiseHearderTh_icon").removeClass("active")

            }


        })
    }

    //选择快递模板修改发件人信息
    table.updateSellerInfo = function (currentTemplate) {
        $(table.rows).each(function (index, row) {
            if (row.IsWeiGong == true || row.IsAgent == true || row.IsManualUpdateSeller == true) {
                return true;
            }
            row.SenderName = currentTemplate.SellerName;
            row.SenderMobile = currentTemplate.SellerMobile;
            row.SenderAddress = currentTemplate.SellerAddress;
        });
        //$("#aialog_SenderName").val(currentTemplate.SellerName);
        //$("#aialog_SenderMobile").val(currentTemplate.SellerMobile);
        //$("#aialog_SenderAddress").val(currentTemplate.SellerAddress);
        //_renderRow(currentTemplate.Index,currentTemplate)
        _render(table.target, table.rows);
        orderPrintModule.RenderProductPic();
        //table.refresh();
    };

    var _renderRow = function (cols, row) {
        if (!row.Id)
            return;
        row.IsCustomerOrder = common.isCustomerOrder(); //是否是自由打印订单
        //子订单选中状态及产品选中状态
        //$("#orderList_orderState_choose .active").attr("data-status");
        var status = row.PlatformStatus || "";
        var isCustomerOrder = commonModule.isCustomerOrder();
        $(row.SubOrders).each(function (index, subRow) {
            var flag = true;//子订单全选状态
            //待买家付款：除了待付款商品，其它商品状态勾选去掉
            //待发货：退款成功、商品关闭、已发货、已确认收货的商品勾选去掉
            //待买家确认收货和交易成功：商品关闭、退款成功的商品勾选去掉
            //交易关闭：所有商品都勾选上
            if (row.RefundStatus == "REFUND_CLOSE")
                row.RefundStatus = "";
            $(subRow.OrderItems).each(function (i, item) {
                var chx = false;
                if (item.RefundStatus == "REFUND_CLOSE")
                    item.RefundStatus = "";
                if (isCustomerOrder)
                    chx = true;
                else if (common.IsPddFds() == false) {
                    if (status == "waitbuyerpay") {
                        if (item.Status == "waitbuyerpay")
                            chx = true;
                    }
                    else if (status == "waitsellersend") {
                        if ((item.RefundStatus != null && item.RefundStatus != '') || item.Status == "waitbuyerreceive" || item.Status == "signinsuccess" || item.Status == "confirm_goods_but_not_fund" || item.Status == 'close' || item.Status == 'cancel' || item.Status == 'success') {
                            if (item.RefundStatus == "REFUND_CLOSE")
                                chx = true;
                            else
                                chx = false;
                        }
                        else
                            chx = true;
                    }
                    else if (status == "waitbuyerreceive" || status == "success") {
                        if (item.RefundStatus != "REFUND_SUCCESS")
                            chx = true;
                    }
                    else if (status == "close" || status == "cancel" && (item.Status == 'close' || item.Status == 'cancel')) {
                        chx = true;
                    }
                }
                else {
                    //拼多多厂商代打订单，默认都选中产品，只有已取消分配的才不选中产品
                    //<!-- 0:取消分配，1:待打印，2:待回传，3:已回传，4:已打印，已取消分配，5:已回传，已取消分配 -->
                    if (status == 1 || status == 2 || status == 3)
                        chx = true;
                }
                item.checked = chx;
                if (!item.checked)
                    flag = false;

                //有赞平台订单商品规格存储方式不一样，所以这里要特殊处理 
                if (platformType == "youzan") {
                    var keys = item.ExtAttr4 ? item.ExtAttr4.replace(/&nbsp;/g, ' ').split(';') : [];
                    var vals = item.Color ? item.Color.replace(/&nbsp;/g, ' ').split(';') : [];
                    var productAttr = '';
                    for (var x = 0; x < keys.length; x++) {
                        var key = keys[x];
                        var val = vals[x];
                        productAttr += key + ":" + val + ";";
                    }
                    if (productAttr == '') {
                        if (item.Color)
                            productAttr += item.Color + ";";
                        if (item.Size)
                            productAttr += item.Size;
                    }
                    if (productAttr.indexOf(';') > -1)
                        productAttr = productAttr.trimEndDgj(';').trimStartDgj(';');
                    item.ProductAttr = productAttr;
                }
                else if (platformType == "kuaishou") {
                    item.ProductAttr = item.Color;
                }

            });
            subRow.checked = flag;
        });

        //生成打印内容,自由打印不生成，或者打印内容为空时，自由打印也生成
        row.isCustomerOrder = common.isCustomerOrder();
        row.PrintInfo = row.PrintContent ? keywordFilterReplaceModule.FilterAndReplace(row.PrintContent) : '';
        if (row.isCustomerOrder == false || row.PrintContent == '' || row.PrintContent == null) {
            row.PrintInfo = printContentFormatSetModule.ForatPrintContent(row);
            row.PrintContent = row.PrintInfo;
        }
        var rowClass = "";
        if (row.IsCantSendGood)
            rowClass = " order-row-cantsend ";
        var html = '<tr id="order-' + row.Id + '" data-index="' + row.Index + '" class="order-row' + rowClass + (row.IsLocked == "1" ? " suotouColor" : "") + (row.checked == true ? " onClickColor" : "") + (row.zk == true ? " showMoreColor" : "") + '" onclick="orderTableBuilder.row_click_handler.bind(this)()">';
        $(cols).each(function (index2, col) {
            var style = "";
            if (col.align)
                style += "text-align:" + col.align + ";";
            if (!col.display)
                style += "display:none";
            var className = col.className ? col.className : "";
            className += " " + col.field;
            html += "<td style='" + style + "' class='" + className + "'>"
            html += defaultContentFormatter(row, col, row.Index);
            html += "</td>";
        });
        html += "</tr>";
        //TODO:订单详细信息
        html += _renderDetailInfo(row);
        html += _renderListPicInfo(row);
        return html;
    }

    table.row_click_handler = function () {
        var row = table.rows[$(this).attr('data-index')];
        var chx = $(this).find(".order-chx")[0];
        if (row.IsCantSendGood) {
            chx.checked = false;
            $(this).removeClass("onClickColor");
            if (row.PlatformType == "Alibaba")
                layer.msg(row.CantSendReason);
            else if (row.PlatformType == "YouZan")
                layer.msg("当前订单是【分销买家订单】，不需要打单发货");
            return;
        }
        chx.checked = !chx.checked;
        $(this).toggleClass("onClickColor");
        row.checked = chx.checked; //数据选中标识
        var isAll = $(".order-chx:checked").length == $(".order-chx").length;
        $(".order-chx-all")[0].checked = isAll;

        if ($(".order-chx:checked").length != 0) {
            $("#footers_chooseOrderMun").html('已选<i style="color:#3aadff;padding:0 5px;">' + $(".order-chx:checked").length + '</i>单');
        } else {
            $("#footers_chooseOrderMun").html('快捷勾选');
        }

    };
    var _renderListPicInfo = function (row) {

        //rowDownSelectCount = 5;    //从设置表单 select 传来的值

        row.columnCount = table.columns.length;
        var products = [];
        var count = 0;

        if (common.isUseOldTheme) { //老版本写死，最多显示10条,超过10条显示更多按钮
            var maxCount = 11;
        } else {
            var maxCount = orderPrintModule.RowDownSelectCount() + 1;
        }


        for (var i = 0; i < row.SubOrders.length; i++) {
            var sub = row.SubOrders[i];
            if (count >= maxCount) {
                break;
            }
            for (var j = 0; j < sub.OrderItems.length; j++) {
                if (count >= maxCount) {
                    break;
                }
                products.push(sub.OrderItems[j]);
                count++;
            }
        }
        if (common.isUseOldTheme) {   //老版本写死，最多显示10条
            row.products = products.splice(0, 10);
            row.OrderItemsLength = count;
            row.selectCount = 10;
        } else {
            row.products = products.splice(0, orderPrintModule.RowDownSelectCount());//取前n条   新版本从数据库配制显示条数
            row.OrderItemsLength = count;
            row.selectCount = orderPrintModule.RowDownSelectCount();
        }



        //var currentStatusSpan = $("#orderList_orderState_choose span[class='active']");
        //var currentStatus = "";
        //if (currentStatusSpan)
        //    currentStatus = currentStatusSpan.attr("data-status");
        //row.CurrentStatus = currentStatus;

        row.CurrentStatus = row.PlatformStatus || "";
        var preFix = common.isUseOldTheme ? "#old-" : "#";
        var orderDetailTmpl = $.templates(preFix + "order-listPic-tmpl");
        var productShowType = commonModule.ProductShowType;
        var html = orderDetailTmpl.render({ row, productShowType: productShowType });

        //console.log("rowrowrowrow", row)
        return html;
    }

    var _renderDetailInfo = function (row) {
        row.columnCount = table.columns.length;

        //var currentStatusSpan = $("#orderList_orderState_choose span[class='active']");
        //var currentStatus = "";
        //if (currentStatusSpan)
        //    currentStatus = currentStatusSpan.attr("data-status");
        //row.CurrentStatus = currentStatus;

        row.CurrentStatus = row.PlatformStatus || "";
        row.PlatformType = commonModule.isCustomerOrder() ? commonModule.PlatformType : row.PlatformType;
        row.IsPddFds = common.IsPddFds();
        var preFix = common.isUseOldTheme ? "#old-" : "#";
        var orderDetailTmpl = $.templates(preFix + "order-detail-tmpl");
        var html = orderDetailTmpl.render(row);
        return html;

    }

    table.LoadOptions = {};

    var _isQuerying = false;
    table.IsQuerying = function getIsQuerying() {
        return _isQuerying;
    }
    table.progress = function (percent) {
        $(".dgj-progress-bar").css({ width: percent + '%' });
        $(".dgj-progress-percent").html(percent + '%');
    }
    table.progressTimer = null;
    table.IsLoading = false;
    table.startProgress = function (from) {

        if (table.progressTimer) return;

        $(".syncOrder-btn").addClass("stop"); //禁用手工同步按钮
        $("#search-btn").attr("disabled", true);

        $(".progress-wrap").show();
        table.progressTimer = setInterval(function () {
            if (!table.IsLoading) {
                table.IsLoading = true;
                if (table.blocked)
                    return;
                common.get("/Order/SyncInfo?from=" + from + "&requestUrl=" + location.href.split('?')[0], function (rsp) {
                    var percent = 0;
                    table.IsLoading = false;
                    if (rsp.Success) {
                        table.progress(rsp.Data.Percent);
                        if (rsp.Data.IsFinished) {
                            common.IsSyncProcessEnabled = false;
                            table.endProgress();
                        } else {
                            if (rsp.Data.ErrorMessage == "blocked")
                                table.blocked = true;
                            else
                                table.blocked = false;
                        }
                    }
                    else {
                        common.IsSyncProcessEnabled = false;
                        table.endProgress();
                    }
                });
            }
        }, 750);
    }

    table.endProgress = function () {
        if (table.progressTimer) {
            clearInterval(table.progressTimer);
            table.progressTimer = null;
        }
        if (common.IsSyncOrderByHand) {
            orderPrintModule.SyncOrderByHandTimer(60);
        }
        _isQuerying = false;
        $("#search-btn").attr("disabled", false);
        $(".progress-wrap").hide();
        $("#search-btn").trigger("click");
    }

    //从接口加载数据
    table.load = function (element, options, isPaging, isPageSizeChange) {
        if (common.isMigrating)
            return;
        if (common.isExpired)
            return;
        if (common.IsSyncProcessEnabled) {
            table.sync();
            table.startProgress();
            return;
        }
        if (!element)
            element = $("#order-list-table")[0];
        table.target = element;
        //TODO:获取查询条件
        if (options == undefined) {
            var endTime = new Date();
            var startTime = new Date();
            startTime = new Date(startTime.setDate(startTime.getDate() - 15));
            var endTimeFmt = endTime.format("yyyy-MM-dd hh:mm:ss");
            var startTimeFmt = startTime.format("yyyy-MM-dd hh:mm:ss");

            options = { PageSize: _pageSize, PageIndex: _pageIndex, IsOrderDesc: true, OrderByField: "o.CreateTime" };
            if (!commonModule.isCustomerOrder()) {
                // 默认待发货
                var shopId = commonModule.CurrShop.Id || "";
                options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: "P_Order", Name: "PlatformStatus", Value: "waitsellersend", Contract: "=" });
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "ShopId", Value: shopId, Contract: "in" });
                options.Filters.push({ TableAlias: "o", FieldType: "DateTime", TableName: "P_Order", Name: "CreateTime", Value: startTimeFmt, ExtValue: endTimeFmt, Contract: "between" });
            }
            else {
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_CustomerOrder", Name: "ShopId", Value: shopId, Contract: "in" });
                options.Filters.push({ TableAlias: "o", FieldType: "bool", TableName: "P_CustomerOrder", Name: "IsDeleted", Value: "0", Contract: "=" });
                options.Filters.push({ TableAlias: "o", FieldType: "bool", TableName: "P_CustomerOrder", Name: "IsPreordain", Value: "0", Contract: "=" });
                options.Filters.push({ TableAlias: "o", FieldType: "DateTime", TableName: "P_CustomerOrder", Name: "CreateTime", Value: startTimeFmt, ExtValue: endTimeFmt, Contract: "between" });
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_CustomerOrder", Name: "ExpressPrintTimes", Value: "0", Contract: "=", Operator: "AND", CustomQuery: "freeprint_wait_print" });
            }
        }
        options.PageSize = options.PageSize || _pageSize;
        options.PageIndex = options.PageIndex || _pageIndex;
        options.OrderByField = options.OrderByField || "o.CreateTime";
        options.IsDontSyncOrder = false;
        if (!isPaging || isPageSizeChange) {
            _pageIndex = 1;
            options.PageIndex = _pageIndex;
        }

        // Excel导出查询参数
        table.LoadOptions = options;
        // 查询时禁用查询、高级查询和导出订单
        _isQuerying = true;

        //$(".orderList_main,.footer").hide();
        element.innerHTML = "";
        options.isFilterDangerChar = false; //不过滤危险字符
        var msg = "查询中...";
        //if (common.IsSyncProcessEnabled) {
        //    msg = null;
        //    $(".orderList_expressTemplate, .orderList_main,.separation:last").hide();
        //    $(".progress-wrap").show();
        //    table.progress(0);
        //    table.startProgress();
        //}
        options.requestUrl = location.href.split('?')[0];
        //TODO:添加配置
        common.Ajax({
            url: '/Order/List',
            data: options,
            type: 'POST',
            loadingMessage: msg,
            showMasker: false,
            success: function (rsp) {
                //console.timeEnd("获取订单");    
                if (rsp.Success) {
                    if (rsp.Data.IsSyncing) {
                        table.startProgress("list");
                        return;
                    }
                    var logId = rsp.Data.LogId;
                    table.rows = rsp.Data.Rows;
                    table.Setting = rsp.Data.Setting;
                    //commonModule.ProductAttrs = rsp.Data.ProductAtrs;
                    //if (!commonModule.isCustomerOrder())
                    //    orderPrintModule.LoadProductList(rsp.Data.ProductAtrs);
                    // 退款数量
                    var refundTotal = rsp.Data.RefundTotal;
                    // 退货退款数量
                    var otherRefundTotal = rsp.Data.OtherRefundTotal;
                    // 订单状态统计行数
                    var orderCount = rsp.Data.RealOrderTotal;
                    var $span = $("#orderList_orderState_choose span").filter(".active");
                    //debugger;
                    if ($span.length > 0) {
                        var orderStatus = $span.attr("data-status");
                        $("#orderList_orderState_choose>span>i").remove();
                        if (orderStatus == "orderList_refund") {
                            $(".orderList_refundCount").text(refundTotal);
                        }
                        else if (orderStatus == "locked" || orderStatus == "locked_sended") {
                            $("#jd_refunding .orderList_refundCount").text(refundTotal);
                            //if (platformType == "toutiao")
                            //    $("#jd_other_refunding .orderList_refundCount").text(otherRefundTotal);
                        }
                        else {
                            // 点击退款状态外其他状态下订单数量
                            $span.append("<i class='i-order-count' title='实际订单数量'>[" + orderCount + "]</i>");
                            //显示退款状态下订单数量
                            if (platformType == "jingdong") {
                                $("#jd_refunding .orderList_refundCount").text(refundTotal);
                            }
                            //else if (platformType == "toutiao") {
                            //    $("#jd_refunding .orderList_refundCount").text(refundTotal);
                            //    $("#jd_other_refunding .orderList_refundCount").text(otherRefundTotal);
                            //}
                            else
                                $(".orderList_refundCount").text(refundTotal);
                        }
                    }
                    //根据订单ID查询，不管什么状态都需要显示发货按钮
                    var poSearchField = $('#SeachConditions  input[name="PlatformOrderId"]');
                    if (poSearchField && poSearchField.length == 1) {
                        var val = poSearchField.val();
                        if (val != "" && val.trim() != "") {
                            $(".batch-send-btnonly-in-order-list").show();
                        }
                    }
                    // 时间筛选的text和val值
                    var name = $("#QueryDate>option:selected").text();
                    var val = $("#QueryDate>option:selected").val();
                    if (name != "" && val != "") {
                        orderTableBuilder.changeOrderTime(val, name, false);
                    }
                    var date1 = new Date();  //开始时间
                    console.time("渲染表格");
                    _render(element, table.rows);
                    console.timeEnd("渲染表格");
                    //configRenderToHtml(2);
                    // 商品图片显示位置    
                    orderPrintModule.RenderProductPic();
                    var date2 = new Date();    //结束时间
                    var ms = date2.getTime() - date1.getTime()  //时间差的毫秒数
                    upadteRenderTime(logId, ms);

                    // 头条系平台禁用修改留言备注功能
                    //if (platformType == "toutiao" || platformType == "yunji" || platformType == "vipshop") {
                    if (commonModule.CanUpdateSellerRemark(platformType) == false) {
                        var $detailTr = $("tr[id^='order-detail']");
                        $detailTr.find("input[id^='txt_buyer_remark_']").attr("disabled", "disabled");
                        $detailTr.find("input[id^='txt_seller_remark']").attr("disabled", "disabled");
                        $detailTr.find("input[id^='txt_seller_remark']~span").hide();
                    }
                    //if (isPaging == true) {
                    //    return;
                    //}
                    //异步执行，不影响页面加载
                    setTimeout(function () {
                        $('#sp_free_order_count').text(rsp.Data.Total);
                        var layout = ['count', 'prev', 'page', 'next', 'limit', 'skip'];
                        if (common.isUseOldTheme)
                            layout = ['limit', 'prev', 'page', 'next'];
                        if (!isPaging || isPageSizeChange) {
                            layPage.render({
                                elem: 'paging'
                                , count: rsp.Data.Total
                                , limit: _pageSize
                                , curr: _pageIndex
                                , limits: [20, 40, 50, 60, 100, 200, 300, 400, 500]
                                , layout: layout
                                , jump: function (obj, first) {
                                    $(".layui-laypage-count").hide(); // 隐藏分页总条数
                                    if (!first) {
                                        _pageIndex = obj.curr;
                                        var isPageSizeChange = false;
                                        if (_pageSize != obj.limit) {
                                            _pageSize = obj.limit;
                                            //savePageSize(obj.limit);
                                            orderPrintModule.SavePaggingOrderBySet()
                                            isPageSizeChange = true;
                                        }

                                        options.PageSize = _pageSize;
                                        options.PageIndex = _pageIndex;

                                        table.load(element, options, true, isPageSizeChange);
                                    }

                                    $('html , body').animate({ scrollTop: 0 }, 0);
                                }
                            });
                        }
                        //if (table.rows && table.rows.length > 0) {
                        //    var keys = [];
                        //    var printedCount = 0;
                        //    for (var i = 0; i < table.rows.length; i++) {
                        //        var row = table.rows[i];
                        //        var cid = [];
                        //        if (row.ChildOrderId && row.ChildOrderId != '')
                        //            cid = row.ChildOrderId.split(',');
                        //        var isPrint = row.LastExpressPrintTime != null || row.IsPreviewed;
                        //        isPrint = true;
                        //        if (isPrint)
                        //            printedCount++;
                        //        keys.push({ Id: row.Id, PlatformOrderId: row.PlatformOrderId, ShopId: row.ShopId, ChildPlatformOrderIds: cid,IsPrint:isPrint });
                        //    }
                        //    if (printedCount > 0) {
                        //        common.post("/Order/GetWaybillCodeByOrderKey", { keys: keys }, function (r) {
                        //            if (r.Success && r.Data) {
                        //                for (var j = 0; j < r.Data.length; j++) {
                        //                    table.rows[j].WaybillCodes = r.Data[j].Value;
                        //                }
                        //                var t = addTmplInOrderListModule.GetCurrentTemplate()
                        //                table.changeTemplate(t);
                        //            }
                        //        });
                        //    }
                        //}
                        var t = addTmplInOrderListModule.GetCurrentTemplate()
                        table.changeTemplate(t);
                    }, 1);
                }
                else {
                    if (rsp.ErrorCode != 2 && rsp.ErrorCode != 'auth_expires')
                        layer.msg("数据加载失败，请刷新重试");
                }
                if (common.IsSyncProcessEnabled) {
                    setTimeout(function () {
                        $(".progress-wrap").hide();
                        $(".orderList_expressTemplate, .orderList_main,.separation").show();
                        table.endProgress();
                    }, 300);
                }
                else {
                    setTimeout(function () {
                        checkIsSyncing();
                    }, 500);
                }
                _isQuerying = false;
                //$(".orderList_main,.footer").show();
            }
        });
    }

    table.sync = function () {
        common.Ajax({
            url: '/Order/Sync',
            data: { requestUrl: location.href.split('?')[0] },
            type: 'POST',
            success: function (rsp) { }
        });
    }

    var upadteRenderTime = function (logId, ms) {
        var options = { logId: logId, ms: ms };
        common.Ajax({
            url: '/Order/UpdateRenderTime',
            data: options,
            type: 'POST',
            success: function (rsp) { }
        });
    }

    table.SetPageSize = function (pageSize) {
        _pageSize = pageSize;
    }

    table.GetPageSize = function () {
        return _pageSize || 20;
    }

    table.SetDefaultPageIndex = function () {
        _pageIndex = 1;
    }


    //刷新table
    table.refresh = function (options) {
        table.load(table.target, options);
    }
    table.getSelectionsExt = function (orders) {
        //计算退款的订单数量
        if (orders == null || orders.length <= 0)
            return {};
        var refundOrder = [];
        var refundItem = [];
        var productCount = 0;
        var productItemCount = 0;
        $(orders).each(function (index, order) {
            var entity = table.rows[order.Index];
            var cur = { Id: entity.Id, RefundItems: [] };
            var subs = entity.SubOrders;
            for (var i = 0; i < subs.length; i++) {
                var sub = subs[i];
                for (var j = 0; j < sub.OrderItems.length; j++) {
                    var oi = sub.OrderItems[j];
                    if (entity.RefundStatus && oi.RefundStatus && entity.RefundStatus != 'REFUND_CLOSE' && entity.RefundStatus != 'refundclose'
                        && oi.RefundStatus != 'REFUND_CLOSE' && oi.RefundStatus != 'refundclose'
                    ) {
                        cur.RefundItems.push(oi.Id);
                        refundItem.push(oi.Id);
                    }
                    //var ischeck = $(".orderitem-chx[data-id='" + oi.Id + "']")[0].checked;
                    var ischeck = oi.checked == true;
                    if (ischeck) {
                        productCount += 1;
                        productItemCount += oi.Count;
                    }
                }
            }
            if (entity.RefundStatus && entity.RefundStatus != 'REFUND_CLOSE' && entity.RefundStatus != 'refundclose')
                refundOrder.push(cur);
        });
        return { RefundOrders: refundOrder, RefundItems: refundItem, productItemCount: productItemCount, productCount: productCount };
    }

    /*****************
    获取选择的订单数据
    参数 orderId 指定的订单id，获取选中的订单时不用填写
    参数 isValidation 是否验证订单信息：验证商品有没有勾选
    *****************/
    table.getSelections = function (orderId, isValidation, needChildOrderId) {
        var selections = [];
        var ochxs = [];
        if (orderId)
            ochxs = $(".order-chx[data-id='" + orderId + "']")
        if (ochxs && ochxs.length > 0) {
            var $chx = $(ochxs[0])
            var order = {};
            order.Id = $chx.attr("data-id");
            order.Index = $chx.attr("data-index");
            order.PlatformOrderId = $chx.attr("data-pid");
            order.CustomerOrderId = table.rows[order.Index].CustomerOrderId;
            order.LastExpressPrintTime = table.rows[order.Index].LastExpressPrintTime;
            order.ShopId = $chx.attr("data-sid");
            order = getOrderInfo(order, isValidation);
            if (needChildOrderId) {
                var row = table.rows[order.Index];
                order.ChildOrderId = row.ChildOrderId;
            }
            if (order == null)
                return [];
            selections.push(order);
        }
        else {
            var count = table.rows.length;
            for (var i = 0; i < count; i++) {
                var row = table.rows[i];
                if (!row.checked || !row.Id)
                    continue;
                var order = {};
                order.Id = row.Id;
                order.Index = row.Index;
                order.PlatformOrderId = row.PlatformOrderId;
                order.CustomerOrderId = row.CustomerOrderId;
                order.LastExpressPrintTime = row.LastExpressPrintTime;
                order.ShopId = row.ShopId;
                order = getOrderInfo(order, isValidation);
                if (needChildOrderId)
                    order.ChildOrderId = row.ChildOrderId;
                if (order == null)
                    return [];
                selections.push(order);
            }
        }
        return selections;
    }

    function getOrderInfo(order, isValidation) {
        var wc = $("#order-" + order.Id + " .LastWaybillCode_input").val();
        if (wc && wc.trim() != '打印后返回')
            order.WaybillCode = wc;
        else
            order.WaybillCode = "";
        order.OrderItems = [];
        order.Receiver = {};
        order.Sender = {};
        order.Buyer = {};
        var orderEntity = table.rows[order.Index];
        //子订单
        var orderIdName = "#order-detail-" + order.Id;
        var oichxs = $(orderIdName + " .orderitem-chx:checked");
        if (oichxs != null && oichxs.length > 0) {
            $(oichxs).each(function (index2, ichx) {
                order.OrderItems.push($(ichx).attr("data-id"));
            });
        }
        //收件人信息
        //拼多多 非待发货订单 界面不再显示发件人信息明文，所以要取rows里面的数据
        if (orderEntity.PlatformType == "Pinduoduo" && orderEntity.PlatformStatus != 'waitsellersend') {
            order.Receiver.ToName = orderEntity.ToName;
            order.Receiver.ToMobile = orderEntity.ToMobile ? orderEntity.ToMobile : orderEntity.ToPhone;
            order.Receiver.ToAddress = orderEntity.ToFullAddress ? orderEntity.ToFullAddress : (orderEntity.ToProvince + " " + orderEntity.ToCity + " " + orderEntity.ToCounty + " " + orderEntity.ToAddress);
        }
        else {
            order.Receiver.ToName = $(orderIdName + " .ToName-input").val();
            order.Receiver.ToMobile = $(orderIdName + " .ToMobile-input").val();
            order.Receiver.ToAddress = $(orderIdName + " .ToAddress-input").val();
        }
        order.Receiver.toFullName = order.Receiver.ToName;
        order.Receiver.toArea = order.Receiver.ToAddress;
        //发件人信息
        order.Sender.SenderName = orderEntity.SenderName;//$(orderIdName + " .SenderName-input").val();
        order.Sender.SenderPhone = orderEntity.SenderMobile ? orderEntity.SenderMobile : orderEntity.SenderPhone;//$(orderIdName + " .SenderPhone-input").val();
        order.Sender.SenderAddress = orderEntity.SenderAddress;//$(orderIdName + " .SenderAddress-input").val();
        order.Sender.SenderCompany = orderEntity.SenderCompany ? orderEntity.SenderCompany : "";
        order.Sender.CompanyName = order.Sender.SenderCompany;
        order.PrintInfo = $(orderIdName + " .product-info-txt").val();
        //买家信息
        order.Buyer.BuyerMemberId = orderEntity.BuyerMemberId;
        order.Buyer.BuyerMemberName = orderEntity.BuyerMemberName;
        order.Buyer.BuyerWangWang = orderEntity.BuyerWangWang;
        if (isValidation && order.OrderItems.length <= 0 && !common.isCustomerOrder) {
            layer.closeAll();
            layer.alert("买家【" + order.Buyer.BuyerMemberName + "】的订单未勾选任何商品，请勾选商品");
            return null;
        }
        order.PlatformType = orderEntity.PlatformType;
        order.IsWeiGong = orderEntity.IsWeiGong;
        order.ExtField1 = orderEntity.ExtField1;

        //买家留言,卖家备注
        order.BuyerRemark = '';
        order.SellerRemark = '';
        if (orderEntity.ChildOrderId) {
            var l = orderEntity.SubOrders.length;
            common.Foreach(orderEntity.SubOrders, function (i, so) {
                order.BuyerRemark += $('#txt_buyer_remark_' + so.Id).val();
                order.SellerRemark += $('#txt_seller_remark_' + so.Id).val();
                if (i < (l - 1)) {
                    order.BuyerRemark += '|||';
                    order.SellerRemark += '|||';
                }
            });
            order.BuyerRemark;
            order.SellerRemark;
        }
        else {
            order.BuyerRemark = $('#txt_buyer_remark_' + orderEntity.Id).val();
            order.SellerRemark = $('#txt_seller_remark_' + orderEntity.Id).val();
        }

        return order;
    }


    /*****************
    获取选择的订单数据
    参数 orderId 指定的订单id，获取选中的订单时不用填写
    参数 isValidation 是否验证订单信息：验证商品有没有勾选
    *****************/
    table.getSelectionsWithStatus = function (orderId, isValidation) {
        var selections = [];
        var expressPrintCount = 0;
        var fahuoPrintCount = 0;
        var nahuoPrintCount = 0;
        var ochxs = [];
        if (orderId)
            ochxs = $(".order-chx[data-id='" + orderId + "']")
        if (ochxs && ochxs.length > 0) {
            var $chx = $(ochxs[0])
            var order = {};
            order.Id = $chx.attr("data-id");
            order.Index = $chx.attr("data-index");
            order.PlatformOrderId = $chx.attr("data-pid");
            order.CustomerOrderId = table.rows[order.Index].CustomerOrderId;
            order.ShopId = $chx.attr("data-sid");
            order = getOrderInfo(order, isValidation);
            var orderEntity = table.rows[order.Index];
            if (orderEntity.LastExpressPrintTime)
                expressPrintCount++;
            if (orderEntity.LastNahuoPrintTime)
                nahuoPrintCount++;
            if (orderEntity.LastSendPrintTime)
                fahuoPrintCount++;
            if (order == null)
                return [];
            selections.push(order);
        }
        else {
            var count = table.rows.length;
            for (var i = 0; i < count; i++) {
                var row = table.rows[i];
                if (!row.checked || !row.Id)
                    continue;
                var order = {};
                order.Id = row.Id;
                order.Index = row.Index;
                order.PlatformOrderId = row.PlatformOrderId;
                order.CustomerOrderId = row.CustomerOrderId;
                order.ShopId = row.ShopId;
                order = getOrderInfo(order, isValidation);
                if (order == null)
                    return [];
                if (row.LastExpressPrintTime)
                    expressPrintCount++;
                if (row.LastNahuoPrintTime)
                    nahuoPrintCount++;
                if (row.LastSendPrintTime)
                    fahuoPrintCount++;
                selections.push(order);
            }
        }
        var refundInfo = table.getSelectionsExt(selections);
        var data = { orders: selections, expressPrintCount: expressPrintCount, fahuoPrintCount: fahuoPrintCount, nahuoPrintCount: nahuoPrintCount };
        return $.extend(refundInfo, data);
    }

    //刷新指定的列
    table.refreshColumn = function (columnFieldName) {
        var column = {};
        for (var i = 0; i < table.columns.length; i++) {
            var cur = table.columns[i];
            if (cur.field == columnFieldName) {
                column = cur;
                break;
            }
        }
        if (column) {
            $("td." + column.field).each(function (ix, td) {
                var index = $(td).parent().attr("data-index");
                var row = table.rows[index];
                // 重置订单分类，生成html时重新获取
                if (columnFieldName == "OrderClassify" && ix == 0) {
                    orderClassifyArr = [];
                }

                //var productShowStyle = commonModule.SystemoConfig.ProductShowStyleSet;
                //var showStyle = (commonModule.isCustomerOrder() ? productShowStyle.FreePrint.Style : productShowStyle.OrderPrint.Style) || "";
                //if (columnFieldName == "Product" && showStyle == "ShowDownRow") {
                //    var trHtml = _renderListPicInfo(row);
                //    $("tr.listPicTr[data-id='" + row.Id + "']").replaceWith(trHtml);  
                //}

                if (columnFieldName == "Product") {
                    var trHtml = _renderListPicInfo(row);
                    $("tr.listPicTr[data-id='" + row.Id + "']").replaceWith(trHtml);
                    orderPrintModule.RenderProductPic();
                }
                var html = defaultContentFormatter(row, column, index);
                $(td).html(html);
            });
        }

        // 重新生成html，绑定相应的事件
        if (columnFieldName == "OrderClassify") {
            orderPrintModule.InitOrderClassifyEvent();
        }
        if (columnFieldName == "LastWaybillCode") {
            var template = addTmplInOrderListModule.GetCurrentTemplate();
            table.changeTemplate(template);
        }
    }

    //刷新指定的行
    table.refreshRow = function (row) {
        orderTableBuilder.rows[row.Index] = row;
        var cols = common.Sort(table.columns, "order", false);
        var html = _renderRow(cols, row);
        var tr = $(".orderList_table_content>tbody>tr[data-index='" + row.Index + "']");
        tr.next().next().remove().end().remove();
        tr.replaceWith(html);
        orderPrintModule.RenderProductPic();//根据配置显示产品信息
        //$("img.lazy").lazyload({   //图片懒加载
        //    threshold: 500,
        //    placeholder: "/Content/Images/loading_small.gif"
        //});
        var template = addTmplInOrderListModule.GetCurrentTemplate();
        table.changeTemplate(template);

        var $moreOperationsSpan = $(".moreOperations_aialog>span");
        changeNavBackgroundColor($moreOperationsSpan);   //表格设置-更多选项--着行变色
    }

    table.setPrintState = function (datas, printType) {
        if (datas && datas.length > 0) {
            var field = "";
            for (var i = 0; i < datas.length; i++) {
                var kv = datas[i];
                var ix = $(".order-chx[data-id='" + kv.OrderId + "']").attr("data-index");
                var row = table.rows[ix];
                //1.快递 2 发货单 3拿货单
                if (printType == 1) {
                    row.LastExpressPrintTime = 1; //最后打印时间
                    row.ExpressPrintTimes += 1; //打印次数
                    $('#sp_expr_print_count_' + row.Id).text(row.ExpressPrintTimes); //更新打印次数
                    row.PrintedSerialNumber = kv.PrintedSerialNumber; //打印序列号
                    row.ExpressPrintedCount = kv.OrderItemIds.length; //打印产品类别数量
                    for (var j = 0; j < kv.OrderItemIds.length; j++) {
                        var oi_id = kv.OrderItemIds[j];
                        $("#product-print-state-td-" + oi_id + " .product-express-print-state").html("√");
                        //更新订单项的打印时间
                        common.Foreach(row.SubOrders, function (idx, subOrd) {
                            common.Foreach(subOrd.OrderItems, function (idx2, soi) {
                                if (oi_id == soi.Id) {
                                    soi.LastExpressPrintTime = '1'; //有值表示已打印
                                }
                            });
                        });
                    }

                }
                else if (printType == 2) {
                    row.LastSendPrintTime = 1;
                    row.FahuoPrintedCount = kv.OrderItemIds.length;
                    for (var j = 0; j < kv.OrderItemIds.length; j++) {
                        var oi_id = kv.OrderItemIds[j];
                        $("#product-print-state-td-" + oi_id + " .product-fahuo-print-state").html("√");
                        //更新订单项的打印时间
                        common.Foreach(row.SubOrders, function (idx, subOrd) {
                            common.Foreach(subOrd.OrderItems, function (idx2, soi) {
                                if (oi_id == soi.Id) {
                                    soi.LastFahuoPrintTime = '1'; //有值表示已打印
                                }
                            });
                        });
                    }
                }
                else if (printType == 3) {
                    row.LastNahuoPrintTime = 1;
                    row.NaHuoPrintedCount = kv.OrderItemIds.length;
                    for (var j = 0; j < kv.OrderItemIds.length; j++) {
                        var oi_id = kv.OrderItemIds[j];
                        $("#product-print-state-td-" + oi_id + " .product-nahuo-print-state").html("√");
                        //更新订单项的打印时间
                        common.Foreach(row.SubOrders, function (idx, subOrd) {
                            common.Foreach(subOrd.OrderItems, function (idx2, soi) {
                                if (oi_id == soi.Id) {
                                    soi.LastNahuoPrintTime = '1'; //有值表示已打印
                                }
                            });
                        });
                    }
                }
            }
            if (printType == 1)
                table.refreshColumn("PrintedSerialNumber");
            table.refreshColumn("PrintState");
        }
    }

    table.refreshColumns = function (cols) {
        if (cols && cols.length > 0) {
            $(cols).each(function (i, colName) {
                if (colName)
                    table.refreshColumn(colName);
            });
        }
        else {
            _render(table.target, table.rows);
            // 商品图片显示位置       
            orderPrintModule.RenderProductPic();
        }
    }

    //普通面单单号自动生成
    table._logisticNumGengerate_result = null;
    table.OpenLogisticNumGenerate = function () {
        event.stopPropagation();
        layer.close(orderTableBuilder._logisticNumGengerate_result);
        var $this = $("#sp_open_logistic_num_gener");
        var offset = $this.offset();
        var top = offset.top - $(document).scrollTop() + 20;
        var left = offset.left;
        orderTableBuilder._logisticNumGengerate_result = layer.open({
            type: 1,
            title: false,
            fixed: false,
            shade: false,
            //area: ['350px'],
            anim: -1,
            isOutAnim: false,
            closeBtn: 0,
            content: $("#div_logistic_generate_container"),
            shadeClose: true,
            offset: [top, left],
            btn: ['生成', '取消'],
            success: function () {
                if (tradionalWaybillCodeConfig) {
                    var template = addTmplInOrderListModule.GetCurrentTemplate();
                    var template_logisticCode_generate_config = tradionalWaybillCodeConfig[template.Id];
                    if (template_logisticCode_generate_config) {
                        $("#txtOrderTotal").val(template_logisticCode_generate_config.OrderTotalNumber);
                        $("#sel_logistic_generate").val(template_logisticCode_generate_config.AddOrSub);
                        $("#txtStartOrder").val(template_logisticCode_generate_config.StartWaybillCode);
                    }
                }

                $(".content").on("click", function () {
                    layer.close(orderTableBuilder._logisticNumGengerate_result);
                })

            },
            yes: function () {

                //var orderTotalCtrl = $("#txtOrderTotal");
                //var orderTotalMsg = $("#sp_order_total_msg");
                //var orderTotalVal = orderTotalCtrl.val();

                var addOrSubCtrl = $("#sel_logistic_generate");
                var addOrSubMsg = $("#sp_logistic_generate_msg");
                var addOrSubVal = addOrSubCtrl.val();

                var startOrderCtrl = $("#txtStartOrder");
                var startOrderMsg = $("#sp_start_order_msg");
                var startOrderVal = startOrderCtrl.val();

                //if (isNaN(orderTotalVal)) {
                //    orderTotalMsg.text('单号总数必须为数字！');
                //}
                //else {
                //    orderTotalMsg.text('');
                //}

                var orderStr = '';
                if (startOrderVal.length > 3) {
                    orderStr = startOrderVal.substring(startOrderVal.length - 4);
                }
                else {
                    orderStr = startOrderVal;
                }

                if (isNaN(orderStr)) {
                    startOrderMsg.text('起始单号后四位必须为数字！');
                }
                else {
                    startOrderMsg.text('');
                }

                ////更改配置对象
                //var template = addTmplInOrderListModule.GetCurrentTemplate();
                //tradionalWaybillCodeConfig[template.Id] = {
                //    StartWaybillCode: startOrderVal,
                //    AddOrSub: addOrSubVal,
                //    OrderTotalNumber: (orderTotalVal ? parseInt(orderTotalVal) : orderTotalVal)
                //};

                var start_num = parseInt(orderStr); //起始

                for (var i = 0; orderTableBuilder.rows && i < orderTableBuilder.rows.length; i++) {
                    var row = orderTableBuilder.rows[i];
                    if (!row.checked || !row.Id)
                        continue;
                    var input = $("#order-" + row.Id + " .LastWaybillCode_input");

                    var tempNum = startOrderVal.substring(0, startOrderVal.length - start_num.toString().length);
                    input.val(tempNum + '' + start_num);

                    if (addOrSubVal == 1)
                        start_num++;
                    else if (addOrSubVal == 2)
                        start_num--;
                }
                layer.close(orderTableBuilder._logisticNumGengerate_result);
            },
            btn2: function () { }
        });
    }

    table.changeTemplate = function (template) {
        if (!template)
            return;
        if (template.TemplateType == 1) {
            $('#div_logistic_generate_config').show();
        }
        else {
            $('#div_logistic_generate_config').hide();
        }
        var orders = [];
        for (var i = 0; table.rows && i < table.rows.length; i++) {
            var row = table.rows[i];
            var input = $("#order-" + row.Id + " .LastWaybillCode_input");
            //显示单号
            table.ShowWaybillCodeToInput(row, template, input);
            var val = $("#txtReceiver_address_" + row.Id).val();
            try {
                var address = addressSpliter.parse(val, false);
                orders.push({
                    Id: row.Id,
                    Province: address.province,
                    City: address.city,
                    Area: address.area,
                    Street: address.addr
                });

            } catch (e) {
            }
        }

        ////快运模板
        //if (template.TemplateType == 7 || template.TemplateType == 8) {
        //    //隐藏订单行上的操作中的 一单多包、新单号打印
        //    $('.moreOperations_aialog').find(".oneToMany,.newPrint").each(function (idx, sp) {
        //        $(sp).addClass("moreHide");
        //    });
        //    $(".express-print-onetomany-btn").hide();
        //}
        //else {
        //    $('.moreOperations_aialog').find(".oneToMany,.newPrint").each(function (idx, sp) {
        //        var $sp = $(sp);
        //        if (($sp.prevAll().length + $sp.nextAll().length) > 3)
        //            $sp.removeClass("moreHide");
        //        $(".express-print-onetomany-btn").show();
        //    });
        //}
        //检查是否可派送
        //common.ajax({
        //    url: "/Order/CheckReachable",
        //    data: { ExpressCompanyCode: template.ExpressCompanyCode, Orders: orders },
        //    success: function (rsp) {
        //        if (rsp && rsp.Success && rsp.Data) {
        //            $(rsp.Data).each(function (index, cur) {
        //                var text = "√";
        //                var color = "green";
        //                var title = "当前快递支持派送到该地区";
        //                if (!cur.IsReachable) {
        //                    text = "×";
        //                    color = "red";
        //                    title = "当前快递不支持派送到该地区，具体请咨询快递公司";
        //                }
        //                $("#is-express-send-span-" + cur.Id).html(text).css({ color: color }).attr("title", title);
        //            });
        //        }
        //    }
        //});

        var refreshSenderInfo = function (row) {
            var rowId = row.Id;
            var senderName = row.SenderName;
            var senderMobile = row.SenderMobile ? row.SenderMobile : row.SenderPhone;
            var senderAddress = row.SenderAddress;
            var platformOrderId = row.PlatformOrderId;
            var shopId = row.ShopId;
            var index = row.Index;
            return '<span>发件人姓名：</span><span style="font-weight:normal">' + senderName + '</span><span class="my-btn" onclick="modifyOrderInfoModule.UpdataOrderSeller(this,\'' + rowId + '\',\'' + platformOrderId + '\',\'' + shopId + '\',\'' + index + '\',\'' + senderName + '\',\'' + senderMobile + '\',\'' + senderAddress + '\')">修改</span>';
        }

        //根据模板绑定的发件人更新订单发件人
        if (template.SenderId > 0 && template.SellerName && template.SellerAddress && (template.SellerMobile || template.SellerPhone)) {


            $(table.rows).each(function (i, o) {

                if (o.IsWeiGong == true || o.IsAgent == true || o.IsManualUpdateSeller == true) {
                    return true;
                }

                o.UpdateSenderInfoByTemplate = true; //按模板修改过发件人信息

                //备份
                if (!o.OldSenderName)
                    o.OldSenderName = o.SenderName;
                if (!o.OldSenderPhone)
                    o.OldSenderPhone = o.SenderPhone;
                if (!o.OldSenderMobile)
                    o.OldSenderMobile = o.SenderMobile;
                if (!o.OldSenderAddress)
                    o.OldSenderAddress = o.SenderAddress;

                //赋值模板
                o.SenderName = template.SellerName;
                o.SenderPhone = template.SellerPhone;
                o.SenderMobile = template.SellerMobile;
                o.SenderAddress = template.SellerAddress;
                //刷新发件人信息
                var senderInfoHtml = refreshSenderInfo(o);
                $("#order-detail-" + o.Id + " .tdMoreShow_SenderAddress").html(senderInfoHtml);
            });

            //刷新发件人列
            table.refreshColumn("SenderName");
        }
        else {
            $(table.rows).each(function (i, o) {
                if (o.UpdateSenderInfoByTemplate == true) {
                    //赋值
                    o.SenderName = o.OldSenderName;
                    o.SenderPhone = o.OldSenderPhone;
                    o.SenderMobile = o.OldSenderMobile;
                    o.SenderAddress = o.OldSenderAddress;
                    //刷新发件人信息
                    var senderInfoHtml = refreshSenderInfo(o);
                    $("#order-detail-" + o.Id + " .tdMoreShow_SenderAddress").html(senderInfoHtml);
                }
            });
            //刷新发件人列
            table.refreshColumn("SenderName");
        }
    }
    table.ShowWaybillCodeToInput = function (row, template, input) {

        if (template && template.TemplateType == 1) {
            input.removeAttr("readonly").val("");
        } else {
            input.prop("readonly", true).val("打印后返回");
        }

        //获取订单列表的状态
        var isShowExpressName = false;
        var orderStatus = $('#orderList_orderState_choose').children('.active').attr('data-status');
        if ('waitbuyerreceive' == orderStatus || 'success' == orderStatus) {
            input.prop("disabled", "disabled");
            isShowExpressName = true;
        }
        else {
            input.removeAttr("disabled");
            isShowExpressName = false;
        }

        var hasWaybillCode = false;
        var waybillCodeCountByTemplate = 0; //模板打印的运单个数
        table.CheckCurrentTemplateHasWaybillCode(row, template, function (w) {
            if (hasWaybillCode == false) {
                input.val(w.WaybillCode);
                if (isShowExpressName == true) {
                    input.prevAll().remove().end().before('<label>' + template.ExpressCompanyName + '</label>').parent().css('textAlign', 'center');
                }
                else {
                    input.prevAll().remove();
                }
            }
            waybillCodeCountByTemplate++;
            hasWaybillCode = true;
        });
        if (hasWaybillCode == false)
            input.prevAll().remove();

        //显示多个运单号
        if (waybillCodeCountByTemplate > 1) {
            input.nextAll().remove().end().after('<div class="moreWaybillCode">\
                        <i title="该订单打印过多个电子面单">单号('+ waybillCodeCountByTemplate + ')</i>\
                        <span class="moreWaybillCode-show" onclick="modifyOrderInfoModule.WaybillCodePrintDetails.bind(this)(' + row.Index + ')">查看详情</span>\
                     </div>');
        }
        else {
            input.nextAll().remove();
        }
    }

    table.CheckCurrentTemplateHasWaybillCode = function (row, template, hasCallback) {
        if (!row) return;
        var ws = row.WaybillCodes;
        if (ws != null && ws !== undefined && ws.length > 0) {
            for (var j = 0; j < ws.length; j++) {
                var w = ws[j];
                if (w.TemplateId == template.Id || (w.TemplateType == template.TemplateType && w.CompanyCode == template.ExpressCompanyCode)) {
                    //以老系统为准，只要快递一致就可以
                    //if (w.CompanyCode == template.ExpressCompanyCode) {
                    hasCallback(w);
                }
            }
        }
    }

    //子订单勾选事件
    table.subOrderSeletedHandler = function (chk_all, orderId, parentId) {
        //alert(orderId);
        //1.界面响应
        var isChecked = $(chk_all).is(":checked");
        $(":checkbox[class='orderitem-chx'][data-parent-id='" + orderId + "']").each(function (index, chx) {
            chx.checked = isChecked;
        })
        //2.数据变更
        var orderRow = null;
        $(table.rows).each(function (index, row) {
            if (row.Id == parentId) {
                orderRow = row;
                $(row.SubOrders).each(function (i, subRow) {
                    if (subRow.Id == orderId) {

                        subRow.checked = isChecked;

                        $(subRow.OrderItems).each(function (ii, item) {
                            item.checked = isChecked;
                        });
                    }
                });
            }
        });
        //3.重新生成打印内容
        $("#txt_print_content_" + parentId).val(printContentFormatSetModule.ForatPrintContent(orderRow));
        //4.如果是自由打印，则触发保存
        if (common.isCustomerOrder()) {
            $("#txt_print_content_" + parentId).trigger('change');
        }
    }

    //产品勾选事件
    table.OrderProductItemSeletedHandler = function (chk, itemId, orderId, parentId) {
        //alert(orderId);
        //1.界面响应
        var isChecked = $(chk).is(":checked");
        //2.数据变更
        var orderRow = null;
        $(table.rows).each(function (index, row) {
            if (row.Id == parentId) {
                orderRow = row;
                $(row.SubOrders).each(function (i, subRow) {
                    var flag = true; //子订单全选状态
                    if (subRow.Id == orderId) {
                        $(subRow.OrderItems).each(function (ii, item) {
                            if (item.Id == itemId) {
                                item.checked = isChecked;
                            }
                            if (item.checked == false || item.checked == undefined) {
                                flag = false;
                            }
                        });
                        subRow.checked = flag;
                        $(".orderitem-chx-all-" + orderId).prop('checked', flag); //设置子订单权限复选框选中状态
                    }
                });
            }
        });
        //3.重新生成打印内容
        $("#txt_print_content_" + parentId).val(printContentFormatSetModule.ForatPrintContent(orderRow));
        //4.如果是自由打印，则触发保存
        if (common.isCustomerOrder()) {
            $("#txt_print_content_" + parentId).trigger('change');
        }
    }

    //添加订单到预发货
    table.AddPreordainOrder = function () {
        //逻辑：
        //1.将勾选的额订单添加到预发货表
        //2.将勾选订单标记为预发货,合并订单，子订单都需要标记
        //20190126变更，去掉了了预发货表，
        //保存单号操作：1.直接标记订单为预发货，
        //2.将单号和快递模板id存入lastwaybillCode 和 lastExpresstemplateId

        //校验是否勾选了模板
        var currentTemplate = addTmplInOrderListModule.GetCurrentTemplate();
        if (!currentTemplate) {
            layer.alert('请选择模板，发货需要快递信息！');
            return;
        }

        //获取所选订单的平台订单id，合并订单还要获取子订单平台订单id
        var addPreordainOrder = []; //需修改为预发货的订单
        var updateIsPreordainOrder = []; //需加入预发货表的订单
        var flag = false;
        common.Foreach(table.rows, function (i, o) {
            if (o.checked == true) {

                var waybillCode = $("#order-" + o.Id + " .LastWaybillCode_input").val().trim();
                if (waybillCode == "" || waybillCode == "打印后返回") {
                    flag = true;
                    return 'break';
                }

                if (o.ChildOrderId) {
                    updateIsPreordainOrder.push({
                        Id: o.Id,
                        PlatformOrderId: o.PlatformOrderId,
                        ShopId: o.ShopId,
                        WaybillCode: waybillCode,
                    });
                    //汇总子订单
                    common.Foreach(o.SubOrders, function (sub_i, sub_o) {

                        updateIsPreordainOrder.push({
                            Id: sub_o.Id,
                            PlatformOrderId: sub_o.PlatformOrderId,
                            ShopId: sub_o.ShopId,
                            WaybillCode: waybillCode,
                        });

                    });
                }
                else {
                    //如果是子订单，且被合并过，则汇集父订单
                    if (o.IsMergered == true) {
                        addPreordainOrder.push({
                            Id: o.id,
                            PlatformOrderId: o.MergeredOrderId,
                            ShopId: o.ShopId,
                            //WaybillCode: waybillCode,
                        });
                    }

                    updateIsPreordainOrder.push({
                        Id: o.Id,
                        PlatformOrderId: o.PlatformOrderId,
                        ShopId: o.ShopId,
                        WaybillCode: waybillCode,
                    });
                }
            }
        });

        if (flag == true) {
            layer.alert('订单中有未填写的快递单号！');
            return;
        }

        //提交 保存单号
        common.Ajax({
            url: '/Preordain/AddPreordainOrder',
            data: {
                model: {
                    AddPreordainOrder: addPreordainOrder,
                    UpdateIsPreordainOrder: updateIsPreordainOrder,
                    ExpressId: currentTemplate.ExpressCompanyId,
                }
            },
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                $("#search-btn").click(); //刷新表格
                layer.msg('保存单号成功.');
            }
        });
    }

    //加载需用户确认打印状态的数据
    table.LoadWaitConfirmPrintedOrderList = function (isOpenWin) {

        return;

        var _getOrderIdData = function (isGetConfirmOrderId) {
            //获取勾选的订单
            var chk_list = $(':checkbox[name="chk_wait_confirm_printstatu"]:checked');
            if (chk_list.length == 0) {
                layer.msg("请勾选订单");
                return false;
            }

            var dataList = {};

            chk_list.each(function (index, chk) {

                var orderId = chk.getAttribute('data-orderId');
                var printId = chk.getAttribute('data-printHistoryId');
                var shopId = chk.getAttribute('data-ShopId');
                var templateId = chk.getAttribute('data-templateId');
                var waybillCode = chk.getAttribute('data-epxressWaybillCode');

                //dataList[templateId]={
                //    TemplateId: ,
                //    Orders: [],
                //    PrintType: printType,
                //    PrintHistoryIds: data.PrintHistoryIds
                //};

                var temp = null;
                if (dataList[templateId] == undefined) {
                    temp = dataList[templateId] = {
                        Orders: [],
                        PrintHistoryIds: [],
                    };
                }
                else {
                    temp = dataList[templateId];
                }

                if (orderId.indexOf(',') == -1) {
                    temp.Orders.push({ PlatformOrderId: orderId, ShopId: shopId, WaybillCode: waybillCode });
                }
                else {
                    var orderIdList = orderId.split(',');
                    common.Foreach(orderIdList, function (i, o) {
                        temp.Orders.push({ PlatformOrderId: o, ShopId: shopId, WaybillCode: waybillCode });
                    });
                    //temp.Orders.push(orderId);
                }

                temp.PrintHistoryIds.push(printId);

            });

            var requestData = [];

            for (var key in dataList) {
                requestData.push({
                    TemplateId: key,
                    Orders: dataList[key].Orders,
                    PrintType: 1,
                    PrintHistoryIds: dataList[key].PrintHistoryIds,
                });
            }

            return requestData;
        }

        common.Ajax({
            url: '/PrintHistory/GetWaitConfirmPrintHistoryList',
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                if (rsp.Data.length <= 0) {
                    if (waitConfirmPrintstatusDialog)
                        layer.close(waitConfirmPrintstatusDialog);
                    return;
                }

                var html = "";
                common.Foreach(rsp.Data, function (i, o) {

                    var orderId = (o.PlatformOrderJoin == null || o.PlatformOrderJoin == "") ? o.PlatformOrderId : o.PlatformOrderId + ',' + o.PlatformOrderJoin;
                    var orderIdShow = (o.PlatformOrderJoin == null || o.PlatformOrderJoin == "") ? o.PlatformOrderId : o.PlatformOrderJoin;

                    html += "<tr>\
                                <td><input type='checkbox' name='chk_wait_confirm_printstatu' data-orderId='" + orderId + "' data-printHistoryId='" + o.ID + "' data-templateId='" + o.TemplateId + "' data-ShopId='" + o.ShopId + "' data-epxressWaybillCode='" + o.ExpressWaybillCode + "'/></td>\
                                <td>" + common.OrderIdFormatter(orderIdShow) + "</td>\
                                <td>" + ((o.BuyerMemberName == 'null' || !!o.BuyerMemberName == false) ? '' : o.BuyerMemberName) + "</td>\
                                <td>" + o.TemplateName + "</td>\
                                <td>" + o.PrintDate + "</td>\
                                <td>" + o.ExpressWaybillCode + "</td>\
                            </tr>";
                });
                $('#sp_wait_confirm_data_count').text(rsp.Data.length);
                $('#tbody_wait_confirm_printed').html(html);

                $('#chk_wait_confirm_printstatu_all').unbind('click').bind('click', function () {
                    var ischecked = $(this).is(":checked");
                    $('#tbody_wait_confirm_printed :checkbox[name="chk_wait_confirm_printstatu"]').prop("checked", ischecked);
                });

                $('#tbody_wait_confirm_printed :checkbox[name="chk_wait_confirm_printstatu"]').unbind('click').bind('click', function () {
                    var selector = '#tbody_wait_confirm_printed :checkbox[name="chk_wait_confirm_printstatu"]';
                    var ischecked = $(this).is(":checked");
                    if (ischecked == false) {
                        $('#chk_wait_confirm_printstatu_all').prop("checked", ischecked);
                    }
                    else if ($(selector + ':checked').length == $(selector).length) {
                        $('#chk_wait_confirm_printstatu_all').prop("checked", ischecked);
                    }
                });

                if (isOpenWin == false) {

                    return;
                }

                var waitConfirmPrintstatusDialog = layer.open({
                    type: 1,
                    title: '确认运单是否已打印',
                    area: ['800px', '500px'],
                    shadeClose: true, //点击遮罩关闭
                    content: $("#div_confirm_printed"),
                    success: function () {
                        $('#div_confirm_printed').parent().siblings('.layui-layer-btn').find('.layui-layer-btn1').css({ backgroundColor: '#e95e4f', borderColor: '#e95e4f', color: '#fff', borderRadius: '5px' });
                        $('#div_confirm_printed').parent().siblings('.layui-layer-btn').find('.layui-layer-btn2').css({ backgroundColor: '#ee9c33', borderColor: '#ee9c33', color: '#fff', borderRadius: '5px' });
                    },
                    btn: ['未打印', '已打印', '查看这些订单', '关闭'],
                    btnAlign: 'c',
                    yes: function () {
                        //未打印
                        var dataList = _getOrderIdData(true);
                        if (dataList == false) return false;

                        //提交数据
                        common.Ajax({
                            url: '/Order/ConfirmOrderPrintStatu',
                            loading: true,
                            data: { modelList: dataList, isPrinted: false },
                            success: function (rsp) {
                                if (rsp.Success == false) {
                                    layer.msg(rsp.Message, { icon: 2 });
                                    return;
                                }
                                table.LoadWaitConfirmPrintedOrderList(false);
                            }
                        });

                    },
                    btn2: function () {
                        //alert('已打印');

                        var dataList = _getOrderIdData(true);
                        if (dataList == false) return false;
                        //提交数据
                        common.Ajax({
                            url: '/Order/ConfirmOrderPrintStatu',
                            loading: true,
                            data: { modelList: dataList, isPrinted: true },
                            success: function (rsp) {
                                if (rsp.Success == false) {
                                    layer.msg(rsp.Message, { icon: 2 });
                                    return;
                                }
                                table.LoadWaitConfirmPrintedOrderList(false);
                            }
                        });
                        return false;
                    },
                    btn3: function () {
                        //alert('查看这些订单');
                        var dataList = _getOrderIdData(false);
                        if (dataList == false) return false;
                        var platformOrderIds = '';
                        common.Foreach(dataList, function (i, o) {
                            common.Foreach(o.Orders, function (ii, oo) {
                                if ((',' + platformOrderIds).indexOf((',' + oo.PlatformOrderId + ',')) == -1) {
                                    platformOrderIds += (oo.PlatformOrderId + ',');
                                }
                            });
                        });
                        $('.orderList_search input[name="PlatformOrderId"]').val(platformOrderIds.trimEndDgj(','));
                        $("#search-btn").click();
                        layer.close(waitConfirmPrintstatusDialog);
                    },
                    btn4: function () {
                        //alert('关闭');
                        layer.close(waitConfirmPrintstatusDialog);
                    }
                });
            }
        });
    }

    //检测拼多多用户是否有添加拼多多电子面单模板，或者当前登录用户开通了电子面单且没有添加模板
    table.CheckPddUserHasWaybillTemplate = function () {
        if (window.isShowingSyncTips)
            return;
        common.Ajax({
            url: '/Order/PddUserCheckWaybillUse',
            success: function (rsp) {

                if (common.IsError(rsp) == true || rsp.Data == null) {
                    return;
                }
                if (window.isShowingSyncTips)
                    return;
                //返回0 ，忽略
                if (rsp.Data == 0) {
                    return;
                }
                //返回1，提示开通电子面单
                if (rsp.Data == 1) {
                    var pddUserWaybillUseCheckDailog = layer.open({
                        type: 1,
                        title: '提醒',
                        content: '<div id="div_pddUserWaybillUseCheck" style="padding:10px;line-height:30px;font-size:14px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好，系统检测到您当前登录的店铺为拼多多店铺，由于拼多多官方要求打单发货需使用拼多多电子面单，否则可能会发货失败。<br/>请前往拼多多商家后台》发货管理》<a href="https://mms.pinduoduo.com/logistics/open" target="_blank">电子面单菜单</a>，申请开通相应电子面单。</div>',
                        btn: ['前往拼多多后台', '不再提醒', '关闭'],
                        area: ['500px'],
                        btnAlign: 'c',
                        success: function () {
                            $('#div_pddUserWaybillUseCheck').parent().siblings('.layui-layer-btn').find('.layui-layer-btn1').css({ backgroundColor: '#e95e4f', borderColor: '#e95e4f', color: '#fff', borderRadius: '5px' });
                            $('#div_pddUserWaybillUseCheck').parent().siblings('.layui-layer-btn').find('.layui-layer-btn2').css({ backgroundColor: '#ee9c33', borderColor: '#ee9c33', color: '#fff', borderRadius: '5px' });
                        },
                        yes: function () {
                            window.open("https://mms.pinduoduo.com/logistics/open");
                        },
                        btn2: function () {
                            common.SaveCommonSetting("PddUserWaybillUseChecked_01", true);
                            layer.close(pddUserWaybillUseCheckDailog);
                        },
                        btn3: function () {
                            layer.close(pddUserWaybillUseCheckDailog);
                        }
                    });
                    return;
                }

                var branchs = rsp.Data;
                var model = {
                    Message: "您好，系统检测到当前登录账号已开通拼多多电子面单，请确定是否添加相应快递模板。",
                    Branchs: branchs
                };

                var dialog = $.templates("#pddwaybill_check_dialog_tmpl");
                var html = dialog.render(model);

                var pddUserWaybillUseCheckDailog_02 = layer.open({
                    type: 1,
                    title: '拼多多电子面单检测提醒',
                    area: ['500px', '350px'],
                    //shadeClose: true, //点击遮罩关闭
                    content: html,
                    success: function () {
                        $('#div_pddwaybill_check').parent().siblings('.layui-layer-btn').find('.layui-layer-btn1').css({ backgroundColor: '#e95e4f', borderColor: '#e95e4f', color: '#fff', borderRadius: '5px' });
                        $('#div_pddwaybill_check').parent().siblings('.layui-layer-btn').find('.layui-layer-btn2').css({ backgroundColor: '#ee9c33', borderColor: '#ee9c33', color: '#fff', borderRadius: '5px' });

                        $('#chk_pddwaybill_check_all').unbind('click').bind('click', function () {
                            var ischecked = $(this).is(":checked");
                            $('#div_pddwaybill_check tbody :checkbox[name="chk_pddwaybill_check"]').prop("checked", ischecked);
                        });

                        $('#div_pddwaybill_check tbody :checkbox[name="chk_pddwaybill_check"]').unbind('click').bind('click', function () {
                            var selector = '#div_pddwaybill_check tbody :checkbox[name="chk_pddwaybill_check"]';
                            var ischecked = $(this).is(":checked");
                            if (ischecked == false) {
                                $('#chk_pddwaybill_check_all').prop("checked", ischecked);
                            }
                            else if ($(selector + ':checked').length == $(selector).length) {
                                $('#chk_pddwaybill_check_all').prop("checked", ischecked);
                            }
                        });

                    },
                    btn: ['添加勾选的模板', '不再提醒', '关闭'],
                    btnAlign: 'c',
                    yes: function () {
                        //添加模板
                        var dataList = [];
                        var chks = $('#div_pddwaybill_check tbody :checkbox[name="chk_pddwaybill_check"]:checked');
                        if (chks.length == 0) {
                            layer.msg("请勾选需要添加的模板");
                            return false;
                        }
                        chks.each(function (index, chk) {
                            dataList.push(chk.value);
                        });

                        //提交数据
                        common.Ajax({
                            url: '/Order/AddTemplates',
                            loading: true,
                            data: { branchHashCodeList: dataList },
                            success: function (rsp) {
                                if (rsp.Success == false) {
                                    layer.msg(rsp.Message, { icon: 2 });
                                    return;
                                }
                                layer.close(pddUserWaybillUseCheckDailog_02);
                                //重新初始化常用模板
                                addTmplInOrderListModule.Initialize(templateSelectedCallback);
                            }
                        });
                    },
                    btn2: function () {
                        //不再提醒
                        common.SaveCommonSetting("PddUserWaybillUseChecked_02", true);
                        layer.close(pddUserWaybillUseCheckDailog_02);
                    },
                    btn3: function () {
                        //alert('关闭');
                        layer.close(pddUserWaybillUseCheckDailog_02);
                    }
                });
            }
        });
    }

    //渲染指定的列内容
    table.rederField = function (row, field, index, ext) {
        var column = null;
        for (var i = 0; i < table.columns.length; i++) {
            var col = table.columns[i];
            if (col.field == field) {
                column = col;
                break;
            }
        }
        var html = defaultContentFormatter(row, column, index, ext);
        return html;
    }

    // 订单时间显示指定列的值
    table.changeOrderTime = function (showField, showName, needRefresh) {
        for (var i = 0; i < table.columns.length; i++) {
            if (table.columns[i].field == "OrderTime") {
                table.columns[i].aliasField = showField;
                table.columns[i].aliasName = showName;
                break;
            }
        }

        //table.columns = columns;
        if (needRefresh) {
            table.refreshColumn("OrderTime");
        }
    }

    table.SyncOrder = function (index) {
        var row = table.rows[index];
        var request = { "PlatformOrderId": row.PlatformOrderId, "ChildOrderId": row.ChildOrderId, "ShopId": row.ShopId };
        common.ajax({
            url: "/Order/SyncSingleOrder",
            data: request,
            loadingMessage: "同步中...",
            success: function (rsp) {
                if (rsp.Success) {
                    layer.msg("同步成功");
                    table.getSingleOrder(row.PlatformOrderId, row.ShopId, function (rows) {
                        if (rows.length > 0) {
                            var newRow = rows[0];
                            newRow.Index = index;
                            newRow.WaybillCodes = row.WaybillCodes;
                            table.refreshRow(newRow);
                            table.changeTemplate(addTmplInOrderListModule.GetCurrentTemplate());
                        }
                    });
                }
                else
                    layer.msg("同步失败：" + rsp.Message);
            }
        });
    }

    table.getSingleOrder = function (pids, sids, callback) {
        common.Ajax({
            url: "/Order/List",
            data: {
                requestUrl: location.href.split('?')[0],
                PageSize: 100,
                Filters: [
                    { TableAlias: "o", FieldType: "string", TableName: "P_Order", Name: "ShopId", Value: sids, Contract: "=" },
                    { TableAlias: "o", FieldType: "string", TableName: "P_Order", Name: "PlatformOrderId", Value: pids, Contract: "in" },
                ]
            },
            success: function (data) {
                if (data.Success) {
                    var rows = data.Data.Rows;
                    callback(rows);
                }
                else
                    callback([]);
            }
        });

    }

    // 检测头条是否整单发货
    table.checkTouTiaoOrders = function () {
        var orders = table.getSelections();
        var isAllCheck = true;
        $(orders).each(function (i, o) {
            // 选中订单的订单项Id集合
            var oiids = o.OrderItems;
            $(table.rows).each(function (i1, row) {
                if (row.PlatformOrderId == o.PlatformOrderId) {
                    $(row.SubOrders).each(function (i2, srow) {
                        $(srow.OrderItems).each(function (i3, oi) {
                            // 排除退款成功的订单项后，检查是否选中全部订单商品
                            if (srow.RefundStatus != "REFUND_SUCCESS" && oiids.indexOf(oi.Id + "") == -1) {
                                isAllCheck = false;
                                return;
                            }
                        });
                        if (!isAllCheck)
                            return;
                    });
                }
                if (!isAllCheck)
                    return;
            });
            if (!isAllCheck)
                return;
        });
        return isAllCheck;
    }

    //检查订单退款状态的请求
    var _CheckReturnOrderUrl = "";
    table.SetCheckReturnOrdersUrl = function (url) {
        _CheckReturnOrderUrl = url;
    }

    //设置是否显示loding
    table.SetCheckReturnOrdersRequestShowLoading = function (isShowLoading) {
        _IsCheckReturnOrdersRequestShowLoading = isShowLoading;
    }
    var _IsCheckReturnOrdersRequestShowLoading = true;
    var _GetCheckReturnOrdersRequestShowLoadingMsg = function () {
        if (!_IsCheckReturnOrdersRequestShowLoading)
            return null;
        return "检查订单退款状态";
    }

    table.checkReturnOrders = function (orders, isSync) {
        if (!orders || orders.length == 0) return;

        var options = {};
        options.Filters = [];
        var platformOrderIds = "";
        var shopIds = "";
        for (var i = 0; i < orders.length; i++) {
            platformOrderIds += orders[i].PlatformOrderId + ",";
            shopIds += orders[i].ShopId + ",";
        }
        platformOrderIds = platformOrderIds.trimEndDgj(",");
        shopIds = shopIds.trimEndDgj(",");

        var orderTbName = commonModule.isCustomerOrder() ? "P_CustomerOrder" : "P_Order";
        var orderItemTbName = commonModule.isCustomerOrder() ? "P_CustomerOrderItem" : "P_OrderItem";
        var orderStatus = $("#orderList_orderState_choose span[data-status].active").attr("data-status");
        if (orderStatus != "orderList_refund")
            options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: orderTbName, Name: "PlatformStatus", Value: orderStatus, Contract: "=" });

        options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: orderTbName, Name: "ShopId", Value: shopIds, Contract: "in" });
        options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: orderTbName, Name: "PlatformOrderId", Value: platformOrderIds, Contract: "in", CustomQuery: "PlatformOrderIdSearch" });
        options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: orderItemTbName, Name: "RefundStatus", Value: "", Contract: "!=", CustomQuery: "ReturnOrder" });

        return common.Ajax({
            url: _CheckReturnOrderUrl || '/Order/GetOrders',
            data: options,
            loadingMessage: _GetCheckReturnOrdersRequestShowLoadingMsg(),//'检查订单退款状态',
            type: 'POST',
            async: isSync || false,
            success: function (rsp) {
                if (rsp.Success) {
                    // 为了不影响其他页面已修改的值，此处只更新退款状态
                    var newOrders = rsp.Data.Rows || [];
                    for (var i = 0; i < newOrders.length; i++) {
                        for (var i1 = 0; i1 < table.rows.length; i1++) {
                            if (table.rows[i1].PlatformOrderId == newOrders[i].PlatformOrderId) {
                                var index = table.rows[i1].Index;
                                table.rows[i1].RefundStatus = newOrders[i].RefundStatus;
                                var subOrders = newOrders[i].SubOrders;
                                var otbSubOrders = table.rows[i1].SubOrders;
                                for (var i2 = 0; i2 < subOrders.length; i2++) {
                                    for (var i3 = 0; i3 < table.rows[i1].SubOrders.length; i3++) {
                                        if (otbSubOrders[i3].PlatformOrderId == subOrders[i2].PlatformOrderId) {
                                            var orderItems = subOrders[i2].OrderItems;
                                            var otbOrderItems = otbSubOrders[i3].OrderItems;
                                            for (var i4 = 0; i4 < orderItems.length; i4++) {
                                                for (var i5 = 0; i5 < otbOrderItems.length; i5++) {
                                                    if (orderItems[i4].Id == otbOrderItems[i5].Id) {
                                                        table.rows[i1].SubOrders[i3].OrderItems[i5].RefundStatus = orderItems[i4].RefundStatus;
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                //table.refreshRow(table.rows[i1]);
                                var refreshFields = ["ReceiveAddress", "ToFullAddress", "Product"];
                                table.refreshColumns(refreshFields);
                                break;
                            }
                        }
                    }
                }
            }
        });
    }

    table.confirmOrder = function (platformOrderId) {
        var pids = [];
        if (platformOrderId)
            pids.push(platformOrderId);
        else {
            var orders = table.getSelections();
            for (var i = 0; i < orders.length; i++) {
                pids.push(orders[i].PlatformOrderId);
            }
        }
        // 分批确认订单       
        var pgSize = 50;
        var groupIds = [];
        var tmpIds = [];
        var count = pids.length;
        for (var i = 0; i < count; i++) {
            if (i % pgSize == 0 && tmpIds.length > 0) {
                groupIds.push(tmpIds);
                tmpIds = [];
            }
            tmpIds.push(pids[i]);
        }
        if (tmpIds.length > 0)
            groupIds.push(tmpIds);

        var needSuccessCount = groupIds.length;
        var execedCount = 0;
        var falidCount = 0;
        $(groupIds).each(function (i, ids) {
            common.Ajax({
                url: '/Order/ConfirmOrder',
                data: { "platformOrderIds": JSON.stringify(ids) },
                type: 'POST',
                async: false,
                success: function (rsp) {
                    execedCount++;
                    if (!rsp.Success)
                        falidCount++;
                }
            });
        });

        var intervalIndex = setInterval(function () {
            if (execedCount == needSuccessCount) {
                var msg = falidCount == needSuccessCount ? "订单" : "部分订单";
                if (falidCount > 0)
                    layer.msg(msg + '确认失败，请重试<hr style="background-color: #FFB800!important;height: 1px;margin: 10px 0;border: 0;clear: both;"><span>' + msg + '已确认，正在刷新列表...</span>', { icon: 2, time: 2000 });
                else
                    layer.msg("订单已确认，正在刷新列表...", { icon: 1, time: 2000 });

                setTimeout(function () {
                    $("#search-btn").click();
                }, 3000);
                clearInterval(intervalIndex);
            }
        }, 500);
    }


    table.zkSort = function (isThis) {
        if (event.stopPropagation) {
            event.stopPropagation();
            event.preventDefault();
        } else {
            window.event.returnValue = false;
            window.event.cancelBubble = true;
        };
        if ($(isThis).hasClass("zk")) {
            $(isThis).removeClass("zk");
            $("#sortWrap_main").animate({
                top: '30px',
                opacity: '0'
            }, function () {
                $("#sortWrap_main").hide();
            });
        } else {
            $(isThis).addClass("zk");
            $("#sortWrap_main").css({
                display: "block"
            }).animate({
                top: '20px',
                opacity: '1'
            });
        }
    }
    $(document).on("click", function () {
        $("#sortWrap_main").animate({
            top: '50px',
            opacity: '0'
        }, function () {
            $("#sortWrap_main").hide();
            $("#promiseHearderTh_icon").removeClass("zk");
        });
        $(".mergeWrap").hide();
    })
    return table;
}(orderTableBuilder || {}, printContentFormatSetModule, commonModule, jQuery));