/// <reference path="../jquery-1.10.2.min.js" />
/// <reference path="../layer/layer.js" />
/// <reference path="KeywordFilterRepalceModule.js" />
/// <reference path="OrderTableBuilder.js" />
/// <reference path="../CommonModule.js" />

var printContentFormatSetModule = (function (module, keywordFilterReplaceModule, common, $, layer) {

    var _settingDialog = null;
    var _settingKey = 'PrintContent';

    var platformType = (commonModule.PlatformType || "").toLowerCase();


    //界面数据
    var _settings = null;
    //    {
    //    PrintContentSet: [
    //        { Name: '序号', Value: 'SerialNumber', Checked: true, Sort: 0 },
    //        { Name: '简称标题', Value: 'Title', Checked: true, Sort: 1 },
    //        { Name: '货号', Value: 'UPC', Checked: false, Sort: 2 },
    //        { Name: '规格颜色', Value: 'Color', Checked: true, Sort: 3 },
    //        { Name: '规格尺码', Value: 'Size', Checked: false, Sort: 4 },
    //        { Name: '单价', Value: 'Price', Checked: false, Sort: 5 },
    //        { Name: '数量', Value: 'Qty', Checked: false, Sort: 6 }
    //    ],
    //    TitleSet: 'NoShortPrintTitle',
    //    UpcSet: 'NoProductUpcPirntGoodsUpc',
    //    MergeGoodsSet: false,
    //    LinePrintNum: 1,
    //    QtyStryle: '【{0}】',
    //    QtyUnit: '个',
    //};

    var _repeatSettings = function () {
        //生成打印内容和位置
        _repeatPrintContent();

        //input:radio[name=sex][value=1]").attr("checked",true); 
        $("input:radio[name='rdo_TitleSet'][value='" + _settings.TitleSet + "']").prop("checked", true);
        $("input:radio[name='rdo_TitleSet']").unbind('click').bind('click', _titleSetClickHandler);

        if (platformType == 'kuaishou') {
            $("input:radio[name='rdo_UpcSet']").parent().parent().parent().hide();
        }
        else {
            $("input:radio[name='rdo_UpcSet'][value='" + _settings.UpcSet + "']").prop("checked", true);
            $("input:radio[name='rdo_UpcSet']").unbind('click').bind('click', _upcSetClickHandler);
        }

        $("#chk_MergeGoodsSet").unbind('click').bind('click', _mergerGoodsSetClickHandler);
        if (_settings.MergeGoodsSet == true) {
            $("#chk_MergeGoodsSet").prop("checked", 'checked')
        }

        $("input:radio[name='rdo_paging'][value='" + _settings.PrintPaging + "']").prop("checked", true);
        if (_settings.PrintPaging == "Paging") {
            $("#txt_paging_product_num").val(_settings.PagingProductNum);
        }
        $("input:radio[name='rdo_paging']").unbind('click').bind('click', _rdo_pagingClickHandler);

        if (_settings.Separator == "space" || _settings.Separator == undefined) {
            $("#rdo_separator_01").prop("checked", 'checked');
        } else {
            $("#rdo_separator_02").prop("checked", 'checked');
        }
        $("input:radio[name='rdo_separator']").unbind('click').bind('click', _rdo_separatorClickHandler);

        $("#sel_LinePrintNum").val(_settings.LinePrintNum);
        $("#sel_LinePrintNum").unbind('change').bind('change', _linePrintNumSetClickHandler);

        $("input:radio[name='rdo_QtyStryle'][value='" + _settings.QtyStryle + "']").prop("checked", 'checked');
        $("input:radio[name='rdo_QtyStryle']").unbind('click').bind('click', _qtyStyleSetClickHandler);


        $("input:radio[name='rdo_QtyUnit']").unbind('click').bind('click', _qtyUnitSetClickHandler);

        if ($("input:radio[name='rdo_QtyUnit'][value='" + _settings.QtyUnit + "']").length == 0) {
            $("input:radio[name='rdo_QtyUnit']").last().attr("checked", 'checked');
            $("#txtCustomUnit").val(_settings.QtyUnit);
        }
        else {
            $("input:radio[name='rdo_QtyUnit'][value='" + _settings.QtyUnit + "']").attr("checked", 'checked');
        }
    }

    var _repeatPrintContent = function () {
        var list = common.Sort(_settings.PrintContentSet, 'Sort');
        var ul_print_content = '';

        common.Foreach(list, function (i, o) {
            if (platformType == "kuaishou") {
                if (o.Value == "Color") {
                    o.Name = "商品规格";
                }
                else if (o.Value == "Size") {
                    return;
                }
                else if (o.Value == "UPC") {
                    o.Name = "SKU编码";
                }
            }
            var checked = '';
            if (o.Checked) {
                checked = 'checked="checked"';
            }
            ul_print_content += '<li><div><input type="checkbox" ' + checked + ' value="' + o.Value + '" sort="' + o.Sort + '" name="chk_print_content_item" id="chk_print_con_' + o.Value + '"><label for="chk_print_con_' + o.Value + '">'
                + o.Name + '</label></div><span><i class="icon_left" title="前移" onclick="printContentFormatSetModule.Move_Left(\'' + o.Value + '\')"></i><i class="icon_right" title="后移" onclick="printContentFormatSetModule.Move_Right(\'' + o.Value + '\')"></i></span></li>';
        });

        $("#ul_print_content").html(ul_print_content);

        $("input:checkbox[name='chk_print_content_item']").unbind('click').bind('click', _hoosePrintContentClickHandler)

    }

    var _titleSetClickHandler = function () {
        var v = $(this).val();
        _settings.TitleSet = v;

    }

    var _upcSetClickHandler = function () {
        var v = $(this).val();
        _settings.UpcSet = v;

    }

    var _mergerGoodsSetClickHandler = function () {
        var v = $(this).is(":checked");
        _settings.MergeGoodsSet = v;
    }

    var _rdo_pagingClickHandler = function () {
        var v = $(this).val();
        _settings.PrintPaging = v;
    }

    var _rdo_separatorClickHandler = function () {
        var v = $(this).val();
        _settings.Separator = v;
    }

    var _linePrintNumSetClickHandler = function () {
        var v = $(this).val();
        _settings.LinePrintNum = v;
    }

    var _qtyStyleSetClickHandler = function () {
        var v = $(this).val();
        _settings.QtyStryle = v;
    }

    var _qtyUnitSetClickHandler = function () {
        var v = $(this).val();
        if (v == '自定义') {
            _settings.QtyUnit = $('#txtCustomUnit').val().trim();
        }
        else {
            _settings.QtyUnit = v;
        }
    }

    var _hoosePrintContentClickHandler = function () {
        var v = $(this).val();
        var checked = $(this).is(":checked")
        common.Foreach(_settings.PrintContentSet, function (i, o) {
            if (o.Value == v) {
                o.Checked = checked;
                return 'break';
            }
        });
        _repeatPrintContent();
    }

    module.Move_Left = function (v) {
        var pre_obj = null;
        common.Foreach(common.Sort(_settings.PrintContentSet, 'Sort'), function (i, o) {
            if (o.Value == v && o.Sort > 0) {
                o.Sort--;
                pre_obj.Sort++;
                return 'break';
            }
            pre_obj = o;
        });

        _repeatPrintContent();
    }

    module.Move_Right = function (v) {
        var next_obj = null;
        common.Foreach(common.Sort(_settings.PrintContentSet, 'Sort', true), function (i, o) {
            if (o.Value == v && o.Sort < _settings.PrintContentSet.length - 1) {
                o.Sort++;
                next_obj.Sort--;
                return 'break';
            }
            next_obj = o;
        });
        _repeatPrintContent();
    }

    //加载配置
    module.LoadPrintContentSet = function (isOpenWin, async, isRealLoadSetting) {
        common.LoadCommonSetting(_settingKey, async, function (rsp) {
            if (rsp.Success == false) {
                layer.msg(rsp.Message, { icon: 2 });
                return;
            }

            if (rsp.Data != null) {
                _settings = eval('(' + rsp.Data + ')');
            }
            if (isRealLoadSetting == true) {
                return;
            }

            if (isOpenWin == false) {
                ////加载完配置，格式化所有列表数据
                //common.Foreach(orderTableBuilder.rows, function (i, o) {
                //    $('#txt_print_content_' + o.Id).val(module.ForatPrintContent(o));
                //});
                return;
            }

            _repeatSettings();

            _settingDialog = layer.open({
                type: 1,
                title: '发货内容打印界面设置',
                area: ['600px', '470px'],
                //shadeClose: true, //点击遮罩关闭
                content: $("#div_print_content"),
                btn: ['保存', '取消'],
                yes: function () {
                    module.SaveSetting();
                },
                btn2: function () {
                    layer.close(_settingDialog);
                }
            });

        });
    }

    module.SaveSetting = function () {

        if (_settings.QtyUnit.trim() == '') {
            if ($("input:radio[name='rdo_QtyUnit'][value='自定义']").is(':checked')) {
                _settings.QtyUnit = $('#txtCustomUnit').val().trim();
            }
        }

        if (_settings.QtyUnit.trim() == '') {
            layer.alert('请输入自定义单位');
            return;
        }

        _settings.PrintPaging = $("input:radio[name='rdo_paging']:checked").val();  //分页
        _settings.Separator = $("input:radio[name='rdo_separator']:checked").val(); //分隔符

        if (_settings.PrintPaging == "Paging") {
            var paging_product_num = $("#txt_paging_product_num").val();
            if ($.trim(paging_product_num) == '') {
                layer.alert('请输入分页打印的产品数量。');
                $("#txt_paging_product_num").focus();
                return;
            }
            if (isNaN($.trim(paging_product_num)) == true) {
                layer.alert('分页产品数量必须为数字。');
                $("#txt_paging_product_num").focus();
                return;
            }
            _settings.PagingProductNum = paging_product_num;
        }

        common.SaveCommonSetting(_settingKey, JSON.stringify(_settings), function (rsp) {

            if (rsp.Success == false) {
                layer.msg(rsp.Message, { icon: 2 });
                return;
            }
            layer.msg('保存成功');
            layer.close(_settingDialog);

            if (window.orderTableBuilder && orderTableBuilder.rows && orderTableBuilder.rows.length > 0) {
                common.Foreach(orderTableBuilder.rows, function (i, o) {
                    $('#txt_print_content_' + o.Id).val(module.ForatPrintContent(o));
                });
            }
        });
    }

    module.OpenSettingWindow = function () {

        module.LoadPrintContentSet(true);
    }

    module.ForatPrintContent = function (orderItems) {

        var sortFunc = function (ois) {
            //根据设置排序
            if (!_settings["PrintContentSort"])
                _settings["PrintContentSort"] = 0; //0：不排序，按后台商品顺序生成

            switch (_settings.PrintContentSort.toString()) {
                case "1": //按商家编码排序
                    ois = common.SortExt(ois, "CargoNumber", false, true);
                    break;
                case "2"://按商品简称
                    ois = common.SortExt(ois, "ShortTitle", false, true); //先按简称排序
                    break;
                case "3"://按标题排序
                    ois = common.SortExt(ois, "ProductSubject", false, true); //再按标题排序
                    break;
                case "4"://按规格商家编码（sku编码）排序
                    ois = common.SortExt(ois, "productCargoNumber", false, true);
                    break;
            }
            return ois;
        }

        if (orderItems == null || orderItems == undefined
            || orderItems.SubOrders == null || orderItems.SubOrders == undefined || Array.isArray(orderItems.SubOrders) == false) {
            return '暂无打印数据';
        }

        var text = "";

        //生成打印内容数据
        var printContent = {};
        var index = 0;

        //配置没有加载完成，则同步加载一次
        if (_settings == null) {
            module.LoadPrintContentSet(false, false, true);
        }
        //合并产品打印
        if (_settings.MergeGoodsSet) {
            //合并的样式=》商品简称标题:货号,颜色,尺码,数量；货号,颜色,尺码,数量；货号,颜色,尺码,数量;
            common.Foreach(orderItems.SubOrders, function (i, o) {
                var ois = o.OrderItems;
                ois = sortFunc(ois);
                common.Foreach(ois, function (x, y) {
                    if (y.checked == false || y.checked == undefined) {
                        return 'continue';
                    }
                    var isExists = true;
                    if (printContent['A' + y.ProductID] == undefined) {
                        isExists = false;
                        index++;
                        printContent['A' + y.ProductID] = _generalPrintContentV1(index, y, isExists, true);
                    }
                    else {
                        printContent['A' + y.ProductID] = printContent['A' + y.ProductID] + _generalPrintContentV1(index, y, isExists, true);
                    }
                });
            });

        }
        else {
            //不合并的样式=》简称标题,货号,规格颜色,数量;
            common.Foreach(orderItems.SubOrders, function (i, o) {
                var ois = o.OrderItems;
                ois = sortFunc(ois);
                common.Foreach(ois, function (x, y) {
                    if (y.checked == false || y.checked == undefined) {
                        return 'continue';
                    }
                    index++;
                    printContent['A' + o.Id + '_' + y.Id] = _generalPrintContentV1(index, y, false, false);
                });
            });
        }

        if (index <= 0) {
            return text;
        }

        var idx = 1;
        var temp = 0;
        for (var i in printContent) {
            temp++;
            text += printContent[i];
            if (temp == _settings.LinePrintNum || idx == index) {
                text += '\n';
                temp = 0;
            }
            idx++;
        }
        if (keywordFilterReplaceModule != null)
            return keywordFilterReplaceModule.FilterAndReplace(text);
        return text;
    }

    var _generalPrintContent = function (index, item, isExists, isMerge) {

        var joinStr = function (a, b) {
            var str = '';
            a = a == null ? '' : a.replace(/ /g, '');
            b = b == null ? '' : b.replace(/ /g, '');
            if (a != '' && a.toLowerCase() != 'null') {
                str += a;
            }
            if (b != '' && b.toLowerCase() != 'null') {
                if (str != '') {
                    str += ("(" + b + ")");
                }
                else {
                    str += b;
                }
            }
            if (str != '')
                str += ',';
            return str;
        }

        var temp = '';
        common.Foreach(common.Sort(_settings.PrintContentSet, 'Sort'), function (m, n) {
            if (n.Checked) {
                switch (n.Value) {
                    case 'SerialNumber':
                        if (isExists == false) {
                            temp += '(' + (index) + ')'
                        }
                        break;
                    case 'Title':
                        if (isExists == false) {
                            switch (_settings.TitleSet) {
                                case 'NoShortPrintTitle':
                                    if (!item.ShortTitle) {
                                        temp += item.ProductSubject ? (item.ProductSubject + (isMerge ? ':' : ',')) : '';
                                    }
                                    else {
                                        temp += item.ShortTitle ? (item.ShortTitle + (isMerge ? ':' : ',')) : '';
                                    }
                                    break;
                                case 'PrintTitle':
                                    temp += item.ProductSubject ? (item.ProductSubject + (isMerge ? ':' : ',')) : '';
                                    break;
                                case 'PrintShort':
                                    temp += item.ShortTitle ? (item.ShortTitle + (isMerge ? ':' : ',')) : '';
                                    break;
                                case 'NoTitlePrintShort':
                                    if (!item.ProductSubject) {
                                        temp += item.ShortTitle ? (item.ShortTitle + (isMerge ? ':' : ',')) : '';
                                    }
                                    else {
                                        temp += item.ProductSubject ? (item.ProductSubject + (isMerge ? ':' : ',')) : '';
                                    }
                                    break;
                                case 'PrintTitleAndShort':
                                    var shortStr = item.ShortTitle ? ('(' + item.ShortTitle + ')') : '';
                                    var str = item.ProductSubject + shortStr
                                    temp += str ? (str + (isMerge ? ':' : ',')) : '';
                                    break;
                            }
                        }
                        break;
                    case 'UPC':
                        //单品货号:CargoNumber，商品货号:productCargoNumber
                        switch (_settings.UpcSet) {
                            case 'NoProductUpcPirntGoodsUpc': //无单品货号打印商品货号
                                if (item.CargoNumber == null || item.CargoNumber == undefined || $.trim(item.CargoNumber) == '') {
                                    temp += item.productCargoNumber ? (item.productCargoNumber + ",") : '';
                                }
                                else {
                                    temp += item.CargoNumber ? (item.CargoNumber + ",") : '';
                                }
                                break;
                            case 'PrintProductUpc': //单品货号
                                temp += item.CargoNumber ? (item.CargoNumber + ",") : '';
                                break;
                            case 'PrintGoodsUpc': //商品货号
                                temp += item.productCargoNumber ? (item.productCargoNumber + ",") : '';
                                break;
                            case 'PrintProductUpc_GoodsUpc': //单品货号+商品货号
                                temp += joinStr(item.CargoNumber, item.productCargoNumber);
                                break;
                            case 'PrintGoodsUpc_ProductUpc': //商品货号+单品货号
                                temp += joinStr(item.productCargoNumber, item.CargoNumber);
                                break;
                        }

                        break;
                    case 'ProductID':
                        //产品ID
                        if (item.ProductID)
                            temp += item.ProductID + ','; //产品id

                        break;
                    case 'Color':
                        if (item.Color)
                            temp += item.Color + ','; //颜色第一个属性
                        if (commonModule.PlatformType != "Alibaba" && commonModule.PlatformType != "AlibabaC2M" && commonModule.PlatformType != "TaobaoMaiCaiV2") {
                            if (item.ExtAttr1)
                                temp += item.ExtAttr1 + ','; //扩展属性
                            if (item.ExtAttr2)
                                temp += item.ExtAttr2 + ','; //扩展属性
                            if (item.ExtAttr3)
                                temp += item.ExtAttr3 + ','; //扩展属性
                        }
                        break;
                    case 'Size':
                        if (item.Size)
                            temp += item.Size + ','; //尺码第二个属性
                        break;
                    case 'ProductAttr': //商品规格，有赞平台商品属性不能区分颜色、尺码
                        if (item.ProductAttr)
                            temp += item.ProductAttr + ','; //尺码第二个属性
                        break;
                    case 'Price':
                        temp += item.Price ? (item.Price + ',') : '';
                        break;
                    case 'Qty':
                        var qtyFormat = _settings.QtyStryle;
                        var qtyStr = qtyFormat.replace('{0}', item.Count);
                        temp += qtyStr;

                        if (_settings.QtyUnit == 'product_unit') {
                            temp += item.Unit ? (item.Unit + ',') : '';
                        }
                        else if (_settings.QtyUnit != '无单位') {
                            temp += _settings.QtyUnit ? (_settings.QtyUnit + ',') : '';
                        }
                        break;
                }
            }
        });

        temp = temp.trimEndDgj(',').trimEndDgj(':');
        temp = temp.replace(/null/g, '');
        temp += ";";

        return temp;
    }

    var _generalPrintContentV1 = function (index, item, isExists, isMerge) {

        var joinStr = function (a, b) {
            var str = '';
            a = a == null ? '' : a.replace(/ /g, '');
            b = b == null ? '' : b.replace(/ /g, '');
            if (a != '' && a.toLowerCase() != 'null') {
                str += a;
            }
            if (b != '' && b.toLowerCase() != 'null') {
                if (str != '') {
                    str += ("(" + b + ")");
                }
                else {
                    str += b;
                }
            }
            if (str != '')
                str += ',';
            return str;
        }

        var temp = '';
        //商品信息分隔符 comma空格 space逗号 
        var _sepa = (_settings.Separator == "space" || _settings.Separator == undefined) ? " " : ",";
        common.Foreach(common.Sort(_settings.PrintContentSet, 'Sort'), function (m, n) {
            if (n.Checked) {
                switch (n.Value) {
                    case 'SerialNumber':
                        if (isExists == false) {
                            temp += '(' + (index) + ')'
                        }
                        break;
                    case 'Title':
                        if (isExists == false) {
                            switch (_settings.TitleSet) {
                                case 'NoShortPrintTitle':
                                    if (!item.ShortTitle) {
                                        temp += item.ProductSubject ? (item.ProductSubject + (isMerge ? ':' : _sepa)) : '';
                                    }
                                    else {
                                        temp += item.ShortTitle ? (item.ShortTitle + (isMerge ? ':' : _sepa)) : '';
                                    }
                                    break;
                                case 'PrintTitle':
                                    temp += item.ProductSubject ? (item.ProductSubject + (isMerge ? ':' : _sepa)) : '';
                                    break;
                                case 'PrintShort':
                                    temp += item.ShortTitle ? (item.ShortTitle + (isMerge ? ':' : _sepa)) : '';
                                    break;
                                case 'NoTitlePrintShort':
                                    if (!item.ProductSubject) {
                                        temp += item.ShortTitle ? (item.ShortTitle + (isMerge ? ':' : _sepa)) : '';
                                    }
                                    else {
                                        temp += item.ProductSubject ? (item.ProductSubject + (isMerge ? ':' : _sepa)) : '';
                                    }
                                    break;
                                case 'PrintTitleAndShort':
                                    var shortStr = item.ShortTitle ? ('(' + item.ShortTitle + ')') : '';
                                    var str = item.ProductSubject + shortStr
                                    temp += str ? (str + (isMerge ? ':' : _sepa)) : '';
                                    break;
                            }
                        }
                        break;
                    case 'UPC':
                        //单品货号:CargoNumber，商品货号:productCargoNumber
                        switch (_settings.UpcSet) {
                            case 'NoProductUpcPirntGoodsUpc': //无单品货号打印商品货号
                                if (item.CargoNumber == null || item.CargoNumber == undefined || $.trim(item.CargoNumber) == '') {
                                    temp += item.productCargoNumber ? (item.productCargoNumber + _sepa) : '';
                                }
                                else {
                                    temp += item.CargoNumber ? (item.CargoNumber + _sepa) : '';
                                }
                                break;
                            case 'PrintProductUpc': //单品货号
                                temp += item.CargoNumber ? (item.CargoNumber + _sepa) : '';
                                break;
                            case 'PrintGoodsUpc': //商品货号
                                temp += item.productCargoNumber ? (item.productCargoNumber + _sepa) : '';
                                break;
                            case 'PrintProductUpc_GoodsUpc': //单品货号+商品货号
                                temp += joinStr(item.CargoNumber, item.productCargoNumber);
                                break;
                            case 'PrintGoodsUpc_ProductUpc': //商品货号+单品货号
                                temp += joinStr(item.productCargoNumber, item.CargoNumber);
                                break;
                        }

                        break;
                    case 'ProductID':
                        //产品ID
                        if (item.ProductID)
                            temp += item.ProductID + _sepa; //产品id

                        break;
                    case 'Color':
                        if (item.Color)
                            temp += item.Color + _sepa; //颜色第一个属性
                        if (commonModule.PlatformType != "Alibaba" && commonModule.PlatformType != "AlibabaC2M" && commonModule.PlatformType != "TaobaoMaiCaiV2") {
                            if (item.ExtAttr1)
                                temp += item.ExtAttr1 + _sepa; //扩展属性
                            if (item.ExtAttr2)
                                temp += item.ExtAttr2 + _sepa; //扩展属性
                            if (item.ExtAttr3)
                                temp += item.ExtAttr3 + _sepa; //扩展属性
                        }
                        break;
                    case 'Size':
                        if (item.Size)
                            temp += item.Size + _sepa; //尺码第二个属性
                        break;
                    case 'ProductAttr': //商品规格，有赞平台商品属性不能区分颜色、尺码
                        if (item.ProductAttr)
                            temp += item.ProductAttr + _sepa; //尺码第二个属性
                        break;
                    case 'Price':
                        temp += item.Price ? (item.Price + _sepa) : '';
                        break;
                    case 'Qty':
                        var qtyFormat = _settings.QtyStryle;
                        var qtyStr = qtyFormat.replace('{0}', item.Count);
                        temp += qtyStr;

                        if (_settings.QtyUnit == 'product_unit') {
                            temp += item.Unit ? (item.Unit + _sepa) : '';
                        }
                        else if (_settings.QtyUnit != '无单位') {
                            temp += _settings.QtyUnit ? (_settings.QtyUnit + _sepa) : '';
                        }
                        break;
                }
            }
        });

        temp = temp.trimEndDgj(_sepa).trimEndDgj(':');
        temp = temp.replace(/null/g, '');
        temp += ";";

        return temp;
    }

    module.Initialize = function () {
        module.LoadPrintContentSet(false);
    }

    return module;
}(printContentFormatSetModule || {}, keywordFilterReplaceModule, commonModule, jQuery, layer));